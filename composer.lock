{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "85b69dd4d9f3a357d2d65cbfbbdc1b5d", "packages": [{"name": "acelaya/doctrine-enum-type", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/acelaya/doctrine-enum-type.git", "reference": "3d63f81b4f310ad9f2826c53eb4773b5b354dc86"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/acelaya/doctrine-enum-type/zipball/3d63f81b4f310ad9f2826c53eb4773b5b354dc86", "reference": "3d63f81b4f310ad9f2826c53eb4773b5b354dc86", "shasum": ""}, "require": {"doctrine/dbal": "~2.4", "myclabs/php-enum": "~1.4", "php": ">=5.4"}, "require-dev": {"phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~2.0"}, "type": "library", "autoload": {"psr-4": {"Acelaya\\Doctrine\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A custom Doctrine type that maps column values to enum objects using myclabs/php-enum", "keywords": ["doctrine", "enum", "type"], "support": {"issues": "https://github.com/acelaya/doctrine-enum-type/issues", "source": "https://github.com/acelaya/doctrine-enum-type/tree/master"}, "time": "2016-01-20T19:23:37+00:00"}, {"name": "algolia/algoliasearch-client-php", "version": "1.28.1", "source": {"type": "git", "url": "https://github.com/algolia/algoliasearch-client-php.git", "reference": "43b0b0dc64e2d0f206d903ad3a4fb8b0a3660f81"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/algolia/algoliasearch-client-php/zipball/43b0b0dc64e2d0f206d903ad3a4fb8b0a3660f81", "reference": "43b0b0dc64e2d0f206d903ad3a4fb8b0a3660f81", "shasum": ""}, "require": {"ext-curl": "*", "ext-mbstring": "*", "php": "^5.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "satooshi/php-coveralls": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.0": "2.0.x-dev"}}, "autoload": {"psr-0": {"AlgoliaSearch": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Algolia Team", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Algolia Search API Client for PHP", "homepage": "https://github.com/algolia/algoliasearch-client-php", "support": {"issues": "https://github.com/algolia/algoliasearch-client-php/issues", "source": "https://github.com/algolia/algoliasearch-client-php/tree/1.28.1"}, "time": "2020-06-03T15:36:28+00:00"}, {"name": "aws/aws-sdk-php", "version": "3.164.0", "source": {"type": "git", "url": "https://github.com/aws/aws-sdk-php.git", "reference": "39ecf57d828eb50ab23fd70255a68b8996392ac9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aws/aws-sdk-php/zipball/39ecf57d828eb50ab23fd70255a68b8996392ac9", "reference": "39ecf57d828eb50ab23fd70255a68b8996392ac9", "shasum": ""}, "require": {"ext-json": "*", "ext-pcre": "*", "ext-simplexml": "*", "guzzlehttp/guzzle": "^5.3.3|^6.2.1|^7.0", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.4.1", "mtdowling/jmespath.php": "^2.5", "php": ">=5.5"}, "require-dev": {"andrewsville/php-token-reflection": "^1.4", "aws/aws-php-sns-message-validator": "~1.0", "behat/behat": "~3.0", "doctrine/cache": "~1.4", "ext-dom": "*", "ext-openssl": "*", "ext-pcntl": "*", "ext-sockets": "*", "nette/neon": "^2.3", "paragonie/random_compat": ">= 2", "phpunit/phpunit": "^4.8.35|^5.4.3", "psr/cache": "^1.0", "psr/simple-cache": "^1.0", "sebastian/comparator": "^1.2.3"}, "suggest": {"aws/aws-php-sns-message-validator": "To validate incoming SNS notifications", "doctrine/cache": "To use the DoctrineCacheAdapter", "ext-curl": "To send requests using cURL", "ext-openssl": "Allows working with CloudFront private distributions and verifying received SNS messages", "ext-sockets": "To use client-side monitoring"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Aws\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Amazon Web Services", "homepage": "http://aws.amazon.com"}], "description": "AWS SDK for PHP - Use Amazon Web Services in your PHP project", "homepage": "http://aws.amazon.com/sdkforphp", "keywords": ["amazon", "aws", "cloud", "dynamodb", "ec2", "glacier", "s3", "sdk"], "support": {"forum": "https://forums.aws.amazon.com/forum.jspa?forumID=80", "issues": "https://github.com/aws/aws-sdk-php/issues", "source": "https://github.com/aws/aws-sdk-php/tree/3.164.0"}, "time": "2020-11-24T19:18:22+00:00"}, {"name": "barryvdh/elfinder-flysystem-driver", "version": "v0.2.1", "source": {"type": "git", "url": "https://github.com/barryvdh/elfinder-flysystem-driver.git", "reference": "1f323056495fdce019b6ef1621be697f2945c609"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/barryvdh/elfinder-flysystem-driver/zipball/1f323056495fdce019b6ef1621be697f2945c609", "reference": "1f323056495fdce019b6ef1621be697f2945c609", "shasum": ""}, "require": {"intervention/image": "^2.0", "league/flysystem": "^1.0", "league/flysystem-cached-adapter": "^1.0", "php": ">=5.4", "studio-42/elfinder": "^2.1.10"}, "suggest": {"league/glide": "1.x to display images through Glide"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.2-dev"}}, "autoload": {"psr-4": {"Barryvdh\\elFinderFlysystemDriver\\": "src/"}, "classmap": ["elFinderVolumeFlysystem.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Barry vd. Heuvel", "email": "<EMAIL>"}], "description": "A Flysystem Driver for elFinder", "keywords": ["Flysystem", "elfinder", "filesystem"], "support": {"issues": "https://github.com/barryvdh/elfinder-flysystem-driver/issues", "source": "https://github.com/barryvdh/elfinder-flysystem-driver/tree/master"}, "time": "2017-07-08T17:59:38+00:00"}, {"name": "beber<PERSON>i/assert", "version": "v2.9.9", "source": {"type": "git", "url": "https://github.com/beberlei/assert.git", "reference": "124317de301b7c91d5fce34c98bba2c6925bec95"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/beberlei/assert/zipball/124317de301b7c91d5fce34c98bba2c6925bec95", "reference": "124317de301b7c91d5fce34c98bba2c6925bec95", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.3"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.1.1", "phpunit/phpunit": "^4.8.35|^5.7"}, "type": "library", "autoload": {"files": ["lib/Assert/functions.php"], "psr-4": {"Assert\\": "lib/Assert"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Collaborator"}], "description": "Thin assertion library for input validation in business models.", "keywords": ["assert", "assertion", "validation"], "support": {"issues": "https://github.com/beberlei/assert/issues", "source": "https://github.com/beberlei/assert/tree/v2.9.9"}, "time": "2019-05-28T15:27:37+00:00"}, {"name": "behat/transliterator", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/Behat/Transliterator.git", "reference": "3c4ec1d77c3d05caa1f0bf8fb3aae4845005c7fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Behat/Transliterator/zipball/3c4ec1d77c3d05caa1f0bf8fb3aae4845005c7fc", "reference": "3c4ec1d77c3d05caa1f0bf8fb3aae4845005c7fc", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"chuyskywalker/rolling-curl": "^3.1", "php-yaoi/php-yaoi": "^1.0", "phpunit/phpunit": "^4.8.36|^6.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"psr-4": {"Behat\\Transliterator\\": "src/Behat/Transliterator"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Artistic-1.0"], "description": "String transliterator", "keywords": ["i18n", "slug", "transliterator"], "support": {"issues": "https://github.com/Behat/Transliterator/issues", "source": "https://github.com/Behat/Transliterator/tree/v1.3.0"}, "time": "2020-01-14T16:39:13+00:00"}, {"name": "broadway/broadway", "version": "0.9.0", "source": {"type": "git", "url": "https://github.com/broadway/broadway.git", "reference": "0ba385ee4e8f21beaaddd9c06564b21a60b57500"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/broadway/broadway/zipball/0ba385ee4e8f21beaaddd9c06564b21a60b57500", "reference": "0ba385ee4e8f21beaaddd9c06564b21a60b57500", "shasum": ""}, "require": {"beberlei/assert": "~2.0", "broadway/uuid-generator": "~0.1.0", "php": ">=5.5.9", "ramsey/uuid": "~2.4"}, "require-dev": {"doctrine/dbal": "~2.4", "doctrine/mongodb": "~1.0", "elasticsearch/elasticsearch": "~1.0|^2.0", "instaclick/base-test-bundle": "~0.5", "monolog/monolog": "~1.8", "symfony/console": "~2.4", "symfony/proxy-manager-bridge": "~2.4"}, "suggest": {"doctrine/dbal": "For the BroadwayBundle (to persist events)", "doctrine/mongodb": "For persisting saga states (required for BroadwayBundle)", "elasticsearch/elasticsearch": "For persisting read models (required for BroadwayBundle)", "psr/log-implementation": "Implementation for PSR3, LoggerInterface", "symfony/console": "For the BroadwayBundle", "symfony/proxy-manager-bridge": "For the BroadwayBundle"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.9.x-dev"}}, "autoload": {"psr-0": {"Broadway\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Qandidate.com", "homepage": "http://labs.qandidate.com/"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Infrastructure and testing helpers for creating CQRS and event sourced applications.", "keywords": ["cqrs", "ddd", "domain-driven design", "event sourcing"], "support": {"issues": "https://github.com/broadway/broadway/issues", "source": "https://github.com/broadway/broadway/tree/0.9.0"}, "time": "2016-04-19T13:31:15+00:00"}, {"name": "broadway/uuid-generator", "version": "0.1.0", "source": {"type": "git", "url": "https://github.com/qandidate-labs/broadway-uuid-generator.git", "reference": "58380b723c671807131fe9ac0ff077f162be47bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/qandidate-labs/broadway-uuid-generator/zipball/58380b723c671807131fe9ac0ff077f162be47bb", "reference": "58380b723c671807131fe9ac0ff077f162be47bb", "shasum": ""}, "require-dev": {"rhumsaa/uuid": "~2.4"}, "suggest": {"rhumsaa/uuid": "Allows creating UUIDs"}, "type": "library", "autoload": {"psr-0": {"Broadway\\UuidGenerator\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "UUID generator for broadway/broadway.", "support": {"issues": "https://github.com/qandidate-labs/broadway-uuid-generator/issues", "source": "https://github.com/qandidate-labs/broadway-uuid-generator/tree/master"}, "time": "2014-09-12T14:14:07+00:00"}, {"name": "cakephp/cache", "version": "3.9.4", "source": {"type": "git", "url": "https://github.com/cakephp/cache.git", "reference": "bde82c6692f03351d113d010e1a4d7f69359144d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/cache/zipball/bde82c6692f03351d113d010e1a4d7f69359144d", "reference": "bde82c6692f03351d113d010e1a4d7f69359144d", "shasum": ""}, "require": {"cakephp/core": "^3.6.0", "php": ">=5.6.0", "psr/simple-cache": "^1.0.0"}, "type": "library", "autoload": {"psr-4": {"Cake\\Cache\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/cache/graphs/contributors"}], "description": "Easy to use Caching library with support for multiple caching backends", "homepage": "https://cakephp.org", "keywords": ["cache", "caching", "cakephp"], "support": {"forum": "https://stackoverflow.com/tags/cakephp", "irc": "irc://irc.freenode.org/cakephp", "issues": "https://github.com/cakephp/cakephp/issues", "source": "https://github.com/cakephp/cache"}, "time": "2020-10-20T12:53:41+00:00"}, {"name": "cakephp/collection", "version": "3.9.4", "source": {"type": "git", "url": "https://github.com/cakephp/collection.git", "reference": "ddfff69d7bfa8b9b03eb7150097b0b06258fcaa3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/collection/zipball/ddfff69d7bfa8b9b03eb7150097b0b06258fcaa3", "reference": "ddfff69d7bfa8b9b03eb7150097b0b06258fcaa3", "shasum": ""}, "require": {"php": ">=5.6.0"}, "type": "library", "autoload": {"files": ["functions.php"], "psr-4": {"Cake\\Collection\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/collection/graphs/contributors"}], "description": "Work easily with arrays and iterators by having a battery of utility traversal methods", "homepage": "https://cakephp.org", "keywords": ["arrays", "cakephp", "collections", "iterators"], "support": {"forum": "https://stackoverflow.com/tags/cakephp", "irc": "irc://irc.freenode.org/cakephp", "issues": "https://github.com/cakephp/cakephp/issues", "source": "https://github.com/cakephp/collection"}, "time": "2020-07-05T02:00:29+00:00"}, {"name": "cakephp/core", "version": "3.9.4", "source": {"type": "git", "url": "https://github.com/cakephp/core.git", "reference": "4b45635d6be8a98be175fea9c9f575de29d515b3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/core/zipball/4b45635d6be8a98be175fea9c9f575de29d515b3", "reference": "4b45635d6be8a98be175fea9c9f575de29d515b3", "shasum": ""}, "require": {"cakephp/utility": "^3.6.0", "php": ">=5.6.0"}, "suggest": {"cakephp/cache": "To use Configure::store() and restore().", "cakephp/event": "To use PluginApplicationInterface or plugin applications."}, "type": "library", "autoload": {"files": ["functions.php"], "psr-4": {"Cake\\Core\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/core/graphs/contributors"}], "description": "CakePHP Framework Core classes", "homepage": "https://cakephp.org", "keywords": ["cakephp", "core", "framework"], "support": {"forum": "https://stackoverflow.com/tags/cakephp", "irc": "irc://irc.freenode.org/cakephp", "issues": "https://github.com/cakephp/cakephp/issues", "source": "https://github.com/cakephp/core"}, "time": "2020-10-21T21:21:05+00:00"}, {"name": "cakephp/database", "version": "3.9.4", "source": {"type": "git", "url": "https://github.com/cakephp/database.git", "reference": "6f4cd60f53e8b6559cc6782ff288cc6d2b8fe1d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/database/zipball/6f4cd60f53e8b6559cc6782ff288cc6d2b8fe1d3", "reference": "6f4cd60f53e8b6559cc6782ff288cc6d2b8fe1d3", "shasum": ""}, "require": {"cakephp/cache": "^3.6.0", "cakephp/core": "^3.6.0", "cakephp/datasource": "^3.6.0", "cakephp/log": "^3.6.0", "php": ">=5.6.0"}, "type": "library", "autoload": {"psr-4": {"Cake\\Database\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/database/graphs/contributors"}], "description": "Flexible and powerful Database abstraction library with a familiar PDO-like API", "homepage": "https://cakephp.org", "keywords": ["abstraction", "cakephp", "database", "database abstraction", "pdo"], "support": {"forum": "https://stackoverflow.com/tags/cakephp", "irc": "irc://irc.freenode.org/cakephp", "issues": "https://github.com/cakephp/cakephp/issues", "source": "https://github.com/cakephp/database"}, "time": "2020-10-04T00:34:57+00:00"}, {"name": "cakephp/datasource", "version": "3.9.4", "source": {"type": "git", "url": "https://github.com/cakephp/datasource.git", "reference": "c1c5e06c38dc61b997703d38fc704917f39b4dd8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/datasource/zipball/c1c5e06c38dc61b997703d38fc704917f39b4dd8", "reference": "c1c5e06c38dc61b997703d38fc704917f39b4dd8", "shasum": ""}, "require": {"cakephp/core": "^3.6.0", "php": ">=5.6.0"}, "suggest": {"cakephp/cache": "If you decide to use Query caching.", "cakephp/collection": "If you decide to use ResultSetInterface.", "cakephp/utility": "If you decide to use EntityTrait."}, "type": "library", "autoload": {"psr-4": {"Cake\\Datasource\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/datasource/graphs/contributors"}], "description": "Provides connection managing and traits for Entities and Queries that can be reused for different datastores", "homepage": "https://cakephp.org", "keywords": ["cakephp", "connection management", "datasource", "entity", "query"], "support": {"forum": "https://stackoverflow.com/tags/cakephp", "irc": "irc://irc.freenode.org/cakephp", "issues": "https://github.com/cakephp/cakephp/issues", "source": "https://github.com/cakephp/datasource"}, "time": "2020-10-20T12:53:41+00:00"}, {"name": "cakephp/log", "version": "3.9.4", "source": {"type": "git", "url": "https://github.com/cakephp/log.git", "reference": "b5b97154b8e63f41e206021901e49397e2f4ca90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/log/zipball/b5b97154b8e63f41e206021901e49397e2f4ca90", "reference": "b5b97154b8e63f41e206021901e49397e2f4ca90", "shasum": ""}, "require": {"cakephp/core": "^3.6.0", "php": ">=5.6.0", "psr/log": "^1.0.0"}, "type": "library", "autoload": {"psr-4": {"Cake\\Log\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/log/graphs/contributors"}], "description": "CakePHP logging library with support for multiple different streams", "homepage": "https://cakephp.org", "keywords": ["Streams", "cakephp", "log", "logging"], "support": {"forum": "https://stackoverflow.com/tags/cakephp", "irc": "irc://irc.freenode.org/cakephp", "issues": "https://github.com/cakephp/cakephp/issues", "source": "https://github.com/cakephp/log"}, "time": "2020-10-20T12:53:41+00:00"}, {"name": "cakephp/utility", "version": "3.9.4", "source": {"type": "git", "url": "https://github.com/cakephp/utility.git", "reference": "e655b399b7492e517caef52fb87af9db10543112"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/utility/zipball/e655b399b7492e517caef52fb87af9db10543112", "reference": "e655b399b7492e517caef52fb87af9db10543112", "shasum": ""}, "require": {"cakephp/core": "^3.6.0", "php": ">=5.6.0"}, "suggest": {"ext-intl": "To use Text::transliterate() or Text::slug()", "lib-ICU": "To use Text::transliterate() or Text::slug()"}, "type": "library", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Cake\\Utility\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "CakePHP Community", "homepage": "https://github.com/cakephp/utility/graphs/contributors"}], "description": "CakePHP Utility classes such as Inflector, String, Hash, and Security", "homepage": "https://cakephp.org", "keywords": ["cakephp", "hash", "inflector", "security", "string", "utility"], "support": {"forum": "https://stackoverflow.com/tags/cakephp", "irc": "irc://irc.freenode.org/cakephp", "issues": "https://github.com/cakephp/cakephp/issues", "source": "https://github.com/cakephp/utility"}, "time": "2020-08-18T13:55:20+00:00"}, {"name": "cocur/slugify", "version": "v3.2", "source": {"type": "git", "url": "https://github.com/cocur/slugify.git", "reference": "d41701efe58ba2df9cae029c3d21e1518cc6780e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cocur/slugify/zipball/d41701efe58ba2df9cae029c3d21e1518cc6780e", "reference": "d41701efe58ba2df9cae029c3d21e1518cc6780e", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.5.9"}, "require-dev": {"laravel/framework": "~5.1", "latte/latte": "~2.2", "league/container": "^2.2.0", "mikey179/vfsstream": "~1.6", "mockery/mockery": "~0.9", "nette/di": "~2.2", "phpunit/phpunit": "~4.8.36|~5.2", "pimple/pimple": "~1.1", "plumphp/plum": "~0.1", "silex/silex": "~1.3", "symfony/config": "~2.4|~3.0|~4.0", "symfony/dependency-injection": "~2.4|~3.0|~4.0", "symfony/http-kernel": "~2.4|~3.0|~4.0", "twig/twig": "~1.26|~2.0", "zendframework/zend-modulemanager": "~2.2", "zendframework/zend-servicemanager": "~2.2", "zendframework/zend-view": "~2.2"}, "type": "library", "autoload": {"psr-4": {"Cocur\\Slugify\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://florian.ec"}], "description": "Converts a string into a slug.", "keywords": ["slug", "slugify"], "support": {"issues": "https://github.com/cocur/slugify/issues", "source": "https://github.com/cocur/slugify/tree/master"}, "time": "2019-01-31T20:38:55+00:00"}, {"name": "composer/package-versions-deprecated", "version": "*********", "source": {"type": "git", "url": "https://github.com/composer/package-versions-deprecated.git", "reference": "b4f54f74ef3453349c24a845d22392cd31e65f1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/package-versions-deprecated/zipball/b4f54f74ef3453349c24a845d22392cd31e65f1d", "reference": "b4f54f74ef3453349c24a845d22392cd31e65f1d", "shasum": ""}, "require": {"composer-plugin-api": "^1.1.0 || ^2.0", "php": "^7 || ^8"}, "replace": {"ocramius/package-versions": "1.11.99"}, "require-dev": {"composer/composer": "^1.9.3 || ^2.0@dev", "ext-zip": "^1.13", "phpunit/phpunit": "^6.5 || ^7"}, "type": "composer-plugin", "extra": {"class": "PackageVersions\\Installer", "branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"PackageVersions\\": "src/PackageVersions"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "Composer plugin that provides efficient querying for installed package versions (no runtime IO)", "support": {"issues": "https://github.com/composer/package-versions-deprecated/issues", "source": "https://github.com/composer/package-versions-deprecated/tree/*********"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-01-17T14:14:24+00:00"}, {"name": "doctrine/annotations", "version": "1.13.2", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "5b668aef16090008790395c02c893b1ba13f7e08"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/5b668aef16090008790395c02c893b1ba13f7e08", "reference": "5b668aef16090008790395c02c893b1ba13f7e08", "shasum": ""}, "require": {"doctrine/lexer": "1.*", "ext-tokenizer": "*", "php": "^7.1 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^6.0 || ^8.1", "phpstan/phpstan": "^0.12.20", "phpunit/phpunit": "^7.5 || ^8.0 || ^9.1.5", "symfony/cache": "^4.4 || ^5.2"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.13.2"}, "time": "2021-08-05T19:00:23+00:00"}, {"name": "doctrine/cache", "version": "1.12.1", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "4cf401d14df219fa6f38b671f5493449151c9ad8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/4cf401d14df219fa6f38b671f5493449151c9ad8", "reference": "4cf401d14df219fa6f38b671f5493449151c9ad8", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "cache/integration-tests": "dev-master", "doctrine/coding-standard": "^8.0", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "predis/predis": "~1.0", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.2 || ^6.0@dev", "symfony/var-exporter": "^4.4 || ^5.2 || ^6.0@dev"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.12.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2021-07-17T14:39:21+00:00"}, {"name": "doctrine/collections", "version": "1.6.8", "source": {"type": "git", "url": "https://github.com/doctrine/collections.git", "reference": "1958a744696c6bb3bb0d28db2611dc11610e78af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/collections/zipball/1958a744696c6bb3bb0d28db2611dc11610e78af", "reference": "1958a744696c6bb3bb0d28db2611dc11610e78af", "shasum": ""}, "require": {"php": "^7.1.3 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.1.5", "vimeo/psalm": "^4.2.1"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "lib/Doctrine/Common/Collections"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "homepage": "https://www.doctrine-project.org/projects/collections.html", "keywords": ["array", "collections", "iterators", "php"], "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/1.6.8"}, "time": "2021-08-10T18:51:53+00:00"}, {"name": "doctrine/common", "version": "2.13.3", "source": {"type": "git", "url": "https://github.com/doctrine/common.git", "reference": "f3812c026e557892c34ef37f6ab808a6b567da7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/common/zipball/f3812c026e557892c34ef37f6ab808a6b567da7f", "reference": "f3812c026e557892c34ef37f6ab808a6b567da7f", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/inflector": "^1.0", "doctrine/lexer": "^1.0", "doctrine/persistence": "^1.3.3", "doctrine/reflection": "^1.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^1.0", "phpstan/phpstan": "^0.11", "phpstan/phpstan-phpunit": "^0.11", "phpunit/phpunit": "^7.0", "squizlabs/php_codesniffer": "^3.0", "symfony/phpunit-bridge": "^4.0.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.11.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Doctrine Common project is a library that provides additional functionality that other Doctrine projects depend on such as better reflection support, persistence interfaces, proxies, event system and much more.", "homepage": "https://www.doctrine-project.org/projects/common.html", "keywords": ["common", "doctrine", "php"], "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.13.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcommon", "type": "tidelift"}], "time": "2020-06-05T16:46:05+00:00"}, {"name": "doctrine/dbal", "version": "2.12.1", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "adce7a954a1c2f14f85e94aed90c8489af204086"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/adce7a954a1c2f14f85e94aed90c8489af204086", "reference": "adce7a954a1c2f14f85e94aed90c8489af204086", "shasum": ""}, "require": {"doctrine/cache": "^1.0", "doctrine/event-manager": "^1.0", "ext-pdo": "*", "php": "^7.3 || ^8"}, "require-dev": {"doctrine/coding-standard": "^8.1", "jetbrains/phpstorm-stubs": "^2019.1", "phpstan/phpstan": "^0.12.40", "phpunit/phpunit": "^9.4", "psalm/plugin-phpunit": "^0.10.0", "symfony/console": "^2.0.5|^3.0|^4.0|^5.0", "vimeo/psalm": "^3.17.2"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\DBAL\\": "lib/Doctrine/DBAL"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlanywhere", "sqlite", "sqlserver", "sqlsrv"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/2.12.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdbal", "type": "tidelift"}], "time": "2020-11-14T20:26:58+00:00"}, {"name": "doctrine/doctrine-bundle", "version": "1.12.12", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineBundle.git", "reference": "5a94ec5c9081ddf10061b95e0c140dc260d6b6d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineBundle/zipball/5a94ec5c9081ddf10061b95e0c140dc260d6b6d3", "reference": "5a94ec5c9081ddf10061b95e0c140dc260d6b6d3", "shasum": ""}, "require": {"doctrine/dbal": "^2.5.12|^3.0", "doctrine/doctrine-cache-bundle": "~1.2", "doctrine/persistence": "^1.3.3", "jdorn/sql-formatter": "^1.2.16", "php": "^7.1 || ^8.0", "symfony/cache": "^3.4.30|^4.3.3", "symfony/config": "^3.4.30|^4.3.3", "symfony/console": "^3.4.30|^4.3.3", "symfony/dependency-injection": "^3.4.30|^4.3.3", "symfony/doctrine-bridge": "^3.4.30|^4.3.3", "symfony/framework-bundle": "^3.4.30|^4.3.3", "symfony/service-contracts": "^1.1.1|^2.0"}, "conflict": {"doctrine/orm": "<2.6", "twig/twig": "<1.34|>=2.0,<2.4"}, "require-dev": {"doctrine/coding-standard": "^6.0", "doctrine/orm": "^2.6", "ocramius/proxy-manager": "^2.1", "php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^7.5", "symfony/phpunit-bridge": "^4.2", "symfony/property-info": "^3.4.30|^4.3.3", "symfony/proxy-manager-bridge": "^3.4|^4|^5", "symfony/twig-bridge": "^3.4|^4.1", "symfony/validator": "^3.4.30|^4.3.3", "symfony/web-profiler-bundle": "^3.4.30|^4.3.3", "symfony/yaml": "^3.4.30|^4.3.3", "twig/twig": "^1.34|^2.12"}, "suggest": {"doctrine/orm": "The Doctrine ORM integration is optional in the bundle.", "symfony/web-profiler-bundle": "To use the data collector."}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.3.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Bundle\\DoctrineBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org/"}], "description": "Symfony DoctrineBundle", "homepage": "http://www.doctrine-project.org", "keywords": ["database", "dbal", "orm", "persistence"], "support": {"issues": "https://github.com/doctrine/DoctrineBundle/issues", "source": "https://github.com/doctrine/DoctrineBundle/tree/1.12.12"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdoctrine-bundle", "type": "tidelift"}], "time": "2020-11-10T20:24:48+00:00"}, {"name": "doctrine/doctrine-cache-bundle", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineCacheBundle.git", "reference": "6bee2f9b339847e8a984427353670bad4e7bdccb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineCacheBundle/zipball/6bee2f9b339847e8a984427353670bad4e7bdccb", "reference": "6bee2f9b339847e8a984427353670bad4e7bdccb", "shasum": ""}, "require": {"doctrine/cache": "^1.4.2", "doctrine/inflector": "^1.0", "php": "^7.1", "symfony/doctrine-bridge": "^3.4|^4.0"}, "require-dev": {"instaclick/coding-standard": "~1.1", "instaclick/object-calisthenics-sniffs": "dev-master", "instaclick/symfony2-coding-standard": "dev-remaster", "phpunit/phpunit": "^7.0", "predis/predis": "~0.8", "satooshi/php-coveralls": "^1.0", "squizlabs/php_codesniffer": "~1.5", "symfony/console": "^3.4|^4.0", "symfony/finder": "^3.4|^4.0", "symfony/framework-bundle": "^3.4|^4.0", "symfony/phpunit-bridge": "^3.4|^4.0", "symfony/security-acl": "^2.8", "symfony/validator": "^3.4|^4.0", "symfony/yaml": "^3.4|^4.0"}, "suggest": {"symfony/security-acl": "For using this bundle to cache ACLs"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Bundle\\DoctrineCacheBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "g<PERSON><PERSON><PERSON><PERSON><EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org/"}], "description": "Symfony Bundle for Doctrine Cache", "homepage": "https://www.doctrine-project.org", "keywords": ["cache", "caching"], "support": {"issues": "https://github.com/doctrine/DoctrineCacheBundle/issues", "source": "https://github.com/doctrine/DoctrineCacheBundle/tree/1.4.0"}, "abandoned": true, "time": "2019-11-29T11:22:01+00:00"}, {"name": "doctrine/event-manager", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "41370af6a30faa9dc0368c4a6814d596e81aba7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/41370af6a30faa9dc0368c4a6814d596e81aba7f", "reference": "41370af6a30faa9dc0368c4a6814d596e81aba7f", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9@dev"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/1.1.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2020-05-29T18:28:51+00:00"}, {"name": "doctrine/inflector", "version": "1.4.4", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9", "reference": "4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^8.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector", "Doctrine\\Common\\Inflector\\": "lib/Doctrine/Common/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/1.4.4"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2021-04-16T17:34:40+00:00"}, {"name": "doctrine/instantiator", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "d56bf6102915de5702778fe20f2de3b2fe570b5b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/d56bf6102915de5702778fe20f2de3b2fe570b5b", "reference": "d56bf6102915de5702778fe20f2de3b2fe570b5b", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^8.0", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.13 || 1.0.0-alpha2", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.4.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2020-11-10T18:47:58+00:00"}, {"name": "doctrine/lexer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/c268e882d4dbdd85e36e4ad69e02dc284f89d229", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.11"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/1.2.3"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2022-02-28T11:07:21+00:00"}, {"name": "doctrine/orm", "version": "2.7.4", "source": {"type": "git", "url": "https://github.com/doctrine/orm.git", "reference": "7d84a4998091ece4d645253ac65de9f879eeed2f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/orm/zipball/7d84a4998091ece4d645253ac65de9f879eeed2f", "reference": "7d84a4998091ece4d645253ac65de9f879eeed2f", "shasum": ""}, "require": {"composer/package-versions-deprecated": "^1.8", "doctrine/annotations": "^1.8", "doctrine/cache": "^1.9.1", "doctrine/collections": "^1.5", "doctrine/common": "^2.11 || ^3.0", "doctrine/dbal": "^2.9.3", "doctrine/event-manager": "^1.1", "doctrine/inflector": "^1.0", "doctrine/instantiator": "^1.3", "doctrine/lexer": "^1.0", "doctrine/persistence": "^1.3.3 || ^2.0", "ext-pdo": "*", "php": "^7.1", "symfony/console": "^3.0|^4.0|^5.0"}, "require-dev": {"doctrine/coding-standard": "^5.0", "phpstan/phpstan": "^0.12.18", "phpunit/phpunit": "^7.5", "symfony/yaml": "^3.4|^4.0|^5.0", "vimeo/psalm": "^3.11"}, "suggest": {"symfony/yaml": "If you want to use YAML Metadata Mapping Driver"}, "bin": ["bin/doctrine"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.7.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\ORM\\": "lib/Doctrine/ORM"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Object-Relational-Mapper for PHP", "homepage": "https://www.doctrine-project.org/projects/orm.html", "keywords": ["database", "orm"], "support": {"issues": "https://github.com/doctrine/orm/issues", "source": "https://github.com/doctrine/orm/tree/2.7.4"}, "time": "2020-10-10T17:11:26+00:00"}, {"name": "doctrine/persistence", "version": "1.3.8", "source": {"type": "git", "url": "https://github.com/doctrine/persistence.git", "reference": "7a6eac9fb6f61bba91328f15aa7547f4806ca288"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/persistence/zipball/7a6eac9fb6f61bba91328f15aa7547f4806ca288", "reference": "7a6eac9fb6f61bba91328f15aa7547f4806ca288", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/reflection": "^1.2", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.10@dev"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan": "^0.11", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "vimeo/psalm": "^3.11"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common", "Doctrine\\Persistence\\": "lib/Doctrine/Persistence"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Persistence project is a set of shared interfaces and functionality that the different Doctrine object mappers share.", "homepage": "https://doctrine-project.org/projects/persistence.html", "keywords": ["mapper", "object", "odm", "orm", "persistence"], "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/1.3.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fpersistence", "type": "tidelift"}], "time": "2020-06-20T12:56:16+00:00"}, {"name": "doctrine/reflection", "version": "1.2.2", "source": {"type": "git", "url": "https://github.com/doctrine/reflection.git", "reference": "fa587178be682efe90d005e3a322590d6ebb59a5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/reflection/zipball/fa587178be682efe90d005e3a322590d6ebb59a5", "reference": "fa587178be682efe90d005e3a322590d6ebb59a5", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "ext-tokenizer": "*", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^6.0 || ^8.2.0", "doctrine/common": "^2.10", "phpstan/phpstan": "^0.11.0 || ^0.12.20", "phpstan/phpstan-phpunit": "^0.11.0 || ^0.12.16", "phpunit/phpunit": "^7.5 || ^9.1.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Reflection project is a simple library used by the various Doctrine projects which adds some additional functionality on top of the reflection functionality that comes with PHP. It allows you to get the reflection information about classes, methods and properties statically.", "homepage": "https://www.doctrine-project.org/projects/reflection.html", "keywords": ["reflection", "static"], "support": {"issues": "https://github.com/doctrine/reflection/issues", "source": "https://github.com/doctrine/reflection/tree/1.2.2"}, "abandoned": "roave/better-reflection", "time": "2020-10-27T21:46:55+00:00"}, {"name": "egulias/email-validator", "version": "3.1.2", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "ee0db30118f661fb166bcffbf5d82032df484697"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/ee0db30118f661fb166bcffbf5d82032df484697", "reference": "ee0db30118f661fb166bcffbf5d82032df484697", "shasum": ""}, "require": {"doctrine/lexer": "^1.2", "php": ">=7.2", "symfony/polyfill-intl-idn": "^1.15"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^8.5.8|^9.3.3", "vimeo/psalm": "^4"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/3.1.2"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2021-10-11T09:18:27+00:00"}, {"name": "eventio/bbq", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/wizacha/bbq.git", "reference": "2b89c83e643bb61cfbb3c14ca843191c10026ca3"}, "require": {"php": ">=5.3.0"}, "require-dev": {"iron-io/iron_mq": "~1.4.5", "pda/pheanstalk": "~2.1", "predis/predis": "~0.8"}, "type": "library", "autoload": {"psr-0": {"Eventio\\BBQ": "src/"}}, "license": ["MIT"], "authors": [{"name": "Ville Mattila", "email": "<EMAIL>", "homepage": "http://github.com/vmattila", "role": "Developer"}], "description": "PHP Message Queue Abstraction Library", "homepage": "https://github.com/eventio/bbq", "keywords": ["beanstalkd", "ironmq", "message queue", "mq"], "time": "2017-10-03T11:10:00+00:00"}, {"name": "exercise/htmlpurifier-bundle", "version": "v3.0.1", "source": {"type": "git", "url": "https://github.com/Exercise/HTMLPurifierBundle.git", "reference": "852d00ce4ec6b077fb6f3bfa7931b55ab46666da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Exercise/HTMLPurifierBundle/zipball/852d00ce4ec6b077fb6f3bfa7931b55ab46666da", "reference": "852d00ce4ec6b077fb6f3bfa7931b55ab46666da", "shasum": ""}, "require": {"ezyang/htmlpurifier": "~4.0", "php": "^7.1.3", "symfony/config": "~3.4 || ~4.0 || ^5.0", "symfony/dependency-injection": "~3.4.1 || ^4.0.1 || ^5.0", "symfony/http-kernel": "~3.4.1 || ^4.0.1 || ^5.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.0", "symfony/form": "~3.4.1 || ^4.0.1 || ^5.0", "symfony/phpunit-bridge": "4.4.*", "twig/twig": "^1.35.0 || ^2.4.4 || ^3.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Exercise\\HTMLPurifierBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "contributors", "homepage": "https://github.com/Exercise/HTMLPurifierBundle/contributors"}], "description": "HTMLPurifier integration for your Symfony project", "homepage": "https://github.com/Exercise/HTMLPurifierBundle", "keywords": ["Purifier", "html", "htmlpurifier", "symfony"], "support": {"issues": "https://github.com/Exercise/HTMLPurifierBundle/issues", "source": "https://github.com/Exercise/HTMLPurifierBundle/tree/v3.0.1"}, "time": "2020-09-18T10:46:31+00:00"}, {"name": "ezyang/htmlpurifier", "version": "v4.13.0", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "08e27c97e4c6ed02f37c5b2b20488046c8d90d75"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/08e27c97e4c6ed02f37c5b2b20488046c8d90d75", "reference": "08e27c97e4c6ed02f37c5b2b20488046c8d90d75", "shasum": ""}, "require": {"php": ">=5.2"}, "require-dev": {"simpletest/simpletest": "dev-master#72de02a7b80c6bb8864ef9bf66d41d2f58f826bd"}, "type": "library", "autoload": {"files": ["library/HTMLPurifier.composer.php"], "psr-0": {"HTMLPurifier": "library/"}, "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "support": {"issues": "https://github.com/ezyang/htmlpurifier/issues", "source": "https://github.com/ezyang/htmlpurifier/tree/master"}, "time": "2020-06-29T00:56:53+00:00"}, {"name": "firebase/php-jwt", "version": "v5.2.0", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "feb0e820b8436873675fd3aca04f3728eb2185cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/feb0e820b8436873675fd3aca04f3728eb2185cb", "reference": "feb0e820b8436873675fd3aca04f3728eb2185cb", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": ">=4.8 <=9"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/master"}, "time": "2020-03-25T18:49:23+00:00"}, {"name": "friendsofphp/proxy-manager-lts", "version": "v1.0.7", "source": {"type": "git", "url": "https://github.com/FriendsOfPHP/proxy-manager-lts.git", "reference": "c828ced1f932094ab79e4120a106a666565e4d9c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfPHP/proxy-manager-lts/zipball/c828ced1f932094ab79e4120a106a666565e4d9c", "reference": "c828ced1f932094ab79e4120a106a666565e4d9c", "shasum": ""}, "require": {"laminas/laminas-code": "~3.4.1|^4.0", "php": ">=7.1", "symfony/filesystem": "^4.4.17|^5.0|^6.0"}, "conflict": {"laminas/laminas-stdlib": "<3.2.1", "zendframework/zend-stdlib": "<3.2.1"}, "replace": {"ocramius/proxy-manager": "^2.1"}, "require-dev": {"ext-phar": "*", "symfony/phpunit-bridge": "^5.4|^6.0"}, "type": "library", "extra": {"thanks": {"name": "ocramius/proxy-manager", "url": "https://github.com/Ocramius/ProxyManager"}}, "autoload": {"psr-4": {"ProxyManager\\": "src/ProxyManager"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Adding support for a wider range of PHP versions to ocramius/proxy-manager", "homepage": "https://github.com/FriendsOfPHP/proxy-manager-lts", "keywords": ["aop", "lazy loading", "proxy", "proxy pattern", "service proxies"], "support": {"issues": "https://github.com/FriendsOfPHP/proxy-manager-lts/issues", "source": "https://github.com/FriendsOfPHP/proxy-manager-lts/tree/v1.0.7"}, "funding": [{"url": "https://github.com/Ocramius", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ocramius/proxy-manager", "type": "tidelift"}], "time": "2022-03-02T09:29:19+00:00"}, {"name": "friendsofsymfony/jsrouting-bundle", "version": "2.7.0", "source": {"type": "git", "url": "https://github.com/FriendsOfSymfony/FOSJsRoutingBundle.git", "reference": "d56600542504148bf2faa2b6bd7571a6adf6799e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfSymfony/FOSJsRoutingBundle/zipball/d56600542504148bf2faa2b6bd7571a6adf6799e", "reference": "d56600542504148bf2faa2b6bd7571a6adf6799e", "shasum": ""}, "require": {"php": "^7.1|^8.0", "symfony/console": "~3.3|^4.0|^5.0", "symfony/framework-bundle": "~3.3|^4.0|^5.0", "symfony/serializer": "~3.3|^4.0|^5.0", "willdurand/jsonp-callback-validator": "~1.0"}, "require-dev": {"symfony/expression-language": "~3.3|^4.0|^5.0", "symfony/phpunit-bridge": "^5.1"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"FOS\\JsRoutingBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "FriendsOfSymfony Community", "homepage": "https://github.com/friendsofsymfony/FOSJsRoutingBundle/contributors"}], "description": "A pretty nice way to expose your Symfony2 routing to client applications.", "homepage": "http://friendsofsymfony.github.com", "keywords": ["Js Routing", "javascript", "routing"], "support": {"issues": "https://github.com/FriendsOfSymfony/FOSJsRoutingBundle/issues", "source": "https://github.com/FriendsOfSymfony/FOSJsRoutingBundle/tree/2.7.0"}, "time": "2020-11-20T10:38:12+00:00"}, {"name": "gedmo/doctrine-extensions", "version": "v2.4.42", "source": {"type": "git", "url": "https://github.com/Atlantic18/DoctrineExtensions.git", "reference": "b6c4442b4f32ce05673fbdf1fa4a2d5e315cc0a4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Atlantic18/DoctrineExtensions/zipball/b6c4442b4f32ce05673fbdf1fa4a2d5e315cc0a4", "reference": "b6c4442b4f32ce05673fbdf1fa4a2d5e315cc0a4", "shasum": ""}, "require": {"behat/transliterator": "~1.2", "doctrine/common": "~2.4", "php": ">=5.3.2"}, "conflict": {"doctrine/annotations": "<1.2", "doctrine/mongodb-odm": ">=2.0"}, "require-dev": {"doctrine/common": ">=2.5.0", "doctrine/mongodb-odm": ">=1.0.2 <2.0", "doctrine/orm": ">=2.5.0", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.5", "symfony/yaml": "^2.6 || ^3.0 || ^4.0 || ^5.0"}, "suggest": {"doctrine/mongodb-odm": "to use the extensions with the MongoDB ODM", "doctrine/orm": "to use the extensions with the ORM"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4.x-dev"}}, "autoload": {"psr-4": {"Gedmo\\": "lib/G<PERSON>mo"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Doctrine2 behavioral extensions", "homepage": "http://gediminasm.org/", "keywords": ["Blameable", "behaviors", "doctrine2", "extensions", "gedmo", "loggable", "nestedset", "sluggable", "sortable", "timestampable", "translatable", "tree", "uploadable"], "support": {"email": "<EMAIL>", "issues": "https://github.com/Atlantic18/DoctrineExtensions/issues", "source": "https://github.com/Atlantic18/DoctrineExtensions/tree/v3.0.0-beta", "wiki": "https://github.com/Atlantic18/DoctrineExtensions/tree/master/doc"}, "time": "2020-08-21T01:27:20+00:00"}, {"name": "giggsey/libphonenumber-for-php", "version": "7.7.5", "source": {"type": "git", "url": "https://github.com/giggsey/libphonenumber-for-php.git", "reference": "7137cef7f295d225aec2a290f6829c1c71a1ffb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/giggsey/libphonenumber-for-php/zipball/7137cef7f295d225aec2a290f6829c1c71a1ffb0", "reference": "7137cef7f295d225aec2a290f6829c1c71a1ffb0", "shasum": ""}, "require": {"ext-mbstring": "*", "giggsey/locale": "^1.0", "php": ">=5.3.2"}, "require-dev": {"pear/pear-core-minimal": "^1.9", "pear/pear_exception": "^1.0", "pear/versioncontrol_git": "dev-master", "phing/phing": "^2.7", "phpunit/phpunit": "^4.8|^5.0", "satooshi/php-coveralls": "^1.0", "symfony/console": "^2.8|^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.x-dev"}}, "autoload": {"psr-0": {"libphonenumber": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://giggsey.com/"}], "description": "PHP Port of Google's libphonenumber", "homepage": "https://github.com/giggsey/libphonenumber-for-php", "keywords": ["geocoding", "geolocation", "libphonenumber", "mobile", "phonenumber", "validation"], "support": {"irc": "irc://irc.appliedirc.com/lobby", "issues": "https://github.com/giggsey/libphonenumber-for-php/issues", "source": "https://github.com/giggsey/libphonenumber-for-php"}, "time": "2016-11-23T15:39:02+00:00"}, {"name": "giggsey/locale", "version": "1.9", "source": {"type": "git", "url": "https://github.com/giggsey/Locale.git", "reference": "b07f1eace8072ccc61445ad8fbd493ff9d783043"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/giggsey/Locale/zipball/b07f1eace8072ccc61445ad8fbd493ff9d783043", "reference": "b07f1eace8072ccc61445ad8fbd493ff9d783043", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"pear/pear-core-minimal": "^1.9", "pear/pear_exception": "^1.0", "pear/versioncontrol_git": "^0.5", "phing/phing": "~2.7", "php-coveralls/php-coveralls": "^1.0|^2.0", "phpunit/phpunit": "^4.8|^5.0", "symfony/console": "^2.8|^3.0|^4.0", "symfony/filesystem": "^2.8|^3.0|^4.0", "symfony/finder": "^2.8|^3.0|^4.0", "symfony/process": "^2.8|^3.0|^4.0"}, "type": "library", "autoload": {"psr-4": {"Giggsey\\Locale\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://giggsey.com/"}], "description": "Locale functions required by libphonenumber-for-php", "support": {"issues": "https://github.com/giggsey/Locale/issues", "source": "https://github.com/giggsey/Locale/tree/master"}, "time": "2020-07-07T11:16:24+00:00"}, {"name": "google/apiclient", "version": "v2.8.3", "source": {"type": "git", "url": "https://github.com/googleapis/google-api-php-client.git", "reference": "81696e6206322e38c643cfcc96c4494ccfef8a32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-api-php-client/zipball/81696e6206322e38c643cfcc96c4494ccfef8a32", "reference": "81696e6206322e38c643cfcc96c4494ccfef8a32", "shasum": ""}, "require": {"firebase/php-jwt": "~2.0||~3.0||~4.0||~5.0", "google/apiclient-services": "~0.13", "google/auth": "^1.10", "guzzlehttp/guzzle": "~5.3.1||~6.0||~7.0", "guzzlehttp/psr7": "^1.2", "monolog/monolog": "^1.17|^2.0", "php": ">=5.4", "phpseclib/phpseclib": "~0.3.10||~2.0"}, "require-dev": {"cache/filesystem-adapter": "^0.3.2", "composer/composer": "^1.10", "dealerdirect/phpcodesniffer-composer-installer": "^0.7", "phpcompatibility/php-compatibility": "^9.2", "phpunit/phpunit": "^4.8.36|^5.0", "squizlabs/php_codesniffer": "~2.3", "symfony/css-selector": "~2.1", "symfony/dom-crawler": "~2.1"}, "suggest": {"cache/filesystem-adapter": "For caching certs and tokens (using Google\\Client::setCache)"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"files": ["src/aliases.php"], "psr-4": {"Google\\": "src/"}, "classmap": ["src/aliases.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Client library for Google APIs", "homepage": "http://developers.google.com/api-client-library/php", "keywords": ["google"], "support": {"issues": "https://github.com/googleapis/google-api-php-client/issues", "source": "https://github.com/googleapis/google-api-php-client/tree/v2.8.3"}, "time": "2020-11-17T17:33:35+00:00"}, {"name": "google/apiclient-services", "version": "v0.56", "source": {"type": "git", "url": "https://github.com/googleapis/google-api-php-client-services.git", "reference": "94ae125bd4ac33eec676142f7776c0b137cf7bc7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-api-php-client-services/zipball/94ae125bd4ac33eec676142f7776c0b137cf7bc7", "reference": "94ae125bd4ac33eec676142f7776c0b137cf7bc7", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "type": "library", "autoload": {"psr-0": {"Google_Service_": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Client library for Google APIs", "homepage": "http://developers.google.com/api-client-library/php", "keywords": ["google"], "support": {"issues": "https://github.com/googleapis/google-api-php-client-services/issues", "source": "https://github.com/googleapis/google-api-php-client-services/tree/v0.56"}, "time": "2018-04-21T00:23:45+00:00"}, {"name": "google/auth", "version": "v1.14.3", "source": {"type": "git", "url": "https://github.com/googleapis/google-auth-library-php.git", "reference": "c1503299c779af0cbc99b43788f75930988852cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-auth-library-php/zipball/c1503299c779af0cbc99b43788f75930988852cf", "reference": "c1503299c779af0cbc99b43788f75930988852cf", "shasum": ""}, "require": {"firebase/php-jwt": "~2.0|~3.0|~4.0|~5.0", "guzzlehttp/guzzle": "^5.3.1|^6.2.1|^7.0", "guzzlehttp/psr7": "^1.2", "php": ">=5.4", "psr/cache": "^1.0", "psr/http-message": "^1.0"}, "require-dev": {"guzzlehttp/promises": "0.1.1|^1.3", "kelvinmo/simplejwt": "^0.2.5", "phpseclib/phpseclib": "^2", "phpunit/phpunit": "^4.8.36|^5.7", "sebastian/comparator": ">=1.2.3", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"phpseclib/phpseclib": "May be used in place of OpenSSL for signing strings or for token management. Please require version ^2."}, "type": "library", "autoload": {"psr-4": {"Google\\Auth\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google Auth Library for PHP", "homepage": "http://github.com/google/google-auth-library-php", "keywords": ["Authentication", "google", "oauth2"], "support": {"docs": "https://googleapis.github.io/google-auth-library-php/master/", "issues": "https://github.com/googleapis/google-auth-library-php/issues", "source": "https://github.com/googleapis/google-auth-library-php/tree/v1.14.3"}, "time": "2020-10-16T21:33:48+00:00"}, {"name": "google/recaptcha", "version": "1.2.4", "source": {"type": "git", "url": "https://github.com/google/recaptcha.git", "reference": "614f25a9038be4f3f2da7cbfd778dc5b357d2419"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/google/recaptcha/zipball/614f25a9038be4f3f2da7cbfd778dc5b357d2419", "reference": "614f25a9038be4f3f2da7cbfd778dc5b357d2419", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.2.20|^2.15", "php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^4.8.36|^5.7.27|^6.59|^7.5.11"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"ReCaptcha\\": "src/ReCaptcha"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Client library for reCAPTCHA, a free service that protects websites from spam and abuse.", "homepage": "https://www.google.com/recaptcha/", "keywords": ["Abuse", "<PERSON><PERSON>a", "recaptcha", "spam"], "support": {"forum": "https://groups.google.com/forum/#!forum/recaptcha", "issues": "https://github.com/google/recaptcha/issues", "source": "https://github.com/google/recaptcha"}, "time": "2020-03-31T17:50:54+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.5.5", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "9d4290de1cfd701f38099ef7e183b64b4b7b0c5e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/9d4290de1cfd701f38099ef7e183b64b4b7b0c5e", "reference": "9d4290de1cfd701f38099ef7e183b64b4b7b0c5e", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.6.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.17.0"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.1"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.5-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/6.5"}, "time": "2020-06-16T21:01:06+00:00"}, {"name": "guzzlehttp/promises", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "60d379c243457e073cff02bc323a2a86cb355631"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/60d379c243457e073cff02bc323a2a86cb355631", "reference": "60d379c243457e073cff02bc323a2a86cb355631", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.4.0"}, "time": "2020-09-30T07:37:28+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.8.5", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "337e3ad8e5716c15f9657bd214d16cc5e69df268"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/337e3ad8e5716c15f9657bd214d16cc5e69df268", "reference": "337e3ad8e5716c15f9657bd214d16cc5e69df268", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.10"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.7-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/1.8.5"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2022-03-20T21:51:18+00:00"}, {"name": "h4cc/wkhtmltopdf-amd64", "version": "0.12.4", "source": {"type": "git", "url": "https://github.com/h4cc/wkhtmltopdf-amd64.git", "reference": "4e2ab2d032a5d7fbe2a741de8b10b8989523c95b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/h4cc/wkhtmltopdf-amd64/zipball/4e2ab2d032a5d7fbe2a741de8b10b8989523c95b", "reference": "4e2ab2d032a5d7fbe2a741de8b10b8989523c95b", "shasum": ""}, "bin": ["bin/wkhtmltopdf-amd64"], "type": "library", "autoload": {"psr-4": {"h4cc\\WKHTMLToPDF\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL Version 3"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Convert html to pdf using webkit (qtwebkit). Static linked linux binary for amd64 systems.", "homepage": "http://wkhtmltopdf.org/", "keywords": ["binary", "convert", "pdf", "snapshot", "thumbnail", "wkhtmltopdf"], "support": {"issues": "https://github.com/h4cc/wkhtmltopdf-amd64/issues", "source": "https://github.com/h4cc/wkhtmltopdf-amd64/tree/master"}, "time": "2018-01-15T06:57:33+00:00"}, {"name": "hipay/hipay-fullservice-sdk-php", "version": "2.7.0", "source": {"type": "git", "url": "https://github.com/hipay/hipay-fullservice-sdk-php.git", "reference": "35027b97c3509759ca000d74d9fdce8ee3403195"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hipay/hipay-fullservice-sdk-php/zipball/35027b97c3509759ca000d74d9fdce8ee3403195", "reference": "35027b97c3509759ca000d74d9fdce8ee3403195", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "php": ">=5.6.0"}, "require-dev": {"phpstan/phpstan": "0.*", "phpunit/phpunit": "6.2.*"}, "type": "library", "autoload": {"psr-4": {"HiPay\\Fullservice\\": "lib/HiPay/Fullservice/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "HiPay", "email": "<EMAIL>", "homepage": "http://www.hipay.com"}], "description": "The HiPay Enterprise SDK for PHP is a library for developers who want to integrate HiPay Enterprise payment methods to any PHP platform.", "homepage": "https://hipay.com/product-enterprise/", "keywords": ["<PERSON><PERSON>", "payment", "payment method"], "support": {"email": "<EMAIL>", "issues": "https://github.com/hipay/hipay-fullservice-sdk-php/issues", "source": "https://github.com/hipay/hipay-fullservice-sdk-php/tree/2.7.0"}, "time": "2020-10-30T08:35:35+00:00"}, {"name": "hoa/compiler", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Compiler.git", "reference": "aa09caf0bf28adae6654ca6ee415ee2f522672de"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Compiler/zipball/aa09caf0bf28adae6654ca6ee415ee2f522672de", "reference": "aa09caf0bf28adae6654ca6ee415ee2f522672de", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/exception": "~1.0", "hoa/file": "~1.0", "hoa/iterator": "~2.0", "hoa/math": "~1.0", "hoa/protocol": "~1.0", "hoa/regex": "~1.0", "hoa/visitor": "~2.0"}, "require-dev": {"hoa/json": "~2.0", "hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Compiler\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Compiler library.", "homepage": "https://hoa-project.net/", "keywords": ["algebraic", "ast", "compiler", "context-free", "coverage", "exhaustive", "grammar", "isotropic", "language", "lexer", "library", "ll1", "llk", "parser", "pp", "random", "regular", "rule", "sampler", "syntax", "token", "trace", "uniform"], "support": {"docs": "https://central.hoa-project.net/Documentation/Library/Compiler", "email": "<EMAIL>", "forum": "https://users.hoa-project.net/", "irc": "irc://chat.freenode.net/hoaproject", "issues": "https://github.com/hoaproject/Compiler/issues", "source": "https://central.hoa-project.net/Resource/Library/Compiler"}, "abandoned": true, "time": "2017-08-08T07:44:07+00:00"}, {"name": "hoa/consistency", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Consistency.git", "reference": "fd7d0adc82410507f332516faf655b6ed22e4c2f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Consistency/zipball/fd7d0adc82410507f332516faf655b6ed22e4c2f", "reference": "fd7d0adc82410507f332516faf655b6ed22e4c2f", "shasum": ""}, "require": {"hoa/exception": "~1.0", "php": ">=5.5.0"}, "require-dev": {"hoa/stream": "~1.0", "hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Consistency\\": "."}, "files": ["Prelude.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Consistency library.", "homepage": "https://hoa-project.net/", "keywords": ["autoloader", "callable", "consistency", "entity", "flex", "keyword", "library"], "support": {"docs": "https://central.hoa-project.net/Documentation/Library/Consistency", "email": "<EMAIL>", "forum": "https://users.hoa-project.net/", "irc": "irc://chat.freenode.net/hoaproject", "issues": "https://github.com/hoaproject/Consistency/issues", "source": "https://central.hoa-project.net/Resource/Library/Consistency"}, "abandoned": true, "time": "2017-05-02T12:18:12+00:00"}, {"name": "hoa/event", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Event.git", "reference": "6c0060dced212ffa3af0e34bb46624f990b29c54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Event/zipball/6c0060dced212ffa3af0e34bb46624f990b29c54", "reference": "6c0060dced212ffa3af0e34bb46624f990b29c54", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/exception": "~1.0"}, "require-dev": {"hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Event\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Event library.", "homepage": "https://hoa-project.net/", "keywords": ["event", "library", "listener", "observer"], "support": {"docs": "https://central.hoa-project.net/Documentation/Library/Event", "email": "<EMAIL>", "forum": "https://users.hoa-project.net/", "irc": "irc://chat.freenode.net/hoaproject", "issues": "https://github.com/hoaproject/Event/issues", "source": "https://central.hoa-project.net/Resource/Library/Event"}, "abandoned": true, "time": "2017-01-13T15:30:50+00:00"}, {"name": "hoa/exception", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Exception.git", "reference": "091727d46420a3d7468ef0595651488bfc3a458f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Exception/zipball/091727d46420a3d7468ef0595651488bfc3a458f", "reference": "091727d46420a3d7468ef0595651488bfc3a458f", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/event": "~1.0"}, "require-dev": {"hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Exception\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Exception library.", "homepage": "https://hoa-project.net/", "keywords": ["exception", "library"], "support": {"docs": "https://central.hoa-project.net/Documentation/Library/Exception", "email": "<EMAIL>", "forum": "https://users.hoa-project.net/", "irc": "irc://chat.freenode.net/hoaproject", "issues": "https://github.com/hoaproject/Exception/issues", "source": "https://central.hoa-project.net/Resource/Library/Exception"}, "abandoned": true, "time": "2017-01-16T07:53:27+00:00"}, {"name": "hoa/file", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/File.git", "reference": "35cb979b779bc54918d2f9a4e02ed6c7a1fa67ca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/File/zipball/35cb979b779bc54918d2f9a4e02ed6c7a1fa67ca", "reference": "35cb979b779bc54918d2f9a4e02ed6c7a1fa67ca", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/event": "~1.0", "hoa/exception": "~1.0", "hoa/iterator": "~2.0", "hoa/stream": "~1.0"}, "require-dev": {"hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Hoa\\File\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\File library.", "homepage": "https://hoa-project.net/", "keywords": ["Socket", "directory", "file", "finder", "library", "link", "temporary"], "support": {"docs": "https://central.hoa-project.net/Documentation/Library/File", "email": "<EMAIL>", "forum": "https://users.hoa-project.net/", "irc": "irc://chat.freenode.net/hoaproject", "issues": "https://github.com/hoaproject/File/issues", "source": "https://central.hoa-project.net/Resource/Library/File"}, "abandoned": true, "time": "2017-07-11T07:42:15+00:00"}, {"name": "hoa/iterator", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Iterator.git", "reference": "d1120ba09cb4ccd049c86d10058ab94af245f0cc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Iterator/zipball/d1120ba09cb4ccd049c86d10058ab94af245f0cc", "reference": "d1120ba09cb4ccd049c86d10058ab94af245f0cc", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/exception": "~1.0"}, "require-dev": {"hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Iterator\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Iterator library.", "homepage": "https://hoa-project.net/", "keywords": ["iterator", "library"], "support": {"docs": "https://central.hoa-project.net/Documentation/Library/Iterator", "email": "<EMAIL>", "forum": "https://users.hoa-project.net/", "irc": "irc://chat.freenode.net/hoaproject", "issues": "https://github.com/hoaproject/Iterator/issues", "source": "https://central.hoa-project.net/Resource/Library/Iterator"}, "abandoned": true, "time": "2017-01-10T10:34:47+00:00"}, {"name": "hoa/math", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Math.git", "reference": "7150785d30f5d565704912116a462e9f5bc83a0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Math/zipball/7150785d30f5d565704912116a462e9f5bc83a0c", "reference": "7150785d30f5d565704912116a462e9f5bc83a0c", "shasum": ""}, "require": {"hoa/compiler": "~3.0", "hoa/consistency": "~1.0", "hoa/exception": "~1.0", "hoa/iterator": "~2.0", "hoa/protocol": "~1.0", "hoa/zformat": "~1.0"}, "require-dev": {"hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Math\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Math library.", "homepage": "https://hoa-project.net/", "keywords": ["arrangement", "combination", "combinatorics", "counting", "library", "math", "permutation", "sampler", "set"], "support": {"docs": "https://central.hoa-project.net/Documentation/Library/Math", "email": "<EMAIL>", "forum": "https://users.hoa-project.net/", "irc": "irc://chat.freenode.net/hoaproject", "issues": "https://github.com/hoaproject/Math/issues", "source": "https://central.hoa-project.net/Resource/Library/Math"}, "abandoned": true, "time": "2017-05-16T08:02:17+00:00"}, {"name": "hoa/protocol", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Protocol.git", "reference": "5c2cf972151c45f373230da170ea015deecf19e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Protocol/zipball/5c2cf972151c45f373230da170ea015deecf19e2", "reference": "5c2cf972151c45f373230da170ea015deecf19e2", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/exception": "~1.0"}, "require-dev": {"hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Protocol\\": "."}, "files": ["Wrapper.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Protocol library.", "homepage": "https://hoa-project.net/", "keywords": ["library", "protocol", "resource", "stream", "wrapper"], "support": {"docs": "https://central.hoa-project.net/Documentation/Library/Protocol", "email": "<EMAIL>", "forum": "https://users.hoa-project.net/", "irc": "irc://chat.freenode.net/hoaproject", "issues": "https://github.com/hoaproject/Protocol/issues", "source": "https://central.hoa-project.net/Resource/Library/Protocol"}, "abandoned": true, "time": "2017-01-14T12:26:10+00:00"}, {"name": "hoa/regex", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Regex.git", "reference": "7e263a61b6fb45c1d03d8e5ef77668518abd5bec"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Regex/zipball/7e263a61b6fb45c1d03d8e5ef77668518abd5bec", "reference": "7e263a61b6fb45c1d03d8e5ef77668518abd5bec", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/exception": "~1.0", "hoa/math": "~1.0", "hoa/protocol": "~1.0", "hoa/ustring": "~4.0", "hoa/visitor": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Regex\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Regex library.", "homepage": "https://hoa-project.net/", "keywords": ["compiler", "library", "regex"], "support": {"docs": "https://central.hoa-project.net/Documentation/Library/Regex", "email": "<EMAIL>", "forum": "https://users.hoa-project.net/", "irc": "irc://chat.freenode.net/hoaproject", "issues": "https://github.com/hoaproject/Regex/issues", "source": "https://central.hoa-project.net/Resource/Library/Regex"}, "abandoned": true, "time": "2017-01-13T16:10:24+00:00"}, {"name": "hoa/ruler", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Ruler.git", "reference": "696835daf8336dfd490f032da7af444050e52dfc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Ruler/zipball/696835daf8336dfd490f032da7af444050e52dfc", "reference": "696835daf8336dfd490f032da7af444050e52dfc", "shasum": ""}, "require": {"hoa/compiler": "~3.0", "hoa/consistency": "~1.0", "hoa/exception": "~1.0", "hoa/file": "~1.0", "hoa/protocol": "~1.0", "hoa/visitor": "~2.0"}, "require-dev": {"hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Ruler\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Ruler library.", "homepage": "https://hoa-project.net/", "keywords": ["library", "ruler"], "support": {"docs": "https://central.hoa-project.net/Documentation/Library/Ruler", "email": "<EMAIL>", "forum": "https://users.hoa-project.net/", "irc": "irc://chat.freenode.net/hoaproject", "issues": "https://github.com/hoaproject/Ruler/issues", "source": "https://central.hoa-project.net/Resource/Library/Ruler"}, "abandoned": true, "time": "2017-05-16T07:52:21+00:00"}, {"name": "hoa/stream", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Stream.git", "reference": "3293cfffca2de10525df51436adf88a559151d82"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Stream/zipball/3293cfffca2de10525df51436adf88a559151d82", "reference": "3293cfffca2de10525df51436adf88a559151d82", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/event": "~1.0", "hoa/exception": "~1.0", "hoa/protocol": "~1.0"}, "require-dev": {"hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Stream\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Stream library.", "homepage": "https://hoa-project.net/", "keywords": ["Context", "bucket", "composite", "filter", "in", "library", "out", "protocol", "stream", "wrapper"], "support": {"docs": "https://central.hoa-project.net/Documentation/Library/Stream", "email": "<EMAIL>", "forum": "https://users.hoa-project.net/", "irc": "irc://chat.freenode.net/hoaproject", "issues": "https://github.com/hoaproject/Stream/issues", "source": "https://central.hoa-project.net/Resource/Library/Stream"}, "abandoned": true, "time": "2017-02-21T16:01:06+00:00"}, {"name": "hoa/ustring", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Ustring.git", "reference": "e6326e2739178799b1fe3fdd92029f9517fa17a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Ustring/zipball/e6326e2739178799b1fe3fdd92029f9517fa17a0", "reference": "e6326e2739178799b1fe3fdd92029f9517fa17a0", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/exception": "~1.0"}, "require-dev": {"hoa/test": "~2.0"}, "suggest": {"ext-iconv": "ext/iconv must be present (or a third implementation) to use Hoa\\Ustring::transcode().", "ext-intl": "To get a better Hoa\\Ustring::toAscii() and Hoa\\Ustring::compareTo()."}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Ustring\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Ustring library.", "homepage": "https://hoa-project.net/", "keywords": ["library", "search", "string", "unicode"], "support": {"docs": "https://central.hoa-project.net/Documentation/Library/Ustring", "email": "<EMAIL>", "forum": "https://users.hoa-project.net/", "irc": "irc://chat.freenode.net/hoaproject", "issues": "https://github.com/hoaproject/Ustring/issues", "source": "https://central.hoa-project.net/Resource/Library/Ustring"}, "abandoned": true, "time": "2017-01-16T07:08:25+00:00"}, {"name": "hoa/visitor", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Visitor.git", "reference": "c18fe1cbac98ae449e0d56e87469103ba08f224a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Visitor/zipball/c18fe1cbac98ae449e0d56e87469103ba08f224a", "reference": "c18fe1cbac98ae449e0d56e87469103ba08f224a", "shasum": ""}, "require": {"hoa/consistency": "~1.0"}, "require-dev": {"hoa/test": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Visitor\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Visitor library.", "homepage": "https://hoa-project.net/", "keywords": ["library", "structure", "visit", "visitor"], "support": {"docs": "https://central.hoa-project.net/Documentation/Library/Visitor", "email": "<EMAIL>", "forum": "https://users.hoa-project.net/", "irc": "irc://chat.freenode.net/hoaproject", "issues": "https://github.com/hoaproject/Visitor/issues", "source": "https://central.hoa-project.net/Resource/Library/Visitor"}, "abandoned": true, "time": "2017-01-16T07:02:03+00:00"}, {"name": "hoa/zformat", "version": "**********", "source": {"type": "git", "url": "https://github.com/hoaproject/Zformat.git", "reference": "522c381a2a075d4b9dbb42eb4592dd09520e4ac2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hoaproject/Zformat/zipball/522c381a2a075d4b9dbb42eb4592dd09520e4ac2", "reference": "522c381a2a075d4b9dbb42eb4592dd09520e4ac2", "shasum": ""}, "require": {"hoa/consistency": "~1.0", "hoa/exception": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Hoa\\Zformat\\": "."}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Hoa community", "homepage": "https://hoa-project.net/"}], "description": "The Hoa\\Zformat library.", "homepage": "https://hoa-project.net/", "keywords": ["library", "parameter", "zformat"], "support": {"docs": "https://central.hoa-project.net/Documentation/Library/Zformat", "email": "<EMAIL>", "forum": "https://users.hoa-project.net/", "irc": "irc://chat.freenode.net/hoaproject", "issues": "https://github.com/hoaproject/Zformat/issues", "source": "https://central.hoa-project.net/Resource/Library/Zformat"}, "abandoned": true, "time": "2017-01-10T10:39:54+00:00"}, {"name": "ifsnop/mysqldump-php", "version": "v2.9", "source": {"type": "git", "url": "https://github.com/ifsnop/mysqldump-php.git", "reference": "fc9c119fe5d70af9a685cad6a8ac612fd7589e25"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ifsnop/mysqldump-php/zipball/fc9c119fe5d70af9a685cad6a8ac612fd7589e25", "reference": "fc9c119fe5d70af9a685cad6a8ac612fd7589e25", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "4.8.36", "squizlabs/php_codesniffer": "1.*"}, "type": "library", "autoload": {"psr-4": {"Ifsnop\\": "src/Ifsnop/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/ifsnop", "role": "Developer"}], "description": "PHP version of mysqldump cli that comes with MySQL", "homepage": "https://github.com/ifsnop/mysqldump-php", "keywords": ["PHP7", "database", "hhvm", "ma<PERSON>b", "mysql", "mysql-backup", "mysqldump", "pdo", "php", "php5", "sql"], "support": {"issues": "https://github.com/ifsnop/mysqldump-php/issues", "source": "https://github.com/ifsnop/mysqldump-php/tree/master"}, "time": "2020-04-03T14:40:40+00:00"}, {"name": "influxdb/influxdb-php", "version": "1.15.1", "source": {"type": "git", "url": "https://github.com/influxdata/influxdb-php.git", "reference": "447acb600969f9510c9f1900a76d442fc3537b0e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/influxdata/influxdb-php/zipball/447acb600969f9510c9f1900a76d442fc3537b0e", "reference": "447acb600969f9510c9f1900a76d442fc3537b0e", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.0|^7.0", "php": "^5.5 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^5.7"}, "suggest": {"ext-curl": "Curl extension, needed for <PERSON><PERSON><PERSON> driver", "stefanotorresi/influxdb-php-async": "An asyncronous client for InfluxDB, implemented via ReactPHP."}, "type": "library", "autoload": {"psr-4": {"InfluxDB\\": "src/InfluxDB"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "InfluxDB client library for PHP", "keywords": ["client", "influxdata", "influxdb", "influxdb class", "influxdb client", "influxdb library", "time series"], "support": {"issues": "https://github.com/influxdata/influxdb-php/issues", "source": "https://github.com/influxdata/influxdb-php/tree/1.15.1"}, "time": "2020-09-18T13:24:03+00:00"}, {"name": "intervention/image", "version": "2.5.1", "source": {"type": "git", "url": "https://github.com/Intervention/image.git", "reference": "abbf18d5ab8367f96b3205ca3c89fb2fa598c69e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Intervention/image/zipball/abbf18d5ab8367f96b3205ca3c89fb2fa598c69e", "reference": "abbf18d5ab8367f96b3205ca3c89fb2fa598c69e", "shasum": ""}, "require": {"ext-fileinfo": "*", "guzzlehttp/psr7": "~1.1", "php": ">=5.4.0"}, "require-dev": {"mockery/mockery": "~0.9.2", "phpunit/phpunit": "^4.8 || ^5.7"}, "suggest": {"ext-gd": "to use GD library based image processing.", "ext-imagick": "to use Imagick based image processing.", "intervention/imagecache": "Caching extension for the Intervention Image library"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}, "laravel": {"providers": ["Intervention\\Image\\ImageServiceProvider"], "aliases": {"Image": "Intervention\\Image\\Facades\\Image"}}}, "autoload": {"psr-4": {"Intervention\\Image\\": "src/Intervention/Image"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://olivervogel.com/"}], "description": "Image handling and manipulation library with support for Laravel integration", "homepage": "http://image.intervention.io/", "keywords": ["gd", "image", "imagick", "laravel", "thumbnail", "watermark"], "support": {"issues": "https://github.com/Intervention/image/issues", "source": "https://github.com/Intervention/image/tree/master"}, "time": "2019-11-02T09:15:47+00:00"}, {"name": "jdorn/sql-formatter", "version": "v1.2.17", "source": {"type": "git", "url": "https://github.com/jdorn/sql-formatter.git", "reference": "64990d96e0959dff8e059dfcdc1af130728d92bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jdorn/sql-formatter/zipball/64990d96e0959dff8e059dfcdc1af130728d92bc", "reference": "64990d96e0959dff8e059dfcdc1af130728d92bc", "shasum": ""}, "require": {"php": ">=5.2.4"}, "require-dev": {"phpunit/phpunit": "3.7.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"classmap": ["lib"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://jeremydorn.com/"}], "description": "a PHP SQL highlighting library", "homepage": "https://github.com/jdorn/sql-formatter/", "keywords": ["highlight", "sql"], "support": {"issues": "https://github.com/jdorn/sql-formatter/issues", "source": "https://github.com/jdorn/sql-formatter/tree/master"}, "time": "2014-01-12T16:20:24+00:00"}, {"name": "knplabs/knp-snappy", "version": "v1.2.1", "source": {"type": "git", "url": "https://github.com/KnpLabs/snappy.git", "reference": "7bac60fb729147b7ccd8532c07df3f52a4afa8a4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/snappy/zipball/7bac60fb729147b7ccd8532c07df3f52a4afa8a4", "reference": "7bac60fb729147b7ccd8532c07df3f52a4afa8a4", "shasum": ""}, "require": {"php": ">=7.1", "psr/log": "^1.0", "symfony/process": "~3.4||~4.3||~5.0"}, "require-dev": {"phpunit/phpunit": "~7.4"}, "suggest": {"h4cc/wkhtmltoimage-amd64": "Provides wkhtmltoimage-amd64 binary for Linux-compatible machines, use version `~0.12` as dependency", "h4cc/wkhtmltoimage-i386": "Provides wkhtmltoimage-i386 binary for Linux-compatible machines, use version `~0.12` as dependency", "h4cc/wkhtmltopdf-amd64": "Provides wkhtmltopdf-amd64 binary for Linux-compatible machines, use version `~0.12` as dependency", "h4cc/wkhtmltopdf-i386": "Provides wkhtmltopdf-i386 binary for Linux-compatible machines, use version `~0.12` as dependency", "wemersonjanuario/wkhtmltopdf-windows": "Provides wkhtmltopdf executable for Windows, use version `~0.12` as dependency"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Knp\\Snappy\\": "src/Knp/Snappy"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KnpLabs Team", "homepage": "http://knplabs.com"}, {"name": "Symfony Community", "homepage": "http://github.com/KnpLabs/snappy/contributors"}], "description": "PHP5 library allowing thumbnail, snapshot or PDF generation from a url or a html page. Wrapper for wkhtmltopdf/wkhtmltoimage.", "homepage": "http://github.com/KnpLabs/snappy", "keywords": ["knp", "knplabs", "pdf", "snapshot", "thumbnail", "wkhtmltopdf"], "support": {"issues": "https://github.com/KnpLabs/snappy/issues", "source": "https://github.com/KnpLabs/snappy/tree/master"}, "time": "2020-01-20T08:30:30+00:00"}, {"name": "knplabs/knp-snappy-bundle", "version": "v1.8.0", "source": {"type": "git", "url": "https://github.com/KnpLabs/KnpSnappyBundle.git", "reference": "0f81887b0379a2731b869289bf3d880f34dfd423"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/KnpSnappyBundle/zipball/0f81887b0379a2731b869289bf3d880f34dfd423", "reference": "0f81887b0379a2731b869289bf3d880f34dfd423", "shasum": ""}, "require": {"knplabs/knp-snappy": "^1.2", "php": ">=7.2.5", "symfony/framework-bundle": "^4.4|^5.1"}, "require-dev": {"doctrine/annotations": "^1.11", "symfony/asset": "^4.4|^5.1", "symfony/finder": "^4.4|^5.1", "symfony/phpunit-bridge": "^4.4|^5.1", "symfony/security-csrf": "^4.4|^5.1", "symfony/templating": "^4.4|^5.1", "symfony/validator": "^4.4|^5.1", "symfony/yaml": "^4.4|^5.1"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Knp\\Bundle\\SnappyBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KnpLabs Team", "homepage": "http://knplabs.com"}, {"name": "Symfony Community", "homepage": "http://github.com/KnpLabs/KnpSnappyBundle/contributors"}], "description": "Easily create PDF and images in Symfony by converting Twig/HTML templates.", "homepage": "http://github.com/KnpLabs/KnpSnappyBundle", "keywords": ["bundle", "knp", "knplabs", "pdf", "snappy"], "support": {"issues": "https://github.com/KnpLabs/KnpSnappyBundle/issues", "source": "https://github.com/KnpLabs/KnpSnappyBundle/tree/v1.8.0"}, "time": "2020-11-16T07:45:16+00:00"}, {"name": "kphoen/rulerz", "version": "0.21.1", "source": {"type": "git", "url": "https://github.com/K-Phoen/rulerz.git", "reference": "d28a1bd59b4e66cc9fcdeee965f13f685eb9ce41"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/K-Phoen/rulerz/zipball/d28a1bd59b4e66cc9fcdeee965f13f685eb9ce41", "reference": "d28a1bd59b4e66cc9fcdeee965f13f685eb9ce41", "shasum": ""}, "require": {"hoa/ruler": "~2.0", "php": ">=7.1", "symfony/property-access": "~3.0|~4.0"}, "require-dev": {"behat/behat": "~3.0", "coduo/phpspec-data-provider-extension": "~1.0,!=1.0.2", "doctrine/orm": "~2.4", "elasticsearch/elasticsearch": "~1.0", "illuminate/database": "~5.0", "kphoen/rusty": "dev-master", "liip/rmt": "^1.2", "mikey179/vfsstream": "~1.4", "phpspec/phpspec": "~2.0,>=2.4-dev", "pomm-project/cli": "~2.0@dev", "pomm-project/foundation": "~2.0@dev", "pomm-project/model-manager": "~2.0.@dev", "ruflin/elastica": "~1.0", "solarium/solarium": "~3.0", "vlucas/phpdotenv": "~2.1"}, "suggest": {"doctrine/orm": "To execute rules as Doctrine queries", "elasticsearch/elasticsearch": "To execute rules as Elasticsearch queries", "kphoen/rulerz-spec-builder": "If you want a specification builder", "pomm-project/model-manager": "To execute rules as Pomm queries", "solarium/solarium": "To execute rules as Solr queries"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"RulerZ\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Powerful implementation of the Specification pattern", "homepage": "https://github.com/K-Phoen/RulerZ", "keywords": ["doctrine", "specification"], "support": {"issues": "https://github.com/K-Phoen/rulerz/issues", "source": "https://github.com/K-Phoen/rulerz/tree/0.21.1"}, "time": "2018-09-18T15:15:42+00:00"}, {"name": "laminas/laminas-code", "version": "4.5.1", "source": {"type": "git", "url": "https://github.com/laminas/laminas-code.git", "reference": "6fd96d4d913571a2cd056a27b123fa28cb90ac4e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-code/zipball/6fd96d4d913571a2cd056a27b123fa28cb90ac4e", "reference": "6fd96d4d913571a2cd056a27b123fa28cb90ac4e", "shasum": ""}, "require": {"php": ">=7.4, <8.2"}, "require-dev": {"doctrine/annotations": "^1.13.2", "ext-phar": "*", "laminas/laminas-coding-standard": "^2.3.0", "laminas/laminas-stdlib": "^3.6.1", "phpunit/phpunit": "^9.5.10", "psalm/plugin-phpunit": "^0.16.1", "vimeo/psalm": "^4.13.1"}, "suggest": {"doctrine/annotations": "Doctrine\\Common\\Annotations >=1.0 for annotation features", "laminas/laminas-stdlib": "Laminas\\Stdlib component"}, "type": "library", "autoload": {"files": ["polyfill/ReflectionEnumPolyfill.php"], "psr-4": {"Laminas\\Code\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Extensions to the PHP Reflection API, static code scanning, and code generation", "homepage": "https://laminas.dev", "keywords": ["code", "laminas", "laminasframework"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-code/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-code/issues", "rss": "https://github.com/laminas/laminas-code/releases.atom", "source": "https://github.com/laminas/laminas-code"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2021-12-19T18:06:55+00:00"}, {"name": "lcobucci/jwt", "version": "3.4.6", "source": {"type": "git", "url": "https://github.com/lcobucci/jwt.git", "reference": "3ef8657a78278dfeae7707d51747251db4176240"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lcobucci/jwt/zipball/3ef8657a78278dfeae7707d51747251db4176240", "reference": "3ef8657a78278dfeae7707d51747251db4176240", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-openssl": "*", "php": "^5.6 || ^7.0"}, "require-dev": {"mikey179/vfsstream": "~1.5", "phpmd/phpmd": "~2.2", "phpunit/php-invoker": "~1.1", "phpunit/phpunit": "^5.7 || ^7.3", "squizlabs/php_codesniffer": "~2.3"}, "suggest": {"lcobucci/clock": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"files": ["compat/class-aliases.php", "compat/json-exception-polyfill.php", "compat/lcobucci-clock-polyfill.php"], "psr-4": {"Lcobucci\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to work with JSON Web Token and JSON Web Signature", "keywords": ["JWS", "jwt"], "support": {"issues": "https://github.com/lcobucci/jwt/issues", "source": "https://github.com/lcobucci/jwt/tree/3.4.6"}, "funding": [{"url": "https://github.com/lcobucci", "type": "github"}, {"url": "https://www.patreon.com/lcobucci", "type": "patreon"}], "time": "2021-09-28T19:18:28+00:00"}, {"name": "league/flysystem", "version": "1.1.5", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "18634df356bfd4119fe3d6156bdb990c414c14ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/18634df356bfd4119fe3d6156bdb990c414c14ea", "reference": "18634df356bfd4119fe3d6156bdb990c414c14ea", "shasum": ""}, "require": {"ext-fileinfo": "*", "league/mime-type-detection": "^1.3", "php": "^7.2.5 || ^8.0"}, "conflict": {"league/flysystem-sftp": "<1.0.6"}, "require-dev": {"phpspec/prophecy": "^1.11.1", "phpunit/phpunit": "^8.5.8"}, "suggest": {"ext-ftp": "Allows you to use FTP server storage", "ext-openssl": "Allows you to use FTPS server storage", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage", "srmklive/flysystem-dropbox-v2": "Allows you to use Dropbox storage for PHP 5 applications"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"League\\Flysystem\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Filesystem abstraction: Many filesystems, one API.", "keywords": ["Cloud Files", "WebDAV", "abstraction", "aws", "cloud", "copy.com", "dropbox", "file systems", "files", "filesystem", "filesystems", "ftp", "rackspace", "remote", "s3", "sftp", "storage"], "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.1.5"}, "funding": [{"url": "https://offset.earth/frankdejonge", "type": "other"}], "time": "2021-08-17T13:49:42+00:00"}, {"name": "league/flysystem-aws-s3-v3", "version": "1.0.29", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem-aws-s3-v3.git", "reference": "4e25cc0582a36a786c31115e419c6e40498f6972"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem-aws-s3-v3/zipball/4e25cc0582a36a786c31115e419c6e40498f6972", "reference": "4e25cc0582a36a786c31115e419c6e40498f6972", "shasum": ""}, "require": {"aws/aws-sdk-php": "^3.20.0", "league/flysystem": "^1.0.40", "php": ">=5.5.0"}, "require-dev": {"henrikbjorn/phpspec-code-coverage": "~1.0.1", "phpspec/phpspec": "^2.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"League\\Flysystem\\AwsS3v3\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Flysystem adapter for the AWS S3 SDK v3.x", "support": {"issues": "https://github.com/thephpleague/flysystem-aws-s3-v3/issues", "source": "https://github.com/thephpleague/flysystem-aws-s3-v3/tree/1.0.29"}, "time": "2020-10-08T18:58:37+00:00"}, {"name": "league/flysystem-azure-blob-storage", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem-azure-blob-storage.git", "reference": "fbfd44cd758fefa6d3de26a962fba5db663497c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem-azure-blob-storage/zipball/fbfd44cd758fefa6d3de26a962fba5db663497c4", "reference": "fbfd44cd758fefa6d3de26a962fba5db663497c4", "shasum": ""}, "require": {"guzzlehttp/psr7": "^1.5", "league/flysystem": "^1.0", "microsoft/azure-storage-blob": "^1.1", "php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "^5.7"}, "type": "library", "autoload": {"psr-4": {"League\\Flysystem\\AzureBlobStorage\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/thephpleague/flysystem-azure-blob-storage/issues", "source": "https://github.com/thephpleague/flysystem-azure-blob-storage/tree/master"}, "time": "2020-08-09T13:19:24+00:00"}, {"name": "league/flysystem-cached-adapter", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem-cached-adapter.git", "reference": "d1925efb2207ac4be3ad0c40b8277175f99ffaff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem-cached-adapter/zipball/d1925efb2207ac4be3ad0c40b8277175f99ffaff", "reference": "d1925efb2207ac4be3ad0c40b8277175f99ffaff", "shasum": ""}, "require": {"league/flysystem": "~1.0", "psr/cache": "^1.0.0"}, "require-dev": {"mockery/mockery": "~0.9", "phpspec/phpspec": "^3.4", "phpunit/phpunit": "^5.7", "predis/predis": "~1.0", "tedivm/stash": "~0.12"}, "suggest": {"ext-phpredis": "Pure C implemented extension for PHP"}, "type": "library", "autoload": {"psr-4": {"League\\Flysystem\\Cached\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "frank<PERSON><PERSON>e", "email": "<EMAIL>"}], "description": "An adapter decorator to enable meta-data caching.", "support": {"issues": "https://github.com/thephpleague/flysystem-cached-adapter/issues", "source": "https://github.com/thephpleague/flysystem-cached-adapter/tree/master"}, "time": "2020-07-25T15:56:04+00:00"}, {"name": "league/mime-type-detection", "version": "1.8.0", "source": {"type": "git", "url": "https://github.com/thephpleague/mime-type-detection.git", "reference": "b38b25d7b372e9fddb00335400467b223349fd7e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/b38b25d7b372e9fddb00335400467b223349fd7e", "reference": "b38b25d7b372e9fddb00335400467b223349fd7e", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.18", "phpstan/phpstan": "^0.12.68", "phpunit/phpunit": "^8.5.8 || ^9.3"}, "type": "library", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mime-type detection for Flysystem", "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.8.0"}, "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2021-09-25T08:23:19+00:00"}, {"name": "mangopay/php-sdk-v2", "version": "3.1.4", "source": {"type": "git", "url": "https://github.com/Mangopay/mangopay2-php-sdk.git", "reference": "6d9c17c5e8cc042da19f91c7bbaeccfac475bd41"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Mangopay/mangopay2-php-sdk/zipball/6d9c17c5e8cc042da19f91c7bbaeccfac475bd41", "reference": "6d9c17c5e8cc042da19f91c7bbaeccfac475bd41", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-openssl": "*", "php": ">=5.6", "psr/log": "^1.0"}, "require-dev": {"phpunit/phpunit": "^4.8.36 || ^5.7.27 || 7.3"}, "type": "library", "autoload": {"psr-4": {"MangoPay\\": "MangoPay/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHP SDK for MANGOPAY", "support": {"issues": "https://github.com/Mangopay/mangopay2-php-sdk/issues", "source": "https://github.com/Mangopay/mangopay2-php-sdk/tree/3.1.4"}, "time": "2020-10-23T11:47:04+00:00"}, {"name": "matthiasnoback/broadway-serialization", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/matthiasnoback/broadway-serialization.git", "reference": "de52b13ecdb6f8efee4bdceb913734ead003dca9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/matthiasnoback/broadway-serialization/zipball/de52b13ecdb6f8efee4bdceb913734ead003dca9", "reference": "de52b13ecdb6f8efee4bdceb913734ead003dca9", "shasum": ""}, "require": {"broadway/broadway": "~0.4", "doctrine/instantiator": "~1.0", "php": ">=5.5.9"}, "require-dev": {"phpbench/phpbench": "^0.11.2", "phpunit/phpunit": "^4.7", "satooshi/php-coveralls": "^0.6.1", "symfony/framework-bundle": "~2.7"}, "type": "library", "autoload": {"psr-4": {"BroadwaySerialization\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://php-and-symfony.matthiasnoback.nl"}], "description": "Serialization helpers for Broadway", "homepage": "http://github.com/matthiasnoback/broadway-serialization", "keywords": ["broadway", "serialization"], "support": {"issues": "https://github.com/matthiasnoback/broadway-serialization/issues", "source": "https://github.com/matthiasnoback/broadway-serialization/tree/master"}, "time": "2016-07-22T17:27:40+00:00"}, {"name": "microsoft/azure-storage-blob", "version": "1.5.1", "source": {"type": "git", "url": "https://github.com/Azure/azure-storage-blob-php.git", "reference": "44f9268b3510a18450c591b1155a49399edb0c9b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Azure/azure-storage-blob-php/zipball/44f9268b3510a18450c591b1155a49399edb0c9b", "reference": "44f9268b3510a18450c591b1155a49399edb0c9b", "shasum": ""}, "require": {"microsoft/azure-storage-common": "~1.5", "php": ">=5.6.0"}, "type": "library", "autoload": {"psr-4": {"MicrosoftAzure\\Storage\\Blob\\": "src/Blob"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Azure Storage PHP Client Library", "email": "<EMAIL>"}], "description": "This project provides a set of PHP client libraries that make it easy to access Microsoft Azure Storage Blob APIs.", "keywords": ["azure", "blob", "php", "sdk", "storage"], "support": {"issues": "https://github.com/Azure/azure-storage-blob-php/issues", "source": "https://github.com/Azure/azure-storage-blob-php/tree/v1.5.1"}, "time": "2020-08-28T09:40:50+00:00"}, {"name": "microsoft/azure-storage-common", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/Azure/azure-storage-common-php.git", "reference": "fe85677aa5188f8efe6916b4d6773a194e2c2ede"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Azure/azure-storage-common-php/zipball/fe85677aa5188f8efe6916b4d6773a194e2c2ede", "reference": "fe85677aa5188f8efe6916b4d6773a194e2c2ede", "shasum": ""}, "require": {"guzzlehttp/guzzle": "~6.0", "php": ">=5.6.0"}, "type": "library", "autoload": {"psr-4": {"MicrosoftAzure\\Storage\\Common\\": "src/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Azure Storage PHP Client Library", "email": "<EMAIL>"}], "description": "This project provides a set of common code shared by Azure Storage Blob, Table, Queue and File PHP client libraries.", "keywords": ["azure", "common", "php", "sdk", "storage"], "support": {"issues": "https://github.com/Azure/azure-storage-common-php/issues", "source": "https://github.com/Azure/azure-storage-common-php/tree/master"}, "time": "2020-08-28T09:02:11+00:00"}, {"name": "monolog/monolog", "version": "1.27.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "52ebd235c1f7e0d5e1b16464b695a28335f8e44a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/52ebd235c1f7e0d5e1b16464b695a28335f8e44a", "reference": "52ebd235c1f7e0d5e1b16464b695a28335f8e44a", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpstan/phpstan": "^0.12.59", "phpunit/phpunit": "~4.5", "ruflin/elastica": ">=0.90 <3.0", "sentry/sentry": "^0.13", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "sentry/sentry": "Allow sending log messages to a Sentry server"}, "type": "library", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.27.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2022-03-13T20:29:46+00:00"}, {"name": "mtdowling/jmespath.php", "version": "2.6.0", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "42dae2cbd13154083ca6d70099692fef8ca84bfb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/42dae2cbd13154083ca6d70099692fef8ca84bfb", "reference": "42dae2cbd13154083ca6d70099692fef8ca84bfb", "shasum": ""}, "require": {"php": "^5.4 || ^7.0 || ^8.0", "symfony/polyfill-mbstring": "^1.17"}, "require-dev": {"composer/xdebug-handler": "^1.4", "phpunit/phpunit": "^4.8.36 || ^7.5.15"}, "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.6-dev"}}, "autoload": {"files": ["src/JmesPath.php"], "psr-4": {"JmesPath\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "support": {"issues": "https://github.com/jmespath/jmespath.php/issues", "source": "https://github.com/jmespath/jmespath.php/tree/2.6.0"}, "time": "2020-07-31T21:01:56+00:00"}, {"name": "mustache/mustache", "version": "v2.14.1", "source": {"type": "git", "url": "https://github.com/bobthecow/mustache.php.git", "reference": "579ffa5c96e1d292c060b3dd62811ff01ad8c24e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/mustache.php/zipball/579ffa5c96e1d292c060b3dd62811ff01ad8c24e", "reference": "579ffa5c96e1d292c060b3dd62811ff01ad8c24e", "shasum": ""}, "require": {"php": ">=5.2.4"}, "require-dev": {"friendsofphp/php-cs-fixer": "~1.11", "phpunit/phpunit": "~3.7|~4.0|~5.0"}, "type": "library", "autoload": {"psr-0": {"Mustache": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "A Mustache implementation in PHP.", "homepage": "https://github.com/bobthecow/mustache.php", "keywords": ["mustache", "templating"], "support": {"issues": "https://github.com/bobthecow/mustache.php/issues", "source": "https://github.com/bobthecow/mustache.php/tree/v2.14.1"}, "time": "2022-01-21T06:08:36+00:00"}, {"name": "myclabs/php-enum", "version": "1.7.7", "source": {"type": "git", "url": "https://github.com/myclabs/php-enum.git", "reference": "d178027d1e679832db9f38248fcc7200647dc2b7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/php-enum/zipball/d178027d1e679832db9f38248fcc7200647dc2b7", "reference": "d178027d1e679832db9f38248fcc7200647dc2b7", "shasum": ""}, "require": {"ext-json": "*", "php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7", "squizlabs/php_codesniffer": "1.*", "vimeo/psalm": "^3.8"}, "type": "library", "autoload": {"psr-4": {"MyCLabs\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP Enum contributors", "homepage": "https://github.com/myclabs/php-enum/graphs/contributors"}], "description": "PHP Enum implementation", "homepage": "http://github.com/myclabs/php-enum", "keywords": ["enum"], "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.7.7"}, "funding": [{"url": "https://github.com/mnapoli", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/myclabs/php-enum", "type": "tidelift"}], "time": "2020-11-14T18:14:52+00:00"}, {"name": "nelmio/cors-bundle", "version": "1.5.6", "source": {"type": "git", "url": "https://github.com/nelmio/NelmioCorsBundle.git", "reference": "10a24c10f242440211ed31075e74f81661c690d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nelmio/NelmioCorsBundle/zipball/10a24c10f242440211ed31075e74f81661c690d9", "reference": "10a24c10f242440211ed31075e74f81661c690d9", "shasum": ""}, "require": {"symfony/framework-bundle": "^2.7 || ^3.0 || ^4.0"}, "require-dev": {"matthiasnoback/symfony-dependency-injection-test": "^1.0 || ^2.0", "mockery/mockery": "^0.9 || ^1.0", "symfony/phpunit-bridge": "^2.7 || ^3.0 || ^4.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "autoload": {"psr-4": {"Nelmio\\CorsBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Nelmio", "homepage": "http://nelm.io"}, {"name": "Symfony Community", "homepage": "https://github.com/nelmio/NelmioCorsBundle/contributors"}], "description": "Adds CORS (Cross-Origin Resource Sharing) headers support in your Symfony2 application", "keywords": ["api", "cors", "crossdomain"], "support": {"issues": "https://github.com/nelmio/NelmioCorsBundle/issues", "source": "https://github.com/nelmio/NelmioCorsBundle/tree/1.5.6"}, "time": "2019-06-17T08:53:14+00:00"}, {"name": "nikic/iter", "version": "v2.1.0", "source": {"type": "git", "url": "https://github.com/nikic/iter.git", "reference": "a7f3aa313c1315e14cf1d7e520c0f781f584a42f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/iter/zipball/a7f3aa313c1315e14cf1d7e520c0f781f584a42f", "reference": "a7f3aa313c1315e14cf1d7e520c0f781f584a42f", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "~7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"files": ["src/iter.func.php", "src/iter.php", "src/iter.rewindable.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Iteration primitives using generators", "keywords": ["functional", "generator", "iterator"], "support": {"issues": "https://github.com/nikic/iter/issues", "source": "https://github.com/nikic/iter/tree/v2.1.0"}, "time": "2020-09-19T15:58:13+00:00"}, {"name": "ocramius/doctrine-batch-utils", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/Ocramius/DoctrineBatchUtils.git", "reference": "b9489288c8c544f8c948c284aea7ca63a4298b5b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Ocramius/DoctrineBatchUtils/zipball/b9489288c8c544f8c948c284aea7ca63a4298b5b", "reference": "b9489288c8c544f8c948c284aea7ca63a4298b5b", "shasum": ""}, "require": {"doctrine/common": "^2.8.1", "doctrine/orm": "^2.6.0", "php": "^7.1.0"}, "require-dev": {"phpunit/phpunit": "^7.0.0"}, "type": "library", "autoload": {"psr-4": {"DoctrineBatchUtils\\": "src/DoctrineBatchUtils"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.io/"}], "description": "A set of utilities to operate with Doctrine ORM's batch processing functionality", "homepage": "https://github.com/Ocramius/DoctrineBatchUtils", "keywords": ["batch processing", "doctrine", "doctrine2", "orm"], "support": {"issues": "https://github.com/Ocramius/DoctrineBatchUtils/issues", "source": "https://github.com/Ocramius/DoctrineBatchUtils/tree/master"}, "time": "2018-02-05T11:40:33+00:00"}, {"name": "paragonie/random_compat", "version": "v2.0.21", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "96c132c7f2f7bc3230723b66e89f8f150b29d5ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/96c132c7f2f7bc3230723b66e89f8f150b29d5ae", "reference": "96c132c7f2f7bc3230723b66e89f8f150b29d5ae", "shasum": ""}, "require": {"php": ">=5.2.0"}, "require-dev": {"phpunit/phpunit": "*"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "autoload": {"files": ["lib/random.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2022-02-16T17:07:03+00:00"}, {"name": "php-amqplib/php-amqplib", "version": "v2.12.1", "source": {"type": "git", "url": "https://github.com/php-amqplib/php-amqplib.git", "reference": "0eaaa9d5d45335f4342f69603288883388c2fe21"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-amqplib/php-amqplib/zipball/0eaaa9d5d45335f4342f69603288883388c2fe21", "reference": "0eaaa9d5d45335f4342f69603288883388c2fe21", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-sockets": "*", "php": ">=5.6.3", "phpseclib/phpseclib": "^2.0.0"}, "conflict": {"php": "7.4.0 - 7.4.1"}, "replace": {"videlalvaro/php-amqplib": "self.version"}, "require-dev": {"ext-curl": "*", "nategood/httpful": "^0.2.20", "phpunit/phpunit": "^5.7|^6.5|^7.0", "squizlabs/php_codesniffer": "^2.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.12-dev"}}, "autoload": {"psr-4": {"PhpAmqpLib\\": "PhpAmqpLib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON><PERSON>", "role": "Original Maintainer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Maintainer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Maintainer"}], "description": "Formerly videlalvaro/php-amqplib.  This library is a pure PHP implementation of the AMQP protocol. It's been tested against RabbitMQ.", "homepage": "https://github.com/php-amqplib/php-amqplib/", "keywords": ["message", "queue", "rabbitmq"], "support": {"issues": "https://github.com/php-amqplib/php-amqplib/issues", "source": "https://github.com/php-amqplib/php-amqplib/tree/v2.12.1"}, "time": "2020-09-25T18:34:58+00:00"}, {"name": "phpseclib/phpseclib", "version": "2.0.31", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "233a920cb38636a43b18d428f9a8db1f0a1a08f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/233a920cb38636a43b18d428f9a8db1f0a1a08f4", "reference": "233a920cb38636a43b18d428f9a8db1f0a1a08f4", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phing/phing": "~2.7", "phpunit/phpunit": "^4.8.35|^5.7|^6.0|^9.4", "squizlabs/php_codesniffer": "~2.0"}, "suggest": {"ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations."}, "type": "library", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib\\": "phpseclib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/2.0.31"}, "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "time": "2021-04-06T13:56:45+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/master"}, "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/container", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/513e0666f7216c7459170d56df27dfcefe1689ea", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.2"}, "time": "2021-11-05T16:50:12+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/master"}, "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/link", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/link.git", "reference": "eea8e8662d5cd3ae4517c9b864493f59fca95562"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/link/zipball/eea8e8662d5cd3ae4517c9b864493f59fca95562", "reference": "eea8e8662d5cd3ae4517c9b864493f59fca95562", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Link\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for HTTP links", "keywords": ["http", "http-link", "link", "psr", "psr-13", "rest"], "support": {"source": "https://github.com/php-fig/link/tree/master"}, "time": "2016-10-28T16:06:13+00:00"}, {"name": "psr/log", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "time": "2021-05-03T11:20:27+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/master"}, "time": "2017-10-23T01:57:42+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "ramsey/uuid", "version": "2.9.0", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "b2ef4dd9584268d73f92f752a62bc24cd534dc9a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/b2ef4dd9584268d73f92f752a62bc24cd534dc9a", "reference": "b2ef4dd9584268d73f92f752a62bc24cd534dc9a", "shasum": ""}, "require": {"paragonie/random_compat": "^1.0|^2.0", "php": ">=5.3.3"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"doctrine/dbal": ">=2.3", "jakub-onderka/php-parallel-lint": "^0.9.0", "moontoast/math": "~1.1", "phpunit/phpunit": "~4.1|~5.0", "satooshi/php-coveralls": "~0.6", "squizlabs/php_codesniffer": "^2.3", "symfony/console": "~2.3|~3.0"}, "suggest": {"doctrine/dbal": "Allow the use of a UUID as doctrine field type.", "moontoast/math": "Support for converting UUID to 128-bit integer (in string form).", "symfony/console": "Support for use of the bin/uuid command line tool."}, "bin": ["bin/uuid"], "type": "library", "autoload": {"psr-4": {"Rhumsaa\\Uuid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://benramsey.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A PHP 5.3+ library for generating RFC 4122 version 1, 3, 4, and 5 universally unique identifiers (UUID).", "homepage": "https://github.com/ramsey/uuid", "keywords": ["guid", "identifier", "uuid"], "support": {"issues": "https://github.com/ramsey/uuid/issues", "source": "https://github.com/ramsey/uuid"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/ramsey/uuid", "type": "tidelift"}], "time": "2016-03-22T18:20:19+00:00"}, {"name": "robmorgan/phinx", "version": "0.10.8", "source": {"type": "git", "url": "https://github.com/cakephp/phinx.git", "reference": "1960e93169707096fdfde04904a204970077f4be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cakephp/phinx/zipball/1960e93169707096fdfde04904a204970077f4be", "reference": "1960e93169707096fdfde04904a204970077f4be", "shasum": ""}, "require": {"cakephp/collection": "^3.6", "cakephp/database": "^3.6", "php": ">=5.6", "symfony/config": "^2.8|^3.0|^4.0", "symfony/console": "^2.8|^3.0|^4.0", "symfony/yaml": "^2.8|^3.0|^4.0"}, "require-dev": {"cakephp/cakephp-codesniffer": "^3.0", "phpunit/phpunit": ">=5.7,<7.0", "sebastian/comparator": ">=1.2.3"}, "bin": ["bin/phinx"], "type": "library", "autoload": {"psr-4": {"Phinx\\": "src/Phinx/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://shadowhand.me", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://robmorgan.id.au", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "CakePHP Community", "homepage": "https://github.com/cakephp/phinx/graphs/contributors"}], "description": "Phinx makes it ridiculously easy to manage the database migrations for your PHP app.", "homepage": "https://phinx.org", "keywords": ["database", "database migrations", "db", "migrations", "phinx"], "support": {"issues": "https://github.com/cakephp/phinx/issues", "source": "https://github.com/cakephp/phinx/tree/v0.10.8"}, "time": "2019-07-08T16:59:55+00:00"}, {"name": "smarty/smarty", "version": "v3.1.44", "source": {"type": "git", "url": "https://github.com/smarty-php/smarty.git", "reference": "99085d8dc65eeb5e55ae3cba74d3dc6b3bb0205e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/smarty-php/smarty/zipball/99085d8dc65eeb5e55ae3cba74d3dc6b3bb0205e", "reference": "99085d8dc65eeb5e55ae3cba74d3dc6b3bb0205e", "shasum": ""}, "require": {"php": ">=5.2"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^6.5 || ^5.7 || ^4.8", "smarty/smarty-lexer": "^3.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"classmap": ["libs/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Smarty - the compiling PHP template engine", "homepage": "http://www.smarty.net", "keywords": ["templating"], "support": {"forum": "http://www.smarty.net/forums/", "irc": "irc://irc.freenode.org/smarty", "issues": "https://github.com/smarty-php/smarty/issues", "source": "https://github.com/smarty-php/smarty/tree/v3.1.44"}, "time": "2022-01-17T23:12:04+00:00"}, {"name": "snc/redis-bundle", "version": "2.1.13", "source": {"type": "git", "url": "https://github.com/snc/SncRedisBundle.git", "reference": "7c8652c1811c573ae567c1bcaa5e1c65723c5907"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/snc/SncRedisBundle/zipball/7c8652c1811c573ae567c1bcaa5e1c65723c5907", "reference": "7c8652c1811c573ae567c1bcaa5e1c65723c5907", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0", "symfony/framework-bundle": "^2.7 || ^3.0 || ^4.0", "symfony/yaml": "^2.7 || ^3.0 || ^4.0"}, "require-dev": {"doctrine/cache": "1.*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.5", "predis/predis": "^1.0", "symfony/console": "^2.7 || ^3.0 || ^4.0", "symfony/phpunit-bridge": "^2.7 || ^3.0 || ^4.0"}, "suggest": {"monolog/monolog": "If you want to use the monolog redis handler.", "predis/predis": "If you want to use predis.", "symfony/console": "If you want to use commands to interact with the redis database", "symfony/proxy-manager-bridge": "If you want to lazy-load some services"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Snc\\RedisBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Community contributors", "homepage": "https://github.com/snc/SncRedisBundle/contributors"}], "description": "A Redis bundle for Symfony", "homepage": "https://github.com/snc/SncRedisBundle", "keywords": ["nosql", "redis", "symfony"], "support": {"issues": "https://github.com/snc/SncRedisBundle/issues", "source": "https://github.com/snc/SncRedisBundle/tree/2.1"}, "time": "2019-12-09T12:56:15+00:00"}, {"name": "steevanb/composer-overload-class", "version": "1.3.3", "source": {"type": "git", "url": "https://github.com/steevanb/composer-overload-class.git", "reference": "ad1f722caa1f7b4598d16c11f9e54362d6ce939f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/steevanb/composer-overload-class/zipball/ad1f722caa1f7b4598d16c11f9e54362d6ce939f", "reference": "ad1f722caa1f7b4598d16c11f9e54362d6ce939f", "shasum": ""}, "require": {"php": "^5.4.0 || ^7.0"}, "type": "library", "autoload": {"psr-4": {"steevanb\\ComposerOverloadClass\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Add extra to composer.json, to overload autoloaded class", "support": {"issues": "https://github.com/steevanb/composer-overload-class/issues", "source": "https://github.com/steevanb/composer-overload-class/tree/master"}, "time": "2019-12-05T14:31:24+00:00"}, {"name": "stripe/stripe-php", "version": "v6.43.1", "source": {"type": "git", "url": "https://github.com/stripe/stripe-php.git", "reference": "42fcdaf99c44bb26937223f8eae1f263491d5ab8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/stripe/stripe-php/zipball/42fcdaf99c44bb26937223f8eae1f263491d5ab8", "reference": "42fcdaf99c44bb26937223f8eae1f263491d5ab8", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "php": ">=5.4.0"}, "require-dev": {"php-coveralls/php-coveralls": "1.*", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~2.0", "symfony/process": "~2.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"Stripe\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Stripe and contributors", "homepage": "https://github.com/stripe/stripe-php/contributors"}], "description": "Stripe PHP Library", "homepage": "https://stripe.com/", "keywords": ["api", "payment processing", "stripe"], "support": {"issues": "https://github.com/stripe/stripe-php/issues", "source": "https://github.com/stripe/stripe-php/tree/master"}, "time": "2019-08-29T16:56:12+00:00"}, {"name": "studio-42/elfinder", "version": "2.1.61", "source": {"type": "git", "url": "https://github.com/Studio-42/elFinder.git", "reference": "33bee2654615db62e9b722efb4fdd2a21844fbb2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Studio-42/elFinder/zipball/33bee2654615db62e9b722efb4fdd2a21844fbb2", "reference": "33bee2654615db62e9b722efb4fdd2a21844fbb2", "shasum": ""}, "require": {"php": ">=5.2"}, "suggest": {"barryvdh/elfinder-flysystem-driver": "VolumeDriver for elFinder to use Flysystem as a root.", "google/apiclient": "VolumeDriver GoogleDrive require `google/apiclient:^2.0.", "kunalvarma05/dropbox-php-sdk": "VolumeDriver `Dropbox`2 require `kunalvarma05/dropbox-php-sdk.", "nao-pon/flysystem-google-drive": "require in GoogleDrive network volume mounting with Flysystem."}, "type": "library", "autoload": {"classmap": ["php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://std42.ru"}, {"name": "Troex Nevelin", "email": "<EMAIL>", "homepage": "http://std42.ru"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://xoops.hypweb.net"}, {"name": "Community contributions", "homepage": "https://github.com/Studio-42/elFinder/contributors"}], "description": "File manager for web", "homepage": "http://elfinder.org", "support": {"issues": "https://github.com/Studio-42/elFinder/issues", "source": "https://github.com/Studio-42/elFinder/tree/2.1.61"}, "funding": [{"url": "https://github.com/nao-pon", "type": "github"}], "time": "2022-03-14T15:07:52+00:00"}, {"name": "swiftmailer/swiftmailer", "version": "v6.3.0", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "8a5d5072dca8f48460fce2f4131fcc495eec654c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/8a5d5072dca8f48460fce2f4131fcc495eec654c", "reference": "8a5d5072dca8f48460fce2f4131fcc495eec654c", "shasum": ""}, "require": {"egulias/email-validator": "^2.0|^3.1", "php": ">=7.0.0", "symfony/polyfill-iconv": "^1.0", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"mockery/mockery": "^1.0", "symfony/phpunit-bridge": "^4.4|^5.4"}, "suggest": {"ext-intl": "Needed to support internationalized email addresses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "https://swiftmailer.symfony.com", "keywords": ["email", "mail", "mailer"], "support": {"issues": "https://github.com/swiftmailer/swiftmailer/issues", "source": "https://github.com/swiftmailer/swiftmailer/tree/v6.3.0"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/swiftmailer/swiftmailer", "type": "tidelift"}], "abandoned": "symfony/mailer", "time": "2021-10-18T15:26:12+00:00"}, {"name": "symfony/contracts", "version": "v1.1.12", "source": {"type": "git", "url": "https://github.com/symfony/contracts.git", "reference": "5236c15b24aeeecee4b9c6ad4b22f6331f2cbdcb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/contracts/zipball/5236c15b24aeeecee4b9c6ad4b22f6331f2cbdcb", "reference": "5236c15b24aeeecee4b9c6ad4b22f6331f2cbdcb", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/cache": "^1.0|^2.0|^3.0", "psr/container": "^1.0"}, "replace": {"symfony/cache-contracts": "self.version", "symfony/event-dispatcher-contracts": "self.version", "symfony/http-client-contracts": "self.version", "symfony/service-contracts": "self.version", "symfony/translation-contracts": "self.version"}, "require-dev": {"symfony/polyfill-intl-idn": "^1.10"}, "suggest": {"psr/event-dispatcher": "When using the EventDispatcher contracts", "symfony/cache-implementation": "", "symfony/event-dispatcher-implementation": "", "symfony/http-client-implementation": "", "symfony/service-implementation": "", "symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.1-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\": ""}, "exclude-from-classmap": ["**/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A set of abstractions extracted out of the Symfony components", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/contracts/tree/v1.1.12"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-03-09T17:53:12+00:00"}, {"name": "symfony/monolog-bundle", "version": "v3.7.1", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bundle.git", "reference": "fde12fc628162787a4e53877abadc30047fd868b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bundle/zipball/fde12fc628162787a4e53877abadc30047fd868b", "reference": "fde12fc628162787a4e53877abadc30047fd868b", "shasum": ""}, "require": {"monolog/monolog": "~1.22 || ~2.0", "php": ">=7.1.3", "symfony/config": "~4.4 || ^5.0 || ^6.0", "symfony/dependency-injection": "^4.4 || ^5.0 || ^6.0", "symfony/http-kernel": "~4.4 || ^5.0 || ^6.0", "symfony/monolog-bridge": "~4.4 || ^5.0 || ^6.0"}, "require-dev": {"symfony/console": "~4.4 || ^5.0 || ^6.0", "symfony/phpunit-bridge": "^5.2 || ^6.0", "symfony/yaml": "~4.4 || ^5.0 || ^6.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MonologBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony MonologBundle", "homepage": "https://symfony.com", "keywords": ["log", "logging"], "support": {"issues": "https://github.com/symfony/monolog-bundle/issues", "source": "https://github.com/symfony/monolog-bundle/tree/v3.7.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-05T10:34:29+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.25.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "30885182c981ab175d4d034db0f6f469898070ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/30885182c981ab175d4d034db0f6f469898070ab", "reference": "30885182c981ab175d4d034db0f6f469898070ab", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.25.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-10-20T20:35:02+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.25.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "f1aed619e28cb077fc83fac8c4c0383578356e40"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/f1aed619e28cb077fc83fac8c4c0383578356e40", "reference": "f1aed619e28cb077fc83fac8c4c0383578356e40", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-iconv": "*"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-iconv/tree/v1.25.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-04T09:04:05+00:00"}, {"name": "symfony/polyfill-intl-icu", "version": "v1.25.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-icu.git", "reference": "c023a439b8551e320cc3c8433b198e408a623af1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-icu/zipball/c023a439b8551e320cc3c8433b198e408a623af1", "reference": "c023a439b8551e320cc3c8433b198e408a623af1", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance and support of other locales than \"en\""}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Icu\\": ""}, "classmap": ["Resources/stubs"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's ICU-related data and classes", "homepage": "https://symfony.com", "keywords": ["compatibility", "icu", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-icu/tree/v1.25.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-10-26T17:16:04+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.25.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "749045c69efb97c70d25d7463abba812e91f3a44"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/749045c69efb97c70d25d7463abba812e91f3a44", "reference": "749045c69efb97c70d25d7463abba812e91f3a44", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.25.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-09-14T14:02:44+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.25.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "8590a5f561694770bdcd3f9b5c69dde6945028e8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/8590a5f561694770bdcd3f9b5c69dde6945028e8", "reference": "8590a5f561694770bdcd3f9b5c69dde6945028e8", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.25.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-02-19T12:13:01+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.25.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "0abb51d2f102e00a4eefcf46ba7fec406d245825"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/0abb51d2f102e00a4eefcf46ba7fec406d245825", "reference": "0abb51d2f102e00a4eefcf46ba7fec406d245825", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.25.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-11-30T18:21:41+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.25.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "9a142215a36a3888e30d0a9eeea9766764e96976"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/9a142215a36a3888e30d0a9eeea9766764e96976", "reference": "9a142215a36a3888e30d0a9eeea9766764e96976", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php72\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php72/tree/v1.25.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-05-27T09:17:38+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.25.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "cc5db0e22b3cb4111010e48785a97f670b350ca5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/cc5db0e22b3cb4111010e48785a97f670b350ca5", "reference": "cc5db0e22b3cb4111010e48785a97f670b350ca5", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.25.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-06-05T21:20:04+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.25.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "4407588e0d3f1f52efb65fbe92babe41f37fe50c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/4407588e0d3f1f52efb65fbe92babe41f37fe50c", "reference": "4407588e0d3f1f52efb65fbe92babe41f37fe50c", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.25.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-03-04T08:16:47+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.25.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "5de4ba2d41b15f9bd0e19b2ab9674135813ec98f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/5de4ba2d41b15f9bd0e19b2ab9674135813ec98f", "reference": "5de4ba2d41b15f9bd0e19b2ab9674135813ec98f", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.25.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-09-13T13:58:11+00:00"}, {"name": "symfony/swiftmailer-bundle", "version": "v3.5.4", "source": {"type": "git", "url": "https://github.com/symfony/swiftmailer-bundle.git", "reference": "9daab339f226ac958192bf89836cb3378cc0e652"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/swiftmailer-bundle/zipball/9daab339f226ac958192bf89836cb3378cc0e652", "reference": "9daab339f226ac958192bf89836cb3378cc0e652", "shasum": ""}, "require": {"php": ">=7.1", "swiftmailer/swiftmailer": "^6.1.3", "symfony/config": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/http-kernel": "^4.4|^5.0"}, "conflict": {"twig/twig": "<1.41|>=2.0,<2.10"}, "require-dev": {"symfony/console": "^4.4|^5.0", "symfony/framework-bundle": "^4.4|^5.0", "symfony/phpunit-bridge": "^4.4|^5.0", "symfony/yaml": "^4.4|^5.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\SwiftmailerBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "Symfony SwiftmailerBundle", "homepage": "http://symfony.com", "support": {"issues": "https://github.com/symfony/swiftmailer-bundle/issues", "source": "https://github.com/symfony/swiftmailer-bundle/tree/v3.5.4"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "abandoned": "symfony/mailer", "time": "2022-02-06T08:03:40+00:00"}, {"name": "symfony/symfony", "version": "v4.4.40", "source": {"type": "git", "url": "https://github.com/symfony/symfony.git", "reference": "406847f8c944b577c116531cb342d2875324a2c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/symfony/zipball/406847f8c944b577c116531cb342d2875324a2c1", "reference": "406847f8c944b577c116531cb342d2875324a2c1", "shasum": ""}, "require": {"doctrine/event-manager": "~1.0", "doctrine/persistence": "^1.3|^2", "ext-xml": "*", "friendsofphp/proxy-manager-lts": "^1.0.2", "php": ">=7.1.3", "psr/cache": "^1.0|^2.0", "psr/container": "^1.0", "psr/link": "^1.0", "psr/log": "^1|^2", "symfony/contracts": "^1.1.8", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-icu": "~1.0", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php72": "~1.5", "symfony/polyfill-php73": "^1.11", "symfony/polyfill-php80": "^1.16", "symfony/polyfill-php81": "^1.22", "twig/twig": "^1.43|^2.13|^3.0.4"}, "conflict": {"doctrine/dbal": "<2.7", "egulias/email-validator": "~3.0.0", "masterminds/html5": "<2.6", "monolog/monolog": ">=2", "ocramius/proxy-manager": "<2.1", "phpdocumentor/reflection-docblock": "<3.0|>=3.2.0,<3.2.2", "phpdocumentor/type-resolver": "<0.3.0|1.3.*", "phpunit/phpunit": "<5.4.3"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/cache-implementation": "1.0|2.0", "psr/container-implementation": "1.0", "psr/event-dispatcher-implementation": "1.0", "psr/http-client-implementation": "1.0", "psr/link-implementation": "1.0", "psr/log-implementation": "1.0|2.0", "psr/simple-cache-implementation": "1.0|2.0", "symfony/cache-implementation": "1.0|2.0", "symfony/event-dispatcher-implementation": "1.1", "symfony/http-client-implementation": "1.1|2.0", "symfony/service-implementation": "1.0|2.0", "symfony/translation-implementation": "1.0|2.0"}, "replace": {"symfony/amazon-mailer": "self.version", "symfony/asset": "self.version", "symfony/browser-kit": "self.version", "symfony/cache": "self.version", "symfony/config": "self.version", "symfony/console": "self.version", "symfony/css-selector": "self.version", "symfony/debug": "self.version", "symfony/debug-bundle": "self.version", "symfony/dependency-injection": "self.version", "symfony/doctrine-bridge": "self.version", "symfony/dom-crawler": "self.version", "symfony/dotenv": "self.version", "symfony/error-handler": "self.version", "symfony/event-dispatcher": "self.version", "symfony/expression-language": "self.version", "symfony/filesystem": "self.version", "symfony/finder": "self.version", "symfony/form": "self.version", "symfony/framework-bundle": "self.version", "symfony/google-mailer": "self.version", "symfony/http-client": "self.version", "symfony/http-foundation": "self.version", "symfony/http-kernel": "self.version", "symfony/inflector": "self.version", "symfony/intl": "self.version", "symfony/ldap": "self.version", "symfony/lock": "self.version", "symfony/mailchimp-mailer": "self.version", "symfony/mailer": "self.version", "symfony/mailgun-mailer": "self.version", "symfony/messenger": "self.version", "symfony/mime": "self.version", "symfony/monolog-bridge": "self.version", "symfony/options-resolver": "self.version", "symfony/postmark-mailer": "self.version", "symfony/process": "self.version", "symfony/property-access": "self.version", "symfony/property-info": "self.version", "symfony/proxy-manager-bridge": "self.version", "symfony/routing": "self.version", "symfony/security": "self.version", "symfony/security-bundle": "self.version", "symfony/security-core": "self.version", "symfony/security-csrf": "self.version", "symfony/security-guard": "self.version", "symfony/security-http": "self.version", "symfony/sendgrid-mailer": "self.version", "symfony/serializer": "self.version", "symfony/stopwatch": "self.version", "symfony/templating": "self.version", "symfony/translation": "self.version", "symfony/twig-bridge": "self.version", "symfony/twig-bundle": "self.version", "symfony/validator": "self.version", "symfony/var-dumper": "self.version", "symfony/var-exporter": "self.version", "symfony/web-link": "self.version", "symfony/web-profiler-bundle": "self.version", "symfony/web-server-bundle": "self.version", "symfony/workflow": "self.version", "symfony/yaml": "self.version"}, "require-dev": {"cache/integration-tests": "dev-master", "composer/package-versions-deprecated": "^1.8", "doctrine/annotations": "^1.10.4", "doctrine/cache": "^1.6|^2.0", "doctrine/collections": "~1.0", "doctrine/data-fixtures": "^1.1", "doctrine/dbal": "^2.7|^3.0", "doctrine/orm": "^2.6.3", "egulias/email-validator": "^2.1.10|^3.1", "guzzlehttp/promises": "^1.4", "masterminds/html5": "^2.6", "monolog/monolog": "^1.25.1", "nyholm/psr7": "^1.0", "paragonie/sodium_compat": "^1.8", "php-http/httplug": "^1.0|^2.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "predis/predis": "~1.1", "psr/http-client": "^1.0", "psr/simple-cache": "^1.0|^2.0", "symfony/phpunit-bridge": "^5.2", "symfony/security-acl": "~2.8|~3.0", "twig/cssinliner-extra": "^2.12|^3", "twig/inky-extra": "^2.12|^3", "twig/markdown-extra": "^2.12|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Bundle\\": "src/Symfony/Bundle/", "Symfony\\Component\\": "src/Symfony/Component/", "Symfony\\Bridge\\Twig\\": "src/Symfony/Bridge/Twig/", "Symfony\\Bridge\\Monolog\\": "src/Symfony/Bridge/Monolog/", "Symfony\\Bridge\\Doctrine\\": "src/Symfony/Bridge/Doctrine/", "Symfony\\Bridge\\ProxyManager\\": "src/Symfony/Bridge/ProxyManager/"}, "classmap": ["src/Symfony/Component/Intl/Resources/stubs"], "exclude-from-classmap": ["**/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "The Symfony PHP framework", "homepage": "https://symfony.com", "keywords": ["framework"], "support": {"issues": "https://github.com/symfony/symfony/issues", "source": "https://github.com/symfony/symfony/tree/v4.4.40"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-04-02T05:56:07+00:00"}, {"name": "tedivm/jshrink", "version": "v1.3.3", "source": {"type": "git", "url": "https://github.com/tedious/JShrink.git", "reference": "566e0c731ba4e372be2de429ef7d54f4faf4477a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tedious/JShrink/zipball/566e0c731ba4e372be2de429ef7d54f4faf4477a", "reference": "566e0c731ba4e372be2de429ef7d54f4faf4477a", "shasum": ""}, "require": {"php": "^5.6|^7.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.8", "php-coveralls/php-coveralls": "^1.1.0", "phpunit/phpunit": "^6"}, "type": "library", "autoload": {"psr-0": {"JShrink": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Javascript Minifier built in PHP", "homepage": "http://github.com/tedious/JShrink", "keywords": ["javascript", "minifier"], "support": {"issues": "https://github.com/tedious/JShrink/issues", "source": "https://github.com/tedious/JShrink/tree/master"}, "time": "2019-06-28T18:11:46+00:00"}, {"name": "twig/extensions", "version": "v1.5.4", "source": {"type": "git", "url": "https://github.com/twigphp/Twig-extensions.git", "reference": "57873c8b0c1be51caa47df2cdb824490beb16202"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig-extensions/zipball/57873c8b0c1be51caa47df2cdb824490beb16202", "reference": "57873c8b0c1be51caa47df2cdb824490beb16202", "shasum": ""}, "require": {"twig/twig": "^1.27|^2.0"}, "require-dev": {"symfony/phpunit-bridge": "^3.4", "symfony/translation": "^2.7|^3.4"}, "suggest": {"symfony/translation": "Allow the time_diff output to be translated"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"psr-0": {"Twig_Extensions_": "lib/"}, "psr-4": {"Twig\\Extensions\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common additional features for Twig that do not directly belong in core", "keywords": ["i18n", "text"], "support": {"issues": "https://github.com/twigphp/Twig-extensions/issues", "source": "https://github.com/twigphp/Twig-extensions/tree/master"}, "abandoned": true, "time": "2018-12-05T18:34:18+00:00"}, {"name": "twig/twig", "version": "v2.14.13", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "66856cd0459df3dc97d32077a98454dc2a0ee75a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/66856cd0459df3dc97d32077a98454dc2a0ee75a", "reference": "66856cd0459df3dc97d32077a98454dc2a0ee75a", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3", "symfony/polyfill-php72": "^1.8"}, "require-dev": {"psr/container": "^1.0", "symfony/phpunit-bridge": "^4.4.9|^5.0.9|^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.14-dev"}}, "autoload": {"psr-0": {"Twig_": "lib/"}, "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v2.14.13"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2022-04-06T06:45:17+00:00"}, {"name": "violet/streaming-json-encoder", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/violet-php/streaming-json-encoder.git", "reference": "5aa8bc3494c4a24116b46c0d461c409eeb8bf313"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/violet-php/streaming-json-encoder/zipball/5aa8bc3494c4a24116b46c0d461c409eeb8bf313", "reference": "5aa8bc3494c4a24116b46c0d461c409eeb8bf313", "shasum": ""}, "require": {"php": ">=5.6.0", "psr/http-message": "^1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.3", "phpunit/phpunit": "^5.7 || ^6.2", "squizlabs/php_codesniffer": "^3.0"}, "type": "library", "autoload": {"psr-4": {"Violet\\StreamingJsonEncoder\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://riimu.net"}], "description": "Library for iteratively encoding large JSON documents piece by piece", "homepage": "http://violet.riimu.net", "keywords": ["encoder", "json", "psr-7", "streaming"], "support": {"issues": "https://github.com/violet-php/streaming-json-encoder/issues", "source": "https://github.com/violet-php/streaming-json-encoder/tree/master"}, "time": "2017-07-09T14:42:39+00:00"}, {"name": "wikimedia/less.php", "version": "v2.0.0", "source": {"type": "git", "url": "https://github.com/wikimedia/less.php.git", "reference": "c1affb4d4472c9e100fc80bf456b4334862ace3c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wikimedia/less.php/zipball/c1affb4d4472c9e100fc80bf456b4334862ace3c", "reference": "c1affb4d4472c9e100fc80bf456b4334862ace3c", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "7.5.14"}, "bin": ["bin/lessc"], "type": "library", "autoload": {"psr-0": {"Less": "lib/"}, "classmap": ["lessc.inc.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "homepage": "https://github.com/oyejorge"}, {"name": "<PERSON>", "homepage": "https://github.com/agar"}, {"name": "<PERSON>", "homepage": "https://github.com/Mordred"}], "description": "PHP port of the Javascript version of LESS http://lesscss.org (Originally maintained by <PERSON>)", "keywords": ["css", "less", "less.js", "lesscss", "php", "stylesheet"], "support": {"issues": "https://github.com/wikimedia/less.php/issues", "source": "https://github.com/wikimedia/less.php/tree/v2.0.0"}, "time": "2020-02-04T22:36:29+00:00"}, {"name": "<PERSON><PERSON><PERSON>/jsonp-callback-validator", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/willdurand/JsonpCallbackValidator.git", "reference": "1a7d388bb521959e612ef50c5c7b1691b097e909"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/willdurand/JsonpCallbackValidator/zipball/1a7d388bb521959e612ef50c5c7b1691b097e909", "reference": "1a7d388bb521959e612ef50c5c7b1691b097e909", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~3.7"}, "type": "library", "autoload": {"psr-0": {"JsonpCallbackValidator": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.willdurand.fr"}], "description": "JSONP callback validator.", "support": {"issues": "https://github.com/willdurand/JsonpCallbackValidator/issues", "source": "https://github.com/willdurand/JsonpCallbackValidator/tree/master"}, "time": "2014-01-20T22:35:06+00:00"}, {"name": "will<PERSON><PERSON>/negotiation", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/willdurand/Negotiation.git", "reference": "2a59f2376557303e3fa91465ab691abb82945edf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/willdurand/Negotiation/zipball/2a59f2376557303e3fa91465ab691abb82945edf", "reference": "2a59f2376557303e3fa91465ab691abb82945edf", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"psr-4": {"Negotiation\\": "src/Negotiation"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Content Negotiation tools for PHP provided as a standalone library.", "homepage": "http://williamdurand.fr/Negotiation/", "keywords": ["accept", "content", "format", "header", "negotiation"], "support": {"issues": "https://github.com/willdurand/Negotiation/issues", "source": "https://github.com/willdurand/Negotiation/tree/1.5.0"}, "time": "2015-10-01T07:42:40+00:00"}, {"name": "wizaplace/semantic-versioning", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/wizaplace/semantic-versioning.git", "reference": "860f06502180042aad647c4e7e1c6bf6829542c8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wizaplace/semantic-versioning/zipball/860f06502180042aad647c4e7e1c6bf6829542c8", "reference": "860f06502180042aad647c4e7e1c6bf6829542c8", "shasum": ""}, "require": {"php": "^7.1.3"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.14", "phpstan/phpstan": "^0.11.1", "phpunit/phpunit": "^7.5"}, "type": "library", "autoload": {"psr-4": {"Wizaplace\\SemanticVersioning\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Domain objects to manage semantic versioning", "support": {"issues": "https://github.com/wizaplace/semantic-versioning/issues", "source": "https://github.com/wizaplace/semantic-versioning/tree/master"}, "time": "2019-01-22T12:48:52+00:00"}], "packages-dev": [{"name": "atoum/atoum", "version": "3.4.2", "source": {"type": "git", "url": "https://github.com/atoum/atoum.git", "reference": "e90606b89e62c5c18c5d02596078edf55f35b3c3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/atoum/atoum/zipball/e90606b89e62c5c18c5d02596078edf55f35b3c3", "reference": "e90606b89e62c5c18c5d02596078edf55f35b3c3", "shasum": ""}, "require": {"ext-hash": "*", "ext-json": "*", "ext-tokenizer": "*", "ext-xml": "*", "php": "^5.6.0 || ^7.0.0 <7.5.0"}, "replace": {"mageekguy/atoum": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2"}, "suggest": {"atoum/stubs": "Provides IDE support (like autocompletion) for atoum", "ext-mbstring": "Provides support for UTF-8 strings", "ext-xdebug": "Provides code coverage report (>= 2.3)"}, "bin": ["bin/atoum"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"classmap": ["classes/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://blog.mageekbox.net"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Simple modern and intuitive unit testing framework for PHP 5.3+", "homepage": "http://www.atoum.org", "keywords": ["TDD", "atoum", "test", "unit testing"], "support": {"issues": "https://github.com/atoum/atoum/issues", "source": "https://github.com/atoum/atoum/tree/3.4.2"}, "time": "2020-03-04T10:29:09+00:00"}, {"name": "atoum/ruler-extension", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/atoum/ruler-extension.git", "reference": "32fe62675bf106c59ebe4b7e5ee3daa93c1f8b1b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/atoum/ruler-extension/zipball/32fe62675bf106c59ebe4b7e5ee3daa93c1f8b1b", "reference": "32fe62675bf106c59ebe4b7e5ee3daa93c1f8b1b", "shasum": ""}, "require": {"atoum/atoum": "^2.9 | ^3.0", "hoa/ruler": ">=**********,<2.0 | >=**********,<3.0"}, "conflict": {"hoa/compiler": "<**********", "hoa/core": "<**********", "hoa/file": "<**********", "hoa/stream": "<**********", "hoa/visitor": "<**********"}, "type": "library", "autoload": {"files": ["configuration.php"], "psr-4": {"mageekguy\\atoum\\ruler\\": "classes"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON><PERSON>@gmail.com"}], "description": "The atoum ruler-extension allows you to filter your tests using Hoa\\Ruler", "keywords": ["TDD", "atoum", "atoum-extension", "ruler", "test", "unit testing"], "support": {"issues": "https://github.com/atoum/ruler-extension/issues", "source": "https://github.com/atoum/ruler-extension/tree/master"}, "time": "2017-02-24T13:24:23+00:00"}, {"name": "atoum/stubs", "version": "2.6.0", "source": {"type": "git", "url": "https://github.com/atoum/stubs.git", "reference": "df8b73b0358de7283ecba91d8f4a9683f583993d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/atoum/stubs/zipball/df8b73b0358de7283ecba91d8f4a9683f583993d", "reference": "df8b73b0358de7283ecba91d8f4a9683f583993d", "shasum": ""}, "suggest": {"atoum/atoum": "Include atoum in your projet dependencies"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Stubs for atoum, the simple modern and intuitive unit testing framework for PHP 5.3+", "homepage": "http://www.atoum.org", "keywords": ["TDD", "atoum", "test", "unit testing"], "support": {"issues": "https://github.com/atoum/stubs/issues", "source": "https://github.com/atoum/stubs/tree/master"}, "time": "2018-01-29T22:41:37+00:00"}, {"name": "brainmaestro/composer-git-hooks", "version": "v2.8.5", "source": {"type": "git", "url": "https://github.com/BrainMaestro/composer-git-hooks.git", "reference": "ffed8803690ac12214082120eee3441b00aa390e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/BrainMaestro/composer-git-hooks/zipball/ffed8803690ac12214082120eee3441b00aa390e", "reference": "ffed8803690ac12214082120eee3441b00aa390e", "shasum": ""}, "require": {"php": "^5.6 || >=7.0", "symfony/console": "^3.2 || ^4.0 || ^5.0"}, "require-dev": {"ext-json": "*", "friendsofphp/php-cs-fixer": "^2.9", "phpunit/phpunit": "^5.7 || ^7.0"}, "bin": ["cghooks"], "type": "library", "extra": {"hooks": {"pre-commit": "composer check-style", "pre-push": ["composer test", "appver=$(grep -o -E '\\d.\\d.\\d' cghooks)", "tag=$(git describe --tags --abbrev=0)", "if [ \"$tag\" != \"v$appver\" ]; then", "echo \"The most recent tag $tag does not match the application version $appver\\n\"", "tag=${tag#v}", "sed -i -E \"s/$appver/$tag/\" cghooks", "exit 1", "fi"]}}, "autoload": {"files": ["src/helpers.php"], "psr-4": {"BrainMaestro\\GitHooks\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Easily manage git hooks in your composer config", "keywords": ["HOOK", "composer", "git"], "support": {"issues": "https://github.com/BrainMaestro/composer-git-hooks/issues", "source": "https://github.com/BrainMaestro/composer-git-hooks/tree/v2.8.5"}, "time": "2021-02-08T15:59:11+00:00"}, {"name": "composer/xdebug-handler", "version": "1.4.5", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "f28d44c286812c714741478d968104c5e604a1d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/f28d44c286812c714741478d968104c5e604a1d4", "reference": "f28d44c286812c714741478d968104c5e604a1d4", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0", "psr/log": "^1.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || 6.5 - 8"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/1.4.5"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2020-11-13T08:04:11+00:00"}, {"name": "fzaninotto/faker", "version": "v1.9.1", "source": {"type": "git", "url": "https://github.com/fzaninotto/Faker.git", "reference": "fc10d778e4b84d5bd315dad194661e091d307c6f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fzaninotto/Faker/zipball/fc10d778e4b84d5bd315dad194661e091d307c6f", "reference": "fc10d778e4b84d5bd315dad194661e091d307c6f", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"ext-intl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7", "squizlabs/php_codesniffer": "^2.9.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "support": {"issues": "https://github.com/fzaninotto/Faker/issues", "source": "https://github.com/fzaninotto/Faker/tree/v1.9.1"}, "abandoned": true, "time": "2019-12-12T13:22:17+00:00"}, {"name": "hamcrest/hamcrest-php", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/hamcrest/hamcrest-php.git", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hamcrest/hamcrest-php/zipball/8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "shasum": ""}, "require": {"php": "^5.3|^7.0|^8.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["hamcrest"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "support": {"issues": "https://github.com/hamcrest/hamcrest-php/issues", "source": "https://github.com/hamcrest/hamcrest-php/tree/v2.0.1"}, "time": "2020-07-09T08:09:16+00:00"}, {"name": "jean85/pretty-package-versions", "version": "1.5.1", "source": {"type": "git", "url": "https://github.com/Jean85/pretty-package-versions.git", "reference": "a917488320c20057da87f67d0d40543dd9427f7a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Jean85/pretty-package-versions/zipball/a917488320c20057da87f67d0d40543dd9427f7a", "reference": "a917488320c20057da87f67d0d40543dd9427f7a", "shasum": ""}, "require": {"composer/package-versions-deprecated": "^1.8.0", "php": "^7.0|^8.0"}, "require-dev": {"phpunit/phpunit": "^6.0|^8.5|^9.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Jean85\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A wrapper for ocramius/package-versions to get pretty versions strings", "keywords": ["composer", "package", "release", "versions"], "support": {"issues": "https://github.com/Jean85/pretty-package-versions/issues", "source": "https://github.com/Jean85/pretty-package-versions/tree/1.5.1"}, "time": "2020-09-14T08:43:34+00:00"}, {"name": "league/flysystem-memory", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem-memory.git", "reference": "d0e87477c32e29f999b4de05e64c1adcddb51757"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem-memory/zipball/d0e87477c32e29f999b4de05e64c1adcddb51757", "reference": "d0e87477c32e29f999b4de05e64c1adcddb51757", "shasum": ""}, "require": {"league/flysystem": "~1.0"}, "require-dev": {"phpunit/phpunit": "^5.7.10"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"League\\Flysystem\\Memory\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "An in-memory adapter for Flysystem.", "homepage": "https://github.com/thephpleague/flysystem-memory", "keywords": ["Flysystem", "adapter", "memory"], "support": {"issues": "https://github.com/thephpleague/flysystem-memory/issues", "source": "https://github.com/thephpleague/flysystem-memory/tree/1.0.2"}, "time": "2019-05-30T21:34:13+00:00"}, {"name": "m6web/redis-mock", "version": "v3.3.2", "source": {"type": "git", "url": "https://github.com/BedrockStreaming/RedisMock.git", "reference": "3c73d01577227b641d8615b07d9730f354155020"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/BedrockStreaming/RedisMock/zipball/3c73d01577227b641d8615b07d9730f354155020", "reference": "3c73d01577227b641d8615b07d9730f354155020", "shasum": ""}, "require": {"php": ">=7.1.0"}, "require-dev": {"atoum/atoum": "master-dev", "predis/predis": "~1.1"}, "type": "library", "autoload": {"psr-0": {"M6Web\\Component\\RedisMock": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "M6Web", "email": "<EMAIL>", "homepage": "http://tech.m6web.fr/"}], "description": "Library providing a PHP mock for Redis", "keywords": ["mock", "redis"], "support": {"issues": "https://github.com/BedrockStreaming/RedisMock/issues", "source": "https://github.com/BedrockStreaming/RedisMock/tree/v4.0.0"}, "time": "2018-06-04T07:39:56+00:00"}, {"name": "mockery/mockery", "version": "1.3.x-dev", "source": {"type": "git", "url": "https://github.com/mockery/mockery.git", "reference": "937a86b3ba04ea2195c96d8e31d5c2d23a7e7fc6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mockery/mockery/zipball/937a86b3ba04ea2195c96d8e31d5c2d23a7e7fc6", "reference": "937a86b3ba04ea2195c96d8e31d5c2d23a7e7fc6", "shasum": ""}, "require": {"hamcrest/hamcrest-php": "^2.0.1", "lib-pcre": ">=7.0", "php": ">=5.6.0"}, "require-dev": {"phpunit/phpunit": "^5.7.10|^6.5|^7.5|^8.5|^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-0": {"Mockery": "library/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://blog.astrumfutura.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://davedevelopment.co.uk"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/1.3"}, "time": "2020-12-17T10:56:03+00:00"}, {"name": "myclabs/deep-copy", "version": "1.10.2", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "776f831124e9c62e1a2c601ecc52e776d8bb7220"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/776f831124e9c62e1a2c601ecc52e776d8bb7220", "reference": "776f831124e9c62e1a2c601ecc52e776d8bb7220", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/collections": "^1.0", "doctrine/common": "^2.6", "phpunit/phpunit": "^7.1"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.10.2"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2020-11-13T09:40:50+00:00"}, {"name": "nategood/httpful", "version": "0.2.20", "source": {"type": "git", "url": "https://github.com/nategood/httpful.git", "reference": "c1cd4d46a4b281229032cf39d4dd852f9887c0f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nategood/httpful/zipball/c1cd4d46a4b281229032cf39d4dd852f9887c0f6", "reference": "c1cd4d46a4b281229032cf39d4dd852f9887c0f6", "shasum": ""}, "require": {"ext-curl": "*", "php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "*"}, "type": "library", "autoload": {"psr-0": {"Httpful": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nategood.com"}], "description": "A Readable, Chainable, REST friendly, PHP HTTP Client", "homepage": "http://github.com/nategood/httpful", "keywords": ["api", "curl", "http", "requests", "rest", "restful"], "support": {"issues": "https://github.com/nategood/httpful/issues", "source": "https://github.com/nategood/httpful/tree/v0.2.20"}, "time": "2015-10-26T16:11:30+00:00"}, {"name": "nette/bootstrap", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/nette/bootstrap.git", "reference": "67830a65b42abfb906f8e371512d336ebfb5da93"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/bootstrap/zipball/67830a65b42abfb906f8e371512d336ebfb5da93", "reference": "67830a65b42abfb906f8e371512d336ebfb5da93", "shasum": ""}, "require": {"nette/di": "^3.0", "nette/utils": "^3.0", "php": ">=7.1"}, "conflict": {"tracy/tracy": "<2.6"}, "require-dev": {"latte/latte": "^2.2", "nette/application": "^3.0", "nette/caching": "^3.0", "nette/database": "^3.0", "nette/forms": "^3.0", "nette/http": "^3.0", "nette/mail": "^3.0", "nette/robot-loader": "^3.0", "nette/safe-stream": "^2.2", "nette/security": "^3.0", "nette/tester": "^2.0", "phpstan/phpstan-nette": "^0.12", "tracy/tracy": "^2.6"}, "suggest": {"nette/robot-loader": "to use Configurator::createRobotLoader()", "tracy/tracy": "to use Configurator::enableTracy()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🅱 Nette Bootstrap: the simple way to configure and bootstrap your Nette application.", "homepage": "https://nette.org", "keywords": ["bootstrapping", "configurator", "nette"], "support": {"issues": "https://github.com/nette/bootstrap/issues", "source": "https://github.com/nette/bootstrap/tree/master"}, "time": "2020-05-26T08:46:23+00:00"}, {"name": "nette/di", "version": "v3.0.6", "source": {"type": "git", "url": "https://github.com/nette/di.git", "reference": "e639ccfbc0230e022ca08bf59c6b07df7caf2007"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/di/zipball/e639ccfbc0230e022ca08bf59c6b07df7caf2007", "reference": "e639ccfbc0230e022ca08bf59c6b07df7caf2007", "shasum": ""}, "require": {"ext-tokenizer": "*", "nette/neon": "^3.0", "nette/php-generator": "^3.3.3", "nette/robot-loader": "^3.2", "nette/schema": "^1.0", "nette/utils": "^3.1.4", "php": ">=7.1 <8.1"}, "conflict": {"nette/bootstrap": "<3.0"}, "require-dev": {"nette/tester": "^2.2", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "💎 Nette Dependency Injection Container: Flexible, compiled and full-featured DIC with perfectly usable autowiring and support for all new PHP 7.1 features.", "homepage": "https://nette.org", "keywords": ["compiled", "di", "dic", "factory", "ioc", "nette", "static"], "support": {"issues": "https://github.com/nette/di/issues", "source": "https://github.com/nette/di/tree/v3.0.6"}, "time": "2020-11-25T22:42:55+00:00"}, {"name": "nette/finder", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/nette/finder.git", "reference": "4ad2c298eb8c687dd0e74ae84206a4186eeaed50"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/finder/zipball/4ad2c298eb8c687dd0e74ae84206a4186eeaed50", "reference": "4ad2c298eb8c687dd0e74ae84206a4186eeaed50", "shasum": ""}, "require": {"nette/utils": "^2.4 || ^3.0", "php": ">=7.1"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/tester": "^2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0", "GPL-3.0"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🔍 Nette Finder: find files and directories with an intuitive API.", "homepage": "https://nette.org", "keywords": ["filesystem", "glob", "iterator", "nette"], "support": {"issues": "https://github.com/nette/finder/issues", "source": "https://github.com/nette/finder/tree/v2.5.2"}, "time": "2020-01-03T20:35:40+00:00"}, {"name": "nette/neon", "version": "v3.2.1", "source": {"type": "git", "url": "https://github.com/nette/neon.git", "reference": "a5b3a60833d2ef55283a82d0c30b45d136b29e75"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/neon/zipball/a5b3a60833d2ef55283a82d0c30b45d136b29e75", "reference": "a5b3a60833d2ef55283a82d0c30b45d136b29e75", "shasum": ""}, "require": {"ext-iconv": "*", "ext-json": "*", "php": ">=7.1"}, "require-dev": {"nette/tester": "^2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🍸 Nette NEON: encodes and decodes NEON file format.", "homepage": "https://ne-on.org", "keywords": ["export", "import", "neon", "nette", "yaml"], "support": {"issues": "https://github.com/nette/neon/issues", "source": "https://github.com/nette/neon/tree/master"}, "time": "2020-07-31T12:28:05+00:00"}, {"name": "nette/php-generator", "version": "v3.5.1", "source": {"type": "git", "url": "https://github.com/nette/php-generator.git", "reference": "fe54415cd22d01bee1307a608058bf131978610a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/php-generator/zipball/fe54415cd22d01bee1307a608058bf131978610a", "reference": "fe54415cd22d01bee1307a608058bf131978610a", "shasum": ""}, "require": {"nette/utils": "^3.1.2", "php": ">=7.1"}, "require-dev": {"nette/tester": "^2.0", "nikic/php-parser": "^4.4", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "suggest": {"nikic/php-parser": "to use ClassType::withBodiesFrom() & GlobalFunction::withBodyFrom()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.5-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🐘 Nette PHP Generator: generates neat PHP code for you. Supports new PHP 7.4 features.", "homepage": "https://nette.org", "keywords": ["code", "nette", "php", "scaffolding"], "support": {"issues": "https://github.com/nette/php-generator/issues", "source": "https://github.com/nette/php-generator/tree/v3.5.1"}, "time": "2020-11-04T11:26:26+00:00"}, {"name": "nette/robot-loader", "version": "v3.3.1", "source": {"type": "git", "url": "https://github.com/nette/robot-loader.git", "reference": "15c1ecd0e6e69e8d908dfc4cca7b14f3b850a96b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/robot-loader/zipball/15c1ecd0e6e69e8d908dfc4cca7b14f3b850a96b", "reference": "15c1ecd0e6e69e8d908dfc4cca7b14f3b850a96b", "shasum": ""}, "require": {"ext-tokenizer": "*", "nette/finder": "^2.5 || ^3.0", "nette/utils": "^3.0", "php": ">=7.1"}, "require-dev": {"nette/tester": "^2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🍀 Nette RobotLoader: high performance and comfortable autoloader that will search and autoload classes within your application.", "homepage": "https://nette.org", "keywords": ["autoload", "class", "interface", "nette", "trait"], "support": {"issues": "https://github.com/nette/robot-loader/issues", "source": "https://github.com/nette/robot-loader/tree/v3.3.1"}, "time": "2020-09-15T15:14:17+00:00"}, {"name": "nette/schema", "version": "v1.0.3", "source": {"type": "git", "url": "https://github.com/nette/schema.git", "reference": "34baf9eca75eccdad3d04306c5d6bec0f6b252ad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/schema/zipball/34baf9eca75eccdad3d04306c5d6bec0f6b252ad", "reference": "34baf9eca75eccdad3d04306c5d6bec0f6b252ad", "shasum": ""}, "require": {"nette/utils": "^3.1.4", "php": ">=7.1 <8.1"}, "require-dev": {"nette/tester": "^2.2", "phpstan/phpstan-nette": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "📐 Nette Schema: validating data structures against a given Schema.", "homepage": "https://nette.org", "keywords": ["config", "nette"], "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.0.3"}, "time": "2020-11-25T21:54:59+00:00"}, {"name": "nette/utils", "version": "v3.2.0", "source": {"type": "git", "url": "https://github.com/nette/utils.git", "reference": "d0427c1811462dbb6c503143eabe5478b26685f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/d0427c1811462dbb6c503143eabe5478b26685f7", "reference": "d0427c1811462dbb6c503143eabe5478b26685f7", "shasum": ""}, "require": {"php": ">=7.2 <8.1"}, "conflict": {"nette/di": "<3.0.6"}, "require-dev": {"nette/tester": "~2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize(), to<PERSON>cii(), chr() and reverse()", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()", "ext-xml": "to use Strings::length() etc. when mbstring is not available"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🛠  Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.2.0"}, "time": "2020-11-25T23:47:50+00:00"}, {"name": "nikic/php-parser", "version": "v4.13.2", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "210577fe3cf7badcc5814d99455df46564f3c077"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/210577fe3cf7badcc5814d99455df46564f3c077", "reference": "210577fe3cf7badcc5814d99455df46564f3c077", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.0"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^6.5 || ^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v4.13.2"}, "time": "2021-11-30T19:35:32+00:00"}, {"name": "phar-io/manifest", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "7761fcacf03b4d4f16e7ccb606d4879ca431fcf4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/7761fcacf03b4d4f16e7ccb606d4879ca431fcf4", "reference": "7761fcacf03b4d4f16e7ccb606d4879ca431fcf4", "shasum": ""}, "require": {"ext-dom": "*", "ext-phar": "*", "phar-io/version": "^2.0", "php": "^5.6 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/master"}, "time": "2018-07-08T19:23:20+00:00"}, {"name": "phar-io/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "45a2ec53a73c70ce41d55cedef9063630abaf1b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/45a2ec53a73c70ce41d55cedef9063630abaf1b6", "reference": "45a2ec53a73c70ce41d55cedef9063630abaf1b6", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/master"}, "time": "2018-07-08T19:19:57+00:00"}, {"name": "phing/phing", "version": "2.16.3", "source": {"type": "git", "url": "https://github.com/phingofficial/phing.git", "reference": "b34c2bf9cd6abd39b4287dee31e68673784c8567"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phingofficial/phing/zipball/b34c2bf9cd6abd39b4287dee31e68673784c8567", "reference": "b34c2bf9cd6abd39b4287dee31e68673784c8567", "shasum": ""}, "require": {"php": ">=5.2.0"}, "require-dev": {"ext-pdo_sqlite": "*", "mikey179/vfsstream": "^1.6", "pdepend/pdepend": "2.x", "pear/archive_tar": "1.4.x", "pear/http_request2": "dev-trunk", "pear/net_growl": "dev-trunk", "pear/pear-core-minimal": "1.10.1", "pear/versioncontrol_git": "@dev", "pear/versioncontrol_svn": "~0.5", "phpdocumentor/phpdocumentor": "2.x", "phploc/phploc": "~2.0.6", "phpmd/phpmd": "~2.2", "phpunit/phpunit": ">=3.7", "sebastian/git": "~1.0", "sebastian/phpcpd": "2.x", "siad007/versioncontrol_hg": "^1.0", "simpletest/simpletest": "^1.1", "squizlabs/php_codesniffer": "~2.2", "symfony/yaml": "^2.8 || ^3.1 || ^4.0"}, "suggest": {"pdepend/pdepend": "PHP version of JDepend", "pear/archive_tar": "Tar file management class", "pear/versioncontrol_git": "A library that provides OO interface to handle Git repository", "pear/versioncontrol_svn": "A simple OO-style interface for Subversion, the free/open-source version control system", "phpdocumentor/phpdocumentor": "Documentation Generator for PHP", "phploc/phploc": "A tool for quickly measuring the size of a PHP project", "phpmd/phpmd": "PHP version of PMD tool", "phpunit/php-code-coverage": "Library that provides collection, processing, and rendering functionality for PHP code coverage information", "phpunit/phpunit": "The PHP Unit Testing Framework", "sebastian/phpcpd": "Copy/Paste Detector (CPD) for PHP code", "siad007/versioncontrol_hg": "A library for interfacing with Mercurial repositories.", "tedivm/jshrink": "Javascript Minifier built in PHP"}, "bin": ["bin/phing"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.16.x-dev"}}, "autoload": {"classmap": ["classes/phing/"]}, "notification-url": "https://packagist.org/downloads/", "include-path": ["classes"], "license": ["LGPL-3.0-only"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Phing Community", "homepage": "https://www.phing.info/trac/wiki/Development/Contributors"}], "description": "PHing Is Not GNU make; it's a PHP project build system or build tool based on Apache Ant.", "homepage": "https://www.phing.info/", "keywords": ["build", "phing", "task", "tool"], "support": {"irc": "irc://irc.freenode.net/phing", "issues": "https://www.phing.info/trac/report", "source": "https://github.com/phingofficial/phing/tree/oldstable"}, "time": "2020-02-03T18:50:54+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.1.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "cd72d394ca794d3466a3b2fc09d5a6c1dc86b47e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/cd72d394ca794d3466a3b2fc09d5a6c1dc86b47e", "reference": "cd72d394ca794d3466a3b2fc09d5a6c1dc86b47e", "shasum": ""}, "require": {"ext-filter": "^7.1", "php": "^7.2", "phpdocumentor/reflection-common": "^2.0", "phpdocumentor/type-resolver": "^1.0", "webmozart/assert": "^1"}, "require-dev": {"doctrine/instantiator": "^1", "mockery/mockery": "^1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.1.0"}, "time": "2020-02-22T12:28:44+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "6a467b8989322d92aa1c8bf2bebcc6e5c2ba55c0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/6a467b8989322d92aa1c8bf2bebcc6e5c2ba55c0", "reference": "6a467b8989322d92aa1c8bf2bebcc6e5c2ba55c0", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"ext-tokenizer": "*"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.4.0"}, "time": "2020-09-17T18:55:26+00:00"}, {"name": "phpspec/prophecy", "version": "1.11.1", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "b20034be5efcdab4fb60ca3a29cba2949aead160"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/prophecy/zipball/b20034be5efcdab4fb60ca3a29cba2949aead160", "reference": "b20034be5efcdab4fb60ca3a29cba2949aead160", "shasum": ""}, "require": {"doctrine/instantiator": "^1.2", "php": "^7.2", "phpdocumentor/reflection-docblock": "^5.0", "sebastian/comparator": "^3.0 || ^4.0", "sebastian/recursion-context": "^3.0 || ^4.0"}, "require-dev": {"phpspec/phpspec": "^6.0", "phpunit/phpunit": "^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.11.x-dev"}}, "autoload": {"psr-4": {"Prophecy\\": "src/Prophecy"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/master"}, "time": "2020-07-08T12:44:21+00:00"}, {"name": "phpstan/phpdoc-parser", "version": "0.3.5", "source": {"type": "git", "url": "https://github.com/phpstan/phpdoc-parser.git", "reference": "8c4ef2aefd9788238897b678a985e1d5c8df6db4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/8c4ef2aefd9788238897b678a985e1d5c8df6db4", "reference": "8c4ef2aefd9788238897b678a985e1d5c8df6db4", "shasum": ""}, "require": {"php": "~7.1"}, "require-dev": {"consistence/coding-standard": "^3.5", "jakub-onderka/php-parallel-lint": "^0.9.2", "phing/phing": "^2.16.0", "phpstan/phpstan": "^0.10", "phpunit/phpunit": "^6.3", "slevomat/coding-standard": "^4.7.2", "squizlabs/php_codesniffer": "^3.3.2", "symfony/process": "^3.4 || ^4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.3-dev"}}, "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/master"}, "time": "2019-06-07T19:13:52+00:00"}, {"name": "phpstan/phpstan", "version": "0.11.20", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan.git", "reference": "938dcc03a005280e1a9587ec7684345bff06ebfc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan/zipball/938dcc03a005280e1a9587ec7684345bff06ebfc", "reference": "938dcc03a005280e1a9587ec7684345bff06ebfc", "shasum": ""}, "require": {"composer/xdebug-handler": "^1.3.0", "jean85/pretty-package-versions": "^1.0.3", "nette/bootstrap": "^2.4 || ^3.0", "nette/di": "^2.4.7 || ^3.0", "nette/neon": "^2.4.3 || ^3.0", "nette/robot-loader": "^3.0.1", "nette/schema": "^1.0", "nette/utils": "^2.4.5 || ^3.0", "nikic/php-parser": "^4.2.3", "php": "~7.1", "phpstan/phpdoc-parser": "^0.3.5", "symfony/console": "~3.2 || ~4.0", "symfony/finder": "~3.2 || ~4.0"}, "conflict": {"symfony/console": "3.4.16 || 4.1.5"}, "require-dev": {"brianium/paratest": "^2.0 || ^3.0", "consistence/coding-standard": "^3.5", "dealerdirect/phpcodesniffer-composer-installer": "^0.4.4", "ext-intl": "*", "ext-mysqli": "*", "ext-simplexml": "*", "ext-soap": "*", "ext-zip": "*", "jakub-onderka/php-parallel-lint": "^1.0", "localheinz/composer-normalize": "^1.1.0", "phing/phing": "^2.16.0", "phpstan/phpstan-deprecation-rules": "^0.11", "phpstan/phpstan-php-parser": "^0.11", "phpstan/phpstan-phpunit": "^0.11", "phpstan/phpstan-strict-rules": "^0.11", "phpunit/phpunit": "^7.5.14 || ^8.0", "slevomat/coding-standard": "^4.7.2", "squizlabs/php_codesniffer": "^3.3.2"}, "bin": ["bin/phpstan"], "type": "library", "extra": {"branch-alias": {"dev-master": "0.11-dev"}}, "autoload": {"psr-4": {"PHPStan\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan - PHP Static Analysis Tool", "support": {"issues": "https://github.com/phpstan/phpstan/issues", "source": "https://github.com/phpstan/phpstan/tree/0.11.20"}, "funding": [{"url": "https://github.com/ondrejmirtes", "type": "github"}, {"url": "https://www.patreon.com/phpstan", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpstan/phpstan", "type": "tidelift"}], "time": "2020-10-12T14:33:05+00:00"}, {"name": "phpstan/phpstan-doctrine", "version": "0.11.6", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan-doctrine.git", "reference": "77592865e167b32c7dcb4f39a35210e909a8854c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan-doctrine/zipball/77592865e167b32c7dcb4f39a35210e909a8854c", "reference": "77592865e167b32c7dcb4f39a35210e909a8854c", "shasum": ""}, "require": {"nikic/php-parser": "^4.0", "php": "~7.1", "phpstan/phpdoc-parser": "^0.3", "phpstan/phpstan": "^0.11.7"}, "conflict": {"doctrine/collections": "<1.0", "doctrine/common": "<2.7", "doctrine/mongodb-odm": "<1.2", "doctrine/orm": "<2.5"}, "require-dev": {"consistence/coding-standard": "^3.8", "dealerdirect/phpcodesniffer-composer-installer": "^0.4.4", "doctrine/collections": "^1.0", "doctrine/common": "^2.7", "doctrine/mongodb-odm": "^1.2", "doctrine/orm": "^2.5", "jakub-onderka/php-parallel-lint": "^1.0", "phing/phing": "^2.16.0", "phpstan/phpstan-phpunit": "^0.11", "phpstan/phpstan-strict-rules": "^0.11", "phpunit/phpunit": "^7.0", "slevomat/coding-standard": "^5.0.4"}, "type": "phpstan-extension", "extra": {"branch-alias": {"dev-master": "0.11-dev"}, "phpstan": {"includes": ["extension.neon", "rules.neon"]}}, "autoload": {"psr-4": {"PHPStan\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Doctrine extensions for PHPStan", "support": {"issues": "https://github.com/phpstan/phpstan-doctrine/issues", "source": "https://github.com/phpstan/phpstan-doctrine/tree/master"}, "time": "2019-09-13T08:40:06+00:00"}, {"name": "phpstan/phpstan-symfony", "version": "0.11.6", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan-symfony.git", "reference": "c7be3054c21fd472a52b1c38eb129c3f93776084"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan-symfony/zipball/c7be3054c21fd472a52b1c38eb129c3f93776084", "reference": "c7be3054c21fd472a52b1c38eb129c3f93776084", "shasum": ""}, "require": {"ext-simplexml": "*", "nikic/php-parser": "^4.0", "php": "^7.1", "phpstan/phpstan": "^0.11.7"}, "conflict": {"symfony/framework-bundle": "<3.0"}, "require-dev": {"consistence/coding-standard": "^3.0.1", "dealerdirect/phpcodesniffer-composer-installer": "^0.4.4", "jakub-onderka/php-parallel-lint": "^1.0", "nette/di": "^3.0-stable", "phing/phing": "^2.16.0", "phpstan/phpstan-phpunit": "^0.11", "phpstan/phpstan-strict-rules": "^0.11", "phpunit/phpunit": "^7.0", "slevomat/coding-standard": "^4.5.2", "squizlabs/php_codesniffer": "^3.3.2", "symfony/console": "^3.0 || ^4.0", "symfony/framework-bundle": "^3.0 || ^4.0", "symfony/messenger": "^4.2", "symfony/serializer": "^3.0 || ^4.0"}, "type": "phpstan-extension", "extra": {"branch-alias": {"dev-master": "0.11-dev"}, "phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"PHPStan\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://lookyman.net"}], "description": "Symfony Framework extensions and rules for PHPStan", "support": {"issues": "https://github.com/phpstan/phpstan-symfony/issues", "source": "https://github.com/phpstan/phpstan-symfony/tree/0.11.6"}, "time": "2019-05-19T17:40:25+00:00"}, {"name": "phpunit/php-code-coverage", "version": "6.1.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "807e6013b00af69b6c5d9ceb4282d0393dbb9d8d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/807e6013b00af69b6c5d9ceb4282d0393dbb9d8d", "reference": "807e6013b00af69b6c5d9ceb4282d0393dbb9d8d", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlwriter": "*", "php": "^7.1", "phpunit/php-file-iterator": "^2.0", "phpunit/php-text-template": "^1.2.1", "phpunit/php-token-stream": "^3.0", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^3.1 || ^4.0", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "suggest": {"ext-xdebug": "^2.6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/master"}, "time": "2018-10-31T16:06:48+00:00"}, {"name": "phpunit/php-file-iterator", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "050bedf145a257b1ff02746c31894800e5122946"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/050bedf145a257b1ff02746c31894800e5122946", "reference": "050bedf145a257b1ff02746c31894800e5122946", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/2.0.2"}, "time": "2018-09-13T20:33:42+00:00"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/1.2.1"}, "time": "2015-06-21T13:50:34+00:00"}, {"name": "phpunit/php-timer", "version": "2.1.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "1038454804406b0b5f5f520358e78c1c2f71501e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/php-timer/zipball/1038454804406b0b5f5f520358e78c1c2f71501e", "reference": "1038454804406b0b5f5f520358e78c1c2f71501e", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-timer/tree/master"}, "time": "2019-06-07T04:22:29+00:00"}, {"name": "phpunit/php-token-stream", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "995192df77f63a59e47f025390d2d1fdf8f425ff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-token-stream/zipball/995192df77f63a59e47f025390d2d1fdf8f425ff", "reference": "995192df77f63a59e47f025390d2d1fdf8f425ff", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-token-stream/issues", "source": "https://github.com/sebastian<PERSON>mann/php-token-stream/tree/3.1.1"}, "abandoned": true, "time": "2019-09-17T06:23:10+00:00"}, {"name": "phpunit/phpunit", "version": "7.5.20", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "9467db479d1b0487c99733bb1e7944d32deded2c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/9467db479d1b0487c99733bb1e7944d32deded2c", "reference": "9467db479d1b0487c99733bb1e7944d32deded2c", "shasum": ""}, "require": {"doctrine/instantiator": "^1.1", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "myclabs/deep-copy": "^1.7", "phar-io/manifest": "^1.0.2", "phar-io/version": "^2.0", "php": "^7.1", "phpspec/prophecy": "^1.7", "phpunit/php-code-coverage": "^6.0.7", "phpunit/php-file-iterator": "^2.0.1", "phpunit/php-text-template": "^1.2.1", "phpunit/php-timer": "^2.1", "sebastian/comparator": "^3.0", "sebastian/diff": "^3.0", "sebastian/environment": "^4.0", "sebastian/exporter": "^3.1", "sebastian/global-state": "^2.0", "sebastian/object-enumerator": "^3.0.3", "sebastian/resource-operations": "^2.0", "sebastian/version": "^2.0.1"}, "conflict": {"phpunit/phpunit-mock-objects": "*"}, "require-dev": {"ext-pdo": "*"}, "suggest": {"ext-soap": "*", "ext-xdebug": "*", "phpunit/php-invoker": "^2.0"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "7.5-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/7.5.20"}, "time": "2020-01-08T08:45:45+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "4419fcdb5eabb9caa61a27c7a1db532a6b55dd18"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/4419fcdb5eabb9caa61a27c7a1db532a6b55dd18", "reference": "4419fcdb5eabb9caa61a27c7a1db532a6b55dd18", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/master"}, "time": "2017-03-04T06:30:41+00:00"}, {"name": "sebastian/comparator", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "5de4fc177adf9bce8df98d8d141a7559d7ccf6da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/5de4fc177adf9bce8df98d8d141a7559d7ccf6da", "reference": "5de4fc177adf9bce8df98d8d141a7559d7ccf6da", "shasum": ""}, "require": {"php": "^7.1", "sebastian/diff": "^3.0", "sebastian/exporter": "^3.1"}, "require-dev": {"phpunit/phpunit": "^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/comparator/tree/master"}, "time": "2018-07-12T15:12:46+00:00"}, {"name": "sebastian/diff", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "720fcc7e9b5cf384ea68d9d930d480907a0c1a29"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/720fcc7e9b5cf384ea68d9d930d480907a0c1a29", "reference": "720fcc7e9b5cf384ea68d9d930d480907a0c1a29", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.0", "symfony/process": "^2 || ^3.3 || ^4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/master"}, "time": "2019-02-04T06:01:07+00:00"}, {"name": "sebastian/environment", "version": "4.2.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "464c90d7bdf5ad4e8a6aea15c091fec0603d4368"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/464c90d7bdf5ad4e8a6aea15c091fec0603d4368", "reference": "464c90d7bdf5ad4e8a6aea15c091fec0603d4368", "shasum": ""}, "require": {"php": "^7.1"}, "require-dev": {"phpunit/phpunit": "^7.5"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/4.2.3"}, "time": "2019-11-20T08:46:58+00:00"}, {"name": "sebastian/exporter", "version": "3.1.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "68609e1261d215ea5b21b7987539cbfbe156ec3e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/68609e1261d215ea5b21b7987539cbfbe156ec3e", "reference": "68609e1261d215ea5b21b7987539cbfbe156ec3e", "shasum": ""}, "require": {"php": "^7.0", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/master"}, "time": "2019-09-14T09:02:43+00:00"}, {"name": "sebastian/global-state", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/global-state/zipball/e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4", "reference": "e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/2.0.0"}, "time": "2017-04-27T15:39:26+00:00"}, {"name": "sebastian/object-enumerator", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "7cfd9e65d11ffb5af41198476395774d4c8a84c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/7cfd9e65d11ffb5af41198476395774d4c8a84c5", "reference": "7cfd9e65d11ffb5af41198476395774d4c8a84c5", "shasum": ""}, "require": {"php": "^7.0", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/master"}, "time": "2017-08-03T12:35:26+00:00"}, {"name": "sebastian/object-reflector", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "773f97c67f28de00d397be301821b06708fca0be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/773f97c67f28de00d397be301821b06708fca0be", "reference": "773f97c67f28de00d397be301821b06708fca0be", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/master"}, "time": "2017-03-29T09:07:27+00:00"}, {"name": "sebastian/recursion-context", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "5b0cd723502bac3b006cbf3dbf7a1e3fcefe4fa8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/5b0cd723502bac3b006cbf3dbf7a1e3fcefe4fa8", "reference": "5b0cd723502bac3b006cbf3dbf7a1e3fcefe4fa8", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/master"}, "time": "2017-03-03T06:23:57+00:00"}, {"name": "sebastian/resource-operations", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "4d7a795d35b889bf80a0cc04e08d77cedfa917a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/resource-operations/zipball/4d7a795d35b889bf80a0cc04e08d77cedfa917a9", "reference": "4d7a795d35b889bf80a0cc04e08d77cedfa917a9", "shasum": ""}, "require": {"php": "^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "support": {"issues": "https://github.com/sebastian<PERSON>mann/resource-operations/issues", "source": "https://github.com/sebastian<PERSON>mann/resource-operations/tree/master"}, "time": "2018-10-04T04:07:39+00:00"}, {"name": "sebastian/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/99732be0ddb3361e16ad77b68ba41efc8e979019", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019", "shasum": ""}, "require": {"php": ">=5.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/master"}, "time": "2016-10-03T07:35:21+00:00"}, {"name": "squizlabs/php_codesniffer", "version": "3.5.8", "source": {"type": "git", "url": "https://github.com/squizlabs/PHP_CodeSniffer.git", "reference": "9d583721a7157ee997f235f327de038e7ea6dac4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/squizlabs/PHP_CodeSniffer/zipball/9d583721a7157ee997f235f327de038e7ea6dac4", "reference": "9d583721a7157ee997f235f327de038e7ea6dac4", "shasum": ""}, "require": {"ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "bin": ["bin/phpcs", "bin/phpcbf"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "lead"}], "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "homepage": "https://github.com/squizlabs/PHP_CodeSniffer", "keywords": ["phpcs", "standards"], "support": {"issues": "https://github.com/squizlabs/PHP_CodeSniffer/issues", "source": "https://github.com/squizlabs/PHP_CodeSniffer", "wiki": "https://github.com/squizlabs/PHP_CodeSniffer/wiki"}, "time": "2020-10-23T02:01:07+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.5.1", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "e8b495ea28c1d97b5e0c121748d6f9b53d075c66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/e8b495ea28c1d97b5e0c121748d6f9b53d075c66", "reference": "e8b495ea28c1d97b5e0c121748d6f9b53d075c66", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/maker-bundle", "version": "v1.38.0", "source": {"type": "git", "url": "https://github.com/symfony/maker-bundle.git", "reference": "143024ab0e426285d3d9b7f6a3ce51e12a9d8ec5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/maker-bundle/zipball/143024ab0e426285d3d9b7f6a3ce51e12a9d8ec5", "reference": "143024ab0e426285d3d9b7f6a3ce51e12a9d8ec5", "shasum": ""}, "require": {"doctrine/inflector": "^1.2|^2.0", "nikic/php-parser": "^4.11", "php": ">=7.1.3", "symfony/config": "^4.4|^5.0|^6.0", "symfony/console": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/deprecation-contracts": "^2.2|^3", "symfony/filesystem": "^4.4|^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/framework-bundle": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0"}, "require-dev": {"composer/semver": "^3.0", "doctrine/doctrine-bundle": "^1.12.3|^2.0", "doctrine/orm": "^2.3", "symfony/http-client": "^4.4|^5.0|^6.0", "symfony/phpunit-bridge": "^4.4|^5.0|^6.0", "symfony/polyfill-php80": "^1.16.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/security-core": "^4.4|^5.0|^6.0", "symfony/yaml": "^4.4|^5.0|^6.0", "twig/twig": "^2.0|^3.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-main": "1.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MakerBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Maker helps you create empty commands, controllers, form classes, tests and more so you can forget about writing boilerplate code.", "homepage": "https://symfony.com/doc/current/bundles/SymfonyMakerBundle/index.html", "keywords": ["code generator", "generator", "scaffold", "scaffolding"], "support": {"issues": "https://github.com/symfony/maker-bundle/issues", "source": "https://github.com/symfony/maker-bundle/tree/v1.38.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-02-24T21:06:51+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "75a63c33a8577608444246075ea0af0d052e452a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/75a63c33a8577608444246075ea0af0d052e452a", "reference": "75a63c33a8577608444246075ea0af0d052e452a", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/master"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2020-07-12T23:59:07+00:00"}, {"name": "webmozart/assert", "version": "1.8.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "ab2cb0b3b559010b75981b1bdce728da3ee90ad6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/ab2cb0b3b559010b75981b1bdce728da3ee90ad6", "reference": "ab2cb0b3b559010b75981b1bdce728da3ee90ad6", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"vimeo/psalm": "<3.9.1"}, "require-dev": {"phpunit/phpunit": "^4.8.36 || ^7.5.13"}, "type": "library", "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.8.0"}, "time": "2020-04-18T12:12:48+00:00"}, {"name": "wizaplace/atoum-slicer", "version": "0.10.0", "source": {"type": "git", "url": "https://github.com/wizaplace/atoum-slicer", "reference": "9e2b7ec7abd4ecb2afe7076a2d034d86f8a99039"}, "require-dev": {"atoum/atoum": "^3.0", "atoum/stubs": "~2.1", "friendsofphp/php-cs-fixer": "^2.15", "phpstan/phpstan": "^0.11.8", "phpunit/phpunit": "^8.1"}, "bin": ["atoum-slicer"], "type": "library", "autoload": {"psr-4": {"Wizaplace\\Atoum\\Slicer\\": "src/"}}, "autoload-dev": {"psr-4": {"Wizaplace\\Test\\Atoum\\Slicer\\": "tests/"}}, "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Slice your Atoum test suite", "homepage": "https://github.com/wizaplace/atoum-slicer", "time": "2019-06-03T15:38:11+00:00"}, {"name": "wizaplace/phpcs", "version": "v1.2.3", "source": {"type": "git", "url": "https://github.com/wizaplace/phpcs.git", "reference": "dbddc8d1c52da15781d5a3b11d90640c0a9bc5a0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wizaplace/phpcs/zipball/dbddc8d1c52da15781d5a3b11d90640c0a9bc5a0", "reference": "dbddc8d1c52da15781d5a3b11d90640c0a9bc5a0", "shasum": ""}, "require": {"php": "^7.1", "squizlabs/php_codesniffer": "^3.5"}, "type": "library", "autoload": {"psr-4": {"BaBeuloula\\PhpCS\\": "BaBeuloula/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "BaBeuloula", "email": "<EMAIL>"}], "description": "My own PHP code sniffs", "keywords": ["BaBeuloula", "<PERSON><PERSON><PERSON>", "codesniffer", "phpcs", "standard"], "support": {"source": "https://github.com/wizaplace/phpcs/tree/v1.2.3"}, "time": "2020-07-09T08:42:56+00:00"}, {"name": "wizaplace/phpunit-slicer", "version": "0.1.2", "source": {"type": "git", "url": "https://github.com/wizaplace/phpunit-slicer.git", "reference": "50911871bb46dc2650adfc5d27f612d9c93f3adb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/wizaplace/phpunit-slicer/zipball/50911871bb46dc2650adfc5d27f612d9c93f3adb", "reference": "50911871bb46dc2650adfc5d27f612d9c93f3adb", "shasum": ""}, "require": {"php": "^7.1", "phpunit/phpunit": "^7|^8"}, "bin": ["phpunit-slicer"], "type": "library", "autoload": {"psr-4": {"Wizaplace\\PHPUnit\\Slicer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "support": {"issues": "https://github.com/wizaplace/phpunit-slicer/issues", "source": "https://github.com/wizaplace/phpunit-slicer/tree/0.1.2"}, "time": "2019-05-28T07:03:46+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {"mockery/mockery": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": "^7.4", "ext-redis": "*", "ext-pcntl": "*", "ext-intl": "*", "ext-gd": "*", "ext-mbstring": "*", "ext-pdo": "*", "ext-xml": "*", "ext-soap": "*", "ext-json": "*", "ext-curl": "*", "ext-fileinfo": "*", "ext-amqp": "*", "ext-posix": "*"}, "platform-dev": [], "plugin-api-version": "2.2.0"}