version: '3.5'

services:

  # Base de données de dev de la marketplace
  mysql:
    image: mysql:5.7.32
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: "${DATABASE_ROOT_PASSWORD}"
      MYSQL_DATABASE: "${DATABASE_NAME}"
      MYSQL_USER: "${DATABASE_USER}"
      MYSQL_PASSWORD: "${DATABASE_PASSWORD}"
      TZ: ${TZ:-"Europe/Paris"}
    volumes:
      - mysql:/var/lib/mysql
    ports:
      - "${MYSQL_PORT:-3307}:3306"

  # Base de données de test de la marketplace
  mysql_test:
    image: mysql:5.7.32
    restart: unless-stopped
    env_file: .env.test
    tmpfs:
      - /var/lib/mysql
    ports:
      - "${MYSQL_TEST_PORT:-3309}:3306"

  # Base de données de dev pour discuss
  mysql_discuss:
    image: mysql:5.7.32
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: "${DISCUSS_ROOT_PASSWORD}"
      MYSQL_DATABASE: "${DISCUSS_DBNAME}"
      MYSQL_USER: "${DISCUSS_USER}"
      MYSQL_PASSWORD: "${DISCUSS_PASSWORD}"
      TZ: ${TZ:-"Europe/Paris"}
    volumes:
      - mysql_discuss:/var/lib/mysql

  # Base de données de test pour discuss
  mysql_discuss_test:
    image: mysql:5.7.32
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: "${DISCUSS_ROOT_PASSWORD}"
      MYSQL_DATABASE: "${DISCUSS_DBNAME}"
      MYSQL_USER: "${DISCUSS_USER}"
      MYSQL_PASSWORD: "${DISCUSS_PASSWORD}"
      TZ: ${TZ:-"Europe/Paris"}
    tmpfs:
      - /var/lib/mysql

  # Outils de gestion de base de données
  adminer:
    image: adminer
    restart: unless-stopped
    ports:
      - "${ADMINER_PORT:-8081}:8080"

  # Serveur web
  http:
    image: nginx:stable-alpine
    restart: unless-stopped
    ports:
      - "${HTTP_PORT:-80}:80"
    volumes:
      - .:/var/www/html
      - ./docker/nginx/site.conf:/etc/nginx/conf.d/default.conf:ro
      - ./docker/nginx/upstream.conf:/etc/nginx/conf.d/upstream.conf:ro
    networks:
      default:
        aliases:
          - wizaplace.test
          - wizaplace.dev
          - wizaplace.prod
          - wizaplace.loc

  # PHP FPM
  php:
    build:
      context: ./docker/php
    restart: unless-stopped
    volumes:
      - .:/var/www/html
      - ./docker/php/log.conf:/usr/local/etc/php-fpm.d/zz-log.conf:ro
      - ./docker/php/php.ini:/usr/local/etc/php/conf.d/php.ini:ro
      - ./docker/php/xdebug.${XDEBUG:-disabled}.ini:/usr/local/etc/php/conf.d/xdebug.ini:ro
      - ~/.ssh:/var/www/.ssh:ro
      - xdebug:/xdebug
    env_file: .env
    environment:
      DATABASE_HOST: "mysql"
      DATABASE_PORT: "3306"
      DISCUSS_DRIVER: "pdo_mysql"
      DISCUSS_HOST: "mysql_discuss"
      DISCUSS_PORT: "3306"
      REDIS_HOST: "redis"
      REDIS_PORT: "6379"
      HTTP_HOST: "${HTTP_HOST:-wizaplace.loc}:${HTTP_PORT:-80}"
      MARKETPLACE_VERSION: dev-master
      APP_ENV: "${APP_ENV:-dev}"
      PHP_IDE_CONFIG: "serverName=${HTTP_HOST}"
      AMQP_HOST: rabbitmq
      AMQP_PORT: "${AMQP_PORT:-5672}"
      AMQP_USER: "${AMQP_USER:-guest}"
      AMQP_PASS: "${AMQP_PASS:-guest}"
      AMQP_VHOST: "${AMQP_VHOST:-/}"
      MAILER_TRANSPORT: smtp
      MAILER_HOST: mailhog
      MAILER_PORT: 1025
      MAILER_ENCRYPTION: ~
      MAILER_USER: ~
      MAILER_PASSWORD: ~

  # PHP FPM
  php_test:
    build:
      context: ./docker/php
    restart: unless-stopped
    volumes:
      - .:/var/www/html
      - ./docker/php/log.conf:/usr/local/etc/php-fpm.d/zz-log.conf:ro
      - ./docker/php/xdebug.${XDEBUG:-disabled}.ini:/usr/local/etc/php/conf.d/xdebug.ini:ro
      - ~/.ssh:/var/www/.ssh:ro
    env_file: .env.test

  # Cache + Session
  redis:
    image: redis:alpine
    restart: unless-stopped

  # Async
  rabbitmq:
    image: rabbitmq:3.7-management-alpine
    restart: unless-stopped
    environment:
        RABBITMQ_DEFAULT_USER: "${AMQP_USER:-guest}"
        RABBITMQ_DEFAULT_PASS: "${AMQP_PASS:-guest}"
        RABBITMQ_DEFAULT_VHOST: "${AMQP_VHOST:-/}"
    ports:
      - "15672:15672"
    volumes:
      - rabbitmq:/var/lib/rabbitmq

  # Used for yavin
  rabbitmq-yavin:
    image: rabbitmq:3.8-management-alpine
    restart: unless-stopped
    environment:
      RABBITMQ_DEFAULT_USER: "yavin"
      RABBITMQ_DEFAULT_PASS: "yavin"
      RABBITMQ_DEFAULT_VHOST: "/"
    ports:
      - "15673:15672"
    volumes:
      - rabbitmq-yavin:/var/lib/rabbitmq

  # Mail catcher
  mailhog:
    image: mailhog/mailhog
    restart: unless-stopped
    ports:
      - "${DOCKER_PORT_MAILHOG:-8025}:8025"

volumes:
  mysql:
  mysql_discuss:
  mysql_test:
  mysql_discuss_test:
  rabbitmq:
  xdebug:
  rabbitmq-yavin:

networks:
  default:
    driver: bridge