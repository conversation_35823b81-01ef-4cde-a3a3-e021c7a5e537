<?php

use Symfony\Component\HttpFoundation\Request;

// Platform.sh: deny direct access to Symfony controller. It must be accessed by passthru
if (strtok($_SERVER['REQUEST_URI'], '?') === '/web/app.php') {
    http_response_code(404);
    exit;
}

$url = explode("?", $_SERVER['REQUEST_URI'], 2)[0];
$controllerLegacy = false;
if (preg_match('/^\/api\/v1\/(categories|features|options|orders|products|returns|settings|shipments|shippings|statuses|taxes)(.*)/', $url, $matches) === 1
    && preg_match('/^\/api\/v1\/orders\/returns\/reasons$/', $url) !== 1
    && preg_match('/^\/api\/v1\/orders\/[0-9]*\/handDelivery$/', $url) !== 1
    && preg_match('/^\/api\/v1\/orders\/[0-9]*\/mondialRelayLabel$/', $url) !== 1
    && preg_match('/^\/api\/v1\/orders\/[0-9]*\/payment$/', $url) !== 1
    && preg_match('/^\/api\/v1\/orders\/[0-9]*\/details/', $url) !== 1
    && preg_match('/^\/api\/v1\/orders\/[0-9]*\/adjustments$/', $url) !== 1
    && preg_match('/^\/api\/v1\/orders\/[0-9]*\/transactions/', $url) !== 1
    && preg_match('/^\/api\/v1\/orders\/[0-9]*\/pdf-invoice/', $url) !== 1
    && preg_match('/^\/api\/v1\/orders\/[0-9]*\/subscriptions/', $url) !== 1
    && preg_match('/^\/api\/v1\/orders\/[0-9]*\/cancel/', $url) !== 1
    && preg_match('/^\/api\/v1\/orders\/[0-9]*\/dispatch/', $url) !== 1
    && preg_match('/^\/api\/v1\/orders\/[0-9]*\/refunds/', $url) !== 1
    && preg_match('/^\/api\/v1\/orders\/[0-9]*\/credit-notes/', $url) !== 1
    && preg_match('/^\/api\/v1\/orders\/[0-9]*\/attachments/', $url) !== 1
    && preg_match('/^\/api\/v1\/orders\/[0-9]*\/mark-as-paid/', $url) !== 1
    && preg_match('/^\/api\/v1\/orders\/[0-9]*\/mark-as-delivered/', $url) !== 1
    && preg_match('/^\/api\/v1\/orders\/[0-9]*\/actions/', $url) !== 1
    && preg_match('/^\/api\/v1\/products\/name(.*)$/', $url) !== 1
    && preg_match('/^\/api\/v1\/products\/[0-9]*\/divisions/', $url) !== 1
    && preg_match('/^\/api\/v1\/products\/[0-9]*\/video$/', $url) !== 1
    && preg_match('/^\/api\/v1\/products\/[0-9]*\/related$/', $url) !== 1
    && preg_match('/^\/api\/v1\/categories\/[0-9]*\/commissions$/', $url) !== 1
    && preg_match('/^\/api\/v1\/orders\/[0-9]*\/shipments$/', $url) !== 1
    && preg_match('/^\/api\/v1\/categories\/[0-9]*\/commissions\/[0-9A-Fa-f]{8}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{12}$/', $url) !== 1
    && preg_match('/^\/api\/v1\/orders\/[0-9]*\/set-invoice-number/', $url) !== 1
    && preg_match('/^\/api\/v1\/orders\/[0-9]*\/extra/', $url) !== 1
    && preg_match('/^\/api\/v1\/report\/transactions/', $url) !== 1
    && preg_match('/^\/api\/v1\/shipments\/[0-9]*\/mark-as-delivered/', $url) !== 1
    && preg_match('/^\/api\/v1\/orders\/[0-9]*\/child/', $url) !== 1
) {
    $controllerLegacy = true;
    $_GET['_d'] = $matches[1] . $matches[2];
    $_GET['ajax_custom'] = 1;
}

if (preg_match('/^\/csv\/(.*)/', $url, $matches) === 1) {
    $controllerLegacy = true;
    $_GET['is_csv'] = 1;
    $_GET['_d'] = $matches[1];
    $_GET['ajax_custom'] = 1;
}

// Autoriser l'utilisation de la méthode OPTIONS sur les route csCart sans token
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    $controllerLegacy = false;
}

// CS Cart constant
\define('NO_SESSION', true);

if ($controllerLegacy === true) {
    \define('API', true);
} else {
    \define('AJAX_REQUEST', true); // to avoid CS Cart's AJAX stuff to override the response
}

require __DIR__ . '/../init.php';

// Detect if the app run in a subdirectory = if the parent ( = / ) of the parent ( = /web ) of this file is not /
// Ex: correct case: SCRIPT_NAME = /web/app.php => the baseUrl will be empty, and the routing is done correctly
// Ex: subfolder case: SCRIPT_NAME = /toto/web/app.php => transform in /toto/app.php => the baseUrl will be /toto and the routing is not disturbed by the prefix
if (\dirname(\dirname($_SERVER['SCRIPT_NAME'])) != '/') {
    // Without the /web part, Request::getUrlencodedPrefix returns the right prefix
    $_SERVER['SCRIPT_NAME'] = str_replace('/web', '', $_SERVER['SCRIPT_NAME']);
}

$request = Request::createFromGlobals();
$trustedProxies = explode(',', $kernel->getContainer()->getParameter('config.security.trusted_proxies'));
// Uniquement quand on est dans le cluster k8s
if (false !== getenv('KUBERNETES_SERVICE_HOST')) {
    // On fait confiance à toutes les requêtes qui arrivent,  dans le cas ou on est
    // en direct, REMOTE_ADDR correspond à l'adresse réelle du client, dans le cas
    // de k8s, elle correspond à l'adresse de l'ingress
    $trustedProxies = \array_merge($trustedProxies, ['127.0.0.1', 'REMOTE_ADDR']);
}
Request::setTrustedProxies($trustedProxies, Request::HEADER_X_FORWARDED_ALL);

$response = $kernel->handle($request);
$response->send();
$kernel->terminate($request, $response);
