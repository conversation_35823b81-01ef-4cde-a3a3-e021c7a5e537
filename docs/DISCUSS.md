# Discuss

Le module "Discuss" fournit un système de messagerie entre les utilisateurs. Celui-ci est utilisé pour faire la messagerie acheteur/vendeur.

Les échanges entre acheteurs et vendeurs passent par un système de messagerie interne (plutôt que par email) car c'est dans l'intérêt d'un marketplace que tous les échanges se fassent via celle-ci. Sinon les vendeurs auraient intérêt à contourner la marketplace pour éviter les frais de commissions.

Cette messagerie acheteur/vendeur fonctionne de plusieurs façons différentes :

- un acheteur peut démarrer une discussion avec un vendeur à partir d'un produit (cela lui permet de poser des questions à propos d'un produit précis)
- un acheteur peut démarrer une discussion avec un vendeur sans produit particulier

Méthodes principales :

- `DiscussService::createDiscussionWithCompany()`
- `DiscussService::createDiscussionWithProductSeller()`
