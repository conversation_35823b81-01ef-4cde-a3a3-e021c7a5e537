# Order

Ce module représente les commandes des utilisateurs et les concepts liés (retours, réclamations, etc.)

## Commandes (`Order`)

Il y a 13 statuts différents pour les `Order`:

- `STANDBY_BILLING`: "Attente de paiement"
- `STANDBY_VENDOR`: "Validation vendeur"
- `PROCESSING_SHIPPING`: "Envoi en cours"
- `PROCESSED`: "Traitée"
- `COMPLETED`: "Terminée"
- `BILLING_FAILED`: "Paiement échoué"
- `VENDOR_DECLINED`: "Refusée par le vendeur"
- `STANDBY_SUPPLYING`: "Attente d'approvisionnement"
- `UNPAID`: "Impayée"
- `REFUNDED`: "Remboursée"
- `CANCELED`: "Annulée"
- `IMCOMPLETED`: "Imcomplète"
- `PARENT_ORDER`: "Parente"

## Retours (`OrderReturn`)

Il y a 4 statuts différents pour les `OrderReturn`:

- `R`: "Demande de retour en cours"
- `A`: "Réception enregistrée"
- `C`: "Terminée"
- `D`: "Refusée par le vendeur"

Un `Order` est éligible au retour s'il a le statut `PROCESSING_SHIPPING` ou `PROCESSED`.  
Le statut `COMPLETED` est celui qui marque l'arrêt de l'égibilité.

Les retours sont également appelés "RMA" (Return Merchandise Authorization) mais on préfère utiliser "Order Return".
