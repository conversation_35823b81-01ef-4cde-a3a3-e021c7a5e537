# XDebug

XDebug est une extention pour PHP qui permet notament de faire du debugging en mode pas à pas.

Point négatif, les performances en sont grandement dégradées. Il convient donc de l'activer uniquement quand vous en avez besoin.

## Activation/Desactivation 

Il est possible d'activer ou desactiver l'extension sur les conteneurs de dev et de tests grâce aux commandes suivante :

```bash
make docker-dev-xdebug-enable
make docker-dev-xdebug-disable
```

```bash
make docker-test-xdebug-enable
make docker-test-xdebug-disable
```

## Debugger un script CLI

Quand vous lancez un script en CLI, XDebug n'est pas lancé. Toutefois, vous pouvez lancer la commande suivante :

```bash
make docker-dev-debug cmd="bin/console foo:command"
```

```bash
make docker-test-debug cmd="bin/console foo:command"
```

Si vous êtes déjà connecté dans un conteneur docker vous pouvez lancer directement :

```bash
make debug cmd="bin/console foo:command"
```

## Utiliser XDebug dans PhpStorm

cf [Wiki](https://github.com/wizaplace/wizaplace/wiki/Xdebug-avec-PhpStorm)
