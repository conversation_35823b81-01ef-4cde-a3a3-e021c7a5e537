@startuml OrderStatus
hide empty description

skinparam state {
    ArrowColor Black
    BorderColor Black
    BackgroundColor<<order>> LightSkyBlue
    BackgroundColor<<cancel>> Orange
    BackgroundColor<<refund>> TECHNOLOGY
    BackgroundColor<<transition>> Blue
}

skinparam note{
    BorderColor black
}

INCOMPLETED: État initial à la création de la commande
[*] --> INCOMPLETED <<order>>: UI: validation du panier\n API: POST /basket/{basketId}/order

INCOMPLETED --> STANDBY_BILLING: UI/API: module de paiement\n (avec PSP)
INCOMPLETED -> CANCELED.unpaid: UI: Annulation\n de la commande
INCOMPLETED -right-> STANDBY_VENDOR: auto\n (B2B, sans PSP)

STANDBY_BILLING: en attente de validation de paiement
BILLING_FAILED: échec du paiement

STANDBY_BILLING <<order>> --> STANDBY_VENDOR: PSP\nIPN ok
    STANDBY_BILLING --> BILLING_FAILED: PSP\nIPN ko
    STANDBY_BILLING --> INCOMPLETED: CRON_Orders_Trash\n (orders:trash-abandoned)
    STANDBY_BILLING --> CANCELED.unpaid <<cancel>>: PSP: IPN ?\n  CRON ?
    BILLING_FAILED <<order>> -> CANCELED.unpaid: CRON ?

STANDBY_VENDOR: en attente de validation du vendeur
REFUND: remboursement
VENDOR_DECLINED: refusé par le vendeur
STANDBY_VENDOR <<order>> --> PROCESSING_SHIPPING: UI: Accepter la commande\n API: PUT /orders/{orderId}
    VENDOR_DECLINED <<order>> --> REFUND <<refund>>: UI: Remboursement
    STANDBY_VENDOR --> VENDOR_DECLINED: UI: Refuser la commande\n API: PUT /orders/{orderId}
    STANDBY_VENDOR -> CANCELED.paid <<cancel>>: CRON_Orders_check_acceptation\n (orders:check-acceptation-delay)\n ou \n UI:  Annulation\n de la commande

PROCESSING_SHIPPING: livraison en cours de préparation
PROCESSING_SHIPPING <<order>> --> PROCESSED: UI:\n Numéro de facture\n + Créer livraison
    PROCESSING_SHIPPING --> CANCELED.paid: UI: Annulation\n de la commande

PROCESSED: livraison préparé
PROCESSED <<order>> --> COMPLETED: CRON_Orders_Completed (orders:update-to-completed)\n + CRON_Orders_End_Withdrawal (orders:end-withdrawal-period) \n + CRON_Dispatch_funds (orders:dispatch-funds)
    PROCESSED -> CANCELED.paid <<cancel>>: UI: Annulation\n de la commande


COMPLETED <<order>> --> [*]

@enduml
