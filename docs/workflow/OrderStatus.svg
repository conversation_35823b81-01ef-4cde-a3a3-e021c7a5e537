<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" contentScriptType="application/ecmascript" contentStyleType="text/css"
     height="1019px" preserveAspectRatio="none" style="width:967px;height:1019px;" version="1.1" viewBox="0 0 967 1019"
     width="967px" zoomAndPan="magnify"><defs><filter height="300%" id="fz609yz4ifqjc" width="300%" x="-1" y="-1"><feGaussianBlur result="blurOut" stdDeviation="2.0"/><feColorMatrix in="blurOut" result="blurOut2" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 .4 0"/><feOffset dx="4.0" dy="4.0" in="blurOut2" result="blurOut3"/><feBlend in="SourceGraphic" in2="blurOut3" mode="normal"/></filter></defs><g><rect fill="#87CEFA" filter="url(#fz609yz4ifqjc)" height="50.2656" rx="12.5" ry="12.5" style="stroke: #000000; stroke-width: 1.5;" width="274" x="389.5" y="120"/><line style="stroke: #000000; stroke-width: 1.5;" x1="389.5" x2="663.5" y1="146.2969" y2="146.2969"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="100" x="476.5" y="137.9951">INCOMPLETED</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="254" x="394.5" y="162.4355">État initial à la création de la commande</text><ellipse cx="526.5" cy="18" fill="#000000" filter="url(#fz609yz4ifqjc)" rx="10" ry="10" style="stroke: none; stroke-width: 1.0;"/><rect fill="#87CEFA" filter="url(#fz609yz4ifqjc)" height="50.2656" rx="12.5" ry="12.5" style="stroke: #000000; stroke-width: 1.5;" width="254" x="505.5" y="262"/><line style="stroke: #000000; stroke-width: 1.5;" x1="505.5" x2="759.5" y1="288.2969" y2="288.2969"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="125" x="570" y="279.9951">STANDBY_BILLING</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="234" x="510.5" y="304.4355">en attente de validation de paiement</text><rect fill="#FFA500" filter="url(#fz609yz4ifqjc)" height="40" rx="12.5" ry="12.5" style="stroke: #000000; stroke-width: 1.5;" width="136" x="767.5" y="409"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="126" x="772.5" y="433.8467">CANCELED.unpaid</text><rect fill="#87CEFA" filter="url(#fz609yz4ifqjc)" height="50.2656" rx="12.5" ry="12.5" style="stroke: #000000; stroke-width: 1.5;" width="246" x="249.5" y="404"/><line style="stroke: #000000; stroke-width: 1.5;" x1="249.5" x2="495.5" y1="430.2969" y2="430.2969"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="134" x="305.5" y="421.9951">STANDBY_VENDOR</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="226" x="254.5" y="446.4355">en attente de validation du vendeur</text><rect fill="#87CEFA" filter="url(#fz609yz4ifqjc)" height="50.2656" rx="12.5" ry="12.5" style="stroke: #000000; stroke-width: 1.5;" width="141" x="542" y="404"/><line style="stroke: #000000; stroke-width: 1.5;" x1="542" x2="683" y1="430.2969" y2="430.2969"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="105" x="560" y="421.9951">BILLING_FAILED</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="121" x="547" y="446.4355">échec du paiement</text><rect fill="#90EE90" filter="url(#fz609yz4ifqjc)" height="50.2656" rx="12.5" ry="12.5" style="stroke: #000000; stroke-width: 1.5;" width="120" x="643.5" y="730"/><line style="stroke: #000000; stroke-width: 1.5;" x1="643.5" x2="763.5" y1="756.2969" y2="756.2969"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="58" x="674.5" y="747.9951">REFUND</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="100" x="648.5" y="772.4355">remboursement</text><rect fill="#87CEFA" filter="url(#fz609yz4ifqjc)" height="50.2656" rx="12.5" ry="12.5" style="stroke: #000000; stroke-width: 1.5;" width="157" x="625" y="559.5"/><line style="stroke: #000000; stroke-width: 1.5;" x1="625" x2="782" y1="585.7969" y2="585.7969"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="137" x="635" y="577.4951">VENDOR_DECLINED</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="135" x="630" y="601.9355">refusé par le vendeur</text><rect fill="#87CEFA" filter="url(#fz609yz4ifqjc)" height="50.2656" rx="12.5" ry="12.5" style="stroke: #000000; stroke-width: 1.5;" width="228" x="62.5" y="559.5"/><line style="stroke: #000000; stroke-width: 1.5;" x1="62.5" x2="290.5" y1="585.7969" y2="585.7969"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="162" x="95.5" y="577.4951">PROCESSING_SHIPPING</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="208" x="67.5" y="601.9355">livraison en cours de préparation</text><rect fill="#FFA500" filter="url(#fz609yz4ifqjc)" height="40" rx="12.5" ry="12.5" style="stroke: #000000; stroke-width: 1.5;" width="118" x="279.5" y="735"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="108" x="284.5" y="759.8467">CANCELED.paid</text><rect fill="#87CEFA" filter="url(#fz609yz4ifqjc)" height="50.2656" rx="12.5" ry="12.5" style="stroke: #000000; stroke-width: 1.5;" width="125" x="6" y="730"/><line style="stroke: #000000; stroke-width: 1.5;" x1="6" x2="131" y1="756.2969" y2="756.2969"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="87" x="25" y="747.9951">PROCESSED</text><text fill="#000000" font-family="sans-serif" font-size="12" lengthAdjust="spacingAndGlyphs" textLength="105" x="11" y="772.4355">livraison préparé</text><rect fill="#87CEFA" filter="url(#fz609yz4ifqjc)" height="40" rx="12.5" ry="12.5" style="stroke: #000000; stroke-width: 1.5;" width="97" x="20" y="887"/><text fill="#000000" font-family="sans-serif" font-size="14" lengthAdjust="spacingAndGlyphs" textLength="87" x="25" y="911.8467">COMPLETED</text><ellipse cx="68.5" cy="998" fill="none" filter="url(#fz609yz4ifqjc)" rx="10" ry="10" style="stroke: #000000; stroke-width: 1.0;"/><ellipse cx="69" cy="998.5" fill="#000000" rx="6" ry="6" style="stroke: none; stroke-width: 1.0;"/><!--link *start to INCOMPLETED--><path d="M526.5,28.3929 C526.5,47.0758 526.5,86.9843 526.5,114.8818 " fill="none" id="*start-&gt;INCOMPLETED" style="stroke: #000000; stroke-width: 1.0;"/><polygon fill="#000000" points="526.5,119.9432,530.5,110.9432,526.5,114.9432,522.5,110.9432,526.5,119.9432" style="stroke: #000000; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="146" x="569" y="71.0669">UI: validation du panier</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="225" x="531.5" y="86.1997">API: POST /basket/{basketId}/order</text><!--link INCOMPLETED to STANDBY_BILLING--><path d="M563.411,170.2505 C574.0771,178.801 585.0943,188.9984 593.5,200 C606.5436,217.0717 616.3862,239.197 622.9206,256.8628 " fill="none" id="INCOMPLETED-&gt;STANDBY_BILLING" style="stroke: #000000; stroke-width: 1.0;"/><polygon fill="#000000" points="624.6758,261.7193,625.3784,251.8955,622.9762,257.017,617.8547,254.6148,624.6758,261.7193" style="stroke: #000000; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="178" x="612.5" y="213.0669">UI/API: module de paiement</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="69" x="669" y="228.1997">(avec PSP)</text><!--link STANDBY_BILLING to INCOMPLETED--><path d="M506.7908,261.9306 C462.0871,251.7618 421.0818,240.4486 414.5,232 C396.1117,208.3963 420.4195,187.5973 451.0312,172.2591 " fill="none" id="STANDBY_BILLING-&gt;INCOMPLETED" style="stroke: #000000; stroke-width: 1.0;"/><polygon fill="#000000" points="455.5891,170.0361,445.7465,170.3862,451.0951,172.2279,449.2534,177.5766,455.5891,170.0361" style="stroke: #000000; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="129" x="435.5" y="213.0669">CRON_Orders_Trash</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="165" x="419.5" y="228.1997">(orders:trash-abandoned)</text><!--link INCOMPLETED to CANCELED.unpaid--><path d="M663.6188,158.0262 C721.4794,166.3895 779.9755,179.62 799.5,200 C852.9818,255.8255 846.4709,356.8128 839.8879,403.7517 " fill="none" id="INCOMPLETED-&gt;CANCELED.unpaid" style="stroke: #000000; stroke-width: 1.0;"/><polygon fill="#000000" points="839.1298,408.9322,844.3906,400.6061,839.8537,403.9849,836.4749,399.4479,839.1298,408.9322" style="stroke: #000000; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="89" x="855" y="284.0669">UI: Annulation</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="108" x="847.5" y="299.1997">de la commande</text><!--link INCOMPLETED to STANDBY_VENDOR--><path d="M442.6247,170.0698 C427.0981,177.7127 412.1339,187.5143 400.5,200 C349.9182,254.285 357.8797,349.9198 366.1436,398.6342 " fill="none" id="INCOMPLETED-&gt;STANDBY_VENDOR" style="stroke: #000000; stroke-width: 1.0;"/><polygon fill="#000000" points="367.0453,403.7681,369.428,394.2118,366.1803,398.8435,361.5486,395.5958,367.0453,403.7681" style="stroke: #000000; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="29" x="408.5" y="284.0669">auto</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="103" x="373.5" y="299.1997">(B2B, sans PSP)</text><!--link STANDBY_BILLING to STANDBY_VENDOR--><path d="M586.6157,312.0599 C540.7169,337.1277 470.3018,375.5851 422.8781,401.4858 " fill="none" id="STANDBY_BILLING-&gt;STANDBY_VENDOR" style="stroke: #000000; stroke-width: 1.0;"/><polygon fill="#000000" points="418.3621,403.9522,428.1781,403.1486,422.7502,401.5555,424.3434,396.1276,418.3621,403.9522" style="stroke: #000000; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="25" x="535" y="355.0669">PSP</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="40" x="527.5" y="370.1997">IPN ok</text><!--link STANDBY_BILLING to BILLING_FAILED--><path d="M628.9704,312.0599 C625.5642,336.2444 620.4026,372.8916 616.7673,398.7023 " fill="none" id="STANDBY_BILLING-&gt;BILLING_FAILED" style="stroke: #000000; stroke-width: 1.0;"/><polygon fill="#000000" points="616.0615,403.713,621.2778,395.3589,616.7589,398.7619,613.356,394.243,616.0615,403.713" style="stroke: #000000; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="25" x="632" y="355.0669">PSP</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="40" x="624.5" y="370.1997">IPN ko</text><!--link STANDBY_BILLING to CANCELED.unpaid--><path d="M662.0154,312.1326 C683.7017,330.1504 714.1968,354.5693 742.5,374 C759.0385,385.354 777.9893,396.8202 794.4505,406.3234 " fill="none" id="STANDBY_BILLING-&gt;CANCELED.unpaid" style="stroke: #000000; stroke-width: 1.0;"/><polygon fill="#000000" points="799.0233,408.95,793.2114,400.9988,794.6876,406.4596,789.2268,407.9358,799.0233,408.95" style="stroke: #000000; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="65" x="743.5" y="355.0669">PSP: IPN ?</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="48" x="756" y="370.1997">CRON ?</text><!--link BILLING_FAILED to CANCELED.unpaid--><path d="M683.0586,429 C708.2998,429 736.7917,429 762.2547,429 " fill="none" id="BILLING_FAILED-&gt;CANCELED.unpaid" style="stroke: #000000; stroke-width: 1.0;"/><polygon fill="#000000" points="767.305,429,758.305,425,762.305,429,758.305,433,767.305,429" style="stroke: #000000; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="48" x="701.25" y="422.0669">CRON ?</text><!--link STANDBY_VENDOR to PROCESSING_SHIPPING--><path d="M249.4773,450.3528 C214.9887,458.9781 184.0015,470.2023 173.5,484 C158.5255,503.6746 161.0401,532.3444 166.2748,554.0321 " fill="none" id="STANDBY_VENDOR-&gt;PROCESSING_SHIPPING" style="stroke: #000000; stroke-width: 1.0;"/><polygon fill="#000000" points="167.5786,559.1053,169.2122,549.3929,166.3339,554.2627,161.464,551.3844,167.5786,559.1053" style="stroke: #000000; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="170" x="175" y="497.0669">UI: Accepter la commande</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="167" x="178.5" y="512.1997">API: PUT /orders/{orderId}</text><!--link VENDOR_DECLINED to REFUND--><path d="M703.5,609.6579 C703.5,640.2567 703.5,691.8708 703.5,724.6465 " fill="none" id="VENDOR_DECLINED-&gt;REFUND" style="stroke: #000000; stroke-width: 1.0;"/><polygon fill="#000000" points="703.5,729.7389,707.5,720.7389,703.5,724.7389,699.5,720.7389,703.5,729.7389" style="stroke: #000000; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="128" x="704.5" y="681.0669">UI: Remboursement</text><!--link STANDBY_VENDOR to VENDOR_DECLINED--><path d="M426.0487,454.1566 C486.1437,482.3885 583.3121,528.0371 645.2596,557.1393 " fill="none" id="STANDBY_VENDOR-&gt;VENDOR_DECLINED" style="stroke: #000000; stroke-width: 1.0;"/><polygon fill="#000000" points="650.0282,559.3796,643.5831,551.9324,645.5027,557.2536,640.1815,559.1731,650.0282,559.3796" style="stroke: #000000; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="161" x="554.5" y="497.0669">UI: Refuser la commande</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="167" x="553.5" y="512.1997">API: PUT /orders/{orderId}</text><!--link STANDBY_VENDOR to CANCELED.paid--><path d="M371.5825,454.1607 C369.5828,502.1814 363.8911,610.1256 350.5,700 C349.0417,709.7874 346.8598,720.4362 344.7549,729.7341 " fill="none" id="STANDBY_VENDOR-&gt;CANCELED.paid" style="stroke: #000000; stroke-width: 1.0;"/><polygon fill="#000000" points="343.5984,734.7466,349.5194,726.8763,344.7225,729.8746,341.7243,725.0777,343.5984,734.7466" style="stroke: #000000; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="213" x="368.5" y="559.0669">CRON_Orders_check_acceptation</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="211" x="371.5" y="574.1997">(orders:check-acceptation-delay)</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="16" x="467" y="589.3325">ou</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="93" x="430.5" y="604.4653">UI:  Annulation</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="108" x="423" y="619.5981">de la commande</text><!--link PROCESSING_SHIPPING to PROCESSED--><path d="M112.1587,609.5771 C93.7813,620.0877 75.927,634.3127 65.5,653 C53.3529,674.7701 55.4663,703.4263 59.8331,724.8204 " fill="none" id="PROCESSING_SHIPPING-&gt;PROCESSED" style="stroke: #000000; stroke-width: 1.0;"/><polygon fill="#000000" points="60.9205,729.8185,62.9157,720.1738,59.8575,724.9328,55.0985,721.8746,60.9205,729.8185" style="stroke: #000000; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="17" x="120.5" y="666.0669">UI:</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="121" x="70.5" y="681.1997">Numéro de facture</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="106" x="78" y="696.3325">+ Créer livraison</text><!--link PROCESSING_SHIPPING to CANCELED.paid--><path d="M183.5741,609.7893 C191.7791,635.2338 207.4772,674.2878 232.5,700 C245.5848,713.4453 262.6639,724.2575 279.2785,732.6181 " fill="none" id="PROCESSING_SHIPPING-&gt;CANCELED.paid" style="stroke: #000000; stroke-width: 1.0;"/><polygon fill="#000000" points="283.9487,734.9114,277.6332,727.354,279.4606,732.7076,274.107,734.535,283.9487,734.9114" style="stroke: #000000; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="89" x="245" y="673.5669">UI: Annulation</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="108" x="237.5" y="688.6997">de la commande</text><!--link PROCESSED to COMPLETED--><path d="M68.5,780.0997 C68.5,808.2003 68.5,853.4828 68.5,881.6749 " fill="none" id="PROCESSED-&gt;COMPLETED" style="stroke: #000000; stroke-width: 1.0;"/><polygon fill="#000000" points="68.5,886.7321,72.5,877.7321,68.5,881.7321,64.5,877.7321,68.5,886.7321" style="stroke: #000000; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="359" x="97.5" y="823.0669">CRON_Orders_Completed (orders:update-to-completed)</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="407" x="73.5" y="838.1997">+ CRON_Orders_End_Withdrawal (orders:end-withdrawal-period)</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="310" x="124" y="853.3325">+ CRON_Dispatch_funds (orders:dispatch-funds)</text><!--link PROCESSED to CANCELED.paid--><path d="M131.1221,755 C174.0852,755 230.9337,755 274.2433,755 " fill="none" id="PROCESSED-&gt;CANCELED.paid" style="stroke: #000000; stroke-width: 1.0;"/><polygon fill="#000000" points="279.456,755,270.456,751,274.456,755,270.456,759,279.456,755" style="stroke: #000000; stroke-width: 1.0;"/><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="89" x="160.75" y="733.0669">UI: Annulation</text><text fill="#000000" font-family="sans-serif" font-size="13" lengthAdjust="spacingAndGlyphs" textLength="108" x="153.25" y="748.1997">de la commande</text><!--link COMPLETED to *end--><path d="M68.5,927.1958 C68.5,943.9844 68.5,967.5123 68.5,982.7552 " fill="none" id="COMPLETED-&gt;*end" style="stroke: #000000; stroke-width: 1.0;"/><polygon fill="#000000" points="68.5,987.9684,72.5,978.9684,68.5,982.9684,64.5,978.9684,68.5,987.9684" style="stroke: #000000; stroke-width: 1.0;"/><!--
@startuml OrderStatus
hide empty description

skinparam state {
    ArrowColor Black
    BorderColor Black
    BackgroundColor<<order>> LightSkyBlue
    BackgroundColor<<cancel>> Orange
    BackgroundColor<<refund>> TECHNOLOGY
    BackgroundColor<<transition>> Blue
}

skinparam note{
    BorderColor black
}

INCOMPLETED: État initial à la création de la commande
[*] - -> INCOMPLETED <<order>>: UI: validation du panier\n API: POST /basket/{basketId}/order

INCOMPLETED - -> STANDBY_BILLING: UI/API: module de paiement\n (avec PSP)
INCOMPLETED -> CANCELED.unpaid: UI: Annulation\n de la commande
INCOMPLETED -right-> STANDBY_VENDOR: auto\n (B2B, sans PSP)

STANDBY_BILLING: en attente de validation de paiement
BILLING_FAILED: échec du paiement

STANDBY_BILLING <<order>> - -> STANDBY_VENDOR: PSP\nIPN ok
    STANDBY_BILLING - -> BILLING_FAILED: PSP\nIPN ko
    STANDBY_BILLING - -> INCOMPLETED: CRON_Orders_Trash\n (orders:trash-abandoned)
    STANDBY_BILLING - -> CANCELED.unpaid <<cancel>>: PSP: IPN ?\n  CRON ?
    BILLING_FAILED <<order>> -> CANCELED.unpaid: CRON ?

STANDBY_VENDOR: en attente de validation du vendeur
REFUND: remboursement
VENDOR_DECLINED: refusé par le vendeur
STANDBY_VENDOR <<order>> - -> PROCESSING_SHIPPING: UI: Accepter la commande\n API: PUT /orders/{orderId}
    VENDOR_DECLINED <<order>> - -> REFUND <<refund>>: UI: Remboursement
    STANDBY_VENDOR - -> VENDOR_DECLINED: UI: Refuser la commande\n API: PUT /orders/{orderId}
    STANDBY_VENDOR -> CANCELED.paid <<cancel>>: CRON_Orders_check_acceptation\n (orders:check-acceptation-delay)\n ou \n UI:  Annulation\n de la commande

PROCESSING_SHIPPING: livraison en cours de préparation
PROCESSING_SHIPPING <<order>> - -> PROCESSED: UI:\n Numéro de facture\n + Créer livraison
    PROCESSING_SHIPPING - -> CANCELED.paid: UI: Annulation\n de la commande

PROCESSED: livraison préparé
PROCESSED <<order>> - -> COMPLETED: CRON_Orders_Completed (orders:update-to-completed)\n + CRON_Orders_End_Withdrawal (orders:end-withdrawal-period) \n + CRON_Dispatch_funds (orders:dispatch-funds)
    PROCESSED -> CANCELED.paid <<cancel>>: UI: Annulation\n de la commande


COMPLETED <<order>> - -> [*]

@enduml

PlantUML version 1.2019.09(Mon Aug 26 18:19:51 CEST 2019)
(GPL source distribution)
Java Runtime: OpenJDK Runtime Environment
JVM: OpenJDK 64-Bit Server VM
Java Version: 11.0.4+11-post-Ubuntu-1ubuntu218.04.3
Operating System: Linux
OS Version: 4.15.0-69-generic
Default Encoding: UTF-8
Language: fr
Country: FR
--></g></svg>
