# AMQP (Advanced Message Queuing Protocol)

L'AMQP est un protocole ouvert pour les systèmes de messagerie.

Au sein de Wizaplace nous utilisons [RabbitMQ](https://www.rabbitmq.com/), qui est notamment utilisé pour la gestion des imports.
Il permet une gestion asynchrone du traitement des données.
Ainsi il suffit de pousser un message dans la file correspondante et le script vous rend la main instantanément, peut importe la grosseur du traitement à faire.

## Ajouter une nouvelle file de message

Pour configurer une nouvelle file de message il faut ajouter dans le fichier *parameters.default.yml* le nom de la file au paramètre *marketplace.queue.config*.

Exemple :
Ici on ajoute la file **emails** avec l'option **priority** qui permet de gérer l'ordre de priorité des files.

```conf
marketplace.queue.config:
    emails:
        priority: 10
```

Note : Ne pas utiliser comme nom de file "wait_retry", "retry", "dead_letter" car ces files sont créés par defaut et leurs noms sont réservés.

## Configuration

La commande *amqp:create-queues* crée obligatoirement les files suivantes:

- wait_retry (si un message est en echec pour la première fois il est ajouté à cette file puis au bout de 5min il est transféré à la file *retry*)
- retry (cette file consomme les messages qui ont été en echec une fois, si ils retombent en echec alors ils sont mis dans la file *dead_letter* )
- dead_letter (cette file sauvegarde pendant une semaine les messages en echec)

La création de ces files permet de temporiser les messages en cas d'erreur.

## Activer RabbitMQ et lancer la commande de création des files

Accès à l'interface RabbitMQ: http://wizaplace.loc:15672/ (guest/guest)

Pour activer RabbitMQ il faut ajouter ces variables d'environnement:

```env
QUEUE_TYPE=amqp
AMQP_HOST=127.0.0.1
AMQP_PORT=5672
AMQP_USER=guest
AMQP_PASS=guest
```

Redémarrez Docker :

```sh
make docker-dev-restart
```

Lancer la commande pour créer les files de messages :

```sh
bin/console amqp:create-queues
```

## Prise en charge de l'Async en environnement de dev.

Une commande simplifiée permet l'exécution d'un worker et du debouncer :

```sh
bin/async.sh
```

Tout les traitements asynchrones nécessaires au bon fonctionnement de l'application sont pris en charge tant que cette commande est en cours d'exécution.

On peut aussi manuellement lancer ces commandes indépendamment :

### Lancement du worker

Une fois un fichier CSV d'import uploadé via le BO ou via l'API, il faut lancer le worker pour traiter la file de messages.

Démarrez un shell au sein du container PHP :

```sh
make docker-dev-shell
```

Et afin de traiter les messages des files RabbitMQ, utilisez la commande symfony `bin/console amqp:consume`

```sh
# Démarrer un worker qui traite les messages de toute les files (legacy style)
bin/console amqp:consume

# Démarrer un worker qui traite les messages d'une file spécifique
bin/console amqp:consume readmodel

# Afficher l'aide
bin/console amqp:consume --help
```

Vous pouvez lister les files créees sur RabbitMQ avec :

```sh
bin/console amqp:list
```

Il peut être nécessaire d'augmenter quelques limites pour travailler avec des gros fichiers :

Taille max d'un fichier à l'upload :

```ini
# ./docker/php/php.ini

upload_max_filesize = 20M
```

### Lancement du Debouncer

Lors de traitements successifs du PIM via nos API, un debouncer permet d'éviter les projections intempestives de readmodels FP et FPU.
En plus des workers, il est necessaire de lancer ce debouncer afin de mettre à jour ces readmodels (et donc les infos du catalog).

```sh
bin/debouncer.sh
```

## Problèmes connus

Si lors d'un `reload-data` vous avez l'erreur suivante :
`ErrorException : stream_socket_client(): unable to connect to tcp://127.0.0.1:5672 (Connection refused)`,
il faut désactiver l'AMQP de votre docker et relancer le `reload-data`.

## Sur platform.sh

Sur platform.sh nous n'avons pas accès à l'UI web, toutefois il existe quelques commandes cli (`amqp-*`) pour faire de la maintenance.

Ex. :

### Purges des files

Si besoin vous pouvez vider les files avec cette commande :

```sh
amqp-purge -H rabbitmq.internal {nom_de_la_file}
```

### Statistiques des files

Si vous souhaitez connaitre le nombres de messages actuellement dans les files :

```sh
amqp-statq -H rabbitmq.internal $(bin/console amqp:list | cut -d '|' -f 2 | grep '\w' | tail +2)
```
