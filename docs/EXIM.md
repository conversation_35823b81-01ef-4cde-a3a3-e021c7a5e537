# Exim

Présentation et implémentation des exports et des imports de la marketplace depuis le Backoffice et depuis l'API.

## Sommaire

1. Export
2. Import
3. Gestion des erreurs
4. Activer RabbitMQ
5. Ajouter un nouvel Export / Import

## 1. Export

Les exports de données sont disponibles via le Backoffice.
Chaque export est effectué au format CSV avec la possiblité de définir les champs à exportés ainsi que le choix de/des langues.

Il est aussi possible de définir le délimiteur CSV.

Exports disponibles:

- Produits
- Commandes
- Attributs
- Traductions
- Newsletters
- Catégories
- Divisions
- Fiches produits unifiées
- Promotions marketplace

## 2. Import

Les imports de données sur la Marketplace se font uniquement avec un fichier au format CSV.

Liste des imports disponibles:

- Produits
- Catégories
- Attributs (features)
- Variantes  (features_variants)
- Liens de catégories
- Divisions
- Fiches produits unifiées

Liste des routes API:

- /api/v1/import/products
- /api/v1/import/categories
- /api/v1/import/attributes
- /api/v1/import/variants
- /api/v1/import/linksCategories
- /api/v1/import/divisions
- /api/v1/import/multiVendorProducts

### Import produits

Seul les Marchands ont la possibilités d'importer des produits.

Les imports de produits sont effectués de manière asynchrone en étant envoyés dans une file de messages [RabbitMQ](./AMQP.md).

Le mode de fonctionnement est l'ajout et mise à jour des produits en se basant sur le champ **product_code**.

Aucune suppression n'est effectué lors d'un import.

### Import categories, attributs, variantes, ...

Seul l'administrateur de la marketplace est autorisé à importer ces données.

Ces imports sont effectués de manière synchrone et ne sont pas traités pas RabbitMQ.

## 3. Gestion des erreurs

À chaque import un **job** est créé afin de logguer les élement importés, rejetés et comportant des erreurs.

Ce **job** permet de définir le statut d'un import :
- PENDING (import en cours)
- SUCCESS (toutes les lignes ont été importés)
- WARNING (l'import contient au moins un warning)
- ERROR (au moins une ligne n'a pas pu être importée)

La gestion des logs se fait via la librairie Symfony Monolog avec l'ajout de deux Handlers :
- EximJobLogHandler qui stock les logs en BDD
- EximJobHandler qui incrémente les compteurs et fixe le statut à la fin de l'import

Ces jobs d'import sont disponibles via le Backoffice ainsi que via l'API:
- /api/v1/jobs (liste)
- /api/v1/jobs/{jobId} (détails)
- /api/v1/jobs/{jobId}/report (rapport au format CSV)

## 4. Activer RabbitMQ

cf [AMQP.md](./AMQP.md).

## 5. Ajouter un nouvel Export / Import

### Créer un schéma cscart

Aller dans le répertoire `app/schemas/exim/` dupliquer un schéma existant (ex: companies).
Je n'expliquerai pas tout le détail des schémas ici car je ne maitrise pas assez le sujet, la meilleur technique reste de s'inspirer de l'existant.

Voici tout de même un petit resumé: A la base, il fallait décrire le mapping de la table sql, des champs, jointures ...
Il est maintenant préférable de passer par une classe qui fait la passerelle entre le schema et les entitées de la marketplace.
Pour cela, dans le schéma, il faut utiliser la propriété `process_get` dans le detail d'une colonne. Elle permet de renseigner le nom
d'une methode à executer pour récupérer la valeur de cette colonne. Dans la même idée, il existe une propriété de colonne `process_import`.
```php
'Status' => [
    'linked' => false,
    'process_get' => ['\Wizacha\Exim\Companies::getStatus', '#key'],
],
```

### Créé un classe passerelle:

Ces classes passerelles se trouvent dans le dossier `src/Exim`.
Elle permettent de retourner le contenu d'un champ ou/et de faire le traitement d'import. Ex:
```php
# src/Exim/Companies.php

public static function getStatus(int $id): string
{
    return strtolower(static::getCompany($id)->getStatus()->getKey());
}
```
Il est important d'optimiser la récupération des données pour ne pas faire de requêtes à chaque colonne. Ex:
```php
# src/Exim/Companies.php

private static function getCompany(int $id): Company
{
    if (static::$currentCompany instanceof Company && static::$currentCompany->getId() === $id) {
        return static::$currentCompany;
    }

    static::$currentCompany = container()->get(CompanyService::class)->get($id);

    return static::$currentCompany;
}
```

### Ajout du panneau dans le Back Office

#### Menu
Il faut ajouter une nouvelle entrée dans le menu via ce fichier `src/AppBundle/Backend/Menu/MenuExtension.php` en s'inspirant du code ci-dessous.
```php
# src/AppBundle/Backend/Menu/MenuExtension.php
        ...
        if (Registry::defaultInstance()->get(['runtime', 'company_id']) === 0) {
            $menuSchema['top']['administration']['items']['export_data']['subitems']['companies'] = [
                'href' => 'exim.export?section=companies',
                'position' => 800,
            ];
        }`
```
Le module d'exim utilisera le schéma passé en paramètre (ici `companies`) pour afficher le détail de la page.
La condition dans le `if` permet d'afficher le bouton uniquement pour un admin de MP.

#### Sous menu

L'affichage du sous menu, c'est à dire, la colonne de gauche dans la page des imports/exports, se gère
dans le controlleur exim: `src/AppBundle/Controller/CsCart/backend/exim.php`
```php
# src/AppBundle/Controller/CsCart/backend/exim.php
if ($mode == 'export') {
    ...
    if (!array_key_exists('superadmin', $_GET)) {
        $exportSections = [
            'categories',
            'orders',
            'products',
            'features',
            'accounting',
        ];
    }
```
Il y a ce bout de code pour le mode import et export.

On peut aussi ajouter une condition sur un feature flag comme ci-dessous :
```php
# src/AppBundle/Controller/CsCart/backend/exim.php
...
if (container()->getParameter('feature.enable_marketplace_discounts') === true) {
    $exportSections[] = 'marketplace_discounts';
}
```

#### Permissions

Il faut gérer les permissions d'accès des admins de marchand car pour le moment tous les admins peuvent accéder
au panneau via l'url.
Cela se fait dans le fichier: `app/schemas/permissions/vendor_multivendor.php` voici un exemple de code pour interdire
l'accès des admins de marchands aux promotions marketplace:
```php
# app/schemas/permissions/vendor_multivendor.php
    ...
    'export' => [
        'sections' => [
            'marketplace_discounts' => [
                'permission' => false,
            ],
```
