# Partage de fichiers sur notre réseau local

C'est excessivement simple sur une distribution Ubuntu:

1. lancer un serveur temporaire http _(normalement installée par défaut sur la majorité des distributions linux, sinon faire `pip3 install simple_http_server`)_ :
```bash
cd chemin/du/dossier/a/partager
python3 -m http.server
```
2. trouver son hostname :
```bash
hostname
```
3. partager l'url :
```
http://<hostname>.local:8000
```
