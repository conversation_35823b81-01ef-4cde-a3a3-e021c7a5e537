# Organisation du code

```bash
app/
	config/						# Configuration de l'application
	addons/						# Partie de l'appli CS Cart sous forme d'add-ons (legacy)
	functions/					# Fonctions CS Cart (legacy)
	payments/					# Moyens de paiement (legacy)
	schemas/					# Configuration legacy de CS Cart (y compris traductions, …)
design/
	backend/					# Template Smarty CS Cart du back-office (legacy)
docs/
js/								# Javascript legacy (Back-office CS Cart par exemple)
migrations/						# Migrations SQL
src/
	Component/					# Nos composants réutilisables (penser open-source)
	Bridge/						# Extensions de libraries (Symfony, Doctrine, …)
	Marketplace/				# Code métier organisé par module
	AppBundle/					# Code applicatif (front, back-office, API, CLI, …)
	***Bundle/					# Code applicatif spécifique à chaque client
	# le reste doit être réorganisé dans les dossiers ci-dessus
tests/
	atoum/						# Tests atoum (legacy)
	PHPUnit/					# Tests unitaires et fonctionnels
	Fixture/					# Fixtures de test et outils pour les créer
var/
	cache/
	logs/
web/							# Future web root quand CS Cart aura disparu
```
