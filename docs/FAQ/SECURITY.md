# Bonnes pratiques Sécurité pour le développement

Voici un résumé des bonnes pratiques de développement concernant la sécurité que nous suivons. Ce sont des pratiques standards, c'est pourquoi il y a beaucoup de liens vers des sources plus détaillées, par exemple [PHP The Right Way](http://www.phptherightway.com/#security) (lecture de la section entière recommandée).

Autre ressource : [PHP Security Cheat Sheet de l'OWASP](https://www.owasp.org/index.php/PHP_Security_Cheat_Sheet)

Ci-dessous une liste non exhaustive des problématiques les plus communes.

## Vérification des droits utilisateurs

Tout contrôleur doit vérifier les droits de l'utilisateur connecté à accéder aux ressources demandées. Cela peut être fait via le système d'ACL de Symfony (`security.yml`) pour les pages réservées à différents rôles (admin, vendeur, …) et de manière plus fine dans les controleurs (le vendeur accède uniquement à ses  produits, etc.).

## Stockage de mot de passes

Les mots de passe sont stockés hashés et salés en utilisant la méthode officiellement recommandée par PHP: [`password_hash()`](http://php.net/function.password-hash).

[Plus d'infos](http://www.phptherightway.com/#password_hashing_title).

## Injections SQL

Utiliser systématiquement les requêtes paramètrées.

- [Doctrine DBAL](http://docs.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/security.html)


- [Doctrine ORM](http://doctrine-orm.readthedocs.io/projects/doctrine-orm/en/latest/reference/security.html)

Privilégier cette solution plutôt que des techniques d'échappement qui sont beaucoup moins fiables et plus complexes. De manière générale, s'il y a une concaténation de chaines avec une variable, il y a des questions à se poser.

Attention : cela concerne le SQL mais aussi potentiellement tout autre système de stockage de données.

[Plus d'infos](https://fr.wikipedia.org/wiki/Injection_SQL)

## Injections XSS ou autres injections via les données utilisateurs

Il faut échapper toutes les données _non safe_ lorsque l'on souhaite les afficher à l'utilisateur. Dans Twig cela est fait automatiquement, mais ça n'est pas si simple.

Lorsque l'on affiche une chaine dans du HTML on va vouloir échapper (par exemple) des tags HTML, ce que Twig fait par défaut.

Mais si on veut dumper cette chaine dans du Javascript/JSON, on va plutôt vouloir échapper les guillemets (et non pas les tags HTML). Si on dump la chaine dans une URL, on va vouloir l'échapper différemment aussi.

```handlebars
{{ user.username }}
{{ user.username|e }} # équivalent à la ligne du dessus
{{ user.username|e('html') }} # équivalent à la ligne du dessus

var js = "{{ user.username|e('js') }}"; # Pour dumper dans une chaine JS

<div class="{{ var|e('html_attr') }}"></div> # Pour dumper dans un attribut HTML

... voir les autres dans la doc ci-dessous
```

[Plus d'infos sur le filtre `|escape`](https://twig.sensiolabs.org/doc/2.x/filters/escape.html)

À noter que c'est une réflexion à avoir dans tout moteur de template (Smarty, VueJS, …), mais aussi si on génère du HTML, XML, JSON ou autre export de données.

## Données externes

Résumé :

> Ne jamais faire confiance à des données qui viennent de l'extérieur du système.

Quand on récupère des données d'une requête HTTP, il faut s'assurer de ne garder que ce qui a du sens. Par exemple si on attend un ID numérique on peut utiliser la méthode `getInt()` plutôt que `get()` sur la `Request` Symfony, cela limite les possibilités d'injecter du contenu inattendu. De manière générale, être le plus restrictif et précis possible en validant les données.

La validation ne résoud pas tous les problèmes et elle va de pair avec ce qui est expliqué dans la section précédente lorsque les données ressortent du système.

[Plus d'infos](http://www.phptherightway.com/#data_filtering_title)

## CSRF

Symfony fournit des outils pour se protéger des attaques CSRF : [plus d'infos](http://symfony.com/doc/current/form/csrf_protection.html).

## Erreurs

En environnement de production les pages ou réponses d'erreur ne doivent pas exposer des informations concernant notre système. Donc pas d'affichage de stack traces, et de manière générale ne pas afficher les messages d'exceptions (du code) aux utilisateurs. Il est préférable de catcher les exceptions dans les contrôleurs et renvoyer une réponse avec un message plus approprié (et plus explicite pour l'utilisateur).
