# Page d'administration

La page `dispatch patch` permet d'exécuter manuellement des actions de maintenance via le back-office : [http://wizaplace.loc/admin.php?dispatch=patch](http://wizaplace.loc/admin.php?dispatch=patch).

Actions utiles :
- `automated_products_feeds` : Lancé toutes les nuits pour les chargement des flux renseignés sur les vendeurs
- `clear_read_model_product` : Supprime toutes les projections de produits. Cela peut être utile lorsque les ReadModel du déploiement n-1 ne sont pas compatibles avec le deploiement n. Par contre, le temps de regénérer les ReadModels, le site va générer des 404 sur les produits
- `company_options` : Permet de recréer l'option "libre" sur les entreprises qui ne l'ont pas
- `dump_read_model_product` : Permet de récuperer le ReadModel d'un produit. Utilisé pour du debug
- `payment_deferment_orders_command` : Permet de lancer manuellement la commande `PayPaymentDefermentOrdersCommand`
- `phpinfo` : Affiche un phpinfo (pour debug)
- `search_engine_update_products` ou `search_engine_update_product_config` : Permet de pousser tous les produits dans le moteur de recherche (utile en cas de désynchro)
- `update_read_model_product` : Mise à jour de tous les ReadModels
