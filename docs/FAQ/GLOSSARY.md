# Glossary

Ce glossaire liste le vocabulaire employé. Pour chaque définition on essaye de lister le terme français suivi du terme anglais, et éventuellement une courte définition si c'est pertinent.

Pour les termes qui peuvent avoir des synonymes qu'on choisit de ne pas utiliser il est utile de lister les synonymes (par exemple on choisit d'utiliser "basket" plutôt que "cart" pour les paniers). Cela facilite la recherche dans ce document.

## Général

- B2B : Business To Business
- B2C : Business To Customer
- C2C : [Customer To Customer](https://fr.wikipedia.org/wiki/Customer_to_customer) (examples: Le Bon Coin, BlaBlaCar, etc.)
- [Cs-cart](https://www.cs-cart.com/) : our legacy base
- [Facette](https://fr.wikipedia.org/wiki/Recherche_%C3%A0_facettes)
- Exim : export/import (utilisé aujourd'hui dans le PIM), voir [PIM](../PRODUCT/PIM.md)

## Métier

- MP / MKP : Marketplace
- Panier/Basket : ne pas utiliser *cart*
- [PIM](../PRODUCT/PIM.md)
- Produit/Product : voir [PIM](../PRODUCT/PIM.md)
- Déclinaison/Declination : déclinaison d'un produit en combinant des variantes d'options voir [PIM](../PRODUCT/PIM.md)
- FPU/MVP : Fiche produit unifiée/Multi-vendor product, rassemble le même produit vendu par des vendeurs différents, voir [PIM](../PRODUCT/PIM.md)
- Attributs/Attributes : voir [PIM](../PRODUCT/PIM.md)
- Variante d'attribut/Attribute variant : valeur possible pour un attribut, voir [PIM](../PRODUCT/PIM.md)
- Attributs libres/Free attributes : voir [PIM](../PRODUCT/PIM.md) et [Catalog](../PRODUCT/CATALOG.md), aka "features" dans le code legacy mais il ne faut plus utiliser ce terme
- Options/Options : permet de créer des déclinaisons d'un produit, voir [PIM](../PRODUCT/PIM.md)
- Variante d'option/Option variant : valeur possible pour une option, voir [PIM](../PRODUCT/PIM.md)
- Commande/Order : voir [Order](../ORDERS.md)
- Retour/Order return : voir [Order](../ORDERS.md)
- Expédition/Shipping : expédition d'un colis par un préparateur de commande, dans le code le terme "Shipping" est aussi utilisé pour représenter un "moyen de livraison"
- Livraison/Delivery : livraison d'un colis (d'une commande) par un transporteur (UPS, Chronopost, etc.)
- Transporteur/Carrier : UPS, Chronopost, etc.
- Shipment : expédition d'un colis pour une commande
- PSP : Payment service provider - ex: Mangopay CB, Lemonway Virement, Hipay CB, Stripe CB, etc...
- IPN : Instant Payment Notification - Cette notification est directement envoyée par le PSP à l'API Wizaplace, ne pas confondre avec la redirection envoyé au navigateur du payeur.
- Paiement/Payment : règlement d'une commande, à ne pas confondre avec une *transaction* (par exemple un acheteur pourrait avoir fait 2 transactions pour le paiement d'une commande : la 1ère qui a échouée car il a mal tapé son numéro de CB et la seconde qui a réussie => au final il y a un seul paiement car 1 seule commande et celui-ci est réussi)
- Transaction : transfert d'argent (réussi ou échoué) pour le paiement d'une commande
- Moyen de paiement/Payment method : configuré par l'administrateur, "Paiement par CB", "Paiement par virement" (utilise un provider)


## Infra

- SES : [Simple Email Service](https://aws.amazon.com/fr/ses/)
- SNS : [Simple Notification Service](https://aws.amazon.com/fr/sns/)
- SQS : [Simple Queue Service](https://aws.amazon.com/fr/sqs/)
