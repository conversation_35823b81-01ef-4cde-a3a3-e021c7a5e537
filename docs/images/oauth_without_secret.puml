@startuml

title OAuth2 flow when the client_secret is held by the front

participant UA
participant Front
participant Back
participant IDP

UA -> Front : secured firewall
Front -> UA : login button
UA -> Front : login request
Front -> UA : redirect authorize url
UA -> IDP : request code
== IDP Oauth process ==
IDP -> UA : redirect with code
UA -> Front : follow redirect
Front -> IDP : get token with code + client_id + client_secret
IDP -> Front : access_token
Front -> Back : /user/oauth-token with access_token
Back -> IDP : get user with access_token
IDP -> Back : return user info
Back -> Back : get or create user
Back -> Back : create apiKey for user
Back-> Front : return apiKey + userId
Front -> Front : authenticate user + apiKey
Front -> UA : redirect target_path Set-Cookie
UA -> Front : secured firewall with Cookie
Front -> Back : API call with apiKey authorization header
Back -> Front : API response
Front -> UA : secured page

@enduml
