@startuml

title OAuth2 flow when the client_secret is held by the back

participant UA
participant Front
participant Back
participant IDP

UA -> Front : secured firewall
Front -> UA : login button
UA -> Front : login request
Front -> Back : get authorize url
Back -> IDP : get authorize url
IDP -> Back : authorize url
Back -> Front :  authorize url
Front -> UA : redirect authorize url
UA -> IDP : request code
== IDP Oauth process ==
IDP -> UA : redirect with code
UA -> Front : follow redirect
Front -> Back : /oauth with code
Back -> IDP : oauth code + secret
IDP -> Back : access_token + OpenID
Back -> Back : get or create user
Back -> Back : create apiKey for user
Back-> Front : return apiKey + userId
Front -> Front : authenticate user + apiKey
Front -> UA : redirect target_path Set-Cookie
UA -> Front : secured firewall with <PERSON>ie
Front -> Back : API call with apiKey authorization header
Back -> Front : API response
Front -> UA : secured page

@enduml
