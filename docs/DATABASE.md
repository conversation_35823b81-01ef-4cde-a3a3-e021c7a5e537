# Database

La base de données relationnelle qu'utilise Wizaplace est un serveur MySQL 5.6.

## Purge

Quand la base de données remplit l'espace disque qui lui est alloué (en local, sur Platform.sh, etc.), Doctrine ne peut plus s'y connecter et l'application tombe en rade.

Pour faire un peu de place et reprendre la main sur la DB, on peut vider les tables suivantes :

- doctrine_auth_logs
- doctrine_exim_job_logs
