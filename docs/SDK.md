# SDK

### Lancement des tests

La première fois que vous installez le projet, lancez un composer install:

```
make docker-dev-shell
cd php-sdk
composer install
```

Important: à la suite de la commande `composer install`, les hooks git `post-checkout` et `post-merge` sont modifiés.
Le répertoire `.git/hooks` qui contient ces hooks est commun avec celui de l'application Wizaplace.
Il faudra donc refaire un `composer install` ou un `make docker-dev` de retour côté application
pour réinitialiser les hooks (cf doc [DOCKER.md](./DOCKER.md))

Pour lancer un test, il faut passer en ligne de commande.
Lorsque vous lancez un test `testMyFunction()` du fichier `MyServiceTest.php`, une cassette au format
`testMyFunction.yml` sera générée dans un dossier `MyServiceTest`. A chaque fois que vous voulez relancer le test, il
faudra supprimer la cassette.

La commande pour lancer votre test sera la suivante:

```
cd php-sdk
vendor/bin/phpunit --debug --verbose tests/PathToTestClass/MyServiceTest.php --filter testMyFunction
```


### Nommage des tests

Pour que la génération des cassettes se fasse correctement il faut nommer chaque test différemment et ce même si les
tests se trouvent dans un namespace différent.

#### Exemple :
Soit les deux méthodes suivantes :
 * php-sdk/tests/Order/OrderServiceTest::testGetOrder
 * php-sdk/tests/Vendor/Order/OrderServiceTest::testGetOrder

On aura une erreur lorsque l'on lancera les tests SDK car le second test va utiliser la cassette du premier test.
Les données de cette cassette ne correspondront donc pas au bon test.

On pourrait donc modifier le nom des méthodes de la manière suivante afin de régler le problème :
 * php-sdk/tests/Order/OrderServiceTest::testGetOrder
 * php-sdk/tests/Vendor/Order/OrderServiceTest::testGetVendorOrder
