# Ajout d'une tâche avec Phing

Phing permet d'accéder aux propriétés du système à l'aide d'une tâche.

## 1. Variable d'environnement

Définir la valeur de la variable env en utilisant le fichier `.env` et `.env.dist` situé dans le répertoire racine du projet.
```
MY_PROPERTY_NAME=value
```

## 2. Références

Référencer la variable en incluant le nom avec `env()`.
fichier `app/config/parameters.env.yml`

```
my_property_name: "%env(MY_PROPERTY_NAME)%"
```

## 3. Paramètres

Ajouter aux paramètres `env()` une valeur par défaut dans le fichier `app/config/parameters.default.yml`
```
env(MY_PROPERTY_NAME): 'default value'
```

## 4. Propriétés intégrées

Ajouter une nouvelle propriété dans le fichier `build/config.xml`

```
<propertyprompt propertyName="my_property_name" defaultValue="" promptText="" useExistingValue="true"/>
```
La valeur définie est disponible via la clé incluse dans `$ {` et `}`
```
${line.separator}  my_property_name: ${my_property_name}
```
| Attribut | Type | Description |
|---|---|---|
| `propertyName` | String | Le nom de la propriété à définir |
| `defaultValue` | String | La valeur par défaut à utiliser |
| `promptText` | String | Le texte à utiliser pour le prompt |
| `useExistingValue` | Boolean | Si la propriété existante doit être utilisée si elle est disponible |

## 5. Variables globales et paramètres
Ajouter une variable globale dans le fichier `app/config_dev.yml`
```
twig:
    globals:
        myPropertyName: '%my_property_name%'
```
Ajouter un nouveau paramètre dans le fichier `app/config.yml`
```
parameters:
    my_property_name: 'value'
```
## 6. Views
Afficher la variable globale en front.
```
{% if myPropertyName is defined and myPropertyName != "" %}
   ...
{% endif %}
```
