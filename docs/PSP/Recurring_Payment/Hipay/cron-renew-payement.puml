@startuml

title

    HiPay Recurring Payment - Sequence Diagram

endtitle

!include ./../General/_participants-cron.puml

note right of Backend  : (CRON) Fire at 12:00 PM (noon) every day

activate Backend
    loop orders has active/defaulted subscription

        alt  Commitment date expired and the subscription nonrenewable

            Backend -> Backend: Update subscription status to FINISHED

        else The payment date has been reached and either \n subscription is renewable  or commitment date is not yet reached

            Backend -> Backend: Creates Order

            Backend -> Backend: Creates Subscription

            Backend -> HiPay: Calls the PSP API

            activate HiPay

                HiPay -> HiPay: Asks for payment

                HiPay --> Backend: Sends confirmation response (OK)

            deactivate HiPay
        end
    end
deactivate Backend

@enduml
