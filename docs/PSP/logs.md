# Logs

## Description

Les requêtes aux PSP sont loguées sur un channel `psp` en respectant les formats ECS :
* `url.full` : Si on a l'url complète de la requête envoyée au PSP ("https://v1/...")
* `url.path` : Si on a seulement le endpoint de l'API du psp auquel la requête est envoyée ("/v1/...")
* `url.original` : Ce cas apparaît si on n'a pas de endpoint (`null`), notamment dans certains cas d'erreurs
* `http.request.method` : Le type de requête GET, POST etc
* `http.request.body.content` : Le corps de la requête, le plus souvent représentera un array décodé d'une réponse JSON, et anonymisé le cas échéant. Peut être `null`.

Si la requête n'a pas échoué et a une réponse, les champs suivants sont visibles :
* `http.response.code` : Le code HTTP de la requête 200, 400 etc
* `http.response.body.content` : Le corps de la réponse, le plus souvent représentera un array décodé d'une réponse JSON, et anonymisé le cas échéant. Peut être `null`.

A cela s'ajoute un contexte, quand il y en a, représentant un array de variables passées au logger.
Ces variables seront toujours au format array et leurs données anonymisées.

Si le log représente une exception, celle-ci est passée à la fonction de log et sera visible dans les champs `ctxt_exception`.

Enfin, grâce au use du `EventTraitDuration`, chaque log contient les informations relatives au temps d'exécution via les champs suivants :
* `event.start` : Date de début de la requête
* `event.end` : Date de fin de la requête
* `event.duration` : Durée de la requête
* `event.outcome` : Résultat de la requête (success ou failure)

## Logger PSP

Une classe `Wizacha\Marketplace\Payment\AbstractPaymentLogger` implémente toutes les fonctions de log :
* `logRequestSuccess` : Pour loguer des requêtes ayant réussi (code 2XX)
* `logRequestError` : Pour loguer des requêtes dont le code de réponse est différent de 2XX
* `logRequestException` : Pour loguer des requêtes en erreur (si une erreur est survenue avant l'envoi ou si le code de réponse est différent de 2XX)
* `log` : Pour les logs en-dehors des requêtes aux PSP elles-mêmes. (traitement spécifique d'une réponse)

En fonction du PSP et de sa façon de traiter et remonter les réponses en erreur (code différent de 2XX), on va utiliser soit `logRequestError` soit `logRequestException`.

>Chaque PSP doit ensuite avoir son propre logger qui gère le format des variables des requêtes spécifiques à son API, et qui doit donc étendre de `AbstractPaymentLogger`.

>**Attention ! Il ne faut pas oublier d'appeler la fonction `start` pour initialiser l'event (startEvent) avant l'envoi de la requête à l'API.**

## Anonymisation des données

Chaque fonction de log anonymise les données des 3 champs suivants quand ils sont présents :
* `http.request.body.content`
* `http.response.body.content`
* Variables de contexte

Pour ce faire, `AbstractPaymentLogger` inclut une instance de `Wizacha\Marketplace\Security\DataAnonymizer\DataAnonymizer`.

Ce `DataAnonymizer` permet de masquer un nombre de caractères dans une chaîne en gardant X caractères à gauche et Z caractères à droite.
Tout autre caractère sera remplacé par une `*`.
Pour tout masquer il suffit de définir ces deux seuils à 0.

Quelques fonctions sont implémentées pour mutualiser et faciliter des cas d'usage récurrents :
* `anonymizeIban` : Masque les caractères d'un IBAN en garder 4 caractères à gauche et à droite
* `anonymizeToken` : Masque un Token (authentification, token de carte, etc) en gardant uniquement les 2 derniers caractères
* `anonymizePersonalData` : Masque une données personnelles (nom, prénom, adresse, etc) en gardant le 1er et le dernier caractère

Une autre fonction `findAndAnonymizeData` permet d'anonymiser les données d'un array.
Celle-ci nécessite la définition de l'un des deux array `$dataToAnonymize` (destiné à `http.request.body.content` et `http.response.body.content`) et `$contextToAnonymize` (destiné donc aux variables de contexte).
Ceux-ci représente les données à anonymiser avec le type d'anonymisation à appliquer (IBAN, Token, etc.) et doit respecter l'arborescence des données d'origine pour pouvoir être trouvées et remplacées.

Chacune de ces fonctions fait appel à une fonction commune `anonymizeData` qui fait l'anonymisation à proprement parler.
Celle-ci peut également être appelée directement pour utiliser des seuils personnalisés.

>Chaque PSP qui a des données à anonymiser doit avoir son propre anonymizer dont le rôle sera uniquement de renseigner les deux listes susmentionnées, et qui doit donc étendre de `DataAnonymizer`.

>**Attention ! Il est important de respecter l'usage des fonction `mb_XXX` qui gèrent correctement les caractères multibytes.**

## Logger par PSP

* [HiPay](./hipay.md#Logs)
* [MangoPay](./mangopay.md#Logs)
* [Stripe](./stripe.md#Logs)
* [LemonWay](./lemonway.md#Logs)

## Schéma

![logs.png](./images/logs.png)
