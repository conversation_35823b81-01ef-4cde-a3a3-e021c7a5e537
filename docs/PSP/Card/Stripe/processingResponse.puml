@startuml

title Stripe CB - Processing Response \n

autonumber

participant "Wizaplace \nBackend" as Backend
participant "Strip<PERSON>" as <PERSON><PERSON>

activate Stripe
Stripe -> Backend: Notifies payment (IPN)
        activate Backend
            Backend -> Stripe: Requests payment intent information
            Stripe --> Backend: Returns payment intent information
            Backend -> Stripe: Gets charges details
            Stripe --> Backend: Returns charges details
            Backend -> Backend: Updates transaction state
            Backend -> Backend: Changes order state
            Backend --> Stripe: Sends IPN response
        deactivate Backend
deactivate Stripe
@enduml
