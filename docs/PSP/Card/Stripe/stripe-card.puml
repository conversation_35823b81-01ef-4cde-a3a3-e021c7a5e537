@startuml

title Stripe CB - Sequence Diagram \n

autonumber

participant "User Agent" as UA
participant "Marketplace \nFront Office" as Front
participant "Wizaplace \nBackend" as Backend
participant "Stripe" as Stripe

UA -> Front: Selects Stripe card
activate UA
    activate Front
        Front -> Backend: Asks for create order: \n"Checkout" API endpoint
        activate Backend
            Backend -> Backend: Creates order (incomplete)
            Backend -> Stripe: Requests hosted payment page
            activate Stripe
                Stripe -> Stripe: Asks for payment
                Stripe --> Backend: Returns redirect payment response
            deactivate Stripe
            Backend -> Backend: Creates credit card transaction with "READY" state
            Backend -> Backend: Generate the Stripe payment form
            Backend --> Front : Returns HTML Payment Response
        deactivate Backend
        Front -> Front: Integrates the answer into the checkout page
        Front --> UA: Displays Stripe page
    deactivate Front
    UA -> Stripe: Submits Stripe form
    activate Stripe
        Stripe -> Stripe: Creates transaction
            ref over Backend,Stripe
                processingResponse
            end ref
        Stripe --> UA: Sends redirection to marketplace (API payment notifications)
        deactivate Stripe
    UA -> Backend: Follows redirection
    activate Backend
    ref over Backend,Stripe
      processingResponse
    end ref
        Backend --> UA: Redirects to complete order page
    deactivate Backend
    UA -> Front: Follows redirect
    activate Front
        Front --> UA: Returns confirmation page
    deactivate Front
deactivate UA

@enduml
