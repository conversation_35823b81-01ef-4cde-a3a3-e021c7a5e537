@startuml

title

    Smoney card - Sequence Diagram

endtitle

!include ./../General/_participants-smoney-list.puml

UA -> Front: Selects Smoney card
activate UA
    activate Front
        Front -> Backend: Asks for create order: \n"Checkout" API endpoint
        activate Backend
            Backend -> Backend: Creates order (incomplete)
            Backend -> Smoney: Requests hosted payment page
            activate Smoney
                Smoney -> Smoney: Asks for payment
                Smoney --> Backend: Returns redirect payment response
            deactivate Smoney
            Backend -> Backend: Creates credit card transaction with "READY" state
            Backend --> Front : Returns redirect URL
        deactivate Backend
        Front --> UA: Redirects To Smoney page
    deactivate Front
    UA -> Smoney: Submits Smoney form
    activate Smoney
        Smoney -> Smoney: Creates transaction
        Smoney -> Backend: Sends payment notification
        activate Backend
            Backend -> Smoney: Requests transaction information
            Smoney --> Backend: Returns transaction information
            Backend -> Smoney: Gets transaction details by reference
            Smoney --> Backend: Returns transaction details
            deactivate S<PERSON>ey
            Backend -> Backend: Updates transaction state (SUCCESS)
            Backend -> Backend: Changes order state (Paid, pending processing)
            Backend --> UA: Returns redirect URL
        deactivate Backend
        UA -> Front: Follows redirect
        activate Front
            Front --> UA: Returns confirmation page
        deactivate Front
deactivate UA
@enduml
