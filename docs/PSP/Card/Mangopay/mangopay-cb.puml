@startuml

title

    MangoPay CB - Sequence Diagram

endtitle

!include ./../General/_participants-Mangopay-list.puml

UA -> Front: Selects MangoPay card
activate UA
    activate Front
        Front -> Backend: Asks for create order: \n"Checkout" API endpoint
        activate Backend
            Backend -> Backend: Creates order (incomplete)
            Backend -> MangoPay: Asks to create pay-in object
            activate MangoPay
                MangoPay -> MangoPay: Asks for payment\n(PayIn created)
                MangoPay --> Backend: Returns redirect payment response
            deactivate MangoPay
            Backend -> Backend: Creates credit card transaction with "READY" state
            Backend --> Front : Returns redirect URL
        deactivate Backend
        Front --> UA: Redirects To MangoPay page
    deactivate Front
    UA -> MangoPay: Submits MangoPay form
    activate MangoPay
        MangoPay -> Backend: Sends payment notification
        activate Backend
            Backend -> MangoPay: Gets transaction order id
            MangoPay --> Backend: Returns order id
            Backend -> MangoPay: Gets transaction details by reference
            MangoPay -> Backend: Returns transaction details
            deactivate MangoPay
            Backend -> Backend: Updates transaction state (SUCCESS)
            Backend -> Backend: Changes order state (Paid, pending processing)
            Backend --> UA: Returns redirect URL
        deactivate Backend
        UA -> Front: Follows redirect
        activate Front
            Front --> UA: Returns confirmation page
        deactivate Front
deactivate UA
@enduml
