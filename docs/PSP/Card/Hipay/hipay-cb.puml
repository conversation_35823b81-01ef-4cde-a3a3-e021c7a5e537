@startuml

title

    HiPay CB - Sequence Diagram

endtitle

!include ./../General/_participants-hipay-list.puml

UA -> Front: Selects HiPay card
activate UA
    activate Front
        Front -> Backend: Asks for create order: \n"Checkout" API endpoint
        activate Backend
            Backend -> Backend: Creates order (incomplete)
            Backend -> HiPay: Requests hosted payment page
            activate HiPay
                HiPay -> HiPay: Asks for payment
                HiPay --> Backend: Returns redirect payment response
            deactivate HiPay
            Backend -> Backend: Creates credit card transaction with "READY" state
            Backend --> Front : Returns redirect URL
        deactivate Backend
        Front --> UA: Redirects To HiPay page
    deactivate Front
    UA -> HiPay: Submits HiPay form
    activate HiPay
    HiPay -> HiPay: Creates transaction with 'captured' state
        HiPay -> Backend: Notifies payment (IPN)
        activate Backend
            Backend -> HiPay: Requests transaction information
            HiPay --> Backend: Returns transaction information
            Backend -> HiPay: Gets transaction details by reference
            HiPay --> Backend: Returns transaction details
            Backend -> Backend: Updates transaction state (SUCCESS)
            Backend -> Backend: Changes order state (Paid, pending processing)
            Backend --> HiPay: Sends IPN response
        deactivate Backend
            HiPay --> UA: Sends redirection to marketplace (API payment notifications)
    deactivate HiPay
    UA -> Backend: Follows redirection
    activate Backend
        Backend -> Backend: Same then above:\n IPN treatment
        Backend --> UA: Redirects to complete order page
    deactivate Backend
    UA -> Front: Follows redirect
    activate Front
        Front --> UA: Returns confirmation page
    deactivate Front
deactivate UA
@enduml
