@startuml

title

    HiPay CB Capture - Sequence Diagram

endtitle

!include ./../General/_participants-hipay-capture-list.puml

UA -> Front: Selects HiPay CB Capture
activate UA
    activate Front
        Front -> Backend: Asks for create order: \n"Checkout" API endpoint
        activate Backend
            Backend -> Backend: Creates order (incomplete)
            Backend -> HiPay: Requests hosted payment page
            activate HiPay
                HiPay -> HiPay: Creates an authorized transaction
                HiPay --> Backend: Returns redirect payment response
            deactivate HiPay
            Backend -> Backend: Creates credit card transactions with "READY" state
            Backend -> Backend: Creates subscription with "READY" state
            Backend --> Front : Returns redirect URL
        deactivate Backend
        Front --> UA: Redirects To HiPay page
    deactivate Front
    UA -> HiPay: Submits HiPay form
    activate HiPay
        HiPay -> Backend: Notifies Payment (IPN)
        activate Backend
            Backend -> HiPay: Requests transaction information
            HiPay --> Backend: Returns transaction information
            Backend -> HiPay: Gets transaction details by reference
            HiPay --> Backend: Returns transaction details

            Backend -> Backend: Updates transactions state to 'PENDING'
            Backend -> Backend: Changes order state
            Backend --> HiPay: Send IPN response
        deactivate Backend
            HiPay --> UA: Sends redirection to marketplace (API Payment notification)
    deactivate HiPay
    UA -> Backend: Follows redirection
    activate Backend
        Backend -> Backend: Same then above: \n IPN treatment
        Backend --> UA: Redirects to complete order page
    deactivate Backend

    UA -> Front: Follows redirect
    activate Front
        Front --> UA: Sends complete order page
    deactivate Front
deactivate UA

activate Backend
    activate Admin
        opt The order has product with adjusted price
        Admin -> Backend: Adjusts price
            Backend --> Admin: Displays order details
        end
        Admin -> Backend: Accepts order
        Backend -> HiPay: Captures the HiPay transaction for the order
        activate HiPay
            HiPay -> HiPay: Updates transaction state to 'Captured'
            HiPay --> Backend: Returns details
        deactivate HiPay
        Backend -> Backend: Updates transaction state to 'SUCCESS'
        Backend -> Backend: Changes order state
        Backend --> Admin : Displays order details
    deactivate Admin
deactivate Backend
@enduml
