@startuml
'
'LemonWay
'
class LemonWayLogger extends AbstractPaymentLogger {
    - const LEMONWAY_LOG_NAME = 'LemonWay API SDK'
    + requestSuccess(): void : calls logRequestSuccess of AbstractPaymentLogger
    + requestError(): void : calls logRequestError of AbstractPaymentLogger
    + requestException(): void : calls logRequestException of AbstractPaymentLogger
}

class LemonWayDataAnonymizer extends DataAnonymizer {
    List all LemonWay data to anonymize
}
AbstractPaymentLogger o-- DataAnonymizer

class LemonWayHttpClient {
    - logger: LemonWayLogger : used in sendRequest
    -- Methods --
    + sendRequest(): \stdClass
    -- Details --
    The logs are done in the sendRequest() method
}
LemonWayHttpClient o-- LemonWayLogger

class LemonWay {
    # api: LemonWayApi
    -- Details --
    For each LemonWay action, calls $this->api->{action}()
}
LemonWay *-- ApiAwareTrait
LemonWay o-- LemonWayApi

class LemonWayApi {
    # httpClient: LemonWayHttpClient
    -- Details --
    For each API request, calls $this->httpClient->sendRequest().
}
LemonWayApi o-- LemonWayHttpClient
@enduml
