@startuml
'
'Stripe
'
class StripeLogger extends AbstractPaymentLogger {
    - const STRIPE_LOG_NAME = 'Stripe API SDK'
    -- Methods --
    + requestSuccess(): void : calls logRequestSuccess of AbstractPaymentLogger
    + requestException(): void : calls logRequestException of AbstractPaymentLogger
    -- Details --
    Each requestXXX() method calls the getters of StripeHTTPClient:
    1. getLatestRequestMethod()
    2. getLatestRequestUrl()
    3. getLatestRequestParams()

    requestSuccess() additionally calls the response getters:
    1. getLatestResponseCode()
    2. getLatestResponseBody()
}

class StripeDataAnonymizer extends DataAnonymizer {
    List all Stripe data to anonymize
}
AbstractPaymentLogger o-- DataAnonymizer

class StripeHTTPClient extends Stripe\HttpClient\CurlClient {
    - latestRequestMethod: string
    - latestRequestUrl: string
    - latestRequestParams: array
    - latestResponseBody: string
    - latestResponseCode: int
    + request(): array(string $rbody, int $rcode, array $rheaders) : calls request of Stripe\HttpClient\CurlClient
    + getLatestRequestMethod(): string
    + getLatestRequestUrl(): string
    + getLatestRequestParams(): array
    + getLatestResponseBody(): string
    + getLatestResponseCode(): int
}

class Stripe\HttpClient\CurlClient implements Stripe\HttpClient\ClientInterface {
    + request(): array(string $rbody, int $rcode, array $rheaders)
}

interface Stripe\HttpClient\ClientInterface {
    + request(): array(string $rbody, int $rcode, array $rheaders)
}

class Stripe {
    # api: StripeApi
    -- Details --
    For each Stripe actions, calls $this->api->{action}()
    For example: $this->api->createPaymentIntent([...])
}
Stripe *-- ApiAwareTrait
Stripe o-- StripeApi

class StripeApi {
    - stripeHTTPClient: StripeHTTPClient
    - stripeLogger: StripeLogger
    -- Methods --
    + getStripeHTTPClient(): StripeHTTPClient
    + __construct() : Calls ApiRequestor::setHttpClient($stripeHTTPClient)
    -- Details --
    For each Stripe API request, calls the appropriate Stripe object Object::action(...)
    For example: $paymentIntent = PaymentIntent::create($params, $options);
    The logs are done here.
    Request info are fetched with $this->stripeHTTPClient
}
StripeApi o-- StripeLogger
StripeApi o-- StripeHTTPClient
Stripe\ApiRequestor -- StripeApi : < setHttpClient($client)

class Stripe\ApiRequestor {
    - {static} _httpClient: Stripe\HttpClient\ClientInterface (StripeHTTPClient)
    + {static} setHttpClient($client)
}
@enduml
