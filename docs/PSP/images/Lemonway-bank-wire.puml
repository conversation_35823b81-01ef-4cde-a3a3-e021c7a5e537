@startuml
title Bank wire payment

participant "User Agent " as UA
participant "Marketplace \nfront office" as Front
participant "Wizaplace API " as Wizaplace
participant "LemonWay API " as LemonWay

UA -> Front: Chooses bank wire payment
activate UA
activate Front
    Front -> Wizaplace: Requests bank wire details
    activate Wizaplace
        Wizaplace -> LemonWay: Creates or gets money-in wallet
        activate LemonWay
            LemonWay --> Wizaplace: Sends wallet details
        deactivate LemonWay
        Wizaplace --> Front: Sends bank wire details
    deactivate Wizaplace
    Front --> UA: Sends bank wire details page
deactivate Front
deactivate UA

======Buyer does the bank transfer to LemonWay IBAN==

LemonWay -> Wizaplace: Sends bank wire money-in notification
activate LemonWay
activate Wizaplace
    |||
    Wizaplace -> LemonWay: Requests more transaction details
    activate LemonWay
        LemonWay --> Wizaplace: Sends transaction details
    deactivate LemonWay
    |||
    opt With buyer identification
        Wizaplace -> LemonWay: Requests transfer from buyer to technical wallet
        activate LemonWay
            LemonWay --> Wizaplace: Sends transaction response
        deactivate LemonWay
    end
    |||
    Wizaplace -> Wizaplace: Manages order payment
    |||
    Wizaplace -> Wizaplace: Sends emails notification to \n-Buyer\n-Vendor\n-Marketplace Admin
    Wizaplace --> LemonWay: Sends notification response
deactivate Wizaplace
deactivate LemonWay
@enduml
