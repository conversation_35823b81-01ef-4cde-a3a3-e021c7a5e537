@startuml
'
'Common classes
'
abstract class AbstractPaymentLogger implements LoggerInterface {
    # dataAnonymizer: DataAnonymizer
    # logger: LoggerInterface
    -- Methods --
    + logRequestSuccess(): void
    + logRequestError(): void
    + logRequestException(): void
    + log(): void
    + start(): void : calls startEvent() of EventDurationTrait
    - addEndEventData(): void : calls endEvent() of EventDurationTrait to add to the log data
    -- Setter --
    + setDataAnonymizer() : sets the DataAnonymizer, called from each PSP logger
}
AbstractPaymentLogger *-- EventDurationTrait
AbstractPaymentLogger o-- DataAnonymizer

class EventDurationTrait {
    # startEvent(): void
    # endEvent(): void
}

class DataAnonymizer {
    # {static} dataToAnonymize: array
    # {static} contextToAnonymize: array
    + {static} anonymizeData(): ?string
    + {static} anonymizeIban(): ?string
    + {static} anonymizeToken(): ?string
    + {static} anonymizePersonalData(): ?string
    + findAndAnonymizeData(): array
    + getDataToAnonymize(): array
    + getContextToAnonymize(): array
}

'
'PSP
'
class PSPLogger1 extends AbstractPaymentLogger {}

class PSPLogger2 extends AbstractPaymentLogger {}

class PSPLoggerN extends AbstractPaymentLogger {}

class PSPDataAnonymizer1 extends DataAnonymizer {}

class PSPDataAnonymizer2  extends DataAnonymizer {}

class PSPDataAnonymizerN  extends DataAnonymizer {}
@enduml
