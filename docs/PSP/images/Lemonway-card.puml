@startuml
title Card payment

participant "User Agent " as UA
participant "Marketplace \nfront office " as Front
participant "Wizaplace API " as Wizaplace
participant "LemonWay API " as LemonWay

UA -> Front: Chooses card payment method
activate UA
    activate Front
        Front -> Wizaplace: Requests card payment
        activate Wizaplace
            Wizaplace -> LemonWay: Initializes card payment
            activate LemonWay
                LemonWay --> Wizaplace: Sends URL or token for card payment page
            deactivate LemonWay
            Wizaplace --> Front: Sends URL
        deactivate Wizaplace
        Front --> UA: Redirects to PSP payment page
    deactivate Front
    UA -> LemonWay: Follow redirection to payment form
    activate LemonWay
        LemonWay --> UA: Sends payment form page
    deactivate LemonWay
deactivate UA
|||
UA -> LemonWay: Posts card details
activate UA
    activate LemonWay
        LemonWay -> Wizaplace: Notifies Payment (IPN)
        hnote over Wizaplace : IPN treatment
        activate LemonWay
        activate Wizaplace
            Wizaplace -> LemonWay: Request more transaction details
            activate LemonWay
                LemonWay --> Wizaplace: Transaction details
            deactivate LemonWay
            |||
            opt With buyer identification
                Wizaplace -> LemonWay: Transfers credits from \nbuyer wallet to technical wallet
                activate LemonWay
                    LemonWay --> Wizaplace: Send transfer response
                deactivate LemonWay
            end
            |||
            Wizaplace -> Wizaplace: Manages order's payment
            |||
            Wizaplace --> LemonWay: Sends IPN response
        deactivate Wizaplace
        deactivate LemonWay
        |||
        LemonWay --> UA: Sends redirection to marketplace (API Payment notification)
    deactivate LemonWay
    UA -> Wizaplace: Follows redirection
    activate Wizaplace
        Wizaplace -> Wizaplace: <i>Same then above: </i>\n<i>IPN treatment</i>

        note right Wizaplace : Note: We process both payment notifications \n received from the IPN and the UA.\n\n The main goal is to validate \n the order if the UA is coming before the IPN.
        Wizaplace --> UA: Redirects to complete order page
    deactivate Wizaplace
    UA -> Front: Follow redirection
    activate Front
        Front --> UA: Sends complete order page
    deactivate Front
deactivate UA
@enduml
