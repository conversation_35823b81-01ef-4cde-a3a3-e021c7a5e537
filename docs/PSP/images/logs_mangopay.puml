@startuml
'
'MangoPay
'
class MangoPayLogger extends AbstractPaymentLogger {
    - const MONGOPAY_LOG_NAME = 'MangoPay API SDK'
    -- Methods --
    + requestSuccess(): void : calls logRequestSuccess of AbstractPaymentLogger
    + requestException(): void : calls logRequestException of AbstractPaymentLogger
    + requestWarning(): void : calls logRequestException of AbstractPaymentLogger
    -- Details --
    Each requestXXX() method calls the getters of MangoPayHTTPClient :
    1. getLatestRequestRestTool()
    2. getLatestResponse()

    Then from the MangoPay\Libraries\RestTool we got, calls its getters :
    1. GetRequestType()
    2. GetRequestUrl()
    3. GetRequestData()

    requestSuccess() method gets the properties from the MangoPay\Libraries\HttpResponse we got :
    1. ResponseCode
    2. Body
}

class MangoPayDataAnonymizer extends DataAnonymizer {
    List all MangoPay data to anonymize
}
AbstractPaymentLogger o-- DataAnonymizer

class MangoPayHTTPClient extends MangoPay\Libraries\HttpCurl {
    - latestRequestRestTool: MangoPay\Libraries\RestTool
    - latestResponse: MangoPay\Libraries\HttpResponse
    + Request(RestTool $restTool): MangoPay\Libraries\HttpResponse : calls Request of MangoPay\Libraries\HttpCurl
    + getLatestRequestRestTool(): MangoPay\Libraries\RestTool
    + getLatestResponse(): MangoPay\Libraries\HttpResponse
}
MangoPayHTTPClient o-- MangoPay\Libraries\RestTool
MangoPayHTTPClient o-- MangoPay\Libraries\HttpResponse

class MangoPay\Libraries\HttpCurl extends MangoPay\Libraries\HttpBase {
    + Request(RestTool $restTool): MangoPay\Libraries\HttpResponse
}

class MangoPay\Libraries\HttpBase {
    + Request(RestTool $restTool): MangoPay\Libraries\HttpResponse
}

class MangoPay\Libraries\RestTool {
    + GetRequestType(): RequestType (string)
    + GetRequestUrl(): string
    + GetRequestData(): array|string
}

class MangoPay\Libraries\HttpResponse {
    + ResponseCode: int
    + Body: string
}

class MangoPay {
    # api: MangoPay\MangoPayApi
    # mangoPayLogger: MangoPayLogger
    -- Details --
    For each MangoPay API request, calls $this->api->{object}->{action}()
    For example: $this->api->PayIns->Create($payin)
    The logs are done here.
    Request info are fetched with $this->api->getHttpClient()
}
MangoPay *-- ApiAwareTrait
MangoPay o-- MangoPay\MangoPayApi
MangoPay o-- MangoPayLogger

class MangoPay\MangoPayApi {
    - httpClient: MangoPay\Libraries\HttpBase (MangoPayHTTPClient)
    + getHttpClient(): MangoPay\Libraries\HttpBase (MangoPayHTTPClient)
}
MangoPay\MangoPayApi o-- MangoPayHTTPClient
@enduml
