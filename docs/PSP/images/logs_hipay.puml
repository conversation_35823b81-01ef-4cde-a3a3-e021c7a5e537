@startuml
'
'HiPay
'
class HiPayLogger extends AbstractPaymentLogger {
    - const HIPAY_LOG_NAME = 'HiPay API SDK'
    + requestSuccess(): void : calls logRequestSuccess of AbstractPaymentLogger
    + requestException(): void : calls logRequestException of AbstractPaymentLogger
}

class HiPayWalletLogger extends AbstractPaymentLogger {
    - const HIPAY_WALLET_LOG_NAME = 'HiPay API Wallet'
    + requestSuccess(): void : calls logRequestSuccess of AbstractPaymentLogger
    + requestException(): void : calls logRequestException of AbstractPaymentLogger
}

class HiPayDataAnonymizer extends DataAnonymizer {
    List all HiPay data to anonymize
}
AbstractPaymentLogger o-- DataAnonymizer

class HiPayHTTPClient {
    - hiPayLogger: HiPayLogger : used in doRequest
    -- Methods --
    + doRequest(): HiPay\Fullservice\HTTP\Response\Response : calls doRequest() of SimpleHTTPClient
    -- Details --
    The logs are done in the doRequest() method
}
HiPayHTTPClient o-- HiPayLogger

class HiPay\Fullservice\HTTP\SimpleHTTPClient {
    + doRequest(): HiPay\Fullservice\HTTP\Response\Response
}
HiPay\Fullservice\HTTP\SimpleHTTPClient <|-- HiPayHTTPClient

class HiPay {
    # api: GatewayClient
    Calls the HiPay API: $this->api->getClientProvider()->request()
    With getClientProvider() returning the HiPayHTTPClient
}
HiPay *-- ApiAwareTrait
HiPay o-- HiPay\Fullservice\Gateway\Client\GatewayClient

class HiPay\Fullservice\Gateway\Client\GatewayClient {
    # _clientProvider: HiPay\Fullservice\HTTP\ClientProvider (HiPayHTTPClient)
    + getClientProvider: HiPay\Fullservice\HTTP\ClientProvider (HiPayHTTPClient)
}
HiPay\Fullservice\Gateway\Client\GatewayClient -- HiPayHTTPClient : < getClientProvider

class HiPayWalletApi {
    - client: GuzzleHttp\Client
    - hiPayWalletLogger: HiPayWalletLogger
    -- Details --
    For each Wallet API request, calls $this->client->request().
    The logs are done here.
}
HiPayWalletApi o-- HiPayWalletLogger
HiPayWalletApi o-- GuzzleHttp\Client

class GuzzleHttp\Client {
    + request: Psr\Http\Message\ResponseInterface
}
@enduml
