@startuml

title MangoPay Bank Wire \n

autonumber

participant "User Agent" as UA
participant "Marketplace \nFront Office" as Front
participant "Wizaplace \nBackend" as Backend
participant "MangoPay API" as PSP

    UA -> Front: Select MangoPay bankWire
activate UA
activate Front
    Front -> Backend: Ask for create order: \n"Checkout" API endpoint
activate Backend
    Backend -> Backend: Create Order (Payment in progress)
    Backend -> PSP: Create or gets wallet
activate PSP
    PSP -> Backend: send wallet details
deactivate PSP
    Backend -> Front: Send bankWire details
deactivate Backend
    Front -> UA: Send bankWire details page
deactivate Front
deactivate UA

== Buyer does the bank transfer to MangoPay ==

    PSP -> Backend: Send  bank wire money notification
activate PSP
activate Backend
    Backend -> PSP: Request more transaction details
    PSP -> Backend: send transaction details
deactivate PSP
    Backend -> Backend: Manage order payment
    Backend -> Backend: Send emails to \n -Buyer \n -Vendor \n -Marketplace Admin
activate PSP
    Backend -> PSP: Send notification response
deactivate PSP
deactivate Backend
@enduml

