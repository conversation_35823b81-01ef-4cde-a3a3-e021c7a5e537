@startuml

title

    SEPA (Direct Debit) Lemonway - Sequence Diagram
    Immediate or deferred
    The user doesn't have a SEPA mandate

endtitle

!include ./../General/_participants-list.puml
UA -> Front: Selects SEPA Direct Debit
activate UA
    activate Front
        Front -> Backend: Does the user has a mandate? \n[GET] /user/mandates API endpoint
        activate Backend
            Backend -> Backend: Load the user's mandates
            alt The user has a mandate
                Backend -> PSP: Asks PSP to check if the token is valid
            activate PSP
                PSP -> Backend: Token is invalid
            deactivate PSP
            end
            Backend --> Front: No: return an empty list of mandates
            deactivate Backend
            Front --> UA: Generates a form and sends the page
    deactivate Front

UA -> Front: Submits the form
activate Front
Front -> Backend: Creates the mandate\n[POST] /user/mandates API endpoint
    activate Backend
            alt The user doesn't have a wallet
                Backend -> PSP: Calls the Lemonway API /RegisterWallet
            activate PSP
                PSP -> PSP: Creates new technical wallet
                PSP -> Backend: Sends confirmation response (OK)
            deactivate PSP
            end
        Backend -> PSP: Get user technical wallet
        activate PSP
            PSP -> PSP: Load wallet informations
            PSP -> Backend: Sends wallet informations
        deactivate PSP
        alt The user doesn't have a mandate
            Backend -> PSP: Calls the Lemonway API /RegisterSddMandate
            activate PSP
                PSP -> PSP: Creates mandate
                PSP -> Backend: Sends confirmation response
                Backend -> Backend: Save the user's mandate information
            deactivate PSP
        end
        Backend -> Backend: Get user's mandate informations
        alt The user doesn't have the electronic signature
            Backend -> PSP: Calls the Lemonway API /SignDocumentInit
            activate PSP
                PSP -> PSP: Initialization of the electronic signature
                PSP -> Backend: Sends confirmation response
                Backend -> Backend: Save lemonway token and \n Authorise paiement
            deactivate PSP
        end



        Backend  --> Front: Return status
    deactivate Backend
@enduml
