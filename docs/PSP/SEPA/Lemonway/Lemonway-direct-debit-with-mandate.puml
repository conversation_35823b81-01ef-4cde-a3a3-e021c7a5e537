@startuml

title

    SEPA (Direct Debit) Lemonway - Sequence Diagram
    Immediate or deferred
    The user have a SEPA mandate

endtitle

!include ./../General/_participants-list.puml

UA -> Front: Selects SEPA Direct Debit
activate UA
    activate Front
        Front -> Backend: Does the user have a mandate? \n[GET] /user/mandates API endpoint
        activate Backend
            Backend -> Backend: Load the user's mandates
            Backend --> Front : Yes: return a list of one mandate
        deactivate Backend


!include ./../General/_direct-debit-processing-with-token.puml
@enduml
