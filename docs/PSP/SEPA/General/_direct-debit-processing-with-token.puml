@startuml

    Front -> Backend: Asks for create order: \n"Checkout" API endpoint
    activate Backend
        Backend -> Backend: Create Order
        activate PSP
            alt Transaction is immediate
                Backend -> PSP: Calls the PSP API
                PSP -> PSP: Ask for payment. The payment itself \nwill be processed later.
                PSP --> Backend: Sends confirmation response (OK)
                Backend -> Backend: Updates order
            end
        deactivate PSP
        Backend  --> Front: Return the order id
    note right Front: Note that the front does what it wants\nfrom this point (redirection).
deactivate Backend
deactivate Front
@enduml
