@startuml

title

    SEPA (Direct Debit) PSP - Sequence Diagram
    Payment is due

endtitle

participant "Wizaplace \nBackend" as Backend
participant "PSP API" as PSP


activate PSP
    PSP -> PSP: Processes payment
    PSP -> Backend: Sends payment status
    activate Backend
    Backend -> PSP: Asks the PSP the transaction\n data to make sure they match (security)
    PSP -> Backend: Returns the transaction data
deactivate PSP
    Backend -> Backend: Updates order
    opt on payment failure
        Backend -> Backend: Sends emails notification to:\n- Marketplace admin\n- Buyer\n- Vendor
    end
deactivate Backend

@enduml
