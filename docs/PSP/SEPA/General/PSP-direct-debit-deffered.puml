@startuml

title

    SEPA (Direct Debit) - Sequence Diagram
    Deferred payment transaction creation

endtitle

participant "Wizaplace \nBackend" as Backend
participant "PSP API" as PSP


activate Backend
    Backend -> Backend: CRON: check for orders for which deferred payment is due
    opt Payment is due
        activate PSP
            Backend -> PSP: Calls the PSP API
            PSP -> PSP: Ask for payment. The payment itself \nwill be processed later.
            PSP --> Backend: Sends confirmation response (OK)
        deactivate PSP
        Backend -> Backend: Updates order
    end
deactivate Backend

@enduml
