@startuml

title

    SEPA (Direct Debit) Hipay - Sequence Diagram
    Immediate or deferred
    The user doesn't have a token (No SEPA mandate) or token is invalid

endtitle

!include ./../General/_participants-list.puml

UA -> Front: Selects SEPA Direct Debit
activate UA
    activate Front
        Front -> Backend: Does the user has a mandate? \n[GET] /user/mandates API endpoint
        activate Backend
            Backend -> Backend: Load the user's mandates
            alt The user has a mandate
                Backend -> PSP: Asks PSP to check if the token is valid
            activate PSP
                PSP -> Backend: Token is invalid
            deactivate PSP
            end
            Backend --> Front: No: return an empty list of mandates
            deactivate Backend
            Front --> UA: Generates a form and sends the page
    deactivate Front

UA -> Front: Submits the form
activate Front
Front -> Backend: Creates the mandate\n[POST] /user/mandates API endpoint
    activate Backend
        Backend -> PSP: Calls the Hipay API
        activate PSP
            PSP -> PSP: Creates mandate
            PSP --> Backend: Sends confirmation response (OK)
        deactivate PSP
        Backend  --> Front: Return status
    deactivate Backend

!include ./../General/_direct-debit-processing-with-token.puml
@enduml
