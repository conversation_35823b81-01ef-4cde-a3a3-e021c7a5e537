#SEPA (Direct Debit)

## Fonctionnement

La norme SEPA est un système de paiement par prélèvement bancaire.
Il peut être soit immédiat, soit différé.
Pour pouvoir faire un prélèvement, l'acheteur doit valider une autorisation : un mandat SEPA.
Techniquement ce mandat se matérialise sous la forme d'un token d'autorisation que nous stockons dans la base de données,
et que nous réutilisons si l'utilisateur fait un nouvel achat.

Cependant la notion de paiement immédiat peut être trompeuse : l'application va en fait faire une demande
de paiement que le PSP réalisera au plus tôt, mais pas de manière synchrone.
 Ainsi le workflow classique d'un paiement par SEPA est le suivant :
 * que le paiement soit immédiat ou différé : une demande de paiement est faite au PSP qui en accuse réception ;
 * plus tard, quand le PSP réalise le prélèvement, il va contacter notre application via une URL pour l'informer de la
 réussite ou non de l'opération. En cas d'erreur, nous pourrons alors notifier le vendeur et l'acheteur par email.

La seule différence se fait ici : si le paiement est immédiat, nous demandons immédiatement au PSP de faire un prélèvement,
alors que pour ceux en différé, nous avons une tâche CRON qui va chercher les commandes dont le paiement est échu,
 et pour chacune d'entre-elles, faire une demande de paiement au PSP.

 Dans les deux cas, le paiement est fait quand le PSP le décide.

 ## Particularités de notre implémentation

 La validation d'un panier se fait via notre route API _**checkout**_ qui va transformer un panier en commande
 et retourner l'id de la commande, avec parfois du HTML (voir la doc de ce endpoint pour plus d'infos).

### Stripe
 Le premier PSP que nous avons implémenté pour le support de SEPA est _**Stripe**_.
 Lors de la validation du panier (route checkout), deux possibilités se présentent :
 * soit l'utilisateur a déjà un mandat enregistré, et alors la demande de prélèvement est immédiatement faite à Stripe (si paiement immédiat) et l'api
 ne retourne alors que l'id de la commande ;
 * soit il n'a pas de mandat et alors la route checkout de notre API va retourner un formulaire en plus de l'id de la commande.
 Ce formulaire est complet, il comprend l'URL de soumission, route spécifique (_payment_notification_stripe_sepa_init_) conçue pour créer un paiement SEPA Stripe,
 route qui est donc dédiée mais surtout non documentée.
 Lors de la soumission du formulaire, le contrôle est fait par JavaScript. Dans un premier temps un appel Ajax est fait à Stripe pour créer et récuperer le token,
 et dans la foulée le formulaire est soumis au back-end de Wizaplace, via la route que nous venons de mentionner.

 Actuellement un seul client utilise ce PSP avec SEPA : **Warmango**.

### Hipay et suivants

L'implémentation de Hipay avec le support SEPA a été l'occasion de repenser le système mis en place pour Stripe et de prévoir l'arrivée du support
SEPA pour d'autres PSP chez Wizaplace. Le fonctionnement de Stripe n'a pas été modifié pour ne pas créer de BC.
Voici le principe du fonctionnement (voir aussi les diagrammes de séquence)
* l'utilisateur choisit le prélèvement par SEPA Hipay. La route checkout transforme le panier en commande (comportement normal)
mais puisque nous utilisons SEPA Hipay, elle retourne un formulaire *partiel* dédié, spécifique à Hipay avec SEPA ;

* ce formulaire est intégré par l'application front. La soumission doit être faite par le front à nouvelle route API que nous
avons créée, qui accepte différentes payloads en fonction du PSP. Cette route, documentée, a pour but de créer le mandat ;

* le statut de la création est retourné. Le front doit alors, de manière transparente, contacter une autre route de notre API
, également créée pour l'occasion, qui a pour rôle de faire une demande de paiement par mandat SEPA. Il devra passer le basket ID
et s'assurer que l'utilisateur est authentifié. Un statut est retourné, et le front est libre de faire ce qu'il souhaite à partir de là.

Le fonctionnement du cas où l'utilisateur a déjà un mandat est identique à Stripe.




