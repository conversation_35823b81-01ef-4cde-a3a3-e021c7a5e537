@startuml

title

    SEPA (Direct Debit) PSP - Sequence Diagram
    Immediate or deferred
    The user doesn't have a token (No SEPA mandate)

endtitle

!include ./../General/_participants-list.puml

UA -> Front: Selects SEPA Direct Debit
activate UA
    activate Front
        Front -> Backend: Asks for create order: \n"Checkout" API endpoint
        activate Backend
            Backend -> Backend: Create Order
            Backend --> Front : Returns the full SEPA form
            deactivate Backend
            Front --> UA: Integrate form and sends IBAN page
        deactivate Front
    deactivate UA

|||

UA -> PSP: Submits the form (JavaScript Stripe SDK)
activate UA
    activate PSP
        PSP -> PSP: Create mandate
        PSP --> UA: Returns status with mandate token
    deactivate PSP
    UA -> Backend: POST to Stripe SEPA init route
    activate Backend
        Backend -> PSP: Calls the Stripe API
        activate PSP
            PSP -> PSP: Ask for payment. The payment itself \nwill be processed later.
            PSP --> Backend: Sends confirmation response (OK)
        deactivate PSP
        Backend -> Backend: Updates order
        Backend --> UA: Returns redirect URL
    deactivate Backend
    UA -> Front: Follows redirect
    activate Front
        Front --> UA: Returns confirmation page
    deactivate Front
@enduml
