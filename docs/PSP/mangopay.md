# Mangopay

## Backoffice

Demander la création d'un accès au BO à quelqu'un possédant un compte admin.

(Dans `wizaplace > Account > Team access`)

Accès au backoffice : https://api.sandbox.mangopay.com/authorize?response_type=code&client_id=mangodashboardapp&redirect_uri=https://dashboard.sandbox.mangopay.com/Authorize/SignIn

## Webhooks, Notification de paiement

(Sur un poste de Dév., utiliser [ngrok](https://ngrok.com/) pour se rendre visible à mangopay)

Aujourd'hui (15/02/2019) nous ne traitons que les notifications de PayIn, aucune autre interaction avec le PSP n'entraine de notification/validation sur notre système :(

Réglage du Hook PayIn Succeeded :

- en CB : https://XXXXX.ngrok.io/payment-notification/mangopay-card
- en Virement (Bankwire) : https://XXXXX.ngrok.io/mangopay-hook

**Merci de désactiver les webhooks que vous n'utilisez plus après vos développements : une grande quantité d'emails d'alerte inutiles sont envoyés lorsque les urls renseignées ne répondent plus.**

# Mangopay et Paiement bancaires
​
Suite au ticket : [4402](https://wizaplace.atlassian.net/browse/WIZ-4402),
  Les transactions bancaires sur Mangopay sont initialisées dans la méthode `createBankwireTransaction` de la classe `MangoPay.php`.
​
Dans cette méthode on initialise également le hook pour l'évènement `PAYIN_NORMAL_SUCCEEDED` via la méthode `setupPayinSucceededHook`
​
# Paiement par Virement

### Méthode `startPayment` de MangopayBankwireProcessor.php
~~~php
public function startPayment(int $orderId, string $redirectUrl = null, string $cssUrl = null): PaymentResponse
~~~
- Création de la transaction
​
### Méthode `createBankwireTransaction` de Mangopay.php
​
~~~php
public function createBankwireTransaction(int $orderId, string $redirectUrl = null): array
~~~
​
- En premier lieu on encode l'url de redirection finale du client.
- Setup du Hook
- Récupération de l'order et de ses enfants
- Récupération du wallet du client
- Instanciation de la classe `PayIn` et injection des informations nécessaires pour créer la transaction dans `PayInPaymentDetailsBankWire`
- Création de la requête, envoi puis récupération de la réponse
- Si la réponse est `STATUS_CREATED` ou `STATUS_SUCCEEDED` on continue
- On remplit quelques données dans des variables (qui seront dans le tableau suivant) et qui seront affichées au client
- On construit l'url de redirection qui contient l'url encodée précédemment
- On sauvegarde les informations de la transaction `saveBankWireTransaction`

Tableau retourné :
~~~php
return [
   'IBAN' => $bankDetails->IBAN,
   'BIC' => $bankDetails->BIC,
   'WireReference' => $paymentDetails->WireReference,
   'OwnerName' => $bankAccount->OwnerName,
   'OwnerAddress' => $address,
   'completeUrl' => $callbackUrl,
   'amount' => $order->getCustomerTotal(),
];
~~~
L'url de redirection/confirmation :
~~~php
payment_notification_transfer_standby:
   path: /payment-notification/transfer-standby
   defaults: { _controller: AppBundle:PaymentNotification:markBankTransferIsDone }
   schemes:  [https] # force URL generation with https:// to prevent redirection
~~~
​
### Méthode `markBankTransferIsDoneAction` de `PaymentNotificationController`
​
~~~php
/**
* Est appelée par le client, quand il a fait le virement bancaire du montant indiqué aux coordonnées indiquées,
* il clique sur le bouton embarqué dans le HTML, qui le renvoit ici.
* Ici on passe simplement la commande en attente de paiement.
*/
~~~
Tout est dit dans le commentaire, et finalement le client sera redirigé sur l'url finale.
​
### Méthode `setupPayinSucceededHook` de Mangopay.php
​
Crée simplement le hook indépendamment de la transaction.
​
~~~yml
mangopay_hooks:
   path: /mangopay-hooks
   defaults: { _controller: AppBundle:PaymentNotification:mangopayBankwire }
   schemes:  [https] # force URL generation with https:// to prevent redirection
   methods: [GET]
~~~
​
### Méthode `mangopayBankwireAction` de `PaymentNotificationController`
~~~php
/**
* Est appelé par les serveurs de Mangopay quand un virement bancaire est créé de leur coté.
*/
~~~

## Diagrams

![](./Bank_Wire/MangoPay/images/mango_pay_bank_wire.png)

​
# Paiement par Carte
​
Ca commence par `startPayment` dans `MangopayCardProcessor` qui appelle `createCardTransaction` dans `Mangopay.php`
​
`createCardTransaction` créé la transaction et return l'url du formulaire créé par Mangopay et qui contiendra beaucoup d'informations dont l'url de redirection :
​
~~~yml
payment_notification_mangopay_card:
   path: /payment-notification/mangopay-card
   defaults: { _controller: AppBundle:PaymentNotification:mangopayCard }
   schemes:  [https] # force URL generation with https:// to prevent redirection
~~~

Avant d’être redirigé sur l'url finale, le client passera par `mangopayCardAction` dans `PaymentNotificationController`.

Commentaire de la méthode :
~~~yml
/**
* Est appelée par le client quand il revient de la page de paiement Mangopay
*/
...
/*
* MangoPay uses transactionId when the user's browser is redirected,
* but they use RessourceId when they call a hook (e.g. the payment didn't succeed after a while).
* And we anticipate the typo fix.
*
* @see https://docs.mangopay.com/endpoints/v2.01/hooks#e246_the-hook-object
* Note that in the URL parameters, RessourceId has two "s", and not one as in the API objects - this is a mistake and will be corrected in a future API version
*/
~~~
​
Dans cette méthode on questionne Mangopay sur le statut de la transaction et on modifie le statut de l'order en fonction.

## Cartes de test

[Voir la documentation en ligne](https://docs.mangopay.com/guide/testing-payments)

# Wizaplace

## .ENV

Réglage en sandbox :

```env
# MANGOPAY sandbox
MANGOPAY_API_CLIENT_ID=wizaplace
MANGOPAY_API_CLIENT_PASSWORD=fb2sgGWYWY9KDJqrEDqSGCkuT0zoYj3AeSYY8wLq9oMQ4Z5FBj
MANGOPAY_API_BASE_URL=https://api.sandbox.mangopay.com
MANGOPAY_API_SECURE_MODE=FORCE
MANGOPAY_USER_ID_AGENT_ETABLISSEMENT_PAIEMENT=
```

Une deuxième sandbox est disponible avec les paramètres suivants :

```env
MANGOPAY_API_CLIENT_ID=wizaplace2
MANGOPAY_API_CLIENT_PASSWORD=FfQNp03wtO46GSop73sLqoseabgxytADXywUSWC4Lhb37Nb74k
```

Sur cette dernière, la connexion au BO se fait avec les identifiants suivants :

- <EMAIL>
- 123Wizaplace!

Explications :
- `MANGOPAY_SECURE_MODE`:

  - `FORCE` (par défaut) : 3D secure activé dans tout les cas, **ATTENTION** le client doit avoir souscrit à l'option 3D secure auprès de Mangopay, sinon régler la variable d'env ainsi : `MANGOPAY_SECURE_MODE=DEFAULT`.
  - `DEFAULT` : 3D secure activé uniquement pour les paiements > 50€, ou désactivé si l'option ne fait pas partie du contrat du client.

#KYC

L'envoi des KYC chez MangoPay présente un fonctionnement assez particulier.
Nous devons créer le document chez eux, ajouter les binaries à celui-ci et le soumettre pour validation.

À ce jour, nous renvoyons systématiquement toutes les KYC stockées dans le BO Wizaplace chez Mangopay.

Exemple :
* Ajout d'un premier fichier = un fichier chez Mangopay
* Ajout d'un deuxième fichier = trois fichiers chez Mangopay (2x le premier document + 1x le deuxième document)

En vérifiant les KYC sur le BO Mangopay, il est normal d'avoir plusieurs fois la même KYC.
Cela ne pose pas de problème car lorsque Mangopay valide une KYC, elle fait office de KYC valide pour sa catégorie.

## Le saviez-vous ?

- Aujourd'hui (15/03/2019) une commande `src/AppBundle/Command/ClearPaymentDataBeforeProductionCommand.php` permet l'effacement de l'intégralité des IDs d'utilisateur chez les différents PSP (mangopay_id, hipay_id, stripe_id):

```bash
bin/console payment:clear
```

# Logs

Pour loguer les requêtes envoyées à MangoPay, on a besoin de récupérer les infos adéquates.

Pour ce faire, un client `Wizacha\Marketplace\Payment\HiPay\MangoPayHTTPClient` amène une surcouche sur le client HTTP de MangoPay `MangoPay\Libraries\HttpCurl`, notamment sa fonction `Request`.
Le but est ainsi de garder de côté les 2 objets contenant les infos de la dernière requête à loguer : `MangoPay\Libraries\RestTool` et `MangoPay\Libraries\HttpResponse`.
En effet MangoPay ne nous permet pas de tout connaître à ce moment précis. L'objet RestTool dont on dispose ici sera alimenté dans la fonction parente (notamment les paramètres du request body).
On ne peut donc s'en servir complètement qu'une fois la requête terminée.

Ensuite dans `Wizacha\Marketplace\Payment\Processor\MangoPay` où on fait appel aux fonctions de l'API, on est capable de loguer les infos gardées de côté dans notre client HTTP.

C'est ainsi ici qu'on a besoin d'un logger spécifique à MangoPay `Wizacha\Marketplace\Payment\Mangopay\MangoPayLogger` qui étend de `Wizacha\Marketplace\Payment\AbstractPaymentLogger`.
Il est nécessaire pour prendre en compte les types des variables qu'on va passer au logger principal.
Par exemple, avec le `RestTool` on va y récupérer `GetRequestUrl` et `GetRequestData`.

## Anonymisation

Un anonymizer spécifique à MangoPay liste les données à anonymiser :

`Wizacha\Marketplace\Payment\Security\MangoPayDataAnonymizer` qui étend de `Wizacha\Marketplace\Security\DataAnonymizer\DataAnonymizer`.

## Schéma

![logs_mangopay.png](./images/logs_mangopay.png)

## A voir aussi

* [Logs](./logs.md) : Eléments communs des logs des PSP
* [Événements Logs PSP Kibana - Mangopay API](https://wizaplace.atlassian.net/wiki/spaces/WI/pages/2876473499/v+nements+Logs+PSP+Kibana+-+Mangopay+API) : La liste complète des logs et des données anonymisées
