# Stripe

## Backoffice

Créer un compte développeur sur http://stripe.com

Les clefs API sont disponible dans :
`Developers > API keys`

> **Attention** : avec un compte non activé vous ne pourrez pas effectuer
> de virements SEPA. Récuperez les identifiants du compte développeur Wizaplace pour cela.
> Voir aussi la documentation [PSP - Comptes de test](https://docs.google.com/document/d/1V50RN3Zu0tZmFNmZyuHKEqGkuxdy5FfMICc-vUPc1PU/edit?ts=5d89c630#heading=h.eu73s1oq9d0u).

# Wizaplace

## .ENV

Réglage en sandbox:

```env
# STRIPE sandbox
STRIPE_PUBLIC_TOKEN=pk_test_oszGqjCRi0OobHZorqoO3a7e
STRIPE_SECRET_TOKEN=sk_test_iOG9qe1C7mb1avCt6IWuHkYG
```

```env
# STRIPE sandbox
STRIPE_PUBLIC_TOKEN=pk_test_9CPZgJzllZqZgNK1NqL0OkbS00xnXNM1FX
STRIPE_SECRET_TOKEN=sk_test_H2SX3XjUCJvPGCqAsEyjyvBl000KJ9iUQu
```

## Version API
Le numéro de version de l'API Stripe est paramètrable en modificant la constante `StripeConfig::API_VERSION`.

Les montées en version de l'api nécessite forcément une évolution du code, la version doit être une constante afin d'être versionée.

## SEPA Direct et A échéance

Le client passe une commande sur le front et sélectionne l'un des deux modes de paiement.

Une fois la commande passée, le front va récupérer les informations de la commande et plus précisément le mode de
paiement utilisé.
En fonction du mode il va faire ou non l'appel au endpoint `/v1/user/orders/{id}/commitment` qui permet de saisir une
date de prélèvement pour la commande.

1. Paiement a échéance

Cas Warmango : juste avant d'appeler ce endpoint, le front va intérroger l'API Wizaforce de Warmango pour connaître la
date de prélèvement.
Une fois cette date récupérée, il l'envoi via le endpoint.

2. Paiement Direct

Pour ce mode, la date passée est la date courante +1 minute. Cette durée n'est pas configurable, elle est définie en dur
dans le  code.
Ce mode est donc un paiement à échéance mais avec une date de prélèvement instantanée.

Par la suite, toutes les heures, un CRON exécute la commande `bin/console orders:pay-payment-deferment-orders`
qui va récupérer toutes les commandes en paiement à échéance et si la date du paiement est inférieure à la date
d'exécution du CRON, alors un ordre de prélèvement est envoyé à Stripe.

Quand Stripe reçoit le paiement de la part du client, il nous envoi une notification via le webhook qui pointe sur le
endpoint `/payment-notification/stripe-sepa`.

**Attention**, a ne pas confondre avec `/payment-notification/stripe-sepa-init` qui permet de récupérer la vue où
l'utilisateur va renseigner son IBAN et signer le mandat de prélèvement Stripe.

## Paiement par Carte

## Diagrams

![](./Card/Stripe/images/stripe_card.png)

![](./Card/Stripe/images/processingResponse.png)

# Logs

Pour loguer les requêtes envoyées à Stripe, on a besoin de récupérer les infos adéquates.

Pour ce faire, un client `Wizacha\Marketplace\Payment\Stripe\StripeHTTPClient` amène une surcouche sur le client HTTP de Stripe `Stripe\HttpClient\CurlClient`, notamment sa fonction `request`.
Le but est ainsi de garder de côté les informations relatives à la dernière requête à loguer :
- `$latestRequestMethod`
- `$latestRequestUrl`
- `$latestRequestParams`
- `$latestResponseBody`
- `$latestResponseCode`

En effet Stripe nous devons loguer à un niveau au-dessus pour catcher des exceptions.
On ne peut donc s'en servir complètement qu'une fois la requête terminée en-dehors du client HTTP.

Ensuite dans `Wizacha\Marketplace\Payment\Stripe\StripeApi` où on fait appel aux fonctions de l'API on est capable de loguer les infos gardées de côté dans notre client HTTP.
On doit utiliser la fonction static `setHttpClient` de `Stripe\ApiRequestor` pour écraser le client HTTP avec le nôtre.
Lors de chaque requête, Stripe va créer une nouvelle instance de ApiRequestor qui va retrouver sa propriété static `$_httpClient` déjà settée.

On a ainsi besoin dans `StripeApi` d'un logger spécifique à Stripe `Wizacha\Marketplace\Payment\Stripe\StripeLogger` qui étend de `Wizacha\Marketplace\Payment\AbstractPaymentLogger`.
Il est nécessaire pour prendre en compte les types des variables qu'on va passer au logger principal.
Chaque fonction de log prend ainsi l'instance de `StripeHTTPClient` contenant les informations de la requête en cours.
On va donc y récupérer tous les `getLatestXXX` à passer au logger principal.

## Anonymisation

Un anonymizer spécifique à Stripe liste les données à anonymiser :

`Wizacha\Marketplace\Payment\Security\StripeDataAnonymizer` qui étend de `Wizacha\Marketplace\Security\DataAnonymizer\DataAnonymizer`.

## Schéma

![logs_stripe.png](./images/logs_stripe.png)

## A voir aussi

* [Logs](./logs.md) : Eléments communs des logs des PSP
* [Événements Logs PSP Kibana - Stripe API](https://wizaplace.atlassian.net/wiki/spaces/WI/pages/2908422220/v+nements+Logs+PSP+Kibana+-+Stripe+API) : La liste complète des logs et des données anonymisées
