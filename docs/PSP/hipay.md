# HiPay

## Backoffice

Il semblerait qu'il existe 2 dashboards, un pour le PayIn et un pour le PayOut.

Demander la création d'un accès au BO à quelqu'un possédant un compte admin.

(Dans `compte > gestion des utilisateurs`)

Accès au backoffice : http://stage-merchant.hipay-tpp.com/default/auth/login

Login : <EMAIL>
Password : wizaplaceAdmin2018!

## Webhooks, Notification de paiement

(Sur un poste de Dév., utiliser [ngrok](https://ngrok.com/) pour se rendre visible à HiPay)

Aujourd'hui (19/02/2019) nous ne traitons que le status `HipayTransactionStatus::AUTHORIZED` lors d'un PayIn, aucune autre interactions avec le PSP n'entraine de notification/validation sur notre système.

Réglage de la notification de PayIn: ex.: https://XXXXX.ngrok.io/payment-notification/hipay-card

N.B. : URL de callback de notification de paiement CB récupérable dans le BO wizaplace dans `Administration > Modes de paiement > hipay > configurer`, par ex pour alexandbox : `https://back.alexandbox.sandbox.wizaplace.com/payment-notification/hipay-card`

Pour modifier le webhook il faut aller dans `Intégration > Notifications > URL de notification`

## Cartes de tests

https://support.hipay.com/hc/fr/articles/213882649-Comment-tester-les-m%C3%A9thodes-de-paiement-

# Wizaplace

## .ENV

Réglage en sandbox:

```env
# HIPAY sandbox
HIPAY_CASHIN_API_USERNAME=94679884.stage-secure-gateway.hipay-tpp.com
HIPAY_CASHIN_API_PASSWORD=Test_sQX3Cb6G8yqCAR5C0H7Ns267
HIPAY_CASHIN_ENV=stage
HIPAY_CASHOUT_API_URI=https://test-merchant.hipaywallet.com/api/
HIPAY_CASHOUT_API_LOGIN= 9fb515c056d61469868cfcf3f97935b1
HIPAY_CASHOUT_API_PASSWORD= 776aae5aecbc786a60c7f7ffe40d0bfc
HIPAY_CASHOUT_ENTITY=wizaplacev2
HIPAY_CASHOUT_MERCHANT_GROUP_ID=259
HIPAY_COMMISSION_RECIPIENT_ID=760950

HIPAY_PAYMENT_PRODUCT_LIST=visa,mastercard,cb,american-express
```

Réglage en production:

```env
# HIPAY sandbox
HIPAY_CASHOUT_API_URI=https://merchant.hipaywallet.com/api/
HIPAY_CASHIN_ENV=production
```

Variables :

`HIPAY_CASHOUT_ENTITY` : [ à définir ]

`HIPAY_CASHOUT_MERCHANT_GROUP_ID` : [ à définir ]

`HIPAY_COMMISSION_RECIPIENT_ID` : l'id du compte marchand bénéficiaire de la commission ? -> src/Marketplace/Payment/Processor/HiPay.php

```php
?string \$commissionRecipientId
```

# KYC

Lors de l'upload de KYC chez HiPay, chaque fichier doit être envoyé dans une requête POST différente.

HiPay associe les documents à différents types :

Documents à envoyer sur leur endpoint `/identification.json`

* Carte d'identité => type 1
* Carte d'identité de la personne physique => type 1
* Carte d'identité du représentant légal => type 1

> Les cartes d'identité doivent être recto-verso

* Justificatif de domicile  => type 2

* Kbis => type 4
* SIREN => type 4 (le SIREN est indiqué dans l'extrait KBIS. Il n'est pas necessaire de l'envoyer en plus)

* Derniers status à jours signés (identiques à ceux du greffe) => type 5

Documents à envoyer sur leur endpoint `/user-account/bank-info.json`

* RIB personnel => type 6
* RIB pro personne morale => type 6
* RIB pro personne physique => type 6

# Logs

Pour loguer les requêtes envoyées à HiPay, on a besoin de récupérer les infos adéquates.

Pour ce faire, un client `Wizacha\Marketplace\Payment\HiPay\HiPayHTTPClient` amène une surcouche sur le client HTTP de HiPay `HiPay\Fullservice\HTTP\SimpleHTTPClient`, notamment sa fonction `doRequest`.
Le but est ainsi d'ajouter les logs dans la surcouche de cette fonction où on a accès à toutes infos nécessaires.

Ce client a besoin d'un logger spécifique à HiPay `Wizacha\Marketplace\Payment\HiPay\HiPayLogger` qui étend de `Wizacha\Marketplace\Payment\AbstractPaymentLogger`.
Il est nécessaire pour prendre en compte les types des variables qu'on va passer au logger principal.
Par exemple, on récupère un objet response de type `HiPay\Fullservice\HTTP\Response\Response` => On y prend le `statusCode` et le `body`.

## Anonymisation

Un anonymizer spécifique à HiPay liste les données à anonymiser :

`Wizacha\Marketplace\Payment\Security\HiPayDataAnonymizer` qui étend de `Wizacha\Marketplace\Security\DataAnonymizer\DataAnonymizer`.

## Schéma

![logs_hipay.png](./images/logs_hipay.png)

## A voir aussi

* [Logs](./logs.md) : Eléments communs des logs des PSP
* [Événements Logs PSP Kibana - Hipay API](https://wizaplace.atlassian.net/wiki/spaces/WI/pages/2877161596/v+nements+Logs+PSP+Kibana+-+Hipay+API) : La liste complète des logs et des données anonymisées
