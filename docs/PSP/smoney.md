# SMoney

## Backoffice

Accès au backoffice : https://rest-pp.s-money.fr/api/backoffice/sandbox  
Identifiant : wizacha2-admin  
Password : 123456  

### KYC

Chez S-Money, la validation des KYC en environnement de sandbox
est faite manuellement par le support S-Money.

Pour cela, dans le back office S-Money, il faut aller dans le détail d'un compte marchand,
cliquer sur l'onglet « Demandes », puis « Ajouter une demande » de
type « Demande de KYC ».

Puis créer un ticket sur leur [ZenDesk](https://s-money.zendesk.com/hc/fr),
en mentionnant qu'il s'agit d'un environnement de Sandbox, ainsi que les informations suivantes
relatives au marchand que vous souhaitez valider :

  * Identifiant S-Money
  * AppUserId
  * Identifiant de la demande de KYC
  
Pour l'identification au système de tickets, il est possible de simplement
créer un nouveau compte avec une adresse `@wizacha.com`.

## Wizaplace

### .ENV

```env
# SMONEY sandbox
SMONEY_API_BASE_URL=https://rest-pp.s-money.fr/api/sandbox
SMONEY_WEBSITE_NAME=sandbox
SMONEY_TOKEN=NTs1NjM7flp2cU5PS3VJbg==
```

- SMONEY_WEBSITE_NAME : domaine partenaire pour l'API sandbox (ex.: POST /api/[domaine_partenaire]/users/[appuserid]/subaccounts HTTP/1.1)
