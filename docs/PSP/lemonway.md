# LemonWay

## Wallets and co
### Technical wallet
Un technical wallet est un wallet où transite l'argent (compte de cantonnement).
Dans le cas où `LEMONWAY_API_USE_BUYER_WALLET_FOR_MONEYIN` est à `false`, c'est-à-dire que l'on va utiliser le compte de la
MP `LEMONWAY_API_TECH_WALLET_ID`. Il est généralement nommé `MKP`.

Dans le cas où `LEMONWAY_API_USE_BUYER_WALLET_FOR_MONEYIN` est à `true`, on va créer un compte par client acheteur au moment de
l'achat. Une fois l'argent recu sur ce wallet, il sera immédiatement transféré sur le compte de cantonnement.

**Attention**: Pour utiliser le technical wallet, il faut avoir souscrit à l'option.

### MoneyIn sur un wallet validé
Il est possible de faire transiter l'argent en MoneyIn via un compte validé par des KYC, cela permet de faire transiter
des sommes plus importantes. Pour cela, il faut activer le paramètre `LEMONWAY_API_USE_BUYER_WALLET_FOR_MONEYIN` et utiliser les
admin de marchand comme client. Dans ce cas, on va utiliser le wallet du marchand, et non de l'admin de marchand pour
faire transiter les fonds du moneyIn.

### Wallet commission
Le wallet SC est le wallet qui sert à récupérer la commission lors du dispatch funds.
C'est aussi à partir de ce wallet que l'admin de la MP peut se payer.
Cette valeur est invariable, il n'est donc pas nécessaire de la mettre dans la configuration.

## Proxy pour les appels à l'API Lemonway
LemonWay bloque toutes les adresses IP sauf celles qui sont dans leur white-list. Le soucis c'est qu'il n'est pas
possible d'en ajouter par API. Le soucis est donc qu'en production, à chaque livraison, AWS nous fournit une nouvelle
adresse IP (plus facile en cas de rollback). Il est donc nécessaire de passer par un proxy pour exécuter les requêtes API.

## Backoffice

Accès au backoffice : https://sandbox-backoffice.lemonway.fr/wizaplace/tdb

- Login: adminmb
- Password: adminmb

## Cartes de crédit de tests

http://documentation.lemonway.fr/pages/viewpage.action?pageId=1540432

/!\ Certaines cartes ne fonctionnent plus à l'heure actuelle, il faut utiliser celle-ci :
`5017670000001800 (successful payment in 3D Secure)`

# Wizaplace

## .ENV

Réglage en sandbox:

```env
# LemonWay sandbox
LEMONWAY_API_CLIENT_LOGIN=adminmb
LEMONWAY_API_CLIENT_PASSWORD=adminmb
LEMONWAY_API_DIRECTKIT_URL=https://sandbox-api.lemonway.fr/mb/wizaplace/dev/directkitjson2/service.asmx
LEMONWAY_API_WEBKIT_URL=https://sandbox-webkit.lemonway.fr/wizaplace/dev
LEMONWAY_API_MARKETPLACE_ID=wizaplace
LEMONWAY_API_USE_BUYER_WALLET_FOR_MONEYIN=0
LEMONWAY_API_TECH_WALLET_ID=MKP
LEMONWAY_PROXY_HOST=proxy-tools.wizacha.com
LEMONWAY_PROXY_PORT=3128
LEMONWAY_PROXY_USER=wizaplace-dev
LEMONWAY_PROXY_PASSWORD=£4e5E6ywF;h9W,K
LEMONWAY_BANKWIRE_IBAN=***************************
LEMONWAY_BANKWIRE_BIC=AGFBFRCC
LEMONWAY_BANKWIRE_HOLDER_NAME=Wizaplace
LEMONWAY_BANKWIRE_HOLDER_ADDRESS=24 rue de la Gare, 69009, LYON
```

Les champs `BANKWIRE` peuvent être renseignés avec n'importe quelle valeur en dev. Cela est juste pour de l'affichage

## Notifications
Il existe deux hooks différents pour la CB et le virement :

- **CB** : `payment_notification_lemonway_card` => `/payment-notification/lemonway-card`
La notification sera reçue sur l'URL envoyée lors de l'initialisation de paiement
(Get via le navigateur et Post en serveur à serveur)

- **Virement** : `payment_notification_lemonway_bankwire` => `/payment-notification/lemonway-bankwire`

  Les notifications de virement sont configurées dans le BackOffice LW : `Outils / Configuration / Notifications / Paramétrage / MoneyIn : by wire received`

  En mode Sandbox, il est possible de simuler un virement reçu en allant sur la page du wallet concerné et de cliquer
  sur **Action compte** puis dans la page des notifications, cliquez sur **Process unsent notification**

## Diagrams

![](./images/Lemonway-card.png)
---
![](./images/Lemonway-bank-wire.png)

# Logs

Les logs des requêtes à LemonWay sont faits directement dans notre client HTTP `Wizacha\Marketplace\Payment\LemonWay\LemonWayHttpClient`, dans la fonction `sendRequest`.

Ici nous avons toutes les infos des requêtes à disposition.
On a ainsi besoin d'un logger spécifique à LemonWay `Wizacha\Marketplace\Payment\LemonWay\LemonWayLogger` qui étend de `Wizacha\Marketplace\Payment\AbstractPaymentLogger`.
Il est nécessaire pour prendre en compte les types des variables qu'on va passer au logger principal.

>Pour les réponses en erreur (code de retour différent de 2XX) on log la requête avec `logRequestError` et non `logRequestException`. `LemonWayLogger` a donc une fonction supplémentaire `requestError` pour traiter ces cas. `logRequestException` est utilisée pour tous les autres cas d'erreur.

## Anonymisation

Un anonymizer spécifique à LemonWay liste les données à anonymiser :

`Wizacha\Marketplace\Payment\Security\LemonWayDataAnonymizer` qui étend de `Wizacha\Marketplace\Security\DataAnonymizer\DataAnonymizer`.

## Schéma

![logs_lemonway.png](./images/logs_lemonway.png)

## A voir aussi

* [Logs](./logs.md) : Eléments communs des logs des PSP
* [Événements Logs PSP Kibana - LemonWay API](https://wizaplace.atlassian.net/wiki/spaces/WI/pages/2909405245/v+nements+Logs+PSP+Kibana+-+LemonWay+API) : La liste complète des logs et des données anonymisées
