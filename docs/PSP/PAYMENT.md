# Payment

Le module de paiement s'occupe de dialoguer avec les PSP (*Payment Service Provider*) pour gérer les paiements des commandes.

Exemples de PSP : Paypal, Stripe, Mangopay, HiPay, …

## E-Commerce vs Marketplace

Le workflow de paiement dans une marketplace est plus complexe que dans un site e-commerce classique, notamment parce que sur un marketplace on peut acheter des produits de plusieurs vendeurs via un seul achat :

- paiement e-commerce classique :
    - l'acheteur paie sur le PSP le montant de sa commande (opération **cash-in**)
    - le PSP prélève sa commission
    - le PSP stocke l'argent sur le compte du vendeur (l'administrateur du site e-commerce)
    - l'argent est périodiquement transféré sur le RIB du vendeur (opération **cash-out**)
- paiement marketplace :
    - l'acheteur paie sur le PSP le montant de sa commande (opération **cash-in**)
    - le PSP prélève sa commission (parfois cette étape arrive plus tard)
    - le PSP place l'argent sur un wallet (portefeuille) au nom de l'acheteur (l'acheteur n'est pas alerté et n'a pas réellement accès à ce wallet)
    - une fois la commande terminée, Wizaplace déclenche la *répartition des fonds* : l'argent est répartit dans des wallets au nom des vendeurs + un wallet au nom de la marketplace (qui sert à collecter la commission de la marketplace)
    - l'administrateur de la marketplace déclenche le transfert des fonds sur les comptes en banque des vendeurs et de la marketplace (opérations **cash-out**)

Cela est également fait pour des raisons légales. Si la marketplace encaissait tous les paiements en son nom et les redistribuait à ses vendeurs elle-même alors elle serait dans la même position qu'une banque : cela s'appelle **l'encaissement pour compte de tiers**. C'est une situation très compliquée légalement et financièrement. Du coup les PSP spécialisé pour les marketplaces proposent de gérer eux-même l'argent via des wallets pour résoudre ce problème.

## Workflows de paiement

Le workflow décrit au dessus n'est qu'un exemple. Nous supportons 3 workflows différents pour le paiement :

- workflow "*Classique*"
- workflow "*Vendeur direct*"
- workflow "*Wallet temporaire*"

### Workflow "classique"

C'est le workflow présenté plus haut :

- cash-in de l'acheteur : l'argent atterri dans un wallet acheteur
- répartition des fonds : l'argent est réparti dans les wallets vendeurs et marketplace
- cash-out : l'argent est transféré sur les comptes en banque des vendeurs et de la marketplace

Workflow implémenté pour :

- MangoPay
- LemonWay

Avantages :

- Bonne traçabilité : l'argent des commandes en cours est sur le wallet de l'acheteur, si il manque même 1 centime à un moment ça se verra tout de suite
- L'argent sur les wallets vendeurs correspond à des commandes terminées, le cash-out peut être fait à tout moment sans se poser la moindre question

Inconvénients :

- La loi française impose des [KYC](https://fr.wikipedia.org/wiki/Know_your_customer) pour des paiements au delà de 250€ (ou 2500€ sur l'année), ce qui n'est pas pratique du tout sur les sites e-commerces
- En 2018 le KYC sera même obligatoire au dessus de 1€

### Workflow "vendeur direct"

- cash-in de l'acheteur : l'argent est réparti directement dans les wallets vendeurs et marketplace (pas de wallet acheteur)
- cash-out : l'argent est transféré sur les comptes en banque des vendeurs et de la marketplace

Workflow implémenté pour :

- S-Money

Avantages :

- Pas de KYC acheteur donc pas de limite de montant

Inconvénients :

- L'argent pour toutes les commandes du vendeur est mélangé dans un seul wallet : c'est donc plus difficile de tracer les montants et de s'assurer qu'une erreur est bien détectée/empêchée
- L'argent sur les wallets vendeurs mélange les commandes terminées et celles en cours : le cash-out doit être fait attentivement pour pas prélever plus que nécessaire (par ex. si une commande est annulée, il faudra prélever dans ces wallets pour le remboursement)

### Workflow "wallet temporaire"

- cash-in de l'acheteur : l'argent atterri dans un wallet temporaire global à la marketplace
- répartition des fonds : l'argent est réparti dans les wallets vendeurs et marketplace
- cash-out : l'argent est transféré sur les comptes en banque des vendeurs et de la marketplace

Workflow implémenté pour :

- HiPay
- Stripe (fonds bloqués 7 jours dans le wallet commun)

Avantages :

- Pas de KYC acheteur donc pas de limite de montant
- L'argent sur les wallets vendeurs correspond à des commandes terminées, le cash-out peut être fait à tout moment sans se poser la moindre question

Inconvénients :

- L'argent pour toutes les commandes est mélangé dans un seul wallet : c'est donc plus difficile de tracer les montants et de s'assurer qu'une erreur est bien détectée/empêchée

## Misc

Lors du changement de clés de paiement, il est nécessaire de lancer la commande symfony `bin/console psp:wallets:reset`.
