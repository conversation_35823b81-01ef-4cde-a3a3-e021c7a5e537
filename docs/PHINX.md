# Migrations

Les migrations BDD sont gérées avec [Phinx](http://docs.phinx.org/).

## Créer une migration

La commande requiert un nom de classe PHP, donc en CamelCase

```bash
vendor/bin/phinx create MyMigration
```

Attention la classe créée aura une méthode `change()`, nous ne l'utilisons pas.
Il faut ajouter la méthode `up()` qui laisse plus de liberté et ajouter la méthode `down()` (pour gérer le rollback).

Attention également, phinx crée une fonction vide. Il faut manuellement ajouter les requetes SQL en les reprenant via la commande `d:s:u` :

```bash
bin/console doctrine:schema:update --dump-sql
```

Nb : cette commande renvoie plus de requetes que les changement que l'on a fait car le modèle de données n'est pas synchrone avec les données en BDD.

```php
class MyMigration extends AbstractMigration
{
    public function up()
    {
        ...
    }

    public function down()
    {
        ...
    }
}
```

## Jouer les migrations

```
vendor/bin/phinx migrate
```

## Rollback une migration

Il faut spécifier la version anterieure à laquelle on souhaite revenir.

```bash
vendor/bin/phinx rollback -t 20190812092206
```

[Documentation Phinx](http://docs.phinx.org/en/latest/commands.html#the-rollback-command)
