@startuml
class OrderAmounts {
id : Int
OrderId : Int
amountItemId : Int
shippingAmountid : Int
subtotalExclTaxes : Money
subTotalInclTaxes : Money
totalDiscount : Money
totalExclTaxes : Money
totalInclTaxes : Money
totalGreenTaxes : Money
taxes : Array
totalMarketplaceDiscount : Money
customerTotal : Money
}

class AmountItem {
id : Int
orderItemDataId : OrderItemData
discount : Money
totalExclTaxes : Money
totalInclTaxes : Money
unitPriceExclTaxes : Money
unitPriceInclTaxes : Money
quantity : Int
taxRate : Float
greenTax : Money
totalGreenTax : Money
unitTaxAmount : Money
totalTaxAmount : Money
discountedUnitPriceExclTaxes : Money
discountedUnitPriceInclTaxes : Money
discountedUnitTaxAmount : Money
discountedTotalExclTaxes : Money
discountedTotalInclTaxes : Money
discountedTotalTaxAmount : Money
discountMarketplace : Money
}

class ShippingAmounts {
id : Int
discount : Money
totalExclTaxes : Money
totalInclTaxes : Money
taxRate : Float
totalTaxAmount : Money
discountedTotalExclTaxes : Money
discountedTotalInclTaxes : Money
discountedTotalTaxAmount : Money
}

class OrderItemData {
id : Int
productId : Int
declinationId : String
productName : String
productCode : String
taxIncludedInPrice : Bool
maxPriceAdjustment : Int
itemId : Int
taxData : Mixed[]
rawPrice : Float
priceBeforeAdjustement : Money
}

OrderAmounts "1" -- "0..n" AmountItem
OrderAmounts -- ShippingAmounts : 1
AmountItem  --  OrderItemData : 1
@enduml
