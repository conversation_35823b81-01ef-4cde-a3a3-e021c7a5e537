# Fonctionnement du projet

Désormais, tout passe par Symfony, quelque soit le point d'entrée (excepté l'API legacy `api.php`).
Pour savoir s'il faut router vers le controller CsCart `fn_dispatch` ou vers un controller Symfony, j'ai décoré le `RouterListener` de Symfony.
Le schéma de décision du routing est disponible [ici](https://docs.google.com/drawings/d/1agCvV5cK6U5Ehhy8wN3ZphR8A8NH-KsDnGCXN4NTpyk).

L'enchainement simplifié des actions est le suivant :

* On arrive sur le point d'entrée `(index|admin|vendor|web\/app(_dev)?)\.php`.
* On crée une `Request` Symfony
* Cette request est passée au `HttpKernel`
* Le kernel déclenche l'évènement `kernel.request`
* Le router listener défini le `_controller` en charge de la requête
* Le kernel instancie et exécute le controller
* Le controller renvoi une `Response` Symfony au kernel
* La réponse est renvoyée à apache
