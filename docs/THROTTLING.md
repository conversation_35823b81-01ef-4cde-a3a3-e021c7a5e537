# Throttling

Le throttling consiste à limiter la vitesse d'exécution d'un traitement. <br>
Actuellement deux types de throttling ont été mis à en place pour l'authentification :

- Throttle sur l’adresse mail
- Throttle sur l’addresse IP

Ces validations peuvent concerner le BO et/ou l’API.

## Throttling BO sur l'adresse mail

Cette feature a été développée avant les autres, pour répondre à un besoin de protection contre les attaques par brute-force sur le BO.
Elle n’est pas désactivable, et peut se paramétrer uniquement dans le fichier parameters.defaults.yml.

parameters.defaults.yml


| key           | value         |
| ------------- |:-------------:|
| authenticator_throttling.email_throttle_map | 3: 60 <br> 5: 120<br> 10: 180 |
| authenticator_throttling.email_throttle_window | 1320 |

Pour la throttle map, chaque clé correspond à un nombre de tentative, et chaque valeur au temps d’attente en secondes. <br>
Par exemple 6 tentatives avec un mauvais de mot de passe vont bloquer une adresse mail pour 120 secondes. <br><br>
La throttle window correspond au TTL en secondes pour chaque tentative. Une fois le TTL atteint, la tentative est supprimée de la base Redis.

## Throttling BO sur l'adresse IP

Le fonctionnement est le même que pour le BO, mais sur la route authenticate pour éviter de pénaliser les
implémentations existantes, un FF permet d’activer/désactiver la feature.

parameters.defaults.yml

| key           | value         |
| ------------- |:-------------:|
| authenticator_throttling.email_throttle_map.api | 5: 60 <br> 10: 120<br> 15: 180 |
| authenticator_throttling.email_throttle_window.api | 1320 |

.env

| key           | value         | value         |
| ------------- |:-------------:|:-------------:|
| SECURITY_LOCK_EMAIL_API | 1 | Activation de la feature |

## Throttling API

Sur la route authenticate et sur le BO le fonctionnement est le même. <br>

.env

| key           | value            | value      |
| ------------- |:-------------:|:-------------:|
| SECURITY_LOCK_IP_ADDRESS | 1 | Activation de la feature |
| SECURITY_LOCK_IP_ADDRESS_FAILED_LIMIT | 3 | Nombre d’essai avant le bloquage de l'IP |
| SECURITY_LOCK_IP_ADDRESS_DURATION | 43200 | Temps d’attente en secondes |

Le fonctionnement est différent du throttle par mail. Il n’y pas de paliers : une fois la limite atteinte, l’utilisateur doit attendre SECURITY_LOCK_IP_ADDRESS_DURATION secondes. <br>
Une fois le temps écoulé, SECURITY_LOCK_IP_ADDRESS_FAILED_LIMIT repasse à 0.

Attention, si le front ne communique pas directement avec le back (par exemple si il y a un proxy d'entreprise ou un reverse proxy),
il faut bien renseigner le HTTP_X_FORWARDED_FOR de la requête.
Sans quoi l’ip du proxy sera bloquée, et le connexion au back sera impossible pour tous les utilisateurs. <br>
Dans la plupart des cas, dans le cadre d'une connexion HTTPS derrière un reverse (Nginx) ou un cache (Varnish), cela est fait automatiquement.

La récupération de l’addresse ip se fait via le getClientIp de symfony.

## Implémentation d'un nouveau throttling

Une classe abstraite AuthenticatorThrottling permet de facilement implémenter de nouveaux throttling. <br>
Cette classe à trois méthodes :

- checkThrottling -> retourne le temps d’attente. Il faut retourner 0 si l'utilisateur n'a pas à attendre
- saveAttempt -> sauvegarde d’une tentative sur le redis
- resetAttempt -> supprime les tentatives du redis

