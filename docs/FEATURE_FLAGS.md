# Gestion des Feature Flags

Un *feature flag* (FF) est une option de configuration qui active
ou désactive une fonctionnalité de l'application.
En ce sens, un FF est toujours un booléen,
dont la valeur pour Symfony est *true* ou *false*.
La fonctionnalité activée peut ensuite avoir
ses propres paramètres de configuration
(URL de callback, durée de conservation, etc.)
qui ne sont pas des feature flags.

## Routes API

Lorsqu'une route API doit être désactivée en même temps que son FF,
il faut que son Controller implémente
l'interface `\Wizacha\FeatureFlag\FeatureFlaggableInterface`.
Toutes les routes du Controller vont alors être désactivées
si le FF dont le nom est retourné par `getFeatureFlag` vaut `false`.

Cette désactivation automatique est gérée
par `\Wizacha\AppBundle\EventListener\FeatureFlagListener`.
La réponse HTTP retournée est une `403 Access Denied`.

## Connaitre l'état des FF

Lorsqu'on a besoin de connaitre l'état d'un FF, il faut utiliser
le service `\Wizacha\FeatureFlag\FeatureFlagService`
et sa méthode `public function get(string $name): bool`.

## Nommage des FF

Les feature flags doivent avoir un nom qui commence par `feature.`.
Par exemple : `feature.iban_validation`.

## Commande de debug

La commande `debug:features` est disponible pour connaitre la liste
des FF et leur valeur.

```shell
Description:
  List all features from the kernel

Usage:
  debug:features [<name>]

Arguments:
  name                      A feature name

Options:
  -h, --help                Display this help message
  -q, --quiet               Do not output any message
  -V, --version             Display this application version
      --ansi                Force ANSI output
      --no-ansi             Disable ANSI output
  -n, --no-interaction      Do not ask any interactive question
  -e, --env=ENV             The Environment name. [default: "dev"]
      --no-debug            Switches off debug mode.
      --bypass-legacy-init  Do not require init.php
      --no-redis            Do not use Redis
  -v|vv|vvv, --verbose      Increase the verbosity of messages: 1 for normal output, 2 for more verbose output and 3 for debug

Help:
  The debug:features displays the features' status:

  php bin/console debug:features


```
