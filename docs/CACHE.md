# Gestion de cache

## Cache HTTP
Afin d'améliorer les performances des Marketplaces, un système de mis en cache basé sur les headers HTTP a été mis en place. Pour ne pas mettre des commentaires partout dans le code, on explique dans cette section le fonctionnement du cache.
### Avec une durée d'expiration :
Il suffit de spécifier une durée de validité du cache, comme illustré dans le schéma suivant :
```
Utilisateur                                   Cache                                    Backend
     |              ---GET--->                  |                                         |
     |                                          |             ---GET--->                  |
     |                                          |    <---Response(200, maxage=60s)---     |
     |     <---Response(200, maxage=60s)---     |                                         |
     |                                          |                                         |
                               ----------- (Après 30s) --------------
     |              ---GET--->                  |                                         |
     |  <---Response(200, age=30s, maxage=60)-- |                                         |
     |                                          |                                         |
```
Exemple de Code :
```
public function treeAction()
    {
        $tree = $this->$catalogService->getTree();

        $response =  JsonResponse($tree);

        // cache valide pour 900 secondes
        $response->setSharedMaxAge(900);

        return $response;
    }
```
### Avec une mécanisme de validation :
On fait une vérification pour savoir si la réponse a changé depuis la dernière requête, s'il a eu un changement, on renvoit la réponse mise à jour, sinon on demande au cache de renvoyer la réponse (code 304).
```
Le schéma suivant est un exemple de validation à base de la dernière date de modification :
Utilisateur                                   Cache                                    Backend
     |               ---GET--->                 |                                         |
     |                                          |               ---GET--->                |
     |                                          |      <---Response(200, lastUpdate)---   |  lastUpdate = 11.01.2018...
     |     <---Response(200, lastUpdate)---     |                                         |
     |                                          |                                         |
                               ----------- (Après 30s) --------------
     |               ---GET--->                 |                                         |
     |                                          |       ---GET(lastUpdate ?)--->          |
     |                                          |    <---Response(304 not modified)---    |  lastUpdate = 11.01.2018...
     |    <---Response(200, lastUpdate)---      |                                         |
     |                                          |                                         |
```
Exemple de Code :
```
    public function getIdAction(Request $request, string $id)
    {
        $product = $productService->getProduct($id);

        $response = new JsonResponse();
        $response->setLastModified($product->getUpdatedAt());

        // Rendre la réponse publique, privée par défaut
        $response->setPublic();

        if ($response->isNotModified($request)) {
            return $response;
        }

        $response->setData($product);

        return $response;
    }
```
### Combinaison d'expiration et validation :
Une première vérification est faite à base de temps d'expiration, si l'âge du cache est dépassé, un fallback sur le mécanisme de validation est réalisé.

## Table Récapitulative du cache implémenté
| API  | Durée  | Validation | Commentaires | Impact|
| ------------- | ------------- | ------------- | ------------- | ------------- |
| api_search_products  | 5 min  | Non | valable uniquement pour les requêtes simples | récupération des produits récents ou avec 1 attribut, principalement pour les pages d'accueil
| api_catalog_category_tree | 15 min  | Non || le menu principal des catégories |
| api_catalog_category_list | 15 min  | Non ||
| api_cms_menu_list | 15 min  | Non || les menus CMS affichés dans le footer |
| api_banners_get | 15 min  | Non || les bannières |
| api_banners_get_category | 15 min  | Non || les bannières propres à une catégorie |
| api_catalog_products_id | 5 min | date de modification du produit || page d'affichage d'un produit |
| api_catalog_product_reviews | 15 min | Non || commentaires à propos d'un produit |

## Cache Storage

Pour nos APIs, nous considérons actuellement qu'il est de la responsabilité des serveurs Fronts-Offices (FO) de gérer leur propre stockage de cache. Par example, le [WizaplaceFrontBundle](https://github.com/wizaplace/wizaplace-front-bundle) rajoute à Guzzle un [middleware](https://github.com/Kevinrob/guzzle-cache-middleware) fait très exactement pour ca.

Dans le meilleur des cas (résultat en cache sans besoin de validation), cela permet d'éviter aux FO d'effectuer le moindre appel réseau.

## Activation du cache

Le cache peut être activé avec une feature flag dans chaque environnement (qui prend la valeur false par défaut).

**Il faudra rajouter une ligne d'activation du cache pour chaque client en production.**

## Liens utiles :
[Symfony 3.4 HTTP Cache](https://symfony.com/doc/3.4/http_cache.html)

[Things cache do](https://tomayko.com/blog/2008/things-caches-do)
