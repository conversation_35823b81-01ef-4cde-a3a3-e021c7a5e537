# Deploy

[legacy](PLATFORMSH.md)

Les déploiements se font sur un cluster [kubernetes](https://kubernetes.io/) via [helm](https://helm.sh/). <PERSON><PERSON>, un package s'appelle un `chart`, pour la marketplace il se trouve dans le dépôt `wizaplace-config` dans le dossier `charts/wizaplace/`.

## Workflow

- À chaque `push` sur master, l'image de production docker est construite et envoyée sur notre registre Azure,
- À chaque `tag`, l'image de production est tagguée et envoyée sur notre registre Azure,
- Ce qui déclenche un job sur notre jenkins pour lancer le déploiement de chacun de nos clients

## Configuration

```yaml
# Mandatory
hostname: # nom de domaine
mail_domain: # domaine d'envoi de mail
system_api_password: # bin/console security:encode-password --empty-salt [password] Symfony\Component\Security\Core\User\User
aws_access_key:
aws_secret:
aws_bucket:
# Azure MySQL
database_host:
database_user:
database_password:
google_oauth_id:
google_oauth_secret:
google_oauth_redirect_url: https://auth.wizntw.com/redirect.php
google_maps_api_key:
crypt_key: # openssl rand -base64 32
video_system_pipeline:
video_system_temporary_bucket:

# Mailer
mailer:
  transport: smtp
  host: email-smtp.eu-west-1.amazonaws.com
  port: 465
  user: # Amazon SES user
  password: # Amazon SES password
  encryption: ssl

# Mangopay
mangopay:
  client_id:
  client_password:
  base_url:
  user_id_agent_etablissement_paiement:

# Stripe
stripe:
  secret_token:

# Smoney
smoney:
  token:
  base_url:
  website_name:

# Lemonway
lemonway:
  client_login:
  client_password:
  directkit_url:
  webkit_url:
  marketplace_id:
  use_buyer_wallet_for_moneyin:

# Hipay
hipay:
  cashin_username:
  cashin_password:
  cashout_login:
  cashout_password:
  cashout_entity:
  cashout_merchant_group_id:
  commission_recipient_id:
  sandbox:

# Dolist
dolist:
  account_id:
  authentication_key:
  debug:

# Chronopost
chronopost:
  account_number:
  password:

# Prediggo
prediggo:
  api_url:
  enable_exporter:

# Algolia
algolia:
  identifier:
  index_prefix:
  key_full:
  key_limited:
```

## Déploiement

```bash
$ # écriture de la configuration ci-dessus dans le fichier data/k8s/<client name>/values.yaml
$ export CLIENT_NAME=wizaplace-test
$ export DOCKER_TAG=$(git describe --tags --abbrev=0)
$ helm upgrade --install -f data/k8s/<client name>/values.yaml  \
    --set dockerTag=$DOCKER_TAG \
    --namespace $CLIENT_NAME \
    $CLIENT_NAME ./charts/wizaplace
```
