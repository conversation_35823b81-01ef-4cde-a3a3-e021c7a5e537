# WEBGRIND

Webgrind is a Xdebug profiling output analysis frontend (default: `http://localhost:9999`)

The profiling output (cachegrind) is enabled by a trigger (Xdebug need to be activated: `make docker-dev-xdebug-enable`)

### Activate Xdebug profiling from the browser

You'll need a browser extension helper: [chrome extension](https://github.com/mac-cain13/xdebug-helper-for-chrome), [firefox extension](https://github.com/BrianGilbert/xdebug-helper-for-firefox) for instance.

### From a REST client _(ex.: Postman)_

By setting the `XDEBUG_PROFILE` as a COOKIE or GET/POST parameter.

### From the CLI

You need to run php with this argument:
```bash
php -d xdebug.profiler_enable=On <your script>
```

ex.:
```bash
php -d xdebug.profiler_enable=On console/bin a:symfony:command
php -d xdebug.profiler_enable=On vendore/bin phpunit path/to/test/file
```
