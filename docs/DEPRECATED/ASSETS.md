# Gestion des assets

## Front Office

Il existe deux moyens de compiler les assets (javascript, less) d'un thème :

### avec `Phing` :

```
$ make generate-assets
```

### avec `Gulp` :

```
$ gulp front
```

remarques :
- cette commande lance un watcher qui permet de re-déclencher la compilation des assets quand un fichier `.less` ou
 `.js` du thème courant est modifié.
Elle doit être exécutée avant la première modification car elle permet au script de déterminer le thème courant à surveiller.
- cette commande exécute trois sous-tâches,
une première fois `gulp front:style` et `gulp front:script` puis `gulp front:watch` qui ré-exécutera ces tâches en fonction des assets
modifiés par la suite.

## Back Office

Les assets du Back Office sont entièrement gérés via `Gulp` :

```
$ gulp back
```

remarques :
- selon le même fonctionnement que pour le Front Office, cette commande lance les tâches `gulp back:style`, `gulp back:script:dev` puis `gulp back:watch`.
- il existe une tâche `gulp back:script:prod` qui minifie les fichiers javascript.
Différencier les scripts de développement et de production permet d'utiliser les versions non minifiées des bibliothèques
javascript, ce qui est utile par exemple dans le cas de VueJs qui
[donne accès à des outils](https://chrome.google.com/webstore/detail/vuejs-devtools/nhdogjmejiglipccpnnnanhbledajbpd)
en environnement de développement.

Note sur ES6+ :
Les tâches `gulp back:script:dev` et `gulp back:script:prod` exécutent également une sous-tâche `gulp back:babelify` qui permet de pré-compiler
du code javascript ES6+. Afin de rester compatible avec le code existant, le point d'entrée du code ES6 est le fichier
`backoffice.js` qui sera pré-compilé avant d'être incorporé au reste du code.
