# Database

## AWS

Je laisse le soin aux anciens de documenter cette partie.

## Platform.sh

Tous les backups sont fais sur `backup.wizaplace.com`. Le serveur est accessible avec l'utilisateur `ubuntu` et la clé SSH `Landing.pem`. Les backup sont lancés toutes les 6 heures, soit à minuit, 6h, 12 et 18h (UTC). Ils sont ensuite stockés dans notre bucket S3 `wizaplace-platformsh-backup` et sont gardés pour une durée de 14 jours. Après quoi ils sont archivés sur Glacier. Il est toujours possible de les remonter manuellement dans le bucket.

Le format de stockage est le suivant : `<project>/<db>.<date>.sql.gz`.
Le format de date est le suivant : `YYYY-MM-DD-HH`, avec `HH` pouvant valoir `00`, `06`, `12` ou `18`.

Pour lancer une procédure de restauration d'une version d'un dump, il faut aller sur [jenkins](http://smithers.wizacha.com/jenkins/job/DEPLOY_Restore_platformsh_backup/) et lancer un _job_ avec le nom du projet et au besoin la version à restaurer. Par défaut le job va restaurer le dernier backup.

Ensuite il suffit d'attendre dans le chan `#infra` la réponse du bot. Dans un premier temps le nom d'utilisateur et le mot de passe pour accéder à la restauration vont être envoyés :

> Here are the credentials for the restoration you asked through jenkins build:
> Username: `alexandbox-1501164841`
> Password: `yt8YNTwl50AqtFAmURg+IQ==`

Ensuite, l'import est lancé en asynchrone, il peut prendre un certain temps en fonction de la taille de la base, une fois celui-ci terminé, vous allez recevoir dans slack le nom de la ou des bases restaurées.

> Your backup of **alexandbox** is ready in **alexandbox-marketplace-database-1501164841**. Check your credentials in the console log of the jenkins build.
> Your backup of **alexandbox** is ready in **alexandbox-discuss-database-1501164841**. Check your credentials in the console log of the jenkins build.

Pour finir, voici comment se connecter à la base restaurée :

### SSH

| Paramètre | Valeur |
| --- | --- |
| Host  | `backup.wizaplace.com`  |
| Username  | `ubuntu`  |
| Private key  | `Landing.pen`  |

### MySQL

| Paramètre | Valeur |
| --- | --- |
| Host  | `127.0.0.1`  |
| Port  | `3306`  |
| Username  | `<affiché sur slack>`  |
| Password  | `<affiché sur slack>`  |

Un tunnel SSH est nécessaire pour se connecter à la base de donnée, pour ne pas avoir à exposer le port `3306` sur l'extérieur. Tout bon client MySQL comme **Sequel Pro** ou encore **Workbench** supportent ce mode de connexion.
