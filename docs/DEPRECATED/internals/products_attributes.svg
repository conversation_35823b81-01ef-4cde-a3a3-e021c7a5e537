<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" width="1143px" height="952px" version="1.1"
     content="&lt;mxfile userAgent=&quot;Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/55.0.2883.87 Safari/537.36&quot; version=&quot;********&quot; editor=&quot;www.draw.io&quot; type=&quot;google&quot;&gt;&lt;diagram name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;"
     style="background-color: rgb(255, 255, 255);"><defs/><g transform="translate(0.5,0.5)"><rect x="41" y="26" width="180" height="150" fill="#ffffff" stroke="#000000" pointer-events="none"/><path d="M 41 90 L 41 26 L 221 26 L 221 90 Z" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(47.5,38.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="166" height="40" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Produit<div><span id="docs-internal-guid-5d08617d-a77e-7780-17fa-ce758f791989"><span>cscart_products</span></span><br /></div><div><span id="docs-internal-guid-5d08617d-a77e-a596-c7c5-65a21c355a77"><span>(example: Jean Levis™ #1000)</span></span><span><span><br /></span></span></div></div></div></foreignObject><text x="83" y="26" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(46.5,96.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="66" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; overflow: hidden; max-height: 22px; max-width: 168px; width: 67px; white-space: normal; word-wrap: normal;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">+ product_id</div></div></foreignObject><text x="33" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">+ product_id</text></switch></g><g transform="translate(46.5,122.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="20" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; overflow: hidden; max-height: 22px; max-width: 168px; width: 21px; white-space: normal; word-wrap: normal;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">+ ...</div></div></foreignObject><text x="10" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">+ ...</text></switch></g><rect x="681" y="1" width="160" height="136" fill="#ffffff" stroke="#000000" pointer-events="none"/><path d="M 681 59 L 681 1 L 841 1 L 841 59 Z" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(690.5,10.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="140" height="40" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Option<div><span id="docs-internal-guid-5d08617d-a77c-6c94-b5da-5c82e5dd919c"><span>cscart_product_options</span></span></div><div><span id="docs-internal-guid-5d08617d-a77c-d53d-039b-29d7f852ffef"><span>(examples : taille, couleur)</span></span><span><span><br /></span></span></div></div></div></foreignObject><text x="70" y="26" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(686.5,65.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="59" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; overflow: hidden; max-height: 22px; max-width: 148px; width: 60px; white-space: normal; word-wrap: normal;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">+ option_id</div></div></foreignObject><text x="30" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">+ option_id</text></switch></g><g transform="translate(686.5,91.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="66" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; overflow: hidden; max-height: 22px; max-width: 148px; width: 67px; white-space: normal; word-wrap: normal;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">+ product_id</div></div></foreignObject><text x="33" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">+ product_id</text></switch></g><g transform="translate(686.5,117.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="20" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; overflow: hidden; max-height: 22px; max-width: 148px; width: 21px; white-space: normal; word-wrap: normal;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">+ ...</div></div></foreignObject><text x="10" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">+ ...</text></switch></g><rect x="871" y="249" width="210" height="136" fill="#ffffff" stroke="#000000" pointer-events="none"/><path d="M 871 307 L 871 249 L 1081 249 L 1081 307 Z" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(892.5,258.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="167" height="40" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Variante d'option<div><span id="docs-internal-guid-5d08617d-a77d-80c3-df36-9c984ffc37b5"><span>cscart_product_option_variants</span></span><br /></div><div><span id="docs-internal-guid-5d08617d-a77d-b706-c9b3-96cb63da3c52"><span>(examples: bleu, noir, 42, 43)</span></span><span><span><br /></span></span></div></div></div></foreignObject><text x="84" y="26" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(876.5,313.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="62" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; overflow: hidden; max-height: 22px; max-width: 198px; width: 63px; white-space: normal; word-wrap: normal;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">+ <span id="docs-internal-guid-5d08617d-a77d-ef8f-28ab-e95b34608db7"><span>variant_id</span></span></div></div></foreignObject><text x="31" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(876.5,339.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="59" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; overflow: hidden; max-height: 22px; max-width: 198px; width: 60px; white-space: normal; word-wrap: normal;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">+ <span id="docs-internal-guid-5d08617d-a77e-1a33-cc22-9afb2a21f2c4"><span>option_id</span></span></div></div></foreignObject><text x="30" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(876.5,365.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="20" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; overflow: hidden; max-height: 22px; max-width: 198px; width: 21px; white-space: normal; word-wrap: normal;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">+ ...</div></div></foreignObject><text x="10" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">+ ...</text></switch></g><rect x="351" y="272" width="280" height="136" fill="#ffffff" stroke="#000000" pointer-events="none"/><path d="M 351 330 L 351 272 L 631 272 L 631 330 Z" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(377.5,281.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="226" height="40" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Inventaire<div><span id="docs-internal-guid-5d08617d-a77f-32a9-7a0d-32ede31fb14b"><span>cscart_product_options_inventory</span></span><br /></div><div><span id="docs-internal-guid-5d08617d-a77f-4c06-4213-f05b2698d07c"><span>(example: coute 50</span><span>€</span><span> et a 3 items en stock)</span></span><span><span><br /></span></span></div></div></div></foreignObject><text x="113" y="26" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(356.5,336.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="66" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; overflow: hidden; max-height: 22px; max-width: 268px; width: 67px; white-space: normal; word-wrap: normal;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">+ <span id="docs-internal-guid-5d08617d-a77f-e888-5ae9-76615932bc5e"><span>product_id</span></span></div></div></foreignObject><text x="33" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(356.5,362.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="75" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; overflow: hidden; max-height: 22px; max-width: 268px; width: 76px; white-space: normal; word-wrap: normal;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">+ <span id="docs-internal-guid-5d08617d-a780-06a4-b673-776e56a9f614"><span>combination</span></span></div></div></foreignObject><text x="38" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(356.5,388.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="20" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; overflow: hidden; max-height: 22px; max-width: 268px; width: 21px; white-space: normal; word-wrap: normal;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">+ ...</div></div></foreignObject><text x="10" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">+ ...</text></switch></g><path d="M 227.37 103 L 451 103 L 451 98 L 681 98" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 222.12 103 L 229.12 99.5 L 227.37 103 L 229.12 106.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 631 369 L 751 369 L 751 320 L 864.63 320" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 869.88 320 L 862.88 323.5 L 864.63 320 L 862.88 316.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 631 369 L 751 369 L 751 346 L 864.63 346" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 869.88 346 L 862.88 349.5 L 864.63 346 L 862.88 342.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 1081 346 L 1101 346 L 1101 72 L 847.37 72" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 842.12 72 L 849.12 68.5 L 847.37 72 L 849.12 75.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="31" y="620" width="280" height="136" fill="#ffffff" stroke="#000000" pointer-events="none"/><path d="M 31 678 L 31 620 L 311 620 L 311 678 Z" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(103.5,629.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="135" height="40" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Feature<div><span id="docs-internal-guid-5d08617d-a788-1599-c128-307ff0bfe135"><span>cscart_product_features</span></span><br /></div><div><span id="docs-internal-guid-5d08617d-a788-3160-a45f-a2a0b6071bc2"><span>(example: type de coupe)</span></span><span><span><br /></span></span></div></div></div></foreignObject><text x="68" y="26" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(36.5,684.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="64" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; overflow: hidden; max-height: 22px; max-width: 268px; width: 65px; white-space: normal; word-wrap: normal;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">+ <span id="docs-internal-guid-5d08617d-a77f-e888-5ae9-76615932bc5e"><span>feature_id</span></span></div></div></foreignObject><text x="32" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(36.5,710.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="20" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; overflow: hidden; max-height: 22px; max-width: 268px; width: 21px; white-space: normal; word-wrap: normal;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">+ ...</div></div></foreignObject><text x="10" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">+ ...</text></switch></g><rect x="351" y="431" width="280" height="162" fill="#ffffff" stroke="#000000" pointer-events="none"/><path d="M 351 489 L 351 431 L 631 431 L 631 489 Z" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(405.5,447.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="170" height="26" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Valeur de feature<div><span id="docs-internal-guid-5d08617d-a788-cca2-de4a-cfc34220dcae"><span>cscart_product_features_values</span></span></div></div></div></foreignObject><text x="85" y="19" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(356.5,495.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="66" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; overflow: hidden; max-height: 22px; max-width: 268px; width: 67px; white-space: normal; word-wrap: normal;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">+ <span id="docs-internal-guid-5d08617d-a77f-e888-5ae9-76615932bc5e"><span>product_id</span></span></div></div></foreignObject><text x="33" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(356.5,521.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="64" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; overflow: hidden; max-height: 22px; max-width: 268px; width: 65px; white-space: normal; word-wrap: normal;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">+ <span id="docs-internal-guid-5d08617d-a77f-e888-5ae9-76615932bc5e"><span>feature_id</span></span></div></div></foreignObject><text x="32" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(356.5,547.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="62" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; overflow: hidden; max-height: 22px; max-width: 268px; width: 63px; white-space: normal; word-wrap: normal;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">+ variant_id</div></div></foreignObject><text x="31" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">+ variant_id</text></switch></g><g transform="translate(356.5,573.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="20" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; overflow: hidden; max-height: 22px; max-width: 268px; width: 21px; white-space: normal; word-wrap: normal;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">+ ...</div></div></foreignObject><text x="10" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">+ ...</text></switch></g><path d="M 351 502 L 21 502 L 21 103 L 34.63 103" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 39.88 103 L 32.88 106.5 L 34.63 103 L 32.88 99.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="711" y="625" width="280" height="162" fill="#ffffff" stroke="#000000" pointer-events="none"/><path d="M 711 683 L 711 625 L 991 625 L 991 683 Z" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(765.5,634.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="171" height="40" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Variante de feature<div><span id="docs-internal-guid-5d08617d-a78a-3f89-8ee5-cdefa846910d"><span>cscart_product_feature_variants</span></span><br /></div><div><span id="docs-internal-guid-5d08617d-a78a-5c74-712f-bb041236ea94"><span>(examples: slim, baggy)</span></span><span><span><br /></span></span></div></div></div></foreignObject><text x="86" y="26" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(716.5,689.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="62" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; overflow: hidden; max-height: 22px; max-width: 268px; width: 63px; white-space: normal; word-wrap: normal;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">+ variant_id</div></div></foreignObject><text x="31" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">+ variant_id</text></switch></g><g transform="translate(716.5,715.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="64" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; overflow: hidden; max-height: 22px; max-width: 268px; width: 65px; white-space: normal; word-wrap: normal;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">+ <span id="docs-internal-guid-5d08617d-a77f-e888-5ae9-76615932bc5e"><span>feature_id</span></span></div></div></foreignObject><text x="32" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><g transform="translate(716.5,741.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="20" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; overflow: hidden; max-height: 22px; max-width: 268px; width: 21px; white-space: normal; word-wrap: normal;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">+ ...</div></div></foreignObject><text x="10" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">+ ...</text></switch></g><path d="M 317.37 691 L 511 691 L 511 722 L 711 722" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 312.12 691 L 319.12 687.5 L 317.37 691 L 319.12 694.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 24.63 691 L 11 691 L 11 528 L 351 528" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 29.88 691 L 22.88 694.5 L 24.63 691 L 22.88 687.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 631 554 L 671 554 L 671 696 L 704.63 696" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 709.88 696 L 702.88 699.5 L 704.63 696 L 702.88 692.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 351 343 L 131 343 L 131 182.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 131 177.12 L 134.5 184.12 L 131 182.37 L 127.5 184.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(1.5,845.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="1138" height="68" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 1138px; white-space: normal; word-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;"><div style="text-align: left"><span>Un produit possède 2 types d'attributs :  les options et les features.</span></div><div style="text-align: left"><span>Les options doivent être choisies par l'utilisateur pour acheter un produit : un jean peut-être disponible en différentes tailles et couleurs. Chaque combinaison unique d'options correspond à un inventaire, qui contient le stock et le prix pour ce produit avec ces options.</span></div><div style="text-align: left"><span>Les features ne sont pas sélectionnables : elles ne font que décrire le produit. Elles sont notamment utilisées pour la recherche et les filtres: dans la catégorie "Jean" vous pouvez filtrer tous les produits qui pour la feature "type de coupe" ont pour valeur la variante "baggy".</span></div></div></div></foreignObject><text x="569" y="40" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g></g></svg>
