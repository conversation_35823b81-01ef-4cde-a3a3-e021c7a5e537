# Déploiement

### 1 - Déclenchement

Pour déployer, un **développeur** :

* créer un tag sous la forme `1.AA.MM.JJ.x`

Avec _x_ qui est un numéro incrémental de déploiement dans la journée, commençant à 0.

Exemple :

```
$ git tag **********.0
$ git push origin **********.0
```

Il est aussi possible d'utiliser le [script de déploiement](../scripts/wiza-git-deploy.sh) qui automatise tout ça.

### 2 - Build

Lorsque le tag de déploiement est pushé, [**Drone**](https://drone.io/) :

* exécute l'ensemble des tests sur ce tag,
* détecte que c'est un déploiement (avec le format du tag),
* créer une archive contenant tout le code source si les tests sont réussis,
* copie l'archive sur le serveur interne [smithers](http://smithers.wizacha.com).

### 3 - Injection de la configuration

Lorsque **Jenkins** qui tourne sur _smithers_ détecte qu'il y a une nouvelle archive :

* il extrait l'archive dans un répertoire dédié contenant l'arborescence complète de la prod (un répertoire par client),
* il injecte la configuration (qu'il a en dur) dans chaque client.

### 4 - Création du snapshot sur AWS

Lorsque l'application temporaire sur _smithers_ est configurée, alors **Jenkins** :

* créer une instance EC2 sur AWS à l'aide d'une AMI standard,
* copie l'application sur cette instance,
* fait un snapshot prêt à monter,
* détruit l'instance EC2.

### 5 - Création des instances EC2

Lorsque le snapshot est réalisé, alors **Jenkins** :

* monte deux instances EC2 à partir du snapshot,
* exécute la commande [DeployPostActionsCommand.php](https://github.com/wizaplace/wizaplace/blob/master/src/AppBundle/Command/DeployPostActionsCommand.php) pour appliquer les changements de la base de données (structure et données),
* notifie les développeurs sur Slack que la mise en prod du code est terminé et fourni l'IP des deux nouvelles instances.

**ATTENTION** : à partir de là, l'ancien code toujours en production tourne sur les nouvelles données, tant que les DNS n'ont pas été mis à jour pour pointer sur les nouvelles instances qui viennent d'être créées et qui contiennent le nouveau code. CHAUD PATATE.

### 6 - Validation humaine

Lorsque Jenkins a envoyé une notification sur Slack, **les développeurs** :

* lancent la tâche `make set-futur-prod-ip` pour faire pointer en local les urls des sites clients vers les nouvelles instances,
* testent leurs développements,
* remettent les bons hosts avec la tâche `make remove-futur-prod-ip`.

### 7 - Pointage DNS

Lorsqu'ils ont validé leurs développements, **les développeurs** :

* lancent une tache Jenkins qui met à jour les DNS chez AWS pour pointer sur les nouvelles instances (qu'elle a récupéré en retour de la tache 5).
