# Mettre en place un nouveau client (pour l'infra)

## Lancer la tâche [DEPLOY_Create_Plateforme](http://smithers/jenkins/job/DEPLOY_Create_Plateforme/)
Cette tâche crée 
* les files SQS,
* le bucket S3,
* la base de donnée vide,
* le compte IAM,
* les index algolia,
* le compte SES.

En sortie, la tâche nous donne les `echo VAR=value` à copier/coller.

## Créer le recaptcha

## Écrire les informations pour la tâche [DEPLOY_1_GENERATE_Config_All](http://smithers/jenkins/job/DEPLOY_1_GENERATE_Config_All/)

Copier/coller les informations de la tâche `DEPLOY_Create_Plateforme`.

Rajouter les clés recaptcha.

Penser à rajouter le nouveau client dans la matrice pour ses fichiers de configuration soient créés

## Mettre à jour le fichier de conf Jenkins

Dans `Administ<PERSON> -> Configuration Files` modifier `Wizaplace clients Configuration`.

Vous aurez besoin de determiner un id pour le domaine `wizntw.com`.

## Créer les certificats let's encrypt.
* C<PERSON>er le record dans route53 qui pointe vers une des machines actuellement en production
* Lancer la tâche [DEPLOY_LetsEncryptSsl](http://smithers/jenkins/job/DEPLOY_LetsEncryptSsl/)

## Mettre à jour le fichier pour le `set_deploy_ip`
 * se connecter sur smithers
 * ouvrir le fichier `/var/www/deployTestTargets`
 * rajouter le nouveau client

## Mettre à jour DEPLOY_5_Update_Dns
 * Acceder à la tache [ DEPLOY_5_Update_Dns](http://smithers/jenkins/job/DEPLOY_5_Update_Dns/) 
 * rajouter le domaine `wizntw` au tableau `$domainZ30BQ0NW4QTH65` 

## Valider le nom de domaine pour l'envoi d'emails
Tant que l'envoi est fait depuis un email en `*.sandbox.wizaplace.com` il suffit de se rendre sur SES et de cliquer sur la validation du domaine avec S3.

## Premier deploiement.
Au premier déploiement, se connecter dans le back admin du nouveau client.

* Configurer les emails `/superviseur.php?dispatch=settings.manage&section_id=Emails`
* Tester l'envoi des emails `/superviseur.php?dispatch=patch` et la tache `test_email`

## Remplir checklist

## Notifier par email la création de la sandbox.


