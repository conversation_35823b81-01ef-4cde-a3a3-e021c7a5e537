# Database

The database is split between the CS-Cart tables (prefixed by `cscart_`) and the tables managed by doctrine (prefixed by `doctrine_`).
You can find some information about CS-Cart in the [official documentation](http://docs.cs-cart.com/4.3.x/developer_guide/core/db/structure_and_naming.html).

To know more about products and their attributes, see the diagram [products_attributes.svg](./products_attributes.svg).