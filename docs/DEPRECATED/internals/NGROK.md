# Debugger wizaplace/wizaplace via ngrok - résoudre le problème des assets manquants

(Pour installer ngrok via npm, voir https://www.npmjs.com/package/ngrok)

Certains liens des assets des bundles du repository wizaplace/wizaplace sont en valeur absolue :
Par exemple `http://wizaplace.loc/images/logo.png` au lieu de `images/logo.png` pour un lien relatif.

Cela empêche de tester le projet avec ngrok car les images et fichiers css seront absents.
Pour contourner le problème, il faut faire pointer ces liens absolus vers l'uri fournie par ngrok à la place de `wizaplace.loc` :

Se rendre à la source du projet wizaplace et taper la commande suivante dans un terminal :
ngrok http wizaplace.loc:80

Attention : Il est important de laisser tourner le serveur ngrok pendant toute la durée de l'opération car l'URI fournie change à chaque fois qu'il est lancé.
Récupérer l'URI fournie par ngrok, par exemple : 984891a6.ngrok.io

Et s'en servir pour modifier le paramètre `http_host` du fichier `app/config/config_dev.yml` :
```
parameters:
    http_host: wizaplace.loc
```

devient :

```
parameters:
    http_host: 984891a6.ngrok.io
```

Le site fonctionnel est désormais accessible via l'adresse fournie par ngrok
Dans notre exemple, il s'agirait de `http://984891a6.ngrok.io`
