#!/bin/bash

FORMAT=`date +"1.%y.%m.%d."`

if [ $# -eq 0 ]; then
    echo "Pas de numéro d'incrémentation : génération depuis le dernier tag sur GitHub..."
    if [ -z "$GITHUB_TOKEN" ]; then
        echo "Il est nécessaire d'ajouter un token GitHub dans la variable \$GITHUB_TOKEN."
        exit 1
    fi
    OLD_INCR=`curl -s -H "Authorization: token $GITHUB_TOKEN" https://api.github.com/repos/wizaplace/wizaplace/git/refs/tags | grep "\"refs/tags/$FORMAT[0-9]" | tail -n 1 | awk -F "." '{print $5}' | awk -F "\"" '{print $1}'`
    if [ -z "$var" ]; then
        echo "Premier déploiment de la journée."
        INCR=0;
    else
        echo "Incrémentation du numéro de déploiement de la journée."
        INCR=$((OLD_INCR+1))
    fi
else
    echo "Récupération du numéro d'incrémentation passé à la commande."
    INCR=$1
fi

# Génération du nom de tag
TAG="$FORMAT$INCR";

read -p "Voulez-vous vraiment déployer le tag [$TAG] ? [o/N] " -n 1 -r
if [[ ! $REPLY =~ ^[OoYy]$ ]]; then
    echo "Annulation du déploiement."
    exit 1
fi

echo "Déploiement de $TAG...";

ON_MASTER=`git branch | grep "\* master"`

if [ -z "$ON_MASTER" ]; then
    echo "Il faut être sur la branch master pour déployer."
    exit 1
fi

echo "Opérations Git :"

# Opérations Git
git checkout master
git pull origin master
git tag "$TAG"
git push origin "$TAG"
