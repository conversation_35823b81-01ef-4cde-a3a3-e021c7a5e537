Ce dossier contient un ensemble de scripts dédiés au fonctionnement du projet est [disponible](docs/scripts), il suffit d'ajouter le dossier `docs/scripts/` à votre `$PATH` :

```
$ echo "export PATH=\$PATH:~/path/to/wizaplace/docs/scripts" >> ~/.bash_profile
```

Il est aussi nécessaire d'ajouter un [token GitHub](https://github.com/settings/tokens) dans la variable d'environnement `GITHUB_TOKEN` :

```
$ echo "export GITHUB_TOKEN=XXXX" >> ~/.bash_profile
```
Si vous utilisez zsh, remplacez `~/.bash_profile` par `~/.zshrc`

* [wiza-set-futur-prod-ip.sh](docs/scripts/wiza-set-futur-prod-ip.sh) : ajoute la liste des url de tous les clients pointant sur l'une des 2 IP de la future prod dans le fichier `/etc/hosts`,
* [wiza-remove-futur-prod-ip.sh](docs/scripts/wiza-remove-futur-prod-ip.sh) : supprime la liste des url de tous les clients pointant sur l'une des 2 IP de la future prod dans le fichier `/etc/hosts`,
* [wiza-git-deploy.sh](docs/scripts/wiza-git-deploy.sh) : détecte automatiquement le numéro de déploiement, créer un tag depuis _master_ et le push sur l'_origin_.
