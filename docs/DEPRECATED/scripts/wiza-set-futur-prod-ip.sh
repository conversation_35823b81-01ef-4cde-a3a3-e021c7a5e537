#!/bin/bash

IPS=`curl -s http://smithers.wizacha.com/deployTestIps`
DOMAINS=`curl -s http://smithers.wizacha.com/deployTestTargets`

# Génération d'un entier entre 1 et 3
INDEX=`echo $((RANDOM%3))`
INDEX=$((INDEX+1))

# Récupération d'une des deux IP aléatoirement
IP=`curl -s http://smithers.wizacha.com/deployTestIps | head -n "$INDEX" | tail -n 1`

# Suppression des anciennes IP de test (si présentes)
source wiza-remove-futur-prod-ip.sh

# Ajout des domaines dans le /etc/hosts pointant sur l'IP sélectionnée aléatoirement
echo "$DOMAINS" | while read domain
do
    echo "$IP $domain #deploy_test" | sudo tee -a /etc/hosts > /dev/null
done

echo "$IP status.wizacha.com #deploy_test" | sudo tee -a /etc/hosts > /dev/null
