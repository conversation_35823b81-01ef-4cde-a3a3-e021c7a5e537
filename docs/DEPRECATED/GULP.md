# Gulp

Gulp tourne sur NodeJs, installé par défaut avec Vagrant.

Les fichiers en relation avec Gulp sont `/node_modules/*`, `gulpfile.js`, `package.json`.

Installation
------------
1. Installer Gulp de manière globale sur votre poste à l'aide du gestionnaire de paquet de Node ('npm') : `npm install gulp -g`.

Remarques :
- Cette opération n'est à exécuter qu'une seule fois lors de la mise en place de l'environnement de développement.)

- Il est aussi possible d'installer gulp de manière locale uniquement (`npm install gulp`). L'executable sera alors accessible via `node_modules/.bin/gulp`.

2. A la racine du projet, lancer à la console : `npm install`.

La commande récupère la liste des dépendances dans le fichier 'package.json' et les installe en créant un répertoire 'node_modules' (fonctionnement similaire à `Composer`).
Ce répertoire n'est pas commitable et est donc répertorié dans le fichier '.gitignore'.

Remarque : Cette commande est à exécuter à chaque mise à jour du fichier 'package.json'.

