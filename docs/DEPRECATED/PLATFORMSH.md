# Platform.sh

To run the commands in this guide you will need to install the [Platform.sh CLI](https://docs.platform.sh/overview/cli.html):

```bash
# Install the CLI
curl -sS https://platform.sh/cli/installer | php
# Login
platform
```

We have a technical sandbox for the PRs. It is possible to setup an environment for the current branch (it must be pushed to the remote repository) to try the changes.

* `make platform-env-up`: activate the environment for the current branch
* `make platform-env-down`: delete the environment for the current branch

If you get the following error: `Specified project not found: sqsqw2lwo7z62` that means that someone must give you access to the project on Platform.sh.

## Déploiement

![Platform.sh](https://platform.sh/sites/default/files/styles/section_image/public/2017-05/psh-light%404x.png)

### Workflow

La branche principale devient `staging`, toutes les PR sont mergées sur cette branche. La branche `master` est considérée comme la branche de production. Il suffit donc de faire un merge de `staging` dans `master` pour déployer.

Un job jenkins sera créé pour merger `staging` sur `master` et créer le tag qui va bien (pour savoir ce qu'on release).

Chaque branche/PR de notre repo GitHub est synchronisée via les webhooks avec les projets sur platform. Il suffit d'aller sur platform pour activer un environnement pour un projet en particulier (par défaut uniquement `master` et `staging` sont _live_).

#### Déploiement

Utiliser le job jenkins pour merger sur `master` et créer le tag :

- Un job jenkins est lancé pour merger sur `master`
- Un job jenkins se lance pour créer le tag et la release
- Synchronisation avec les projets `Platform.sh` via les webhooks
- Build de chaque projet
- Deploy de chaque projet
- Live

#### Patch (plus ou moins)

Faire une PR avec `master` au lieu de `staging` comme base. Au besoin on peut ne pas attendre l'exécution des tests et merger directement. Au merge, un déploiement sera fait.

### Organisation

Il y a un projet `Platform.sh` par client en production. Chaque projet est synchronisé avec notre repo Github via les intégrations. De ce fait, un push sur `master` sur notre repo GitHub, va déclencher un déploiement pour chacun des projets.

### Configuration

La configuration de chacun des clients (ID projet, domaine, clés d'API diverses, etc) est stockée dans repo GitHub. Un push sur `master` sur ce repo GitHub déclenche une synchronisation de la configuration entre les données du repo (_source of truth_) et les projets sur `Platform.sh`. Actuellement il n'y a pas d'API permettant de créer un projet sur `Platform.sh`, c'est la seule étape manuelle.

Le repo de stockage de la configuration va ajouter les domaines configurés au projet `Platform.sh`, configurer les `CNAME` via `route53`, activer l'intégration GitHub, ajouter le webhook à notre repo.

Un push sur `master` sur ce repo de configuration déclenche une synchronisation via un job jenkins.

#### Ajout d'un client

- Création du projet sur `Platform.sh`
- Récupération de l'ID du projet
- Ajout du projet et des paramètres dans le repo de stockage de la config
- Ouverture d'une PR (review possible)
- Au merge sur `master` la configuration est synchronisée et le nouveau client est _live_.

## Utiliser xdebug sur platform

Prérequis : 

- Connaître l'id du projet platform.sh (http://smithers.wizacha.com/wizacha/projets.html  peut aider)
- Avoir un compte sur Platform.sh
    - avoir les droit sur le projet correspondant (voir avec la team infra si ce n'est pas le cas)
    - avoir renseigné sa clé ssh chez platform.sh

À partir de là, il faut pousser un commit sur le repository platform.sh qui activera xdebug.
- Ajout du repository distant : `git add remote nom_projet <EMAIL>:id_projet.git`
- dans le fichier `.platform.app.yaml`  ajouter `xdebug` dans la section `runtime > extensions` 
```$yaml
runtime:
    extensions:
        - redis
        - xdebug
```
- pousser le commit sur le repository distant `git push nom_projet HEAD:master` (master si c'est sur la branche principal, sinon, on peut pousser sur n'importe quel environnement)
- Récuperer l'url de connection ssh à l'environnement
- Lier le port xdebug de l'environnement avec le port 9000 local : `ssh -R 9000:localhost:9000 url_de_connection`
- Configurer l'IDE pour accepter les connections xdebug

Il n'y a plus qu'à identifier d'où vient le bug.
