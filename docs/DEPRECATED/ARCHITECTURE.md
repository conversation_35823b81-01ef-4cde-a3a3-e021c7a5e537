# Architecture

Objectif de ce document :

- Documenter l'architecture actuelle de l'application et définir l'architecture cible vers laquelle on souhaite tendre
- Documenter certains choix techniques sur des problèmes récurrents pour uniformiser la base de code et gagner du temps sur ces choix dans le futur

## Sommaire

1. Architecture globale
2. Organisation en modules
3. Architecture d'un module

## 1. Architecture globale

La solution Wizaplace offre les services suivants :

- Un back-office administrateur
- Un back-office vendeur
- Un front-office e-commerce
- Une API destinée aux administrateurs, vendeurs et front-offices

Ces services sont fournis par 2 applications :

- L'application Wizaplace : les 2 back-offices et l'API
- L'application front-office e-commerce

### Application Wizaplace

L'application Wizaplace (projet [`wizaplace/wizaplace`](https://github.com/wizaplace/wizaplace)) est une application PHP, Symfony, MySQL, etc.

Les 2 back-offices sont en fait la même application : les droits de l'utilisateur (vendeur ou admin) impactent ce qui est affiché et possible.

Cette application est déployée comme une application PHP classique sur Platform.sh. Le même code est déployé pour tous les clients. Certains clients ont du code spécifique via le système de bundle Symfony, mais le projet `wizaplace/wizaplace` contient tout le code.

L'application Wizaplace est un monolithe. Nous choisissons volontairement (pour le moment) de ne pas créer de micro-services car :

- les architectures en micro-services demandent un effort supplémentaire (développement, infrastructure, maintenance, …)
- découper en micro-services implique d'avoir déjà des frontières clairement identifiées et validées entre les modules, ce qui n'est pas forcément le cas aujourd'hui dans notre application

### Application front-office e-commerce

Chaque client a une application front-office e-commerce spécifique : cela permet d'intégrer le design et le comportement spécifique que veut chaque client sur son site de vente.

Chaque front-office est donc un projet GitHub séparé. Ces applications sont des applications Symfony et sont appuyées sur l'API de l'application Wizaplace pour afficher les produits, les paniers, etc. Il n'y a donc pas de base de données sur ces applications.

Nous réalisons la majorité de ces front-offices (en sous-traitant le développement) mais nos clients sont libres de créer un front-office eux-mêmes en utilisant nos APIs.

Pour industrialiser la réalisation de ces front-offices, nous avons créé un [SDK PHP](https://github.com/wizaplace/wizaplace-php-sdk) et un [StarterKit](https://github.com/wizaplace/starterkit). Le StarterKit est une application Symfony template que nous clonons à chaque nouveau front-office.

## 2. Organisation en modules

Le code métier est [organisé par modules fonctionnels](http://tech.wizaplace.com/posts/organisation-du-code-par-modules-fonctionnels) dans l'application Wizaplace. Chaque module est un namespace dans le dossier `src/Marketplace`.

Exemple de module :

- PIM : gestion du catalogue par les vendeurs et administrateurs
- Catalog : catalogue publié côté front
- Basket : paniers utilisateurs
- Order : gestion des commandes
- Accounting : comptabilité pour les vendeur et administrateur
- CMS : gestion du contenu comme les menus, pages de contenu, …

Chaque module représente une fonctionnalité isolée de la marketplace. Ces modules sont très fortement inspirés du principe des *Bounded Contexts* du Domain Driven Design.

### Interconnexion des modules

**Un énorme focus est mis sur l'API des modules**. Nous visons à ce que l'API de chaque module représente les actions métier derrière. L'implémentation des modules est presque secondaire. En effet nous savons que tous nos modules sont voués à évoluer (l'application et ses fonctionnalités sont en plein développement) mais **l'interconnexion entre ces modules doit rester claire et stable**. C'est ce qui nous permettra d'évoluer et refactoriser en toute confiance.

Clairement c'est un voeu pieux aujourd'hui : les modules ne sont pas toujours séparés et les logiques métier sont souvent mélangées. Cependant c'est la direction que nous essayons de mettre en place sur les nouveaux développements et les refactorings.

Nous essayons de suivre les mêmes règles de conception pour les modules que les principes de conception orientée objet :

- Chaque module doit être découplé autant que possible des autres modules.

  Par exemple les promotions impactent très fortement les totaux du panier : les promotions sont gérées dans un module séparé du panier et l'interaction avec le panier est limitée et identifiée.

  Ce principe est à tempérer : nous n'appliquons pas (encore) un découplage strict. Sylius par exemple met systématiquement en place des interfaces PHP et évènements pour découpler les modules : cela représente un effort conséquent et une complexification non négligeable. Sylius est un framework ouvert aux extensions (ou chaque module peut être remplacé), leur approche est donc compréhensible. Wizaplace est une application et non pas un framework, le découplage est surtout (pour le moment) une affaire de simplifier le travail et la maintenance. Si un léger couplage (clair et précis) entraine une grosse simplification nous préférerons ce choix.

  Par exemple le Catalog utilise parfois les mêmes tables que le PIM : pour récupérer les catégories, attributs, etc. Les données sont justes traitées différemment. Cela rend les 2 modules couplés. À l'heure actuelle cela est un compromis acceptable.

- Un module peut utiliser un autre module, il est dans ce cas dépendant de cet autre module. On évite autant que possible les dépendances circulaires.

  Par exemple le module SEO gère les "slugs" (transformation de noms en URLs uniques, par exemple `Chaussure Nike` en `chaussure-nike`). Le PIM utilise ce module pour générer des slugs pour les produits. À l'inverse le module SEO ne connait pas le PIM.

  Parfois le sens des dépendances n'est pas évident : est-ce que le PIM publie un produit dans le Catalog ou est-ce que le Catalog va chercher les infos dans le PIM ? Est-ce que le Basket crée une commande dans Order ou est-ce que Order crée une commande à partir d'un panier ? À priori le choix n'est pas critique, il faut privilégier en premier lieu ce qui a du sens au niveau du métier et essayer de garder une cohérence avec les dépendances existantes (donc si le Catalog pioche déjà dans le PIM continuer dans ce sens, ou inversement).
 
