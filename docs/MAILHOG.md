# MailHog

MailHog is an email testing tool for developers:
- Configure your application to use MailHog for SMTP delivery
- View messages in the web UI, or retrieve them with the JSON API

[Offical documentation](https://github.com/mailhog/MailHog)

## Install

see [Docker documentation](DOCKER.md)

## UI

To be able to access to UI `http://localhost:8025`

## Wizaplace integration

You don't have to install something in a normal local docker environment. It's already well configured.

However if your `FRONTEND_THEME` theme is set to `basic`, you have to set this env parameter
`MAILER_DELIVERY_ADDRESS` with a value which is not ''.
 
