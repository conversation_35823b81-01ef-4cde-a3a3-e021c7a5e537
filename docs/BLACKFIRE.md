# Blackfire.io

## Intro

[Blackfire.io](https://blackfire.io/) can be used at any stage of your application lifecycle - development, testing, staging, and production - to profile, test, debug, and optimize performance.

## Configuration

- Creating an account : https://blackfire.io/signup
- Retrieve the credentials : https://blackfire.io/my/settings/credentials

or

- Ask <NAME_EMAIL> blackfire account credentials
  > We paid for the single user "profiler" subscription [(détail)](https://blackfire.io/pricing). So make sure no one is already using it (ask in #devteam slack channel)

then

- Add environment variable in file `.env` filled with the server and client credentials

```env
# Blackfire
BLACKFIRE_SERVER_ID=
BLACKFIRE_SERVER_TOKEN=
BLACKFIRE_CLIENT_ID=
BLACKFIRE_CLIENT_TOKEN=
BLACKFIRE_AGENT_SOCKET_PORT=8707
BLACKFIRE_LOG_LEVEL=1
```

## Enable blackfire

Use the docker-compose.override.yml.blackfire.dist and restart docker containers.

```shell
ln -sf docker-compose.override.yml{.blackfire.dist,}
make docker-dev-restart
```

## Usage

### Profile URL

Run command

```shell
docker-compose exec php blackfire curl [URL]
```

Or directly from the browser
https://blackfire.io/docs/profiling-cookbooks/profiling-http-via-browser

### Profile CLI (ex.: Symfony command)

Run command

```shell
docker-compose exec php blackfire run \
    bin/console my:symfony:command
```

## Disable blackfire

```shell
rm docker-compose.override.yml
make docker-dev-restart
```
