# Abonnements

## Feature flag et mise en place

Pour activer la feature Abonnement (Subscription), il faut renseigner dans le .env les features flags suivants:
```
SUBSCRIPTION=1
CONFIG_ENABLED_SYSTEM_OPTIONS=payment_frequency,commitment_period
```
Le premier active la feature.
Le second définit les options systèmes à avoir pour que la feature fonctionne.

Pour que ces options systèmes soient déployées et disponibles dans le BO il faut lancer la commande suivante :

``bin/console deploy:command:system-options create``

## Création d'un produit de type Abonnement

Tout d'abord depuis le BO, se rendre sur la page des options. Sélectionner et éditer chaque option système
(fréquence de paiement et période d'engagement) et aller à l'onglet catégorie. Sélectionner la catégorie sur laquelle vont
s'appliquer les options. (Cela est nécessaire pour avoir des abonnements)

Pour créer un produit de type Abonnement, se rendre sur la page de création d'un produit dans le BO.

Cocher les cases `type abonnement` et `service ou bien téléchargeable` et enregister votre produit.

Ensuite il faut définir des déclinaisons à ce produit, choisir les combinaisons souhaitées, et enfin renseigner les
quantités et prix de chaque déclinaison.

## PSP

La feature abonnement fonctionne avec le psp Hipay.

Ajouter dans le BO la méthode de paiement Hipay Carte.

Renseigner les credentials dans le .env (https://wizaplace.atlassian.net/wiki/spaces/WI/pages/765460608/Compte+de+test+Hipay)

À savoir : Il se peut qu'il y ait un problème avec les numéros de commandes au moment de passer une commande. Cela est dû au
fait que les numéros de commande sont déjà existants coté hipay. Pour résoudre ce problème en local, il suffit
d'incrémenter l'id de la table cscart_orders en bdd.
