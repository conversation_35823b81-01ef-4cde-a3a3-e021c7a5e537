## Available offers

La disponibilité des offres permet d'associer des produits à des zones géographiques.
Les clients de la marketplace sont associés a une zone géographique.

Au moment du checkout du panier, une vérification est faite pour contrôler que la division du client
est incluse dans celles des produits du panier.
Si oui les étapes du checkout continuent et sinon le client ne peut pas checkout son panier.

Le catalogue n'est pas impacté par les divisions, c'est-à-dire que tout le monde voit le même catalogue,
indépendamment des divions paramétrées.

## Feature Flag

Variable d'env: `AVAILABLE_OFFERS= 0|1`

Symfony: `feature.available_offers: true|false`

La feature "disponibilité des offres" est dépendante de la feature [Divisions](./docs/PRODUCT/DIVISIONS.md)
qui concerne les différentes zone géographiques. il faut donc activer les 2 feature flags et avoir
les données de divisions présentes en bdd.

En local et sur les sandbox, il peut être intéressant de désactiver le cache http car sur certains endpoints,
on voudra avoir le résultat de nos modifications directement, sans attendre l'invalidation du cache (15 minutes)

Petit récap :
```
FEATURE_ENABLE_DIVISIONS=1
AVAILABLE_OFFERS=1
FEATURE_CACHE_HTTP=0
```

## Principes

### Configuration sur 3 niveaux

Les divisions sont configurées sur 3 niveaux d'administration : Marketplace, marchand et produits.
Pour chaque niveau, il y a la possibilité d'inclure et d'exclure des zones.

Une zone se représente elle-même ainsi que tous ses enfants.

Le résultat des divisions effectivement activées sur un marchand ou produit, correspond à l'intersection des zones
configurées sur les niveaux supérieurs. Sur lesquelles, on va enlever l'union des zones exclues.

##### Illustration avec des équations

Divisions active MP = inclues MP - exclues MP

Divisions active MD = (inclues MP ⋂ inclues MD) - (exclues MP ⋃ exclues MD)

Divisions active P = (inclues MP ⋂ inclues MD ⋂ inclues P) - (exclues MP ⋃ exclues MD ⋃ exclues P)

 *Avec : MP = Marketplace; MD = Marchand; P = Produit*

##### Exemple

|             | Inclues     | Exclues|
| ----------- | ---------- | ------ |
| Marketplace | FR, ES, IT | FR-69  |
| Marchand    | FR         |        |
| Produit     | FR-ARA     | FR-42  |
=> Le produit en question sera accessible uniquement pour la région Auvergne-Rhône-Alpes
hors départements FR-69 et FR-42.

##### ALL is love
Le mot clef `ALL` correspond à la divisions Root. Comme le résultat est une intersection des 3 niveaux,
l'utilisation de cette division revient à déléguer la restriction des divisions aux autres niveaux de configurations.
De manière dynamique, si la Marketplace change son scope de divisions actives, les entités configurées avec `ALL`
verront leur scope évoluer également.

##### Exemple

|             | Inclues     | Exclues|
| ----------- | ---------- | ------ |
| Marketplace | FR, ES, IT | FR-69  |
| Marchand    | ALL        |        |
| Produit     | ALL        |        |
=> Le produit sera accessible sur les zones FR, ES, IT hors département FR-69.

Si l'on ne souhaite pas que le scope d'un marchand s'agrandisse dynamiquement, il suffit de renseigner
un scope défini à la place de `ALL` (pour l'exemple ci-dessous : FR, ES, IT).

##### Cohérence
Il n'y a pas de contrôle de cohérence entre les niveaux, c'est le principe d'intersection qui fait foi.

Si l'utilisateur renseigne des données incohérentes, le résultat sera simplement null.

##### Exemple

|             | Inclues     | Exclues|
| ----------- | ---------- | ------ |
| Marketplace | FR         | FR-69  |
| Marchand    | FR, DE     |        |
| Produit     | DE         |        |
=> Aucune division ne sera accessible sur ce produit.

### Tables de liaisons

Les configurations de divisions, représentées par des zones, sont ensuite converties en données
unitaires.
C'est-à-dire que l'on va activer/désactiver toutes les divisions correspondantes pour chaque entitées.

C'est fait de façon assez différente pour chaque niveau de configuration.

**Marketplace** : un champ `is_enabled` dans la table `doctrine_divisions`

**Marchand** : Table de liaison `doctrine_division_blacklists` chaque enregistrement lie un marchand
et une division exclues, c'est-à-dire qu'on y enregistre toutes les divisions désactivées pour un marchand.

**Produit** : Table de liaison `doctrine_division_products` entre un produit et les divisions activées sur ce produit.

Cette conversion des "configurations par zones" en "listes de divisions" est lancé en cascade
après chaque enregistrement d'une configuration de division.

Si l'on modifie la configuration Marketplace, cela va recalculer les divisions de tous les marchands et tous les produits.
Si l'on modifie un marchand, cela va recalculer les divisions de lui-même et de ses produits.
Si l'on modifie un produit, cela va recalculer uniquement ses divisions.

### PIM

Vous pouvez vérifier la bonne génération des listes de divisions via le PIM `/products/{id}`
sous la propriété `divisions`. Elles sont accessibles sous forme d'arbre avec `/products/{id}/division-tree`.

Cette API existe également pour la marketplace et les marchands `/products/{id}/division-tree`
et `/companies/{id}/division-tree`.

### ReadModel / Catalogue / Moteur de recherche

La liste des divisions activées d'un produit est renseignée dans sa projection Readmodel.
C'est cette liste qui est utilisée lors d'une recherche par division.
Par exemple : `/catalog/search/products?filters[offers]=XX`

Vous pouvez vérifier que les données sont bien dans le Readmodel via `/catalog/products/{id}`,
sous la propriété `divisions`.

### Valeur par défaut

La valeur par défaut des marchands et produits est `ALL` ce qui signifie (que tu vivras ta viiiie ...)
qu'il aura les mêmes divisions activées que dans les autres niveaux de configuration.

### Traitement asynchrone

Le dispatch des configurations vers les tables de liaisons, ainsi que la génération du readmodel
des produits sont réalisés de manière asynchrone. Une file est dédiée à ces traitements : `feature_available_offers`.

## Déploiement

La commande `product:divisions:init` permet de renseigner la valeur `ALL` dans la configuration des divisions inclues
pour tous les marchands et produits. Elle lance ensuite la regénération des listes de divisions.

Cette commande ne modifie pas la configuration des divisions de la marketplace.
Il faut les configurer avant de lancer la commande.
