# Translations

Le système de traduction par défaut est maintenant le composant _translation_ de Symfony. La fonction `__()` de CsCart fait désormais appel au `translator` pour traduire la chaîne demandée.

Le `translator` de Symfony va mettre en cache dans un fichier toutes les traductions de la base de données. Chaque fois que ces traductions sont mises à jour, le cache est invalidé pour la _locale_ courante.

## Files

The Symfony command `translation:update` is deprecated, use `marketplace:translation:update` instead. For more help run:

```shell
$ bin/console marketplace:translation:update -h
```

### Theme

Les traductions des thèmes sont désormais dans le dossier `src/<theme>Bundle/Resources/translations` et sont nommées de la façon suivante `messages.<locale>.json`. Ces traductions sont désormais disponibles via le `translator` Symfony. En développement il n'y a pas besoin de charger les traductions en base pour qu'elles fonctionnent.

Pour mettre à jour le fichier de traduction du thème :

```shell
$ bin/console marketplace:translation:update --force --prefix="<prefix>" <locale> <theme>
```

* `--prefix="<prefix>"`: Override the default prefix [default: "__"]
* `<locale>`: language file to update
* `<theme>`: Update translation of the specifid `<theme>`

### Traductions de base

Les traductions de base sont dans le dossier `src/AppBundle/Resources/translations` et sont nommées de la façon suivante `messages.<locale>.yml`. **Ne pas utiliser les fichiers `addon.xml`**

Pour mettre à jour le fichier de traduction de base :

```shell
$ bin/console marketplace:translation:update --force --prefix="<prefix>" <locale>
```

* `--prefix="<prefix>"`: Override the default prefix [default: "__"]
* `<locale>`: language file to update

## Translation strategy

### Back office

It's decided to allow to edit a product one language at a time. The user will select the language he wants to edit and all the fields will expose with this language.

### Storage

The data is stored in 2 tables. The master table will contain fields that are not translated, the linked table will contain the translated fields (title, description, etc) with a fix set of columns meaning a new translation is a new row (not an extra column).

### API

Only one language is exposed per request, the language exposed is the one asked in the http header `Accept-Language`. This goes for backend and frontend endpoints as the line between the two is blurring.

When the language is not provided use the default language.

### PHP

By default we handle one locale per php process, the locale is stored in a global state via a singleton (see symfony translator) and not via a constant.

When the language needs to be changed during the process it will me manually done on the singleton (see symfony translator, don't forget to reset the state), this case can happen for a notification where a customer may use french, the company english and the admin spanish.

## POC MVP

We are going to try to map the doctrine MVP entities in a way that the entity returned contains the data in the language of the request. This is done to avoid the n+1 request (fetching first the master then entity containing the translation uppon use), and the eager loading alternative loading all translations all the time.

## Activation d'une langue

En tant qu'administrateur de la marketplace il faut se rendre sur [cette page](http://wizaplace.loc/admin.php?dispatch=languages.manage) pour ajouter une nouvelle langue, il faut bien la laisser en _cachée_ pour l'instant car le _frontend_ n'est pas fonctionnel en multilingue. Ensuite il faut traduire le catalogue [ici](http://wizaplace.loc/admin.php?dispatch=languages.translations).

Pour les partie dynamiques il faut commencer par les différents statuts :

* [Raisons RMA](http://wizaplace.loc/admin.php?dispatch=rma.properties&property_type=R)
* [Gestion des modes de livraison](http://wizaplace.loc/admin.php?dispatch=shippings.manage)
* [Statuts de commande](http://wizaplace.loc/admin.php?dispatch=statuses.manage&type=O)
* [Statuts des retours](http://wizaplace.loc/admin.php?dispatch=statuses.manage&type=R)
* [Taxes](http://wizaplace.loc/admin.php?dispatch=taxes.manage)
* [Sections d'options](http://wizaplace.loc/admin.php?dispatch=settings.sections)

Une fois tous ces éléments traduits, les vendeurs peuvent a présent traduire leur catalogue. Il faut quand même penser à traduire :

* [Catégories](http://wizaplace.loc/admin.php?dispatch=categories.manage)
* [Attributs](http://wizaplace.loc/admin.php?dispatch=product_features.manage)
* [Options](http://wizaplace.loc/admin.php?dispatch=product_options.manage)

## Détection de la langue

### API

On regarde la langue présente dans le header `Accept-Language` pour les méthodes safe et `Content-Language` pour les autres, si celle trouvée est une géré par la marketplace alors elle est utilisé pour `GlobalState::interfaceLocale()` **et** `GlobalState::contentLocale()`. Si la langue détectée n'est pas supportée alors on retourne une `406 Not Acceptable` au client. Si le header n'est pas présent ou vide alors on fallback sur la méthode de détection _non API_.

### Non API

On va chercher dans l'ordre pour la langue de l'interface:

* dans la query string la clé `sl`
* dans la session la clé `sl`
* dans les préférences de l'utilisateur connecté
* dans le header http

Si aucune de ces stratégies ne trouve une _locale_ alors on fallback l'interface sur le paramètre `%locale%` de symfony

Pour la langue du contenu on cherche dans l'ordre:

* dans la query string la clé `descr_sl`
* dans la session la clé `descr_sl`
* dans le header http

Si aucune de ces stratégies ne trouve une _locale_ alors on fallback sur celle de l'interface.

Pour les 2 chaines de stratégies si une _locale_ est trouvée alors on s'assure qu'elle fait bien partie de celles gérées par la marketplace, si ce n'est pas le cas on applique les fallback.
