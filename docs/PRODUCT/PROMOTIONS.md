# Promotions

Le module "Promotion" permet de créer des promotions :

- sur les produits
- sur les paniers

Méthodes principales :

- `PromotionService::getProductFinalPrice()`
- `PromotionService::getProductPriceCalendar()`
- `PromotionService::refreshBasketPrices()`
- `PromotionService::refreshBasketTotals()`

## Principe

Une promotion contient :

- des règles (le panier a un total > à 100€, le produit est dans la catégorie "téléphone", etc.)
- des bonus (réduction de 10€, réduction de 5%, etc.)
- chaque bonus vise un "target"/une cible (le prix du produit, le total du panier, les frais de port, etc.)

Une promotion s'applique si les règles sont vérifiées. Quand une promotion s'applique, ses bonus sont appliqués aux "targets".

## Promotions produit

Une promotion produit permet de réduire son prix de vente. Le prix de vente initial devient alors le prix barré ("crossed out price").

Par exemple pour un produit à 100€, une promotion de 10€ donnerait un "crossed out price" à 100€ et un prix à 90€.

Attention : le "crossed out price" peut également être renseigné directement sur les produits dans le PIM. Cela permet de gérer les promotions manuellement sur chaque produit sans avoir à créer des règles de promo. Lors de la publication du produit dans le catalog on prend donc soit le crossed out price du PIM (si il a été renseigné), soit le prix de base du produit si une promo s'applique.

## Promotions panier

Une promotion panier permet de modifier les prix du panier (le total, les frais de port, etc.).

Voici des use cases classiques (liste non exhaustive) :

- coupon permettant de gagner 5€ sur un panier
- frais de port gratuit si commande > 100€
- les écouteurs sont gratuits si on achète le téléphone qui va avec

## Promotions marketplace

Une promotion marketplace permet de modifier les prix du panier (le total, les frais de port etc.).
La différence entre la promo marketplace et la promo panier c'est au moment de transformer le panier en commande, le total ne doit pas comptabililiser la promo marketplace.
Cette promotion doit être traitée comme un mode de règlement pour l'acheteur et le vendeur

## Implémentation technique

Les règles sont écrite via un language textuel. Elle sont interprétées par Rulerz (librairie PHP). Rulerz a la capacité de pouvoir lire les règles et les évaluer :

- sur un objet PHP (donc pour vérifier si un objet satisfait les règles)
- sur une source de données, par exemple une table BDD (pour *chercher* les records qui matchent les règles)

Cette évaluation à double sens est cruciale :

- elle permet de vérifier en temps réel si un panier ou un produit a droit à des promotions (on évalue toutes les promotions actives sur le panier, et pour les produits on calcule le prix final de chaque produit lors de sa publication dans le Catalog)
- elle permet de chercher en BDD les produits concernés par une promotion qui vient d'être activée (évite d'itérer sur tous les produits ce qui serait beaucoup plus long)
