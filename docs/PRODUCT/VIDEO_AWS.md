# Vidéo AWS

## Configuration

Ajouter la configuration suivante dans le fichier .env:

```yml
AWS_BUCKET={client_bucket}
AWS_ACCESS_KEY_ID={client_key_id}
AWS_ACCESS_KEY_SECRET={client_key_secret}
VIDEO_SYSTEM_TEMPORARY_BUCKET=wizaplace-tmp
VIDEO_SYSTEM_PIPELINE=1471333054811-38py11
```

Cette configuration permet d'utiliser la fonctionnalité d'ajout et de suppression d'une image à un produit.
 Ces variables d'environnement permettent d’accéder au dossier source pour y uploader la video, puis après l'avoir transcodée elle est copiée dans le dossier de destination.
