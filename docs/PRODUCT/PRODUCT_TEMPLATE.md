# Product Template

aka "Fiche produit service"

For specifications, see https://docs.google.com/document/d/1tAwmlh3Bm_R1W540QKfe53UKYwJ74xe3pONEwJgdx1I/edit?usp=sharing

## Configuration

Configuration is done via the Symfony parameter `config_product_templates` and select the templates with the parameter `marketplace.pim.product_templates` (read from environment variable `PRODUCT_TEMPLATES` by default).

Example:

```yml
product: []
service:
  hidden_fields:
      w_green_tax: 0.0
      w_condition: 'N'
```

This will give us a standard "product" template with all the fields, and a "service" template where the field "w_green_tax" is hidden and has a default value of `0.0`.

Templates' display names are computed from translations based on this pattern : `product_template_{$templateName}`.
So on the example above, to change the display name of "service", you need to set the translation key `product_template_service

If only one template is available, the template field won't be shown on the product page.

## Implementation details

This feature uses a [Symfony Form](https://symfony.com/doc/3.4/forms.html) to handle the configurable display changes.
The main form type is `\Wizacha\Marketplace\PIM\Product\Template\ProductInfoFormType`.

The form starts with all the fields, but depending on the template type we replace some with hidden fields (this happens in `\Wizacha\Marketplace\PIM\Product\Template\TemplateService::customizeForm`).

Relevant files : (see https://github.com/wizaplace/wizaplace/pull/5840/files)
 - [src/AppBundle/Controller/CsCart/backend/products.php](../src/AppBundle/Controller/CsCart/backend/products.php) where we create the form and pass it to the smarty template
 - [design/backend/templates/views/products/update.tpl](../design/backend/templates/views/products/update.tpl) where we call the twig templates
 - [src/AppBundle/Resources/views/backend/product](../src/AppBundle/Resources/views/backend/product) where the Twig templates are
 - this whole directory
 - [app/addons/wizacha_backend_design/controllers/backend/products.post.php](../app/addons/wizacha_backend_design/controllers/backend/products.post.php) where the form data is restored after validation errors
