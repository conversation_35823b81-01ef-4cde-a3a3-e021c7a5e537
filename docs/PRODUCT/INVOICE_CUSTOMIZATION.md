# PDF Invoices

In order to customize the invoices, you can setup an invoice url template.
* This template is mandatory in order to customize your invoice.

* You can also provide a header template file and a footer template file.
The header and footer are optional. 


* Thoses files need to be available on internet and your backend must be able to access them. (You may need to enable CORS.)

* The template must be a twig template.


# Available Data

* ### Order
  * Access to the same data that is available through the `GET /user/order/{orderId}` endpoint. 
  * In addition, you also have access to the following data: 
    * taxTotal: this field correspond to the amount of taxes applied to this order.
    * itemId: You can access the id  of the item in the `item` key of the order.
    * amountExcludingTaxes: total price without tax.
    * greenTaxAmount: amount of green tax of this order.
    * shippingCostWithoutTax: total shipping cost without tax.

  * ### Company:
    * Access to the same data that is available through the `GET /catalog/companies/{companyId}`  endpoint.
    * In addition, you also have access to the following data: 
      * email: email of the vendor company.
      * legalStatus: status of the company.
      * siretNumber: SIRET number of the company.
      * vatNumber: VAT number of the company.
      * capital: capital of the company.
      * fax: fax number of the company.
      * url: address of the company website.
      * iban: IBAN number of the company.
      * rcs: RCS of the company.
      * bic: BIC number of the company.
      * legalRepresentativeFirstName: first name of the legal representative of the company.
      * legalRepresentativeLastName: last name of the legal representative of the company.  
      * nafCode: NAF code of the company.


* ### Payment Method 
  Access to the payment method used to pay the order. 

# Exemple access to the data in the template.

  * Access to the green tax amount of the order: ` {{ order.greenTaxAmount }} `
  * Access the total of an order: `{{ order.total }}`
  * Access the shipping address zipcode: ` {{ order.shippingAddress.zipcode }}`
  * Access the vendor company name: `{{ order.company.name }}`
  * Access the vendor company zipcode: `{{ order.company.zipcode }}`
  * Access the company SIRET number: ` {{ order.company.siretNumber }} `
  * Access the payment method: ` {{ paymentMethod }} `

