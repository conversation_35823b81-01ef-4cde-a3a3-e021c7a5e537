# Association de produits / Related products

Cette feature permet d'associer des produits relatifs à un produit parent.

Une association se définit par:
- le produit parent
- son type (voir l'enum dans le paragraphe Config/Enum)
- le produit relatif

## Config

### Enum

L'Enum `\Wizacha\Marketplace\RelatedProductRelatedProductType` définit tous les types d'association possible. 

Les types <strong>disponibles</strong> dans l'application sont listés dans le Feature Flag décrit juste après.
Par défaut, seul le type `recommended` est disponible.

Cette classe contient également la méthode `getTranslated()` qui est utilisée pour la traduction.
Cette méthode prend en compte le préfixe utilisé pour toutes les clés de traduction liées aux produits associées: 
`RELATED_PRODUCT_TYPE_PREFIX`.

### Feature Flag

Nouveau Feature Flag qui contient tous les types d'association disponibles au sein de la Marketplace:
`config.related_products.types (CONFIG_RELATED_PRODUCTS_TYPES)`

Les valeurs sont séparées par une virgule, sans espace. Par exemple:
`recommended,shipping,other`

Valeur par défaut: `recommended`

## Base de données

Nouvelle table: `doctrine_related_products`.

Création du type `\Wizacha\Marketplace\RelatedProduct\DoctrineType\RelatedProductsEnumType`
qui est utilisé dans la définition du mapping orm sur le champ `type` 
(`src\Marketplace\RelatedProduct\Mapping\RelatedProduct.orm.yml`).

### Services et repository

- `ConfigService`
  
  Le service `\Wizacha\Marketplace\RelatedProduct\ConfigService` s'occupe de contrôler la validité 
  du type (existance et disponibilité au sein de la Marketplace).
  
- `RelatedProductService`
  
  Le service `\Wizacha\Marketplace\RelatedProduct\RelatedProductService` contient:
    - des méthodes de modification sur la base de données:
      - addRelatedProduct(RelatedProductDto $dto, ?int $userId): RelatedProduct
        
        -> Dispatch un event RelatedProduct::EVENT_CREATE pour mettre à jour le ReadModel
        
      - deleteRelatedProduct(int $productId, ?int $relatedProductId, ?string $type) : void
        
        -> Dispatch un event RelatedProduct::EVENT_DELETE pour mettre à jour le ReadModel
        
      - deleteRelatedProductById(int $relatedProductId): void
        
        -> Dispatch un event RelatedProduct::EVENT_DELETE pour mettre à jour le ReadModel
      
      - updateRelatedProduct(RelatedProductDto $dto, ?int $userId): RelatedProduct
      
        -> Dispatch un event RelatedProduct::EVENT_UPDATE pour mettre à jour le ReadModel

    - des méthodes de recherche:
      - findByFromProductId(int $productId): array
      - findByFromProductIdForBO(int $productId): array
      - findBy(array $filters): array
      - findByToProductId(int $productId): array
    - des méthodes de mise en forme pour affichage:  
      - toArrayForBO(RelatedProduct $relatedProduct, Product $product): array
      - getAvailableTypesToString(): string
      - getAvailableTypes(): array
    - des méthodes de contrôle:  
      - isValidType(string $type): bool

- `RelatedProductRepository`
  
  Le service `\Wizacha\Marketplace\RelatedProduct\RelatedProductRepository` contient:
  - des méthodes d'action sur la base de données:
    - save(RelatedProduct $relatedProduct): RelatedProduct
    - delete(RelatedProduct $relatedProduct): void
  - des méthodes de recherche:
    - findByFromProductId(int $fromProductId): array
    - findByToProductId(int $toProductId): array

## RelatedProductSorter

Cette classe `\Wizacha\Marketplace\RelatedProduct\RelatedProductSorter` définit le tri par défaut mis en place 
dans le BO, sur l'onglet Produits associés dans la fiche produit.

## ReadModel

Ajout de la méthode `onRelatedProductOffersUpdate()` dans `src/Search/EventSubscriber.php` qui va être appelée sur:
- RelatedProduct::EVENT_CREATE
- RelatedProduct::EVENT_DELETE
- RelatedProduct::EVENT_UPDATE

Cet event va mettre à jour le ReadModel, la nouvelle propriété `relatedOffers`, du produit parent 
(`updateRelatedProductOffersProjector()`).

En cas de modification (activation, désactivation, suppression, modification d'une propriété, ...) d'un produit, 
on va rechercher tous les parents qui ont ce produit comme enfant, et on met à jour le `relatedOffers` de 
ces parents (`updateParentRelatedProductOffersProjector()`). On ne met pas à jour l'intégralité des propriétés
des parents, seulement la propriété relatedOffers, pour ne pas alourdir le système.

Dans les 2 cas mentionnés au dessus, si le produit parent concerné fait partie d'une FPU, 
alors la propriété `relatedOffers` de la FPU est également mise à jour (`updateMVPRelatedProductOffersProjector`).

## BackOffice

L'onglet `Produits associés` a été ajouté dans
`addons/wizacha_backend_design/controllers/backend/products.post.php`.

Le template associé au contenu de cet onglet est
`design\backend\templates\views\products\products_related.tpl`.

Le template associé à la popup d'ajout d'association est
`design\backend\templates\views\products\related_products_add.tpl`.

La liste produits, résultat de recherche dans la popup, est
`design\backend\templates\views\products\components\products_related_search_list.tpl`.

## Export / Import

Le schéma d'export et d'import est définit dans `app\schemas\exim\related_products.php`.

Un nouveau type `RELATED_PRODUCTS` a été ajouté dans la `\Wizacha\Component\Import\JobType`.

Dans le fichier `src\Exim\Misc.php`, une nouvelle entrée a été ajoutée dans la constante 
de mapping `JOBTYPE_MAPPING`:

      ```'related_products' => 'RELATED_PRODUCTS'```

L'obligation de présence du champ `Company id` est conditionné par l'absence d'un `runtime.company_id`.

### Export

L'export est un export dédié.

Le layout d'export devant contenir par défaut des colonnes non requises, un layout par défaut est
créé dans la table `cscart_exim_layouts` lors du déploiement. La requête se trouve dans:
`migrations\20210720101100_add_exim_layout_related_products.php`

### Import

L'import est un import dédié.

L'option de menu d'import a été ajoutée dans `src\AppBundle\Backend\Menu\MenuExtension.php`.

L'intégralité de l'import est définit dans `\Wizacha\Exim\RelatedProducts::put`.

Une file d'attente lui est dédiée: `related_products_import`, de priorité 5.

Elle est définit dans `app\config\parameters.default.yml`, sous la clé `marketplace.queue.config`.

Et elle est associée à l'import dans `app\config\parameters.queues.yml`, sous la clé
`marketplace.function_to_dispatch`.

## Cscart/Exim

Dans `src/Cscart/Exim`, ajout d'une nouvelle clé pour l'import `import_skip_check_alt_keys` qui permet 
de ne pas contrôler la présence de clé alternative lors de l'import.
