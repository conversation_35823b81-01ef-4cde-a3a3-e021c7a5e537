# Catalog

Le module Catalog contient les infos du catalogue produit qui sont consultable par les acheteurs :

- produits
- catégories
- attributs
- vendeurs
- etc.

Ces informations sont également présentes dans le module PIM, voici une comparaison pour comprendre la relation PIM/Catalog :

|                            | Catalog                                                      | PIM                                                          |
| -------------------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| Cible                      | Acheteurs (Front-office)                                     | Vendeurs et administrateurs (BO)                             |
| Infos contenues            | Uniquement les produits/catégories/etc publiées. Contient que des infos publiques (pas de coordonnées vendeur par ex.). | Tous les produits/catégories/etc, même ceux désactivés, non modérés… |
| Interaction                | Read-only, achat des produits                                | CRUD                                                         |
| Représentation des données | Dénormalisée : optimisée pour la lecture                     | Normalisée : nombreuses tables, jointures, etc.              |

Le PIM constitue la source de vérité des données, le Catalog étant une duplication de celles-ci.

## Attributs

On retrouve les attributs à 3 endroits :
- (_deprecated_) dans chaque déclinaison, sous le nom de `features`, sous la forme de tableaux imbriqués,
- (_deprecated_) dans le produit, sous le nom d'`attributes`, sous la forme d'un tableau de `Wizacha\Marketplace\Catalog\AttributeValue`,
- dans le produit, séparé en 2 :
    - `ungroupedAttributesValues` : tableau de toutes les `Wizacha\Marketplace\Catalog\AttributeValue` n'appartenant pas à un groupe,
    - `groupedAttributesValues` : tableau de `Wizacha\Marketplace\Catalog\AttributesValuesGroup`.
