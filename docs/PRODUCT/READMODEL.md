# Readmodel

![](../images/Readmodel.png)

## Readmodel en JSON

### Vision d'ensemble

La PR https://github.com/wizaplace/wizaplace/pull/9633 modifie le Readmodel pour que les données soient sérialisées en JSON et non pas en PHP natif.

La sérialisation JSON est stockée dans la nouvelle colonne `product_catalog.product_json`. Les données anciennement sérialisées en PHP natif restent dans la colonne `product_catalog.product`.

Ces données sérialisées en JSON sont versionnées, c'est-à-dire que la version de la sérialisation est stockée dans la nouvelle colonne `product_catalog.version`. Enfin, la date de la sérialisation est stockée au format DATETIME dans la nouvelle colonne `product_catalog.updated_at`.

2 nouveaux index sont créés sur cette table : `product_catalog_updated_at_IDX` et `product_catalog_version_IDX`.

Les classes capables de désérialiser les données de `product_catalog` doivent implémenter l'interface `\Wizacha\Marketplace\ReadModel\denormalizer\ReadModelDenormalizerInterface` et elles doivent avoir le tag `read_model_denormalizer` dans le Service Container. L'interface définit une méthode `public function supports(int $version): bool` qui indique si l'hydrateur sait désérialiser les données d'une certaine version, et une méthode `public function hydrate(array $data): array` qui réalise la désérialisation.

Pour obtenir le bon hydrateur, on utilise la classe `\Wizacha\Marketplace\ReadModel\Denormalizer\ReadModelDenormalizerRegistry`. Cette classe connait tous les hydrateurs de Readmodel grâce à l'injection automatique et retourne le bon hydrateur à partir d'une version avec la méthode `public function getInstance(int $version): ReadModelDenormalizerInterface`. A tout moment on sait donc désérialiser les données de toutes les versions possibles.

Les données à sérialiser (instance de `\Wizacha\Marketplace\ReadModel\Product`) sont générées normalement (par exemple par `\Wizacha\Marketplace\ReadModel\ProductProjector`) et elles sont persistées en base par `\Wizacha\Marketplace\ReadModel\ProductRepository::save` qui se charge aussi de définir `version` et `updated_at`. La version est toujours la version courante de l'application ; on ne sait pas sérialiser suivant une ancienne version.

### Sous le capot

Rappel : la sérialisation JSON repose sur 2 processus : une normalisation (transformation d'objets en tableaux associatifs) et un encodage JSON (transformation de données structurées en une chaine de caractères au format JSON).

La classe `\Wizacha\Marketplace\ReadModel\Product` est un simple conteneur pour un tableau associatif `$data` avec un ensemble de getters sur les données de ce tableau.

La sérialisation se fait au moyen de la fonction `json_encode` car la classe `\Wizacha\Marketplace\ReadModel\Product` implémente l'interface `JsonSerializable` et une méthode `public function jsonSerialize(): array`. Le but de cette méthode est de réaliser la normalisation de l'instance. `\Wizacha\Marketplace\ReadModel\Product::jsonSerialize` se contente de retourner le tableau associatif `$data`. Comme `json_encode` est récursif, les valeurs de ce tableau associatif sont à leur tour normalisées. Certaines de ces valeurs sont des objets qui implémentent à leur tour `JsonSerializable`. Elles sont donc normalisées par leur méthode `public function jsonSerialize(): array`. Il s'agit des classes :
- `\Wizacha\Marketplace\Catalog\Attribute`
- `\Wizacha\Marketplace\Catalog\AttributeValue`
- `\Wizacha\Marketplace\Catalog\AttributesValuesGroup`
- `\Wizacha\Marketplace\Price\PriceComposition`
- `\Wizacha\Marketplace\SeoData`
- `\Wizacha\Money\Money`

Une fois toutes les normalisations effectuées récursivement, `json_encode` réalise l'encodage au format JSON de la structure de données résultante.

La désérialisation consiste à exécuter `json_decode` sur les données. On obtient une version décodée mais encore normalisée des données. PHP ne fournit pas de mécanisme pour réaliser la dénormalisation. On fait alors appel au `\Wizacha\Marketplace\ReadModel\Denormalizer\ReadModelDenormalizerInterface` adapté à cette version du readmodel, qui va s'occuper de la partie dénormalisation.

## Montée de version du sérialiseur

On peut corriger l'hydrateur d'une version s'il dénormalise mal les données de cette version. Par contre si on modifie une sérialisation, que ce soit en modifiant les méthodes `jsonSerialize` (directement *ou* indirectement), ou en modifiant les données stockées dans l'objet `\Wizacha\Marketplace\ReadModel\Product`, alors il faut décider si on monte de version de sérialisation ou pas.

Si on ne monte pas la version de sérialisation, alors il est impératif de s'assurer que les changements apportés à l'hydrateur sont rétro-compatibles avec les données actuellement en production.

Si on ne peut pas garantir cette rétro-compatibilité, il faut monter de version. Et dans ce cas il faut aussi s'assurer que les classes utilisées par les hydrateurs existants sont rétro-compatibles, si on les modifie.

Pour cette raison, il est indispensable de maintenir des tests unitaires pour les désérialisations de toutes les versions du readmodel.

## Gestion de la Migration

Le code est compatible avec une table `product_catalog` qui ne contient que des données sérialisées au format PHP. Chaque fois qu'un readmodel est récupéré, on essaie la sérialisation JSON puis la sérialisation PHP.

A partir du moment où le nouveau code est en place, les sérialisations sont réalisées en JSON version 1 au lieu d'être faites en PHP. Le readmodel va donc être migré petit à petit du PHP vers le JSON.

Pour forcer une régénération complète du readmodel dans la nouvelle version, on peut utiliser la commande `readmodel:create`.
