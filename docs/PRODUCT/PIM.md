# PIM

> Vous pouvez consulter le [Guide utilisateur Wizaplace](https://docs.wizaplace.com/) pour de plus amples informations.

**PIM**: [Product Information Management](https://fr.wikipedia.org/wiki/Product_information_management), ou Gestion de l'Information Produit.

Le module PIM est accessible par les vendeurs et les administrateurs. Il leur permet de gérer le catalogue dans son ensemble : produits, catégories, etc.

Le PIM de la marketplace est différent d'un PIM classique e-commerce (comme Akeneo), par exemple :

- les produits vendeurs sont modérés par les administrateurs de la marketplace
- plusieurs vendeurs peuvent vendre le même produit (c'est le principe des *fiches produit unifiées*)
- pas de multi-catégorisation

## Produits

Chaque produit appartient à un seul vendeur.

Un produit peut avoir plusieurs déclinaisons :
Une option vous permet de proposer des variantes pour un même produit (appelées déclinaisons). Elles permettent donc pour chacune de ces déclinaisons de gérer un stock et un prix pour chacune d'entre elle.

## Catégories

Les catégories sont modélisées sous forme d'arbre.

Les produits ne peuvent être ajoutés que dans une catégorie feuille car sinon les vendeurs auraient intérêt à ajouter leurs produits à des catégories racines pour augmenter leur visibilité.

Les produits ne peuvent être que dans une seule catégorie (pas de multi-catégorisation) car sinon les vendeurs auraient intérêt à ajouter leurs produits dans un maximum de catégories (au détriment de la pertinence du catalogue) pour augmenter leur visibilité.

## Fiches produit unifiées

**FPU/MVP** : Fiches produit unifiées/Multi-vendor products.

### État par défaut lors de l'activation des MVP

Si la fonctionnalité `multi_vendor_product` est activée :

- la fonctionnalité `multi_vendor_product_auto_link` est activée par défaut,
- la fonctionnalité `multi_vendor_product_auto_create_if_no_match` est désactivée.

L'activation par défaut de la fonctionnalitée `multi_vendor_product_auto_create_if_no_match` fera l'objet d'une autre demande/ticket/PR car elle induit notamment  une refonte assez profondes des tests fonctionnels et donc du fonctionnement réel de l'application.

### Règles

Une [matrice exhaustive](https://docs.google.com/spreadsheets/d/1P_8TbvpRNwP1qsDfALf0lBifB6qXdu-RYloWNFiBYMg/edit#gid=0) explique le comportement des _MVPs_ et des _Products_ dans l'application, selon leurs différents états potentiels.
_Ces règles vont très prochainement changer._

## Options de produits

Les options appliquées à un produit définissent les déclinaisons de ce produit.

### Relation avec les produits

Les options doivent être liées à des catégories de produits afin que celles si puissent être selectionnées pour les produits appartenant à une catégorie.

Les catégories selectionnées sur un groupe d'options ne sont plus appliquées sur les options appartenant à ce groupe.

## Attributs

Les attributs permettent aux administrateurs et vendeurs d'ajouter des champs/attributs sur les produits. Les attributs peuvent être utilisés :

- pour afficher des infos sur la page produit, par exemple les caractéristiques techniques d'un produit (sa taille, puissance, etc.)
- pour filtrer les produits dans la recherche, par exemple filtrer par couleur, etc.
- pour stocker des informations supplémentaires visibles uniquement dans le PIM, par exemple la marge du vendeur pour chaque produit, etc.

Les **attributs** sont généralement :

- soit des groupes hiérarchiques contenant d'autres attributs mais pas de valeur,
- soit des attributs finaux contenant une **valeur**, pouvant être :
  - l'id d'une réponse pré-définie (une variante),
  - un tableau d'id de réponses pré-définies (une liste de variantes),
  - un texte libre.

Pour ne pas confondre les attributs et leurs valeurs, ils sont explicitement nommés :

- `DetailedAttributes` pour les attributs,
- `AttributesValues` pour leurs valeurs.

Pour ne pas confondre avec les objects _Catalog_, les fonctions manipulant ces attributs contiennent le terme `Legacy` :

- `DetailedLegacyAttributes` pour les attributs,
- `LegacyAttributesValues` pour leurs valeurs.

Les Products et les MVPs partagent ces attributs, ils bénéficient donc des même fonctions pour les manipuler :

```php
$mvpService = $this->get('marketplace.multi_vendor_product.service');

// Récupération des attributs détaillés : arbo, tous les champs, etc.
$mvpAttributes = $mvpService->getDetailedLegacyAttributes($mvpId);

=> [
    "id de l'attribut (qui est un groupe)" => [
        'feature_id' => "id de l'attribut (qui est un groupe)"
        'value' => "nom de l'attribut (qui est un groupe)"
        ...data..
        'subfeatures' => [
            "id de l'attribut" => [
                'feature_id' =>  "id de l'attribut"
                'value' => "valeur de l'attribut"
                ...data...
                'subfeatures' => null
            ]
        ]
    ]
]


// Récupération des valeurs des attributs : tableau aplati, sans les groupes
$mvpValues = $mvpService->getLegacyAttributesValues($mvpId);

=> [
    "id de l'attribut" => "valeur de l'attribut"
]
```

La fonction de mise à jour des valeurs des attributs peut recevoir en paramètre ce qui vient d'être récupérer précédemment :

```php
$productService = $this->get('marketplace.product.productservice');

$productService->setLegacyAttributesValues($productId, $mvpValues);
```

Il n'existe pour l'instant pas de fonction de mise à jour des attributs détaillés du genre `setDetailedLegacyAttributes`.

### Free attributes

Les attributs libres sont plus simples à gérer, les fonctions `getFreeLegacyAttributes` et `setFreeLegacyAttributes` utilisent _presque_ la même structure de paramètre :

```php
$mvpService = $this->get('marketplace.multi_vendor_product.service');

// Mise à jour des attributs libres
$freeAttributes = [
    "Attribut 1" => [
        "Valeur 1 - 1",
        "Valeur 1 - 2",
    ],
    "Attribut 2" => "Valeur 2 - 1",
]

$mvpService->setFreeLegacyAttributes($mvpId, $freeAttributes);

// Récupération des attributs libres
$mvpAttributes = $mvpService->getFreeLegacyAttributes($mvpId);

=> [
   "Attribut 1" => [
       "Valeur 1 - 1",
       "Valeur 1 - 2",
   ],
   "Attribut 2" => [
       "Valeur 2 - 1"
   ],
]
```

La seule nuance est qu'il est possible de ne pas encapsuler les valeurs simple dans un tableau lorsque l'on met à jour un Product ou un MVP avec la fonction `setFreeLegacyAttributes`.

Mais lors de la récupération des attributs libres avec la fonction `getFreeLegacyAttributes`, les valeurs simples sont toujours encapsulées dans un tableau (que l'on peut repasser directement à la fonction `setFreeLegacyAttributes` qui accepte les deux formats).

## Exim : Export/Import

CSCart utilise des notions de _pattern_ pour effectuer ses exports, ces patterns sont définis dans des fichiers du type `schemas/exim/{type}.php` (où type est le type de données qu'on va manipuler, ie: `products`, `categories`, etc...).

Dans ces schémas on défini quels champs on peut importer/exporter et comment les extraire/insérer en base.

Puisque CSCart a généraliser les colonnes `lang_code` dans ses tables pour gérer son multilingue il réutilise ce principe ici aussi. Il est donc possible de définir une colonne qui lui permet de retrouver comment regrouper les éléments; chaque traduction est donc une nouvelle ligne.

### Export/Import des catégories

Si la colonne `Category ID` CSCart va essayer d'update la catégorie correspondante, donc s'il n'y en a pas la catégorie ne sera pas importée.
