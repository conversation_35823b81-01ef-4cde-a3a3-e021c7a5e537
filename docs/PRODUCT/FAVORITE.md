# Favoris

Le module "Favorite" permet aux acheteurs de créer des listes de produits favoris.

Méthodes principales :

- `FavoriteService::addDeclinationToUserFavorites()`
- `FavoriteService::removeDeclinationFromUserFavorites()`
- `FavoriteService::getFavoritesByUser()`

Un acheteur (connecté) peut ajouter des produits à ses favoris.

Attention : ce sont en fait des déclinaisons qui sont ajoutées aux favoris. Cela permet ensuite de créer un panier à partir des favoris (ce sont les déclinaisons qu'on ajoute au panier, un produit en soit n'est pas achetable).
