# Attributs de produit / Product attributes

## EXIM

Le schéma d'export et d'import est définit dans le schéma `app\schemas\exim\product_attributes.php`.

Un nouveau type `PRODUCT_ATTRIBUTES` a été ajouté dans la `\Wizacha\Component\Import\JobType`.

Dan<PERSON> le fichier `src\Exim\Misc.php`, une nouvelle entrée a été ajoutée dans la constante
de mapping `JOBTYPE_MAPPING`:

      'product_attributes' => 'PRODUCT_ATTRIBUTES'

L'obligation de présence du champ `Company id` est conditionné par l'absence d'un `runtime.company_id`.

### Export

L'export est un export dédié.

L'option de menu d'export a été ajoutée dans `src\AppBundle\Backend\Menu\MenuExtension.php`.

Important: il n'a pas été possible d'utiliser le moteur d'export existant pour les raisons suivantes:

- L'export complet des attributs consiste en l'extraction de données de 2 tables différentes:
  - `cscart_product_features_values` pour les attributs
  - `cscart_product_metadata` pour les attributs libres
- on rencontre un problème pour la gestion automatique du multi langue avec le contenu de la 
table `cscart_product_features_values` (attributs) du fait qu'on n'a pas forcément toutes les langues 
présentes pour chaque valeur d'attribut.

La présence de plusieurs lignes pour un même attribut (cas du checkbox multi qui a plusieurs valeurs possibles)
pouvait, elle, se régler par un GROUP_CONCAT. 

Il a donc été ajouté une nouvelle propriété dans le schéma `export_custom_function`.
Cette propriété définit une fonction de callback qui va être appelée à la place de la construction de la requête
du moteur d'export. En retour, cette fonction fournit un tableau de données pré formatées pour être injectées
tel quel dans le fichier csv.

Le multi langue est également géré dans cette fonction de callback.

Le code d'extraction des données pour l'export est définit dans `\Wizacha\Exim\ProductAttributes::getData`.

Tous les autres mécanismes liés aux exports (mode async, mail) restent inchangés.

Le layout d'export devant contenir par défaut des colonnes non requises, un layout par défaut est
créé dans la table `cscart_exim_layouts` lors du déploiement. La requête se trouve dans:
`migrations\20210825102034_add_exim_layout_product_attributes.php`

### Import

L'import est un import dédié.

L'option de menu d'import a été ajoutée dans `src\AppBundle\Backend\Menu\MenuExtension.php`.

L'intégralité de l'import est définit dans `\Wizacha\Exim\ProductAttributes::put`.

## Cscart/Exim

Dans `src/Cscart/Exim`, ajout d'une nouvelle propriété pour l'export `export_custom_function` qui permet
d'appeler une fonction de callback pour récupérer les données, ce à la place de la requête construite 
dynamiquement par le moteur d'export.
