# Notifications

## Composant NotificationDispatcher

L'appli utilise un composant `Wizacha\Component\Notification\NotificationDispatcher` qui se charge d'écouter certains évènements
et de passer le relais à une class Notifier pour gérer l'envoi du mail.

Son fonctionnement est assez spécifique:
Pour chaque évènement écouté, si la notification est active (voir le paragraphe Activation des notifications),
le Dispatcher va récupérer le bon Notifier via un mapping (`NotificationDispatcher::getMapping()`). Puis il va chercher
à appeler la méthode de ce Notifier qui porte exactement le même nom que l'évènement. On ne map donc que l'évènement 
vers le Notifier à utiliser.

A noter que chaque classe présente dans le getMapping doit implémenter NotificationEvent. Cette interface apporte les méthodes suivantes:
- getDescription: récupération de la description qui s'affiche dans la liste des notifications dans le BO. Si cette fonction n'est pas présente alors la notification ne pourra pas être sauvegardée en base de données. Un message d'erreur apparaitra à l'écran.
- createFromForm et buildDebugForm: sont nécessaires pour le mode debug.

Petit exemple d'implémentation avec ces 3 bouts de code :
```php
# 1 - NotificationDispatcher

private static function getMapping() : array
{
    return [
        ShipmentCreated::class => 'app.notification.order_notifier',
...
```
```php
# 2 - OrderNotifier (app.notification.order_notifier)

public function shipmentCreated(ShipmentCreated $event)
{
    $this->sendEmailTo....()
    ...
}
```
```php
# 3 - ShipmentService

eventDispatcher->dispatch(
    ShipmentCreated::class,
    new ShipmentCreated(...)
);
```

1. On précise le mapping dans le Notifier : c'est le service `app.notification.order_notifier` qui gère l'évènement 
`ShipmentCreated`.
2. Dans `OrderNotifier`, on a bien une méthode qui s'appelle `shipmentCreated(...)`.
3. Lors de la création d'un Shipment, on n'a plus qu'à dispatch l'évènement `ShipmentCreated`

## Activation des notifications

Chaque event listé dans la fonction getMapping() doit avoir un enregistrement dans la table doctrine_notifications
pour définir si les notifications associées sont actives ou non (champ enabled). Si l'enregistrement n'existe pas dans 
la table, alors on considère les notifications comme actives.

A noter que lorsqu'on affiche la liste de toutes les notifications emails dans le BO, celles présentes dans le 
mapping et qui n'existent pas en base de données sont automatiquement créées comme actives.

Dans le cas de l'appel de la fonction dispatchDebug() qui permet de voir le contenu de la notification, même si les 
notifications sont désactivées, on appelle quand même le notifier car le but est juste de voir le résultat du mail. 
Seul un warning stipulant que les notifications sont désactivées est affiché dans le BO.

## Les classes Notifier

Elles héritent de la classe abstraite `Notifier` qui possède les méthodes d'envoi d'email à utiliser ex: 
`sendEmailToAdmin()`

Les classes se situent ici : `Wizacha\AppBundle\Notification`

## Template de mails

La plupart des notifications utilisent un template twig pour mettre en forme le contenu du mail, on peut lui passer des 
variables et gérer les traductions comme pour une vue classique. (voir la classe Notifier)

Les templates se situent ici : `src/AppBundle/Resources/views/mail/`

La page du back-office `Administration > Variables des emails` permet de consulter en mode superadmin les différentes
clés de traduction et les variables qui sont injectées dans ces templates.

Cette interface se charge de parcourir à la demande les différentes classes `Notifier` ainsi que les templates Twig
qui y sont chargés.  

## Status de commande

Via le back-office il est possible pour chaque statut de commande de définir à qui seront envoyés les notifications:

* envoi au client
* envoi à l'admin
* envoi au vendeur

## Langue

La langue d'envoi des notifications suit ces règles:

* envoi à un user: notif dans la langue du user (ou langue par défaut de la marketplace s’il n'en a pas de défini).
* envoi à un admin: langue par défaut de la MP, c'est un paramètre serveur: `APP_LOCAL`.
* envoi à un vendeur: langue du 1er admin de vendeur (ou langue par défaut de la marketplace s’il n'en a pas de défini).

Les notifications via Dolist ne sont pas affectées par la langue d'un user ou de la marketplace car leur api ne gère pas 
le multilingue, il n'y a que des ids de templates.

## DoList et autres implementation d'un Notifier

Les différents notifiers peuvent être surchargés pour fonctionner avec d'autre systèmes d'envoi de messages comme DoList.

Il faudra donc faire attention lorsque qu'un nouveau type de messsage est créé, de coder son implementation DoList également.

Voir les différentes classe `Notifier` de CashConverter pour l'exemple.
`src/CashconvertersBundle/Notification/OrderNotifier.php`
