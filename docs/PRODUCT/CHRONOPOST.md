# Chronopost

## Configuration dans le code

Activer la ou les fonctionnalités souhaitées grace aux _feature flags_ suivant :

```yml
feature.carrier.chronopost.chrono13: true
feature.carrier.chronopost.chronorelais: true
```

Configurer les informations de connexion à l'API de Chronopost :

```yml
chronopost_api_account_number=********
chronopost_api_password=255562
```

Remarque : ces identifiants sont ceux de test de Cash Converters.

## Configuration dans l'application

- En admin > créer un mode de livraison du type souhaité :
   - Chrono 13 API
   - Chrono Relais API
- En admin > activer ce nouveau mode de livraison.
- En vendeur > activer ce nouveau mode de livraison.

