# Workflow de commande

## Principe

Un workflow permet *seulement* de savoir où l'on se trouve dans l'avancement d'une commande mais ce n'est pas lui qui va faire avancer l'état de la commande.
C'est dans la marketplace que l'on va executer des actions métier (`validateByVendor->execute($order)`, `RefusePayment->execute($order)`, etc) qui vont faire avancer ce workflow.
Ces méthodes lèveront une exception si elles ne sont pas appelées au bon moment.

## Convertir les workflows en image

Il est possible de générer des images pour chacun des workflows existants sur la marketplace. Pour cela il suffit de lancer la commande suivante dans le conteneur Docker :

```bash
bin/console workflow:graphviz
```

La commande va générer des fichiers _*.dot_ dans le dossier _var/workflows_. Pour les convertir directement en PNG, il vous suffit de lancer la commande suivante :

```bash
cd var/workflows
find -type f -name "*.dot" | xargs dot -Tpng -O
```

Si la commande `dot` n'est pas disponible il est nécessaire d'installer la librairie `graphviz` via :

```bash
apt install -y graphviz
```

## Paiement par CB directement

![](./images/workflow/commande/paiement-cb.JPG)

([version lucidchart](https://www.lucidchart.com/documents/edit/101140c8-7cb8-4852-8c48-5ef4cb2c756f?shared=true&docId=101140c8-7cb8-4852-8c48-5ef4cb2c756f))

Lecture du tableau: L'enchainement des opérations possible se fait en lisant les numéros au dessus des commandes (cf légende en haut à gauche du tableau pour les couleurs). Si plusieurs commandes se suivent verticalement sans numéro entre elles c'est qu'elles sont conditionnelles, c'est à dire qu'il ne peut y en avoir qu'une parmis le groupe.

Le panier passe en commande au moment où l'utilisateur valide son panier, on a donc une commande avant que le paiement soit effectué.

Dans la commande 3 lorsque l'acheteur procède au paiement, l'utilisateur n'est redirigé sur le PSP qu'à condition que la commande soit bien passée en attente de paiement.

C'est le PSP qui nous appelle pour dire si le paiement a été validé ou refusé, on va mettre à jour la commande en fonction. Dans le cas d'un echec est-ce qu'on redirige l'utilisateur? Dans le cas du succès, le PSP a déjà prélevé les fonds et on a envoyé une notification au vendeur afin qu'il _traite_ la commande.

Le vendeur a le choix entre valider la commande (qui passe alors en `payée en attente de traitement`), refuser la commande (qui passe alors en `refusée`). S'il décide de refuser la commande il faudra que l'admin rembourse la commande, à terme cette commande devrait être faite automatiquement par la MP à la suite de l'évènement `commande refusée`.

L'étape suivante consiste au vendeur d'expédier la commande et de renseigner le numéro de facture de celle ci (actuellement ces 2 commandes sont faites en même temps).

La commande passe en `livrée` après une période de 7 jours (à terme cet évènement sera généré à la suite d'une commande d'un acteur).

A la suite de ce délai de 7 jours s'ensuit une délai de rétractation de 14 jours qui génèrera l'évènement `commande terminée, en attente de répartition` (s'il n'y a pas eu de demande de retour dans ce lapse de temps). Ce dernier va lancer automatiquement la commande de répartition des fonds, on sort alors du workflow de commande et on entre dans le workflow de _cash out_.
