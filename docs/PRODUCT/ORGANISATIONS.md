# Organisations

Les _Organisations_ représentent les entités morales au sein de la marketplace B2B. Elles chapotent directement une liste de _groupes utilisateurs_ ainsi qu'une liste d'_établissements_, et contiennent en elles même les informations directe à leur statut (tel que le SIRET/SIREN, adresse du siège social, etc).

## Organisation

Une organisation est unique au sein d'une même marketplace, c'est à dire que 2 personnes différentes ne peuvent pas demander la création d'une même organisation. La création d'une organisation est soumise à modération par l'admin de la marketplace.

Lors d'une création, un groupe _Administrateurs_ est créé automatiquement (et ne pourra jamais être supprimé), l'utilisateur qui a demandé cette création sera automatiquement ajouté à ce groupe. Par utilisateur on entend une personne quelconque en dehors de notre système, un _user_ (au sens de la marketplace) ne peut pas exister en dehors d'une organisation dans le contexte B2B.

Un admin peut créer une organisation lui même, auquel cas aucun utilisateur ne sera ajouté au groupe des administrateurs.

## Administrateurs

Ce groupe a les droits de manipuler les autres groupes utilisateurs de l'organisation et peuvent gérer la liste des établissements.

Ils ont accès à toutes les commandes de l'organisation.

## Groupe utilisateur

Cette structure est un regroupement nommé de plusieurs utilisateurs de la marketplace sur lequel s'applique une liste de droits définis. Un groupe peut être rattaché à un ou plusieurs établissements.

Un groupe ne possède pas lui même d'adresse mail, lors de notifications c'est l'ensemble des utilisateurs qui vont recevoir la notification (ceci dans le but d'éviter au client à devoir créer des mailing list pour chaque groupe).

Un groupe peut se retrouver sans utilisateur, par contre un utilisateur ne peut **pas** se retrouver sans groupe.

## Etablissement

Cette notion représente principalement une adresse pour laquelle on pourra faire livrer une commande.

## Droit

Un droit est juste un flag qui permet de savoir si un utilisateur a le droit d'effectuer une action.

## Panier

Dans ce contexte B2B un panier n'est plus rattaché à un utilisateur mais à une organisation puisque ça ne sera pas forcemment les même personnes qui pourront le constituer et faire le paiement.
