# Divisions géographiques

Les données de divisions géographiques se trouvent dans la table `doctrine_division`.
La table `division_descriptions` contient les données de traduction.
Elles sont gérées sous forme d'arbre hiérarchisé de type ["Nested Set"](https://en.wikipedia.org/wiki/Nested_set_model).

## Feature Flags

Variable d'env: `FEATURE_ENABLE_DIVISIONS= 0|1`

Symfony: `feature.enable_divisions true|false`

Ce feature flag doit être activé pour permettre d'importer les données de divisions dans la bdd.

## Migration des données

Une fois le feature flag activé :

Sur un serveur géré par une routine de déploiement, l'import sera lancé automatiquement via la commande
`DeployMigrateDivisionCommand` géré comme une migration.

Sur un environement local:

- Avec un reload-data les données des divisions seront chargées avec le reste des fixtures:
    1. `make docker-dev-xdebug-disable`
    2. `make docker-reload-data`

- Pour charger uniquement les divisions :
    1. Vider les tables `doctrine_divisions` et `doctrine_division_descriptions`
(elles contiennent parfois de vieilles fixtures)
    2. Désactiver xdebug `make docker-dev-xdebug-disable`
    3. Lancer la commande `docker-compose exec -T php bin/console deploy:command:migrate-division`

    Note: Cette commande ne peut être lancée qu'une seule fois car elle est traitée comme une migration,
il faudra donc l'enlever de la table migration si vous voulez la rejouer.

## Disponibilité des offres

Il est important de ne pas confondre la [disponibilité des offres](./docs/PRODUCT/AVAILABLE_OFFERS.md) et les divisions:
- Les divisions concernent uniquement les données géographiques
- La disponibilité des offres est l'utilisation de ces données dans le catalogue pour proposer des produits
 en fonction de la zone géographique de l'acheteur.
