# Reviews

Module permettant aux acheteurs de laisser des commentaires et des notes sur les produits ou les vendeurs.

Une "review" contient :

- une note
- un commentaire (optionnel)

Lorsqu'une review est postée par un acheteur, elle passe par une phase de modération par l'administrateur de la marketplace. Celui-ci peut approuver ou rejeter une review.

Les reviews peuvent être affichées sur les pages produit ou vendeur, et les notes moyennes peuvent être utilisées pour être affichées dans les résultats de recherche (par exemple).

Méthodes principales :

- `ProductReviewService::addReview()`
- `ProductReviewService::getAverageRating()`
- `ProductReviewService::getReviews()`
- `CompanyReviewService::reviewCompany()`
- `CompanyReviewService::listCompanyReviews()`
- `CompanyReviewService::getAverageRating()`
