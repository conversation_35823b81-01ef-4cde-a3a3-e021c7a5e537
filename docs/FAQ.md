FAQ & Troobleshooting
=====================

### Reload des fixtures

C'est le thème `basic` qui doit être utilisé.

### Controleurs legacy API

Ils sont dans src/AppBundle/Controller/Api/* (sans Controller).php

Ils utilisent les classes situées dans app/Tygh/Api/Entities/
Les champs des objects, avant d'être renvoyés dans la réponse sont filtrés : voir le fichier `display_objects.php`
Il peut aussi y avoir certaines conversion effectuées, voir les fichiers `convert_fields_api.php` ou `update_objects.post.php`

### Debug des workflows

Sur la page de détails d'une commande, ajouter à l'url '&debug_workflow'
Ex: http://wizaplace.loc/admin.php?dispatch=orders.details&order_id=1&debug_workflow

### Champs produits en contexte vendeur

Les champs produits (lors de l'ajout / édition) sont filtrés par ce fichier lorsque l'on édite en contexte vendeur :
`app/addons/wizacha_backend_design/controllers/backend/products.pre.php`

Il faut donc y ajouter les champs que l'on souhaite modifiables, sinon il seront rejetés.

### JS Back office

Il se trouve ici : `design/backend/templates/common/scripts.tpl`

### Tests qui fail a cause de données non présente (ex: company not found ?)

Vérifier que les fixtures (tests/data/import.sql) sont entièrement bien chargées lors de l'init des tests.
Généralement cela fail silencieusement donc c'est compliqué à tracer (essayer de réimporter le .import.sql manuellement après l'avoir converti via cscart).

### Phpcs

Pour désactiver le lint sur une partie de code, il faut mettre le code entre `// phpcs:disable` et `// phpcs:enable`.

Pour désactiver le lint sur une ligne, il faut précéder le code de `// phpcs:ignore`

### Champ produit ignoré lorsqu'un vendeur ajoute/édite un produit

Il faut ajouter le champ a la liste des champs autorisés de modifications par les vendeurs.
Voir fichier la fonction `fn_w_product_authorized_fields` dans le fichier :

```
app/addons/wizacha_backend_design/controllers/backend/products.pre.php
```
