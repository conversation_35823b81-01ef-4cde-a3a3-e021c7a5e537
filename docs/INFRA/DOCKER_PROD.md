# Docker

Warning the docker-compose is not use in production, it is used for testing docker images.

The real production environment use kubernetes.

## Installing/Configuring

### Requirements

* [Docker](https://docs.docker.com/install/#supported-platforms)
* [Docker compose](https://docs.docker.com/compose/install) >= 1.23

Test your versions with :
* `docker --version`
* `docker-compose --version`

#### Add current user to Docker group on Linux :

```
sudo usermod -a -G docker username
```

* Reboot system to apply this modification

### Git

Add your SSH key in your [GitHub's account](https://github.com/settings/keys) and get the project

```
<NAME_EMAIL>:wizaplace/wizaplace.git
cd wizaplace
```

### Configure environment

```
cp docker/prod/.env.dist docker/prod/.env
```

Customize environment variables in file `docker/prod/.env` or use the default values

### Build images

```
cd docker/prod
docker-compose build --pull
```

### start images

```
cd docker/prod
docker-compose up -d
```
