# Problématique générale

Lemonway exige une liste exhaustive d'adresse IP en provenance desquelles le trafic de nos environnements est autorisé.

# Legacy

Les environnements de production legacy et de pré-production legacy voient leurs adresses IP publiques changer à chaque déploiement.
Nous avons donc été contraint de mettre en place un proxy, vers lequel le trafic Lemonway des environnements legacy est envoyé, proxy qui relaye alors ce trafic vers Lemonway. C'est le trafic provenant de l'IP publique sortante de ce proxy que Lemonway autorise.

### Accès

Chaque environnement legacy de chacun de nos clients possède son propre couple de login/mot de passe d'accès au proxy, à des fins de sécurité et de facilité de maintenance.

Wizaplace a lui aussi ses propres accès qui sont détaillés dans la documentation du [PSP](../PSP/lemonway.md).

### Fonctionnement

Le proxy en question est le logiciel [Squid](http://www.squid-cache.org/), qui gère entre autres, et cela nous suffit, les protocoles HTTP et HTTPS.

Il intègre nativement une notion de cache pour les pages qui sont visitées fréquemment.

### Hébergement

Ce proxy est hébergé dans une machine virtuelle du réseau Wizaplace chez AWS.

Le déploiement est assuré via [Terraform](https://github.com/wizaplace/infra-tf/) et [Ansible](https://github.com/wizaplace/infra-ansible).

# Yavin

Les environnements Yavin disposent de leur propre IP publique fixe de trafic sortant, et ne nécessite donc pas d'utiliser un proxy pour le trafic Lemonway des environnements clients.

# Adresses IPs

* *************: IP sortante du proxy historique relayant le trafic Lemonway des environnements legacy (va être décommissionner sous quelques jours)
* ************: IP sortante du proxy actuel relayant le trafic Lemonway des environnements legacy (machine virtuelle AWS)
* ************* : IP sortante des production Yavin hébergée chez Azure
* ************* : IP sortante des sandbox Yavin hébergée chez Azure
