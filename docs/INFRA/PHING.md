# Phing

Phing est utilisé sur les environnements de prod. Il permet de faire ce que nous faisons manuellement dans notre fichier `.env`. 
La génération du fichier `build.properties` est faite par l'infra lors des déploiements.

## 1. Variable d'environnement

Définir la valeur de la variable env en utilisant le fichier `.env` situé dans le répertoire racine du projet.
```
MY_PROPERTY_NAME=value
```

## 2. Références

Référencer la variable en incluant le nom avec `env()` => [`app/config/parameters.env.yml`](./../../app/config/parameters.env.yml)
```
my_property_name: "%env(MY_PROPERTY_NAME)%"
```

## 3. Paramètres

Ajouter aux paramètres `env()` une valeur par défaut  => [`app/config/parameters.default.yml`](./../../app/config/parameters.default.yml)
```
env(MY_PROPERTY_NAME): ''
```

## 4. Propriétés intégrées

Ajouter une nouvelle propriété dans le fichier de configuration phing => [`build/config.xml`](./../../build/config.xml)

```xml
<propertyprompt propertyName="my_property_name" defaultValue="" promptText="" useExistingValue="true"/>
```
La valeur définie est disponible via la clé incluse dans `$ {` et `}`
```
${line.separator} my_property_name: ${my_property_name}
```
| Attribut | Type | Description |
|---|---|---|
| `propertyName` | String | Le nom de la propriété à définir |
| `defaultValue` | String | La valeur par défaut à utiliser |
| `promptText` | String | Le texte à utiliser pour le prompt |
| `useExistingValue` | Boolean | Si la propriété existante doit être utilisée si elle est disponible |

Il est possible aussi de caster une variable. Par exemple, une variable qui doit être un boolean peut être casté de la manière suivante :

```xml
<php expression="boolval('${my_property_name}')" returnProperty="my_property_name_boolean" />
```

Phing gérant mal la réassignation d'une variable, il est préférable de créer une nouvelle variable suffixé du type de celle-ci.

## 5. Ajout de la nouvelle variable dans Wizaplace Delivery

Afin que la QA puisse tester ce nouveau feature flag, il faut créer une PR dans le projet [Wizaplace Delivery](https://github.com/wizaplace/wizaplace-delivery/).

Se référer à la documentation du projet pour plus d'informations.

## 6. Variables globales et paramètres

**/!\ Uniquement pour les fronts legacy.**

Ajouter une variable globale dans le fichier `app/config_dev.yml`
```yaml
twig:
    globals:
        myPropertyName: '%my_property_name%'
```

Ajouter un nouveau paramètre dans le fichier `app/config.yml`
```yaml
parameters:
    my_property_name: 'value'
```

## 7. Views

**/!\ Uniquement pour les fronts legacy.**

Afficher la variable globale en front.
```twig
{% if myPropertyName is defined and myPropertyName != "" %}
   ...
{% endif %}
```
