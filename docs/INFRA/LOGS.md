# Logs

## Politique de rétention des logs

Cette politique a été définie avec l'équipe produit et administratif.

### Logs collectés

Pour répondre à des contraintes légales et contractuelles nous avons besoin de stocker les logs bruts au format fichier sans altération. Logs concernés :

- Apache (access et error)
- Logs applicatifs PHP (erreurs, notice/info, ...)
- Déploiements
- SLA, downtime et temps de réponse (suivi par Pingdom, hors des logs textuels de notre système de log donc non concernés par la suite)

Séparément on collecte des logs et métriques pour notre **usage interne** tels que le debug, le suivi de performances, etc. (on peut traiter ces logs et dropper des infos inutiles) :

- Apache (access et error)
- PHP-FPM
- Logs applicatifs PHP (erreurs, notice/info)
- Déploiements
- Métriques

### Durée de rétention

Logs bruts : À l'infini.

Logs à usage interne : 2 ans.

### Niveau de backup et redondances

Logs bruts : système de stockage avec redondance (type AWS S3, Glacier ou similaire).

Logs à usage interne : système de backup d'Elasticsearch suffisant.

### Cloisonnement entre clients

Logs bruts : stockage séparé par client, cloisonemment type "bucket S3" : gestion des droits/accès séparés. Pour des grands comptes avec des besoins spécifiques nous pourrons appliquer une solution dédiée.

Logs à usage interne : pas besoin de cloisonemment. Pour des grands comptes avec des besoins spécifiques nous pourrons appliquer une solution dédiée.

## Architecture

### Versions

* Elasticsearch: `2.4.6`
* Logstash: `2.4.1`
* Filebeat: `5.5.2`
* Kibana: `4.6.6`

### AWS

![schema AWS](./../images/logs-aws.png)

Sur chaque front AWS il y a une instance de [logstash](https://www.elastic.co/guide/en/logstash/2.4/index.html) qui va collecter les logs apache ainsi que ceux de Symfony, les analyser et les envoyer sur notre instance [elasticsearch](https://www.elastic.co/guide/en/elasticsearch/reference/2.4/index.html) `logs.wizacha.com`.

### Platform.sh

![schema Platforn.sh](./../images/logs-platform-sh.png)

Sur chaque instance platform.sh une instance de [filebeat](https://www.elastic.co/guide/en/beats/filebeat/current/index.html) va collecter les logs de NGINX au format apache, ainsi que les logs de Symfony. Ces logs sont envoyés sur l'instance de [logstash](https://www.elastic.co/guide/en/logstash/2.4/index.html) sur le serveur `logs.wizacha.com` pour être analysés. Ces logs sont ensuite stocké sur [elasticsearch](https://www.elastic.co/guide/en/elasticsearch/reference/2.4/index.html) sur le même serveur.

## Visualisation

### Applicatif

Les logs de l'app sont configurés en prod pour n'envoyer les logs que si on atteint le level `warning` ou plus haut (cf [`LogLevel`](./../../vendor/psr/log/Psr/Log/LogLevel.php)).

Les logs sont accessibles via [kibana](http://kibana.wizacha.com/app/kibana#/discover/Errors)

### Apache

les logs d'accès sont disponibles via cette [recherche kibana](http://kibana.wizacha.com/app/kibana#/discover/Apache-access).
