# Commandes de Post-Deploy

Après chaque déploiement, une suite de commandes est exécuté. Ce sont les commandes `deploy:post-actions`.

Il est possible de se greffer dessus pour ajouter de nouvelles commandes à exécuter.

## Créer une nouvelle commande

Il y a deux sortes de commandes Post-Deploy qui peuvent être créées.
L'une qui va s'executer à chaque déploiement et l'autre qui ne va s'exécuter qu'une seule fois, un peu à la manière d'une migration.

Peut importe la commande à créer, le tronc principal reste le même.

Pour créer une nouvelle commande, il faut qu'elle :
- implémente `DeployPostActionsInterface` et implémente la methode `getPriority`
- se nomme `deploy:command:*****`
- soit préfixée de `Deploy`

`getPriority` doit retourner un entier qui correspond à sa priorité.
Les priorités sont traitées par ordre croissant. Une commande avec une priorité de 0 va être traitée en premier.

```php
class DeployFooCommand extends Command implements DeployPostActionsInterface
{
    ...

    public function getPriority(): int
    {
        return 100;
    }

    protected function configure(): void
    {
        $this
            ->setName('deploy:command:foo')
            ->setDescription('Foo example for post deploy commands.')
        ;
    }

    ...
}
```

### Création d'une action à ne lancer qu'une seule fois

Pour que la commande s'exécute correctement, elle doit utiliser le trait `DeployPostActionsOnce`
et avoir en injection de dépendence `EntityManagerInterface`.

> Cas d'utilisation : migration de données dans laquelle il est nécessaire d'utiliser un service Symfony.

Votre classe de migration doit définir une date et un nom unique afin de l'identifier grâce aux méthodes abstraites
du trait. Par convention, il est préférable de définir ces informations dans des constantes au début de la classe
afin de pouvoir l'identifier rapidement.

```php
class DeployFooCommand extends Command implements DeployPostActionsInterface
{
    use DeployPostActionsOnce;

    /** @var string */
    protected const VERSION_DATE = '2019-12-04 11:08:43';

    /** @var string */
    protected const VERSION_NAME = 'DeployFoo';

    /** @var EntityManagerInterface */
    protected $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        parent::__construct();

        $this->entityManager = $entityManager;
    }

    public function getPriority(): int
    {
        return 100;
    }

    protected function configure(): void
    {
        $this
            ->setName('deploy:command:foo')
            ->setDescription('Foo example for post deploy commands.')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if ($this->hasRun() === true) {
            return 0;
        }

        // TODO

        $this->saveExecution();

        return 0;
    }

    ...

    protected function getConnection(): Connection
    {
        return $this->entityManager->getConnection();
    }

    protected function getDateTime(): \DateTimeInterface
    {
        return new \DateTimeImmutable(static::VERSION_DATE);
    }

    protected function getVersionName(): string
    {
        return static::VERSION_NAME;
    }
}
```

## Lister toutes les commandes

```php bin/console deploy:debug```

## Exécuter les commandes

```php bin/console deploy:run```
