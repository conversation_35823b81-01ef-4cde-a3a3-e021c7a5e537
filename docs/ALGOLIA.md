# Algolia

## Development

### Configuration

Algolia configuration is mandatory to connect to the Wizaplace Back Office.

To use Algolia in development you need to:

- edit your `.env` file to set the key `ALGOLIA_API_INDEX_PREFIX` to a unique value

Development uses an Algolia application `9L2Y34HCYO` dedicated to internal use. No production data is stored in this application.
The preconfigured API key in `.env.dist` is a full rights API key with these ACLs:
  - `addObject`
  - `browse`
  - `deleteObject`
  - `editSettings`
  - `search`
  - `settings`
  - `deleteIndex`

To use Algolia in development you need to:
 - create a private project on Algolia, with your username and the "Community" plan
 - copy the `geocoding` index to your project (see below)
 - add the following config to your `.env` file:

```conf
ALGOLIA_API_IDENTIFIER=(your application ID)
ALGOLIA_API_INDEX_PREFIX=(leave blank)
ALGOLIA_API_KEY_FULL_RIGHTS=(Admin API Key)
ALGOLIA_API_KEY_LIMITED_RIGHTS=(Search-Only API Key)
```

### Activation

Enable Algolia with the make command

```shell
make algolia-enable
```

And disable it with

```shell
make algolia-disable
```

### First indexation

```shell
bin/console deploy:command:update-algolia
bin/console algolia:products:push
```

You can access the Algolia dashboard [here](https://www.algolia.com/apps/9L2Y34HCYO/dashboard).
