# MailingList

Le module `MailingList` gère les mailing lists et newsletter des front-offices.

Ce module est à la fois over-engineered et limité, il nécessiterait d'être refactorisé (ou externalisé ?) :

- il permet de créer plusieurs mailing lists alors qu'en front on a toujours eu besoin d'une seule : la newsletter
- vu qu'on a X mailing list on est obligé de créer une mailing list avec l'ID `1` -> celle-ci représente la newsletter
- on peut uniquement subscribe un email, donc impossible d'afficher à un utilisateur connecté si il est déjà inscrit à la mailing list (sinon n'importe quel utilisateur de l'API pourrait récupérer le statut d'inscription à la newsletter de n'importe qui)

On essaye de pousser les front-offices à utiliser des systèmes tiers tels que des formulaires HTML/JS mailjet ou autre pour les inscriptions newsletter, mais ce module reste parfois utilisé.

Méthodes principales :

- `MailingListService::getMailingLists()`
- `MailingListService::subscribe()`
- `MailingListService::unsubscribe()`
