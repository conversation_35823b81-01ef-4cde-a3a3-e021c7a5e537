# CircleCI

## Introduction
CircleCI s’intègre à GitHub. Chaque fois que vous validez du code, CircleCI crée un build.

CircleCI exécute automatiquement votre build dans un conteneur propre ou une machine virtuelle, vous permettant de tester chaque commit.

## Configuration
La configuration de la CI s'effectue dans le fichier `.circleci/config.yml`. 

Ce fichier contient la configuration des commandes, executors, jobs et workflows.

## Edition de la configuration
**WARNING**: ajouter ou supprimer des jobs présente un risque de casser les builds des autres PR.

A chaque push sur la CI on build une image docker que l'on stocke en cache pour gagner du temps. 
Si la configuration impacte l'image docker celle ci ne sera pas rebuildée, on utilisera l'image en cache à la place.

#### Eviter les conflits :
Pour éviter des conflits entre les push il faut modifier le nom de l'image mise en cache :
- Modifier l'executor `api-v1_wizaplace-test` en ajoutant un suffixe dans le nom de l'image
```yml
- image: ${DOCKER_REGISTRY}/circleci:${CIRCLE_PROJECT_REPONAME}-suffix
```
- Modifier le tag de l'image docker `v1-api_build-docker-test` en ajoutant un suffixe dans le nom de l'image
```yml
docker build . -f tests/docker/Dockerfile -t ${DOCKER_REGISTRY}/circleci:${CIRCLE_PROJECT_REPONAME}-suffix; \
docker push ${DOCKER_REGISTRY}/circleci:${CIRCLE_PROJECT_REPONAME}-suffix; \
```

#### Corriger le problème de cache :
- Se connecter en SSH sur le container `v1-api_build-docker-test` depuis l'interface CircleCI
![CircleCI SSH 1](images/circleci-ssh.png)
![CircleCI SSH 2](images/circleci-ssh-2.png)
- Exécuter la commande suivante 
```bash
cd repo
echo ${DOCKER_PASSWORD} | docker login -u ${DOCKER_USERNAME} --password-stdin ${DOCKER_REGISTRY}
docker build . -f tests/docker/Dockerfile -t ${DOCKER_REGISTRY}/circleci:${CIRCLE_PROJECT_REPONAME}
docker push ${DOCKER_REGISTRY}/circleci:${CIRCLE_PROJECT_REPONAME};
```

## Commandes
- api-v1_wizaplace-test: Exécuter les tests via phing
- api-v1_wizaplace-orm-mapping: Valider le schéma doctrine
- api-v1_wizaplace-openapi: Valider la documentation openapi
- docker_build_and_push: Build l'image docker de prod et la push
- download_github_file: Télécharger la configuration depuis le dépot github

## Executors
- api-v1_wizaplace-test: Exécute les tests sur l'image docker
- php-sdk: Exécute uniquement les tests du SDK

## Jobs
- v1-api_vendor: Télécharge les vendor depuis composer et créer un cache
- v1-api_nodes_modules: Télécharge les node_modules depuis NPM et créer un cache
- v1-api_build-docker-test: Télécharge l'image docker et créer un cache
- v1-api_check: orm-mapping, openapi, phpcs et phpstan
- v1-api_atoum-parallel: Atoum en parallèle 
- v1-api_atoum-sequential: Atoum 
- v1-api_phpunit-1: PHPUnit 
- v1-api_phpunit-2: PHPUnit
- v1-api_behat: Behat
- deployment_build-aws: Déploiement sur AWS
- deployment_platform: Déploiement sur platform
- deployment_build-docker-php: Build de l'image php de prod (k8s)
- deployment_build-docker-nginx: Build de l'image nginx de prod (k8s)
- php-sdk_vendor: Télécharge les vendor du SDK
- php-sdk_lint: Linter du SDK
- php-sdk_stan: phpstan pour le SDK
- php-sdk_phpunit: PHPunit pour le SDK

## Workflows
- Deployment:
    - deployment_platform
    - deployment_build-aws
    - deployment_build-docker-php
    - deployment_build-docker-nginx
    
- v1-api:
    - v1-api_vendor
    - v1-api_node-modules
    - v1-api_build-docker-test
    - v1-api_atoum-parallel
    - v1-api_atoum-sequential
    - v1-api_check
    - v1-api_phpunit-1
    - v1-api_phpunit-2
    - v1-api_behat
    
- php-sdk:
    - php-sdk_vendor
    - php-sdk_lint
    - php-sdk_stan
    - php-sdk_phpunit
