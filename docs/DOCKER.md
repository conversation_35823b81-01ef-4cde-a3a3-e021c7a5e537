# Docker

How to use Docker to setup a dev environment.

## Installing/Configuring

### Requirements

* [Docker](https://docs.docker.com/install/#supported-platforms)
* [Docker compose](https://docs.docker.com/compose/install) >= 1.23

Test your versions with :
* `docker --version`
* `docker-compose --version`

#### Add current user to Docker group on Linux :

```
sudo usermod -a -G docker username
```

* Reboot system to apply this modification

### Git

Add your SSH key in your [GitHub's account](https://github.com/settings/keys) and get the project

```
<NAME_EMAIL>:wizaplace/wizaplace.git
cd wizaplace
```

### Configure environment

/!\ If first time you use Docker, remove your file `.env`

```
cp .env.dist .env
```

Customize environment variables in file `.env` or use the default values

### PhpStorm

When `.env` is updated reload php container

Open File > Settings > Tools > File Watchers > + (custom)

File type : `.env`

Scope : Project Files

Program: make

Argument: docker-dev-restart

Working directory: YOUR_PROJECT_PATH

#### Linux

```
sudo bash -c 'echo "127.0.0.1 wizaplace.test wizaplace.loc wizaplace.prod" >> /etc/hosts'
```

#### Mac

```
sudo bash -c 'echo "127.0.0.1 wizaplace.loc" >> /etc/resolver/wizaplace.loc'
sudo bash -c 'echo "127.0.0.1 wizaplace.test" >> /etc/resolver/wizaplace.test'
```

### Install

```
make docker-dev
```

Build docker images:
- php

Start docker application's services:
- http (nginx / apache)
- php
- php-test
- mysql
- mysql_discuss
- mysql_test
- redis
- adminer

Install dependency:
- composer
- npm

Following `composer install`, git hooks are modified:
- `post-checkout`: the existing content had been removed and replaced by `This hook has been reseted by Wizaplace app.`
- `post-merge`: the existing content had been removed and replaced by `This hook has been reseted by Wizaplace app.`
- `pre-push`: check the branch name

Create databases:
- marketplace
- discuss
- redis

Set fixtures:
- theme
- database

### Algolia (search engine)

Algolia environment variables are mandatory, even in the development environment, to run the back office.

cf [ALGOLIA.md](./ALGOLIA.md) for the configuration.

## Use

### Http access

* Front is reachable at `http://wizaplace.loc/` or `http://localhost` (you can log in with `<EMAIL>` / `Windows.98`).
* Vendor is reachable at `http://wizaplace.loc/vendor.php` (`<EMAIL>` / `Windows.98`).
* Back is reachable at `http://wizaplace.loc/admin.php` (`<EMAIL>` / `Windows.98`).

To be able to access to `http://wizaplace.test/` and `http://wizaplace.prod/` :

### Connect to shell

```
make docker-dev-shell
```

### Xdebug

cf [XDEBUG.md](./XDEBUG.md)

### MySQL access

Use adminer with your browser http://localhost:8081

Or use the command line inside Docker if you're a badass programmer or a sysadmin :

```
make docker-dev-shell
mysql -h mysql -uroot -proot wizaplace
mysql -h mysql_discuss -uroot -proot wizaplace
```

```
# Base de dev
mysql -h localhost -P 3307 -uroot -proot wizaplace
# Base de test
mysql -h localhost -P 3308 -uroot -proot wizaplace
```

### Environment variable

* Variables are managed in file `.env`

    If you add or modify environment variables, you need to execute the following command :

```
make docker-dev-restart
```

### Help and support

```
external connectivity on endpoint wizaplace_http_1 (b3167fef30f7bafa0b89b0ae15ad9c0b5a4d2c34eae6f992d43431aee87356f5): Error starting userland proxy: listen tcp 0.0.0.0:80: bind: address already in use
```

The http port is already bind

* Define env variable `HTTP_PORT` to unused port

OR

* Release used port

```
ERROR: Couldn't connect to Docker daemon at http+docker://localhost - is it running?
```

In this case, add current user to Docker group

## Extra

### Use tmpfs instead of volume for Mysql and Discuss

If you want to go faster, you can override docker-compose.

```
cp docker-compose.override.yml.dist docker-compose.override.yml
docker-compose down
docker-compose up -d
```

Now Mysql database are loaded into RAM and not from your HDD.

/!\ Becareful, now you need to `make reload-data` when your containers starts.
