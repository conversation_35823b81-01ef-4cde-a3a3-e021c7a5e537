# Redis et Cache dans Wizaplace

## Avertissement

Documentation non exhaustive comprenant un début d'analyse par l'équipe n'ayant ni initié l'utilisation de redis ni la connaissance de l'historique.

## Implémentation

### SncRedisBundle

Redis est intégré dans l'appli via [SncRedisBundle](https://github.com/snc/SncRedisBundle) et configuré dans *app/config.services.marketplace.yml*.
On y définit 4 client redis (4 services donc), on peut aussi parler de 4 "databases" :
- `snc_redis.default`
- `scn_redis.cscart_session`
- `scn_redis.handlers`
- `scn_redis.locks`

Ces services ne sont pas fait pour être utilisés directement dans l'appli, car il est bien d'avoir une surcouche métier qui abstrait l'implémentation technique du cache.
Cependant `snc_redis.default` est utilisé directement une fois (voir chapitre *Utilisations*).

### Couche d'abstraction 1 : le service `cscart.cache_backend`

Ce service peut prendre deux formes suivant la variable d'env **CACHE_BACKEND** :

- une instance de `Tygh\Backend\Cache\Redis` si *CACHE_BACKEND=redis* (comme en prod).
- une instance de `Tygh\Backend\Cache\File` sinon

Ces deux classes étendent une abstraite : `Tygh\Backend\Cache\ABackend`, et dans les deux cas les valeurs sont stockées avec un TTL par défaut de deux jours (*Wizacha\Config::CACHE_TIMEOUT*).

C'est le fonctionnement de `Tygh\Backend\Cache\Redis` qui nous intéresse vraiment. C'est lui le coeur de la mise en cache dans l'application.
Il englobe 3 des 4 clients *scn_redis* : tous sauf *scn_redis.cscart_session*.

- snc_redis.default : c'est là que le service met les valeurs en cache. **Les clés y sont préfixées automatiquement avec "cache" suivi de la version de l'application en cours** (*marketplace.version*). Ex : `cache:1.60.1:key`.
- scn_redis.handlers : sorte de gestion de "groupes" de cache. Utilisé dans la logique d'un autre "service", voir *Couche d'abstraction 2*.
- scn_redis.locks : pas utilisé, il semble ok de supprimer cette chose du code et de la stack redis. Probablement une gestion non implémentée de "lock" de cache (anti écrasement, etc..).

### Couche d'abstraction 2 : le service `wizacha.registry`

Ce service est une instance de `Wizacha\Registry`. Il est souvent récupéré via un singleton `Wizacha\Registry::defaultInstance()` mais des instances sont aussi créé avec un `new` par ci par là.
En tant que service (en injection), c'est le singleton qui est utilisé.

- dans le service (donc le singleton) se trouve une instance de `cscart.cache_backend` (c'est ce qui va nous intéresser),
- mais dans les instances faites à la main par un new (et il y en a beaucoup), on y trouvera à la place une instance de `MemoryCache` qui extend aussi `ABackend` et qui fait du cache dans une propriété donc juste pour le script en cours.

Ce service utilise donc Redis, cependant, utiliser `Wizacha\Registry::set()` ne garantit pas que l'on va stocker dedans. Il faut précédemment avoir appellé `Typh\Registry::registerCache` avec les clés que l'on voudra peut-être stocker dans le cache plus tard dans le script.
Si ce n'est pas le cas, les valeurs seront stockées dans une propriété (local_cache) uniquement pour le script en cours. A savoir que les valeurs stockées dans Redis restent aussi stockées pour le script en cours dans cette même propriété et sont chargées dès l'appel à `Typh\Registry::registerCache` => potentiellement beaucoup de données.

Les clés enregistrées comme allant dans Redis peuvent être liées à une ou plusieurs "catégories", appelées parfois `$handler` ou `$condition` dans php. En pratique ce sont des noms de table à laquelle la donné est liée (sauf cas particulier).
La clé dans Redis sera alors suffixée en fonction de ces "handlers". Il suffira de marquer alors la table comme modifiée pour regénérer automatiquement un suffix différent et invalider toutes les clés liées à cette table. Les suffixes calculés sont stockés dans `scn_redis.handlers`.
Et c'est là que ça devient intéressant : `Tygh\Database::process` marque les tables modifiées à chaque query qui fait une modif de base (insert, update, etc). Il y a donc potentiellement beaucoup d'appels Redis notemment dans des imports (concrêtement ça va incrémenter une variable dans `scn_redis.handlers`).

**Exemple**

- `Tygh\Registry::registerCache("settings", ["cscart_settings_sections", "cscart_settings_objects", "cscart_settings_descriptions"])`
Ici ça va faire 3 appels GET dans `scn_redis.handlers` pour récupérer le suffixe calculé correspondant à ces "tables", puis un GET de la clé calculée dans `snc_redis.default` pour avoir la valeur et la stocker dans `$local_cache`.

- `Tygh\Registry::set("settings", $value)`
Stocke (ou modifie) dans une propriété `local_cache`, et va aussi envoyer la valeur dans redis `snc_redis.default`.

- `Tygh\Registry::get("settings")`
get dans `$local_cache`, pas d'appel Redis.

- Je fais un update de la table `cscart_settings_sections`...
Il va y avoir un INCR dans `scn_redis.handlers` et par conséquent modifier le suffix supposé des clés liées à cette table. Concrètement ça invalide toutes clés liés à cette table (les anciennes valeurs clés sont supposées expirer toutes seules en fait).
Bon là c'est bizarre mais j'ai l'impression que ce changement n'est effectif qu'au prochain script, je trouve pas de modification de "$local_cache" à ce moment. A vérifier.

- `Wizacha\Registry::defaultInstance()->set(['runtime', 'company_id'], 5);` ne va pas dans Redis.

TODO checker les services dans lesquels `wizacha.registry` est injecté.

### Couche d'abstraction 1 bis : le service `marketplace.cache`

Le service `marketplace.cache` est l'instance du service `cscart.cache_backend` récupérée dans le singleton de `wizacha.registry`.

### Couche d'abstraction 3 : `Tygh\Registry`

A ne pas confondre avec `Wizacha\Registry`, `Tygh\Registry` n'est pas un service mais une bibliothèque de fonctions statiques de cache utilisant le singleton de `Wizacha\Registry`.
C'est lui qui "initialise" le singleton en lui settant l'instance de `cscart.cache_backend` dans `init.php`.

## Utilisations

Trouver l'utilisation faite du cache revient à trouver l'utilisation de ces quatre couches dans le code, car aucune restriction nous en empêche.

### SncRedisBundle

- `snc_redis.default` est directement utilisé par le service `marketplace.stock.domain_service` (*Wizacha\Marketplace\PIM\Stock\StockService*).
C'est pour le stock en temps réel, timeout de 900 secondes.

- `scn_redis.cscart_session` est utilisé par `Tygh\Backend\Session\Redis` pour stocker les sessions dans Redis avec un TTL de deux semaines (*SESSIONS_STORAGE_ALIVE_TIME*).
A noter que la session peut être stockée dans MySQL en fonction de la variable d'env `SESSION_BACKEND`, qui est à *redis* en prod.


### cscart.cache_backend

Le service `cscart.cache_backend` n'a pas l'air d'être utilisé directement (ailleurs que dans la couche d'abstraction 2).

### wizacha.registry et Tygh\Registry

Après analyse, les appels comme `[Wizacha|Typh]\Registry::set`  ne mettent pas forcément de choses dans le cache (voir explication dans *Couche d'abstraction 2*).

Il semble plus pertinent de chercher les utilisations de `Tygh\Registry::registerCache` pour savoir quelles clés seront vraiment persistées dans le cache.

- `Tygh\Backend\Storage\[Amazon|Azure]` enregistre le fait qu'un fichier existe, TODO checker potentiellement beaucoup d'utilisations si lié aux images.

- Sont des clés potentiellement stockées dans le cache : `thooks_*`, `settings`, `addons`, `seo`, des schémas exim (complexe à vraiment comprendre, voir fn.common.php::fn_get_schema...),
Piste : checker `fn_seo_cache_static_create` qui potentiellement met beaucoup de choses dans le cache.

- `Tygh\BlockManager\RenderManager\[renderBlockContent|...]` stocke des rendus de block smarty à priori.

- `Tygh\BlockManager\SchemesManager` à peu près pareil.

- `Tygh\Languages\Values::getLangVar` stocke un gros tableau clé/valeur de translations chargées depuis la table `cscart_language_values` (mais construit ligne par ligne). Une opti est probablement faisable ici.

### marketplace.cache

- `Wizacha\Component\Dolist\DolistClient` ne stocke qu'un `dolist_auth_token`.

- `Wizacha\CashconvertersBundle\Service\StoreService` stocke des infos de stores TTL 1h, code métier spécifique à cashconverter et ne semble pas être problématique.

- `Wizacha\CashconvertersBundle\Controller\CategoryProductBlocks` stocke des infos calculées depuis la bdd, 1 min TTL.

- `Wizacha\CashconvertersBundle\Controller\HomeController` pareil.

- `Wizacha\AppBundle\Controller\Api\ImageController` stocke dans le cache les urls calculées des images, TTL 2 jours. Il y a autant de clés par image que d'appels à cette image avec un couple width et height différent. Ca peut aller vite.

## Lazy Services

Une version _lazy_ des services `snc_redis` est disponible sous le nom `app.redis`.

C'est le service `app.redis.default` qui est fourni à `StockService` de manière à ne pas faire de connexion à Redis tant que le service n'est pas utilisé.
