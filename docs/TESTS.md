# Tests

Il y a 2 types de tests unitaires/fonctionnels qui sont implémentés dans Wizaplace.

1. Atoum (deprecated)
1. PHPunit

Ceux-ci doivent être lancés avec le thème *basic*. (cf [THEME.md](THEME.md))

## Conteneur de tests

Il est possible de se connecter au conteneur Docker où sont joués les tests via la commande :

```bash
make docker-test-shell
```

La base de données des tests quant à elle est montée en TMPFS, c'est à dire qu'elle est chargée en RAM.
Si vous avez une erreur lors du lancement des tests vous indiquant qu'il manque des tables, lancez la commande suivante :

```bash
make docker-test-reload-data
```

Si vous êtes déjà dans le conteneur de tests, vous pouvez lancer directement :

```bash
make reload-data
```

## Lancement des tests

### PHPunit

- [CLI](https://github.com/wizaplace/wizaplace/wiki/Lancer-les-tests-PhpUnit-en-CLI)
- [PhpStorm](https://github.com/wizaplace/wizaplace/wiki/Lancer-les-tests-PhpUnit-dans-PhpStorm-(Docker))

### Atoum

```bash
vendor/bin/atoum tests/atoum/Wizacha/Misc.php -mcn 1
```

Pour lancer un seul test :

```bash
vendor/bin/atoum tests/atoum/Wizacha/Foo.php -m '*::testFooMethod' -mcn 1
```

Note: Les tests ne fonctionnent pas sur l'environnement de dev si xdebug est activé.

- Utiliser la command `make debug cmd='vendor/bin/atoum ...'` à l'interieur du container `php_test` s'il est nécessaire de debugger les tests.
- Sinon, juste désactiver xdebug, `make docker-dev-xdebug-disable`

> Attention ! Lancer le debug des tests via `make docker-test-debug cmd="vendor/bin/atoum"` ne permet pas de proprement abandonner leur exécution via `CTRL-C`. La série de tests continuera à être exécuté en tâche de fond, `CTRL-C` est intercepté par docker-compose et non transmis jusqu'à la commande exécutée dans le container.

### Circle CI like

Pour lancer les tests unitaires comme CircleCI le fait, il est nécessaire de réaliser ces opérations :

* Vider le contenu du fichier `.dockerignore`.
* docker build . -f tests/docker/Dockerfile -t wizaplace-tests
* docker run -v $(pwd):/var/www -it wizaplace-tests <theme> <test_suite>

Les valeurs possibles de `test_suite` sont dans [tests/build.xml](../tests/build.xml), ce sont les `target` : `test`, `test.atoum` etc.

Attention, lancer les tests ainsi effectue des modifications dans votre projet,
qu'il faut revert pour ne pas les commit et refaire fonctionner votre projet en local :
* L'owner des fichiers de votre projet est modifié, il faut vous redonner les droits : `sudo chown -R $USER:$GROUP /var/www/wizaplace`
* Le fichier `.env.dist` est entièrement réécrit, donc pensez bien à revert les modifications effectuées
* Le fichier `.env` est entièrement réécrit, donc pensez bien à revert les modifications effectuées via l'historique local de PHPStorm par exemple
* Il crée `app/config/parameters.doctrine.yml` et `app/config/parameters.yml`, à supprimer

## Connexion à la base de données

### Depuis le conteneur de tests

```bash
make docker-test-shell
mysql -h mysql_test -uroot -proot wizaplace
```

### Depuis votre poste

```bash
mysql -h localhost -P 3308 -uroot -proot wizaplace
```

### Depuis adminer

Rendez-vous sur [adminer](http://wizaplace.loc:8081/) et renseignez les informations suivantes :
- serveur : **mysql_test**
- utilisateur : **wizaplace**
- mot de passe : **wizaplace**
- base de données : **wizaplace**
