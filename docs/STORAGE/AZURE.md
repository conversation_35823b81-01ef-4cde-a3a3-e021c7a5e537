# AZURE

## Configuration *.env*

```env
STORAGE_SYSTEM=azure
AZURE_ACCOUNT_NAME=devstoreaccount1
AZURE_ACCOUNT_KEY=Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw==
```


### Configuration des variables d'environnement en dev

Par défaut, le connecteur azure communique en https avec azure.storage.com, il est possible de surcharger cette configuration en local avec :


```env
AZURE_PROTOCOL=http
AZURE_LOCAL_ENDPOINTS=BlobEndpoint=http://<ip-host-local>:10000/devstoreaccount1;QueueEndpoint=http://<ip-host-local>:10001/devstoreaccount1;
```

+ remplacer IP locale par 192.168.\*.\* ou 10.\*.\*.\*  (info à trouver avec `ip a` ou `ifconfig`)


## Test sur environnement local :

### Azurite 

Azurite est un émulateur de storage azure que vous pouvez faire tourner en local,
cela permettra de tester correctement ce type de storage.

Azurite ne supporte pas les options d'url rscd : inline attachment. Sur un storage azure, si l'url contient `&rscd=attachement` alors la réponse contiendra bien un header Content-Disposition.  

Pour le lancer :

`docker run -p 10000:10000 -p 10001:10001 mcr.microsoft.com/azure-storage/azurite`

Attention, pas de persistance des données entre 2 redémarrages.

Si besoin de persistance, possibilité de l'installer en local avec NPM en suivant les [instructions de ce lien](https://github.com/azure/azurite#npm) ou avec ces commandes :

```shell
npm install -g azurite
mkdir ~/azuritestorage
azurite --silent --blobHost=0.0.0.0 --location=~/azuritestorage --debug=~/azuritestorage/debug.log
```


### Azure storage explorer

Azure storage explorer est un petit GUI qui vous permet de visualiser votre storage azure avec les différents containers et arboresences.

**Installation :**

`sudo snap install storage-explorer` \
`sudo snap connect storage-explorer:password-manager-service`

**Premier lancement :**

Lancer "Azure Storage Explorer" > Attacher un émulateur local > [suivant] > [connexion]

Menu à gauche :
```
> Local et attaché :
  > Storage Accounts :
     > Emulateur - Ports par défaut
        > Blob containers
```

## Subtilité stockage azure

Sous azure, il y a une notion de cloisonnement (droits d'accès) par container dans lesquels sont stockés nos fichiers. Les urls azure contiennent donc 2 fois le prefix de nos fichiers).

Exemple pour un CSS du backoffice avec "statics":
`http://<ip-locale-du-host>:10000/devstoreaccount1/statics/statics/design/backend/css/standalone.f5ba824eb723bad8f4604c5ca07e66b6.css`

Exemples de containers :
- assets
- attachements
- downloads
- images
- vendorsubscriptions

Attention, le container ne peut contenir que des lettres [a-z], on peut donc avoir des urls avec "vendorsubscriptions/vendor_subscriptions" 
pour le cas d'un underscore qui n'est pas retenu pour la création du container.