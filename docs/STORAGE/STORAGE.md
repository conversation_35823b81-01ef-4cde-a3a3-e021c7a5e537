# Storage

Pour créer un stockage il faut ajouter un storage dans `app/config/storage.flysystem.yaml` :

```yaml
  Wizacha\Storage\YourNewStorageService:
    factory: '@Wizacha\Storage\StorageFactory'
    public: true
    arguments:
      - 'nom_du_storage'
      -
        'prefix' : 'path_to_storage'
        'dir' : '/other/prefix/only/used/with/local/storage'
        'secured' : true
```

Pour utiliser un storage, il faut l'injecter dans votre service comme ceci (pas d'autowire possible) :


```yaml
Wizacha\AppBundle\Controller\Api\Order\OrderAttachmentController:
    public: true
    autowire: true
    arguments:
        $orderAttachementsStorageService: '@Wizacha\Storage\OrderAttachmentsStorageService'
```

```php
    private StorageService $orderAttachementsStorageService;

    public function __construct(StorageService $orderAttachementsStorageService)
    {
        $this->orderAttachementsStorageService = $orderAttachementsStorageService;
    }
```

Ou bien utiliser le singleton container (seulement pour les anciens fichiers cscart) : 
```php
$orderAttachmentsStorageService = container()->get('Wizacha\Storage\OrderAttachmentsStorageService');
```
