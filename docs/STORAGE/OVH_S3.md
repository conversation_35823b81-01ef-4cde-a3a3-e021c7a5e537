# OVH S3

Configuration *.env*

**Important:** L'api d'Ovh n'aime pas trop les `_` dans le nom du bucket => `400 Bad Request - The specified bucket is not valid`

```env
# OVH S3 STORAGE
STORAGE_SYSTEM=ovh
OVH_S3_REGION=<region_uppercase>
OVH_S3_API_URL=https://storage.<region_lowercase>.cloud.ovh.net
OVH_S3_CREDENTIALS_KEY=<s3_credential_key>
OVH_S3_CREDENTIALS_SECRET=<s3_credential_secret>
OVH_S3_PUBLIC_BUCKET=<public_bucket>
OVH_S3_PRIVATE_BUCKET=<private_bucket>
OVH_S3_PUBLIC_URL_TOKEN=<auth_token>
```
