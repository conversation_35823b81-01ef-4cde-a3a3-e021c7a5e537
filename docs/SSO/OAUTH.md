# OAuth

Il est possible de se connecter à wizaplace au travers d'un service externe qui suit le protocole OAuth2.

Pour intégrer un prestataire il faut implémenter 2 méthodes:

## [`Provider::getAuthorizeUrl(): Url`](../../src/Component/OAuth/Provider.php)

Cette méthode `getAuthorizeUrl` doit retourner l'url vers laquelle l'utilisateur sera redirigé pour qu'il se connecte sur la plateforme du prestataire. L'url générée doit contenir l'url de redirection pour que le prestataire redirige l'utilisateur vers wizaplace lorsque celui ci s'est connecté chez le prestataire.

L'url de retour devra prendre en compte le token renvoyé par le prestataire, ce token permet d'identifier l'utilisateur sur leur plateforme.

## [`Provider::authorize(Token): User`](../../src/Component/OAuth/Provider.php)

Cette méthode doit retourner un objet `User` de notre marketplace pour un token donné. Typiquement l'implémentation de cette méthode va devoir aller chercher les informations de bases de l'utilisateur (tel que son email) sur la plateforme de l'utilisateur et devoir créer l'utilisateur dans notre base de données; dans le cas où l'utilisateur existe déjà (et a été créé via ce prestataire, sinon il faut lever une exception) alors il faut mettre à jour l'utilisateur dans notre base avec son nouveau token.

Dans le cas où le token qui nous est fourni est invalide alors il faudra lever une exception.

## Enchainement des étapes

Lorsque le client clique sur le bouton pour se connecter via le prestataire il est redirigé sur l'url fourni via la méthode `getAuthorizeUrl`. Une fois sur le site celui ci renseigne ses identifiants et est redirigé vers le remote front ou sur le back office wizaplace. L'url appelée contient un token qui permet d'identifié l'utilisateur chez le prestataire et doit être utilisé pour la méthode `authorize`; dans le cas du remote front cet appel doit se faire via l'endpoint de notre API dédié à cet action.

Dans le cas d'une connection au back office, l'utilisateur qui est retourné doit être utilisé pour activer l'authentification dans cscart (via son id). Dans le cas d'une connection via l'api on retourne le token API de wizaplace associé à cet utilisateur afin qu'il puisse utiliser notre API comme pour une connection normale.

![oauth](img/oauth.jpg)

## Enchainement des étapes sur un front

![oauth with secret](img/oauth_with_secret.png)
![oauth without secret](img/oauth_without_secret.png)

## Implémentations

### Okta

La marketplace permet à tous les types d'utilisateurs (client, vendeur et admin) de se connecter via oauth. Afin de pouvoir les distinguer de manière sécurisée l'api d'Okta doit nous renvoyer le type de l'utilisateur lorsqu'on accède aux informations de l'utilisateur (appel à l'endpoint `/v1/introspect`), cet appel doit nous retourner un champ `user_type` qui peut contenir les valeurs `client`, `vendor` ou `admin`. Si ce champ n'est pas précisé le type par défaut est `client`; une fois l'utilisateur oauth créé dans la marketplace son type ne sera jamais changé même si le serveur oauth nous renvoie une valeur différente de celle d'origine (ou si elle n'en envoie pas), il est donc **impératif** que ce champ soit configuré correctement.

Afin de rajouter ce champ `user_type` il faut aller dans la console Okta dans le menu `API > Authoriation Servers`, choisir l'application utilisée pour la connection avec wizaplace puis aller dans l'onglet `Claims` et en ajouter un nouveau. Pour ce nouveau `Claim` il faut saisir `user_type` comme nom et `appuser.userType` pour le champ `Mapping` (les autres valeurs peuvent être laissées par défaut). Il faudra bien évidemment saisir le champ `userType` dans tous les profils des utilisateurs pouvant se connecter via cette application.

Exemple de la page d'ajout du `Claim`:

![okta claim](img/okta_claim.png)

> Configuration d'OKTA sur votre environnement de DEV : [OKTA](OKTA.md)

### Google

Ce provider est uniquement utilisé pour les tests lors des devs, chaque utilisateur se connectant via google sera considéré comme un client. Ce choix est hardcodé, il faut donc aller changer cette valeur dans le code si les tests effectués par le développeur portent sur une connexion admin ou vendeur.
