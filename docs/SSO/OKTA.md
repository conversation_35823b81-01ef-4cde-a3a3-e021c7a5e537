# OKTA

## Backoffice

Un compte préconfiguré :

- url : `https://dev-572685.okta.com/login/login.htm`
- user : `<EMAIL>`
- pass : `j6kyNOhISlids`

## Settings

Idéalement, créer un nouveau compte developer sur OKTA :
`https://developer.okta.com/`

Régler **User > Groups** selon les valeurs des paramètres .env : `OKTA_BO_VENDOR_GROUP`, `OKTA_BO_ADMIN_GROUP`
![groups](img/groups.png)

Régler **API > Authorization servers > default > claims** :
![claims](img/conf.png)

**Applications > Wizaplace > general** : Ajouter votre `REDIRECT_URI` dans la whitelist **General Settings > Login > Login redirect URIs** :
![redirect uri](img/add_redirect_uri_to_whitelist.png)

## .ENV

Exemple de settings pour le DEV.

```env
# OKTA Oauth
CURRENT_OAUTH_PROVIDER=okta
OKTA_BO_AUTHORIZATION_SERVER=https://dev-572685.okta.com/oauth2/default/.well-known/openid-configuration
OKTA_BO_REDIRECT_URI=[URL de l'instance de test]/admin.php/admin/oauth/authorize
OKTA_BO_CLIENT_ID=0oams7tzudaEpyFrX356
OKTA_BO_CLIENT_SECRET=1YCfYf3ufD3oGbPi9lzZlmqDoTTCZqOIdv7JarCH
OKTA_BO_VENDOR_GROUP=OKTA-WIZAPLACE_VENDEUR-DEV-CTRL
OKTA_BO_ADMIN_GROUP=OKTA-WIZAPLACE_ADMIN-DEV-CTRL
OKTA_BO_RESPONSE_TYPE=code
```

Régler `OKTA_BO_AUTHORIZATION_SERVER` selon la configuration de  **API > Authorization servers > default > settings** :
![settings](img/settings.png)

## Comptes OKTA

Vendeur :

- login : `<EMAIL>`
- pass : `6UZ2Cvl1qrlVQ`

Admin :

- login : `<EMAIL>`
- pass : `HlmpkQlr8pof2`
