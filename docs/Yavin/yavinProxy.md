# Yavin Proxy

Cette doc. explique l'origine de
la naissance du Yavin Proxy et décrit son identité.

## Acteurs

* Le nouveau backend/back-office en VueJs désigné par “BoVjs” dans la suite de ce document.
* La V1 Wizaplace qui sera désignée par “V1”.
* Les microservices vivants sur Yavin désignés par “MsYavin”.
* Le client HTTP YavinHttpClient intégré dans la V1

## Problématique

BoVjs souhaite faire deux opérations :

* utiliser les fonctionnalités existantes exposées par API dans la V1 ;
* utiliser les fonctionnalités existantes/futures exposées par API par les MsYavin.

Pour réussir un appel API depuis Wizaplace, il faut remplir deux conditions :

* fournir le token de l’utilisateur connecté peu importe le destinataire (V1 ou MsYavin) dans le header,
  pour les requêtes nécessitant une authentification seulement.
  En effet, certaines sont publiques et ne nécessitent pas de token ;
* utiliser un préfixe d' URL /v1.5/ pour les MsYavin et  /v1/ pour les API de la V1.

BoVjs a donc besoin du token de l’utilisateur courant afin de réussir
son appel API et effectuer l’opération.

BoVjs va s’exécuter côté client (navigateur) et s’il appelle les
API avec le token celui-ci peut-être intercepté et connu de l’utilisateur final et ouvrir
une brèche de sécurité.

## Entrée en scène du Yavin Proxy

Il faut constituer la requête et l’appeler côté PHP.

Ainsi, BoVjs va utiliser une URL de type
http://wizaplace.loc/proxy/api/v1.5/user-groups/{userGroupId}/members pour un appel à
un MsYavin ou http://wizaplace.loc/proxy/api/v1/user/{userId}/user-groups pour un appel
à une fonctionnalité exposée par l'API de la V1.

Ensuite ce sera la responsabilité du Yavin Proxy :

* de charger l’utilisateur courant et son token à partir de la session courante;
* transformer l’URL de destination à partir de l’URL envoyée par le BoVjs;
* construire la requête;
* envoyer la requête;
* renvoyer la réponse;
* logger les éventuelles erreurs.

Le Yavin Proxy devra écouter une route préfixée par “/proxy/”.

Le Yavin Proxy est un controller : Wizacha\AppBundle\Controller\ProxyController
et il utilise le Guzzle Yavin Client : Wizacha\Component\Http\Client\GuzzleYavinClient
pour envoyer les réquêtes et gérer la réponse.

## Exigences

* BoVjs devra s’assurer que les fonctionnalités appelées sont disponibles.

* BoVjs devra utiliser une URL préfixée par /proxy/api/v1.5/ ou /proxy/api/v1/ en fonction de la destination.

* BoVjs devra avoir connaissance de l’URL du back-end de destination, par exemple https://back.mon-client.wizaplace.com.

## Schéma

![Yavin proxy workflow](yavin_proxy.png)

## Comportements inattendus

Dans les cas ci-dessous une réponse 400 est renvoyée :

* Session de l'utilisateur est altérée

* Utilisateur courant pas en BDD

* Echec de l'envoi de la Request par le Yavin HTTP Client
