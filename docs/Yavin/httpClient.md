# HTTP CLIENT

## Description

L' HTTP client permet d'envoyer des requêtes sur les APIs exposées par les micro services Yavin.

## Comportement

Le client envoie (au travers de sa méthode `YavinClientInterface::send()`) une Request (`Wizacha\Component\Http\Request\RequestInterface`)
et renvoie :
* une Response (`Wizacha\Component\Http\Response\ResponseInterface`) si tout s'est bien passé,
* sinon une exception de type YavinClientException est levée (`Wizacha\Component\Http\YavinClientException`)

La Request est composée par son uri, sa méthode et ses options (`Wizacha\Component\Http\Option\OptionInterface`).

Le composant fournit les éléments concrets suivants :

### Un client concret : GuzzleYavinClient

Le GuzzleYavinClient (`Wizacha\Component\Http\Client\GuzzleYavinClient`) encapsule le client Guzzle (`GuzzleHttp\Client`).

### Une Request concrète : YavinRequest

La YavinRequest (`Wizacha\Component\Http\Request\YavinRequest`) sert à ajouter
des comportements supplémentaires (pattern decorator) comme la base URI, le HeaderOption et
la possibilité d'ajouter des options supplémentaires ou d'écraser des options existantes à une Request donnée.
Pour envoyer des requêtes à destination de Yavin, utilisez de préférence la YavinRequest.

Remarques :

* La YavinRequest permet d'ajouter via la méthode addOption() uniquement des options si elles
  ne sont pas déjà présentes dans la Request décorée et dans le HeaderOption.
  Toutefois si elles sont dèjà présentes, la YavinRequest permet de les écraser
  au travers de la méthode overrideOption()

* Les options peuvent être ajoutées au travers du StringOption (`Wizacha\Component\Http\Option\StringOption`)
  ou de l'ArrayOption (`Wizacha\Component\Http\Option\ArrayOption`)

### Une option concrète commune : le HeaderOption

Le HeaderOption (`Wizacha\Component\Http\Option\Common\HeaderOption`)
embarque une Authorization (`Wizacha\Component\Http\Authorization\AuthorizationInterface`) concrète basée sur le
token de l'utilisateur connecté courant : `Wizacha\Component\Http\Authorization\UserApiKeyAuthorization`.
Cette authorization est nécessaire car toutes les requêtes sortantes à destination de Yavin
doivent être authentifiées dans les options du header par l'api key de l'utilisateur courant.


## Usage

### Déterminer l'utilisateur courant

Aller chercher l'utilisateur courant (désigné par la variable `$user` ci-dessous) par la méthode (controller trait, user service, repository etc ...) que
vous souhaitez dans votre client (controller, command, test ... etc)

### Définir une Authorization basée sur l'utilisateur courant

Instancier l'Authorization en lui passant l'utilisateur :

```
$userApiKeyAuthorization = new \Wizacha\Component\Http\Authorization\UserApiKeyAuthorization($user);
```

### Définir une HeaderOption avec l'Authorization

Instancier le HeaderOption en lui passant l'Authorization :

```
$headerOption = new \Wizacha\Component\Http\Option\Common\HeaderOption($userApiKeyAuthorization);
```

### Définir la Request à envoyer

Instancier la requête que vous souhaitez envoyer.
  Exemple ici on instancie la requête UserGroupRetrieve :

```
class UserGroupRetrieve implements RequestInterface
{
    public function getMethod(): string
    {
        return 'GET';
    }

    public function getUri(): string
    {
        return 'user-groups';
    }

    public function getOptions(): array
    {
        return [];
    }
}

$userGroupRetrievRequest = new UserGroupRetrieve();

```

### Définir la YavinRequest

Instancier la YavinRequest : avec votre custom Request, la base Uri de l'API et le HeaderOption

```
$yavinRequest = new \Wizacha\Component\Http\Request\YavinRequest(
    $userGroupRetrievRequest,
    'http://wizaplace.loc/api/v1.5/',
    $headerOption
);
```

Remarque : La base URI pourra être récupérée via les paramètres de symfony ou un loader

### Definir le client

Définir (l'instancier ou le récupérer par le container symfony ou par injection de dépendance)
le client GuzzleYavinClient:

```
  $client = new \Wizacha\Component\Http\Client\GuzzleYavinClient(
    new \GuzzleHttp\Client(),
    new \Wizacha\Component\Http\YavinResponseFactory(),
    new \Wizacha\Component\Http\OptionMapper()
  );
```

Remarques :

* l'OptionMapper (`Wizacha\Component\Http\Option\OptionInterface\OptionMapper`) est nécessaire pour transformer
  le tableaux d'options renvoyés par la Request en tableau associatif clé => valeur.
* le YavinResponseFactory (` Wizacha\Component\Http\YavinResponseFactory`) permet de construire la ResponseInterface

### Envoyer la Request et récupérer la Response

Envoyer la Request avec le Client en gérant le comportement de l'exception :

```
try {
    $response = $client->send($yavinRequest);
} catch (\Wizacha\Component\Http\YavinClientException $e) {
    // log here
}
```

Remarque : la Response Yavin encapsule une Psr Response renvoyée par Guzzle.
La Response Yavin permet de récupérer directement le status code,
le body et les headers sans passer par la Psr Response.



