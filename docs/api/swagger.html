<!DOCTYPE html>
<html lang="en">

    <head>
        <meta charset="utf8" />
        <title>Wizaplace API Documentation</title>
        <link rel="icon" type="image/png" href="/assets/bundles/app/images/favicon.png" sizes="32x32"/>
        <!-- needed for adaptive design -->
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
            body {
                padding: 0;
                margin: 0;
            }
        </style>
        <link href="https://fonts.googleapis.com/css?family=Raleway:300,400,700|Roboto:300,400,700" rel="stylesheet">
    </head>

    <body id="redoc">
    <script src="https://cdn.jsdelivr.net/npm/redoc@next/bundles/redoc.standalone.js"> </script>
    <script type="application/javascript">
        Redoc.init('schema.json', {
            nativeScrollbars: true,
            hideHostname: true,
            theme: {
                typography: {
                    headings: {
                        fontFamily: "Raleway",
                        fontWeight: 600
                    },
                    links: {
                        color: "#E7365C"
                    }
                },
                colors: {
                    primary: {
                        main: "#6bc4d9"
                    }
                }
            }
        }, document.getElementById('redoc'))
    </script>
    </body>

</html>
