# Documentation de l'API

La documentation de l'API est directement disponible : http://wizaplace.loc/api/v1/doc/

Cette documentation est réalisée à l'aide de [ReDoc](https://github.com/Rebilly/ReDoc) via le format YAML OpenAPI 3.0.

Tout le schéma est dans un seul fichier pour pouvoir exploiter les $refs correctement et pouvoir valider le fichier directement.

## Edition

Dans PhpStorm, la vue "Structure" permet de naviguer facilement d'une section à une autre. 

Associez le fichier au schéma "openapi-3.0" pour avoir une aide à la complétion et une validation du fichier directement dans l'éditeur. Pour cela, dans PhpStorm, dans la fenêtre d'édition du fichier, dans la barre du bas, cliquez sur "No JSON schema" et sélectionnez "openapi-3.0" dans la liste déroulante.

## Génération

Si en allant sur l'URL http://wizaplace.loc/api/v1/doc vous avez une 404 c'est que la documentation n'est pas générée.

Pour la générer il faut lancer la commande : `make api-doc`

## Validation

- Pour afficher les alertes dans une fenêtre PhpStorm : Code > Inspect Code > File swagger.yaml.
- Pour valider le schéma JSON fichier en ligne de commande : `make swagger`
- Pour valider la structure OpenAPI du fichier : chargez le fichier dans https://editor.swagger.io/ (ou [lancez un container Docker de Swagger Editor en local](https://hub.docker.com/r/swaggerapi/swagger-editor/)).

