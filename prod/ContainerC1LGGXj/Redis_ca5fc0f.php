<?php

class Redis_ca5fc0f extends \Redis implements \ProxyManager\Proxy\VirtualProxyInterface
{
    private $valueHoldera1fd8 = null;
    private $initializere3996 = null;
    private static $publicProperties7ed0d = [
        
    ];
    public function _compress($value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '_compress', array('value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->_compress($value);
    }
    public function _uncompress($value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '_uncompress', array('value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->_uncompress($value);
    }
    public function _prefix($key)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '_prefix', array('key' => $key), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->_prefix($key);
    }
    public function _serialize($value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '_serialize', array('value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->_serialize($value);
    }
    public function _unserialize($value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '_unserialize', array('value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->_unserialize($value);
    }
    public function _pack($value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '_pack', array('value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->_pack($value);
    }
    public function _unpack($value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '_unpack', array('value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->_unpack($value);
    }
    public function acl($subcmd, ... $args)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'acl', array('subcmd' => $subcmd, 'args' => $args), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->acl($subcmd, ...$args);
    }
    public function append($key, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'append', array('key' => $key, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->append($key, $value);
    }
    public function auth($credentials)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'auth', array('credentials' => $credentials), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->auth($credentials);
    }
    public function bgSave()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'bgSave', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->bgSave();
    }
    public function bgrewriteaof()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'bgrewriteaof', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->bgrewriteaof();
    }
    public function bitcount($key, $start = null, $end = null, $bybit = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'bitcount', array('key' => $key, 'start' => $start, 'end' => $end, 'bybit' => $bybit), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->bitcount($key, $start, $end, $bybit);
    }
    public function bitop($operation, $deskey, $srckey, ... $other_keys)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'bitop', array('operation' => $operation, 'deskey' => $deskey, 'srckey' => $srckey, 'other_keys' => $other_keys), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->bitop($operation, $deskey, $srckey, ...$other_keys);
    }
    public function bitpos($key, $bit, $start = null, $end = null, $bybit = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'bitpos', array('key' => $key, 'bit' => $bit, 'start' => $start, 'end' => $end, 'bybit' => $bybit), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->bitpos($key, $bit, $start, $end, $bybit);
    }
    public function blPop($key_or_keys, $timeout_or_key, ... $extra_args)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'blPop', array('key_or_keys' => $key_or_keys, 'timeout_or_key' => $timeout_or_key, 'extra_args' => $extra_args), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->blPop($key_or_keys, $timeout_or_key, ...$extra_args);
    }
    public function brPop($key_or_keys, $timeout_or_key, ... $extra_args)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'brPop', array('key_or_keys' => $key_or_keys, 'timeout_or_key' => $timeout_or_key, 'extra_args' => $extra_args), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->brPop($key_or_keys, $timeout_or_key, ...$extra_args);
    }
    public function brpoplpush($src, $dst, $timeout)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'brpoplpush', array('src' => $src, 'dst' => $dst, 'timeout' => $timeout), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->brpoplpush($src, $dst, $timeout);
    }
    public function bzPopMax($key, $timeout_or_key, ... $extra_args)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'bzPopMax', array('key' => $key, 'timeout_or_key' => $timeout_or_key, 'extra_args' => $extra_args), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->bzPopMax($key, $timeout_or_key, ...$extra_args);
    }
    public function bzPopMin($key, $timeout_or_key, ... $extra_args)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'bzPopMin', array('key' => $key, 'timeout_or_key' => $timeout_or_key, 'extra_args' => $extra_args), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->bzPopMin($key, $timeout_or_key, ...$extra_args);
    }
    public function bzmpop($timeout, $keys, $from, $count = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'bzmpop', array('timeout' => $timeout, 'keys' => $keys, 'from' => $from, 'count' => $count), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->bzmpop($timeout, $keys, $from, $count);
    }
    public function zmpop($keys, $from, $count = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zmpop', array('keys' => $keys, 'from' => $from, 'count' => $count), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zmpop($keys, $from, $count);
    }
    public function blmpop($timeout, $keys, $from, $count = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'blmpop', array('timeout' => $timeout, 'keys' => $keys, 'from' => $from, 'count' => $count), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->blmpop($timeout, $keys, $from, $count);
    }
    public function lmpop($keys, $from, $count = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'lmpop', array('keys' => $keys, 'from' => $from, 'count' => $count), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->lmpop($keys, $from, $count);
    }
    public function clearLastError()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'clearLastError', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->clearLastError();
    }
    public function client($opt, ... $args)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'client', array('opt' => $opt, 'args' => $args), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->client($opt, ...$args);
    }
    public function close()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'close', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->close();
    }
    public function command($opt = null, ... $args)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'command', array('opt' => $opt, 'args' => $args), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->command($opt, ...$args);
    }
    public function config($operation, $key_or_settings = null, $value = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'config', array('operation' => $operation, 'key_or_settings' => $key_or_settings, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->config($operation, $key_or_settings, $value);
    }
    public function connect($host, $port = null, $timeout = null, $persistent_id = null, $retry_interval = null, $read_timeout = null, $context = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'connect', array('host' => $host, 'port' => $port, 'timeout' => $timeout, 'persistent_id' => $persistent_id, 'retry_interval' => $retry_interval, 'read_timeout' => $read_timeout, 'context' => $context), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->connect($host, $port, $timeout, $persistent_id, $retry_interval, $read_timeout, $context);
    }
    public function copy($src, $dst, $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'copy', array('src' => $src, 'dst' => $dst, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->copy($src, $dst, $options);
    }
    public function dbSize()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'dbSize', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->dbSize();
    }
    public function debug($key)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'debug', array('key' => $key), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->debug($key);
    }
    public function decr($key, $by = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'decr', array('key' => $key, 'by' => $by), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->decr($key, $by);
    }
    public function decrBy($key, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'decrBy', array('key' => $key, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->decrBy($key, $value);
    }
    public function del($key, ... $other_keys)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'del', array('key' => $key, 'other_keys' => $other_keys), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->del($key, ...$other_keys);
    }
    public function delete($key, ... $other_keys)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'delete', array('key' => $key, 'other_keys' => $other_keys), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->delete($key, ...$other_keys);
    }
    public function discard()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'discard', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->discard();
    }
    public function dump($key)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'dump', array('key' => $key), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->dump($key);
    }
    public function echo($str)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'echo', array('str' => $str), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->echo($str);
    }
    public function eval($script, $args = null, $num_keys = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'eval', array('script' => $script, 'args' => $args, 'num_keys' => $num_keys), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->eval($script, $args, $num_keys);
    }
    public function eval_ro($script_sha, $args = null, $num_keys = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'eval_ro', array('script_sha' => $script_sha, 'args' => $args, 'num_keys' => $num_keys), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->eval_ro($script_sha, $args, $num_keys);
    }
    public function evalsha($sha1, $args = null, $num_keys = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'evalsha', array('sha1' => $sha1, 'args' => $args, 'num_keys' => $num_keys), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->evalsha($sha1, $args, $num_keys);
    }
    public function evalsha_ro($sha1, $args = null, $num_keys = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'evalsha_ro', array('sha1' => $sha1, 'args' => $args, 'num_keys' => $num_keys), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->evalsha_ro($sha1, $args, $num_keys);
    }
    public function exec()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'exec', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->exec();
    }
    public function exists($key, ... $other_keys)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'exists', array('key' => $key, 'other_keys' => $other_keys), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->exists($key, ...$other_keys);
    }
    public function expire($key, $timeout, $mode = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'expire', array('key' => $key, 'timeout' => $timeout, 'mode' => $mode), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->expire($key, $timeout, $mode);
    }
    public function expireAt($key, $timestamp, $mode = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'expireAt', array('key' => $key, 'timestamp' => $timestamp, 'mode' => $mode), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->expireAt($key, $timestamp, $mode);
    }
    public function failover($to = null, $abort = null, $timeout = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'failover', array('to' => $to, 'abort' => $abort, 'timeout' => $timeout), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->failover($to, $abort, $timeout);
    }
    public function expiretime($key)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'expiretime', array('key' => $key), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->expiretime($key);
    }
    public function pexpiretime($key)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'pexpiretime', array('key' => $key), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->pexpiretime($key);
    }
    public function fcall($fn, $keys = null, $args = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'fcall', array('fn' => $fn, 'keys' => $keys, 'args' => $args), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->fcall($fn, $keys, $args);
    }
    public function fcall_ro($fn, $keys = null, $args = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'fcall_ro', array('fn' => $fn, 'keys' => $keys, 'args' => $args), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->fcall_ro($fn, $keys, $args);
    }
    public function flushAll($sync = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'flushAll', array('sync' => $sync), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->flushAll($sync);
    }
    public function flushDB($sync = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'flushDB', array('sync' => $sync), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->flushDB($sync);
    }
    public function function($operation, ... $args)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'function', array('operation' => $operation, 'args' => $args), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->function($operation, ...$args);
    }
    public function geoadd($key, $lng, $lat, $member, ... $other_triples_and_options)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'geoadd', array('key' => $key, 'lng' => $lng, 'lat' => $lat, 'member' => $member, 'other_triples_and_options' => $other_triples_and_options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->geoadd($key, $lng, $lat, $member, ...$other_triples_and_options);
    }
    public function geodist($key, $src, $dst, $unit = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'geodist', array('key' => $key, 'src' => $src, 'dst' => $dst, 'unit' => $unit), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->geodist($key, $src, $dst, $unit);
    }
    public function geohash($key, $member, ... $other_members)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'geohash', array('key' => $key, 'member' => $member, 'other_members' => $other_members), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->geohash($key, $member, ...$other_members);
    }
    public function geopos($key, $member, ... $other_members)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'geopos', array('key' => $key, 'member' => $member, 'other_members' => $other_members), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->geopos($key, $member, ...$other_members);
    }
    public function georadius($key, $lng, $lat, $radius, $unit, $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'georadius', array('key' => $key, 'lng' => $lng, 'lat' => $lat, 'radius' => $radius, 'unit' => $unit, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->georadius($key, $lng, $lat, $radius, $unit, $options);
    }
    public function georadius_ro($key, $lng, $lat, $radius, $unit, $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'georadius_ro', array('key' => $key, 'lng' => $lng, 'lat' => $lat, 'radius' => $radius, 'unit' => $unit, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->georadius_ro($key, $lng, $lat, $radius, $unit, $options);
    }
    public function georadiusbymember($key, $member, $radius, $unit, $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'georadiusbymember', array('key' => $key, 'member' => $member, 'radius' => $radius, 'unit' => $unit, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->georadiusbymember($key, $member, $radius, $unit, $options);
    }
    public function georadiusbymember_ro($key, $member, $radius, $unit, $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'georadiusbymember_ro', array('key' => $key, 'member' => $member, 'radius' => $radius, 'unit' => $unit, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->georadiusbymember_ro($key, $member, $radius, $unit, $options);
    }
    public function geosearch($key, $position, $shape, $unit, $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'geosearch', array('key' => $key, 'position' => $position, 'shape' => $shape, 'unit' => $unit, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->geosearch($key, $position, $shape, $unit, $options);
    }
    public function geosearchstore($dst, $src, $position, $shape, $unit, $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'geosearchstore', array('dst' => $dst, 'src' => $src, 'position' => $position, 'shape' => $shape, 'unit' => $unit, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->geosearchstore($dst, $src, $position, $shape, $unit, $options);
    }
    public function get($key)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'get', array('key' => $key), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->get($key);
    }
    public function getAuth()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getAuth', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getAuth();
    }
    public function getBit($key, $idx)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getBit', array('key' => $key, 'idx' => $idx), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getBit($key, $idx);
    }
    public function getEx($key, $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getEx', array('key' => $key, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getEx($key, $options);
    }
    public function getDBNum()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getDBNum', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getDBNum();
    }
    public function getDel($key)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getDel', array('key' => $key), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getDel($key);
    }
    public function getHost()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getHost', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getHost();
    }
    public function getLastError()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getLastError', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getLastError();
    }
    public function getMode()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getMode', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getMode();
    }
    public function getOption($option)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getOption', array('option' => $option), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getOption($option);
    }
    public function getPersistentID()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getPersistentID', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getPersistentID();
    }
    public function getPort()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getPort', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getPort();
    }
    public function getRange($key, $start, $end)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getRange', array('key' => $key, 'start' => $start, 'end' => $end), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getRange($key, $start, $end);
    }
    public function lcs($key1, $key2, $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'lcs', array('key1' => $key1, 'key2' => $key2, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->lcs($key1, $key2, $options);
    }
    public function getReadTimeout()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getReadTimeout', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getReadTimeout();
    }
    public function getset($key, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getset', array('key' => $key, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getset($key, $value);
    }
    public function getTimeout()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getTimeout', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getTimeout();
    }
    public function getTransferredBytes()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getTransferredBytes', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getTransferredBytes();
    }
    public function clearTransferredBytes()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'clearTransferredBytes', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->clearTransferredBytes();
    }
    public function hDel($key, $field, ... $other_fields)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'hDel', array('key' => $key, 'field' => $field, 'other_fields' => $other_fields), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->hDel($key, $field, ...$other_fields);
    }
    public function hExists($key, $field)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'hExists', array('key' => $key, 'field' => $field), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->hExists($key, $field);
    }
    public function hGet($key, $member)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'hGet', array('key' => $key, 'member' => $member), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->hGet($key, $member);
    }
    public function hGetAll($key)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'hGetAll', array('key' => $key), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->hGetAll($key);
    }
    public function hIncrBy($key, $field, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'hIncrBy', array('key' => $key, 'field' => $field, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->hIncrBy($key, $field, $value);
    }
    public function hIncrByFloat($key, $field, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'hIncrByFloat', array('key' => $key, 'field' => $field, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->hIncrByFloat($key, $field, $value);
    }
    public function hKeys($key)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'hKeys', array('key' => $key), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->hKeys($key);
    }
    public function hLen($key)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'hLen', array('key' => $key), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->hLen($key);
    }
    public function hMget($key, $fields)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'hMget', array('key' => $key, 'fields' => $fields), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->hMget($key, $fields);
    }
    public function hMset($key, $fieldvals)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'hMset', array('key' => $key, 'fieldvals' => $fieldvals), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->hMset($key, $fieldvals);
    }
    public function hRandField($key, $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'hRandField', array('key' => $key, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->hRandField($key, $options);
    }
    public function hSet($key, $member, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'hSet', array('key' => $key, 'member' => $member, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->hSet($key, $member, $value);
    }
    public function hSetNx($key, $field, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'hSetNx', array('key' => $key, 'field' => $field, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->hSetNx($key, $field, $value);
    }
    public function hStrLen($key, $field)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'hStrLen', array('key' => $key, 'field' => $field), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->hStrLen($key, $field);
    }
    public function hVals($key)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'hVals', array('key' => $key), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->hVals($key);
    }
    public function hscan($key, &$iterator, $pattern = null, $count = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'hscan', array('key' => $key, 'iterator' => $iterator, 'pattern' => $pattern, 'count' => $count), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->hscan($key, $iterator, $pattern, $count);
    }
    public function incr($key, $by = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'incr', array('key' => $key, 'by' => $by), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->incr($key, $by);
    }
    public function incrBy($key, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'incrBy', array('key' => $key, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->incrBy($key, $value);
    }
    public function incrByFloat($key, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'incrByFloat', array('key' => $key, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->incrByFloat($key, $value);
    }
    public function info(... $sections)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'info', array('sections' => $sections), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->info(...$sections);
    }
    public function isConnected()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'isConnected', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->isConnected();
    }
    public function keys($pattern)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'keys', array('pattern' => $pattern), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->keys($pattern);
    }
    public function lInsert($key, $pos, $pivot, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'lInsert', array('key' => $key, 'pos' => $pos, 'pivot' => $pivot, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->lInsert($key, $pos, $pivot, $value);
    }
    public function lLen($key)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'lLen', array('key' => $key), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->lLen($key);
    }
    public function lMove($src, $dst, $wherefrom, $whereto)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'lMove', array('src' => $src, 'dst' => $dst, 'wherefrom' => $wherefrom, 'whereto' => $whereto), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->lMove($src, $dst, $wherefrom, $whereto);
    }
    public function blmove($src, $dst, $wherefrom, $whereto, $timeout)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'blmove', array('src' => $src, 'dst' => $dst, 'wherefrom' => $wherefrom, 'whereto' => $whereto, 'timeout' => $timeout), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->blmove($src, $dst, $wherefrom, $whereto, $timeout);
    }
    public function lPop($key, $count = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'lPop', array('key' => $key, 'count' => $count), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->lPop($key, $count);
    }
    public function lPos($key, $value, $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'lPos', array('key' => $key, 'value' => $value, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->lPos($key, $value, $options);
    }
    public function lPush($key, ... $elements)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'lPush', array('key' => $key, 'elements' => $elements), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->lPush($key, ...$elements);
    }
    public function rPush($key, ... $elements)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'rPush', array('key' => $key, 'elements' => $elements), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->rPush($key, ...$elements);
    }
    public function lPushx($key, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'lPushx', array('key' => $key, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->lPushx($key, $value);
    }
    public function rPushx($key, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'rPushx', array('key' => $key, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->rPushx($key, $value);
    }
    public function lSet($key, $index, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'lSet', array('key' => $key, 'index' => $index, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->lSet($key, $index, $value);
    }
    public function lastSave()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'lastSave', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->lastSave();
    }
    public function lindex($key, $index)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'lindex', array('key' => $key, 'index' => $index), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->lindex($key, $index);
    }
    public function lrange($key, $start, $end)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'lrange', array('key' => $key, 'start' => $start, 'end' => $end), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->lrange($key, $start, $end);
    }
    public function lrem($key, $value, $count = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'lrem', array('key' => $key, 'value' => $value, 'count' => $count), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->lrem($key, $value, $count);
    }
    public function ltrim($key, $start, $end)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'ltrim', array('key' => $key, 'start' => $start, 'end' => $end), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->ltrim($key, $start, $end);
    }
    public function mget($keys)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'mget', array('keys' => $keys), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->mget($keys);
    }
    public function migrate($host, $port, $key, $dstdb, $timeout, $copy = null, $replace = null, $credentials = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'migrate', array('host' => $host, 'port' => $port, 'key' => $key, 'dstdb' => $dstdb, 'timeout' => $timeout, 'copy' => $copy, 'replace' => $replace, 'credentials' => $credentials), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->migrate($host, $port, $key, $dstdb, $timeout, $copy, $replace, $credentials);
    }
    public function move($key, $index)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'move', array('key' => $key, 'index' => $index), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->move($key, $index);
    }
    public function mset($key_values)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'mset', array('key_values' => $key_values), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->mset($key_values);
    }
    public function msetnx($key_values)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'msetnx', array('key_values' => $key_values), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->msetnx($key_values);
    }
    public function multi($value = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'multi', array('value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->multi($value);
    }
    public function object($subcommand, $key)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'object', array('subcommand' => $subcommand, 'key' => $key), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->object($subcommand, $key);
    }
    public function open($host, $port = null, $timeout = null, $persistent_id = null, $retry_interval = null, $read_timeout = null, $context = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'open', array('host' => $host, 'port' => $port, 'timeout' => $timeout, 'persistent_id' => $persistent_id, 'retry_interval' => $retry_interval, 'read_timeout' => $read_timeout, 'context' => $context), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->open($host, $port, $timeout, $persistent_id, $retry_interval, $read_timeout, $context);
    }
    public function pconnect($host, $port = null, $timeout = null, $persistent_id = null, $retry_interval = null, $read_timeout = null, $context = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'pconnect', array('host' => $host, 'port' => $port, 'timeout' => $timeout, 'persistent_id' => $persistent_id, 'retry_interval' => $retry_interval, 'read_timeout' => $read_timeout, 'context' => $context), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->pconnect($host, $port, $timeout, $persistent_id, $retry_interval, $read_timeout, $context);
    }
    public function persist($key)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'persist', array('key' => $key), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->persist($key);
    }
    public function pexpire($key, $timeout, $mode = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'pexpire', array('key' => $key, 'timeout' => $timeout, 'mode' => $mode), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->pexpire($key, $timeout, $mode);
    }
    public function pexpireAt($key, $timestamp, $mode = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'pexpireAt', array('key' => $key, 'timestamp' => $timestamp, 'mode' => $mode), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->pexpireAt($key, $timestamp, $mode);
    }
    public function pfadd($key, $elements)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'pfadd', array('key' => $key, 'elements' => $elements), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->pfadd($key, $elements);
    }
    public function pfcount($key_or_keys)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'pfcount', array('key_or_keys' => $key_or_keys), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->pfcount($key_or_keys);
    }
    public function pfmerge($dst, $srckeys)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'pfmerge', array('dst' => $dst, 'srckeys' => $srckeys), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->pfmerge($dst, $srckeys);
    }
    public function ping($message = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'ping', array('message' => $message), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->ping($message);
    }
    public function pipeline()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'pipeline', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->pipeline();
    }
    public function popen($host, $port = null, $timeout = null, $persistent_id = null, $retry_interval = null, $read_timeout = null, $context = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'popen', array('host' => $host, 'port' => $port, 'timeout' => $timeout, 'persistent_id' => $persistent_id, 'retry_interval' => $retry_interval, 'read_timeout' => $read_timeout, 'context' => $context), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->popen($host, $port, $timeout, $persistent_id, $retry_interval, $read_timeout, $context);
    }
    public function psetex($key, $expire, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'psetex', array('key' => $key, 'expire' => $expire, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->psetex($key, $expire, $value);
    }
    public function psubscribe($patterns, $cb)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'psubscribe', array('patterns' => $patterns, 'cb' => $cb), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->psubscribe($patterns, $cb);
    }
    public function pttl($key)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'pttl', array('key' => $key), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->pttl($key);
    }
    public function publish($channel, $message)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'publish', array('channel' => $channel, 'message' => $message), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->publish($channel, $message);
    }
    public function pubsub($command, $arg = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'pubsub', array('command' => $command, 'arg' => $arg), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->pubsub($command, $arg);
    }
    public function punsubscribe($patterns)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'punsubscribe', array('patterns' => $patterns), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->punsubscribe($patterns);
    }
    public function rPop($key, $count = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'rPop', array('key' => $key, 'count' => $count), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->rPop($key, $count);
    }
    public function randomKey()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'randomKey', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->randomKey();
    }
    public function rawcommand($command, ... $args)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'rawcommand', array('command' => $command, 'args' => $args), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->rawcommand($command, ...$args);
    }
    public function rename($old_name, $new_name)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'rename', array('old_name' => $old_name, 'new_name' => $new_name), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->rename($old_name, $new_name);
    }
    public function renameNx($key_src, $key_dst)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'renameNx', array('key_src' => $key_src, 'key_dst' => $key_dst), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->renameNx($key_src, $key_dst);
    }
    public function reset()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'reset', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->reset();
    }
    public function restore($key, $ttl, $value, $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'restore', array('key' => $key, 'ttl' => $ttl, 'value' => $value, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->restore($key, $ttl, $value, $options);
    }
    public function role()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'role', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->role();
    }
    public function rpoplpush($srckey, $dstkey)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'rpoplpush', array('srckey' => $srckey, 'dstkey' => $dstkey), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->rpoplpush($srckey, $dstkey);
    }
    public function sAdd($key, $value, ... $other_values)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'sAdd', array('key' => $key, 'value' => $value, 'other_values' => $other_values), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->sAdd($key, $value, ...$other_values);
    }
    public function sAddArray($key, $values)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'sAddArray', array('key' => $key, 'values' => $values), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->sAddArray($key, $values);
    }
    public function sDiff($key, ... $other_keys)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'sDiff', array('key' => $key, 'other_keys' => $other_keys), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->sDiff($key, ...$other_keys);
    }
    public function sDiffStore($dst, $key, ... $other_keys)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'sDiffStore', array('dst' => $dst, 'key' => $key, 'other_keys' => $other_keys), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->sDiffStore($dst, $key, ...$other_keys);
    }
    public function sInter($key, ... $other_keys)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'sInter', array('key' => $key, 'other_keys' => $other_keys), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->sInter($key, ...$other_keys);
    }
    public function sintercard($keys, $limit = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'sintercard', array('keys' => $keys, 'limit' => $limit), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->sintercard($keys, $limit);
    }
    public function sInterStore($key, ... $other_keys)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'sInterStore', array('key' => $key, 'other_keys' => $other_keys), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->sInterStore($key, ...$other_keys);
    }
    public function sMembers($key)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'sMembers', array('key' => $key), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->sMembers($key);
    }
    public function sMisMember($key, $member, ... $other_members)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'sMisMember', array('key' => $key, 'member' => $member, 'other_members' => $other_members), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->sMisMember($key, $member, ...$other_members);
    }
    public function sMove($src, $dst, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'sMove', array('src' => $src, 'dst' => $dst, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->sMove($src, $dst, $value);
    }
    public function sPop($key, $count = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'sPop', array('key' => $key, 'count' => $count), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->sPop($key, $count);
    }
    public function sRandMember($key, $count = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'sRandMember', array('key' => $key, 'count' => $count), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->sRandMember($key, $count);
    }
    public function sUnion($key, ... $other_keys)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'sUnion', array('key' => $key, 'other_keys' => $other_keys), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->sUnion($key, ...$other_keys);
    }
    public function sUnionStore($dst, $key, ... $other_keys)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'sUnionStore', array('dst' => $dst, 'key' => $key, 'other_keys' => $other_keys), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->sUnionStore($dst, $key, ...$other_keys);
    }
    public function save()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'save', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->save();
    }
    public function scan(&$iterator, $pattern = null, $count = null, $type = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'scan', array('iterator' => $iterator, 'pattern' => $pattern, 'count' => $count, 'type' => $type), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->scan($iterator, $pattern, $count, $type);
    }
    public function scard($key)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'scard', array('key' => $key), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->scard($key);
    }
    public function script($command, ... $args)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'script', array('command' => $command, 'args' => $args), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->script($command, ...$args);
    }
    public function select($db)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'select', array('db' => $db), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->select($db);
    }
    public function set($key, $value, $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'set', array('key' => $key, 'value' => $value, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->set($key, $value, $options);
    }
    public function setBit($key, $idx, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'setBit', array('key' => $key, 'idx' => $idx, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->setBit($key, $idx, $value);
    }
    public function setRange($key, $index, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'setRange', array('key' => $key, 'index' => $index, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->setRange($key, $index, $value);
    }
    public function setOption($option, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'setOption', array('option' => $option, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->setOption($option, $value);
    }
    public function setex($key, $expire, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'setex', array('key' => $key, 'expire' => $expire, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->setex($key, $expire, $value);
    }
    public function setnx($key, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'setnx', array('key' => $key, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->setnx($key, $value);
    }
    public function sismember($key, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'sismember', array('key' => $key, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->sismember($key, $value);
    }
    public function slaveof($host = null, $port = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'slaveof', array('host' => $host, 'port' => $port), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->slaveof($host, $port);
    }
    public function replicaof($host = null, $port = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'replicaof', array('host' => $host, 'port' => $port), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->replicaof($host, $port);
    }
    public function touch($key_or_array, ... $more_keys)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'touch', array('key_or_array' => $key_or_array, 'more_keys' => $more_keys), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->touch($key_or_array, ...$more_keys);
    }
    public function slowlog($operation, $length = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'slowlog', array('operation' => $operation, 'length' => $length), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->slowlog($operation, $length);
    }
    public function sort($key, $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'sort', array('key' => $key, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->sort($key, $options);
    }
    public function sort_ro($key, $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'sort_ro', array('key' => $key, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->sort_ro($key, $options);
    }
    public function sortAsc($key, $pattern = null, $get = null, $offset = null, $count = null, $store = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'sortAsc', array('key' => $key, 'pattern' => $pattern, 'get' => $get, 'offset' => $offset, 'count' => $count, 'store' => $store), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->sortAsc($key, $pattern, $get, $offset, $count, $store);
    }
    public function sortAscAlpha($key, $pattern = null, $get = null, $offset = null, $count = null, $store = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'sortAscAlpha', array('key' => $key, 'pattern' => $pattern, 'get' => $get, 'offset' => $offset, 'count' => $count, 'store' => $store), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->sortAscAlpha($key, $pattern, $get, $offset, $count, $store);
    }
    public function sortDesc($key, $pattern = null, $get = null, $offset = null, $count = null, $store = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'sortDesc', array('key' => $key, 'pattern' => $pattern, 'get' => $get, 'offset' => $offset, 'count' => $count, 'store' => $store), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->sortDesc($key, $pattern, $get, $offset, $count, $store);
    }
    public function sortDescAlpha($key, $pattern = null, $get = null, $offset = null, $count = null, $store = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'sortDescAlpha', array('key' => $key, 'pattern' => $pattern, 'get' => $get, 'offset' => $offset, 'count' => $count, 'store' => $store), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->sortDescAlpha($key, $pattern, $get, $offset, $count, $store);
    }
    public function srem($key, $value, ... $other_values)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'srem', array('key' => $key, 'value' => $value, 'other_values' => $other_values), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->srem($key, $value, ...$other_values);
    }
    public function sscan($key, &$iterator, $pattern = null, $count = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'sscan', array('key' => $key, 'iterator' => $iterator, 'pattern' => $pattern, 'count' => $count), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->sscan($key, $iterator, $pattern, $count);
    }
    public function ssubscribe($channels, $cb)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'ssubscribe', array('channels' => $channels, 'cb' => $cb), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->ssubscribe($channels, $cb);
    }
    public function strlen($key)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'strlen', array('key' => $key), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->strlen($key);
    }
    public function subscribe($channels, $cb)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'subscribe', array('channels' => $channels, 'cb' => $cb), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->subscribe($channels, $cb);
    }
    public function sunsubscribe($channels)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'sunsubscribe', array('channels' => $channels), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->sunsubscribe($channels);
    }
    public function swapdb($src, $dst)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'swapdb', array('src' => $src, 'dst' => $dst), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->swapdb($src, $dst);
    }
    public function time()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'time', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->time();
    }
    public function ttl($key)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'ttl', array('key' => $key), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->ttl($key);
    }
    public function type($key)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'type', array('key' => $key), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->type($key);
    }
    public function unlink($key, ... $other_keys)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'unlink', array('key' => $key, 'other_keys' => $other_keys), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->unlink($key, ...$other_keys);
    }
    public function unsubscribe($channels)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'unsubscribe', array('channels' => $channels), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->unsubscribe($channels);
    }
    public function unwatch()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'unwatch', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->unwatch();
    }
    public function watch($key, ... $other_keys)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'watch', array('key' => $key, 'other_keys' => $other_keys), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->watch($key, ...$other_keys);
    }
    public function wait($numreplicas, $timeout)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'wait', array('numreplicas' => $numreplicas, 'timeout' => $timeout), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->wait($numreplicas, $timeout);
    }
    public function xack($key, $group, $ids)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'xack', array('key' => $key, 'group' => $group, 'ids' => $ids), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->xack($key, $group, $ids);
    }
    public function xadd($key, $id, $values, $maxlen = null, $approx = null, $nomkstream = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'xadd', array('key' => $key, 'id' => $id, 'values' => $values, 'maxlen' => $maxlen, 'approx' => $approx, 'nomkstream' => $nomkstream), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->xadd($key, $id, $values, $maxlen, $approx, $nomkstream);
    }
    public function xautoclaim($key, $group, $consumer, $min_idle, $start, $count = null, $justid = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'xautoclaim', array('key' => $key, 'group' => $group, 'consumer' => $consumer, 'min_idle' => $min_idle, 'start' => $start, 'count' => $count, 'justid' => $justid), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->xautoclaim($key, $group, $consumer, $min_idle, $start, $count, $justid);
    }
    public function xclaim($key, $group, $consumer, $min_idle, $ids, $options)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'xclaim', array('key' => $key, 'group' => $group, 'consumer' => $consumer, 'min_idle' => $min_idle, 'ids' => $ids, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->xclaim($key, $group, $consumer, $min_idle, $ids, $options);
    }
    public function xdel($key, $ids)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'xdel', array('key' => $key, 'ids' => $ids), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->xdel($key, $ids);
    }
    public function xgroup($operation, $key = null, $group = null, $id_or_consumer = null, $mkstream = null, $entries_read = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'xgroup', array('operation' => $operation, 'key' => $key, 'group' => $group, 'id_or_consumer' => $id_or_consumer, 'mkstream' => $mkstream, 'entries_read' => $entries_read), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->xgroup($operation, $key, $group, $id_or_consumer, $mkstream, $entries_read);
    }
    public function xinfo($operation, $arg1 = null, $arg2 = null, $count = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'xinfo', array('operation' => $operation, 'arg1' => $arg1, 'arg2' => $arg2, 'count' => $count), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->xinfo($operation, $arg1, $arg2, $count);
    }
    public function xlen($key)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'xlen', array('key' => $key), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->xlen($key);
    }
    public function xpending($key, $group, $start = null, $end = null, $count = null, $consumer = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'xpending', array('key' => $key, 'group' => $group, 'start' => $start, 'end' => $end, 'count' => $count, 'consumer' => $consumer), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->xpending($key, $group, $start, $end, $count, $consumer);
    }
    public function xrange($key, $start, $end, $count = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'xrange', array('key' => $key, 'start' => $start, 'end' => $end, 'count' => $count), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->xrange($key, $start, $end, $count);
    }
    public function xread($streams, $count = null, $block = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'xread', array('streams' => $streams, 'count' => $count, 'block' => $block), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->xread($streams, $count, $block);
    }
    public function xreadgroup($group, $consumer, $streams, $count = null, $block = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'xreadgroup', array('group' => $group, 'consumer' => $consumer, 'streams' => $streams, 'count' => $count, 'block' => $block), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->xreadgroup($group, $consumer, $streams, $count, $block);
    }
    public function xrevrange($key, $end, $start, $count = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'xrevrange', array('key' => $key, 'end' => $end, 'start' => $start, 'count' => $count), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->xrevrange($key, $end, $start, $count);
    }
    public function xtrim($key, $threshold, $approx = null, $minid = null, $limit = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'xtrim', array('key' => $key, 'threshold' => $threshold, 'approx' => $approx, 'minid' => $minid, 'limit' => $limit), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->xtrim($key, $threshold, $approx, $minid, $limit);
    }
    public function zAdd($key, $score_or_options, ... $more_scores_and_mems)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zAdd', array('key' => $key, 'score_or_options' => $score_or_options, 'more_scores_and_mems' => $more_scores_and_mems), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zAdd($key, $score_or_options, ...$more_scores_and_mems);
    }
    public function zCard($key)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zCard', array('key' => $key), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zCard($key);
    }
    public function zCount($key, $start, $end)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zCount', array('key' => $key, 'start' => $start, 'end' => $end), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zCount($key, $start, $end);
    }
    public function zIncrBy($key, $value, $member)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zIncrBy', array('key' => $key, 'value' => $value, 'member' => $member), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zIncrBy($key, $value, $member);
    }
    public function zLexCount($key, $min, $max)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zLexCount', array('key' => $key, 'min' => $min, 'max' => $max), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zLexCount($key, $min, $max);
    }
    public function zMscore($key, $member, ... $other_members)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zMscore', array('key' => $key, 'member' => $member, 'other_members' => $other_members), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zMscore($key, $member, ...$other_members);
    }
    public function zPopMax($key, $count = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zPopMax', array('key' => $key, 'count' => $count), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zPopMax($key, $count);
    }
    public function zPopMin($key, $count = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zPopMin', array('key' => $key, 'count' => $count), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zPopMin($key, $count);
    }
    public function zRange($key, $start, $end, $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zRange', array('key' => $key, 'start' => $start, 'end' => $end, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zRange($key, $start, $end, $options);
    }
    public function zRangeByLex($key, $min, $max, $offset = null, $count = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zRangeByLex', array('key' => $key, 'min' => $min, 'max' => $max, 'offset' => $offset, 'count' => $count), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zRangeByLex($key, $min, $max, $offset, $count);
    }
    public function zRangeByScore($key, $start, $end, $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zRangeByScore', array('key' => $key, 'start' => $start, 'end' => $end, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zRangeByScore($key, $start, $end, $options);
    }
    public function zrangestore($dstkey, $srckey, $start, $end, $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zrangestore', array('dstkey' => $dstkey, 'srckey' => $srckey, 'start' => $start, 'end' => $end, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zrangestore($dstkey, $srckey, $start, $end, $options);
    }
    public function zRandMember($key, $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zRandMember', array('key' => $key, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zRandMember($key, $options);
    }
    public function zRank($key, $member)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zRank', array('key' => $key, 'member' => $member), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zRank($key, $member);
    }
    public function zRem($key, $member, ... $other_members)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zRem', array('key' => $key, 'member' => $member, 'other_members' => $other_members), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zRem($key, $member, ...$other_members);
    }
    public function zRemRangeByLex($key, $min, $max)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zRemRangeByLex', array('key' => $key, 'min' => $min, 'max' => $max), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zRemRangeByLex($key, $min, $max);
    }
    public function zRemRangeByRank($key, $start, $end)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zRemRangeByRank', array('key' => $key, 'start' => $start, 'end' => $end), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zRemRangeByRank($key, $start, $end);
    }
    public function zRemRangeByScore($key, $start, $end)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zRemRangeByScore', array('key' => $key, 'start' => $start, 'end' => $end), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zRemRangeByScore($key, $start, $end);
    }
    public function zRevRange($key, $start, $end, $scores = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zRevRange', array('key' => $key, 'start' => $start, 'end' => $end, 'scores' => $scores), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zRevRange($key, $start, $end, $scores);
    }
    public function zRevRangeByLex($key, $max, $min, $offset = null, $count = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zRevRangeByLex', array('key' => $key, 'max' => $max, 'min' => $min, 'offset' => $offset, 'count' => $count), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zRevRangeByLex($key, $max, $min, $offset, $count);
    }
    public function zRevRangeByScore($key, $max, $min, $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zRevRangeByScore', array('key' => $key, 'max' => $max, 'min' => $min, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zRevRangeByScore($key, $max, $min, $options);
    }
    public function zRevRank($key, $member)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zRevRank', array('key' => $key, 'member' => $member), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zRevRank($key, $member);
    }
    public function zScore($key, $member)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zScore', array('key' => $key, 'member' => $member), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zScore($key, $member);
    }
    public function zdiff($keys, $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zdiff', array('keys' => $keys, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zdiff($keys, $options);
    }
    public function zdiffstore($dst, $keys)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zdiffstore', array('dst' => $dst, 'keys' => $keys), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zdiffstore($dst, $keys);
    }
    public function zinter($keys, $weights = null, $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zinter', array('keys' => $keys, 'weights' => $weights, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zinter($keys, $weights, $options);
    }
    public function zintercard($keys, $limit = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zintercard', array('keys' => $keys, 'limit' => $limit), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zintercard($keys, $limit);
    }
    public function zinterstore($dst, $keys, $weights = null, $aggregate = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zinterstore', array('dst' => $dst, 'keys' => $keys, 'weights' => $weights, 'aggregate' => $aggregate), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zinterstore($dst, $keys, $weights, $aggregate);
    }
    public function zscan($key, &$iterator, $pattern = null, $count = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zscan', array('key' => $key, 'iterator' => $iterator, 'pattern' => $pattern, 'count' => $count), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zscan($key, $iterator, $pattern, $count);
    }
    public function zunion($keys, $weights = null, $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zunion', array('keys' => $keys, 'weights' => $weights, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zunion($keys, $weights, $options);
    }
    public function zunionstore($dst, $keys, $weights = null, $aggregate = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'zunionstore', array('dst' => $dst, 'keys' => $keys, 'weights' => $weights, 'aggregate' => $aggregate), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->zunionstore($dst, $keys, $weights, $aggregate);
    }
    public static function staticProxyConstructor($initializer)
    {
        static $reflection;
        $reflection = $reflection ?? new \ReflectionClass(__CLASS__);
        $instance   = $reflection->newInstanceWithoutConstructor();
        $instance->initializere3996 = $initializer;
        return $instance;
    }
    public function __construct($options = null)
    {
        static $reflection;
        if (! $this->valueHoldera1fd8) {
            $reflection = $reflection ?? new \ReflectionClass('Redis');
            $this->valueHoldera1fd8 = $reflection->newInstanceWithoutConstructor();
        }
        $this->valueHoldera1fd8->__construct($options);
    }
    public function & __get($name)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '__get', ['name' => $name], $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        if (isset(self::$publicProperties7ed0d[$name])) {
            return $this->valueHoldera1fd8->$name;
        }
        $realInstanceReflection = new \ReflectionClass('Redis');
        if (! $realInstanceReflection->hasProperty($name)) {
            $targetObject = $this->valueHoldera1fd8;
            $backtrace = debug_backtrace(false, 1);
            trigger_error(
                sprintf(
                    'Undefined property: %s::$%s in %s on line %s',
                    $realInstanceReflection->getName(),
                    $name,
                    $backtrace[0]['file'],
                    $backtrace[0]['line']
                ),
                \E_USER_NOTICE
            );
            return $targetObject->$name;
        }
        $targetObject = $this->valueHoldera1fd8;
        $accessor = function & () use ($targetObject, $name) {
            return $targetObject->$name;
        };
        $backtrace = debug_backtrace(true, 2);
        $scopeObject = isset($backtrace[1]['object']) ? $backtrace[1]['object'] : new \ProxyManager\Stub\EmptyClassStub();
        $accessor = $accessor->bindTo($scopeObject, get_class($scopeObject));
        $returnValue = & $accessor();
        return $returnValue;
    }
    public function __set($name, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '__set', array('name' => $name, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        $realInstanceReflection = new \ReflectionClass('Redis');
        if (! $realInstanceReflection->hasProperty($name)) {
            $targetObject = $this->valueHoldera1fd8;
            $targetObject->$name = $value;
            return $targetObject->$name;
        }
        $targetObject = $this->valueHoldera1fd8;
        $accessor = function & () use ($targetObject, $name, $value) {
            $targetObject->$name = $value;
            return $targetObject->$name;
        };
        $backtrace = debug_backtrace(true, 2);
        $scopeObject = isset($backtrace[1]['object']) ? $backtrace[1]['object'] : new \ProxyManager\Stub\EmptyClassStub();
        $accessor = $accessor->bindTo($scopeObject, get_class($scopeObject));
        $returnValue = & $accessor();
        return $returnValue;
    }
    public function __isset($name)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '__isset', array('name' => $name), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        $realInstanceReflection = new \ReflectionClass('Redis');
        if (! $realInstanceReflection->hasProperty($name)) {
            $targetObject = $this->valueHoldera1fd8;
            return isset($targetObject->$name);
        }
        $targetObject = $this->valueHoldera1fd8;
        $accessor = function () use ($targetObject, $name) {
            return isset($targetObject->$name);
        };
        $backtrace = debug_backtrace(true, 2);
        $scopeObject = isset($backtrace[1]['object']) ? $backtrace[1]['object'] : new \ProxyManager\Stub\EmptyClassStub();
        $accessor = $accessor->bindTo($scopeObject, get_class($scopeObject));
        $returnValue = $accessor();
        return $returnValue;
    }
    public function __unset($name)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '__unset', array('name' => $name), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        $realInstanceReflection = new \ReflectionClass('Redis');
        if (! $realInstanceReflection->hasProperty($name)) {
            $targetObject = $this->valueHoldera1fd8;
            unset($targetObject->$name);
            return;
        }
        $targetObject = $this->valueHoldera1fd8;
        $accessor = function () use ($targetObject, $name) {
            unset($targetObject->$name);
            return;
        };
        $backtrace = debug_backtrace(true, 2);
        $scopeObject = isset($backtrace[1]['object']) ? $backtrace[1]['object'] : new \ProxyManager\Stub\EmptyClassStub();
        $accessor = $accessor->bindTo($scopeObject, get_class($scopeObject));
        $accessor();
    }
    public function __clone()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '__clone', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        $this->valueHoldera1fd8 = clone $this->valueHoldera1fd8;
    }
    public function __sleep()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '__sleep', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return array('valueHoldera1fd8');
    }
    public function __wakeup()
    {
    }
    public function setProxyInitializer(\Closure $initializer = null) : void
    {
        $this->initializere3996 = $initializer;
    }
    public function getProxyInitializer() : ?\Closure
    {
        return $this->initializere3996;
    }
    public function initializeProxy() : bool
    {
        return $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'initializeProxy', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
    }
    public function isProxyInitialized() : bool
    {
        return null !== $this->valueHoldera1fd8;
    }
    public function getWrappedValueHolderValue()
    {
        return $this->valueHoldera1fd8;
    }
    public function __destruct()
    {
        $this->initializere3996 || $this->valueHoldera1fd8->__destruct();
    }
}
