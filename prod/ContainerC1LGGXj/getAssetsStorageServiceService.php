<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Storage\AssetsStorageService' shared service.

return $this->services['Wizacha\\Storage\\AssetsStorageService'] = ($this->privates['Wizacha\\Storage\\StorageFactory'] ?? $this->load('getStorageFactoryService.php'))->__invoke('assets', ['prefix' => 'assets', 'dir' => '/assets']);
