<?php

namespace ContainerC1LGGXj;

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\DependencyInjection\Container;
use Symfony\Component\DependencyInjection\Exception\InvalidArgumentException;
use Symfony\Component\DependencyInjection\Exception\LogicException;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;
use Symfony\Component\DependencyInjection\ParameterBag\FrozenParameterBag;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

/*
 * This class has been auto-generated
 * by the Symfony Dependency Injection Component.
 *
 * @final
 */
class appAppKernelProdContainer extends Container
{
    private $buildParameters;
    private $containerDir;
    private $targetDir;
    private $parameters = [];
    private $getService;

    public function __construct(array $buildParameters = [], $containerDir = __DIR__)
    {
        $this->getService = \Closure::fromCallable([$this, 'getService']);
        $this->buildParameters = $buildParameters;
        $this->containerDir = $containerDir;
        $this->targetDir = \dirname($containerDir);
        $this->parameters = $this->getDefaultParameters();

        $this->services = $this->privates = [];
        $this->syntheticIds = [
            'kernel' => true,
        ];
        $this->methodMap = [
            'Wizacha\\FeatureFlag\\FeatureFlagService' => 'getFeatureFlagServiceService',
            'Wizacha\\Marketplace\\Country\\CountryService' => 'getCountryServiceService',
            'Wizacha\\Marketplace\\Currency\\CurrencyService' => 'getCurrencyServiceService',
            'Wizacha\\Marketplace\\Seo\\SeoRepository' => 'getSeoRepositoryService',
            'Wizacha\\Marketplace\\Seo\\SeoService' => 'getSeoServiceService',
            'Wizacha\\Marketplace\\Subscription\\SubscriptionRepository' => 'getSubscriptionRepositoryService',
            'Wizacha\\Marketplace\\User\\UserRepository' => 'getUserRepositoryService',
            'Wizacha\\Marketplace\\User\\UserService' => 'getUserServiceService',
            'app.locale.detector' => 'getApp_Locale_DetectorService',
            'app.seo_slug_router' => 'getApp_SeoSlugRouterService',
            'doctrine' => 'getDoctrineService',
            'doctrine.dbal.default_connection' => 'getDoctrine_Dbal_DefaultConnectionService',
            'doctrine.orm.default_entity_manager' => 'getDoctrine_Orm_DefaultEntityManagerService',
            'event_dispatcher' => 'getEventDispatcherService',
            'http_kernel' => 'getHttpKernelService',
            'marketplace.ekey.repository' => 'getMarketplace_Ekey_RepositoryService',
            'marketplace.seo.slug_generator' => 'getMarketplace_Seo_SlugGeneratorService',
            'monolog.logger.psp' => 'getMonolog_Logger_PspService',
            'request_stack' => 'getRequestStackService',
            'router' => 'getRouterService',
            'router_listener.cs_cart' => 'getRouterListener_CsCartService',
            'security.token_storage' => 'getSecurity_TokenStorageService',
        ];
        $this->fileMap = [
            'Broadway\\UuidGenerator\\Rfc4122\\Version4Generator' => 'getVersion4GeneratorService.php',
            'HiPay\\Fullservice\\HTTP\\Configuration\\Configuration' => 'getConfigurationService.php',
            'MangoPay\\MangoPayApi' => 'getMangoPayApiService.php',
            'RulerZ\\Compiler\\Compiler' => 'getCompilerService.php',
            'RulerZ\\Parser\\Parser' => 'getParserService.php',
            'RulerZ\\Target\\DoctrineDBAL\\DoctrineDBAL' => 'getDoctrineDBALService.php',
            'RulerZ\\Target\\Native\\Native' => 'getNativeService.php',
            'Symfony\\Bundle\\FrameworkBundle\\Controller\\RedirectController' => 'getRedirectControllerService.php',
            'Symfony\\Bundle\\FrameworkBundle\\Controller\\TemplateController' => 'getTemplateControllerService.php',
            'Symfony\\Component\\Translation\\Dumper\\XliffFileDumper' => 'getXliffFileDumperService.php',
            'Symfony\\Component\\Translation\\Loader\\XliffFileLoader' => 'getXliffFileLoaderService.php',
            'Tygh\\Backend\\Cache\\File' => 'getFileService.php',
            'Wizacha\\ActionLogger\\EventSubscribers\\DispatchFundsFailedSubscriber' => 'getDispatchFundsFailedSubscriberService.php',
            'Wizacha\\ActionLogger\\EventSubscribers\\OrderSubscriber' => 'getOrderSubscriberService.php',
            'Wizacha\\ActionLogger\\EventSubscribers\\ProductSubscriber' => 'getProductSubscriberService.php',
            'Wizacha\\ActionLogger\\EventSubscribers\\ShippingSubscriber' => 'getShippingSubscriberService.php',
            'Wizacha\\ActionLogger\\EventSubscribers\\TransactionSubscriber' => 'getTransactionSubscriberService.php',
            'Wizacha\\ActionLogger\\EventSubscribers\\UserEventSubscriber' => 'getUserEventSubscriberService.php',
            'Wizacha\\AppBundle\\Command\\ImportAutomatedFeedsCommand' => 'getImportAutomatedFeedsCommandService.php',
            'Wizacha\\AppBundle\\Command\\OrderCancelRollbackCommand' => 'getOrderCancelRollbackCommandService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\AuthLogController' => 'getAuthLogControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\BasketController' => 'getBasketControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\Catalog\\CompanyController' => 'getCompanyControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\Catalog\\ExportController' => 'getExportControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\Catalog\\ProductController' => 'getProductControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\CommissionController' => 'getCommissionControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\CompanyController' => 'getCompanyController2Service.php',
            'Wizacha\\AppBundle\\Controller\\Api\\CompanyPerson\\CompanyPersonController' => 'getCompanyPersonControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\ContactController' => 'getContactControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\CreditCardController' => 'getCreditCardControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\CreditNoteController' => 'getCreditNoteControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\CurrencyController' => 'getCurrencyControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\DiscussionController' => 'getDiscussionControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\Division\\DivisionController' => 'getDivisionControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\GroupController' => 'getGroupControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\ImageController' => 'getImageControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\ImportController' => 'getImportControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\JobController' => 'getJobControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\LanguageController' => 'getLanguageControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\MultiVendorProductController' => 'getMultiVendorProductControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\OrderAdjustmentController' => 'getOrderAdjustmentControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\OrderController' => 'getOrderControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\OrderDetailsController' => 'getOrderDetailsControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\OrderTransactionsController' => 'getOrderTransactionsControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\Order\\HandDeliveryController' => 'getHandDeliveryControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\Order\\MondialRelayController' => 'getMondialRelayControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\Order\\OrderActionController' => 'getOrderActionControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\Order\\OrderAttachmentController' => 'getOrderAttachmentControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\Organisation\\OrganisationBasketController' => 'getOrganisationBasketControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\Organisation\\OrganisationOrderController' => 'getOrganisationOrderControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\PaymentController' => 'getPaymentControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\Payment\\DirectDebitPaymentController' => 'getDirectDebitPaymentControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\ProductController' => 'getProductController2Service.php',
            'Wizacha\\AppBundle\\Controller\\Api\\Promotion\\BasketPromotionsController' => 'getBasketPromotionsControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\Promotion\\CatalogPromotionsController' => 'getCatalogPromotionsControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\Promotion\\MarketplacePromotionsController' => 'getMarketplacePromotionsControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\Quotation\\QuoteRequestSelectionController' => 'getQuoteRequestSelectionControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\RefundController' => 'getRefundControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\RelatedProduct\\RelatedProductController' => 'getRelatedProductControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\SeoController' => 'getSeoControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\Shipments' => 'getShipmentsService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\ShipmentsController' => 'getShipmentsControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\StripeCallbackController' => 'getStripeCallbackControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\SubscriptionController' => 'getSubscriptionControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\TransactionTransferController' => 'getTransactionTransferControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\TranslationController' => 'getTranslationControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Api\\UserController' => 'getUserControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Backend\\AuthController' => 'getAuthControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Backend\\CommissionController' => 'getCommissionController2Service.php',
            'Wizacha\\AppBundle\\Controller\\Backend\\CurrencyController' => 'getCurrencyController2Service.php',
            'Wizacha\\AppBundle\\Controller\\Backend\\DiscussController' => 'getDiscussControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Backend\\FinancialFlowsHistoryController' => 'getFinancialFlowsHistoryControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Backend\\InvoicingSettingsController' => 'getInvoicingSettingsControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Backend\\MangopayController' => 'getMangopayControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Backend\\MultiVendorProductController' => 'getMultiVendorProductController2Service.php',
            'Wizacha\\AppBundle\\Controller\\Backend\\NotificationController' => 'getNotificationControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Backend\\NotificationsController' => 'getNotificationsControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Backend\\SandboxDiskUsageController' => 'getSandboxDiskUsageControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Backend\\TaxController' => 'getTaxControllerService.php',
            'Wizacha\\AppBundle\\Controller\\Backend\\UserGroupController' => 'getUserGroupControllerService.php',
            'Wizacha\\AppBundle\\Controller\\CardController' => 'getCardControllerService.php',
            'Wizacha\\AppBundle\\Controller\\CheckoutController' => 'getCheckoutControllerService.php',
            'Wizacha\\AppBundle\\Controller\\DocumentationController' => 'getDocumentationControllerService.php',
            'Wizacha\\AppBundle\\Controller\\PaymentNotificationController' => 'getPaymentNotificationControllerService.php',
            'Wizacha\\AppBundle\\Controller\\PaymentNotification\\HipaySepaNotificationController' => 'getHipaySepaNotificationControllerService.php',
            'Wizacha\\AppBundle\\Controller\\PaymentNotification\\LemonWaySepaNotificationController' => 'getLemonWaySepaNotificationControllerService.php',
            'Wizacha\\AppBundle\\Controller\\PaymentNotification\\SepaMandateSignatureNotificationController' => 'getSepaMandateSignatureNotificationControllerService.php',
            'Wizacha\\AppBundle\\Controller\\ProxyController' => 'getProxyControllerService.php',
            'Wizacha\\AppBundle\\Notification\\ModerationNotifier' => 'getModerationNotifierService.php',
            'Wizacha\\AppBundle\\Notification\\NotificationConfigRepository' => 'getNotificationConfigRepositoryService.php',
            'Wizacha\\AppBundle\\Notification\\NotificationConfigService' => 'getNotificationConfigServiceService.php',
            'Wizacha\\AppBundle\\Notification\\OrderNotifier' => 'getOrderNotifierService.php',
            'Wizacha\\AppBundle\\Notification\\UserNotifier' => 'getUserNotifierService.php',
            'Wizacha\\AppBundle\\Security\\User\\UserService' => 'getUserService2Service.php',
            'Wizacha\\AppBundle\\Service\\TranslationService' => 'getTranslationServiceService.php',
            'Wizacha\\Async\\AmqpQueue' => 'getAmqpQueueService.php',
            'Wizacha\\Async\\Debouncer\\DebouncerEventSubscriber' => 'getDebouncerEventSubscriberService.php',
            'Wizacha\\Async\\Debouncer\\DebouncerService' => 'getDebouncerServiceService.php',
            'Wizacha\\Async\\Dispatcher' => 'getDispatcherService.php',
            'Wizacha\\Async\\QueueServiceFactory' => 'getQueueServiceFactoryService.php',
            'Wizacha\\Async\\SqsQueue' => 'getSqsQueueService.php',
            'Wizacha\\Bridge\\Broadway\\DatabaseRepository' => 'getDatabaseRepositoryService.php',
            'Wizacha\\CloudImageManager' => 'getCloudImageManagerService.php',
            'Wizacha\\Component\\AuthLog\\AuthLogRepository' => 'getAuthLogRepositoryService.php',
            'Wizacha\\Component\\AuthLog\\AuthLogService' => 'getAuthLogServiceService.php',
            'Wizacha\\Component\\Geocoding\\Geocoding' => 'getGeocodingService.php',
            'Wizacha\\Component\\Http\\Client\\GuzzleYavinClient' => 'getGuzzleYavinClientService.php',
            'Wizacha\\Component\\Http\\Factory\\YavinResponseFactory' => 'getYavinResponseFactoryService.php',
            'Wizacha\\Component\\Http\\OptionMapper' => 'getOptionMapperService.php',
            'Wizacha\\Component\\Import\\ImportFactory' => 'getImportFactoryService.php',
            'Wizacha\\Component\\Import\\ImportService' => 'getImportServiceService.php',
            'Wizacha\\Core\\Concurrent\\MutexService' => 'getMutexServiceService.php',
            'Wizacha\\Exim\\Import\\TranslationImporter' => 'getTranslationImporterService.php',
            'Wizacha\\ImageManager' => 'getImageManagerService.php',
            'Wizacha\\Marketplace\\AdminCompany' => 'getAdminCompanyService.php',
            'Wizacha\\Marketplace\\Basket\\BasketService' => 'getBasketServiceService.php',
            'Wizacha\\Marketplace\\Basket\\Checkout' => 'getCheckoutService.php',
            'Wizacha\\Marketplace\\Catalog\\AttributeService' => 'getAttributeServiceService.php',
            'Wizacha\\Marketplace\\Catalog\\Category\\CategoryService' => 'getCategoryServiceService.php',
            'Wizacha\\Marketplace\\Catalog\\Product\\ProductService' => 'getProductServiceService.php',
            'Wizacha\\Marketplace\\Commission\\CommissionService' => 'getCommissionServiceService.php',
            'Wizacha\\Marketplace\\CompanyPerson\\CompanyPersonService' => 'getCompanyPersonServiceService.php',
            'Wizacha\\Marketplace\\Company\\CompanyRepository' => 'getCompanyRepositoryService.php',
            'Wizacha\\Marketplace\\Company\\CompanyService' => 'getCompanyServiceService.php',
            'Wizacha\\Marketplace\\Company\\LegacyCompanyCache' => 'getLegacyCompanyCacheService.php',
            'Wizacha\\Marketplace\\CreditCard\\Repository\\CreditCardRepository' => 'getCreditCardRepositoryService.php',
            'Wizacha\\Marketplace\\CreditCard\\Service\\CreditCardService' => 'getCreditCardServiceService.php',
            'Wizacha\\Marketplace\\Division\\Service\\DivisionService' => 'getDivisionServiceService.php',
            'Wizacha\\Marketplace\\Division\\Service\\DivisionSettingsService' => 'getDivisionSettingsServiceService.php',
            'Wizacha\\Marketplace\\Favorite\\FavoriteService' => 'getFavoriteServiceService.php',
            'Wizacha\\Marketplace\\FinancialFlowsHistory\\FinancialFlowsHistoryService' => 'getFinancialFlowsHistoryServiceService.php',
            'Wizacha\\Marketplace\\Group\\UserGroupService' => 'getUserGroupServiceService.php',
            'Wizacha\\Marketplace\\Language\\LanguageRepository' => 'getLanguageRepositoryService.php',
            'Wizacha\\Marketplace\\Messenger\\BroadcastPublisher' => 'getBroadcastPublisherService.php',
            'Wizacha\\Marketplace\\Messenger\\JsonEnvelopeSerializer' => 'getJsonEnvelopeSerializerService.php',
            'Wizacha\\Marketplace\\Messenger\\JsonMessageSerializer' => 'getJsonMessageSerializerService.php',
            'Wizacha\\Marketplace\\Metrics\\SandboxMetrics' => 'getSandboxMetricsService.php',
            'Wizacha\\Marketplace\\Order\\Action\\Accept' => 'getAcceptService.php',
            'Wizacha\\Marketplace\\Order\\Action\\Cancel' => 'getCancelService.php',
            'Wizacha\\Marketplace\\Order\\Action\\CommitTo' => 'getCommitToService.php',
            'Wizacha\\Marketplace\\Order\\Action\\Confirm' => 'getConfirmService.php',
            'Wizacha\\Marketplace\\Order\\Action\\DeclareInvoiceNumberGeneratedElsewhere' => 'getDeclareInvoiceNumberGeneratedElsewhereService.php',
            'Wizacha\\Marketplace\\Order\\Action\\DeclareParcelLost' => 'getDeclareParcelLostService.php',
            'Wizacha\\Marketplace\\Order\\Action\\DispatchFunds' => 'getDispatchFundsService.php',
            'Wizacha\\Marketplace\\Order\\Action\\DispatchFundsFailed' => 'getDispatchFundsFailedService.php',
            'Wizacha\\Marketplace\\Order\\Action\\DispatchFundsSucceeded' => 'getDispatchFundsSucceededService.php',
            'Wizacha\\Marketplace\\Order\\Action\\EndWithdrawalPeriod' => 'getEndWithdrawalPeriodService.php',
            'Wizacha\\Marketplace\\Order\\Action\\MarkAsDelivered' => 'getMarkAsDeliveredService.php',
            'Wizacha\\Marketplace\\Order\\Action\\MarkAsFinished' => 'getMarkAsFinishedService.php',
            'Wizacha\\Marketplace\\Order\\Action\\MarkAsPaid' => 'getMarkAsPaidService.php',
            'Wizacha\\Marketplace\\Order\\Action\\MarkAsShipped' => 'getMarkAsShippedService.php',
            'Wizacha\\Marketplace\\Order\\Action\\MarkPaymentAsRefused' => 'getMarkPaymentAsRefusedService.php',
            'Wizacha\\Marketplace\\Order\\Action\\MarkPaymentAuthorizationAsCaptured' => 'getMarkPaymentAuthorizationAsCapturedService.php',
            'Wizacha\\Marketplace\\Order\\Action\\MarkPaymentAuthorizationAsRefused' => 'getMarkPaymentAuthorizationAsRefusedService.php',
            'Wizacha\\Marketplace\\Order\\Action\\MarkPaymentDefermentAsAuthorized' => 'getMarkPaymentDefermentAsAuthorizedService.php',
            'Wizacha\\Marketplace\\Order\\Action\\MarkPaymentDefermentAsRefused' => 'getMarkPaymentDefermentAsRefusedService.php',
            'Wizacha\\Marketplace\\Order\\Action\\ProvideInvoiceNumber' => 'getProvideInvoiceNumberService.php',
            'Wizacha\\Marketplace\\Order\\Action\\RedirectToPaymentProcessor' => 'getRedirectToPaymentProcessorService.php',
            'Wizacha\\Marketplace\\Order\\Action\\Refuse' => 'getRefuseService.php',
            'Wizacha\\Marketplace\\Order\\Action\\Trash' => 'getTrashService.php',
            'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Service\\OrderAmountsCalculator' => 'getOrderAmountsCalculatorService.php',
            'Wizacha\\Marketplace\\Order\\CreateOrder' => 'getCreateOrderService.php',
            'Wizacha\\Marketplace\\Order\\Discount\\OrderDiscountsCalculator' => 'getOrderDiscountsCalculatorService.php',
            'Wizacha\\Marketplace\\Order\\OrderAction\\Event\\OrderStatusUpdatedEventSubscriber' => 'getOrderStatusUpdatedEventSubscriberService.php',
            'Wizacha\\Marketplace\\Order\\OrderAction\\Repository\\OrderActionRepository' => 'getOrderActionRepositoryService.php',
            'Wizacha\\Marketplace\\Order\\OrderAction\\Service\\OrderActionService' => 'getOrderActionServiceService.php',
            'Wizacha\\Marketplace\\Order\\OrderAttachment\\Service\\OrderAttachmentService' => 'getOrderAttachmentServiceService.php',
            'Wizacha\\Marketplace\\Order\\OrderMutator' => 'getOrderMutatorService.php',
            'Wizacha\\Marketplace\\Order\\OrderService' => 'getOrderServiceService.php',
            'Wizacha\\Marketplace\\Order\\Token\\TokenService' => 'getTokenServiceService.php',
            'Wizacha\\Marketplace\\Order\\Workflow\\WorkflowService' => 'getWorkflowServiceService.php',
            'Wizacha\\Marketplace\\PIM\\MultiVendorProduct\\MultiVendorProductService' => 'getMultiVendorProductServiceService.php',
            'Wizacha\\Marketplace\\PIM\\Option\\OptionRepository' => 'getOptionRepositoryService.php',
            'Wizacha\\Marketplace\\PIM\\Option\\OptionService' => 'getOptionServiceService.php',
            'Wizacha\\Marketplace\\PIM\\Option\\SystemOptionsRegistry' => 'getSystemOptionsRegistryService.php',
            'Wizacha\\Marketplace\\PIM\\Product\\ProductRepository' => 'getProductRepositoryService.php',
            'Wizacha\\Marketplace\\PIM\\Product\\ProductService' => 'getProductService2Service.php',
            'Wizacha\\Marketplace\\PIM\\Product\\Template\\TemplateService' => 'getTemplateServiceService.php',
            'Wizacha\\Marketplace\\PIM\\Stock\\StockService' => 'getStockServiceService.php',
            'Wizacha\\Marketplace\\PIM\\Tax\\TaxService' => 'getTaxServiceService.php',
            'Wizacha\\Marketplace\\PIM\\TransactionMode\\TransactionModeService' => 'getTransactionModeServiceService.php',
            'Wizacha\\Marketplace\\Payment\\HiPay\\Wallet\\HiPayWalletApi' => 'getHiPayWalletApiService.php',
            'Wizacha\\Marketplace\\Payment\\LemonWay\\LemonWayApi' => 'getLemonWayApiService.php',
            'Wizacha\\Marketplace\\Payment\\LemonWay\\LemonWayConfig' => 'getLemonWayConfigService.php',
            'Wizacha\\Marketplace\\Payment\\LemonWay\\LemonWayHttpClient' => 'getLemonWayHttpClientService.php',
            'Wizacha\\Marketplace\\Payment\\LemonWay\\LemonWaySepaDefermentProcessor' => 'getLemonWaySepaDefermentProcessorService.php',
            'Wizacha\\Marketplace\\Payment\\LemonWay\\LemonWaySepaDirectProcessor' => 'getLemonWaySepaDirectProcessorService.php',
            'Wizacha\\Marketplace\\Payment\\LemonWay\\LemonWayTransactionSyncService' => 'getLemonWayTransactionSyncServiceService.php',
            'Wizacha\\Marketplace\\Payment\\MandateService' => 'getMandateServiceService.php',
            'Wizacha\\Marketplace\\Payment\\Mangopay\\MangoPayApiConfig' => 'getMangoPayApiConfigService.php',
            'Wizacha\\Marketplace\\Payment\\Processor\\HiPay' => 'getHiPayService.php',
            'Wizacha\\Marketplace\\Payment\\Processor\\LemonWay' => 'getLemonWayService.php',
            'Wizacha\\Marketplace\\Payment\\Processor\\MangoPay' => 'getMangoPayService.php',
            'Wizacha\\Marketplace\\Payment\\Processor\\NoPayment' => 'getNoPaymentService.php',
            'Wizacha\\Marketplace\\Payment\\Processor\\PayoutService' => 'getPayoutServiceService.php',
            'Wizacha\\Marketplace\\Payment\\Processor\\SMoney' => 'getSMoneyService.php',
            'Wizacha\\Marketplace\\Payment\\Processor\\Stripe' => 'getStripeService.php',
            'Wizacha\\Marketplace\\Payment\\SMoney\\SMoneyApi' => 'getSMoneyApiService.php',
            'Wizacha\\Marketplace\\Payment\\Stripe\\StripeApi' => 'getStripeApiService.php',
            'Wizacha\\Marketplace\\Payment\\Stripe\\StripeConfig' => 'getStripeConfigService.php',
            'Wizacha\\Marketplace\\PriceTier\\Repository\\PriceTierRepository' => 'getPriceTierRepositoryService.php',
            'Wizacha\\Marketplace\\PriceTier\\Service\\PriceTierService' => 'getPriceTierServiceService.php',
            'Wizacha\\Marketplace\\ProductOptionInventory\\Repository\\ProductOptionInventoryRepository' => 'getProductOptionInventoryRepositoryService.php',
            'Wizacha\\Marketplace\\Promotion\\ProductPromotionApplier' => 'getProductPromotionApplierService.php',
            'Wizacha\\Marketplace\\Promotion\\PromotionService' => 'getPromotionServiceService.php',
            'Wizacha\\Marketplace\\Quotation\\QuoteRequestService' => 'getQuoteRequestServiceService.php',
            'Wizacha\\Marketplace\\Quotation\\Repository\\QuoteRequestSelectionDeclinationRepository' => 'getQuoteRequestSelectionDeclinationRepositoryService.php',
            'Wizacha\\Marketplace\\Quotation\\Repository\\QuoteRequestSelectionRepository' => 'getQuoteRequestSelectionRepositoryService.php',
            'Wizacha\\Marketplace\\ReadModel\\ProductProjector' => 'getProductProjectorService.php',
            'Wizacha\\Marketplace\\ReadModel\\ProductRepository' => 'getProductRepository2Service.php',
            'Wizacha\\Marketplace\\ReadModel\\ReadModelService' => 'getReadModelServiceService.php',
            'Wizacha\\Marketplace\\RelatedProduct\\ConfigService' => 'getConfigServiceService.php',
            'Wizacha\\Marketplace\\RelatedProduct\\RelatedProductRepository' => 'getRelatedProductRepositoryService.php',
            'Wizacha\\Marketplace\\RelatedProduct\\RelatedProductService' => 'getRelatedProductServiceService.php',
            'Wizacha\\Marketplace\\RgpdAnonymizer\\RgpdAnonymizerService' => 'getRgpdAnonymizerServiceService.php',
            'Wizacha\\Marketplace\\Shipping\\ShippingService' => 'getShippingServiceService.php',
            'Wizacha\\Marketplace\\Subscription\\Log\\SubscriptionActionTraceRepository' => 'getSubscriptionActionTraceRepositoryService.php',
            'Wizacha\\Marketplace\\Subscription\\Log\\SubscriptionActionTraceService' => 'getSubscriptionActionTraceServiceService.php',
            'Wizacha\\Marketplace\\Subscription\\SubscriptionService' => 'getSubscriptionServiceService.php',
            'Wizacha\\Marketplace\\Transaction\\TransactionService' => 'getTransactionServiceService.php',
            'Wizacha\\Marketplace\\User\\UserPasswordHistoryService' => 'getUserPasswordHistoryServiceService.php',
            'Wizacha\\Marketplace\\User\\UserSecurity' => 'getUserSecurityService.php',
            'Wizacha\\Prediggo\\Exporter' => 'getExporterService.php',
            'Wizacha\\Premoderation\\ProductModeration\\ProductModerationInProgressService' => 'getProductModerationInProgressServiceService.php',
            'Wizacha\\Search\\Engine\\AlgoliaService' => 'getAlgoliaServiceService.php',
            'Wizacha\\Search\\Engine\\AlgoliaSyncService' => 'getAlgoliaSyncServiceService.php',
            'Wizacha\\Search\\Record\\AlgoliaProductRecordFactory' => 'getAlgoliaProductRecordFactoryService.php',
            'Wizacha\\Sentinel\\AlertManager' => 'getAlertManagerService.php',
            'Wizacha\\Storage\\AssetsStorageService' => 'getAssetsStorageServiceService.php',
            'Wizacha\\Storage\\CatalogExportStorageService' => 'getCatalogExportStorageServiceService.php',
            'Wizacha\\Storage\\CsvExportStorageService' => 'getCsvExportStorageServiceService.php',
            'Wizacha\\Storage\\CsvStorageService' => 'getCsvStorageServiceService.php',
            'Wizacha\\Storage\\CustomFilesStorageService' => 'getCustomFilesStorageServiceService.php',
            'Wizacha\\Storage\\DownloadsStorageService' => 'getDownloadsStorageServiceService.php',
            'Wizacha\\Storage\\ElfinderStorageService' => 'getElfinderStorageServiceService.php',
            'Wizacha\\Storage\\HadesStorageService' => 'getHadesStorageServiceService.php',
            'Wizacha\\Storage\\ImagesStorageService' => 'getImagesStorageServiceService.php',
            'Wizacha\\Storage\\MessageAttachmentsStorageService' => 'getMessageAttachmentsStorageServiceService.php',
            'Wizacha\\Storage\\OrderAttachmentsStorageService' => 'getOrderAttachmentsStorageServiceService.php',
            'Wizacha\\Storage\\OrganisationStorageService' => 'getOrganisationStorageServiceService.php',
            'Wizacha\\Storage\\PrediggoStorageService' => 'getPrediggoStorageServiceService.php',
            'Wizacha\\Storage\\ProductAttachmentsStorageService' => 'getProductAttachmentsStorageServiceService.php',
            'Wizacha\\Storage\\SitemapStorageService' => 'getSitemapStorageServiceService.php',
            'Wizacha\\Storage\\StaticsStorageService' => 'getStaticsStorageServiceService.php',
            'Wizacha\\Storage\\VendorSubscriptionStorageService' => 'getVendorSubscriptionStorageServiceService.php',
            'app.authenticator_throttling' => 'getApp_AuthenticatorThrottlingService.php',
            'app.authenticator_throttling.email' => 'getApp_AuthenticatorThrottling_EmailService.php',
            'app.authenticator_throttling.email.api' => 'getApp_AuthenticatorThrottling_Email_ApiService.php',
            'app.authenticator_throttling.ip' => 'getApp_AuthenticatorThrottling_IpService.php',
            'app.backend_menu_badge_extension' => 'getApp_BackendMenuBadgeExtensionService.php',
            'app.base_backend_menu_extension' => 'getApp_BaseBackendMenuExtensionService.php',
            'app.captcha' => 'getApp_CaptchaService.php',
            'app.configuration_service' => 'getApp_ConfigurationServiceService.php',
            'app.discuss_service' => 'getApp_DiscussServiceService.php',
            'app.google.oauth_client' => 'getApp_Google_OauthClientService.php',
            'app.google_auth_support_client' => 'getApp_GoogleAuthSupportClientService.php',
            'app.mailer' => 'getApp_MailerService.php',
            'app.mailer.debug' => 'getApp_Mailer_DebugService.php',
            'app.notification.company_notifier' => 'getApp_Notification_CompanyNotifierService.php',
            'app.notification.company_notifier.debug' => 'getApp_Notification_CompanyNotifier_DebugService.php',
            'app.notification.company_person_notifier' => 'getApp_Notification_CompanyPersonNotifierService.php',
            'app.notification.contact_notifier' => 'getApp_Notification_ContactNotifierService.php',
            'app.notification.contact_notifier.debug' => 'getApp_Notification_ContactNotifier_DebugService.php',
            'app.notification.currency_notifier' => 'getApp_Notification_CurrencyNotifierService.php',
            'app.notification.discuss_notifier' => 'getApp_Notification_DiscussNotifierService.php',
            'app.notification.discuss_notifier.debug' => 'getApp_Notification_DiscussNotifier_DebugService.php',
            'app.notification.export_notifier' => 'getApp_Notification_ExportNotifierService.php',
            'app.notification.export_notifier.debug' => 'getApp_Notification_ExportNotifier_DebugService.php',
            'app.notification.moderation_notifier.debug' => 'getApp_Notification_ModerationNotifier_DebugService.php',
            'app.notification.option_notifier' => 'getApp_Notification_OptionNotifierService.php',
            'app.notification.option_notifier.debug' => 'getApp_Notification_OptionNotifier_DebugService.php',
            'app.notification.order_notifier.debug' => 'getApp_Notification_OrderNotifier_DebugService.php',
            'app.notification.organisation_notifier' => 'getApp_Notification_OrganisationNotifierService.php',
            'app.notification.organisation_notifier.debug' => 'getApp_Notification_OrganisationNotifier_DebugService.php',
            'app.notification.product_notifier' => 'getApp_Notification_ProductNotifierService.php',
            'app.notification.product_notifier.debug' => 'getApp_Notification_ProductNotifier_DebugService.php',
            'app.notification.refund_notifier' => 'getApp_Notification_RefundNotifierService.php',
            'app.notification.refund_notifier.debug' => 'getApp_Notification_RefundNotifier_DebugService.php',
            'app.notification.rma_notifier' => 'getApp_Notification_RmaNotifierService.php',
            'app.notification.rma_notifier.debug' => 'getApp_Notification_RmaNotifier_DebugService.php',
            'app.notification.security_notifier' => 'getApp_Notification_SecurityNotifierService.php',
            'app.notification.test_notifier' => 'getApp_Notification_TestNotifierService.php',
            'app.notification.user_notifier.debug' => 'getApp_Notification_UserNotifier_DebugService.php',
            'app.notification_dispatcher' => 'getApp_NotificationDispatcherService.php',
            'app.notifier_parser' => 'getApp_NotifierParserService.php',
            'app.setting_storage' => 'getApp_SettingStorageService.php',
            'app.twig_extension' => 'getApp_TwigExtensionService.php',
            'app.validator.url_validator' => 'getApp_Validator_UrlValidatorService.php',
            'app.validator.video_validator' => 'getApp_Validator_VideoValidatorService.php',
            'assets.packages' => 'getAssets_PackagesService.php',
            'basic.configuration_service' => 'getBasic_ConfigurationServiceService.php',
            'broadway.command_handling.command_bus' => 'getBroadway_CommandHandling_CommandBusService.php',
            'broadway_serialization.reconstitute' => 'getBroadwaySerialization_ReconstituteService.php',
            'cache.app' => 'getCache_AppService.php',
            'cache.app_clearer' => 'getCache_AppClearerService.php',
            'cache.global_clearer' => 'getCache_GlobalClearerService.php',
            'cache.system' => 'getCache_SystemService.php',
            'cache.system_clearer' => 'getCache_SystemClearerService.php',
            'cache_clearer' => 'getCacheClearerService.php',
            'cache_warmer' => 'getCacheWarmerService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\AdminInstallCommand' => 'getAdminInstallCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\Algolia\\ClearAlgoliaIndexCommand' => 'getClearAlgoliaIndexCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\Algolia\\CreateAlgoliaIndexCommand' => 'getCreateAlgoliaIndexCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\Algolia\\PushAlgoliaConfigCommand' => 'getPushAlgoliaConfigCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\Algolia\\PushAlgoliaProductsCommand' => 'getPushAlgoliaProductsCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\Algolia\\UpdateAlgoliaProductsCommand' => 'getUpdateAlgoliaProductsCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\AssetsInstallCommand' => 'getAssetsInstallCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\AuthLogClearCommand' => 'getAuthLogClearCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\AuthLogPurgeCommand' => 'getAuthLogPurgeCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\BasketsDeleteAllCommand' => 'getBasketsDeleteAllCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\CancelEximJobsImport' => 'getCancelEximJobsImportService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\CatalogJsonExporterCommand' => 'getCatalogJsonExporterCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\CheckIncompleteOrdersCommand' => 'getCheckIncompleteOrdersCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\CheckMangopayUserIdCommand' => 'getCheckMangopayUserIdCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\CheckValidityMarketplaceDiscountCommand' => 'getCheckValidityMarketplaceDiscountCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\ClearEximJobsCommand' => 'getClearEximJobsCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\ClearPaymentDataBeforeProductionCommand' => 'getClearPaymentDataBeforeProductionCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\ClearTemporaryResourcesCommand' => 'getClearTemporaryResourcesCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\CreateAdminUserCommand' => 'getCreateAdminUserCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\CreateAmqpQueuesCommand' => 'getCreateAmqpQueuesCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\CreateCompanyWalletCommand' => 'getCreateCompanyWalletCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\CreateIndexAlgoliaCommand' => 'getCreateIndexAlgoliaCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\CreateSimpleUserCommand' => 'getCreateSimpleUserCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\CreateUserFromCSVCommand' => 'getCreateUserFromCSVCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\DebugRegistryCommand' => 'getDebugRegistryCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\DebugVariablesCommand' => 'getDebugVariablesCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeleteAllMultiVendorProductCommand' => 'getDeleteAllMultiVendorProductCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeleteAllOrdersCommand' => 'getDeleteAllOrdersCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeleteOrdersAmountsCommand' => 'getDeleteOrdersAmountsCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeleteOrdersCommand' => 'getDeleteOrdersCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeleteOrganisationsCommand' => 'getDeleteOrganisationsCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeployAlgoliaUpdateCommand' => 'getDeployAlgoliaUpdateCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeployDebugCommand' => 'getDeployDebugCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeployMigrateDivisionCommand' => 'getDeployMigrateDivisionCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeployMigrateOrderTransactionsCommand' => 'getDeployMigrateOrderTransactionsCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeployMigrateOrderTransactionsIsBackCommand' => 'getDeployMigrateOrderTransactionsIsBackCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeployMigrateRefunds' => 'getDeployMigrateRefundsService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeployPostActionsCommand' => 'getDeployPostActionsCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeployRemoveAssetsAwsCacheCommand' => 'getDeployRemoveAssetsAwsCacheCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeployRunCommand' => 'getDeployRunCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeploySetTransactionCompanyIdAndCurrencyCommand' => 'getDeploySetTransactionCompanyIdAndCurrencyCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeploySetUsersPasswordsHistoryCommand' => 'getDeploySetUsersPasswordsHistoryCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeploySystemOptions' => 'getDeploySystemOptionsService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\DispatchFundsCommand' => 'getDispatchFundsCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\Division\\ImportDivisionProductsCommand' => 'getImportDivisionProductsCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\DropMarketplaceDatabaseCommand' => 'getDropMarketplaceDatabaseCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\EndBlockedDispatchedOrdersCommand' => 'getEndBlockedDispatchedOrdersCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\GenerateSitemapCommand' => 'getGenerateSitemapCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\HipayCheckKycCommand' => 'getHipayCheckKycCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\ImportThemeTranslationsCommand' => 'getImportThemeTranslationsCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\Janus\\JanusExportCommand' => 'getJanusExportCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\Janus\\JanusImportCommand' => 'getJanusImportCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\LemonWayCheckKycCommand' => 'getLemonWayCheckKycCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\LemonwaySendKycCommand' => 'getLemonwaySendKycCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\ListAllFeaturesCommand' => 'getListAllFeaturesCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\ListAmqpQueuesCommand' => 'getListAmqpQueuesCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\ListEximJobsImport' => 'getListEximJobsImportService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\LoadFixtureCommand' => 'getLoadFixtureCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\MangoPayCheckKycCommand' => 'getMangoPayCheckKycCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\MangoPaySendKycCommand' => 'getMangoPaySendKycCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\MarkOrdersAsDeliveredCommand' => 'getMarkOrdersAsDeliveredCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\MiraklCreateCompanyAdministrators' => 'getMiraklCreateCompanyAdministratorsService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\MiraklOrderForceCompletedCommand' => 'getMiraklOrderForceCompletedCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\MiraklOrderForceFromProcessingToProcessedCommand' => 'getMiraklOrderForceFromProcessingToProcessedCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\Moderation\\PurgeProductInProgressCommand' => 'getPurgeProductInProgressCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\OrderCheckAcceptationDelayCommand' => 'getOrderCheckAcceptationDelayCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\OrderCompletedStatsCommand' => 'getOrderCompletedStatsCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\OrderCreationStatsCommand' => 'getOrderCreationStatsCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\OrderExecuteActionCommand' => 'getOrderExecuteActionCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\OrderListActionsCommand' => 'getOrderListActionsCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\PayPaymentDefermentOrdersCommand' => 'getPayPaymentDefermentOrdersCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\PayoutCompaniesCommand' => 'getPayoutCompaniesCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\ProductRefreshCacheCommand' => 'getProductRefreshCacheCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\ProductVisibilityCommand' => 'getProductVisibilityCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\PspCheckKycCommand' => 'getPspCheckKycCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\PspSendKycCommand' => 'getPspSendKycCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\PurgeResourcesCommand' => 'getPurgeResourcesCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\PushVendorInfoToPspCommand' => 'getPushVendorInfoToPspCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\Readmodel\\ClearReadmodelCommand' => 'getClearReadmodelCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\Readmodel\\CreateReadmodelCommand' => 'getCreateReadmodelCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\Readmodel\\DumpReadmodelCommand' => 'getDumpReadmodelCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\Readmodel\\UpdateReadmodelCommand' => 'getUpdateReadmodelCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\RecountProductInCategoriesCommand' => 'getRecountProductInCategoriesCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\RefreshNewPromotionsInSearchCommand' => 'getRefreshNewPromotionsInSearchCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\RenameProductAttachmentCommand' => 'getRenameProductAttachmentCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\RenewSubscriptionCommand' => 'getRenewSubscriptionCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\SMoneyCheckKycCommand' => 'getSMoneyCheckKycCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\SMoneySendKycCommand' => 'getSMoneySendKycCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\SeoRefreshNamesCommand' => 'getSeoRefreshNamesCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\StripeRemoveConsumedMandatesCommand' => 'getStripeRemoveConsumedMandatesCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\TranslationMissingKeysCommand' => 'getTranslationMissingKeysCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\TrashAbandonedOrdersCommand' => 'getTrashAbandonedOrdersCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\UpdateOrdersToCompletedCommand' => 'getUpdateOrdersToCompletedCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\UpdateTranslationFromCsvCommand' => 'getUpdateTranslationFromCsvCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\UpdateTranslationOrderStatusCommand' => 'getUpdateTranslationOrderStatusCommandService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\UserAccessToken' => 'getUserAccessTokenService.php',
            'console.command.public_alias.Wizacha\\AppBundle\\Command\\WorkflowToGraphvizCommand' => 'getWorkflowToGraphvizCommandService.php',
            'console.command.public_alias.doctrine_cache.contains_command' => 'getConsole_Command_PublicAlias_DoctrineCache_ContainsCommandService.php',
            'console.command.public_alias.doctrine_cache.delete_command' => 'getConsole_Command_PublicAlias_DoctrineCache_DeleteCommandService.php',
            'console.command.public_alias.doctrine_cache.flush_command' => 'getConsole_Command_PublicAlias_DoctrineCache_FlushCommandService.php',
            'console.command.public_alias.doctrine_cache.stats_command' => 'getConsole_Command_PublicAlias_DoctrineCache_StatsCommandService.php',
            'console.command_loader' => 'getConsole_CommandLoaderService.php',
            'container.env_var_processors_locator' => 'getContainer_EnvVarProcessorsLocatorService.php',
            'cscart.cache_backend' => 'getCscart_CacheBackendService.php',
            'cscart.crypt' => 'getCscart_CryptService.php',
            'cscart.product_manager' => 'getCscart_ProductManagerService.php',
            'error_controller' => 'getErrorControllerService.php',
            'event.subscriber.search' => 'getEvent_Subscriber_SearchService.php',
            'exercise_html_purifier.default.public' => 'getExerciseHtmlPurifier_Default_PublicService.php',
            'fallback_controller' => 'getFallbackControllerService.php',
            'filesystem' => 'getFilesystemService.php',
            'form.factory' => 'getForm_FactoryService.php',
            'form.type.file' => 'getForm_Type_FileService.php',
            'fos_js_routing.controller' => 'getFosJsRouting_ControllerService.php',
            'fos_js_routing.extractor' => 'getFosJsRouting_ExtractorService.php',
            'fos_js_routing.serializer' => 'getFosJsRouting_SerializerService.php',
            'fragment.handler' => 'getFragment_HandlerService.php',
            'guzzle.client' => 'getGuzzle_ClientService.php',
            'html_service' => 'getHtmlServiceService.php',
            'knp_snappy.pdf' => 'getKnpSnappy_PdfService.php',
            'less_compiler' => 'getLessCompilerService.php',
            'logger' => 'getLoggerService.php',
            'marketplace.accounting.payment' => 'getMarketplace_Accounting_PaymentService.php',
            'marketplace.accounting.service' => 'getMarketplace_Accounting_ServiceService.php',
            'marketplace.amqp.connection' => 'getMarketplace_Amqp_ConnectionService.php',
            'marketplace.api_productimporter' => 'getMarketplace_ApiProductimporterService.php',
            'marketplace.asset_manager' => 'getMarketplace_AssetManagerService.php',
            'marketplace.async_progress' => 'getMarketplace_AsyncProgressService.php',
            'marketplace.attribute.api.controller' => 'getMarketplace_Attribute_Api_ControllerService.php',
            'marketplace.aws.client' => 'getMarketplace_Aws_ClientService.php',
            'marketplace.aws.sqs.client' => 'getMarketplace_Aws_Sqs_ClientService.php',
            'marketplace.azure.client' => 'getMarketplace_Azure_ClientService.php',
            'marketplace.azure.sas_helper' => 'getMarketplace_Azure_SasHelperService.php',
            'marketplace.backend.price.formatter' => 'getMarketplace_Backend_Price_FormatterService.php',
            'marketplace.backend_productimporter' => 'getMarketplace_BackendProductimporterService.php',
            'marketplace.banner.banner_service' => 'getMarketplace_Banner_BannerServiceService.php',
            'marketplace.basket.command_handler' => 'getMarketplace_Basket_CommandHandlerService.php',
            'marketplace.basket.projector' => 'getMarketplace_Basket_ProjectorService.php',
            'marketplace.basket.repository.aggregate' => 'getMarketplace_Basket_Repository_AggregateService.php',
            'marketplace.cache' => 'getMarketplace_CacheService.php',
            'marketplace.catalog' => 'getMarketplace_CatalogService.php',
            'marketplace.catalog.company_service' => 'getMarketplace_Catalog_CompanyServiceService.php',
            'marketplace.catalog.exporter' => 'getMarketplace_Catalog_ExporterService.php',
            'marketplace.chronopost.client' => 'getMarketplace_Chronopost_ClientService.php',
            'marketplace.cms.menu_service' => 'getMarketplace_Cms_MenuServiceService.php',
            'marketplace.cms.page_service' => 'getMarketplace_Cms_PageServiceService.php',
            'marketplace.commission.commission_repository' => 'getMarketplace_Commission_CommissionRepositoryService.php',
            'marketplace.commission.order_listener' => 'getMarketplace_Commission_OrderListenerService.php',
            'marketplace.commission.transaction_listener' => 'getMarketplace_Commission_TransactionListenerService.php',
            'marketplace.debouncer.repository' => 'getMarketplace_Debouncer_RepositoryService.php',
            'marketplace.division.blacklists.service' => 'getMarketplace_Division_Blacklists_ServiceService.php',
            'marketplace.division.division_repository' => 'getMarketplace_Division_DivisionRepositoryService.php',
            'marketplace.division.products.service' => 'getMarketplace_Division_Products_ServiceService.php',
            'marketplace.dolist_template_repository' => 'getMarketplace_DolistTemplateRepositoryService.php',
            'marketplace.dolist_template_service' => 'getMarketplace_DolistTemplateServiceService.php',
            'marketplace.import.csv_uploader' => 'getMarketplace_Import_CsvUploaderService.php',
            'marketplace.import.eximjob_handler' => 'getMarketplace_Import_EximjobHandlerService.php',
            'marketplace.import.eximjoblog_handler' => 'getMarketplace_Import_EximjoblogHandlerService.php',
            'marketplace.import.job_repository' => 'getMarketplace_Import_JobRepositoryService.php',
            'marketplace.import.job_service' => 'getMarketplace_Import_JobServiceService.php',
            'marketplace.import.log_repository' => 'getMarketplace_Import_LogRepositoryService.php',
            'marketplace.import.logger' => 'getMarketplace_Import_LoggerService.php',
            'marketplace.international_tax.shipping' => 'getMarketplace_InternationalTax_ShippingService.php',
            'marketplace.international_tax.tax_repository' => 'getMarketplace_InternationalTax_TaxRepositoryService.php',
            'marketplace.invoicing_settings_service' => 'getMarketplace_InvoicingSettingsServiceService.php',
            'marketplace.jwt.parser' => 'getMarketplace_Jwt_ParserService.php',
            'marketplace.listeners.user' => 'getMarketplace_Listeners_UserService.php',
            'marketplace.mailing_list.mailing_list_service' => 'getMarketplace_MailingList_MailingListServiceService.php',
            'marketplace.marketplace_promotion.repository' => 'getMarketplace_MarketplacePromotion_RepositoryService.php',
            'marketplace.message_attachment.message_attachment_repository' => 'getMarketplace_MessageAttachment_MessageAttachmentRepositoryService.php',
            'marketplace.message_attachment.message_attachment_service' => 'getMarketplace_MessageAttachment_MessageAttachmentServiceService.php',
            'marketplace.moderation.api.controller' => 'getMarketplace_Moderation_Api_ControllerService.php',
            'marketplace.moderation.moderation_service' => 'getMarketplace_Moderation_ModerationServiceService.php',
            'marketplace.moderation.product_moderation_in_progress_repository' => 'getMarketplace_Moderation_ProductModerationInProgressRepositoryService.php',
            'marketplace.mondial_relay.client' => 'getMarketplace_MondialRelay_ClientService.php',
            'marketplace.monolog.level.mailer' => 'getMarketplace_Monolog_Level_MailerService.php',
            'marketplace.multi_vendor_product.linker' => 'getMarketplace_MultiVendorProduct_LinkerService.php',
            'marketplace.multi_vendor_product.linker.rule.ean' => 'getMarketplace_MultiVendorProduct_Linker_Rule_EanService.php',
            'marketplace.multi_vendor_product.linker.rule.locator' => 'getMarketplace_MultiVendorProduct_Linker_Rule_LocatorService.php',
            'marketplace.multi_vendor_product.linker.rule.supplier_reference' => 'getMarketplace_MultiVendorProduct_Linker_Rule_SupplierReferenceService.php',
            'marketplace.multi_vendor_product.product_synchronization.event_subscriber' => 'getMarketplace_MultiVendorProduct_ProductSynchronization_EventSubscriberService.php',
            'marketplace.multi_vendor_product.product_synchronization.rules_service' => 'getMarketplace_MultiVendorProduct_ProductSynchronization_RulesServiceService.php',
            'marketplace.multi_vendor_product_link.service' => 'getMarketplace_MultiVendorProductLink_ServiceService.php',
            'marketplace.oauth.admin_provider' => 'getMarketplace_Oauth_AdminProviderService.php',
            'marketplace.oauth.provider' => 'getMarketplace_Oauth_ProviderService.php',
            'marketplace.oauth.provider.google' => 'getMarketplace_Oauth_Provider_GoogleService.php',
            'marketplace.oauth.provider.okta' => 'getMarketplace_Oauth_Provider_OktaService.php',
            'marketplace.oauth.provider.okta.admin' => 'getMarketplace_Oauth_Provider_Okta_AdminService.php',
            'marketplace.oauth.provider.openid' => 'getMarketplace_Oauth_Provider_OpenidService.php',
            'marketplace.oauth.provider.openid.admin' => 'getMarketplace_Oauth_Provider_Openid_AdminService.php',
            'marketplace.oauth.provider.somfy' => 'getMarketplace_Oauth_Provider_SomfyService.php',
            'marketplace.order.adjustment_repository' => 'getMarketplace_Order_AdjustmentRepositoryService.php',
            'marketplace.order.adjustment_service' => 'getMarketplace_Order_AdjustmentServiceService.php',
            'marketplace.order.after_sales_service' => 'getMarketplace_Order_AfterSalesServiceService.php',
            'marketplace.order.billing_number_generator' => 'getMarketplace_Order_BillingNumberGeneratorService.php',
            'marketplace.order.credit_note.credit_note_helper' => 'getMarketplace_Order_CreditNote_CreditNoteHelperService.php',
            'marketplace.order.credit_note.credit_note_service' => 'getMarketplace_Order_CreditNote_CreditNoteServiceService.php',
            'marketplace.order.credit_note_reference_generator' => 'getMarketplace_Order_CreditNoteReferenceGeneratorService.php',
            'marketplace.order.order_attachment.repository' => 'getMarketplace_Order_OrderAttachment_RepositoryService.php',
            'marketplace.order.order_details_service' => 'getMarketplace_Order_OrderDetailsServiceService.php',
            'marketplace.order.refund.mp_discount_refund_checker' => 'getMarketplace_Order_Refund_MpDiscountRefundCheckerService.php',
            'marketplace.order.refund.refund_amounts_calculator' => 'getMarketplace_Order_Refund_RefundAmountsCalculatorService.php',
            'marketplace.order.refund.refund_checker' => 'getMarketplace_Order_Refund_RefundCheckerService.php',
            'marketplace.order.refund.refund_config' => 'getMarketplace_Order_Refund_RefundConfigService.php',
            'marketplace.order.refund.refund_event_subscriber' => 'getMarketplace_Order_Refund_RefundEventSubscriberService.php',
            'marketplace.order.refund.refund_notification_service' => 'getMarketplace_Order_Refund_RefundNotificationServiceService.php',
            'marketplace.order.refund.refund_payment' => 'getMarketplace_Order_Refund_RefundPaymentService.php',
            'marketplace.order.refund.refund_repository' => 'getMarketplace_Order_Refund_RefundRepositoryService.php',
            'marketplace.order.refund.refund_service' => 'getMarketplace_Order_Refund_RefundServiceService.php',
            'marketplace.order.refund.utils.creator' => 'getMarketplace_Order_Refund_Utils_CreatorService.php',
            'marketplace.order.refund.utils.executor' => 'getMarketplace_Order_Refund_Utils_ExecutorService.php',
            'marketplace.order.rma_number_generator' => 'getMarketplace_Order_RmaNumberGeneratorService.php',
            'marketplace.order.tracer' => 'getMarketplace_Order_TracerService.php',
            'marketplace.order_return.service' => 'getMarketplace_OrderReturn_ServiceService.php',
            'marketplace.organisation.basket_service' => 'getMarketplace_Organisation_BasketServiceService.php',
            'marketplace.organisation.order_service' => 'getMarketplace_Organisation_OrderServiceService.php',
            'marketplace.organisation.service' => 'getMarketplace_Organisation_ServiceService.php',
            'marketplace.organisation.user_group_service' => 'getMarketplace_Organisation_UserGroupServiceService.php',
            'marketplace.ovh.s3.client' => 'getMarketplace_Ovh_S3_ClientService.php',
            'marketplace.payment.hipay_api' => 'getMarketplace_Payment_HipayApiService.php',
            'marketplace.payment.hipay_cashout_client' => 'getMarketplace_Payment_HipayCashoutClientService.php',
            'marketplace.payment.hipay_client' => 'getMarketplace_Payment_HipayClientService.php',
            'marketplace.payment.hipay_event_subscriber' => 'getMarketplace_Payment_HipayEventSubscriberService.php',
            'marketplace.payment.lemonway_event_subscriber' => 'getMarketplace_Payment_LemonwayEventSubscriberService.php',
            'marketplace.payment.mangopay_event_subscriber' => 'getMarketplace_Payment_MangopayEventSubscriberService.php',
            'marketplace.payment.mangopay_http_client' => 'getMarketplace_Payment_MangopayHttpClientService.php',
            'marketplace.payment.offline_event_subscriber' => 'getMarketplace_Payment_OfflineEventSubscriberService.php',
            'marketplace.payment.payment_service' => 'getMarketplace_Payment_PaymentServiceService.php',
            'marketplace.payment.processor.hipay_card' => 'getMarketplace_Payment_Processor_HipayCardService.php',
            'marketplace.payment.processor.hipay_card_capture' => 'getMarketplace_Payment_Processor_HipayCardCaptureService.php',
            'marketplace.payment.processor.hipay_sepa_deferment' => 'getMarketplace_Payment_Processor_HipaySepaDefermentService.php',
            'marketplace.payment.processor.hipay_sepa_direct' => 'getMarketplace_Payment_Processor_HipaySepaDirectService.php',
            'marketplace.payment.processor.lemonway_card' => 'getMarketplace_Payment_Processor_LemonwayCardService.php',
            'marketplace.payment.processor.lemonway_transfer' => 'getMarketplace_Payment_Processor_LemonwayTransferService.php',
            'marketplace.payment.processor.mangopay' => 'getMarketplace_Payment_Processor_MangopayService.php',
            'marketplace.payment.processor.mangopay_bankwire' => 'getMarketplace_Payment_Processor_MangopayBankwireService.php',
            'marketplace.payment.processor.manualpayment' => 'getMarketplace_Payment_Processor_ManualpaymentService.php',
            'marketplace.payment.processor.nopayment' => 'getMarketplace_Payment_Processor_NopaymentService.php',
            'marketplace.payment.processor.smoney_card' => 'getMarketplace_Payment_Processor_SmoneyCardService.php',
            'marketplace.payment.processor.stripe_card' => 'getMarketplace_Payment_Processor_StripeCardService.php',
            'marketplace.payment.processor.stripe_sepa_deferment' => 'getMarketplace_Payment_Processor_StripeSepaDefermentService.php',
            'marketplace.payment.processor.stripe_sepa_direct' => 'getMarketplace_Payment_Processor_StripeSepaDirectService.php',
            'marketplace.payment.provider' => 'getMarketplace_Payment_ProviderService.php',
            'marketplace.payment.repository.user_mandate' => 'getMarketplace_Payment_Repository_UserMandateService.php',
            'marketplace.payment.repository.user_payment_info' => 'getMarketplace_Payment_Repository_UserPaymentInfoService.php',
            'marketplace.payment.smoney_event_subscriber' => 'getMarketplace_Payment_SmoneyEventSubscriberService.php',
            'marketplace.payment.stripe_controller' => 'getMarketplace_Payment_StripeControllerService.php',
            'marketplace.payment.stripe_event_subscriber' => 'getMarketplace_Payment_StripeEventSubscriberService.php',
            'marketplace.payment.user_payment_info_service' => 'getMarketplace_Payment_UserPaymentInfoServiceService.php',
            'marketplace.pim.attribute_service' => 'getMarketplace_Pim_AttributeServiceService.php',
            'marketplace.pim.category_service' => 'getMarketplace_Pim_CategoryServiceService.php',
            'marketplace.pim.product_creator' => 'getMarketplace_Pim_ProductCreatorService.php',
            'marketplace.pim.video_repository' => 'getMarketplace_Pim_VideoRepositoryService.php',
            'marketplace.pim.video_service' => 'getMarketplace_Pim_VideoServiceService.php',
            'marketplace.pim.video_storage' => 'getMarketplace_Pim_VideoStorageService.php',
            'marketplace.price.formatter' => 'getMarketplace_Price_FormatterService.php',
            'marketplace.price_tier.product_option_inventory_service' => 'getMarketplace_PriceTier_ProductOptionInventoryServiceService.php',
            'marketplace.product.csv_converter' => 'getMarketplace_Product_CsvConverterService.php',
            'marketplace.product.declination_factory' => 'getMarketplace_Product_DeclinationFactoryService.php',
            'marketplace.product.product_file_service' => 'getMarketplace_Product_ProductFileServiceService.php',
            'marketplace.product.product_visibility_report_service' => 'getMarketplace_Product_ProductVisibilityReportServiceService.php',
            'marketplace.product.xml_converter' => 'getMarketplace_Product_XmlConverterService.php',
            'marketplace.promotion.api.promotioncontroller' => 'getMarketplace_Promotion_Api_PromotioncontrollerService.php',
            'marketplace.promotion.basket_applier' => 'getMarketplace_Promotion_BasketApplierService.php',
            'marketplace.promotion.marketplace_promotion_validator' => 'getMarketplace_Promotion_MarketplacePromotionValidatorService.php',
            'marketplace.promotion.price_calendar_factory' => 'getMarketplace_Promotion_PriceCalendarFactoryService.php',
            'marketplace.promotion.promotion_subscriber' => 'getMarketplace_Promotion_PromotionSubscriberService.php',
            'marketplace.promotion.promotion_usage.repository' => 'getMarketplace_Promotion_PromotionUsage_RepositoryService.php',
            'marketplace.promotion.repository' => 'getMarketplace_Promotion_RepositoryService.php',
            'marketplace.promotion.rule_engine' => 'getMarketplace_Promotion_RuleEngineService.php',
            'marketplace.promotion.serializer' => 'getMarketplace_Promotion_SerializerService.php',
            'marketplace.queue_manager' => 'getMarketplace_QueueManagerService.php',
            'marketplace.queue_manager_factory' => 'getMarketplace_QueueManagerFactoryService.php',
            'marketplace.review.api.controller' => 'getMarketplace_Review_Api_ControllerService.php',
            'marketplace.review.company.service' => 'getMarketplace_Review_Company_ServiceService.php',
            'marketplace.review.product.service' => 'getMarketplace_Review_Product_ServiceService.php',
            'marketplace.search.category_index' => 'getMarketplace_Search_CategoryIndexService.php',
            'marketplace.search.product_index' => 'getMarketplace_Search_ProductIndexService.php',
            'marketplace.search_engine' => 'getMarketplace_SearchEngineService.php',
            'marketplace.storage.organisation.identity_card' => 'getMarketplace_Storage_Organisation_IdentityCardService.php',
            'marketplace.storage.organisation.proof_of_appointment' => 'getMarketplace_Storage_Organisation_ProofOfAppointmentService.php',
            'marketplace.subscription.subscription_subscriber' => 'getMarketplace_Subscription_SubscriptionSubscriberService.php',
            'marketplace.table_truncator' => 'getMarketplace_TableTruncatorService.php',
            'marketplace.transaction.transaction_repository' => 'getMarketplace_Transaction_TransactionRepositoryService.php',
            'marketplace.transction.transfers.failed' => 'getMarketplace_Transction_Transfers_FailedService.php',
            'marketplace.user.address.book_repository' => 'getMarketplace_User_Address_BookRepositoryService.php',
            'marketplace.user.address_service' => 'getMarketplace_User_AddressServiceService.php',
            'marketplace.user.domain_service' => 'getMarketplace_User_DomainServiceService.php',
            'message_bus' => 'getMessageBusService.php',
            'monolog.logger.audit' => 'getMonolog_Logger_AuditService.php',
            'monolog.logger.functional' => 'getMonolog_Logger_FunctionalService.php',
            'monolog.logger.sentinel' => 'getMonolog_Logger_SentinelService.php',
            'monolog.logger.worker' => 'getMonolog_Logger_WorkerService.php',
            'order_amount_repository' => 'getOrderAmountRepositoryService.php',
            'order_amounts_commission_repository' => 'getOrderAmountsCommissionRepositoryService.php',
            'pdf_generator' => 'getPdfGeneratorService.php',
            'prediggo.attribute_translation_exporter' => 'getPrediggo_AttributeTranslationExporterService.php',
            'prediggo.cms_exporter' => 'getPrediggo_CmsExporterService.php',
            'prediggo.hierarchy_exporter' => 'getPrediggo_HierarchyExporterService.php',
            'prediggo.order_exporter' => 'getPrediggo_OrderExporterService.php',
            'prediggo.product_exporter' => 'getPrediggo_ProductExporterService.php',
            'prediggo.user_exporter' => 'getPrediggo_UserExporterService.php',
            'purifier.default' => 'getPurifier_DefaultService.php',
            'routing.loader' => 'getRouting_LoaderService.php',
            'rulerz' => 'getRulerzService.php',
            'rulerz.evaluator.file' => 'getRulerz_Evaluator_FileService.php',
            'rulerz.parser' => 'getRulerz_ParserService.php',
            'security.authentication_utils' => 'getSecurity_AuthenticationUtilsService.php',
            'security.authorization_checker' => 'getSecurity_AuthorizationCheckerService.php',
            'security.csrf.token_manager' => 'getSecurity_Csrf_TokenManagerService.php',
            'security.encoder_factory.public' => 'getSecurity_EncoderFactory_PublicService.php',
            'security.password_encoder' => 'getSecurity_PasswordEncoderService.php',
            'serializer' => 'getSerializerService.php',
            'services_resetter' => 'getServicesResetterService.php',
            'session' => 'getSessionService.php',
            'snc_redis.public.cscart_sessions' => 'getSncRedis_Public_CscartSessionsService.php',
            'snc_redis.public.default' => 'getSncRedis_Public_DefaultService.php',
            'snc_redis.public.handlers' => 'getSncRedis_Public_HandlersService.php',
            'snc_redis.public.locks' => 'getSncRedis_Public_LocksService.php',
            'swiftmailer.mailer.fake_mailer' => 'getSwiftmailer_Mailer_FakeMailerService.php',
            'swiftmailer.mailer.fake_mailer.plugin.messagelogger' => 'getSwiftmailer_Mailer_FakeMailer_Plugin_MessageloggerService.php',
            'swiftmailer.mailer.fake_mailer.transport' => 'getSwiftmailer_Mailer_FakeMailer_TransportService.php',
            'swiftmailer.mailer.mailer' => 'getSwiftmailer_Mailer_MailerService.php',
            'swiftmailer.mailer.mailer.transport' => 'getSwiftmailer_Mailer_Mailer_TransportService.php',
            'templating' => 'getTemplatingService.php',
            'templating.loader' => 'getTemplating_LoaderService.php',
            'templating.mustache' => 'getTemplating_MustacheService.php',
            'templating.smarty' => 'getTemplating_SmartyService.php',
            'translation.importer' => 'getTranslation_ImporterService.php',
            'translation.public.reader' => 'getTranslation_Public_ReaderService.php',
            'translator' => 'getTranslatorService.php',
            'twig' => 'getTwigService.php',
            'twig.controller.exception' => 'getTwig_Controller_ExceptionService.php',
            'twig.controller.preview_error' => 'getTwig_Controller_PreviewErrorService.php',
            'validator' => 'getValidatorService.php',
            'wizacha.registry' => 'getWizacha_RegistryService.php',
            'worker.job_provider' => 'getWorker_JobProviderService.php',
            'worker.queue_list' => 'getWorker_QueueListService.php',
        ];
        $this->aliases = [
            'app.backend_menu_extension' => 'app.base_backend_menu_extension',
            'app.feature_flag_service' => 'Wizacha\\FeatureFlag\\FeatureFlagService',
            'app.notification.moderation_notifier' => 'Wizacha\\AppBundle\\Notification\\ModerationNotifier',
            'app.notification.order_notifier' => 'Wizacha\\AppBundle\\Notification\\OrderNotifier',
            'app.notification.user_notifier' => 'Wizacha\\AppBundle\\Notification\\UserNotifier',
            'app.order.amount.calculator' => 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Service\\OrderAmountsCalculator',
            'app.subscription_actions_traces_service' => 'Wizacha\\Marketplace\\Subscription\\Log\\SubscriptionActionTraceService',
            'app.translation' => 'Wizacha\\AppBundle\\Service\\TranslationService',
            'broadway.uuid.generator' => 'Broadway\\UuidGenerator\\Rfc4122\\Version4Generator',
            'database_connection' => 'doctrine.dbal.default_connection',
            'doctrine.orm.entity_manager' => 'doctrine.orm.default_entity_manager',
            'image.manager' => 'Wizacha\\ImageManager',
            'mailer' => 'swiftmailer.mailer.mailer',
            'marketplace.admin_company' => 'Wizacha\\Marketplace\\AdminCompany',
            'marketplace.async_dispatcher' => 'Wizacha\\Async\\Dispatcher',
            'marketplace.authlog.repository' => 'Wizacha\\Component\\AuthLog\\AuthLogRepository',
            'marketplace.authlog.service' => 'Wizacha\\Component\\AuthLog\\AuthLogService',
            'marketplace.basket.checkout' => 'Wizacha\\Marketplace\\Basket\\Checkout',
            'marketplace.basket.domain_service' => 'Wizacha\\Marketplace\\Basket\\BasketService',
            'marketplace.basket.repository.read_model' => 'Wizacha\\Bridge\\Broadway\\DatabaseRepository',
            'marketplace.catalog.attribute_service' => 'Wizacha\\Marketplace\\Catalog\\AttributeService',
            'marketplace.catalog.category_service' => 'Wizacha\\Marketplace\\Catalog\\Category\\CategoryService',
            'marketplace.commission.commission_service' => 'Wizacha\\Marketplace\\Commission\\CommissionService',
            'marketplace.company_service' => 'Wizacha\\Marketplace\\Company\\CompanyService',
            'marketplace.debouncer.service' => 'Wizacha\\Async\\Debouncer\\DebouncerService',
            'marketplace.division.service' => 'Wizacha\\Marketplace\\Division\\Service\\DivisionService',
            'marketplace.divisions_settings.service' => 'Wizacha\\Marketplace\\Division\\Service\\DivisionSettingsService',
            'marketplace.favorite.favorite_service' => 'Wizacha\\Marketplace\\Favorite\\FavoriteService',
            'marketplace.financial_flows_history_service' => 'Wizacha\\Marketplace\\FinancialFlowsHistory\\FinancialFlowsHistoryService',
            'marketplace.import.import_factory' => 'Wizacha\\Component\\Import\\ImportFactory',
            'marketplace.import.import_service' => 'Wizacha\\Component\\Import\\ImportService',
            'marketplace.import.translation_importer' => 'Wizacha\\Exim\\Import\\TranslationImporter',
            'marketplace.moderation.product_moderation_in_progress_service' => 'Wizacha\\Premoderation\\ProductModeration\\ProductModerationInProgressService',
            'marketplace.multi_vendor_product.api.controller' => 'Wizacha\\AppBundle\\Controller\\Api\\MultiVendorProductController',
            'marketplace.multi_vendor_product.service' => 'Wizacha\\Marketplace\\PIM\\MultiVendorProduct\\MultiVendorProductService',
            'marketplace.order.action.accept' => 'Wizacha\\Marketplace\\Order\\Action\\Accept',
            'marketplace.order.action.cancel' => 'Wizacha\\Marketplace\\Order\\Action\\Cancel',
            'marketplace.order.action.commit_to' => 'Wizacha\\Marketplace\\Order\\Action\\CommitTo',
            'marketplace.order.action.confirm' => 'Wizacha\\Marketplace\\Order\\Action\\Confirm',
            'marketplace.order.action.declare_invoice_number_generated_elsewhere' => 'Wizacha\\Marketplace\\Order\\Action\\DeclareInvoiceNumberGeneratedElsewhere',
            'marketplace.order.action.declare_parcel_lost' => 'Wizacha\\Marketplace\\Order\\Action\\DeclareParcelLost',
            'marketplace.order.action.dispatch_funds' => 'Wizacha\\Marketplace\\Order\\Action\\DispatchFunds',
            'marketplace.order.action.dispatch_funds_failed' => 'Wizacha\\Marketplace\\Order\\Action\\DispatchFundsFailed',
            'marketplace.order.action.dispatch_funds_succeeded' => 'Wizacha\\Marketplace\\Order\\Action\\DispatchFundsSucceeded',
            'marketplace.order.action.end_withdrawal_period' => 'Wizacha\\Marketplace\\Order\\Action\\EndWithdrawalPeriod',
            'marketplace.order.action.mark_as_delivered' => 'Wizacha\\Marketplace\\Order\\Action\\MarkAsDelivered',
            'marketplace.order.action.mark_as_finished' => 'Wizacha\\Marketplace\\Order\\Action\\MarkAsFinished',
            'marketplace.order.action.mark_as_paid' => 'Wizacha\\Marketplace\\Order\\Action\\MarkAsPaid',
            'marketplace.order.action.mark_as_shipped' => 'Wizacha\\Marketplace\\Order\\Action\\MarkAsShipped',
            'marketplace.order.action.mark_payment_as_refused' => 'Wizacha\\Marketplace\\Order\\Action\\MarkPaymentAsRefused',
            'marketplace.order.action.mark_payment_authorization_captured' => 'Wizacha\\Marketplace\\Order\\Action\\MarkPaymentAuthorizationAsCaptured',
            'marketplace.order.action.mark_payment_authorization_refused' => 'Wizacha\\Marketplace\\Order\\Action\\MarkPaymentAuthorizationAsRefused',
            'marketplace.order.action.mark_payment_deferment_as_authorized' => 'Wizacha\\Marketplace\\Order\\Action\\MarkPaymentDefermentAsAuthorized',
            'marketplace.order.action.mark_payment_deferment_as_refused' => 'Wizacha\\Marketplace\\Order\\Action\\MarkPaymentDefermentAsRefused',
            'marketplace.order.action.provide_invoice_number' => 'Wizacha\\Marketplace\\Order\\Action\\ProvideInvoiceNumber',
            'marketplace.order.action.redirect_to_payment_processor' => 'Wizacha\\Marketplace\\Order\\Action\\RedirectToPaymentProcessor',
            'marketplace.order.action.refuse' => 'Wizacha\\Marketplace\\Order\\Action\\Refuse',
            'marketplace.order.action.trash' => 'Wizacha\\Marketplace\\Order\\Action\\Trash',
            'marketplace.order.order_action.order_action_repository' => 'Wizacha\\Marketplace\\Order\\OrderAction\\Repository\\OrderActionRepository',
            'marketplace.order.order_action.order_action_service' => 'Wizacha\\Marketplace\\Order\\OrderAction\\Service\\OrderActionService',
            'marketplace.order.order_service' => 'Wizacha\\Marketplace\\Order\\OrderService',
            'marketplace.order.workflow_service' => 'Wizacha\\Marketplace\\Order\\Workflow\\WorkflowService',
            'marketplace.payment.hipay' => 'Wizacha\\Marketplace\\Payment\\Processor\\HiPay',
            'marketplace.payment.lemonway' => 'Wizacha\\Marketplace\\Payment\\Processor\\LemonWay',
            'marketplace.payment.mandate_service' => 'Wizacha\\Marketplace\\Payment\\MandateService',
            'marketplace.payment.mangopay' => 'Wizacha\\Marketplace\\Payment\\Processor\\MangoPay',
            'marketplace.payment.payout_service' => 'Wizacha\\Marketplace\\Payment\\Processor\\PayoutService',
            'marketplace.payment.processor.lemonway_sepa_deferment' => 'Wizacha\\Marketplace\\Payment\\LemonWay\\LemonWaySepaDefermentProcessor',
            'marketplace.payment.processor.lemonway_sepa_direct' => 'Wizacha\\Marketplace\\Payment\\LemonWay\\LemonWaySepaDirectProcessor',
            'marketplace.payment.smoney' => 'Wizacha\\Marketplace\\Payment\\Processor\\SMoney',
            'marketplace.payment.stripe' => 'Wizacha\\Marketplace\\Payment\\Processor\\Stripe',
            'marketplace.pim.option_service' => 'Wizacha\\Marketplace\\PIM\\Option\\OptionService',
            'marketplace.pim.product.service' => 'Wizacha\\Marketplace\\PIM\\Product\\ProductService',
            'marketplace.pim.tax_service' => 'Wizacha\\Marketplace\\PIM\\Tax\\TaxService',
            'marketplace.price_tier.price_tier_repository' => 'Wizacha\\Marketplace\\PriceTier\\Repository\\PriceTierRepository',
            'marketplace.price_tier.price_tier_service' => 'Wizacha\\Marketplace\\PriceTier\\Service\\PriceTierService',
            'marketplace.price_tier.product_option_inventory_repository' => 'Wizacha\\Marketplace\\ProductOptionInventory\\Repository\\ProductOptionInventoryRepository',
            'marketplace.product.api.productcontroller' => 'Wizacha\\AppBundle\\Controller\\Api\\ProductController',
            'marketplace.product.productservice' => 'Wizacha\\Marketplace\\Catalog\\Product\\ProductService',
            'marketplace.product.projector' => 'Wizacha\\Marketplace\\ReadModel\\ProductProjector',
            'marketplace.product.repository.read_model' => 'Wizacha\\Marketplace\\ReadModel\\ProductRepository',
            'marketplace.promotion.catalog_applier' => 'Wizacha\\Marketplace\\Promotion\\ProductPromotionApplier',
            'marketplace.promotion.promotionservice' => 'Wizacha\\Marketplace\\Promotion\\PromotionService',
            'marketplace.related_product.config_service' => 'Wizacha\\Marketplace\\RelatedProduct\\ConfigService',
            'marketplace.related_product.related_product_repository' => 'Wizacha\\Marketplace\\RelatedProduct\\RelatedProductRepository',
            'marketplace.related_product.related_product_service' => 'Wizacha\\Marketplace\\RelatedProduct\\RelatedProductService',
            'marketplace.search.product_record_factory' => 'Wizacha\\Search\\Record\\AlgoliaProductRecordFactory',
            'marketplace.seo.seo_repository' => 'Wizacha\\Marketplace\\Seo\\SeoRepository',
            'marketplace.seo.seo_service' => 'Wizacha\\Marketplace\\Seo\\SeoService',
            'marketplace.stock.domain_service' => 'Wizacha\\Marketplace\\PIM\\Stock\\StockService',
            'marketplace.transaction.transaction_service' => 'Wizacha\\Marketplace\\Transaction\\TransactionService',
            'marketplace.transaction_mode.service' => 'Wizacha\\Marketplace\\PIM\\TransactionMode\\TransactionModeService',
            'marketplace.user.api.usercontroller' => 'Wizacha\\AppBundle\\Controller\\Api\\UserController',
            'marketplace.user.user_repository' => 'Wizacha\\Marketplace\\User\\UserRepository',
            'marketplace.user.user_security' => 'Wizacha\\Marketplace\\User\\UserSecurity',
            'marketplace.user.user_service' => 'Wizacha\\Marketplace\\User\\UserService',
            'marketplace.user_group.service' => 'Wizacha\\Marketplace\\Group\\UserGroupService',
            'marketplace.user_service' => 'Wizacha\\AppBundle\\Security\\User\\UserService',
            'messenger.default_bus' => 'message_bus',
            'prediggo.exporter' => 'Wizacha\\Prediggo\\Exporter',
            'rulerz.evaluator' => 'rulerz.evaluator.file',
            'snc_redis.cscart_sessions' => 'snc_redis.public.cscart_sessions',
            'snc_redis.default' => 'snc_redis.public.default',
            'snc_redis.handlers' => 'snc_redis.public.handlers',
            'snc_redis.locks' => 'snc_redis.public.locks',
            'swiftmailer.transport' => 'swiftmailer.mailer.mailer.transport',
        ];
    }

    public function compile(): void
    {
        throw new LogicException('You cannot compile a dumped container that was already compiled.');
    }

    public function isCompiled(): bool
    {
        return true;
    }

    public function getRemovedIds(): array
    {
        return require $this->containerDir.\DIRECTORY_SEPARATOR.'removed-ids.php';
    }

    protected function load($file, $lazyLoad = true)
    {
        return require $this->containerDir.\DIRECTORY_SEPARATOR.$file;
    }

    protected function createProxy($class, \Closure $factory)
    {
        class_exists($class, false) || $this->load("{$class}.php");

        return $factory();
    }

    /*
     * Gets the public 'Wizacha\FeatureFlag\FeatureFlagService' shared autowired service.
     *
     * @return \Wizacha\FeatureFlag\FeatureFlagService
     */
    protected function getFeatureFlagServiceService()
    {
        return $this->services['Wizacha\\FeatureFlag\\FeatureFlagService'] = new \Wizacha\FeatureFlag\FeatureFlagService(($this->privates['parameter_bag'] ?? ($this->privates['parameter_bag'] = new \Symfony\Component\DependencyInjection\ParameterBag\ContainerBag($this))));
    }

    /*
     * Gets the public 'Wizacha\Marketplace\Country\CountryService' shared autowired service.
     *
     * @return \Wizacha\Marketplace\Country\CountryService
     */
    protected function getCountryServiceService()
    {
        return $this->services['Wizacha\\Marketplace\\Country\\CountryService'] = new \Wizacha\Marketplace\Country\CountryService(new \Wizacha\Marketplace\Country\CountryRepository(($this->services['doctrine'] ?? $this->getDoctrineService()), 'Wizacha\\Marketplace\\Country\\Country'), ($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()));
    }

    /*
     * Gets the public 'Wizacha\Marketplace\Currency\CurrencyService' shared autowired service.
     *
     * @return \Wizacha\Marketplace\Currency\CurrencyService
     */
    protected function getCurrencyServiceService()
    {
        $a = ($this->services['doctrine'] ?? $this->getDoctrineService());

        return $this->services['Wizacha\\Marketplace\\Currency\\CurrencyService'] = new \Wizacha\Marketplace\Currency\CurrencyService(new \Wizacha\Marketplace\Currency\CurrencyRepository($a, 'Wizacha\\Marketplace\\Currency\\Currency', new \Wizacha\Marketplace\Currency\QueryFilters\Factory(new RewindableGenerator(function () {
            yield 0 => ($this->privates['Wizacha\\Marketplace\\Currency\\QueryFilters\\CountryCode'] ?? ($this->privates['Wizacha\\Marketplace\\Currency\\QueryFilters\\CountryCode'] = new \Wizacha\Marketplace\Currency\QueryFilters\CountryCode()));
            yield 1 => ($this->privates['Wizacha\\Marketplace\\Currency\\QueryFilters\\Enabled'] ?? ($this->privates['Wizacha\\Marketplace\\Currency\\QueryFilters\\Enabled'] = new \Wizacha\Marketplace\Currency\QueryFilters\Enabled()));
        }, 2))), new \Wizacha\Marketplace\Currency\CurrencyCountriesRepository($a, 'Wizacha\\Marketplace\\Currency\\CurrencyCountries'), 'EUR', '^[a-zA-Z]{2}$', false, '€');
    }

    /*
     * Gets the public 'Wizacha\Marketplace\Seo\SeoRepository' shared service.
     *
     * @return \Wizacha\Marketplace\Seo\SeoRepository
     */
    protected function getSeoRepositoryService()
    {
        return $this->services['Wizacha\\Marketplace\\Seo\\SeoRepository'] = new \Wizacha\Marketplace\Seo\SeoRepository(($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()));
    }

    /*
     * Gets the public 'Wizacha\Marketplace\Seo\SeoService' shared autowired service.
     *
     * @return \Wizacha\Marketplace\Seo\SeoService
     */
    protected function getSeoServiceService()
    {
        return $this->services['Wizacha\\Marketplace\\Seo\\SeoService'] = new \Wizacha\Marketplace\Seo\SeoService(($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()), ($this->services['marketplace.seo.slug_generator'] ?? ($this->services['marketplace.seo.slug_generator'] = new \Wizacha\Marketplace\Seo\Slug\SlugGenerator())), ($this->services['router'] ?? $this->getRouterService()), ($this->services['Wizacha\\Marketplace\\Seo\\SeoRepository'] ?? $this->getSeoRepositoryService()), ($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()));
    }

    /*
     * Gets the public 'Wizacha\Marketplace\Subscription\SubscriptionRepository' shared service.
     *
     * @return \Wizacha\Marketplace\Subscription\SubscriptionRepository
     */
    protected function getSubscriptionRepositoryService()
    {
        return $this->services['Wizacha\\Marketplace\\Subscription\\SubscriptionRepository'] = new \Wizacha\Marketplace\Subscription\SubscriptionRepository(($this->services['doctrine'] ?? $this->getDoctrineService()), 'Wizacha\\Marketplace\\Subscription\\Subscription');
    }

    /*
     * Gets the public 'Wizacha\Marketplace\User\UserRepository' shared service.
     *
     * @return \Wizacha\Marketplace\User\UserRepository
     */
    protected function getUserRepositoryService()
    {
        return $this->services['Wizacha\\Marketplace\\User\\UserRepository'] = new \Wizacha\Marketplace\User\UserRepository(($this->services['doctrine.orm.default_entity_manager'] ?? $this->getDoctrine_Orm_DefaultEntityManagerService()));
    }

    /*
     * Gets the public 'Wizacha\Marketplace\User\UserService' shared service.
     *
     * @return \Wizacha\Marketplace\User\UserService
     */
    protected function getUserServiceService()
    {
        return $this->services['Wizacha\\Marketplace\\User\\UserService'] = new \Wizacha\Marketplace\User\UserService(($this->services['Wizacha\\Marketplace\\User\\UserRepository'] ?? $this->getUserRepositoryService()), ($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()), ($this->services['Wizacha\\Marketplace\\Currency\\CurrencyService'] ?? $this->getCurrencyServiceService()), ($this->services['Wizacha\\Marketplace\\Subscription\\SubscriptionRepository'] ?? $this->getSubscriptionRepositoryService()), new \Wizacha\Cscart\FnFunctions(), ($this->services['Wizacha\\Marketplace\\Country\\CountryService'] ?? $this->getCountryServiceService()), ($this->services['Wizacha\\FeatureFlag\\FeatureFlagService'] ?? $this->getFeatureFlagServiceService()), ($this->services['marketplace.ekey.repository'] ?? $this->getMarketplace_Ekey_RepositoryService()), new \Wizacha\Component\BytesGenerator\RandomBytesGenerator(), 0);
    }

    /*
     * Gets the public 'app.locale.detector' shared service.
     *
     * @return \Wizacha\AppBundle\Locale\Detector
     */
    protected function getApp_Locale_DetectorService()
    {
        $a = new \Wizacha\AppBundle\Locale\ApiOnlyDetector(new \Wizacha\AppBundle\Locale\ApiDetector(\Wizacha\AppBundle\Factory\Locale\LocaleInHeaderFactory::make(), '/api/v1/doc/schema.yaml', '/api/v1/doc/schema.json'));

        return $this->services['app.locale.detector'] = new \Wizacha\AppBundle\Locale\Detector(new \Wizacha\Component\Locale\LocaleDetectorDelegate($a, \Wizacha\AppBundle\Factory\Locale\AllowedLocalesDetectorFactory::make(new \Wizacha\Component\Locale\LocaleDetectorDelegate(new \Wizacha\Component\Locale\LocaleInQuery('sl'), \Wizacha\AppBundle\Factory\Locale\LocaleInSessionFactory::make(), new \Wizacha\Component\Locale\UserLocale()))), new \Wizacha\Component\Locale\LocaleDetectorDelegate($a, \Wizacha\AppBundle\Factory\Locale\AllowedLocalesDetectorFactory::make(new \Wizacha\Component\Locale\LocaleDetectorDelegate(new \Wizacha\Component\Locale\LocaleInQuery('descr_sl'), new \Wizacha\Component\Locale\LocaleInSession('descr_sl')))), 'fr', ($this->services['Wizacha\\Marketplace\\User\\UserService'] ?? $this->getUserServiceService()));
    }

    /*
     * Gets the public 'app.seo_slug_router' shared service.
     *
     * @return \Wizacha\Bridge\Symfony\SeoSlugRouter
     */
    protected function getApp_SeoSlugRouterService()
    {
        return $this->services['app.seo_slug_router'] = new \Wizacha\Bridge\Symfony\SeoSlugRouter(($this->services['marketplace.seo.slug_generator'] ?? ($this->services['marketplace.seo.slug_generator'] = new \Wizacha\Marketplace\Seo\Slug\SlugGenerator())), ($this->services['Wizacha\\Marketplace\\Seo\\SeoService'] ?? $this->getSeoServiceService()), '');
    }

    /*
     * Gets the public 'doctrine' shared service.
     *
     * @return \Doctrine\Bundle\DoctrineBundle\Registry
     */
    protected function getDoctrineService()
    {
        return $this->services['doctrine'] = new \Doctrine\Bundle\DoctrineBundle\Registry($this, $this->parameters['doctrine.connections'], $this->parameters['doctrine.entity_managers'], 'default', 'default');
    }

    /*
     * Gets the public 'doctrine.dbal.default_connection' shared service.
     *
     * @return \Doctrine\DBAL\Connection
     */
    protected function getDoctrine_Dbal_DefaultConnectionService()
    {
        $a = new \Doctrine\DBAL\Configuration();
        $a->setSchemaAssetsFilter(new \Doctrine\Bundle\DoctrineBundle\Dbal\SchemaAssetsFilterManager([0 => new \Doctrine\Bundle\DoctrineBundle\Dbal\RegexSchemaAssetFilter('/^(?=doctrine_).+|cscart_(users|products|product_descriptions|product_options_inventory|categories|category_descriptions|products_categories|companies|taxes|tax_descriptions|tax_rates|order_adjustment|countries|country_descriptions|user_profiles)$/')]));
        $b = new \Symfony\Bridge\Doctrine\ContainerAwareEventManager(new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($this->getService, [
            'doctrine.orm.default_listeners.attach_entity_listeners' => ['privates', 'doctrine.orm.default_listeners.attach_entity_listeners', 'getDoctrine_Orm_DefaultListeners_AttachEntityListenersService.php', true],
            'gedmo.listener.tree' => ['privates', 'gedmo.listener.tree', 'getGedmo_Listener_TreeService.php', true],
            'marketplace.doctrine_table_prefixer' => ['privates', 'marketplace.doctrine_table_prefixer', 'getMarketplace_DoctrineTablePrefixerService.php', true],
            'marketplace.user.address_book_event_listenner' => ['privates', 'marketplace.user.address_book_event_listenner', 'getMarketplace_User_AddressBookEventListennerService.php', true],
        ], [
            'doctrine.orm.default_listeners.attach_entity_listeners' => '?',
            'gedmo.listener.tree' => '?',
            'marketplace.doctrine_table_prefixer' => '?',
            'marketplace.user.address_book_event_listenner' => '?',
        ]), [0 => 'gedmo.listener.tree', 1 => 'marketplace.doctrine_table_prefixer']);
        $b->addEventListener([0 => 'prePersist'], 'marketplace.user.address_book_event_listenner');
        $b->addEventListener([0 => 'loadClassMetadata'], 'doctrine.orm.default_listeners.attach_entity_listeners');

        return $this->services['doctrine.dbal.default_connection'] = (new \Doctrine\Bundle\DoctrineBundle\ConnectionFactory($this->parameters['doctrine.dbal.connection_factory.types']))->createConnection(['host' => '************', 'port' => 3306, 'dbname' => 'marketplace_prod_0001', 'user' => 'marketplace_0001', 'password' => 'nixfH4oYnmqhn85mVSh8qPD589', 'charset' => 'utf8mb4', 'driver' => 'pdo_mysql', 'driverOptions' => [1002 => 'SET sql_mode = \'\'; SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;'], 'serverVersion' => 5.6, 'defaultTableOptions' => []], $a, $b, ['enum' => 'string']);
    }

    /*
     * Gets the public 'doctrine.orm.default_entity_manager' shared service.
     *
     * @return \Doctrine\ORM\EntityManager
     */
    protected function getDoctrine_Orm_DefaultEntityManagerService($lazyLoad = true)
    {
        if ($lazyLoad) {
            return $this->services['doctrine.orm.default_entity_manager'] = $this->createProxy('EntityManager_9a5be93', function () {
                return \EntityManager_9a5be93::staticProxyConstructor(function (&$wrappedInstance, \ProxyManager\Proxy\LazyLoadingInterface $proxy) {
                    $wrappedInstance = $this->getDoctrine_Orm_DefaultEntityManagerService(false);

                    $proxy->setProxyInitializer(null);

                    return true;
                });
            });
        }

        $a = new \Doctrine\ORM\Configuration();

        $b = new \Doctrine\Persistence\Mapping\Driver\MappingDriverChain();

        $c = new \Doctrine\ORM\Mapping\Driver\AnnotationDriver(($this->privates['annotations.cached_reader'] ?? $this->getAnnotations_CachedReaderService()), [0 => (\dirname(__DIR__, 4).'/vendor/gedmo/doctrine-extensions/lib/Gedmo/Tree/Entity'), 1 => (\dirname(__DIR__, 4).'/src/Marketplace/Order/AmountsCalculator/Entity'), 2 => (\dirname(__DIR__, 4).'/src/Marketplace/Tax'), 3 => (\dirname(__DIR__, 4).'/src/Marketplace/Quotation/Entity')]);
        $d = new \Doctrine\ORM\Mapping\Driver\SimplifiedYamlDriver([(\dirname(__DIR__, 4).'/src/Marketplace/Promotion/Mapping') => 'Wizacha\\Marketplace\\Promotion', (\dirname(__DIR__, 4).'/src/Marketplace/PIM/Video/Mapping') => 'Wizacha\\Marketplace\\PIM\\Video', (\dirname(__DIR__, 4).'/src/Marketplace/MailingList/Mapping') => 'Wizacha\\Marketplace\\MailingList', (\dirname(__DIR__, 4).'/src/Marketplace/Commission/Mapping') => 'Wizacha\\Marketplace\\Commission', (\dirname(__DIR__, 4).'/src/Marketplace/Payment/Mapping') => 'Wizacha\\Marketplace\\Payment', (\dirname(__DIR__, 4).'/src/Marketplace/PIM/MultiVendorProduct/Mapping') => 'Wizacha\\Marketplace\\PIM\\MultiVendorProduct', (\dirname(__DIR__, 4).'/src/Marketplace/PIM/MultiVendorProduct/ProductSynchronization/Mapping') => 'Wizacha\\Marketplace\\PIM\\MultiVendorProduct\\ProductSynchronization', (\dirname(__DIR__, 4).'/src/Marketplace/Organisation/Mapping') => 'Wizacha\\Marketplace\\Organisation', (\dirname(__DIR__, 4).'/src/Marketplace/Division/Mapping') => 'Wizacha\\Marketplace\\Division', (\dirname(__DIR__, 4).'/src/Component/Import/Mapping') => 'Wizacha\\Component\\Import', (\dirname(__DIR__, 4).'/src/Component/Dolist/Mapping') => 'Wizacha\\Component\\Dolist\\Entities', (\dirname(__DIR__, 4).'/src/Marketplace/PriceTier/Mapping') => 'Wizacha\\Marketplace\\PriceTier', (\dirname(__DIR__, 4).'/src/Marketplace/Currency/Mapping') => 'Wizacha\\Marketplace\\Currency', (\dirname(__DIR__, 4).'/src/Marketplace/Transaction/Mapping') => 'Wizacha\\Marketplace\\Transaction', (\dirname(__DIR__, 4).'/src/Marketplace/Order/Token/Mapping') => 'Wizacha\\Marketplace\\Order\\Token', (\dirname(__DIR__, 4).'/src/Component/AuthLog/Mapping') => 'Wizacha\\Component\\AuthLog', (\dirname(__DIR__, 4).'/src/Marketplace/CreditCard/Mapping') => 'Wizacha\\Marketplace\\CreditCard', (\dirname(__DIR__, 4).'/src/Marketplace/Subscription/Mapping') => 'Wizacha\\Marketplace\\Subscription', (\dirname(__DIR__, 4).'/src/Marketplace/Subscription/Log/Mapping') => 'Wizacha\\Marketplace\\Subscription\\Log', (\dirname(__DIR__, 4).'/src/Marketplace/Order/Refund/Mapping') => 'Wizacha\\Marketplace\\Order\\Refund\\Entity', (\dirname(__DIR__, 4).'/src/Premoderation/ProductModeration/Mapping') => 'Wizacha\\Premoderation\\ProductModeration', (\dirname(__DIR__, 4).'/src/Marketplace/Order/OrderAttachment/Mapping') => 'Wizacha\\Marketplace\\Order\\OrderAttachment\\Entity', (\dirname(__DIR__, 4).'/src/Marketplace/MessageAttachment/Mapping') => 'Wizacha\\Marketplace\\MessageAttachment\\Entity', (\dirname(__DIR__, 4).'/src/Marketplace/Order/OrderAction/Mapping') => 'Wizacha\\Marketplace\\Order\\OrderAction\\Entity', (\dirname(__DIR__, 4).'/src/Marketplace/CompanyPerson/Mapping') => 'Wizacha\\Marketplace\\CompanyPerson', (\dirname(__DIR__, 4).'/src/Marketplace/PSP/UboMangopay/Mapping') => 'Wizacha\\Marketplace\\PSP\\UboMangopay', (\dirname(__DIR__, 4).'/src/Marketplace/RelatedProduct/Mapping') => 'Wizacha\\Marketplace\\RelatedProduct', (\dirname(__DIR__, 4).'/src/AppBundle/Notification/Mapping') => 'Wizacha\\AppBundle\\Notification', (\dirname(__DIR__, 4).'/src/Marketplace/User/Mapping') => 'Wizacha\\Marketplace\\User', (\dirname(__DIR__, 4).'/src/Marketplace/PIM/Category/Mapping') => 'Wizacha\\Marketplace\\PIM\\Category', (\dirname(__DIR__, 4).'/src/Marketplace/PIM/Product/Mapping') => 'Wizacha\\Marketplace\\PIM\\Product', (\dirname(__DIR__, 4).'/src/Marketplace/PIM/Company/Mapping') => 'Wizacha\\Marketplace\\PIM\\Company', (\dirname(__DIR__, 4).'/src/Marketplace/PIM/Tax/Mapping') => 'Wizacha\\Marketplace\\PIM\\Tax', (\dirname(__DIR__, 4).'/src/Marketplace/ProductOptionInventory/Mapping') => 'Wizacha\\Marketplace\\ProductOptionInventory', (\dirname(__DIR__, 4).'/src/Marketplace/Order/Adjustment/Mapping') => 'Wizacha\\Marketplace\\Order\\Adjustment', (\dirname(__DIR__, 4).'/src/Async/Debouncer/Mapping') => 'Wizacha\\Async\\Debouncer', (\dirname(__DIR__, 4).'/src/Marketplace/Country/Mapping') => 'Wizacha\\Marketplace\\Country', (\dirname(__DIR__, 4).'/src/Marketplace/Group/Mapping') => 'Wizacha\\Marketplace\\Group', (\dirname(__DIR__, 4).'/src/Discuss/Entity/Mapping') => 'Wizacha\\Discuss\\Entity', (\dirname(__DIR__, 4).'/src/Discuss/Internal/Entity/Mapping') => 'Wizacha\\Discuss\\Internal\\Entity']);
        $d->setGlobalBasename('mapping');

        $b->addDriver($c, 'Gedmo\\Tree\\Entity');
        $b->addDriver($c, 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity');
        $b->addDriver($c, 'Wizacha\\Marketplace\\Tax');
        $b->addDriver($c, 'Wizacha\\Marketplace\\Quotation\\Entity');
        $b->addDriver($d, 'Wizacha\\Marketplace\\Promotion');
        $b->addDriver($d, 'Wizacha\\Marketplace\\PIM\\Video');
        $b->addDriver($d, 'Wizacha\\Marketplace\\MailingList');
        $b->addDriver($d, 'Wizacha\\Marketplace\\Commission');
        $b->addDriver($d, 'Wizacha\\Marketplace\\Payment');
        $b->addDriver($d, 'Wizacha\\Marketplace\\PIM\\MultiVendorProduct');
        $b->addDriver($d, 'Wizacha\\Marketplace\\PIM\\MultiVendorProduct\\ProductSynchronization');
        $b->addDriver($d, 'Wizacha\\Marketplace\\Organisation');
        $b->addDriver($d, 'Wizacha\\Marketplace\\Division');
        $b->addDriver($d, 'Wizacha\\Component\\Import');
        $b->addDriver($d, 'Wizacha\\Component\\Dolist\\Entities');
        $b->addDriver($d, 'Wizacha\\Marketplace\\PriceTier');
        $b->addDriver($d, 'Wizacha\\Marketplace\\Currency');
        $b->addDriver($d, 'Wizacha\\Marketplace\\Transaction');
        $b->addDriver($d, 'Wizacha\\Marketplace\\Order\\Token');
        $b->addDriver($d, 'Wizacha\\Component\\AuthLog');
        $b->addDriver($d, 'Wizacha\\Marketplace\\CreditCard');
        $b->addDriver($d, 'Wizacha\\Marketplace\\Subscription');
        $b->addDriver($d, 'Wizacha\\Marketplace\\Subscription\\Log');
        $b->addDriver($d, 'Wizacha\\Marketplace\\Order\\Refund\\Entity');
        $b->addDriver($d, 'Wizacha\\Premoderation\\ProductModeration');
        $b->addDriver($d, 'Wizacha\\Marketplace\\Order\\OrderAttachment\\Entity');
        $b->addDriver($d, 'Wizacha\\Marketplace\\MessageAttachment\\Entity');
        $b->addDriver($d, 'Wizacha\\Marketplace\\Order\\OrderAction\\Entity');
        $b->addDriver($d, 'Wizacha\\Marketplace\\CompanyPerson');
        $b->addDriver($d, 'Wizacha\\Marketplace\\PSP\\UboMangopay');
        $b->addDriver($d, 'Wizacha\\Marketplace\\RelatedProduct');
        $b->addDriver($d, 'Wizacha\\AppBundle\\Notification');
        $b->addDriver($d, 'Wizacha\\Marketplace\\User');
        $b->addDriver($d, 'Wizacha\\Marketplace\\PIM\\Category');
        $b->addDriver($d, 'Wizacha\\Marketplace\\PIM\\Product');
        $b->addDriver($d, 'Wizacha\\Marketplace\\PIM\\Company');
        $b->addDriver($d, 'Wizacha\\Marketplace\\PIM\\Tax');
        $b->addDriver($d, 'Wizacha\\Marketplace\\ProductOptionInventory');
        $b->addDriver($d, 'Wizacha\\Marketplace\\Order\\Adjustment');
        $b->addDriver($d, 'Wizacha\\Async\\Debouncer');
        $b->addDriver($d, 'Wizacha\\Marketplace\\Country');
        $b->addDriver($d, 'Wizacha\\Marketplace\\Group');
        $b->addDriver($d, 'Wizacha\\Discuss\\Entity');
        $b->addDriver($d, 'Wizacha\\Discuss\\Internal\\Entity');

        $a->setEntityNamespaces(['Gedmo' => 'Gedmo\\Tree\\Entity', 'Promotion' => 'Wizacha\\Marketplace\\Promotion', 'Video' => 'Wizacha\\Marketplace\\PIM\\Video', 'MailingList' => 'Wizacha\\Marketplace\\MailingList', 'Commission' => 'Wizacha\\Marketplace\\Commission', 'Payment' => 'Wizacha\\Marketplace\\Payment', 'MultiVendorProduct' => 'Wizacha\\Marketplace\\PIM\\MultiVendorProduct', 'MultiVendorProductSyncRules' => 'Wizacha\\Marketplace\\PIM\\MultiVendorProduct\\ProductSynchronization', 'Link' => 'Wizacha\\Marketplace\\PIM\\MultiVendorProduct', 'Organisation' => 'Wizacha\\Marketplace\\Organisation', 'Division' => 'Wizacha\\Marketplace\\Division', 'EximJob' => 'Wizacha\\Component\\Import', 'EximJobLog' => 'Wizacha\\Component\\Import', 'DolistTemplate' => 'Wizacha\\Component\\Dolist\\Entities', 'PriceTier' => 'Wizacha\\Marketplace\\PriceTier', 'Currency' => 'Wizacha\\Marketplace\\Currency', 'Transaction' => 'Wizacha\\Marketplace\\Transaction', 'Token' => 'Wizacha\\Marketplace\\Order\\Token', 'AuthLog' => 'Wizacha\\Component\\AuthLog', 'CreditCard' => 'Wizacha\\Marketplace\\CreditCard', 'Subscription' => 'Wizacha\\Marketplace\\Subscription\\Log', 'Refund' => 'Wizacha\\Marketplace\\Order\\Refund\\Entity', 'ProductModerationInProgress' => 'Wizacha\\Premoderation\\ProductModeration', 'OrderAttachment' => 'Wizacha\\Marketplace\\Order\\OrderAttachment\\Entity', 'OrderAmounts' => 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity', 'AmountItem' => 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity', 'ShippingAmounts' => 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity', 'OrderItemData' => 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity', 'MessageAttachment' => 'Wizacha\\Marketplace\\MessageAttachment\\Entity', 'OrderAction' => 'Wizacha\\Marketplace\\Order\\OrderAction\\Entity', 'UserMandate' => 'Wizacha\\Marketplace\\Payment', 'UserPaymentInfo' => 'Wizacha\\Marketplace\\Payment', 'CompanyPerson' => 'Wizacha\\Marketplace\\CompanyPerson', 'UboMangopay' => 'Wizacha\\Marketplace\\PSP\\UboMangopay', 'RelatedProduct' => 'Wizacha\\Marketplace\\RelatedProduct', 'ShippingTax' => 'Wizacha\\Marketplace\\Tax', 'NotificationConfig' => 'Wizacha\\AppBundle\\Notification', 'User' => 'Wizacha\\Marketplace\\User', 'Category' => 'Wizacha\\Marketplace\\PIM\\Category', 'CategoryDescription' => 'Wizacha\\Marketplace\\PIM\\Category', 'Product' => 'Wizacha\\Marketplace\\PIM\\Product', 'Company' => 'Wizacha\\Marketplace\\PIM\\Company', 'Tax' => 'Wizacha\\Marketplace\\PIM\\Tax', 'ProductOptionInventory' => 'Wizacha\\Marketplace\\ProductOptionInventory', 'TaxDescription' => 'Wizacha\\Marketplace\\PIM\\Tax', 'TaxRate' => 'Wizacha\\Marketplace\\PIM\\Tax', 'OrderAdjustment' => 'Wizacha\\Marketplace\\Order\\Adjustment', 'DebouncedJob' => 'Wizacha\\Async\\Debouncer', 'Country' => 'Wizacha\\Marketplace\\Country', 'CountryDescriptions' => 'Wizacha\\Marketplace\\Country', 'Group' => 'Wizacha\\Marketplace\\Group', 'QuoteRequestSelection' => 'Wizacha\\Marketplace\\Quotation\\Entity', 'QuoteRequestSelectionDeclination' => 'Wizacha\\Marketplace\\Quotation\\Entity', 'Discussion' => 'Wizacha\\Discuss\\Entity', 'DiscussionInternal' => 'Wizacha\\Discuss\\Internal\\Entity']);
        $a->setMetadataCacheImpl(new \Symfony\Component\Cache\DoctrineProvider(($this->privates['cache.doctrine.orm.default.metadata'] ?? ($this->privates['cache.doctrine.orm.default.metadata'] = new \Symfony\Component\Cache\Adapter\ArrayAdapter()))));
        $a->setQueryCacheImpl(new \Symfony\Component\Cache\DoctrineProvider(($this->privates['cache.doctrine.orm.default.query'] ?? ($this->privates['cache.doctrine.orm.default.query'] = new \Symfony\Component\Cache\Adapter\ArrayAdapter()))));
        $a->setResultCacheImpl(new \Symfony\Component\Cache\DoctrineProvider(($this->privates['cache.doctrine.orm.default.result'] ?? ($this->privates['cache.doctrine.orm.default.result'] = new \Symfony\Component\Cache\Adapter\ArrayAdapter()))));
        $a->setMetadataDriverImpl($b);
        $a->setProxyDir(($this->targetDir.''.'/doctrine/orm/Proxies'));
        $a->setProxyNamespace('Proxies');
        $a->setAutoGenerateProxyClasses(false);
        $a->setClassMetadataFactoryName('Doctrine\\ORM\\Mapping\\ClassMetadataFactory');
        $a->setDefaultRepositoryClassName('Doctrine\\ORM\\EntityRepository');
        $a->setNamingStrategy(new \Doctrine\ORM\Mapping\UnderscoreNamingStrategy());
        $a->setQuoteStrategy(new \Doctrine\ORM\Mapping\DefaultQuoteStrategy());
        $a->setEntityListenerResolver(new \Doctrine\Bundle\DoctrineBundle\Mapping\ContainerEntityListenerResolver($this));
        $a->setRepositoryFactory(new \Doctrine\Bundle\DoctrineBundle\Repository\ContainerRepositoryFactory(($this->privates['.service_locator.I3K77mT'] ?? ($this->privates['.service_locator.I3K77mT'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($this->getService, [], [])))));

        $instance = \Doctrine\ORM\EntityManager::create(($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()), $a);

        (new \Doctrine\Bundle\DoctrineBundle\ManagerConfigurator([], []))->configure($instance);

        return $instance;
    }

    /*
     * Gets the public 'event_dispatcher' shared service.
     *
     * @return \Symfony\Component\EventDispatcher\EventDispatcher
     */
    protected function getEventDispatcherService()
    {
        $this->services['event_dispatcher'] = $instance = new \Symfony\Component\EventDispatcher\EventDispatcher();

        $instance->addListener('kernel.controller', [0 => function () {
            return ($this->privates['app.feature_flag.listener'] ?? $this->getApp_FeatureFlag_ListenerService());
        }, 1 => 'onKernelController'], 0);
        $instance->addListener('console.command', [0 => function () {
            return ($this->privates['broadway.metadata_enricher.console'] ?? ($this->privates['broadway.metadata_enricher.console'] = new \Broadway\Bundle\BroadwayBundle\Command\CommandMetadataEnricher()));
        }, 1 => 'handleConsoleCommandEvent'], 0);
        $instance->addListener('kernel.request', [0 => function () {
            return ($this->privates['nelmio_cors.cors_listener'] ?? $this->getNelmioCors_CorsListenerService());
        }, 1 => 'onKernelRequest'], 250);
        $instance->addListener('security.authentication.success', [0 => function () {
            return ($this->privates['Wizacha\\AppBundle\\Security\\Listener\\UserAuthenticatedListener'] ?? $this->load('getUserAuthenticatedListenerService.php'));
        }, 1 => 'migrateLegacyPasswords'], 0);
        $instance->addListener('security.authentication.success', [0 => function () {
            return ($this->privates['Wizacha\\AppBundle\\Security\\Listener\\UserAuthenticatedListener'] ?? $this->load('getUserAuthenticatedListenerService.php'));
        }, 1 => 'onAuthenticationSuccess'], 0);
        $instance->addListener('security.authentication.success', [0 => function () {
            return ($this->privates['Wizacha\\AppBundle\\Security\\Listener\\UserAuthenticatedListener'] ?? $this->load('getUserAuthenticatedListenerService.php'));
        }, 1 => 'clearLoginAttempt'], 0);
        $instance->addListener('security.authentication.success', [0 => function () {
            return ($this->privates['Wizacha\\AppBundle\\Security\\Listener\\UserAuthenticatedListener'] ?? $this->load('getUserAuthenticatedListenerService.php'));
        }, 1 => 'resetLoginAttempts'], 0);
        $instance->addListener('security.authentication.failure', [0 => function () {
            return ($this->privates['Wizacha\\AppBundle\\Security\\Listener\\UserAuthenticatedListener'] ?? $this->load('getUserAuthenticatedListenerService.php'));
        }, 1 => 'onAuthenticationFailure'], 0);
        $instance->addListener('security.authentication.failure', [0 => function () {
            return ($this->privates['Wizacha\\AppBundle\\Security\\Listener\\UserAuthenticationFailureListener'] ?? $this->load('getUserAuthenticationFailureListenerService.php'));
        }, 1 => 'authenticationFailure'], 0);
        $instance->addListener('kernel.exception', [0 => function () {
            return ($this->privates['app.exception_subscriber'] ?? $this->load('getApp_ExceptionSubscriberService.php'));
        }, 1 => 'onKernelException'], 0);
        $instance->addListener('kernel.request', [0 => function () {
            return ($this->privates['app.json_decoder_request_subscriber'] ?? ($this->privates['app.json_decoder_request_subscriber'] = new \Wizacha\AppBundle\EventSubscriber\JsonDecoderRequestSubscriber()));
        }, 1 => 'onKernelRequest'], 10);
        $instance->addListener('message.new', [0 => function () {
            return ($this->services['app.discuss_service'] ?? $this->load('getApp_DiscussServiceService.php'));
        }, 1 => 'onNewMessage'], 0);
        $instance->addListener('kernel.request', [0 => function () {
            return ($this->services['app.seo_slug_router'] ?? $this->getApp_SeoSlugRouterService());
        }, 1 => 'routeFromSlug'], 100);
        $instance->addListener('kernel.request', [0 => function () {
            return ($this->services['router_listener.cs_cart'] ?? $this->getRouterListener_CsCartService());
        }, 1 => 'onKernelRequest'], 35);
        $instance->addListener('kernel.request', [0 => function () {
            return ($this->privates['app.permissions_subscriber'] ?? ($this->privates['app.permissions_subscriber'] = new \Wizacha\AppBundle\EventSubscriber\CheckPermissionsSubscriber()));
        }, 1 => 'checkPermissions'], 0);
        $instance->addListener('kernel.request', [0 => function () {
            return ($this->privates['Wizacha\\AppBundle\\EventSubscriber\\ApiApplicationFirewall'] ?? ($this->privates['Wizacha\\AppBundle\\EventSubscriber\\ApiApplicationFirewall'] = new \Wizacha\AppBundle\EventSubscriber\ApiApplicationFirewall(NULL)));
        }, 1 => 'checkApiAuthentication'], 0);
        $instance->addListener('kernel.request', [0 => function () {
            return ($this->privates['Wizacha\\AppBundle\\EventSubscriber\\PspRequestSubscriber'] ?? $this->getPspRequestSubscriberService());
        }, 1 => 'onRequestEvent'], 1);
        $instance->addListener('kernel.terminate', [0 => function () {
            return ($this->privates['Wizacha\\AppBundle\\EventSubscriber\\PspRequestSubscriber'] ?? $this->getPspRequestSubscriberService());
        }, 1 => 'checkAndLogRequest'], 1);
        $instance->addListener('kernel.request', [0 => function () {
            return ($this->privates['app.auth_redirect'] ?? ($this->privates['app.auth_redirect'] = new \Wizacha\AppBundle\EventSubscriber\AuthRedirectSubscriber()));
        }, 1 => 'authRedirect'], 0);
        $instance->addListener('product.create', [0 => function () {
            return ($this->services['event.subscriber.search'] ?? $this->load('getEvent_Subscriber_SearchService.php'));
        }, 1 => 'onProductUpdate'], 0);
        $instance->addListener('product.update', [0 => function () {
            return ($this->services['event.subscriber.search'] ?? $this->load('getEvent_Subscriber_SearchService.php'));
        }, 1 => 'onProductUpdate'], 0);
        $instance->addListener('product.delete', [0 => function () {
            return ($this->services['event.subscriber.search'] ?? $this->load('getEvent_Subscriber_SearchService.php'));
        }, 1 => 'onProductUpdate'], 0);
        $instance->addListener('relatedProduct.create', [0 => function () {
            return ($this->services['event.subscriber.search'] ?? $this->load('getEvent_Subscriber_SearchService.php'));
        }, 1 => 'onRelatedProductOffersUpdate'], 0);
        $instance->addListener('relatedProduct.delete', [0 => function () {
            return ($this->services['event.subscriber.search'] ?? $this->load('getEvent_Subscriber_SearchService.php'));
        }, 1 => 'onRelatedProductOffersUpdate'], 0);
        $instance->addListener('relatedProduct.update', [0 => function () {
            return ($this->services['event.subscriber.search'] ?? $this->load('getEvent_Subscriber_SearchService.php'));
        }, 1 => 'onRelatedProductOffersUpdate'], 0);
        $instance->addListener('multiVendorProduct_updated', [0 => function () {
            return ($this->services['event.subscriber.search'] ?? $this->load('getEvent_Subscriber_SearchService.php'));
        }, 1 => 'onMultiVendorProductUpdate'], 0);
        $instance->addListener('multiVendorProduct_deleted', [0 => function () {
            return ($this->services['event.subscriber.search'] ?? $this->load('getEvent_Subscriber_SearchService.php'));
        }, 1 => 'onMultiVendorProductDelete'], 0);
        $instance->addListener('link_created', [0 => function () {
            return ($this->services['event.subscriber.search'] ?? $this->load('getEvent_Subscriber_SearchService.php'));
        }, 1 => 'onMultiVendorProductLinkUpdate'], 0);
        $instance->addListener('link_updated', [0 => function () {
            return ($this->services['event.subscriber.search'] ?? $this->load('getEvent_Subscriber_SearchService.php'));
        }, 1 => 'onMultiVendorProductLinkUpdate'], 0);
        $instance->addListener('link_deleted', [0 => function () {
            return ($this->services['event.subscriber.search'] ?? $this->load('getEvent_Subscriber_SearchService.php'));
        }, 1 => 'onMultiVendorProductLinkDelete'], 0);
        $instance->addListener('company.updated', [0 => function () {
            return ($this->services['event.subscriber.search'] ?? $this->load('getEvent_Subscriber_SearchService.php'));
        }, 1 => 'onCompanyUpdate'], -100);
        $instance->addListener('attribute.update', [0 => function () {
            return ($this->services['event.subscriber.search'] ?? $this->load('getEvent_Subscriber_SearchService.php'));
        }, 1 => 'onFeatureUpdate'], 0);
        $instance->addListener('attribute.delete', [0 => function () {
            return ($this->services['event.subscriber.search'] ?? $this->load('getEvent_Subscriber_SearchService.php'));
        }, 1 => 'onFeatureUpdate'], 0);
        $instance->addListener('attribute.update_variant', [0 => function () {
            return ($this->services['event.subscriber.search'] ?? $this->load('getEvent_Subscriber_SearchService.php'));
        }, 1 => 'onFeatureVariantUpdate'], 0);
        $instance->addListener('category.update', [0 => function () {
            return ($this->services['event.subscriber.search'] ?? $this->load('getEvent_Subscriber_SearchService.php'));
        }, 1 => 'onCategoryUpdate'], 0);
        $instance->addListener('category.delete', [0 => function () {
            return ($this->services['event.subscriber.search'] ?? $this->load('getEvent_Subscriber_SearchService.php'));
        }, 1 => 'onCategoryUpdate'], 0);
        $instance->addListener('option.update', [0 => function () {
            return ($this->services['event.subscriber.search'] ?? $this->load('getEvent_Subscriber_SearchService.php'));
        }, 1 => 'onOptionUpdate'], 0);
        $instance->addListener('shipping_vendor.update', [0 => function () {
            return ($this->services['event.subscriber.search'] ?? $this->load('getEvent_Subscriber_SearchService.php'));
        }, 1 => 'onShippingVendorUpdate'], 0);
        $instance->addListener('product.create', [0 => function () {
            return ($this->privates['event.subscriber.premoderation'] ?? $this->load('getEvent_Subscriber_PremoderationService.php'));
        }, 1 => 'onProductUpdate'], 10000);
        $instance->addListener('product.update', [0 => function () {
            return ($this->privates['event.subscriber.premoderation'] ?? $this->load('getEvent_Subscriber_PremoderationService.php'));
        }, 1 => 'onProductUpdate'], 10000);
        $instance->addListener('kernel.request', [0 => function () {
            return ($this->privates['monolog.processor.http_request'] ?? ($this->privates['monolog.processor.http_request'] = new \Wizacha\Bridge\Monolog\Processor\HttpRequestProcessor($this->getEnv('REQUEST_ID_HEADER_NAME'))));
        }, 1 => 'onRequestEvent'], 100);
        $instance->addListener('console.command', [0 => function () {
            return ($this->privates['monolog.processor.console_command'] ?? ($this->privates['monolog.processor.console_command'] = new \Wizacha\Bridge\Monolog\Processor\ConsoleCommandProcessor()));
        }, 1 => 'onCommandCommandEvent'], 100);
        $instance->addListener('kernel.request', [0 => function () {
            return ($this->privates['monolog.logger.http_request'] ?? $this->getMonolog_Logger_HttpRequestService());
        }, 1 => 'onRequestEvent'], 1);
        $instance->addListener('kernel.terminate', [0 => function () {
            return ($this->privates['monolog.logger.http_request'] ?? $this->getMonolog_Logger_HttpRequestService());
        }, 1 => 'onTerminateEvent'], 1);
        $instance->addListener('console.command', [0 => function () {
            return ($this->privates['monolog.logger.console_command'] ?? $this->load('getMonolog_Logger_ConsoleCommandService.php'));
        }, 1 => 'onCommandCommandEvent'], 1);
        $instance->addListener('console.terminate', [0 => function () {
            return ($this->privates['monolog.logger.console_command'] ?? $this->load('getMonolog_Logger_ConsoleCommandService.php'));
        }, 1 => 'onConsoleTerminateEvent'], 1);
        $instance->addListener('order.updated', [0 => function () {
            return ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\MangoPay'] ?? $this->load('getMangoPayService.php'));
        }, 1 => 'onOrderUpdate'], 0);
        $instance->addListener('company.updated', [0 => function () {
            return ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\MangoPay'] ?? $this->load('getMangoPayService.php'));
        }, 1 => 'onCompanyUpdate'], 0);
        $instance->addListener('company.send_to_psp', [0 => function () {
            return ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\MangoPay'] ?? $this->load('getMangoPayService.php'));
        }, 1 => 'onCompanyUpdate'], 0);
        $instance->addListener('company.iban_bic.updated', [0 => function () {
            return ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\MangoPay'] ?? $this->load('getMangoPayService.php'));
        }, 1 => 'onCompanyIbanBicUpdated'], 0);
        $instance->addListener('company.approved', [0 => function () {
            return ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\MangoPay'] ?? $this->load('getMangoPayService.php'));
        }, 1 => 'sendKycs'], 0);
        $instance->addListener('company.legal_documents.updated', [0 => function () {
            return ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\MangoPay'] ?? $this->load('getMangoPayService.php'));
        }, 1 => 'onKycUpdated'], 0);
        $instance->addListener('ubo.submitted', [0 => function () {
            return ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\MangoPay'] ?? $this->load('getMangoPayService.php'));
        }, 1 => 'submitUBO'], 0);
        $instance->addListener('order.updated', [0 => function () {
            return ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\Stripe'] ?? $this->load('getStripeService.php'));
        }, 1 => 'onOrderUpdate'], 0);
        $instance->addListener('order.updated', [0 => function () {
            return ($this->services['marketplace.payment.hipay_event_subscriber'] ?? $this->load('getMarketplace_Payment_HipayEventSubscriberService.php'));
        }, 1 => 'onOrderUpdate'], 0);
        $instance->addListener('company.updated', [0 => function () {
            return ($this->services['marketplace.payment.hipay_event_subscriber'] ?? $this->load('getMarketplace_Payment_HipayEventSubscriberService.php'));
        }, 1 => 'onCompanyUpdate'], 0);
        $instance->addListener('company.send_to_psp', [0 => function () {
            return ($this->services['marketplace.payment.hipay_event_subscriber'] ?? $this->load('getMarketplace_Payment_HipayEventSubscriberService.php'));
        }, 1 => 'onCompanyUpdate'], 0);
        $instance->addListener('company.legal_documents.updated', [0 => function () {
            return ($this->services['marketplace.payment.hipay_event_subscriber'] ?? $this->load('getMarketplace_Payment_HipayEventSubscriberService.php'));
        }, 1 => 'onCompanyLegalDocumentsUpdated'], 0);
        $instance->addListener('company.iban_bic.updated', [0 => function () {
            return ($this->services['marketplace.payment.hipay_event_subscriber'] ?? $this->load('getMarketplace_Payment_HipayEventSubscriberService.php'));
        }, 1 => 'onCompanyIbanBicUpdated'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Payment\\Event\\HipayPaymentCallbackEvent', [0 => function () {
            return ($this->services['marketplace.payment.hipay_event_subscriber'] ?? $this->load('getMarketplace_Payment_HipayEventSubscriberService.php'));
        }, 1 => 'updateTransactionStatus'], 24);
        $instance->addListener('Wizacha\\Marketplace\\Payment\\Event\\HipayPaymentCallbackEvent', [0 => function () {
            return ($this->services['marketplace.payment.hipay_event_subscriber'] ?? $this->load('getMarketplace_Payment_HipayEventSubscriberService.php'));
        }, 1 => 'addCreditCardToSubscription'], 16);
        $instance->addListener('Wizacha\\Marketplace\\Payment\\Event\\HipayPaymentCallbackEvent', [0 => function () {
            return ($this->services['marketplace.payment.hipay_event_subscriber'] ?? $this->load('getMarketplace_Payment_HipayEventSubscriberService.php'));
        }, 1 => 'updateSubscriptionStatus'], 8);
        $instance->addListener('order.updated', [0 => function () {
            return ($this->services['marketplace.payment.lemonway_event_subscriber'] ?? $this->load('getMarketplace_Payment_LemonwayEventSubscriberService.php'));
        }, 1 => 'dispatchFunds'], 0);
        $instance->addListener('company.approved', [0 => function () {
            return ($this->services['marketplace.payment.lemonway_event_subscriber'] ?? $this->load('getMarketplace_Payment_LemonwayEventSubscriberService.php'));
        }, 1 => 'createCompany'], 0);
        $instance->addListener('company.updated', [0 => function () {
            return ($this->services['marketplace.payment.lemonway_event_subscriber'] ?? $this->load('getMarketplace_Payment_LemonwayEventSubscriberService.php'));
        }, 1 => 'updateCompany'], 0);
        $instance->addListener('company.send_to_psp', [0 => function () {
            return ($this->services['marketplace.payment.lemonway_event_subscriber'] ?? $this->load('getMarketplace_Payment_LemonwayEventSubscriberService.php'));
        }, 1 => 'createCompanyAndRegisterIban'], 0);
        $instance->addListener('company.iban_bic.updated', [0 => function () {
            return ($this->services['marketplace.payment.lemonway_event_subscriber'] ?? $this->load('getMarketplace_Payment_LemonwayEventSubscriberService.php'));
        }, 1 => 'registerIban'], 0);
        $instance->addListener('company.legal_documents.updated', [0 => function () {
            return ($this->services['marketplace.payment.lemonway_event_subscriber'] ?? $this->load('getMarketplace_Payment_LemonwayEventSubscriberService.php'));
        }, 1 => 'sendKyc'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Payment\\Event\\LemonwayPaymentCallbackEvent', [0 => function () {
            return ($this->services['marketplace.payment.lemonway_event_subscriber'] ?? $this->load('getMarketplace_Payment_LemonwayEventSubscriberService.php'));
        }, 1 => 'transferFromBuyerToTechnical'], 24);
        $instance->addListener('Wizacha\\Marketplace\\Payment\\Event\\LemonwayPaymentCallbackEvent', [0 => function () {
            return ($this->services['marketplace.payment.lemonway_event_subscriber'] ?? $this->load('getMarketplace_Payment_LemonwayEventSubscriberService.php'));
        }, 1 => 'marketplaceDiscountProcess'], 16);
        $instance->addListener('Wizacha\\Marketplace\\Payment\\Event\\LemonwayPaymentCallbackEvent', [0 => function () {
            return ($this->services['marketplace.payment.lemonway_event_subscriber'] ?? $this->load('getMarketplace_Payment_LemonwayEventSubscriberService.php'));
        }, 1 => 'updateTransactionStatus'], 8);
        $instance->addListener('Wizacha\\Marketplace\\Payment\\Event\\MangopayPaymentCallbackEvent', [0 => function () {
            return ($this->services['marketplace.payment.mangopay_event_subscriber'] ?? $this->load('getMarketplace_Payment_MangopayEventSubscriberService.php'));
        }, 1 => 'updateTransactionStatus'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Payment\\Event\\SmoneyPaymentCallbackEvent', [0 => function () {
            return ($this->services['marketplace.payment.smoney_event_subscriber'] ?? $this->load('getMarketplace_Payment_SmoneyEventSubscriberService.php'));
        }, 1 => 'updateTransactionStatus'], 0);
        $instance->addListener('order.updated', [0 => function () {
            return ($this->services['marketplace.payment.smoney_event_subscriber'] ?? $this->load('getMarketplace_Payment_SmoneyEventSubscriberService.php'));
        }, 1 => 'onOrderUpdate'], 0);
        $instance->addListener('company.approved', [0 => function () {
            return ($this->services['marketplace.payment.smoney_event_subscriber'] ?? $this->load('getMarketplace_Payment_SmoneyEventSubscriberService.php'));
        }, 1 => 'onCompanyApproved'], 0);
        $instance->addListener('company.send_to_psp', [0 => function () {
            return ($this->services['marketplace.payment.smoney_event_subscriber'] ?? $this->load('getMarketplace_Payment_SmoneyEventSubscriberService.php'));
        }, 1 => 'onCompanyIbanBicUpdated'], 0);
        $instance->addListener('company.iban_bic.updated', [0 => function () {
            return ($this->services['marketplace.payment.smoney_event_subscriber'] ?? $this->load('getMarketplace_Payment_SmoneyEventSubscriberService.php'));
        }, 1 => 'onCompanyIbanBicUpdated'], 0);
        $instance->addListener('company.legal_documents.updated', [0 => function () {
            return ($this->services['marketplace.payment.smoney_event_subscriber'] ?? $this->load('getMarketplace_Payment_SmoneyEventSubscriberService.php'));
        }, 1 => 'onKycUpdated'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Payment\\Event\\StripePaymentCallbackEvent', [0 => function () {
            return ($this->services['marketplace.payment.stripe_event_subscriber'] ?? $this->load('getMarketplace_Payment_StripeEventSubscriberService.php'));
        }, 1 => 'updateTransactionStatus'], 24);
        $instance->addListener('Wizacha\\Marketplace\\Payment\\Event\\StripePaymentCallbackEvent', [0 => function () {
            return ($this->services['marketplace.payment.stripe_event_subscriber'] ?? $this->load('getMarketplace_Payment_StripeEventSubscriberService.php'));
        }, 1 => 'addCreditCardToSubscription'], 16);
        $instance->addListener('Wizacha\\Marketplace\\Payment\\Event\\StripePaymentCallbackEvent', [0 => function () {
            return ($this->services['marketplace.payment.stripe_event_subscriber'] ?? $this->load('getMarketplace_Payment_StripeEventSubscriberService.php'));
        }, 1 => 'updateSubscriptionStatus'], 8);
        $instance->addListener('Wizacha\\Marketplace\\Payment\\Event\\StripeSetupCardCallbackEvent', [0 => function () {
            return ($this->services['marketplace.payment.stripe_event_subscriber'] ?? $this->load('getMarketplace_Payment_StripeEventSubscriberService.php'));
        }, 1 => 'updateCreditCard'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Payment\\Event\\StripeFailedPaymentCallbackEvent', [0 => function () {
            return ($this->services['marketplace.payment.stripe_event_subscriber'] ?? $this->load('getMarketplace_Payment_StripeEventSubscriberService.php'));
        }, 1 => 'updateSubscriptionStatus'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Payment\\Event\\OfflinePaymentEvent', [0 => function () {
            return ($this->services['marketplace.payment.offline_event_subscriber'] ?? $this->load('getMarketplace_Payment_OfflineEventSubscriberService.php'));
        }, 1 => 'onOfflinePayment'], 0);
        $instance->addListener('vendor.transfer', [0 => function () {
            return ($this->services['marketplace.transction.transfers.failed'] ?? $this->load('getMarketplace_Transction_Transfers_FailedService.php'));
        }, 1 => 'onVendorTransfer'], 0);
        $instance->addListener('vendor.commission', [0 => function () {
            return ($this->services['marketplace.transction.transfers.failed'] ?? $this->load('getMarketplace_Transction_Transfers_FailedService.php'));
        }, 1 => 'onCommissionTransfer'], 0);
        $instance->addListener('vendor.withdrawal', [0 => function () {
            return ($this->services['marketplace.transction.transfers.failed'] ?? $this->load('getMarketplace_Transction_Transfers_FailedService.php'));
        }, 1 => 'onVendorWithdrawal'], 0);
        $instance->addListener('order.updated', [0 => function () {
            return ($this->services['marketplace.promotion.promotion_subscriber'] ?? $this->load('getMarketplace_Promotion_PromotionSubscriberService.php'));
        }, 1 => 'onOrderUpdate'], 0);
        $instance->addListener('order.trashed', [0 => function () {
            return ($this->services['marketplace.promotion.promotion_subscriber'] ?? $this->load('getMarketplace_Promotion_PromotionSubscriberService.php'));
        }, 1 => 'onOrderTrash'], 0);
        $instance->addListener('multiVendorProduct_updated', [0 => function () {
            return ($this->services['marketplace.multi_vendor_product.product_synchronization.event_subscriber'] ?? $this->load('getMarketplace_MultiVendorProduct_ProductSynchronization_EventSubscriberService.php'));
        }, 1 => 'onMultiVendorProductUpdate'], 0);
        $instance->addListener('multiVendorProduct_deleted', [0 => function () {
            return ($this->services['marketplace.multi_vendor_product.product_synchronization.event_subscriber'] ?? $this->load('getMarketplace_MultiVendorProduct_ProductSynchronization_EventSubscriberService.php'));
        }, 1 => 'onMultiVendorProductDelete'], 0);
        $instance->addListener('product.delete', [0 => function () {
            return ($this->services['marketplace.division.products.service'] ?? $this->load('getMarketplace_Division_Products_ServiceService.php'));
        }, 1 => 'onProductDelete'], 0);
        $instance->addListener('company.deleted', [0 => function () {
            return ($this->services['marketplace.division.blacklists.service'] ?? $this->load('getMarketplace_Division_Blacklists_ServiceService.php'));
        }, 1 => 'onCompanyDelete'], 0);
        $instance->addListener('company.deleted', [0 => function () {
            return ($this->services['Wizacha\\Marketplace\\Commission\\CommissionService'] ?? $this->load('getCommissionServiceService.php'));
        }, 1 => 'onCompanyDelete'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Transaction\\TransactionUpdatedEvent', [0 => function () {
            return ($this->services['marketplace.commission.transaction_listener'] ?? $this->load('getMarketplace_Commission_TransactionListenerService.php'));
        }, 1 => 'onTransactionUpdated'], 0);
        $instance->addListener('order.updated', [0 => function () {
            return ($this->services['marketplace.commission.order_listener'] ?? $this->load('getMarketplace_Commission_OrderListenerService.php'));
        }, 1 => 'onOrderUpdate'], 10);
        $instance->addListener('order.updated', [0 => function () {
            return ($this->services['marketplace.order.refund.refund_event_subscriber'] ?? $this->load('getMarketplace_Order_Refund_RefundEventSubscriberService.php'));
        }, 1 => 'refundOrder'], 0);
        $instance->addListener('form.pre_set_data', [0 => function () {
            return ($this->services['Wizacha\\Marketplace\\PIM\\Product\\Template\\TemplateService'] ?? $this->load('getTemplateServiceService.php'));
        }, 1 => 'customizeForm'], 0);
        $instance->addListener('kernel.terminate', [0 => function () {
            return ($this->services['Wizacha\\Async\\Debouncer\\DebouncerEventSubscriber'] ?? $this->load('getDebouncerEventSubscriberService.php'));
        }, 1 => 'onTerminate'], 0);
        $instance->addListener('console.terminate', [0 => function () {
            return ($this->services['Wizacha\\Async\\Debouncer\\DebouncerEventSubscriber'] ?? $this->load('getDebouncerEventSubscriberService.php'));
        }, 1 => 'onTerminate'], 0);
        $instance->addListener('subscription_created', [0 => function () {
            return ($this->services['marketplace.subscription.subscription_subscriber'] ?? $this->load('getMarketplace_Subscription_SubscriptionSubscriberService.php'));
        }, 1 => 'onCreateSubscriptionEvent'], 0);
        $instance->addListener('status_updated', [0 => function () {
            return ($this->services['marketplace.subscription.subscription_subscriber'] ?? $this->load('getMarketplace_Subscription_SubscriptionSubscriberService.php'));
        }, 1 => 'onSubscriptionStatusUpdatedEvent'], 0);
        $instance->addListener('auto_renew_updated', [0 => function () {
            return ($this->services['marketplace.subscription.subscription_subscriber'] ?? $this->load('getMarketplace_Subscription_SubscriptionSubscriberService.php'));
        }, 1 => 'onSubscriptionAutoRenewUpdatedEvent'], 0);
        $instance->addListener('payment_method_renewed', [0 => function () {
            return ($this->services['marketplace.subscription.subscription_subscriber'] ?? $this->load('getMarketplace_Subscription_SubscriptionSubscriberService.php'));
        }, 1 => 'onPaymentMethodRenewedEvent'], 0);
        $instance->addListener('shipping_vendor.update', [0 => function () {
            return ($this->services['Wizacha\\ActionLogger\\EventSubscribers\\ShippingSubscriber'] ?? $this->load('getShippingSubscriberService.php'));
        }, 1 => 'onUpdate'], 0);
        $instance->addListener('product.create', [0 => function () {
            return ($this->services['Wizacha\\ActionLogger\\EventSubscribers\\ProductSubscriber'] ?? $this->load('getProductSubscriberService.php'));
        }, 1 => 'onCreate'], 0);
        $instance->addListener('product.update', [0 => function () {
            return ($this->services['Wizacha\\ActionLogger\\EventSubscribers\\ProductSubscriber'] ?? $this->load('getProductSubscriberService.php'));
        }, 1 => 'onUpdate'], 0);
        $instance->addListener('product.delete', [0 => function () {
            return ($this->services['Wizacha\\ActionLogger\\EventSubscribers\\ProductSubscriber'] ?? $this->load('getProductSubscriberService.php'));
        }, 1 => 'onDelete'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Order\\Event\\OrderStatusChanged', [0 => function () {
            return ($this->services['Wizacha\\ActionLogger\\EventSubscribers\\OrderSubscriber'] ?? $this->load('getOrderSubscriberService.php'));
        }, 1 => 'onUpdate'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Payment\\Event\\DispatchFundsFailedEvent', [0 => function () {
            return ($this->services['Wizacha\\ActionLogger\\EventSubscribers\\DispatchFundsFailedSubscriber'] ?? $this->load('getDispatchFundsFailedSubscriberService.php'));
        }, 1 => 'onUpdate'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Transaction\\TransactionUpdatedEvent', [0 => function () {
            return ($this->services['Wizacha\\ActionLogger\\EventSubscribers\\TransactionSubscriber'] ?? $this->load('getTransactionSubscriberService.php'));
        }, 1 => 'onUpdate'], 0);
        $instance->addListener('order.updated', [0 => function () {
            return ($this->services['Wizacha\\Marketplace\\Order\\OrderAction\\Event\\OrderStatusUpdatedEventSubscriber'] ?? $this->load('getOrderStatusUpdatedEventSubscriberService.php'));
        }, 1 => 'onOrderUpdate'], 0);
        $instance->addListener('Wizacha\\Marketplace\\User\\Event\\UserPasswordChanged', [0 => function () {
            return ($this->services['Wizacha\\ActionLogger\\EventSubscribers\\UserEventSubscriber'] ?? $this->load('getUserEventSubscriberService.php'));
        }, 1 => 'onUserPasswordChanged'], 10);
        $instance->addListener('kernel.response', [0 => function () {
            return ($this->privates['response_listener'] ?? ($this->privates['response_listener'] = new \Symfony\Component\HttpKernel\EventListener\ResponseListener('UTF-8')));
        }, 1 => 'onKernelResponse'], 0);
        $instance->addListener('kernel.response', [0 => function () {
            return ($this->privates['streamed_response_listener'] ?? ($this->privates['streamed_response_listener'] = new \Symfony\Component\HttpKernel\EventListener\StreamedResponseListener()));
        }, 1 => 'onKernelResponse'], -1024);
        $instance->addListener('kernel.request', [0 => function () {
            return ($this->privates['locale_listener'] ?? $this->getLocaleListenerService());
        }, 1 => 'setDefaultLocale'], 100);
        $instance->addListener('kernel.request', [0 => function () {
            return ($this->privates['locale_listener'] ?? $this->getLocaleListenerService());
        }, 1 => 'onKernelRequest'], 16);
        $instance->addListener('kernel.finish_request', [0 => function () {
            return ($this->privates['locale_listener'] ?? $this->getLocaleListenerService());
        }, 1 => 'onKernelFinishRequest'], 0);
        $instance->addListener('kernel.request', [0 => function () {
            return ($this->privates['validate_request_listener'] ?? ($this->privates['validate_request_listener'] = new \Symfony\Component\HttpKernel\EventListener\ValidateRequestListener()));
        }, 1 => 'onKernelRequest'], 256);
        $instance->addListener('kernel.request', [0 => function () {
            return ($this->privates['.legacy_resolve_controller_name_subscriber'] ?? $this->get_LegacyResolveControllerNameSubscriberService());
        }, 1 => 'resolveControllerName'], 24);
        $instance->addListener('kernel.request', [0 => function () {
            return ($this->privates['locale_aware_listener'] ?? $this->getLocaleAwareListenerService());
        }, 1 => 'onKernelRequest'], 15);
        $instance->addListener('kernel.finish_request', [0 => function () {
            return ($this->privates['locale_aware_listener'] ?? $this->getLocaleAwareListenerService());
        }, 1 => 'onKernelFinishRequest'], -15);
        $instance->addListener('console.error', [0 => function () {
            return ($this->privates['console.error_listener'] ?? $this->load('getConsole_ErrorListenerService.php'));
        }, 1 => 'onConsoleError'], -128);
        $instance->addListener('console.terminate', [0 => function () {
            return ($this->privates['console.error_listener'] ?? $this->load('getConsole_ErrorListenerService.php'));
        }, 1 => 'onConsoleTerminate'], -128);
        $instance->addListener('console.error', [0 => function () {
            return ($this->privates['console.suggest_missing_package_subscriber'] ?? ($this->privates['console.suggest_missing_package_subscriber'] = new \Symfony\Bundle\FrameworkBundle\EventListener\SuggestMissingPackageSubscriber()));
        }, 1 => 'onConsoleError'], 0);
        $instance->addListener('kernel.request', [0 => function () {
            return ($this->privates['session_listener'] ?? $this->getSessionListenerService());
        }, 1 => 'onKernelRequest'], 128);
        $instance->addListener('kernel.response', [0 => function () {
            return ($this->privates['session_listener'] ?? $this->getSessionListenerService());
        }, 1 => 'onKernelResponse'], -1000);
        $instance->addListener('kernel.finish_request', [0 => function () {
            return ($this->privates['session_listener'] ?? $this->getSessionListenerService());
        }, 1 => 'onFinishRequest'], 0);
        $instance->addListener('Symfony\\Component\\Messenger\\Event\\WorkerMessageFailedEvent', [0 => function () {
            return ($this->privates['messenger.retry.send_failed_message_for_retry_listener'] ?? $this->load('getMessenger_Retry_SendFailedMessageForRetryListenerService.php'));
        }, 1 => 'onMessageFailed'], 100);
        $instance->addListener('Symfony\\Component\\Messenger\\Event\\WorkerRunningEvent', [0 => function () {
            return ($this->privates['messenger.listener.dispatch_pcntl_signal_listener'] ?? ($this->privates['messenger.listener.dispatch_pcntl_signal_listener'] = new \Symfony\Component\Messenger\EventListener\DispatchPcntlSignalListener()));
        }, 1 => 'onWorkerRunning'], 100);
        $instance->addListener('Symfony\\Component\\Messenger\\Event\\WorkerStartedEvent', [0 => function () {
            return ($this->privates['messenger.listener.stop_worker_on_restart_signal_listener'] ?? $this->load('getMessenger_Listener_StopWorkerOnRestartSignalListenerService.php'));
        }, 1 => 'onWorkerStarted'], 0);
        $instance->addListener('Symfony\\Component\\Messenger\\Event\\WorkerRunningEvent', [0 => function () {
            return ($this->privates['messenger.listener.stop_worker_on_restart_signal_listener'] ?? $this->load('getMessenger_Listener_StopWorkerOnRestartSignalListenerService.php'));
        }, 1 => 'onWorkerRunning'], 0);
        $instance->addListener('Symfony\\Component\\Messenger\\Event\\WorkerStartedEvent', [0 => function () {
            return ($this->privates['messenger.listener.stop_worker_on_sigterm_signal_listener'] ?? ($this->privates['messenger.listener.stop_worker_on_sigterm_signal_listener'] = new \Symfony\Component\Messenger\EventListener\StopWorkerOnSigtermSignalListener()));
        }, 1 => 'onWorkerStarted'], 100);
        $instance->addListener('kernel.request', [0 => function () {
            return ($this->privates['debug.debug_handlers_listener'] ?? $this->getDebug_DebugHandlersListenerService());
        }, 1 => 'configure'], 2048);
        $instance->addListener('console.command', [0 => function () {
            return ($this->privates['debug.debug_handlers_listener'] ?? $this->getDebug_DebugHandlersListenerService());
        }, 1 => 'configure'], 2048);
        $instance->addListener('kernel.request', [0 => function () {
            return ($this->privates['router_listener'] ?? $this->getRouterListenerService());
        }, 1 => 'onKernelRequest'], 32);
        $instance->addListener('kernel.finish_request', [0 => function () {
            return ($this->privates['router_listener'] ?? $this->getRouterListenerService());
        }, 1 => 'onKernelFinishRequest'], 0);
        $instance->addListener('kernel.exception', [0 => function () {
            return ($this->privates['router_listener'] ?? $this->getRouterListenerService());
        }, 1 => 'onKernelException'], -64);
        $instance->addListener('kernel.request', [0 => function () {
            return ($this->privates['security.firewall'] ?? $this->getSecurity_FirewallService());
        }, 1 => 'configureLogoutUrlGenerator'], 8);
        $instance->addListener('kernel.request', [0 => function () {
            return ($this->privates['security.firewall'] ?? $this->getSecurity_FirewallService());
        }, 1 => 'onKernelRequest'], 8);
        $instance->addListener('kernel.finish_request', [0 => function () {
            return ($this->privates['security.firewall'] ?? $this->getSecurity_FirewallService());
        }, 1 => 'onKernelFinishRequest'], 0);
        $instance->addListener('kernel.response', [0 => function () {
            return ($this->privates['security.rememberme.response_listener'] ?? ($this->privates['security.rememberme.response_listener'] = new \Symfony\Component\Security\Http\RememberMe\ResponseListener()));
        }, 1 => 'onKernelResponse'], 0);
        $instance->addListener('Symfony\\Component\\Messenger\\Event\\WorkerMessageHandledEvent', [0 => function () {
            return ($this->privates['doctrine.orm.messenger.event_subscriber.doctrine_clear_entity_manager'] ?? $this->load('getDoctrine_Orm_Messenger_EventSubscriber_DoctrineClearEntityManagerService.php'));
        }, 1 => 'onWorkerMessageHandled'], 0);
        $instance->addListener('Symfony\\Component\\Messenger\\Event\\WorkerMessageFailedEvent', [0 => function () {
            return ($this->privates['doctrine.orm.messenger.event_subscriber.doctrine_clear_entity_manager'] ?? $this->load('getDoctrine_Orm_Messenger_EventSubscriber_DoctrineClearEntityManagerService.php'));
        }, 1 => 'onWorkerMessageFailed'], 0);
        $instance->addListener('kernel.exception', [0 => function () {
            return ($this->privates['twig.exception_listener'] ?? $this->load('getTwig_ExceptionListenerService.php'));
        }, 1 => 'logKernelException'], 0);
        $instance->addListener('kernel.exception', [0 => function () {
            return ($this->privates['twig.exception_listener'] ?? $this->load('getTwig_ExceptionListenerService.php'));
        }, 1 => 'onKernelException'], -128);
        $instance->addListener('Symfony\\Component\\Mailer\\Event\\MessageEvent', [0 => function () {
            return ($this->privates['twig.mailer.message_listener'] ?? $this->load('getTwig_Mailer_MessageListenerService.php'));
        }, 1 => 'onMessage'], 0);
        $instance->addListener('kernel.exception', [0 => function () {
            return ($this->privates['swiftmailer.email_sender.listener'] ?? $this->load('getSwiftmailer_EmailSender_ListenerService.php'));
        }, 1 => 'onException'], 0);
        $instance->addListener('kernel.terminate', [0 => function () {
            return ($this->privates['swiftmailer.email_sender.listener'] ?? $this->load('getSwiftmailer_EmailSender_ListenerService.php'));
        }, 1 => 'onTerminate'], 0);
        $instance->addListener('console.error', [0 => function () {
            return ($this->privates['swiftmailer.email_sender.listener'] ?? $this->load('getSwiftmailer_EmailSender_ListenerService.php'));
        }, 1 => 'onException'], 0);
        $instance->addListener('console.terminate', [0 => function () {
            return ($this->privates['swiftmailer.email_sender.listener'] ?? $this->load('getSwiftmailer_EmailSender_ListenerService.php'));
        }, 1 => 'onTerminate'], 0);
        $instance->addListener('product.create', [0 => function () {
            return ($this->services['marketplace.multi_vendor_product.linker'] ?? $this->load('getMarketplace_MultiVendorProduct_LinkerService.php'));
        }, 1 => 'onProductChange'], 0);
        $instance->addListener('product.update', [0 => function () {
            return ($this->services['marketplace.multi_vendor_product.linker'] ?? $this->load('getMarketplace_MultiVendorProduct_LinkerService.php'));
        }, 1 => 'onProductChange'], 0);
        $instance->addListener('multiVendorProduct_updated', [0 => function () {
            return ($this->services['marketplace.multi_vendor_product.linker'] ?? $this->load('getMarketplace_MultiVendorProduct_LinkerService.php'));
        }, 1 => 'onMultiVendorProductChange'], 0);
        $instance->addListener('Wizacha\\Component\\Notification\\TestNotificationRequested', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Order\\Event\\ShipmentCreated', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\User\\Event\\UserAskedToRecoverPassword', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\User\\Event\\UserProfileActivated', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\User\\Event\\UserProfileCreated', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\User\\Event\\UserProfileUpdated', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\User\\Event\\UserProfileActivationRequested', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\User\\Event\\UserProfileBlocked', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\PIM\\Option\\Event\\OptionApproved', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\PIM\\Option\\Event\\OptionRejected', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Order\\Event\\OrderCodeGenerated', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Order\\Event\\OrderCodeFailed', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Order\\Refund\\Event\\OrderRefundedEvent', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Order\\Event\\OrderStatusChanged', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Payment\\Event\\DispatchFundsFailedEvent', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Tax\\Event\\MissingTaxConfigurationEvent', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\PIM\\Product\\Event\\ProductApproved', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\PIM\\Product\\Event\\ProductRejected', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\PIM\\Product\\Event\\ProductChangesRequested', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\PIM\\Product\\Event\\ProductStockThresholdReached', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Company\\Event\\CompanyApplied', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Company\\Event\\C2cCompanyApplied', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Company\\Event\\CompanyPending', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Company\\Event\\CompanyDisabled', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Company\\Event\\CompanyRejected', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\PIM\\Moderation\\ProductWasReported', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Cms\\Event\\ContactFormSubmitted', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Discuss\\Event\\DiscussMessagePosted', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Order\\OrderReturn\\Event\\RmaRequested', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Order\\OrderReturn\\Event\\RmaReceived', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Order\\OrderReturn\\Event\\RmaDeclined', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Order\\AfterSales\\Event\\LitigationCreated', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Order\\AfterSales\\Event\\AfterSalesServiceRequested', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Exim\\AsyncExportHasFinished', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Organisation\\Event\\OrganisationRegistered', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Organisation\\Event\\OrganisationApproved', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Organisation\\Event\\OrganisationDisapproved', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Payment\\Event\\InternalTransferErrorEvent', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Payment\\Event\\BankwireFailedToRetrieveOrderEvent', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Payment\\Event\\BankwireNotificationFailedStatusEvent', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Payment\\Event\\HipayTransactionChargedbackEvent', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Currency\\Event\\CurrencyRatesUpdateFailed', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Payment\\Event\\BankwirePaymentEvent', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\CompanyPerson\\Event\\UBOValidationFailed', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\Security\\Event\\IpBlockedEvent', [0 => function () {
            return ($this->services['app.notification_dispatcher'] ?? $this->load('getApp_NotificationDispatcherService.php'));
        }, 1 => 'dispatch'], 0);
        $instance->addListener('Wizacha\\Marketplace\\User\\Event\\Yavin\\UserActivated', [0 => function () {
            return ($this->privates['Wizacha\\AppBundle\\EventSubscriber\\Yavin\\User\\UserActivatedSubscriber'] ?? $this->load('getUserActivatedSubscriberService.php'));
        }, 1 => 'dispatchMessage'], 0);
        $instance->addListener('Wizacha\\Marketplace\\User\\Event\\Yavin\\UserCreated', [0 => function () {
            return ($this->privates['Wizacha\\AppBundle\\EventSubscriber\\Yavin\\User\\UserCreatedSubscriber'] ?? $this->load('getUserCreatedSubscriberService.php'));
        }, 1 => 'dispatchMessage'], 0);
        $instance->addListener('Wizacha\\Marketplace\\User\\Event\\Yavin\\UserUpdated', [0 => function () {
            return ($this->privates['Wizacha\\AppBundle\\EventSubscriber\\Yavin\\User\\UserUpdatedSubscriber'] ?? $this->load('getUserUpdatedSubscriberService.php'));
        }, 1 => 'dispatchMessage'], 0);
        $instance->addListener('Wizacha\\Marketplace\\User\\Event\\Yavin\\UserDeactivated', [0 => function () {
            return ($this->privates['Wizacha\\AppBundle\\EventSubscriber\\Yavin\\User\\UserDeactivatedSubscriber'] ?? $this->load('getUserDeactivatedSubscriberService.php'));
        }, 1 => 'dispatchMessage'], 0);
        $instance->addListener('Wizacha\\Marketplace\\User\\Event\\Yavin\\UserTypeChanged', [0 => function () {
            return ($this->privates['Wizacha\\AppBundle\\EventSubscriber\\Yavin\\User\\UserTypeChangedSubscriber'] ?? $this->load('getUserTypeChangedSubscriberService.php'));
        }, 1 => 'dispatchMessage'], 0);
        $instance->addListener('Wizacha\\Marketplace\\User\\Event\\Yavin\\UserCompanyChanged', [0 => function () {
            return ($this->privates['Wizacha\\AppBundle\\EventSubscriber\\Yavin\\User\\UserCompanyChangedSubscriber'] ?? $this->load('getUserCompanyChangedSubscriberService.php'));
        }, 1 => 'dispatchMessage'], 0);
        $instance->addListener('kernel.request', [0 => function () {
            return ($this->privates['app.listener.language'] ?? $this->getApp_Listener_LanguageService());
        }, 1 => 'detectLanguage'], 254);
        $instance->addListener('kernel.response', [0 => function () {
            return ($this->privates['app.listener.language'] ?? $this->getApp_Listener_LanguageService());
        }, 1 => 'injectContentLanguage'], 0);

        return $instance;
    }

    /*
     * Gets the public 'http_kernel' shared service.
     *
     * @return \Symfony\Component\HttpKernel\HttpKernel
     */
    protected function getHttpKernelService()
    {
        return $this->services['http_kernel'] = new \Symfony\Component\HttpKernel\HttpKernel(($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()), new \Symfony\Bundle\FrameworkBundle\Controller\ControllerResolver($this, ($this->privates['monolog.logger.request'] ?? $this->getMonolog_Logger_RequestService()), ($this->privates['.legacy_controller_name_converter'] ?? ($this->privates['.legacy_controller_name_converter'] = new \Symfony\Bundle\FrameworkBundle\Controller\ControllerNameParser(($this->services['kernel'] ?? $this->get('kernel', 1)), false)))), ($this->services['request_stack'] ?? ($this->services['request_stack'] = new \Symfony\Component\HttpFoundation\RequestStack())), new \Symfony\Component\HttpKernel\Controller\ArgumentResolver(new \Symfony\Component\HttpKernel\ControllerMetadata\ArgumentMetadataFactory(), new RewindableGenerator(function () {
            yield 0 => ($this->privates['argument_resolver.request_attribute'] ?? ($this->privates['argument_resolver.request_attribute'] = new \Symfony\Component\HttpKernel\Controller\ArgumentResolver\RequestAttributeValueResolver()));
            yield 1 => ($this->privates['argument_resolver.request'] ?? ($this->privates['argument_resolver.request'] = new \Symfony\Component\HttpKernel\Controller\ArgumentResolver\RequestValueResolver()));
            yield 2 => ($this->privates['argument_resolver.session'] ?? ($this->privates['argument_resolver.session'] = new \Symfony\Component\HttpKernel\Controller\ArgumentResolver\SessionValueResolver()));
            yield 3 => ($this->privates['security.user_value_resolver'] ?? $this->load('getSecurity_UserValueResolverService.php'));
            yield 4 => ($this->privates['argument_resolver.service'] ?? $this->load('getArgumentResolver_ServiceService.php'));
            yield 5 => ($this->privates['argument_resolver.default'] ?? ($this->privates['argument_resolver.default'] = new \Symfony\Component\HttpKernel\Controller\ArgumentResolver\DefaultValueResolver()));
            yield 6 => ($this->privates['argument_resolver.variadic'] ?? ($this->privates['argument_resolver.variadic'] = new \Symfony\Component\HttpKernel\Controller\ArgumentResolver\VariadicValueResolver()));
        }, 7)));
    }

    /*
     * Gets the public 'marketplace.ekey.repository' shared service.
     *
     * @return \Wizacha\Marketplace\User\EKeyRepository
     */
    protected function getMarketplace_Ekey_RepositoryService()
    {
        return $this->services['marketplace.ekey.repository'] = new \Wizacha\Marketplace\User\EKeyRepository(($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()));
    }

    /*
     * Gets the public 'marketplace.seo.slug_generator' shared service.
     *
     * @return \Wizacha\Marketplace\Seo\Slug\SlugGenerator
     */
    protected function getMarketplace_Seo_SlugGeneratorService()
    {
        return $this->services['marketplace.seo.slug_generator'] = new \Wizacha\Marketplace\Seo\Slug\SlugGenerator();
    }

    /*
     * Gets the public 'monolog.logger.psp' shared service.
     *
     * @return \Symfony\Bridge\Monolog\Logger
     */
    protected function getMonolog_Logger_PspService()
    {
        $this->services['monolog.logger.psp'] = $instance = new \Symfony\Bridge\Monolog\Logger('psp');

        $instance->pushProcessor([0 => ($this->privates['monolog.processor.http_request'] ?? ($this->privates['monolog.processor.http_request'] = new \Wizacha\Bridge\Monolog\Processor\HttpRequestProcessor($this->getEnv('REQUEST_ID_HEADER_NAME')))), 1 => 'addCorrelationInformation']);
        $instance->pushProcessor([0 => ($this->privates['monolog.processor.console_command'] ?? ($this->privates['monolog.processor.console_command'] = new \Wizacha\Bridge\Monolog\Processor\ConsoleCommandProcessor())), 1 => 'addCorrelationInformation']);
        $instance->pushProcessor(($this->privates['monolog.processor.extra_context'] ?? ($this->privates['monolog.processor.extra_context'] = new \Wizacha\Bridge\Monolog\Processor\ExtraContextProcessor())));
        $instance->pushHandler(($this->privates['monolog.handler.main'] ?? $this->getMonolog_Handler_MainService()));
        $instance->pushHandler(($this->privates['monolog.handler.verbose'] ?? $this->getMonolog_Handler_VerboseService()));

        return $instance;
    }

    /*
     * Gets the public 'request_stack' shared service.
     *
     * @return \Symfony\Component\HttpFoundation\RequestStack
     */
    protected function getRequestStackService()
    {
        return $this->services['request_stack'] = new \Symfony\Component\HttpFoundation\RequestStack();
    }

    /*
     * Gets the public 'router' shared service.
     *
     * @return \Symfony\Bundle\FrameworkBundle\Routing\Router
     */
    protected function getRouterService()
    {
        $a = new \Symfony\Bridge\Monolog\Logger('router');
        $a->pushProcessor([0 => ($this->privates['monolog.processor.http_request'] ?? ($this->privates['monolog.processor.http_request'] = new \Wizacha\Bridge\Monolog\Processor\HttpRequestProcessor($this->getEnv('REQUEST_ID_HEADER_NAME')))), 1 => 'addCorrelationInformation']);
        $a->pushProcessor([0 => ($this->privates['monolog.processor.console_command'] ?? ($this->privates['monolog.processor.console_command'] = new \Wizacha\Bridge\Monolog\Processor\ConsoleCommandProcessor())), 1 => 'addCorrelationInformation']);
        $a->pushProcessor(($this->privates['monolog.processor.extra_context'] ?? ($this->privates['monolog.processor.extra_context'] = new \Wizacha\Bridge\Monolog\Processor\ExtraContextProcessor())));
        $a->pushHandler(($this->privates['monolog.handler.main'] ?? $this->getMonolog_Handler_MainService()));

        $this->services['router'] = $instance = new \Symfony\Bundle\FrameworkBundle\Routing\Router((new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($this->getService, [
            'routing.loader' => ['services', 'routing.loader', 'getRouting_LoaderService.php', true],
        ], [
            'routing.loader' => 'Symfony\\Component\\Config\\Loader\\LoaderInterface',
        ]))->withContext('router.default', $this), (\dirname(__DIR__, 4).'/app/config/routing.yml'), ['cache_dir' => $this->targetDir.'', 'debug' => false, 'generator_class' => 'Symfony\\Component\\Routing\\Generator\\CompiledUrlGenerator', 'generator_dumper_class' => 'Symfony\\Component\\Routing\\Generator\\Dumper\\CompiledUrlGeneratorDumper', 'matcher_class' => 'Symfony\\Bundle\\FrameworkBundle\\Routing\\RedirectableCompiledUrlMatcher', 'matcher_dumper_class' => 'Symfony\\Component\\Routing\\Matcher\\Dumper\\CompiledUrlMatcherDumper', 'strict_requirements' => NULL], ($this->privates['router.request_context'] ?? $this->getRouter_RequestContextService()), ($this->privates['parameter_bag'] ?? ($this->privates['parameter_bag'] = new \Symfony\Component\DependencyInjection\ParameterBag\ContainerBag($this))), $a, 'fr');

        $instance->setConfigCacheFactory(($this->privates['config_cache_factory'] ?? ($this->privates['config_cache_factory'] = new \Symfony\Component\Config\ResourceCheckerConfigCacheFactory())));

        return $instance;
    }

    /*
     * Gets the public 'router_listener.cs_cart' shared autowired service.
     *
     * @return \Wizacha\AppBundle\EventListener\RouterListener
     */
    protected function getRouterListener_CsCartService()
    {
        return $this->services['router_listener.cs_cart'] = new \Wizacha\AppBundle\EventListener\RouterListener(($this->privates['router_listener'] ?? $this->getRouterListenerService()), ($this->privates['router.request_context'] ?? $this->getRouter_RequestContextService()));
    }

    /*
     * Gets the public 'security.token_storage' shared service.
     *
     * @return \Symfony\Component\Security\Core\Authentication\Token\Storage\UsageTrackingTokenStorage
     */
    protected function getSecurity_TokenStorageService()
    {
        return $this->services['security.token_storage'] = new \Symfony\Component\Security\Core\Authentication\Token\Storage\UsageTrackingTokenStorage(($this->privates['security.untracked_token_storage'] ?? ($this->privates['security.untracked_token_storage'] = new \Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorage())), new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($this->getService, [
            'session' => ['services', 'session', 'getSessionService.php', true],
        ], [
            'session' => '?',
        ]));
    }

    /*
     * Gets the private '.legacy_resolve_controller_name_subscriber' shared service.
     *
     * @return \Symfony\Bundle\FrameworkBundle\EventListener\ResolveControllerNameSubscriber
     */
    protected function get_LegacyResolveControllerNameSubscriberService()
    {
        return $this->privates['.legacy_resolve_controller_name_subscriber'] = new \Symfony\Bundle\FrameworkBundle\EventListener\ResolveControllerNameSubscriber(($this->privates['.legacy_controller_name_converter'] ?? ($this->privates['.legacy_controller_name_converter'] = new \Symfony\Bundle\FrameworkBundle\Controller\ControllerNameParser(($this->services['kernel'] ?? $this->get('kernel', 1)), false))), false);
    }

    /*
     * Gets the private 'Wizacha\AppBundle\EventSubscriber\PspRequestSubscriber' shared autowired service.
     *
     * @return \Wizacha\AppBundle\EventSubscriber\PspRequestSubscriber
     */
    protected function getPspRequestSubscriberService()
    {
        return $this->privates['Wizacha\\AppBundle\\EventSubscriber\\PspRequestSubscriber'] = new \Wizacha\AppBundle\EventSubscriber\PspRequestSubscriber(($this->services['monolog.logger.psp'] ?? $this->getMonolog_Logger_PspService()));
    }

    /*
     * Gets the private 'annotations.cache_adapter' shared service.
     *
     * @return \Symfony\Component\Cache\Adapter\PhpArrayAdapter
     */
    protected function getAnnotations_CacheAdapterService()
    {
        return \Symfony\Component\Cache\Adapter\PhpArrayAdapter::create(($this->targetDir.''.'/annotations.php'), ($this->privates['cache.annotations'] ?? $this->getCache_AnnotationsService()));
    }

    /*
     * Gets the private 'annotations.cached_reader' shared service.
     *
     * @return \Doctrine\Common\Annotations\PsrCachedReader
     */
    protected function getAnnotations_CachedReaderService()
    {
        return $this->privates['annotations.cached_reader'] = new \Doctrine\Common\Annotations\PsrCachedReader(($this->privates['annotations.reader'] ?? $this->getAnnotations_ReaderService()), $this->getAnnotations_CacheAdapterService(), false);
    }

    /*
     * Gets the private 'annotations.reader' shared service.
     *
     * @return \Doctrine\Common\Annotations\AnnotationReader
     */
    protected function getAnnotations_ReaderService()
    {
        $this->privates['annotations.reader'] = $instance = new \Doctrine\Common\Annotations\AnnotationReader();

        $a = new \Doctrine\Common\Annotations\AnnotationRegistry();
        $a->registerUniqueLoader('class_exists');

        $instance->addGlobalIgnoredName('required', $a);

        return $instance;
    }

    /*
     * Gets the private 'app.feature_flag.listener' shared autowired service.
     *
     * @return \Wizacha\AppBundle\EventListener\FeatureFlagListener
     */
    protected function getApp_FeatureFlag_ListenerService()
    {
        return $this->privates['app.feature_flag.listener'] = new \Wizacha\AppBundle\EventListener\FeatureFlagListener(($this->services['Wizacha\\FeatureFlag\\FeatureFlagService'] ?? $this->getFeatureFlagServiceService()));
    }

    /*
     * Gets the private 'app.listener.language' shared service.
     *
     * @return \Wizacha\AppBundle\EventListener\LanguageListener
     */
    protected function getApp_Listener_LanguageService()
    {
        return $this->privates['app.listener.language'] = new \Wizacha\AppBundle\EventListener\LanguageListener(($this->services['app.locale.detector'] ?? $this->getApp_Locale_DetectorService()));
    }

    /*
     * Gets the private 'cache.annotations' shared service.
     *
     * @return \Symfony\Component\Cache\Adapter\AdapterInterface
     */
    protected function getCache_AnnotationsService()
    {
        return $this->privates['cache.annotations'] = \Symfony\Component\Cache\Adapter\AbstractAdapter::createSystemCache('y8dsTiH+0A', 0, $this->getParameter('container.build_id'), ($this->targetDir.''.'/pools'), ($this->privates['monolog.logger.cache'] ?? $this->getMonolog_Logger_CacheService()));
    }

    /*
     * Gets the private 'debug.debug_handlers_listener' shared service.
     *
     * @return \Symfony\Component\HttpKernel\EventListener\DebugHandlersListener
     */
    protected function getDebug_DebugHandlersListenerService()
    {
        return $this->privates['debug.debug_handlers_listener'] = new \Symfony\Component\HttpKernel\EventListener\DebugHandlersListener(NULL, NULL, NULL, 0, false, ($this->privates['debug.file_link_formatter'] ?? ($this->privates['debug.file_link_formatter'] = new \Symfony\Component\HttpKernel\Debug\FileLinkFormatter(NULL))), false);
    }

    /*
     * Gets the private 'locale_aware_listener' shared service.
     *
     * @return \Symfony\Component\HttpKernel\EventListener\LocaleAwareListener
     */
    protected function getLocaleAwareListenerService()
    {
        return $this->privates['locale_aware_listener'] = new \Symfony\Component\HttpKernel\EventListener\LocaleAwareListener(new RewindableGenerator(function () {
            yield 0 => ($this->privates['translator.default'] ?? $this->load('getTranslator_DefaultService.php'));
        }, 1), ($this->services['request_stack'] ?? ($this->services['request_stack'] = new \Symfony\Component\HttpFoundation\RequestStack())));
    }

    /*
     * Gets the private 'locale_listener' shared service.
     *
     * @return \Symfony\Component\HttpKernel\EventListener\LocaleListener
     */
    protected function getLocaleListenerService()
    {
        return $this->privates['locale_listener'] = new \Symfony\Component\HttpKernel\EventListener\LocaleListener(($this->services['request_stack'] ?? ($this->services['request_stack'] = new \Symfony\Component\HttpFoundation\RequestStack())), 'fr', ($this->services['router'] ?? $this->getRouterService()));
    }

    /*
     * Gets the private 'monolog.handler.main' shared service.
     *
     * @return \Monolog\Handler\FingersCrossedHandler
     */
    protected function getMonolog_Handler_MainService()
    {
        $a = new \Monolog\Handler\StreamHandler((\dirname(__DIR__, 3).'/logs/prod.json'), 100, true, NULL, false);
        $a->pushProcessor(($this->privates['monolog.processor.psr_log_message'] ?? ($this->privates['monolog.processor.psr_log_message'] = new \Monolog\Processor\PsrLogMessageProcessor())));
        $a->setFormatter(($this->privates['monolog.formatter.logstash'] ?? ($this->privates['monolog.formatter.logstash'] = new \Monolog\Formatter\LogstashFormatter('app'))));

        return $this->privates['monolog.handler.main'] = new \Monolog\Handler\FingersCrossedHandler($a, 400, 50, true, true, NULL);
    }

    /*
     * Gets the private 'monolog.handler.verbose' shared service.
     *
     * @return \Monolog\Handler\StreamHandler
     */
    protected function getMonolog_Handler_VerboseService()
    {
        $this->privates['monolog.handler.verbose'] = $instance = new \Monolog\Handler\StreamHandler((\dirname(__DIR__, 3).'/logs/prod.json'), 100, false, NULL, false);

        $instance->pushProcessor(($this->privates['monolog.processor.psr_log_message'] ?? ($this->privates['monolog.processor.psr_log_message'] = new \Monolog\Processor\PsrLogMessageProcessor())));
        $instance->setFormatter(($this->privates['monolog.formatter.logstash'] ?? ($this->privates['monolog.formatter.logstash'] = new \Monolog\Formatter\LogstashFormatter('app'))));

        return $instance;
    }

    /*
     * Gets the private 'monolog.logger.cache' shared service.
     *
     * @return \Symfony\Bridge\Monolog\Logger
     */
    protected function getMonolog_Logger_CacheService()
    {
        $this->privates['monolog.logger.cache'] = $instance = new \Symfony\Bridge\Monolog\Logger('cache');

        $instance->pushProcessor([0 => ($this->privates['monolog.processor.http_request'] ?? ($this->privates['monolog.processor.http_request'] = new \Wizacha\Bridge\Monolog\Processor\HttpRequestProcessor($this->getEnv('REQUEST_ID_HEADER_NAME')))), 1 => 'addCorrelationInformation']);
        $instance->pushProcessor([0 => ($this->privates['monolog.processor.console_command'] ?? ($this->privates['monolog.processor.console_command'] = new \Wizacha\Bridge\Monolog\Processor\ConsoleCommandProcessor())), 1 => 'addCorrelationInformation']);
        $instance->pushProcessor(($this->privates['monolog.processor.extra_context'] ?? ($this->privates['monolog.processor.extra_context'] = new \Wizacha\Bridge\Monolog\Processor\ExtraContextProcessor())));
        $instance->pushHandler(($this->privates['monolog.handler.main'] ?? $this->getMonolog_Handler_MainService()));

        return $instance;
    }

    /*
     * Gets the private 'monolog.logger.http_request' shared service.
     *
     * @return \Wizacha\Bridge\Monolog\EventLogger\HttpRequestEventLogger
     */
    protected function getMonolog_Logger_HttpRequestService()
    {
        $this->privates['monolog.logger.http_request'] = $instance = new \Wizacha\Bridge\Monolog\EventLogger\HttpRequestEventLogger();

        $instance->setLogger(($this->privates['monolog.logger.request'] ?? $this->getMonolog_Logger_RequestService()));

        return $instance;
    }

    /*
     * Gets the private 'monolog.logger.request' shared service.
     *
     * @return \Symfony\Bridge\Monolog\Logger
     */
    protected function getMonolog_Logger_RequestService()
    {
        $this->privates['monolog.logger.request'] = $instance = new \Symfony\Bridge\Monolog\Logger('request');

        $instance->pushProcessor([0 => ($this->privates['monolog.processor.http_request'] ?? ($this->privates['monolog.processor.http_request'] = new \Wizacha\Bridge\Monolog\Processor\HttpRequestProcessor($this->getEnv('REQUEST_ID_HEADER_NAME')))), 1 => 'addCorrelationInformation']);
        $instance->pushProcessor([0 => ($this->privates['monolog.processor.console_command'] ?? ($this->privates['monolog.processor.console_command'] = new \Wizacha\Bridge\Monolog\Processor\ConsoleCommandProcessor())), 1 => 'addCorrelationInformation']);
        $instance->pushProcessor(($this->privates['monolog.processor.extra_context'] ?? ($this->privates['monolog.processor.extra_context'] = new \Wizacha\Bridge\Monolog\Processor\ExtraContextProcessor())));
        $instance->pushHandler(($this->privates['monolog.handler.main'] ?? $this->getMonolog_Handler_MainService()));

        return $instance;
    }

    /*
     * Gets the private 'nelmio_cors.cors_listener' shared service.
     *
     * @return \Nelmio\CorsBundle\EventListener\CorsListener
     */
    protected function getNelmioCors_CorsListenerService()
    {
        return $this->privates['nelmio_cors.cors_listener'] = new \Nelmio\CorsBundle\EventListener\CorsListener(($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()), new \Nelmio\CorsBundle\Options\Resolver([0 => new \Nelmio\CorsBundle\Options\ConfigProvider($this->parameters['nelmio_cors.map'], $this->parameters['nelmio_cors.defaults'])]));
    }

    /*
     * Gets the private 'parameter_bag' shared service.
     *
     * @return \Symfony\Component\DependencyInjection\ParameterBag\ContainerBag
     */
    protected function getParameterBagService()
    {
        return $this->privates['parameter_bag'] = new \Symfony\Component\DependencyInjection\ParameterBag\ContainerBag($this);
    }

    /*
     * Gets the private 'router.request_context' shared service.
     *
     * @return \Symfony\Component\Routing\RequestContext
     */
    protected function getRouter_RequestContextService()
    {
        return $this->privates['router.request_context'] = new \Symfony\Component\Routing\RequestContext('', 'GET', 'back.marketplace.ovhcloud.com', 'https', 80, 443);
    }

    /*
     * Gets the private 'router_listener' shared service.
     *
     * @return \Symfony\Component\HttpKernel\EventListener\RouterListener
     */
    protected function getRouterListenerService()
    {
        return $this->privates['router_listener'] = new \Symfony\Component\HttpKernel\EventListener\RouterListener(($this->services['router'] ?? $this->getRouterService()), ($this->services['request_stack'] ?? ($this->services['request_stack'] = new \Symfony\Component\HttpFoundation\RequestStack())), ($this->privates['router.request_context'] ?? $this->getRouter_RequestContextService()), ($this->privates['monolog.logger.request'] ?? $this->getMonolog_Logger_RequestService()), \dirname(__DIR__, 4), false);
    }

    /*
     * Gets the private 'security.firewall' shared service.
     *
     * @return \Symfony\Bundle\SecurityBundle\EventListener\FirewallListener
     */
    protected function getSecurity_FirewallService()
    {
        return $this->privates['security.firewall'] = new \Symfony\Bundle\SecurityBundle\EventListener\FirewallListener(new \Symfony\Bundle\SecurityBundle\Security\FirewallMap(new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($this->getService, [
            'security.firewall.map.context.api_get_key' => ['privates', 'security.firewall.map.context.api_get_key', 'getSecurity_Firewall_Map_Context_ApiGetKeyService.php', true],
            'security.firewall.map.context.default' => ['privates', 'security.firewall.map.context.default', 'getSecurity_Firewall_Map_Context_DefaultService.php', true],
            'security.firewall.map.context.dev' => ['privates', 'security.firewall.map.context.dev', 'getSecurity_Firewall_Map_Context_DevService.php', true],
            'security.firewall.map.context.system' => ['privates', 'security.firewall.map.context.system', 'getSecurity_Firewall_Map_Context_SystemService.php', true],
        ], [
            'security.firewall.map.context.api_get_key' => '?',
            'security.firewall.map.context.default' => '?',
            'security.firewall.map.context.dev' => '?',
            'security.firewall.map.context.system' => '?',
        ]), new RewindableGenerator(function () {
            yield 'security.firewall.map.context.dev' => ($this->privates['.security.request_matcher.Iy.T22O'] ?? ($this->privates['.security.request_matcher.Iy.T22O'] = new \Symfony\Component\HttpFoundation\RequestMatcher('^/(_(profiler|wdt)|css|images|js)/')));
            yield 'security.firewall.map.context.system' => ($this->privates['.security.request_matcher.PHFiQdM'] ?? ($this->privates['.security.request_matcher.PHFiQdM'] = new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/translations/front')));
            yield 'security.firewall.map.context.api_get_key' => ($this->privates['.security.request_matcher.8C0nUd.'] ?? ($this->privates['.security.request_matcher.8C0nUd.'] = new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/users/authenticate')));
            yield 'security.firewall.map.context.default' => NULL;
        }, 4)), ($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()), ($this->privates['security.logout_url_generator'] ?? $this->getSecurity_LogoutUrlGeneratorService()));
    }

    /*
     * Gets the private 'security.logout_url_generator' shared service.
     *
     * @return \Symfony\Component\Security\Http\Logout\LogoutUrlGenerator
     */
    protected function getSecurity_LogoutUrlGeneratorService()
    {
        return $this->privates['security.logout_url_generator'] = new \Symfony\Component\Security\Http\Logout\LogoutUrlGenerator(($this->services['request_stack'] ?? ($this->services['request_stack'] = new \Symfony\Component\HttpFoundation\RequestStack())), ($this->services['router'] ?? $this->getRouterService()), ($this->services['security.token_storage'] ?? $this->getSecurity_TokenStorageService()));
    }

    /*
     * Gets the private 'session_listener' shared service.
     *
     * @return \Symfony\Component\HttpKernel\EventListener\SessionListener
     */
    protected function getSessionListenerService()
    {
        return $this->privates['session_listener'] = new \Symfony\Component\HttpKernel\EventListener\SessionListener(new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($this->getService, [
            'initialized_session' => ['services', 'session', NULL, true],
            'session' => ['services', 'session', 'getSessionService.php', true],
        ], [
            'initialized_session' => '?',
            'session' => '?',
        ]));
    }

    /**
     * @return array|bool|float|int|string|\UnitEnum|null
     */
    public function getParameter($name)
    {
        $name = (string) $name;
        if (isset($this->buildParameters[$name])) {
            return $this->buildParameters[$name];
        }

        if (!(isset($this->parameters[$name]) || isset($this->loadedDynamicParameters[$name]) || array_key_exists($name, $this->parameters))) {
            throw new InvalidArgumentException(sprintf('The parameter "%s" must be defined.', $name));
        }
        if (isset($this->loadedDynamicParameters[$name])) {
            return $this->loadedDynamicParameters[$name] ? $this->dynamicParameters[$name] : $this->getDynamicParameter($name);
        }

        return $this->parameters[$name];
    }

    public function hasParameter($name): bool
    {
        $name = (string) $name;
        if (isset($this->buildParameters[$name])) {
            return true;
        }

        return isset($this->parameters[$name]) || isset($this->loadedDynamicParameters[$name]) || array_key_exists($name, $this->parameters);
    }

    public function setParameter($name, $value): void
    {
        throw new LogicException('Impossible to call set() on a frozen ParameterBag.');
    }

    public function getParameterBag(): ParameterBagInterface
    {
        if (null === $this->parameterBag) {
            $parameters = $this->parameters;
            foreach ($this->loadedDynamicParameters as $name => $loaded) {
                $parameters[$name] = $loaded ? $this->dynamicParameters[$name] : $this->getDynamicParameter($name);
            }
            foreach ($this->buildParameters as $name => $value) {
                $parameters[$name] = $value;
            }
            $this->parameterBag = new FrozenParameterBag($parameters);
        }

        return $this->parameterBag;
    }

    private $loadedDynamicParameters = [
        'kernel.cache_dir' => false,
        'okta_authorization_server' => false,
        'okta_client_id' => false,
        'okta_client_secret' => false,
        'okta_redirect_uri' => false,
        'okta_admin_group' => false,
        'okta_vendor_group' => false,
        'okta_response_type' => false,
        'okta_bo_authorization_server' => false,
        'okta_bo_client_id' => false,
        'okta_bo_client_secret' => false,
        'okta_bo_redirect_uri' => false,
        'okta_bo_admin_group' => false,
        'okta_bo_vendor_group' => false,
        'okta_bo_response_type' => false,
        'openid_discovery_uri' => false,
        'openid_client_id' => false,
        'openid_client_secret' => false,
        'openid_redirect_uri' => false,
        'openid_default_user_type' => false,
        'openid_bo_discovery_uri' => false,
        'openid_bo_client_id' => false,
        'openid_bo_client_secret' => false,
        'openid_bo_redirect_uri' => false,
        'openid_bo_default_user_type' => false,
        'azure_ad_discovery_uri' => false,
        'azure_ad_default_user_type' => false,
        'azure_ad_client_id' => false,
        'azure_ad_response_type' => false,
        'azure_ad_client_secret' => false,
        'azure_ad_redirect_uri' => false,
        'azure_ad_bo_discovery_uri' => false,
        'azure_ad_bo_default_user_type' => false,
        'azure_ad_bo_client_id' => false,
        'azure_ad_bo_response_type' => false,
        'azure_ad_bo_client_secret' => false,
        'azure_ad_bo_redirect_uri' => false,
        'google_oauth_redirect_uri' => false,
        'somfy_authorization_server' => false,
        'feature.carrier.mondial_relay' => false,
        'feature.carrier.chronopost.chronorelais' => false,
        'feature.carrier.chronopost.chrono13' => false,
        'kernel.secret' => false,
        'request_id.header_name' => false,
        'aws.transcoding.region' => false,
        'redis_host' => false,
        'redis.db' => false,
        'mangopay_api.client_id' => false,
        'mangopay_api.client_password' => false,
        'mangopay_api.base_url' => false,
        'mangopay_api.secure_mode' => false,
        'mangopay_user_id_agent_etablissement_paiement' => false,
        'stripe.secret_token' => false,
        'stripe.public_token' => false,
        'smoney.token' => false,
        'smoney.api_base_url' => false,
        'smoney.website_name' => false,
        'lemonway_api.client_login' => false,
        'lemonway_api.client_password' => false,
        'lemonway_api.directkit_url' => false,
        'lemonway_api.webkit_url' => false,
        'lemonway_api.marketplace_id' => false,
        'lemonway_api.use_buyer_wallet_for_moneyin' => false,
        'lemonway_api.tech_wallet_id' => false,
        'lemonway_api.marketplace_discount_wallet' => false,
        'lemonway_proxy.host' => false,
        'lemonway_proxy.port' => false,
        'lemonway_proxy.user' => false,
        'lemonway_proxy.password' => false,
        'lemonway_bankwire.iban' => false,
        'lemonway_bankwire.bic' => false,
        'lemonway_bankwire.holder_name' => false,
        'lemonway_bankwire.holder_address' => false,
        'dolist.account_id' => false,
        'dolist.authentication_key' => false,
        'dolist.debug' => false,
        'chronopost.api.account_number' => false,
        'chronopost.api.password' => false,
        'mondial_relay.api.userid' => false,
        'mondial_relay.api.password' => false,
        'mondial_relay.api.endpoint' => false,
        'entrypoint.marketplace' => false,
        'azure.protocol' => false,
        'azure.local_endpoints' => false,
        'marketplace.invoice.template_url' => false,
        'marketplace.invoice.header.template_url' => false,
        'marketplace.invoice.footer.template_url' => false,
        'marketplace.invoice.options.margin.top' => false,
        'marketplace.invoice.options.margin.bottom' => false,
        'marketplace.rma.template_url' => false,
        'marketplace.rma.header.template_url' => false,
        'marketplace.rma.footer.template_url' => false,
        'marketplace.rma.options.margin.top' => false,
        'marketplace.rma.options.margin.bottom' => false,
        'yavin_messenger_vhost_test' => false,
        'yavin_api_v2_jwt' => false,
        'session.save_path' => false,
        'validator.mapping.cache.file' => false,
        'serializer.mapping.cache.file' => false,
        'doctrine.orm.proxy_dir' => false,
    ];
    private $dynamicParameters = [];

    private function getDynamicParameter(string $name)
    {
        switch ($name) {
            case 'kernel.cache_dir': $value = $this->targetDir.''; break;
            case 'okta_authorization_server': $value = $this->getEnv('OKTA_AUTHORIZATION_SERVER'); break;
            case 'okta_client_id': $value = $this->getEnv('OKTA_CLIENT_ID'); break;
            case 'okta_client_secret': $value = $this->getEnv('OKTA_CLIENT_SECRET'); break;
            case 'okta_redirect_uri': $value = $this->getEnv('OKTA_REDIRECT_URI'); break;
            case 'okta_admin_group': $value = $this->getEnv('OKTA_ADMIN_GROUP'); break;
            case 'okta_vendor_group': $value = $this->getEnv('OKTA_VENDOR_GROUP'); break;
            case 'okta_response_type': $value = $this->getEnv('OKTA_RESPONSE_TYPE'); break;
            case 'okta_bo_authorization_server': $value = $this->getEnv('OKTA_BO_AUTHORIZATION_SERVER'); break;
            case 'okta_bo_client_id': $value = $this->getEnv('OKTA_BO_CLIENT_ID'); break;
            case 'okta_bo_client_secret': $value = $this->getEnv('OKTA_BO_CLIENT_SECRET'); break;
            case 'okta_bo_redirect_uri': $value = $this->getEnv('OKTA_BO_REDIRECT_URI'); break;
            case 'okta_bo_admin_group': $value = $this->getEnv('OKTA_BO_ADMIN_GROUP'); break;
            case 'okta_bo_vendor_group': $value = $this->getEnv('OKTA_BO_VENDOR_GROUP'); break;
            case 'okta_bo_response_type': $value = $this->getEnv('OKTA_BO_RESPONSE_TYPE'); break;
            case 'openid_discovery_uri': $value = $this->getEnv('OPENID_DISCOVERY_URI'); break;
            case 'openid_client_id': $value = $this->getEnv('OPENID_CLIENT_ID'); break;
            case 'openid_client_secret': $value = $this->getEnv('OPENID_CLIENT_SECRET'); break;
            case 'openid_redirect_uri': $value = $this->getEnv('OPENID_REDIRECT_URI'); break;
            case 'openid_default_user_type': $value = $this->getEnv('OPENID_DEFAULT_USER_TYPE'); break;
            case 'openid_bo_discovery_uri': $value = $this->getEnv('OPENID_BO_DISCOVERY_URI'); break;
            case 'openid_bo_client_id': $value = $this->getEnv('OPENID_BO_CLIENT_ID'); break;
            case 'openid_bo_client_secret': $value = $this->getEnv('OPENID_BO_CLIENT_SECRET'); break;
            case 'openid_bo_redirect_uri': $value = $this->getEnv('OPENID_BO_REDIRECT_URI'); break;
            case 'openid_bo_default_user_type': $value = $this->getEnv('OPENID_BO_DEFAULT_USER_TYPE'); break;
            case 'azure_ad_discovery_uri': $value = $this->getEnv('AZURE_AD_DISCOVERY_URI'); break;
            case 'azure_ad_default_user_type': $value = $this->getEnv('AZURE_AD_DEFAULT_USER_TYPE'); break;
            case 'azure_ad_client_id': $value = $this->getEnv('AZURE_AD_CLIENT_ID'); break;
            case 'azure_ad_response_type': $value = $this->getEnv('AZURE_AD_RESPONSE_TYPE'); break;
            case 'azure_ad_client_secret': $value = $this->getEnv('AZURE_AD_CLIENT_SECRET'); break;
            case 'azure_ad_redirect_uri': $value = $this->getEnv('AZURE_AD_REDIRECT_URI'); break;
            case 'azure_ad_bo_discovery_uri': $value = $this->getEnv('AZURE_AD_BO_DISCOVERY_URI'); break;
            case 'azure_ad_bo_default_user_type': $value = $this->getEnv('AZURE_AD_BO_DEFAULT_USER_TYPE'); break;
            case 'azure_ad_bo_client_id': $value = $this->getEnv('AZURE_AD_BO_CLIENT_ID'); break;
            case 'azure_ad_bo_response_type': $value = $this->getEnv('AZURE_AD_BO_RESPONSE_TYPE'); break;
            case 'azure_ad_bo_client_secret': $value = $this->getEnv('AZURE_AD_BO_CLIENT_SECRET'); break;
            case 'azure_ad_bo_redirect_uri': $value = $this->getEnv('AZURE_AD_BO_REDIRECT_URI'); break;
            case 'google_oauth_redirect_uri': $value = $this->getEnv('GOOGLE_OAUTH_REDIRECT_URI'); break;
            case 'somfy_authorization_server': $value = $this->getEnv('SOMFY_AUTHORIZATION_SERVER'); break;
            case 'feature.carrier.mondial_relay': $value = $this->getEnv('bool:ENABLE_CARRIER_MONDIAL_RELAY'); break;
            case 'feature.carrier.chronopost.chronorelais': $value = $this->getEnv('bool:ENABLE_CARRIER_CHRONOPOST_CHRONORELAIS'); break;
            case 'feature.carrier.chronopost.chrono13': $value = $this->getEnv('bool:ENABLE_CARRIER_CHRONOPOST_CHRONO13'); break;
            case 'kernel.secret': $value = $this->getEnv('CSCART_CRYPT_KEY'); break;
            case 'request_id.header_name': $value = $this->getEnv('REQUEST_ID_HEADER_NAME'); break;
            case 'aws.transcoding.region': $value = $this->getEnv('AWS_TRANSCODING_REGION'); break;
            case 'redis_host': $value = $this->getEnv('REDIS_HOST'); break;
            case 'redis.db': $value = $this->getEnv('REDIS_DB'); break;
            case 'mangopay_api.client_id': $value = $this->getEnv('MANGOPAY_API_CLIENT_ID'); break;
            case 'mangopay_api.client_password': $value = $this->getEnv('MANGOPAY_API_CLIENT_PASSWORD'); break;
            case 'mangopay_api.base_url': $value = $this->getEnv('MANGOPAY_API_BASE_URL'); break;
            case 'mangopay_api.secure_mode': $value = $this->getEnv('MANGOPAY_API_SECURE_MODE'); break;
            case 'mangopay_user_id_agent_etablissement_paiement': $value = $this->getEnv('MANGOPAY_USER_ID_AGENT_ETABLISSEMENT_PAIEMENT'); break;
            case 'stripe.secret_token': $value = $this->getEnv('STRIPE_SECRET_TOKEN'); break;
            case 'stripe.public_token': $value = $this->getEnv('STRIPE_PUBLIC_TOKEN'); break;
            case 'smoney.token': $value = $this->getEnv('SMONEY_TOKEN'); break;
            case 'smoney.api_base_url': $value = $this->getEnv('SMONEY_API_BASE_URL'); break;
            case 'smoney.website_name': $value = $this->getEnv('SMONEY_WEBSITE_NAME'); break;
            case 'lemonway_api.client_login': $value = $this->getEnv('LEMONWAY_API_CLIENT_LOGIN'); break;
            case 'lemonway_api.client_password': $value = $this->getEnv('LEMONWAY_API_CLIENT_PASSWORD'); break;
            case 'lemonway_api.directkit_url': $value = $this->getEnv('LEMONWAY_API_DIRECTKIT_URL'); break;
            case 'lemonway_api.webkit_url': $value = $this->getEnv('LEMONWAY_API_WEBKIT_URL'); break;
            case 'lemonway_api.marketplace_id': $value = $this->getEnv('LEMONWAY_API_MARKETPLACE_ID'); break;
            case 'lemonway_api.use_buyer_wallet_for_moneyin': $value = $this->getEnv('bool:LEMONWAY_API_USE_BUYER_WALLET_FOR_MONEYIN'); break;
            case 'lemonway_api.tech_wallet_id': $value = $this->getEnv('LEMONWAY_API_TECH_WALLET_ID'); break;
            case 'lemonway_api.marketplace_discount_wallet': $value = $this->getEnv('LEMONWAY_API_MARKETPLACE_DISCOUNT_WALLET'); break;
            case 'lemonway_proxy.host': $value = $this->getEnv('LEMONWAY_PROXY_HOST'); break;
            case 'lemonway_proxy.port': $value = $this->getEnv('int:LEMONWAY_PROXY_PORT'); break;
            case 'lemonway_proxy.user': $value = $this->getEnv('LEMONWAY_PROXY_USER'); break;
            case 'lemonway_proxy.password': $value = $this->getEnv('LEMONWAY_PROXY_PASSWORD'); break;
            case 'lemonway_bankwire.iban': $value = $this->getEnv('LEMONWAY_BANKWIRE_IBAN'); break;
            case 'lemonway_bankwire.bic': $value = $this->getEnv('LEMONWAY_BANKWIRE_BIC'); break;
            case 'lemonway_bankwire.holder_name': $value = $this->getEnv('LEMONWAY_BANKWIRE_HOLDER_NAME'); break;
            case 'lemonway_bankwire.holder_address': $value = $this->getEnv('LEMONWAY_BANKWIRE_HOLDER_ADDRESS'); break;
            case 'dolist.account_id': $value = $this->getEnv('DOLIST_ACCOUNT_ID'); break;
            case 'dolist.authentication_key': $value = $this->getEnv('DOLIST_AUTHENTICATION_KEY'); break;
            case 'dolist.debug': $value = $this->getEnv('bool:DOLIST_DEBUG'); break;
            case 'chronopost.api.account_number': $value = $this->getEnv('CHRONOPOST_API_ACCOUNT_NUMBER'); break;
            case 'chronopost.api.password': $value = $this->getEnv('CHRONOPOST_API_PASSWORD'); break;
            case 'mondial_relay.api.userid': $value = $this->getEnv('MONDIAL_RELAY_API_USERID'); break;
            case 'mondial_relay.api.password': $value = $this->getEnv('MONDIAL_RELAY_API_PASSWORD'); break;
            case 'mondial_relay.api.endpoint': $value = $this->getEnv('MONDIAL_RELAY_API_ENDPOINT'); break;
            case 'entrypoint.marketplace': $value = $this->getEnv('ENTRYPOINT_MARKETPLACE'); break;
            case 'azure.protocol': $value = $this->getEnv('AZURE_PROTOCOL'); break;
            case 'azure.local_endpoints': $value = $this->getEnv('AZURE_LOCAL_ENDPOINTS'); break;
            case 'marketplace.invoice.template_url': $value = $this->getEnv('INVOICE_TEMPLATE_URL'); break;
            case 'marketplace.invoice.header.template_url': $value = $this->getEnv('INVOICE_TEMPLATE_HEADER_URL'); break;
            case 'marketplace.invoice.footer.template_url': $value = $this->getEnv('INVOICE_TEMPLATE_FOOTER_URL'); break;
            case 'marketplace.invoice.options.margin.top': $value = $this->getEnv('INVOICE_TEMPLATE_OPTIONS_MARGIN_TOP'); break;
            case 'marketplace.invoice.options.margin.bottom': $value = $this->getEnv('INVOICE_TEMPLATE_OPTIONS_MARGIN_BOTTOM'); break;
            case 'marketplace.rma.template_url': $value = $this->getEnv('RMA_TEMPLATE_URL'); break;
            case 'marketplace.rma.header.template_url': $value = $this->getEnv('RMA_TEMPLATE_HEADER_URL'); break;
            case 'marketplace.rma.footer.template_url': $value = $this->getEnv('RMA_TEMPLATE_FOOTER_URL'); break;
            case 'marketplace.rma.options.margin.top': $value = $this->getEnv('RMA_TEMPLATE_OPTIONS_MARGIN_TOP'); break;
            case 'marketplace.rma.options.margin.bottom': $value = $this->getEnv('RMA_TEMPLATE_OPTIONS_MARGIN_BOTTOM'); break;
            case 'yavin_messenger_vhost_test': $value = $this->getEnv('YAVIN_MESSENGER_VHOST_TEST'); break;
            case 'yavin_api_v2_jwt': $value = $this->getEnv('YAVIN_API_V2_JWT'); break;
            case 'session.save_path': $value = ($this->targetDir.''.'/sessions'); break;
            case 'validator.mapping.cache.file': $value = ($this->targetDir.''.'/validation.php'); break;
            case 'serializer.mapping.cache.file': $value = ($this->targetDir.''.'/serialization.php'); break;
            case 'doctrine.orm.proxy_dir': $value = ($this->targetDir.''.'/doctrine/orm/Proxies'); break;
            default: throw new InvalidArgumentException(sprintf('The dynamic parameter "%s" must be defined.', $name));
        }
        $this->loadedDynamicParameters[$name] = true;

        return $this->dynamicParameters[$name] = $value;
    }

    protected function getDefaultParameters(): array
    {
        return [
            'kernel.root_dir' => (\dirname(__DIR__, 4).'/app'),
            'kernel.project_dir' => \dirname(__DIR__, 4),
            'kernel.environment' => 'prod',
            'kernel.debug' => false,
            'kernel.name' => 'app',
            'kernel.logs_dir' => (\dirname(__DIR__, 3).'/logs'),
            'kernel.bundles' => [
                'FrameworkBundle' => 'Symfony\\Bundle\\FrameworkBundle\\FrameworkBundle',
                'SecurityBundle' => 'Symfony\\Bundle\\SecurityBundle\\SecurityBundle',
                'BroadwayBundle' => 'Broadway\\Bundle\\BroadwayBundle\\BroadwayBundle',
                'DoctrineBundle' => 'Doctrine\\Bundle\\DoctrineBundle\\DoctrineBundle',
                'MonologBundle' => 'Symfony\\Bundle\\MonologBundle\\MonologBundle',
                'SncRedisBundle' => 'Snc\\RedisBundle\\SncRedisBundle',
                'BroadwaySerializationBundle' => 'BroadwaySerialization\\SymfonyIntegration\\BroadwaySerializationBundle',
                'TwigBundle' => 'Symfony\\Bundle\\TwigBundle\\TwigBundle',
                'KnpSnappyBundle' => 'Knp\\Bundle\\SnappyBundle\\KnpSnappyBundle',
                'FOSJsRoutingBundle' => 'FOS\\JsRoutingBundle\\FOSJsRoutingBundle',
                'SwiftmailerBundle' => 'Symfony\\Bundle\\SwiftmailerBundle\\SwiftmailerBundle',
                'NelmioCorsBundle' => 'Nelmio\\CorsBundle\\NelmioCorsBundle',
                'AppBundle' => 'Wizacha\\AppBundle\\AppBundle',
                'ExerciseHTMLPurifierBundle' => 'Exercise\\HTMLPurifierBundle\\ExerciseHTMLPurifierBundle',
                'RemotefrontBundle' => 'Wizacha\\RemotefrontBundle\\RemotefrontBundle',
            ],
            'kernel.bundles_metadata' => [
                'FrameworkBundle' => [
                    'path' => (\dirname(__DIR__, 4).'/vendor/symfony/symfony/src/Symfony/Bundle/FrameworkBundle'),
                    'namespace' => 'Symfony\\Bundle\\FrameworkBundle',
                ],
                'SecurityBundle' => [
                    'path' => (\dirname(__DIR__, 4).'/vendor/symfony/symfony/src/Symfony/Bundle/SecurityBundle'),
                    'namespace' => 'Symfony\\Bundle\\SecurityBundle',
                ],
                'BroadwayBundle' => [
                    'path' => (\dirname(__DIR__, 4).'/vendor/broadway/broadway/src/Broadway/Bundle/BroadwayBundle'),
                    'namespace' => 'Broadway\\Bundle\\BroadwayBundle',
                ],
                'DoctrineBundle' => [
                    'path' => (\dirname(__DIR__, 4).'/vendor/doctrine/doctrine-bundle'),
                    'namespace' => 'Doctrine\\Bundle\\DoctrineBundle',
                ],
                'MonologBundle' => [
                    'path' => (\dirname(__DIR__, 4).'/vendor/symfony/monolog-bundle'),
                    'namespace' => 'Symfony\\Bundle\\MonologBundle',
                ],
                'SncRedisBundle' => [
                    'path' => (\dirname(__DIR__, 4).'/vendor/snc/redis-bundle'),
                    'namespace' => 'Snc\\RedisBundle',
                ],
                'BroadwaySerializationBundle' => [
                    'path' => (\dirname(__DIR__, 4).'/vendor/matthiasnoback/broadway-serialization/src/SymfonyIntegration'),
                    'namespace' => 'BroadwaySerialization\\SymfonyIntegration',
                ],
                'TwigBundle' => [
                    'path' => (\dirname(__DIR__, 4).'/vendor/symfony/symfony/src/Symfony/Bundle/TwigBundle'),
                    'namespace' => 'Symfony\\Bundle\\TwigBundle',
                ],
                'KnpSnappyBundle' => [
                    'path' => (\dirname(__DIR__, 4).'/vendor/knplabs/knp-snappy-bundle/src'),
                    'namespace' => 'Knp\\Bundle\\SnappyBundle',
                ],
                'FOSJsRoutingBundle' => [
                    'path' => (\dirname(__DIR__, 4).'/vendor/friendsofsymfony/jsrouting-bundle'),
                    'namespace' => 'FOS\\JsRoutingBundle',
                ],
                'SwiftmailerBundle' => [
                    'path' => (\dirname(__DIR__, 4).'/vendor/symfony/swiftmailer-bundle'),
                    'namespace' => 'Symfony\\Bundle\\SwiftmailerBundle',
                ],
                'NelmioCorsBundle' => [
                    'path' => (\dirname(__DIR__, 4).'/vendor/nelmio/cors-bundle'),
                    'namespace' => 'Nelmio\\CorsBundle',
                ],
                'AppBundle' => [
                    'path' => (\dirname(__DIR__, 4).'/src/AppBundle'),
                    'namespace' => 'Wizacha\\AppBundle',
                ],
                'ExerciseHTMLPurifierBundle' => [
                    'path' => (\dirname(__DIR__, 4).'/vendor/exercise/htmlpurifier-bundle/src'),
                    'namespace' => 'Exercise\\HTMLPurifierBundle',
                ],
                'RemotefrontBundle' => [
                    'path' => (\dirname(__DIR__, 4).'/src/RemotefrontBundle'),
                    'namespace' => 'Wizacha\\RemotefrontBundle',
                ],
            ],
            'kernel.charset' => 'UTF-8',
            'kernel.container_class' => 'appAppKernelProdContainer',
            'env(ACTIVATE_BILLING_NUMBER_AUTO_GENERATOR)' => false,
            'env(ACTIVATE_WORKFLOW_TRANSLATION)' => false,
            'env(ADD_FULL_SHIPPING_TO_COMMISSION)' => false,
            'env(ALGOLIA_API_IDENTIFIER)' => '',
            'env(ALGOLIA_API_INDEX_PREFIX)' => '',
            'env(ALGOLIA_API_KEY_FULL_RIGHTS)' => '',
            'env(ALGOLIA_API_KEY_LIMITED_RIGHTS)' => '',
            'env(ALGOLIA_CONFIG_MAX_VALUES_PER_FACET)' => 10,
            'env(ALGOLIA_FPU_MAX_FP)' => 300,
            'env(ALGOLIA_GEOCODING_API_IDENTIFIER)' => '',
            'env(ALGOLIA_GEOCODING_API_KEY_LIMITED_RIGHTS)' => '',
            'env(ALLOW_MVP_SYNC_FROM_PRODUCTS)' => false,
            'env(AMQP_HOST)' => '',
            'env(AMQP_PASS)' => '',
            'env(AMQP_PORT)' => '',
            'env(AMQP_USER)' => '',
            'env(AMQP_VHOST)' => '/',
            'env(API_APPLICATION_TOKEN)' => NULL,
            'env(API_SEARCH_APPLICATION_TOKEN_REQUIRED)' => 0,
            'env(APP_LOCALE)' => 'fr',
            'env(APPROVE_USER_BY_ADMIN)' => false,
            'env(ARE_CLIENTS_PROFESSIONALS)' => false,
            'env(AVAILABLE_OFFERS)' => '0',
            'env(AWS_ACCESS_KEY_ID)' => '',
            'env(AWS_ACCESS_KEY_SECRET)' => '',
            'env(AWS_BUCKET)' => 'wizachatest',
            'env(AWS_REGION)' => 'eu-west-1',
            'env(AWS_SQS_ACCESS_KEY_ID)' => '',
            'env(AWS_SQS_ACCESS_KEY_SECRET)' => '',
            'env(AWS_SQS_REGION)' => 'eu-west-1',
            'env(AWS_TRANSCODING_REGION)' => 'eu-west-1',
            'env(AZURE_ACCOUNT_KEY)' => '',
            'env(AZURE_ACCOUNT_NAME)' => '',
            'env(AZURE_AD_BO_CLIENT_ID)' => '',
            'env(AZURE_AD_BO_CLIENT_SECRET)' => '',
            'env(AZURE_AD_BO_DEFAULT_USER_TYPE)' => 'A',
            'env(AZURE_AD_BO_DISCOVERY_URI)' => '',
            'env(AZURE_AD_BO_REDIRECT_URI)' => 'https://back.marketplace.ovhcloud.com/admin.php/admin/oauth/authorize',
            'env(AZURE_AD_BO_RESPONSE_TYPE)' => 'code',
            'env(AZURE_AD_CLIENT_ID)' => '',
            'env(AZURE_AD_CLIENT_SECRET)' => '',
            'env(AZURE_AD_DEFAULT_USER_TYPE)' => 'C',
            'env(AZURE_AD_DISCOVERY_URI)' => '',
            'env(AZURE_AD_REDIRECT_URI)' => '',
            'env(AZURE_AD_RESPONSE_TYPE)' => 'code',
            'env(AZURE_LOCAL_ENDPOINTS)' => '',
            'env(AZURE_PROTOCOL)' => 'https',
            'env(BFO_URL)' => '',
            'env(CACHE_BACKEND)' => 'file',
            'env(CATALOG_SHOW_OUT_OF_STOCK_PRODUCTS)' => 0,
            'env(CHRONOPOST_API_ACCOUNT_NUMBER)' => '',
            'env(CHRONOPOST_API_PASSWORD)' => '',
            'env(CLEAR_CACHE_AUTH_DELAY_MONTH)' => '3',
            'env(CLICK_AND_COLLECT_SHIPPING_ID)' => 0,
            'env(CLIENT_TRUSTED_PROXIES)' => '',
            'env(CLOUDIMAGE)' => 0,
            'env(CLOUDIMAGE_API_URL)' => 'https://api.cloudimage.com',
            'env(CLOUDIMAGE_CLIENT_KEY)' => '',
            'env(CLOUDIMAGE_VERSION_HEAD)' => '',
            'env(CONFIG_ENABLE_AUTO_VALIDATE_PRODUCT)' => false,
            'env(CONFIG_ENABLED_SYSTEM_OPTIONS)' => '',
            'env(CONFIG_KPI_WEBHOOK_URL)' => '',
            'env(CONFIG_RELATED_PRODUCTS_TYPES)' => 'recommended',
            'env(CREATE_LEGAL_WALLETS_FOR_CUSTOMERS)' => false,
            'env(CSCART_CRYPT_KEY)' => '9cad92f60417cf8021d4e37103a8494b',
            'env(CURRENCY_CODE)' => 'EUR',
            'env(CURRENCY_FIXER_API_KEY)' => '',
            'env(CURRENCY_FIXER_BASE_URL)' => '',
            'env(CURRENCY_RATES_PROVIDER)' => 'fixer',
            'env(CURRENCY_SIGN)' => '€',
            'env(CURRENT_OAUTH_PROVIDER)' => 'okta',
            'env(DATABASE_HOST)' => '127.0.0.1',
            'env(DATABASE_NAME)' => 'wizaplace',
            'env(DATABASE_PASSWORD)' => NULL,
            'env(DATABASE_PORT)' => 3306,
            'env(DATABASE_USER)' => 'root',
            'env(DELIVERY_PERIOD)' => 7,
            'env(DISCUSS_DBNAME)' => 'wizaplace_discuss',
            'env(DISCUSS_DRIVER)' => 'pdo_mysql',
            'env(DISCUSS_HOST)' => '127.0.0.1',
            'env(DISCUSS_PASSWORD)' => 'wizaplace',
            'env(DISCUSS_PORT)' => 3306,
            'env(DISCUSS_USER)' => 'wizaplace',
            'env(DISCUSSIONS_ALLOWED_FOR_ADMINISTRATORS)' => 0,
            'env(DOLIST_ACCOUNT_ID)' => '0',
            'env(DOLIST_AUTHENTICATION_KEY)' => '',
            'env(DOLIST_DEBUG)' => true,
            'env(DUMP_ANONYMIZER_OUTPUT_S3_BUCKET)' => '',
            'env(DUMP_ANONYMIZER_OUTPUT_S3_DIRECTORY)' => 'dump_anonymizer',
            'env(DUMP_ANONYMIZER_OUTPUT_S3_KEY)' => '',
            'env(DUMP_ANONYMIZER_OUTPUT_S3_REGION)' => 'eu-west-1',
            'env(DUMP_ANONYMIZER_OUTPUT_S3_SECRET)' => '',
            'env(ENABLE_AFFILIATE)' => false,
            'env(ENABLE_AUTOMATED_FEEDS)' => 1,
            'env(ENABLE_BFO)' => 0,
            'env(ENABLE_C2C)' => true,
            'env(ENABLE_CARRIER_CHRONOPOST_CHRONO13)' => false,
            'env(ENABLE_CARRIER_CHRONOPOST_CHRONORELAIS)' => false,
            'env(ENABLE_CARRIER_MONDIAL_RELAY)' => false,
            'env(ENABLE_DATABASE_LIMIT)' => 0,
            'env(ENABLE_DATABASE_LIMIT_SIZE)' => 2000,
            'env(ENABLE_GREEN_TAXE)' => '1',
            'env(ENABLE_MVP)' => false,
            'env(ENABLE_POST_DEPLOY_UPDATE_ALGOLIA)' => 1,
            'env(ENABLE_PRODUCT_ATTACHMENTS)' => false,
            'env(ENABLE_YAVIN)' => '0',
            'env(ENTRYPOINT_ADMINISTRATOR)' => 'admin.php',
            'env(ENTRYPOINT_MARKETPLACE)' => 'index.php',
            'env(ENTRYPOINT_MARKETPLACE_DOMAIN)' => '',
            'env(ENTRYPOINT_VENDOR)' => 'vendor.php',
            'env(ENTRYPOINT_VENDOR_DOMAIN)' => '',
            'env(EXTERNAL_BACKOFFICE_URL)' => '',
            'env(EXTERNAL_FRONT_OFFICE_URL)' => '',
            'env(FEATURE_CACHE_HTTP)' => false,
            'env(FEATURE_CURRENCY_ADVANCED)' => '0',
            'env(FEATURE_ENABLE_DIVISIONS)' => '0',
            'env(FEATURE_ENABLE_IMAGE_RESIZING)' => 1,
            'env(FEATURE_IBAN_VALIDATION)' => 0,
            'env(FEATURE_MARKETPLACE_ONLY_SELL_SERVICES)' => false,
            'env(FEATURE_SECURITY_CAPTCHA)' => 0,
            'env(FEATURE_SHOW_ZERO_PRICE_PRODUCTS)' => 0,
            'env(FEATURE_SUBSCRIPTION_LIMIT_RENEW_ATTEMPTS)' => 1,
            'env(FREQUENCY_CRON_PAYOUT)' => 'bimonthly',
            'env(FRONTEND_CALLBACK_WHITELIST)' => '*',
            'env(FRONTEND_THEME)' => 'remotefront',
            'env(GOOGLE_ANALYTICS_TRACKING_ID)' => '',
            'env(GOOGLE_OAUTH_ID)' => '',
            'env(GOOGLE_OAUTH_REDIRECT_URI)' => 'http://wizaplace.loc/oauth',
            'env(GOOGLE_OAUTH_REDIRECT_URL)' => 'https://auth.wizntw.com/redirect.php',
            'env(GOOGLE_OAUTH_SECRET)' => '',
            'env(GOOGLEMAPS_API_KEY)' => 'AIzaSyAH25S8aFeVbcVOtv1hwkOS-rpeKCMtPF0',
            'env(HADES_AWS_BUCKET)' => '',
            'env(HADES_AWS_KEY)' => '',
            'env(HADES_AWS_REGION)' => 'eu-west-1',
            'env(HADES_AWS_SECRET)' => '',
            'env(HIDE_CLIENT_EMAIL_FROM_VENDOR)' => false,
            'env(HIPAY_CASHIN_API_PASSWORD)' => 'password',
            'env(HIPAY_CASHIN_API_USERNAME)' => 'wizaplace',
            'env(HIPAY_CASHIN_ENV)' => NULL,
            'env(HIPAY_CASHOUT_API_LOGIN)' => '',
            'env(HIPAY_CASHOUT_API_PASSWORD)' => '',
            'env(HIPAY_CASHOUT_API_URI)' => '',
            'env(HIPAY_CASHOUT_ENTITY)' => '',
            'env(HIPAY_CASHOUT_MERCHANT_GROUP_ID)' => 0,
            'env(HIPAY_CHARGEBACK_TEST_ENV)' => '',
            'env(HIPAY_COMMISSION_RECIPIENT_ID)' => NULL,
            'env(HIPAY_PAYMENT_PRODUCT_LIST)' => 'visa,mastercard,cb',
            'env(HTTP_HOST)' => 'localhost',
            'env(HTTP_PATH)' => '',
            'env(INACTIVITY_TIME_OUT)' => 1209600,
            'env(INCLUDE_SHIPPING_IN_COMMISSION)' => false,
            'env(INVOICE_TEMPLATE_FOOTER_URL)' => '',
            'env(INVOICE_TEMPLATE_HEADER_URL)' => '',
            'env(INVOICE_TEMPLATE_OPTIONS_MARGIN_BOTTOM)' => '',
            'env(INVOICE_TEMPLATE_OPTIONS_MARGIN_TOP)' => '',
            'env(INVOICE_TEMPLATE_URL)' => '',
            'env(IS_GLOBAL_OPTION_ALLOWED_FOR_VENDORS)' => false,
            'env(LEMONWAY_API_CLIENT_LOGIN)' => '',
            'env(LEMONWAY_API_CLIENT_PASSWORD)' => '',
            'env(LEMONWAY_API_DIRECTKIT_URL)' => '',
            'env(LEMONWAY_API_MARKETPLACE_DISCOUNT_WALLET)' => 'SUBVENTIONS',
            'env(LEMONWAY_API_MARKETPLACE_ID)' => '',
            'env(LEMONWAY_API_TECH_WALLET_ID)' => 'MKP',
            'env(LEMONWAY_API_USE_BUYER_WALLET_FOR_MONEYIN)' => false,
            'env(LEMONWAY_API_WEBKIT_URL)' => '',
            'env(LEMONWAY_BANKWIRE_BIC)' => '',
            'env(LEMONWAY_BANKWIRE_HOLDER_ADDRESS)' => '',
            'env(LEMONWAY_BANKWIRE_HOLDER_NAME)' => '',
            'env(LEMONWAY_BANKWIRE_IBAN)' => '',
            'env(LEMONWAY_PROXY_HOST)' => '',
            'env(LEMONWAY_PROXY_PASSWORD)' => '',
            'env(LEMONWAY_PROXY_PORT)' => 3128,
            'env(LEMONWAY_PROXY_USER)' => '',
            'env(MAILER_DELIVERY_ADDRESS)' => NULL,
            'env(MAILER_ENCRYPTION)' => NULL,
            'env(MAILER_HOST)' => NULL,
            'env(MAILER_PASSWORD)' => NULL,
            'env(MAILER_PORT)' => NULL,
            'env(MAILER_TRANSPORT)' => NULL,
            'env(MAILER_USER)' => NULL,
            'env(MANGOPAY_API_BASE_URL)' => '',
            'env(MANGOPAY_API_CLIENT_ID)' => '',
            'env(MANGOPAY_API_CLIENT_PASSWORD)' => '',
            'env(MANGOPAY_API_SECURE_MODE)' => 'FORCE',
            'env(MANGOPAY_USER_ID_AGENT_ETABLISSEMENT_PAIEMENT)' => NULL,
            'env(MARKETPLACE_DISCOUNT_OPTIONAL_BONUSES)' => 0,
            'env(MARKETPLACE_DISCOUNTS)' => '0',
            'env(MARKETPLACE_SEARCH_FORCE_SQL)' => false,
            'env(MARKETPLACE_STOCK_CART_TIME_LIMIT)' => 900,
            'env(MARKETPLACE_VERSION)' => 'dev-master',
            'env(MERCHANTS_REFUND_ABILITY)' => '0',
            'env(MONDIAL_RELAY_API_ENDPOINT)' => 'https://api.mondialrelay.com/Web_Services.asmx?WSDL',
            'env(MONDIAL_RELAY_API_PASSWORD)' => '',
            'env(MONDIAL_RELAY_API_USERID)' => '',
            'env(MONOLOG_LOG_LEVEL_MAILER)' => '',
            'env(MONOLOG_LOG_LEVEL_SENTINEL)' => 'error',
            'env(MULTI_VENDOR_PRODUCT_RULES)' => 'ean',
            'env(MVP_AUTO_CREATE)' => false,
            'env(MYSQL_VERSION)' => 5.6,
            'env(NOTIFY_SHIPMENT_CREATED)' => '1',
            'env(NOTIFY_USER_UPDATE)' => false,
            'env(OKTA_ADMIN_GROUP)' => 'admin',
            'env(OKTA_AUTHORIZATION_SERVER)' => '',
            'env(OKTA_BO_ADMIN_GROUP)' => 'admin',
            'env(OKTA_BO_AUTHORIZATION_SERVER)' => '',
            'env(OKTA_BO_CLIENT_ID)' => '',
            'env(OKTA_BO_CLIENT_SECRET)' => '',
            'env(OKTA_BO_REDIRECT_URI)' => 'https://back.marketplace.ovhcloud.com/admin.php/admin/oauth/authorize',
            'env(OKTA_BO_RESPONSE_TYPE)' => 'code',
            'env(OKTA_BO_VENDOR_GROUP)' => 'vendor',
            'env(OKTA_CLIENT_ID)' => '',
            'env(OKTA_CLIENT_SECRET)' => '',
            'env(OKTA_REDIRECT_URI)' => 'http://wizaplace.loc/oauth',
            'env(OKTA_RESPONSE_TYPE)' => 'id_token',
            'env(OKTA_VENDOR_GROUP)' => 'vendor',
            'env(OPENID_BO_CLIENT_ID)' => '',
            'env(OPENID_BO_CLIENT_SECRET)' => '',
            'env(OPENID_BO_DEFAULT_USER_TYPE)' => NULL,
            'env(OPENID_BO_DISCOVERY_URI)' => '',
            'env(OPENID_BO_REDIRECT_URI)' => 'https://back.marketplace.ovhcloud.com/admin.php/admin/oauth/authorize',
            'env(OPENID_CLIENT_ID)' => '',
            'env(OPENID_CLIENT_SECRET)' => '',
            'env(OPENID_DEFAULT_USER_TYPE)' => NULL,
            'env(OPENID_DISCOVERY_URI)' => '',
            'env(OPENID_REDIRECT_URI)' => 'http://wizaplace.loc/oauth',
            'env(ORDER_ADJUSTMENT)' => '0',
            'env(ORGANISATIONS_ENABLED)' => false,
            'env(OVH_S3_API_URL)' => 'http://null.com',
            'env(OVH_S3_CREDENTIALS_KEY)' => '',
            'env(OVH_S3_CREDENTIALS_SECRET)' => '',
            'env(OVH_S3_PRIVATE_BUCKET)' => '',
            'env(OVH_S3_PUBLIC_BUCKET)' => '',
            'env(OVH_S3_PUBLIC_URL_TOKEN)' => '',
            'env(OVH_S3_REGION)' => 'GRA',
            'env(PASSWORD_RECOVERY_FORCE_CHANGE)' => 0,
            'env(PASSWORD_RENEWAL_TIME_LIMIT)' => 0,
            'env(PAYMENT_DEADLINE)' => 0,
            'env(PLATFORM_TECHNICAL_NAME)' => 'wizaplace-local-dev',
            'env(PREDIGGO_ENABLE_EXPORTER)' => false,
            'env(PREVIOUS_PASSWORDS_DIFFERENCE_LIMIT)' => 0,
            'env(PRISMIC_CONTENT)' => '',
            'env(PRODUCT_TEMPLATES)' => 'product',
            'env(PROJECT_NAME)' => 'wizaplace.loc',
            'env(QUEUE_KEY)' => '',
            'env(QUEUE_PATH)' => 'var/queues',
            'env(QUEUE_REGION)' => '',
            'env(QUEUE_SECRET)' => '',
            'env(QUEUE_TYPE)' => '',
            'env(QUOTE_REQUESTS_ENABLED)' => 0,
            'env(REDIS_DB)' => '0',
            'env(REDIS_HOST)' => 'localhost',
            'env(REFUND_AUTO)' => 'NONE',
            'env(REQUEST_ID_HEADER_NAME)' => 'X-Request-Id',
            'env(RESOURCES_PURGE_PERIOD_IMAGES)' => 100,
            'env(RMA_TEMPLATE_FOOTER_URL)' => '',
            'env(RMA_TEMPLATE_HEADER_URL)' => '',
            'env(RMA_TEMPLATE_OPTIONS_MARGIN_BOTTOM)' => '',
            'env(RMA_TEMPLATE_OPTIONS_MARGIN_TOP)' => '',
            'env(RMA_TEMPLATE_URL)' => '',
            'env(SANDBOX)' => false,
            'env(SECURITY_ENABLE_STRICT_PASSWORD)' => 0,
            'env(SECURITY_LOCK_EMAIL_API)' => 0,
            'env(SECURITY_LOCK_IP_ADDRESS)' => 0,
            'env(SECURITY_LOCK_IP_ADDRESS_DURATION)' => 43200,
            'env(SECURITY_LOCK_IP_ADDRESS_FAILED_LIMIT)' => 100,
            'env(SECURITY_LOCK_USER_ACCOUNTS)' => 0,
            'env(SECURITY_LOCK_USER_ACCOUNTS_FAILED_LIMIT)' => 20,
            'env(SECURITY_REQUEST_OLD_PASSWORD_ON_CHANGE)' => 0,
            'env(SECURITY_XSS_FILTER)' => 0,
            'env(SESSION_BACKEND)' => 'database',
            'env(SESSION_COOKIE_LIFETIME)' => 1209600,
            'env(SMONEY_API_BASE_URL)' => '',
            'env(SMONEY_TOKEN)' => '',
            'env(SMONEY_WEBSITE_NAME)' => '',
            'env(SOMFY_AUTHORIZATION_SERVER)' => '',
            'env(SSO_CONNECTION_ONLY_BO)' => '0',
            'env(STORAGE_SYSTEM)' => 'file',
            'env(STRIPE_PUBLIC_TOKEN)' => '',
            'env(STRIPE_SECRET_TOKEN)' => '',
            'env(SUBSCRIPTION)' => '0',
            'env(SUPPORT_LOGIN_GROUPS)' => '',
            'env(SYSTEM_API_USER_PASSWORD)' => '',
            'env(TAX_MODE)' => 'NATIONAL',
            'env(TIER_PRICING)' => '0',
            'env(USER_GROUPS_ENABLED)' => 0,
            'env(VIDEO_SYSTEM_PIPELINE)' => NULL,
            'env(VIDEO_SYSTEM_TEMPORARY_BUCKET)' => NULL,
            'env(WITHDRAWAL_PERIOD)' => 14,
            'env(WKHTMLTOPDF_BINARY_PATH)' => (\dirname(__DIR__, 4).'/vendor/bin/wkhtmltopdf-amd64'),
            'env(YAVIN_API_V2_JWT)' => '',
            'env(YAVIN_MESSENGER_TRANSPORT_DSN)' => '',
            'env(YAVIN_MESSENGER_VHOST_TEST)' => 'localhost',
            'env(YAVIN_URI)' => '',
            'marketplace.read_model.indexes.basket_short' => 'basket_short',
            'broadway.saga.mongodb.storage_suffix' => '_prod',
            'cscart_table_prefix' => 'cscart_',
            'doctrine_table_prefix' => 'doctrine_',
            'broadway.event_store.dbal.table' => 'cscart_broadway_events',
            'route_statistics_token' => '3E7547EBCB6739AA965B19C356793',
            'route_system_token' => '82F2BABAF3F177268F635A7172265',
            'marketplace.cookies_page_id' => NULL,
            'redis.url' => 'localhost:6379/1',
            'redis.default.prefix' => 'default.',
            'redis.locks.prefix' => 'locks.',
            'redis.handlers.prefix' => 'handlers.',
            'redis.cscart_sessions.prefix' => 'sessions.',
            'aws.video.maxsize' => 52428800,
            'aws.video.preset' => '1351620000001-000020',
            'aws.video.prefix' => 'videos/',
            'aws.video.maxduration' => 600,
            'aws.credentials.version' => 'latest',
            'marketplace.queue.config' => [
                'emails' => [
                    'priority' => 100,
                ],
                'csv_line' => [
                    'priority' => 25,
                ],
                'csv_entities' => [
                    'priority' => 0,
                ],
                'csv_exports' => [
                    'priority' => 0,
                ],
                'image_thumbnails' => [
                    'priority' => 0,
                ],
                'cache' => [
                    'priority' => 0,
                ],
                'search_engine' => [
                    'priority' => 15,
                ],
                'videos' => [
                    'priority' => 20,
                ],
                'products' => [
                    'priority' => 60,
                ],
                'multi_vendor_products' => [
                    'priority' => 30,
                ],
                'product_moderation' => [
                    'priority' => 50,
                ],
                'feature_products' => [
                    'priority' => 40,
                ],
                'readmodel' => [
                    'priority' => 10,
                ],
                'feature_available_offers' => [
                    'priority' => 20,
                ],
                'product_price_import' => [
                    'priority' => 20,
                ],
                'product_stock_import' => [
                    'priority' => 15,
                ],
                'translations_import' => [
                    'priority' => 5,
                ],
                'user_group_import' => [
                    'priority' => 0,
                ],
                'related_products_import' => [
                    'priority' => 5,
                ],
            ],
            'marketplace.function_to_dispatch' => [
                'Wizacha\\Exim\\Product::handleCSVProduct' => [
                    'queue' => 'csv_line',
                    'payload' => '\\Wizacha\\Async\\FunctionPayload',
                    'env' => [
                        0 => [
                            0 => 'runtime',
                            1 => 'company_id',
                        ],
                    ],
                ],
                'Wizacha\\Exim\\Import\\Entities\\EntitiesImporter::import' => [
                    'queue' => 'csv_entities',
                    'payload' => '\\Wizacha\\Async\\FunctionPayload',
                    'env' => [
                        0 => [
                            0 => 'runtime',
                            1 => 'company_id',
                        ],
                    ],
                ],
                'marketplace.moderation.moderation_service::moderateProduct' => [
                    'queue' => 'product_moderation',
                    'payload' => '\\Wizacha\\Async\\ServicePayload',
                ],
                'marketplace.search.category_index::updateByIds' => [
                    'queue' => 'search_engine',
                    'payload' => '\\Wizacha\\Async\\ServicePayload',
                ],
                'fn_delete_product' => [
                    'queue' => 'products',
                    'payload' => '\\Wizacha\\Async\\FunctionPayload',
                    'env' => [
                        0 => [
                            0 => 'runtime',
                            1 => 'company_id',
                        ],
                        1 => [
                            0 => '\\Wizacha\\Config::REG_AREA',
                        ],
                    ],
                ],
                'marketplace.product.projector::projectProduct' => [
                    'queue' => 'readmodel',
                    'payload' => '\\Wizacha\\Async\\ServicePayload',
                    'force_async' => true,
                ],
                'marketplace.product.projector::projectMultiVendorProduct' => [
                    'queue' => 'readmodel',
                    'payload' => '\\Wizacha\\Async\\ServicePayload',
                    'force_async' => false,
                ],
                'marketplace.catalog::rebuild' => [
                    'queue' => 'readmodel',
                    'payload' => '\\Wizacha\\Async\\ServicePayload',
                    'force_async' => true,
                ],
                'marketplace.pim.video_service::startImportFromUrl' => [
                    'queue' => 'videos',
                    'payload' => '\\Wizacha\\Async\\ServicePayload',
                ],
                'app.mailer::send' => [
                    'queue' => 'emails',
                    'payload' => '\\Wizacha\\Async\\ServicePayload',
                ],
                'app.dolist_client::sendMessageRequest' => [
                    'queue' => 'emails',
                    'payload' => '\\Wizacha\\Async\\ServicePayload',
                ],
                'marketplace.multi_vendor_product.linker::attachProductIdToMatchingMvp' => [
                    'queue' => 'multi_vendor_products',
                    'payload' => '\\Wizacha\\Async\\ServicePayload',
                ],
                'marketplace.multi_vendor_product.service::updateFromFirstProduct' => [
                    'queue' => 'multi_vendor_products',
                    'payload' => '\\Wizacha\\Async\\ServicePayload',
                ],
                'marketplace.multi_vendor_product.linker::attachMatchingProductsToMvpId' => [
                    'queue' => 'multi_vendor_products',
                    'payload' => '\\Wizacha\\Async\\ServicePayload',
                ],
                'fn_export' => [
                    'queue' => 'csv_exports',
                    'payload' => '\\Wizacha\\Async\\FunctionPayload',
                    'env' => [
                        0 => [
                            0 => 'runtime',
                            1 => 'company_id',
                        ],
                    ],
                ],
                'event.subscriber.search::onProductUpdateAsync' => [
                    'queue' => 'feature_products',
                    'payload' => '\\Wizacha\\Async\\ServicePayload',
                ],
                'marketplace.divisions_settings.service::onAsyncMarketplaceDivisionSettingsUpdate' => [
                    'queue' => 'feature_available_offers',
                    'payload' => '\\Wizacha\\Async\\ServicePayload',
                ],
                'marketplace.divisions_settings.service::onAsyncCompanyDivisionSettingsDispatch' => [
                    'queue' => 'feature_available_offers',
                    'payload' => '\\Wizacha\\Async\\ServicePayload',
                ],
                'marketplace.divisions_settings.service::onAsyncCompanyDivisionSettingsUpdate' => [
                    'queue' => 'feature_available_offers',
                    'payload' => '\\Wizacha\\Async\\ServicePayload',
                ],
                'marketplace.divisions_settings.service::onAsyncProductDivisionSettingsUpdate' => [
                    'queue' => 'feature_available_offers',
                    'payload' => '\\Wizacha\\Async\\ServicePayload',
                ],
                'Wizacha\\Exim\\ProductPrices::ImportProductPrices' => [
                    'queue' => 'product_price_import',
                    'payload' => '\\Wizacha\\Async\\FunctionPayload',
                    'env' => [
                        0 => [
                            0 => 'runtime',
                            1 => 'company_id',
                        ],
                    ],
                ],
                'Wizacha\\Exim\\ProductStock::ImportProductStocks' => [
                    'queue' => 'product_stock_import',
                    'payload' => '\\Wizacha\\Async\\FunctionPayload',
                    'env' => [
                        0 => [
                            0 => 'runtime',
                            1 => 'company_id',
                        ],
                    ],
                ],
                'Wizacha\\Exim\\Import\\TranslationImporter::import' => [
                    'queue' => 'translations_import',
                    'payload' => '\\Wizacha\\Async\\ServicePayload',
                ],
                'marketplace.user_group.service::importUsers' => [
                    'queue' => 'user_group_import',
                    'payload' => '\\Wizacha\\Async\\ServicePayload',
                ],
                'Wizacha\\Exim\\RelatedProducts::put' => [
                    'queue' => 'related_products_import',
                    'payload' => '\\Wizacha\\Async\\FunctionPayload',
                    'env' => [
                        0 => [
                            0 => 'runtime',
                            1 => 'company_id',
                        ],
                        1 => [
                            0 => 'user_info',
                        ],
                    ],
                ],
                'Wizacha\\CloudImageManager::invalidateCloudImageCache' => [
                    'queue' => 'videos',
                    'payload' => '\\Wizacha\\Async\\ServicePayload',
                ],
            ],
            'readonly_attributes' => [

            ],
            'regexp_guid' => '[0-9A-Fa-f]{8}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{12}',
            'image.serve_fake_images' => false,
            'entrypoint.api_url' => 'back.marketplace.ovhcloud.com',
            'current_oauth_provider' => 'okta',
            'google_analytics_tracking_id' => '',
            'security.authentication.hide_user_not_found' => false,
            'security.authentication.basic.realmname' => 'Secured Area',
            'config_product_templates' => [
                'product' => [

                ],
                'service' => [
                    'hidden_fields' => [
                        'w_green_tax' => '0.00',
                        'w_condition' => 'N',
                        'amount' => 0,
                        'infinite_stock' => 'Y',
                        'is_edp' => 'Y',
                        'is_returnable' => 'N',
                    ],
                ],
            ],
            'regexp_currency_code' => '^[a-zA-Z]{3}$',
            'regexp_country_code' => '^[a-zA-Z]{2}$',
            'url_wizaplace_documentation' => 'https://docs.wizaplace.com/',
            'authenticator_throttling.email_throttle_map' => [
                3 => 60,
                5 => 120,
                10 => 180,
            ],
            'authenticator_throttling.email_throttle_map.api' => [
                5 => 60,
                10 => 120,
                15 => 180,
            ],
            'authenticator_throttling.email_throttle_window' => 1320,
            'authenticator_throttling.email_throttle_window.api' => 1320,
            'authenticator_throttling.ip_throttle_window' => 604800,
            'order_amounts.calculator.pivot_date' => '20-07-2021 13:00:00',
            'session_cookie_lifetime' => 1209600,
            'feature.approve_user_by_admin' => 0,
            'feature.config_enable_auto_validate_product' => false,
            'session_backend' => 'redis',
            'cache_backend' => 'redis',
            'mailer_transport' => 'smtp',
            'mailer_host' => 'smtp.mandrillapp.com',
            'mailer_port' => '587',
            'mailer_user' => '<EMAIL>',
            'mailer_password' => 'md-PRVSaicT1KUruVJyQExGBg',
            'mailer_encryption' => NULL,
            'mailer_delivery_address' => NULL,
            'mail_domain' => 'marketplace.ovhcloud.com',
            'marketplace.frontend_theme' => 'remotefront',
            'theme_bundle' => 'RemotefrontBundle',
            'marketplace.version' => 'client-ovh-1.154.5',
            'cscart.crypt_key' => 'BjyOVTHUtYCdRfyL3VSgIR/9uQxLdj0VLhxNrdDHYr4=',
            'feature.sql_search' => 0,
            'feature.global_option_allowed_for_vendors' => false,
            'currency.sign' => '€',
            'currency.code' => 'EUR',
            'feature.currency.advanced' => false,
            'currency.rates_provider' => 'fixer',
            'currency.fixer.api_key' => '',
            'currency.fixer.base_url' => '',
            'system_api_user_password' => '$2y$12$kAqraW3UThn6ySZZHuhpQeHSjivPsaDcGO5neVShHOWjcYHzgzzAq',
            'aws.video.pipeline' => '1573041604946-jbcot2',
            'aws.video.temporary_bucket' => 'wizaplace-video-transcoding',
            'aws.s3.credentials.version' => 'latest',
            'aws.s3.credentials.region' => 'eu-west-1',
            'aws.s3.credentials.key' => '',
            'aws.s3.credentials.secret' => '',
            'aws.bucket' => '',
            'aws.sqs.credentials.version' => 'latest',
            'aws.sqs.credentials.region' => 'eu-west-1',
            'aws.sqs.credentials.key' => '',
            'aws.sqs.credentials.secret' => '',
            'ovh.s3.api_url' => 'https://storage.gra.cloud.ovh.net',
            'ovh.s3.region' => 'GRA',
            'ovh.s3.credentials.key' => '0e4c78f14d2248d2826bf468f427af43',
            'ovh.s3.credentials.secret' => '079024379f4c498ab16ebe0fba20d4ff',
            'ovh.s3.public_bucket' => 'public',
            'ovh.s3.private_bucket' => 'private',
            'ovh.s3.public_url_token' => 'AUTH_9e1013faed644b4a9452f7edcc391dc9',
            'database_host' => '************',
            'database_port' => 3306,
            'database_name' => 'marketplace_prod_0001',
            'database_name_tests' => 'marketplace_tests',
            'database_user' => 'marketplace_0001',
            'database_password' => 'nixfH4oYnmqhn85mVSh8qPD589',
            'discuss.database_driver' => 'pdo_mysql',
            'discuss.database_user' => 'marketplace',
            'discuss.database_password' => 'Lun3963HdAsYE0e38e4cbab40',
            'discuss.database_dbname' => 'discuss',
            'discuss.database_host' => 'ae137298-002.dbaas.ovh.net',
            'discuss.database_port' => 35853,
            'hipay.cashin.api_username' => '94697050.secure-gateway.hipay-tpp.com',
            'hipay.cashin.api_password' => 'Live_JfvkESTRxxtv50U18lRBaaET',
            'hipay.cashout.api_uri' => 'https://merchant.hipaywallet.com/api/',
            'hipay.cashout.api_login' => '45dfd460650228636b74ae1faccc344b',
            'hipay.cashout.api_password' => '47027328e6d37a29b47ddcf6ec171c20',
            'hipay.cashout.entity' => 'ovhsas',
            'hipay.cashout.merchant_group_id' => 503,
            'hipay.commission_recipient_id' => 14744799,
            'hipay.cashin.env' => 'production',
            'hipay.payment_product_list' => 'visa,mastercard,cb',
            'googlemaps.api_key' => 'AIzaSyAw9jadoH_dgVqvbLU5tjAlsEZ75qt1TTw',
            'entrypoint.administrator' => 'admin.php',
            'entrypoint.vendor' => 'vendor.php',
            'entrypoint.vendor_domain' => '',
            'entrypoint.marketplace_domain' => '',
            'http_host' => 'back.marketplace.ovhcloud.com',
            'http_path' => '',
            'external_backoffice_url' => NULL,
            'google_oauth_id' => '844056769527-ogmajn5st3i1c3ps61lgvvopsmsqj7ri.apps.googleusercontent.com',
            'google_oauth_secret' => 'JFJJwfjtDqsXGSzSiOewVPhA',
            'google_oauth_redirect_url' => 'https://auth.wizntw.com/redirect.php',
            'locale' => 'fr',
            'external_front_office_url' => 'marketplace.ovhcloud.com',
            'storage_system' => 'ovh',
            'queue.type' => 'amqp',
            'queue.path' => 'ovh_prod',
            'queue.region' => '',
            'queue.key' => '',
            'queue.secret' => '',
            'amqp.host' => '************',
            'amqp.port' => '5672',
            'amqp.user' => 'guest',
            'amqp.pass' => 'guest',
            'amqp.vhost' => '/',
            'mysql_version' => 5.6,
            'recaptcha.private_key' => '6Le_gSgTAAAAAKe_Jaa7sx_qzX2w9N5cE1q5WfZH',
            'recaptcha.public_key' => '6Le_gSgTAAAAAK_Hul4Tdk94n3LoNma1h-rWRZ0x',
            'feature.prediggo_exporter' => false,
            'algolia.api_identifier' => '2NO2C2V6G0',
            'algolia.api_index_prefix' => 'prod-ovh_',
            'algolia.api_key_full_rights' => '********************************',
            'algolia.geocoding.api_identifier' => '8EH90U3Z4P',
            'algolia.geocoding.api_key_limited_rights' => '********************************',
            'algolia.config.max_values_per_facet' => '100',
            'algolia.enable_command_update_post_deploy' => true,
            'azure.account_name' => '\'',
            'azure.account_key' => '\'',
            'marketplace.project_name' => 'Ovhcloud',
            'wkhtmltopdf_binary_path' => (\dirname(__DIR__, 4).'/vendor/bin/wkhtmltopdf-amd64'),
            'feature.sso_connection_only_bo' => false,
            'marketplace.pim.product_templates' => 'service',
            'feature.payment_deadline' => 0,
            'feature.notify_shipment_created' => true,
            'feature.enable_divisions' => false,
            'feature.available_offers' => false,
            'feature.green_tax_is_enabled' => true,
            'feature.order_adjustment' => false,
            'feature.marketplace_discounts' => false,
            'feature.marketplace_discount_optional_bonuses' => false,
            'feature.delivery_period' => 7,
            'feature.withdrawal_period' => 14,
            'feature.tier_pricing' => true,
            'feature.refund_auto' => 'NONE',
            'feature.merchants_refund_ability' => false,
            'feature.hide_client_email_from_vendor' => false,
            'clear_cache_auth_delay_month' => '3',
            'feature.subscription' => true,
            'feature.subscription.limit_renew_attempts' => 1,
            'config.enabled_system_options' => 'payment_frequency,commitment_period',
            'support_login.groups' => '<EMAIL>,<EMAIL>,<EMAIL>',
            'feature.cloudimage' => 0,
            'cloudimage_client_key' => NULL,
            'cloudimage_api_url' => 'https://api.cloudimage.com',
            'cloudimage_version_head' => NULL,
            'resources_purge_period.images' => 100,
            'config.algolia_fpu_max_fp' => 300,
            'monolog.log_level.sentinel' => 'warning',
            'config.frontend_callback_whitelist' => '*',
            'feature.security.enable_strict_password' => false,
            'feature.security.request_old_password_on_change' => false,
            'feature.security.xss_filter' => false,
            'feature.security.lock_user_accounts' => false,
            'security.lock_user_accounts.max_attempts' => 20,
            'feature.security.lock_ip_address' => false,
            'security.lock_ip_address.failed_limit' => 100,
            'security.lock_ip_address.duration' => 43200,
            'feature.security.lock_email_address.api' => false,
            'config.security.trusted_proxies' => NULL,
            'monolog.log_level.mailer' => '',
            'yavin_messenger_transport_dsn' => '',
            'feature.iban_validation' => false,
            'feature.security.captcha' => false,
            'frequency_cron_payout' => 'bimonthly',
            'feature.enable_image_resizing' => true,
            'feature.user_groups_enabled' => false,
            'config.kpi.webhook.url' => 'https://ckb5xzmas0.execute-api.eu-west-3.amazonaws.com/kpiMarketplaces',
            'inactivity_time_out' => 1209600,
            'feature.notify_user_update' => false,
            'config.related_products.types' => 'recommended',
            'previous_passwords_difference_limit' => 0,
            'feature.password_recovery_force_change' => false,
            'password_renewal_time_limit' => 0,
            'feature.catalog.show_zero_price_products' => false,
            'config.tax_mode' => 'NATIONAL',
            'feature.catalog.show_out_of_stock_products' => true,
            'config.prismic_content' => '',
            'config.bfo_url' => '',
            'feature.enable_bfo' => false,
            'feature.enable_yavin' => false,
            'yavin.uri' => '',
            'feature.discussions_allowed_for_admin' => false,
            'config.platform_technical_name' => 'wizaplace-loc',
            'config.hades.s3.region' => 'eu-west-1',
            'config.hades.s3.bucket' => 'wizachatest',
            'config.hades.s3.key' => '',
            'config.hades.s3.secret' => '',
            'feature.hipay_chargeback_test_env' => false,
            'config.dump_anonymizer.output.s3.directory' => 'dump_anonymizer',
            'config.dump_anonymizer.output.s3.region' => 'eu-west-1',
            'config.dump_anonymizer.output.s3.bucket' => 'wizachatest',
            'config.dump_anonymizer.output.s3.key' => '',
            'config.dump_anonymizer.output.s3.secret' => '',
            'feature.enable_automated_feeds' => true,
            'feature.enable_database_limit' => false,
            'config.enable_database_limit_size' => '2000',
            'feature.quote_requests' => false,
            'marketplace.stock.cart_time_limit' => 900,
            'api_application_token' => NULL,
            'feature.application_token_for_public_routes' => 0,
            'feature.marketplace_only_sell_services' => 0,
            'feature.multi_vendor_product' => true,
            'feature.multi_vendor_product_auto_create_if_no_match' => false,
            'feature.enable_c2c' => 0,
            'feature.sandbox' => 0,
            'feature.commission.include_shipping_in_commission' => 0,
            'feature.commission.add_full_shipping_to_commission' => 0,
            'feature.enable_product_attachments' => true,
            'feature.professional_clients' => false,
            'feature.create_legal_wallets_for_customers' => 0,
            'feature.activate_billing_number_auto_generation' => false,
            'feature.activate_workflow_translation' => false,
            'feature.allow_mvp_sync_from_products' => false,
            'feature.organisations_enabled' => false,
            'feature.multi_vendor_product.rules' => 'ean',
            'marketplace.transaction_mode.affiliate' => true,
            'feature.cache_http' => true,
            'click_and_collect_shipping_id' => 0,
            'doctrine.mapped_cscart_tables.regex' => 'users|products|product_descriptions|product_options_inventory|categories|category_descriptions|products_categories|companies|taxes|tax_descriptions|tax_rates|order_adjustment|countries|country_descriptions|user_profiles',
            'base_url' => '//back.marketplace.ovhcloud.com/',
            'router.request_context.host' => 'back.marketplace.ovhcloud.com',
            'router.request_context.scheme' => 'https',
            'router.request_context.base_url' => '',
            'monolog.path' => (\dirname(__DIR__, 3).'/logs/prod.json'),
            'event_dispatcher.event_aliases' => [
                'Symfony\\Component\\Console\\Event\\ConsoleCommandEvent' => 'console.command',
                'Symfony\\Component\\Console\\Event\\ConsoleErrorEvent' => 'console.error',
                'Symfony\\Component\\Console\\Event\\ConsoleTerminateEvent' => 'console.terminate',
                'Symfony\\Component\\Form\\Event\\PreSubmitEvent' => 'form.pre_submit',
                'Symfony\\Component\\Form\\Event\\SubmitEvent' => 'form.submit',
                'Symfony\\Component\\Form\\Event\\PostSubmitEvent' => 'form.post_submit',
                'Symfony\\Component\\Form\\Event\\PreSetDataEvent' => 'form.pre_set_data',
                'Symfony\\Component\\Form\\Event\\PostSetDataEvent' => 'form.post_set_data',
                'Symfony\\Component\\HttpKernel\\Event\\ControllerArgumentsEvent' => 'kernel.controller_arguments',
                'Symfony\\Component\\HttpKernel\\Event\\ControllerEvent' => 'kernel.controller',
                'Symfony\\Component\\HttpKernel\\Event\\ResponseEvent' => 'kernel.response',
                'Symfony\\Component\\HttpKernel\\Event\\FinishRequestEvent' => 'kernel.finish_request',
                'Symfony\\Component\\HttpKernel\\Event\\RequestEvent' => 'kernel.request',
                'Symfony\\Component\\HttpKernel\\Event\\ViewEvent' => 'kernel.view',
                'Symfony\\Component\\HttpKernel\\Event\\ExceptionEvent' => 'kernel.exception',
                'Symfony\\Component\\HttpKernel\\Event\\TerminateEvent' => 'kernel.terminate',
                'Symfony\\Component\\Workflow\\Event\\GuardEvent' => 'workflow.guard',
                'Symfony\\Component\\Workflow\\Event\\LeaveEvent' => 'workflow.leave',
                'Symfony\\Component\\Workflow\\Event\\TransitionEvent' => 'workflow.transition',
                'Symfony\\Component\\Workflow\\Event\\EnterEvent' => 'workflow.enter',
                'Symfony\\Component\\Workflow\\Event\\EnteredEvent' => 'workflow.entered',
                'Symfony\\Component\\Workflow\\Event\\CompletedEvent' => 'workflow.completed',
                'Symfony\\Component\\Workflow\\Event\\AnnounceEvent' => 'workflow.announce',
                'Symfony\\Component\\Security\\Core\\Event\\AuthenticationSuccessEvent' => 'security.authentication.success',
                'Symfony\\Component\\Security\\Core\\Event\\AuthenticationFailureEvent' => 'security.authentication.failure',
                'Symfony\\Component\\Security\\Http\\Event\\InteractiveLoginEvent' => 'security.interactive_login',
                'Symfony\\Component\\Security\\Http\\Event\\SwitchUserEvent' => 'security.switch_user',
            ],
            'fragment.renderer.hinclude.global_template' => NULL,
            'fragment.path' => '/_fragment',
            'kernel.http_method_override' => true,
            'kernel.trusted_hosts' => [

            ],
            'kernel.default_locale' => 'fr',
            'kernel.error_controller' => 'error_controller',
            'templating.helper.code.file_link_format' => NULL,
            'debug.file_link_format' => NULL,
            'session.metadata.storage_key' => '_sf2_meta',
            'session.storage.options' => [
                'cache_limiter' => '0',
                'cookie_lifetime' => 1209600,
                'cookie_secure' => true,
                'cookie_httponly' => true,
                'gc_probability' => 1,
                'gc_divisor' => 10,
            ],
            'session.metadata.update_threshold' => 0,
            'form.type_extension.csrf.enabled' => true,
            'form.type_extension.csrf.field_name' => '_token',
            'asset.request_context.base_path' => '',
            'asset.request_context.secure' => false,
            'templating.loader.cache.path' => NULL,
            'templating.engines' => [
                0 => 'smarty',
                1 => 'twig',
            ],
            'validator.translation_domain' => 'validators',
            'translator.logging' => false,
            'translator.default_path' => (\dirname(__DIR__, 4).'/translations'),
            'data_collector.templates' => [

            ],
            'debug.error_handler.throw_at' => 0,
            'router.resource' => (\dirname(__DIR__, 4).'/app/config/routing.yml'),
            'router.cache_class_prefix' => 'appAppKernelProdContainer',
            'request_listener.http_port' => 80,
            'request_listener.https_port' => 443,
            'security.authentication.trust_resolver.anonymous_class' => NULL,
            'security.authentication.trust_resolver.rememberme_class' => NULL,
            'security.role_hierarchy.roles' => [
                'ROLE_VENDOR' => [
                    0 => 'ROLE_USER',
                ],
                'ROLE_ADMIN' => [
                    0 => 'ROLE_USER',
                ],
            ],
            'security.access.denied_url' => NULL,
            'security.authentication.manager.erase_credentials' => true,
            'security.authentication.session_strategy.strategy' => 'migrate',
            'security.access.always_authenticate_before_granting' => false,
            'broadway.saga.mongodb.database' => 'broadway_prod_prod',
            'broadway.event_store.dbal.connection' => 'default',
            'broadway.event_store.dbal.use_binary' => false,
            'broadway.serializer.payload.service_id' => 'broadway.simple_interface_serializer',
            'broadway.serializer.readmodel.service_id' => 'broadway.simple_interface_serializer',
            'broadway.serializer.metadata.service_id' => 'broadway.simple_interface_serializer',
            'doctrine_cache.apc.class' => 'Doctrine\\Common\\Cache\\ApcCache',
            'doctrine_cache.apcu.class' => 'Doctrine\\Common\\Cache\\ApcuCache',
            'doctrine_cache.array.class' => 'Doctrine\\Common\\Cache\\ArrayCache',
            'doctrine_cache.chain.class' => 'Doctrine\\Common\\Cache\\ChainCache',
            'doctrine_cache.couchbase.class' => 'Doctrine\\Common\\Cache\\CouchbaseCache',
            'doctrine_cache.couchbase.connection.class' => 'Couchbase',
            'doctrine_cache.couchbase.hostnames' => 'localhost:8091',
            'doctrine_cache.file_system.class' => 'Doctrine\\Common\\Cache\\FilesystemCache',
            'doctrine_cache.php_file.class' => 'Doctrine\\Common\\Cache\\PhpFileCache',
            'doctrine_cache.memcache.class' => 'Doctrine\\Common\\Cache\\MemcacheCache',
            'doctrine_cache.memcache.connection.class' => 'Memcache',
            'doctrine_cache.memcache.host' => 'localhost',
            'doctrine_cache.memcache.port' => 11211,
            'doctrine_cache.memcached.class' => 'Doctrine\\Common\\Cache\\MemcachedCache',
            'doctrine_cache.memcached.connection.class' => 'Memcached',
            'doctrine_cache.memcached.host' => 'localhost',
            'doctrine_cache.memcached.port' => 11211,
            'doctrine_cache.mongodb.class' => 'Doctrine\\Common\\Cache\\MongoDBCache',
            'doctrine_cache.mongodb.collection.class' => 'MongoCollection',
            'doctrine_cache.mongodb.connection.class' => 'MongoClient',
            'doctrine_cache.mongodb.server' => 'localhost:27017',
            'doctrine_cache.predis.client.class' => 'Predis\\Client',
            'doctrine_cache.predis.scheme' => 'tcp',
            'doctrine_cache.predis.host' => 'localhost',
            'doctrine_cache.predis.port' => 6379,
            'doctrine_cache.redis.class' => 'Doctrine\\Common\\Cache\\RedisCache',
            'doctrine_cache.redis.connection.class' => 'Redis',
            'doctrine_cache.redis.host' => 'localhost',
            'doctrine_cache.redis.port' => 6379,
            'doctrine_cache.riak.class' => 'Doctrine\\Common\\Cache\\RiakCache',
            'doctrine_cache.riak.bucket.class' => 'Riak\\Bucket',
            'doctrine_cache.riak.connection.class' => 'Riak\\Connection',
            'doctrine_cache.riak.bucket_property_list.class' => 'Riak\\BucketPropertyList',
            'doctrine_cache.riak.host' => 'localhost',
            'doctrine_cache.riak.port' => 8087,
            'doctrine_cache.sqlite3.class' => 'Doctrine\\Common\\Cache\\SQLite3Cache',
            'doctrine_cache.sqlite3.connection.class' => 'SQLite3',
            'doctrine_cache.void.class' => 'Doctrine\\Common\\Cache\\VoidCache',
            'doctrine_cache.wincache.class' => 'Doctrine\\Common\\Cache\\WinCacheCache',
            'doctrine_cache.xcache.class' => 'Doctrine\\Common\\Cache\\XcacheCache',
            'doctrine_cache.zenddata.class' => 'Doctrine\\Common\\Cache\\ZendDataCache',
            'doctrine_cache.security.acl.cache.class' => 'Doctrine\\Bundle\\DoctrineCacheBundle\\Acl\\Model\\AclCache',
            'doctrine.dbal.logger.chain.class' => 'Doctrine\\DBAL\\Logging\\LoggerChain',
            'doctrine.dbal.logger.profiling.class' => 'Doctrine\\DBAL\\Logging\\DebugStack',
            'doctrine.dbal.logger.class' => 'Symfony\\Bridge\\Doctrine\\Logger\\DbalLogger',
            'doctrine.dbal.configuration.class' => 'Doctrine\\DBAL\\Configuration',
            'doctrine.data_collector.class' => 'Doctrine\\Bundle\\DoctrineBundle\\DataCollector\\DoctrineDataCollector',
            'doctrine.dbal.connection.event_manager.class' => 'Symfony\\Bridge\\Doctrine\\ContainerAwareEventManager',
            'doctrine.dbal.connection_factory.class' => 'Doctrine\\Bundle\\DoctrineBundle\\ConnectionFactory',
            'doctrine.dbal.events.mysql_session_init.class' => 'Doctrine\\DBAL\\Event\\Listeners\\MysqlSessionInit',
            'doctrine.dbal.events.oracle_session_init.class' => 'Doctrine\\DBAL\\Event\\Listeners\\OracleSessionInit',
            'doctrine.class' => 'Doctrine\\Bundle\\DoctrineBundle\\Registry',
            'doctrine.entity_managers' => [
                'default' => 'doctrine.orm.default_entity_manager',
            ],
            'doctrine.default_entity_manager' => 'default',
            'doctrine.dbal.connection_factory.types' => [
                'money' => [
                    'class' => 'Wizacha\\Money\\MoneyDoctrineType',
                    'commented' => NULL,
                ],
                'precise_money' => [
                    'class' => 'Wizacha\\Money\\PreciseMoneyDoctrineType',
                    'commented' => NULL,
                ],
                'exchange_rate' => [
                    'class' => 'Wizacha\\Marketplace\\Currency\\ExchangeRateDoctrineType',
                    'commented' => NULL,
                ],
                'php_enum_promotion_type' => [
                    'class' => 'Wizacha\\Marketplace\\Promotion\\PromotionTypeEnumType',
                    'commented' => NULL,
                ],
                'php_enum_price_fields' => [
                    'class' => 'Wizacha\\Marketplace\\Price\\PriceFieldsEnumType',
                    'commented' => NULL,
                ],
                'bonus_target' => [
                    'class' => 'Wizacha\\Marketplace\\Promotion\\BonusTarget\\BonusTargetDoctrineType',
                    'commented' => NULL,
                ],
                'datetime_utc' => [
                    'class' => 'Wizacha\\Bridge\\Doctrine\\UTCDateTimeType',
                    'commented' => NULL,
                ],
                'multi_vendor_product_status' => [
                    'class' => 'Wizacha\\Marketplace\\PIM\\MultiVendorProduct\\Status\\StatusType',
                    'commented' => NULL,
                ],
                'locale' => [
                    'class' => 'Wizacha\\Component\\Locale\\Bridge\\Doctrine\\LocaleType',
                    'commented' => NULL,
                ],
                'organisation_status' => [
                    'class' => 'Wizacha\\Marketplace\\Organisation\\Doctrine\\OrganisationStatusType',
                    'commented' => NULL,
                ],
                'user_type' => [
                    'class' => 'Wizacha\\Marketplace\\User\\Doctrine\\UserType',
                    'commented' => NULL,
                ],
                'ramsey_uuid' => [
                    'class' => 'Rhumsaa\\Uuid\\Doctrine\\UuidType',
                    'commented' => NULL,
                ],
                'php_enum_transaction_type' => [
                    'class' => 'Wizacha\\Marketplace\\Transaction\\DoctrineType\\TransactionTypeEnumType',
                    'commented' => NULL,
                ],
                'php_enum_transaction_status' => [
                    'class' => 'Wizacha\\Marketplace\\Transaction\\DoctrineType\\TransactionStatusEnumType',
                    'commented' => NULL,
                ],
                'php_enum_order_refund_status' => [
                    'class' => 'Wizacha\\Marketplace\\Order\\Refund\\DoctrineType\\OrderRefundStatusEnumType',
                    'commented' => NULL,
                ],
                'php_enum_refund_status' => [
                    'class' => 'Wizacha\\Marketplace\\Order\\Refund\\DoctrineType\\RefundStatusEnumType',
                    'commented' => NULL,
                ],
                'php_enum_order_attachment_type' => [
                    'class' => 'Wizacha\\Marketplace\\Order\\OrderAttachment\\DoctrineType\\OrderAttachmentEnumType',
                    'commented' => NULL,
                ],
                'php_enum_tax_rate_type' => [
                    'class' => 'Wizacha\\Marketplace\\PIM\\Tax\\DoctrineType\\TaxRateTypeEnumType',
                    'commented' => NULL,
                ],
                'php_enum_order_status' => [
                    'class' => 'Wizacha\\Marketplace\\Order\\DoctrineType\\OrderStatusEnumType',
                    'commented' => NULL,
                ],
                'php_enum_mandate_status' => [
                    'class' => 'Wizacha\\Marketplace\\Payment\\DoctrineType\\MandateStatusEnumType',
                    'commented' => NULL,
                ],
                'php_enum_company_person_type' => [
                    'class' => 'Wizacha\\Marketplace\\CompanyPerson\\DoctrineType\\CompanyPersonEnumType',
                    'commented' => NULL,
                ],
                'php_enum_related_products_type' => [
                    'class' => 'Wizacha\\Marketplace\\RelatedProduct\\DoctrineType\\RelatedProductsEnumType',
                    'commented' => NULL,
                ],
            ],
            'doctrine.connections' => [
                'default' => 'doctrine.dbal.default_connection',
            ],
            'doctrine.default_connection' => 'default',
            'doctrine.orm.configuration.class' => 'Doctrine\\ORM\\Configuration',
            'doctrine.orm.entity_manager.class' => 'Doctrine\\ORM\\EntityManager',
            'doctrine.orm.manager_configurator.class' => 'Doctrine\\Bundle\\DoctrineBundle\\ManagerConfigurator',
            'doctrine.orm.cache.array.class' => 'Doctrine\\Common\\Cache\\ArrayCache',
            'doctrine.orm.cache.apc.class' => 'Doctrine\\Common\\Cache\\ApcCache',
            'doctrine.orm.cache.memcache.class' => 'Doctrine\\Common\\Cache\\MemcacheCache',
            'doctrine.orm.cache.memcache_host' => 'localhost',
            'doctrine.orm.cache.memcache_port' => 11211,
            'doctrine.orm.cache.memcache_instance.class' => 'Memcache',
            'doctrine.orm.cache.memcached.class' => 'Doctrine\\Common\\Cache\\MemcachedCache',
            'doctrine.orm.cache.memcached_host' => 'localhost',
            'doctrine.orm.cache.memcached_port' => 11211,
            'doctrine.orm.cache.memcached_instance.class' => 'Memcached',
            'doctrine.orm.cache.redis.class' => 'Doctrine\\Common\\Cache\\RedisCache',
            'doctrine.orm.cache.redis_host' => 'localhost',
            'doctrine.orm.cache.redis_port' => 6379,
            'doctrine.orm.cache.redis_instance.class' => 'Redis',
            'doctrine.orm.cache.xcache.class' => 'Doctrine\\Common\\Cache\\XcacheCache',
            'doctrine.orm.cache.wincache.class' => 'Doctrine\\Common\\Cache\\WinCacheCache',
            'doctrine.orm.cache.zenddata.class' => 'Doctrine\\Common\\Cache\\ZendDataCache',
            'doctrine.orm.metadata.driver_chain.class' => 'Doctrine\\Persistence\\Mapping\\Driver\\MappingDriverChain',
            'doctrine.orm.metadata.annotation.class' => 'Doctrine\\ORM\\Mapping\\Driver\\AnnotationDriver',
            'doctrine.orm.metadata.xml.class' => 'Doctrine\\ORM\\Mapping\\Driver\\SimplifiedXmlDriver',
            'doctrine.orm.metadata.yml.class' => 'Doctrine\\ORM\\Mapping\\Driver\\SimplifiedYamlDriver',
            'doctrine.orm.metadata.php.class' => 'Doctrine\\ORM\\Mapping\\Driver\\PHPDriver',
            'doctrine.orm.metadata.staticphp.class' => 'Doctrine\\ORM\\Mapping\\Driver\\StaticPHPDriver',
            'doctrine.orm.proxy_cache_warmer.class' => 'Symfony\\Bridge\\Doctrine\\CacheWarmer\\ProxyCacheWarmer',
            'form.type_guesser.doctrine.class' => 'Symfony\\Bridge\\Doctrine\\Form\\DoctrineOrmTypeGuesser',
            'doctrine.orm.validator.unique.class' => 'Symfony\\Bridge\\Doctrine\\Validator\\Constraints\\UniqueEntityValidator',
            'doctrine.orm.validator_initializer.class' => 'Symfony\\Bridge\\Doctrine\\Validator\\DoctrineInitializer',
            'doctrine.orm.security.user.provider.class' => 'Symfony\\Bridge\\Doctrine\\Security\\User\\EntityUserProvider',
            'doctrine.orm.listeners.resolve_target_entity.class' => 'Doctrine\\ORM\\Tools\\ResolveTargetEntityListener',
            'doctrine.orm.listeners.attach_entity_listeners.class' => 'Doctrine\\ORM\\Tools\\AttachEntityListenersListener',
            'doctrine.orm.naming_strategy.default.class' => 'Doctrine\\ORM\\Mapping\\DefaultNamingStrategy',
            'doctrine.orm.naming_strategy.underscore.class' => 'Doctrine\\ORM\\Mapping\\UnderscoreNamingStrategy',
            'doctrine.orm.quote_strategy.default.class' => 'Doctrine\\ORM\\Mapping\\DefaultQuoteStrategy',
            'doctrine.orm.quote_strategy.ansi.class' => 'Doctrine\\ORM\\Mapping\\AnsiQuoteStrategy',
            'doctrine.orm.entity_listener_resolver.class' => 'Doctrine\\Bundle\\DoctrineBundle\\Mapping\\ContainerEntityListenerResolver',
            'doctrine.orm.second_level_cache.default_cache_factory.class' => 'Doctrine\\ORM\\Cache\\DefaultCacheFactory',
            'doctrine.orm.second_level_cache.default_region.class' => 'Doctrine\\ORM\\Cache\\Region\\DefaultRegion',
            'doctrine.orm.second_level_cache.filelock_region.class' => 'Doctrine\\ORM\\Cache\\Region\\FileLockRegion',
            'doctrine.orm.second_level_cache.logger_chain.class' => 'Doctrine\\ORM\\Cache\\Logging\\CacheLoggerChain',
            'doctrine.orm.second_level_cache.logger_statistics.class' => 'Doctrine\\ORM\\Cache\\Logging\\StatisticsCacheLogger',
            'doctrine.orm.second_level_cache.cache_configuration.class' => 'Doctrine\\ORM\\Cache\\CacheConfiguration',
            'doctrine.orm.second_level_cache.regions_configuration.class' => 'Doctrine\\ORM\\Cache\\RegionsConfiguration',
            'doctrine.orm.auto_generate_proxy_classes' => false,
            'doctrine.orm.proxy_namespace' => 'Proxies',
            'monolog.use_microseconds' => true,
            'monolog.swift_mailer.handlers' => [

            ],
            'monolog.handlers_to_channels' => [
                'monolog.handler.main' => NULL,
                'monolog.handler.verbose' => [
                    'type' => 'inclusive',
                    'elements' => [
                        0 => 'mailer',
                        1 => 'sentinel',
                        2 => 'psp',
                        3 => 'functional',
                    ],
                ],
            ],
            'snc_redis.client.class' => 'Predis\\Client',
            'snc_redis.client_options.class' => 'Predis\\Configuration\\Options',
            'snc_redis.connection_parameters.class' => 'Predis\\Connection\\Parameters',
            'snc_redis.connection_factory.class' => 'Snc\\RedisBundle\\Client\\Predis\\Connection\\ConnectionFactory',
            'snc_redis.connection_wrapper.class' => 'Snc\\RedisBundle\\Client\\Predis\\Connection\\ConnectionWrapper',
            'snc_redis.phpredis_client.class' => 'Redis',
            'snc_redis.phpredis_connection_wrapper.class' => 'Snc\\RedisBundle\\Client\\Phpredis\\Client',
            'snc_redis.logger.class' => 'Snc\\RedisBundle\\Logger\\RedisLogger',
            'snc_redis.data_collector.class' => 'Snc\\RedisBundle\\DataCollector\\RedisDataCollector',
            'snc_redis.doctrine_cache_phpredis.class' => 'Doctrine\\Common\\Cache\\RedisCache',
            'snc_redis.doctrine_cache_predis.class' => 'Doctrine\\Common\\Cache\\PredisCache',
            'snc_redis.monolog_handler.class' => 'Monolog\\Handler\\RedisHandler',
            'snc_redis.swiftmailer_spool.class' => 'Snc\\RedisBundle\\SwiftMailer\\RedisSpool',
            'twig.exception_listener.controller' => 'twig.controller.exception::showAction',
            'twig.form.resources' => [
                0 => 'form_div_layout.html.twig',
                1 => '@App/Form/feature_row.html.twig',
                2 => '@App/Form/checkbox_row.html.twig',
                3 => '@App/Form/choice_widget_expanded.html.twig',
            ],
            'twig.default_path' => (\dirname(__DIR__, 4).'/templates'),
            'knp_snappy.pdf.binary' => (\dirname(__DIR__, 4).'/vendor/bin/wkhtmltopdf-amd64'),
            'knp_snappy.pdf.options' => [

            ],
            'knp_snappy.pdf.env' => [

            ],
            'fos_js_routing.extractor.class' => 'FOS\\JsRoutingBundle\\Extractor\\ExposedRoutesExtractor',
            'fos_js_routing.controller.class' => 'FOS\\JsRoutingBundle\\Controller\\Controller',
            'fos_js_routing.normalizer.route_collection.class' => 'FOS\\JsRoutingBundle\\Serializer\\Normalizer\\RouteCollectionNormalizer',
            'fos_js_routing.normalizer.routes_response.class' => 'FOS\\JsRoutingBundle\\Serializer\\Normalizer\\RoutesResponseNormalizer',
            'fos_js_routing.denormalizer.route_collection.class' => 'FOS\\JsRoutingBundle\\Serializer\\Denormalizer\\RouteCollectionDenormalizer',
            'fos_js_routing.request_context_base_url' => NULL,
            'fos_js_routing.cache_control' => [
                'enabled' => false,
            ],
            'swiftmailer.mailer.mailer.transport.name' => 'smtp',
            'swiftmailer.mailer.mailer.transport.smtp.encryption' => NULL,
            'swiftmailer.mailer.mailer.transport.smtp.port' => '587',
            'swiftmailer.mailer.mailer.transport.smtp.host' => 'smtp.mandrillapp.com',
            'swiftmailer.mailer.mailer.transport.smtp.username' => '<EMAIL>',
            'swiftmailer.mailer.mailer.transport.smtp.password' => 'md-PRVSaicT1KUruVJyQExGBg',
            'swiftmailer.mailer.mailer.transport.smtp.auth_mode' => NULL,
            'swiftmailer.mailer.mailer.transport.smtp.timeout' => 30,
            'swiftmailer.mailer.mailer.transport.smtp.source_ip' => NULL,
            'swiftmailer.mailer.mailer.transport.smtp.local_domain' => NULL,
            'swiftmailer.mailer.mailer.transport.smtp.stream_options' => [

            ],
            'swiftmailer.mailer.mailer.spool.enabled' => false,
            'swiftmailer.mailer.mailer.plugin.impersonate' => NULL,
            'swiftmailer.mailer.mailer.single_address' => NULL,
            'swiftmailer.mailer.mailer.delivery.enabled' => true,
            'swiftmailer.spool.enabled' => false,
            'swiftmailer.delivery.enabled' => true,
            'swiftmailer.single_address' => NULL,
            'swiftmailer.mailer.fake_mailer.transport.name' => 'null',
            'swiftmailer.mailer.fake_mailer.transport.smtp.encryption' => NULL,
            'swiftmailer.mailer.fake_mailer.transport.smtp.port' => 25,
            'swiftmailer.mailer.fake_mailer.transport.smtp.host' => 'localhost',
            'swiftmailer.mailer.fake_mailer.transport.smtp.username' => NULL,
            'swiftmailer.mailer.fake_mailer.transport.smtp.password' => NULL,
            'swiftmailer.mailer.fake_mailer.transport.smtp.auth_mode' => NULL,
            'swiftmailer.mailer.fake_mailer.transport.smtp.timeout' => 30,
            'swiftmailer.mailer.fake_mailer.transport.smtp.source_ip' => NULL,
            'swiftmailer.mailer.fake_mailer.transport.smtp.local_domain' => NULL,
            'swiftmailer.mailer.fake_mailer.transport.smtp.stream_options' => [

            ],
            'swiftmailer.mailer.fake_mailer.spool.enabled' => false,
            'swiftmailer.mailer.fake_mailer.plugin.impersonate' => NULL,
            'swiftmailer.mailer.fake_mailer.single_address' => NULL,
            'swiftmailer.mailer.fake_mailer.delivery.enabled' => false,
            'swiftmailer.mailers' => [
                'fake_mailer' => 'swiftmailer.mailer.fake_mailer',
                'mailer' => 'swiftmailer.mailer.mailer',
            ],
            'swiftmailer.default_mailer' => 'mailer',
            'nelmio_cors.map' => [
                '^/api/' => [
                    'allow_origin' => true,
                    'allow_methods' => [
                        0 => 'POST',
                        1 => 'PUT',
                        2 => 'GET',
                        3 => 'DELETE',
                        4 => 'PATCH',
                    ],
                    'allow_headers' => [
                        0 => 'authorization',
                        1 => 'content-type',
                    ],
                    'forced_allow_origin_value' => '*',
                ],
            ],
            'nelmio_cors.defaults' => [
                'allow_origin' => [

                ],
                'allow_credentials' => false,
                'allow_headers' => [

                ],
                'expose_headers' => [

                ],
                'allow_methods' => [

                ],
                'max_age' => 0,
                'hosts' => [

                ],
                'origin_regex' => false,
                'forced_allow_origin_value' => NULL,
            ],
            'nelmio_cors.cors_listener.class' => 'Nelmio\\CorsBundle\\EventListener\\CorsListener',
            'nelmio_cors.options_resolver.class' => 'Nelmio\\CorsBundle\\Options\\Resolver',
            'nelmio_cors.options_provider.config.class' => 'Nelmio\\CorsBundle\\Options\\ConfigProvider',
            'use_new_checkout' => true,
            'are_shippings_editable_by_vendors' => true,
            'search_record_product_image_width' => 175,
            'search_record_product_image_height' => 175,
            'feature.allow_files_on_non_edp_products' => false,
            'feature.checkout.accept_terms_and_conditions' => true,
            'checkout.address.billing_first' => true,
            'feature.enable_company_type_facet' => false,
            'feature.enable_companies_facet' => true,
            'feature.create_account.must_accept_terms' => true,
            'feature.multi_vendor_product_auto_link' => true,
            'feature.enable_ecommerce_analytics' => false,
            'feature.pim_sync_product_from_mvp' => false,
            'feature.disable_front_office' => true,
            'feature.lead_gen' => false,
            'feature.attributes_in_groups_can_override_categories' => true,
            'marketplace.transaction_mode.transactional' => true,
            'marketplace.transaction_mode.contact' => true,
            'smarty.template_directories' => [
                0 => (\dirname(__DIR__, 4).'/src/RemotefrontBundle/Resources/views/frontend/'),
                1 => (\dirname(__DIR__, 4).'/src/AppBundle/Resources/views/frontend/'),
            ],
            'smarty.plugin_directories' => [
                0 => (\dirname(__DIR__, 4).'/src/RemotefrontBundle/Smarty/'),
                1 => (\dirname(__DIR__, 4).'/src/AppBundle/Smarty/'),
            ],
            'cache_api_catalog_products_id' => 300,
            'cache_api_catalog_product_reviews' => 900,
            'cache_api_search_products' => 300,
            'cache_api_catalog_category_tree' => 900,
            'cache_api_catalog_category_list' => 900,
            'cache_api_cms_menu_list' => 900,
            'cache_api_banners_get' => 900,
            'cache_api_banners_get_category' => 900,
            'cache_api_divisions' => 900,
            'cache_api_declination' => 300,
            'hipay.css' => NULL,
            'console.command.ids' => [
                0 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\CreateSimpleUserCommand',
                1 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\CreateAdminUserCommand',
                2 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\AuthLogPurgeCommand',
                3 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\AuthLogClearCommand',
                4 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\ClearEximJobsCommand',
                5 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\UserAccessToken',
                6 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\PushVendorInfoToPspCommand',
                7 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\SMoneyCheckKycCommand',
                8 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\MangoPayCheckKycCommand',
                9 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\LemonWayCheckKycCommand',
                10 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\HipayCheckKycCommand',
                11 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\CheckValidityMarketplaceDiscountCommand',
                12 => 'Wizacha\\AppBundle\\Command\\OrderCancelRollbackCommand',
                13 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeleteOrdersCommand',
                14 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeleteAllOrdersCommand',
                15 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\Readmodel\\ClearReadmodelCommand',
                16 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\Readmodel\\CreateReadmodelCommand',
                17 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\Readmodel\\UpdateReadmodelCommand',
                18 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\Readmodel\\DumpReadmodelCommand',
                19 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\SeoRefreshNamesCommand',
                20 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\Algolia\\ClearAlgoliaIndexCommand',
                21 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\Algolia\\CreateAlgoliaIndexCommand',
                22 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\Algolia\\PushAlgoliaConfigCommand',
                23 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\Algolia\\PushAlgoliaProductsCommand',
                24 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\Algolia\\UpdateAlgoliaProductsCommand',
                25 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\ProductRefreshCacheCommand',
                26 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\CheckMangopayUserIdCommand',
                27 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\DropMarketplaceDatabaseCommand',
                28 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\UpdateTranslationOrderStatusCommand',
                29 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\CreateUserFromCSVCommand',
                30 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\MiraklCreateCompanyAdministrators',
                31 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\MiraklOrderForceCompletedCommand',
                32 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\MiraklOrderForceFromProcessingToProcessedCommand',
                33 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\UpdateTranslationFromCsvCommand',
                34 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\BasketsDeleteAllCommand',
                35 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeployRemoveAssetsAwsCacheCommand',
                36 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeployAlgoliaUpdateCommand',
                37 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\EndBlockedDispatchedOrdersCommand',
                38 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\RenewSubscriptionCommand',
                39 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeployMigrateOrderTransactionsCommand',
                40 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeployMigrateOrderTransactionsIsBackCommand',
                41 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeployMigrateRefunds',
                42 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\CheckIncompleteOrdersCommand',
                43 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\Division\\ImportDivisionProductsCommand',
                44 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\PayPaymentDefermentOrdersCommand',
                45 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\PspSendKycCommand',
                46 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\MangoPaySendKycCommand',
                47 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\LemonwaySendKycCommand',
                48 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\SMoneySendKycCommand',
                49 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\ClearTemporaryResourcesCommand',
                50 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\ListAmqpQueuesCommand',
                51 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeployMigrateDivisionCommand',
                52 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\Moderation\\PurgeProductInProgressCommand',
                53 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\PurgeResourcesCommand',
                54 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\CreateAmqpQueuesCommand',
                55 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\LoadFixtureCommand',
                56 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\AssetsInstallCommand',
                57 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\ListAllFeaturesCommand',
                58 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\RenameProductAttachmentCommand',
                59 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\AdminInstallCommand',
                60 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeployPostActionsCommand',
                61 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeployRunCommand',
                62 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\UpdateOrdersToCompletedCommand',
                63 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\MarkOrdersAsDeliveredCommand',
                64 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\DispatchFundsCommand',
                65 => 'Wizacha\\AppBundle\\Command\\ImportAutomatedFeedsCommand',
                66 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\GenerateSitemapCommand',
                67 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\RecountProductInCategoriesCommand',
                68 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\RefreshNewPromotionsInSearchCommand',
                69 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\TrashAbandonedOrdersCommand',
                70 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\PayoutCompaniesCommand',
                71 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\OrderCheckAcceptationDelayCommand',
                72 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\PspCheckKycCommand',
                73 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\CatalogJsonExporterCommand',
                74 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\DebugRegistryCommand',
                75 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\DebugVariablesCommand',
                76 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeleteAllMultiVendorProductCommand',
                77 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\Janus\\JanusExportCommand',
                78 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\Janus\\JanusImportCommand',
                79 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeleteOrdersAmountsCommand',
                80 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\StripeRemoveConsumedMandatesCommand',
                81 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeploySetTransactionCompanyIdAndCurrencyCommand',
                82 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\CreateCompanyWalletCommand',
                83 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\ClearPaymentDataBeforeProductionCommand',
                84 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\CreateIndexAlgoliaCommand',
                85 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeleteOrganisationsCommand',
                86 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\TranslationMissingKeysCommand',
                87 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\ProductVisibilityCommand',
                88 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\OrderExecuteActionCommand',
                89 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\OrderListActionsCommand',
                90 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeploySetUsersPasswordsHistoryCommand',
                91 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\ListEximJobsImport',
                92 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\CancelEximJobsImport',
                93 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeployDebugCommand',
                94 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\OrderCompletedStatsCommand',
                95 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\OrderCreationStatsCommand',
                96 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\WorkflowToGraphvizCommand',
                97 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\ImportThemeTranslationsCommand',
                98 => 'console.command.public_alias.Wizacha\\AppBundle\\Command\\DeploySystemOptions',
                99 => 'console.command.public_alias.doctrine_cache.contains_command',
                100 => 'console.command.public_alias.doctrine_cache.delete_command',
                101 => 'console.command.public_alias.doctrine_cache.flush_command',
                102 => 'console.command.public_alias.doctrine_cache.stats_command',
            ],
        ];
    }

    protected function throw($message)
    {
        throw new RuntimeException($message);
    }
}
