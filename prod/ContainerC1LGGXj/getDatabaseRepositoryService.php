<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Bridge\Broadway\DatabaseRepository' shared service.

return $this->services['Wizacha\\Bridge\\Broadway\\DatabaseRepository'] = new \Wizacha\Bridge\Broadway\DatabaseRepository('basket_short', 'Wizacha\\Marketplace\\ReadModel\\Basket');
