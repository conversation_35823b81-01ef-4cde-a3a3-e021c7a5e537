<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Payment\Stripe\StripeConfig' shared service.

return $this->services['Wizacha\\Marketplace\\Payment\\Stripe\\StripeConfig'] = new \Wizacha\Marketplace\Payment\Stripe\StripeConfig($this->getEnv('STRIPE_SECRET_TOKEN'), 'EUR', 'Ovhcloud');
