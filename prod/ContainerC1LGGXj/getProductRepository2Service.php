<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\ReadModel\ProductRepository' shared service.

return $this->services['Wizacha\\Marketplace\\ReadModel\\ProductRepository'] = new \Wizacha\Marketplace\ReadModel\ProductRepository(($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()), new \Wizacha\Marketplace\ReadModel\Denormalizer\ReadModelDenormalizerRegistry(new RewindableGenerator(function () {
    yield 0 => ($this->privates['Wizacha\\Marketplace\\ReadModel\\Denormalizer\\ReadModelDenormalizerV1'] ?? ($this->privates['Wizacha\\Marketplace\\ReadModel\\Denormalizer\\ReadModelDenormalizerV1'] = new \Wizacha\Marketplace\ReadModel\Denormalizer\ReadModelDenormalizerV1()));
}, 1)), ($this->services['Wizacha\\Marketplace\\Metrics\\SandboxMetrics'] ?? $this->load('getSandboxMetricsService.php')));
