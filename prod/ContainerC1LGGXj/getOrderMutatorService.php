<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Order\OrderMutator' shared autowired service.

return $this->services['Wizacha\\Marketplace\\Order\\OrderMutator'] = new \Wizacha\Marketplace\Order\OrderMutator(($this->services['Wizacha\\Marketplace\\Order\\Action\\Cancel'] ?? $this->load('getCancelService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\MarkPaymentDefermentAsAuthorized'] ?? $this->load('getMarkPaymentDefermentAsAuthorizedService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\MarkPaymentDefermentAsRefused'] ?? $this->load('getMarkPaymentDefermentAsRefusedService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\MarkAsPaid'] ?? $this->load('getMarkAsPaidService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\Refuse'] ?? $this->load('getRefuseService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\MarkPaymentAsRefused'] ?? $this->load('getMarkPaymentAsRefusedService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\CommitTo'] ?? $this->load('getCommitToService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\Accept'] ?? $this->load('getAcceptService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\DeclareInvoiceNumberGeneratedElsewhere'] ?? $this->load('getDeclareInvoiceNumberGeneratedElsewhereService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\MarkAsShipped'] ?? $this->load('getMarkAsShippedService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\MarkAsDelivered'] ?? $this->load('getMarkAsDeliveredService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\EndWithdrawalPeriod'] ?? $this->load('getEndWithdrawalPeriodService.php')));
