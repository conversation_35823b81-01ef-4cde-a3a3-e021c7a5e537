<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private '.errored..service_locator.mx_yHOn.Symfony\Component\HttpFoundation\Response' shared service.

$this->throw('Cannot autowire service ".service_locator.mx_yHOn": it references class "Symfony\\Component\\HttpFoundation\\Response" but no such service exists.');
