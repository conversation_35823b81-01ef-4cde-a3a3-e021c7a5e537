<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'console.command.public_alias.Wizacha\AppBundle\Command\LemonWayCheckKycCommand' shared autowired service.

$this->services['console.command.public_alias.Wizacha\\AppBundle\\Command\\LemonWayCheckKycCommand'] = $instance = new \Wizacha\AppBundle\Command\LemonWayCheckKycCommand(($this->services['Wizacha\\Marketplace\\Payment\\Processor\\LemonWay'] ?? $this->load('getLemonWayService.php')), ($this->services['Wizacha\\Marketplace\\Company\\CompanyService'] ?? $this->load('getCompanyServiceService.php')));

$instance->setLogger(($this->services['logger'] ?? $this->load('getLoggerService.php')));

return $instance;
