<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'HiPay\Fullservice\HTTP\Configuration\Configuration' shared service.

$this->services['HiPay\\Fullservice\\HTTP\\Configuration\\Configuration'] = $instance = new \HiPay\Fullservice\HTTP\Configuration\Configuration(['apiUsername' => '94697050.secure-gateway.hipay-tpp.com', 'apiPassword' => 'Live_JfvkESTRxxtv50U18lRBaaET']);

$instance->setApiEnv('production');

return $instance;
