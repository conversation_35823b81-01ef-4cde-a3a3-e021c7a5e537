<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'order_amount_repository' shared service.

return $this->services['order_amount_repository'] = new \Wizacha\Marketplace\Order\AmountsCalculator\Repository\OrderAmountsRepository(($this->services['doctrine'] ?? $this->getDoctrineService()), 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts');
