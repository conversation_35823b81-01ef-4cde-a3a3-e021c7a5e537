<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Order\Action\Accept' shared autowired service.

return $this->services['Wizacha\\Marketplace\\Order\\Action\\Accept'] = new \Wizacha\Marketplace\Order\Action\Accept(($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()), ($this->services['Wizacha\\Marketplace\\Order\\Workflow\\WorkflowService'] ?? $this->load('getWorkflowServiceService.php')), ($this->services['logger'] ?? $this->load('getLoggerService.php')), ($this->services['marketplace.order.tracer'] ?? $this->load('getMarketplace_Order_TracerService.php')), ($this->services['Wizacha\\Marketplace\\Company\\CompanyService'] ?? $this->load('getCompanyServiceService.php')));
