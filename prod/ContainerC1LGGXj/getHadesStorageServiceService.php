<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Storage\HadesStorageService' shared service.

return $this->services['Wizacha\\Storage\\HadesStorageService'] = ($this->privates['Wizacha\\Storage\\StorageFactory'] ?? $this->load('getStorageFactoryService.php'))->__invoke('hades_backup', ['prefix' => 'hades_backup', 'dir' => '/var', 'secured' => true]);
