<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Notification\OrderNotifier' shared service.

$this->services['Wizacha\\AppBundle\\Notification\\OrderNotifier'] = $instance = new \Wizacha\AppBundle\Notification\OrderNotifier(($this->services['app.mailer'] ?? $this->load('getApp_MailerService.php')), ($this->services['templating'] ?? $this->load('getTemplatingService.php')), ($this->services['logger'] ?? $this->load('getLoggerService.php')), ($this->services['Wizacha\\Marketplace\\AdminCompany'] ?? ($this->services['Wizacha\\Marketplace\\AdminCompany'] = new \Wizacha\Marketplace\AdminCompany())), ($this->services['Wizacha\\Marketplace\\User\\UserRepository'] ?? $this->getUserRepositoryService()), ($this->services['router'] ?? $this->getRouterService()), ($this->services['marketplace.monolog.level.mailer'] ?? $this->load('getMarketplace_Monolog_Level_MailerService.php')), ($this->services['Wizacha\\Marketplace\\Order\\OrderService'] ?? $this->load('getOrderServiceService.php')));

$instance->setNotifyShipmentCreated(true);
$instance->setSubscription(true, ($this->services['Wizacha\\Marketplace\\Subscription\\SubscriptionService'] ?? $this->load('getSubscriptionServiceService.php')));
$instance->setExternalFrontOfficeUrl('marketplace.ovhcloud.com');
$instance->setPriceFormatter(($this->services['marketplace.backend.price.formatter'] ?? ($this->services['marketplace.backend.price.formatter'] = new \Wizacha\Marketplace\Price\DetailedPriceFormatter('EUR'))));

return $instance;
