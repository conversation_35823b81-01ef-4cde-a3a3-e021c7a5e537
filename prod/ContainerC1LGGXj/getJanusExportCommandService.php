<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'console.command.public_alias.Wizacha\AppBundle\Command\Janus\JanusExportCommand' shared autowired service.

return $this->services['console.command.public_alias.Wizacha\\AppBundle\\Command\\Janus\\JanusExportCommand'] = new \Wizacha\AppBundle\Command\Janus\JanusExportCommand(($this->privates['Wizacha\\Janus\\JanusService'] ?? $this->load('getJanusServiceService.php')));
