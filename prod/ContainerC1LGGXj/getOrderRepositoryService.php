<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'Wizacha\Marketplace\Order\Repository\OrderRepository' shared autowired service.

return $this->privates['Wizacha\\Marketplace\\Order\\Repository\\OrderRepository'] = new \Wizacha\Marketplace\Order\Repository\OrderRepository(($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()));
