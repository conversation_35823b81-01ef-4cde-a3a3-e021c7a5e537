<?php

class BlobRestProxy_4f514e3 extends \MicrosoftAzure\Storage\Blob\BlobRestProxy implements \ProxyManager\Proxy\VirtualProxyInterface
{
    private $valueHoldera1fd8 = null;
    private $initializere3996 = null;
    private static $publicProperties7ed0d = [
        
    ];
    public function getSingleBlobUploadThresholdInBytes()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getSingleBlobUploadThresholdInBytes', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getSingleBlobUploadThresholdInBytes();
    }
    public function getBlockSize()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getBlockSize', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getBlockSize();
    }
    public function setSingleBlobUploadThresholdInBytes($val)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'setSingleBlobUploadThresholdInBytes', array('val' => $val), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->setSingleBlobUploadThresholdInBytes($val);
    }
    public function setBlockSize($val)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'setBlockSize', array('val' => $val), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->setBlockSize($val);
    }
    public function getBlobUrl($container, $blob)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getBlobUrl', array('container' => $container, 'blob' => $blob), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getBlobUrl($container, $blob);
    }
    public function listContainers(?\MicrosoftAzure\Storage\Blob\Models\ListContainersOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'listContainers', array('options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->listContainers($options);
    }
    public function listContainersAsync(?\MicrosoftAzure\Storage\Blob\Models\ListContainersOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'listContainersAsync', array('options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->listContainersAsync($options);
    }
    public function createContainer($container, ?\MicrosoftAzure\Storage\Blob\Models\CreateContainerOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'createContainer', array('container' => $container, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->createContainer($container, $options);
    }
    public function createContainerAsync($container, ?\MicrosoftAzure\Storage\Blob\Models\CreateContainerOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'createContainerAsync', array('container' => $container, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->createContainerAsync($container, $options);
    }
    public function deleteContainer($container, ?\MicrosoftAzure\Storage\Blob\Models\BlobServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'deleteContainer', array('container' => $container, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->deleteContainer($container, $options);
    }
    public function deleteContainerAsync($container, ?\MicrosoftAzure\Storage\Blob\Models\BlobServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'deleteContainerAsync', array('container' => $container, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->deleteContainerAsync($container, $options);
    }
    public function getContainerProperties($container, ?\MicrosoftAzure\Storage\Blob\Models\BlobServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getContainerProperties', array('container' => $container, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getContainerProperties($container, $options);
    }
    public function getContainerPropertiesAsync($container, ?\MicrosoftAzure\Storage\Blob\Models\BlobServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getContainerPropertiesAsync', array('container' => $container, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getContainerPropertiesAsync($container, $options);
    }
    public function getContainerMetadata($container, ?\MicrosoftAzure\Storage\Blob\Models\BlobServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getContainerMetadata', array('container' => $container, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getContainerMetadata($container, $options);
    }
    public function getContainerMetadataAsync($container, ?\MicrosoftAzure\Storage\Blob\Models\BlobServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getContainerMetadataAsync', array('container' => $container, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getContainerMetadataAsync($container, $options);
    }
    public function getContainerAcl($container, ?\MicrosoftAzure\Storage\Blob\Models\BlobServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getContainerAcl', array('container' => $container, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getContainerAcl($container, $options);
    }
    public function getContainerAclAsync($container, ?\MicrosoftAzure\Storage\Blob\Models\BlobServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getContainerAclAsync', array('container' => $container, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getContainerAclAsync($container, $options);
    }
    public function setContainerAcl($container, \MicrosoftAzure\Storage\Blob\Models\ContainerACL $acl, ?\MicrosoftAzure\Storage\Blob\Models\BlobServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'setContainerAcl', array('container' => $container, 'acl' => $acl, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->setContainerAcl($container, $acl, $options);
    }
    public function setContainerAclAsync($container, \MicrosoftAzure\Storage\Blob\Models\ContainerACL $acl, ?\MicrosoftAzure\Storage\Blob\Models\BlobServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'setContainerAclAsync', array('container' => $container, 'acl' => $acl, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->setContainerAclAsync($container, $acl, $options);
    }
    public function setContainerMetadata($container, array $metadata, ?\MicrosoftAzure\Storage\Blob\Models\BlobServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'setContainerMetadata', array('container' => $container, 'metadata' => $metadata, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->setContainerMetadata($container, $metadata, $options);
    }
    public function setContainerMetadataAsync($container, array $metadata, ?\MicrosoftAzure\Storage\Blob\Models\BlobServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'setContainerMetadataAsync', array('container' => $container, 'metadata' => $metadata, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->setContainerMetadataAsync($container, $metadata, $options);
    }
    public function setBlobTier($container, $blob, ?\MicrosoftAzure\Storage\Blob\Models\SetBlobTierOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'setBlobTier', array('container' => $container, 'blob' => $blob, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->setBlobTier($container, $blob, $options);
    }
    public function setBlobTierAsync($container, $blob, ?\MicrosoftAzure\Storage\Blob\Models\SetBlobTierOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'setBlobTierAsync', array('container' => $container, 'blob' => $blob, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->setBlobTierAsync($container, $blob, $options);
    }
    public function listBlobs($container, ?\MicrosoftAzure\Storage\Blob\Models\ListBlobsOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'listBlobs', array('container' => $container, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->listBlobs($container, $options);
    }
    public function listBlobsAsync($container, ?\MicrosoftAzure\Storage\Blob\Models\ListBlobsOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'listBlobsAsync', array('container' => $container, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->listBlobsAsync($container, $options);
    }
    public function createPageBlob($container, $blob, $length, ?\MicrosoftAzure\Storage\Blob\Models\CreatePageBlobOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'createPageBlob', array('container' => $container, 'blob' => $blob, 'length' => $length, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->createPageBlob($container, $blob, $length, $options);
    }
    public function createPageBlobAsync($container, $blob, $length, ?\MicrosoftAzure\Storage\Blob\Models\CreatePageBlobOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'createPageBlobAsync', array('container' => $container, 'blob' => $blob, 'length' => $length, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->createPageBlobAsync($container, $blob, $length, $options);
    }
    public function createAppendBlob($container, $blob, ?\MicrosoftAzure\Storage\Blob\Models\CreateBlobOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'createAppendBlob', array('container' => $container, 'blob' => $blob, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->createAppendBlob($container, $blob, $options);
    }
    public function createAppendBlobAsync($container, $blob, ?\MicrosoftAzure\Storage\Blob\Models\CreateBlobOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'createAppendBlobAsync', array('container' => $container, 'blob' => $blob, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->createAppendBlobAsync($container, $blob, $options);
    }
    public function createBlockBlob($container, $blob, $content, ?\MicrosoftAzure\Storage\Blob\Models\CreateBlockBlobOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'createBlockBlob', array('container' => $container, 'blob' => $blob, 'content' => $content, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->createBlockBlob($container, $blob, $content, $options);
    }
    public function createBlockBlobAsync($container, $blob, $content, ?\MicrosoftAzure\Storage\Blob\Models\CreateBlockBlobOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'createBlockBlobAsync', array('container' => $container, 'blob' => $blob, 'content' => $content, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->createBlockBlobAsync($container, $blob, $content, $options);
    }
    public function createPageBlobFromContent($container, $blob, $length, $content, ?\MicrosoftAzure\Storage\Blob\Models\CreatePageBlobFromContentOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'createPageBlobFromContent', array('container' => $container, 'blob' => $blob, 'length' => $length, 'content' => $content, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->createPageBlobFromContent($container, $blob, $length, $content, $options);
    }
    public function createPageBlobFromContentAsync($container, $blob, $length, $content, ?\MicrosoftAzure\Storage\Blob\Models\CreatePageBlobFromContentOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'createPageBlobFromContentAsync', array('container' => $container, 'blob' => $blob, 'length' => $length, 'content' => $content, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->createPageBlobFromContentAsync($container, $blob, $length, $content, $options);
    }
    public function clearBlobPages($container, $blob, \MicrosoftAzure\Storage\Common\Models\Range $range, ?\MicrosoftAzure\Storage\Blob\Models\CreateBlobPagesOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'clearBlobPages', array('container' => $container, 'blob' => $blob, 'range' => $range, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->clearBlobPages($container, $blob, $range, $options);
    }
    public function clearBlobPagesAsync($container, $blob, \MicrosoftAzure\Storage\Common\Models\Range $range, ?\MicrosoftAzure\Storage\Blob\Models\CreateBlobPagesOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'clearBlobPagesAsync', array('container' => $container, 'blob' => $blob, 'range' => $range, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->clearBlobPagesAsync($container, $blob, $range, $options);
    }
    public function createBlobPages($container, $blob, \MicrosoftAzure\Storage\Common\Models\Range $range, $content, ?\MicrosoftAzure\Storage\Blob\Models\CreateBlobPagesOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'createBlobPages', array('container' => $container, 'blob' => $blob, 'range' => $range, 'content' => $content, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->createBlobPages($container, $blob, $range, $content, $options);
    }
    public function createBlobPagesAsync($container, $blob, \MicrosoftAzure\Storage\Common\Models\Range $range, $content, ?\MicrosoftAzure\Storage\Blob\Models\CreateBlobPagesOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'createBlobPagesAsync', array('container' => $container, 'blob' => $blob, 'range' => $range, 'content' => $content, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->createBlobPagesAsync($container, $blob, $range, $content, $options);
    }
    public function createBlobBlock($container, $blob, $blockId, $content, ?\MicrosoftAzure\Storage\Blob\Models\CreateBlobBlockOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'createBlobBlock', array('container' => $container, 'blob' => $blob, 'blockId' => $blockId, 'content' => $content, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->createBlobBlock($container, $blob, $blockId, $content, $options);
    }
    public function createBlobBlockAsync($container, $blob, $blockId, $content, ?\MicrosoftAzure\Storage\Blob\Models\CreateBlobBlockOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'createBlobBlockAsync', array('container' => $container, 'blob' => $blob, 'blockId' => $blockId, 'content' => $content, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->createBlobBlockAsync($container, $blob, $blockId, $content, $options);
    }
    public function appendBlock($container, $blob, $content, ?\MicrosoftAzure\Storage\Blob\Models\AppendBlockOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'appendBlock', array('container' => $container, 'blob' => $blob, 'content' => $content, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->appendBlock($container, $blob, $content, $options);
    }
    public function appendBlockAsync($container, $blob, $content, ?\MicrosoftAzure\Storage\Blob\Models\AppendBlockOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'appendBlockAsync', array('container' => $container, 'blob' => $blob, 'content' => $content, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->appendBlockAsync($container, $blob, $content, $options);
    }
    public function commitBlobBlocks($container, $blob, $blockList, ?\MicrosoftAzure\Storage\Blob\Models\CommitBlobBlocksOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'commitBlobBlocks', array('container' => $container, 'blob' => $blob, 'blockList' => $blockList, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->commitBlobBlocks($container, $blob, $blockList, $options);
    }
    public function commitBlobBlocksAsync($container, $blob, $blockList, ?\MicrosoftAzure\Storage\Blob\Models\CommitBlobBlocksOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'commitBlobBlocksAsync', array('container' => $container, 'blob' => $blob, 'blockList' => $blockList, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->commitBlobBlocksAsync($container, $blob, $blockList, $options);
    }
    public function listBlobBlocks($container, $blob, ?\MicrosoftAzure\Storage\Blob\Models\ListBlobBlocksOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'listBlobBlocks', array('container' => $container, 'blob' => $blob, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->listBlobBlocks($container, $blob, $options);
    }
    public function listBlobBlocksAsync($container, $blob, ?\MicrosoftAzure\Storage\Blob\Models\ListBlobBlocksOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'listBlobBlocksAsync', array('container' => $container, 'blob' => $blob, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->listBlobBlocksAsync($container, $blob, $options);
    }
    public function getBlobProperties($container, $blob, ?\MicrosoftAzure\Storage\Blob\Models\GetBlobPropertiesOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getBlobProperties', array('container' => $container, 'blob' => $blob, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getBlobProperties($container, $blob, $options);
    }
    public function getBlobPropertiesAsync($container, $blob, ?\MicrosoftAzure\Storage\Blob\Models\GetBlobPropertiesOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getBlobPropertiesAsync', array('container' => $container, 'blob' => $blob, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getBlobPropertiesAsync($container, $blob, $options);
    }
    public function getBlobMetadata($container, $blob, ?\MicrosoftAzure\Storage\Blob\Models\GetBlobMetadataOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getBlobMetadata', array('container' => $container, 'blob' => $blob, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getBlobMetadata($container, $blob, $options);
    }
    public function getBlobMetadataAsync($container, $blob, ?\MicrosoftAzure\Storage\Blob\Models\GetBlobMetadataOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getBlobMetadataAsync', array('container' => $container, 'blob' => $blob, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getBlobMetadataAsync($container, $blob, $options);
    }
    public function listPageBlobRanges($container, $blob, ?\MicrosoftAzure\Storage\Blob\Models\ListPageBlobRangesOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'listPageBlobRanges', array('container' => $container, 'blob' => $blob, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->listPageBlobRanges($container, $blob, $options);
    }
    public function listPageBlobRangesAsync($container, $blob, ?\MicrosoftAzure\Storage\Blob\Models\ListPageBlobRangesOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'listPageBlobRangesAsync', array('container' => $container, 'blob' => $blob, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->listPageBlobRangesAsync($container, $blob, $options);
    }
    public function listPageBlobRangesDiff($container, $blob, $previousSnapshotTime, ?\MicrosoftAzure\Storage\Blob\Models\ListPageBlobRangesOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'listPageBlobRangesDiff', array('container' => $container, 'blob' => $blob, 'previousSnapshotTime' => $previousSnapshotTime, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->listPageBlobRangesDiff($container, $blob, $previousSnapshotTime, $options);
    }
    public function listPageBlobRangesDiffAsync($container, $blob, $previousSnapshotTime, ?\MicrosoftAzure\Storage\Blob\Models\ListPageBlobRangesOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'listPageBlobRangesDiffAsync', array('container' => $container, 'blob' => $blob, 'previousSnapshotTime' => $previousSnapshotTime, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->listPageBlobRangesDiffAsync($container, $blob, $previousSnapshotTime, $options);
    }
    public function setBlobProperties($container, $blob, ?\MicrosoftAzure\Storage\Blob\Models\SetBlobPropertiesOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'setBlobProperties', array('container' => $container, 'blob' => $blob, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->setBlobProperties($container, $blob, $options);
    }
    public function setBlobPropertiesAsync($container, $blob, ?\MicrosoftAzure\Storage\Blob\Models\SetBlobPropertiesOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'setBlobPropertiesAsync', array('container' => $container, 'blob' => $blob, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->setBlobPropertiesAsync($container, $blob, $options);
    }
    public function setBlobMetadata($container, $blob, array $metadata, ?\MicrosoftAzure\Storage\Blob\Models\BlobServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'setBlobMetadata', array('container' => $container, 'blob' => $blob, 'metadata' => $metadata, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->setBlobMetadata($container, $blob, $metadata, $options);
    }
    public function setBlobMetadataAsync($container, $blob, array $metadata, ?\MicrosoftAzure\Storage\Blob\Models\BlobServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'setBlobMetadataAsync', array('container' => $container, 'blob' => $blob, 'metadata' => $metadata, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->setBlobMetadataAsync($container, $blob, $metadata, $options);
    }
    public function saveBlobToFile($path, $container, $blob, ?\MicrosoftAzure\Storage\Blob\Models\GetBlobOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'saveBlobToFile', array('path' => $path, 'container' => $container, 'blob' => $blob, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->saveBlobToFile($path, $container, $blob, $options);
    }
    public function saveBlobToFileAsync($path, $container, $blob, ?\MicrosoftAzure\Storage\Blob\Models\GetBlobOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'saveBlobToFileAsync', array('path' => $path, 'container' => $container, 'blob' => $blob, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->saveBlobToFileAsync($path, $container, $blob, $options);
    }
    public function getBlob($container, $blob, ?\MicrosoftAzure\Storage\Blob\Models\GetBlobOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getBlob', array('container' => $container, 'blob' => $blob, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getBlob($container, $blob, $options);
    }
    public function getBlobAsync($container, $blob, ?\MicrosoftAzure\Storage\Blob\Models\GetBlobOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getBlobAsync', array('container' => $container, 'blob' => $blob, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getBlobAsync($container, $blob, $options);
    }
    public function undeleteBlob($container, $blob, ?\MicrosoftAzure\Storage\Blob\Models\UndeleteBlobOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'undeleteBlob', array('container' => $container, 'blob' => $blob, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->undeleteBlob($container, $blob, $options);
    }
    public function undeleteBlobAsync($container, $blob, ?\MicrosoftAzure\Storage\Blob\Models\UndeleteBlobOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'undeleteBlobAsync', array('container' => $container, 'blob' => $blob, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->undeleteBlobAsync($container, $blob, $options);
    }
    public function deleteBlob($container, $blob, ?\MicrosoftAzure\Storage\Blob\Models\DeleteBlobOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'deleteBlob', array('container' => $container, 'blob' => $blob, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->deleteBlob($container, $blob, $options);
    }
    public function deleteBlobAsync($container, $blob, ?\MicrosoftAzure\Storage\Blob\Models\DeleteBlobOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'deleteBlobAsync', array('container' => $container, 'blob' => $blob, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->deleteBlobAsync($container, $blob, $options);
    }
    public function createBlobSnapshot($container, $blob, ?\MicrosoftAzure\Storage\Blob\Models\CreateBlobSnapshotOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'createBlobSnapshot', array('container' => $container, 'blob' => $blob, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->createBlobSnapshot($container, $blob, $options);
    }
    public function createBlobSnapshotAsync($container, $blob, ?\MicrosoftAzure\Storage\Blob\Models\CreateBlobSnapshotOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'createBlobSnapshotAsync', array('container' => $container, 'blob' => $blob, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->createBlobSnapshotAsync($container, $blob, $options);
    }
    public function copyBlob($destinationContainer, $destinationBlob, $sourceContainer, $sourceBlob, ?\MicrosoftAzure\Storage\Blob\Models\CopyBlobOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'copyBlob', array('destinationContainer' => $destinationContainer, 'destinationBlob' => $destinationBlob, 'sourceContainer' => $sourceContainer, 'sourceBlob' => $sourceBlob, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->copyBlob($destinationContainer, $destinationBlob, $sourceContainer, $sourceBlob, $options);
    }
    public function copyBlobAsync($destinationContainer, $destinationBlob, $sourceContainer, $sourceBlob, ?\MicrosoftAzure\Storage\Blob\Models\CopyBlobOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'copyBlobAsync', array('destinationContainer' => $destinationContainer, 'destinationBlob' => $destinationBlob, 'sourceContainer' => $sourceContainer, 'sourceBlob' => $sourceBlob, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->copyBlobAsync($destinationContainer, $destinationBlob, $sourceContainer, $sourceBlob, $options);
    }
    public function copyBlobFromURL($destinationContainer, $destinationBlob, $sourceURL, ?\MicrosoftAzure\Storage\Blob\Models\CopyBlobFromURLOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'copyBlobFromURL', array('destinationContainer' => $destinationContainer, 'destinationBlob' => $destinationBlob, 'sourceURL' => $sourceURL, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->copyBlobFromURL($destinationContainer, $destinationBlob, $sourceURL, $options);
    }
    public function copyBlobFromURLAsync($destinationContainer, $destinationBlob, $sourceURL, ?\MicrosoftAzure\Storage\Blob\Models\CopyBlobFromURLOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'copyBlobFromURLAsync', array('destinationContainer' => $destinationContainer, 'destinationBlob' => $destinationBlob, 'sourceURL' => $sourceURL, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->copyBlobFromURLAsync($destinationContainer, $destinationBlob, $sourceURL, $options);
    }
    public function abortCopy($container, $blob, $copyId, ?\MicrosoftAzure\Storage\Blob\Models\BlobServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'abortCopy', array('container' => $container, 'blob' => $blob, 'copyId' => $copyId, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->abortCopy($container, $blob, $copyId, $options);
    }
    public function abortCopyAsync($container, $blob, $copyId, ?\MicrosoftAzure\Storage\Blob\Models\BlobServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'abortCopyAsync', array('container' => $container, 'blob' => $blob, 'copyId' => $copyId, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->abortCopyAsync($container, $blob, $copyId, $options);
    }
    public function acquireLease($container, $blob, $proposedLeaseId = null, $leaseDuration = null, ?\MicrosoftAzure\Storage\Blob\Models\BlobServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'acquireLease', array('container' => $container, 'blob' => $blob, 'proposedLeaseId' => $proposedLeaseId, 'leaseDuration' => $leaseDuration, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->acquireLease($container, $blob, $proposedLeaseId, $leaseDuration, $options);
    }
    public function acquireLeaseAsync($container, $blob, $proposedLeaseId = null, $leaseDuration = null, ?\MicrosoftAzure\Storage\Blob\Models\BlobServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'acquireLeaseAsync', array('container' => $container, 'blob' => $blob, 'proposedLeaseId' => $proposedLeaseId, 'leaseDuration' => $leaseDuration, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->acquireLeaseAsync($container, $blob, $proposedLeaseId, $leaseDuration, $options);
    }
    public function changeLease($container, $blob, $leaseId, $proposedLeaseId, ?\MicrosoftAzure\Storage\Blob\Models\BlobServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'changeLease', array('container' => $container, 'blob' => $blob, 'leaseId' => $leaseId, 'proposedLeaseId' => $proposedLeaseId, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->changeLease($container, $blob, $leaseId, $proposedLeaseId, $options);
    }
    public function changeLeaseAsync($container, $blob, $leaseId, $proposedLeaseId, ?\MicrosoftAzure\Storage\Blob\Models\BlobServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'changeLeaseAsync', array('container' => $container, 'blob' => $blob, 'leaseId' => $leaseId, 'proposedLeaseId' => $proposedLeaseId, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->changeLeaseAsync($container, $blob, $leaseId, $proposedLeaseId, $options);
    }
    public function renewLease($container, $blob, $leaseId, ?\MicrosoftAzure\Storage\Blob\Models\BlobServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'renewLease', array('container' => $container, 'blob' => $blob, 'leaseId' => $leaseId, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->renewLease($container, $blob, $leaseId, $options);
    }
    public function renewLeaseAsync($container, $blob, $leaseId, ?\MicrosoftAzure\Storage\Blob\Models\BlobServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'renewLeaseAsync', array('container' => $container, 'blob' => $blob, 'leaseId' => $leaseId, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->renewLeaseAsync($container, $blob, $leaseId, $options);
    }
    public function releaseLease($container, $blob, $leaseId, ?\MicrosoftAzure\Storage\Blob\Models\BlobServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'releaseLease', array('container' => $container, 'blob' => $blob, 'leaseId' => $leaseId, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->releaseLease($container, $blob, $leaseId, $options);
    }
    public function releaseLeaseAsync($container, $blob, $leaseId, ?\MicrosoftAzure\Storage\Blob\Models\BlobServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'releaseLeaseAsync', array('container' => $container, 'blob' => $blob, 'leaseId' => $leaseId, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->releaseLeaseAsync($container, $blob, $leaseId, $options);
    }
    public function breakLease($container, $blob, $breakPeriod = null, ?\MicrosoftAzure\Storage\Blob\Models\BlobServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'breakLease', array('container' => $container, 'blob' => $blob, 'breakPeriod' => $breakPeriod, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->breakLease($container, $blob, $breakPeriod, $options);
    }
    public function breakLeaseAsync($container, $blob, $breakPeriod = null, ?\MicrosoftAzure\Storage\Blob\Models\BlobServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'breakLeaseAsync', array('container' => $container, 'blob' => $blob, 'breakPeriod' => $breakPeriod, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->breakLeaseAsync($container, $blob, $breakPeriod, $options);
    }
    public function addOptionalAccessConditionHeader(array $headers, ?array $accessConditions = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'addOptionalAccessConditionHeader', array('headers' => $headers, 'accessConditions' => $accessConditions), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->addOptionalAccessConditionHeader($headers, $accessConditions);
    }
    public function addOptionalSourceAccessConditionHeader(array $headers, ?array $accessConditions = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'addOptionalSourceAccessConditionHeader', array('headers' => $headers, 'accessConditions' => $accessConditions), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->addOptionalSourceAccessConditionHeader($headers, $accessConditions);
    }
    public function getAccountName()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getAccountName', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getAccountName();
    }
    public function addPostParameter(array $postParameters, $key, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'addPostParameter', array('postParameters' => $postParameters, 'key' => $key, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->addPostParameter($postParameters, $key, $value);
    }
    public function generateMetadataHeaders(?array $metadata = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'generateMetadataHeaders', array('metadata' => $metadata), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->generateMetadataHeaders($metadata);
    }
    public function getPsrPrimaryUri()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getPsrPrimaryUri', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getPsrPrimaryUri();
    }
    public function getPsrSecondaryUri()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getPsrSecondaryUri', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getPsrSecondaryUri();
    }
    public function getMiddlewares()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getMiddlewares', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getMiddlewares();
    }
    public function pushMiddleware($middleware)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'pushMiddleware', array('middleware' => $middleware), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->pushMiddleware($middleware);
    }
    public function getServiceProperties(?\MicrosoftAzure\Storage\Common\Models\ServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getServiceProperties', array('options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getServiceProperties($options);
    }
    public function getServicePropertiesAsync(?\MicrosoftAzure\Storage\Common\Models\ServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getServicePropertiesAsync', array('options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getServicePropertiesAsync($options);
    }
    public function setServiceProperties(\MicrosoftAzure\Storage\Common\Models\ServiceProperties $serviceProperties, ?\MicrosoftAzure\Storage\Common\Models\ServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'setServiceProperties', array('serviceProperties' => $serviceProperties, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->setServiceProperties($serviceProperties, $options);
    }
    public function setServicePropertiesAsync(\MicrosoftAzure\Storage\Common\Models\ServiceProperties $serviceProperties, ?\MicrosoftAzure\Storage\Common\Models\ServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'setServicePropertiesAsync', array('serviceProperties' => $serviceProperties, 'options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->setServicePropertiesAsync($serviceProperties, $options);
    }
    public function getServiceStats(?\MicrosoftAzure\Storage\Common\Models\ServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getServiceStats', array('options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getServiceStats($options);
    }
    public function getServiceStatsAsync(?\MicrosoftAzure\Storage\Common\Models\ServiceOptions $options = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getServiceStatsAsync', array('options' => $options), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getServiceStatsAsync($options);
    }
    public static function staticProxyConstructor($initializer)
    {
        static $reflection;
        $reflection = $reflection ?? new \ReflectionClass(__CLASS__);
        $instance   = $reflection->newInstanceWithoutConstructor();
        unset($instance->dataSerializer);
        \Closure::bind(function (\MicrosoftAzure\Storage\Blob\BlobRestProxy $instance) {
            unset($instance->singleBlobUploadThresholdInBytes, $instance->blockSize);
        }, $instance, 'MicrosoftAzure\\Storage\\Blob\\BlobRestProxy')->__invoke($instance);
        \Closure::bind(function (\MicrosoftAzure\Storage\Common\Internal\ServiceRestProxy $instance) {
            unset($instance->accountName, $instance->psrPrimaryUri, $instance->psrSecondaryUri, $instance->options, $instance->client);
        }, $instance, 'MicrosoftAzure\\Storage\\Common\\Internal\\ServiceRestProxy')->__invoke($instance);
        \Closure::bind(function (\MicrosoftAzure\Storage\Common\Internal\RestProxy $instance) {
            unset($instance->middlewares);
        }, $instance, 'MicrosoftAzure\\Storage\\Common\\Internal\\RestProxy')->__invoke($instance);
        $instance->initializere3996 = $initializer;
        return $instance;
    }
    public function __construct($primaryUri, $secondaryUri, $accountName, array $options = [])
    {
        static $reflection;
        if (! $this->valueHoldera1fd8) {
            $reflection = $reflection ?? new \ReflectionClass('MicrosoftAzure\\Storage\\Blob\\BlobRestProxy');
            $this->valueHoldera1fd8 = $reflection->newInstanceWithoutConstructor();
        unset($this->dataSerializer);
        \Closure::bind(function (\MicrosoftAzure\Storage\Blob\BlobRestProxy $instance) {
            unset($instance->singleBlobUploadThresholdInBytes, $instance->blockSize);
        }, $this, 'MicrosoftAzure\\Storage\\Blob\\BlobRestProxy')->__invoke($this);
        \Closure::bind(function (\MicrosoftAzure\Storage\Common\Internal\ServiceRestProxy $instance) {
            unset($instance->accountName, $instance->psrPrimaryUri, $instance->psrSecondaryUri, $instance->options, $instance->client);
        }, $this, 'MicrosoftAzure\\Storage\\Common\\Internal\\ServiceRestProxy')->__invoke($this);
        \Closure::bind(function (\MicrosoftAzure\Storage\Common\Internal\RestProxy $instance) {
            unset($instance->middlewares);
        }, $this, 'MicrosoftAzure\\Storage\\Common\\Internal\\RestProxy')->__invoke($this);
        }
        $this->valueHoldera1fd8->__construct($primaryUri, $secondaryUri, $accountName, $options);
    }
    public function & __get($name)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '__get', ['name' => $name], $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        if (isset(self::$publicProperties7ed0d[$name])) {
            return $this->valueHoldera1fd8->$name;
        }
        $realInstanceReflection = new \ReflectionClass('MicrosoftAzure\\Storage\\Blob\\BlobRestProxy');
        if (! $realInstanceReflection->hasProperty($name)) {
            $targetObject = $this->valueHoldera1fd8;
            $backtrace = debug_backtrace(false, 1);
            trigger_error(
                sprintf(
                    'Undefined property: %s::$%s in %s on line %s',
                    $realInstanceReflection->getName(),
                    $name,
                    $backtrace[0]['file'],
                    $backtrace[0]['line']
                ),
                \E_USER_NOTICE
            );
            return $targetObject->$name;
        }
        $targetObject = $this->valueHoldera1fd8;
        $accessor = function & () use ($targetObject, $name) {
            return $targetObject->$name;
        };
        $backtrace = debug_backtrace(true, 2);
        $scopeObject = isset($backtrace[1]['object']) ? $backtrace[1]['object'] : new \ProxyManager\Stub\EmptyClassStub();
        $accessor = $accessor->bindTo($scopeObject, get_class($scopeObject));
        $returnValue = & $accessor();
        return $returnValue;
    }
    public function __set($name, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '__set', array('name' => $name, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        $realInstanceReflection = new \ReflectionClass('MicrosoftAzure\\Storage\\Blob\\BlobRestProxy');
        if (! $realInstanceReflection->hasProperty($name)) {
            $targetObject = $this->valueHoldera1fd8;
            $targetObject->$name = $value;
            return $targetObject->$name;
        }
        $targetObject = $this->valueHoldera1fd8;
        $accessor = function & () use ($targetObject, $name, $value) {
            $targetObject->$name = $value;
            return $targetObject->$name;
        };
        $backtrace = debug_backtrace(true, 2);
        $scopeObject = isset($backtrace[1]['object']) ? $backtrace[1]['object'] : new \ProxyManager\Stub\EmptyClassStub();
        $accessor = $accessor->bindTo($scopeObject, get_class($scopeObject));
        $returnValue = & $accessor();
        return $returnValue;
    }
    public function __isset($name)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '__isset', array('name' => $name), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        $realInstanceReflection = new \ReflectionClass('MicrosoftAzure\\Storage\\Blob\\BlobRestProxy');
        if (! $realInstanceReflection->hasProperty($name)) {
            $targetObject = $this->valueHoldera1fd8;
            return isset($targetObject->$name);
        }
        $targetObject = $this->valueHoldera1fd8;
        $accessor = function () use ($targetObject, $name) {
            return isset($targetObject->$name);
        };
        $backtrace = debug_backtrace(true, 2);
        $scopeObject = isset($backtrace[1]['object']) ? $backtrace[1]['object'] : new \ProxyManager\Stub\EmptyClassStub();
        $accessor = $accessor->bindTo($scopeObject, get_class($scopeObject));
        $returnValue = $accessor();
        return $returnValue;
    }
    public function __unset($name)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '__unset', array('name' => $name), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        $realInstanceReflection = new \ReflectionClass('MicrosoftAzure\\Storage\\Blob\\BlobRestProxy');
        if (! $realInstanceReflection->hasProperty($name)) {
            $targetObject = $this->valueHoldera1fd8;
            unset($targetObject->$name);
            return;
        }
        $targetObject = $this->valueHoldera1fd8;
        $accessor = function () use ($targetObject, $name) {
            unset($targetObject->$name);
            return;
        };
        $backtrace = debug_backtrace(true, 2);
        $scopeObject = isset($backtrace[1]['object']) ? $backtrace[1]['object'] : new \ProxyManager\Stub\EmptyClassStub();
        $accessor = $accessor->bindTo($scopeObject, get_class($scopeObject));
        $accessor();
    }
    public function __clone()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '__clone', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        $this->valueHoldera1fd8 = clone $this->valueHoldera1fd8;
    }
    public function __sleep()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '__sleep', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return array('valueHoldera1fd8');
    }
    public function __wakeup()
    {
        unset($this->dataSerializer);
        \Closure::bind(function (\MicrosoftAzure\Storage\Blob\BlobRestProxy $instance) {
            unset($instance->singleBlobUploadThresholdInBytes, $instance->blockSize);
        }, $this, 'MicrosoftAzure\\Storage\\Blob\\BlobRestProxy')->__invoke($this);
        \Closure::bind(function (\MicrosoftAzure\Storage\Common\Internal\ServiceRestProxy $instance) {
            unset($instance->accountName, $instance->psrPrimaryUri, $instance->psrSecondaryUri, $instance->options, $instance->client);
        }, $this, 'MicrosoftAzure\\Storage\\Common\\Internal\\ServiceRestProxy')->__invoke($this);
        \Closure::bind(function (\MicrosoftAzure\Storage\Common\Internal\RestProxy $instance) {
            unset($instance->middlewares);
        }, $this, 'MicrosoftAzure\\Storage\\Common\\Internal\\RestProxy')->__invoke($this);
    }
    public function setProxyInitializer(\Closure $initializer = null) : void
    {
        $this->initializere3996 = $initializer;
    }
    public function getProxyInitializer() : ?\Closure
    {
        return $this->initializere3996;
    }
    public function initializeProxy() : bool
    {
        return $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'initializeProxy', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
    }
    public function isProxyInitialized() : bool
    {
        return null !== $this->valueHoldera1fd8;
    }
    public function getWrappedValueHolderValue()
    {
        return $this->valueHoldera1fd8;
    }
}
