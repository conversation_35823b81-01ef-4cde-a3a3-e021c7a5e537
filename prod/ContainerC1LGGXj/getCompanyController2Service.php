<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\CompanyController' shared autowired service.

$this->services['Wizacha\\AppBundle\\Controller\\Api\\CompanyController'] = $instance = new \Wizacha\AppBundle\Controller\Api\CompanyController(($this->services['Wizacha\\Marketplace\\Company\\CompanyService'] ?? $this->load('getCompanyServiceService.php')), ($this->services['marketplace.division.blacklists.service'] ?? $this->load('getMarketplace_Division_Blacklists_ServiceService.php')), ($this->services['Wizacha\\Marketplace\\Division\\Service\\DivisionService'] ?? $this->load('getDivisionServiceService.php')), ($this->services['Wizacha\\Marketplace\\Subscription\\SubscriptionRepository'] ?? $this->getSubscriptionRepositoryService()), ($this->services['validator'] ?? $this->load('getValidatorService.php')), ($this->services['Wizacha\\Marketplace\\Division\\Service\\DivisionSettingsService'] ?? $this->load('getDivisionSettingsServiceService.php')), ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\MangoPay'] ?? $this->load('getMangoPayService.php')), ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\PayoutService'] ?? $this->load('getPayoutServiceService.php')));

$instance->setUserService(($this->services['Wizacha\\Marketplace\\User\\UserService'] ?? $this->getUserServiceService()));

return $instance;
