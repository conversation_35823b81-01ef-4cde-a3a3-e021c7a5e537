<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'Wizacha\AppBundle\Command\KPIPublishCommand' shared autowired service.

$this->privates['Wizacha\\AppBundle\\Command\\KPIPublishCommand'] = $instance = new \Wizacha\AppBundle\Command\KPIPublishCommand(new \Wizacha\Marketplace\KPI\KPIPublishService('https://ckb5xzmas0.execute-api.eu-west-3.amazonaws.com/kpiMarketplaces', 'Ovhcloud', ($this->services['app.validator.url_validator'] ?? ($this->services['app.validator.url_validator'] = new \Wizacha\AppBundle\Validator\UrlValidator())), ($this->privates['Wizacha\\Marketplace\\Payment\\Processor\\ProcessorService'] ?? $this->load('getProcessorServiceService.php')), ($this->services['Wizacha\\Marketplace\\PIM\\Product\\ProductRepository'] ?? $this->load('getProductRepositoryService.php')), ($this->services['Wizacha\\Marketplace\\Company\\CompanyRepository'] ?? $this->load('getCompanyRepositoryService.php')), ($this->privates['Wizacha\\Marketplace\\Order\\Repository\\OrderRepository'] ?? $this->load('getOrderRepositoryService.php'))), ($this->services['logger'] ?? $this->load('getLoggerService.php')));

$instance->setName('kpi:publish');

return $instance;
