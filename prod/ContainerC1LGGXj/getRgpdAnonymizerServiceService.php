<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\RgpdAnonymizer\RgpdAnonymizerService' shared autowired service.

return $this->services['Wizacha\\Marketplace\\RgpdAnonymizer\\RgpdAnonymizerService'] = new \Wizacha\Marketplace\RgpdAnonymizer\RgpdAnonymizerService(new \Wizacha\Marketplace\RgpdAnonymizer\RgpdAnonymizerFactory(new RewindableGenerator(function () {
    yield 0 => ($this->privates['Wizacha\\Marketplace\\RgpdAnonymizer\\RgpdCompanyAnonymizer'] ?? $this->load('getRgpdCompanyAnonymizerService.php'));
    yield 1 => ($this->privates['Wizacha\\Marketplace\\RgpdAnonymizer\\RgpdUserAnonymizer'] ?? $this->load('getRgpdUserAnonymizerService.php'));
}, 2)));
