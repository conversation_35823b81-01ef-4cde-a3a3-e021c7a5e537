<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Payment\Stripe\StripeApi' shared autowired service.

$a = ($this->services['monolog.logger.psp'] ?? $this->getMonolog_Logger_PspService());
$b = ($this->services['serializer'] ?? $this->load('getSerializerService.php'));

$c = new \Wizacha\Marketplace\Payment\Stripe\StripeLogger($a, $b);
$c->setDataAnonymizer(new \Wizacha\Marketplace\Payment\Security\StripeDataAnonymizer());

return $this->services['Wizacha\\Marketplace\\Payment\\Stripe\\StripeApi'] = new \Wizacha\Marketplace\Payment\Stripe\StripeApi($c, new \Wizacha\Marketplace\Payment\Stripe\StripeHTTPClient($a, $b));
