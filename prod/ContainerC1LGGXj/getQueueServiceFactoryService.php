<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Async\QueueServiceFactory' shared service.

return $this->services['Wizacha\\Async\\QueueServiceFactory'] = new \Wizacha\Async\QueueServiceFactory(new RewindableGenerator(function () {
    yield 0 => ($this->services['Wizacha\\Async\\AmqpQueue'] ?? $this->load('getAmqpQueueService.php'));
    yield 1 => ($this->services['Wizacha\\Async\\SqsQueue'] ?? $this->load('getSqsQueueService.php'));
}, 2), 'amqp');
