<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'Wizacha\Marketplace\Division\CompanyDivisionSettings' shared autowired service.

$a = ($this->privates['Wizacha\\Marketplace\\Division\\DivisionSet'] ?? ($this->privates['Wizacha\\Marketplace\\Division\\DivisionSet'] = new \Wizacha\Marketplace\Division\DivisionSet()));

return $this->privates['Wizacha\\Marketplace\\Division\\CompanyDivisionSettings'] = new \Wizacha\Marketplace\Division\CompanyDivisionSettings(($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()), ($this->services['marketplace.division.division_repository'] ?? $this->load('getMarketplace_Division_DivisionRepositoryService.php')), $a, $a, ($this->services['marketplace.division.blacklists.service'] ?? $this->load('getMarketplace_Division_Blacklists_ServiceService.php')));
