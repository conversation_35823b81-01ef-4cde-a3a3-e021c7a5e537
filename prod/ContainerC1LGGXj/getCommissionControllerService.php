<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\CommissionController' shared autowired service.

return $this->services['Wizacha\\AppBundle\\Controller\\Api\\CommissionController'] = new \Wizacha\AppBundle\Controller\Api\CommissionController(($this->services['Wizacha\\Marketplace\\Commission\\CommissionService'] ?? $this->load('getCommissionServiceService.php')), ($this->services['marketplace.pim.category_service'] ?? $this->load('getMarketplace_Pim_CategoryServiceService.php')), ($this->services['Wizacha\\Marketplace\\Company\\CompanyService'] ?? $this->load('getCompanyServiceService.php')), ($this->services['marketplace.commission.commission_repository'] ?? $this->load('getMarketplace_Commission_CommissionRepositoryService.php')));
