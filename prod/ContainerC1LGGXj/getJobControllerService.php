<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\JobController' shared autowired service.

return $this->services['Wizacha\\AppBundle\\Controller\\Api\\JobController'] = new \Wizacha\AppBundle\Controller\Api\JobController(($this->services['marketplace.import.job_service'] ?? $this->load('getMarketplace_Import_JobServiceService.php')));
