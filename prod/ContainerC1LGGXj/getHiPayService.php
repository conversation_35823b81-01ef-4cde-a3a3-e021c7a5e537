<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Payment\Processor\HiPay' shared autowired service.

$a = ($this->services['Wizacha\\Marketplace\\Subscription\\SubscriptionService'] ?? $this->load('getSubscriptionServiceService.php'));

if (isset($this->services['Wizacha\\Marketplace\\Payment\\Processor\\HiPay'])) {
    return $this->services['Wizacha\\Marketplace\\Payment\\Processor\\HiPay'];
}

$this->services['Wizacha\\Marketplace\\Payment\\Processor\\HiPay'] = $instance = new \Wizacha\Marketplace\Payment\Processor\HiPay(($this->services['Wizacha\\Marketplace\\Commission\\CommissionService'] ?? $this->load('getCommissionServiceService.php')), ($this->services['router'] ?? $this->getRouterService()), ($this->services['logger'] ?? $this->load('getLoggerService.php')), ($this->services['Wizacha\\Marketplace\\Order\\OrderService'] ?? $this->load('getOrderServiceService.php')), 'Ovhcloud', 14744799, $a, ($this->services['marketplace.payment.user_payment_info_service'] ?? $this->load('getMarketplace_Payment_UserPaymentInfoServiceService.php')), 'visa,mastercard,cb', ($this->services['Wizacha\\Marketplace\\User\\UserService'] ?? $this->getUserServiceService()), ($this->services['Wizacha\\AppBundle\\Security\\User\\UserService'] ?? $this->load('getUserService2Service.php')), ($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()));

$instance->setApi(($this->services['marketplace.payment.hipay_api'] ?? $this->load('getMarketplace_Payment_HipayApiService.php')));
$instance->setWalletApi(($this->services['Wizacha\\Marketplace\\Payment\\HiPay\\Wallet\\HiPayWalletApi'] ?? $this->load('getHiPayWalletApiService.php')));
$instance->setTransactionService(($this->services['Wizacha\\Marketplace\\Transaction\\TransactionService'] ?? $this->load('getTransactionServiceService.php')));
$instance->setFeatureSubscription(true);

return $instance;
