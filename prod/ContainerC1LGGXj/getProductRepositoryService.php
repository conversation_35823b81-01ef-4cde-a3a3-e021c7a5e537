<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\PIM\Product\ProductRepository' shared autowired service.

return $this->services['Wizacha\\Marketplace\\PIM\\Product\\ProductRepository'] = new \Wizacha\Marketplace\PIM\Product\ProductRepository(($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()));
