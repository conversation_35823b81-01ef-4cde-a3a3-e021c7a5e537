<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'Wizacha\AppBundle\Command\Benchmark\BenchmarkStorageCommand' shared autowired service.

$this->privates['Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkStorageCommand'] = $instance = new \Wizacha\AppBundle\Command\Benchmark\BenchmarkStorageCommand(($this->services['Wizacha\\Storage\\StaticsStorageService'] ?? $this->load('getStaticsStorageServiceService.php')));

$instance->setName('debug:benchmark:storage');

return $instance;
