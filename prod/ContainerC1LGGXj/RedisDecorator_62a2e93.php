<?php

class RedisDecorator_62a2e93 extends \Wizacha\Cache\RedisDecorator implements \ProxyManager\Proxy\VirtualProxyInterface
{
    private $valueHoldera1fd8 = null;
    private $initializere3996 = null;
    private static $publicProperties7ed0d = [
        
    ];
    public function setex($key, $ttl, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'setex', array('key' => $key, 'ttl' => $ttl, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->setex($key, $ttl, $value);
    }
    public function set($key, $value, $timeout = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'set', array('key' => $key, 'value' => $value, 'timeout' => $timeout), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->set($key, $value, $timeout);
    }
    public function del($key1, ... $otherKeys)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'del', array('key1' => $key1, 'otherKeys' => $otherKeys), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->del($key1, ...$otherKeys);
    }
    public function delFromPattern(string $pattern, string $prefix) : int
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'delFromPattern', array('pattern' => $pattern, 'prefix' => $prefix), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->delFromPattern($pattern, $prefix);
    }
    public function keys($pattern)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'keys', array('pattern' => $pattern), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->keys($pattern);
    }
    public function mget(array $array)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'mget', array('array' => $array), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->mget($array);
    }
    public function get(string $key, ?callable $setter = null, ?int $timeout = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'get', array('key' => $key, 'setter' => $setter, 'timeout' => $timeout), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->get($key, $setter, $timeout);
    }
    public function setOption($option, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'setOption', array('option' => $option, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->setOption($option, $value);
    }
    public function getOption($option)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'getOption', array('option' => $option), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->getOption($option);
    }
    public static function staticProxyConstructor($initializer)
    {
        static $reflection;
        $reflection = $reflection ?? new \ReflectionClass(__CLASS__);
        $instance   = $reflection->newInstanceWithoutConstructor();
        \Closure::bind(function (\Wizacha\Cache\RedisDecorator $instance) {
            unset($instance->redis);
        }, $instance, 'Wizacha\\Cache\\RedisDecorator')->__invoke($instance);
        $instance->initializere3996 = $initializer;
        return $instance;
    }
    public function __construct(\Redis $redis)
    {
        static $reflection;
        if (! $this->valueHoldera1fd8) {
            $reflection = $reflection ?? new \ReflectionClass('Wizacha\\Cache\\RedisDecorator');
            $this->valueHoldera1fd8 = $reflection->newInstanceWithoutConstructor();
        \Closure::bind(function (\Wizacha\Cache\RedisDecorator $instance) {
            unset($instance->redis);
        }, $this, 'Wizacha\\Cache\\RedisDecorator')->__invoke($this);
        }
        $this->valueHoldera1fd8->__construct($redis);
    }
    public function & __get($name)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '__get', ['name' => $name], $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        if (isset(self::$publicProperties7ed0d[$name])) {
            return $this->valueHoldera1fd8->$name;
        }
        $realInstanceReflection = new \ReflectionClass('Wizacha\\Cache\\RedisDecorator');
        if (! $realInstanceReflection->hasProperty($name)) {
            $targetObject = $this->valueHoldera1fd8;
            $backtrace = debug_backtrace(false, 1);
            trigger_error(
                sprintf(
                    'Undefined property: %s::$%s in %s on line %s',
                    $realInstanceReflection->getName(),
                    $name,
                    $backtrace[0]['file'],
                    $backtrace[0]['line']
                ),
                \E_USER_NOTICE
            );
            return $targetObject->$name;
        }
        $targetObject = $this->valueHoldera1fd8;
        $accessor = function & () use ($targetObject, $name) {
            return $targetObject->$name;
        };
        $backtrace = debug_backtrace(true, 2);
        $scopeObject = isset($backtrace[1]['object']) ? $backtrace[1]['object'] : new \ProxyManager\Stub\EmptyClassStub();
        $accessor = $accessor->bindTo($scopeObject, get_class($scopeObject));
        $returnValue = & $accessor();
        return $returnValue;
    }
    public function __set($name, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '__set', array('name' => $name, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        $realInstanceReflection = new \ReflectionClass('Wizacha\\Cache\\RedisDecorator');
        if (! $realInstanceReflection->hasProperty($name)) {
            $targetObject = $this->valueHoldera1fd8;
            $targetObject->$name = $value;
            return $targetObject->$name;
        }
        $targetObject = $this->valueHoldera1fd8;
        $accessor = function & () use ($targetObject, $name, $value) {
            $targetObject->$name = $value;
            return $targetObject->$name;
        };
        $backtrace = debug_backtrace(true, 2);
        $scopeObject = isset($backtrace[1]['object']) ? $backtrace[1]['object'] : new \ProxyManager\Stub\EmptyClassStub();
        $accessor = $accessor->bindTo($scopeObject, get_class($scopeObject));
        $returnValue = & $accessor();
        return $returnValue;
    }
    public function __isset($name)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '__isset', array('name' => $name), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        $realInstanceReflection = new \ReflectionClass('Wizacha\\Cache\\RedisDecorator');
        if (! $realInstanceReflection->hasProperty($name)) {
            $targetObject = $this->valueHoldera1fd8;
            return isset($targetObject->$name);
        }
        $targetObject = $this->valueHoldera1fd8;
        $accessor = function () use ($targetObject, $name) {
            return isset($targetObject->$name);
        };
        $backtrace = debug_backtrace(true, 2);
        $scopeObject = isset($backtrace[1]['object']) ? $backtrace[1]['object'] : new \ProxyManager\Stub\EmptyClassStub();
        $accessor = $accessor->bindTo($scopeObject, get_class($scopeObject));
        $returnValue = $accessor();
        return $returnValue;
    }
    public function __unset($name)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '__unset', array('name' => $name), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        $realInstanceReflection = new \ReflectionClass('Wizacha\\Cache\\RedisDecorator');
        if (! $realInstanceReflection->hasProperty($name)) {
            $targetObject = $this->valueHoldera1fd8;
            unset($targetObject->$name);
            return;
        }
        $targetObject = $this->valueHoldera1fd8;
        $accessor = function () use ($targetObject, $name) {
            unset($targetObject->$name);
            return;
        };
        $backtrace = debug_backtrace(true, 2);
        $scopeObject = isset($backtrace[1]['object']) ? $backtrace[1]['object'] : new \ProxyManager\Stub\EmptyClassStub();
        $accessor = $accessor->bindTo($scopeObject, get_class($scopeObject));
        $accessor();
    }
    public function __clone()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '__clone', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        $this->valueHoldera1fd8 = clone $this->valueHoldera1fd8;
    }
    public function __sleep()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '__sleep', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return array('valueHoldera1fd8');
    }
    public function __wakeup()
    {
        \Closure::bind(function (\Wizacha\Cache\RedisDecorator $instance) {
            unset($instance->redis);
        }, $this, 'Wizacha\\Cache\\RedisDecorator')->__invoke($this);
    }
    public function setProxyInitializer(\Closure $initializer = null) : void
    {
        $this->initializere3996 = $initializer;
    }
    public function getProxyInitializer() : ?\Closure
    {
        return $this->initializere3996;
    }
    public function initializeProxy() : bool
    {
        return $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'initializeProxy', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
    }
    public function isProxyInitialized() : bool
    {
        return null !== $this->valueHoldera1fd8;
    }
    public function getWrappedValueHolderValue()
    {
        return $this->valueHoldera1fd8;
    }
}
