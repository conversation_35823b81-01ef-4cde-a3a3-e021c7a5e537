<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'twig' shared service.

$a = new \Symfony\Bundle\TwigBundle\Loader\FilesystemLoader(($this->privates['templating.locator'] ?? $this->load('getTemplating_LocatorService.php')), ($this->privates['templating.name_parser'] ?? $this->load('getTemplating_NameParserService.php')), \dirname(__DIR__, 4));
$a->addPath((\dirname(__DIR__, 4).'/src/RemotefrontBundle/Resources/views'), 'App');
$a->addPath((\dirname(__DIR__, 4).'/vendor/symfony/symfony/src/Symfony/Bundle/FrameworkBundle/Resources/views'), 'Framework');
$a->addPath((\dirname(__DIR__, 4).'/vendor/symfony/symfony/src/Symfony/Bundle/FrameworkBundle/Resources/views'), '!Framework');
$a->addPath((\dirname(__DIR__, 4).'/vendor/symfony/symfony/src/Symfony/Bundle/SecurityBundle/Resources/views'), 'Security');
$a->addPath((\dirname(__DIR__, 4).'/vendor/symfony/symfony/src/Symfony/Bundle/SecurityBundle/Resources/views'), '!Security');
$a->addPath((\dirname(__DIR__, 4).'/vendor/doctrine/doctrine-bundle/Resources/views'), 'Doctrine');
$a->addPath((\dirname(__DIR__, 4).'/vendor/doctrine/doctrine-bundle/Resources/views'), '!Doctrine');
$a->addPath((\dirname(__DIR__, 4).'/vendor/snc/redis-bundle/Resources/views'), 'SncRedis');
$a->addPath((\dirname(__DIR__, 4).'/vendor/snc/redis-bundle/Resources/views'), '!SncRedis');
$a->addPath((\dirname(__DIR__, 4).'/app/Resources/TwigBundle/views'), 'Twig');
$a->addPath((\dirname(__DIR__, 4).'/vendor/symfony/symfony/src/Symfony/Bundle/TwigBundle/Resources/views'), 'Twig');
$a->addPath((\dirname(__DIR__, 4).'/vendor/symfony/symfony/src/Symfony/Bundle/TwigBundle/Resources/views'), '!Twig');
$a->addPath((\dirname(__DIR__, 4).'/vendor/symfony/swiftmailer-bundle/Resources/views'), 'Swiftmailer');
$a->addPath((\dirname(__DIR__, 4).'/vendor/symfony/swiftmailer-bundle/Resources/views'), '!Swiftmailer');
$a->addPath((\dirname(__DIR__, 4).'/src/AppBundle/Resources/views'), 'App');
$a->addPath((\dirname(__DIR__, 4).'/src/AppBundle/Resources/views'), '!App');
$a->addPath((\dirname(__DIR__, 4).'/src/RemotefrontBundle/Resources/views'), 'Remotefront');
$a->addPath((\dirname(__DIR__, 4).'/src/RemotefrontBundle/Resources/views'), '!Remotefront');
$a->addPath((\dirname(__DIR__, 4).'/vendor/symfony/symfony/src/Symfony/Bridge/Twig/Resources/views/Email'), 'email');
$a->addPath((\dirname(__DIR__, 4).'/vendor/symfony/symfony/src/Symfony/Bridge/Twig/Resources/views/Email'), '!email');
$a->addPath((\dirname(__DIR__, 4).'/vendor/symfony/symfony/src/Symfony/Bridge/Twig/Resources/views/Form'));

$this->services['twig'] = $instance = new \Twig\Environment($a, ['debug' => false, 'strict_variables' => false, 'autoescape' => 'name', 'cache' => ($this->targetDir.''.'/twig'), 'charset' => 'UTF-8']);

$b = ($this->services['request_stack'] ?? ($this->services['request_stack'] = new \Symfony\Component\HttpFoundation\RequestStack()));
$c = new \Symfony\Bridge\Twig\AppVariable();
$c->setEnvironment('prod');
$c->setDebug(false);
if ($this->has('security.token_storage')) {
    $c->setTokenStorage(($this->services['security.token_storage'] ?? $this->getSecurity_TokenStorageService()));
}
if ($this->has('request_stack')) {
    $c->setRequestStack($b);
}

$instance->addExtension(new \Symfony\Bridge\Twig\Extension\CsrfExtension());
$instance->addExtension(new \Symfony\Bridge\Twig\Extension\LogoutUrlExtension(($this->privates['security.logout_url_generator'] ?? $this->getSecurity_LogoutUrlGeneratorService())));
$instance->addExtension(new \Symfony\Bridge\Twig\Extension\SecurityExtension(($this->services['security.authorization_checker'] ?? $this->load('getSecurity_AuthorizationCheckerService.php'))));
$instance->addExtension(new \Symfony\Bridge\Twig\Extension\TranslationExtension(($this->services['translator'] ?? $this->load('getTranslatorService.php'))));
$instance->addExtension(new \Symfony\Bridge\Twig\Extension\AssetExtension(($this->services['assets.packages'] ?? $this->load('getAssets_PackagesService.php'))));
$instance->addExtension(new \Symfony\Bridge\Twig\Extension\CodeExtension(($this->privates['debug.file_link_formatter'] ?? ($this->privates['debug.file_link_formatter'] = new \Symfony\Component\HttpKernel\Debug\FileLinkFormatter(NULL))), \dirname(__DIR__, 4), 'UTF-8'));
$instance->addExtension(new \Symfony\Bridge\Twig\Extension\RoutingExtension(($this->services['router'] ?? $this->getRouterService())));
$instance->addExtension(new \Symfony\Bridge\Twig\Extension\YamlExtension());
$instance->addExtension(new \Symfony\Bridge\Twig\Extension\StopwatchExtension(($this->privates['debug.stopwatch'] ?? ($this->privates['debug.stopwatch'] = new \Symfony\Component\Stopwatch\Stopwatch(true))), false));
$instance->addExtension(new \Symfony\Bridge\Twig\Extension\ExpressionExtension());
$instance->addExtension(new \Symfony\Bridge\Twig\Extension\HttpKernelExtension());
$instance->addExtension(new \Symfony\Bridge\Twig\Extension\HttpFoundationExtension(new \Symfony\Component\HttpFoundation\UrlHelper($b, ($this->privates['router.request_context'] ?? $this->getRouter_RequestContextService()))));
$instance->addExtension(new \Symfony\Bridge\Twig\Extension\FormExtension());
$instance->addExtension(($this->services['app.twig_extension'] ?? $this->load('getApp_TwigExtensionService.php')));
$instance->addExtension(new \Twig_Extensions_Extension_Text());
$instance->addExtension(new \Twig_Extensions_Extension_Intl());
$instance->addExtension(new \Doctrine\Bundle\DoctrineBundle\Twig\DoctrineExtension());
$instance->addExtension(new \Exercise\HTMLPurifierBundle\Twig\HTMLPurifierExtension());
$instance->addGlobal('app', $c);
$instance->addRuntimeLoader(new \Twig\RuntimeLoader\ContainerRuntimeLoader(new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($this->getService, [
    'Exercise\\HTMLPurifierBundle\\Twig\\HTMLPurifierRuntime' => ['privates', 'exercise_html_purifier.twig.runtime', 'getExerciseHtmlPurifier_Twig_RuntimeService.php', true],
    'Symfony\\Bridge\\Twig\\Extension\\CsrfRuntime' => ['privates', 'twig.runtime.security_csrf', 'getTwig_Runtime_SecurityCsrfService.php', true],
    'Symfony\\Bridge\\Twig\\Extension\\HttpKernelRuntime' => ['privates', 'twig.runtime.httpkernel', 'getTwig_Runtime_HttpkernelService.php', true],
    'Symfony\\Component\\Form\\FormRenderer' => ['privates', 'twig.form.renderer', 'getTwig_Form_RendererService.php', true],
], [
    'Exercise\\HTMLPurifierBundle\\Twig\\HTMLPurifierRuntime' => '?',
    'Symfony\\Bridge\\Twig\\Extension\\CsrfRuntime' => '?',
    'Symfony\\Bridge\\Twig\\Extension\\HttpKernelRuntime' => '?',
    'Symfony\\Component\\Form\\FormRenderer' => '?',
])));
$instance->addGlobal('search_prefix', 'prod-ovh_');
$instance->addGlobal('isDev', false);
$instance->addGlobal('currencySign', '€');
$instance->addGlobal('currencyCode', 'EUR');
$instance->addGlobal('googleMapsApiKey', 'AIzaSyAw9jadoH_dgVqvbLU5tjAlsEZ75qt1TTw');
$instance->addGlobal('messageAttachmentService', ($this->services['marketplace.message_attachment.message_attachment_service'] ?? $this->load('getMarketplace_MessageAttachment_MessageAttachmentServiceService.php')));
(new \Symfony\Bundle\TwigBundle\DependencyInjection\Configurator\EnvironmentConfigurator('F j, Y H:i', '%d days', NULL, 0, '.', ','))->configure($instance);

return $instance;
