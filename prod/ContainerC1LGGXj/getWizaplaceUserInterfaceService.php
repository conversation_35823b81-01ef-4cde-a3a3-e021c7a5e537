<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private '.errored..service_locator.pEXF19J.Wizacha\Marketplace\User\WizaplaceUserInterface' shared service.

$this->throw('Cannot autowire service ".service_locator.pEXF19J": it references interface "Wizacha\\Marketplace\\User\\WizaplaceUserInterface" but no such service exists. Did you create a class that implements this interface?');
