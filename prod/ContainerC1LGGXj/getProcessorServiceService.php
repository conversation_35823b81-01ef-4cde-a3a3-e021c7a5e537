<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'Wizacha\Marketplace\Payment\Processor\ProcessorService' shared autowired service.

$a = ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\Stripe'] ?? $this->load('getStripeService.php'));

if (isset($this->privates['Wizacha\\Marketplace\\Payment\\Processor\\ProcessorService'])) {
    return $this->privates['Wizacha\\Marketplace\\Payment\\Processor\\ProcessorService'];
}
$b = ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\HiPay'] ?? $this->load('getHiPayService.php'));

if (isset($this->privates['Wizacha\\Marketplace\\Payment\\Processor\\ProcessorService'])) {
    return $this->privates['Wizacha\\Marketplace\\Payment\\Processor\\ProcessorService'];
}

return $this->privates['Wizacha\\Marketplace\\Payment\\Processor\\ProcessorService'] = new \Wizacha\Marketplace\Payment\Processor\ProcessorService(($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()), ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\MangoPay'] ?? $this->load('getMangoPayService.php')), $b, ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\SMoney'] ?? $this->load('getSMoneyService.php')), ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\LemonWay'] ?? $this->load('getLemonWayService.php')), $a);
