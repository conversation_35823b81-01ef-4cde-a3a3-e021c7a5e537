<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Payment\LemonWay\LemonWayConfig' shared service.

return $this->services['Wizacha\\Marketplace\\Payment\\LemonWay\\LemonWayConfig'] = new \Wizacha\Marketplace\Payment\LemonWay\LemonWayConfig($this->getEnv('bool:LEMONWAY_API_USE_BUYER_WALLET_FOR_MONEYIN'), $this->getEnv('LEMONWAY_API_TECH_WALLET_ID'), $this->getEnv('LEMONWAY_API_MARKETPLACE_ID'), $this->getEnv('LEMONWAY_API_MARKETPLACE_DISCOUNT_WALLET'), $this->getEnv('LEMONWAY_BANKWIRE_IBAN'), $this->getEnv('LEMONWAY_BANKWIRE_BIC'), $this->getEnv('LEMONWAY_BANKWIRE_HOLDER_NAME'), $this->getEnv('LEMONWAY_BANKWIRE_HOLDER_ADDRESS'), 'EUR');
