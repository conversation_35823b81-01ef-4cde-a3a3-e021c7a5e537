<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\CardController' shared autowired service.

$this->services['Wizacha\\AppBundle\\Controller\\CardController'] = $instance = new \Wizacha\AppBundle\Controller\CardController(($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()));

$instance->setContainer($this);

return $instance;
