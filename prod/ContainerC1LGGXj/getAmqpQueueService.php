<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Async\AmqpQueue' shared service.

return $this->services['Wizacha\\Async\\AmqpQueue'] = new \Wizacha\Async\AmqpQueue(($this->services['marketplace.queue_manager'] ?? $this->load('getMarketplace_QueueManagerService.php')), ($this->services['marketplace.amqp.connection'] ?? $this->load('getMarketplace_Amqp_ConnectionService.php')));
