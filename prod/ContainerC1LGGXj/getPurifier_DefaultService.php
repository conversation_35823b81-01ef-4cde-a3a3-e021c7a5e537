<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'purifier.default' shared service.

return $this->services['purifier.default'] = new \Wizacha\Purifier\Purifier(($this->services['exercise_html_purifier.default.public'] ?? $this->load('getExerciseHtmlPurifier_Default_PublicService.php')), false);
