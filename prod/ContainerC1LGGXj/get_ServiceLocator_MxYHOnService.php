<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private '.service_locator.mx_yHOn' shared service.

return $this->privates['.service_locator.mx_yHOn'] = new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($this->getService, [
    'response' => ['privates', '.errored..service_locator.mx_yHOn.Symfony\\Component\\HttpFoundation\\Response', NULL, 'Cannot autowire service ".service_locator.mx_yHOn": it references class "Symfony\\Component\\HttpFoundation\\Response" but no such service exists.'],
], [
    'response' => 'Symfony\\Component\\HttpFoundation\\Response',
]);
