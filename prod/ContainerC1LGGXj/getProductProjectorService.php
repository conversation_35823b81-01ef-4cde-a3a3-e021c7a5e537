<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\ReadModel\ProductProjector' shared service.

return $this->services['Wizacha\\Marketplace\\ReadModel\\ProductProjector'] = new \Wizacha\Marketplace\ReadModel\ProductProjector(($this->services['Wizacha\\Marketplace\\ReadModel\\ProductRepository'] ?? $this->load('getProductRepository2Service.php')), ($this->services['Wizacha\\Marketplace\\Promotion\\PromotionService'] ?? $this->load('getPromotionServiceService.php')), ($this->services['marketplace.pim.video_service'] ?? $this->load('getMarketplace_Pim_VideoServiceService.php')), ($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()), ($this->services['router'] ?? $this->getRouterService()), ($this->services['Wizacha\\Marketplace\\Catalog\\Category\\CategoryService'] ?? $this->load('getCategoryServiceService.php')), ($this->services['marketplace.catalog.company_service'] ?? $this->load('getMarketplace_Catalog_CompanyServiceService.php')), ($this->services['Wizacha\\Async\\Dispatcher'] ?? $this->load('getDispatcherService.php')), ($this->services['Wizacha\\Marketplace\\PIM\\MultiVendorProduct\\MultiVendorProductService'] ?? $this->load('getMultiVendorProductServiceService.php')), ($this->services['Wizacha\\Marketplace\\PIM\\Product\\ProductService'] ?? $this->load('getProductService2Service.php')), ($this->services['Wizacha\\ImageManager'] ?? $this->load('getImageManagerService.php')), ($this->services['marketplace.search.product_index'] ?? $this->load('getMarketplace_Search_ProductIndexService.php')), ($this->services['marketplace.review.product.service'] ?? $this->load('getMarketplace_Review_Product_ServiceService.php')), true, ($this->services['Wizacha\\Marketplace\\PriceTier\\Service\\PriceTierService'] ?? $this->load('getPriceTierServiceService.php')), ($this->services['marketplace.product.declination_factory'] ?? ($this->services['marketplace.product.declination_factory'] = new \Wizacha\Marketplace\Entities\DeclinationFactory())), ($this->services['Wizacha\\Marketplace\\RelatedProduct\\RelatedProductService'] ?? $this->load('getRelatedProductServiceService.php')));
