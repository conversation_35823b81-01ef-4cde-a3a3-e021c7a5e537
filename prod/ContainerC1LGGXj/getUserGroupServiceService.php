<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Group\UserGroupService' shared service.

return $this->services['Wizacha\\Marketplace\\Group\\UserGroupService'] = new \Wizacha\Marketplace\Group\UserGroupService(new \Wizacha\Marketplace\Group\GroupRepository(($this->services['doctrine.orm.default_entity_manager'] ?? $this->getDoctrine_Orm_DefaultEntityManagerService()), ($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService())), false, ($this->services['Wizacha\\Marketplace\\User\\UserService'] ?? $this->getUserServiceService()), ($this->services['marketplace.import.csv_uploader'] ?? $this->load('getMarketplace_Import_CsvUploaderService.php')), ($this->services['Wizacha\\Async\\Dispatcher'] ?? $this->load('getDispatcherService.php')));
