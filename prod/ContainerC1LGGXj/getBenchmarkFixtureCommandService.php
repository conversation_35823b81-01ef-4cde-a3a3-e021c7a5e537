<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'Wizacha\AppBundle\Command\BenchmarkFixtureCommand' shared autowired service.

$this->privates['Wizacha\\AppBundle\\Command\\BenchmarkFixtureCommand'] = $instance = new \Wizacha\AppBundle\Command\BenchmarkFixtureCommand(($this->services['doctrine'] ?? $this->getDoctrineService()), 'client-ovh-1.154.5');

$instance->setName('debug:benchmark:fixtures');

return $instance;
