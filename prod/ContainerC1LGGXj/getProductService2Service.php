<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\PIM\Product\ProductService' shared service.

return $this->services['Wizacha\\Marketplace\\PIM\\Product\\ProductService'] = new \Wizacha\Marketplace\PIM\Product\ProductService(($this->services['doctrine.orm.default_entity_manager'] ?? $this->getDoctrine_Orm_DefaultEntityManagerService()), ($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()), ($this->services['marketplace.pim.attribute_service'] ?? $this->load('getMarketplace_Pim_AttributeServiceService.php')), ($this->services['marketplace.multi_vendor_product_link.service'] ?? $this->load('getMarketplace_MultiVendorProductLink_ServiceService.php')), ($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()), ($this->services['Broadway\\UuidGenerator\\Rfc4122\\Version4Generator'] ?? ($this->services['Broadway\\UuidGenerator\\Rfc4122\\Version4Generator'] = new \Broadway\UuidGenerator\Rfc4122\Version4Generator())), ($this->services['Wizacha\\Marketplace\\PIM\\Product\\ProductRepository'] ?? $this->load('getProductRepositoryService.php')), ($this->services['Wizacha\\Marketplace\\PIM\\Option\\OptionRepository'] ?? $this->load('getOptionRepositoryService.php')), ($this->services['Wizacha\\Storage\\ProductAttachmentsStorageService'] ?? $this->load('getProductAttachmentsStorageServiceService.php')));
