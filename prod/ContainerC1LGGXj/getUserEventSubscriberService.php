<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\ActionLogger\EventSubscribers\UserEventSubscriber' shared autowired service.

return $this->services['Wizacha\\ActionLogger\\EventSubscribers\\UserEventSubscriber'] = new \Wizacha\ActionLogger\EventSubscribers\UserEventSubscriber(($this->services['Wizacha\\Marketplace\\User\\UserPasswordHistoryService'] ?? $this->load('getUserPasswordHistoryServiceService.php')));
