<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Payment\Processor\MangoPay' shared autowired service.

$a = new \Wizacha\Marketplace\Payment\Mangopay\MangoPayLogger(($this->services['monolog.logger.psp'] ?? $this->getMonolog_Logger_PspService()), ($this->services['serializer'] ?? $this->load('getSerializerService.php')));
$a->setDataAnonymizer(new \Wizacha\Marketplace\Payment\Security\MangoPayDataAnonymizer());

$this->services['Wizacha\\Marketplace\\Payment\\Processor\\MangoPay'] = $instance = new \Wizacha\Marketplace\Payment\Processor\MangoPay(($this->services['Wizacha\\Marketplace\\Commission\\CommissionService'] ?? $this->load('getCommissionServiceService.php')), ($this->services['marketplace.payment.user_payment_info_service'] ?? $this->load('getMarketplace_Payment_UserPaymentInfoServiceService.php')), 0, $this->getEnv('MANGOPAY_USER_ID_AGENT_ETABLISSEMENT_PAIEMENT'), $a, ($this->services['router'] ?? $this->getRouterService()), ($this->services['Wizacha\\Marketplace\\User\\UserRepository'] ?? $this->getUserRepositoryService()), ($this->services['Wizacha\\Marketplace\\Order\\OrderService'] ?? $this->load('getOrderServiceService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\DispatchFundsFailed'] ?? $this->load('getDispatchFundsFailedService.php')), ($this->services['Wizacha\\Marketplace\\Company\\CompanyService'] ?? $this->load('getCompanyServiceService.php')), ($this->services['Wizacha\\Marketplace\\Transaction\\TransactionService'] ?? $this->load('getTransactionServiceService.php')), ($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()), ($this->services['Wizacha\\Marketplace\\Order\\Action\\DispatchFundsSucceeded'] ?? $this->load('getDispatchFundsSucceededService.php')), ($this->services['Wizacha\\Marketplace\\CompanyPerson\\CompanyPersonService'] ?? $this->load('getCompanyPersonServiceService.php')), ($this->privates['Wizacha\\Marketplace\\PSP\\UboMangopay\\UboMangopayService'] ?? $this->load('getUboMangopayServiceService.php')));

$instance->setApi(($this->services['MangoPay\\MangoPayApi'] ?? $this->load('getMangoPayApiService.php')));
$instance->setConfig(($this->services['Wizacha\\Marketplace\\Payment\\Mangopay\\MangoPayApiConfig'] ?? $this->load('getMangoPayApiConfigService.php')));

return $instance;
