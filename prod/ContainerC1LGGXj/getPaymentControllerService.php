<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\PaymentController' shared autowired service.

return $this->services['Wizacha\\AppBundle\\Controller\\Api\\PaymentController'] = new \Wizacha\AppBundle\Controller\Api\PaymentController(($this->services['marketplace.payment.payment_service'] ?? $this->load('getMarketplace_Payment_PaymentServiceService.php')));
