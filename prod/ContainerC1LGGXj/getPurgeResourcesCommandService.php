<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'console.command.public_alias.Wizacha\AppBundle\Command\PurgeResourcesCommand' shared autowired service.

return $this->services['console.command.public_alias.Wizacha\\AppBundle\\Command\\PurgeResourcesCommand'] = new \Wizacha\AppBundle\Command\PurgeResourcesCommand(new \Wizacha\Component\PurgeResources\PurgeResourceFactory(new RewindableGenerator(function () {
    yield 0 => ($this->privates['Wizacha\\Component\\PurgeResources\\Types\\PurgeImages'] ?? $this->load('getPurgeImagesService.php'));
}, 1)), ($this->privates['Wizacha\\Component\\PurgeResources\\Report'] ?? ($this->privates['Wizacha\\Component\\PurgeResources\\Report'] = new \Wizacha\Component\PurgeResources\Report())), 100);
