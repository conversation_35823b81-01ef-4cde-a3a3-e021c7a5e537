<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\RelatedProduct\ConfigService' shared service.

return $this->services['Wizacha\\Marketplace\\RelatedProduct\\ConfigService'] = new \Wizacha\Marketplace\RelatedProduct\ConfigService('recommended', ($this->services['logger'] ?? $this->load('getLoggerService.php')));
