<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'prediggo.cms_exporter' shared service.

return $this->services['prediggo.cms_exporter'] = new \Wizacha\Prediggo\CMSExporter(($this->services['marketplace.cms.page_service'] ?? $this->load('getMarketplace_Cms_PageServiceService.php')));
