<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Backend\MangopayController' shared service.

return $this->services['Wizacha\\AppBundle\\Controller\\Backend\\MangopayController'] = new \Wizacha\AppBundle\Controller\Backend\MangopayController(($this->services['Wizacha\\Marketplace\\Payment\\Processor\\MangoPay'] ?? $this->load('getMangoPayService.php')), ($this->services['Wizacha\\Marketplace\\CompanyPerson\\CompanyPersonService'] ?? $this->load('getCompanyPersonServiceService.php')), ($this->services['logger'] ?? $this->load('getLoggerService.php')), ($this->privates['Wizacha\\Marketplace\\PSP\\UboMangopay\\UboMangopayService'] ?? $this->load('getUboMangopayServiceService.php')));
