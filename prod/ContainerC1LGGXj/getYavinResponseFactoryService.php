<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Component\Http\Factory\YavinResponseFactory' shared autowired service.

return $this->services['Wizacha\\Component\\Http\\Factory\\YavinResponseFactory'] = new \Wizacha\Component\Http\Factory\YavinResponseFactory();
