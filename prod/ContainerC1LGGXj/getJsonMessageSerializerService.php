<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Messenger\JsonMessageSerializer' shared autowired service.

return $this->services['Wizacha\\Marketplace\\Messenger\\JsonMessageSerializer'] = new \Wizacha\Marketplace\Messenger\JsonMessageSerializer(($this->services['serializer'] ?? $this->load('getSerializerService.php')));
