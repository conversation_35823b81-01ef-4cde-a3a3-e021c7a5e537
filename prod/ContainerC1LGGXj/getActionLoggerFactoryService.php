<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'Wizacha\ActionLogger\ActionLoggerFactory' shared service.

return $this->privates['Wizacha\\ActionLogger\\ActionLoggerFactory'] = new \Wizacha\ActionLogger\ActionLoggerFactory(($this->services['monolog.logger.audit'] ?? $this->load('getMonolog_Logger_AuditService.php')), ($this->services['Wizacha\\AppBundle\\Security\\User\\UserService'] ?? $this->load('getUserService2Service.php')));
