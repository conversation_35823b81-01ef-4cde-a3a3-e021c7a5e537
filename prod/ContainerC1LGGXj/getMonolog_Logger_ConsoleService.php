<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'monolog.logger.console' shared service.

$this->privates['monolog.logger.console'] = $instance = new \Symfony\Bridge\Monolog\Logger('console');

$instance->pushProcessor([0 => ($this->privates['monolog.processor.http_request'] ?? ($this->privates['monolog.processor.http_request'] = new \Wizacha\Bridge\Monolog\Processor\HttpRequestProcessor($this->getEnv('REQUEST_ID_HEADER_NAME')))), 1 => 'addCorrelationInformation']);
$instance->pushProcessor([0 => ($this->privates['monolog.processor.console_command'] ?? ($this->privates['monolog.processor.console_command'] = new \Wizacha\Bridge\Monolog\Processor\ConsoleCommandProcessor())), 1 => 'addCorrelationInformation']);
$instance->pushProcessor(($this->privates['monolog.processor.extra_context'] ?? ($this->privates['monolog.processor.extra_context'] = new \Wizacha\Bridge\Monolog\Processor\ExtraContextProcessor())));
$instance->pushHandler(($this->privates['monolog.handler.main'] ?? $this->getMonolog_Handler_MainService()));

return $instance;
