<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Commission\CommissionService' shared service.

return $this->services['Wizacha\\Marketplace\\Commission\\CommissionService'] = new \Wizacha\Marketplace\Commission\CommissionService(($this->services['marketplace.commission.commission_repository'] ?? $this->load('getMarketplace_Commission_CommissionRepositoryService.php')), ($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()), ($this->services['app.setting_storage'] ?? $this->load('getApp_SettingStorageService.php')), 0, 0, ($this->services['marketplace.pim.category_service'] ?? $this->load('getMarketplace_Pim_CategoryServiceService.php')), ($this->services['logger'] ?? $this->load('getLoggerService.php')), ($this->services['Wizacha\\Marketplace\\Order\\OrderService'] ?? $this->load('getOrderServiceService.php')), ($this->services['order_amounts_commission_repository'] ?? $this->load('getOrderAmountsCommissionRepositoryService.php')));
