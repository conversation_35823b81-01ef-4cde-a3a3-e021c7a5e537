<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'RulerZ\Compiler\Compiler' shared service.

return $this->services['RulerZ\\Compiler\\Compiler'] = new \RulerZ\Compiler\Compiler(($this->services['rulerz.evaluator.file'] ?? ($this->services['rulerz.evaluator.file'] = new \RulerZ\Compiler\FileEvaluator())));
