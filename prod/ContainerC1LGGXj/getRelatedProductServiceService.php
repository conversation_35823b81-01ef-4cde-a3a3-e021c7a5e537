<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\RelatedProduct\RelatedProductService' shared service.

return $this->services['Wizacha\\Marketplace\\RelatedProduct\\RelatedProductService'] = new \Wizacha\Marketplace\RelatedProduct\RelatedProductService(($this->services['Wizacha\\Marketplace\\RelatedProduct\\ConfigService'] ?? $this->load('getConfigServiceService.php')), ($this->services['Wizacha\\Marketplace\\RelatedProduct\\RelatedProductRepository'] ?? $this->load('getRelatedProductRepositoryService.php')));
