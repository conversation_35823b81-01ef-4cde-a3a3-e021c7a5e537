<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\CreditCardController' shared autowired service.

$this->services['Wizacha\\AppBundle\\Controller\\Api\\CreditCardController'] = $instance = new \Wizacha\AppBundle\Controller\Api\CreditCardController(($this->services['Wizacha\\Marketplace\\CreditCard\\Repository\\CreditCardRepository'] ?? $this->load('getCreditCardRepositoryService.php')), ($this->services['Wizacha\\Marketplace\\User\\UserRepository'] ?? $this->getUserRepositoryService()), ($this->services['validator'] ?? $this->load('getValidatorService.php')), ($this->services['marketplace.payment.payment_service'] ?? $this->load('getMarketplace_Payment_PaymentServiceService.php')), ($this->privates['Wizacha\\AppBundle\\Service\\WhitelistDomainsService'] ?? $this->load('getWhitelistDomainsServiceService.php')));

$instance->setAddressBookService(($this->services['marketplace.user.address_service'] ?? $this->load('getMarketplace_User_AddressServiceService.php')));
$instance->setUserService(($this->services['Wizacha\\Marketplace\\User\\UserService'] ?? $this->getUserServiceService()));

return $instance;
