<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'Wizacha\AppBundle\Command\Benchmark\BenchmarkQueueCommand' shared autowired service.

$this->privates['Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkQueueCommand'] = $instance = new \Wizacha\AppBundle\Command\Benchmark\BenchmarkQueueCommand('amqp', ($this->services['marketplace.queue_manager'] ?? $this->load('getMarketplace_QueueManagerService.php')), ['region' => 'eu-west-1', 'key' => '', 'secret' => ''], ['host' => '************', 'port' => '5672', 'user' => 'guest', 'pass' => 'guest', 'vhost' => '/']);

$instance->setName('debug:benchmark:queue');

return $instance;
