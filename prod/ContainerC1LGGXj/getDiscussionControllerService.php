<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\DiscussionController' shared autowired service.

return $this->services['Wizacha\\AppBundle\\Controller\\Api\\DiscussionController'] = new \Wizacha\AppBundle\Controller\Api\DiscussionController(($this->services['marketplace.message_attachment.message_attachment_service'] ?? $this->load('getMarketplace_MessageAttachment_MessageAttachmentServiceService.php')), ($this->services['purifier.default'] ?? $this->load('getPurifier_DefaultService.php')), ($this->services['Wizacha\\Storage\\MessageAttachmentsStorageService'] ?? $this->load('getMessageAttachmentsStorageServiceService.php')), ($this->services['app.discuss_service'] ?? $this->load('getApp_DiscussServiceService.php')));
