<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\ReadModel\ReadModelService' shared autowired service.

return $this->services['Wizacha\\Marketplace\\ReadModel\\ReadModelService'] = new \Wizacha\Marketplace\ReadModel\ReadModelService(($this->services['Wizacha\\Marketplace\\ReadModel\\ProductRepository'] ?? $this->load('getProductRepository2Service.php')), ($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()));
