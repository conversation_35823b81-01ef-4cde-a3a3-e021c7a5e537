<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\Division\DivisionController' shared autowired service.

return $this->services['Wizacha\\AppBundle\\Controller\\Api\\Division\\DivisionController'] = new \Wizacha\AppBundle\Controller\Api\Division\DivisionController(($this->services['Wizacha\\Marketplace\\Division\\Service\\DivisionSettingsService'] ?? $this->load('getDivisionSettingsServiceService.php')), ($this->services['Wizacha\\Marketplace\\Division\\Service\\DivisionService'] ?? $this->load('getDivisionServiceService.php')));
