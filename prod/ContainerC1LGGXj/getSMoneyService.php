<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Payment\Processor\SMoney' shared autowired service.

$this->services['Wizacha\\Marketplace\\Payment\\Processor\\SMoney'] = $instance = new \Wizacha\Marketplace\Payment\Processor\SMoney($this->getEnv('SMONEY_WEBSITE_NAME'), ($this->services['Wizacha\\Marketplace\\Commission\\CommissionService'] ?? $this->load('getCommissionServiceService.php')), ($this->services['router'] ?? $this->getRouterService()), ($this->services['Wizacha\\Marketplace\\User\\UserRepository'] ?? $this->getUserRepositoryService()), ($this->services['logger'] ?? $this->load('getLoggerService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\DispatchFundsFailed'] ?? $this->load('getDispatchFundsFailedService.php')), ($this->services['Wizacha\\Marketplace\\Order\\OrderService'] ?? $this->load('getOrderServiceService.php')), ($this->services['Wizacha\\Marketplace\\Company\\CompanyService'] ?? $this->load('getCompanyServiceService.php')), ($this->services['marketplace.order.refund.refund_repository'] ?? $this->load('getMarketplace_Order_Refund_RefundRepositoryService.php')), ($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()), ($this->services['Wizacha\\Marketplace\\Order\\Action\\DispatchFundsSucceeded'] ?? $this->load('getDispatchFundsSucceededService.php')));

$instance->setApi(($this->services['Wizacha\\Marketplace\\Payment\\SMoney\\SMoneyApi'] ?? $this->load('getSMoneyApiService.php')));
$instance->setTransactionService(($this->services['Wizacha\\Marketplace\\Transaction\\TransactionService'] ?? $this->load('getTransactionServiceService.php')));

return $instance;
