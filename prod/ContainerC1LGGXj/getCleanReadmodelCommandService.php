<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'Wizacha\AppBundle\Command\Readmodel\CleanReadmodelCommand' shared autowired service.

$this->privates['Wizacha\\AppBundle\\Command\\Readmodel\\CleanReadmodelCommand'] = $instance = new \Wizacha\AppBundle\Command\Readmodel\CleanReadmodelCommand(($this->services['Wizacha\\Marketplace\\ReadModel\\ProductProjector'] ?? $this->load('getProductProjectorService.php')), ($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()), ($this->services['logger'] ?? $this->load('getLoggerService.php')));

$instance->setName('readmodel:orphan:clean');

return $instance;
