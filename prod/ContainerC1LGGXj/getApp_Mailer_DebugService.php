<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'app.mailer.debug' shared service.

return $this->services['app.mailer.debug'] = new \Wizacha\Bridge\Swiftmailer\DebugSwiftMailer(($this->services['swiftmailer.mailer.fake_mailer'] ?? $this->load('getSwiftmailer_Mailer_FakeMailerService.php')));
