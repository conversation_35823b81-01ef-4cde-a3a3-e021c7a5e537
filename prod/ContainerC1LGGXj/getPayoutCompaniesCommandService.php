<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'console.command.public_alias.Wizacha\AppBundle\Command\PayoutCompaniesCommand' shared autowired service.

$this->services['console.command.public_alias.Wizacha\\AppBundle\\Command\\PayoutCompaniesCommand'] = $instance = new \Wizacha\AppBundle\Command\PayoutCompaniesCommand(new RewindableGenerator(function () {
    yield 0 => ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\NoPayment'] ?? $this->load('getNoPaymentService.php'));
    yield 1 => ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\MangoPay'] ?? $this->load('getMangoPayService.php'));
    yield 2 => ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\LemonWay'] ?? $this->load('getLemonWayService.php'));
    yield 3 => ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\SMoney'] ?? $this->load('getSMoneyService.php'));
    yield 4 => ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\Stripe'] ?? $this->load('getStripeService.php'));
    yield 5 => ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\HiPay'] ?? $this->load('getHiPayService.php'));
}, 6), 'bimonthly', ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\PayoutService'] ?? $this->load('getPayoutServiceService.php')));

$instance->setLogger(($this->services['logger'] ?? $this->load('getLoggerService.php')));

return $instance;
