<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Catalog\Product\ProductService' shared service.

return $this->services['Wizacha\\Marketplace\\Catalog\\Product\\ProductService'] = new \Wizacha\Marketplace\Catalog\Product\ProductService(($this->services['Wizacha\\Marketplace\\ReadModel\\ProductRepository'] ?? $this->load('getProductRepository2Service.php')), ($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()), ($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()), true, false, 'back.marketplace.ovhcloud.com', ($this->services['html_service'] ?? ($this->services['html_service'] = new \Wizacha\Component\Html\HtmlService('back.marketplace.ovhcloud.com'))), ($this->services['Wizacha\\Marketplace\\PIM\\Product\\ProductService'] ?? $this->load('getProductService2Service.php')), ($this->services['Wizacha\\Storage\\ImagesStorageService'] ?? $this->load('getImagesStorageServiceService.php')), false);
