<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Basket\Checkout' shared service.

$a = ($this->services['Wizacha\\Marketplace\\Subscription\\SubscriptionService'] ?? $this->load('getSubscriptionServiceService.php'));

if (isset($this->services['Wizacha\\Marketplace\\Basket\\Checkout'])) {
    return $this->services['Wizacha\\Marketplace\\Basket\\Checkout'];
}

$this->services['Wizacha\\Marketplace\\Basket\\Checkout'] = $instance = new \Wizacha\Marketplace\Basket\Checkout(($this->services['Wizacha\\Marketplace\\Promotion\\PromotionService'] ?? $this->load('getPromotionServiceService.php')), ($this->services['marketplace.payment.payment_service'] ?? $this->load('getMarketplace_Payment_PaymentServiceService.php')), ($this->services['Wizacha\\Marketplace\\Order\\OrderService'] ?? $this->load('getOrderServiceService.php')), ($this->services['Wizacha\\Marketplace\\User\\UserService'] ?? $this->getUserServiceService()), ($this->services['Wizacha\\Marketplace\\Payment\\MandateService'] ?? $this->load('getMandateServiceService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\Trash'] ?? $this->load('getTrashService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\Confirm'] ?? $this->load('getConfirmService.php')), ($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()), $a, ($this->services['Wizacha\\Marketplace\\Order\\Action\\CommitTo'] ?? $this->load('getCommitToService.php')), 0, ($this->services['Wizacha\\Marketplace\\Order\\Action\\RedirectToPaymentProcessor'] ?? $this->load('getRedirectToPaymentProcessorService.php')), ($this->services['Wizacha\\Sentinel\\AlertManager'] ?? $this->load('getAlertManagerService.php')));

$instance->setUserPaymentInfoService(($this->services['marketplace.payment.user_payment_info_service'] ?? $this->load('getMarketplace_Payment_UserPaymentInfoServiceService.php')));

return $instance;
