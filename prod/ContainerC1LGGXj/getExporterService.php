<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Prediggo\Exporter' shared service.

return $this->services['Wizacha\\Prediggo\\Exporter'] = new \Wizacha\Prediggo\Exporter(($this->services['prediggo.product_exporter'] ?? $this->load('getPrediggo_ProductExporterService.php')), ($this->services['prediggo.hierarchy_exporter'] ?? $this->load('getPrediggo_HierarchyExporterService.php')), ($this->services['prediggo.attribute_translation_exporter'] ?? $this->load('getPrediggo_AttributeTranslationExporterService.php')), ($this->services['prediggo.user_exporter'] ?? $this->load('getPrediggo_UserExporterService.php')), ($this->services['prediggo.order_exporter'] ?? $this->load('getPrediggo_OrderExporterService.php')), ($this->services['prediggo.cms_exporter'] ?? $this->load('getPrediggo_CmsExporterService.php')), ($this->services['Wizacha\\Storage\\PrediggoStorageService'] ?? $this->load('getPrediggoStorageServiceService.php')));
