<?php

use <PERSON>ymfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'services_resetter' shared service.

return $this->services['services_resetter'] = new \Symfony\Component\HttpKernel\DependencyInjection\ServicesResetter(new RewindableGenerator(function () {
    if (isset($this->services['cache.app'])) {
        yield 'cache.app' => ($this->services['cache.app'] ?? null);
    }
    if (isset($this->services['cache.system'])) {
        yield 'cache.system' => ($this->services['cache.system'] ?? null);
    }
    if (isset($this->privates['cache.validator'])) {
        yield 'cache.validator' => ($this->privates['cache.validator'] ?? null);
    }
    if (isset($this->privates['cache.serializer'])) {
        yield 'cache.serializer' => ($this->privates['cache.serializer'] ?? null);
    }
    if (isset($this->privates['cache.annotations'])) {
        yield 'cache.annotations' => ($this->privates['cache.annotations'] ?? null);
    }
    if (false) {
        yield 'cache.property_info' => null;
    }
    if (isset($this->privates['cache.messenger.restart_workers_signal'])) {
        yield 'cache.messenger.restart_workers_signal' => ($this->privates['cache.messenger.restart_workers_signal'] ?? null);
    }
    if (isset($this->privates['form.choice_list_factory.cached'])) {
        yield 'form.choice_list_factory.cached' => ($this->privates['form.choice_list_factory.cached'] ?? null);
    }
    if (isset($this->privates['messenger.transport.in_memory.factory'])) {
        yield 'messenger.transport.in_memory.factory' => ($this->privates['messenger.transport.in_memory.factory'] ?? null);
    }
    if (isset($this->privates['debug.stopwatch'])) {
        yield 'debug.stopwatch' => ($this->privates['debug.stopwatch'] ?? null);
    }
    if (isset($this->services['security.token_storage'])) {
        yield 'security.token_storage' => ($this->services['security.token_storage'] ?? null);
    }
    if (isset($this->privates['cache.security_expression_language'])) {
        yield 'cache.security_expression_language' => ($this->privates['cache.security_expression_language'] ?? null);
    }
    if (isset($this->services['doctrine'])) {
        yield 'doctrine' => ($this->services['doctrine'] ?? null);
    }
    if (isset($this->privates['form.type.entity'])) {
        yield 'form.type.entity' => ($this->privates['form.type.entity'] ?? null);
    }
    if (isset($this->privates['monolog.handler.verbose'])) {
        yield 'monolog.handler.verbose' => ($this->privates['monolog.handler.verbose'] ?? null);
    }
    if (isset($this->privates['monolog.handler.main'])) {
        yield 'monolog.handler.main' => ($this->privates['monolog.handler.main'] ?? null);
    }
    if (isset($this->privates['twig.form.engine'])) {
        yield 'twig.form.engine' => ($this->privates['twig.form.engine'] ?? null);
    }
    if (isset($this->privates['swiftmailer.email_sender.listener'])) {
        yield 'swiftmailer.email_sender.listener' => ($this->privates['swiftmailer.email_sender.listener'] ?? null);
    }
}, function () {
    return 0 + (int) (isset($this->services['cache.app'])) + (int) (isset($this->services['cache.system'])) + (int) (isset($this->privates['cache.validator'])) + (int) (isset($this->privates['cache.serializer'])) + (int) (isset($this->privates['cache.annotations'])) + (int) (false) + (int) (isset($this->privates['cache.messenger.restart_workers_signal'])) + (int) (isset($this->privates['form.choice_list_factory.cached'])) + (int) (isset($this->privates['messenger.transport.in_memory.factory'])) + (int) (isset($this->privates['debug.stopwatch'])) + (int) (isset($this->services['security.token_storage'])) + (int) (isset($this->privates['cache.security_expression_language'])) + (int) (isset($this->services['doctrine'])) + (int) (isset($this->privates['form.type.entity'])) + (int) (isset($this->privates['monolog.handler.verbose'])) + (int) (isset($this->privates['monolog.handler.main'])) + (int) (isset($this->privates['twig.form.engine'])) + (int) (isset($this->privates['swiftmailer.email_sender.listener']));
}), ['cache.app' => [0 => 'reset'], 'cache.system' => [0 => 'reset'], 'cache.validator' => [0 => 'reset'], 'cache.serializer' => [0 => 'reset'], 'cache.annotations' => [0 => 'reset'], 'cache.property_info' => [0 => 'reset'], 'cache.messenger.restart_workers_signal' => [0 => 'reset'], 'form.choice_list_factory.cached' => [0 => 'reset'], 'messenger.transport.in_memory.factory' => [0 => 'reset'], 'debug.stopwatch' => [0 => 'reset'], 'security.token_storage' => [0 => 'disableUsageTracking', 1 => 'setToken'], 'cache.security_expression_language' => [0 => 'reset'], 'doctrine' => [0 => 'reset'], 'form.type.entity' => [0 => 'reset'], 'monolog.handler.verbose' => [0 => 'reset'], 'monolog.handler.main' => [0 => 'reset'], 'twig.form.engine' => [0 => 'reset'], 'swiftmailer.email_sender.listener' => [0 => 'reset']]);
