<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Backend\CommissionController' shared service.

return $this->services['Wizacha\\AppBundle\\Controller\\Backend\\CommissionController'] = new \Wizacha\AppBundle\Controller\Backend\CommissionController(($this->services['Wizacha\\Marketplace\\Commission\\CommissionService'] ?? $this->load('getCommissionServiceService.php')));
