<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\SubscriptionController' shared autowired service.

$this->services['Wizacha\\AppBundle\\Controller\\Api\\SubscriptionController'] = $instance = new \Wizacha\AppBundle\Controller\Api\SubscriptionController(($this->services['validator'] ?? $this->load('getValidatorService.php')), ($this->services['Wizacha\\Marketplace\\Subscription\\SubscriptionService'] ?? $this->load('getSubscriptionServiceService.php')), ($this->services['Wizacha\\Marketplace\\Subscription\\SubscriptionRepository'] ?? $this->getSubscriptionRepositoryService()), ($this->services['security.authorization_checker'] ?? $this->load('getSecurity_AuthorizationCheckerService.php')), ($this->services['Wizacha\\Marketplace\\Subscription\\Log\\SubscriptionActionTraceRepository'] ?? $this->load('getSubscriptionActionTraceRepositoryService.php')), ($this->services['Wizacha\\Marketplace\\Subscription\\Log\\SubscriptionActionTraceService'] ?? $this->load('getSubscriptionActionTraceServiceService.php')), ($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()));

$instance->setUserService(($this->services['Wizacha\\Marketplace\\User\\UserService'] ?? $this->getUserServiceService()));

return $instance;
