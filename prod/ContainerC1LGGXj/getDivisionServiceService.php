<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Division\Service\DivisionService' shared service.

$this->services['Wizacha\\Marketplace\\Division\\Service\\DivisionService'] = $instance = new \Wizacha\Marketplace\Division\Service\DivisionService(($this->services['marketplace.division.division_repository'] ?? $this->load('getMarketplace_Division_DivisionRepositoryService.php')), ($this->services['Wizacha\\Marketplace\\ReadModel\\ProductRepository'] ?? $this->load('getProductRepository2Service.php')));

$instance->featureIsEnabled(false);

return $instance;
