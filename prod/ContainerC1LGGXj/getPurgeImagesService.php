<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'Wizacha\Component\PurgeResources\Types\PurgeImages' shared autowired service.

return $this->privates['Wizacha\\Component\\PurgeResources\\Types\\PurgeImages'] = new \Wizacha\Component\PurgeResources\Types\PurgeImages(($this->privates['Wizacha\\Component\\PurgeResources\\Report'] ?? ($this->privates['Wizacha\\Component\\PurgeResources\\Report'] = new \Wizacha\Component\PurgeResources\Report())), 100);
