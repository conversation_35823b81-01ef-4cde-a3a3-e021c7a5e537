<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Notification\UserNotifier' shared service.

return $this->services['Wizacha\\AppBundle\\Notification\\UserNotifier'] = new \Wizacha\AppBundle\Notification\UserNotifier(($this->services['app.mailer'] ?? $this->load('getApp_MailerService.php')), ($this->services['templating'] ?? $this->load('getTemplatingService.php')), ($this->services['logger'] ?? $this->load('getLoggerService.php')), ($this->services['Wizacha\\Marketplace\\AdminCompany'] ?? ($this->services['Wizacha\\Marketplace\\AdminCompany'] = new \Wizacha\Marketplace\AdminCompany())), ($this->services['Wizacha\\Marketplace\\User\\UserRepository'] ?? $this->getUserRepositoryService()), ($this->services['router'] ?? $this->getRouterService()), ($this->services['marketplace.monolog.level.mailer'] ?? $this->load('getMarketplace_Monolog_Level_MailerService.php')), ($this->privates['app.bfo_service'] ?? ($this->privates['app.bfo_service'] = new \Wizacha\AppBundle\Service\BFOService('', false))));
