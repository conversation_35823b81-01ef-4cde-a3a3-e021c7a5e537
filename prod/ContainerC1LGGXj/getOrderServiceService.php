<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Order\OrderService' shared service.

return $this->services['Wizacha\\Marketplace\\Order\\OrderService'] = new \Wizacha\Marketplace\Order\OrderService(($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()), ($this->services['marketplace.payment.payment_service'] ?? $this->load('getMarketplace_Payment_PaymentServiceService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Workflow\\WorkflowService'] ?? $this->load('getWorkflowServiceService.php')), ($this->services['Wizacha\\Marketplace\\User\\UserService'] ?? $this->getUserServiceService()), ($this->services['Wizacha\\Marketplace\\Company\\CompanyService'] ?? $this->load('getCompanyServiceService.php')), ($this->services['Wizacha\\Marketplace\\Shipping\\ShippingService'] ?? $this->load('getShippingServiceService.php')), ($this->services['logger'] ?? $this->load('getLoggerService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\Cancel'] ?? $this->load('getCancelService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Discount\\OrderDiscountsCalculator'] ?? ($this->services['Wizacha\\Marketplace\\Order\\Discount\\OrderDiscountsCalculator'] = new \Wizacha\Marketplace\Order\Discount\OrderDiscountsCalculator())), ($this->privates['Wizacha\\Marketplace\\Order\\Repository\\OrderRepository'] ?? $this->load('getOrderRepositoryService.php')), ($this->services['order_amount_repository'] ?? $this->load('getOrderAmountRepositoryService.php')), ($this->services['marketplace.international_tax.shipping'] ?? $this->load('getMarketplace_InternationalTax_ShippingService.php')));
