<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Order\Token\TokenService' shared autowired service.

return $this->services['Wizacha\\Marketplace\\Order\\Token\\TokenService'] = new \Wizacha\Marketplace\Order\Token\TokenService(new \Wizacha\Marketplace\Order\Token\TokenRepository(($this->services['doctrine'] ?? $this->getDoctrineService()), 'Wizacha\\Marketplace\\Order\\Token\\Token'), new \Wizacha\Component\Token\NumericTokenGenerator(), ($this->services['Wizacha\\AppBundle\\Security\\User\\UserService'] ?? $this->load('getUserService2Service.php')));
