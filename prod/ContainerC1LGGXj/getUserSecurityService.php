<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\User\UserSecurity' shared service.

return $this->services['Wizacha\\Marketplace\\User\\UserSecurity'] = new \Wizacha\Marketplace\User\UserSecurity(($this->services['Wizacha\\Marketplace\\User\\UserRepository'] ?? $this->getUserRepositoryService()), ($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()), false, 20);
