<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'console.command.public_alias.Wizacha\AppBundle\Command\AssetsInstallCommand' shared autowired service.

return $this->services['console.command.public_alias.Wizacha\\AppBundle\\Command\\AssetsInstallCommand'] = new \Wizacha\AppBundle\Command\AssetsInstallCommand(($this->services['less_compiler'] ?? ($this->services['less_compiler'] = new \Wizacha\Marketplace\Theme\LessCompiler())), ($this->services['basic.configuration_service'] ?? $this->load('getBasic_ConfigurationServiceService.php')), 'RemotefrontBundle', \dirname(__DIR__, 4));
