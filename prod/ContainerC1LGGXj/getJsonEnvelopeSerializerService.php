<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Messenger\JsonEnvelopeSerializer' shared autowired service.

return $this->services['Wizacha\\Marketplace\\Messenger\\JsonEnvelopeSerializer'] = new \Wizacha\Marketplace\Messenger\JsonEnvelopeSerializer(($this->services['serializer'] ?? $this->load('getSerializerService.php')));
