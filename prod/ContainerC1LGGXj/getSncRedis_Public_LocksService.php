<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'snc_redis.public.locks' shared service.

if ($lazyLoad) {
    return $this->services['snc_redis.public.locks'] = $this->createProxy('Redis_ca5fc0f', function () {
        return \Redis_ca5fc0f::staticProxyConstructor(function (&$wrappedInstance, \ProxyManager\Proxy\LazyLoadingInterface $proxy) {
            $wrappedInstance = $this->load('getSncRedis_Public_LocksService.php', false);

            $proxy->setProxyInitializer(null);

            return true;
        });
    });
}

return (new \Snc\RedisBundle\Factory\PhpredisClientFactory(($this->privates['snc_redis.logger'] ?? $this->load('getSncRedis_LoggerService.php'))))->create('Redis', 'redis://localhost:6379/1', ['prefix' => 'locks.', 'connection_async' => false, 'connection_persistent' => false, 'connection_timeout' => 5, 'read_write_timeout' => NULL, 'iterable_multibulk' => false, 'throw_errors' => true, 'serialization' => 'default', 'profile' => 'default', 'cluster' => NULL, 'service' => NULL], 'locks');
