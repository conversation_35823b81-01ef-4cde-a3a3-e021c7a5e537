<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'marketplace.catalog' shared service.

return $this->services['marketplace.catalog'] = new \Wizacha\Marketplace\Catalog\CatalogService(($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()), ($this->services['Wizacha\\Marketplace\\ReadModel\\ProductProjector'] ?? $this->load('getProductProjectorService.php')), ($this->services['Wizacha\\Async\\Dispatcher'] ?? $this->load('getDispatcherService.php')));
