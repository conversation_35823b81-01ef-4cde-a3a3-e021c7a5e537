<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'Wizacha\AppBundle\EventSubscriber\Yavin\User\UserCreatedSubscriber' shared autowired service.

return $this->privates['Wizacha\\AppBundle\\EventSubscriber\\Yavin\\User\\UserCreatedSubscriber'] = new \Wizacha\AppBundle\EventSubscriber\Yavin\User\UserCreatedSubscriber(($this->services['Wizacha\\Marketplace\\Messenger\\BroadcastPublisher'] ?? $this->load('getBroadcastPublisherService.php')));
