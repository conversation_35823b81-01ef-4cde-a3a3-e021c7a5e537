<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'event.subscriber.search' shared service.

return $this->services['event.subscriber.search'] = new \Wizacha\Search\EventSubscriber(($this->services['marketplace.search.category_index'] ?? $this->load('getMarketplace_Search_CategoryIndexService.php')), ($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()), ($this->services['marketplace.pim.attribute_service'] ?? $this->load('getMarketplace_Pim_AttributeServiceService.php')), ($this->services['Wizacha\\Marketplace\\ReadModel\\ProductProjector'] ?? $this->load('getProductProjectorService.php')), ($this->services['Wizacha\\Marketplace\\PIM\\Product\\ProductService'] ?? $this->load('getProductService2Service.php')), ($this->services['Wizacha\\Marketplace\\PIM\\MultiVendorProduct\\MultiVendorProductService'] ?? $this->load('getMultiVendorProductServiceService.php')), ($this->services['marketplace.search.product_index'] ?? $this->load('getMarketplace_Search_ProductIndexService.php')), ($this->services['Wizacha\\Async\\Dispatcher'] ?? $this->load('getDispatcherService.php')), ($this->services['marketplace.product.declination_factory'] ?? ($this->services['marketplace.product.declination_factory'] = new \Wizacha\Marketplace\Entities\DeclinationFactory())));
