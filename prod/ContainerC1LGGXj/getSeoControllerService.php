<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\SeoController' shared autowired service.

return $this->services['Wizacha\\AppBundle\\Controller\\Api\\SeoController'] = new \Wizacha\AppBundle\Controller\Api\SeoController(($this->services['Wizacha\\Marketplace\\Seo\\SeoService'] ?? $this->getSeoServiceService()));
