<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'Wizacha\AppBundle\Command\HadesBackupCommand' shared autowired service.

$this->privates['Wizacha\\AppBundle\\Command\\HadesBackupCommand'] = $instance = new \Wizacha\AppBundle\Command\HadesBackupCommand('wizaplace-loc', new \Aws\S3\S3Client(['version' => '2006-03-01', 'region' => 'eu-west-1', 'credentials' => ['key' => '', 'secret' => '']]), 'wizachatest');

$instance->setName('hades:backup');

return $instance;
