<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\CreditNoteController' shared autowired service.

$this->services['Wizacha\\AppBundle\\Controller\\Api\\CreditNoteController'] = $instance = new \Wizacha\AppBundle\Controller\Api\CreditNoteController(($this->services['router'] ?? $this->getRouterService()), ($this->services['marketplace.order.refund.refund_service'] ?? $this->load('getMarketplace_Order_Refund_RefundServiceService.php')), ($this->services['Wizacha\\Marketplace\\Order\\OrderService'] ?? $this->load('getOrderServiceService.php')), ($this->services['marketplace.order.credit_note.credit_note_service'] ?? $this->load('getMarketplace_Order_CreditNote_CreditNoteServiceService.php')), ($this->services['marketplace.order.credit_note.credit_note_helper'] ?? $this->load('getMarketplace_Order_CreditNote_CreditNoteHelperService.php')), ($this->services['pdf_generator'] ?? $this->load('getPdfGeneratorService.php')));

$instance->setCurrency('€');

return $instance;
