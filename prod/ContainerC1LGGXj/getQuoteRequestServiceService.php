<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Quotation\QuoteRequestService' shared autowired service.

return $this->services['Wizacha\\Marketplace\\Quotation\\QuoteRequestService'] = new \Wizacha\Marketplace\Quotation\QuoteRequestService(($this->services['Wizacha\\Marketplace\\Quotation\\Repository\\QuoteRequestSelectionRepository'] ?? $this->load('getQuoteRequestSelectionRepositoryService.php')), ($this->services['Wizacha\\Marketplace\\Quotation\\Repository\\QuoteRequestSelectionDeclinationRepository'] ?? $this->load('getQuoteRequestSelectionDeclinationRepositoryService.php')), ($this->services['Wizacha\\Marketplace\\ReadModel\\ProductRepository'] ?? $this->load('getProductRepository2Service.php')));
