<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\CreditCard\Repository\CreditCardRepository' shared service.

return $this->services['Wizacha\\Marketplace\\CreditCard\\Repository\\CreditCardRepository'] = new \Wizacha\Marketplace\CreditCard\Repository\CreditCardRepository(($this->services['doctrine'] ?? $this->getDoctrineService()), 'Wizacha\\Marketplace\\CreditCard\\CreditCard');
