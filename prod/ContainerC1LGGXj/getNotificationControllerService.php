<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Backend\NotificationController' shared service.

return $this->services['Wizacha\\AppBundle\\Controller\\Backend\\NotificationController'] = new \Wizacha\AppBundle\Controller\Backend\NotificationController(($this->services['Wizacha\\AppBundle\\Notification\\NotificationConfigService'] ?? $this->load('getNotificationConfigServiceService.php')));
