<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Company\CompanyService' shared autowired service.

return $this->services['Wizacha\\Marketplace\\Company\\CompanyService'] = new \Wizacha\Marketplace\Company\CompanyService(($this->services['logger'] ?? $this->load('getLoggerService.php')), ($this->services['Wizacha\\Marketplace\\User\\UserService'] ?? $this->getUserServiceService()), ($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()), ($this->services['Wizacha\\ImageManager'] ?? $this->load('getImageManagerService.php')), ($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()), ($this->services['Wizacha\\Marketplace\\Company\\CompanyRepository'] ?? $this->load('getCompanyRepositoryService.php')), ($this->services['Wizacha\\Storage\\VendorSubscriptionStorageService'] ?? $this->load('getVendorSubscriptionStorageServiceService.php')), ($this->services['marketplace.invoicing_settings_service'] ?? $this->load('getMarketplace_InvoicingSettingsServiceService.php')));
