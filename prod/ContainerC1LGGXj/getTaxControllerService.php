<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Backend\TaxController' shared service.

return $this->services['Wizacha\\AppBundle\\Controller\\Backend\\TaxController'] = new \Wizacha\AppBundle\Controller\Backend\TaxController(($this->services['marketplace.international_tax.shipping'] ?? $this->load('getMarketplace_InternationalTax_ShippingService.php')));
