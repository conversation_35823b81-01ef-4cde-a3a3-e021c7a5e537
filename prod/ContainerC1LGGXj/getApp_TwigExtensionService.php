<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'app.twig_extension' shared service.

return $this->services['app.twig_extension'] = new \Wizacha\AppBundle\Twig\AppExtension(($this->services['marketplace.price.formatter'] ?? ($this->services['marketplace.price.formatter'] = new \Wizacha\Marketplace\Price\DetailedPriceFormatter('EUR'))), ($this->services['marketplace.backend.price.formatter'] ?? ($this->services['marketplace.backend.price.formatter'] = new \Wizacha\Marketplace\Price\DetailedPriceFormatter('EUR'))), ($this->services['assets.packages'] ?? $this->load('getAssets_PackagesService.php')), ($this->services['session'] ?? $this->load('getSessionService.php')), $this, ($this->services['basic.configuration_service'] ?? $this->load('getBasic_ConfigurationServiceService.php')));
