<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Payment\LemonWay\LemonWayHttpClient' shared service.

$a = new \Wizacha\Marketplace\Payment\LemonWay\LemonWayLogger(($this->services['monolog.logger.psp'] ?? $this->getMonolog_Logger_PspService()), ($this->services['serializer'] ?? $this->load('getSerializerService.php')));
$a->setDataAnonymizer(new \Wizacha\Marketplace\Payment\Security\LemonWayDataAnonymizer());

return $this->services['Wizacha\\Marketplace\\Payment\\LemonWay\\LemonWayHttpClient'] = new \Wizacha\Marketplace\Payment\LemonWay\LemonWayHttpClient($a, ($this->services['request_stack'] ?? ($this->services['request_stack'] = new \Symfony\Component\HttpFoundation\RequestStack())), $this->getEnv('LEMONWAY_API_DIRECTKIT_URL'), $this->getEnv('LEMONWAY_API_CLIENT_LOGIN'), $this->getEnv('LEMONWAY_API_CLIENT_PASSWORD'), $this->getEnv('LEMONWAY_PROXY_HOST'), $this->getEnv('int:LEMONWAY_PROXY_PORT'), $this->getEnv('LEMONWAY_PROXY_USER'), $this->getEnv('LEMONWAY_PROXY_PASSWORD'));
