<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'rulerz' shared service.

return $this->services['rulerz'] = new \RulerZ\RulerZ(($this->services['RulerZ\\Compiler\\Compiler'] ?? $this->load('getCompilerService.php')), [0 => ($this->services['RulerZ\\Target\\Native\\Native'] ?? $this->load('getNativeService.php')), 1 => ($this->services['RulerZ\\Target\\DoctrineDBAL\\DoctrineDBAL'] ?? ($this->services['RulerZ\\Target\\DoctrineDBAL\\DoctrineDBAL'] = new \RulerZ\Target\DoctrineDBAL\DoctrineDBAL()))]);
