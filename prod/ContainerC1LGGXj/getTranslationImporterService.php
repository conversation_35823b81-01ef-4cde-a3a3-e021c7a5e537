<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Exim\Import\TranslationImporter' shared service.

return $this->services['Wizacha\\Exim\\Import\\TranslationImporter'] = new \Wizacha\Exim\Import\TranslationImporter(($this->services['Wizacha\\Async\\Dispatcher'] ?? $this->load('getDispatcherService.php')), ($this->services['Wizacha\\AppBundle\\Service\\TranslationService'] ?? $this->load('getTranslationServiceService.php')));
