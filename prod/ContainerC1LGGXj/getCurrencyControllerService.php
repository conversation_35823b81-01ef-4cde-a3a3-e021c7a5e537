<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\CurrencyController' shared autowired service.

return $this->services['Wizacha\\AppBundle\\Controller\\Api\\CurrencyController'] = new \Wizacha\AppBundle\Controller\Api\CurrencyController(($this->services['Wizacha\\Marketplace\\Currency\\CurrencyService'] ?? $this->getCurrencyServiceService()));
