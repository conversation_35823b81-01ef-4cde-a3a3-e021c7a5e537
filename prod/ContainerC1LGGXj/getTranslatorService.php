<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'translator' shared service.

return $this->services['translator'] = new \Wizacha\Bridge\Symfony\Translator(($this->privates['translator.default'] ?? $this->load('getTranslator_DefaultService.php')), ($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()), ($this->targetDir.''.'/translations'), false);
