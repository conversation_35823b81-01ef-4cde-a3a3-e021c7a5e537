<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\GroupController' shared autowired service.

$this->services['Wizacha\\AppBundle\\Controller\\Api\\GroupController'] = $instance = new \Wizacha\AppBundle\Controller\Api\GroupController(($this->services['Wizacha\\Marketplace\\Group\\UserGroupService'] ?? $this->load('getUserGroupServiceService.php')));

$instance->setContainer(($this->privates['.service_locator.vdmMuyE'] ?? $this->load('get_ServiceLocator_VdmMuyEService.php'))->withContext('Wizacha\\AppBundle\\Controller\\Api\\GroupController', $this));

return $instance;
