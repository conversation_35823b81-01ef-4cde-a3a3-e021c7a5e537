<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'Wizacha\AppBundle\Command\ExportCatalogCommand' shared autowired service.

$this->privates['Wizacha\\AppBundle\\Command\\ExportCatalogCommand'] = $instance = new \Wizacha\AppBundle\Command\ExportCatalogCommand(($this->services['marketplace.catalog.exporter'] ?? $this->load('getMarketplace_Catalog_ExporterService.php')));

$instance->setLogger(($this->services['logger'] ?? $this->load('getLoggerService.php')));
$instance->setName('export:catalog');

return $instance;
