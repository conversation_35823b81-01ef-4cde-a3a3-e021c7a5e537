<?php

return [
    '.1_<PERSON>trineProvider~MuH03m3' => true,
    '.1_LegacyRouteLoaderContainer~MqrjvUI' => true,
    '.1_NativeFileSessionHandler~LNOJmW.' => true,
    '.1_ServiceLocator~jkva3E.' => true,
    '.1_Swift_Transport_Esmtp_Auth_CramMd5Authenticator~n.Bnhn4' => true,
    '.1_bool~rJSI2wc' => true,
    '.1_~.UBqiR_' => true,
    '.1_~J.rB0tD' => true,
    '.2_ArrayAdapter~MuH03m3' => true,
    '.2_Swift_Transport_Esmtp_Auth_LoginAuthenticator~n.Bnhn4' => true,
    '.2_~.UBqiR_' => true,
    '.2_~J.rB0tD' => true,
    '.3_Swift_Transport_Esmtp_Auth_PlainAuthenticator~n.Bnhn4' => true,
    '.4_Swift_Transport_Esmtp_Auth_NTLMAuthenticator~n.Bnhn4' => true,
    '.abstract.instanceof.Wizacha\\ActionLogger\\EventSubscribers\\DispatchFundsFailedSubscriber' => true,
    '.abstract.instanceof.Wizacha\\ActionLogger\\EventSubscribers\\OrderSubscriber' => true,
    '.abstract.instanceof.Wizacha\\ActionLogger\\EventSubscribers\\ProductSubscriber' => true,
    '.abstract.instanceof.Wizacha\\ActionLogger\\EventSubscribers\\ShippingSubscriber' => true,
    '.abstract.instanceof.Wizacha\\ActionLogger\\EventSubscribers\\TransactionSubscriber' => true,
    '.abstract.instanceof.Wizacha\\ActionLogger\\EventSubscribers\\UserEventSubscriber' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\AdminInstallCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\Algolia\\ClearAlgoliaIndexCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\Algolia\\CreateAlgoliaIndexCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\Algolia\\PushAlgoliaConfigCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\Algolia\\PushAlgoliaProductsCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\Algolia\\UpdateAlgoliaProductsCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\AmqpDebouncerBenchmarkCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\AmqpDebouncerStatsCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\AmqpDebouncerTriggerCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\AssetsInstallCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\AuthLogClearCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\AuthLogPurgeCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\BasketsDeleteAllCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\BenchmarkFixtureCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkAlgoliaCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkDatabaseCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkQueueCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkRedisCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkStorageCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\CancelEximJobsImport' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\CatalogJsonExporterCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\CheckIncompleteOrdersCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\CheckMangopayUserIdCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\CheckValidityMarketplaceDiscountCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\ClearEximJobsCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\ClearPaymentDataBeforeProductionCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\ClearTemporaryResourcesCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\CommissionsCheckConfigCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\CommissionsRecalculateCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\ConsumeAmqpMessagesCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\CreateAdminUserCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\CreateAmqpQueuesCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\CreateCompanyWalletCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\CreateIndexAlgoliaCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\CreateSimpleUserCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\CreateUserFromCSVCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\DebugRegistryCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\DebugUnserializeCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\DebugVariablesCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\DeleteAllMultiVendorProductCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\DeleteAllOrdersCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\DeleteOrdersAmountsCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\DeleteOrdersCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\DeleteOrganisationsCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\DeployAlgoliaUpdateCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\DeployDebugCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\DeployMigrateDivisionCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\DeployMigrateOrderTransactionsCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\DeployMigrateOrderTransactionsIsBackCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\DeployMigrateRefunds' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\DeployOnceCompanyEmailSettingsCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\DeployPostActionsCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\DeployRemoveAssetsAwsCacheCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\DeployRunCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\DeploySetTransactionCompanyIdAndCurrencyCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\DeploySetTransactionOriginAndDestinationCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\DeploySetUsersPasswordsHistoryCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\DeploySystemOptions' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\DispatchFundsCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\Division\\ImportDivisionProductsCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\DropMarketplaceDatabaseCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\DumpAnonymizerCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\EndBlockedDispatchedOrdersCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\EndOrdersWithdrawalPeriodCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\ExportCatalogCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\ExportCompanyTokenCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\FixCorruptedSerializedDataCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\FixDeclinationInventoryCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\FixDuplicateVariationsCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\FixDuplicatedMainImageLinksCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\FixOrderCommissionSyncCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\FixOrderDecryptPaymentInformation' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\FixOrderInvoiceDataCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\FixOrdersEmailCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\GenerateSitemapCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\HadesBackupCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\HipayCheckKycCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\ImportAutomatedFeedsCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\ImportMiraklCompanyStatusCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\ImportThemeTranslationsCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\Janus\\JanusExportCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\Janus\\JanusImportCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\KPIPublishCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\LemonWayCheckKycCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\LemonwaySendKycCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\LemonwayTransactionSyncCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\ListAllFeaturesCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\ListAmqpQueuesCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\ListEximImportMessages' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\ListEximJobsImport' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\LoadFixtureCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\MagentoPasswordHashUpdateCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\MangoPayCheckKycCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\MangoPaySendKycCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\MarkOrdersAsDeliveredCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\MiraklCreateCompanyAdministrators' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\MiraklOrderForceCompletedCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\MiraklOrderForceFromProcessingToProcessedCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\Moderation\\PurgeProductInProgressCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\OrderAmountsRecalculateCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\OrderCancelRollbackCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\OrderCheckAcceptationDelayCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\OrderCompletedStatsCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\OrderCreationStatsCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\OrderExecuteActionCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\OrderListActionsCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\OrderRefundOfflineMarkAsPaidCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\OrdersResetDispatchFundsCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\PayPaymentDefermentOrdersCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\PayoutCompaniesCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\PrediggoExportCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\PriceTiers\\DeleteComplexPriceTiersCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\ProductRefreshCacheCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\ProductVisibilityCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\PspCheckKycCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\PspSendKycCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\PurgeResourcesCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\PushVendorInfoToPspCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\Readmodel\\CleanReadmodelCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\Readmodel\\ClearReadmodelCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\Readmodel\\CreateReadmodelCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\Readmodel\\DumpReadmodelCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\Readmodel\\UpdateReadmodelCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\RecountProductInCategoriesCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\RefreshNewPromotionsInSearchCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\RemovePromotionsOnIrrelevantOrdersCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\RenameProductAttachmentCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\RenewSubscriptionCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\ResetProgressForProductInModeration' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\RgpdCompanyAnonymizerCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\RgpdUserAnonymizerCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\RollbackProcessedOrdersCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\SMoneyCheckKycCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\SMoneySendKycCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\SeoRefreshNamesCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\StripeRemoveConsumedMandatesCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\TranslationCacheRefreshCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\TranslationMissingKeysCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\TrashAbandonedOrdersCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\UpdateCurrencyRatesCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\UpdateOrdersToCompletedCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\UpdateTranslationFromCsvCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\UpdateTranslationOrderStatusCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\UserAccessToken' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\UserUpdateCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\WorkflowToGraphvizCommand' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Command\\Yavin\\UploadUsersToAuthGateway' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\BasketController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\Catalog\\CompanyController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\Catalog\\ExportController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\Catalog\\ProductController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\CommissionController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\CompanyController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\CompanyPerson\\CompanyPersonController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\CreditCardController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\CreditNoteController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\CurrencyController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\DiscussionController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\Division\\DivisionController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\GroupController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\ImportController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\JobController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\LanguageController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\OrderAdjustmentController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\OrderController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\OrderDetailsController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\OrderTransactionsController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\Order\\HandDeliveryController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\Order\\MondialRelayController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\Order\\OrderActionController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\Organisation\\OrganisationBasketController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\PaymentController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\Payment\\DirectDebitPaymentController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\ProductController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\Promotion\\BasketPromotionNormalizer' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\Promotion\\BasketPromotionsController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\Promotion\\CatalogPromotionNormalizer' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\Promotion\\CatalogPromotionsController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\Promotion\\MarketplacePromotionsController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\Quotation\\QuoteRequestSelectionController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\RelatedProduct\\RelatedProductController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\SeoController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\SubscriptionController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\TransactionTransferController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Api\\TranslationController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Backend\\AuthController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Backend\\CurrencyController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Backend\\DiscussController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Backend\\InvoicingSettingsController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Backend\\MangopayController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Backend\\MultiVendorProductController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\Backend\\UserGroupController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Controller\\DocumentationController' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\EventSubscriber\\ApiApplicationFirewall' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\EventSubscriber\\PspRequestSubscriber' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Security\\Listener\\UserAuthenticatedListener' => true,
    '.abstract.instanceof.Wizacha\\AppBundle\\Security\\Listener\\UserAuthenticationFailureListener' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Division\\Service\\DivisionBlacklistsService' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Division\\Service\\DivisionProductsService' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Order\\Action\\Accept' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Order\\Action\\Cancel' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Order\\Action\\CommitTo' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Order\\Action\\Confirm' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Order\\Action\\DeclareInvoiceNumberGeneratedElsewhere' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Order\\Action\\DeclareParcelLost' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Order\\Action\\DispatchFunds' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Order\\Action\\DispatchFundsFailed' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Order\\Action\\DispatchFundsSucceeded' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Order\\Action\\EndWithdrawalPeriod' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Order\\Action\\MarkAsDelivered' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Order\\Action\\MarkAsFinished' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Order\\Action\\MarkAsPaid' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Order\\Action\\MarkAsShipped' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Order\\Action\\MarkPaymentAsRefused' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Order\\Action\\MarkPaymentAuthorizationAsCaptured' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Order\\Action\\MarkPaymentAuthorizationAsRefused' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Order\\Action\\MarkPaymentDefermentAsAuthorized' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Order\\Action\\MarkPaymentDefermentAsRefused' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Order\\Action\\ProvideInvoiceNumber' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Order\\Action\\RedirectToPaymentProcessor' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Order\\Action\\Refuse' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Order\\Action\\Trash' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Order\\OrderAction\\Event\\OrderStatusUpdatedEventSubscriber' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\PIM\\Option\\Handler\\CommitmentPeriodHandler' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\PIM\\Option\\Handler\\PaymentFrequencyHandler' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\PIM\\Product\\Template\\ProductInfoFormType' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\PIM\\Product\\Template\\TemplateService' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Payment\\Stripe\\Handler\\CustomerSourceCreatedHandler' => true,
    '.abstract.instanceof.Wizacha\\Marketplace\\Transaction\\Form\\CommissionTransactionType' => true,
    '.abstract.instanceof.marketplace.order.refund.refund_event_subscriber' => true,
    '.abstract.instanceof.marketplace.promotion.promotion_subscriber' => true,
    '.abstract.instanceof.marketplace.subscription.subscription_subscriber' => true,
    '.abstract.instanceof.marketplace.transction.transfers.failed' => true,
    '.abstract.instanceof.monolog.logger.console_command' => true,
    '.abstract.instanceof.monolog.logger.http_request' => true,
    '.abstract.instanceof.monolog.processor.console_command' => true,
    '.abstract.instanceof.monolog.processor.http_request' => true,
    '.cache_connection.GD_MSZC' => true,
    '.cache_connection.JKE6keX' => true,
    '.errored..service_locator.5G6k4BF.Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' => true,
    '.errored..service_locator.mx_yHOn.Symfony\\Component\\HttpFoundation\\Response' => true,
    '.errored..service_locator.pEXF19J.Wizacha\\Marketplace\\Order\\Order' => true,
    '.errored..service_locator.pEXF19J.Wizacha\\Marketplace\\User\\WizaplaceUserInterface' => true,
    '.instanceof.Psr\\Log\\LoggerAwareInterface.0.Wizacha\\AppBundle\\Command\\CheckValidityMarketplaceDiscountCommand' => true,
    '.instanceof.Psr\\Log\\LoggerAwareInterface.0.Wizacha\\AppBundle\\Command\\DispatchFundsCommand' => true,
    '.instanceof.Psr\\Log\\LoggerAwareInterface.0.Wizacha\\AppBundle\\Command\\ExportCatalogCommand' => true,
    '.instanceof.Psr\\Log\\LoggerAwareInterface.0.Wizacha\\AppBundle\\Command\\LemonWayCheckKycCommand' => true,
    '.instanceof.Psr\\Log\\LoggerAwareInterface.0.Wizacha\\AppBundle\\Command\\MangoPayCheckKycCommand' => true,
    '.instanceof.Psr\\Log\\LoggerAwareInterface.0.Wizacha\\AppBundle\\Command\\PayoutCompaniesCommand' => true,
    '.instanceof.Psr\\Log\\LoggerAwareInterface.0.Wizacha\\AppBundle\\Command\\SMoneyCheckKycCommand' => true,
    '.instanceof.Psr\\Log\\LoggerAwareInterface.0.monolog.logger.console_command' => true,
    '.instanceof.Psr\\Log\\LoggerAwareInterface.0.monolog.logger.http_request' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.Wizacha\\AppBundle\\Controller\\Api\\BasketController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.Wizacha\\AppBundle\\Controller\\Api\\CompanyPerson\\CompanyPersonController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.Wizacha\\AppBundle\\Controller\\Api\\GroupController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.Wizacha\\AppBundle\\Controller\\Api\\Order\\OrderActionController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\AbstractController.0.Wizacha\\AppBundle\\Controller\\Api\\TransactionTransferController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\Catalog\\CompanyController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\Catalog\\ExportController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\Catalog\\ProductController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\CommissionController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\CompanyController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\CreditCardController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\CreditNoteController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\CurrencyController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\DiscussionController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\Division\\DivisionController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\ImportController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\JobController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\LanguageController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\OrderAdjustmentController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\OrderController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\OrderDetailsController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\OrderTransactionsController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\Order\\HandDeliveryController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\Order\\MondialRelayController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\Organisation\\OrganisationBasketController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\PaymentController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\Payment\\DirectDebitPaymentController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\ProductController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\Promotion\\BasketPromotionsController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\Promotion\\CatalogPromotionsController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\Promotion\\MarketplacePromotionsController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\Quotation\\QuoteRequestSelectionController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\RelatedProduct\\RelatedProductController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\SeoController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\SubscriptionController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Api\\TranslationController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Backend\\AuthController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Backend\\CurrencyController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Backend\\DiscussController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Backend\\InvoicingSettingsController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Backend\\MangopayController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Backend\\MultiVendorProductController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\Backend\\UserGroupController' => true,
    '.instanceof.Symfony\\Bundle\\FrameworkBundle\\Controller\\Controller.0.Wizacha\\AppBundle\\Controller\\DocumentationController' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\AdminInstallCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\Algolia\\ClearAlgoliaIndexCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\Algolia\\CreateAlgoliaIndexCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\Algolia\\PushAlgoliaConfigCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\Algolia\\PushAlgoliaProductsCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\Algolia\\UpdateAlgoliaProductsCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\AmqpDebouncerBenchmarkCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\AmqpDebouncerStatsCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\AmqpDebouncerTriggerCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\AssetsInstallCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\AuthLogClearCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\AuthLogPurgeCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\BasketsDeleteAllCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\BenchmarkFixtureCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkAlgoliaCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkDatabaseCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkQueueCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkRedisCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkStorageCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\CancelEximJobsImport' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\CatalogJsonExporterCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\CheckIncompleteOrdersCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\CheckMangopayUserIdCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\CheckValidityMarketplaceDiscountCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\ClearEximJobsCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\ClearPaymentDataBeforeProductionCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\ClearTemporaryResourcesCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\CommissionsCheckConfigCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\CommissionsRecalculateCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\ConsumeAmqpMessagesCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\CreateAdminUserCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\CreateAmqpQueuesCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\CreateCompanyWalletCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\CreateIndexAlgoliaCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\CreateSimpleUserCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\CreateUserFromCSVCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\DebugRegistryCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\DebugUnserializeCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\DebugVariablesCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\DeleteAllMultiVendorProductCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\DeleteAllOrdersCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\DeleteOrdersAmountsCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\DeleteOrdersCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\DeleteOrganisationsCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\DeployAlgoliaUpdateCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\DeployDebugCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\DeployMigrateDivisionCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\DeployMigrateOrderTransactionsCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\DeployMigrateOrderTransactionsIsBackCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\DeployMigrateRefunds' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\DeployOnceCompanyEmailSettingsCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\DeployPostActionsCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\DeployRemoveAssetsAwsCacheCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\DeployRunCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\DeploySetTransactionCompanyIdAndCurrencyCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\DeploySetTransactionOriginAndDestinationCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\DeploySetUsersPasswordsHistoryCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\DeploySystemOptions' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\DispatchFundsCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\Division\\ImportDivisionProductsCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\DropMarketplaceDatabaseCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\DumpAnonymizerCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\EndBlockedDispatchedOrdersCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\EndOrdersWithdrawalPeriodCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\ExportCatalogCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\ExportCompanyTokenCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\FixCorruptedSerializedDataCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\FixDeclinationInventoryCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\FixDuplicateVariationsCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\FixDuplicatedMainImageLinksCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\FixOrderCommissionSyncCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\FixOrderDecryptPaymentInformation' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\FixOrderInvoiceDataCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\FixOrdersEmailCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\GenerateSitemapCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\HadesBackupCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\HipayCheckKycCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\ImportAutomatedFeedsCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\ImportMiraklCompanyStatusCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\ImportThemeTranslationsCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\Janus\\JanusExportCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\Janus\\JanusImportCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\KPIPublishCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\LemonWayCheckKycCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\LemonwaySendKycCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\LemonwayTransactionSyncCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\ListAllFeaturesCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\ListAmqpQueuesCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\ListEximImportMessages' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\ListEximJobsImport' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\LoadFixtureCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\MagentoPasswordHashUpdateCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\MangoPayCheckKycCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\MangoPaySendKycCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\MarkOrdersAsDeliveredCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\MiraklCreateCompanyAdministrators' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\MiraklOrderForceCompletedCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\MiraklOrderForceFromProcessingToProcessedCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\Moderation\\PurgeProductInProgressCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\OrderAmountsRecalculateCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\OrderCancelRollbackCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\OrderCheckAcceptationDelayCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\OrderCompletedStatsCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\OrderCreationStatsCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\OrderExecuteActionCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\OrderListActionsCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\OrderRefundOfflineMarkAsPaidCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\OrdersResetDispatchFundsCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\PayPaymentDefermentOrdersCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\PayoutCompaniesCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\PrediggoExportCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\PriceTiers\\DeleteComplexPriceTiersCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\ProductRefreshCacheCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\ProductVisibilityCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\PspCheckKycCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\PspSendKycCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\PurgeResourcesCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\PushVendorInfoToPspCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\Readmodel\\CleanReadmodelCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\Readmodel\\ClearReadmodelCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\Readmodel\\CreateReadmodelCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\Readmodel\\DumpReadmodelCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\Readmodel\\UpdateReadmodelCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\RecountProductInCategoriesCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\RefreshNewPromotionsInSearchCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\RemovePromotionsOnIrrelevantOrdersCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\RenameProductAttachmentCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\RenewSubscriptionCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\ResetProgressForProductInModeration' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\RgpdCompanyAnonymizerCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\RgpdUserAnonymizerCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\RollbackProcessedOrdersCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\SMoneyCheckKycCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\SMoneySendKycCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\SeoRefreshNamesCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\StripeRemoveConsumedMandatesCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\TranslationCacheRefreshCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\TranslationMissingKeysCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\TrashAbandonedOrdersCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\UpdateCurrencyRatesCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\UpdateOrdersToCompletedCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\UpdateTranslationFromCsvCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\UpdateTranslationOrderStatusCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\UserAccessToken' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\UserUpdateCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\WorkflowToGraphvizCommand' => true,
    '.instanceof.Symfony\\Component\\Console\\Command\\Command.0.Wizacha\\AppBundle\\Command\\Yavin\\UploadUsersToAuthGateway' => true,
    '.instanceof.Symfony\\Component\\EventDispatcher\\EventSubscriberInterface.0.Wizacha\\ActionLogger\\EventSubscribers\\DispatchFundsFailedSubscriber' => true,
    '.instanceof.Symfony\\Component\\EventDispatcher\\EventSubscriberInterface.0.Wizacha\\ActionLogger\\EventSubscribers\\OrderSubscriber' => true,
    '.instanceof.Symfony\\Component\\EventDispatcher\\EventSubscriberInterface.0.Wizacha\\ActionLogger\\EventSubscribers\\ProductSubscriber' => true,
    '.instanceof.Symfony\\Component\\EventDispatcher\\EventSubscriberInterface.0.Wizacha\\ActionLogger\\EventSubscribers\\ShippingSubscriber' => true,
    '.instanceof.Symfony\\Component\\EventDispatcher\\EventSubscriberInterface.0.Wizacha\\ActionLogger\\EventSubscribers\\TransactionSubscriber' => true,
    '.instanceof.Symfony\\Component\\EventDispatcher\\EventSubscriberInterface.0.Wizacha\\ActionLogger\\EventSubscribers\\UserEventSubscriber' => true,
    '.instanceof.Symfony\\Component\\EventDispatcher\\EventSubscriberInterface.0.Wizacha\\AppBundle\\EventSubscriber\\ApiApplicationFirewall' => true,
    '.instanceof.Symfony\\Component\\EventDispatcher\\EventSubscriberInterface.0.Wizacha\\AppBundle\\EventSubscriber\\PspRequestSubscriber' => true,
    '.instanceof.Symfony\\Component\\EventDispatcher\\EventSubscriberInterface.0.Wizacha\\AppBundle\\Security\\Listener\\UserAuthenticatedListener' => true,
    '.instanceof.Symfony\\Component\\EventDispatcher\\EventSubscriberInterface.0.Wizacha\\AppBundle\\Security\\Listener\\UserAuthenticationFailureListener' => true,
    '.instanceof.Symfony\\Component\\EventDispatcher\\EventSubscriberInterface.0.Wizacha\\Marketplace\\Division\\Service\\DivisionBlacklistsService' => true,
    '.instanceof.Symfony\\Component\\EventDispatcher\\EventSubscriberInterface.0.Wizacha\\Marketplace\\Division\\Service\\DivisionProductsService' => true,
    '.instanceof.Symfony\\Component\\EventDispatcher\\EventSubscriberInterface.0.Wizacha\\Marketplace\\Order\\OrderAction\\Event\\OrderStatusUpdatedEventSubscriber' => true,
    '.instanceof.Symfony\\Component\\EventDispatcher\\EventSubscriberInterface.0.Wizacha\\Marketplace\\PIM\\Product\\Template\\TemplateService' => true,
    '.instanceof.Symfony\\Component\\EventDispatcher\\EventSubscriberInterface.0.marketplace.order.refund.refund_event_subscriber' => true,
    '.instanceof.Symfony\\Component\\EventDispatcher\\EventSubscriberInterface.0.marketplace.promotion.promotion_subscriber' => true,
    '.instanceof.Symfony\\Component\\EventDispatcher\\EventSubscriberInterface.0.marketplace.subscription.subscription_subscriber' => true,
    '.instanceof.Symfony\\Component\\EventDispatcher\\EventSubscriberInterface.0.marketplace.transction.transfers.failed' => true,
    '.instanceof.Symfony\\Component\\EventDispatcher\\EventSubscriberInterface.0.monolog.logger.console_command' => true,
    '.instanceof.Symfony\\Component\\EventDispatcher\\EventSubscriberInterface.0.monolog.logger.http_request' => true,
    '.instanceof.Symfony\\Component\\EventDispatcher\\EventSubscriberInterface.0.monolog.processor.console_command' => true,
    '.instanceof.Symfony\\Component\\EventDispatcher\\EventSubscriberInterface.0.monolog.processor.http_request' => true,
    '.instanceof.Symfony\\Component\\Form\\FormTypeInterface.0.Wizacha\\Marketplace\\PIM\\Product\\Template\\ProductInfoFormType' => true,
    '.instanceof.Symfony\\Component\\Form\\FormTypeInterface.0.Wizacha\\Marketplace\\Transaction\\Form\\CommissionTransactionType' => true,
    '.instanceof.Symfony\\Component\\Serializer\\Normalizer\\DenormalizerInterface.0.Wizacha\\AppBundle\\Controller\\Api\\Promotion\\BasketPromotionNormalizer' => true,
    '.instanceof.Symfony\\Component\\Serializer\\Normalizer\\DenormalizerInterface.0.Wizacha\\AppBundle\\Controller\\Api\\Promotion\\CatalogPromotionNormalizer' => true,
    '.instanceof.Symfony\\Component\\Serializer\\Normalizer\\NormalizerInterface.0.Wizacha\\AppBundle\\Controller\\Api\\Promotion\\BasketPromotionNormalizer' => true,
    '.instanceof.Symfony\\Component\\Serializer\\Normalizer\\NormalizerInterface.0.Wizacha\\AppBundle\\Controller\\Api\\Promotion\\CatalogPromotionNormalizer' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.Wizacha\\AppBundle\\Controller\\Api\\BasketController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.Wizacha\\AppBundle\\Controller\\Api\\CompanyPerson\\CompanyPersonController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.Wizacha\\AppBundle\\Controller\\Api\\GroupController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.Wizacha\\AppBundle\\Controller\\Api\\Order\\OrderActionController' => true,
    '.instanceof.Symfony\\Contracts\\Service\\ServiceSubscriberInterface.0.Wizacha\\AppBundle\\Controller\\Api\\TransactionTransferController' => true,
    '.instanceof.Wizacha\\Marketplace\\Order\\Action\\OrderWorkflowActionInterface.0.Wizacha\\Marketplace\\Order\\Action\\Accept' => true,
    '.instanceof.Wizacha\\Marketplace\\Order\\Action\\OrderWorkflowActionInterface.0.Wizacha\\Marketplace\\Order\\Action\\Cancel' => true,
    '.instanceof.Wizacha\\Marketplace\\Order\\Action\\OrderWorkflowActionInterface.0.Wizacha\\Marketplace\\Order\\Action\\CommitTo' => true,
    '.instanceof.Wizacha\\Marketplace\\Order\\Action\\OrderWorkflowActionInterface.0.Wizacha\\Marketplace\\Order\\Action\\Confirm' => true,
    '.instanceof.Wizacha\\Marketplace\\Order\\Action\\OrderWorkflowActionInterface.0.Wizacha\\Marketplace\\Order\\Action\\DeclareInvoiceNumberGeneratedElsewhere' => true,
    '.instanceof.Wizacha\\Marketplace\\Order\\Action\\OrderWorkflowActionInterface.0.Wizacha\\Marketplace\\Order\\Action\\DeclareParcelLost' => true,
    '.instanceof.Wizacha\\Marketplace\\Order\\Action\\OrderWorkflowActionInterface.0.Wizacha\\Marketplace\\Order\\Action\\DispatchFunds' => true,
    '.instanceof.Wizacha\\Marketplace\\Order\\Action\\OrderWorkflowActionInterface.0.Wizacha\\Marketplace\\Order\\Action\\DispatchFundsFailed' => true,
    '.instanceof.Wizacha\\Marketplace\\Order\\Action\\OrderWorkflowActionInterface.0.Wizacha\\Marketplace\\Order\\Action\\DispatchFundsSucceeded' => true,
    '.instanceof.Wizacha\\Marketplace\\Order\\Action\\OrderWorkflowActionInterface.0.Wizacha\\Marketplace\\Order\\Action\\EndWithdrawalPeriod' => true,
    '.instanceof.Wizacha\\Marketplace\\Order\\Action\\OrderWorkflowActionInterface.0.Wizacha\\Marketplace\\Order\\Action\\MarkAsDelivered' => true,
    '.instanceof.Wizacha\\Marketplace\\Order\\Action\\OrderWorkflowActionInterface.0.Wizacha\\Marketplace\\Order\\Action\\MarkAsFinished' => true,
    '.instanceof.Wizacha\\Marketplace\\Order\\Action\\OrderWorkflowActionInterface.0.Wizacha\\Marketplace\\Order\\Action\\MarkAsPaid' => true,
    '.instanceof.Wizacha\\Marketplace\\Order\\Action\\OrderWorkflowActionInterface.0.Wizacha\\Marketplace\\Order\\Action\\MarkAsShipped' => true,
    '.instanceof.Wizacha\\Marketplace\\Order\\Action\\OrderWorkflowActionInterface.0.Wizacha\\Marketplace\\Order\\Action\\MarkPaymentAsRefused' => true,
    '.instanceof.Wizacha\\Marketplace\\Order\\Action\\OrderWorkflowActionInterface.0.Wizacha\\Marketplace\\Order\\Action\\MarkPaymentAuthorizationAsCaptured' => true,
    '.instanceof.Wizacha\\Marketplace\\Order\\Action\\OrderWorkflowActionInterface.0.Wizacha\\Marketplace\\Order\\Action\\MarkPaymentAuthorizationAsRefused' => true,
    '.instanceof.Wizacha\\Marketplace\\Order\\Action\\OrderWorkflowActionInterface.0.Wizacha\\Marketplace\\Order\\Action\\MarkPaymentDefermentAsAuthorized' => true,
    '.instanceof.Wizacha\\Marketplace\\Order\\Action\\OrderWorkflowActionInterface.0.Wizacha\\Marketplace\\Order\\Action\\MarkPaymentDefermentAsRefused' => true,
    '.instanceof.Wizacha\\Marketplace\\Order\\Action\\OrderWorkflowActionInterface.0.Wizacha\\Marketplace\\Order\\Action\\ProvideInvoiceNumber' => true,
    '.instanceof.Wizacha\\Marketplace\\Order\\Action\\OrderWorkflowActionInterface.0.Wizacha\\Marketplace\\Order\\Action\\RedirectToPaymentProcessor' => true,
    '.instanceof.Wizacha\\Marketplace\\Order\\Action\\OrderWorkflowActionInterface.0.Wizacha\\Marketplace\\Order\\Action\\Refuse' => true,
    '.instanceof.Wizacha\\Marketplace\\Order\\Action\\OrderWorkflowActionInterface.0.Wizacha\\Marketplace\\Order\\Action\\Trash' => true,
    '.instanceof.Wizacha\\Marketplace\\PIM\\Option\\Handler\\SystemOptionsHandlerInterface.0.Wizacha\\Marketplace\\PIM\\Option\\Handler\\CommitmentPeriodHandler' => true,
    '.instanceof.Wizacha\\Marketplace\\PIM\\Option\\Handler\\SystemOptionsHandlerInterface.0.Wizacha\\Marketplace\\PIM\\Option\\Handler\\PaymentFrequencyHandler' => true,
    '.instanceof.Wizacha\\Marketplace\\Payment\\Handler\\CallbackHandlerInterface.0.Wizacha\\Marketplace\\Payment\\Stripe\\Handler\\CustomerSourceCreatedHandler' => true,
    '.legacy_controller_name_converter' => true,
    '.legacy_resolve_controller_name_subscriber' => true,
    '.security.request_matcher..h9STz0' => true,
    '.security.request_matcher..zmjuxL' => true,
    '.security.request_matcher.0BWS06t' => true,
    '.security.request_matcher.1W_1GWY' => true,
    '.security.request_matcher.1mWg1w.' => true,
    '.security.request_matcher.1uNiKxJ' => true,
    '.security.request_matcher.28W8cr3' => true,
    '.security.request_matcher.2u3Zp7d' => true,
    '.security.request_matcher.3eJ2gFY' => true,
    '.security.request_matcher.468zMt0' => true,
    '.security.request_matcher.4j5n4ef' => true,
    '.security.request_matcher.5fVb4iT' => true,
    '.security.request_matcher.6Ro04tr' => true,
    '.security.request_matcher.7_L0eLm' => true,
    '.security.request_matcher.8C0nUd.' => true,
    '.security.request_matcher.98uzVza' => true,
    '.security.request_matcher.BJV23mq' => true,
    '.security.request_matcher.CJ3VXfr' => true,
    '.security.request_matcher.CgixLeT' => true,
    '.security.request_matcher.Dk._hWv' => true,
    '.security.request_matcher.ETjyBYY' => true,
    '.security.request_matcher.FNWA4Kg' => true,
    '.security.request_matcher.FqQj7bu' => true,
    '.security.request_matcher.FxowQ01' => true,
    '.security.request_matcher.G3WWXWG' => true,
    '.security.request_matcher.Gly.XXx' => true,
    '.security.request_matcher.H8dKh3X' => true,
    '.security.request_matcher.ID_W4tK' => true,
    '.security.request_matcher.Ir_PHuV' => true,
    '.security.request_matcher.Iy.T22O' => true,
    '.security.request_matcher.JNw5Mhx' => true,
    '.security.request_matcher.Jqiqxxx' => true,
    '.security.request_matcher.L0rcXot' => true,
    '.security.request_matcher.L_M7RB4' => true,
    '.security.request_matcher.LpkOQTG' => true,
    '.security.request_matcher.NSyCB2H' => true,
    '.security.request_matcher.N_X68oM' => true,
    '.security.request_matcher.OhzDZyt' => true,
    '.security.request_matcher.OjBIoYD' => true,
    '.security.request_matcher.OlcOiR5' => true,
    '.security.request_matcher.PHFiQdM' => true,
    '.security.request_matcher.PdK_71I' => true,
    '.security.request_matcher.Q.WKlc6' => true,
    '.security.request_matcher.S6FZnf8' => true,
    '.security.request_matcher.S8yd3_Q' => true,
    '.security.request_matcher.SFJ4kOQ' => true,
    '.security.request_matcher.SQueMhI' => true,
    '.security.request_matcher.UPpVLI5' => true,
    '.security.request_matcher.UQkPxNy' => true,
    '.security.request_matcher.UYkZNQj' => true,
    '.security.request_matcher.V7UKdxs' => true,
    '.security.request_matcher.VE.228T' => true,
    '.security.request_matcher.VT8C7Xl' => true,
    '.security.request_matcher.VXp64FV' => true,
    '.security.request_matcher.Vo8GTS7' => true,
    '.security.request_matcher.WEy1MnJ' => true,
    '.security.request_matcher.WhCCXXu' => true,
    '.security.request_matcher.X7sepIy' => true,
    '.security.request_matcher.XOaLDlP' => true,
    '.security.request_matcher.Xhb93GB' => true,
    '.security.request_matcher.YCpl1D.' => true,
    '.security.request_matcher.YGxCWVY' => true,
    '.security.request_matcher.YO6BwhR' => true,
    '.security.request_matcher.YU0vWS.' => true,
    '.security.request_matcher.YlMx4i.' => true,
    '.security.request_matcher.Z.iDKUF' => true,
    '.security.request_matcher.ZC8N9Ux' => true,
    '.security.request_matcher.ZsM5Rpt' => true,
    '.security.request_matcher.adr4C4M' => true,
    '.security.request_matcher.bhl7fPn' => true,
    '.security.request_matcher.bm2Q8wl' => true,
    '.security.request_matcher.d37RHSw' => true,
    '.security.request_matcher.eXFzKT_' => true,
    '.security.request_matcher.hE91O35' => true,
    '.security.request_matcher.itR.0T4' => true,
    '.security.request_matcher.j7Bbo6_' => true,
    '.security.request_matcher.jWG8I9V' => true,
    '.security.request_matcher.jagRVUP' => true,
    '.security.request_matcher.kfQE6al' => true,
    '.security.request_matcher.l6Bhy9M' => true,
    '.security.request_matcher.nSEhPQn' => true,
    '.security.request_matcher.nWOHzRz' => true,
    '.security.request_matcher.oM3ReCS' => true,
    '.security.request_matcher.ofDJV1B' => true,
    '.security.request_matcher.qAIMUqn' => true,
    '.security.request_matcher.qk0ND3y' => true,
    '.security.request_matcher.r10pSLE' => true,
    '.security.request_matcher.r76gOHO' => true,
    '.security.request_matcher.sUE.Kz5' => true,
    '.security.request_matcher.uFQelh2' => true,
    '.security.request_matcher.wZEYWh9' => true,
    '.security.request_matcher.wc.D4JJ' => true,
    '.security.request_matcher.xBdoRa7' => true,
    '.security.request_matcher.yLaYOTR' => true,
    '.service_locator.0ZAr7Xn' => true,
    '.service_locator.2zQBAnq' => true,
    '.service_locator.4l_ga.k' => true,
    '.service_locator.5G6k4BF' => true,
    '.service_locator.6zQlgQt' => true,
    '.service_locator.8PnJdCR' => true,
    '.service_locator.CCTmIC0' => true,
    '.service_locator.HVvC9yz' => true,
    '.service_locator.HZJnjFQ' => true,
    '.service_locator.I3K77mT' => true,
    '.service_locator.J3lEO9T' => true,
    '.service_locator.JKGkIcP' => true,
    '.service_locator.JxT1Pp5' => true,
    '.service_locator.JzYhbLD' => true,
    '.service_locator.JzYhbLD.Wizacha\\AppBundle\\Controller\\Api\\BasketController' => true,
    '.service_locator.SPvOdIk' => true,
    '.service_locator.SPvOdIk.router.default' => true,
    '.service_locator.TJZ_Bp7' => true,
    '.service_locator.UgR_Ybd' => true,
    '.service_locator.XSes1R5' => true,
    '.service_locator.XSes1R5.translation.warmer' => true,
    '.service_locator.XjnY8xG' => true,
    '.service_locator.Y7gDuDN' => true,
    '.service_locator._cmka45' => true,
    '.service_locator.aUOCe8D' => true,
    '.service_locator.dJNhnwz' => true,
    '.service_locator.ewr02tJ' => true,
    '.service_locator.f1ancQ8' => true,
    '.service_locator.knFiihF' => true,
    '.service_locator.l__LcCr' => true,
    '.service_locator.m016NJz' => true,
    '.service_locator.m90YCjy' => true,
    '.service_locator.m90YCjy.router.cache_warmer' => true,
    '.service_locator.mx_yHOn' => true,
    '.service_locator.o9nGMXB' => true,
    '.service_locator.o9nGMXB.twig.cache_warmer' => true,
    '.service_locator.o9nGMXB.twig.template_cache_warmer' => true,
    '.service_locator.pEXF19J' => true,
    '.service_locator.tHpW6v3' => true,
    '.service_locator.vdmMuyE' => true,
    '.service_locator.vdmMuyE.Wizacha\\AppBundle\\Controller\\Api\\CompanyPerson\\CompanyPersonController' => true,
    '.service_locator.vdmMuyE.Wizacha\\AppBundle\\Controller\\Api\\GroupController' => true,
    '.service_locator.vdmMuyE.Wizacha\\AppBundle\\Controller\\Api\\Order\\OrderActionController' => true,
    '.service_locator.vdmMuyE.Wizacha\\AppBundle\\Controller\\Api\\TransactionTransferController' => true,
    '.service_locator.vp.ghrP' => true,
    '.service_locator.wnlveDj' => true,
    '.service_locator.x3PiPAY' => true,
    'Broadway\\CommandHandling\\CommandBusInterface' => true,
    'Broadway\\ReadModel\\RepositoryInterface' => true,
    'Cocur\\Slugify\\Slugify' => true,
    'Doctrine\\Bundle\\DoctrineBundle\\Registry' => true,
    'Doctrine\\Common\\Annotations\\Reader' => true,
    'Doctrine\\Common\\Persistence\\ManagerRegistry' => true,
    'Doctrine\\Common\\Persistence\\ObjectManager' => true,
    'Doctrine\\DBAL\\Connection' => true,
    'Doctrine\\DBAL\\Driver\\Connection' => true,
    'Doctrine\\ORM\\EntityManagerInterface' => true,
    'Doctrine\\Persistence\\ManagerRegistry' => true,
    'Exercise\\HTMLPurifierBundle\\HTMLPurifiersRegistryInterface' => true,
    'GuzzleHttp\\Client' => true,
    'HTMLPurifier' => true,
    'Knp\\Snappy\\Pdf' => true,
    'Psr\\Cache\\CacheItemPoolInterface' => true,
    'Psr\\Container\\ContainerInterface' => true,
    'Psr\\Container\\ContainerInterface $parameterBag' => true,
    'Psr\\Log\\LoggerInterface' => true,
    'Psr\\Log\\LoggerInterface $auditLogger' => true,
    'Psr\\Log\\LoggerInterface $cacheLogger' => true,
    'Psr\\Log\\LoggerInterface $consoleLogger' => true,
    'Psr\\Log\\LoggerInterface $doctrineLogger' => true,
    'Psr\\Log\\LoggerInterface $functionalLogger' => true,
    'Psr\\Log\\LoggerInterface $mailerLogger' => true,
    'Psr\\Log\\LoggerInterface $messengerLogger' => true,
    'Psr\\Log\\LoggerInterface $phpLogger' => true,
    'Psr\\Log\\LoggerInterface $pspLogger' => true,
    'Psr\\Log\\LoggerInterface $requestLogger' => true,
    'Psr\\Log\\LoggerInterface $routerLogger' => true,
    'Psr\\Log\\LoggerInterface $securityLogger' => true,
    'Psr\\Log\\LoggerInterface $sentinelLogger' => true,
    'Psr\\Log\\LoggerInterface $snappyLogger' => true,
    'Psr\\Log\\LoggerInterface $sncRedisLogger' => true,
    'Psr\\Log\\LoggerInterface $translationLogger' => true,
    'Psr\\Log\\LoggerInterface $workerLogger' => true,
    'Psr\\SimpleCache\\CacheInterface' => true,
    'SessionHandlerInterface' => true,
    'Swift_Mailer' => true,
    'Swift_Transport' => true,
    'Symfony\\Bridge\\Doctrine\\RegistryInterface' => true,
    'Symfony\\Bundle\\FrameworkBundle\\Templating\\EngineInterface' => true,
    'Symfony\\Component\\Asset\\Packages' => true,
    'Symfony\\Component\\Cache\\Adapter\\AdapterInterface' => true,
    'Symfony\\Component\\Console\\Output\\OutputInterface' => true,
    'Symfony\\Component\\DependencyInjection\\ContainerInterface' => true,
    'Symfony\\Component\\DependencyInjection\\ParameterBag\\ContainerBagInterface' => true,
    'Symfony\\Component\\DependencyInjection\\ParameterBag\\ParameterBagInterface' => true,
    'Symfony\\Component\\DependencyInjection\\ReverseContainer' => true,
    'Symfony\\Component\\EventDispatcher\\EventDispatcherInterface' => true,
    'Symfony\\Component\\Filesystem\\Filesystem' => true,
    'Symfony\\Component\\Form\\FormFactoryInterface' => true,
    'Symfony\\Component\\Form\\FormRegistryInterface' => true,
    'Symfony\\Component\\Form\\ResolvedFormTypeFactoryInterface' => true,
    'Symfony\\Component\\HttpFoundation\\RequestStack' => true,
    'Symfony\\Component\\HttpFoundation\\Session\\Flash\\FlashBagInterface' => true,
    'Symfony\\Component\\HttpFoundation\\Session\\SessionInterface' => true,
    'Symfony\\Component\\HttpFoundation\\Session\\Storage\\SessionStorageInterface' => true,
    'Symfony\\Component\\HttpFoundation\\UrlHelper' => true,
    'Symfony\\Component\\HttpKernel\\Config\\FileLocator' => true,
    'Symfony\\Component\\HttpKernel\\Debug\\FileLinkFormatter' => true,
    'Symfony\\Component\\HttpKernel\\HttpKernelInterface' => true,
    'Symfony\\Component\\HttpKernel\\KernelInterface' => true,
    'Symfony\\Component\\Messenger\\MessageBusInterface' => true,
    'Symfony\\Component\\Messenger\\Transport\\Serialization\\SerializerInterface' => true,
    'Symfony\\Component\\Mime\\MimeTypeGuesserInterface' => true,
    'Symfony\\Component\\Mime\\MimeTypesInterface' => true,
    'Symfony\\Component\\PropertyAccess\\PropertyAccessorInterface' => true,
    'Symfony\\Component\\Routing\\Generator\\UrlGeneratorInterface' => true,
    'Symfony\\Component\\Routing\\Matcher\\UrlMatcherInterface' => true,
    'Symfony\\Component\\Routing\\RequestContext' => true,
    'Symfony\\Component\\Routing\\RequestContextAwareInterface' => true,
    'Symfony\\Component\\Routing\\RouterInterface' => true,
    'Symfony\\Component\\Security\\Core\\Authentication\\AuthenticationManagerInterface' => true,
    'Symfony\\Component\\Security\\Core\\Authentication\\Token\\Storage\\TokenStorageInterface' => true,
    'Symfony\\Component\\Security\\Core\\Authorization\\AccessDecisionManagerInterface' => true,
    'Symfony\\Component\\Security\\Core\\Authorization\\AuthorizationCheckerInterface' => true,
    'Symfony\\Component\\Security\\Core\\Encoder\\EncoderFactoryInterface' => true,
    'Symfony\\Component\\Security\\Core\\Encoder\\UserPasswordEncoderInterface' => true,
    'Symfony\\Component\\Security\\Core\\Role\\RoleHierarchyInterface' => true,
    'Symfony\\Component\\Security\\Core\\Security' => true,
    'Symfony\\Component\\Security\\Core\\User\\UserCheckerInterface' => true,
    'Symfony\\Component\\Security\\Csrf\\CsrfTokenManagerInterface' => true,
    'Symfony\\Component\\Security\\Csrf\\TokenGenerator\\TokenGeneratorInterface' => true,
    'Symfony\\Component\\Security\\Csrf\\TokenStorage\\TokenStorageInterface' => true,
    'Symfony\\Component\\Security\\Guard\\GuardAuthenticatorHandler' => true,
    'Symfony\\Component\\Security\\Http\\Authentication\\AuthenticationUtils' => true,
    'Symfony\\Component\\Security\\Http\\Firewall' => true,
    'Symfony\\Component\\Security\\Http\\HttpUtils' => true,
    'Symfony\\Component\\Security\\Http\\Session\\SessionAuthenticationStrategyInterface' => true,
    'Symfony\\Component\\Serializer\\Encoder\\DecoderInterface' => true,
    'Symfony\\Component\\Serializer\\Encoder\\EncoderInterface' => true,
    'Symfony\\Component\\Serializer\\Mapping\\ClassDiscriminatorResolverInterface' => true,
    'Symfony\\Component\\Serializer\\Mapping\\Factory\\ClassMetadataFactoryInterface' => true,
    'Symfony\\Component\\Serializer\\Normalizer\\DenormalizerInterface' => true,
    'Symfony\\Component\\Serializer\\Normalizer\\NormalizerInterface' => true,
    'Symfony\\Component\\Serializer\\Normalizer\\ObjectNormalizer' => true,
    'Symfony\\Component\\Serializer\\SerializerInterface' => true,
    'Symfony\\Component\\Stopwatch\\Stopwatch' => true,
    'Symfony\\Component\\Templating\\EngineInterface' => true,
    'Symfony\\Component\\Translation\\Extractor\\ExtractorInterface' => true,
    'Symfony\\Component\\Translation\\Reader\\TranslationReaderInterface' => true,
    'Symfony\\Component\\Translation\\TranslatorInterface' => true,
    'Symfony\\Component\\Translation\\Writer\\TranslationWriterInterface' => true,
    'Symfony\\Component\\Validator\\Validator\\ValidatorInterface' => true,
    'Symfony\\Contracts\\Cache\\CacheInterface' => true,
    'Symfony\\Contracts\\Cache\\TagAwareCacheInterface' => true,
    'Symfony\\Contracts\\EventDispatcher\\EventDispatcherInterface' => true,
    'Symfony\\Contracts\\Translation\\TranslatorInterface' => true,
    'Twig\\Environment' => true,
    'Twig_Environment' => true,
    'Wizacha\\ActionLogger\\ActionLoggerFactory' => true,
    'Wizacha\\AppBundle\\Command\\AdminInstallCommand' => true,
    'Wizacha\\AppBundle\\Command\\Algolia\\ClearAlgoliaIndexCommand' => true,
    'Wizacha\\AppBundle\\Command\\Algolia\\CreateAlgoliaIndexCommand' => true,
    'Wizacha\\AppBundle\\Command\\Algolia\\PushAlgoliaConfigCommand' => true,
    'Wizacha\\AppBundle\\Command\\Algolia\\PushAlgoliaProductsCommand' => true,
    'Wizacha\\AppBundle\\Command\\Algolia\\UpdateAlgoliaProductsCommand' => true,
    'Wizacha\\AppBundle\\Command\\AmqpDebouncerBenchmarkCommand' => true,
    'Wizacha\\AppBundle\\Command\\AmqpDebouncerStatsCommand' => true,
    'Wizacha\\AppBundle\\Command\\AmqpDebouncerTriggerCommand' => true,
    'Wizacha\\AppBundle\\Command\\AssetsInstallCommand' => true,
    'Wizacha\\AppBundle\\Command\\AuthLogClearCommand' => true,
    'Wizacha\\AppBundle\\Command\\AuthLogPurgeCommand' => true,
    'Wizacha\\AppBundle\\Command\\BasketsDeleteAllCommand' => true,
    'Wizacha\\AppBundle\\Command\\BenchmarkFixtureCommand' => true,
    'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkAlgoliaCommand' => true,
    'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkDatabaseCommand' => true,
    'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkQueueCommand' => true,
    'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkRedisCommand' => true,
    'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkStorageCommand' => true,
    'Wizacha\\AppBundle\\Command\\CancelEximJobsImport' => true,
    'Wizacha\\AppBundle\\Command\\CatalogJsonExporterCommand' => true,
    'Wizacha\\AppBundle\\Command\\CheckIncompleteOrdersCommand' => true,
    'Wizacha\\AppBundle\\Command\\CheckMangopayUserIdCommand' => true,
    'Wizacha\\AppBundle\\Command\\CheckValidityMarketplaceDiscountCommand' => true,
    'Wizacha\\AppBundle\\Command\\ClearEximJobsCommand' => true,
    'Wizacha\\AppBundle\\Command\\ClearPaymentDataBeforeProductionCommand' => true,
    'Wizacha\\AppBundle\\Command\\ClearTemporaryResourcesCommand' => true,
    'Wizacha\\AppBundle\\Command\\CommissionsCheckConfigCommand' => true,
    'Wizacha\\AppBundle\\Command\\CommissionsRecalculateCommand' => true,
    'Wizacha\\AppBundle\\Command\\ConsumeAmqpMessagesCommand' => true,
    'Wizacha\\AppBundle\\Command\\CreateAdminUserCommand' => true,
    'Wizacha\\AppBundle\\Command\\CreateAmqpQueuesCommand' => true,
    'Wizacha\\AppBundle\\Command\\CreateCompanyWalletCommand' => true,
    'Wizacha\\AppBundle\\Command\\CreateIndexAlgoliaCommand' => true,
    'Wizacha\\AppBundle\\Command\\CreateSimpleUserCommand' => true,
    'Wizacha\\AppBundle\\Command\\CreateUserFromCSVCommand' => true,
    'Wizacha\\AppBundle\\Command\\DebugRegistryCommand' => true,
    'Wizacha\\AppBundle\\Command\\DebugUnserializeCommand' => true,
    'Wizacha\\AppBundle\\Command\\DebugVariablesCommand' => true,
    'Wizacha\\AppBundle\\Command\\DeleteAllMultiVendorProductCommand' => true,
    'Wizacha\\AppBundle\\Command\\DeleteAllOrdersCommand' => true,
    'Wizacha\\AppBundle\\Command\\DeleteOrdersAmountsCommand' => true,
    'Wizacha\\AppBundle\\Command\\DeleteOrdersCommand' => true,
    'Wizacha\\AppBundle\\Command\\DeleteOrganisationsCommand' => true,
    'Wizacha\\AppBundle\\Command\\DeployAlgoliaUpdateCommand' => true,
    'Wizacha\\AppBundle\\Command\\DeployDebugCommand' => true,
    'Wizacha\\AppBundle\\Command\\DeployMigrateDivisionCommand' => true,
    'Wizacha\\AppBundle\\Command\\DeployMigrateOrderTransactionsCommand' => true,
    'Wizacha\\AppBundle\\Command\\DeployMigrateOrderTransactionsIsBackCommand' => true,
    'Wizacha\\AppBundle\\Command\\DeployMigrateRefunds' => true,
    'Wizacha\\AppBundle\\Command\\DeployOnceCompanyEmailSettingsCommand' => true,
    'Wizacha\\AppBundle\\Command\\DeployPostActionsCommand' => true,
    'Wizacha\\AppBundle\\Command\\DeployRemoveAssetsAwsCacheCommand' => true,
    'Wizacha\\AppBundle\\Command\\DeployRunCommand' => true,
    'Wizacha\\AppBundle\\Command\\DeploySetTransactionCompanyIdAndCurrencyCommand' => true,
    'Wizacha\\AppBundle\\Command\\DeploySetTransactionOriginAndDestinationCommand' => true,
    'Wizacha\\AppBundle\\Command\\DeploySetUsersPasswordsHistoryCommand' => true,
    'Wizacha\\AppBundle\\Command\\DeploySystemOptions' => true,
    'Wizacha\\AppBundle\\Command\\DispatchFundsCommand' => true,
    'Wizacha\\AppBundle\\Command\\Division\\ImportDivisionProductsCommand' => true,
    'Wizacha\\AppBundle\\Command\\DropMarketplaceDatabaseCommand' => true,
    'Wizacha\\AppBundle\\Command\\DumpAnonymizerCommand' => true,
    'Wizacha\\AppBundle\\Command\\EndBlockedDispatchedOrdersCommand' => true,
    'Wizacha\\AppBundle\\Command\\EndOrdersWithdrawalPeriodCommand' => true,
    'Wizacha\\AppBundle\\Command\\ExportCatalogCommand' => true,
    'Wizacha\\AppBundle\\Command\\ExportCompanyTokenCommand' => true,
    'Wizacha\\AppBundle\\Command\\FixCorruptedSerializedDataCommand' => true,
    'Wizacha\\AppBundle\\Command\\FixDeclinationInventoryCommand' => true,
    'Wizacha\\AppBundle\\Command\\FixDuplicateVariationsCommand' => true,
    'Wizacha\\AppBundle\\Command\\FixDuplicatedMainImageLinksCommand' => true,
    'Wizacha\\AppBundle\\Command\\FixOrderCommissionSyncCommand' => true,
    'Wizacha\\AppBundle\\Command\\FixOrderDecryptPaymentInformation' => true,
    'Wizacha\\AppBundle\\Command\\FixOrderInvoiceDataCommand' => true,
    'Wizacha\\AppBundle\\Command\\FixOrdersEmailCommand' => true,
    'Wizacha\\AppBundle\\Command\\GenerateSitemapCommand' => true,
    'Wizacha\\AppBundle\\Command\\HadesBackupCommand' => true,
    'Wizacha\\AppBundle\\Command\\HipayCheckKycCommand' => true,
    'Wizacha\\AppBundle\\Command\\ImportMiraklCompanyStatusCommand' => true,
    'Wizacha\\AppBundle\\Command\\ImportThemeTranslationsCommand' => true,
    'Wizacha\\AppBundle\\Command\\Janus\\JanusExportCommand' => true,
    'Wizacha\\AppBundle\\Command\\Janus\\JanusImportCommand' => true,
    'Wizacha\\AppBundle\\Command\\KPIPublishCommand' => true,
    'Wizacha\\AppBundle\\Command\\LemonWayCheckKycCommand' => true,
    'Wizacha\\AppBundle\\Command\\LemonwaySendKycCommand' => true,
    'Wizacha\\AppBundle\\Command\\LemonwayTransactionSyncCommand' => true,
    'Wizacha\\AppBundle\\Command\\ListAllFeaturesCommand' => true,
    'Wizacha\\AppBundle\\Command\\ListAmqpQueuesCommand' => true,
    'Wizacha\\AppBundle\\Command\\ListEximImportMessages' => true,
    'Wizacha\\AppBundle\\Command\\ListEximJobsImport' => true,
    'Wizacha\\AppBundle\\Command\\LoadFixtureCommand' => true,
    'Wizacha\\AppBundle\\Command\\MagentoPasswordHashUpdateCommand' => true,
    'Wizacha\\AppBundle\\Command\\MangoPayCheckKycCommand' => true,
    'Wizacha\\AppBundle\\Command\\MangoPaySendKycCommand' => true,
    'Wizacha\\AppBundle\\Command\\MarkOrdersAsDeliveredCommand' => true,
    'Wizacha\\AppBundle\\Command\\MigrateDiscussCommand' => true,
    'Wizacha\\AppBundle\\Command\\MiraklCreateCompanyAdministrators' => true,
    'Wizacha\\AppBundle\\Command\\MiraklOrderForceCompletedCommand' => true,
    'Wizacha\\AppBundle\\Command\\MiraklOrderForceFromProcessingToProcessedCommand' => true,
    'Wizacha\\AppBundle\\Command\\Moderation\\PurgeProductInProgressCommand' => true,
    'Wizacha\\AppBundle\\Command\\OrderAmountsRecalculateCommand' => true,
    'Wizacha\\AppBundle\\Command\\OrderCheckAcceptationDelayCommand' => true,
    'Wizacha\\AppBundle\\Command\\OrderCompletedStatsCommand' => true,
    'Wizacha\\AppBundle\\Command\\OrderCreationStatsCommand' => true,
    'Wizacha\\AppBundle\\Command\\OrderExecuteActionCommand' => true,
    'Wizacha\\AppBundle\\Command\\OrderListActionsCommand' => true,
    'Wizacha\\AppBundle\\Command\\OrderRefundOfflineMarkAsPaidCommand' => true,
    'Wizacha\\AppBundle\\Command\\OrdersResetDispatchFundsCommand' => true,
    'Wizacha\\AppBundle\\Command\\PayPaymentDefermentOrdersCommand' => true,
    'Wizacha\\AppBundle\\Command\\PayoutCompaniesCommand' => true,
    'Wizacha\\AppBundle\\Command\\PrediggoExportCommand' => true,
    'Wizacha\\AppBundle\\Command\\PriceTiers\\CleanAndRealignBasicPriceTiersCommand' => true,
    'Wizacha\\AppBundle\\Command\\PriceTiers\\DeleteComplexPriceTiersCommand' => true,
    'Wizacha\\AppBundle\\Command\\ProductRefreshCacheCommand' => true,
    'Wizacha\\AppBundle\\Command\\ProductVisibilityCommand' => true,
    'Wizacha\\AppBundle\\Command\\PspCheckKycCommand' => true,
    'Wizacha\\AppBundle\\Command\\PspSendKycCommand' => true,
    'Wizacha\\AppBundle\\Command\\PurgeResourcesCommand' => true,
    'Wizacha\\AppBundle\\Command\\PushVendorInfoToPspCommand' => true,
    'Wizacha\\AppBundle\\Command\\Readmodel\\CleanReadmodelCommand' => true,
    'Wizacha\\AppBundle\\Command\\Readmodel\\ClearReadmodelCommand' => true,
    'Wizacha\\AppBundle\\Command\\Readmodel\\CreateReadmodelCommand' => true,
    'Wizacha\\AppBundle\\Command\\Readmodel\\DumpReadmodelCommand' => true,
    'Wizacha\\AppBundle\\Command\\Readmodel\\UpdateReadmodelCommand' => true,
    'Wizacha\\AppBundle\\Command\\RecountProductInCategoriesCommand' => true,
    'Wizacha\\AppBundle\\Command\\RefreshNewPromotionsInSearchCommand' => true,
    'Wizacha\\AppBundle\\Command\\RemovePromotionsOnIrrelevantOrdersCommand' => true,
    'Wizacha\\AppBundle\\Command\\RenameProductAttachmentCommand' => true,
    'Wizacha\\AppBundle\\Command\\RenewSubscriptionCommand' => true,
    'Wizacha\\AppBundle\\Command\\ResetProgressForProductInModeration' => true,
    'Wizacha\\AppBundle\\Command\\RgpdCompanyAnonymizerCommand' => true,
    'Wizacha\\AppBundle\\Command\\RgpdUserAnonymizerCommand' => true,
    'Wizacha\\AppBundle\\Command\\RollbackProcessedOrdersCommand' => true,
    'Wizacha\\AppBundle\\Command\\SMoneyCheckKycCommand' => true,
    'Wizacha\\AppBundle\\Command\\SMoneySendKycCommand' => true,
    'Wizacha\\AppBundle\\Command\\SeoRefreshNamesCommand' => true,
    'Wizacha\\AppBundle\\Command\\StripeRemoveConsumedMandatesCommand' => true,
    'Wizacha\\AppBundle\\Command\\TranslationCacheRefreshCommand' => true,
    'Wizacha\\AppBundle\\Command\\TranslationMissingKeysCommand' => true,
    'Wizacha\\AppBundle\\Command\\TrashAbandonedOrdersCommand' => true,
    'Wizacha\\AppBundle\\Command\\UpdateCurrencyRatesCommand' => true,
    'Wizacha\\AppBundle\\Command\\UpdateOrdersToCompletedCommand' => true,
    'Wizacha\\AppBundle\\Command\\UpdateTranslationFromCsvCommand' => true,
    'Wizacha\\AppBundle\\Command\\UpdateTranslationOrderStatusCommand' => true,
    'Wizacha\\AppBundle\\Command\\UserAccessToken' => true,
    'Wizacha\\AppBundle\\Command\\UserUpdateCommand' => true,
    'Wizacha\\AppBundle\\Command\\WorkflowToGraphvizCommand' => true,
    'Wizacha\\AppBundle\\Command\\Yavin\\UploadUsersToAuthGateway' => true,
    'Wizacha\\AppBundle\\Controller\\Api\\AttributeController' => true,
    'Wizacha\\AppBundle\\Controller\\Api\\ModerationController' => true,
    'Wizacha\\AppBundle\\Controller\\Api\\PingController' => true,
    'Wizacha\\AppBundle\\Controller\\Api\\Promotion\\BasketPromotionNormalizer' => true,
    'Wizacha\\AppBundle\\Controller\\Api\\Promotion\\BasketPromotionValidator' => true,
    'Wizacha\\AppBundle\\Controller\\Api\\Promotion\\CatalogPromotionNormalizer' => true,
    'Wizacha\\AppBundle\\Controller\\Api\\Promotion\\MarketplacePromotionValidator' => true,
    'Wizacha\\AppBundle\\Controller\\Api\\Promotion\\RulesUserGroupValidator' => true,
    'Wizacha\\AppBundle\\Controller\\Backend\\StripeController' => true,
    'Wizacha\\AppBundle\\EventSubscriber\\ApiApplicationFirewall' => true,
    'Wizacha\\AppBundle\\EventSubscriber\\PspRequestSubscriber' => true,
    'Wizacha\\AppBundle\\EventSubscriber\\Yavin\\User\\UserActivatedSubscriber' => true,
    'Wizacha\\AppBundle\\EventSubscriber\\Yavin\\User\\UserCompanyChangedSubscriber' => true,
    'Wizacha\\AppBundle\\EventSubscriber\\Yavin\\User\\UserCreatedSubscriber' => true,
    'Wizacha\\AppBundle\\EventSubscriber\\Yavin\\User\\UserDeactivatedSubscriber' => true,
    'Wizacha\\AppBundle\\EventSubscriber\\Yavin\\User\\UserTypeChangedSubscriber' => true,
    'Wizacha\\AppBundle\\EventSubscriber\\Yavin\\User\\UserUpdatedSubscriber' => true,
    'Wizacha\\AppBundle\\Security\\Listener\\UserAuthenticatedListener' => true,
    'Wizacha\\AppBundle\\Security\\Listener\\UserAuthenticationFailureListener' => true,
    'Wizacha\\AppBundle\\Service\\WhitelistDomainsService' => true,
    'Wizacha\\Async\\AbstractAsyncQueue' => true,
    'Wizacha\\Component\\BytesGenerator\\RandomBytesGenerator' => true,
    'Wizacha\\Component\\Divisions\\XmlImporter' => true,
    'Wizacha\\Component\\Html\\HtmlService' => true,
    'Wizacha\\Component\\Http\\Factory\\ProxyRequestFactory' => true,
    'Wizacha\\Component\\Import\\EximJobService' => true,
    'Wizacha\\Component\\Language\\LanguageNormalizer' => true,
    'Wizacha\\Component\\Log\\CommandLogService' => true,
    'Wizacha\\Component\\MondialRelay\\Client' => true,
    'Wizacha\\Component\\PdfGenerator\\PdfGenerator' => true,
    'Wizacha\\Component\\PurgeResources\\PurgeResourceFactory' => true,
    'Wizacha\\Component\\PurgeResources\\Report' => true,
    'Wizacha\\Component\\PurgeResources\\Types\\AbstractPurgeResource' => true,
    'Wizacha\\Component\\PurgeResources\\Types\\PurgeImages' => true,
    'Wizacha\\Component\\Token\\NumericTokenGenerator' => true,
    'Wizacha\\Component\\Token\\TokenGeneratorInterface' => true,
    'Wizacha\\Cscart\\FnFunctions' => true,
    'Wizacha\\DumpAnonymizer\\Services\\OutputS3Manager' => true,
    'Wizacha\\Exim\\CompanyTokenCsv' => true,
    'Wizacha\\Exim\\Import\\AutomatedFeedsService' => true,
    'Wizacha\\Janus\\JanusRepository' => true,
    'Wizacha\\Janus\\JanusService' => true,
    'Wizacha\\Marketplace\\Catalog\\Company\\CompanyService' => true,
    'Wizacha\\Marketplace\\Catalog\\ExporterService' => true,
    'Wizacha\\Marketplace\\Commission\\TransactionListener' => true,
    'Wizacha\\Marketplace\\CompanyPerson\\CompanyPersonRepository' => true,
    'Wizacha\\Marketplace\\Country\\CountryRepository' => true,
    'Wizacha\\Marketplace\\Currency\\CurrencyCountriesRepository' => true,
    'Wizacha\\Marketplace\\Currency\\CurrencyRepository' => true,
    'Wizacha\\Marketplace\\Currency\\FixerClientApi' => true,
    'Wizacha\\Marketplace\\Currency\\QueryFilters\\CountryCode' => true,
    'Wizacha\\Marketplace\\Currency\\QueryFilters\\Enabled' => true,
    'Wizacha\\Marketplace\\Currency\\QueryFilters\\Factory' => true,
    'Wizacha\\Marketplace\\Date\\DateService' => true,
    'Wizacha\\Marketplace\\Division\\CompanyDivisionSettings' => true,
    'Wizacha\\Marketplace\\Division\\DivisionSet' => true,
    'Wizacha\\Marketplace\\Division\\MarketplaceDivisionSettings' => true,
    'Wizacha\\Marketplace\\Division\\ProductDivisionSettings' => true,
    'Wizacha\\Marketplace\\Division\\Repository\\DivisionRepository' => true,
    'Wizacha\\Marketplace\\Division\\Service\\DivisionBlacklistsService' => true,
    'Wizacha\\Marketplace\\Division\\Service\\DivisionProductsService' => true,
    'Wizacha\\Marketplace\\Group\\GroupRepository' => true,
    'Wizacha\\Marketplace\\InvoicingSettings\\InvoicingSettingsRepository' => true,
    'Wizacha\\Marketplace\\InvoicingSettings\\InvoicingSettingsService' => true,
    'Wizacha\\Marketplace\\KPI\\KPIPublishService' => true,
    'Wizacha\\Marketplace\\MessageAttachment\\MessageAttachmentService' => true,
    'Wizacha\\Marketplace\\MessageAttachment\\Repository\\MessageAttachmentRepository' => true,
    'Wizacha\\Marketplace\\Order\\Adjustment\\OrderAdjustmentRepository' => true,
    'Wizacha\\Marketplace\\Order\\Adjustment\\OrderAdjustmentService' => true,
    'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Repository\\OrderAmountsRepository' => true,
    'Wizacha\\Marketplace\\Order\\CheckIncompleteOrdersService' => true,
    'Wizacha\\Marketplace\\Order\\CreditNote\\CreditNoteHelper' => true,
    'Wizacha\\Marketplace\\Order\\CreditNote\\CreditNoteService' => true,
    'Wizacha\\Marketplace\\Order\\DirectDebitService' => true,
    'Wizacha\\Marketplace\\Order\\OrderAttachment\\Repository\\OrderAttachmentRepository' => true,
    'Wizacha\\Marketplace\\Order\\OrderAttachment\\Service\\UploadFileService' => true,
    'Wizacha\\Marketplace\\Order\\OrderAttachment\\Service\\Validator\\OrderAttachmentTypeValidatorService' => true,
    'Wizacha\\Marketplace\\Order\\OrderAttachment\\Service\\Validator\\OrderAttachmentValidatorService' => true,
    'Wizacha\\Marketplace\\Order\\OrderDetailsService' => true,
    'Wizacha\\Marketplace\\Order\\OrderListener' => true,
    'Wizacha\\Marketplace\\Order\\OrderReturn\\OrderReturnService' => true,
    'Wizacha\\Marketplace\\Order\\Refund\\Checker\\MarketplaceDiscountRefundChecker' => true,
    'Wizacha\\Marketplace\\Order\\Refund\\Checker\\RefundChecker' => true,
    'Wizacha\\Marketplace\\Order\\Refund\\Repository\\RefundRepository' => true,
    'Wizacha\\Marketplace\\Order\\Refund\\Service\\RefundNotificationService' => true,
    'Wizacha\\Marketplace\\Order\\Refund\\Service\\RefundService' => true,
    'Wizacha\\Marketplace\\Order\\Refund\\Utils\\RefundAmountsCalculator' => true,
    'Wizacha\\Marketplace\\Order\\Refund\\Utils\\RefundConfig' => true,
    'Wizacha\\Marketplace\\Order\\Refund\\Utils\\RefundCreator' => true,
    'Wizacha\\Marketplace\\Order\\Refund\\Utils\\RefundExecutor' => true,
    'Wizacha\\Marketplace\\Order\\Refund\\Utils\\RefundPayment' => true,
    'Wizacha\\Marketplace\\Order\\Repository\\OrderRepository' => true,
    'Wizacha\\Marketplace\\Order\\Token\\TokenRepository' => true,
    'Wizacha\\Marketplace\\Order\\Tracer\\Tracer' => true,
    'Wizacha\\Marketplace\\PIM\\MultiVendorProduct\\ProductSynchronization\\RulesService' => true,
    'Wizacha\\Marketplace\\PIM\\Option\\Handler\\CommitmentPeriodHandler' => true,
    'Wizacha\\Marketplace\\PIM\\Option\\Handler\\PaymentFrequencyHandler' => true,
    'Wizacha\\Marketplace\\PIM\\Product\\ProductVisibilityReportService' => true,
    'Wizacha\\Marketplace\\PIM\\Product\\Template\\ProductInfoFormType' => true,
    'Wizacha\\Marketplace\\PSP\\UboMangopay\\UboMangopayRepository' => true,
    'Wizacha\\Marketplace\\PSP\\UboMangopay\\UboMangopayService' => true,
    'Wizacha\\Marketplace\\Payment\\AbstractPaymentMethod' => true,
    'Wizacha\\Marketplace\\Payment\\HiPay\\AbstractHipayPaymentMethod' => true,
    'Wizacha\\Marketplace\\Payment\\LemonWay\\AbstractLemonWayPaymentMethod' => true,
    'Wizacha\\Marketplace\\Payment\\LemonWay\\LemonWayLogger' => true,
    'Wizacha\\Marketplace\\Payment\\PaymentRepository' => true,
    'Wizacha\\Marketplace\\Payment\\PaymentService' => true,
    'Wizacha\\Marketplace\\Payment\\Processor\\PaymentProcessorProvider' => true,
    'Wizacha\\Marketplace\\Payment\\Processor\\ProcessorCallbackRegistry' => true,
    'Wizacha\\Marketplace\\Payment\\Processor\\ProcessorService' => true,
    'Wizacha\\Marketplace\\Payment\\Repository\\UserMandateRepository' => true,
    'Wizacha\\Marketplace\\Payment\\Repository\\UserPaymentInfoRepository' => true,
    'Wizacha\\Marketplace\\Payment\\Stripe\\Handler\\CustomerSourceCreatedHandler' => true,
    'Wizacha\\Marketplace\\Payment\\Stripe\\StripeHTTPClient' => true,
    'Wizacha\\Marketplace\\Payment\\Stripe\\StripeLogger' => true,
    'Wizacha\\Marketplace\\Payment\\UserPaymentInfoService' => true,
    'Wizacha\\Marketplace\\Price\\DetailedPriceFormatter' => true,
    'Wizacha\\Marketplace\\ProductOptionInventory\\Service\\ProductOptionInventoryService' => true,
    'Wizacha\\Marketplace\\Promotion\\PromotionUsage\\PromotionUsageRepository' => true,
    'Wizacha\\Marketplace\\ReadModel\\Denormalizer\\ReadModelDenormalizerRegistry' => true,
    'Wizacha\\Marketplace\\ReadModel\\Denormalizer\\ReadModelDenormalizerV1' => true,
    'Wizacha\\Marketplace\\RgpdAnonymizer\\Report\\CompanyReport' => true,
    'Wizacha\\Marketplace\\RgpdAnonymizer\\Report\\UserReport' => true,
    'Wizacha\\Marketplace\\RgpdAnonymizer\\RgpdAnonymizerFactory' => true,
    'Wizacha\\Marketplace\\RgpdAnonymizer\\RgpdCompanyAnonymizer' => true,
    'Wizacha\\Marketplace\\RgpdAnonymizer\\RgpdDetachUserFromCompany' => true,
    'Wizacha\\Marketplace\\RgpdAnonymizer\\RgpdUserAnonymizer' => true,
    'Wizacha\\Marketplace\\Tax\\InternationalTaxService' => true,
    'Wizacha\\Marketplace\\Tax\\Repository\\ShippingTaxRepository' => true,
    'Wizacha\\Marketplace\\Tax\\Repository\\TaxRepository' => true,
    'Wizacha\\Marketplace\\Transaction\\Form\\CommissionTransactionType' => true,
    'Wizacha\\Marketplace\\User\\AddressBookService' => true,
    'Wizacha\\Marketplace\\User\\EKeyRepository' => true,
    'Wizacha\\Marketplace\\User\\UserPasswordHistoryRepository' => true,
    'Wizacha\\Marketplace\\Webhooks\\Service\\WebhooksService' => true,
    'Wizacha\\ProductManager' => true,
    'Wizacha\\Search\\Engine\\Algolia' => true,
    'Wizacha\\Search\\Index\\AbstractIndex' => true,
    'Wizacha\\Storage\\Adapter\\Aws' => true,
    'Wizacha\\Storage\\Adapter\\Azure' => true,
    'Wizacha\\Storage\\Adapter\\Local' => true,
    'Wizacha\\Storage\\Adapter\\Ovh' => true,
    'Wizacha\\Storage\\StorageFactory' => true,
    'annotation_reader' => true,
    'annotations.cache' => true,
    'annotations.cache_adapter' => true,
    'annotations.cache_warmer' => true,
    'annotations.cached_reader' => true,
    'annotations.dummy_registry' => true,
    'annotations.filesystem_cache' => true,
    'annotations.filesystem_cache_adapter' => true,
    'annotations.reader' => true,
    'app.auth_redirect' => true,
    'app.bfo_service' => true,
    'app.env_var_processor' => true,
    'app.exception_subscriber' => true,
    'app.feature_flag.listener' => true,
    'app.file_system' => true,
    'app.json_decoder_request_subscriber' => true,
    'app.listener.language' => true,
    'app.locale.detector.api' => true,
    'app.locale.detector.api_only' => true,
    'app.locale.detector.content' => true,
    'app.locale.detector.content.allowed' => true,
    'app.locale.detector.content.front' => true,
    'app.locale.detector.content.in_query' => true,
    'app.locale.detector.content.in_session' => true,
    'app.locale.detector.in_header' => true,
    'app.locale.detector.interface' => true,
    'app.locale.detector.interface.allowed' => true,
    'app.locale.detector.interface.front' => true,
    'app.locale.detector.interface.in_query' => true,
    'app.locale.detector.interface.in_session' => true,
    'app.locale.detector.interface.user' => true,
    'app.permissions_subscriber' => true,
    'app.redis.cscart_sessions' => true,
    'app.redis.default' => true,
    'app.redis.handlers' => true,
    'app.redis.locks' => true,
    'app.session_backend' => true,
    'app.session_handler' => true,
    'argument_metadata_factory' => true,
    'argument_resolver' => true,
    'argument_resolver.controller_locator' => true,
    'argument_resolver.default' => true,
    'argument_resolver.request' => true,
    'argument_resolver.request_attribute' => true,
    'argument_resolver.service' => true,
    'argument_resolver.session' => true,
    'argument_resolver.variadic' => true,
    'assets._default_package' => true,
    'assets._version__default' => true,
    'assets.context' => true,
    'assets.empty_package' => true,
    'assets.empty_version_strategy' => true,
    'assets.json_manifest_version_strategy' => true,
    'assets.path_package' => true,
    'assets.static_version_strategy' => true,
    'assets.url_package' => true,
    'aws.client' => true,
    'broadway.aggregate_factory' => true,
    'broadway.auditing.command_logger' => true,
    'broadway.auditing.logger' => true,
    'broadway.auditing.serializer' => true,
    'broadway.command_handling.event_dispatching_command_bus' => true,
    'broadway.command_handling.simple_command_bus' => true,
    'broadway.elasticsearch.client' => true,
    'broadway.elasticsearch.client_factory' => true,
    'broadway.event_dispatcher' => true,
    'broadway.event_handling.event_bus' => true,
    'broadway.event_store' => true,
    'broadway.event_store.dbal' => true,
    'broadway.event_store.dbal.connection' => true,
    'broadway.event_store.in_memory' => true,
    'broadway.metadata_enricher.console' => true,
    'broadway.metadata_enriching_event_stream_decorator' => true,
    'broadway.read_model.elasticsearch.repository_factory' => true,
    'broadway.read_model.repository_factory' => true,
    'broadway.saga.metadata.factory' => true,
    'broadway.saga.metadata_enricher' => true,
    'broadway.saga.multiple_saga_manager' => true,
    'broadway.saga.state.mongodb_collection' => true,
    'broadway.saga.state.mongodb_connection' => true,
    'broadway.saga.state.mongodb_database' => true,
    'broadway.saga.state.mongodb_repository' => true,
    'broadway.saga.state.repository' => true,
    'broadway.saga.state.state_manager' => true,
    'broadway.serializer.metadata' => true,
    'broadway.serializer.payload' => true,
    'broadway.serializer.readmodel' => true,
    'broadway.simple_interface_serializer' => true,
    'broadway_serialization.hydrate' => true,
    'broadway_serialization.instantiator' => true,
    'cache.adapter.apcu' => true,
    'cache.adapter.array' => true,
    'cache.adapter.doctrine' => true,
    'cache.adapter.filesystem' => true,
    'cache.adapter.memcached' => true,
    'cache.adapter.pdo' => true,
    'cache.adapter.psr6' => true,
    'cache.adapter.redis' => true,
    'cache.adapter.system' => true,
    'cache.annotations' => true,
    'cache.app.simple' => true,
    'cache.app.taggable' => true,
    'cache.default_clearer' => true,
    'cache.default_marshaller' => true,
    'cache.default_memcached_provider' => true,
    'cache.default_pdo_provider' => true,
    'cache.default_redis_provider' => true,
    'cache.doctrine.orm.default.metadata' => true,
    'cache.doctrine.orm.default.query' => true,
    'cache.doctrine.orm.default.result' => true,
    'cache.messenger.restart_workers_signal' => true,
    'cache.property_access' => true,
    'cache.property_info' => true,
    'cache.security_expression_language' => true,
    'cache.serializer' => true,
    'cache.validator' => true,
    'config.resource.self_checking_resource_checker' => true,
    'config_cache_factory' => true,
    'console.command.about' => true,
    'console.command.assets_install' => true,
    'console.command.cache_clear' => true,
    'console.command.cache_pool_clear' => true,
    'console.command.cache_pool_delete' => true,
    'console.command.cache_pool_list' => true,
    'console.command.cache_pool_prune' => true,
    'console.command.cache_warmup' => true,
    'console.command.config_debug' => true,
    'console.command.config_dump_reference' => true,
    'console.command.container_debug' => true,
    'console.command.container_lint' => true,
    'console.command.debug_autowiring' => true,
    'console.command.event_dispatcher_debug' => true,
    'console.command.form_debug' => true,
    'console.command.messenger_consume_messages' => true,
    'console.command.messenger_debug' => true,
    'console.command.messenger_setup_transports' => true,
    'console.command.messenger_stop_workers' => true,
    'console.command.router_debug' => true,
    'console.command.router_match' => true,
    'console.command.secrets_decrypt_to_local' => true,
    'console.command.secrets_encrypt_from_local' => true,
    'console.command.secrets_generate_key' => true,
    'console.command.secrets_list' => true,
    'console.command.secrets_remove' => true,
    'console.command.secrets_set' => true,
    'console.command.translation_debug' => true,
    'console.command.translation_update' => true,
    'console.command.xliff_lint' => true,
    'console.command.yaml_lint' => true,
    'console.error_listener' => true,
    'console.suggest_missing_package_subscriber' => true,
    'container.env_var_processor' => true,
    'controller_name_converter' => true,
    'controller_resolver' => true,
    'crypto' => true,
    'data_collector.doctrine' => true,
    'data_collector.security' => true,
    'data_collector.twig' => true,
    'debug.debug_handlers_listener' => true,
    'debug.file_link_formatter' => true,
    'debug.stopwatch' => true,
    'decorating_translator' => true,
    'decorating_translator.inner' => true,
    'dependency_injection.config.container_parameters_resource_checker' => true,
    'doctrine.cache_clear_metadata_command' => true,
    'doctrine.cache_clear_query_cache_command' => true,
    'doctrine.cache_clear_result_command' => true,
    'doctrine.cache_collection_region_command' => true,
    'doctrine.clear_entity_region_command' => true,
    'doctrine.clear_query_region_command' => true,
    'doctrine.database_create_command' => true,
    'doctrine.database_drop_command' => true,
    'doctrine.database_import_command' => true,
    'doctrine.dbal.connection' => true,
    'doctrine.dbal.connection.configuration' => true,
    'doctrine.dbal.connection.event_manager' => true,
    'doctrine.dbal.connection_factory' => true,
    'doctrine.dbal.default_connection.configuration' => true,
    'doctrine.dbal.default_connection.event_manager' => true,
    'doctrine.dbal.default_regex_schema_filter' => true,
    'doctrine.dbal.default_schema_asset_filter_manager' => true,
    'doctrine.dbal.event_manager' => true,
    'doctrine.dbal.logger' => true,
    'doctrine.dbal.logger.backtrace' => true,
    'doctrine.dbal.logger.chain' => true,
    'doctrine.dbal.logger.profiling' => true,
    'doctrine.dbal.schema_asset_filter_manager' => true,
    'doctrine.dbal.well_known_schema_asset_filter' => true,
    'doctrine.ensure_production_settings_command' => true,
    'doctrine.generate_entities_command' => true,
    'doctrine.mapping_convert_command' => true,
    'doctrine.mapping_import_command' => true,
    'doctrine.mapping_info_command' => true,
    'doctrine.orm.cache.provider.cache.doctrine.orm.default.metadata' => true,
    'doctrine.orm.cache.provider.cache.doctrine.orm.default.query' => true,
    'doctrine.orm.cache.provider.cache.doctrine.orm.default.result' => true,
    'doctrine.orm.configuration' => true,
    'doctrine.orm.container_repository_factory' => true,
    'doctrine.orm.default_annotation_metadata_driver' => true,
    'doctrine.orm.default_configuration' => true,
    'doctrine.orm.default_entity_listener_resolver' => true,
    'doctrine.orm.default_entity_manager.event_manager' => true,
    'doctrine.orm.default_entity_manager.property_info_extractor' => true,
    'doctrine.orm.default_entity_manager.validator_loader' => true,
    'doctrine.orm.default_listeners.attach_entity_listeners' => true,
    'doctrine.orm.default_manager_configurator' => true,
    'doctrine.orm.default_metadata_cache' => true,
    'doctrine.orm.default_metadata_driver' => true,
    'doctrine.orm.default_query_cache' => true,
    'doctrine.orm.default_result_cache' => true,
    'doctrine.orm.default_yml_metadata_driver' => true,
    'doctrine.orm.entity_manager.abstract' => true,
    'doctrine.orm.listeners.resolve_target_entity' => true,
    'doctrine.orm.manager_configurator.abstract' => true,
    'doctrine.orm.messenger.event_subscriber.doctrine_clear_entity_manager' => true,
    'doctrine.orm.metadata.annotation_reader' => true,
    'doctrine.orm.naming_strategy.default' => true,
    'doctrine.orm.naming_strategy.underscore' => true,
    'doctrine.orm.naming_strategy.underscore_number_aware' => true,
    'doctrine.orm.proxy_cache_warmer' => true,
    'doctrine.orm.quote_strategy.ansi' => true,
    'doctrine.orm.quote_strategy.default' => true,
    'doctrine.orm.security.user.provider' => true,
    'doctrine.orm.validator.unique' => true,
    'doctrine.orm.validator_initializer' => true,
    'doctrine.query_dql_command' => true,
    'doctrine.query_sql_command' => true,
    'doctrine.schema_create_command' => true,
    'doctrine.schema_drop_command' => true,
    'doctrine.schema_update_command' => true,
    'doctrine.schema_validate_command' => true,
    'doctrine.twig.doctrine_extension' => true,
    'doctrine_cache.abstract.apc' => true,
    'doctrine_cache.abstract.apcu' => true,
    'doctrine_cache.abstract.array' => true,
    'doctrine_cache.abstract.chain' => true,
    'doctrine_cache.abstract.couchbase' => true,
    'doctrine_cache.abstract.file_system' => true,
    'doctrine_cache.abstract.memcache' => true,
    'doctrine_cache.abstract.memcached' => true,
    'doctrine_cache.abstract.mongodb' => true,
    'doctrine_cache.abstract.php_file' => true,
    'doctrine_cache.abstract.predis' => true,
    'doctrine_cache.abstract.redis' => true,
    'doctrine_cache.abstract.riak' => true,
    'doctrine_cache.abstract.sqlite3' => true,
    'doctrine_cache.abstract.void' => true,
    'doctrine_cache.abstract.wincache' => true,
    'doctrine_cache.abstract.xcache' => true,
    'doctrine_cache.abstract.zenddata' => true,
    'doctrine_cache.contains_command' => true,
    'doctrine_cache.delete_command' => true,
    'doctrine_cache.flush_command' => true,
    'doctrine_cache.stats_command' => true,
    'dump_anonymizer.output.aws.client' => true,
    'error_handler.error_renderer.html' => true,
    'error_handler.error_renderer.serializer' => true,
    'error_renderer' => true,
    'error_renderer.html' => true,
    'error_renderer.serializer' => true,
    'event.subscriber.premoderation' => true,
    'exception_listener' => true,
    'exercise_html_purifier.cache_warmer.serializer' => true,
    'exercise_html_purifier.config.default' => true,
    'exercise_html_purifier.default' => true,
    'exercise_html_purifier.form.text_type_extension' => true,
    'exercise_html_purifier.purifiers_registry' => true,
    'exercise_html_purifier.twig.extension' => true,
    'exercise_html_purifier.twig.runtime' => true,
    'file_locator' => true,
    'form.choice_list_factory' => true,
    'form.choice_list_factory.cached' => true,
    'form.choice_list_factory.default' => true,
    'form.choice_list_factory.property_access' => true,
    'form.extension' => true,
    'form.feature' => true,
    'form.login' => true,
    'form.property_accessor' => true,
    'form.registry' => true,
    'form.resolved_type_factory' => true,
    'form.server_params' => true,
    'form.type.choice' => true,
    'form.type.entity' => true,
    'form.type.form' => true,
    'form.type_extension.csrf' => true,
    'form.type_extension.form.http_foundation' => true,
    'form.type_extension.form.request_handler' => true,
    'form.type_extension.form.transformation_failure_handling' => true,
    'form.type_extension.form.validator' => true,
    'form.type_extension.repeated.validator' => true,
    'form.type_extension.submit.validator' => true,
    'form.type_extension.upload.validator' => true,
    'form.type_guesser.doctrine' => true,
    'form.type_guesser.validator' => true,
    'fos_js_routing.denormalizer.route_collection' => true,
    'fos_js_routing.dump_command' => true,
    'fos_js_routing.encoder' => true,
    'fos_js_routing.normalizer.route_collection' => true,
    'fos_js_routing.normalizer.routes_response' => true,
    'fos_js_routing.router' => true,
    'fos_js_routing.router_debug_exposed_command' => true,
    'fragment.renderer.inline' => true,
    'gedmo.listener.tree' => true,
    'hades.aws.client' => true,
    'identity_translator' => true,
    'locale_aware_listener' => true,
    'locale_listener' => true,
    'marketplace.api_basic_authentication_entry_point' => true,
    'marketplace.api_user_provider' => true,
    'marketplace.apikey_authenticator' => true,
    'marketplace.asset_theme_manager' => true,
    'marketplace.aws.transcoding_bucket' => true,
    'marketplace.aws.transcoding_service' => true,
    'marketplace.backend_PricesImporter' => true,
    'marketplace.backend_QuantitiesImporter' => true,
    'marketplace.cscart_password_encoder' => true,
    'marketplace.doctrine_table_prefixer' => true,
    'marketplace.oauth.provider.azure' => true,
    'marketplace.oauth.provider.azure.admin' => true,
    'marketplace.order.shipment_service' => true,
    'marketplace.payment.hipay_data_anonymizer' => true,
    'marketplace.payment.hipay_logger' => true,
    'marketplace.payment.hipay_wallet_logger' => true,
    'marketplace.payment.lemonway_data_anonymizer' => true,
    'marketplace.payment.logger' => true,
    'marketplace.payment.mangopay_data_anonymizer' => true,
    'marketplace.payment.mangopay_logger' => true,
    'marketplace.payment.stripe_data_anonymizer' => true,
    'marketplace.theme_customizer' => true,
    'marketplace.user.address_book_event_listenner' => true,
    'marketplace.user.api_user_serializer' => true,
    'marketplace.user_provider' => true,
    'messenger.bus.default' => true,
    'messenger.bus.default.messenger.handlers_locator' => true,
    'messenger.bus.default.middleware.add_bus_name_stamp_middleware' => true,
    'messenger.bus.default.middleware.handle_message' => true,
    'messenger.default_serializer' => true,
    'messenger.listener.dispatch_pcntl_signal_listener' => true,
    'messenger.listener.stop_worker_on_restart_signal_listener' => true,
    'messenger.listener.stop_worker_on_sigterm_signal_listener' => true,
    'messenger.middleware.add_bus_name_stamp_middleware' => true,
    'messenger.middleware.dispatch_after_current_bus' => true,
    'messenger.middleware.doctrine_close_connection' => true,
    'messenger.middleware.doctrine_ping_connection' => true,
    'messenger.middleware.doctrine_transaction' => true,
    'messenger.middleware.failed_message_processing_middleware' => true,
    'messenger.middleware.handle_message' => true,
    'messenger.middleware.reject_redelivered_message_middleware' => true,
    'messenger.middleware.send_message' => true,
    'messenger.middleware.traceable' => true,
    'messenger.middleware.validation' => true,
    'messenger.receiver_locator' => true,
    'messenger.retry.abstract_multiplier_retry_strategy' => true,
    'messenger.retry.multiplier_retry_strategy.async_yavin' => true,
    'messenger.retry.send_failed_message_for_retry_listener' => true,
    'messenger.retry_strategy_locator' => true,
    'messenger.routable_message_bus' => true,
    'messenger.senders_locator' => true,
    'messenger.transport.amqp.factory' => true,
    'messenger.transport.async_yavin' => true,
    'messenger.transport.doctrine.factory' => true,
    'messenger.transport.in_memory.factory' => true,
    'messenger.transport.native_php_serializer' => true,
    'messenger.transport.redis.factory' => true,
    'messenger.transport.symfony_serializer' => true,
    'messenger.transport.sync.factory' => true,
    'messenger.transport_factory' => true,
    'mime_types' => true,
    'monolog.activation_strategy.not_found' => true,
    'monolog.formatter.chrome_php' => true,
    'monolog.formatter.gelf_message' => true,
    'monolog.formatter.html' => true,
    'monolog.formatter.json' => true,
    'monolog.formatter.line' => true,
    'monolog.formatter.loggly' => true,
    'monolog.formatter.logstash' => true,
    'monolog.formatter.normalizer' => true,
    'monolog.formatter.scalar' => true,
    'monolog.formatter.wildfire' => true,
    'monolog.handler.fingers_crossed.error_level_activation_strategy' => true,
    'monolog.handler.logstash' => true,
    'monolog.handler.main' => true,
    'monolog.handler.null_internal' => true,
    'monolog.handler.verbose' => true,
    'monolog.http_client' => true,
    'monolog.logger' => true,
    'monolog.logger.cache' => true,
    'monolog.logger.console' => true,
    'monolog.logger.console_command' => true,
    'monolog.logger.doctrine' => true,
    'monolog.logger.http_request' => true,
    'monolog.logger.mailer' => true,
    'monolog.logger.messenger' => true,
    'monolog.logger.php' => true,
    'monolog.logger.request' => true,
    'monolog.logger.router' => true,
    'monolog.logger.security' => true,
    'monolog.logger.snappy' => true,
    'monolog.logger.snc_redis' => true,
    'monolog.logger.translation' => true,
    'monolog.logger_prototype' => true,
    'monolog.processor.console_command' => true,
    'monolog.processor.extra_context' => true,
    'monolog.processor.http_request' => true,
    'monolog.processor.psr_log_message' => true,
    'nelmio_cors.cors_listener' => true,
    'nelmio_cors.options_provider.config' => true,
    'nelmio_cors.options_resolver' => true,
    'ovh.client' => true,
    'parameter_bag' => true,
    'property_accessor' => true,
    'property_info.serializer_extractor' => true,
    'resolve_controller_name_subscriber' => true,
    'response_listener' => true,
    'reverse_container' => true,
    'router.cache_warmer' => true,
    'router.default' => true,
    'router.request_context' => true,
    'router_listener' => true,
    'routing.loader.annotation' => true,
    'routing.loader.annotation.directory' => true,
    'routing.loader.annotation.file' => true,
    'routing.loader.container' => true,
    'routing.loader.directory' => true,
    'routing.loader.glob' => true,
    'routing.loader.php' => true,
    'routing.loader.service' => true,
    'routing.loader.xml' => true,
    'routing.loader.yml' => true,
    'routing.resolver' => true,
    'rulerz.datasource.object' => true,
    'rulerz.operator.array_intersect' => true,
    'secrets.local_vault' => true,
    'secrets.vault' => true,
    'security.access.authenticated_voter' => true,
    'security.access.decision_manager' => true,
    'security.access.expression_voter' => true,
    'security.access.role_hierarchy_voter' => true,
    'security.access_listener' => true,
    'security.access_map' => true,
    'security.authentication.basic_entry_point' => true,
    'security.authentication.basic_entry_point.default' => true,
    'security.authentication.basic_entry_point.system' => true,
    'security.authentication.custom_failure_handler' => true,
    'security.authentication.custom_success_handler' => true,
    'security.authentication.failure_handler' => true,
    'security.authentication.form_entry_point' => true,
    'security.authentication.guard_handler' => true,
    'security.authentication.listener.abstract' => true,
    'security.authentication.listener.anonymous' => true,
    'security.authentication.listener.anonymous.default' => true,
    'security.authentication.listener.anonymous.system' => true,
    'security.authentication.listener.basic' => true,
    'security.authentication.listener.basic.api_get_key' => true,
    'security.authentication.listener.basic.default' => true,
    'security.authentication.listener.basic.system' => true,
    'security.authentication.listener.form' => true,
    'security.authentication.listener.guard' => true,
    'security.authentication.listener.json' => true,
    'security.authentication.listener.rememberme' => true,
    'security.authentication.listener.remote_user' => true,
    'security.authentication.listener.simple_form' => true,
    'security.authentication.listener.simple_preauth' => true,
    'security.authentication.listener.simple_preauth.default' => true,
    'security.authentication.listener.x509' => true,
    'security.authentication.manager' => true,
    'security.authentication.provider.anonymous' => true,
    'security.authentication.provider.anonymous.default' => true,
    'security.authentication.provider.anonymous.system' => true,
    'security.authentication.provider.dao' => true,
    'security.authentication.provider.dao.api_get_key' => true,
    'security.authentication.provider.dao.default' => true,
    'security.authentication.provider.dao.system' => true,
    'security.authentication.provider.guard' => true,
    'security.authentication.provider.ldap_bind' => true,
    'security.authentication.provider.pre_authenticated' => true,
    'security.authentication.provider.rememberme' => true,
    'security.authentication.provider.simple' => true,
    'security.authentication.provider.simple_preauth.default' => true,
    'security.authentication.rememberme.services.abstract' => true,
    'security.authentication.rememberme.services.persistent' => true,
    'security.authentication.rememberme.services.simplehash' => true,
    'security.authentication.retry_entry_point' => true,
    'security.authentication.session_strategy' => true,
    'security.authentication.session_strategy.api_get_key' => true,
    'security.authentication.session_strategy.default' => true,
    'security.authentication.session_strategy.system' => true,
    'security.authentication.session_strategy_noop' => true,
    'security.authentication.simple_success_failure_handler' => true,
    'security.authentication.success_handler' => true,
    'security.authentication.switchuser_listener' => true,
    'security.authentication.trust_resolver' => true,
    'security.channel_listener' => true,
    'security.command.user_password_encoder' => true,
    'security.context_listener' => true,
    'security.csrf.token_generator' => true,
    'security.csrf.token_storage' => true,
    'security.encoder_factory' => true,
    'security.encoder_factory.generic' => true,
    'security.exception_listener' => true,
    'security.exception_listener.api_get_key' => true,
    'security.exception_listener.default' => true,
    'security.exception_listener.system' => true,
    'security.expression_language' => true,
    'security.firewall' => true,
    'security.firewall.config' => true,
    'security.firewall.context' => true,
    'security.firewall.lazy_context' => true,
    'security.firewall.map' => true,
    'security.firewall.map.config.api_get_key' => true,
    'security.firewall.map.config.default' => true,
    'security.firewall.map.config.dev' => true,
    'security.firewall.map.config.system' => true,
    'security.firewall.map.context.api_get_key' => true,
    'security.firewall.map.context.default' => true,
    'security.firewall.map.context.dev' => true,
    'security.firewall.map.context.system' => true,
    'security.helper' => true,
    'security.http_utils' => true,
    'security.logout.handler.cookie_clearing' => true,
    'security.logout.handler.csrf_token_clearing' => true,
    'security.logout.handler.session' => true,
    'security.logout.success_handler' => true,
    'security.logout_listener' => true,
    'security.logout_url_generator' => true,
    'security.rememberme.response_listener' => true,
    'security.rememberme.token.provider.in_memory' => true,
    'security.role_hierarchy' => true,
    'security.untracked_token_storage' => true,
    'security.user.provider.chain' => true,
    'security.user.provider.concrete.cscart' => true,
    'security.user.provider.concrete.cscart_api' => true,
    'security.user.provider.concrete.in_memory' => true,
    'security.user.provider.in_memory' => true,
    'security.user.provider.in_memory.user' => true,
    'security.user.provider.ldap' => true,
    'security.user.provider.missing' => true,
    'security.user_checker' => true,
    'security.user_checker.api_get_key' => true,
    'security.user_checker.default' => true,
    'security.user_checker.system' => true,
    'security.user_password_encoder.generic' => true,
    'security.user_value_resolver' => true,
    'security.validator.user_password' => true,
    'serializer.denormalizer.array' => true,
    'serializer.encoder.csv' => true,
    'serializer.encoder.json' => true,
    'serializer.encoder.xml' => true,
    'serializer.encoder.yaml' => true,
    'serializer.mapping.cache.symfony' => true,
    'serializer.mapping.cache_class_metadata_factory' => true,
    'serializer.mapping.cache_class_metadata_factory.inner' => true,
    'serializer.mapping.cache_warmer' => true,
    'serializer.mapping.chain_loader' => true,
    'serializer.mapping.class_discriminator_resolver' => true,
    'serializer.mapping.class_metadata_factory' => true,
    'serializer.name_converter.camel_case_to_snake_case' => true,
    'serializer.name_converter.metadata_aware' => true,
    'serializer.normalizer.constraint_violation_list' => true,
    'serializer.normalizer.data_uri' => true,
    'serializer.normalizer.dateinterval' => true,
    'serializer.normalizer.datetime' => true,
    'serializer.normalizer.datetimezone' => true,
    'serializer.normalizer.json_serializable' => true,
    'serializer.normalizer.object' => true,
    'serializer.normalizer.problem' => true,
    'serializer.property_accessor' => true,
    'session.abstract_handler' => true,
    'session.attribute_bag' => true,
    'session.flash_bag' => true,
    'session.handler' => true,
    'session.handler.native_file' => true,
    'session.save_listener' => true,
    'session.storage' => true,
    'session.storage.cscart_bridge' => true,
    'session.storage.filesystem' => true,
    'session.storage.metadata_bag' => true,
    'session.storage.mock_file' => true,
    'session.storage.native' => true,
    'session.storage.php_bridge' => true,
    'session_listener' => true,
    'snc_redis.command.flush_all' => true,
    'snc_redis.command.flush_db' => true,
    'snc_redis.cscart_sessions_client' => true,
    'snc_redis.data_collector' => true,
    'snc_redis.default_client' => true,
    'snc_redis.handlers_client' => true,
    'snc_redis.locks_client' => true,
    'snc_redis.logger' => true,
    'snc_redis.phpredis.cscart_sessions' => true,
    'snc_redis.phpredis.default' => true,
    'snc_redis.phpredis.handlers' => true,
    'snc_redis.phpredis.locks' => true,
    'streamed_response_listener' => true,
    'swiftmailer.command.debug' => true,
    'swiftmailer.command.new_email' => true,
    'swiftmailer.command.send_email' => true,
    'swiftmailer.data_collector' => true,
    'swiftmailer.email_sender.listener' => true,
    'swiftmailer.mailer' => true,
    'swiftmailer.mailer.abstract' => true,
    'swiftmailer.mailer.fake_mailer.transport.eventdispatcher' => true,
    'swiftmailer.mailer.fake_mailer.transport.null' => true,
    'swiftmailer.mailer.mailer.transport.authhandler' => true,
    'swiftmailer.mailer.mailer.transport.buffer' => true,
    'swiftmailer.mailer.mailer.transport.eventdispatcher' => true,
    'swiftmailer.mailer.mailer.transport.smtp' => true,
    'swiftmailer.plugin.antiflood.abstract' => true,
    'swiftmailer.plugin.impersonate.abstract' => true,
    'swiftmailer.plugin.messagelogger.abstract' => true,
    'swiftmailer.plugin.redirecting.abstract' => true,
    'swiftmailer.spool.file.abstract' => true,
    'swiftmailer.spool.memory.abstract' => true,
    'swiftmailer.transport.authhandler.abstract' => true,
    'swiftmailer.transport.buffer.abstract' => true,
    'swiftmailer.transport.configurator.mailer' => true,
    'swiftmailer.transport.eventdispatcher.abstract' => true,
    'swiftmailer.transport.failover' => true,
    'swiftmailer.transport.null.abstract' => true,
    'swiftmailer.transport.replacementfactory' => true,
    'swiftmailer.transport.sendmail.abstract' => true,
    'swiftmailer.transport.smtp.abstract' => true,
    'swiftmailer.transport.smtp.configurator.abstract' => true,
    'swiftmailer.transport.spool.abstract' => true,
    'templating.cache_warmer.template_paths' => true,
    'templating.engine.delegating' => true,
    'templating.engine.smarty' => true,
    'templating.engine.twig' => true,
    'templating.filename_parser' => true,
    'templating.finder' => true,
    'templating.helper.logout_url' => true,
    'templating.helper.security' => true,
    'templating.loader.cache' => true,
    'templating.loader.chain' => true,
    'templating.loader.filesystem' => true,
    'templating.locator' => true,
    'templating.name_parser' => true,
    'translation.dumper.csv' => true,
    'translation.dumper.ini' => true,
    'translation.dumper.json' => true,
    'translation.dumper.mo' => true,
    'translation.dumper.php' => true,
    'translation.dumper.po' => true,
    'translation.dumper.qt' => true,
    'translation.dumper.res' => true,
    'translation.dumper.xliff' => true,
    'translation.dumper.yaml' => true,
    'translation.dumper.yml' => true,
    'translation.extractor' => true,
    'translation.extractor.cscart' => true,
    'translation.extractor.php' => true,
    'translation.extractor.smarty' => true,
    'translation.loader.csv' => true,
    'translation.loader.dat' => true,
    'translation.loader.database' => true,
    'translation.loader.ini' => true,
    'translation.loader.json' => true,
    'translation.loader.mo' => true,
    'translation.loader.php' => true,
    'translation.loader.po' => true,
    'translation.loader.qt' => true,
    'translation.loader.res' => true,
    'translation.loader.xliff' => true,
    'translation.loader.yml' => true,
    'translation.reader' => true,
    'translation.warmer' => true,
    'translation.writer' => true,
    'translator.default' => true,
    'translator.formatter' => true,
    'translator.formatter.default' => true,
    'translator.logging' => true,
    'translator.selector' => true,
    'twig.app_variable' => true,
    'twig.cache_warmer' => true,
    'twig.command.debug' => true,
    'twig.command.lint' => true,
    'twig.configurator.environment' => true,
    'twig.error_renderer.html' => true,
    'twig.error_renderer.html.inner' => true,
    'twig.exception_listener' => true,
    'twig.extension.assets' => true,
    'twig.extension.code' => true,
    'twig.extension.debug' => true,
    'twig.extension.debug.stopwatch' => true,
    'twig.extension.expression' => true,
    'twig.extension.form' => true,
    'twig.extension.httpfoundation' => true,
    'twig.extension.httpkernel' => true,
    'twig.extension.intl' => true,
    'twig.extension.logout_url' => true,
    'twig.extension.profiler' => true,
    'twig.extension.routing' => true,
    'twig.extension.security' => true,
    'twig.extension.security_csrf' => true,
    'twig.extension.text' => true,
    'twig.extension.trans' => true,
    'twig.extension.weblink' => true,
    'twig.extension.yaml' => true,
    'twig.form.engine' => true,
    'twig.form.renderer' => true,
    'twig.loader' => true,
    'twig.loader.chain' => true,
    'twig.loader.filesystem' => true,
    'twig.loader.native_filesystem' => true,
    'twig.mailer.message_listener' => true,
    'twig.mime_body_renderer' => true,
    'twig.profile' => true,
    'twig.runtime.httpkernel' => true,
    'twig.runtime.security_csrf' => true,
    'twig.runtime_loader' => true,
    'twig.template_cache_warmer' => true,
    'twig.template_iterator' => true,
    'twig.translation.extractor' => true,
    'uri_signer' => true,
    'url_helper' => true,
    'validate_request_listener' => true,
    'validator.builder' => true,
    'validator.email' => true,
    'validator.expression' => true,
    'validator.mapping.cache.adapter' => true,
    'validator.mapping.cache.symfony' => true,
    'validator.mapping.cache_warmer' => true,
    'validator.mapping.class_metadata_factory' => true,
    'validator.not_compromised_password' => true,
    'validator.validator_factory' => true,
    'workflow.twig_extension' => true,
];
