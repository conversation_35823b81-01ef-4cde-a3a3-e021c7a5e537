<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'console.command.public_alias.Wizacha\AppBundle\Command\ListEximJobsImport' shared autowired service.

return $this->services['console.command.public_alias.Wizacha\\AppBundle\\Command\\ListEximJobsImport'] = new \Wizacha\AppBundle\Command\ListEximJobsImport(($this->services['marketplace.import.job_service'] ?? $this->load('getMarketplace_Import_JobServiceService.php')));
