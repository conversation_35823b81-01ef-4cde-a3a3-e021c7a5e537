<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Component\AuthLog\AuthLogRepository' shared service.

return $this->services['Wizacha\\Component\\AuthLog\\AuthLogRepository'] = new \Wizacha\Component\AuthLog\AuthLogRepository(($this->services['doctrine'] ?? $this->getDoctrineService()), 'Wizacha\\Component\\AuthLog\\AuthLog', ($this->privates['monolog.logger.security'] ?? $this->load('getMonolog_Logger_SecurityService.php')));
