<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'Wizacha\Marketplace\PIM\Option\Handler\CommitmentPeriodHandler' shared autowired service.

return $this->privates['Wizacha\\Marketplace\\PIM\\Option\\Handler\\CommitmentPeriodHandler'] = new \Wizacha\Marketplace\PIM\Option\Handler\CommitmentPeriodHandler(($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()));
