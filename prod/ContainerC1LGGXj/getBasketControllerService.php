<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\BasketController' shared autowired service.

$a = ($this->services['marketplace.user.address_service'] ?? $this->load('getMarketplace_User_AddressServiceService.php'));
$b = ($this->services['Wizacha\\Marketplace\\User\\UserService'] ?? $this->getUserServiceService());

$this->services['Wizacha\\AppBundle\\Controller\\Api\\BasketController'] = $instance = new \Wizacha\AppBundle\Controller\Api\BasketController(($this->services['validator'] ?? $this->load('getValidatorService.php')), ($this->services['Wizacha\\Marketplace\\PIM\\Stock\\StockService'] ?? $this->load('getStockServiceService.php')), $a, $b, false, ($this->services['Wizacha\\Marketplace\\Basket\\Checkout'] ?? $this->load('getCheckoutService.php')), ($this->privates['Wizacha\\AppBundle\\Service\\WhitelistDomainsService'] ?? $this->load('getWhitelistDomainsServiceService.php')), ($this->services['marketplace.chronopost.client'] ?? ($this->services['marketplace.chronopost.client'] = new \Wizacha\Component\Chronopost\Client($this->getEnv('CHRONOPOST_API_ACCOUNT_NUMBER'), $this->getEnv('CHRONOPOST_API_PASSWORD')))), ($this->services['marketplace.mondial_relay.client'] ?? ($this->services['marketplace.mondial_relay.client'] = new \Wizacha\Component\MondialRelay\Client($this->getEnv('MONDIAL_RELAY_API_USERID'), $this->getEnv('MONDIAL_RELAY_API_PASSWORD'), $this->getEnv('MONDIAL_RELAY_API_ENDPOINT')))), false, ($this->services['marketplace.payment.payment_service'] ?? $this->load('getMarketplace_Payment_PaymentServiceService.php')), $this->getEnv('CHRONOPOST_API_ACCOUNT_NUMBER'), $this->getEnv('CHRONOPOST_API_PASSWORD'), $this->getEnv('MONDIAL_RELAY_API_USERID'), $this->getEnv('MONDIAL_RELAY_API_PASSWORD'), ($this->services['Wizacha\\Marketplace\\Basket\\BasketService'] ?? $this->load('getBasketServiceService.php')), ($this->services['logger'] ?? $this->load('getLoggerService.php')), ($this->services['monolog.logger.functional'] ?? $this->load('getMonolog_Logger_FunctionalService.php')), false);

$instance->setContainer((new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($this->getService, [
    'doctrine' => ['services', 'doctrine', 'getDoctrineService', false],
    'form.factory' => ['services', 'form.factory', 'getForm_FactoryService.php', true],
    'http_kernel' => ['services', 'http_kernel', 'getHttpKernelService', false],
    'message_bus' => ['services', 'message_bus', 'getMessageBusService.php', true],
    'messenger.default_bus' => ['services', 'message_bus', 'getMessageBusService.php', true],
    'parameter_bag' => ['privates', 'parameter_bag', 'getParameterBagService', false],
    'request_stack' => ['services', 'request_stack', 'getRequestStackService', false],
    'router' => ['services', 'router', 'getRouterService', false],
    'security.authorization_checker' => ['services', 'security.authorization_checker', 'getSecurity_AuthorizationCheckerService.php', true],
    'security.csrf.token_manager' => ['services', 'security.csrf.token_manager', 'getSecurity_Csrf_TokenManagerService.php', true],
    'security.token_storage' => ['services', 'security.token_storage', 'getSecurity_TokenStorageService', false],
    'serializer' => ['services', 'serializer', 'getSerializerService.php', true],
    'session' => ['services', 'session', 'getSessionService.php', true],
    'templating' => ['services', 'templating', 'getTemplatingService.php', true],
    'twig' => ['services', 'twig', 'getTwigService.php', true],
], [
    'doctrine' => '?',
    'form.factory' => '?',
    'http_kernel' => '?',
    'message_bus' => '?',
    'messenger.default_bus' => '?',
    'parameter_bag' => '?',
    'request_stack' => '?',
    'router' => '?',
    'security.authorization_checker' => '?',
    'security.csrf.token_manager' => '?',
    'security.token_storage' => '?',
    'serializer' => '?',
    'session' => '?',
    'templating' => '?',
    'twig' => '?',
]))->withContext('Wizacha\\AppBundle\\Controller\\Api\\BasketController', $this));
$instance->setAddressBookService($a);
$instance->setUserService($b);

return $instance;
