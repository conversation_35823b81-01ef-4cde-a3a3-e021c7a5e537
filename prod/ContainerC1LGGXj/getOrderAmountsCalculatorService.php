<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Order\AmountsCalculator\Service\OrderAmountsCalculator' shared autowired service.

return $this->services['Wizacha\\Marketplace\\Order\\AmountsCalculator\\Service\\OrderAmountsCalculator'] = new \Wizacha\Marketplace\Order\AmountsCalculator\Service\OrderAmountsCalculator('20-07-2021 13:00:00', ($this->services['order_amount_repository'] ?? $this->load('getOrderAmountRepositoryService.php')), ($this->services['marketplace.promotion.repository'] ?? $this->load('getMarketplace_Promotion_RepositoryService.php')), ($this->services['Wizacha\\Marketplace\\Order\\OrderService'] ?? $this->load('getOrderServiceService.php')), ($this->services['Wizacha\\Marketplace\\Commission\\CommissionService'] ?? $this->load('getCommissionServiceService.php')), ($this->services['logger'] ?? $this->load('getLoggerService.php')), ($this->services['marketplace.international_tax.tax_repository'] ?? $this->load('getMarketplace_InternationalTax_TaxRepositoryService.php')), ($this->services['Wizacha\\Core\\Concurrent\\MutexService'] ?? $this->load('getMutexServiceService.php')));
