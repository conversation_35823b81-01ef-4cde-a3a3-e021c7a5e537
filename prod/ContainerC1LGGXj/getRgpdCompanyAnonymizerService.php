<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'Wizacha\Marketplace\RgpdAnonymizer\RgpdCompanyAnonymizer' shared service.

return $this->privates['Wizacha\\Marketplace\\RgpdAnonymizer\\RgpdCompanyAnonymizer'] = new \Wizacha\Marketplace\RgpdAnonymizer\RgpdCompanyAnonymizer(($this->services['Wizacha\\Marketplace\\Order\\OrderService'] ?? $this->load('getOrderServiceService.php')), ($this->services['Wizacha\\Marketplace\\Company\\CompanyService'] ?? $this->load('getCompanyServiceService.php')), ($this->services['Wizacha\\Storage\\VendorSubscriptionStorageService'] ?? $this->load('getVendorSubscriptionStorageServiceService.php')), ($this->privates['Wizacha\\Marketplace\\RgpdAnonymizer\\RgpdUserAnonymizer'] ?? $this->load('getRgpdUserAnonymizerService.php')), new \Wizacha\Marketplace\RgpdAnonymizer\Report\CompanyReport());
