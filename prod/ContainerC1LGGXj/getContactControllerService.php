<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\ContactController' shared autowired service.

return $this->services['Wizacha\\AppBundle\\Controller\\Api\\ContactController'] = new \Wizacha\AppBundle\Controller\Api\ContactController(($this->services['Wizacha\\Marketplace\\AdminCompany'] ?? ($this->services['Wizacha\\Marketplace\\AdminCompany'] = new \Wizacha\Marketplace\AdminCompany())), ($this->services['app.mailer'] ?? $this->load('getApp_MailerService.php')), ($this->services['logger'] ?? $this->load('getLoggerService.php')), ($this->services['validator'] ?? $this->load('getValidatorService.php')));
