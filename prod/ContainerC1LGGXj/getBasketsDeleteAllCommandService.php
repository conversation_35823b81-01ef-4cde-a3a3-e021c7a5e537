<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'console.command.public_alias.Wizacha\AppBundle\Command\BasketsDeleteAllCommand' shared autowired service.

return $this->services['console.command.public_alias.Wizacha\\AppBundle\\Command\\BasketsDeleteAllCommand'] = new \Wizacha\AppBundle\Command\BasketsDeleteAllCommand(($this->services['Wizacha\\Bridge\\Broadway\\DatabaseRepository'] ?? ($this->services['Wizacha\\Bridge\\Broadway\\DatabaseRepository'] = new \Wizacha\Bridge\Broadway\DatabaseRepository('basket_short', 'Wizacha\\Marketplace\\ReadModel\\Basket'))), ($this->services['Wizacha\\Marketplace\\User\\UserRepository'] ?? $this->getUserRepositoryService()));
