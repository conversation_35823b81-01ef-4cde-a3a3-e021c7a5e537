<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Payment\Processor\Stripe' shared autowired service.

$a = ($this->services['Wizacha\\Marketplace\\Subscription\\SubscriptionService'] ?? $this->load('getSubscriptionServiceService.php'));

if (isset($this->services['Wizacha\\Marketplace\\Payment\\Processor\\Stripe'])) {
    return $this->services['Wizacha\\Marketplace\\Payment\\Processor\\Stripe'];
}

$this->services['Wizacha\\Marketplace\\Payment\\Processor\\Stripe'] = $instance = new \Wizacha\Marketplace\Payment\Processor\Stripe(($this->services['Wizacha\\Marketplace\\Order\\OrderService'] ?? $this->load('getOrderServiceService.php')), ($this->services['Wizacha\\Marketplace\\Commission\\CommissionService'] ?? $this->load('getCommissionServiceService.php')), ($this->services['marketplace.payment.user_payment_info_service'] ?? $this->load('getMarketplace_Payment_UserPaymentInfoServiceService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\MarkAsPaid'] ?? $this->load('getMarkAsPaidService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\MarkPaymentAsRefused'] ?? $this->load('getMarkPaymentAsRefusedService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\RedirectToPaymentProcessor'] ?? $this->load('getRedirectToPaymentProcessorService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\Confirm'] ?? $this->load('getConfirmService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\DispatchFunds'] ?? $this->load('getDispatchFundsService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\DispatchFundsFailed'] ?? $this->load('getDispatchFundsFailedService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\MarkPaymentAuthorizationAsCaptured'] ?? $this->load('getMarkPaymentAuthorizationAsCapturedService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\MarkPaymentAuthorizationAsRefused'] ?? $this->load('getMarkPaymentAuthorizationAsRefusedService.php')), ($this->services['logger'] ?? $this->load('getLoggerService.php')), ($this->services['Broadway\\UuidGenerator\\Rfc4122\\Version4Generator'] ?? ($this->services['Broadway\\UuidGenerator\\Rfc4122\\Version4Generator'] = new \Broadway\UuidGenerator\Rfc4122\Version4Generator())), ($this->services['Wizacha\\Marketplace\\Payment\\Stripe\\StripeConfig'] ?? ($this->services['Wizacha\\Marketplace\\Payment\\Stripe\\StripeConfig'] = new \Wizacha\Marketplace\Payment\Stripe\StripeConfig($this->getEnv('STRIPE_SECRET_TOKEN'), 'EUR', 'Ovhcloud'))), ($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()), ($this->services['Wizacha\\Marketplace\\Order\\Action\\DispatchFundsSucceeded'] ?? $this->load('getDispatchFundsSucceededService.php')), $a, ($this->services['Wizacha\\Marketplace\\User\\UserService'] ?? $this->getUserServiceService()));

$instance->setTransactionService(($this->services['Wizacha\\Marketplace\\Transaction\\TransactionService'] ?? $this->load('getTransactionServiceService.php')));
$instance->setApi(($this->services['Wizacha\\Marketplace\\Payment\\Stripe\\StripeApi'] ?? $this->load('getStripeApiService.php')));

return $instance;
