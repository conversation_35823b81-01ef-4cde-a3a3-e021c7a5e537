<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\PriceTier\Repository\PriceTierRepository' shared service.

return $this->services['Wizacha\\Marketplace\\PriceTier\\Repository\\PriceTierRepository'] = new \Wizacha\Marketplace\PriceTier\Repository\PriceTierRepository(($this->services['doctrine'] ?? $this->getDoctrineService()), 'Wizacha\\Marketplace\\PriceTier\\PriceTier');
