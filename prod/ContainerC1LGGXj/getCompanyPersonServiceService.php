<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\CompanyPerson\CompanyPersonService' shared service.

return $this->services['Wizacha\\Marketplace\\CompanyPerson\\CompanyPersonService'] = new \Wizacha\Marketplace\CompanyPerson\CompanyPersonService(($this->privates['Wizacha\\Marketplace\\CompanyPerson\\CompanyPersonRepository'] ?? $this->load('getCompanyPersonRepositoryService.php')));
