<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\ImportController' shared autowired service.

return $this->services['Wizacha\\AppBundle\\Controller\\Api\\ImportController'] = new \Wizacha\AppBundle\Controller\Api\ImportController(($this->services['Wizacha\\Component\\Import\\ImportService'] ?? $this->load('getImportServiceService.php')), ($this->services['Wizacha\\Component\\Import\\ImportFactory'] ?? $this->load('getImportFactoryService.php')), ($this->services['marketplace.import.job_service'] ?? $this->load('getMarketplace_Import_JobServiceService.php')));
