<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'app.exception_subscriber' shared autowired service.

return $this->privates['app.exception_subscriber'] = new \Wizacha\AppBundle\EventSubscriber\ExceptionSubscriber(($this->services['logger'] ?? $this->load('getLoggerService.php')));
