<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\PIM\Option\SystemOptionsRegistry' shared autowired service.

return $this->services['Wizacha\\Marketplace\\PIM\\Option\\SystemOptionsRegistry'] = new \Wizacha\Marketplace\PIM\Option\SystemOptionsRegistry(new RewindableGenerator(function () {
    yield 0 => ($this->privates['Wizacha\\Marketplace\\PIM\\Option\\Handler\\CommitmentPeriodHandler'] ?? $this->load('getCommitmentPeriodHandlerService.php'));
    yield 1 => ($this->privates['Wizacha\\Marketplace\\PIM\\Option\\Handler\\PaymentFrequencyHandler'] ?? $this->load('getPaymentFrequencyHandlerService.php'));
}, 2));
