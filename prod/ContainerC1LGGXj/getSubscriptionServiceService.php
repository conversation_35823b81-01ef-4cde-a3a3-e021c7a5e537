<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Subscription\SubscriptionService' shared autowired service.

$this->services['Wizacha\\Marketplace\\Subscription\\SubscriptionService'] = $instance = new \Wizacha\Marketplace\Subscription\SubscriptionService(($this->services['Wizacha\\Marketplace\\User\\UserService'] ?? $this->getUserServiceService()), ($this->services['Wizacha\\Marketplace\\PIM\\Product\\ProductService'] ?? $this->load('getProductService2Service.php')), ($this->services['Wizacha\\Marketplace\\Subscription\\SubscriptionRepository'] ?? $this->getSubscriptionRepositoryService()), ($this->services['doctrine.orm.default_entity_manager'] ?? $this->getDoctrine_Orm_DefaultEntityManagerService()), ($this->privates['Wizacha\\Marketplace\\Date\\DateService'] ?? ($this->privates['Wizacha\\Marketplace\\Date\\DateService'] = new \Wizacha\Marketplace\Date\DateService())), ($this->services['Wizacha\\Marketplace\\Order\\OrderService'] ?? $this->load('getOrderServiceService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\MarkPaymentAsRefused'] ?? $this->load('getMarkPaymentAsRefusedService.php')), ($this->services['Wizacha\\Marketplace\\PIM\\Option\\SystemOptionsRegistry'] ?? $this->load('getSystemOptionsRegistryService.php')), ($this->services['Wizacha\\Marketplace\\PriceTier\\Service\\PriceTierService'] ?? $this->load('getPriceTierServiceService.php')), ($this->services['Wizacha\\Marketplace\\Promotion\\PromotionService'] ?? $this->load('getPromotionServiceService.php')), ($this->services['Wizacha\\Marketplace\\Promotion\\ProductPromotionApplier'] ?? ($this->services['Wizacha\\Marketplace\\Promotion\\ProductPromotionApplier'] = new \Wizacha\Marketplace\Promotion\ProductPromotionApplier())), ($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()), ($this->services['logger'] ?? $this->load('getLoggerService.php')), ($this->services['Wizacha\\AppBundle\\Security\\User\\UserService'] ?? $this->load('getUserService2Service.php')));

$instance->setFeatureFlag(true, 1);
$instance->setCreateOrder(($this->services['Wizacha\\Marketplace\\Order\\CreateOrder'] ?? $this->load('getCreateOrderService.php')));
$instance->setConfiguredProcessors(($this->privates['Wizacha\\Marketplace\\Payment\\Processor\\ProcessorService'] ?? $this->load('getProcessorServiceService.php'))->getConfiguredProcessor());

return $instance;
