<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Backend\DiscussController' shared autowired service.

return $this->services['Wizacha\\AppBundle\\Controller\\Backend\\DiscussController'] = new \Wizacha\AppBundle\Controller\Backend\DiscussController(($this->services['marketplace.message_attachment.message_attachment_service'] ?? $this->load('getMarketplace_MessageAttachment_MessageAttachmentServiceService.php')));
