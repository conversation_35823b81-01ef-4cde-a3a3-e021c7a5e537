<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'console.command.public_alias.Wizacha\AppBundle\Command\DispatchFundsCommand' shared autowired service.

$this->services['console.command.public_alias.Wizacha\\AppBundle\\Command\\DispatchFundsCommand'] = $instance = new \Wizacha\AppBundle\Command\DispatchFundsCommand(($this->services['Wizacha\\Marketplace\\Order\\OrderService'] ?? $this->load('getOrderServiceService.php')));

$instance->setLogger(($this->services['logger'] ?? $this->load('getLoggerService.php')));

return $instance;
