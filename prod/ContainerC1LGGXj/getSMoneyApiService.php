<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Payment\SMoney\SMoneyApi' shared service.

return $this->services['Wizacha\\Marketplace\\Payment\\SMoney\\SMoneyApi'] = new \Wizacha\Marketplace\Payment\SMoney\SMoneyApi($this->getEnv('SMONEY_API_BASE_URL'), $this->getEnv('SMONEY_TOKEN'), ($this->services['logger'] ?? $this->load('getLoggerService.php')), ($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()));
