<?php

use <PERSON>ymfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'knp_snappy.pdf' shared service.

$this->services['knp_snappy.pdf'] = $instance = new \Knp\Snappy\Pdf((\dirname(__DIR__, 4).'/vendor/bin/wkhtmltopdf-amd64'), [], []);

$a = new \Symfony\Bridge\Monolog\Logger('snappy');
$a->pushProcessor([0 => ($this->privates['monolog.processor.http_request'] ?? ($this->privates['monolog.processor.http_request'] = new \Wizacha\Bridge\Monolog\Processor\HttpRequestProcessor($this->getEnv('REQUEST_ID_HEADER_NAME')))), 1 => 'addCorrelationInformation']);
$a->pushProcessor([0 => ($this->privates['monolog.processor.console_command'] ?? ($this->privates['monolog.processor.console_command'] = new \Wizacha\Bridge\Monolog\Processor\ConsoleCommandProcessor())), 1 => 'addCorrelationInformation']);
$a->pushProcessor(($this->privates['monolog.processor.extra_context'] ?? ($this->privates['monolog.processor.extra_context'] = new \Wizacha\Bridge\Monolog\Processor\ExtraContextProcessor())));
$a->pushHandler(($this->privates['monolog.handler.main'] ?? $this->getMonolog_Handler_MainService()));

$instance->setLogger($a);

return $instance;
