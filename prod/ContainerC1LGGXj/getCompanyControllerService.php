<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\Catalog\CompanyController' shared service.

return $this->services['Wizacha\\AppBundle\\Controller\\Api\\Catalog\\CompanyController'] = new \Wizacha\AppBundle\Controller\Api\Catalog\CompanyController(($this->services['marketplace.review.company.service'] ?? $this->load('getMarketplace_Review_Company_ServiceService.php')), ($this->services['marketplace.catalog.company_service'] ?? $this->load('getMarketplace_Catalog_CompanyServiceService.php')));
