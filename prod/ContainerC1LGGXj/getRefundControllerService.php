<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\RefundController' shared service.

return $this->services['Wizacha\\AppBundle\\Controller\\Api\\RefundController'] = new \Wizacha\AppBundle\Controller\Api\RefundController(($this->services['router'] ?? $this->getRouterService()), ($this->services['logger'] ?? $this->load('getLoggerService.php')), ($this->services['marketplace.order.refund.refund_service'] ?? $this->load('getMarketplace_Order_Refund_RefundServiceService.php')), ($this->services['marketplace.order.refund.refund_notification_service'] ?? $this->load('getMarketplace_Order_Refund_RefundNotificationServiceService.php')), ($this->services['Wizacha\\Marketplace\\Order\\OrderService'] ?? $this->load('getOrderServiceService.php')), ($this->services['Wizacha\\Bridge\\Broadway\\DatabaseRepository'] ?? ($this->services['Wizacha\\Bridge\\Broadway\\DatabaseRepository'] = new \Wizacha\Bridge\Broadway\DatabaseRepository('basket_short', 'Wizacha\\Marketplace\\ReadModel\\Basket'))), ($this->services['Wizacha\\Marketplace\\Company\\CompanyService'] ?? $this->load('getCompanyServiceService.php')));
