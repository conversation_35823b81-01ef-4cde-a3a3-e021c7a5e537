<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'console.command.public_alias.Wizacha\AppBundle\Command\MangoPaySendKycCommand' shared autowired service.

return $this->services['console.command.public_alias.Wizacha\\AppBundle\\Command\\MangoPaySendKycCommand'] = new \Wizacha\AppBundle\Command\MangoPaySendKycCommand(($this->services['Wizacha\\Marketplace\\Payment\\Processor\\MangoPay'] ?? $this->load('getMangoPayService.php')), ($this->services['Wizacha\\Marketplace\\Company\\CompanyService'] ?? $this->load('getCompanyServiceService.php')), ($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()));
