<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Component\Http\Client\GuzzleYavinClient' shared autowired service.

return $this->services['Wizacha\\Component\\Http\\Client\\GuzzleYavinClient'] = new \Wizacha\Component\Http\Client\GuzzleYavinClient(new \GuzzleHttp\Client(), ($this->services['Wizacha\\Component\\Http\\Factory\\YavinResponseFactory'] ?? ($this->services['Wizacha\\Component\\Http\\Factory\\YavinResponseFactory'] = new \Wizacha\Component\Http\Factory\YavinResponseFactory())), ($this->services['Wizacha\\Component\\Http\\OptionMapper'] ?? ($this->services['Wizacha\\Component\\Http\\OptionMapper'] = new \Wizacha\Component\Http\OptionMapper())));
