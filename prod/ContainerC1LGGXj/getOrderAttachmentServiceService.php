<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Order\OrderAttachment\Service\OrderAttachmentService' shared autowired service.

$a = ($this->services['marketplace.order.order_attachment.repository'] ?? $this->load('getMarketplace_Order_OrderAttachment_RepositoryService.php'));

return $this->services['Wizacha\\Marketplace\\Order\\OrderAttachment\\Service\\OrderAttachmentService'] = new \Wizacha\Marketplace\Order\OrderAttachment\Service\OrderAttachmentService(new \Wizacha\Marketplace\Order\OrderAttachment\Service\Validator\OrderAttachmentValidatorService(), new \Wizacha\Marketplace\Order\OrderAttachment\Service\Validator\OrderAttachmentTypeValidatorService(), $a, ($this->services['Wizacha\\Marketplace\\Order\\OrderService'] ?? $this->load('getOrderServiceService.php')), ($this->services['router'] ?? $this->getRouterService()), new \Wizacha\Marketplace\Order\OrderAttachment\Service\UploadFileService($a, ($this->services['Wizacha\\Storage\\OrderAttachmentsStorageService'] ?? $this->load('getOrderAttachmentsStorageServiceService.php'))));
