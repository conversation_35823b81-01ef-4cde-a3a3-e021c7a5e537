<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'cscart.cache_backend' shared service.

return $this->services['cscart.cache_backend'] = new \Tygh\Backend\Cache\Redis(($this->services['snc_redis.public.default'] ?? $this->load('getSncRedis_Public_DefaultService.php')), ($this->services['snc_redis.public.handlers'] ?? $this->load('getSncRedis_Public_HandlersService.php')), ($this->services['snc_redis.public.locks'] ?? $this->load('getSncRedis_Public_LocksService.php')), 'client-ovh-1.154.5');
