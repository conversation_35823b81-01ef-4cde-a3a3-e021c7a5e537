<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Basket\BasketService' shared service.

return $this->services['Wizacha\\Marketplace\\Basket\\BasketService'] = new \Wizacha\Marketplace\Basket\BasketService(($this->services['broadway.command_handling.command_bus'] ?? $this->load('getBroadway_CommandHandling_CommandBusService.php')), ($this->services['Broadway\\UuidGenerator\\Rfc4122\\Version4Generator'] ?? ($this->services['Broadway\\UuidGenerator\\Rfc4122\\Version4Generator'] = new \Broadway\UuidGenerator\Rfc4122\Version4Generator())), ($this->services['marketplace.basket.repository.aggregate'] ?? $this->load('getMarketplace_Basket_Repository_AggregateService.php')), ($this->services['Wizacha\\Bridge\\Broadway\\DatabaseRepository'] ?? ($this->services['Wizacha\\Bridge\\Broadway\\DatabaseRepository'] = new \Wizacha\Bridge\Broadway\DatabaseRepository('basket_short', 'Wizacha\\Marketplace\\ReadModel\\Basket'))), ($this->services['Wizacha\\Marketplace\\PIM\\Stock\\StockService'] ?? $this->load('getStockServiceService.php')), ($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()), ($this->services['Wizacha\\Marketplace\\Promotion\\PromotionService'] ?? $this->load('getPromotionServiceService.php')), ($this->services['Wizacha\\Marketplace\\ReadModel\\ProductRepository'] ?? $this->load('getProductRepository2Service.php')), ($this->services['logger'] ?? $this->load('getLoggerService.php')), ($this->services['Wizacha\\Marketplace\\Group\\UserGroupService'] ?? $this->load('getUserGroupServiceService.php')));
