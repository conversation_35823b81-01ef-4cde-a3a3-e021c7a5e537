<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\PIM\Product\Template\TemplateService' shared autowired service.

return $this->services['Wizacha\\Marketplace\\PIM\\Product\\Template\\TemplateService'] = new \Wizacha\Marketplace\PIM\Product\Template\TemplateService('service', $this->parameters['config_product_templates']);
