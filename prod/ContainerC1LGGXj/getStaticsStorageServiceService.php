<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Storage\StaticsStorageService' shared service.

return $this->services['Wizacha\\Storage\\StaticsStorageService'] = ($this->privates['Wizacha\\Storage\\StorageFactory'] ?? $this->load('getStorageFactoryService.php'))->__invoke('statics', ['prefix' => 'statics', 'dir' => 'var/cache/misc', 'max-age' => 604800]);
