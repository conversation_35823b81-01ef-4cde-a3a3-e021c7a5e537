<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\CloudImageManager' shared autowired service.

return $this->services['Wizacha\\CloudImageManager'] = new \Wizacha\CloudImageManager('https://api.cloudimage.com', NULL, NULL, ($this->services['guzzle.client'] ?? ($this->services['guzzle.client'] = new \GuzzleHttp\Client())), ($this->services['router'] ?? $this->getRouterService()), ($this->services['logger'] ?? $this->load('getLoggerService.php')), ($this->services['Wizacha\\Async\\Dispatcher'] ?? $this->load('getDispatcherService.php')));
