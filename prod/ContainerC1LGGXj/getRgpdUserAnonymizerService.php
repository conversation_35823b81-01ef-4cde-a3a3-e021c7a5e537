<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'Wizacha\Marketplace\RgpdAnonymizer\RgpdUserAnonymizer' shared autowired service.

$a = ($this->services['Wizacha\\Marketplace\\User\\UserService'] ?? $this->getUserServiceService());

return $this->privates['Wizacha\\Marketplace\\RgpdAnonymizer\\RgpdUserAnonymizer'] = new \Wizacha\Marketplace\RgpdAnonymizer\RgpdUserAnonymizer($a, ($this->services['Wizacha\\Marketplace\\Order\\OrderService'] ?? $this->load('getOrderServiceService.php')), new \Wizacha\Marketplace\RgpdAnonymizer\RgpdDetachUserFromCompany($a));
