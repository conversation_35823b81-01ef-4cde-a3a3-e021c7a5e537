<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Payment\Mangopay\MangoPayApiConfig' shared service.

return $this->services['Wizacha\\Marketplace\\Payment\\Mangopay\\MangoPayApiConfig'] = new \Wizacha\Marketplace\Payment\Mangopay\MangoPayApiConfig($this->getEnv('MANGOPAY_API_CLIENT_ID'), $this->getEnv('MANGOPAY_API_CLIENT_PASSWORD'), $this->getEnv('MANGOPAY_API_BASE_URL'), $this->getEnv('MANGOPAY_API_SECURE_MODE'), 'EUR');
