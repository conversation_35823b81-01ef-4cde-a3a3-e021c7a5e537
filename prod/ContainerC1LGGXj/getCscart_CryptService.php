<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'cscart.crypt' shared service.

$this->services['cscart.crypt'] = $instance = new \phpseclib\Crypt\Blowfish(1);

$instance->setKey('BjyOVTHUtYCdRfyL3VSgIR/9uQxLdj0VLhxNrdDHYr4=');
$instance->disablePadding();

return $instance;
