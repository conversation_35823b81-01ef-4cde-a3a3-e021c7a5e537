<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'MangoPay\MangoPayApi' shared service.

$this->services['MangoPay\\MangoPayApi'] = $instance = new \MangoPay\MangoPayApi();

$instance->setHttpClient(($this->services['marketplace.payment.mangopay_http_client'] ?? $this->load('getMarketplace_Payment_MangopayHttpClientService.php')));

return $instance;
