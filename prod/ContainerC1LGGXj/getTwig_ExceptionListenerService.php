<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'twig.exception_listener' shared service.

@trigger_error('The "twig.exception_listener" service is deprecated since Symfony 4.4.', E_USER_DEPRECATED);

return $this->privates['twig.exception_listener'] = new \Symfony\Component\HttpKernel\EventListener\ExceptionListener('twig.controller.exception::showAction', ($this->privates['monolog.logger.request'] ?? $this->getMonolog_Logger_RequestService()), false);
