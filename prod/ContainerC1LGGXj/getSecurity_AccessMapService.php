<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'security.access_map' shared service.

$this->privates['security.access_map'] = $instance = new \Symfony\Component\Security\Http\AccessMap();

$a = new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/quote-request-selections');

$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/callbacks'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/divisions-tree'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/divisions'), [0 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/currencies', NULL, [0 => 'GET']), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/currencies'), [0 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/promotions/catalog'), [0 => 'ROLE_VENDOR'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/promotions/basket'), [0 => 'ROLE_VENDOR'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/promotions/marketplace'), [0 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/catalog/search/products'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/user/oauth-token'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/user/oauth/authorize-url'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/user/oauth/admin-authorize-url'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/user/oauth/logout'), [0 => 'ROLE_USER'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/user/revoke'), [0 => 'ROLE_USER'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/users/authenticate'), [0 => 'ROLE_USER'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/users/password/recover'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/users/password/change-with-token'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/users/[\\d]+'), [0 => 'ROLE_USER'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/users$', NULL, [0 => 'POST']), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/users/set-vendor', NULL, [0 => 'POST']), [0 => 'ROLE_VENDOR', 1 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/users'), [0 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/user'), [0 => 'ROLE_USER'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/user/orders/[0-9]+/credit-note'), [0 => 'ROLE_USER'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/user/orders/[0-9]+/credit-note/[0-9]+'), [0 => 'ROLE_USER'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/user/orders/[0-9]+/refunds'), [0 => 'ROLE_USER'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/user/orders/[0-9]+/refunds/[0-9]+'), [0 => 'ROLE_USER'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/user/orders/[0-9]+/mark-as-paid', NULL, [0 => 'PUT']), [0 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/prediggo'), [0 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/catalog/products/[^\\/]+/reviews', NULL, [0 => 'POST']), [0 => 'ROLE_USER'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/catalog/products/[^\\/]+/report', NULL, [0 => 'POST']), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/catalog/companies/[0-9]+/reviews', NULL, [0 => 'POST']), [0 => 'ROLE_USER'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/catalog'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/basket/[^\\/]+/chronorelais-pickup-point$', NULL, [0 => 'POST']), [0 => 'ROLE_USER'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/basket/[^\\/]+/order$'), [0 => 'ROLE_USER'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/basket/[^\\/]+/shipping-price$', NULL, [0 => 'POST', 1 => 'DELETE']), [0 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/basket'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/orders/[\\d]+/adjustments'), [0 => 'ROLE_VENDOR', 1 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/orders/returns/reasons'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/orders/[0-9]+/actions', NULL, [0 => 'GET']), [0 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/orders/[0-9]+/transactions', NULL, [0 => 'GET']), [0 => 'ROLE_VENDOR', 1 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/orders/[0-9]+/pdf-invoice'), [0 => 'ROLE_VENDOR', 1 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/orders/[0-9]+/refunds'), [0 => 'ROLE_VENDOR', 1 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/orders/[0-9]+/refunds/[0-9]+'), [0 => 'ROLE_VENDOR', 1 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/orders/[0-9]+/credit-note'), [0 => 'ROLE_VENDOR', 1 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/orders/[0-9]+/credit-note/[0-9]+'), [0 => 'ROLE_VENDOR', 1 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/orders/[0-9]+/attachments'), [0 => 'ROLE_VENDOR', 1 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/orders'), [0 => 'ROLE_USER'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/mailinglists/[0-9]+/subscription$'), [0 => 'ROLE_USER'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/mailinglists'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/image/[\\d]+'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/cms'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/seo/slugs'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/translations/front', NULL, [0 => 'GET']), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/translations/front', NULL, [0 => 'POST']), [0 => 'ROLE_SYSTEM'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/doc'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/pim/multi-vendor-products'), [0 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/pim/moderation'), [0 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/pim/attributes', NULL, [0 => 'GET']), [0 => 'ROLE_VENDOR', 1 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/pim/attributes', NULL, [0 => 'POST', 1 => 'PUT', 2 => 'DELETE']), [0 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/statistics'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/system'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/test'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/discussions'), [0 => 'ROLE_USER'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/chronopost/points-relais'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/mondial-relay/points-relais$'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/mondial-relay/points-relais/[0-9]+$'), [0 => 'ROLE_USER'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/mondial-relay/brand-code'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/companies/c2c'), [0 => 'ROLE_USER'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/companies/[\\d]+/commissions'), [0 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/companies/[\\d]+', NULL, [0 => 'GET']), [0 => 'ROLE_USER'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/companies'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/contact-request'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/organisations/registration'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/organisations/'), [0 => 'ROLE_USER'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/languages'), [0 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/security/'), [0 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/reports/transactions', NULL, [0 => 'GET']), [0 => 'ROLE_VENDOR', 1 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/subscriptions'), [0 => 'ROLE_USER'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/ping'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/payments'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/commissions'), [0 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/categories/[\\d]+/commissions'), [0 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/orders/[\\d]+/shipments', NULL, [0 => 'GET']), [0 => 'ROLE_VENDOR', 1 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/shipments/[ 0-9 ]+/mark-as-delivered', NULL, [0 => 'PUT']), [0 => 'ROLE_USER'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/orders/[ 0-9 ]+/mark-as-delivered', NULL, [0 => 'PUT']), [0 => 'ROLE_USER'], NULL);
$instance->add($a, [0 => 'ROLE_USER'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/'), [0 => 'ROLE_VENDOR', 1 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/api/v1/products/[0-9]+/related', NULL, [0 => 'POST', 1 => 'DELETE']), [0 => 'ROLE_VENDOR', 1 => 'ROLE_ADMIN'], NULL);
$instance->add($a, [0 => 'ROLE_USER'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/admin/api'), [0 => 'ROLE_VENDOR', 1 => 'ROLE_ADMIN'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/admin/oauth/authorize'), [0 => 'IS_AUTHENTICATED_ANONYMOUSLY'], NULL);
$instance->add(new \Symfony\Component\HttpFoundation\RequestMatcher('^/dev/publish'), [0 => 'ROLE_ADMIN'], NULL);

return $instance;
