<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Storage\DownloadsStorageService' shared service.

return $this->services['Wizacha\\Storage\\DownloadsStorageService'] = ($this->privates['Wizacha\\Storage\\StorageFactory'] ?? $this->load('getStorageFactoryService.php'))->__invoke('downloads', ['prefix' => 'downloads', 'dir' => '/var', 'secured' => true]);
