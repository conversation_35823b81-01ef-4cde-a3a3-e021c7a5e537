<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\Order\MondialRelayController' shared autowired service.

return $this->services['Wizacha\\AppBundle\\Controller\\Api\\Order\\MondialRelayController'] = new \Wizacha\AppBundle\Controller\Api\Order\MondialRelayController(($this->services['Wizacha\\Marketplace\\Order\\OrderService'] ?? $this->load('getOrderServiceService.php')), ($this->services['marketplace.mondial_relay.client'] ?? ($this->services['marketplace.mondial_relay.client'] = new \Wizacha\Component\MondialRelay\Client($this->getEnv('MONDIAL_RELAY_API_USERID'), $this->getEnv('MONDIAL_RELAY_API_PASSWORD'), $this->getEnv('MONDIAL_RELAY_API_ENDPOINT')))));
