<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Sentinel\AlertManager' shared service.

return $this->services['Wizacha\\Sentinel\\AlertManager'] = new \Wizacha\Sentinel\AlertManager(($this->services['monolog.logger.sentinel'] ?? $this->load('getMonolog_Logger_SentinelService.php')), 'warning', 'Ovhcloud');
