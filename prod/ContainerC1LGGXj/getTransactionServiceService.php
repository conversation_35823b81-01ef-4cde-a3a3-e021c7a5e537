<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Transaction\TransactionService' shared service.

return $this->services['Wizacha\\Marketplace\\Transaction\\TransactionService'] = new \Wizacha\Marketplace\Transaction\TransactionService(($this->services['marketplace.transaction.transaction_repository'] ?? $this->load('getMarketplace_Transaction_TransactionRepositoryService.php')), ($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()), ($this->services['marketplace.order.refund.refund_repository'] ?? $this->load('getMarketplace_Order_Refund_RefundRepositoryService.php')), ($this->services['logger'] ?? $this->load('getLoggerService.php')), ($this->privates['Wizacha\\Marketplace\\Order\\Repository\\OrderRepository'] ?? $this->load('getOrderRepositoryService.php')));
