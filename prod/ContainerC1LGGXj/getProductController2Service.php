<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\ProductController' shared autowired service.

$this->services['Wizacha\\AppBundle\\Controller\\Api\\ProductController'] = $instance = new \Wizacha\AppBundle\Controller\Api\ProductController(($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()), 'cscart_', ($this->services['Wizacha\\Marketplace\\Division\\Service\\DivisionService'] ?? $this->load('getDivisionServiceService.php')), ($this->services['marketplace.division.products.service'] ?? $this->load('getMarketplace_Division_Products_ServiceService.php')), ($this->services['Wizacha\\Marketplace\\PIM\\Product\\ProductService'] ?? $this->load('getProductService2Service.php')), ($this->services['Wizacha\\Marketplace\\PIM\\Stock\\StockService'] ?? $this->load('getStockServiceService.php')), ($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()), ($this->services['Wizacha\\Marketplace\\Division\\Service\\DivisionSettingsService'] ?? $this->load('getDivisionSettingsServiceService.php')), ($this->services['logger'] ?? $this->load('getLoggerService.php')));

$instance->setContainer($this);
$instance->setUserService(($this->services['Wizacha\\Marketplace\\User\\UserService'] ?? $this->getUserServiceService()));

return $instance;
