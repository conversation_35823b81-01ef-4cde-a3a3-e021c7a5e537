<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\CompanyPerson\CompanyPersonController' shared service.

return $this->services['Wizacha\\AppBundle\\Controller\\Api\\CompanyPerson\\CompanyPersonController'] = new \Wizacha\AppBundle\Controller\Api\CompanyPerson\CompanyPersonController(($this->services['Wizacha\\Marketplace\\CompanyPerson\\CompanyPersonService'] ?? $this->load('getCompanyPersonServiceService.php')), ($this->services['Wizacha\\Marketplace\\Company\\CompanyService'] ?? $this->load('getCompanyServiceService.php')), ($this->privates['Wizacha\\Marketplace\\PSP\\UboMangopay\\UboMangopayService'] ?? $this->load('getUboMangopayServiceService.php')), ($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()), ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\MangoPay'] ?? $this->load('getMangoPayService.php')));
