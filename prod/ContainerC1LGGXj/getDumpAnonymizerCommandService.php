<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'Wizacha\AppBundle\Command\DumpAnonymizerCommand' shared autowired service.

$this->privates['Wizacha\\AppBundle\\Command\\DumpAnonymizerCommand'] = $instance = new \Wizacha\AppBundle\Command\DumpAnonymizerCommand(($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()), new \Wizacha\DumpAnonymizer\Services\OutputS3Manager(new \Aws\S3\S3Client(['version' => '2006-03-01', 'region' => 'eu-west-1', 'credentials' => ['key' => '', 'secret' => '']]), 'wizachatest', 'dump_anonymizer'), new \Cocur\Slugify\Slugify(), ($this->services['logger'] ?? $this->load('getLoggerService.php')), 'wizaplace-loc');

$instance->setName('rgpd:anonymize:dump');

return $instance;
