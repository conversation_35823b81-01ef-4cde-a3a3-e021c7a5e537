<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Metrics\SandboxMetrics' shared autowired service.

return $this->services['Wizacha\\Marketplace\\Metrics\\SandboxMetrics'] = new \Wizacha\Marketplace\Metrics\SandboxMetrics(($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()), false, '2000');
