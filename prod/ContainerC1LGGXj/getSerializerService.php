<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'serializer' shared service.

$a = new \Symfony\Component\Serializer\Mapping\Factory\CacheClassMetadataFactory(new \Symfony\Component\Serializer\Mapping\Factory\ClassMetadataFactory(new \Symfony\Component\Serializer\Mapping\Loader\LoaderChain([])), \Symfony\Component\Cache\Adapter\PhpArrayAdapter::create(($this->targetDir.''.'/serialization.php'), ($this->privates['cache.serializer'] ?? $this->load('getCache_SerializerService.php'))));

$b = new \Symfony\Component\Serializer\NameConverter\MetadataAwareNameConverter($a);

return $this->services['serializer'] = new \Symfony\Component\Serializer\Serializer([0 => new \Wizacha\AppBundle\Controller\Api\Promotion\CatalogPromotionNormalizer(($this->services['RulerZ\\Parser\\Parser'] ?? ($this->services['RulerZ\\Parser\\Parser'] = new \RulerZ\Parser\Parser()))), 1 => ($this->privates['Wizacha\\AppBundle\\Controller\\Api\\Promotion\\BasketPromotionNormalizer'] ?? $this->load('getBasketPromotionNormalizerService.php')), 2 => new \Symfony\Component\Serializer\Normalizer\ProblemNormalizer(false), 3 => new \Symfony\Component\Serializer\Normalizer\JsonSerializableNormalizer(), 4 => new \Symfony\Component\Serializer\Normalizer\DateTimeNormalizer(), 5 => new \Symfony\Component\Serializer\Normalizer\ConstraintViolationListNormalizer([], $b), 6 => new \Symfony\Component\Serializer\Normalizer\DateTimeZoneNormalizer(), 7 => new \Symfony\Component\Serializer\Normalizer\DateIntervalNormalizer(), 8 => new \Symfony\Component\Serializer\Normalizer\DataUriNormalizer(($this->privates['mime_types'] ?? $this->load('getMimeTypesService.php'))), 9 => new \Symfony\Component\Serializer\Normalizer\ArrayDenormalizer(), 10 => new \Symfony\Component\Serializer\Normalizer\ObjectNormalizer($a, $b, ($this->privates['property_accessor'] ?? $this->load('getPropertyAccessorService.php')), NULL, new \Symfony\Component\Serializer\Mapping\ClassDiscriminatorFromClassMetadata($a), NULL, [])], [0 => new \Symfony\Component\Serializer\Encoder\XmlEncoder(), 1 => new \Symfony\Component\Serializer\Encoder\JsonEncoder(), 2 => new \Symfony\Component\Serializer\Encoder\YamlEncoder(), 3 => new \Symfony\Component\Serializer\Encoder\CsvEncoder()]);
