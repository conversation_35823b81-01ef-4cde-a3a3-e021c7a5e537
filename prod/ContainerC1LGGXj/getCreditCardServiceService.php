<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\CreditCard\Service\CreditCardService' shared autowired service.

return $this->services['Wizacha\\Marketplace\\CreditCard\\Service\\CreditCardService'] = new \Wizacha\Marketplace\CreditCard\Service\CreditCardService(($this->services['Wizacha\\Marketplace\\CreditCard\\Repository\\CreditCardRepository'] ?? $this->load('getCreditCardRepositoryService.php')), ($this->services['Wizacha\\Marketplace\\User\\UserService'] ?? $this->getUserServiceService()), ($this->services['Wizacha\\Marketplace\\Subscription\\SubscriptionService'] ?? $this->load('getSubscriptionServiceService.php')), ($this->services['marketplace.payment.payment_service'] ?? $this->load('getMarketplace_Payment_PaymentServiceService.php')));
