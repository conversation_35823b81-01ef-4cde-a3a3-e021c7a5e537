<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Division\Service\DivisionSettingsService' shared autowired service.

$a = ($this->services['marketplace.division.division_repository'] ?? $this->load('getMarketplace_Division_DivisionRepositoryService.php'));
$b = ($this->privates['Wizacha\\Marketplace\\Division\\DivisionSet'] ?? ($this->privates['Wizacha\\Marketplace\\Division\\DivisionSet'] = new \Wizacha\Marketplace\Division\DivisionSet()));

return $this->services['Wizacha\\Marketplace\\Division\\Service\\DivisionSettingsService'] = new \Wizacha\Marketplace\Division\Service\DivisionSettingsService(new \Wizacha\Marketplace\Division\MarketplaceDivisionSettings(($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()), $a, $b, $b), ($this->privates['Wizacha\\Marketplace\\Division\\CompanyDivisionSettings'] ?? $this->load('getCompanyDivisionSettingsService.php')), ($this->privates['Wizacha\\Marketplace\\Division\\ProductDivisionSettings'] ?? $this->load('getProductDivisionSettingsService.php')), $a, ($this->services['Wizacha\\Async\\Dispatcher'] ?? $this->load('getDispatcherService.php')), ($this->services['marketplace.catalog.company_service'] ?? $this->load('getMarketplace_Catalog_CompanyServiceService.php')));
