<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Storage\CsvExportStorageService' shared service.

return $this->services['Wizacha\\Storage\\CsvExportStorageService'] = ($this->privates['Wizacha\\Storage\\StorageFactory'] ?? $this->load('getStorageFactoryService.php'))->__invoke('csv_export', ['prefix' => 'csv_export', 'dir' => '/var', 'secured' => true]);
