<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'app.redis.default' shared service.

if ($lazyLoad) {
    return $this->privates['app.redis.default'] = $this->createProxy('RedisDecorator_62a2e93', function () {
        return \RedisDecorator_62a2e93::staticProxyConstructor(function (&$wrappedInstance, \ProxyManager\Proxy\LazyLoadingInterface $proxy) {
            $wrappedInstance = $this->load('getApp_Redis_DefaultService.php', false);

            $proxy->setProxyInitializer(null);

            return true;
        });
    });
}

return new \Wizacha\Cache\RedisDecorator(($this->services['snc_redis.public.default'] ?? $this->load('getSncRedis_Public_DefaultService.php')));
