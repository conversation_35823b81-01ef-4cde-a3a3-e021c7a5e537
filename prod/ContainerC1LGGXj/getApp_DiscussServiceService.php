<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'app.discuss_service' shared service.

$this->services['app.discuss_service'] = $instance = new \Wizacha\Discuss\DiscussService(($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()), ($this->services['Wizacha\\Marketplace\\User\\UserRepository'] ?? $this->getUserRepositoryService()), ($this->services['doctrine.orm.default_entity_manager'] ?? $this->getDoctrine_Orm_DefaultEntityManagerService()), ($this->privates['app.redis.default'] ?? $this->load('getApp_Redis_DefaultService.php')), ($this->services['Wizacha\\Marketplace\\Order\\OrderService'] ?? $this->load('getOrderServiceService.php')), ($this->services['Wizacha\\Marketplace\\Company\\CompanyService'] ?? $this->load('getCompanyServiceService.php')));

$instance->setRouter(($this->services['router'] ?? $this->getRouterService()));

return $instance;
