<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'fallback_controller' shared service.

return $this->services['fallback_controller'] = new \Wizacha\AppBundle\Controller\FallbackController(($this->services['templating'] ?? $this->load('getTemplatingService.php')));
