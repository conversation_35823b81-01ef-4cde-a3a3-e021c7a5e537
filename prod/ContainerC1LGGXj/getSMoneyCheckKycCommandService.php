<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'console.command.public_alias.Wizacha\AppBundle\Command\SMoneyCheckKycCommand' shared autowired service.

$this->services['console.command.public_alias.Wizacha\\AppBundle\\Command\\SMoneyCheckKycCommand'] = $instance = new \Wizacha\AppBundle\Command\SMoneyCheckKycCommand(($this->services['Wizacha\\Marketplace\\Payment\\Processor\\SMoney'] ?? $this->load('getSMoneyService.php')), ($this->services['Wizacha\\Marketplace\\Company\\CompanyService'] ?? $this->load('getCompanyServiceService.php')));

$instance->setLogger(($this->services['logger'] ?? $this->load('getLoggerService.php')));

return $instance;
