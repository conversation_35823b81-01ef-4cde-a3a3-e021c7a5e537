<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\ImageManager' shared service.

return $this->services['Wizacha\\ImageManager'] = new \Wizacha\ImageManager(($this->services['Wizacha\\Storage\\ImagesStorageService'] ?? $this->load('getImagesStorageServiceService.php')), false);
