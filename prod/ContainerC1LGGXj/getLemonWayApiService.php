<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Payment\LemonWay\LemonWayApi' shared service.

return $this->services['Wizacha\\Marketplace\\Payment\\LemonWay\\LemonWayApi'] = new \Wizacha\Marketplace\Payment\LemonWay\LemonWayApi(($this->services['Wizacha\\Marketplace\\Payment\\LemonWay\\LemonWayHttpClient'] ?? $this->load('getLemonWayHttpClientService.php')), $this->getEnv('LEMONWAY_API_WEBKIT_URL'));
