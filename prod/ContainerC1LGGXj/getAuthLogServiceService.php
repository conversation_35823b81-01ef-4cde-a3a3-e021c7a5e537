<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Component\AuthLog\AuthLogService' shared service.

return $this->services['Wizacha\\Component\\AuthLog\\AuthLogService'] = new \Wizacha\Component\AuthLog\AuthLogService(($this->services['Wizacha\\Component\\AuthLog\\AuthLogRepository'] ?? $this->load('getAuthLogRepositoryService.php')), ($this->services['doctrine.orm.default_entity_manager'] ?? $this->getDoctrine_Orm_DefaultEntityManagerService()));
