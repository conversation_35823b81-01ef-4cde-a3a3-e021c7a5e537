<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Shipping\ShippingService' shared autowired service.

return $this->services['Wizacha\\Marketplace\\Shipping\\ShippingService'] = new \Wizacha\Marketplace\Shipping\ShippingService(($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()));
