<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\Catalog\ProductController' shared service.

return $this->services['Wizacha\\AppBundle\\Controller\\Api\\Catalog\\ProductController'] = new \Wizacha\AppBundle\Controller\Api\Catalog\ProductController(true, ($this->services['Wizacha\\Marketplace\\Catalog\\Product\\ProductService'] ?? $this->load('getProductServiceService.php')), ($this->services['Wizacha\\Marketplace\\PIM\\Product\\ProductService'] ?? $this->load('getProductService2Service.php')));
