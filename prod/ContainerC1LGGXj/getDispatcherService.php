<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Async\Dispatcher' shared service.

if ($lazyLoad) {
    return $this->services['Wizacha\\Async\\Dispatcher'] = $this->createProxy('Dispatcher_7e72b24', function () {
        return \Dispatcher_7e72b24::staticProxyConstructor(function (&$wrappedInstance, \ProxyManager\Proxy\LazyLoadingInterface $proxy) {
            $wrappedInstance = $this->load('getDispatcherService.php', false);

            $proxy->setProxyInitializer(null);

            return true;
        });
    });
}

return new \Wizacha\Async\Dispatcher($this->parameters['marketplace.function_to_dispatch'], ($this->services['marketplace.queue_manager'] ?? $this->load('getMarketplace_QueueManagerService.php')), ($this->services['logger'] ?? $this->load('getLoggerService.php')), ($this->services['Wizacha\\Async\\Debouncer\\DebouncerService'] ?? $this->load('getDebouncerServiceService.php')), 'amqp');
