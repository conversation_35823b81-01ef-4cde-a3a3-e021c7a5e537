<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Search\Engine\AlgoliaSyncService' shared autowired service.

return $this->services['Wizacha\\Search\\Engine\\AlgoliaSyncService'] = new \Wizacha\Search\Engine\AlgoliaSyncService(($this->services['marketplace.search_engine'] ?? $this->load('getMarketplace_SearchEngineService.php')), ($this->services['Wizacha\\Search\\Record\\AlgoliaProductRecordFactory'] ?? $this->load('getAlgoliaProductRecordFactoryService.php')), ($this->services['Wizacha\\Marketplace\\Catalog\\Product\\ProductService'] ?? $this->load('getProductServiceService.php')));
