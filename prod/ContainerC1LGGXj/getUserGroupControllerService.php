<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Backend\UserGroupController' shared service.

return $this->services['Wizacha\\AppBundle\\Controller\\Backend\\UserGroupController'] = new \Wizacha\AppBundle\Controller\Backend\UserGroupController(($this->services['Wizacha\\Marketplace\\Group\\UserGroupService'] ?? $this->load('getUserGroupServiceService.php')), ($this->services['marketplace.import.csv_uploader'] ?? $this->load('getMarketplace_Import_CsvUploaderService.php')), ($this->services['marketplace.import.job_service'] ?? $this->load('getMarketplace_Import_JobServiceService.php')));
