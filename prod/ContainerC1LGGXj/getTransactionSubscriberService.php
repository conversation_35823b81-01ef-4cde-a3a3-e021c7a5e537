<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\ActionLogger\EventSubscribers\TransactionSubscriber' shared autowired service.

return $this->services['Wizacha\\ActionLogger\\EventSubscribers\\TransactionSubscriber'] = new \Wizacha\ActionLogger\EventSubscribers\TransactionSubscriber(($this->privates['Wizacha\\ActionLogger\\ActionLoggerFactory'] ?? $this->load('getActionLoggerFactoryService.php')));
