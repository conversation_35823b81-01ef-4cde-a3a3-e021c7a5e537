<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'templating.smarty' shared service.

return $this->services['templating.smarty'] = \Wizacha\Smarty\SmartyFactory::create(($this->services['wizacha.registry'] ?? $this->load('getWizacha_RegistryService.php')));
