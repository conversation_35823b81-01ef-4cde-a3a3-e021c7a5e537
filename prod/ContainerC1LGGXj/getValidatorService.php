<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'validator' shared service.

return $this->services['validator'] = ($this->privates['validator.builder'] ?? $this->load('getValidator_BuilderService.php'))->getValidator();
