<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'console.command.public_alias.Wizacha\AppBundle\Command\DeploySystemOptions' shared autowired service.

return $this->services['console.command.public_alias.Wizacha\\AppBundle\\Command\\DeploySystemOptions'] = new \Wizacha\AppBundle\Command\DeploySystemOptions('payment_frequency,commitment_period', ($this->services['Wizacha\\Marketplace\\PIM\\Option\\SystemOptionsRegistry'] ?? $this->load('getSystemOptionsRegistryService.php')));
