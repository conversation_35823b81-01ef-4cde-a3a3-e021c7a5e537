<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\PIM\Option\OptionService' shared autowired service.

return $this->services['Wizacha\\Marketplace\\PIM\\Option\\OptionService'] = new \Wizacha\Marketplace\PIM\Option\OptionService(($this->services['Wizacha\\Marketplace\\PIM\\Option\\OptionRepository'] ?? $this->load('getOptionRepositoryService.php')));
