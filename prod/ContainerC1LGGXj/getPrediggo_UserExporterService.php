<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'prediggo.user_exporter' shared service.

return $this->services['prediggo.user_exporter'] = new \Wizacha\Prediggo\UserExporter(($this->services['Wizacha\\Marketplace\\User\\UserService'] ?? $this->getUserServiceService()));
