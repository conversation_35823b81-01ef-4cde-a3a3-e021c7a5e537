<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Payment\HiPay\Wallet\HiPayWalletApi' shared service.

$a = new \Wizacha\Marketplace\Payment\HiPay\Wallet\HiPayWalletLogger(($this->services['monolog.logger.psp'] ?? $this->getMonolog_Logger_PspService()), ($this->services['serializer'] ?? $this->load('getSerializerService.php')));
$a->setDataAnonymizer(($this->privates['marketplace.payment.hipay_data_anonymizer'] ?? ($this->privates['marketplace.payment.hipay_data_anonymizer'] = new \Wizacha\Marketplace\Payment\Security\HiPayDataAnonymizer())));

return $this->services['Wizacha\\Marketplace\\Payment\\HiPay\\Wallet\\HiPayWalletApi'] = new \Wizacha\Marketplace\Payment\HiPay\Wallet\HiPayWalletApi(($this->services['marketplace.payment.hipay_cashout_client'] ?? $this->load('getMarketplace_Payment_HipayCashoutClientService.php')), 'ovhsas', 503, 'EUR', $a);
