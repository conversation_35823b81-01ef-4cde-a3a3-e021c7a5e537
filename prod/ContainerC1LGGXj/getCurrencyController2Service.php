<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Backend\CurrencyController' shared autowired service.

return $this->services['Wizacha\\AppBundle\\Controller\\Backend\\CurrencyController'] = new \Wizacha\AppBundle\Controller\Backend\CurrencyController(($this->services['Wizacha\\Marketplace\\Currency\\CurrencyService'] ?? $this->getCurrencyServiceService()));
