<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Component\Geocoding\Geocoding' shared service.

return $this->services['Wizacha\\Component\\Geocoding\\Geocoding'] = new \Wizacha\Component\Geocoding\Geocoding('8EH90U3Z4P', '36848569caa081ddff784016481ff319', ($this->services['logger'] ?? $this->load('getLoggerService.php')));
