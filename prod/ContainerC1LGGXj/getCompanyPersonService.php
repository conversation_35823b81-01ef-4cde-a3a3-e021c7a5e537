<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private '.errored..service_locator.5G6k4BF.Wizacha\Marketplace\CompanyPerson\CompanyPerson' shared service.

$this->throw('Cannot autowire service ".service_locator.5G6k4BF": it references class "Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson" but no such service exists.');
