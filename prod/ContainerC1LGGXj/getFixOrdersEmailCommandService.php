<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'Wizacha\AppBundle\Command\FixOrdersEmailCommand' shared autowired service.

$this->privates['Wizacha\\AppBundle\\Command\\FixOrdersEmailCommand'] = $instance = new \Wizacha\AppBundle\Command\FixOrdersEmailCommand(($this->services['Wizacha\\Marketplace\\Order\\OrderService'] ?? $this->load('getOrderServiceService.php')));

$instance->setName('orders:fix-email');

return $instance;
