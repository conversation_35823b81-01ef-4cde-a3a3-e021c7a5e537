<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'Wizacha\Marketplace\PIM\Product\Template\ProductInfoFormType' shared autowired service.

return $this->privates['Wizacha\\Marketplace\\PIM\\Product\\Template\\ProductInfoFormType'] = new \Wizacha\Marketplace\PIM\Product\Template\ProductInfoFormType(($this->services['Wizacha\\Marketplace\\PIM\\Tax\\TaxService'] ?? $this->load('getTaxServiceService.php')), ($this->services['Wizacha\\Marketplace\\PIM\\Product\\Template\\TemplateService'] ?? $this->load('getTemplateServiceService.php')), ($this->services['Wizacha\\Marketplace\\PIM\\TransactionMode\\TransactionModeService'] ?? ($this->services['Wizacha\\Marketplace\\PIM\\TransactionMode\\TransactionModeService'] = new \Wizacha\Marketplace\PIM\TransactionMode\TransactionModeService(true, true, true))), false, 'ean', true);
