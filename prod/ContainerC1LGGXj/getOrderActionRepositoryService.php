<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Order\OrderAction\Repository\OrderActionRepository' shared service.

return $this->services['Wizacha\\Marketplace\\Order\\OrderAction\\Repository\\OrderActionRepository'] = new \Wizacha\Marketplace\Order\OrderAction\Repository\OrderActionRepository(($this->services['doctrine'] ?? $this->getDoctrineService()), 'Wizacha\\Marketplace\\Order\\OrderAction\\Entity\\OrderAction');
