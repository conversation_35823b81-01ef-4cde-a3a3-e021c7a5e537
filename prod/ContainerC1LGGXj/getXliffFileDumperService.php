<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Symfony\Component\Translation\Dumper\XliffFileDumper' shared service.

return $this->services['Symfony\\Component\\Translation\\Dumper\\XliffFileDumper'] = new \Symfony\Component\Translation\Dumper\XliffFileDumper();
