<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\CheckoutController' shared autowired service.

return $this->services['Wizacha\\AppBundle\\Controller\\CheckoutController'] = new \Wizacha\AppBundle\Controller\CheckoutController(($this->services['Wizacha\\Marketplace\\Order\\OrderService'] ?? $this->load('getOrderServiceService.php')));
