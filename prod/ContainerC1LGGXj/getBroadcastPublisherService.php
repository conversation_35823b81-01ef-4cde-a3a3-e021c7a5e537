<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Messenger\BroadcastPublisher' shared autowired service.

return $this->services['Wizacha\\Marketplace\\Messenger\\BroadcastPublisher'] = new \Wizacha\Marketplace\Messenger\BroadcastPublisher(($this->services['message_bus'] ?? $this->load('getMessageBusService.php')), '', ($this->services['logger'] ?? $this->load('getLoggerService.php')), false);
