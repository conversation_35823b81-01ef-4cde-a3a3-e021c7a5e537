<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'Wizacha\Storage\StorageFactory' shared autowired service.

return $this->privates['Wizacha\\Storage\\StorageFactory'] = new \Wizacha\Storage\StorageFactory(\dirname(__DIR__, 4), 'ovh', new \Wizacha\Storage\Adapter\Aws(new \Aws\S3\S3Client(['version' => '2006-03-01', 'region' => 'eu-west-1', 'credentials' => ['key' => '', 'secret' => '']]), ''), new \Wizacha\Storage\Adapter\Local(\dirname(__DIR__, 4)), new \Wizacha\Storage\Adapter\Ovh(new \Aws\S3\S3Client(['version' => '2006-03-01', 'use_path_style_endpoint' => true, 'region' => 'GRA', 'credentials' => ['key' => '0e4c78f14d2248d2826bf468f427af43', 'secret' => '079024379f4c498ab16ebe0fba20d4ff'], 'endpoint' => 'https://storage.gra.cloud.ovh.net', 'S3' => ['version' => '2006-03-01', 'endpoint_url' => 'https://storage.gra.cloud.ovh.net', 'signature_version' => 's3v4', 'addressing_style' => 'virtual'], 'S3API' => ['endpoint_url' => 'https://storage.gra.cloud.ovh.net']]), 'private', 'public'), new \Wizacha\Storage\Adapter\Azure(($this->services['marketplace.azure.client'] ?? $this->load('getMarketplace_Azure_ClientService.php'))));
