<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\ImageController' shared autowired service.

return $this->services['Wizacha\\AppBundle\\Controller\\Api\\ImageController'] = new \Wizacha\AppBundle\Controller\Api\ImageController(($this->services['Wizacha\\ImageManager'] ?? $this->load('getImageManagerService.php')), ($this->services['Wizacha\\FeatureFlag\\FeatureFlagService'] ?? $this->getFeatureFlagServiceService()), ($this->services['session'] ?? $this->load('getSessionService.php')));
