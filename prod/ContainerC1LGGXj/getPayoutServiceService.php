<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Payment\Processor\PayoutService' shared autowired service.

return $this->services['Wizacha\\Marketplace\\Payment\\Processor\\PayoutService'] = new \Wizacha\Marketplace\Payment\Processor\PayoutService(new RewindableGenerator(function () {
    yield 0 => ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\NoPayment'] ?? $this->load('getNoPaymentService.php'));
    yield 1 => ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\MangoPay'] ?? $this->load('getMangoPayService.php'));
    yield 2 => ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\LemonWay'] ?? $this->load('getLemonWayService.php'));
    yield 3 => ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\SMoney'] ?? $this->load('getSMoneyService.php'));
    yield 4 => ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\Stripe'] ?? $this->load('getStripeService.php'));
    yield 5 => ($this->services['Wizacha\\Marketplace\\Payment\\Processor\\HiPay'] ?? $this->load('getHiPayService.php'));
}, 6), ($this->services['Wizacha\\Marketplace\\Company\\CompanyService'] ?? $this->load('getCompanyServiceService.php')), ($this->services['logger'] ?? $this->load('getLoggerService.php')));
