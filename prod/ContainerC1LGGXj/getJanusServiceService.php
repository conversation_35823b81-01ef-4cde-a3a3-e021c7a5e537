<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'Wizacha\Janus\JanusService' shared autowired service.

return $this->privates['Wizacha\\Janus\\JanusService'] = new \Wizacha\Janus\JanusService(new \Wizacha\Janus\JanusRepository(($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService())), ($this->services['kernel'] ?? $this->get('kernel', 1)));
