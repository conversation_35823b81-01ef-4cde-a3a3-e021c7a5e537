<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Favorite\FavoriteService' shared service.

return $this->services['Wizacha\\Marketplace\\Favorite\\FavoriteService'] = new \Wizacha\Marketplace\Favorite\FavoriteService(($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()), ($this->services['Wizacha\\Marketplace\\Catalog\\Product\\ProductService'] ?? $this->load('getProductServiceService.php')));
