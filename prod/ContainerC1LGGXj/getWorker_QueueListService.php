<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'worker.queue_list' shared service.

return $this->services['worker.queue_list'] = ($this->services['marketplace.queue_manager'] ?? $this->load('getMarketplace_QueueManagerService.php'))->getQueues();
