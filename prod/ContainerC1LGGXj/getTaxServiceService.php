<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\PIM\Tax\TaxService' shared autowired service.

return $this->services['Wizacha\\Marketplace\\PIM\\Tax\\TaxService'] = new \Wizacha\Marketplace\PIM\Tax\TaxService(($this->services['doctrine.orm.default_entity_manager'] ?? $this->getDoctrine_Orm_DefaultEntityManagerService()));
