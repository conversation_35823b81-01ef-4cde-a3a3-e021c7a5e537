<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Promotion\PromotionService' shared service.

return $this->services['Wizacha\\Marketplace\\Promotion\\PromotionService'] = new \Wizacha\Marketplace\Promotion\PromotionService(($this->services['marketplace.promotion.repository'] ?? $this->load('getMarketplace_Promotion_RepositoryService.php')), ($this->services['marketplace.promotion.promotion_usage.repository'] ?? $this->load('getMarketplace_Promotion_PromotionUsage_RepositoryService.php')), ($this->services['Broadway\\UuidGenerator\\Rfc4122\\Version4Generator'] ?? ($this->services['Broadway\\UuidGenerator\\Rfc4122\\Version4Generator'] = new \Broadway\UuidGenerator\Rfc4122\Version4Generator())), ($this->services['Wizacha\\Marketplace\\Promotion\\ProductPromotionApplier'] ?? ($this->services['Wizacha\\Marketplace\\Promotion\\ProductPromotionApplier'] = new \Wizacha\Marketplace\Promotion\ProductPromotionApplier())), ($this->services['marketplace.promotion.basket_applier'] ?? ($this->services['marketplace.promotion.basket_applier'] = new \Wizacha\Marketplace\Promotion\BasketPromotionApplier())), ($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()), ($this->services['marketplace.promotion.rule_engine'] ?? $this->load('getMarketplace_Promotion_RuleEngineService.php')), ($this->services['marketplace.promotion.price_calendar_factory'] ?? $this->load('getMarketplace_Promotion_PriceCalendarFactoryService.php')), ($this->services['marketplace.promotion.serializer'] ?? ($this->services['marketplace.promotion.serializer'] = new \Wizacha\Marketplace\Promotion\PromotionSerializer())), ($this->services['logger'] ?? $this->load('getLoggerService.php')), ($this->services['marketplace.marketplace_promotion.repository'] ?? $this->load('getMarketplace_MarketplacePromotion_RepositoryService.php')), ($this->privates['Wizacha\\AppBundle\\Controller\\Api\\Promotion\\BasketPromotionNormalizer'] ?? $this->load('getBasketPromotionNormalizerService.php')), ($this->services['Wizacha\\Marketplace\\Group\\UserGroupService'] ?? $this->load('getUserGroupServiceService.php')));
