<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Notification\ModerationNotifier' shared service.

return $this->services['Wizacha\\AppBundle\\Notification\\ModerationNotifier'] = new \Wizacha\AppBundle\Notification\ModerationNotifier(($this->services['app.mailer'] ?? $this->load('getApp_MailerService.php')), ($this->services['templating'] ?? $this->load('getTemplatingService.php')), ($this->services['logger'] ?? $this->load('getLoggerService.php')), ($this->services['Wizacha\\Marketplace\\AdminCompany'] ?? ($this->services['Wizacha\\Marketplace\\AdminCompany'] = new \Wizacha\Marketplace\AdminCompany())), ($this->services['Wizacha\\Marketplace\\User\\UserRepository'] ?? $this->getUserRepositoryService()), ($this->services['router'] ?? $this->getRouterService()), ($this->services['marketplace.monolog.level.mailer'] ?? $this->load('getMarketplace_Monolog_Level_MailerService.php')));
