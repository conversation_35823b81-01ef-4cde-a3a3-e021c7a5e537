<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\Order\HandDeliveryController' shared autowired service.

return $this->services['Wizacha\\AppBundle\\Controller\\Api\\Order\\HandDeliveryController'] = new \Wizacha\AppBundle\Controller\Api\Order\HandDeliveryController(($this->services['Wizacha\\Marketplace\\Order\\OrderService'] ?? $this->load('getOrderServiceService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\MarkAsShipped'] ?? $this->load('getMarkAsShippedService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\MarkAsDelivered'] ?? $this->load('getMarkAsDeliveredService.php')));
