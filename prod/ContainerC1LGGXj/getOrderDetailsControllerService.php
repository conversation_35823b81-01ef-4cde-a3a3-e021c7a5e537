<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\OrderDetailsController' shared autowired service.

return $this->services['Wizacha\\AppBundle\\Controller\\Api\\OrderDetailsController'] = new \Wizacha\AppBundle\Controller\Api\OrderDetailsController(($this->services['marketplace.order.order_details_service'] ?? $this->load('getMarketplace_Order_OrderDetailsServiceService.php')), ($this->services['Wizacha\\Marketplace\\Order\\OrderService'] ?? $this->load('getOrderServiceService.php')), ($this->services['security.authorization_checker'] ?? $this->load('getSecurity_AuthorizationCheckerService.php')));
