<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'RulerZ\Target\DoctrineDBAL\DoctrineDBAL' shared service.

return $this->services['RulerZ\\Target\\DoctrineDBAL\\DoctrineDBAL'] = new \RulerZ\Target\DoctrineDBAL\DoctrineDBAL();
