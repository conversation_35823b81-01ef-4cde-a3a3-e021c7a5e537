<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Component\Import\ImportService' shared service.

return $this->services['Wizacha\\Component\\Import\\ImportService'] = new \Wizacha\Component\Import\ImportService(($this->services['marketplace.backend_productimporter'] ?? ($this->services['marketplace.backend_productimporter'] = new \Wizacha\Exim\Import\Product\SimpleImporter())), ($this->services['Wizacha\\Async\\Dispatcher'] ?? $this->load('getDispatcherService.php')), new \Wizacha\Exim\Import\Inventory\Prices(), new \Wizacha\Exim\Import\Inventory\Quantities(), ($this->services['Wizacha\\Exim\\Import\\TranslationImporter'] ?? $this->load('getTranslationImporterService.php')));
