<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'console.command.public_alias.Wizacha\AppBundle\Command\MangoPayCheckKycCommand' shared autowired service.

$this->services['console.command.public_alias.Wizacha\\AppBundle\\Command\\MangoPayCheckKycCommand'] = $instance = new \Wizacha\AppBundle\Command\MangoPayCheckKycCommand(($this->services['Wizacha\\Marketplace\\Payment\\Processor\\MangoPay'] ?? $this->load('getMangoPayService.php')), ($this->services['Wizacha\\Marketplace\\Company\\CompanyService'] ?? $this->load('getCompanyServiceService.php')));

$instance->setLogger(($this->services['logger'] ?? $this->load('getLoggerService.php')));

return $instance;
