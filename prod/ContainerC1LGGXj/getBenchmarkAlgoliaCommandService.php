<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'Wizacha\AppBundle\Command\Benchmark\BenchmarkAlgoliaCommand' shared autowired service.

$this->privates['Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkAlgoliaCommand'] = $instance = new \Wizacha\AppBundle\Command\Benchmark\BenchmarkAlgoliaCommand(($this->services['marketplace.search_engine'] ?? $this->load('getMarketplace_SearchEngineService.php')));

$instance->setName('debug:benchmark:algolia');

return $instance;
