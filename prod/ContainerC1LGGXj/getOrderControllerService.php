<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\OrderController' shared autowired service.

$this->services['Wizacha\\AppBundle\\Controller\\Api\\OrderController'] = $instance = new \Wizacha\AppBundle\Controller\Api\OrderController(($this->services['Wizacha\\Marketplace\\Subscription\\SubscriptionRepository'] ?? $this->getSubscriptionRepositoryService()), 0, ($this->services['logger'] ?? $this->load('getLoggerService.php')), ($this->services['Wizacha\\Marketplace\\Transaction\\TransactionService'] ?? $this->load('getTransactionServiceService.php')), ($this->services['Wizacha\\Marketplace\\Order\\OrderService'] ?? $this->load('getOrderServiceService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Action\\MarkAsDelivered'] ?? $this->load('getMarkAsDeliveredService.php')), ($this->services['Wizacha\\Marketplace\\Order\\OrderAttachment\\Service\\OrderAttachmentService'] ?? $this->load('getOrderAttachmentServiceService.php')));

$instance->setUserService(($this->services['Wizacha\\Marketplace\\User\\UserService'] ?? $this->getUserServiceService()));

return $instance;
