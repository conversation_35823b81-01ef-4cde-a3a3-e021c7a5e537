<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'Wizacha\AppBundle\Command\UserUpdateCommand' shared autowired service.

$this->privates['Wizacha\\AppBundle\\Command\\UserUpdateCommand'] = $instance = new \Wizacha\AppBundle\Command\UserUpdateCommand(($this->services['router'] ?? $this->getRouterService()));

$instance->setName('user:update:csv');

return $instance;
