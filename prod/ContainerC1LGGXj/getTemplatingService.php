<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'templating' shared service.

@trigger_error('The "templating" service is deprecated since Symfony 4.3 and will be removed in 5.0.', E_USER_DEPRECATED);

$this->services['templating'] = $instance = new \Symfony\Bundle\FrameworkBundle\Templating\DelegatingEngine($this, []);

$instance->addEngine(new \Wizacha\Smarty\SmartyEngine(($this->services['templating.smarty'] ?? $this->load('getTemplating_SmartyService.php')), ($this->services['twig'] ?? $this->load('getTwigService.php'))));
$instance->addEngine($this->load('getTemplating_Engine_TwigService.php'));

return $instance;
