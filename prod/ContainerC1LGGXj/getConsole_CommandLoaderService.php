<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'console.command_loader' shared service.

return $this->services['console.command_loader'] = new \Symfony\Component\Console\CommandLoader\ContainerCommandLoader(new \Symfony\Component\DependencyInjection\Argument\ServiceLocator($this->getService, [
    'Wizacha\\AppBundle\\Command\\AmqpDebouncerBenchmarkCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\AmqpDebouncerBenchmarkCommand', 'getAmqpDebouncerBenchmarkCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\AmqpDebouncerStatsCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\AmqpDebouncerStatsCommand', 'getAmqpDebouncerStatsCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\AmqpDebouncerTriggerCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\AmqpDebouncerTriggerCommand', 'getAmqpDebouncerTriggerCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\BenchmarkFixtureCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\BenchmarkFixtureCommand', 'getBenchmarkFixtureCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkAlgoliaCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkAlgoliaCommand', 'getBenchmarkAlgoliaCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkDatabaseCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkDatabaseCommand', 'getBenchmarkDatabaseCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkQueueCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkQueueCommand', 'getBenchmarkQueueCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkRedisCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkRedisCommand', 'getBenchmarkRedisCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkStorageCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkStorageCommand', 'getBenchmarkStorageCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\CommissionsCheckConfigCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\CommissionsCheckConfigCommand', 'getCommissionsCheckConfigCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\CommissionsRecalculateCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\CommissionsRecalculateCommand', 'getCommissionsRecalculateCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\ConsumeAmqpMessagesCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\ConsumeAmqpMessagesCommand', 'getConsumeAmqpMessagesCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\DebugUnserializeCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\DebugUnserializeCommand', 'getDebugUnserializeCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\DeployOnceCompanyEmailSettingsCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\DeployOnceCompanyEmailSettingsCommand', 'getDeployOnceCompanyEmailSettingsCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\DeploySetTransactionOriginAndDestinationCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\DeploySetTransactionOriginAndDestinationCommand', 'getDeploySetTransactionOriginAndDestinationCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\DumpAnonymizerCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\DumpAnonymizerCommand', 'getDumpAnonymizerCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\EndOrdersWithdrawalPeriodCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\EndOrdersWithdrawalPeriodCommand', 'getEndOrdersWithdrawalPeriodCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\ExportCatalogCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\ExportCatalogCommand', 'getExportCatalogCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\ExportCompanyTokenCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\ExportCompanyTokenCommand', 'getExportCompanyTokenCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\FixCorruptedSerializedDataCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\FixCorruptedSerializedDataCommand', 'getFixCorruptedSerializedDataCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\FixDeclinationInventoryCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\FixDeclinationInventoryCommand', 'getFixDeclinationInventoryCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\FixDuplicateVariationsCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\FixDuplicateVariationsCommand', 'getFixDuplicateVariationsCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\FixDuplicatedMainImageLinksCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\FixDuplicatedMainImageLinksCommand', 'getFixDuplicatedMainImageLinksCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\FixOrderCommissionSyncCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\FixOrderCommissionSyncCommand', 'getFixOrderCommissionSyncCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\FixOrderDecryptPaymentInformation' => ['privates', 'Wizacha\\AppBundle\\Command\\FixOrderDecryptPaymentInformation', 'getFixOrderDecryptPaymentInformationService.php', true],
    'Wizacha\\AppBundle\\Command\\FixOrderInvoiceDataCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\FixOrderInvoiceDataCommand', 'getFixOrderInvoiceDataCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\FixOrdersEmailCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\FixOrdersEmailCommand', 'getFixOrdersEmailCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\HadesBackupCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\HadesBackupCommand', 'getHadesBackupCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\ImportMiraklCompanyStatusCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\ImportMiraklCompanyStatusCommand', 'getImportMiraklCompanyStatusCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\KPIPublishCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\KPIPublishCommand', 'getKPIPublishCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\LemonwayTransactionSyncCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\LemonwayTransactionSyncCommand', 'getLemonwayTransactionSyncCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\ListEximImportMessages' => ['privates', 'Wizacha\\AppBundle\\Command\\ListEximImportMessages', 'getListEximImportMessagesService.php', true],
    'Wizacha\\AppBundle\\Command\\MagentoPasswordHashUpdateCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\MagentoPasswordHashUpdateCommand', 'getMagentoPasswordHashUpdateCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\OrderAmountsRecalculateCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\OrderAmountsRecalculateCommand', 'getOrderAmountsRecalculateCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\OrderRefundOfflineMarkAsPaidCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\OrderRefundOfflineMarkAsPaidCommand', 'getOrderRefundOfflineMarkAsPaidCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\OrdersResetDispatchFundsCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\OrdersResetDispatchFundsCommand', 'getOrdersResetDispatchFundsCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\PrediggoExportCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\PrediggoExportCommand', 'getPrediggoExportCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\PriceTiers\\DeleteComplexPriceTiersCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\PriceTiers\\DeleteComplexPriceTiersCommand', 'getDeleteComplexPriceTiersCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\Readmodel\\CleanReadmodelCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\Readmodel\\CleanReadmodelCommand', 'getCleanReadmodelCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\RemovePromotionsOnIrrelevantOrdersCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\RemovePromotionsOnIrrelevantOrdersCommand', 'getRemovePromotionsOnIrrelevantOrdersCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\ResetProgressForProductInModeration' => ['privates', 'Wizacha\\AppBundle\\Command\\ResetProgressForProductInModeration', 'getResetProgressForProductInModerationService.php', true],
    'Wizacha\\AppBundle\\Command\\RgpdCompanyAnonymizerCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\RgpdCompanyAnonymizerCommand', 'getRgpdCompanyAnonymizerCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\RgpdUserAnonymizerCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\RgpdUserAnonymizerCommand', 'getRgpdUserAnonymizerCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\RollbackProcessedOrdersCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\RollbackProcessedOrdersCommand', 'getRollbackProcessedOrdersCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\TranslationCacheRefreshCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\TranslationCacheRefreshCommand', 'getTranslationCacheRefreshCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\UpdateCurrencyRatesCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\UpdateCurrencyRatesCommand', 'getUpdateCurrencyRatesCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\UserUpdateCommand' => ['privates', 'Wizacha\\AppBundle\\Command\\UserUpdateCommand', 'getUserUpdateCommandService.php', true],
    'Wizacha\\AppBundle\\Command\\Yavin\\UploadUsersToAuthGateway' => ['privates', 'Wizacha\\AppBundle\\Command\\Yavin\\UploadUsersToAuthGateway', 'getUploadUsersToAuthGatewayService.php', true],
    'console.command.about' => ['privates', 'console.command.about', 'getConsole_Command_AboutService.php', true],
    'console.command.assets_install' => ['privates', 'console.command.assets_install', 'getConsole_Command_AssetsInstallService.php', true],
    'console.command.cache_clear' => ['privates', 'console.command.cache_clear', 'getConsole_Command_CacheClearService.php', true],
    'console.command.cache_pool_clear' => ['privates', 'console.command.cache_pool_clear', 'getConsole_Command_CachePoolClearService.php', true],
    'console.command.cache_pool_delete' => ['privates', 'console.command.cache_pool_delete', 'getConsole_Command_CachePoolDeleteService.php', true],
    'console.command.cache_pool_list' => ['privates', 'console.command.cache_pool_list', 'getConsole_Command_CachePoolListService.php', true],
    'console.command.cache_pool_prune' => ['privates', 'console.command.cache_pool_prune', 'getConsole_Command_CachePoolPruneService.php', true],
    'console.command.cache_warmup' => ['privates', 'console.command.cache_warmup', 'getConsole_Command_CacheWarmupService.php', true],
    'console.command.config_debug' => ['privates', 'console.command.config_debug', 'getConsole_Command_ConfigDebugService.php', true],
    'console.command.config_dump_reference' => ['privates', 'console.command.config_dump_reference', 'getConsole_Command_ConfigDumpReferenceService.php', true],
    'console.command.container_debug' => ['privates', 'console.command.container_debug', 'getConsole_Command_ContainerDebugService.php', true],
    'console.command.container_lint' => ['privates', 'console.command.container_lint', 'getConsole_Command_ContainerLintService.php', true],
    'console.command.debug_autowiring' => ['privates', 'console.command.debug_autowiring', 'getConsole_Command_DebugAutowiringService.php', true],
    'console.command.event_dispatcher_debug' => ['privates', 'console.command.event_dispatcher_debug', 'getConsole_Command_EventDispatcherDebugService.php', true],
    'console.command.form_debug' => ['privates', 'console.command.form_debug', 'getConsole_Command_FormDebugService.php', true],
    'console.command.messenger_consume_messages' => ['privates', 'console.command.messenger_consume_messages', 'getConsole_Command_MessengerConsumeMessagesService.php', true],
    'console.command.messenger_debug' => ['privates', 'console.command.messenger_debug', 'getConsole_Command_MessengerDebugService.php', true],
    'console.command.messenger_setup_transports' => ['privates', 'console.command.messenger_setup_transports', 'getConsole_Command_MessengerSetupTransportsService.php', true],
    'console.command.messenger_stop_workers' => ['privates', 'console.command.messenger_stop_workers', 'getConsole_Command_MessengerStopWorkersService.php', true],
    'console.command.router_debug' => ['privates', 'console.command.router_debug', 'getConsole_Command_RouterDebugService.php', true],
    'console.command.router_match' => ['privates', 'console.command.router_match', 'getConsole_Command_RouterMatchService.php', true],
    'console.command.secrets_decrypt_to_local' => ['privates', 'console.command.secrets_decrypt_to_local', 'getConsole_Command_SecretsDecryptToLocalService.php', true],
    'console.command.secrets_encrypt_from_local' => ['privates', 'console.command.secrets_encrypt_from_local', 'getConsole_Command_SecretsEncryptFromLocalService.php', true],
    'console.command.secrets_generate_key' => ['privates', 'console.command.secrets_generate_key', 'getConsole_Command_SecretsGenerateKeyService.php', true],
    'console.command.secrets_list' => ['privates', 'console.command.secrets_list', 'getConsole_Command_SecretsListService.php', true],
    'console.command.secrets_remove' => ['privates', 'console.command.secrets_remove', 'getConsole_Command_SecretsRemoveService.php', true],
    'console.command.secrets_set' => ['privates', 'console.command.secrets_set', 'getConsole_Command_SecretsSetService.php', true],
    'console.command.translation_debug' => ['privates', 'console.command.translation_debug', 'getConsole_Command_TranslationDebugService.php', true],
    'console.command.translation_update' => ['privates', 'console.command.translation_update', 'getConsole_Command_TranslationUpdateService.php', true],
    'console.command.xliff_lint' => ['privates', 'console.command.xliff_lint', 'getConsole_Command_XliffLintService.php', true],
    'console.command.yaml_lint' => ['privates', 'console.command.yaml_lint', 'getConsole_Command_YamlLintService.php', true],
    'doctrine.cache_clear_metadata_command' => ['privates', 'doctrine.cache_clear_metadata_command', 'getDoctrine_CacheClearMetadataCommandService.php', true],
    'doctrine.cache_clear_query_cache_command' => ['privates', 'doctrine.cache_clear_query_cache_command', 'getDoctrine_CacheClearQueryCacheCommandService.php', true],
    'doctrine.cache_clear_result_command' => ['privates', 'doctrine.cache_clear_result_command', 'getDoctrine_CacheClearResultCommandService.php', true],
    'doctrine.cache_collection_region_command' => ['privates', 'doctrine.cache_collection_region_command', 'getDoctrine_CacheCollectionRegionCommandService.php', true],
    'doctrine.clear_entity_region_command' => ['privates', 'doctrine.clear_entity_region_command', 'getDoctrine_ClearEntityRegionCommandService.php', true],
    'doctrine.clear_query_region_command' => ['privates', 'doctrine.clear_query_region_command', 'getDoctrine_ClearQueryRegionCommandService.php', true],
    'doctrine.database_create_command' => ['privates', 'doctrine.database_create_command', 'getDoctrine_DatabaseCreateCommandService.php', true],
    'doctrine.database_drop_command' => ['privates', 'doctrine.database_drop_command', 'getDoctrine_DatabaseDropCommandService.php', true],
    'doctrine.database_import_command' => ['privates', 'doctrine.database_import_command', 'getDoctrine_DatabaseImportCommandService.php', true],
    'doctrine.ensure_production_settings_command' => ['privates', 'doctrine.ensure_production_settings_command', 'getDoctrine_EnsureProductionSettingsCommandService.php', true],
    'doctrine.generate_entities_command' => ['privates', 'doctrine.generate_entities_command', 'getDoctrine_GenerateEntitiesCommandService.php', true],
    'doctrine.mapping_convert_command' => ['privates', 'doctrine.mapping_convert_command', 'getDoctrine_MappingConvertCommandService.php', true],
    'doctrine.mapping_import_command' => ['privates', 'doctrine.mapping_import_command', 'getDoctrine_MappingImportCommandService.php', true],
    'doctrine.mapping_info_command' => ['privates', 'doctrine.mapping_info_command', 'getDoctrine_MappingInfoCommandService.php', true],
    'doctrine.query_dql_command' => ['privates', 'doctrine.query_dql_command', 'getDoctrine_QueryDqlCommandService.php', true],
    'doctrine.query_sql_command' => ['privates', 'doctrine.query_sql_command', 'getDoctrine_QuerySqlCommandService.php', true],
    'doctrine.schema_create_command' => ['privates', 'doctrine.schema_create_command', 'getDoctrine_SchemaCreateCommandService.php', true],
    'doctrine.schema_drop_command' => ['privates', 'doctrine.schema_drop_command', 'getDoctrine_SchemaDropCommandService.php', true],
    'doctrine.schema_update_command' => ['privates', 'doctrine.schema_update_command', 'getDoctrine_SchemaUpdateCommandService.php', true],
    'doctrine.schema_validate_command' => ['privates', 'doctrine.schema_validate_command', 'getDoctrine_SchemaValidateCommandService.php', true],
    'fos_js_routing.dump_command' => ['privates', 'fos_js_routing.dump_command', 'getFosJsRouting_DumpCommandService.php', true],
    'fos_js_routing.router_debug_exposed_command' => ['privates', 'fos_js_routing.router_debug_exposed_command', 'getFosJsRouting_RouterDebugExposedCommandService.php', true],
    'security.command.user_password_encoder' => ['privates', 'security.command.user_password_encoder', 'getSecurity_Command_UserPasswordEncoderService.php', true],
    'snc_redis.command.flush_all' => ['privates', 'snc_redis.command.flush_all', 'getSncRedis_Command_FlushAllService.php', true],
    'snc_redis.command.flush_db' => ['privates', 'snc_redis.command.flush_db', 'getSncRedis_Command_FlushDbService.php', true],
    'swiftmailer.command.debug' => ['privates', 'swiftmailer.command.debug', 'getSwiftmailer_Command_DebugService.php', true],
    'swiftmailer.command.new_email' => ['privates', 'swiftmailer.command.new_email', 'getSwiftmailer_Command_NewEmailService.php', true],
    'swiftmailer.command.send_email' => ['privates', 'swiftmailer.command.send_email', 'getSwiftmailer_Command_SendEmailService.php', true],
    'twig.command.debug' => ['privates', 'twig.command.debug', 'getTwig_Command_DebugService.php', true],
    'twig.command.lint' => ['privates', 'twig.command.lint', 'getTwig_Command_LintService.php', true],
], [
    'Wizacha\\AppBundle\\Command\\AmqpDebouncerBenchmarkCommand' => 'Wizacha\\AppBundle\\Command\\AmqpDebouncerBenchmarkCommand',
    'Wizacha\\AppBundle\\Command\\AmqpDebouncerStatsCommand' => 'Wizacha\\AppBundle\\Command\\AmqpDebouncerStatsCommand',
    'Wizacha\\AppBundle\\Command\\AmqpDebouncerTriggerCommand' => 'Wizacha\\AppBundle\\Command\\AmqpDebouncerTriggerCommand',
    'Wizacha\\AppBundle\\Command\\BenchmarkFixtureCommand' => 'Wizacha\\AppBundle\\Command\\BenchmarkFixtureCommand',
    'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkAlgoliaCommand' => 'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkAlgoliaCommand',
    'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkDatabaseCommand' => 'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkDatabaseCommand',
    'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkQueueCommand' => 'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkQueueCommand',
    'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkRedisCommand' => 'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkRedisCommand',
    'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkStorageCommand' => 'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkStorageCommand',
    'Wizacha\\AppBundle\\Command\\CommissionsCheckConfigCommand' => 'Wizacha\\AppBundle\\Command\\CommissionsCheckConfigCommand',
    'Wizacha\\AppBundle\\Command\\CommissionsRecalculateCommand' => 'Wizacha\\AppBundle\\Command\\CommissionsRecalculateCommand',
    'Wizacha\\AppBundle\\Command\\ConsumeAmqpMessagesCommand' => 'Wizacha\\AppBundle\\Command\\ConsumeAmqpMessagesCommand',
    'Wizacha\\AppBundle\\Command\\DebugUnserializeCommand' => 'Wizacha\\AppBundle\\Command\\DebugUnserializeCommand',
    'Wizacha\\AppBundle\\Command\\DeployOnceCompanyEmailSettingsCommand' => 'Wizacha\\AppBundle\\Command\\DeployOnceCompanyEmailSettingsCommand',
    'Wizacha\\AppBundle\\Command\\DeploySetTransactionOriginAndDestinationCommand' => 'Wizacha\\AppBundle\\Command\\DeploySetTransactionOriginAndDestinationCommand',
    'Wizacha\\AppBundle\\Command\\DumpAnonymizerCommand' => 'Wizacha\\AppBundle\\Command\\DumpAnonymizerCommand',
    'Wizacha\\AppBundle\\Command\\EndOrdersWithdrawalPeriodCommand' => 'Wizacha\\AppBundle\\Command\\EndOrdersWithdrawalPeriodCommand',
    'Wizacha\\AppBundle\\Command\\ExportCatalogCommand' => 'Wizacha\\AppBundle\\Command\\ExportCatalogCommand',
    'Wizacha\\AppBundle\\Command\\ExportCompanyTokenCommand' => 'Wizacha\\AppBundle\\Command\\ExportCompanyTokenCommand',
    'Wizacha\\AppBundle\\Command\\FixCorruptedSerializedDataCommand' => 'Wizacha\\AppBundle\\Command\\FixCorruptedSerializedDataCommand',
    'Wizacha\\AppBundle\\Command\\FixDeclinationInventoryCommand' => 'Wizacha\\AppBundle\\Command\\FixDeclinationInventoryCommand',
    'Wizacha\\AppBundle\\Command\\FixDuplicateVariationsCommand' => 'Wizacha\\AppBundle\\Command\\FixDuplicateVariationsCommand',
    'Wizacha\\AppBundle\\Command\\FixDuplicatedMainImageLinksCommand' => 'Wizacha\\AppBundle\\Command\\FixDuplicatedMainImageLinksCommand',
    'Wizacha\\AppBundle\\Command\\FixOrderCommissionSyncCommand' => 'Wizacha\\AppBundle\\Command\\FixOrderCommissionSyncCommand',
    'Wizacha\\AppBundle\\Command\\FixOrderDecryptPaymentInformation' => 'Wizacha\\AppBundle\\Command\\FixOrderDecryptPaymentInformation',
    'Wizacha\\AppBundle\\Command\\FixOrderInvoiceDataCommand' => 'Wizacha\\AppBundle\\Command\\FixOrderInvoiceDataCommand',
    'Wizacha\\AppBundle\\Command\\FixOrdersEmailCommand' => 'Wizacha\\AppBundle\\Command\\FixOrdersEmailCommand',
    'Wizacha\\AppBundle\\Command\\HadesBackupCommand' => 'Wizacha\\AppBundle\\Command\\HadesBackupCommand',
    'Wizacha\\AppBundle\\Command\\ImportMiraklCompanyStatusCommand' => 'Wizacha\\AppBundle\\Command\\ImportMiraklCompanyStatusCommand',
    'Wizacha\\AppBundle\\Command\\KPIPublishCommand' => 'Wizacha\\AppBundle\\Command\\KPIPublishCommand',
    'Wizacha\\AppBundle\\Command\\LemonwayTransactionSyncCommand' => 'Wizacha\\AppBundle\\Command\\LemonwayTransactionSyncCommand',
    'Wizacha\\AppBundle\\Command\\ListEximImportMessages' => 'Wizacha\\AppBundle\\Command\\ListEximImportMessages',
    'Wizacha\\AppBundle\\Command\\MagentoPasswordHashUpdateCommand' => 'Wizacha\\AppBundle\\Command\\MagentoPasswordHashUpdateCommand',
    'Wizacha\\AppBundle\\Command\\OrderAmountsRecalculateCommand' => 'Wizacha\\AppBundle\\Command\\OrderAmountsRecalculateCommand',
    'Wizacha\\AppBundle\\Command\\OrderRefundOfflineMarkAsPaidCommand' => 'Wizacha\\AppBundle\\Command\\OrderRefundOfflineMarkAsPaidCommand',
    'Wizacha\\AppBundle\\Command\\OrdersResetDispatchFundsCommand' => 'Wizacha\\AppBundle\\Command\\OrdersResetDispatchFundsCommand',
    'Wizacha\\AppBundle\\Command\\PrediggoExportCommand' => 'Wizacha\\AppBundle\\Command\\PrediggoExportCommand',
    'Wizacha\\AppBundle\\Command\\PriceTiers\\DeleteComplexPriceTiersCommand' => 'Wizacha\\AppBundle\\Command\\PriceTiers\\DeleteComplexPriceTiersCommand',
    'Wizacha\\AppBundle\\Command\\Readmodel\\CleanReadmodelCommand' => 'Wizacha\\AppBundle\\Command\\Readmodel\\CleanReadmodelCommand',
    'Wizacha\\AppBundle\\Command\\RemovePromotionsOnIrrelevantOrdersCommand' => 'Wizacha\\AppBundle\\Command\\RemovePromotionsOnIrrelevantOrdersCommand',
    'Wizacha\\AppBundle\\Command\\ResetProgressForProductInModeration' => 'Wizacha\\AppBundle\\Command\\ResetProgressForProductInModeration',
    'Wizacha\\AppBundle\\Command\\RgpdCompanyAnonymizerCommand' => 'Wizacha\\AppBundle\\Command\\RgpdCompanyAnonymizerCommand',
    'Wizacha\\AppBundle\\Command\\RgpdUserAnonymizerCommand' => 'Wizacha\\AppBundle\\Command\\RgpdUserAnonymizerCommand',
    'Wizacha\\AppBundle\\Command\\RollbackProcessedOrdersCommand' => 'Wizacha\\AppBundle\\Command\\RollbackProcessedOrdersCommand',
    'Wizacha\\AppBundle\\Command\\TranslationCacheRefreshCommand' => 'Wizacha\\AppBundle\\Command\\TranslationCacheRefreshCommand',
    'Wizacha\\AppBundle\\Command\\UpdateCurrencyRatesCommand' => 'Wizacha\\AppBundle\\Command\\UpdateCurrencyRatesCommand',
    'Wizacha\\AppBundle\\Command\\UserUpdateCommand' => 'Wizacha\\AppBundle\\Command\\UserUpdateCommand',
    'Wizacha\\AppBundle\\Command\\Yavin\\UploadUsersToAuthGateway' => 'Wizacha\\AppBundle\\Command\\Yavin\\UploadUsersToAuthGateway',
    'console.command.about' => 'Symfony\\Bundle\\FrameworkBundle\\Command\\AboutCommand',
    'console.command.assets_install' => 'Symfony\\Bundle\\FrameworkBundle\\Command\\AssetsInstallCommand',
    'console.command.cache_clear' => 'Symfony\\Bundle\\FrameworkBundle\\Command\\CacheClearCommand',
    'console.command.cache_pool_clear' => 'Symfony\\Bundle\\FrameworkBundle\\Command\\CachePoolClearCommand',
    'console.command.cache_pool_delete' => 'Symfony\\Bundle\\FrameworkBundle\\Command\\CachePoolDeleteCommand',
    'console.command.cache_pool_list' => 'Symfony\\Bundle\\FrameworkBundle\\Command\\CachePoolListCommand',
    'console.command.cache_pool_prune' => 'Symfony\\Bundle\\FrameworkBundle\\Command\\CachePoolPruneCommand',
    'console.command.cache_warmup' => 'Symfony\\Bundle\\FrameworkBundle\\Command\\CacheWarmupCommand',
    'console.command.config_debug' => 'Symfony\\Bundle\\FrameworkBundle\\Command\\ConfigDebugCommand',
    'console.command.config_dump_reference' => 'Symfony\\Bundle\\FrameworkBundle\\Command\\ConfigDumpReferenceCommand',
    'console.command.container_debug' => 'Symfony\\Bundle\\FrameworkBundle\\Command\\ContainerDebugCommand',
    'console.command.container_lint' => 'Symfony\\Bundle\\FrameworkBundle\\Command\\ContainerLintCommand',
    'console.command.debug_autowiring' => 'Symfony\\Bundle\\FrameworkBundle\\Command\\DebugAutowiringCommand',
    'console.command.event_dispatcher_debug' => 'Symfony\\Bundle\\FrameworkBundle\\Command\\EventDispatcherDebugCommand',
    'console.command.form_debug' => 'Symfony\\Component\\Form\\Command\\DebugCommand',
    'console.command.messenger_consume_messages' => 'Symfony\\Component\\Messenger\\Command\\ConsumeMessagesCommand',
    'console.command.messenger_debug' => 'Symfony\\Component\\Messenger\\Command\\DebugCommand',
    'console.command.messenger_setup_transports' => 'Symfony\\Component\\Messenger\\Command\\SetupTransportsCommand',
    'console.command.messenger_stop_workers' => 'Symfony\\Component\\Messenger\\Command\\StopWorkersCommand',
    'console.command.router_debug' => 'Symfony\\Bundle\\FrameworkBundle\\Command\\RouterDebugCommand',
    'console.command.router_match' => 'Symfony\\Bundle\\FrameworkBundle\\Command\\RouterMatchCommand',
    'console.command.secrets_decrypt_to_local' => 'Symfony\\Bundle\\FrameworkBundle\\Command\\SecretsDecryptToLocalCommand',
    'console.command.secrets_encrypt_from_local' => 'Symfony\\Bundle\\FrameworkBundle\\Command\\SecretsEncryptFromLocalCommand',
    'console.command.secrets_generate_key' => 'Symfony\\Bundle\\FrameworkBundle\\Command\\SecretsGenerateKeysCommand',
    'console.command.secrets_list' => 'Symfony\\Bundle\\FrameworkBundle\\Command\\SecretsListCommand',
    'console.command.secrets_remove' => 'Symfony\\Bundle\\FrameworkBundle\\Command\\SecretsRemoveCommand',
    'console.command.secrets_set' => 'Symfony\\Bundle\\FrameworkBundle\\Command\\SecretsSetCommand',
    'console.command.translation_debug' => 'Symfony\\Bundle\\FrameworkBundle\\Command\\TranslationDebugCommand',
    'console.command.translation_update' => 'Symfony\\Bundle\\FrameworkBundle\\Command\\TranslationUpdateCommand',
    'console.command.xliff_lint' => 'Symfony\\Bundle\\FrameworkBundle\\Command\\XliffLintCommand',
    'console.command.yaml_lint' => 'Symfony\\Bundle\\FrameworkBundle\\Command\\YamlLintCommand',
    'doctrine.cache_clear_metadata_command' => 'Doctrine\\Bundle\\DoctrineBundle\\Command\\Proxy\\ClearMetadataCacheDoctrineCommand',
    'doctrine.cache_clear_query_cache_command' => 'Doctrine\\Bundle\\DoctrineBundle\\Command\\Proxy\\ClearQueryCacheDoctrineCommand',
    'doctrine.cache_clear_result_command' => 'Doctrine\\Bundle\\DoctrineBundle\\Command\\Proxy\\ClearResultCacheDoctrineCommand',
    'doctrine.cache_collection_region_command' => 'Doctrine\\Bundle\\DoctrineBundle\\Command\\Proxy\\CollectionRegionDoctrineCommand',
    'doctrine.clear_entity_region_command' => 'Doctrine\\Bundle\\DoctrineBundle\\Command\\Proxy\\EntityRegionCacheDoctrineCommand',
    'doctrine.clear_query_region_command' => 'Doctrine\\Bundle\\DoctrineBundle\\Command\\Proxy\\QueryRegionCacheDoctrineCommand',
    'doctrine.database_create_command' => 'Doctrine\\Bundle\\DoctrineBundle\\Command\\CreateDatabaseDoctrineCommand',
    'doctrine.database_drop_command' => 'Doctrine\\Bundle\\DoctrineBundle\\Command\\DropDatabaseDoctrineCommand',
    'doctrine.database_import_command' => 'Doctrine\\Bundle\\DoctrineBundle\\Command\\Proxy\\ImportDoctrineCommand',
    'doctrine.ensure_production_settings_command' => 'Doctrine\\Bundle\\DoctrineBundle\\Command\\Proxy\\EnsureProductionSettingsDoctrineCommand',
    'doctrine.generate_entities_command' => 'Doctrine\\Bundle\\DoctrineBundle\\Command\\GenerateEntitiesDoctrineCommand',
    'doctrine.mapping_convert_command' => 'Doctrine\\Bundle\\DoctrineBundle\\Command\\Proxy\\ConvertMappingDoctrineCommand',
    'doctrine.mapping_import_command' => 'Doctrine\\Bundle\\DoctrineBundle\\Command\\ImportMappingDoctrineCommand',
    'doctrine.mapping_info_command' => 'Doctrine\\Bundle\\DoctrineBundle\\Command\\Proxy\\InfoDoctrineCommand',
    'doctrine.query_dql_command' => 'Doctrine\\Bundle\\DoctrineBundle\\Command\\Proxy\\RunDqlDoctrineCommand',
    'doctrine.query_sql_command' => 'Doctrine\\Bundle\\DoctrineBundle\\Command\\Proxy\\RunSqlDoctrineCommand',
    'doctrine.schema_create_command' => 'Doctrine\\Bundle\\DoctrineBundle\\Command\\Proxy\\CreateSchemaDoctrineCommand',
    'doctrine.schema_drop_command' => 'Doctrine\\Bundle\\DoctrineBundle\\Command\\Proxy\\DropSchemaDoctrineCommand',
    'doctrine.schema_update_command' => 'Doctrine\\Bundle\\DoctrineBundle\\Command\\Proxy\\UpdateSchemaDoctrineCommand',
    'doctrine.schema_validate_command' => 'Doctrine\\Bundle\\DoctrineBundle\\Command\\Proxy\\ValidateSchemaCommand',
    'fos_js_routing.dump_command' => 'FOS\\JsRoutingBundle\\Command\\DumpCommand',
    'fos_js_routing.router_debug_exposed_command' => 'FOS\\JsRoutingBundle\\Command\\RouterDebugExposedCommand',
    'security.command.user_password_encoder' => 'Symfony\\Bundle\\SecurityBundle\\Command\\UserPasswordEncoderCommand',
    'snc_redis.command.flush_all' => 'Snc\\RedisBundle\\Command\\RedisFlushallCommand',
    'snc_redis.command.flush_db' => 'Snc\\RedisBundle\\Command\\RedisFlushdbCommand',
    'swiftmailer.command.debug' => 'Symfony\\Bundle\\SwiftmailerBundle\\Command\\DebugCommand',
    'swiftmailer.command.new_email' => 'Symfony\\Bundle\\SwiftmailerBundle\\Command\\NewEmailCommand',
    'swiftmailer.command.send_email' => 'Symfony\\Bundle\\SwiftmailerBundle\\Command\\SendEmailCommand',
    'twig.command.debug' => 'Symfony\\Bridge\\Twig\\Command\\DebugCommand',
    'twig.command.lint' => 'Symfony\\Bundle\\TwigBundle\\Command\\LintCommand',
]), ['translation:cache:refresh' => 'Wizacha\\AppBundle\\Command\\TranslationCacheRefreshCommand', 'migration:magento:passwords' => 'Wizacha\\AppBundle\\Command\\MagentoPasswordHashUpdateCommand', 'import:mirakl:company-status' => 'Wizacha\\AppBundle\\Command\\ImportMiraklCompanyStatusCommand', 'rgpd:anonymize-user' => 'Wizacha\\AppBundle\\Command\\RgpdUserAnonymizerCommand', 'rgpd:anonymize-company' => 'Wizacha\\AppBundle\\Command\\RgpdCompanyAnonymizerCommand', 'readmodel:orphan:clean' => 'Wizacha\\AppBundle\\Command\\Readmodel\\CleanReadmodelCommand', 'fix:duplicated-variations' => 'Wizacha\\AppBundle\\Command\\FixDuplicateVariationsCommand', 'deploy:command:company_email_settings' => 'Wizacha\\AppBundle\\Command\\DeployOnceCompanyEmailSettingsCommand', 'products:price-tiers:reset' => 'Wizacha\\AppBundle\\Command\\PriceTiers\\DeleteComplexPriceTiersCommand', 'orders:invoice-vat' => 'Wizacha\\AppBundle\\Command\\FixOrderInvoiceDataCommand', 'commissions:recalculate' => 'Wizacha\\AppBundle\\Command\\CommissionsRecalculateCommand', 'company:token:export' => 'Wizacha\\AppBundle\\Command\\ExportCompanyTokenCommand', 'commissions:check:config' => 'Wizacha\\AppBundle\\Command\\CommissionsCheckConfigCommand', 'amqp:debouncer:trigger' => 'Wizacha\\AppBundle\\Command\\AmqpDebouncerTriggerCommand', 'amqp:debouncer:stats' => 'Wizacha\\AppBundle\\Command\\AmqpDebouncerStatsCommand', 'amqp:debouncer:benchmark' => 'Wizacha\\AppBundle\\Command\\AmqpDebouncerBenchmarkCommand', 'amqp:consume' => 'Wizacha\\AppBundle\\Command\\ConsumeAmqpMessagesCommand', 'orders:fix-email' => 'Wizacha\\AppBundle\\Command\\FixOrdersEmailCommand', 'user:update:csv' => 'Wizacha\\AppBundle\\Command\\UserUpdateCommand', 'prediggo:export' => 'Wizacha\\AppBundle\\Command\\PrediggoExportCommand', 'currency:rates:update' => 'Wizacha\\AppBundle\\Command\\UpdateCurrencyRatesCommand', 'products:moderation:reset-progress' => 'Wizacha\\AppBundle\\Command\\ResetProgressForProductInModeration', 'orders:end-withdrawal-period' => 'Wizacha\\AppBundle\\Command\\EndOrdersWithdrawalPeriodCommand', 'export:catalog' => 'Wizacha\\AppBundle\\Command\\ExportCatalogCommand', 'fix:products:declinations' => 'Wizacha\\AppBundle\\Command\\FixDeclinationInventoryCommand', 'orders:remove:irrelevant-promotions' => 'Wizacha\\AppBundle\\Command\\RemovePromotionsOnIrrelevantOrdersCommand', 'orders:reset:dispatch-funds' => 'Wizacha\\AppBundle\\Command\\OrdersResetDispatchFundsCommand', 'orders:rollback-processed-orders' => 'Wizacha\\AppBundle\\Command\\RollbackProcessedOrdersCommand', 'orders:refund:offline-mark-as-paid' => 'Wizacha\\AppBundle\\Command\\OrderRefundOfflineMarkAsPaidCommand', 'orders:decrypt-payment-information' => 'Wizacha\\AppBundle\\Command\\FixOrderDecryptPaymentInformation', 'orders:commission:sync' => 'Wizacha\\AppBundle\\Command\\FixOrderCommissionSyncCommand', 'debug:import-messages' => 'Wizacha\\AppBundle\\Command\\ListEximImportMessages', 'debug:benchmark:database' => 'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkDatabaseCommand', 'debug:benchmark:redis' => 'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkRedisCommand', 'debug:benchmark:queue' => 'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkQueueCommand', 'debug:benchmark:algolia' => 'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkAlgoliaCommand', 'debug:benchmark:storage' => 'Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkStorageCommand', 'lemonway:transaction:sync' => 'Wizacha\\AppBundle\\Command\\LemonwayTransactionSyncCommand', 'kpi:publish' => 'Wizacha\\AppBundle\\Command\\KPIPublishCommand', 'debug:benchmark:fixtures' => 'Wizacha\\AppBundle\\Command\\BenchmarkFixtureCommand', 'hades:backup' => 'Wizacha\\AppBundle\\Command\\HadesBackupCommand', 'debug:unserialize' => 'Wizacha\\AppBundle\\Command\\DebugUnserializeCommand', 'fix:transactions:set-origin-and-destination' => 'Wizacha\\AppBundle\\Command\\DeploySetTransactionOriginAndDestinationCommand', 'fix:duplicated-main-image-links' => 'Wizacha\\AppBundle\\Command\\FixDuplicatedMainImageLinksCommand', 'user:init-auth-gateway' => 'Wizacha\\AppBundle\\Command\\Yavin\\UploadUsersToAuthGateway', 'rgpd:anonymize:dump' => 'Wizacha\\AppBundle\\Command\\DumpAnonymizerCommand', 'orders:recalculate' => 'Wizacha\\AppBundle\\Command\\OrderAmountsRecalculateCommand', 'fix:corrupted-serialization' => 'Wizacha\\AppBundle\\Command\\FixCorruptedSerializedDataCommand', 'about' => 'console.command.about', 'assets:install' => 'console.command.assets_install', 'cache:clear' => 'console.command.cache_clear', 'cache:pool:clear' => 'console.command.cache_pool_clear', 'cache:pool:prune' => 'console.command.cache_pool_prune', 'cache:pool:delete' => 'console.command.cache_pool_delete', 'cache:pool:list' => 'console.command.cache_pool_list', 'cache:warmup' => 'console.command.cache_warmup', 'debug:config' => 'console.command.config_debug', 'config:dump-reference' => 'console.command.config_dump_reference', 'debug:container' => 'console.command.container_debug', 'lint:container' => 'console.command.container_lint', 'debug:autowiring' => 'console.command.debug_autowiring', 'debug:event-dispatcher' => 'console.command.event_dispatcher_debug', 'messenger:consume' => 'console.command.messenger_consume_messages', 'messenger:consume-messages' => 'console.command.messenger_consume_messages', 'messenger:setup-transports' => 'console.command.messenger_setup_transports', 'debug:messenger' => 'console.command.messenger_debug', 'messenger:stop-workers' => 'console.command.messenger_stop_workers', 'debug:router' => 'console.command.router_debug', 'router:match' => 'console.command.router_match', 'debug:translation' => 'console.command.translation_debug', 'translation:update' => 'console.command.translation_update', 'lint:xliff' => 'console.command.xliff_lint', 'lint:yaml' => 'console.command.yaml_lint', 'debug:form' => 'console.command.form_debug', 'secrets:set' => 'console.command.secrets_set', 'secrets:remove' => 'console.command.secrets_remove', 'secrets:generate-keys' => 'console.command.secrets_generate_key', 'secrets:list' => 'console.command.secrets_list', 'secrets:decrypt-to-local' => 'console.command.secrets_decrypt_to_local', 'secrets:encrypt-from-local' => 'console.command.secrets_encrypt_from_local', 'security:encode-password' => 'security.command.user_password_encoder', 'doctrine:database:create' => 'doctrine.database_create_command', 'doctrine:database:drop' => 'doctrine.database_drop_command', 'doctrine:database:import' => 'doctrine.database_import_command', 'doctrine:generate:entities' => 'doctrine.generate_entities_command', 'doctrine:query:sql' => 'doctrine.query_sql_command', 'doctrine:cache:clear-metadata' => 'doctrine.cache_clear_metadata_command', 'doctrine:cache:clear-query' => 'doctrine.cache_clear_query_cache_command', 'doctrine:cache:clear-result' => 'doctrine.cache_clear_result_command', 'doctrine:cache:clear-collection-region' => 'doctrine.cache_collection_region_command', 'doctrine:mapping:convert' => 'doctrine.mapping_convert_command', 'doctrine:schema:create' => 'doctrine.schema_create_command', 'doctrine:schema:drop' => 'doctrine.schema_drop_command', 'doctrine:ensure-production-settings' => 'doctrine.ensure_production_settings_command', 'doctrine:cache:clear-entity-region' => 'doctrine.clear_entity_region_command', 'doctrine:mapping:info' => 'doctrine.mapping_info_command', 'doctrine:cache:clear-query-region' => 'doctrine.clear_query_region_command', 'doctrine:query:dql' => 'doctrine.query_dql_command', 'doctrine:schema:update' => 'doctrine.schema_update_command', 'doctrine:schema:validate' => 'doctrine.schema_validate_command', 'doctrine:mapping:import' => 'doctrine.mapping_import_command', 'redis:flushall' => 'snc_redis.command.flush_all', 'redis:flushdb' => 'snc_redis.command.flush_db', 'debug:twig' => 'twig.command.debug', 'lint:twig' => 'twig.command.lint', 'fos:js-routing:dump' => 'fos_js_routing.dump_command', 'fos:js-routing:debug' => 'fos_js_routing.router_debug_exposed_command', 'debug:swiftmailer' => 'swiftmailer.command.debug', 'swiftmailer:email:send' => 'swiftmailer.command.new_email', 'swiftmailer:spool:send' => 'swiftmailer.command.send_email']);
