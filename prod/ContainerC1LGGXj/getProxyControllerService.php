<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\ProxyController' shared autowired service.

return $this->services['Wizacha\\AppBundle\\Controller\\ProxyController'] = new \Wizacha\AppBundle\Controller\ProxyController(($this->services['logger'] ?? $this->load('getLoggerService.php')), ($this->services['Wizacha\\Marketplace\\User\\UserService'] ?? $this->getUserServiceService()), ($this->services['Wizacha\\Component\\Http\\Client\\GuzzleYavinClient'] ?? $this->load('getGuzzleYavinClientService.php')), new \Wizacha\Component\Http\Factory\ProxyRequestFactory());
