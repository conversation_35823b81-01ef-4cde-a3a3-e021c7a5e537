<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Order\CreateOrder' shared autowired service.

$a = ($this->services['Wizacha\\Marketplace\\Basket\\Checkout'] ?? $this->load('getCheckoutService.php'));

if (isset($this->services['Wizacha\\Marketplace\\Order\\CreateOrder'])) {
    return $this->services['Wizacha\\Marketplace\\Order\\CreateOrder'];
}
$b = ($this->privates['Wizacha\\Marketplace\\Payment\\Processor\\ProcessorService'] ?? $this->load('getProcessorServiceService.php'));

if (isset($this->services['Wizacha\\Marketplace\\Order\\CreateOrder'])) {
    return $this->services['Wizacha\\Marketplace\\Order\\CreateOrder'];
}

return $this->services['Wizacha\\Marketplace\\Order\\CreateOrder'] = new \Wizacha\Marketplace\Order\CreateOrder(($this->services['Wizacha\\Marketplace\\Basket\\BasketService'] ?? $this->load('getBasketServiceService.php')), $a, $b, ($this->services['logger'] ?? $this->load('getLoggerService.php')));
