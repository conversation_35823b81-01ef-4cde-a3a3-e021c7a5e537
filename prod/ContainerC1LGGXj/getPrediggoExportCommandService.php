<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'Wizacha\AppBundle\Command\PrediggoExportCommand' shared autowired service.

$this->privates['Wizacha\\AppBundle\\Command\\PrediggoExportCommand'] = $instance = new \Wizacha\AppBundle\Command\PrediggoExportCommand(($this->services['Wizacha\\Prediggo\\Exporter'] ?? $this->load('getExporterService.php')), false);

$instance->setName('prediggo:export');

return $instance;
