<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'RulerZ\Target\Native\Native' shared service.

$this->services['RulerZ\\Target\\Native\\Native'] = $instance = new \RulerZ\Target\Native\Native();

$instance->defineOperator('intersects', new \Wizacha\Marketplace\Promotion\Rule\Operator\ArrayIntersects());

return $instance;
