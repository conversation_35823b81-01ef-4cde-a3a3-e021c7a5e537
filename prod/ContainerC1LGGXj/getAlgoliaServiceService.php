<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Search\Engine\AlgoliaService' shared service.

return $this->services['Wizacha\\Search\\Engine\\AlgoliaService'] = new \Wizacha\Search\Engine\AlgoliaService(($this->services['marketplace.search_engine'] ?? $this->load('getMarketplace_SearchEngineService.php')), ($this->services['marketplace.search.product_index'] ?? $this->load('getMarketplace_Search_ProductIndexService.php')), true);
