<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\PIM\Stock\StockService' shared service.

return $this->services['Wizacha\\Marketplace\\PIM\\Stock\\StockService'] = new \Wizacha\Marketplace\PIM\Stock\StockService(($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()), ($this->privates['app.redis.default'] ?? $this->load('getApp_Redis_DefaultService.php')), 'default.', 900);
