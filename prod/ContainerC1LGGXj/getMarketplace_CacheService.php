<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'marketplace.cache' shared service.

return $this->services['marketplace.cache'] = ($this->services['wizacha.registry'] ?? $this->load('getWizacha_RegistryService.php'))->cache();
