<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'console.command.public_alias.Wizacha\AppBundle\Command\DeployMigrateRefunds' shared autowired service.

return $this->services['console.command.public_alias.Wizacha\\AppBundle\\Command\\DeployMigrateRefunds'] = new \Wizacha\AppBundle\Command\DeployMigrateRefunds(($this->services['doctrine.orm.default_entity_manager'] ?? $this->getDoctrine_Orm_DefaultEntityManagerService()), ($this->services['marketplace.order_return.service'] ?? $this->load('getMarketplace_OrderReturn_ServiceService.php')), ($this->services['marketplace.order.refund.refund_service'] ?? $this->load('getMarketplace_Order_Refund_RefundServiceService.php')), ($this->services['Wizacha\\Marketplace\\Order\\OrderService'] ?? $this->load('getOrderServiceService.php')));
