<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private '.errored..service_locator.pEXF19J.Wizacha\Marketplace\Order\Order' shared service.

$this->throw('Cannot autowire service ".service_locator.pEXF19J": it references class "Wizacha\\Marketplace\\Order\\Order" but no such service exists.');
