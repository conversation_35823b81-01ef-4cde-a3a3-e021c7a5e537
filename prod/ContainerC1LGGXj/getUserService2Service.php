<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Security\User\UserService' shared service.

return $this->services['Wizacha\\AppBundle\\Security\\User\\UserService'] = new \Wizacha\AppBundle\Security\User\UserService(($this->services['security.token_storage'] ?? $this->getSecurity_TokenStorageService()), ($this->services['Wizacha\\Marketplace\\Basket\\BasketService'] ?? $this->load('getBasketServiceService.php')), ($this->services['session'] ?? $this->load('getSessionService.php')), ($this->services['logger'] ?? $this->load('getLoggerService.php')));
