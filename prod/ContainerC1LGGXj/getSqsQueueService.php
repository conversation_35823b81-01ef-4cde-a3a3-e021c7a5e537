<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Async\SqsQueue' shared service.

return $this->services['Wizacha\\Async\\SqsQueue'] = new \Wizacha\Async\SqsQueue(($this->services['marketplace.aws.sqs.client'] ?? $this->load('getMarketplace_Aws_Sqs_ClientService.php')), 'ovh_prod');
