<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'worker.job_provider' shared service.

return $this->services['worker.job_provider'] = new \Wizacha\Async\JobProvider($this->parameters['marketplace.queue.config'], ($this->services['worker.queue_list'] ?? $this->load('getWorker_QueueListService.php')));
