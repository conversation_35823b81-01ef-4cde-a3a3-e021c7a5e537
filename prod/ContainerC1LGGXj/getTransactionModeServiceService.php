<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\PIM\TransactionMode\TransactionModeService' shared service.

return $this->services['Wizacha\\Marketplace\\PIM\\TransactionMode\\TransactionModeService'] = new \Wizacha\Marketplace\PIM\TransactionMode\TransactionModeService(true, true, true);
