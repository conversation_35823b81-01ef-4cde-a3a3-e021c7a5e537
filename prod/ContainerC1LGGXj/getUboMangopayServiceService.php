<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'Wizacha\Marketplace\PSP\UboMangopay\UboMangopayService' shared service.

return $this->privates['Wizacha\\Marketplace\\PSP\\UboMangopay\\UboMangopayService'] = new \Wizacha\Marketplace\PSP\UboMangopay\UboMangopayService(new \Wizacha\Marketplace\PSP\UboMangopay\UboMangopayRepository(($this->services['doctrine'] ?? $this->getDoctrineService()), 'Wizacha\\Marketplace\\PSP\\UboMangopay\\UboMangopay'), ($this->privates['Wizacha\\Marketplace\\CompanyPerson\\CompanyPersonRepository'] ?? $this->load('getCompanyPersonRepositoryService.php')));
