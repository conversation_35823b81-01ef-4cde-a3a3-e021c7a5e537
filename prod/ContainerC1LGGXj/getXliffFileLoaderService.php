<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Symfony\Component\Translation\Loader\XliffFileLoader' shared service.

return $this->services['Symfony\\Component\\Translation\\Loader\\XliffFileLoader'] = new \Symfony\Component\Translation\Loader\XliffFileLoader();
