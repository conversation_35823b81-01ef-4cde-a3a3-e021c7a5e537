<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'pdf_generator' shared autowired service.

return $this->services['pdf_generator'] = new \Wizacha\Component\PdfGenerator\PdfGenerator(($this->services['knp_snappy.pdf'] ?? $this->load('getKnpSnappy_PdfService.php')));
