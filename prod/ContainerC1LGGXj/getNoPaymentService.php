<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Payment\Processor\NoPayment' shared autowired service.

return $this->services['Wizacha\\Marketplace\\Payment\\Processor\\NoPayment'] = new \Wizacha\Marketplace\Payment\Processor\NoPayment(($this->services['Wizacha\\Marketplace\\Transaction\\TransactionService'] ?? $this->load('getTransactionServiceService.php')), ($this->services['Wizacha\\Marketplace\\Order\\OrderService'] ?? $this->load('getOrderServiceService.php')), ($this->services['logger'] ?? $this->load('getLoggerService.php')));
