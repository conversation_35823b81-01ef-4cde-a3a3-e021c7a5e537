<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'marketplace.aws.client' shared service.

return $this->services['marketplace.aws.client'] = new \Aws\Sdk(['region' => 'eu-west-1', 'version' => 'latest', 'credentials' => ['key' => '', 'secret' => '']]);
