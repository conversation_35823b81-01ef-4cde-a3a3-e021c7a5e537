<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'app.mailer' shared service.

return $this->services['app.mailer'] = new \Wizacha\Bridge\Swiftmailer\SwiftMailer(($this->services['swiftmailer.mailer.mailer'] ?? $this->load('getSwiftmailer_Mailer_MailerService.php')), \dirname(__DIR__, 4), ($this->services['Wizacha\\Async\\Dispatcher'] ?? $this->load('getDispatcherService.php')));
