<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Component\Import\ImportFactory' shared service.

return $this->services['Wizacha\\Component\\Import\\ImportFactory'] = new \Wizacha\Component\Import\ImportFactory(($this->services['marketplace.import.csv_uploader'] ?? $this->load('getMarketplace_Import_CsvUploaderService.php')), ($this->services['marketplace.import.job_service'] ?? $this->load('getMarketplace_Import_JobServiceService.php')), ($this->services['logger'] ?? $this->load('getLoggerService.php')));
