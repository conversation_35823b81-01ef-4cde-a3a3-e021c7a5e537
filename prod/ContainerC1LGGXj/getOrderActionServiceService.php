<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Order\OrderAction\Service\OrderActionService' shared autowired service.

return $this->services['Wizacha\\Marketplace\\Order\\OrderAction\\Service\\OrderActionService'] = new \Wizacha\Marketplace\Order\OrderAction\Service\OrderActionService(($this->services['Wizacha\\Marketplace\\Order\\OrderAction\\Repository\\OrderActionRepository'] ?? $this->load('getOrderActionRepositoryService.php')), ($this->services['Wizacha\\Marketplace\\Order\\OrderService'] ?? $this->load('getOrderServiceService.php')), ($this->services['router'] ?? $this->getRouterService()));
