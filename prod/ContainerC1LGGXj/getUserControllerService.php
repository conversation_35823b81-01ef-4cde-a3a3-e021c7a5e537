<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\UserController' shared service.

$a = ($this->services['Wizacha\\Marketplace\\User\\UserService'] ?? $this->getUserServiceService());

$this->services['Wizacha\\AppBundle\\Controller\\Api\\UserController'] = $instance = new \Wizacha\AppBundle\Controller\Api\UserController(($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()), $a, new \Wizacha\Marketplace\User\UserSerializer($a, false), ($this->services['Wizacha\\Marketplace\\Basket\\BasketService'] ?? $this->load('getBasketServiceService.php')), ($this->services['validator'] ?? $this->load('getValidatorService.php')), ($this->services['marketplace.user.address_service'] ?? $this->load('getMarketplace_User_AddressServiceService.php')), ($this->services['Wizacha\\Marketplace\\Country\\CountryService'] ?? $this->getCountryServiceService()), ($this->privates['Wizacha\\AppBundle\\Service\\WhitelistDomainsService'] ?? $this->load('getWhitelistDomainsServiceService.php')), ($this->services['Wizacha\\Marketplace\\Company\\CompanyService'] ?? $this->load('getCompanyServiceService.php')), ($this->privates['marketplace.cscart_password_encoder'] ?? ($this->privates['marketplace.cscart_password_encoder'] = new \Wizacha\AppBundle\Security\User\CscartPasswordEncoder())), false);

$instance->setContainer($this);

return $instance;
