<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Backend\AuthController' shared service.

return $this->services['Wizacha\\AppBundle\\Controller\\Backend\\AuthController'] = new \Wizacha\AppBundle\Controller\Backend\AuthController(($this->services['Wizacha\\Component\\AuthLog\\AuthLogRepository'] ?? $this->load('getAuthLogRepositoryService.php')), ($this->services['marketplace.oauth.admin_provider'] ?? $this->load('getMarketplace_Oauth_AdminProviderService.php')));
