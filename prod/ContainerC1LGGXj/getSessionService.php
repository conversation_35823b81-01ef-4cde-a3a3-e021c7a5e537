<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'session' shared service.

return $this->services['session'] = new \Symfony\Component\HttpFoundation\Session\Session(new \Wizacha\Bridge\Symfony\CsCartSessionStorage($this->parameters['session.storage.options'], new \Wizacha\Bridge\Tygh\SessionHandler(\Wizacha\Bridge\Tygh\SessionFactory::createSessionBackend(), 1209600), new \Symfony\Component\HttpFoundation\Session\Storage\MetadataBag('_sf2_meta', 0)));
