<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'Wizacha\Marketplace\CompanyPerson\CompanyPersonRepository' shared service.

return $this->privates['Wizacha\\Marketplace\\CompanyPerson\\CompanyPersonRepository'] = new \Wizacha\Marketplace\CompanyPerson\CompanyPersonRepository(($this->services['doctrine'] ?? $this->getDoctrineService()), 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson');
