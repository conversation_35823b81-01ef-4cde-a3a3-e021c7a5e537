<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Payment\Processor\LemonWay' shared autowired service.

$this->services['Wizacha\\Marketplace\\Payment\\Processor\\LemonWay'] = $instance = new \Wizacha\Marketplace\Payment\Processor\LemonWay(($this->services['Wizacha\\Marketplace\\Payment\\LemonWay\\LemonWayApi'] ?? $this->load('getLemonWayApiService.php')), ($this->services['Wizacha\\Marketplace\\Payment\\LemonWay\\LemonWayConfig'] ?? $this->load('getLemonWayConfigService.php')), ($this->services['logger'] ?? $this->load('getLoggerService.php')), ($this->services['router'] ?? $this->getRouterService()), ($this->services['Wizacha\\Marketplace\\User\\UserRepository'] ?? $this->getUserRepositoryService()), ($this->services['Wizacha\\Marketplace\\Country\\CountryService'] ?? $this->getCountryServiceService()), ($this->services['Wizacha\\Marketplace\\Transaction\\TransactionService'] ?? $this->load('getTransactionServiceService.php')), ($this->services['Wizacha\\Marketplace\\Order\\Token\\TokenService'] ?? $this->load('getTokenServiceService.php')), ($this->services['event_dispatcher'] ?? $this->getEventDispatcherService()), ($this->services['Wizacha\\Marketplace\\Payment\\MandateService'] ?? $this->load('getMandateServiceService.php')), ($this->services['marketplace.payment.user_payment_info_service'] ?? $this->load('getMarketplace_Payment_UserPaymentInfoServiceService.php')), ($this->services['Wizacha\\Marketplace\\Order\\OrderService'] ?? $this->load('getOrderServiceService.php')), ($this->services['Wizacha\\Marketplace\\User\\UserService'] ?? $this->getUserServiceService()));

$instance->setUrlValidator(($this->services['app.validator.url_validator'] ?? ($this->services['app.validator.url_validator'] = new \Wizacha\AppBundle\Validator\UrlValidator())));

return $instance;
