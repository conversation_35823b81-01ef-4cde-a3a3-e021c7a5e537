<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Async\Debouncer\DebouncerService' shared service.

return $this->services['Wizacha\\Async\\Debouncer\\DebouncerService'] = new \Wizacha\Async\Debouncer\DebouncerService(($this->services['marketplace.debouncer.repository'] ?? $this->load('getMarketplace_Debouncer_RepositoryService.php')), ($this->services['marketplace.queue_manager'] ?? $this->load('getMarketplace_QueueManagerService.php')), ($this->services['logger'] ?? $this->load('getLoggerService.php')));
