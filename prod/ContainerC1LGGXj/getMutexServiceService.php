<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Core\Concurrent\MutexService' shared autowired service.

return $this->services['Wizacha\\Core\\Concurrent\\MutexService'] = new \Wizacha\Core\Concurrent\MutexService(($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()));
