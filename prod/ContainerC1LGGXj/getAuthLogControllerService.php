<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\AuthLogController' shared autowired service.

return $this->services['Wizacha\\AppBundle\\Controller\\Api\\AuthLogController'] = new \Wizacha\AppBundle\Controller\Api\AuthLogController(($this->services['Wizacha\\Component\\AuthLog\\AuthLogService'] ?? $this->load('getAuthLogServiceService.php')), ($this->services['serializer'] ?? $this->load('getSerializerService.php')));
