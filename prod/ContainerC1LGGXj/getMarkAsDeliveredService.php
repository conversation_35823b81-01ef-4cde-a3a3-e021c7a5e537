<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Order\Action\MarkAsDelivered' shared autowired service.

$this->services['Wizacha\\Marketplace\\Order\\Action\\MarkAsDelivered'] = $instance = new \Wizacha\Marketplace\Order\Action\MarkAsDelivered(($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()), ($this->services['Wizacha\\Marketplace\\Order\\Workflow\\WorkflowService'] ?? $this->load('getWorkflowServiceService.php')), ($this->services['logger'] ?? $this->load('getLoggerService.php')), ($this->services['marketplace.order.tracer'] ?? $this->load('getMarketplace_Order_TracerService.php')));

$instance->setShipmentService(($this->privates['marketplace.order.shipment_service'] ?? $this->load('getMarketplace_Order_ShipmentServiceService.php')));

return $instance;
