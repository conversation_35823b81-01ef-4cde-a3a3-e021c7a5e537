<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'translation.extractor' shared service.

$this->privates['translation.extractor'] = $instance = new \Symfony\Component\Translation\Extractor\ChainExtractor();

$instance->addExtractor('cscart', new \Wizacha\AppBundle\Translation\CsCartExtractor());
$instance->addExtractor('smarty', new \Wizacha\AppBundle\Translation\SmartyExtractor(($this->services['templating.smarty'] ?? $this->load('getTemplating_SmartyService.php')), \dirname(__DIR__, 4)));
$instance->addExtractor('php', new \Symfony\Component\Translation\Extractor\PhpExtractor());
$instance->addExtractor('twig', new \Symfony\Bridge\Twig\Translation\TwigExtractor(($this->services['twig'] ?? $this->load('getTwigService.php'))));

return $instance;
