<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\Payment\MandateService' shared service.

return $this->services['Wizacha\\Marketplace\\Payment\\MandateService'] = new \Wizacha\Marketplace\Payment\MandateService(($this->services['marketplace.payment.repository.user_payment_info'] ?? $this->load('getMarketplace_Payment_Repository_UserPaymentInfoService.php')), ($this->services['marketplace.payment.repository.user_mandate'] ?? $this->load('getMarketplace_Payment_Repository_UserMandateService.php')));
