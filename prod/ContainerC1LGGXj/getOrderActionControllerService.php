<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\Order\OrderActionController' shared autowired service.

$this->services['Wizacha\\AppBundle\\Controller\\Api\\Order\\OrderActionController'] = $instance = new \Wizacha\AppBundle\Controller\Api\Order\OrderActionController(($this->services['Wizacha\\Marketplace\\Order\\OrderAction\\Service\\OrderActionService'] ?? $this->load('getOrderActionServiceService.php')));

$instance->setContainer(($this->privates['.service_locator.vdmMuyE'] ?? $this->load('get_ServiceLocator_VdmMuyEService.php'))->withContext('Wizacha\\AppBundle\\Controller\\Api\\Order\\OrderActionController', $this));

return $instance;
