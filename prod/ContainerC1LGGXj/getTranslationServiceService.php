<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Service\TranslationService' shared autowired service.

return $this->services['Wizacha\\AppBundle\\Service\\TranslationService'] = new \Wizacha\AppBundle\Service\TranslationService(($this->services['translator'] ?? $this->load('getTranslatorService.php')), ($this->services['Symfony\\Component\\Translation\\Loader\\XliffFileLoader'] ?? ($this->services['Symfony\\Component\\Translation\\Loader\\XliffFileLoader'] = new \Symfony\Component\Translation\Loader\XliffFileLoader())), ($this->services['doctrine.dbal.default_connection'] ?? $this->getDoctrine_Dbal_DefaultConnectionService()));
