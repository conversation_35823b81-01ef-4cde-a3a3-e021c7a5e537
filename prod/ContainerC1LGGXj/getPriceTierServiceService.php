<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\Marketplace\PriceTier\Service\PriceTierService' shared autowired service.

return $this->services['Wizacha\\Marketplace\\PriceTier\\Service\\PriceTierService'] = new \Wizacha\Marketplace\PriceTier\Service\PriceTierService(($this->services['Wizacha\\Marketplace\\PriceTier\\Repository\\PriceTierRepository'] ?? $this->load('getPriceTierRepositoryService.php')), ($this->services['Wizacha\\Marketplace\\PIM\\Product\\ProductService'] ?? $this->load('getProductService2Service.php')), ($this->services['Wizacha\\Marketplace\\ProductOptionInventory\\Repository\\ProductOptionInventoryRepository'] ?? $this->load('getProductOptionInventoryRepositoryService.php')), ($this->services['marketplace.price_tier.product_option_inventory_service'] ?? $this->load('getMarketplace_PriceTier_ProductOptionInventoryServiceService.php')), true);
