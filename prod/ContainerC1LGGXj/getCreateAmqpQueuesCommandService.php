<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'console.command.public_alias.Wizacha\AppBundle\Command\CreateAmqpQueuesCommand' shared autowired service.

return $this->services['console.command.public_alias.Wizacha\\AppBundle\\Command\\CreateAmqpQueuesCommand'] = new \Wizacha\AppBundle\Command\CreateAmqpQueuesCommand();
