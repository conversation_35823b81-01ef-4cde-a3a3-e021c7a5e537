<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the public 'Wizacha\AppBundle\Controller\Api\TranslationController' shared autowired service.

return $this->services['Wizacha\\AppBundle\\Controller\\Api\\TranslationController'] = new \Wizacha\AppBundle\Controller\Api\TranslationController(($this->services['Wizacha\\AppBundle\\Service\\TranslationService'] ?? $this->load('getTranslationServiceService.php')));
