<?php

use Symfony\Component\DependencyInjection\Argument\RewindableGenerator;
use Symfony\Component\DependencyInjection\Exception\RuntimeException;

// This file has been auto-generated by the Symfony Dependency Injection Component for internal use.
// Returns the private 'Wizacha\AppBundle\Command\Benchmark\BenchmarkRedisCommand' shared autowired service.

$this->privates['Wizacha\\AppBundle\\Command\\Benchmark\\BenchmarkRedisCommand'] = $instance = new \Wizacha\AppBundle\Command\Benchmark\BenchmarkRedisCommand(($this->services['snc_redis.public.default'] ?? $this->load('getSncRedis_Public_DefaultService.php')));

$instance->setName('debug:benchmark:redis');

return $instance;
