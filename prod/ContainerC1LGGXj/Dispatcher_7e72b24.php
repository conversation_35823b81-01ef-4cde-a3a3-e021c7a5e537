<?php

class Dispatcher_7e72b24 extends \Wizacha\Async\Dispatcher implements \ProxyManager\Proxy\VirtualProxyInterface
{
    private $valueHoldera1fd8 = null;
    private $initializere3996 = null;
    private static $publicProperties7ed0d = [
        
    ];
    public function nextExecIsSynchronous()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'nextExecIsSynchronous', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->nextExecIsSynchronous();
    }
    public function delayExec($function_name, array $args, ?\Wizacha\Registry $registry = null, array $progress_id = [], ?string $debounceKey = null)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'delayExec', array('function_name' => $function_name, 'args' => $args, 'registry' => $registry, 'progress_id' => $progress_id, 'debounceKey' => $debounceKey), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->delayExec($function_name, $args, $registry, $progress_id, $debounceKey);
    }
    public function bbq()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'bbq', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->bbq();
    }
    public function functionSchema()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'functionSchema', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return $this->valueHoldera1fd8->functionSchema();
    }
    public static function staticProxyConstructor($initializer)
    {
        static $reflection;
        $reflection = $reflection ?? new \ReflectionClass(__CLASS__);
        $instance   = $reflection->newInstanceWithoutConstructor();
        \Closure::bind(function (\Wizacha\Async\Dispatcher $instance) {
            unset($instance->_functions_schema, $instance->_bbq, $instance->_oneSynchronousShot, $instance->logger, $instance->debouncerService, $instance->queueType);
        }, $instance, 'Wizacha\\Async\\Dispatcher')->__invoke($instance);
        $instance->initializere3996 = $initializer;
        return $instance;
    }
    public function __construct(array $functions_schema, \Eventio\BBQ $bbq, \Psr\Log\LoggerInterface $logger, \Wizacha\Async\Debouncer\DebouncerService $debouncerService, string $queueType = '')
    {
        static $reflection;
        if (! $this->valueHoldera1fd8) {
            $reflection = $reflection ?? new \ReflectionClass('Wizacha\\Async\\Dispatcher');
            $this->valueHoldera1fd8 = $reflection->newInstanceWithoutConstructor();
        \Closure::bind(function (\Wizacha\Async\Dispatcher $instance) {
            unset($instance->_functions_schema, $instance->_bbq, $instance->_oneSynchronousShot, $instance->logger, $instance->debouncerService, $instance->queueType);
        }, $this, 'Wizacha\\Async\\Dispatcher')->__invoke($this);
        }
        $this->valueHoldera1fd8->__construct($functions_schema, $bbq, $logger, $debouncerService, $queueType);
    }
    public function & __get($name)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '__get', ['name' => $name], $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        if (isset(self::$publicProperties7ed0d[$name])) {
            return $this->valueHoldera1fd8->$name;
        }
        $realInstanceReflection = new \ReflectionClass('Wizacha\\Async\\Dispatcher');
        if (! $realInstanceReflection->hasProperty($name)) {
            $targetObject = $this->valueHoldera1fd8;
            $backtrace = debug_backtrace(false, 1);
            trigger_error(
                sprintf(
                    'Undefined property: %s::$%s in %s on line %s',
                    $realInstanceReflection->getName(),
                    $name,
                    $backtrace[0]['file'],
                    $backtrace[0]['line']
                ),
                \E_USER_NOTICE
            );
            return $targetObject->$name;
        }
        $targetObject = $this->valueHoldera1fd8;
        $accessor = function & () use ($targetObject, $name) {
            return $targetObject->$name;
        };
        $backtrace = debug_backtrace(true, 2);
        $scopeObject = isset($backtrace[1]['object']) ? $backtrace[1]['object'] : new \ProxyManager\Stub\EmptyClassStub();
        $accessor = $accessor->bindTo($scopeObject, get_class($scopeObject));
        $returnValue = & $accessor();
        return $returnValue;
    }
    public function __set($name, $value)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '__set', array('name' => $name, 'value' => $value), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        $realInstanceReflection = new \ReflectionClass('Wizacha\\Async\\Dispatcher');
        if (! $realInstanceReflection->hasProperty($name)) {
            $targetObject = $this->valueHoldera1fd8;
            $targetObject->$name = $value;
            return $targetObject->$name;
        }
        $targetObject = $this->valueHoldera1fd8;
        $accessor = function & () use ($targetObject, $name, $value) {
            $targetObject->$name = $value;
            return $targetObject->$name;
        };
        $backtrace = debug_backtrace(true, 2);
        $scopeObject = isset($backtrace[1]['object']) ? $backtrace[1]['object'] : new \ProxyManager\Stub\EmptyClassStub();
        $accessor = $accessor->bindTo($scopeObject, get_class($scopeObject));
        $returnValue = & $accessor();
        return $returnValue;
    }
    public function __isset($name)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '__isset', array('name' => $name), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        $realInstanceReflection = new \ReflectionClass('Wizacha\\Async\\Dispatcher');
        if (! $realInstanceReflection->hasProperty($name)) {
            $targetObject = $this->valueHoldera1fd8;
            return isset($targetObject->$name);
        }
        $targetObject = $this->valueHoldera1fd8;
        $accessor = function () use ($targetObject, $name) {
            return isset($targetObject->$name);
        };
        $backtrace = debug_backtrace(true, 2);
        $scopeObject = isset($backtrace[1]['object']) ? $backtrace[1]['object'] : new \ProxyManager\Stub\EmptyClassStub();
        $accessor = $accessor->bindTo($scopeObject, get_class($scopeObject));
        $returnValue = $accessor();
        return $returnValue;
    }
    public function __unset($name)
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '__unset', array('name' => $name), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        $realInstanceReflection = new \ReflectionClass('Wizacha\\Async\\Dispatcher');
        if (! $realInstanceReflection->hasProperty($name)) {
            $targetObject = $this->valueHoldera1fd8;
            unset($targetObject->$name);
            return;
        }
        $targetObject = $this->valueHoldera1fd8;
        $accessor = function () use ($targetObject, $name) {
            unset($targetObject->$name);
            return;
        };
        $backtrace = debug_backtrace(true, 2);
        $scopeObject = isset($backtrace[1]['object']) ? $backtrace[1]['object'] : new \ProxyManager\Stub\EmptyClassStub();
        $accessor = $accessor->bindTo($scopeObject, get_class($scopeObject));
        $accessor();
    }
    public function __clone()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '__clone', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        $this->valueHoldera1fd8 = clone $this->valueHoldera1fd8;
    }
    public function __sleep()
    {
        $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, '__sleep', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
        return array('valueHoldera1fd8');
    }
    public function __wakeup()
    {
        \Closure::bind(function (\Wizacha\Async\Dispatcher $instance) {
            unset($instance->_functions_schema, $instance->_bbq, $instance->_oneSynchronousShot, $instance->logger, $instance->debouncerService, $instance->queueType);
        }, $this, 'Wizacha\\Async\\Dispatcher')->__invoke($this);
    }
    public function setProxyInitializer(\Closure $initializer = null) : void
    {
        $this->initializere3996 = $initializer;
    }
    public function getProxyInitializer() : ?\Closure
    {
        return $this->initializere3996;
    }
    public function initializeProxy() : bool
    {
        return $this->initializere3996 && ($this->initializere3996->__invoke($valueHoldera1fd8, $this, 'initializeProxy', array(), $this->initializere3996) || 1) && $this->valueHoldera1fd8 = $valueHoldera1fd8;
    }
    public function isProxyInitialized() : bool
    {
        return null !== $this->valueHoldera1fd8;
    }
    public function getWrappedValueHolderValue()
    {
        return $this->valueHoldera1fd8;
    }
}
