<?php //Wizacha%255CMarketplace%255COrder%255CAmountsCalculator%255CEntity%255COrderAmounts

return [PHP_INT_MAX, static function () { return \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
    $o = [
        clone (($p = &\Symfony\Component\VarExporter\Internal\Registry::$prototypes)['Doctrine\\ORM\\Mapping\\Table'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Doctrine\\ORM\\Mapping\\Table')),
        clone ($p['Doctrine\\ORM\\Mapping\\Entity'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Doctrine\\ORM\\Mapping\\Entity')),
    ],
    null,
    [
        'stdClass' => [
            'name' => [
                'order_amounts',
            ],
            'repositoryClass' => [
                1 => 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Repository\\OrderAmountsRepository',
            ],
        ],
    ],
    [
        $o[0],
        $o[1],
    ],
    []
); }];
