<?php //Wizacha%255CMarketplace%255CQuotation%255CEntity%255CQuoteRequestSelectionDeclination%2524quoteRequestSelection

return [PHP_INT_MAX, static function () { return \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
    $o = [
        clone (($p = &\Symfony\Component\VarExporter\Internal\Registry::$prototypes)['Doctrine\\ORM\\Mapping\\Id'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Doctrine\\ORM\\Mapping\\Id')),
        clone ($p['Doctrine\\ORM\\Mapping\\ManyToOne'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Doctrine\\ORM\\Mapping\\ManyToOne')),
        clone ($p['Doctrine\\ORM\\Mapping\\JoinColumn'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Doctrine\\ORM\\Mapping\\JoinColumn')),
    ],
    null,
    [
        'stdClass' => [
            'targetEntity' => [
                1 => 'QuoteRequestSelection',
            ],
            'inversedBy' => [
                1 => 'selectionDeclinations',
            ],
            'name' => [
                2 => 'quote_request_selection_id',
            ],
            'referencedColumnName' => [
                2 => 'quote_request_selection_id',
            ],
        ],
    ],
    [
        $o[0],
        $o[1],
        $o[2],
    ],
    []
); }];
