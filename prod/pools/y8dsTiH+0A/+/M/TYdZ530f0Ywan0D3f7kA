<?php //Wizacha%255CMarketplace%255CTax%255CEntity%255CShippingTax%2524updatedAt

return [PHP_INT_MAX, static function () { return \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
    $o = [
        clone (\Symfony\Component\VarExporter\Internal\Registry::$prototypes['Doctrine\\ORM\\Mapping\\Column'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Doctrine\\ORM\\Mapping\\Column')),
    ],
    null,
    [
        'stdClass' => [
            'name' => [
                'updated_at',
            ],
            'type' => [
                'datetime',
            ],
            'nullable' => [
                true,
            ],
        ],
    ],
    [
        $o[0],
    ],
    []
); }];
