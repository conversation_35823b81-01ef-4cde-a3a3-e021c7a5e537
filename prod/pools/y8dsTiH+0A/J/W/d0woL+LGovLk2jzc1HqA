<?php //Wizacha%255CMarketplace%255COrder%255CAmountsCalculator%255CEntity%255COrderAmounts%2524taxes

return [PHP_INT_MAX, static function () { return \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
    $o = [
        clone (\Symfony\Component\VarExporter\Internal\Registry::$prototypes['Doctrine\\ORM\\Mapping\\Column'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Doctrine\\ORM\\Mapping\\Column')),
    ],
    null,
    [
        'stdClass' => [
            'name' => [
                'taxes',
            ],
            'type' => [
                'json',
            ],
        ],
    ],
    [
        $o[0],
    ],
    []
); }];
