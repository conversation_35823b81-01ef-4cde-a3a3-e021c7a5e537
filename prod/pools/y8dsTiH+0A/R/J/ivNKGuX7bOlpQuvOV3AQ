<?php //Wizacha%255CMarketplace%255CQuotation%255CEntity%255CQuoteRequestSelection%2524userId

return [PHP_INT_MAX, static function () { return \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
    $o = [
        clone (\Symfony\Component\VarExporter\Internal\Registry::$prototypes['Doctrine\\ORM\\Mapping\\Column'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Doctrine\\ORM\\Mapping\\Column')),
    ],
    null,
    [
        'stdClass' => [
            'name' => [
                'user_id',
            ],
            'type' => [
                'integer',
            ],
        ],
    ],
    [
        $o[0],
    ],
    []
); }];
