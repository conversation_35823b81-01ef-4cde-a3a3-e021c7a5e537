<?php //Wizacha%255CMarketplace%255COrder%255CAmountsCalculator%255CEntity%255CAmountItem%2524orderAmounts

return [PHP_INT_MAX, static function () { return \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
    $o = [
        clone (($p = &\Symfony\Component\VarExporter\Internal\Registry::$prototypes)['Doctrine\\ORM\\Mapping\\ManyToOne'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Doctrine\\ORM\\Mapping\\ManyToOne')),
        clone ($p['Doctrine\\ORM\\Mapping\\JoinColumn'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Doctrine\\ORM\\Mapping\\JoinColumn')),
    ],
    null,
    [
        'stdClass' => [
            'targetEntity' => [
                'OrderAmounts',
            ],
            'inversedBy' => [
                'amountItem',
            ],
            'name' => [
                1 => 'order_id',
            ],
            'referencedColumnName' => [
                1 => 'order_id',
            ],
        ],
    ],
    [
        $o[0],
        $o[1],
    ],
    []
); }];
