<?php //Gedmo%255CTree%255CEntity%255CMappedSuperclass%255CAbstractClosure

return [PHP_INT_MAX, static function () { return \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
    $o = [
        clone (\Symfony\Component\VarExporter\Internal\Registry::$prototypes['Doctrine\\ORM\\Mapping\\MappedSuperclass'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Doctrine\\ORM\\Mapping\\MappedSuperclass')),
    ],
    null,
    [],
    [
        $o[0],
    ],
    []
); }];
