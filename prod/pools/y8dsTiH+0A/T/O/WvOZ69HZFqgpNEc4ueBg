<?php //Wizacha%255CMarketplace%255COrder%255CAmountsCalculator%255CEntity%255COrderAmounts%2524shippingAmount

return [PHP_INT_MAX, static function () { return \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
    $o = [
        clone (($p = &\Symfony\Component\VarExporter\Internal\Registry::$prototypes)['Doctrine\\ORM\\Mapping\\OneToOne'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Doctrine\\ORM\\Mapping\\OneToOne')),
        clone ($p['Doctrine\\ORM\\Mapping\\JoinColumn'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Doctrine\\ORM\\Mapping\\JoinColumn')),
    ],
    null,
    [
        'stdClass' => [
            'targetEntity' => [
                'ShippingAmounts',
            ],
            'cascade' => [
                [
                    'persist',
                ],
            ],
            'onDelete' => [
                1 => 'CASCADE',
            ],
        ],
    ],
    [
        $o[0],
        $o[1],
    ],
    []
); }];
