<?php //Wizacha%255CMarketplace%255COrder%255CAmountsCalculator%255CEntity%255COrderAmountsCommission%2524categoryId

return [PHP_INT_MAX, static function () { return \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
    $o = [
        clone (\Symfony\Component\VarExporter\Internal\Registry::$prototypes['Doctrine\\ORM\\Mapping\\Column'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Doctrine\\ORM\\Mapping\\Column')),
    ],
    null,
    [
        'stdClass' => [
            'name' => [
                'category_id',
            ],
            'type' => [
                'integer',
            ],
        ],
    ],
    [
        $o[0],
    ],
    []
); }];
