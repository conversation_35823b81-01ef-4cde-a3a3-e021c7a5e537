<?php //Wizacha%255CMarketplace%255CQuotation%255CEntity%255CQuoteRequestSelection%2524quoteRequestIds

return [PHP_INT_MAX, static function () { return \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
    $o = [
        clone (\Symfony\Component\VarExporter\Internal\Registry::$prototypes['Doctrine\\ORM\\Mapping\\Column'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Doctrine\\ORM\\Mapping\\Column')),
    ],
    null,
    [
        'stdClass' => [
            'name' => [
                'quote_request_ids',
            ],
            'type' => [
                'simple_array',
            ],
            'nullable' => [
                true,
            ],
        ],
    ],
    [
        $o[0],
    ],
    []
); }];
