<?php //Wizacha%255CMarketplace%255CQuotation%255CEntity%255CQuoteRequestSelection

return [PHP_INT_MAX, static function () { return \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
    $o = [
        clone (($p = &\Symfony\Component\VarExporter\Internal\Registry::$prototypes)['Doctrine\\ORM\\Mapping\\Table'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Doctrine\\ORM\\Mapping\\Table')),
        clone ($p['Doctrine\\ORM\\Mapping\\Entity'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Doctrine\\ORM\\Mapping\\Entity')),
    ],
    null,
    [
        'stdClass' => [
            'name' => [
                'quote_request_selection',
            ],
            'repositoryClass' => [
                1 => 'Wizacha\\Marketplace\\Quotation\\Repository\\QuoteRequestSelectionRepository',
            ],
        ],
    ],
    [
        $o[0],
        $o[1],
    ],
    []
); }];
