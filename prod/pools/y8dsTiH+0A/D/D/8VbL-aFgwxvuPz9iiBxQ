<?php //Wizacha%255CMarketplace%255CQuotation%255CEntity%255CQuoteRequestSelectionDeclination%2524declinationId

return [PHP_INT_MAX, static function () { return \Symfony\Component\VarExporter\Internal\Hydrator::hydrate(
    $o = [
        clone (($p = &\Symfony\Component\VarExporter\Internal\Registry::$prototypes)['Doctrine\\ORM\\Mapping\\Id'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Doctrine\\ORM\\Mapping\\Id')),
        clone ($p['Doctrine\\ORM\\Mapping\\Column'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Doctrine\\ORM\\Mapping\\Column')),
        clone ($p['Doctrine\\ORM\\Mapping\\GeneratedValue'] ?? \Symfony\Component\VarExporter\Internal\Registry::p('Doctrine\\ORM\\Mapping\\GeneratedValue')),
    ],
    null,
    [
        'stdClass' => [
            'name' => [
                1 => 'declination_id',
            ],
            'strategy' => [
                2 => 'NONE',
            ],
        ],
    ],
    [
        $o[0],
        $o[1],
        $o[2],
    ],
    []
); }];
