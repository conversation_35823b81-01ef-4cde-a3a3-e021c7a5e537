<?php

namespace Proxies\__CG__\Wizacha\Component\Import;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class EximJobLog extends \Wizacha\Component\Import\EximJobLog implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Wizacha\\Component\\Import\\EximJobLog' . "\0" . 'id', '' . "\0" . 'Wizacha\\Component\\Import\\EximJobLog' . "\0" . 'jobId', '' . "\0" . 'Wizacha\\Component\\Import\\EximJobLog' . "\0" . 'entityId', '' . "\0" . 'Wizacha\\Component\\Import\\EximJobLog' . "\0" . 'line', '' . "\0" . 'Wizacha\\Component\\Import\\EximJobLog' . "\0" . 'code', '' . "\0" . 'Wizacha\\Component\\Import\\EximJobLog' . "\0" . 'message', '' . "\0" . 'Wizacha\\Component\\Import\\EximJobLog' . "\0" . 'extra', '' . "\0" . 'Wizacha\\Component\\Import\\EximJobLog' . "\0" . 'createdAt', '' . "\0" . 'Wizacha\\Component\\Import\\EximJobLog' . "\0" . 'additionalData'];
        }

        return ['__isInitialized__', '' . "\0" . 'Wizacha\\Component\\Import\\EximJobLog' . "\0" . 'id', '' . "\0" . 'Wizacha\\Component\\Import\\EximJobLog' . "\0" . 'jobId', '' . "\0" . 'Wizacha\\Component\\Import\\EximJobLog' . "\0" . 'entityId', '' . "\0" . 'Wizacha\\Component\\Import\\EximJobLog' . "\0" . 'line', '' . "\0" . 'Wizacha\\Component\\Import\\EximJobLog' . "\0" . 'code', '' . "\0" . 'Wizacha\\Component\\Import\\EximJobLog' . "\0" . 'message', '' . "\0" . 'Wizacha\\Component\\Import\\EximJobLog' . "\0" . 'extra', '' . "\0" . 'Wizacha\\Component\\Import\\EximJobLog' . "\0" . 'createdAt', '' . "\0" . 'Wizacha\\Component\\Import\\EximJobLog' . "\0" . 'additionalData'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (EximJobLog $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getId(): ?int
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function setId(int $id): \Wizacha\Component\Import\EximJobLog
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setId', [$id]);

        return parent::setId($id);
    }

    /**
     * {@inheritDoc}
     */
    public function getJobId(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getJobId', []);

        return parent::getJobId();
    }

    /**
     * {@inheritDoc}
     */
    public function setJobId(string $jobId): \Wizacha\Component\Import\EximJobLog
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setJobId', [$jobId]);

        return parent::setJobId($jobId);
    }

    /**
     * {@inheritDoc}
     */
    public function getEntityId(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getEntityId', []);

        return parent::getEntityId();
    }

    /**
     * {@inheritDoc}
     */
    public function setEntityId(?int $entityId): \Wizacha\Component\Import\EximJobLog
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setEntityId', [$entityId]);

        return parent::setEntityId($entityId);
    }

    /**
     * {@inheritDoc}
     */
    public function getLine(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLine', []);

        return parent::getLine();
    }

    /**
     * {@inheritDoc}
     */
    public function getLastLine(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLastLine', []);

        return parent::getLastLine();
    }

    /**
     * {@inheritDoc}
     */
    public function setLine(int $line): \Wizacha\Component\Import\EximJobLog
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setLine', [$line]);

        return parent::setLine($line);
    }

    /**
     * {@inheritDoc}
     */
    public function getCode(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCode', []);

        return parent::getCode();
    }

    /**
     * {@inheritDoc}
     */
    public function setCode(int $code): \Wizacha\Component\Import\EximJobLog
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCode', [$code]);

        return parent::setCode($code);
    }

    /**
     * {@inheritDoc}
     */
    public function getStringCode(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getStringCode', []);

        return parent::getStringCode();
    }

    /**
     * {@inheritDoc}
     */
    public function getMessage(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getMessage', []);

        return parent::getMessage();
    }

    /**
     * {@inheritDoc}
     */
    public function setMessage(string $message): \Wizacha\Component\Import\EximJobLog
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setMessage', [$message]);

        return parent::setMessage($message);
    }

    /**
     * {@inheritDoc}
     */
    public function getExtra(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getExtra', []);

        return parent::getExtra();
    }

    /**
     * {@inheritDoc}
     */
    public function setExtra(?string $extra): \Wizacha\Component\Import\EximJobLog
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setExtra', [$extra]);

        return parent::setExtra($extra);
    }

    /**
     * {@inheritDoc}
     */
    public function getCreatedAt(): \DateTimeInterface
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCreatedAt', []);

        return parent::getCreatedAt();
    }

    /**
     * {@inheritDoc}
     */
    public function setCreatedAt(\DateTimeInterface $createdAt): \Wizacha\Component\Import\EximJobLog
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCreatedAt', [$createdAt]);

        return parent::setCreatedAt($createdAt);
    }

    /**
     * {@inheritDoc}
     */
    public function getAdditionalData(): ?array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAdditionalData', []);

        return parent::getAdditionalData();
    }

    /**
     * {@inheritDoc}
     */
    public function expose(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'expose', []);

        return parent::expose();
    }

}
