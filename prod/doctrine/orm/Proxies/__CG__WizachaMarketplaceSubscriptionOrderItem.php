<?php

namespace Proxies\__CG__\Wizacha\Marketplace\Subscription;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class OrderItem extends \Wizacha\Marketplace\Subscription\OrderItem implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', 'id', 'categoryId', 'productId', 'productCode', 'productName', 'productIsRenewable', 'productOptionInventoryCode', 'quantity', 'unitPrice', 'totalPrice', 'priceIncludeTaxes', 'subscriptions', 'taxItems', 'priceTiers'];
        }

        return ['__isInitialized__', 'id', 'categoryId', 'productId', 'productCode', 'productName', 'productIsRenewable', 'productOptionInventoryCode', 'quantity', 'unitPrice', 'totalPrice', 'priceIncludeTaxes', 'subscriptions', 'taxItems', 'priceTiers'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (OrderItem $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getCategoryId(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCategoryId', []);

        return parent::getCategoryId();
    }

    /**
     * {@inheritDoc}
     */
    public function setCategoryId(?int $categoryId): \Wizacha\Marketplace\Subscription\OrderItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCategoryId', [$categoryId]);

        return parent::setCategoryId($categoryId);
    }

    /**
     * {@inheritDoc}
     */
    public function getProductId(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProductId', []);

        return parent::getProductId();
    }

    /**
     * {@inheritDoc}
     */
    public function setProductId(?int $productId): \Wizacha\Marketplace\Subscription\OrderItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setProductId', [$productId]);

        return parent::setProductId($productId);
    }

    /**
     * {@inheritDoc}
     */
    public function getProductCode(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProductCode', []);

        return parent::getProductCode();
    }

    /**
     * {@inheritDoc}
     */
    public function setProductCode(string $productCode): \Wizacha\Marketplace\Subscription\OrderItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setProductCode', [$productCode]);

        return parent::setProductCode($productCode);
    }

    /**
     * {@inheritDoc}
     */
    public function getProductName(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProductName', []);

        return parent::getProductName();
    }

    /**
     * {@inheritDoc}
     */
    public function setProductName(string $productName): \Wizacha\Marketplace\Subscription\OrderItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setProductName', [$productName]);

        return parent::setProductName($productName);
    }

    /**
     * {@inheritDoc}
     */
    public function getProductIsRenewable(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProductIsRenewable', []);

        return parent::getProductIsRenewable();
    }

    /**
     * {@inheritDoc}
     */
    public function setProductIsRenewable(bool $productIsRenewable): \Wizacha\Marketplace\Subscription\OrderItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setProductIsRenewable', [$productIsRenewable]);

        return parent::setProductIsRenewable($productIsRenewable);
    }

    /**
     * {@inheritDoc}
     */
    public function getProductOptionInventoryCode(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProductOptionInventoryCode', []);

        return parent::getProductOptionInventoryCode();
    }

    /**
     * {@inheritDoc}
     */
    public function setProductOptionInventoryCode(string $productOptionInventoryCode): \Wizacha\Marketplace\Subscription\OrderItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setProductOptionInventoryCode', [$productOptionInventoryCode]);

        return parent::setProductOptionInventoryCode($productOptionInventoryCode);
    }

    /**
     * {@inheritDoc}
     */
    public function getQuantity(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getQuantity', []);

        return parent::getQuantity();
    }

    /**
     * {@inheritDoc}
     */
    public function setQuantity(int $quantity): \Wizacha\Marketplace\Subscription\OrderItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setQuantity', [$quantity]);

        return parent::setQuantity($quantity);
    }

    /**
     * {@inheritDoc}
     */
    public function getUnitPrice(): ?\Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUnitPrice', []);

        return parent::getUnitPrice();
    }

    /**
     * {@inheritDoc}
     */
    public function setUnitPrice(\Wizacha\Money\Money $unitPrice): \Wizacha\Marketplace\Subscription\OrderItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setUnitPrice', [$unitPrice]);

        return parent::setUnitPrice($unitPrice);
    }

    /**
     * {@inheritDoc}
     */
    public function getTotalPrice(): ?\Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTotalPrice', []);

        return parent::getTotalPrice();
    }

    /**
     * {@inheritDoc}
     */
    public function setTotalPrice(\Wizacha\Money\Money $totalPrice): \Wizacha\Marketplace\Subscription\OrderItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTotalPrice', [$totalPrice]);

        return parent::setTotalPrice($totalPrice);
    }

    /**
     * {@inheritDoc}
     */
    public function getPriceIncludeTaxes(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPriceIncludeTaxes', []);

        return parent::getPriceIncludeTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setPriceIncludeTaxes(bool $priceIncludeTaxes): \Wizacha\Marketplace\Subscription\OrderItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPriceIncludeTaxes', [$priceIncludeTaxes]);

        return parent::setPriceIncludeTaxes($priceIncludeTaxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getSubscriptions(): \Doctrine\Common\Collections\Collection
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSubscriptions', []);

        return parent::getSubscriptions();
    }

    /**
     * {@inheritDoc}
     */
    public function setSubscriptions(iterable $subscriptions): \Wizacha\Marketplace\Subscription\OrderItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSubscriptions', [$subscriptions]);

        return parent::setSubscriptions($subscriptions);
    }

    /**
     * {@inheritDoc}
     */
    public function addSubscription(\Wizacha\Marketplace\Subscription\Subscription $subscription): \Wizacha\Marketplace\Subscription\OrderItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addSubscription', [$subscription]);

        return parent::addSubscription($subscription);
    }

    /**
     * {@inheritDoc}
     */
    public function removeSubscription(\Wizacha\Marketplace\Subscription\Subscription $subscription): \Wizacha\Marketplace\Subscription\OrderItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'removeSubscription', [$subscription]);

        return parent::removeSubscription($subscription);
    }

    /**
     * {@inheritDoc}
     */
    public function clearSubscriptions(): \Wizacha\Marketplace\Subscription\OrderItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'clearSubscriptions', []);

        return parent::clearSubscriptions();
    }

    /**
     * {@inheritDoc}
     */
    public function getTaxItems(): \Doctrine\Common\Collections\Collection
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTaxItems', []);

        return parent::getTaxItems();
    }

    /**
     * {@inheritDoc}
     */
    public function setTaxItems(iterable $taxItems): \Wizacha\Marketplace\Subscription\OrderItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTaxItems', [$taxItems]);

        return parent::setTaxItems($taxItems);
    }

    /**
     * {@inheritDoc}
     */
    public function addTaxItem(\Wizacha\Marketplace\Subscription\TaxItem $taxItem): \Wizacha\Marketplace\Subscription\OrderItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addTaxItem', [$taxItem]);

        return parent::addTaxItem($taxItem);
    }

    /**
     * {@inheritDoc}
     */
    public function removeTaxItem(\Wizacha\Marketplace\Subscription\TaxItem $taxItem): \Wizacha\Marketplace\Subscription\OrderItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'removeTaxItem', [$taxItem]);

        return parent::removeTaxItem($taxItem);
    }

    /**
     * {@inheritDoc}
     */
    public function clearTaxItems(): \Wizacha\Marketplace\Subscription\OrderItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'clearTaxItems', []);

        return parent::clearTaxItems();
    }

    /**
     * {@inheritDoc}
     */
    public function jsonSerialize(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'jsonSerialize', []);

        return parent::jsonSerialize();
    }

    /**
     * {@inheritDoc}
     */
    public function getPriceDetails(\Doctrine\Common\Collections\Collection $taxItems, bool $priceIncludeTaxes, \Wizacha\Money\Money $price, int $quantity = 1): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPriceDetails', [$taxItems, $priceIncludeTaxes, $price, $quantity]);

        return parent::getPriceDetails($taxItems, $priceIncludeTaxes, $price, $quantity);
    }

    /**
     * {@inheritDoc}
     */
    public function getPriceTiers(): \Doctrine\Common\Collections\Collection
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPriceTiers', []);

        return parent::getPriceTiers();
    }

    /**
     * {@inheritDoc}
     */
    public function setPriceTiers(iterable $priceTiers): \Wizacha\Marketplace\Subscription\OrderItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPriceTiers', [$priceTiers]);

        return parent::setPriceTiers($priceTiers);
    }

    /**
     * {@inheritDoc}
     */
    public function addPriceTiers(\Wizacha\Marketplace\Subscription\PriceTier $priceTier): \Wizacha\Marketplace\Subscription\OrderItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addPriceTiers', [$priceTier]);

        return parent::addPriceTiers($priceTier);
    }

    /**
     * {@inheritDoc}
     */
    public function removePriceTiers(\Wizacha\Marketplace\Subscription\PriceTier $priceTier): \Wizacha\Marketplace\Subscription\OrderItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'removePriceTiers', [$priceTier]);

        return parent::removePriceTiers($priceTier);
    }

    /**
     * {@inheritDoc}
     */
    public function clearPriceTiers(): \Wizacha\Marketplace\Subscription\OrderItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'clearPriceTiers', []);

        return parent::clearPriceTiers();
    }

    /**
     * {@inheritDoc}
     */
    public function updateUnitPriceWithPriceTiers(): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'updateUnitPriceWithPriceTiers', []);

        parent::updateUnitPriceWithPriceTiers();
    }

    /**
     * {@inheritDoc}
     */
    public function getId(): ?string
    {
        if ($this->__isInitialized__ === false) {
            return  parent::getId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function defineGuid(): \Wizacha\Marketplace\Subscription\OrderItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'defineGuid', []);

        return parent::defineGuid();
    }

}
