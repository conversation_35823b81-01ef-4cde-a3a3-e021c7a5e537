<?php

namespace Proxies\__CG__\Wizacha\Marketplace\User;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class User extends \Wizacha\Marketplace\User\User implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'userId', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'status', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'isLocked', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'userType', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'userLogin', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'referer', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'isRoot', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'companyId', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'lastLogin', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'failedLoginCount', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'timestamp', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'password', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'salt', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'title', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'firstname', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'lastname', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'company', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'email', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'phone', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'fax', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'url', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'taxExempt', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'langCode', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'birthday', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'purchaseTimestampFrom', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'purchaseTimestampTo', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'responsibleEmail', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'lastPasswords', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'passwordChangeTimestamp', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'apiKey', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'basketId', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'loyaltyIdentifier', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'billingAddress', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'shippingAddress', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'organisation', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'oauthToken', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'currencyCode', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'pendingCompanyId', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'externalIdentifier', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'isProfessional', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'intraEuropeanCommunityVAT', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'jobTitle', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'comment', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'legalIdentifier', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'subscriptions', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'creditCards', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'addressBook', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'userPasswordHistory', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'userUuid', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'nationalities', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'apiKeyUpdatedAt', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'extra', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'groups', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'userDeclinationDisplayType', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'quoteRequestSelectionIds'];
        }

        return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'userId', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'status', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'isLocked', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'userType', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'userLogin', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'referer', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'isRoot', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'companyId', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'lastLogin', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'failedLoginCount', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'timestamp', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'password', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'salt', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'title', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'firstname', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'lastname', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'company', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'email', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'phone', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'fax', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'url', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'taxExempt', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'langCode', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'birthday', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'purchaseTimestampFrom', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'purchaseTimestampTo', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'responsibleEmail', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'lastPasswords', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'passwordChangeTimestamp', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'apiKey', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'basketId', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'loyaltyIdentifier', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'billingAddress', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'shippingAddress', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'organisation', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'oauthToken', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'currencyCode', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'pendingCompanyId', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'externalIdentifier', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'isProfessional', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'intraEuropeanCommunityVAT', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'jobTitle', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'comment', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'legalIdentifier', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'subscriptions', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'creditCards', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'addressBook', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'userPasswordHistory', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'userUuid', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'nationalities', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'apiKeyUpdatedAt', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'extra', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'groups', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'userDeclinationDisplayType', '' . "\0" . 'Wizacha\\Marketplace\\User\\User' . "\0" . 'quoteRequestSelectionIds'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (User $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getUserId(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUserId', []);

        return parent::getUserId();
    }

    /**
     * {@inheritDoc}
     */
    public function setUserId($userId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setUserId', [$userId]);

        return parent::setUserId($userId);
    }

    /**
     * {@inheritDoc}
     */
    public function getUserUuid(): \Rhumsaa\Uuid\Uuid
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUserUuid', []);

        return parent::getUserUuid();
    }

    /**
     * {@inheritDoc}
     */
    public function setUserUuid(\Rhumsaa\Uuid\Uuid $userUuid): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setUserUuid', [$userUuid]);

        return parent::setUserUuid($userUuid);
    }

    /**
     * {@inheritDoc}
     */
    public function setStatus($status)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setStatus', [$status]);

        return parent::setStatus($status);
    }

    /**
     * {@inheritDoc}
     */
    public function getStatus()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getStatus', []);

        return parent::getStatus();
    }

    /**
     * {@inheritDoc}
     */
    public function setIsLocked(bool $isLocked): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIsLocked', [$isLocked]);

        return parent::setIsLocked($isLocked);
    }

    /**
     * {@inheritDoc}
     */
    public function isLocked(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isLocked', []);

        return parent::isLocked();
    }

    /**
     * {@inheritDoc}
     */
    public function setUserType($userType)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setUserType', [$userType]);

        return parent::setUserType($userType);
    }

    /**
     * {@inheritDoc}
     */
    public function getUserType()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUserType', []);

        return parent::getUserType();
    }

    /**
     * {@inheritDoc}
     */
    public function setUserLogin($userLogin)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setUserLogin', [$userLogin]);

        return parent::setUserLogin($userLogin);
    }

    /**
     * {@inheritDoc}
     */
    public function getUserLogin()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUserLogin', []);

        return parent::getUserLogin();
    }

    /**
     * {@inheritDoc}
     */
    public function setReferer($referer)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setReferer', [$referer]);

        return parent::setReferer($referer);
    }

    /**
     * {@inheritDoc}
     */
    public function getReferer()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getReferer', []);

        return parent::getReferer();
    }

    /**
     * {@inheritDoc}
     */
    public function setIsRoot($isRoot)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIsRoot', [$isRoot]);

        return parent::setIsRoot($isRoot);
    }

    /**
     * {@inheritDoc}
     */
    public function getIsRoot()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getIsRoot', []);

        return parent::getIsRoot();
    }

    /**
     * {@inheritDoc}
     */
    public function setCompanyId($companyId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCompanyId', [$companyId]);

        return parent::setCompanyId($companyId);
    }

    /**
     * {@inheritDoc}
     */
    public function getCompanyId(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCompanyId', []);

        return parent::getCompanyId();
    }

    /**
     * {@inheritDoc}
     */
    public function setLastLogin($lastLogin)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setLastLogin', [$lastLogin]);

        return parent::setLastLogin($lastLogin);
    }

    /**
     * {@inheritDoc}
     */
    public function getLastLogin()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLastLogin', []);

        return parent::getLastLogin();
    }

    /**
     * {@inheritDoc}
     */
    public function getFailedLoginCount(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getFailedLoginCount', []);

        return parent::getFailedLoginCount();
    }

    /**
     * {@inheritDoc}
     */
    public function setFailedLoginCount(int $failedLoginCount): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setFailedLoginCount', [$failedLoginCount]);

        return parent::setFailedLoginCount($failedLoginCount);
    }

    /**
     * {@inheritDoc}
     */
    public function setTimestamp($timestamp)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTimestamp', [$timestamp]);

        return parent::setTimestamp($timestamp);
    }

    /**
     * {@inheritDoc}
     */
    public function getTimestamp()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTimestamp', []);

        return parent::getTimestamp();
    }

    /**
     * {@inheritDoc}
     */
    public function setPassword($password)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPassword', [$password]);

        return parent::setPassword($password);
    }

    /**
     * {@inheritDoc}
     */
    public function getPassword()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPassword', []);

        return parent::getPassword();
    }

    /**
     * {@inheritDoc}
     */
    public function setSalt($salt)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSalt', [$salt]);

        return parent::setSalt($salt);
    }

    /**
     * {@inheritDoc}
     */
    public function getSalt()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSalt', []);

        return parent::getSalt();
    }

    /**
     * {@inheritDoc}
     */
    public function setTitle(?\Wizacha\Marketplace\User\UserTitle $title): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTitle', [$title]);

        return parent::setTitle($title);
    }

    /**
     * {@inheritDoc}
     */
    public function getTitle(): ?\Wizacha\Marketplace\User\UserTitle
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTitle', []);

        return parent::getTitle();
    }

    /**
     * {@inheritDoc}
     */
    public function setFirstname($firstname)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setFirstname', [$firstname]);

        return parent::setFirstname($firstname);
    }

    /**
     * {@inheritDoc}
     */
    public function getFirstname()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getFirstname', []);

        return parent::getFirstname();
    }

    /**
     * {@inheritDoc}
     */
    public function setLastname($lastname)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setLastname', [$lastname]);

        return parent::setLastname($lastname);
    }

    /**
     * {@inheritDoc}
     */
    public function getLastname()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLastname', []);

        return parent::getLastname();
    }

    /**
     * {@inheritDoc}
     */
    public function getFullName(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getFullName', []);

        return parent::getFullName();
    }

    /**
     * {@inheritDoc}
     */
    public function setCompany($company)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCompany', [$company]);

        return parent::setCompany($company);
    }

    /**
     * {@inheritDoc}
     */
    public function getCompany()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCompany', []);

        return parent::getCompany();
    }

    /**
     * {@inheritDoc}
     */
    public function setEmail($email)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setEmail', [$email]);

        return parent::setEmail($email);
    }

    /**
     * {@inheritDoc}
     */
    public function getEmail()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getEmail', []);

        return parent::getEmail();
    }

    /**
     * {@inheritDoc}
     */
    public function setPhone($phone)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPhone', [$phone]);

        return parent::setPhone($phone);
    }

    /**
     * {@inheritDoc}
     */
    public function getPhone()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPhone', []);

        return parent::getPhone();
    }

    /**
     * {@inheritDoc}
     */
    public function setFax($fax)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setFax', [$fax]);

        return parent::setFax($fax);
    }

    /**
     * {@inheritDoc}
     */
    public function getFax()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getFax', []);

        return parent::getFax();
    }

    /**
     * {@inheritDoc}
     */
    public function setUrl($url)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setUrl', [$url]);

        return parent::setUrl($url);
    }

    /**
     * {@inheritDoc}
     */
    public function getUrl()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUrl', []);

        return parent::getUrl();
    }

    /**
     * {@inheritDoc}
     */
    public function setTaxExempt($taxExempt)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTaxExempt', [$taxExempt]);

        return parent::setTaxExempt($taxExempt);
    }

    /**
     * {@inheritDoc}
     */
    public function getTaxExempt()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTaxExempt', []);

        return parent::getTaxExempt();
    }

    /**
     * {@inheritDoc}
     */
    public function setLangCode($langCode)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setLangCode', [$langCode]);

        return parent::setLangCode($langCode);
    }

    /**
     * {@inheritDoc}
     */
    public function getLangCode(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLangCode', []);

        return parent::getLangCode();
    }

    /**
     * {@inheritDoc}
     */
    public function setLocale(\Wizacha\Component\Locale\Locale $locale): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setLocale', [$locale]);

        return parent::setLocale($locale);
    }

    /**
     * {@inheritDoc}
     */
    public function getLocale(): \Wizacha\Component\Locale\Locale
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLocale', []);

        return parent::getLocale();
    }

    /**
     * {@inheritDoc}
     */
    public function setBirthday(?\DateTime $birthday)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setBirthday', [$birthday]);

        return parent::setBirthday($birthday);
    }

    /**
     * {@inheritDoc}
     */
    public function setCurrencyCode(?string $currencyCode): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCurrencyCode', [$currencyCode]);

        return parent::setCurrencyCode($currencyCode);
    }

    /**
     * {@inheritDoc}
     */
    public function getBirthday(): ?\DateTime
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getBirthday', []);

        return parent::getBirthday();
    }

    /**
     * {@inheritDoc}
     */
    public function getCurrencyCode(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCurrencyCode', []);

        return parent::getCurrencyCode();
    }

    /**
     * {@inheritDoc}
     */
    public function setPurchaseTimestampFrom($purchaseTimestampFrom)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPurchaseTimestampFrom', [$purchaseTimestampFrom]);

        return parent::setPurchaseTimestampFrom($purchaseTimestampFrom);
    }

    /**
     * {@inheritDoc}
     */
    public function getPurchaseTimestampFrom()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPurchaseTimestampFrom', []);

        return parent::getPurchaseTimestampFrom();
    }

    /**
     * {@inheritDoc}
     */
    public function setPurchaseTimestampTo($purchaseTimestampTo)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPurchaseTimestampTo', [$purchaseTimestampTo]);

        return parent::setPurchaseTimestampTo($purchaseTimestampTo);
    }

    /**
     * {@inheritDoc}
     */
    public function getPurchaseTimestampTo()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPurchaseTimestampTo', []);

        return parent::getPurchaseTimestampTo();
    }

    /**
     * {@inheritDoc}
     */
    public function setResponsibleEmail($responsibleEmail)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setResponsibleEmail', [$responsibleEmail]);

        return parent::setResponsibleEmail($responsibleEmail);
    }

    /**
     * {@inheritDoc}
     */
    public function getResponsibleEmail()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getResponsibleEmail', []);

        return parent::getResponsibleEmail();
    }

    /**
     * {@inheritDoc}
     */
    public function setLastPasswords($lastPasswords)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setLastPasswords', [$lastPasswords]);

        return parent::setLastPasswords($lastPasswords);
    }

    /**
     * {@inheritDoc}
     */
    public function getLastPasswords()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLastPasswords', []);

        return parent::getLastPasswords();
    }

    /**
     * {@inheritDoc}
     */
    public function setPasswordChangeTimestamp($passwordChangeTimestamp)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPasswordChangeTimestamp', [$passwordChangeTimestamp]);

        return parent::setPasswordChangeTimestamp($passwordChangeTimestamp);
    }

    /**
     * {@inheritDoc}
     */
    public function getPasswordChangeTimestamp()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPasswordChangeTimestamp', []);

        return parent::getPasswordChangeTimestamp();
    }

    /**
     * {@inheritDoc}
     */
    public function setApiKey($apiKey)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setApiKey', [$apiKey]);

        return parent::setApiKey($apiKey);
    }

    /**
     * {@inheritDoc}
     */
    public function getApiKey()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getApiKey', []);

        return parent::getApiKey();
    }

    /**
     * {@inheritDoc}
     */
    public function setBasketId(?string $basketId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setBasketId', [$basketId]);

        return parent::setBasketId($basketId);
    }

    /**
     * {@inheritDoc}
     */
    public function getBasketId(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getBasketId', []);

        return parent::getBasketId();
    }

    /**
     * {@inheritDoc}
     */
    public function getLoyaltyIdentifier(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLoyaltyIdentifier', []);

        return parent::getLoyaltyIdentifier();
    }

    /**
     * {@inheritDoc}
     */
    public function setLoyaltyIdentifier(string $loyaltyIdentifier = NULL): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setLoyaltyIdentifier', [$loyaltyIdentifier]);

        return parent::setLoyaltyIdentifier($loyaltyIdentifier);
    }

    /**
     * {@inheritDoc}
     */
    public function setBillingAddress(\Wizacha\Marketplace\User\UserAddress $address)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setBillingAddress', [$address]);

        return parent::setBillingAddress($address);
    }

    /**
     * {@inheritDoc}
     */
    public function getBillingAddress(): \Wizacha\Marketplace\User\UserAddress
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getBillingAddress', []);

        return parent::getBillingAddress();
    }

    /**
     * {@inheritDoc}
     */
    public function setShippingAddress(\Wizacha\Marketplace\User\UserAddress $address)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setShippingAddress', [$address]);

        return parent::setShippingAddress($address);
    }

    /**
     * {@inheritDoc}
     */
    public function getShippingAddress(): \Wizacha\Marketplace\User\UserAddress
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getShippingAddress', []);

        return parent::getShippingAddress();
    }

    /**
     * {@inheritDoc}
     */
    public function getDivision(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDivision', []);

        return parent::getDivision();
    }

    /**
     * {@inheritDoc}
     */
    public function setOrganisation(\Wizacha\Marketplace\Organisation\Organisation $organisation): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setOrganisation', [$organisation]);

        return parent::setOrganisation($organisation);
    }

    /**
     * {@inheritDoc}
     */
    public function belongsToAnOrganisation(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'belongsToAnOrganisation', []);

        return parent::belongsToAnOrganisation();
    }

    /**
     * {@inheritDoc}
     */
    public function belongsToOrganisation(\Wizacha\Marketplace\Organisation\Organisation $organisation): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'belongsToOrganisation', [$organisation]);

        return parent::belongsToOrganisation($organisation);
    }

    /**
     * {@inheritDoc}
     */
    public function getOrganisation(): \Wizacha\Marketplace\Organisation\Organisation
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOrganisation', []);

        return parent::getOrganisation();
    }

    /**
     * {@inheritDoc}
     */
    public function isPrivateIndividual(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isPrivateIndividual', []);

        return parent::isPrivateIndividual();
    }

    /**
     * {@inheritDoc}
     */
    public function isMarketplaceAdministrator(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isMarketplaceAdministrator', []);

        return parent::isMarketplaceAdministrator();
    }

    /**
     * {@inheritDoc}
     */
    public function isMarketplaceSupportAdministrator(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isMarketplaceSupportAdministrator', []);

        return parent::isMarketplaceSupportAdministrator();
    }

    /**
     * {@inheritDoc}
     */
    public function canEditUser(\Wizacha\Marketplace\User\User $user): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'canEditUser', [$user]);

        return parent::canEditUser($user);
    }

    /**
     * {@inheritDoc}
     */
    public function isEnabled(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isEnabled', []);

        return parent::isEnabled();
    }

    /**
     * {@inheritDoc}
     */
    public function isPending(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isPending', []);

        return parent::isPending();
    }

    /**
     * {@inheritDoc}
     */
    public function hasOAuthToken(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasOAuthToken', []);

        return parent::hasOAuthToken();
    }

    /**
     * {@inheritDoc}
     */
    public function getOAuthToken(): \Wizacha\Marketplace\User\OAuthToken
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOAuthToken', []);

        return parent::getOAuthToken();
    }

    /**
     * {@inheritDoc}
     */
    public function setOAuthToken(\Wizacha\Marketplace\User\OAuthToken $token): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setOAuthToken', [$token]);

        parent::setOAuthToken($token);
    }

    /**
     * {@inheritDoc}
     */
    public function refreshOAuthToken(\Wizacha\Marketplace\User\OAuthToken $token): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'refreshOAuthToken', [$token]);

        parent::refreshOAuthToken($token);
    }

    /**
     * {@inheritDoc}
     */
    public function getPendingCompanyId(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPendingCompanyId', []);

        return parent::getPendingCompanyId();
    }

    /**
     * {@inheritDoc}
     */
    public function setPendingCompanyId(?int $pendingCompanyId): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPendingCompanyId', [$pendingCompanyId]);

        return parent::setPendingCompanyId($pendingCompanyId);
    }

    /**
     * {@inheritDoc}
     */
    public function getSubscriptions(): \Doctrine\Common\Collections\Collection
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSubscriptions', []);

        return parent::getSubscriptions();
    }

    /**
     * {@inheritDoc}
     */
    public function setSubscriptions(iterable $subscriptions): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSubscriptions', [$subscriptions]);

        return parent::setSubscriptions($subscriptions);
    }

    /**
     * {@inheritDoc}
     */
    public function addSubscription(\Wizacha\Marketplace\Subscription\Subscription $subscription): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addSubscription', [$subscription]);

        return parent::addSubscription($subscription);
    }

    /**
     * {@inheritDoc}
     */
    public function removeSubscription(\Wizacha\Marketplace\Subscription\Subscription $subscription): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'removeSubscription', [$subscription]);

        return parent::removeSubscription($subscription);
    }

    /**
     * {@inheritDoc}
     */
    public function clearSubscriptions(): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'clearSubscriptions', []);

        return parent::clearSubscriptions();
    }

    /**
     * {@inheritDoc}
     */
    public function getCreditCards(): \Doctrine\Common\Collections\Collection
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCreditCards', []);

        return parent::getCreditCards();
    }

    /**
     * {@inheritDoc}
     */
    public function setCreditCards(iterable $creditCards): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCreditCards', [$creditCards]);

        return parent::setCreditCards($creditCards);
    }

    /**
     * {@inheritDoc}
     */
    public function addCreditCard(\Wizacha\Marketplace\CreditCard\CreditCard $creditCard): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addCreditCard', [$creditCard]);

        return parent::addCreditCard($creditCard);
    }

    /**
     * {@inheritDoc}
     */
    public function removeCreditCard(\Wizacha\Marketplace\CreditCard\CreditCard $creditCard): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'removeCreditCard', [$creditCard]);

        return parent::removeCreditCard($creditCard);
    }

    /**
     * {@inheritDoc}
     */
    public function clearCreditCards(): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'clearCreditCards', []);

        return parent::clearCreditCards();
    }

    /**
     * {@inheritDoc}
     */
    public function expose(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'expose', []);

        return parent::expose();
    }

    /**
     * {@inheritDoc}
     */
    public function fromArray(array $values): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'fromArray', [$values]);

        return parent::fromArray($values);
    }

    /**
     * {@inheritDoc}
     */
    public function getExternalIdentifier(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getExternalIdentifier', []);

        return parent::getExternalIdentifier();
    }

    /**
     * {@inheritDoc}
     */
    public function setExternalIdentifier(string $externalIdentifier): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setExternalIdentifier', [$externalIdentifier]);

        return parent::setExternalIdentifier($externalIdentifier);
    }

    /**
     * {@inheritDoc}
     */
    public function isProfessional(): ?bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isProfessional', []);

        return parent::isProfessional();
    }

    /**
     * {@inheritDoc}
     */
    public function setIsProfessional(?bool $isProfessional): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIsProfessional', [$isProfessional]);

        return parent::setIsProfessional($isProfessional);
    }

    /**
     * {@inheritDoc}
     */
    public function getIntraEuropeanCommunityVAT(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getIntraEuropeanCommunityVAT', []);

        return parent::getIntraEuropeanCommunityVAT();
    }

    /**
     * {@inheritDoc}
     */
    public function setIntraEuropeanCommunityVAT(?string $intraEuropeanCommunityVAT): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIntraEuropeanCommunityVAT', [$intraEuropeanCommunityVAT]);

        return parent::setIntraEuropeanCommunityVAT($intraEuropeanCommunityVAT);
    }

    /**
     * {@inheritDoc}
     */
    public function setJobTitle(string $jobTitle): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setJobTitle', [$jobTitle]);

        return parent::setJobTitle($jobTitle);
    }

    /**
     * {@inheritDoc}
     */
    public function getJobTitle(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getJobTitle', []);

        return parent::getJobTitle();
    }

    /**
     * {@inheritDoc}
     */
    public function getComment(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getComment', []);

        return parent::getComment();
    }

    /**
     * {@inheritDoc}
     */
    public function setComment(string $comment): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setComment', [$comment]);

        return parent::setComment($comment);
    }

    /**
     * {@inheritDoc}
     */
    public function getLegalIdentifier()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLegalIdentifier', []);

        return parent::getLegalIdentifier();
    }

    /**
     * {@inheritDoc}
     */
    public function setLegalIdentifier(string $legalIdentifier): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setLegalIdentifier', [$legalIdentifier]);

        return parent::setLegalIdentifier($legalIdentifier);
    }

    /**
     * {@inheritDoc}
     */
    public function getAddressBook(): \Doctrine\Common\Collections\Collection
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAddressBook', []);

        return parent::getAddressBook();
    }

    /**
     * {@inheritDoc}
     */
    public function setAddressBook(iterable $addressBook): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAddressBook', [$addressBook]);

        return parent::setAddressBook($addressBook);
    }

    /**
     * {@inheritDoc}
     */
    public function addAddressBook(\Wizacha\Marketplace\User\AddressBook $addressBook): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addAddressBook', [$addressBook]);

        return parent::addAddressBook($addressBook);
    }

    /**
     * {@inheritDoc}
     */
    public function removeAddressBook(\Wizacha\Marketplace\User\AddressBook $addressBook): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'removeAddressBook', [$addressBook]);

        return parent::removeAddressBook($addressBook);
    }

    /**
     * {@inheritDoc}
     */
    public function clearAddressBook(): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'clearAddressBook', []);

        return parent::clearAddressBook();
    }

    /**
     * {@inheritDoc}
     */
    public function getNationalities(): \Doctrine\Common\Collections\Collection
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNationalities', []);

        return parent::getNationalities();
    }

    /**
     * {@inheritDoc}
     */
    public function setNationalities(\Doctrine\Common\Collections\ArrayCollection $nationalities): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNationalities', [$nationalities]);

        return parent::setNationalities($nationalities);
    }

    /**
     * {@inheritDoc}
     */
    public function addNationality(\Wizacha\Marketplace\Country\Country $country): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addNationality', [$country]);

        return parent::addNationality($country);
    }

    /**
     * {@inheritDoc}
     */
    public function getExtra(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getExtra', []);

        return parent::getExtra();
    }

    /**
     * {@inheritDoc}
     */
    public function setExtra(array $extra): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setExtra', [$extra]);

        return parent::setExtra($extra);
    }

    /**
     * {@inheritDoc}
     */
    public function getGroups(): \Doctrine\Common\Collections\Collection
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getGroups', []);

        return parent::getGroups();
    }

    /**
     * {@inheritDoc}
     */
    public function setGroups(\Doctrine\Common\Collections\ArrayCollection $groups): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setGroups', [$groups]);

        return parent::setGroups($groups);
    }

    /**
     * {@inheritDoc}
     */
    public function addGroup(\Wizacha\Marketplace\Group\Group $group): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addGroup', [$group]);

        return parent::addGroup($group);
    }

    /**
     * {@inheritDoc}
     */
    public function getApiKeyUpdatedAt(): ?\DateTime
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getApiKeyUpdatedAt', []);

        return parent::getApiKeyUpdatedAt();
    }

    /**
     * {@inheritDoc}
     */
    public function setApiKeyUpdatedAt(?\DateTime $apiKeyUpdatedAt): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setApiKeyUpdatedAt', [$apiKeyUpdatedAt]);

        return parent::setApiKeyUpdatedAt($apiKeyUpdatedAt);
    }

    /**
     * {@inheritDoc}
     */
    public function getDeclinationDisplayType(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDeclinationDisplayType', []);

        return parent::getDeclinationDisplayType();
    }

    /**
     * {@inheritDoc}
     */
    public function setDeclinationDisplayType(string $declinationDisplayType): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDeclinationDisplayType', [$declinationDisplayType]);

        return parent::setDeclinationDisplayType($declinationDisplayType);
    }

    /**
     * {@inheritDoc}
     */
    public function getUserPasswordHistory(): \Doctrine\Common\Collections\Collection
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUserPasswordHistory', []);

        return parent::getUserPasswordHistory();
    }

    /**
     * {@inheritDoc}
     */
    public function getQuoteRequestSelectionIds(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getQuoteRequestSelectionIds', []);

        return parent::getQuoteRequestSelectionIds();
    }

    /**
     * {@inheritDoc}
     */
    public function setQuoteRequestSelectionIds(array $quoteRequestSelectionIds): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setQuoteRequestSelectionIds', [$quoteRequestSelectionIds]);

        return parent::setQuoteRequestSelectionIds($quoteRequestSelectionIds);
    }

}
