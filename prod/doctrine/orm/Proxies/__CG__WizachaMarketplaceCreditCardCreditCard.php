<?php

namespace Proxies\__CG__\Wizacha\Marketplace\CreditCard;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class CreditCard extends \Wizacha\Marketplace\CreditCard\CreditCard implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', 'id', 'user', 'token', 'brand', 'pan', 'cardHolder', 'cardExpiryMonth', 'cardExpiryYear', 'issuer', 'country', 'createdAt', 'subscriptions', 'paymentProductCode', 'pspUserId'];
        }

        return ['__isInitialized__', 'id', 'user', 'token', 'brand', 'pan', 'cardHolder', 'cardExpiryMonth', 'cardExpiryYear', 'issuer', 'country', 'createdAt', 'subscriptions', 'paymentProductCode', 'pspUserId'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (CreditCard $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getUser(): ?\Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUser', []);

        return parent::getUser();
    }

    /**
     * {@inheritDoc}
     */
    public function setUser(?\Wizacha\Marketplace\User\User $user): \Wizacha\Marketplace\CreditCard\CreditCard
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setUser', [$user]);

        return parent::setUser($user);
    }

    /**
     * {@inheritDoc}
     */
    public function getToken(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getToken', []);

        return parent::getToken();
    }

    /**
     * {@inheritDoc}
     */
    public function setToken(string $token): \Wizacha\Marketplace\CreditCard\CreditCard
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setToken', [$token]);

        return parent::setToken($token);
    }

    /**
     * {@inheritDoc}
     */
    public function getBrand(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getBrand', []);

        return parent::getBrand();
    }

    /**
     * {@inheritDoc}
     */
    public function setBrand(string $brand): \Wizacha\Marketplace\CreditCard\CreditCard
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setBrand', [$brand]);

        return parent::setBrand($brand);
    }

    /**
     * {@inheritDoc}
     */
    public function getPan(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPan', []);

        return parent::getPan();
    }

    /**
     * {@inheritDoc}
     */
    public function setPan(string $pan): \Wizacha\Marketplace\CreditCard\CreditCard
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPan', [$pan]);

        return parent::setPan($pan);
    }

    /**
     * {@inheritDoc}
     */
    public function getCardHolder(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCardHolder', []);

        return parent::getCardHolder();
    }

    /**
     * {@inheritDoc}
     */
    public function setCardHolder(?string $cardHolder): \Wizacha\Marketplace\CreditCard\CreditCard
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCardHolder', [$cardHolder]);

        return parent::setCardHolder($cardHolder);
    }

    /**
     * {@inheritDoc}
     */
    public function getCardExpiryMonth(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCardExpiryMonth', []);

        return parent::getCardExpiryMonth();
    }

    /**
     * {@inheritDoc}
     */
    public function setCardExpiryMonth(string $cardExpiryMonth): \Wizacha\Marketplace\CreditCard\CreditCard
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCardExpiryMonth', [$cardExpiryMonth]);

        return parent::setCardExpiryMonth($cardExpiryMonth);
    }

    /**
     * {@inheritDoc}
     */
    public function getCardExpiryYear(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCardExpiryYear', []);

        return parent::getCardExpiryYear();
    }

    /**
     * {@inheritDoc}
     */
    public function setCardExpiryYear(string $cardExpiryYear): \Wizacha\Marketplace\CreditCard\CreditCard
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCardExpiryYear', [$cardExpiryYear]);

        return parent::setCardExpiryYear($cardExpiryYear);
    }

    /**
     * {@inheritDoc}
     */
    public function getIssuer(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getIssuer', []);

        return parent::getIssuer();
    }

    /**
     * {@inheritDoc}
     */
    public function setIssuer(?string $issuer): \Wizacha\Marketplace\CreditCard\CreditCard
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIssuer', [$issuer]);

        return parent::setIssuer($issuer);
    }

    /**
     * {@inheritDoc}
     */
    public function getCountry(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCountry', []);

        return parent::getCountry();
    }

    /**
     * {@inheritDoc}
     */
    public function setCountry(string $country): \Wizacha\Marketplace\CreditCard\CreditCard
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCountry', [$country]);

        return parent::setCountry($country);
    }

    /**
     * {@inheritDoc}
     */
    public function getCreatedAt(): ?\DateTimeImmutable
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCreatedAt', []);

        return parent::getCreatedAt();
    }

    /**
     * {@inheritDoc}
     */
    public function defineCreatedAt(): \Wizacha\Marketplace\CreditCard\CreditCard
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'defineCreatedAt', []);

        return parent::defineCreatedAt();
    }

    /**
     * {@inheritDoc}
     */
    public function getSubscriptions(): \Doctrine\Common\Collections\Collection
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSubscriptions', []);

        return parent::getSubscriptions();
    }

    /**
     * {@inheritDoc}
     */
    public function setPaymentProductCode(string $paymentProductCode): \Wizacha\Marketplace\CreditCard\CreditCard
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPaymentProductCode', [$paymentProductCode]);

        return parent::setPaymentProductCode($paymentProductCode);
    }

    /**
     * {@inheritDoc}
     */
    public function getPaymentProductCode(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPaymentProductCode', []);

        return parent::getPaymentProductCode();
    }

    /**
     * {@inheritDoc}
     */
    public function setSubscriptions(iterable $subscriptions): \Wizacha\Marketplace\CreditCard\CreditCard
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSubscriptions', [$subscriptions]);

        return parent::setSubscriptions($subscriptions);
    }

    /**
     * {@inheritDoc}
     */
    public function addSubscription(\Wizacha\Marketplace\Subscription\Subscription $subscription): \Wizacha\Marketplace\CreditCard\CreditCard
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addSubscription', [$subscription]);

        return parent::addSubscription($subscription);
    }

    /**
     * {@inheritDoc}
     */
    public function removeSubscription(\Wizacha\Marketplace\Subscription\Subscription $subscription): \Wizacha\Marketplace\CreditCard\CreditCard
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'removeSubscription', [$subscription]);

        return parent::removeSubscription($subscription);
    }

    /**
     * {@inheritDoc}
     */
    public function clearSubscriptions(): \Wizacha\Marketplace\CreditCard\CreditCard
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'clearSubscriptions', []);

        return parent::clearSubscriptions();
    }

    /**
     * {@inheritDoc}
     */
    public function getPspUserId(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPspUserId', []);

        return parent::getPspUserId();
    }

    /**
     * {@inheritDoc}
     */
    public function setPspUserId(?string $pspUserId): \Wizacha\Marketplace\CreditCard\CreditCard
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPspUserId', [$pspUserId]);

        return parent::setPspUserId($pspUserId);
    }

    /**
     * {@inheritDoc}
     */
    public function jsonSerialize(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'jsonSerialize', []);

        return parent::jsonSerialize();
    }

    /**
     * {@inheritDoc}
     */
    public function getId(): ?string
    {
        if ($this->__isInitialized__ === false) {
            return  parent::getId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function defineGuid(): \Wizacha\Marketplace\CreditCard\CreditCard
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'defineGuid', []);

        return parent::defineGuid();
    }

}
