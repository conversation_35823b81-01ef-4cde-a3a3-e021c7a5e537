<?php

namespace Proxies\__CG__\Wizacha\Marketplace\Order\Refund\Entity;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Refund extends \Wizacha\Marketplace\Order\Refund\Entity\Refund implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', 'id', 'orderId', 'userId', 'isPartial', 'hasShipping', 'amount', 'shippingAmount', 'basketDiscount', 'marketplaceDiscount', 'status', 'isLegacy', 'creditNoteReference', 'message', 'items', 'refundAmounts', 'refundedAfterWithdrawalPeriod', 'createdAt', 'updatedAt'];
        }

        return ['__isInitialized__', 'id', 'orderId', 'userId', 'isPartial', 'hasShipping', 'amount', 'shippingAmount', 'basketDiscount', 'marketplaceDiscount', 'status', 'isLegacy', 'creditNoteReference', 'message', 'items', 'refundAmounts', 'refundedAfterWithdrawalPeriod', 'createdAt', 'updatedAt'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (Refund $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getId(): ?int
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function setId(int $id): \Wizacha\Marketplace\Order\Refund\Entity\Refund
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setId', [$id]);

        return parent::setId($id);
    }

    /**
     * {@inheritDoc}
     */
    public function getOrderId(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOrderId', []);

        return parent::getOrderId();
    }

    /**
     * {@inheritDoc}
     */
    public function setOrderId(int $orderId): \Wizacha\Marketplace\Order\Refund\Entity\Refund
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setOrderId', [$orderId]);

        return parent::setOrderId($orderId);
    }

    /**
     * {@inheritDoc}
     */
    public function getUserId(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUserId', []);

        return parent::getUserId();
    }

    /**
     * {@inheritDoc}
     */
    public function setUserId(int $userId = NULL): \Wizacha\Marketplace\Order\Refund\Entity\Refund
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setUserId', [$userId]);

        return parent::setUserId($userId);
    }

    /**
     * {@inheritDoc}
     */
    public function isPartial(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isPartial', []);

        return parent::isPartial();
    }

    /**
     * {@inheritDoc}
     */
    public function setIsPartial(bool $isPartial): \Wizacha\Marketplace\Order\Refund\Entity\Refund
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIsPartial', [$isPartial]);

        return parent::setIsPartial($isPartial);
    }

    /**
     * {@inheritDoc}
     */
    public function hasShipping(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasShipping', []);

        return parent::hasShipping();
    }

    /**
     * {@inheritDoc}
     */
    public function setHasShipping(bool $hasShipping): \Wizacha\Marketplace\Order\Refund\Entity\Refund
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setHasShipping', [$hasShipping]);

        return parent::setHasShipping($hasShipping);
    }

    /**
     * {@inheritDoc}
     */
    public function getAmount(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAmount', []);

        return parent::getAmount();
    }

    /**
     * {@inheritDoc}
     */
    public function setAmount(\Wizacha\Money\Money $amount): \Wizacha\Marketplace\Order\Refund\Entity\Refund
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAmount', [$amount]);

        return parent::setAmount($amount);
    }

    /**
     * {@inheritDoc}
     */
    public function getShippingAmount(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getShippingAmount', []);

        return parent::getShippingAmount();
    }

    /**
     * {@inheritDoc}
     */
    public function setShippingAmount(\Wizacha\Money\Money $shippingAmount): \Wizacha\Marketplace\Order\Refund\Entity\Refund
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setShippingAmount', [$shippingAmount]);

        return parent::setShippingAmount($shippingAmount);
    }

    /**
     * {@inheritDoc}
     */
    public function getBasketDiscount(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getBasketDiscount', []);

        return parent::getBasketDiscount();
    }

    /**
     * {@inheritDoc}
     */
    public function setBasketDiscount(\Wizacha\Money\Money $basketDiscount): \Wizacha\Marketplace\Order\Refund\Entity\Refund
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setBasketDiscount', [$basketDiscount]);

        return parent::setBasketDiscount($basketDiscount);
    }

    /**
     * {@inheritDoc}
     */
    public function getMarketplaceDiscount(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getMarketplaceDiscount', []);

        return parent::getMarketplaceDiscount();
    }

    /**
     * {@inheritDoc}
     */
    public function setMarketplaceDiscount(\Wizacha\Money\Money $marketplaceDiscount): \Wizacha\Marketplace\Order\Refund\Entity\Refund
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setMarketplaceDiscount', [$marketplaceDiscount]);

        return parent::setMarketplaceDiscount($marketplaceDiscount);
    }

    /**
     * {@inheritDoc}
     */
    public function getStatus(): \Wizacha\Marketplace\Order\Refund\Enum\RefundStatus
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getStatus', []);

        return parent::getStatus();
    }

    /**
     * {@inheritDoc}
     */
    public function setStatus(\Wizacha\Marketplace\Order\Refund\Enum\RefundStatus $status): \Wizacha\Marketplace\Order\Refund\Entity\Refund
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setStatus', [$status]);

        return parent::setStatus($status);
    }

    /**
     * {@inheritDoc}
     */
    public function isLegacy(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isLegacy', []);

        return parent::isLegacy();
    }

    /**
     * {@inheritDoc}
     */
    public function setIsLegacy(bool $isLegacy): \Wizacha\Marketplace\Order\Refund\Entity\Refund
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIsLegacy', [$isLegacy]);

        return parent::setIsLegacy($isLegacy);
    }

    /**
     * {@inheritDoc}
     */
    public function getCreditNoteReference(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCreditNoteReference', []);

        return parent::getCreditNoteReference();
    }

    /**
     * {@inheritDoc}
     */
    public function setCreditNoteReference(?string $creditNoteReference): \Wizacha\Marketplace\Order\Refund\Entity\Refund
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCreditNoteReference', [$creditNoteReference]);

        return parent::setCreditNoteReference($creditNoteReference);
    }

    /**
     * {@inheritDoc}
     */
    public function getMessage(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getMessage', []);

        return parent::getMessage();
    }

    /**
     * {@inheritDoc}
     */
    public function setMessage(?string $message): \Wizacha\Marketplace\Order\Refund\Entity\Refund
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setMessage', [$message]);

        return parent::setMessage($message);
    }

    /**
     * {@inheritDoc}
     */
    public function getItems(): iterable
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getItems', []);

        return parent::getItems();
    }

    /**
     * {@inheritDoc}
     */
    public function addItem(\Wizacha\Marketplace\Order\Refund\Entity\RefundItem $item): \Wizacha\Marketplace\Order\Refund\Entity\Refund
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addItem', [$item]);

        return parent::addItem($item);
    }

    /**
     * {@inheritDoc}
     */
    public function setItems(iterable $items): \Wizacha\Marketplace\Order\Refund\Entity\Refund
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setItems', [$items]);

        return parent::setItems($items);
    }

    /**
     * {@inheritDoc}
     */
    public function setRefundAmounts(\Wizacha\Marketplace\Order\Refund\Utils\RefundAmounts $refundAmounts): \Wizacha\Marketplace\Order\Refund\Entity\Refund
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setRefundAmounts', [$refundAmounts]);

        return parent::setRefundAmounts($refundAmounts);
    }

    /**
     * {@inheritDoc}
     */
    public function getRefundAmounts(): \Wizacha\Marketplace\Order\Refund\Utils\RefundAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRefundAmounts', []);

        return parent::getRefundAmounts();
    }

    /**
     * {@inheritDoc}
     */
    public function setRefundedAfterWithdrawalPeriod(bool $refundedAfterWithdrawalPeriod): \Wizacha\Marketplace\Order\Refund\Entity\Refund
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setRefundedAfterWithdrawalPeriod', [$refundedAfterWithdrawalPeriod]);

        return parent::setRefundedAfterWithdrawalPeriod($refundedAfterWithdrawalPeriod);
    }

    /**
     * {@inheritDoc}
     */
    public function isRefundedAfterWithdrawalPeriod(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isRefundedAfterWithdrawalPeriod', []);

        return parent::isRefundedAfterWithdrawalPeriod();
    }

    /**
     * {@inheritDoc}
     */
    public function jsonSerialize(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'jsonSerialize', []);

        return parent::jsonSerialize();
    }

    /**
     * {@inheritDoc}
     */
    public function getCreatedAt(): \DateTimeImmutable
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCreatedAt', []);

        return parent::getCreatedAt();
    }

    /**
     * {@inheritDoc}
     */
    public function setCreatedAt(\DateTime $createdAt): \Wizacha\Marketplace\Order\Refund\Entity\Refund
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCreatedAt', [$createdAt]);

        return parent::setCreatedAt($createdAt);
    }

    /**
     * {@inheritDoc}
     */
    public function getUpdatedAt(): \DateTimeImmutable
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUpdatedAt', []);

        return parent::getUpdatedAt();
    }

    /**
     * {@inheritDoc}
     */
    public function updateTimestamp()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'updateTimestamp', []);

        return parent::updateTimestamp();
    }

}
