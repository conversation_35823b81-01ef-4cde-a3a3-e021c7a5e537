<?php

namespace Proxies\__CG__\Wizacha\Marketplace\Subscription;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Subscription extends \Wizacha\Marketplace\Subscription\Subscription implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', 'id', 'firstOrderId', 'creditCard', 'user', 'companyId', 'name', 'status', 'price', 'isAutoRenew', 'createdAt', 'commitmentPeriod', 'paymentFrequency', 'nextPaymentAt', 'commitmentEndAt', 'orderItems', 'subscriptionActionTrace', 'renewAttemptsCount'];
        }

        return ['__isInitialized__', 'id', 'firstOrderId', 'creditCard', 'user', 'companyId', 'name', 'status', 'price', 'isAutoRenew', 'createdAt', 'commitmentPeriod', 'paymentFrequency', 'nextPaymentAt', 'commitmentEndAt', 'orderItems', 'subscriptionActionTrace', 'renewAttemptsCount'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (Subscription $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getFirstOrderId(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getFirstOrderId', []);

        return parent::getFirstOrderId();
    }

    /**
     * {@inheritDoc}
     */
    public function setFirstOrderId(int $firstOrderId): \Wizacha\Marketplace\Subscription\Subscription
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setFirstOrderId', [$firstOrderId]);

        return parent::setFirstOrderId($firstOrderId);
    }

    /**
     * {@inheritDoc}
     */
    public function getCreditCard(): ?\Wizacha\Marketplace\CreditCard\CreditCard
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCreditCard', []);

        return parent::getCreditCard();
    }

    /**
     * {@inheritDoc}
     */
    public function setCreditCard(?\Wizacha\Marketplace\CreditCard\CreditCard $creditCard): \Wizacha\Marketplace\Subscription\Subscription
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCreditCard', [$creditCard]);

        return parent::setCreditCard($creditCard);
    }

    /**
     * {@inheritDoc}
     */
    public function getUser(): ?\Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUser', []);

        return parent::getUser();
    }

    /**
     * {@inheritDoc}
     */
    public function setUser(?\Wizacha\Marketplace\User\User $user): \Wizacha\Marketplace\Subscription\Subscription
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setUser', [$user]);

        return parent::setUser($user);
    }

    /**
     * {@inheritDoc}
     */
    public function getCompanyId(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCompanyId', []);

        return parent::getCompanyId();
    }

    /**
     * {@inheritDoc}
     */
    public function setCompanyId(int $companyId): \Wizacha\Marketplace\Subscription\Subscription
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCompanyId', [$companyId]);

        return parent::setCompanyId($companyId);
    }

    /**
     * {@inheritDoc}
     */
    public function getName(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getName', []);

        return parent::getName();
    }

    /**
     * {@inheritDoc}
     */
    public function setName(string $name): \Wizacha\Marketplace\Subscription\Subscription
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setName', [$name]);

        return parent::setName($name);
    }

    /**
     * {@inheritDoc}
     */
    public function getStatus(): ?\Wizacha\Marketplace\Subscription\SubscriptionStatus
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getStatus', []);

        return parent::getStatus();
    }

    /**
     * {@inheritDoc}
     */
    public function setStatus(\Wizacha\Marketplace\Subscription\SubscriptionStatus $status): \Wizacha\Marketplace\Subscription\Subscription
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setStatus', [$status]);

        return parent::setStatus($status);
    }

    /**
     * {@inheritDoc}
     */
    public function getPrice(): ?\Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPrice', []);

        return parent::getPrice();
    }

    /**
     * {@inheritDoc}
     */
    public function setPrice(\Wizacha\Money\Money $price): \Wizacha\Marketplace\Subscription\Subscription
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPrice', [$price]);

        return parent::setPrice($price);
    }

    /**
     * {@inheritDoc}
     */
    public function isAutoRenew(): ?bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isAutoRenew', []);

        return parent::isAutoRenew();
    }

    /**
     * {@inheritDoc}
     */
    public function setIsAutoRenew(bool $isAutoRenew): \Wizacha\Marketplace\Subscription\Subscription
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIsAutoRenew', [$isAutoRenew]);

        return parent::setIsAutoRenew($isAutoRenew);
    }

    /**
     * {@inheritDoc}
     */
    public function getCreatedAt(): ?\DateTimeImmutable
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCreatedAt', []);

        return parent::getCreatedAt();
    }

    /**
     * {@inheritDoc}
     */
    public function defineCreatedAt(): \Wizacha\Marketplace\Subscription\Subscription
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'defineCreatedAt', []);

        return parent::defineCreatedAt();
    }

    /**
     * {@inheritDoc}
     */
    public function getCommitmentPeriod(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCommitmentPeriod', []);

        return parent::getCommitmentPeriod();
    }

    /**
     * {@inheritDoc}
     */
    public function setCommitmentPeriod(int $commitmentPeriod): \Wizacha\Marketplace\Subscription\Subscription
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCommitmentPeriod', [$commitmentPeriod]);

        return parent::setCommitmentPeriod($commitmentPeriod);
    }

    /**
     * {@inheritDoc}
     */
    public function getPaymentFrequency(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPaymentFrequency', []);

        return parent::getPaymentFrequency();
    }

    /**
     * {@inheritDoc}
     */
    public function setPaymentFrequency(int $paymentFrequency): \Wizacha\Marketplace\Subscription\Subscription
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPaymentFrequency', [$paymentFrequency]);

        return parent::setPaymentFrequency($paymentFrequency);
    }

    /**
     * {@inheritDoc}
     */
    public function getNextPaymentAt(): ?\DateTimeImmutable
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNextPaymentAt', []);

        return parent::getNextPaymentAt();
    }

    /**
     * {@inheritDoc}
     */
    public function setNextPaymentAt(\DateTimeImmutable $nextPaymentAt): \Wizacha\Marketplace\Subscription\Subscription
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNextPaymentAt', [$nextPaymentAt]);

        return parent::setNextPaymentAt($nextPaymentAt);
    }

    /**
     * {@inheritDoc}
     */
    public function getCommitmentEndAt(): ?\DateTimeImmutable
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCommitmentEndAt', []);

        return parent::getCommitmentEndAt();
    }

    /**
     * {@inheritDoc}
     */
    public function setCommitmentEndAt(\DateTimeImmutable $commitmentEndAt): \Wizacha\Marketplace\Subscription\Subscription
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCommitmentEndAt', [$commitmentEndAt]);

        return parent::setCommitmentEndAt($commitmentEndAt);
    }

    /**
     * {@inheritDoc}
     */
    public function getRenewAttemptsCount(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRenewAttemptsCount', []);

        return parent::getRenewAttemptsCount();
    }

    /**
     * {@inheritDoc}
     */
    public function setRenewAttemptsCount(int $renewAttemptsCount): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setRenewAttemptsCount', [$renewAttemptsCount]);

        parent::setRenewAttemptsCount($renewAttemptsCount);
    }

    /**
     * {@inheritDoc}
     */
    public function incrementRenewAttemptsCount(): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'incrementRenewAttemptsCount', []);

        parent::incrementRenewAttemptsCount();
    }

    /**
     * {@inheritDoc}
     */
    public function getOrderItems(): \Doctrine\Common\Collections\Collection
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOrderItems', []);

        return parent::getOrderItems();
    }

    /**
     * {@inheritDoc}
     */
    public function setOrderItems(iterable $orderItems): \Wizacha\Marketplace\Subscription\Subscription
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setOrderItems', [$orderItems]);

        return parent::setOrderItems($orderItems);
    }

    /**
     * {@inheritDoc}
     */
    public function addOrderItem(\Wizacha\Marketplace\Subscription\OrderItem $orderItem): \Wizacha\Marketplace\Subscription\Subscription
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addOrderItem', [$orderItem]);

        return parent::addOrderItem($orderItem);
    }

    /**
     * {@inheritDoc}
     */
    public function removeOrderItem(\Wizacha\Marketplace\Subscription\OrderItem $orderItem): \Wizacha\Marketplace\Subscription\Subscription
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'removeOrderItem', [$orderItem]);

        return parent::removeOrderItem($orderItem);
    }

    /**
     * {@inheritDoc}
     */
    public function clearOrderItems(): \Wizacha\Marketplace\Subscription\Subscription
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'clearOrderItems', []);

        return parent::clearOrderItems();
    }

    /**
     * {@inheritDoc}
     */
    public function getPriceDetails(array $orderItems): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPriceDetails', [$orderItems]);

        return parent::getPriceDetails($orderItems);
    }

    /**
     * {@inheritDoc}
     */
    public function getSubscriptionActionTraces(): \Doctrine\Common\Collections\Collection
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSubscriptionActionTraces', []);

        return parent::getSubscriptionActionTraces();
    }

    /**
     * {@inheritDoc}
     */
    public function expose(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'expose', []);

        return parent::expose();
    }

    /**
     * {@inheritDoc}
     */
    public function jsonSerialize(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'jsonSerialize', []);

        return parent::jsonSerialize();
    }

    /**
     * {@inheritDoc}
     */
    public function getId(): ?string
    {
        if ($this->__isInitialized__ === false) {
            return  parent::getId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function defineGuid(): \Wizacha\Marketplace\Subscription\Subscription
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'defineGuid', []);

        return parent::defineGuid();
    }

}
