<?php

namespace Proxies\__CG__\Wizacha\Marketplace\Order\Refund\Entity;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class RefundItem extends \Wizacha\Marketplace\Order\Refund\Entity\RefundItem implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', 'id', 'refund', 'itemId', 'amount', 'quantity', 'amounts', 'line', 'discountedLine', 'createdAt', 'updatedAt'];
        }

        return ['__isInitialized__', 'id', 'refund', 'itemId', 'amount', 'quantity', 'amounts', 'line', 'discountedLine', 'createdAt', 'updatedAt'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (RefundItem $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getId(): ?int
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function setId(int $id): \Wizacha\Marketplace\Order\Refund\Entity\RefundItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setId', [$id]);

        return parent::setId($id);
    }

    /**
     * {@inheritDoc}
     */
    public function getRefund(): \Wizacha\Marketplace\Order\Refund\Entity\Refund
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRefund', []);

        return parent::getRefund();
    }

    /**
     * {@inheritDoc}
     */
    public function setRefund(\Wizacha\Marketplace\Order\Refund\Entity\Refund $refund): \Wizacha\Marketplace\Order\Refund\Entity\RefundItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setRefund', [$refund]);

        return parent::setRefund($refund);
    }

    /**
     * {@inheritDoc}
     */
    public function getItemId(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getItemId', []);

        return parent::getItemId();
    }

    /**
     * {@inheritDoc}
     */
    public function setItemId(int $itemId): \Wizacha\Marketplace\Order\Refund\Entity\RefundItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setItemId', [$itemId]);

        return parent::setItemId($itemId);
    }

    /**
     * {@inheritDoc}
     */
    public function getAmount(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAmount', []);

        return parent::getAmount();
    }

    /**
     * {@inheritDoc}
     */
    public function setAmount(\Wizacha\Money\Money $amount): \Wizacha\Marketplace\Order\Refund\Entity\RefundItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAmount', [$amount]);

        return parent::setAmount($amount);
    }

    /**
     * {@inheritDoc}
     */
    public function getQuantity(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getQuantity', []);

        return parent::getQuantity();
    }

    /**
     * {@inheritDoc}
     */
    public function setQuantity(int $quantity): \Wizacha\Marketplace\Order\Refund\Entity\RefundItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setQuantity', [$quantity]);

        return parent::setQuantity($quantity);
    }

    /**
     * {@inheritDoc}
     */
    public function getLine(): ?\Wizacha\Component\Order\Line
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLine', []);

        return parent::getLine();
    }

    /**
     * {@inheritDoc}
     */
    public function getDiscountedLine(): ?\Wizacha\Component\Order\Line
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDiscountedLine', []);

        return parent::getDiscountedLine();
    }

    /**
     * {@inheritDoc}
     */
    public function getAmounts(): ?\Wizacha\Component\Order\Amounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAmounts', []);

        return parent::getAmounts();
    }

    /**
     * {@inheritDoc}
     */
    public function initializeAmounts(\Wizacha\Marketplace\Order\OrderItem $orderItem, int $quantity): \Wizacha\Marketplace\Order\Refund\Entity\RefundItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'initializeAmounts', [$orderItem, $quantity]);

        return parent::initializeAmounts($orderItem, $quantity);
    }

    /**
     * {@inheritDoc}
     */
    public function getCreatedAt(): \DateTimeImmutable
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCreatedAt', []);

        return parent::getCreatedAt();
    }

    /**
     * {@inheritDoc}
     */
    public function setCreatedAt(\DateTime $createdAt): \Wizacha\Marketplace\Order\Refund\Entity\RefundItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCreatedAt', [$createdAt]);

        return parent::setCreatedAt($createdAt);
    }

    /**
     * {@inheritDoc}
     */
    public function getUpdatedAt(): \DateTimeImmutable
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUpdatedAt', []);

        return parent::getUpdatedAt();
    }

    /**
     * {@inheritDoc}
     */
    public function updateTimestamp()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'updateTimestamp', []);

        return parent::updateTimestamp();
    }

}
