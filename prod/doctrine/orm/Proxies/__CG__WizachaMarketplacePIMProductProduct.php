<?php

namespace Proxies\__CG__\Wizacha\Marketplace\PIM\Product;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Product extends \Wizacha\Marketplace\PIM\Product\Product implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', 'id', 'code', 'type', 'status', 'companyId', 'approved', 'listPrice', 'amount', 'weight', 'length', 'width', 'height', 'shippingFreight', 'lowAvailableLimit', 'timestamp', 'updatedTimestamp', 'usergroupIds', 'isEdp', 'edpShipping', 'unlimitedDownload', 'tracking', 'freeShipping', 'featureComparison', 'zeroPriceAction', 'isPbp', 'isOp', 'isOper', 'isReturnable', 'returnPeriod', 'availableSince', 'outOfStockActions', 'localization', 'minQuantity', 'maxQuantity', 'quantityStep', 'listQuantityCount', 'taxIds', 'ageVerification', 'ageLimit', 'optionsType', 'exceptionsType', 'detailsLayout', 'shippingParams', 'disableShippings', 'supplierRef', 'greenTax', 'condition', 'crossedOutPrice', 'affiliateLink', 'transactionMode', 'productTemplateType', 'link', 'maxPriceAdjustment', 'isSubscription', 'isRenewable', 'descriptions', 'categories', 'attachments', 'priceTiers', 'quoteRequestMinQuantity', 'isQuoteRequestExclusive'];
        }

        return ['__isInitialized__', 'id', 'code', 'type', 'status', 'companyId', 'approved', 'listPrice', 'amount', 'weight', 'length', 'width', 'height', 'shippingFreight', 'lowAvailableLimit', 'timestamp', 'updatedTimestamp', 'usergroupIds', 'isEdp', 'edpShipping', 'unlimitedDownload', 'tracking', 'freeShipping', 'featureComparison', 'zeroPriceAction', 'isPbp', 'isOp', 'isOper', 'isReturnable', 'returnPeriod', 'availableSince', 'outOfStockActions', 'localization', 'minQuantity', 'maxQuantity', 'quantityStep', 'listQuantityCount', 'taxIds', 'ageVerification', 'ageLimit', 'optionsType', 'exceptionsType', 'detailsLayout', 'shippingParams', 'disableShippings', 'supplierRef', 'greenTax', 'condition', 'crossedOutPrice', 'affiliateLink', 'transactionMode', 'productTemplateType', 'link', 'maxPriceAdjustment', 'isSubscription', 'isRenewable', 'descriptions', 'categories', 'attachments', 'priceTiers', 'quoteRequestMinQuantity', 'isQuoteRequestExclusive'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (Product $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function __toString()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, '__toString', []);

        return parent::__toString();
    }

    /**
     * {@inheritDoc}
     */
    public function getId(): int
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function getCode(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCode', []);

        return parent::getCode();
    }

    /**
     * {@inheritDoc}
     */
    public function getSupplierRef(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSupplierRef', []);

        return parent::getSupplierRef();
    }

    /**
     * {@inheritDoc}
     */
    public function getType(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getType', []);

        return parent::getType();
    }

    /**
     * {@inheritDoc}
     */
    public function getStatus(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getStatus', []);

        return parent::getStatus();
    }

    /**
     * {@inheritDoc}
     */
    public function getCompanyId(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCompanyId', []);

        return parent::getCompanyId();
    }

    /**
     * {@inheritDoc}
     */
    public function getApproved(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getApproved', []);

        return parent::getApproved();
    }

    /**
     * {@inheritDoc}
     */
    public function getLink(): ?\Wizacha\Marketplace\PIM\MultiVendorProduct\Link
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLink', []);

        return parent::getLink();
    }

    /**
     * {@inheritDoc}
     */
    public function setLink(\Wizacha\Marketplace\PIM\MultiVendorProduct\Link $link)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setLink', [$link]);

        return parent::setLink($link);
    }

    /**
     * {@inheritDoc}
     */
    public function removeLink()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'removeLink', []);

        return parent::removeLink();
    }

    /**
     * {@inheritDoc}
     */
    public function getMultiVendorProduct(): ?\Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getMultiVendorProduct', []);

        return parent::getMultiVendorProduct();
    }

    /**
     * {@inheritDoc}
     */
    public function getName(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getName', []);

        return parent::getName();
    }

    /**
     * {@inheritDoc}
     */
    public function getShortDescription(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getShortDescription', []);

        return parent::getShortDescription();
    }

    /**
     * {@inheritDoc}
     */
    public function getDescription(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDescription', []);

        return parent::getDescription();
    }

    /**
     * {@inheritDoc}
     */
    public function getCategory()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCategory', []);

        return parent::getCategory();
    }

    /**
     * {@inheritDoc}
     */
    public function getCategories(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCategories', []);

        return parent::getCategories();
    }

    /**
     * {@inheritDoc}
     */
    public function getImageIds(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getImageIds', []);

        return parent::getImageIds();
    }

    /**
     * {@inheritDoc}
     */
    public function getImages(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getImages', []);

        return parent::getImages();
    }

    /**
     * {@inheritDoc}
     */
    public function attachments(): \Doctrine\Common\Collections\Collection
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'attachments', []);

        return parent::attachments();
    }

    /**
     * {@inheritDoc}
     */
    public function getGreenTax(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getGreenTax', []);

        return parent::getGreenTax();
    }

    /**
     * {@inheritDoc}
     */
    public function getProductTemplateType(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProductTemplateType', []);

        return parent::getProductTemplateType();
    }

    /**
     * {@inheritDoc}
     */
    public function getWeight(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getWeight', []);

        return parent::getWeight();
    }

    /**
     * {@inheritDoc}
     */
    public function getMaxPriceAdjustment(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getMaxPriceAdjustment', []);

        return parent::getMaxPriceAdjustment();
    }

    /**
     * {@inheritDoc}
     */
    public function getPriceTiers(): \Doctrine\Common\Collections\Collection
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPriceTiers', []);

        return parent::getPriceTiers();
    }

    /**
     * {@inheritDoc}
     */
    public function getUpdatedTimestamp(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUpdatedTimestamp', []);

        return parent::getUpdatedTimestamp();
    }

    /**
     * {@inheritDoc}
     */
    public function addPriceTier(\Wizacha\Marketplace\PriceTier\PriceTier $priceTier): \Wizacha\Marketplace\PIM\Product\Product
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addPriceTier', [$priceTier]);

        return parent::addPriceTier($priceTier);
    }

    /**
     * {@inheritDoc}
     */
    public function removePriceTier(\Wizacha\Marketplace\PriceTier\PriceTier $priceTier): \Wizacha\Marketplace\PIM\Product\Product
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'removePriceTier', [$priceTier]);

        return parent::removePriceTier($priceTier);
    }

    /**
     * {@inheritDoc}
     */
    public function isSubscription(): ?bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isSubscription', []);

        return parent::isSubscription();
    }

    /**
     * {@inheritDoc}
     */
    public function setIsSubscription(bool $isSubscription): \Wizacha\Marketplace\PIM\Product\Product
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIsSubscription', [$isSubscription]);

        return parent::setIsSubscription($isSubscription);
    }

    /**
     * {@inheritDoc}
     */
    public function isRenewable(): ?bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isRenewable', []);

        return parent::isRenewable();
    }

    /**
     * {@inheritDoc}
     */
    public function setIsRenewable(bool $isRenewable): \Wizacha\Marketplace\PIM\Product\Product
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIsRenewable', [$isRenewable]);

        return parent::setIsRenewable($isRenewable);
    }

}
