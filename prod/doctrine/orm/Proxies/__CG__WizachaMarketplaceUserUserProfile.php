<?php

namespace Proxies\__CG__\Wizacha\Marketplace\User;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class UserProfile extends \Wizacha\Marketplace\User\UserProfile implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\User\\UserProfile' . "\0" . 'profileId', '' . "\0" . 'Wizacha\\Marketplace\\User\\UserProfile' . "\0" . 'userId', '' . "\0" . 'Wizacha\\Marketplace\\User\\UserProfile' . "\0" . 'profileType', '' . "\0" . 'Wizacha\\Marketplace\\User\\UserProfile' . "\0" . 'externalIdentifier', '' . "\0" . 'Wizacha\\Marketplace\\User\\UserProfile' . "\0" . 'isProfessional', '' . "\0" . 'Wizacha\\Marketplace\\User\\UserProfile' . "\0" . 'legalIdentifier'];
        }

        return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\User\\UserProfile' . "\0" . 'profileId', '' . "\0" . 'Wizacha\\Marketplace\\User\\UserProfile' . "\0" . 'userId', '' . "\0" . 'Wizacha\\Marketplace\\User\\UserProfile' . "\0" . 'profileType', '' . "\0" . 'Wizacha\\Marketplace\\User\\UserProfile' . "\0" . 'externalIdentifier', '' . "\0" . 'Wizacha\\Marketplace\\User\\UserProfile' . "\0" . 'isProfessional', '' . "\0" . 'Wizacha\\Marketplace\\User\\UserProfile' . "\0" . 'legalIdentifier'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (UserProfile $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function setProfileId(int $profileId): \Wizacha\Marketplace\User\UserProfile
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setProfileId', [$profileId]);

        return parent::setProfileId($profileId);
    }

    /**
     * {@inheritDoc}
     */
    public function getProfileId(): int
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getProfileId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProfileId', []);

        return parent::getProfileId();
    }

    /**
     * {@inheritDoc}
     */
    public function getUserId(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUserId', []);

        return parent::getUserId();
    }

    /**
     * {@inheritDoc}
     */
    public function setUserId($userId): \Wizacha\Marketplace\User\UserProfile
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setUserId', [$userId]);

        return parent::setUserId($userId);
    }

    /**
     * {@inheritDoc}
     */
    public function getProfileType(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProfileType', []);

        return parent::getProfileType();
    }

    /**
     * {@inheritDoc}
     */
    public function setProfileType(string $profileType): \Wizacha\Marketplace\User\UserProfile
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setProfileType', [$profileType]);

        return parent::setProfileType($profileType);
    }

    /**
     * {@inheritDoc}
     */
    public function getExternalIdentifier(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getExternalIdentifier', []);

        return parent::getExternalIdentifier();
    }

    /**
     * {@inheritDoc}
     */
    public function setExternalIdentifier(string $externalIdentifier): \Wizacha\Marketplace\User\UserProfile
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setExternalIdentifier', [$externalIdentifier]);

        return parent::setExternalIdentifier($externalIdentifier);
    }

    /**
     * {@inheritDoc}
     */
    public function getIsProfessional(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getIsProfessional', []);

        return parent::getIsProfessional();
    }

    /**
     * {@inheritDoc}
     */
    public function setIsProfessional(bool $isProfessional): \Wizacha\Marketplace\User\UserProfile
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIsProfessional', [$isProfessional]);

        return parent::setIsProfessional($isProfessional);
    }

    /**
     * {@inheritDoc}
     */
    public function getLegalIdentifier(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLegalIdentifier', []);

        return parent::getLegalIdentifier();
    }

    /**
     * {@inheritDoc}
     */
    public function setLegalIdentifier(string $legalIdentifier): \Wizacha\Marketplace\User\UserProfile
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setLegalIdentifier', [$legalIdentifier]);

        return parent::setLegalIdentifier($legalIdentifier);
    }

}
