<?php

namespace Proxies\__CG__\Wizacha\Marketplace\Promotion;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Promotion extends \Wizacha\Marketplace\Promotion\Promotion implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\Promotion\\Promotion' . "\0" . 'id', '' . "\0" . 'Wizacha\\Marketplace\\Promotion\\Promotion' . "\0" . 'companyId', '' . "\0" . 'Wizacha\\Marketplace\\Promotion\\Promotion' . "\0" . 'active', '' . "\0" . 'Wizacha\\Marketplace\\Promotion\\Promotion' . "\0" . 'name', '' . "\0" . 'Wizacha\\Marketplace\\Promotion\\Promotion' . "\0" . 'type', '' . "\0" . 'Wizacha\\Marketplace\\Promotion\\Promotion' . "\0" . 'rules', '' . "\0" . 'Wizacha\\Marketplace\\Promotion\\Promotion' . "\0" . 'startTime', '' . "\0" . 'Wizacha\\Marketplace\\Promotion\\Promotion' . "\0" . 'endTime', '' . "\0" . 'Wizacha\\Marketplace\\Promotion\\Promotion' . "\0" . 'bonuses', '' . "\0" . 'Wizacha\\Marketplace\\Promotion\\Promotion' . "\0" . 'target', '' . "\0" . 'Wizacha\\Marketplace\\Promotion\\Promotion' . "\0" . 'isValid'];
        }

        return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\Promotion\\Promotion' . "\0" . 'id', '' . "\0" . 'Wizacha\\Marketplace\\Promotion\\Promotion' . "\0" . 'companyId', '' . "\0" . 'Wizacha\\Marketplace\\Promotion\\Promotion' . "\0" . 'active', '' . "\0" . 'Wizacha\\Marketplace\\Promotion\\Promotion' . "\0" . 'name', '' . "\0" . 'Wizacha\\Marketplace\\Promotion\\Promotion' . "\0" . 'type', '' . "\0" . 'Wizacha\\Marketplace\\Promotion\\Promotion' . "\0" . 'rules', '' . "\0" . 'Wizacha\\Marketplace\\Promotion\\Promotion' . "\0" . 'startTime', '' . "\0" . 'Wizacha\\Marketplace\\Promotion\\Promotion' . "\0" . 'endTime', '' . "\0" . 'Wizacha\\Marketplace\\Promotion\\Promotion' . "\0" . 'bonuses', '' . "\0" . 'Wizacha\\Marketplace\\Promotion\\Promotion' . "\0" . 'target', '' . "\0" . 'Wizacha\\Marketplace\\Promotion\\Promotion' . "\0" . 'isValid'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (Promotion $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getId(): ?string
    {
        if ($this->__isInitialized__ === false) {
            return  parent::getId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function setId(string $id): \Wizacha\Marketplace\Promotion\Promotion
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setId', [$id]);

        return parent::setId($id);
    }

    /**
     * {@inheritDoc}
     */
    public function getCompanyId(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCompanyId', []);

        return parent::getCompanyId();
    }

    /**
     * {@inheritDoc}
     */
    public function setCompanyId(?int $companyId): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCompanyId', [$companyId]);

        parent::setCompanyId($companyId);
    }

    /**
     * {@inheritDoc}
     */
    public function isActive(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isActive', []);

        return parent::isActive();
    }

    /**
     * {@inheritDoc}
     */
    public function setActive(bool $active): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setActive', [$active]);

        parent::setActive($active);
    }

    /**
     * {@inheritDoc}
     */
    public function getName(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getName', []);

        return parent::getName();
    }

    /**
     * {@inheritDoc}
     */
    public function setName(string $name): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setName', [$name]);

        parent::setName($name);
    }

    /**
     * {@inheritDoc}
     */
    public function getType(): \Wizacha\Marketplace\Promotion\PromotionType
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getType', []);

        return parent::getType();
    }

    /**
     * {@inheritDoc}
     */
    public function getRules(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRules', []);

        return parent::getRules();
    }

    /**
     * {@inheritDoc}
     */
    public function setRules(string $rules): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setRules', [$rules]);

        parent::setRules($rules);
    }

    /**
     * {@inheritDoc}
     */
    public function getBonuses(): \Doctrine\Common\Collections\Collection
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getBonuses', []);

        return parent::getBonuses();
    }

    /**
     * {@inheritDoc}
     */
    public function setBonuses(array $bonuses)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setBonuses', [$bonuses]);

        return parent::setBonuses($bonuses);
    }

    /**
     * {@inheritDoc}
     */
    public function getStartTime(): \DateTime
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getStartTime', []);

        return parent::getStartTime();
    }

    /**
     * {@inheritDoc}
     */
    public function getEndTime(): \DateTime
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getEndTime', []);

        return parent::getEndTime();
    }

    /**
     * {@inheritDoc}
     */
    public function setActivationPeriod(\DateTime $startTime, \DateTime $endTime): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setActivationPeriod', [$startTime, $endTime]);

        parent::setActivationPeriod($startTime, $endTime);
    }

    /**
     * {@inheritDoc}
     */
    public function getTarget(): \Wizacha\Marketplace\Promotion\BonusTarget\BonusTarget
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTarget', []);

        return parent::getTarget();
    }

    /**
     * {@inheritDoc}
     */
    public function setTarget(\Wizacha\Marketplace\Promotion\BonusTarget\BonusTarget $target): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTarget', [$target]);

        parent::setTarget($target);
    }

    /**
     * {@inheritDoc}
     */
    public function isValid(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isValid', []);

        return parent::isValid();
    }

    /**
     * {@inheritDoc}
     */
    public function setIsValid(bool $valid): \Wizacha\Marketplace\Promotion\Promotion
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIsValid', [$valid]);

        return parent::setIsValid($valid);
    }

    /**
     * {@inheritDoc}
     */
    public function hasTypeMarketplace(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasTypeMarketplace', []);

        return parent::hasTypeMarketplace();
    }

    /**
     * {@inheritDoc}
     */
    public function hasTypeCatalog(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasTypeCatalog', []);

        return parent::hasTypeCatalog();
    }

    /**
     * {@inheritDoc}
     */
    public function hasTypeBasket(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasTypeBasket', []);

        return parent::hasTypeBasket();
    }

}
