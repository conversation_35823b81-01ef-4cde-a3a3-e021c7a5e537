<?php

namespace Proxies\__CG__\Wizacha\Marketplace\Order\AmountsCalculator\Entity;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class ShippingAmounts extends \Wizacha\Marketplace\Order\AmountsCalculator\Entity\ShippingAmounts implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', 'id', 'shippingId', 'basketDiscount', 'marketplaceDiscount', 'totalExclTaxes', 'totalInclTaxes', 'taxRate', 'taxId', 'totalTaxAmount', 'discountedTotalExclTaxes', 'discountedTotalInclTaxes', 'discountedTotalTaxAmount'];
        }

        return ['__isInitialized__', 'id', 'shippingId', 'basketDiscount', 'marketplaceDiscount', 'totalExclTaxes', 'totalInclTaxes', 'taxRate', 'taxId', 'totalTaxAmount', 'discountedTotalExclTaxes', 'discountedTotalInclTaxes', 'discountedTotalTaxAmount'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (ShippingAmounts $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getId(): int
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function setId(int $id): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\ShippingAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setId', [$id]);

        return parent::setId($id);
    }

    /**
     * {@inheritDoc}
     */
    public function getShippingId(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getShippingId', []);

        return parent::getShippingId();
    }

    /**
     * {@inheritDoc}
     */
    public function setShippingId(int $shippingId): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\ShippingAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setShippingId', [$shippingId]);

        return parent::setShippingId($shippingId);
    }

    /**
     * {@inheritDoc}
     */
    public function getBasketDiscount(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getBasketDiscount', []);

        return parent::getBasketDiscount();
    }

    /**
     * {@inheritDoc}
     */
    public function setBasketDiscount(\Wizacha\Money\Money $discount): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\ShippingAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setBasketDiscount', [$discount]);

        return parent::setBasketDiscount($discount);
    }

    /**
     * {@inheritDoc}
     */
    public function getMarketplaceDiscount(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getMarketplaceDiscount', []);

        return parent::getMarketplaceDiscount();
    }

    /**
     * {@inheritDoc}
     */
    public function setMarketplaceDiscount(\Wizacha\Money\Money $marketplaceDiscount): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\ShippingAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setMarketplaceDiscount', [$marketplaceDiscount]);

        return parent::setMarketplaceDiscount($marketplaceDiscount);
    }

    /**
     * {@inheritDoc}
     */
    public function getTotalExclTaxes(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTotalExclTaxes', []);

        return parent::getTotalExclTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setTotalExclTaxes(\Wizacha\Money\Money $totalExclTaxes): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\ShippingAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTotalExclTaxes', [$totalExclTaxes]);

        return parent::setTotalExclTaxes($totalExclTaxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getTotalInclTaxes(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTotalInclTaxes', []);

        return parent::getTotalInclTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setTotalInclTaxes(\Wizacha\Money\Money $totalInclTaxes): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\ShippingAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTotalInclTaxes', [$totalInclTaxes]);

        return parent::setTotalInclTaxes($totalInclTaxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getTaxRate(): float
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTaxRate', []);

        return parent::getTaxRate();
    }

    /**
     * {@inheritDoc}
     */
    public function setTaxRate(float $taxRate): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\ShippingAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTaxRate', [$taxRate]);

        return parent::setTaxRate($taxRate);
    }

    /**
     * {@inheritDoc}
     */
    public function getTaxId(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTaxId', []);

        return parent::getTaxId();
    }

    /**
     * {@inheritDoc}
     */
    public function setTaxId(int $taxId): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\ShippingAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTaxId', [$taxId]);

        return parent::setTaxId($taxId);
    }

    /**
     * {@inheritDoc}
     */
    public function getTotalTaxAmount(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTotalTaxAmount', []);

        return parent::getTotalTaxAmount();
    }

    /**
     * {@inheritDoc}
     */
    public function setTotalTaxAmount(\Wizacha\Money\Money $totalTaxAmount): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\ShippingAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTotalTaxAmount', [$totalTaxAmount]);

        return parent::setTotalTaxAmount($totalTaxAmount);
    }

    /**
     * {@inheritDoc}
     */
    public function getDiscountedTotalExclTaxes(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDiscountedTotalExclTaxes', []);

        return parent::getDiscountedTotalExclTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setDiscountedTotalExclTaxes(\Wizacha\Money\Money $discountedTotalExclTaxes): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\ShippingAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDiscountedTotalExclTaxes', [$discountedTotalExclTaxes]);

        return parent::setDiscountedTotalExclTaxes($discountedTotalExclTaxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getDiscountedTotalInclTaxes(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDiscountedTotalInclTaxes', []);

        return parent::getDiscountedTotalInclTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setDiscountedTotalInclTaxes(\Wizacha\Money\Money $discountedTotalInclTaxes): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\ShippingAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDiscountedTotalInclTaxes', [$discountedTotalInclTaxes]);

        return parent::setDiscountedTotalInclTaxes($discountedTotalInclTaxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getDiscountedTotalTaxAmount(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDiscountedTotalTaxAmount', []);

        return parent::getDiscountedTotalTaxAmount();
    }

    /**
     * {@inheritDoc}
     */
    public function setDiscountedTotalTaxAmount(\Wizacha\Money\Money $discountedTotalTaxAmount): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\ShippingAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDiscountedTotalTaxAmount', [$discountedTotalTaxAmount]);

        return parent::setDiscountedTotalTaxAmount($discountedTotalTaxAmount);
    }

}
