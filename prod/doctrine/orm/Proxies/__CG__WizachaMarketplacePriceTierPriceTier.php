<?php

namespace Proxies\__CG__\Wizacha\Marketplace\PriceTier;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class PriceTier extends \Wizacha\Marketplace\PriceTier\PriceTier implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', 'id', 'product', 'productOptionInventory', 'lowerLimit', 'price', 'taxes', 'priceIncludeTax', 'productIdLowerLimitOptionId'];
        }

        return ['__isInitialized__', 'id', 'product', 'productOptionInventory', 'lowerLimit', 'price', 'taxes', 'priceIncludeTax', 'productIdLowerLimitOptionId'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (PriceTier $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * {@inheritDoc}
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);

        parent::__clone();
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getId(): ?int
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function hasId(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasId', []);

        return parent::hasId();
    }

    /**
     * {@inheritDoc}
     */
    public function isDefault(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isDefault', []);

        return parent::isDefault();
    }

    /**
     * {@inheritDoc}
     */
    public function setProduct(\Wizacha\Marketplace\PIM\Product\Product $product): \Wizacha\Marketplace\PriceTier\PriceTier
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setProduct', [$product]);

        return parent::setProduct($product);
    }

    /**
     * {@inheritDoc}
     */
    public function getProduct(): \Wizacha\Marketplace\PIM\Product\Product
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProduct', []);

        return parent::getProduct();
    }

    /**
     * {@inheritDoc}
     */
    public function setProductOptionInventory(?\Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory $productOptionInventory): \Wizacha\Marketplace\PriceTier\PriceTier
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setProductOptionInventory', [$productOptionInventory]);

        return parent::setProductOptionInventory($productOptionInventory);
    }

    /**
     * {@inheritDoc}
     */
    public function getProductOptionInventory(): ?\Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProductOptionInventory', []);

        return parent::getProductOptionInventory();
    }

    /**
     * {@inheritDoc}
     */
    public function setLowerLimit(int $lowerLimit): \Wizacha\Marketplace\PriceTier\PriceTier
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setLowerLimit', [$lowerLimit]);

        return parent::setLowerLimit($lowerLimit);
    }

    /**
     * {@inheritDoc}
     */
    public function getLowerLimit(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLowerLimit', []);

        return parent::getLowerLimit();
    }

    /**
     * {@inheritDoc}
     */
    public function setPrice(\Wizacha\Money\Money $price): \Wizacha\Marketplace\PriceTier\PriceTier
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPrice', [$price]);

        return parent::setPrice($price);
    }

    /**
     * {@inheritDoc}
     */
    public function getPrice(): ?\Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPrice', []);

        return parent::getPrice();
    }

    /**
     * {@inheritDoc}
     */
    public function setTaxes(?\Wizacha\Money\Money $taxes): \Wizacha\Marketplace\PriceTier\PriceTier
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTaxes', [$taxes]);

        return parent::setTaxes($taxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getTaxes(): ?\Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTaxes', []);

        return parent::getTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setPriceIncludeTax(bool $priceIncludeTax): \Wizacha\Marketplace\PriceTier\PriceTier
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPriceIncludeTax', [$priceIncludeTax]);

        return parent::setPriceIncludeTax($priceIncludeTax);
    }

    /**
     * {@inheritDoc}
     */
    public function getPriceIncludeTax(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPriceIncludeTax', []);

        return parent::getPriceIncludeTax();
    }

    /**
     * {@inheritDoc}
     */
    public function expose(bool $minimalInformation = false): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'expose', [$minimalInformation]);

        return parent::expose($minimalInformation);
    }

    /**
     * {@inheritDoc}
     */
    public function setLowerLimitAndPrice(\Wizacha\Money\Money $price, int $lowerLimit = 0): \Wizacha\Marketplace\PriceTier\PriceTier
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setLowerLimitAndPrice', [$price, $lowerLimit]);

        return parent::setLowerLimitAndPrice($price, $lowerLimit);
    }

    /**
     * {@inheritDoc}
     */
    public function toArray(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'toArray', []);

        return parent::toArray();
    }

    /**
     * {@inheritDoc}
     */
    public function clonePriceTier(\Wizacha\Marketplace\PIM\Product\Product $product, ?\Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory $combination): \Wizacha\Marketplace\PriceTier\PriceTier
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'clonePriceTier', [$product, $combination]);

        return parent::clonePriceTier($product, $combination);
    }

    /**
     * {@inheritDoc}
     */
    public function getProductIdLowerLimitOptionId(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProductIdLowerLimitOptionId', []);

        return parent::getProductIdLowerLimitOptionId();
    }

    /**
     * {@inheritDoc}
     */
    public function setProductIdLowerLimitOptionId(): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setProductIdLowerLimitOptionId', []);

        parent::setProductIdLowerLimitOptionId();
    }

}
