<?php

namespace Proxies\__CG__\Wizacha\Marketplace\Order\Adjustment;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class OrderAdjustment extends \Wizacha\Marketplace\Order\Adjustment\OrderAdjustment implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\Order\\Adjustment\\OrderAdjustment' . "\0" . 'id', '' . "\0" . 'Wizacha\\Marketplace\\Order\\Adjustment\\OrderAdjustment' . "\0" . 'orderId', '' . "\0" . 'Wizacha\\Marketplace\\Order\\Adjustment\\OrderAdjustment' . "\0" . 'itemId', '' . "\0" . 'Wizacha\\Marketplace\\Order\\Adjustment\\OrderAdjustment' . "\0" . 'oldPriceWithoutTaxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\Adjustment\\OrderAdjustment' . "\0" . 'newPriceWithoutTaxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\Adjustment\\OrderAdjustment' . "\0" . 'oldTotalIncludingTaxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\Adjustment\\OrderAdjustment' . "\0" . 'newTotalIncludingTaxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\Adjustment\\OrderAdjustment' . "\0" . 'createdAt', '' . "\0" . 'Wizacha\\Marketplace\\Order\\Adjustment\\OrderAdjustment' . "\0" . 'user'];
        }

        return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\Order\\Adjustment\\OrderAdjustment' . "\0" . 'id', '' . "\0" . 'Wizacha\\Marketplace\\Order\\Adjustment\\OrderAdjustment' . "\0" . 'orderId', '' . "\0" . 'Wizacha\\Marketplace\\Order\\Adjustment\\OrderAdjustment' . "\0" . 'itemId', '' . "\0" . 'Wizacha\\Marketplace\\Order\\Adjustment\\OrderAdjustment' . "\0" . 'oldPriceWithoutTaxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\Adjustment\\OrderAdjustment' . "\0" . 'newPriceWithoutTaxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\Adjustment\\OrderAdjustment' . "\0" . 'oldTotalIncludingTaxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\Adjustment\\OrderAdjustment' . "\0" . 'newTotalIncludingTaxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\Adjustment\\OrderAdjustment' . "\0" . 'createdAt', '' . "\0" . 'Wizacha\\Marketplace\\Order\\Adjustment\\OrderAdjustment' . "\0" . 'user'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (OrderAdjustment $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getId(): ?int
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function getOrderId(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOrderId', []);

        return parent::getOrderId();
    }

    /**
     * {@inheritDoc}
     */
    public function setOrderId(int $orderId): \Wizacha\Marketplace\Order\Adjustment\OrderAdjustment
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setOrderId', [$orderId]);

        return parent::setOrderId($orderId);
    }

    /**
     * {@inheritDoc}
     */
    public function getItemId(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getItemId', []);

        return parent::getItemId();
    }

    /**
     * {@inheritDoc}
     */
    public function setItemId(int $itemId): \Wizacha\Marketplace\Order\Adjustment\OrderAdjustment
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setItemId', [$itemId]);

        return parent::setItemId($itemId);
    }

    /**
     * {@inheritDoc}
     */
    public function getOldPriceWithoutTaxes(): float
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOldPriceWithoutTaxes', []);

        return parent::getOldPriceWithoutTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setOldPriceWithoutTaxes(float $oldPriceWithoutTaxes): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setOldPriceWithoutTaxes', [$oldPriceWithoutTaxes]);

        parent::setOldPriceWithoutTaxes($oldPriceWithoutTaxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewPriceWithoutTaxes(): float
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewPriceWithoutTaxes', []);

        return parent::getNewPriceWithoutTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewPriceWithoutTaxes(float $newPriceWithoutTaxes): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewPriceWithoutTaxes', [$newPriceWithoutTaxes]);

        parent::setNewPriceWithoutTaxes($newPriceWithoutTaxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getOldTotalIncludingTaxes(): float
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOldTotalIncludingTaxes', []);

        return parent::getOldTotalIncludingTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setOldTotalIncludingTaxes(float $oldTotalIncludingTaxes): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setOldTotalIncludingTaxes', [$oldTotalIncludingTaxes]);

        parent::setOldTotalIncludingTaxes($oldTotalIncludingTaxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewTotalIncludingTaxes(): float
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewTotalIncludingTaxes', []);

        return parent::getNewTotalIncludingTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewTotalIncludingTaxes(float $newTotalIncludingTaxes): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewTotalIncludingTaxes', [$newTotalIncludingTaxes]);

        parent::setNewTotalIncludingTaxes($newTotalIncludingTaxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getCreatedAt(): \DateTimeInterface
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCreatedAt', []);

        return parent::getCreatedAt();
    }

    /**
     * {@inheritDoc}
     */
    public function setCreatedAt(\DateTimeInterface $createdAt): \Wizacha\Marketplace\Order\Adjustment\OrderAdjustment
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCreatedAt', [$createdAt]);

        return parent::setCreatedAt($createdAt);
    }

    /**
     * {@inheritDoc}
     */
    public function getUser(): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUser', []);

        return parent::getUser();
    }

    /**
     * {@inheritDoc}
     */
    public function setUser(\Wizacha\Marketplace\User\User $user): \Wizacha\Marketplace\Order\Adjustment\OrderAdjustment
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setUser', [$user]);

        return parent::setUser($user);
    }

    /**
     * {@inheritDoc}
     */
    public function expose(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'expose', []);

        return parent::expose();
    }

}
