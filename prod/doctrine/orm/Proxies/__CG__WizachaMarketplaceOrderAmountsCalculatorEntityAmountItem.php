<?php

namespace Proxies\__CG__\Wizacha\Marketplace\Order\AmountsCalculator\Entity;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class AmountItem extends \Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', 'id', 'orderAmounts', 'orderItemData', 'discount', 'totalExclTaxes', 'totalInclTaxes', 'unitPriceExclTaxes', 'unitPriceInclTaxes', 'adjustedUnitPriceExclTaxes', 'adjustedUnitPriceInclTaxes', 'quantity', 'taxId', 'taxRate', 'greenTax', 'totalGreenTax', 'unitTaxAmount', 'totalTaxAmount', 'discountedUnitPriceExclTaxes', 'discountedUnitPriceInclTaxes', 'discountedUnitTaxAmount', 'discountedTotalExclTaxes', 'discountedTotalInclTaxes', 'discountedTotalTaxAmount', 'discountMarketplace'];
        }

        return ['__isInitialized__', 'id', 'orderAmounts', 'orderItemData', 'discount', 'totalExclTaxes', 'totalInclTaxes', 'unitPriceExclTaxes', 'unitPriceInclTaxes', 'adjustedUnitPriceExclTaxes', 'adjustedUnitPriceInclTaxes', 'quantity', 'taxId', 'taxRate', 'greenTax', 'totalGreenTax', 'unitTaxAmount', 'totalTaxAmount', 'discountedUnitPriceExclTaxes', 'discountedUnitPriceInclTaxes', 'discountedUnitTaxAmount', 'discountedTotalExclTaxes', 'discountedTotalInclTaxes', 'discountedTotalTaxAmount', 'discountMarketplace'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (AmountItem $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function hydrateFromOrderItem(\Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts $orderAmounts, \Wizacha\Marketplace\Order\OrderItem $orderItem): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hydrateFromOrderItem', [$orderAmounts, $orderItem]);

        return parent::hydrateFromOrderItem($orderAmounts, $orderItem);
    }

    /**
     * {@inheritDoc}
     */
    public function hydrate(\Wizacha\Money\Money $price, int $taxId, float $taxRate, \Wizacha\Money\Money $greenTaxAmount, bool $isTaxIncludedInPrice, int $quantity): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hydrate', [$price, $taxId, $taxRate, $greenTaxAmount, $isTaxIncludedInPrice, $quantity]);

        return parent::hydrate($price, $taxId, $taxRate, $greenTaxAmount, $isTaxIncludedInPrice, $quantity);
    }

    /**
     * {@inheritDoc}
     */
    public function getId(): int
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function setId(int $id): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setId', [$id]);

        return parent::setId($id);
    }

    /**
     * {@inheritDoc}
     */
    public function getOrderAmounts(): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOrderAmounts', []);

        return parent::getOrderAmounts();
    }

    /**
     * {@inheritDoc}
     */
    public function setOrderAmounts(\Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts $orderAmounts): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setOrderAmounts', [$orderAmounts]);

        return parent::setOrderAmounts($orderAmounts);
    }

    /**
     * {@inheritDoc}
     */
    public function getOrderItemData(): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderItemData
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOrderItemData', []);

        return parent::getOrderItemData();
    }

    /**
     * {@inheritDoc}
     */
    public function setOrderItemData(\Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderItemData $orderItemData): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setOrderItemData', [$orderItemData]);

        return parent::setOrderItemData($orderItemData);
    }

    /**
     * {@inheritDoc}
     */
    public function getDiscount(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDiscount', []);

        return parent::getDiscount();
    }

    /**
     * {@inheritDoc}
     */
    public function getUnitDiscount(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUnitDiscount', []);

        return parent::getUnitDiscount();
    }

    /**
     * {@inheritDoc}
     */
    public function setDiscount(\Wizacha\Money\Money $discount): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDiscount', [$discount]);

        return parent::setDiscount($discount);
    }

    /**
     * {@inheritDoc}
     */
    public function getTotalExclTaxes(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTotalExclTaxes', []);

        return parent::getTotalExclTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setTotalExclTaxes(\Wizacha\Money\Money $totalExclTaxes): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTotalExclTaxes', [$totalExclTaxes]);

        return parent::setTotalExclTaxes($totalExclTaxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getTotalInclTaxes(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTotalInclTaxes', []);

        return parent::getTotalInclTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setTotalInclTaxes(\Wizacha\Money\Money $totalInclTaxes): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTotalInclTaxes', [$totalInclTaxes]);

        return parent::setTotalInclTaxes($totalInclTaxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getUnitPriceExclTaxes(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUnitPriceExclTaxes', []);

        return parent::getUnitPriceExclTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setUnitPriceExclTaxes(\Wizacha\Money\Money $unitPriceExclTaxes): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setUnitPriceExclTaxes', [$unitPriceExclTaxes]);

        return parent::setUnitPriceExclTaxes($unitPriceExclTaxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getUnitPriceInclTaxes(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUnitPriceInclTaxes', []);

        return parent::getUnitPriceInclTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setUnitPriceInclTaxes(\Wizacha\Money\Money $unitPriceInclTaxes): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setUnitPriceInclTaxes', [$unitPriceInclTaxes]);

        return parent::setUnitPriceInclTaxes($unitPriceInclTaxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getAdjustedUnitPriceExclTaxes(): ?\Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAdjustedUnitPriceExclTaxes', []);

        return parent::getAdjustedUnitPriceExclTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function getAdjustedUnitPriceInclTaxes(): ?\Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAdjustedUnitPriceInclTaxes', []);

        return parent::getAdjustedUnitPriceInclTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setAdjustedPrices(\Wizacha\Money\Money $adjustedPriceExclTaxes): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAdjustedPrices', [$adjustedPriceExclTaxes]);

        return parent::setAdjustedPrices($adjustedPriceExclTaxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getTaxId(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTaxId', []);

        return parent::getTaxId();
    }

    /**
     * {@inheritDoc}
     */
    public function setTaxId(int $taxId): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTaxId', [$taxId]);

        return parent::setTaxId($taxId);
    }

    /**
     * {@inheritDoc}
     */
    public function getQuantity(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getQuantity', []);

        return parent::getQuantity();
    }

    /**
     * {@inheritDoc}
     */
    public function setQuantity(int $quantity): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setQuantity', [$quantity]);

        return parent::setQuantity($quantity);
    }

    /**
     * {@inheritDoc}
     */
    public function getTaxRate(): float
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTaxRate', []);

        return parent::getTaxRate();
    }

    /**
     * {@inheritDoc}
     */
    public function setTaxRate(float $taxRate): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTaxRate', [$taxRate]);

        return parent::setTaxRate($taxRate);
    }

    /**
     * {@inheritDoc}
     */
    public function getGreenTax(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getGreenTax', []);

        return parent::getGreenTax();
    }

    /**
     * {@inheritDoc}
     */
    public function setGreenTax(\Wizacha\Money\Money $greenTax): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setGreenTax', [$greenTax]);

        return parent::setGreenTax($greenTax);
    }

    /**
     * {@inheritDoc}
     */
    public function getTotalGreenTax(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTotalGreenTax', []);

        return parent::getTotalGreenTax();
    }

    /**
     * {@inheritDoc}
     */
    public function setTotalGreenTax(\Wizacha\Money\Money $totalGreenTax): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTotalGreenTax', [$totalGreenTax]);

        return parent::setTotalGreenTax($totalGreenTax);
    }

    /**
     * {@inheritDoc}
     */
    public function getUnitTaxAmount(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUnitTaxAmount', []);

        return parent::getUnitTaxAmount();
    }

    /**
     * {@inheritDoc}
     */
    public function setUnitTaxAmount(\Wizacha\Money\Money $unitTaxAmount): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setUnitTaxAmount', [$unitTaxAmount]);

        return parent::setUnitTaxAmount($unitTaxAmount);
    }

    /**
     * {@inheritDoc}
     */
    public function getTotalTaxAmount(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTotalTaxAmount', []);

        return parent::getTotalTaxAmount();
    }

    /**
     * {@inheritDoc}
     */
    public function setTotalTaxAmount(\Wizacha\Money\Money $totalTaxAmount): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTotalTaxAmount', [$totalTaxAmount]);

        return parent::setTotalTaxAmount($totalTaxAmount);
    }

    /**
     * {@inheritDoc}
     */
    public function getDiscountedUnitPriceExclTaxes(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDiscountedUnitPriceExclTaxes', []);

        return parent::getDiscountedUnitPriceExclTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setDiscountedUnitPriceExclTaxes(\Wizacha\Money\Money $discountedUnitPriceExclTaxes): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDiscountedUnitPriceExclTaxes', [$discountedUnitPriceExclTaxes]);

        return parent::setDiscountedUnitPriceExclTaxes($discountedUnitPriceExclTaxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getDiscountedUnitPriceInclTaxes(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDiscountedUnitPriceInclTaxes', []);

        return parent::getDiscountedUnitPriceInclTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setDiscountedUnitPriceInclTaxes(\Wizacha\Money\Money $discountedUnitPriceInclTaxes): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDiscountedUnitPriceInclTaxes', [$discountedUnitPriceInclTaxes]);

        return parent::setDiscountedUnitPriceInclTaxes($discountedUnitPriceInclTaxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getDiscountedUnitTaxAmount(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDiscountedUnitTaxAmount', []);

        return parent::getDiscountedUnitTaxAmount();
    }

    /**
     * {@inheritDoc}
     */
    public function setDiscountedUnitTaxAmount(\Wizacha\Money\Money $discountedUnitTaxAmount): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDiscountedUnitTaxAmount', [$discountedUnitTaxAmount]);

        return parent::setDiscountedUnitTaxAmount($discountedUnitTaxAmount);
    }

    /**
     * {@inheritDoc}
     */
    public function getDiscountedTotalExclTaxes(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDiscountedTotalExclTaxes', []);

        return parent::getDiscountedTotalExclTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setDiscountedTotalExclTaxes(\Wizacha\Money\Money $discountedTotalExclTaxes): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDiscountedTotalExclTaxes', [$discountedTotalExclTaxes]);

        return parent::setDiscountedTotalExclTaxes($discountedTotalExclTaxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getDiscountedTotalInclTaxes(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDiscountedTotalInclTaxes', []);

        return parent::getDiscountedTotalInclTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setDiscountedTotalInclTaxes(\Wizacha\Money\Money $discountedTotalInclTaxes): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDiscountedTotalInclTaxes', [$discountedTotalInclTaxes]);

        return parent::setDiscountedTotalInclTaxes($discountedTotalInclTaxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getDiscountedTotalTaxAmount(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDiscountedTotalTaxAmount', []);

        return parent::getDiscountedTotalTaxAmount();
    }

    /**
     * {@inheritDoc}
     */
    public function setDiscountedTotalTaxAmount(\Wizacha\Money\Money $discountedTotalTaxAmount): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDiscountedTotalTaxAmount', [$discountedTotalTaxAmount]);

        return parent::setDiscountedTotalTaxAmount($discountedTotalTaxAmount);
    }

    /**
     * {@inheritDoc}
     */
    public function getDiscountMarketplace(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDiscountMarketplace', []);

        return parent::getDiscountMarketplace();
    }

    /**
     * {@inheritDoc}
     */
    public function getUnitDiscountMarketplace(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUnitDiscountMarketplace', []);

        return parent::getUnitDiscountMarketplace();
    }

    /**
     * {@inheritDoc}
     */
    public function setDiscountMarketplace(\Wizacha\Money\Money $discountMarketplace): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDiscountMarketplace', [$discountMarketplace]);

        return parent::setDiscountMarketplace($discountMarketplace);
    }

}
