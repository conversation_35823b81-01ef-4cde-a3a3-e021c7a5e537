<?php

namespace Proxies\__CG__\Wizacha\Marketplace\Payment;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class UserMandate extends \Wizacha\Marketplace\Payment\UserMandate implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserMandate' . "\0" . 'id', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserMandate' . "\0" . 'userId', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserMandate' . "\0" . 'createdAt', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserMandate' . "\0" . 'iban', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserMandate' . "\0" . 'bic', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserMandate' . "\0" . 'bankName', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserMandate' . "\0" . 'gender', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserMandate' . "\0" . 'firstName', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserMandate' . "\0" . 'lastName', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserMandate' . "\0" . 'agreementId', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserMandate' . "\0" . 'processorId', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserMandate' . "\0" . 'status'];
        }

        return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserMandate' . "\0" . 'id', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserMandate' . "\0" . 'userId', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserMandate' . "\0" . 'createdAt', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserMandate' . "\0" . 'iban', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserMandate' . "\0" . 'bic', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserMandate' . "\0" . 'bankName', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserMandate' . "\0" . 'gender', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserMandate' . "\0" . 'firstName', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserMandate' . "\0" . 'lastName', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserMandate' . "\0" . 'agreementId', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserMandate' . "\0" . 'processorId', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserMandate' . "\0" . 'status'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (UserMandate $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getId(): int
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function getUserId(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUserId', []);

        return parent::getUserId();
    }

    /**
     * {@inheritDoc}
     */
    public function setUserId(int $userId): \Wizacha\Marketplace\Payment\UserMandate
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setUserId', [$userId]);

        return parent::setUserId($userId);
    }

    /**
     * {@inheritDoc}
     */
    public function getCreatedAt(): ?\DateTime
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCreatedAt', []);

        return parent::getCreatedAt();
    }

    /**
     * {@inheritDoc}
     */
    public function setCreatedAt(?\DateTime $createdAt): \Wizacha\Marketplace\Payment\UserMandate
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCreatedAt', [$createdAt]);

        return parent::setCreatedAt($createdAt);
    }

    /**
     * {@inheritDoc}
     */
    public function getIban(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getIban', []);

        return parent::getIban();
    }

    /**
     * {@inheritDoc}
     */
    public function setIban(?string $iban): \Wizacha\Marketplace\Payment\UserMandate
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIban', [$iban]);

        return parent::setIban($iban);
    }

    /**
     * {@inheritDoc}
     */
    public function getBic(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getBic', []);

        return parent::getBic();
    }

    /**
     * {@inheritDoc}
     */
    public function setBic(?string $bic): \Wizacha\Marketplace\Payment\UserMandate
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setBic', [$bic]);

        return parent::setBic($bic);
    }

    /**
     * {@inheritDoc}
     */
    public function getBankName(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getBankName', []);

        return parent::getBankName();
    }

    /**
     * {@inheritDoc}
     */
    public function setBankName(?string $bankName): \Wizacha\Marketplace\Payment\UserMandate
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setBankName', [$bankName]);

        return parent::setBankName($bankName);
    }

    /**
     * {@inheritDoc}
     */
    public function getGender(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getGender', []);

        return parent::getGender();
    }

    /**
     * {@inheritDoc}
     */
    public function setGender(?string $gender): \Wizacha\Marketplace\Payment\UserMandate
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setGender', [$gender]);

        return parent::setGender($gender);
    }

    /**
     * {@inheritDoc}
     */
    public function getFirstName(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getFirstName', []);

        return parent::getFirstName();
    }

    /**
     * {@inheritDoc}
     */
    public function setFirstName(?string $firstName): \Wizacha\Marketplace\Payment\UserMandate
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setFirstName', [$firstName]);

        return parent::setFirstName($firstName);
    }

    /**
     * {@inheritDoc}
     */
    public function getLastName(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLastName', []);

        return parent::getLastName();
    }

    /**
     * {@inheritDoc}
     */
    public function setLastName(?string $lastName): \Wizacha\Marketplace\Payment\UserMandate
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setLastName', [$lastName]);

        return parent::setLastName($lastName);
    }

    /**
     * {@inheritDoc}
     */
    public function getAgreementId(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAgreementId', []);

        return parent::getAgreementId();
    }

    /**
     * {@inheritDoc}
     */
    public function setAgreementId(?int $agreementId): \Wizacha\Marketplace\Payment\UserMandate
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAgreementId', [$agreementId]);

        return parent::setAgreementId($agreementId);
    }

    /**
     * {@inheritDoc}
     */
    public function getProcessorId(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProcessorId', []);

        return parent::getProcessorId();
    }

    /**
     * {@inheritDoc}
     */
    public function setProcessorId(int $processorId): \Wizacha\Marketplace\Payment\UserMandate
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setProcessorId', [$processorId]);

        return parent::setProcessorId($processorId);
    }

    /**
     * {@inheritDoc}
     */
    public function getStatus(): \Wizacha\Marketplace\Payment\MandateStatus
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getStatus', []);

        return parent::getStatus();
    }

    /**
     * {@inheritDoc}
     */
    public function setStatus(\Wizacha\Marketplace\Payment\MandateStatus $status): \Wizacha\Marketplace\Payment\UserMandate
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setStatus', [$status]);

        return parent::setStatus($status);
    }

    /**
     * {@inheritDoc}
     */
    public function setMandateInfo(array $data): \Wizacha\Marketplace\Payment\UserMandate
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setMandateInfo', [$data]);

        return parent::setMandateInfo($data);
    }

    /**
     * {@inheritDoc}
     */
    public function expose(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'expose', []);

        return parent::expose();
    }

}
