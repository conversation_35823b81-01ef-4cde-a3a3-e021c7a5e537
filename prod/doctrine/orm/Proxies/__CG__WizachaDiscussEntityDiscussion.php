<?php

namespace Proxies\__CG__\Wizacha\Discuss\Entity;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Discussion extends \Wizacha\Discuss\Entity\Discussion implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', 'id', 'users', 'open', 'meta_data', 'messages'];
        }

        return ['__isInitialized__', 'id', 'users', 'open', 'meta_data', 'messages'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (Discussion $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getId()
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function setInitiator($initiator)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setInitiator', [$initiator]);

        return parent::setInitiator($initiator);
    }

    /**
     * {@inheritDoc}
     */
    public function getUsers()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUsers', []);

        return parent::getUsers();
    }

    /**
     * {@inheritDoc}
     */
    public function getOtherUser($user_id)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOtherUser', [$user_id]);

        return parent::getOtherUser($user_id);
    }

    /**
     * {@inheritDoc}
     */
    public function getInitiator()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getInitiator', []);

        return parent::getInitiator();
    }

    /**
     * {@inheritDoc}
     */
    public function setRecipient($recipient)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setRecipient', [$recipient]);

        return parent::setRecipient($recipient);
    }

    /**
     * {@inheritDoc}
     */
    public function getRecipient()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRecipient', []);

        return parent::getRecipient();
    }

    /**
     * {@inheritDoc}
     */
    public function getStatusInitiator()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getStatusInitiator', []);

        return parent::getStatusInitiator();
    }

    /**
     * {@inheritDoc}
     */
    public function setOpen($open)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setOpen', [$open]);

        return parent::setOpen($open);
    }

    /**
     * {@inheritDoc}
     */
    public function getOpen()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOpen', []);

        return parent::getOpen();
    }

    /**
     * {@inheritDoc}
     */
    public function getStatusRecipient()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getStatusRecipient', []);

        return parent::getStatusRecipient();
    }

    /**
     * {@inheritDoc}
     */
    public function hideDiscussion()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hideDiscussion', []);

        return parent::hideDiscussion();
    }

    /**
     * {@inheritDoc}
     */
    public function setUserStatus($user_id, \Wizacha\Discuss\Entity\Discussion\Status $status)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setUserStatus', [$user_id, $status]);

        return parent::setUserStatus($user_id, $status);
    }

    /**
     * {@inheritDoc}
     */
    public function setAllUsersStatus(\Wizacha\Discuss\Entity\Discussion\Status $status)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAllUsersStatus', [$status]);

        return parent::setAllUsersStatus($status);
    }

    /**
     * {@inheritDoc}
     */
    public function getMetaData($name)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getMetaData', [$name]);

        return parent::getMetaData($name);
    }

    /**
     * {@inheritDoc}
     */
    public function setMetaData($name, $value)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setMetaData', [$name, $value]);

        return parent::setMetaData($name, $value);
    }

}
