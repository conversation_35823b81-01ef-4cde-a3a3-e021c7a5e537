<?php

namespace Proxies\__CG__\Wizacha\Marketplace\Organisation;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Organisation extends \Wizacha\Marketplace\Organisation\Organisation implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\Organisation' . "\0" . 'id', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\Organisation' . "\0" . 'name', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\Organisation' . "\0" . 'address', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\Organisation' . "\0" . 'shippingAddress', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\Organisation' . "\0" . 'organisationBaskets', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\Organisation' . "\0" . 'adminGroup', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\Organisation' . "\0" . 'userGroups', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\Organisation' . "\0" . 'orders', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\Organisation' . "\0" . 'legalInformation', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\Organisation' . "\0" . 'businessUnit', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\Organisation' . "\0" . 'administrators', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\Organisation' . "\0" . 'status'];
        }

        return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\Organisation' . "\0" . 'id', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\Organisation' . "\0" . 'name', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\Organisation' . "\0" . 'address', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\Organisation' . "\0" . 'shippingAddress', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\Organisation' . "\0" . 'organisationBaskets', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\Organisation' . "\0" . 'adminGroup', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\Organisation' . "\0" . 'userGroups', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\Organisation' . "\0" . 'orders', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\Organisation' . "\0" . 'legalInformation', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\Organisation' . "\0" . 'businessUnit', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\Organisation' . "\0" . 'administrators', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\Organisation' . "\0" . 'status'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (Organisation $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getId(): string
    {
        if ($this->__isInitialized__ === false) {
            return  parent::getId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function getName(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getName', []);

        return parent::getName();
    }

    /**
     * {@inheritDoc}
     */
    public function getAdministrators(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAdministrators', []);

        return parent::getAdministrators();
    }

    /**
     * {@inheritDoc}
     */
    public function getAdministrator(\Wizacha\Marketplace\User\User $user): \Wizacha\Marketplace\Organisation\Administrator
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAdministrator', [$user]);

        return parent::getAdministrator($user);
    }

    /**
     * {@inheritDoc}
     */
    public function isAdministrator(\Wizacha\Marketplace\User\User $user): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isAdministrator', [$user]);

        return parent::isAdministrator($user);
    }

    /**
     * {@inheritDoc}
     */
    public function getAddress(): \Wizacha\Marketplace\Organisation\OrganisationAddress
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAddress', []);

        return parent::getAddress();
    }

    /**
     * {@inheritDoc}
     */
    public function setAddress(\Wizacha\Marketplace\Organisation\OrganisationAddress $address): \Wizacha\Marketplace\Organisation\Organisation
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAddress', [$address]);

        return parent::setAddress($address);
    }

    /**
     * {@inheritDoc}
     */
    public function getShippingAddress(): \Wizacha\Marketplace\Organisation\OrganisationAddress
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getShippingAddress', []);

        return parent::getShippingAddress();
    }

    /**
     * {@inheritDoc}
     */
    public function setShippingAddress(\Wizacha\Marketplace\Organisation\OrganisationAddress $shippingAddress): \Wizacha\Marketplace\Organisation\Organisation
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setShippingAddress', [$shippingAddress]);

        return parent::setShippingAddress($shippingAddress);
    }

    /**
     * {@inheritDoc}
     */
    public function rename(string $name): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'rename', [$name]);

        parent::rename($name);
    }

    /**
     * {@inheritDoc}
     */
    public function attachBasket(string $basketId, \Wizacha\Marketplace\User\User $user, string $name): \Wizacha\Marketplace\Organisation\Organisation
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'attachBasket', [$basketId, $user, $name]);

        return parent::attachBasket($basketId, $user, $name);
    }

    /**
     * {@inheritDoc}
     */
    public function getOrganisationBaskets(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOrganisationBaskets', []);

        return parent::getOrganisationBaskets();
    }

    /**
     * {@inheritDoc}
     */
    public function ownsBasket(string $basketId): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'ownsBasket', [$basketId]);

        return parent::ownsBasket($basketId);
    }

    /**
     * {@inheritDoc}
     */
    public function getBasket(string $basketId): \Wizacha\Marketplace\Organisation\OrganisationBasket
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getBasket', [$basketId]);

        return parent::getBasket($basketId);
    }

    /**
     * {@inheritDoc}
     */
    public function getAdminGroup(): \Wizacha\Marketplace\Organisation\UserGroup
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAdminGroup', []);

        return parent::getAdminGroup();
    }

    /**
     * {@inheritDoc}
     */
    public function getUserGroups(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUserGroups', []);

        return parent::getUserGroups();
    }

    /**
     * {@inheritDoc}
     */
    public function getUserGroup(string $type): \Wizacha\Marketplace\Organisation\UserGroup
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUserGroup', [$type]);

        return parent::getUserGroup($type);
    }

    /**
     * {@inheritDoc}
     */
    public function hasGroupType(string $type): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasGroupType', [$type]);

        return parent::hasGroupType($type);
    }

    /**
     * {@inheritDoc}
     */
    public function addUserGroup(\Wizacha\Marketplace\Organisation\UserGroup $group): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addUserGroup', [$group]);

        parent::addUserGroup($group);
    }

    /**
     * {@inheritDoc}
     */
    public function declareLegalInformation(string $siret, string $vatNumber, string $businessName): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'declareLegalInformation', [$siret, $vatNumber, $businessName]);

        parent::declareLegalInformation($siret, $vatNumber, $businessName);
    }

    /**
     * {@inheritDoc}
     */
    public function getLegalInformation(): \Wizacha\Marketplace\Organisation\LegalInformation
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLegalInformation', []);

        return parent::getLegalInformation();
    }

    /**
     * {@inheritDoc}
     */
    public function getBusinessUnit(): \Wizacha\Marketplace\Organisation\BusinessUnit
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getBusinessUnit', []);

        return parent::getBusinessUnit();
    }

    /**
     * {@inheritDoc}
     */
    public function registerBusinessUnit(string $code, string $name): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'registerBusinessUnit', [$code, $name]);

        parent::registerBusinessUnit($code, $name);
    }

    /**
     * {@inheritDoc}
     */
    public function getStatus(): \Wizacha\Marketplace\Organisation\OrganisationStatus
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getStatus', []);

        return parent::getStatus();
    }

    /**
     * {@inheritDoc}
     */
    public function approve(): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'approve', []);

        parent::approve();
    }

    /**
     * {@inheritDoc}
     */
    public function isApproved(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isApproved', []);

        return parent::isApproved();
    }

    /**
     * {@inheritDoc}
     */
    public function disapprove(): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'disapprove', []);

        parent::disapprove();
    }

    /**
     * {@inheritDoc}
     */
    public function isDisapproved(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isDisapproved', []);

        return parent::isDisapproved();
    }

    /**
     * {@inheritDoc}
     */
    public function addAdministrator(\Wizacha\Marketplace\User\User $user, string $occupation, \Rhumsaa\Uuid\Uuid $identityCard, \Rhumsaa\Uuid\Uuid $proofOfAppointment): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addAdministrator', [$user, $occupation, $identityCard, $proofOfAppointment]);

        parent::addAdministrator($user, $occupation, $identityCard, $proofOfAppointment);
    }

    /**
     * {@inheritDoc}
     */
    public function expose(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'expose', []);

        return parent::expose();
    }

    /**
     * {@inheritDoc}
     */
    public function getOrders(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOrders', []);

        return parent::getOrders();
    }

}
