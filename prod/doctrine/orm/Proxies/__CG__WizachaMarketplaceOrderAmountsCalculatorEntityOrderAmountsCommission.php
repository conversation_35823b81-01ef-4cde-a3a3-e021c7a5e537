<?php

namespace Proxies\__CG__\Wizacha\Marketplace\Order\AmountsCalculator\Entity;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class OrderAmountsCommission extends \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmountsCommission implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmountsCommission' . "\0" . 'id', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmountsCommission' . "\0" . 'orderId', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmountsCommission' . "\0" . 'companyId', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmountsCommission' . "\0" . 'categoryId', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmountsCommission' . "\0" . 'percentAmount', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmountsCommission' . "\0" . 'fixAmount', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmountsCommission' . "\0" . 'maximumAmount', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmountsCommission' . "\0" . 'commissionType'];
        }

        return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmountsCommission' . "\0" . 'id', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmountsCommission' . "\0" . 'orderId', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmountsCommission' . "\0" . 'companyId', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmountsCommission' . "\0" . 'categoryId', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmountsCommission' . "\0" . 'percentAmount', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmountsCommission' . "\0" . 'fixAmount', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmountsCommission' . "\0" . 'maximumAmount', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmountsCommission' . "\0" . 'commissionType'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (OrderAmountsCommission $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getOrderId(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOrderId', []);

        return parent::getOrderId();
    }

    /**
     * {@inheritDoc}
     */
    public function setOrderId(int $orderId): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmountsCommission
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setOrderId', [$orderId]);

        return parent::setOrderId($orderId);
    }

    /**
     * {@inheritDoc}
     */
    public function getId(): string
    {
        if ($this->__isInitialized__ === false) {
            return  parent::getId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function setId(string $id): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmountsCommission
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setId', [$id]);

        return parent::setId($id);
    }

    /**
     * {@inheritDoc}
     */
    public function getCompanyId(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCompanyId', []);

        return parent::getCompanyId();
    }

    /**
     * {@inheritDoc}
     */
    public function setCompanyId(?int $companyId): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmountsCommission
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCompanyId', [$companyId]);

        return parent::setCompanyId($companyId);
    }

    /**
     * {@inheritDoc}
     */
    public function getCategoryId(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCategoryId', []);

        return parent::getCategoryId();
    }

    /**
     * {@inheritDoc}
     */
    public function setCategoryId(?int $categoryId): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmountsCommission
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCategoryId', [$categoryId]);

        return parent::setCategoryId($categoryId);
    }

    /**
     * {@inheritDoc}
     */
    public function getFixAmount(): float
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getFixAmount', []);

        return parent::getFixAmount();
    }

    /**
     * {@inheritDoc}
     */
    public function setFixAmount(float $fixAmount): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmountsCommission
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setFixAmount', [$fixAmount]);

        return parent::setFixAmount($fixAmount);
    }

    /**
     * {@inheritDoc}
     */
    public function getMaximumAmount(): ?float
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getMaximumAmount', []);

        return parent::getMaximumAmount();
    }

    /**
     * {@inheritDoc}
     */
    public function setMaximumAmount(?float $maximumAmount): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmountsCommission
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setMaximumAmount', [$maximumAmount]);

        return parent::setMaximumAmount($maximumAmount);
    }

    /**
     * {@inheritDoc}
     */
    public function getCommissionType(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCommissionType', []);

        return parent::getCommissionType();
    }

    /**
     * {@inheritDoc}
     */
    public function setCommissionType(string $commissionType): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmountsCommission
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCommissionType', [$commissionType]);

        return parent::setCommissionType($commissionType);
    }

    /**
     * {@inheritDoc}
     */
    public function getPercentAmount(): float
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPercentAmount', []);

        return parent::getPercentAmount();
    }

    /**
     * {@inheritDoc}
     */
    public function setPercentAmount(float $percentAmount): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmountsCommission
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPercentAmount', [$percentAmount]);

        return parent::setPercentAmount($percentAmount);
    }

}
