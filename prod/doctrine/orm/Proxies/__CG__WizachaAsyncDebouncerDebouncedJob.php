<?php

namespace Proxies\__CG__\Wizacha\Async\Debouncer;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Debounced<PERSON>ob extends \Wizacha\Async\Debouncer\Debounced<PERSON>ob implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Wizacha\\Async\\Debouncer\\DebouncedJob' . "\0" . 'queue', '' . "\0" . 'Wizacha\\Async\\Debouncer\\DebouncedJob' . "\0" . 'debounceKey', '' . "\0" . 'Wizacha\\Async\\Debouncer\\DebouncedJob' . "\0" . 'payload', '' . "\0" . 'Wizacha\\Async\\Debouncer\\DebouncedJob' . "\0" . 'createdAt', '' . "\0" . 'Wizacha\\Async\\Debouncer\\DebouncedJob' . "\0" . 'updatedAt', '' . "\0" . 'Wizacha\\Async\\Debouncer\\DebouncedJob' . "\0" . 'ttl', '' . "\0" . 'Wizacha\\Async\\Debouncer\\DebouncedJob' . "\0" . 'expiresAt', '' . "\0" . 'Wizacha\\Async\\Debouncer\\DebouncedJob' . "\0" . 'bounceCounter', '' . "\0" . 'Wizacha\\Async\\Debouncer\\DebouncedJob' . "\0" . 'jobPayload'];
        }

        return ['__isInitialized__', '' . "\0" . 'Wizacha\\Async\\Debouncer\\DebouncedJob' . "\0" . 'queue', '' . "\0" . 'Wizacha\\Async\\Debouncer\\DebouncedJob' . "\0" . 'debounceKey', '' . "\0" . 'Wizacha\\Async\\Debouncer\\DebouncedJob' . "\0" . 'payload', '' . "\0" . 'Wizacha\\Async\\Debouncer\\DebouncedJob' . "\0" . 'createdAt', '' . "\0" . 'Wizacha\\Async\\Debouncer\\DebouncedJob' . "\0" . 'updatedAt', '' . "\0" . 'Wizacha\\Async\\Debouncer\\DebouncedJob' . "\0" . 'ttl', '' . "\0" . 'Wizacha\\Async\\Debouncer\\DebouncedJob' . "\0" . 'expiresAt', '' . "\0" . 'Wizacha\\Async\\Debouncer\\DebouncedJob' . "\0" . 'bounceCounter', '' . "\0" . 'Wizacha\\Async\\Debouncer\\DebouncedJob' . "\0" . 'jobPayload'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (DebouncedJob $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getQueue(): string
    {
        if ($this->__isInitialized__ === false) {
            return  parent::getQueue();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getQueue', []);

        return parent::getQueue();
    }

    /**
     * {@inheritDoc}
     */
    public function getDebounceKey(): string
    {
        if ($this->__isInitialized__ === false) {
            return  parent::getDebounceKey();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDebounceKey', []);

        return parent::getDebounceKey();
    }

    /**
     * {@inheritDoc}
     */
    public function getPayload(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPayload', []);

        return parent::getPayload();
    }

    /**
     * {@inheritDoc}
     */
    public function getCreatedAt(): \DateTimeInterface
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCreatedAt', []);

        return parent::getCreatedAt();
    }

    /**
     * {@inheritDoc}
     */
    public function getUpdatedAt(): \DateTimeInterface
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUpdatedAt', []);

        return parent::getUpdatedAt();
    }

    /**
     * {@inheritDoc}
     */
    public function getTtl(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTtl', []);

        return parent::getTtl();
    }

    /**
     * {@inheritDoc}
     */
    public function getBounceCounter(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getBounceCounter', []);

        return parent::getBounceCounter();
    }

    /**
     * {@inheritDoc}
     */
    public function getExpiresAt(): \DateTimeInterface
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getExpiresAt', []);

        return parent::getExpiresAt();
    }

    /**
     * {@inheritDoc}
     */
    public function getJobPayload(): \Wizacha\Async\FunctionPayload
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getJobPayload', []);

        return parent::getJobPayload();
    }

    /**
     * {@inheritDoc}
     */
    public function expose(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'expose', []);

        return parent::expose();
    }

}
