<?php

namespace Proxies\__CG__\Wizacha\Marketplace\Transaction;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Transaction extends \Wizacha\Marketplace\Transaction\Transaction implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', 'id', 'orderId', 'transactionReference', 'transactionLabel', 'type', 'status', 'amount', 'processorName', 'processorInformations', 'refundId', 'origin', 'destination', 'currency', 'companyId', 'createdAt', 'updatedAt'];
        }

        return ['__isInitialized__', 'id', 'orderId', 'transactionReference', 'transactionLabel', 'type', 'status', 'amount', 'processorName', 'processorInformations', 'refundId', 'origin', 'destination', 'currency', 'companyId', 'createdAt', 'updatedAt'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (Transaction $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getId(): ?\Rhumsaa\Uuid\Uuid
    {
        if ($this->__isInitialized__ === false) {
            return  parent::getId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function getOrderId(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOrderId', []);

        return parent::getOrderId();
    }

    /**
     * {@inheritDoc}
     */
    public function getTransactionReference(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTransactionReference', []);

        return parent::getTransactionReference();
    }

    /**
     * {@inheritDoc}
     */
    public function setTransactionReference(?string $transactionReference): \Wizacha\Marketplace\Transaction\Transaction
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTransactionReference', [$transactionReference]);

        return parent::setTransactionReference($transactionReference);
    }

    /**
     * {@inheritDoc}
     */
    public function getTransactionLabel(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTransactionLabel', []);

        return parent::getTransactionLabel();
    }

    /**
     * {@inheritDoc}
     */
    public function setTransactionLabel(?string $transactionLabel): \Wizacha\Marketplace\Transaction\Transaction
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTransactionLabel', [$transactionLabel]);

        return parent::setTransactionLabel($transactionLabel);
    }

    /**
     * {@inheritDoc}
     */
    public function getType(): \Wizacha\Marketplace\Transaction\TransactionType
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getType', []);

        return parent::getType();
    }

    /**
     * {@inheritDoc}
     */
    public function getStatus(): \Wizacha\Marketplace\Transaction\TransactionStatus
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getStatus', []);

        return parent::getStatus();
    }

    /**
     * {@inheritDoc}
     */
    public function setStatus(\Wizacha\Marketplace\Transaction\TransactionStatus $status): \Wizacha\Marketplace\Transaction\Transaction
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setStatus', [$status]);

        return parent::setStatus($status);
    }

    /**
     * {@inheritDoc}
     */
    public function getRefundId(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRefundId', []);

        return parent::getRefundId();
    }

    /**
     * {@inheritDoc}
     */
    public function setRefundId(?int $refundId): \Wizacha\Marketplace\Transaction\Transaction
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setRefundId', [$refundId]);

        return parent::setRefundId($refundId);
    }

    /**
     * {@inheritDoc}
     */
    public function getAmount(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAmount', []);

        return parent::getAmount();
    }

    /**
     * {@inheritDoc}
     */
    public function getProcessorName(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProcessorName', []);

        return parent::getProcessorName();
    }

    /**
     * {@inheritDoc}
     */
    public function setProcessorName(?string $processorName): \Wizacha\Marketplace\Transaction\Transaction
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setProcessorName', [$processorName]);

        return parent::setProcessorName($processorName);
    }

    /**
     * {@inheritDoc}
     */
    public function getProcessorInformations(): ?array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProcessorInformations', []);

        return parent::getProcessorInformations();
    }

    /**
     * {@inheritDoc}
     */
    public function addInformation(string $key, $value): \Wizacha\Marketplace\Transaction\Transaction
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addInformation', [$key, $value]);

        return parent::addInformation($key, $value);
    }

    /**
     * {@inheritDoc}
     */
    public function setProcessorInformations(?array $processorInformations): \Wizacha\Marketplace\Transaction\Transaction
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setProcessorInformations', [$processorInformations]);

        return parent::setProcessorInformations($processorInformations);
    }

    /**
     * {@inheritDoc}
     */
    public function isReady(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isReady', []);

        return parent::isReady();
    }

    /**
     * {@inheritDoc}
     */
    public function isPending(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isPending', []);

        return parent::isPending();
    }

    /**
     * {@inheritDoc}
     */
    public function isSuccess(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isSuccess', []);

        return parent::isSuccess();
    }

    /**
     * {@inheritDoc}
     */
    public function isFailed(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isFailed', []);

        return parent::isFailed();
    }

    /**
     * {@inheritDoc}
     */
    public function setOrderId(?int $orderId): \Wizacha\Marketplace\Transaction\Transaction
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setOrderId', [$orderId]);

        return parent::setOrderId($orderId);
    }

    /**
     * {@inheritDoc}
     */
    public function setType(\Wizacha\Marketplace\Transaction\TransactionType $type): \Wizacha\Marketplace\Transaction\Transaction
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setType', [$type]);

        return parent::setType($type);
    }

    /**
     * {@inheritDoc}
     */
    public function setAmount(\Wizacha\Money\Money $amount): \Wizacha\Marketplace\Transaction\Transaction
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAmount', [$amount]);

        return parent::setAmount($amount);
    }

    /**
     * {@inheritDoc}
     */
    public function getInformation(string $key): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getInformation', [$key]);

        return parent::getInformation($key);
    }

    /**
     * {@inheritDoc}
     */
    public function getLabel(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLabel', []);

        return parent::getLabel();
    }

    /**
     * {@inheritDoc}
     */
    public function getOrigin(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOrigin', []);

        return parent::getOrigin();
    }

    /**
     * {@inheritDoc}
     */
    public function setOrigin(?string $origin): \Wizacha\Marketplace\Transaction\Transaction
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setOrigin', [$origin]);

        return parent::setOrigin($origin);
    }

    /**
     * {@inheritDoc}
     */
    public function getDestination(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDestination', []);

        return parent::getDestination();
    }

    /**
     * {@inheritDoc}
     */
    public function setDestination(?string $destination): \Wizacha\Marketplace\Transaction\Transaction
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDestination', [$destination]);

        return parent::setDestination($destination);
    }

    /**
     * {@inheritDoc}
     */
    public function getCurrency(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCurrency', []);

        return parent::getCurrency();
    }

    /**
     * {@inheritDoc}
     */
    public function setCurrency(?string $currency): \Wizacha\Marketplace\Transaction\Transaction
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCurrency', [$currency]);

        return parent::setCurrency($currency);
    }

    /**
     * {@inheritDoc}
     */
    public function getCompanyId(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCompanyId', []);

        return parent::getCompanyId();
    }

    /**
     * {@inheritDoc}
     */
    public function setCompanyId(?int $companyId): \Wizacha\Marketplace\Transaction\Transaction
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCompanyId', [$companyId]);

        return parent::setCompanyId($companyId);
    }

    /**
     * {@inheritDoc}
     */
    public function expose(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'expose', []);

        return parent::expose();
    }

    /**
     * {@inheritDoc}
     */
    public function exposeTransactionHistory(string $dateFormat = 'd/m/Y H:i:s'): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'exposeTransactionHistory', [$dateFormat]);

        return parent::exposeTransactionHistory($dateFormat);
    }

    /**
     * {@inheritDoc}
     */
    public function getCreatedAt(): \DateTimeImmutable
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCreatedAt', []);

        return parent::getCreatedAt();
    }

    /**
     * {@inheritDoc}
     */
    public function setCreatedAt(\DateTime $createdAt): \Wizacha\Marketplace\Transaction\Transaction
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCreatedAt', [$createdAt]);

        return parent::setCreatedAt($createdAt);
    }

    /**
     * {@inheritDoc}
     */
    public function getUpdatedAt(): \DateTimeImmutable
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUpdatedAt', []);

        return parent::getUpdatedAt();
    }

    /**
     * {@inheritDoc}
     */
    public function updateTimestamp()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'updateTimestamp', []);

        return parent::updateTimestamp();
    }

}
