<?php

namespace Proxies\__CG__\Wizacha\Marketplace\ProductOptionInventory;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class ProductOptionInventory extends \Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', 'id', 'priceTiers', 'combination', 'productId', 'productCode', 'combinationHash', 'amount', 'temp', 'position', 'wPrice', 'crossedOutPrice', 'affiliateLink', 'declinationId', 'supplierReference', 'infiniteStock'];
        }

        return ['__isInitialized__', 'id', 'priceTiers', 'combination', 'productId', 'productCode', 'combinationHash', 'amount', 'temp', 'position', 'wPrice', 'crossedOutPrice', 'affiliateLink', 'declinationId', 'supplierReference', 'infiniteStock'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (ProductOptionInventory $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getId(): ?int
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function setPriceTiers(iterable $priceTiers): \Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPriceTiers', [$priceTiers]);

        return parent::setPriceTiers($priceTiers);
    }

    /**
     * {@inheritDoc}
     */
    public function getPriceTiers(): \Doctrine\Common\Collections\Collection
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPriceTiers', []);

        return parent::getPriceTiers();
    }

    /**
     * {@inheritDoc}
     */
    public function clearPriceTiers(): \Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'clearPriceTiers', []);

        return parent::clearPriceTiers();
    }

    /**
     * {@inheritDoc}
     */
    public function addPriceTier(\Wizacha\Marketplace\PriceTier\PriceTier $priceTier): \Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addPriceTier', [$priceTier]);

        return parent::addPriceTier($priceTier);
    }

    /**
     * {@inheritDoc}
     */
    public function removePriceTier(\Wizacha\Marketplace\PriceTier\PriceTier $priceTier): \Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'removePriceTier', [$priceTier]);

        return parent::removePriceTier($priceTier);
    }

    /**
     * {@inheritDoc}
     */
    public function getCombination(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCombination', []);

        return parent::getCombination();
    }

    /**
     * {@inheritDoc}
     */
    public function setCombination(?string $combination): \Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCombination', [$combination]);

        return parent::setCombination($combination);
    }

    /**
     * {@inheritDoc}
     */
    public function setProductId(?int $productId): \Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setProductId', [$productId]);

        return parent::setProductId($productId);
    }

    /**
     * {@inheritDoc}
     */
    public function getProductId(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProductId', []);

        return parent::getProductId();
    }

    /**
     * {@inheritDoc}
     */
    public function setProductCode(?string $productCode): \Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setProductCode', [$productCode]);

        return parent::setProductCode($productCode);
    }

    /**
     * {@inheritDoc}
     */
    public function getProductCode(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProductCode', []);

        return parent::getProductCode();
    }

    /**
     * {@inheritDoc}
     */
    public function setCombinationHash(?int $combinationHash): \Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCombinationHash', [$combinationHash]);

        return parent::setCombinationHash($combinationHash);
    }

    /**
     * {@inheritDoc}
     */
    public function getCombinationHash(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCombinationHash', []);

        return parent::getCombinationHash();
    }

    /**
     * {@inheritDoc}
     */
    public function setAmount(?int $amount): \Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAmount', [$amount]);

        return parent::setAmount($amount);
    }

    /**
     * {@inheritDoc}
     */
    public function getAmount(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAmount', []);

        return parent::getAmount();
    }

    /**
     * {@inheritDoc}
     */
    public function setTemp(?string $temp): \Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTemp', [$temp]);

        return parent::setTemp($temp);
    }

    /**
     * {@inheritDoc}
     */
    public function getTemp(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTemp', []);

        return parent::getTemp();
    }

    /**
     * {@inheritDoc}
     */
    public function setPosition(?int $position): \Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPosition', [$position]);

        return parent::setPosition($position);
    }

    /**
     * {@inheritDoc}
     */
    public function getPosition(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPosition', []);

        return parent::getPosition();
    }

    /**
     * {@inheritDoc}
     */
    public function setWPrice(?\Wizacha\Money\Money $wPrice): \Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setWPrice', [$wPrice]);

        return parent::setWPrice($wPrice);
    }

    /**
     * {@inheritDoc}
     */
    public function getWPrice(): ?\Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getWPrice', []);

        return parent::getWPrice();
    }

    /**
     * {@inheritDoc}
     */
    public function setCrossedOutPrice(?\Wizacha\Money\Money $crossedOutPrice): \Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCrossedOutPrice', [$crossedOutPrice]);

        return parent::setCrossedOutPrice($crossedOutPrice);
    }

    /**
     * {@inheritDoc}
     */
    public function getCrossedOutPrice(): ?\Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCrossedOutPrice', []);

        return parent::getCrossedOutPrice();
    }

    /**
     * {@inheritDoc}
     */
    public function setAffiliateLink(?string $affiliateLink): \Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAffiliateLink', [$affiliateLink]);

        return parent::setAffiliateLink($affiliateLink);
    }

    /**
     * {@inheritDoc}
     */
    public function getAffiliateLink(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAffiliateLink', []);

        return parent::getAffiliateLink();
    }

    /**
     * {@inheritDoc}
     */
    public function setDeclinationId(?string $declinationId): \Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDeclinationId', [$declinationId]);

        return parent::setDeclinationId($declinationId);
    }

    /**
     * {@inheritDoc}
     */
    public function getDeclinationId(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDeclinationId', []);

        return parent::getDeclinationId();
    }

    /**
     * {@inheritDoc}
     */
    public function setInfiniteStock(?bool $infiniteStock): \Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setInfiniteStock', [$infiniteStock]);

        return parent::setInfiniteStock($infiniteStock);
    }

    /**
     * {@inheritDoc}
     */
    public function getInfiniteStock(): ?bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getInfiniteStock', []);

        return parent::getInfiniteStock();
    }

    /**
     * {@inheritDoc}
     */
    public function getSupplierReference(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSupplierReference', []);

        return parent::getSupplierReference();
    }

    /**
     * {@inheritDoc}
     */
    public function setSupplierReference(?string $supplierReference): \Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSupplierReference', [$supplierReference]);

        return parent::setSupplierReference($supplierReference);
    }

    /**
     * {@inheritDoc}
     */
    public function expose(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'expose', []);

        return parent::expose();
    }

}
