<?php

namespace Proxies\__CG__\Wizacha\Marketplace\Organisation;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class OrganisationBasket extends \Wizacha\Marketplace\Organisation\OrganisationBasket implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\OrganisationBasket' . "\0" . 'basketId', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\OrganisationBasket' . "\0" . 'organisation', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\OrganisationBasket' . "\0" . 'user', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\OrganisationBasket' . "\0" . 'name', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\OrganisationBasket' . "\0" . 'isLocked', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\OrganisationBasket' . "\0" . 'lockedAt', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\OrganisationBasket' . "\0" . 'isAccepted', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\OrganisationBasket' . "\0" . 'isCheckout', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\OrganisationBasket' . "\0" . 'isHidden', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\OrganisationBasket' . "\0" . 'createdAt'];
        }

        return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\OrganisationBasket' . "\0" . 'basketId', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\OrganisationBasket' . "\0" . 'organisation', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\OrganisationBasket' . "\0" . 'user', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\OrganisationBasket' . "\0" . 'name', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\OrganisationBasket' . "\0" . 'isLocked', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\OrganisationBasket' . "\0" . 'lockedAt', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\OrganisationBasket' . "\0" . 'isAccepted', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\OrganisationBasket' . "\0" . 'isCheckout', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\OrganisationBasket' . "\0" . 'isHidden', '' . "\0" . 'Wizacha\\Marketplace\\Organisation\\OrganisationBasket' . "\0" . 'createdAt'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (OrganisationBasket $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getBasketId(): string
    {
        if ($this->__isInitialized__ === false) {
            return  parent::getBasketId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getBasketId', []);

        return parent::getBasketId();
    }

    /**
     * {@inheritDoc}
     */
    public function getUser(): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUser', []);

        return parent::getUser();
    }

    /**
     * {@inheritDoc}
     */
    public function isOwnedBy(\Wizacha\Marketplace\User\User $user): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isOwnedBy', [$user]);

        return parent::isOwnedBy($user);
    }

    /**
     * {@inheritDoc}
     */
    public function getName(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getName', []);

        return parent::getName();
    }

    /**
     * {@inheritDoc}
     */
    public function isLocked(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isLocked', []);

        return parent::isLocked();
    }

    /**
     * {@inheritDoc}
     */
    public function lock(): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'lock', []);

        parent::lock();
    }

    /**
     * {@inheritDoc}
     */
    public function getLockedAt(): \DateTimeImmutable
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLockedAt', []);

        return parent::getLockedAt();
    }

    /**
     * {@inheritDoc}
     */
    public function isAccepted(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isAccepted', []);

        return parent::isAccepted();
    }

    /**
     * {@inheritDoc}
     */
    public function isRefused(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isRefused', []);

        return parent::isRefused();
    }

    /**
     * {@inheritDoc}
     */
    public function accept(): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'accept', []);

        parent::accept();
    }

    /**
     * {@inheritDoc}
     */
    public function isCheckout(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isCheckout', []);

        return parent::isCheckout();
    }

    /**
     * {@inheritDoc}
     */
    public function checkout(): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'checkout', []);

        parent::checkout();
    }

    /**
     * {@inheritDoc}
     */
    public function isHidden(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isHidden', []);

        return parent::isHidden();
    }

    /**
     * {@inheritDoc}
     */
    public function hide(): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hide', []);

        parent::hide();
    }

    /**
     * {@inheritDoc}
     */
    public function getOrganisation(): \Wizacha\Marketplace\Organisation\Organisation
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOrganisation', []);

        return parent::getOrganisation();
    }

    /**
     * {@inheritDoc}
     */
    public function getBasket(): ?\Wizacha\Marketplace\Basket\ReadModel\Basket
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getBasket', []);

        return parent::getBasket();
    }

    /**
     * {@inheritDoc}
     */
    public function getCreatedAt(): \DateTimeImmutable
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCreatedAt', []);

        return parent::getCreatedAt();
    }

    /**
     * {@inheritDoc}
     */
    public function expose(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'expose', []);

        return parent::expose();
    }

}
