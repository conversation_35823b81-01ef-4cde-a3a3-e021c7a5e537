<?php

namespace Proxies\__CG__\Wizacha\Component\Import;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class EximJob extends \Wizacha\Component\Import\EximJob implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Wizacha\\Component\\Import\\EximJob' . "\0" . 'id', '' . "\0" . 'Wizacha\\Component\\Import\\EximJob' . "\0" . 'type', '' . "\0" . 'Wizacha\\Component\\Import\\EximJob' . "\0" . 'status', '' . "\0" . 'Wizacha\\Component\\Import\\EximJob' . "\0" . 'userId', '' . "\0" . 'Wizacha\\Component\\Import\\EximJob' . "\0" . 'companyId', '' . "\0" . 'Wizacha\\Component\\Import\\EximJob' . "\0" . 'file', '' . "\0" . 'Wizacha\\Component\\Import\\EximJob' . "\0" . 'createdAt', '' . "\0" . 'Wizacha\\Component\\Import\\EximJob' . "\0" . 'startedAt', '' . "\0" . 'Wizacha\\Component\\Import\\EximJob' . "\0" . 'finishedAt', '' . "\0" . 'Wizacha\\Component\\Import\\EximJob' . "\0" . 'nbLines', '' . "\0" . 'Wizacha\\Component\\Import\\EximJob' . "\0" . 'nbImported', '' . "\0" . 'Wizacha\\Component\\Import\\EximJob' . "\0" . 'nbWarnings', '' . "\0" . 'Wizacha\\Component\\Import\\EximJob' . "\0" . 'isImport'];
        }

        return ['__isInitialized__', '' . "\0" . 'Wizacha\\Component\\Import\\EximJob' . "\0" . 'id', '' . "\0" . 'Wizacha\\Component\\Import\\EximJob' . "\0" . 'type', '' . "\0" . 'Wizacha\\Component\\Import\\EximJob' . "\0" . 'status', '' . "\0" . 'Wizacha\\Component\\Import\\EximJob' . "\0" . 'userId', '' . "\0" . 'Wizacha\\Component\\Import\\EximJob' . "\0" . 'companyId', '' . "\0" . 'Wizacha\\Component\\Import\\EximJob' . "\0" . 'file', '' . "\0" . 'Wizacha\\Component\\Import\\EximJob' . "\0" . 'createdAt', '' . "\0" . 'Wizacha\\Component\\Import\\EximJob' . "\0" . 'startedAt', '' . "\0" . 'Wizacha\\Component\\Import\\EximJob' . "\0" . 'finishedAt', '' . "\0" . 'Wizacha\\Component\\Import\\EximJob' . "\0" . 'nbLines', '' . "\0" . 'Wizacha\\Component\\Import\\EximJob' . "\0" . 'nbImported', '' . "\0" . 'Wizacha\\Component\\Import\\EximJob' . "\0" . 'nbWarnings', '' . "\0" . 'Wizacha\\Component\\Import\\EximJob' . "\0" . 'isImport'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (EximJob $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getId(): ?string
    {
        if ($this->__isInitialized__ === false) {
            return  parent::getId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function setId(string $id): \Wizacha\Component\Import\EximJob
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setId', [$id]);

        return parent::setId($id);
    }

    /**
     * {@inheritDoc}
     */
    public function getType(): \Wizacha\Component\Import\JobType
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getType', []);

        return parent::getType();
    }

    /**
     * {@inheritDoc}
     */
    public function setType(\Wizacha\Component\Import\JobType $type): \Wizacha\Component\Import\EximJob
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setType', [$type]);

        return parent::setType($type);
    }

    /**
     * {@inheritDoc}
     */
    public function getStatus(): \Wizacha\Component\Import\JobStatus
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getStatus', []);

        return parent::getStatus();
    }

    /**
     * {@inheritDoc}
     */
    public function setStatus(\Wizacha\Component\Import\JobStatus $status): \Wizacha\Component\Import\EximJob
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setStatus', [$status]);

        return parent::setStatus($status);
    }

    /**
     * {@inheritDoc}
     */
    public function getUserId(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUserId', []);

        return parent::getUserId();
    }

    /**
     * {@inheritDoc}
     */
    public function setUserId(int $userId): \Wizacha\Component\Import\EximJob
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setUserId', [$userId]);

        return parent::setUserId($userId);
    }

    /**
     * {@inheritDoc}
     */
    public function getCompanyId(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCompanyId', []);

        return parent::getCompanyId();
    }

    /**
     * {@inheritDoc}
     */
    public function setCompanyId(?int $companyId): \Wizacha\Component\Import\EximJob
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCompanyId', [$companyId]);

        return parent::setCompanyId($companyId);
    }

    /**
     * {@inheritDoc}
     */
    public function getFile(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getFile', []);

        return parent::getFile();
    }

    /**
     * {@inheritDoc}
     */
    public function setFile(?string $file): \Wizacha\Component\Import\EximJob
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setFile', [$file]);

        return parent::setFile($file);
    }

    /**
     * {@inheritDoc}
     */
    public function getCreatedAt(): \DateTimeInterface
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCreatedAt', []);

        return parent::getCreatedAt();
    }

    /**
     * {@inheritDoc}
     */
    public function setCreatedAt(\DateTimeInterface $createdAt): \Wizacha\Component\Import\EximJob
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCreatedAt', [$createdAt]);

        return parent::setCreatedAt($createdAt);
    }

    /**
     * {@inheritDoc}
     */
    public function getStartedAt(): ?\DateTimeInterface
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getStartedAt', []);

        return parent::getStartedAt();
    }

    /**
     * {@inheritDoc}
     */
    public function setStartedAt(\DateTimeInterface $startedAt): \Wizacha\Component\Import\EximJob
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setStartedAt', [$startedAt]);

        return parent::setStartedAt($startedAt);
    }

    /**
     * {@inheritDoc}
     */
    public function getFinishedAt(): ?\DateTimeInterface
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getFinishedAt', []);

        return parent::getFinishedAt();
    }

    /**
     * {@inheritDoc}
     */
    public function setFinishedAt(?\DateTimeInterface $finishedAt): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setFinishedAt', [$finishedAt]);

        parent::setFinishedAt($finishedAt);
    }

    /**
     * {@inheritDoc}
     */
    public function getNbLines(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNbLines', []);

        return parent::getNbLines();
    }

    /**
     * {@inheritDoc}
     */
    public function setNbLines(int $nbLines): \Wizacha\Component\Import\EximJob
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNbLines', [$nbLines]);

        return parent::setNbLines($nbLines);
    }

    /**
     * {@inheritDoc}
     */
    public function getNbImported(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNbImported', []);

        return parent::getNbImported();
    }

    /**
     * {@inheritDoc}
     */
    public function setNbImported(int $nbImported): \Wizacha\Component\Import\EximJob
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNbImported', [$nbImported]);

        return parent::setNbImported($nbImported);
    }

    /**
     * {@inheritDoc}
     */
    public function getNbRejected(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNbRejected', []);

        return parent::getNbRejected();
    }

    /**
     * {@inheritDoc}
     */
    public function getNbWarnings(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNbWarnings', []);

        return parent::getNbWarnings();
    }

    /**
     * {@inheritDoc}
     */
    public function setNbWarnings(int $nbWarnings): \Wizacha\Component\Import\EximJob
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNbWarnings', [$nbWarnings]);

        return parent::setNbWarnings($nbWarnings);
    }

    /**
     * {@inheritDoc}
     */
    public function getPercent(): float
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPercent', []);

        return parent::getPercent();
    }

    /**
     * {@inheritDoc}
     */
    public function finish()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'finish', []);

        return parent::finish();
    }

    /**
     * {@inheritDoc}
     */
    public function getDuration(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDuration', []);

        return parent::getDuration();
    }

    /**
     * {@inheritDoc}
     */
    public function expose(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'expose', []);

        return parent::expose();
    }

    /**
     * {@inheritDoc}
     */
    public function isImport(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isImport', []);

        return parent::isImport();
    }

    /**
     * {@inheritDoc}
     */
    public function setIsImport(bool $isImport): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIsImport', [$isImport]);

        parent::setIsImport($isImport);
    }

}
