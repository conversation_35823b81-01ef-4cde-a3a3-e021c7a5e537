<?php

namespace Proxies\__CG__\Wizacha\Marketplace\User;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class AddressBook extends \Wizacha\Marketplace\User\AddressBook implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'id', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'label', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'comment', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'title', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'firstname', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'lastname', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'company', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'address', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'address2', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'city', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'county', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'state', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'country', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'zipcode', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'phone', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'division', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'user'];
        }

        return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'id', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'label', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'comment', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'title', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'firstname', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'lastname', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'company', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'address', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'address2', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'city', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'county', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'state', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'country', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'zipcode', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'phone', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'division', '' . "\0" . 'Wizacha\\Marketplace\\User\\AddressBook' . "\0" . 'user'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (AddressBook $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function setId(string $id): \Wizacha\Marketplace\User\AddressBook
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setId', [$id]);

        return parent::setId($id);
    }

    /**
     * {@inheritDoc}
     */
    public function getId(): string
    {
        if ($this->__isInitialized__ === false) {
            return  parent::getId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function setLabel(?string $label): \Wizacha\Marketplace\User\AddressBook
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setLabel', [$label]);

        return parent::setLabel($label);
    }

    /**
     * {@inheritDoc}
     */
    public function getLabel(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLabel', []);

        return parent::getLabel();
    }

    /**
     * {@inheritDoc}
     */
    public function setComment(?string $comment): \Wizacha\Marketplace\User\AddressBook
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setComment', [$comment]);

        return parent::setComment($comment);
    }

    /**
     * {@inheritDoc}
     */
    public function getComment(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getComment', []);

        return parent::getComment();
    }

    /**
     * {@inheritDoc}
     */
    public function setTitle(?string $title): \Wizacha\Marketplace\User\AddressBook
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTitle', [$title]);

        return parent::setTitle($title);
    }

    /**
     * {@inheritDoc}
     */
    public function getTitle(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTitle', []);

        return parent::getTitle();
    }

    /**
     * {@inheritDoc}
     */
    public function setFirstname(?string $firstname): \Wizacha\Marketplace\User\AddressBook
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setFirstname', [$firstname]);

        return parent::setFirstname($firstname);
    }

    /**
     * {@inheritDoc}
     */
    public function getFirstname(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getFirstname', []);

        return parent::getFirstname();
    }

    /**
     * {@inheritDoc}
     */
    public function setLastname(?string $lastname): \Wizacha\Marketplace\User\AddressBook
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setLastname', [$lastname]);

        return parent::setLastname($lastname);
    }

    /**
     * {@inheritDoc}
     */
    public function getLastName()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLastName', []);

        return parent::getLastName();
    }

    /**
     * {@inheritDoc}
     */
    public function setCompany(?string $company): \Wizacha\Marketplace\User\AddressBook
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCompany', [$company]);

        return parent::setCompany($company);
    }

    /**
     * {@inheritDoc}
     */
    public function getCompany(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCompany', []);

        return parent::getCompany();
    }

    /**
     * {@inheritDoc}
     */
    public function setAddress(?string $address): \Wizacha\Marketplace\User\AddressBook
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAddress', [$address]);

        return parent::setAddress($address);
    }

    /**
     * {@inheritDoc}
     */
    public function getAddress(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAddress', []);

        return parent::getAddress();
    }

    /**
     * {@inheritDoc}
     */
    public function setAddress2(?string $address2): \Wizacha\Marketplace\User\AddressBook
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAddress2', [$address2]);

        return parent::setAddress2($address2);
    }

    /**
     * {@inheritDoc}
     */
    public function getAddress2(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAddress2', []);

        return parent::getAddress2();
    }

    /**
     * {@inheritDoc}
     */
    public function setCity(?string $city): \Wizacha\Marketplace\User\AddressBook
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCity', [$city]);

        return parent::setCity($city);
    }

    /**
     * {@inheritDoc}
     */
    public function getCity(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCity', []);

        return parent::getCity();
    }

    /**
     * {@inheritDoc}
     */
    public function setCounty(?string $county): \Wizacha\Marketplace\User\AddressBook
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCounty', [$county]);

        return parent::setCounty($county);
    }

    /**
     * {@inheritDoc}
     */
    public function getCounty(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCounty', []);

        return parent::getCounty();
    }

    /**
     * {@inheritDoc}
     */
    public function setState(?string $state): \Wizacha\Marketplace\User\AddressBook
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setState', [$state]);

        return parent::setState($state);
    }

    /**
     * {@inheritDoc}
     */
    public function getState(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getState', []);

        return parent::getState();
    }

    /**
     * {@inheritDoc}
     */
    public function setCountry(?string $country): \Wizacha\Marketplace\User\AddressBook
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCountry', [$country]);

        return parent::setCountry($country);
    }

    /**
     * {@inheritDoc}
     */
    public function getCountry(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCountry', []);

        return parent::getCountry();
    }

    /**
     * {@inheritDoc}
     */
    public function setZipCode(?string $zipcode): \Wizacha\Marketplace\User\AddressBook
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setZipCode', [$zipcode]);

        return parent::setZipCode($zipcode);
    }

    /**
     * {@inheritDoc}
     */
    public function getZipCode(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getZipCode', []);

        return parent::getZipCode();
    }

    /**
     * {@inheritDoc}
     */
    public function setPhone(?string $phone): \Wizacha\Marketplace\User\AddressBook
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPhone', [$phone]);

        return parent::setPhone($phone);
    }

    /**
     * {@inheritDoc}
     */
    public function getPhone(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPhone', []);

        return parent::getPhone();
    }

    /**
     * {@inheritDoc}
     */
    public function setDivision(?string $division): \Wizacha\Marketplace\User\AddressBook
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDivision', [$division]);

        return parent::setDivision($division);
    }

    /**
     * {@inheritDoc}
     */
    public function getDivision(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDivision', []);

        return parent::getDivision();
    }

    /**
     * {@inheritDoc}
     */
    public function setUser(\Wizacha\Marketplace\User\User $user): \Wizacha\Marketplace\User\AddressBook
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setUser', [$user]);

        return parent::setUser($user);
    }

    /**
     * {@inheritDoc}
     */
    public function getUser(): \Wizacha\Marketplace\User\User
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUser', []);

        return parent::getUser();
    }

    /**
     * {@inheritDoc}
     */
    public function jsonSerialize(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'jsonSerialize', []);

        return parent::jsonSerialize();
    }

    /**
     * {@inheritDoc}
     */
    public function assertValid(bool $featureAvailableOffers = false): \Wizacha\Marketplace\User\AddressBook
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'assertValid', [$featureAvailableOffers]);

        return parent::assertValid($featureAvailableOffers);
    }

}
