<?php

namespace Proxies\__CG__\Wizacha\Marketplace\CompanyPerson;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Company<PERSON>erson extends \Wizacha\Marketplace\CompanyPerson\CompanyPerson implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'id', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'companyId', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'firstname', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'lastname', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'title', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'address', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'address2', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'city', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'state', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'zipcode', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'country', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'birthdate', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'birthplaceCity', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'birthplaceCountry', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'type', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'ownershipPercentage', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'createdAt', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'nationalities'];
        }

        return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'id', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'companyId', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'firstname', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'lastname', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'title', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'address', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'address2', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'city', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'state', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'zipcode', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'country', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'birthdate', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'birthplaceCity', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'birthplaceCountry', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'type', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'ownershipPercentage', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'createdAt', '' . "\0" . 'Wizacha\\Marketplace\\CompanyPerson\\CompanyPerson' . "\0" . 'nationalities'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (CompanyPerson $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function setId(?int $id): \Wizacha\Marketplace\CompanyPerson\CompanyPerson
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setId', [$id]);

        return parent::setId($id);
    }

    /**
     * {@inheritDoc}
     */
    public function setCompanyId(?int $companyId): \Wizacha\Marketplace\CompanyPerson\CompanyPerson
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCompanyId', [$companyId]);

        return parent::setCompanyId($companyId);
    }

    /**
     * {@inheritDoc}
     */
    public function setFirstName(?string $firstname): \Wizacha\Marketplace\CompanyPerson\CompanyPerson
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setFirstName', [$firstname]);

        return parent::setFirstName($firstname);
    }

    /**
     * {@inheritDoc}
     */
    public function setLastName(?string $lastname): \Wizacha\Marketplace\CompanyPerson\CompanyPerson
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setLastName', [$lastname]);

        return parent::setLastName($lastname);
    }

    /**
     * {@inheritDoc}
     */
    public function setTitle(?string $title): \Wizacha\Marketplace\CompanyPerson\CompanyPerson
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTitle', [$title]);

        return parent::setTitle($title);
    }

    /**
     * {@inheritDoc}
     */
    public function setAddress(?string $address): \Wizacha\Marketplace\CompanyPerson\CompanyPerson
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAddress', [$address]);

        return parent::setAddress($address);
    }

    /**
     * {@inheritDoc}
     */
    public function setAddress2(?string $address2): \Wizacha\Marketplace\CompanyPerson\CompanyPerson
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAddress2', [$address2]);

        return parent::setAddress2($address2);
    }

    /**
     * {@inheritDoc}
     */
    public function setCity(?string $city): \Wizacha\Marketplace\CompanyPerson\CompanyPerson
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCity', [$city]);

        return parent::setCity($city);
    }

    /**
     * {@inheritDoc}
     */
    public function setState(?string $state): \Wizacha\Marketplace\CompanyPerson\CompanyPerson
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setState', [$state]);

        return parent::setState($state);
    }

    /**
     * {@inheritDoc}
     */
    public function setZipCode(?string $zipCode): \Wizacha\Marketplace\CompanyPerson\CompanyPerson
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setZipCode', [$zipCode]);

        return parent::setZipCode($zipCode);
    }

    /**
     * {@inheritDoc}
     */
    public function setCountry(?string $country): \Wizacha\Marketplace\CompanyPerson\CompanyPerson
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCountry', [$country]);

        return parent::setCountry($country);
    }

    /**
     * {@inheritDoc}
     */
    public function setBirthdate(?\DateTime $birthdate): \Wizacha\Marketplace\CompanyPerson\CompanyPerson
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setBirthdate', [$birthdate]);

        return parent::setBirthdate($birthdate);
    }

    /**
     * {@inheritDoc}
     */
    public function setBirthplaceCity(?string $birthplaceCity): \Wizacha\Marketplace\CompanyPerson\CompanyPerson
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setBirthplaceCity', [$birthplaceCity]);

        return parent::setBirthplaceCity($birthplaceCity);
    }

    /**
     * {@inheritDoc}
     */
    public function setBirthplaceCountry(?string $birthplaceCountry): \Wizacha\Marketplace\CompanyPerson\CompanyPerson
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setBirthplaceCountry', [$birthplaceCountry]);

        return parent::setBirthplaceCountry($birthplaceCountry);
    }

    /**
     * {@inheritDoc}
     */
    public function setOwnershipPercentage(?float $ownershipPercentage): \Wizacha\Marketplace\CompanyPerson\CompanyPerson
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setOwnershipPercentage', [$ownershipPercentage]);

        return parent::setOwnershipPercentage($ownershipPercentage);
    }

    /**
     * {@inheritDoc}
     */
    public function setType(?\Wizacha\Marketplace\CompanyPerson\CompanyPersonType $type): \Wizacha\Marketplace\CompanyPerson\CompanyPerson
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setType', [$type]);

        return parent::setType($type);
    }

    /**
     * {@inheritDoc}
     */
    public function setNationalities(iterable $nationalities): \Wizacha\Marketplace\CompanyPerson\CompanyPerson
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNationalities', [$nationalities]);

        return parent::setNationalities($nationalities);
    }

    /**
     * {@inheritDoc}
     */
    public function addNationality(?\Wizacha\Marketplace\Country\Country $nationality): \Wizacha\Marketplace\CompanyPerson\CompanyPerson
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addNationality', [$nationality]);

        return parent::addNationality($nationality);
    }

    /**
     * {@inheritDoc}
     */
    public function removeNationality(?\Wizacha\Marketplace\Country\Country $nationality): \Wizacha\Marketplace\CompanyPerson\CompanyPerson
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'removeNationality', [$nationality]);

        return parent::removeNationality($nationality);
    }

    /**
     * {@inheritDoc}
     */
    public function setCreatedAt(?\DateTime $createdAt): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCreatedAt', [$createdAt]);

        parent::setCreatedAt($createdAt);
    }

    /**
     * {@inheritDoc}
     */
    public function getId(): ?int
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function getCompanyId(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCompanyId', []);

        return parent::getCompanyId();
    }

    /**
     * {@inheritDoc}
     */
    public function getFirstName(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getFirstName', []);

        return parent::getFirstName();
    }

    /**
     * {@inheritDoc}
     */
    public function getLastName(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLastName', []);

        return parent::getLastName();
    }

    /**
     * {@inheritDoc}
     */
    public function getTitle(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTitle', []);

        return parent::getTitle();
    }

    /**
     * {@inheritDoc}
     */
    public function getAddress(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAddress', []);

        return parent::getAddress();
    }

    /**
     * {@inheritDoc}
     */
    public function getAddress2(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAddress2', []);

        return parent::getAddress2();
    }

    /**
     * {@inheritDoc}
     */
    public function getCity(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCity', []);

        return parent::getCity();
    }

    /**
     * {@inheritDoc}
     */
    public function getState(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getState', []);

        return parent::getState();
    }

    /**
     * {@inheritDoc}
     */
    public function getZipCode(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getZipCode', []);

        return parent::getZipCode();
    }

    /**
     * {@inheritDoc}
     */
    public function getCountry(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCountry', []);

        return parent::getCountry();
    }

    /**
     * {@inheritDoc}
     */
    public function getBirthdate(): ?\DateTime
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getBirthdate', []);

        return parent::getBirthdate();
    }

    /**
     * {@inheritDoc}
     */
    public function getBirthplaceCity(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getBirthplaceCity', []);

        return parent::getBirthplaceCity();
    }

    /**
     * {@inheritDoc}
     */
    public function getBirthplaceCountry(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getBirthplaceCountry', []);

        return parent::getBirthplaceCountry();
    }

    /**
     * {@inheritDoc}
     */
    public function getOwnershipPercentage(): ?float
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOwnershipPercentage', []);

        return parent::getOwnershipPercentage();
    }

    /**
     * {@inheritDoc}
     */
    public function getType(): ?\Wizacha\Marketplace\CompanyPerson\CompanyPersonType
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getType', []);

        return parent::getType();
    }

    /**
     * {@inheritDoc}
     */
    public function getNationalities(): ?\Doctrine\Common\Collections\Collection
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNationalities', []);

        return parent::getNationalities();
    }

    /**
     * {@inheritDoc}
     */
    public function exposeNationalities(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'exposeNationalities', []);

        return parent::exposeNationalities();
    }

    /**
     * {@inheritDoc}
     */
    public function getCreatedAt(): ?\DateTime
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCreatedAt', []);

        return parent::getCreatedAt();
    }

    /**
     * {@inheritDoc}
     */
    public function expose(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'expose', []);

        return parent::expose();
    }

}
