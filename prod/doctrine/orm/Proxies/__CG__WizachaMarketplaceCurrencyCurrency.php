<?php

namespace Proxies\__CG__\Wizacha\Marketplace\Currency;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Currency extends \Wizacha\Marketplace\Currency\Currency implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\Currency\\Currency' . "\0" . 'code', '' . "\0" . 'Wizacha\\Marketplace\\Currency\\Currency' . "\0" . 'exchangeRate', '' . "\0" . 'Wizacha\\Marketplace\\Currency\\Currency' . "\0" . 'enabled', '' . "\0" . 'Wizacha\\Marketplace\\Currency\\Currency' . "\0" . 'symbol', '' . "\0" . 'Wizacha\\Marketplace\\Currency\\Currency' . "\0" . 'countryCodes', '' . "\0" . 'Wizacha\\Marketplace\\Currency\\Currency' . "\0" . 'updatedAt'];
        }

        return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\Currency\\Currency' . "\0" . 'code', '' . "\0" . 'Wizacha\\Marketplace\\Currency\\Currency' . "\0" . 'exchangeRate', '' . "\0" . 'Wizacha\\Marketplace\\Currency\\Currency' . "\0" . 'enabled', '' . "\0" . 'Wizacha\\Marketplace\\Currency\\Currency' . "\0" . 'symbol', '' . "\0" . 'Wizacha\\Marketplace\\Currency\\Currency' . "\0" . 'countryCodes', '' . "\0" . 'Wizacha\\Marketplace\\Currency\\Currency' . "\0" . 'updatedAt'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (Currency $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getCode(): string
    {
        if ($this->__isInitialized__ === false) {
            return  parent::getCode();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCode', []);

        return parent::getCode();
    }

    /**
     * {@inheritDoc}
     */
    public function getExchangeRate(): ?\Wizacha\Marketplace\Currency\ExchangeRate
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getExchangeRate', []);

        return parent::getExchangeRate();
    }

    /**
     * {@inheritDoc}
     */
    public function getExchangeRateFloatValue(): ?float
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getExchangeRateFloatValue', []);

        return parent::getExchangeRateFloatValue();
    }

    /**
     * {@inheritDoc}
     */
    public function isEnabled(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isEnabled', []);

        return parent::isEnabled();
    }

    /**
     * {@inheritDoc}
     */
    public function getSymbol(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSymbol', []);

        return parent::getSymbol();
    }

    /**
     * {@inheritDoc}
     */
    public function getCountryCodes(): \Doctrine\Common\Collections\Collection
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCountryCodes', []);

        return parent::getCountryCodes();
    }

    /**
     * {@inheritDoc}
     */
    public function setSymbol(?string $symbol): \Wizacha\Marketplace\Currency\Currency
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSymbol', [$symbol]);

        return parent::setSymbol($symbol);
    }

    /**
     * {@inheritDoc}
     */
    public function setExchangeRate(?\Wizacha\Marketplace\Currency\ExchangeRate $exchangeRate): \Wizacha\Marketplace\Currency\Currency
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setExchangeRate', [$exchangeRate]);

        return parent::setExchangeRate($exchangeRate);
    }

    /**
     * {@inheritDoc}
     */
    public function setEnabled(bool $enabled): \Wizacha\Marketplace\Currency\Currency
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setEnabled', [$enabled]);

        return parent::setEnabled($enabled);
    }

    /**
     * {@inheritDoc}
     */
    public function setCountryCodes(iterable $countryCodes): \Wizacha\Marketplace\Currency\Currency
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCountryCodes', [$countryCodes]);

        return parent::setCountryCodes($countryCodes);
    }

    /**
     * {@inheritDoc}
     */
    public function addCountryCode(\Wizacha\Marketplace\Currency\CurrencyCountries $countryCode): \Wizacha\Marketplace\Currency\Currency
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addCountryCode', [$countryCode]);

        return parent::addCountryCode($countryCode);
    }

    /**
     * {@inheritDoc}
     */
    public function removeCountryCode(\Wizacha\Marketplace\Currency\CurrencyCountries $countryCode): \Wizacha\Marketplace\Currency\Currency
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'removeCountryCode', [$countryCode]);

        return parent::removeCountryCode($countryCode);
    }

    /**
     * {@inheritDoc}
     */
    public function clearCountryCodes(): \Wizacha\Marketplace\Currency\Currency
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'clearCountryCodes', []);

        return parent::clearCountryCodes();
    }

    /**
     * {@inheritDoc}
     */
    public function expose(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'expose', []);

        return parent::expose();
    }

    /**
     * {@inheritDoc}
     */
    public function fromArray(array $values): \Wizacha\Marketplace\Currency\Currency
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'fromArray', [$values]);

        return parent::fromArray($values);
    }

    /**
     * {@inheritDoc}
     */
    public function setUpdatedAt(?\DateTime $updatedAt): \Wizacha\Marketplace\Currency\Currency
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setUpdatedAt', [$updatedAt]);

        return parent::setUpdatedAt($updatedAt);
    }

    /**
     * {@inheritDoc}
     */
    public function getUpdatedAt(): ?\DateTime
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUpdatedAt', []);

        return parent::getUpdatedAt();
    }

}
