<?php

namespace Proxies\__CG__\Wizacha\Marketplace\Payment;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class UserPaymentInfo extends \Wizacha\Marketplace\Payment\UserPaymentInfo implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserPaymentInfo' . "\0" . 'userId', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserPaymentInfo' . "\0" . 'mangopayUserId', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserPaymentInfo' . "\0" . 'stripeBankAccountToken', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserPaymentInfo' . "\0" . 'stripeCustomerId', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserPaymentInfo' . "\0" . 'hipaySepaAgreement', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserPaymentInfo' . "\0" . 'lemonwaySepaAgreement', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserPaymentInfo' . "\0" . 'lemonwayElectronicSignature'];
        }

        return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserPaymentInfo' . "\0" . 'userId', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserPaymentInfo' . "\0" . 'mangopayUserId', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserPaymentInfo' . "\0" . 'stripeBankAccountToken', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserPaymentInfo' . "\0" . 'stripeCustomerId', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserPaymentInfo' . "\0" . 'hipaySepaAgreement', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserPaymentInfo' . "\0" . 'lemonwaySepaAgreement', '' . "\0" . 'Wizacha\\Marketplace\\Payment\\UserPaymentInfo' . "\0" . 'lemonwayElectronicSignature'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (UserPaymentInfo $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getUserId(): int
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getUserId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUserId', []);

        return parent::getUserId();
    }

    /**
     * {@inheritDoc}
     */
    public function getMangopayUserId(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getMangopayUserId', []);

        return parent::getMangopayUserId();
    }

    /**
     * {@inheritDoc}
     */
    public function setMangopayUserId(string $mangopayUserId = NULL): \Wizacha\Marketplace\Payment\UserPaymentInfo
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setMangopayUserId', [$mangopayUserId]);

        return parent::setMangopayUserId($mangopayUserId);
    }

    /**
     * {@inheritDoc}
     */
    public function getStripeBankAccountToken(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getStripeBankAccountToken', []);

        return parent::getStripeBankAccountToken();
    }

    /**
     * {@inheritDoc}
     */
    public function setStripeBankAccountToken(?string $stripeBankAccountToken): \Wizacha\Marketplace\Payment\UserPaymentInfo
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setStripeBankAccountToken', [$stripeBankAccountToken]);

        return parent::setStripeBankAccountToken($stripeBankAccountToken);
    }

    /**
     * {@inheritDoc}
     */
    public function getStripeCustomerId(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getStripeCustomerId', []);

        return parent::getStripeCustomerId();
    }

    /**
     * {@inheritDoc}
     */
    public function setStripeCustomerId(string $stripeCustomerId): \Wizacha\Marketplace\Payment\UserPaymentInfo
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setStripeCustomerId', [$stripeCustomerId]);

        return parent::setStripeCustomerId($stripeCustomerId);
    }

    /**
     * {@inheritDoc}
     */
    public function getHipaySepaAgreement(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getHipaySepaAgreement', []);

        return parent::getHipaySepaAgreement();
    }

    /**
     * {@inheritDoc}
     */
    public function setHipaySepaAgreement(int $hipaySepaAgreement): \Wizacha\Marketplace\Payment\UserPaymentInfo
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setHipaySepaAgreement', [$hipaySepaAgreement]);

        return parent::setHipaySepaAgreement($hipaySepaAgreement);
    }

    /**
     * {@inheritDoc}
     */
    public function getLemonwaySepaAgreement(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLemonwaySepaAgreement', []);

        return parent::getLemonwaySepaAgreement();
    }

    /**
     * {@inheritDoc}
     */
    public function setLemonwaySepaAgreement(int $lemonwaySepaAgreement): \Wizacha\Marketplace\Payment\UserPaymentInfo
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setLemonwaySepaAgreement', [$lemonwaySepaAgreement]);

        return parent::setLemonwaySepaAgreement($lemonwaySepaAgreement);
    }

    /**
     * {@inheritDoc}
     */
    public function getLemonwayElectronicSignature(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLemonwayElectronicSignature', []);

        return parent::getLemonwayElectronicSignature();
    }

    /**
     * {@inheritDoc}
     */
    public function setLemonwayElectronicSignature(string $lemonwayElectronicSignature): \Wizacha\Marketplace\Payment\UserPaymentInfo
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setLemonwayElectronicSignature', [$lemonwayElectronicSignature]);

        return parent::setLemonwayElectronicSignature($lemonwayElectronicSignature);
    }

}
