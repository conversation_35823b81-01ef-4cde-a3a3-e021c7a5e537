<?php

namespace Proxies\__CG__\Wizacha\Marketplace\Order\AmountsCalculator\Entity;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class OrderAmounts extends \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'orderId', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'amountItem', 'shippingAmount', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'subTotalExclTaxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'subTotalInclTaxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'totalDiscount', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'totalExclTaxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'totalInclTaxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'totalGreenTaxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'taxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'totalTaxAmount', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'marketplaceTaxRate', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'commissionTaxRate', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'totalMarketplaceDiscount', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'customerTotal', 'promotions', 'orderAmountsCommission', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'commissionExclTaxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'commissionInclTaxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'commissionTaxAmount', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'vendorShareExclTaxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'vendorShareInclTaxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'vendorShareTaxAmount'];
        }

        return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'orderId', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'amountItem', 'shippingAmount', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'subTotalExclTaxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'subTotalInclTaxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'totalDiscount', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'totalExclTaxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'totalInclTaxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'totalGreenTaxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'taxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'totalTaxAmount', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'marketplaceTaxRate', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'commissionTaxRate', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'totalMarketplaceDiscount', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'customerTotal', 'promotions', 'orderAmountsCommission', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'commissionExclTaxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'commissionInclTaxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'commissionTaxAmount', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'vendorShareExclTaxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'vendorShareInclTaxes', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderAmounts' . "\0" . 'vendorShareTaxAmount'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (OrderAmounts $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function hydrateShippingFromOrder(\Wizacha\Marketplace\Order\Order $order): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hydrateShippingFromOrder', [$order]);

        return parent::hydrateShippingFromOrder($order);
    }

    /**
     * {@inheritDoc}
     */
    public function getOrderId(): int
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getOrderId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOrderId', []);

        return parent::getOrderId();
    }

    /**
     * {@inheritDoc}
     */
    public function setOrderId(int $orderId): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setOrderId', [$orderId]);

        return parent::setOrderId($orderId);
    }

    /**
     * {@inheritDoc}
     */
    public function getAmountItems(): \Doctrine\Common\Collections\Collection
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAmountItems', []);

        return parent::getAmountItems();
    }

    /**
     * {@inheritDoc}
     */
    public function getAmountItem(string $itemId): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAmountItem', [$itemId]);

        return parent::getAmountItem($itemId);
    }

    /**
     * {@inheritDoc}
     */
    public function addAmountItem(\Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem $amountItem): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addAmountItem', [$amountItem]);

        return parent::addAmountItem($amountItem);
    }

    /**
     * {@inheritDoc}
     */
    public function getSubTotalExclTaxes(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSubTotalExclTaxes', []);

        return parent::getSubTotalExclTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setSubTotalExclTaxes(\Wizacha\Money\Money $subTotalExclTaxes): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSubTotalExclTaxes', [$subTotalExclTaxes]);

        return parent::setSubTotalExclTaxes($subTotalExclTaxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getSubTotalInclTaxes(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSubTotalInclTaxes', []);

        return parent::getSubTotalInclTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setSubTotalInclTaxes(\Wizacha\Money\Money $subTotalInclTaxes): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSubTotalInclTaxes', [$subTotalInclTaxes]);

        return parent::setSubTotalInclTaxes($subTotalInclTaxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getTotalDiscount(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTotalDiscount', []);

        return parent::getTotalDiscount();
    }

    /**
     * {@inheritDoc}
     */
    public function setTotalDiscount(\Wizacha\Money\Money $totalDiscount): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTotalDiscount', [$totalDiscount]);

        return parent::setTotalDiscount($totalDiscount);
    }

    /**
     * {@inheritDoc}
     */
    public function getTotalExclTaxes(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTotalExclTaxes', []);

        return parent::getTotalExclTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setTotalExclTaxes(\Wizacha\Money\Money $totalExclTaxes): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTotalExclTaxes', [$totalExclTaxes]);

        return parent::setTotalExclTaxes($totalExclTaxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getTotalInclTaxes(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTotalInclTaxes', []);

        return parent::getTotalInclTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setTotalInclTaxes(\Wizacha\Money\Money $totalInclTaxes): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTotalInclTaxes', [$totalInclTaxes]);

        return parent::setTotalInclTaxes($totalInclTaxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getTotalGreenTaxes(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTotalGreenTaxes', []);

        return parent::getTotalGreenTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setTotalGreenTaxes(\Wizacha\Money\Money $greenTaxes): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTotalGreenTaxes', [$greenTaxes]);

        return parent::setTotalGreenTaxes($greenTaxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getTaxes(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTaxes', []);

        return parent::getTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setTaxes(array $taxes): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTaxes', [$taxes]);

        return parent::setTaxes($taxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getTotalTaxAmount(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTotalTaxAmount', []);

        return parent::getTotalTaxAmount();
    }

    /**
     * {@inheritDoc}
     */
    public function setTotalTaxAmount(\Wizacha\Money\Money $totalTaxAmount): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTotalTaxAmount', [$totalTaxAmount]);

        return parent::setTotalTaxAmount($totalTaxAmount);
    }

    /**
     * {@inheritDoc}
     */
    public function getMarketplaceTaxRate(): float
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getMarketplaceTaxRate', []);

        return parent::getMarketplaceTaxRate();
    }

    /**
     * {@inheritDoc}
     */
    public function setMarketplaceTaxRate(float $marketplaceTaxRate): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setMarketplaceTaxRate', [$marketplaceTaxRate]);

        return parent::setMarketplaceTaxRate($marketplaceTaxRate);
    }

    /**
     * {@inheritDoc}
     */
    public function getCommissionTaxRate(): float
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCommissionTaxRate', []);

        return parent::getCommissionTaxRate();
    }

    /**
     * {@inheritDoc}
     */
    public function setCommissionTaxRate(float $commissionTaxRate): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCommissionTaxRate', [$commissionTaxRate]);

        return parent::setCommissionTaxRate($commissionTaxRate);
    }

    /**
     * {@inheritDoc}
     */
    public function getTotalMarketplaceDiscount(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTotalMarketplaceDiscount', []);

        return parent::getTotalMarketplaceDiscount();
    }

    /**
     * {@inheritDoc}
     */
    public function setTotalMarketplaceDiscount(\Wizacha\Money\Money $totalMarketplaceDiscount): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTotalMarketplaceDiscount', [$totalMarketplaceDiscount]);

        return parent::setTotalMarketplaceDiscount($totalMarketplaceDiscount);
    }

    /**
     * {@inheritDoc}
     */
    public function getCustomerTotal(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCustomerTotal', []);

        return parent::getCustomerTotal();
    }

    /**
     * {@inheritDoc}
     */
    public function setCustomerTotal(\Wizacha\Money\Money $customerTotal): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCustomerTotal', [$customerTotal]);

        return parent::setCustomerTotal($customerTotal);
    }

    /**
     * {@inheritDoc}
     */
    public function getShippingAmount(): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\ShippingAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getShippingAmount', []);

        return parent::getShippingAmount();
    }

    /**
     * {@inheritDoc}
     */
    public function setShippingAmount(\Wizacha\Marketplace\Order\AmountsCalculator\Entity\ShippingAmounts $shippingAmount): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setShippingAmount', [$shippingAmount]);

        return parent::setShippingAmount($shippingAmount);
    }

    /**
     * {@inheritDoc}
     */
    public function getCommissionExclTaxes(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCommissionExclTaxes', []);

        return parent::getCommissionExclTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setCommissionExclTaxes(\Wizacha\Money\Money $commissionExclTaxes): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCommissionExclTaxes', [$commissionExclTaxes]);

        return parent::setCommissionExclTaxes($commissionExclTaxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getCommissionInclTaxes(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCommissionInclTaxes', []);

        return parent::getCommissionInclTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setCommissionInclTaxes(\Wizacha\Money\Money $commissionInclTaxes): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCommissionInclTaxes', [$commissionInclTaxes]);

        return parent::setCommissionInclTaxes($commissionInclTaxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getCommissionTaxAmount(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCommissionTaxAmount', []);

        return parent::getCommissionTaxAmount();
    }

    /**
     * {@inheritDoc}
     */
    public function setCommissionTaxAmount(\Wizacha\Money\Money $commissionTaxAmount): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCommissionTaxAmount', [$commissionTaxAmount]);

        return parent::setCommissionTaxAmount($commissionTaxAmount);
    }

    /**
     * {@inheritDoc}
     */
    public function getVendorShareExclTaxes(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getVendorShareExclTaxes', []);

        return parent::getVendorShareExclTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setVendorShareExclTaxes(\Wizacha\Money\Money $vendorShareExclTaxes): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setVendorShareExclTaxes', [$vendorShareExclTaxes]);

        return parent::setVendorShareExclTaxes($vendorShareExclTaxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getVendorShareInclTaxes(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getVendorShareInclTaxes', []);

        return parent::getVendorShareInclTaxes();
    }

    /**
     * {@inheritDoc}
     */
    public function setVendorShareInclTaxes(\Wizacha\Money\Money $vendorShareInclTaxes): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setVendorShareInclTaxes', [$vendorShareInclTaxes]);

        return parent::setVendorShareInclTaxes($vendorShareInclTaxes);
    }

    /**
     * {@inheritDoc}
     */
    public function getVendorShareTaxAmount(): \Wizacha\Money\Money
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getVendorShareTaxAmount', []);

        return parent::getVendorShareTaxAmount();
    }

    /**
     * {@inheritDoc}
     */
    public function setVendorShareTaxAmount(\Wizacha\Money\Money $vendorShareTaxAmount): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setVendorShareTaxAmount', [$vendorShareTaxAmount]);

        return parent::setVendorShareTaxAmount($vendorShareTaxAmount);
    }

    /**
     * {@inheritDoc}
     */
    public function getPromotions(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPromotions', []);

        return parent::getPromotions();
    }

    /**
     * {@inheritDoc}
     */
    public function setPromotions(array $promotions): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPromotions', [$promotions]);

        return parent::setPromotions($promotions);
    }

    /**
     * {@inheritDoc}
     */
    public function getOrderAmountsCommission(): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmountsCommission
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOrderAmountsCommission', []);

        return parent::getOrderAmountsCommission();
    }

    /**
     * {@inheritDoc}
     */
    public function setOrderAmountsCommission(\Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmountsCommission $orderAmountsCommission): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setOrderAmountsCommission', [$orderAmountsCommission]);

        return parent::setOrderAmountsCommission($orderAmountsCommission);
    }

}
