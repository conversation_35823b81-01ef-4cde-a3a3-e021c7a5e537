<?php

namespace Proxies\__CG__\Wizacha\Marketplace\Division;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Division extends \Wizacha\Marketplace\Division\Division implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\Division\\Division' . "\0" . 'code', '' . "\0" . 'Wizacha\\Marketplace\\Division\\Division' . "\0" . 'alpha3', '' . "\0" . 'Wizacha\\Marketplace\\Division\\Division' . "\0" . 'level', '' . "\0" . 'Wizacha\\Marketplace\\Division\\Division' . "\0" . 'lft', '' . "\0" . 'Wizacha\\Marketplace\\Division\\Division' . "\0" . 'rgt', '' . "\0" . 'Wizacha\\Marketplace\\Division\\Division' . "\0" . 'isEnabled', '' . "\0" . 'Wizacha\\Marketplace\\Division\\Division' . "\0" . 'updatedAt', '' . "\0" . 'Wizacha\\Marketplace\\Division\\Division' . "\0" . 'updatedBy', '' . "\0" . 'Wizacha\\Marketplace\\Division\\Division' . "\0" . 'descriptions', '' . "\0" . 'Wizacha\\Marketplace\\Division\\Division' . "\0" . 'root', '' . "\0" . 'Wizacha\\Marketplace\\Division\\Division' . "\0" . 'parent', '' . "\0" . 'Wizacha\\Marketplace\\Division\\Division' . "\0" . 'children'];
        }

        return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\Division\\Division' . "\0" . 'code', '' . "\0" . 'Wizacha\\Marketplace\\Division\\Division' . "\0" . 'alpha3', '' . "\0" . 'Wizacha\\Marketplace\\Division\\Division' . "\0" . 'level', '' . "\0" . 'Wizacha\\Marketplace\\Division\\Division' . "\0" . 'lft', '' . "\0" . 'Wizacha\\Marketplace\\Division\\Division' . "\0" . 'rgt', '' . "\0" . 'Wizacha\\Marketplace\\Division\\Division' . "\0" . 'isEnabled', '' . "\0" . 'Wizacha\\Marketplace\\Division\\Division' . "\0" . 'updatedAt', '' . "\0" . 'Wizacha\\Marketplace\\Division\\Division' . "\0" . 'updatedBy', '' . "\0" . 'Wizacha\\Marketplace\\Division\\Division' . "\0" . 'descriptions', '' . "\0" . 'Wizacha\\Marketplace\\Division\\Division' . "\0" . 'root', '' . "\0" . 'Wizacha\\Marketplace\\Division\\Division' . "\0" . 'parent', '' . "\0" . 'Wizacha\\Marketplace\\Division\\Division' . "\0" . 'children'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (Division $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function addDescription(\Wizacha\Marketplace\Division\DivisionDescription $description): \Wizacha\Marketplace\Division\Division
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addDescription', [$description]);

        return parent::addDescription($description);
    }

    /**
     * {@inheritDoc}
     */
    public function getDescription(string $askedLanguage = NULL): ?\Wizacha\Marketplace\Division\DivisionDescription
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDescription', [$askedLanguage]);

        return parent::getDescription($askedLanguage);
    }

    /**
     * {@inheritDoc}
     */
    public function setDescriptions(iterable $descriptions): \Wizacha\Marketplace\Division\Division
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDescriptions', [$descriptions]);

        return parent::setDescriptions($descriptions);
    }

    /**
     * {@inheritDoc}
     */
    public function getDescriptions(): \Doctrine\Common\Collections\Collection
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDescriptions', []);

        return parent::getDescriptions();
    }

    /**
     * {@inheritDoc}
     */
    public function getCode(): string
    {
        if ($this->__isInitialized__ === false) {
            return  parent::getCode();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCode', []);

        return parent::getCode();
    }

    /**
     * {@inheritDoc}
     */
    public function setCode(string $code): \Wizacha\Marketplace\Division\Division
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCode', [$code]);

        return parent::setCode($code);
    }

    /**
     * {@inheritDoc}
     */
    public function getAlpha3(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAlpha3', []);

        return parent::getAlpha3();
    }

    /**
     * {@inheritDoc}
     */
    public function setAlpha3(string $alpha3): \Wizacha\Marketplace\Division\Division
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAlpha3', [$alpha3]);

        return parent::setAlpha3($alpha3);
    }

    /**
     * {@inheritDoc}
     */
    public function getParentCode(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getParentCode', []);

        return parent::getParentCode();
    }

    /**
     * {@inheritDoc}
     */
    public function getLevel(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLevel', []);

        return parent::getLevel();
    }

    /**
     * {@inheritDoc}
     */
    public function setLevel(int $level): \Wizacha\Marketplace\Division\Division
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setLevel', [$level]);

        return parent::setLevel($level);
    }

    /**
     * {@inheritDoc}
     */
    public function isEnabled(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isEnabled', []);

        return parent::isEnabled();
    }

    /**
     * {@inheritDoc}
     */
    public function setIsEnabled(bool $isEnabled): \Wizacha\Marketplace\Division\Division
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIsEnabled', [$isEnabled]);

        return parent::setIsEnabled($isEnabled);
    }

    /**
     * {@inheritDoc}
     */
    public function getUpdatedAt(): \DateTime
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUpdatedAt', []);

        return parent::getUpdatedAt();
    }

    /**
     * {@inheritDoc}
     */
    public function setUpdatedAt(\DateTime $updatedAt): \Wizacha\Marketplace\Division\Division
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setUpdatedAt', [$updatedAt]);

        return parent::setUpdatedAt($updatedAt);
    }

    /**
     * {@inheritDoc}
     */
    public function getUpdatedBy(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUpdatedBy', []);

        return parent::getUpdatedBy();
    }

    /**
     * {@inheritDoc}
     */
    public function setUpdatedBy(int $updatedBy): \Wizacha\Marketplace\Division\Division
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setUpdatedBy', [$updatedBy]);

        return parent::setUpdatedBy($updatedBy);
    }

    /**
     * {@inheritDoc}
     */
    public function getParent(): ?\Wizacha\Marketplace\Division\Division
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getParent', []);

        return parent::getParent();
    }

    /**
     * {@inheritDoc}
     */
    public function setParent(?\Wizacha\Marketplace\Division\Division $parent): \Wizacha\Marketplace\Division\Division
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setParent', [$parent]);

        return parent::setParent($parent);
    }

    /**
     * {@inheritDoc}
     */
    public function getChildren(): \Doctrine\Common\Collections\Collection
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getChildren', []);

        return parent::getChildren();
    }

    /**
     * {@inheritDoc}
     */
    public function setChildren(\Doctrine\Common\Collections\Collection $children): \Wizacha\Marketplace\Division\Division
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setChildren', [$children]);

        return parent::setChildren($children);
    }

    /**
     * {@inheritDoc}
     */
    public function addChild(\Wizacha\Marketplace\Division\Division $child): \Wizacha\Marketplace\Division\Division
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addChild', [$child]);

        return parent::addChild($child);
    }

    /**
     * {@inheritDoc}
     */
    public function removeChild(\Wizacha\Marketplace\Division\Division $child): \Wizacha\Marketplace\Division\Division
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'removeChild', [$child]);

        return parent::removeChild($child);
    }

    /**
     * {@inheritDoc}
     */
    public function removeDescription(\Wizacha\Marketplace\Division\DivisionDescription $description): \Wizacha\Marketplace\Division\Division
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'removeDescription', [$description]);

        return parent::removeDescription($description);
    }

    /**
     * {@inheritDoc}
     */
    public function clearDescriptions(): \Wizacha\Marketplace\Division\Division
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'clearDescriptions', []);

        return parent::clearDescriptions();
    }

    /**
     * {@inheritDoc}
     */
    public function getLeft(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLeft', []);

        return parent::getLeft();
    }

    /**
     * {@inheritDoc}
     */
    public function getRight(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRight', []);

        return parent::getRight();
    }

}
