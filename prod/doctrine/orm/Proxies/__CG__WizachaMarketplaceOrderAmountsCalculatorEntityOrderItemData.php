<?php

namespace Proxies\__CG__\Wizacha\Marketplace\Order\AmountsCalculator\Entity;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class OrderItemData extends \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderItemData implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderItemData' . "\0" . 'id', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderItemData' . "\0" . 'productId', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderItemData' . "\0" . 'fullCategoryPath', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderItemData' . "\0" . 'declinationId', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderItemData' . "\0" . 'productName', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderItemData' . "\0" . 'productCode', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderItemData' . "\0" . 'taxIncludedInPrice', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderItemData' . "\0" . 'maxPriceAdjustment', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderItemData' . "\0" . 'itemId', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderItemData' . "\0" . 'taxData', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderItemData' . "\0" . 'rawPrice'];
        }

        return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderItemData' . "\0" . 'id', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderItemData' . "\0" . 'productId', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderItemData' . "\0" . 'fullCategoryPath', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderItemData' . "\0" . 'declinationId', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderItemData' . "\0" . 'productName', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderItemData' . "\0" . 'productCode', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderItemData' . "\0" . 'taxIncludedInPrice', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderItemData' . "\0" . 'maxPriceAdjustment', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderItemData' . "\0" . 'itemId', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderItemData' . "\0" . 'taxData', '' . "\0" . 'Wizacha\\Marketplace\\Order\\AmountsCalculator\\Entity\\OrderItemData' . "\0" . 'rawPrice'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (OrderItemData $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function hydrateAmountItemDataFromOrder(\Wizacha\Marketplace\Order\OrderItem $orderItem): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderItemData
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hydrateAmountItemDataFromOrder', [$orderItem]);

        return parent::hydrateAmountItemDataFromOrder($orderItem);
    }

    /**
     * {@inheritDoc}
     */
    public function getId(): int
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function getProductId(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProductId', []);

        return parent::getProductId();
    }

    /**
     * {@inheritDoc}
     */
    public function setProductId(int $productId): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderItemData
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setProductId', [$productId]);

        return parent::setProductId($productId);
    }

    /**
     * {@inheritDoc}
     */
    public function getFullCategoryPath(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getFullCategoryPath', []);

        return parent::getFullCategoryPath();
    }

    /**
     * {@inheritDoc}
     */
    public function setFullCategoryPath(?string $fullCategoryPath): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderItemData
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setFullCategoryPath', [$fullCategoryPath]);

        return parent::setFullCategoryPath($fullCategoryPath);
    }

    /**
     * {@inheritDoc}
     */
    public function getDeclinationId(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDeclinationId', []);

        return parent::getDeclinationId();
    }

    /**
     * {@inheritDoc}
     */
    public function setDeclinationId(string $declinationId): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderItemData
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDeclinationId', [$declinationId]);

        return parent::setDeclinationId($declinationId);
    }

    /**
     * {@inheritDoc}
     */
    public function getProductName(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProductName', []);

        return parent::getProductName();
    }

    /**
     * {@inheritDoc}
     */
    public function setProductName(string $productName): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderItemData
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setProductName', [$productName]);

        return parent::setProductName($productName);
    }

    /**
     * {@inheritDoc}
     */
    public function getProductCode(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProductCode', []);

        return parent::getProductCode();
    }

    /**
     * {@inheritDoc}
     */
    public function setProductCode(string $productCode): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderItemData
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setProductCode', [$productCode]);

        return parent::setProductCode($productCode);
    }

    /**
     * {@inheritDoc}
     */
    public function getTaxIncludedInPrice(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTaxIncludedInPrice', []);

        return parent::getTaxIncludedInPrice();
    }

    /**
     * {@inheritDoc}
     */
    public function setTaxIncludedInPrice(bool $taxIncludedInPrice): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderItemData
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTaxIncludedInPrice', [$taxIncludedInPrice]);

        return parent::setTaxIncludedInPrice($taxIncludedInPrice);
    }

    /**
     * {@inheritDoc}
     */
    public function getMaxPriceAdjustment(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getMaxPriceAdjustment', []);

        return parent::getMaxPriceAdjustment();
    }

    /**
     * {@inheritDoc}
     */
    public function setMaxPriceAdjustment(?int $maxPriceAdjustment): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderItemData
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setMaxPriceAdjustment', [$maxPriceAdjustment]);

        return parent::setMaxPriceAdjustment($maxPriceAdjustment);
    }

    /**
     * {@inheritDoc}
     */
    public function getItemId(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getItemId', []);

        return parent::getItemId();
    }

    /**
     * {@inheritDoc}
     */
    public function setItemId(string $itemId): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderItemData
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setItemId', [$itemId]);

        return parent::setItemId($itemId);
    }

    /**
     * {@inheritDoc}
     */
    public function getTaxData(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTaxData', []);

        return parent::getTaxData();
    }

    /**
     * {@inheritDoc}
     */
    public function setTaxData(array $taxData): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderItemData
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTaxData', [$taxData]);

        return parent::setTaxData($taxData);
    }

    /**
     * {@inheritDoc}
     */
    public function getRawPrice(): float
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRawPrice', []);

        return parent::getRawPrice();
    }

    /**
     * {@inheritDoc}
     */
    public function setRawPrice(float $rawPrice): \Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderItemData
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setRawPrice', [$rawPrice]);

        return parent::setRawPrice($rawPrice);
    }

}
