<?php

namespace Proxies\__CG__\Wizacha\Marketplace\PIM\MultiVendorProduct;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class MultiVendorProduct extends \Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', 'code', 'supplierReference', 'status', 'imageIds', 'category', 'links', 'translations', 'slug', 'productTemplateType', 'video', 'createdAt', 'updatedAt', 'id'];
        }

        return ['__isInitialized__', 'code', 'supplierReference', 'status', 'imageIds', 'category', 'links', 'translations', 'slug', 'productTemplateType', 'video', 'createdAt', 'updatedAt', 'id'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (MultiVendorProduct $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getCode(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCode', []);

        return parent::getCode();
    }

    /**
     * {@inheritDoc}
     */
    public function getSupplierReference(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSupplierReference', []);

        return parent::getSupplierReference();
    }

    /**
     * {@inheritDoc}
     */
    public function getName(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getName', []);

        return parent::getName();
    }

    /**
     * {@inheritDoc}
     */
    public function getSlug(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSlug', []);

        return parent::getSlug();
    }

    /**
     * {@inheritDoc}
     */
    public function getShortDescription(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getShortDescription', []);

        return parent::getShortDescription();
    }

    /**
     * {@inheritDoc}
     */
    public function getDescription(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDescription', []);

        return parent::getDescription();
    }

    /**
     * {@inheritDoc}
     */
    public function getSeoTitle(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSeoTitle', []);

        return parent::getSeoTitle();
    }

    /**
     * {@inheritDoc}
     */
    public function getSeoDescription(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSeoDescription', []);

        return parent::getSeoDescription();
    }

    /**
     * {@inheritDoc}
     */
    public function getSeoKeywords(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSeoKeywords', []);

        return parent::getSeoKeywords();
    }

    /**
     * {@inheritDoc}
     */
    public function getStatus(): \Wizacha\Marketplace\PIM\MultiVendorProduct\Status\Status
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getStatus', []);

        return parent::getStatus();
    }

    /**
     * {@inheritDoc}
     */
    public function canHaveAProductPage(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'canHaveAProductPage', []);

        return parent::canHaveAProductPage();
    }

    /**
     * {@inheritDoc}
     */
    public function isSearchable(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isSearchable', []);

        return parent::isSearchable();
    }

    /**
     * {@inheritDoc}
     */
    public function getProductTemplateType(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProductTemplateType', []);

        return parent::getProductTemplateType();
    }

    /**
     * {@inheritDoc}
     */
    public function getVideo(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getVideo', []);

        return parent::getVideo();
    }

    /**
     * {@inheritDoc}
     */
    public function getImageIds(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getImageIds', []);

        return parent::getImageIds();
    }

    /**
     * {@inheritDoc}
     */
    public function addImageId(int $imageId): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addImageId', [$imageId]);

        return parent::addImageId($imageId);
    }

    /**
     * {@inheritDoc}
     */
    public function removeImageId(int $imageId): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'removeImageId', [$imageId]);

        return parent::removeImageId($imageId);
    }

    /**
     * {@inheritDoc}
     */
    public function getCategory(): \Wizacha\Marketplace\PIM\Category\Category
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCategory', []);

        return parent::getCategory();
    }

    /**
     * {@inheritDoc}
     */
    public function getLinks(): \Doctrine\Common\Collections\Collection
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLinks', []);

        return parent::getLinks();
    }

    /**
     * {@inheritDoc}
     */
    public function addLink(\Wizacha\Marketplace\PIM\MultiVendorProduct\Link $link): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addLink', [$link]);

        return parent::addLink($link);
    }

    /**
     * {@inheritDoc}
     */
    public function removeLink(\Wizacha\Marketplace\PIM\MultiVendorProduct\Link $link): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'removeLink', [$link]);

        return parent::removeLink($link);
    }

    /**
     * {@inheritDoc}
     */
    public function getProducts(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProducts', []);

        return parent::getProducts();
    }

    /**
     * {@inheritDoc}
     */
    public function getProductIds(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProductIds', []);

        return parent::getProductIds();
    }

    /**
     * {@inheritDoc}
     */
    public function loadData(array $data, \Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct $existingResource = NULL): \Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'loadData', [$data, $existingResource]);

        return parent::loadData($data, $existingResource);
    }

    /**
     * {@inheritDoc}
     */
    public function loadDataFromExistingResource(array $data, \Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct $existingResource): \Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'loadDataFromExistingResource', [$data, $existingResource]);

        return parent::loadDataFromExistingResource($data, $existingResource);
    }

    /**
     * {@inheritDoc}
     */
    public function mergeData(array $data): \Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'mergeData', [$data]);

        return parent::mergeData($data);
    }

    /**
     * {@inheritDoc}
     */
    public function expose(\Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProductService $multiVendorProductService): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'expose', [$multiVendorProductService]);

        return parent::expose($multiVendorProductService);
    }

    /**
     * {@inheritDoc}
     */
    public function exposeTranslatedData(\Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProductService $multiVendorProductService): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'exposeTranslatedData', [$multiVendorProductService]);

        return parent::exposeTranslatedData($multiVendorProductService);
    }

    /**
     * {@inheritDoc}
     */
    public function getSeoData(): \Wizacha\Marketplace\SeoData
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSeoData', []);

        return parent::getSeoData();
    }

    /**
     * {@inheritDoc}
     */
    public function loadSlug()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'loadSlug', []);

        return parent::loadSlug();
    }

    /**
     * {@inheritDoc}
     */
    public function getImagesData(\Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProductService $mvpService): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getImagesData', [$mvpService]);

        return parent::getImagesData($mvpService);
    }

    /**
     * {@inheritDoc}
     */
    public function getCreatedAt(): \DateTimeImmutable
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCreatedAt', []);

        return parent::getCreatedAt();
    }

    /**
     * {@inheritDoc}
     */
    public function setCreatedAt(\DateTime $createdAt): \Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCreatedAt', [$createdAt]);

        return parent::setCreatedAt($createdAt);
    }

    /**
     * {@inheritDoc}
     */
    public function getUpdatedAt(): \DateTimeImmutable
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getUpdatedAt', []);

        return parent::getUpdatedAt();
    }

    /**
     * {@inheritDoc}
     */
    public function updateTimestamp()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'updateTimestamp', []);

        return parent::updateTimestamp();
    }

    /**
     * {@inheritDoc}
     */
    public function getId(): ?string
    {
        if ($this->__isInitialized__ === false) {
            return  parent::getId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function defineGuid(): \Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'defineGuid', []);

        return parent::defineGuid();
    }

}
