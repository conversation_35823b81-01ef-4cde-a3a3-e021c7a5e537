<?php

namespace Proxies\__CG__\Wizacha\Marketplace\PIM\MultiVendorProduct\ProductSynchronization;

/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Rules extends \Wizacha\Marketplace\PIM\MultiVendorProduct\ProductSynchronization\Rules implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\PIM\\MultiVendorProduct\\ProductSynchronization\\Rules' . "\0" . 'companyId', '' . "\0" . 'Wizacha\\Marketplace\\PIM\\MultiVendorProduct\\ProductSynchronization\\Rules' . "\0" . 'includedCategories', '' . "\0" . 'Wizacha\\Marketplace\\PIM\\MultiVendorProduct\\ProductSynchronization\\Rules' . "\0" . 'excludedCategories', '' . "\0" . 'Wizacha\\Marketplace\\PIM\\MultiVendorProduct\\ProductSynchronization\\Rules' . "\0" . 'includedBrands', '' . "\0" . 'Wizacha\\Marketplace\\PIM\\MultiVendorProduct\\ProductSynchronization\\Rules' . "\0" . 'excludedBrands', '' . "\0" . 'Wizacha\\Marketplace\\PIM\\MultiVendorProduct\\ProductSynchronization\\Rules' . "\0" . 'includedProducts', '' . "\0" . 'Wizacha\\Marketplace\\PIM\\MultiVendorProduct\\ProductSynchronization\\Rules' . "\0" . 'excludedProducts'];
        }

        return ['__isInitialized__', '' . "\0" . 'Wizacha\\Marketplace\\PIM\\MultiVendorProduct\\ProductSynchronization\\Rules' . "\0" . 'companyId', '' . "\0" . 'Wizacha\\Marketplace\\PIM\\MultiVendorProduct\\ProductSynchronization\\Rules' . "\0" . 'includedCategories', '' . "\0" . 'Wizacha\\Marketplace\\PIM\\MultiVendorProduct\\ProductSynchronization\\Rules' . "\0" . 'excludedCategories', '' . "\0" . 'Wizacha\\Marketplace\\PIM\\MultiVendorProduct\\ProductSynchronization\\Rules' . "\0" . 'includedBrands', '' . "\0" . 'Wizacha\\Marketplace\\PIM\\MultiVendorProduct\\ProductSynchronization\\Rules' . "\0" . 'excludedBrands', '' . "\0" . 'Wizacha\\Marketplace\\PIM\\MultiVendorProduct\\ProductSynchronization\\Rules' . "\0" . 'includedProducts', '' . "\0" . 'Wizacha\\Marketplace\\PIM\\MultiVendorProduct\\ProductSynchronization\\Rules' . "\0" . 'excludedProducts'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (Rules $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load()
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized()
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized)
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null)
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer()
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null)
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner()
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties()
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getCompanyId(): int
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getCompanyId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCompanyId', []);

        return parent::getCompanyId();
    }

    /**
     * {@inheritDoc}
     */
    public function getIncludedCategories(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getIncludedCategories', []);

        return parent::getIncludedCategories();
    }

    /**
     * {@inheritDoc}
     */
    public function setIncludedCategories(array $includedCategories): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIncludedCategories', [$includedCategories]);

        parent::setIncludedCategories($includedCategories);
    }

    /**
     * {@inheritDoc}
     */
    public function getExcludedCategories(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getExcludedCategories', []);

        return parent::getExcludedCategories();
    }

    /**
     * {@inheritDoc}
     */
    public function setExcludedCategories(array $excludedCategories): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setExcludedCategories', [$excludedCategories]);

        parent::setExcludedCategories($excludedCategories);
    }

    /**
     * {@inheritDoc}
     */
    public function getIncludedBrands(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getIncludedBrands', []);

        return parent::getIncludedBrands();
    }

    /**
     * {@inheritDoc}
     */
    public function setIncludedBrands(array $includedBrands): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIncludedBrands', [$includedBrands]);

        parent::setIncludedBrands($includedBrands);
    }

    /**
     * {@inheritDoc}
     */
    public function getExcludedBrands(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getExcludedBrands', []);

        return parent::getExcludedBrands();
    }

    /**
     * {@inheritDoc}
     */
    public function setExcludedBrands(array $excludedBrands): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setExcludedBrands', [$excludedBrands]);

        parent::setExcludedBrands($excludedBrands);
    }

    /**
     * {@inheritDoc}
     */
    public function getIncludedProducts(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getIncludedProducts', []);

        return parent::getIncludedProducts();
    }

    /**
     * {@inheritDoc}
     */
    public function setIncludedProducts(array $includedProducts): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIncludedProducts', [$includedProducts]);

        parent::setIncludedProducts($includedProducts);
    }

    /**
     * {@inheritDoc}
     */
    public function getExcludedProducts(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getExcludedProducts', []);

        return parent::getExcludedProducts();
    }

    /**
     * {@inheritDoc}
     */
    public function setExcludedProducts(array $excludedProducts): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setExcludedProducts', [$excludedProducts]);

        parent::setExcludedProducts($excludedProducts);
    }

}
