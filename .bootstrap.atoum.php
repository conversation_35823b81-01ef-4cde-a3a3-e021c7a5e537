<?php
define('AREA', 'A');
define('APP_ENV', 'test');
define('DEVELOPMENT', false);
define('NO_SESSION', true);
require __DIR__ . '/init.php';
require __DIR__ . '/vendor/atoum/atoum/scripts/runner.php';
require __DIR__ . '/tests/atoum/Tygh/Backend/Cache/ABackend.php';
//Helpers
include(__DIR__ . '/tests/atoum/Helpers/Client.php');
include(__DIR__ . '/tests/atoum/Helpers/RepositoryTest.php');
/**
 * Set a null handler in Monolog to avoid tests fails when errors are logged in stderr
 */
$logger = container()->get('logger');
$logger->pushHandler(new \Monolog\Handler\NullHandler());
