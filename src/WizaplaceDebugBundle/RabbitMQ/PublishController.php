<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\WizaplaceDebugBundle\RabbitMQ;

use SecurityException;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;
use Wizacha\Marketplace\Messenger\BroadcastPublisher;

class PublishController extends AbstractController
{
    public const EVENT_NAME = 'debug.message';

    private BroadcastPublisher $publisher;

    private SerializerInterface $serializer;

    private string $env;

    public function __construct(BroadcastPublisher $publisher, SerializerInterface $serializer, string $env)
    {
        $this->publisher = $publisher;
        $this->serializer = $serializer;
        $this->env = $env;
    }

    public function publish(Request $request): Response
    {
        if ('prod' === $this->env) {
            throw new SecurityException('This route must be executed on dev or test environment only.');
        }

        $envelope = $this->publisher->publish(
            static::EVENT_NAME,
            $request->request->all()['payload'] ?? [],
            $request->request->all()['context'] ?? []
        );

        return new Response(
            $this->serializer->serialize(
                $envelope->getMessage(),
                'json'
            ),
            Response::HTTP_OK,
            ['Content-Type' => 'application/json']
        );
    }
}
