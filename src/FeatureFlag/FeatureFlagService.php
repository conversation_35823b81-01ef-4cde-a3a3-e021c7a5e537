<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\FeatureFlag;

use Symfony\Component\DependencyInjection\Exception\ParameterNotFoundException;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class FeatureFlagService
{
    public ParameterBagInterface $containerParams;

    public function __construct(ParameterBagInterface $containerParams)
    {
        $this->containerParams = $containerParams;
    }

    /**
     * Return the state of all feature flags
     * @return array<string, bool>
     */
    public function all(): array
    {
        $features = [];

        foreach ($this->containerParams->all() as $paramName => $paramValue) {
            if ($this->isFeatureFlagName($paramName)) {
                $features[$paramName] = $paramValue;
            }
        }

        return $features;
    }

    /**
     * Return true if a feature flag is active
     *
     * Return false if feature flag is inactive
     * or feature flag value is not a boolean
     * or provided name is not a valid feature flag name
     */
    public function get(string $name): bool
    {
        return $this->containerParams->get($name) === true
            && $this->isFeatureFlagName($name);
    }

    /** Return true if a feature flag is defined */
    public function has(string $name): bool
    {
        return $this->containerParams->has($name)
            && $this->isFeatureFlagName($name);
    }

    private function isFeatureFlagName(string $name): bool
    {
        return \strpos($name, "feature.") === 0;
    }
}
