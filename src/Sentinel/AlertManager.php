<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Sentinel;

use Psr\Log\LoggerInterface;

class AlertManager
{
    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var string
     */
    private $projectName;

    /**
     * @var string
     */
    private $logLevel;

    public function __construct(LoggerInterface $logger, string $logLevel, string $projectName)
    {
        $this->logger = $logger;
        $this->logLevel = $logLevel;
        $this->projectName = $projectName;
    }

    /**
     * Logs a message with a context in the 'sentinel' channel
     * The context contains a hash unique by message, context and project
     * Log level is determined by monolog.log_level.sentinel
     * @TODO Log level can be overwritten by topic: monolog.log_level.sentinel.{topic}
     *
     * @param string $message
     * @param array $context
     * @param string|null $topic
     */
    public function send(string $message, array $context, string $topic = null): void
    {
        $data = array_merge($context, [
            'message' => $message,
            'project_name' => $this->projectName,
        ]);

        $context['hash'] = md5(serialize($data));

        $this->logger->log($this->logLevel, \is_null($topic) ? $message : "[$topic] $message", $context);
    }
}
