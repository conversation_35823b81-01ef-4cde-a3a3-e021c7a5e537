<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\C2C;

use Wizacha\Marketplace\Order\Event\OrderCodeGenerated;
use Wizacha\Marketplace\Order\OrderDataType;

class Order
{
    public const KEY_CODE_IN_ORDER = 'code';

    public static function setCode($order, $additionnal_data)
    {
        if (!empty($additionnal_data[OrderDataType::KEY_CODE_INFO()->getValue()])) {
            $order[self::KEY_CODE_IN_ORDER] = unserialize(
                $additionnal_data[OrderDataType::KEY_CODE_INFO()->getValue()]
            );
        }
        return $order;
    }

    public static function saveCode($order)
    {
        if (!empty($order[self::KEY_CODE_IN_ORDER])) {
            $data = [
                'order_id' => $order['order_id'],
                'type'     => OrderDataType::KEY_CODE_INFO()->getValue(),
                'data'     => serialize($order[self::KEY_CODE_IN_ORDER]),
            ];
            \Tygh\Database::query("REPLACE INTO ?:order_data ?e", $data);
        }
    }

    public static function onUpdateStatus($order, $status, \Wizacha\Registry $registry)
    {
        $shipping_id = null;
        if ($order['shipping_ids']) {
            $shipping_id = $order['shipping_ids'];
        } elseif (\is_array($order['shipping'])) {
            $shipping_id = reset($order['shipping'])['shipping_id'];
        }

        if ($status == \Wizacha\OrderStatus::PROCESSING_SHIPPING
            && empty($order[self::KEY_CODE_IN_ORDER])
            && $order['is_parent_order'] != 'Y'
            && \Wizacha\Shipping::requiredCode($shipping_id)
        ) {
            $order[self::KEY_CODE_IN_ORDER] = new \Wizacha\C2C\Code();
            static::saveCode($order);

            $container = container();
            $orderObj = $container->get('marketplace.order.order_service')->getOrder($order['order_id']);

            // send code to customer
            $container
                ->get('event_dispatcher')
                ->dispatch(
                    new OrderCodeGenerated(
                        $orderObj,
                        $order[self::KEY_CODE_IN_ORDER]
                    ),
                    OrderCodeGenerated::class
                )
            ;
        }
        return $order;
    }
}
