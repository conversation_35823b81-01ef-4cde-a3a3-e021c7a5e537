<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\C2C;

class Code implements \Serializable
{
    public const NUMBER_ELEMENTS_IN_CODE = 9;

    public const MIN_RANDOM_NUMBER       = 1000;

    public const MAX_RANDOM_NUMBER       = 9999;

    public const MAX_LOGGED_ATTEMPT      = 3;

    /**
     * @var array [string1, string2, ...]
     */
    protected $code = null;

    /**
     * @var null|integer
     */
    protected $hidden_key = null;

    /**
     * @var array
     */
    protected $logs = [];

    public function __construct()
    {
        $this->code = [];
        for ($i = 0; $i < self::NUMBER_ELEMENTS_IN_CODE; $i++) {
            do {
                $code = mt_rand(self::MIN_RANDOM_NUMBER, self::MAX_RANDOM_NUMBER);
            } while (\in_array($code, $this->code));

            $this->code[] = $code;
        }

        $this->hidden_key = mt_rand(0, self::NUMBER_ELEMENTS_IN_CODE - 1);
    }

    /**
     * @return string
     */
    public function serialize()
    {
        return serialize([
            'code'  => $this->code,
            'logs'  => $this->logs,
            'hidden_key' => $this->hidden_key,
        ]);
    }

    /**
     * @param string $string
     */
    public function unserialize($string)
    {
        $data = unserialize($string);
        $this->code       = $data['code'];
        $this->hidden_key = $data['hidden_key'];
        $this->logs       = $data['logs'];
    }

    /**
     * @return array|null
     */
    public function full(): ?array
    {
        return $this->code;
    }

    /**
     * @return array|null
     */
    public function fullDebug(): ?array
    {
        if (empty($this->code) || \is_null($this->hidden_key)) {
            return null;
        }

        $codes = [];
        foreach ($this->full() as $key => $value) {
            $codes[] = [
                'value' => $value,
                'is_secret' => $key === $this->hidden_key,
            ];
        }

        return $codes;
    }


    /**
     * Hidden key is set to empty string
     * @return array|null
     */
    public function partial(): ?array
    {
        if (empty($this->code) || \is_null($this->hidden_key)) {
            return null;
        }

        $partial_code = $this->full();
        $partial_code[$this->hidden_key] = '';

        return $partial_code;
    }

    public function validateWithLog(string $try_string): bool
    {
        if (\count($this->logs) >= self::MAX_LOGGED_ATTEMPT) {
            return false;
        }
        $this->logs[] = $try_string;

        return $this->validate($try_string);
    }

    public function validate(string $try_string): bool
    {
        return (
            isset($this->hidden_key, $this->code)
            && $this->code[$this->hidden_key] == $try_string
        );
    }

    public function logs(): array
    {
        return $this->logs;
    }

    /**
     * @return null|string
     */
    public function expectedCode(): ?string
    {
        if (empty($this->code) || \is_null($this->hidden_key)) {
            return null;
        }

        return $this->code[$this->hidden_key];
    }
}
