<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Cache;

class RedisDecorator
{
    /**
     * @var \Redis
     */
    private $redis;

    public function __construct(\Redis $redis)
    {
        $this->redis = $redis;
    }

    public function setex($key, $ttl, $value)
    {
        return $this->redis->setex($key, $ttl, $value);
    }

    public function set($key, $value, $timeout = null)
    {
        return $this->redis->set($key, $value, $timeout);
    }

    public function del($key1, ...$otherKeys)
    {
        return $this->redis->del($key1, $otherKeys);
    }

    /**
     * Delete specific keys from given pattern and prefix
     * We scan with prefix but remove prefix in result
     * We delete keys without prefix because del function already add prefix itself
     */
    public function delFromPattern(string $pattern, string $prefix): int
    {
        $iterator = null;
        $deleted = 0;
        while ($iterator !== 0) {
            $deleted += $this->redis->del(
                array_map(
                    static function ($key) use ($prefix) {
                        return str_replace($prefix, '', $key);
                    },
                    $this->redis->scan($iterator, $prefix . $pattern)
                )
            );
        }

        return $deleted;
    }

    public function keys($pattern)
    {
        return $this->redis->keys($pattern);
    }

    public function mget(array $array)
    {
        return $this->redis->mget($array);
    }

    /**
     * Get a cache value, optionally setting it if it is not set
     * @param string $key Key to fetch from the cache
     * @param callable|null $setter A callable to set the value if it is not in the cache, called with the key as parameter
     * @param int|null $timeout Optional timeout in seconds
     * @return false|mixed|string Value fetched from the cache or from the optional setter
     * @see https://symfony.com/doc/current/components/cache.html#cache-contracts
     */
    public function get(string $key, callable $setter = null, int $timeout = null)
    {
        $value = $this->redis->get($key);

        if (false === $value && \is_callable($setter)) {
            $value = \call_user_func($setter, $key);
            $this->set($key, $value, $timeout);
        }

        return $value;
    }

    public function setOption($option, $value)
    {
        return $this->redis->setOption($option, $value);
    }

    public function getOption($option)
    {
        return $this->redis->getOption($option);
    }
}
