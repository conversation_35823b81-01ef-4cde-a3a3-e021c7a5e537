<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Janus;

use Doctrine\DBAL\Connection;
use Wizacha\User;

class JanusRepository
{
    private Connection $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
    }

    /** @return string[] */
    public function getAllTableNames(): array
    {
        $getStatement = $this->connection->prepare("SHOW TABLES");

        $getStatement->execute();

        $tableNames = [];

        while ($row = $getStatement->fetch()) {
            $tableNames[] = \array_values($row)[0];
        }

        return $tableNames;
    }

    public function getAdminUserIds(): string
    {
        $getStatement = $this->connection->prepare(
            "SELECT
                user_id
            FROM
                cscart_users
            WHERE
                user_type = :user_type
                AND company_id = :company_id
            "
        );
        $getStatement->execute(
            [
                'user_type' => User::ADMIN_TYPE,
                'company_id' => 0
            ]
        );

        $userIds = [];

        while ($row = $getStatement->fetch()) {
            $userIds[] = $row['user_id'];
        }

        return implode(', ', $userIds);
    }

    public function getVendorUserIds(): string
    {
        $getStatement = $this->connection->prepare(
            "SELECT
                user_id
            FROM
                cscart_users
            WHERE
                user_type = :user_type
            "
        );
        $getStatement->execute(
            [
                'user_type' => User::VENDOR_TYPE,
            ]
        );

        $userIds = [];

        while ($row = $getStatement->fetch()) {
            $userIds[] = $row['user_id'];
        }

        return implode(', ', $userIds);
    }

    public function getProductOptionIds(): string
    {
        $getStatement = $this->connection->prepare(
            "SELECT
                option_id
            FROM
                cscart_product_options
            WHERE
                product_id = 0
                AND company_id = 0
            "
        );

        $getStatement->execute();

        $productOptionIds = [];

        while ($row = $getStatement->fetch()) {
            $productOptionIds[] = $row['option_id'];
        }

        return implode(', ', $productOptionIds);
    }

    public function getProductOptionVariantIds(string $productOptionIds): string
    {
        $getStatement = $this->connection->prepare(
            "SELECT
                variant_id
            FROM
                cscart_product_option_variants
            WHERE
                option_id IN ($productOptionIds);
            "
        );

        $getStatement->execute();

        $productOptionVariantIds = [];

        while ($row = $getStatement->fetch()) {
            $productOptionVariantIds[] = $row['variant_id'];
        }

        return implode(', ', $productOptionVariantIds);
    }

    /** @param string[] $tableNames */
    public function drop(array $tableNames): void
    {
        $this->connection->executeQuery('SET foreign_key_checks = 0');

        foreach ($tableNames as $tableName) {
            $dropStatement = $this->connection->prepare(
                "DROP
                    TABLE
                IF EXISTS $tableName
                "
            );
            $dropStatement->execute();
        }

        $this->connection->executeQuery('SET foreign_key_checks = 1');
    }
}
