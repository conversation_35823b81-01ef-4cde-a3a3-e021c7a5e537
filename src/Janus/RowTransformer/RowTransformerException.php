<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Janus\RowTransformer;

class RowTransformerException extends \Exception
{
    public array $context;

    public function __construct(string $message, array $context)
    {
        $this->context = $context;

        parent::__construct($message);
    }

    public function getContext(): array
    {
        return $this->context;
    }
}
