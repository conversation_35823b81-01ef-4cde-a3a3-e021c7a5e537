<?php

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Janus\RowTransformer;

class AnonymizerCscartOrdersTransformer extends AbstractReplacerTransformer
{
    private const PREFIX = 'anon_';

    protected function getTargetTableName(): string
    {
        return 'cscart_orders';
    }

    protected function getReplaceMap(array $row): array
    {
        $userId = $row['user_id'];
        $companyId = $row['company_id'];
        $data = [
            'firstname' => static::PREFIX . 'firstname_' . $userId,
            'lastname' => static::PREFIX . 'lastname_' . $userId,
            'company' => static::PREFIX . 'company_' . $companyId,
            'b_company' => static::PREFIX . 'b_company_' . $userId,
            'b_firstname' => static::PREFIX . 'b_firstname_' . $userId,
            'b_lastname' => static::PREFIX . 'b_lastname_' . $userId,
            'b_address' => static::PREFIX . 'b_address_' . $userId,
            'b_address_2' => static::PREFIX . 'b_address_2' . $userId,
            'b_phone' => '***********.05.06',
            's_firstname' => static::PREFIX . 's_firstname_' . $userId,
            's_lastname' => static::PREFIX . 's_lastname_' . $userId,
            's_company' => static::PREFIX . 's_company_' . $userId,
            's_address' => static::PREFIX . 's_address_' . $userId,
            's_address_2' => static::PREFIX . 's_address_2_' . $userId,
            's_phone' => '***********.05.06',
            'email' => static::PREFIX . 'email_user_' . $userId . '@wizacha.com',
            'ip_address' => '*******.',
        ];

        return $data;
    }
}
