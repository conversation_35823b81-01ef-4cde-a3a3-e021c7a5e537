<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Janus\RowTransformer;

class AnonymizerCscartUsersTransformer extends AbstractReplacerTransformer
{
    private const PREFIX = 'anon_';

    protected function getTargetTableName(): string
    {
        return 'cscart_users';
    }

    protected function getReplaceMap(array $row): array
    {
        $userId = $row['user_id'];
        $companyId = $row['company_id'];

        $data = [
            'firstname' => static::PREFIX . 'firstname_' . $userId,
            'lastname' => static::PREFIX . 'lastname_ ' . $userId,
            'email' => static::PREFIX . 'email_user_' . $userId . '@wizacha.com',
            'company' => static::PREFIX . 'company_' . $companyId,
            'phone' => '01.02.03.04.05.06',
            'fax' => '01.02.03.04.05.06',
            'url' => 'http://' . static::PREFIX . 'urls',
            'user_login' => static::PREFIX . 'user_login_' . $userId,
        ];

        return $data;
    }
}
