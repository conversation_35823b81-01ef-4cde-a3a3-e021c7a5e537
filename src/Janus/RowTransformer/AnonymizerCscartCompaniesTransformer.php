<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Janus\RowTransformer;

class AnonymizerCscartCompaniesTransformer extends AbstractReplacerTransformer
{
    private const PREFIX = 'anon_';

    protected function getTargetTableName(): string
    {
        return 'cscart_companies';
    }

    protected function getReplaceMap(array $row): array
    {
        $companyId = $row['company_id'];
        $data = [
            'company' => static::PREFIX . 'company_' . $companyId,
            'corporate_name' => static::PREFIX . 'corporate_name_' . $companyId,
            'address' => static::PREFIX . 'address_' . $companyId,
            'email' => static::PREFIX . 'email_company_' . $companyId . '@wizacha.com',
            'phone' => '***********.05.06',
            'url' => 'http://' . static::PREFIX . 'url',
            'request_account_data' => static::PREFIX . 'request_account_data',
            'w_rma_address' => static::PREFIX . 'w_rma_address_' . $companyId,
            'w_vat_number' => '',
            'w_siret_number' => '**************',
            'latitude' => 0,
            'longitude' => 0,
            'legal_representative_firstname' => static::PREFIX . 'legal_representative_firstname_' . $companyId,
            'legal_representative_lastname' => static::PREFIX . 'legal_representative_lastname_' . $companyId,
            'iban' => 'FR14 2004 1010 0505 0001 3M02 606',
            'bic' => 'ABCOFRPP',
            'extra' => \serialize([]),
            'naf_code' => '0121Z',
        ];

        return $data;
    }
}
