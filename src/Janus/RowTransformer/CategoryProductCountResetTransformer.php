<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Janus\RowTransformer;

class CategoryProductCountResetTransformer extends AbstractReplacerTransformer
{
    protected function getTargetTableName(): string
    {
        return 'cscart_categories';
    }

    protected function getReplaceMap(array $row): array
    {
        return [
            'product_count' => 0,
            'visible_product_count' => 0,
        ];
    }
}
