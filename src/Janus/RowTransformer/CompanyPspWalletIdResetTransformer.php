<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Janus\RowTransformer;

class CompanyPspWalletIdResetTransformer extends AbstractReplacerTransformer
{
    protected function getTargetTableName(): string
    {
        return 'cscart_companies';
    }

    protected function getReplaceMap(array $row): array
    {
        return [
            'mangopay_id' => null,
            'stripe_id' => null,
            'hipay_id' => null,
        ];
    }
}
