<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Janus\RowTransformer;

/**
 * replace row column in $targetTableName with $value
 */
abstract class AbstractReplacerTransformer implements RowTransformerInterface
{
    abstract protected function getTargetTableName(): string;

    abstract protected function getReplaceMap(array $row): array;

    public function transform(
        string $tableName,
        array $row
    ): array {
        if ($tableName === $this->getTargetTableName()) {
            $transformedRow =  \array_merge(
                $row,
                $this->getReplaceMap($row)
            );

            if (\count($transformedRow) !== \count($row)) {
                throw new InvalidMapException();
            }

            return $transformedRow;
        }

        return $row;
    }
}
