<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Janus\RowTransformer;

class AnonymizerDoctrineOrganisationsTransformer extends AbstractReplacerTransformer
{
    private const PREFIX = 'anon_';

    protected function getTargetTableName(): string
    {
        return 'doctrine_organisation';
    }

    protected function getReplaceMap(array $row): array
    {
        $id = $row['id'];
        $data = [
            'name' => static::PREFIX . 'name_' . $id,
            'address' => static::PREFIX . 'address_' . $id,
            'additional_address' => static::PREFIX . 'additional_address' . $id,
            'shipping_address_address' => static::PREFIX . 'shipping_address_address_' . $id,
            'shipping_address_additional_address' => static::PREFIX . 'shipping_address_additional_address_' . $id,
            'legal_information_siret' => '75236018000034',
            'legal_information_vat_number' => '',
            'legal_information_business_name' => static::PREFIX . 'legal_information_business_name_' . $id,
            'business_unit_code' => $row['id'],
            'business_unit_name' => $row['id'],
        ];

        return $data;
    }
}
