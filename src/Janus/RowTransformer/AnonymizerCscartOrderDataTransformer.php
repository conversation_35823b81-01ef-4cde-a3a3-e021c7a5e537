<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Janus\RowTransformer;

use Wizacha\Core\ErrorProofUnserializer\ErrorProofUnserializer;
use Wizacha\Core\ErrorProofUnserializer\SerializedStringExceptionInterface;
use Wizacha\Marketplace\Order\OrderDataType;

class AnonymizerCscartOrderDataTransformer extends AbstractReplacerTransformer
{
    private const PREFIX = 'anon_';

    protected function getTargetTableName(): string
    {
        return 'cscart_order_data';
    }

    protected function getReplaceMap(array $row): array
    {
        $orderDataType = new OrderDataType($row['type']);

        if (\in_array($orderDataType, [OrderDataType::GROUP_INFO(), OrderDataType::SHIPPING_INFO()])) {
            $unserializer = new ErrorProofUnserializer($row['data']);

            try {
                $decoded = $unserializer->unserialize();
            } catch (SerializedStringExceptionInterface $exception) {
                throw new InvalidSerializedOrderDataException(
                    [
                        'message' => $exception->getMessage(),
                        'order_id' => $row['order_id'],
                    ]
                );
            }

            $anonymized = $this->normalizeOrderType(
                $decoded,
                (int) $row['order_id'],
                $orderDataType
            );

            return [
                'data' => \serialize($anonymized)
            ];
        }

        return [];
    }

    /** from legacy anonymizer */
    private function anonymisationOfWDescriptions(string $w_description, int $id): string
    {
        $newLine = '';
        $w_description = explode("\r\n", $w_description);
        foreach ($w_description as &$dataLine) {
            if (substr_count($dataLine, '<p>Tel :')) {
                $dataLine = \sprintf('<p>Tel : %s</p>', '***********.05.06');
            }
            if (substr_count($dataLine, '<p>Email :')) {
                $dataLine = '<p>Email : clientTest' . $id . '@yopmail.fr</p>';
            }
            if (substr_count($dataLine, '<p>Adresse :')) {
                $dataLine = '<p>Adresse : 42 Avenue du randomTest2 <br /> 6900Lyon<br />France</p>';
            }
        }
        unset($dataLine);

        // We re assemble the array.
        $x = 0;
        while (\array_key_exists($x, $w_description)) {
            $newLine .= $w_description[$x];

            if (\array_key_exists($x, $w_description)) {
                $newLine .= PHP_EOL;
            }

            ++$x;
        }

        return $newLine;
    }

    /** from legacy anonymizer */
    private function normalizeOrderType(array $orderDataToAnonymize, int $id, OrderDataType $type): array
    {
        foreach ($orderDataToAnonymize as &$soloOrderData) {
            // Shared by OrderDataType::SHIPPING_INFO and OrderDataType::GROUP_INFO.

            // Note: is_string($soloOrderData['w_description']): The pharmedistore project has NULLs instead of ""
            if (\array_key_exists('w_description', $soloOrderData ?? []) && \is_string($soloOrderData['w_description'])) {
                $soloOrderData['w_description'] = $this->anonymisationOfWDescriptions($soloOrderData['w_description'], $id);
            }

            if (OrderDataType::GROUP_INFO()->equals($type)) {
                foreach ($orderDataToAnonymize as &$order) {
                    foreach ($order['shippings'] as &$shipping) {
                        $shipping['w_description'] = $this->anonymisationOfWDescriptions($shipping['w_description'] ?? '', $id);
                    }
                    unset($shipping);

                    $order['name'] = static::PREFIX . 'name';
                    $order['origination']['name'] = static::PREFIX . 'name';
                    $order['origination']['address'] = static::PREFIX . 'address';
                    $order['origination']['phone'] = '***********.05.06';
                    $order['origination']['fax'] = '***********.05.06';

                    foreach ($order['package_info']['packages'] as &$package) {
                        foreach ($package['products'] as &$product) {
                            foreach ($product['shippings'] as &$shipping) {
                                $shipping['w_description'] = $this->anonymisationOfWDescriptions(
                                    $shipping['w_description'] ?? '',
                                    $id
                                );
                            }
                            unset($shipping);
                        }
                        unset($product);
                    }
                    unset($package);

                    $order['package_info']['location']['firstname'] = static::PREFIX . 'firstname';
                    $order['package_info']['location']['lastname'] =  static::PREFIX . 'lastname';
                    ;
                    $order['package_info']['location']['company'] = static::PREFIX . 'company';
                    $order['package_info']['location']['address'] = static::PREFIX . 'address';
                    $order['package_info']['location']['address_2'] = static::PREFIX . 'address_2';
                    $order['package_info']['location']['phone'] = '***********.05.06';


                    $order['package_info']['origination']['name'] = static::PREFIX . 'name';
                    $order['package_info']['origination']['address'] = static::PREFIX . 'address';
                    $order['package_info']['origination']['phone'] = '***********.05.06';
                    $order['package_info']['origination']['fax'] = '***********.05.06';

                    if (\array_key_exists('chosen_shippings', $order)) {
                        foreach ($order['chosen_shippings'] as &$chosenShipping) {
                            $chosenShipping['w_description'] = $this->anonymisationOfWDescriptions($chosenShipping['w_description'] ?? '', $id);
                        }
                        unset($chosenShipping);
                    }
                }
                unset($order);
            }
        }
        unset($soloOrderData);

        return $orderDataToAnonymize;
    }
}
