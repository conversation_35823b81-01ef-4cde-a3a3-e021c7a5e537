<?php

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Janus\RowTransformer;

class Anonymizer<PERSON>cartUserProfilesTransformer extends AbstractReplacerTransformer
{
    private const PREFIX = 'anon_';

    protected function getTargetTableName(): string
    {
        return 'cscart_user_profiles';
    }

    protected function getReplaceMap(array $row): array
    {
        $data = [
            'b_firstname' => static::PREFIX . 'b_firstname',
            'b_lastname' => static::PREFIX . 'b_lastname',
            'b_company' => static::PREFIX . 'b_company',
            'b_address' => static::PREFIX . 'b_address',
            'b_address_2' => static::PREFIX . 'b_address_2',
            'b_phone' => '***********.05.06',
            's_firstname' => static::PREFIX . 's_firstname',
            's_lastname' => static::PREFIX . 's_lastname',
            's_company' => static::PREFIX . 's_company',
            's_address' => static::PREFIX . 's_address',
            's_address_2' => static::PREFIX . 's_address_2',
            's_phone' => '***********.05.06',
        ];

        return $data;
    }
}
