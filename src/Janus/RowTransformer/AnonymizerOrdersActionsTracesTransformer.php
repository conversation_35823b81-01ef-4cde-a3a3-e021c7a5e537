<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Janus\RowTransformer;

class AnonymizerOrdersActionsTracesTransformer extends AbstractReplacerTransformer
{
    private const PREFIX = 'anon_';

    protected function getTargetTableName(): string
    {
        return 'orders_actions_traces';
    }

    protected function getReplaceMap(array $row): array
    {
        $data = [
            'user_name' => static::PREFIX . 'user_name_' . $row['user_id']
        ];

        return $data;
    }
}
