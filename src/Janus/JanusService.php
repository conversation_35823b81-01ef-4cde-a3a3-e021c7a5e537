<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Janus;

use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Component\Yaml\Yaml;
use Wizacha\Janus\RowTransformer\RowTransformerInterface;
use Wizacha\Marketplace\Seo\SlugTargetType;

class JanusService
{
    private JanusRepository $janusRepository;
    private ContainerInterface $container;
    /** @var mixed */
    private $tablesConfig;
    private KernelInterface $kernel;

    /** @var RowTransformerInterface[] */
    private array $rowTransformers;

    public function __construct(
        JanusRepository $janusRepository,
        KernelInterface $kernel
    ) {
        $this->janusRepository = $janusRepository;
        $this->kernel = $kernel;

        $this->container = container();
        $this->tablesConfig = Yaml::parseFile($this->kernel->getProjectDir() . '/src/Janus/config/perimeters.yml');
    }

    /** @return string[] */
    public function getNoDataTables(JanusPerimeter $perimeter): array
    {
        return \array_diff(
            $this->janusRepository->getAllTableNames(),
            $this->getTablesHavingData($perimeter)
        );
    }

    /** @return mixed[] */
    public function getTableConditions(JanusPerimeter $perimeter): array
    {
        $tableConditions = $perimeter->getValue() === JanusPerimeter::ADMIN_PERIMETER
            ? [
                'cscart_seo_names' => 'company_id = 0 AND type = "' . SlugTargetType::CATEGORY . '"',
                'cscart_images_links' => 'object_type NOT IN ("product","company")',
            ]
            : []
        ;

        $userIds = $this->getUserIds($perimeter);

        if ($userIds !== "") {
            $tableConditions['cscart_users'] = 'user_id IN (' . $userIds . ')';
            $tableConditions['cscart_user_data'] = 'user_id IN (' . $userIds . ')';
            $tableConditions['cscart_user_profiles'] = 'user_id IN (' . $userIds . ')';
        }

        $productOptionIds = $perimeter->getValue() === JanusPerimeter::ADMIN_PERIMETER
            ? $this->janusRepository->getProductOptionIds()
            : ""
        ;
        $productOptionVariantIds = $productOptionIds === ""
            ? ""
            : $this->janusRepository->getProductOptionVariantIds($productOptionIds)
        ;

        if ($productOptionIds !== "") {
            $tableConditions['cscart_product_options'] = 'option_id IN (' . $productOptionIds . ')';
            $tableConditions['cscart_product_options_descriptions'] = 'option_id IN (' . $productOptionIds . ')';
        }

        if ($productOptionVariantIds !== "") {
            $tableConditions['cscart_product_option_variants'] = 'variant_id IN (' . $productOptionVariantIds . ')';
            $tableConditions['cscart_product_option_variants_descriptions'] = 'variant_id IN (' . $productOptionVariantIds . ')';
        }

        return $tableConditions;
    }

    public function addRowTransformer(RowTransformerInterface $rowTransformer): void
    {
        $this->rowTransformers[] = $rowTransformer;
    }

    public function getTransformTableRowHook(): callable
    {
        return function (string $tableName, array $row) {
            foreach ($this->rowTransformers as $rowTransformer) {
                $row = $rowTransformer->transform($tableName, $row);
            }

            return $row;
        };
    }

    /** @return mixed[] */
    private function getTablesHavingData(JanusPerimeter $perimeter): array
    {
        $tablesHavingData = $this->tablesConfig[JanusPerimeter::ADMIN_PERIMETER]['tables'];

        if ($this->container->getParameter('feature.multi_vendor_product') === true) {
            \array_push(
                $tablesHavingData,
                'doctrine_multi_vendor_product',
                'doctrine_multi_vendor_product_translations'
            );
        }

        if ($this->container->getParameter('feature.enable_divisions') === true) {
            \array_push(
                $tablesHavingData,
                'doctrine_divisions',
                'doctrine_division_descriptions'
            );
        }

        if ($perimeter->getValue() === JanusPerimeter::ADMIN_AND_VENDOR_PERIMETER) {
            $tablesHavingData = array_merge(
                $tablesHavingData,
                $this->tablesConfig[JanusPerimeter::ADMIN_AND_VENDOR_PERIMETER]['tables']
            );

            if ($this->container->getParameter('feature.enable_divisions') === true) {
                \array_push(
                    $tablesHavingData,
                    'doctrine_division_blacklists',
                    'doctrine_division_products'
                );
            }
        }

        return $tablesHavingData;
    }

    public function clearTables(): void
    {
        $allTables = $this->janusRepository->getAllTableNames();

        $this->janusRepository->drop($allTables);
    }

    private function getUserIds(JanusPerimeter $perimeter): string
    {
        $userIds = $this->janusRepository->getAdminUserIds();

        if ($perimeter->getValue() === JanusPerimeter::ADMIN_AND_VENDOR_PERIMETER) {
            $userIds .= $this->janusRepository->getVendorUserIds();
        }

        return $userIds;
    }
}
