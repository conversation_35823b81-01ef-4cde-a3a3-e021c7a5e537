<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha;

use GuzzleHttp\ClientInterface;
use GuzzleHttp\Psr7\Uri;
use Psr\Log\LoggerInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;
use Wizacha\Async\Dispatcher;
use Wizacha\Cscart\Common;

class CloudImageManager
{
    public const WILDCARD = '*';
    public const INVALIDATE_ERROR_MESSAGE = 'CloudImageManager invalidate error';

    protected ?string $apiUrl;
    protected ?string $clientKey;
    protected ?string $versionHead;

    protected ClientInterface $client;
    protected RouterInterface $router;
    protected LoggerInterface $logger;
    protected Dispatcher $asyncDispatcher;

    public function __construct(
        ?string $apiUrl,
        ?string $clientKey,
        ?string $versionHead,
        ClientInterface $client,
        RouterInterface $router,
        LoggerInterface $logger,
        Dispatcher $asyncDispatcher
    ) {
        $this->apiUrl = $apiUrl;
        $this->clientKey = $clientKey;
        $this->versionHead = $versionHead;

        $this->client = $client;
        $this->router = $router;
        $this->logger = $logger;
        $this->asyncDispatcher = $asyncDispatcher;
    }

    public function invalidateCloudImageCache(int $imageId): void
    {
        $isAsync = $this->asyncDispatcher->delayExec(
            self::class . '::' . __FUNCTION__,
            \func_get_args()
        );

        if ($isAsync) {
            return;
        }

        try {
            $this->client->request(
                'POST',
                $this->apiUrl . '/invalidate',
                [
                    'headers' => [
                        'X-Client-Key' => $this->clientKey,
                        'Content-Type' => 'application/json',
                    ],
                    'json' => [
                        'scope' => 'urls',
                        'urls' => [
                            $this->versionHead
                            . $this->getOriginalImageUrl($imageId)
                            . static::WILDCARD
                        ]
                    ]
                ]
            );
        } catch (\Exception $exception) {
            $this->logger->error(
                static::INVALIDATE_ERROR_MESSAGE,
                ['exception' => $exception]
            );
        }
    }

    /**
     * Get Image url without scheme
     */
    protected function getOriginalImageUrl(int $imageId): string
    {
        $uri = Common::normalizeApiUri(
            new Uri(
                $this->router->generate(
                    'api_image_get',
                    ['id' => $imageId],
                    UrlGeneratorInterface::ABSOLUTE_URL
                )
            )
        );

        return \trim(
            (string) $uri->withScheme(''),
            '/'
        );
    }
}
