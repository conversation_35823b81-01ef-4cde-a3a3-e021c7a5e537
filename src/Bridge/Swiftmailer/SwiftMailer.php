<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Bridge\Swiftmailer;

use Wizacha\Async\Dispatcher;

class SwiftMailer implements Mailer
{
    /**
     * @var \Swift_Mailer
     */
    private $mailer;

    /**
     * @var string
     */
    private $kernelProjectDir;

    /**
     * @var Dispatcher
     */
    private $jobDispatcher;

    public function __construct(\Swift_Mailer $mailer, string $kernelProjectDir, Dispatcher $jobDispatcher)
    {
        $this->mailer = $mailer;
        $this->kernelProjectDir = $kernelProjectDir;
        $this->jobDispatcher = $jobDispatcher;
    }

    public function send(\Swift_Mime_SimpleMessage $message)
    {
        if ($this->jobDispatcher->delayExec('app.mailer' . '::' . __FUNCTION__, \func_get_args())) {
            return;
        }

        $transport = $this->mailer->getTransport();
        if (!$transport->ping()) {
            $transport->stop();
            $transport->start();
        }

        $this->mailer->send(
            $this->embedImages($message),
            $failedRecipients
        );
    }

    private function embedImages(\Swift_Mime_SimpleMessage $message): \Swift_Mime_SimpleMessage
    {
        $body = $message->getBody();

        // Get all paths between [imageToEmbed] and [/imageToEmbed] tags
        if (preg_match_all('/\[imageToEmbed\](.*)\[\/imageToEmbed\]/U', $body, $matches)) {
            $imagesUrl = [];
            foreach ($matches[1] as $item) {
                // Remove duplicates
                $imagesUrl[$item] = $item;
            }

            foreach ($imagesUrl as $imageUrl) {
                $cid = $message->embed(\Swift_Image::fromPath($this->kernelProjectDir . parse_url($imageUrl)['path']));

                // Replace current image usage by the cid
                $body = str_replace('[imageToEmbed]' . $imageUrl . '[/imageToEmbed]', $cid, $body);
            }
        }

        $message->setBody($body, 'text/html');

        return $message;
    }
}
