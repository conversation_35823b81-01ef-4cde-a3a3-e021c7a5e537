<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Bridge\Swiftmailer;

/**
 * This mailer implementation is used to preview mail in the back office
 * It doesn't send any mail through SMTP, they are caught by the logger pluggin
 */
class DebugSwiftMailer implements Mailer
{
    /**
     * @var \Swift_Mailer
     */
    private $mailer;

    public function __construct(\Swift_Mailer $mailer)
    {
        $this->mailer = $mailer;
    }

    public function send(\Swift_Mime_SimpleMessage $message)
    {
        $body = $message->getBody();
        $body = str_replace(['[imageToEmbed]', '[/imageToEmbed]'], '', $body);
        $message->setBody($body);

        $this->mailer->send($message);
    }
}
