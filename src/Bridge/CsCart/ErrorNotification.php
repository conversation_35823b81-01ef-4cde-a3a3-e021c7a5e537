<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Bridge\CsCart;

/**
 * ErrorNotification represents a call to `fn_set_notification('E', ...)` as an exception.
 */
class ErrorNotification extends \Exception
{
    /**
     * Calls given function while dealing with error notifications set by CS-Cart
     *
     * @param callable $fn
     * @return mixed $fn's return value
     * @throws ErrorNotification
     */
    public static function catchErrorNotifications(callable $fn)
    {
        $preExistingNotifications = $_SESSION['notifications'] ?? [];
        $_SESSION['notifications'] = [];
        $result = $fn();
        foreach ($_SESSION['notifications'] as $notificationData) {
            if ($notificationData['type'] === 'E') {
                throw new ErrorNotification($notificationData['message']);
            }
        }
        $_SESSION['notifications'] = $preExistingNotifications;

        return $result;
    }
}
