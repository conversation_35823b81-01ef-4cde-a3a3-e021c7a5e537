<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Bridge\CsCart;

/**
 * @deprecated Legacy API
 */
class Api extends \Tygh\Api
{
    /**
     * Returns instance of Entity class by entity properties
     *
     * @param  array             $entity_properties Entity properties data @see Api::getEntityFromPath
     * @return \Tygh\Api\AEntity
     */
    protected function getObjectByEntity($entity_properties)
    {
        $class_name = 'Wizacha\AppBundle\Controller\Api\\' . fn_camelize($entity_properties['name']);
        return class_exists($class_name) ? new $class_name($this->auth) : null;
    }

    public function __construct()
    {
        //Change default content-type
        if (empty($_SERVER['CONTENT_TYPE'])) {
            $_SERVER['CONTENT_TYPE'] = 'application/json';
        }
        parent::__construct($this->allowedFormats());
    }

    protected function allowedFormats()
    {
        return ['json'];
    }
}
