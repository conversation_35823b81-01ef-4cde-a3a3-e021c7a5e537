<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Bridge\CsCart;

use Tygh\Backend\Cache\ABackend;

/**
 * In memory cache backend, working only in current process (tests purpose only)
 */
class MemoryCache extends ABackend
{
    /**
     * @var array Stores all cached datas
     */
    private $_datas = [];
    /**
     * @var array Stores only timeout, i.e keys are a subset of $_datas keys
     */
    private $_timeout = [];

    public function set($name, $data, $timeout = \Wizacha\Config::CACHE_TIMEOUT)
    {
        if ($data) {
            $this->_datas[$name] = $data;
            $this->_timeout[$name] = time() + $timeout;
        }
    }


    public function get($name)
    {
        if ($this->_timeout[$name] < time()) {
            unset($this->_timeout[$name]);
            unset($this->_datas[$name]);
            return false;
        }
        return
            isset($this->_datas[$name]) ?
                [$this->_datas[$name]]
                : false;
    }

    public function clear($tags)
    {
        $this->_datas = array_diff_key($this->_datas, array_flip($tags));
        return true;
    }

    public function cleanup()
    {
        $this->_datas    = [];
        return true;
    }

    public function findKey(string $key): array
    {
        $key = str_replace('*', '.*', $key); // convert wilcard to regex

        $keys = array_keys($this->_datas);
        return array_filter(array_map(function ($arrayKey) use ($key) {
            return preg_match('/^' . $key . '/', $arrayKey) ? $arrayKey : null;
        }, $keys));
    }

    /**
     * @return string[]
     */
    public function keys(): array
    {
        return array_keys($this->_datas);
    }
}
