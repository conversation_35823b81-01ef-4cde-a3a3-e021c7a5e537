<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Bridge\Symfony\Config;

use Symfony\Component\Config\Resource\SelfCheckingResourceInterface;

/**
 * Pour mettre en cache les langues configurées, le composant ConfigCache de
 * symfony a besoin d'une classe pour les Metadata. On sépare le cache par locale
 */
class DatabaseResource implements SelfCheckingResourceInterface
{
    /** @var ?string */
    private $locale;

    public function __construct($locale)
    {
        $this->locale = $locale;
    }

    /**
     * {@inheritdoc}
     */
    public function __toString()
    {
        return $this->locale;
    }

    /**
     * {@inheritdoc}
     */
    public function isFresh($timestamp)
    {
        return true; // Consider a resource comming from the database is always fresh
    }
}
