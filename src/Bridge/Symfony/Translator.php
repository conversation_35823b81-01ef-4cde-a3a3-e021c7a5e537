<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Bridge\Symfony;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\DBALException;
use Symfony\Component\Config\ConfigCache;
use Symfony\Component\Filesystem\Exception\IOException;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\Finder\Finder;
use Symfony\Component\Translation\Translator as SymfonyTranslator;
use Symfony\Component\Translation\TranslatorBagInterface;
use Symfony\Contracts\Translation\LocaleAwareInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Wizacha\Bridge\Symfony\Config\DatabaseResource;

/**
 * Décorateur du Translator de Symfony pour charger des traductions depuis la BDD
 * avec un système de mise en cache.
 */
class Translator implements TranslatorInterface, TranslatorBagInterface, LocaleAwareInterface
{
    /** @var SymfonyTranslator */
    private $translator;

    /** @var Connection */
    private $dbal;

    private $cacheDir;

    /** @var bool */
    private $debug;

    public function __construct(SymfonyTranslator $translator, Connection $dbal, string $cacheDir, bool $debug)
    {
        $this->translator = $translator;
        $this->dbal = $dbal;
        $this->cacheDir = $cacheDir;
        $this->debug = $debug;
        $this->configureLanguages();
    }

    /**
     * Add translator resources for each languages configured in the database
     */
    public function configureLanguages()
    {
        $file = $this->cacheDir . '/database.resources.php';
        $cache = new ConfigCache($file, $this->debug);

        if (!$cache->isFresh()) {
            try {
                $this->dbal->connect();

                // Get active languages
                $languages = $this->dbal->fetchAll("SELECT * FROM cscart_languages");
            } catch (DBALException $ex) {
                // Database not available, do nothing
                return;
            }

            $metadata = [];
            $resources = [];

            foreach ($languages as $language) {
                $resources[$language['lang_code']] = $language;
                $metadata[] = new DatabaseResource($language['lang_code']);
            }

            $content = sprintf("<?php return %s;", var_export($resources, true));
            $cache->write($content, $metadata);
        } else {
            $resources = include $file;
        }

        foreach ($resources as $locale => $language) {
            $this->translator->addResource('database', [], $locale, 'messages');
        }
    }

    /**
     * {@inheritdoc}
     */
    public function trans($id, array $parameters = array(), $domain = null, $locale = null)
    {
        return $this->translator->trans(strtolower($id), $parameters, $domain, $locale);
    }

    /**
     * {@inheritdoc}
     */
    public function transChoice($id, $number, array $parameters = array(), $domain = null, $locale = null)
    {
        return $this->translator->trans(strtolower($id), ['%count%' => $number] + $parameters, $domain, $locale);
    }

    /**
     * {@inheritdoc}
     */
    public function setLocale($locale)
    {
        $this->translator->setLocale($locale);
    }

    /**
     * {@inheritdoc}
     */
    public function getLocale()
    {
        return $this->translator->getLocale();
    }

    /**
     * {@inheritdoc}
     */
    public function getCatalogue($locale = null)
    {
        return $this->translator->getCatalogue($locale);
    }

    /**
     * Passes through all unknown calls onto the translator object.
     */
    public function __call($method, $args)
    {
        return \call_user_func_array(array($this->translator, $method), $args);
    }

    public function removeCatalogueCache(string $locale)
    {
        $finder = new Finder();
        $finder->files()->in($this->cacheDir)->name(sprintf('/catalogue\.%s.*\.php$/', $locale));
        $fs = new Filesystem();
        $filesToRemove = [];

        foreach ($finder as $file) {
            $path = $file->getRealPath();
            $filesToRemove[] = $path;

            $meta = $path . '.meta';

            if ($fs->exists($meta)) {
                $filesToRemove[] = $meta;
            }
        }

        $resource = $this->cacheDir . '/database.resources.php';

        if ($fs->exists($resource)) {
            $filesToRemove[] = $resource;
        }

        $meta = $resource . '.meta';

        if ($fs->exists($meta)) {
            $filesToRemove[] = $meta;
        }

        // invalidate paths in opcache
        array_map('opcache_invalidate', $filesToRemove);

        try {
            $fs->remove($filesToRemove);
        } catch (IOException $ex) {
        }
    }
}
