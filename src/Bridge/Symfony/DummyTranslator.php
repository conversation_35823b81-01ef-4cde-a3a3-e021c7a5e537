<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Bridge\Symfony;

use Symfony\Component\Translation\MessageCatalogue;
use Symfony\Component\Translation\TranslatorBagInterface;
use Symfony\Contracts\Translation\LocaleAwareInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * This translator does not load any translations in the catalogue, it only
 * returns the translation ID concatenated with the parameters if any
 */
class DummyTranslator implements TranslatorInterface, TranslatorBagInterface, LocaleAwareInterface
{
    private $locale;

    /** @var TranslatorInterface */
    private $translator;

    public function __construct(TranslatorInterface $translator)
    {
        $this->translator = $translator;
    }

    public function trans($id, array $parameters = array(), $domain = null, $locale = null)
    {
        // On affiche les paramètres passés
        if (!empty($parameters)) {
            $id .= '(' . json_encode($parameters) . ')';
        }

        return $id;
    }

    public function transChoice($id, $number, array $parameters = array(), $domain = null, $locale = null)
    {
        // On affiche les paramètres passés
        if (!empty($parameters)) {
            $id .= '(' . json_encode($parameters) . ')';
        }

        return $id;
    }

    public function setLocale($locale)
    {
        $this->locale = $locale;
    }

    public function getLocale()
    {
        return $this->locale;
    }

    public function getCatalogue($locale = null)
    {
        return new MessageCatalogue($locale);
    }

    /**
     * Placeholder that does nothing in test
     */
    public function removeCatalogueCache($locale)
    {
    }

    /**
     * Passes through all unknown calls onto the translator object.
     */
    public function __call($method, $args)
    {
        return \call_user_func_array([$this->translator, $method], $args);
    }
}
