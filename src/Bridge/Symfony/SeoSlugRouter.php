<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Bridge\Symfony;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Wizacha\Marketplace\Seo\SeoService;
use Wizacha\Marketplace\Seo\Slug\SlugGenerator;
use Wizacha\Marketplace\Seo\SlugTargetType;

/**
 * Route a request that contains SEO slugs in the URL.
 */
class SeoSlugRouter implements EventSubscriberInterface
{
    /**
     * @var SlugGenerator
     */
    private $slugGenerator;

    /**
     * @var SeoService
     */
    private $seoService;

    /**
     * @var string
     */
    private $baseUrl;

    /**
     * @param string $baseUrl When the marketplace is installed in sub-folders.
     */
    public function __construct(SlugGenerator $slugGenerator, SeoService $seoService, $baseUrl)
    {
        $this->slugGenerator = $slugGenerator;
        $this->seoService = $seoService;
        $this->baseUrl = $baseUrl;
    }

    public static function getSubscribedEvents()
    {
        return [
            // 100 is a priority higher than Symfony's router (so that it runs before the router)
            KernelEvents::REQUEST => ['routeFromSlug', 100],
        ];
    }

    public function routeFromSlug(RequestEvent $event)
    {
        if (!$event->isMasterRequest()) {
            return;
        }

        $request = $event->getRequest();

        if ('/index.php' !== $request->getScriptName()) {
            // this is not a front-office request
            return;
        }

        $uri = $request->getPathInfo();

        // Remove the .html suffix
        if ($this->urlEndsWith($uri, '.html')) {
            $uri = substr($uri, 0, -\strlen('.html'));
        }

        // Split by / and take the last item
        $uri = array_filter(explode('/', $uri));
        if (empty($uri)) {
            return;
        }
        $uri = end($uri);

        if (!$this->slugGenerator->isSlug($uri)) {
            return;
        }

        $slugTarget = $this->seoService->resolveSlug($uri);
        if (!$slugTarget) {
            return;
        }

        switch ($slugTarget->getTargetType()) {
            case SlugTargetType::PRODUCT():
                $request->attributes->set('productId', $slugTarget->getId());
                break;
            case SlugTargetType::CATEGORY():
                $request->attributes->set('categoryId', $slugTarget->getId());
                break;
            case SlugTargetType::COMPANY():
                $request->attributes->set('companyId', $slugTarget->getId());
                break;
            case SlugTargetType::ATTRIBUTE_VARIANT():
                $request->attributes->set('variantId', $slugTarget->getId());
                break;
            case SlugTargetType::CMS_PAGE():
                $request->attributes->set('pageId', $slugTarget->getId());
                break;
        }
    }

    private function urlEndsWith(string $url, string $needle): bool
    {
        return strrpos($url, $needle) === (\strlen($url) - \strlen($needle));
    }
}
