<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Bridge\Symfony\Test;

/**
 * Overrides getSubscribedEvents() to disable the session mock. It messes up with static usage of $_SESSION.
 */
class TestSessionListener extends \Symfony\Component\HttpKernel\EventListener\TestSessionListener
{
    public static function getSubscribedEvents()
    {
        return [];
    }
}
