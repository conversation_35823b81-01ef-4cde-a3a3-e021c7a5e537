<?php

namespace Wizacha\Bridge\Symfony;

use Symfony\Component\HttpFoundation\Session\SessionBagInterface;
use Symfony\Component\HttpFoundation\Session\Storage\NativeSessionStorage;
use Symfony\Component\HttpFoundation\Session\Storage\MetadataBag;
use Tygh\Registry;

/**
 * Allows session to be started by CS Cart and managed by Symfony.
 */
class CsCartSessionStorage extends NativeSessionStorage
{
    public function __construct(array $options = array(), $handler = null, MetadataBag $metaBag = null)
    {
        $options = array_merge($options, [
            'cookie_path' => $this->getCookiePath(),
            'cookie_domain' => $this->getCookieDomain(),
        ]);

        parent::__construct($options, $handler, $metaBag);
    }

    /**
     * {@inheritdoc}
     *
     * Le nom de la session se compose du type de compte et d'un hash du host
     */
    public function start()
    {
        if (\defined('NO_SESSION')) {
            return;
        }

        $sessPostfix = Registry::get('config.http_location');
        $name = 'sid_' . ACCOUNT_TYPE . '_' . substr(md5($sessPostfix), 0, 5);
        $this->setName($name);

        // Si le cookie de session n'est pas présent, on génère nous-même un ID
        if (empty($_COOKIE[$name])) {
            $this->setId($this->generateSessionId());
        }

        parent::start();
    }

    /**
     * {@inheritdoc}
     */
    public function regenerate($destroy = false, $lifetime = null)
    {
        // Cannot regenerate the session ID for non-active sessions.
        if (\PHP_SESSION_ACTIVE !== session_status()) {
            return false;
        }

        if (headers_sent()) {
            return false;
        }

        if (null !== $lifetime) {
            ini_set('session.cookie_lifetime', $lifetime);
        }

        if ($destroy) {
            $this->metadataBag->stampNew();
        }

        $this->save();

        $oldId = $this->getId();
        $newId = $this->generateSessionId();

        $handler = $this->getSaveHandler();
        $handler->write($newId, $handler->read($oldId));

        $this->setId($newId);
        $this->start();

        // The reference to $_SESSION in session bags is lost in PHP7 and we need to re-create it.
        // @see https://bugs.php.net/bug.php?id=70013
        $this->loadSession();

        if ($destroy) {
            $handler->destroy($oldId);
        }

        return true;
    }

    /**
     * @param array|null $session
     */
    protected function loadSession(array &$session = null)
    {
        if (null === $session) {
            $session = &$_SESSION;
        }

        /** @var SessionBagInterface[] $bags */
        $bags = array_merge($this->bags, array($this->metadataBag));

        foreach ($bags as $bag) {
            $key = $bag->getStorageKey();
            if ($key == '_sf2_attributes') {
                $bag->initialize($session);
            } else {
                $session[$key] = isset($session[$key]) ? $session[$key] : array();
                $bag->initialize($session[$key]);
            }
        }

        $this->started = true;
        $this->closed = false;
    }

    private function getCookiePath()
    {
        $currentPath = Registry::get('config.current_path');

        return !empty($currentPath) ? $currentPath : '/';
    }

    private function getCookieDomain()
    {
        $host = \defined('HTTPS') ? Registry::get('config.https_host') : Registry::get('config.http_host');

        if (false === filter_var($host, FILTER_VALIDATE_DOMAIN)) {
            return '';
        }

        if (false === strpos($host, '.')) {
            return $host;
        }

        // Check if host has www prefix and remove it
        $host = strpos($host, 'www.') === 0 ? substr($host, 3) : '.' . $host;

        return $host;
    }

    private function generateSessionId()
    {
        return md5(uniqid('', true)) . '_' . AREA;
    }
}
