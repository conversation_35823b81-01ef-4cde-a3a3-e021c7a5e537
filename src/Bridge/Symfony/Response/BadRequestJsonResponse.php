<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Bridge\Symfony\Response;

use Symfony\Component\HttpFoundation\JsonResponse;

/**
 * JSON response for a request with an invalid value in a field.
 */
class BadRequestJsonResponse extends JsonResponse
{
    public function __construct(string $message, array $errors = [], array $moreData = [])
    {
        $data = [
            'message' => $message,
        ];

        $data = array_merge($data, $moreData);

        if ($errors) {
            $data['errors'] = $errors;
        }
        parent::__construct($data, 400);
    }

    public static function invalidField(string $field, string $message): self
    {
        return new self('Invalid value in field ' . $field, [
            [
                'field' => $field,
                'message' => $message,
            ],
        ]);
    }

    public static function missingField(string $field): self
    {
        return new self('Missing required field ' . $field, [
            [
                'field' => $field,
                'message' => 'Field is required',
            ],
        ]);
    }

    public static function missingFields(string ...$fields): self
    {
        $errors = array_map(function (string $field) {
            return [
                'field' => $field,
                'message' => 'Field is required',
            ];
        }, $fields);

        return new self('Missing required fields', $errors);
    }

    public static function invalidFields(array $fields): self
    {
        $errors = array_map(function (array $fields) {
            return [
                'field' => $fields['field'],
                'message' => $fields['message'],
            ];
        }, $fields);

        return new self('Invalid value in fields ', $errors);
    }
}
