<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Bridge\Symfony\Response;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

/**
 * 403 Access Denied JSON response.
 */
class AccessDeniedJsonResponse extends JsonResponse
{
    public function __construct(string $message = 'Access Denied')
    {
        parent::__construct([
            'message' => $message,
        ], Response::HTTP_FORBIDDEN);
    }
}
