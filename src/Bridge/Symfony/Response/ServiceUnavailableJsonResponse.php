<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Bridge\Symfony\Response;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

/**
 * 503 Service Unavailable JSON response.
 */
class ServiceUnavailableJsonResponse extends JsonResponse
{
    public function __construct(string $message = 'Service Unavailable')
    {
        parent::__construct([
            'message' => $message,
        ], Response::HTTP_SERVICE_UNAVAILABLE);
    }
}
