<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Bridge\Symfony\Response;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

/**
 * 404 Not Found JSON response.
 */
class NotFoundJsonResponse extends JsonResponse
{
    public function __construct(string $message = 'Not found')
    {
        parent::__construct([
            'message' => $message,
        ], Response::HTTP_NOT_FOUND);
    }
}
