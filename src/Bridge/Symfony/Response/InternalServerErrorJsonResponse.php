<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Bridge\Symfony\Response;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

/**
 * 500 Server Error JSON response.
 */
class InternalServerErrorJsonResponse extends JsonResponse
{
    public function __construct(string $message = 'Internal server error')
    {
        parent::__construct([
            'message' => $message,
        ], Response::HTTP_INTERNAL_SERVER_ERROR);
    }
}
