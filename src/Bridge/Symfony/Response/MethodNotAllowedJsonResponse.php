<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Bridge\Symfony\Response;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

/**
 * 405 Method not allowed JSON response.
 */
class MethodNotAllowedJsonResponse extends JsonResponse
{
    public function __construct(string $message = 'Method not allowed', array $headers = [])
    {
        parent::__construct(
            ['message' => $message,],
            Response::HTTP_METHOD_NOT_ALLOWED,
            $headers
        );
    }
}
