<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Bridge\Symfony\Response;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

/**
 * 401 Unauthorized JSON response.
 */
class UnauthorizedJsonResponse extends JsonResponse
{
    public function __construct(string $message = 'Unauthorized')
    {
        parent::__construct([
            'message' => $message,
        ], Response::HTTP_UNAUTHORIZED);
    }
}
