<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Bridge\Monolog\Processor;

/**
 * Move the 'extra' field from the log context to the log extra data.
 * Keys from context cannot overwrite keys in extra.
 */
class ExtraContextProcessor
{
    public function __invoke(array $record): array
    {
        if (isset($record['context']['extra']) && \is_array($record['context']['extra'])) {
            $record['extra'] = array_merge($record['context']['extra'], $record['extra']);
            unset($record['context']['extra']);
        }

        return $record;
    }
}
