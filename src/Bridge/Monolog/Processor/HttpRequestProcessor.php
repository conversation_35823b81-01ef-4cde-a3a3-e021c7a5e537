<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Bridge\Monolog\Processor;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;

/**
 * Add http.request.id to each log during the processing of a request
 */
class HttpRequestProcessor implements EventSubscriberInterface
{
    use CorrelationProcessorTrait;

    private string $headerName;

    /**
     * @see https://www.elastic.co/guide/en/ecs/1.10/ecs-http.html#field-http-request-id
     */
    private const FIELD_NAME = 'http.request.id';

    public function __construct(string $headerName)
    {
        $this->headerName = $headerName;
    }

    public function onRequestEvent(RequestEvent $event)
    {
        $this->setCorrelationField(
            self::FIELD_NAME,
            $event->getRequest()->headers->get($this->headerName)
        );
    }

    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::REQUEST => [['onRequestEvent', 100]],
        ];
    }
}
