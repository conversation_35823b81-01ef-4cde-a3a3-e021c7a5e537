<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Bridge\Monolog\Processor;

/**
 * Provide a mechanism to add a common piece of information to all logs related to each other by a context of execution.
 * This piece of information is injected in each log as a key/value pair.
 */
trait CorrelationProcessorTrait
{
    private ?string $key = null;
    private ?string $value = null;

    /**
     * Add correlation information to each log
     * @param array $record
     * @return array
     */
    public function addCorrelationInformation(array $record): array
    {
        if ($this->value !== null && $this->key !== null) {
            $record['extra'][$this->key] = $this->value;
        }

        return $record;
    }

    /**
     * @param string $key Correlation field name for logging
     * @param string|null $value Correlation id
     */
    protected function setCorrelationField(string $key, ?string $value): void
    {
        $this->key = $key;
        $this->value = $value;
    }
}
