<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Bridge\Monolog\Processor;

use <PERSON>hum<PERSON>a\Uuid\Uuid;
use Symfony\Component\Console\ConsoleEvents;
use Symfony\Component\Console\Event\ConsoleCommandEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

/**
 * Add process.entity_id to each log during the execution of a command
 */
class ConsoleCommandProcessor implements EventSubscriberInterface
{
    use CorrelationProcessorTrait;

    /**
     * @see https://www.elastic.co/guide/en/ecs/1.10/ecs-process.html#field-process-entity-id
     */
    private const FIELD_NAME = 'process.entity_id';

    public function onCommandCommandEvent(ConsoleCommandEvent $event)
    {
        $this->setCorrelationField(self::FIELD_NAME, Uuid::uuid4()->toString());
    }

    public static function getSubscribedEvents(): array
    {
        return [
            ConsoleEvents::COMMAND => [['onCommandCommandEvent', 100]]
        ];
    }
}
