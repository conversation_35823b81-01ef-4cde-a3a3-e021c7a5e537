<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Bridge\Monolog\EventLogger;

use Monolog\Logger;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\Event\TerminateEvent;
use Symfony\Component\HttpKernel\KernelEvents;

/**
 * Log a message with detailed information at the end of each request processing
 */
class HttpRequestEventLogger implements EventSubscriberInterface, LoggerAwareInterface
{
    use LoggerAwareTrait;
    use EventDurationTrait;

    public function onRequestEvent(RequestEvent $event)
    {
        $this->startEvent();
    }

    public function onTerminateEvent(TerminateEvent $event)
    {
        if (!$event->isMasterRequest() || !$this->hasEventStarted()) {
            return;
        }

        $request = $event->getRequest();
        $response = $event->getResponse();

        $requestData = [
            'http.request.method' => $request->getMethod(),
            'http.response.status_code' => $response->getStatusCode(),
            'url.original' => $request->getRequestUri(),
            'labels.route' => $request->attributes->get('_route'),
        ];
        $eventData = $this->endEvent($this->isSuccess($response));
        $extraData = array_merge($requestData, $eventData);

        $message = sprintf(
            'Done handling request "%s" with status code %d',
            $requestData['labels.route'],
            $requestData['http.response.status_code']
        );

        $this->logger->log($this->getLogLevel($response), $message, [ 'extra' => $extraData ]);
    }

    private function isSuccess(Response $response): bool
    {
        return false === $response->isServerError();
    }

    private function getLogLevel(Response $response): int
    {
        if ($response->isServerError()) {
            return Logger::ERROR;
        }

        if ($response->isClientError()) {
            return Logger::NOTICE;
        }

        return Logger::INFO;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::REQUEST => [['onRequestEvent', 1]],
            KernelEvents::TERMINATE => [['onTerminateEvent', 1]],
        ];
    }
}
