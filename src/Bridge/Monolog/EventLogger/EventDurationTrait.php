<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Bridge\Monolog\EventLogger;

/**
 * Provide a mechanism to generate ECS-compliant data related to the start, end, duration and outcome of an event.
 */
trait EventDurationTrait
{
    private bool $initialized = false;
    private int $hrstart;
    private string $start;

    /**
     * Start event
     */
    protected function startEvent(): void
    {
        $this->initialized = true;
        $this->start = $this->getCurrentDate();
        $this->hrstart = hrtime(true);
    }

    /**
     * Get started status
     * @return bool
     */
    protected function hasEventStarted(): bool
    {
        return $this->initialized;
    }

    /**
     * End event and return event information
     * @param bool|null $success
     * @return array Event information
     */
    protected function endEvent(?bool $success = null): array
    {
        if (false === $this->initialized) {
            return [];
        }

        return [
            'event.start' => $this->start,
            'event.end' => $this->getCurrentDate(),
            'event.duration' => hrtime(true) - $this->hrstart,
            'event.outcome' => $this->getOutcome($success),
        ];
    }

    private function getOutcome(?bool $success): string
    {
        if (true === $success) {
            return 'success';
        }

        if (false === $success) {
            return 'failure';
        }

        return 'unknown';
    }

    private function getCurrentDate(): string
    {
        return (string) date(\DateTimeInterface::RFC3339_EXTENDED);
    }
}
