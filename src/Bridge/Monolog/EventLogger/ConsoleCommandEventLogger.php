<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Bridge\Monolog\EventLogger;

use Monolog\Logger;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use Symfony\Component\Console\ConsoleEvents;
use Symfony\Component\Console\Event\ConsoleCommandEvent;
use Symfony\Component\Console\Event\ConsoleTerminateEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

/**
 * Log a message with detailed information at the end of each command execution
 */
class ConsoleCommandEventLogger implements EventSubscriberInterface, LoggerAwareInterface
{
    use LoggerAwareTrait;
    use EventDurationTrait;

    public function onCommandCommandEvent(ConsoleCommandEvent $event)
    {
        $this->startEvent();
    }

    public function onConsoleTerminateEvent(ConsoleTerminateEvent $event)
    {
        if (!$this->hasEventStarted()) {
            return;
        }

        $command = $event->getCommand();
        $exitCode = $event->getExitCode();

        $commandData = [
            'process.name' => $command->getName(),
            'process.title' => $command->getDescription(),
            'process.pid' => posix_getpid(),
            'process.ppid' => posix_getppid(),
            'process.exit_code' => $exitCode
        ];
        $eventData = $this->endEvent($this->isSuccess($exitCode));
        $extraData = array_merge($commandData, $eventData);

        $message = sprintf(
            'Done handling command "%s" with exit code %d',
            $commandData['process.name'],
            $commandData['process.exit_code']
        );

        $this->logger->log($this->getLogLevel($exitCode), $message, [ 'extra' => $extraData ]);
    }

    private function isSuccess(int $exitCode): bool
    {
        return 0 === $exitCode;
    }

    private function getLogLevel(int $exitCode): int
    {
        if ($exitCode > 0) {
            return Logger::ERROR;
        }

        return Logger::INFO;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            ConsoleEvents::COMMAND => [['onCommandCommandEvent', 1]],
            ConsoleEvents::TERMINATE => [['onConsoleTerminateEvent', 1]]
        ];
    }
}
