<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Bridge\Doctrine;

use Doctrine\DBAL\Connection;

class TableTruncator
{
    private const CSCART_TABLE_DONOT_TRUNCATE = [
        "cscart_country_descriptions",
        "cscart_countries"
    ];

    /**
     * @var Connection
     */
    private $dbConnection;

    public function __construct(Connection $dbConnection)
    {
        $this->dbConnection = $dbConnection;
    }

    public function truncateAllTables()
    {
        $schemaManager = $this->dbConnection->getSchemaManager();
        $tables = $schemaManager->listTables();

        $this->dbConnection->query('SET FOREIGN_KEY_CHECKS = 0');

        $query = '';
        foreach ($tables as $table) {
            if (\in_array($table->getName(), self::CSCART_TABLE_DONOT_TRUNCATE) === true) {
                continue;
            }

            $name = $table->getName();
            $query .= 'TRUNCATE ' . $name . ';';
        }
        $this->dbConnection->executeQuery($query);

        $this->dbConnection->query('SET FOREIGN_KEY_CHECKS = 1');
    }
}
