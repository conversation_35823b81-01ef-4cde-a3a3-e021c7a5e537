<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Bridge\Doctrine;

use Doctrine\Common\EventSubscriber;
use Doctrine\ORM\Event\LoadClassMetadataEventArgs;
use Doctrine\ORM\Mapping\ClassMetadata;
use Doctrine\ORM\Mapping\ClassMetadataInfo;

/**
 * Adds a prefix to all tables mapped by Doctrine's ORM.
 *
 * @see http://docs.doctrine-project.org/projects/doctrine-orm/en/latest/cookbook/sql-table-prefixes.html
 */
class TablePrefixer implements EventSubscriber
{
    private $doctrinePrefix = '';
    private $csCartPrefix = '';
    private $csCartTableRegex;

    public function __construct($doctrinePrefix, $csCartPrefix, $csCartTableRegex)
    {
        $this->doctrinePrefix = (string) $doctrinePrefix;
        $this->csCartPrefix = (string) $csCartPrefix;
        $this->csCartTableRegex = (string) $csCartTableRegex;
    }

    public function getSubscribedEvents()
    {
        return ['loadClassMetadata', 0];
    }

    public function loadClassMetadata(LoadClassMetadataEventArgs $eventArgs)
    {
        /** @var ClassMetadata $classMetadata */
        $classMetadata = $eventArgs->getClassMetadata();

        if (!$classMetadata->isInheritanceTypeSingleTable() || $classMetadata->getName() === $classMetadata->rootEntityName) {
            $classMetadata->setTableName($this->buildPrefixedTableName($classMetadata->getTableName()));
        }

        foreach ($classMetadata->getAssociationMappings() as $fieldName => $mapping) {
            if ($mapping['type'] == ClassMetadataInfo::MANY_TO_MANY && $mapping['isOwningSide'] && \array_key_exists('inherited', $mapping) === false) {
                $mappedTableName = $mapping['joinTable']['name'];
                $classMetadata->associationMappings[$fieldName]['joinTable']['name'] = $this->buildPrefixedTableName($mappedTableName);
            }
        }
    }

    private function buildPrefixedTableName($name)
    {
        if (0 !== preg_match($this->csCartTableRegex, $name)) {
            return $this->csCartPrefix . $name;
        }

        return $this->doctrinePrefix . $name;
    }
}
