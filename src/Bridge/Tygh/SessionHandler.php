<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Bridge\Tygh;

use Tygh\Backend\Session\IBackend;
use Symfony\Component\HttpFoundation\Session\Storage\Proxy\SessionHandlerProxy;

class <PERSON><PERSON><PERSON><PERSON> implements \SessionHandlerInterface
{
    private $backend;
    private $lifetime;

    public function __construct(IBackend $backend, int $inactivityTimeOut)
    {
        $this->backend = $backend;
        $this->lifetime = $inactivityTimeOut;
    }

    public function close()
    {
        return true;
    }

    public function destroy($sessionId)
    {
        return $this->backend->delete($sessionId);
    }

    public function gc($maxlifetime)
    {
        return $this->backend->gc($maxlifetime);
    }

    public function open($savePath, $name)
    {
        return true;
    }

    public function read($sessionId)
    {
        return $this->backend->read($sessionId);
    }

    public function write($sessionId, $sessionData)
    {
        $sessionData = [
            'expiry' => TIME + $this->lifetime,
            'data' => $sessionData,
        ];

        return $this->backend->write($sessionId, $sessionData);
    }
}
