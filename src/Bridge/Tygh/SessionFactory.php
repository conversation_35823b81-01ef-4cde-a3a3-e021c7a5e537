<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Bridge\Tygh;

use Tygh\Registry;

class SessionFactory
{
    public static function createSessionBackend()
    {
        $sessionClass = Registry::ifGet('config.session_backend', 'database');
        $sessionClass = '\\Tygh\\Backend\\Session\\' . ucfirst($sessionClass);

        return new $sessionClass(Registry::get('config'));
    }
}
