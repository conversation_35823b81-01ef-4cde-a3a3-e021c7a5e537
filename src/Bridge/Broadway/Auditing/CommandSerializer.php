<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Bridge\Broadway\Auditing;

use Broadway\Auditing\CommandSerializerInterface;

class CommandSerializer implements CommandSerializerInterface
{
    /**
     * {@inheritDoc}
     */
    public function serialize($command)
    {
        $serializedCommand = array();
        foreach ((array) $command as $key => $value) {
            if (\is_object($value) && \is_callable([$value, 'getId'])) {
                $value = $value->getId();
            }
            $serializedCommand[str_replace("\0", '-', $key)] = $value;
        }

        return $serializedCommand;
    }
}
