<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Bridge\Broadway\CommandHandlers;

use Broadway\CommandHandling\CommandHandlerInterface;

abstract class AbstractCommandHandler implements CommandHandlerInterface
{

    /**
     * @return string
     */
    abstract public function getBaseCommandClass();

    /**
     * Kept same naming than in broadway but filter commands by heritage
     * @inherit
     */
    public function handle($command)
    {
        $reflexive = new \ReflectionClass($command);
        if (!$reflexive->isSubclassOf($this->getBaseCommandClass())) {
            return;
        }
        $method = 'handle' . $reflexive->getShortName();

        if (! method_exists($this, $method)) {
            return;
        }

        $this->$method($command);
    }
}
