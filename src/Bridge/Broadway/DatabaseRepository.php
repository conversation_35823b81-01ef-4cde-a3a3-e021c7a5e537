<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Bridge\Broadway;

use Broadway\ReadModel\ReadModelInterface;
use Broadway\ReadModel\RepositoryInterface;

class DatabaseRepository implements RepositoryInterface
{

    protected $index;
    protected $class;

    public function __construct($index, $class)
    {
        $this->index = $index;
        $this->class = $class;
    }

    /**
     * {@inheritDoc}
     */
    public function save(ReadModelInterface $data)
    {
        \Tygh\Database::query("REPLACE INTO ?:read_model(`index`, `id`, `readmodel`) VALUES (?s,?s,?s);", $this->index, $data->getId(), serialize($data));
    }

    /**
     * {@inheritDoc}
     */
    public function find($id)
    {
        try {
            $readModel = \Tygh\Database::getField(
                'SELECT readmodel FROM ?:read_model WHERE `index`=?s AND `id`= ?s',
                $this->index,
                $id
            );
            return $readModel ? unserialize($readModel) : null;
        } catch (\Exception $e) {
            $logger = container()->get('logger');
            $logger->notice(
                "[Cscart Repository] " . $e->getMessage(),
                [
                    'index' => $this->index,
                    'id'    => $id,
                ]
            );
            throw $e;
        }
    }

    public function exists(string $id): bool
    {
        return null !== \Tygh\Database::getField(
            'SELECT 1 FROM ?:read_model WHERE `index`=?s AND `id`= ?s',
            $this->index,
            $id
        );
    }

    /**
     * {@inheritDoc}
     */
    public function findBy(array $fields)
    {
        if (isset($fields['id'])) {
            return $this->find($fields['id']);
        }
        return [];
    }

    /**
     * @todo
     * {@inheritDoc}
     */
    public function findAll()
    {
        return false;
    }

    /**
     * {@inheritDoc}
     */
    public function remove($id)
    {
        \Tygh\Database::query('DELETE FROM ?:read_model WHERE `index`=?s AND `id`=?s', $this->index, $id);
    }


    public function removeAll()
    {
        \Tygh\Database::query('DELETE FROM ?:read_model WHERE `index`=?s', $this->index);
    }
}
