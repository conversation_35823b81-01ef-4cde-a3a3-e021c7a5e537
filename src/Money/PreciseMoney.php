<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Money;

use Wizacha\Decimal\AbstractDecimal;

// Class used to store Money object in database with 4 digits after comma
class PreciseMoney extends AbstractDecimal
{
    /** @var int */
    protected static $digitsPrecision = 4;

    /** @var int */
    protected $value;

    // Automatically call by doctrine to convert the object into an integer
    public function getAmount(): int
    {
        return $this->getValue();
    }

    public function getValue(): int
    {
        return $this->value;
    }

    protected function setValue(int $value): parent
    {
        $this->value = $value;

        return $this;
    }

    public function getConvertedAmount(int $roundingMode = PHP_ROUND_HALF_ODD): float
    {
        return round($this->value / $this->getPrecisionCoeff(), 2, $roundingMode);
    }

    public function getPreciseConvertedAmount(): float
    {
        return round($this->value / $this->getPrecisionCoeff(), 4);
    }
}
