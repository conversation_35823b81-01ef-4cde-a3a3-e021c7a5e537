<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Money;

use Wizacha\Decimal\AbstractDecimalDoctrineType;

class PreciseMoneyDoctrineType extends AbstractDecimalDoctrineType
{
    public const NAME = 'precise_money';

    public function getName(): string
    {
        return static::NAME;
    }

    public function getPhpInstance(int $value): PreciseMoney
    {
        return new PreciseMoney($value);
    }
}
