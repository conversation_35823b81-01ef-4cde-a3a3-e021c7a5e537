<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Money;

use Wizacha\Decimal\AbstractDecimalDoctrineType;

class MoneyDoctrineType extends AbstractDecimalDoctrineType
{
    public const NAME = 'money';

    public function getName(): string
    {
        return static::NAME;
    }

    public function getPhpInstance(int $value): Money
    {
        return new Money($value);
    }
}
