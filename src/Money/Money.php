<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Money;

use Wizacha\Decimal\AbstractDecimal;
use Wizacha\Marketplace\ReadModel\ReadModelObjectId;

/**
 * Value object that represents a monetary value using a currency's smallest unit.
 */
class Money extends AbstractDecimal implements \JsonSerializable
{
    /** @var int $amount */
    private $amount;

    /**
     * Propriété temporaire le temps de regenerer les readmodels produits
     *
     * Même si cette propriété est toujours initialisée à true dans le code
     * ci-dessous il est très probable qu'il y ait encore des versions sérialisées
     * en base de données où cette valeur reste à false, il faut donc
     * malheureusement garder cette propriété
     *
     * @var boolean
     */
    private $preciseMoney = false;

    public function __construct(int $value)
    {
        $this->preciseMoney = true;

        parent::__construct($this->castToInt($value * $this->getInputCoeff()));
    }

    /**
     * Used for database storage and to get the integer representation of the number
     */
    public function getAmount(): int
    {
        return $this->castToInt(round($this->amount / $this->getInputCoeff(), 0, PHP_ROUND_HALF_ODD));
    }

    public function getConvertedAmount(int $roundingMode = PHP_ROUND_HALF_ODD): float
    {
        return round($this->amount / $this->getPrecisionCoeff(), 2, $roundingMode);
    }

    public function getPreciseConvertedAmount(): float
    {
        return round($this->amount / $this->getPrecisionCoeff(), 4);
    }

    public function reducePrecisionToCents(): self
    {
        return static::fromVariable($this->getConvertedAmount());
    }

    /** Alias */
    public function getInt(): int
    {
        return $this->getAmount();
    }

    /**
     * @return int[]
     */
    public function jsonSerialize(): array
    {
        return [
            ReadModelObjectId::MONEY()->getValue() => $this->getAmount(),
        ];
    }

    public function getValue(): int
    {
        return $this->amount;
    }

    protected function setValue(int $value): parent
    {
        $this->amount = $value;

        return $this;
    }

    protected function getPrecisionCoeff(): int
    {
        return $this->preciseMoney ? 10000 : 100;
    }

    /**
     * We raise precision at input for better calculation precision, we will round it in getAmount()
     */
    protected function getInputCoeff(): int
    {
        return $this->preciseMoney ? 100 : 1;
    }
}
