<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Image;

use Wizacha\Marketplace\Exception\NotFound;

class Image
{
    /**
     * @var int
     */
    private $id;

    private ?string $altText;

    public function __construct(int $imageId, string $altText = null)
    {
        $this->id = $imageId;
        $this->altText = $altText;
    }

    /**
     * Create an instance from a CS Cart data array.
     */
    public static function fromCsCartData(array $data): self
    {
        $imageId = (int) ($data['detailed_id'] ?: $data['image_id']);

        return new self($imageId);
    }

    /**
     * Create string of image's alternative text from CS Cart data array.
     *
     * @param array $data
     * @return string
     */
    public static function fromCsCartDataAltImage(array $data): ?string
    {
        return (string) ($data['icon']['alt'] ?? null);
    }

    public function getId(): int
    {
        return $this->id;
    }

    /** Serialize the object to an array. */
    public function toArray(): array
    {
        return [
            'id' => $this->getId(),
            'altText' => $this->getAltText()
        ];
    }

    public function getAltText(): ?string
    {
        return $this->altText;
    }
}
