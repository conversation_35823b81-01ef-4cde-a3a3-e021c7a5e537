<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Product;

use MyCLabs\Enum\Enum;

/**
 * Class ProductStatus
 * @package Wizacha\Marketplace\Product
 *
 * @method static ProductStatus ENABLED()
 * @method static ProductStatus DISABLED()
 * @method static ProductStatus HIDDEN()
 */
final class ProductStatus extends Enum
{
    public const ENABLED = 'A';
    public const DISABLED = 'D';
    public const HIDDEN = 'H';
}
