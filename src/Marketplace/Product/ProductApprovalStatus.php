<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Product;

use MyCLabs\Enum\Enum;

/**
 * Class Product
 * @package Wizacha\Marketplace\Product
 *
 * @method static ProductApprovalStatus APPROVED()
 * @method static ProductApprovalStatus REJECTED()
 * @method static ProductApprovalStatus PENDING()
 * @method static ProductApprovalStatus STANDBY()
 */
final class ProductApprovalStatus extends Enum
{
    public const APPROVED = 'Y';
    public const REJECTED = 'N';
    public const PENDING = 'P';
    public const STANDBY = 'S';
}
