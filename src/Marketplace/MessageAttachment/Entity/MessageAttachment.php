<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\MessageAttachment\Entity;

use Rhumsaa\Uuid\Uuid;
use Wizacha\Marketplace\Traits\HasUuid;

class MessageAttachment implements \JsonSerializable
{
    use HasUuid;

    protected ?string $id;

    /** @var int */
    protected $messageId;

    /** @var string */
    protected $name;

    /** @var string|null */
    protected $publicUrl;

    /** @var string */
    protected $downloadUrl;

    /** @var string */
    protected $viewUrl;

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getMessageId(): int
    {
        return $this->messageId;
    }

    public function setMessageId(int $messageId): self
    {
        $this->messageId = $messageId;

        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getPublicUrl(): ?string
    {
        return $this->publicUrl;
    }

    public function setPublicUrl(string $publicUrl): self
    {
        $this->publicUrl = $publicUrl;

        return $this;
    }

    public function getDownloadUrl(): string
    {
        return $this->downloadUrl;
    }

    public function setDownloadUrl(string $downloadUrl): self
    {
        $this->downloadUrl = $downloadUrl;

        return $this;
    }

    public function getViewUrl(): string
    {
        return $this->viewUrl;
    }

    public function setViewUrl(string $viewUrl): self
    {
        $this->viewUrl = $viewUrl;

        return $this;
    }

    public function defineGuid(): self
    {
        $this->id = Uuid::uuid4()->toString();

        return $this;
    }

    public function jsonSerialize()
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'messageId' => $this->getMessageId(),
            'publicUrl' => $this->getPublicUrl()
        ];
    }
}
