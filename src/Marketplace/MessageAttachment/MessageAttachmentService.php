<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\MessageAttachment;

use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;
use Wizacha\Marketplace\Exception\FileNotFoundException;
use Wizacha\Marketplace\MessageAttachment\Entity\MessageAttachment;
use Wizacha\Marketplace\MessageAttachment\Repository\MessageAttachmentRepository;
use Wizacha\Marketplace\Order\OrderAttachment\Exception\UploadFailException;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Wizacha\Storage\StorageService;

class MessageAttachmentService
{
    public const VALIDATE_ATTACHMENT_MESSAGE_EXTENSIONS = ['png', 'jpg', 'jpeg', 'pdf'];

    protected RouterInterface $router;

    protected MessageAttachmentRepository $attachmentsRepository;

    private StorageService $messageAttachmentsStorageService;

    public function __construct(
        RouterInterface $router,
        MessageAttachmentRepository $attachmentsRepository,
        StorageService $messageAttachmentsStorageService
    ) {
        $this->router = $router;
        $this->attachmentsRepository = $attachmentsRepository;
        $this->messageAttachmentsStorageService = $messageAttachmentsStorageService;
    }

    /**
     * @param UploadedFile[] $messageAttachmentData
     * @param int $messageId
     *
     * @return void
     */
    public function create(array $messageAttachmentData, int $messageId): void
    {
        foreach ($messageAttachmentData as $attachmentData) {
            $filename = $attachmentData->getClientOriginalName();
            $attachment = $this->attachmentsRepository->save(
                (new MessageAttachment())
                    ->setName($filename)
                    ->setMessageId($messageId)
            );
            $this->uploadFile($attachment, $attachmentData);
        }
    }

    protected function generateUrl(MessageAttachment $messageAttachment): string
    {
        return $this->router->generate(
            'api_message_attachment_download',
            ['messageId' => $messageAttachment->getMessageId(), 'attachmentId' => $messageAttachment->getId()],
            UrlGeneratorInterface::ABSOLUTE_URL
        );
    }

    public function uploadFile(MessageAttachment $attachment, UploadedFile $messageData): void
    {
        $filePath = $messageData->getRealPath();
        // Store file
        $result = $this->messageAttachmentsStorageService->put($this->getFilePath($attachment), ['file' => $filePath]);
        if ($result === false) {
            $this->attachmentsRepository->delete($attachment);
            throw new UploadFailException();
        }
    }

    public function get(string $attachmentId): MessageAttachment
    {
        $messageAttachment = $this->attachmentsRepository->findOneById($attachmentId);

        if ($messageAttachment instanceof MessageAttachment === false) {
            throw new NotFoundHttpException('Attachment not found.');
        }

        $messageAttachment->setPublicUrl($this->generateUrl($messageAttachment));

        return $messageAttachment;
    }

    /**
     * @param int $messageId
     *
     * @return MessageAttachment[]
     */
    public function getAttachmentsByMessage(int $messageId): array
    {
        $attachments =  $this->attachmentsRepository->findByMessage($messageId);

        foreach ($attachments as $attachment) {
            try {
                $file = $this->messageAttachmentsStorageService->get($this->getFilePath($attachment));
            } catch (FileNotFoundException $exception) {
                $attachment->setDownloadUrl('');
                $attachment->setPublicUrl('');
                $attachment->setViewUrl('');

                continue;
            }

            $attachment->setPublicUrl($this->generateUrl($attachment));
            $attachment->setViewUrl(
                $this->messageAttachmentsStorageService->getUrl(
                    $this->getFilePath($attachment),
                    '',
                    'inline'
                )
            );
            $attachment->setDownloadUrl(
                $this->messageAttachmentsStorageService->getUrl(
                    $this->getFilePath($attachment),
                    '',
                    'attachment'
                )
            );
        }

        return $attachments;
    }

    public function removeFile(string $path): void
    {
        $isRemoved = $this->messageAttachmentsStorageService->delete($path);

        if ($isRemoved === false) {
            throw new UploadFailException('Upload of the file failed: cannot remove existing file.');
        }
    }

    /**
     * @param UploadedFile[] $files
     *
     * @return bool
     */
    public function isValidateFilesExtension(array $files): bool
    {
        foreach ($files as $file) {
            $fileName = explode(".", $file->getClientOriginalName());
            if (\in_array(end($fileName), self::VALIDATE_ATTACHMENT_MESSAGE_EXTENSIONS) === false) {
                return false;
            }
        }

        return true;
    }

    /**
     * @param UploadedFile[] $files
     *
     * @return bool
     */
    public function isFilesSizeValid(array $files): bool
    {
        foreach ($files as $file) {
            // convert the file size from bytes to megabyte
            // the file size must be less than or equal to 25 megabyte
            if (\number_format($file->getsize() / 1048576, 1) > 25) {
                return false;
            }
        }

        return true;
    }

    private function getFilePath(MessageAttachment $attachment): string
    {
        return $attachment->getMessageId() . '/'
            . $attachment->getId() . '/'
            . $attachment->getName();
    }
}
