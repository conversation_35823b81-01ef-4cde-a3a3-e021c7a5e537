<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\MessageAttachment\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Wizacha\Marketplace\MessageAttachment\Entity\MessageAttachment;

class MessageAttachmentRepository extends ServiceEntityRepository
{
    public function save(MessageAttachment $messageAttachment): MessageAttachment
    {
        $this->getEntityManager()->persist($messageAttachment);
        $this->getEntityManager()->flush();

        return $messageAttachment;
    }

    public function delete(MessageAttachment $messageAttachment): void
    {
        $this->getEntityManager()->remove($messageAttachment);
        $this->getEntityManager()->flush();
    }

    public function findOneById(string $attachmentId): ?MessageAttachment
    {
        return $this->find($attachmentId);
    }

    /**
     * @param int $messageId
     *
     * @return MessageAttachment[]
     */
    public function findByMessage(int $messageId): ?array
    {
        return $this->findBy(['messageId' => $messageId]);
    }
}
