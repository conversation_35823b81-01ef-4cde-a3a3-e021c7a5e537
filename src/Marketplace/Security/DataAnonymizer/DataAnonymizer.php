<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Security\DataAnonymizer;

class DataAnonymizer
{
    public const DEFAULT_REPLACEMENT_CHARACTER = '*';
    public const DEFAULT_LEFT_LIMIT = 0;
    public const DEFAULT_RIGHT_LIMIT = 0;

    public const IBAN_LEFT_LIMIT = 4;
    public const IBAN_RIGHT_LIMIT = 4;

    public const TOKEN_LEFT_LIMIT = 0;
    public const TOKEN_RIGHT_LIMIT = 2;

    public const PERSONAL_DATA_LEFT_LIMIT = 1;
    public const PERSONAL_DATA_RIGHT_LIMIT = 1;

    public const IBAN_TYPE = 'iban';
    public const TOKEN_TYPE = 'token';
    public const PERSONAL_DATA_TYPE = 'personal_data';
    public const FULL_NAME_TYPE = 'full_name';
    public const LONG_TEXT_TYPE = 'long_text';
    public const COMPLETE_ANONYMIZE_TYPE = 'complete_anonymize';

    protected array $dataToAnonymize = [];
    protected array $contextToAnonymize = [];

    /**
     * This function obfuscate the number of characters desired in a string
     * Starts from $leftLimit and stop to $rightLimit (it keeps the $leftLimit and $rightLimit characters)
     * Example :
     * Consider the string "1234567890"
     * If $leftLimit = 3 and $rightLimit = 2 the obfuscated string will be : "123*****90"
     * If the string length is below the limits or the limits are 0, we obfuscate all its characters
     */
    public static function anonymizeData(
        ?string $data,
        int $leftLimit = self::DEFAULT_LEFT_LIMIT,
        int $rightLimit = self::DEFAULT_RIGHT_LIMIT,
        ?int $maxLength = null
    ): ?string {
        if (\is_string($data) === false) {
            return $data;
        }

        $dataLength = \mb_strlen($data);
        if ($dataLength === 0) {
            return $data;
        }

        if ($leftLimit < 0) {
            $leftLimit = 0;
        }

        if ($rightLimit < 0) {
            $rightLimit = 0;
        }

        $nbCharactersToKeep = $leftLimit + $rightLimit;

        if ($dataLength <= $nbCharactersToKeep) {
            $leftLimit = 0;
            $rightLimit = 0;
            $nbCharactersToKeep = 0;
        }

        if ($maxLength === null || $maxLength <= $nbCharactersToKeep || $maxLength > $dataLength) {
            $maxLength = $dataLength;
        }

        $nbCharactersToReplace = $maxLength - $nbCharactersToKeep;

        // We cannot use the substr_replace function as we must handle the multibytes characters (ex: é)
        // Old function used: return \substr_replace($data, $replacementString, $leftLimit, $nbCharactersToReplace);
        // But no \mb_substr_replace or such function exists
        // The solution is to use \mb_substr to keep the correct limits and reconstruct manually the anonymized string
        $anonymizedData = \mb_substr($data, 0, $leftLimit);
        $anonymizedData .= \str_repeat(self::DEFAULT_REPLACEMENT_CHARACTER, $nbCharactersToReplace);
        $anonymizedData .= \mb_substr($data, $dataLength - $rightLimit);

        return $anonymizedData;
    }

    /**
     * Obfuscate an IBAN with the following value:
     * - $leftLimit = 4
     * - $rightLimit = 4
     * Example : "****************" will become "FR00********0000"
     */
    public static function anonymizeIban(?string $data): ?string
    {
        return static::anonymizeData($data, self::IBAN_LEFT_LIMIT, self::IBAN_RIGHT_LIMIT);
    }

    /**
     * Obfuscate a token with the following value:
     * - $leftLimit = 0
     * - $rightLimit = 2
     * Example : "2dza145f6ez78g9e4d5z6a" will become "********************6a"
     */
    public static function anonymizeToken(?string $data): ?string
    {
        return static::anonymizeData($data, self::TOKEN_LEFT_LIMIT, self::TOKEN_RIGHT_LIMIT);
    }

    /**
     * Obfuscate a personal data (such as login or an email) with the following value:
     * - $leftLimit = 1
     * - $rightLimit = 1
     * Example : "<EMAIL>" will become "p***********************m"
     */
    public static function anonymizePersonalData(?string $data): ?string
    {
        return static::anonymizeData($data, self::PERSONAL_DATA_LEFT_LIMIT, self::PERSONAL_DATA_RIGHT_LIMIT);
    }

    /**
     * Obfuscate a full name data (firstnames lastnames) with the following value for each name:
     * - $leftLimit = 1
     * - $rightLimit = 1
     * Example : "Jules César" will become "J***s C***r"
     */
    public static function anonymizeFullNameData(?string $data): ?string
    {
        if (\is_string($data) === false) {
            return $data;
        }

        $anonymizedFullNameArray = array_map(function ($value) {
            return static::anonymizePersonalData($value);
        }, explode(' ', $data));

        return implode(' ', $anonymizedFullNameArray);
    }

    public static function anonymizeLongTextData(?string $data): ?string
    {
        return static::anonymizeData($data, self::PERSONAL_DATA_LEFT_LIMIT, self::PERSONAL_DATA_RIGHT_LIMIT, 50);
    }

    /**
     * Find and anonymize all the data in the original $data array according to the $dataToAnonymize array
     * First call can be made without $dataToAnonymize, it will be retrieved form the DataAnonymizer instance
     * The $dataToAnonymize array must match the $data array tree to find each associative data correctly
     */
    public function findAndAnonymizeData(array $data, ?array $dataToAnonymize = null): array
    {
        if ($dataToAnonymize === null) {
            $dataToAnonymize = $this->dataToAnonymize;
            if (\count($dataToAnonymize) === 0) {
                return $data;
            }
        }

        foreach ($dataToAnonymize as $dataName => $dataType) {
            if ((\is_string($dataName) === true && \array_key_exists($dataName, $data) === false)
                || \is_null($data[$dataName])
            ) {
                continue;
            }

            if (\is_array($dataType) === true) {
                // If we have "#or" it means the same key can represent either a value (string, int, etc) or an array
                if (\array_key_exists('#or', $dataType) === true) {
                    if (\is_array($data[$dataName]) === true) {
                        $dataType = $dataType['#or'][1];
                    } else {
                        $dataType = $dataType['#or'][0];
                    }
                }

                if (\is_string($dataName) === true && \is_array($data[$dataName]) === true) {
                    $data[$dataName] = $this->findAndAnonymizeData($data[$dataName], $dataType);
                }

                // If it's a list, the $dataName is an integer index, we must anonymize each item
                if (\is_numeric($dataName) === true && \is_array($data) === true) {
                    foreach ($data as $index => $subData) {
                        if (\is_array($subData) === true) {
                            $data[$index] = $this->findAndAnonymizeData($subData, $dataType);
                        }
                    }
                }

                // If the dataType is still an array after checking the "#or" condition
                if (\is_array($dataType) === true) {
                    continue;
                }
            }

            // Used for integers for example. In order to anonymize them we must first cast them into string.
            if ($data[$dataName] !== null && \is_string($data[$dataName]) === false) {
                $data[$dataName] = (string) $data[$dataName];
            }

            switch ($dataType) {
                case self::IBAN_TYPE:
                    $data[$dataName] = static::anonymizeIban($data[$dataName]);
                    break;

                case self::TOKEN_TYPE:
                    $data[$dataName] = static::anonymizeToken($data[$dataName]);
                    break;

                case self::PERSONAL_DATA_TYPE:
                    $data[$dataName] = static::anonymizePersonalData($data[$dataName]);
                    break;

                case self::FULL_NAME_TYPE:
                    $data[$dataName] = static::anonymizeFullNameData($data[$dataName]);
                    break;

                case self::LONG_TEXT_TYPE:
                    $data[$dataName] = static::anonymizeLongTextData($data[$dataName]);
                    break;

                case self::COMPLETE_ANONYMIZE_TYPE:
                default:
                    $data[$dataName] = static::anonymizeData($data[$dataName]);
                    break;
            }
        }

        return $data;
    }

    public function findAndAnonymizeContext(array $data): array
    {
        return $this->findAndAnonymizeData($data, $this->contextToAnonymize);
    }

    public function getDataToAnonymize(): array
    {
        return $this->dataToAnonymize;
    }

    public function getContextToAnonymize(): array
    {
        return $this->contextToAnonymize;
    }
}
