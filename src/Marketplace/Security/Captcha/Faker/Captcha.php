<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Security\Captcha\Faker;

use ReCaptcha\Response;

class Captcha extends \Wizacha\Marketplace\Security\Captcha\Captcha
{
    public function __construct()
    {
        parent::__construct('', 'fake'); // secret is required
    }

    /**
     * Faker : always return Successful Response
     *
     * @param string $response Content does not matter.
     * @param string $remoteIp Content does not matter.
     * @return Response Response from the service.
     */
    public function verify($response, $remoteIp = null)
    {
        return new Response(true, []);
    }

    /**
     * @return string JS scripts to include 'as is' in a template
     */
    public function getJS()
    {
        return '';
    }
}
