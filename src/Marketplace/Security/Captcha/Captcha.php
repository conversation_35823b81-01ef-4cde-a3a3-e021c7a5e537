<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Security\Captcha;

use ReCaptcha\ReCaptcha;

class Captcha extends ReCaptcha
{
    /**
     * @var string
     */
    private $publicKey;

    /**
     * @see \ReCaptcha\ReCaptcha::__construct
     */
    public function __construct(string $publicKey, string $secretKey)
    {
        parent::__construct($secretKey);

        $this->publicKey = $publicKey;
    }

    /**
     * Take a request array, verify and return success
     * @param array $request Content of $_REQUEST
     * @return bool
     */
    public function isHuman($request = [])
    {
        return $this
            ->verify(
                isset($request['g-recaptcha-response']) ? $request['g-recaptcha-response'] : ''
            )->isSuccess();
    }

    /**
     * @return string JS scripts to include 'as is' in a template
     */
    public function getJS()
    {
        return "
<script src='//www.google.com/recaptcha/api.js?onload=onLoadCallback&render=explicit'></script>
<script type='text/javascript'>
    var onLoadCallback = function() {
        $('.wizacha-recaptcha').each(function(){
            grecaptcha.render($(this).attr('id'), {
                'sitekey' : '" . $this->publicKey . "'
            });
        });
    };
</script>";
    }
}
