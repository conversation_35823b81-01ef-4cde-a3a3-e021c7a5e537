<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Security\Event;

use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Symfony\Contracts\EventDispatcher\Event;
use Wizacha\Component\Notification\NotificationEvent;

class IpBlockedEvent extends Event implements NotificationEvent
{
    private string $ipAddress;
    private int $maxAttempts;
    private int $waitingDuration;

    public function __construct(string $ipAddress, int $maxAttempts, int $waitingDuration)
    {
        $this->ipAddress = $ipAddress;
        $this->maxAttempts = $maxAttempts;
        $this->waitingDuration = $waitingDuration;
    }

    public static function getDescription(): string
    {
        return 'ip_blocked';
    }

    public static function buildDebugForm(FormBuilder $form): void
    {
        $form->add('ipAddress', TextType::class);
        $form->add('maxAttempts', IntegerType::class);
        $form->add('waitingDuration', IntegerType::class);
    }

    public static function createFromForm(Form $form): self
    {
        return new static(
            $form->getData()['ipAddress'],
            $form->getData()['maxAttempts'],
            $form->getData()['waitingDuration']
        );
    }

    public function getIpAddress(): string
    {
        return $this->ipAddress;
    }

    public function getMaxAttempts(): int
    {
        return $this->maxAttempts;
    }

    public function getWaitingDuration(): int
    {
        return $this->waitingDuration;
    }
}
