<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\RgpdAnonymizer\Report;

use MyCLabs\Enum\Enum;

/**
 * @method static Status NOT_ANONYMIZED()
 * @method static Status ANONYMIZATION_IN_PROGRESS()
 * @method static Status ANONYMIZED()
 * @method static Status ERROR_DURING_ANONIMIZATION()
 */
class Status extends Enum
{
    protected const NOT_ANONYMIZED = 'Not anonymized';
    protected const ANONYMIZATION_IN_PROGRESS = 'Anonymization in progress';
    protected const ANONYMIZED = 'Anonymized';
    protected const ERROR_DURING_ANONIMIZATION = 'Error during anonymization';
}
