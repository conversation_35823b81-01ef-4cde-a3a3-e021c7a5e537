<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\RgpdAnonymizer\Report;

class CompanyReport implements ReportInterface
{
    /** @var int */
    protected $companyId;

    /** @var Status */
    protected $status;

    /** @var UserReport[] */
    protected $userReports = [];

    /** @var Error[] */
    protected $errors = [];

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    public function setCompanyId(int $companyId): self
    {
        $this->companyId = $companyId;

        return $this;
    }

    public function getStatus(): Status
    {
        return $this->status;
    }

    public function setStatus(Status $status): self
    {
        $this->status = $status;

        return $this;
    }

    /** @return Error[] */
    public function getErrors(): array
    {
        return $this->errors;
    }

    public function addError(string $exceptionType, string $message): ReportInterface
    {
        $this->errors[] = (new Error())
            ->setExceptionType($exceptionType)
            ->setMessage($message)
        ;

        return $this;
    }

    /** @return UserReport[] */
    public function getUserReports(): array
    {
        return $this->userReports;
    }

    public function addUserReport(ReportInterface $report): self
    {
        $this->userReports[] = $report;

        return $this;
    }

    /** @return mixed[] */
    public function expose(): array
    {
        $report = [];
        if (\count($this->getErrors()) === 0) {
            $report[] = 'The company ' . $this->getCompanyId() . ' has been anonymized.';
        } else {
            foreach ($this->getErrors() as $error) {
                $report[] = $error->getExceptionType() . ": " . $error->getMessage() . ' on company ' . $this->getCompanyId();
            }
        }

        foreach ($this->getUserReports() as $userReport) {
             $report = \array_merge($report, $userReport->expose());
        }

        return $report;
    }
}
