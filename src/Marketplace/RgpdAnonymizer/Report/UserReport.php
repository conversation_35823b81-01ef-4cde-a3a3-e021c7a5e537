<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\RgpdAnonymizer\Report;

class UserReport implements ReportInterface
{
    /** @var int */
    protected $userId;

    /** @var Status */
    protected $status;

    /** @var Error[] */
    protected $errors = [];

    public function getUserId(): int
    {
        return $this->userId;
    }

    public function setUserId(int $userId): self
    {
        $this->userId = $userId;

        return $this;
    }

    public function getStatus(): Status
    {
        return $this->status;
    }

    public function setStatus(Status $status): self
    {
        $this->status = $status;

        return $this;
    }

    /** @return Error[] */
    public function getErrors(): array
    {
        return $this->errors;
    }

    public function addError(string $exceptionType, string $message): ReportInterface
    {
        $this->errors[] = (new Error())
            ->setExceptionType($exceptionType)
            ->setMessage($message)
        ;

        return $this;
    }

    /** @return string[] */
    public function expose(): array
    {
        $report = [];
        if (\count($this->getErrors()) === 0) {
            $report[] = 'The user ' . $this->getUserId() . ' has been anonymized';
        } else {
            foreach ($this->getErrors() as $error) {
                $report[] = $error->getExceptionType() . ": " . $error->getMessage();
            }
        }

        return $report;
    }
}
