<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\RgpdAnonymizer\Report;

interface ReportInterface
{
    /** @return Error[] */
    public function getErrors(): array;

    public function addError(string $exceptionType, string $message): self;

    /** @return string[] */
    public function expose(): array;
}
