<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\RgpdAnonymizer;

use Wizacha\Marketplace\RgpdAnonymizer\Exception\RgpdAnonymizerNotFoundException;

class RgpdAnonymizerFactory
{
    /** @var RgpdAnonymizerInterface[] */
    protected $anonymizers;

    public function __construct(iterable $anonymizers)
    {
        $this->anonymizers = $anonymizers;

        foreach ($anonymizers as $anonymizer) {
            if (false === $anonymizer instanceof RgpdAnonymizerInterface) {
                throw new \LogicException('You must only provide an RgpdAnonymizerInterface.');
            }
        }
    }

    public function getAnonymizer(string $type): RgpdAnonymizerInterface
    {
        if (false === RgpdAnonymizerType::isValid($type)) {
            throw new RgpdAnonymizerNotFoundException('The anonymizer ' . $type . ' doesn\'t exist.');
        }

        foreach ($this->anonymizers as $anonymizer) {
            if ((new RgpdAnonymizerType($type))->equals($anonymizer->getType())) {
                return $anonymizer;
            }
        }

        throw new RgpdAnonymizerNotFoundException('The anonymizer ' . $type . ' doesn\'t exist.');
    }
}
