<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\RgpdAnonymizer;

use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\RgpdAnonymizer\Exception\UserHasOrdersException;
use Wizacha\Marketplace\RgpdAnonymizer\Exception\UserIdIsNotIntegerException;
use Wizacha\Marketplace\RgpdAnonymizer\Exception\UserIsLastMarketplaceAdminException;
use Wizacha\Marketplace\RgpdAnonymizer\Report\Report;
use Wizacha\Marketplace\RgpdAnonymizer\Report\ReportInterface;
use Wizacha\Marketplace\RgpdAnonymizer\Report\RgpdAnonymizerReportInterface;
use Wizacha\Marketplace\RgpdAnonymizer\Report\Status;
use Wizacha\Marketplace\RgpdAnonymizer\Report\UserReport;
use Wizacha\Marketplace\User\Exception\EmailAlreadyUsed;
use Wizacha\Marketplace\User\UserService;
use Wizacha\Marketplace\User\UserStatus;
use Wizacha\Marketplace\User\UserType;
use Rhumsaa\Uuid\Uuid;

class RgpdUserAnonymizer implements RgpdAnonymizerInterface
{
    /** @var mixed[] */
    protected const USER_TEMPLATE = [
        'email' => 'deleted_user_{id}@deleted.deleted',
        'firstname' => 'deleted',
        'lastname' => 'deleted',
        'user_login' => 'deleted_{id}',
        'birthday' => '',
        'loyaltyIdentifier' => null,
        'phone' => '',
        'fax' => '',
        'url' => '',
        'password1' => '',
        'password2' => '',
        'api_key' => 'deleted_{id}',
        'company' => 'deleted',
        'legal_identifier' => 'deleted',
        'intra_european_community_vat' => 'deleted',
        'job_title' => 'deleted',
        'comment' => 'deleted',
        'external_identifier' => 'deleted',
    ];

    /** @var string[] */
    protected const ADDRESS_TEMPLATE = [
        'firstname' => 'deleted',
        'lastname' => 'deleted',
        'address' => 'deleted',
        'address2' => 'deleted',
        'city' => 'deleted',
        'zipcode' => 'deleted',
        'company' => 'deleted',
        'county' => 'deleted',
        'phone' => '',
        'state' => 'deleted',
    ];

    /** @var int */
    protected $userId;

    /** @var UserService */
    protected $userService;

    /** @var OrderService */
    protected $orderService;

    /** @var UserReport */
    protected $userReport;

    /** @var RgpdDetachUserFromCompany */
    protected $rgpdDetachUserFromCompany;

    public function __construct(
        UserService $userService,
        OrderService $orderService,
        RgpdDetachUserFromCompany $rgpdDetachUserFromCompany
    ) {
        $this->userService = $userService;
        $this->orderService = $orderService;
        $this->rgpdDetachUserFromCompany = $rgpdDetachUserFromCompany;
    }

    public function getType(): RgpdAnonymizerType
    {
        return RgpdAnonymizerType::USER();
    }

    public function process(int $id): ReportInterface
    {
        $this->userId = $id;

        $this->userReport = new UserReport();

        $this->userReport
            ->setUserId($this->userId)
            ->setStatus(Status::NOT_ANONYMIZED())
        ;

        $this
            ->assertUserExist()
            ->assertIsNotLastMpAdmin()
        ;

        try {
            if (true === $this->orderService->userHasOrders($this->userId)) {
                throw new UserHasOrdersException('The user ' . $this->userId . ' has one or more orders');
            }
        } catch (UserHasOrdersException $exception) {
            $this->userReport->addError(
                UserHasOrdersException::class,
                $exception->getMessage()
            );

            throw $exception;
        }

        try {
            $this->userReport->setStatus(Status::ANONYMIZATION_IN_PROGRESS());

            $this->userService->updateUserProfile(
                $this->userId,
                $this->generateParameters(),
                false,
                false
            );

            $this->userService->updateAddresses(
                $this->userId,
                static::ADDRESS_TEMPLATE,
                static::ADDRESS_TEMPLATE
            );

            $this->anonymizePhoneAndAddressFields();
        } catch (EmailAlreadyUsed $exception) {
            $this->userReport->setStatus(Status::ERROR_DURING_ANONIMIZATION());

            $this->userReport->addError(
                EmailAlreadyUsed::class,
                $exception->getMessage()
            );

            throw $exception;
        }


        if (true === $this->assertUserCanBeDetachedFromCompany()) {
            try {
                $this->rgpdDetachUserFromCompany->process($this->userId);
            } catch (\Exception $exception) {
                $this->userReport->setStatus(Status::ERROR_DURING_ANONIMIZATION());

                $this->userReport->addError(\Exception::class, $exception->getMessage());
            }
        }

        $status = (\count($this->userReport->getErrors()) === 0)
            ? Status::ANONYMIZED()
            : Status::ERROR_DURING_ANONIMIZATION()
        ;
        $this->userReport->setStatus($status);

        return $this->userReport;
    }

    /** @return mixed[] */
    protected function generateParameters(): array
    {
        $parameters = static::USER_TEMPLATE;
        $parameters['email'] = $this->generateEmail();
        $parameters['user_login'] = $this->generateUserLogin();
        $parameters['password1'] = $this->generatePassword();
        $parameters['password2'] = $this->generatePassword();
        $parameters['api_key'] = $this->generateUserApiKey();
        $parameters['status'] = UserStatus::INACTIVE()->getValue();
        $parameters['is_locked'] = true;
        $parameters['birthday'] = $this->generateBirthday();
        $parameters['user_uuid'] = Uuid::uuid4()->toString();

        return $parameters;
    }

    protected function generateEmail(): string
    {
        $this->assertUserIdIsSet();

        return 'deleted_user_' . $this->userId . '@deleted.deleted';
    }

    protected function generateUserLogin(): string
    {
        $this->assertUserIdIsSet();

        return 'deleted_' . $this->userId;
    }

    protected function generateUserApiKey(): string
    {
        $this->assertUserIdIsSet();

        return 'deleted_' . $this->userId;
    }

    protected function generatePassword(): string
    {
        $this->assertUserIdIsSet();

        return 'deleted_password_' . $this->userId;
    }

    protected function generateBirthday(): \DateTime
    {
        $this->assertUserIdIsSet();

        return new \DateTime("1900-01-01");
    }

    protected function assertUserExist(): self
    {
        try {
            $this->userService->get($this->userId);
        } catch (NotFound $exception) {
            $this->userReport->addError(
                NotFound::class,
                $exception->getMessage()
            );

            throw $exception;
        }

        return $this;
    }

    protected function assertUserIdIsSet(): self
    {
        try {
            if (false === \is_int($this->userId)) {
                throw new UserIdIsNotIntegerException();
            }
        } catch (UserIdIsNotIntegerException $exception) {
            $this->userReport->setStatus(Status::NOT_ANONYMIZED());

            $this->userReport->addError(
                UserIdIsNotIntegerException::class,
                $exception->getMessage()
            );

            throw $exception;
        }

        return $this;
    }

    protected function assertIsNotLastMpAdmin(): self
    {
        $this->assertUserIdIsSet();

        $user = $this->userService->get($this->userId);

        if ($user->getUserType() !== 'A') {
            return $this;
        }

        $countAdmin = db_get_row("SELECT COUNT(user_id) as count FROM ?:users WHERE user_type='A' AND is_locked = 0");

        if ((int) $countAdmin['count'] <= 1) {
            $this->userReport->addError(
                UserIsLastMarketplaceAdminException::class,
                "The last admin of the marketplace can't be anonymized."
            );
            $this->userReport->setStatus(Status::ERROR_DURING_ANONIMIZATION());

            throw new UserIsLastMarketplaceAdminException();
        }

        return $this;
    }

    protected function anonymizePhoneAndAddressFields(): self
    {
        $this
            ->assertUserIdIsSet()
            ->assertUserExist()
        ;

        // Sadly, I haven't found any other way to update those fields.
        $profileQuery = "UPDATE cscart_user_profiles SET b_address_2 = 'deleted', s_address_2 = 'deleted' WHERE user_id = ?i";
        db_query($profileQuery, $this->userId);

        $phoneQuery = "UPDATE cscart_users SET phone='' WHERE user_id=?i";
        db_query($phoneQuery, $this->userId);

        return $this;
    }

    protected function assertUserCanBeDetachedFromCompany(): bool
    {
        $this
            ->assertUserExist()
            ->assertUserIdIsSet()
        ;

        $user = $this->userService->get($this->userId);

        if (false === (new UserType($user->getUserType()))->equals(UserType::VENDOR())) {
            return false;
        }

        $companyId = $user->getCompanyId();
        $company = new Company($companyId);

        return \count($company->getAdmins() ?? []) > 1;
    }
}
