<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\RgpdAnonymizer;

use Wizacha\Marketplace\RgpdAnonymizer\Report\ReportInterface;

class RgpdAnonymizerService
{
    /** @var int */
    protected $id;

    /** @var string */
    protected $type;

    /** @var RgpdAnonymizerFactory */
    protected $rgpdAnonymizerFactory;

    public function __construct(RgpdAnonymizerFactory $rgpdAnonymizerFactory)
    {
        $this->rgpdAnonymizerFactory = $rgpdAnonymizerFactory;
    }

    public function anonymize(int $id, string $type): ReportInterface
    {
        return $this->rgpdAnonymizerFactory->getAnonymizer($type)->process($id);
    }
}
