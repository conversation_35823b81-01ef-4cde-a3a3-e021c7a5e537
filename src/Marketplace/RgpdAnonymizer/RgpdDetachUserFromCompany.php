<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\RgpdAnonymizer;

use Wizacha\Marketplace\User\UserService;
use Wizacha\Marketplace\User\UserType;

class RgpdDetachUserFromCompany
{
    /** @var int */
    protected $userId;

    /** @var UserService */
    protected $userService;

    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
    }

    public function process(int $id): self
    {
        $this->userId = $id;

        $this->assertUserExist();

        // we need referenced variable
        $_auth = null;

        fn_update_user(
            $this->userId,
            ['user_type' => UserType::CLIENT()->getValue()],
            $_auth,
            false,
            false
        );

        return $this;
    }

    protected function assertUserExist(): self
    {
        $this->userService->get($this->userId);

        return $this;
    }
}
