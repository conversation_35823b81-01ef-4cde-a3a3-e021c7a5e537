<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\RgpdAnonymizer;

use MyCLabs\Enum\Enum;

/**
 * @method static RgpdAnonymizerType USER()
 * @method static RgpdAnonymizerType COMPANY()
 */
class RgpdAnonymizerType extends Enum
{
    protected const USER = 'user';
    protected const COMPANY = 'company';
}
