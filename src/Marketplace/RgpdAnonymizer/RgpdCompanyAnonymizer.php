<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\RgpdAnonymizer;

use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Exception\CompanyNotFound;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\RgpdAnonymizer\Exception\CompanyHasOrdersException;
use Wizacha\Marketplace\RgpdAnonymizer\Exception\CompanyIdIsNotIntegerException;
use Wizacha\Marketplace\RgpdAnonymizer\Report\CompanyReport;
use Wizacha\Marketplace\RgpdAnonymizer\Report\Report;
use Wizacha\Marketplace\RgpdAnonymizer\Report\ReportInterface;
use Wizacha\Marketplace\RgpdAnonymizer\Report\RgpdAnonymizerReportInterface;
use Wizacha\Marketplace\RgpdAnonymizer\Report\Status;
use Wizacha\Marketplace\User\UserService;
use Wizacha\Storage\StorageService;

class RgpdCompanyAnonymizer implements RgpdAnonymizerInterface
{
    /** @var mixed[] */
    protected const TEMPLATE = [
        'company' => '',
        'lang_code' => '',
        'status' => 'D',
        'address' => 'deleted',
        'city' => 'deleted',
        'state' => '',
        'country' => 'FR',
        'zipcode' => 'deleted',
        'email' => 'deleted_company_{id}@deleted.deleted',
        'phone' => '0',
        'fax' => '0',
        'url' => 'deleted',
        'redirect_customer' => null,
        'countries_list' => [],
        'timestamp' => '',
        'categories' => '',
        'shippings' => [],
        'logos' => '',
        'commission' => null,
        'commission_type' => null,
        'request_user_id' => null,
        'request_account_name' => null,
        'request_account_data' => null,
        'pre_moderation' => null,
        'pre_moderation_edit' => null,
        'pre_moderation_edit_vendors' => null,
        'w_rma_address' => 'deleted',
        'w_rma_city' => 'deleted',
        'w_rma_country' => null,
        'w_rma_zipcode' => 'deleted',
        'w_company_type' => null,
        'w_vat_number' => 'deleted',
        'w_siret_number' => 'deleted',
        'w_legal_status' => 'deleted',
        'commission_fixed' => null,
        'commission_percent' => null,
        "w_extras" => [
            "w_capital" => 'deleted',
            "w_RCS" => 'deleted',
        ],
        'mangopay_id' => null,
        'legal_representative_firstname' => 'deleted',
        'legal_representative_lastname' => 'deleted',
        'hipay_id' => null,
        'latitude' => null,
        'longitude' => null,
        'iban' => '***********************',
        'bic' => 'GEBABEBB',
        'stripe_id' => null,
        'initial_billing_number' => null,
        'initial_rma_number' => null,
        'extra' => [],
        'naf_code' => 'erased',
        'company_description' => 'deleted',
        'company_terms' => 'deleted',
        'seo_name' => 'deleted',
        'category_ids' => [],
        'shippings_ids' => [],
        'main_pair' => [],
        'w_company_meta_description' => 'deleted',
        'w_company_meta_keywords' => 'deleted',
        'w_company_meta_title' => 'deleted',
    ];

    /** @var OrderService */
    protected $orderService;

    /** @var CompanyService */
    protected $companyService;

    /** @var int */
    protected $companyId;

    protected ?StorageService $storage;

    /** @var UserService */
    protected $userService;

    /** @var CompanyReport */
    protected $companyReport;

    /** @var RgpdUserAnonymizer */
    protected $rgpdUserAnonymizer;

    public function __construct(
        OrderService $orderService,
        CompanyService $companyService,
        StorageService $storage,
        RgpdUserAnonymizer $rgpdUserAnonymizer,
        CompanyReport $companyReport
    ) {
        $this->orderService = $orderService;
        $this->companyService = $companyService;
        $this->storage = $storage;
        $this->rgpdUserAnonymizer = $rgpdUserAnonymizer;
        $this->companyReport = $companyReport;
    }

    public function getType(): RgpdAnonymizerType
    {
        return RgpdAnonymizerType::COMPANY();
    }

    public function process(int $id): ReportInterface
    {
        $this->companyId = $id;
        $this->companyReport
            ->setCompanyId($this->companyId)
            ->setStatus(Status::NOT_ANONYMIZED())
        ;

        $this->assertCompanyExist();

        try {
            if (true === $this->orderService->companyHasOnlyIncompleteOrNoOrders($this->companyId)) {
                throw new CompanyHasOrdersException('The company ' . $this->companyId . ' has one or more orders.');
            }
        } catch (CompanyHasOrdersException $exception) {
            $this->companyReport->addError(
                CompanyHasOrdersException::class,
                $exception->getMessage()
            );

            throw $exception;
        }

        $this->companyReport->setStatus(Status::ANONYMIZATION_IN_PROGRESS());
        $companyParameters = $this->generateParameters();
        fn_update_company($companyParameters, $this->companyId, 'fr');
        fn_update_company($companyParameters, $this->companyId, 'en');
        $this->anonymizeCompanyVendors();
        $this->removeCompanyImageLink();
        $this->removeCompanyFiles();

        $status = (\count($this->companyReport->getErrors()) === 0)
            ? Status::ANONYMIZED()
            : Status::NOT_ANONYMIZED()
        ;
        $this->companyReport->setStatus($status);

        return $this->companyReport;
    }

    protected function anonymizeCompanyVendors(): self
    {
        $this->assertCompanyIdIsSet();

        $company = new Company($this->companyId);
        $vendors = $company->getAdmins();

        foreach ($vendors as $vendor) {
            $this->companyReport->addUserReport(
                $this->rgpdUserAnonymizer->process($vendor->getId())
            );
        }

        return $this;
    }

    /** @return mixed[] */
    protected function generateParameters(): array
    {
        $parameters = static::TEMPLATE;
        $parameters['email'] = $this->generateEmail();
        $parameters['company'] = $this->generateCompanyName();
        $parameters['timestamp'] = $this->generateTimestamp();

        return $parameters;
    }

    protected function generateEmail(): string
    {
        $this->assertCompanyIdIsSet();

        return "deleted_company_" . $this->companyId . "@deleted.deleted";
    }

    protected function generateTimestamp(): string
    {
        return (string) (new \DateTimeImmutable())->getTimestamp();
    }

    protected function generateCompanyName(): string
    {
        $this->assertCompanyIdIsSet();

        return 'deleted_' . $this->companyId;
    }

    protected function removeCompanyFiles(): self
    {
        $this->assertCompanyIdIsSet();

        $storagePrefix = $this->companyId . '/';
        $this->storage->deleteDir($storagePrefix);

        return $this;
    }

    protected function removeCompanyImageLink(): self
    {
        $this->assertCompanyIdIsSet();

        $query = "SELECT pair_id FROM cscart_images_links WHERE object_id = ?i AND object_type = ?s";
        $results = db_get_row($query, $this->companyId, 'company');

        if (true === \is_array($results) && true === \array_key_exists('pair_id', $results)) {
            fn_delete_image_pair($results['pair_id']);
        }

        return $this;
    }

    protected function assertCompanyIdIsSet(): self
    {
        try {
            if (false === \is_int($this->companyId)) {
                throw new CompanyIdIsNotIntegerException();
            }
        } catch (CompanyIdIsNotIntegerException $exception) {
            $this->companyReport->addError(
                CompanyIdIsNotIntegerException::class,
                $exception->getMessage()
            );

            throw $exception;
        }

        return $this;
    }

    protected function assertCompanyExist(): self
    {
        try {
            $this->companyService->get($this->companyId);
        } catch (CompanyNotFound $exception) {
            $this->companyReport->addError(
                CompanyNotFound::class,
                $exception->getMessage()
            );

            throw $exception;
        }

        return $this;
    }
}
