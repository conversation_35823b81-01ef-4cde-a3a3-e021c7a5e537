<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Cms;

use Wizacha\Marketplace\SeoData;

class Page
{
    /**
     * @var int
     */
    private $id;

    /**
     * @var string
     */
    private $title;

    /**
     * @var string
     */
    private $content;

    /**
     * @var SeoData
     */
    private $seoData;

    public function __construct(
        int $id,
        string $title,
        string $content,
        string $metaTitle,
        string $metaDescription,
        string $metaKeywords,
        string $slug
    ) {
        $this->id = $id;
        $this->title = $title;
        $this->content = $content;
        $this->seoData = new SeoData($metaTitle, $metaDescription, $metaKeywords, $slug);
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    /**
     * This getter will return html content
     */
    public function getContent(): string
    {
        return $this->content;
    }

    public function getSeoData(): SeoData
    {
        return $this->seoData;
    }
}
