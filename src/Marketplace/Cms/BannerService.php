<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Cms;

class BannerService
{
    /**
     * Returns the banners that are currently active and valid for being displayed.
     *
     * @return Banner[]
     */
    public function getCategoriesActiveBanners(int $categoryId, bool $includeMobile = true, bool $includeDesktop = true): array
    {
        list($banners, ) = fn_get_banners([
            'sort_by' => 'position',
            'w_effective' => time(), // filter to return currently active banners
            'display_on_categories' => true,
            'cid' => $categoryId,
            'device' => $includeMobile && $includeDesktop ? 'all' : ($includeMobile ? 'mobile' : 'desktop'),
        ]);

        $banners = array_map(function (array $bannerData) {
            return Banner::fromCsCartData($bannerData);
        }, $banners);

        $banners = array_filter($banners, function (Banner $banner) {
            return $banner->isValid();
        });

        return $banners;
    }

    /**
     * Returns the banners that are currently active and valid for being displayed on mobile
     *
     * @return Banner[]
     */
    public function getCategoriesActiveMobileBanners(int $categoryId): array
    {
        return $this->getCategoriesActiveBanners($categoryId, true, false);
    }

    /**
     * Returns the banners that are currently active and valid for being displayed on desktop
     *
     * @return Banner[]
     */
    public function getCategoriesActiveDesktopBanners(int $categoryId): array
    {
        return $this->getCategoriesActiveBanners($categoryId, false, true);
    }

    /**
     * Returns the banners that are currently active and valid for being displayed.
     *
     * @return Banner[]
     */
    public function getHomepageActiveBanners(bool $includeMobile = true, bool $includeDesktop = true): array
    {
        list($banners, ) = fn_get_banners([
            'sort_by' => 'position',
            'w_effective' => time(), // filter to return currently active banners
            'display_on_homepage' => true,
            'device' => $includeMobile && $includeDesktop ? 'all' : ($includeMobile ? 'mobile' : 'desktop'),
        ]);

        $banners = array_map(function (array $bannerData) {
            return Banner::fromCsCartData($bannerData);
        }, $banners);

        $banners = array_filter($banners, function (Banner $banner) {
            return $banner->isValid();
        });

        return $banners;
    }

    /**
     * Returns the banners that are currently active and valid for being displayed on mobile
     *
     * @return Banner[]
     */
    public function getHomepageActiveMobileBanners(): array
    {
        return $this->getHomepageActiveBanners(true, false);
    }

    /**
     * Returns the banners that are currently active and valid for being displayed on desktop
     *
     * @return Banner[]
     */
    public function getHomepageActiveDesktopBanners(): array
    {
        return $this->getHomepageActiveBanners(false, true);
    }
}
