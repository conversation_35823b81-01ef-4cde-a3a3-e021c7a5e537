<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Cms;

class Menu
{
    /**
     * @var int
     */
    private $id;
    /**
     * @var MenuItem[]
     */
    private $menuElements;
    /**
     * @var string
     */
    private $name;
    /**
     * @var string
     */
    private $status;

    /**
     * @param MenuItem[] $menuElements
     */
    public function __construct(int $id, string $name, string $status, array $menuElements)
    {
        $this->id = $id;
        $this->name = $name;
        $this->status = $status;
        $this->menuElements = $menuElements;
    }

    /**
     * @return MenuItem[]
     */
    public function getItems(): array
    {
        return $this->menuElements;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function isActive(): bool
    {
        return $this->status != 'D';
    }

    public function addItem(MenuItem $item): void
    {
        $this->menuElements[] = $item;
    }
}
