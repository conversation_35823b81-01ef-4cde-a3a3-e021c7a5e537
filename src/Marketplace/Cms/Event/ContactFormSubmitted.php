<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Cms\Event;

use Symfony\Contracts\EventDispatcher\Event;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Wizacha\Component\Notification\NotificationEvent;

class ContactFormSubmitted extends Event implements NotificationEvent
{
    /**
     * @var string
     */
    private $subject;

    /**
     * @var string
     */
    private $receiver;

    /**
     * @var array
     */
    private $elements;

    public function __construct(string $subject, string $receiver, array $elements)
    {
        $this->subject = $subject;
        $this->receiver = $receiver;
        $this->elements = $elements;
    }

    public static function buildDebugForm(FormBuilder $form)
    {
        $form->add('subject', TextType::class);
        $form->add('message', TextareaType::class);
    }

    public static function createFromForm(Form $form)
    {
        return new static(
            $form->getData()['subject'],
            '<EMAIL>',
            [['element_type' => FORM_TEXTAREA, 'description' => 'Message', 'value' => $form->getData()['message']]]
        );
    }

    public static function getDescription(): string
    {
        return 'contact_form_submitted';
    }

    public function getSubject(): string
    {
        return $this->subject;
    }

    public function getReceiver(): string
    {
        return $this->receiver;
    }

    public function getElements(): array
    {
        return $this->elements;
    }
}
