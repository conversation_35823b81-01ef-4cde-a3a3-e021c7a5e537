<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Cms;

class MenuItem
{
    /**
     * @var int
     */
    private $menuId;
    /**
     * @var string
     */
    private $name;
    /**
     * @var string
     */
    private $url;
    /**
     * @var string
     */
    private $status;
    /**
     * @var int
     */
    private $position;
    /**
     * @var int
     */
    private $parent;
    /**
     * @var MenuItem[]
     */
    private $items = [];
    /**
     * @var bool
     */
    private $targetBlank;

    public function __construct(
        int $menuId,
        string $name,
        string $url,
        string $status,
        bool $targetBlank = false,
        int $position = 0,
        int $parent = 0
    ) {
        $this->menuId = $menuId;
        $this->name = $name;
        $this->url = $url;
        $this->status = $status;
        $this->targetBlank = $targetBlank;
        $this->position = $position;
        $this->parent = $parent;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getUrl(): string
    {
        return $this->url;
    }

    public function isActive(): bool
    {
        return $this->status != 'D';
    }

    public function getPosition(): int
    {
        return $this->position;
    }

    /**
     * @return MenuItem[]
     */
    public function getItems(): array
    {
        return $this->items;
    }

    public function addItem(MenuItem $item): void
    {
        $this->items[] = $item;
    }

    public function getMenuId(): int
    {
        return $this->menuId;
    }

    public function getParent(): int
    {
        return $this->parent;
    }

    public function hasChildren(): bool
    {
        return \count($this->items) > 0;
    }

    public function isTargetBlank(): bool
    {
        return $this->targetBlank;
    }
}
