<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Cms;

use Doctrine\DBAL\Connection;
use Wizacha\Marketplace\GlobalState\GlobalState;

class MenuService
{
    /**
     * @var Connection
     */
    private $db;

    public function __construct(Connection $db)
    {
        $this->db = $db;
    }

    /**
     * @return Menu[]
     */
    public function getActiveMenus(): array
    {
        $request = <<<SQL
SELECT m.menu_id AS menu_id,
    m.status AS menu_status,
    m.target_blank AS target_blank,
    md.name AS menu_name,
    sd.status AS element_status,
    sd.param AS element_url,
    sd.position AS element_position,
    sdd.descr AS element_name,
    sd.param_id AS element_id,
    sd.parent_id AS element_parent
FROM cscart_menus AS m
INNER JOIN cscart_menus_descriptions AS md ON m.menu_id = md.menu_id AND md.lang_code = :lang
INNER JOIN cscart_static_data AS sd ON sd.param_5 = m.menu_id AND sd.status = 'A'
INNER JOIN cscart_static_data_descriptions AS sdd ON sdd.param_id = sd.param_id AND sdd.lang_code = :lang
WHERE m.status = 'A'
ORDER BY sd.param_5, sd.position
SQL;
        $sqlMenus = $this->db->fetchAll($request, ['lang' => (string) GlobalState::interfaceLocale()]);

        $menus = [];
        $items = [];

        foreach ($sqlMenus as $data) {
            if (!isset($menus[$data['menu_id']])) {
                $menus[$data['menu_id']] = new Menu(
                    \intval($data['menu_id']),
                    $data['menu_name'],
                    $data['menu_status'],
                    []
                );
            }

            $items[$data['element_id']] = new MenuItem(
                \intval($data['menu_id']),
                $data['element_name'],
                fn_url($data['element_url'], 'C', 'https'),
                $data['element_status'],
                (bool) $data['target_blank'],
                \intval($data['element_position']),
                \intval($data['element_parent'])
            );
        }

        foreach ($items as $itemId => $menuItem) {
            if ($menuItem->getParent() === 0) {
                $menus[$menuItem->getMenuId()]->addItem($menuItem);
            } else {
                $items[$menuItem->getParent()]->addItem($menuItem);
            }
        }

        return $menus;
    }
}
