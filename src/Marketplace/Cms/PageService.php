<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Cms;

use Doctrine\DBAL\Connection;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Status;

class PageService
{
    /**
     * @var Connection
     */
    private $db;

    /**
     * @var bool
     */
    private $hideLegacyPages;

    public function __construct(Connection $db, bool $hideLegacyPages)
    {
        $this->db = $db;
        $this->hideLegacyPages = $hideLegacyPages;
    }

    public function getPage(int $pageId, $locale = null): Page
    {
        $locale = $locale ?? (string) GlobalState::interfaceLocale();
        $pageTypeCondition = $this->hideLegacyPages ? 'AND p.page_type = :pageType' : '';

        $request = <<<SQL
SELECT d.page_id AS page_id,
    d.page AS page,
    d.description AS description,
    d.page_title AS page_title,
    d.meta_description AS meta_description,
    d.meta_keywords AS meta_keywords,
    sn.name AS slug,
    p.status AS status
FROM cscart_page_descriptions AS d
INNER JOIN cscart_pages AS p ON d.page_id = p.page_id
INNER JOIN cscart_seo_names AS sn ON CAST(d.page_id AS CHAR) = sn.object_id
WHERE d.page_id = :pageId AND p.status IN(:status) AND d.lang_code = :locale
AND sn.type = :type
$pageTypeCondition
SQL;
        $sqlPage = $this->db->fetchAssoc(
            $request,
            [
                'pageId' => $pageId,
                'status' => [Status::ENABLED, Status::HIDDEN],
                'locale' => $locale,
                'type' => 'a',
                'pageType' => PAGE_TYPE_TEXT,
            ],
            [
                'status' => Connection::PARAM_STR_ARRAY,
            ]
        );
        if (empty($sqlPage)) {
            throw new NotFound('page not found');
        }
        $page = $this->newPage($sqlPage);

        return $page;
    }

    /**
     * @return Page[]
     */
    public function list($locale = null): array
    {
        $locale = $locale ?? (string) GlobalState::interfaceLocale();
        $pageTypeCondition = $this->hideLegacyPages ? 'AND p.page_type = :pageType' : '';

        $request = <<<SQL
SELECT d.page_id AS page_id,
    d.page AS page,
    d.description AS description,
    d.page_title AS page_title,
    d.meta_description AS meta_description,
    d.meta_keywords AS meta_keywords,
    sn.name AS slug,
    p.status AS status
FROM cscart_page_descriptions AS d
INNER JOIN cscart_pages AS p ON d.page_id = p.page_id
INNER JOIN cscart_seo_names AS sn ON CAST(d.page_id AS CHAR) = sn.object_id
WHERE p.status = :status AND d.lang_code = :locale
AND sn.type = :type
$pageTypeCondition
ORDER BY page_id
SQL;
        $sqlPages = $this->db->fetchAll($request, [
            'status' => Status::ENABLED,
            'locale' => $locale,
            'type' => 'a',
            'pageType' => PAGE_TYPE_TEXT,
        ]);

        return array_map(function (array $sqlPage): Page {
            return $this->newPage($sqlPage);
        }, $sqlPages);
    }

    private function newPage(array $sqlPage): Page
    {
        return new Page(
            (int) $sqlPage['page_id'],
            $sqlPage['page'],
            $sqlPage['description'] ?? '',
            $sqlPage['page_title'],
            $sqlPage['meta_description'],
            $sqlPage['meta_keywords'],
            $sqlPage['slug']
        );
    }
}
