<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Cms;

use Wizacha\Marketplace\Image\Image;

/**
 * Marketing banner.
 */
class Banner
{
    /**
     * @var Image
     */
    private $image;

    /**
     * @var string|null
     */
    private $link;

    /**
     * @var bool
     */
    private $targetBlank = false;

    /**
     * @var string|null
     */
    private $name;

    /**
     * @var string|null
     */
    private $altImage;

    public static function fromCsCartData(array $data): self
    {
        $banner = new self();
        $banner->image = isset($data['main_pair']) ? Image::fromCsCartData($data['main_pair']) : null;
        $banner->altImage = isset($banner->image) ? Image::fromCsCartDataAltImage($data['main_pair']) : null;
        $banner->link = $data['url'];
        $banner->targetBlank = $data['target'] === 'B';
        $banner->name = $data['banner'];

        return $banner;
    }

    public function __construct(string $name = null, Image $image = null, string $link = null, bool $shouldOpenInNewWindow = false, $altImage = null)
    {
        $this->image = $image;
        $this->link = $link;
        $this->targetBlank = $shouldOpenInNewWindow;
        $this->name = $name;
        $this->altImage = $altImage;
    }

    /**
     * Whether the banner is valid for being displayed.
     */
    public function isValid(): bool
    {
        // The banner must have an image
        return $this->image !== null;
    }

    public function getImage(): ?Image
    {
        return $this->image;
    }

    public function hasLink(): bool
    {
        return !empty($this->link);
    }

    public function getLink(): ?string
    {
        return $this->link;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function getAltImage(): ?string
    {
        return $this->altImage;
    }

    public function shouldOpenInNewWindow(): bool
    {
        return $this->targetBlank;
    }
}
