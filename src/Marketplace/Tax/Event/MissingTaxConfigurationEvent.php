<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Tax\Event;

use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Symfony\Contracts\EventDispatcher\Event;
use Wizacha\Component\Notification\NotificationEvent;

class MissingTaxConfigurationEvent extends Event implements NotificationEvent
{
    private string $orderId;

    public function __construct(string $orderId)
    {
        $this->orderId = $orderId;
    }

    public function getOrderId(): string
    {
        return $this->orderId;
    }

    public static function buildDebugForm(FormBuilder $form)
    {
        $form->add('orderId', IntegerType::class);
    }

    public static function createFromForm(Form $form)
    {
        return new static((string) $form->getData()['orderId']);
    }

    public static function getDescription(): string
    {
        return 'missing_tax_configuration_subject';
    }
}
