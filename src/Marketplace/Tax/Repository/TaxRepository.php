<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Tax\Repository;

use Doctrine\ORM\EntityManagerInterface;
use Wizacha\Marketplace\Tax\Exception\TaxNotFoundException;

// Repository to query on the legacy tax table
class TaxRepository
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function setCommissionTax(string $taxId): void
    {
        $connexion = $this->entityManager->getConnection();
        $query = $connexion->prepare('SELECT tax_id FROM cscart_taxes WHERE tax_id = :taxId');
        $query->execute(['taxId' => $taxId]);
        $tax = $query->fetchAllAssociative();

        if (\count($tax) > 0) {
            $connexion->beginTransaction();

            try {
                $connexion->executeQuery('UPDATE cscart_taxes SET selected_as_commission = 0');
                $query = $connexion
                    ->prepare('UPDATE cscart_taxes SET selected_as_commission = 1 WHERE tax_id = :taxId');
                $query->execute(['taxId' => $taxId]);

                $connexion->commit();
            } catch (\Exception $e) {
                $connexion->rollback();
                throw $e;
            }
        } else {
            throw new TaxNotFoundException();
        }
    }

    public function getCommissionTaxId(): ?int
    {
        $connexion = $this->entityManager->getConnection();
        $query = $connexion->prepare("SELECT tax_id FROM cscart_taxes WHERE selected_as_commission = 1");
        $query->execute();

        $result = $query->fetchOne();
        return $result === false ? null : (int) $result;
    }
}
