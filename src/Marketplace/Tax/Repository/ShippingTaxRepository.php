<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Tax\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Tax\Entity\ShippingTax;

class ShippingTaxRepository extends ServiceEntityRepository
{
    /** @return string[] */
    public function getWithTaxDetails(int $itemsPerPage = 15, int $page = 1): array
    {
        $request = $this->getEntityManager()->getConnection()->prepare(
            'SELECT shipping_tax.country_code, shipping_tax.country_code, shipping_tax.tax_id, countries.country
                   FROM doctrine_shipping_tax shipping_tax
              LEFT JOIN cscart_country_descriptions countries
                     ON shipping_tax.country_code = countries.code AND lang_code = :lang
               ORDER BY shipping_tax.country_code
                  LIMIT :lowLimit, :highLimit'
        );
        $request->bindValue(':lang', GlobalState::contentLocale(), \PDO::PARAM_STR);
        $request->bindValue(':lowLimit', ($page - 1) * $itemsPerPage, \PDO::PARAM_INT);
        $request->bindValue(':highLimit', $page * $itemsPerPage, \PDO::PARAM_INT);
        $request->execute();

        return $request->fetchAll();
    }

    /** @return string[] */
    public function getCountriesWithoutTax(): array
    {
        $request = $this->getEntityManager()->getConnection()->prepare(
            "SELECT countries.code as value, descriptions.country as label
                   FROM cscart_countries as countries
              LEFT JOIN cscart_country_descriptions as descriptions
                     ON descriptions.code = countries.code AND descriptions.lang_code = :lang
              LEFT JOIN doctrine_shipping_tax shipping_tax
                     ON shipping_tax.country_code = countries.code
                  WHERE countries.status = 'A'
                    AND shipping_tax.tax_id IS NULL
               ORDER BY descriptions.country"
        );
        $request->bindValue(':lang', GlobalState::contentLocale(), \PDO::PARAM_STR);
        $request->execute();

        return $request->fetchAll();
    }

    public function save(ShippingTax $shippingTax): void
    {
        $this->getEntityManager()->persist($shippingTax);
        $this->getEntityManager()->flush();
    }

    public function delete(ShippingTax $shippingTax): void
    {
        $this->getEntityManager()->remove($shippingTax);
        $this->getEntityManager()->flush();
    }
}
