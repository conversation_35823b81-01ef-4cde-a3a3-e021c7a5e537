<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Tax\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * A tax for a country
 *
 * @ORM\Table(name="shipping_tax")
 * @ORM\Entity(repositoryClass="Wizacha\Marketplace\Tax\Repository\ShippingTaxRepository")
 */
class ShippingTax
{
    /**
     * @ORM\Id
     * @ORM\Column(name="country_code", type="string")
     * @ORM\GeneratedValue(strategy="NONE")
     */
    private string $countryCode;

    /** @ORM\Column(name="tax_id", type="integer") */
    private int $taxId;

    /** @ORM\Column(name="created_at", type="datetime") */
    private \DateTime $createdAt;

    /** @ORM\Column(name="updated_at", type="datetime", nullable = true) */
    private ?\DateTime $updatedAt;

    public function __construct()
    {
        $this->createdAt = new \DateTime('NOW');
    }

    public function getTaxId(): int
    {
        return $this->taxId;
    }

    public function setTaxId(int $taxId): self
    {
        $this->taxId = $taxId;

        return $this;
    }

    public function setCountryCode(string $countryCode): self
    {
        $this->countryCode = $countryCode;

        return $this;
    }

    public function setUpdatedAt(\DateTime $updatedAt): self
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    public function getCountryCode(): string
    {
        return $this->countryCode;
    }
}
