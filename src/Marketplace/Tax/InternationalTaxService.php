<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Tax;

use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Tygh\Registry;
use Wizacha\Marketplace\Tax\Entity\ShippingTax;
use Wizacha\Marketplace\Tax\Enum\TaxParameterEnum;
use Wizacha\Marketplace\Tax\Event\MissingTaxConfigurationEvent;
use Wizacha\Marketplace\Tax\Exception\TaxNotFoundException;
use Wizacha\Marketplace\Tax\Repository\ShippingTaxRepository;
use Wizacha\Marketplace\Tax\Repository\TaxRepository;
use Wizacha\Marketplace\User\User;

class InternationalTaxService
{
    private TaxRepository $taxRepository;
    private ShippingTaxRepository $shippingTaxRepository;
    private EventDispatcherInterface $eventDispatcher;
    private string $taxMode;

    public function __construct(
        TaxRepository $taxRepository,
        ShippingTaxRepository $shippingTaxRepository,
        EventDispatcherInterface $eventDispatcher,
        string $taxMode
    ) {
        $this->taxRepository = $taxRepository;
        $this->shippingTaxRepository = $shippingTaxRepository;
        $this->taxMode = $taxMode;
        $this->eventDispatcher = $eventDispatcher;
    }

    /**
     * @param string[] $params
     * @return string[]
     */
    public function get(array $params): array
    {
        $page = \array_key_exists('page', $params) === true
            ? (int) $params['page']
            : 1;
        $itemsPerPage = \array_key_exists('items_per_page', $params) === true
            ? (int) $params['items_per_page']
            : (int) Registry::get('settings.Appearance.admin_elements_per_page');

        return $this->shippingTaxRepository->getWithTaxDetails($itemsPerPage, $page);
    }

    public function getOneByCountryCode(string $countryCode): ?ShippingTax
    {
        return $this->shippingTaxRepository->findOneBy(['countryCode' => $countryCode ?? '']);
    }

    /** @return string[] */
    public function getCountriesWithoutTax(): array
    {
        return $this->shippingTaxRepository->getCountriesWithoutTax();
    }

    public function count(): int
    {
        return $this->shippingTaxRepository->count([]);
    }

    public function setCommissionTax(string $taxId): void
    {
        $this->taxRepository->setCommissionTax($taxId);
    }

    public function updateShippingTax(int $taxId, string $countryCode): ShippingTax
    {
        $shippingTax = $this->shippingTaxRepository->find($countryCode);
        if ($shippingTax === null) {
            $shippingTax = new ShippingTax();
        }

        $shippingTax->setTaxId($taxId)
            ->setCountryCode($countryCode)
            ->setUpdatedAt(new \DateTime('NOW'));

        $this->shippingTaxRepository->save($shippingTax);
        return $shippingTax;
    }

    public function deleteShippingTax(string $countryCode): void
    {
        $shippingTax = $this->shippingTaxRepository->find($countryCode);
        if (null !== $shippingTax) {
            $this->shippingTaxRepository->delete($shippingTax);
        } else {
            throw new TaxNotFoundException();
        }
    }

    public function getTaxId(string $companyCountry, string $companyVatNumber, ?User $user): string
    {
        if (true === $this->isTransnational($user, $companyVatNumber, $companyCountry)) {
            return '0';
        }

        $shippingTax = $this->getOneByCountryCode($companyCountry);
        if (false === \is_null($shippingTax)) {
            $shippingTaxId = \strval($shippingTax->getTaxId());
        } else {
            // cas ou il n'y a pas de taxe paramétré pour le pays.
            $shippingTaxId = \Wizacha\Tax::getFullRateId();
        }

        return $shippingTaxId;
    }

    public function isTransnational(?User $user, string $companyVat, string $companyCountry, bool $isEdp = false): bool
    {
        if (null === $user || $this->taxMode !== TaxParameterEnum::EU_B2B()->getValue()) {
            return false;
        }

        $country = $isEdp === true
            ? $user->getBillingAddress()->getFieldValue('country')
            : $user->getShippingAddress()->getFieldValue('country');

        if ($user->getIntraEuropeanCommunityVAT() !== ''
            && $companyVat !== ''
            && $country !== $companyCountry
        ) {
            return true;
        }

        return false;
    }

    public function sendMissingTaxConfigurationEmail(string $orderId): void
    {
        $this->eventDispatcher->dispatch(new MissingTaxConfigurationEvent($orderId));
    }
}
