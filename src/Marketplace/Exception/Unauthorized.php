<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Exception;

/**
 * An action is unauthorized.
 */
class Unauthorized extends \Exception
{
    public static function fromId(string $name, $id, $reason = ''): self
    {
        $message = sprintf('%s "%s" unauthorized', $name, $id);

        if (!empty($reason)) {
            $message .= ' : ' . $reason;
        }

        return new self($message);
    }
}
