<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Exception;

/**
 * An entity already exists.
 */
class AlreadyExists extends \Exception
{
    public static function withFields(string $entity, array $fields): self
    {
        $message = sprintf("Entity '%s' already exists", $entity);
        foreach ($fields as $name => $value) {
            $message .= sprintf(" - '%s' : %s", $name, $value);
        }

        return new self($message);
    }
}
