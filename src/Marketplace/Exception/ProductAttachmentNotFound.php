<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Exception;

use Symfony\Component\HttpFoundation\Response;
use Wizacha\AppBundle\Controller\Api\Error\ApiErrorResponseProvider;
use Wizacha\Marketplace\Exception\ErrorCode;
use Wizacha\AppBundle\Controller\Api\Error\ErrorResponse;

class ProductAttachmentNotFound extends NotFound implements ApiErrorResponseProvider
{
    /**
     * @var string
     */
    private $attachmentId;

    public function __construct(string $attachmentId)
    {
        parent::__construct('product attachment not found', ErrorCode::PRODUCT_ATTACHMENT_NOT_FOUND()->getValue());
        $this->attachmentId = $attachmentId;
    }

    public function toApiErrorResponse(): ErrorResponse
    {
        return new ErrorResponse(
            ErrorCode::PRODUCT_ATTACHMENT_NOT_FOUND(),
            'product attachment not found',
            [
                'attachmentId' => $this->attachmentId,
            ],
            Response::HTTP_NOT_FOUND
        );
    }
}
