<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Exception;

use Symfony\Component\HttpFoundation\Response;
use Wizacha\AppBundle\Controller\Api\Error\ApiErrorResponseProvider;
use Wizacha\Marketplace\Exception\ErrorCode;
use Wizacha\AppBundle\Controller\Api\Error\ErrorResponse;

class CompanyNotFound extends NotFound implements ApiErrorResponseProvider
{
    /** @var int */
    private $companyId;

    /** @var string */
    private $companySlug;

    private function __construct()
    {
        parent::__construct('company not found');
    }

    public static function fromCompanyId(int $companyId): self
    {
        $e = new self();
        $e->companyId = $companyId;

        return $e;
    }

    public static function fromCompanySlug(string $slug): self
    {
        $e = new self();
        $e->companySlug = $slug;

        return $e;
    }

    public function toApiErrorResponse(): ErrorResponse
    {
        $context = [];
        if (isset($this->companyId)) {
            $context['companyId'] = $this->companyId;
        }
        if (isset($this->companySlug)) {
            $context['companySlug'] = $this->companySlug;
        }

        return new ErrorResponse(ErrorCode::COMPANY_NOT_FOUND(), 'company not found', $context, Response::HTTP_NOT_FOUND);
    }
}
