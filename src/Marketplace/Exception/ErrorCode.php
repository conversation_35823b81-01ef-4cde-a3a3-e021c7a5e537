<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Exception;

use MyCL<PERSON>s\Enum\Enum;

/**
 * @method static ErrorCode BASKET_NOT_FOUND()
 * @method static ErrorCode COUPON_CODE_DOES_NOT_APPLY()
 * @method static ErrorCode COUPON_CODE_ALREADY_APPLIED()
 * @method static ErrorCode PRODUCT_NOT_FOUND()
 * @method static ErrorCode REVIEWS_ARE_DISABLED()
 * @method static ErrorCode SENDER_IS_ALSO_RECIPIENT()
 * @method static ErrorCode COMPANY_HAS_NO_ADMINISTRATOR()
 * @method static ErrorCode COMPANY_NOT_FOUND()
 * @method static ErrorCode FAVORITE_ALREADY_EXISTS()
 * @method static ErrorCode BASKET_IS_EMPTY()
 * @method static ErrorCode DECLINATION_IS_NOT_ACTIVE()
 * @method static ErrorCode PRODUCT_ATTACHMENT_NOT_FOUND()
 * @method static ErrorCode DISCUSSION_NOT_FOUND()
 * @method static ErrorCode ORDER_NOT_FOUND()
 * @method static ErrorCode PROMOTION_NOT_FOUND()
 * @method static ErrorCode INVALID_PROMOTION_RULE()
 * @method static ErrorCode INVALID_HAND_DELIVERY_CODE()
 * @method static ErrorCode USER_CANNOT_REVIEW_PRODUCT()
 * @method static ErrorCode ORDER_NOT_CANCELLABLE()
 * @method static ErrorCode INVALID_REFUND_REQUEST()
 * @method static ErrorCode REFUND_NOT_FOUND()
 * @method static ErrorCode FEATURE_NOT_IMPLEMENTED()
 * @method static ErrorCode UNAVAILABLE_PAYMENT()
 * @method static ErrorCode INVOICE_NOT_FOUND()
 * @method static ErrorCode USER_EMAIL_NOT_FOUND()
 * @method static ErrorCode INVALID_GROUP_ID()
 * @method static ErrorCode INVALID_DECLINATION_ID()
 * @method static ErrorCode INVALID_COMBINATION()
 * @method static ErrorCode INVALID_SHIPPING_ID()
 * @method static ErrorCode CANNOT_DELETE_OPTION_VARIANT()
 */
class ErrorCode extends Enum implements \JsonSerializable
{
    protected const BASKET_NOT_FOUND = 1;
    protected const COUPON_CODE_DOES_NOT_APPLY = 2;
    protected const COUPON_CODE_ALREADY_APPLIED = 3;
    protected const PRODUCT_NOT_FOUND = 4;
    protected const REVIEWS_ARE_DISABLED = 5;
    protected const SENDER_IS_ALSO_RECIPIENT = 6;
    protected const COMPANY_HAS_NO_ADMINISTRATOR = 7;
    protected const COMPANY_NOT_FOUND = 8;
    protected const FAVORITE_ALREADY_EXISTS = 9;
    protected const BASKET_IS_EMPTY = 10;
    protected const DECLINATION_IS_NOT_ACTIVE = 11;
    protected const PRODUCT_ATTACHMENT_NOT_FOUND = 12;
    protected const DISCUSSION_NOT_FOUND = 13;
    protected const ORDER_NOT_FOUND = 14;
    protected const PROMOTION_NOT_FOUND = 15;
    protected const INVALID_PROMOTION_RULE = 16;
    protected const INVALID_HAND_DELIVERY_CODE = 17;
    protected const USER_CANNOT_REVIEW_PRODUCT = 18;
    protected const ORDER_NOT_CANCELLABLE = 19;
    protected const INVALID_REFUND_REQUEST = 20;
    protected const REFUND_NOT_FOUND = 21;
    protected const FEATURE_NOT_IMPLEMENTED = 22;
    protected const UNAVAILABLE_PAYMENT = 23;
    protected const INVOICE_NOT_FOUND = 24;
    protected const USER_EMAIL_NOT_FOUND = 25;
    protected const INVALID_GROUP_ID = 26;
    protected const INVALID_DECLINATION_ID = 27;
    protected const INVALID_COMBINATION = 28;
    protected const INVALID_SHIPPING_ID = 29;
    protected const CANNOT_DELETE_OPTION_VARIANT = 30;

    /**
     * @inheritdoc
     */
    public function jsonSerialize(): int
    {
        return (int) $this->getValue();
    }
}
