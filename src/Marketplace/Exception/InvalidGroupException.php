<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Exception;

class InvalidGroupException extends \Exception implements MarketplaceExceptionInterface
{
    protected int $groupId;
    private ErrorCode $errorCode;

    public function __construct(int $groupId)
    {
        $this->errorCode = ErrorCode::INVALID_GROUP_ID();

        $this->groupId = $groupId;
        parent::__construct(
            \sprintf(
                'Group #%d is not valid.',
                $this->groupId
            ),
            $this->errorCode->getValue()
        );
    }

    public function getContext(): array
    {
        return [
            'groupId' => $this->groupId,
        ];
    }

    public function getErrorCode(): ErrorCode
    {
        return $this->errorCode;
    }
}
