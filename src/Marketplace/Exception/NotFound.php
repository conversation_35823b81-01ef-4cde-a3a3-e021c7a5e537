<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Exception;

/**
 * An entity was not found.
 */
class NotFound extends \Exception
{
    /**
     * @deprecated please try using more precise exceptions, like \Wizacha\Marketplace\Exception\ProductNotFound, \Wizacha\Marketplace\Exception\CompanyNotFound, etc.
     */
    public static function fromId(string $name, $id): self
    {
        return new self(sprintf('%s "%s" not found', $name, $id));
    }
}
