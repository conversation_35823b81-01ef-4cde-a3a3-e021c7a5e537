<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Exception;

use Symfony\Component\HttpFoundation\Response;
use Wizacha\AppBundle\Controller\Api\Error\ApiErrorResponseProvider;
use Wizacha\Marketplace\Exception\ErrorCode;
use Wizacha\AppBundle\Controller\Api\Error\ErrorResponse;

class DiscussionNotFound extends NotFound implements ApiErrorResponseProvider
{
    /** @var int */
    private $discussionId;

    public function __construct(int $discussionId)
    {
        parent::__construct('discussion not found', ErrorCode::DISCUSSION_NOT_FOUND()->getValue());
        $this->discussionId = $discussionId;
    }

    public function toApiErrorResponse(): ErrorResponse
    {
        return new ErrorResponse(
            ErrorCode::DISCUSSION_NOT_FOUND(),
            'discussion not found',
            [
                'discussionId' => $this->discussionId,
            ],
            Response::HTTP_NOT_FOUND
        );
    }
}
