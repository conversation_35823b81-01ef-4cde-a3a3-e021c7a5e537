<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Exception;

class ImportAlreadyExistsException extends \Exception
{
    /** @var string[] */
    private array $pendingJobIds;

    /**
     * @param string[] $pendingJobIds
     * @param string|null $message
     */
    public function __construct(array $pendingJobIds, string $message = null)
    {
        $this->pendingJobIds = $pendingJobIds;
        $message = $message ?? __('exim_error_import_already_exist');

        parent::__construct($message);
    }

    /**
     * @return string[]
     */
    public function getPendingJobIds(): array
    {
        return $this->pendingJobIds;
    }
}
