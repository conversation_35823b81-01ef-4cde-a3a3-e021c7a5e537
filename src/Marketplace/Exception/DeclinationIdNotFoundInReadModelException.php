<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Exception;

class DeclinationIdNotFoundInReadModelException extends \Exception implements MarketplaceExceptionInterface
{
    protected string $declinationId;
    private ErrorCode $errorCode;

    public function __construct(string $declinationId)
    {
        $this->errorCode = ErrorCode::INVALID_DECLINATION_ID();

        parent::__construct('DeclinationId not found in readModel', $this->errorCode->getValue());

        $this->declinationId = $declinationId;
    }

    public function getContext(): array
    {
        return ['declinationId' => $this->declinationId];
    }

    public function getErrorCode(): ErrorCode
    {
        return $this->errorCode;
    }
}
