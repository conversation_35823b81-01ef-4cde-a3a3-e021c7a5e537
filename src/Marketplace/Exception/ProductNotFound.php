<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Exception;

use Symfony\Component\HttpFoundation\Response;
use Wizacha\AppBundle\Controller\Api\Error\ApiErrorResponseProvider;
use Wizacha\Marketplace\Exception\ErrorCode;
use Wizacha\AppBundle\Controller\Api\Error\ErrorResponse;

class ProductNotFound extends NotFound implements ApiErrorResponseProvider
{
    /**
     * @var int|string
     */
    private $productId;

    /**
     * @param int|string $productId
     */
    public function __construct($productId)
    {
        parent::__construct(sprintf('Product "%s" not found', $productId), ErrorCode::PRODUCT_NOT_FOUND()->getValue());
        $this->productId = (string) $productId;
    }

    public function getProductId(): string
    {
        return $this->productId;
    }

    public function toApiErrorResponse(): ErrorResponse
    {
        return new ErrorResponse(ErrorCode::PRODUCT_NOT_FOUND(), 'product not found', ['product_id' => $this->getProductId()], Response::HTTP_NOT_FOUND);
    }
}
