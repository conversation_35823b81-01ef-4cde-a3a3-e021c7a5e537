<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Exception;

class InvalidShippingException extends \Exception implements MarketplaceExceptionInterface
{
    protected int $shippingId;
    private ErrorCode $errorCode;

    public function __construct(int $shippingId)
    {
        $this->errorCode = ErrorCode::INVALID_SHIPPING_ID();

        $this->shippingId = $shippingId;
        parent::__construct(
            \sprintf(
                'Shipping #%d is not valid.',
                $this->shippingId
            ),
            $this->errorCode->getValue()
        );
    }

    public function getContext(): array
    {
        return [
            'shippingId' => $this->shippingId,
        ];
    }

    public function getErrorCode(): ErrorCode
    {
        return $this->errorCode;
    }
}
