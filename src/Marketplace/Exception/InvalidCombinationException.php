<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Exception;

class InvalidCombinationException extends \Exception implements MarketplaceExceptionInterface
{
    protected int $productId;
    protected array $productOptions;
    private ErrorCode $errorCode;

    public function __construct(int $productId, array $productOptions)
    {
        $this->errorCode = ErrorCode::INVALID_COMBINATION();

        parent::__construct('Invalid combination', $this->errorCode->getValue());

        $this->productId = $productId;
        $this->productOptions = $productOptions;
    }

    public function getContext(): array
    {
        return [
            'productId' => $this->productId,
            'productOptions' => $this->productOptions,
        ];
    }

    public function getErrorCode(): ErrorCode
    {
        return $this->errorCode;
    }
}
