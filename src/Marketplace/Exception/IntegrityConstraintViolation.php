<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Exception;

/**
 * A wrong parameter is passed
 */
class IntegrityConstraintViolation extends \Exception
{
    public const DEFAULT = 1;
    public const MISSING = 2;
    public const INVALID = 3;

    protected $errors;

    public function __construct(string $message = '', array $errors = [])
    {
        $message = $message ?? static::getDedicatedMessage(static::DEFAULT);
        $this->errors = $errors;

        parent::__construct($message);
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    public static function isMissing(string $field): self
    {
        return static::onOneField($field, self::MISSING);
    }

    /**
     * @param string[] $fields
     */
    public static function areMissing(array $fields): self
    {
        return new self(sprintf("Missing required field(s) : %s", implode(', ', $fields)));
    }

    public static function isInvalid(string $field): self
    {
        return static::onOneField($field, self::INVALID);
    }

    public static function onOneField(string $field, int $type): self
    {
        return new self(sprintf("%s : %s", static::getDedicatedMessage($type), $field));
    }

    protected static function getDedicatedMessage(int $type): string
    {
        switch ($type) {
            case static::MISSING:
                return "Missing required field";

            case static::INVALID:
                return "Invalid value in field";

            default:
                return "Integrity constraint violation";
        }
    }
}
