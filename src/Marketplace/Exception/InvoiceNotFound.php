<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Exception;

use Symfony\Component\HttpFoundation\Response;
use Wizacha\AppBundle\Controller\Api\Error\ApiErrorResponseProvider;
use Wizacha\Marketplace\Exception\ErrorCode;
use Wizacha\AppBundle\Controller\Api\Error\ErrorResponse;

class InvoiceNotFound extends NotFound implements ApiErrorResponseProvider
{
    private int $orderId;

    public function __construct(int $orderId)
    {
        parent::__construct('Invoice not found', ErrorCode::INVOICE_NOT_FOUND()->getValue());
        $this->orderId = $orderId;
    }

    public function toApiErrorResponse(): ErrorResponse
    {
        return new ErrorResponse(
            ErrorCode::INVOICE_NOT_FOUND(),
            'Invoice not found',
            ['orderId' => $this->orderId],
            Response::HTTP_NOT_FOUND
        );
    }
}
