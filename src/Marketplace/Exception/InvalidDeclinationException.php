<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Exception;

use Wizacha\Marketplace\Entities\Declination;

class InvalidDeclinationException extends \Exception implements MarketplaceExceptionInterface
{
    protected Declination $declination;
    private ErrorCode $errorCode;

    public function __construct(Declination $declination)
    {
        $this->errorCode = ErrorCode::INVALID_DECLINATION_ID();

        parent::__construct('Invalid declination (not found in PIM)', $this->errorCode->getValue());

        $this->declination = $declination;
    }

    public function getContext(): array
    {
        return ['declinationId' => $this->declination->getId()];
    }

    public function getErrorCode(): ErrorCode
    {
        return $this->errorCode;
    }
}
