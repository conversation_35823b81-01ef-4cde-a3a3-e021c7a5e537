<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\FinancialFlowsHistory;

use Symfony\Component\HttpFoundation\Response;
use Wizacha\Exim\Accounting;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\Refund\Repository\RefundRepository;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionType;

class FinancialFlowsHistoryService
{
    private TransactionService $transactionService;
    private RefundRepository $refundRepository;
    private OrderService $orderService;

    public function __construct(
        TransactionService $transactionService,
        RefundRepository $refundRepository,
        OrderService $orderService
    ) {
        $this->transactionService = $transactionService;
        $this->refundRepository = $refundRepository;
        $this->orderService = $orderService;
    }

    public function doTransactionExport(array $transactions): Response
    {
        $header = [
            __('table_transaction_header_date'),
            __('table_transaction_header_type'),
            __('table_transaction_header_origin'),
            __('table_transaction_header_destination'),
            __('table_transaction_header_transaction_id'),
            __('table_transaction_header_amount'),
            __('table_transaction_header_currency'),
            __('table_transaction_header_status'),
            __('table_transaction_header_order_id'),
            __('table_transaction_header_additional_information'),
        ];

        $fp = fopen('php://output', 'w');
        fputcsv($fp, $header, ';');
        foreach ($transactions as $transaction) {
            $transactionData = $transaction->exposeTransactionHistory();
            fputcsv(
                $fp,
                [
                    $transactionData['transactionDate'],
                    __('transaction_' . $transactionData['type'], [], 'en'), // tjrs en en
                    $transactionData['origin'],
                    $transactionData['destination'],
                    $transactionData['transactionId'],
                    $transactionData['amount'],
                    $transactionData['currency'],
                    $transactionData['status'],
                    $transactionData['order_id'],
                    $transactionData['processorInformation']['additional_info'],
                ],
                ';'
            );
        }

        fclose($fp);

        $response = new Response();
        $response->headers->set('Content-Type', 'text/csv');
        $response->headers->set('Content-Disposition', 'attachment; filename="transactions.csv"');

        return $response;
    }

    /**
     * @param string[] $filters
     *
     * @return mixed[]
     */
    public function getCommissionsByFilters(array $filters): array
    {
        $commissions = [];
        foreach ($this->orderService->getOrdersWithCommissionByFilters($filters) as $order) {
            $orderWorkflowLastUpdate = $this->orderService->getOrderWorkflowLastUpdate($order->getId());
            $commissions[] = [
                $order->getId(),
                $order->getCompanyId(),
                Accounting::getCompanyName($order->getId()),
                $orderWorkflowLastUpdate ? $orderWorkflowLastUpdate->format('Y-m-d H:i:s') : '',
                $order->getTotal()->getConvertedAmount(),
                $this->refundRepository->getOrderTotalAmountRefunded($order)->getConvertedAmount(),
                $this->transactionService->getBalanceByOrderId($order->getId())->getConvertedAmount(),
                Accounting::getCommissionsIncludingTaxes($order->getId()),
                $this->transactionService->getTransactionTransferDate($order->getId(), TransactionType::DISPATCH_FUNDS_TRANSFER_COMMISSION()),
                Accounting::getVendorShareIncludingTaxes($order->getId()),
                $this->transactionService->getTransactionTransferDate($order->getId(), TransactionType::DISPATCH_FUNDS_TRANSFER_VENDOR()),
            ];
        }

        return $commissions;
    }

    public function doCommissionExport(array $commissions): Response
    {
        $header = [
            __('table_transaction_header_order_id'),
            __('table_transaction_header_company_id'),
            __('table_transaction_header_company_name'),
            __('table_transaction_header_finished_id'),
            __('table_transaction_header_total_ttc'),
            __('table_transaction_header_refund_ttc'),
            __('table_transaction_header_order_balance'),
            __('table_transaction_header_commission_ttc'),
            __('table_transaction_header_commission_date'),
            __('table_transaction_header_vendor_share_ttc'),
            __('table_transaction_header_vendor_share_date'),
        ];

        $fp = fopen('php://output', 'w');
        fputcsv($fp, $header, ';');
        foreach ($commissions as $commission) {
            fputcsv($fp, $commission, ';');
        }

        fclose($fp);

        $response = new Response();
        $response->headers->set('Content-Type', 'text/csv');
        $response->headers->set('Content-Disposition', 'attachment; filename="commissions.csv"');

        return $response;
    }
}
