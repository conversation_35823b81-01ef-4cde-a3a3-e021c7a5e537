<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Search;

class SearchCriteria
{
    // extra field search:
    //    - extra[KEY] = '' means search only if the KEY exists, the value is not taken into account
    //    - extra[KEY] = 'VALUE' means search if it exists a KEY equal to VALUE
    //    - extra[KEY][] = 'VALUE' & extra[KEY][] = 'VALUE2' means search if it exists a KEY equal to VALUE OR equal to VALUE2
    //    - extra_start_with[KEY] = 'VALUE' (for old routes with params in snake_case)
    //      or extraStartWith[KEY] = 'VALUE (for new routes with params in camelCase)
    //      means search if it exists a KEY whose value starts with VALUE
    // All criteria are combined with the AND operator.
    public static function getFormattedConditionFromExtraCriteria(
        string $table,
        array $params,
        bool $startWith = false
    ): string {
        $extraCondition = '';

        foreach ($params as $key => $value) {
            if ('' !== $extraCondition) {
                $extraCondition .= ' AND ';
            }

            if (true === \is_array($value)) {
                $extraOr = '';
                foreach ($value as $v) {
                    if ('' !== $extraOr) {
                        $extraOr .= ' OR ';
                    }

                    $extraOr .= self::getSqlCondition($table, (string) $key, (string) $v, $startWith);
                }

                $extraCondition .= '(' . $extraOr . ')';
            } else {
                $extraCondition .= self::getSqlCondition($table, (string) $key, (string) $value, $startWith);
            }
        }

        return $extraCondition;
    }

    private static function formatValue(string $key, string $value, bool $startWith): string
    {
        if ('' === $value) {
            return '%"' . $key . '":%';
        }

        if (true === $startWith) {
            $value = "$value%";
        }

        $quote = (false === $startWith && true === \is_numeric($value)) ? '' : '"';

        return '%"' . $key . '":' . $quote . $value . $quote . '%';
    }

    private static function getSqlCondition(string $table, string $key, string $value, bool $startWith): string
    {
        $formattedExtra = self::formatValue($key, $value, $startWith);

        return db_quote("$table.extra LIKE ?s", $formattedExtra);
    }
}
