<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Review;

use Doctrine\DBAL\Connection;
use Wizacha\Marketplace\Catalog\Company\CompanyService;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Entities\ThreadPost;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Exception\Unauthorized;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\User\UserService;
use Wizacha\Status;

class CompanyReviewService
{
    /**
     * @var UserService
     */
    protected $userService;

    /**
     * @var CompanyService
     */
    protected $companyService;

    /**
     * @var OrderService
     */
    protected $orderService;

    /**
     * @var Connection
     */
    protected $connection;

    public function __construct(
        UserService $userService,
        CompanyService $companyService,
        OrderService $orderService,
        Connection $connection
    ) {
        $this->userService = $userService;
        $this->companyService = $companyService;
        $this->orderService = $orderService;
        $this->connection = $connection;
    }

    public function reviewCompany(int $userId, int $companyId, int $rating, string $message, string $status = ''): Review
    {
        // Récupération du threadId (permet de lancer la 404 si la company n'existe pas)
        $threadId = $this->companyService->getCompany($companyId)->getThreadId();

        // Récupération de l'utilisateur
        $user = $this->userService->get($userId);

        // Vérification de l'autorisation de l'utilisateur à poster une review
        $this->assertUserIsAuthorizedToReviewCompany($userId, $companyId);

        // Initialisation du thread si nécessaire
        if (\is_null($threadId)) {
            $threadId = $this->initThread($companyId, ReviewType::RATE_AND_COMMENT);
        }

        // Génération de la ressource
        $resource = Review::create($threadId, $user, $rating, $message, $status);

        // Sauvegarde de la resource
        $this->save($resource);

        // Retourne la resource sauvegardée
        return $resource;
    }

    /**
     * @return Review[]
     */
    public function listCompanyReviews(int $companyId): array
    {
        // Récupération de la company
        $company = new Company($companyId);

        $resources = array_map(function (ThreadPost $post) {
            // Récupération de l'utilisateur, ou passage au post suivant
            try {
                $user = $this->userService->get($post->getUser()->getId());
            } catch (NotFound $e) {
                return null;
            }

            // Création d'une ressource à partir du post
            return Review::createFromCSCartThreadPost($post, $user);
        }, $company->getReviews());

        return array_filter($resources);
    }

    public function getAverageRating(int $companyId): ?float
    {
        return (new Company($companyId))->getAverageRating();
    }

    /**
     * @throws Unauthorized
     */
    public function assertUserIsAuthorizedToReviewCompany(int $userId, int $companyId): void
    {
        $user = $this->userService->get($userId);

        // Vérification de son status
        if ($user->getStatus() != Status::ENABLED) {
            throw Unauthorized::fromId('user', $userId, 'is not enabled.');
        }

        // Vérification que l'utilisateur à déjà passé une commande avec ce vendeur
        if (!$this->orderService->isUserHaveOrderFromCompany($userId, $companyId)) {
            throw Unauthorized::fromId('user', $userId, 'must have made a purchase to the company.');
        }
    }

    protected function initThread(int $companyId, string $type): int
    {
        $data = [
            'object_id' => $companyId,
            'object_type' => ReviewTarget::COMPANY,
            'type' => $type,
        ];

        $threadId = fn_update_discussion($data);

        if ($threadId === false) {
            throw new \Exception(sprintf("Error while creating company thread (company id: %s)", $companyId));
        }

        if ($threadId === true) {
            throw new \Exception(sprintf("Company thread already exists (company id: %s)", $companyId));
        }

        return (int) $threadId;
    }

    protected function save(Review $review): int
    {
        $data = [
            'thread_id' => $review->getThreadId(),
            'name' => $review->getUser()->getFullname(),
            'timestamp' => $review->getPostedAt()->format("U"),
            'user_id' => $review->getUser()->getUserId(),
            'status' => $review->getStatus(),
        ];

        $this->connection->insert('cscart_discussion_posts', $data);
        $postId = $this->connection->lastInsertId();

        if (!empty($review->getMessage())) {
            $this->connection->insert('cscart_discussion_messages', [
                'thread_id' => $data['thread_id'],
                'post_id' => $postId,
                'message' => $review->getMessage(),
            ]);
        }

        if (!empty($review->getRating())) {
            $this->connection->insert('cscart_discussion_rating', [
                'post_id' => $postId,
                'thread_id' => $data['thread_id'],
                'rating_value' => $review->getRating(),
            ]);
        }

        return (int) $postId;
    }
}
