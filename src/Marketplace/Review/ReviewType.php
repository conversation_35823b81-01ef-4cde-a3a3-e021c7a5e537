<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Review;

use MyCLabs\Enum\Enum;

/**
 * @method static ReviewType DISABLED()
 * @method static ReviewType ONLY_RATE()
 * @method static ReviewType ONLY_COMMENT()
 * @method static ReviewType RATE_AND_COMMENT()
 */
class ReviewType extends Enum
{
    public const DISABLED = 'D';
    public const ONLY_RATE = 'R';
    public const ONLY_COMMENT = 'C';
    public const RATE_AND_COMMENT = 'B';
}
