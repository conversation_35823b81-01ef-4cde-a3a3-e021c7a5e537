<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Review;

use Wizacha\Marketplace\Entities\ThreadPost;
use Wizacha\Marketplace\Exception\IntegrityConstraintViolation;
use Wizacha\Marketplace\User\User;
use Wizacha\Status;

class Review
{
    /**
     * @var int
     */
    protected $threadId;

    /**
     * @var User
     */
    protected $user;

    /**
     * @var string
     */
    protected $message;

    /**
     * @var float
     */
    protected $rating;

    /**
     * @var string
     */
    protected $status;

    /**
     * @var \DateTime
     */
    protected $postedAt;

    protected function __construct(array $data)
    {
        $this->checkIntegrity($data);

        $this->threadId = $data['threadId'];
        $this->user = $data['user'];
        $this->message = $data['message'];
        $this->rating = $data['rating'];
        $this->status = $data['status'];
        $this->postedAt = $data['createdAt'];
    }

    public static function create(int $threadId, User $user, int $rating, string $message, string $status = ''): Review
    {
        $data = [
            'threadId' => $threadId,
            'user' => $user,
            'rating' => $rating,
            'message' => $message,
            'status' => Status::isValid($status) ? $status : Status::DISABLED,
            'createdAt' => new \DateTime(),
        ];

        return new Review($data);
    }

    public static function createFromCSCartThreadPost(ThreadPost $post, User $user): Review
    {
        $data = [
            'threadId' => $post->getThreadId(),
            'user' => $user,
            'rating' => $post->getRatingValue(),
            'message' => $post->getMessage() ?? '',
            'status' => $post->getStatus(),
            'createdAt' => $post->getDatetime(),
        ];

        return new Review($data);
    }

    public function getThreadId(): int
    {
        return $this->threadId;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function getRating(): float
    {
        return $this->rating;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function getPostedAt(): \DateTime
    {
        return $this->postedAt;
    }

    public function expose(): array
    {
        return [
            'author' => [
                'id' => $this->user->getUserId(),
                'name' => $this->user->getFullName(),
                'email' => $this->user->getEmail(),
            ],
            'message' => $this->message,
            'rating' => $this->rating,
            'postedAt' => $this->postedAt->format(\DateTime::RFC3339),
        ];
    }

    protected function checkIntegrity(array $data)
    {
        if (!isset($data['threadId'])) {
            throw IntegrityConstraintViolation::isMissing('threadId');
        }

        if (!\array_key_exists('user', $data)) {
            throw IntegrityConstraintViolation::isMissing('user');
        }

        if (!$data['user'] instanceof User) {
            throw IntegrityConstraintViolation::isInvalid('user');
        }

        if (!isset($data['message'])) {
            throw IntegrityConstraintViolation::isMissing('message');
        }

        if (!isset($data['rating'])) {
            throw IntegrityConstraintViolation::isMissing('rating');
        }

        if (!\is_int($data['rating']) || $data['rating'] < 0 || $data['rating'] > 5) {
            throw IntegrityConstraintViolation::isInvalid('rating');
        }

        if (!Status::isValid($data['status'])) {
            throw IntegrityConstraintViolation::isInvalid('status');
        }

        if (!isset($data['createdAt'])) {
            throw IntegrityConstraintViolation::isMissing('createdAt');
        }
    }
}
