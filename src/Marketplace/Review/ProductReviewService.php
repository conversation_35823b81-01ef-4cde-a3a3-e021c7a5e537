<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Review;

use Doctrine\DBAL\Connection;
use Psr\Log\LoggerAwareTrait;
use Wizacha\Marketplace\Catalog\Exceptions\NoThreadCreated;
use Wizacha\Marketplace\Catalog\Exceptions\ReviewsAreDisabled;
use Wizacha\Marketplace\Entities\Thread;
use Wizacha\Marketplace\Entities\ThreadPost;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\ReadModel\Product;
use Wizacha\Marketplace\User\User;
use Wizacha\Status;

/**
 * Ce service devrait contenir exactement les même fonctions que celui gérant
 * les reviews des company : \Wizacha\Marketplace\Review\CompanyReviewService
 */
class ProductReviewService
{
    use LoggerAwareTrait;

    /**
     * @var Connection
     */
    protected $connection;

    public function __construct(
        Connection $connection
    ) {
        $this->connection = $connection;
    }

    /**
     * @param int|string $productId
     *
     * @return float
     */
    public function getAverageRating($productId): float
    {
        $sql = "SELECT AVG(rating_value) as average_rating
                FROM cscart_discussion_rating AS rating
                INNER JOIN cscart_discussion_posts as post ON post.post_id = rating.post_id
                INNER JOIN cscart_discussion as discussion ON discussion.thread_id = post.thread_id
                WHERE discussion.object_id = :productId AND discussion.object_type = :reviewTarget AND post.status = :reviewStatus";

        $statement = $this->connection->prepare($sql);
        $statement->bindValue('productId', $productId, \PDO::PARAM_STR);
        $statement->bindValue('reviewTarget', ReviewTarget::PRODUCT, \PDO::PARAM_STR);
        $statement->bindValue('reviewStatus', Status::ENABLED, \PDO::PARAM_STR);
        $statement->execute();

        $averageRating = $statement->fetchColumn();

        if (\is_null($averageRating) === true) {
            return 0;
        }

        return (float) $averageRating;
    }

    /**
     * @param int|string $productId
     */
    public function getRatingCount($productId): int
    {
        $sql = "SELECT COUNT(*) as rating_count
                FROM cscart_discussion_rating AS rating
                INNER JOIN cscart_discussion_posts as post ON post.post_id = rating.post_id
                INNER JOIN cscart_discussion as discussion ON discussion.thread_id = post.thread_id
                WHERE discussion.object_id = :productId AND discussion.object_type = :reviewTarget AND post.status = :reviewStatus";

        $statement = $this->connection->prepare($sql);
        $statement->bindValue('productId', $productId, \PDO::PARAM_STR);
        $statement->bindValue('reviewTarget', ReviewTarget::PRODUCT, \PDO::PARAM_STR);
        $statement->bindValue('reviewStatus', Status::ENABLED, \PDO::PARAM_STR);
        $statement->execute();

        return (int) $statement->fetchColumn();
    }

    /**
     * Récupération de toutes les reviews d'un produit pour pouvoir les modérer.
     *
     * @param string[] $productIds tableau d'id d'un MVP et ses produits
     * @return ThreadPost[]
     */
    public function getReviewsForModeration(array $productIds): array
    {
        $ids = implode(',', array_fill(0, \count($productIds), '?'));
        $query = <<<SQL
SELECT dp.post_id FROM cscart_discussion_posts AS dp
    INNER JOIN cscart_discussion AS d ON d.thread_id = dp.thread_id AND d.type != 'D'
    WHERE d.object_id IN ($ids) AND d.object_type = 'P'
    ORDER BY timestamp DESC, dp.post_id DESC

SQL;
        $stmt = $this->connection->prepare($query);
        $stmt->execute($productIds);
        $threadPost = $stmt->fetchAll(\PDO::FETCH_COLUMN);

        return array_map(function ($threadId) {
            return new ThreadPost((int) $threadId);
        }, $threadPost);
    }

    /**
     * Récupération d'une review en vérifiant bien qu'elle appartienne au
     * produit souhaité (pour éviter de faire n'importe quoi sur les reviews
     * des autres).
     *
     * @param int|string $productId Id d'un produit ou d'un MVP
     */
    public function getReview($productId, int $reviewId): ThreadPost
    {
        $query = <<<SQL
SELECT EXISTS (
    SELECT 1 FROM cscart_discussion_posts AS dp
    INNER JOIN cscart_discussion AS d ON d.thread_id = dp.thread_id AND d.type != 'D'
    WHERE d.object_id=? AND dp.post_id=? AND d.object_type = 'P'
)
SQL;

        $exists = $this->connection->executeQuery($query, [
            (string) $productId,
            $reviewId,
        ])->fetchColumn();

        if (!$exists) {
            throw new NotFound();
        }

        return new ThreadPost($reviewId);
    }

    public function approveReview(int $reviewId): void
    {
        $this->changeReviewStatus($reviewId, Status::ENABLED());
    }

    public function changeReviewStatus($reviewId, Status $status): void
    {
        $this->connection->executeUpdate(
            "UPDATE cscart_discussion_posts SET status = ? WHERE post_id = ?",
            [
                (string) $status,
                $reviewId,
            ],
            [
                \PDO::PARAM_STR,
                \PDO::PARAM_INT,
            ]
        );
    }

    public function declineReview(int $reviewId): void
    {
        $this->changeReviewStatus($reviewId, Status::DISABLED());
    }

    /**
     * @param int|string $productId
     * @return ThreadPost[]
     */
    public function getReviews($productId): array
    {
        $query = <<<SQL
SELECT dp.post_id FROM cscart_discussion_posts AS dp
    INNER JOIN cscart_discussion AS d ON d.thread_id = dp.thread_id AND d.type != 'D'
    WHERE d.object_id=? AND d.object_type = 'P' AND dp.status = 'A'
SQL;

        $threadPost = array_column($this->connection->executeQuery($query, [(string) $productId])->fetchAll(), 'post_id');

        return array_map(function ($threadId) {
            return new ThreadPost(\intval($threadId));
        }, $threadPost);
    }

    /**
     * @param Product $product
     * @param User $user
     * @param string $name
     * @param string|null $message
     * @param int|null $rating
     * @param string $status
     * @throws NoThreadCreated
     * @throws ReviewsAreDisabled
     */
    public function addReview(Product $product, User $user, string $name, string $message = null, int $rating = null, $status = Status::DISABLED)
    {
        if ($rating < 1 || $rating > 5) {
            $rating = null;
        }

        $thread = $this->getProductThread($product->getId());

        if (!$thread->getId()) {
            throw new NoThreadCreated('No thread created for product ' . $product->getId());
        }

        if (!$thread->isEnabled()) {
            throw new ReviewsAreDisabled('Thread is disabled for product ' . $product->getId());
        }

        $ip = fn_get_ip();
        $postData = [
            'thread_id' => $thread->getId(),
            'name' => $name,
            'message' => $message,
            'rating_value' => $rating,
            'ip_address' => $ip['host'],
            'status' => $status,
            'timestamp' => time(),
            'user_id' => $user->getUserId(),
        ];
        $postData['post_id'] = db_query("INSERT INTO ?:discussion_posts ?e", $postData);

        if (!empty($postData['message'])) {
            db_query("REPLACE INTO ?:discussion_messages ?e", $postData);
        }

        if (!empty($postData['rating_value'])) {
            db_query("REPLACE INTO ?:discussion_rating ?e", $postData);
        }
    }

    /**
     * @param int|string $productId
     * @return Thread
     *
     * @deprecated Cette fonction devrait se trouver dans le service adéquat :
     * \Wizacha\Marketplace\Review\ProductReviewService
     */
    public function getProductThread($productId): Thread
    {
        $threadId = $this->connection->executeQuery("SELECT thread_id FROM cscart_discussion WHERE object_id=? AND object_type='p' LIMIT 1;", [(string) $productId])->fetchColumn() ?: 0;

        if ($threadId === 0) {
            $this->connection->executeQuery("INSERT INTO cscart_discussion (object_id, object_type, type) VALUES (?, 'p', 'B');", [(string) $productId]);
            $threadId = $this->connection->lastInsertId();
        }

        return new Thread(\intval($threadId));
    }
}
