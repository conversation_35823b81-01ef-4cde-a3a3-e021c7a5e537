<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\CreditCard\Service;

use Doctrine\ORM\NoResultException;
use Wizacha\Marketplace\CreditCard\CreditCard;
use Wizacha\Marketplace\CreditCard\Exception\CreditCardException;
use Wizacha\Marketplace\CreditCard\Repository\CreditCardRepository;
use Wizacha\Marketplace\Payment\PaymentService;
use Wizacha\Marketplace\Payment\Processor\HiPay;
use Wizacha\Marketplace\Subscription\SubscriptionService;
use Wizacha\Marketplace\User\User;
use Wizacha\Marketplace\User\UserService;

class CreditCardService
{
    /** @var CreditCardRepository */
    protected $creditCardRepository;

    /** @var UserService */
    protected $userService;

    /** @var SubscriptionService */
    protected $subscriptionService;

    /** @var PaymentService */
    protected $paymentService;

    public function __construct(
        CreditCardRepository $creditCardRepository,
        UserService $userService,
        SubscriptionService $subscriptionService,
        PaymentService $paymentService
    ) {
        $this->creditCardRepository = $creditCardRepository;
        $this->userService = $userService;
        $this->subscriptionService = $subscriptionService;
        $this->paymentService = $paymentService;
    }

    /**
     * Permet de parser les éléments de la requête POST uniquement envoyés par le PSP afin de stocker les informations
     * de la carte de crédit de l'utilisateur.
     *
     * @param string $pspClass Classe du PSP à utiliser pour parser correctement la requête POST
     *
     * @throws CreditCardException
     */
    public function parseRequestData(array $request, string $pspClass): CreditCard
    {
        if (HiPay::class === $pspClass) {
            return $this->parseHiPayRequestData($request);
        }

        throw new CreditCardException("Enable to parse request for PSP $pspClass.");
    }

    /** @return CreditCard[] */
    public function get(User $user): array
    {
        return $this->creditCardRepository->findByUser($user);
    }

    public function save(CreditCard $creditCard): CreditCard
    {
        try {
            $currentCard = $this
                ->creditCardRepository
                ->findOneByCardInfo(
                    $creditCard->getPan(),
                    $creditCard->getCardExpiryMonth(),
                    $creditCard->getCardExpiryYear(),
                    $creditCard->getUser()
                );
            if (\is_string($creditCard->getToken())
                && $currentCard->getToken() !== $creditCard->getToken()
            ) {
                $currentCard->setToken($creditCard->getToken());
                $creditCard = $this->creditCardRepository->save($currentCard);
            } else {
                $creditCard = $currentCard;
            }
        } catch (NoResultException $exception) {
            $creditCard = $this->creditCardRepository->save($creditCard);
        }

        return $creditCard;
    }

    /** @param mixed[] $request from $request->request->all() */
    public function replace(array $request, string $pspClass): ?CreditCard
    {
        $customData = $request['custom_data'];

        if (false === \is_array($customData)
            || 0 === \count($customData)
            || false === \array_key_exists('renewCreditCard', $customData)
            || false === \array_key_exists('userId', $customData)
            || "1" !== $customData['renewCreditCard'] // Don't cast to bool to avoid bad cast
        ) {
            return null;
        }

        $creditCard = ($this->parseRequestData($request, $pspClass))
            ->setUser(
                $this->userService->get($customData['userId'])
            )
            ->defineCreatedAt()
        ;

        $this->subscriptionService->replaceCreditCard(
            $this->save($creditCard),
            (int) $customData['loggedUser']
        );

        return $creditCard;
    }

    public function renew(User $user, int $paymentId, string $redirectUrl, string $cssUrl = ''): string
    {
        return $this
                ->paymentService
                ->getPaymentProcessor(fn_get_payment_method_data($paymentId))
                ->renewCreditCard($user, $redirectUrl, $cssUrl)
                ->getRedirectUrl()
        ;
    }

    /** @param mixed[] $request from $request->request->all() */
    protected function parseHiPayRequestData(array $request): CreditCard
    {
        return (new CreditCard())
            ->setToken($request['payment_method']['token'])
            ->setBrand($request['payment_method']['brand'])
            ->setPan($request['payment_method']['pan'])
            ->setCardHolder($request['payment_method']['card_holder'])
            ->setCardExpiryMonth($request['payment_method']['card_expiry_month'])
            ->setCardExpiryYear($request['payment_method']['card_expiry_year'])
            ->setIssuer($request['payment_method']['issuer'])
            ->setCountry($request['payment_method']['country'])
            ->setPaymentProductCode($request['payment_product'])
        ;
    }

    public function findOneByCardInfo($params): ?CreditCard
    {
        try {
            return $this
                ->creditCardRepository
                ->findOneByCardInfo(
                    $params['pan'],
                    $params['expiryMonth'],
                    $params['expiryYear'],
                    $params['user']
                );
        } catch (NoResultException $exception) {
            return null;
        }
    }
}
