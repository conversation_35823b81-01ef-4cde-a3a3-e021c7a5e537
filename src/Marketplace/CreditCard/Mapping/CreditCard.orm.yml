Wizacha\Marketplace\CreditCard\CreditCard:
    type: entity
    id:
        id:
            type: guid
            length: 36
            options:
                fixed: true
    fields:
        token:
            unique: true
        brand: ~
        pan:
            length: 19
            options:
                fixed: true
        cardHolder:
            type: string
            nullable: true
        cardExpiryMonth:
            length: 2
            options:
                fixed: true
        cardExpiryYear:
            length: 4
            options:
                fixed: true
        issuer:
            type: string
            nullable: true
        country:
            length: 2
            options:
                fixed: true
        createdAt:
            type: datetimetz_immutable
        paymentProductCode:
            type: string
            nullable: true
        pspUserId:
            type: string
            nullable: true
    uniqueConstraints:
        uniq_card_per_user_idx:
            columns: [user_id, pan, card_expiry_month, card_expiry_year]
    lifecycleCallbacks:
        prePersist: [defineGuid, defineCreatedAt]
    oneToMany:
        subscriptions:
            targetEntity: Wizacha\Marketplace\Subscription\Subscription
            mappedBy: creditCard
            orphanRemoval: true
            cascade: [persist, remove]
    manyToOne:
        user:
            targetEntity: Wizacha\Marketplace\User\User
            inversedBy: creditCards
            joinColumn:
                referencedColumnName: user_id
                nullable: false
