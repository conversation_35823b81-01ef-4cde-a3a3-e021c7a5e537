<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\CreditCard\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NoResultException;
use Wizacha\Marketplace\CreditCard\CreditCard;
use Wizacha\Marketplace\User\User;

class CreditCardRepository extends ServiceEntityRepository
{
    /** @throws NoResultException */
    public function findOneByCardInfo(string $pan, string $expiryMonth, string $expiryYear, User $user): ?CreditCard
    {
        return $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->select("creditCard")
            ->from(CreditCard::class, "creditCard")
            ->where("creditCard.pan = :pan")
            ->andWhere("creditCard.cardExpiryMonth = :expiryMonth")
            ->andWhere("creditCard.cardExpiryYear = :expiryYear")
            ->andWhere("creditCard.user = :user")
            ->setParameters([
                'pan' => $pan,
                'expiryMonth' => $expiryMonth,
                'expiryYear' => $expiryYear,
                'user' => $user,
            ])
            ->getQuery()
            ->getSingleResult()
        ;
    }

    /** @return CreditCard[] */
    public function findByUser(User $user, int $limit = null, int $offset = null): array
    {
        $queryBuilder = $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->select("creditCard")
            ->from(CreditCard::class, "creditCard")
            ->where("creditCard.user = :user")
            ->setParameter('user', $user)
            ->orderBy("creditCard.createdAt")
            ->addOrderBy("creditCard.id")
        ;

        if (\is_int($limit) && \is_int($offset)) {
            $queryBuilder
                ->setMaxResults($limit)
                ->setFirstResult($offset)
            ;
        }

        return $queryBuilder
            ->getQuery()
            ->getResult()
        ;
    }

    public function findOneById(string $cardId, User $user): ?CreditCard
    {
        $queryBuilder = $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->select("creditCard")
            ->from(CreditCard::class, "creditCard")
            ->where("creditCard.id = :id")
            ->setParameter('id', $cardId)
        ;

        if ($user instanceof User) {
            $queryBuilder
                ->andWhere("creditCard.user = :user")
                ->setParameter('user', $user)
            ;
        }

        return $queryBuilder
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }

    public function save(CreditCard $creditCard): CreditCard
    {
        $this->getEntityManager()->persist($creditCard);
        $this->getEntityManager()->flush();

        return $creditCard;
    }
}
