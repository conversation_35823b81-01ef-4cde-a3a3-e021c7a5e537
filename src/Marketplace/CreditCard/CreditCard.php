<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\CreditCard;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Wizacha\Marketplace\Subscription\Subscription;
use Wizacha\Marketplace\Traits\HasUuid;
use Wizacha\Marketplace\User\User;

class CreditCard implements \JsonSerializable
{
    use HasUuid;

    protected ?string $id;
    protected ?User $user;
    protected ?string $token;
    protected ?string $brand;
    protected ?string $pan;
    protected ?string $cardHolder;
    protected ?string $cardExpiryMonth;
    protected ?string $cardExpiryYear;
    protected ?string $issuer;
    protected ?string $country;
    protected ?\DateTimeImmutable $createdAt = null;

    /** @var Collection|Subscription[] */
    protected $subscriptions;

    protected ?string $paymentProductCode;

    protected ?string $pspUserId;

    public function __construct()
    {
        $this->subscriptions = new ArrayCollection();
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getToken(): ?string
    {
        return $this->token;
    }

    public function setToken(string $token): self
    {
        $this->token = $token;

        return $this;
    }

    public function getBrand(): ?string
    {
        return $this->brand;
    }

    public function setBrand(string $brand): self
    {
        $this->brand = $brand;

        return $this;
    }

    public function getPan(): ?string
    {
        return $this->pan;
    }

    public function setPan(string $pan): self
    {
        if (mb_strlen($pan) === 16) {
            $pan = implode(
                " ",
                str_split($pan, 4)
            );
        }

        $this->pan = $pan;

        return $this;
    }

    public function getCardHolder(): ?string
    {
        return $this->cardHolder;
    }

    public function setCardHolder(?string $cardHolder): self
    {
        $this->cardHolder = $cardHolder;

        return $this;
    }

    public function getCardExpiryMonth(): ?string
    {
        return $this->cardExpiryMonth;
    }

    public function setCardExpiryMonth(string $cardExpiryMonth): self
    {
        $this->cardExpiryMonth = $cardExpiryMonth;

        return $this;
    }

    public function getCardExpiryYear(): ?string
    {
        return $this->cardExpiryYear;
    }

    public function setCardExpiryYear(string $cardExpiryYear): self
    {
        $this->cardExpiryYear = $cardExpiryYear;

        return $this;
    }

    public function getIssuer(): ?string
    {
        return $this->issuer;
    }

    public function setIssuer(?string $issuer): self
    {
        $this->issuer = $issuer;

        return $this;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(string $country): self
    {
        $this->country = $country;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function defineCreatedAt(): self
    {
        $this->createdAt = new \DateTimeImmutable();

        return $this;
    }

    /** @return Collection|Subscription[] */
    public function getSubscriptions(): Collection
    {
        return $this->subscriptions;
    }

    public function setPaymentProductCode(string $paymentProductCode): self
    {
        $this->paymentProductCode = $paymentProductCode;

        return $this;
    }

    public function getPaymentProductCode(): ?string
    {
        return $this->paymentProductCode;
    }

    /** @param Subscription[] $subscriptions */
    public function setSubscriptions(iterable $subscriptions): self
    {
        $this->clearSubscriptions();

        foreach ($subscriptions as $subscription) {
            $this->addSubscription($subscription);
        }

        return $this;
    }

    public function addSubscription(Subscription $subscription): self
    {
        if ($this->subscriptions->contains($subscription) === false) {
            $this->subscriptions->add($subscription);
            $subscription->setCreditCard($this);
        }

        return $this;
    }

    public function removeSubscription(Subscription $subscription): self
    {
        if ($this->subscriptions->contains($subscription)) {
            $this->subscriptions->removeElement($subscription);
            $subscription->setCreditCard(null);
        }

        return $this;
    }

    public function clearSubscriptions(): self
    {
        foreach ($this->getSubscriptions() as $subscription) {
            $this->removeSubscription($subscription);
        }

        $this->subscriptions->clear();

        return $this;
    }

    public function getPspUserId(): ?string
    {
        return $this->pspUserId;
    }

    public function setPspUserId(?string $pspUserId): self
    {
        $this->pspUserId = $pspUserId;

        return $this;
    }

    public function jsonSerialize(): array
    {
        return [
            'id' => $this->getId(),
            "userId" => $this->getUser()->getUserId(),
            "brand" => $this->getBrand(),
            "pan" => $this->getPan(),
            "holder" => $this->getCardHolder(),
            "expiryMonth" => $this->getCardExpiryMonth(),
            "expiryYear" => $this->getCardExpiryYear(),
            "issuer" => $this->getIssuer(),
            "country" => $this->getCountry(),
            "createdAt" => $this->getCreatedAt()->format(\DateTimeImmutable::RFC3339),
        ];
    }
}
