<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PSP\UboMangopay;

class UboMangopay
{
    private int $id;
    private ?\DateTime $submittedDate;
    private ?string $declarationId;
    private ?string $uboStatus;
    private ?int $person;

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function setSubmittedDate(?\DateTime $submittedDate): self
    {
        $this->submittedDate = $submittedDate;

        return $this;
    }

    public function setDeclarationId(?string $declarationId): self
    {
        $this->declarationId = $declarationId;

        return $this;
    }

    public function setUboStatus(?string $uboStatus): self
    {
        $this->uboStatus = $uboStatus;

        return $this;
    }

    public function setPerson(?int $companyPerson): self
    {
        $this->person = $companyPerson;

        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getSubmittedDate(): ?\DateTime
    {
        return $this->submittedDate;
    }

    public function getDeclarationId(): ?string
    {
        return $this->declarationId;
    }

    public function getUboStatus(): ?string
    {
        return $this->uboStatus;
    }

    public function getPerson(): ?int
    {
        return $this->person;
    }
}
