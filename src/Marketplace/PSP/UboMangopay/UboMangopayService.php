<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\PSP\UboMangopay;

use Wizacha\Marketplace\CompanyPerson\CompanyPerson;
use Wizacha\Marketplace\CompanyPerson\CompanyPersonRepository;
use Wizacha\Marketplace\Payment\UBO\Status;

class UboMangopayService
{
    private UboMangopayRepository $uboMangopayRepository;
    private CompanyPersonRepository $companyPersonRepository;

    public function __construct(
        UboMangopayRepository $uboMangopayRepository,
        CompanyPersonRepository $companyPersonRepository
    ) {
        $this->uboMangopayRepository = $uboMangopayRepository;
        $this->companyPersonRepository = $companyPersonRepository;
    }

    public function save(UboMangopay $uboMangopay): UboMangopay
    {
        return $this->uboMangopayRepository->save($uboMangopay);
    }

    public function getDeclarationId(CompanyPerson $companyPerson): ?string
    {
        $uboMangoPay = $this->uboMangopayRepository->getByCompanyPerson($companyPerson);

        return $uboMangoPay !== null ? $uboMangoPay->getDeclarationId() : null;
    }

    public function isUBOSubmitted(int $companyId): bool
    {
        $companyPersonList = $this->companyPersonRepository->findByCompanyId($companyId);
        foreach ($companyPersonList as $companyPerson) {
            $uboMangoPay = $this->uboMangopayRepository->getByCompanyPerson($companyPerson);

            return $uboMangoPay !== null && $uboMangoPay->getUboStatus() !== Status::INCOMPLETE()->getValue();
        }

        return false;
    }

    public function getByCompanyPerson(CompanyPerson $companyPerson): ?UboMangopay
    {
        return $this->uboMangopayRepository->getByCompanyPerson($companyPerson);
    }

    public function getByDeclaration(?string $declarationId): array
    {
        return $this->uboMangopayRepository->getByDeclaration($declarationId);
    }
}
