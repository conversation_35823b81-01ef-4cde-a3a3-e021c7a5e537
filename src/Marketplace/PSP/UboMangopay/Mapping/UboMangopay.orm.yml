Wizacha\Marketplace\PSP\UboMangopay\UboMangopay:
    type: entity
    table: ubo_mangopay
    id:
        id:
            type: integer
            column: id
            generator:
                strategy: AUTO
    fields:
        submittedDate:
            type: datetime
            column: submitted_date
        declarationId:
            type: string
            column: declaration_id
        uboStatus:
            type: string
            column: ubo_status
        person:
            type: integer
            column: person
    OneToOne:
        person:
            targetEntity: Wizacha\Marketplace\CompanyPerson\CompanyPerson
            joinColumn:
                name: person
                referencedColumnName: id
