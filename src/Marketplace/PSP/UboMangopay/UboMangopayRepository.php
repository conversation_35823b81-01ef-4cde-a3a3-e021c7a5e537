<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\PSP\UboMangopay;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Persistence\ManagerRegistry;
use Wizacha\Marketplace\CompanyPerson\CompanyPerson;

class UboMangopayRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry, $entityClass)
    {
        parent::__construct($registry, $entityClass);
    }

    public function get(int $id): UboMangopay
    {
        return $this->getEntityManager()->find(UboMangopay::class, $id);
    }

    public function getByCompanyPerson(CompanyPerson $companyPerson): ?UboMangopay
    {
        return
            $this->getEntityManager()
                ->createQueryBuilder()
                ->select('u')
                ->where('u.person = :person')
                ->from(UboMangopay::class, 'u')
                ->innerJoin(CompanyPerson::class, 'c')
                ->setParameter('person', $companyPerson)
                ->orderBy('u.submittedDate', 'ASC')
                ->getQuery()
                ->getOneOrNullResult();
    }

    public function save(UboMangopay $uboMangoPay): UboMangopay
    {
        if ($this->getEntityManager()->contains($uboMangoPay) === false) {
            $this->getEntityManager()->persist($uboMangoPay);
        }
        $this->getEntityManager()->flush();

        return $uboMangoPay;
    }

    public function getByDeclaration(?string $declarationId): array
    {
        return
            $this->getEntityManager()
                ->createQueryBuilder()
                ->select('u')
                ->where('u.declarationId = :declarationId')
                ->from(UboMangopay::class, 'u')
                ->setParameter('declarationId', $declarationId)
                ->getQuery()
                ->getResult();
    }
}
