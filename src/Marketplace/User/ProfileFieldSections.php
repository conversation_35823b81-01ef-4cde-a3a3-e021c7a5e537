<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\User;

use MyCLabs\Enum\Enum;

/**
 * @method static ProfileFieldSections MAIN_PROFILE()
 * @method static ProfileFieldSections BILLING()
 * @method static ProfileFieldSections SHIPPING()
 */
class ProfileFieldSections extends Enum
{
    /**
     * It seems to be the main user profile.
     */
    public const MAIN_PROFILE = 'C';
    /**
     * Billing address.
     */
    public const BILLING = 'B';
    /**
     * Shipping address.
     */
    public const SHIPPING = 'S';
}
