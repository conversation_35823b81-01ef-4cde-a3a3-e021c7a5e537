<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\User\Event;

use Symfony\Component\Form\Form;
use Wizacha\Marketplace\User\User;
use Wizacha\Registry;

class UserAskedToRecoverPassword extends UserEvent
{
    /**
     * @var string
     */
    private $token;

    /**
     * @var null|string
     */
    private $recoverBaseUrl;

    private bool $isPasswordRecoveryForceChangeActivated;

    public function __construct(User $user, string $token, ?string $recoverBaseUrl = null, bool $isPasswordRecoveryForceChangeActivated = false)
    {
        parent::__construct($user);
        $this->token = $token;
        $this->recoverBaseUrl = $recoverBaseUrl;
        $this->isPasswordRecoveryForceChangeActivated = $isPasswordRecoveryForceChangeActivated;
    }

    public function getToken(): string
    {
        return $this->token;
    }

    public function getRecoverBaseUrl(): ?string
    {
        return $this->recoverBaseUrl;
    }

    public function isPasswordRecoveryForceChangeActivated(): bool
    {
        return $this->isPasswordRecoveryForceChangeActivated;
    }

    public static function createFromForm(Form $form)
    {
        $user = $form->getData()['user'];
        // Get User with its repository rather than Doctrine only to ensure address is filled
        $user = Registry::defaultInstance()->container->get('marketplace.user.user_repository')->get($user->getUserId());

        return new UserAskedToRecoverPassword($user, md5(random_bytes(32)));
    }

    public static function getDescription(): string
    {
        return 'forgot_password_question';
    }
}
