<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\User\Event;

use Wizacha\Marketplace\User\User;

class UserPasswordChanged extends UserEvent
{
    public function __construct(User $user)
    {
        parent::__construct($user);
    }

    public static function getDescription(): string
    {
        return 'user_password_changed';
    }
}
