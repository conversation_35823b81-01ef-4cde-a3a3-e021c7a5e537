<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\User\Event;

use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Contracts\EventDispatcher\Event;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Wizacha\Component\Notification\NotificationEvent;
use Wizacha\Marketplace\User\User;
use Wizacha\Registry;

abstract class UserEvent extends Event implements NotificationEvent
{
    private $user;

    public function __construct(User $user)
    {
        $this->user = $user;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public static function buildDebugForm(FormBuilder $form)
    {
        $form->add('user', EntityType::class, [
            'class' => User::class,
            'choice_label' => 'email',
        ]);
    }

    public static function createFromForm(Form $form)
    {
        $user = $form->getData()['user'];
        // Get User with its repository rather than <PERSON>trine only to ensure address is filled
        $user = Registry::defaultInstance()->container->get('marketplace.user.user_repository')->get($user->getUserId());

        return new static($user);
    }
}
