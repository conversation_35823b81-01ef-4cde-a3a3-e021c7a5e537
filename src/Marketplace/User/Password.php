<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\User;

class Password
{
    public static function generatePassword(int $length = 10): string
    {
        $chars = [
            'lower' => 'abcdefghijklmnopqrstuvwxyz',
            'upper' => 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
            'number' => '**********',
            'special' => '?.\/,;:!@$%*-_#',
        ];
        $result = '';
        $lenghtPerChar = \round($length / 4);

        foreach ($chars as $value) {
            for ($i = 0; $i < $lenghtPerChar; $i++) {
                $result .= $value[\mt_rand(0, \strlen($value) - 1)];
            }
        }

        return substr(str_shuffle($result), 0, $length);
    }

    public static function isPasswordFormatValid(string $password): bool
    {
        /**
         * (?=.*\d) Au moin 1 chiffre
         * (?=.*[a-z]) Au moin 1 lettre minuscule
         * (?=.*[A-Z]) Au moin 1 lettre majuscule
         * (?=.*[\W_]) Au moin 1 caractère spécial ou accentué
         * {10,} Au moin 10 caractère
         */
        return 1 === \preg_match('/^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[\W_]).{10,}$/', $password);
    }
}
