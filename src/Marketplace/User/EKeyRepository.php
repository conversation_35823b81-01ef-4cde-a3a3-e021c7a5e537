<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\User;

use Doctrine\DBAL\Connection;

class EKeyRepository
{
    private Connection $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
    }

    /*** @param mixed[] $data */
    public function save(array $data): void
    {
        $this->connection->executeQuery("REPLACE INTO cscart_ekeys VALUES (:object_id, :object_string, :object_type, :ekey, :ttl)", $data);
    }

    public function getUserId($eKey): ?string
    {
        return $this->connection->executeQuery(
            "SELECT object_id FROM cscart_ekeys WHERE ekey = :ekey AND object_type = 'U' AND ttl > :ttl",
            [
                'ekey' => $eKey,
                'ttl' => TIME
            ]
        )->fetchColumn();
    }

    public function removeByUserId(string $userId): void
    {
        $this->connection->executeQuery("DELETE FROM cscart_ekeys WHERE object_id = :userId", ['userId' => $userId]);
    }

    public function removeByEKey(string $eKey): void
    {
        $this->connection->executeQuery('DELETE FROM cscart_ekeys WHERE ekey = :ekey', ['ekey' => $eKey]);
    }

    public function removeExpired(): void
    {
        $this->connection->executeQuery("DELETE FROM cscart_ekeys WHERE ttl > 0 AND ttl < :ttl", ['ttl' => TIME]);
    }
}
