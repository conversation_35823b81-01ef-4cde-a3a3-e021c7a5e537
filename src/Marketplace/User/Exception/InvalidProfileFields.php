<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\User\Exception;

/**
 * Some profile fields are invalid/missing.
 */
class InvalidProfileFields extends \Exception
{
    /**
     * @var array
     */
    private $missingFields;

    public function __construct(array $missingFields)
    {
        $this->missingFields = $missingFields;

        parent::__construct('Invalid fields: ' . implode(', ', $missingFields));
    }

    /**
     * @return string[]
     */
    public function getMissingFields(): array
    {
        return $this->missingFields;
    }
}
