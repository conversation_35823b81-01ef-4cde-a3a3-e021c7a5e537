<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\User\Exception;

class InvalidFieldException extends \Exception
{
    /**
     * @var string
     */
    private $invalidField;

    public function __construct(string $invalidField, string $message)
    {
        parent::__construct($message);

        $this->invalidField = $invalidField;
    }

    public function getInvalidField(): string
    {
        return $this->invalidField;
    }
}
