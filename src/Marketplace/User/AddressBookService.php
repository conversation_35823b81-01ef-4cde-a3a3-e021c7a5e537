<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\User;

use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\User\Event\UserProfileUpdated;

class AddressBookService
{
    /** @var AddressBookRepository */
    private $repository;

    /** @var UserService */
    private $userService;

    /** @var UserRepository */
    private $userRepository;

    /** @var EventDispatcherInterface */
    private $eventDispatcher;

    public function __construct(
        AddressBookRepository $addressBookRepository,
        UserService $userService,
        UserRepository $userRepository,
        EventDispatcherInterface $eventDispatcher
    ) {
        $this->repository = $addressBookRepository;
        $this->userService = $userService;
        $this->userRepository = $userRepository;
        $this->eventDispatcher = $eventDispatcher;
    }

    /**
     * @param  string $id
     * @return AddressBook
     * @throws NotFound
     */
    public function get(string $id): ?AddressBook
    {
        $addressBook = $this->repository->findOneById($id);
        if (null === $addressBook) {
            throw NotFound::fromId('AddressBook', $id);
        }

        return $addressBook;
    }

    public function save(
        int $userId,
        array $data,
        bool $notifyUser,
        string $addressId = '0',
        AddressStatus $typeAddress = null
    ): string {
        $user = $this->userService->get($userId);

        if ($addressId === '0') {
            if (\count($user->getAddressBook()) === 20) {
                fn_set_notification('E', __('error'), __('error_max_address'));

                return '';
            }

            $addressBook = new AddressBook();
        } else {
            $addressBook = $this->repository->findOneById($addressId);
            $this->removeAddressFromProfile($userId, $addressId);
        }

        $data['principal_address'] = ($typeAddress === null) ? $data['principal_address'] : $typeAddress->getValue();

        $label = ($typeAddress === null) ? 'label' : strtolower($typeAddress->getValue()) . '_label';
        $title = ($typeAddress === null) ? 'title' : strtolower($typeAddress->getValue()) . '_title';
        $firstname = ($typeAddress === null) ? 'firstname' : strtolower($typeAddress->getValue()) . '_firstname';
        $lastname = ($typeAddress === null) ? 'lastname' : strtolower($typeAddress->getValue()) . '_lastname';
        $company = ($typeAddress === null) ? 'company' : strtolower($typeAddress->getValue()) . '_company';
        $phone = ($typeAddress === null) ? 'phone' : strtolower($typeAddress->getValue()) . '_phone';
        $address = ($typeAddress === null) ? 'address' : strtolower($typeAddress->getValue()) . '_address';
        $address_2 = ($typeAddress === null) ? 'address_2' : strtolower($typeAddress->getValue()) . '_address_2';
        $zipcode = ($typeAddress === null) ? 'zipcode' : strtolower($typeAddress->getValue()) . '_zipcode';
        $city = ($typeAddress === null) ? 'city' : strtolower($typeAddress->getValue()) . '_city';
        $country = ($typeAddress === null) ? 'country' : strtolower($typeAddress->getValue()) . '_country';
        $comment = ($typeAddress === null) ? 'comment' : strtolower($typeAddress->getValue()) . '_comment';
        $division = ($typeAddress === null) ? 'division_code' : strtolower($typeAddress->getValue()) . '_division_code';

        $addressBook->setUser($user);
        (\array_key_exists($label, $data) === true) ? $addressBook->setLabel($data[$label] ?? '') : '';
        (\array_key_exists($title, $data) ===  true && \is_string($data[$title]) === true) ? $addressBook->setTitle($data[$title]) : '';
        (\array_key_exists($firstname, $data) === true && \is_string($data[$firstname]) === true) ? $addressBook->setFirstname($data[$firstname]) : '';
        (\array_key_exists($lastname, $data) === true && \is_string($data[$lastname]) === true) ? $addressBook->setLastname($data[$lastname]) : '';
        (\array_key_exists($company, $data) === true && \is_string($data[$company]) === true) ? $addressBook->setCompany($data[$company]) : '';
        (\array_key_exists($phone, $data) === true && \is_string($data[$phone]) === true) ? $addressBook->setPhone($data[$phone]) : '';
        (\array_key_exists($address, $data) === true && \is_string($data[$address]) === true) ? $addressBook->setAddress($data[$address]) : '';
        (\array_key_exists($address_2, $data) === true && \is_string($data[$address_2]) === true) ? $addressBook->setAddress2($data[$address_2]) : '';
        (\array_key_exists($zipcode, $data) === true && \is_string($data[$zipcode]) === true) ? $addressBook->setZipCode($data[$zipcode]) : '';
        (\array_key_exists($city, $data) === true && \is_string($data[$city]) === true) ? $addressBook->setCity($data[$city]) : '';
        (\array_key_exists($comment, $data)   === true && \is_string($data[$comment]) === true) ?  $addressBook->setComment($data[$comment]) : '';
        (\array_key_exists($division, $data)  === true && \is_string($data[$division]) === true) ? $addressBook->setDivision($data[$division]) : '';
        (\array_key_exists($country, $data)  === true && \is_string($data[$title]) === true) ? $addressBook->setCountry(\strtoupper($data[$country])) : '';

        $addressBook = $this->repository->save($addressBook);

        if (\array_key_exists('principal_address', $data) === true) {
            $addressData = new UserAddress([
                'address_id' => $addressBook->getId(),
                'label'      => $data[$label],
                'title'      => $data[$title],
                'firstname'  => $data[$firstname],
                'lastname'   => $data[$lastname],
                'company'    => $data[$company],
                'phone'      => $data[$phone],
                'address'    => $data[$address],
                'address_2'  => $data[$address_2],
                'zipcode'    => $data[$zipcode],
                'city'       => $data[$city],
                'country'    => (\array_key_exists($country, $data)  === true) ? \strtoupper($data[$country]) : '',
                'comment'    => $data[$comment],
                'division_code' => (\array_key_exists($division, $data) === true) ? $data[$division] : '',
            ]);

            switch ($data['principal_address']) {
                case AddressStatus::BILLING()->getValue():
                    $user->setBillingAddress($addressData);
                    break;
                case AddressStatus::SHIPPING()->getValue():
                    $user->setShippingAddress($addressData);
                    break;
                case AddressStatus::BILLING_SHIPPING()->getValue():
                    $user->setShippingAddress($addressData);
                    $user->setBillingAddress($addressData);
                    break;
            }

            $this->userService->updateAddresses(
                $user->getUserId(),
                $user->getBillingAddress()->getAllFields(),
                $user->getShippingAddress()->getAllFields()
            );
        }
        // Send notifications to customer
        if ($notifyUser === true) {
            $this->eventDispatcher->dispatch(new UserProfileUpdated($user), UserProfileUpdated::class);
        }

        return $addressBook->getId();
    }

    public function removeAddressFromProfile(int $userId, string $id): void
    {
        $emptyAddress = [
            'address_id'    => '',
            'label'         => '',
            'title'         => '',
            'firstname'     => '',
            'lastname'      => '',
            'company'       => '',
            'phone'         => '',
            'address'       => '',
            'address_2'     => '',
            'zipcode'       => '',
            'city'          => '',
            'country'       => '',
            'comment'       => '',
            'division_code' => '',
        ];

        $user = $this->userService->get($userId);
        $b_address_id = $user->getBillingAddress()->getFieldValue('address_id');
        $s_address_id = $user->getShippingAddress()->getFieldValue('address_id');
        if ($b_address_id === $id) {
            $user->setBillingAddress(new UserAddress($emptyAddress));
        }

        if ($s_address_id === $id) {
            $user->setShippingAddress(new UserAddress($emptyAddress));
        }

        $this->userService->updateAddresses(
            $user->getUserId(),
            $user->getBillingAddress()->getAllFields(),
            $user->getShippingAddress()->getAllFields()
        );
    }

    public function delete(int $userId, string $id): void
    {
        $addressBook = $this->repository->findOneById($id);
        if (null === $addressBook) {
            throw NotFound::fromId('AddressBook', $id);
        }

        $this->removeAddressFromProfile($userId, $id);
        $this->repository->delete($id);
    }
}
