<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\User;

class UserAddress
{
    /**
     * Field value indexed by the field name.
     */
    private $fields;

    public function __construct(array $fields)
    {
        $this->fields = $fields;
    }

    public function getFieldNames(): array
    {
        return array_keys($this->fields);
    }

    public function getFieldValue(string $fieldName)
    {
        if (!\array_key_exists($fieldName, $this->fields)) {
            throw new \InvalidArgumentException(
                'Unknown address field ' . $fieldName . ', available fields: ' . implode(', ', $this->getFieldNames())
            );
        }

        return $this->fields[$fieldName];
    }

    public function getAllFields(): array
    {
        return $this->fields;
    }

    public function has(string $fieldName): bool
    {
        return \array_key_exists($fieldName, $this->fields);
    }
}
