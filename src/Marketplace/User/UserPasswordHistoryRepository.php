<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\User;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;

class UserPasswordHistoryRepository extends ServiceEntityRepository
{
    public function save(UserPasswordHistory $userPasswordHistory): UserPasswordHistory
    {
        $this->getEntityManager()->persist($userPasswordHistory);
        $this->getEntityManager()->flush();

        return $userPasswordHistory;
    }

    public function findCountPassword(User $user): int
    {
        return (int) $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->select("count(UserPasswordHistory.user)")
            ->from(UserPasswordHistory::class, "UserPasswordHistory")
            ->where("UserPasswordHistory.user = :user")
            ->setParameter('user', $user)
            ->getQuery()
            ->getSingleScalarResult()
            ;
    }

    public function remove(UserPasswordHistory $firstPassword): void
    {
        $this->getEntityManager()->remove($firstPassword);
        $this->getEntityManager()->flush();
    }
}
