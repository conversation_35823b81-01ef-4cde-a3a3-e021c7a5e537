<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\User;

class UserProfile
{
    private int $profileId;
    private int $userId;
    private string $profileType;
    private string $externalIdentifier;
    private bool $isProfessional;
    private string $legalIdentifier;

    public function setProfileId(int $profileId): self
    {
        $this->profileId = $profileId;

        return $this;
    }

    public function getProfileId(): int
    {
        return $this->profileId;
    }

    public function getUserId(): int
    {
        return $this->userId;
    }

    public function setUserId($userId): self
    {
        $this->userId = $userId;

        return $this;
    }

    public function getProfileType(): string
    {
        return $this->profileType;
    }

    public function setProfileType(string $profileType): self
    {
        $this->profileType = $profileType;

        return $this;
    }

    public function getExternalIdentifier(): string
    {
        return $this->externalIdentifier;
    }

    public function setExternalIdentifier(string $externalIdentifier): self
    {
        $this->externalIdentifier = $externalIdentifier;

        return $this;
    }

    public function getIsProfessional(): bool
    {
        return $this->isProfessional;
    }

    public function setIsProfessional(bool $isProfessional): self
    {
        $this->isProfessional = $isProfessional;

        return $this;
    }


    public function getLegalIdentifier(): string
    {
        return $this->legalIdentifier;
    }

    public function setLegalIdentifier(string $legalIdentifier): self
    {
        $this->legalIdentifier = $legalIdentifier;

        return $this;
    }
}
