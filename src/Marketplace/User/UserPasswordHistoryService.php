<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\User;

class UserPasswordHistoryService
{
    private UserPasswordHistoryRepository $userPasswordHistoryRepository;
    private int $previousPasswordsDifferenceLimit;

    public function __construct(
        UserPasswordHistoryRepository $userPasswordHistoryRepository,
        int $previousPasswordsDifferenceLimit
    ) {
        $this->userPasswordHistoryRepository = $userPasswordHistoryRepository;
        $this->previousPasswordsDifferenceLimit = $previousPasswordsDifferenceLimit;
    }

    public function save(UserPasswordHistory $userPasswordHistory): void
    {
        $count = $this->userPasswordHistoryRepository->findCountPassword($userPasswordHistory->getUser());

        if ($count >= 10) {
            $this->removeFirstPassword($userPasswordHistory->getUser());
        }

        $this->userPasswordHistoryRepository->save($userPasswordHistory);
    }

    public function findFirstPasswordByUser(User $user): ?UserPasswordHistory
    {
        $result = $this->userPasswordHistoryRepository->findBy(['user' => $user], ['createdAt' => 'asc']);

        return \count($result) > 0 ? $result[0] : null;
    }

    public function removeFirstPassword(User $user): void
    {
        $firstPassword = $this->findFirstPasswordByUser($user);
        if ($firstPassword instanceof UserPasswordHistory === true) {
            $this->userPasswordHistoryRepository->remove($firstPassword);
        }
    }

    public function isPasswordExist(User $user, string $password): bool
    {
        if (\is_int($this->previousPasswordsDifferenceLimit) === true
            && $this->previousPasswordsDifferenceLimit > 0
        ) {
            $userPasswordsHistory = $this->userPasswordHistoryRepository->findBy(
                [
                    'user' => $user
                ],
                [
                    'id' => 'desc'
                ],
                $this->previousPasswordsDifferenceLimit
            );

            if (\count($userPasswordsHistory) > 0) {
                foreach ($userPasswordsHistory as $userPassword) {
                    if (\password_verify($password, $userPassword->getPassword()) === true) {
                        return true;
                    }
                }
            }
        }

        return false;
    }
}
