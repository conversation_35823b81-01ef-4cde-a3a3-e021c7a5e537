<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\User;

use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Wizacha\Marketplace\User\Event\UserProfileBlocked;
use Wizacha\Status;

class UserSecurity
{
    protected UserRepository $userRepository;
    protected EventDispatcherInterface $eventDispatcher;
    protected bool $blockAccountFeatureActivated;
    protected int $blockAccountMaxAttempts;

    public function __construct(
        UserRepository $userRepository,
        EventDispatcherInterface $eventDispatcher,
        string $blockAccountFeatureActivated,
        string $blockAccountMaxAttempts
    ) {
        $this->userRepository = $userRepository;
        $this->blockAccountFeatureActivated = \filter_var($blockAccountFeatureActivated, FILTER_VALIDATE_BOOLEAN);
        $this->blockAccountMaxAttempts = \filter_var($blockAccountMaxAttempts, FILTER_VALIDATE_INT);
        $this->eventDispatcher = $eventDispatcher;
    }

    public function isBlockAccountFeatureActivated(): bool
    {
        return $this->blockAccountFeatureActivated;
    }

    public function registerFailedLoginAttempt(string $email): int
    {
        if (false === $this->blockAccountFeatureActivated) {
            return 0;
        }

        $user = $this->userRepository->findOneByEmail($email);
        $user->setFailedLoginCount($user->getFailedLoginCount() + 1);
        $remainingTry = $this->blockAccountMaxAttempts - $user->getFailedLoginCount();

        // block account
        if ($remainingTry <= 0) {
            $user->setStatus(Status::DISABLED);
            $this->eventDispatcher->dispatch(new UserProfileBlocked($user));
        }
        $this->userRepository->save($user);

        return $remainingTry;
    }

    public function resetAttemptsCount(string $email): void
    {
        if (false === $this->blockAccountFeatureActivated) {
            return;
        }

        $user = $this->userRepository->findOneByEmail($email);
        $user->setFailedLoginCount(0);
        $this->userRepository->save($user);
    }
}
