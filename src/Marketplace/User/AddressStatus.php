<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\User;

use MyCLabs\Enum\Enum;

/**
 * @method static AddressStatus NON()
 * @method static AddressStatus BILLING()
 * @method static AddressStatus SHIPPING()
 * @method static AddressStatus BILLING_SHIPPING()
 */
class AddressStatus extends Enum
{
    protected const NON = 'N';
    protected const BILLING = 'B';
    protected const SHIPPING = 'S';
    protected const BILLING_SHIPPING = 'BS';
}
