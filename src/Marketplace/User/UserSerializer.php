<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\User;

use Doctrine\Common\Collections\Collection;
use Wizacha\Marketplace\User\Exception\InvalidDataForDeserializationException;

class UserSerializer
{
    public const EMPTY_SERIALIZED_USER = [
        "id" => null,
        "email" => null,
        "firstName" => null,
        "lastName" => null,
    ];

    public const EMPTY_SERIALIZED_USER_COUNT = 4;

    public const BIRTHDAY_FORMAT = 'Y-m-d';

    private UserService $userService;
    private bool $isQuoteRequestEnabled;

    public function __construct(UserService $userService, bool $isQuoteRequestEnabled)
    {
        $this->userService = $userService;
        $this->isQuoteRequestEnabled = $isQuoteRequestEnabled;
    }

    public function serialize(User $user): array
    {
        $serializedUser =  [
            'id' => $user->getUserId(),
            'title' => $user->getTitle() ? $user->getTitle()->getValue() : null,
            'email' => $user->getEmail(),
            'type' => $user->getUserType(),
            'lang' => (string) $user->getLocale(),
            'companyId' => $user->getCompanyId(),
            'pendingCompanyId' => $user->getPendingCompanyId(),
            'firstName' => $user->getFirstname(),
            'lastName' => $user->getLastname(),
            'phone' => $user->getPhone(),
            'birthday' => $user->getBirthday() !== null ? $user->getBirthday()->format(self::BIRTHDAY_FORMAT) : null,
            'currencyCode' => $user->getCurrencyCode(),
            'loyaltyIdentifier' => $user->getLoyaltyIdentifier(),
            'addresses' => [
                'billing' => $this->serializeAddress($user->getBillingAddress()),
                'shipping' => $this->serializeAddress($user->getShippingAddress()),
            ],
            'externalIdentifier' => $user->getExternalIdentifier(),
            'isProfessional' => $user->isProfessional(),
            'intraEuropeanCommunityVAT' => $user->getIntraEuropeanCommunityVAT(),
            'company' => $user->getCompany(),
            'jobTitle' => $user->getJobTitle(),
            'comment' => $user->getComment(),
            'legalIdentifier' => $user->getLegalIdentifier(),
            'nationalities' => $this->serializeNationalities($user->getNationalities()),
            'registeredAt' => (new \DateTime())->setTimestamp($user->getTimestamp())->format(\DateTime::RFC3339),
            'extra' => $user->getExtra()
        ];

        if (true === $this->isQuoteRequestEnabled) {
            $serializedUser['quoteRequestSelectionIds'] = $user->getQuoteRequestSelectionIds();
        }

        $passwordExpiryTimeLeft = $this->userService->calculatePasswordExpiryTimeLeft($user->getPasswordChangeTimestamp());
        if ($passwordExpiryTimeLeft !== null) {
            $serializedUser['passwordExpiryTimeLeft'] = $passwordExpiryTimeLeft;
        }

        return $serializedUser;
    }

    /**
     * @param array $data A serialized representation of a user
     * @param User $user An already empty, partial or hydrated user
     *
     * @throws \Wizacha\Marketplace\User\Exception\InvalidDataForDeserializationException
     */
    public function deserialize(array $data, User $user = null): User
    {
        $this->assertDataForDeserializeIsValid($data);

        if (null === $user) {
            $user = $this->userService->createNewUser();
        }

        return $user
            ->setUserId($data["id"])
            ->setEmail($data["email"])
            ->setFirstname($data["firstName"])
            ->setLastname($data["lastName"])
        ;
    }

    private function assertDataForDeserializeIsValid($data)
    {
        if (!\is_array($data) || static::EMPTY_SERIALIZED_USER_COUNT !== \count(array_intersect_key($data, static::EMPTY_SERIALIZED_USER))) {
            throw new InvalidDataForDeserializationException(sprintf(
                "The given data must be an array composed of these 4 entries: %s",
                implode(", ", array_keys(static::EMPTY_SERIALIZED_USER))
            ));
        }
    }

    private function serializeAddress(UserAddress $address)
    {
        $data = [];
        foreach ($address->getFieldNames() as $fieldName) {
            $data[$fieldName] = $address->getFieldValue($fieldName);
        }

        // To keep BC in API, we return numeric fields (now title and company)
        try {
            $data[38] = $data[37] = $address->getFieldValue('title') !== UserTitle::MR ? 3 : 4;
        } catch (\InvalidArgumentException $e) {
        }

        try {
            $data[39] = $data[40] = $address->getFieldValue('company');
        } catch (\InvalidArgumentException $e) {
        }

        return $data;
    }

    /** @return array */
    private function serializeNationalities(Collection $nationalities): array
    {
        $data = [];
        foreach ($nationalities as $nationality) {
            $data[] = $nationality->getCodeA3();
        }

        return $data;
    }
}
