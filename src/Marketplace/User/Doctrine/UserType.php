<?php

/**
 *  <AUTHOR> DevTeam <<EMAIL>>
 *  @copyright   Copyright (c) Wizacha
 *  @license     Proprietary
 */

namespace Wizacha\Marketplace\User\Doctrine;

use Acelaya\Doctrine\Type\AbstractPhpEnumType;
use Wizacha\Marketplace\User\UserType as WizachaUserType;

class UserType extends AbstractPhpEnumType
{
    protected $enumType = WizachaUserType::class;

    protected function getSpecificName(): string
    {
        return 'user_type';
    }
}
