<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\User;

use Broadway\Domain\DomainMessage;
use Broadway\EventHandling\EventListenerInterface;
use Wizacha\Marketplace\Basket\Event\BasketWasPickedUp;
use Wizacha\Marketplace\DomainService\UserDomainService;

class EventListener implements EventListenerInterface
{
    /**
     * @var UserDomainService
     */
    protected $userDomainService;

    public function __construct(UserDomainService $userDomainService)
    {
        $this->userDomainService = $userDomainService;
    }

    public function applyBasketWasPickedUp(BasketWasPickedUp $basket)
    {
        if ($user = $this->userDomainService->getLoggedUser()) {
            $user->setBasketId($basket->getBasketId());
        }
    }

    /**
     * Copy paste from abstract Class Projector to keep same naming convention
     * {@inheritDoc}
     *
     * Matthieu: méthode bougée d'une ancienne classe, je ne sais pas pourquoi on
     * fait ça.
     */
    public function handle(DomainMessage $domainMessage)
    {
        $event  = $domainMessage->getPayload();
        $method = $this->getHandleMethod($event);

        if (! method_exists($this, $method)) {
            return;
        }

        $this->$method($event, $domainMessage);
    }

    private function getHandleMethod($event)
    {
        $classParts = explode('\\', \get_class($event));

        return 'apply' . end($classParts);
    }
}
