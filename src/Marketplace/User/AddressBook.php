<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\User;

use Wizacha\Marketplace\Address\Exception\AddressFieldsException;

/**
 * AddressBook
 */
class AddressBook implements \JsonSerializable
{
    protected const REQUIRED_FIELDS = [
        'firstname',
        'lastname',
        'address',
        'city',
        'zipcode',
    ];

    protected const REQUIRED_FIELDS_WITH_AVAILABLE_OFFERS = [
        'firstname',
        'lastname',
        'address',
        'city',
        'zipcode',
        'division_code',
    ];

    /** @var string */
    private $id;

    /** @var string|null */
    private $label;

    /** @var string|null */
    private $comment;

    /**@var string|null */
    private $title;

    /**@var string|null */
    private $firstname;

    /**@var string|null */
    private $lastname;

    /**@var string|null */
    private $company;

    /**@var string|null */
    private $address;

    /**@var string|null */
    private $address2;

    /**@var string|null */
    private $city;

    /**@var string|null */
    private $county;

    /**@var string|null */
    private $state;

    /**@var string|null */
    private $country;

    /**@var string|null */
    private $zipcode;

    /**@var string|null */
    private $phone;

    /**@var string|null */
    private $division;

    /** @var User */
    private $user;

    public function __construct()
    {
        $this->id = '';
        $this->firstname = '';
        $this->lastname = '';
        $this->company = '';
        $this->address = '';
        $this->address2 = '';
        $this->city = '';
        $this->county = '';
        $this->state = '';
        $this->country = '';
        $this->zipcode = '';
        $this->phone = '';
    }

    public function setId(string $id): self
    {
        $this->id = $id;

        return $this;
    }

    /** @return string */
    public function getId(): string
    {
        return $this->id;
    }

    public function setLabel(?string $label): self
    {
        $this->label = $label;

        return $this;
    }

    /** @return string|null */
    public function getLabel(): ?string
    {
        return $this->label;
    }

    public function setComment(?string $comment): self
    {
        $this->comment = $comment;

        return $this;
    }

    /** @return string|null */
    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function setTitle(?string $title): self
    {
        $this->title = $title;

        return $this;
    }

    /** @return string|null  */
    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setFirstname(?string $firstname): self
    {
        $this->firstname = $firstname;

        return $this;
    }

    /** @return string|null  */
    public function getFirstname(): ?string
    {
        return $this->firstname;
    }


    public function setLastname(?string $lastname): self
    {
        $this->lastname = $lastname;

        return $this;
    }

    /** @return string|null  */
    public function getLastName()
    {
        return $this->lastname;
    }

    public function setCompany(?string $company): self
    {
        $this->company = $company;

        return $this;
    }

    /** @return string|null  */
    public function getCompany(): ?string
    {
        return $this->company;
    }

    public function setAddress(?string $address): self
    {
        $this->address = $address;

        return $this;
    }

    /** @return string|null  */
    public function getAddress(): ?string
    {
        return $this->address;
    }

    public function setAddress2(?string $address2): self
    {
        $this->address2 = $address2;

        return $this;
    }

    /** @return string|null  */
    public function getAddress2(): ?string
    {
        return $this->address2;
    }

    public function setCity(?string $city): self
    {
        $this->city = $city;

        return $this;
    }

    /** @return string|null  */
    public function getCity(): ?string
    {
        return $this->city;
    }

    public function setCounty(?string $county): self
    {
        $this->county = $county;

        return $this;
    }

    /** @return string|null  */
    public function getCounty(): ?string
    {
        return $this->county;
    }

    public function setState(?string $state): self
    {
        $this->state = $state;

        return $this;
    }

    /** @return string|null  */
    public function getState(): ?string
    {
        return $this->state;
    }

    public function setCountry(?string $country): self
    {
        $this->country = $country;

        return $this;
    }

    /** @return string|null  */
    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setZipCode(?string $zipcode): self
    {
        $this->zipcode = $zipcode;

        return $this;
    }

    /** @return string|null  */
    public function getZipCode(): ?string
    {
        return $this->zipcode;
    }

    public function setPhone(?string $phone): self
    {
        $this->phone = $phone;

        return $this;
    }

    /** @return string|null  */
    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function setDivision(?string $division): self
    {
        $this->division = $division;

        return $this;
    }

    /** @return string|null  */
    public function getDivision(): ?string
    {
        return $this->division;
    }

    public function setUser(User $user): self
    {
        $this->user = $user;

        if ($user instanceof User) {
            $user->addAddressBook($this);
        }

        return $this;
    }

    /** @return User  */
    public function getUser(): User
    {
        return $this->user;
    }

    /** @return mixed[] */
    public function jsonSerialize(): array
    {
        return [
            'id' => $this->getId(),
            'label' => $this->getLabel(),
            'firstname' => $this->getFirstName(),
            'lastname' => $this->getLastName(),
            'title' => $this->getTitle(),
            'company' => $this->getCompany(),
            'phone' => $this->getPhone(),
            'address' => $this->getAddress(),
            'address_2' => $this->getAddress2(),
            'city' => $this->getCity(),
            'zipcode' => $this->getZipCode(),
            'country' => $this->getCountry(),
            'division_code' => $this->getDivision() ?? '',
            'comment' => $this->getComment(),
        ];
    }

    public function assertValid(bool $featureAvailableOffers = false): self
    {
        $fields = $this->jsonSerialize();
        if ($featureAvailableOffers === true) {
            $invalidFields = array_filter(static::REQUIRED_FIELDS_WITH_AVAILABLE_OFFERS, function (string $name) use ($fields): bool {
                return "" === trim($fields[$name]);
            });
        } else {
            $invalidFields = array_filter(static::REQUIRED_FIELDS, function (string $name) use ($fields): bool {
                return "" === trim($fields[$name]);
            });
        }

        if (\count($invalidFields) > 0) {
            throw new AddressFieldsException($invalidFields);
        }

        return $this;
    }
}
