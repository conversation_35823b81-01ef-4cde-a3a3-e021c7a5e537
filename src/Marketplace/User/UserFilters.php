<?php

/**
 * @copyright Copyright (c) Wizacha
 * @license Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\User;

use Wizacha\Marketplace\Traits\VariablesTypeValidator;

class UserFilters
{
    use VariablesTypeValidator;

    private ?string $name;
    private ?string $email;
    private ?string $phone;
    private ?string $external;
    private ?string $loyalty;
    private ?bool $isProfessional;
    private ?int $companyId;
    private ?int $elements;
    private int $page;

    /** @var string[]|null */
    private ?array $type;

    /** @var mixed[]|null */
    private ?array $extra;

    /** @var mixed[]|null */
    private ?array $extraStartWith;

    public function __construct(array $data)
    {
        \array_key_exists('name', $data) === true  ? $this->setName($data['name']) : $this->setName(null);
        \array_key_exists('email', $data) === true ? $this->setEmail($data['email']) : $this->setEmail(null);
        \array_key_exists('phone', $data) === true ? $this->setPhone($data['phone']) : $this->setPhone(null);
        \array_key_exists('external', $data) === true
            ? $this->setExternal($data['external'])
            : $this->setExternal(null);
        \array_key_exists('loyalty', $data) === true ? $this->setLoyalty($data['loyalty']) : $this->setLoyalty(null);
        \array_key_exists('isProfessional', $data) === true
            ? $this->setIsProfessional($data['isProfessional'])
            : $this->setIsProfessional(null);
        \array_key_exists('companyId', $data) === true
            ? $this->setCompanyId($data['companyId'])
            : $this->setCompanyId(null);
        \array_key_exists('elements', $data) === true ? $this->setElements(\intval($data['elements'])) : $this->setElements(null);
        \array_key_exists('page', $data) === true ? $this->setPage(\intval($data['page'])) : $this->setPage(0);
        \array_key_exists('type', $data) === true ? $this->setType($data['type']) : $this->setType(null);
        \array_key_exists('extra', $data) === true
            ? $this->setExtra($data['extra'])
            : $this->setExtra(null);
        \array_key_exists('extraStartWith', $data) === true
            ? $this->setExtraStartWith($data['extraStartWith'])
            : $this->setExtraStartWith(null);
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function setPhone(?string $phone): self
    {
        $this->phone = $phone;

        return $this;
    }

    public function getExternal(): ?string
    {
        return $this->external;
    }

    public function setExternal(?string $external): self
    {
        $this->external = $external;

        return $this;
    }

    public function getLoyalty(): ?string
    {
        return $this->loyalty;
    }

    public function setLoyalty(?string $loyalty): self
    {
        $this->loyalty = $loyalty;

        return $this;
    }

    public function getIsProfessional(): ?bool
    {
        return $this->isProfessional;
    }

    public function setIsProfessional(?string $isProfessional): self
    {
        $this->isProfessional = $isProfessional !== null ? (bool) $isProfessional : $isProfessional;

        return $this;
    }

    /** @return int|null */
    public function getCompanyId(): ?int
    {
        return $this->companyId;
    }

    public function setCompanyId(?string $companyId): self
    {
        $this->checkIntType($companyId);
        $this->companyId = $companyId !== null ? \intval($companyId) : $companyId;

        return $this;
    }

    /** @return string[]|null */
    public function getType(): ?array
    {
        return $this->type;
    }

    /** @param string[]|null $type */
    public function setType(?array $type): self
    {
        $this->checkArrayOfUserType($type);
        $this->type = $type;

        return $this;
    }

    public function getElements(): ?int
    {
        return $this->elements;
    }

    public function setElements(?int $elements): self
    {
        $this->elements = $elements;

        return $this;
    }

    public function getPage(): int
    {
        return $this->page;
    }

    public function setPage(int $page): self
    {
        $this->page = $page;

        return $this;
    }

    /** @return mixed[]|null */
    public function getExtra(): ?array
    {
        return $this->extra;
    }

    /** @param mixed[]|null $extra */
    public function setExtra(?array $extra): self
    {
        $this->extra = $extra;

        return $this;
    }

    /** @return mixed[]|null */
    public function getExtraStartWith(): ?array
    {
        return $this->extraStartWith;
    }

    /** @param mixed[]|null $extraStartWith */
    public function setExtraStartWith(?array $extraStartWith): self
    {
        $this->extraStartWith = $extraStartWith;

        return $this;
    }
}
