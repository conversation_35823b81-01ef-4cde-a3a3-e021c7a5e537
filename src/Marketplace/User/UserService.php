<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\User;

use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\Request;
use Tygh\Languages\Languages;
use Wizacha\Component\BytesGenerator\BytesGeneratorInterface;
use Wizacha\Component\Locale\Locale;
use Wizacha\FeatureFlag\FeatureFlagService;
use Wizacha\Marketplace\Currency\CurrencyService;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Subscription\Subscription;
use Wizacha\Marketplace\Subscription\SubscriptionRepository;
use Wizacha\Marketplace\User\Event\UserAskedToRecoverPassword;
use Wizacha\Marketplace\User\Event\UserProfileUpdated;
use Wizacha\Marketplace\User\Event\Yavin\UserDeactivated;
use Wizacha\Marketplace\User\Event\Yavin\UserActivated;
use Wizacha\Marketplace\User\Event\Yavin\UserUpdated;
use Wizacha\Marketplace\User\Exception\CannotChangePassword;
use Wizacha\Marketplace\User\Exception\CannotCreateUser;
use Wizacha\Marketplace\User\Exception\EmailAlreadyUsed;
use Wizacha\Marketplace\User\Exception\InvalidFieldException;
use Wizacha\Marketplace\User\Exception\InvalidProfileFields;
use Wizacha\Marketplace\User\Exception\PasswordDontMatch;
use Wizacha\Marketplace\User\Exception\PasswordExist;
use Wizacha\Marketplace\User\Exception\PasswordFormatNotValid;
use Wizacha\Marketplace\User\Exception\UserDoesNotExist;
use Wizacha\Marketplace\User\Exception\UserAlreadyExists;
use Wizacha\Registry;
use Wizacha\Status;
use Wizacha\Cscart\FnFunctions;
use Wizacha\Marketplace\Country\CountryService;

class UserService
{
    public const DEFAULT_PAGE_SIZE = 20;

    /** numbers of days for notifying user before password get expired */
    public const PASSWORD_RENEWAL_TIME_NOTIFY_USER = 5;

    private UserRepository $repository;
    private EventDispatcherInterface $eventDispatcher;
    private CurrencyService $currencyService;
    private SubscriptionRepository $subscriptionRepository;
    private FnFunctions $fnFunctions;
    private CountryService $countryService;
    private FeatureFlagService $featureFlagService;
    private EKeyRepository $eKeyRepository;
    private BytesGeneratorInterface $bytesGenerator;
    private int $passwordRenewalTimeLimit;

    public function __construct(
        UserRepository $userRepository,
        EventDispatcherInterface $eventDispatcher,
        CurrencyService $currencyService,
        SubscriptionRepository $subscriptionRepository,
        FnFunctions $fnFunctions,
        CountryService $countryService,
        FeatureFlagService $featureFlagService,
        EKeyRepository $eKeyRepository,
        BytesGeneratorInterface $bytesGenerator,
        int $passwordRenewalTimeLimit
    ) {
        $this->repository = $userRepository;
        $this->eventDispatcher = $eventDispatcher;
        $this->currencyService = $currencyService;
        $this->subscriptionRepository = $subscriptionRepository;
        $this->fnFunctions = $fnFunctions;
        $this->countryService = $countryService;
        $this->featureFlagService = $featureFlagService;
        $this->eKeyRepository = $eKeyRepository;
        $this->bytesGenerator = $bytesGenerator;
        $this->passwordRenewalTimeLimit = $passwordRenewalTimeLimit;
    }

    /**
     * @param int $id
     * @return User
     * @throws NotFound
     */
    public function get($id): User
    {
        return $this->repository->get($id);
    }

    public function save(User $user)
    {
        $this->repository->save($user);
    }

    /**
     * @param int $page
     * @param null|integer $pageSize If null, the default size is used
     * @return User[]
     */
    public function findWithPagination($page, $pageSize = null)
    {
        return $this->repository->findWithPagination($page * $pageSize, $pageSize);
    }

    public function count(): int
    {
        return $this->repository->count();
    }

    public function clearEntityManager(): void
    {
        $this->repository->getEntityManager()->flush();
        $this->repository->getEntityManager()->clear();
    }

    /**
     * Update an user with cscart array
     * @throws EmailAlreadyUsed
     */
    public function updateUserProfile($userId, array $userData, bool $sameBillingShippingAddresses, bool $sendNotification)
    {
        $profileId = db_get_field("SELECT profile_id FROM ?:user_profiles WHERE user_id = ?i AND profile_type = 'P'", $userId);
        $currentUserData = fn_get_user_info($userId, true, $profileId);
        if (fn_is_user_exists($userId, $userData)) {
            throw new EmailAlreadyUsed();
        }
        $userData = fn_array_merge($currentUserData, $userData);
        $userData['user_type'] = $currentUserData['user_type'] ? : 'C';

        $userData = fn_fill_contact_info_from_address($userData);
        if ($sameBillingShippingAddresses) {
            $profileFields = fn_get_profile_fields('O');
            fn_fill_address($userData, $profileFields);
        }
        $auth = Registry::defaultInstance()->container->get('session')->get('auth');
        fn_update_user($userId, $userData, $auth, !$sameBillingShippingAddresses, $sendNotification);
    }

    public function patch(User $user, array $data): self
    {
        if (\is_string($data['currencyCode'])) {
            $this->currencyService->assertCurrencyExist($data['currencyCode']);
        }
        $this->save($user->fromArray($data));

        return $this;
    }

    /**
     * Create an administrator account.
     *
     * @return int The user ID.
     */
    public function createAdmin(string $email, string $password, string $apiKey = null): int
    {
        $data = [
            'status' => 'A',
            'user_type' => \Wizacha\User::ADMIN_TYPE,
            'email' => $email,
            'password1' => $password,
            'password2' => $password,
            'api_key' => $apiKey ?? $this->createApiToken(),
            'company_id' => 0,
            'firstname' => '',
            'lastname' => '',
        ];

        $id = fn_update_user(null, $data, $auth = [], false, false);
        if ($id === false) {
            throw new \Exception('Error while creating user');
        }

        if (\is_array($id)) {
            // fn_update_user() may return an array(user_id, profile_id) for vendor/admin creation.
            return $id[0];
        }

        return $id;
    }

    public function recoverPassword(string $email, ?string $recoverBaseUrl = null, bool $notifyUserUpdate = false)
    {
        $ttl = strtotime("+1 day");
        if ($this->isPasswordRecoveryForceChangeActivated() === true) {
            $ttl = strtotime("+1 hour");
        }

        if ($user = $this->repository->findOneByEmail($email)) {
            $_data = [
                'object_id' => $user->getUserId(),
                'object_string' => '',
                'object_type' => 'U',
                'ekey' => md5($this->bytesGenerator->getBytes(32)),
                'ttl' => $ttl
            ];

            //delete previous ekeys for user
            if ($this->isPasswordRecoveryForceChangeActivated() === true) {
                $this->eKeyRepository->removeByUserId($user->getUserId());
            }

            $this->eKeyRepository->save($_data);

            $this->eventDispatcher->dispatch(
                new UserAskedToRecoverPassword($user, $_data['ekey'], $recoverBaseUrl, $this->isPasswordRecoveryForceChangeActivated()),
                UserAskedToRecoverPassword::class
            );

            if ($notifyUserUpdate === true) {
                $this->eventDispatcher->dispatch(new UserProfileUpdated($user), UserProfileUpdated::class);
            }
        }
    }

    /**
     * @throws CannotCreateUser
     * @throws UserAlreadyExists
     * @throws PasswordFormatNotValid
     */
    public function createUser(
        string $email,
        string $password,
        string $firstname = null,
        string $lastname = null,
        string $url = null,
        bool $notifyUser = true,
        ?string $lang = null
    ): int {
        $data = [
            'status' => Status::ENABLED,
            'user_type' => \Wizacha\User::CLIENT_TYPE,
            'email' => $email,
            'password1' => $password,
            'password2' => $password,
            'firstname' => $firstname,
            'lastname' => $lastname,
            'url' => $url,
        ];

        if (\is_null($lang) === false) {
            $data['lang_code'] = $lang;
        }

        $id = fn_update_user(null, $data, $auth = [], false, $notifyUser);
        if ($id === false) {
            $notifications = fn_get_notifications();

            if (!empty($notifications)) {
                $notification = reset($notifications);

                if ($notification['extra'] === 'user_exist') {
                    throw new UserAlreadyExists();
                }

                if ($notification['extra'] === 'password_format_invalid') {
                    throw new PasswordFormatNotValid(__('error_password_format_not_valid'));
                }
            }

            throw new CannotCreateUser($notification['message'] ?? '');
        }

        return reset($id);
    }

    /**
     * @throws CannotChangePassword
     * @throws PasswordFormatNotValid
     * @throws PasswordExist
     */
    public function changePassword(int $userId, string $password, bool $notifyUserUpdate = false)
    {
        $data = [
            'password1' => $password,
            'password2' => $password,
        ];

        if (false === $this->fnFunctions->fn_update_user($userId, $data, $auth = [], true, $notifyUserUpdate)) {
            $notifications = $this->fnFunctions->fn_get_notifications();

            $notification = reset($notifications);

            if (false !== $notification && $notification['extra'] === 'password_format_invalid') {
                throw new PasswordFormatNotValid(__('error_password_format_not_valid'));
            }

            if (false !== $notification && $notification['extra'] === 'error_password_difference_limit') {
                throw new PasswordExist($notification['message'] ?? '');
            }

            throw new CannotChangePassword($notification['message'] ?? ''); // There no such thing that an empty error message
        }
    }

    /**
     * @throws CannotChangePassword
     * @return bool True if the token is OK and the password has been changed. False otherwise
     */
    public function changePasswordWithRecoveryToken(string $token, string $password, bool $notifyUserUpdate = false): bool
    {
        $userId = $this->eKeyRepository->getUserId($token);

        if (!empty($userId)) {
            $this->changePassword((int) $userId, $password, $notifyUserUpdate);

            // Delete the token if the password has been changed
            $this->eKeyRepository->removeByEKey($token);

            return true;
        }

        return false;
    }

    public function updateAddresses(int $userId, array $billingAddress, array $shippingAddress)
    {
        $data = $this->updateAddressFields($billingAddress, [], 'b');
        $data = $this->updateAddressFields($shippingAddress, $data, 's');

        $auth = [];
        $shipToAnotherAddress = true;
        fn_update_user($userId, $data, $auth, $shipToAnotherAddress, false, false, false);
    }

    /**
     * @param mixed[] $addressFields
     * @param mixed[] $data
     * @return mixed[]
     */
    protected function updateAddressFields(array $addressFields, array $data, string $prefix = 'b'): array
    {
        foreach ($addressFields as $field => $value) {
            if (\is_int($field)) { // fields by ID, should be removed in the future
                $data['fields'][$field] = $value;
            } else {
                if ($field === 'country'
                    && $value !== null
                    && $this->countryService->isValidCountryCode($value) === false
                ) {
                    continue;
                }

                if ($field === 'country') {
                    // Always store country code in uppercase to avoid comparison problems
                    $data[$prefix . '_' . $field] = \strtoupper($value);
                } else {
                    $data[$prefix . '_' . $field] = $value;
                }
            }
        }

        return $data;
    }

    /**
     * Validate that fields in the user profile are correctly filled.
     *
     * @throws InvalidProfileFields
     */
    public function validateProfileFields(int $userId, int $profileId = null)
    {
        $userData = fn_get_user_info($userId, true, $profileId);

        // I think "O" means order -> check fields required for the checkout view
        $profileFields = fn_get_profile_fields('O');

        $missingFields = [];
        foreach (ProfileFieldSections::values() as $section) {
            $sectionName = $section->getValue();
            if (empty($profileFields[$sectionName])) {
                continue; // No fields configured in that section
            }

            foreach ($profileFields[$sectionName] as $field) {
                if ($field['required'] !== 'Y') {
                    continue; // field not required? ignore it
                }

                if (empty($userData[$field['field_name']])) {
                    $missingFields[] = $field['field_name'];
                }
            }
        }

        if (!empty($missingFields)) {
            throw new InvalidProfileFields($missingFields);
        }
    }

    public function enable(int $userId): void
    {
        $user = $this->get($userId);
        $user->setStatus(UserStatus::ACTIVE);

        $this->save($user);

        $this->eventDispatcher->dispatch(new UserActivated($user));
    }

    public function disable(int $userId): void
    {
        $user = $this->get($userId);
        $user->setStatus(UserStatus::INACTIVE);

        $this->eventDispatcher->dispatch(new UserDeactivated($user));

        $this->save($user);
    }

    public function getOAuthUser(string $email, OAuthToken $token, UserType $userType): User
    {
        $user = $this->repository->findOneByEmail($email);

        if ($user instanceof User === false) {
            $user = User::createFromOAuth($email, $token, $userType);

            if ($this->isApprovedUserByAdminActivated() === true) {
                $user->setStatus(Status::PENDING);
            }

            $user->setApiKey($this->createApiToken());
            $this->save($user);

            return $user;
        }

        if (!$user->hasOAuthToken()) {
//            throw new NonOAuthUserAlreadyExist("Cannot authenticate user through OAuth 2.0. A user with the same identifier already exists but is not linked to this OAuth 2.0 identity.");
        }

        // dans le cas où l'utilisateur existe déjà on ne mets à jour que son
        // token, on ne touche jamais à son type
        $user->refreshOAuthToken($token);
        $this->save($user);

        return $user;
    }

    public function CreateOrGetOAuthUser(string $email, ?string $token): User
    {
        $user = $this->repository->findOneByEmail($email);

        if (\is_null($user) === true) {
            throw new UserDoesNotExist("No user already exist: $email");
        }

        if ($user->hasOAuthToken() === false) {
            $user->setOAuthToken(
                new OAuthToken(
                    $this->createApiToken(),
                    ''
                )
            );
        } elseif ($user->getOAuthToken()->getToken() === $token) {
            $user->refreshOAuthToken(
                new OAuthToken(
                    $this->createApiToken(),
                    ''
                )
            );
        }

        $this->save($user);

        return $user;
    }

    public function updateIsProfessional(int $userId, bool $isProfessional): void
    {
        $data = ['is_professional' => $isProfessional ? '1' : '0'];
        $auth = [];
        fn_update_user($userId, $data, $auth, true, false, false, false);
    }

    /**
     * Hydrate des champs de l'user depuis un call API (POST / PUT)
     * Rajoutez les champs que vous voulez à la suite, j'ai pas refacto le controller parce que c'est hors périmètre du ticket
     * @throws InvalidFieldException
     */
    public function hydrateUserFromApiCall(User $user, Request $request): self
    {
        if ($request->request->has('lang')) {
            $lang = $request->request->get('lang');
            if (Languages::isActive($lang) === false) {
                throw new InvalidFieldException('lang', "The lang '$lang' is invalid or inactive.");
            }

            $user->setLocale(new Locale($lang));
        };

        return $this;
    }

    /**
     * @param mixed[] $data
     *
     * @reutnr mixed[]
     */
    public function convertDataForSql(User $user, array $data): array
    {
        $userProfiles = fn_get_all_user_profiles($user->getUserId());

        // Also retrieves email (it is not in user_profiles table)
        $userProfiles['email'] = $user->getEmail();

        switch ($user->getUserType()) {
            case UserType::CLIENT:
                if (\array_key_exists('isProfessional', $data)) {
                    $data['is_professional'] = $data['isProfessional'];
                    $user->setIsProfessional($data['is_professional']);
                }

                if (false === $data['is_professional']) {
                    // Setters bellow must have a string not null
                    $data['intraEuropeanCommunityVAT'] = '';
                    $data['legalIdentifier'] = '';
                    $data['jobTitle'] = '';
                    $data['company'] = '';
                }

                if (\array_key_exists('intraEuropeanCommunityVAT', $data)) {
                    $data['intra_european_community_vat'] = $data['intraEuropeanCommunityVAT'];
                    $user->setIntraEuropeanCommunityVAT($data['intra_european_community_vat']);
                }

                if (\array_key_exists('legalIdentifier', $data)) {
                    $data['legal_identifier'] = $data['legalIdentifier'];
                    $user->setLegalIdentifier($data['legal_identifier']);
                }

                if (\array_key_exists('jobTitle', $data)) {
                    $data['job_title'] = $data['jobTitle'];
                    $user->setJobTitle($data['job_title']);
                }

                if (\array_key_exists('company', $data)) {
                    $user->setCompany($data['company']);
                }

                if (\array_key_exists('extra', $data) === true) {
                    $user->setExtra($data['extra']);
                }
                break;

            case UserType::ADMIN:
                $data['company'] = '';
                $user->setCompany($data['company']);
                break;

            case UserType::VENDOR:
                unset($data['company']);
                break;
        }

        if (\in_array($user->getUserType(), [UserType::VENDOR, UserType::ADMIN], true)) {
            $data['isProfessional'] = false;
            $data['is_professional'] = false;
            $user->setIsProfessional($data['is_professional']);

            $data['intraEuropeanCommunityVAT'] = '';
            $data['intra_european_community_vat'] = '';
            $user->setIntraEuropeanCommunityVAT($data['intra_european_community_vat']);

            $data['legalIdentifier'] = '';
            $data['legal_identifier'] = '';
            $user->setLegalIdentifier($data['legal_identifier']);

            $data['jobTitle'] = '';
            $data['job_title'] = '';
            $user->setJobTitle($data['job_title']);
        }

        if (\array_key_exists('externalIdentifier', $data)) {
            $data['external_identifier'] = $data['externalIdentifier'];
            $user->setExternalIdentifier($data['external_identifier']);
        }

        if (\array_key_exists('comment', $data)) {
            $user->setComment($data['comment']);
        }

        if (\array_key_exists('nationalities', $data) === true) {
            $nationalities = $this->countryService->assertValidArrayCodeA3($data['nationalities']);

            $newNationalities = new ArrayCollection($nationalities);
            unset($data['nationalities']);
            $user->setNationalities($newNationalities);
        }

        return array_merge($userProfiles, $data);
    }

    /**
     * @param mixed[] $filters
     *
     * @return Subscription[]
     */
    public function getSubscriptions(User $user, int $limit = null, int $offset = null, array $filters = []): array
    {
        return $this->subscriptionRepository->findByUser($user, $limit, $offset, $filters);
    }

    /**
     * @param User $user
     * @param int|null $limit
     * @param int|null $offset
     *
     * @return array [AddressBook[], int $totalCount]
     */
    public function findAddressBook(User $user, int $limit = null, int $offset = null): array
    {
        return $this->repository->findAddressBookByUser($user, $limit, $offset);
    }

    public function findOneByEmail(string $email): ?User
    {
        return $this->repository->findOneByEmail($email);
    }

    public function findOneByApiKey(string $apiKey): ?User
    {
        return $this->repository->findOneByApiKey($apiKey);
    }

    /**
     * @param array $emails
     *
     * @return User[]
     */
    public function findUsersByEmails(array $emails): array
    {
        return $this->repository->findUsersByEmails($emails);
    }

    /**
     * @return int Number of days
     */
    public function getPasswordRenewalTimeLimit(): int
    {
        return $this->passwordRenewalTimeLimit;
    }

    /**
     * @return int|null Number of days before renewal is required
     */
    public function calculatePasswordExpiryTimeLeft(string $passwordChangeTimestamp): ?int
    {
        // check if FF PASSWORD_RECOVERY_FORCE_CHANGE and PASSWORD_RENEWAL_TIME_LIMIT are disabled
        if ($this->isPasswordRecoveryForceChangeActivated() === false
            || $this->getPasswordRenewalTimeLimit() <= 0
        ) {
            return null;
        }

        $passwordRenewDelay = new \DateInterval('P' . $this->getPasswordRenewalTimeLimit() . 'D');

        $currentDateTime = new \DateTime();
        $passwordChangeDateTime = (new \DateTime())->setTimestamp($passwordChangeTimestamp);
        $passwordChangeRequireDateTime = $passwordChangeDateTime->add($passwordRenewDelay);

        $daysLeftForRenewalPassword = $passwordChangeRequireDateTime->diff($currentDateTime)->format('%a');

        // Ajout d'un jour :  lorsqu'il nous reste 0J 3heures pour changer le mot de passe, on ne veut pas renvoyer 0 mais 1
        $daysLeftForRenewalPassword++;

        return $daysLeftForRenewalPassword < 0 ? 0 : $daysLeftForRenewalPassword;
    }

    public function isUserPasswordExpired(User $user): bool
    {
        $passwordExpiryTimeLeft = $this->calculatePasswordExpiryTimeLeft($user->getPasswordChangeTimestamp());
        if ($passwordExpiryTimeLeft !== null && $passwordExpiryTimeLeft <= 0) {
            return true;
        }

        return false;
    }

    public function createNewUser(): User
    {
        return $this->repository->createUser();
    }

    /**
     * @param int $userId
     *
     * @return string
     */
    public function createApiKey(int $userId): string
    {
        $user = $this->repository->get($userId);
        $user->setApiKey($this->createApiToken())
            ->setApiKeyUpdatedAt(new \DateTime('NOW'))
        ;
        $this->save($user);

        return $user->getApiKey();
    }

    /** @param int[] $customerIds */
    public function bulkDisable(array $customerIds): void
    {
        $this->repository->bulkDisable($customerIds);
    }

    private function createApiToken(): string
    {
        // Generate a 20 characters long API
        return bin2hex(random_bytes(20));
    }

    /**
     * @param string[] $nationalitiesCode
     */
    public function updateNationalities(int $userId, array $nationalitiesCode)
    {
        $countries = new ArrayCollection();
        foreach ($nationalitiesCode as $code) {
            if ($code !== "") {
                $countries[] = $this->countryService->getByCode($code);
            }
        }

        $this->repository->updateNationalities($userId, $countries);
    }

    /**
     * @return string[]
     * @throws NotFound
     */
    public function getNationalitiesCode(int $userId): array
    {
        $nationalities =  $this->repository->getNationalities($userId);
        $nationalitiesCode = [];

        foreach ($nationalities as $nationality) {
            $nationalitiesCode[] = $nationality->getCode();
        }

        return $nationalitiesCode;
    }

    /** @return array [User[], int] */
    public function findUsersPaginatedWithFilters(UserFilters $userFilters, int $page, int $resultPerPage): array
    {
        return $this->repository->findUsersPaginatedWithFilters($userFilters, $page, $resultPerPage);
    }

    public function passwordRecoveryForceChange(?string $eKey, string $password, string $confirmPassword): string
    {
        if ($eKey === null) {
            throw NotFound::fromId('eKey', $eKey);
        }

        $userId = $this->getUserIdFromEKey($eKey);

        if (\is_string($userId) === false || \mb_strlen($userId) === 0) {
            throw NotFound::fromId('User', $userId);
        }

        if ($password !== $confirmPassword) {
            throw new PasswordDontMatch();
        }

        $this->changePassword($userId, $password);

        $userStatus = fn_login_user($userId);

        if ($userStatus == LOGIN_STATUS_OK) {
            $this->eKeyRepository->removeByEKey($eKey);

            return $userId;
        }

        return '';
    }

    public function getUserIdFromEKey(string $eKey): ?string
    {
        return $this->eKeyRepository->getUserId($eKey);
    }

    public function removeByEKey(string $eKey): void
    {
        $this->eKeyRepository->removeByEKey($eKey);
    }

    public function removeExpiredEKeys(): void
    {
        $this->eKeyRepository->removeExpired();
    }

    private function isPasswordRecoveryForceChangeActivated(): bool
    {
        return $this->featureFlagService->get('feature.password_recovery_force_change');
    }

    private function isApprovedUserByAdminActivated(): bool
    {
        return $this->featureFlagService->get('feature.approve_user_by_admin');
    }
}
