<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\User;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Wizacha\Component\Import\EximJob;
use Wizacha\Marketplace\Country\Country;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Search\SearchCriteria;

class UserRepository
{
    /** @var EntityManagerInterface */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function getEntityManager(): EntityManagerInterface
    {
        return $this->entityManager;
    }

    public function createUser(): User
    {
        $profileFields = fn_get_profile_fields();

        return new User(
            $this->createAddress($profileFields, ProfileFieldSections::BILLING(), []),
            $this->createAddress($profileFields, ProfileFieldSections::SHIPPING(), [])
        );
    }

    /**
     * @param int $id
     * @throws NotFound
     */
    public function get($id): User
    {
        /** @var User */
        $user = $this->entityManager->find(User::class, $id);

        if (null === $user) {
            throw NotFound::fromId('User', $id);
        }

        if (false === \is_int($user->getCompanyId())) {
            $companyId = fn_get_company_id_from_request_user_id($user->getUserId());
            if (\is_int($companyId)) {
                $user->setPendingCompanyId($companyId);
            }
        }

        $userInfo = fn_get_user_info($id, true, $profileId, true);

        $this->fillAddresses($user, $userInfo);
        $this->fillComplementaryInformation($user, $userInfo);

        return $user;
    }

    public function save(User $user)
    {
        $this->entityManager->persist($user);
        $this->entityManager->flush();
    }

    /**
     * @return User[]
     */
    public function findWithPagination($offset, $limit): array
    {
        $query = $this->entityManager->createQueryBuilder()
            ->select("u")
            ->from(User::class, "u")
            ->setFirstResult($offset)
            ->setMaxResults($limit)
            ->getQuery()
        ;
        $users = $query->getResult();
        array_walk($users, [$this, 'fillAddresses']);
        array_walk($users, [$this, 'fillComplementaryInformation']);

        return $users;
    }

    /**
     * @return User[]
     */
    public function findActiveAdmins(): array
    {
        $query = $this->entityManager->createQueryBuilder()
            ->select('u')
            ->from(User::class, 'u')
            ->where('u.status = \'A\'')
            ->andWhere('u.userType = \'A\'')
            ->getQuery()
        ;
        $users = $query->getResult();
        array_walk($users, [$this, 'fillAddresses']);

        return $users;
    }

    public function count(): int
    {
        $query = $this->entityManager->createQueryBuilder()
            ->select("COUNT(u)")
            ->from(User::class, "u")
            ->getQuery()
        ;

        return (int) $query->getSingleScalarResult();
    }

    public function findOneByEmail($email, bool $refresh = false): ?User
    {
        $user = $this->entityManager->getRepository(User::class)->findOneBy([
            'email' => $email,
        ]);
        if ($user) {
            $this->fillAddresses($user);
            if ($refresh === true) {
                $this->entityManager->refresh($user);
            }
        }

        return $user;
    }

    /**
     * @param string[] $emails
     *
     * @return User[]
     */
    public function findUsersByEmails(array $emails): array
    {
        return $this->entityManager->createQueryBuilder()
            ->select('u')
            ->from(User::class, 'u')
            ->where('u.email in (:emails)')
            ->setParameter('emails', $emails)
            ->getQuery()
            ->getResult();
    }

    public function findOneByApiKey(string $apiKey): ?User
    {
        $user = $this->entityManager->getRepository(User::class)->findOneBy([
            'apiKey' => $apiKey,
        ]);
        if ($user) {
            $this->fillAddresses($user);
        }

        return $user;
    }

    /**
     * @return User[]
     */
    public function findWithEximJob(): array
    {
        $query = $this->entityManager->createQueryBuilder()
            ->select('u')
            ->from(EximJob::class, 'j')
            ->innerJoin(User::class, 'u', 'WITH', 'u.userId= j.userId')
            ->groupBy('j.userId')
            ->getQuery()
            ;

        return $query->getResult();
    }

    /** @return int|null */
    public function resetAllUsersBaskets(): ?int
    {
        $query = $this->entityManager->createQueryBuilder()
            ->update(User::class, 'u')
            ->set('u.basketId', 'null')
            ->where('u.basketId IS NOT null')
            ->getQuery();

        return $query->getResult();
    }

    /**
     * @param User $user
     * @param int|null $limit
     * @param int|null $offset
     *
     * @return array [AddressBook[], int $totalCount]
     */
    public function findAddressBookByUser(User $user, int $limit = null, int $offset = null): array
    {
        $queryBuilder = $this->entityManager->createQueryBuilder()
            ->select('a')
            ->from(AddressBook::class, 'a')
            ->where('a.user = :userId')
            ->setParameter('userId', $user->getUserId())
        ;

        if (\is_int($limit) && \is_int($offset)) {
            $queryBuilder
                ->setMaxResults($limit)
                ->setFirstResult($offset)
            ;
        }

        $paginator = new Paginator($queryBuilder);

        return [$paginator->getQuery()->getResult(), $paginator->count()];
    }

    /**
     * Fill in the user's addresses from CS Cart.
     *
     * @param mixed $userInfo integer with array_walk and array with $this->fillAddresses
     */
    private function fillAddresses(User $user, $userInfo = null): void
    {
        $profileId = null;

        if (false === \is_array($userInfo)) {
            $addressData = fn_get_user_info($user->getUserId(), true, $profileId, true);
        } else {
            $addressData = $userInfo;
        }

        $profileFields = fn_get_profile_fields('APVC');

        $user->setBillingAddress(
            $this->createAddress($profileFields, ProfileFieldSections::BILLING(), $addressData)
        );
        $user->setShippingAddress(
            $this->createAddress($profileFields, ProfileFieldSections::SHIPPING(), $addressData)
        );
    }

    /**
     * Fill the user's complementary information
     *
     * @param mixed $userInfo integer with array_walk and array with $this->fillComplementaryInformation
     */
    private function fillComplementaryInformation(User $user, $userInfo = null): void
    {
        $profileId = null;

        if (\is_array($userInfo) === false) {
            $userInfo = fn_get_user_info($user->getUserId(), true, $profileId, true);
        }

        $user->setExternalIdentifier($userInfo['external_identifier'] ?? '');
        $user->setIsProfessional('1' === $userInfo['is_professional']);
        $user->setIntraEuropeanCommunityVAT($userInfo['intra_european_community_vat'] ?? '');
        $user->setCompany($userInfo['company'] ?? '');
        $user->setJobTitle($userInfo['job_title'] ?? '');
        $user->setComment($userInfo['comment'] ?? '');
        $user->setLegalIdentifier($userInfo['legal_identifier'] ?? '');

        $user->setQuoteRequestSelectionIds($userInfo['quote_request_selection_ids'] ?? []);
    }

    private function createAddress(array $profileFields, ProfileFieldSections $section, array $addressData): UserAddress
    {
        $sectionName = $section->getValue();
        $fields = [];
        foreach ($profileFields[$sectionName] as $field) {
            $fieldName = empty($field['field_name']) ? $field['field_id'] : $field['field_name'];
            if (strpos($fieldName, 'b_') === 0 || strpos($fieldName, 's_') === 0) {
                $fieldName = substr($fieldName, 2);
            }
            // By name
            $fields[$fieldName] = $addressData[$field['field_name']] ?? null;
        }

        return new UserAddress($fields);
    }

    /** @param int[] $customerIds */
    public function bulkDisable(array $customerIds): void
    {
        $this->entityManager->createQueryBuilder()
            ->update(User::class, 'u')
            ->set('u.status', ':status')
            ->setParameter('status', UserStatus::INACTIVE()->getValue())
            ->where('u.userId in (:ids)')
            ->setParameter('ids', $customerIds)
            ->getQuery()
            ->execute()
        ;
    }

    /** @return Collection|Country[] */
    public function getNationalities(int $userId): Collection
    {
        /** @var User */
        $user = $this->entityManager->find(User::class, $userId);

        if (null === $user) {
            throw NotFound::fromId('User', $userId);
        }

        return  $user->getNationalities();
    }

    /** @param ArrayCollection|Country[] $nationalities */
    public function updateNationalities(int $userId, ArrayCollection $nationalities): User
    {
        /** @var User */
        $user = $this->get($userId);
        $user->setNationalities($nationalities);
        $this->save($user);

        return $user;
    }

    public function updateCompanyId(int $userId, int $companyId): User
    {
        $user = $this->get($userId);
        $user->setCompanyId($companyId);
        $this->save($user);

        return $user;
    }

    /** @return array [User[], int] */
    public function findUsersPaginatedWithFilters(UserFilters $userFilters, int $page, int $resultPerPage): array
    {
        $query = $this->entityManager->createQueryBuilder()
            ->select("u")
            ->from(User::class, "u")
            ->leftJoin(UserProfile::class, "up", 'WITH', 'u.userId = up.userId')
            ->where('u.userId is not null');

        if ($userFilters->getName() !== null) {
            $query->andWhere('u.firstname LIKE :name OR u.lastname LIKE :name')
                ->setParameter('name', '%' . $userFilters->getName() . '%', \PDO::PARAM_STR);
        }

        if ($userFilters->getEmail() !== null) {
            $query->andWhere('u.email LIKE :email')
                ->setParameter('email', '%' . $userFilters->getEmail() . '%');
        }

        if ($userFilters->getPhone() !== null) {
            $query->andWhere('u.phone LIKE :phone')
                ->setParameter('phone', '%' . $userFilters->getPhone() . '%');
        }

        if ($userFilters->getExternal() !== null) {
            $query->andWhere('up.externalIdentifier LIKE :external')
                ->setParameter('external', '%' . $userFilters->getExternal() . '%');
        }

        if ($userFilters->getLoyalty() !== null) {
            $query->andWhere('u.loyaltyIdentifier LIKE :loyalty')
                ->setParameter('loyalty', '%' . $userFilters->getLoyalty() . '%');
        }

        if ($userFilters->getCompanyId() !== null) {
            $query->andWhere('u.companyId = :companyId')
                ->setParameter('companyId', $userFilters->getCompanyId());
        }

        if ($userFilters->getIsProfessional() !== null) {
            $query->andWhere('up.isProfessional = :isProfessional')
                ->setParameter('isProfessional', $userFilters->getIsProfessional());
        }

        if ($userFilters->getType() !== null && \count($userFilters->getType()) > 0) {
            $query->andWhere('u.userType IN (:type)')
                ->setParameter('type', $userFilters->getType());
        }

        if ($userFilters->getExtra() !== null) {
            $query->andWhere(
                SearchCriteria::getFormattedConditionFromExtraCriteria('u', $userFilters->getExtra())
            );
        }

        if ($userFilters->getExtraStartWith() !== null) {
            $query->andWhere(
                SearchCriteria::getFormattedConditionFromExtraCriteria(
                    'u',
                    $userFilters->getExtraStartWith(),
                    true
                )
            );
        }

        $paginator = new Paginator($query);

        $totalUsers = \count($paginator);

        $users = $paginator
            ->getQuery()
            ->setFirstResult($resultPerPage * $page)
            ->setMaxResults($resultPerPage)
            ->getResult()
        ;

        \array_walk($users, [$this, 'fillAddresses']);
        \array_walk($users, [$this, 'fillComplementaryInformation']);

        return [$users, $totalUsers];
    }
}
