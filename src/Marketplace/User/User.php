<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\User;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Wizacha\Component\Locale\Locale;
use Wizacha\Marketplace\Country\Country;
use Wizacha\Marketplace\Group\Group;
use Wizacha\Marketplace\User\AddressBook;
use Wizacha\Marketplace\CreditCard\CreditCard;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Organisation\Organisation;
use Wizacha\Marketplace\Subscription\Subscription;
use Wizacha\Status;
use Rhumsaa\Uuid\Uuid;

/**
 * User
 */
class User
{
    /**
     * @var integer
     */
    private $userId;

    /**
     * @var string
     */
    private $status = 'A';

    /** @var bool */
    private $isLocked = false;

    /**
     * @var string
     */
    private $userType = UserType::CLIENT;

    /**
     * @var string
     */
    private $userLogin = '';

    /**
     * @var string
     */
    private $referer = '';

    /**
     * @var string
     */
    private $isRoot = 'N';

    /**
     * @var integer
     */
    private $companyId = '0';

    /**
     * @var integer
     */
    private $lastLogin = '0';

    private int $failedLoginCount = 0;

    /**
     * @var integer
     */
    private $timestamp = '0';

    /**
     * @var string
     */
    private $password = '';

    /**
     * @var string
     */
    private $salt = '';

    /**
     * @var string
     */
    private $title = UserTitle::MR;

    /**
     * @var string
     */
    private $firstname = '';

    /**
     * @var string
     */
    private $lastname = '';

    /**
     * @var string
     */
    private $company = '';

    /**
     * @var string
     */
    private $email = '';

    /**
     * @var string
     */
    private $phone = '';

    /**
     * @var string
     */
    private $fax = '';

    /**
     * @var string
     */
    private $url = '';

    /**
     * @var string
     */
    private $taxExempt = 'N';

    /**
     * @var Locale
     */
    private $langCode;

    /**
     * @var \DateTime
     */
    private $birthday = null;

    /**
     * @var integer
     */
    private $purchaseTimestampFrom = '0';

    /**
     * @var integer
     */
    private $purchaseTimestampTo = '0';

    /**
     * @var string
     */
    private $responsibleEmail = '';

    /**
     * @var string
     */
    private $lastPasswords = '';

    /**
     * @var integer
     */
    private $passwordChangeTimestamp = '0';

    /**
     * @var string
     */
    private $apiKey = '';

    /**
     * @var string|null
     */
    private $basketId;

    /**
     * @var string|null
     */
    private $loyaltyIdentifier;

    /**
     * @var UserAddress
     */
    private $billingAddress;

    /**
     * @var UserAddress
     */
    private $shippingAddress;

    /**
     * @var Organisation|null
     */
    private $organisation;

    /**
     * @var OAuthToken
     */
    private $oauthToken;

    /**
     * @var ?string
     */
    private $currencyCode;

    /**
     * @var ?int
     */
    private $pendingCompanyId;

    /** @var string */
    private $externalIdentifier = '';

    /** @var ?bool */
    private $isProfessional = false;

    /** @var string */
    private $intraEuropeanCommunityVAT = '';

    /** @var string */
    private $jobTitle = '';

    /** @var string */
    private $comment = '';

    /** @var ?string */
    private $legalIdentifier;

    /** @var Collection|Subscription[] */
    private $subscriptions;

    /** @var Collection|CreditCard[] */
    private $creditCards;

    /** @var Collection|AddressBook[] */
    private $addressBook;

    /** @var Collection|UserPasswordHistory[] */
    private $userPasswordHistory;

    /** @var Uuid */
    private $userUuid;

    /** @var Collection|Country[] */
    private Collection $nationalities;
    private ?\DateTime $apiKeyUpdatedAt;

    private ?string $extra;

    /** @var Collection|Group[] */
    private Collection $groups;

    private string $userDeclinationDisplayType;

    /** @var string[] */
    private $quoteRequestSelectionIds = [];

    public function __construct(UserAddress $billingAddress, UserAddress $shippingAddress)
    {
        $this->billingAddress = $billingAddress;
        $this->shippingAddress = $shippingAddress;
        $this->oauthToken = OAuthToken::empty();
        $this->subscriptions = new ArrayCollection();
        $this->creditCards = new ArrayCollection();
        $this->userUuid = Uuid::uuid4();
        $this->addressBook = new ArrayCollection();
        $this->nationalities = new ArrayCollection();
        $this->extra = null;
        $this->groups = new ArrayCollection();
        $this->userPasswordHistory = new ArrayCollection();
    }

    public static function createFromOAuth(string $email, OAuthToken $token, UserType $userType): self
    {
        $self = new self(new UserAddress([]), new UserAddress([]));
        $self->oauthToken = $token;
        $self->userLogin = $email;
        $self->email = $email;
        $self->userType = (string) $userType;
        $self->langCode = GlobalState::interfaceLocale();
        $self->timestamp = TIME;

        return $self;
    }

    /**
     * Get userId
     *
     * @return integer
     */
    public function getUserId(): int
    {
        return (int) $this->userId;
    }

    /**
     * Set userId
     *
     * @param int $userId
     * @return User
     */
    public function setUserId($userId)
    {
        $this->userId = $userId;

        return $this;
    }

    /**
     * Get userUuid
     *
     * @return Uuid
     */
    public function getUserUuid(): Uuid
    {
        return $this->userUuid;
    }

    /**
     * Set userId
     *
     * @param Uuid $userUuid
     *
     * @return User
     */
    public function setUserUuid(Uuid $userUuid): self
    {
        $this->userUuid = $userUuid;

        return $this;
    }

    /**
     * Set status
     *
     * @param string $status
     *
     * @return User
     */
    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Get status
     *
     * @return string
     */
    public function getStatus()
    {
        return $this->status;
    }

    public function setIsLocked(bool $isLocked): self
    {
        $this->isLocked = $isLocked;

        return $this;
    }

    public function isLocked(): bool
    {
        return $this->isLocked;
    }

    /**
     * Set userType
     *
     * @param string $userType
     *
     * @return User
     */
    public function setUserType($userType)
    {
        $this->userType = $userType;

        return $this;
    }

    /**
     * Get userType
     *
     * @return string
     */
    public function getUserType()
    {
        return $this->userType;
    }

    /**
     * Set userLogin
     *
     * @param string $userLogin
     *
     * @return User
     */
    public function setUserLogin($userLogin)
    {
        $this->userLogin = $userLogin;

        return $this;
    }

    /**
     * Get userLogin
     *
     * @return string
     */
    public function getUserLogin()
    {
        return $this->userLogin;
    }

    /**
     * Set referer
     *
     * @param string $referer
     *
     * @return User
     */
    public function setReferer($referer)
    {
        $this->referer = $referer;

        return $this;
    }

    /**
     * Get referer
     *
     * @return string
     */
    public function getReferer()
    {
        return $this->referer;
    }

    /**
     * Set isRoot
     *
     * @param string $isRoot
     *
     * @return User
     */
    public function setIsRoot($isRoot)
    {
        $this->isRoot = $isRoot;

        return $this;
    }

    /**
     * Get isRoot
     *
     * @return string
     */
    public function getIsRoot()
    {
        return $this->isRoot;
    }

    /**
     * Set companyId
     *
     * @param integer $companyId
     *
     * @return User
     */
    public function setCompanyId($companyId)
    {
        $this->companyId = $companyId;

        return $this;
    }

    public function getCompanyId(): ?int
    {
        return $this->companyId > 0 ? $this->companyId : null;
    }

    /**
     * Set lastLogin
     *
     * @param integer $lastLogin
     *
     * @return User
     */
    public function setLastLogin($lastLogin)
    {
        $this->lastLogin = $lastLogin;

        return $this;
    }

    /**
     * Get lastLogin
     *
     * @return integer
     */
    public function getLastLogin()
    {
        return $this->lastLogin;
    }

    public function getFailedLoginCount(): int
    {
        return $this->failedLoginCount;
    }

    public function setFailedLoginCount(int $failedLoginCount): self
    {
        $this->failedLoginCount = $failedLoginCount;

        return $this;
    }

    /**
     * Set timestamp
     *
     * @param integer $timestamp
     *
     * @return User
     */
    public function setTimestamp($timestamp)
    {
        $this->timestamp = $timestamp;

        return $this;
    }

    /**
     * Get timestamp
     *
     * @return integer
     */
    public function getTimestamp()
    {
        return $this->timestamp;
    }

    /**
     * Set password
     *
     * @param string $password
     *
     * @return User
     */
    public function setPassword($password)
    {
        $this->password = $password;

        return $this;
    }

    /**
     * Get password
     *
     * @return string
     */
    public function getPassword()
    {
        return $this->password;
    }

    /**
     * Set salt
     *
     * @param string $salt
     *
     * @return User
     */
    public function setSalt($salt)
    {
        $this->salt = $salt;

        return $this;
    }

    /**
     * Get salt
     *
     * @return string
     */
    public function getSalt()
    {
        return $this->salt;
    }

    public function setTitle(?UserTitle $title): User
    {
        $title instanceof UserTitle ? $this->title = $title->getValue() : $this->title = null;

        return $this;
    }

    public function getTitle(): ?UserTitle
    {
        return $this->title ? new UserTitle($this->title) : null;
    }

    /**
     * Set firstname
     *
     * @param string $firstname
     *
     * @return User
     */
    public function setFirstname($firstname)
    {
        $this->firstname = $firstname;

        return $this;
    }

    /**
     * Get firstname
     *
     * @return string
     */
    public function getFirstname()
    {
        return $this->firstname;
    }

    /**
     * Set lastname
     *
     * @param string $lastname
     *
     * @return User
     */
    public function setLastname($lastname)
    {
        $this->lastname = $lastname;

        return $this;
    }

    /**
     * Get lastname
     *
     * @return string
     */
    public function getLastname()
    {
        return $this->lastname;
    }


    public function getFullName(): string
    {
        return trim("{$this->getFirstname()} {$this->getLastname()}");
    }

    /**
     * Set company
     *
     * @param string $company
     *
     * @return User
     */
    public function setCompany($company)
    {
        $this->company = $company;

        return $this;
    }

    /**
     * Get company
     *
     * @return string
     */
    public function getCompany()
    {
        return $this->company;
    }

    /**
     * Set email
     *
     * @param string $email
     *
     * @return User
     */
    public function setEmail($email)
    {
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new \InvalidArgumentException('The email must be a valid email address');
        }

        $this->email = $email;

        return $this;
    }

    /**
     * Get email
     *
     * @return string
     */
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * Set phone
     *
     * @param string $phone
     *
     * @return User
     */
    public function setPhone($phone)
    {
        $this->phone = $phone;

        return $this;
    }

    /**
     * Get phone
     *
     * @return string
     */
    public function getPhone()
    {
        return $this->phone;
    }

    /**
     * Set fax
     *
     * @param string $fax
     *
     * @return User
     */
    public function setFax($fax)
    {
        $this->fax = $fax;

        return $this;
    }

    /**
     * Get fax
     *
     * @return string
     */
    public function getFax()
    {
        return $this->fax;
    }

    /**
     * Set url
     *
     * @param string $url
     *
     * @return User
     */
    public function setUrl($url)
    {
        $this->url = $url;

        return $this;
    }

    /**
     * Get url
     *
     * @return string
     */
    public function getUrl()
    {
        return $this->url;
    }

    /**
     * Set taxExempt
     *
     * @param string $taxExempt
     *
     * @return User
     */
    public function setTaxExempt($taxExempt)
    {
        $this->taxExempt = $taxExempt;

        return $this;
    }

    /**
     * Get taxExempt
     *
     * @return string
     */
    public function getTaxExempt()
    {
        return $this->taxExempt;
    }

    /**
     * Set langCode
     *
     * @deprecated
     * @see setLocale
     *
     * @param string $langCode
     *
     * @return User
     */
    public function setLangCode($langCode)
    {
        $this->langCode = new Locale((string) $langCode);

        return $this;
    }

    /**
     * Get langCode
     *
     * @deprecated
     * @see getLocale
     *
     * @return string
     */
    public function getLangCode(): string
    {
        return (string) $this->langCode;
    }

    public function setLocale(Locale $locale): self
    {
        $this->langCode = $locale;

        return $this;
    }

    public function getLocale(): Locale
    {
        return $this->langCode ?? GlobalState::fallbackLocale();
    }

    /**
     * Set birthday
     *
     * @param null|\DateTime $birthday
     *
     * @return User
     */
    public function setBirthday(?\DateTime $birthday)
    {
        $this->birthday = $birthday;

        return $this;
    }

    public function setCurrencyCode(?string $currencyCode): self
    {
        if (preg_match('/^[a-zA-Z]{3}$/', $currencyCode) !== 1 && \is_null($currencyCode) === false) {
            throw new \Exception('Currency code "' . $currencyCode . '" must be ISO 4217.');
        }

        $this->currencyCode = \is_string($currencyCode) ? mb_strtoupper($currencyCode) : $currencyCode;

        return $this;
    }

    /**
     * Get birthday
     *
     * @return null|\DateTime
     */
    public function getBirthday(): ?\DateTime
    {
        return $this->birthday;
    }

    public function getCurrencyCode(): ?string
    {
        return $this->currencyCode;
    }

    /**
     * Set purchaseTimestampFrom
     *
     * @param integer $purchaseTimestampFrom
     *
     * @return User
     */
    public function setPurchaseTimestampFrom($purchaseTimestampFrom)
    {
        $this->purchaseTimestampFrom = $purchaseTimestampFrom;

        return $this;
    }

    /**
     * Get purchaseTimestampFrom
     *
     * @return integer
     */
    public function getPurchaseTimestampFrom()
    {
        return $this->purchaseTimestampFrom;
    }

    /**
     * Set purchaseTimestampTo
     *
     * @param integer $purchaseTimestampTo
     *
     * @return User
     */
    public function setPurchaseTimestampTo($purchaseTimestampTo)
    {
        $this->purchaseTimestampTo = $purchaseTimestampTo;

        return $this;
    }

    /**
     * Get purchaseTimestampTo
     *
     * @return integer
     */
    public function getPurchaseTimestampTo()
    {
        return $this->purchaseTimestampTo;
    }

    /**
     * Set responsibleEmail
     *
     * @param string $responsibleEmail
     *
     * @return User
     */
    public function setResponsibleEmail($responsibleEmail)
    {
        $this->responsibleEmail = $responsibleEmail;

        return $this;
    }

    /**
     * Get responsibleEmail
     *
     * @return string
     */
    public function getResponsibleEmail()
    {
        return $this->responsibleEmail;
    }

    /**
     * Set lastPasswords
     *
     * @param string $lastPasswords
     *
     * @return User
     */
    public function setLastPasswords($lastPasswords)
    {
        $this->lastPasswords = $lastPasswords;

        return $this;
    }

    /**
     * Get lastPasswords
     *
     * @return string
     */
    public function getLastPasswords()
    {
        return $this->lastPasswords;
    }

    /**
     * Set passwordChangeTimestamp
     *
     * @param integer $passwordChangeTimestamp
     *
     * @return User
     */
    public function setPasswordChangeTimestamp($passwordChangeTimestamp)
    {
        $this->passwordChangeTimestamp = $passwordChangeTimestamp;

        return $this;
    }

    /**
     * Get passwordChangeTimestamp
     *
     * @return integer
     */
    public function getPasswordChangeTimestamp()
    {
        return $this->passwordChangeTimestamp;
    }

    /**
     * Set apiKey
     *
     * @param string $apiKey
     *
     * @return User
     */
    public function setApiKey($apiKey)
    {
        $this->apiKey = $apiKey;

        return $this;
    }

    /**
     * Get apiKey
     *
     * @return string
     */
    public function getApiKey()
    {
        return $this->apiKey;
    }

    /**
     * Set basketId
     *
     * @param string $basketId
     *
     * @return User
     */
    public function setBasketId(?string $basketId)
    {
        $this->basketId = $basketId;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getBasketId(): ?string
    {
        return $this->basketId;
    }

    public function getLoyaltyIdentifier(): ?string
    {
        return $this->loyaltyIdentifier;
    }

    public function setLoyaltyIdentifier(string $loyaltyIdentifier = null): self
    {
        $this->loyaltyIdentifier = $loyaltyIdentifier;

        return $this;
    }

    public function setBillingAddress(UserAddress $address)
    {
        $this->billingAddress = $address;
    }

    public function getBillingAddress(): UserAddress
    {
        return $this->billingAddress;
    }

    public function setShippingAddress(UserAddress $address)
    {
        $this->shippingAddress = $address;
    }

    public function getShippingAddress(): UserAddress
    {
        return $this->shippingAddress;
    }

    public function getDivision(): ?string
    {
        if ($this->getShippingAddress() instanceof UserAddress) {
            return $this->getShippingAddress()->getFieldValue('division_code');
        }

        return null;
    }

    public function setOrganisation(Organisation $organisation): User
    {
        if ($this->belongsToAnOrganisation()) {
            throw new \LogicException('The user already belongs to an organisation, this cannot be changed.');
        }

        if (\in_array($this->userType, [UserType::ADMIN, UserType::VENDOR], true)) {
            throw new \LogicException('Only a customer can be attached to an organisation.');
        }

        $this->organisation = $organisation;

        return $this;
    }

    public function belongsToAnOrganisation(): bool
    {
        return $this->organisation instanceof Organisation;
    }

    public function belongsToOrganisation(Organisation $organisation): bool
    {
        return $this->organisation === $organisation;
    }

    /**
     * @throws NotFound When the user doesn't belong to an organisation
     */
    public function getOrganisation(): Organisation
    {
        if (!$this->belongsToAnOrganisation()) {
            throw new NotFound('The user doesn\'t belong to an organisation');
        }

        return $this->organisation;
    }

    public function isPrivateIndividual(): bool
    {
        return !$this->isProfessional();
    }

    public function isMarketplaceAdministrator(): bool
    {
        return $this->userType === UserType::ADMIN;
    }

    public function isMarketplaceSupportAdministrator(): bool
    {
        return ($this->userType === UserType::ADMIN
            && (strpos($this->getEmail(), '@wizacha.com') !== false
                || strpos($this->getEmail(), '@wizaplace.com') !== false)
        );
    }

    public function canEditUser(User $user): bool
    {
        return (
            ($user->isMarketplaceAdministrator() === true
                && ($user === $this
                    || $this->isMarketplaceSupportAdministrator() === true))
            || ($user->isMarketplaceAdministrator() === false)
        );
    }

    public function isEnabled(): bool
    {
        return $this->getStatus() === (string) Status::ENABLED();
    }

    public function isPending(): bool
    {
        return $this->getStatus() === (string) Status::PENDING();
    }

    public function hasOAuthToken(): bool
    {
        return !$this->oauthToken->isEmpty();
    }

    public function getOAuthToken(): OAuthToken
    {
        if (!$this->hasOAuthToken()) {
            throw new \LogicException('No oauth token');
        }

        return $this->oauthToken;
    }

    public function setOAuthToken(OAuthToken $token): void
    {
        $this->oauthToken->refresh(
            $token->getToken(),
            $token->getRefreshToken(),
            $token->hasExpirationDate() ? $token->getExpirationDate() : null
        );
    }

    public function refreshOAuthToken(OAuthToken $token): void
    {
        if (!$this->hasOAuthToken()) {
//            throw new \LogicException();
        }

        $this->oauthToken->refresh(
            $token->getToken(),
            $token->getRefreshToken(),
            $token->hasExpirationDate() ? $token->getExpirationDate() : null
        );
    }

    public function getPendingCompanyId(): ?int
    {
        return $this->pendingCompanyId;
    }

    public function setPendingCompanyId(?int $pendingCompanyId): self
    {
        $this->pendingCompanyId = $pendingCompanyId;

        return $this;
    }

    /** @return Collection|Subscription[] */
    public function getSubscriptions(): Collection
    {
        return $this->subscriptions;
    }

    /** @param Subscription[] $subscriptions */
    public function setSubscriptions(iterable $subscriptions): self
    {
        $this->clearSubscriptions();

        foreach ($subscriptions as $subscription) {
            $this->addSubscription($subscription);
        }

        return $this;
    }

    public function addSubscription(Subscription $subscription): self
    {
        if ($this->subscriptions->contains($subscription) === false) {
            $this->subscriptions->add($subscription);
            $subscription->setUser($this);
        }

        return $this;
    }

    public function removeSubscription(Subscription $subscription): self
    {
        if ($this->subscriptions->contains($subscription)) {
            $this->subscriptions->removeElement($subscription);
            $subscription->setUser(null);
        }

        return $this;
    }

    public function clearSubscriptions(): self
    {
        foreach ($this->getSubscriptions() as $subscription) {
            $this->removeSubscription($subscription);
        }

        $this->subscriptions->clear();

        return $this;
    }

    /** @return Collection|CreditCard[] */
    public function getCreditCards(): Collection
    {
        return $this->creditCards;
    }

    /** @param CreditCard[] $creditCards */
    public function setCreditCards(iterable $creditCards): self
    {
        $this->clearCreditCards();

        foreach ($creditCards as $creditCard) {
            $this->addCreditCard($creditCard);
        }

        return $this;
    }

    public function addCreditCard(CreditCard $creditCard): self
    {
        if ($this->creditCards->contains($creditCard) === false) {
            $this->creditCards->add($creditCard);
            $creditCard->setUser($this);
        }

        return $this;
    }

    public function removeCreditCard(CreditCard $creditCard): self
    {
        if ($this->creditCards->contains($creditCard)) {
            $this->creditCards->removeElement($creditCard);
            $creditCard->setUser(null);
        }

        return $this;
    }

    public function clearCreditCards(): self
    {
        foreach ($this->getCreditCards() as $creditCard) {
            $this->removeCreditCard($creditCard);
        }

        $this->creditCards->clear();

        return $this;
    }

    public function expose(): array
    {
        return [
            'id' => $this->userId,
            'email' => $this->email,
            'firstName' => $this->firstname,
            'lastName' => $this->lastname,
            'status' => $this->status,
        ];
    }

    public function fromArray(array $values): self
    {
        foreach ($values as $key => $value) {
            $methodName = 'set' . $key;
            if (method_exists($this, $methodName)) {
                $this->$methodName($value);
            }
        }

        return $this;
    }

    public function getExternalIdentifier(): string
    {
        return $this->externalIdentifier;
    }

    public function setExternalIdentifier(string $externalIdentifier): self
    {
        $this->externalIdentifier = $externalIdentifier;

        return $this;
    }

    public function isProfessional(): ?bool
    {
        $areClientsProfessionals = container()->getParameter('feature.professional_clients');

        /**
         * Le fait de se baser sur un feature flag n'est plus temporaire car maintenant nous saisissons l'information
         * dans le profile utilisateur mais afin de ne pas casser les MP des clients existants qui ne feront sans doute
         * jamais le necessaire pour passer tous leurs clients en isProfessional, il est nécessaire de laisser la
         * gestion via le feature flag mais uniquement pour les clients qui ont ce feature flag à true.
         */
        if (\is_bool($areClientsProfessionals) && true === $areClientsProfessionals) {
            return true;
        }

        return $this->isProfessional;
    }

    public function setIsProfessional(?bool $isProfessional): self
    {
        $this->isProfessional = $isProfessional;

        return $this;
    }

    public function getIntraEuropeanCommunityVAT(): ?string
    {
        return $this->intraEuropeanCommunityVAT;
    }

    public function setIntraEuropeanCommunityVAT(?string $intraEuropeanCommunityVAT): self
    {
        $this->intraEuropeanCommunityVAT = $intraEuropeanCommunityVAT;

        return $this;
    }

    /**
     * Set function
     *
     * @param string $jobTitle
     *
     * @return User
     */
    public function setJobTitle(string $jobTitle): self
    {
        $this->jobTitle = $jobTitle;

        return $this;
    }

    /**
     * Get function
     *
     * @return string
     */
    public function getJobTitle(): ?string
    {
        return $this->jobTitle;
    }

    /**
     * Get comment
     *
     * @return string
     */
    public function getComment(): string
    {
        return $this->comment;
    }

    /**
     * Set comment
     *
     * @param string $comment
     *
     * @return User
     */
    public function setComment(string $comment): self
    {
        $this->comment = $comment;

        return $this;
    }

    /**
     * @return string|null
     */
    public function getLegalIdentifier()
    {
        return $this->legalIdentifier;
    }

    /**
     * @param string $legalIdentifier
     *
     * @return User
     */
    public function setLegalIdentifier(string $legalIdentifier): self
    {
        $this->legalIdentifier = $legalIdentifier;

        return $this;
    }

    /** @return Collection|AddressBook[] */
    public function getAddressBook(): Collection
    {
        return $this->addressBook;
    }

    /** @param AddressBook[] $addressBook */
    public function setAddressBook(iterable $addressBook): self
    {
        $this->clearAddressBook();

        foreach ($addressBook as $addressB) {
            $this->addAddressBook($addressB);
        }

        return $this;
    }

    public function addAddressBook(AddressBook $addressBook): self
    {
        if ($this->addressBook->contains($addressBook) === false) {
            $this->addressBook->add($addressBook);
            $addressBook->setUser($this);
        }

        return $this;
    }

    public function removeAddressBook(AddressBook $addressBook): self
    {
        if ($this->addressBook->contains($addressBook)) {
            $this->addressBook->removeElement($addressBook);
            $addressBook->setUser(null);
        }

        return $this;
    }

    public function clearAddressBook(): self
    {
        foreach ($this->getAddressBook() as $addressBook) {
            $this->removeAddressBook($addressBook);
        }

        $this->addressBook->clear();

        return $this;
    }

    /** @return Collection|Country[] */
    public function getNationalities(): Collection
    {
        return $this->nationalities;
    }

    public function setNationalities(ArrayCollection $nationalities): self
    {
        $this->nationalities = $nationalities;

        return $this;
    }

    public function addNationality(Country $country): self
    {
        if (false === $this->nationalities->contains($country)) {
            $this->nationalities[] = $country;
        }

        return $this;
    }

    /** @return string[]|int[] */
    public function getExtra(): array
    {
        if (\is_null($this->extra) === true || \mb_strlen($this->extra) === 0) {
            return [];
        }

        return \json_decode($this->extra, true);
    }

    /** @params string[]|int[] $extra */
    public function setExtra(array $extra): self
    {
        $this->extra = \json_encode($extra);

        return $this;
    }

    public function getGroups(): Collection
    {
        return $this->groups;
    }

    public function setGroups(ArrayCollection $groups): self
    {
        foreach ($groups as $group) {
            $this->addGroup($group);
        }

        return $this;
    }

    public function addGroup(Group $group): self
    {
        if (false === $this->groups->contains($group)) {
            $this->groups->add($group);
            $group->addUser($this);
        }

        return $this;
    }

    public function getApiKeyUpdatedAt(): ?\DateTime
    {
        return $this->apiKeyUpdatedAt;
    }

    public function setApiKeyUpdatedAt(?\DateTime $apiKeyUpdatedAt): self
    {
        $this->apiKeyUpdatedAt = $apiKeyUpdatedAt;

        return $this;
    }

    public function getDeclinationDisplayType(): string
    {
        return $this->userDeclinationDisplayType;
    }

    public function setDeclinationDisplayType(string $declinationDisplayType): self
    {
        $this->userDeclinationDisplayType = $declinationDisplayType;

        return $this;
    }

    /** @return Collection|UserPasswordHistory[] */
    public function getUserPasswordHistory(): Collection
    {
        return $this->userPasswordHistory;
    }

    /** @return int[] */
    public function getQuoteRequestSelectionIds(): array
    {
        return $this->quoteRequestSelectionIds;
    }

    /** @param int[] $quoteRequestSelectionIds */
    public function setQuoteRequestSelectionIds(array $quoteRequestSelectionIds): self
    {
        $this->quoteRequestSelectionIds = $quoteRequestSelectionIds;

        return $this;
    }
}
