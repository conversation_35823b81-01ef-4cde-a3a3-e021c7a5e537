<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\User;

use Broadway\UuidGenerator\UuidGeneratorInterface;
use Doctrine\Common\Persistence\Event\LifecycleEventArgs;

class AddressBookEventListener
{
    private $idGenerator;

    public function __construct(UuidGeneratorInterface $idGenerator)
    {
        $this->idGenerator = $idGenerator;
    }

    public function prePersist(LifecycleEventArgs $args): void
    {
        $entity = $args->getObject();

        if ($entity instanceof AddressBook && $entity->getId() === '') {
            $entity->setId($this->idGenerator->generate());
        }
    }
}
