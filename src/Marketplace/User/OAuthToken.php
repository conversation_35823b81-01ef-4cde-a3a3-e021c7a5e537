<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\User;

class OAuthToken
{
    /**
     * @var string|null
     */
    private $token;

    /**
     * @var string|null
     */
    private $type;

    /**
     * @var \DateTimeImmutable|null
     */
    private $expirationDate;

    /**
     * @var string|null
     */
    private $refreshToken;

    public function __construct(string $token, string $type, string $refreshToken = null)
    {
        $this->token = $token;
        $this->type = $type;
        $this->refreshToken = $refreshToken;
    }

    public static function empty(): self
    {
        $self = new self('', '');
        $self->token = null;
        $self->type = null;

        return $self;
    }

    /**
     * Peut être vide lorsqu'il s'agit d'un utilisateur avec login standard
     * Les champs sont vide car doctrine ne supporte pas les Embeddable à null
     */
    public function isEmpty(): bool
    {
        return \is_null($this->getToken()) || (\is_string($this->getToken()) && "" === $this->getToken());
    }

    public function getToken(): ?string
    {
        return $this->token;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function expiresAt(\DateTimeImmutable $date): void
    {
        $this->expirationDate = $date;
    }

    public function hasExpirationDate(): bool
    {
        return $this->expirationDate instanceof \DateTimeImmutable;
    }

    public function getExpirationDate(): \DateTimeImmutable
    {
        return $this->expirationDate;
    }

    public function isExpired(): bool
    {
        if (!$this->hasExpirationDate()) {
            return false;
        }

        return $this->expirationDate < new \DateTimeImmutable('now');
    }

    public function refresh(string $token, string $refreshToken = null, \DateTimeImmutable $date = null): void
    {
        $this->token = $token;
        $this->refreshToken = $refreshToken;
        $this->expirationDate = $date ?? $this->expirationDate;
    }

    public function getRefreshToken(): ?string
    {
        return $this->refreshToken;
    }
}
