Wizacha\Marketplace\User\UserPasswordHistory:
    type: entity
    table: user_passwords_history
    id:
        id:
            type: integer
            nullable: false
            options:
                unsigned: true
            generator:
                strategy: AUTO
    fields:
        password:
            type: string
            nullable: false
            length: 255
        createdAt:
            type: datetime
            nullable: false
            column: created_at
    manyToOne:
        user:
            targetEntity: Wizacha\Marketplace\User\User
            inversedBy: userPasswordHistory
            joinColumn:
                name: user_id
                referencedColumnName: user_id
                nullable: false
