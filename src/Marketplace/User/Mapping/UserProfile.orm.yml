Wizacha\Marketplace\User\UserProfile:
    type: entity
    table: user_profiles
    id:
        profileId:
            type: integer
            nullable: false
            options:
                unsigned: true
            id: true
            column: profile_id
            generator:
                strategy: IDENTITY
    fields:
        userId:
            type: integer
            nullable: false
            length: 8
            options:
                unsigned: true
                default: 0
            column: user_id
        profileType:
            type: string
            nullable: false
            length: 1
            options:
                fixed: true
                default: 'P'
        externalIdentifier:
            type: string
            nullable: false
            length: 1
            options:
                fixed: true
                default: ''
            column: external_identifier
        isProfessional:
            type: integer
            nullable: false
            length: 2
            options:
                default: 0
            column: is_professional
        legalIdentifier:
            type: string
            nullable: false
            options:
                default: ''
            column: legal_identifier
