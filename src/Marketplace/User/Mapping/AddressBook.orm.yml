Wizacha\Marketplace\User\AddressBook:
    type: entity
    table: address_book
    id:
        id:
            type: guid
            length: 36
            options:
                fixed: true
    fields:
        label:
            type: string
            length: 128
            nullable: false
            options:
                default: ''
        comment:
            type: string
            length: 255
            nullable: false
            options:
                default: ''
        title:
            type: string
            length: 10
            nullable: true
        firstname:
            type: string
            nullable: false
            length: 128
            options:
                fixed: false
                default: ''
        lastname:
            type: string
            nullable: false
            length: 128
            options:
                fixed: false
                default: ''
        company:
            type: string
            nullable: false
            length: 128
            options:
                fixed: false
                default: ''
        address:
            type: string
            nullable: false
            column: address
            length: 255
            options:
                fixed: false
                default: ''
        address2:
            type: string
            nullable: false
            column: address_2
            length: 255
            options:
                fixed: false
                default: ''
        city:
            type: string
            nullable: false
            length: 64
            options:
                fixed: false
                default: ''
        country:
            type: string
            nullable: false
            length: 2
            options:
                fixed: false
                default: ''
        state:
            type: string
            nullable: false
            length: 32
            options:
                fixed: false
                default: ''
        zipcode:
            type: string
            nullable: false
            length: 16
            options:
                fixed: false
                default: ''
        phone:
            type: string
            nullable: false
            length: 32
            options:
                fixed: false
                default: ''
        division:
            type: string
            nullable: false
            length: 25
            column: division_code
            options:
                fixed: false
                default: ''
    manyToOne:
        user:
            targetEntity: Wizacha\Marketplace\User\User
            inversedBy: addressBook
            joinColumn:
                referencedColumnName: user_id
                nullable: false
