Wiza<PERSON>\Marketplace\User\User:
    type: entity
    table: users
    indexes:
        user_login:
            columns:
                - user_login
        uname:
            columns:
                - firstname
                - lastname
    id:
        userId:
            type: integer
            nullable: false
            options:
                unsigned: true
            id: true
            column: user_id
            generator:
                strategy: IDENTITY
    fields:
        status:
            type: string
            nullable: false
            length: 1
            options:
                fixed: true
                default: A
        userUuid:
            type: ramsey_uuid
            nullable: false
            length: 36
            options:
                fixed: true
            column: user_uuid
        userType:
            type: string
            nullable: false
            length: 1
            options:
                fixed: true
                default: C
            column: user_type
        userLogin:
            type: string
            nullable: false
            length: 255
            options:
                fixed: false
                default: ''
            column: user_login
        referer:
            type: string
            nullable: false
            length: 255
            options:
                fixed: false
                default: ''
        isRoot:
            type: string
            nullable: false
            length: 1
            options:
                fixed: true
                default: 'N'
            column: is_root
        companyId:
            type: integer
            nullable: false
            options:
                unsigned: true
                default: '0'
            column: company_id
        lastLogin:
            type: integer
            nullable: false
            options:
                unsigned: true
                default: '0'
            column: last_login
        failedLoginCount:
            type: integer
            nullable: false
            options:
                unsigned: true
                default: 0
            column: failed_login_count
        timestamp:
            type: integer
            nullable: false
            options:
                unsigned: true
                default: '0'
        password:
            type: string
            nullable: false
            length: 255
            options:
                fixed: false
                default: ''
        salt:
            type: string
            nullable: false
            length: 10
            options:
                fixed: false
                default: ''
        firstname:
            type: string
            nullable: false
            length: 128
            options:
                fixed: false
                default: ''
        lastname:
            type: string
            nullable: false
            length: 128
            options:
                fixed: false
                default: ''
        title:
            type: string
            length: 10
            nullable: false
            options:
                default: ''
        company:
            type: string
            nullable: false
            length: 255
            options:
                fixed: false
                default: ''
        email:
            type: string
            nullable: false
            length: 128
            options:
                fixed: false
                default: ''
        phone:
            type: string
            nullable: false
            length: 32
            options:
                fixed: false
                default: ''
        fax:
            type: string
            nullable: false
            length: 32
            options:
                fixed: false
                default: ''
        url:
            type: string
            nullable: false
            length: 128
            options:
                fixed: false
                default: ''
        taxExempt:
            type: string
            nullable: false
            length: 1
            options:
                fixed: true
                default: 'N'
            column: tax_exempt
        langCode:
            type: locale
            nullable: false
            length: 2
            options:
                fixed: true
                default: ''
            column: lang_code
        birthday:
            type: datetime
            nullable: true
        purchaseTimestampFrom:
            type: integer
            nullable: false
            options:
                unsigned: false
                default: '0'
            column: purchase_timestamp_from
        purchaseTimestampTo:
            type: integer
            nullable: false
            options:
                unsigned: false
                default: '0'
            column: purchase_timestamp_to
        responsibleEmail:
            type: string
            nullable: false
            length: 80
            options:
                fixed: false
                default: ''
            column: responsible_email
        lastPasswords:
            type: string
            nullable: false
            length: 255
            options:
                fixed: false
                default: ''
            column: last_passwords
        passwordChangeTimestamp:
            type: integer
            nullable: false
            options:
                unsigned: true
                default: '0'
            column: password_change_timestamp
        apiKey:
            type: string
            nullable: false
            unique: true
            length: 40
            options:
                fixed: false
            column: api_key
        basketId:
            type: string
            nullable: true
            length: 36
            options:
                fixed: false
            column: basket_id
        loyaltyIdentifier:
            type: string
            nullable: true
            length: 36
            options:
                fixed: false
            column: loyalty_identifier
        currencyCode:
            type: string
            nullable: true
            length: 3
            column: currency_code
        isLocked:
            type: boolean
            nullable: false
            length: 1
            option:
                default: false
            column: is_locked
        extra:
            type: text
            nullable: true
        apiKeyUpdatedAt:
            type: datetime
            nullable: true
            column: api_key_updated_at
    embedded:
        oauthToken:
            class: Wizacha\Marketplace\User\OAuthToken
    lifecycleCallbacks: {  }
    oneToMany:
        creditCards:
            targetEntity: Wizacha\Marketplace\CreditCard\CreditCard
            mappedBy: user
            orphanRemoval: true
            cascade: [remove]
        subscriptions:
            targetEntity: Wizacha\Marketplace\Subscription\Subscription
            mappedBy: user
            orphanRemoval: true
            cascade: [persist, remove]
        addressBook:
            targetEntity: Wizacha\Marketplace\User\AddressBook
            mappedBy: user
            orphanRemoval: true
            cascade: [persist, remove]
        userPasswordHistory:
            targetEntity: Wizacha\Marketplace\User\UserPasswordHistory
            mappedBy: user
            orphanRemoval: true
            cascade: [remove]
    manyToOne:
        organisation:
            targetEntity: Wizacha\Marketplace\Organisation\Organisation
            joinColumn:
                name: organisation_id
                referencedColumnName: id
                nullable: true
    manyToMany :
        nationalities :
            targetEntity : Wizacha\Marketplace\Country\Country
            joinTable :
                name : users_has_nationalities
                joinColumns :
                    user_id :
                        referencedColumnName : user_id
                        onDelete : CASCADE
                inverseJoinColumns :
                    country_code :
                        referencedColumnName : code
        groups:
            targetEntity: Wizacha\Marketplace\Group\Group
            mappedBy: users
