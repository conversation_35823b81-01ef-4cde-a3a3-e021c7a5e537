<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\User;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;

class AddressBookRepository extends ServiceEntityRepository
{
    public function findOneById(string $id): ?AddressBook
    {
        return $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->select("AddressBook")
            ->from(AddressBook::class, "AddressBook")
            ->where("AddressBook.id = :id")
            ->setParameter('id', $id)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }

    public function save(AddressBook $addressBook): AddressBook
    {
        if ($this->getEntityManager()->contains($addressBook) === false) {
            $this->getEntityManager()->persist($addressBook);
        }
        $this->getEntityManager()->flush();

        return $addressBook;
    }

    public function delete(string $id): void
    {
        $addressBook = $this->findOneById($id);

        if ($addressBook instanceof AddressBook) {
            $this->getEntityManager()->remove($addressBook);
            $this->getEntityManager()->flush();
        }
    }
}
