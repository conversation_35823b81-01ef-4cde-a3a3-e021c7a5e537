<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\User;

use MyCLabs\Enum\Enum;

/**
 * @method static UserTitle MR()
 * @method static UserTitle MRS()
 */
class UserTitle extends Enum
{
    public const MR = 'mr';
    public const MRS = 'mrs';

    public function getTranslationKey(): string
    {
        return 'user_title_' . $this->value;
    }
}
