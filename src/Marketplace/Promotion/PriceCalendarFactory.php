<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Promotion;

use Wizacha\Marketplace\Price\PriceComposition;
use Wizacha\Marketplace\Price\PriceFields;

/**
 * Creates a price calendar from promotions.
 */
class PriceCalendarFactory
{
    /**
     * @var PromotionApplier
     */
    private $catalogResolver;

    public function __construct(PromotionApplier $catalogResolver)
    {
        $this->catalogResolver = $catalogResolver;
    }

    /**
     * Create a price calendar by applying all promotions to the base price.
     *
     * @param PriceComposition $basePrice
     * @param Promotion[] $promotions
     *
     * @return array Returns prices indexed by timestamps. Prices are valid from the timestamp to the next
     *               entry's timestamp (or infinity if last in the array).
     *
     * Example:
     *
     *     [
     *         0      => PriceComposition(...), // price from timestamp 0 to 123456
     *         123456 => PriceComposition(...), // price from timestamp 123456 to 234567
     *         234567 => PriceComposition(...), // price from timestamp 234567 to the end of times
     *     ]
     */
    public function createCalendar(PriceComposition $basePrice, array $promotions): array
    {
        // List all start + end dates, which will give us all the intervals
        $dates = [
            0,          // timestamp 0 (epoch)
            2147483647, // timestamp max (on a 32b system to ensure portability)
        ];
        foreach ($promotions as $promotion) {
            $dates[] = $promotion->getStartTime()->getTimestamp();
            $dates[] = $promotion->getEndTime()->getTimestamp();
        }
        $dates = array_unique($dates); // avoid duplicates
        sort($dates);

        // Calculates the final price for each interval
        $calendar = [];
        for ($i = 0; $i < \count($dates) - 1; $i++) {
            $intervalStart = $dates[$i];
            $intervalEnd = $dates[$i + 1];

            $finalPrice = $basePrice;
            foreach ($promotions as $promotion) {
                $promotionStart = $promotion->getStartTime()->getTimestamp();
                $promotionEnd = $promotion->getEndTime()->getTimestamp();
                if ($this->intervalsOverlap($promotionStart, $promotionEnd, $intervalStart, $intervalEnd)) {
                    $tempPrice = $this->getReducedPrice($promotion, $basePrice);

                    // Keep the most favorable promotion in the interval
                    if ($tempPrice->get(PriceFields::BASE_PRICE()) < $finalPrice->get(PriceFields::BASE_PRICE())) {
                        $finalPrice = $tempPrice;

                        // If the price composition doesn't contains a crossed out price, set it to the price before promotion
                        $crossedOutPrice = $basePrice->get(PriceFields::CROSSED_OUT_PRICE());
                        if (!$crossedOutPrice || $crossedOutPrice->isZero()) {
                            $finalPrice->set(PriceFields::CROSSED_OUT_PRICE(), $basePrice->get(PriceFields::BASE_PRICE()));
                        }
                    }
                }
            }

            $calendar[$intervalStart] = $finalPrice;
        }

        return $calendar;
    }

    /**
     * Check if 2 date intervals overlap.
     *
     * @param int $interval1Start Timestamp
     * @param int $interval1End Timestamp
     * @param int $interval2Start Timestamp
     * @param int $interval2End Timestamp
     */
    private function intervalsOverlap($interval1Start, $interval1End, $interval2Start, $interval2End): bool
    {
        return $interval1Start < $interval2End && $interval1End > $interval2Start;
    }

    /**
     * Applies the promotion to a price to return the reduced price.
     */
    private function getReducedPrice(Promotion $promotion, PriceComposition $basePrice): PriceComposition
    {
        $tempPrice = clone $basePrice;
        $tempPrice = $this->catalogResolver->apply($promotion, $tempPrice);

        return $tempPrice;
    }
}
