<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Promotion;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Wizacha\Marketplace\Order\Event\OrderStatusUpdated;
use Wizacha\Marketplace\Order\Event\OrderTrashed;
use Wizacha\Marketplace\Order\OrderEvents;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\Promotion\PromotionUsage\PromotionUsageRepository;

class PromotionEventSubscriber implements EventSubscriberInterface
{
    /** @var PromotionUsageRepository  */
    protected $promotionUsageRepository;

    public function __construct(PromotionUsageRepository $promotionUsageRepository)
    {
        $this->promotionUsageRepository = $promotionUsageRepository;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            OrderEvents::UPDATED  => ['onOrderUpdate', 0],
            OrderEvents::TRASHED  => ['onOrderTrash', 0],
        ];
    }

    public function onOrderUpdate(OrderStatusUpdated $event): void
    {
        switch ($event->getStatusTo()) {
            case OrderStatus::CANCELED:
            case OrderStatus::BILLING_FAILED:
            case OrderStatus::REFUNDED:
            case OrderStatus::VENDOR_DECLINED:
            case OrderStatus::INCOMPLETED:
                $this->promotionUsageRepository->deleteUsageByOrderId($event->getId());
                break;
        }
    }

    public function onOrderTrash(OrderTrashed $event): void
    {
        $this->promotionUsageRepository->deleteUsageByOrderId($event->getId());
    }
}
