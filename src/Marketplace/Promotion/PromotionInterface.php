<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Promotion;

use Doctrine\Common\Collections\Collection;
use Wizacha\Marketplace\Promotion\BonusTarget\BonusTarget;

interface PromotionInterface
{
    public function getId(): ?string;

    public function getCompanyId(): ?int;

    public function getName(): string;

    public function getType(): PromotionType;

    public function getRules(): string;

    public function isActive(): bool;

    public function isValid(): bool;

    public function getBonuses(): Collection;

    public function getTarget(): BonusTarget;

    public function getStartTime(): \DateTime;

    public function getEndTime(): \DateTime;
}
