<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Promotion;

use Doctrine\ORM\EntityManagerInterface;
use Wizacha\Marketplace\Promotion\Exception\PromotionNotFound;

class PromotionRepository
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function findBasketPromotionsByCoupon(string $coupon): array
    {
        $builder = $this->entityManager->createQueryBuilder();
        $builder
            ->select('bp')
            ->from(BasketPromotion::class, 'bp')
            ->where("bp.coupon = :coupon")
            ->setParameter(":coupon", $coupon)
        ;

        return $builder->getQuery()->getResult();
    }

    /**
     * @param int|null $companyId
     * @param PromotionType|null $type
     * @return Promotion[]
     */
    public function findByCompany($companyId, PromotionType $type = null)
    {
        $builder = $this->entityManager->createQueryBuilder();
        $builder
            ->select('p')
            ->from('Wizacha\Marketplace\Promotion\Promotion', 'p')
            ;

        if (\is_null($companyId)) {
            $builder->where('p.companyId IS NULL');
        } else {
            $builder->where('p.companyId = :companyId');
            $builder->setParameter('companyId', $companyId);
        }

        if ($type !== null) {
            $builder->andWhere('p.type = :type');
            $builder->setParameter('type', $type);
        }

        return $builder->getQuery()->getResult();
    }

    /**
     * Search promotions active at a specific date.
     * @param int $companyId
     * @return Promotion[]
     */
    public function findActivePromotions($companyId, PromotionType $promotionType, \DateTime $activeAt)
    {
        $query = $this->entityManager->createQuery(
            'SELECT p FROM Wizacha\Marketplace\Promotion\Promotion p
             WHERE (p.companyId IS NULL OR p.companyId = :companyId)
                 AND p.active = true
                 AND p.type = :promotionType
                 AND p.startTime < :time
                 AND p.endTime > :time'
        );

        $query->setParameter('companyId', $companyId);
        $query->setParameter('promotionType', $promotionType);
        $query->setParameter('time', $activeAt);

        return $query->getResult();
    }

    /**
     * @return Promotion[]
     */
    public function findPromotionsWithRightDate(PromotionType $promotionType, \DateTime $time): array
    {
        return $this->entityManager->createQueryBuilder()
            ->select('p')
            ->from(Promotion::class, 'p')
            ->andWhere('p.type = :type')
            ->andWhere(':time BETWEEN p.startTime AND p.endTime')
            ->setParameters(['type' => $promotionType, 'time' => $time])
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * @return Promotion[]
     */
    public function findPromotionsWithWrongDate(PromotionType $promotionType, \DateTime $time): array
    {
        return $this->entityManager->createQueryBuilder()
            ->select('p')
            ->from(Promotion::class, 'p')
            ->andWhere('p.type = :type')
            ->andWhere(':time > p.endTime OR :time < p.startTime')
            ->setParameters(['type' => $promotionType, 'time' => $time])
            ->getQuery()
            ->getResult()
            ;
    }

    /**
     * @param int $companyId
     * @param \DateTime $activeSince
     * @return Promotion[]
     */
    public function findAllCatalogPromotions($companyId, \DateTime $activeSince)
    {
        $query = $this->entityManager->createQuery(
            'SELECT p FROM Wizacha\Marketplace\Promotion\Promotion p
             WHERE (p.companyId IS NULL OR p.companyId = :companyId)
                 AND p.active = true
                 AND p.type = :promotionType
                 AND p.endTime > :time'
        );

        $query->setParameter('companyId', $companyId);
        $query->setParameter('promotionType', PromotionType::CATALOG());
        $query->setParameter('time', $activeSince);

        return $query->getResult();
    }

    /**
     * @param string $id
     * @return Promotion
     * @throws PromotionNotFound Promotion not found
     */
    public function get($id)
    {
        $promotion = $this->entityManager->find(Promotion::class, $id);
        if (!$promotion) {
            throw new PromotionNotFound($id);
        }

        return $promotion;
    }

    public function save(Promotion $promotion)
    {
        $this->entityManager->persist($promotion);
        $this->entityManager->flush();
    }

    /**
     * @param string $id
     */
    public function delete($id)
    {
        $promotion = $this->entityManager->find(Promotion::class, $id);

        if ($promotion) {
            $this->entityManager->remove($promotion);
            $this->entityManager->flush();
        }
    }

    /**
     * Returns promotions starting this day and ending yesterday
     * @return Promotion[]
     */
    public function getCatalogPromotionsToRefresh(): array
    {
        // If we are the 25 / 09 at 16h30, we have the following values (after each line in comment):
        $todayMorning = new \DateTimeImmutable('today');  // 25/09 00:00:00
        $timeYesterdayMorning = new \DateTimeImmutable('yesterday'); // 24/09 00:00:00
        $timeYesterdayNight = $todayMorning->sub(new \DateInterval('PT1S')); // 24/09 23:59:59
        $timeTodayNight = $timeYesterdayNight->add(new \DateInterval('P1D')); // 25/09 23:59:59

        $query = $this->entityManager->createQuery(
            'SELECT p FROM Wizacha\Marketplace\Promotion\Promotion p
             WHERE p.active = true
             AND p.type = :promotionType
             AND (
                (p.endTime >= :timeYesterdayMorning AND p.endTime <= :timeYesterdayNight)
                OR
                (p.startTime >= :timeTodayMorning AND p.startTime <= :timeTodayNight)
             )'
        );

        $query->setParameter('promotionType', PromotionType::CATALOG());
        $query->setParameter('timeYesterdayMorning', $timeYesterdayMorning);
        $query->setParameter('timeYesterdayNight', $timeYesterdayNight);
        $query->setParameter('timeTodayMorning', $todayMorning);
        $query->setParameter('timeTodayNight', $timeTodayNight);

        return $query->getResult();
    }

    /**
     * @param string[] $ids
     * @param PromotionType[] $types
     *
     * @return Promotion[]
     */
    public function findTypedPromotionsByIds(array $ids, array $types = null): array
    {
        $qb = $this->entityManager->createQueryBuilder();

        $qb
            ->select('p')
            ->from(Promotion::class, 'p')
            ->where('p.id IN (:ids)')
            ->setParameter('ids', $ids)
        ;

        if ($types !== null) {
            $qb
                ->andWhere('p.type IN (:type)')
                ->setParameter('type', $types)
            ;
        }

        return $qb->getQuery()->getResult();
    }
}
