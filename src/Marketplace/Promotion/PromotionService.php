<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Promotion;

use Broadway\UuidGenerator\UuidGeneratorInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Wizacha\AppBundle\Controller\Api\Promotion\BasketPromotionNormalizer;
use Wizacha\Component\DateRange\DateRange;
use Wizacha\Component\DateRange\DateRangeComparator;
use Wizacha\Events\IterableEvent;
use Wizacha\Marketplace\Entities\Declination;
use Wizacha\Marketplace\Group\UserGroupService;
use Wizacha\Marketplace\Price\PriceComposition;
use Wizacha\Marketplace\Price\PriceFields;
use Wizacha\Marketplace\Promotion\Exception\PromotionNotFound;
use Wizacha\Marketplace\Promotion\PromotionUsage\PromotionUsageRepository;
use Wizacha\Marketplace\Promotion\Rule\BasketSubject;
use Wizacha\Marketplace\Promotion\Rule\Exception\InvalidRules;
use Wizacha\Marketplace\Promotion\Rule\ProductSubject;
use Wizacha\Marketplace\Promotion\Rule\RuleEngine;
use Wizacha\Marketplace\ReadModel\Basket;
use Wizacha\Marketplace\ReadModel\BasketPrices;
use Wizacha\Money\Money;
use Wizacha\Product;

class PromotionService
{
    /**
     * @var PromotionRepository
     */
    private $repository;

    /**
     * @var PromotionUsageRepository
     */
    private $usageRepository;

    /**
     * @var UuidGeneratorInterface
     */
    private $idGenerator;

    /**
     * @var ProductPromotionApplier
     */
    private $productPromotionApplier;

    /**
     * @var BasketPromotionApplier
     */
    private $basketPromotionApplier;

    /**
     * @var EventDispatcherInterface
     */
    private $eventDispatcher;

    /**
     * @var RuleEngine
     */
    private $ruleEngine;

    /**
     * @var PriceCalendarFactory
     */
    private $priceCalendarFactory;
    /**
     * @var PromotionSerializer
     */
    private $serializer;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /** @var MarketplacePromotionRepository */
    private $marketplacePromotionRepository;

    private BasketPromotionNormalizer $basketPromotionNormalizer;

    private UserGroupService $userGroupService;

    public function __construct(
        PromotionRepository $repository,
        PromotionUsageRepository $usageRepository,
        UuidGeneratorInterface $idGenerator,
        ProductPromotionApplier $productPromotionApplier,
        BasketPromotionApplier $basketPromotionApplier,
        EventDispatcherInterface $eventDispatcher,
        RuleEngine $ruleEngine,
        PriceCalendarFactory $priceCalendarFactory,
        PromotionSerializer $serializer,
        LoggerInterface $logger,
        MarketplacePromotionRepository $marketplacePromotionRepository,
        BasketPromotionNormalizer $basketPromotionNormalizer,
        UserGroupService $userGroupService
    ) {
        $this->repository = $repository;
        $this->usageRepository = $usageRepository;
        $this->idGenerator = $idGenerator;
        $this->productPromotionApplier = $productPromotionApplier;
        $this->basketPromotionApplier = $basketPromotionApplier;
        $this->eventDispatcher = $eventDispatcher;
        $this->ruleEngine = $ruleEngine;
        $this->priceCalendarFactory = $priceCalendarFactory;
        $this->serializer = $serializer;
        $this->logger = $logger;
        $this->marketplacePromotionRepository = $marketplacePromotionRepository;
        $this->basketPromotionNormalizer = $basketPromotionNormalizer;
        $this->userGroupService = $userGroupService;
    }

    /** @return array [MarketplacePromotion[], int totalCount] */
    public function listMarketplacePromotionsPaginated(int $start, int $limit, array $filters = []): array
    {
        return $this->marketplacePromotionRepository->findPaginated($start, $limit, $filters);
    }

    /**
     * @return MarketplacePromotion[]
     */
    public function findMarketplacePromotionByCoupon(string $coupon): array
    {
        return $this->marketplacePromotionRepository->findByCoupon($coupon);
    }

    /**
     * @return BasketPromotion[]
     */
    public function findBasketPromotionByCoupon(string $coupon): array
    {
        return $this->repository->findBasketPromotionsByCoupon($coupon);
    }

    /**
     * @param int|null $companyId
     * @param PromotionType|null $type
     * @return Promotion[]
     */
    public function findByCompany(?int $companyId, PromotionType $type = null): array
    {
        return $this->repository->findByCompany($companyId, $type);
    }

    /**
     * @param string $id
     * @return Promotion
     * @throws PromotionNotFound Promotion not found
     */
    public function get(string $id): Promotion
    {
        return $this->repository->get($id);
    }

    /**
     * @param string[] $ids
     * @param PromotionType[] $types
     *
     * @return Promotion[]
     */
    public function getTypedPromotionsByIds(array $ids, array $types = null): array
    {
        return $this->repository->findTypedPromotionsByIds($ids, $types);
    }

    /**
     * @param string $id
     *
     * @return MarketplacePromotion
     */
    public function getMarketplacePromotion(string $id): MarketplacePromotion
    {
        return $this->marketplacePromotionRepository->get($id);
    }

    /**
     * @throws PromotionNotFound
     * @throws InvalidRules
     */
    public function save(Promotion $promotion): void
    {
        $this->ruleEngine->validateRules($promotion->getRules());

        $promotion->setIsValid(true);

        if ($promotion->hasTypeCatalog()) {
            $productIds = $this->ruleEngine->getProductsMatchingRules($promotion->getRules(), $promotion->getCompanyId());

            if (!$promotion->getId()) {
                // Assign a new ID
                $promotion->setId($this->idGenerator->generate());
            }

            $this->repository->save($promotion);

            // Update product prices
            $this->triggerProductsUpdate($productIds);
        } elseif ($promotion->hasTypeBasket() || $promotion->hasTypeMarketplace()) {
            if (!$promotion->getId()) {
                $promotion->setId($this->idGenerator->generate());
            }

            if ($promotion->hasTypeMarketplace()) {
                $promotion->setIsValid(DateRangeComparator::contains(
                    new DateRange($promotion->getStartTime(), $promotion->getEndTime()),
                    new \DateTime()
                ));
            }

            $this->repository->save($promotion);
        }
    }

    public function saveFromSerializedData(Promotion $promotion, array $data): void
    {
        if (PromotionType::CATALOG()->equals($promotion->getType())) {
            // On récupere les anciens produits pour pouvoir les mettre à
            // jour s'ils ont été supprimé de la promotion entretemps.
            $oldProductsId = $this->ruleEngine->getProductsMatchingRules($promotion->getRules(), $promotion->getCompanyId());
            $promotion = $this->serializer->deserialize($data, $promotion);
            $this->save($promotion);

            $oldProductsId = array_diff(
                $oldProductsId,
                $this->ruleEngine->getProductsMatchingRules($promotion->getRules(), $promotion->getCompanyId())
            );

            // Update product prices
            $this->triggerProductsUpdate($oldProductsId);
        } elseif (PromotionType::BASKET()->equals($promotion->getType())) {
            $promotion = $this->serializer->deserialize($data, $promotion);
            $this->save($promotion);
        }
    }

    /**
     * @param string $id
     * @throws PromotionNotFound Promotion not found
     */
    public function delete(string $id): void
    {
        $promotion = $this->get($id);
        $productIds = [];

        if (PromotionType::CATALOG()->equals($promotion->getType())) {
            try {
                $productIds = $this->ruleEngine->getProductsMatchingRules($promotion->getRules(), $promotion->getCompanyId());
            } catch (InvalidRules $e) {
                $this->logger->warning('Deleted promotion with invalid rules: ' . $e->getMessage(), [
                    'exception' => $e,
                ]);
            }
        }

        $this->repository->delete($id);
        $this->triggerProductsUpdate($productIds);
    }

    public function disable(string $id): self
    {
        $promotion = $this->get($id);
        $productIds = [];

        if (PromotionType::CATALOG()->equals($promotion->getType())) {
            try {
                $productIds = $this->ruleEngine->getProductsMatchingRules($promotion->getRules(), $promotion->getCompanyId());
            } catch (InvalidRules $e) {
                $this->logger->warning('Deleted promotion with invalid rules: ' . $e->getMessage(), [
                    'exception' => $e,
                ]);
            }
        }

        $promotion->setActive(false);
        $this->repository->save($promotion);
        $this->triggerProductsUpdate($productIds);

        return $this;
    }

    public function validate(string $id): self
    {
        $promotion = $this->get($id);
        $promotion->setIsValid(true);

        $this->repository->save($promotion);

        return $this;
    }

    public function invalidate(string $id): self
    {
        $promotion = $this->get($id);
        $promotion->setIsValid(false);
        $promotion->setActive(false);

        $this->repository->save($promotion);

        return $this;
    }

    public function getProductFinalPrice(Declination $declination, int $quantity = null): PriceComposition
    {
        $promotions = $this->getPromotionsCurrentlyApplyingToDeclination($declination);

        $finalPrice = $declination->getPriceComposition($quantity);
        $basePrice = $finalPrice;

        foreach ($promotions as $promotion) {
            $tempPrice = clone $basePrice;
            $tempPrice = $this->productPromotionApplier->apply($promotion, $tempPrice);

            // Keep the most favorable promotion
            if ($finalPrice->get(PriceFields::BASE_PRICE()) > $tempPrice->get(PriceFields::BASE_PRICE())) {
                $finalPrice = $tempPrice;
            }
        }

        return $finalPrice;
    }

    /**
     * Calculate the price calendar for the given product/declination.
     *
     * @return PriceComposition[] Returns prices indexed by timestamps. Prices are valid from the timestamp to the next
     *         entry's timestamp (or infinity if last in the array).
     * Example:
     *
     *     [
     *         0      => PriceComposition(...), // price from timestamp 0 to 123456
     *         123456 => PriceComposition(...), // price from timestamp 123456 to 234567
     *         234567 => PriceComposition(...), // price from timestamp 234567 to the end of times
     *     ]
     */
    public function getProductPriceCalendar(Declination $product): array
    {
        $subject = ProductSubject::fromDeclination($product);
        $basePrice = $product->getPriceComposition();

        // Apply all promotions
        $promotions = array_filter(
            $this->repository->findAllCatalogPromotions($product->getCompany()->getId(), new \DateTime()),
            function (Promotion $promotion) use ($subject) {
                try {
                    return $this->ruleEngine->productSatisfiesRules($subject, $promotion->getRules(), $promotion->getCompanyId());
                } catch (InvalidRules $e) {
                    // Skip invalid promotions
                    return false;
                }
            }
        );

        return $this->priceCalendarFactory->createCalendar($basePrice, $promotions);
    }

    /**
     * Application des promotions dans le panier.
     *
     * Les promotions sont appliquées sur les shippingsGroup puis repercutées sur les companyGroups
     * et sur le panier global.
     *
     * @param array $basketData Data returned by Basket::getData()
     * @param int $userId
     * @return array Updated basket data
     * @see \Wizacha\Marketplace\ReadModel\Basket::getData()
     */
    public function refreshBasketPrices(array $basketData, int $userId = null, array $productsQuantities = []): array
    {
        $promotionsApplied = [];
        $coupons = (array) $basketData['coupons'];

        // Update the prices for each sub-basket (per company)
        foreach ($basketData['groups'] as &$companyGroup) {
            // Limitation du système : on ne peut pas appliquer des promos sur un
            // company group qui contient plusieurs shipping groups
            // Dans ce cas on applique les promos que sur le shipping group le plus avantageux et tant pis pour
            // les autres.


            $companyId = (int) $companyGroup['companyId'];
            $quantityTotal = array_sum(array_column($companyGroup['all_items'], 'quantity'));
            $promotions = $this->findPromotionsApplyingToBasket(
                $coupons,
                $companyGroup['prices'],
                PromotionType::BASKET(),
                $userId,
                $companyId,
                $quantityTotal
            );

            // Si on n'a pas de promotions qui s'applique, on passe au groupe suivant
            if (!$promotions) {
                continue;
            }

            //Pour chaque shippingGroup, trouver la meilleure promotion et renvoyer :
            //   $companyGroup correspondant
            //   la promotion appliquée
            //   le montant total du nouveau shippingGroup (utile pour trier et trouver la meilleure promo)
            //null si aucune promo ne s'est appliquée
            $reducedShippingGroup = array_filter(array_map(
                function ($shippingGroup) use ($promotions, $productsQuantities) {
                    $prices = $shippingGroup['prices'];
                    $finalPrices = clone $prices;
                    $originalPrice = $prices->get(PriceFields::BASKET_TOTAL())->add($finalPrices->get(PriceFields::SHIPPING_TOTAL()));
                    $promotionsApplied = null;

                    // BASKET_TOTAL doesn't contains shipping cost, theses 2 values are the true total of the basket
                    $finalTotalAmount = $prices->get(PriceFields::BASKET_TOTAL())->add($prices->get(PriceFields::SHIPPING_TOTAL()));

                    // Trouver la promotion la plus avantageuse pour ce shippingGroup
                    foreach ($promotions as $promotion) {
                        $tempPrices = $this->basketPromotionApplier->apply($promotion, clone $prices, $productsQuantities);

                        // BASKET_TOTAL doesn't contains shipping cost, theses 2 values are the true total of the basket
                        $tempTotalAmount = $tempPrices->get(PriceFields::BASKET_TOTAL())->add($tempPrices->get(PriceFields::SHIPPING_TOTAL()));

                        // Keep the most favorable promotion
                        if ($finalTotalAmount > $tempTotalAmount) {
                            $finalPrices = $tempPrices;
                            $finalTotalAmount = $tempTotalAmount;
                            $promotionsApplied = $promotion->getId();
                        }
                    }

                    if ($promotionsApplied !== null) {
                        return [
                            'finalPrices' => $finalPrices,
                            'promotionId' => $promotionsApplied,
                            'promotionValue' => $originalPrice->subtract($finalTotalAmount),
                            'shippingGroupId' => $shippingGroup['simpleGroupId'],
                        ];
                    }

                    return null;
                },
                $companyGroup['products']
            ));

            if (!empty($reducedShippingGroup)) {
                // Trier les shippings groupes par promotion decroissant, comme ca le premier est celui à utiliser dans
                // le companyGroup
                uasort($reducedShippingGroup, function ($a, $b) {
                    return -($a['promotionValue']->compareTo($b['promotionValue']));
                });

                // On a trié $reducedShippingGroup pour que le premier element soit le plus avantageux.
                // On le récupere et on applique les prix avec la promotion au shippingGroup concerné
                $newInfos = reset($reducedShippingGroup);
                $companyGroup['products'][$newInfos['shippingGroupId']]['prices'] = $newInfos['finalPrices'];
                $promotionsApplied[] = $newInfos['promotionId'];
            }
        }

        // Record the IDs of promotions that were applied to the basket
        $basketData['promotions'] = array_unique($promotionsApplied);
        Basket::recalculateBasketDataPricesFromShippingGroup($basketData);

        return $basketData;
    }

    public function refreshBasketTotals(
        BasketPrices $basketPrices,
        int $userId = null,
        $totalQuantity = 0,
        array $productsQuantities = []
    ): BasketPrices {
        $promotionsMarketplace = current($this->findPromotionsApplyingToBasket(
            $basketPrices->getCoupons(),
            $basketPrices->getPrices(),
            PromotionType::MARKETPLACE(),
            $userId,
            null,
            $totalQuantity
        ));

        if (false === $promotionsMarketplace instanceof MarketplacePromotion) {
            return $basketPrices;
        }

        $oldTotal = $basketPrices->getTotal();
        $newPrices = $this->basketPromotionApplier->apply($promotionsMarketplace, $basketPrices->getPrices(), $productsQuantities);
        $basketPrices->setPrices($newPrices);

        if ($oldTotal > $basketPrices->getTotal()) {
            $basketPrices->setMarketplaceDiscountId($promotionsMarketplace->getId());
        }

        return $basketPrices;
    }

    /**
     * @return Promotion[]
     */
    public function findPromotionsWithRightDate(PromotionType $type, \DateTime $time): array
    {
        return $this->repository->findPromotionsWithRightDate($type, $time);
    }

    /**
     * @return Promotion[]
     */
    public function findPromotionsWithWrongDate(PromotionType $type, \DateTime $time): array
    {
        return $this->repository->findPromotionsWithWrongDate($type, $time);
    }

    /**
     * Record the fact that a promotion was used.
     */
    public function recordPromotionUsage(int $userId, string $promotionId, int $orderId): void
    {
        $this->usageRepository->recordUsage($userId, $promotionId, $orderId);
    }

    /**
     * @see \Wizacha\Marketplace\Promotion\PromotionRepository::getCatalogPromotionsToRefresh
     * @return string[] Product ids
     */
    public function getProductsToRefreshInSearch(): array
    {
        $promotions = $this->repository->getCatalogPromotionsToRefresh();

        $productIds = [];
        foreach ($promotions as $promotion) {
            $productIds = array_unique(array_merge(
                $productIds,
                $this->ruleEngine->getProductsMatchingRules($promotion->getRules(), $promotion->getCompanyId())
            ));
        }

        return $productIds;
    }

    /**
     * Returns promotions applying *right now* to the product.
     * @return Promotion[]
     */
    public function getPromotionsCurrentlyApplyingToDeclination(Declination $declination): array
    {
        $companyId = $declination->getCompany()->getId();
        $subject = ProductSubject::fromDeclination($declination);

        return array_filter(
            $this->repository->findActivePromotions($companyId, PromotionType::CATALOG(), new \DateTime()),
            function (Promotion $promotion) use ($subject) {
                try {
                    return $this->ruleEngine->productSatisfiesRules($subject, $promotion->getRules(), $promotion->getCompanyId());
                } catch (InvalidRules $e) {
                    // Skip invalid promotions
                    return false;
                }
            }
        );
    }

    /**
     * @param Promotion[] $promotions
     */
    private function needGroupIds(array $promotions): bool
    {
        foreach ($promotions as $promotion) {
            $rules = $this->getRules($promotion->getId());

            //Single rule
            if (\is_array($rules) === true && \array_key_exists('type', $rules) === true && $rules['type'] === 'basket_has_users_groups') {
                return true;
            }

            //Multiple rules
            if (\is_array($rules) === true && \array_key_exists('items', $rules) === true && \count($rules['items']) > 0) {
                foreach ($rules['items'] as $item) {
                    if ($item['type'] === 'basket_has_users_groups') {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    private function triggerProductsUpdate(array $productIds): void
    {
        if ($productIds) {
            $event = (new IterableEvent())->setArray($productIds);

            $this->eventDispatcher->dispatch($event, Product::EVENT_UPDATE);
        }
    }

    /**
     * @return BasketPromotion[]
     */
    private function findPromotionsApplyingToBasket(
        array $coupons,
        PriceComposition $prices,
        PromotionType $type,
        int $userId = null,
        int $companyId = null,
        int $totalQuantity = 0
    ): array {
        // Find promotions active now
        $promotions = $this->repository->findActivePromotions($companyId, $type, new \DateTime());

        // Retrieve all declinations prices
        $productsPrices = [];
        foreach ($prices->get(PriceFields::PRODUCTS()) ?? [] as $declination) {
            $productsPrices = \array_merge(
                $productsPrices,
                \array_column($declination, PriceFields::PRODUCT_PRICE_WITH_TAX)
            );
        }

        $productsIds = $productsPrices ? array_keys($prices->get(PriceFields::PRODUCTS()) ?? []) : [];

        $totalDiscount = $prices->get(PriceFields::BASKET_DISCOUNT());
        $totalAmount = $prices->get(PriceFields::BASKET_TOTAL());

        if ($totalDiscount instanceof Money) {
            $totalAmount = $totalAmount->subtract($totalDiscount);
        }

        $groupsIds = (
            $userId && $this->needGroupIds($promotions)
            ? $this->userGroupService->getGroupIdsByUserId($userId)
            : []
        );

        $subject = new BasketSubject(
            $companyId,
            $totalAmount,
            $coupons,
            $productsIds,
            0,
            0,
            $totalQuantity,
            $groupsIds
        );

        // Filter promotions to keep only those applying to the given basket
        return (array) array_filter($promotions, function (CouponBasketInterface $promotion) use ($type, $subject, $userId) {
            if ($type === PromotionType::MARKETPLACE() && false === \is_string($promotion->getCoupon())) {
                return false;
            }

            $subject->usageCount = $this->usageRepository->countByPromotion($promotion);
            $subject->userUsageCount = $userId ? $this->usageRepository->countByPromotion($promotion, $userId) : 0;
            try {
                return $this->ruleEngine->basketSatisfiesRules(
                    $subject,
                    $promotion->getRules(),
                    $promotion->getCompanyId(),
                    $promotion->getCoupon()
                );
            } catch (InvalidRules $e) {
                // Skip invalid promotions
                return false;
            }
        });
    }

    public function getRules(string $promotionId): ?array
    {
        try {
            $rules = $this->basketPromotionNormalizer->normalize($this->get($promotionId))['rule'];
        } catch (\Exception $exception) {
            $rules = null;
        }

        return $rules;
    }
}
