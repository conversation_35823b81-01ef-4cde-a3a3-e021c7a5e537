<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Promotion;

use Wizacha\Marketplace\Price\PriceComposition;
use Wizacha\Marketplace\Price\PriceFields;
use Wizacha\Marketplace\Promotion\Bonus\FixedBonus;
use Wizacha\Marketplace\Promotion\Bonus\PercentageBonus;
use Wizacha\Marketplace\Promotion\BonusTarget\BonusTargetBasket;
use Wizacha\Marketplace\Promotion\BonusTarget\BonusTargetProductCategoryInBasket;
use Wizacha\Marketplace\Promotion\BonusTarget\BonusTargetProductInBasket;
use Wizacha\Marketplace\Promotion\BonusTarget\BonusTargetShipping;
use Wizacha\Money\Money;

class BasketPromotionApplier implements PromotionApplier
{
    public function apply(Promotion $promotion, PriceComposition $price, array $productsQuantities = []): PriceComposition
    {
        $oldPriceComposition = clone $price;
        $bonusTarget = $promotion->getTarget();

        if ($bonusTarget instanceof BonusTargetBasket) {
            $price = $this->applyTargetBasket($promotion, $price);
        } elseif ($bonusTarget instanceof BonusTargetProductInBasket) {
            $price = $this->applyTargetProductInBasket($promotion, $price, $oldPriceComposition, $productsQuantities);
        } elseif ($bonusTarget instanceof BonusTargetProductCategoryInBasket) {
            $price = $this->applyTargetProductCategoryInBasket($promotion, $price, $oldPriceComposition, $productsQuantities);
        } elseif ($bonusTarget instanceof BonusTargetShipping) {
            $price = $this->applyTargetShipping($promotion, $price);
        }

        $price->set(
            PriceFields::TAXES(),
            $this->getPriceField(PriceFields::SHIPPING_TAX(), $price)->add($this->getPriceField(PriceFields::BASKET_TAX(), $price))
        );

        $price->set(PriceFields::BASKET_TOTAL_TAXES(), $price->get(PriceFields::TAXES()));

        if ($promotion instanceof MarketplacePromotion) {
            $discount = $oldPriceComposition->get(PriceFields::BASKET_TOTAL())->subtract($price->get(PriceFields::BASKET_TOTAL()));
            $price->set(PriceFields::BASKET_DISCOUNT_MARKETPLACE(), $discount->add(
                $oldPriceComposition->get(PriceFields::SHIPPING_TOTAL())->subtract($price->get(PriceFields::SHIPPING_TOTAL()))
            ));
        }

        if ($oldPriceComposition->has(PriceFields::SHIPPING_TOTAL()) === true) {
            $price->set(PriceFields::SHIPPING_REAL(), $oldPriceComposition->get(PriceFields::SHIPPING_TOTAL()));
        }

        return $price;
    }

    private function applyTargetBasket(Promotion $promotion, PriceComposition $price): PriceComposition
    {
        //Apply each bonus and transfer the reduced price on taxes
        foreach ($promotion->getBonuses() as $bonus) {
            $oldTotal = $price->get(PriceFields::BASKET_TOTAL());
            $currentTax = $this->getPriceField(PriceFields::BASKET_TAX(), $price);
            $price->set(PriceFields::BASKET_TOTAL(), $bonus->applyTo($oldTotal));
            $currentTotal = $price->get(PriceFields::BASKET_TOTAL());
            if ($currentTotal != $oldTotal) {
                $price->set(
                    PriceFields::BASKET_TAX(),
                    $this->getNewTax($currentTax, $oldTotal, $currentTotal)
                );
            }
        }

        return $price;
    }

    private function applyTargetProductInBasket(Promotion $promotion, PriceComposition $price, PriceComposition $oldPriceComposition, array $productsQuantities = []): PriceComposition
    {
        /** @var BonusTargetProductInBasket $bonusTarget */
        $bonusTarget = $promotion->getTarget();
        $productsInBasket = $price->get(PriceFields::PRODUCTS());
        $productsWithPromotion = $bonusTarget->getProductIds();
        $newTotal = new Money(0);
        $newTotalTax = new Money(0);

        // Apply each bonus and transfer the reduced price on taxes
        foreach ($productsInBasket as $productId => $declination) {
            foreach ($declination as $declinationId => $productMoney) {
                if (\in_array($productId, $productsWithPromotion)) {
                    $oldPrice = $newPrice = $productMoney[PriceFields::PRODUCT_PRICE_WITH_TAX];
                    $oldTax = $newTax = $productMoney[PriceFields::PRODUCT_TAX];
                    foreach ($promotion->getBonuses() as $bonus) {
                        $newPrice = $bonus->applyTo($newPrice, $productsQuantities[$declinationId]);
                    }

                    if ($oldPrice != $newPrice) {
                        if ($newPrice->isNegative()) {
                            $newPrice = Money::fromVariable(0);
                        }

                        $productsInBasket[$productId][$declinationId] = [
                            PriceFields::PRODUCT_PRICE_WITH_TAX => $newPrice,
                            PriceFields::PRODUCT_TAX => $this->getNewTax($oldTax, $oldPrice, $newPrice),
                        ];
                    }

                    $newTotal = $newTotal->add($newPrice);
                } else {
                    $newTotal = $newTotal->add($productMoney[PriceFields::PRODUCT_PRICE_WITH_TAX]);
                }

                $newTotalTax = $newTotalTax->add($productsInBasket[$productId][$declinationId][PriceFields::PRODUCT_TAX]);
            }
        }

        if ($promotion instanceof MarketplacePromotion === true) {
            $maxBonusAmount = null;
            foreach ($promotion->getBonuses() as $bonus) {
                if (($bonus instanceof FixedBonus || $bonus instanceof PercentageBonus) && $bonus->getMaxAmount() instanceof Money === true) {
                    if ($maxBonusAmount instanceof Money === true) {
                        $maxBonusAmount = $maxBonusAmount->add($bonus->getMaxAmount());
                    } else {
                        $maxBonusAmount = $bonus->getMaxAmount();
                    }
                }
            }

            if ($maxBonusAmount instanceof Money === true && $newTotal < $oldPriceComposition->get(PriceFields::BASKET_TOTAL())->subtract($maxBonusAmount)) {
                $newTotal = $oldPriceComposition->get(PriceFields::BASKET_TOTAL())->subtract($maxBonusAmount);
            }
        }

        $price->set(PriceFields::PRODUCTS(), $productsInBasket);
        $price->set(PriceFields::BASKET_TOTAL(), $newTotal);
        $price->set(PriceFields::BASKET_TAX(), $newTotalTax);

        return $price;
    }

    private function applyTargetProductCategoryInBasket(Promotion $promotion, PriceComposition $price, PriceComposition $oldPriceComposition, array $productsQuantities = []): PriceComposition
    {
        /** @var BonusTargetProductCategoryInBasket $bonusTarget */
        $bonusTarget = $promotion->getTarget();

        $productsInBasket = $price->get(PriceFields::PRODUCTS());
        $categoriesWithPromotion = $bonusTarget->getProductCategoryIdsWithChildren();
        $newTotal = new Money(0);
        $newTotalTax = new Money(0);

        // Apply each bonus and transfer the reduced price on taxes
        foreach ($productsInBasket as $productId => $declination) {
            foreach ($declination as $declinationId => $productMoney) {
                if (\in_array(fn_get_product_category_id($productId), $categoriesWithPromotion) === true) {
                    $oldPrice = $newPrice = $productMoney[PriceFields::PRODUCT_PRICE_WITH_TAX];
                    $oldTax = $newTax = $productMoney[PriceFields::PRODUCT_TAX];
                    foreach ($promotion->getBonuses() as $bonus) {
                        $newPrice = $bonus->applyTo($newPrice, $productsQuantities[$declinationId]);
                    }

                    if ($oldPrice != $newPrice) {
                        if ($newPrice->isNegative()) {
                            $newPrice = Money::fromVariable(0);
                        }

                        $productsInBasket[$productId][$declinationId] = [
                            PriceFields::PRODUCT_PRICE_WITH_TAX => $newPrice,
                            PriceFields::PRODUCT_TAX => $this->getNewTax($oldTax, $oldPrice, $newPrice),
                        ];
                    }

                    $newTotal = $newTotal->add($newPrice);
                } else {
                    $newTotal = $newTotal->add($productMoney[PriceFields::PRODUCT_PRICE_WITH_TAX]);
                }

                $newTotalTax = $newTotalTax->add($productsInBasket[$productId][$declinationId][PriceFields::PRODUCT_TAX]);
            }
        }

        if ($promotion instanceof MarketplacePromotion === true) {
            $maxBonusAmount = null;
            foreach ($promotion->getBonuses() as $bonus) {
                if (($bonus instanceof FixedBonus || $bonus instanceof PercentageBonus) && $bonus->getMaxAmount() instanceof Money === true) {
                    if ($maxBonusAmount instanceof Money === true) {
                        $maxBonusAmount = $maxBonusAmount->add($bonus->getMaxAmount());
                    } else {
                        $maxBonusAmount = $bonus->getMaxAmount();
                    }
                }
            }
            if ($maxBonusAmount instanceof Money === true && $newTotal < $oldPriceComposition->get(PriceFields::BASKET_TOTAL())->subtract($maxBonusAmount)) {
                $newTotal = $oldPriceComposition->get(PriceFields::BASKET_TOTAL())->subtract($maxBonusAmount);
            }
        }

        $price->set(PriceFields::PRODUCTS(), $productsInBasket);
        $price->set(PriceFields::BASKET_TOTAL(), $newTotal);
        $price->set(PriceFields::BASKET_TAX(), $newTotalTax);

        return $price;
    }

    private function applyTargetShipping(Promotion $promotion, PriceComposition $price): PriceComposition
    {
        $oldShippingPrice = $newShippingPrice = $price->get(PriceFields::SHIPPING_TOTAL());
        foreach ($promotion->getBonuses() as $bonus) {
            $newShippingPrice = $bonus->applyTo($newShippingPrice);
        }

        if ($oldShippingPrice != $newShippingPrice) {
            if ($newShippingPrice->isNegative()) {
                $newShippingPrice = Money::fromVariable(0);
            }

            $oldShippingTax = $this->getPriceField(PriceFields::SHIPPING_TAX(), $price);
            $price->set(PriceFields::SHIPPING_TOTAL(), $newShippingPrice);
            $price->set(PriceFields::SHIPPING_TAX(), $this->getNewTax($oldShippingTax, $oldShippingPrice, $newShippingPrice));
        }

        return $price;
    }

    /**
     * Prevent fatal when basket model change
     */
    private function getPriceField(PriceFields $field, PriceComposition $price): Money
    {
        return $price->get($field) ?: Money::fromVariable(0);
    }

    /**
     * Apply ratio between new and old price to tax
     */
    private function getNewTax(Money $previousTax, Money $previousPrice, Money $newPrice): Money
    {
        return $previousTax->multiply($newPrice->getAmount() / $previousPrice->getAmount());
    }
}
