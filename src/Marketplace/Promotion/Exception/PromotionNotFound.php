<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Promotion\Exception;

use Symfony\Component\HttpFoundation\Response;
use Wizacha\AppBundle\Controller\Api\Error\ApiErrorResponseProvider;
use Wizacha\Marketplace\Exception\ErrorCode;
use Wizacha\AppBundle\Controller\Api\Error\ErrorResponse;
use Wizacha\Marketplace\Exception\NotFound;

class PromotionNotFound extends NotFound implements ApiErrorResponseProvider
{
    /** @var string */
    private $promotionId;

    public function __construct(string $promotionId)
    {
        parent::__construct('promotion not found', ErrorCode::PROMOTION_NOT_FOUND()->getValue());
        $this->promotionId = $promotionId;
    }

    public function toApiErrorResponse(): ErrorResponse
    {
        return new ErrorResponse(
            ErrorCode::PROMOTION_NOT_FOUND(),
            'promotion not found',
            [
                'promotionId' => $this->promotionId,
            ],
            Response::HTTP_NOT_FOUND
        );
    }
}
