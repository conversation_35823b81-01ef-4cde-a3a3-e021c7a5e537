Wizacha\Marketplace\Promotion\Promotion:
    type: entity
    inheritanceType: JOINED
    discriminatorColumn:
        name: discriminator
        type: php_enum_promotion_type
    discriminatorMap:
        basket: BasketPromotion
        catalog: Promotion
        marketplace: MarketplacePromotion
    id:
        id:
            type: guid
    fields:
        companyId:
            type: integer
            nullable: true
        active:
            type: boolean
        isValid:
            type: boolean
        name:
            type: string
        rules:
            type: text
        type:
            type: php_enum_promotion_type
        startTime:
            type: datetime_utc
        endTime:
            type: datetime_utc
        target:
            type: bonus_target
    oneToMany:
        bonuses:
            targetEntity: Wizacha\Marketplace\Promotion\Bonus\Bonus
            mappedBy: promotion
            orphanRemoval: true
            cascade: [ all ]
            fetch: EAGER
    indexes:
        company_id_index:
            columns: [ company_id ]
