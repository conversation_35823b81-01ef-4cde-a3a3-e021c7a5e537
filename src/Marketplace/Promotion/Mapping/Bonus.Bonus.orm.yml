Wizacha\Marketplace\Promotion\Bonus\Bonus:
    type: entity
    inheritanceType: SINGLE_TABLE
    discriminatorColumn:
        name: bonus_type
        type: string
    discriminatorMap:
        fixed: FixedBonus
        free: FreeBonus
        percentage: PercentageBonus
    id:
        id:
            type: integer
            generator:
                strategy: AUTO
    manyToOne:
        promotion:
            targetEntity: Wizacha\Marketplace\Promotion\Promotion
            inversedBy: bonuses
