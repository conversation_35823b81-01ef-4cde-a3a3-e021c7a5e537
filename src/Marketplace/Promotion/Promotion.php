<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Promotion;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Wizacha\Marketplace\Promotion\Bonus\Bonus;
use Wizacha\Marketplace\Promotion\BonusTarget\BonusTarget;

class Promotion implements PromotionInterface
{
    /**
     * @var string|null
     */
    private $id;

    /**
     * @var int|null
     */
    private $companyId;

    /**
     * @var bool
     */
    private $active = true;

    /**
     * @var string
     */
    private $name;

    /**
     * @var PromotionType
     */
    private $type;

    /**
     * @var string
     */
    private $rules;

    /**
     * @var \DateTime
     */
    private $startTime;

    /**
     * @var \DateTime
     */
    private $endTime;

    /**
     * @var Collection|Bonus[]
     */
    private $bonuses;

    /**
     * @var BonusTarget
     */
    private $target;

    /**
     * @var bool
     */
    private $isValid;

    /**
     * @param Bonus[] $bonuses
     * @param BonusTarget $target
     */
    public function __construct(?string $id, ?int $companyId, string $rules, array $bonuses, BonusTarget $target)
    {
        $this->id = $id;
        $this->companyId = $companyId;
        $this->name = '';
        $this->type = PromotionType::CATALOG();
        $this->rules = (string) $rules;
        $this->startTime = new \DateTime("2000-01-01 00:00:00");
        $this->endTime = new \DateTime("3000-01-01 00:00:00");
        $this->isValid = true;
        $this->bonuses = new ArrayCollection();
        $this->setBonuses($bonuses);

        $this->target = $target;
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    /**
     * @return $this
     */
    public function setId(string $id): self
    {
        $this->id = (string) $id;

        return $this;
    }

    public function getCompanyId(): ?int
    {
        return $this->companyId;
    }

    public function setCompanyId(?int $companyId): void
    {
        $this->companyId = $companyId;
    }

    public function isActive(): bool
    {
        return $this->active;
    }

    public function setActive(bool $active): void
    {
        $this->active = (bool) $active;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = (string) $name;
    }

    public function getType(): PromotionType
    {
        return $this->type;
    }

    public function getRules(): string
    {
        return $this->rules;
    }

    public function setRules(string $rules): void
    {
        $this->rules = (string) $rules;
    }

    /**
     * @return Collection|Bonus[]
     */
    public function getBonuses(): Collection
    {
        return $this->bonuses;
    }

    /**
     * @param Bonus[] $bonuses
     */
    public function setBonuses(array $bonuses)
    {
        $this->bonuses->clear();
        foreach ($bonuses as $bonus) {
            $bonus->setPromotion($this);
            $this->bonuses->add($bonus);
        }
    }

    public function getStartTime(): \DateTime
    {
        return $this->startTime;
    }

    public function getEndTime(): \DateTime
    {
        return $this->endTime;
    }

    public function setActivationPeriod(\DateTime $startTime, \DateTime $endTime): void
    {
        if ($startTime > $endTime) {
            throw new \InvalidArgumentException(__('date_validation_error'));
        }

        $this->startTime = $startTime;
        $this->endTime = $endTime;
    }

    public function getTarget(): BonusTarget
    {
        return $this->target;
    }

    public function setTarget(BonusTarget $target): void
    {
        $this->target = $target;
    }

    public function isValid(): bool
    {
        return $this->isValid;
    }

    public function setIsValid(bool $valid): self
    {
        $this->isValid = $valid;

        return $this;
    }

    public function hasTypeMarketplace(): bool
    {
        return $this->type->equals(PromotionType::MARKETPLACE());
    }

    public function hasTypeCatalog(): bool
    {
        return $this->type->equals(PromotionType::CATALOG());
    }

    public function hasTypeBasket(): bool
    {
        return $this->type->equals(PromotionType::BASKET());
    }

    protected function setType(PromotionType $type): void
    {
        $this->type = $type;
    }
}
