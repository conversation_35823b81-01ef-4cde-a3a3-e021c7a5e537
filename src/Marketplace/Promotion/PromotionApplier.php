<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Promotion;

use Wizacha\Marketplace\Price\PriceComposition;

/**
 * Interface PromotionApplier
 * Applies promotion's bonuses by resolving to which part of PriceComposition the bonus applies (base price, shipping, ...)
 * @package Wizacha\Marketplace\Promotion
 */
interface PromotionApplier
{
    public function apply(Promotion $promotion, PriceComposition $price): PriceComposition;
}
