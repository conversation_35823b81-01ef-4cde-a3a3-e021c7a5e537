<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Promotion;

use Acelaya\Doctrine\Type\AbstractPhpEnumType;

class PromotionTypeEnumType extends AbstractPhpEnumType
{
    protected $enumType = PromotionType::class;

    /**
     * @return string
     */
    protected function getSpecificName()
    {
        return 'promotion_type';
    }
}
