<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Promotion;

use Wizacha\Marketplace\Price\PriceComposition;
use Wizacha\Marketplace\Price\PriceFields;
use Wizacha\Marketplace\Promotion\BonusTarget\BonusTargetProduct;
use Wizacha\Money\Money;

class ProductPromotionApplier implements PromotionApplier
{
    public function apply(Promotion $promotion, PriceComposition $price): PriceComposition
    {
        $bonusTarget = $promotion->getTarget();
        if ($bonusTarget instanceof BonusTargetProduct) {
            if (!$price->has(PriceFields::BASE_PRICE())) {
                return $price;
            }
            foreach ($promotion->getBonuses() as $bonus) {
                $basePrice = $price->get(PriceFields::BASE_PRICE());
                $reducedBasePrice = Money::fromVariable($bonus->applyTo($basePrice)->getConvertedAmount());

                $reductionFactor = $basePrice->isZero() ? 0 : $reducedBasePrice->getAmount() / $basePrice->getAmount();

                $price->set(PriceFields::BASE_PRICE(), $reducedBasePrice);

                if ($price->has(PriceFields::PRODUCT_PRICE_WITH_TAX())) {
                    $price->set(
                        PriceFields::PRODUCT_PRICE_WITH_TAX(),
                        $price->get(PriceFields::PRODUCT_PRICE_WITH_TAX())->multiply($reductionFactor)
                    );
                }
                if ($price->has(PriceFields::PRODUCT_TAX())) {
                    $price->set(
                        PriceFields::PRODUCT_TAX(),
                        $price->get(PriceFields::PRODUCT_TAX())->multiply($reductionFactor)
                    );
                }
            }
        }

        return $price;
    }
}
