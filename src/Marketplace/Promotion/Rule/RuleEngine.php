<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Promotion\Rule;

use Doctrine\DBAL\Connection;
use Hoa\Compiler\Exception\UnexpectedToken;
use RulerZ\Compiler\Compiler;
use RulerZ\Compiler\Context;
use RulerZ\RulerZ;
use RulerZ\Target\Native\Native;
use Wizacha\Marketplace\Promotion\Rule\Exception\InvalidRules;

/**
 * Evaluates promotion rules.
 */
class RuleEngine
{
    /**
     * @var RulerZ
     */
    private $rulerz;

    /**
     * @var Compiler
     */
    private $rulerzCompiler;

    /**
     * @var Native
     */
    private $rulerzCompilerTarget;

    /**
     * @var Connection
     */
    private $db;

    public function __construct(RulerZ $rulerz, Compiler $rulerzCompiler, Native $rulerzCompilerTarget, Connection $db)
    {
        $this->rulerz = $rulerz;
        $this->rulerzCompiler = $rulerzCompiler;
        $this->rulerzCompilerTarget = $rulerzCompilerTarget;
        $this->db = $db;
    }

    /**
     * @param string $rules
     * @param integer|null $companyId
     * @return string[] Product IDs
     * @throws InvalidRules
     */
    public function getProductsMatchingRules($rules, $companyId = null)
    {
        if ($companyId !== null) {
            $companyRule = 'company = ' . \intval($companyId);

            if (empty($rules)) {
                $rules = $companyRule;
            } else {
                $rules = '(' . $rules . ') and ' . $companyRule;
            }
        } elseif (empty($rules)) {
            // TODO remove it when rulerz accepts empty rules
            $rules = 'product > 0';
        }

        // Workaround for rules retro-compatibility
        $rules = str_replace('intersects', 'in', $rules);

        // i.w_price is the inventory price. It is set only if the product has declination(s). Else we use the pp.price.
        // Both are stored as DECIMAL, while rules have prices as integer amounts of cents. That's why we apply `*100` to the stored amount for comparison.
        $subQb = $this->db->createQueryBuilder()->select('p.product_id AS product', 'p.company_id AS company', 'c.category_id AS categories', '(IFNULL(i.w_price, pp.price)*100) AS price')
            ->from('cscart_products', 'p')
            ->leftJoin('p', 'cscart_products_categories', 'c', 'p.product_id = c.product_id')
            ->leftJoin('p', 'cscart_product_prices', 'pp', 'p.product_id = pp.product_id')
            ->leftJoin('p', 'cscart_product_options_inventory', 'i', 'p.product_id = i.product_id');

        $qb = $this->db->createQueryBuilder()->select('DISTINCT(results.product)')->from("({$subQb->getSQL()})", 'results');

        try {
            $result = $this->rulerz->filter($qb, $rules);
        } catch (UnexpectedToken $e) {
            throw new InvalidRules('Invalid promotion rules: ' . $rules, 0, $e);
        }

        $productIds = [];
        // Use foreach and not array_map (or similar) to iterate through the generator lazily
        foreach ($result as $declination) {
            $productIds[] = $declination['product'];
        }

        return $productIds;
    }

    /**
     * Check if a product matches the given promotion rules.
     *
     * @param string $rules
     * @param integer|null $companyId
     * @return bool
     * @throws InvalidRules
     */
    public function productSatisfiesRules(ProductSubject $subject, $rules, $companyId)
    {
        if ($companyId !== null && $subject->company !== $companyId) {
            return false;
        }

        if (empty($rules)) {
            return true;
        }

        try {
            return $this->rulerz->satisfies($subject, $rules, [], []);
        } catch (UnexpectedToken $e) {
            throw new InvalidRules('Invalid promotion rules: ' . $rules, 0, $e);
        }
    }

    /**
     * Check if a basket matches the given promotion rules.
     *
     * @param string $rules
     * @param integer|null $companyId
     * @param string|null $coupon
     * @return bool
     * @throws InvalidRules
     */
    public function basketSatisfiesRules(BasketSubject $subject, $rules, $companyId, $coupon)
    {
        if ($companyId !== null && $subject->company !== $companyId) {
            return false;
        }

        // Add the coupon rule dynamically
        if ($coupon !== null) {
            $couponRule = sprintf('coupons intersects ["%s"]', $coupon);
            $rules = $rules ? sprintf('(%s) and %s', $rules, $couponRule) : $couponRule;
        }

        if (empty($rules)) {
            return true;
        }

        try {
            return $this->rulerz->satisfies($subject, $rules, [], []);
        } catch (UnexpectedToken $e) {
            throw new InvalidRules('Invalid promotion rules: ' . $rules, 0, $e);
        }
    }

    /**
     * @throws InvalidRules
     */
    public function validateRules(string $rules)
    {
        if ($rules === '') {
            return;
        }
        try {
            $this->rulerzCompiler->compile($rules, $this->rulerzCompilerTarget, new Context());
        } catch (UnexpectedToken $e) {
            throw new InvalidRules('Invalid promotion rules: ' . $rules, 0, $e);
        }
    }
}
