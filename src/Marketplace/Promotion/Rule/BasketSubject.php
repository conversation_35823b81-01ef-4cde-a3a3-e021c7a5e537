<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Promotion\Rule;

use Wizacha\Money\Money;

/**
 * Promotion rules for basket promotions are applied to this object.
 */
class BasketSubject
{
    /**
     * Company ID.
     *
     * @var int
     */
    public $company;

    /**
     * Total price of the basket.
     *
     * Price in the smallest unit (integers only).
     *
     * @var int
     */
    public $totalAmount;

    /**
     * Total quantity of the basket.
     *
     * @var int
     */
    public $totalQuantity;

    /**
     * Number of times the promotion has been used
     *
     * @var int
     */
    public $usageCount;

    /**
     * Number of times the promotion has been used by the current user
     *
     * @var int
     */
    public $userUsageCount;

    /**
     * Coupons that the user added to the basket.
     *
     * @var string[]
     */
    public $coupons = [];

    /**
     * Products Ids that the user added to the basket.
     *
     * @var int[]
     */
    public $products = [];

    /**
     * Users Group Ids that can use promotion.
     *
     * @var string[]
     */
    public $groups = [];

    /**
     * @param int $company
     * @param Money $totalAmount
     * @param array $coupons
     * @param int $usageCount
     * @param int $userUsageCount
     * @param int $totalQuantity
     * @param array $productsIds
     * @param string[] $usersGroupIds
     */
    public function __construct(
        $company,
        Money $totalAmount,
        array $coupons,
        array $productsIds,
        $usageCount = 0,
        $userUsageCount = 0,
        $totalQuantity = 0,
        array $usersGroupIds = []
    ) {
        $this->company = $company;
        $this->totalAmount = $totalAmount->getAmount();
        $this->totalQuantity = $totalQuantity;
        $this->coupons = $coupons;
        $this->usageCount = $usageCount;
        $this->userUsageCount = $userUsageCount;
        $this->products = $productsIds;
        $this->groups = $usersGroupIds;
    }
}
