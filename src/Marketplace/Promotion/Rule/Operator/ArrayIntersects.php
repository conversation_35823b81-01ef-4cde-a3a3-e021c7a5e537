<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Promotion\Rule\Operator;

/**
 * RulerZ inline operator.
 *
 * Implementation of the "INTERSECTS" operator for filtering an in-memory source.
 *
 * @see \RulerZ\Compiler\Target\GenericVisitor::setOperator()
 */
class ArrayIntersects
{
    public function __invoke($a, $b): bool
    {
        return !empty(array_intersect($a, $b));
    }
}
