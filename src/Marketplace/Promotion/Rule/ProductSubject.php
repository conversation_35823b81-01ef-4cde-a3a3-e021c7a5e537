<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Promotion\Rule;

use Wizacha\Category;
use Wizacha\Marketplace\Entities\Declination;
use Wizacha\Marketplace\Price\PriceFields;
use Wizacha\Product;

/**
 * The promotion rules are applied to this object. It represents a simplified version of a declination.
 */
class ProductSubject
{
    /**
     * Product ID.
     *
     * @var int
     */
    public $product;

    /**
     * Company ID.
     *
     * @var int
     */
    public $company;

    /**
     * List of category IDs.
     *
     * @var int[]
     */
    public $categories;

    /**
     * Price in the smallest unit (integers only).
     *
     * @var int
     */
    public $price;

    public static function fromDeclination(Declination $declination): ProductSubject
    {
        $product = new Product($declination->getProductId());

        $companyId = $declination->getCompany()->getId();
        $price = $declination->getPriceComposition()->get(PriceFields::BASE_PRICE())->getAmount();

        return new self($declination->getProductId(), $companyId, self::getCategories($product), $price);
    }

    /**
     * @return ProductSubject[] Array indexed by declination ID.
     */
    public static function fromProductDeclinations(Product $product): array
    {
        $categories = self::getCategories($product);

        $subjects = [];
        foreach ($product->getDeclinations() as $declination) {
            $companyId = $declination->getCompany()->getId();
            $price = $declination->getPriceComposition()->get(PriceFields::BASE_PRICE())->getAmount();

            $subjects[$declination->getId()] = new self($product->getId(), $companyId, $categories, $price);
        }

        return $subjects;
    }

    /**
     * @param int $product
     * @param int $company
     * @param int[] $categories
     * @param int $price
     */
    public function __construct($product, $company, $categories, $price)
    {
        $this->product = $product;
        $this->company = $company;
        $this->categories = $categories;
        $this->price = $price;
    }

    /**
     * @return array
     */
    public function toArray()
    {
        return (array) $this;
    }

    /**
     * @return int[]
     */
    private static function getCategories(Product $product)
    {
        return array_map(function (Category $category) {
            return $category->getId();
        }, $product->getCategory()->getCategoriesPath());
    }
}
