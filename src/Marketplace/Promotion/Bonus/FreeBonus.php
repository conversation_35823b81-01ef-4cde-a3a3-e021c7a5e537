<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Promotion\Bonus;

use Wizacha\Money\Money;

/**
 * Makes a price free (i.e. equal to 0).
 */
class FreeBonus extends Bonus
{
    public const ID = 'free';

    /**
     * @inherit
     */
    public function applyTo(Money $price, int $quantity = 1): Money
    {
        return new Money(0);
    }

    public function getAmount(Money $price): Money
    {
        return $price;
    }

    public function getType(): string
    {
        return static::ID;
    }
}
