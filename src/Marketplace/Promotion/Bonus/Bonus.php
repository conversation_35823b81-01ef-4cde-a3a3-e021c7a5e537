<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Promotion\Bonus;

use Wizacha\Marketplace\Promotion\Promotion;
use Wizacha\Money\Money;

abstract class Bonus implements BonusInterface
{
    /**
     * @var int
     */
    protected $id;

    /**
     * @var Promotion
     */
    protected $promotion;

    abstract public function applyTo(Money $price, int $quantity = 1): Money;

    abstract public function getAmount(Money $price): Money;

    abstract public function getType(): string;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    public function setPromotion(Promotion $promotion)
    {
        $this->promotion = $promotion;
    }

    /**
     * @return Promotion
     */
    public function getPromotion()
    {
        return $this->promotion;
    }
}
