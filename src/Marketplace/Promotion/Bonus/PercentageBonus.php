<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Promotion\Bonus;

use Wizacha\Money\Money;

/**
 * Percentage reduction.
 *
 * Example: -10% on the base price.
 */
class PercentageBonus extends Bonus
{
    public const ID = 'percentage';

    /**
     * Percentage of reduction (between 0 and 100%).
     * @var float
     */
    private $reduction;

    private ?Money $maxAmount;

    /**
     * @param float $reduction
     * @param null|Money $maxAmount
     */
    public function __construct(float $reduction, Money $maxAmount = null)
    {
        $this->setReduction($reduction);
        $this->maxAmount = $maxAmount;
    }

    /**
     * @return float
     */
    public function getReduction(): float
    {
        return $this->reduction;
    }

    /**
     * @inherit
     */
    public function applyTo(Money $price, int $quantity = 1): Money
    {
        $reduction = $price->multiply($this->reduction / 100.);
        if ($this->getMaxAmount() !== null
            && $reduction->getAmount() > $this->getMaxAmount()->getAmount()
        ) {
            return $price->subtract($this->getMaxAmount());
        }

        return $price->subtract($reduction);
    }

    public function getAmount(Money $price): Money
    {
        return $price->divide((100 - $this->reduction) / 100)->subtract($price);
    }

    public function getMaxAmount(): ?Money
    {
        return $this->maxAmount;
    }

    public function getType(): string
    {
        return static::ID;
    }

    private function setReduction(float $reduction): float
    {
        if ($reduction < 0 || $reduction > 100) {
            throw new \InvalidArgumentException(sprintf(
                '%d%% reduction is invalid: it must be between 0%% and 100%%',
                $reduction
            ));
        }

        return $this->reduction = $reduction;
    }
}
