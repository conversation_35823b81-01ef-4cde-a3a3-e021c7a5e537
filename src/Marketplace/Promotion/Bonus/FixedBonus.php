<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Promotion\Bonus;

use Wizacha\Money\Money;

/**
 * Fixed price reduction.
 *
 * Example: -10 euros.
 */
class FixedBonus extends Bonus
{
    public const ID = 'fixed';

    /**
     * @var Money
     */
    private $reduction;

    private ?Money $maxAmount;

    /**
     * @param Money $reduction
     * @param null|Money $maxAmount
     */
    public function __construct(Money $reduction, Money $maxAmount = null)
    {
        $this->reduction = $reduction;
        $this->maxAmount = $maxAmount;
    }

    /**
     * @inherit
     */
    public function applyTo(Money $price, int $quantity = 1): Money
    {
        $reductionToApply = $this->reduction->multiply($quantity);
        if ($price->lessThanOrEqual($this->reduction)) {
            return new Money(0);
        }

        if ($this->getMaxAmount() !== null && $reductionToApply->getAmount() > $this->getMaxAmount()->getAmount()) {
            return $price->subtract($this->getMaxAmount());
        }

        return $price->subtract($reductionToApply);
    }

    public function getAmount(Money $price): Money
    {
        return $this->getReduction();
    }

    public function getMaxAmount(): ?Money
    {
        return $this->maxAmount;
    }

    public function getReduction(): Money
    {
        return $this->reduction;
    }

    public function getType(): string
    {
        return static::ID;
    }
}
