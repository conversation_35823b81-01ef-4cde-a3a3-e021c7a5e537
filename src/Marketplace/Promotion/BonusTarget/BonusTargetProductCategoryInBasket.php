<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Promotion\BonusTarget;

class BonusTargetProductCategoryInBasket extends BonusTarget
{
    /** @var int[] */
    private array $productCategoryIds;

    public function __construct(string $serializedProductCategoryIds = null)
    {
        $this->productCategoryIds = [];
        if (\is_string($serializedProductCategoryIds) === true && \mb_strlen($serializedProductCategoryIds) > 0) {
            $serializedProductCategoryIds = preg_replace('/\s+/', '', $serializedProductCategoryIds);
            foreach (\preg_split('/,/', $serializedProductCategoryIds, -1, PREG_SPLIT_NO_EMPTY) as $categoryId) {
                if (\ctype_digit($categoryId)) {
                    $this->productCategoryIds[] = (int) $categoryId;
                } else {
                    throw new \Exception(sprintf('"%s" is not a valid category id', $categoryId));
                }
            }
        }
    }

    public function __toString(): string
    {
        return $this->getName() . ';' . implode(',', $this->productCategoryIds);
    }

    public function getName(): string
    {
        return "product_category_in_basket";
    }

    /** @return int[] */
    public function getProductCategoryIds(): array
    {
        return $this->productCategoryIds;
    }

    /** @return int[] */
    public function getProductCategoryIdsWithChildren(): array
    {
        $ids = [];
        //we cant inject service in the constructor
        $categoryService = container()->get('marketplace.catalog.category_service');
        foreach ($this->productCategoryIds as $productCategoryId) {
            if (\in_array($productCategoryId, $ids) === false) {
                $ids = \array_merge($ids, $categoryService->getCategoryAllChildrenIds($productCategoryId));
            }
        }

        return $ids;
    }

    public function setProductCategoryIds(int ...$productCategoryIds): self
    {
        $this->productCategoryIds = $productCategoryIds;

        return $this;
    }
}
