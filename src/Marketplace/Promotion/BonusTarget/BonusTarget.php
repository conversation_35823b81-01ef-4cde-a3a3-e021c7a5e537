<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Promotion\BonusTarget;

abstract class BonusTarget
{
    private static $typeMap = [
        'basket' => BonusTargetBasket::class,
        'product' => BonusTargetProduct::class,
        'product_in_basket' => BonusTargetProductInBasket::class,
        'product_category_in_basket' => BonusTargetProductCategoryInBasket::class,
        'shipping' => BonusTargetShipping::class,
    ];

    /**
     * Builds a BonusTarget from a string generated by __toString method
     * Used by BonusTargetDoctrineType to deserialize from database
     * @param string $input
     * @return BonusTarget
     * @throws \Exception
     */
    public static function fromString($input)
    {
        $parts = explode(';', $input, 2);
        $type = $parts[0];

        if (!isset(self::$typeMap[$type])) {
            throw new \Exception(sprintf('Target "%s" is not valid', $type));
        }

        $className = self::$typeMap[$type];

        return new $className($parts[1]);
    }

    /**
     * Return the serialized target
     * @return string
     */
    abstract public function __toString();

    abstract public function getName(): string;
}
