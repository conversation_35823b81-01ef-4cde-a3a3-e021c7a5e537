<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Promotion\BonusTarget;

class BonusTargetProductInBasket extends BonusTarget
{
    /**
     * @var array
     */
    private $productIds;

    /**
     * BonusTargetProductInBasket constructor.
     * @param string|null $serializedProductIds
     * @throws \Exception
     */
    public function __construct($serializedProductIds = null)
    {
        $this->productIds = [];
        if (!empty($serializedProductIds)) {
            $serializedProductIds = preg_replace('/\s+/', '', $serializedProductIds);
            foreach (preg_split('/,/', $serializedProductIds, -1, PREG_SPLIT_NO_EMPTY) as $productId) {
                if (ctype_digit($productId)) {
                    $this->productIds[] = (int) $productId;
                } else {
                    throw new \Exception(sprintf('"%s" is not a valid product id', $productId));
                }
            }
        }
    }

    /**
     * @inheritdoc
     */
    public function __toString(): string
    {
        return $this->getName() . ';' . implode(',', $this->productIds);
    }

    public function getName(): string
    {
        return "product_in_basket";
    }

    /**
     * @return int[]
     */
    public function getProductIds(): array
    {
        return $this->productIds;
    }

    public function setProductIds(int ...$productIds)
    {
        $this->productIds = $productIds;
    }
}
