<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Promotion;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\QueryBuilder;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Wizacha\Marketplace\Promotion\Exception\PromotionNotFound;

class MarketplacePromotionRepository
{
    /** @var EntityManagerInterface */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    /** @return array [MarketplacePromotion[], int totalCount] */
    public function findPaginated(int $start, int $limit, array $filters = []): array
    {
        $builder = $this->entityManager->createQueryBuilder();
        $builder
            ->select('mp')
            ->from(MarketplacePromotion::class, 'mp')
            ->setFirstResult($start)
            ->setMaxResults($limit)
            ->orderBy('mp.name', 'ASC')
        ;

        $this->applyFilters($builder, $filters);

        $paginator = new Paginator($builder);

        return [$paginator->getQuery()->getResult(), $paginator->count()];
    }

    public function findByCoupon(string $coupon): array
    {
        $builder = $this->entityManager->createQueryBuilder();
        $builder
            ->select('mp')
            ->from(MarketplacePromotion::class, 'mp')
            ->where("mp.coupon = :coupon")
            ->setParameter(":coupon", $coupon)
        ;

        return $builder->getQuery()->getResult();
    }

    public function get(string $id): MarketplacePromotion
    {
        return $this
            ->entityManager
            ->createQueryBuilder()
            ->select('mp')
            ->from(MarketplacePromotion::class, 'mp')
            ->where("mp.id = :id")
            ->setParameter(":id", $id)
            ->getQuery()
            ->getSingleResult()
            ;
    }

    protected function applyFilters(QueryBuilder $queryBuilder, array $filters): self
    {
        if (\array_key_exists('active', $filters)) {
            $this->filterByActive($queryBuilder, $filters['active']);
        }

        if (\array_key_exists('coupon', $filters)) {
            $this->filterByCoupon($queryBuilder, $filters['coupon']);
        }

        if (\array_key_exists('isValid', $filters)) {
            $this->filterByIsValid($queryBuilder, $filters['isValid']);
        }

        return $this;
    }

    protected function filterByActive(QueryBuilder $queryBuilder, int $value): self
    {
        $queryBuilder
            ->andWhere("mp.active = :active")
            ->setParameter(":active", $value)
        ;

        return $this;
    }

    protected function filterByCoupon(QueryBuilder $queryBuilder, string $value): self
    {
        $queryBuilder
            ->andWhere("mp.coupon like :coupon")
            ->setParameter(":coupon", '%' . $value . '%')
        ;

        return $this;
    }

    protected function filterByIsValid(QueryBuilder $queryBuilder, int $value): self
    {
        $queryBuilder
            ->andWhere("mp.isValid = :isValid")
            ->setParameter(":isValid", $value)
        ;

        return $this;
    }
}
