<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Promotion\PromotionUsage;

class PromotionUsage
{
    /**
     * @var int
     */
    private $id;

    /**
     * @var int
     */
    private $userId;

    /**
     * @var string
     */
    private $promotionId;

    /**
     * @var int
     */
    private $orderId;

    /**
     * PromotionUsage constructor.
     * @param int $userId
     * @param string $promotionId
     * @param int $orderId
     */
    public function __construct($userId, $promotionId, $orderId)
    {
        $this->userId = $userId;
        $this->promotionId = $promotionId;
        $this->orderId = $orderId;
    }

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @return int
     */
    public function getUserId()
    {
        return $this->userId;
    }

    /**
     * @param int $userId
     */
    public function setUserId($userId)
    {
        $this->userId = $userId;
    }

    /**
     * @return string
     */
    public function getPromotionId()
    {
        return $this->promotionId;
    }

    /**
     * @param string $promotionId
     */
    public function setPromotionId($promotionId)
    {
        $this->promotionId = $promotionId;
    }

    /**
     * @return int
     */
    public function getOrderId()
    {
        return $this->orderId;
    }

    /**
     * @param int $orderId
     */
    public function setOrderId($orderId)
    {
        $this->orderId = $orderId;
    }
}
