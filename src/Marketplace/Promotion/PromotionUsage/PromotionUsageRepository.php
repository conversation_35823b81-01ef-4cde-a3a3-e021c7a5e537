<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Promotion\PromotionUsage;

use Doctrine\ORM\EntityManagerInterface;
use Wizacha\Marketplace\Promotion\PromotionInterface;

class PromotionUsageRepository
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function countByPromotion(PromotionInterface $promotion, int $userId = null): int
    {
        $builder = $this->entityManager->createQueryBuilder();
        $builder
            ->select('COUNT(pu)')
            ->from(PromotionUsage::class, 'pu')
            ->where('pu.promotionId = :promotionId')
            ->setParameter('promotionId', $promotion->getId());

        if ($userId !== null) {
            $builder
                ->andWhere('pu.userId = :userId')
                ->setParameter('userId', $userId);
        }

        return (int) $builder->getQuery()->getSingleScalarResult();
    }

    /**
     * Record the fact that a promotion was used.
     */
    public function recordUsage(int $userId, string $promotionId, int $orderId)
    {
        $tmp = new PromotionUsage($userId, $promotionId, $orderId);
        $this->entityManager->persist($tmp);
        $this->entityManager->flush();
    }

    public function deleteUsageByOrderId(int $orderId): self
    {
        $promotionsUsage = $this->entityManager->createQueryBuilder()
            ->select('pu')
            ->from(PromotionUsage::class, 'pu')
            ->where('pu.orderId = :orderId')
            ->setParameter('orderId', $orderId)
            ->getQuery()
            ->getResult();

        if (\count($promotionsUsage)) {
            foreach ($promotionsUsage as $usage) {
                $this->entityManager->remove($usage);
            }

            $this->entityManager->flush();
        }

        return $this;
    }
}
