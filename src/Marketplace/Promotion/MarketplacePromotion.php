<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Promotion;

use Wizacha\Marketplace\Promotion\Bonus\Bonus;
use Wizacha\Marketplace\Promotion\BonusTarget\BonusTarget;

/**
 * A marketplace promotion that applies to a basket.
 */
class MarketplacePromotion extends Promotion implements CouponBasketInterface
{
    /* @var string */
    private $coupon;

    /**
     * @param string $coupon
     * @param string $id
     * @param string $rules
     * @param Bonus[] $bonuses
     * @param BonusTarget $target
     * @param int|null $companyId
     */
    public function __construct(string $coupon, ?string $id, string $rules, array $bonuses, BonusTarget $target, int $companyId = null)
    {
        parent::__construct($id, $companyId, $rules, $bonuses, $target);
        $this->setCoupon($coupon);
        $this->setType(PromotionType::MARKETPLACE());
    }

    public function getCoupon(): string
    {
        return $this->coupon;
    }

    public function setCoupon(string $coupon): self
    {
        $this->coupon = $coupon;

        return $this;
    }
}
