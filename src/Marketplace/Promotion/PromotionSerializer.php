<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Promotion;

use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Wizacha\Marketplace\Promotion\Bonus\Bonus;
use Wizacha\Marketplace\Promotion\Bonus\FixedBonus;
use Wizacha\Marketplace\Promotion\Bonus\FreeBonus;
use Wizacha\Marketplace\Promotion\Bonus\PercentageBonus;
use Wizacha\Marketplace\Promotion\BonusTarget\BonusTarget;
use Wizacha\Money\Money;

/**
 * @deprecated for the same reasons as \Wizacha\AppBundle\Controller\Api\LegacyPromotionController
 * @internal
 */
class PromotionSerializer
{
    /**
     * @param Promotion $promotion
     * @return array
     */
    public function serialize(Promotion $promotion)
    {
        $serializedPromotion = [
            'id' => $promotion->getId(),
            'companyId' => $promotion->getCompanyId(),
            'active' => $promotion->isActive(),
            'name' => $promotion->getName(),
            'type' => $promotion->getType()->__toString(),
            'rules' => $promotion->getRules(),
            'startTime' => $promotion->getStartTime()->format('c'), //  ISO 8601: 2004-02-12T15:19:21+00:00
            'endTime' => $promotion->getEndTime()->format('c'),
            'target' => $promotion->getTarget()->__toString(),
            'bonuses' => $promotion->getBonuses()->map(function (Bonus $bonus) {
                switch (true) {
                    case $bonus instanceof FreeBonus:
                        return [
                            'type' => 'free',
                        ];
                    case $bonus instanceof FixedBonus:
                        return [
                            'type' => 'fixed',
                            'reduction' => $bonus->getReduction()->getAmount(),
                            'maxAmount' => ($bonus->getMaxAmount() instanceof Money === true) ? $bonus->getMaxAmount()->getAmount() : null
                        ];
                    case $bonus instanceof PercentageBonus:
                        return [
                            'type' => 'percentage',
                            'reduction' => $bonus->getReduction(),
                            'maxAmount' => ($bonus->getMaxAmount() instanceof Money === true) ? $bonus->getMaxAmount()->getAmount() : null
                        ];
                }
                throw new \Exception('Unknown bonus type');
            })->toArray(),
        ];

        if ($promotion instanceof BasketPromotion) {
            $serializedPromotion['coupon'] = $promotion->getCoupon();
        }

        return $serializedPromotion;
    }

    /**
     * @param array $data
     * @return Promotion
     * @throws BadRequestHttpException
     */
    public function deserialize($data, Promotion $promotion = null)
    {
        $this->assertDataForDeserializeisValid($data);

        $id = empty($data['id']) ? null : $data['id'];
        $rules = empty($data['rules']) ? '' : $data['rules'];
        $target = BonusTarget::fromString($data['target']);
        $type = new PromotionType($data['type']);

        if ($promotion !== null) {
            $promotion->setCompanyId($data['companyId']);
            $promotion->setRules($rules);
            $promotion->setTarget($target);
        } else {
            $arguments = [$id, $data['companyId'], $rules, [], $target];
            if ($type == PromotionType::BASKET()) {
                $promotion = new BasketPromotion(...$arguments);
            } else {
                $promotion = new Promotion(...$arguments);
            }
        }

        if ($promotion instanceof BasketPromotion) {
            $promotion->setCoupon($data['coupon']);
        }

        $promotion->setActive((bool) $data['active']);
        $promotion->setName($data['name']);

        $bonuses = [];
        foreach ($data['bonuses'] as $i => $bonusData) {
            $bonuses[] = $this->deserializeBonus($bonusData, $i + 1);
        }
        $promotion->setBonuses($bonuses);

        try {
            $promotion->setActivationPeriod(new \DateTime($data['startTime']), new \DateTime($data['endTime']));
        } catch (\InvalidArgumentException $e) {
            throw new BadRequestHttpException('endTime must be greater than startTime');
        } catch (\Exception $e) {
            throw new BadRequestHttpException('startTime or endTime is not an ISO 8601 date');
        }

        return $promotion;
    }

    /**
     * @param array $data
     * @throws BadRequestHttpException
     */
    private function assertDataForDeserializeisValid($data)
    {
        if (!isset($data['active'])) {
            throw new BadRequestHttpException('Missing field "active"');
        }
        if (!isset($data['name'])) {
            throw new BadRequestHttpException('Missing field "name"');
        }
        if (!isset($data['bonuses']) || !\is_array($data['bonuses'])) {
            throw new BadRequestHttpException('Field "bonuses" should be an array');
        }
        if (!isset($data['startTime'], $data['endTime'])) {
            throw new BadRequestHttpException('Missing field "startTime" or "endTime"');
        }
        if (!\array_key_exists('companyId', $data)) {
            throw new BadRequestHttpException('Field "companyId" missing');
        }
        if (!isset($data['type'])) {
            //Not dependant of request
            throw new BadRequestHttpException('Missing field "type"');
        }
        if (!PromotionType::isValid($data['type'])) {
            throw new BadRequestHttpException('Unknown promotion type');
        }
        if (!isset($data['target'])) {
            throw new BadRequestHttpException('Missing field "target"');
        }
    }

    private function deserializeBonus($bonusData, $index)
    {
        if (!isset($bonusData['type'])) {
            throw new BadRequestHttpException('Missing field "type" in bonus ' . $index);
        }
        $bonusType = (string) $bonusData['type'];

        switch ($bonusType) {
            case FreeBonus::ID:
                return new FreeBonus();
            case FixedBonus::ID:
                if (!isset($bonusData['reduction'])) {
                    throw new BadRequestHttpException('Missing field "reduction" in bonus ' . $index);
                }
                $reduction = (int) $bonusData['reduction'];

                return new FixedBonus(new Money($reduction), ($bonusData['maxAmount'] != null) ? new Money($bonusData['maxAmount']) : null);
            case PercentageBonus::ID:
                if (!isset($bonusData['reduction'])) {
                    throw new BadRequestHttpException('Missing field "reduction" in bonus ' . $index);
                }
                $reduction = (float) $bonusData['reduction'];
                try {
                    return new PercentageBonus($reduction, ($bonusData['maxAmount'] != null) ? new Money($bonusData['maxAmount']) : null);
                } catch (\InvalidArgumentException $e) {
                    throw new BadRequestHttpException($e->getMessage());
                }
        }

        throw new BadRequestHttpException('Unknown bonus type ' . $bonusType);
    }
}
