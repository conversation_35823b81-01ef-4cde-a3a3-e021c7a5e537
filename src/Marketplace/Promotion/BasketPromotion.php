<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Promotion;

use Wizacha\Marketplace\Promotion\Bonus\Bonus;
use Wizacha\Marketplace\Promotion\BonusTarget\BonusTarget;

/**
 * A promotion that applies to a basket.
 */
class BasketPromotion extends Promotion implements CouponBasketInterface
{
    /**
     * @var string|null
     */
    private $coupon;

    /**
     * @param string $id
     * @param int $companyId
     * @param string $rules
     * @param Bonus[] $bonuses
     * @param BonusTarget $target
     */
    public function __construct($id, $companyId, $rules, array $bonuses, BonusTarget $target)
    {
        parent::__construct($id, $companyId, $rules, $bonuses, $target);

        $this->setType(PromotionType::BASKET());
    }

    /**
     * @return string|null
     */
    public function getCoupon(): ?string
    {
        return $this->coupon;
    }

    /**
     * @param string|null $coupon
     */
    public function setCoupon($coupon)
    {
        $this->coupon = empty($coupon) ? null : $coupon;
    }
}
