<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Catalog;

class Geolocation
{
    private $latitude;
    private $longitude;
    private $label;
    private $zipcode;

    public function __construct(float $latitude, float $longitude, string $label, string $zipcode)
    {
        $this->latitude = $latitude;
        $this->longitude = $longitude;
        $this->label = $label;
        $this->zipcode = $zipcode;
    }

    public function getLatitude(): float
    {
        return $this->latitude;
    }

    public function getLongitude(): float
    {
        return $this->longitude;
    }

    public function getLabel(): string
    {
        return $this->label;
    }

    public function getZipcode(): string
    {
        return $this->zipcode;
    }

    public function expose(): array
    {
        return [
            'latitude' => $this->getLatitude(),
            'longitude' => $this->getLongitude(),
            'label' => $this->getLabel(),
            'zipcode' => $this->getZipcode(),
        ];
    }
}
