<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Catalog;

use Doctrine\DBAL\Connection;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Image\Image;
use Wizacha\Marketplace\SeoData;

class AttributeService
{
    /**
     * @var Connection
     */
    private $db;

    public function __construct(Connection $db)
    {
        $this->db = $db;
    }

    /**
     * @param mixed[]|null $params
     *
     * @return Attribute[]
     */
    public function getAttributes(array $params = null): array
    {
        $query = <<<'SQL'
            SELECT f.feature_id, f.feature_type, f.position, f.parent_id, f.feature_code, d.description
            FROM cscart_product_features AS f
            INNER JOIN cscart_product_features_descriptions AS d
                ON f.feature_id = d.feature_id
                AND d.lang_code = :lang
            WHERE f.status = 'A'
            AND f.display_on_product = 'Y'
SQL;
        if (\is_array($params) === true) {
            if (\array_key_exists('id', $params) === true) {
                $idList = $this->arrayize($params['id']);
                $query = $query . db_quote(' AND f.feature_id IN (?a)', \array_values($idList));
            }
            if (\array_key_exists('code', $params) === true) {
                $codeList = $this->arrayize($params['code']);
                $query = $query . db_quote(' AND f.feature_code IN (?a)', \array_values($codeList));
            }
        }

        $rows = $this->db->fetchAll($query, [
            'lang' => (string) GlobalState::interfaceLocale(),
        ]);

        return array_map(function (array $row) {
            return Attribute::fromCscartData($row);
        }, $rows);
    }

    public function getAttributeByCode(string $code): Attribute
    {
        $query = <<<'SQL'
            SELECT f.feature_id, f.feature_type, f.position, f.parent_id, f.feature_code, d.description
            FROM cscart_product_features AS f
            INNER JOIN cscart_product_features_descriptions AS d
                ON f.feature_id = d.feature_id
                AND d.lang_code = :lang
            WHERE f.status = 'A'
            AND f.display_on_product = 'Y'
            AND f.feature_code = :code
SQL;
        $row = $this->db->fetchAssoc($query, [
            'code' => $code,
            'lang' => (string) GlobalState::interfaceLocale(),
        ]);

        if (empty($row)) {
            throw new NotFound('Attribute with code "' . $code . '"');
        }

        return Attribute::fromCscartData($row);
    }

    public function getAttribute(int $id): Attribute
    {
        $query = <<<'SQL'
            SELECT f.feature_id, f.feature_type, f.position, f.parent_id, f.feature_code, d.description
            FROM cscart_product_features AS f
            INNER JOIN cscart_product_features_descriptions AS d
                ON f.feature_id = d.feature_id
                AND d.lang_code = :lang
            WHERE f.status = 'A'
            AND f.display_on_product = 'Y'
            AND f.feature_id = :id
SQL;
        $row = $this->db->fetchAssoc($query, [
            'id' => $id,
            'lang' => (string) GlobalState::interfaceLocale(),
        ]);

        if (empty($row)) {
            throw NotFound::fromId('Attribute', $id);
        }

        return Attribute::fromCscartData($row);
    }

    /**
     * @return DetailedAttributeVariant[]
     */
    public function getVariants(int $attributeId): array
    {
        $query = <<<'SQL'
            SELECT v.variant_id, v.feature_id, v.position, f.feature_code,
            d.variant, d.description, d.meta_description, d.meta_keywords, d.page_title,
            i.image_id,
            seo.name AS slug
            FROM cscart_product_feature_variants AS v
            LEFT JOIN cscart_product_features AS f
                ON v.feature_id = f.feature_id
            LEFT JOIN cscart_product_feature_variant_descriptions AS d
                ON v.variant_id = d.variant_id
                AND d.lang_code = :lang
            LEFT JOIN cscart_images_links AS i
                ON CAST(v.variant_id AS CHAR) = i.object_id
                AND i.object_type = 'feature_variant'
                AND i.type = 'V'
            LEFT JOIN cscart_seo_names AS seo
                ON CAST(v.variant_id AS CHAR) = seo.object_id
                AND seo.type = 'e'
            WHERE v.feature_id = :feature
            GROUP BY v.variant_id
            ORDER BY v.position, v.variant_id ASC
SQL;
        $rows = $this->db->fetchAll($query, [
            'lang' => (string) GlobalState::interfaceLocale(),
            'feature' => $attributeId,
        ]);

        /**
         * Si on a un array vide, deux possibilités :
         *  - l'attribut n'a pas de variantes -> on renvoit un tableau vide
         *  - l'attribut n'existe pas -> on renvoit une 404
         */
        if (empty($rows)) {
            // Cette méthode récupère l'attribut ou renvoie une 404
            $this->getAttribute($attributeId);
        }

        return array_map([$this, 'variantFromRow'], $rows);
    }

    public function getVariant(int $variantId): DetailedAttributeVariant
    {
        $query = <<<'SQL'
            SELECT v.variant_id, v.feature_id, v.position, f.feature_code,
            d.variant, d.description, d.meta_description, d.meta_keywords, d.page_title,
            i.image_id,
            seo.name AS slug
            FROM cscart_product_feature_variants AS v
            LEFT JOIN cscart_product_features AS f
                ON v.feature_id = f.feature_id
            LEFT JOIN cscart_product_feature_variant_descriptions AS d
                ON v.variant_id = d.variant_id
                AND d.lang_code = :lang
            LEFT JOIN cscart_images_links AS i
                ON CAST(v.variant_id AS CHAR) = i.object_id
                AND i.object_type = 'feature_variant'
                AND i.type = 'V'
            LEFT JOIN cscart_seo_names AS seo
                ON CAST(v.variant_id AS CHAR) = seo.object_id
                AND seo.type = 'e'
            WHERE v.variant_id = :variant
SQL;
        $row = $this->db->fetchAssoc($query, [
            'lang' => (string) GlobalState::interfaceLocale(),
            'variant' => $variantId,
        ]);

        if (empty($row)) {
            throw NotFound::fromId('Attribute variant', $variantId);
        }

        return $this->variantFromRow($row);
    }

    private function variantFromRow(array $row): DetailedAttributeVariant
    {
        $image = $row['image_id'] ? new Image((int) $row['image_id']) : null;

        $seoData = new SeoData(
            (string) $row['page_title'],
            (string) $row['meta_description'],
            (string) $row['meta_keywords'],
            (string) $row['slug']
        );

        return new DetailedAttributeVariant(
            (int) $row['variant_id'],
            (int) $row['feature_id'],
            (string) $row['variant'],
            (string) $row['slug'],
            (string) $row['description'],
            $seoData,
            (int) $row['position'],
            $image,
            (string) $row['feature_code']
        );
    }

    /**
     * Turn any argument into an array
     *
     * @param mixed|null $data Any data
     * @return array An empty array if $data is null, an array containing $data if it is a scalar, $data otherwise
     */
    private function arrayize($data = null): array
    {
        if (\is_array($data) === true) {
            if (\count($data) === 0) {
                return [];
            } else {
                return $data;
            }
        }

        return [$data];
    }
}
