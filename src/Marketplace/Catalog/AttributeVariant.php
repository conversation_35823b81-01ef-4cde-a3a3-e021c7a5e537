<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Catalog;

use Wizacha\Marketplace\Image\Image;

class AttributeVariant
{
    /**
     * @var int|null
     */
    private $id;

    /**
     * @var int|null
     */
    private $attributeId;

    /**
     * @var string
     */
    private $name;

    /**
     * @var string
     */
    private $slug;

    /**
     * @var Image|null
     */
    private $image;

    /**
     * @param int|null $id Peut être null pour des variantes dynamiques (attributs de type "texte libre" par ex.)
     * @param int|null $attributeId Peut être null pour des free attributes
     */
    public function __construct(?int $id, ?int $attributeId, string $name, string $slug, Image $image = null)
    {
        $this->id = $id;
        $this->attributeId = $attributeId;
        $this->name = $name;
        $this->slug = $slug;
        $this->image = $image;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getAttributeId(): ?int
    {
        return $this->attributeId;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function getImage(): ?Image
    {
        return $this->image;
    }

    public function __toString(): string
    {
        return $this->name;
    }

    public function expose(): array
    {
        return [
            'id' => $this->id,
            'attributeId' => $this->attributeId,
            'name' => $this->name,
            'slug' => $this->slug,
            'image' => $this->image ? $this->image->toArray() : null,
        ];
    }
}
