<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Catalog;

use Wizacha\Marketplace\Image\Image;
use Wizacha\Marketplace\SeoData;

class DetailedAttributeVariant
{
    /** @var int|null */
    private $id;

    /** @var int|null */
    private $attributeId;

    /** @var string|null */
    private $attributeCode;

    /** @var string */
    private $name;

    /** @var string */
    private $slug;

    /** @var string */
    private $description;

    /** @var Image|null */
    private $image;

    /** @var SeoData */
    private $seoData;

    private int $position;

    /**
     * @param int|null $id Peut être null pour des variantes dynamiques (attributs de type "texte libre" par ex.)
     * @param int|null $attributeId Peut être null pour des free attributes
     */
    public function __construct(
        ?int $id,
        ?int $attributeId,
        string $name,
        string $slug,
        string $description,
        SeoData $seoData,
        int $position,
        Image $image = null,
        ?string $attributeCode = null
    ) {
        $this->id = $id;
        $this->attributeId = $attributeId;
        $this->attributeCode = $attributeCode ?? null;
        $this->name = $name;
        $this->slug = $slug;
        $this->description = $description;
        $this->image = $image;
        $this->position = $position;
        $this->seoData = $seoData;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getAttributeId(): ?int
    {
        return $this->attributeId;
    }

    public function getAttributeCode(): ?string
    {
        return $this->attributeCode;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function getImage(): ?Image
    {
        return $this->image;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function getSeoData(): SeoData
    {
        return $this->seoData;
    }

    public function getPosition(): int
    {
        return $this->position ?? 0;
    }

    public function __toString(): string
    {
        return $this->name;
    }

    public function expose(): array
    {
        return [
            'id' => $this->id,
            'attributeId' => $this->attributeId,
            'attributeCode' => $this->attributeCode,
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'image' => $this->image ? $this->image->toArray() : null,
            'position' => $this->position,
            'seoData' => [
                'title' => $this->getSeoData()->getTitle(),
                'description' => $this->getSeoData()->getDescription(),
                'keywords' => $this->getSeoData()->getKeywords(),
            ],
        ];
    }
}
