<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Catalog;

use Wizacha\Marketplace\ReadModel\ReadModelObjectId;

class AttributeValue implements \JsonSerializable
{
    /** @var Attribute */
    private $attribute;

    /** @var AttributeVariant[] */
    private $values;

    /**
     * @param Attribute $attribute
     * @param AttributeVariant[] $values
     */
    public function __construct(Attribute $attribute, array $values)
    {
        $this->attribute = $attribute;
        $this->values = $values;
    }

    public function getAttribute(): Attribute
    {
        return $this->attribute;
    }

    public function getLabel(): string
    {
        return $this->attribute->getName();
    }

    /** @return AttributeVariant[] */
    public function getValues(): array
    {
        return $this->values;
    }

    public function expose()
    {
        return [
            'attribute' => $this->attribute->expose(),
            'values' => array_map(function (AttributeVariant $value) {
                return $value->expose();
            }, $this->values),
        ];
    }

    /**
     * @return array[]
     */
    public function jsonSerialize(): array
    {
        return [
            ReadModelObjectId::ATTRIBUTE_VALUE()->getValue() => [
                'attribute' => $this->attribute,
                'values' => array_map(function (AttributeVariant $value) {
                    return $value->expose();
                }, $this->values),
            ],
        ];
    }
}
