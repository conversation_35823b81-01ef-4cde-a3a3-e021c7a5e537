<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Catalog;

use Wizacha\Marketplace\PIM\Attribute\AttributeType;
use Wizacha\Marketplace\ReadModel\ReadModelObjectId;

class Attribute implements \JsonSerializable
{
    /**
     * @var int
     */
    private $id;

    /**
     * @var string
     */
    private $name;

    /**
     * @var AttributeType
     */
    private $type;

    /**
     * @var int
     */
    private $position;

    /**
     * @var int|null
     */
    private $parentId;

    /**
     * @var string|null
     */
    private $code;

    /**
     * @var bool
     */
    private $isUsedForRecommendations;

    public function __construct(?int $id = null, string $name, AttributeType $type, int $position, ?int $parentId = null, ?string $code = null, ?bool $isUsedForRecommendations = false)
    {
        $this->id = $id;
        $this->name = $name;
        $this->type = $type;
        $this->position = $position;
        $this->parentId = $parentId;
        $this->code = $code;
        $this->isUsedForRecommendations = (bool) $isUsedForRecommendations;
    }

    public static function fromCscartData(array $data): self
    {
        return new self(
            (int) $data['feature_id'],
            (string) $data['description'],
            new AttributeType($data['feature_type']),
            (int) $data['position'],
            (int) $data['parent_id'] ?: null,
            (string) $data['feature_code'],
            (bool) $data['used_for_recommendations'] ?? false
        );
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getType(): AttributeType
    {
        return $this->type;
    }

    public function getPosition(): int
    {
        return $this->position;
    }

    public function getParentId(): ?int
    {
        return $this->parentId;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function isUsedForRecommendations(): bool
    {
        return (bool) $this->isUsedForRecommendations;
    }

    public function expose(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'type' => $this->type->getValue(),
            'position' => $this->position,
            'parentId' => $this->parentId,
            'code' => $this->code,
            'isUsedForRecommendations' => $this->isUsedForRecommendations,
        ];
    }

    /**
     * @return array[]
     */
    public function jsonSerialize(): array
    {
        return [
            ReadModelObjectId::ATTRIBUTE()->getValue() => $this->expose()
        ];
    }
}
