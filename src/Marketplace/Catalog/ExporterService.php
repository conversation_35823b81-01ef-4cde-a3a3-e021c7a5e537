<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Catalog;

use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Wizacha\Core\Accumulator;
use Wizacha\Marketplace\Catalog\Product\ProductService;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\ReadModel\Product;
use Wizacha\Marketplace\ReadModel\ProductProjector;
use Wizacha\Marketplace\Traits\MonitoringTrait;
use Wizacha\ProductManager;
use Wizacha\Storage\StorageService;

class ExporterService
{
    use MonitoringTrait;

    private const MAX_LENGTH = 500;

    public const TOTAL_KEY = 'total';
    public const TRIGGER_KEY = 'trigger';

    private ProductService $productService;
    private ProductProjector $productProjector;
    private EntityManagerInterface $entityManager;
    private ProductManager $productManager;
    private LoggerInterface $logger;

    private StorageService $catalogStorageService;

    public function __construct(
        ProductService $productService,
        ProductProjector $productProjector,
        EntityManagerInterface $entityManager,
        ProductManager $productManager,
        LoggerInterface $logger,
        StorageService $catalogStorageService
    ) {
        $this->productService = $productService;
        $this->productProjector = $productProjector;
        $this->entityManager = $entityManager;
        $this->productManager = $productManager;
        $this->logger = $logger;
        $this->catalogStorageService = $catalogStorageService;
    }

    /**
     * @return \Generator<int|bool>
     *
     * @throws \Exception
     */
    public function export(): \Generator
    {
        // On récupère la langue courante
        $language = GlobalState::contentLocale();

        $productIds = $this->productService->getSearcheableProductIdInFront();
        $total = $productIds->count();
        $pages = (int) ceil($total / static::MAX_LENGTH);

        // progressBar helper
        yield static::TOTAL_KEY => $total;

        // init values
        $init = function () use (&$rows, &$fileCount) {
            $fileCount = 0;
            $rows = [];
        };

        // lap action
        $process = function () use (&$rows, &$fileCount, $language, $total, $pages) {
            ++$fileCount;

            $this->logger->debug(
                $this->debugMessage('build files'),
                [
                    'total' => $total,
                    'pages' => [
                        'current' => $fileCount,
                        'total' => $pages,
                    ],
                    'RAM' => sprintf(
                        "%.2f%%",
                        $this->freeMemoryRatio() * 100
                    )
                ]
            );

            $this->buildFile($rows, $fileCount, $language);

            // reset
            $rows = null;

            // clear RAM
            $this->entityManager->clear();
            $this->productManager->clear();
        };

        $finalize = function () use (&$process, &$fileCount, $language) {
            // build last file
            $process();

            // record pagination
            if ($fileCount !== 0) {
                $saveResult = $this
                    ->catalogStorageService
                    ->put(
                        $this->getPaginationFileName($language),
                        [
                            'contents' => json_encode($fileCount),
                            'overwrite' => true,
                        ]
                    )
                ;

                if (false === $saveResult) {
                    throw new \Exception(
                        sprintf(
                            'Could not write the file %s',
                            $this->getPaginationFileName($language)
                        )
                    );
                }
            }
        };

        $accumulator = new Accumulator(
            static::MAX_LENGTH,
            $init,
            $process,
            $finalize
        );

        $accumulator->init();

        foreach ($productIds as $productId) {
            try {
                $rows[] = $this->productService->expose(
                    $this->productService->getProduct($productId)
                    ?? $this->autoRebuildReadmodel($productId)
                );

                // progressBar helper
                yield static::TRIGGER_KEY => true;
            } catch (\Exception $exception) {
                $this->logger->error(
                    $this->debugMessage('export error'),
                    [
                        'productId' => $productId,
                        'exception' => $exception,
                    ]
                );

                // progressBar helper
                yield static::TRIGGER_KEY => false;
            }
            $accumulator->trigger();
        }

        $accumulator->finalize();
    }

    /**
     * Project and return readmodel
     */
    private function autoRebuildReadmodel($id): Product
    {
        $this->logger->error(
            $this->debugMessage('missing readmodel, rebuild'),
            [
                'id' => $id
            ]
        );

        $this->productProjector->rebuildReadmodel($id);

        return $this->productService->getProduct($id);
    }

    public function getPageContent(int $pageId, string $language): string
    {
        return \stream_get_contents(
            $this
            ->catalogStorageService
            ->readStream(
                $this->getFileName($pageId, $language),
            )
        );
    }

    public function getPageMax(string $language): int
    {
        $maxPage = \json_decode(
            \stream_get_contents(
                $this
                ->catalogStorageService
                ->readStream(
                    $this->getPaginationFileName($language)
                )
            )
        );

        return
            true === \is_integer($maxPage)
            ? $maxPage : 0
        ;
    }

    private function getFileName(int $pageId, string $language): string
    {
        return "catalog-${language}-${pageId}.json";
    }

    private function getPaginationFileName(string $language): string
    {
        return "pagination-${language}.json";
    }

    /**
     * @throws \Exception
     */
    private function buildFile(array $rows, int $fileCount, string $language)
    {
        $fileName = $this->getFileName($fileCount, $language);
        $content = $this->encodeFile($rows);

        $saveResult = $this->catalogStorageService
            ->put(
                $fileName,
                [
                    'contents' => $content,
                    'overwrite' => true,
                ]
            )
        ;

        if (false === $saveResult) {
            throw new \Exception('Could not write the file ' . $fileName);
        }

        return $this->catalogStorageService->getAbsolutePath($fileName);
    }

    private function encodeFile(array $data): string
    {
        $content = \json_encode($data);

        if (JSON_ERROR_NONE !== \json_last_error()) {
            throw new \InvalidArgumentException(
                \json_last_error_msg()
            );
        }

        return $content;
    }

    private function debugMessage(string $message): string
    {
        return \sprintf(
            'ExporterService: %s',
            $message
        );
    }
}
