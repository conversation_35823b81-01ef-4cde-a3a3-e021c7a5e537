<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Catalog;

use Doctrine\DBAL\Connection;
use Tygh\Languages\Languages;
use Wizacha\Async\Dispatcher;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct;
use Wizacha\Marketplace\ReadModel\ProductProjector;

class CatalogService
{
    /** @var Connection */
    private $connection;

    /** @var ProductProjector */
    private $projector;

    /** @var Dispatcher */
    private $jobDispatcher;

    public function __construct(
        Connection $connection,
        ProductProjector $projector,
        Dispatcher $jobDispatcher
    ) {
        $this->connection = $connection;
        $this->projector = $projector;
        $this->jobDispatcher = $jobDispatcher;
    }

    /**
     * Reconstruit les données pour tous les produits déjà présents dans le catalogue
     */
    public function rebuild(): self
    {
        if ($this->jobDispatcher->delayExec('marketplace.catalog::' . __FUNCTION__, [])) {
            return $this;
        }

        // SQL Readmodel
        $query = $this->connection->query('SELECT DISTINCT id FROM product_catalog;');
        while ($id = $query->fetchColumn()) {
            if (MultiVendorProduct::isMultiVendorProductId($id)) {
                $this->projector->projectMultiVendorProduct((string) $id);
            } else {
                $this->projector->projectProduct((int) $id);
            }
        }

        return $this;
    }

    /**
     * Supprime toutes les traductions qui ne sont plus actives
     */
    public function cleanup(): self
    {
        $locales = array_keys(Languages::getAvailable());

        // SQL Readmodel
        $this->connection->executeQuery(
            'DELETE FROM product_catalog WHERE locale NOT IN (?)',
            [$locales],
            [Connection::PARAM_STR_ARRAY]
        );

        return $this;
    }
}
