<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Catalog\Company;

use Wizacha\Marketplace\Image\Image;
use Wizacha\Marketplace\Company\CompanyType;

class CompanySummary
{
    /** @var int */
    private $id;

    /** @var string */
    private $name;

    /** @var string */
    private $slug;

    /** @var null|Image */
    private $image;

    /** @var bool */
    private $isProfessional;

    /**
     * @var float|null
     */
    private $averageRating;

    public function __construct(int $id, string $name, string $slug, bool $isProfessional, ?Image $image, ?float $averageRating)
    {
        $this->id = $id;
        $this->name = $name;
        $this->slug = $slug;
        $this->isProfessional = $isProfessional;
        $this->image = $image;
        $this->averageRating = $averageRating;
    }

    public static function createFromDeclinationReadmodel(array $declinationReadmodel)
    {
        return new self(
            (int) $declinationReadmodel['company_id'],
            (string) $declinationReadmodel['company_name'],
            (string) $declinationReadmodel['company_slug'],
            (bool) ($declinationReadmodel['company_type'] === (string) CompanyType::PROFESSIONAL()),
            !empty($declinationReadmodel['company_image']['id']) ? new Image($declinationReadmodel['company_image']['id']) : null,
            $declinationReadmodel['company_rating'] ?? null
        );
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function getImage(): ?Image
    {
        return $this->image;
    }

    public function isProfessional(): bool
    {
        return $this->isProfessional;
    }

    public function getAverageRating(): ?float
    {
        return $this->averageRating;
    }

    public function expose(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'isProfessional' => $this->isProfessional,
            'image' => \is_null($this->image) ? null : $this->image->toArray(),
            'averageRating' => $this->averageRating,
        ];
    }
}
