<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Catalog\Company;

use Wizacha\Marketplace\Image\Image;
use Wizacha\Marketplace\Review\CompanyReviewService;
use Wizacha\Marketplace\Review\ReviewType;
use Wizacha\Marketplace\Company\CompanyType;

class Company
{
    /**
     * @var int
     */
    private $id;

    /**
     * @var string
     */
    private $name;

    /** @var string */
    private $corporateName;

    /**
     * @var string
     */
    private $description;

    /**
     * @var string
     */
    private $website;

    /**
     * @var string
     */
    private $terms;

    /**
     * @var bool
     */
    private $professional;

    /**
     * @var string
     */
    private $slug;

    /**
     * @var Image
     */
    private $image;

    /**
     * @var string
     */
    private $address;

    /**
     * @var string
     */
    private $city;

    /**
     * @var string
     */
    private $zipcode;

    /**
     * @var string
     */
    private $country;

    /**
     * @var string
     */
    private $email;

    /**
     * @var string
     */
    private $phoneNumber;

    /**
     * @var string
     */
    private $faxNumber;

    /**
     * @var float|null
     */
    private $latitude;

    /**
     * @var float|null
     */
    private $longitude;

    /**
     * @var int|null
     */
    private $threadId;

    /**
     * @var ReviewType|null
     */
    private $threadType;

    /**
     * @var float
     */
    private $averageRating;

    /**
     * @var array
     */
    private $extra;

    /** @var array */
    private $extras;

    /**
     * @var null|string
     */
    private $metaDescription;

    /**
     * @var null|string
     */
    private $metaKeywords;

    /**
     * @var null|string
     */
    private $metaTitle;

    /** @var null|string */
    private $capital;

    /** @var null|string */
    private $legalStatus;

    /** @var null|string */
    private $siretNumber;

    /** @var null|string */
    private $vatNumber;

    public function __construct(array $data)
    {
        $this->id = (int) $data['company_id'];
        $this->name = (string) $data['company'];
        $this->corporateName = $data['corporate_name'];
        $this->description = (string) $data['company_description'];
        $this->city = (string) $data['city'];
        $this->address = (string) $data['address'];
        $this->zipcode = (string) $data['zipcode'];
        $this->country = (string) $data['country'];
        $this->email = (string) $data['email'];
        $this->phoneNumber = (string) $data['phone'];
        $this->faxNumber = (string) $data['fax'];
        $this->website = (string) $data['url'];
        $this->terms = (string) $data['company_terms'];
        $this->professional = (bool) ($data['w_company_type'] === (string) CompanyType::PROFESSIONAL());
        $this->slug = (string) $data['slug'];
        $this->image = $data['image_id'] ? new Image((int) $data['image_id']) : null;
        $this->latitude = isset($data['latitude']) ? ((float) $data['latitude']) : null;
        $this->longitude = isset($data['longitude']) ? ((float) $data['longitude']) : null;
        $this->threadId = isset($data['thread_id']) ? ((int) $data['thread_id']) : null;
        $this->threadType = isset($data['thread_type']) ? new ReviewType($data['thread_type']) : null;
        $this->averageRating = isset($data['average_rating']) ? $data['average_rating'] : null;
        $this->metaDescription = $data['w_company_meta_description'] ? (string) $data['w_company_meta_description'] : null;
        $this->metaKeywords = $data['w_company_meta_keywords'] ? (string) $data['w_company_meta_keywords'] : null;
        $this->metaTitle = $data['w_company_meta_title'] ? (string) $data['w_company_meta_title'] : null;
        $this->extras = empty($data['w_extras']) === false ? unserialize($data['w_extras']) : [];
        $this->capital = (\array_key_exists("w_capital", $this->extras) === true && empty($this->extras['w_capital']) === false) ? $this->extras["w_capital"] : null;
        $this->legalStatus = (\array_key_exists("w_legal_status", $data) === true && empty($data['w_legal_status']) === false) ? (string) $data['w_legal_status'] : null;
        $this->siretNumber = (\array_key_exists("w_siret_number", $data) === true && empty($data['w_siret_number']) === false) ? (string) $data['w_siret_number'] : null;
        $this->vatNumber = (\array_key_exists("w_vat_number", $data) === true && empty($data['w_vat_number']) === false) ? (string) $data['w_vat_number'] : null;

        $this->extra = !empty($data['extra']) ? unserialize($data['extra']) : [];
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getCorporateName(): string
    {
        return $this->corporateName;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function getCity(): string
    {
        return $this->city;
    }

    public function getAddress(): string
    {
        return $this->address;
    }

    public function getZipcode(): string
    {
        return $this->zipcode;
    }

    public function getCountry(): string
    {
        return $this->country;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function getPhoneNumber(): string
    {
        return $this->phoneNumber;
    }

    public function getFaxNumber(): string
    {
        return $this->faxNumber;
    }

    public function getWebsite(): string
    {
        return $this->website;
    }

    public function getShortDescription(): string
    {
        $description = (string) $this->getDescription();
        // First 500 characters
        return substr(strip_tags($description), 0, 500);
    }

    public function getTerms(): string
    {
        return $this->terms;
    }

    /**
     * Whether the company is a professional company (B2C/B2B) or a private individual (C2C).
     */
    public function isProfessional(): bool
    {
        return $this->professional;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function getImage(): ?Image
    {
        return $this->image;
    }

    public function getLatitude(): ?float
    {
        return $this->latitude;
    }

    public function getLongitude(): ?float
    {
        return $this->longitude;
    }

    public function getThreadId(): ?int
    {
        return $this->threadId;
    }

    public function getThreadType(): ?ReviewType
    {
        return $this->threadType;
    }

    public function getAverageRating(): ?float
    {
        return $this->averageRating;
    }

    public function getExtra(): array
    {
        return $this->extra;
    }

    public function getMetaDescription(): ?string
    {
        return $this->metaDescription;
    }

    public function getMetaKeywords(): ?string
    {
        return $this->metaKeywords;
    }

    public function getMetaTitle(): ?string
    {
        return $this->metaTitle;
    }

    /** @return array */
    public function getExtras(): array
    {
        return $this->extras;
    }

    public function getCapital(): ?string
    {
        return $this->capital;
    }

    public function getLegalStatus(): ?string
    {
        return $this->legalStatus;
    }

    public function getSiretNumber(): ?string
    {
        return $this->siretNumber;
    }

    public function getVatNumber(): ?string
    {
        return $this->vatNumber;
    }

    public function expose(CompanyReviewService $reviewService)
    {
        $exposedData = [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'corporateName' => $this->getCorporateName(),
            'description' => $this->getDescription(),
            'address' => $this->getAddress(),
            'phoneNumber' => $this->getPhoneNumber(),
            'professional' => $this->isProfessional(),
            'slug' => $this->getSlug(),
            'image' => $this->getImage() ? $this->getImage()->toArray() : null,
            'location' => !\is_null($this->getLatitude()) && !\is_null($this->getLongitude()) ? [
                'latitude' => $this->getLatitude(),
                'longitude' => $this->getLongitude(),
            ] : null,
            'averageRating' => $reviewService->getAverageRating($this->getId()),
            'fullAddress' => [
                'address' => $this->getAddress(),
                'zipCode' => $this->getZipcode(),
                'city' => $this->getCity(),
                'country' => $this->getCountry(),
            ],
            'extra' => $this->getExtra(),
            'capital' => $this->getCapital(),
            'legalStatus' => $this->getLegalStatus(),
            'siretNumber' => $this->getSiretNumber(),
            'vatNumber' => $this->getVatNumber(),
        ];

        return $exposedData;
    }

    public function exposeFull(CompanyReviewService $reviewService)
    {
        return $this->expose($reviewService) + [
            'terms' => $this->getTerms(),
            'meta' => [
                'title' => $this->getMetaTitle(),
                'keywords' => $this->getMetaKeywords(),
                'description' => $this->getMetaDescription(),
            ],
        ];
    }
}
