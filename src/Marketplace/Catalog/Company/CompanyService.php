<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Catalog\Company;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Query\QueryBuilder;
use PDO;
use Wizacha\Marketplace\Exception\CompanyNotFound;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Seo\SlugTargetType;
use Wizacha\Status;

class CompanyService
{
    /**
     * @var Connection
     */
    private $db;

    public function __construct(Connection $db)
    {
        $this->db = $db;
    }

    public function count(): int
    {
        $status = STATUS::ENABLED;
        $query = <<<SQL
        SELECT count(c.company_id) as count
        FROM cscart_companies AS c
        WHERE c.status = '{$status}'
SQL;
        $count = $this->db->fetchColumn($query);

        return (int) $count;
    }

    /**
     * @return Company[]
     */
    public function getCompanies(array $filter = []): array
    {
        $rows = $this->selectCompany(null, $filter)
            ->execute()
            ->fetchAll(PDO::FETCH_ASSOC);

        return array_map(function (array $row) {
            return new Company($row);
        }, $rows);
    }

    /**
     * @return int[]
     */
    public function getCompaniesId(): array
    {
        $queryBuilder = $this->db->createQueryBuilder();

        return $queryBuilder
            ->select('c.company_id')
            ->from('cscart_companies', 'c')
            ->where($queryBuilder->expr()->eq('c.status', ':status'))
            ->setParameter('status', Status::ENABLED)
            ->execute()
            ->fetchAll(PDO::FETCH_COLUMN)
        ;
    }

    /**
     * @return Company[]
     * if no department is given then all companies are returned
     */
    public function getCompaniesByDepartment(string $department, int $page = 1, int $resultsPerPage = 48): array
    {
        if ($page < 1) {
            throw new \InvalidArgumentException('page must be greater than 1');
        }

        $rows = $this->selectCompany()
            ->andWhere("c.zipcode LIKE :department")
            ->setFirstResult(($page - 1) * $resultsPerPage)
            ->setMaxResults($resultsPerPage)
            ->setParameter('department', $department . '%', PDO::PARAM_STR)
            ->execute()
            ->fetchAll(PDO::FETCH_ASSOC);

        return array_map(function (array $row) {
            return new Company($row);
        }, $rows);
    }

    /**
     * @throws CompanyNotFound
     */
    public function getCompany(int $id): Company
    {
        $row = $this->selectCompany()
            ->andWhere("c.company_id = :id")
            ->setParameter('id', $id, PDO::PARAM_INT)
            ->execute()
            ->fetch(PDO::FETCH_ASSOC);

        if (empty($row)) {
            throw CompanyNotFound::fromCompanyId($id);
        }

        $seoData = $this->getCompanySeoData($id);

        foreach ($seoData as $data) {
            $row[$data['object_holder']] = $data['description'];
        }

        return new Company($row);
    }

    /**
     * @throws CompanyNotFound
     */
    public function getCompanyBySlug(string $slug): Company
    {
        $row = $this->selectCompany()
            ->andWhere("s.name = :slug")
            ->setParameter('slug', $slug, PDO::PARAM_STR)
            ->execute()
            ->fetch(PDO::FETCH_ASSOC);

        if (empty($row)) {
            throw CompanyNotFound::fromCompanySlug($slug);
        }

        return new Company($row);
    }

    private function selectCompany(string $lang = null, array $filter = []): QueryBuilder
    {
        $lang = $lang ?? (string) GlobalState::interfaceLocale();
        $qb = $this->db->createQueryBuilder()->select(
            'c.company_id',
            'c.company',
            'c.corporate_name',
            'c.city',
            'c.w_company_type',
            'c.w_legal_status',
            'c.w_extras',
            'c.w_siret_number',
            'c.w_vat_number',
            'c.url',
            'c.address',
            'c.zipcode',
            'c.country',
            'c.email',
            'c.phone',
            'c.fax',
            'c.latitude',
            'c.longitude',
            'c.extra',
            'd.company_description',
            'd.company_terms',
            's.name AS slug',
            'i.detailed_id as image_id',
            't.thread_id',
            't.type as thread_type'
        )->from('cscart_companies', 'c')
        ->innerJoin('c', 'cscart_company_descriptions', 'd', 'c.company_id = d.company_id');

        $qb->leftJoin(
            'c',
            'cscart_seo_names',
            's',
            "CAST(c.company_id AS CHAR) = s.object_id
                AND s.type = {$qb->createNamedParameter(SlugTargetType::COMPANY)}"
        )
        ->leftJoin(
            'c',
            'cscart_images_links',
            'i',
            "CAST(c.company_id AS CHAR) = i.object_id
                AND i.object_type = 'company'
                AND i.type = 'M'"
        )
        ->leftJoin(
            'c',
            'cscart_discussion',
            't', // thread
            "CAST(c.company_id AS CHAR) = t.object_id
                AND t.object_type = 'M'"
        )
        ->where("c.status = {$qb->createNamedParameter(Status::ENABLED)} AND d.lang_code = {$qb->createNamedParameter($lang)}");

        $extraData = [];
        if (\array_key_exists('extra', $filter) === true
            && \count($filter['extra']) > 0
        ) {
            /**
             * Les données de la colonne extra de la table cscart_companies sont sérialisées
             */
            $operator = 'AND';
            foreach ($filter['extra'] as $key => $value) {
                // Filter with the same key ?extra[key][]=value1&extra[key][]=value2
                if (\is_array($value) === true) {
                    $operator = 'OR';
                    foreach ($value as $v) {
                        \preg_match('/\{(.*?)\}/', \serialize([$key => $v]), $match);
                        \array_push($extraData, $match[1]);
                    }
                } elseif ($value === '') { // Filter by key
                    \array_push($extraData, \serialize($key));
                } else { // Filter by key and value
                    \preg_match('/\{(.*?)\}/', \serialize([$key => $value]), $match);
                    \array_push($extraData, $match[1]);
                }
            }

            $qb->andWhere("c.extra like '%" . \implode("%' " . $operator . " c.extra like '%", $extraData) . "%'");
        }

        return $qb;
    }

    private function getCompanySeoData(int $id): array
    {
        return $this
            ->db
            ->createQueryBuilder()
            ->select('description.description, description.object_holder')
            ->from('cscart_common_descriptions', 'description')
            ->where('description.object_id = :id')
            ->setParameter('id', $id)
            ->andWhere('description.object_holder IN (:metadata)')
            ->setParameter(
                'metadata',
                [
                    'w_company_meta_description',
                    'w_company_meta_keywords',
                    'w_company_meta_title',
                ],
                Connection::PARAM_STR_ARRAY
            )
            ->andWhere('description.lang_code = :lang')
            ->setParameter('lang', (string) GlobalState::interfaceLocale())
            ->execute()
            ->fetchAll(\PDO::FETCH_ASSOC);
    }
}
