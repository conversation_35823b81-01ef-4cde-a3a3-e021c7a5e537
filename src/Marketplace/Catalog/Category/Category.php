<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Catalog\Category;

use Wizacha\Marketplace\Image\Image;
use Wizacha\Marketplace\SeoData;

class Category
{
    /**
     * @var int
     */
    private $id;

    /**
     * @var string
     */
    private $name;

    /**
     * @var string
     */
    private $description;

    /**
     * @var int
     */
    private $parentId;

    /**
     * @var int|null
     */
    private $position;

    /**
     * @var int
     */
    private $productCount;

    /** @var int */
    private $ageLimit;

    /**
     * @var string
     */
    private $slug;

    /**
     * @var Image|null
     */
    private $image;

    /**
     * @var SeoData
     */
    private $seoData;

    /**
     * @var CategorySummary[]
     */
    private $categoryPath = [];

    public function __construct(array $data)
    {
        $this->id = (int) $data['category_id'];
        $this->name = (string) $data['name'];
        $this->description = (string) $data['description'];
        $this->parentId = (int) $data['parent_id'] ?: null;
        $this->position = (int) $data['position'];
        $this->productCount = (int) $data['visible_product_count'];
        $this->ageLimit = \array_key_exists('age_limit', $data) ? (int) $data['age_limit'] : 0;
        $this->slug = (string) $data['slug'];
        $this->image = $data['image_id'] ? new Image((int) $data['image_id'], $data['image_alt_text']) : null;
        $this->seoData = new SeoData(
            (string) $data['page_title'],
            (string) $data['meta_description'],
            (string) $data['meta_keywords'],
            $this->slug
        );
        $this->categoryPath = $data['categoryPath'] ?? [];
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function getParentId(): ?int
    {
        return $this->parentId;
    }

    public function getPosition(): int
    {
        return $this->position;
    }

    public function getProductCount(): int
    {
        return $this->productCount;
    }

    /** @return int */
    public function getAgeLimit(): int
    {
        return $this->ageLimit;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function getImage(): ?Image
    {
        return $this->image;
    }

    public function getSeoData(): SeoData
    {
        return $this->seoData;
    }

    /**
     * @return CategorySummary[]
     */
    public function getCategoryPath(): array
    {
        return $this->categoryPath;
    }

    public function expose(): array
    {
        return [
            'id' => $this->getId(),
            'parentId' => $this->getParentId(),
            'name' => $this->getName(),
            'description' => $this->getDescription(),
            'slug' => $this->getSlug(),
            'image' => $this->getImage() ? $this->getImage()->toArray() : null,
            'position' => $this->getPosition(),
            'productCount' => $this->getProductCount(),
            'ageLimit' => $this->getAgeLimit(),
            'seoData' => [
                'title' => $this->getSeoData()->getTitle(),
                'description' => $this->getSeoData()->getDescription(),
                'keywords' => $this->getSeoData()->getKeywords(),
            ],
            'categoryPath' => array_map(function (CategorySummary $category) {
                return $category->expose();
            }, $this->getCategoryPath()),
        ];
    }
}
