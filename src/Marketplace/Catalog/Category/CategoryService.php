<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Catalog\Category;

use Doctrine\DBAL\Connection;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\GlobalState\GlobalState;

class CategoryService
{
    /**
     * @var Connection
     */
    private $db;

    public function __construct(Connection $db)
    {
        $this->db = $db;
    }

    /**
     * Returns all active and visible categories.
     *
     * @param int|int[]|null $id
     * @return Category[]
     */
    public function getCategories($id = [], bool $showDisabled = false): ?array
    {
        $statusCondition = ($showDisabled === false) ? "c.status = 'A'" : "c.status IN ('A', 'H', 'D')";

        $qb = $this->db->createQueryBuilder()->select(
            'c.category_id',
            'c.parent_id',
            'c.id_path',
            'c.position',
            'c.visible_product_count',
            'c.age_limit',
            'cd.category AS name',
            'cd.description',
            'cd.meta_description',
            'cd.meta_keywords',
            'cd.page_title',
            's.name AS slug',
            'i.detailed_id AS image_id',
            'd.description AS image_alt_text'
        )
            ->from('cscart_categories', 'c');
        $qb->leftJoin(
            'c',
            'cscart_category_descriptions',
            'cd',
            'c.category_id = cd.category_id
                AND cd.lang_code = :lang'
        )
            ->leftJoin(
                'c',
                'cscart_seo_names',
                's',
                "CAST(c.category_id AS CHAR) = s.object_id
                AND s.type = 'c'"
            )
            ->leftJoin(
                'c',
                'cscart_images_links',
                'i',
                "CAST(c.category_id AS CHAR) = i.object_id
                AND i.object_type = 'category'
                AND i.type = 'M'"
            )
            ->leftJoin(
                'i',
                'cscart_common_descriptions',
                'd',
                "d.object_id = i.detailed_id
                AND d.object_holder = 'images'
                AND d.lang_code = :lang"
            )
            ->where($statusCondition)
            ->setParameter('lang', (string) GlobalState::interfaceLocale())
            ->groupBy('c.category_id')
            ->orderBy('c.position');

        $rows = $qb->execute()->fetchAll();

        $rows = array_map(
            function (array $row): Category {
                return new Category($row);
            },
            $this->addCategoryPath($rows)
        );

        if (true === \is_array($id)
            && 0 < \count($id)
        ) {
            $rows = \array_values(
                \array_filter(
                    $rows,
                    function (Category $category) use ($id): bool {
                        return \in_array($category->getId(), $id, true);
                    }
                )
            );
        }

        return $rows;
    }

    public function getCategory(int $id): Category
    {
        // Hidden categories can be retrieved directly
        $query = <<<'SQL'
            SELECT c.category_id, c.parent_id, c.id_path, c.position, c.visible_product_count, c.age_limit,
            cd.category AS name, cd.description, cd.meta_description, cd.meta_keywords, cd.page_title,
            s.name AS slug,
            i.detailed_id AS image_id,
            d.description AS image_alt_text
            FROM cscart_categories AS c
            LEFT JOIN cscart_category_descriptions AS cd
                ON c.category_id = cd.category_id
                AND cd.lang_code = :lang
            LEFT JOIN cscart_seo_names AS s
                ON CAST(c.category_id AS CHAR) = s.object_id
                AND s.type = 'c'
            LEFT JOIN cscart_images_links AS i
                ON CAST(c.category_id AS CHAR) = i.object_id
                AND i.object_type = 'category'
                AND i.type = 'M'
            LEFT JOIN cscart_common_descriptions AS d 
                ON d.object_id = i.detailed_id
                AND d.object_holder = 'images'
                AND d.lang_code = :lang
            WHERE (c.status = 'A' OR c.status = 'H')
                AND c.category_id = :categoryId
            GROUP BY c.category_id
SQL;

        $row = $this->db->fetchAssoc($query, [
            'lang' => (string) GlobalState::contentLocale(),
            'categoryId' => $id,
        ]);

        if (!$row) {
            throw NotFound::fromId('Category', $id);
        }

        $row['id_path'] = $row['id_path'] ? explode('/', (string) $row['id_path']) : [];
        $row['categoryPath'] = $this->getCategorySummariesByIds($row['id_path']);

        return new Category($row);
    }


    /**
     * @return Category[]
     */
    public function getRootCategories(): array
    {
        $query = <<<'SQL'
            SELECT c.category_id, c.parent_id, c.id_path, c.position, c.visible_product_count,
            cd.category AS name, cd.description, cd.meta_description, cd.meta_keywords, cd.page_title,
            s.name AS slug,
            i.detailed_id AS image_id
            FROM cscart_categories AS c
            LEFT JOIN cscart_category_descriptions AS cd
                ON c.category_id = cd.category_id
                AND cd.lang_code = :lang
            LEFT JOIN cscart_seo_names AS s
                ON CAST(c.category_id AS CHAR) = s.object_id
                AND s.type = 'c'
            LEFT JOIN cscart_images_links AS i
                ON CAST(c.category_id AS CHAR) = i.object_id
                AND i.object_type = 'category'
                AND i.type = 'M'
            WHERE c.status = 'A' AND c.parent_id = 0
            GROUP BY c.category_id
            ORDER BY c.position
SQL;
        $rows = $this->db->fetchAll($query, [
            'lang' => (string) GlobalState::contentLocale(),
        ]);

        return array_map(function (array $row) {
            return new Category($row);
        }, $this->addCategoryPath($rows));
    }

    /**
     * @param array $categoriesIds
     * @return CategorySummary[]
     */
    public function getCategorySummariesByIds(array $categoriesIds): array
    {
        if (\count($categoriesIds) === 0) {
            return [];
        }

        $query = <<<'SQL'
            SELECT
                c.category_id,
                c.parent_id,
                c.position,
                cd.category as name,
                s.name AS slug
            FROM
                cscart_categories AS c
                LEFT JOIN cscart_category_descriptions AS cd
                    ON c.category_id = cd.category_id
                    AND cd.lang_code = ?
                LEFT JOIN cscart_seo_names AS s
                    ON CAST(c.category_id AS CHAR) = s.object_id
                    AND s.type = 'c'
            WHERE
                c.status = 'A'
                AND c.category_id IN (?)
SQL;

        $rows = $this->db->executeQuery(
            $query,
            [(string) GlobalState::interfaceLocale(), $categoriesIds],
            [\PDO::PARAM_STR, $this->db::PARAM_INT_ARRAY]
        )->fetchAll();

        return array_map(
            function (array $row): CategorySummary {
                return new CategorySummary(
                    (int) $row['category_id'],
                    (string) $row['name'],
                    (string) $row['slug'],
                    (int) $row['position']
                );
            },
            $rows
        );
    }

    /**
     * @return array An array of 'branch'. Each branch is an array which contains a Category in 'category' key and an array of branch in 'children'
     */
    public function getTree(): array
    {
        $categories = [];

        foreach ($this->getCategories() as $category) {
            $categories[$category->getId()] = [
                'category' => $category,
                'children' => [],
            ];
        }

        return self::buildTree($categories);
    }

    private static function buildTree(array &$elements, $parentId = null): array
    {
        $branch = [];

        foreach ($elements as $element) {
            /** @var Category $category */
            $category = $element['category'];
            $categoryId = $category->getId();

            if ($category->getParentId() === $parentId) {
                $children = self::buildTree($elements, $categoryId);
                if ($children) {
                    $element['children'] = $children;
                }
                $branch[$categoryId] = $element;
                unset($elements[$categoryId]);
            }
        }

        return $branch;
    }

    private function addCategoryPath(array $rows): array
    {
        $data = [];

        foreach ($rows as &$row) {
            $row['id_path'] = $row['id_path'] ? explode('/', $row['id_path']) : [];
            $data[$row['category_id']] = $row;
        }

        foreach ($rows as &$row) {
            $row['categoryPath'] = array_map(function ($id) use ($data) {
                return new CategorySummary(
                    (int) $data[$id]['category_id'],
                    (string) $data[$id]['name'],
                    (string) $data[$id]['slug'],
                    (int) $data[$id]['position']
                );
            }, $row['id_path']);
        }

        return $rows;
    }

    /** @return int[] */
    public function getCategoryChildrenIds($id): array
    {
        $query = <<<'SQL'
            SELECT category_id
            FROM cscart_categories
            WHERE parent_id = :parentId
SQL;

        $rows = $this->db->fetchAll($query, [
            'parentId' => $id,
        ]);

        return \array_map(
            function (array $row): int {
                return (int) $row['category_id'];
            },
            $rows
        );
    }

    /** @return int[] */
    public function getCategoryAllChildrenIds(int $id): array
    {
        $ids = [];
        $children = $this->getCategoryChildrenIds($id);

        if (\count($children) > 0) {
            foreach ($children as $child) {
                $ids = \array_merge($ids, $this->getCategoryAllChildrenIds($child));
            }
        }

        if (\in_array($id, $ids) === false) {
            $ids[] = $id;
        }

        return $ids;
    }
}
