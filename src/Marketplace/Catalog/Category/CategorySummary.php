<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Catalog\Category;

/**
 * Light representation of the category of a product.
 *
 * This entity does not contain all the information of the category, only the ones we want to make
 * available in the product readmodel.
 */
class CategorySummary
{
    /**
     * @var int
     */
    private $id;

    /**
     * @var string
     */
    private $name;

    /**
     * @var string
     */
    private $slug;

    /**
     * @var int
     */
    private $position;

    public function __construct(int $id, string $name, string $slug, int $position)
    {
        $this->id = $id;
        $this->name = $name;
        $this->slug = $slug;
        $this->position = $position;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function getPosition(): int
    {
        return $this->position;
    }

    public function expose(): array
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'slug' => $this->getSlug(),
        ];
    }
}
