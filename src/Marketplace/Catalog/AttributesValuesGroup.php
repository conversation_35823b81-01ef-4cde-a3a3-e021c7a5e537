<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Catalog;

use Wizacha\Marketplace\PIM\Attribute\AttributeType;
use Wizacha\Marketplace\ReadModel\ReadModelObjectId;

class AttributesValuesGroup implements \JsonSerializable
{
    /**
     * @var Attribute
     */
    private $groupAttribute;

    /**
     * @var AttributeValue[]
     */
    private $attributesValues;

    /**
     * @param Attribute $groupAttribute MUST be of type AttributeType::GROUP()
     * @param AttributeValue[] $attributesValues
     */
    public function __construct(Attribute $groupAttribute, array $attributesValues)
    {
        if (!$groupAttribute->getType()->equals(AttributeType::GROUP())) {
            throw new \InvalidArgumentException("Expected group attribute, got {$groupAttribute->getType()->getValue()}");
        }
        $this->groupAttribute = $groupAttribute;

        $this->checkArrayContainsAttributeValues(...$attributesValues);
        $this->attributesValues = $attributesValues;
    }

    public function getGroupAttribute(): Attribute
    {
        return $this->groupAttribute;
    }

    public function getName(): string
    {
        return $this->groupAttribute->getName();
    }

    /**
     * @return AttributeValue[]
     */
    public function getAttributesValues(): array
    {
        return $this->attributesValues;
    }

    public function expose(): array
    {
        return [
            'groupAttribute' => $this->getGroupAttribute()->expose(),
            'attributesValues' => array_map(function (AttributeValue $value): array {
                return $value->expose();
            }, $this->getAttributesValues()),
        ];
    }

    /**
     * @return array[]
     */
    public function jsonSerialize(): array
    {
        return [
            ReadModelObjectId::ATTRIBUTE_GROUP()->getValue() => [
                'groupAttribute' => $this->getGroupAttribute(),
                'attributesValues' => $this->getAttributesValues(),
            ]
        ];
    }

    private function checkArrayContainsAttributeValues(AttributeValue ...$attributeValues): void
    {
        // just here for the type check
    }
}
