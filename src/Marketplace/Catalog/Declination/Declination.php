<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Catalog\Declination;

use Wizacha\Marketplace\Catalog\Category\CategorySummary;
use W<PERSON>cha\Marketplace\Catalog\Company\CompanySummary;
use Wizacha\Marketplace\Catalog\Product\ProductCondition;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Image\Image;
use Wizacha\Money\Money;

/**
 * Represents a product declination.
 *
 * For example given a "T-Shirt" product, the product may have the following declinations:
 *
 * - "T-Shirt black XL"
 * - "T-Shirt red XL"
 * - "T-Shirt black M"
 */
class Declination
{
    /**
     * @var array
     */
    private $data;

    /**
     * @var DeclinationOption[]|null
     */
    private $options;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function getId(): string
    {
        return $this->data['objectID'];
    }

    public function getProductId(): int
    {
        return $this->data['product_id'];
    }

    public function getPrice(): Money
    {
        return Money::fromVariable($this->data['final_price']);
    }

    public function getPriceWithTax(): Money
    {
        return Money::fromVariable($this->data['final_price_with_tax'] ?? 0);
    }

    public function getPriceWithoutTax(): Money
    {
        return Money::fromVariable($this->data['final_price_without_tax'] ?? 0);
    }

    public function getTax(): Money
    {
        return Money::fromVariable($this->data['final_price_tax'] ?? 0);
    }

    public function getGreenTax(): Money
    {
        return Money::fromVariable($this->data['green_tax'] ?? 0);
    }

    public function getCrossedOutPrice(): ?Money
    {
        $price = $this->data['crossed_out_price'] ?? null;
        if ($price === 0) {
            return null;
        }

        return $price ? Money::fromVariable($price) : null;
    }

    /**
     * Returns the price reduction percentage if there is a crossed out price, else null.
     *
     * @return float|null
     */
    public function getReductionPercentage()
    {
        $originalPrice = $this->getCrossedOutPrice();
        if (!$originalPrice) {
            return null;
        }
        $priceDifference = $this->getPrice()->subtract($originalPrice)->getConvertedAmount();

        return $priceDifference * 100 / $originalPrice->getConvertedAmount();
    }

    public function getAmount(): int
    {
        return (int) $this->data['amount'];
    }

    public function hasInfiniteStock(): bool
    {
        return (bool) $this->data['infinite_stock'];
    }

    public function isAvailable(): bool
    {
        return ($this->hasInfiniteStock() || $this->inStock()) && !$this->getPrice()->isZero();
    }

    public function inStock(): bool
    {
        return $this->getAmount() > 0;
    }

    public function isContact(): bool
    {
        return $this->data['is_contact_only'];
    }

    public function isAffiliate(): bool
    {
        return $this->data['is_affiliate'];
    }

    public function getAffiliateLink(): ?string
    {
        return $this->data['affiliate_link'];
    }

    /**
     * @return DeclinationOption[]
     */
    public function getOptions(): array
    {
        if (!$this->options) {
            $this->options = array_map(function ($option) {
                return new DeclinationOption(
                    (int) $option['option_id'],
                    $option['option_name'],
                    (int) $option['value_id'],
                    $option['value_name'],
                    $option['image_id'] ? new Image((int) $option['image_id']) : null,
                    $option['code']
                );
            }, $this->data['declination']);
        }

        return array_values($this->options);
    }

    public function getOption(int $id): DeclinationOption
    {
        $this->getOptions(); // trigger creating the array
        if (!isset($this->options[$id])) {
            throw NotFound::fromId('Declination option', $id);
        }

        return $this->options[$id];
    }

    /**
     * Vérifie que la déclinaison contient les options passées en paramètre
     *
     * $optionCriteria est un tableau de type array<int, int>
     *
     * Par exemple, avec un "T-Shirt noir XL", la fonction retourne `true` si `$optionCriteria` contient les données
     * correspondant à l'option "Taille" avec la valeur "XL" ET l'option "Couleur" avec la valeur "noir".
     */
    public function optionsValuesContains(array $optionCriteria): bool
    {
        foreach ($optionCriteria as $optionId => $valueId) {
            try {
                $option = $this->getOption((int) $optionId);
                if ($option->getValueId() !== (int) $valueId) {
                    return false;
                }
            } catch (NotFound $e) {
                return false;
            }
        }

        return true;
    }

    /**
     * Vérifie que la déclinaison contient exactement toutes les options passées en paramètre
     *
     * $optionCriteria est un tableau de type array<int, int>
     *
     * Par exemple, avec un "T-Shirt noir XL", la fonction retourne `true` si `$optionCriteria` contient les données
     * correspondant à l'option "Taille" avec la valeur "XL" ET l'option "Couleur" avec la valeur "noir" et que la
     * déclinaison contient le même nombre nombre d'option que `$optionCriteria`.
     */
    public function optionsValuesAre(array $optionCriteria): bool
    {
        if (!$this->optionsValuesContains($optionCriteria)) {
            return false;
        }

        return \count($optionCriteria) === \count($this->getOptions());
    }

    public function getCompany(): CompanySummary
    {
        return CompanySummary::createFromDeclinationReadmodel($this->data);
    }

    /**
     * @return Image[]
     */
    public function getImages(): array
    {
        return array_map(function ($image) {
            return new Image($image['id']);
        }, $this->data['images']);
    }

    public function getMainImage(): ?Image
    {
        $images = $this->getImages();

        if (\count($images) > 0) {
            return reset($images);
        }

        return null;
    }

    public function getCondition(): ProductCondition
    {
        return new ProductCondition($this->data['condition']);
    }

    public function getName(): string
    {
        return $this->data['product_name'];
    }

    public function getShortDescription(): string
    {
        return $this->data['short_description'];
    }

    public function getDescription(): string
    {
        return $this->data['description'];
    }

    public function getShippings(): array
    {
        return $this->data['shippings'];
    }

    public function getCode(): string
    {
        return $this->data['code'] ?? '';
    }

    /**
     * @return CategorySummary[]
     */
    public function getCategoryPath(): array
    {
        return $this->data['category_path'] ?? [];
    }

    public function expose(): array
    {
        return [
            'id' => $this->getId(),
            'productId' => $this->data['product_id'],
            'name' => $this->getName(),
            'isAvailable' => $this->isAvailable(),
            'slug' => $this->data['seo_data']->getSlug(),
            'code' => $this->getCode(),
            'crossedOutPrice' => $this->getCrossedOutPrice() ? $this->getCrossedOutPrice()->getPreciseConvertedAmount() : null,
            'shortDescription' => $this->getShortDescription(),
            'prices' => [
                'priceWithoutVat' => $this->getPriceWithoutTax()->getPreciseConvertedAmount(),
                'priceWithTaxes' => $this->getPriceWithTax()->getPreciseConvertedAmount(),
                'vat' => $this->getTax()->getPreciseConvertedAmount(),
            ],
            'amount' => $this->getAmount(),
            'infiniteStock' => $this->hasInfiniteStock(),
            'affiliateLink' => $this->getAffiliateLink(),
            'mainImage' => $this->getMainImage() ? $this->getMainImage()->toArray() : null,
            'isBrandNew' => $this->getCondition()->equals(ProductCondition::NEW()),
            'options' => array_map(static function (DeclinationOption $option): array {
                return $option->expose();
            }, $this->getOptions()),
            'categoryPath' => array_map(static function (CategorySummary $category): array {
                return $category->expose();
            }, $this->getCategoryPath()),
            'company' => $this->getCompany()->expose(),
        ];
    }
}
