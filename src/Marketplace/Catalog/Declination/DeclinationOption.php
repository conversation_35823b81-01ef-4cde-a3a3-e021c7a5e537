<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Catalog\Declination;

use Wizacha\Marketplace\Image\Image;

class DeclinationOption
{
    /**
     * @var int
     */
    private $id;

    /**
     * @var string
     */
    private $name;

    /**
     * @var int
     */
    private $valueId;

    /**
     * @var string
     */
    private $value;

    /**
     * @var Image|null
     */
    private $image;

    /** @var string|null */
    private $code;

    public function __construct(int $id, string $name, int $valueId, string $value, Image $image = null, string $code = null)
    {
        $this->id = $id;
        $this->name = $name;
        $this->valueId = $valueId;
        $this->value = $value;
        $this->code = $code;
        $this->image = $image;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getValueId(): int
    {
        return $this->valueId;
    }

    public function getValue(): string
    {
        return $this->value;
    }

    public function getImage(): ?Image
    {
        return $this->image;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function expose(): array
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'code' => $this->getCode(),
            'variantId' => $this->getValueId(),
            'variantName' => $this->getValue(),
            'image' => $this->getImage() ? $this->getImage()->toArray() : null,
        ];
    }
}
