<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Catalog\Declination;

use Wizacha\Marketplace\Image\Image;

/**
 * Represents a declination identified with it objectId and its dedicated images
 */
class DeclinationImages
{
    /** @var string */
    private $declinationId;

    /** @var Image[] */
    private $images;

    public function __construct(string $declinationId, array $images)
    {
        $this->declinationId = $declinationId;
        $this->images = $images;
    }

    public function getDeclinationId(): string
    {
        return $this->declinationId;
    }

    /**
     * @return Image[]
     */
    public function getImages(): array
    {
        return $this->images;
    }

    /**
     * Serialize the object to an array.
     */
    public function toArray(): array
    {
        return [
            'declinationId' => $this->getDeclinationId(),
            'images' => array_map(
                function (Image $image) {
                    return $image->toArray();
                },
                $this->getImages()
            ),
        ];
    }
}
