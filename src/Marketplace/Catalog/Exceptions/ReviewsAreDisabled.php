<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Catalog\Exceptions;

use Symfony\Component\HttpFoundation\Response;
use Wizacha\AppBundle\Controller\Api\Error\ApiErrorResponseProvider;
use Wizacha\Marketplace\Exception\ErrorCode;
use Wizacha\AppBundle\Controller\Api\Error\ErrorResponse;

class ReviewsAreDisabled extends \Exception implements ApiErrorResponseProvider
{
    public function toApiErrorResponse(): ErrorResponse
    {
        return new ErrorResponse(ErrorCode::REVIEWS_ARE_DISABLED(), 'reviews are disabled', [], Response::HTTP_NOT_FOUND);
    }
}
