<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Catalog\Exceptions;

use Symfony\Component\HttpFoundation\Response;
use Wizacha\AppBundle\Controller\Api\Error\ApiErrorResponseProvider;
use Wizacha\Marketplace\Exception\ErrorCode;
use Wizacha\AppBundle\Controller\Api\Error\ErrorResponse;

class UserCannotReviewProduct extends \Exception implements ApiErrorResponseProvider
{
    public function toApiErrorResponse(): ErrorResponse
    {
        return new ErrorResponse(ErrorCode::USER_CANNOT_REVIEW_PRODUCT(), 'user cannot review product', [], Response::HTTP_BAD_REQUEST);
    }
}
