<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Catalog\Product;

use DateTimeInterface;
use Wizacha\Marketplace\Catalog\AttributeValue;
use Wizacha\Marketplace\Catalog\Category\CategorySummary;
use Wizacha\Marketplace\Catalog\Company\CompanySummary;
use Wizacha\Marketplace\Catalog\Geolocation;
use Wizacha\Marketplace\Image\Image;
use Wizacha\Marketplace\PIM\TransactionMode\TransactionMode;
use Wizacha\Money\Money;

/**
 * Represents the basic information in a product.
 *
 * That summary is used to show the product as a thumbnail or list item.
 */
interface ProductSummary
{
    /**
     * @return int|string
     */
    public function getProductId();

    public function getName(): string;

    /**
     * Returns the current minimum price of the product.
     *
     * A product can have many different prices with declinations.
     */
    public function getMinimumPrice(): Money;

    /**
     * This is the original price if there is a promotion, or the value of the crossed out price field if set
     */
    public function getCrossedOutPrice(): ?Money;

    /**
     * Whether the product is marked as "available" or not, e.g. whether it has stocks.
     */
    public function isAvailable(): bool;

    /** @return float */
    public function getAverageRating(): float;

    public function getUrl(): string;

    public function getCreatedAt(): DateTimeInterface;

    public function getUpdatedAt(): DateTimeInterface;

    public function getDeclinationCount(): int;

    public function getCode(): string;

    /**
     * @return string|null Returns null if there is no link.
     */
    public function getAffiliateLink(): ?string;

    public function getMainImage(): ?Image;

    /**
     * Very short description sometimes shown below the product name.
     * Current implementation is: 300 first characters of the long description
     */
    public function getSubtitle(): string;

    public function getShortDescription(): string;

    /**
     * Return the "path" of categories (from the root category to the category of the product).
     *
     * @return CategorySummary[]
     */
    public function getCategoryPath(): array;

    /**
     * Slug of the product name, e.g. `my-super-product` (URL friendly).
     */
    public function getSlug(): string;

    /**
     * Path of category slugs, e.g. `root-category/sub-category` (URL friendly).
     *
     * The path may contain any number of sub-levels (separated by `/`).
     */
    public function getCategorySlugPath(): string;

    /**
     * @return AttributeValue[]
     */
    public function getAttributes(): array;

    /**
     * @return ProductCondition[]
     */
    public function getConditions(): array;

    public function getTransactionMode(): TransactionMode;

    /**
     * @return CompanySummary[]
     */
    public function getCompanies(): array;

    public function getGeolocation(): ?Geolocation;

    /**
     * @return Geolocation[]
     */
    public function getGeolocations(): ?array;

    public function getMainDeclinationId(): ?string;

    public function getProductTemplateType(): ?string;

    public function getOffers(): array;

    public function isSubscription(): bool;

    public function isRenewable(): bool;

    public function getQuoteRequestsMinQuantity(): int;

    public function isExclusiveToQuoteRequests(): bool;

    /**
     * @return array[]
     */
    public function getImages(): array;

    /**
     * @return array[]
     */
    public function getDeclinationsImages(): array;
}
