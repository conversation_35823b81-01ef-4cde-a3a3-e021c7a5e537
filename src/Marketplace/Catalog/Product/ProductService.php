<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Catalog\Product;

use Doctrine\DBAL\Connection;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Tygh\Database;
use Wizacha\ArrayUtils;
use Wizacha\Component\Html\HtmlService;
use Wizacha\Core\Iterator\PdoColumnIterator;
use Wizacha\Marketplace\Catalog\AttributesValuesGroup;
use Wizacha\Marketplace\Catalog\AttributeValue;
use Wizacha\Marketplace\Catalog\AttributeVariant;
use Wizacha\Marketplace\Catalog\Category\CategorySummary;
use Wizacha\Marketplace\Catalog\Company\CompanySummary;
use Wizacha\Marketplace\Catalog\Exceptions\MultipleProductsFoundException;
use Wizacha\Marketplace\Catalog\Exceptions\ProductNotFoundException;
use Wizacha\Marketplace\Entities\Declination;
use Wizacha\Marketplace\Exception\InvalidDeclinationException;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Exception\ProductNotFound;
use Wizacha\Marketplace\Image\Image;
use Wizacha\Marketplace\PIM\Attribute\AttributeType;
use Wizacha\Marketplace\PIM\Moderation\ProductWasReported;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct;
use Wizacha\Marketplace\ReadModel\Product;
use Wizacha\Marketplace\PIM\Product\ProductService as PimService;
use Wizacha\Marketplace\ReadModel\ProductRepository;
use Wizacha\Status;
use Wizacha\Storage\StorageService;

/**
 * Service to retrieve catalog products.
 *
 * This service does not manipulate products: this is done in the PIM module, not the catalog.
 */
class ProductService
{
    /** @var ProductRepository */
    private $repository;

    /** @var PimService */
    private $pimService;

    /** @var Connection */
    private $connection;

    /** @var bool */
    private $showOutOfStockProducts;

    /** @var bool */
    private $showZeroPriceProducts;

    /** @var EventDispatcherInterface */
    private $eventDispatcher;

    /** @var string */
    private $httpHost;

    /** @var HtmlService */
    private $htmlService;

    private ?StorageService $imagesStorageService;
    private bool $isQuoteRequestEnabled;

    public function __construct(
        ProductRepository $repository,
        Connection $connection,
        EventDispatcherInterface $eventDispatcher,
        bool $showOutOfStockProducts,
        bool $showZeroPriceProducts,
        string $httpHost,
        HtmlService $htmlService,
        PimService $pimService,
        StorageService $imagesStorageService,
        bool $isQuoteRequestEnabled
    ) {
        $this->repository = $repository;
        $this->showOutOfStockProducts = $showOutOfStockProducts;
        $this->showZeroPriceProducts = $showZeroPriceProducts;
        $this->connection = $connection;
        $this->eventDispatcher = $eventDispatcher;
        $this->httpHost = $httpHost;
        $this->htmlService = $htmlService;
        $this->pimService = $pimService;
        $this->imagesStorageService = $imagesStorageService;
        $this->isQuoteRequestEnabled = $isQuoteRequestEnabled;
    }

    public function getProduct($productId = 0): ?Product
    {
        return $this->repository->find($productId);
    }

    /**
     * @param array $ids
     * @return Product[]
     * @throws \Exception
     */
    public function getProductsByIds(array $ids = []): array
    {
        return $this->repository->findByIds($ids);
    }

    public function getProductByDeclinationId(string $declinationId): ?Product
    {
        $productId = Declination::fromId($declinationId)->getProductId();

        return $this->getProduct($productId);
    }

    /**
     * Return a Declination by ID
     */
    public function getDeclinationById(string $declinationId): array
    {
        $declination = Declination::fromId($declinationId);
        if (false === $declination->isValidCombination()) {
            throw new InvalidDeclinationException($declination);
        }

        $product = $this->getProduct($declination->getProductId());

        if (null === $product) {
            throw NotFound::fromId('Product', $declination->getProductId());
        }

        $declinationsReadModel = $product->getDeclinations();

        $key = array_search($declinationId, array_column($declinationsReadModel, 'objectID'));

        if (false === $key) {
            throw NotFound::fromId('Declination', $declination->getCombinationCode());
        }

        $declinationReadModel = $product->formattedDeclination($declinationsReadModel[$key]);

        $declinationExpose = $this->exposeDeclination($declinationReadModel, $product);
        $declinationExpose['shippings'] = $declinationReadModel['shippings'];
        $declinationExpose['isSubscription'] = $product->isSubscription();
        $declinationExpose['isRenewable'] = $product->isRenewable();

        return $declinationExpose;
    }

    /**
     * @param int $productId
     */
    public function isProductSearchableInFront($productId): bool
    {
        return $this->getSearcheableProductIdInFront([$productId])->valid();
    }

    /**
     * Filter a list of Products to return only those that can be returned by the Catalog API.
     *
     * WARNING!!
     *
     * If you modify some conditions in this function, you must modify them in these 2 following places:
     * - src/Product.php : function frontIds
     * - src/Marketplace/PIM/Product/ProductVisibilityReport.php
     *
     * These 3 places must reflect the same behavior at all time! Else we could be out of step with the real behavior
     *
     * @param array $ids A mix of Product Ids and MVP Ids
     * @param int[] $categoriesIds
     * @param bool $allowMvp
     * @return PdoColumnIterator
     */
    public function getSearcheableProductIdInFront(
        array $ids = [],
        array $categoriesIds = [],
        bool $allowMvp = true
    ): PdoColumnIterator {
        $conditions = [
            $this->getInventoryCondition(),
            "companies.status='A'",
            "products.status='A'",
            "products.approved='Y'",
            "w_cat.status=?s",
        ];
        $params = [
            Status::ENABLED,
        ];

        if (\count($categoriesIds) > 0) {
            $conditions[] = "w_cat.category_id IN (?a)";
            $params[] = array_map('intval', $categoriesIds);
        }

        [$productIds, $multiVendorProductIds] = ArrayUtils::partition($ids, [$this, 'isProductId']);
        $productIds = array_map('intval', $productIds);
        $multiVendorProductIds = array_map('strval', $multiVendorProductIds);

        $conditions[] = "(mvp.id IS NULL OR mvp.status IN (?a))";
        $params[] = MultiVendorProduct::SEARCHABLE_STATUSES;

        $select = true === $allowMvp
            ? "IFNULL(mvp.id, products.product_id)"
            : "products.product_id";

        $hasProductIds = \count($productIds) > 0;
        $hasMultiVendorProductIds = \count($multiVendorProductIds) > 0;

        if (true === $hasProductIds
            || true === $hasMultiVendorProductIds
        ) {
            $unionQueries = [];
            $unionParams = [];

            if (true === $hasProductIds) {
                $unionQueries[] = $this->mainQuery(
                    $select,
                    array_merge(
                        $conditions,
                        ["(products.product_id IN (?a) AND mvp.id IS NULL)"]
                    )
                );
                $unionParams[] = array_merge(
                    $params,
                    [$productIds]
                );
            }
            if (true === $hasMultiVendorProductIds) {
                $unionQueries[] = $this->mainQuery(
                    $select,
                    array_merge(
                        $conditions,
                        ["(mvp.id IN (?a))"]
                    )
                );
                $unionParams[] = array_merge(
                    $params,
                    [$multiVendorProductIds]
                );
            }

            return
                new PdoColumnIterator(
                    Database::prepareArray(
                        implode(' UNION ', $unionQueries),
                        array_merge(...array_values($unionParams))
                    )
                )
            ;
        }

        return
            new PdoColumnIterator(
                Database::prepareArray(
                    $this->mainQuery($select, $conditions),
                    $params
                )
            )
        ;
    }

    /**
     * @param string[] $conditions
     */
    private function mainQuery(
        string $select,
        array $conditions
    ): string {
        $condition = implode(' AND ', $conditions);

        return
            "SELECT
                $select AS catalog_id
            FROM
                ?:products as products
                LEFT JOIN `doctrine_multi_vendor_product_link` AS link ON products.product_id = link.product_id
                LEFT JOIN `doctrine_multi_vendor_product` AS mvp ON link.multi_vendor_product_id = mvp.id
                LEFT JOIN ?:product_prices AS pp ON pp.product_id = products.product_id
                LEFT JOIN ?:product_options_inventory AS poi ON poi.product_id = products.product_id
                INNER JOIN ?:products_categories AS w_prod_cat ON products.product_id = w_prod_cat.product_id AND link_type = 'M'
                INNER JOIN ?:categories AS w_cat ON w_cat.category_id = w_prod_cat.category_id
                INNER JOIN ?:companies AS companies ON companies.company_id = products.company_id
            WHERE
                $condition
            GROUP BY catalog_id
        ";
    }

    /** Retrieve a product ID using a company ID and its supplier reference. */
    public function getCompanyProductBySupplierRef(int $companyId, string $supplierRef): int
    {
        $result = Database::getColumn(
            'SELECT
                DISTINCT p.product_id
            FROM
                ?:products p
            WHERE
                p.company_id = ?i
                AND p.w_supplier_ref = ?a
                AND p.product_id IS NOT NULL
            ',
            $companyId,
            $supplierRef
        );

        if (0 === \count($result)) {
            throw new ProductNotFound($supplierRef);
        }

        if (\count($result) > 1) {
            throw new MultipleProductsFoundException();
        }

        return (int) $result[0];
    }

    /**
     * @param int $productId
     * @param string $declinationId
     * @return array
     * @throws NotFound
     */
    public function getDeclinationOptions(int $productId, string $declinationId)
    {
        $product = $this->getProduct($productId);

        $declinations = $product->getDeclinations();

        foreach ($declinations as $item) {
            if ($item['objectID'] == $declinationId) {
                $declination = $item;

                return $declination['declination'];
            }
        }

        throw NotFound::fromId('Declination', $declinationId);
    }

    /**
     * @param mixed $id
     * @return bool True if $id is a product ID, false if it is a multi-vendor product id.
     */
    public static function isProductId($id): bool
    {
        return is_numeric($id);
    }

    public function reportProduct($productId, string $name, string $email, string $message)
    {
        $product = $this->getProduct($productId);
        if (!$product) {
            throw new ProductNotFound($productId);
        }

        $event = new ProductWasReported($product->getId(), $email, $name, $message, $product->isMultiVendorProduct());
        $this->eventDispatcher->dispatch($event, ProductWasReported::class);
    }

    /**
     * @param string|string[]|null $id A Product Id, or an array of those, or null
     * @param string|string[]|null $code A Product code, or a MVP code, or an array of those, or null
     * @param string|string[]|null $supplierRef A Product supplierRef, or a MVP supplierRef, or an array of those, or null
     * @param string|string[]|null $mvpId A MVP Id, or an array of those, or null
     *
     * @return array A mix of Product Ids and MVP Ids
     */
    public function getProductsIdByFilters(
        $id = null,
        $code = null,
        $supplierRef = null,
        $mvpId = null
    ): array {
        $idList = $this->arrayize($id);
        $codeList = $this->arrayize($code);
        $supplierRefList = $this->arrayize($supplierRef);
        $mvpIdList = $this->arrayize($mvpId);

        if (empty($idList)
            && empty($codeList)
            && empty($supplierRefList)
            && empty($mvpIdList)
        ) {
            return [];
        }

        foreach ($idList as $id) {
            if (is_numeric($id) === false) {
                throw new \UnexpectedValueException('Expected a numeric, got [' . $id . ']');
            }
        }
        foreach ($mvpIdList as $mvpId) {
            if (\Rhumsaa\Uuid\Uuid::isValid($mvpId) === false) {
                throw new \UnexpectedValueException('Expected a UUID, got [' . $mvpId . ']');
            }
        }

        $idList = array_map('intval', $idList);

        $result = array_merge(
            $this->getRealProductsIdByFilters($idList, $codeList, $supplierRefList),
            $this->getMvpIdFromRealProductId($idList),
            $this->getMvpIdByFilters($codeList, $supplierRefList),
            $mvpIdList
        );

        return array_unique($result);
    }

    public function isUpToDate(string $productId): bool
    {
        $product = $this->getProduct($productId);
        $updateTimeProductCatalog = $product->getUpdatedAt()->getTimestamp();

        $updateTimeProductPim = $this->pimService->get($productId)->getUpdatedTimestamp();

        if ($updateTimeProductPim > $updateTimeProductCatalog) {
            return false;
        }

        return true;
    }

    public function getIsUpToDateAndLastUpdate(string $productId): ProductUpToDateMessage
    {
        $product = $this->getProduct($productId);

        if ($product instanceof Product === false) {
            // No exception: it may happen when the readmodel is not already created (async)
            return new ProductUpToDateMessage(true, '', []);
        }

        $updateTimeProductPim = new \DateTime();
        $updateTimeProductPim->setTimestamp($this->pimService->get($productId)->getUpdatedTimestamp());

        $updateTimeProductCatalog = new \DateTime();
        $updateTimeProductCatalog->setTimestamp($product->getUpdatedAt()->getTimestamp());

        return $this->compareDates($updateTimeProductPim, $updateTimeProductCatalog);
    }

    public function compareDates(
        \DateTime $pimLastUpdate,
        \DateTime $catalogLastUpdate
    ): ?ProductUpToDateMessage {
        $isUpToDate = true;
        $message = '';
        $messageData = [];

        $lastMinute = new \DateTime();
        $lastMinute->sub(new \DateInterval('PT1M'));

        // If there is more than 1 minute the Pim has been updated
        // considers the catalog update still pending
        if ($pimLastUpdate > $catalogLastUpdate
            && $pimLastUpdate <= $lastMinute
        ) {
            $isUpToDate = false;

            $interval = date_diff($pimLastUpdate, $lastMinute);
            // More than 24h, display the date and time
            if ($interval->d > 0 || $interval->m > 0 || $interval->y > 0) {
                $message = 'datetime';
                $messageData = [
                    'date' => $pimLastUpdate->format('Y-m-d'),
                    'time' => $pimLastUpdate->format('H:i'),
                ];
            } elseif ($interval->h > 0 && $interval->i > 0) {
                // More than 60 minutes display the number of the hours,
                // rounded to superior
                $message = 'hours';
                $messageData = ['hours' => $interval->h + 1];
            } elseif ($interval->h > 0 && $interval->i == 0) {
                // More than 60 minutes display the number of the hours
                $message = 'hours';
                $messageData = ['hours' => $interval->h];
            } elseif ($interval->i > 0 || $interval->s > 0) {
                // Less than an hour, display the number of minutes
                $message = 'minutes';
                $messageData = ['minutes' => $interval->i > 0 ? $interval->i : 1];
            } else {
                $message = 'datetime';
                $messageData = [
                    'date' => $pimLastUpdate->format('Y-m-d'),
                    'time' => $pimLastUpdate->format('H:i')
                ];
            }
        }

        return new ProductUpToDateMessage($isUpToDate, $message, $messageData);
    }

    public function expose(Product $readModel, array $features = []): array
    {
        $outProduct = $this->exposeCommon($readModel, $features);

        if (false === $readModel->isMultiVendorProduct()) {
            $outProduct['relatedOffers'] = $this->exposeRelatedOffers($readModel);
        } else {
            $outProduct['relatedOffers'] = $this->exposeRelatedOffers($readModel, true);
        }

        return $outProduct;
    }

    public function exposeFull(Product $readModel, array $features = []): array
    {
        $outProduct = $this->exposeCommon($readModel, $features);

        if (false === $readModel->isMultiVendorProduct()) {
            $outProduct['relatedOffers'] = $this->exposeFullRelatedOffers($readModel);
        } else {
            $outProduct['relatedOffers'] = $this->exposeFullRelatedOffers($readModel, true);
        }

        return $outProduct;
    }

    /** @return mixed[] */
    private function exposeCommon(Product $readModel, array $features = []): array
    {
        $outProduct = [];
        $declinations = $readModel->getData();

        $outProduct['id'] = $readModel->getId();
        $outProduct['code'] = $readModel->getCode();
        $outProduct['supplierReference'] = $readModel->getSupplierReference();
        $outProduct['name'] = $readModel->getName();
        $outProduct['url'] = $this->getAbsoluteUrl($readModel->getUrl());
        $outProduct['shortDescription'] = $this->htmlService->transformImagesToAbsoluteUrl($readModel->getShortDescription());
        $outProduct['description'] = $this->htmlService->transformImagesToAbsoluteUrl($readModel->getDescription());
        $outProduct['slug'] = $readModel->getSeoData()->getSlug();
        $outProduct['minPrice'] = $readModel->getCurrentPrice()->getConvertedAmount();
        $outProduct['maxPriceAdjustment'] = $readModel->getMaxPriceAdjustment();

        $outProduct['greenTax'] = $readModel->getGreenTax()->getConvertedAmount();
        $outProduct['isTransactional'] = $readModel->isTransactional();
        $outProduct['weight'] = $readModel->getWeight();
        // Deprecated
        $outProduct['averageRating'] = (int) round($readModel->getAverageRating());
        $outProduct['averageRatingFloat'] = $readModel->getAverageRating();
        $outProduct['shippings'] = array_map(function ($shipping) {
            $image = $shipping['image']['path'] ? $this->imagesStorageService->getUrl($shipping['image']['path']) : null;

            return [
                'shipping_id' => $shipping['shipping_id'],
                'name' => $shipping['name'],
                'image' => $image,
                'firstRate' => $shipping['first_rate'],
                'nextRate' => $shipping['next_rate'],
                'position' => $shipping['position'],
                'deliveryTime' => $shipping['delivery_time'],
                'carriagePaidThreshold' => $shipping['carriagePaidThreshold'],
            ];
        }, array_values($readModel->getShippings()));
        $outProduct['companies'] = array_map(function (CompanySummary $company) {
            return $company->expose();
        }, $readModel->getCompanies());
        $outProduct['categoryPath'] = array_map(function (CategorySummary $category) {
            return $category->expose();
        }, $readModel->getCategoryPath());
        $outProduct['geolocation'] = $readModel->getGeolocation() ? $readModel->getGeolocation()->expose() : null;
        $outProduct['createdAt'] = $readModel->getCreatedAt()->format(\DateTime::RFC3339);
        $outProduct['updatedAt'] = $readModel->getUpdatedAt()->format(\DateTime::RFC3339);
        $outProduct['availableSince'] = $readModel->getAvailabilityDate() instanceof \DateTimeInterface ? $readModel->getAvailabilityDate()->format(\DateTime::RFC3339) : null;
        $outProduct['infiniteStock'] = $readModel->hasInfiniteStock();
        $outProduct['video'] = null;
        if ($video = $readModel->getVideo()) {
            $outProduct['video'] = [
                'thumbnailUrl' => $video['thumb'],
                'videoUrl' => $video['path'],
            ];
        }
        $outProduct['seoData'] = [
            'title' => $readModel->getSeoData()->getTitle(),
            'description' => $readModel->getSeoData()->getDescription(),
            'keywords' => $readModel->getSeoData()->getKeywords(),
        ];

        foreach ($declinations as $declination) {
            $outProduct['declinations'][] = $this->exposeDeclination($declination, $readModel);
        }

        $outProduct['offers'] = $readModel->getOffers();

        // Transformation des attributs contenus dans le readmodel afin
        // qu'ils soient au format legacy d'exposition..
        $transformedGroupedAttributes = array_map([$this, 'exposeGroupedAttribute'], $readModel->getGroupedAttributesValues());
        $transformedUngroupedAttributes = array_map([$this, 'exposeAttribute'], $readModel->getUngroupedAttributesValues());
        $outProduct['attributes'] = array_merge($transformedGroupedAttributes, $transformedUngroupedAttributes);

        $options = $readModel->getDeclinationOptions();

        $outProduct['options'] = [];
        foreach ($options as $id => $option) {
            $newOption = [
                'id' => $id,
                'name' => $option['name'],
                'position' => $option['option_position'],
                'code' => $option['code'],
            ];

            foreach ($option['values'] as $variantId => $variantName) {
                $imageId = $option['images'][$variantId];

                $newOption['variants'][] = [
                    'id' => $variantId,
                    'name' => $variantName,
                    'image' => $imageId ? (new Image($imageId))->toArray() : null,
                    'position' => $option['positions'][$variantId],
                ];
            }
            $outProduct['options'][] = $newOption;
        }

        $outProduct['attachments'] = $readModel->getAttachments();

        $outProduct['images'] = array_map(
            function ($image) {
                return [
                    'id' => $image['id'],
                    'altText' => $image['altText']
                ];
            },
            $readModel->getImages()
        );

        $outProduct['productTemplateType'] = $readModel->getProductTemplateType();

        if (\count($features) === 0) {
            $outProduct['features'] = array_values(fn_get_product_features_list([
                'product_id' => $readModel->getProductId(),
            ]));
        } else {
            $outProduct['features'] = \is_array($features[$readModel->getProductId()]) ? array_values($features[$readModel->getProductId()]) : [];
        }

        $outProduct['isSubscription'] = $readModel->isSubscription();
        $outProduct['isRenewable'] = $readModel->isRenewable();

        if (true === $this->isQuoteRequestEnabled) {
            $outProduct['quoteRequestsMinQuantity'] = $readModel->getQuoteRequestsMinQuantity();
            $outProduct['isExclusiveToQuoteRequests'] = $readModel->isExclusiveToQuoteRequests();
        }

        return $outProduct;
    }

    /** @return mixed[] */
    private function exposeRelatedOffers(Product $readModel, $forMvp = false): array
    {
        return array_map(function ($related) use ($forMvp): array {
            $element = [
                'type' => $related['type'],
                'productId' => $related['productId'],
                'description' => $related['description'],
                'extra' => $related['extra'],
                'name' => $related['name'],
                'status' => $related['status'],
            ];

            if (true === $forMvp) {
                $element = array_merge(
                    ['parentProductId' => $related['parentProductId']],
                    $element
                );
            }

            return $element;
        }, $readModel->getRelatedOffers());
    }

    /** @return mixed[] */
    private function exposeFullRelatedOffers(Product $readModel, bool $forMvp = false): array
    {
        return array_map(function ($related) use ($forMvp): array {
            $element = [
                'type' => $related['type'],
                'productId' => $related['productId'],
                'description' => $related['description'],
                'extra' => $related['extra'],
                'name' => $related['name'],
                'status' => $related['status'],
                'url' => $related['url'],
                'minPrice' => $related['minPrice'],
                'code' => $related['code'],
                'supplierReference' => $related['supplierReference'],
                'images' => $related['images'],
                'company' => $related['company'],
            ];

            if (true === $forMvp) {
                $element = array_merge(
                    ['parentProductId' => $related['parentProductId']],
                    $element
                );
            }

            return $element;
        }, $readModel->getRelatedOffers());
    }

    private function getAbsoluteUrl(string $relativeUrl): string
    {
        if (strpos($relativeUrl, 'http') === 0) {
            return $relativeUrl;
        }

        return (\defined('HTTPS') ? 'https' : 'http') . '://' . $this->httpHost . $relativeUrl;
    }

    private function exposeGroupedAttribute(AttributesValuesGroup $attribute): array
    {
        $exposedChildren = array_map(function (AttributeValue $child) {
            return $this->exposeAttribute($child);
        }, $attribute->getAttributesValues());

        return [
            'id' => $attribute->getGroupAttribute()->getId(),
            'name' => $attribute->getName(),
            'values' => null,
            'value' => null, // Deprecated field : will be removed in next major version.
            'valueIds' => [],  // Deprecated field : will be removed in next major version.
            'children' => $exposedChildren,
            'subfeatures' => $exposedChildren,
            'imageUrls' => [],  // Deprecated field : will be removed in next major version.
            'type' => AttributeType::GROUP()->getKey(),
        ];
    }

    private function exposeAttribute(AttributeValue $attribute): array
    {
        $exposed = [
            'id' => $attribute->getAttribute()->getId(),
            'name' => $attribute->getAttribute()->getName(),
            'code' => $attribute->getAttribute()->getCode(),
            'values' => array_map(function (AttributeVariant $value) {
                return $value->expose();
            }, $attribute->getValues()),
            // Deprecated field : will be removed in next major version.
            'value' => array_map(function (AttributeVariant $value) {
                return $value->getName();
            }, $attribute->getValues()),
            // Deprecated field : will be removed in next major version.
            'valueIds' => array_filter(array_map(function (AttributeVariant $value) {
                return $value->getId();
            }, $attribute->getValues())),
            'children' => [],
            'subfeatures' => [],
            // Deprecated field : will be removed in next major version.
            'imageUrls' => [],
            'type' => $attribute->getAttribute()->getType()->getKey(),
            'isUsedForRecommendations' => $attribute->getAttribute()->isUsedForRecommendations(),
        ];

        // PATCH : on aplati le contenu de la valeur pour le type "free_text"
        // Cela vient de ce qui est retourné dans l'API legacy
        // Deprecated behavior : will be removed in next major version.
        if ($attribute->getAttribute()->getType()->equals(AttributeType::FREE_TEXT())) {
            if (\count($exposed['value'])) {
                $exposed['value'] = reset($exposed['value']);
            } else {
                $exposed['value'] = '';
            }
        }

        return $exposed;
    }

    private function exposeDeclination(array $declination, Product $product): array
    {
        $outDeclination['id'] = $declination['objectID'];
        $outDeclination['code'] = $declination['code'] ?: '';
        $outDeclination['isAvailable'] = !$declination['unavailable'];
        $outDeclination['supplierReference'] = \strlen($declination['supplierReference']) === 0 ?  $product->getSupplierReference() : $declination['supplierReference'];
        $outDeclination['price'] = (float) $declination['final_price'];
        $outDeclination['maxPriceAdjustment'] = $product->getMaxPriceAdjustment();
        $outDeclination['originalPrice'] = (float) $declination['price'];
        $outDeclination['crossedOutPrice'] = $declination['crossed_out_price'] !== null ? (float) $declination['crossed_out_price'] : null;
        $outDeclination['prices'] = [
            'priceWithTaxes' => $declination['prices']['priceWithTaxes'],
            'priceWithoutVat' => $declination['prices']['priceWithoutVat'],
            'vat' => $declination['prices']['vat'],
        ];
        $outDeclination['greenTax'] = $declination['green_tax'];
        $outDeclination['amount'] = (int) $declination['amount'];
        $outDeclination['infiniteStock'] = $declination['infinite_stock'];
        $outDeclination['affiliateLink'] = $declination['affiliate_link'] ?: null;
        $outDeclination['images'] = array_map(
            function ($image) {
                return [
                    'id' => $image['id'],
                    'altText' => $image['altText']
                ];
            },
            $declination['images']
        );
        $outDeclination['options'] = array_map(function (array $option): array {
            return [
                'id' => $option['option_id'],
                'name' => $option['option_name'],
                'code' => $option['code'],
                'variantId' => $option['value_id'],
                'variantName' => $option['value_name'],
                'image' => $option['image_id'] ? (new Image((int) $option['image_id']))->toArray() : null,
                'position' => \intval($option['position']),
            ];
        }, array_values($declination['declination']));

        $outDeclination['isBrandNew'] = $declination['condition'] === ProductCondition::NEW;
        $outDeclination['company'] = CompanySummary::createFromDeclinationReadmodel($declination)->expose();

        // We check if we have PRICETIERS for retro compatibility
        // In the absence of priceTiers, we will build it manually
        if (\array_key_exists('price_tiers', $declination) === false) {
            $declination['price_tiers'] = container()
                ->get('marketplace.price_tier.price_tier_service')
                ->buildAndExposePriceTiersWithTaxesForReadModel(
                    $declination['product_id'],
                    fn_combination_from_product_code($declination['code'], $declination['product_id'])
                )
            ;
        }

        $outDeclination['priceTiers'] = $declination['price_tiers'];

        return $outDeclination;
    }

    /**
     * Turn any argument into an array
     *
     * @param mixed|null $data Any data
     * @return array An empty array if $data is null, an array containing $data if it is a scalar, $data otherwise
     */
    private function arrayize($data = null): array
    {
        if (empty($data)) {
            return [];
        }

        if (\is_array($data)) {
            return $data;
        }

        return [$data];
    }

    private function getInventoryCondition(): string
    {
        $priceOperator = ($this->showZeroPriceProducts === true) ? '>=' : '>';

        if ($this->showOutOfStockProducts) {
            return Database::quote(
                "IF(products.tracking = ?s, poi.w_price $priceOperator 0, pp.price $priceOperator 0)",
                \Wizacha\Product::TRACKING_TYPE_INVENTORY
            );
        }

        return Database::quote(
            "IF(products.tracking = ?s,
                (poi.w_price $priceOperator 0 AND (products.infinite_stock OR poi.amount > 0)),
                (pp.price $priceOperator 0 AND (products.infinite_stock OR products.amount > 0))
             )",
            \Wizacha\Product::TRACKING_TYPE_INVENTORY
        );
    }

    /**
     * Find a list of non-MVP Products according to conditions on id, code and supplierReference on these Products
     *
     * @param int[] $idList
     * @param string[] $codeList
     * @param string[] $supplierRefList
     *
     * @return int[] An array of (real, non-MVP) Product Ids
     */
    private function getRealProductsIdByFilters(array $idList, array $codeList, array $supplierRefList): array
    {
        if (empty($idList)
            && empty($codeList)
            && empty($supplierRefList)
        ) {
            return [];
        }

        $sql = '
            SELECT DISTINCT p.product_id
            FROM ?:products p
            LEFT JOIN ?:product_options_inventory poi
            ON poi.product_id = p.product_id
            WHERE p.product_id IS NOT NULL
        ';

        $conditions = [];
        $params = [];

        if (\count($idList) > 0) {
            $conditions[] = '(p.product_id IN (?a))';
            $params[] = $idList;
        }

        if (\count($codeList) > 0) {
            $conditions[] = '(p.product_code IN (?a) OR poi.product_code IN (?a))';
            $params[] = $codeList;
            $params[] = $codeList;
        }

        if (\count($supplierRefList) > 0) {
            $conditions[] = '(p.w_supplier_ref IN (?a) OR poi.supplier_reference IN (?a))';
            $params[] = $supplierRefList;
            $params[] = $supplierRefList;
        }

        $condition = implode(' OR ', $conditions);
        $query = \count($conditions) > 0 ? "$sql AND ($condition)" : $sql;

        return Database::getColumn($query, ...$params);
    }

    /**
     * Return the list of all the MVP linked to a list of Products
     *
     * @param int[] $idList
     * @return string[] An array of MVP Ids
     */
    private function getMvpIdFromRealProductId(array $idList): array
    {
        if (empty($idList)) {
            return [];
        }

        $sql = '
            SELECT link.multi_vendor_product_id
            FROM doctrine_multi_vendor_product_link as link
            WHERE link.product_id IN (?a)
        ';

        return Database::getColumn($sql, $idList);
    }

    /**
     * Find a list of MVP according to conditions on code and supplierReference on these MVP
     *
     * @param string[] $codeList
     * @param string[] $supplierRefList
     * @return string[] An array of MVP Ids
     */
    private function getMvpIdByFilters(array $codeList, array $supplierRefList): array
    {
        if (empty($codeList)
            && empty($supplierRefList)
        ) {
            return [];
        }

        $sql = '
            SELECT mvp.id
            FROM doctrine_multi_vendor_product as mvp
            WHERE mvp.id IS NOT NULL
        ';

        $conditions = [];
        $params = [];

        if (\count($codeList) > 0) {
            $conditions[] = '(mvp.code IN (?a))';
            $params[] = $codeList;
        }

        if (\count($supplierRefList) > 0) {
            $conditions[] = '(mvp.supplier_reference IN (?a))';
            $params[] = $supplierRefList;
        }

        $condition = implode(' OR ', $conditions);
        $query = \count($conditions) > 0 ? "$sql AND ($condition)" : $sql;

        return Database::getColumn($query, ...$params);
    }
}
