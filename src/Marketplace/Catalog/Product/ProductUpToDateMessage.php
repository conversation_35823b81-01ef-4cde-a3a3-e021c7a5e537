<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Catalog\Product;

class ProductUpToDateMessage
{
    public bool $isUpToDate;
    public string $message = '';
    public array $messageData = [];

    public function __construct(
        bool $isUpToDate,
        string $message,
        array $messageData
    ) {
        $this->isUpToDate = $isUpToDate;
        $this->message = $message;
        $this->messageData = $messageData;
    }
}
