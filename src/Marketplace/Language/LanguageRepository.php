<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Language;

use Doctrine\DBAL\Connection;

class LanguageRepository
{
    private Connection $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
    }

    /** @return string[]|null */
    public function findByLangCode(string $langCode): ?array
    {
        $getStatement = $this->connection->prepare("SELECT * FROM cscart_languages WHERE lang_code = :langCode");
        $getStatement->execute(
            [
                'langCode' => $langCode
            ]
        );

        $language = $getStatement->fetchAssociative();

        if ($language === false) {
            return null;
        }

        return $language;
    }

    public function getConnection(): Connection
    {
        return $this->connection;
    }
}
