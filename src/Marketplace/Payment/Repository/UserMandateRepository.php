<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Wizacha\Marketplace\Payment\UserMandate;

class UserMandateRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, UserMandate::class);
    }

    public function save(UserMandate $userMandate): UserMandate
    {
        $this->getEntityManager()->persist($userMandate);
        $this->getEntityManager()->flush();

        return $userMandate;
    }
}
