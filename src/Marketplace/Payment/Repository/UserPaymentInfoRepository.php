<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Wizacha\Marketplace\Payment\UserPaymentInfo;

class UserPaymentInfoRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, UserPaymentInfo::class);
    }

    public function save(UserPaymentInfo $userPaymentInfo): UserPaymentInfo
    {
        $this->getEntityManager()->persist($userPaymentInfo);
        $this->getEntityManager()->flush();

        return $userPaymentInfo;
    }

    /**
     * @return UserPaymentInfo[]
     */
    public function findUserWithStripeMandate(): array
    {
        return $this->createQueryBuilder('u')->where('u.stripeBankAccountToken IS NOT NULL')->getQuery()->getResult();
    }
}
