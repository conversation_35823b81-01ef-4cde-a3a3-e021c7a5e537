<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment;

use Wizacha\Marketplace\Payment\Repository\UserMandateRepository;
use Wizacha\Marketplace\Payment\Repository\UserPaymentInfoRepository;

class MandateService
{
    private UserPaymentInfoRepository $userPaymentInfoRepository;

    private UserMandateRepository $userMandateRepository;

    public function __construct(
        UserPaymentInfoRepository $userPaymentInfoRepository,
        UserMandateRepository $userMandateRepository
    ) {
        $this->userPaymentInfoRepository = $userPaymentInfoRepository;
        $this->userMandateRepository = $userMandateRepository;
    }

    public function saveMandate(UserMandate $userMandate): UserMandate
    {
        return $this->userMandateRepository->save($userMandate);
    }

    public function saveUserInfo(UserPaymentInfo $userPaymentInfo): UserPaymentInfo
    {
        return $this->userPaymentInfoRepository->save($userPaymentInfo);
    }

    public function findOneMandateBy(array $array): ?UserMandate
    {
        return $this->userMandateRepository->findOneBy($array);
    }

    public function findOneUserInfoBy(array $array): ?UserPaymentInfo
    {
        return $this->userPaymentInfoRepository->findOneBy($array);
    }
}
