<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment;

use MyCLabs\Enum\Enum;
use Wizacha\Marketplace\Payment\Exception\UnsupportedApiException;
use Wizacha\Marketplace\Payment\Processor\HiPay;
use Wizacha\Marketplace\Payment\Processor\LemonWay;
use Wizacha\Marketplace\Payment\Processor\MangoPay;
use Wizacha\Marketplace\Payment\Processor\NoPayment;
use Wizacha\Marketplace\Payment\Processor\ManualPayment;
use Wizacha\Marketplace\Payment\Processor\SMoney;
use Wizacha\Marketplace\Payment\Processor\Stripe;

/**
 * @method static PaymentProcessorName HIPAY()
 * @method static PaymentProcessorName LEMONWAY()
 * @method static PaymentProcessorName MANGOPAY()
 * @method static PaymentProcessorName SMONEY()
 * @method static PaymentProcessorName STRIPE()
 * @method static PaymentProcessorName NONE()
 * @method static PaymentProcessorName MANUAL()
 */
class PaymentProcessorName extends Enum
{
    private const HIPAY = 'hipay';
    private const LEMONWAY = 'lemonway';
    private const MANGOPAY = 'mangopay';
    private const SMONEY = 'smoney';
    private const STRIPE = 'stripe';
    private const NONE = 'none';
    private const MANUAL = 'nopayment';

    public function getServiceClassName(): string
    {
        switch ($this->getValue()) {
            case static::NONE()->getValue():
            case static::MANUAL()->getValue():
                return NoPayment::class;
            case static::LEMONWAY()->getValue():
                return LemonWay::class;
            case static::MANGOPAY()->getValue():
                return MangoPay::class;
            case static::STRIPE()->getValue():
                return Stripe::class;
            case static::HIPAY()->getValue():
                return HiPay::class;
            case static::SMONEY()->getValue():
                return SMoney::class;
            default:
                throw new UnsupportedApiException();
        }
    }
}
