<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment;

use Doctrine\DBAL\Connection;
use Wizacha\Marketplace\GlobalState\GlobalState;

class PaymentRepository
{
    /** @var Connection */
    private $connection;

    public function __construct(Connection $connexion)
    {
        $this->connection = $connexion;
    }

    /** @return mixed[] */
    public function getActivePayments(): array
    {
        $query = $this->connection->prepare(
            'SELECT
                   p.payment_id, p.position, p.status, p.external_reference, p.processor_id,
                   d.payment, d.description,
                   i.image_id, cd.description as altText
            FROM cscart_payments AS p
            INNER JOIN cscart_payment_descriptions AS d
                ON p.payment_id = d.payment_id
                AND d.lang_code = :langCode
            LEFT JOIN cscart_images_links AS i
                ON CAST(p.payment_id AS CHAR) = i.object_id
                AND i.object_type = "payment"
            LEFT JOIN cscart_common_descriptions AS cd
                ON i.image_id = cd.object_id
                AND cd.object_holder = "images"
                AND cd.lang_code = :langCode
            WHERE p.status = "A"
            ORDER BY p.position, d.payment'
        );

        $query->execute(
            [
                'langCode' => (string) GlobalState::interfaceLocale()
            ]
        );

        return $query->fetchAllAssociative();
    }
}
