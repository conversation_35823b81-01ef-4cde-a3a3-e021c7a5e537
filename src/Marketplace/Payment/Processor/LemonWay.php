<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Processor;

use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Routing\RouterInterface;
use Wizacha\AppBundle\Controller\Api\Error\ApiErrorException;
use Wizacha\AppBundle\Validator\UrlValidatorInterface;
use Wizacha\Marketplace\Basket\Exception\MissingPaymentException;
use Wizacha\Marketplace\Company\CompanyStatus;
use Wizacha\Marketplace\Country\CountryService;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Order\Exception\FieldsValidationException;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderPricesInterface;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\Refund\Entity\Refund;
use Wizacha\Marketplace\Order\Token\TokenService;
use Wizacha\Marketplace\Payment\AgreementInterface;
use Wizacha\Marketplace\Payment\ApiAwareTrait;
use Wizacha\Marketplace\Payment\Exception\RefundErrorFromPspException;
use Wizacha\Marketplace\Payment\Exception\SepaMandateNotCreatedException;
use Wizacha\Marketplace\Payment\Exception\SepaMandateNotSignedException;
use Wizacha\Marketplace\Payment\LemonWay\Exception\KycLevelTooHigh;
use Wizacha\Marketplace\Payment\LemonWay\Exception\LemonWayException;
use Wizacha\Marketplace\Payment\LemonWay\Exception\LemonWayWalletRequestFailed;
use Wizacha\Marketplace\Payment\LemonWay\LemonWayApi;
use Wizacha\Marketplace\Payment\LemonWay\LemonWayConfig;
use Wizacha\Marketplace\Payment\LemonWay\LemonWayTransactionDetails;
use Wizacha\Marketplace\Payment\LemonWay\LemonWayTransactionStatus;
use Wizacha\Marketplace\Payment\LemonWay\LemonWayWalletStatus;
use Wizacha\Marketplace\Payment\MandateService;
use Wizacha\Marketplace\Payment\MandateStatus;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\PaymentType;
use Wizacha\Marketplace\Payment\TransactionDetails;
use Wizacha\Marketplace\Payment\UserMandate;
use Wizacha\Marketplace\Payment\UserPaymentInfo;
use Wizacha\Marketplace\Payment\UserPaymentInfoService;
use Wizacha\Marketplace\Transaction\Exception\TransactionNotFound;
use Wizacha\Marketplace\Transaction\Transaction;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionStatus;
use Wizacha\Marketplace\Transaction\TransactionsTransferEventType;
use Wizacha\Marketplace\Transaction\TransactionTransferEvent;
use Wizacha\Marketplace\Transaction\TransactionType;
use Wizacha\Marketplace\User\User;
use Wizacha\Marketplace\User\UserRepository;
use Wizacha\Marketplace\User\UserService;
use Wizacha\Marketplace\User\UserType;
use Wizacha\Money\Money;
use Wizacha\Order as WizachaOrder;

class LemonWay extends AbstractProcessor implements
    AgreementInterface,
    BankwireProcessorInterface,
    RefundProcessorInterface,
    RefundMarketplaceDiscountProcessorInterface
{
    use ApiAwareTrait;
    use RefundableProcessorTrait;

    /** @var LemonWayApi */
    protected $api;

    /** @var LemonWayConfig */
    protected $config;

    /** @var LoggerInterface */
    protected $logger;

    /** @var RouterInterface */
    protected $router;

    /** @var UserRepository */
    protected $userRepository;

    /** @var CountryService */
    protected $countryService;

    /** @var TransactionService */
    protected $transactionService;

    /** @var TokenService */
    private $orderTokenService;

    private EventDispatcherInterface $eventDispatcher;

    private MandateService $mandateService;

    protected UserPaymentInfoService $userPaymentInfoService;

    private OrderService $orderService;

    private UrlValidatorInterface $urlValidator;

    private UserService $userService;

    public function __construct(
        LemonWayApi $lemonWayApi,
        LemonWayConfig $config,
        LoggerInterface $logger,
        RouterInterface $router,
        UserRepository $userRepository,
        CountryService $countryService,
        TransactionService $transactionService,
        TokenService $orderTokenService,
        EventDispatcherInterface $eventDispatcher,
        MandateService $mandateService,
        UserPaymentInfoService $userPaymentInfoService,
        OrderService $orderService,
        UserService $userService
    ) {
        $this->api = $lemonWayApi;
        $this->config = $config;
        $this->logger = $logger;
        $this->router = $router;
        $this->userRepository = $userRepository;
        $this->countryService = $countryService;
        $this->transactionService = $transactionService;
        $this->orderTokenService = $orderTokenService;
        $this->mandateService = $mandateService;
        $this->userPaymentInfoService = $userPaymentInfoService;
        $this->orderService = $orderService;
        $this->eventDispatcher = $eventDispatcher;
        $this->userService = $userService;
    }

    public function isConfigured(): bool
    {
        return $this->api->isConfigured();
    }

    public function getConfig(): LemonWayConfig
    {
        return $this->config;
    }

    public function getLemonwayWebKitUrl(): ?string
    {
        return $this->api->getWebKitUrl();
    }

    public function getOrderTokenService(): TokenService
    {
        return $this->orderTokenService;
    }

    public function setOrderTokenService(TokenService $orderTokenService): self
    {
        $this->orderTokenService = $orderTokenService;

        return $this;
    }

    public function createSepaTransaction(int $orderId): AbstractProcessor
    {
        $order = new WizachaOrder($orderId);
        $orders = $this->orderService->getChildOrders($orderId);
        $user = $this->userRepository->get($order->getUserId());
        $mandate = $this->getMandate($user->getUserId());

        if ($mandate === null) {
            throw new SepaMandateNotCreatedException(
                "We tried to create a SEPA transaction for the order $orderId but the user doesn't have a Mandate"
            );
        }

        //Creation of a technical wallet
        $wallet = $this->getTechnicalUserWallet($user, true);

        if ($this->sepaMandateIsEnabled($wallet, $mandate) === false) {
            throw new SepaMandateNotSignedException(
                "We tried to create a SEPA transaction for the order $orderId but the user doesn't have validate Mandate"
            );
        }

        $data = [
            'amountTot' => $this->formatPrice($order->getCustomerTotal()->getConvertedAmount()),
            'autoCommission' => 0,
            'collectionDate' => '',
            'comment' => $this->generateComment($order),
        ];

        //Create and save the mandate on lemonway and our DB
        $data['wallet'] = $wallet->ID;
        $data['sddMandateId'] = $mandate->getAgreementId();

        $response = $this->moneyInSddInit($data);

        if ($response->HPAY->STATUS === "0") {
            $this->orderTokenService->createToken($order->getId());

            foreach ($orders as $order) {
                $this->saveDirectDebitTransaction($order, $response->HPAY->ID, [LemonWayConfig::LEMONWAY_SEPA => true]);
            }
        }

        return $this;
    }

    public function createCardTransaction(int $orderId, string $redirectUrl = null, string $cssUrl = null, bool $isCaptured = true): string
    {
        if (\is_string($redirectUrl)) {
            $redirectUrl = base64_encode($redirectUrl);
        }

        $order = new WizachaOrder($orderId);
        $orders = $order->isParentOrder() ? $order->getSubOrders() : [$order];

        $callbackUrl = $this->router->generate(
            'payment_notification_lemonway_card',
            [
                'payment_redirect_url' => $redirectUrl,
                'utm_nooverride' => '1',
            ],
            RouterInterface::ABSOLUTE_URL
        );

        $wallet = $this->getMoneyInWallet(
            $this->userRepository->get($order->getUserId())
        );

        $this->sendKyc($wallet, $order->getCompany());

        $moneyIn = $this->api->moneyInWebInit([
            'wallet' => $wallet->ID,
            'amountTot' => $this->formatPrice($order->getCustomerTotal()->getConvertedAmount()),
            'wkToken' => $this->api->generateToken(),
            'returnUrl' => urlencode($callbackUrl),
            'errorUrl' => urlencode($callbackUrl),
            'cancelUrl' => urlencode($callbackUrl),
            'amountCom' => $this->formatPrice(0.),
            'comment' => $this->generateComment($order),
            'autoCommission' => "0",
            'isPreAuth' => $order->hasMarketplaceDiscount() ? '1' : '0',
            'delayedDays' => $order->hasMarketplaceDiscount() ? LemonWayApi::DELAYED_DAYS_MIN : '0',
        ]);

        foreach ($orders as $order) {
            $this->saveCreditCardTransaction($order, (string) $moneyIn->ID, ['credited_wallet' => $wallet->ID]);
        }

        return $this->api->generatePaymentUrl($moneyIn, $cssUrl);
    }

    public function createBankwireTransaction(int $orderId, string $redirectUrl = null): array
    {
        $this->getConfig()->assertBankWireIsConfigured();

        if (\is_string($redirectUrl)) {
            $redirectUrl = base64_encode($redirectUrl);
        }

        $order = new WizachaOrder($orderId);
        $orders = $order->isParentOrder() ? $order->getSubOrders() : [$order];
        $user = $this->userRepository->get($order->getUserId());

        $wallet = $this->getMoneyInWallet($user);

        $completeUrl = $this->router->generate(
            'payment_notification_transfer_standby',
            [
                'order_id' => $orderId,
                'payment_redirect_url' => $redirectUrl,
                'utm_nooverride' => '1',
            ],
            RouterInterface::ABSOLUTE_URL
        );

        $label = $this->generateLabel(
            $wallet->ID,
            $this->orderTokenService->createToken($orderId)->getValue()
        );

        foreach ($orders as $subOrder) {
            $this->saveBankWireTransaction($subOrder, $label, [LemonWayConfig::LEMONWAY_BANKWIRE => true]);
        }

        return [
            'amount' => $order->getCustomerTotal(),
            'IBAN' => $this->getConfig()->getIban(),
            'BIC' => $this->getConfig()->getBic(),
            'label' => $label,
            'comment' => null,
            'socialName' => $this->getConfig()->getHolderName(),
            'address' => $this->getConfig()->getHolderAddress(),
            'completeUrl' => $completeUrl,
        ];
    }

    /**
     * Get a LemonWayTransactionDetails which can give you access to all Lemonway transaction details
     * @see LemonWayTransactionDetails
     *
     * @return LemonWayTransactionDetails for phpstan
     */
    public function getTransactionDetailsByToken(string $transactionMerchantToken): TransactionDetails
    {
        return new LemonWayTransactionDetails(
            $this->api->getMoneyInTransDetails([
                'transactionMerchantToken' => $transactionMerchantToken,
            ]),
            $transactionMerchantToken
        );
    }

    /**
     * Get a LemonWayTransactionDetails which can give you access to all Lemonway transaction details
     * @see LemonWayTransactionDetails
     *
     * @return LemonWayTransactionDetails for phpstan
     */
    public function getTransactionDetails(string $transactionId): TransactionDetails
    {
        return new LemonWayTransactionDetails(
            $this->api->getMoneyInTransDetails([
                'transactionId' => $transactionId,
            ])
        );
    }

    /**
     * This send a query to LemonWay on each use.
     * use $this->getTransactionDetails() if you need to read several transaction properties
     */
    public function isTransactionSuccessful(string $transactionMerchantToken): bool
    {
        return $this->getTransactionDetailsByToken($transactionMerchantToken)->isSuccessFul();
    }

    /**
     * This send a query to LemonWay on each use.
     * use $this->getTransactionDetails() if you need to read several transaction properties
     */
    public function getOrderId(string $transactionMerchantToken): string
    {
        return $this->getTransactionDetailsByToken($transactionMerchantToken)->getOrderId();
    }

    /**
     * Le wallet utilisé pour le moneyin peut-être différent en fonction du parameter 'lemonway_api.use_buyer_wallet_for_moneyin'
     *   - true: le wallet de l'acheteur
     *   - false: le wallet technique créé par LW pour la MP. Il est généralement nommé MKP
     *
     * Dans le premier cas, un compte est créé pour chacun des acheteurs et on stock le montant de sa commande dedans.
     * Pour la répartition des fonds, il faut partir de ce wallet pour transférer les fonds vers les marchands.
     */
    public function getMoneyInWallet(User $user): \stdClass
    {
        if ($this->getConfig()->isUseBuyerWalletForMoneyIn()) {
            return $this->getOrCreateWallet($user);
        }

        return $this->getTechnicalWallet();
    }

    public function getTechnicalWallet(): \stdClass
    {
        $techWallet = new \stdClass();
        $techWallet->ID = $this->getConfig()->getTechnicalWalletId();

        return $techWallet;
    }

    public function getCompanyWallet(Company $company): \stdClass
    {
        return $this->getOrCreateWallet($this->userRepository->get($company->getFirstAdmin()->getId()));
    }

    public function validateKyc(Company $company): self
    {
        $wallet = $this->getCompanyWallet($company);

        $statusFrom = $company->getCompanyStatus();

        $lemonWayStatus = new LemonWayWalletStatus(\intval($wallet->STATUS));

        switch ($lemonWayStatus) {
            case LemonWayWalletStatus::REGISTERED_AND_VERIFIED():
            case LemonWayWalletStatus::REGISTERED_AND_VERIFIED_BY_PREVIOUS_PSP_KYC3():
            case LemonWayWalletStatus::ONE_TIME_CUSTOMER():
                $statusTo = CompanyStatus::ENABLED();
                break;
            case LemonWayWalletStatus::BLOCKED():
                $statusTo = CompanyStatus::DISABLED();
                break;
            // Not very clear case, so we do nothing
            case LemonWayWalletStatus::REGISTERED_NOT_VERIFIED_DOCUMENTS_REJECTED():
            case LemonWayWalletStatus::REGISTERED_NOT_VERIFIED_EXPIRED_DOCUMENTS():
            case LemonWayWalletStatus::CLOSED():
            case LemonWayWalletStatus::REGISTERED_STATUS_IS_BEING_UPDATED_FROM_KYC2_TO_KYC3():
            case LemonWayWalletStatus::SPECIAL_WALLET_FOR_CROWLENDING():
            case LemonWayWalletStatus::TECHNICAL_WALLET():
                return $this;
            default:
                $statusTo = CompanyStatus::PENDING();
        }

        if ($statusTo == $statusFrom) {
            return $this;
        }

        if (false === fn_companies_change_status($company->getId(), $statusTo->getValue(), $this->getReason($wallet), $statusFrom->getValue())) {
            $this->logger->error('[LemonWay] validate KYC, unable to update company status.', [
                'data' => [
                    'company' => $company->getData(),
                    'statusFrom' => $statusFrom,
                    'statusTo' => $statusTo,
                    'reason' => $this->getReason($wallet),
                ],
            ]);

            throw new \Exception("[LemonWay] Cannot update status of company #{$company->getId()}.");
        }

        return $this;
    }

    public function captureTransaction(LemonWayTransactionDetails $transaction): \stdClass
    {
        $response = $this->api->moneyInValidate([
            'transactionId' => $transaction->getTransactionId(),
        ]);

        if ($response->HPAY->STATUS !== LemonWayApi::STATUS_SUCCESS) {
            throw new \Exception("Capture Failed, transactionId: {$transaction->getTransactionId()}, status: {$response->HPAY->STATUS}");
        }

        return $response;
    }

    public function cancelTransaction(LemonWayTransactionDetails $transaction): \stdClass
    {
        $response = $this->api->cancelMoneyIn([
            'wallet' => $transaction->getWalletId(),
            'transaction' => $transaction->getTransactionId(),
        ]);

        // We need to get transaction details, because $response STATUS is wrong, it's return new error code (6)
        $transactionDetails = $this->getTransactionDetails($transaction->getTransactionId());

        // a successfully canceled transaction should have a status === LemonWayApi::STATUS_ERROR()
        if ($transactionDetails->checkTransactionStatus(LemonWayApi::STATUS_ERROR)) {
            return $response;
        }

        throw new \Exception("Cancelation Failed, transactionId: {$response->HPAY->ID}, status: {$response->HPAY->STATUS}");
    }

    public function transferTransaction(
        WizachaOrder $order,
        Money $amount,
        string $walletFrom,
        string $walletTo,
        bool $mpRefund = false
    ): \stdClass {
        return $this->api->sendPayment([
            'debitWallet' => $walletFrom,
            'creditWallet' => $walletTo,
            'amount' => $this->formatPrice($amount->getConvertedAmount()),
            'message' => $mpRefund ? 'Refund ' . $this->generateComment($order) : $this->generateComment($order),
        ]);
    }

    public function getHumanStatus($status): TransactionStatus
    {
        switch ($status) {
            case LemonWayApi::STATUS_SUCCESS:
                return TransactionStatus::SUCCESS();

            case LemonWayApi::STATUS_ERROR:
                return TransactionStatus::FAILED();

            case LemonWayApi::STATUS_PENDING:
            case LemonWayApi::STATUS_RESERVATION:
                return TransactionStatus::PENDING();
        }

        throw new \Exception("Unable to map LemonWay status : $status.");
    }

    public function updateWallet(UserType $userType, User $user): \stdClass
    {
        if ($userType->equals(UserType::CLIENT())) {
            $company = null;
            $wallet = LemonWayConfig::PREFIX_USER . $user->getUserId();
        } else {
            $company = new Company($user->getCompanyId());
            $company->setSkipCache(true);

            $wallet = LemonWayConfig::PREFIX_VENDOR . $company->getId();
        }

        // We can't change wallet information if KYC 2 or 3
        $walletStatus = $this->getOrCreateWallet($user)->STATUS;
        if (true !== \is_null($walletStatus) //When wallet is created, it doesn't have status
            && false === LemonWayWalletStatus::isKyc1($walletStatus)
        ) {
            throw new KycLevelTooHigh(__('lemonway_kyc_too_high'));
        }

        return $this->api->updateWalletDetails(
            $this->buildArrayUpdateWallet($userType, $user, $wallet, $company)
        );
    }

    public function dispatchFunds(WizachaOrder $order, Money $commission): self
    {
        if ($order->getTotal()->isZero()) {
            return $this;
        }

        $company = $order->getCompany();
        $walletFrom = $this->getWalletFrom($order);

        $walletTo = $this->getCompanyWallet($company);
        $vendorAmount = $order->getBalanceTotal()->subtract($commission);
        $paymentInfo = $order->getPaymentInformation();

        if ($vendorAmount->isPositive()
            && (\array_key_exists(LemonWayConfig::LEMONWAY_TRANSFER_VENDOR_DONE, $paymentInfo) === false
            || $paymentInfo[LemonWayConfig::LEMONWAY_TRANSFER_VENDOR_DONE] === false)
        ) {
            // Envoi du compte de l'acheteur vers le compte du marchand
            $responseVendor = null;
            try {
                $responseVendor = $this->api->sendPayment(
                    [
                        'debitWallet' => $walletFrom->ID,
                        'creditWallet' => $walletTo->ID,
                        'amount' => $this->formatPrice($vendorAmount->getConvertedAmount()),
                        'message' => $this->generateComment($order),
                        'OriginTransaction' => $order->getId(),
                    ]
                );
                $order->setPaymentInformation(LemonWayConfig::LEMONWAY_TRANSFER_VENDOR_DONE, true);

                $this->eventDispatcher->dispatch(
                    new TransactionTransferEvent(
                        $order->getId(),
                        $order->getCompany()->getId(),
                        $walletFrom->ID,
                        $walletTo->ID,
                        '',
                        $this->getConfig()->getCurrencyCode(),
                        $vendorAmount->getAmount(),
                        TransactionStatus::SUCCESS(),
                        $responseVendor->HPAY->ID ?? uniqid('wizaplace_'),
                        $this->getName()
                    ),
                    TransactionsTransferEventType::DISPATCH_FUNDS_TRANSFER_VENDOR()->getValue()
                );
            } catch (LemonWayException $exception) {
                $order->setPaymentInformation(LemonWayConfig::LEMONWAY_TRANSFER_VENDOR_DONE, false);
                $this->eventDispatcher->dispatch(
                    new TransactionTransferEvent(
                        $order->getId(),
                        $order->getCompany()->getId(),
                        $walletFrom->ID,
                        $walletTo->ID,
                        '',
                        $this->getConfig()->getCurrencyCode(),
                        $vendorAmount->getAmount(),
                        TransactionStatus::FAILED(),
                        $responseVendor->HPAY->ID ?? uniqid('wizaplace_'),
                        $this->getName()
                    ),
                    TransactionsTransferEventType::DISPATCH_FUNDS_TRANSFER_VENDOR()->getValue()
                );
                if ($commission->isPositive() === true) {
                    $order->setPaymentInformation(LemonWayConfig::LEMONWAY_TRANSFER_COM_DONE, false);
                    $this->eventDispatcher->dispatch(
                        new TransactionTransferEvent(
                            $order->getId(),
                            $order->getCompany()->getId(),
                            $walletFrom->ID,
                            LemonWayConfig::MAIN_WALLET,
                            '',
                            $this->getConfig()->getCurrencyCode(),
                            $commission->getAmount(),
                            TransactionStatus::FAILED(),
                            uniqid('wizaplace_'),
                            $this->getName()
                        ),
                        TransactionsTransferEventType::DISPATCH_FUNDS_TRANSFER_COMMISSION()->getValue()
                    );
                }
                throw new LemonWayException($exception->getMessage());
            }
        }

        if ($commission->isPositive()
            && (\array_key_exists(LemonWayConfig::LEMONWAY_TRANSFER_COM_DONE, $paymentInfo) === false
            || $paymentInfo[LemonWayConfig::LEMONWAY_TRANSFER_COM_DONE] === false)
        ) {
            // Envoi du compte de l'acheteur vers le compte de l'admin de la MP
            $responseCommission = null;
            try {
                $responseCommission = $this->api->sendPayment(
                    [
                        'debitWallet' => $walletFrom->ID,
                        'creditWallet' => LemonWayConfig::MAIN_WALLET,
                        'amount' => $this->formatPrice($commission->getConvertedAmount()),
                        'message' => $this->generateComment($order),
                        'OriginTransaction' => $order->getId(),
                    ]
                );
                $order->setPaymentInformation(LemonWayConfig::LEMONWAY_TRANSFER_COM_DONE, true);
                $this->eventDispatcher->dispatch(
                    new TransactionTransferEvent(
                        $order->getId(),
                        $order->getCompany()->getId(),
                        $walletFrom->ID,
                        LemonWayConfig::MAIN_WALLET,
                        '',
                        $this->getConfig()->getCurrencyCode(),
                        $commission->getAmount(),
                        TransactionStatus::SUCCESS(),
                        $responseCommission->HPAY->ID ?? uniqid('wizaplace_'),
                        $this->getName()
                    ),
                    TransactionsTransferEventType::DISPATCH_FUNDS_TRANSFER_COMMISSION()->getValue()
                );
            } catch (LemonWayException $exception) {
                $order->setPaymentInformation(LemonWayConfig::LEMONWAY_TRANSFER_COM_DONE, false);
                $this->eventDispatcher->dispatch(
                    new TransactionTransferEvent(
                        $order->getId(),
                        $order->getCompany()->getId(),
                        $walletFrom->ID,
                        LemonWayConfig::MAIN_WALLET,
                        '',
                        $this->getConfig()->getCurrencyCode(),
                        $commission->getAmount(),
                        TransactionStatus::FAILED(),
                        $responseCommission->HPAY->ID ?? uniqid('wizaplace_'),
                        $this->getName()
                    ),
                    TransactionsTransferEventType::DISPATCH_FUNDS_TRANSFER_COMMISSION()->getValue()
                );
                throw new LemonWayException($exception->getMessage());
            }
        }

        return $this;
    }

    public function unregisterIban(\stdClass $wallet): self
    {
        foreach ($wallet->IBANS as $bankAccount) {
            // phpcs:ignore
            if (LemonWayApi::STATUS_IBAN_DISABLE !== $bankAccount->S) {
                $this->api->unregisterIBAN([
                    'wallet' => $wallet->ID,
                    'ibanId' => $bankAccount->ID,
                ]);
            }
        }

        return $this;
    }

    public function registerIban(\stdClass $wallet, Company $company): self
    {
        $this->api->registerIBAN([
            'wallet' => $wallet->ID,
            'holder' => mb_substr(
                sprintf("%s - %s %s", $company->getName(), $company->getLegalRepresentativeFirstname(), $company->getLegalRepresentativeLastname()),
                0,
                100 // LW n'accepte un holder qu'avec maxi 100 caractères
            ),
            'iban' => $company->getIban(),
            'bic' => $company->getBic(),
        ]);

        return $this;
    }

    public function isCompanyActivated(int $companyId): bool
    {
        $company = new Company($companyId);
        $company->setSkipCache(true);

        try {
            $this->userRepository->get($company->getFirstAdmin()->getId());

            $this->api->getWalletDetails([
                'wallet' => LemonWayConfig::PREFIX_VENDOR . $company->getId(),
                'email' => $company->getEmail(),
            ]);

            return true;
        } catch (NotFound | LemonWayException $exception) {
            return false;
        }
    }

    public function sendKyc(
        \stdClass $wallet,
        Company $company,
        array $registrationFilesList = [],
        bool $force = true
    ): self {
        if ($wallet->ID === $this->getConfig()->getTechnicalWalletId()) {
            // Le wallet est le wallet technique de l'admin de la MP
            // il n'est donc pas necessaire d'envoyer les KYC qui seront de toutes façon vides

            return $this;
        }

        $userType = UserType::VENDOR();
        if (false !== strpos($wallet->ID, LemonWayConfig::PREFIX_USER)) {
            $user = $this->userRepository->get(
                str_replace(LemonWayConfig::PREFIX_USER, '', $wallet->ID)
            );

            if (false === \is_int($user->getCompanyId())) {
                // FIXME
                // Il n'est pas encore possible d'ajouter des registration files
                // pour un utilisateur qui n'est pas un admin de marchand

                throw new LemonWayException("Users cannot have registration files.");
            }

            $company = new Company($user->getCompanyId());
            $company->setSkipCache(true);

            // On set le type à client car on est dans le cas où il faut fournir des KYC pour les acheteurs
            $userType = UserType::CLIENT();
        }

        $bypass = false;
        if (false === $force
            && is_iterable($wallet->DOCS)
            && \count($wallet->DOCS) > 0
        ) {
            $this->logger->debug(
                'LemonWay',
                ['KYC' => 'already sent, bypassing ...']
            );
            $bypass = true;
        }

        if (\count($registrationFilesList) === 0) {
            $this->logger->debug(
                'LemonWay',
                ['KYC' => 'nothing to send, bypassing ...']
            );
            $bypass = true;
        }

        if (true === $bypass) {
            return $this;
        }

        foreach (LemonWayConfig::KYC[$userType->getValue()] as $name => $type) {
            if (\array_key_exists($name, $registrationFilesList)) {
                $fileName = $registrationFilesList[$name] ?? "$name: unknown file";

                try {
                    $filePath = $company->getId() . "/" . $fileName;
                    $this->api->uploadFile(
                        [
                            'wallet' => $wallet->ID,
                            'fileName' => $filePath,
                            'type' => $type,
                        ],
                        container()->get('Wizacha\Storage\VendorSubscriptionStorageService')
                    );
                } catch (LemonWayException $exception) {
                    if ($exception->getCode() !== LemonWayApi::ERROR_CODES_DUPLICATED_DOCUMENT_FOUND) {
                        fn_set_notification(
                            "E",
                            'LemonWay KYC',
                            implode(
                                ' - ',
                                [
                                    $fileName,
                                    $exception->getMessage()
                                ]
                            )
                        );

                        $this->logger->error('[LemonWay] send KYC, unable to send KYC.', [
                            'exception' => $exception,
                            'data' => $company->getData(),
                            'file' => $fileName,
                        ]);
                    }
                }
            }
        }

        return $this;
    }

    public function getPayoutBalance(Company $company): ?int
    {
        return Money::fromVariable($this->getCompanyWallet($company)->BAL)->getAmount();
    }

    public function assertCanDoPayoutCompany(Company $company): void
    {
        parent::assertCanDoPayoutCompany($company);

        $wallet = $this->getCompanyWallet($company);

        if ($wallet->BAL > 0) {
            try {
                $ibanId = $this->getEnabledIbanIdFromWallet($wallet);
            } catch (LemonWayException $exception) {
                $this->eventDispatcher->dispatch(
                    new TransactionTransferEvent(
                        null,
                        $company->getId(),
                        '',
                        '',
                        $exception->getMessage(),
                        '',
                        Money::fromVariable($wallet->BAL)->getAmount(),
                        TransactionStatus::FAILED(),
                        uniqid('wizaplace_'),
                        $this->getName()
                    ),
                    TransactionsTransferEventType::VENDOR_WITHDRAWAL()->getValue()
                );

                throw $exception;
            }

            $return = $this->api->moneyOut([
                'wallet' => $wallet->ID,
                'amountTot' => $wallet->BAL,
                'message' => "Payout $wallet->ID " . date('Y-m-d'),
                'ibanId' => $ibanId,
                'autoCommission' => 0,
            ]);

            $payoutStatus = TransactionStatus::SUCCESS();
            $messageException = '';

            if ($return->STATUS !== LemonWayApi::STATUS_SUCCESS) {
                if ($return->STATUS === LemonWayApi::STATUS_PENDING) {
                    $payoutStatus = TransactionStatus::PENDING();
                    $messageException = 'Payout waiting for validation for company ' . $company->getId() . '.';
                    $this->logger->info($messageException);
                } elseif ($return->STATUS === LemonWayApi::STATUS_ERROR) {
                    $payoutStatus = TransactionStatus::FAILED();
                    $messageException = 'Error on payout: transaction refused.';
                }
            }

            $this->eventDispatcher->dispatch(
                new TransactionTransferEvent(
                    null,
                    $company->getId(),
                    $wallet->ID,
                    $company->getName() . ' bank account',
                    $messageException,
                    'EUR',
                    Money::fromVariable($wallet->BAL)->getAmount(),
                    $payoutStatus,
                    uniqid('wizaplace_'),
                    $this->getName()
                ),
                TransactionsTransferEventType::VENDOR_WITHDRAWAL()->getValue()
            );

            if ($payoutStatus->equals(TransactionStatus::FAILED()) === true) {
                throw new LemonWayException($messageException);
            }
        }
    }

    public function canDoPayoutCompany(): bool
    {
        return true;
    }

    public function refund(Transaction $originTransaction, Refund $refund): Transaction
    {
        return $this->refundTransaction(
            $originTransaction,
            $refund,
            function (Transaction $transaction) use ($originTransaction, $refund): Transaction {
                try {
                    $amount = $refund->getAmount()->reducePrecisionToCents();
                    $order = new WizachaOrder($originTransaction->getOrderId());
                    if ($this->getConfig()->isUseBuyerWalletForMoneyIn() === true) {
                        $wallet = $this->getMoneyInWallet(
                            $this->userRepository->get($order->getUserId())
                        );
                        //Refund P2P: MKP TO Wallet client
                        $responseP2P = $this->transferTransaction(
                            $order,
                            $amount,
                            $this->getTechnicalWallet()->ID,
                            $wallet->ID,
                            true
                        );
                    }

                    $response = $this->api->refundMoneyIn([
                        'transactionId' => $originTransaction->getTransactionReference(),
                        'amountToRefund' => $this->formatPrice($amount->getConvertedAmount()),
                        'comment' => $this->generateComment($order),
                    ]);
                    $transaction->setTransactionReference($response->HPAY->ID);
                    $transaction->addInformation('response', $response);
                } catch (LemonWayException $exception) {
                    $this->transactionService->updateTransactionStatus($transaction, TransactionStatus::FAILED());

                    throw new RefundErrorFromPspException(
                        PaymentProcessorName::LEMONWAY()->getValue(),
                        $exception->getMessage(),
                        $exception->getCode(),
                        $exception
                    );
                }

                return $transaction;
            }
        );
    }

    public function refundMarketplaceDiscount(Order $order, Refund $refund, Money $amount): Transaction
    {
        $transaction = $this->transactionService->createRefundTransferTransaction(
            $order->getId(),
            $amount,
            PaymentProcessorName::LEMONWAY(),
            ['refund_id' => $refund->getId()]
        );

        try {
            $response = $this->transferTransaction(
                $order->getLegacyOrder(),
                $amount,
                $this->getTechnicalWallet()->ID,
                $this->config->getDiscountWallet(),
                true
            );

            $transaction->setTransactionReference($response->HPAY->ID);
            $transaction->setStatus($this->getHumanStatus($response->HPAY->STATUS));
        } catch (\Throwable $exception) {
            $this->logger->error('[LEMONWAY] p2p transfer from technical to MP discount wallet failed', [
                'transactionId' => $transaction->getId(),
                'exception' => $exception,
            ]);
            $transaction->setStatus(TransactionStatus::FAILED());
        }

        return $this->transactionService->save($transaction);
    }

    public function getTransactionDetailsByOrderId(int $orderId): ?TransactionDetails
    {
        $transactions = $this->transactionService->findBy([
            'orderId' => $orderId,
            'processorName' => $this->getName(),
        ]);

        if (\count($transactions) === 0) {
            return null;
        }

        return $this->getTransactionDetails(
            $transactions[0]->getTransactionReference()
        );
    }

    public function getSepaTransactionDetails(string $transactionId): ?TransactionDetails
    {
        try {
            $transaction = $this->transactionService->getOneBy(
                [
                    'processorName' => $this->getName(),
                    'transactionReference' => $transactionId,
                    'type' => TransactionType::DIRECT_DEBIT,
                ]
            );
        } catch (TransactionNotFound $e) {
            return null;
        }

        // Ignore empty transaction & non SEPA orders ( CB, ... )
        if (\array_key_exists(LemonWayConfig::LEMONWAY_SEPA, $transaction->getProcessorInformations()) === false) {
            return null;
        }

        return $this->getTransactionDetails($transactionId);
    }

    /**
     * Check whether the transaction status is successful.
     *
     * @param TransactionDetails $transaction
     *
     * @return bool
     *
     * @throws \Exception
     */
    public function isTransactionDetailsSuccessful(TransactionDetails $transaction): bool
    {
        return $this->getHumanStatus($transaction->getStatus())->equals(TransactionStatus::SUCCESS());
    }

    /**
     * Check whether the transaction status if failed.
     *
     * @param TransactionDetails $transaction
     *
     * @return bool
     *
     * @throws \Exception
     */
    public function isTransactionDetailsFailed(TransactionDetails $transaction): bool
    {
        return $this->getHumanStatus($transaction->getStatus())->equals(TransactionStatus::FAILED());
    }

    public function isTransactionDetailsCancelled(TransactionDetails $transactionDetails): bool
    {
        if ($transactionDetails instanceof LemonWayTransactionDetails) {
            return $transactionDetails->getTransactionStatus()->equals(LemonWayTransactionStatus::CANCELED());
        }

        return false;
    }

    public function getName(): PaymentProcessorName
    {
        return PaymentProcessorName::LEMONWAY();
    }

    protected function getEnabledIbanIdFromWallet(\stdClass $wallet): string
    {
        $ibanId = null;

        if (\is_array($wallet->IBANS) === false) {
            throw new LemonWayException('Error: IBAN not found.');
        }

        foreach ($wallet->IBANS as $iban) {
            // phpcs:ignore
            if ($iban->S === LemonWayApi::STATUS_IBAN_ENABLE) {
                $ibanId = $iban->ID;
                break;
            }
        }

        if (\is_string($ibanId) === false) {
            throw new LemonWayException('Error: no enabled IBAN found.');
        }

        return $ibanId;
    }

    protected function getOrCreateWallet(User $user, bool $isTechnicalWalletUser = false): \stdClass
    {
        $userType = new UserType($user->getUserType());
        if ($isTechnicalWalletUser === true) {
            $company = null;
            $wallet = LemonWayConfig::PREFIX_TECH_USER . $user->getUserId();
            $email = LemonWayConfig::PREFIX_USER . $user->getUserId() . LemonWayConfig::PREFIX_MAIL_WALLET;
        } elseif ($userType->equals(UserType::VENDOR())) {
            // The user is a vendor, we use the company wallet
            $company = new Company($user->getCompanyId());
            $company->setSkipCache(true);

            $wallet = LemonWayConfig::PREFIX_VENDOR . $company->getId();
            $email = $company->getEmail();
        } else {
            $company = null;
            $wallet = LemonWayConfig::PREFIX_USER . $user->getUserId();
            $email = $user->getEmail();
        }

        try {
            return $this->api->getWalletDetails([
                'wallet' => $wallet,
                'email' => $email,
            ]);
        } catch (LemonWayException $exception) {
            if ($exception->getCode() !== LemonWayException::WALLET_NOT_FOUND) {
                throw $exception;
            }
        }

        $arrayRegisterWallet = $this->buildArrayRegisterWallet($userType, $user, $wallet, $company, $isTechnicalWalletUser);

        try {
            $wallet = $this->api->registerWallet(
                $arrayRegisterWallet
            );
        } catch (LemonWayException $exception) {
            $this->logger->error(
                '[LemonWay] Error on register Wallet.',
                [
                    'user_id' => $user->getUserId(),
                    'company_id' => $user->getCompanyId(),
                    'array_register_wallet' => $arrayRegisterWallet,
                ]
            );

            throw new LemonWayWalletRequestFailed($exception->getMessage(), 0, $exception);
        }

        if (\strpos($wallet->ID, LemonWayConfig::PREFIX_VENDOR) === false
            && $userType->equals(UserType::VENDOR()) === true
        ) {
            $this->logger->error(
                '[LemonWay] create company, unable to create company.',
                [
                    'user_id' => $user->getUserId(),
                    'company_id' => $user->getCompanyId(),
                    'array_register_wallet' => $arrayRegisterWallet,
                ]
            );
        }

        return $wallet;
    }

    private function getReason(\stdClass $wallet): string
    {
        $kycReasons = [];
        // phpcs:disable
        foreach ($wallet->DOCS as $doc) {
            if (LemonWayApi::STATUS_KYC_WAITING === $doc->S) {
                continue;
            }

            if (LemonWayApi::STATUS_KYC_ACCEPTED !== $doc->S) {
                $kycReasons[] = $doc->C;
            }
        }
        // phpcs:enable

        return implode(", ", $kycReasons);
    }

    private function generateComment(WizachaOrder $order): string
    {
        $comment = "M" . $order->getId();
        foreach ($order->getSubOrders() as $subOrder) {
            $comment .= ";" . $subOrder->getId();
        }

        return $comment;
    }

    private function buildArrayRegisterWallet(UserType $userType, User $user, string $wallet, Company $company = null, bool $isTechnicalWalletUser = false): array
    {
        $birthdate = $user->getBirthday() instanceof \DateTimeInterface
            ? $user->getBirthday()
            : \DateTime::createFromFormat('U', "0");

        $data = [
            'wallet' => $wallet,
            'birthdate' => $birthdate->format("d/m/Y"),
            'isOneTimeCustomer' => '0',
        ];

        $userCountry = $this->getUserCountry($user);
        $userNationalities = $this->getUserNationalities($user);

        if ($isTechnicalWalletUser === true) {
                $data = array_merge($data, [
                    'clientMail' => LemonWayConfig::PREFIX_USER . $user->getUserId() . LemonWayConfig::PREFIX_MAIL_WALLET,
                    'clientFirstName' => $user->getFirstname(),
                    'clientLastName' => $user->getLastname(),
                    'isCompany' => '0',
                    'payerOrBeneficiary' => '2',
                    'isTechWallet' => '1',
                    'ctry' => $userCountry,
                    'nationality' => $userNationalities,
                ]);
        } elseif ($userType->equals(UserType::CLIENT()) === true) {
            $data = array_merge($data, [
                'clientMail' => $user->getEmail(),
                'clientFirstName' => $user->getFirstname(),
                'clientLastName' => $user->getLastname(),
                'isCompany' => '0',
                'payerOrBeneficiary' => '1',
                'isTechWallet' => '0',
                'ctry' => $userCountry,
                'nationality' => $userNationalities,
            ]);
        } else {
            $country = $this->countryService->getCodeA3($company->getCountry());

            $data = array_merge($data, [
                'isCompany' => '1',
                'companyName' => $company->getCorporateName(),
                'companyWebsite' => $company->getUrl(),
                'companyDescription' => $company->getDescription() ?: "Company " . $company->getId(),
                'clientMail' => $company->getEmail(),
                'clientFirstName' => $company->getLegalRepresentativeFirstname(),
                'clientLastName' => $company->getLegalRepresentativeLastname(),
                'payerOrBeneficiary' => '2',
                'street' => $company->getAddress(),
                'postCode' => $company->getZipcode(),
                'city' => $company->getCity(),
                'isTechWallet' => '0',
                'ctry' => $country,
                'nationality' => $country,
            ]);
        }

        return $data;
    }

    private function buildArrayUpdateWallet(UserType $userType, User $user, string $wallet, Company $company = null): array
    {
        $data = [
            'wallet' => $wallet,
            'newBirthDate' => $user->getBirthday() !== null ? $user->getBirthday()->format('d/m/Y') : null,
        ];

        if ($userType->equals(UserType::CLIENT())) {
            $data = array_merge($data, [
                'newEmail' => $user->getEmail(),
                'newFirstName' => $user->getFirstname(),
                'newLastName' => $user->getLastname(),
                'newCtry' => $this->getUserCountry($user),
                'newNationality' => $this->getUserNationalities($user),
            ]);
        } else {
            $country = $this->countryService->getCodeA3($company->getCountry());

            $data = array_merge($data, [
                'newCompanyName' => $company->getCorporateName(),
                'newCompanyWebsite' => $company->getUrl(),
                'newCompanyDescription' => $company->getDescription(),
                'newEmail' => $company->getEmail(),
                'newFirstName' => $company->getLegalRepresentativeFirstname(),
                'newLastName' => $company->getLegalRepresentativeLastname(),
                'newStreet' => $company->getAddress(),
                'newPostCode' => $company->getZipcode(),
                'newCity' => $company->getCity(),
                'newCtry' => $country,
                'newNationality' => $country,
            ]);
        }

        return $data;
    }

    private function formatPrice(float $price): string
    {
        return number_format($price, 2, '.', '');
    }

    /**
     * Used to generate bankwire label
     * Format is xx-yy-zz
     * xx : lemonway marketplace id
     * yy : destination wallet external id
     * zz : order token used to retrieve order
     *
     * The last part was added after discussion with Lemonway
     * see original format : http://documentation.lemonway.fr/display/EN/IBAN+common
     */
    private function generateLabel(string $walletId, string $orderToken): string
    {
        return \sprintf(
            "%s-%s-%s",
            $this->getConfig()->getMarketplaceId(),
            $walletId,
            $orderToken
        );
    }

    /** @param mixed[] $information Additional information, e.g. credited wallet ID */
    private function saveCreditCardTransaction(OrderPricesInterface $order, string $transactionId, array $information): Transaction
    {
        // Keep legacy transactions persistence to prevent unwanted BC break
        fn_update_order_payment_info($order->getId(), ['lemonway_transaction_id' => $transactionId]);

        $transaction = $this
            ->transactionService->createCreditCardTransaction(
                $order->getId(),
                $order->getCustomerTotal(),
                $this->getName(),
                $information
            )
            ->setTransactionReference($transactionId)
        ;

        //Set Origin and Destination
        $user = $this->userRepository->get($order->getUserId());
        $transaction->setOrigin($user->getUserId() . ' ' . $user->getFirstname() . ' ' . $user->getLastname());
        if (\array_key_exists('credited_wallet', $information) === true) {
            $transaction->setDestination($information['credited_wallet']);
        }

        $transaction->setCurrency($this->getConfig()->getCurrencyCode());

        $this->transactionService->save($transaction);

        return $transaction;
    }

    /** @param mixed[] $information Additional information, e.g. credited wallet ID */
    private function saveBankWireTransaction(OrderPricesInterface $order, string $label, array $information): Transaction
    {
        // Keep legacy transactions persistence to prevent unwanted BC break
        fn_update_order_payment_info($order->getId(), [
            LemonWayConfig::BANKWIRE_LABEL => $label,
            LemonWayConfig::LEMONWAY_BANKWIRE => true,
        ]);

        $transaction = $this->transactionService->createBankWireTransaction(
            $order->getId(),
            $order->getCustomerTotal(),
            $this->getName(),
            $information
        );

        $transaction->setStatus(TransactionStatus::PENDING());
        $transaction->setTransactionReference($label);
        $this->transactionService->save($transaction);

        return $transaction;
    }

    /**
     * @throws ApiErrorException
     * @throws LemonWayException
     */
    public function createSepaMandate(User $user, array $lemonwayData, string $returnUrl, string $errorUrl, bool $isTechnicalWalletUser = false): int
    {
        if ($this->urlValidator->isUrlValid($returnUrl) === false
            || $this->urlValidator->isUrlValid($errorUrl) === false
        ) {
            throw new FieldsValidationException(__("text_invalid_url"));
        }

        if ($this->isValidPhoneNumber($user->getPhone()) === false) {
            throw new FieldsValidationException(__('api_post_mandat_need_user_number_phone'));
        }

        //Creation of a technical wallet
        $wallet = $this->getTechnicalUserWallet($user, $isTechnicalWalletUser);
        //Create and save the mandate on lemonway and our DB
        $lemonwayData['wallet'] = $wallet->ID;

        //Saving in DB
        $userMandate = new UserMandate($user->getUserId(), $lemonwayData['processor_id']);
        $mandateResponse = $this->registerMandate($lemonwayData);
        $userMandate->setMandateInfo($lemonwayData);
        $userMandate->setAgreementId(\intval($mandateResponse->ID));
        $this->mandateService->saveMandate($userMandate);

        //Insert or update payment info
        $userPaymentInfo = $this->mandateService->findOneUserInfoBy(['userId' => $user->getUserId()]);

        if (\is_null($userPaymentInfo) === true) {
            $userPaymentInfo = new UserPaymentInfo($user->getUserId());
            $userPaymentInfo->setLemonwaySepaAgreement(\intval($mandateResponse->ID));
            $this->mandateService->saveUserInfo($userPaymentInfo);
        } else {
            $userPaymentInfo->setLemonwaySepaAgreement(\intval($mandateResponse->ID));
            $this->mandateService->saveUserInfo($userPaymentInfo);
        }

        $signData = [
            'wallet' => $wallet->ID,
            'mobileNumber' => $user->getPhone(),
            'documentId' => $mandateResponse->ID,
            'documentType' => 21,
            'returnUrl' => $returnUrl,
            'errorUrl' => $errorUrl,
        ];
        $responseSign = $this->signDocumentInit($signData);
        $userPaymentInfo = $this->mandateService->findOneUserInfoBy(['userId' => $user->getUserId()]);
        $userPaymentInfo->setLemonwayElectronicSignature($responseSign->TOKEN);
        $this->mandateService->saveUserInfo($userPaymentInfo);

        return \intval($mandateResponse->ID);
    }

    public function getTechnicalUserWallet(User $user, bool $isTechnicalWalletUser = false): \stdClass
    {
        return $this->getOrCreateWallet($user, $isTechnicalWalletUser);
    }

    public function registerMandate(array $lemonwayData): \stdClass
    {
        $holder = \sprintf(
            '%s %s',
            $lemonwayData['firstName'],
            $lemonwayData['lastName']
        );
        //Informations needed by lemonway's API
        $information = [
            'wallet' => $lemonwayData['wallet'],
            'holder' => $holder,
            'bic' => $lemonwayData['bic'],
            'iban' => $lemonwayData['iban'],
            'isRecurring' => 1,
        ];

        $response = $this->api->registerSddMandate($information);

        return $response;
    }

    public function getSepaMandateInfo(int $agreementId): ?UserMandate
    {
        return $this->mandateService->findOneMandateBy(['agreementId' => $agreementId, 'status' => MandateStatus::ENABLED()]);
    }

    private function signDocumentInit(array $signData): \stdClass
    {
        try {
            $response = $this->api->signDocumentInit($signData);
        } catch (LemonWayException $exception) {
            throw $exception;
        }

        return $response;
    }

    private function moneyInSddInit(array $data): \stdClass
    {
        try {
            $response = $this->api->moneyInSddInit($data);
        } catch (LemonWayException $exception) {
            throw $exception;
        }

        return $response;
    }

    public function getMandate($userId): ?UserMandate
    {
        $agreementLemonwayId = $this->userPaymentInfoService->getLemonwaySepaAgreement($userId);

        return $agreementLemonwayId !== null ? $this->getSepaMandateInfo($agreementLemonwayId) : null;
    }

    // check status mandate SDD chez Lemonway
    public function sepaMandateIsEnabled(\stdClass $wallet, UserMandate $mandate): bool
    {
        foreach ($wallet->SDDMANDATES as $sddMandate) {
            if (\intval($sddMandate->ID) === $mandate->getAgreementId() && $sddMandate->S === LemonWayApi::STATUS_MANDATE_SDD_ENABLED) {
                return true;
            }
        }

        return false;
    }

    public function setUrlValidator(UrlValidatorInterface $urlValidator): void
    {
        $this->urlValidator = $urlValidator;
    }

    public function isValidPhoneNumber(string $phoneNumber): bool
    {
        return \strlen($phoneNumber) >= 10
            && \strlen($phoneNumber) <= 14
            && \is_numeric($phoneNumber)
            === true;
    }

    protected function saveDirectDebitTransaction(
        OrderPricesInterface $order,
        string $transactionId,
        array $informations
    ): Transaction {
        // Keep legacy transactions persistence to prevent unwanted BC break
        fn_update_order_payment_info(
            $order->getId(),
            [
                'lemonway_transaction_id' => $transactionId,
                LemonWayConfig::LEMONWAY_SEPA => true,
            ]
        );

        $transaction = $this->transactionService->createDirectDebitTransaction(
            $order->getId(),
            $order->getCustomerTotal(),
            $this->getName(),
            $informations
        );

        $transaction->setTransactionReference($transactionId);
        $this->transactionService->updateTransactionStatus($transaction, TransactionStatus::PENDING());

        return $transaction;
    }

    private function getUserCountry(User $user): string
    {
        $userCountry = $user->getBillingAddress()->getFieldValue('country');

        return \is_string($userCountry) === true && $userCountry !== ''
            ? $this->countryService->getCodeA3($userCountry)
            : 'FRA';
    }

    /** @return string nationalities list seprated with a , */
    private function getUserNationalities(User $user): string
    {
        $nationalities = [];
        foreach ($user->getNationalities() as $nationality) {
            if (\is_string($nationality->getCodeA3()) === true) {
                $nationalities[] = $nationality->getCodeA3();
            }
        }

        if (\count($nationalities) > 0) {
            return implode(',', $nationalities);
        }

        return $this->getUserCountry($user);
    }

    /**  @throws \Wizacha\Marketplace\Basket\Exception\MissingPaymentException */
    public function getUserWalletByOrderId(int $orderId): string
    {
        $order = new WizachaOrder($orderId);

        return $this->getWalletFrom($order)->ID;
    }

    private function getWalletFrom(WizachaOrder $order): \stdClass
    {
        if (null === $order->getPaymentType()) {
            throw new MissingPaymentException('There is no payment type set for the order with id #' . $order->getId());
        }

        if ($order->getPaymentType()->equals(PaymentType::SEPA_DIRECT())
            || $order->getPaymentType()->equals(PaymentType::PAYMENT_DEFERMENT())
        ) {
            return $this->getTechnicalUserWallet($this->userRepository->get($order->getUserId()), true);
        }

        return $this->getTechnicalWallet();
    }
}
