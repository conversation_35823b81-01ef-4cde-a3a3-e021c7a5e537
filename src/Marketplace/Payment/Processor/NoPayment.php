<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Processor;

use Psr\Log\LoggerInterface;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\Refund\Entity\Refund;
use Wizacha\Marketplace\Payment\Exception\InvalidStatusException;
use Wizacha\Marketplace\Payment\Exception\RefundAmountIsTooBigException;
use Wizacha\Marketplace\Payment\GenericTransactionDetails;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\TransactionDetails;
use Wizacha\Marketplace\Transaction\Transaction;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionStatus;
use Wizacha\Money\Money;

class NoPayment implements RefundProcessorInterface, ProcessorInterface, RefundMarketplaceDiscountProcessorInterface
{
    /** @var TransactionService  */
    private $transactionService;

    /** @var OrderService */
    private $orderService;

    /** @var LoggerInterface */
    private $logger;

    public function __construct(
        TransactionService $transactionService,
        OrderService $orderService,
        LoggerInterface $logger
    ) {
        $this->transactionService = $transactionService;
        $this->orderService = $orderService;
        $this->logger = $logger;
    }

    public function refund(Transaction $originTransaction, Refund $refund, string $message = null): Transaction
    {
        if (false === $originTransaction->getStatus()->equals(TransactionStatus::SUCCESS())) {
            throw new InvalidStatusException($originTransaction->getStatus());
        }

        $amount = $refund->getAmount()->reducePrecisionToCents();
        if ($amount->greaterThan($originTransaction->getAmount())) {
            throw new RefundAmountIsTooBigException($originTransaction->getAmount(), $amount);
        }

        $orderId = $originTransaction->getOrderId();

        $transaction = $this->transactionService->createRefundTransaction(
            $orderId,
            $amount,
            PaymentProcessorName::NONE()
        );

        $transaction->setTransactionReference(uniqid('offline_'));

        if ($refund->isRefundedAfterWithdrawalPeriod() === true) {
            $transaction->setRefundId($refund->getId());
        }

        return $this->transactionService->updateTransactionStatus($transaction, TransactionStatus::SUCCESS());
    }

    public function refundMarketplaceDiscount(Order $order, Refund $refund, Money $amount): Transaction
    {
        $transaction = $this->transactionService->createRefundTransferTransaction(
            $order->getId(),
            $amount,
            PaymentProcessorName::NONE(),
            ['refund_id' => $refund->getId()]
        );

        return $this->transactionService->updateTransactionStatus($transaction, TransactionStatus::SUCCESS());
    }

    public function getPayoutBalance(Company $company): ?int
    {
        //Method not used
        return null;
    }

    public function doPayoutCompany(Company $company): void
    {
        //Method used but not implemented yet
        return;
    }

    public function canDoPayoutCompany(): bool
    {
        return false;
    }

    public function getTransactionDetails(string $identifier): TransactionDetails
    {
        return new GenericTransactionDetails(
            $identifier,
            null,
            TransactionStatus::SUCCESS(),
            []
        );
    }

    public function isConfigured(): bool
    {
        return true;
    }

    public function getHumanStatus($status): TransactionStatus
    {
        return TransactionStatus::SUCCESS();
    }

    public function getName(): PaymentProcessorName
    {
        return PaymentProcessorName::NONE();
    }
}
