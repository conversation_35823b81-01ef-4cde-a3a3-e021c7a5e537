<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Processor;

use MangoPay\Address;
use MangoPay\BankAccount;
use MangoPay\BankAccountDetailsIBAN;
use MangoPay\Birthplace;
use MangoPay\EventType;
use MangoPay\Hook;
use MangoPay\KycDocument;
use MangoPay\KycDocumentRefusedReasonType;
use MangoPay\KycDocumentStatus;
use MangoPay\KycDocumentType;
use MangoPay\KycPage;
use MangoPay\Libraries\Configuration;
use MangoPay\Libraries\Exception;
use MangoPay\Libraries\ResponseException;
use MangoPay\MangoPayApi;
use MangoPay\Money as MangoPayMoney;
use MangoPay\PayIn;
use MangoPay\PayInExecutionDetailsDirect;
use MangoPay\PayInExecutionDetailsWeb;
use MangoPay\PayInExecutionType;
use MangoPay\PayInPaymentDetailsBankWire;
use MangoPay\PayInPaymentDetailsCard;
use MangoPay\PayInPaymentType;
use MangoPay\PayInStatus;
use MangoPay\PayOut;
use MangoPay\PayOutPaymentDetailsBankWire;
use MangoPay\PayOutPaymentType;
use MangoPay\Refund;
use MangoPay\Transaction;
use MangoPay\Transfer;
use MangoPay\Ubo;
use MangoPay\UboDeclaration;
use MangoPay\User;
use MangoPay\UserLegal;
use MangoPay\UserNatural;
use MangoPay\Wallet;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;
use Wizacha\Events\IterableEvent;
use Wizacha\Marketplace\Commission\CommissionService;
use Wizacha\Marketplace\Company\CompanyEvents;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Company\CompanyStatus;
use Wizacha\Marketplace\Company\Event\CompanyLegalDocumentsUpdated;
use Wizacha\Marketplace\CompanyPerson\CompanyPersonEvents;
use Wizacha\Marketplace\CompanyPerson\CompanyPersonService;
use Wizacha\Marketplace\CompanyPerson\Event\UBOSubmitted;
use Wizacha\Marketplace\CompanyPerson\Exception\CompanyPersonNotFound;
use Wizacha\Marketplace\CompanyPerson\Event\UBOValidationFailed;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Order\Action\DispatchFundsFailed;
use Wizacha\Marketplace\Payment\AbstractPaymentLogger;
use Wizacha\Marketplace\Payment\Event\DispatchFundsFailedEvent;
use Wizacha\Marketplace\Order\Action\DispatchFundsSucceeded;
use Wizacha\Marketplace\Order\Event\OrderStatusUpdated;
use Wizacha\Marketplace\Order\OrderEvents;
use Wizacha\Marketplace\Order\OrderPricesInterface;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\Refund\Entity\Refund as WizachaRefund;
use Wizacha\Marketplace\Order\Refund\Exception\InvalidRefundRequestException;
use Wizacha\Marketplace\Payment\ApiAwareTrait;
use Wizacha\Marketplace\Payment\Exception\PayoutException;
use Wizacha\Marketplace\Payment\Exception\RefundErrorFromPspException;
use Wizacha\Marketplace\Payment\GenericTransactionDetails;
use Wizacha\Marketplace\Payment\Mangopay\MangoPayErrorsCodes;
use Wizacha\Marketplace\Payment\Mangopay\MangoPayLogger;
use Wizacha\Marketplace\Payment\UBO\Status;
use Wizacha\Marketplace\Payment\Mangopay\MangoPayApiConfig;
use Wizacha\Marketplace\Payment\Mangopay\MangopayUnsupportedBankwireType;
use Wizacha\Marketplace\Payment\Mangopay\PayinHasFailed;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\TransactionDetails;
use Wizacha\Marketplace\Payment\UserPaymentInfoService;
use Wizacha\Marketplace\PSP\UboMangopay\UboMangopay;
use Wizacha\Marketplace\PSP\UboMangopay\UboMangopayService;
use Wizacha\Marketplace\Transaction\Exception\InvalidTransactionException;
use Wizacha\Marketplace\Transaction\Transaction as WizachaTransaction;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionStatus;
use Wizacha\Marketplace\Transaction\TransactionsTransferEventType;
use Wizacha\Marketplace\Transaction\TransactionTransferEvent;
use Wizacha\Marketplace\User\UserRepository;
use Wizacha\Money\Money;
use Wizacha\Order;
use Wizacha\Marketplace\Order\OrderStatus;

class MangoPay extends AbstractProcessor implements
    EventSubscriberInterface,
    BankwireProcessorInterface,
    RefundProcessorInterface
{
    use ApiAwareTrait;
    use RefundableProcessorTrait;

    public const STATUS_CREATED = 'CREATED';
    public const STATUS_FAILED = 'FAILED';
    public const STATUS_SUCCEEDED = 'SUCCEEDED';
    public const AUTHORIZED_CULTURE = ['DE', 'EN', 'DA', 'ES', 'ET', 'FI', 'FR', 'EL', 'HU', 'IT', 'NL', 'NO', 'PL', 'PT', 'SK', 'SV', 'CS'];
    public const DESTINATON_COMMISSION = 'FEES';

    private const KYC = [
        'idCard' => KycDocumentType::IdentityProof,
        'updated_and_signed_status_document' => KycDocumentType::ArticlesOfAssociation,
        'division_of_powers' => KycDocumentType::ShareholderDeclaration,
        'kbis' => KycDocumentType::RegistrationProof,
    ];

    /** @var MangoPayApi */
    protected $api;

    /** @var CommissionService */
    private $commissionService;

    /** @var UserPaymentInfoService */
    private $userPaymentInfo;

    /** @var bool */
    private $configured;

    /** @var bool */
    private $createLegalWalletsForCustomers;

    /** @var string|null */
    private $userIdAgentEtablissementPaiement;

    /** @var string */
    private $currencyCode;

    private MangoPayLogger $mangoPayLogger;

    /** @var RouterInterface */
    private $router;

    /** @var UserRepository */
    private $userRepository;

    /** @var OrderService */
    private $orderService;

    /** @var DispatchFundsFailed */
    private $dispatchFundsFailedAction;

    /** @var string */
    private $secureMode;

    /** @var CompanyService */
    private $companyService;

    /** @var TransactionService */
    protected $transactionService;

    /** @var MangoPayApiConfig */
    protected $config;

    /** @var EventDispatcherInterface */
    private $eventDispatcher;

    /** @var DispatchFundsSucceeded */
    private $dispatchFundsSucceededAction;

    protected CompanyPersonService $companyPersonService;
    protected UboMangopayService $uboMangopayService;

    public function __construct(
        CommissionService $commissionService,
        UserPaymentInfoService $userPaymentWalletService,
        $createLegalWalletsForCustomers,
        $userIdAgentEtablissementPaiement,
        MangoPayLogger $mangoPayLogger,
        RouterInterface $router,
        UserRepository $userRepository,
        OrderService $orderService,
        DispatchFundsFailed $dispatchFundsFailedAction,
        CompanyService $companyService,
        TransactionService $transactionService,
        EventDispatcherInterface $eventDispatcher,
        DispatchFundsSucceeded $dispatchFundsSucceededAction,
        companyPersonService $companyPersonService,
        UboMangopayService $uboMangopayService
    ) {
        $this->commissionService = $commissionService;
        $this->userPaymentInfo = $userPaymentWalletService;
        $this->createLegalWalletsForCustomers = (bool) $createLegalWalletsForCustomers;
        $this->userIdAgentEtablissementPaiement = $userIdAgentEtablissementPaiement;
        $this->mangoPayLogger = $mangoPayLogger;
        $this->router = $router;
        $this->userRepository = $userRepository;
        $this->orderService = $orderService;
        $this->companyService = $companyService;
        $this->transactionService = $transactionService;
        $this->eventDispatcher =  $eventDispatcher;
        $this->dispatchFundsFailedAction = $dispatchFundsFailedAction;
        $this->dispatchFundsSucceededAction = $dispatchFundsSucceededAction;
        $this->companyPersonService = $companyPersonService;
        $this->uboMangopayService = $uboMangopayService;
    }

    public function setConfig(MangoPayApiConfig $config): self
    {
        $this->config = $config;

        if ($this->api->Config instanceof Configuration) {
            $this->api->Config->ClientId = $config->getClientId();
            $this->api->Config->ClientPassword = $config->getClientPassword();
            $this->api->Config->TemporaryFolder = $config->getTemporaryFolder();
            $this->api->Config->BaseUrl = $config->getBaseUrl();
        }

        $this->currencyCode = $config->getCurrencyCode();
        $this->secureMode = $config->getSecureMode();

        return $this;
    }

    //Not used yet
    public function createSepaTransaction(int $orderId): AbstractProcessor
    {
        $this->mangoPayLogger->log(
            AbstractPaymentLogger::ALERT_LEVEL,
            'Method used but not implemented yet MangoPay->createSepaTransaction($orderId)',
            ['orderId' => $orderId]
        );

        return $this;
    }

    public function createPayInTag(Order $order, int $orderId, $orders)
    {
        if ($order->isParentOrder()) {
            $ordersIdArray = [];
            foreach ($orders as $oneOrder) {
                $ordersIdArray[] = $oneOrder->getId();
            }
            $tag = ["Order" => $orderId,  "Suborders" => $ordersIdArray];
        } else {
            $tag = ["Order" => $orderId];
        }

        return json_encode($tag);
    }
    /**
     * Create Mangopay User
     */
    public function createCardTransaction(int $orderId, string $redirectUrl = null, string $cssUrl = null): string
    {
        if (!empty($redirectUrl)) {
            $redirectUrl = base64_encode($redirectUrl);
        }

        $order = new Order($orderId);

        $payin = new PayIn();

        $user = $this->getUser($order);
        $wallet = $this->getTransactionWallet($user);
        $payin->AuthorId = $user->Id;
        $payin->CreditedWalletId = $wallet->Id;

        // Mode un peu particulier ou l'on créé les user/wallet pour chaque utilisateur, mais on déclare que le paiement
        // est a destination d'un utilisateur système. Cette pratique est utilisée pour ne pas etre géné par les KYC
        if (false === empty($this->userIdAgentEtablissementPaiement)) {
            $payin->CreditedUserId = $this->userIdAgentEtablissementPaiement;
        }

        $payin->PaymentType = PayInPaymentType::Card;

        $paymentDetail = new PayInPaymentDetailsCard();
        $paymentDetail->CardType = 'CB_VISA_MASTERCARD';
        $payin->PaymentDetails = $paymentDetail;

        $executionType = new PayInExecutionDetailsWeb();
        $executionType->ReturnURL = $this->router->generate(
            'payment_notification_mangopay_card',
            [
                'payment_redirect_url' => $redirectUrl,
                'utm_nooverride' => '1'
            ],
            RouterInterface::ABSOLUTE_URL
        );

        // Set culture of payment page
        $paymentInfo = $order->getPaymentInformation();
        if (true === \array_key_exists('locale', $paymentInfo) && null !== $paymentInfo['locale']) {
            $superLanguage = $paymentInfo['locale'];
            if (false !== $position = \strpos($paymentInfo['locale'], '_')) {
                $superLanguage = \substr($paymentInfo['locale'], 0, $position);
            }
            if (true === \in_array(\strtoupper($superLanguage), self::AUTHORIZED_CULTURE, false)) {
                $culture = $superLanguage;
            }
        }

        $executionType->Culture = strtoupper($culture ?? (string) GlobalState::interfaceLocale());
        $executionType->SecureMode = $this->secureMode;
        $payin->ExecutionType = 'WEB';
        $payin->ExecutionDetails = $executionType;

        $payin->DebitedFunds = $this->convertMoneyToMangopayMoney($order->getCustomerTotal());
        $payin->Fees = $this->convertMoneyToMangopayMoney(new Money(0));


        $orders = $order->isParentOrder() ? $order->getSubOrders() : [$order];
        $tag = $this->createPayInTag($order, $orderId, $orders);
        $payin->Tag = $tag;

        $logContext = ['payin' => (array) $payin];

        try {
            $this->mangoPayLogger->start();

            $payin = $this->api->PayIns->Create($payin);

            $this->mangoPayLogger->requestSuccess($this->api->getHttpClient(), $logContext);
        } catch (\Throwable $exception) {
            $this->mangoPayLogger->requestException($this->api->getHttpClient(), $exception, $logContext);

            throw $exception;
        }

        if (empty($payin->Status)
            || false === \in_array($payin->Status, [static::STATUS_CREATED, static::STATUS_SUCCEEDED])
            || empty($payin->ExecutionDetails->RedirectURL)
        ) {
            $this->mangoPayLogger->log(
                AbstractPaymentLogger::ERROR_LEVEL,
                'Mangopay: Cannot create card transaction',
                [
                    'data' => [
                        'payin' => (array) $payin,
                    ],
                ]
            );

            throw new PayinHasFailed($payin->ResultMessage ?? '');
        }

        foreach ($orders as $order) {
            $this->saveCreditCardTransaction(
                $order,
                (string) $payin->Id,
                ['credited_wallet' => $payin->CreditedWalletId]
            );
        }

        return $payin->ExecutionDetails->RedirectURL;
    }

    /**
     * @param int $orderId
     * @param string|null $redirectUrl
     *
     * @return array with keys IBAN, BIC, WireReference, OwnerName, completeUrl, amount.
     *
     * @throws MangopayUnsupportedBankwireType when Mangopay use a different type than IBAN
     * @throws PayinHasFailed
     * @throws ResponseException
     */
    public function createBankwireTransaction(int $orderId, string $redirectUrl = null): array
    {
        if (!empty($redirectUrl)) {
            $redirectUrl = base64_encode($redirectUrl);
        }

        $this->setupPayinSucceededHook();

        $order = new Order($orderId);
        $orders = $order->isParentOrder() ? $order->getSubOrders() : [$order];
        $user = $this->getUser($order);
        $wallet = $this->getTransactionWallet($user);

        $payin = new PayIn();
        $payin->PaymentType = PayInPaymentType::BankWire;
        $payin->ExecutionType = PayInExecutionType::Direct;
        $payin->AuthorId = $user->Id;
        $payin->CreditedWalletId = $wallet->Id;

        $payin->Tag = $this->createPayInTag($order, $orderId, $orders);

        // required, even if it's empty
        $payin->ExecutionDetails = new PayInExecutionDetailsDirect();

        if (false === empty($this->userIdAgentEtablissementPaiement)) {
            $payin->CreditedUserId = $this->userIdAgentEtablissementPaiement;
        }

        $paymentDetail = new PayInPaymentDetailsBankWire();
        $paymentDetail->DeclaredDebitedFunds = $this->convertMoneyToMangopayMoney($order->getCustomerTotal());
        $paymentDetail->DeclaredFees = $this->convertMoneyToMangopayMoney(new Money(0));
        $payin->PaymentDetails = $paymentDetail;

        $logContext = ['payin' => (array) $payin];

        try {
            $this->mangoPayLogger->start();

            $payin = $this->api->PayIns->Create($payin);

            $this->mangoPayLogger->requestSuccess($this->api->getHttpClient(), $logContext);
        } catch (\Throwable $exception) {
            $this->mangoPayLogger->requestException($this->api->getHttpClient(), $exception, $logContext);

            throw $exception;
        }

        if (empty($payin->Status)
            || false === \in_array($payin->Status, [static::STATUS_CREATED, static::STATUS_SUCCEEDED])
        ) {
            $this->mangoPayLogger->log(
                AbstractPaymentLogger::ERROR_LEVEL,
                'Mangopay: Cannot create bankwire transaction',
                [
                    'data' => [
                        'payin' => (array) $payin,
                    ],
                ]
            );

            throw new PayinHasFailed($payin->ResultMessage ?? '', $payin->ResultCode ?? 0);
        }

        /** @var PayInPaymentDetailsBankWire $paymentDetails */
        $paymentDetails = $payin->PaymentDetails;
        $bankAccount = $paymentDetails->BankAccount;

        if ($bankAccount->Type === 'IBAN') {
            /** @var \MangoPay\BankAccountDetailsIBAN $bankDetails */
            $bankDetails = $bankAccount->Details;

            $address = $bankAccount->OwnerAddress->AddressLine1 . "\n";
            if (!empty($bankAccount->OwnerAddress->AddressLine2)) {
                $address .= $bankAccount->OwnerAddress->AddressLine2 . "\n";
            }
            $address .= $bankAccount->OwnerAddress->PostalCode . ' ';
            $address .= $bankAccount->OwnerAddress->City . "\n";
            if (!empty($bankAccount->OwnerAddress->Region)) {
                $address .= $bankAccount->OwnerAddress->Region . ', ';
            }
            $address .= $bankAccount->OwnerAddress->Country;

            $callbackUrl = $this->router->generate(
                'payment_notification_transfer_standby',
                [
                    'order_id' => $orderId,
                    'payment_redirect_url' => $redirectUrl,
                    'utm_nooverride' => '1'
                ],
                RouterInterface::ABSOLUTE_URL
            );

            foreach ($orders as $subOrder) {
                $this->saveBankWireTransaction($subOrder, (string) $payin->Id, ['mangopay_bankwire' => true , 'label' => $paymentDetails->WireReference]);
            }

            return [
                'IBAN' => $bankDetails->IBAN,
                'BIC' => $bankDetails->BIC,
                'label' => $paymentDetails->WireReference,
                'socialName' => $bankAccount->OwnerName,
                'address' => $address,
                'completeUrl' => $callbackUrl,
                'amount' => $order->getCustomerTotal(),
            ];
        } else {
            throw new MangopayUnsupportedBankwireType('Type "' . $bankAccount->Type . '" is not "IBAN"');
        }
    }

    /** Get a TransactionDetails built from a PayIn object which can give you access to all MangoPay data
     */
    public function getTransactionDetails(string $transactionId): TransactionDetails
    {
        $logContext = ['transactionId' => $transactionId];

        try {
            $this->mangoPayLogger->start();

            $payin = $this->api->PayIns->Get($transactionId);

            $this->mangoPayLogger->requestSuccess($this->api->getHttpClient(), $logContext);
        } catch (\Throwable $exception) {
            $this->mangoPayLogger->requestException($this->api->getHttpClient(), $exception, $logContext);

            throw $exception;
        }


        $data = json_decode($payin->Tag, true);

        if (false === \array_key_exists('Order', $data)) {
            throw new InvalidTransactionException('MangoPay transaction with invalid OrderID - ' . $transactionId);
        }

        return new GenericTransactionDetails(
            $payin->Id,
            $data['Order'],
            $payin->Status,
            $payin
        );
    }

    /**
     * This send a query to MangoPay on each use.
     * use $this->getTransactionDetails() if you need to read several transaction properties
     */
    public function isTransactionSuccessful(string $transactionId): bool
    {
        return $this->getTransactionDetails($transactionId)->getStatus() === PayInStatus::Succeeded;
    }

    public function isTransactionCancelled(string $transactionId): bool
    {
        $transaction = $this->getTransactionDetails($transactionId);
        return $this->isTransactionDetailsCancelled($transaction);
    }

    public function isTransactionDetailsCancelled(TransactionDetails $transactionDetails): bool
    {
        return true ===
            \in_array(
                $transactionDetails->getRawData()->ResultCode,
                MangoPayErrorsCodes::OPERATION_CANCELLED
            );
    }

    public function getTransactionOrderId($transactionId): ?int
    {
        $logContext = ['transactionId' => $transactionId];

        try {
            $this->mangoPayLogger->start();

            $tag = $this->api->PayIns->Get($transactionId)->Tag;

            $this->mangoPayLogger->requestSuccess($this->api->getHttpClient(), $logContext);
        } catch (\Throwable $exception) {
            $this->mangoPayLogger->requestException($this->api->getHttpClient(), $exception, $logContext);

            throw $exception;
        }

        if (!empty($tag)) {
            $data = json_decode($tag, true);
            if (true === \array_key_exists('Order', $data)) {
                return (int) $data['Order'];
            }
        }

        return null;
    }

    /**
     * When the order has been paid with mangopay, the money is transferred from client wallet to vendors wallet
     */
    public function transferFundsToVendor(int $orderId)
    {
        $resultTransfer = [];
        $order = new Order($orderId);
        $paymentInfo = $order->getPaymentInformation();
        $commission = $this->commissionService->getTotalCommission($order->getOrderV2());
        $amountWithoutCom = $order->getBalanceTotal()->subtract($commission);

        if (empty($paymentInfo['mangopay_transaction_id'])) {
            $this->mangoPayLogger->log(
                AbstractPaymentLogger::ERROR_LEVEL,
                'Mangopay: transaction not found.',
                [
                    'mangopayTransactionId' => $paymentInfo['mangopay_transaction_id'],
                ]
            );

            return;
        }

        if ($paymentInfo['mangopay_transfer_done']) {
            $this->mangoPayLogger->log(
                AbstractPaymentLogger::ERROR_LEVEL,
                'Mangopay: transfert already done.',
                [
                    'mangopayTransactionId' => $paymentInfo['mangopay_transaction_id'],
                ]
            );

            return;
        }

        if (\array_key_exists('mangopay_transaction_id', $order->getPaymentInformation()) === true
            || \array_key_exists('mangopay_bankwire', $order->getPaymentInformation()) === true
        ) {
            $logContext = ['transactionId' => $paymentInfo['mangopay_transaction_id']];

            try {
                $this->mangoPayLogger->start();

                $transaction = $this->api->PayIns->Get($paymentInfo['mangopay_transaction_id']);

                $this->mangoPayLogger->requestSuccess($this->api->getHttpClient(), $logContext);
            } catch (\Throwable $exception) {
                $this->mangoPayLogger->requestException(
                    $this->api->getHttpClient(),
                    $exception,
                    $logContext
                );

                throw new \Exception(
                    "Mangopay: unable to get PayIn information, transaction: {$paymentInfo['mangopay_transaction_id']}.",
                    0,
                    $exception
                );
            }

            $subOrders = $order->getSubOrders();

            if ($subOrders) {
                foreach ($subOrders as $suborder) {
                    $resultTransfer = $this->transferSpecificOrder($suborder, $transaction);
                }
            } else {
                $resultTransfer = $this->transferSpecificOrder($order, $transaction);
            }

            /**
             * Avancement du workflow
             */
            $orders = $this->orderService->getChildOrders($orderId);

            if (\array_key_exists('status', $resultTransfer) === true
                && $resultTransfer['status'] === true
            ) {
                foreach ($orders as $orderChild) {
                    $this->dispatchFundsSucceededAction->execute($orderChild);
                }
            } else {
                $this->eventDispatcher->dispatch(
                    new DispatchFundsFailedEvent($order, $this->commissionService->getTotalCommission($order->getOrderV2())),
                    DispatchFundsFailedEvent::class
                );

                foreach ($orders as $orderChild) {
                    $this->dispatchFundsFailedAction->execute($orderChild);
                }
            }
        }
    }

    private function transferSpecificOrder(Order $order, Transaction $transaction): array
    {
        $paymentInfo = $order->getPaymentInformation();

        if (!empty($paymentInfo['mangopay_transfer_done'])) {
            $this->mangoPayLogger->log(
                AbstractPaymentLogger::ERROR_LEVEL,
                'Mangopay: transfert already done.',
                [
                    'mangopayTransactionId' => $paymentInfo['mangopay_transaction_id'],
                ]
            );

            return [
                'status' => true
            ];
        }

        [$companyUserId, $companyWalletId] = $this->getCompanyUserAndWallet($order->getCompany());

        $transfer = new Transfer();
        $transfer->AuthorId = $transaction->AuthorId;
        $transfer->DebitedWalletId = $transaction->CreditedWalletId;
        $transfer->CreditedWalletId = $companyWalletId;
        $transfer->CreditedUserId = $companyUserId;

        $transfer->DebitedFunds = $this->convertMoneyToMangopayMoney($order->getBalanceTotal());
        $transfer->Fees = $this->convertMoneyToMangopayMoney($this->commissionService->getTotalCommission($order->getOrderV2()));

        $mangoPayTransferDone = true;
        $messageException = '';

        $resultTransfer = null;
        $logContext = ['transfer' => (array) $transfer];

        try {
            $this->mangoPayLogger->start();

            $resultTransfer = $this->api->Transfers->Create($transfer);
            if ($resultTransfer->Status === 'FAILED') {
                $mangoPayTransferDone = false;
                $messageException = $resultTransfer->ResultMessage;
            }

            $this->mangoPayLogger->requestSuccess($this->api->getHttpClient(), $logContext);
        } catch (Exception $exception) {
            $this->mangoPayLogger->requestException($this->api->getHttpClient(), $exception, $logContext);

            $mangoPayTransferDone = false;
            $messageException = $exception->getMessage();
        } catch (\Throwable $exception) {
            $this->mangoPayLogger->requestException($this->api->getHttpClient(), $exception, $logContext);

            throw $exception;
        }

        $order->setPaymentInformation('mangopay_transfer_done', $mangoPayTransferDone);
        $transactionStatus = ($mangoPayTransferDone === true) ? TransactionStatus::SUCCESS() : TransactionStatus::FAILED();
        $transactionId = $resultTransfer->Id ?? uniqid('wizaplace_');

        $this->eventDispatcher->dispatch(
            new TransactionTransferEvent(
                $order->getId(),
                $order->getCompany()->getId(),
                $transfer->DebitedWalletId ?? '',
                $transfer->CreditedWalletId,
                $messageException,
                $transfer->DebitedFunds->Currency,
                $order->getBalanceTotal()->subtract(
                    $this->commissionService->getTotalCommission($order->getOrderV2())
                )->getAmount(),
                $transactionStatus,
                $transactionId,
                $this->getName()
            ),
            TransactionsTransferEventType::DISPATCH_FUNDS_TRANSFER_VENDOR()->getValue()
        );

        if ($this->commissionService->getTotalCommission($order->getOrderV2())->isPositive() === true) {
            $this->eventDispatcher->dispatch(
                new TransactionTransferEvent(
                    $order->getId(),
                    $order->getCompany()->getId(),
                    $transfer->DebitedWalletId ?? '',
                    static::DESTINATON_COMMISSION,
                    $messageException,
                    $transfer->DebitedFunds->Currency,
                    $this->commissionService->getTotalCommission($order->getOrderV2())->getAmount(),
                    $transactionStatus,
                    $transactionId,
                    $this->getName()
                ),
                TransactionsTransferEventType::DISPATCH_FUNDS_TRANSFER_COMMISSION()->getValue()
            );
        }

        return [
            'status' => $mangoPayTransferDone,
            'transactionId' => $transactionId
        ];
    }

    /**
     * @return array format: [string mangopayUserId, string WalletId, boolean newUser]
     */
    public function getCompanyUserAndWallet(Company $company): array
    {
        $oldMangopayId = $company->getMangopayId();
        $mangopayUserId = $this->checkMangopayUserId($oldMangopayId, $company->getId());
        if (false === \is_string($mangopayUserId)) {
            try {
                $mangopayUser = $this->generateMangoPayUserEntity($company);
            } catch (\Throwable $exception) {
                $this->mangoPayLogger->log(
                    AbstractPaymentLogger::ERROR_LEVEL,
                    'Mangopay: error during creation of mangopay user',
                    [
                        'exception' => $exception,
                        'data' => [
                            'oldMangopayId' => $oldMangopayId,
                            'mangopayUserId' => $mangopayUserId,
                            'company' => (array) $company,
                        ],
                    ]
                );
                throw $exception;
            }

            $logContext = ['mangopayUser' => (array) $mangopayUser];

            try {
                $this->mangoPayLogger->start();

                $mangopayUserId = $this->api->Users->Create($mangopayUser)->Id;

                $this->mangoPayLogger->requestSuccess($this->api->getHttpClient(), $logContext);
            } catch (\Throwable $exception) {
                $this->mangoPayLogger->requestException(
                    $this->api->getHttpClient(),
                    $exception,
                    $logContext
                );

                throw $exception;
            }

            $company->setMangopayId($mangopayUserId);

            // Register IBAN / BIC
            $this->registerCompanyIban($company);
        }

        $logContext = ['mangopayUserId' => $mangopayUserId];

        try {
            $this->mangoPayLogger->start();

            $wallets = $this->api->Users->GetWallets($mangopayUserId);

            $this->mangoPayLogger->requestSuccess($this->api->getHttpClient(), $logContext);
        } catch (\Throwable $exception) {
            $this->mangoPayLogger->requestException($this->api->getHttpClient(), $exception, $logContext);

            throw $exception;
        }

        if (!$wallets) {
            $wallet = new Wallet();
            $wallet->Owners = [$mangopayUserId];
            $wallet->Currency = $this->currencyCode;
            $wallet->Description = 'User wallet, owner ID: ' . $mangopayUserId;

            $logContext = ['wallet' => (array) $wallet];

            try {
                $this->mangoPayLogger->start();

                $wallet = $this->api->Wallets->Create($wallet);

                $this->mangoPayLogger->requestSuccess($this->api->getHttpClient(), $logContext);
            } catch (\Throwable $exception) {
                $this->mangoPayLogger->requestException($this->api->getHttpClient(), $exception, $logContext);

                throw $exception;
            }
        } else {
            $wallet = reset($wallets);
        }

        return [$mangopayUserId, $wallet->Id, $oldMangopayId !== $mangopayUserId];
    }

    protected function getUser(Order $order): User
    {
        $userId = $order->getUserId();

        try {
            if ($mangopayUserId = $this->userPaymentInfo->getMangopayUserId($userId)) {
                $logContext = ['mangopayUserId' => $mangopayUserId];

                try {
                    $this->mangoPayLogger->start();

                    $mangoUser = $this->api->Users->Get($mangopayUserId);

                    $this->mangoPayLogger->requestSuccess($this->api->getHttpClient(), $logContext);
                } catch (\Throwable $exception) {
                    $this->mangoPayLogger->requestException($this->api->getHttpClient(), $exception, $logContext);

                    throw $exception;
                }

                if ($mangoUser instanceof User) {
                    return $mangoUser;
                }
            }
        // If the user has been deleted by Mangopay, we continue with the creation process
        } catch (ResponseException $e) {
            if ($e->getCode() != 404) {
                throw $e;
            }
            $this->mangoPayLogger->log(
                AbstractPaymentLogger::WARNING_LEVEL,
                'Mangopay : user should exist but can\'t be retrieve',
                [
                    'exception' => $e,
                    'userId' => $userId
                ]
            );
        }

        $user = $this->userRepository->get($userId);
        $address = $user->getBillingAddress();

        try {
            $firstname = $address->getFieldValue('firstname');
        } catch (\InvalidArgumentException $e) {
            $firstname = $user->getFirstname();
        }
        try {
            $lastname = $address->getFieldValue('lastname');
        } catch (\InvalidArgumentException $e) {
            $lastname = $user->getLastname();
        }

        $birthday = 0;
        if ($user->getBirthday() instanceof \DateTime) {
            $birthday = (int) $user->getBirthday()->format('u');
        }

        if ($this->createLegalWalletsForCustomers) {
            $mangoUser = new \MangoPay\UserLegal();
            $mangoUser->Name = $firstname . ' ' . $lastname;
            $mangoUser->LegalPersonType = 'BUSINESS';
            $mangoUser->LegalRepresentativeFirstName = $firstname;
            $mangoUser->LegalRepresentativeLastName = $lastname;
            $mangoUser->LegalRepresentativeBirthday = $birthday;
            $mangoUser->LegalRepresentativeNationality = 'FR';
            $mangoUser->LegalRepresentativeCountryOfResidence = 'FR';
        } else {
            $mangoUser = new \MangoPay\UserNatural();
            $mangoUser->PersonType = "NATURAL";
            $mangoUser->FirstName = $firstname;
            $mangoUser->LastName = $lastname;
            $mangoUser->Birthday = $birthday;
            $mangoUser->Nationality = 'FR';
            $mangoUser->CountryOfResidence = 'FR';
        }

        $mangoUser->Email = $order->getEmail();

        $logContext = ['mangopayUser' => (array) $mangoUser];

        try {
            $this->mangoPayLogger->start();

            //Send the request
            $mangoUser = $this->api->Users->Create($mangoUser);

            $this->mangoPayLogger->requestSuccess($this->api->getHttpClient(), $logContext);
        } catch (\Throwable $exception) {
            $this->mangoPayLogger->requestException($this->api->getHttpClient(), $exception, $logContext);

            throw $exception;
        }

        $this->userPaymentInfo->setMangopayUserId($userId, (string) $mangoUser->Id);

        return $mangoUser;
    }

    protected function getTransactionWallet(User $user): Wallet
    {
        $logContext = ['userId' => $user->Id];

        try {
            $this->mangoPayLogger->start();

            $wallets = $this->api->Users->GetWallets($user->Id);

            $this->mangoPayLogger->requestSuccess($this->api->getHttpClient(), $logContext);
        } catch (\Throwable $exception) {
            $this->mangoPayLogger->requestException($this->api->getHttpClient(), $exception, $logContext);

            throw $exception;
        }

        if (empty($wallets)) {
            $wallet = new Wallet();
            $wallet->Owners = [$user->Id];
            $wallet->Currency = $this->currencyCode;
            $wallet->Description = 'User wallet, owner ID: ' . $user->Id;

            $logContext = ['wallet' => (array) $wallet];

            try {
                $this->mangoPayLogger->start();

                $wallet = $this->api->Wallets->Create($wallet);

                $this->mangoPayLogger->requestSuccess($this->api->getHttpClient(), $logContext);
            } catch (\Throwable $exception) {
                $this->mangoPayLogger->requestException($this->api->getHttpClient(), $exception, $logContext);

                throw $exception;
            }

            return $wallet;
        }

        return reset($wallets);
    }

    protected function convertMoneyToMangopayMoney(Money $moneyIn): MangoPayMoney
    {
        $moneyOut = new MangoPayMoney();
        $moneyOut->Currency = $this->currencyCode;
        $moneyOut->Amount = $moneyIn->getAmount();
        return $moneyOut;
    }

    public static function getSubscribedEvents()
    {
        return [
            OrderEvents::UPDATED => ['onOrderUpdate', 0],
            CompanyEvents::UPDATED => ['onCompanyUpdate', 0],
            CompanyEvents::SEND_TO_PSP => ['onCompanyUpdate', 0],
            CompanyEvents::IBAN_BIC_UPDATED => ['onCompanyIbanBicUpdated', 0],
            CompanyEvents::APPROVED => ['sendKycs', 0],
            CompanyEvents::LEGAL_DOCUMENTS_UPDATED => ['onKycUpdated', 0],
            CompanyPersonEvents::SUBMITTED => ['submitUBO', 0]
        ];
    }

    public function onOrderUpdate(OrderStatusUpdated $event)
    {
        if ($this->isConfigured() && $event->getStatusTo() === OrderStatus::COMPLETED) {
            $this->transferFundsToVendor($event->getId());
        }
    }

    public function onCompanyUpdate(IterableEvent $event)
    {
        if ($this->isConfigured()) {
            foreach ($event as $companyId) {
                $company = new Company($companyId);
                $company->setSkipCache(true);

                $this->mangoPayLogger->log(
                    AbstractPaymentLogger::INFO_LEVEL,
                    'Mangopay: company updated',
                    [
                        'companyId' => $companyId,
                        'companyStatus' => $company->getStatus(),
                    ]
                );

                if ($this->companyService->canCreatePspAccount($company)) {
                    if (\is_string($company->getMangopayId())
                        && $this->checkMangopayUserId($company->getMangopayId(), $company->getId())
                    ) {
                        $this->updateCompanyUser($company);
                    } else {
                        $this->getCompanyUserAndWallet($company);
                    }
                }
            }
        }
    }

    public function onCompanyIbanBicUpdated(IterableEvent $event)
    {
        if ($this->isConfigured()) {
            foreach ($event as $companyId) {
                $company = new Company($companyId);
                $company->setSkipCache(true);

                $this->mangoPayLogger->log(
                    AbstractPaymentLogger::INFO_LEVEL,
                    'Mangopay: company iban or bic updated',
                    ['companyId' => $companyId]
                );

                // We only check if the account was created or not
                try {
                    $this->getCompanyUserAndWallet($company);
                } catch (NotFound $exception) {
                    // Le compte du vendeur au sein de la MP n'est pas créé encore, on arrête le processus
                    return;
                }

                if ($this->companyService->canRegisterIban($company)) {
                    if (\is_string($company->getMangopayId())) {
                        $this->updateCompanyIban($company);
                    } else {
                        $this->registerCompanyIban($company);
                    }
                }
            }
        }
    }

    /**
     * @internal Must be called by the event dispatcher
     */
    public function sendKycs(IterableEvent $event): void
    {
        if (false === $this->isConfigured()) {
            return;
        }

        foreach ($event as $companyId) {
            $company = new Company($companyId);
            $company->setSkipCache(true);

            $this->mangoPayLogger->log(
                AbstractPaymentLogger::INFO_LEVEL,
                'Mangopay: company was approved',
                ['companyId' => $companyId]
            );

            if ($this->companyService->canCreatePspAccount($company)) {
                try {
                    $this->doSendKyc($company);
                } catch (\Exception $exception) {
                    $this->mangoPayLogger->log(
                        AbstractPaymentLogger::ERROR_LEVEL,
                        'MangoPay: error on company approvement',
                        [
                            "code" => $exception->getCode(),
                            "message" => $exception->getMessage(),
                            "exception" => $exception,
                        ]
                    );
                }
            }
        }
    }

    public function onKycUpdated(CompanyLegalDocumentsUpdated $kyc): void
    {
        if (false === $this->isConfigured()) {
            return;
        }

        $company = new Company($kyc->getCompanyId());
        $company->setSkipCache(true);

        $this->mangoPayLogger->log(
            AbstractPaymentLogger::INFO_LEVEL,
            'Mangopay event: company wants to update kyc',
            ['companyId' => $kyc->getCompanyId()]
        );

        if ($this->companyService->canCreatePspAccount($company)) {
            try {
                $this->doSendKyc($company);
            } catch (\Exception $exception) {
                $this->mangoPayLogger->log(
                    AbstractPaymentLogger::ERROR_LEVEL,
                    'MangoPay: error on company legal documents updated',
                    [
                        "code" => $exception->getCode(),
                        "message" => $exception->getMessage(),
                        "exception" => $exception,
                    ]
                );
            }
        }
    }

    public function isConfigured(): bool
    {
        return $this->config->isConfigured();
    }

    /**
     * Créé un hook chez Mangopay sur l'évenement de réussite de paiement (PAYIN_NORMAL_SUCCEEDED)
     * C'est envoyé aussi bien pour les paiements carte que virement (avec délai de plusieurs heures / jours pour le virement)
     * Pour les paiements carte, on a pas besoin de ça car on a l'info à temps juste après le paiement. On ignorera donc les appels CB
     * On vérifie que le hook n'existe pas avant de l'enregistrer (propreté + restriction forte Mangopay)
     * Cette vérification nous permet d'appeler cette fonction avant chaque transaction par virement pour s'assurer de la présence du hook
     * @see \Wizacha\AppBundle\Controller\PaymentNotificationController::mangopayBankwireAction
     */
    private function setupPayinSucceededHook()
    {
        try {
            $this->mangoPayLogger->start();

            // Check if it already exists, because duplicates are forbidden
            /** @var \stdClass[] $hooks */
            $hooks = $this->api->Hooks->GetAll();

            $this->mangoPayLogger->requestSuccess($this->api->getHttpClient());
        } catch (\Throwable $exception) {
            $this->mangoPayLogger->requestException($this->api->getHttpClient(), $exception);

            throw $exception;
        }

        $updateExistingHook = false;
        $hook = null;
        foreach ($hooks as $hook) {
            // payin success hook is already setup
            if ($hook->EventType === EventType::PayinNormalSucceeded && $hook->Status === 'ENABLED') {
                // the hook have the correct url: ok, exit
                if ($hook->Url === $this->router->generate('mangopay_hooks', [], UrlGeneratorInterface::ABSOLUTE_URL)) {
                    return;
                } else {
                    // The hook is setup with an incorrect url, update it rather than create a new one
                    $updateExistingHook = true;
                    break;
                }
            }
        }

        $hook = $updateExistingHook ? $hook : new Hook();
        $hook->Tag = 'Wizaplace hook, Don\'t edit this manually';
        $hook->EventType = EventType::PayinNormalSucceeded;
        $hook->Url = $this->router->generate('mangopay_hooks', [], UrlGeneratorInterface::ABSOLUTE_URL);

        $logContext = ['hook' => (array) $hook];

        try {
            $this->mangoPayLogger->start();

            if ($updateExistingHook) {
                $this->api->Hooks->Update($hook);
            } else {
                $this->api->Hooks->Create($hook);
            }

            $this->mangoPayLogger->requestSuccess($this->api->getHttpClient(), $logContext);
        } catch (ResponseException $exception) {
            $this->mangoPayLogger->requestException($this->api->getHttpClient(), $exception, $logContext);
        } catch (\Throwable $exception) {
            $this->mangoPayLogger->requestException($this->api->getHttpClient(), $exception, $logContext);

            throw $exception;
        }
    }

    public function getPayoutBalance(Company $company): ?int
    {
        $mangopayUserId = $this->getCompanyUserAndWallet($company)[0];

        $logContext = ['mangopayUserId' => $mangopayUserId];

        try {
            $this->mangoPayLogger->start();

            $wallet = $this->api->Users->GetWallets($mangopayUserId)[0];

            $this->mangoPayLogger->requestSuccess($this->api->getHttpClient(), $logContext);
        } catch (\Throwable $exception) {
            $this->mangoPayLogger->requestException($this->api->getHttpClient(), $exception, $logContext);

            throw $exception;
        }

        return $wallet->Balance->Amount;
    }

    public function assertCanDoPayoutCompany(Company $company): void
    {
        parent::assertCanDoPayoutCompany($company);

        $mangopayUserId = $this->getCompanyUserAndWallet($company)[0];

        if (!$mangopayUserId) {
            throw new \InvalidArgumentException('Wizaplace: Company does not have an account');
        }

        $logContext = ['mangopayUserId' => $mangopayUserId];

        try {
            $this->mangoPayLogger->start();

            $wallet = $this->api->Users->GetWallets($mangopayUserId)[0];

            $this->mangoPayLogger->requestSuccess($this->api->getHttpClient(), $logContext);
        } catch (\Throwable $exception) {
            $this->mangoPayLogger->requestException($this->api->getHttpClient(), $exception, $logContext);

            throw $exception;
        }

        if ($wallet->Balance->Amount < 50) {
            throw new \InvalidArgumentException('Mangopay: Balance under 0.5€');
        }

        $iban = $company->getIban();

        if (null === $iban) {
            throw new \InvalidArgumentException('Wizaplace: No IBAN recorded');
        }

        $bankAccount = $this->getBankAccount($company);
        if (!$bankAccount instanceof BankAccount) {
            // On essaye de le créer
            if ($this->companyService->canRegisterIban($company)) {
                $this->registerCompanyIban($company);
            }

            // Et si même avec ça on a pas pu le créer on lève une erreur
            // On a besoin de le re-récupérer car nous avons besoin de son ID
            $bankAccount = $this->getBankAccount($company);
            if (!$bankAccount instanceof BankAccount) {
                $messageException = sprintf(
                    'Mangopay: Company does not have a bank account with IBAN : %s',
                    $iban
                );

                $this->eventDispatcher->dispatch(
                    new TransactionTransferEvent(
                        null,
                        $company->getId(),
                        '',
                        '',
                        $messageException,
                        $this->currencyCode,
                        $wallet->Balance->Amount,
                        TransactionStatus::FAILED(),
                        uniqid('wizaplace_'),
                        $this->getName()
                    ),
                    TransactionsTransferEventType::VENDOR_WITHDRAWAL()->getValue()
                );
                throw new \InvalidArgumentException(
                    $messageException
                );
            }
        }

        $payout = new PayOut();
        $payout->PaymentType = PayOutPaymentType::BankWire;
        $payout->AuthorId = $mangopayUserId;
        $payout->DebitedFunds = $wallet->Balance;
        $payout->DebitedWalletId = $wallet->Id;
        $payout->Fees = $this->convertMoneyToMangopayMoney(new Money(0));
        $payout->MeanOfPaymentDetails = new PayOutPaymentDetailsBankWire();
        $payout->MeanOfPaymentDetails->BankAccountId = $bankAccount->Id;
        $payout->MeanOfPaymentDetails->BankWireRef = date('Ymd');
        $messageException = '';
        $isPayoutDone = true;
        $payoutException = null;

        $logContext = ['payout' => (array) $payout];

        try {
            $this->mangoPayLogger->start();

            $this->api->PayOuts->Create($payout);

            $this->mangoPayLogger->requestSuccess($this->api->getHttpClient(), $logContext);
        } catch (ResponseException $exception) {
            $this->mangoPayLogger->requestException($this->api->getHttpClient(), $exception, $logContext);
            $isPayoutDone = false;
            $messageException = $exception->getMessage();
            $payoutException = $exception;
        } catch (\Throwable $exception) {
            $this->mangoPayLogger->requestException($this->api->getHttpClient(), $exception, $logContext);

            throw $exception;
        }

        $this->eventDispatcher->dispatch(
            new TransactionTransferEvent(
                null,
                $company->getId(),
                $wallet->Id,
                $company->getName() . ' bank account',
                $messageException,
                $payout->DebitedFunds->Currency,
                $payout->DebitedFunds->Amount,
                ($isPayoutDone === true) ? TransactionStatus::SUCCESS() : TransactionStatus::FAILED(),
                uniqid('wizaplace_'),
                $this->getName()
            ),
            TransactionsTransferEventType::VENDOR_WITHDRAWAL()->getValue()
        );

        if ($isPayoutDone === false) {
            throw new \Exception(
                sprintf(
                    'Mangopay: Cannot create payout - exception: %s - data: %s',
                    $payoutException,
                    var_export($payout, true)
                )
            );
        }
    }

    public function canDoPayoutCompany(): bool
    {
        return true;
    }

    public function isCompanyActivated(int $companyId): bool
    {
        $company = new Company($companyId);

        return false === \is_null($this->checkMangopayUserId($company->getMangopayId(), $company->getId()));
    }

    public function validateKyc(int $companyId): void
    {
        $company = new Company($companyId);
        $mangopayUserId = $this->getCompanyUserAndWallet($company)[0];

        $kycMangoPay = $this->api->Users->GetKycDocuments($mangopayUserId);

        $kycStatus = [
            KycDocumentStatus::Refused => 0,
            KycDocumentStatus::Validated => 0,
            KycDocumentStatus::Created => 0,
            KycDocumentStatus::ValidationAsked => 0,
        ];
        $kycReasons = [];

        /** @var KycDocument $kyc */
        foreach ($kycMangoPay as $kyc) {
            if (\array_key_exists($kyc->Status, $kycStatus)) {
                if ($company->isPrivateIndividual() && $kyc->Type !== KycDocumentType::IdentityProof) {
                    continue;
                }

                ++$kycStatus[$kyc->Status];

                if ($kyc->Status === KycDocumentStatus::Refused) {
                    $kycReasons[] = $this->getRefusedReasonMessage($kyc);
                }
            }
        }

        $statusFrom = $company->getStatus();
        $status = CompanyStatus::PENDING();

        if ($kycStatus[KycDocumentStatus::Refused] > 0) {
            $status = CompanyStatus::DISABLED();
        } elseif ($kycStatus[KycDocumentStatus::Refused] === 0
            && $kycStatus[KycDocumentStatus::Validated] > 0
            && ($kycStatus[KycDocumentStatus::Created] + $kycStatus[KycDocumentStatus::ValidationAsked]) === 0
        ) {
            $status = CompanyStatus::ENABLED();
        }

        //check UBO before updating companies status
        try {
            $status = $this->validateUBO($companyId, $mangopayUserId, $status)->getValue();
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }

        if ($status === $statusFrom) { // Si le status est le même pas besoin d'éditer la company
            return;
        }

        if (false === fn_companies_change_status($companyId, $status, implode(", ", $kycReasons), $statusFrom)) {
            $this->mangoPayLogger->log(
                AbstractPaymentLogger::ERROR_LEVEL,
                'MangoPay: cannot update company status.',
                [
                    'companyId' => $companyId,
                    'status' => $status,
                    'reason' => implode(", ", $kycReasons),
                    'statusFrom' => $statusFrom,
                ]
            );

            throw new \Exception("Cannot update status of company #{$companyId}");
        }
    }

    public function getHumanStatus($status): TransactionStatus
    {
        switch ($status) {
            case static::STATUS_CREATED:
                $transactionStatus = TransactionStatus::PENDING();
                break;
            case static::STATUS_SUCCEEDED:
                $transactionStatus = TransactionStatus::SUCCESS();
                break;
            case static::STATUS_FAILED:
                $transactionStatus = TransactionStatus::FAILED();
                break;
            default:
                $transactionStatus = null;
                break;
        }

        if (\is_null($status)) {
            throw new \Exception('Unable to map MangoPay status: ' . $status);
        }

        return $transactionStatus;
    }

    private function registerCompanyIban(Company $company): self
    {
        if ($this->companyService->canRegisterIban($company)) {
            $bankAccount = $this->generateBankAccount($company);

            if (\is_null($bankAccount)) {
                // Account already exist, skip
                return $this;
            }

            $logContext = ['bankAccount' => (array) $bankAccount];

            try {
                $this->mangoPayLogger->start();

                $this->api->Users->CreateBankAccount($bankAccount->UserId, $bankAccount);

                $this->mangoPayLogger->requestSuccess($this->api->getHttpClient(), $logContext);
            } catch (ResponseException $exception) {
                $this->mangoPayLogger->requestException(
                    $this->api->getHttpClient(),
                    $exception,
                    $logContext
                );
            } catch (\Throwable $exception) {
                $this->mangoPayLogger->requestException(
                    $this->api->getHttpClient(),
                    $exception,
                    $logContext
                );

                throw $exception;
            }
        }

        return $this;
    }

    /**
     * L'envoi des KYC chez MangoPay a un fonctionnement assez particulier.
     * On doit créer le document chez eux, puis ajouter les binaries à ce document et après le soumettre à validation
     *
     * En gros, on donne des enveloppes aux vendeurs où ils vont mettre un document par enveloppe et envoyer les enveloppes
     */
    private function doSendKyc(Company $company): self
    {
        $mangopayUserId = $this->getCompanyUserAndWallet($company)[0];

        $kyc = $this->companyService->getRegistrationFilesList($company->getId(), null, true);

        if (\count($kyc) === 0) {
            throw new \LogicException(
                "MangoPay: Unable to find any KYC to send for company id: " . $company->getId()
            );
        }

        foreach (static::KYC as $name => $kycDocumentType) {
            if (\array_key_exists($name, $kyc)) {
                if ($company->isPrivateIndividual() && $kycDocumentType !== KycDocumentType::IdentityProof) {
                    continue;
                }

                $kycDocument = new KycDocument();
                $kycDocument->Tag = "KYC company {$company->getId()}";
                $kycDocument->Type = $kycDocumentType;
                $kycDocumentId = $this->api->Users->CreateKycDocument($mangopayUserId, $kycDocument)->Id;

                $kycPage = new KycPage();
                $vendorSubscriptionStorage = container()->get('Wizacha\Storage\VendorSubscriptionStorageService');
                $kycPage->File = base64_encode(
                    stream_get_contents(
                        $vendorSubscriptionStorage->readStream(
                            "{$company->getId()}/{$kyc[$name]}"
                        )
                    )
                );
                $this->api->Users->CreateKycPage($mangopayUserId, $kycDocumentId, $kycPage);

                $kycDocument = new KycDocument();
                $kycDocument->Status = KycDocumentStatus::ValidationAsked;
                $kycDocument->Id = $kycDocumentId;
                $kycDocument->UserId = $mangopayUserId;
                $this->api->Users->UpdateKycDocument($mangopayUserId, $kycDocument);
            }
        }

        return $this;
    }

    private function getRefusedReasonMessage(KycDocument $kycDocument): string
    {
        if (\is_string($kycDocument->RefusedReasonMessage)) {
            return $kycDocument->RefusedReasonMessage;
        }

        $errors = [
            KycDocumentRefusedReasonType::DocumentUnreadable => "Document Unreadable",
            KycDocumentRefusedReasonType::DocumentNotAccepted => "Document Not Accepted",
            KycDocumentRefusedReasonType::DocumentHasExpired => "Document Has Expired",
            KycDocumentRefusedReasonType::DocumentIncomplete => "Document Incomplete",
            KycDocumentRefusedReasonType::DocumentMissing => "Document Missing",
            KycDocumentRefusedReasonType::DocumentDoesNotMatchUserData => "Document Does Not Match User Data",
            KycDocumentRefusedReasonType::DocumentDoesNotMatchAccountData => "Document Does Not Match Account Data",
            KycDocumentRefusedReasonType::SpecificCase => "Specific Case",
            KycDocumentRefusedReasonType::DocumentFalsified => "Document Falsified",
            KycDocumentRefusedReasonType::UnderagePerson => "Underage Person",
            KycDocumentRefusedReasonType::TriggerPEPS => "Trigger PEPS",
            KycDocumentRefusedReasonType::TriggerSanactionLists => "Trigger Sanaction Lists",
            KycDocumentRefusedReasonType::TriggerInterpol => "Trigger Interpol",
        ];

        if (\array_key_exists($kycDocument->RefusedReasonType, $errors)) {
            return $errors[$kycDocument->RefusedReasonType];
        }

        return "Unknow Reason";
    }

    private function updateCompanyUser(Company $company): self
    {
        try {
            $mangopayUser = $this->generateMangoPayUserEntity($company, false);
        } catch (\Throwable $exception) {
            $this->mangoPayLogger->log(
                AbstractPaymentLogger::ERROR_LEVEL,
                'Mangopay: error during creation of mangopay user',
                [
                    'exception' => $exception,
                    'data' => [
                        'company' => (array) $company,
                    ],
                ]
            );
            throw $exception;
        }

        $logContext = ['mangopayUser' => (array) $mangopayUser];

        try {
            $this->mangoPayLogger->start();

            $this->api->Users->Update($mangopayUser);

            $this->mangoPayLogger->requestSuccess($this->api->getHttpClient(), $logContext);
        } catch (\Throwable $exception) {
            $this->mangoPayLogger->requestException($this->api->getHttpClient(), $exception, $logContext);

            throw $exception;
        }

        return $this;
    }

    private function updateCompanyIban(Company $company): self
    {
        $iban = $company->getIban();
        $bic = $company->getBic();
        $mangopayUserId = $company->getMangopayId();

        if (\is_string($iban) && \is_string($bic) && \is_string($mangopayUserId)) {
            $bankAccount = $this->generateBankAccount($company);

            if (\is_null($bankAccount)) {
                // Account already exist, skip
                return $this;
            }

            if (\is_string($bankAccount->Id)) {
                $logContext = ['bankAccount' => (array) $bankAccount];

                try {
                    $this->mangoPayLogger->start();

                    $this->api->Users->UpdateBankAccount($bankAccount->UserId, $bankAccount);

                    $this->mangoPayLogger->requestSuccess($this->api->getHttpClient(), $logContext);
                } catch (\Throwable $exception) {
                    $this->mangoPayLogger->requestException(
                        $this->api->getHttpClient(),
                        $exception,
                        $logContext
                    );

                    throw $exception;
                }
            }

            $this->registerCompanyIban($company);
        }

        return $this;
    }

    /**
     * @return UserNatural|UserLegal
     */
    private function generateMangoPayUserEntity(
        Company $company,
        bool $isNew = true
    ) {
        $user = $company->getFirstAdmin();
        if ($company->isPrivateIndividual()) {
            $mangopayUser = new UserNatural();
            $mangopayUser->Email = $user->getEmail();
            $mangopayUser->FirstName = $user->getFirstname();
            $mangopayUser->LastName = $user->getLastname();
            $mangopayUser->Birthday = $this->getDateAsAFakedTimestamp($user->getBirthday());

            /**
             * Voir commentaire un peu plus bas
             */
            if (true === $isNew) {
                $mangopayUser->Nationality = $user->getNationality();
                $mangopayUser->CountryOfResidence = $user->getCountryOfResidence();
            }
        } else {
            $mangopayUser = new UserLegal();
            $mangopayUser->Email = $company->getEmail();
            $mangopayUser->Name = $company->getCorporateName();
            $mangopayUser->LegalRepresentativeFirstName = $company->getLegalRepresentativeFirstname();
            $mangopayUser->LegalRepresentativeLastName = $company->getLegalRepresentativeLastname();
            $mangopayUser->LegalRepresentativeBirthday = $this->getDateAsAFakedTimestamp($user->getBirthday());

            /**
             * User::getNationality() et User::getCountryOfResidence() retournant 'FR' en dur ...
             *
             * Dans le cas d'update, on ne cherche pas à renvoyer ces données, pour ne pas risquer d'écraser ce qui
             * aurait été corrigé à la main par l'opérateur dans le BO de Mangopay (en attendant une évolution
             * qui permetterait de mapper correctement nos données de company avec celle enregistré chez Mangopay).
             *
             */
            if (true === $isNew) {
                $mangopayUser->LegalRepresentativeNationality = $user->getNationality();
                $mangopayUser->LegalRepresentativeCountryOfResidence = $user->getCountryOfResidence();
                /**
                 * Et hop on rajoute LegalPersonType qui lui non plus ne mappe pas correctement avec Wizaplace.
                 * (ex.: SOLETRADER)
                 */
                $mangopayUser->LegalPersonType = 'BUSINESS';
            }

            $mangopayUser->CompanyNumber = $company->getSiretNumber();
            // headquartersAddress
            $address = new Address();
            $address->Country = $company->getCountry();
            $address->City = $company->getCity();
            $address->PostalCode = $company->getZipcode();
            [$address->AddressLine1, $address->AddressLine2] = $this->getCompanyAddressLines($company);
            $mangopayUser->HeadquartersAddress = $address;
            $mangopayUser->LegalRepresentativeAddress = $address;

            $mangopayUser->LegalRepresentativeEmail = $company->getEmail();
        }

        if (\is_string($company->getMangopayId())) {
            $mangopayUser->Id = $company->getMangopayId();
        }

        return $mangopayUser;
    }

    /**
     * Get the company address in a Mangopay compatible format
     */
    private function getCompanyAddressLines(Company $company): array
    {
        $addressLines = \preg_split(
            '/\R/',
            $company->getAddress()
        );

        $normalizedLines = \array_filter(
            \array_map(
                'trim',
                $addressLines
            )
        );

        return [
            \array_shift($normalizedLines),
            \implode(' ', $normalizedLines),
        ];
    }

    private function generateBankAccount(Company $company): ?BankAccount
    {
        $iban = $company->getIban();
        $bic = $company->getBic();
        $mangopayUserId = $company->getMangopayId();
        $bankAccountId = null;

        $logContext = ['mangopayUserId' => $mangopayUserId];

        try {
            $this->mangoPayLogger->start();

            $bankAccounts = $this->api->Users->GetBankAccounts($mangopayUserId);

            $this->mangoPayLogger->requestSuccess($this->api->getHttpClient(), $logContext);
        } catch (\Throwable $exception) {
            $this->mangoPayLogger->requestException($this->api->getHttpClient(), $exception, $logContext);

            throw $exception;
        }

        /** @var BankAccount $bankAccount */
        foreach ($bankAccounts as $bankAccount) {
            if (true === $bankAccount->Active) {
                if ($bankAccount->Details instanceof BankAccountDetailsIBAN
                    && $bankAccount->Details->IBAN === $iban
                    && $bankAccount->Details->BIC === $bic
                ) {
                    // Account already exist, skip
                    return null;
                } else {
                    $bankAccountId = $bankAccount->Id;
                }
            }
        }

        [$addressLine1, $addressLine2] = $this->getCompanyAddressLines($company);

        $bankAccount = new BankAccount();
        if (\is_string($bankAccountId)) {
            $bankAccount->Id = $bankAccountId;
        }
        $bankAccount->UserId = $mangopayUserId;
        $bankAccount->OwnerAddress = new Address();
        $bankAccount->OwnerAddress->AddressLine1 = $addressLine1;
        $bankAccount->OwnerAddress->AddressLine2 = $addressLine2;
        $bankAccount->OwnerAddress->City = $company->getCity();
        $bankAccount->OwnerAddress->Country = $company->getCountry();
        $bankAccount->OwnerAddress->PostalCode = $company->getZipcode();
        $bankAccount->Details = new BankAccountDetailsIBAN();
        $bankAccount->Details->IBAN = $iban;
        $bankAccount->Details->BIC = $bic;

        if ($company->isPrivateIndividual()) {
            $user = $company->getFirstAdmin();
            $bankAccount->OwnerName = $user->getFirstname() . ' ' . $user->getLastname();
        } else {
            $bankAccount->OwnerName = $company->getName();
        }

        return $bankAccount;
    }

    protected function getBankAccount(Company $company): ?BankAccount
    {
        $iban = $company->getIban();
        $bankAccount = null;

        $logContext = ['mangopayUserId' => $company->getMangopayId()];

        try {
            $this->mangoPayLogger->start();

            $bankAccounts = $this->api->Users->GetBankAccounts($company->getMangopayId());

            $this->mangoPayLogger->requestSuccess($this->api->getHttpClient(), $logContext);
        } catch (\Throwable $exception) {
            $this->mangoPayLogger->requestException($this->api->getHttpClient(), $exception, $logContext);

            throw $exception;
        }

        /** @var BankAccount $bankAccount */
        foreach ($bankAccounts as $tmpBankAccount) {
            if ($tmpBankAccount->Details instanceof BankAccountDetailsIBAN
                && $tmpBankAccount->Details->IBAN === $iban
            ) {
                $bankAccount = $tmpBankAccount;
                break;
            }
        }

        return $bankAccount;
    }

    protected function checkMangopayUserId(?string $mangopayUserId, int $companyId): ?string
    {
        if (\is_string($mangopayUserId)) {
            $logContext = ['mangopayUserId' => $mangopayUserId, 'companyId' => $companyId];

            try {
                $this->mangoPayLogger->start();

                $this->api->Users->Get($mangopayUserId);

                $this->mangoPayLogger->requestSuccess($this->api->getHttpClient(), $logContext);
            } catch (ResponseException $exception) {
                $this->mangoPayLogger->requestWarning(
                    'Mangopay user not found. Create new account',
                    $this->api->getHttpClient(),
                    $exception,
                    $logContext
                );

                $mangopayUserId = null;
            } catch (\Throwable $exception) {
                $this->mangoPayLogger->requestException($this->api->getHttpClient(), $exception, $logContext);

                throw $exception;
            }
        }

        return $mangopayUserId;
    }

    /** @param mixed[] $information Additional information, e.g. credited wallet ID */
    private function saveCreditCardTransaction(
        OrderPricesInterface $order,
        string $transactionId,
        array $information
    ): WizachaTransaction {
        // Keep legacy transactions persistence to prevent unwanted BC break
        fn_update_order_payment_info($order->getId(), ['mangopay_transaction_id' => $transactionId]);

        $transaction = $this
            ->transactionService->createCreditCardTransaction(
                $order->getId(),
                $order->getCustomerTotal(),
                $this->getName(),
                $information
            )
            ->setTransactionReference($transactionId)
        ;

        //Set Origin and Destination
        $user = $this->userRepository->get($order->getUserId());
        $transaction->setOrigin($user->getUserId() . ' ' . $user->getFirstname() . ' ' . $user->getLastname());
        if (\array_key_exists('credited_wallet', $information) === true) {
            $transaction->setDestination($information['credited_wallet']);
        }

        $transaction->setCurrency($this->currencyCode);

        $this->transactionService->save($transaction);

        return $transaction;
    }

    /** @param mixed[] $information Additional information, e.g. credited wallet ID */
    private function saveBankWireTransaction(
        OrderPricesInterface $order,
        string $transactionId,
        array $information
    ): WizachaTransaction {
        // Keep legacy transactions persistence to prevent unwanted BC break
        fn_update_order_payment_info(
            $order->getId(),
            [
                'mangopay_transaction_id' => $transactionId,
                'mangopay_bankwire' => true,
            ]
        );

        $transaction = $this->transactionService->createBankWireTransaction(
            $order->getId(),
            $order->getCustomerTotal(),
            $this->getName(),
            $information
        );

        $transaction->setStatus(TransactionStatus::PENDING());
        $transaction->setTransactionReference($transactionId);
        $transaction->setTransactionLabel($transaction->getInformation('label'));

        $this->transactionService->save($transaction);

        return $transaction;
    }

    public function getTransactionDetailsByOrderId(int $orderId): ?TransactionDetails
    {
        $transactions = $this->transactionService->findBy(
            [
                'orderId' => $orderId,
                'processorName' => $this->getName(),
            ]
        );

        if (\count($transactions) === 0) {
            return null;
        }

        $transactionRef = $transactions[0]->getTransactionReference();

        return $this->getTransactionDetails($transactionRef);
    }

    /**
     * Check whether the transaction status is successful.
     *
     * @param TransactionDetails $transaction
     *
     * @return bool
     *
     * @throws \Exception
     */
    public function isTransactionDetailsSuccessful(TransactionDetails $transaction): bool
    {
        return $this->getHumanStatus($transaction->getStatus())->equals(TransactionStatus::SUCCESS());
    }

    /**
     * Check whether the transaction status if failed.
     *
     * @param TransactionDetails $transaction
     *
     * @return bool
     *
     * @throws \Exception
     */
    public function isTransactionDetailsFailed(TransactionDetails $transaction): bool
    {
        return $this->getHumanStatus($transaction->getStatus())->equals(TransactionStatus::FAILED());
    }

    public function getName(): PaymentProcessorName
    {
        return PaymentProcessorName::MANGOPAY();
    }

    public function refund(WizachaTransaction $originTransaction, WizachaRefund $refund): WizachaTransaction
    {
        return $this->refundTransaction(
            $originTransaction,
            $refund,
            function (WizachaTransaction $transaction) use ($originTransaction, $refund): WizachaTransaction {
                try {
                    $amount = $refund->getAmount()->reducePrecisionToCents();

                    $refund = $this->buildRefund(
                        $originTransaction,
                        $amount->getAmount(),
                        $refund->getMessage()
                    );

                    $logContext = [
                        'transactionReference' => $originTransaction->getTransactionReference(),
                        'refund' => (array) $refund,
                    ];

                    try {
                        $this->mangoPayLogger->start();

                        $refund = $this->api->PayIns->CreateRefund(
                            $originTransaction->getTransactionReference(),
                            $refund
                        );

                        $this->mangoPayLogger->requestSuccess($this->api->getHttpClient(), $logContext);
                    } catch (\Throwable $exception) {
                        $this->mangoPayLogger->requestException(
                            $this->api->getHttpClient(),
                            $exception,
                            $logContext
                        );

                        throw $exception;
                    }

                    if (false === \in_array($refund->Status, [static::STATUS_SUCCEEDED,])) {
                        throw new InvalidRefundRequestException(
                            __('psp_refund_error'),
                            [
                                'processorName' => $this->getName()->getValue(),
                                'orderId' => $originTransaction->getOrderId(),
                                'transactionId' => $originTransaction->getId(),
                                'amount' => $amount->getAmount(),
                                'resultMessage' => $refund->ResultMessage,
                            ]
                        );
                    }

                    $transaction->setTransactionReference($refund->Id);
                    $transaction->addInformation('response', \json_encode($refund));
                } catch (\Exception $exception) {
                    $this->transactionService->updateTransactionStatus($transaction, TransactionStatus::FAILED());

                    throw new RefundErrorFromPspException(
                        $this->getName()->getValue(),
                        $exception->getMessage(),
                        $exception->getCode(),
                        $exception
                    );
                }

                return $transaction;
            }
        );
    }

    public function setUserIdAgentEtablissementPaiement(?string $userIdAgentEtablissementPaiement): self
    {
        $this->userIdAgentEtablissementPaiement = $userIdAgentEtablissementPaiement;

        return $this;
    }

    protected function buildRefund(
        WizachaTransaction $transaction,
        int $amount,
        ?string $message
    ): Refund {
        $logContext = ['transactionReference' => $transaction->getTransactionReference()];

        try {
            $this->mangoPayLogger->start();

            $mangoTransaction = $this->api->PayIns->Get($transaction->getTransactionReference());

            $this->mangoPayLogger->requestSuccess($this->api->getHttpClient(), $logContext);
        } catch (\Throwable $exception) {
            $this->mangoPayLogger->requestException($this->api->getHttpClient(), $exception, $logContext);

            throw $exception;
        }

        $refund = new Refund();
        $refund->InitialTransactionId = $transaction->getTransactionReference();
        $refund->InitialTransactionType = $transaction->getType()->getValue();
        $refund->RefundReason = $message;
        $refund->AuthorId = $mangoTransaction->AuthorId;

        $money = new MangoPayMoney();
        $money->Amount = $amount;
        $money->Currency = $this->currencyCode;
        $refund->DebitedFunds = $money;

        $fees = new MangoPayMoney();
        $fees->Amount = 0;
        $fees->Currency = $this->currencyCode;
        $refund->Fees = $fees;

        $creditedFunds = new MangoPayMoney();
        $creditedFunds->Amount = $money->Amount - $fees->Amount;
        $creditedFunds->Currency = $this->currencyCode;
        $refund->CreditedFunds = $creditedFunds;

        return $refund;
    }

    public function createUBODeclaration(int $userId): UboDeclaration
    {
        try {
            return $this->api->UboDeclarations->Create($userId);
        } catch (\Exception $e) {  // Log parameters which made this request fail and forward exception
            $this->mangoPayLogger->log(
                AbstractPaymentLogger::ERROR_LEVEL,
                '[MangoPay][UBO]: Cannot create UboDeclaration',
                [
                    'exception' => $e,
                    'userId' => $userId,
                ]
            );

            throw new \Exception(__('enable_reach_psp'), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function createUBO(int $userId, ?string $uboDeclarationId, Ubo $ubo): Ubo
    {
        try {
            return $this->api->UboDeclarations->CreateUbo($userId, $uboDeclarationId, $ubo);
        } catch (\Exception $e) {  // Log parameters which made this request fail and forward exception
            $this->mangoPayLogger->log(
                AbstractPaymentLogger::ERROR_LEVEL,
                '[MangoPay][UBO]: Cannot create UBO',
                [
                    'exception' => $e,
                    'userId' => $userId,
                ]
            );

            throw new \Exception(__('enable_reach_psp'), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function SubmitForValidation(int $userId, ?string $uboDeclarationId): UboDeclaration
    {
        try {
            return $this->api->UboDeclarations->SubmitForValidation($userId, $uboDeclarationId);
        } catch (\Exception $e) {
            $this->mangoPayLogger->log(
                AbstractPaymentLogger::ERROR_LEVEL,
                '[MangoPay][UBO]: Cannot submit UboDeclaration',
                [
                    'exception' => $e,
                    'userId' => $userId,
                ]
            );

            throw new \Exception(__('enable_reach_psp'), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function submitUBO(UBOSubmitted $event): void
    {
        if ($this->isConfigured() === false) {
            return;
        }

        $companyId = $event->getCompanyId();
        $company = new Company($companyId);
        $mangopayId = $company->getMangopayId();

        if ($mangopayId === null) {
            throw new \InvalidArgumentException(__('company_not_pending'));
        }

        $companyPersonList = $this->companyPersonService->getByCompanyId($companyId);

        if (\count($companyPersonList) === 0) {
            throw new CompanyPersonNotFound(__('company_with_not_ubo_added'));
        }

        try {
            $this->companyPersonService->checkPersonsAreValidToBeSubmitted($companyPersonList);
        } catch (\InvalidArgumentException $e) {
            throw $e;
        }

        //check UBO declaration is not created
        $uboDeclarationId = $this->uboMangopayService->getDeclarationId($companyPersonList[0]);
        if ($uboDeclarationId === null) {
            try {
                $uboDeclaration = $this->createUBODeclaration((int) $mangopayId);
                $uboDeclarationId = $uboDeclaration->Id;
            } catch (\Exception $e) {
                throw $e;
            }

            foreach ($companyPersonList as $companyPerson) {
                $uboMangopay = new UboMangopay();
                $uboMangopay->setDeclarationId($uboDeclarationId);
                $uboMangopay->setPerson($companyPerson->getId());
                $uboMangopay->setUboStatus(Status::INCOMPLETE()->getValue());
                $this->uboMangopayService->save($uboMangopay);
            }
        }

        foreach ($companyPersonList as $companyPerson) {
            $ubo = new Ubo();
            $ubo->FirstName = $companyPerson->getFirstName();
            $ubo->LastName = $companyPerson->getLastName();
            $ubo->Address = new Address();
            $ubo->Address->AddressLine1 = $companyPerson->getAddress();
            $ubo->Address->AddressLine2 = $companyPerson->getAddress2();
            $ubo->Address->City = $companyPerson->getCity();
            $ubo->Address->Region = $companyPerson->getState();
            $ubo->Address->PostalCode = $companyPerson->getZipCode();
            $ubo->Address->Country = $companyPerson->getCountry();
            $ubo->Nationality = $companyPerson->getNationalities()->first()->getCode();
            $ubo->Birthday = $this->getDateAsAFakedTimestamp($companyPerson->getBirthdate());
            $ubo->Birthplace = new Birthplace();
            $ubo->Birthplace->City = $companyPerson->getBirthplaceCity();
            $ubo->Birthplace->Country = $companyPerson->getBirthplaceCountry();

            try {
                $ubo = $this->createUBO((int) $mangopayId, $uboDeclarationId, $ubo);
            } catch (\Exception $e) {
                throw $e;
            }
        }

        try {
            $uboDeclaration = $this->SubmitForValidation(
                (int) $mangopayId,
                $uboDeclarationId
            );
        } catch (\Exception $e) {
            throw $e;
        }

        $listUboMangopay = $this->uboMangopayService->getByDeclaration($uboDeclarationId);

        foreach ($listUboMangopay as $uboMangopay) {
            $uboMangopay->setUboStatus(\strval($uboDeclaration->Status));
            $uboMangopay->setSubmittedDate(\DateTime::createFromFormat('U', \strval($uboDeclaration->CreationDate)));
            $this->uboMangopayService->save($uboMangopay);
        }
    }

    public function validateUBO(int $companyId, string $mangopayUserId, CompanyStatus $status): CompanyStatus
    {
        $companyPersonList = $this->companyPersonService->getByCompanyId($companyId);
        if (\count($companyPersonList) === 0) {
            return $status;
        }

        $uboMangoPay = $this->uboMangopayService->getByCompanyPerson($companyPersonList[0]);

        if ($uboMangoPay === null
            || $uboMangoPay->getUboStatus() === Status::REFUSED()->getValue()
            || $uboMangoPay->getUboStatus() === Status::VALIDATED()->getValue()
        ) {
            return $status;
        }

        $uboDeclaration = $this->api->UboDeclarations->Get($mangopayUserId, $uboMangoPay->getDeclarationId());

        foreach ($companyPersonList as $companyPerson) {
            $uboMangoPay = $this->uboMangopayService->getByCompanyPerson($companyPerson);
            $uboMangoPay->setUboStatus($uboDeclaration->Status);
            $this->uboMangopayService->save($uboMangoPay);
        }

        switch ($uboDeclaration->Status) {
            case Status::VALIDATION_ASKED():
                $this->mangoPayLogger->log(
                    AbstractPaymentLogger::INFO_LEVEL,
                    '[MangoPay][UBO]: UBO Submission in progress.',
                    [
                        'userId' => $mangopayUserId,
                        'declarationId' => $uboDeclaration->Id,
                    ]
                );

                return $status;
            case Status::VALIDATED():
                $this->mangoPayLogger->log(
                    AbstractPaymentLogger::INFO_LEVEL,
                    '[MangoPay][UBO]: UBO Submission validated.',
                    [
                        'userId' => $mangopayUserId,
                        'declarationId' => $uboDeclaration->Id,
                    ]
                );

                return $status;
            case Status::INCOMPLETE():
                $this->mangoPayLogger->log(
                    AbstractPaymentLogger::ERROR_LEVEL,
                    '[MangoPay][UBO]: UBO Submission incomplete.',
                    [
                        'userId' => $mangopayUserId,
                        'declarationId' => $uboDeclaration->Id,
                        'status' => $uboDeclaration->Status,
                        'reason' => $uboDeclaration->Reason,
                        'message' => $uboDeclaration->Message,
                    ]
                );

                return CompanyStatus::PENDING();
            case Status::REFUSED():
                $this->eventDispatcher->dispatch(
                    new UBOValidationFailed(
                        $companyId,
                        $uboMangoPay->getDeclarationId(),
                        $uboDeclaration->Status,
                        $uboDeclaration->Reason,
                        $uboDeclaration->Message
                    ),
                    UBOValidationFailed::class
                );

                $this->mangoPayLogger->log(
                    AbstractPaymentLogger::ERROR_LEVEL,
                    '[MangoPay][UBO]: UBO Submission refused.',
                    [
                        'userId'  => $mangopayUserId,
                        'declarationId' => $uboDeclaration->Id,
                        'status'  => $uboDeclaration->Status,
                        'reason'  => $uboDeclaration->Reason,
                        'message' => $uboDeclaration->Message,
                    ]
                );

                return CompanyStatus::DISABLED();
            default:
                return $status;
        }
    }

    public function getUserWalletId(int $orderId): string
    {
        $order = new Order($orderId);
        $mangoPayUserId = $this->getCompanyUserAndWallet($order->getCompany())[0];

        if (\is_string($mangoPayUserId) === false) {
            throw new \InvalidArgumentException('Wizaplace: Company does not have an account');
        }

        return $this->api->Users->GetWallets($mangoPayUserId)[0]->Id;
    }

    public function getCreditedWalletId(int $orderId): string
    {
        $order = new Order($orderId);

        return $this->getTransactionWallet($this->getUser($order))->Id;
    }

    /**
     * Mangopay dans son API ne prend pas des dates mais des timestamps. Puis il va considerer que la date de naissance
     * correspond à la date qu'il etait sur GMT à ce moment là. Nous pouvons appréciez le concept de timezone sur un timestamp
     * dans cet extrait de la documentaition :
     *      The date of birth of the user
     *          - be careful to set the right timezone (should be UTC) to avoid 00h becoming 23h
     *            (and hence interpreted as the day before)
     * Exemple de ce que fait la méthode :
     *      On part du 14/12/1985 00:00:00 +1
     *          Ca donne le timestamp ********* qui correspond au 13 décembre à Londres
     *          On rajoute les 3600 secondes de décalages entre Paris et Londres :
     *          On obtient 503366400 ce qui correspond au 14 décembre à Londres
     */
    private function getDateAsAFakedTimestamp(?\DateTime $dateTime): int
    {
        if ($dateTime === null) {
            return 0;
        }

        return $dateTime->getTimestamp() + $dateTime->getOffset();
    }
}
