<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Processor;

use GuzzleHttp\Exception\ClientException;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\RouterInterface;
use Wizacha\Marketplace\Commission\CommissionService;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Order\Action\DispatchFundsFailed;
use Wizacha\Marketplace\Payment\Event\DispatchFundsFailedEvent;
use Wizacha\Marketplace\Order\Action\DispatchFundsSucceeded;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Wizacha\Marketplace\Order\Exception\OrderNotFound;
use Wizacha\Marketplace\Order\OrderPricesInterface;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\Refund\Entity\Refund;
use Wizacha\Marketplace\Order\Refund\Repository\RefundRepository;
use Wizacha\Marketplace\Payment\ApiAwareTrait;
use Wizacha\Marketplace\Payment\Exception\PayoutException;
use Wizacha\Marketplace\Payment\Exception\RefundErrorFromPspException;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\GenericTransactionDetails;
use Wizacha\Marketplace\Payment\SMoney\Enum\SMoneyTransactionStatus;
use Wizacha\Marketplace\Payment\SMoney\SMoneyApi;
use Wizacha\Marketplace\Payment\SMoney\SMoneyException;
use Wizacha\Marketplace\Payment\TransactionDetails;
use Wizacha\Marketplace\Transaction\Transaction;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionStatus;
use Wizacha\Marketplace\Transaction\TransactionType;
use Wizacha\Marketplace\Transaction\TransactionsTransferEventType;
use Wizacha\Marketplace\Transaction\TransactionTransferEvent;
use Wizacha\Marketplace\User\UserRepository;
use Wizacha\Marketplace\User\UserTitle;
use Wizacha\Order;

/**
 * Class SMoney
 * @package Wizacha\Marketplace\Payment\Processor
 * @property SMoneyApi $api
 */
class SMoney extends AbstractProcessor implements RefundProcessorInterface
{
    use RefundableProcessorTrait;
    use ApiAwareTrait;

    protected const PRIVATE_INDIVIDUAL_USER = 1;
    protected const PROFESSIONAL_USER = 2;

    protected const KYC_URL = [
        CompanyService::ID_CARD => '/kyc/identitycontrol',
        CompanyService::ADDRESS_PROOF => '/kyc/addresscontrol',
        CompanyService::RIB => '/kyc',
        CompanyService::KBIS => '/kyc',
        CompanyService::EXTRA_ACTIVITY => '/kyc',
        CompanyService::EXTRA_LEGAL_ID_CARD => '/kyc',
        CompanyService::EXTRA_STATUS => '/kyc',
    ];

    protected CommissionService $commissionService;
    protected RouterInterface $router;
    protected UserRepository $userRepository;
    protected LoggerInterface $logger;
    /** Nom du site a afficher dans le message du virement bancaire des payout */
    protected string $payoutWebsiteName;
    private DispatchFundsFailed $dispatchFundsFailedAction;
    private OrderService $orderService;
    protected CompanyService $companyService;
    protected RefundRepository $refundRepository;
    private EventDispatcherInterface $eventDispatcher;
    private DispatchFundsSucceeded $dispatchFundsSucceededAction;

    public function __construct(
        string $payoutWebsiteName,
        CommissionService $commissionService,
        RouterInterface $router,
        UserRepository $userRepository,
        LoggerInterface $logger,
        DispatchFundsFailed $dispatchFundsFailedAction,
        OrderService $orderService,
        CompanyService $companyService,
        RefundRepository $refundRepository,
        EventDispatcherInterface $eventDispatcher,
        DispatchFundsSucceeded $dispatchFundsSucceededAction
    ) {
        $this->commissionService = $commissionService;
        $this->router = $router;
        $this->userRepository = $userRepository;
        $this->logger = $logger;
        $this->payoutWebsiteName = preg_replace('/[^a-zA-Z0-9]/', '', $payoutWebsiteName) ?: 'Wizaplace';
        $this->orderService = $orderService;
        $this->companyService = $companyService;
        $this->refundRepository = $refundRepository;
        $this->eventDispatcher =  $eventDispatcher;
        $this->dispatchFundsFailedAction = $dispatchFundsFailedAction;
        $this->dispatchFundsSucceededAction = $dispatchFundsSucceededAction;
    }

    public function isConfigured(): bool
    {
        return $this->api->isConfigured();
    }

    public function createCardTransaction(int $orderId, string $redirectUrl = null, string $cssUrl = null, bool $isCaptured = true): string
    {
        if (!empty($redirectUrl)) {
            $redirectUrl = base64_encode($redirectUrl);
        }

        $order = $this->orderService->getAnyOrder($orderId);
        $orders = $order->isParentOrder() ? $this->orderService->getChildOrders($orderId) : [$order];

        $user = $this->userRepository->get($order->getUserId());

        $callbackUrl = $this->router->generate(
            'payment_notification_smoney_card',
            [
                'payment_redirect_url' => $redirectUrl,
                'utm_nooverride' => '1',
            ],
            RouterInterface::ABSOLUTE_URL
        );

        $request = [
            'OrderId' => $this->createOrderId((string) $orderId),
            'IsMine' => false,
            'UrlReturn' => $callbackUrl,
            'Require3DS' => true,
            'Extraparameters' => [
                'SystempayLanguage' => (string) GlobalState::interfaceLocale(),
            ],
            'PayerInfo' => [
                'Name' => $user->getFullName(),
                'Mail' => $user->getEmail(),
            ],
        ];

        $information = null;

        // Commande multi vendeur, on utilise le paiement CB multi destinataire.
        // Un seul paiement et SMoney réparti direct dans les bons wallets vendeurs
        if ($order->isParentOrder()) {
            $request['Payments'] = [];

            foreach ($orders as $subOrder) {
                try {
                    $accountId = $this->getOrCreateProfessionalUser($subOrder->getCompany()->getId());
                } catch (NotFound $exception) {
                    throw new SMoneyException('Cannot create card transaction : no admin found in company.');
                } catch (\Exception $exception) {
                    $this->logger->error('SMoney: something is wrong on payment', [
                        'orderId' => $orderId,
                        'subOrder' => $subOrder,
                        'exception' => $exception,
                    ]);

                    throw new SMoneyException('Cannot create card transaction');
                }
                $information['credit_wallets'][] = $accountId;
                $request['Payments'][] = [
                    'OrderId' => $this->createOrderId((string) $subOrder->getId()),
                    'Amount' => $subOrder->getCustomerTotal()->getAmount(),
                    'Beneficiary' => [
                        'AppAccountId' => $accountId,
                    ],
                ];
            }
        } else { // Commande mono vendeur, on envoit direct dans le wallet du vendeur
            try {
                $accountId = $this->getOrCreateProfessionalUser($order->getCompany()->getId());
            } catch (NotFound $exception) {
                throw new SMoneyException('Cannot create card transaction : no admin found in company.');
            } catch (\Exception $exception) {
                $this->logger->error('SMoney: something is wrong on payment', [
                    'orderId' => $order->getId(),
                    'exception' => $exception,
                ]);

                throw new SMoneyException('Cannot create card transaction');
            }

            $information['credit_wallet'] = $accountId;
            $request['Beneficiary'] = [
                'AppAccountId' => $accountId,
            ];
            $request['Amount'] = $order->getCustomerTotal()->getAmount();
        }

        try {
            $response = $this->api->requestApi('post', 'payins/cardpayments', $request, 2);
        } catch (ClientException $exception) {
            throw $this->api->convertGuzzleClientException($exception, $order->getCompany()->getId(), $request);
        }

        if ($response['ErrorCode'] !== 0) {
            throw new SMoneyException('Cannot create card transaction (see exception code)', (int) $response['ErrorCode']);
        }

        if (empty($response['Href'])) {
            throw new SMoneyException('Cannot create card transaction: Href is empty');
        }

        foreach ($orders as $order) {
            $this->saveCreditCardTransaction($order, (string) $response['Id'], $information);
        }

        return $response['Href'];
    }

    public function getTransactionDetails(string $orderId): TransactionDetails
    {
        try {
            // If order is a child, we get parent order id
            $parentOrderId = $this->orderService->getParentOrderId((int) $orderId);
            if (0 !== $parentOrderId) {
                $orderId = (string) $parentOrderId;
            }
        } catch (OrderNotFound $exception) {
            $this->logger->warning('Attempt to get an order with an invalid id');
        }

        try {
            $response = $this->api->requestApi('get', 'payins/cardpayments/' . $this->createOrderId($orderId), [], 2);
        } catch (ClientException $e) {
            $this->logger->error(
                'An error occurred while trying to retrieve SMoney transaction [orderId=' . $orderId . ']',
                [
                    'exception' => $e,
                ]
            );
            throw $this->api->convertGuzzleClientException($e, $this->orderService->getAnyOrder((int) $orderId)->getCompanyId());
        }

        return new GenericTransactionDetails($response['Id'], $orderId, $response['Status'], $response);
    }

    //Not used yet
    public function createSepaTransaction(int $orderId): AbstractProcessor
    {
        $this->logger->alert("Method used but not implemented yet SMoney->createSepaTransaction($orderId)");

        return $this;
    }

    public function isTransactionSuccessful(string $orderId): bool
    {
        try {
            $transaction = $this->getTransactionDetails($orderId);
        } catch (SMoneyException $e) {
            return false;
        }

        // Confirmé par SMoney au téléphone. Peut importe la valeur du champ ErrorCode c'est Status qui prends le dessus
        return $transaction->getStatus() === 1;
    }

    public function isTransactionDetailsCancelled(TransactionDetails $transactionDetails): bool
    {
        return false;
    }

    public function refund(Transaction $originTransaction, Refund $refund): Transaction
    {
        return $this->refundTransaction(
            $originTransaction,
            $refund,
            function (Transaction $transaction) use ($refund): Transaction {
                try {
                    $amount = $refund->getAmount()->reducePrecisionToCents();
                    $parentOrderId = $this->getParentOrderId($transaction->getOrderId());

                    $response = $this->api->refund(
                        $amount->getAmount(),
                        $this->createOrderId((string) $transaction->getOrderId()),
                        0,
                        $this->countSmoneyRefund($refund),
                        $parentOrderId
                    );

                    $transaction->setTransactionReference((string) $response['Id']);
                    $transaction->addInformation('response', $response);
                } catch (SMoneyException $exception) {
                    $this->transactionService->updateTransactionStatus($transaction, TransactionStatus::FAILED());

                    throw new RefundErrorFromPspException(
                        $this->getName()->getValue(),
                        $exception->getMessage(),
                        $exception->getCode(),
                        $exception
                    );
                }

                return $transaction;
            }
        );
    }

    public function countSmoneyRefund(Refund $currentRefund): int
    {
        $order = $this->orderService->getOrder($currentRefund->getOrderId());

        return \count($this->refundRepository->findPreviousRefunds($order, $currentRefund));
    }

    public function checkKyc(int $companyId): void
    {
        try {
            $response = $this->api->requestApi('get', 'users/company-' . $companyId . '/kyc');

            if (empty($response)) {
                return;
            }

            $company = new Company($companyId);
            $reason = "";
            $statusFrom = $company->getStatus();
            $status = null;

            // On ne tien compte que des statuts Refusé et Accepté pour changer le statuts des compagnies
            switch ($response[0]['Status']) {
                case 2:
                    $reason = $response[0]['Reason'];
                    $status = \Wizacha\Company::STATUS_DISABLED;
                    break;

                case 3:
                    $status = \Wizacha\Company::STATUS_ENABLED;
                    break;
            }

            if (!\is_null($status)) {
                if (!fn_companies_change_status($companyId, $status, $reason, $statusFrom)) {
                    $this->logger->error('SMoney: cannot update status', [
                        'companyId'  => $companyId,
                        'status'     => $status,
                        'reason'     => $reason,
                        'statusFrom' => $statusFrom,
                        'response'   => $response,
                    ]);

                    throw new \Exception("cannot update status for company #{$companyId}");
                }
            }
        } catch (ClientException $e) {
            throw $this->api->convertGuzzleClientException($e, $companyId);
        }
    }

    public function getOrderId(Request $request): ?int
    {
        $orderId = $request->get('id', $request->get('Id'));
        if (1 !== preg_match("/^{$this->payoutWebsiteName}_order_([0-9]+)$/", $orderId, $matches)) {
            return null;
        }

        return (int) $matches[1];
    }

    public function createOrderId(string $orderId): string
    {
        return $this->payoutWebsiteName . "_order_" . $orderId;
    }

    public function getPayoutBalance(Company $company): ?int
    {
        $appUserId = "company-{$company->getId()}";

        return $this->api->getAmountFromSubAccount($appUserId);
    }

    public function assertCanDoPayoutCompany(Company $company): void
    {
        parent::assertCanDoPayoutCompany($company);

        $appUserId = "company-{$company->getId()}";

        $date = (new \DateTime())->format("Ymd-his");
        $orderId = "Payout-{$date}";

        $amount = $this->api->getAmountFromSubAccount($appUserId);

        if ($amount > 0) {
            $this->api->payoutRequest($appUserId, $amount, $orderId, $this->generateMotif($orderId), $company->getId(), $company->getName());
        }
    }

    public function canDoPayoutCompany(): bool
    {
        return true;
    }

    public function sendKyc(int $companyId): void
    {
        $company = new Company($companyId);
        $appUserId = "company-{$companyId}";

        try {
            $this->api->requestApi('get', 'users/' . $appUserId);
        } catch (ClientException $e) {
            // Return si la company n'existe pas encore dans S-money
            return;
        }

        try {
            $response = $this->api->requestApi('get', 'users/' . $appUserId . '/kyc');
        } catch (ClientException $exception) {
            throw $this->api->convertGuzzleClientException($exception, $appUserId);
        }

        if (!empty($response)) {
            // Des KYC on déjà été envoyée pour cet utilisateur
            return;
        }


        $kyc = $this->companyService->getRegistrationFilesList($companyId, null, true);

        if (\count($kyc) === 0) {
            return; // Il n'y a aucun KYC à envoyer, inutile de continuer
        }

        $files = [];
        foreach (self::KYC_URL as $name => $endpoint) {
            if (\array_key_exists($name, $kyc)) {
                $file = null;

                $vendorSubscriptionStorage = container()->get('Wizacha\Storage\VendorSubscriptionStorageService');
                try {
                    $file = [
                        'name'     => $kyc[$name],
                        'contents' => $vendorSubscriptionStorage->readStream("{$companyId}/$kyc[$name]"),
                    ];
                } catch (\Exception $e) {
                    $this->logger->error("Unable to find file {$kyc[$name]} for company {$companyId}");
                }

                // phpcs:disable
                if (false === is_null($file)) {
                    if (true === in_array($name, [CompanyService::ID_CARD, CompanyService::ADDRESS_PROOF]) && true === $company->isPrivateIndividual()) {
                        try {
                            $this->api->requestFileApi('users/'.$appUserId.$endpoint, [
                                $file,
                            ]);
                        } catch (ClientException $exception) {
                            throw $this->api->convertGuzzleClientException($exception, $appUserId, [
                                'endpoint' => 'users/'.$appUserId.$endpoint,
                            ]);
                        }
                    } else {
                        $files[] = $file;
                    }
                }
                // phpcs:enable
            }
        }

        try {
            $this->api->requestFileApi('users/' . $appUserId . "/kyc", $files);
        } catch (ClientException $exception) {
            throw $this->api->convertGuzzleClientException($exception, $appUserId, [
                'endpoint' => 'users/' . $appUserId . "/kyc",
            ]);
        }
    }

    public function getHumanStatus($status): TransactionStatus
    {
        switch ($status) {
            case SMoneyTransactionStatus::WAITING()->getValue():
            case SMoneyTransactionStatus::WAITING_FOR_TRANSFER()->getValue():
            case SMoneyTransactionStatus::WAITING_FOR_VALIDATION()->getValue():
                return TransactionStatus::PENDING();
            case SMoneyTransactionStatus::CANCELLED()->getValue():
            case SMoneyTransactionStatus::FAILED()->getValue():
                return TransactionStatus::FAILED();
            case SMoneyTransactionStatus::COMPLETED()->getValue():
            case SMoneyTransactionStatus::REFUNDED()->getValue():
                return TransactionStatus::SUCCESS();
        }

        throw new \Exception('Unable to map SMoney status: ' . $status);
    }

    public function setTransactionService(TransactionService $transactionService): void
    {
        $this->transactionService = $transactionService;
    }

    public function isCompanyActivated(int $companyId): bool
    {
        try {
            $this->api->requestApi('get', 'users/company-' . $companyId);

            return true;
        } catch (ClientException $e) {
            return false;
        }
    }

    public function getOrCreateProfessionalUser(int $companyId): string
    {
        $company = new Company($companyId);

        try {
            $response = $this->api->requestApi('get', 'users/company-' . $companyId);

            return $response['AppUserId'];
        } catch (ClientException $e) {
            // User not found, skip it and create it
        }

        $user = $this->userRepository->get($company->getFirstAdmin()->getId());

        $birthdate = $user->getBirthday() instanceof \DateTimeInterface
            ? $user->getBirthday()
            : \DateTime::createFromFormat('U', "0");

        $createUser = [
            'AppUserId' => "company-{$companyId}",
            'Type'      => ($company->isPrivateIndividual()) ? self::PRIVATE_INDIVIDUAL_USER : self::PROFESSIONAL_USER,
            'Profile'   => [
                'Civility'  => $user->getTitle() instanceof UserTitle && $user->getTitle()->equals(UserTitle::MRS()) ? 1 : 0,
                'Firstname' => mb_strlen($user->getFirstname()) > 0 ? $user->getFirstname() : $company->getLegalRepresentativeFirstname(),
                'Lastname'  => mb_strlen($user->getLastname()) > 0 ? $user->getLastname() : $company->getLegalRepresentativeLastname(),
                'Birthdate' => $birthdate->format('c'),
                'Email'     => $user->getEmail(),
                'Address'   => [
                    "Street"  => $company->getAddress(),
                    "Zipcode" => $company->getZipcode(),
                    "City"    => $company->getCity(),
                    'Country' => $company->getCountry(),
                ],
            ],
        ];

        // SMoney impose un numéro de mobile...
        $phone = trim(str_replace(' ', '', $user->getPhone()));
        if (\strlen($phone) === 10 && (substr($phone, 0, 2) === '06' || substr($phone, 0, 2) === '07')) {
            $createUser['Profile']['Phonenumber'] = $phone;
        }

        if (!$company->isPrivateIndividual()) {
            $createUser['Company'] = [
                'Name'  => $company->getCorporateName(),
                'Siret' => $company->getSiretNumber(),
                'NAFCode' => $company->getNafCode(),
            ];
        }

        try {
            $response = $this->api->requestApi('post', 'users', $createUser);
        } catch (ClientException $exception) {
            throw $this->api->convertGuzzleClientException($exception, $companyId, $createUser);
        }

        $this->createSubAccount($company);

        $this->sendKyc($companyId);

        return $response['AppUserId'];
    }

    public function getTransactionDetailsByOrderId(int $orderId): ?TransactionDetails
    {
        return $this->getTransactionDetails((string) $orderId);
    }

    public function isTransactionDetailsSuccessful(TransactionDetails $transaction): bool
    {
        return $this->getHumanStatus($transaction->getStatus())->equals(TransactionStatus::SUCCESS());
    }

    public function isTransactionDetailsFailed(TransactionDetails $transaction): bool
    {
        return $this->getHumanStatus($transaction->getStatus())->equals(TransactionStatus::FAILED());
    }

    public function getName(): PaymentProcessorName
    {
        return PaymentProcessorName::SMONEY();
    }

    private function createSubAccount(Company $company): void
    {
        try {
            $this->api->requestApi('get', "users/company-{$company->getId()}/subaccounts/payout-company-{$company->getId()}");

            return; // Le compte existe déjà on ne fait rien
        } catch (ClientException $exception) {
            // On ne fait rien car cela veut dire que le sous-compte n'existe pas
        }

        $request = [
            'AppAccountId' => "payout-company-{$company->getId()}",
            'DisplayName'  => "Sub account for company-{$company->getId()}",
        ];

        try {
            $this->api->requestApi('post', "users/company-{$company->getId()}/subaccounts", $request);
        } catch (ClientException $exception) {
            throw $this->api->convertGuzzleClientException($exception, $company->getId(), $request);
        }
    }

    public function dispatchFunds(Order $order): void
    {
        $company = $order->getCompany();
        $commission = $this->commissionService->getTotalCommission($order->getOrderV2());
        $paymentInfo = $order->getPaymentInformation();
        if ($order->getTotal()->isPositive()
            && (false === \array_key_exists('smoney_dispatch_funds_done', $paymentInfo)
            || (true === \array_key_exists('smoney_dispatch_funds_done', $paymentInfo)
            && false === $paymentInfo['smoney_dispatch_funds_done']))
            && true === \array_key_exists('smoney_transaction_id', $paymentInfo)
        ) {
            $this->getOrCreateProfessionalUser($company->getId());
            $this->createSubAccount($company);

            $amount = $order->getBalanceTotal()->subtract($commission);

            $fee = $commission->getAmount();
            $orderId = 'Dispatch-' . $order->getId();

            $request = [
                'OrderId' => $orderId,
                'Beneficiary' => [
                    "AppAccountId" => "payout-company-{$company->getId()}",
                ],
                'Amount' => $amount->getAmount(),
                'Message' => $this->generateMotif($orderId),
                'Fee' => [
                    'AmountWithVAT' => $fee,
                    'VAT' => 0,
                ],
            ];

            $sMoneyDispatchFundsDone = true;
            $convertedException = null;
            $response = null;
            $messageException = '';

            try {
                if ($amount->getAmount() > 0) {
                    $response = $this->api->requestApi('post', "users/company-{$company->getId()}/payments", $request);
                } else {
                    $this->logger->warning("[SMoney] Dispatch funds, amount = 0", [
                        'data' => [
                            'request' => $request,
                            'total' => $order->getTotal()->getConvertedAmount(),
                            'commission' => $commission->getConvertedAmount(),
                        ],
                    ]);
                }
            } catch (ClientException $exception) {
                $sMoneyDispatchFundsDone = false;
                $this->eventDispatcher->dispatch(
                    new DispatchFundsFailedEvent($order, $commission),
                    DispatchFundsFailedEvent::class
                );

                $convertedException = $this->api->convertGuzzleClientException($exception, "company-" . $company->getId(), $request);
                $messageException = $convertedException->getMessage();
            }

            $order->setPaymentInformation('smoney_dispatch_funds_done', $sMoneyDispatchFundsDone);
            $orders = $this->orderService->getChildOrders($order->getId());
            $transactionStatus = ($sMoneyDispatchFundsDone === true) ? TransactionStatus::SUCCESS() : TransactionStatus::FAILED();
            $this->eventDispatcher->dispatch(
                new TransactionTransferEvent(
                    $order->getId(),
                    $company->getId(),
                    'company-' . $company->getId(),
                    "payout-company-{$company->getId()}",
                    $messageException,
                    'EUR',
                    $amount->getAmount(),
                    $transactionStatus,
                    \strval($response['Id'] ?? uniqid('wizaplace_')),
                    $this->getName()
                ),
                TransactionsTransferEventType::DISPATCH_FUNDS_TRANSFER_VENDOR()->getValue()
            );

            if ($commission->isPositive() === true) {
                $this->eventDispatcher->dispatch(
                    new TransactionTransferEvent(
                        $order->getId(),
                        $company->getId(),
                        'company-' . $company->getId(),
                        "payout-company-{$company->getId()}",
                        $messageException,
                        'EUR',
                        $fee,
                        $transactionStatus,
                        \strval($response['Id'] ?? uniqid('wizaplace_')),
                        $this->getName()
                    ),
                    TransactionsTransferEventType::DISPATCH_FUNDS_TRANSFER_COMMISSION()->getValue()
                );
            }

            if ($sMoneyDispatchFundsDone === false) {
                foreach ($orders as $subOrder) {
                    $this->dispatchFundsFailedAction->execute($subOrder);
                }
                throw $convertedException;
            }

            foreach ($orders as $subOrder) {
                $this->dispatchFundsSucceededAction->execute($subOrder);
            }
        }
    }

    public function registerCompanyIban(Company $company): void
    {
        if ($this->companyService->canRegisterIban($company)) {
            // Ensure the account exists. But we don't use the result because we need an AppUserId and not an Id
            $this->getOrCreateProfessionalUser($company->getId());

            try {
                $response = $this->api->requestApi('get', 'users/company-' . $company->getId() . '/bankaccounts');

                foreach ($response as $rib) {
                    if ($rib['Iban'] === $company->getIban()) {
                        return;
                    }
                }

                $data = [
                    'DisplayName' => 'Wizaplace-Company-' . $company->getId() . '-' . time(),
                    'Bic' => $company->getBic(),
                    'Iban' => $company->getIban(),
                    'IsMine' => true,
                ];
            } catch (ClientException $exception) {
                throw $this->api->convertGuzzleClientException($exception, $company->getId());
            }

            try {
                $this->api->requestApi('post', 'users/company-' . $company->getId() . '/bankaccounts', $data);
            } catch (ClientException $exception) {
                throw $this->api->convertGuzzleClientException($exception, $company->getId(), $data);
            }
        }
    }

    /**
     * Correspond au libellé, il sera transmis dans l’opération de virement SEPA (limité à 35 caractères)
     *
     * @param string $orderId
     *
     * @return string
     */
    public function generateMotif(string $orderId): string
    {
        return $this->payoutWebsiteName . ' ' . substr(preg_replace("/[^a-zA-Z0-9]/", "", $orderId), 0, 35);
    }

    protected function getParentOrderId(int $orderId): ?string
    {
        $order = $this->orderService->getAnyOrder($orderId);
        $parentOrderId = $order->getParentOrderId();

        return false === \is_null($parentOrderId) ? $this->createOrderId((string) $parentOrderId) : null;
    }

    /** @param mixed[] $information Additional information, e.g. credited wallet ID */
    private function saveCreditCardTransaction(OrderPricesInterface $order, string $transactionId, array $information): Transaction
    {
        // Keep legacy transactions persistence to prevent unwanted BC break
        fn_update_order_payment_info($order->getId(), ['smoney_transaction_id' => $transactionId]);

        $transaction = $this
            ->transactionService->createCreditCardTransaction(
                $order->getId(),
                $order->getCustomerTotal(),
                $this->getName(),
                $information
            )
            ->setTransactionReference($transactionId)
        ;

        //Set Origin and Destination
        $user = $this->userRepository->get($order->getUserId());
        $transaction->setOrigin($user->getUserId() . ' ' . $user->getFirstname() . ' ' . $user->getLastname());
        if (\array_key_exists('credit_wallet', $information) === true) {
            $transaction->setDestination($information['credit_wallet']);
        }

        $transaction->setCurrency('EUR');

        $this->transactionService->save($transaction);

        return $transaction;
    }
}
