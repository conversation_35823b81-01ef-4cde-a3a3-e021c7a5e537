<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Processor;

use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\TransactionDetails;
use Wizacha\Marketplace\Transaction\TransactionStatus;

interface TransactionProcessorInterface
{
    public function getTransactionDetails(string $identifier): TransactionDetails;

    public function getTransactionDetailsByOrderId(int $orderId): ?TransactionDetails;

    public function isTransactionDetailsSuccessful(TransactionDetails $transaction): bool;

    public function isTransactionDetailsFailed(TransactionDetails $transaction): bool;

    public function isTransactionDetailsCancelled(TransactionDetails $transaction): bool;

    public function getHumanStatus($status): TransactionStatus;

    public function getName(): PaymentProcessorName;
}
