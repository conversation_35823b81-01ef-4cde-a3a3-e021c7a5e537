<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Processor;

use Wizacha\Marketplace\Order\Refund\Entity\Refund;
use Wizacha\Marketplace\Transaction\Transaction;

interface RefundProcessorInterface
{
    public function refund(Transaction $originTransaction, Refund $refund): Transaction;
}
