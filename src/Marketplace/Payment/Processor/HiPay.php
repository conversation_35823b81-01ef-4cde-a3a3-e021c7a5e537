<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Processor;

use HiPay\Fullservice\Enum\Transaction\Template;
use HiPay\Fullservice\Enum\Transaction\TransactionState;
use HiPay\Fullservice\Enum\Transaction\TransactionStatus as HiPayTransactionStatus;
use HiPay\Fullservice\Exception\ApiErrorException;
use HiPay\Fullservice\Exception\UnexpectedValueException;
use HiPay\Fullservice\Gateway\Client\GatewayClient;
use HiPay\Fullservice\Gateway\Client\GatewayClientInterface;
use HiPay\Fullservice\Gateway\Model\Operation;
use HiPay\Fullservice\Gateway\Model\Request\ThreeDSTwo\AccountInfo;
use HiPay\Fullservice\Gateway\Model\Request\ThreeDSTwo\AccountInfo\Customer;
use HiPay\Fullservice\Gateway\Model\Request\ThreeDSTwo\AccountInfo\Purchase;
use HiPay\Fullservice\Gateway\Model\Request\ThreeDSTwo\AccountInfo\Shipping;
use HiPay\Fullservice\Gateway\Model\Request\ThreeDSTwo\MerchantRiskStatement;
use HiPay\Fullservice\Gateway\Model\Request\ThreeDSTwo\PreviousAuthInfo;
use HiPay\Fullservice\Gateway\Model\Transaction as HiPayTransaction;
use HiPay\Fullservice\Gateway\Request\Info\CustomerBillingInfoRequest;
use HiPay\Fullservice\Gateway\Request\Info\CustomerShippingInfoRequest;
use HiPay\Fullservice\Gateway\Request\Order\HostedPaymentPageRequest;
use HiPay\Fullservice\Gateway\Request\Order\OrderRequest;
use HiPay\Fullservice\Gateway\Request\PaymentMethod\CardTokenPaymentMethod;
use HiPay\Fullservice\Gateway\Request\PaymentMethod\SEPADirectDebitPaymentMethod;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\RouterInterface;
use Wizacha\Marketplace\Commission\CommissionService;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Order\OrderPricesInterface;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\Refund\Entity\Refund;
use Wizacha\Marketplace\Payment\AgreementInterface;
use Wizacha\Marketplace\Payment\ApiAwareInterface;
use Wizacha\Marketplace\Payment\ApiAwareTrait;
use Wizacha\Marketplace\Payment\Exception\PayoutException;
use Wizacha\Marketplace\Payment\Exception\RefundErrorFromPspException;
use Wizacha\Marketplace\Payment\GenericTransactionDetails;
use Wizacha\Marketplace\Payment\HiPay\Dsp\Enum\DeliveryTimeFrameType;
use Wizacha\Marketplace\Payment\HiPay\Dsp\Enum\DeviceChannelType;
use Wizacha\Marketplace\Payment\HiPay\Dsp\Enum\NameIndicatorStatus;
use Wizacha\Marketplace\Payment\HiPay\Dsp\Enum\PurchaseIndicatorStatus;
use Wizacha\Marketplace\Payment\HiPay\Dsp\Enum\ShippingIndicatorType;
use Wizacha\Marketplace\Payment\HiPay\Dsp\Enum\SuspiciousActivityStatus;
use Wizacha\Marketplace\Payment\HiPay\Wallet\HiPayResponseStatusCode;
use Wizacha\Marketplace\Payment\HiPay\Wallet\HiPayWalletApi;
use Wizacha\Marketplace\Payment\HiPay\Wallet\HiPayWalletRequestFailed;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\Stripe\BankAccountIsMissing;
use Wizacha\Marketplace\Payment\TransactionDetails;
use Wizacha\Marketplace\Payment\UserPaymentInfoService;
use Wizacha\Marketplace\Security\DataAnonymizer\DataAnonymizer;
use Wizacha\Marketplace\Subscription\Exception\SubscriptionException;
use Wizacha\Marketplace\Subscription\SubscriptionService;
use Wizacha\Marketplace\Transaction\Exception\TransactionNotFound;
use Wizacha\Marketplace\Transaction\Transaction;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionStatus;
use Wizacha\Marketplace\Transaction\TransactionsTransferEventType;
use Wizacha\Marketplace\Transaction\TransactionTransferEvent;
use Wizacha\Marketplace\Transaction\TransactionType;
use Wizacha\Marketplace\User\User;
use Wizacha\Marketplace\User\UserService;
use Wizacha\AppBundle\Security\User\UserService as SecurityUserService;
use Wizacha\Money\Money;
use Wizacha\Marketplace\Order\Order;

/**
 * Class HiPay
 * @package Wizacha\Marketplace\Payment\Processor
 * @property GatewayClient $api
 */
class HiPay extends AbstractProcessor implements AgreementInterface, ApiAwareInterface, RefundProcessorInterface
{
    use ApiAwareTrait;
    use RefundableProcessorTrait;

    private const ECI_RECURRING_ECOMMERCE = 9;

    /** @var string */
    private const MANDATE_DEBIT_AGREEMENT_ENDPOINT = 'v2/debit-agreement';

    /** @var string */
    private const MANDATE_STATUS_AVAILABLE = 'available';

    private const TECHNICAL_WALLET = 'Technical Wallet';

    /** @var HiPayWalletApi  */
    private $walletApi;

    /** @var CommissionService  */
    private $commissionService;

    /** @var RouterInterface  */
    private $router;

    /** @var LoggerInterface  */
    private $logger;

    /** @var OrderService  */
    private $orderService;

    /** @var string  */
    private $projectName;

    /** @var string|null  */
    private $commissionRecipientId;

    private SubscriptionService $subscriptionService;

    /** @var bool */
    private $featureSubscription;

    /** @var UserPaymentInfoService */
    private $userPaymentInfoService;

    /** @var string */
    private $cardPaymentType;

    /** @var UserService */
    protected $userService;

    protected SecurityUserService $securityUserService;

    private EventDispatcherInterface $eventDispatcher;

    private const OPERATION_SALE = 'Sale';
    private const OPERATION_AUTHORIZATION = 'Authorization';
    private const OPERATION_CAPTURE = 'capture';
    private const OPERATION_REFUND = 'refund';

    public function __construct(
        CommissionService $commissionService,
        RouterInterface $router,
        LoggerInterface $logger,
        OrderService $orderService,
        string $projectName,
        ?string $commissionRecipientId,
        SubscriptionService $subscriptionService,
        UserPaymentInfoService $userPaymentInfoService,
        string $cardPaymentType,
        UserService $userService,
        SecurityUserService $securityUserService,
        EventDispatcherInterface $eventDispatcher
    ) {
        $this->commissionService = $commissionService;
        $this->router = $router;
        $this->logger = $logger;
        $this->orderService = $orderService;
        $this->projectName = $projectName;
        $this->commissionRecipientId = $commissionRecipientId;
        $this->subscriptionService = $subscriptionService;
        $this->userPaymentInfoService = $userPaymentInfoService;
        $this->cardPaymentType = $cardPaymentType;
        $this->userService = $userService;
        $this->securityUserService = $securityUserService;
        $this->eventDispatcher =  $eventDispatcher;
    }

    public function isConfigured(): bool
    {
        return (
            true === $this->api instanceof GatewayClientInterface
            && true === \in_array(
                $this->api->getClientProvider()->getConfiguration()->getApiEnv(),
                ['stage', 'production'],
                true
            )
        );
    }

    public function setWalletApi(HiPayWalletApi $walletApi): self
    {
        $this->walletApi = $walletApi;

        return $this;
    }

    public function getWalletApi(): HiPayWalletApi
    {
        return $this->walletApi;
    }

    public function setTransactionService(TransactionService $transactionService): self
    {
        $this->transactionService = $transactionService;

        return $this;
    }

    public function setCommissionRecipientId(?string $commissionRecipientId): self
    {
        $this->commissionRecipientId = $commissionRecipientId;

        return $this;
    }

    public function setFeatureSubscription(bool $featureSubscription): self
    {
        $this->featureSubscription = $featureSubscription;

        return $this;
    }

    /**
     * @param string[] $userInfo
     * @throws ApiErrorException
     */
    public function createSepaMandate(array $userInfo): int
    {
        // Default value for creating a mandate without transaction, see Hipay doc
        $defaultField = [
            'payment_product' => "sdd",
            'currency' => "EUR",
            'authentication_indicator' => 0,
        ];

        $fieldToPost = \array_merge($userInfo, $defaultField);
        // Full endpoint for testing use https://stage-secure-gateway.hipay-tpp.com/rest/v2/debit-agreement
        try {
            $response = $this->api->getClientProvider()->request(
                'POST',
                static::MANDATE_DEBIT_AGREEMENT_ENDPOINT,
                $fieldToPost
            );
        } catch (\Exception $exception) {
            $this->logger->error('Hipay api request error :' . $exception->getMessage());
            throw new UnexpectedValueException("Creation of mandate failed internal error");
        }

        $response = $response->toArray();

        if (true === \array_key_exists('code', $response)) {
            if (0 === $response['code']) {
                return $response['debit_agreement']['id'];
            }

            throw new UnexpectedValueException("Creation of mandate failed status code : {$response['code']}");
        }

        throw new UnexpectedValueException("Creation of mandate failed status code not found");
    }

    public function sepaMandateIdIsCorrect(int $agreemmentId): bool
    {
        try {
            $response = $this->api->getClientProvider()->request(
                'GET',
                static::MANDATE_DEBIT_AGREEMENT_ENDPOINT . '/' . $agreemmentId,
                []
            );
        } catch (\Exception $exception) {
            $this->logger->error('Hipay api request error :' . $exception->getMessage());
            return false;
        }

        $status = $response->getStatusCode();
        if (true === \is_int($status) && $status === Response::HTTP_CREATED) {
            $data = $response->toArray();
            if ($data['debit_agreement']['status'] === static::MANDATE_STATUS_AVAILABLE) {
                return true;
            }
        }

        return false;
    }

    public function getSepaMandateInfo(int $agreemmentId): array
    {
        try {
            $response = $this->api->getClientProvider()->request(
                'GET',
                static::MANDATE_DEBIT_AGREEMENT_ENDPOINT . '/' . $agreemmentId,
                []
            );
        } catch (\Exception $exception) {
            $this->logger->error('Hipay api request error :' . $exception->getMessage());
            throw new BankAccountIsMissing("The mandate id can't found or does not exist");
        }

        $status = $response->getStatusCode();
        if (true === \is_int($status) && $status === Response::HTTP_CREATED) {
            $data = $response->toArray();
            if ($data['debit_agreement']['status'] !== static::MANDATE_STATUS_AVAILABLE) {
                throw new BankAccountIsMissing("The mandate status is not available");
            }

            $data['debit_agreement']['properties']['iban'] = DataAnonymizer::anonymizeIban(
                $data['debit_agreement']['properties']['iban']
            );

            return $data;
        }

        throw new BankAccountIsMissing("The mandate id can't found or does not exist");
    }

    /**
     * @throws \Wizacha\Marketplace\Order\Exception\OrderNotFound
     * @throws ApiErrorException
     */
    public function createSepaTransaction(int $orderId): AbstractProcessor
    {
        $order = $this->orderService->getAnyOrder($orderId);
        $orders = $this->orderService->getChildOrders($orderId);
        $userId = $order->getUserId();

        $debitMandateAgreement = $this->userPaymentInfoService->getHipaySepaAgreement($userId);

        if ($debitMandateAgreement === null || false === $this->sepaMandateIdIsCorrect($debitMandateAgreement)) {
            throw new BankAccountIsMissing(
                "We tried to create a SEPA transaction for the order $orderId but the user doesn't have a Hipay token"
            );
        }

        $callbackUrl = $this->router->generate(
            'payment_notification_hipay_sepa_transaction',
            [],
            RouterInterface::ABSOLUTE_URL
        );

        $request = new OrderRequest();
        $request->operation = static::OPERATION_SALE;
        $request->notify_url = $callbackUrl;
        $request->payment_product = 'sdd';

        $request->orderid = $this->encodeOrderId($orderId);
        $request->cid = $userId;
        $request->language = ((string) GlobalState::interfaceLocale()) === 'fr' ? 'fr_FR' : 'en_GB';
        $request->description = __('order') . ' ' . $orderId;
        $request->amount = $order->getCustomerTotal()->getConvertedAmount();
        $request->shipping = $order->getShippingCost()->getConvertedAmount();
        $request->accept_url = $callbackUrl;
        $request->cancel_url = $callbackUrl;
        $request->decline_url = $callbackUrl;
        $request->exception_url = $callbackUrl;
        $request->pending_url = $callbackUrl;

        $request->customerBillingInfo = $this->customerBillingInfoRequest($orders[0]);
        $request->customerShippingInfo = $this->customerShippingInfoRequest($orders[0]);

        $paymentMethod = new SEPADirectDebitPaymentMethod();
        $paymentMethod->debit_agreement_id = $debitMandateAgreement;
        $paymentMethod->recurring_payment = 1;
        $request->paymentMethod = $paymentMethod;

        $response = $this->api->requestNewOrder($request);

        foreach ($orders as $order) {
            $this->saveDirectDebitTransaction($order, $response->getTransactionReference(), ['hipay_sepa' => true]);
        }

        return $this;
    }

    public function createCardTransaction(int $orderId, string $redirectUrl = null, string $cssUrl = null, bool $isCapture = true): string
    {
        if (!empty($redirectUrl)) {
            $redirectUrl = base64_encode($redirectUrl);
        }

        $order = $this->orderService->getAnyOrder($orderId);
        $orders = $this->orderService->getChildOrders($orderId);

        $callbackUrl = $this->router->generate(
            'payment_notification_hipay_card',
            [
                'order_id' => $orderId,
                'payment_redirect_url' => $redirectUrl,
                'utm_nooverride' => '1'
            ],
            RouterInterface::ABSOLUTE_URL
        );

        $request = new HostedPaymentPageRequest();
        $request->language = ((string) GlobalState::interfaceLocale()) === 'fr' ? 'fr_FR' : 'en_GB';
        $request->orderid = $this->encodeOrderId($orderId);
        $request->cid = $order->getUserId();
        $request->payment_product_list = HipayPaymentProductList::checkCardPaymentType($this->cardPaymentType);
        $request->operation = $isCapture ? static::OPERATION_SALE : static::OPERATION_AUTHORIZATION;
        $request->description = __('order') . ' ' . $orderId;
        $request->amount = $order->getCustomerTotal()->getConvertedAmount();
        $request->shipping = $order->getShippingCost()->getConvertedAmount();
        $request->accept_url = $callbackUrl;
        $request->cancel_url = $callbackUrl;
        $request->decline_url = $callbackUrl;
        $request->exception_url = $callbackUrl;
        $request->pending_url = $callbackUrl;
        $request->notify_url = $this->router->generate(
            'payment_notification_hipay_card',
            [],
            RouterInterface::ABSOLUTE_URL
        );

        $isSubscriptionOrder = false;

        foreach ($orders as $childOrder) {
            foreach ($childOrder->getItems() as $item) {
                if (true === $item->isSubscription()) {
                    $isSubscriptionOrder = true;
                    break;
                }
            }
        }

        if (true === $isSubscriptionOrder) {
            $request->multi_use = 1;
        }

        $request->customerBillingInfo = $this->customerBillingInfoRequest($orders[0]);
        $request->customerShippingInfo = $this->customerShippingInfoRequest($orders[0]);

        if (!empty($cssUrl)) {
            $request->template = Template::BASIC_JS;
            $request->css = $cssUrl;
        }

        //add DSP2 TO Request HiPay
        $request = $this->addDsp2FieldsToRequest($request, $order);

        // Renew subscription case
        if (\is_string($orders[0]->getSubscriptionId())) {
            $subscriptionId = $orders[0]->getSubscriptionId();

            if (\count($orders) > 1) {
                throw new SubscriptionException("Unable to renew the order '" . $orderId . "' because we've got several child orders.");
            }

            $creditCard = $this->subscriptionService->get($subscriptionId)->getCreditCard();

            $request->payment_product = $creditCard->getPaymentProductCode();

            $paymentMethod = new CardTokenPaymentMethod();
            $paymentMethod->cardtoken = $creditCard->getToken();
            $paymentMethod->eci = static::ECI_RECURRING_ECOMMERCE;
            $paymentMethod->authentication_indicator = 0;

            $request->paymentMethod = $paymentMethod;

            try {
                $response = $this->api->requestNewOrder($request);
            } catch (\Exception $exception) {
                $this->logger->error('Hipay api request error', [
                    'orderId' => $orderId,
                    'message' => $exception->getMessage(),
                ]);
                throw $exception;
            }
        } else {
            try {
                $response = $this->api->requestHostedPaymentPage($request);
            } catch (\Exception $exception) {
                $this->logger->error('Hipay api request error', [
                    'orderId' => $orderId,
                    'message' => $exception->getMessage(),
                ]);
                throw $exception;
            }
        }

        foreach ($orders as $order) {
            $this->saveCreditCardTransaction($order, $response->getMid());
        }

        return $response->getForwardUrl();
    }

    protected function addDsp2FieldsToRequest(OrderRequest $request, Order $order): OrderRequest
    {
        $legacyOrder = $order->getLegacyOrder();
        $orders = $this->orderService->getChildOrders($order->getId());
        $billingAddress = $orders[0]->getBillingAddress();
        $shippingAddress = $orders[0]->getShippingAddress();
        $user = $this->userService->get($order->getUserId());

        $request->account_info = new AccountInfo();
        $request->account_info->customer = new Customer();
        $request->account_info->customer->opening_account_date = (int) \DateTime::createFromFormat('U', (string) $user->getTimestamp())->format('Ymd');
        $passwordChangeDate = \DateTime::createFromFormat('U', (string) $user->getPasswordChangeTimestamp());
        if ($passwordChangeDate instanceof \DateTime === true) {
            $request->account_info->customer->password_change = (int) $passwordChangeDate->format('Ymd');
        }

        $request->account_info->purchase = new Purchase();
        $request->account_info->purchase->count = $this->orderService->getNumberOrdersForUser($legacyOrder);
        $request->account_info->purchase->payment_attempts_24h = $this->orderService->getNumberCompleteOrdersForTransactionType($legacyOrder, TransactionType::CREDITCARD(), 1);
        $request->account_info->purchase->payment_attempts_1y = $this->orderService->getNumberCompleteOrdersForTransactionType($legacyOrder, TransactionType::CREDITCARD(), 360);

        $request->account_info->shipping = new Shipping();
        $createdAt = $this->orderService->getDateFirstOrderForShippingAddress($legacyOrder, $shippingAddress);
        if ($createdAt !== null && $createdAt !== false) {
            $request->account_info->shipping->shipping_used_date = (int) $createdAt->format('Ymd');
        }
        $request->account_info->shipping->suspicious_activity = SuspiciousActivityStatus::NO_SUSPICIOUS_OBSERVED()->getValue();
        $request->account_info->shipping->name_indicator = $shippingAddress->getFirstname() === $user->getFirstname() ? NameIndicatorStatus::NAME_ACCOUNT_IDENTICAL_TO_DELIVERY()->getValue() : NameIndicatorStatus::NAME_ACCOUNT_NOT_IDENTICAL_TO_DELIVERY()->getValue();

        $request->device_channel = DeviceChannelType::BROWSER()->getValue();

        $previousReference = $this->orderService->getPreviousTransactionReference($order->getUserId());
        if ($previousReference !== null) {
            $request->previous_auth_info = new PreviousAuthInfo();
            $request->previous_auth_info->transaction_reference = $previousReference;
        }

        $request->merchant_risk_statement = new MerchantRiskStatement();
        if ($this->orderService->hasProductIsDematerializedInOrdered($order->getId()) === true) {
            $request->merchant_risk_statement->delivery_time_frame = DeliveryTimeFrameType::ELECTRONIC_DELIVERY()->getValue();
            $request->merchant_risk_statement->email_delivery_address = $user->getEmail();
            $request->merchant_risk_statement->shipping_indicator = ShippingIndicatorType::DIGITAL_GOODS()->getValue();
        } elseif (\count(array_diff($shippingAddress->expose(), $billingAddress->expose())) === 0) {
            $request->merchant_risk_statement->shipping_indicator = ShippingIndicatorType::DELIVERY_TO_BILLING_ADDRESS()->getValue();
        } else {
            $request->merchant_risk_statement->shipping_indicator = ShippingIndicatorType::DELIVERY_TO_OTHER_ADDRESS()->getValue();
        }
        $request->merchant_risk_statement->reorder_indicator = $this->orderService->getReorderIndicator($legacyOrder);

        $request->merchant_risk_statement->purchase_indicator = PurchaseIndicatorStatus::AVAILABLE_GOODS()->getValue();

        return $request;
    }

    /** @return string The URL to redirect the customer */
    public function renewCardTransaction(User $user, string $redirectUrl, string $cssUrl = null): string
    {
        $callbackUrl = $this->router->generate(
            'payment_notification_hipay_card',
            [
                'payment_redirect_url' => \base64_encode($redirectUrl),
                'utm_nooverride' => '1',
                'renew_credit_card' => '1',
            ],
            RouterInterface::ABSOLUTE_URL
        );

        $request = new HostedPaymentPageRequest();
        $request->language = ((string) GlobalState::interfaceLocale()) === 'fr' ? 'fr_FR' : 'en_GB';
        $request->orderid = 'Card registration' . \uniqid(' ');
        $request->cid = $user->getUserId();
        $request->payment_product_list = HipayPaymentProductList::checkCardPaymentType($this->cardPaymentType);
        $request->operation = static::OPERATION_AUTHORIZATION;
        $request->description = __('credit_card_renew_user') . $user->getUserId();
        $request->amount = 1;
        $request->shipping = 0;
        $request->accept_url = $callbackUrl;
        $request->cancel_url = $callbackUrl;
        $request->decline_url = $callbackUrl;
        $request->exception_url = $callbackUrl;
        $request->pending_url = $callbackUrl;
        $request->notify_url = $this->router->generate(
            'payment_notification_hipay_card',
            [],
            RouterInterface::ABSOLUTE_URL
        );
        $request->multi_use = 1;
        $request->custom_data = \json_encode([
            'userId' => $user->getUserId(),
            'renewCreditCard' => true,
            'loggedUser' => $this->securityUserService->getCurrentUserId()
        ]);

        if (!empty($cssUrl)) {
            $request->template = Template::BASIC_JS;
            $request->css = $cssUrl;
        }

        return $this->api->requestHostedPaymentPage($request)->getForwardUrl();
    }

    public function getTransactionDetails(string $identifier): TransactionDetails
    {
        $transaction = $this->api->requestTransactionInformation($identifier);

        if (\is_null($transaction)) {
            throw new TransactionNotFound();
        }

        return new GenericTransactionDetails(
            $transaction->getTransactionReference(),
            $this->decodeOrderId($transaction->getOrder()->getId()),
            $transaction->getStatus(),
            $transaction
        );
    }

    public function isTransactionSuccessful(string $transactionMerchantToken): bool
    {
        $transaction = $this->getTransactionDetails($transactionMerchantToken);

        if (false === $transaction instanceof TransactionDetails) {
            return false;
        }

        return $this->isTransactionDetailsSuccessful($transaction);
    }

    public function isTransactionDetailsCancelled(TransactionDetails $transactionDetails): bool
    {
        return false;
    }

    public function isTransactionDetailsSuccessful(TransactionDetails $transaction): bool
    {
        $rawTransaction = $transaction->getRawData();

        if ($rawTransaction instanceof \HiPay\Fullservice\Gateway\Model\Transaction
            && \is_string($rawTransaction->getPaymentProduct()) === true
            && HipayPaymentProductList::isValid($rawTransaction->getPaymentProduct())
            && HipayPaymentProductList::isCardPaymentType(new HipayPaymentProductList($rawTransaction->getPaymentProduct()))
        ) {
            return $this->getHumanStatus($transaction->getStatus())->equals(TransactionStatus::SUCCESS());
        }

        return $this->getHumanStatus($transaction->getStatus())->equals(TransactionStatus::SUCCESS())
            || $this->getHumanStatus($transaction->getStatus())->equals(TransactionStatus::PENDING());
    }

    public function isTransactionNotRefunded(string $transactionMerchantToken): bool
    {
        $transaction = $this->getTransactionDetails($transactionMerchantToken);

        if (false === $transaction instanceof TransactionDetails) {
            return true;
        }

        return $this->isTransactionDetailsNotRefunded($transaction);
    }

    public function isTransactionDetailsNotRefunded(TransactionDetails $transaction): bool
    {
        return $transaction->getStatus() !== HiPayTransactionStatus::REFUNDED;
    }

    public function isTransactionDetailsFailed(TransactionDetails $transaction): bool
    {
        return $this->getHumanStatus($transaction->getStatus())->equals(TransactionStatus::FAILED());
    }

    public function getPayoutBalance(Company $company): ?int
    {
        $hipayId = (int) $company->getHipayId();
        if (false === $hipayId) {
            return null;
        }

        return $this->walletApi->getBalance($hipayId)->getAmount();
    }

    public function assertCanDoPayoutCompany(Company $company): void
    {
        parent::assertCanDoPayoutCompany($company);

        $hipayId = (int) $company->getHipayId();
        $iban = $company->getIban();

        if (\is_null($iban) === true || $hipayId === 0) {
            // Company does not have an account. We can leave now
            throw new \Exception('HiPay: Company does not have an account');
        }

        $balance = $this->walletApi->getBalance($hipayId);

        if ($balance->getAmount() < 50) {
            throw new \Exception('HiPay: Balance under 50€');
        }

        $reference = 'PAYOUT_' . $company->getId() . '_' . date('Ymd');
        $payoutTransfer = true;
        $messageException = '';
        $payoutTransferException = null;

        try {
            $this->walletApi->withdrawal($hipayId, $balance, $reference, $reference);
        } catch (\Exception $exception) {
            $payoutTransfer = false;
            $messageException = $exception->getMessage();
            $hipayStatus = HiPayResponseStatusCode::search($exception->getCode());
            $payoutTransferException = $exception;
            $this->logger->error('HiPay: Error during withdrawal', [
                'exception' => $exception,
                'hipay_id' => $hipayId,
                'error_code_detail' => ($hipayStatus === false )
                    ? 'The error code doesn\'t match HiPay\'s code'
                    : $hipayStatus,
            ]);
        }

        $this->eventDispatcher->dispatch(
            new TransactionTransferEvent(
                null,
                $company->getId(),
                (string) $hipayId,
                $company->getName() . " bank account",
                $messageException,
                'EUR',
                $balance->getAmount(),
                ($payoutTransfer === true) ? TransactionStatus::SUCCESS() : TransactionStatus::FAILED(),
                uniqid('wizaplace_'),
                $this->getName()
            ),
            TransactionsTransferEventType::VENDOR_WITHDRAWAL()->getValue()
        );

        if (\is_null($payoutTransferException) === false) {
            throw $payoutTransferException;
        }

        $this->logger->info('HiPay: Company' . $company->getId() . ' ,The withdrawal was successful on account ' . $hipayId);
    }

    public function canDoPayoutCompany(): bool
    {
        return true;
    }

    public function checkingOrderPayment(string $transactionMerchantToken): bool
    {
        return $this->isTransactionSuccessful($transactionMerchantToken)
            && $this->isTransactionNotRefunded($transactionMerchantToken);
    }

    public function getTransactionByOrderId(int $orderId): ?HiPayTransaction
    {
        $transactions = $this->api->requestOrderTransactionInformation($this->encodeOrderId($orderId));

        if (false === \is_array($transactions) || \count($transactions) === 0) {
            return null;
        }

        if (\is_array($transactions) === true && \count($transactions) > 0) {
            return reset($transactions);
        }

        return null;
    }

    public function getTransactionDetailsByOrderId(int $orderId): ?TransactionDetails
    {
        // If order is a child, we get parent order id
        $parentOrderId = $this->orderService->getParentOrderId($orderId);
        if (0 !== $parentOrderId) {
            $orderId = $parentOrderId;
        }

        $hiPayTransaction = $this->getTransactionByOrderId($orderId);
        if ($hiPayTransaction === null) {
            return null;
        }

        return new GenericTransactionDetails(
            $hiPayTransaction->getTransactionReference(),
            $this->decodeOrderId($hiPayTransaction->getOrder()->getId()),
            $hiPayTransaction->getStatus(),
            $hiPayTransaction
        );
    }

    public function captureTransaction(OrderPricesInterface $order): bool
    {
        /** @var Transaction $transaction */
        $transaction = \current($this->transactionService->findByOrderId($order->getId(), TransactionType::CREDITCARD()));

        try {
            $response = $this->api->requestMaintenanceOperation(
                static::OPERATION_CAPTURE,
                $this->getTransactionByOrderId($order->getId())->getTransactionReference(),
                $order->getCustomerTotal()->getConvertedAmount()
            );

            $transaction->setAmount(Money::fromVariable($response->getCapturedAmount()));

            $this->transactionService->updateTransactionStatus($transaction, TransactionStatus::SUCCESS());

            if (\floatval($response->getCapturedAmount()) === $order->getCustomerTotal()->getConvertedAmount()) {
                return true;
            }
        } catch (\Throwable $exception) {
            $this->logger->error('Unable to capture the HiPay transaction for the order ' . $order->getId());

            $this->transactionService->updateTransactionStatus($transaction, TransactionStatus::FAILED());
        }

        return false;
    }

    public function isWaitingCaptured(int $orderId): bool
    {
        $transaction = $this->getTransactionByOrderId($orderId);

        return $transaction instanceof HiPayTransaction
            && $transaction->getState() === TransactionState::COMPLETED
            && \is_string($transaction->getTransactionReference())
            && \floatval($transaction->getCapturedAmount()) === 0.00;
    }

    public function createAccount(int $companyId): string
    {
        return (string) $this->walletApi->createAccount($companyId)['account_id'];
    }

    public function transferSpecificOrder(Order $order): bool
    {
        $legacyOrder = $order->getLegacyOrder();
        $paymentInfo = $legacyOrder->getPaymentInformation();

        if (false === empty($paymentInfo['hipay_transfer_done'])) {
            return true;
        }

        $commission = $this->commissionService->getTotalCommission($order);
        $amountWithoutCom = $order->getBalanceTotal()->subtract($commission);
        $hiPayTransferVendorDone = true;
        $messageException = '';
        $company = $order->getCompany();
        $responseVendor = null;

        // Transfer funds - com => vendor
        if (empty($paymentInfo['hipay_transfer_vendor_done'])) {
            $recipientId = $company->getHipayId();

            if (\is_null($recipientId) === true) {
                // Account doesn't exists, try to create it
                try {
                    // Account has been created, reload ID
                    $recipientId = $this->createAccount($company->getId());
                } catch (HiPayWalletRequestFailed $e) {
                    $hiPayTransferVendorDone = false;
                    $messageException = 'HiPay: company account does not exists and cannot create it';
                    $this->logger->error($messageException, [
                        'exception' => $e,
                        'companyId' => $company->getId(),
                        'order_id' => $order->getId(),
                    ]);
                }
            }

            // handle the transfer funds to vendor when there was a complete refund.
            if ($amountWithoutCom->isZero() === true || $amountWithoutCom->isNegative() === true) {
                // Make a placeholder transaction for the vendor transfer.
                $this->transactionService
                    ->createDispatchFundsTransaction(
                        $order->getId(),
                        TransactionType::DISPATCH_FUNDS_TRANSFER_VENDOR(),
                        TransactionStatus::SUCCESS(),
                        $amountWithoutCom,
                        static::getName(),
                        uniqid('wizaplace_')
                    );

                // Make a placeholder transaction for the commission ( to keep track )
                if ($commission->isZero() === true || $commission->isNegative() === true) {
                    $this->transactionService
                        ->createDispatchFundsTransaction(
                            $order->getId(),
                            TransactionType::DISPATCH_FUNDS_TRANSFER_COMMISSION(),
                            TransactionStatus::SUCCESS(),
                            $commission,
                            static::getName(),
                            uniqid('wizaplace_')
                        );
                }

                // Set order status to done even tho we did not make the PSP call.
                $legacyOrder->setPaymentInformation('hipay_transfer_vendor_done', true);
                $legacyOrder->setPaymentInformation('hipay_transfer_com_done', true);

                return true;
            }

            if (\is_null($recipientId) === false) {
                try {
                    $responseVendor = $this->walletApi->transferFundsFromTechnicalWallet(
                        $amountWithoutCom,
                        $recipientId,
                        __('order') . ' ' . $order->getId(),
                        'ORDER_' . $order->getId()
                    );
                } catch (HiPayWalletRequestFailed $e) {
                    $messageException = $e->getMessage();
                    $hiPayTransferVendorDone = false;
                    $this->logger->error('HiPay: transfer to vendor failed', [
                        'exception' => $e,
                        'order_id' => $order->getId(),
                    ]);
                }
            }

            $legacyOrder->setPaymentInformation('hipay_transfer_vendor_done', $hiPayTransferVendorDone);

            $this->eventDispatcher->dispatch(
                new TransactionTransferEvent(
                    $order->getId(),
                    $company->getId(),
                    static::TECHNICAL_WALLET,
                    $recipientId ?? '',
                    $messageException,
                    'EUR',
                    $amountWithoutCom->getAmount(),
                    ($hiPayTransferVendorDone === true) ? TransactionStatus::SUCCESS() : TransactionStatus::FAILED(),
                    $responseVendor['transaction_id'] ?? uniqid('wizaplace_'),
                    $this->getName()
                ),
                TransactionsTransferEventType::DISPATCH_FUNDS_TRANSFER_VENDOR()->getValue()
            );
        }

        if ($hiPayTransferVendorDone === false) {
            if ($commission->isPositive() === true) {
                $this->eventDispatcher->dispatch(
                    new TransactionTransferEvent(
                        $order->getId(),
                        $company->getId(),
                        static::TECHNICAL_WALLET,
                        $recipientId ?? '',
                        $messageException,
                        'EUR',
                        $commission->getAmount(),
                        TransactionStatus::FAILED(),
                        $responseVendor['transaction_id'] ?? uniqid('wizaplace_'),
                        $this->getName()
                    ),
                    TransactionsTransferEventType::DISPATCH_FUNDS_TRANSFER_COMMISSION()->getValue()
                );

                $legacyOrder->setPaymentInformation('hipay_transfer_com_done', false);
                $legacyOrder->setPaymentInformation('hipay_transfer_done', false);
            }

            return false;
        }

        // Transfer com => admin
        $hiPayTransferComDone = true;
        $responseCommission = null;
        if ($commission->isPositive() && empty($paymentInfo['hipay_transfer_com_done'])) {
            if (\is_null($this->commissionRecipientId) === false) {
                try {
                    $responseCommission = $this->walletApi->transferFundsFromTechnicalWallet(
                        $commission,
                        $this->commissionRecipientId,
                        __('commission') . ' ' . __('order') . ' ' . $order->getId(),
                        'COMMISSION_ORDER_' . $order->getId()
                    );
                } catch (HiPayWalletRequestFailed $e) {
                    $this->logger->error('HiPay: transfer commission failed', [
                        'exception' => $e,
                        'order_id' => $order->getId(),
                    ]);
                    $messageException = $e->getMessage();
                    $hiPayTransferComDone = false;
                }
            } else {
                $messageException = 'HiPay: a commission is set but the recipient is not configured. The amount remains on the technical account.';
                $hiPayTransferComDone = false;
                $this->logger->error($messageException, [
                    'orderId' => $order->getId(),
                    'commission' => $commission->getConvertedAmount(),
                ]);
            }

            $this->eventDispatcher->dispatch(
                new TransactionTransferEvent(
                    $order->getId(),
                    $company->getId(),
                    static::TECHNICAL_WALLET,
                    $this->commissionRecipientId ?? '',
                    $messageException,
                    'EUR',
                    $commission->getAmount(),
                    ($hiPayTransferComDone === true) ? TransactionStatus::SUCCESS() : TransactionStatus::FAILED(),
                    $responseCommission['transaction_id'] ?? uniqid('wizaplace_'),
                    $this->getName()
                ),
                TransactionsTransferEventType::DISPATCH_FUNDS_TRANSFER_COMMISSION()->getValue(),
            );

            $legacyOrder->setPaymentInformation('hipay_transfer_done', $hiPayTransferComDone);
            $legacyOrder->setPaymentInformation('hipay_transfer_com_done', $hiPayTransferComDone);
        }

        return $hiPayTransferComDone;
    }

    public function getHumanStatus($status): TransactionStatus
    {
        switch ($status) {
            case HiPayTransactionStatus::CREATED:
            case HiPayTransactionStatus::CARD_HOLDER_ENROLLED:
            case HiPayTransactionStatus::AUTHENTICATION_REQUESTED:
                return TransactionStatus::READY();
            case HiPayTransactionStatus::CARD_HOLDER_NOT_ENROLLED:
            case HiPayTransactionStatus::AUTHORIZED:
            case HiPayTransactionStatus::PENDING_PAYMENT:
            case HiPayTransactionStatus::AUTHORIZED_AND_PENDING:
            case HiPayTransactionStatus::CARD_HOLDER_AUTHENTICATED:
            case HiPayTransactionStatus::AUTHENTICATION_ATTEMPTED:
            case HiPayTransactionStatus::CAPTURE_REQUESTED:
            case HiPayTransactionStatus::AUTHENTICATED:
            case HiPayTransactionStatus::AUTHORIZATION_REQUESTED:
            case HiPayTransactionStatus::ACQUIRER_FOUND:
            case HiPayTransactionStatus::RISK_ACCEPTED:
            case HiPayTransactionStatus::REFUND_REQUESTED:
                return TransactionStatus::PENDING();
            case HiPayTransactionStatus::UNABLE_TO_AUTHENTICATE:
            case HiPayTransactionStatus::CAPTURE_REFUSED:
            case HiPayTransactionStatus::REFUND_REFUSED:
            case HiPayTransactionStatus::AUTHORIZATION_REFUSED:
            case HiPayTransactionStatus::CANCELLED:
            case HiPayTransactionStatus::UNKNOWN:
            case HiPayTransactionStatus::BLOCKED:
            case HiPayTransactionStatus::DENIED:
            case HiPayTransactionStatus::REFUSED:
            case HiPayTransactionStatus::EXPIRED:
            case HiPayTransactionStatus::COULD_NOT_AUTHENTICATE:
            case HiPayTransactionStatus::AUTHENTICATION_FAILED:
            case HiPayTransactionStatus::CHARGED_BACK:
            case HiPayTransactionStatus::ACQUIRER_NOT_FOUND:
            case HiPayTransactionStatus::CARD_HOLDER_ENROLLMENT_UNKNOWN:
            case HiPayTransactionStatus::AUTHORIZATION_CANCELLATION_REQUESTED:
                return TransactionStatus::FAILED();
            case HiPayTransactionStatus::PARTIALLY_CAPTURED:
            case HiPayTransactionStatus::CAPTURED:
            case HiPayTransactionStatus::REFUNDED:
            case HiPayTransactionStatus::COLLECTED:
            case HiPayTransactionStatus::PARTIALLY_COLLECTED:
            case HiPayTransactionStatus::SETTLED:
            case HiPayTransactionStatus::PARTIALLY_SETTLED:
            case HiPayTransactionStatus::PARTIALLY_REFUNDED:
            case HiPayTransactionStatus::DEBITED:
            case HiPayTransactionStatus::PARTIALLY_DEBITED:
                return TransactionStatus::SUCCESS();
        }

        throw new \Exception('Unable to map HiPay status: ' . $status);
    }

    public function encodeOrderId(int $orderId): string
    {
        return $this->projectName . '_' . $orderId;
    }

    public function decodeOrderId(string $orderIdString): int
    {
        return static::processDecodeOrderId($orderIdString);
    }

    public static function processDecodeOrderId(string $orderIdString): int
    {
        \preg_match('/\D*(?<order_id>\d+)$/', \trim($orderIdString), $matches);
        $orderId = \intval($matches['order_id']);

        if ($orderId === 0) {
            throw new \InvalidArgumentException("Invalid orderId string $orderIdString");
        };

        return $orderId;
    }

    public function refund(Transaction $originTransaction, Refund $refund): Transaction
    {
        return $this->refundTransaction(
            $originTransaction,
            $refund,
            function (Transaction $transaction) use ($originTransaction, $refund): Transaction {
                try {
                    /** @var Operation $response */
                    $amount = $refund->getAmount()->reducePrecisionToCents();
                    $response = $this->api->requestMaintenanceOperation(
                        static::OPERATION_REFUND,
                        $originTransaction->getTransactionReference(),
                        $amount->getConvertedAmount(),
                        $this->encodeOrderId($originTransaction->getOrderId())
                    );
                    $transaction->setTransactionReference($response->getTransactionReference());
                    $transaction->addInformation('response', \json_encode($response));
                } catch (\Exception $exception) {
                    $this->transactionService->updateTransactionStatus($transaction, TransactionStatus::FAILED());

                    throw new RefundErrorFromPspException(
                        $this->getName()->getValue(),
                        $exception->getMessage(),
                        $exception->getCode(),
                        $exception
                    );
                }

                return $transaction;
            }
        );
    }

    public function getName(): PaymentProcessorName
    {
        return PaymentProcessorName::HIPAY();
    }

    public function customerBillingInfoRequest(Order $order): CustomerBillingInfoRequest
    {
        $billingAddress = $order->getBillingAddress();
        $customerBillingInfo = new CustomerBillingInfoRequest();
        $customerBillingInfo->email = $order->getEmail();
        $customerBillingInfo->firstname = $billingAddress->getFirstname();
        $customerBillingInfo->lastname = $billingAddress->getLastname();
        $customerBillingInfo->recipientinfo = $billingAddress->getCompany();
        $customerBillingInfo->streetaddress = $billingAddress->getAddress();
        $customerBillingInfo->streetaddress2 = $billingAddress->getAddress2();
        $customerBillingInfo->city = $billingAddress->getCity();
        $customerBillingInfo->zipcode = $billingAddress->getZipcode();
        $customerBillingInfo->country = $billingAddress->getCountry();

        return $customerBillingInfo;
    }

    public function customerShippingInfoRequest(Order $order): CustomerShippingInfoRequest
    {
        $shippingAddress = $order->getShippingAddress();
        $customerShippingInfo = new CustomerShippingInfoRequest();
        $customerShippingInfo->shipto_firstname = $shippingAddress->getFirstname();
        $customerShippingInfo->shipto_lastname = $shippingAddress->getLastname();
        $customerShippingInfo->shipto_recipientinfo = $shippingAddress->getCompany();
        $customerShippingInfo->shipto_streetaddress = $shippingAddress->getAddress();
        $customerShippingInfo->shipto_streetaddress2 = $shippingAddress->getAddress2();
        $customerShippingInfo->shipto_city = $shippingAddress->getCity();
        $customerShippingInfo->shipto_zipcode = $shippingAddress->getZipcode();
        $customerShippingInfo->shipto_country = $shippingAddress->getCountry();

        return $customerShippingInfo;
    }

    protected function saveDirectDebitTransaction(
        OrderPricesInterface $order,
        string $transactionId,
        array $informations
    ): Transaction {
        // Keep legacy transactions persistence to prevent unwanted BC break
        fn_update_order_payment_info(
            $order->getId(),
            [
                'hipay_transaction_id' => $transactionId,
                'hipay_sepa' => true,
            ]
        );

        $transaction = $this->transactionService->createDirectDebitTransaction(
            $order->getId(),
            $order->getCustomerTotal(),
            $this->getName(),
            $informations
        );

        $transaction->setTransactionReference($transactionId);
        $this->transactionService->updateTransactionStatus($transaction, TransactionStatus::PENDING());

        return $transaction;
    }

    private function saveCreditCardTransaction(OrderPricesInterface $order, string $transactionId): Transaction
    {
        $transaction = $this
            ->transactionService->createCreditCardTransaction(
                $order->getId(),
                $order->getCustomerTotal(),
                $this->getName()
            )
            ->setTransactionReference($transactionId)
        ;

        //Set Origin and Destination
        $user = $this->userService->get($order->getUserId());
        $transaction
            ->setOrigin($user->getUserId() . ' ' . $user->getFirstname() . ' ' . $user->getLastname())
            ->setDestination(static::TECHNICAL_WALLET)
            ->setCurrency('EUR')
        ;
        $this->transactionService->save($transaction);

        return $transaction;
    }

    public function getMandate($userId): array
    {
        $agreementHipayId = $this->userPaymentInfoService->getHipaySepaAgreement($userId);

        if ($agreementHipayId === null) {
            return [];
        }

        $agreementInfo = $this->getSepaMandateInfo($agreementHipayId);

        return [
            'createdAt' => $agreementInfo['debit_agreement']['creation_date'],
            'iban' => $agreementInfo['debit_agreement']['properties']['iban'],
            'issuerBankId' => $agreementInfo['debit_agreement']['properties']['issuer_bank_id'],
            'bankName' => $agreementInfo['debit_agreement']['properties']['bank_name'],
            'gender' => $agreementInfo['debit_agreement']['properties']['gender'],
            'firstName' => $agreementInfo['debit_agreement']['properties']['firstname'],
            'lastName' => $agreementInfo['debit_agreement']['properties']['lastname'],
        ];
    }
}
