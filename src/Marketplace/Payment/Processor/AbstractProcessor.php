<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Processor;

use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Payment\Exception\CannotDoPayoutProcessorException;

abstract class AbstractProcessor implements
    CreditCardProcessorInterface,
    TransactionProcessorInterface,
    ProcessorInterface
{
    abstract public function createSepaTransaction(int $orderId): AbstractProcessor;
    abstract public function getPayoutBalance(Company $company): ?int;
    abstract public function canDoPayoutCompany(): bool;

    public function assertCanDoPayoutCompany(Company $company): void
    {
        if ($this->canDoPayoutCompany() === false) {
            throw new CannotDoPayoutProcessorException($this->getName());
        }
    }
}
