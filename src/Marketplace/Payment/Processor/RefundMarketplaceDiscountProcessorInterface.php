<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Processor;

use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Refund\Entity\Refund;
use Wizacha\Marketplace\Transaction\Transaction;
use Wizacha\Money\Money;

interface RefundMarketplaceDiscountProcessorInterface
{
    public function refundMarketplaceDiscount(Order $order, Refund $refund, Money $amount): Transaction;
}
