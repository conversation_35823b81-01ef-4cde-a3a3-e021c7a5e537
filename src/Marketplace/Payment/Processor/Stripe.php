<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Processor;

use Broadway\UuidGenerator\UuidGeneratorInterface;
use Psr\Log\LoggerInterface;
use Stripe\Account;
use Stripe\Charge;
use Stripe\Collection;
use Stripe\Customer;
use Stripe\Error\Base;
use Stripe\PaymentIntent;
use Stripe\Person;
use Stripe\Stripe as StripeSdk;
use Stripe\StripeObject;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Wizacha\Marketplace\Commission\CommissionService;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Order\Action\Confirm;
use Wizacha\Marketplace\Order\Action\DispatchFunds;
use Wizacha\Marketplace\Order\Action\DispatchFundsFailed;
use Wizacha\Marketplace\Order\Action\DispatchFundsSucceeded;
use Wizacha\Marketplace\Order\Action\MarkAsPaid;
use Wizacha\Marketplace\Order\Action\MarkPaymentAsRefused;
use Wizacha\Marketplace\Order\Action\MarkPaymentAuthorizationAsCaptured;
use Wizacha\Marketplace\Order\Action\MarkPaymentAuthorizationAsRefused;
use Wizacha\Marketplace\Order\Action\RedirectToPaymentProcessor;
use Wizacha\Marketplace\Payment\Event\DispatchFundsFailedEvent;
use Wizacha\Marketplace\Order\Event\OrderStatusUpdated;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderEvents;
use Wizacha\Marketplace\Order\OrderPricesInterface;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\Order\Refund\Entity\Refund;
use Wizacha\Marketplace\Payment\ApiAwareTrait;
use Wizacha\Marketplace\Payment\Exception\PayoutException;
use Wizacha\Marketplace\Payment\Exception\RefundErrorFromPspException;
use Wizacha\Marketplace\Payment\GenericTransactionDetails;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\PaymentType;
use Wizacha\Marketplace\Payment\Stripe\BankAccountIsMissing;
use Wizacha\Marketplace\Payment\Stripe\CompanyAlreadyHaveAccount;
use Wizacha\Marketplace\Payment\Stripe\StripeApi;
use Wizacha\Marketplace\Payment\Stripe\StripeCardException;
use Wizacha\Marketplace\Payment\Stripe\StripeConfig;
use Wizacha\Marketplace\Payment\TransactionDetails;
use Wizacha\Marketplace\Payment\UserPaymentInfoService;
use Wizacha\Marketplace\Subscription\Exception\SubscriptionException;
use Wizacha\Marketplace\Subscription\SubscriptionService;
use Wizacha\Marketplace\Subscription\SubscriptionStatus;
use Wizacha\Marketplace\Transaction\Exception\TransactionNotFound;
use Wizacha\Marketplace\Transaction\Exception\UnsupportedTransactionStatusException;
use Wizacha\Marketplace\Transaction\Exception\UnsupportedTransactionTypeException;
use Wizacha\Marketplace\Transaction\Transaction;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionStatus;
use Wizacha\Marketplace\Transaction\TransactionsTransferEventType;
use Wizacha\Marketplace\Transaction\TransactionTransferEvent;
use Wizacha\Marketplace\Transaction\TransactionType;
use Wizacha\Marketplace\User\User;
use Wizacha\Marketplace\User\UserService;
use Wizacha\Money\Money;

/**
 * Class Stripe
 * @package Wizacha\Marketplace\Payment\Processor
 * @property StripeApi $api
 */
class Stripe extends AbstractProcessor implements EventSubscriberInterface, RefundProcessorInterface
{
    use RefundableProcessorTrait;
    use ApiAwareTrait;

    public const PROCESSOR_NAME = 'processorName';
    public const OLD_STATUS_REQUIRES_PAYMENT_METHOD = 'requires_source';
    public const OLD_STATUS_REQUIRES_ACTION = 'requires_source_action';
    public const ORIGIN_TRANSACTION_PAYMENT = 'Payment';

    /** @var UuidGeneratorInterface */
    protected $uuidGenerator;

    /** @var MarkAsPaid */
    protected $markAsPaid;

    /** @var OrderService */
    protected $orderService;

    /** @var MarkPaymentAsRefused */
    protected $markAsRefused;

    /** @var Confirm */
    protected $confirm;

    /** @var CommissionService */
    protected $commissionService;

    /** @var DispatchFunds */
    protected $dispatchFundsAction;

    /** @var DispatchFundsFailed */
    protected $dispatchFundsFailedAction;

    /**
     * @var MarkPaymentAuthorizationAsCaptured
     */
    protected $markAuthorizationAsCaptured;

    /** @var MarkPaymentAuthorizationAsRefused */
    protected $markAuthorizationAsRefused;

    /** @var bool */
    protected $configured;

    /** @var UserPaymentInfoService */
    protected $userPaymentInfoService;

    /** @var RedirectToPaymentProcessor */
    protected $redirectToPaymentProcessor;

    /** @var LoggerInterface */
    protected $logger;

    /** @var boolean */
    protected $testMode;

    /** @var TransactionService */
    protected $transactionService;

    /** @var StripeConfig */
    protected $config;

    private EventDispatcherInterface $eventDispatcher;
    private SubscriptionService $subscriptionService;

    /** @var DispatchFundsSucceeded */
    private $dispatchFundsSucceededAction;

    private UserService $userService;

    public function __construct(
        OrderService $orderService,
        CommissionService $commissionService,
        UserPaymentInfoService $userPaymentInfoService,
        MarkAsPaid $markAsPaid,
        MarkPaymentAsRefused $markAsRefused,
        RedirectToPaymentProcessor $redirectToPaymentProcessor,
        Confirm $confirm,
        DispatchFunds $dispatchFundsAction,
        DispatchFundsFailed $dispatchFundsFailedAction,
        MarkPaymentAuthorizationAsCaptured $markAuthorizationAsCaptured,
        MarkPaymentAuthorizationAsRefused $markAuthorizationAsRefused,
        LoggerInterface $logger,
        UuidGeneratorInterface $uuidGenerator,
        StripeConfig $config,
        EventDispatcherInterface $eventDispatcher,
        DispatchFundsSucceeded $dispatchFundsSucceededAction,
        SubscriptionService $subscriptionService,
        UserService $userService
    ) {
        $this->config = $config;
        $this->configured = !empty($this->getConfig()->getSecretKey());

        if ($this->configured) {
            $this->orderService = $orderService;
            $this->markAsPaid = $markAsPaid;
            $this->markAsRefused = $markAsRefused;
            $this->confirm = $confirm;
            $this->dispatchFundsAction = $dispatchFundsAction;
            $this->dispatchFundsFailedAction = $dispatchFundsFailedAction;
            $this->markAuthorizationAsCaptured = $markAuthorizationAsCaptured;
            $this->markAuthorizationAsRefused = $markAuthorizationAsRefused;

            StripeSdk::setApiKey($this->getConfig()->getSecretKey());
            StripeSdk::setApiVersion($this->getConfig()->getStripeApiVersion());

            $this->commissionService = $commissionService;
            $this->userPaymentInfoService = $userPaymentInfoService;
            $this->redirectToPaymentProcessor = $redirectToPaymentProcessor;
            $this->logger = $logger;
            $this->uuidGenerator = $uuidGenerator;
            $this->eventDispatcher =  $eventDispatcher;

            $this->testMode = substr($this->getConfig()->getSecretKey(), 0, 7) === 'sk_test';
            $this->dispatchFundsSucceededAction = $dispatchFundsSucceededAction;
            $this->userService = $userService;
        }
        $this->subscriptionService = $subscriptionService;
    }

    /** @throws StripeCardException */
    public function createCardTransaction(int $orderId, string $redirectUrl = null, string $cssUrl = null, bool $isCaptured = true): string
    {
        $order = $this->orderService->getAnyOrder($orderId);
        $orders = $this->orderService->getChildOrders($orderId);
        $description = 'Order ' . $orderId;
        // Works with Stripe card
        $statementDescriptionSuffix = 'Order ' . $orderId;
        if ($order->hasParentOrder() === true) {
            $statementDescriptionSuffix = 'Order ' . $order->getParentOrderId();
        }

        if ($order->isParentOrder()) {
            $orderIds = array_map(function (Order $order) {
                return $order->getId();
            }, $orders);

            $description .= ' (' . implode(', ', $orderIds) . ')';
        }

        $paymentIntent = null;
        if (\is_string($orders[0]->getSubscriptionId())) {
            $subscriptionId = $orders[0]->getSubscriptionId();

            if (\count($orders) > 1) {
                throw new SubscriptionException("Unable to renew the order '" . $orderId . "' because we've got several child orders.");
            }

            $subscription = $this->subscriptionService->get($subscriptionId);
            if (null === $subscription) {
                throw new SubscriptionException("Unable to renew the order '" . $orderId . "' because subscription is not retrievable from database.");
            }

            $creditCard = $subscription->getCreditCard();
            if (null === $creditCard) {
                throw new SubscriptionException("Unable to renew the order '" . $orderId . "' because subscription doesn't have credit card");
            }

            try {
                $paymentIntent = $this->api->createPaymentIntent(
                    [
                        'amount' => $order->getCustomerTotal()->getAmount(),
                        'currency' => $this->getConfig()->getCurrencyCode(),
                        'customer' => $creditCard->getPspUserId(),
                        'payment_method' => $creditCard->getToken(),
                        'off_session' => true,
                        'confirm' => true,
                        'metadata' => [
                            // On n'a pas réellement besoin de ça techniquement, mais si un jour on a besoin de rapprocher
                            // une commande à une transaction, on a déjà l'info
                            'orderId' => $orderId,
                            'origin' => $this->getConfig()->getOrigin(),
                        ],
                    ]
                );
            } catch (\Exception $exception) {
                $this->subscriptionService->updateSubscriptionStatus($subscription, SubscriptionStatus::DEFAULTED());
                throw new StripeCardException('Unable to finalize the stripe transaction for the order ' . $order->getId(), 0, $exception);
            }
        } else {
            $customer = $this->createCustomer($orders[0]->getUser());
            try {
                $paymentIntent = $this->api->createPaymentIntent(
                    [
                        'amount' => $order->getCustomerTotal()->getAmount(),
                        'currency' => $this->getConfig()->getCurrencyCode(),
                        'capture_method' => $isCaptured ? 'automatic' : 'manual',
                        'customer' => $customer->id,
                        'payment_method_types' => [
                            'card',
                        ],
                        'description' => $description,
                        'metadata' => [
                            // On n'a pas réellement besoin de ça techniquement, mais si un jour on a besoin de rapprocher
                            // une commande à une transaction, on a déjà l'info
                            'orderId' => $orderId,
                            'origin' => $this->getConfig()->getOrigin(),
                        ],
                        'setup_future_usage' => 'off_session',
                    ],
                    [
                        'idempotency_key' => $this->uuidGenerator->generate(),
                    ]
                );
            } catch (Base $exception) {
                if ($exception->getStripeCode() === 'amount_too_large') {
                    throw new StripeCardException('Transaction amount is higher than maximum permitted amount', 0, $exception);
                }
                throw new StripeCardException('Unable to finalize the stripe transaction for the order ' . $order->getId(), 0, $exception);
            } catch (\Exception $exception) {
                throw new StripeCardException('Unable to finalize the stripe transaction for the order ' . $order->getId(), 0, $exception);
            }
        }
        foreach ($orders as $order) {
            $this->saveCreditCardTransaction($order, $paymentIntent->id, [
                // phpcs:ignore
                'capture_method' => $paymentIntent->capture_method,
            ]);
        }

        // Following field of PaymentIntent Stripe API is camelcase which fails phpcs. So we have to ignore it
        // phpcs:ignore
        return $paymentIntent->client_secret;
    }

    /** @throws \Exception */
    public function finalizeCardTransaction(int $orderId, string $token)
    {
        $orders = $this->orderService->getChildOrders($orderId);
        $paymentIntent = null;
        $charge = null;

        try {
            $paymentIntent = $this->api->getPaymentIntent($token);
            $charge = $this->api->getAllCharges([
                'payment_intent' => $paymentIntent->id,
                'limit' => 2,
            ]);

            $paymentIsOk = $charge->data[0]['captured'] === true ? $paymentIntent->status === 'succeeded' : $charge->data[0]['paid'] === true;
        } catch (\Exception $exception) { // Catch "soft" exception and let parent handle critical problems
            $paymentIsOk = false;
            $this->logger->error('Unable to finalize the stripe transaction for the order ' . $orderId);
        }

        foreach ($orders as $order) {
            $this->saveCreditCardTransactionDetails($order->getId(), $token, $paymentIntent, $charge, $paymentIsOk);
        }

        if ($paymentIsOk === true) {
            foreach ($orders as $order) {
                if ($this->markAsPaid->isAllowed($order)) {
                    $this->markAsPaid->execute($order);
                }

                if ($this->confirm->isAllowed($order)) {
                    $this->confirm->execute($order);
                }
            }

            fn_change_order_status($orderId, OrderStatus::STANDBY_VENDOR);
        } else {
            foreach ($orders as $order) {
                if ($this->markAsRefused->isAllowed($order)) {
                    $this->markAsRefused->execute($order);
                }
            }
        }
    }

    /**
     * @throws BankAccountIsMissing
     */
    public function createSepaTransaction(int $orderId, ?string $idempotencyKey = null): AbstractProcessor
    {
        $order = $this->orderService->getAnyOrder($orderId);

        $token = $this->userPaymentInfoService->getStripeBankAccountToken($order->getUserId());
        $customerId = $this->userPaymentInfoService->getStripeCustomerId($order->getUserId());

        if (!$token) {
            throw new BankAccountIsMissing("We tried to create a SEPA transaction for the order $orderId but the user doesn't have a Stripe bank account");
        }

        $childOrders = $this->orderService->getChildOrders($orderId);
        $description = 'Order ' . $orderId;
        // Works with Stripe SEPA
        $statementDescription = 'Order ' . $orderId;
        if ($order->hasParentOrder() === true) {
            $statementDescription = 'Order ' . $order->getParentOrderId();
        }


        if ($order->isParentOrder()) {
            $orderIds = array_map(function (Order $order) {
                return $order->getId();
            }, $childOrders);

            $description .= ' (' . implode(', ', $orderIds) . ')';
        }

        /** @var Charge $charge */
        $charge = $this->api->createCharge(
            [
                'amount' => $order->getCustomerTotal()->getAmount(),
                'currency' => $this->getConfig()->getCurrencyCode(),
                'source' => $token,
                'statement_descriptor' => $statementDescription,
                'customer' => $customerId,
                'transfer_group' => 'ORDER_' . $orderId,
                'description' => $description,
                'metadata' => [
                    'orderId' => $orderId,
                    'origin' => $this->getConfig()->getOrigin(),
                ],
            ],
            [
                'idempotency_key' => $idempotencyKey ?? $this->uuidGenerator->generate(),
            ]
        );

        foreach ($childOrders as $childOrder) {
            $this->saveDirectDebitTransaction($childOrder, $charge->id, ['stripe_sepa' => true]);
        }

        return $this;
    }

    public static function getSubscribedEvents()
    {
        return [
            OrderEvents::UPDATED  => ['onOrderUpdate', 0],
        ];
    }

    public function onOrderUpdate(OrderStatusUpdated $event)
    {
        if ($this->configured) {
            $order = $this->orderService->getAnyOrder($event->getId());

            if ($order->getLegacyOrder()->hasProcessorName($this->getName()) === false) {
                return;
            }

            if ($event->getStatusTo() === OrderStatus::PROCESSING_SHIPPING && $this->isWaitingCaptured($order)) {
                foreach ($this->orderService->getChildOrders($event->getId()) as $order) {
                    $action = $this->captureTransaction($order) ? $this->markAuthorizationAsCaptured : $this->markAuthorizationAsRefused;

                    if ($action->isAllowed($order)) {
                        $action->execute($order);
                    }
                }
            }

            if ($event->getStatusTo() === OrderStatus::COMPLETED) {
                $this->transferFundsToVendor($event->getId());
            }
        }
    }

    /** @param array|string|null $chargeId */
    public function getCharge($chargeId): Charge
    {
        return $this->api->getCharge($chargeId);
    }

    public function isConfigured(): bool
    {
        return $this->configured;
    }

    public function createCustomer(User $user, string $sourceToken = null): Customer
    {
        $customerId = $this->userPaymentInfoService->getStripeCustomerId($user->getUserId());

        // if we already have a stripe customer id in database
        if (\is_string($customerId) === true) {
            $customer = $this->api->retrieveCustomer($customerId);
            if (null !== $sourceToken) {
                $this->userPaymentInfoService->setStripeBankAccountToken($user->getUserId(), $sourceToken);
                // link it to already existing customer
                $this->api->attachSourceToCustomer($customerId, $sourceToken);
            }
        } else {
            // else we should create a new one
            $params = [
                'description' => 'User #' . $user->getUserId() . ' ' . $user->getEmail(),
                'email' => $user->getEmail(),
            ];

            if (null !== $sourceToken) {
                $params['source'] = $sourceToken;
                $this->userPaymentInfoService->setStripeBankAccountToken($user->getUserId(), $sourceToken);
            }

            $customer = $this->api->createCustomer($params);
            $this->userPaymentInfoService->setStripeCustomerId($user->getUserId(), $customer->id);
        }

        return $customer;
    }

    public function getCustomAccount(string $id): Account
    {
        return $this->api->getAccount($id);
    }

    public function getCustomPerson(Account $account): ?Person
    {
        foreach ($account->persons() as $person) {
            return $this->api->getPerson($account->id, $person->id);
        }

        return null;
    }

    public function createCustomAccount(Company $company, string $accountToken, string $personToken): Account
    {
        if ($company->getStripeId()) {
            throw new CompanyAlreadyHaveAccount();
        }

        $data = [
            'type' => 'custom',
            'account_token' => $accountToken,
            'email' => $company->getEmail(),
            'country' => $company->getCountry(),
            'external_account' => [
                'object' => 'bank_account',
                'country' => $company->getCountry(),
                'currency' => $this->getConfig()->getCurrencyCode(),
                'account_number' => $company->getIban(),
            ],
            'metadata' => [
                'companyId' => $company->getId(),
            ],
            'requested_capabilities' => [
                'card_payments',
                'transfers',
            ],
            'business_profile' => [
                'url' => $company->getUrl(),
                'mcc' => 1520, // General Services
            ],
        ];

        if ($company->getCountry() == "CA") {
            $data['external_account']['routing_number'] = $company->getBic();
        }

        $account = $this->api->createAccount($data);

        $company->setStripeId($account->id);

        $this->api->createPerson($account->id, $personToken);

        return $account;
    }

    public function updateCustomAccount(Company $company, string $accountToken, string $personToken): Account
    {
        if (false === $company->getStripeId() || null === $company->getStripeId()) {
            throw new BankAccountIsMissing();
        }

        $data = [
            'account_token' => $accountToken,
            'metadata' => [
                'companyId' => $company->getId(),
            ],
            'external_account' => [
                'object' => 'bank_account',
                'country' => $company->getCountry(),
                'currency' => $this->getConfig()->getCurrencyCode(),
                'account_number' => $company->getIban(),
            ],
            'requested_capabilities' => [
                'card_payments',
                'transfers',
            ],
            'business_profile' => [
                'url' => $company->getUrl(),
                'mcc' => 1520, // General Services
            ],
        ];

        $account =  $this->api->updateAccount($company->getStripeId(), $data);

        if ($account->persons()->count() > 0) {
            foreach ($account->persons() as $person) {
                $this->api->updatePerson($account->id, $person->id, $personToken);
                break;
            }
        } else {
            $this->api->createPerson($account->id, $personToken);
        }

        return $account;
    }

    /**
     * @param resource $file
     */
    public function uploadIdFile($file): StripeObject
    {
        return $this->api->createFile([
            'file' => $file,
            'purpose' => 'identity_document',
        ]);
    }

    /**
     * Stripe is different from usual PaymentProcessorServiceInterface implementations,
     * because it manipulates 2 different objects depending on the TransactionType:
     *   - PaymentIntent for credit card payments, wrapping a Charge object
     *   - Charge for SEPA wires
     *
     * @return GenericTransactionDetails
     */
    public function getTransactionDetails(string $transactionMerchantToken): TransactionDetails
    {
        try {
            $type = $this->transactionService->getOneBy([
                'transactionReference' => $transactionMerchantToken,
                static::PROCESSOR_NAME => $this->getName(),
            ])->getType();
        } catch (\Throwable $e) {
            $this->logger->warning('Could not find transaction ' . $transactionMerchantToken . '. It might not have been initialized.');
            $type = null;
        }

        try {
            if ($type === null || $type->equals(TransactionType::CREDITCARD()) === true) {
                $paymentIntent = $this->api->getPaymentIntent($transactionMerchantToken);

                return new GenericTransactionDetails($paymentIntent->id, null, $paymentIntent->status, $paymentIntent);
            }
        } catch (\Throwable $exception) {
            if ($type->equals(TransactionType::CREDITCARD())) {
                throw new TransactionNotFound(
                    'Unable to retrieve payment intent: invalid token: ' . $transactionMerchantToken,
                    0,
                    $exception
                );
            }
        }

        try {
            if ($type === null
                || $type->equals(TransactionType::DIRECT_DEBIT())
                || $type->equals(TransactionType::BANK_WIRE())
            ) {
                $charge = $this->api->getCharge($transactionMerchantToken);

                return new GenericTransactionDetails($charge->id, null, $charge->status, (array) $charge);
            }
        } catch (\Throwable $exception) {
            throw new TransactionNotFound(
                'Unable to retrieve payment intent: invalid token: ' . $transactionMerchantToken,
                0,
                $exception
            );
        }

        throw new UnsupportedTransactionTypeException();
    }

    /**
     * Vous trouverez 2 valeurs ajoutées en dur car certains de nos clients utilisent une
     * version de l'API non mise à jour < 2019-02-11
     * Il faudra remodifier ce bout de code lorsque tous nos clients seront passés à une version
     * mise à jour >= 2020
     */
    public function getHumanStatus($status): TransactionStatus
    {
        switch ($status) {
            case PaymentIntent::STATUS_CANCELED:
            case Charge::STATUS_FAILED:
                return TransactionStatus::FAILED();
            case PaymentIntent::STATUS_REQUIRES_ACTION:
            case PaymentIntent::STATUS_REQUIRES_CAPTURE:
            case PaymentIntent::STATUS_REQUIRES_CONFIRMATION:
            case PaymentIntent::STATUS_REQUIRES_PAYMENT_METHOD:
            case PaymentIntent::STATUS_PROCESSING:
            case Charge::STATUS_PENDING:
            case static::OLD_STATUS_REQUIRES_PAYMENT_METHOD:
            case static::OLD_STATUS_REQUIRES_ACTION:
                return TransactionStatus::PENDING();
            case PaymentIntent::STATUS_SUCCEEDED:
            case Charge::STATUS_SUCCEEDED:
                return TransactionStatus::SUCCESS();
        }

        throw new UnsupportedTransactionStatusException($status);
    }

    public function isTransactionSuccessful(string $transactionMerchantToken): bool
    {
        $transaction = $this->getTransactionDetails($transactionMerchantToken);

        return $transaction->getStatus() === 'succeeded';
    }

    public function isTransactionDetailsCancelled(TransactionDetails $transactionDetails): bool
    {
        return false;
    }

    public function setTransactionService(TransactionService $transactionService): void
    {
        $this->transactionService = $transactionService;
    }

    public function refund(Transaction $originTransaction, Refund $refund): Transaction
    {
        return $this->refundTransaction(
            $originTransaction,
            $refund,
            function (Transaction $transaction) use ($originTransaction, $refund): Transaction {
                try {
                    $amount = $refund->getAmount()->reducePrecisionToCents();
                    $response = $this->api->createRefund([
                        'charge' => $originTransaction->getInformation('stripe_charge_id'),
                        'amount' => $amount->getAmount(),
                    ]);

                    $transaction->setTransactionReference($response->charge);
                    $transaction->addInformation('response', \json_encode($response));
                } catch (\Exception $exception) {
                    $this->transactionService->updateTransactionStatus($transaction, TransactionStatus::FAILED());

                    throw new RefundErrorFromPspException(
                        $this->getName()->getValue(),
                        $exception->getMessage(),
                        $exception->getCode(),
                        $exception
                    );
                }

                return $transaction;
            }
        );
    }

    public function getConfig(): StripeConfig
    {
        return $this->config;
    }

    public function setConfig(StripeConfig $config): self
    {
        $this->config = $config;

        return $this;
    }

    public function getTransactionDetailsByOrderId(int $orderId): ?TransactionDetails
    {
        $transactions = $this->transactionService->findBy([
            'orderId' => $orderId,
            static::PROCESSOR_NAME => $this->getName(),
        ]);

        if (\count($transactions) === 0) {
            return null;
        }

        $transactionRef = $transactions[0]->getTransactionReference();

        return $this->getTransactionDetails($transactionRef);
    }

    /**
     * Check whether the transaction status is successful.
     *
     * @param TransactionDetails $transaction
     *
     * @return bool
     *
     * @throws \Exception
     */
    public function isTransactionDetailsSuccessful(TransactionDetails $transaction): bool
    {
        return $this->getHumanStatus($transaction->getStatus())->equals(TransactionStatus::SUCCESS());
    }

    /**
     * Check whether the transaction status if failed.
     *
     * @param TransactionDetails $transaction
     *
     * @return bool
     *
     * @throws \Exception
     */
    public function isTransactionDetailsFailed(TransactionDetails $transaction): bool
    {
        if (true === $this->getHumanStatus($transaction->getStatus())->equals(TransactionStatus::FAILED())) {
            return true;
        }

        /**
         * On check si il y a eu des tentative de paiement échoué et que la date de
         * création de la transaction dépasse 24h, si c'est le cas le client ne peut
         * plus payer et comme la derniere tentative a échoué on considere que la
         * transaction a échoué
         */
        $paymentIntent = $this->api->getPaymentIntent((string) $transaction->getId());

        if (null !== $paymentIntent->last_payment_error && $paymentIntent->created + 84600 < time()) {
            return true;
        }

        return false;
    }

    public function getName(): PaymentProcessorName
    {
        return PaymentProcessorName::STRIPE();
    }

    /**
     * When the order has been paid with Stripe, the money is transferred from marketplace wallet to vendors wallet.
     * Note: there is a recursive call to handle child orders, but in theory only child orders should be processed.
     */
    protected function transferFundsToVendor(int $orderId, int $parentOrderId = null)
    {
        $order = $this->orderService->getAnyOrder($orderId);
        $legacyOrder = $order->getLegacyOrder();
        $orders = $this->orderService->getChildOrders($orderId);

        if ($order === null) {
            return;
        }

        if ($this->dispatchFundsAction->isAllowed($order) === false && $this->canBeDispatchedAfterFailed($order) === false) {
            return;
        }

        $paymentInfo = $legacyOrder->getPaymentInformation();
        if ((true === empty($paymentInfo['stripe_charge_id']) || true === $paymentInfo['stripe_transfer_done'])
            && false === $this->canBeDispatchedAfterFailed($order)
        ) {
            return;
        }

        // If the order is the parent, we transfer funds for every child orders
        if (null === $parentOrderId && true === $legacyOrder->isParentOrder()) {
            $legacySubOrders = $legacyOrder->getSubOrders();
            foreach ($legacySubOrders as $legacySubOrder) {
                $this->transferFundsToVendor($legacySubOrder->getId(), $orderId);
            }

            $this->advanceDispatchWorkflow([$order]);
            $legacyOrder->setPaymentInformation('stripe_transfer_done', true);

            return;
        }

        $resultTransfer = $this->transferSpecificOrder($order, $parentOrderId);
        if ($resultTransfer === true) {
            $this->advanceDispatchWorkflow($orders);
        } else {
            $commission = $this->commissionService->getTotalCommission($legacyOrder->getOrderV2());
            $this->eventDispatcher->dispatch(
                new DispatchFundsFailedEvent($legacyOrder, $commission),
                DispatchFundsFailedEvent::class
            );
            $this->advanceDispatchFailedWorkflow($orders);
        }
    }

    public function getPayoutBalance(Company $company): ?int
    {
        $balance = $this->api->getBalance(['stripe_account' => $company->getStripeId()]);

        return $balance->available[0]->amount;
    }

    public function assertCanDoPayoutCompany(Company $company): void
    {
        /**
         * There is no such thing possible with Stripe API !
         * Payout process and periodicity are handled by Stripe itself
         */
        parent::assertCanDoPayoutCompany($company);
    }

    public function canDoPayoutCompany(): bool
    {
        return false;
    }

    protected function transferSpecificOrder(Order $order, int $parentOrderId = null): bool
    {
        $legacyOrder = $order->getLegacyOrder();
        $stripeId = $legacyOrder->getCompany()->getStripeId();
        $stripeTransferDone = true;
        $messageException = $origin = '';
        $response = null;
        $totalWithoutCom = new Money(0);

        if (\is_null($stripeId) === true) {
            $messageException = 'Stripe transfer: company does not have account';
            $stripeTransferDone = false;
            $this->logger->error($messageException);
            $legacyOrder->setPaymentInformation('stripe_transfer_done', false);
        } else {
            $account = $this->api->getAccount($stripeId);
            $origin = self::ORIGIN_TRANSACTION_PAYMENT;
            $totalWithoutCom = $order->getBalanceTotal()->subtract(
                $this->commissionService->getTotalCommission($order)
            );

            if ($totalWithoutCom->isPositive()) {
                try {
                    $response = $this->api->createTransfer(
                        [
                            'amount' => $totalWithoutCom->getAmount(),
                            'currency' => $this->getConfig()->getCurrencyCode(),
                            'destination' => $account->id,
                            'description' => 'Order ' . $order->getId(),
                            'transfer_group' => 'ORDER_' . ($parentOrderId ? $parentOrderId : $order->getId()),
                        ],
                        [
                            'idempotency_key' => $this->uuidGenerator->generate(),
                        ]
                    );
                } catch (\Throwable $exception) {
                    $stripeTransferDone = false;
                    $messageException = $exception->getMessage();
                    $this->logger->error($messageException, ['exception' => $exception]);
                }
            } else {
                $legacyOrder->setPaymentInformation('stripe_transfer_done', true);

                return true;
            }

            $this->eventDispatcher->dispatch(
                new TransactionTransferEvent(
                    $order->getId(),
                    $order->getCompany()->getId(),
                    $origin,
                    $stripeId,
                    $messageException,
                    $this->getConfig()->getCurrencyCode(),
                    $totalWithoutCom->getAmount(),
                    ($stripeTransferDone === true) ? TransactionStatus::SUCCESS() : TransactionStatus::FAILED(),
                    $response->id ?? uniqid('wizaplace_'),
                    $this->getName()
                ),
                TransactionsTransferEventType::DISPATCH_FUNDS_TRANSFER_VENDOR()->getValue()
            );
        }

        $legacyOrder->setPaymentInformation('stripe_transfer_done', $stripeTransferDone);

        return $stripeTransferDone;
    }

    protected function captureTransaction(Order $order): bool
    {
        $legacyOrder = $order->getLegacyOrder();
        try {
            $transaction = $this->transactionService->getOneBy([
                'orderId' => $order->getId(),
                static::PROCESSOR_NAME => $this->getName(),
                'type' => TransactionType::CREDITCARD(),
            ]);
            $paymentIntent = $this->api->getPaymentIntent($transaction->getTransactionReference());
            $paymentIntentCaptured = $this->api->capturePaymentIntent($paymentIntent, [
                'amount_to_capture' => $order->getCustomerTotal()->getAmount(),
            ]);

            $transaction->setAmount($order->getCustomerTotal());

            if ($paymentIntentCaptured->status === 'succeeded') {
                $this->transactionService->save(
                    $transaction->setStatus(TransactionStatus::SUCCESS())
                        ->addInformation('stripe_charge_captured', true)
                );

                $legacyOrder->setPaymentInformation('stripe_charge_captured', true);

                return true;
            }

            $this->transactionService->save(
                $transaction
                    ->setStatus(TransactionStatus::FAILED())
                    ->addInformation('stripe_charge_captured', false)
            );

            $legacyOrder->setPaymentInformation('stripe_charge_captured', false);
        } catch (\Throwable $exception) {
            $this->logger->error(
                'Unable to capture the stripe transaction for the order ' . $order->getId(),
                ['exception' => $exception->getMessage()]
            );
        }

        return false;
    }

    protected function isWaitingCaptured(Order $order): bool
    {
        $paymentInfo = $order->getLegacyOrder()->getPaymentInformation();

        return isset($paymentInfo['stripe_charge_id'])
            && \is_string($paymentInfo['stripe_charge_id'])
            && ($paymentInfo['stripe_charge_captured'] ?? null) === false;
    }

    /** @param mixed[] $information Additional information, e.g. credited wallet ID */
    protected function saveCreditCardTransaction(OrderPricesInterface $order, string $transactionId, array $information): Transaction
    {
        $transaction = $this
            ->transactionService->createCreditCardTransaction(
                $order->getId(),
                $order->getCustomerTotal(),
                $this->getName(),
                $information
            )
            ->setTransactionReference($transactionId)
        ;

        //Set Origin and Destination
        $user = $this->userService->get($order->getUserId());
        $transaction->setOrigin($user->getUserId() . ' ' . $user->getFirstname() . ' ' . $user->getLastname());
        $transaction->setDestination(__('technical_wallet'));

        $transaction->setCurrency($this->getConfig()->getCurrencyCode());

        $this->transactionService->save($transaction);

        return $transaction;
    }

    protected function saveCreditCardTransactionDetails(
        int $orderId,
        string $transactionId,
        PaymentIntent $paymentIntent,
        Collection $charge,
        bool $withLegacy
    ): ?Transaction {
        $information = [
            'stripe_payment_intent_id' => $paymentIntent->id,
            'stripe_charge_id' => $charge->data[0]['id'],
            'stripe_charge_captured' => $charge->data[0]['captured'],
        ];

        // Keep legacy transactions persistence to prevent unwanted BC break
        // This were not store before if the transaction wasn't successful.
        if ($withLegacy === true) {
            fn_update_order_payment_info($orderId, $information);
        }

        try {
            $transaction = $this->transactionService->getOneBy([
                'orderId' => $orderId,
                static::PROCESSOR_NAME => $this->getName(),
                'type' => TransactionType::CREDITCARD(),
            ]);

            foreach ($information as $key => $value) {
                $transaction->addInformation($key, $value);
            }

            $this->transactionService->save($transaction);
        } catch (TransactionNotFound $e) {
            $this->logger->error('Transaction not found: order ' . $orderId . ' - transaction ' . $transactionId, [
                'exception' => $e,
            ]);
        }

        return $transaction ?? null;
    }

    /** @param mixed[] $information Additional information, e.g. credited wallet ID */
    protected function saveDirectDebitTransaction(OrderPricesInterface $order, string $transactionId, array $information): Transaction
    {
        // Keep legacy transactions persistence to prevent unwanted BC break
        fn_update_order_payment_info($order->getId(), [
            'stripe_charge_id' => $transactionId,
            'stripe_sepa' => true,
        ]);

        $transaction = $this->transactionService->createDirectDebitTransaction(
            $order->getId(),
            $order->getCustomerTotal(),
            $this->getName(),
            $information
        );

        $transaction->setStatus(TransactionStatus::PENDING());
        $transaction->setTransactionReference($transactionId);
        $this->transactionService->save($transaction);

        return $transaction;
    }

    /** @param Order[] $orders */
    protected function advanceDispatchWorkflow(array $orders): void
    {
        foreach ($orders as $order) {
            $this->dispatchFundsSucceededAction->execute($order);
        }
    }

    /** @param Order[] $orders */
    protected function advanceDispatchFailedWorkflow(array $orders): void
    {
        foreach ($orders as $order) {
            $this->dispatchFundsFailedAction->execute($order);
        }
    }

    public function createCheckoutSession(int $userId, string $successCallbackUrl, ?string $customerId): string
    {
        $params = [
            'payment_method_types' => ['card'],
            'mode' => 'setup',
            'success_url' => $successCallbackUrl . '&session_id={CHECKOUT_SESSION_ID}',
            'cancel_url' => $successCallbackUrl . '&canceled=1',
            'metadata' => [
                'userId' => $userId
            ]
        ];

        if (null !== $customerId) {
            $params['customer'] = $customerId;
        }

        return $this->api->createSession($params)->id;
    }

    private function canBeDispatchedAfterFailed(Order $order): bool
    {
        //to do dispatch when payment refused by stripe and mark as payed by operator
        if ($this->transactionService->hasRefusedPayment($order->getId()) === true
            && $order->isPaid() === true
            && $order->getPayment()->getType()->getValue() === PaymentType::PAYMENT_DEFERMENT()->getValue()
            && $order->getPayment()->getProcessorName()->getValue() === PaymentProcessorName::STRIPE()->getValue()
        ) {
            return true;
        }

        return false;
    }
}
