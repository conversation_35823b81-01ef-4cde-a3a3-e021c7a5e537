<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Processor;

use Doctrine\DBAL\Driver\Connection;
use Wizacha\Marketplace\Payment\PaymentProcessorIdentifier;

class ProcessorService
{
    protected Connection $connection;

    private array $processors = [];

    public function __construct(
        Connection $connection,
        MangoPay $mangoPay,
        HiPay $hiPay,
        SMoney $smoney,
        LemonWay $lemonWay,
        Stripe $stripe
    ) {
        $this->connection = $connection;

        if ($mangoPay->isConfigured()) {
            $this->processors[] = PaymentProcessorIdentifier::MANGOPAY_CARD();
        }
        if ($hiPay->isConfigured()) {
            $this->processors[] = PaymentProcessorIdentifier::HIPAY_CARD();
        }
        if ($smoney->isConfigured()) {
            $this->processors[] = PaymentProcessorIdentifier::SMONEY_CARD();
        }
        if ($lemonWay->isConfigured()) {
            $this->processors[] = PaymentProcessorIdentifier::LEMONWAY_CARD();
        }
        if ($stripe->isConfigured()) {
            $this->processors[] = PaymentProcessorIdentifier::STRIPE_CARD();
        }
        if (true === empty($this->processors)) {
            $this->processors[] = PaymentProcessorIdentifier::OFFLINE();
        }
    }

    public function findByProcessor(PaymentProcessorIdentifier $processor): int
    {
        $statement = $this->connection->prepare("SELECT payment_id FROM cscart_payments WHERE processor_id = :processorId");
        $statement->execute(['processorId' => $processor->getValue()]);

        $paymentId = $statement->fetchColumn(0);

        if (\is_bool($paymentId)) {
            throw new \RuntimeException("No payment ID found for processor ID '" . $processor->getValue() . "'.");
        }

        return (int) $paymentId;
    }

    /** @return PaymentProcessorIdentifier[] */
    public function getConfiguredProcessor(): array
    {
        return $this->processors;
    }

    public function getConfiguredProcessorName(): array
    {
        return array_map(
            function ($paymentProcessorIdentifier) {
                return $paymentProcessorIdentifier->getName();
            },
            $this->getConfiguredProcessor()
        );
    }
}
