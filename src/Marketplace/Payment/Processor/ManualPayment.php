<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Processor;

use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Refund\Entity\Refund;
use Wizacha\Marketplace\Payment\Exception\InvalidStatusException;
use Wizacha\Marketplace\Payment\Exception\RefundAmountIsTooBigException;
use Wizacha\Marketplace\Payment\GenericTransactionDetails;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\TransactionDetails;
use Wizacha\Marketplace\Transaction\Transaction;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionStatus;
use Wizacha\Money\Money;

class ManualPayment implements RefundProcessorInterface, ProcessorInterface, RefundMarketplaceDiscountProcessorInterface
{
    /** @var TransactionService  */
    private $transactionService;

    public function __construct(TransactionService $transactionService)
    {
        $this->transactionService = $transactionService;
    }

    public function refund(Transaction $originTransaction, Refund $refund, string $message = null): Transaction
    {
        if (false === $originTransaction->getStatus()->equals(TransactionStatus::SUCCESS())) {
            throw new InvalidStatusException($originTransaction->getStatus());
        }

        $amount = $refund->getAmount()->reducePrecisionToCents();
        if ($amount->greaterThan($originTransaction->getAmount())) {
            throw new RefundAmountIsTooBigException($originTransaction->getAmount(), $amount);
        }

        $transaction = $this->transactionService->createRefundTransaction(
            $originTransaction->getOrderId(),
            $amount,
            PaymentProcessorName::MANUAL()
        );

        $transaction->setTransactionReference(uniqid('manual_'));

        return $this->transactionService->updateTransactionStatus($transaction, TransactionStatus::SUCCESS());
    }

    public function refundMarketplaceDiscount(Order $order, Refund $refund, Money $amount): Transaction
    {
        $transaction = $this->transactionService->createRefundTransferTransaction(
            $order->getId(),
            $amount,
            PaymentProcessorName::MANUAL(),
            ['refund_id' => $refund->getId()]
        );

        return $this->transactionService->updateTransactionStatus($transaction, TransactionStatus::SUCCESS());
    }

    public function getTransactionDetails(string $identifier): TransactionDetails
    {
        return new GenericTransactionDetails(
            $identifier,
            null,
            TransactionStatus::SUCCESS(),
            []
        );
    }

    public function isConfigured(): bool
    {
        return true;
    }

    public function canDoPayoutCompany(): bool
    {
        return false;
    }

    public function getHumanStatus($status): TransactionStatus
    {
        return TransactionStatus::SUCCESS();
    }

    public function getName(): PaymentProcessorName
    {
        return PaymentProcessorName::MANUAL();
    }
}
