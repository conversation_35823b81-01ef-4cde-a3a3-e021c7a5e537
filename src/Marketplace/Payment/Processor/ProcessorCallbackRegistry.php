<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Processor;

use Wizacha\Marketplace\Payment\Handler\CallbackHandlerInterface;
use Wizacha\Marketplace\Payment\PaymentProcessorName;

class ProcessorCallbackRegistry
{
    /** @var array[] */
    protected $handlers = [];

    /** @param CallbackHandlerInterface[] $handlers */
    public function __construct(iterable $handlers)
    {
        foreach ($handlers as $handler) {
            if (false === $handler instanceof CallbackHandlerInterface) {
                continue;
            }

            if (false === \array_key_exists($handler->getProcessorName()->getValue(), $this->handlers)) {
                $this->handlers[$handler->getProcessorName()->getValue()] = [];
            }

            $this->handlers[$handler->getProcessorName()->getValue()][$handler->getEventName()] = $handler;
        }
    }

    public function getHandler(string $eventName, PaymentProcessorName $processorName): ?CallbackHandlerInterface
    {
        if (\array_key_exists($processorName->getValue(), $this->handlers)
            && \array_key_exists($eventName, $this->handlers[$processorName->getValue()])
        ) {
            return $this->handlers[$processorName->getValue()][$eventName];
        }

        return null;
    }
}
