<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Processor;

use Psr\Log\LoggerInterface;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Payment\Exception\CannotDoPayoutProcessorException;
use Wizacha\Marketplace\Payment\Exception\PayoutException;
use Wizacha\Marketplace\Payment\Processor\Exception\NoValidProcessorException;

class PayoutService
{
    /** @var iterable|AbstractProcessor[] */
    private iterable $processors;

    private CompanyService $companyService;

    private LoggerInterface $logger;

    public function __construct(iterable $processors, CompanyService $companyService, LoggerInterface $logger)
    {
        $this->processors = $processors;
        $this->companyService = $companyService;
        $this->logger = $logger;
    }

    public function doPayoutCompany($companyId): void
    {
        $processor = $this->getFirstValidProcessor();

        try {
            $processor->assertCanDoPayoutCompany(new Company($companyId));
        } catch (CannotDoPayoutProcessorException $exception) {
            throw $exception;
        } catch (\Exception $exception) {
            $this->logger->error('Payout ' . $processor->getName() . ': Fail with company ' . $companyId . ' : ' . $exception->getMessage());

            throw new PayoutException(__('company_trigger_payment_error'), $exception);
        }
    }

    public function getPayoutBalance($companyId): ?int
    {
        $company = new Company($companyId);
        // Some processors can create an account when they retrieve the balance. So we have to check if we can create
        // the psp account before.
        if ($this->companyService->canCreatePspAccount($company) === false) {
            return null;
        }

        try {
            $processor = $this->getFirstValidProcessor();
            return $processor->getPayoutBalance($company);
        } catch (\Exception $exception) {
            return null;
        }
    }

    public function getFirstValidProcessor(): AbstractProcessor
    {
        foreach ($this->processors as $processor) {
            // We don't want NoPayment and Manual payment processors
            if ($processor->isConfigured() === true && $processor instanceof AbstractProcessor === true) {
                return $processor;
            }
        }

        throw new NoValidProcessorException();
    }
}
