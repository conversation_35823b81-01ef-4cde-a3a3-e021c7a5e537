<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Processor;

use Wizacha\Marketplace\Order\Refund\Entity\Refund;
use Wizacha\Marketplace\Payment\Exception\InvalidProcessorException;
use Wizacha\Marketplace\Payment\Exception\InvalidStatusException;
use Wizacha\Marketplace\Payment\Exception\RefundAmountIsTooBigException;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Transaction\Transaction;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionStatus;

// TODO: replace $transactionService by an abstract method getTransactionService()
trait RefundableProcessorTrait
{
    abstract public function getName(): PaymentProcessorName;

    /** @var TransactionService */
    protected $transactionService;

    public function refundTransaction(Transaction $originTransaction, Refund $refund, callable $callback): Transaction
    {
        $refundAmount = $refund->getAmount()->reducePrecisionToCents();

        if (false === $originTransaction->isSuccess()) {
            throw new InvalidStatusException($originTransaction->getStatus());
        }

        if ($this->getName()->getValue() !== $originTransaction->getProcessorName()) {
            throw new InvalidProcessorException($originTransaction->getProcessorName(), $this->getName());
        }

        if ($refundAmount->greaterThan($originTransaction->getAmount())) {
            throw new RefundAmountIsTooBigException($originTransaction->getAmount(), $refundAmount);
        }

        $refundTransaction = $this->transactionService->createRefundTransaction(
            $originTransaction->getOrderId(),
            $refundAmount,
            $this->getName()
        );

        if ($refundTransaction->getProcessorName() === PaymentProcessorName::HIPAY()->getValue()) {
            $refundTransaction->setStatus(TransactionStatus::PENDING());
        } else {
            $refundTransaction->setStatus(TransactionStatus::SUCCESS());
        }

        $refundTransaction->setRefundId($refund->getId());
        $this->transactionService->save($refundTransaction);

        return $callback($refundTransaction);
    }
}
