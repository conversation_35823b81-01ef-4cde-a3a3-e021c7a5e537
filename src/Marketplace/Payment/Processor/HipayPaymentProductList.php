<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Processor;

use MyCLabs\Enum\Enum;

/**
 * See https://developer.hipay.com/api-explorer/api-online-payments#/payments/generateHostedPaymentPage for all values
 *
 * @method static HipayPaymentProductList VISA()
 * @method static HipayPaymentProductList MASTERCARD()
 * @method static HipayPaymentProductList CB()
 * @method static HipayPaymentProductList MAESTRO()
 * @method static HipayPaymentProductList AMERICAN_EXPRESS()
 */
class HipayPaymentProductList extends Enum
{
    private const VISA = 'visa';
    private const MASTERCARD = 'mastercard';
    private const CB = 'cb';
    private const MAESTRO = 'maestro';
    private const AMERICAN_EXPRESS = 'american-express';

    public static function checkCardPaymentType(string $cardPaymentType): string
    {
        return implode(',', array_intersect(explode(',', $cardPaymentType), static::listCardPayments()));
    }

    public static function isCardPaymentType(HipayPaymentProductList $payment): bool
    {
        return \in_array($payment, static::listCardPayments());
    }

    /**
     * @return HipayPaymentProductList[]
     */
    public static function listCardPayments(): array
    {
        return [
            static::VISA(),
            static::MASTERCARD(),
            static::CB(),
            static::MAESTRO(),
            static::AMERICAN_EXPRESS()
        ];
    }
}
