<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Processor;

interface CreditCardProcessorInterface
{
    public function createCardTransaction(int $orderId, string $redirectUrl = null, string $cssUrl = null): string;

    public function isTransactionSuccessful(string $transactionMerchantToken): bool;
}
