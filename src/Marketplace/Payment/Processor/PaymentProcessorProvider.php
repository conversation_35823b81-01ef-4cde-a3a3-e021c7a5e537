<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Processor;

use Wizacha\Marketplace\Payment\Exception\UnsupportedApiException;
use Wizacha\Marketplace\Payment\PaymentProcessorName;

class PaymentProcessorProvider
{
    /** @var ProcessorInterface[] */
    protected $services;

    /** @param ProcessorInterface[] $paymentProcessorServices */
    public function __construct(iterable $paymentProcessorServices)
    {
        foreach ($paymentProcessorServices as $paymentProcessorService) {
            $this->services[\get_class($paymentProcessorService)] = $paymentProcessorService;
        }
    }

    public function getPaymentProcessorService(PaymentProcessorName $name): ProcessorInterface
    {
        $className = $name->getServiceClassName();
        if (\array_key_exists($className, $this->services) === false) {
            throw new UnsupportedApiException();
        }

        return $this->services[$className];
    }
}
