<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment;

use Wizacha\Marketplace\Payment\Response\RedirectPaymentResponse;
use Wizacha\Marketplace\User\User;

interface RenewCardProcessorInterface
{
    /**
     * Renew a credit card.
     *
     * The response returned (HTML page, form, redirection, ...) will enable the user to renew the credit card.
     */
    public function renewCreditCard(User $user, string $redirectUrl, string $cssUrl = null): RedirectPaymentResponse;
}
