<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\SMoney;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\RequestOptions;
use Psr\Http\Message\StreamInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Transaction\TransactionStatus;
use Wizacha\Marketplace\Transaction\TransactionsTransferEventType;
use Wizacha\Marketplace\Transaction\TransactionTransferEvent;

class SMoneyApi
{
    /** @var ?Client  */
    private $client;

    /** @var LoggerInterface  */
    private $logger;

    private EventDispatcherInterface $eventDispatcher;

    public function __construct(
        ?string $apiBaseUrl,
        ?string $token,
        LoggerInterface $logger,
        EventDispatcherInterface $eventDispatcher
    ) {
        if (\is_string($apiBaseUrl) && $apiBaseUrl !== '' && \is_string($token)) {
            if (substr($apiBaseUrl, 0, -1) !== '/') {
                $apiBaseUrl .= '/';
            }

            $this->client = new Client([
                'base_uri' => $apiBaseUrl,
                'headers' => [
                    'Authorization' => 'Bearer ' . $token,
                ],
            ]);
        }

        $this->logger = $logger;
        $this->eventDispatcher = $eventDispatcher;
    }

    public function isConfigured(): bool
    {
        return $this->client instanceof Client;
    }

    public function requestApi(string $method, string $uri, array $json = [], int $version = 1)
    {
        return $this->sendRequest($method, $uri, $json, [], $version);
    }

    public function requestFileApi(string $uri, array $files = [], int $version = 1)
    {
        return $this->sendRequest("post", $uri, [], $files, $version);
    }

    public function refund(
        int $amount,
        string $orderId,
        int $fee = 0,
        int $refundAmount = 0,
        string $parentOrderId = null
    ): array {
        $suffix = $refundAmount > 0 ? '_' . ($refundAmount + 1) : '';

        $request = [
            'amount' => $amount,
            'orderId' => 'refund_' . $orderId . $suffix,
            'fee' => $fee,
        ];

        $url = false === \is_null($parentOrderId)
            ? "payins/cardpayments/$parentOrderId/payments/$orderId/refunds"
            : "payins/cardpayments/$orderId/refunds";

        try {
            return $this->sendRequest("post", $url, $request);
        } catch (ClientException $exception) {
            throw $this->convertGuzzleClientException($exception, 0, $request);
        }
    }

    public function payoutRequest(
        string $appUserId,
        int $amount,
        string $orderId,
        ?string $motif,
        int $companyId,
        string $companyName
    ): void {
        try {
            $account = $this->getBankAccount($appUserId);
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());

            throw $e;
        }

        $request = [
            'OrderId' => $orderId,
            'Amount' => $amount,
            "AccountId" => [
                "AppAccountId" => "payout-{$appUserId}",
            ],
            'Fee' => [
                'AmountWithVAT' => 0,
                'VAT' => 0,
            ],
            'BankAccount' => [
                'Id' => $account['Id'],
            ],
            'Motif' => $motif,
        ];

        $messageException = '';
        $payoutRequestEx = null;
        $isPayoutRequestDone = true;

        try {
            $this->logger->info("SMoney payoutRequest debug", [
                'url'     => 'users/' . $appUserId . '/payouts/storedbankaccounts',
                'request' => $request,
            ]);

            $this->requestApi('post', 'users/' . $appUserId . '/payouts/storedbankaccounts', $request);
        } catch (ClientException $exception) {
            $isPayoutRequestDone = false;
            $payoutRequestEx = $this->convertGuzzleClientException($exception, $appUserId, $request);
            $messageException = $payoutRequestEx->getMessage();
        }

        $this->eventDispatcher->dispatch(
            new TransactionTransferEvent(
                null,
                $companyId,
                "payout-{$appUserId}",
                $companyName . " bank account",
                $messageException,
                'EUR',
                $amount,
                ($isPayoutRequestDone === true) ? TransactionStatus::SUCCESS() : TransactionStatus::FAILED(),
                uniqid('wizaplace_'),
                PaymentProcessorName::SMONEY()
            ),
            TransactionsTransferEventType::VENDOR_WITHDRAWAL()->getValue()
        );

        if ($isPayoutRequestDone === false) {
            throw  $payoutRequestEx;
        }
    }

    public function getBankAccount(string $appUserId): array
    {
        try {
            $response = $this->requestApi('get', 'users/' . $appUserId . '/bankaccounts');

            $this->logger->info("SMoney getBankAccount users/{$appUserId}/bankaccounts", [
                'response' => $response,
            ]);
        } catch (ClientException $exception) {
            throw $this->convertGuzzleClientException($exception, $appUserId);
        }

        // Keep only active accounts
        $accounts = array_filter($response, function ($account) {
            return $account['Status'] === 1;
        });

        if (\count($accounts) > 1) {
            throw new \Exception("SMoney: cannot do payout because the user have more than one active bank account!");
        }

        if (empty($accounts)) {
            throw new \Exception("SMoney: cannot do payout because the user does not have a bank account!");
        }

        return reset($accounts);
    }

    public function getAmountFromSubAccount(string $appUserId): int
    {
        try {
            $response = $this->requestApi('get', 'users/' . $appUserId . '/subaccounts/payout-' . $appUserId);

            $this->logger->info("SMoney getAmountFromBankAccount debug", [
                'request'  => 'users/' . $appUserId . '/subaccounts/payout-' . $appUserId,
                'response' => $response,
            ]);

            return $response['Amount'];
        } catch (ClientException $exception) {
            throw $this->convertGuzzleClientException($exception, $appUserId);
        }
    }

    public function convertGuzzleClientException(ClientException $exception, $companyId, array $data = []): SMoneyException
    {
        $response = $exception->hasResponse() ? json_decode((string) $exception->getResponse()->getBody(), true) : null;

        $this->logger->error("SMoney request error", [
            'exception'   => $exception,
            'trace'       => $exception->getTraceAsString(),
            'message'     => $response['ErrorMessage'],
            'exc_message' => $exception->getMessage(),
            'companyId'   => $companyId,
            'data'        => $data,
        ]);

        // Check if the json contains a "beautiful" exception
        if (false === $response || false === \array_key_exists('ErrorMessage', $response)) {
            return new SMoneyException('Unknown error', 0, $exception);
        }

        return new SMoneyException($response['ErrorMessage'], (int) $response['Code'], $exception);
    }

    private function sendRequest(string $method, string $uri, array $body = [], array $files = [], int $version = 1)
    {
        $options = [
            'headers' => [
                'Accept' => 'application/vnd.s-money.v' . $version . '+json',
                'Content-Type' => 'application/vnd.s-money.v' . $version . '+json',
            ],
        ];

        if (\count($body) > 0) {
            $options['body'] = json_encode($body);
        }

        if (\count($files) > 0) {
            unset($options['headers']['Content-Type']);

            $multipart = [];
            foreach ($files as $file) {
                if (false === ($file['contents'] instanceof StreamInterface)) {
                    $file['contents'] = \GuzzleHttp\Psr7\stream_for($file['contents']);
                }

                $multipart[] = [
                    'name'     => basename($file['name']),
                    'contents' => $file['contents']->getContents(),
                    'filename' => $file['name'],
                ];
            }
            $options[RequestOptions::MULTIPART] = $multipart;
        }

        return json_decode(
            $this->client->$method($uri, $options)->getBody()->getContents(),
            true
        );
    }
}
