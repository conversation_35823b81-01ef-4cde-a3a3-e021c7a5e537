<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\SMoney;

use Wizacha\Marketplace\Payment\PaymentProcessor;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\PaymentType;
use Wizacha\Marketplace\Payment\Processor\SMoney;
use Wizacha\Marketplace\Payment\Response\PaymentResponse;
use Wizacha\Marketplace\Payment\Response\RedirectPaymentResponse;

class SMoneyCardProcessor implements PaymentProcessor
{
    /**
     * @var SMoney
     */
    private $smoney;

    public function __construct(SMoney $smoney)
    {
        $this->smoney = $smoney;
    }

    public function startPayment(int $orderId, string $redirectUrl = null, string $cssUrl = null): PaymentResponse
    {
        $url = $this->smoney->createCardTransaction($orderId, $redirectUrl);

        return new RedirectPaymentResponse($url);
    }

    public function getType(): PaymentType
    {
        return PaymentType::CREDIT_CARD();
    }

    public function getName(): PaymentProcessorName
    {
        return PaymentProcessorName::SMONEY();
    }

    public function isConfigured(): bool
    {
        return $this->smoney->isConfigured();
    }
}
