<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\SMoney\Enum;

use MyCLabs\Enum\Enum;

/**
 * List of SMoney transaction status with their API value.
 *
 * @see http://api.s-money.fr/documentation/utiliser-l-api/paiement-par-carte-bancaire/
 *
 * @method static SMoneyTransactionStatus WAITING()
 * @method static SMoneyTransactionStatus COMPLETED()
 * @method static SMoneyTransactionStatus REFUNDED()
 * @method static SMoneyTransactionStatus FAILED()
 * @method static SMoneyTransactionStatus WAITING_FOR_VALIDATION()
 * @method static SMoneyTransactionStatus CANCELLED()
 * @method static SMoneyTransactionStatus WAITING_FOR_TRANSFER()
 */
class SMoneyTransactionStatus extends Enum
{
    protected const WAITING = 0;
    protected const COMPLETED = 1;
    protected const REFUNDED = 2;
    protected const FAILED = 3;
    protected const WAITING_FOR_VALIDATION = 4;
    protected const CANCELLED = 5;
    protected const WAITING_FOR_TRANSFER = 6;
}
