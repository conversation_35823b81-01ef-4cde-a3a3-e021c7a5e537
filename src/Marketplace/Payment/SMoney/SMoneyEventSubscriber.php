<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\SMoney;

use Wizacha\Events\IterableEvent;
use Wizacha\Marketplace\Company\CompanyEvents;
use Wizacha\Marketplace\Company\Event\CompanyLegalDocumentsUpdated;
use Wizacha\Marketplace\CreditCard\CreditCard;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Order\Event\OrderStatusUpdated;
use Wizacha\Marketplace\Order\Exception\ActionNotAllowed;
use Wizacha\Marketplace\Order\OrderEvents;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\Payment\AbstractProcessorEventSubscriber;
use Wizacha\Marketplace\Payment\Event\SmoneyPaymentCallbackEvent;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Order;

class SMoneyEventSubscriber extends AbstractProcessorEventSubscriber
{
    public function getName(): PaymentProcessorName
    {
        return PaymentProcessorName::SMONEY();
    }

    /** @return array [(string) Event class name => (string) self method name] */
    public static function getSubscribedEvents(): array
    {
        return [
            SmoneyPaymentCallbackEvent::class => ['updateTransactionStatus', 0],
            OrderEvents::UPDATED  => ['onOrderUpdate', 0],
            CompanyEvents::APPROVED => ['onCompanyApproved', 0],
            CompanyEvents::SEND_TO_PSP => ['onCompanyIbanBicUpdated', 0],
            CompanyEvents::IBAN_BIC_UPDATED => ['onCompanyIbanBicUpdated', 0],
            CompanyEvents::LEGAL_DOCUMENTS_UPDATED => ['onKycUpdated', 0],
        ];
    }

    /**
     * @internal Must be called by the event dispatcher
     */
    public function onOrderUpdate(OrderStatusUpdated $event): void
    {
        if ($this->processor->isConfigured() && $event->getStatusTo() === OrderStatus::COMPLETED) {
            try {
                $this->processor->dispatchFunds(new Order($event->getId()));
            } catch (ActionNotAllowed $exception) {
                $this->logger->error('SMoney: workflow action not allowed', [
                    'exception' => $exception,
                    'message'   => $exception->getMessage(),
                    'trace'     => $exception->getTraceAsString(),
                    'orderId'   => $event->getId(),
                ]);
            } catch (\Exception $exception) {
                $this->logger->error('SMoney: error on order update, cannot do payout', [
                    'exception' => $exception,
                    'message'   => $exception->getMessage(),
                    'trace'     => $exception->getTraceAsString(),
                    'orderId'   => $event->getId(),
                ]);
            }
        }
    }

    /**
     * @internal Must be called by the event dispatcher
     */
    public function onCompanyApproved(IterableEvent $event): void
    {
        if (false === $this->processor->isConfigured()) {
            return;
        }

        foreach ($event as $companyId) {
            try {
                $this->processor->getOrCreateProfessionalUser((int) $companyId);
            } catch (\Exception $exception) {
                $this->logger->error('SMoney: error on company approve', [
                    'exception' => $exception,
                ]);
            }
        }
    }

    /**
     * @internal Must be called by the event dispatcher
     * @throws \Exception
     */
    public function onCompanyIbanBicUpdated(IterableEvent $event): void
    {
        if ($this->processor->isConfigured()) {
            foreach ($event as $companyId) {
                try {
                    $this->processor->registerCompanyIban(new Company($companyId));
                } catch (SMoneyException $exception) {
                    $this->logger->error('SMoney: error on company iban update', [
                        'exception' => $exception,
                    ]);

                    fn_set_notification('E', __('error'), "SMoney error: {$exception->getMessage()}");
                } catch (\Exception $exception) {
                    $this->logger->error('SMoney: error on company iban update', [
                        'exception' => $exception,
                    ]);
                }
            }
        }
    }

    public function onKycUpdated(CompanyLegalDocumentsUpdated $kyc): void
    {
        if ($this->processor->isConfigured()) {
            $this->processor->sendKyc($kyc->getCompanyId());
        }
    }

    public function createCardFromTransaction($data): ?CreditCard
    {
        return null;
    }
}
