<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment;

use Wizacha\Marketplace\Payment\Processor\ProcessorInterface;
use Wizacha\Marketplace\Payment\Response\PaymentResponse;

interface PaymentProcessor extends ProcessorInterface
{
    /**
     * Start paying an order.
     *
     * The response returned (HTML page, form, redirection, ...) will enable the user to pay their order.
     */
    public function startPayment(int $orderId, string $redirectUrl = null, string $cssUrl = null): PaymentResponse;
    public function getType(): PaymentType;
    public function getName(): PaymentProcessorName;
}
