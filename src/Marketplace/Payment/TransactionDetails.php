<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment;

interface TransactionDetails
{
    /** return mixed Transaction unique identifier */
    public function getId();

    /** return mixed Order identifier */
    public function getOrderId();

    /** return mixed Transaction status */
    public function getStatus();

    /** return mixed raw data Transaction */
    public function getRawData();
}
