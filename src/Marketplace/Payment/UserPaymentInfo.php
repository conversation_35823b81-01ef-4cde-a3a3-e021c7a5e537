<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment;

class UserPaymentInfo
{
    private int $userId;

    /**
     * Quote from Mangopay: At this moment, identifier is a numeric string - in the future, will be GUID.
     */
    private ?string $mangopayUserId;

    private ?string $stripeBankAccountToken;

    private ?string $stripeCustomerId;

    private ?int $hipaySepaAgreement;

    private ?int $lemonwaySepaAgreement;

    private ?string $lemonwayElectronicSignature;

    public function __construct(int $userId)
    {
        $this->userId = $userId;
    }

    public function getUserId(): int
    {
        return $this->userId;
    }

    public function getMangopayUserId(): ?string
    {
        return $this->mangopayUserId;
    }

    public function setMangopayUserId(string $mangopayUserId = null): self
    {
        $this->mangopayUserId = $mangopayUserId;

        return $this;
    }

    public function getStripeBankAccountToken(): ?string
    {
        return $this->stripeBankAccountToken;
    }

    public function setStripeBankAccountToken(?string $stripeBankAccountToken): self
    {
        $this->stripeBankAccountToken = $stripeBankAccountToken;

        return $this;
    }

    public function getStripeCustomerId(): ?string
    {
        return $this->stripeCustomerId;
    }

    public function setStripeCustomerId(string $stripeCustomerId): self
    {
        $this->stripeCustomerId = $stripeCustomerId;

        return $this;
    }

    public function getHipaySepaAgreement(): ?int
    {
        return $this->hipaySepaAgreement;
    }

    public function setHipaySepaAgreement(int $hipaySepaAgreement): self
    {
        $this->hipaySepaAgreement = $hipaySepaAgreement;

        return $this;
    }

    public function getLemonwaySepaAgreement(): ?int
    {
        return $this->lemonwaySepaAgreement;
    }

    public function setLemonwaySepaAgreement(int $lemonwaySepaAgreement): self
    {
        $this->lemonwaySepaAgreement = $lemonwaySepaAgreement;

        return $this;
    }

    public function getLemonwayElectronicSignature(): ?string
    {
        return $this->lemonwayElectronicSignature;
    }

    public function setLemonwayElectronicSignature(string $lemonwayElectronicSignature): self
    {
        $this->lemonwayElectronicSignature = $lemonwayElectronicSignature;

        return $this;
    }
}
