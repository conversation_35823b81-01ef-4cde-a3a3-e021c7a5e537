<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Event;

use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Symfony\Contracts\EventDispatcher\Event;
use Wizacha\Component\Notification\NotificationEvent;

class BankwireFailedToRetrieveOrderEvent extends Event implements NotificationEvent
{
    /** @var string */
    private $tokenValue;

    /** @var string */
    private $amount;

    /** @var string */
    private $transactionId;

    /** @var string */
    private $currency;

    public function __construct(string $tokenValue, string $amount, string $transactionId, string $currency)
    {
        $this->tokenValue = $tokenValue;
        $this->amount = $amount;
        $this->transactionId = $transactionId;
        $this->currency = $currency;
    }

    public function getTokenValue(): string
    {
        return $this->tokenValue;
    }

    public function getAmount(): string
    {
        return $this->amount;
    }

    public function getTransactionId(): string
    {
        return $this->transactionId;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public static function getDescription(): string
    {
        return 'BankwireFailedToRetrieveOrderEvent';
    }


    public static function buildDebugForm(FormBuilder $form)
    {
        $form->add('tokenValue', TextType::class);
        $form->add('amount', TextType::class);
        $form->add('transactionId', TextType::class);
        $form->add('currency', TextType::class);
    }

    public static function createFromForm(Form $form)
    {
        return new static(
            $form->getData()['tokenValue'],
            $form->getData()['amount'],
            $form->getData()['transactionId'],
            $form->getData()['currency']
        );
    }
}
