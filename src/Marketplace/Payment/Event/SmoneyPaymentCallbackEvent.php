<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Event;

use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Transaction\TransactionType;
use Wizacha\Order;

class SmoneyPaymentCallbackEvent extends PaymentCallbackEvent
{
    public function __construct(
        Order $order,
        string $reference,
        TransactionType $transactionType
    ) {
        parent::__construct($order, $reference, PaymentProcessorName::SMONEY(), $transactionType);
    }
}
