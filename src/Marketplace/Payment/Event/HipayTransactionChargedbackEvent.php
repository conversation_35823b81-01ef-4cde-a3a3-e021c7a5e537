<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Event;

use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Wizacha\Component\Notification\NotificationEvent;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Transaction\TransactionType;
use Wizacha\Order;

class HipayTransactionChargedbackEvent extends PaymentCallbackEvent implements NotificationEvent
{
    public function __construct(
        Order $order,
        string $reference,
        TransactionType $transactionType
    ) {
        parent::__construct($order, $reference, PaymentProcessorName::HIPAY(), $transactionType);
    }

    public static function getDescription(): string
    {
        return 'HipayTransactionChargedbackEvent';
    }

    public static function buildDebugForm(FormBuilder $form)
    {
        $form->add('orderId', IntegerType::class);
        $form->add('reference', TextType::class);
    }

    public static function createFromForm(Form $form)
    {
        return new static(
            new Order($form->getData()['orderId']),
            $form->getData()['reference'],
            TransactionType::CREDITCARD()
        );
    }
}
