<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Event;

use Symfony\Contracts\EventDispatcher\Event;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Wizacha\Component\Notification\NotificationEvent;
use Wizacha\Money\Money;
use Wizacha\Order;

class DispatchFundsFailedEvent extends Event implements NotificationEvent
{
    private Order $order;
    private Money $commission;

    public function __construct(Order $order, Money $commission)
    {
        $this->order = $order;
        $this->commission = $commission;
    }

    /**
     * Build the Symfony Form that will be shown in the backend to simulate the event.
     */
    public static function buildDebugForm(FormBuilder $form)
    {
        $form
            ->add('order', IntegerType::class)
            ->add('commission', IntegerType::class)
        ;
    }

    /**
     * Create a instance of that class from the submitted form.
     *
     * @return static
     */
    public static function createFromForm(Form $form)
    {
        $values = $form->getData();
        return new static(new Order($values['order']), new Money($values['commission']));
    }

    /**
     * Returns the translation that describes the event.
     *
     * It will be used in the back-office to list all notifications.
     *
     * You must return a translation ID!
     */
    public static function getDescription(): string
    {
        return 'order_status';
    }

    public function getOrder(): Order
    {
        return $this->order;
    }

    public function getCommission(): Money
    {
        return $this->commission;
    }
}
