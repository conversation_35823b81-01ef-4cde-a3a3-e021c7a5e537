<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Event;

use Symfony\Contracts\EventDispatcher\Event;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Order;
use Wizacha\Marketplace\Transaction\TransactionType;

/**
 * To be triggered on a payment callback
 */
abstract class PaymentCallbackEvent extends Event
{
    protected Order $order;
    protected string $reference;
    protected PaymentProcessorName $processorName;
    protected TransactionType $transactionType;

    public function __construct(
        Order $order,
        string $reference,
        PaymentProcessorName $processorName,
        TransactionType $transactionType
    ) {
        $this->order = $order;
        $this->reference = $reference;
        $this->processorName = $processorName;
        $this->transactionType = $transactionType;
    }

    public function getOrder(): Order
    {
        return $this->order;
    }

    public function getReference(): string
    {
        return $this->reference;
    }

    public function getProcessorName(): PaymentProcessorName
    {
        return $this->processorName;
    }

    public function getTransactionType(): TransactionType
    {
        return $this->transactionType;
    }
}
