<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Event;

use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Symfony\Contracts\EventDispatcher\Event;
use Wizacha\Component\Notification\NotificationEvent;
use Wizacha\Marketplace\Order\Order;

class BankwireNotificationFailedStatusEvent extends Event implements NotificationEvent
{
    /** @var Order[] */
    private $orders;

    /** @var string */
    private $amount;

    /** @var string */
    private $transactionId;

    /** @var string|null */
    private $pspName;

    public function __construct(array $orders, string $amount, string $transactionId, string $pspName = null)
    {
        $this->orders = $orders;
        $this->amount = $amount;
        $this->transactionId = $transactionId;
        $this->pspName = $pspName;
    }

    /** @return int[] orders id */
    public function getOrdersId(): array
    {
        return \array_map(function (Order $order): int {
            return $order->getId();
        }, $this->orders);
    }

    /** @return Order[] */
    public function getOrders(): array
    {
        return $this->orders;
    }

    public function getAmount(): string
    {
        return $this->amount;
    }

    public function getTransactionId(): string
    {
        return $this->transactionId;
    }

    public function getPspName(): ?string
    {
        return $this->pspName;
    }

    public static function getDescription(): string
    {
        return 'BankwireNotificationFailedStatusEvent';
    }

    public static function buildDebugForm(FormBuilder $form)
    {
        $form->add('orderId', IntegerType::class);
        $form->add('amount', TextType::class);
        $form->add('transactionId', TextType::class);
        $form->add('pspName', TextType::class);
    }

    public static function createFromForm(Form $form)
    {
        $order = container()->get('marketplace.order.order_service')->getOrder($form->get('orderId'));
        return new static(
            [$order],
            $form->getData()['amount'],
            $form->getData()['transactionId'],
            $form->getData()['pspName']
        );
    }
}
