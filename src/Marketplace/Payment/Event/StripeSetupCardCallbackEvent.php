<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Event;

class StripeSetupCardCallbackEvent
{
    private string $sessionId;
    private ?string $customerId;

    public function __construct(string $sessionId, ?string $customerId)
    {
        $this->sessionId = $sessionId;
        $this->customerId = $customerId;
    }

    public function getCustomerId(): ?string
    {
        return $this->customerId;
    }

    public function getSessionId(): string
    {
        return $this->sessionId;
    }
}
