<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Event;

use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Symfony\Contracts\EventDispatcher\Event;
use Wizacha\Component\Notification\NotificationEvent;

class InternalTransferErrorEvent extends Event implements NotificationEvent
{
    /** @var int */
    private $orderId;

    /** @var float */
    private $amount;

    /** @var string */
    private $sourceTransactionId;

    /** @var string|null */
    private $internalTransactionId;

    /** @var string */
    private $fromWallet;

    /** @var string */
    private $toWallet;

    public function __construct(
        int $orderId,
        float $amount,
        string $sourceTransactionId,
        ?string $internalTransactionId,
        string $fromWallet,
        string $toWallet
    ) {
        $this->orderId = $orderId;
        $this->amount = $amount;
        $this->sourceTransactionId = $sourceTransactionId;
        $this->internalTransactionId = $internalTransactionId;
        $this->fromWallet = $fromWallet;
        $this->toWallet = $toWallet;
    }

    public function getOrderId(): int
    {
        return $this->orderId;
    }

    public function getAmount(): float
    {
        return $this->amount;
    }

    public function getSourceTransactionId(): string
    {
        return $this->sourceTransactionId;
    }

    public function getInternalTransactionId(): ?string
    {
        return $this->internalTransactionId;
    }

    public function getFromWallet(): string
    {
        return $this->fromWallet;
    }

    public function getToWallet(): string
    {
        return $this->toWallet;
    }

    public static function getDescription(): string
    {
        return 'InternalTransferErrorEvent';
    }

    public static function buildDebugForm(FormBuilder $form)
    {

        $form->add('orderId', IntegerType::class);
        $form->add('amount', NumberType::class);
        $form->add('sourceTransactionId', TextType::class);
        $form->add('internalTransactionId', TextType::class);
        $form->add('fromWallet', TextType::class);
        $form->add('toWallet', TextType::class);
    }

    public static function createFromForm(Form $form)
    {
        return new static(
            $form->getData()['orderId'],
            $form->getData()['amount'],
            $form->getData()['sourceTransactionId'],
            $form->getData()['internalTransactionId'],
            $form->getData()['fromWallet'],
            $form->getData()['toWallet']
        );
    }
}
