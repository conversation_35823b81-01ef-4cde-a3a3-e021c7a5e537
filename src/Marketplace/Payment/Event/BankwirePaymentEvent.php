<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Event;

use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Symfony\Contracts\EventDispatcher\Event;
use Wizacha\Component\Notification\NotificationEvent;
use Wizacha\Order;

class BankwirePaymentEvent extends Event implements NotificationEvent
{
    private Order $order;
    private array $paymentDetails;

    public function __construct(Order $order, array $paymentDetails)
    {
        $this->order = $order;
        $this->paymentDetails = $paymentDetails;
    }

    public function getOrder(): Order
    {
        return $this->order;
    }

    /** @return array with keys IBAN, BIC, WireReference, OwnerName, completeUrl, amount. */
    public function getPaymentDetails(): array
    {
        return $this->paymentDetails;
    }

    public static function getDescription(): string
    {
        return 'bank_wire_payment';
    }

    public static function buildDebugForm(FormBuilder $form)
    {
        $form->add('orderId', IntegerType::class);
    }

    public static function createFromForm(Form $form)
    {
        $orderId = $form->getData()['orderId'];
        $order = new Order($orderId);
        $dataInfo = [
            'amount' => $order->getCustomerTotal(),
            'IBAN' => 'IBANNumber',
            'BIC' => 'BICNumber',
            'label' => 'labelField',
            'comment' => null,
            'socialName' => 'socialName',
            'address' => 'address',
            'completeUrl' => 'completeUrl',
        ];
        return new static(
            $order,
            $dataInfo
        );
    }
}
