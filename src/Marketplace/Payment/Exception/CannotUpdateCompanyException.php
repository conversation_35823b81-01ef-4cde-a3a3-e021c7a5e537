<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Exception;

class CannotUpdateCompanyException extends \Exception
{
    /** @var \Throwable */
    protected $previous;

    /** @var int */
    protected $companyId;

    /** @var string */
    protected $status;

    /** @var string */
    protected $statusFrom;

    /** @var string */
    protected $reason;

    public function __construct(
        int $companyId,
        string $status,
        string $statusFrom,
        string $reason,
        string $message = "",
        int $code = 0,
        \Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);

        $this->message = $message;
        $this->code = $code;
        $this->previous = $previous;
        $this->companyId = $companyId;
        $this->status = $status;
        $this->statusFrom = $statusFrom;
        $this->reason = $reason;
    }

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function getStatusFrom(): string
    {
        return $this->statusFrom;
    }

    public function getReason(): string
    {
        return $this->reason;
    }
}
