<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Exception;

use Wizacha\Marketplace\Payment\PaymentProcessorName;

class CannotDoPayoutProcessorException extends \Exception
{
    public function __construct(PaymentProcessorName $expected)
    {
        parent::__construct(\sprintf('Vendor Withdrawal unavailable for %s.', $expected->getValue()));
    }
}
