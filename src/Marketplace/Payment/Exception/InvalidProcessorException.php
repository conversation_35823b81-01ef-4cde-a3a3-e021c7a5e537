<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Exception;

use Wizacha\Marketplace\Payment\PaymentProcessorName;

class InvalidProcessorException extends \Exception
{
    public function __construct($origin, PaymentProcessorName $expected, \Throwable $previous = null)
    {
        parent::__construct(
            sprintf('Processor must be %s, %s instead.', $expected->getValue(), $origin),
            0,
            $previous
        );
    }
}
