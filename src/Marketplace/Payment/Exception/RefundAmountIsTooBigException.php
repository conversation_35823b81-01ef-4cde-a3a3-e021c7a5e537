<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Exception;

use Wizacha\Money\Money;

class RefundAmountIsTooBigException extends \Exception
{
    public function __construct(Money $originAmount, Money $amount, \Throwable $previous = null)
    {
        parent::__construct(
            sprintf(
                "Refund amount (%s) greater than origin (%s)",
                $amount->getConvertedAmount(),
                $originAmount->getConvertedAmount()
            ),
            0,
            $previous
        );
    }
}
