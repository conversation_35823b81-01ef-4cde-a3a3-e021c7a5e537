<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Exception;

use Wizacha\Marketplace\Transaction\TransactionStatus;

class InvalidStatusException extends \Exception
{
    public function __construct(TransactionStatus $status, \Throwable $previous = null)
    {
        parent::__construct(
            sprintf(
                'Transaction status must be %s, %s instead.',
                TransactionStatus::SUCCESS()->getValue(),
                $status->getValue()
            ),
            0,
            $previous
        );
    }
}
