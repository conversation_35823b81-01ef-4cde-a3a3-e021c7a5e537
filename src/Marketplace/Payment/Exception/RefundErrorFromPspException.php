<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Exception;

use Throwable;
use Wizacha\Marketplace\Transaction\Transaction;

class RefundErrorFromPspException extends \Exception
{
    /** @var string */
    protected $processorName;

    /** @var string */
    protected $originalMessage;

    public function __construct($processorName, $message = "", $code = 0, Throwable $previous = null)
    {
        parent::__construct(__('refund.error.refund_failed'), $code, $previous);

        $this->processorName = $processorName;
        $this->originalMessage = $message;
    }

    public function getProcessorName(): string
    {
        return $this->processorName;
    }

    public function setProcessorName(string $processorName): self
    {
        $this->processorName = $processorName;

        return $this;
    }

    public function getOriginalMessage(): string
    {
        return $this->originalMessage;
    }
}
