<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Stripe;

use Wizacha\Marketplace\Payment\AbstractPaymentLogger;

/**
 * This logger is used in StripeApi for every API call.
 *
 * @see StripeApi
 */
class StripeLogger extends AbstractPaymentLogger
{
    private const STRIPE_LOG_NAME = 'Stripe API SDK';

    public function requestSuccess(StripeHTTPClient $stripeHTTPClient): void
    {
        $this->logRequestSuccess(
            self::STRIPE_LOG_NAME . ' Request Success',
            $stripeHTTPClient->getLatestRequestMethod(),
            $stripeHTTPClient->getLatestRequestUrl(),
            $stripeHTTPClient->getLatestRequestParams(),
            $stripeHTTPClient->getLatestResponseCode(),
            $stripeHTTPClient->getLatestResponseBody()
        );
    }

    public function requestException(StripeHTTPClient $stripeHTTPClient, \Throwable $exception): void
    {
        $this->logRequestException(
            self::STRIPE_LOG_NAME . ' Error',
            $stripeHTTPClient->getLatestRequestMethod(),
            $stripeHTTPClient->getLatestRequestUrl(),
            $stripeHTTPClient->getLatestRequestParams(),
            $exception
        );
    }
}
