<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Stripe;

use Stripe\HttpClient\CurlClient;

/**
 * This class is used as a wrapper for the stripe http client.
 * We want to log the requests sent to the Stripe API,
 * therefore we need to get the necessary request and response info from the Stripe objects
 *
 * Then, when we want to log a request, we need to get this client to pass it to the log function
 * @see StripeLogger
 */
class StripeHTTPClient extends CurlClient
{
    private string $latestRequestMethod;
    private string $latestRequestUrl;
    private array $latestRequestParams;
    private string $latestResponseBody;
    private int $latestResponseCode;

    public function request($method, $absUrl, $headers, $params, $hasFile)
    {
        $this->latestRequestMethod = $method;
        $this->latestRequestUrl = $absUrl;
        $this->latestRequestParams = $params;

        list($rbody, $rcode, $rheaders) = $this->parentRequest($method, $absUrl, $headers, $params, $hasFile);

        $this->latestResponseBody = $rbody;
        $this->latestResponseCode = $rcode;

        return [$rbody, $rcode, $rheaders];
    }

    protected function parentRequest($method, $absUrl, $headers, $params, $hasFile)
    {
        return parent::request($method, $absUrl, $headers, $params, $hasFile);
    }

    public function getLatestRequestMethod(): string
    {
        return $this->latestRequestMethod;
    }

    public function getLatestRequestUrl(): string
    {
        return $this->latestRequestUrl;
    }

    public function getLatestRequestParams(): array
    {
        return $this->latestRequestParams;
    }

    public function getLatestResponseBody(): string
    {
        return $this->latestResponseBody;
    }

    public function getLatestResponseCode(): int
    {
        return $this->latestResponseCode;
    }
}
