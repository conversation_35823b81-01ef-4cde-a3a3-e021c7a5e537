<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Stripe\Handler;

use Psr\Log\LoggerInterface;
use Wizacha\Marketplace\Payment\Handler\CallbackHandlerInterface;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\UserPaymentInfoService;

class CustomerSourceCreatedHandler implements CallbackHandlerInterface
{
    public const EVENT_NAME = 'customer.source.created';

    /** @var UserPaymentInfoService */
    protected $userPaymentInfoService;

    /** @var LoggerInterface */
    protected $logger;

    public function __construct(UserPaymentInfoService $userPaymentInfoService, LoggerInterface $logger)
    {
        $this->userPaymentInfoService = $userPaymentInfoService;
        $this->logger = $logger;
    }

    /** @param mixed[] $payload */
    public function process(array $payload): void
    {
        if (\array_key_exists('sepa_debit', $payload['object'])) {
            $this->saveStripeBankAccountToken($payload['object']);
        }
    }

    public function getProcessorName(): PaymentProcessorName
    {
        return PaymentProcessorName::STRIPE();
    }

    public function getEventName(): string
    {
        return static::EVENT_NAME;
    }

    /** @param mixed[] $data */
    protected function saveStripeBankAccountToken(array $data): void
    {
        $tokenId = $data['id'] ?? null;
        $customerId = $data['customer'] ?? null;

        if (false === \is_string($tokenId) || false === \is_string($customerId)) {
            return;
        }

        $userId = $this->userPaymentInfoService->getUserIdFromStripeCustomerId($customerId);

        if ($userId === null) {
            $this->logger->warning("Stripe Callback " . static::EVENT_NAME . ": No user in database for customerId.", [
                'data' => ["customerId" => $customerId],
            ]);

            return;
        }

        $this
            ->userPaymentInfoService
            ->setStripeBankAccountToken(
                $userId,
                $tokenId
            )
        ;
    }
}
