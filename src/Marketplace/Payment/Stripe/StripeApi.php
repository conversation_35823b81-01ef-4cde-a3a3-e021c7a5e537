<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Stripe;

use Stripe\Account;
use Stripe\ApiRequestor;
use Stripe\ApiResource;
use Stripe\Charge;
use Stripe\Checkout\Session;
use Stripe\Collection;
use Stripe\Customer;
use Stripe\File;
use Stripe\PaymentIntent;
use Stripe\PaymentMethod;
use Stripe\Person;
use Stripe\Refund;
use Stripe\SetupIntent;
use Stripe\Source;
use Stripe\Transfer;
use Stripe\Balance;
use Stripe\Payout;

class StripeApi
{
    private StripeLogger $stripeLogger;
    private StripeHTTPClient $stripeHTTPClient;

    public function __construct(StripeLogger $stripeLogger, StripeHTTPClient $stripeHTTPClient)
    {
        $this->stripeLogger = $stripeLogger;
        $this->stripeHTTPClient = $stripeHTTPClient;

        ApiRequestor::setHttpClient($stripeHTTPClient);
    }

    public function getStripeHTTPClient(): StripeHTTPClient
    {
        return $this->stripeHTTPClient;
    }

    /** @param array|string|null $options */
    public function createPaymentIntent(array $params = null, $options = null): PaymentIntent
    {
        try {
            $paymentIntent = PaymentIntent::create($params, $options);

            $this->stripeLogger->requestSuccess($this->stripeHTTPClient);

            return $paymentIntent;
        } catch (\Throwable $exception) {
            $this->stripeLogger->requestException($this->stripeHTTPClient, $exception);

            throw $exception;
        }
    }

    /** @param mixed[] $options */
    public function getBalance(array $options): Balance
    {
        try {
            $balance = Balance::retrieve($options);

            $this->stripeLogger->requestSuccess($this->stripeHTTPClient);

            return $balance;
        } catch (\Throwable $exception) {
            $this->stripeLogger->requestException($this->stripeHTTPClient, $exception);

            throw $exception;
        }
    }

    /**
     * @param mixed[] $params
     * @param mixed[] $options
     */
    public function doPayout(array $params, array $options): void
    {
        try {
            Payout::create($params, $options);

            $this->stripeLogger->requestSuccess($this->stripeHTTPClient);
        } catch (\Throwable $exception) {
            $this->stripeLogger->requestException($this->stripeHTTPClient, $exception);

            throw $exception;
        }
    }

    /**
     * @param array|string $paymentIntentId
     * @param array|string|null $opts
     */
    public function getPaymentIntent($paymentIntentId, $opts = null): PaymentIntent
    {
        try {
            $paymentIntent = PaymentIntent::retrieve($paymentIntentId, $opts);

            $this->stripeLogger->requestSuccess($this->stripeHTTPClient);

            return $paymentIntent;
        } catch (\Throwable $exception) {
            $this->stripeLogger->requestException($this->stripeHTTPClient, $exception);

            throw $exception;
        }
    }

    public function getPaymentMethod($paymentMethodId, $opts = null): PaymentMethod
    {
        try {
            $paymentMethod = PaymentMethod::retrieve($paymentMethodId, $opts);

            $this->stripeLogger->requestSuccess($this->stripeHTTPClient);

            return $paymentMethod;
        } catch (\Throwable $exception) {
            $this->stripeLogger->requestException($this->stripeHTTPClient, $exception);

            throw $exception;
        }
    }

    public function capturePaymentIntent(PaymentIntent $paymentIntent, array $params = null): PaymentIntent
    {
        try {
            $paymentIntent = $paymentIntent->capture($params);

            $this->stripeLogger->requestSuccess($this->stripeHTTPClient);

            return $paymentIntent;
        } catch (\Throwable $exception) {
            $this->stripeLogger->requestException($this->stripeHTTPClient, $exception);

            throw $exception;
        }
    }

    /** @param array|string|null $opts */
    public function getAllCharges(array $params = null, $opts = null): Collection
    {
        try {
            $charges = Charge::all($params, $opts);

            $this->stripeLogger->requestSuccess($this->stripeHTTPClient);

            return $charges;
        } catch (\Throwable $exception) {
            $this->stripeLogger->requestException($this->stripeHTTPClient, $exception);

            throw $exception;
        }
    }

    /** @param array|string|null $options */
    public function createCharge(array $params = null, $options = null): Charge
    {
        try {
            $charge = Charge::create($params, $options);

            $this->stripeLogger->requestSuccess($this->stripeHTTPClient);

            return $charge;
        } catch (\Throwable $exception) {
            $this->stripeLogger->requestException($this->stripeHTTPClient, $exception);

            throw $exception;
        }
    }

    /**
     * @param array|string|null $chargeId
     * @param array|string|null $opts
     */
    public function getCharge($chargeId, $opts = null): Charge
    {
        try {
            $charge = Charge::retrieve($chargeId, $opts);

            $this->stripeLogger->requestSuccess($this->stripeHTTPClient);

            return $charge;
        } catch (\Throwable $exception) {
            $this->stripeLogger->requestException($this->stripeHTTPClient, $exception);

            throw $exception;
        }
    }

    /** @param array|string|null $options */
    public function createCustomer(array $params = null, $options = null): Customer
    {
        try {
            $customer = Customer::create($params, $options);

            $this->stripeLogger->requestSuccess($this->stripeHTTPClient);

            return $customer;
        } catch (\Throwable $exception) {
            $this->stripeLogger->requestException($this->stripeHTTPClient, $exception);

            throw $exception;
        }
    }

    /** @param array|string|null $options */
    public function retrieveCustomer(string $customerId, $options = null): Customer
    {
        try {
            $customer = Customer::retrieve($customerId, $options);

            $this->stripeLogger->requestSuccess($this->stripeHTTPClient);

            return $customer;
        } catch (\Throwable $exception) {
            $this->stripeLogger->requestException($this->stripeHTTPClient, $exception);

            throw $exception;
        }
    }

    public function attachSourceToCustomer(string $customerId, string $sourceToken): ApiResource
    {
        try {
            $source = Customer::createSource($customerId, [
                'source' => $sourceToken
            ]);

            $this->stripeLogger->requestSuccess($this->stripeHTTPClient);

            return $source;
        } catch (\Throwable $exception) {
            $this->stripeLogger->requestException($this->stripeHTTPClient, $exception);

            throw $exception;
        }
    }

    public function retrieveSource(string $id, $options = null): Source
    {
        try {
            $source = Source::retrieve($id, $options);

            $this->stripeLogger->requestSuccess($this->stripeHTTPClient);

            return $source;
        } catch (\Throwable $exception) {
            $this->stripeLogger->requestException($this->stripeHTTPClient, $exception);

            throw $exception;
        }
    }

    /** @param array|string|null $options */
    public function createSession(array $params = null, $options = null): Session
    {
        try {
            $session = Session::create($params, $options);

            $this->stripeLogger->requestSuccess($this->stripeHTTPClient);

            return $session;
        } catch (\Throwable $exception) {
            $this->stripeLogger->requestException($this->stripeHTTPClient, $exception);

            throw $exception;
        }
    }

    /** @param array|string|null $options */
    public function retrieveSession(array $params, $options = null): Session
    {
        try {
            $session = Session::retrieve($params, $options);

            $this->stripeLogger->requestSuccess($this->stripeHTTPClient);

            return $session;
        } catch (\Throwable $exception) {
            $this->stripeLogger->requestException($this->stripeHTTPClient, $exception);

            throw $exception;
        }
    }

    /** @param array|string|null $options */
    public function createAccount(array $params = null, $options = null): Account
    {
        try {
            $account = Account::create($params, $options);

            $this->stripeLogger->requestSuccess($this->stripeHTTPClient);

            return $account;
        } catch (\Throwable $exception) {
            $this->stripeLogger->requestException($this->stripeHTTPClient, $exception);

            throw $exception;
        }
    }

    /** @param array|string|null $options */
    public function updateAccount(string $id, array $params = null, $options = null): Account
    {
        try {
            $account = Account::update($id, $params, $options);

            $this->stripeLogger->requestSuccess($this->stripeHTTPClient);

            return $account;
        } catch (\Throwable $exception) {
            $this->stripeLogger->requestException($this->stripeHTTPClient, $exception);

            throw $exception;
        }
    }

    public function createPerson(string $id, string $personToken = null): Person
    {
        try {
            $person = Account::createPerson($id, [
                'person_token' => $personToken,
            ]);

            $this->stripeLogger->requestSuccess($this->stripeHTTPClient);

            return $person;
        } catch (\Throwable $exception) {
            $this->stripeLogger->requestException($this->stripeHTTPClient, $exception);

            throw $exception;
        }
    }

    public function updatePerson(string $id, string $personId, string $personToken = null): Person
    {
        try {
            $person = Account::updatePerson($id, $personId, [
                'person_token' => $personToken,
            ]);

            $this->stripeLogger->requestSuccess($this->stripeHTTPClient);

            return $person;
        } catch (\Throwable $exception) {
            $this->stripeLogger->requestException($this->stripeHTTPClient, $exception);

            throw $exception;
        }
    }

    /**
     * @param array|string|null $id
     * @param array|string|null $opts
     */
    public function getAccount($id = null, $opts = null): Account
    {
        try {
            $account = Account::retrieve($id, $opts);

            $this->stripeLogger->requestSuccess($this->stripeHTTPClient);

            return $account;
        } catch (\Throwable $exception) {
            $this->stripeLogger->requestException($this->stripeHTTPClient, $exception);

            throw $exception;
        }
    }

    /** @param array|string|null $options */
    public function getPerson(string $id, string $personId, $options = null): Person
    {
        try {
            $person = Account::retrievePerson($id, $personId, $options);

            $this->stripeLogger->requestSuccess($this->stripeHTTPClient);

            return $person;
        } catch (\Throwable $exception) {
            $this->stripeLogger->requestException($this->stripeHTTPClient, $exception);

            throw $exception;
        }
    }

    /** @param array|string|null $options */
    public function createFile(array $params = null, $options = null): File
    {
        return File::create($params, $options);
    }

    /** @param array|string|null $options */
    public function createRefund(array $params = null, $options = null): Refund
    {
        try {
            $refund = Refund::create($params, $options);

            $this->stripeLogger->requestSuccess($this->stripeHTTPClient);

            return $refund;
        } catch (\Throwable $exception) {
            $this->stripeLogger->requestException($this->stripeHTTPClient, $exception);

            throw $exception;
        }
    }

    /** @param array|string|null $options */
    public function createTransfer(array $params = null, $options = null): Transfer
    {
        try {
            $transfer = Transfer::create($params, $options);

            $this->stripeLogger->requestSuccess($this->stripeHTTPClient);

            return $transfer;
        } catch (\Throwable $exception) {
            $this->stripeLogger->requestException($this->stripeHTTPClient, $exception);

            throw $exception;
        }
    }

    /** @param array|string|null $options */
    public function retrieveSetupIntent(string $setup_intent, $options = null): SetupIntent
    {
        try {
            $setupIntent = SetupIntent::retrieve($setup_intent, $options);

            $this->stripeLogger->requestSuccess($this->stripeHTTPClient);

            return $setupIntent;
        } catch (\Throwable $exception) {
            $this->stripeLogger->requestException($this->stripeHTTPClient, $exception);

            throw $exception;
        }
    }
}
