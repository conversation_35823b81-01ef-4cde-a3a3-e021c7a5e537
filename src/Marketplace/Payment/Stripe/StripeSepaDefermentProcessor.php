<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Stripe;

use Broadway\UuidGenerator\UuidGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Templating\EngineInterface;
use Wizacha\Marketplace\AdminCompany;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Order\Action\Confirm;
use Wizacha\Marketplace\Order\Action\MarkPaymentDefermentAsAuthorized;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Payment\PaymentProcessor;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\PaymentType;
use Wizacha\Marketplace\Payment\Processor\Stripe;
use Wizacha\Marketplace\Payment\Response\HtmlPaymentResponse;
use Wizacha\Marketplace\Payment\Response\NoPaymentResponse;
use Wizacha\Marketplace\Payment\Response\PaymentResponse;
use Wizacha\Marketplace\Payment\UserPaymentInfoService;
use Wizacha\Marketplace\User\UserRepository;
use Wizacha\Order;

class StripeSepaDefermentProcessor implements PaymentProcessor
{
    /** @var string */
    private $publicTokenStripe;

    /** @var RouterInterface */
    private $router;

    /** @var EngineInterface */
    private $templating;

    /** @var UserRepository */
    private $userRepository;

    /** @var AdminCompany */
    private $adminCompany;

    /** @var UserPaymentInfoService */
    private $userPaymentInfoService;

    /** @var MarkPaymentDefermentAsAuthorized */
    private $markPaymentDefermentAsAuthorized;

    /** @var Confirm */
    private $confirm;

    /** @var OrderService */
    private $orderService;

    /** @var  Stripe */
    private $stripe;

    private UuidGeneratorInterface $uuidGenerator;

    public function __construct(
        string $publicTokenStripe,
        RouterInterface $router,
        EngineInterface $templating,
        UserRepository $userRepository,
        AdminCompany $adminCompany,
        UserPaymentInfoService $userPaymentInfoService,
        MarkPaymentDefermentAsAuthorized $markPaymentDefermentAsAuthorized,
        Confirm $confirm,
        OrderService $orderService,
        Stripe $stripe,
        UuidGeneratorInterface $uuidGenerator
    ) {
        $this->publicTokenStripe = $publicTokenStripe;
        $this->router = $router;
        $this->templating = $templating;
        $this->userRepository = $userRepository;
        $this->adminCompany = $adminCompany;
        $this->userPaymentInfoService = $userPaymentInfoService;
        $this->markPaymentDefermentAsAuthorized = $markPaymentDefermentAsAuthorized;
        $this->confirm = $confirm;
        $this->orderService = $orderService;
        $this->stripe = $stripe;
        $this->uuidGenerator = $uuidGenerator;
    }

    public function startPayment(int $orderId, string $redirectUrl = null, string $cssUrl = null): PaymentResponse
    {
        if ($this->userPaymentInfoService->getStripeBankAccountToken((new Order($orderId))->getUserId()) !== null) {
            /**
             * Avancement du workflow
             */
            $orders = $this->orderService->getChildOrders($orderId);
            foreach ($orders as $order) {
                if ($this->markPaymentDefermentAsAuthorized->isAllowed($order)) {
                    $this->markPaymentDefermentAsAuthorized->execute($order);
                }

                if ($this->confirm->isAllowed($order)) {
                    $this->confirm->execute($order);
                }
            }

            return new NoPaymentResponse();
        }

        $callbackUrl = $this->router->generate(
            'payment_notification_stripe_sepa_init',
            [
                'payment_redirect_url' => $redirectUrl ? base64_encode($redirectUrl) : null,
                'utm_nooverride' => '1',
                'orderId' => $orderId,
            ],
            RouterInterface::ABSOLUTE_URL
        );

        // Génère le formulaire de paiement Stripe qui sera intégré côté front dans la page de checkout.
        $html = $this->templating->render('@App/frontend/checkout/stripe_sepa_minimal.html.twig', [
            'stripePublicKey' => $this->publicTokenStripe,
            'stripeApiVersion' => StripeConfig::API_VERSION,
            'callbackUrl' => $callbackUrl,
            'name' => $this->userRepository->get((new Order($orderId))->getUserId())->getFullName(),
            'adminName' => $this->adminCompany->getName(),
            'locale' => (string) GlobalState::contentLocale(),
            'idempotencyKey' => $this->uuidGenerator->generate(),
        ]);

        return new HtmlPaymentResponse($html);
    }

    public function getType(): PaymentType
    {
        return PaymentType::PAYMENT_DEFERMENT();
    }

    public function getName(): PaymentProcessorName
    {
        return PaymentProcessorName::STRIPE();
    }

    public function isConfigured(): bool
    {
        return $this->stripe->isConfigured();
    }
}
