<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Stripe;

use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Templating\EngineInterface;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Payment\PaymentProcessor;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\PaymentType;
use Wizacha\Marketplace\Payment\Processor\Stripe;
use Wizacha\Marketplace\Payment\RenewCardProcessorInterface;
use Wizacha\Marketplace\Payment\Response\HtmlPaymentResponse;
use Wizacha\Marketplace\Payment\Response\PaymentResponse;
use Wizacha\Marketplace\Payment\Response\RedirectPaymentResponse;
use Wizacha\Marketplace\User\User;

class StripeCardProcessor implements PaymentProcessor, RenewCardProcessorInterface
{
    /** @var string */
    private $publicTokenStripe;

    /** @var RouterInterface */
    private $router;

    /** @var EngineInterface */
    public $templating;

    /** @var Stripe */
    private $stripe;

    public function __construct(string $publicTokenStripe, RouterInterface $router, EngineInterface $templating, Stripe $stripe)
    {
        $this->publicTokenStripe = $publicTokenStripe;
        $this->router = $router;
        $this->templating = $templating;
        $this->stripe = $stripe;
    }

    public function startPayment(int $orderId, string $redirectUrl = null, string $cssUrl = null): PaymentResponse
    {
        // Creates the payment intent at checkout time
        $clientSecret = $this->stripe->createCardTransaction($orderId);

        $callbackUrl = $this->router->generate(
            'payment_notification_stripe_card',
            [
                'payment_redirect_url' => $redirectUrl ? base64_encode($redirectUrl) : null,
                'utm_nooverride' => '1',
                'orderId' => $orderId,
            ],
            RouterInterface::ABSOLUTE_URL
        );

        // Génère le formulaire de paiement Stripe qui sera intégré côté front dans la page de checkout.
        $html = $this->templating->render('@App/frontend/checkout/stripe_credit_card_minimal.html.twig', [
            'clientSecret' => $clientSecret,
            'stripePublicKey' => $this->publicTokenStripe,
            'callbackUrl' => $callbackUrl,
            'locale' => (string) GlobalState::contentLocale(),
        ]);

        return new HtmlPaymentResponse($html);
    }

    public function getType(): PaymentType
    {
        return PaymentType::CREDIT_CARD();
    }

    public function getName(): PaymentProcessorName
    {
        return PaymentProcessorName::STRIPE();
    }

    public function isConfigured(): bool
    {
        return $this->stripe->isConfigured();
    }

    protected function getToken(): string
    {
        return $this->publicTokenStripe;
    }

    protected function getRouter(): RouterInterface
    {
        return $this->router;
    }

    protected function getTemplating(): EngineInterface
    {
        return $this->templating;
    }

    protected function getStripe(): Stripe
    {
        return $this->stripe;
    }

    public function renewCreditCard(User $user, string $redirectUrl = null, string $cssUrl = null): RedirectPaymentResponse
    {
        $customerId = null;
        // looking for a customer_id
        foreach ($user->getCreditCards() as $creditCard) {
            if ($creditCard->getPspUserId() !== null) {
                $customerId = $creditCard->getPspUserId();
                break;
            }
        }

        $successCallbackUrl = $this->router->generate(
            'payment_card_stripe_update_callback',
            [
                'redirect_url' => $redirectUrl ? base64_encode($redirectUrl) : null,
                'customer_id' => $customerId
            ],
            RouterInterface::ABSOLUTE_URL
        );

        $checkoutSessionId = $this->stripe->createCheckoutSession($user->getUserId(), $successCallbackUrl, $customerId);

        $url = $this->router->generate(
            'payment_card_stripe_update',
            [
                'stripePublicKey' => $this->publicTokenStripe,
                'checkoutSessionId' => $checkoutSessionId
            ],
            RouterInterface::ABSOLUTE_URL
        );

        return new RedirectPaymentResponse($url);
    }
}
