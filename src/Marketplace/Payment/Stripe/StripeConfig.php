<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Stripe;

final class StripeConfig
{
    public const API_VERSION = '2020-03-02';

    /** @var string */
    private $origin;

    /** @var string */
    private $currencyCode;

    /** @var string */
    private $secretKey;

    public function __construct(string $secretKey, string $currencyCode, string $origin)
    {
        $this->origin = $origin;
        $this->secretKey = $secretKey;
        $this->currencyCode = $currencyCode;
    }

    public function getOrigin(): string
    {
        return $this->origin;
    }

    public function setOrigin(string $origin): self
    {
        $this->origin = $origin;

        return $this;
    }

    public function getCurrencyCode(): string
    {
        return $this->currencyCode;
    }

    public function setCurrencyCode(string $currencyCode): self
    {
        $this->currencyCode = $currencyCode;

        return $this;
    }

    public function getSecretKey(): string
    {
        return $this->secretKey;
    }

    public function setSecretKey(string $secretKey): self
    {
        $this->secretKey = $secretKey;

        return $this;
    }

    public function getStripeApiVersion(): string
    {
        return static::API_VERSION;
    }
}
