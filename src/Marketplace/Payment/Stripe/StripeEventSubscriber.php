<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Stripe;

use Psr\Log\LoggerInterface;
use Stripe\PaymentMethod;
use Wizacha\Marketplace\CreditCard\CreditCard;
use Wizacha\Marketplace\CreditCard\Service\CreditCardService;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Payment\AbstractProcessorEventSubscriber;
use Wizacha\Marketplace\Payment\Event\StripeFailedPaymentCallbackEvent;
use Wizacha\Marketplace\Payment\Event\StripePaymentCallbackEvent;
use Wizacha\Marketplace\Payment\Event\StripeSetupCardCallbackEvent;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\Processor\Stripe;
use Wizacha\Marketplace\Subscription\SubscriptionService;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\User\UserRepository;

class StripeEventSubscriber extends AbstractProcessorEventSubscriber
{
    private StripeApi $stripeApi;

    public function __construct(
        LoggerInterface $logger,
        Stripe $processor,
        SubscriptionService $subscriptionService,
        CreditCardService $creditCardService,
        OrderService $orderService,
        UserRepository $userRepository,
        TransactionService $transactionService,
        StripeApi $stripeApi
    ) {
        $this->stripeApi = $stripeApi;
        parent::__construct($logger, $processor, $subscriptionService, $creditCardService, $orderService, $userRepository, $transactionService);
    }

    public function getName(): PaymentProcessorName
    {
        return PaymentProcessorName::STRIPE();
    }

    /** @return array [(string) Event class name => (string) self method name] */
    public static function getSubscribedEvents(): array
    {
        return [
            StripePaymentCallbackEvent::class => [
                ['updateTransactionStatus', 24],
                ['addCreditCardToSubscription', 16],
                ['updateSubscriptionStatus', 8],
            ],
            StripeSetupCardCallbackEvent::class => [
                ['updateCreditCard', 0]
            ],
            StripeFailedPaymentCallbackEvent::class => [
                ['updateSubscriptionStatus', 0]
            ]
        ];
    }

    public function createCardFromTransaction($data): ?CreditCard
    {
        $paymentMethod = $this->stripeApi->getPaymentMethod($data->payment_method);

        return $this->populateCreditCardWithPaymentMethod(new CreditCard(), $paymentMethod);
    }

    public function updateCreditCard(StripeSetupCardCallbackEvent $event): ?CreditCard
    {
        // expand option avoid multiple successive calls to stripe API
        $stripeCheckoutSession = $this->stripeApi->retrieveSession(
            [
                'id' => $event->getSessionId(),
                [
                    'expand' =>
                    [
                        'setup_intent',
                        'setup_intent.payment_method',
                    ]
                ]
            ]
        );

        try {
            $user = $this->userRepository->get($stripeCheckoutSession->metadata->userId);
        } catch (NotFound $e) {
            $this->logger->warning(
                "CreditCard not updated because user doesn't exist",
                [
                    'userId' => $stripeCheckoutSession->metadata->userId
                ]
            );
            return null;
        }

        if (0 === \count($this->creditCardService->get($user))) {
            $this->logger->warning(
                "User doesn't have any card to update",
                [
                    'userId' => $user->getUserId()
                ]
            );
            return null;
        }

        $existingCreditCard = $this->creditCardService->findOneByCardInfo(
            [
                'pan' => '**** **** **** ' . $stripeCheckoutSession->setup_intent->payment_method->card->last4,
                'expiryMonth' => (string) $stripeCheckoutSession->setup_intent->payment_method->card->exp_month,
                'expiryYear' => (string) $stripeCheckoutSession->setup_intent->payment_method->card->exp_year,
                'user' => $user,
            ]
        );

        if (null !== $existingCreditCard) {
            $this->logger->info(
                'Reuse same credit card saved for another subscription, but update paymentMethod token.',
                [
                    'userId' => $user->getUserId()
                ]
            );
            $existingCreditCard->setToken($stripeCheckoutSession->setup_intent->payment_method->id);
            $this->subscriptionService->replaceCreditCard($existingCreditCard, $user->getUserId());
            $creditCard = $existingCreditCard;
        } else {
            $creditCard = $this->populateCreditCardWithPaymentMethod(
                new CreditCard(),
                $stripeCheckoutSession->setup_intent->payment_method
            )->setUser($user);

            $this->subscriptionService->replaceCreditCard($creditCard, $user->getUserId());
        }

        return $creditCard;
    }

    private function populateCreditCardWithPaymentMethod(CreditCard $creditCard, PaymentMethod $paymentMethod): CreditCard
    {
        return $creditCard
            ->setToken($paymentMethod->id)
            ->setPan('**** **** **** ' . $paymentMethod->card->last4)
            ->setCardExpiryMonth((string) $paymentMethod->card->exp_month)
            ->setCardExpiryYear((string) $paymentMethod->card->exp_year)
            ->setBrand($paymentMethod->card->brand)
            ->setCountry($paymentMethod->card->country)
            ->setPspUserId($paymentMethod->customer ?? null)
        ;
    }

    public function setStripeApi(StripeApi $stripeApi): void
    {
        $this->stripeApi = $stripeApi;
    }
}
