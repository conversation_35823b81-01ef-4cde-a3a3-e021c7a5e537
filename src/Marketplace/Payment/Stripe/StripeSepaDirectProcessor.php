<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Stripe;

use Broadway\UuidGenerator\UuidGeneratorInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Templating\EngineInterface;
use Wizacha\Marketplace\AdminCompany;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Order\Action\CommitTo;
use Wizacha\Marketplace\Order\Action\Confirm;
use Wizacha\Marketplace\Order\Action\MarkPaymentDefermentAsAuthorized;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\Payment\PaymentProcessor;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\PaymentType;
use Wizacha\Marketplace\Payment\Processor\Stripe;
use Wizacha\Marketplace\Payment\Response\HtmlPaymentResponse;
use Wizacha\Marketplace\Payment\Response\NoPaymentResponse;
use Wizacha\Marketplace\Payment\Response\PaymentResponse;
use Wizacha\Marketplace\Payment\UserPaymentInfoService;
use Wizacha\Marketplace\User\UserRepository;
use Wizacha\Order;

class StripeSepaDirectProcessor implements PaymentProcessor
{
    public const DUE_DATE = "+1 minute";

    /**
     * @var string
     */
    private $publicTokenStripe;
    /**
     * @var RouterInterface
     */
    private $router;
    /**
     * @var EngineInterface
     */
    private $templating;
    /**
     * @var UserRepository
     */
    private $userRepository;
    /**
     * @var AdminCompany
     */
    private $adminCompany;
    /**
     * @var UserPaymentInfoService
     */
    private $userPaymentInfoService;
    /**
     * @var MarkPaymentDefermentAsAuthorized
     */
    private $markPaymentDefermentAsAuthorized;
    /**
     * @var Confirm
     */
    private $confirm;
    /**
     * @var OrderService
     */
    private $orderService;

    /** @var CommitTo */
    protected $commitTo;

    /** @var Stripe */
    protected $stripe;

    /** @var LoggerInterface */
    protected $logger;

    private UuidGeneratorInterface $uuidGenerator;

    public function __construct(
        string $publicTokenStripe,
        RouterInterface $router,
        EngineInterface $templating,
        UserRepository $userRepository,
        AdminCompany $adminCompany,
        UserPaymentInfoService $userPaymentInfoService,
        MarkPaymentDefermentAsAuthorized $markPaymentDefermentAsAuthorized,
        Confirm $confirm,
        OrderService $orderService,
        CommitTo $commitTo,
        Stripe $stripe,
        LoggerInterface $logger,
        UuidGeneratorInterface $uuidGenerator
    ) {
        $this->publicTokenStripe = $publicTokenStripe;
        $this->router = $router;
        $this->templating = $templating;
        $this->userRepository = $userRepository;
        $this->adminCompany = $adminCompany;
        $this->userPaymentInfoService = $userPaymentInfoService;
        $this->markPaymentDefermentAsAuthorized = $markPaymentDefermentAsAuthorized;
        $this->confirm = $confirm;
        $this->orderService = $orderService;
        $this->commitTo = $commitTo;
        $this->stripe = $stripe;
        $this->logger = $logger;
        $this->uuidGenerator = $uuidGenerator;
    }

    public function startPayment(int $orderId, string $redirectUrl = null, string $cssUrl = null): PaymentResponse
    {
        // Dans le cas où l'utilisateur à déjà signé un mandat de prélèvement chez Stripe
        if ($this->userPaymentInfoService->getStripeBankAccountToken((new Order($orderId))->getUserId()) !== null) {
            /**
             * Avancement du workflow
             */
            $orders = $this->orderService->getChildOrders($orderId);
            foreach ($orders as $order) {
                if ($this->markPaymentDefermentAsAuthorized->isAllowed($order)) {
                    $this->markPaymentDefermentAsAuthorized->execute($order);
                }

                if ($this->confirm->isAllowed($order)) {
                    $this->confirm->execute($order);
                }

                if ($this->commitTo->isAllowed($order)) {
                    $this->commitTo->execute(
                        $order,
                        new \DateTimeImmutable(static::DUE_DATE),
                        static::generateCommitmentNumber()
                    );

                    fn_change_order_status($order->getId(), (string) $order->deduceLegacyStatus(), (string) OrderStatus::INCOMPLETED());
                }
            }

            try {
                $this->stripe->createSepaTransaction($orderId);
            } catch (\Throwable $e) {
                $this->logger->error('[Stripe SEPA Direct] Exception in SEPA start payment: ' . $e->getMessage(), [
                    'exception' => $e,
                    'data' => [
                        'orderId' => $orderId,
                    ],
                ]);
            }

            return new NoPaymentResponse();
        }

        $callbackUrl = $this->router->generate(
            'payment_notification_stripe_sepa_init',
            [
                'payment_redirect_url' => $redirectUrl ? base64_encode($redirectUrl) : null,
                'utm_nooverride' => '1',
                'orderId' => $orderId,
            ],
            RouterInterface::ABSOLUTE_URL
        );

        // Génère le formulaire de paiement Stripe qui sera intégré côté front dans la page de checkout.
        $html = $this->templating->render('@App/frontend/checkout/stripe_sepa_minimal.html.twig', [
            'stripePublicKey' => $this->publicTokenStripe,
            'stripeApiVersion' => StripeConfig::API_VERSION,
            'callbackUrl' => $callbackUrl,
            'name' => $this->userRepository->get((new Order($orderId))->getUserId())->getFullName(),
            'adminName' => $this->adminCompany->getName(),
            'locale' => (string) GlobalState::contentLocale(),
            'idempotencyKey' => $this->uuidGenerator->generate(),
        ]);

        return new HtmlPaymentResponse($html);
    }

    public function getType(): PaymentType
    {
        return PaymentType::SEPA_DIRECT();
    }

    public function getName(): PaymentProcessorName
    {
        return PaymentProcessorName::STRIPE();
    }

    public static function generateCommitmentNumber(): string
    {
        return uniqid('commitment_');
    }

    public function isConfigured(): bool
    {
        return $this->stripe->isConfigured();
    }
}
