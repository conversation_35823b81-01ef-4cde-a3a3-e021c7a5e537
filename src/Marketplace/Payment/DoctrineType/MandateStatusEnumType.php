<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\DoctrineType;

use Acelaya\Doctrine\Type\AbstractPhpEnumType;
use Wizacha\Marketplace\Payment\MandateStatus;

/**
 * Doctrine Type for MandateStatus
 */
class MandateStatusEnumType extends AbstractPhpEnumType
{
    protected $enumType = MandateStatus::class;

    protected function getSpecificName(): string
    {
        return 'mandate_status';
    }
}
