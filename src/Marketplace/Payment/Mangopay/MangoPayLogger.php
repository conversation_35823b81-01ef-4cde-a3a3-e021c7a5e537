<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Mangopay;

use MangoPay\Libraries\HttpCurl;
use Wizacha\Marketplace\Payment\AbstractPaymentLogger;
use Wizacha\Marketplace\Payment\Processor\MangoPay;

/**
 * This logger is used in MangoPay for every API call excepts KYC/UBO.
 *
 * @see MangoPay
 */
class MangoPayLogger extends AbstractPaymentLogger
{
    private const MONGOPAY_LOG_NAME = 'MangoPay API SDK';

    /**
     * This function need the MangoPayHTTPClient to get the latest request info
     * @param MangoPayHTTPClient $mangoPayHTTPClient
     */
    public function requestSuccess(HttpCurl $mangoPayHTTPClient, array $context = []): void
    {
        $restTool = $mangoPayHTTPClient->getLatestRequestRestTool();
        $response = $mangoPayHTTPClient->getLatestResponse();

        /**
         * For PHPStorm: deactivate alert on RequestType
         * @var string $requestType
         */
        $requestType = $restTool->GetRequestType();

        $this->logRequestSuccess(
            self::MONGOPAY_LOG_NAME . ' Request Success',
            $requestType,
            $restTool->GetRequestUrl(),
            $restTool->GetRequestData(),
            $response->ResponseCode,
            $response->Body,
            $context
        );
    }

    /**
     * This function need the MangoPayHTTPClient to get the latest request info
     * @param MangoPayHTTPClient $mangoPayHTTPClient
     */
    public function requestException(
        HttpCurl $mangoPayHTTPClient,
        \Throwable $exception,
        array $context = []
    ): void {
        $restTool = $mangoPayHTTPClient->getLatestRequestRestTool();

        /**
         * For PHPStorm: deactivate alert on RequestType
         * @var string $requestType
         */
        $requestType = $restTool->GetRequestType();

        $this->logRequestException(
            self::MONGOPAY_LOG_NAME . ' Error',
            $requestType,
            $restTool->GetRequestUrl(),
            $restTool->GetRequestData(),
            $exception,
            $context,
        );
    }

    /**
     * This function need the MangoPayHTTPClient to get the latest request info
     * @param MangoPayHTTPClient $mangoPayHTTPClient
     */
    public function requestWarning(
        string $reason,
        HttpCurl $mangoPayHTTPClient,
        \Throwable $exception,
        array $context = []
    ): void {
        $restTool = $mangoPayHTTPClient->getLatestRequestRestTool();

        /**
         * For PHPStorm: deactivate alert on RequestType
         * @var string $requestType
         */
        $requestType = $restTool->GetRequestType();

        $this->logRequestException(
            self::MONGOPAY_LOG_NAME . ' Warning',
            $requestType,
            $restTool->GetRequestUrl(),
            $restTool->GetRequestData(),
            $exception,
            array_merge($context, ['reason' => $reason]),
            self::WARNING_LEVEL
        );
    }
}
