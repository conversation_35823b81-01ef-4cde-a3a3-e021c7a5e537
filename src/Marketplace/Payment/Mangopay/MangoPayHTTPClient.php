<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Mangopay;

use MangoPay\Libraries\HttpCurl;
use MangoPay\Libraries\HttpResponse;
use MangoPay\Libraries\RestTool;

/**
 * This class is used as a wrapper for the mangopay http client.
 * We want to log the requests sent to the MangoPay API,
 * therefore we need to get the necessary request and response info from the MangoPay objects
 *
 * Then, when we want to log a request, we need to get this client to pass it to the log function
 * @see MangoPayLogger
 */
class MangoPayHTTPClient extends HttpCurl
{
    private RestTool $latestRequestRestTool;
    private HttpResponse $latestResponse;

    /**
     * This is a wrapper around the MangoPay HttpCurl Request function
     * We get the request info and call the parent Request function
     */
    public function Request(RestTool $restTool): HttpResponse
    {
        // Here we keep the latest RestTool object.
        // We can't use it yet because its values (ex: request body) will be built in the parent Request function.
        $this->latestRequestRestTool = $restTool;

        // Call the parent Request function with our latestRequestRestTool as argument:
        // Each modification of this RestTool in the parent Request will be accessible from here
        // And keep the request response as well
        $this->latestResponse = $this->parentRequest($this->latestRequestRestTool);

        return $this->latestResponse;
    }

    /**
     * This function is only necessary for a test purpose
     */
    protected function parentRequest(RestTool $restTool): HttpResponse
    {
        return parent::Request($restTool);
    }

    /**
     * Use this getter to access the request info to log
     */
    public function getLatestRequestRestTool(): ?RestTool
    {
        return $this->latestRequestRestTool;
    }

    /**
     * Use this getter to access the response info to log
     */
    public function getLatestResponse(): ?HttpResponse
    {
        return $this->latestResponse;
    }
}
