<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Payment\Mangopay;

use Wizacha\Marketplace\CreditCard\CreditCard;
use Wizacha\Marketplace\Payment\AbstractProcessorEventSubscriber;
use Wizacha\Marketplace\Payment\Event\MangopayPaymentCallbackEvent;
use Wizacha\Marketplace\Payment\PaymentProcessorName;

class MangopayEventSubscriber extends AbstractProcessorEventSubscriber
{
    public function getName(): PaymentProcessorName
    {
        return PaymentProcessorName::MANGOPAY();
    }

    /** @return array [(string) Event class name => (string) self method name] */
    public static function getSubscribedEvents(): array
    {
        return [
            MangopayPaymentCallbackEvent::class => ['updateTransactionStatus', 0],
        ];
    }

    public function createCardFromTransaction($data): ?CreditCard
    {
        return null;
    }
}
