<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Mangopay;

class MangoPayApiConfig
{
    /** @var string */
    protected $clientId = '';

    /** @var string */
    protected $clientPassword = '';

    /** @var string */
    protected $baseUrl = '';

    /** @var string */
    protected $secureMode = 'FORCE';

    /** @var string */
    protected $currencyCode = 'EUR';

    /** @var string */
    protected $temporaryFolder;

    public function __construct(
        string $clientId,
        string $clientPassword,
        string $baseUrl,
        string $secureMode,
        string $currencyCode
    ) {
        $this->clientId = $clientId;
        $this->clientPassword = $clientPassword;
        $this->baseUrl = $baseUrl;
        $this->secureMode = $secureMode;
        $this->currencyCode = $currencyCode;
        $this->temporaryFolder = \sys_get_temp_dir();
    }

    public function getClientId(): string
    {
        return $this->clientId;
    }

    public function getClientPassword(): string
    {
        return $this->clientPassword;
    }

    public function getTemporaryFolder(): string
    {
        return $this->temporaryFolder;
    }

    public function getBaseUrl(): string
    {
        return $this->baseUrl;
    }

    public function getSecureMode(): string
    {
        return $this->secureMode;
    }

    public function getCurrencyCode(): string
    {
        return $this->currencyCode;
    }

    public function isConfigured(): bool
    {
        return '' !== $this->clientId && '' !== $this->clientPassword && '' !== $this->baseUrl;
    }
}
