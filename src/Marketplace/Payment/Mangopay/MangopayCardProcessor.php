<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Mangopay;

use Wizacha\Marketplace\Payment\PaymentProcessor;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\PaymentType;
use Wizacha\Marketplace\Payment\Processor\MangoPay;
use Wizacha\Marketplace\Payment\Response\PaymentResponse;
use Wizacha\Marketplace\Payment\Response\RedirectPaymentResponse;

class MangopayCardProcessor implements PaymentProcessor
{
    /**
     * @var MangoPay
     */
    private $mangopay;

    public function __construct(MangoPay $mangopay)
    {
        $this->mangopay = $mangopay;
    }

    public function startPayment(int $orderId, string $redirectUrl = null, string $cssUrl = null): PaymentResponse
    {
        $url = $this->mangopay->createCardTransaction($orderId, $redirectUrl);

        return new RedirectPaymentResponse($url);
    }

    public function getType(): PaymentType
    {
        return PaymentType::CREDIT_CARD();
    }

    public function getName(): PaymentProcessorName
    {
        return PaymentProcessorName::MANGOPAY();
    }

    public function isConfigured(): bool
    {
        return $this->mangopay->isConfigured();
    }
}
