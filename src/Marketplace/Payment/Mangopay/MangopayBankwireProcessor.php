<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Mangopay;

use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Templating\EngineInterface;
use Wizacha\Marketplace\Order\Action\Confirm;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Payment\Event\BankwirePaymentEvent;
use Wizacha\Marketplace\Payment\PaymentProcessor;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\PaymentType;
use Wizacha\Marketplace\Payment\Processor\MangoPay;
use Wizacha\Marketplace\Payment\Response\HtmlPaymentResponse;
use Wizacha\Marketplace\Payment\Response\PaymentResponse;
use Wizacha\Order;

class MangopayBankwireProcessor implements PaymentProcessor
{
    /**
     * @var MangoPay
     */
    private $mangopay;

    /**
     * @var EngineInterface
     */
    private $templating;

    /**
     * @var OrderService
     */
    private $orderService;

    /**
     * @var Confirm
     */
    private $confirm;

    private EventDispatcherInterface $eventDispatcher;

    public function __construct(
        MangoPay $mangopay,
        EngineInterface $templating,
        Confirm $confirm,
        OrderService $orderService,
        EventDispatcherInterface $eventDispatcher
    ) {
        $this->mangopay = $mangopay;
        $this->templating = $templating;
        $this->confirm = $confirm;
        $this->orderService = $orderService;
        $this->eventDispatcher = $eventDispatcher;
    }

    public function startPayment(int $orderId, string $redirectUrl = null, string $cssUrl = null): PaymentResponse
    {
        $transferInfo = $this->mangopay->createBankwireTransaction($orderId, $redirectUrl);

        $this->eventDispatcher->dispatch(new BankwirePaymentEvent(new Order($orderId), $transferInfo));

        $html = $this->templating->render('@App/frontend/checkout/mangopay_bankwire_minimal.html.twig', $transferInfo);

        $orders = $this->orderService->getChildOrders($orderId);
        foreach ($orders as $order) {
            if ($this->confirm->isAllowed($order)) {
                $this->confirm->execute($order);
            }
        }

        return new HtmlPaymentResponse($html);
    }

    public function getType(): PaymentType
    {
        return PaymentType::BANK_TRANSFER();
    }

    public function getName(): PaymentProcessorName
    {
        return PaymentProcessorName::MANGOPAY();
    }

    public function isConfigured(): bool
    {
        return $this->mangopay->isConfigured();
    }
}
