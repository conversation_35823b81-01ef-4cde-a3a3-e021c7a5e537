<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment;

use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Wizacha\Marketplace\CreditCard\CreditCard;
use Wizacha\Marketplace\CreditCard\Service\CreditCardService;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Payment\Event\PaymentCallbackEvent;
use Wizacha\Marketplace\Payment\Processor\SMoney;
use Wizacha\Marketplace\Payment\Processor\Stripe;
use Wizacha\Marketplace\Payment\Processor\TransactionProcessorInterface;
use Wizacha\Marketplace\Subscription\SubscriptionService;
use Wizacha\Marketplace\Subscription\SubscriptionStatus;
use Wizacha\Marketplace\Transaction\Exception\TransactionNotFound;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionType;
use Wizacha\Marketplace\Transaction\TransactionStatus;
use Wizacha\Marketplace\User\UserRepository;

abstract class AbstractProcessorEventSubscriber implements EventSubscriberInterface
{
    protected LoggerInterface $logger;
    protected TransactionProcessorInterface $processor;
    protected TransactionService $transactionService;
    protected SubscriptionService $subscriptionService;
    protected CreditCardService $creditCardService;
    protected OrderService $orderService;
    protected UserRepository $userRepository;

    public function __construct(
        LoggerInterface $logger,
        TransactionProcessorInterface $processor,
        SubscriptionService $subscriptionService,
        CreditCardService $creditCardService,
        OrderService $orderService,
        UserRepository $userRepository,
        TransactionService $transactionService
    ) {
        $this->logger = $logger;
        $this->processor = $processor;
        $this->transactionService = $transactionService;
        $this->subscriptionService = $subscriptionService;
        $this->creditCardService = $creditCardService;
        $this->orderService = $orderService;
        $this->userRepository = $userRepository;
    }

    abstract public function getName(): PaymentProcessorName;

    abstract public function createCardFromTransaction($data): ?CreditCard;

    public function updateTransactionStatus(PaymentCallbackEvent $event): self
    {
        if ($event->getProcessorName()->equals($this->getName()) === false) {
            return $this;
        }
        $orders = $event->getOrder()->isParentOrder() ? $event->getOrder()->getSubOrders() : [$event->getOrder()];

        /**
         * Specific case for Smoney where the reference is orderId
         * Todo: Rewrite smoney implementation
         */
        if ($this->processor instanceof SMoney) {
            $processorTransaction = $this->processor->getTransactionDetails((string) $event->getOrder()->getId());
        } else {
            $processorTransaction = $this->processor->getTransactionDetails($event->getReference());
        }

        foreach ($orders as $order) {
            try {
                if ($this->processor instanceof Stripe) {
                    $transaction = $this->transactionService->retrieveTransaction($this->getName(), $event->getTransactionType(), $order->getId(), $event->getReference());
                } else {
                    $transaction = $this->transactionService->retrieveTransaction($this->getName(), $event->getTransactionType(), $order->getId());
                }
                $this->transactionService->save(
                    $transaction
                        ->setTransactionReference((string) $processorTransaction->getId())
                        ->setStatus($this->processor->getHumanStatus($processorTransaction->getStatus()))
                );
            } catch (TransactionNotFound $e) {
                $error = $this->getName()->getValue() . ': could not find the transaction linked to ';
                $this->logger->error($error . ' order #' . $order->getId() . ' - transaction #' . $event->getReference());

                continue;
            }
        }

        return $this;
    }

    public function addCreditCardToSubscription(PaymentCallbackEvent $event): self
    {
        // Pre-conditions
        if ($this->isSubscription($event) === true) {
            return $this;
        }

        $userId = $event->getOrder()->getUserId();
        $orders = $this->orderService->getChildOrders($event->getOrder()->getId());
        $transaction = $this->processor->getTransactionDetails($event->getReference());

        if (true === $this->processor->getHumanStatus($transaction->getStatus())->equals(TransactionStatus::FAILED())
        ) {
            return $this;
        }

        $data = $transaction->getRawData();

        $creditCard = $this->createCardFromTransaction($data);

        if ($creditCard instanceof CreditCard === true) {
            $creditCard = $this->creditCardService->save(
                $creditCard->setUser($this->userRepository->get($userId))
            );
            $this->subscriptionService->addCreditCard($orders, $creditCard);
        }

        return $this;
    }

    public function updateSubscriptionStatus(PaymentCallbackEvent $event): self
    {
        // Pre-conditions
        if ($this->isSubscription($event) === true) {
            return $this;
        }

        $transaction = $this->processor->getTransactionDetails($event->getReference());

        if ($event->getTransactionType()->getValue() === TransactionType::REFUND()->getValue()) {
            return $this;
        }

        $orders = $this->orderService->getChildOrders($event->getOrder()->getId());

        $status = SubscriptionStatus::ACTIVE();
        if (true === $this->processor->isTransactionDetailsFailed($transaction)) {
            $status = $this->subscriptionService->updateUnpaidSubscription($orders);
        }

        $firstOrders = $this->subscriptionService->getFirstOrders($orders);

        foreach ($firstOrders as $firstOrder) {
            $subscriptions = $this->subscriptionService->getByFirstOrderId($firstOrder->getId());
            foreach ($subscriptions as $subscription) {
                $this->subscriptionService->updateAutoSubscriptionStatusFromPsp($subscription, $status);
            }
        }

        return $this;
    }

    private function isSubscription(PaymentCallbackEvent $event): bool
    {
        return false === $event->getProcessorName()->equals($this->getName())
            || false === $this->subscriptionService->hasFeatureFlag()
            || false === $event->getOrder()->getOrderV2()->isSubscription();
    }
}
