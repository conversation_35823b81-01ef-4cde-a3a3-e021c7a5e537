<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment;

use MyCLabs\Enum\Enum;

/**
 * @method static PayoutFrequency DAILY()
 * @method static PayoutFrequency WEEKLY()
 * @method static PayoutFrequency BIMONTHLY()
 * @method static PayoutFrequency MONTHLY()
 * @method static PayoutFrequency MANUAL()
 */
class PayoutFrequency extends Enum
{
    protected const DAILY = 'daily';
    protected const WEEKLY = 'weekly';
    protected const BIMONTHLY = 'bimonthly';
    protected const MONTHLY = 'monthly';
    protected const MANUAL = 'manual';
}
