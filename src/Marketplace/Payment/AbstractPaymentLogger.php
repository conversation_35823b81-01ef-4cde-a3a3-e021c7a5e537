<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment;

use Psr\Log\LoggerInterface;
use <PERSON>ymfony\Component\Serializer\Normalizer\NormalizerInterface;
use Wizacha\Bridge\Monolog\EventLogger\EventDurationTrait;
use Wizacha\Marketplace\Security\DataAnonymizer\DataAnonymizer;

abstract class AbstractPaymentLogger
{
    use EventDurationTrait;

    public const INFO_LEVEL = 'info';
    public const ERROR_LEVEL = 'error';
    public const WARNING_LEVEL = 'warning';
    public const ALERT_LEVEL = 'alert';

    protected DataAnonymizer $dataAnonymizer;
    private LoggerInterface $logger;
    private NormalizerInterface $normalizer;

    public function __construct(LoggerInterface $logger, NormalizerInterface $normalizer)
    {
        $this->logger = $logger;
        $this->normalizer = $normalizer;
    }

    public function getLogger(): LoggerInterface
    {
        return $this->logger;
    }

    public function setDataAnonymizer(DataAnonymizer $dataAnonymizer): AbstractPaymentLogger
    {
        $this->dataAnonymizer = $dataAnonymizer;

        return $this;
    }

    public function start(): void
    {
        $this->startEvent();
    }

    /**
     * @param null|string|array $requestParams
     * @param int|string $responseCode
     * @param string|array $responseBody
     * @param object|array $context
     */
    public function logRequestSuccess(
        string $message,
        ?string $method,
        ?string $endpoint,
        $requestParams,
        $responseCode,
        $responseBody,
        $context = []
    ): void {
        $extraData = $this->getRequestExtraData(
            $method,
            $endpoint,
            $requestParams,
            $responseCode,
            $responseBody,
            $context
        );
        $extraData = $this->addEndEventData($extraData, true);

        $this->logger->info($message, ['extra' => $extraData]);
    }

    /**
     * @param null|string|array $requestParams
     * @param int|string $responseCode
     * @param string|array $responseBody
     * @param object|array $context
     */
    public function logRequestError(
        string $message,
        ?string $method,
        ?string $endpoint,
        $requestParams,
        $responseCode,
        $responseBody,
        $context = []
    ) {
        $extraData = $this->getRequestExtraData(
            $method,
            $endpoint,
            $requestParams,
            $responseCode,
            $responseBody,
            $context
        );
        $extraData = $this->addEndEventData($extraData, true);

        $this->logger->error($message, ['extra' => $extraData]);
    }

    /**
     * @param null|string|array $requestParams
     * @param object|array $context
     * @param string $level Can be either "error" or "warning". Defaults to "error".
     */
    public function logRequestException(
        string $message,
        ?string $method,
        ?string $endpoint,
        $requestParams,
        \Throwable $exception,
        $context = [],
        string $level = self::ERROR_LEVEL
    ): void {
        $extraData = $this->initExtraData($method, $endpoint, $requestParams, $context);

        $extraData = $this->addEndEventData($extraData, false);

        $this->logger->log(
            $level === self::WARNING_LEVEL ? $level : self::ERROR_LEVEL,
            $message,
            ['extra' => $extraData, 'exception' => $exception]
        );
    }

    /**
     * @param object|array $context
     */
    public function log(string $level, string $message, $context = []): void
    {
        $this->logger->log($level, $message, $this->normalizeAndAnonymizeContext($context));
    }

    /**
     * @param null|string|array $requestParams
     * @param object|array $context
     */
    private function initExtraData(?string $method, ?string $endpoint, $requestParams, $context): array
    {
        $extraData = [];

        if (\is_null($endpoint) === false) {
            if (str_starts_with($endpoint, 'https')) {
                $extraData['url.full'] = $endpoint;
            } else {
                $extraData['url.path'] = $endpoint;
            }
        } else {
            $extraData['url.original'] = $endpoint;
        }

        $extraData['http.request.method'] = $method;

        if (\is_string($requestParams) === true && $requestParams !== '') {
            $requestParams = \json_decode($requestParams, true);
            if (null === $requestParams) {
                $requestParams = 'Error decoding JSON request params: ' . json_last_error_msg();
            }
        }

        if (\is_array($requestParams) === true) {
            $requestParams = $this->dataAnonymizer->findAndAnonymizeData($requestParams);
        }

        $extraData['http.request.body.content'] = $requestParams;

        return array_merge($extraData, $this->normalizeAndAnonymizeContext($context));
    }

    private function addEndEventData(array $extraData, bool $isSuccess): array
    {
        return array_merge($extraData, $this->endEvent($isSuccess));
    }

    /**
     * @param null|string|array $requestParams
     * @param int|string $responseCode
     * @param string|array $responseBody
     * @param object|array $context
     */
    private function getRequestExtraData(
        ?string $method,
        ?string $endpoint,
        $requestParams,
        $responseCode,
        $responseBody,
        $context
    ): array {
        $extraData = $this->initExtraData($method, $endpoint, $requestParams, $context);

        if (\is_string($responseBody) === true && $responseBody !== '') {
            $responseBody = \json_decode($responseBody, true);
            if (null === $responseBody) {
                $responseBody = 'Error decoding JSON response: ' . \json_last_error_msg();
            }
        }

        if (\is_array($responseBody) === true) {
            $responseBody = $this->dataAnonymizer->findAndAnonymizeData($responseBody);
        }

        return array_merge($extraData, [
            'http.response.code' => (int) $responseCode,
            'http.response.body.content' => $responseBody,
        ]);
    }

    /**
     * @param object|array $context
     */
    private function normalizeAndAnonymizeContext($context): array
    {
        $context = $this->normalizer->normalize($context);
        if (\count($context) > 0) {
            $context = $this->dataAnonymizer->findAndAnonymizeContext($context);
        }

        return $context;
    }
}
