<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\LemonWay;

use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Wizacha\AppBundle\Notification\DTO\InternalTransferErrorDTO;
use Wizacha\AppBundle\Notification\OrderNotifier;
use Wizacha\Company as CompanyStatus;
use Wizacha\Events\IterableEvent;
use Wizacha\Marketplace\Commission\CommissionService;
use Wizacha\Marketplace\Company\CompanyEvents;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Company\Event\CompanyLegalDocumentsUpdated;
use Wizacha\Marketplace\CreditCard\CreditCard;
use Wizacha\Marketplace\CreditCard\Service\CreditCardService;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Order\Action\DispatchFundsSucceeded;
use Wizacha\Marketplace\Order\Action\DispatchFundsFailed;
use Wizacha\Marketplace\Order\Action\MarkPaymentAsRefused;
use Wizacha\Marketplace\Order\Action\TransferMarketplaceDiscountFailed;
use Wizacha\Marketplace\Payment\Event\DispatchFundsFailedEvent;
use Wizacha\Marketplace\Order\Event\OrderStatusUpdated;
use Wizacha\Marketplace\Order\OrderEvents;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\Payment\AbstractProcessorEventSubscriber;
use Wizacha\Marketplace\Payment\Event\LemonwayPaymentCallbackEvent;
use Wizacha\Marketplace\Payment\Event\PaymentCallbackEvent;
use Wizacha\Marketplace\Payment\LemonWay\Exception\KycLevelTooHigh;
use Wizacha\Marketplace\Payment\LemonWay\Exception\LemonWayException;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\PaymentType;
use Wizacha\Marketplace\Payment\Processor\LemonWay;
use Wizacha\Marketplace\Order\Order as MarketplaceOrder;
use Wizacha\Marketplace\Subscription\SubscriptionService;
use Wizacha\Marketplace\Transaction\Transaction;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionStatus;
use Wizacha\Marketplace\Transaction\TransactionsTransferEventType;
use Wizacha\Marketplace\Transaction\TransactionTransferEvent;
use Wizacha\Marketplace\Transaction\TransactionType;
use Wizacha\Marketplace\User\UserRepository;
use Wizacha\Marketplace\User\UserType;
use Wizacha\Money\Money;
use Wizacha\Order;

final class LemonWayEventSubscriber extends AbstractProcessorEventSubscriber
{

    private CommissionService $commissionService;
    private CompanyService $companyService;
    private DispatchFundsSucceeded $dispatchFundsSucceededAction;
    private DispatchFundsFailed $dispatchFundsFailedAction;
    private MarkPaymentAsRefused $markPaymentAsRefused;
    private OrderNotifier $orderNotifier;
    private EventDispatcherInterface $eventDispatcher;

    public function __construct(
        LoggerInterface $logger,
        LemonWay $processor,
        TransactionService $transactionService,
        CommissionService $commissionService,
        UserRepository $userRepository,
        CompanyService $companyService,
        DispatchFundsFailed $dispatchFundsFailedAction,
        OrderService $orderService,
        MarkPaymentAsRefused $markPaymentAsRefused,
        OrderNotifier $orderNotifier,
        EventDispatcherInterface $eventDispatcher,
        DispatchFundsSucceeded $dispatchFundsSucceededAction,
        SubscriptionService $subscriptionService,
        CreditCardService $creditCardService
    ) {
        parent::__construct($logger, $processor, $subscriptionService, $creditCardService, $orderService, $userRepository, $transactionService);

        $this->commissionService = $commissionService;
        $this->companyService = $companyService;
        $this->dispatchFundsFailedAction = $dispatchFundsFailedAction;
        $this->markPaymentAsRefused = $markPaymentAsRefused;
        $this->orderNotifier = $orderNotifier;
        $this->eventDispatcher =  $eventDispatcher;
        $this->dispatchFundsSucceededAction = $dispatchFundsSucceededAction;
    }

    public function getName(): PaymentProcessorName
    {
        return PaymentProcessorName::LEMONWAY();
    }

    public static function getSubscribedEvents(): array
    {
        return [
            OrderEvents::UPDATED  => ['dispatchFunds', 0],
            CompanyEvents::APPROVED => ['createCompany', 0],
            CompanyEvents::UPDATED => ['updateCompany', 0],
            CompanyEvents::SEND_TO_PSP => ['createCompanyAndRegisterIban', 0],
            CompanyEvents::IBAN_BIC_UPDATED => ['registerIban', 0],
            CompanyEvents::LEGAL_DOCUMENTS_UPDATED => ['sendKyc', 0],
            LemonwayPaymentCallbackEvent::class => [
                ['transferFromBuyerToTechnical', 24],
                ['marketplaceDiscountProcess', 16],
                ['updateTransactionStatus', 8],
            ],
        ];
    }

    public function setUserRepository(UserRepository $userRepository): self
    {
        $this->userRepository = $userRepository;

        return $this;
    }

    private function isDispatchable(
        string $orderStatus,
        array $paymentsInformation,
        Money $orderTotalAmount,
        MarketplaceOrder $order
    ): bool {
        $isConfigured = true === $this->processor->isConfigured();
        $isCompleted = $orderStatus === OrderStatus::COMPLETED;
        $hasNoDispatch = (
            false === \array_key_exists(
                LemonWayConfig::LEMONWAY_DISPATCH_FUNDS_DONE,
                $paymentsInformation
            )
            || false === $paymentsInformation[LemonWayConfig::LEMONWAY_DISPATCH_FUNDS_DONE]
        );
        $hasPositiveAmount = $orderTotalAmount->isPositive();

        $hadPaymentTransaction = (
            true === \array_key_exists(LemonWayConfig::LEMONWAY_TRANSACTION_ID, $paymentsInformation)
            || true === \array_key_exists(LemonWayConfig::LEMONWAY_BANKWIRE, $paymentsInformation)
        );

        return (
            $isConfigured
            && $isCompleted
            && $hasNoDispatch
            && $hasPositiveAmount
            && ($hadPaymentTransaction || $this->canBeDispatchedAfterFailed($order) === true)
        );
    }

    public function dispatchFunds(OrderStatusUpdated $orderEvent): self
    {
        $order = new Order($orderEvent->getId());
        $paymentsInformation = $order->getPaymentInformation();

        $isDispatchable = $this->isDispatchable(
            $orderEvent->getStatusTo(),
            $paymentsInformation,
            $order->getTotal(),
            $order->getOrderV2()
        );

        if (false === $isDispatchable) {
            return $this;
        }

        $commission = $this->commissionService->getTotalCommission(
            $order->getOrderV2()
        );

        try {
            $this->processor->dispatchFunds($order, $commission);
        } catch (LemonWayException $exception) {
            $this->eventDispatcher->dispatch(
                new DispatchFundsFailedEvent($order, $commission),
                DispatchFundsFailedEvent::class
            );
            $order->setPaymentInformation(LemonWayConfig::LEMONWAY_DISPATCH_FUNDS_DONE, false);

            $orders = $this->orderService->getChildOrders($order->getId());
            foreach ($orders as $order) {
                $this->dispatchFundsFailedAction->execute($order);
            }

            $this->logger->error('[LemonWay] Error on dispatch order funds.', [
                'exception' => $exception,
                'data' => [
                    'message' => $exception->getMessage(),
                    'code' => $exception->getCode(),
                    'order' => $orderEvent->getId(),
                    'status' => [
                        'from' => $orderEvent->getStatusFrom(),
                        'to' => $orderEvent->getStatusTo(),
                    ],
                ],
            ]);

            return $this;
        }

        $order->setPaymentInformation(LemonWayConfig::LEMONWAY_DISPATCH_FUNDS_DONE, true);

        /** Avancement du workflow */

        $orders = $this->orderService->getChildOrders($order->getId());
        foreach ($orders as $order) {
            $this->dispatchFundsSucceededAction->execute($order);
        }

        return $this;
    }

    public function createCompany(IterableEvent $event): self
    {
        if (false === $this->processor->isConfigured()) {
            return $this;
        }

        foreach ($event as $companyId) {
            $company = $this->getCompany((int) $companyId);

            if ($this->companyService->canCreatePspAccount($company)) {
                try {
                    $wallet = $this->processor->getCompanyWallet($company);
                } catch (\Exception $exception) {
                    $this->logger->error('[LemonWay] create company, unable to create company.', [
                        'exception' => $exception,
                        'data' => $company->getData(),
                    ]);

                    continue;
                }

                $files = $this->companyService->getRegistrationFilesList(
                    $company->getId(),
                    null,
                    true
                );

                try {
                    $this->processor->sendKyc($wallet, $company, $files);
                } catch (\Exception $exception) {
                    $this->logger->error('[LemonWay] create company, unable to send KYC.', [
                        'exception' => $exception,
                        'data' => $company->getData(),
                    ]);
                }
            }
        }


        return $this;
    }

    public function updateCompany(IterableEvent $event): self
    {
        if (false === $this->processor->isConfigured()) {
            return $this;
        }

        foreach ($event as $companyId) {
            $company = $this->getCompany((int) $companyId);

            if (CompanyStatus::STATUS_NEW !== $company->getStatus()) {
                try {
                    $this->processor->updateWallet(
                        UserType::VENDOR(),
                        $this->userRepository->get(
                            $company->getFirstAdmin()->getId()
                        )
                    );
                } catch (KycLevelTooHigh $exception) {
                    fn_set_notification(
                        'W',
                        'LemonWay KYC',
                        $exception->getMessage()
                    );
                } catch (NotFound $exception) {
                    // Inutile de logger l'erreur, c'est juste que dans le workflow de changement de status (N => P)
                    // l'event company.updated est lancé avant company.approved
                } catch (\Exception $exception) {
                    $this->logger->error('[LemonWay] Error on company update.', [
                        'exception' => $exception,
                        'data' => $company->getData(),
                    ]);
                }
            }
        }

        return $this;
    }

    public function registerIban(IterableEvent $event): self
    {
        if (false === $this->processor->isConfigured()) {
            return $this;
        }

        foreach ($event as $companyId) {
            $company = $this->getCompany((int) $companyId);

            if ($this->companyService->canRegisterIban($company)) {
                try {
                    $wallet = $this->processor->getCompanyWallet($company);
                } catch (\Exception $exception) {
                    $this->logger->error('[LemonWay] Register IBAN, unable to get wallet details.', [
                        'exception' => $exception,
                        'data' => $company->getData(),
                    ]);

                    continue;
                }

                try {
                    $this->processor->unregisterIban($wallet);
                } catch (\Exception $exception) {
                    $this->logger->error('[LemonWay] Register IBAN, unable to remove iban.', [
                        'exception' => $exception,
                        'data' => $company->getData(),
                    ]);

                    continue;
                }

                try {
                    $this->processor->registerIban($wallet, $company);
                } catch (\Exception $exception) {
                    $this->logger->error('[LemonWay] Register IBAN, unable to register iban.', [
                        'exception' => $exception,
                        'data' => $company->getData(),
                    ]);
                }
            }
        }

        return $this;
    }

    public function createCompanyAndRegisterIban(IterableEvent $event): self
    {
        $this->createCompany($event)
            ->registerIban($event);

        foreach ($event as $companyId) {
            $this->sendKyc(new CompanyLegalDocumentsUpdated($companyId, []));
        }

        return $this;
    }

    public function sendKyc(CompanyLegalDocumentsUpdated $event, bool $force = false): self
    {
        if (false === $this->processor->isConfigured()) {
            return $this;
        }

        $company = $this->getCompany($event->getCompanyId());

        if ($this->companyService->canSendKyc($company)) {
            try {
                $wallet = $this->processor->getCompanyWallet($company);
            } catch (\Exception $exception) {
                $this->logger->error('[LemonWay] send KYC, unable to get wallet details.', [
                    'exception' => $exception,
                    'data' => $company->getData(),
                ]);

                return $this;
            }

            $files = $this
                ->companyService
                ->getRegistrationFilesList($company->getId(), null, true, $event->getDocuments());

            try {
                $this->processor->sendKyc($wallet, $company, $files, $force);
            } catch (\Exception $exception) {
                $this->logger->error('[LemonWay] send KYC, unable to send KYC.', [
                    'exception' => $exception,
                    'data' => $company->getData(),
                    'file' => $event->getDocuments(),
                ]);
            }
        }

        return $this;
    }

    public function transferFromBuyerToTechnical(PaymentCallbackEvent $event): self
    {
        if ($event->getProcessorName()->equals(PaymentProcessorName::LEMONWAY()) === false) {
            return $this;
        }

        $mainOrder = $event->getOrder();
        $fromWallet = $this->processor->getMoneyInWallet($this->userRepository->get($mainOrder->getUserId()))->ID;
        $toWallet = $this->processor->getTechnicalWallet()->ID;

        $response = null;
        try {
            // Check if we need to do the transfer ?
            /** @var LemonWayTransactionDetails $transactionDetails */
            $transactionDetails = $this->processor->getTransactionDetails($event->getReference());
            if (false === $this->processor->getConfig()->isUseBuyerWalletForMoneyIn()
                || false === $transactionDetails->isSuccessFul()
                || true === $this->orderService->isPaid($mainOrder->getId())
                || true === $this->orderService->isBillingFailed($mainOrder->getId())
            ) {
                return $this;
            }

            $response = $this->processor->transferTransaction(
                $mainOrder,
                Money::fromVariable($transactionDetails->getAmount()),
                $fromWallet,
                $toWallet
            );

            if ($response->HPAY->STATUS !== LemonWayApi::STATUS_SUCCESS) {
                throw new LemonWayException("Transfer transaction from buyer to technical wallet failed");
            }
        } catch (\Throwable $e) {
            $this->logger->error('[LEMONWAY] p2p transfer from buyer to technical failed', [
                'transactionId' => $event->getReference(),
                'orderId' => $mainOrder->getId(),
                'exception' => $e,
            ]);

            $this->orderService->markOrdersWithAction(
                $mainOrder->getId(),
                $this->markPaymentAsRefused,
                OrderStatus::BILLING_FAILED()
            );

            $this->orderNotifier->internalTransferError(new InternalTransferErrorDTO(
                $mainOrder->getId(),
                $mainOrder->getTotal()->getConvertedAmount(),
                $event->getReference(),
                $response->HPAY->ID ?? null,
                $fromWallet,
                $toWallet
            ));
        }

        return $this;
    }

    public function marketplaceDiscountProcess(PaymentCallbackEvent $event): self
    {
        if ($event->getProcessorName()->equals(PaymentProcessorName::LEMONWAY()) === false) {
            return $this;
        }

        // Need marketplace discount process ?
        /** @var LemonWayTransactionDetails $transactionDetails */
        $transactionDetails = $this->processor->getTransactionDetails($event->getReference());
        $order = $event->getOrder();
        if (false === $transactionDetails->isReservation() || false === $order->hasMarketplaceDiscount()) {
            return $this;
        }

        try {
            $this->transferMarketplaceDiscount($order);
        } catch (\Throwable $exception) {
            $this->logger->error('Unable to transfer marketplace discount', [
                'orderId' => $order->getId(),
                'transactionId' => $transactionDetails->getTransactionId(),
                'exception' => $exception,
            ]);

            $this->processor->cancelTransaction($transactionDetails);

            return $this;
        }

        try {
            $this->processor->captureTransaction($transactionDetails);
        } catch (\Throwable $exception) {
            $this->logger->error('Unable to capture transaction', [
                'orderId' => $order->getId(),
                'transactionId' => $transactionDetails->getTransactionId(),
                'exception' => $exception,
            ]);

            $this->refundMarketplaceDiscount($order);
        }

        return $this;
    }

    /**
     * In case of a bank wire, we need to update the Transaction Reference before updating its Status.
     * Because at transaction creation, the real reference (Lemonway transaction id) was unknown.
     */
    public function updateTransactionStatus(PaymentCallbackEvent $event): AbstractProcessorEventSubscriber
    {
        if ($event->getProcessorName()->equals(PaymentProcessorName::LEMONWAY()) === false) {
            return $this;
        }

        $transactions = $this->transactionService->findByOrderIds(
            \array_map(
                function (Order $order): int {
                    return $order->getId();
                },
                $event->getOrder()->getSubOrders()
            ),
            TransactionType::BANK_WIRE()
        );

        foreach ($transactions as $transaction) {
            if ($transaction->isPending() && $transaction->getProcessorName() === PaymentProcessorName::LEMONWAY()->getValue()) {
                $this->transactionService->save(
                    $transaction->setTransactionReference($event->getReference())
                );
            }
        }

        return parent::updateTransactionStatus($event);
    }

    public function transferMarketplaceDiscount(Order $order): self
    {
        $orders = \count($order->getSubOrders()) > 0 ? $order->getSubOrders() : [$order];

        foreach ($orders as $order) {
            if ($order->hasMarketplaceDiscount()) {
                $transaction = $this->transactionService->createTransferTransaction(
                    $order->getId(),
                    $order->getMarketplaceDiscountTotal(),
                    PaymentProcessorName::LEMONWAY(),
                    ['coupon_marketplace_discount' => $order->getCouponMarketplaceDiscount()]
                );

                try {
                    $response = $this->processor->transferTransaction(
                        $order,
                        $order->getMarketplaceDiscountTotal(),
                        $this->processor->getConfig()->getDiscountWallet(),
                        $this->processor->getTechnicalWallet()->ID
                    );
                } catch (\Throwable $exception) {
                    /** @var TransferMarketplaceDiscountFailed */
                    $transferMarketplaceDiscountFailed = container()->get('marketplace.order.action.transfer_marketplace_discount_failed');
                    $transferMarketplaceDiscountFailed->execute($order->getOrderV2());

                    $transaction->setStatus(TransactionStatus::FAILED());
                    $this->transactionService->save($transaction);

                    $this->logger->error("[LemonWay] Transfer failed.", [
                        'orderId' => $order->getId(),
                        'amount' => $order->getMarketplaceDiscountTotal()->getConvertedAmount(),
                        'exception' => $exception,
                    ]);

                    throw new \Exception("[LemonWay] Transfer failed, orderId: {$order->getId()}.");
                }

                // Update the transaction
                $transaction->setTransactionReference($response->HPAY->ID);
                $transaction->setStatus($this->processor->getHumanStatus($response->HPAY->STATUS));
                $this->transactionService->save($transaction);

                if ($transaction->isFailed()) {
                    throw new \Exception("[LemonWay] Transfer failed, orderId: {$order->getId()}.");
                }
            }
        }

        return $this;
    }

    public function createCardFromTransaction($data): ?CreditCard
    {
        return null;
    }

    private function refundMarketplaceDiscount(Order $order): self
    {
        $orders = \count($order->getSubOrders()) > 0 ? $order->getSubOrders() : [$order];
        $orderIds = array_map(function (Order $order): int {
            return $order->getId();
        }, $orders);

        $transactions = $this->transactionService->findBy([
            'orderId' => $orderIds,
            'type' => TransactionType::TRANSFER(),
            'status' => TransactionStatus::SUCCESS(),
        ]);

        if (\count($transactions) > 0) {
            array_map(function (Transaction $transaction, Order $order): void {
                if ($transaction->getOrderId() !== $order->getId()) {
                    return;
                }

                $this->processor->transferTransaction(
                    $order,
                    $transaction->getAmount(),
                    $this->processor->getTechnicalWallet()->ID,
                    $this->processor->getConfig()->getDiscountWallet()
                );

                $transaction->setStatus(TransactionStatus::FAILED());
                $this->transactionService->save($transaction);
            }, $transactions, $orders);
        }

        return $this;
    }

    private function getCompany($companyId): Company
    {
        $company = new Company($companyId);
        $company->setSkipCache(true);

        return $company;
    }

    private function canBeDispatchedAfterFailed(MarketplaceOrder $order): bool
    {
        // To dispatch when payment refused by Lemonway and marked as payed by operator
        if ($this->transactionService->hasRefusedPayment($order->getId()) === true
            && $order->isPaid() === true
            && $order->getPayment()->getType()->equals(PaymentType::PAYMENT_DEFERMENT()) === true
            && $order->getPayment()->getProcessorName()->equals(PaymentProcessorName::LEMONWAY()) === true
        ) {
            return true;
        }

        return false;
    }
}
