<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\LemonWay;

use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Payment\LemonWay\Exception\LemonWayException;
use Wizacha\Storage\StorageService;

use function GuzzleHttp\Psr7\stream_for;

class LemonWayApi
{
    public const STATUS_SUCCESS = '3';
    public const STATUS_ERROR = '4';
    public const STATUS_PENDING = '0';
    public const STATUS_RESERVATION = '16';

    public const STATUS_IBAN_PENDING = '4';
    public const STATUS_IBAN_ENABLE = '5';
    public const STATUS_IBAN_DISABLE = '8';
    public const STATUS_IBAN_REJECT = '9';

    public const STATUS_KYC_WAITING = '0';
    public const STATUS_KYC_PENDING = '1';
    public const STATUS_KYC_ACCEPTED = '2';
    public const STATUS_KYC_REJECTED = '3';
    public const STATUS_KYC_UNREADABLE = '4';
    public const STATUS_KYC_EXPIRED = '5';
    public const STATUS_KYC_WRONG_TYPE = '6';
    public const STATUS_KYC_WRONG_NAME = '7';

    public const ERROR_CODES_DUPLICATED_DOCUMENT_FOUND = 341;

    public const NOTIFICATION_CATEGORY_BANKWIRE = '10';
    public const NOTIFICATION_TRANSACTION_SUCCESS = '0';
    public const NOTIFICATION_TRANSACTION_ERROR = '6';
    public const NOTIFICATION_SDD_RECEIVED = '11';
    public const NOTIFICATION_SDD_CANCELED = '17';

    public const STATUS_MANDATE_SDD_ENABLED = '6';

    /** Days limit for payment authorization  */
    public const DELAYED_DAYS_MIN = '1';
    public const DELAYED_DAYS_MAX = '6';

    /** @var LemonWayHttpClient */
    protected $httpClient;

    /** @var ?string */
    protected $webKitUrl;

    /** @var bool */
    protected $configured = false;

    public function __construct(LemonWayHttpClient $httpClient, string $webKitUrl)
    {
        $this->httpClient = $httpClient;
        if ("" !== $webKitUrl) {
            $this->configured = true;
            $this->webKitUrl = $webKitUrl;
        }
    }

    public function getWebKitUrl(): ?string
    {
        return $this->webKitUrl;
    }

    public function isConfigured(): bool
    {
        return $this->configured;
    }

    /**
     * @see moneyInWebInit
     */
    public function generatePaymentUrl(\stdClass $moneyIn, string $cssUrl = null): string
    {
        $this->assertConfigured();

        $queries = [
            'moneyInToken' => $moneyIn->TOKEN,
            'lang' => (string) GlobalState::interfaceLocale(),
        ];

        if (\is_string($cssUrl)) {
            $queries['p'] = urlencode($cssUrl);
        }

        return $this->webKitUrl . '?' . http_build_query($queries);
    }

    public function generateToken(): string
    {
        return (string) (microtime(true) * 1000);
    }

    /** @see http://documentation.lemonway.fr/pages/viewpage.action?pageId=4195030 */
    public function getWalletDetails(array $parameters): \stdClass
    {
        return $this->httpClient->sendRequest('GetWalletDetails', $parameters);
    }

    /** @see http://documentation.lemonway.fr/pages/viewpage.action?pageId=1540439 */
    public function registerWallet(array $parameters): \stdClass
    {
        return $this->httpClient->sendRequest('RegisterWallet', $parameters);
    }

    /** @see http://documentation.lemonway.fr/pages/viewpage.action?pageId=1540446 */
    public function moneyInWebInit(array $parameters): \stdClass
    {
        return $this->httpClient->sendRequest('MoneyInWebInit', $parameters);
    }

    /** @see http://documentation.lemonway.fr/pages/viewpage.action?pageId=1540450 */
    public function moneyInValidate(array $parameters): \stdClass
    {
        return $this->httpClient->sendRequest('MoneyInValidate', $parameters);
    }

    /** @see http://documentation.lemonway.fr/pages/viewpage.action?pageId=9043985 */
    public function cancelMoneyIn(array $parameters): \stdClass
    {
        return $this->httpClient->sendRequest('CancelMoneyIn', $parameters);
    }

    /** @see http://documentation.lemonway.fr/pages/viewpage.action?pageId=1540455 */
    public function getMoneyInTransDetails(array $parameters): \stdClass
    {
        return $this->httpClient->sendRequest('GetMoneyInTransDetails', $parameters);
    }

    /** @see http://documentation.lemonway.fr/pages/viewpage.action?pageId=1540451 */
    public function sendPayment(array $parameters): \stdClass
    {
        return $this->httpClient->sendRequest('SendPayment', $parameters);
    }

    /** @see http://documentation.lemonway.fr/pages/viewpage.action?pageId=1540440 */
    public function updateWalletDetails(array $parameters): \stdClass
    {
        return $this->httpClient->sendRequest('UpdateWalletDetails', $parameters);
    }

    /** @see http://documentation.lemonway.fr/pages/viewpage.action?pageId=1540452 */
    public function registerIBAN(array $parameters): \stdClass
    {
        return $this->httpClient->sendRequest('RegisterIBAN', $parameters);
    }

    /** @see http://documentation.lemonway.fr/pages/viewpage.action?pageId=11993224 */
    public function unregisterIBAN(array $parameters): \stdClass
    {
        return $this->httpClient->sendRequest('UnregisterIBAN', $parameters);
    }

    /** @see http://documentation.lemonway.fr/pages/viewpage.action?pageId=1540457 */
    public function uploadFile(array $parameters, StorageService $store): \stdClass
    {
        $stream = $store->readStream($parameters['fileName']);
        $parameters['buffer'] = base64_encode(stream_for($stream)->getContents());
        $parameters['fileName'] = basename($parameters['fileName']);

        return $this->httpClient->sendRequest('UploadFile', $parameters);
    }

    /** @see http://documentation.lemonway.fr/pages/viewpage.action?pageId=1540453 */
    public function moneyOut(array $parameters): \stdClass
    {
        return $this->httpClient->sendRequest('MoneyOut', $parameters);
    }

    /**
     * @see http://documentation.lemonway.fr/pages/viewpage.action?pageId=1540460
     *
     * @param string[] $parameters
     */
    public function refundMoneyIn(array $parameters): \stdClass
    {
        return $this->httpClient->sendRequest('RefundMoneyIn', $parameters);
    }

    /**
     * @param string[] $parameters
     */
    public function registerSddMandate(array $parameters): \stdClass
    {
        return $this->httpClient->sendRequest('RegisterSddMandate', $parameters);
    }

    /**
     * @param string[] $parameters
     */
    public function signDocumentInit(array $parameters): \stdClass
    {
        return $this->httpClient->sendRequest('SignDocumentInit', $parameters);
    }

    protected function assertConfigured(): self
    {
        if (false === $this->isConfigured()) {
            throw new LemonWayException("LemonWay API is not configured");
        }

        return $this;
    }

    public function moneyInSddInit(array $parameters): \stdClass
    {
        return $this->httpClient->sendRequest('MoneyInSddInit', $parameters);
    }
}
