<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\LemonWay;

use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Wizacha\Marketplace\Payment\LemonWay\Exception\LemonWayException;

class LemonWayHttpClient
{
    public const MAX_RETRY = 3;
    protected const VERSION = '4.7';
    protected const REQUEST_PAYLOAD_KEY = 'request_payload';

    /** @var bool */
    protected $configured = false;

    protected LemonWayLogger $logger;

    /** @var RequestStack */
    protected $requestStack;

    /** @var ?string */
    protected $directKitUrl;

    /** @var ?string */
    protected $clientLogin;

    /** @var ?string */
    protected $clientPassword;

    /** @var ?string */
    protected $proxyHost;

    /** @var int */
    protected $proxyPort;

    /** @var ?string */
    protected $proxyUser;

    /** @var ?string */
    protected $proxyPassword;

    private array $latestParameters;

    /** @var null|false|string */
    private $latestRequest;

    private ?string $latestServiceUrl;
    private ?int $latestHttpStatus;
    private array $latestLogContext;

    public function __construct(
        LemonWayLogger $logger,
        RequestStack $requestStack,
        string $directKitUrl,
        string $clientLogin,
        string $clientPassword,
        string $proxyHost,
        int $proxyPort,
        string $proxyUser,
        string $proxyPassword
    ) {
        if ("" !== $directKitUrl) {
            $this->configured = true;

            $this->logger = $logger;
            $this->requestStack = $requestStack;
            $this->directKitUrl = $directKitUrl;
            $this->clientLogin = $clientLogin;
            $this->clientPassword = $clientPassword;
            $this->proxyHost = $proxyHost;
            $this->proxyPort = $proxyPort;
            $this->proxyUser = $proxyUser;
            $this->proxyPassword = $proxyPassword;
        }
    }

    /**
     * LemonWay ne dispose pas d'un SDK, ils ne font que proposer une fonction qui utilise cURL
     * J'ai modifié un peu la fonction pour couvrir nos besoins et surtout la rendre un peu plus propre
     *
     * @see https://github.com/lemonwaysas/php-client-directkit-json2/blob/master/LemonWay.php
     */
    public function sendRequest(string $name, array $parameters): \stdClass
    {
        static $sequenceId = 0;
        ++$sequenceId;

        $this->latestParameters = $parameters;
        $this->latestRequest = null;
        $this->latestServiceUrl = null;
        $this->latestHttpStatus = null;
        $this->latestLogContext = ['sequence_id' => $sequenceId];

        $this->logger->start();

        try {
            return $this->doRequest($name);
        } catch (\Throwable $exception) {
            if ($this->latestHttpStatus !== Response::HTTP_OK) {
                $this->logger->requestException(
                    'POST',
                    $this->latestServiceUrl ?? $name,
                    \is_string($this->latestRequest) ? $this->latestRequest : ['p' => $this->latestParameters],
                    $exception,
                    array_merge(
                        $this->latestLogContext,
                        [
                            'proxy' => [
                                'host' => $this->proxyHost,
                                'port' => $this->proxyPort,
                                'user' => $this->proxyUser,
                                'passwd' => $this->proxyPassword,
                            ],
                        ]
                    )
                );
            }

            throw $exception;
        }
    }

    private function doRequest(string $name): \stdClass
    {
        $this->assertConfigured();

        $currentRequest = $this->requestStack->getCurrentRequest();

        ksort($this->latestParameters);

        // add missing required parameters
        $this->latestParameters['wlLogin'] = $this->clientLogin;
        $this->latestParameters['wlPass'] = $this->clientPassword;
        $this->latestParameters['version'] = static::VERSION;
        $this->latestParameters['walletIp'] = \is_null($currentRequest) ? '127.0.0.1' : $currentRequest->getClientIp();
        $this->latestParameters['walletUa'] = \is_null($currentRequest) ? 'ua' : $currentRequest->headers->get(
            'User-Agent',
            'ua'
        );

        $this->latestRequest = json_encode(['p' => $this->latestParameters]);
        if ($this->latestRequest === false) {
            throw new LemonWayException('[LemonWay] Unable to encode request parameters');
        }

        $this->latestServiceUrl = $this->directKitUrl . '/' . $name;
        $headers = [
            "Content-type: application/json;charset=utf-8",
            "Accept: application/json",
            "Cache-Control: no-cache",
            "Pragma: no-cache",
        ];
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->latestServiceUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_ANY);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $this->latestRequest);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        // Init proxy
        if ("" !== $this->proxyHost && \is_int($this->proxyPort)) {
            curl_setopt($ch, CURLOPT_HTTPPROXYTUNNEL, true);

            // Proxy URL
            curl_setopt($ch, CURLOPT_PROXY, $this->proxyHost);
            curl_setopt($ch, CURLOPT_PROXYPORT, $this->proxyPort);

            // Proxy authenticate
            if ("" !== $this->proxyUser && "" != $this->proxyPassword) {
                curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
                curl_setopt($ch, CURLOPT_PROXYUSERPWD, $this->proxyUser . ":" . $this->proxyPassword);
            }
        }

        $retry = 0;
        do {
            if ($retry > 0) {
                \sleep(10);
            }

            $response = curl_exec($ch);
            $httpStatus = (int) curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $retry++;
        } while ($httpStatus === 0 && $retry < self::MAX_RETRY);

        $networkErr = curl_errno($ch);
        if ($networkErr) {
            $error = curl_error($ch);

            $this->latestLogContext = [
                'curl_errno' => $networkErr,
                'curl_error' => $error,
                'curl_info' => curl_getinfo($ch),
            ];

            throw new LemonWayException($error);
        }

        $this->latestHttpStatus = (int) curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if ($this->latestHttpStatus === 0) {
            $error = curl_error($ch);

            $this->latestLogContext = [
                'curl_errno' => $networkErr,
                'curl_error' => $error,
                'curl_info' => curl_getinfo($ch),
            ];

            curl_close($ch);

            throw new LemonWayException($error);
        }

        curl_close($ch);

        if ($this->latestHttpStatus === Response::HTTP_OK) {
            $unwrapResponse = json_decode($response)->d;
            // phpcs:ignore
            $businessErr = $unwrapResponse->E;
            if ($businessErr) {
                $this->logger->requestError(
                    'POST',
                    $this->latestServiceUrl,
                    $this->latestRequest,
                    $this->latestHttpStatus,
                    $response,
                    $this->latestLogContext
                );

                throw new LemonWayException(
                // phpcs:ignore
                    $businessErr->Msg,
                    // phpcs:ignore
                    (int) $businessErr->Code
                );
            }

            $responseArray = get_object_vars($unwrapResponse);
            unset($responseArray['__type'], $responseArray['E']);

            $responseArrayKeys = array_keys($responseArray);
            $responseData = $unwrapResponse->{reset($responseArrayKeys)};

            $this->logger->requestSuccess(
                'POST',
                $this->latestServiceUrl,
                $this->latestRequest,
                $this->latestHttpStatus,
                $response,
                $this->latestLogContext
            );

            return $responseData;
        }

        throw new LemonWayException("[LemonWay] Service return HttpStatus $this->latestHttpStatus");
    }

    public function isConfigured(): bool
    {
        return $this->configured;
    }

    protected function assertConfigured(): self
    {
        if (false === $this->isConfigured()) {
            throw new LemonWayException("LemonWay HTTP client is not configured");
        }

        return $this;
    }
}
