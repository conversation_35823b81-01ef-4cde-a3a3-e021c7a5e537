<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\LemonWay;

use Wizacha\Marketplace\Payment\AbstractPaymentLogger;

/**
 * This logger is used in LemonWayHttpClient for every API call.
 *
 * @see LemonWayHttpClient
 */
class LemonWayLogger extends AbstractPaymentLogger
{
    private const LEMONWAY_LOG_NAME = 'LemonWay API';

    public function requestSuccess(
        string $method,
        string $endpoint,
        string $requestParams,
        int $responseCode,
        string $responseBody,
        array $context
    ): void {
        $this->logRequestSuccess(
            self::LEMONWAY_LOG_NAME . ' Request Success',
            $method,
            $endpoint,
            $requestParams,
            $responseCode,
            $responseBody,
            $context
        );
    }

    public function requestError(
        string $method,
        string $endpoint,
        string $requestParams,
        int $responseCode,
        string $responseBody,
        array $context
    ) {
        $this->logRequestError(
            self::LEMONWAY_LOG_NAME . ' Request Error',
            $method,
            $endpoint,
            $requestParams,
            $responseCode,
            $responseBody,
            $context
        );
    }

    /**
     * @param string|array $requestParams
     */
    public function requestException(
        string $method,
        string $endpoint,
        $requestParams,
        \Throwable $exception,
        array $context
    ): void {
        $this->logRequestException(
            self::LEMONWAY_LOG_NAME . ' Error',
            $method,
            $endpoint,
            $requestParams,
            $exception,
            $context
        );
    }
}
