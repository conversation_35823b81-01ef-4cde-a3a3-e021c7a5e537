<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\LemonWay;

use MyCLabs\Enum\Enum;
use Wizacha\Marketplace\Payment\LemonWay\Exception\MissingTransactionStatusMapping;
use Wizacha\Marketplace\Transaction\TransactionStatus;

/**
 * @method static self SUCCESS()
 * @method static self PENDING()
 * @method static self ERROR()
 * @method static self CANCELED()
 * @method static self WAIT()
 */
class LemonWayTransactionStatus extends Enum
{
    private const SUCCESS = '0';
    private const PENDING = '4';
    private const ERROR = '6';
    // canceled (by the customer or dropped)
    private const CANCELED = '7';
    // wait for validation / finalization
    private const WAIT = '16';

    /**
     * Lemonway:Wizaplace Transaction status mapping
     */
    public function getWizaplaceTransactionStatus(): TransactionStatus
    {
        switch ($this->getValue()) {
            case static::PENDING:
            case static::WAIT:
                return TransactionStatus::PENDING();
            case static::SUCCESS:
                return TransactionStatus::SUCCESS();
            case static::ERROR:
            case static::CANCELED:
                return TransactionStatus::FAILED();
            default:
                throw new MissingTransactionStatusMapping($this);
        }
    }
}
