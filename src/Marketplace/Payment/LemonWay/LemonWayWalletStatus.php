<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\LemonWay;

use MyCLabs\Enum\Enum;

/**
 * Class LemonWayWalletStatus
 * @method static self WALLET_SC()
 * @method static self REGISTERED_NOT_VERIFIED_MISSING_DOCUMENTS()
 * @method static self REGISTERED_NOT_VERIFIED_DOCUMENTS_REJECTED()
 * @method static self REGISTERED_NOT_VERIFIED_KYC1()
 * @method static self REGISTERED_AND_VERIFIED()
 * @method static self REGISTERED_AND_VERIFIED_BY_PREVIOUS_PSP_KYC3()
 * @method static self REGISTERED_NOT_VERIFIED_EXPIRED_DOCUMENTS()
 * @method static self BLOCKED()
 * @method static self CLOSED()
 * @method static self REGISTERED_STATUS_IS_BEING_UPDATED_FROM_KYC2_TO_KYC3()
 * @method static self ONE_TIME_CUSTOMER()
 * @method static self SPECIAL_WALLET_FOR_CROWLENDING()
 * @method static self TECHNICAL_WALLET()
 * @package Wizacha\Marketplace\Payment\LemonWay
 */
class LemonWayWalletStatus extends Enum
{
    private const WALLET_SC = -1;
    private const REGISTERED_NOT_VERIFIED_MISSING_DOCUMENTS = 2;
    private const REGISTERED_NOT_VERIFIED_DOCUMENTS_REJECTED = 3;

    // status given at registration
    private const REGISTERED_NOT_VERIFIED_KYC1 = 5;
    private const REGISTERED_AND_VERIFIED = 6;
    private const REGISTERED_AND_VERIFIED_BY_PREVIOUS_PSP_KYC3 = 7;
    private const REGISTERED_NOT_VERIFIED_EXPIRED_DOCUMENTS = 8;
    private const BLOCKED = 10;
    private const CLOSED = 12;
    private const REGISTERED_STATUS_IS_BEING_UPDATED_FROM_KYC2_TO_KYC3 = 13;
    private const ONE_TIME_CUSTOMER = 14;
    private const SPECIAL_WALLET_FOR_CROWLENDING = 15;
    private const TECHNICAL_WALLET = 16;

    public static function isKyc1(string $walletStatus): bool
    {
        return \in_array(
            $walletStatus,
            [
                static::REGISTERED_NOT_VERIFIED_MISSING_DOCUMENTS,
                static::REGISTERED_NOT_VERIFIED_DOCUMENTS_REJECTED,
                static::REGISTERED_NOT_VERIFIED_KYC1
            ]
        );
    }
}
