<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\LemonWay;

use Wizacha\Marketplace\Payment\LemonWay\Exception\BankWireLabelIsEmptyException;

class LemonWayTokenRetriever
{
    /**
     * Used to extract token from a Lemonway bank wire label
     * see Lemonway::generateLabel() for format details
     */
    public static function fromBankWireLabel(string $label): string
    {
        if ($label === "") {
            throw new BankWireLabelIsEmptyException();
        }

        return \trim(
            \array_pop(
                \explode('-', $label)
            )
        );
    }
}
