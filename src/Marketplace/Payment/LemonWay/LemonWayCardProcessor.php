<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\LemonWay;

use Wizacha\Marketplace\Payment\PaymentProcessor;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\PaymentType;
use Wizacha\Marketplace\Payment\Processor\LemonWay;
use Wizacha\Marketplace\Payment\Response\PaymentResponse;
use Wizacha\Marketplace\Payment\Response\RedirectPaymentResponse;

class LemonWayCardProcessor implements PaymentProcessor
{
    /**
     * @var LemonWay
     */
    private $lemonWay;

    public function __construct(LemonWay $lemonWay)
    {
        $this->lemonWay = $lemonWay;
    }

    public function startPayment(int $orderId, string $redirectUrl = null, string $cssUrl = null): PaymentResponse
    {
        $url = $this->lemonWay->createCardTransaction($orderId, $redirectUrl, $cssUrl);

        return new RedirectPaymentResponse($url);
    }

    public function getType(): PaymentType
    {
        return PaymentType::CREDIT_CARD();
    }

    public function getName(): PaymentProcessorName
    {
        return PaymentProcessorName::LEMONWAY();
    }

    public function isConfigured(): bool
    {
        return $this->lemonWay->isConfigured();
    }
}
