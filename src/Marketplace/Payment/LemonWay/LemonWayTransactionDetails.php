<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\LemonWay;

use Wizacha\Marketplace\Payment\LemonWay\Exception\LemonWayException;
use Wizacha\Marketplace\Payment\TransactionDetails;

/**
 * For managing moneyInTransDetails returned by LemonWay
 */
class LemonWayTransactionDetails implements TransactionDetails
{
    /** @var \stdClass */
    protected $data;

    /** @var string|null */
    protected $token;

    public function __construct(\stdClass $data, string $token = null)
    {
        $this->setToken($token);
        $this->setData($data);
    }

    public function setToken(?string $token): self
    {
        $this->token = $token;

        return $this;
    }

    public function setData(\stdClass $data): self
    {
        $this->data = $data;

        return $this;
    }

    public function getToken(): ?string
    {
        return $this->token;
    }

    public function getData(): \stdClass
    {
        return $this->data;
    }

    public function isSuccessFul(): bool
    {
        return $this->checkTransactionStatus(LemonWayApi::STATUS_SUCCESS);
    }

    public function isReservation(): bool
    {
        return $this->checkTransactionStatus(LemonWayApi::STATUS_RESERVATION);
    }

    public function checkTransactionStatus(string $status): bool
    {
        return $this->getStatus() === $status;
    }

    /**
     * @deprecated STATUS deprecated and replaced by INT_STATUS in LemonWay
     *
     * @see self::getTransactionStatus
     */
    public function getStatus(): string
    {
        if (\array_key_exists(0, $this->data->HPAY)) {
            return $this->data->HPAY[0]->STATUS;
        }

        throw new LemonWayException("Cannot get status from transactionMerchantToken {$this->token}.");
    }

    public function getTransactionStatus(): LemonWayTransactionStatus
    {
        if (\array_key_exists(0, $this->data->HPAY)) {
            return new LemonWayTransactionStatus(
                $this->data->HPAY[0]->INT_STATUS
            );
        }

        throw new LemonWayException("Cannot get status from transactionMerchantToken {$this->token}.");
    }

    public function getTransactionId(): string
    {
        if (\array_key_exists(0, $this->data->HPAY)) {
            return $this->data->HPAY[0]->ID;
        }

        throw new LemonWayException("Cannot get transaction Id from transactionMerchantToken {$this->token}.");
    }

    public function getOrderId(): string
    {
        return \reset($this->getOrderIds());
    }

    public function getOrderIds(): array
    {
        if (false === \array_key_exists(0, $this->data->HPAY)) {
            throw new LemonWayException("Cannot get orderId from transactionMerchantToken {$this->token}.");
        }
        $orderIds = static::extractOrderIds($this->data->HPAY[0]->MSG);

        if (0 === \count($orderIds)) {
            throw new LemonWayException("Cannot get orderId from transactionMerchantToken {$this->token}.");
        }

        return $orderIds;
    }

    private static function isValidOrderIds(string $msg): bool
    {
        return 1 === \preg_match('/^M(\d+;)*(\d+)/', $msg);
    }

    public static function extractOrderIds(string $msg): array
    {
        if (false === static::isValidOrderIds($msg)) {
            return [];
        }

        return \array_values(
            \array_filter(
                \explode(
                    ';',
                    trim($msg, 'M;')
                )
            )
        );
    }

    public function getComment(): string
    {
        if (\array_key_exists(0, $this->data->HPAY)) {
            return $this->data->HPAY[0]->MSG;
        }

        throw new LemonWayException("Cannot get comment transactionMerchantToken {$this->token}.");
    }

    public function getWalletId(): string
    {
        if (\array_key_exists(0, $this->data->HPAY)) {
            return $this->data->HPAY[0]->REC;
        }

        throw new LemonWayException("Cannot get wallet Id from transactionMerchantToken {$this->token}.");
    }

    public function getId(): string
    {
        return $this->getTransactionId();
    }

    public function getAmount(): string
    {
        if (\array_key_exists(0, $this->data->HPAY)) {
            return $this->data->HPAY[0]->CRED;
        }

        throw new LemonWayException("Cannot get amount from transactionMerchantToken {$this->token}.");
    }

    public function getDate(): string
    {
        if (\array_key_exists(0, $this->data->HPAY)) {
            return $this->data->HPAY[0]->DATE;
        }

        throw new LemonWayException("Cannot get date transactionMerchantToken {$this->token}.");
    }

    /** @return string[] */
    public function expose(): array
    {
        return \json_decode($this->data, true);
    }

    /** return mixed raw data Transaction */
    public function getRawData(): \stdClass
    {
        return $this->data;
    }
}
