<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\LemonWay\Exception;

class OrderTransactionDetailNotFound extends \Exception
{
    public int $orderId;

    public function __construct(int $orderId)
    {
        $this->orderId = $orderId;

        parent::__construct(
            'Transaction detail not found for orderId'
        );
    }
}
