<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\LemonWay\Exception;

use Wizacha\Marketplace\Payment\LemonWay\LemonWayTransactionDetails;

class LemonWayTransactionWizaplaceOrderIdMatchingException extends \Exception
{
    public int $orderId;
    public LemonWayTransactionDetails $lemonwayTransactionDetails;

    public function __construct(
        int $orderId,
        LemonWayTransactionDetails $lemonwayTransactionDetails
    ) {
        $this->orderId = $orderId;
        $this->lemonwayTransactionDetails = $lemonwayTransactionDetails;

        parent::__construct(
            'orderId not matching Transaction details'
        );
    }
}
