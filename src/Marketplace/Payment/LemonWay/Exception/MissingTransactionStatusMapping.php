<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\LemonWay\Exception;

use Wizacha\Marketplace\Payment\LemonWay\LemonWayTransactionStatus;

class MissingTransactionStatusMapping extends LemonWayException
{
    public LemonWayTransactionStatus $lemonWayTransactionStatus;

    public function __construct(LemonWayTransactionStatus $lemonWayTransactionStatus)
    {
        $this->lemonWayTransactionStatus = $lemonWayTransactionStatus;

        parent::__construct('Lemonway:Wizaplace Transaction status mapping missing');
    }
}
