<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\LemonWay\Exception;

use Symfony\Component\HttpFoundation\Response;

/**
 * @see http://documentation.lemonway.fr/api-en/annex/error-codes-of-our-directkit
 */
class LemonWayException extends \Exception
{
    public const INTERNAL_ERROR = 1;
    public const LIMIT_AMOUNT_REACHED_SENDING_WALLET = 100;
    public const LIMIT_AMOUNT_REACHED_RECEIVING_WALLET = 101;
    public const INVALID_TRANSACTION = 143;
    public const WALLET_NOT_FOUND = 147;
    public const WALLET_ALREADY_EXIST = 152;
    public const INSUFFICIENT_CREDIT = 110;
    public const AMOUNT_NOT_ALLOWED = 151;
    public const SENDER_WALLET_BLOCKED_FOR_SECURITY_REASON = 167;
    public const RECEIVER_WALLET_BLOCKED_FOR_SECURITY_REASON = 168;
    public const BAD_FORMAT_URL = 239;
    public const AMOUNT_HIGHER_THAN_THE_ROLLING_RESERVE = 260;

    public function getResponseCode(): int
    {
        return Response::HTTP_FORBIDDEN;
    }

    public function getResponseMessage(): string
    {
        switch ($this->code) {
            case static::AMOUNT_NOT_ALLOWED:
                return __("lemonway_checkout_amount_unauthorised", ['[code]' => $this->getCode()]);
            case static::LIMIT_AMOUNT_REACHED_SENDING_WALLET:
            case static::INSUFFICIENT_CREDIT:
            case static::SENDER_WALLET_BLOCKED_FOR_SECURITY_REASON:
            case static::RECEIVER_WALLET_BLOCKED_FOR_SECURITY_REASON:
            case static::BAD_FORMAT_URL:
            case static::AMOUNT_HIGHER_THAN_THE_ROLLING_RESERVE:
                return __("lemonway_checkout_payment_service_error", ['[code]' => $this->getCode()]);
            default:
                return __("lemonway_checkout_service_unavailable", ['[code]' => $this->getCode()]);
        }
    }
}
