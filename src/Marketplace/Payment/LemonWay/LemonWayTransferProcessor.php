<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\LemonWay;

use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Templating\EngineInterface;
use Wizacha\Marketplace\Payment\Event\BankwirePaymentEvent;
use Wizacha\Marketplace\Payment\PaymentProcessor;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\PaymentType;
use Wizacha\Marketplace\Payment\Processor\LemonWay;
use Wizacha\Marketplace\Payment\Response\HtmlPaymentResponse;
use Wizacha\Marketplace\Payment\Response\PaymentResponse;
use Wizacha\Order;

class LemonWayTransferProcessor implements PaymentProcessor
{
    /** @var LemonWay */
    protected $lemonWay;

    /** @var EngineInterface */
    protected $templating;

    private EventDispatcherInterface $eventDispatcher;

    public function __construct(
        LemonWay $lemonWay,
        EngineInterface $templating,
        EventDispatcherInterface $eventDispatcher
    ) {
        $this->lemonWay = $lemonWay;
        $this->templating = $templating;
        $this->eventDispatcher = $eventDispatcher;
    }

    public function startPayment(int $orderId, string $redirectUrl = null, string $cssUrl = null): PaymentResponse
    {
        $transferInfo = $this->lemonWay->createBankwireTransaction($orderId, $redirectUrl);

        $this->eventDispatcher->dispatch(new BankwirePaymentEvent(new Order($orderId), $transferInfo));

        return new HtmlPaymentResponse(
            $this->templating->render('@App/frontend/checkout/lemonway_transfer_minimal.html.twig', $transferInfo)
        );
    }

    public function getType(): PaymentType
    {
        return PaymentType::BANK_TRANSFER();
    }

    public function getName(): PaymentProcessorName
    {
        return PaymentProcessorName::LEMONWAY();
    }

    public function isConfigured(): bool
    {
        return $this->lemonWay->isConfigured();
    }
}
