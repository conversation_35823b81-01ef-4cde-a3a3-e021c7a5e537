<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\LemonWay;

use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\PaymentType;
use Wizacha\Marketplace\Payment\Response\NoPaymentResponse;
use Wizacha\Marketplace\Payment\Response\PaymentResponse;

class LemonWaySepaDirectProcessor extends AbstractLemonWayPaymentMethod
{
    public function startPayment(int $orderId, string $redirectUrl = null, string $cssUrl = null): PaymentResponse
    {
        $orders = $this->orderService->getChildOrders($orderId);

        foreach ($orders as $order) {
            if (true === $this->markPaymentDefermentAsAuthorized->isAllowed($order)) {
                $this->markPaymentDefermentAsAuthorized->execute($order);
            }

            if (true === $this->confirm->isAllowed($order)) {
                $this->confirm->execute($order);
            }

            if (true === $this->commitTo->isAllowed($order)) {
                $this->commitTo->execute(
                    $order,
                    new \DateTimeImmutable(static::DUE_DATE),
                    static::generateCommitmentNumber()
                );

                fn_change_order_status(
                    $order->getId(),
                    (string) $order->deduceLegacyStatus(),
                    (string) OrderStatus::INCOMPLETED()
                );
            }
        }
        try {
            $this->processor->createSepaTransaction($orderId);
        } catch (\Throwable $e) {
            $this->logger->error(
                '[LemonWay SEPA Direct] Exception in SEPA start payment: ' . $e->getMessage(),
                [
                    'exception' => $e,
                    'data' => [
                        'orderId' => $orderId,
                    ],
                ]
            );

            throw $e;
        }

        return new NoPaymentResponse();
    }

    public function getType(): PaymentType
    {
        return PaymentType::SEPA_DIRECT();
    }

    public function getName(): PaymentProcessorName
    {
        return PaymentProcessorName::LEMONWAY();
    }

    public static function generateCommitmentNumber(): string
    {
        return uniqid('commitment_');
    }
}
