<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\LemonWay;

use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\PaymentType;
use Wizacha\Marketplace\Payment\Response\NoPaymentResponse;
use Wizacha\Marketplace\Payment\Response\PaymentResponse;

class LemonWaySepaDefermentProcessor extends AbstractLemonWayPaymentMethod
{
    public function startPayment(
        int $orderId,
        string $redirectUrl = null,
        string $cssUrl = null
    ): PaymentResponse {
        $orders = $this->orderService->getChildOrders($orderId);

        foreach ($orders as $order) {
            if (true === $this->markPaymentDefermentAsAuthorized->isAllowed($order)) {
                $this->markPaymentDefermentAsAuthorized->execute($order);
            }

            if (true === $this->confirm->isAllowed($order)) {
                $this->confirm->execute($order);
            }
        }

        return new NoPaymentResponse();
    }

    public function getType(): PaymentType
    {
        return PaymentType::PAYMENT_DEFERMENT();
    }

    public function getName(): PaymentProcessorName
    {
        return PaymentProcessorName::LEMONWAY();
    }
}
