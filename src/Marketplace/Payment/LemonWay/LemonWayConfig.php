<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\LemonWay;

use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Payment\LemonWay\Exception\LemonWayException;
use Wizacha\Marketplace\User\UserType;

final class LemonWayConfig
{
    public const LEMONWAY_TRANSACTION_ID = 'lemonway_transaction_id';

    public const BANKWIRE_LABEL = 'payment_transfer_libelle';

    public const PREFIX_VENDOR = 'company_';

    public const PREFIX_USER = 'user_';

    public const PREFIX_TECH_USER = 'tech_user_';

    public const PREFIX_MAIL_WALLET = '@wizaplace.com';

    /**
     * Le wallet SC est le wallet qui sert à récupérer la commission lors du dispatch funds
     * C'est aussi à partir de ce wallet que l'admin de la MP peut se payer
     *
     * Cette valeur est invariable, il n'est donc pas nécessaire de la mettre dans la configuration
     */
    public const MAIN_WALLET = 'SC';

    public const KYC = [
        UserType::CLIENT => [
            CompanyService::ID_CARD => '0',
            CompanyService::ADDRESS_PROOF => '1',
            CompanyService::RIB => '2',
        ],
        UserType::VENDOR => [
            CompanyService::ID_CARD => '0',
            CompanyService::ADDRESS_PROOF => '1',
            CompanyService::RIB => '2',
            CompanyService::KBIS => '7',
            CompanyService::STATUS_DOCUMENT => '12',
            CompanyService::DIVISION_OF_POWER => '14',
            CompanyService::DIVISION_OF_POWER_ID_CARDS => '15',
        ],
    ];

    public const LEMONWAY_DISPATCH_FUNDS_DONE = 'lemonway_dispatch_funds_done';
    public const LEMONWAY_TRANSFER_COM_DONE = 'lemonWay_transfer_com_done';
    public const LEMONWAY_TRANSFER_VENDOR_DONE = 'lemonWay_transfer_vendor_done';

    public const LEMONWAY_BANKWIRE = 'lemonway_bankwire';

    public const LEMONWAY_SEPA = 'lemonway_sepa';

    /**
     * @var bool
     *
     * Un technical wallet, est un wallet où transite l'argent.
     *
     * Dans le cas où $useTechWallet est à true, c'est à dire que l'on va utiliser le compte de la MP $techWalletId.
     * Sinon on va créer un compte par client acheteur et ils seront des technical wallets.
     */
    private $useBuyerWalletForMoneyIn;

    /** @var string  */
    private $technicalWalletId;

    /** @var string  */
    private $marketplaceId;

    /** @var string  */
    private $iban;

    /** @var string  */
    private $bic;

    /** @var string  */
    private $holderName;

    /** @var string  */
    private $holderAddress;

    /** @var string */
    private $discountWallet;

    /** @var string */
    protected $currencyCode;

    public function __construct(
        bool $useBuyerWalletForMoneyIn,
        string $technicalWalletId,
        string $marketplaceId,
        string $discountWallet,
        string $ban,
        string $bic,
        string $holderName,
        string $holderAddress,
        string $currencyCode
    ) {
        $this->useBuyerWalletForMoneyIn = $useBuyerWalletForMoneyIn;
        $this->technicalWalletId = $technicalWalletId;
        $this->marketplaceId = $marketplaceId;
        $this->iban = $ban;
        $this->bic = $bic;
        $this->holderName = $holderName;
        $this->holderAddress = $holderAddress;
        $this->discountWallet = $discountWallet;
        $this->currencyCode = $currencyCode;
    }

    public function isUseBuyerWalletForMoneyIn(): bool
    {
        return $this->useBuyerWalletForMoneyIn;
    }

    public function setUseBuyerWalletForMoneyIn(bool $useBuyerWalletForMoneyIn): self
    {
        $this->useBuyerWalletForMoneyIn = $useBuyerWalletForMoneyIn;

        return $this;
    }

    public function getTechnicalWalletId(): string
    {
        return $this->technicalWalletId;
    }

    public function setTechnicalWalletId(string $technicalWalletId): self
    {
        $this->technicalWalletId = $technicalWalletId;

        return $this;
    }

    public function getMarketplaceId(): string
    {
        return $this->marketplaceId;
    }

    public function setMarketplaceId(string $marketplaceId): self
    {
        $this->marketplaceId = $marketplaceId;

        return $this;
    }

    public function getIban(): string
    {
        return $this->iban;
    }

    public function setIban(string $iban): self
    {
        $this->iban = $iban;

        return $this;
    }

    public function getBic(): string
    {
        return $this->bic;
    }

    public function setBic(string $bic): self
    {
        $this->bic = $bic;

        return $this;
    }

    public function getHolderName(): string
    {
        return $this->holderName;
    }

    public function setHolderName(string $holderName): self
    {
        $this->holderName = $holderName;

        return $this;
    }

    public function getHolderAddress(): string
    {
        return $this->holderAddress;
    }

    public function setHolderAddress(string $holderAddress): self
    {
        $this->holderAddress = $holderAddress;

        return $this;
    }

    public function getDiscountWallet(): string
    {
        return $this->discountWallet;
    }

    /**
     * @return LemonWayConfig
     * @throws LemonWayException
     */
    public function assertBankWireIsConfigured(): self
    {
        if (0 === mb_strlen($this->iban)
            || 0 === mb_strlen($this->bic)
            || 0 === mb_strlen($this->holderName)
            || 0 === mb_strlen($this->holderAddress)
        ) {
            throw new LemonWayException("LemonWay bankwire is not configured.");
        }

        return $this;
    }

    public function getCurrencyCode(): string
    {
        return $this->currencyCode;
    }
}
