<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\LemonWay;

use Wizacha\Marketplace\Payment\LemonWay\Exception\LemonWayTransactionWizaplaceOrderIdMatchingException;
use Wizacha\Marketplace\Payment\LemonWay\Exception\OrderTransactionDetailNotFound;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\Processor\LemonWay;
use Wizacha\Marketplace\Payment\TransactionDetails;
use Wizacha\Marketplace\Transaction\Transaction;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionType;

class LemonWayTransactionSyncService
{
    private LemonWay $lemonWay;
    private TransactionService $transactionService;

    public function __construct(
        LemonWay $lemonWay,
        TransactionService $transactionService
    ) {
        $this->lemonWay = $lemonWay;
        $this->transactionService = $transactionService;
    }

    public function isConfigured(): bool
    {
        return $this->lemonWay->isConfigured();
    }

    /**
     * Sync Wizaplace:Lemonway transaction by Lemonway transaction id (Wizaplace transaction reference)
     */
    public function syncTransactionStatusByTransactionId(
        TransactionType $transactionType,
        int $orderId,
        string $transactionId
    ): Transaction {
        return $this->syncTransactionStatus(
            $transactionType,
            $orderId,
            $this->lemonWay->getTransactionDetails($transactionId)
        );
    }
    /**
     * Sync Wizaplace:Lemonway transaction by Lemonway webkit token
     */
    public function syncTransactionStatusByToken(
        TransactionType $transactionType,
        int $orderId,
        string $transactionMerchantToken
    ): Transaction {
        return $this->syncTransactionStatus(
            $transactionType,
            $orderId,
            $this->lemonWay->getTransactionDetailsByToken($transactionMerchantToken)
        );
    }
    /**
     * Sync Wizaplace:Lemonway transaction by Wizaplace order id
     */
    public function syncTransactionStatusByOrderId(
        TransactionType $transactionType,
        int $orderId
    ): Transaction {
        $transactionDetail = $this->lemonWay->getTransactionDetailsByOrderId($orderId);

        if (false === $transactionDetail instanceof TransactionDetails) {
            throw new OrderTransactionDetailNotFound($orderId);
        }

        return $this->syncTransactionStatus(
            $transactionType,
            $orderId,
            $transactionDetail
        );
    }

    /**
     * Sync Lemonway:Wizaplace transaction status if needed
     */
    public function syncTransactionStatus(
        TransactionType $transactionType,
        int $orderId,
        LemonWayTransactionDetails $lemonWayTransactionDetails
    ): Transaction {
        $expectedTransactionStatus = $lemonWayTransactionDetails
            ->getTransactionStatus()
            ->getWizaplaceTransactionStatus()
        ;

        if (false === \in_array($orderId, $lemonWayTransactionDetails->getOrderIds())) {
            throw new LemonWayTransactionWizaplaceOrderIdMatchingException(
                $orderId,
                $lemonWayTransactionDetails
            );
        }

        $transaction = $this->transactionService->retrieveTransaction(
            PaymentProcessorName::LEMONWAY(),
            $transactionType,
            $orderId,
            $lemonWayTransactionDetails->getTransactionId()
        );

        if (true === $expectedTransactionStatus->equals($transaction->getStatus())) {
            return $transaction;
        }

        // Actual transaction is out of sync with the Lemonway transaction status
        return $this
            ->transactionService
            ->save(
                $transaction->setStatus(
                    $expectedTransactionStatus
                )
            )
        ;
    }
}
