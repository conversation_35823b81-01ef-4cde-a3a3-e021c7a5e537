<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment;

use Wizacha\Marketplace\Image\Image;
use Wizacha\Status;

class Payment implements \JsonSerializable
{
    /**
     * @var int
     */
    private $id;

    /**
     * @var string
     */
    private $name;

    /**
     * @var string
     */
    private $description;

    /**
     * @var int
     */
    private $position;

    /**
     * @var Image|null
     */
    private $image;

    /**
     * @var string
     */
    private $status;

    /**
     * @var PaymentType
     */
    private $type;

    /**
     * @var string|null
     */
    private $externalReference;

    /** @var int */
    protected $processorId;

    public function __construct(array $data)
    {
        $this->id = (int) $data['payment_id'];
        $this->name = (string) $data['payment'];
        $this->description = (string) $data['description'];
        $this->position = (int) $data['position'];
        $this->image = $data['image_id'] ? new Image((int) $data['image_id'], $data['altText']) : null;
        $this->status = (string) $data['status'];
        $this->type = $data['type'];
        $this->externalReference = $data['external_reference'] ?? null;
        $this->processorId = (int) $data['processor_id'];
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function getPosition(): int
    {
        return $this->position;
    }

    public function getImage(): ?Image
    {
        return $this->image;
    }

    public function isActive(): bool
    {
        return $this->status !== Status::DISABLED;
    }

    public function getType(): PaymentType
    {
        return $this->type;
    }

    public function getExternalReference(): ?string
    {
        return $this->externalReference;
    }

    public function isNotCreditCard(): bool
    {
        return false === PaymentType::CREDIT_CARD()->equals($this->getType());
    }

    public function isNotCreditCardCapture(): bool
    {
        return false === PaymentType::CREDIT_CARD_CAPTURE()->equals($this->getType());
    }

    public function getProcessorId(): int
    {
        return $this->processorId;
    }

    /** @return mixed[] */
    public function jsonSerialize(): array
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'description' => $this->getDescription(),
            'position' => $this->getPosition(),
            'image' => $this->getImage() ? $this->getImage()->toArray() : null,
            'type' => $this->getType()->getValue(),
            'externalReference' => $this->getExternalReference(),
        ];
    }
}
