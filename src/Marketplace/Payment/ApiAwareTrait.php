<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment;

/**
 * Trait ApiAwareTrait
 * @package Wizacha\Marketplace\Payment
 * @see https://github.com/Payum/Payum/blob/master/src/Payum/Core/ApiAwareTrait.php
 */
trait ApiAwareTrait
{
    protected $api;

    public function setApi($api): self
    {
        $this->api = $api;

        return $this;
    }

    /** Retrieve the given Payment Service Provider API/SDK */
    public function getApi()
    {
        return $this->api;
    }
}
