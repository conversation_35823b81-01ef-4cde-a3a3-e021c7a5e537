<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Handler;

use Wizacha\Marketplace\Payment\PaymentProcessorName;

interface CallbackHandlerInterface
{
    /** @param mixed[] $payload */
    public function process(array $payload);
    public function getProcessorName(): PaymentProcessorName;
    public function getEventName(): string;
}
