<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\KYC;

use MyCLabs\Enum\Enum;

/**
 * @method static FileTypes ID_CARD()
 * @method static FileTypes ADDRESS_PROOF()
 * @method static FileTypes KBIS()
 * @method static FileTypes DIVISION_OF_POWER()
 * @method static FileTypes RIB()
 */
class FileTypes extends Enum
{
    protected const ID_CARD = 1;
    protected const ADDRESS_PROOF = 2;
    protected const KBIS = 4;
    protected const UPDATED_AND_SIGNED_STATUS_DOCUMENT = 5;
    protected const RIB = 6;

    /** @return string[] */
    public static function defineFileTypes(): array
    {
        return [
            static::ID_CARD => 'ID_card',
            static::ADDRESS_PROOF => 'address_proof',
            static::KBIS => 'KBIS',
            static::UPDATED_AND_SIGNED_STATUS_DOCUMENT => 'updated_and_signed_status_document',
            static::RIB => 'RIB',
        ];
    }
}
