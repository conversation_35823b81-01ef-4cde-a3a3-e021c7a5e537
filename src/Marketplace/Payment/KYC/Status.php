<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\KYC;

use MyCLabs\Enum\Enum;

/**
 * @method static Status VALIDATED()
 * @method static Status REFUSED()
 * @method static Status MISSING()
 * @method static Status WAITING()
 */
class Status extends Enum
{
    private const VALIDATED = 'Validated';
    private const REFUSED = 'Refused';
    private const MISSING = 'Missing';
    private const WAITING = 'Waiting';

    public static function fromHiPayAPI(string $status): self
    {
        // HiPay statuses match with internal values of the enum, but we check to prevent errors with stranges statuses
        if (self::isValid($status)) {
            return new self($status);
        }

        return self::MISSING();
    }
}
