<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment;

use Doctrine\DBAL\Connection;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Wizacha\Marketplace\Payment\Event\OfflinePaymentEvent;
use Wizacha\Marketplace\Payment\Response\NoPaymentResponse;
use Wizacha\Marketplace\Payment\Response\PaymentResponse;
use Wizacha\Order;

class PaymentService
{

    private Connection $db;
    private LoggerInterface $logger;
    private PaymentRepository $paymentRepository;

    private const MAX_ACCEPTATION_DELAY_DAYS = 7;

    /** The container is used to get the correct payment processor service.*/
    private ContainerInterface $container;

    public function __construct(
        Connection $db,
        ContainerInterface $container,
        LoggerInterface $logger,
        PaymentRepository $paymentRepository
    ) {
        $this->db = $db;
        $this->container = $container;
        $this->logger = $logger;
        $this->paymentRepository = $paymentRepository;
    }

    /** @return Payment[] */
    public function getActivePayments(): array
    {
        $rows = $this->paymentRepository->getActivePayments();

        return \array_map(function (array $row) {
            $row['type'] = $this->getPaymentType((int) $row['payment_id']);

            return new Payment($row);
        }, $rows);
    }

    /**
     * Pay an order.
     *
     * The response returned will lead to a payment page.
     */
    public function pay(
        int $orderId,
        int $paymentId,
        string $redirectUrl = null,
        string $cssUrl = null
    ): ?PaymentResponse {
        $payment = fn_get_payment_method_data($paymentId);
        if (empty($payment['processor_id'])) {
            // Special case for manual payments
            $confirm = $this->container->get('marketplace.order.action.confirm');
            $orders = $this->container->get('marketplace.order.order_service')->getChildOrders($orderId);

            $this->container->get('event_dispatcher')->dispatch(
                new OfflinePaymentEvent(new Order($orderId)),
                OfflinePaymentEvent::class
            );

            foreach ($orders as $order) {
                if ($order->getPayment()->isOfType(PaymentType::MANUAL()) && $confirm->isAllowed($order)) {
                    $confirm->execute($order);
                }
            }

            return new NoPaymentResponse();
        }

        $processor = $this->getPaymentProcessor($payment);

        if ($processor->isConfigured() === true) {
            $paymentResponse = $processor->startPayment($orderId, $redirectUrl, $cssUrl);
        } else {
            $paymentResponse = null;

            $this->logger->error('Payment processor ' . $processor->getName() . 'not configured ' . $orderId);
        }

        // Workflow next step for payment processor
        $orders = $this->container->get('marketplace.order.order_service')->getChildOrders($orderId);
        $redirectToPaymentProcessorAction = $this->container->get('marketplace.order.action.redirect_to_payment_processor');
        foreach ($orders as $order) {
            if ($redirectToPaymentProcessorAction->isAllowed($order)) {
                $redirectToPaymentProcessorAction->execute($order);
            }
        }

        return $paymentResponse;
    }

    public function getPaymentType(int $paymentId): PaymentType
    {
        // Si pas de paymentId : il n'y a pas eu d'action de payer,
        // ni manuellement, ni en CB, ni d'aucune sorte.
        if ($paymentId == 0) {
            return PaymentType::NONE();
        }

        $payment = fn_get_payment_method_data($paymentId);

        // S'il y a un paiement mais pas de processor, c'est que le paiement
        // est manuel : il est géré en dehors de la marketplace.
        if (empty($payment['processor_id'])) {
            return PaymentType::MANUAL();
        }

        // S'il y a eu un paiement avec un processor, on déduit le type
        // de paiement depuis le processor.
        return $this
            ->getPaymentProcessor($payment)
            ->getType();
    }

    public function getPaymentName(int $paymentId): ?string
    {
        if ($paymentId === 0) {
            return null;
        }

        return fn_get_payment_method_data($paymentId)['payment'];
    }

    public function getPaymentProcessorName(int $paymentId): ?PaymentProcessorName
    {
        // Si pas de paymentId : il n'y a pas eu d'action de payer,
        // ni manuellement, ni en CB, ni d'aucune sorte.
        // Donc pas de processor.
        if ($paymentId == 0) {
            return null;
        }

        $payment = fn_get_payment_method_data($paymentId);

        // S'il y a un paiement mais pas de processor, c'est que le paiement
        // est manuel : il est géré en dehors de la marketplace.
        // Donc pas de processor.
        if (empty($payment['processor_id'])) {
            return null;
        }

        // S'il y a eu un paiement avec un processor, on retourne le nom du
        // processor.
        return $this
            ->getPaymentProcessor($payment)
            ->getName();
    }

    public function getPaymentExternalReference(int $paymentId): ?string
    {
        if ($paymentId === 0) {
            return null;
        }

        $payment = fn_get_payment_method_data($paymentId);

        return \is_array($payment) && \array_key_exists('external_reference', $payment) ? $payment['external_reference'] : null;
    }

    public function getPaymentWithAcceptationDelay(): array
    {
        $query = <<<'SQL'
            SELECT p.payment_id, p.order_acceptation_delay
            FROM cscart_payments AS p
            WHERE p.status = 'A'
            AND p.has_acceptation_delay = 1
            AND p.order_acceptation_delay > 0
SQL;

        $payments = [];
        foreach ($this->db->fetchAll($query) as $row) {
            $payments[$row['payment_id']] = (int) $row['order_acceptation_delay'];
        }

        return $payments;
    }

    public static function getAcceptationDelay(): array
    {
        return range(0, static::MAX_ACCEPTATION_DELAY_DAYS);
    }

    /** @return PaymentProcessor|RenewCardProcessorInterface */
    public function getPaymentProcessor($payment)
    {
        $processorData = fn_get_processor_data($payment['payment_id']);
        if (empty($processorData['processor_script'])) {
            throw new \Exception('No processor_script defined for payment processor ' . $payment['processor_id']);
        }

        // Turn the file name (e.g. "mangopay.php") into a Symfony service ID
        // That avoids adding a new column in database for now
        $serviceId = 'marketplace.payment.processor.' . basename($processorData['processor_script'], '.php');
        if (!$this->container->has($serviceId)) {
            throw new \Exception(sprintf('Payment processor %s is not implemented as a service', $serviceId));
        }

        return $this->container->get($serviceId);
    }
}
