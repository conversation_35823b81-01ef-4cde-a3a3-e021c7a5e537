<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment;

use Psr\Log\LoggerInterface;
use Wizacha\Marketplace\Order\Action\CommitTo;
use Wizacha\Marketplace\Order\Action\Confirm;
use Wizacha\Marketplace\Order\Action\MarkPaymentDefermentAsAuthorized;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Payment\Processor\AbstractProcessor;
use Wizacha\Marketplace\Payment\Response\PaymentResponse;

abstract class AbstractPaymentMethod implements PaymentProcessor
{
    /** @var string */
    public const DUE_DATE = "+1 minute";

    /** LoggerInterface */
    protected $logger;

    /** @var OrderService */
    protected $orderService;

    /** @var MarkPaymentDefermentAsAuthorized */
    protected $markPaymentDefermentAsAuthorized;

    /** @var Confirm */
    protected $confirm;

    /** @var CommitTo */
    protected $commitTo;

    /** @var AbstractProcessor */
    protected $processor;

    public function __construct(
        OrderService $orderService,
        MarkPaymentDefermentAsAuthorized $markPaymentDefermentAsAuthorized,
        Confirm $confirm,
        CommitTo $commitTo,
        LoggerInterface $logger
    ) {
        $this->orderService = $orderService;
        $this->markPaymentDefermentAsAuthorized = $markPaymentDefermentAsAuthorized;
        $this->confirm = $confirm;
        $this->commitTo = $commitTo;
        $this->logger = $logger;
    }

    abstract public function startPayment(
        int $orderId,
        string $redirectUrl = null,
        string $cssUrl = null
    ): PaymentResponse;

    abstract public function getType(): PaymentType;

    abstract public function setProcessor(AbstractProcessor $processor): void;

    public function getName(): PaymentProcessorName
    {
        return $this->processor->getName();
    }

    public function isConfigured(): bool
    {
        return $this->processor->isConfigured();
    }

    public static function generateCommitmentNumber(): string
    {
        return uniqid('commitment_');
    }
}
