<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\UBO;

use MyCLabs\Enum\Enum;

/**
 * @method static Status VALIDATED()
 * @method static Status REFUSED()
 * @method static Status INCOMPLETE()
 * @method static Status VALIDATION_ASKED()
 */
class Status extends Enum
{
    private const VALIDATED = 'VALIDATED';
    private const REFUSED = 'REFUSED';
    private const INCOMPLETE = 'INCOMPLETE';
    private const VALIDATION_ASKED = 'VALIDATION_ASKED';
}
