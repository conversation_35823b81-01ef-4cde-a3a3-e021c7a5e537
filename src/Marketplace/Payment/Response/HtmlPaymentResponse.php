<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Response;

/**
 * Show an HTML page/form to the client.
 */
class HtmlPaymentResponse implements PaymentResponse
{
    /** @var string */
    private $html;
    /** @var bool */
    private $isPartial;

    public function __construct(string $html, bool $isPartial = false)
    {
        $this->html = $html;
        $this->isPartial = $isPartial;
    }

    public function getHtml(): string
    {
        return $this->html;
    }

    public function isPartial(): bool
    {
        return $this->isPartial;
    }
}
