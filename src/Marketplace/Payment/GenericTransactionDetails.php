<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment;

/**
 * Class PaymentTransaction
 *
 * Unified generic object representing a payment transaction for any payment processor.
 * Note that most properties have mixed type because the actual type depends on the processor and its SDK.
 */
class GenericTransactionDetails implements TransactionDetails
{
    /** @var mixed Transaction unique identifier */
    protected $id;

    /** @var mixed Order identifier */
    protected $orderId;

    /** @var mixed Transaction status */
    protected $status;

    /** @var mixed Payment processor raw response data */
    protected $rawData;

    /**
     * GenericTransactionDetails constructor.
     *
     * @param mixed $id
     * @param mixed $orderId
     * @param mixed $status
     * @param mixed $rawData
     */
    public function __construct($id, $orderId, $status, $rawData)
    {
        $this->id = $id;
        $this->orderId = $orderId;
        $this->status = $status;
        $this->rawData = $rawData;
    }

    /** @return mixed */
    public function getId()
    {
        return $this->id;
    }

    /** @param mixed $id */
    public function setId($id): self
    {
        $this->id = $id;

        return $this;
    }

    /** @return mixed */
    public function getOrderId()
    {
        return $this->orderId;
    }

    /** @param mixed $orderId */
    public function setOrderId($orderId): self
    {
        $this->orderId = $orderId;

        return $this;
    }

    /** @return mixed */
    public function getStatus()
    {
        return $this->status;
    }

    /** @param mixed $status */
    public function setStatus($status): self
    {
        $this->status = $status;

        return $this;
    }

    /** @return mixed */
    public function getRawData()
    {
        return $this->rawData;
    }

    /** @param mixed $rawData */
    public function setRawData($rawData): self
    {
        $this->rawData = $rawData;

        return $this;
    }
}
