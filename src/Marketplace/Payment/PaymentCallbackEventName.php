<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment;

use Wizacha\Marketplace\Payment\Event\HipayPaymentCallbackEvent;
use Wizacha\Marketplace\Payment\Event\LemonwayPaymentCallbackEvent;
use Wizacha\Marketplace\Payment\Event\MangopayPaymentCallbackEvent;
use Wizacha\Marketplace\Payment\Event\SmoneyPaymentCallbackEvent;
use Wizacha\Marketplace\Payment\Event\StripePaymentCallbackEvent;
use Wizacha\Marketplace\Payment\Exception\UnsupportedApiException;

class PaymentCallbackEventName
{
    public static function getCallbackEventName(PaymentProcessorName $processor): string
    {
        switch ($processor) {
            case PaymentProcessorName::LEMONWAY():
                return LemonwayPaymentCallbackEvent::class;
            case PaymentProcessorName::MANGOPAY():
                return MangopayPaymentCallbackEvent::class;
            case PaymentProcessorName::STRIPE():
                return StripePaymentCallbackEvent::class;
            case PaymentProcessorName::HIPAY():
                return HipayPaymentCallbackEvent::class;
            case PaymentProcessorName::SMONEY():
                return SmoneyPaymentCallbackEvent::class;
            default:
                throw new UnsupportedApiException();
        }
    }
}
