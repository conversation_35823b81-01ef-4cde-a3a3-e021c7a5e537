<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment;

use MyCLabs\Enum\Enum;

/**
 * @method static PaymentProcessorIdentifier MANGOPAY_CARD()
 * @method static PaymentProcessorIdentifier MANGOPAY_BANKWIRE()

 * @method static PaymentProcessorIdentifier LEMONWAY_CARD()
 * @method static PaymentProcessorIdentifier LEMONWAY_BANKWIRE()
 * @method static PaymentProcessorIdentifier LEMONWAY_SEPA_DIRECT_DEBIT()
 * @method static PaymentProcessorIdentifier LEMONWAY_DEFERED_SEPA_DIRECT_DEBIT()

 * @method static PaymentProcessorIdentifier HIPAY_CARD()
 * @method static PaymentProcessorIdentifier HIPAY_CAPTURE()
 * @method static PaymentProcessorIdentifier HIPAY_SEPA_DIRECT_DEBIT()
 * @method static PaymentProcessorIdentifier HIPAY_DEFERED_SEPA_DIRECT_DEBIT()

 * @method static PaymentProcessorIdentifier SMONEY_CARD()

 * @method static PaymentProcessorIdentifier STRIPE_CARD()
 * @method static PaymentProcessorIdentifier STRIPE_CAPTURE()
 * @method static PaymentProcessorIdentifier STRIPE_SEPA_DIRECT()
 * @method static PaymentProcessorIdentifier STRIPE_SEPA_DEFERMENT()

 * @method static PaymentProcessorIdentifier NO_PAYMENT()
 * @method static PaymentProcessorIdentifier OFFLINE()
 */
class PaymentProcessorIdentifier extends Enum
{
    protected const MANGOPAY_CARD = 1001;
    protected const MANGOPAY_BANKWIRE = 1004;

    protected const LEMONWAY_CARD = 1002;
    protected const LEMONWAY_BANKWIRE = 1003;
    protected const LEMONWAY_SEPA_DIRECT_DEBIT = 1015;
    protected const LEMONWAY_DEFERED_SEPA_DIRECT_DEBIT = 1016;

    protected const HIPAY_CARD = 1005;
    protected const HIPAY_CAPTURE = 1012;
    protected const HIPAY_SEPA_DIRECT_DEBIT = 1013;
    protected const HIPAY_DEFERED_SEPA_DIRECT_DEBIT = 1014;

    protected const SMONEY_CARD = 1006;

    protected const STRIPE_CARD = 1007;
    protected const STRIPE_SEPA_DIRECT = 1009;
    protected const STRIPE_SEPA_DEFERMENT = 1008;

    protected const NO_PAYMENT = 1010;
    protected const OFFLINE = 0;

    /**
     * @return string
     */
    public function getName(): string
    {
        switch ($this->getValue()) {
            case self::MANGOPAY_CARD:
            case self::MANGOPAY_BANKWIRE:
                $processorName = PaymentProcessorName::MANGOPAY()->getValue();
                break;

            case self::LEMONWAY_CARD:
            case self::LEMONWAY_BANKWIRE:
            case self::LEMONWAY_SEPA_DIRECT_DEBIT:
            case self::LEMONWAY_DEFERED_SEPA_DIRECT_DEBIT:
                $processorName = PaymentProcessorName::LEMONWAY()->getValue();
                break;

            case self::HIPAY_CARD:
            case self::HIPAY_CAPTURE:
            case self::HIPAY_SEPA_DIRECT_DEBIT:
            case self::HIPAY_DEFERED_SEPA_DIRECT_DEBIT:
                $processorName = PaymentProcessorName::HIPAY()->getValue();
                break;

            case self::SMONEY_CARD:
                $processorName = PaymentProcessorName::SMONEY()->getValue();
                break;

            case self::STRIPE_CARD:
            case self::STRIPE_SEPA_DIRECT:
            case self::STRIPE_SEPA_DEFERMENT:
                $processorName = PaymentProcessorName::STRIPE()->getValue();
                break;

            case self::NO_PAYMENT:
            case self::OFFLINE:
            default:
                $processorName = PaymentProcessorName::NONE()->getValue();
                break;
        }

        return $processorName;
    }
}
