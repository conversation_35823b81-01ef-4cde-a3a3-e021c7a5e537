<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment;

use DateTime;
use Wizacha\Marketplace\Company\Exception\LogicException;

class UserMandate
{
    private int $id;

    private int $userId;

    private ?DateTime $createdAt;

    private ?string $iban;

    private ?string $bic;

    private ?string $bankName;

    private ?string $gender;

    private ?string $firstName;

    private ?string $lastName;

    private ?int $agreementId;

    private int $processorId;

    private MandateStatus $status;

    public function __construct(int $userId, int $processorId)
    {
        $this->userId = $userId;
        $this->status = MandateStatus::DISABLED();
        $this->processorId = $processorId;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getUserId(): int
    {
        return $this->userId;
    }

    public function setUserId(int $userId): self
    {
        $this->userId = $userId;

        return $this;
    }

    public function getCreatedAt(): ?DateTime
    {
        return $this->createdAt;
    }

    public function setCreatedAt(?DateTime $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getIban(): ?string
    {
        return $this->iban;
    }

    public function setIban(?string $iban): self
    {
        if ($iban !== null) {
            $iban = $this->obfuscateIban($iban);
        }

        $this->iban = $iban;

        return $this;
    }

    public function getBic(): ?string
    {
        return $this->bic;
    }

    public function setBic(?string $bic): self
    {
        $this->bic = $bic;

        return $this;
    }

    public function getBankName(): ?string
    {
        return $this->bankName;
    }

    public function setBankName(?string $bankName): self
    {
        $this->bankName = $bankName;

        return $this;
    }

    public function getGender(): ?string
    {
        return $this->gender;
    }

    public function setGender(?string $gender): self
    {
        $this->gender = $gender;

        return $this;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(?string $firstName): self
    {
        $this->firstName = $firstName;

        return $this;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function setLastName(?string $lastName): self
    {
        $this->lastName = $lastName;

        return $this;
    }

    public function getAgreementId(): ?int
    {
        return $this->agreementId;
    }

    public function setAgreementId(?int $agreementId): self
    {
        $this->agreementId = $agreementId;

        return $this;
    }

    public function getProcessorId(): int
    {
        return $this->processorId;
    }

    public function setProcessorId(int $processorId): self
    {
        $this->processorId = $processorId;

        return $this;
    }

    public function getStatus(): MandateStatus
    {
        return $this->status;
    }

    public function setStatus(MandateStatus $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function setMandateInfo(array $data): self
    {
        $this->setCreatedAt(new \DateTime('NOW'));
        $this->setIban($data['iban']);
        $this->setBic($data['bic']);
        $this->setBankName($data['bankName']);
        $this->setGender($data['gender']);
        $this->setFirstName($data['firstName']);
        $this->setLastName($data['lastName']);

        return $this;
    }

    public function expose(): array
    {
        return [
            'createdAt' => $this->getCreatedAt()->format(\DateTime::RFC3339),
            'iban' => $this->getIban(),
            'issuerBankId' => $this->getBic(),
            'bankName' => $this->getBankName(),
            'gender' => $this->getGender(),
            'firstName' => $this->getFirstName(),
            'lastName' => $this->getLastName(),
        ];
    }

    private function obfuscateIban(string $iban, string $replacementChar = '*', int $limit = 4): string
    {
        if (\mb_strlen($iban) < $limit * 2) {
            throw new LogicException("can't obfuscate string");
        }

        $replacementString = \str_repeat($replacementChar, \mb_strlen($iban) - $limit * 2);

        return \substr_replace($iban, $replacementString, $limit, -$limit);
    }
}
