<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment;

use Doctrine\ORM\EntityManagerInterface;

class UserPaymentInfoService
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    /**
     * Returns the Mangopay ID for a marketplace user if it exists.
     *
     * @param int $userId Marketplace user ID
     * @return null|string ID of a Mangopay User, or null if the Mangopay user doesn't exist
     */
    public function getMangopayUserId(int $userId)
    {
        $userPaymentInfo = $this->getUserPaymentInfo($userId);

        return $userPaymentInfo ? $userPaymentInfo->getMangopayUserId() : null;
    }

    /**
     * Link a marketplace user to a Mangopay user.
     */
    public function setMangopayUserId(int $userId, string $mangopayUserId)
    {
        $userPaymentInfo = $this->getUserPaymentInfo($userId) ?? new UserPaymentInfo($userId);

        $userPaymentInfo->setMangopayUserId($mangopayUserId);
        $this->entityManager->persist($userPaymentInfo);
        $this->entityManager->flush();
    }

    /**
     * Returns the Bank Account Token for a marketplace user if it exists.
     */
    public function getStripeBankAccountToken(int $userId): ?string
    {
        $userPaymentInfo = $this->getUserPaymentInfo($userId);

        return $userPaymentInfo ? $userPaymentInfo->getStripeBankAccountToken() : null;
    }

    /**
     * Link a marketplace user to a Stripe Bank Account
     */
    public function setStripeBankAccountToken(int $userId, string $stripeBankAccountToken)
    {
        $userPaymentInfo = $this->getUserPaymentInfo($userId) ?? new UserPaymentInfo($userId);

        $userPaymentInfo->setStripeBankAccountToken($stripeBankAccountToken);
        $this->entityManager->persist($userPaymentInfo);
        $this->entityManager->flush();
    }

    /**
     * Returns the Customer Id for a marketplace user if it exists.
     */
    public function getStripeCustomerId(int $userId): ?string
    {
        $userPaymentInfo = $this->getUserPaymentInfo($userId);

        return $userPaymentInfo ? $userPaymentInfo->getStripeCustomerId() : null;
    }

    /**
     * Link a marketplace user to a Stripe Customer Id
     */
    public function setStripeCustomerId(int $userId, string $stripeCustomerId): void
    {
        $userPaymentInfo = $this->getUserPaymentInfo($userId) ?? new UserPaymentInfo($userId);

        $userPaymentInfo->setStripeCustomerId($stripeCustomerId);
        $this->entityManager->persist($userPaymentInfo);
        $this->entityManager->flush();
    }

    public function getHipaySepaAgreement(int $userId): ?int
    {
        $userPaymentInfo = $this->getUserPaymentInfo($userId);

        return (false === \is_null($userPaymentInfo))
            ? $userPaymentInfo->getHipaySepaAgreement()
            : null;
    }

    public function setHipaySepaAgreement(int $userId, int $hipaySepaAgreement, bool $flush = true): self
    {
        $userPaymentInfo = $this->getUserPaymentInfo($userId) ?? new UserPaymentInfo($userId);

        $userPaymentInfo->setHipaySepaAgreement($hipaySepaAgreement);
        $this->entityManager->persist($userPaymentInfo);

        if (true === $flush) {
            $this->entityManager->flush();
        }

        return $this;
    }

    public function getUserIdFromStripeCustomerId(string $custumerId): ?int
    {
        $data = $this
            ->entityManager
            ->createQueryBuilder()
            ->select('userPaymentInfo.userId')
            ->from(UserPaymentInfo::class, 'userPaymentInfo')
            ->where('userPaymentInfo.stripeCustomerId = :custumerId')
            ->setParameter('custumerId', $custumerId)
            ->getQuery()
            ->getOneOrNullResult()
        ;

        return \is_array($data) && \array_key_exists('userId', $data) ? $data['userId'] : null;
    }

    private function getUserPaymentInfo(int $userId): ?UserPaymentInfo
    {
        return $this->entityManager->find(UserPaymentInfo::class, $userId);
    }

    public function getLemonwaySepaAgreement(int $userId): ?int
    {
        $userPaymentInfo = $this->getUserPaymentInfo($userId);

        return ($userPaymentInfo instanceof UserPaymentInfo)
            ? $userPaymentInfo->getLemonwaySepaAgreement()
            : null;
    }

    public function getLemonwayElectronicSignature(int $userId): ?string
    {
        $userPaymentInfo = $this->getUserPaymentInfo($userId);

        return ($userPaymentInfo instanceof UserPaymentInfo)
            ? $userPaymentInfo->getLemonwayElectronicSignature()
            : null;
    }
}
