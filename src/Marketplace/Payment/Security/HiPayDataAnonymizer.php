<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Security;

use Wizacha\Marketplace\Security\DataAnonymizer\DataAnonymizer;

class HiPayDataAnonymizer extends DataAnonymizer
{
    public function __construct()
    {
        $this->dataToAnonymize = [
            'cardtoken' => self::TOKEN_TYPE,
            'threeDSecure' => [
                'authenticationToken' => self::TOKEN_TYPE,
            ],
            'paymentMethod' => [
                'token' => self::TOKEN_TYPE,
                'cardHolder' => self::PERSONAL_DATA_TYPE,
            ],
            'transaction' => [
                'threeDSecure' => [
                    'authenticationToken' => self::TOKEN_TYPE,
                ],
                'paymentMethod' => [
                    'token' => self::TOKEN_TYPE,
                    'cardHolder' => self::PERSONAL_DATA_TYPE,
                ],
                'order' => [
                    'email' => self::PERSONAL_DATA_TYPE,
                    'firstname' => self::PERSONAL_DATA_TYPE,
                    'lastname' => self::PERSONAL_DATA_TYPE,
                    'streetAddress' => self::PERSONAL_DATA_TYPE,
                    'shippingAddress' => [
                        'firstname' => self::PERSONAL_DATA_TYPE,
                        'lastname' => self::PERSONAL_DATA_TYPE,
                        'streetAddress' => self::PERSONAL_DATA_TYPE,
                    ],
                ],
                'ipAddress' => self::PERSONAL_DATA_TYPE,
            ],
            'credential[wslogin]' => self::PERSONAL_DATA_TYPE,
            'credential[wspassword]' => self::COMPLETE_ANONYMIZE_TYPE,
            'wslogin' => self::PERSONAL_DATA_TYPE,
            'wspassword' => self::COMPLETE_ANONYMIZE_TYPE,
            'iban' => self::IBAN_TYPE,
            'debit_agreement' => [
                'properties' => [
                    'iban' => self::IBAN_TYPE,
                    'firstname' => self::PERSONAL_DATA_TYPE,
                    'lastname' => self::PERSONAL_DATA_TYPE,
                ],
            ],
            'email' => self::PERSONAL_DATA_TYPE,
            'user_email' => self::PERSONAL_DATA_TYPE,
            'firstname' => self::PERSONAL_DATA_TYPE,
            'lastname' => self::PERSONAL_DATA_TYPE,
            'address[address]' => self::PERSONAL_DATA_TYPE,
            'streetaddress' => self::PERSONAL_DATA_TYPE,
            'streetaddress2' => self::PERSONAL_DATA_TYPE,
            'shipto_firstname' => self::PERSONAL_DATA_TYPE,
            'shipto_lastname' => self::PERSONAL_DATA_TYPE,
            'shipto_streetaddress' => self::PERSONAL_DATA_TYPE,
            'shipto_streetaddress2' => self::PERSONAL_DATA_TYPE,
            'merchant_risk_statement' => [
                'email_delivery_address' => self::PERSONAL_DATA_TYPE,
            ],
            'order' => [
                'email' => self::PERSONAL_DATA_TYPE,
            ],
            'acct_num' => self::IBAN_TYPE,
            'recipient_username' => self::PERSONAL_DATA_TYPE,
            'ipAddress' => self::PERSONAL_DATA_TYPE,
        ];
    }
}
