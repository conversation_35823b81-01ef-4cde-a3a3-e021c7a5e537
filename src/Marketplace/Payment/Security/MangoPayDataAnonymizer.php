<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Security;

use Wizacha\Marketplace\Security\DataAnonymizer\DataAnonymizer;

class MangoPayDataAnonymizer extends DataAnonymizer
{
    public function __construct()
    {
        $bankAccount = [
            'IBAN' => self::IBAN_TYPE,
        ];

        $this->dataToAnonymize = [
            $bankAccount,
            'BankAccount' => $bankAccount,
            'IBAN' => self::IBAN_TYPE,
            'Billing' => [
                'FirstName' => self::PERSONAL_DATA_TYPE,
                'LastName' => self::PERSONAL_DATA_TYPE,
                'Address' => [
                    'AddressLine1' => self::PERSONAL_DATA_TYPE,
                    'AddressLine2' => self::PERSONAL_DATA_TYPE,
                ],
            ],
            'Shipping' => [
                'FirstName' => self::PERSONAL_DATA_TYPE,
                'LastName' => self::PERSONAL_DATA_TYPE,
                'Address' => [
                    'AddressLine1' => self::PERSONAL_DATA_TYPE,
                    'AddressLine2' => self::PERSONAL_DATA_TYPE,
                ],
            ],
            'AccountNumber' => self::IBAN_TYPE,
            'FirstName' => self::PERSONAL_DATA_TYPE,
            'LastName' => self::PERSONAL_DATA_TYPE,
            'Email' => self::PERSONAL_DATA_TYPE,
            'Address' => [
                'AddressLine1' => self::PERSONAL_DATA_TYPE,
                'AddressLine2' => self::PERSONAL_DATA_TYPE,
            ],
            'LegalRepresentativeFirstName' => self::PERSONAL_DATA_TYPE,
            'LegalRepresentativeLastName' => self::PERSONAL_DATA_TYPE,
            'LegalRepresentativeEmail' => self::PERSONAL_DATA_TYPE,
            'LegalRepresentativeAddress' => [
                'AddressLine1' => self::PERSONAL_DATA_TYPE,
                'AddressLine2' => self::PERSONAL_DATA_TYPE,
            ],
        ];

        $this->contextToAnonymize = [
            'payin' => [
                'PaymentDetails' => [
                    'BankAccount' => [
                        'Details' => [
                            'IBAN' => self::IBAN_TYPE,
                        ],
                    ],
                ],
                'ExecutionDetails' => [
                    'Billing' => [
                        'Address' => [
                            'AddressLine1' => self::PERSONAL_DATA_TYPE,
                            'AddressLine2' => self::PERSONAL_DATA_TYPE,
                        ],
                    ],
                ],
            ],
            'data' => [
                'payin' => [
                    'PaymentDetails' => [
                        'BankAccount' => [
                            'Details' => [
                                'IBAN' => self::IBAN_TYPE,
                            ],
                        ],
                    ],
                    'ExecutionDetails' => [
                        'Billing' => [
                            'Address' => [
                                'AddressLine1' => self::PERSONAL_DATA_TYPE,
                                'AddressLine2' => self::PERSONAL_DATA_TYPE,
                            ],
                        ],
                    ],
                ],
            ],
            'bankAccount' => [
                'Details' => [
                    'IBAN' => self::IBAN_TYPE,
                ],
            ],
            'mangopayUser' => [
                'FirstName' => self::PERSONAL_DATA_TYPE,
                'LastName' => self::PERSONAL_DATA_TYPE,
                'Email' => self::PERSONAL_DATA_TYPE,
                'LegalRepresentativeFirstName' => self::PERSONAL_DATA_TYPE,
                'LegalRepresentativeLastName' => self::PERSONAL_DATA_TYPE,
                'LegalRepresentativeEmail' => self::PERSONAL_DATA_TYPE,
                'LegalRepresentativeAddress' => [
                    'AddressLine1' => self::PERSONAL_DATA_TYPE,
                    'AddressLine2' => self::PERSONAL_DATA_TYPE,
                ],
            ]
        ];
    }
}
