<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Security;

use Wizacha\Marketplace\Security\DataAnonymizer\DataAnonymizer;

class LemonWayDataAnonymizer extends DataAnonymizer
{
    public function __construct()
    {
        $hpay = [
            'MLABEL' => self::IBAN_TYPE,
            'MTOKEN' => self::TOKEN_TYPE,
        ];

        $ibanOrSDDMandate = [
            'DATA' => self::IBAN_TYPE,
            'HOLDER' => self::FULL_NAME_TYPE,
        ];

        $this->dataToAnonymize = [
            'p' => [
                'buffer' => self::LONG_TEXT_TYPE,
                'clientMail' => self::PERSONAL_DATA_TYPE,
                'clientFirstName' => self::PERSONAL_DATA_TYPE,
                'clientLastName' => self::PERSONAL_DATA_TYPE,
                'email' => self::PERSONAL_DATA_TYPE,
                'holder' => self::FULL_NAME_TYPE,
                'iban' => self::IBAN_TYPE,
                'mobileNumber' => self::PERSONAL_DATA_TYPE,
                'newEmail' => self::PERSONAL_DATA_TYPE,
                'newFirstName' => self::PERSONAL_DATA_TYPE,
                'newLastName' => self::PERSONAL_DATA_TYPE,
                'newStreet' => self::PERSONAL_DATA_TYPE,
                'street' => self::PERSONAL_DATA_TYPE,
                'transactionMerchantToken' => self::TOKEN_TYPE,
                'walletIp' => self::PERSONAL_DATA_TYPE,
                'wkToken' => self::TOKEN_TYPE,
                'wlLogin' => self::PERSONAL_DATA_TYPE,
                'wlPass' => self::COMPLETE_ANONYMIZE_TYPE,
            ],
            'd' => [
                'CANCELMONEYIN' => [
                    'HPAY' => $hpay,
                ],
                'MLABEL' => self::IBAN_TYPE,
                'MONEYINVALIDATE' => [
                    'HPAY' => $hpay,
                ],
                'MONEYINWEB' => [
                    'TOKEN' => self::TOKEN_TYPE,
                ],
                'SIGNDOCUMENT' => [
                    'TOKEN' => self::TOKEN_TYPE,
                ],
                'TRANS' => [
                    'HPAY' => array_merge($hpay, [$hpay]),
                ],
                'TRANS_SENDPAYMENT' => [
                    'HPAY' => $hpay,
                ],
                'WALLET' => [
                    'EMAIL' => self::PERSONAL_DATA_TYPE,
                    'FirstName' => self::PERSONAL_DATA_TYPE,
                    'IBANS' => [$ibanOrSDDMandate],
                    'LastName' => self::PERSONAL_DATA_TYPE,
                    'MobileNumber' => self::PERSONAL_DATA_TYPE,
                    'NAME' => self::FULL_NAME_TYPE,
                    'PhoneNumber' => self::PERSONAL_DATA_TYPE,
                    'SDDMANDATES' => [$ibanOrSDDMandate],
                    'Street' => self::PERSONAL_DATA_TYPE,
                ],
            ],
        ];

        $this->contextToAnonymize = [
            'curl_info' => [
                'primary_ip' => self::PERSONAL_DATA_TYPE,
                'local_ip' => self::PERSONAL_DATA_TYPE,
            ],
            'proxy' => [
                'host' => self::PERSONAL_DATA_TYPE,
                'port' => self::COMPLETE_ANONYMIZE_TYPE,
                'user' => self::PERSONAL_DATA_TYPE,
                'passwd' => self::COMPLETE_ANONYMIZE_TYPE,
            ],
        ];
    }
}
