<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\Security;

use Wizacha\Marketplace\Security\DataAnonymizer\DataAnonymizer;

class StripeDataAnonymizer extends DataAnonymizer
{
    public function __construct()
    {
        $addressDataToAnonymize = [
            'line1' => self::PERSONAL_DATA_TYPE,
            'line2' => self::PERSONAL_DATA_TYPE,
        ];

        $billingDetailsDataToAnonymize = [
            'address' => $addressDataToAnonymize,
            'email' => self::PERSONAL_DATA_TYPE,
            'name' => self::FULL_NAME_TYPE,
            'phone' => self::PERSONAL_DATA_TYPE,
        ];

        $walletDataToAnonymize = [
            'billing_address' => $addressDataToAnonymize,
            'email' => self::PERSONAL_DATA_TYPE,
            'name' => self::FULL_NAME_TYPE,
            'shipping_address' => $addressDataToAnonymize,
        ];

        $cardDataToAnonymize = [
            'fingerprint' => self::TOKEN_TYPE,
            'wallet' => [
                'masterpass' => $walletDataToAnonymize,
                'visa_checkout' => $walletDataToAnonymize,
            ],
        ];

        $shippingDataToAnonymize = [
            'address' => $addressDataToAnonymize,
            'name' => self::FULL_NAME_TYPE,
            'phone' => self::PERSONAL_DATA_TYPE,
        ];

        $ownerDataToAnonymize = [
            'address' => $addressDataToAnonymize,
            'email' => self::PERSONAL_DATA_TYPE,
            'name' => self::FULL_NAME_TYPE,
            'phone' => self::PERSONAL_DATA_TYPE,
            'verified_address' => $addressDataToAnonymize,
            'verified_email' => self::PERSONAL_DATA_TYPE,
            'verified_name' => self::FULL_NAME_TYPE,
            'verified_phone' => self::PERSONAL_DATA_TYPE,
        ];

        $sourceOrderDataToAnonymize = [
            'email' => self::PERSONAL_DATA_TYPE,
            'phone' => self::PERSONAL_DATA_TYPE,
            'shipping' => $shippingDataToAnonymize,
            'items' => [['description' => self::LONG_TEXT_TYPE]],
        ];

        $sourceDataToAnonymize = [
            'client_secret' => self::TOKEN_TYPE,
            'owner' => $ownerDataToAnonymize,
            'sepa_debit' => [
                'fingerprint' => self::TOKEN_TYPE,
            ],
            'source_order' => $sourceOrderDataToAnonymize,
        ];

        $chargeDataToAnonymize = [
            'description' => self::LONG_TEXT_TYPE,
            'billing_details' => $billingDetailsDataToAnonymize,
            'payment_method_details' => [
                'card' => $cardDataToAnonymize,
                'sepa_debit' => [
                    'fingerprint' => self::TOKEN_TYPE,
                ],
            ],
            'receipt_email' => self::PERSONAL_DATA_TYPE,
            'shipping' => $shippingDataToAnonymize,
            'refunds' => [
                'data' => [['description' => self::LONG_TEXT_TYPE]],
            ],
            'source' => $sourceDataToAnonymize,
        ];

        $this->dataToAnonymize = [
            'address' => $addressDataToAnonymize,
            'account_token' => self::TOKEN_TYPE,
            'billing_details' => $billingDetailsDataToAnonymize,
            'card' => $cardDataToAnonymize,
            'charges' => [
                'data' => [$chargeDataToAnonymize],
            ],
            'client_secret' => self::TOKEN_TYPE,
            'customer_details' => [
                'email' => self::PERSONAL_DATA_TYPE,
                'phone' => self::PERSONAL_DATA_TYPE,
            ],
            'customer_email' => self::PERSONAL_DATA_TYPE,
            'data' => [$chargeDataToAnonymize],
            'description' => self::LONG_TEXT_TYPE,
            'email' => self::PERSONAL_DATA_TYPE,
            'external_account' => [
                'account_number' => self::IBAN_TYPE,
            ],
            'external_accounts' => [
                'data' => [
                    [
                        'address_line_1' => self::PERSONAL_DATA_TYPE,
                        'address_line_2' => self::PERSONAL_DATA_TYPE,
                        'fingerprint' => self::TOKEN_TYPE,
                        'name' => self::FULL_NAME_TYPE,
                    ],
                ],
            ],
            'first_name' => self::PERSONAL_DATA_TYPE,
            'full_name_aliases' => self::FULL_NAME_TYPE,
            'individual' => [
                'address' => $addressDataToAnonymize,
                'email' => self::PERSONAL_DATA_TYPE,
                'first_name' => self::PERSONAL_DATA_TYPE,
                'full_name_aliases' => self::FULL_NAME_TYPE,
                'last_name' => self::PERSONAL_DATA_TYPE,
                'phone' => self::PERSONAL_DATA_TYPE,
            ],
            'last_name' => self::PERSONAL_DATA_TYPE,
            'maiden_name' => self::PERSONAL_DATA_TYPE,
            'name' => self::FULL_NAME_TYPE,
            'owner' => $ownerDataToAnonymize,
            'payment_method_details' => [
                'card' => $cardDataToAnonymize,
                'sepa_debit' => [
                    'fingerprint' => self::TOKEN_TYPE,
                ],
            ],
            'person_token' => self::TOKEN_TYPE,
            'phone' => self::PERSONAL_DATA_TYPE,
            'receipt_email' => self::PERSONAL_DATA_TYPE,
            'refunds' => [
                'data' => [['description' => self::LONG_TEXT_TYPE]],
            ],
            'sepa_debit' => [
                'fingerprint' => self::TOKEN_TYPE,
            ],
            'sepa_fingerprint' => self::TOKEN_TYPE,
            'shipping' => $shippingDataToAnonymize,
            'source' => [
                '#or' => [
                    self::TOKEN_TYPE,
                    $sourceDataToAnonymize,
                ],
            ],
            'source_order' => $sourceOrderDataToAnonymize,
            'sources' => [
                'data' => [$sourceDataToAnonymize],
            ],
            'tos_acceptance' => [
                'ip' => self::PERSONAL_DATA_TYPE,
            ],
        ];
    }
}
