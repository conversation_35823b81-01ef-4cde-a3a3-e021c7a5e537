<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\HiPay;

use Wizacha\Marketplace\Payment\PaymentType;
use Wizacha\Marketplace\Payment\Response\NoPaymentResponse;
use Wizacha\Marketplace\Payment\Response\PaymentResponse;
use Wizacha\Marketplace\Order\OrderStatus;

final class HipaySepaDirectProcessor extends AbstractHipayPaymentMethod
{
    public function startPayment(
        int $orderId,
        string $redirectUrl = null,
        string $cssUrl = null
    ): PaymentResponse {
        $orders = $this->orderService->getChildOrders($orderId);

        foreach ($orders as $order) {
            if (true === $this->markPaymentDefermentAsAuthorized->isAllowed($order)) {
                $this->markPaymentDefermentAsAuthorized->execute($order);
            }

            if (true === $this->confirm->isAllowed($order)) {
                $this->confirm->execute($order);
            }

            if (true === $this->commitTo->isAllowed($order)) {
                $this->commitTo->execute(
                    $order,
                    new \DateTimeImmutable(static::DUE_DATE),
                    static::generateCommitmentNumber()
                );

                fn_change_order_status(
                    $order->getId(),
                    (string) $order->deduceLegacyStatus(),
                    (string) OrderStatus::INCOMPLETED()
                );
            }
        }
        try {
            $this->processor->createSepaTransaction($orderId);
        } catch (\Throwable $e) {
            $this->logger->error(
                '[Hipay SEPA Direct] Exception in SEPA start payment: ' . $e->getMessage(),
                [
                    'exception' => $e,
                    'data' => [
                        'orderId' => $orderId,
                    ],
                ]
            );
        }

        return new NoPaymentResponse();
    }

    public function getType(): PaymentType
    {
        return PaymentType::SEPA_DIRECT();
    }
}
