<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\HiPay;

use Wizacha\Marketplace\Payment\PaymentProcessor;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\PaymentType;
use Wizacha\Marketplace\Payment\Processor\HiPay;
use Wizacha\Marketplace\Payment\RenewCardProcessorInterface;
use Wizacha\Marketplace\Payment\Response\PaymentResponse;
use Wizacha\Marketplace\Payment\Response\RedirectPaymentResponse;
use Wizacha\Marketplace\User\User;

class HiPayCardProcessor implements PaymentProcessor, RenewCardProcessorInterface
{
    /**
     * @var HiPay
     */
    private $hipay;

    public function __construct(HiPay $hipay)
    {
        $this->hipay = $hipay;
    }

    public function startPayment(int $orderId, string $redirectUrl = null, string $cssUrl = null): PaymentResponse
    {
        $url = $this->hipay->createCardTransaction($orderId, $redirectUrl, $cssUrl);

        return new RedirectPaymentResponse($url);
    }

    /**
     * Renew a credit card.
     *
     * The response returned will enable the user to renew the credit card.
     */
    public function renewCreditCard(User $user, string $redirectUrl, string $cssUrl = null): RedirectPaymentResponse
    {
        return new RedirectPaymentResponse(
            $this->hipay->renewCardTransaction($user, $redirectUrl, $cssUrl)
        );
    }

    public function getType(): PaymentType
    {
        return PaymentType::CREDIT_CARD();
    }

    public function getName(): PaymentProcessorName
    {
        return PaymentProcessorName::HIPAY();
    }

    public function isConfigured(): bool
    {
        return $this->hipay->isConfigured();
    }
}
