<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\HiPay;

use HiPay\Fullservice\HTTP\Response\Response;
use Wizacha\Marketplace\Payment\AbstractPaymentLogger;

/**
 * This logger is used in HiPayHTTPClient for every API call.
 *
 * @see HiPayHTTPClient::doRequest()
 */
class HiPayLogger extends AbstractPaymentLogger
{
    private const HIPAY_LOG_NAME = 'HiPay API SDK';

    public function requestSuccess(?string $method, ?string $endpoint, array $params, Response $response): void
    {
        $this->logRequestSuccess(
            self::HIPAY_LOG_NAME . ' Request Success',
            $method,
            $endpoint,
            $params,
            $response->getStatusCode(),
            $response->getBody()
        );
    }

    public function requestException(?string $method, ?string $endpoint, array $params, \Throwable $exception): void
    {
        $this->logRequestException(self::HIPAY_LOG_NAME . ' Error', $method, $endpoint, $params, $exception);
    }
}
