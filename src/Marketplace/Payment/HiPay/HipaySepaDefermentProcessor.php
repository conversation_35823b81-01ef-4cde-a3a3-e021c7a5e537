<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\HiPay;

use Wizacha\Marketplace\Payment\PaymentType;
use Wizacha\Marketplace\Payment\Response\NoPaymentResponse;
use Wizacha\Marketplace\Payment\Response\PaymentResponse;

final class HipaySepaDefermentProcessor extends AbstractHipayPaymentMethod
{
    public function startPayment(
        int $orderId,
        string $redirectUrl = null,
        string $cssUrl = null
    ): PaymentResponse {
        $orders = $this->orderService->getChildOrders($orderId);

        foreach ($orders as $order) {
            if (true === $this->markPaymentDefermentAsAuthorized->isAllowed($order)) {
                $this->markPaymentDefermentAsAuthorized->execute($order);
            }

            if (true === $this->confirm->isAllowed($order)) {
                $this->confirm->execute($order);
            }
        }

        return new NoPaymentResponse();
    }

    public function getType(): PaymentType
    {
        return PaymentType::PAYMENT_DEFERMENT();
    }
}
