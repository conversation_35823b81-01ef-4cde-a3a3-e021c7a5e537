<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\HiPay;

use Wizacha\Marketplace\Payment\PaymentProcessor;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\PaymentType;
use Wizacha\Marketplace\Payment\Processor\HiPay;
use Wizacha\Marketplace\Payment\Response\PaymentResponse;
use Wizacha\Marketplace\Payment\Response\RedirectPaymentResponse;

class HiPayCardCaptureProcessor implements PaymentProcessor
{
    /**
     * @var HiPay
     */
    private $hipay;

    public function __construct(HiPay $hipay)
    {
        $this->hipay = $hipay;
    }

    public function startPayment(int $orderId, string $redirectUrl = null, string $cssUrl = null): PaymentResponse
    {
        $url = $this->hipay->createCardTransaction($orderId, $redirectUrl, $cssUrl, false);

        return new RedirectPaymentResponse($url);
    }

    public function getType(): PaymentType
    {
        return PaymentType::CREDIT_CARD_CAPTURE();
    }

    public function getName(): PaymentProcessorName
    {
        return PaymentProcessorName::HIPAY();
    }

    public function isConfigured(): bool
    {
        return $this->hipay->isConfigured();
    }
}
