<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\HiPay;

use Wizacha\Marketplace\Payment\AbstractPaymentMethod;
use Wizacha\Marketplace\Payment\Processor\AbstractProcessor;
use Wizacha\Marketplace\Payment\Stripe\BankAccountIsMissing;

abstract class AbstractHipayPaymentMethod extends AbstractPaymentMethod
{
    public function setProcessor(AbstractProcessor $processor): void
    {
        $this->processor = $processor;
    }
}
