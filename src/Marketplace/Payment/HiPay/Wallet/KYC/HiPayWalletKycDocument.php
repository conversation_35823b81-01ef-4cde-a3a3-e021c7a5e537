<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\HiPay\Wallet\KYC;

use Wizacha\Marketplace\Payment\KYC\Status;

class HiPayWalletKycDocument
{
    /** @var string */
    protected $label;

    /** @var int */
    protected $type;

    /** @var Status */
    protected $status;

    /** @var string */
    protected $message;

    public function __construct(string $label, int $type, Status $status, string $message)
    {
        $this->label = $label;
        $this->type = $type;
        $this->status = $status;
        $this->message = $message;
    }

    public function __toString(): string
    {
        return $this->getLabel() . '_' . $this->getType() . '_' . $this->getStatus();
    }

    public function getLabel(): string
    {
        return $this->label;
    }

    public function setLabel(string $label): self
    {
        $this->label = $label;

        return $this;
    }

    public function getType(): int
    {
        return $this->type;
    }

    public function setType(int $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getStatus(): Status
    {
        return $this->status;
    }

    public function setStatus(Status $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function setMessage(string $message): self
    {
        $this->message = $message;

        return $this;
    }
}
