<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\HiPay\Wallet\KYC;

use Wizacha\Marketplace\Payment\KYC\Status;

class HiPayWalletKycReasons
{
    /** @var int[] */
    protected $documentCount;

    /** @var string[] */
    protected $refusedReasons = [];

    public function __construct()
    {
        $this->documentCount = [
            Status::MISSING()->getValue() => 0,
            Status::REFUSED()->getValue() => 0,
            Status::VALIDATED()->getValue() => 0,
            Status::WAITING()->getValue() => 0,
        ];
    }

    public function addDocumentStatus(Status $status): self
    {
        $this->documentCount[$status->getValue()]++;

        return $this;
    }

    public function addRefusedReason(HiPayWalletKycDocument $kycDocument): self
    {
        $this->refusedReasons[] = $kycDocument->getLabel() . ': ' . $kycDocument->getMessage();
        $this->addDocumentStatus(Status::REFUSED());

        return $this;
    }

    public function countDocumentsStatus(Status $status): int
    {
        return $this->documentCount[$status->getValue()];
    }

    /** @return string[] */
    public function getRefusedReasons(): array
    {
        return $this->refusedReasons;
    }

    public function getBadStatusCount(): int
    {
        return $this->countDocumentsStatus(Status::REFUSED()) + $this->countDocumentsStatus(Status::MISSING());
    }
}
