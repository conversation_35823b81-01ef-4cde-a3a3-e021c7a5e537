<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\HiPay\Wallet\KYC;

use Wizacha\Marketplace\Payment\HiPay\Wallet\HiPayResponseStatusCode;
use Wizacha\Marketplace\Payment\HiPay\Wallet\HiPayWalletLogger;
use Wizacha\Marketplace\Payment\KYC\FileTypes;
use Wizacha\Marketplace\Payment\KYC\Status;
use Wizacha\Marketplace\Payment\AbstractPaymentLogger;
use Wizacha\Storage\StorageService;

class HiPayWalletKyc
{
    protected int $companyId;

    /** @var HiPayWalletKycDocument[] */
    protected $existingDocuments = [];

    /** @var string[] */
    protected $fileTypes;

    protected ?StorageService $storage;

    protected string $storagePrefix;

    private HiPayWalletLogger $hiPayWalletLogger;

    /** @param array[] $documentsFromResponse */
    public function __construct(
        int $companyId,
        array $documentsFromResponse,
        StorageService $storage,
        HiPayWalletLogger $paymentLoggerService
    ) {
        $this->companyId = $companyId;
        $this->storagePrefix = $companyId . '/';
        $this->fileTypes = FileTypes::defineFileTypes();
        $this->fillExistingDocuments($documentsFromResponse);
        $this->setStorage($storage);
        $this->hiPayWalletLogger = $paymentLoggerService;
    }

    /** @return string[] */
    public function getFileTypes(): array
    {
        return $this->fileTypes;
    }

    public function setRibFromResponse(int $statusCode, string $status, string $message = null): void
    {
        $this->addExistingDocument(new HiPayWalletKycDocument(
            $this->getFileTypes()[FileTypes::RIB()->getValue()],
            FileTypes::RIB()->getValue(),
            $statusCode === HiPayResponseStatusCode::BANK_INFORMATION_VALIDATED()->getValue()
                ? Status::fromHiPayAPI($status)
                : Status::MISSING(),
            $message ?? ''
        ));
    }

    /** @return resource[]|resource */
    public function getDocumentsToUpload($bankInformations = false)
    {
        $documentsToUpload = [];

        foreach ($this->getFileTypes() as $type => $typeLabel) {
            $kycDocument = $this->getDocumentFromType($type);
            if (\is_null($kycDocument) === true) {
                continue;
            }

            foreach ($this->getStorage()->getList($this->getStoragePrefix()) as $documentInStorage) {
                if (\strpos($documentInStorage, 'w_' . $typeLabel) === 0) {
                    if (true === $bankInformations && $type === FileTypes::RIB()->getValue()) {
                        return $this->getStorage()->readStream($this->getStoragePrefix() . $documentInStorage);
                    }

                    if ($kycDocument->getStatus()->equals(Status::MISSING()) === true
                        || $kycDocument->getStatus()->equals(Status::REFUSED()) === true
                    ) {
                        $documentsToUpload[$type] = $this
                            ->getStorage()
                            ->readStream($this->getStoragePrefix() . $documentInStorage)
                        ;
                        break;
                    }
                }
            }
        }

        return $documentsToUpload;
    }

    public function getDocumentFromType(int $type): ?HiPayWalletKycDocument
    {
        foreach ($this->getExistingDocuments() as $existingDocument) {
            /** @var HiPayWalletKycDocument $existingDocument */
            if ($existingDocument->getType() === $type) {
                return $existingDocument;
            }
        }

        return null;
    }

    /** @param HiPayWalletKycDocument[] $documents */
    public function checkKyc(array $documents): HiPayWalletKycReasons
    {
        $kycReasons = new HiPayWalletKycReasons();

        /** @var HiPayWalletKycDocument $kycDocument */
        foreach ($documents as $kycDocument) {
            if ($kycDocument->getStatus()->equals(Status::REFUSED())) {
                $kycReasons->addRefusedReason($kycDocument);
            } elseif ($kycDocument->getStatus()->equals(Status::MISSING())) {
                $kycReasons->addDocumentStatus(Status::MISSING());
            } elseif ($kycDocument->getStatus()->equals(Status::WAITING())) {
                $kycReasons->addDocumentStatus(Status::WAITING());
            } else {
                $kycReasons->addDocumentStatus(Status::VALIDATED());
            }
        }

        return $kycReasons;
    }

    public function setStorage(StorageService $storage): self
    {
        $this->storage = $storage;

        return $this;
    }

    public function addExistingDocument(HiPayWalletKycDocument $document): self
    {
        $this->existingDocuments[] = $document;

        return $this;
    }

    public function getStorage(): StorageService
    {
        return $this->storage;
    }

    /** @return HiPayWalletKycDocument[] */
    public function getExistingDocuments(): array
    {
        return $this->existingDocuments;
    }

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    public function getStoragePrefix(): string
    {
        return $this->storagePrefix;
    }

    /** @param array[] $documents */
    protected function fillExistingDocuments(array $documents): void
    {
        foreach ($documents['documents'] as $kyc) {
            if (\array_key_exists($kyc['type'], $this->getFileTypes()) === false) {
                $this->hiPayWalletLogger->log(
                    AbstractPaymentLogger::ERROR_LEVEL,
                    'HiPayWalletKycDocument File type does not exist',
                    ['fileType' => $kyc['type']]
                );

                continue;
            }

            $this->addExistingDocument(new HiPayWalletKycDocument(
                $this->getFileTypes()[$kyc['type']],
                $kyc['type'],
                Status::fromHiPayAPI($kyc['status_label']),
                $kyc['message'] ?? ''
            ));
        }
    }
}
