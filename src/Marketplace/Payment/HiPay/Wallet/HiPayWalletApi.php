<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Payment\HiPay\Wallet;

use GuzzleHttp\Client;
use GuzzleHttp\ClientInterface;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\RequestOptions;
use Psr\Http\Message\StreamInterface;
use Wizacha\Company as WizachaCompany;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Payment\Exception\CannotUpdateCompanyException;
use Wizacha\Marketplace\Payment\HiPay\Wallet\KYC\HiPayWalletKyc;
use Wizacha\Marketplace\Payment\KYC\Status;
use Wizacha\Marketplace\Payment\AbstractPaymentLogger;
use Wizacha\Money\Money;

/**
 * API client to use the Hipay marketplace/wallet API
 * @see https://developer.hipay.com/doc-api/marketplace/api
 */
class HiPayWalletApi
{
    /** @var Client */
    private $client;

    /** @var string */
    private $entity;

    /** @var int */
    private $merchantGroupId;

    /** @var string  */
    private $currencyCode;

    private HiPayWalletLogger $hiPayWalletLogger;

    public function __construct(
        ClientInterface $client,
        $entity,
        int $merchantGroupId,
        string $currencyCode,
        HiPayWalletLogger $hiPayWalletLogger
    ) {
        $this->client = $client;
        $this->entity = $entity;
        $this->merchantGroupId = $merchantGroupId;
        $this->currencyCode = $currencyCode;
        $this->hiPayWalletLogger = $hiPayWalletLogger;
    }

    /**
     * Check if given email can be used to create an HiPay wallet
     */
    public function isEmailAvailable(string $email): bool
    {
        $response = $this->doRequest('POST', 'user-account/is-available.json', [
            'user_email' => $email,
            'entity' => $this->entity,
        ]);

        return (bool) ($response['is_available'] ?? false);
    }

    /**
     * @param int|null $subAccountId If null, the balance of the root account is returned
     */
    public function getBalance(int $subAccountId = null): Money
    {
        $response = $this->doRequest('GET', 'user-account/balance.json', [], [], ['subAccountId' => $subAccountId]);

        return Money::fromVariable($response['balances'][0]['money_available_for_withdrawal']);
    }

    public function withdrawal(int $subAccountId, Money $amount, string $label, string $reference)
    {
        $this->doRequest('POST', 'withdrawal.json', [
            'amount' => $amount->getConvertedAmount(),
            'label' => $label,
            'merchant_unique_id' => $reference,
        ], [], ['subAccountId' => $subAccountId]);
    }

    public function createAccount(int $companyId)
    {
        $company = new Company($companyId);
        $company->getEmail();

        $user = $company->getFirstAdmin();

        $legacyUserInfo = fn_get_user_info($user->getId(), true);

        $birthdate = $user->getBirthday() instanceof \DateTimeInterface
            ? $user->getBirthday()
            : \DateTime::createFromFormat('U', "0");

        $params = [
            'email' => $company->getEmail(),
            'controle_type' => 'CREDENTIALS',
            'credential[wslogin]' => $this->client->getConfig('auth')[0],
            'credential[wspassword]' => $this->client->getConfig('auth')[1],
            'firstname' => $company->getLegalRepresentativeFirstname(),
            'lastname' => $company->getLegalRepresentativeLastname(),
            'currency' => $this->currencyCode,
            'locale' => 'fr_FR', // TODO: fix locale
            'civility' => ($legacyUserInfo['title'] ?: 'mr') === 'mr' ? 1 : 2, // 1 Mr, 2 Mrs, 3 Miss
            'merchant_group_id' => $this->merchantGroupId,
            'entity_code' => $this->entity,
            'account_type' => $company->isProfessional()
                ? HiPayWalletAccountTypes::BUSINESS()->getValue()
                : HiPayWalletAccountTypes::PERSONAL()->getValue()
            ,
            'structure' => $company->getLegalStatus(),
            'company_name' => $company->getCorporateName(),
            'vat_number' => $company->getVatNumber(),
            'address[address]' => $company->getAddress(),
            'address[zipcode]' => $company->getZipcode(),
            'address[city]' => $company->getCity(),
            'address[country]' => $company->getCountry(),
            'timezone' => 'Europe/Paris',
            'birthdate' => $birthdate->format("Y-m-d"),
        ];

        if ((new HiPayWalletAccountTypes($params['account_type']))->equals(HiPayWalletAccountTypes::BUSINESS())
            && $company->isProfessional()
        ) {
            $params['pro_type'] = HiPayWalletProfessionalAccountTypes::PROFESSIONAL_CORPORATIONS()->getValue();
        }

        $response = $this->doRequest('POST', 'user-account.json', $params);

        $company->setHipayId($response['account_id']);

        return $response;
    }

    /**
     * @throws HiPayWalletRequestFailed
     */
    public function transferFundsFromTechnicalWallet(Money $amount, string $recipientId, string $label, string $reference): array
    {
        $response = $this->doRequest('POST', 'transfer.json', [
            'recipient_account_id' => $recipientId,
            'amount' => $amount->getConvertedAmount(),
            'public_label' => $label,
            'merchant_unique_id' => $reference,
            'periodicity_type' => 'direct', // direct, deferred, periodic
        ]);

        return $response;
    }

    public function uploadBankInfo(Company $company): void
    {
        $companyId = $company->getId();
        $accountId = $company->getHipayId();

        if (empty($accountId)) {
            throw new \InvalidArgumentException("HiPay: given company ($companyId) is not registered on HiPay Wallet");
        }

        if (empty($company->getIban()) || empty($company->getBic())) {
            throw new \InvalidArgumentException("HiPay: given company ($companyId) doest not have IBAN / BIC");
        }

        $ibanFile = null;
        $kycDocuments = $this->getIdentityDocuments($accountId, $companyId);
        $ibanFile = $kycDocuments->getDocumentsToUpload(true);

        if (\is_resource($ibanFile) === false) {
            throw new HiPayKycFileException("HiPay: given company ($companyId) does not have IBAN file");
        }

        $this->doRequest(
            'POST',
            'user-account/bank-info.json',
            [
                'iban' => $company->getIban(),
                'swift' => $company->getBic(),
            ],
            [
                'file' => stream_get_contents($ibanFile),
            ],
            ['subAccountId' => $company->getHipayId()]
        );

        fclose($ibanFile);
    }

    public function getIdentityDocuments(int $subAccountId, int $companyId): HiPayWalletKyc
    {
        $context = [
            'company_id' => $companyId,
            "subAccountId" => $subAccountId,
        ];

        $response = $this->doRequest('GET', 'identification.json', [], [], $context);

        $vendorSubscriptionStorage = container()->get('Wizacha\Storage\VendorSubscriptionStorageService');
        $kycDocuments = new HiPayWalletKyc(
            $companyId,
            $response,
            $vendorSubscriptionStorage,
            $this->hiPayWalletLogger
        );

        $response = $this->doRequest('GET', 'user-account/bank-info.json', [], [], $context);

        $kycDocuments->setRibFromResponse($response['status_code'], $response['status'], $response['message']);

        return $kycDocuments;
    }

    public function uploadIdentityDocuments(Company $company): void
    {
        $companyId = $company->getId();
        $accountId = $company->getHipayId();

        if (empty($accountId)) {
            throw new \InvalidArgumentException("HiPay: given company ($companyId) is not registered on HiPay Wallet");
        }

        $kycDocuments = $this->getIdentityDocuments($accountId, $companyId);
        $files = $kycDocuments->getDocumentsToUpload();

        foreach ($files as $fileType => $file) {
            $this->doRequest(
                'POST',
                'identification.json',
                [
                    'type' => $fileType,
                ],
                [
                    'file' => stream_get_contents($file),
                ],
                ['subAccountId' => $company->getHipayId()]
            );

            fclose($file);
        }
    }

    public function validateKyc(Company $company): void
    {
        if (true === \is_null($company->getHipayId())) {
            $this->hiPayWalletLogger->log(AbstractPaymentLogger::ERROR_LEVEL, "[ValidateKyc]", [
                "company_id" => $company->getId(),
                "hipay_id" => $company->getHipayId(),
                "message" => "hipay_id is null",
            ]);

            return;
        }

        $kycDocuments = $this->getIdentityDocuments($company->getHipayId(), $company->getId());
        $kycReasons = $kycDocuments->checkKyc($kycDocuments->getExistingDocuments());
        $currentStatus = $company->getStatus();
        $status = WizachaCompany::STATUS_PENDING;

        if ($kycReasons->countDocumentsStatus(Status::REFUSED()) > 0) {
            $status = WizachaCompany::STATUS_DISABLED;
        } elseif ($kycReasons->countDocumentsStatus(Status::VALIDATED()) > 0
            && $kycReasons->getBadStatusCount() === 0
        ) {
            $status = WizachaCompany::STATUS_ENABLED;
        }

        $reasons = \implode(", ", $kycReasons->getRefusedReasons());

        $this->hiPayWalletLogger->log(AbstractPaymentLogger::INFO_LEVEL, "[ValidateKyc:getIdentityDocuments]", [
            "company_id" => $company->getId(),
            "status" => $company->getStatus(),
            "number_approved_documents" => $kycReasons->countDocumentsStatus(Status::VALIDATED()),
            "number_refused_documents" => $kycReasons->countDocumentsStatus(Status::REFUSED()),
            "reasons" => $reasons,
            "number_bad_status" => $kycReasons->getBadStatusCount(),
        ]);

        if ($status === $currentStatus) {
            $this->hiPayWalletLogger->log(AbstractPaymentLogger::ERROR_LEVEL, "[ValidateKyc:Company status not updated]", [
                "company_id" => $company->getId(),
                "hipay_id" => $company->getHipayId(),
                "current_status" => $company->getStatus(),
                "new_status" => $status,
            ]);

            return;
        }

        $this->hiPayWalletLogger->log(AbstractPaymentLogger::INFO_LEVEL, "[ValidateKyc:fn_companies_change_status]", [
            "company_id" => $company->getId(),
            "hipay_id" => $company->getHipayId(),
            "current_status" => $company->getStatus(),
            "new_status" => $status,
            "message" => "try to change vendor status",
        ]);

        if (false === fn_companies_change_status(
            $company->getId(),
            $status,
            $reasons,
            $currentStatus
        )
        ) {
            $this->hiPayWalletLogger->log(AbstractPaymentLogger::ERROR_LEVEL, "[ValidateKyc:CannotUpdateCompanyException]", [
                "company_id" => $company->getId(),
                "hipay_id" => $company->getHipayId(),
                "current_status" => $company->getStatus(),
                "new_status" => $status,
                "message" => "CannotUpdateCompanyException",
            ]);

            throw new CannotUpdateCompanyException(
                $company->getId(),
                $status,
                $currentStatus,
                $reasons
            );
        }
    }

    /**
     * @param StreamInterface[]|resource[] $files
     * @throws HiPayWalletRequestFailed
     */
    private function doRequest(
        string $method,
        string $action,
        array $params,
        array $files = [],
        array $context = []
    ): array {
        $paramsToLog = $params;

        if (\count($files) > 0) {
            $multipartRequest = [];

            foreach ($params as $name => $value) {
                $multipartRequest[] = [
                    'name' => $name,
                    'contents' => $value,
                ];
            }

            foreach ($files as $name => $value) {
                $multipartRequest[] = [
                    'name' => $name,
                    'contents' => $value,
                    'filename' => 'uploaded_file_' . $name,
                ];
            }

            $options = [ RequestOptions::MULTIPART => $multipartRequest ];
        } else {
            $options = [ RequestOptions::FORM_PARAMS => $params ];
        }

        $options = array_merge(
            [
                RequestOptions::HEADERS => isset($context['subAccountId']) ? ['php-auth-subaccount-id' => $context['subAccountId']] : [],
            ],
            $options
        );

        $this->hiPayWalletLogger->start();

        try {
            $response = $this->client->request($method, $action, $options);

            $body = \json_decode($response->getBody()->getContents(), true);

            if ($body['code'] > 0) {
                throw new HiPayWalletRequestFailed(
                    $body['message'],
                    (true === \array_key_exists('error_code_detail', $body))
                        ? $body['error_code_detail']
                        : $body['code']
                );
            } else {
                $this->hiPayWalletLogger->requestSuccess($method, $action, $paramsToLog, $response, $context);
            }
        } catch (GuzzleException $exception) {
            $this->hiPayWalletLogger->requestException($method, $action, $paramsToLog, $exception, $context);

            throw new HiPayWalletRequestFailed(
                $exception->getMessage(),
                $exception->getCode(),
                $exception
            );
        } catch (HiPayWalletRequestFailed $exception) {
            $this->hiPayWalletLogger->requestException($method, $action, $paramsToLog, $exception, $context);

            throw $exception;
        }

        return $body;
    }
}
