<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\HiPay\Wallet;

use Psr\Http\Message\ResponseInterface;
use Wizacha\Marketplace\Payment\AbstractPaymentLogger;

/**
 * This logger is used in HiPay for every Wallet API call.
 *
 * @see HiPayWalletApi::doRequest()
 */
class HiPayWalletLogger extends AbstractPaymentLogger
{
    private const HIPAY_WALLET_LOG_NAME = 'HiPay API Wallet';

    public function requestSuccess(
        string $method,
        string $endpoint,
        array $requestParams,
        ResponseInterface $response,
        array $context
    ): void {
        $this->logRequestSuccess(
            self::HIPAY_WALLET_LOG_NAME . ' Request Success',
            $method,
            $endpoint,
            $requestParams,
            $response->getStatusCode(),
            $response->getBody()->getContents(),
            $context
        );
    }

    public function requestException(
        string $method,
        string $endpoint,
        array $requestParams,
        \Throwable $exception,
        array $context
    ): void {
        $this->logRequestException(
            self::HIPAY_WALLET_LOG_NAME . ' Error',
            $method,
            $endpoint,
            $requestParams,
            $exception,
            $context
        );
    }
}
