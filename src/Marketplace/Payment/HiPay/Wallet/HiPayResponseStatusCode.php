<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\HiPay\Wallet;

use MyCLabs\Enum\Enum;

/**
 * @method static HiPayResponseStatusCode BANK_INFORMATION_MISSING()
 * @method static HiPayResponseStatusCode BANK_INFORMATION_VALIDATION_IN_PROGRESS()
 * @method static HiPayResponseStatusCode BANK_INFORMATION_VALIDATED()
 * @method static HipayResponseStatusCode WITHDRAWAL_VALIDATION_FAILED()
 * @method static HipayResponseStatusCode WITHDRAWAL_AUTHENTIFICATION_FAILED()
 * @method static HipayResponseStatusCode WITHDRAWAL_USERSPACE_NOT_ACTIVATED()
 * @method static HipayResponseStatusCode WITHDRAWAL_USERSPACE_INFO_NOT_VALID()
 * @method static HipayResponseStatusCode WITHDRAWAL_USERSPACE_NOT_IDENTIFIED()
 * @method static HipayResponseStatusCode WITHDRAWAL_BANK_INFO_NOT_VALID()
 * @method static HipayResponseStatusCode WITHDRAWAL_USERSPACE_STATE_NOT_NORMAL()
 * @method static HipayResponseStatusCode WITHDRAWAL_ACCOUNT_NOT_ACTIVATED()
 * @method static HipayResponseStatusCode WITHDRAWAL_ACCOUNT_DISABLED()
 * @method static HipayResponseStatusCode WITHDRAWAL_ACCOUNT_DELETED()
 * @method static HipayResponseStatusCode WITHDRAWAL_ACCOUNT_IS_NOT_MAIN()
 * @method static HipayResponseStatusCode WITHDRAWAL_BANK_COUNTRY_BLACKLISTED()
 * @method static HipayResponseStatusCode WITHDRAWAL_MAX_LIMIT_REACHED()
 * @method static HipayResponseStatusCode BALANCE_AFTER_WITHDRAWAL_WILL_BE_TOO_LOW()
 */
class HiPayResponseStatusCode extends Enum
{
    protected const BANK_INFORMATION_MISSING = 1;
    protected const BANK_INFORMATION_VALIDATION_IN_PROGRESS = 2;
    protected const BANK_INFORMATION_VALIDATED = 3;
    protected const WITHDRAWAL_VALIDATION_FAILED = 400;
    protected const WITHDRAWAL_AUTHENTIFICATION_FAILED = 401;
    protected const WITHDRAWAL_USERSPACE_NOT_ACTIVATED = 4002;
    protected const WITHDRAWAL_USERSPACE_INFO_NOT_VALID = 4003;
    protected const WITHDRAWAL_USERSPACE_NOT_IDENTIFIED = 4004;
    protected const WITHDRAWAL_BANK_INFO_NOT_VALID = 4005;
    protected const WITHDRAWAL_USERSPACE_STATE_NOT_NORMAL = 4006;
    protected const WITHDRAWAL_ACCOUNT_NOT_ACTIVATED = 4007;
    protected const WITHDRAWAL_ACCOUNT_DISABLED = 4008;
    protected const WITHDRAWAL_ACCOUNT_DELETED = 4009;
    protected const WITHDRAWAL_ACCOUNT_IS_NOT_MAIN = 4010;
    protected const WITHDRAWAL_BANK_COUNTRY_BLACKLISTED = 4012;
    protected const WITHDRAWAL_MAX_LIMIT_REACHED = 4013;
    protected const BALANCE_AFTER_WITHDRAWAL_WILL_BE_TOO_LOW = 4014;
}
