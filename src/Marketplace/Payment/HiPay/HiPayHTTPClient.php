<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\HiPay;

use HiPay\Fullservice\HTTP\SimpleHTTPClient;

/**
 * This class is used as a wrapper for the HiPay http client.
 * We want to log the requests sent to the HiPay API (except Wallets),
 * therefore we need to get the necessary request and response info from the HiPay objects
 *
 * Each request is logged directly in this wrapper as we have everything we need
 * @see HiPayLogger
 */
class HiPayHTTPClient extends SimpleHTTPClient
{
    private HiPayLogger $hiPayLogger;

    public function setHiPayLogger(HiPayLogger $hiPayLogger): void
    {
        $this->hiPayLogger = $hiPayLogger;
    }

    /**
     * This is a wrapper around the HiPay SimpleHTTPClient doRequest function
     * We get the request info and call the original Request function
     */
    public function doRequest($method, $endpoint, array $params = array(), $isVault = false, $isData = false)
    {
        $this->hiPayLogger->start();

        try {
            // We can use the response returned here in our logs
            $response = $this->parentDoRequest($method, $endpoint, $params, $isVault, $isData);

            $this->hiPayLogger->requestSuccess($method, $endpoint, $params, $response);

            return $response;
        } catch (\Throwable $exception) {
            $this->hiPayLogger->requestException($method, $endpoint, $params, $exception);

            throw $exception;
        }
    }

    /**
     * This function is only necessary for a test purpose
     */
    protected function parentDoRequest($method, $endpoint, $params, $isVault, $isData)
    {
        return parent::doRequest($method, $endpoint, $params, $isVault, $isData);
    }
}
