<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\HiPay;

use HiPay\Fullservice\Gateway\Model\PaymentMethod;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Wizacha\Events\IterableEvent;
use Wizacha\Marketplace\Commission\CommissionService;
use Wizacha\Marketplace\Company\CompanyEvents;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Company\CompanyStatus;
use Wizacha\Marketplace\Company\Event\CompanyLegalDocumentsUpdated;
use Wizacha\Marketplace\CreditCard\CreditCard;
use Wizacha\Marketplace\CreditCard\Service\CreditCardService;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Order\Action\DispatchFundsFailed;
use Wizacha\Marketplace\Order\Action\DispatchFundsSucceeded;
use Wizacha\Marketplace\Order\Action\MarkPaymentAuthorizationAsCaptured;
use Wizacha\Marketplace\Order\Action\MarkPaymentAuthorizationAsRefused;
use Wizacha\Marketplace\Payment\Event\DispatchFundsFailedEvent;
use Wizacha\Marketplace\Order\Event\OrderStatusUpdated;
use Wizacha\Marketplace\Order\OrderEvents;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\Payment\AbstractProcessorEventSubscriber;
use Wizacha\Marketplace\Payment\Event\HipayPaymentCallbackEvent;
use Wizacha\Marketplace\Payment\HiPay\Wallet\HiPayKycFileException;
use Wizacha\Marketplace\Payment\HiPay\Wallet\HiPayWalletRequestFailed;
use Wizacha\Marketplace\Payment\KYC\FileTypes;
use Wizacha\Marketplace\Payment\KYC\Status;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\Processor\HiPay;
use Wizacha\Marketplace\Subscription\SubscriptionService;
use Wizacha\Marketplace\Transaction\Transaction;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\User\UserRepository;
use Wizacha\Order;

class HiPayEventSubscriber extends AbstractProcessorEventSubscriber
{
    private DispatchFundsFailed $dispatchFundsFailedAction;
    private MarkPaymentAuthorizationAsCaptured $markAuthorizationAsCaptured;
    private MarkPaymentAuthorizationAsRefused $markAuthorizationAsRefused;
    protected CompanyService $companyService;
    private EventDispatcherInterface $eventDispatcher;
    private CommissionService $commissionService;
    private DispatchFundsSucceeded $dispatchFundsSucceededAction;

    public const EXCEPTION = 'exception';

    public function __construct(
        LoggerInterface $logger,
        HiPay $processor,
        TransactionService $transactionService,
        OrderService $orderService,
        DispatchFundsFailed $dispatchFundsFailedAction,
        MarkPaymentAuthorizationAsCaptured $markAuthorizationAsCaptured,
        MarkPaymentAuthorizationAsRefused $markAuthorizationAsRefused,
        CompanyService $companyService,
        CreditCardService $creditCardService,
        UserRepository $userRepository,
        SubscriptionService $subscriptionService,
        EventDispatcherInterface $eventDispatcher,
        CommissionService $commissionService,
        DispatchFundsSucceeded $dispatchFundsSucceededAction
    ) {
        parent::__construct($logger, $processor, $subscriptionService, $creditCardService, $orderService, $userRepository, $transactionService);

        $this->dispatchFundsFailedAction = $dispatchFundsFailedAction;
        $this->markAuthorizationAsCaptured = $markAuthorizationAsCaptured;
        $this->markAuthorizationAsRefused = $markAuthorizationAsRefused;
        $this->companyService = $companyService;
        $this->eventDispatcher =  $eventDispatcher;
        $this->commissionService = $commissionService;
        $this->dispatchFundsSucceededAction = $dispatchFundsSucceededAction;
    }

    public function getName(): PaymentProcessorName
    {
        return PaymentProcessorName::HIPAY();
    }

    /** @return string[] */
    public static function getSubscribedEvents(): array
    {
        return [
            OrderEvents::UPDATED  => ['onOrderUpdate', 0],
            CompanyEvents::UPDATED => ['onCompanyUpdate', 0],
            CompanyEvents::SEND_TO_PSP => ['onCompanyUpdate', 0],
            CompanyEvents::LEGAL_DOCUMENTS_UPDATED => ['onCompanyLegalDocumentsUpdated', 0],
            CompanyEvents::IBAN_BIC_UPDATED => ['onCompanyIbanBicUpdated', 0],
            HipayPaymentCallbackEvent::class => [
                ['updateTransactionStatus', 24],
                ['addCreditCardToSubscription', 16],
                ['updateSubscriptionStatus', 8],
            ],
        ];
    }

    public function onOrderUpdate(OrderStatusUpdated $event): void
    {
        if ($this->processor->isConfigured()) {
            $order = new Order($event->getId());

            if ($order->hasProcessorName(PaymentProcessorName::HIPAY()) === false) {
                return;
            }

            $orderId = $order->getId();
            if ($event->getStatusTo() === OrderStatus::PROCESSING_SHIPPING && $this->processor->isWaitingCaptured($orderId)) {
                $transactions = $this->transactionService->findByOrderId($orderId);

                /** @var Transaction $transaction */
                foreach ($transactions as $transaction) {
                    // No order process needed if transaction is Hipay SEPA Direct/Deferment
                    $processorInfo = $transaction->getProcessorInformations() ?? [];
                    if (true === \array_key_exists('hipay_sepa', $processorInfo)) {
                        return;
                    }
                }

                foreach ($this->orderService->getChildOrders($event->getId()) as $order) {
                    $action = $this->processor->captureTransaction($order) ? $this->markAuthorizationAsCaptured : $this->markAuthorizationAsRefused;

                    if ($action->isAllowed($order)) {
                        $action->execute($order);
                    }
                }
            }

            if ($event->getStatusTo() === OrderStatus::COMPLETED) {
                $this->transferFundsToVendor($event->getId());
            }
        }
    }

    public function onCompanyUpdate(IterableEvent $event): void
    {
        if (false === $this->processor->isConfigured()) {
            return;
        }

        foreach ($event as $companyId) {
            $company = new Company($companyId);
            /*
             * On créé un compte :
             *  - Si le vendeur n'est pas au statut actif (il devrait déjà avoir un compte)
             *  - S'il n'a pas encore d'ID HiPay
             *  - Si le vendeur est relié à un user
             */
            if ((true === $company->isEnabled() && true === $company->isProfessional())
                || false === \is_null($company->getHipayId())
                || 0 === \count($company->getAdmins())
            ) {
                continue;
            }

            if (true === $this->companyService->canCreatePspAccount($company)
                && true === $this->isAvailableEmail($company->getEmail())
            ) {
                try {
                    $this->processor->getWalletApi()->createAccount($company->getId());
                } catch (HiPayWalletRequestFailed $e) {
                    $this->logger->error('HiPay: cannot create company account', [
                        static::EXCEPTION => $e,
                        'companyId' => $company->getId(),
                    ]);
                    //change status to NEW
                    $this->companyService->ChangeStatusWhenFailed((int) $companyId, CompanyStatus::NEW(), $e->getMessage());
                    continue;
                }

                $vendorSubscriptionStorage = container()->get('Wizacha\Storage\VendorSubscriptionStorageService');
                $files = $vendorSubscriptionStorage->getList('/' . $companyId);

                try {
                    $this->onCompanyLegalDocumentsUpdated(new CompanyLegalDocumentsUpdated((int) $companyId, $files));
                } catch (\Exception $exception) {
                    $this->logger->error('[HiPay] unable to send KYC.', [
                        static::EXCEPTION => $exception,
                        'data' => $company->getData(),
                    ]);
                    //change status to NEW
                    $this->companyService->ChangeStatusWhenFailed((int) $companyId, CompanyStatus::NEW(), $exception->getMessage());
                }
            }
        }
    }

    /**
     * Upload Bank Info to Hipay and log any error
     * @param Company $company
     */
    private function uploadBankInfo(Company $company)
    {
        try {
            $this->processor->getWalletApi()->uploadBankInfo($company);
        } catch (HiPayKycFileException $exception) {
            $this->logger->error('[HiPay] error_rib_file', [
                static::EXCEPTION => $exception,
                'company' => $company->getData(),
            ]);

            fn_set_notification('E', __('error'), __('error_rib_file'));
        } catch (HiPayWalletRequestFailed $exception) {
            $this->logger->error('[HiPay] error_request_failed', [
                static::EXCEPTION => $exception,
                'company' => $company->getData(),
            ]);

            fn_set_notification('E', __('error'), __('error_iban'));
        }
    }

    public function onCompanyLegalDocumentsUpdated(CompanyLegalDocumentsUpdated $event): void
    {
        if (false === $this->processor->isConfigured()) {
            return;
        }

        $company = new Company($event->getCompanyId());

        if (\is_null($company->getHipayId()) || \is_null($company->getIban()) || \is_null($company->getBic())) {
            // Ignore incomplete companies
            return;
        }

        $kyc = $this->processor->getWalletApi()->getIdentityDocuments((int) $company->getHipayId(), $company->getId());
        $ribDocument = $kyc->getDocumentFromType(FileTypes::RIB()->getValue());

        if (true === $ribDocument->getStatus()->equals(Status::MISSING()) || true === $ribDocument->getStatus()->equals(Status::REFUSED())) {
            $this->uploadBankInfo($company);
        }

        $this->processor->getWalletApi()->uploadIdentityDocuments($company);
    }

    public function onCompanyIbanBicUpdated(IterableEvent $event): void
    {
        if ($this->processor->isConfigured() === false) {
            return;
        }

        foreach ($event as $companyId) {
            $company = new Company($companyId);

            if (\is_null($company->getHipayId()) === true
                || \is_null($company->getIban()) === true
                || \is_null($company->getBic()) === true
            ) {
                // Ignore incomplete companies
                return;
            }

            $kyc = $this->processor->getWalletApi()->getIdentityDocuments((int) $company->getHipayId(), $company->getId());
            $ribDocument = $kyc->getDocumentFromType(FileTypes::RIB()->getValue());

            if (true === $ribDocument->getStatus()->equals(Status::MISSING()) || true === $ribDocument->getStatus()->equals(Status::REFUSED())) {
                $this->uploadBankInfo($company);
            }
        }
    }

    public function transferFundsToVendor(int $orderId): void
    {
        $resultTransfer = false;
        $order = new Order($orderId);

        $paymentsInformation = $order->getPaymentInformation();
        if (\array_key_exists('hipay_transfer_done', $paymentsInformation)) {
            $checkAll = true;
            foreach ($paymentsInformation as $paymentInformation) {
                if ($paymentInformation === false) {
                    $checkAll = false;
                    break;
                }
            }
            //Si toutes les transactions sont à true on ne fait pas de dispatch sinon on relance le dispatch
            if ($checkAll === true) {
                return;
            }
        }

        $subOrders = $order->getSubOrders();

        if (\count($subOrders) > 0) {
            foreach ($subOrders as $suborder) {
                $resultTransfer = $this->processor->transferSpecificOrder($suborder->getOrderV2());
            }
        } else {
            $resultTransfer = $this->processor->transferSpecificOrder($order->getOrderV2());
        }

        /**
         * Avancement du workflow
         */
        $orders = $this->orderService->getChildOrders($orderId);

        if ($resultTransfer === false) {
            $commission = $this->commissionService->getTotalCommission($order->getOrderV2());
            $this->eventDispatcher->dispatch(
                new DispatchFundsFailedEvent($order, $commission),
                DispatchFundsFailedEvent::class
            );
            $order->setPaymentInformation('hipay_transfer_done', false);

            foreach ($orders as $order) {
                $this->dispatchFundsFailedAction->execute($order);
            }
        } else {
            foreach ($orders as $order) {
                $this->dispatchFundsSucceededAction->execute($order);
            }
        }
    }

    public function isAvailableEmail($email): bool
    {
        try {
            return  $this->processor->getWalletApi()->isEmailAvailable($email);
        } catch (HiPayWalletRequestFailed $e) {
            $this->logger->error('HiPay: cannot get email for company account', [
                static::EXCEPTION => $e,
            ]);
        }

        return false;
    }

    public function createCardFromTransaction($data): ?CreditCard
    {
        if ($data->getPaymentMethod() instanceof PaymentMethod === true) {
            return (new CreditCard())
                ->setToken($data->getPaymentMethod()->getToken())
                ->setBrand($data->getPaymentMethod()->getBrand())
                ->setPan($data->getPaymentMethod()->getPan())
                ->setCardHolder($data->getPaymentMethod()->getCardHolder())
                ->setCardExpiryMonth($data->getPaymentMethod()->getCardExpiryMonth())
                ->setCardExpiryYear($data->getPaymentMethod()->getCardExpiryYear())
                ->setIssuer($data->getPaymentMethod()->getIssuer())
                ->setCountry($data->getPaymentMethod()->getCountry())
                ->setPaymentProductCode($data->getPaymentProduct())
            ;
        }

        return null;
    }
}
