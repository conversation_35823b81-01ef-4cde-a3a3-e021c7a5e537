<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\HiPay\Dsp\Enum;

use MyCLabs\Enum\Enum;

/**
 * @method static DeviceChannelType APP_BASED()
 * @method static DeviceChannelType BROWSER()
 * @method static DeviceChannelType SECURE_AUTHENTICATION()
 */
class DeviceChannelType extends Enum
{
    // App-based (APP)
    private const APP_BASED = 1;
    // Browser (BRW)
    private const BROWSER = 2;
    // 3DS Requestor Initiated (3RI = MIT)
    private const SECURE_AUTHENTICATION = 3;
}
