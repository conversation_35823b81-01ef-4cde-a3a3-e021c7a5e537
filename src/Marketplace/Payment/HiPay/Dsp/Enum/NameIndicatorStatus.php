<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\HiPay\Dsp\Enum;

use MyCLabs\Enum\Enum;

/**
 * @method static NameIndicatorStatus NAME_ACCOUNT_IDENTICAL_TO_DELIVERY()
 * @method static NameIndicatorStatus NAME_ACCOUNT_NOT_IDENTICAL_TO_DELIVERY()
 */
class NameIndicatorStatus extends Enum
{
    private const NAME_ACCOUNT_IDENTICAL_TO_DELIVERY = 1;
    private const NAME_ACCOUNT_NOT_IDENTICAL_TO_DELIVERY = 2;
}
