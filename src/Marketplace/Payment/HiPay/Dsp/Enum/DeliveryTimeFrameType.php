<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\HiPay\Dsp\Enum;

use MyCLabs\Enum\Enum;

/**
 * @method static DeliveryTimeFrameType ELECTRONIC_DELIVERY()
 * @method static DeliveryTimeFrameType SAME_DAY_SHIPPING()
 * @method static DeliveryTimeFrameType NEXT_DAY_DELIVERY()
 * @method static DeliveryTimeFrameType DELIVERY_IN_TWO_DAYS_OR_MORE()
 */
class DeliveryTimeFrameType extends Enum
{
    private const ELECTRONIC_DELIVERY = 1;
    private const SAME_DAY_SHIPPING = 2;
    private const NEXT_DAY_DELIVERY = 3;
    private const DELIVERY_IN_TWO_DAYS_OR_MORE = 4;
}
