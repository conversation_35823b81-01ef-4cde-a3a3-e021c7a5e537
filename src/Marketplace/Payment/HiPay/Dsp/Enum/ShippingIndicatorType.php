<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\HiPay\Dsp\Enum;

use MyCLabs\Enum\Enum;

/**
 * @method static ShippingIndicatorType DELIVERY_TO_BILLING_ADDRESS()
 * @method static ShippingIndicatorType DELIVERY_TO_NOT_REGISTERED_ADDRESS()
 * @method static ShippingIndicatorType DELIVERY_TO_OTHER_ADDRESS()
 * @method static ShippingIndicatorType DELIVERY_OR_PICKUP()
 * @method static ShippingIndicatorType DIGITAL_GOODS()
 * @method static ShippingIndicatorType TICKETS()
 * @method static ShippingIndicatorType OTHER()
 */
class ShippingIndicatorType extends Enum
{
    // Livraison à l'adresse de facturation du titulaire de la carte
    private const DELIVERY_TO_BILLING_ADDRESS = 1;
    // Livraison à une autre adresse vérifiée enregistrée chez le marchand
    private const DELIVERY_TO_NOT_REGISTERED_ADDRESS = 2;
    // Livraison à une adresse différente de celle de l'adresse de facturation du titulaire de la carte
    private const DELIVERY_TO_OTHER_ADDRESS = 3;
    // Livraison / retrait en magasin
    private const DELIVERY_OR_PICKUP = 4;
    // Biens numériques (services en ligne, cartes cadeaux électroniques, ou codes promotionnels)
    private const DIGITAL_GOODS = 5;
    // Billets de transport ou de spectacle, pas de livraison
    private const TICKETS = 6;
    // Autre (jeux, services numériques sans livraison, abonnement e-média)
    private const OTHER = 7;
}
