<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\NoPayment;

use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Wizacha\Marketplace\Payment\Event\OfflinePaymentEvent;
use Wizacha\Marketplace\Payment\LemonWay\LemonWayEventSubscriber;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionStatus;
use Wizacha\Order;

final class OfflinePaymentEventSubscriber implements EventSubscriberInterface
{
    /** @var TransactionService  */
    private $transactionService;

    /** @var LemonWayEventSubscriber  */
    private $lemonWayEventSubscriber;

    public function __construct(TransactionService $transactionService, LemonWayEventSubscriber $lemonWayEventSubscriber)
    {
        $this->transactionService = $transactionService;
        $this->lemonWayEventSubscriber = $lemonWayEventSubscriber;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            OfflinePaymentEvent::class => ['onOfflinePayment', 0],
        ];
    }

    public function onOfflinePayment(OfflinePaymentEvent $event): self
    {
        $order = $event->getOrder();

        $orders = \count($order->getSubOrders()) > 0 ? $order->getSubOrders() : [$order];

        foreach ($orders as $subOrder) {
            // PromoMarket paid the full orders, one transaction had to be done
            if ($subOrder->getMarketplaceDiscountTotal()->getConvertedAmount() === $subOrder->getTotal()->getConvertedAmount()) {
                $this->lemonWayEventSubscriber->transferMarketplaceDiscount($order);
            } else {
                $transaction = $this->transactionService->createOfflineTransaction(
                    $subOrder->getId(),
                    $subOrder->getCustomerTotal(),
                    PaymentProcessorName::NONE()
                );
                $transaction->setTransactionReference($this->generateTransactionReference());
                $this->transactionService->save($transaction);

                $this->transferMarketplaceDiscount($subOrder);
            }
        }

        return $this;
    }

    private function transferMarketplaceDiscount(Order $order): self
    {
        if ($order->hasMarketplaceDiscount()) {
            $transaction = $this->transactionService->createTransferTransaction(
                $order->getId(),
                $order->getMarketplaceDiscountTotal(),
                PaymentProcessorName::NONE(),
                ['coupon_marketplace_discount' => $order->getCouponMarketplaceDiscount()]
            );

            // Update the transaction
            $transaction->setTransactionReference($this->generateTransactionReference());
            $transaction->setStatus(TransactionStatus::SUCCESS());
            $this->transactionService->save($transaction);
        }

        return $this;
    }

    private function generateTransactionReference(): string
    {
        return uniqid('offline_');
    }
}
