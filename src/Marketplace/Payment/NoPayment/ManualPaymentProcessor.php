<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment\NoPayment;

use Wizacha\Marketplace\Order\Action\Confirm;
use Wizacha\Marketplace\Order\Action\MarkAsPaid;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\Payment\PaymentProcessor;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\PaymentType;
use Wizacha\Marketplace\Payment\Response\NoPaymentResponse;
use Wizacha\Marketplace\Payment\Response\PaymentResponse;
use Wizacha\Marketplace\Transaction\TransactionService;

class ManualPaymentProcessor implements PaymentProcessor
{
    private OrderService $orderService;
    private MarkAsPaid $markAsPaid;
    private Confirm $confirm;
    protected TransactionService $transactionService;

    public function __construct(
        OrderService $orderService,
        MarkAsPaid $markAsPaid,
        Confirm $confirm,
        TransactionService $transactionService
    ) {
        $this->orderService = $orderService;
        $this->markAsPaid = $markAsPaid;
        $this->confirm = $confirm;
        $this->transactionService = $transactionService;
    }

    /**
     * Aucun retour dans un cas sans paiement
     */
    public function startPayment(int $orderId, string $redirectUrl = null, string $cssUrl = null): PaymentResponse
    {
        $orders = $this->orderService->getChildOrders($orderId);

        foreach ($orders as $order) {
            if ($this->confirm->isAllowed($order)) {
                $this->confirm->execute($order);
            }

            if ($this->markAsPaid->isAllowed($order)) {
                $this->markAsPaid->execute($order);
            }

            $transaction = $this->transactionService
                ->createManualTransaction(
                    $order->getId(),
                    $order->getCustomerTotal(),
                    $this->getName()
                )
            ;

            $transaction->setTransactionReference($this->generateTransactionReference());
            $this->transactionService->save($transaction);

            fn_change_order_status(
                $order->getId(),
                (string) $order->deduceLegacyStatus(),
                (string) OrderStatus::INCOMPLETED()
            );
        }

        return new NoPaymentResponse();
    }


    public function getType(): PaymentType
    {
        return PaymentType::MANUAL();
    }

    public function getName(): PaymentProcessorName
    {
        return PaymentProcessorName::MANUAL();
    }

    public function isConfigured(): bool
    {
        return true;
    }

    private function generateTransactionReference(): string
    {
        return uniqid('offline_');
    }
}
