<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Payment;

use MyCLabs\Enum\Enum;
use Wizacha\Marketplace\Transaction\TransactionType;

/**
 * @method static PaymentType CREDIT_CARD()
 * @method static PaymentType CREDIT_CARD_CAPTURE()
 * @method static PaymentType BANK_TRANSFER()
 * @method static PaymentType SEPA_DIRECT()
 * @method static PaymentType PAYMENT_DEFERMENT()
 * @method static PaymentType MANUAL()
 * @method static PaymentType NONE()
 */
class PaymentType extends Enum
{
    // CB
    private const CREDIT_CARD = 'credit-card';
    // CB Author/Capture
    private const CREDIT_CARD_CAPTURE = 'credit-card-capture';
    // Virement
    private const BANK_TRANSFER = 'bank-transfer';
    // Prelevement
    private const SEPA_DIRECT = 'sepa-direct';
    // Paiement à échéance
    private const PAYMENT_DEFERMENT = 'payment-deferment';
    // Paiement manuel, "de main à la main", g<PERSON><PERSON> en dehors de la marketplace
    // nécessitant donc une action manuelle de l'admin pour indiquer que le
    // client a payé.
    private const MANUAL = 'manual';
    // Aucun paiement, typiquement si le montant est 0€
    private const NONE = 'none';

    public function getTransactionType(): TransactionType
    {
        switch ($this) {
            case static::BANK_TRANSFER():
                $transactionType = TransactionType::BANK_WIRE();
                break;
            case static::SEPA_DIRECT():
            case static::PAYMENT_DEFERMENT():
                $transactionType = TransactionType::DIRECT_DEBIT();
                break;
            case static::MANUAL():
                $transactionType = TransactionType::OFFLINE();
                break;
            default:
                $transactionType = TransactionType::CREDITCARD();
        }

        return $transactionType;
    }
}
