<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Transaction;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\DBAL\Connection;
use Doctrine\ORM\Internal\Hydration\IterableResult;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Rhumsaa\Uuid\Uuid;
use Wizacha\Marketplace\Transaction\Exception\TransactionNotFound;

/**
 *  Manage transactions entities in Doctrine
 */
class TransactionRepository extends ServiceEntityRepository
{
    /** for bulk usage only */
    public function persist(Transaction $transaction)
    {
        $this->getEntityManager()->persist($transaction);
    }

    /** for bulk usage only */
    public function commit()
    {
        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
    }

    public function save(Transaction $transaction): Transaction
    {
        $this->getEntityManager()->persist($transaction);
        $this->getEntityManager()->flush();

        return $transaction;
    }

    public function get(Uuid $id): Transaction
    {
        $query = $this
            ->createQueryBuilder('t')
            ->where('t.id = :id')
            ->setParameter('id', $id)
            ->getQuery();

        try {
            return $query->getSingleResult();
        } catch (NoResultException $e) {
            throw new TransactionNotFound("Transaction '$id' not found.");
        }
    }

    /** @throws TransactionNotFound */
    public function getOneBy(
        array $criteria,
        ?array $orderBy = null
    ): Transaction {
        $transaction = $this->findOneBy(
            $criteria,
            $orderBy
        );

        if ($transaction instanceof Transaction === false) {
            throw new TransactionNotFound("Transaction not found.");
        }

        return $transaction;
    }

    /**
     * @param int[] $orderIds
     * @param TransactionType|null $type
     *
     * @return Transaction[]
     */
    public function findByOrderIds(array $orderIds, TransactionType $type = null): array
    {
        $query = $this
            ->createQueryBuilder('t')
            ->where('t.orderId IN (:ids)')
            ->setParameter('ids', $orderIds)
        ;

        if ($type !== null) {
            $query->andWhere('t.type = :type')
                ->setParameter('type', $type)
            ;
        }

        return $query
            ->getQuery()
            ->getResult();
    }

    public function countByOrderIds(...$orderIds): int
    {
        return (int) $this
            ->createQueryBuilder('t')
            ->select('count(t.id)')
            ->where('t.orderId IN (:ids)')
            ->setParameter('ids', $orderIds)
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function hasRefusedPayment(int $orderId): bool
    {
        $query = $this
            ->createQueryBuilder('t')
            ->select('count(t.id)')
            ->where('t.orderId = :id')
            ->setParameter('id', $orderId)
            ->andWhere('t.status = :status')
            ->setParameter('status', TransactionStatus::FAILED())
            ->getQuery()
            ->getSingleScalarResult();

        return $query > 0;
    }

    /** @return Transaction[] */
    public function findRefundableTransactions(int $orderId, TransactionType $type): array
    {
        $transactions = $this
            ->createQueryBuilder('t')
            ->where('t.orderId = :orderId')
            ->setParameter('orderId', $orderId)
            ->andWhere('t.status = :status')
            ->setParameter('status', TransactionStatus::SUCCESS())
            ->andWhere('t.type = :type')
            ->setParameter('type', $type)
            ->getQuery()
            ->getResult()
        ;

        /** fix entity/db desynchro */
        \array_map(
            function (Transaction $transaction): void {
                $this->getEntityManager()->refresh($transaction);
            },
            $transactions
        );

        return $transactions;
    }

    public function countCreditCardTransactionByOrderId(int $orderId): int
    {
        return (int) $this
            ->createQueryBuilder('t')
            ->select('count(t.id)')
            ->where('t.orderId = :orderId')
            ->setParameter('orderId', $orderId)
            ->andWhere('t.status = :status')
            ->setParameter('status', TransactionStatus::SUCCESS())
            ->andWhere('t.type = :type')
            ->setParameter('type', TransactionType::CREDITCARD())
            ->getQuery()
            ->getSingleScalarResult()
        ;
    }

    /**
     * @param int[] $orderIds
     * @return \Generator<array<int,string>> orderId, TransactionReferences
     */
    public function findBankWireTransactionReferences(array $orderIds): \Generator
    {
        $table = $this
            ->getEntityManager()
            ->getClassMetadata(Transaction::class)
            ->getTableName()
        ;

        $query = $this
            ->getEntityManager()
            ->getConnection()
            ->executeQuery(
                <<<SQL
                SELECT
                    *
                FROM
                    $table t
                WHERE
                    t.order_id IN(?)
                    AND t.type = ?
                SQL,
                [
                    $orderIds,
                    TransactionType::BANK_WIRE()
                ],
                [
                    Connection::PARAM_INT_ARRAY,
                    \PDO::PARAM_STR
                ]
            )
        ;

        while ($row = $query->fetch()) {
            yield $row['order_id'] => $this->getTransactionReferenceFormat($row);
        }
    }

    private function getTransactionReferenceFormat(array $row): string
    {
        $processorInformation = unserialize($row['processor_informations']);
        $processorName = $row['processor_name'];
        $transactionReference = $row['transaction_reference'];

        if ($processorName === 'mangopay') {
            return \implode(
                '-',
                \array_filter(
                    [
                        $processorInformation['label'] ?? null,
                        $transactionReference
                    ]
                )
            );
        }

        return $transactionReference;
    }

    public function findBankWireTransactionReference(int $orderId): ?string
    {
        return $this
            ->findBankWireTransactionReferences([$orderId])
            ->current()
        ;
    }

    /** @param string[] $filters */
    public function findExternalTransferTransactions(
        array $filters,
        ?int $page = null,
        ?int $resultsPerPage = null
    ): Paginator {
        $queryBuilder =  $this
            ->createQueryBuilder('t');

        $this->applyFilter($queryBuilder, $filters);

        $paginator = new Paginator($queryBuilder);

        if (\is_null($resultsPerPage) === false
            && \is_null($page) === false
        ) {
            $paginator->getQuery()
                ->setFirstResult($resultsPerPage * ($page - 1))
                ->setMaxResults($resultsPerPage);
        }

        return $paginator;
    }

    /** @param array $filters */
    private function applyFilter(QueryBuilder $queryBuilder, array $filters): QueryBuilder
    {
        if (\array_key_exists('companies', $filters) === true && \is_null($filters['companies']) === false) {
            $queryBuilder->andWhere('t.companyId IN (:companyId)');
            $queryBuilder->setParameter('companyId', $filters['companies']);
        }

        if (\array_key_exists('type', $filters) === true && \is_null($filters['type']) === false) {
            $queryBuilder->andWhere('t.type IN (:type)');
            $queryBuilder->setParameter('type', $filters['type']);
        }

        if (\array_key_exists('status', $filters) === true && \is_null($filters['status']) === false) {
            $queryBuilder->andWhere('t.status IN (:status)');
            $queryBuilder->setParameter('status', $filters['status']);
        }

        if (\array_key_exists('period_start', $filters) === true
            && \is_null($filters['period_start']) === false
        ) {
            $queryBuilder->andWhere('t.createdAt >= :from')
                ->setParameter('from', $filters['period_start']);
        }

        if (\array_key_exists('period_end', $filters) === true
            && \is_null($filters['period_end']) === false
        ) {
            $queryBuilder->andWhere('t.createdAt <= :to')
                ->setParameter('to', $filters['period_end']);
        }

        $queryBuilder->orderBy('t.createdAt', 'DESC');

        return $queryBuilder;
    }

    public function fixTransactionWithoutCompanyId(
        string $currencyCode
    ): int {
        $statement = $this
            ->getEntityManager()
            ->getConnection()
            ->prepare(<<<SQL
                UPDATE
                    `doctrine_order_transactions` dot
                    JOIN `cscart_orders` co ON
                        co.order_id = dot.order_id
                SET
                    dot.company_id = co.company_id,
                    dot.currency = :currency_code
                WHERE (
                    dot.company_id = 0
                    OR dot.company_id is NULL
                )
                SQL
            )
        ;

        $statement->execute(
            ['currency_code' => $currencyCode]
        );

        return $statement->rowCount();
    }

    public function findTransactionWithoutOriginFilteredByProcessorName(string $processorName): IterableResult
    {
        $query = $this
            ->createQueryBuilder('t')
            ->where('TRIM(t.origin) =  \'\'')
            ->OrWhere('t.origin is null')
            ->OrWhere('t.origin = 0')
            ->andWhere('t.processorName = :processorName')
            ->setParameter(':processorName', $processorName)
            ->getQuery()
        ;

        return $query->iterate();
    }

    public function findTransactionWithoutDestinationFilteredByProcessorName(string $processorName): IterableResult
    {
        $query =  $this
            ->createQueryBuilder('t')
            ->where('TRIM(t.destination) =  \'\'')
            ->OrWhere('t.destination is null')
            ->OrWhere('t.destination = 0')
            ->andWhere('t.processorName = :processorName')
            ->setParameter(':processorName', $processorName)
            ->getQuery()
        ;

        return $query->iterate();
    }

    public function deleteTransaction(Transaction $transaction): void
    {
        $this->getEntityManager()->remove($transaction);
        $this->getEntityManager()->flush();
    }
}
