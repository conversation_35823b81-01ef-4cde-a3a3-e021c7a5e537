<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Transaction;

use Psr\Log\LoggerInterface;
use Rhumsaa\Uuid\Uuid;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Refund\Repository\RefundRepository;
use Wizacha\Marketplace\Order\Repository\OrderRepository;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Transaction\Exception\TransactionNotFound;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Wizacha\Money\Money;

/**
 * Service to manage transaction
 */
class TransactionService
{
    protected TransactionRepository $repository;
    protected EventDispatcherInterface $eventDispatcher;
    protected RefundRepository $refundRepository;
    protected LoggerInterface $logger;
    private OrderRepository $orderRepository;

    public function __construct(
        TransactionRepository $repository,
        EventDispatcherInterface $eventDispatcher,
        RefundRepository $refundRepository,
        LoggerInterface $logger,
        OrderRepository $orderRepository
    ) {
        $this->repository = $repository;
        $this->eventDispatcher = $eventDispatcher;
        $this->refundRepository = $refundRepository;
        $this->logger = $logger;
        $this->orderRepository = $orderRepository;
    }

    public function save(Transaction $transaction): Transaction
    {
        $transaction->updateTimestamp();

        $this->repository->save($transaction);
        $this->eventDispatcher->dispatch(
            new TransactionUpdatedEvent($transaction),
            TransactionUpdatedEvent::class
        );

        return $transaction;
    }

    public function get(Uuid $id): Transaction
    {
        return $this->repository->get($id);
    }

    public function getOneBy(
        array $criteria,
        ?array $orderBy = null
    ): Transaction {
        return $this->repository->getOneBy(
            $criteria,
            $orderBy
        );
    }

    public function hasRefusedPayment(int $orderId): bool
    {
        return $this->repository->hasRefusedPayment($orderId);
    }

    /**
     * @param mixed[] $filters Doctrine filters
     * @param mixed[] $orderBy Doctrine orderBy
     *
     * @return Transaction[]
     */
    public function findBy(array $filters, array $orderBy = null): array
    {
        return $this->repository->findBy($filters, $orderBy);
    }

    /** @return Transaction[] */
    public function findByOrderId(int $orderId, TransactionType $type = null): array
    {
        $filter = $type instanceof TransactionType ? ['type' => $type] : [];

        return $this->repository->findBy(
            array_merge(['orderId' => $orderId], $filter),
            ['createdAt' => 'ASC']
        );
    }

    public function findByTransactionReference(string $reference, PaymentProcessorName $processorName): ?Transaction
    {
        return $this->repository->getOneBy([
            'transactionReference' => $reference,
            'processorName' => (string) $processorName,
        ]);
    }

    /**
     * @param int[]           $orderIds
     * @param TransactionType $type
     *
     * @return Transaction[]
     */
    public function findByOrderIds(array $orderIds, TransactionType $type = null): array
    {
        return $this->repository->findByOrderIds($orderIds, $type);
    }

    public function findBankWireTransactionReference(int $orderId): ?string
    {
        return $this->repository->findBankWireTransactionReference($orderId);
    }

    /**
     * @param int[] $orderIds
     */
    public function findBankWireTransactionReferences(array $orderIds): array
    {
        return \iterator_to_array(
            $this->repository->findBankWireTransactionReferences($orderIds)
        );
    }

    public function countByOrderIds(...$orderIds): int
    {
        return $this->repository->countByOrderIds($orderIds);
    }

    /** @param mixed[]|null $information Additional specific information related to the transaction */
    public function createCreditCardTransaction(
        int $orderId,
        Money $amount,
        PaymentProcessorName $processorName,
        array $information = null
    ): Transaction {
        $transaction = new Transaction($orderId, TransactionType::CREDITCARD(), TransactionStatus::READY(), $amount);
        $transaction->setProcessorInformations($information);
        $transaction->setProcessorName((string) $processorName);
        $transaction->setCompanyId($this->orderRepository->getCompanyIdByOrder($orderId));

        return $this->save($transaction);
    }

    /** @param mixed[]|null $information Additional specific information related to the transaction */
    public function createBankWireTransaction(
        int $orderId,
        Money $amount,
        PaymentProcessorName $processorName,
        array $information = null
    ): Transaction {
        $transaction = new Transaction($orderId, TransactionType::BANK_WIRE(), TransactionStatus::READY(), $amount);
        $transaction->setProcessorInformations($information);
        $transaction->setProcessorName((string) $processorName);
        $transaction->setCompanyId($this->orderRepository->getCompanyIdByOrder($orderId));

        return $this->save($transaction);
    }

    /** @param mixed[]|null $information Additional specific information related to the transaction */
    public function createDirectDebitTransaction(
        int $orderId,
        Money $amount,
        PaymentProcessorName $processorName,
        array $information = null
    ): Transaction {
        $transaction = new Transaction($orderId, TransactionType::DIRECT_DEBIT(), TransactionStatus::READY(), $amount);
        $transaction->setProcessorInformations($information);
        $transaction->setProcessorName((string) $processorName);
        $transaction->setCompanyId($this->orderRepository->getCompanyIdByOrder($orderId));

        return $this->save($transaction);
    }

    /** @param mixed[]|null $information Additional specific information related to the transaction */
    public function createTransferTransaction(
        int $orderId,
        Money $amount,
        PaymentProcessorName $processorName,
        array $information = null
    ): Transaction {
        $transaction = new Transaction($orderId, TransactionType::TRANSFER(), TransactionStatus::READY(), $amount);
        $transaction->setProcessorInformations($information);
        $transaction->setProcessorName((string) $processorName);
        $transaction->setCompanyId($this->orderRepository->getCompanyIdByOrder($orderId));

        return $this->save($transaction);
    }

    /** @param mixed[]|null $information Additional specific information related to the transaction */
    public function createRefundTransaction(
        int $orderId,
        Money $amount,
        PaymentProcessorName $processorName,
        array $information = null
    ): Transaction {
        $transaction = new Transaction($orderId, TransactionType::REFUND(), TransactionStatus::READY(), $amount);
        $transaction->setProcessorInformations($information);
        $transaction->setProcessorName((string) $processorName);
        $transaction->setCompanyId($this->orderRepository->getCompanyIdByOrder($orderId));

        return $this->save($transaction);
    }

    /** @param mixed[]|null $information Additional specific information related to the transaction */
    public function createRefundTransferTransaction(
        int $orderId,
        Money $amount,
        PaymentProcessorName $processorName,
        array $information = null
    ): Transaction {
        $transaction = new Transaction(
            $orderId,
            TransactionType::REFUND_TRANSFER(),
            TransactionStatus::READY(),
            $amount
        );

        $transaction
            ->setProcessorInformations($information)
            ->setProcessorName((string) $processorName)
            ->setRefundId($information['refund_id'])
            ->setCompanyId($this->orderRepository->getCompanyIdByOrder($orderId))
        ;

        return $this->save($transaction);
    }

    /** @param mixed[]|null $information Additional specific information related to the transaction */
    public function createOfflineTransaction(
        int $orderId,
        Money $amount,
        PaymentProcessorName $processorName,
        array $information = null
    ): Transaction {
        $transaction = new Transaction($orderId, TransactionType::OFFLINE(), TransactionStatus::PENDING(), $amount);
        $transaction->setProcessorInformations($information);
        $transaction->setProcessorName($processorName->getValue());
        $transaction->setCompanyId($this->orderRepository->getCompanyIdByOrder($orderId));

        return $this->save($transaction);
    }

    /** @param mixed[]|null $information Additional specific information related to the transaction */
    public function createManualTransaction(
        int $orderId,
        Money $amount,
        PaymentProcessorName $processorName,
        array $information = null
    ): Transaction {
        $transaction = new Transaction($orderId, TransactionType::OFFLINE(), TransactionStatus::SUCCESS(), $amount);
        $transaction->setProcessorInformations($information);
        $transaction->setProcessorName($processorName->getValue());
        $transaction->setCompanyId($this->orderRepository->getCompanyIdByOrder($orderId));

        return $this->save($transaction);
    }

    public function createExternalTransferTransaction(
        ?int $orderId,
        int $companyId,
        int $amount,
        string $origin,
        string $destination,
        string $resultTransfer,
        string $currency,
        TransactionType $transactionType,
        TransactionStatus $transactionStatus,
        string $reference,
        PaymentProcessorName $processorName
    ): void {
        $transaction = new Transaction(
            $orderId,
            $transactionType,
            $transactionStatus,
            new Money($amount)
        );

        $transaction
            ->setCompanyId($companyId)
            ->setOrigin($origin)
            ->setDestination($destination)
            ->setCurrency($currency)
            ->setProcessorInformations(['additional_info' => $resultTransfer])
            ->setTransactionReference($reference)
            ->setProcessorName((string) $processorName);

        $this->save($transaction);
    }

    public function updateTransactionStatus(Transaction $transaction, TransactionStatus $status): Transaction
    {
        $transaction->setStatus($status);

        return $this->save($transaction);
    }

    /** @return Transaction[] */
    public function retrievePaymentTransaction(Order $order): array
    {
        return $this->repository->findRefundableTransactions(
            $order->getId(),
            $order->getPayment()->getType()->getTransactionType()
        );
    }

    /** @return Transaction[] */
    public function retrievePaymentMPDiscountTransaction(Order $order): array
    {
        return $this->repository->findRefundableTransactions(
            $order->getId(),
            TransactionType::TRANSFER()
        );
    }

    /**
     * - see commmit 42926c2d4ea218c42082a2796ab83fc013fda3d7 -
     * Exception for order with Adjustment price payment status at STAND By Vendor.
     * Transaction Status could be "PENDING" in the other case the transaction status is 'SUCCESS'
     */
    protected function isPendingAjustementPrice(Transaction $transaction): bool
    {
        return (
            TransactionStatus::PENDING()->equals(
                $transaction->getStatus()
            )
            && TransactionType::CREDITCARD()->equals(
                $transaction->getType()
            )
            && PaymentProcessorName::HIPAY()->equals(
                $transaction->getProcessorName()
            )
        );
    }

    /**
     * @return Transaction[]
     */
    protected function getOrderBalanceAccountableTransactions(
        int $orderId
    ): array {
        return \array_filter(
            $this->findBy(['orderId' => $orderId]),
            function (Transaction $transaction): bool {
                $isDispatchFunds = TransactionType::isDispatchFundsTransferType(
                    $transaction->getType()
                );

                $isSuccess = TransactionStatus::SUCCESS()->equals(
                    $transaction->getStatus()
                );

                return (
                    false === $isDispatchFunds
                    && (
                        true === $isSuccess
                        || true === $this->isPendingAjustementPrice($transaction)
                    )
                );
            }
        );
    }

    public function getBalanceByOrderId(int $orderId): Money
    {
        return \array_reduce(
            $this->getOrderBalanceAccountableTransactions($orderId),
            function (Money $balanceTotal, $transaction): Money {
                $refundId = $transaction->getRefundId();
                $isRefundedAfterWithdrawalPeriod = false;
                if ($refundId !== null) {
                    $refundData = $this->refundRepository->get($refundId);
                    $isRefundedAfterWithdrawalPeriod = $refundData->isRefundedAfterWithdrawalPeriod();
                }

                if ($isRefundedAfterWithdrawalPeriod === true) {
                    return $balanceTotal;
                }

                return
                    TransactionType::isCreditType(
                        $transaction->getType()
                    )
                    ?  $balanceTotal->add($transaction->getAmount())
                    :  $balanceTotal->subtract($transaction->getAmount())
                ;
            },
            new Money(0)
        );
    }

    /**
     * This function is used to get order balance without taking into consideration if the refund was done before or after withdrawal period
     * For getting real order balance for accounting see getBalanceByOrderId(int $orderId)
     */
    public function getBalanceByOrderIdOnlyForRefundDetail(int $orderId): Money
    {
        return \array_reduce(
            $this->getOrderBalanceAccountableTransactions($orderId),
            function (Money $balanceTotal, $transaction): Money {
                return
                    TransactionType::isCreditType(
                        $transaction->getType()
                    )
                        ?  $balanceTotal->add($transaction->getAmount())
                        :  $balanceTotal->subtract($transaction->getAmount())
                    ;
            },
            new Money(0)
        );
    }

    public function hasCreditCardTransaction(int $orderId): bool
    {
        return $this->repository->countCreditCardTransactionByOrderId($orderId) > 0;
    }

    public function findOneByRefundId(int $refundId): ?Transaction
    {
        return $this->repository->getOneBy([
            'refundId' => $refundId,
        ]);
    }

    public function assertTransactionCanBeMarkAsPaid(Transaction $transaction): bool
    {
        $canMarkAsPaid = false;
        $transactionType = $transaction->getType();

        if ($transaction->isPending() === true && (($transactionType->equals(TransactionType::OFFLINE()) === true)
            || ($transactionType->equals(TransactionType::BANK_WIRE()) === true)
            || ($transactionType->equals(TransactionType::DIRECT_DEBIT())) === true)
        ) {
            $canMarkAsPaid = true;
        }

        return $canMarkAsPaid;
    }

    public function createDispatchFundsTransaction(
        int $orderId,
        TransactionType $type,
        TransactionStatus $transactionStatus,
        Money $amount,
        PaymentProcessorName $processorName,
        string $transactionReference
    ): Transaction {
        $transaction = new Transaction($orderId, $type, $transactionStatus, $amount);
        $transaction->setProcessorName((string) $processorName);
        $transaction->setTransactionReference($transactionReference);

        return $this->save($transaction);
    }

    public function retrieveTransaction(
        PaymentProcessorName $processorName,
        TransactionType $transactionType,
        int $orderId,
        string $transactionReference = null
    ): Transaction {
        $criteria = [
            'processorName' => $processorName,
            'orderId' => $orderId,
            'type' => $transactionType->getValue()
        ];

        if (true === \is_string($transactionReference)) {
            $criteria['transactionReference'] = $transactionReference;
        }

        $transactions = $this->repository->findBy(
            $criteria,
            ['updatedAt' => 'DESC']
        );

        if (\count($transactions) === 0) {
            throw new TransactionNotFound("Transaction not found.");
        }

        if (\count($transactions) > 1) {
            $this->logger->error(
                'TransactionService::retrieveTransaction: Multiple transactions found',
                [
                    'arguments' => \func_get_args(),
                    'transactions' => \array_map(
                        function (Transaction $transaction): array {
                            return $transaction->expose();
                        },
                        $transactions
                    ),
                ]
            );
        }

        return \reset($transactions);
    }

    /** @param string[] $filters */
    public function findExternalTransferTransactions(
        array $filters,
        ?int $page = null,
        ?int $resultsPerPage = null
    ): Paginator {
        return $this->repository->findExternalTransferTransactions($filters, $page, $resultsPerPage);
    }

    /**
     * Check all transactions status are successful
     *
     * @param Transaction[] $transactions
     * @return bool
     */
    public function isTransactionSuccessful(array $transactions): bool
    {
        foreach ($transactions as $transaction) {
            if (false === TransactionStatus::SUCCESS()->equals($transaction->getStatus())) {
                return false;
            }
        }

        return true;
    }

    public function getTransactionTransferDate(int $orderId, TransactionType $type): string
    {
        $transactions = $this->findByOrderId($orderId, $type);

        return \is_array($transactions) === true
        && \count($transactions) > 0
            ? $transactions[0]->getCreatedAt()->format('Y-m-d H:i') : '';
    }
}
