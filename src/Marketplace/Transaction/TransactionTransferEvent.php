<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Transaction;

use Symfony\Component\EventDispatcher\Event;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Money\Money;

/**
 * To be triggered on transfer failed
 */
class TransactionTransferEvent extends Event
{
    private int $companyId;
    private string $origin;
    private string $destination;
    private string $resultTransfer;
    private string $currency;
    private ?int $orderId;
    private int $amount;
    private TransactionStatus $transactionStatus;
    private string $reference;
    private PaymentProcessorName $processorName;

    public function __construct(
        ?int $orderId,
        int $companyId,
        string $origin,
        string $destination,
        string $resultTransfer,
        string $currency,
        int $amount,
        TransactionStatus $transactionStatus,
        string $reference,
        PaymentProcessorName $processorName
    ) {
        $this->orderId = $orderId;
        $this->companyId = $companyId;
        $this->origin = $origin;
        $this->destination = $destination;
        $this->resultTransfer = $resultTransfer;
        $this->currency = $currency;
        $this->amount = $amount;
        $this->transactionStatus = $transactionStatus;
        $this->reference = $reference;
        $this->processorName = $processorName;
    }

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    public function getOrigin(): string
    {
        return $this->origin;
    }

    public function getDestination(): string
    {
        return $this->destination;
    }

    public function getResultTransfer(): string
    {
        return $this->resultTransfer;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function getOrderId(): ?int
    {
        return $this->orderId;
    }

    public function getAmount(): int
    {
        return $this->amount;
    }

    public function getTransactionStatus(): TransactionStatus
    {
        return $this->transactionStatus;
    }

    public function getReference(): string
    {
        return $this->reference;
    }

    public function getProcessorName(): PaymentProcessorName
    {
        return $this->processorName;
    }
}
