<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Transaction\DoctrineType;

use Acelaya\Doctrine\Type\AbstractPhpEnumType;
use Wizacha\Marketplace\Transaction\TransactionType;

/**
 * Doctrine Type for TransactionType
 */
class TransactionTypeEnumType extends AbstractPhpEnumType
{
    protected $enumType = TransactionType::class;

    protected function getSpecificName(): string
    {
        return 'transaction_type';
    }
}
