<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Transaction\DoctrineType;

use Acelaya\Doctrine\Type\AbstractPhpEnumType;
use Wizacha\Marketplace\Transaction\TransactionStatus;

/**
 * Doctrine Type for TransactionStatus
 */
class TransactionStatusEnumType extends AbstractPhpEnumType
{
    protected $enumType = TransactionStatus::class;

    protected function getSpecificName(): string
    {
        return 'transaction_status';
    }
}
