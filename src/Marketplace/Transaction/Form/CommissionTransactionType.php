<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Transaction\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;
use Wizacha\Company;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Transaction\TransactionType;
use Wizacha\Registry;

class CommissionTransactionType extends AbstractType
{
    private CompanyService $companyService;

    /** @var mixed[]  */
    private ?array $companies = null;

    public function __construct(CompanyService $companyService)
    {
        $this->companyService = $companyService;
    }

    /** @param mixed[] $options */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        if ($this->companies === null) {
            $this->companies = $this->getCompanies();
        }

        $builder
            ->add(
                'companies',
                ChoiceType::class,
                [
                    'choices' => $this->companies[0],
                    'multiple' => true,
                    'required' => $this->companies[1]
                ]
            )
            ->add('period_start', HiddenType::class)
            ->add('period_end', HiddenType::class)
            ->add(
                'type',
                HiddenType::class,
                [
                    'data' => TransactionType::DISPATCH_FUNDS_TRANSFER_COMMISSION()->getValue(),
                    'attr' => ['class' => 'noneShowing'],
                ]
            )
            ->add(
                'save',
                SubmitType::class,
                [
                    'label' => 'export',
                    'attr' => ['class' => 'btn btn-primary btn-export'],
                ]
            );
    }

    /** @return string The prefix of the template block name */
    public function getBlockPrefix(): string
    {
        return "form";
    }

    /** @return array [string[], bool] */
    private function getCompanies(): array
    {
        $companyId = Company::runtimeID(AREA, $_SESSION, Registry::defaultInstance());
        $companyData = fn_get_company_data($companyId);

        if (\is_array($companyData) === false) {
            $isCompanyDisabled = false;
            $companyList = null;
            $statement = $this->companyService->getAllVendorsExceptNew();
            while ($company = $statement->fetch()) {
                $companyList[$company['company']] = $company['company_id'];
            }
        } else {
            $isCompanyDisabled = true;
            $companyList[$companyData['company']] = $companyData['company_id'];
        }

        return [$companyList, $isCompanyDisabled];
    }
}
