<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Transaction;

use MyCLabs\Enum\Enum;

/**
 * Manage type of transaction
 *
 * @method static TransactionType CREDITCARD()
 * @method static TransactionType BANK_WIRE()
 * @method static TransactionType TRANSFER()
 * @method static TransactionType REFUND()
 * @method static TransactionType REFUND_TRANSFER()
 * @method static TransactionType DIRECT_DEBIT()
 * @method static TransactionType OFFLINE()
 * @method static TransactionType DISPATCH_FUNDS_TRANSFER_VENDOR()
 * @method static TransactionType DISPATCH_FUNDS_TRANSFER_COMMISSION()
 * @method static TransactionType VENDOR_WITHDRAWAL()
 */

class TransactionType extends Enum
{
    # credit
    public const CREDITCARD = 'CREDITCARD';
    public const BANK_WIRE = 'BANK_WIRE';
    public const TRANSFER = 'TRANSFER';
    public const DIRECT_DEBIT = 'DIRECT_DEBIT';
    public const OFFLINE = 'OFFLINE';

    # debit
    public const REFUND = 'REFUND';
    public const REFUND_TRANSFER = 'REFUND_TRANSFER';
    public const DISPATCH_FUNDS_TRANSFER_VENDOR = 'DISPATCH_FUNDS_TRANSFER_VENDOR';
    public const DISPATCH_FUNDS_TRANSFER_COMMISSION = 'DISPATCH_FUNDS_TRANSFER_COMMISSION';
    public const VENDOR_WITHDRAWAL = 'VENDOR_WITHDRAWAL';

    final public function getTranslationKey(string $suffix = null): string
    {
        return
            \is_null($suffix)
            ? 'transaction_' . strtolower($this->getValue())
            : 'transaction_' . strtolower($this->getValue()) . '_' . $suffix
        ;
    }

    /**
     * @param static[] $types
     *
     * @return \Generator<static>
     */
    protected static function getValuesGenerator(array $types): \Generator
    {
        foreach ($types as $type) {
            yield $type->getKey() => $type;
        }
    }

    /**
     * @param static[] $types
     *
     * @return static[]
     */
    protected static function getValues(array $types): array
    {
        return \iterator_to_array(
            static::getValuesGenerator($types)
        );
    }

    /** @param static[] $types */
    protected static function inValues(self $type, array $types): bool
    {
        return
            \in_array(
                $type,
                static::getValues($types)
            )
        ;
    }

    /** @return static[] */
    public static function getCreditTypes(): array
    {
        return static::getValues(
            [
                static::CREDITCARD(),
                static::BANK_WIRE(),
                static::TRANSFER(),
                static::DIRECT_DEBIT(),
                static::OFFLINE(),
            ]
        );
    }
    public static function isCreditType(self $type): bool
    {
        return
            static::inValues(
                $type,
                static::getCreditTypes()
            )
        ;
    }

    /** @return static[] */
    public static function getDebitTypes(): array
    {
        return static::getValues(
            [
                static::REFUND(),
                static::REFUND_TRANSFER(),
                static::DISPATCH_FUNDS_TRANSFER_COMMISSION(),
                static::DISPATCH_FUNDS_TRANSFER_VENDOR(),
                static::VENDOR_WITHDRAWAL(),
            ]
        );
    }
    public static function isDebitType(self $type): bool
    {
        return
            static::inValues(
                $type,
                static::getDebitTypes()
            )
        ;
    }

    /** @return static[] */
    public static function getRefundedTypes(): array
    {
        return static::getValues(
            [
                static::REFUND(),
                static::REFUND_TRANSFER(),
            ]
        );
    }
    public static function isRefundType(self $type): bool
    {
        return
            static::inValues(
                $type,
                static::getRefundedTypes()
            )
        ;
    }

    /**
     * @return static[]
     */
    public static function getDispatchFundsTransferTypes(): array
    {
        return static::getValues(
            [
                static::DISPATCH_FUNDS_TRANSFER_VENDOR(),
                static::DISPATCH_FUNDS_TRANSFER_COMMISSION(),
            ]
        );
    }
    public static function isDispatchFundsTransferType(self $type): bool
    {
        return
            static::inValues(
                $type,
                static::getDispatchFundsTransferTypes()
            )
        ;
    }

    public static function getTransactionFilter(): array
    {
        return [
            static::DISPATCH_FUNDS_TRANSFER_VENDOR,
            static::DISPATCH_FUNDS_TRANSFER_COMMISSION,
            static::VENDOR_WITHDRAWAL,
        ];
    }
}
