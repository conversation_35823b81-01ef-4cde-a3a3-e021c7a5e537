<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Transaction;

use Rhumsaa\Uuid\Uuid;
use Wizacha\Marketplace\Traits;
use Wizacha\Money\Money;

/**
 * Transaction entity
 */
class Transaction
{
    use Traits\HasTimestamp;

    /** @var Uuid|null */
    protected $id;

    protected ?int $orderId;

    /** @var string|null */
    protected $transactionReference;

    /** @var string|null */
    protected $transactionLabel;

    /** @var TransactionType */
    protected $type;

    /** @var TransactionStatus */
    protected $status;

    /** @var Money */
    protected $amount;

    /** @var string|null */
    protected $processorName;

    /** @var mixed[]|null */
    protected $processorInformations;

    /** @var int|null */
    protected $refundId;

    protected ?string $origin;
    protected ?string $destination;
    protected ?string $currency;
    protected ?int $companyId;

    public function __construct(
        ?int $orderId,
        TransactionType $type,
        TransactionStatus $status,
        Money $amount,
        ?string $origin = null,
        ?string $destination = null,
        ?string $currency = null,
        ?int $companyId = null
    ) {
        $this
            ->setOrderId($orderId)
            ->setType($type)
            ->setStatus($status)
            ->setAmount($amount)
            ->setOrigin($origin)
            ->setDestination($destination)
            ->setCurrency($currency)
            ->setCompanyId($companyId);
    }

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getOrderId(): ?int
    {
        return $this->orderId;
    }

    public function getTransactionReference(): ?string
    {
        return $this->transactionReference;
    }

    public function setTransactionReference(?string $transactionReference): self
    {
        $this->transactionReference = $transactionReference;

        return $this;
    }

    public function getTransactionLabel(): ?string
    {
        return $this->transactionLabel;
    }

    public function setTransactionLabel(?string $transactionLabel): self
    {
        $this->transactionLabel = $transactionLabel;

        return $this;
    }

    public function getType(): TransactionType
    {
        return $this->type;
    }

    public function getStatus(): TransactionStatus
    {
        return $this->status;
    }

    public function setStatus(TransactionStatus $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getRefundId(): ?int
    {
        return $this->refundId;
    }

    public function setRefundId(?int $refundId): self
    {
        $this->refundId = $refundId;

        return $this;
    }

    public function getAmount(): Money
    {
        return $this->amount;
    }

    public function getProcessorName(): ?string
    {
        return $this->processorName;
    }

    public function setProcessorName(?string $processorName): self
    {
        $this->processorName = $processorName;

        return $this;
    }

    public function getProcessorInformations(): ?array
    {
        return $this->processorInformations;
    }

    /**
     * Note that this method will override existing value matching the provided key.
     *
     * @param string $key
     * @param mixed  $value Any value to store as serialized data.
     *
     * @return Transaction
     */
    public function addInformation(string $key, $value): self
    {
        $this->processorInformations[$key] = $value;

        return $this;
    }

    /** @param mixed[]|null $processorInformations */
    public function setProcessorInformations(?array $processorInformations): self
    {
        $this->processorInformations = $processorInformations;

        return $this;
    }

    public function isReady(): bool
    {
        return $this->getStatus()->equals(TransactionStatus::READY());
    }

    public function isPending(): bool
    {
        return $this->getStatus()->equals(TransactionStatus::PENDING());
    }

    public function isSuccess(): bool
    {
        return $this->getStatus()->equals(TransactionStatus::SUCCESS());
    }

    public function isFailed(): bool
    {
        return $this->getStatus()->equals(TransactionStatus::FAILED());
    }

    public function setOrderId(?int $orderId): self
    {
        $this->orderId = $orderId;

        return $this;
    }

    public function setType(TransactionType $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function setAmount(Money $amount): self
    {
        $this->amount = $amount;

        return $this;
    }

    public function getInformation(string $key): ?string
    {
        return \is_array($this->getProcessorInformations()) && \array_key_exists($key, $this->getProcessorInformations())
            ? (string) $this->getProcessorInformations()[$key]
            : null;
    }

    public function getLabel(): string
    {
        $coupon = $this->getInformation('coupon_marketplace_discount');

        return __(
            $this->getType()->getTranslationKey(\is_string($coupon) ? 'coupon' : null),
            \is_string($coupon) ? ['%s' => $coupon] : []
        );
    }

    public function getOrigin(): ?string
    {
        return $this->origin;
    }

    public function setOrigin(?string $origin): self
    {
        $this->origin = $origin;

        return $this;
    }

    public function getDestination(): ?string
    {
        return $this->destination;
    }

    public function setDestination(?string $destination): self
    {
        $this->destination = $destination;

        return $this;
    }

    public function getCurrency(): ?string
    {
        return $this->currency;
    }

    public function setCurrency(?string $currency): self
    {
        $this->currency = $currency;

        return $this;
    }

    public function getCompanyId(): ?int
    {
        return $this->companyId;
    }

    public function setCompanyId(?int $companyId): self
    {
        $this->companyId = $companyId;

        return $this;
    }

    public function expose(): array
    {
        return [
            "transactionId" => $this->getId() ? $this->getId()->toString() : 'not_persisted',
            "amount" => $this->getAmount()->getPreciseConvertedAmount(),
            "type" => $this->getType()->getValue(),
            "label" => $this->getLabel(),
            "status" => $this->getStatus()->getValue(),
            "transactionDate" => $this->getCreatedAt()->format(\DateTime::RFC3339),
            "processorName" => $this->getProcessorName(),
            "transactionReference" => $this->getTransactionReference(),
            "processorInformation" => $this->getProcessorInformations(),
        ];
    }

    public function exposeTransactionHistory(string $dateFormat = 'd/m/Y H:i:s'): array
    {
        $transaction = $this->expose();
        $transaction['transactionDate'] = $this->getCreatedAt()->format($dateFormat);

        return \array_merge(
            $transaction,
            [
                'origin' => $this->getOrigin(),
                'destination' => $this->getDestination(),
                'currency' => $this->getCurrency(),
                'order_id' => $this->getOrderId(),
            ]
        );
    }
}
