<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Transaction;

use MyCLabs\Enum\Enum;

/**
 * Class TransactionsTransferEventType
 * @package Wizacha\Marketplace\Transaction
 *
 * @method static TransactionsTransferEventType DISPATCH_FUNDS_TRANSFER_VENDOR()
 * @method static TransactionsTransferEventType DISPATCH_FUNDS_TRANSFER_COMMISSION()
 * @method static TransactionsTransferEventType VENDOR_WITHDRAWAL()
 */
class TransactionsTransferEventType extends Enum
{
    public const DISPATCH_FUNDS_TRANSFER_VENDOR = 'vendor.transfer';
    public const DISPATCH_FUNDS_TRANSFER_COMMISSION = 'vendor.commission';
    public const VENDOR_WITHDRAWAL = 'vendor.withdrawal';
}
