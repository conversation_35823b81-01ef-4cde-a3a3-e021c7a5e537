<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Transaction;

use MyCLabs\Enum\Enum;

/**
 * Manage status of transaction
 *
 * @method static TransactionStatus READY()
 * @method static TransactionStatus PENDING()
 * @method static TransactionStatus SUCCESS()
 * @method static TransactionStatus FAILED()
 */
class TransactionStatus extends Enum
{
    /**
     * Payment has been instantiate with the PSP but still need some actions,
     * eg: fill the card form, required more authorizations, ...
     */
    public const READY = 'READY';

    /**
     * Everything is OK, no more actions are required from the buyer.
     * Usefull for Authorization / Capture, to indicate: Payment has been authorized and
     * the PSP is waiting for the capture order.
     */
    public const PENDING = 'PENDING';

    /**
     * Transaction is successful.
     */
    public const SUCCESS = 'SUCCESS';

    /**
     * Something went wrong and money won't get to PSP.
     */
    public const FAILED = 'FAILED';
}
