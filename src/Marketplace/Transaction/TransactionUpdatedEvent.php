<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Transaction;

use Symfony\Component\EventDispatcher\Event;

/**
 * To be triggered after a transaction update
 */
class TransactionUpdatedEvent extends Event
{
    /** @var Transaction */
    private $transaction;

    public function __construct(Transaction $transaction)
    {
        $this->transaction = $transaction;
    }

    public function getTransaction(): Transaction
    {
        return $this->transaction;
    }
}
