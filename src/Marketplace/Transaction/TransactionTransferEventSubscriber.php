<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Transaction;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class TransactionTransferEventSubscriber implements EventSubscriberInterface
{
    private TransactionService $transactionService;

    public function __construct(
        TransactionService $transactionService
    ) {
        $this->transactionService = $transactionService;
    }

    /** @return string[] */
    public static function getSubscribedEvents(): array
    {
        return [
            TransactionsTransferEventType::DISPATCH_FUNDS_TRANSFER_VENDOR()->getValue()  => ['onVendorTransfer', 0],
            TransactionsTransferEventType::DISPATCH_FUNDS_TRANSFER_COMMISSION()->getValue()  => ['onCommissionTransfer', 0],
            TransactionsTransferEventType::VENDOR_WITHDRAWAL()->getValue()  => ['onVendorWithdrawal', 0],
        ];
    }

    public function onVendorTransfer(TransactionTransferEvent $event): void
    {
        $this->transactionService->createExternalTransferTransaction(
            $event->getOrderId(),
            $event->getCompanyId(),
            $event->getAmount(),
            $event->getOrigin(),
            $event->getDestination(),
            $event->getResultTransfer(),
            $event->getCurrency(),
            TransactionType::DISPATCH_FUNDS_TRANSFER_VENDOR(),
            $event->getTransactionStatus(),
            $event->getReference(),
            $event->getProcessorName()
        );
    }

    public function onCommissionTransfer(TransactionTransferEvent $event): void
    {
        $this->transactionService->createExternalTransferTransaction(
            $event->getOrderId(),
            $event->getCompanyId(),
            $event->getAmount(),
            $event->getOrigin(),
            $event->getDestination(),
            $event->getResultTransfer(),
            $event->getCurrency(),
            TransactionType::DISPATCH_FUNDS_TRANSFER_COMMISSION(),
            $event->getTransactionStatus(),
            $event->getReference(),
            $event->getProcessorName()
        );
    }

    public function onVendorWithdrawal(TransactionTransferEvent $event): void
    {
        $this->transactionService->createExternalTransferTransaction(
            $event->getOrderId(),
            $event->getCompanyId(),
            $event->getAmount(),
            $event->getOrigin(),
            $event->getDestination(),
            $event->getResultTransfer(),
            $event->getCurrency(),
            TransactionType::VENDOR_WITHDRAWAL(),
            $event->getTransactionStatus(),
            $event->getReference(),
            $event->getProcessorName()
        );
    }
}
