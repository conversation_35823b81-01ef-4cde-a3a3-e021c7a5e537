Wizacha\Marketplace\Transaction\Transaction:
    type: entity
    table: order_transactions
    id:
        id:
            type: ramsey_uuid
            generator:
                strategy: UUID
    fields:
        orderId:
            type: integer
            nullable: true
        transactionReference:
            type: string
            nullable: true
        transactionLabel:
            type: string
            nullable: true
        amount:
            type: money
            nullable: false
        type:
            type: php_enum_transaction_type
            nullable: false
        status:
            type: php_enum_transaction_status
            nullable: false
        processorName:
            type: string
            nullable: true
        processorInformations:
            type: array
            nullable: true
        createdAt:
            type: datetime_immutable
            nullable: false
        updatedAt:
            type: datetime_immutable
            nullable: true
        refundId:
            type: integer
            nullable: true
        origin:
            type: string
            nullable: true
        destination:
            type: string
            nullable: true
        currency:
            type: string
            nullable: true
        companyId:
            type: integer
            nullable: true
