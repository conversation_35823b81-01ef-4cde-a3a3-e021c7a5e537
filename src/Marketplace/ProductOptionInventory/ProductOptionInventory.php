<?php

/**
 *  <AUTHOR> DevTeam <<EMAIL>>
 *  @copyright   Copyright (c) Wizacha
 *  @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\ProductOptionInventory;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Wizacha\Marketplace\PriceTier\PriceTier;
use Wizacha\Marketplace\ProductOptionInventory\Service\ProductOptionInventoryService;
use Wizacha\Money\Money;

class ProductOptionInventory
{
    /** @var null|int */
    protected $id;

    /** @var Collection|PriceTier[] */
    protected $priceTiers;

    /** @var null|string */
    protected $combination;

    /** @var null|int */
    protected $productId;

    /** @var null|string */
    protected $productCode;

    /** @var null|int */
    protected $combinationHash;

    /** @var null|int */
    protected $amount;

    /** @var null|string */
    protected $temp;

    /** @var null|int */
    protected $position;

    /** @var null|Money */
    protected $wPrice;

    /** @var null|Money */
    protected $crossedOutPrice;

    /** @var null|string */
    protected $affiliateLink;

    /** @var null|string */
    protected $declinationId;

    /** @var null|string */
    protected $supplierReference;

    /** @var null|bool */
    protected $infiniteStock;

    public function __construct()
    {
        $this->priceTiers = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setPriceTiers(iterable $priceTiers): self
    {
        $this->clearPriceTiers();
        /** @var PriceTier $priceTier */
        foreach ($priceTiers as $priceTier) {
            $this->addPriceTier($priceTier);
        }

        return $this;
    }

    /**
     * Pour le typage de PhpStorm
     * @return Collection|PriceTier[]
     */
    public function getPriceTiers(): Collection
    {
        return $this->priceTiers;
    }

    public function clearPriceTiers(): self
    {
        foreach ($this->getPriceTiers() as $priceTier) {
            $this->removePriceTier($priceTier);
        }

        return $this;
    }

    public function addPriceTier(PriceTier $priceTier): self
    {
        if ($this->priceTiers->contains($priceTier) === false) {
            $this->priceTiers->add($priceTier);
            $priceTier->setProductOptionInventory($this);
        }

        return $this;
    }

    public function removePriceTier(PriceTier $priceTier): self
    {
        if ($this->priceTiers->contains($priceTier)) {
            $this->priceTiers->removeElement($priceTier);
            $priceTier->setProductOptionInventory(null);
        }

        return $this;
    }

    public function getCombination(): ?string
    {
        return $this->combination;
    }

    public function setCombination(?string $combination): self
    {
        $this->combination = $combination;

        return $this;
    }

    public function setProductId(?int $productId): self
    {
        $this->productId = $productId;

        return $this;
    }

    public function getProductId(): ?int
    {
        return $this->productId;
    }

    public function setProductCode(?string $productCode): self
    {
        $this->productCode = $productCode;

        return $this;
    }

    public function getProductCode(): ?string
    {
        return $this->productCode;
    }

    public function setCombinationHash(?int $combinationHash): self
    {
        $this->combinationHash = $combinationHash;

        return $this;
    }

    public function getCombinationHash(): ?int
    {
        return $this->combinationHash;
    }

    public function setAmount(?int $amount): self
    {
        $this->amount = $amount;

        return $this;
    }

    public function getAmount(): ?int
    {
        return $this->amount;
    }

    public function setTemp(?string $temp): self
    {
        $this->temp = $temp;

        return $this;
    }

    public function getTemp(): ?string
    {
        return $this->temp;
    }

    public function setPosition(?int $position): self
    {
        $this->position = $position;

        return $this;
    }

    public function getPosition(): ?int
    {
        return $this->position;
    }

    public function setWPrice(?Money $wPrice): self
    {
        $this->wPrice = $wPrice;

        return $this;
    }

    public function getWPrice(): ?Money
    {
        return \is_float($this->wPrice) ? Money::fromVariable($this->wPrice) : null;
    }

    public function setCrossedOutPrice(?Money $crossedOutPrice): self
    {
        $this->crossedOutPrice = $crossedOutPrice;

        return $this;
    }

    public function getCrossedOutPrice(): ?Money
    {
        return \is_float($this->crossedOutPrice) ? Money::fromVariable($this->crossedOutPrice) : null;
    }

    public function setAffiliateLink(?string $affiliateLink): self
    {
        $this->affiliateLink = $affiliateLink;

        return $this;
    }

    public function getAffiliateLink(): ?string
    {
        return $this->affiliateLink;
    }

    public function setDeclinationId(?string $declinationId): self
    {
        $this->declinationId = $declinationId;

        return $this;
    }

    public function getDeclinationId(): ?string
    {
        return $this->declinationId;
    }

    public function setInfiniteStock(?bool $infiniteStock): self
    {
        $this->infiniteStock = $infiniteStock;

        return $this;
    }

    public function getInfiniteStock(): ?bool
    {
        return $this->infiniteStock;
    }

    public function getSupplierReference(): ?string
    {
        return $this->supplierReference;
    }

    public function setSupplierReference(?string $supplierReference): self
    {
        $this->supplierReference = $supplierReference;

        return $this;
    }

    public function expose(): array
    {
        return [
            'product_id' => $this->getProductId(),
            'product_code' => $this->getProductCode(),
            'combination_hash' => $this->getCombinationHash(),
            'combination' => $this->getCombination(),
            'amount' => $this->getAmount(),
            'temp' => $this->getTemp(),
            'position' => $this->getPosition(),
            'w_price' => ($this->getWPrice() instanceof Money) ? $this->getWPrice()->getConvertedAmount() : null,
            'crossed_out_price' => ($this->getCrossedOutPrice() instanceof Money) ? $this->getCrossedOutPrice()->getConvertedAmount() : null,
            'affiliate_link' => $this->getAffiliateLink(),
            'declination_id' => $this->getDeclinationId(),
            'infinite_stock' => $this->getInfiniteStock(),
            'supplier_reference' =>  (\is_null($this->getSupplierReference()) || \strlen($this->getSupplierReference()) === 0) ? fn_get_product_supplier_ref($this->productId) : $this->getSupplierReference()
        ];
    }
}
