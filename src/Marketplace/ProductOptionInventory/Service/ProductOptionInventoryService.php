<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\ProductOptionInventory\Service;

use Wizacha\Marketplace\PIM\Product\Product;
use Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory;
use Wizacha\Marketplace\ProductOptionInventory\Repository\ProductOptionInventoryRepository;

class ProductOptionInventoryService
{
    /** @var ProductOptionInventoryRepository */
    protected $productOptionInventoryRepository;

    public function __construct(ProductOptionInventoryRepository $productOptionInventoryRepository)
    {
        $this->productOptionInventoryRepository = $productOptionInventoryRepository;
    }

    public function removeAll(Product $product): self
    {
        $productOptionInventory = $this->productOptionInventoryRepository->findByProductId($product->getId());

        /** @var ProductOptionInventory $inventory */
        foreach ($productOptionInventory as $inventory) {
            $this->productOptionInventoryRepository->remove($inventory);
        }

        return $this;
    }

    /**
     * Get an array of combinations "as a string" ("optionId"_"variantId") from combinations as an array of arrays
     *
     * @return string[]
     */
    public function allowedOptionsVariantsToString(array $allowedOptionsVariants): array
    {
        foreach ($allowedOptionsVariants as $key => $values) {
            $allowedOptionsVariants[$key] = array_keys($values);
        }

        $max = $this->countMaxCombinations($allowedOptionsVariants);

        $combinations = [];
        for ($i = 0; $i < $max; $i++) {
            $combinations[] = $this->getCombinationAsString($allowedOptionsVariants, $i);
        }
        natcasesort($combinations);

        return array_values($combinations);
    }

    /**
     * Get the maximum number of possible combinations from an array of options variants
     *
     * Determine max integer
     */
    protected function countMaxCombinations(array $allowedOptionsVariants): int
    {
        return array_reduce(
            $allowedOptionsVariants,
            function (int $carry, array $item): int {
                $carry *= \count($item);

                return $carry;
            },
            1
        );
    }

    /** Convert allowed options variants to a combination */
    protected function getCombinationAsString(array $allowedOptionsVariants, int $combinationIndex): string
    {
        $combinationArray = [];
        foreach ($allowedOptionsVariants as $key => $values) {
            $base = \count($values);
            $mod = $combinationIndex % $base;
            $combinationIndex = intdiv($combinationIndex, $base);
            $combinationArray[$key] = $allowedOptionsVariants[$key][$mod];
        }

        $combination = [];
        foreach ($combinationArray as $key => $val) {
            $combination[] = $key;
            $combination[] = $val;
        }

        return implode('_', $combination);
    }
}
