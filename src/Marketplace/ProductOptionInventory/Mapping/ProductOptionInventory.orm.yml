Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory:
    type: entity
    table: product_options_inventory
    id:
        id:
            type: integer
            generator:
                strategy: AUTO
            options:
                unsigned: true
    fields:
        productId:
            type: integer
            options:
                unsigned: true
                default: 0
        productCode:
            type: string
            length: 32
            options:
                default: ''
        combinationHash:
            type: integer
            options:
                unsigned: true
                default: 0
        combination:
            type: string
            length: 255
        amount:
            type: integer
            options:
                default: 0
        temp:
            type: string
            length: 1
            options:
                default: 'N'
        position:
            type: integer
            options:
                default: 0
        wPrice:
            type: float
        crossedOutPrice:
            type: float
        affiliateLink:
            type: text
        declinationId:
            type: string
            length: 36
        infiniteStock:
            type: boolean
            options:
                default: false
        supplierReference:
            type: string
            length: 255
    oneToMany:
        priceTiers:
            targetEntity: Wizacha\Marketplace\PriceTier\PriceTier
            mappedBy: productOptionInventory
            cascade:
                - remove
                - persist
