<?php

/**
 *  <AUTHOR> DevTeam <<EMAIL>>
 *  @copyright   Copyright (c) Wizacha
 *  @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\ProductOptionInventory\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory;

class ProductOptionInventoryRepository extends ServiceEntityRepository
{
    public function save(ProductOptionInventory $productOptionInventory): ProductOptionInventory
    {
        $this->getEntityManager()->persist($productOptionInventory);
        $this->getEntityManager()->flush($productOptionInventory);

        return $productOptionInventory;
    }

    public function remove(ProductOptionInventory $productOptionInventory): self
    {
        $this->getEntityManager()->remove($productOptionInventory);
        $this->getEntityManager()->flush($productOptionInventory);

        return $this;
    }

    /**
     * @param string $combination
     * @param int $productId
     *
     * @return ProductOptionInventory|null
     *
     * @throws \Doctrine\ORM\NonUniqueResultException
     */
    public function findOneByCombination(string $combination, int $productId): ?ProductOptionInventory
    {
        return $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->select("poi")
            ->from(ProductOptionInventory::class, 'poi')
            ->where("poi.combination = ?0")
            ->andWhere("poi.productId = ?1")
            ->setParameters([
                $combination,
                $productId,
            ])
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /** @return ProductOptionInventory[] */
    public function findByProductId(int $productId): array
    {
        return $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->select("poi")
            ->from(ProductOptionInventory::class, 'poi')
            ->where("poi.productId = ?0")
            ->setParameter(0, $productId)
            ->getQuery()
            ->getResult();
    }

    public function findOneById($productOptionId): ?ProductOptionInventory
    {
        return $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->select("poi")
            ->from(ProductOptionInventory::class, 'poi')
            ->where("poi.id = ?0")
            ->setParameters([
                $productOptionId,
            ])
            ->getQuery()
            ->getOneOrNullResult();
    }
}
