<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\InvoicingSettings;

class InvoicingSettingsService
{
    private InvoicingSettingsRepository $invoicingSettingsRepository;

    public function __construct(InvoicingSettingsRepository $invoicingSettingsRepository)
    {
        $this->invoicingSettingsRepository = $invoicingSettingsRepository;
    }

    public function getMarketplaceInvoicingDisplayed(): bool
    {
        return $this->invoicingSettingsRepository->getMarketplaceInvoicingDisplayed();
    }

    public function updateMarketplaceInvoicingDisplayed(bool $value): void
    {
        $this->invoicingSettingsRepository->updateMarketplaceInvoicingDisplayed($value);
    }

    /** set do_not_create_invoice = true and w_invoice_number = null for being processed orders after changed company invoicing_disabled to true */
    public function resetOrdersAfterDisplayedInvoicing(int $companyId): void
    {
        $this->invoicingSettingsRepository->resetOrdersAfterDisplayedInvoicing($companyId);
    }
}
