<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\InvoicingSettings;

use Doctrine\DBAL\Connection;
use Wizacha\OrderStatus as LegacyOrderStatus;

class InvoicingSettingsRepository
{
    private const TABLE = 'cscart_settings_objects';
    private const INVOICING_DISPLAYED_COLUMN = 'invoicing_disabled';
    private Connection $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
    }

    public function getMarketplaceInvoicingDisplayed(): bool
    {
        $queryBuilder = $this->connection->createQueryBuilder();

        $marketplaceInvoicingDisplayed = $queryBuilder
            ->select('value')
            ->from(self::TABLE)
            ->where($queryBuilder->expr()->eq('name', ':name'))
            ->setParameter('name', self::INVOICING_DISPLAYED_COLUMN)
            ->execute()
            ->fetchColumn()
        ;

        return $marketplaceInvoicingDisplayed === 'Y';
    }

    public function updateMarketplaceInvoicingDisplayed(bool $value): void
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->update(self::TABLE)
            ->set('value', ':value')
            ->where($queryBuilder->expr()->eq('name', ':name'))
            ->setParameter('name', self::INVOICING_DISPLAYED_COLUMN)
            ->setParameter('value', $value === true ? "Y" : "N")
            ->execute()
        ;

        //reset all company with the new value of marketplace InvoicingDisplayed
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder
            ->update('cscart_companies')
            ->set('invoicing_disabled', ':invoicingDisabled')
            ->set('invoicing_disabled_by_admin', ':invoicingDisabledByAdmin')
            ->setParameter('invoicingDisabled', (int) $value)
            ->setParameter('invoicingDisabledByAdmin', 0)
            ->execute()
        ;

        if ($value === true) {
            $sql = 'UPDATE cscart_orders SET do_not_create_invoice = "1", w_invoice_number = NULL WHERE status IN (:status)';
            $this->connection->executeQuery(
                $sql,
                [
                    'status' => [LegacyOrderStatus::STANDBY_BILLING, LegacyOrderStatus::PROCESSING_SHIPPING]
                ],
                [
                    'status' => Connection::PARAM_STR_ARRAY
                ]
            );
        }
    }

    //set do_not_create_invoice = 1 and w_invoice_number = null for all orders with status = STANDBY_BILLING or PROCESSING_SHIPPING
    public function resetOrdersAfterDisplayedInvoicing(int $companyId): void
    {
        $sql = 'UPDATE cscart_orders SET do_not_create_invoice = "1", w_invoice_number = NULL WHERE status IN (:status) AND company_id = :companyId';

        $this->connection->executeQuery(
            $sql,
            [
                'status' => [LegacyOrderStatus::STANDBY_BILLING, LegacyOrderStatus::PROCESSING_SHIPPING],
                'companyId' => $companyId,
            ],
            [
                'status' => Connection::PARAM_STR_ARRAY,
            ]
        );
    }
}
