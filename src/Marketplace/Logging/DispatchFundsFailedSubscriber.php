<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Logging;

use Psr\Log\LoggerInterface;
use <PERSON>ymfony\Component\EventDispatcher\EventSubscriberInterface;
use Wizacha\Marketplace\Payment\Event\DispatchFundsFailedEvent;

/**
 * Log domain event DispatchFundsFailedEvent as an error
 */
class DispatchFundsFailedSubscriber implements EventSubscriberInterface
{
    /**
     * @var LoggerInterface
     */
    private $logger;

    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    public static function getSubscribedEvents()
    {
        return [
            DispatchFundsFailedEvent::class => ['onDispatchFundsFailedEvent', 0],
        ];
    }

    public function onOrderDispatchFundsFailedEvent(DispatchFundsFailedEvent $event)
    {
        $this->logger->error('DispatchF<PERSON> failed', [
            'orderId' => $event->getOrder()->getId(),
            'commission' => $event->getCommission()->getConvertedAmount()
        ]);
    }
}
