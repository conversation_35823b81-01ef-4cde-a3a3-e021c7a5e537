Wizacha\Marketplace\MailingList\Recipient\Recipient:
    type: entity

    id:
        email:
            type: string

    manyToMany:
        mailingLists:
            targetEntity: Wizacha\Marketplace\MailingList\MailingList
            joinTable:
                name: mailing_list_recipients
                joinColumns:
                    email:
                        referencedColumnName: email
                inverseJoinColumns:
                    mailing_list_id:
                        referencedColumnName: id
