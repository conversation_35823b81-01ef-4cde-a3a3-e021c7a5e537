<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\MailingList;

/**
 * Lists to which mailing list an email is subscribed to.
 */
class Subscriptions
{
    /**
     * @var MailingList[]
     */
    private $mailingLists;

    /**
     * Map of mailing list ID to boolean (status).
     *
     * @var boolean[]
     */
    private $status = [];

    /**
     * @param MailingList[] $mailingLists
     * @param array $status
     */
    public function __construct(array $mailingLists, array $status)
    {
        $this->mailingLists = $mailingLists;
        $this->status = $status;
    }

    /**
     * @return MailingList[]
     */
    public function getMailingLists(): array
    {
        return $this->mailingLists;
    }

    public function isSubscribedTo(MailingList $mailingList): bool
    {
        return $this->status[$mailingList->getId()];
    }
}
