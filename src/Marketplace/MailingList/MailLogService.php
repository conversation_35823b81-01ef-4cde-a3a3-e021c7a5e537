<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\MailingList;

use Psr\Log\LoggerInterface;

class MailLogService
{
    /** LoggerInterface */
    protected $logger;

    /** string */
    protected $logLevel;

    private const VALID_LOG_LEVEL_ = [
        'emergency',
        'alert',
        'critical',
        'error',
        'warning',
        'notice',
        'info',
        'debug',
    ];

    public function __construct(LoggerInterface $logger, string $logLevel)
    {
        $this->logger = $logger;
        $this->logLevel = $logLevel;
    }

    public function logger(string $message, array $context): void
    {
        if (\strlen($this->logLevel) > 0  && $this->isValidLogLevel() === true) {
            $this->logger->log($this->logLevel, $message, $context);
        }
    }

    private function isValidLogLevel(): bool
    {
        return \in_array($this->logLevel, self::VALID_LOG_LEVEL_, true) === true;
    }
}
