<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\MailingList;

use Doctrine\DBAL\Exception\UniqueConstraintViolationException;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\EventDispatcher\LegacyEventDispatcherProxy;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\MailingList\Exception\AlreadyRegisteredException;
use Wizacha\Marketplace\MailingList\Recipient\Recipient;

/**
 * Manages mailing lists and subscriptions.
 */
class MailingListService
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    /**
     * @var EventDispatcherInterface
     */
    private $eventDispatcher;

    public function __construct(EntityManagerInterface $entityManager, EventDispatcherInterface $eventDispatcher)
    {
        $this->entityManager = $entityManager;
        $this->eventDispatcher = LegacyEventDispatcherProxy::decorate($eventDispatcher);
    }

    /**
     * @return MailingList[]
     */
    public function getMailingLists()
    {
        return $this->entityManager->getRepository(MailingList::class)->findAll();
    }

    /**
     * @throws NotFound
     */
    public function getMailingList(int $id): MailingList
    {
        if ($mailinglist = $this->entityManager->getRepository(MailingList::class)->find($id)) {
            return $mailinglist;
        }

        throw NotFound::fromId('MailingList', $id);
    }

    /**
     * @return Recipient[]
     */
    public function getSubscribers(): array
    {
        $qb = $this->entityManager->createQueryBuilder();
        $qb->select('recipient')->from(Recipient::class, 'recipient')
            ->join('recipient.mailingLists', 'list');

        return $qb->getQuery()->getResult();
    }

    /**
     * Subscribe an email to a mailing list.
     *
     * @param string $email
     * @param int $mailingListId
     *
     * @throws NotFound Mailing list not found
     * @throws AlreadyRegisteredException
     */
    public function subscribe($email, $mailingListId)
    {
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new \InvalidArgumentException('The email must be a valid email address');
        }

        // Load or create the recipient
        /** @var Recipient */
        $recipient = $this->entityManager->find(Recipient::class, $email);
        if (!$recipient) {
            $recipient = new Recipient($email);
            $this->entityManager->persist($recipient);
        }

        $mailingList = $this->entityManager->find(MailingList::class, $mailingListId);
        if (!$mailingList) {
            throw new NotFound(sprintf('Cannot subscribe %s to mailing list %d, mailing list not found', $email, $mailingListId));
        }

        $recipient->subscribe($mailingList);

        try {
            $this->entityManager->flush();
        } catch (UniqueConstraintViolationException $e) {
            throw new AlreadyRegisteredException("The given email address has already subscribed to the mailing list");
        }

        $this->eventDispatcher->dispatch(new SubscriptionEvent($mailingListId, $email), SubscriptionEvent::SUBSCRIBED);
    }

    /**
     * Unsubscribe an email from a mailing list.
     *
     * @param string $email
     * @param int $mailingListId
     */
    public function unsubscribe($email, $mailingListId)
    {
        /** @var Recipient */
        $recipient = $this->entityManager->find(Recipient::class, $email);
        if (!$recipient) {
            return;
        }
        $mailingList = $this->entityManager->find(MailingList::class, $mailingListId);
        if (!$mailingList) {
            return;
        }

        $recipient->unsubscribe($mailingList);
        $this->entityManager->flush();

        $this->eventDispatcher->dispatch(new SubscriptionEvent($mailingListId, $email), SubscriptionEvent::UNSUBSCRIBED);
    }

    /**
     * @param string $email
     */
    public function getSubscriptions($email): Subscriptions
    {
        $mailingLists = $this->getMailingLists();

        $status = [];
        foreach ($mailingLists as $mailingList) {
            $status[$mailingList->getId()] = false;
        }

        $qb = $this->entityManager->createQueryBuilder();
        $qb->select('list.id')->from(Recipient::class, 'recipient')
            ->join('recipient.mailingLists', 'list')
            ->where('recipient.email = :email');
        $qb->setParameter('email', $email);
        foreach ($qb->getQuery()->getResult() as $row) {
            $status[$row['id']] = true;
        }

        return new Subscriptions($mailingLists, $status);
    }

    /**
     * @param string $name
     */
    public function createMailingList($name): MailingList
    {
        $mailingList = new MailingList($name);
        $this->entityManager->persist($mailingList);
        $this->entityManager->flush();

        return $mailingList;
    }
}
