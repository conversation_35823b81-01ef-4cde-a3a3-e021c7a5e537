<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\MailingList;

use Symfony\Contracts\EventDispatcher\Event;

class SubscriptionEvent extends Event
{
    public const SUBSCRIBED = 'mailing_list.subscribed';
    public const UNSUBSCRIBED = 'mailing_list.unsubscribed';

    /**
     * @var int
     */
    private $mailingListId;

    /**
     * @var string
     */
    private $email;

    /**
     * @param int $mailingListId
     * @param string $email
     */
    public function __construct($mailingListId, $email)
    {
        $this->mailingListId = $mailingListId;
        $this->email = $email;
    }

    /**
     * @return int
     */
    public function getMailingListId()
    {
        return $this->mailingListId;
    }

    /**
     * @return string
     */
    public function getEmail()
    {
        return $this->email;
    }
}
