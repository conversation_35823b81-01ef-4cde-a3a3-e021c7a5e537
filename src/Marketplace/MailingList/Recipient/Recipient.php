<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\MailingList\Recipient;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Wizacha\Marketplace\MailingList\MailingList;

class Recipient
{
    /**
     * @var string
     */
    private $email;

    /**
     * @var Collection|MailingList[]
     */
    private $mailingLists;

    public function __construct($email)
    {
        $this->email = $email;
        $this->mailingLists = new ArrayCollection();
    }

    public function subscribe(MailingList $mailingList)
    {
        $this->mailingLists->add($mailingList);
    }

    public function unsubscribe(MailingList $mailingList)
    {
        $this->mailingLists->removeElement($mailingList);
    }

    /**
     * @return string
     */
    public function getEmail()
    {
        return $this->email;
    }
}
