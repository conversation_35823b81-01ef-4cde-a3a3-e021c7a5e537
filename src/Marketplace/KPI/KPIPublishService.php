<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\KPI;

use GuzzleHttp\Client;
use GuzzleHttp\ClientInterface;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\RequestOptions;
use Wizacha\AppBundle\Validator\UrlValidatorInterface;
use Wizacha\Marketplace\Company\CompanyRepository;
use Wizacha\Marketplace\Order\Repository\OrderRepository;
use Wizacha\Marketplace\Payment\Processor\Exception\InvalidPaymentProcessorConfigurationException;
use Wizacha\Marketplace\Payment\Processor\ProcessorService;
use Wizacha\Marketplace\PIM\Product\ProductRepository;

class KPIPublishService
{
    private string $configKPIWebhookURL;
    private string $marketplaceProjectName;
    private string $configuredProcessorName;
    private UrlValidatorInterface $urlValidator;
    private ProcessorService $processorService;
    private ProductRepository $productRepository;
    private CompanyRepository $companyRepository;
    private OrderRepository $orderRepository;
    private ClientInterface $client;

    /**
     * @param string $configKPIWebhookURL
     * @param string $marketplaceProjectName
     * @param UrlValidatorInterface $urlValidator
     * @param ProcessorService $processorService
     * @param ProductRepository $productRepository
     * @param CompanyRepository $companyRepository
     * @param OrderRepository $orderRepository
     */
    public function __construct(
        string $configKPIWebhookURL,
        string $marketplaceProjectName,
        UrlValidatorInterface $urlValidator,
        ProcessorService $processorService,
        ProductRepository $productRepository,
        CompanyRepository $companyRepository,
        OrderRepository $orderRepository
    ) {
        $this->configKPIWebhookURL = $configKPIWebhookURL;
        $this->marketplaceProjectName = $marketplaceProjectName;
        $this->urlValidator = $urlValidator;
        $this->processorService = $processorService;
        $this->productRepository = $productRepository;
        $this->companyRepository = $companyRepository;
        $this->orderRepository = $orderRepository;
    }

    public function getConfigKPIWebhookURL(): string
    {
        return $this->configKPIWebhookURL;
    }

    /**
     * Called in the tests to use the mock instead of the real object
     * @param ClientInterface $client
     */
    public function setClient(ClientInterface $client)
    {
        $this->client = $client;
    }

    /**
     * Checks the KPI parameters:
     * - webhook url is not empty (feature flag is disabled)
     * - webhook url is valid
     * - payment processor list is not empty
     * - only one payment processor must be configured
     */
    public function checkKPIParameters()
    {
        if (empty($this->configKPIWebhookURL)) {
            throw new \InvalidArgumentException('Publish KPI feature is disabled');
        }

        if ($this->urlValidator->isUrlValid($this->configKPIWebhookURL) === false) {
            throw new \InvalidArgumentException('Invalid KPI Webhook URL');
        }

        $configuredProcessorNames = $this->processorService->getConfiguredProcessorName();
        if (empty($configuredProcessorNames)) {
            throw new InvalidPaymentProcessorConfigurationException();
        }

        if (\count($configuredProcessorNames) > 1) {
            throw new InvalidPaymentProcessorConfigurationException();
        }

        $this->configuredProcessorName = $configuredProcessorNames[0];
    }

    /**
     * Generates KPI in array format as follows:
     * <pre>
     * [
     *   "project_name" => "My Project",
     *   "psp" => "lemonway",
     *   "nb_products" => 11551,
     *   "nb_vendors" => 88,
     *   "orders" => [
     *     [
     *       "year" => 2021,
     *       "month" => 4,
     *       "orders" => 102,
     *       "items" => 103
     *     ],
     *     [
     *       "year" => 2021,
     *       "month" => 5,
     *       "orders" => 96,
     *       "items" => 108
     *     ]
     *   ]
     * ]
     * </pre>
     * @return array
     */
    public function generateKPIArray(): array
    {
        $nbProducts = $this->productRepository->getNbProductsApproved();
        $nbVendors = $this->companyRepository->getNbCompaniesEnabled();
        $orders = $this->orderRepository->getOrdersStatsGroupedByYearAndMonth();

        foreach ($orders as $key => $order) {
            $orders[$key] = array_map('intval', $order);
        }

        return [
            'project_name' => $this->marketplaceProjectName,
            'psp' => $this->configuredProcessorName,
            'nb_products' => $nbProducts,
            'nb_vendors' => $nbVendors,
            'orders' => $orders,
        ];
    }

    /**
     * Sends the previously generated KPI JSON to the configured webhook URL.
     * The function generateKPIJSON must be called before and its result passed as argument of this function.
     *
     * @param array $requestBody The KPI as array which will be transformed to JSON
     * @return array
     * <pre>
     * [
     *   'http_code' => int,
     *   'response' => string,
     * ]
     * </pre>
     * @throws GuzzleException
     * @see generateKPIArray
     */
    public function sendKPIToWebhook(array $requestBody, ?string $url = null): array
    {
        $this->client = $this->client ?? new Client();
        $response = $this->client->request(
            'POST',
            $url ?? $this->configKPIWebhookURL,
            [
                RequestOptions::JSON => $requestBody,
            ]
        );

        return [
            'status_code' => $response->getStatusCode(),
            'response' => $response->getBody(),
        ];
    }
}
