<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Messenger;

use Psr\Log\LoggerInterface;
use <PERSON>humsa<PERSON>\Uuid\Uuid;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\Exception\TransportException;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Messenger\Transport\AmqpExt\AmqpStamp;

class BroadcastPublisher
{
    private MessageBusInterface $bus;
    private LoggerInterface $logger;

    private ?string $yavinTransportDsn;
    private bool $yavinEnabled;

    public function __construct(
        MessageBusInterface $bus,
        ?string $yavinTransportDsn,
        LoggerInterface $logger,
        bool $yavinEnabled
    ) {
        $this->bus = $bus;
        $this->yavinTransportDsn = $yavinTransportDsn;
        $this->logger = $logger;
        $this->yavinEnabled = $yavinEnabled;
    }

    /**
     * @param string $eventName
     * @param mixed[] $payload
     * @param mixed[] $context
     *
     * @return Envelope|null
     */
    public function publish(string $eventName, array $payload, array $context): ?Envelope
    {
        if (false === $this->yavinEnabled) {
            return null;
        }

        // if DSN is empty or null do nothing and return null
        if ('' === $this->yavinTransportDsn || null === $this->yavinTransportDsn) {
            $this->logger->error(
                'Yavin is enabled but no Transport DSN has been set!',
                ['event' => $eventName, 'context' => $context, 'payload' => $payload]
            );

            return null;
        }

        // we publish the message only if the DSN is not empty
        // to avoid error 400 when DSN is empty and we return an envelope
        try {
            return $this->bus->dispatch(
                new Envelope(
                    new EventMessage(
                        $this->generateUuid(),
                        $this->getNowDate(),
                        $eventName,
                        $context,
                        $payload
                    )
                ),
                [new AmqpStamp('event.' . $eventName)]
            );
        } catch (TransportException $exception) {
            $this->logger->error(
                'Unable to publish message : ' . $exception->getMessage(),
                ['event' => $eventName, 'context' => $context, 'payload' => $payload]
            );

            return null;
        }
    }

    private function generateUuid(): string
    {
        return Uuid::uuid4()->toString();
    }

    private function getNowDate(): string
    {
        return (new \DateTime('now'))->format(DATE_RFC3339);
    }
}
