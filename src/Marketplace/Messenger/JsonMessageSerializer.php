<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Messenger;

use <PERSON>ymfony\Component\Messenger\Envelope;
use <PERSON>ymfony\Component\Messenger\Transport\Serialization\SerializerInterface;
use <PERSON><PERSON>fony\Component\Serializer\SerializerInterface as Serializer;

class JsonMessageSerializer implements SerializerInterface
{
    private Serializer $serializer;

    public function __construct(Serializer $serializer)
    {
        $this->serializer = $serializer;
    }

    /** @param mixed[] $encodedEnvelope */
    public function decode(array $encodedEnvelope): Envelope
    {
        return new Envelope(
            $this->serializer->deserialize($encodedEnvelope['body'], EventMessage::class, 'json'),
            []
        );
    }

    /** @return mixed[] */
    public function encode(Envelope $envelope): array
    {
        $message = $envelope->getMessage();

        if ($message instanceof EventMessage === false) {
            throw new \LogicException('Unsupported message class.');
        }

        $allStamps = [];
        foreach ($envelope->all() as $stamps) {
            $allStamps = \array_merge($allStamps, $stamps);
        }

        return [
            'body' => $this->serializer->serialize($message, 'json'),
            'headers' => [
                'stamps' => \serialize($allStamps)
            ],
        ];
    }
}
