<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Messenger;

class EventMessage
{
    private string $uuid;

    private string $createdAt;

    private string $name;

    /** @var mixed[] */
    private array $context;

    /** @var mixed[] */
    private array $payload;

    public function __construct(
        string $uuid,
        string $createdAt,
        string $name,
        array $context,
        array $payload
    ) {
        $this->uuid = $uuid;
        $this->createdAt = $createdAt;
        $this->name = $name;
        $this->context = $context;
        $this->payload = $payload;
    }

    public function getUuid(): string
    {
        return $this->uuid;
    }

    public function getCreatedAt(): string
    {
        return $this->createdAt;
    }

    public function getName(): string
    {
        return $this->name;
    }

    /** @return mixed[] */
    public function getContext(): array
    {
        return $this->context;
    }

    /** @return mixed[] */
    public function getPayload(): array
    {
        return $this->payload;
    }
}
