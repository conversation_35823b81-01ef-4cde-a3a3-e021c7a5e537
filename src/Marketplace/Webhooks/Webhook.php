<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Webhooks;

class Webhook
{
    /** @var string */
    private $id;

    /** @var string */
    private $url;

    /** @var int */
    private $retryMaxAttempts;

    /** @var int|null */
    private $companyId;

    /** @var mixed[] */
    private $eventReferences;

    public function __construct(
        string $id,
        string $url,
        int $retryMaxAttempts,
        array $eventReferences,
        int $companyId = null
    ) {
        $this->id = $id;
        $this->url = $url;
        $this->retryMaxAttempts = $retryMaxAttempts;
        $this->eventReferences = $eventReferences;
        $this->companyId = $companyId ?? null;
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function getUrl(): string
    {
        return $this->url;
    }

    public function getRetryMaxAttempts(): int
    {
        return $this->retryMaxAttempts;
    }

    public function getCompanyId(): ?int
    {
        return $this->companyId;
    }

    /** @return mixed[] */
    public function getEventReferences(): array
    {
        return $this->eventReferences;
    }
}
