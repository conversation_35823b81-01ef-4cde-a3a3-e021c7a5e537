<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Webhooks;

class WebhooksEventReference
{
    /** @var string */
    private $name;

    /** @var string */
    private $group;

    public function __construct(string $name)
    {
        $this->name = $name;
        $this->group = \explode('.', $this->name)[0];
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getGroup(): string
    {
        return $this->group;
    }
}
