<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Webhooks\Service;

use Psr\Log\LoggerInterface;
use Wizacha\Component\Http\Authorization\UserApiKeyAuthorization;
use Wizacha\Component\Http\Authorization\YavinJwtAuthorization;
use Wizacha\Component\Http\Client\YavinClientInterface;
use Wizacha\Component\Http\Option\Common\HeaderOption;
use Wizacha\Component\Http\Request\RequestInterface;
use Wizacha\Component\Http\Request\YavinRequest;
use Wizacha\Component\Http\Response\ResponseInterface;
use Wizacha\Component\Http\YavinClientException;
use Wizacha\Marketplace\User\UserService;
use Wizacha\Marketplace\Webhooks\Request\EventReferencesRetrieve;
use Wizacha\Marketplace\Webhooks\Request\ScopesRetrieve;
use Wizacha\Marketplace\Webhooks\Request\WebhookCreate;
use Wizacha\Marketplace\Webhooks\Request\WebhookDelete;
use Wizacha\Marketplace\Webhooks\Request\WebhookRetrieve;
use Wizacha\Marketplace\Webhooks\Request\WebhooksRetrieve;
use Wizacha\Marketplace\Webhooks\Request\WebhookUpdate;
use Wizacha\Marketplace\Webhooks\Webhook;
use Wizacha\Marketplace\Webhooks\WebhookData;
use Wizacha\Marketplace\Webhooks\WebhooksEventReference;

class WebhooksService
{
    private UserService $userService;
    private YavinClientInterface $httpClient;
    private string $uri;
    private LoggerInterface $logger;
    private string $yavinApiJwt;

    public function __construct(
        UserService $userService,
        YavinClientInterface $httpClient,
        string $uri,
        LoggerInterface $logger,
        string $yavinApiJwt = ''
    ) {
        $this->userService = $userService;
        $this->httpClient = $httpClient;
        $this->uri = rtrim($uri, '/');
        $this->logger = $logger;
        $this->yavinApiJwt = $yavinApiJwt;
    }

    /** @return Webhook[] */
    public function getWebhooks(): array
    {
        $webhooks = [];
        $yavinRequest = new WebhooksRetrieve();

        foreach (\json_decode($this->requestApi($yavinRequest)->getBody()->getContents(), true)['results'] as $webhook) {
            $webhooks[] = new Webhook(
                $webhook['id'],
                $webhook['url'],
                $webhook['retryMaxAttempts'],
                $webhook['eventReferences'],
                $webhook['companyId']
            );
        }

        return $webhooks;
    }

    /** @return WebhooksEventReference[] */
    public function getWebhooksEventReferences(): array
    {
        $eventReferences = [];
        $userScopes = $this->getUserScopes();
        $yavinRequest = new EventReferencesRetrieve();

        foreach (\json_decode($this->requestApi($yavinRequest)->getBody()->getContents(), true)['results'] as $eventReference) {
            if (\count(\array_intersect($eventReference['scopes'], $userScopes)) > 0) {
                $webhookEventReference = new WebhooksEventReference($eventReference['name']);
                $eventReferences[$webhookEventReference->getGroup()][] = $webhookEventReference;
            }
        }

        return $eventReferences;
    }

    public function getWebhook(string $webhookId): Webhook
    {
        $yavinRequest = new WebhookRetrieve($webhookId);

        $webhook = \json_decode($this->requestApi($yavinRequest)->getBody()->getContents(), true);

        return new Webhook(
            $webhook['id'],
            $webhook['url'],
            $webhook['retryMaxAttempts'],
            $webhook['eventReferences'],
            $webhook['companyId']
        );
    }

    public function deleteWebhook(string $webhookId): void
    {
        $this->requestApi(new WebhookDelete($webhookId));
    }

    /** @param WebhookData $webhookData */
    public function patchWebhook(string $webhookId, WebhookData $webhookData): Webhook
    {
        $yavinRequest = new WebhookUpdate($webhookId, $webhookData);

        $webhook = \json_decode($this->requestApi($yavinRequest)->getBody()->getContents(), true);

        return new Webhook(
            $webhook['id'],
            $webhook['url'],
            $webhook['retryMaxAttempts'],
            $webhook['eventReferences'],
            $webhook['companyId']
        );
    }

    /** @param WebhookData $webhookData */
    public function postWebhook(WebhookData $webhookData): Webhook
    {
        $yavinRequest = new WebhookCreate($webhookData);

        $webhook = \json_decode($this->requestApi($yavinRequest)->getBody()->getContents(), true);

        return new Webhook(
            $webhook['id'],
            $webhook['url'],
            $webhook['retryMaxAttempts'],
            $webhook['eventReferences'],
            $webhook['companyId']
        );
    }

    protected function requestApi(RequestInterface $webhooksRequest): ResponseInterface
    {
        $yavinRequest = new YavinRequest(
            $webhooksRequest,
            $this->uri,
            $this->getHeaders()
        );

        try {
            return $this->httpClient->send($yavinRequest);
        } catch (YavinClientException $exception) {
            $this->logWebhooksException($exception);

            throw $exception;
        }
    }

    /** @return string[] */
    protected function getUserScopes(): array
    {
        if ($this->yavinApiJwt !== '') {
            return ['orders:view'];
        }

        return \array_map(
            function ($scope) {
                return $scope['name'];
            },
            \json_decode($this->requestApi(new ScopesRetrieve())->getBody()->getContents(), true)
        );
    }

    protected function getHeaders(): HeaderOption
    {
        $userId = \Tygh\Registry::get('user_info')['user_id'];
        $user = $this->userService->get($userId);

        if ($this->yavinApiJwt !== '') {
            return new HeaderOption(new YavinJwtAuthorization($this->yavinApiJwt));
        } else {
            return new HeaderOption(new UserApiKeyAuthorization($user));
        }
    }

    protected function logWebhooksException(YavinClientException $exception): void
    {
        $this->logger->error("Webhooks service error", [
            'exception' => $exception,
            'trace' => $exception->getTraceAsString(),
            'message' => $exception->getMessage(),
            'code' => $exception->getCode()
        ]);
    }
}
