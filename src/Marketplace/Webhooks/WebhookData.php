<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Webhooks;

class WebhookData implements \JsonSerializable
{
    /** @var string|null */
    protected $url;
    /** @var int|null */
    protected $companyId;
    /** @var int|null */
    protected $retryMaxAttempt;
    /** @var mixed[]|null */
    protected $eventReferences;

    public function __construct(
        string $url = null,
        ?int $companyId = null,
        int $retryMaxAttempt = null,
        array $eventReferences = null
    ) {
        $this->url = $url ?? null;
        $this->companyId = $companyId ?? null;
        $this->retryMaxAttempt = $retryMaxAttempt ?? null;
        $this->eventReferences = $eventReferences ?? null;
    }

    public function getUrl(): ?string
    {
        return $this->url;
    }

    public function getCompanyId(): ?int
    {
        return $this->companyId;
    }

    public function getRetryMaxAttempt(): ?int
    {
        return $this->retryMaxAttempt;
    }

    public function getEventReferences(): ?array
    {
        return $this->eventReferences;
    }

    public function jsonSerialize(): array
    {
        $webhookDataAsTrimmedArray = [];

        if (\is_string($this->getUrl()) === true) {
            $webhookDataAsTrimmedArray['url'] = $this->getUrl();
        }

        if (\is_int($this->getCompanyId()) === true || $this->getCompanyId() === null) {
            $webhookDataAsTrimmedArray['companyId'] = $this->getCompanyId();
        }

        if (\is_int($this->getRetryMaxAttempt()) === true) {
            $webhookDataAsTrimmedArray['retryMaxAttempt'] = $this->getRetryMaxAttempt();
        }

        if (\is_array($this->getEventReferences()) === true) {
            $webhookDataAsTrimmedArray['eventReferences'] = $this->getEventReferences();
        }

        return $webhookDataAsTrimmedArray;
    }
}
