<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Webhooks\Request;

use Wizacha\Component\Http\Option\StringOption;
use Wizacha\Component\Http\Request\RequestInterface;
use Wizacha\Marketplace\Webhooks\WebhookData;

class WebhookUpdate implements RequestInterface
{
    private string $webhookId;
    private WebhookData $data;

    public function __construct(string $webhookId, WebhookData $data)
    {
        $this->webhookId = $webhookId;
        $this->data = $data;
    }

    public function getMethod(): string
    {
        return 'PATCH';
    }

    public function getUri(): string
    {
        return '/webhooks/' . $this->webhookId;
    }

    public function getOptions(): array
    {
        return [new StringOption('body', \json_encode($this->data))];
    }
}
