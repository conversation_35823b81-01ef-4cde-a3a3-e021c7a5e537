<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Webhooks\Request;

use Wizacha\Component\Http\Request\RequestInterface;

class WebhookRetrieve implements RequestInterface
{
    private string $webhookId;

    public function __construct(string $webhookId)
    {
        $this->webhookId = $webhookId;
    }

    public function getMethod(): string
    {
        return 'GET';
    }

    public function getUri(): string
    {
        return '/webhooks/' . $this->webhookId;
    }

    public function getOptions(): array
    {
        return [];
    }
}
