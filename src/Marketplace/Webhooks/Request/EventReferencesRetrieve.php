<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Webhooks\Request;

use Wizacha\Component\Http\Request\RequestInterface;

class EventReferencesRetrieve implements RequestInterface
{
    public function getMethod(): string
    {
        return 'GET';
    }

    public function getUri(): string
    {
        return '/webhooks/event-references';
    }

    public function getOptions(): array
    {
        return [];
    }
}
