<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Webhooks\Request;

use Wizacha\Component\Http\Request\RequestInterface;

// It will not be use only for webhooks in the future, perhaps we will need to move this file in another directory
class ScopesRetrieve implements RequestInterface
{
    public function getMethod(): string
    {
        return 'GET';
    }

    public function getUri(): string
    {
        return '/me/scopes';
    }

    public function getOptions(): array
    {
        return [];
    }
}
