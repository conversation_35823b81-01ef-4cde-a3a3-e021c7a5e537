<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Webhooks\Request;

use Wizacha\Component\Http\Option\StringOption;
use Wizacha\Component\Http\Request\RequestInterface;
use Wizacha\Marketplace\Webhooks\WebhookData;

class WebhookCreate implements RequestInterface
{
    private WebhookData $data;

    public function __construct(WebhookData $data)
    {
        $this->data = $data;
    }

    public function getMethod(): string
    {
        return 'POST';
    }

    public function getUri(): string
    {
        return '/webhooks';
    }

    public function getOptions(): array
    {
        return [new StringOption('body', \json_encode($this->data))];
    }
}
