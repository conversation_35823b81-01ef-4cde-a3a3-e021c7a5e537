<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order;

use Wizacha\Marketplace\Order\Action\Accept;
use Wizacha\Marketplace\Order\Action\Cancel;
use Wizacha\Marketplace\Order\Action\CommitTo;
use Wizacha\Marketplace\Order\Action\DeclareInvoiceNumberGeneratedElsewhere;
use Wizacha\Marketplace\Order\Action\EndWithdrawalPeriod;
use Wizacha\Marketplace\Order\Action\MarkAsDelivered;
use Wizacha\Marketplace\Order\Action\MarkAsPaid;
use Wizacha\Marketplace\Order\Action\MarkAsShipped;
use Wizacha\Marketplace\Order\Action\MarkPaymentAsRefused;
use Wizacha\Marketplace\Order\Action\MarkPaymentDefermentAsAuthorized;
use Wizacha\Marketplace\Order\Action\MarkPaymentDefermentAsRefused;
use Wizacha\Marketplace\Order\Action\Refuse;

class OrderMutator
{
    /** @var Cancel */
    protected $cancel;

    /** @var Refuse */
    protected $refuse;

    /** @var MarkPaymentAsRefused */
    protected $markPaymentAsRefused;

    /** @var MarkPaymentDefermentAsAuthorized */
    protected $markPaymentDefermentAsAuthorized;

    /** @var MarkPaymentDefermentAsRefused */
    protected $markPaymentDefermentAsRefused;

    /** @var MarkAsPaid */
    protected $markAsPaid;

    /** @var CommitTo */
    protected $commitTo;

    /** @var Accept */
    protected $accept;

    /** @var DeclareInvoiceNumberGeneratedElsewhere */
    protected $declareInvoiceNumberGeneratedElsewhere;

    /** @var MarkAsShipped */
    protected $markAsShipped;

    /** @var MarkAsDelivered */
    protected $markAsDelivered;

    /** @var EndWithdrawalPeriod */
    protected $endWithdrawalPeriod;


    public function __construct(
        Cancel $cancel,
        MarkPaymentDefermentAsAuthorized $markPaymentDefermentAsAuthorized,
        MarkPaymentDefermentAsRefused $markPaymentDefermentAsRefused,
        MarkAsPaid $markAsPaid,
        Refuse $refuse,
        MarkPaymentAsRefused $markPaymentAsRefused,
        CommitTo $commitTo,
        Accept $accept,
        DeclareInvoiceNumberGeneratedElsewhere $declareInvoiceNumberGeneratedElsewhere,
        MarkAsShipped $markAsShipped,
        MarkAsDelivered $markAsDelivered,
        EndWithdrawalPeriod $endWithdrawalPeriod
    ) {
        $this->cancel = $cancel;
        $this->markPaymentDefermentAsAuthorized = $markPaymentDefermentAsAuthorized;
        $this->markPaymentDefermentAsRefused = $markPaymentDefermentAsRefused;
        $this->markAsPaid = $markAsPaid;
        $this->refuse = $refuse;
        $this->markPaymentAsRefused = $markPaymentAsRefused;
        $this->commitTo = $commitTo;
        $this->accept = $accept;
        $this->declareInvoiceNumberGeneratedElsewhere = $declareInvoiceNumberGeneratedElsewhere;
        $this->markAsShipped = $markAsShipped;
        $this->markAsDelivered = $markAsDelivered;
        $this->endWithdrawalPeriod = $endWithdrawalPeriod;
    }

    public function setOrderStatus(
        Order $order,
        OrderStatus $orderStatus
    ): Order {
        fn_change_order_status(
            $this->process(
                $order,
                $orderStatus
            )->getId(),
            $orderStatus->getValue()
        );

        return $order;
    }

    protected function process(
        Order $order,
        OrderStatus $orderStatus
    ): Order {
        if ($orderStatus->equals(OrderStatus::CANCELED())) {
            $this->cancel->execute($order);

            return $order;
        }

        // Avancement jusqu'à BILLING_FAILED
        if ($orderStatus->equals(OrderStatus::BILLING_FAILED())) {
            if ($this->markPaymentDefermentAsRefused->isAllowed($order)) {
                $this->markPaymentDefermentAsRefused->execute($order);
            }
            if ($this->markPaymentAsRefused->isAllowed($order)) {
                $this->markPaymentAsRefused->execute($order);
            }

            return $order;
        }

        // Avancement jusqu'à STAND_BY_VENDOR
        if ($this->markPaymentDefermentAsAuthorized->isAllowed($order)) {
            $this->markPaymentDefermentAsAuthorized->execute($order);
        }
        if ($this->markAsPaid->isAllowed($order)) {
            $this->markAsPaid->execute($order);
        }
        if ($orderStatus->equals(OrderStatus::STANDBY_VENDOR())) {
            return $order;
        }

        // Avancement jusqu'à VENDOR_DECLINED
        if ($orderStatus->equals(OrderStatus::VENDOR_DECLINED())) {
            $this->refuse->execute($order);

            return $order;
        }

        // Avancement jusqu'à PROCESSING_SHIPPING
        if ($this->commitTo->isAllowed($order)) {
            $this->commitTo->execute(
                $order,
                new \DateTimeImmutable(),
                'commitment'
            );
        }
        if ($this->markPaymentDefermentAsAuthorized->isAllowed($order)) {
            $this->markPaymentDefermentAsAuthorized->execute($order);
        }
        if ($this->accept->isAllowed($order)) {
            $this->accept->execute($order);
        }
        if ($orderStatus->equals(OrderStatus::PROCESSING_SHIPPING())) {
            return $order;
        }

        // Avancement jusqu'à PROCESSED
        if ($this->declareInvoiceNumberGeneratedElsewhere->isAllowed($order)) {
            $this->declareInvoiceNumberGeneratedElsewhere->execute($order);
        }
        if ($this->markAsShipped->isAllowed($order)) {
            $this->markAsShipped->execute($order);
        }
        if ($orderStatus->equals(OrderStatus::PROCESSED())) {
            return $order;
        }

        // Avancement jusqu'à COMPLETED
        if ($this->markAsDelivered->isAllowed($order)) {
            $this->markAsDelivered->execute($order);
        }
        if ($this->endWithdrawalPeriod->isAllowed($order)) {
            $this->endWithdrawalPeriod->execute($order);
        }
        if ($orderStatus->equals(OrderStatus::COMPLETED())) {
            return $order;
        }

        return $order;
    }
}
