<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order;

class OrderDelivery
{
    /** @var \DateTimeImmutable|null */
    private $deliveryDate;

    /** @var bool|null */
    private $isDelivered;

    public function __construct(?\DateTimeImmutable $deliveryDate, ?bool $isDelivered)
    {
        $this->deliveryDate = $deliveryDate;
        $this->isDelivered = $isDelivered;
    }

    public function isDeliveryStatusKnown(): bool
    {
        return \is_bool($this->isDelivered);
    }

    public function getDeliveryDate(): \DateTimeImmutable
    {
        return $this->deliveryDate;
    }

    public function isDelivered(): bool
    {
        return $this->isDelivered;
    }
}
