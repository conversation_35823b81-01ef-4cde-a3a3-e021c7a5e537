<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order;

class OrderShipping
{
    private $shippingDate;

    public function __construct(\DateTimeImmutable $shippingDate)
    {
        $this->shippingDate = $shippingDate;
    }

    public function getShippingDate(): \DateTimeImmutable
    {
        return $this->shippingDate;
    }
}
