<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Event;

use Symfony\Component\Form\Form;
use Wizacha\C2C\Code;
use Wizacha\Marketplace\Order\Order;

class OrderCodeGenerated extends OrderEvent
{
    /**
     * @var Code
     */
    private $code;

    public function __construct(Order $order, Code $code)
    {
        parent::__construct($order);
        $this->code = $code;
    }

    public static function createFromForm(Form $form)
    {
        $orderId = $form->getData()['order'];
        $order = container()->get('marketplace.order.order_service')->getOrder($orderId);

        return new OrderCodeGenerated($order, new Code());
    }

    public static function getDescription(): string
    {
        return 'w_c2c_code';
    }

    public function getCode(): Code
    {
        return $this->code;
    }
}
