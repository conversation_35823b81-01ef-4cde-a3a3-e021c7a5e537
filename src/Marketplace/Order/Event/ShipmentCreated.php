<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Order\Event;

use Symfony\Contracts\EventDispatcher\Event;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Wizacha\Component\Notification\NotificationEvent;
use Wizacha\Marketplace\Order\Shipment;
use Wizacha\Registry;

class ShipmentCreated extends Event implements NotificationEvent
{
    /** @var  Shipment */
    private $shipment;

    public function __construct(Shipment $shipment)
    {
        $this->shipment = $shipment;
    }

    public function getShipment(): Shipment
    {
        return $this->shipment;
    }

    public static function buildDebugForm(FormBuilder $form)
    {
        $form->add('orderId', TextType::class);
    }

    public static function createFromForm(Form $form)
    {
        $orderService = Registry::defaultInstance()->container->get('marketplace.order.order_service');
        list($shipments) = fn_get_shipments_info(['order_id' => $form->getData()['orderId']]);
        $shipmentId = reset($shipments)['shipment_id'];

        return new static($orderService->getShipment($shipmentId));
    }

    public static function getDescription(): string
    {
        return 'shipment_created';
    }
}
