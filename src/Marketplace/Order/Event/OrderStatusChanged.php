<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Order\Event;

use Symfony\Contracts\EventDispatcher\Event;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Wizacha\Component\Notification\NotificationEvent;

class OrderStatusChanged extends Event implements NotificationEvent
{
    /**
     * @var array
     */
    private $orderInfo;

    /**
     * @var bool
     */
    private $notifyCustomer;

    /**
     * @var bool
     */
    private $notifyCompany;

    /**
     * @var bool
     */
    private $notifyAdmin;

    public function __construct(array $orderInfo, bool $notifyCustomer, bool $notifyCompany, bool $notifyAdmin)
    {
        $this->orderInfo = $orderInfo;
        $this->notifyCustomer = $notifyCustomer;
        $this->notifyCompany = $notifyCompany;
        $this->notifyAdmin = $notifyAdmin;
    }

    /**
     * Build the Symfony Form that will be shown in the backend to simulate the event.
     */
    public static function buildDebugForm(FormBuilder $form)
    {
        $form->add('order', IntegerType::class);
    }

    /**
     * Create a instance of that class from the submitted form.
     *
     * @return static
     */
    public static function createFromForm(Form $form)
    {
        $order_info = container()
            ->get('marketplace.order.order_service')
            ->overrideLegacyOrder($form->getData()['order']);
        return new static($order_info, true, true, true);
    }

    /**
     * Returns the translation that describes the event.
     *
     * It will be used in the back-office to list all notifications.
     *
     * You must return a translation ID!
     */
    public static function getDescription(): string
    {
        return 'order_status';
    }

    public function getOrderInfo(): array
    {
        return $this->orderInfo;
    }

    public function getNotifyCustomer(): bool
    {
        return $this->notifyCustomer;
    }

    public function getNotifyCompany(): bool
    {
        return $this->notifyCompany;
    }

    public function getNotifyAdmin(): bool
    {
        return $this->notifyAdmin;
    }
}
