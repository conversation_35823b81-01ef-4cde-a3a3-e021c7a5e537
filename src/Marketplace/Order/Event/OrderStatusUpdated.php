<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Order\Event;

use Symfony\Contracts\EventDispatcher\Event;

class OrderStatusUpdated extends Event
{
    /**
     * @var integer
     */
    private $id;

    /**
     * @var string
     */
    private $statusFrom;

    /**
     * @var string
     */
    private $statusTo;

    /**
     * OrderUpdate constructor.
     * @param string $statusFrom
     * @param string $statusTo
     */
    public function __construct(int $id, $statusFrom, $statusTo)
    {
        $this->id = $id;
        $this->statusFrom = $statusFrom;
        $this->statusTo = $statusTo;
    }

    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getStatusFrom()
    {
        return $this->statusFrom;
    }

    /**
     * @return string
     */
    public function getStatusTo()
    {
        return $this->statusTo;
    }
}
