<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Event;

use Symfony\Contracts\EventDispatcher\Event;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Wizacha\Component\Notification\NotificationEvent;
use Wizacha\Marketplace\Order\Order;

abstract class OrderEvent extends Event implements NotificationEvent
{
    private $order;

    public function __construct(Order $order)
    {
        $this->order = $order;
    }

    public function getOrder(): Order
    {
        return $this->order;
    }

    public static function buildDebugForm(FormBuilder $form)
    {
        $form->add('order', IntegerType::class);
    }

    public static function createFromForm(Form $form)
    {
        $orderId = $form->getData()['order'];
        $order = container()->get('marketplace.order.order_service')->getOrder($orderId);

        return new static($order);
    }
}
