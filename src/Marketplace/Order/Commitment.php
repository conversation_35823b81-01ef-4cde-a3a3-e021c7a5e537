<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order;

class Commitment
{
    /** @var \DateTimeImmutable */
    private $date;

    /** @var string */
    private $number;

    public function __construct(\DateTimeImmutable $date, string $number)
    {
        $this->date = $date;
        $this->number = $number;
    }

    public function getDate(): \DateTimeImmutable
    {
        return $this->date;
    }

    public function getNumber(): string
    {
        return $this->number;
    }
}
