<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Refund\Entity;

use Wizacha\Component\Order\Amounts;
use Wizacha\Component\Order\Line;
use Wizacha\Marketplace\Order\OrderItem;
use Wizacha\Marketplace\Traits\HasTimestamp;
use Wizacha\Money\Money;

class RefundItem
{
    use HasTimestamp;

    /** @var int */
    protected $id;

    /** @var Refund */
    protected $refund;

    /** @var int */
    protected $itemId;

    /** @var Money - Item Unit Price*/
    protected $amount;

    /** @var int */
    protected $quantity;

    /** @var Amounts */
    protected $amounts;

    /** @var Line */
    protected $line;

    /** @var Line */
    protected $discountedLine;

    public function __construct()
    {
        $this->updateTimestamp();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getRefund(): Refund
    {
        return $this->refund;
    }

    public function setRefund(Refund $refund): self
    {
        $this->refund = $refund;

        return $this;
    }

    public function getItemId(): int
    {
        return $this->itemId;
    }

    public function setItemId(int $itemId): self
    {
        $this->itemId = $itemId;

        return $this;
    }

    public function getAmount(): Money
    {
        return $this->amount;
    }

    public function setAmount(Money $amount): self
    {
        $this->amount = $amount;

        return $this;
    }

    public function getQuantity(): int
    {
        return $this->quantity;
    }

    public function setQuantity(int $quantity): self
    {
        $this->quantity = $quantity;

        return $this;
    }

    public function getLine(): ?Line
    {
        return $this->line;
    }

    public function getDiscountedLine(): ?Line
    {
        return $this->discountedLine;
    }

    public function getAmounts(): ?Amounts
    {
        return $this->amounts;
    }

    public function initializeAmounts(OrderItem $orderItem, int $quantity): self
    {
        $this->amounts = new Amounts();
        $this->line = new Line(
            $orderItem->getAmountItem()->getUnitPriceExclTaxes(),
            $quantity,
            $orderItem->getTaxRate()
        );
        $this->discountedLine = new Line(
            $orderItem->getAmountItem()->getDiscountedUnitPriceExclTaxes(),
            $quantity,
            $orderItem->getTaxRate()
        );
        $this->amounts = $this->amounts->withLine($this->line);

        return $this;
    }
}
