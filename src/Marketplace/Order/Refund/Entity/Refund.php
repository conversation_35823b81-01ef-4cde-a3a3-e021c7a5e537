<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Refund\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Wizacha\Marketplace\Order\AmountTaxesDetail\AmountTaxesDetail;
use Wizacha\Marketplace\Order\Refund\Enum\RefundStatus;
use Wizacha\Marketplace\Order\Refund\Utils\RefundAmounts;
use Wizacha\Marketplace\Traits\HasTimestamp;
use Wizacha\Money\Money;

class Refund implements \JsonSerializable
{
    use HasTimestamp;

    /** @var int */
    protected $id;

    /** @var int */
    protected $orderId;

    /** @var int|null */
    protected $userId;

    /** @var bool */
    protected $isPartial;

    /** @var bool */
    protected $hasShipping;

    /** @var Money */
    protected $amount;

    /** @var Money */
    protected $shippingAmount;

    /** @var Money */
    protected $basketDiscount;

    /** @var Money */
    protected $marketplaceDiscount;

    /** @var RefundStatus */
    protected $status;

    /** @var bool */
    protected $isLegacy;

    /** @var string */
    protected $creditNoteReference;

    /** @var string */
    protected $message;

    /** @var RefundItem[] */
    protected $items;

    /** @var RefundAmounts */
    protected $refundAmounts;

    /** @var bool */
    protected $refundedAfterWithdrawalPeriod;

    public function __construct()
    {
        $this->status = RefundStatus::CREATED();
        $this->items = new ArrayCollection();
        $this->updateTimestamp();
        $this->refundedAfterWithdrawalPeriod = false;
        $this->marketplaceDiscount = new Money(0);
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getOrderId(): int
    {
        return $this->orderId;
    }

    public function setOrderId(int $orderId): self
    {
        $this->orderId = $orderId;

        return $this;
    }

    public function getUserId(): ?int
    {
        return $this->userId;
    }

    public function setUserId(?int $userId = null): self
    {
        $this->userId = $userId;

        return $this;
    }

    public function isPartial(): bool
    {
        return $this->isPartial;
    }

    public function setIsPartial(bool $isPartial): self
    {
        $this->isPartial = $isPartial;

        return $this;
    }

    public function hasShipping(): bool
    {
        return $this->hasShipping;
    }

    public function setHasShipping(bool $hasShipping): self
    {
        $this->hasShipping = $hasShipping;

        return $this;
    }

    public function getAmount(): Money
    {
        return $this->amount;
    }

    public function setAmount(Money $amount): self
    {
        $this->amount = $amount;

        return $this;
    }

    public function getShippingAmount(): Money
    {
        return $this->shippingAmount;
    }

    public function setShippingAmount(Money $shippingAmount): self
    {
        $this->shippingAmount = $shippingAmount;

        return $this;
    }

    public function getBasketDiscount(): Money
    {
        return $this->basketDiscount;
    }

    public function setBasketDiscount(Money $basketDiscount): self
    {
        $this->basketDiscount = $basketDiscount;

        return $this;
    }

    public function getMarketplaceDiscount(): Money
    {
        return $this->marketplaceDiscount;
    }

    public function setMarketplaceDiscount(Money $marketplaceDiscount): self
    {
        $this->marketplaceDiscount = $marketplaceDiscount;

        return $this;
    }

    public function getStatus(): RefundStatus
    {
        return $this->status;
    }

    public function setStatus(RefundStatus $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function isLegacy(): bool
    {
        return $this->isLegacy;
    }

    public function setIsLegacy(bool $isLegacy): self
    {
        $this->isLegacy = $isLegacy;

        return $this;
    }

    public function getCreditNoteReference(): ?string
    {
        return $this->creditNoteReference;
    }

    public function setCreditNoteReference(?string $creditNoteReference): self
    {
        $this->creditNoteReference = $creditNoteReference;

        return $this;
    }

    public function getMessage(): ?string
    {
        return $this->message;
    }

    public function setMessage(?string $message): self
    {
        $this->message = $message;

        return $this;
    }

    /** @return RefundItem[] */
    public function getItems(): iterable
    {
        return $this->items;
    }

    public function addItem(RefundItem $item): self
    {
        $this->items[] = $item;

        return $this;
    }

    /** @param RefundItem[] $items */
    public function setItems(iterable $items): self
    {
        $this->items = $items;

        return $this;
    }

    public function setRefundAmounts(RefundAmounts $refundAmounts): self
    {
        $this->refundAmounts = $refundAmounts;

        return $this;
    }

    public function getRefundAmounts(): RefundAmounts
    {
        return $this->refundAmounts;
    }

    public function setRefundedAfterWithdrawalPeriod(bool $refundedAfterWithdrawalPeriod): self
    {
        $this->refundedAfterWithdrawalPeriod = $refundedAfterWithdrawalPeriod;

        return $this;
    }

    public function isRefundedAfterWithdrawalPeriod(): bool
    {
        return $this->refundedAfterWithdrawalPeriod;
    }

    /** @inheritDoc */
    public function jsonSerialize(): array
    {
        $refundItems = [];

        foreach ($this->getItems() as $item) {
            $refundItems[] = [
                'itemId' => $item->getItemId(),
                'unitPrice' => (new AmountTaxesDetail(
                    'unitPrice',
                    $item->getLine()->getUnitPrice(),
                    $item->getLine()->getTaxAmount()->divide($item->getQuantity()),
                    $item->getLine()->getAmountIncludingTax()->divide($item->getQuantity())
                ))->expose(),
                'quantity' => $item->getQuantity(),
                'totalPrice' => (new AmountTaxesDetail(
                    'totalPrice',
                    $item->getAmounts()->getAmountExcludingTaxes(),
                    $item->getAmounts()->getTaxAmount(),
                    $item->getAmounts()->getAmountIncludingTaxes()
                ))->expose(),
            ];
        }

        return [
            'refundId' => $this->id,
            'orderId' => $this->orderId,
            'userId' => $this->userId,
            'isPartial' => $this->isPartial,
            'hasShipping' => $this->hasShipping,
            'amount' => $this->amount instanceof Money ? $this->amount->getConvertedAmount() : null,
            'shippingAmount' => $this->shippingAmount instanceof Money
                ? $this->shippingAmount->getConvertedAmount()
                : null,
            'message' => $this->getMessage(),
            'status' => $this->status !== null ? strtolower($this->status->getKey()) : null,
            'createdAt' => $this->createdAt instanceof \DateTimeInterface
                ? $this->createdAt->format(\DateTime::RFC3339)
                : null,
            'updatedAt' => $this->updatedAt instanceof \DateTimeInterface
                ? $this->updatedAt->format(\DateTime::RFC3339)
                : null,
            'items' => $refundItems,
            'totalItemsPrice' => $this->getRefundAmounts()->exposeTotalItemsAmounts(),
            'totalShippingPrice' => $this->getRefundAmounts()->exposeShippingsAmounts(),
            'totalGlobalPrice' => $this->getRefundAmounts()->exposeGlobalAmounts(),
            'totalTaxes' => $this->getRefundAmounts()->exposeTaxesAmounts(),
            'discountsIncluded' => [
                'total' => $this->getBasketDiscount()->add($this->getMarketplaceDiscount())->getConvertedAmount(),
                'basketDiscount' => $this->getBasketDiscount()->getConvertedAmount(),
                'marketplaceDiscount' => $this->getMarketplaceDiscount()->getConvertedAmount(),
            ],
            'refundedAfterWithdrawalPeriod' => $this->isRefundedAfterWithdrawalPeriod(),
        ];
    }
}
