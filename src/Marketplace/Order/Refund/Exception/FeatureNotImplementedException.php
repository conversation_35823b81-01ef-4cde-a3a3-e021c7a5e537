<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Refund\Exception;

use Wizacha\Marketplace\Exception\ErrorCode;
use Wizacha\Marketplace\Exception\MarketplaceExceptionInterface;

class FeatureNotImplementedException extends \Exception implements MarketplaceExceptionInterface
{
    private ErrorCode $errorCode;

    public function __construct(string $message, \Throwable $previous = null)
    {
        $this->errorCode = ErrorCode::FEATURE_NOT_IMPLEMENTED();

        parent::__construct($message, $this->errorCode->getValue(), $previous);
    }

    public function getContext(): array
    {
        return [];
    }

    public function getErrorCode(): ErrorCode
    {
        return $this->errorCode;
    }
}
