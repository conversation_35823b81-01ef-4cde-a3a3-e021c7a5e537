<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Refund\Exception;

use Wizacha\Marketplace\Exception\ErrorCode;
use Wizacha\Marketplace\Exception\MarketplaceExceptionInterface;

class InvalidRefundRequestException extends \Exception implements MarketplaceExceptionInterface
{
    /** @var mixed[] Marketplace exception additional data */
    protected $additionalData;

    private ErrorCode $errorCode;

    public function __construct(string $message, array $additionalData, \Throwable $previous = null)
    {
        $this->errorCode = ErrorCode::INVALID_REFUND_REQUEST();

        parent::__construct($message, $this->errorCode->getValue(), $previous);

        $this->additionalData = $additionalData;
    }

    /** @return mixed[] */
    public function getContext(): array
    {
        return $this->additionalData;
    }

    public function getErrorCode(): ErrorCode
    {
        return $this->errorCode;
    }
}
