<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Refund\Exception;

use Throwable;
use Wizacha\Marketplace\Exception\ErrorCode;
use Wizacha\Marketplace\Exception\MarketplaceExceptionInterface;

class RefundNotFoundException extends \Exception implements MarketplaceExceptionInterface
{
    /** @var int */
    protected $criteria;

    private ErrorCode $errorCode;

    /** @var mixed[] Criteria used to retrieve the refund */
    public function __construct(array $criteria, Throwable $previous = null)
    {
        $this->errorCode = ErrorCode::REFUND_NOT_FOUND();

        parent::__construct('Refund not found', $this->errorCode->getValue(), $previous);

        $this->criteria = $criteria;
    }

    /** @return mixed[] Criteria used to retrieve the refund */
    public function getContext(): array
    {
        return $this->criteria;
    }

    public function getErrorCode(): ErrorCode
    {
        return $this->errorCode;
    }
}
