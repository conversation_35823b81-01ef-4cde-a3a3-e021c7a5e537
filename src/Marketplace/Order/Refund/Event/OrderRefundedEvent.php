<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Refund\Event;

use Symfony\Contracts\EventDispatcher\Event;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Wizacha\Component\Notification\NotificationEvent;
use Wizacha\Marketplace\Order\CreditNote\CreditNote;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Refund\Entity\Refund;

class OrderRefundedEvent extends Event implements NotificationEvent
{
    /** @var Refund */
    protected $refund;

    /** @var Order */
    protected $order;

    /** @var CreditNote */
    protected $creditNote;

    public function __construct(Refund $refund, Order $order, CreditNote $creditNote = null)
    {
        $this->refund = $refund;
        $this->order = $order;
        $this->creditNote = $creditNote;
    }

    public static function buildDebugForm(FormBuilder $form): void
    {
        $form->add('refundId', TextType::class);
        $form->add('orderId', TextType::class);
    }

    public static function createFromForm(Form $form): self
    {
        $refundService = container()->get('marketplace.order.refund.refund_service');
        $orderService = container()->get('marketplace.order.order_service');
        $creditNoteService = container()->get(
            'marketplace.order.credit_note.credit_note_service'
        );

        $refund = $refundService->get((int) $form->getData()['refundId']);
        $order = $orderService->getOrder((int) $form->getData()['orderId']);
        $creditNote = null;
        if ($creditNoteService->isAvailable($order) === true) {
            $creditNote = $creditNoteService->buildCreditNote($refund, $order);
        }

        return new static($refund, $order, $creditNote);
    }

    public static function getDescription(): string
    {
        return 'order_refund_event';
    }

    public function getRefund(): Refund
    {
        return $this->refund;
    }

    public function setRefund(Refund $refund): self
    {
        $this->refund = $refund;

        return $this;
    }

    public function getOrder(): Order
    {
        return $this->order;
    }

    public function setOrder(Order $order): self
    {
        $this->order = $order;

        return $this;
    }

    public function getCreditNote(): ?CreditNote
    {
        return $this->creditNote;
    }

    public function setCreditNote(?CreditNote $creditNote): self
    {
        $this->creditNote = $creditNote;

        return $this;
    }
}
