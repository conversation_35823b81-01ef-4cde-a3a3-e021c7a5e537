<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Refund\Event;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Wizacha\Marketplace\Order\Event\OrderStatusUpdated;
use Wizacha\Marketplace\Order\OrderEvents;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\Order\Refund\Enum\RefundPaymentMethod;
use Wizacha\Marketplace\Order\Refund\Service\RefundNotificationService;
use Wizacha\Marketplace\Order\Refund\Service\RefundService;
use Wizacha\Marketplace\Order\Refund\Utils\RefundConfig;

class RefundEventSubscriber implements EventSubscriberInterface
{
    /** @var RefundService */
    protected $refundService;

    /** @var RefundNotificationService */
    protected $refundNotificationService;

    /** @var RefundConfig */
    protected $refundConfig;

    /** @var OrderService */
    protected $orderService;

    public function __construct(
        RefundService $refundService,
        RefundNotificationService $refundNotificationService,
        RefundConfig $refundConfig,
        OrderService $orderService
    ) {
        $this->refundService = $refundService;
        $this->refundNotificationService = $refundNotificationService;
        $this->orderService = $orderService;
        $this->refundConfig = $refundConfig;
    }

    /** @return string[] List of subscribed events and associated methods */
    public static function getSubscribedEvents(): array
    {
        return [
            OrderEvents::UPDATED  => ['refundOrder', 0],
        ];
    }

    public function refundOrder(OrderStatusUpdated $event): void
    {
        if ($event->getStatusFrom() === OrderStatus::INCOMPLETED) {
            return;
        }

        if (\in_array($event->getStatusTo(), [OrderStatus::CANCELED, OrderStatus::VENDOR_DECLINED], true) === false) {
            return;
        }

        if ($this->refundConfig->isRefundAutoAll() === false) {
            if ($event->getStatusTo() === OrderStatus::CANCELED
                && $this->refundConfig->isRefundAutoCanceled() === false
            ) {
                return;
            }

            if ($event->getStatusTo() === OrderStatus::VENDOR_DECLINED
                && $this->refundConfig->isRefundAutoRefused() === false
            ) {
                return;
            }
        }

        $order = $this->orderService->getOrder($event->getId());

        $refund = $this->refundService->createCompleteRefund($order);
        $refund = $this->refundService->executeRefund($refund, $order);

        $this->refundNotificationService->notifyRefund($refund, $order);
    }
}
