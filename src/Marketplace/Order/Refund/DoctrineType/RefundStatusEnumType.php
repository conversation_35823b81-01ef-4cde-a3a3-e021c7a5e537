<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Refund\DoctrineType;

use Acelaya\Doctrine\Type\AbstractPhpEnumType;
use Doctrine\DBAL\Platforms\AbstractPlatform;
use Wizacha\Marketplace\Order\Refund\Enum\RefundStatus;

/** Doctrine Type for RefundStatus */
class RefundStatusEnumType extends AbstractPhpEnumType
{
    protected $enumType = RefundStatus::class;

    public function convertToPHPValue($value, AbstractPlatform $platform)
    {
        return parent::convertToPHPValue((int) $value, $platform);
    }

    protected function getSpecificName(): string
    {
        return 'refund_status';
    }
}
