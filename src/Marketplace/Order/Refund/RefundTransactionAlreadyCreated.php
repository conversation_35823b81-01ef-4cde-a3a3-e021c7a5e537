<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Refund;

use Wizacha\Marketplace\Order\Refund\Entity\Refund;
use Wizacha\Marketplace\Transaction\Transaction;
use Wizacha\Money\Money;

class RefundTransactionAlreadyCreated extends \Exception
{
    public int $refundId;
    public int $orderId;
    public Money $amount;
    public ?string $transactionReference;
    public ?string $processorName;

    public function __construct(
        string $message,
        Refund $refund,
        Transaction $transaction
    ) {
        parent::__construct($message);

        $this->refundId = $refund->getId();
        $this->orderId = $refund->getOrderId();
        $this->amount = $refund->getAmount();
        $this->transactionReference = $transaction->getTransactionReference();
        $this->processorName = $transaction->getProcessorName();
    }
}
