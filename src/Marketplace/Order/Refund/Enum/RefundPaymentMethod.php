<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Refund\Enum;

use MyCLabs\Enum\Enum;

/**
 * @method static RefundPaymentMethod REFUND_CB()
 * @method static RefundPaymentMethod REFUND_OFFLINE()
 */
class RefundPaymentMethod extends Enum
{
    protected const REFUND_CB = 'refund_cb';
    protected const REFUND_OFFLINE = 'refund_offline';
}
