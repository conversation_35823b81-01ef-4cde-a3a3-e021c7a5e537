<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Refund\Enum;

use MyCLabs\Enum\Enum;

/**
 * @method static RefundStatus CREATED()
 * @method static RefundStatus PAID()
 * @method static RefundStatus FAILED()
 */
class RefundStatus extends Enum
{
    protected const CREATED = 0;
    protected const PAID = 1;
    protected const FAILED = 2;
}
