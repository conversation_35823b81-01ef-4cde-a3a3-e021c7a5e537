<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Refund\Enum;

use MyCLabs\Enum\Enum;

/**
 * @method static OrderRefundStatus NOT_REFUNDED()
 * @method static OrderRefundStatus REFUND_FAILED()
 * @method static OrderRefundStatus COMPLETE_REFUND()
 * @method static OrderRefundStatus PARTIAL_REFUND()
 */
class OrderRefundStatus extends Enum
{
    protected const NOT_REFUNDED = 0;
    protected const REFUND_FAILED = 1;
    protected const COMPLETE_REFUND = 2;
    protected const PARTIAL_REFUND = 3;
}
