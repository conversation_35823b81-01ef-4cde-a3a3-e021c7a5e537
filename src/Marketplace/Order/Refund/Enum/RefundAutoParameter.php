<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Refund\Enum;

use MyCLabs\Enum\Enum;

/**
 * @method static RefundAutoParameter NONE()
 * @method static RefundAutoParameter CANCELED()
 * @method static RefundAutoParameter REFUSED()
 * @method static RefundAutoParameter ALL()
 */
class RefundAutoParameter extends Enum
{
    protected const NONE = 'NONE';
    protected const CANCELED = 'CANCELED';
    protected const REFUSED = 'REFUSED';
    protected const ALL = 'ALL';
}
