<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Refund\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\Query\ResultSetMappingBuilder;
use Wizacha\Marketplace\Company\Company;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Refund\Entity\Refund;
use Wizacha\Marketplace\Order\Refund\Enum\RefundStatus;
use Wizacha\Marketplace\Order\Refund\Exception\RefundNotFoundException;
use Wizacha\Money\Money;

class RefundRepository extends ServiceEntityRepository
{
    public function save(Refund $refund): Refund
    {
        $this->getEntityManager()->persist($refund);
        $this->getEntityManager()->flush();

        return $refund;
    }

    public function get(int $id): Refund
    {
        $query = $this
            ->createQueryBuilder('refund')
            ->where('refund.id = :id')
            ->setParameter('id', $id)
            ->getQuery()
        ;

        try {
            return $query->getSingleResult();
        } catch (NoResultException $e) {
            throw new RefundNotFoundException(['id' => $id]);
        }
    }

    /** @param mixed[] $filters Doctrine filters */
    public function getOneBy(array $filters): Refund
    {
        $refund = $this->findOneBy($filters);

        if ($refund instanceof Refund === false) {
            throw new RefundNotFoundException($filters);
        }

        return $refund;
    }

    /** @return Refund[] */
    public function findPreviousRefunds(Order $order, Refund $refund = null): array
    {
        $qb = $this
            ->createQueryBuilder('refund')
            ->where('refund.orderId = :orderId')
            ->setParameter('orderId', $order->getId())
        ;

        if ($refund instanceof Refund && $refund->getId() !== null) {
            $qb->andWhere('refund.id != :refundId')
               ->setParameter('refundId', $refund->getId());
        }

        return $qb
            ->getQuery()
            ->getResult()
        ;
    }

    public function countExistingReferences(Company $company): int
    {
        // Order is not a Doctrine entity. :(
        $rsm = new ResultSetMappingBuilder($this->getEntityManager());
        $rsm->addScalarResult('count', 'count');

        $query = $this->getEntityManager()->createNativeQuery(
            '
            SELECT count(*) as count
            FROM doctrine_order_refunds dor
            INNER JOIN cscart_orders co ON co.order_id = dor.order_id
            WHERE co.company_id = :company
            AND dor.credit_note_reference IS NOT NULL
            ',
            $rsm
        );

        $query->setParameter('company', $company->getId());

        return (int) $query->getSingleScalarResult();
    }

    public function getOrderTotalAmountRefunded(Order $order): Money
    {
        $qb = $this
            ->createQueryBuilder('refund')
            ->select('SUM(refund.amount)')
            ->where('refund.orderId = :orderId AND refund.status = :status')
            ->setParameters(['orderId' => $order->getId(), 'status' => RefundStatus::PAID()->getValue()])
        ;

        $result = $qb
            ->getQuery()
            ->getSingleScalarResult()
            ;

        return $result !== null ? new Money((int) $result) : new Money(0);
    }
}
