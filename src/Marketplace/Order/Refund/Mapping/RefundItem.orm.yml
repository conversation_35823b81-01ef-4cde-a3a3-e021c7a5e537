Wizacha\Marketplace\Order\Refund\Entity\RefundItem:
    type: entity
    table: order_refund_items
    id:
        id:
            type: integer
            nullable: false
            options:
                unsigned: false
            generator:
                strategy: IDENTITY
    fields:
        itemId:
            type: integer
            nullable: false
        amount:
            type: money
            nullable: false
        quantity:
            type: integer
            nullable: false
        createdAt:
            type: datetime_immutable
            nullable: false
        updatedAt:
            type: datetime_immutable
            nullable: true
    manyToOne:
        refund:
            targetEntity: Wizacha\Marketplace\Order\Refund\Entity\Refund
            inversedBy: items
            joinColumn:
                name: refund_id
                referencedColumnName: id
                nullable: false
    uniqueConstraints:
        doctrine_order_refund_items_UN:
            columns: [item_id, refund_id]
