Wizacha\Marketplace\Order\Refund\Entity\Refund:
    type: entity
    table: order_refunds
    repositoryClass: Wizacha\Marketplace\Order\Refund\Repository\RefundRepository
    id:
        id:
            type: integer
            nullable: false
            options:
                unsigned: false
            generator:
                strategy: IDENTITY
    fields:
        orderId:
            type: integer
            nullable: false
        userId:
            type: integer
            nullable: false
        isPartial:
            type: boolean
            nullable: false
        hasShipping:
            type: boolean
            nullable: false
        amount:
            type: money
            nullable: false
        shippingAmount:
            type: money
            nullable: false
        basketDiscount:
            type: money
            nullable: false
        marketplaceDiscount:
            type: money
            nullable: false
        status:
            type: php_enum_refund_status
            nullable: false
        isLegacy:
            type: boolean
            nullable: false
        creditNoteReference:
            type: string
            length: 100
            nullable: true
        refundedAfterWithdrawalPeriod:
            type: boolean
            nullable: false
        createdAt:
            type: datetime_immutable
            nullable: false
        updatedAt:
            type: datetime_immutable
            nullable: true
    oneToMany:
        items:
            targetEntity: Wizacha\Marketplace\Order\Refund\Entity\RefundItem
            mappedBy: refund
            fetch: EXTRA_LAZY
            cascade: ["persist", "remove"]
