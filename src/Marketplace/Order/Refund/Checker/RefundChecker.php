<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Refund\Checker;

use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\Adjustment\OrderAdjustmentService;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\Order\Refund\Entity\Refund;
use Wizacha\Marketplace\Order\Refund\Enum\OrderRefundStatus;
use Wizacha\Marketplace\Order\Refund\Enum\RefundStatus;
use Wizacha\Marketplace\Order\Refund\Exception\InvalidRefundRequestException;
use Wizacha\Marketplace\Order\Refund\Utils\RefundAmountsCalculator;
use Wizacha\Marketplace\Payment\PaymentProcessorIdentifier;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Transaction\Transaction;
use Wizacha\Money\Money;

class RefundChecker
{
    /** @var OrderService */
    protected $orderService;

    /** @var OrderAdjustmentService */
    protected $orderAdjustmentService;

    /** @var RefundAmountsCalculator */
    protected $refundCalculator;

    /** @var boolean */
    protected $orderAdjustmentFlag;

    public function __construct(
        OrderService $orderService,
        OrderAdjustmentService $orderAdjustmentService,
        RefundAmountsCalculator $refundCalculator,
        bool $orderAdjustmentFlag
    ) {
        $this->orderService = $orderService;
        $this->orderAdjustmentFlag = $orderAdjustmentFlag;
        $this->orderAdjustmentService = $orderAdjustmentService;
        $this->refundCalculator = $refundCalculator;
    }

    /**
     * @param Refund[] $existingRefunds
     * @param Transaction[] $existingTransactions
     */
    public function check(Refund $refund, Order $order, array $existingRefunds, array $existingTransactions): bool
    {
        $this->checkOrderState($refund, $order);
        $this->checkContainsActualRefund($refund, $order);
        $this->checkAmountIntegrity($refund, $order);
        $this->checkRefunds($refund, $order, $existingRefunds);
        $this->checkTransactionsBalance($refund, $order, $existingTransactions);

        return true;
    }

    public function assertCanDeleteRefund(Refund $refund): bool
    {
        if ($refund->getStatus()->getValue() === RefundStatus::CREATED()->getValue()
            && $refund->isPartial() === true
        ) {
            return true;
        }

        throw new InvalidRefundRequestException(
            'Unable to assert that the refund can be deleted',
            ['refund' => $refund]
        );
    }

    /** @param Refund[] $existingRefunds */
    public function isOrderRefundable(Order $order, ?array $existingRefunds): bool
    {
        $status = [OrderStatus::CANCELED, OrderStatus::VENDOR_DECLINED, OrderStatus::PROCESSED, OrderStatus::COMPLETED];

        /* - Only cancelled and declined orders can be refunded
           - We can't refund orders which have already been entirely refunded.*/
        if (false === \in_array($order->getStatus()->getValue(), $status, true)
            || $order->getRefundStatus()->equals(OrderRefundStatus::COMPLETE_REFUND())
        ) {
            return false;
        }

        if ($this->refundCalculator->isOrderFullyRefunded($order, $existingRefunds, true) === true) {
            return false;
        }

        if ($order->getPayment()->isCompleted() === false
            && $order->getPayment()->getId() !== 0
            && $order->getPayment()->hasProcessorName(PaymentProcessorName::NONE()) === false
        ) {
            return false;
        }

        $paymentData = fn_get_payment_method_data($order->getPayment()->getId());

        if ((int) $paymentData['processor_id'] === PaymentProcessorIdentifier::HIPAY_CAPTURE()->getValue()
            && $order->isAcceptedByVendor() !== true
        ) {
            return false;
        }

        return true;
    }

    /**
     * We process a few checks on the order object to make sure that it is refundable:
     *   - it should not have been completely refunded
     *   - it must be paid
     *   - partial refunds are only available for delivered orders
     */
    protected function checkOrderState(Refund $refund, Order $order): bool
    {
        if ($order->getRefundStatus()->equals(OrderRefundStatus::COMPLETE_REFUND()) === true) {
            throw new InvalidRefundRequestException(
                __('refund_status_does_not_allow_more_refund'),
                ['refund_status' => strtolower($order->getRefundStatus()->getKey())]
            );
        }

        if ($order->getPayment()->isCompleted() !== true
            && $order->getPayment()->getId() !== 0
            && $order->getPayment()->hasProcessorName(PaymentProcessorName::NONE()) === false
        ) {
            throw new InvalidRefundRequestException(
                __('order_must_be_paid_for_refund'),
                ['order' => $order]
            );
        }

        if ($refund->isPartial() === true && $this->isOrderPartiallyRefundable($order) === false) {
            throw new InvalidRefundRequestException(
                __('order_status_does_not_allow_refund'),
                ['order' => $order]
            );
        }

        return true;
    }

    /**
     * General bunch of check to make sure that the provided Refund object is coherent:
     *   - refund amount or marketplace discount should be more than 0
     *   - complete refunds must contain items
     */
    protected function checkContainsActualRefund(Refund $refund, Order $order): bool
    {
        if ($refund->getMarketplaceDiscount()->isZero() === true
            && $refund->getAmount()->isZero() === true
        ) {
            throw new InvalidRefundRequestException(
                __('refund_amount_is_zero'),
                ['refund' => $refund]
            );
        }

        if ($refund->isPartial() === false) {
            if (\count($refund->getItems()) !== \count($order->getItems())) {
                throw new InvalidRefundRequestException(
                    __('item_number_mismatch_complete_refund'),
                    ['refund' => $refund]
                );
            }

            if ($refund->hasShipping() === false) {
                throw new InvalidRefundRequestException(
                    __('no_shipping_complete_refund'),
                    ['refund' => $refund]
                );
            }
        }

        if ($refund->isPartial() === true) {
            if (\count($refund->getItems()) === 0 && $refund->hasShipping() === false) {
                throw new InvalidRefundRequestException(
                    __('no_item_partial_refund'),
                    ['refund' => $refund]
                );
            }

            if ($refund->hasShipping() === false && $refund->getShippingAmount()->isZero() === false) {
                throw new InvalidRefundRequestException(
                    __('shipping_error_on_refund'),
                    ['refund' => $refund]
                );
            }
        }

        return true;
    }

    /**
     * Make sure that the current refund amount doesn't exceed the order amount,
     * including existing refund _transactions_.
     *
     * @param Transaction[] $existingTransactions
     */
    protected function checkTransactionsBalance(Refund $refund, Order $order, array $existingTransactions): bool
    {
        /** @var Money $total */
        $total = array_reduce($existingTransactions, function (Money $carry, Transaction $item): Money {
            return $item->getAmount()->add($carry);
        }, new Money(0));

        if ($total->add($refund->getAmount())->compareTo($order->getCustomerTotal()) === 1) {
            throw new InvalidRefundRequestException(
                __('transaction_amount_greater_than_order_amount'),
                ['refund' => $refund, 'order' => $order]
            );
        }

        return true;
    }

    /** @param Refund[] $existingRefunds */
    protected function checkRefunds(Refund $refund, Order $order, array $existingRefunds): bool
    {
        $this->checkRefundsBalance($refund, $order, $existingRefunds);
        $this->checkRefundsItemQuantities($refund, $order, $existingRefunds);

        return true;
    }

    /**
     * Make sure that the current refund amount doesn't exceed the order amount, including existing refund objects.
     *
     * @param Refund[] $existingRefunds
     */
    protected function checkRefundsBalance(Refund $refund, Order $order, array $existingRefunds): bool
    {
        /** @var Money $total */
        $total = array_reduce($existingRefunds, function (Money $carry, Refund $item): Money {
            return $item->getAmount()->reducePrecisionToCents()->add($carry);
        }, new Money(0));

        if ($total->add($refund->getAmount())->compareTo($order->getCustomerTotal()) === 1) {
            throw new InvalidRefundRequestException(
                __('refund_amount_greater_than_order_amount'),
                ['new_refund' => $refund, 'existing_refunds' => $existingRefunds]
            );
        }

        return true;
    }

    /**
     * Make sure that refund items included in the provided refund don't exceed the quantity of any order items.
     *
     * @param Refund[] $existingRefunds
     */
    protected function checkRefundsItemQuantities(Refund $refund, Order $order, array $existingRefunds): bool
    {
        $refundedItemsQuantity = [];
        $orderItemsQuantity = [];

        foreach ($existingRefunds as $existingRefund) {
            foreach ($existingRefund->getItems() as $refundItem) {
                $refundedItemsQuantity[$refundItem->getItemId()]
                    = ($refundedItemsQuantity[$refundItem->getItemId()] ?? 0) + $refundItem->getQuantity();
            }
        }

        foreach ($order->getItems() as $orderItem) {
            $orderItemsQuantity[$orderItem->getItemId()] = $orderItem->getQuantity();
        }

        foreach ($refund->getItems() as $item) {
            $newQuantity = $item->getQuantity() + ($refundedItemsQuantity[$item->getItemId()] ?? 0);
            if ($newQuantity > ($orderItemsQuantity[$item->getItemId()] ?? 0)) {
                throw new InvalidRefundRequestException(
                    __('refund_item_quantity_error'),
                    ['new_refund' => $refund, 'existing_refunds' => $existingRefunds]
                );
            }
        }

        return true;
    }

    /** We make sure that the amount in the Refund object is coherent with shipping and item amounts. */
    protected function checkAmountIntegrity(Refund $refund, Order $order): bool
    {
        if ($this->orderAdjustmentFlag === true) {
            $adjustments = $this->orderAdjustmentService->findByOrderId($refund->getOrderId());
            if (\count($adjustments) > 0) {
                return true;
            }
        }

        $aggregatedAmount = new Money(0);
        $nbItems = 0;

        foreach ($refund->getItems() as $item) {
            $itemQuantity = $item->getQuantity();
            $aggregatedAmount = $aggregatedAmount->add($item->getAmount()->multiply($itemQuantity));
            $nbItems += $itemQuantity;
        }

        if ($refund->hasShipping()) {
            $aggregatedAmount = $aggregatedAmount->add($refund->getShippingAmount());
        }

        $refundAmount = $refund->getAmount();
        $amountDifference = $aggregatedAmount->subtract($refundAmount)->getPreciseConvertedAmount();
        $amountDifferenceIsNegligible = $aggregatedAmount->equals($refund->getAmount())
            || \abs($amountDifference) <= $nbItems / 100;

        if (false === $amountDifferenceIsNegligible) {
            $priceExcludingTaxes = $order->getItems()[0]->isTaxIncludedInPrice() === false;

            // If there is a discount, the total amount might have been re-adjusted
            // and not exactly match the items amount (max variance: 0.01 per item)
            if ($order->getDiscountTotal()->isZero() === false
                || $order->getMarketplaceDiscountTotal()->isZero() === false
                || $priceExcludingTaxes
            ) {
                return true;
            }

            throw new InvalidRefundRequestException(
                __('refund_amount_mismatch'),
                ['new_refund' => $refund]
            );
        }

        return true;
    }

    /**
     * The current scope of partial refunds limit them to delivered or completed orders only.
     * This restriction might be lifted in future versions.
     */
    protected function isOrderPartiallyRefundable(Order $order): bool
    {
        return $this->orderService->isDelivered($order->getId())
            || $order->getStatus()->equals(OrderStatus::COMPLETED()) === true;
    }
}
