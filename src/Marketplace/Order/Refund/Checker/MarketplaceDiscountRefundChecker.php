<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Refund\Checker;

use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Refund\Entity\Refund;
use Wizacha\Marketplace\Order\Refund\Exception\InvalidRefundRequestException;
use Wizacha\Marketplace\Transaction\Transaction;
use Wizacha\Money\Money;

class MarketplaceDiscountRefundChecker
{
    /** @param Transaction[] $transactions */
    public function check(Order $order, Refund $refund, Money $amountToRefund, array $transactions): bool
    {
        $this->checkAmount($order, $refund, $amountToRefund);
        $this->checkExistingTransactions($order, $refund, $amountToRefund, $transactions);

        return true;
    }

    protected function checkAmount(Order $order, Refund $refund, Money $amountToRefund): bool
    {
        if ($amountToRefund->isNegative() === true) {
            throw new InvalidRefundRequestException(
                __('cannot_refund_mp_discount_negative_amount'),
                [
                    'refundId' => $refund->getId(),
                    'amountToRefund' => $amountToRefund->getConvertedAmount(),
                ]
            );
        }

        if ($order->getMarketplaceDiscountTotal()->compareTo($amountToRefund) === -1) {
            throw new InvalidRefundRequestException(
                __('cannot_refund_mp_discount_too_big'),
                [
                    'refundId' => $refund->getId(),
                    'amountToRefund' => $amountToRefund->getConvertedAmount(),
                ]
            );
        }

        return true;
    }

    /** @param Transaction[] $transactions */
    protected function checkExistingTransactions(
        Order $order,
        Refund $refund,
        Money $amountToRefund,
        array $transactions
    ): bool {
        $transactionsAmount = new Money(0);
        foreach ($transactions as $transaction) {
            $transactionsAmount = $transactionsAmount->add($transaction->getAmount());
        }

        $transactionsAmount = $transactionsAmount->add($amountToRefund);

        if ($order->getMarketplaceDiscountTotal()->subtract($transactionsAmount)->isNegative()) {
            throw new InvalidRefundRequestException(
                __('cannot_refund_mp_discount_too_big'),
                [
                    'refundId' => $refund->getId(),
                    'amountToRefund' => $amountToRefund->getConvertedAmount(),
                ]
            );
        }

        return true;
    }
}
