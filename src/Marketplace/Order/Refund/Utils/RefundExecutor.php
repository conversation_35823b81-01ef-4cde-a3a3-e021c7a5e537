<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Refund\Utils;

use Exception;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\Refund\Checker\MarketplaceDiscountRefundChecker;
use Wizacha\Marketplace\Order\Refund\Entity\Refund;
use Wizacha\Marketplace\Order\Refund\Enum\OrderRefundStatus;
use Wizacha\Marketplace\Order\Refund\Enum\RefundStatus;
use Wizacha\Marketplace\Payment\PaymentType;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionStatus;
use Wizacha\Marketplace\Transaction\TransactionType;

class RefundExecutor
{
    /** @var RefundPayment */
    protected $refundPayment;

    /** @var RefundAmountsCalculator */
    protected $refundCalculator;

    /** @var MarketplaceDiscountRefundChecker */
    protected $mpDiscountRefundChecker;

    /** @var OrderService */
    protected $orderService;

    /** @var TransactionService */
    protected $transactionService;

    public function __construct(
        RefundPayment $refundPayment,
        RefundAmountsCalculator $refundCalculator,
        MarketplaceDiscountRefundChecker $mpDiscountRefundChecker,
        OrderService $orderService,
        TransactionService $transactionService
    ) {
        $this->refundPayment = $refundPayment;
        $this->refundCalculator = $refundCalculator;
        $this->mpDiscountRefundChecker = $mpDiscountRefundChecker;
        $this->orderService = $orderService;
        $this->transactionService = $transactionService;
    }

    /** @param Refund[] $existingRefunds */
    public function executeRefund(
        Refund $refund,
        Order $order,
        array $existingRefunds = []
    ): Refund {
        $encounteredError = null;

        if (false === $refund->getAmount()->isZero()) {
            try {
                $refund = $this->refundPayment->executeRefundPayment($refund, $order);
            } catch (Exception $exception) {
                if (false === $order->getPayment()->isOfType(PaymentType::MANUAL())) {
                    $encounteredError = $exception;
                }
            }
        }

        if ($order->getMarketplaceDiscountTotal()->isPositive() === true) {
            $this->executeMarketplaceDiscountRefund($refund, $order);
        }

        $this->updateOrderStatus($refund, $order, $existingRefunds);

        if ($encounteredError instanceof Exception) {
            throw $encounteredError;
        }

        return $refund;
    }

    public function updateOrderStatus(Refund $refund, Order $order, array $existingRefunds): void
    {
        $existingRefunds = array_merge(
            array_filter($existingRefunds, function (Refund $refund): bool {
                return \in_array($refund->getStatus(), [RefundStatus::PAID()]);
            }),
            [$refund]
        );
        if ($refund->getStatus()->equals(RefundStatus::FAILED())) {
            $this->orderService->refundOrder($order, OrderRefundStatus::REFUND_FAILED());
        } elseif ($refund->isPartial() === false
            || $this->refundCalculator->isOrderFullyRefunded($order, $existingRefunds, true) === true
        ) {
            $this->orderService->refundOrder($order, OrderRefundStatus::COMPLETE_REFUND());
        } else {
            $this->orderService->refundOrder($order, OrderRefundStatus::PARTIAL_REFUND());
        }
    }

    protected function executeMarketplaceDiscountRefund(Refund $refund, Order $order): void
    {
        if ($order->getMarketplaceDiscountTotal()->isPositive() === false) {
            return;
        }

        $existingTransactions = $this->transactionService->findBy([
            'orderId' => $order->getId(),
            'type' => TransactionType::REFUND_TRANSFER(),
            'status' => TransactionStatus::SUCCESS(),
        ]);

        $amountToRefund = $refund->getMarketplaceDiscount();

        // With percentage discount on shipping, the total amount can be slightly negative on refund without shipping
        if ($amountToRefund->isZero() === true || $amountToRefund->isNegative()) {
            return;
        }

        $this->mpDiscountRefundChecker->check($order, $refund, $amountToRefund, $existingTransactions);
        $this->refundPayment->executeDiscountRefundPayment($refund, $order, $amountToRefund);
    }
}
