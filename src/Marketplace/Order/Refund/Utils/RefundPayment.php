<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Refund\Utils;

use Exception;
use Psr\Log\LoggerInterface;
use Wizacha\Marketplace\Order\Exception\OrderNotFound;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\Order\Refund\Entity\Refund;
use Wizacha\Marketplace\Order\Refund\Enum\RefundStatus;
use Wizacha\Marketplace\Order\Refund\Exception\FeatureNotImplementedException;
use Wizacha\Marketplace\Payment\Exception\UnsupportedApiException;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\Processor\PaymentProcessorProvider;
use Wizacha\Marketplace\Payment\Processor\RefundMarketplaceDiscountProcessorInterface;
use Wizacha\Marketplace\Payment\Processor\RefundProcessorInterface;
use Wizacha\Marketplace\Transaction\Exception\TransactionNotFound;
use Wizacha\Marketplace\Transaction\Transaction;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionType;
use Wizacha\Money\Money;

class RefundPayment
{
    /** @var LoggerInterface */
    protected $logger;

    /** @var TransactionService */
    protected $transactionService;

    /** @var OrderService */
    private $orderService;

    /** @var PaymentProcessorProvider */
    protected $paymentProcessorProvider;

    public function __construct(
        LoggerInterface $logger,
        TransactionService $transactionService,
        PaymentProcessorProvider $paymentProcessorProvider,
        OrderService $orderService
    ) {
        $this->logger = $logger;
        $this->transactionService = $transactionService;
        $this->paymentProcessorProvider = $paymentProcessorProvider;
        $this->orderService = $orderService;
    }

    public function executeRefundPayment(Refund $refund, Order $order): Refund
    {
        try {
            $originTransaction = $this->getOriginTransaction($order);
        } catch (TransactionNotFound | FeatureNotImplementedException $e) {
            $this->logger->error('Could not retrieve the original transaction for refund', ['exception' => $e]);

            // If the original transaction can't be found, we set its status to failed.
            $refund->setStatus(RefundStatus::FAILED());

            throw $e;
        }

        $processor = $this->getRefundProcessor($originTransaction);

        if (\is_null($processor)) {
            return $refund;
        }

        try {
            $processor->refund($originTransaction, $refund);

            $refund->setStatus(RefundStatus::PAID());
        } catch (Exception $exception) {
            $refund->setStatus(RefundStatus::FAILED());

            throw $exception;
        }

        return $refund;
    }

    public function executeDiscountRefundPayment(Refund $refund, Order $order, Money $amountToRefund): void
    {
        $originTransaction = $this->transactionService->retrievePaymentMPDiscountTransaction($order);

        // If the payment service does not support refunds, just skip the execution.
        try {
            $processorName = new PaymentProcessorName($originTransaction[0]->getProcessorName());
            $processor = $this->paymentProcessorProvider->getPaymentProcessorService($processorName);

            if (false === $processor instanceof RefundProcessorInterface) {
                throw new UnsupportedApiException();
            }
        } catch (UnsupportedApiException $e) {
            $this->logger->warning('Unsupported refund payment service processor', ['exception' => $e]);

            return;
        }

        if ($processor instanceof RefundMarketplaceDiscountProcessorInterface) {
            $processor->refundMarketplaceDiscount($order, $refund, $amountToRefund);
            $refund->setStatus(RefundStatus::PAID());
        }
    }

    protected function getRefundProcessor(Transaction $originTransaction): ?RefundProcessorInterface
    {
        try {
            $processorName = new PaymentProcessorName($originTransaction->getProcessorName());
            $processor = $this->paymentProcessorProvider->getPaymentProcessorService($processorName);

            $order = $this->orderService->getOrder($originTransaction->getOrderId());
            if ($processor instanceof RefundProcessorInterface
                && $originTransaction->getType()->equals(TransactionType::CREDITCARD())
                && $order->getStatus()->equals(OrderStatus::COMPLETED()) === false
            ) {
                return $processor;
            }

            $offline = $this->paymentProcessorProvider->getPaymentProcessorService(PaymentProcessorName::NONE());

            if ($offline instanceof RefundProcessorInterface) {
                return $offline;
            }

            throw new UnsupportedApiException();
        } catch (UnsupportedApiException $e) {
            $this->logger->warning('Unsupported refund payment service processor', ['exception' => $e]);
        } catch (OrderNotFound $exception) {
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);
        }

        return null;
    }

    protected function getOriginTransaction(Order $order): Transaction
    {
        $transactions = $this->transactionService->retrievePaymentTransaction($order);

        if (\count($transactions) === 0) {
            throw new TransactionNotFound(__('refund.error.original_transaction_not_found'));
        }

        if (\count($transactions) > 1) {
            throw new FeatureNotImplementedException(__('refund.error.multiple_transactions_not_supported'));
        }

        return $transactions[0];
    }
}
