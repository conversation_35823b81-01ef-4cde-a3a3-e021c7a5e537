<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Refund\Utils;

use Wizacha\Marketplace\Order\Refund\Enum\RefundAutoParameter;

class RefundConfig
{
    /** @var RefundAutoParameter  */
    protected $refundAuto;

    public function __construct(?string $refundAuto)
    {
        $this->refundAuto = new RefundAutoParameter($this->setRefundAutoToNoneIfObsoleteValue($refundAuto));
    }

    public function getRefundAuto(): RefundAutoParameter
    {
        return $this->refundAuto;
    }

    public function isRefundAutoAll(): bool
    {
        return $this->refundAuto->equals(RefundAutoParameter::ALL());
    }

    public function isRefundAutoCanceled(): bool
    {
        return $this->refundAuto->equals(RefundAutoParameter::CANCELED());
    }

    public function isRefundAutoRefused(): bool
    {
        return $this->refundAuto->equals(RefundAutoParameter::REFUSED());
    }

    protected function setRefundAutoToNoneIfObsoleteValue(?string $refundAuto): string
    {
        if (true === \is_null($refundAuto)
            || "" === $refundAuto
            || false === \in_array($refundAuto, RefundAutoParameter::values())
        ) {
            $refundAuto = RefundAutoParameter::NONE()->getValue();
        }

        return $refundAuto;
    }
}
