<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Refund\Utils;

use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderItem;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\Order\Refund\Entity\Refund;
use Wizacha\Marketplace\Order\Refund\Entity\RefundItem;
use Wizacha\Marketplace\Order\Refund\Enum\RefundStatus;
use Wizacha\Marketplace\Order\Refund\Exception\InvalidRefundRequestException;
use Wizacha\Money\Money;

class RefundCreator
{
    /** @var RefundAmountsCalculator */
    protected $refundCalculator;

    public function __construct(RefundAmountsCalculator $refundCalculator)
    {
        $this->refundCalculator = $refundCalculator;
    }

    /**
     * @param mixed[] $items ['itemId' => <Item ID>, 'quantity' => <Quantity>]
     * @param Refund[] $existingRefunds
     */
    public function createRefund(
        Order $order,
        int $userId = null,
        bool $completeRefund = false,
        string $message = null,
        array $items = null,
        bool $hasShipping = true,
        string $creditNoteReference = null,
        array $existingRefunds = []
    ): Refund {
        if ($completeRefund === true) {
            foreach ($order->getItems() as $item) {
                $items[] = ['itemId' => $item->getItemId(), 'quantity' => $item->getQuantity()];
            }
        } else {
            $this->assertValidItemsFormat($items);
        }

        $refund = new Refund();
        $shippingAmount = $completeRefund || $hasShipping ? $order->getShippingCostAfterPromotion() : new Money(0);

        $refund
            ->setOrderId($order->getId())
            ->setUserId($userId)
            ->setStatus(RefundStatus::CREATED())
            ->setIsPartial($completeRefund === false)
            ->setHasShipping($completeRefund || $hasShipping)
            ->setShippingAmount($shippingAmount)
            ->setBasketDiscount(new Money(0))
            ->setMarketplaceDiscount(new Money(0))
            ->setIsLegacy(false)
            ->setMessage($message)
            ->setCreditNoteReference($creditNoteReference)
            ->setAmount($shippingAmount)
        ;

        $refundedAfterWithdrawalPeriod = false;
        if ($order->getStatus()->equals(OrderStatus::COMPLETED()) === true) {
            $refundedAfterWithdrawalPeriod = true;
        }

        $refund->setRefundedAfterWithdrawalPeriod($refundedAfterWithdrawalPeriod);

        if (\count($items ?? []) > 0) {
            $this->processRefundItems($refund, $order, $items);
        }

        $this->refundCalculator->calculateMpDiscount($refund, $order);

        return $this->refundCalculator->fixDiscountedItemAmounts($order, $refund, $existingRefunds);
    }

    /** @param mixed[] $items ['itemId' => <Item ID>, 'quantity' => <Quantity>] */
    protected function processRefundItems(Refund $refund, Order $order, array $items): Refund
    {
        $itemsMap = [];

        foreach ($items as $item) {
            $itemsMap[$item['itemId']] = $item['quantity'];
        }

        $totalBasketDiscountRefunded = new Money(0);
        foreach ($order->getItems() as $orderItem) {
            if (\array_key_exists((int) $orderItem->getItemId(), $itemsMap) === false) {
                continue;
            }

            $refundItem = $this->createRefundItem($refund, $orderItem, $itemsMap[(int) $orderItem->getItemId()]);
            $refund->setAmount(
                $refund->getAmount()->add($refundItem->getAmount()->multiply($refundItem->getQuantity()))
            );
            $basketDiscount = $orderItem->getAmountItem()->getUnitDiscount()->multiply($refundItem->getQuantity());
            $totalBasketDiscountRefunded = $totalBasketDiscountRefunded->add($basketDiscount);
        }

        $refund->setBasketDiscount($totalBasketDiscountRefunded);
        return $refund;
    }

    protected function createRefundItem(Refund $refund, OrderItem $orderItem, int $quantity): RefundItem
    {
        $refundItem = new RefundItem();

        $refundItem
            ->setItemId((int) $orderItem->getItemId())
            ->setAmount($orderItem->getDiscountedUnitPriceForRefund())
            ->setQuantity($quantity)
            ->setRefund($refund)
        ;
        $refund->addItem($refundItem);

        return $refundItem;
    }

    /** @param mixed[] $items */
    protected function assertValidItemsFormat(?array $items): bool
    {
        foreach ($items ?? [] as $item) {
            if (\array_key_exists('itemId', $item) === false || \array_key_exists('quantity', $item) === false) {
                throw new InvalidRefundRequestException(
                    __('bad_refund_item_format'),
                    ['item' => $item]
                );
            }
        }

        return true;
    }
}
