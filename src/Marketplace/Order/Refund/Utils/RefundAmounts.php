<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Refund\Utils;

use Wizacha\Component\Order\Amounts;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Order\AmountTaxesDetail\AmountTaxesDetail;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Refund\Entity\Refund;
use Wizacha\Money\Money;
use Wizacha\Tax;

class RefundAmounts
{
    protected const EXCLUDING_TAXES = 'excludingTaxes';

    protected const TAXES = 'taxes';

    protected const INCLUDING_TAXES = 'includingTaxes';

    /** @var Amounts */
    protected $amounts;

    /** @var Amounts */
    protected $discountedAmounts;

    /** @var mixed[] */
    protected $taxesAmounts;

    /** @var mixed[] */
    protected $shippingsAmounts;

    public function __construct(Refund $refund, Order $order)
    {
        $this->amounts = new Amounts();
        $this->discountedAmounts = new Amounts();

        $this->computeAmounts($refund, $order);
    }

    public function addTaxesAmounts(int $taxId, float $taxRate, Money $amount): self
    {
        $this->taxesAmounts[$taxId] = [
            'taxRate' => $taxRate,
            'amount' => $this->taxesAmounts[$taxId]['amount'] instanceof Money
                ? $this->taxesAmounts[$taxId]['amount']->add($amount)
                : $amount,
        ];

        return $this;
    }

    /** @return mixed[] */
    public function exposeTaxesAmounts(): array
    {
        $taxes = [];

        foreach ($this->taxesAmounts as $id => $taxDetails) {
            $taxes[] = [
                'taxId' => $id,
                'taxRate' => $taxDetails['taxRate'],
                'amount' => $taxDetails['amount']->getConvertedAmount(),
            ];
        }

        return $taxes;
    }

    public function exposeTotalItemsAmounts(): array
    {
        return (new AmountTaxesDetail(
            'totalItemsPrice',
            $this->amounts->getAmountExcludingTaxes(),
            $this->amounts->getTaxAmount(),
            $this->amounts->getAmountIncludingTaxes()
        ))->expose();
    }

    /** @return mixed[] */
    public function exposeShippingsAmounts(): array
    {
        return (new AmountTaxesDetail(
            'totalShippingPrice',
            $this->shippingsAmounts[static::EXCLUDING_TAXES],
            $this->shippingsAmounts[static::TAXES],
            $this->shippingsAmounts[static::INCLUDING_TAXES]
        ))->expose();
    }

    /** @return mixed[] */
    public function exposeGlobalAmounts(): array
    {
        return (new AmountTaxesDetail(
            'globalPrices',
            $this->discountedAmounts->getAmountExcludingTaxes()->add($this->shippingsAmounts[static::EXCLUDING_TAXES]),
            $this->discountedAmounts->getTaxAmount()->add($this->shippingsAmounts[static::TAXES]),
            $this->discountedAmounts->getAmountIncludingTaxes()->add($this->shippingsAmounts[static::INCLUDING_TAXES])
        ))->expose();
    }

    protected function computeAmounts(Refund $refund, Order $order): self
    {
        $orderItems = [];
        $taxId = 0;
        $taxRate = 0.;

        foreach ($order->getItems() as $orderItem) {
            $orderItems[$orderItem->getItemId()] = $orderItem;
        }

        $this->shippingsAmounts = [
            static::EXCLUDING_TAXES => new Money(0),
            static::TAXES => new Money(0),
            static::INCLUDING_TAXES => new Money(0),
        ];

        if ($refund->hasShipping()) {
            $this->shippingsAmounts = [
                static::EXCLUDING_TAXES => $order->getShippingCostWithoutTax(),
                static::TAXES => $order->getShippingTaxCost(),
                static::INCLUDING_TAXES => $order->getShippingCost(),
            ];

            $this->addTaxesAmounts(
                $order->getShippingTaxId(),
                $order->getShippingTaxRate()->toFloat(),
                $order->getShippingTaxCost()
            );
        }


        foreach ($refund->getItems() as $item) {
            $itemId = $item->getItemId();
            $item->initializeAmounts($orderItems[$itemId], $item->getQuantity());
            $this->amounts = $this->amounts->withLine($item->getLine());
            $this->discountedAmounts = $this->discountedAmounts->withLine($item->getDiscountedLine());

            foreach ($orderItems as $orderItem) {
                foreach ($orderItem->filterTaxes('P_' . $itemId) as $taxData) {
                    $taxId = (int) Tax::getIdFromTax(
                        $taxData['description'] ?? '',
                        (string) GlobalState::interfaceLocale()
                    );
                    $taxRate = $orderItem->getTaxRate()->toFloat();

                    break 2;
                }
            }

            $this->addTaxesAmounts(
                $taxId,
                $taxRate,
                $item->getDiscountedLine()->getTaxAmount()
            );
        }

        return $this;
    }
}
