<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Refund\Utils;

use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderItem;
use Wizacha\Marketplace\Order\Adjustment\OrderAdjustmentService;
use Wizacha\Marketplace\Order\Refund\Entity\Refund;
use Wizacha\Marketplace\Order\Refund\Repository\RefundRepository;
use Wizacha\Marketplace\Transaction\Transaction;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionStatus;
use Wizacha\Marketplace\Transaction\TransactionType;
use Wizacha\Money\Money;
use Wizacha\Marketplace\Promotion\Bonus\FixedBonus;
use Wizacha\Marketplace\Promotion\Bonus\PercentageBonus;

class RefundAmountsCalculator
{
    /** @var OrderAdjustmentService */
    protected $orderAdjustmentService;

    /** @var TransactionService */
    private TransactionService $transactionService;

    /** @var RefundRepository */
    private RefundRepository $refundRepository;

    /** @var boolean */
    protected $orderAdjustmentFlag;

    public function __construct(
        OrderAdjustmentService $orderAdjustmentService,
        TransactionService $transactionService,
        RefundRepository $refundRepository,
        bool $orderAdjustmentFlag
    ) {
        $this->orderAdjustmentFlag = $orderAdjustmentFlag;
        $this->transactionService = $transactionService;
        $this->refundRepository = $refundRepository;
        $this->orderAdjustmentService = $orderAdjustmentService;
    }

    /**
     * This method is used to be 100% sure that we won't have any floating issue with
     * discount total amount calculations. If the refund is the last that can be done on the order (item refund),
     * then we make sure that the refund amount is the difference between the amount paid and the remaining balance.
     *
     * @param Refund[] $existingRefunds
     */
    public function fixDiscountedItemAmounts(
        Order $order,
        Refund $currentRefund,
        array $existingRefunds = []
    ): Refund {
        // There are still items in the order that can be refunded, skipping
        if ($this->isOrderFullyRefunded($order, array_merge($existingRefunds, [$currentRefund])) === false) {
            return $currentRefund;
        }

        $accumulatedAmount = new Money(0);

        foreach ($existingRefunds as $refund) {
            // Remove precise amount, to make sure that we refund the right amount.
            $accumulatedAmount = $accumulatedAmount
                ->add($refund->getAmount()->reducePrecisionToCents())
                ->subtract($refund->getShippingAmount());
        }

        $shippingAmount = $currentRefund->getShippingAmount();
        $adjustments = [];
        if ($this->orderAdjustmentFlag === true) {
            $adjustments = $this->orderAdjustmentService->findByOrderId($order->getId());
        }

        if (\count($adjustments) > 0) {
            $orderTotal = $order->getTotal()->subtract($order->getShippingCostAfterPromotion());
        } else {
            $orderTotal = $order->getCustomerTotal()->subtract($order->getShippingCostAfterPromotion());
        }

        $currentRefund
            ->setAmount($orderTotal->subtract($accumulatedAmount)->add($shippingAmount));

        return $currentRefund;
    }

    public function calculateMpDiscount(Refund $refund, Order $order): Refund
    {
        $existingTransactions = $this->transactionService->findBy([
            'orderId' => $order->getId(),
            'type' => TransactionType::REFUND_TRANSFER(),
            'status' => TransactionStatus::SUCCESS(),
        ]);

        $refunded = array_reduce(
            $existingTransactions,
            function (?Money $carry, Transaction $transaction): Money {
                return ($carry ?? new Money(0))->add($transaction->getAmount());
            }
        );

        $existingRefunds = $this->refundRepository->findPreviousRefunds($order, $refund);

        $amount = $this->getRefundableMarketplaceDiscountAmount($order, $refund, $refunded, $existingRefunds);

        $refund->setMarketplaceDiscount($amount);

        return $refund;
    }

    /**
     * This calculates the amount to refund to the marketplace when the refunded order includes a MP discount.
     *
     * To sum it, this methods:
     *    1. return the remaining amount to refund on the discount if the order is fully refunded
     *    2. Calculates the discount amount on shipping
     *    3. Calculate the discount amount on items per unit
     *    4. Then calculate the amount to refund depending on the provided Refund object
     *
     * @param Refund[] $existingRefunds
     */
    public function getRefundableMarketplaceDiscountAmount(
        Order $order,
        Refund $refund,
        ?Money $refunded,
        array $existingRefunds
    ): Money {
        $isOrderFullyRefunded = $this->isOrderFullyRefunded(
            $order,
            array_merge([$refund], $existingRefunds),
            true
        );

        if ($isOrderFullyRefunded === true) {
            return $order->getMarketplaceDiscountTotal()->subtract($refunded ?? new Money(0))->reducePrecisionToCents();
        }

        $itemsDiscount = $order->getMarketplaceDiscountTotal();
        $itemsCustomerSubtotal = $order->getCustomerTotal()->subtract($order->getShippingCostAfterPromotion());
        $refundableAmount = new Money(0);
        $shippingDiscountAmount = new Money(0);

        // We need to exclude shipping discounts from the discount total
        foreach ($order->getPromotions() as $promotion) {
            if (\array_key_exists('target', $promotion) === false
                || $promotion['target'] !== 'shipping'
                || $promotion['promotion_type'] !== 'marketplace'
                || 0 === \count($promotion['bonus'])
            ) {
                continue;
            }

            $promotionBonus = $promotion['bonus'][0];
            if ($promotionBonus['type'] === 'fixed') {
                $bonus = new FixedBonus(Money::fromVariable($promotionBonus['reduction']));
            } elseif ($promotionBonus['type'] === 'percentage') {
                $bonus = new PercentageBonus((float) $promotionBonus['reduction']);
            } else {
                continue;
            }

            if (($bonus instanceof FixedBonus) || ($bonus instanceof PercentageBonus)) {
                $shippingDiscountAmount = $shippingDiscountAmount->add($bonus->getAmount($order->getShippingCostAfterPromotion()));
            }
        }

        $itemsDiscount = $itemsDiscount->subtract($shippingDiscountAmount);

        if ($refund->hasShipping() && $shippingDiscountAmount->isPositive()) {
            if ($shippingDiscountAmount->greaterThan($order->getMarketplaceDiscountTotal()) === true) {
                $shippingDiscountAmount = $order->getMarketplaceDiscountTotal();
            }

            $refundableAmount = $refundableAmount->add($shippingDiscountAmount);
        }

        $itemsInitialSubtotal = $itemsCustomerSubtotal->add($itemsDiscount);
        $itemsCustomerSubtotal = $order->getCustomerTotal()->subtract($order->getShippingCostAfterPromotion());

        /** @var OrderItem[] $orderItems */
        $orderItems = [];
        foreach ($order->getItems() as $item) {
            $orderItems[$item->getItemId()] = $item;
        }

        foreach ($refund->getItems() as $refundItem) {
            $discount = $orderItems[$refundItem->getItemId()]->getDiscountedUnitPrice()->subtract($orderItems[$refundItem->getItemId()]->getDiscountedUnitPriceForRefund());
            $refundableAmount = $refundableAmount->add($discount->multiply($refundItem->getQuantity()));
        }

        return $refundableAmount->reducePrecisionToCents();
    }

    /**
     * This methods checks if an order is fully refunded based on a list of existing Refund objects.
     * It can checks if the complete order is refunded, or only the items part (without shippings).
     *
     * @param Refund[] $refunds Existing refunds
     */
    public function isOrderFullyRefunded(Order $order, ?array $refunds, bool $checkShipping = false): bool
    {
        if (\is_array($refunds) === false || \count($refunds) === 0) {
            return false;
        }

        $shippingPaid = false;
        $orderItemsCount = [];
        foreach ($order->getItems() as $item) {
            $orderItemsCount[$item->getItemId()] = $item->getQuantity();
        }

        foreach ($refunds as $refund) {
            foreach ($refund->getItems() as $item) {
                $orderItemsCount[$item->getItemId()] -= $item->getQuantity();
            }

            if ($refund->hasShipping() === true || $order->getShippingCostAfterPromotion()->isZero()) {
                $shippingPaid = true;
            }
        }

        return \count(array_filter($orderItemsCount)) === 0
            && ($checkShipping === false || $checkShipping === true && $shippingPaid);
    }
}
