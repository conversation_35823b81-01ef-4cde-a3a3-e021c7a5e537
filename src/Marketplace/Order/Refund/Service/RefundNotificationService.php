<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Refund\Service;

use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Wizacha\Marketplace\Order\CreditNote\CreditNoteService;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Refund\Entity\Refund;
use Wizacha\Marketplace\Order\Refund\Event\OrderRefundedEvent;

class RefundNotificationService
{
    /** @var CreditNoteService */
    protected $creditNoteService;

    /** @var EventDispatcherInterface */
    protected $eventDispatcher;

    public function __construct(CreditNoteService $creditNoteService, EventDispatcherInterface $eventDispatcher)
    {
        $this->creditNoteService = $creditNoteService;
        $this->eventDispatcher = $eventDispatcher;
    }

    public function notifyRefund(Refund $refund, Order $order): void
    {
        $creditNote = null;
        if ($this->creditNoteService->isAvailable($order) === true) {
            $creditNote = $this->creditNoteService->buildCreditNote($refund, $order);
        }

        $this->eventDispatcher->dispatch(
            new OrderRefundedEvent($refund, $order, $creditNote),
            OrderRefundedEvent::class
        );
    }
}
