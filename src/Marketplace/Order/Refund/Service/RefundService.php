<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Refund\Service;

use Wizacha\Marketplace\Commission\CommissionService;
use Wizacha\Marketplace\Company\Company;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\Refund\Checker\RefundChecker;
use Wizacha\Marketplace\Order\Refund\Entity\Refund;
use Wizacha\Marketplace\Order\Refund\Enum\RefundPaymentMethod;
use Wizacha\Marketplace\Order\Refund\Enum\RefundStatus;
use Wizacha\Marketplace\Order\Refund\Exception\InvalidRefundRequestException;
use Wizacha\Marketplace\Order\Refund\Repository\RefundRepository;
use Wizacha\Marketplace\Order\Refund\Utils\RefundCreator;
use Wizacha\Marketplace\Order\Refund\Utils\RefundExecutor;
use Wizacha\Marketplace\Order\Tracer\Tracer;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionStatus;
use Wizacha\Marketplace\Transaction\TransactionType;
use Wizacha\AppBundle\Security\User\UserService;
use Wizacha\Marketplace\Order\Refund\RefundTransactionAlreadyCreated;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Transaction\Exception\TransactionNotFound;
use Wizacha\Money\Money;

/**
 * Service class used to handle operations related to payment refunds.
 */
class RefundService
{
    /** @var RefundRepository */
    protected $repository;

    /** @var TransactionService */
    protected $transactionService;

    /** @var OrderService */
    protected $orderService;

    /** @var RefundChecker */
    protected $refundChecker;

    /** @var Tracer */
    protected $orderDebugTracer;

    /** @var RefundCreator */
    protected $refundCreator;

    /** @var RefundExecutor */
    protected $refundExecutor;

    /** @var CommissionService */
    protected $commissionService;

    /** @var UserService */
    protected $userService;

    /** @var bool */
    protected $merchantsRefundAbility;

    public function __construct(
        RefundRepository $repository,
        TransactionService $transactionService,
        OrderService $orderService,
        RefundChecker $refundChecker,
        Tracer $orderDebugTracer,
        RefundCreator $refundCreator,
        RefundExecutor $refundExecutor,
        CommissionService $commissionService,
        UserService $userService,
        ?string $merchantsRefundAbility
    ) {
        $this->repository = $repository;
        $this->transactionService = $transactionService;
        $this->orderService = $orderService;
        $this->refundChecker = $refundChecker;
        $this->orderDebugTracer = $orderDebugTracer;
        $this->refundCreator = $refundCreator;
        $this->refundExecutor = $refundExecutor;
        $this->commissionService = $commissionService;
        $this->userService = $userService;
        if (\is_numeric($merchantsRefundAbility) === true
            && (int) $merchantsRefundAbility === 1
        ) {
            $this->merchantsRefundAbility = true;
        } else {
            $this->merchantsRefundAbility = false;
        }
    }

    public function createCompleteRefund(Order $order, string $message = null): Refund
    {
        $userId = $this->userService->getCurrentUserId();
        $refund = $this->refundCreator->createRefund($order, $userId, true, $message);

        $this->assertCanExecuteRefund($refund, $order);

        $this->orderDebugTracer->trace($order->getId(), 'CompleteRefundCreated');

        return $this->save($refund);
    }

    /** @param mixed[] $items ['itemId' => <Item ID>, 'quantity' => <Quantity>] */
    public function createPartialRefund(
        Order $order,
        string $message = null,
        array $items = null,
        bool $hasShipping = true,
        string $creditNoteReference = null
    ): Refund {
        $userId = $this->userService->getCurrentUserId();
        $existingRefunds = $this->repository->findPreviousRefunds($order);
        $refund = $this->refundCreator->createRefund(
            $order,
            $userId,
            false,
            $message,
            $items,
            $hasShipping,
            $creditNoteReference,
            $existingRefunds
        );

        $this->assertCanExecuteRefund($refund, $order, $existingRefunds);

        $this->orderDebugTracer->trace($order->getId(), 'PartialRefundCreated');

        return $this->save($refund);
    }

    public function executeRefund(Refund $refund, Order $order): Refund
    {
        $existingRefunds = $this->repository->findPreviousRefunds($order, $refund);

        $this->assertCanExecuteRefund($refund, $order, $existingRefunds);

        $exception = null;
        try {
            $refund = $this->refundExecutor->executeRefund($refund, $order, $existingRefunds);
        } catch (\Exception $e) {
            $exception = $e;
        } finally {
            $this->save($refund);
        }

        if ($exception instanceof \Exception) {
            throw $exception;
        }

        return $refund;
    }

    /** @throws TransactionNotFound */
    public function markRefundAsPaid(Refund $refund): Refund
    {
        $transaction = $this->transactionService->findOneByRefundId($refund->getId());
        $transaction->setTransactionReference(uniqid('offline_'));
        $this->transactionService->updateTransactionStatus($transaction, TransactionStatus::SUCCESS());
        $refund->setStatus(RefundStatus::PAID());

        return $this->save($refund);
    }

    /** @throws RefundTransactionAlreadyCreated */
    public function offlineMarkRefundAsPaid(Refund $refund): Refund
    {
        try {
            $transaction = $this->transactionService->findOneByRefundId($refund->getId());

            throw new RefundTransactionAlreadyCreated(
                'Offline mark refund as paid: transaction already exist',
                $refund,
                $transaction
            );
        } catch (TransactionNotFound $exception) {
            $transaction = $this->transactionService->createOfflineTransaction(
                $refund->getOrderId(),
                $refund->getAmount(),
                PaymentProcessorName::MANUAL(),
                ['Manual refund recover']
            );

            $transaction->setRefundId($refund->getId());
            $transaction->setType(TransactionType::REFUND());
            $this->transactionService->save($transaction);

            return $this->markRefundAsPaid($refund);
        }
    }

    public function save(Refund $refund): Refund
    {
        $refund->updateTimestamp();

        return $this->repository->save($refund);
    }

    public function get(int $id): Refund
    {
        return $this->repository->get($id);
    }

    public function getOneBy($filters): Refund
    {
        return $this->repository->getOneBy($filters);
    }

    /** @return Refund[] */
    public function getByOrderId(int $orderId): array
    {
        return $this->repository->findBy(['orderId' => $orderId]);
    }

    public function countExistingReferences(Company $company)
    {
        return $this->repository->countExistingReferences($company);
    }

    public function isManuallyRefundable(Order $order, bool $isAdmin): bool
    {
        // Only MP admin can perform manual refunds Or Vendor refund ability is active
        if ($isAdmin === false
            && $this->merchantsRefundAbility === false
        ) {
            return false;
        }

        return $this->refundChecker->isOrderRefundable($order, $this->repository->findPreviousRefunds($order));
    }

    public function assertHasValidPaymentMethod(int $orderId, string $paymentMethod = null): bool
    {
        try {
            $refundPaymentMethod = new RefundPaymentMethod($paymentMethod);
        } catch (\UnexpectedValueException $exception) {
            throw new InvalidRefundRequestException(
                __('refund_payment_method_invalid'),
                ['paymentMethod', $paymentMethod]
            );
        }

        $hasCreditCardTransaction = $this->transactionService->hasCreditCardTransaction($orderId);

        if ($hasCreditCardTransaction === false && $refundPaymentMethod->equals(RefundPaymentMethod::REFUND_CB())) {
            throw new InvalidRefundRequestException(
                __('cannot_refund_bad_payment_method'),
                ['paymentMethod', $paymentMethod]
            );
        }

        if ($hasCreditCardTransaction === true && $refundPaymentMethod->equals(RefundPaymentMethod::REFUND_OFFLINE())) {
            throw new InvalidRefundRequestException(
                __('refund_payment_method_does_not_match_transaction_type'),
                ['paymentMethod', $paymentMethod]
            );
        }

        return true;
    }

    public function assertCanRefundShipping(Order $order): bool
    {
        $existingRefunds = $this->repository->findPreviousRefunds($order);

        foreach ($existingRefunds as $refund) {
            if ($refund->hasShipping() === true) {
                throw new InvalidRefundRequestException(__('refund.refund_cannot_be_made'), []);
            }
        }

        return true;
    }

    /** @param Refund[] $existingRefunds */
    protected function assertCanExecuteRefund(Refund $refund, Order $order, array $existingRefunds = null): bool
    {
        if (\is_array($existingRefunds) === false) {
            $existingRefunds = $this->repository->findPreviousRefunds($order, $refund);
        }
        $existingTransactions = $this->transactionService->findBy([
            'orderId' => $order->getId(),
            'type' => TransactionType::REFUND(),
            'status' => TransactionStatus::SUCCESS(),
        ]);

        $check = $this->refundChecker->check($refund, $order, $existingRefunds, $existingTransactions);
        if ($check !== true) {
            throw new InvalidRefundRequestException(
                __('order_cannot_be_refunded'),
                ['refund' => $refund, 'order' => $order]
            );
        }

        return $check;
    }

    public function getVendorRefundAbility(): bool
    {
        return $this->merchantsRefundAbility;
    }

    public function getOrderTotalAmountRefunded(Order $order): Money
    {
        return $this->repository->getOrderTotalAmountRefunded($order);
    }
}
