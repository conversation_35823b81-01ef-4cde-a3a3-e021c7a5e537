<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order;

use Psr\Log\LoggerInterface;
use Wizacha\Marketplace\Basket\BasketService;
use Wizacha\Marketplace\Basket\Checkout;
use Wizacha\Marketplace\Basket\CheckoutResult;
use Wizacha\Marketplace\Entities\Declination;
use Wizacha\Marketplace\Payment\PaymentProcessorIdentifier;
use Wizacha\Marketplace\Payment\Processor\ProcessorService;
use Wizacha\Marketplace\User\User;

class CreateOrder
{
    /** @var BasketService */
    protected $basketService;

    /** @var Checkout */
    protected $checkout;

    /** @var ProcessorService */
    protected $paymentService;

    /** @var LoggerInterface */
    protected $logger;

    /** @var mixed[] */
    protected $baskets = [];

    public function __construct(
        BasketService $basketService,
        Checkout $checkout,
        ProcessorService $paymentService,
        LoggerInterface $logger
    ) {
        $this->basketService = $basketService;
        $this->checkout = $checkout;
        $this->paymentService = $paymentService;
        $this->logger = $logger;
    }

    public function createBasket(): string
    {
        $basketId = $this->basketService->generateNewBasket();
        $this->baskets[$basketId] = 0;

        return $basketId;
    }

    public function addProduct(string $basketId, Declination $declination, int $quantity): self
    {
        $this->basketService->addProductToBasket($basketId, $declination, $quantity);

        ++$this->baskets[$basketId];

        return $this;
    }

    public function countProducts(string $basketId): int
    {
        return $this->baskets[$basketId];
    }

    public function checkout(string $basketId, PaymentProcessorIdentifier $processor, User $user): CheckoutResult
    {
        return $this->checkout->checkout(
            $this->basketService->getById($basketId),
            $this->paymentService->findByProcessor($processor),
            $user->getUserId()
        );
    }
}
