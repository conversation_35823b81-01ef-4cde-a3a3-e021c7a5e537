<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Token;

use Wizacha\Marketplace\Traits\HasTimestamp;

class Token
{
    use HasTimestamp;

    /** @var int */
    protected $id;

    /** @var int */
    protected $orderId;

    /** @var string */
    protected $value;

    /** @var int|null user_id */
    protected $createdBy;

    public function __construct(int $orderId, string $value, int $userId = null)
    {
        $this
            ->setOrderId($orderId)
            ->setValue($value)
            ->setCreatedBy($userId)
            ->updateTimestamp()
        ;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getOrderId(): int
    {
        return $this->orderId;
    }

    public function getValue(): string
    {
        return $this->value;
    }

    public function getCreatedBy(): ?int
    {
        return $this->createdBy;
    }

    private function setOrderId(int $orderId): self
    {
        $this->orderId = $orderId;

        return $this;
    }

    private function setValue(string $value): self
    {
        $this->value = $value;

        return $this;
    }

    private function setCreatedBy(?int $createdBy): self
    {
        $this->createdBy = $createdBy;

        return $this;
    }
}
