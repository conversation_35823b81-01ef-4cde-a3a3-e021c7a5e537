<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Token;

use Wizacha\AppBundle\Security\User\UserService;
use Wizacha\Component\Token\TokenGeneratorInterface;
use Wizacha\Marketplace\Order\Token\Exception\CannotGenerateTokenValue;
use Wizacha\Marketplace\Order\Token\Exception\EmptyTokenException;
use Wizacha\Marketplace\Order\Token\Exception\TokenNotFoundException;

class TokenService
{
    public const MAX_GENERATE_TOKEN_LOOP = 10;

    /** @var TokenRepository */
    private $repository;

    /** @var TokenGeneratorInterface */
    private $tokenGenerator;

    /** @var UserService */
    private $userService;

    public function __construct(TokenRepository $repository, TokenGeneratorInterface $tokenGenerator, UserService $userService)
    {
        $this->repository = $repository;
        $this->tokenGenerator = $tokenGenerator;
        $this->userService = $userService;
    }

    public function createToken(int $orderId): Token
    {
        return $this->repository->save(new Token(
            $orderId,
            $this->getFreeTokenValue(),
            $this->userService->getCurrentUserId()
        ));
    }

    public function save(Token $token): Token
    {
        return $this->repository->save($token);
    }

    public function getByTokenValue(string $tokenValue): Token
    {
        if ("" === $tokenValue) {
            throw new EmptyTokenException();
        }

        $token = $this->repository->findOneBy(['value' => $tokenValue]);

        if (false === $token instanceof Token) {
            throw new TokenNotFoundException();
        }

        return $token;
    }

    public function getOrderId(string $tokenValue): int
    {
        return $this->getByTokenValue($tokenValue)->getOrderId();
    }

    public function setTokenGenerator(TokenGeneratorInterface $tokenGenerator): self
    {
        $this->tokenGenerator = $tokenGenerator;

        return $this;
    }

    private function getFreeTokenValue(): string
    {
        $nbLoop = 0;

        do {
            $tokenValue = $this->tokenGenerator->generate();
            $nbLoop++;

            if ($nbLoop > static::MAX_GENERATE_TOKEN_LOOP) {
                throw new CannotGenerateTokenValue("Max attempts to generate a free token value: $nbLoop");
            }
        } while ($this->repository->checkIfTokenExists($tokenValue));

        return $tokenValue;
    }
}
