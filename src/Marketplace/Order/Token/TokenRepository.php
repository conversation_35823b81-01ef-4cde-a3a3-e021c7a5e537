<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Token;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\AbstractQuery;

class TokenRepository extends ServiceEntityRepository
{
    public function save(Token $token): Token
    {
        $this->getEntityManager()->persist($token);
        $this->getEntityManager()->flush();

        return $token;
    }

    public function checkIfTokenExists($tokenValue): bool
    {
        $queryResult = $this
            ->createQueryBuilder('t')
            ->select('COUNT(t.id)')
            ->where('t.value = :tokenValue')
            ->setParameter(':tokenValue', $tokenValue)
            ->getQuery()
            ->getOneOrNullResult(AbstractQuery::HYDRATE_SINGLE_SCALAR)
        ;

        return \intval($queryResult) > 0 ? true : false;
    }
}
