Wizacha\Marketplace\Order\Token\Token:
    type: entity
    table: order_token
    id:
        id:
            type: integer
            generator:
                strategy: IDENTITY
    fields:
        orderId:
            type: integer
            nullable: false
        value:
            column: value
            type: string
            nullable: false
        createdBy:
            type: integer
            nullable: true
        createdAt:
            type: datetime_immutable
            nullable: false
        updatedAt:
            type: datetime_immutable
            nullable: false
