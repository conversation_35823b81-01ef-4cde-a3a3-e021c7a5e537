<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\AmountTaxesDetail;

/**
 * Manage a TaxDetailedAmount Collection
 */
class AmountsTaxesDetails
{
    public const TOTALS = 'totals';
    public const SHIPPING_COSTS = 'shipping_costs';
    public const COMMISSIONS = 'commissions';
    public const VENDOR_SHARE = 'vendor_share';

    /**
     * @var AmountTaxesDetail[]
     */
    private $items = [];

    /**
     * Return the AmountTaxDetail refered by it's name property, null if not found
     */
    public function get(string $name): ?AmountTaxesDetail
    {
        foreach ($this->items as $item) {
            if ($item->getName() === $name) {
                return $item;
            }
        }

        return null;
    }

    public function add(AmountTaxesDetail $amountTaxDetail): AmountsTaxesDetails
    {
        $this->items[] = $amountTaxDetail;

        return $this;
    }

    public function count(): int
    {
        return \count($this->items);
    }
}
