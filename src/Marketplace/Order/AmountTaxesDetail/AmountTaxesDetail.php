<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\AmountTaxesDetail;

use Wizacha\Money\Money;

class AmountTaxesDetail
{
    /** @var string */
    private $name;

    /** @var Money */
    private $excludingTaxes;

    /** @var Money */
    private $taxes;

    /** @var Money */
    private $includingTaxes;

    public function __construct(string $name, Money $excludingTaxes, Money $taxes, Money $includingTaxes)
    {
        $this->name = $name;
        $this->excludingTaxes = $excludingTaxes;
        $this->taxes = $taxes;
        $this->includingTaxes = $includingTaxes;

        $this->assertAmountsSumIsValid();
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getIncludingTaxes(): Money
    {
        return $this->includingTaxes;
    }

    public function getExcludingTaxes(): Money
    {
        return $this->excludingTaxes;
    }

    public function getTaxes(): Money
    {
        return $this->taxes;
    }

    public function exposeIncludingTaxes(): float
    {
        return $this->includingTaxes->getConvertedAmount();
    }

    public function exposeExcludingTaxes(): float
    {
        return $this->excludingTaxes->getConvertedAmount();
    }

    public function exposeTaxes(): float
    {
        return $this->taxes->getConvertedAmount();
    }

    public function expose(): array
    {
        return [
            'excluding_taxes' => $this->exposeExcludingTaxes(),
            'taxes' => $this->exposeTaxes(),
            'including_taxes' => $this->exposeIncludingTaxes(),
        ];
    }

    public function assertAmountsSumIsValid(): void
    {
        if ($this->includingTaxes->equals($this->excludingTaxes->add($this->taxes)) === false) {
            throw new \Exception("Invalid Amounts, including taxes is not equals to excluding taxes + taxes, [{$this->exposeIncludingTaxes()} !== {$this->exposeExcludingTaxes()} + {$this->exposeTaxes()} ]");
        }
    }
}
