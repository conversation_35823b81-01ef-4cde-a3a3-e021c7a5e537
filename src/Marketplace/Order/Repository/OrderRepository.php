<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Repository;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Driver\ResultStatement;
use Wizacha\Marketplace\Transaction\TransactionType;

class OrderRepository
{
    private const SQL_SELECT = [
        'o.*',
        'o.extra AS order_extra',
        'o.payment_id AS payment_method',
        'od.*',
        'od.extra AS item_extra',
        'code.data AS hand_delivery_code_data',
        'MIN(images.detailed_id) AS detailed_id',
        'p.w_supplier_ref',
        'p.max_price_adjustment',
        'p.is_subscription',
        'p.is_renewable',
    ];

    private const SQL_FROM = <<<SQL
        cscart_orders AS o
        INNER JOIN cscart_order_details AS od ON
            od.order_id = o.order_id
        INNER JOIN doctrine_order_transactions AS ot
            ON (od.order_id = ot.order_id)
        LEFT JOIN cscart_products as p ON
            od.product_id = p.product_id
        LEFT JOIN cscart_order_data as code ON
            code.order_id = o.order_id
            AND code.type = 'W'
        LEFT JOIN cscart_images_links AS images ON
            images.object_id = CAST(p.product_id AS CHAR)
            AND images.object_type = 'product'
            AND images.type = "M"
        SQL
    ;

    private const SQL_GROUP_BY = [
        'o.order_id',
        'od.item_id',
    ];

    /** @var Connection */
    private $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
    }

    private function makeQuery(
        string $sqlWhere,
        ?array $orderBy = ['o.order_id ASC, od.product_id']
    ): string {
        return \sprintf(
            <<<SQL
            SELECT %s
            FROM %s
            WHERE
                o.order_id > 0
                %s
            GROUP BY %s
            ORDER BY %s
            SQL,
            \implode(', ', static::SQL_SELECT),
            static::SQL_FROM,
            $sqlWhere,
            \implode(', ', static::SQL_GROUP_BY),
            \implode(', ', $orderBy)
        );
    }

    /** @return int[] */
    public function prepareOrdersToUpdate(): array
    {
        $getStatement = $this->connection->prepare(
            "SELECT
                order_id
            FROM
                cscart_orders
            WHERE
                is_parent_order = 'N'
                AND subtotal_discount = 0.00
                AND promotion_ids <> ''
                AND marketplace_discount_id IS NULL
            "
        );

        $updateStatement = $this->connection->prepare(
            "UPDATE
                cscart_orders
            SET
                promotions = :promotions,
                promotion_ids = ''
            WHERE
                order_id = :orderId
                AND is_parent_order = 'N'
                AND subtotal_discount = 0.00
                AND promotion_ids <> ''
                AND marketplace_discount_id IS NULL
            "
        );

        $this->connection->beginTransaction();

        $getStatement->execute();

        $orderIds = [];

        while ($row = $getStatement->fetch()) {
            $updateStatement->execute(
                [
                    'promotions' => \serialize([]),
                    'orderId' => $row['order_id'],
                ]
            );
            $orderIds[] = $row['order_id'];
        }

        return $orderIds;
    }

    /**
     * This method returns orders in a 'pending delivery' status, without having any item shipped
     *
     * @see RollbackProcessedOrdersCommand
     *
     * @return int[]
     */
    public function getProcessedOrdersToRollbackToPendingPreparation(): array
    {
        $getStatement = $this->connection->prepare(
            "SELECT
                order_id
            FROM
                cscart_orders
            WHERE
                workflow_current_step_name = 'pending-delivery'
                AND order_id NOT IN (SELECT order_id FROM cscart_shipment_items)
            "
        );

        $getStatement->execute();

        $orderIds = [];

        while ($row = $getStatement->fetch()) {
            $orderIds[] = (int) $row['order_id'];
        }

        return $orderIds;
    }

    /**
     * This method sets back orders to a 'pending-vendor-preparation-end',
     * giving the possibility to the merchant to set an invoice and a tracking number
     *
     * @param int[] $orderIds
     *
     * @see RollbackProcessedOrdersCommand
     */
    public function rollbackProcessedOrdersToPendingPreparation(array $orderIds): void
    {
        $orderIds = implode(',', $orderIds);

        $updateStatement = $this->connection->prepare(
            "UPDATE
                cscart_orders
            SET
                status = :status,
                workflow_current_module_name = :workflowModuleName,
                workflow_current_step_name = :workflowStepName,
                invoice_number_provided = :invoiceNumberProvided,
                do_not_create_invoice = :doNotCreateInvoice,
                invoice_date = :invoiceDate,
                shipping_date = :shippingDate
            WHERE
                order_id IN ($orderIds)
                AND status = :old_status
            "
        );

        $updateStatement->execute(
            [
                'status' => 'P',
                'workflowModuleName' => 'order-preparation',
                'workflowStepName' => 'pending-vendor-preparation-end',
                'invoiceNumberProvided' => false,
                'doNotCreateInvoice' => false,
                'invoiceDate' => null,
                'shippingDate' => null,
                'old_status' => 'C'
            ]
        );
    }

    public function getOrderById(int $orderId): array
    {
        $getStatement = $this->connection->prepare("SELECT * FROM cscart_orders WHERE order_id = :orderId");
        $getStatement->execute(
            [
                'orderId' => $orderId
            ]
        );

        return $getStatement->fetchAllAssociative();
    }

    public function getOrderWorkflowLastUpdate(int $orderId): ?\DateTime
    {
        $getStatement = $this->connection->prepare("SELECT workflow_last_update FROM cscart_orders WHERE order_id = :orderId");
        $getStatement->execute(
            [
                'orderId' => $orderId
            ]
        );

        $row = $getStatement->fetchOne();

        return \is_string($row) === true ? new \DateTime($row) : null;
    }

    public function getOrdersStatsGroupedByYearAndMonth(): array
    {
        $statement = $this->connection->prepare(
            "SELECT
                YEAR(FROM_UNIXTIME(timestamp)) AS year,
                MONTH(FROM_UNIXTIME(timestamp)) AS month,
                COUNT(DISTINCT cscart_orders.order_id) AS orders,
                COUNT(cscart_order_details.item_id) AS items
            FROM cscart_orders
            INNER JOIN cscart_order_details ON cscart_orders.order_id=cscart_order_details.order_id
            WHERE accepted_by_vendor=1
            GROUP BY
                YEAR(FROM_UNIXTIME(timestamp)),
                MONTH(FROM_UNIXTIME(timestamp))
            ;"
        );
        $statement->execute();

        return $statement->fetchAllAssociative();
    }

    public function getCompanyIdByOrder(int $orderId): ?int
    {
        $statement = $this->connection->prepare("SELECT company_id FROM cscart_orders WHERE order_id = :orderId;");

        $statement->execute(['orderId' => $orderId]);

        $company = $statement->fetchAssociative();

        return \is_array($company) === true && \count($company) > 0 ? (int) $company['company_id'] : null;
    }

    /**
     * @param string[] $filters
     *
     * @return ResultStatement
     */
    public function getOrdersWithCommissionByFilters(array $filters): ResultStatement
    {
        $sql = "AND o.status = :status
                AND o.is_parent_order = :isParentOrder
                AND ot.type = :type
                AND o.w_last_status_change BETWEEN :startDate AND :endDate";

        $params = [
            'status' => 'H',
            'isParentOrder' => 'N',
            'type' => TransactionType::DISPATCH_FUNDS_TRANSFER_COMMISSION()->getValue(),
            'startDate' => $filters["period_start"] . " 00:00:00",
            'endDate' => $filters['period_end'] . " 23:59:59",
        ];

        $types = [];
        if (\array_key_exists('companies', $filters) === true) {
            $sql = $sql . " AND o.company_id IN(:companyIds)";
            $params['companyIds'] = $filters['companies'];
            $types = ['companyIds' => Connection::PARAM_STR_ARRAY];
        }

        $sql = $this->makeQuery($sql);

        return $this->connection->executeQuery($sql, $params, $types);
    }

    public function getConnection(): Connection
    {
        return $this->connection;
    }
}
