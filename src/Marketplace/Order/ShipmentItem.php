<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Order;

class ShipmentItem
{
    /**
     * @var OrderItem
     */
    private $item;

    /**
     * @var int
     */
    private $amount;

    public function __construct(OrderItem $item, int $amount)
    {
        $this->item = $item;
        $this->amount = $amount;
    }

    public function getItem(): OrderItem
    {
        return $this->item;
    }

    public function getAmount(): int
    {
        return $this->amount;
    }
}
