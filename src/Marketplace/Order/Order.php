<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order;

use Rhumsaa\Uuid\Uuid;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Wizacha\C2C\Code;
use Wizacha\Component\Order\Amounts;
use Wizacha\Component\Order\TaxRate;
use Wizacha\Cscart\AppearanceType;
use Wizacha\Marketplace\Company\Company;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts;
use Wizacha\Marketplace\Order\AmountsCalculator\Service\OrderAmountsCalculator;
use Wizacha\Marketplace\Order\AmountTaxesDetail\AmountsTaxesDetails;
use Wizacha\Marketplace\Order\AmountTaxesDetail\AmountTaxesDetail;
use Wizacha\Marketplace\Order\Discount\OrderDiscountsCalculator;
use Wizacha\Marketplace\Order\Exception\LogicException;
use Wizacha\Marketplace\Order\Refund\Enum\OrderRefundStatus;
use Wizacha\Marketplace\Order\Workflow\Exception\ActionNotAllowedInCurrentModule;
use Wizacha\Marketplace\Order\Workflow\Exception\NoActionAllowedForCanceledOrder;
use Wizacha\Marketplace\Order\Workflow\Exception\NoActionAllowedForTrashedOrder;
use Wizacha\Marketplace\Order\Workflow\ModuleName;
use Wizacha\Marketplace\Order\Workflow\Progress;
use Wizacha\Marketplace\Order\Workflow\StepName;
use Wizacha\Marketplace\Order\Workflow\WorkflowName;
use Wizacha\Marketplace\Price\PriceFormatter;
use Wizacha\Marketplace\Shipping\DeliveryType;
use Wizacha\Marketplace\User\User;
use Wizacha\Marketplace\User\UserTitle;
use Wizacha\Money\Money;
use Wizacha\Order as LegacyOrder;

class Order implements OrderPricesInterface, \JsonSerializable
{
    /** @var int */
    public const DEFAULT_DELIVERY_PERIOD = 7;

    /** @var int */
    public const DEFAULT_WITHDRAWAL_PERIOD = 14;

    /** @var string */
    public const DEFAULT_TIME_ZONE = 'Europe/Paris';

    /** @var ContainerInterface */
    protected $container;

    /** @var int */
    private $id;

    /** @var Company|null */
    private $company;

    /** @var User */
    private $user;

    private Money $total;

    private Money $subtotal;

    protected Money $marketplaceDiscountTotal;

    /** @var \DateTimeImmutable */
    private $timestamp;

    private ?string $lastChangedStatusDate;

    /** @var OrderStatus */
    private $status;

    /** @var OrderItem[] */
    private $items;

    /** @var string */
    private $email;

    /** @var string */
    private $shippingName;

    /** @var int */
    private $shippingId;

    /** @var OrderAddress */
    private $shippingAddress;

    /** @var OrderAddress */
    private $billingAddress;

    /** @var Money */
    private $shippingCost;

    /** @var Money */
    private $shippingCostWithoutTax;

    /** @var string */
    private $basketId;

    /** @var string */
    private $customerComment;

    /** @var bool|null */
    private $acceptedByVendor;

    /** @var bool */
    private $confirmed;

    /** @var bool */
    private $invoiceNumberProvided;

    /** @var bool */
    private $userRedirectedToPaymentProcessor;

    /** @var Payment */
    private $payment;

    /** @var OrderShipping|null */
    private $shipping;

    /** @var OrderDelivery|null */
    private $delivery;

    /** @var bool|null */
    private $fundsDispatched;

    /** @var bool */
    private $withdrawalPeriodOver;

    /** @var bool */
    private $isGarbage;

    /** @var WorkflowName|null */
    private $workflowName;

    /** @var Progress|null */
    private $workflowProgress;

    /** @var string|null */
    private $declineReason;

    /** @var bool */
    private $refunded;

    /** @var DeliveryType|null */
    private $deliveryType;

    /**@var bool */
    private $canceled;

    /** @var Commitment|null */
    private $commitment;

    /** @var string|null */
    private $invoiceNumber;

    /** @var Amounts */
    private $amounts;

    /** @var TaxRate */
    private $shippingTaxRate;

    /** @var int */
    private $shippingTaxId;

    /** @var Money */
    protected $originalShippingCost;

    /** @var null|Code */
    private $handDeliveryCode;

    /** @var Money */
    protected $subTotalDiscount;

    /** @var \DateTimeImmutable|false|null */
    private $invoiceDate;

    /** @var AmountsTaxesDetails */
    private $amountsTaxesDetails;

    /**
     * @var LegacyOrder
     * @internal Use ::getLegacyOrder() to get it
     * @deprecated
     */
    private $legacyOrder;

    /** @var Uuid|null */
    protected $marketplaceDiscountId;

    /** @var Money */
    private $customerTotal;

    /** @var null|string */
    private $subscriptionId;

    /** @var bool */
    private $isPaid;

    /** @var bool */
    private $carriagePaid;

    /** @var OrderRefundStatus */
    protected $refundStatus;

    /** @var Money|null */
    protected $balance = null;

    /** @var string[] */
    protected $promotionIds;

    /** @var array */
    protected $promotions;

    /** @var bool|null */
    private $doNotCreateInvoiceNumber;

    /** @var int|null */
    private $parentOrderId;

    protected bool $isCustomerProfessional;
    protected string $customerCompany;
    protected string $customerLegalIdentifier;
    protected string $customerIntraEuropeanCommunityVat;
    protected string $customerJobTitle;
    protected string $customerAccountComment;
    protected string $customerExternalIdentifier;

    /**
     * @var OrderAmounts
     * An entity to store calculations about orders
     */
    protected $orderAmounts;

    protected bool $isParentOrder;

    /** @var string[] */
    protected array $extra;

    public function __construct(array $data)
    {
        $this->container = container();
        $this->id = (int) $data['order_id'];
        $this->company = $data['company'];
        $this->user = $data['user'];
        $this->total = Money::fromVariable($data['total']);
        $this->subtotal = Money::fromVariable($data['subtotal']);
        $this->marketplaceDiscountId = Uuid::isValid($data['marketplace_discount_id']) ? Uuid::fromString($data['marketplace_discount_id']) : null;
        $this->marketplaceDiscountTotal = Money::fromVariable($data['marketplace_discount_total'] ?? 0);
        $this->customerTotal = Money::fromVariable($data['customer_total'] ?? 0);
        $this->timestamp = \DateTimeImmutable::createFromFormat('U', $data['timestamp'], new \DateTimeZone(static::DEFAULT_TIME_ZONE));
        $this->lastChangedStatusDate = $data['w_last_status_change'] ?? null;
        $this->status = new OrderStatus($data['status']);
        $this->items = $data['items'];
        $this->email = $data['email'];
        $this->shippingTaxRate = $data['shipping_tax_rate'];
        $this->shippingCost = Money::fromVariable($data['shipping_cost']);
        $this->shippingCostWithoutTax = $this->shippingTaxRate->getAmountExcludingTax($this->shippingCost);
        $this->basketId = (string) $data['basket_id'];
        $this->customerComment = $data['customer_comment'];
        $this->declineReason = $data['decline_reason'];
        $this->shippingTaxId = (int) $data['shipping_tax_id'];
        $this->isParentOrder = 'Y' === $data['is_parent_order'];

        $this->isCustomerProfessional = (bool) $data['is_customer_professional'];
        $this->customerCompany = $data['customer_company'] ?? '';
        $this->customerLegalIdentifier = $data['customer_legal_identifier'] ?? '';
        $this->customerIntraEuropeanCommunityVat = $data['customer_intra_european_community_vat'] ?? '';
        $this->customerJobTitle = $data['customer_job_title'] ?? '';
        $this->customerAccountComment = $data['customer_account_comment'] ?? '';
        $this->customerExternalIdentifier = $data['customer_external_identifier'] ?? '';

        if (\is_array($data['promotions']) && \count($data['promotions']) > 0) {
            $this->promotionIds = array_keys($data['promotions']);
            $this->promotions = $data['promotions'];
        } else {
            $this->promotions = [];
        }

        $orderData = (array) @unserialize($data['data'] ?? '');
        $this->shippingName = $orderData[0]['chosen_shippings'][0]['shipping'] ?? '';
        $this->shippingId = (int) $orderData[0]['chosen_shippings'][0]['shipping_id'];
        $this->setOriginalShippingCost($orderData);

        $this->subTotalDiscount = Money::fromVariable($orderData[0]['subtotal_discount'] ?? 0);

        $this->shippingAddress = new OrderAddress([
            'label' => $data['s_label'] ?? '',
            'title' => UserTitle::isValid($data['s_title']) ? new UserTitle($data['s_title']) : UserTitle::MR(),
            'firstname' => $data['s_firstname'] ?? '',
            'lastname' => $data['s_lastname'] ?? '',
            'address' => $data['s_address'] ?? '',
            'address2' => $data['s_address_2'] ?? '',
            'city' => $data['s_city'] ?? '',
            'zipcode' => $data['s_zipcode'] ?? '',
            'country' => $data['s_country'] ?? '',
            'company' => $data['s_company'] ?? '',
            'pickupPointId' => $data['s_pickup_point_id'] ?? '',
            'phone' => $data['s_phone'] ?? null,
            'comment' => $data['s_comment'] ?? null,
        ]);

        $this->billingAddress = new OrderAddress([
            'label' => $data['b_label'] ?? '',
            'title' => UserTitle::isValid($data['b_title']) ? new UserTitle($data['b_title']) : UserTitle::MR(),
            'firstname' => $data['b_firstname'],
            'lastname' => $data['b_lastname'],
            'address' => $data['b_address'],
            'address2' => $data['b_address_2'],
            'city' => $data['b_city'],
            'zipcode' => $data['b_zipcode'],
            'country' => $data['b_country'],
            'company' => $data['b_company'],
            'phone' => $data['b_phone'] ?? null,
            'comment' => $data['b_comment'] ?? null,
        ]);

        $this->acceptedByVendor = is_numeric($data['accepted_by_vendor']) ? (bool) (int) $data['accepted_by_vendor'] : null;
        $this->invoiceNumberProvided = is_numeric($data['invoice_number_provided']) ? (bool) (int) $data['invoice_number_provided'] : null;
        $this->userRedirectedToPaymentProcessor = (bool) (int) $data['user_redirected_to_payment_processor'];
        $this->withdrawalPeriodOver = is_numeric($data['withdrawal_period_over']) ? (bool) (int) $data['withdrawal_period_over'] : null;
        $this->isGarbage = (bool) (int) $data['is_garbage'];
        $this->refunded = (bool) (int) $data['refunded'];
        $this->confirmed = (bool) $data['confirmed'];

        $this->refundStatus = \array_key_exists('refund_status', $data)
            ? new OrderRefundStatus((int) $data['refund_status'])
            : OrderRefundStatus::NOT_REFUNDED()
        ;

        if (!\is_null($data['commitment_date']) && !\is_null($data['commitment_number'])) {
            $this->commitment = new Commitment(
                new \DateTimeImmutable($data['commitment_date']),
                $data['commitment_number']
            );
        }

        $processorInformation = [];
        if ($this->container->get('security.token_storage')->getToken() instanceof TokenInterface
            && $this->container->get('security.authorization_checker')->isGranted('ROLE_ADMIN')
        ) {
            $processorInformation = $this->getLegacyOrder()->getPaymentInformation();
        }

        $this->payment = new Payment(
            (int) $data['payment_id'],
            $data['payment_type'],
            $data['payment_processor_name'],
            $this->getCommitment() ? $this->getCommitment()->getDate() : null,
            is_numeric($data['payment_deferment_authorized']) ? (bool) (int) $data['payment_deferment_authorized'] : null,
            is_numeric($data['is_paid']) ? (bool) (int) $data['is_paid'] : null,
            $processorInformation,
            $data['payment_external_reference'] ?? null,
            is_numeric($data['is_capture']) ? (bool) (int) $data['is_capture'] : null
        );

        if (\is_string($data['shipping_date'])) {
            $this->shipping = new OrderShipping(new \DateTimeImmutable($data['shipping_date']));
        }

        if (\is_string($data['delivery_date'])) {
            $this->delivery = new OrderDelivery(
                new \DateTimeImmutable($data['delivery_date']),
                is_numeric($data['delivered']) ? (bool) (int) $data['delivered'] : null
            );
        }

        $this->fundsDispatched = $data['funds_dispatched'] === null ? null : (bool) (int) $data['funds_dispatched'];
        $this->workflowName = $data['workflow_name'] ? new WorkflowName($data['workflow_name']) : null;

        if (isset($data['workflow_status'])) {
            //on reconstruit le value object ici, les 2 arguments ne sont utiles
            //que pour l'état de processing
            $this->workflowProgress = Progress::{$data['workflow_status']}(
                new ModuleName($data['workflow_current_module_name']),
                new StepName($data['workflow_current_step_name'])
            );
        }

        $this->deliveryType = $data['delivery_type'];
        $this->invoiceNumber = $data['w_invoice_number'];
        $this->invoiceDate = $data['invoice_date'] ? \DateTimeImmutable::createFromFormat('Y-m-d H:i:s', $data['invoice_date']) : null;
        $this->canceled = (bool) (int) $data['canceled'];
        $this->handDeliveryCode = isset($data['hand_delivery_code_data']) ? unserialize($data['hand_delivery_code_data'], [Code::class]) : null;

        // We get and/or calculate the corrected frozen amounts from old order
        $this->initializeOrderAmounts();

        // Taxes details Amount
        $this->amountsTaxesDetails = (new AmountsTaxesDetails())
            ->add(new AmountTaxesDetail(
                AmountsTaxesDetails::TOTALS,
                $this->getAmountExcludingTaxes(),
                $this->getTaxAmount(),
                $this->getAmountIncludingTaxes()
            ))
            ->add(new AmountTaxesDetail(
                AmountsTaxesDetails::SHIPPING_COSTS,
                $this->getShippingCostWithoutTax(),
                $this->getShippingTaxCost(),
                $this->getShippingCost()
            ));

        if ($this->hasMarketplaceDiscount() === true) {
            $this->promotionIds[] = $this->marketplaceDiscountId;
        }

        // Commission & Vendor share
        if ($this->getId() !== 0) {
            $this->getAmountsTaxesDetails()
                ->add(new AmountTaxesDetail(
                    AmountsTaxesDetails::COMMISSIONS,
                    $this->orderAmounts->getCommissionExclTaxes(),
                    $this->orderAmounts->getCommissionTaxAmount(),
                    $this->orderAmounts->getCommissionInclTaxes()
                ));

            $this->getAmountsTaxesDetails()
                ->add(new AmountTaxesDetail(
                    AmountsTaxesDetails::VENDOR_SHARE,
                    $this->orderAmounts->getVendorShareExclTaxes(),
                    $this->orderAmounts->getVendorShareTaxAmount(),
                    $this->orderAmounts->getVendorShareInclTaxes()
                ));
        }

        $this->subscriptionId = $data['subscription_id'];
        $this->isPaid = (bool) $data['is_paid'];
        $this->carriagePaid = (bool) $data['carriage_paid'];
        $this->doNotCreateInvoiceNumber = \array_key_exists('do_not_create_invoice', $data) === true
            && is_numeric($data['do_not_create_invoice'])
            ? (bool) (int) $data['do_not_create_invoice']
            : null
        ;

        $this->parentOrderId = $data['parent_order_id'] ? (int) $data['parent_order_id'] : null;
        $this->extra = \array_key_exists('extra', $data) === true
            && \is_string($data['extra']) === true
            && \strlen($data['extra']) > 0
            ? \json_decode($data['extra'], true)
            : []
        ;
    }

    public function initializeOrderAmounts()
    {
        $this->orderAmounts = null;
        foreach ($this->getItems() as $item) {
            $item->setAmountItem(null);
        }

        /** @var OrderAmountsCalculator $orderAmountsCalculator */
        $orderAmountsCalculator = container()->get('app.order.amount.calculator');
        $this->orderAmounts = $orderAmountsCalculator->calculateFromOrder($this);
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getCompanyId(): int
    {
        if (null === $this->getCompany()) {
            return 0;
        }

        return $this->getCompany()->getId();
    }

    public function getCompany(): ?Company
    {
        return $this->company;
    }

    public function getUserId(): int
    {
        return $this->user->getUserId();
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function getTotal(): Money
    {
        if (null === $this->orderAmounts) {
            return $this->total;
        }

        return $this->orderAmounts->getTotalInclTaxes();
    }

    public function setTotal(Money $total): self
    {
        $this->orderAmounts->setTotalInclTaxes($total);

        return $this;
    }

    public function getMarketplaceDiscountTotal(): Money
    {
        if (null === $this->orderAmounts) {
            return $this->marketplaceDiscountTotal;
        }

        return $this->orderAmounts->getTotalMarketplaceDiscount();
    }

    public function getCustomerTotal(): Money
    {
        if (null === $this->orderAmounts) {
            return $this->customerTotal;
        }

        return $this->orderAmounts->getCustomerTotal();
    }

    public function getSubtotal(): Money
    {
        if (null === $this->orderAmounts) {
            return $this->subtotal;
        }

        return $this->orderAmounts->getSubTotalInclTaxes();
    }

    public function getSubtotalDiscounted(): Money
    {
        return $this->getSubtotal()
            ->subtract($this->getSubTotalDiscount());
    }

    public function setSubTotal(Money $subtotal): self
    {
        $this->orderAmounts->setSubTotalInclTaxes($subtotal);

        return $this;
    }

    public function getTaxTotal(): Money
    {
        return $this->orderAmounts->getTotalTaxAmount();
    }

    public function getIssueDate(): \DateTimeImmutable
    {
        return $this->timestamp;
    }

    public function getLastChangedStatusDate(): ?string
    {
        return $this->lastChangedStatusDate;
    }

    public function hasMarketplaceDiscount(): bool
    {
        return $this->getMarketplaceDiscountTotal()->isPositive();
    }

    public function getMarketplaceDiscountId(): ?Uuid
    {
        return $this->marketplaceDiscountId;
    }

    /**
     * @deprecated use getIssueDate instead
     */
    public function getTimestamp(): \DateTimeImmutable
    {
        return $this->timestamp;
    }

    public function getStatus(): OrderStatus
    {
        return $this->status;
    }

    /**
     * @return OrderItem[]
     */
    public function getItems(): array
    {
        return $this->items ?? [];
    }

    public function getItem(int $id): OrderItem
    {
        foreach ($this->getItems() as $item) {
            if ((int) $item->getItemId() === $id) {
                return $item;
            }
        }

        throw new NotFound("Item $id not found in order " . $this->getId());
    }

    public function getShippingId(): int
    {
        return $this->shippingId;
    }

    public function getShippingName(): string
    {
        return $this->shippingName;
    }

    public function getShippingAddress(): OrderAddress
    {
        return $this->shippingAddress;
    }

    public function getBillingAddress(): OrderAddress
    {
        return $this->billingAddress;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function getShippingCost(): Money
    {
        if (null === $this->orderAmounts) {
            return $this->shippingCost;
        }

        return $this->orderAmounts->getShippingAmount()->getDiscountedTotalInclTaxes();
    }

    public function getShippingCostWithoutTax(): Money
    {
        if (null === $this->orderAmounts) {
            return $this->shippingCostWithoutTax;
        }

        return $this->orderAmounts->getShippingAmount()->getDiscountedTotalExclTaxes();
    }

    public function getShippingTaxCost(): Money
    {
        return $this->orderAmounts->getShippingAmount()->getDiscountedTotalTaxAmount();
    }

    public function getShippingTaxRate(): TaxRate
    {
        if (null !== $this->orderAmounts) {
            return new TaxRate($this->orderAmounts->getShippingAmount()->getTaxRate());
        }

        return $this->shippingTaxRate;
    }

    public function getShippingTaxId(): int
    {
        return $this->shippingTaxId;
    }

    public function getBasketId(): string
    {
        return $this->basketId;
    }

    public function getCustomerComment(): string
    {
        return $this->customerComment;
    }

    public function isAcceptedByVendor(): ?bool
    {
        return $this->acceptedByVendor;
    }

    public function isConfirmed(): bool
    {
        return $this->confirmed;
    }

    public function isRefusedByVendor(): bool
    {
        return $this->acceptedByVendor === false;
    }

    public function hasInvoiceNumber(): bool
    {
        return $this->invoiceNumberProvided === true;
    }

    public function hasAccountingDocument(): bool
    {
        return (
            true === $this->shouldShowAsInvoice()
            || false === $this->getRefundStatus()->equals(
                OrderRefundStatus::NOT_REFUNDED()
            )
        );
    }

    public function shouldInvoiceBeGeneratedElsewhere(): bool
    {
        return $this->invoiceNumberProvided === false;
    }

    public function pendingInvoice(): bool
    {
        if ($this->shouldInvoiceBeGeneratedElsewhere()) {
            return false;
        }

        if ($this->hasInvoiceNumber()) {
            return false;
        }

        return true;
    }

    public function shouldShowAsInvoice(): bool
    {
        $appearanceType = $this->getAppearanceType();

        if (true === \is_null($appearanceType)) {
            return false;
        }

        return (
            true === $appearanceType->equals(AppearanceType::INVOICE())
            && false === $this->shouldInvoiceBeGeneratedElsewhere()
            || (
                true === $appearanceType->equals(AppearanceType::ORDER_OVERVIEW())
                && true === $this->hasInvoiceNumber()
                && true === $this->hasShipping()
            )
        );
    }

    public function getAppearanceType(): ?AppearanceType
    {
        $status_settings = fn_get_status_params($this->getStatus());

        return (
            $status_settings['appearance_type']
            ? new AppearanceType(
                $status_settings['appearance_type']
            )
            : null
        );
    }

    /** Whether the invoice number has been filled or is generated elsewhere, the invoice date is populated. */
    public function getInvoiceDate(): ?\DateTimeImmutable
    {
        return $this->invoiceDate;
    }

    /**
     * Le nom de cette fonction ne correspond pas complètement au paramètre
     * qu'elle consulte, car son scope s'est élargi avec l'arrivé du paiement
     * à échance : on initie un paiement en contactant le PSP depuis un CRON,
     * ce n'est pas vraiment l'utilisateur qui est redirigé vers le PSP (ce qui
     * est le cas des autres moyens de paiement).
     */
    public function paymentInitiatedThroughPaymentProcessor(): bool
    {
        return $this->userRedirectedToPaymentProcessor;
    }

    public function getPayment(): Payment
    {
        return $this->payment;
    }

    public function hasShipping(): bool
    {
        return $this->shipping instanceof OrderShipping;
    }

    public function getShipping(): OrderShipping
    {
        return $this->shipping;
    }

    public function hasDelivery(): bool
    {
        return $this->delivery instanceof OrderDelivery;
    }

    public function getDelivery(): ?OrderDelivery
    {
        return $this->delivery;
    }

    public function isFundsDispatched(): ?bool
    {
        return $this->fundsDispatched;
    }

    public function isWorkflowSelected(): bool
    {
        return $this->workflowName instanceof WorkflowName;
    }

    public function getWorkflowName(): ?WorkflowName
    {
        return $this->workflowName;
    }

    /**
     * @internal À n'utiliser que dans \Wizacha\Marketplace\Order\Workflow\WorkflowService::saveWorkflowProgress
     */
    public function setWorkflowName(WorkflowName $name): void
    {
        if ($this->workflowName) {
            throw new \LogicException('You can\'t change the order workflow');
        }

        $this->workflowName = $name;
    }

    public function getWorkflowProgress(): Progress
    {
        return $this->workflowProgress;
    }

    /**
     * @internal : ne pas utiliser cette fonction : risque de désynchronisation
     * des information du workflow.
     */
    public function setWorkflowProgress(Progress $progress): void
    {
        $this->workflowProgress = $progress;
    }

    public function allowWorkflow(WorkflowName $name): bool
    {
        if (!$this->isWorkflowSelected()) {
            return true;
        }

        return $this->workflowName->equals($name);
    }

    public function isWithdrawalPeriodOver(): bool
    {
        return $this->withdrawalPeriodOver;
    }

    public function getDeclineReason(): ?string
    {
        return $this->declineReason;
    }

    public function deduceLegacyStatus(): OrderStatus
    {
        return WorkflowToStatusMap::deduce($this);
    }

    public function isAdjustable(): bool
    {
        return $this->getStatus()->equals(OrderStatus::STANDBY_VENDOR());
    }

    public function deduceWorkflowTranslationKey(): string
    {
        if ($this->isCanceled()) {
            return 'workflow_canceled';
        }

        return $this->getWorkflowProgress()->getTranslationKey();
    }

    /**
     * @param ModuleName[] $allowedModuleNames
     * @throws ActionNotAllowedInCurrentModule
     * @throws NoActionAllowedForTrashedOrder
     */
    public function assertIsInModules(array $allowedModuleNames): void
    {
        $currentModuleName = $this->getWorkflowProgress()->getModuleName();

        // Si le workflow n'est pas en fonctionnement,
        // aucune action agissant sur ce workflow ne peut être autorisée.
        if (!$this->getWorkflowProgress()->isProcessing()
            && !($this->getWorkflowProgress()->hasFailed() === true
            && $this->getWorkflowProgress()->getModuleName()->equals(ModuleName::FUNDS_DISPATCH()) === true)
        ) {
            throw new ActionNotAllowedInCurrentModule($currentModuleName, []);
        }

        // On empêche toute action si la commande a été mise à la poubelle
        if ($this->isGarbage()) {
            throw new NoActionAllowedForTrashedOrder();
        }

        // On empêche toute action si la commande a été annulée
        if ($this->isCanceled()) {
            throw new NoActionAllowedForCanceledOrder();
        }

        // Parcours de modules autorisés pour voir s'il correspond
        // au module en cours.
        $authorized = false;
        foreach ($allowedModuleNames as $moduleName) {
            if ($currentModuleName->equals($moduleName)) {
                $authorized = true;
                break;
            }
        }

        if (!$authorized) {
            throw new ActionNotAllowedInCurrentModule($currentModuleName, $allowedModuleNames);
        }
    }

    public function areAllShipmentsSent()
    {
        $orderInfo = $this->container
            ->get('marketplace.order.order_service')
            ->overrideLegacyOrder($this->getId(), false, true, true, true);

        return !isset($orderInfo['shipping'][0]['need_shipment']);
    }

    public function deliveryPeriodIsOver(): bool
    {
        // Si la commande n'est pas expédiée, elle ne peut pas être livrée
        if (!$this->hasShipping()) {
            return false;
        }

        // Récupération de la date d'expédition
        $shippingDate = $this->getShipping()->getShippingDate();

        // Récupération du délai de livraison
        $deliveryPeriod = $this->normalizePeriod(
            $this->container->getParameter('feature.delivery_period'),
            static::DEFAULT_DELIVERY_PERIOD
        );

        // Si moins du délai de livraison, la période n'est pas terminée
        if ($shippingDate->add(new \DateInterval('P' . $deliveryPeriod . 'D')) > new \DateTime()) {
            return false;
        }

        return true;
    }

    public function withdrawalPeriodIsOver(): bool
    {
        $withdrawalDateEnd = $this->getWithdrawalDateEnd();

        // Withdrawal period not started
        if (true === \is_null($withdrawalDateEnd)) {
            return false;
        }

        $today = new \DateTimeImmutable(
            'today',
            new \DateTimeZone(static::DEFAULT_TIME_ZONE)
        );

        return $withdrawalDateEnd <= $today;
    }

    public function getWithdrawalDateStart(): ?\DateTimeImmutable
    {
        // Là, on veut cibler des abonnements de services qui n'ont pas de livraison ni de delivery et on s'attend à ce
        // que ça marche un peu magiquement et qu'aucun autre workflow ne soit concerné #YOLO
        if ($this->getWorkflowName()->getValue() === WorkflowName::CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_WITHDRAWAL_PERIOD_FUNDS_DISPATCH()->getValue()) {
            return $this->getIssueDate();
        }

        if ($this->isHandDelivered()) {
            return $this->getShipping()->getShippingDate();
        }

        if ($this->getDelivery()) {
            return $this->getDelivery()->getDeliveryDate();
        }

        // À partir du moment où on est dans la bonne step, on doit considérer que la période de retraction a débuté.
        // Indépendamment de tout le reste (shipping, delivery, etc.)
        if ($this->getWorkflowProgress()->getStepName()->equals(StepName::PENDING_WITHDRAWAL_PERIOD_END())) {
            return new \DateTimeImmutable(
                $this->getLastChangedStatusDate()
            );
        }

        return null;
    }

    public function getWithdrawalDateEnd(): ?\DateTimeImmutable
    {
        $withdrawalDateStart = $this->getWithdrawalDateStart();

        // Periode non démarrer
        if (true === \is_null($withdrawalDateStart)) {
            return null;
        }

        // Récupération du délai de rétractation
        $withdrawalPeriod = $this->normalizePeriod(
            $this->container->getParameter('feature.withdrawal_period'),
            static::DEFAULT_WITHDRAWAL_PERIOD
        );

        // Pas de délai
        if (0 === $withdrawalPeriod) {
            return $withdrawalDateStart->sub(
                new \DateInterval('P1D')
            );
        }

        return $withdrawalDateStart->add(
            new \DateInterval('P' . $withdrawalPeriod . 'D')
        );
    }

    public static function normalizePeriod($days, int $fallback): int
    {
        return
            (
                false === \is_int($days)
                || 0 > $days
            )
            ? $fallback
            : $days
        ;
    }

    public function areStatusesDifferent(): bool
    {
        return !$this->getStatus()->equals($this->deduceLegacyStatus());
    }

    public function overrideLegacyStatus(): void
    {
        $this->status = $this->deduceLegacyStatus();
    }

    /**
     * @internal fonction de mise à jour des variables pour ne pas devoir
     * recharger l'entité depuis la DB à chaque action métier.
     */
    public function acceptByVendor(): void
    {
        $this->acceptedByVendor = true;
    }

    /**
     * @internal fonction de mise à jour des variables pour ne pas devoir
     * recharger l'entité depuis la DB à chaque action métier.
     */
    public function declareInvoiceNumberGeneratedElsewhere(): void
    {
        $this->invoiceNumberProvided = false;
    }

    /**
     * @internal fonction de mise à jour des variables pour ne pas devoir
     * recharger l'entité depuis la DB à chaque action métier.
     */
    public function declareParcelLost(\DateTimeImmutable $parcelLostDate): void
    {
        $this->delivery = new OrderDelivery($parcelLostDate, false);
    }

    /**
     * @internal fonction de mise à jour des variables pour ne pas devoir
     * recharger l'entité depuis la DB à chaque action métier.
     */
    public function dispatchFunds(): void
    {
        $this->fundsDispatched = true;
    }

    /**
     * @internal fonction de mise à jour des variables pour ne pas devoir
     * recharger l'entité depuis la DB à chaque action métier.
     */
    public function dispatchFundsFailed(): void
    {
        $this->fundsDispatched = false;
    }

    /**
     * @internal fonction de mise à jour des variables pour ne pas devoir
     * recharger l'entité depuis la DB à chaque action métier.
     */
    public function endWithdrawalPeriod(): void
    {
        $this->withdrawalPeriodOver = true;
    }

    /**
     * @internal fonction de mise à jour des variables pour ne pas devoir
     * recharger l'entité depuis la DB à chaque action métier.
     */
    public function markAsDelivered(\DateTimeImmutable $deliveryDate): void
    {
        $this->delivery = new OrderDelivery($deliveryDate, true);
    }

    /**
     * @internal fonction de mise à jour des variables pour ne pas devoir
     * recharger l'entité depuis la DB à chaque action métier.
     */
    public function markAsPaid(): void
    {
        $this->getPayment()->setPaymentCompletion(true);
        $this->isPaid = true;
    }

    /**
     * @internal fonction de mise à jour des variables pour ne pas devoir
     * recharger l'entité depuis la DB à chaque action métier.
     */
    public function markAsShipped(\DateTimeImmutable $shippingDate): void
    {
        $this->shipping = new OrderShipping($shippingDate);
    }

    /**
     * @internal fonction de mise à jour des variables pour ne pas devoir
     * recharger l'entité depuis la DB à chaque action métier.
     */
    public function markPaymentAsRefused(): void
    {
        $this->getPayment()->setPaymentCompletion(false);
    }

    /**
     * @internal fonction de mise à jour des variables pour ne pas devoir
     * recharger l'entité depuis la DB à chaque action métier.
     */
    public function markPaymentDefermentAsAuthorized(): void
    {
        $this->getPayment()->setDefermentAuthorization(true);
    }

    /**
     * @internal fonction de mise à jour des variables pour ne pas devoir
     * recharger l'entité depuis la DB à chaque action métier.
     */
    public function markPaymentDefermentAsRefused(): void
    {
        $this->getPayment()->setDefermentAuthorization(false);
    }

    public function markPaymentAuthorizationAsCaptured(): self
    {
        $this->getPayment()->setPaymentCapture(true);

        return $this;
    }

    public function markPaymentAuthorizationAsRefused(): self
    {
        $this->getPayment()->setPaymentCapture(false);

        return $this;
    }

    /**
     * @internal fonction de mise à jour des variables pour ne pas devoir
     * recharger l'entité depuis la DB à chaque action métier.
     */
    public function provideInvoiceNumber(string $invoiceNumber): void
    {
        $this->invoiceNumberProvided = true;
        $this->invoiceNumber = $invoiceNumber;
    }

    /**
     * @internal fonction de mise à jour des variables pour ne pas devoir
     * recharger l'entité depuis la DB à chaque action métier.
     */
    public function initiatePaymentThroughPaymentProcessor(): void
    {
        $this->userRedirectedToPaymentProcessor = true;
    }

    /**
     * @internal fonction de mise à jour des variables pour ne pas devoir
     * recharger l'entité depuis la DB à chaque action métier.
     */
    public function refuseByVendor(): void
    {
        $this->acceptedByVendor = false;
    }

    /**
     * @internal fonction de mise à jour des variables pour ne pas devoir
     * recharger l'entité depuis la DB à chaque action métier.
     */
    public function trash(): void
    {
        $this->isGarbage = true;
    }

    /**
     * @internal fonction de mise à jour des variables pour ne pas devoir
     * recharger l'entité depuis la DB à chaque action métier.
     */
    public function untrash(): void
    {
        $this->isGarbage = false;
    }

    public function isGarbage(): bool
    {
        return $this->isGarbage;
    }

    public function isC2C(): bool
    {
        if (null === $this->company) {
            return false;
        }
        return $this->company->isPrivateIndividual() && $this->user->isPrivateIndividual();
    }

    public function isB2C(): bool
    {
        if (null === $this->company) {
            return false;
        }
        return $this->company->isProfessional() && $this->user->isPrivateIndividual();
    }

    public function isB2B(): bool
    {
        if (null === $this->company) {
            return false;
        }
        return $this->company->isProfessional() && $this->user->isProfessional();
    }

    /**
     * B2C and B2B orders must have a withdrawal period.
     * return true if order isB2B or isB2C
     */
    public function hasWithdrawalPeriod(): bool
    {
        return $this->isB2C() || $this->isB2B() ;
    }

    public function hasDeliveryType(): bool
    {
        return $this->deliveryType instanceof DeliveryType;
    }

    public function getDeliveryType(): DeliveryType
    {
        return $this->deliveryType;
    }

    public function isHandDelivered(): bool
    {
        return !\is_null($this->deliveryType) &&
            ($this->deliveryType->equals(DeliveryType::HAND_WITH_CODE()) ||
            $this->deliveryType->equals(DeliveryType::HAND_WITHOUT_CODE()));
    }

    public function isCanceled(): bool
    {
        return $this->canceled;
    }

    public function cancel(): void
    {
        $this->canceled = true;
    }

    public function confirm(): void
    {
        $this->confirmed = true;
    }

    public function hasCommitted(): bool
    {
        return $this->commitment instanceof Commitment;
    }

    public function commitTo(\DateTimeImmutable $date, string $number): void
    {
        if ($this->hasCommitted()) {
            throw new LogicException('Already committed');
        }

        $this->commitment = new Commitment($date, $number);
    }

    public function getCommitment(): ?Commitment
    {
        return $this->commitment;
    }

    public function getInvoiceNumber(): ?string
    {
        return $this->invoiceNumber;
    }

    public function getAmountIncludingTaxes(): Money
    {
        return $this->orderAmounts->getTotalInclTaxes();
    }

    public function getAmountExcludingTaxes(): Money
    {
        return $this->orderAmounts->getTotalExclTaxes();
    }

    public function getTaxAmount(): Money
    {
        return $this->orderAmounts->getTotalTaxAmount();
    }

    public function getGreenTaxAmount(): Money
    {
        $greenTax = new Money(0);

        foreach ($this->getItems() as $orderItem) {
            $greenTax = $greenTax->add($orderItem->getGreenTaxAmount()->multiply($orderItem->getQuantity()));
        }

        return $greenTax;
    }

    public function expose(): array
    {
        return [
            'id' => $this->getId(),
            'basketId' => $this->getBasketId(),
            'companyId' => $this->getCompanyId(),
            'companyName' => $this->getCompany()->getName(),
            'total' => $this->getTotal()->getConvertedAmount(),
            'taxTotal' => $this->getTaxTotal()->getConvertedAmount(),
            'subtotal' => $this->getSubtotal()->getConvertedAmount(),
            'customerTotal' => $this->getCustomerTotal()->getConvertedAmount(),
            'marketplaceDiscountTotal' => $this->getMarketplaceDiscountTotal()->getConvertedAmount(),
            'timestamp' => $this->getTimestamp()->getTimestamp(),
            'status' => $this->getStatus()->getKey(),
            'workflow' => $this->deduceWorkflowTranslationKey(),
            'shippingName' => $this->getShippingName(),
            'customerComment' => $this->getCustomerComment(),
            'declineReason' => $this->getDeclineReason(),
            'shippingAddress' => $this->getShippingAddress()->expose(),
            'billingAddress' => $this->getBillingAddress()->expose(),
            'items' => array_map(function (OrderItem $item) {
                return $item->expose();
            }, $this->getItems()),
            'payment' => $this->getPayment()->expose(),
            'shippingCost' => $this->getShippingCost()->getConvertedAmount(),
            'discount' => $this->getSubTotalDiscount()->getConvertedAmount(),
            'subscriptionId' => $this->getSubscriptionId(),
            'isSubscriptionInitiator' => $this->isSubscriptionInitiator(),
            'isPaid' => $this->isPaid(),
            'carriagePaid' => $this->carriagePaid(),
            /**
             * Les valeurs possibles pour la colonne refund_status sont :
             * 0 - NOT REFUNDED / 1 - MANUAL REFUND / 2 - COMPLETE REFUND / 3 - PARTIAL REFUND
             * Si refund_status NOT_REFUNDED === false => La commande est remboursée
             */
            'refunded' => $this->getRefundStatus()->equals(OrderRefundStatus::NOT_REFUNDED()) === false,
            'balance' => $this->getBalanceTotal()->getConvertedAmount(),
            'isCustomerProfessional' => $this->isCustomerProfessional(),
            'customerLegalIdentifier' => $this->getCustomerLegalIdentifier(),
            'customerIntraEuropeanCommunityVat' => $this->getCustomerIntraEuropeanCommunityVat(),
            'customerCompany' => $this->getCustomerCompany(),
            'customerJobTitle' => $this->getCustomerJobTitle(),
            'customerAccountComment' => $this->getCustomerAccountComment(),
            'customerExternalIdentifier' => $this->getCustomerExternalIdentifier(),
            'extra' => $this->getOrderExtra(),
            'transaction_reference' => $this->getFullBankWireTransactionReference(),
        ];
    }

    /** @return mixed[] */
    public function jsonSerialize(): array
    {
        return $this->expose();
    }

    public function exposeForCustomInvoice(PriceFormatter $priceFormatter): array
    {
        $exposedData = $this->expose();
        $exposedData['company'] = $this->getCompany()->expose();

        $exposedData['invoiceNumber'] = $this->getInvoiceNumber();

        $exposedData['commited'] = $this->hasCommitted();
        $exposedData['commitmentDate'] = $this->hasCommitted() ? $this->getCommitment()->getDate() : null;

        $exposedData['timestamp'] = $this->getTimestamp(); // Give a real datetime rather than an unix timestamp like expose()
        $exposedData['total'] = $this->getTotal(); // Give a real money rather than a float like expose()

        $taxes = $this->getOrderAmounts()->getTaxes();
        foreach ($taxes as $tax) {
            $exposedData['taxLines'][] = [
                'rate' => new TaxRate($tax['rate']),
                'taxAmount' => $tax['total']
            ];
        }

        $exposedData['amountExcludingTaxes'] = $this->getAmountExcludingTaxes();
        $exposedData['greenTaxAmount'] = $this->getGreenTaxAmount();
        $exposedData['shippingCostWithoutTax'] = $this->getShippingCostWithoutTax();

        foreach ($this->getItems() as $k => $orderItem) {
            $exposedData['items'][$k] += [
                'amountExcludingTax' => $orderItem->getAmountExcludingTax(),
                'priceWithoutTaxes' => $orderItem->getPriceWithoutTaxes(),
                'greenTaxAmount' => $orderItem->getGreenTaxAmount(),
            ];
        }

        array_walk_recursive($exposedData, function (&$item, $key) use ($priceFormatter) {
            if ($item instanceof Money) {
                $item = $priceFormatter->formatFloat($item->getConvertedAmount());
            }
        });

        return $exposedData;
    }

    public function getHandDeliveryCode(): ?Code
    {
        return $this->handDeliveryCode;
    }

    /** @return Money */
    public function getSubTotalDiscount(): Money
    {
        if (null === $this->orderAmounts) {
            return $this->subTotalDiscount;
        }

        return $this->orderAmounts->getTotalDiscount();
    }

    public function getAmountsTaxesDetails(): AmountsTaxesDetails
    {
        return $this->amountsTaxesDetails;
    }

    public function getRefundStatus(): OrderRefundStatus
    {
        return $this->refundStatus;
    }

    public function setRefundStatus(OrderRefundStatus $refundStatus): self
    {
        $this->refundStatus = $refundStatus;

        return $this;
    }

    /**
     * @param bool $forceRefresh true if you need to be sure that the orders datas are fresh from the DB
     * @deprecated
     */
    public function getLegacyOrder(bool $forceRefresh = false): LegacyOrder
    {
        if ($forceRefresh === true) {
            $this->legacyOrder = null;
        }

        return $this->legacyOrder ?: $this->legacyOrder = new \Wizacha\Order($this->getId(), $this);
    }

    public function getSubscriptionId(): ?string
    {
        return $this->subscriptionId;
    }

    public function isSubscription(): bool
    {
        return $this->isSubscriptionSubsequent() || $this->isSubscriptionInitiator();
    }

    public function isSubscriptionSubsequent(): bool
    {
        return \is_string($this->getSubscriptionId());
    }

    public function isSubscriptionInitiator(): bool
    {
        return
            false === \is_string($this->getSubscriptionId())
            && true === $this->areItemsSubscriptions()
        ;
    }

    public function isPaid(): bool
    {
        return $this->isPaid;
    }

    public function carriagePaid(): bool
    {
        return $this->carriagePaid;
    }

    public function getBalanceTotal(bool $cacheBalance = true): Money
    {
        if (true === \is_null($this->balance)) {
            $balanceTotal = $this->container
                ->get('marketplace.transaction.transaction_service')
                ->getBalanceByOrderId($this->getId())
            ;

            if (true === $cacheBalance) {
                $this->balance = $balanceTotal;
            }

            return $balanceTotal;
        }

        return $this->balance;
    }

    public function getBalanceTotalOnlyForRefundDetail(): Money
    {
        if (true === \is_null($this->balance)) {
            $balanceTotal = $this->container
                ->get('marketplace.transaction.transaction_service')
                ->getBalanceByOrderIdOnlyForRefundDetail($this->getId())
            ;

            $this->balance = $balanceTotal;

            return $balanceTotal;
        }

        return $this->balance;
    }

    public function isDiscounted(): bool
    {
        return $this->getDiscountTotal()->isPositive() === true || $this->hasMarketplaceDiscount();
    }

    /** @return array */
    public function getPromotions(): array
    {
        return $this->promotions;
    }

    /** @return string[] */
    public function getPromotionIds(): ?array
    {
        return $this->promotionIds;
    }

    public function getOrderDiscountsCalculator(): OrderDiscountsCalculator
    {
        return $this->container->get(OrderDiscountsCalculator::class);
    }

    public function getDiscountTotal(): Money
    {
        return $this->getSubTotalDiscount();
    }

    public function getOriginalShippingCost(): Money
    {
        return $this->originalShippingCost;
    }

    public function getFullBankWireTransactionReference(): ?string
    {
        return container()->get('marketplace.transaction.transaction_service')->findBankWireTransactionReference($this->getId());
    }

    /** @param mixed[] $orderData */
    public function setOriginalShippingCost(array $orderData): self
    {
        $orderData = $orderData[0];
        $chosenShipping = $orderData['chosen_shippings'][0];

        // If user didn't paid for shipping, the original shipping cost was 0
        if ($orderData['carriage_paid'] === true) {
            $this->originalShippingCost = new Money(0);

            return $this;
        }

        // If shipping price is custom it becomes the new shipping cost
        // If price does not include tax, we return the taxes price
        if ($chosenShipping['customShippingPrice']['isCustomShippingPrice'] === true) {
            $customShippingPrice = Money::fromVariable($chosenShipping['customShippingPrice']['price'] ?? 0);
            $this->originalShippingCost = $customShippingPrice;
            if (true === \is_array($chosenShipping['taxes'])
                && reset($chosenShipping['taxes'])['price_includes_tax'] === 'N'
            ) {
                $this->originalShippingCost = $this->shippingTaxRate->getAmountIncludingTax($customShippingPrice);
            }

            return $this;
        }

        if (true === \is_array($chosenShipping['taxes'])) {
            // rate = Shipping with Taxes if price_includes_tax = Y
            // tax_price = Shipping with Taxes if price_includes_tax = N
            // otherwise tax_price = 0
            $shippingTax = reset($chosenShipping['taxes']);
            $key = $shippingTax['price_includes_tax'] === 'Y' ? 'rate' : 'taxed_price';
        } else {
            // In case of a 0% tax, the tax array is empty and we can't use the is tax included info
            $key = $chosenShipping['rate'] !== 0 ? 'rate' : 'taxed_price';
        }

        // originalShippingCost always include taxes
        $this->originalShippingCost = Money::fromVariable($chosenShipping[$key] ?? 0);

        return $this;
    }

    public function setContainer(ContainerInterface $container): self
    {
        $this->container = $container;

        return $this;
    }

    /** @return mixed[] */
    public function toArray(): array
    {
        // $this->items and $this->workflowProgress are not returned
        return [
            'orderId' => $this->id,
            'companyId' => $this->company->getId(),
            'userId' => $this->user->getUserId(),
            'total' => $this->total,
            'subtotal' => $this->subtotal,
            'marketplaceDiscountTotal' => $this->marketplaceDiscountTotal,
            'timestamp' => $this->timestamp,
            'status' => $this->status,
            'email' => $this->email,
            'shippingName' => $this->shippingName,
            'shippingId' => $this->shippingId,
            'shippingAddress' => $this->shippingAddress,
            'billingAddress' => $this->billingAddress,
            'shippingCost' => $this->shippingCost,
            'basketId' => $this->basketId,
            'customerComment' => $this->customerComment,
            'acceptedByVendor' => $this->acceptedByVendor,
            'confirmed' => $this->confirmed,
            'invoiceNumberProvided' => $this->invoiceNumberProvided,
            'userRedirectedToPaymentProcessor' => $this->userRedirectedToPaymentProcessor,
            'payment' => $this->payment,
            'shipping' => $this->shipping,
            'delivery' => $this->delivery,
            'fundsDispatched' => $this->fundsDispatched,
            'withdrawalPeriodOver' => $this->withdrawalPeriodOver,
            'isGarbage' => $this->isGarbage,
            'workflowName' => $this->workflowName,
            'declineReason' => $this->declineReason,
            'refunded' => $this->refunded,
            'deliveryType' => $this->deliveryType,
            'canceled' => $this->canceled,
            'commitment' => $this->commitment,
            'invoiceNumber' => $this->invoiceNumber,
            'amounts' => $this->amounts,
            'shippingTaxRate' => $this->shippingTaxRate,
            'shippingCostWithoutTax' => $this->shippingCostWithoutTax,
            'shippingTaxId' => $this->shippingTaxId,
            'originalShippingCost' => $this->originalShippingCost,
            'handDeliveryCode' => $this->handDeliveryCode,
            'subTotalDiscount' => $this->subTotalDiscount,
            'invoiceDate' => $this->invoiceDate,
            'amountsTaxesDetails' => $this->amountsTaxesDetails,
            'customerTotal' => $this->customerTotal,
            'subscriptionId' => $this->subscriptionId,
            'isPaid' => $this->isPaid,
            'carriagePaid' => $this->carriagePaid,
            'refundStatus' => $this->refundStatus,
            'balance' => $this->balance,
            'promotions' => $this->promotions,
        ];
    }

    protected function areItemsSubscriptions()
    {
        foreach ($this->getItems() as $item) {
            if ($item->isSubscription()) {
                return true;
            }
        }

        return false;
    }

    public function doNotCreateInvoiceNumber(): ?bool
    {
        return $this->doNotCreateInvoiceNumber;
    }

    public function hasParentOrder(): bool
    {
        return $this->parentOrderId !== null;
    }

    // Return discounted shipping cost with marketplace discount
    public function getShippingCostAfterPromotion(): Money
    {
        return $this->orderAmounts
            ->getShippingAmount()
            ->getDiscountedTotalInclTaxes()
            ->subtract($this->orderAmounts->getShippingAmount()->getMarketplaceDiscount());
    }

    public function getShippingCostDiscount(): Money
    {
        $basketDiscount = $this->orderAmounts->getShippingAmount()->getBasketDiscount();
        $marketplaceDiscount = $this->orderAmounts->getShippingAmount()->getMarketplaceDiscount();

        return $basketDiscount->add($marketplaceDiscount);
    }

    public function isCustomerProfessional(): bool
    {
        return $this->isCustomerProfessional;
    }

    public function getCustomerCompany(): string
    {
        return $this->customerCompany;
    }

    public function getCustomerLegalIdentifier(): string
    {
        return $this->customerLegalIdentifier;
    }

    public function getCustomerIntraEuropeanCommunityVat(): string
    {
        return $this->customerIntraEuropeanCommunityVat;
    }

    public function getCustomerJobTitle(): string
    {
        return $this->customerJobTitle;
    }

    public function getCustomerAccountComment(): string
    {
        return $this->customerAccountComment;
    }

    public function getCustomerExternalIdentifier(): string
    {
        return $this->customerExternalIdentifier;
    }

    /** @return string[] */
    public function getOrderExtra(): array
    {
        return $this->extra;
    }

    public function getOrderAmounts(): OrderAmounts
    {
        return $this->orderAmounts;
    }

    public function setOrderAmounts(?OrderAmounts $orderAmounts): Order
    {
        $this->orderAmounts = $orderAmounts;
        return $this;
    }

    public function isParentOrder(): bool
    {
        return $this->isParentOrder;
    }

    public function getParentOrderId(): ?int
    {
        return $this->parentOrderId;
    }

    /**
     * Get list of order_extra from $transVariable and verify if exist in extra key of the order return the correct value else return ''
     * return mixed[]
     */
    public function getExtraDataForMailNotificationVariables(string $transVariable): array
    {
        $extraDataToMailNotificationVariables = [];
        $orderExtras = $this->getOrderExtra();
        preg_match_all('/\[order_extra_(?<keys>\w+)]/', __($transVariable), $extras);

        if (\count($extras['keys']) > 0) {
            foreach ($extras['keys'] as $extra) {
                if (\array_key_exists($extra, $orderExtras) === true) {
                    $extraDataToMailNotificationVariables['[order_extra_' . $extra . ']'] = $orderExtras[$extra];
                } else {
                    $extraDataToMailNotificationVariables['[order_extra_' . $extra . ']'] = '';
                }
            }
        }

        return $extraDataToMailNotificationVariables;
    }
}
