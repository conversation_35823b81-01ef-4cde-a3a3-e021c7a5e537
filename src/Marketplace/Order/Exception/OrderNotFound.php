<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Exception;

use Symfony\Component\HttpFoundation\Response;
use Wizacha\AppBundle\Controller\Api\Error\ApiErrorResponseProvider;
use Wizacha\Marketplace\Exception\ErrorCode;
use Wizacha\AppBundle\Controller\Api\Error\ErrorResponse;
use Wizacha\Marketplace\Exception\NotFound;

class OrderNotFound extends NotFound implements ApiErrorResponseProvider
{
    /**
     * @var int
     */
    private $orderId;

    public function __construct(int $orderId)
    {
        parent::__construct('order not found', ErrorCode::ORDER_NOT_FOUND()->getValue());
        $this->orderId = $orderId;
    }

    public function toApiErrorResponse(): ErrorResponse
    {
        return new ErrorResponse(
            ErrorCode::ORDER_NOT_FOUND(),
            'order not found',
            [
                'orderId' => $this->orderId,
            ],
            Response::HTTP_NOT_FOUND
        );
    }
}
