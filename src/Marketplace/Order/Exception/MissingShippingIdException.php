<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Exception;

class MissingShippingIdException extends \Exception
{
    /** @var mixed[] */
    protected $cart;

    public function __construct(string $message, array $cart)
    {
        parent::__construct($message);

        $this->cart = $cart;
    }

    /** @return mixed[] */
    public function getCart(): array
    {
        return $this->cart;
    }
}
