<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Exception;

use Wizacha\Marketplace\Exception\ErrorCode;
use Wizacha\Marketplace\Exception\MarketplaceExceptionInterface;

class NotCancellableOrderException extends \Exception implements MarketplaceExceptionInterface
{
    protected int $orderId;

    private ErrorCode $errorCode;

    public function __construct(int $orderId, \Throwable $previous = null)
    {
        $this->orderId = $orderId;

        $this->errorCode = ErrorCode::ORDER_NOT_CANCELLABLE();
        parent::__construct(
            'Order #' . $this->orderId . ' is not cancellable',
            $this->errorCode->getValue(),
            $previous
        );
    }

    /** @inheritDoc */
    public function getContext(): array
    {
        return [
            'orderId' => $this->orderId,
        ];
    }

    public function getErrorCode(): ErrorCode
    {
        return $this->errorCode;
    }
}
