<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Exception;

/**
 * A corrupted order is an order which have corrupted data about the workflow.
 * Workflow progression have values set to null, like: workflow_name, workflow_status, workflow_current_module_name, workflow_current_step_name
 */
class OrderCorruptedException extends \Exception
{
}
