<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\OrderAttachment\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Wizacha\Marketplace\Order\OrderAttachment\Entity\OrderAttachment;
use Wizacha\Marketplace\Order\OrderAttachment\Enum\OrderAttachmentType;

class OrderAttachmentRepository extends ServiceEntityRepository
{
    public function save(OrderAttachment $orderAttachment): OrderAttachment
    {
        $orderAttachment->updateTimestamp();

        $this->getEntityManager()->persist($orderAttachment);
        $this->getEntityManager()->flush();

        return $orderAttachment;
    }

    public function delete(OrderAttachment $orderAttachment): void
    {
        $this->getEntityManager()->remove($orderAttachment);
        $this->getEntityManager()->flush();
    }

    public function findOneById(string $attachmentId, int $orderId): ?OrderAttachment
    {
        return $this->findOneBy(['id' => $attachmentId, 'orderId' => $orderId]);
    }

    /**  @return OrderAttachment[] */
    public function findAllByOrderId(
        int $orderId,
        ?OrderAttachmentType $attachmentType = null,
        ?int $limit = null,
        ?int $offset = null
    ): array {
        $parameters['orderId'] = $orderId;

        if ($attachmentType instanceof OrderAttachmentType) {
            $parameters['type'] = $attachmentType;
        }

        return $this->findBy($parameters, ['createdAt' => 'ASC'], $limit, $offset);
    }
}
