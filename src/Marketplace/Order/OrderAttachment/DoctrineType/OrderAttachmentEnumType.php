<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\OrderAttachment\DoctrineType;

use Acelaya\Doctrine\Type\AbstractPhpEnumType;
use Wizacha\Marketplace\Order\OrderAttachment\Enum\OrderAttachmentType;

class OrderAttachmentEnumType extends AbstractPhpEnumType
{
    protected $enumType = OrderAttachmentType::class;

    protected function getSpecificName(): string
    {
        return 'order_attachment';
    }
}
