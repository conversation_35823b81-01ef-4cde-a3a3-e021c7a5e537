<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\OrderAttachment\Enum;

use MyCLabs\Enum\Enum;

/**
 * @method static OrderAttachmentType CUSTOMER_INVOICE()
 * @method static OrderAttachmentType DELIVERY_BILL()
 * @method static OrderAttachmentType OTHER()
 */
class OrderAttachmentType extends Enum
{
    private const CUSTOMER_INVOICE = 'CUSTOMER_INVOICE';
    private const DELIVERY_BILL = 'DELIVERY_BILL';
    private const OTHER = 'OTHER';
}
