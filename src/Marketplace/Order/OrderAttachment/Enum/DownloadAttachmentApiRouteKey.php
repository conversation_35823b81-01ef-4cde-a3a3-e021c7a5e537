<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\OrderAttachment\Enum;

use MyCLabs\Enum\Enum;

/**
 * @method static DownloadAttachmentApiRouteKey USER_ROUTE_DOWNLOAD()
 * @method static DownloadAttachmentApiRouteKey ORGANISATION_ROUTE_DOWNLOAD()
 * @method static DownloadAttachmentApiRouteKey DEFAULT_ROUTE_DOWNLOAD()
 */
class DownloadAttachmentApiRouteKey extends Enum
{
    private const USER_ROUTE_DOWNLOAD = 'api_order_attachment_download_by_user';
    private const ORGANISATION_ROUTE_DOWNLOAD = 'api_organisations_order_attachment_download_by_user';
    private const DEFAULT_ROUTE_DOWNLOAD = 'api_order_attachment_download';
}
