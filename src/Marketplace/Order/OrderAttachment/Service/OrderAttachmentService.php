<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\OrderAttachment\Service;

use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;
use Wizacha\Marketplace\Order\Exception\OrderNotFound;
use Wizacha\Marketplace\Order\OrderAttachment\Entity\OrderAttachment;
use Wizacha\Marketplace\Order\OrderAttachment\Enum\DownloadAttachmentApiRouteKey;
use Wizacha\Marketplace\Order\OrderAttachment\Enum\OrderAttachmentType;
use Wizacha\Marketplace\Order\OrderAttachment\Repository\OrderAttachmentRepository;
use Wizacha\Marketplace\Order\OrderAttachment\Service\Validator\OrderAttachmentTypeValidatorService;
use Wizacha\Marketplace\Order\OrderAttachment\Service\Validator\OrderAttachmentValidatorService;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\User\WizaplaceUserInterface;

class OrderAttachmentService
{
    /** @var OrderAttachmentValidatorService  */
    protected $attachmentValidatorService;

    /** @var OrderAttachmentTypeValidatorService */
    protected $orderAttachmentTypeValidatorService;

    /** @var OrderAttachmentRepository  */
    protected $attachmentsRepository;

    /** @var OrderService  */
    protected $orderService;

    /** @var UploadFileService  */
    protected $fileService;

    /** @var RouterInterface */
    protected $router;

    public function __construct(
        OrderAttachmentValidatorService $attachmentValidatorService,
        OrderAttachmentTypeValidatorService $orderAttachmentTypeValidatorService,
        OrderAttachmentRepository $attachmentsRepository,
        OrderService $orderService,
        RouterInterface $router,
        UploadFileService $fileService
    ) {
        $this->attachmentValidatorService = $attachmentValidatorService;
        $this->orderAttachmentTypeValidatorService = $orderAttachmentTypeValidatorService;
        $this->attachmentsRepository = $attachmentsRepository;
        $this->orderService = $orderService;
        $this->fileService = $fileService;
        $this->router = $router;
    }

    /** @param mixed[] $orderAttachmentData */
    public function create(array $orderAttachmentData, int $orderId, WizaplaceUserInterface $user): OrderAttachment
    {
        $this->assertCanAttachFile($user, $orderId);
        $this->attachmentValidatorService->orderAttachmentValidator($orderAttachmentData, 'POST');

        $filename = $this->getFilenameFromFile($orderAttachmentData);

        $attachment = $this->attachmentsRepository->save(
            (new OrderAttachment())
                ->setName($orderAttachmentData['name'])
                ->setType(new OrderAttachmentType($orderAttachmentData['type']))
                ->setOrderId($orderId)
                ->setFilename($filename)
                ->setCreatedBy($user->getId())
                ->setUpdatedBy($user->getId())
        );

        $this->fileService->uploadFile($attachment, $orderAttachmentData);

        $attachment->setUrl($this->generateUrl($attachment, DownloadAttachmentApiRouteKey::DEFAULT_ROUTE_DOWNLOAD()));

        return $attachment;
    }

    public function get(int $orderId, string $attachmentId): OrderAttachment
    {
        $orderAttachment = $this->attachmentsRepository->findOneById($attachmentId, $orderId);

        if ($orderAttachment instanceof OrderAttachment === false) {
            throw new NotFoundHttpException('Attachment not found.');
        }

        $orderAttachment->setUrl($this->generateUrl($orderAttachment, DownloadAttachmentApiRouteKey::DEFAULT_ROUTE_DOWNLOAD()));

        return $orderAttachment;
    }

    /**
     * @param int $orderId
     * @param array $payload
     * @param string|null $api
     *
     * @return array
     */
    public function list(int $orderId, array $payload, ?string $api = null): array
    {
        $this->orderAttachmentTypeValidatorService->orderAttachmentTypeValidator($payload);

        $type = null;

        if (\array_key_exists('type', $payload) === true && \is_string($payload['type']) === true) {
            $type = new OrderAttachmentType($payload['type']);
        }

        if (\array_key_exists('limit', $payload) === false || \is_int($payload['limit']) === false) {
            $payload['limit'] = 100;
        }

        if (\array_key_exists('offset', $payload) === false || \is_int($payload['offset']) === false) {
            $payload['offset'] = 0;
        }

        $orderAttachments = $this->attachmentsRepository->findAllByOrderId(
            $orderId,
            $type,
            $payload['limit'],
            $payload['offset']
        );

        foreach ($orderAttachments as $orderAttachment) {
            $orderAttachment->setUrl($this->generateUrl($orderAttachment, DownloadAttachmentApiRouteKey::DEFAULT_ROUTE_DOWNLOAD()));
        }

        return $orderAttachments;
    }

    public function delete(int $orderId, string $attachmentId, WizaplaceUserInterface $user): void
    {
        $this->assertCanAttachFile($user, $orderId);

        $orderAttachment = $this->get($orderId, $attachmentId);

        $this->fileService->removeFile(
            $orderAttachment->getOrderId() . '/'
            . $orderAttachment->getId() . '/'
            . $orderAttachment->getFilename()
        );

        $this->attachmentsRepository->delete($orderAttachment);
    }

    public function update(
        array $orderAttachmentData,
        int $orderId,
        string $attachmentId,
        WizaplaceUserInterface $user
    ): OrderAttachment {
        $this->assertCanAttachFile($user, $orderId);
        $orderAttachment = $this->get($orderId, $attachmentId);
        $this->attachmentValidatorService->orderAttachmentValidator($orderAttachmentData, 'PATCH');

        if (\array_key_exists('type', $orderAttachmentData) === true) {
            $orderAttachment->setType(new OrderAttachmentType($orderAttachmentData['type']));
        }
        if (\array_key_exists('name', $orderAttachmentData) === true) {
            $orderAttachment->setName(
                $orderAttachmentData['name'] === ''
                    ? $orderAttachment->getName()
                    : $orderAttachmentData['name']
            );
        }
        $orderAttachment->setUpdatedBy($user->getId());

        $this->attachmentsRepository->save($orderAttachment);

        $orderAttachment->setUrl($this->generateUrl($orderAttachment, DownloadAttachmentApiRouteKey::DEFAULT_ROUTE_DOWNLOAD()));

        return $orderAttachment;
    }

    /** @return OrderAttachment[] */
    public function getAllByOrderId(
        int $orderId,
        ?OrderAttachmentType $attachmentType,
        int $limit = 100,
        int $offset = 0
    ): array {
        return $this->attachmentsRepository->findAllByOrderId($orderId, $attachmentType, $limit, $offset);
    }

    protected function getFilenameFromFile(array $orderAttachmentData): string
    {
        $filename = '';

        if (\is_string($orderAttachmentData['url']) === true) {
            $filename = \pathinfo($orderAttachmentData['url'])['basename'];
        }

        if ($orderAttachmentData['file'] instanceof UploadedFile) {
            $filename = $orderAttachmentData['file']->getClientOriginalName();
        }

        return $filename;
    }

    public function assertCanUserAccessAttachFile(WizaplaceUserInterface $user, int $orderId): bool
    {
        $order = $this->orderService->getOrder($orderId);

        if ($order->getUserId() !== $user->getId()) {
            // Throw 404 to avoid leaking information
            throw new OrderNotFound($orderId);
        }

        return true;
    }

    public function assertCanAttachFile(WizaplaceUserInterface $user, int $orderId): bool
    {
        $order = $this->orderService->getOrder($orderId);

        if ($order->getCompanyId() !== $user->getCompanyId()
            && \in_array('ROLE_ADMIN', $user->getRoles()) === false
        ) {
            // Throw 404 to avoid leaking information
            throw new OrderNotFound($orderId);
        }

        return true;
    }

    protected function generateUrl(OrderAttachment $orderAttachment, DownloadAttachmentApiRouteKey $apiRouteKey): string
    {
        return $this->router->generate(
            $apiRouteKey->getValue(),
            ['orderId' => $orderAttachment->getOrderId(), 'attachmentId' => $orderAttachment->getId()],
            UrlGeneratorInterface::ABSOLUTE_URL
        );
    }

    /**
     * @param array $orderAttachments
     * @param DownloadAttachmentApiRouteKey $apiRouteKey
     * @return array
     */
    public function summaryAttachment(array $orderAttachments, DownloadAttachmentApiRouteKey $apiRouteKey): array
    {
        $orderAttachments = \array_map(function (OrderAttachment $orderAttachment) use ($apiRouteKey): array {
            $orderAttachment->setUrl($this->generateUrl($orderAttachment, $apiRouteKey));
            $orderAttachment = $orderAttachment->summaryAttachment();
            return $orderAttachment;
        }, $orderAttachments);

        return $orderAttachments;
    }
}
