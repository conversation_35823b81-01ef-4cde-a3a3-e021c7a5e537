<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\OrderAttachment\Service;

use Wizacha\Cscart\Http;
use Wizacha\Marketplace\Order\OrderAttachment\Entity\OrderAttachment;
use Wizacha\Marketplace\Order\OrderAttachment\Exception\UploadFailException;
use Wizacha\Marketplace\Order\OrderAttachment\Repository\OrderAttachmentRepository;
use GuzzleHttp\Psr7\Response;
use Wizacha\Storage\StorageService;

class UploadFileService
{
    /** @var OrderAttachmentRepository  */
    protected $attachmentsRepository;

    protected ?StorageService $orderAttachmentsStorageService;

    public function __construct(OrderAttachmentRepository $attachmentsRepository, StorageService $orderAttachmentsStorageService)
    {
        $this->attachmentsRepository = $attachmentsRepository;
        $this->orderAttachmentsStorageService = $orderAttachmentsStorageService;
    }

    /** @param mixed[] $orderAttachmentData */
    public function uploadFile(
        OrderAttachment $orderAttachment,
        array $orderAttachmentData
    ): bool {
        $tempFile = '';
        //Get file path depending on file type
        if (\is_string($orderAttachmentData['url']) === true) {
            $tempFile = \tmpfile();

            $response = Http::get($orderAttachmentData['url']);
            if ($response instanceof Response === false) {
                throw new UploadFailException('Upload of the file failed: invalid url provided.');
            }

            fwrite($tempFile, (string) $response->getBody());
            fseek($tempFile, 0);
            $filePath = \stream_get_meta_data($tempFile)['uri'];
        } else {
            $filePath = $orderAttachmentData['file']->getRealPath();
        }

        // Store file
        $result = $this->orderAttachmentsStorageService
            ->put(
                $orderAttachment->getOrderId() . '/'
                . $orderAttachment->getId() . '/'
                . $orderAttachment->getFilename(),
                ['file' => $filePath]
            )
        ;

        if (\is_string($orderAttachmentData['url']) === true) {
            fclose($tempFile);
        }

        if ($result === false) {
            $this->attachmentsRepository->delete($orderAttachment);
            throw new UploadFailException();
        }

        return true;
    }

    public function removeFile(string $path): void
    {
        $isRemoved = $this->orderAttachmentsStorageService->delete($path);

        if ($isRemoved === false) {
            throw new UploadFailException('Upload of the file failed: cannot remove existing file.');
        }
    }
}
