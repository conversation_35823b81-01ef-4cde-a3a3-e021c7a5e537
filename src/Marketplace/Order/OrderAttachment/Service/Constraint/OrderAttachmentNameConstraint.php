<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\OrderAttachment\Service\Constraint;

use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Type;

class OrderAttachmentNameConstraint
{
    /** @return mixed[] */
    public static function getConstraints(): array
    {
        return [
            new NotBlank(),
            new Type(['type' => 'string']),
        ];
    }
}
