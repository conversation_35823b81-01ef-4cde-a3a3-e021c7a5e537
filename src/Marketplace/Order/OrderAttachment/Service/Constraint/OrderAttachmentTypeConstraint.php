<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\OrderAttachment\Service\Constraint;

use Symfony\Component\Validator\Constraints\Choice;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Optional;
use Symfony\Component\Validator\Constraints\Type;
use Wizacha\Marketplace\Order\OrderAttachment\Enum\OrderAttachmentType;

class OrderAttachmentTypeConstraint
{
    public static function getConstraints(): array
    {
        return [
            new NotBlank(),
            new Type(['type' => 'string']),
            new Choice(
                [
                    OrderAttachmentType::DELIVERY_BILL()->getValue(),
                    OrderAttachmentType::CUSTOMER_INVOICE()->getValue(),
                    OrderAttachmentType::OTHER()->getValue()
                ]
            ),
        ];
    }
}
