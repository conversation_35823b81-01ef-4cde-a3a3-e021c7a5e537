<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\OrderAttachment\Service\Validator;

use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Validator\Constraints\Collection as ConstraintCollection;
use Symfony\Component\Validator\Constraints\File;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Optional;
use Symfony\Component\Validator\Constraints\Required;
use Symfony\Component\Validator\Constraints\Type;
use Symfony\Component\Validator\Constraints\Url;
use Symfony\Component\Validator\ConstraintViolationInterface;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Symfony\Component\Validator\Validation;
use Wizacha\Marketplace\Order\OrderAttachment\Service\Constraint\OrderAttachmentNameConstraint;
use Wizacha\Marketplace\Order\OrderAttachment\Service\Constraint\OrderAttachmentTypeConstraint;

class OrderAttachmentValidatorService
{
    /** @param mixed[] $orderAttachmentFields */
    public function orderAttachmentValidator(array $orderAttachmentFields, string $method): bool
    {
        if (\count($orderAttachmentFields) === 0) {
            throw new BadRequestHttpException('Fields are empty.');
        }

        $fields = [];

        if ($method === 'POST') {
            $this->assertFileOrUrlIsSet($orderAttachmentFields);

            if ($orderAttachmentFields['file'] instanceof UploadedFile === true
                && \is_string($orderAttachmentFields['url']) === true
            ) {
                throw new BadRequestHttpException("Only one of the fields 'file' and 'url' must be set");
            }

            if (\is_string($orderAttachmentFields['url']) === true) {
                switch (\pathinfo($orderAttachmentFields['url'])['extension']) {
                    case 'jpg':
                    case 'png':
                    case 'pdf':
                        break;
                    default:
                        throw new BadRequestHttpException(
                            "Invalid file provided. Accepted types are '.png', '.jpg' and '.pdf'."
                        );
                }
            }

            $fields = $this->getPostFields();
        } else {
            $fields = $this->getPatchFields();
        }

        // Check all fields validity
        $validator = Validation::createValidator();
        $violations = $validator->validate(
            $orderAttachmentFields,
            new ConstraintCollection(['fields' => $fields])
        );

        return $this->isValid($violations);
    }

    protected function assertFileOrUrlIsSet(array $orderAttachmentFields): bool
    {
        // Check validity of both 'url and 'file' fields
        if (\is_null($orderAttachmentFields['file']) === true && \is_null($orderAttachmentFields['url']) === true) {
            throw new BadRequestHttpException("At least one of the fields 'file' and 'url' must be set");
        }

        return true;
    }

    /** @return mixed[] */
    protected function getPostFields(): array
    {
        return [
            'file' => new Optional(
                [
                    new File(
                        [
                            'binaryFormat' => true,
                            'mimeTypes' =>
                                [
                                    'image/png',
                                    'image/jpeg',
                                    'application/pdf',
                                ]
                        ]
                    )
                ]
            ),
            'url' => new Optional(
                [
                    new NotBlank(),
                    new Url(),
                    new Type(['type' => 'string']),
                ]
            ),
            'name' => new Required(
                OrderAttachmentNameConstraint::getConstraints()
            ),
            'type' => new Required(
                OrderAttachmentTypeConstraint::getConstraints()
            ),
        ];
    }

    /** @return mixed[] */
    protected function getPatchFields(): array
    {
        return [
            'name' => new Optional(
                OrderAttachmentNameConstraint::getConstraints()
            ),
            'type' => new Optional(
                OrderAttachmentTypeConstraint::getConstraints()
            ),
        ];
    }

    protected function isValid(ConstraintViolationListInterface $violations): bool
    {
        // Throw exception if any field doesn't match
        if (\count($violations) > 0) {
            throw new BadRequestHttpException(
                'OrderAttachment validation failed : ' . \json_encode(
                    array_map(
                        function (ConstraintViolationInterface $violation): string {
                            return "Incorrect value for field " . $violation->getPropertyPath() . ' : '
                                .  $violation->getMessage();
                        },
                        iterator_to_array($violations)
                    )
                )
            );
        }

        return true;
    }
}
