<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\OrderAttachment\Service\Validator;

use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Validator\Constraints\Collection as ConstraintCollection;
use Symfony\Component\Validator\Constraints\Optional;
use Symfony\Component\Validator\ConstraintViolationInterface;
use Symfony\Component\Validator\Validation;
use Wizacha\Marketplace\Order\OrderAttachment\Service\Constraint\OrderAttachmentTypeConstraint;

class OrderAttachmentTypeValidatorService
{
    public function orderAttachmentTypeValidator(array $orderAttachmentFields): bool
    {
        $validator = Validation::createValidator();
        $violations = $validator->validate(
            $orderAttachmentFields,
            new ConstraintCollection(
                [
                    'fields' => [
                        'type' => new Optional(
                            OrderAttachmentTypeConstraint::getConstraints()
                        ),
                    ],
                    'allowExtraFields' => true,
                ]
            )
        );

        // Throw exception if any field doesn't match
        if (\count($violations) > 0) {
            throw new BadRequestHttpException(
                'OrderAttachment validation failed : ' . \json_encode(
                    array_map(
                        function (ConstraintViolationInterface $violation): string {
                            return "Incorrect value for field " . $violation->getPropertyPath() . ' : '
                                .  $violation->getMessage();
                        },
                        iterator_to_array($violations)
                    )
                )
            );
        }

        return true;
    }
}
