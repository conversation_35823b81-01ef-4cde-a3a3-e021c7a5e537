<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\OrderAttachment\Entity;

use Rhumsaa\Uuid\Uuid;
use Wizacha\Marketplace\Order\OrderAttachment\Enum\OrderAttachmentType;
use Wizacha\Marketplace\Traits\HasTimestamp;

class OrderAttachment implements \JsonSerializable
{
    use HasTimestamp;

    /** @var null|string */
    protected $id;

    /** @var int */
    protected $orderId;

    /** @var string */
    protected $name;

    /** @var string */
    protected $filename;

    /** @var OrderAttachmentType */
    protected $type;

    /** @var int */
    protected $createdBy;

    /** @var int */
    protected $updatedBy;

    /** @var string */
    protected $url;

    public function __construct()
    {
        $this->updateTimestamp();
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getOrderId(): int
    {
        return $this->orderId;
    }

    public function setOrderId(int $orderId): self
    {
        $this->orderId = $orderId;

        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getFilename(): string
    {
        return $this->filename;
    }

    public function setFilename(string $filename): self
    {
        $this->filename = $filename;

        return $this;
    }

    public function getType(): OrderAttachmentType
    {
        return $this->type;
    }

    public function setType(OrderAttachmentType $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getCreatedBy(): int
    {
        return $this->createdBy;
    }

    public function setCreatedBy(int $createdBy): self
    {
        $this->createdBy = $createdBy;

        return $this;
    }

    public function getUpdatedBy(): int
    {
        return $this->updatedBy;
    }

    public function setUpdatedBy(int $updatedBy): self
    {
        $this->updatedBy = $updatedBy;

        return $this;
    }

    public function getUrl(): string
    {
        return $this->url;
    }

    public function setUrl(string $url): self
    {
        $this->url = $url;

        return $this;
    }

    public function defineGuid(): self
    {
        $this->id = Uuid::uuid4()->toString();

        return $this;
    }

    public function jsonSerialize(): array
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'filename' => $this->getFilename(),
            'createdAt' => $this->getCreatedAt()->format(\DateTime::RFC3339),
            'updatedAt' => $this->getUpdatedAt()->format(\DateTime::RFC3339),
            'url' => $this->getUrl(),
            'type' => $this->getType(),
            'createdBy' => $this->getCreatedBy(),
            'updatedBy' => $this->getUpdatedBy(),
        ];
    }

    public function summaryAttachment(): array
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'filename' => $this->getFilename(),
            'url' => $this->getUrl(),
            'type' => $this->getType(),
        ];
    }
}
