Wizacha\Marketplace\Order\OrderAttachment\Entity\OrderAttachment:
    type: entity
    table: order_attachments
    repositoryClass: Wizacha\Marketplace\Order\OrderAttachment\Repository\OrderAttachmentRepository
    id:
        id:
            type: guid
            nullable: false
    fields:
        orderId:
            type: integer
            nullable: false
        name:
            type: string
            length: 255
            nullable: false
        filename:
            type: string
            length: 255
            nullable: false
        type:
            type: php_enum_order_attachment_type
            nullable: false
        createdBy:
            type: integer
            nullable: false
        updatedBy:
            type: integer
            nullable: false
        createdAt:
            type: datetime_immutable
            nullable: false
        updatedAt:
            type: datetime_immutable
            nullable: false
    lifecycleCallbacks:
        prePersist: [defineGuid]
