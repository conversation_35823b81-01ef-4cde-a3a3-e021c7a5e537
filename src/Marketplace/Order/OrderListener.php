<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Order;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Wizacha\Marketplace\Order\Event\OrderStatusUpdated;
use Wizacha\Marketplace\Transaction\Transaction;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionStatus;
use Wizacha\Marketplace\Transaction\TransactionType;
use Wizacha\Money\Money;
use Wizacha\Order;
use Wizacha\OrderStatus;
use Wizacha\Marketplace\Order\OrderEvents;

class OrderListener implements EventSubscriberInterface
{
    private TransactionService $transactionService;

    public function __construct(TransactionService $transactionService)
    {
        $this->transactionService = $transactionService;
    }

    /** @return string[] List of subscribed events and associated methods */
    public static function getSubscribedEvents(): array
    {
        return [
            OrderEvents::UPDATED => ['onOrderUpdate', 10],
        ];
    }

    public function onOrderUpdate(OrderStatusUpdated $event): void
    {
        $order = new Order($event->getId());

        if ($event->getStatusFrom() === OrderStatus::STANDBY_BILLING && $event->getStatusTo() === OrderStatus::COMPLETED) {
            //check if payments_failed filtre is checked and get orders with failures we create offline transaction with status success
            if ($this->transactionService->hasRefusedPayment($order->getId()) === true) {
                $transaction = new Transaction(
                    $orderId = $order->getId(),
                    $type = TransactionType::OFFLINE(),
                    $status = TransactionStatus::SUCCESS(),
                    $amount = new Money($order->getTotal()->getAmount())
                );
                $transaction->setTransactionReference(uniqid('offline_'));
                $this->transactionService->save($transaction);
            }
        }
    }
}
