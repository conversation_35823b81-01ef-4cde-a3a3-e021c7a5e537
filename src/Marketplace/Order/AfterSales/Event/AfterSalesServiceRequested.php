<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\AfterSales\Event;

use Symfony\Contracts\EventDispatcher\Event;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Wizacha\Component\Notification\NotificationEvent;

class AfterSalesServiceRequested extends Event implements NotificationEvent
{
    /**
     * @var int
     */
    private $orderId;
    /**
     * @var array
     */
    private $declinations;
    /**
     * @var string
     */
    private $comment;

    /**
     * @param string[] $declinations list of declination ids
     */
    public function __construct(int $orderId, array $declinations, string $comment)
    {
        $this->orderId = $orderId;
        $this->declinations = $declinations;
        $this->comment = $comment;
    }

    public static function getDescription(): string
    {
        return 'after_sales_service_requested';
    }

    public static function buildDebugForm(FormBuilder $form)
    {
        $form->add('order', IntegerType::class);
        $form->add('comment', TextType::class);
    }

    public static function createFromForm(Form $form)
    {
        $orderId = $form->getData()['order'];
        $order = container()->get('marketplace.order.order_service')->getOrder($orderId);
        $declinations = [$order->getItems()[0]->getDeclinationId()];

        return new self($orderId, $declinations, $form->getData()['comment']);
    }

    public function getOrderId(): int
    {
        return $this->orderId;
    }

    public function getDeclinations(): array
    {
        return $this->declinations;
    }

    public function getComment(): string
    {
        return $this->comment;
    }
}
