<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\AfterSales;

use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Wizacha\Marketplace\Order\AfterSales\Event\AfterSalesServiceRequested;
use Wizacha\Marketplace\Order\AfterSales\Event\LitigationCreated;
use Wizacha\Marketplace\Order\Exception\OrderIsNotComplete;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderItem;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\User\UserService;

use function Wizacha\Marketplace\Order\is_order_status_equal_to;

/**
 * Gestion du service après-vente.
 *
 * Comme le nom l'indique, cela ne concerne que les commandes terminées. Il est possible d'effectuer 2 actions :
 *
 * - une demande de SAV : s'applique au B2C/B2B
 * - un litige : s'applique au C2C (il n'y a pas de SAV en C2C)
 */
class AfterSalesService
{
    /**
     * @var UserService
     */
    private $userService;
    /**
     * @var EventDispatcherInterface
     */
    private $eventDispatcher;

    public function __construct(UserService $userService, EventDispatcherInterface $eventDispatcher)
    {
        $this->userService = $userService;
        $this->eventDispatcher = $eventDispatcher;
    }

    /**
     * @param OrderItem[] $orderItems
     * @throws OrderIsNotComplete
     */
    public function createLitigation(Order $order, array $orderItems, string $comment)
    {
        if (!is_order_status_equal_to($order->getId(), OrderStatus::COMPLETED)) {
            throw new OrderIsNotComplete();
        }

        $declinations = array_map(function (OrderItem $item) {
            return $item->getDeclinationId();
        }, $orderItems);

        $this->eventDispatcher->dispatch(
            new LitigationCreated($order->getId(), $declinations, $comment),
            LitigationCreated::class
        );
    }

    /**
     * @param OrderItem[] $orderItems
     * @throws OrderIsNotComplete
     */
    public function createAfterSalesServiceRequest(Order $order, array $orderItems, string $comment)
    {
        if (!is_order_status_equal_to($order->getId(), OrderStatus::COMPLETED)) {
            throw new OrderIsNotComplete();
        }

        $declinations = array_map(function (OrderItem $item) {
            return $item->getDeclinationId();
        }, $orderItems);

        $this->eventDispatcher->dispatch(
            new AfterSalesServiceRequested($order->getId(), $declinations, $comment),
            AfterSalesServiceRequested::class
        );
    }
}
