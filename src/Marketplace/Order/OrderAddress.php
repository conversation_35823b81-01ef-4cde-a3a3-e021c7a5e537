<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order;

use Wizacha\Marketplace\User\UserTitle;

class OrderAddress
{
    /**  @var string|null */
    private $label;

    /**
     * @var string
     */
    private $firstname;

    /**
     * @var string
     */
    private $lastname;

    /**
     * @var string
     */
    private $address;

    /**
     * @var string
     */
    private $address2;

    /**
     * @var string
     */
    private $city;

    /**
     * @var string
     */
    private $zipcode;

    /**
     * @var string
     */
    private $country;

    /**
     * @var UserTitle
     */
    private $title;

    /**
     * @var string
     */
    private $company;

    /**
     * @var string|null
     */
    private $pickupPointId;

    /*** @var string|null */
    private $phone;

    /*** @var string|null */
    private $comment;

    public function __construct(array $data)
    {
        $this->label = (\array_key_exists('label', $data) === true) ? (string) $data['label'] : null;
        $this->title = $data['title'];
        $this->firstname = (string) $data['firstname'];
        $this->lastname = (string) $data['lastname'];
        $this->address = (string) $data['address'];
        $this->address2 = (string) $data['address2'];
        $this->city = (string) $data['city'];
        $this->zipcode = (string) $data['zipcode'];
        $this->country = (string) $data['country'];
        $this->company = (string) $data['company'];
        $this->phone = (\array_key_exists('phone', $data) === true) ? (string) $data['phone'] : null;
        $this->comment = (\array_key_exists('comment', $data) === true) ? (string) $data['comment'] : null;

        if (!empty($data['pickupPointId'])) {
            $this->pickupPointId = (string) $data['pickupPointId'];
        }
    }

    public function getTitle(): UserTitle
    {
        return $this->title;
    }

    public function getFirstname(): string
    {
        return $this->firstname;
    }

    public function getLastname(): string
    {
        return $this->lastname;
    }

    public function getAddress(): string
    {
        return $this->address;
    }

    public function getAddress2(): string
    {
        return $this->address2;
    }

    public function getCity(): string
    {
        return $this->city;
    }

    public function getZipcode(): string
    {
        return $this->zipcode;
    }

    public function getCountry(): string
    {
        return $this->country;
    }

    public function getCompany(): string
    {
        return $this->company;
    }

    public function getPickupPointId(): ?string
    {
        return $this->pickupPointId;
    }

    public function getPhone(): ?string
    {
        return $this->phone;
    }

    public function getLabel(): ?string
    {
        return $this->label;
    }

    public function getComment(): ?string
    {
        return $this->comment;
    }

    public function expose()
    {
        return [
            'label' => $this->getLabel(),
            'title' => (string) $this->getTitle(),
            'firstname' => $this->getFirstname(),
            'lastname' => $this->getLastname(),
            'address' => $this->getAddress(),
            'address2' => $this->getAddress2(),
            'city' => $this->getCity(),
            'zipcode' => $this->getZipcode(),
            'country' => $this->getCountry(),
            'company' => $this->getCompany(),
            'phone' => $this->getPhone(),
            'comment' => $this->getComment(),
            'pickupPointId' => $this->getPickupPointId(),
        ];
    }
}
