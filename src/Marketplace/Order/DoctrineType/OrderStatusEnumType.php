<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\DoctrineType;

use Acelaya\Doctrine\Type\AbstractPhpEnumType;
use Wizacha\Marketplace\Order\OrderStatus;

class OrderStatusEnumType extends AbstractPhpEnumType
{
    protected $enumType = OrderStatus::class;

    protected function getSpecificName(): string
    {
        return 'order_status_type';
    }
}
