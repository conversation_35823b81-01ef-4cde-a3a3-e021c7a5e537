<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\CreditNote;

use Wizacha\Component\Order\Amounts;
use Wizacha\Marketplace\Company\Company;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderAddress;
use Wizacha\Marketplace\Order\OrderItem;
use Wizacha\Marketplace\Order\Refund\Entity\Refund;
use Wizacha\Money\Money;

/**
 * Class CreditNote
 *
 * Note: This is *not* a Doctrine entity.
 * This is a read only representation of a credit note, built upon an existing Refund.
 */
class CreditNote
{
    /** @var Company */
    protected $company;

    /** @var Order */
    protected $order;

    /** @var Refund */
    protected $refund;

    /** @var CreditNoteItem[] */
    protected $items = [];

    /** @var Money */
    protected $orderTotalExcludingTaxes;

    /** @var Money */
    protected $orderTotalGreenTax;

    /** @var Amounts */
    protected $amounts;

    /** @var Amounts */
    protected $discountedAmounts;

    /** @var Money */
    protected $discountTotal = null;

    public function __construct(Refund $refund, Order $order)
    {
        $this->refund = $refund;
        $this->order = $order;
        $this->company = $order->getCompany();

        $this->orderTotalExcludingTaxes = new Money(0);
        $this->orderTotalGreenTax = new Money(0);

        $orderItems = [];
        foreach ($order->getItems() as $orderItem) {
            $orderItems[$orderItem->getItemId()] = $orderItem;
        }

        $this->amounts = new Amounts();
        $this->discountedAmounts = new Amounts();
        foreach ($refund->getItems() as $refundItem) {
            $orderItem = $orderItems[$refundItem->getItemId()];
            $item = new CreditNoteItem($orderItem, $refundItem->getQuantity());
            $this->items[] = $item;

            $this->amounts = $this->amounts->withLine($item->getLine());
            $this->discountedAmounts = $this->discountedAmounts->withLine($item->getDiscountedLine());

            $greenTaxAmount = $orderItem->getGreenTaxAmount()->multiply($refundItem->getQuantity());
            $this->orderTotalGreenTax = $this->orderTotalGreenTax->add($greenTaxAmount);
        }
    }

    public function getCreditNoteId(): string
    {
        return $this->refund->getCreditNoteReference() ?? (string) $this->refund->getId();
    }

    public function getOrderId(): int
    {
        return $this->order->getId();
    }

    public function getOrderInvoiceNumber(): ?string
    {
        return $this->order->getInvoiceNumber();
    }

    public function getOrderDate(): \DateTimeInterface
    {
        return $this->order->getIssueDate();
    }

    public function getRefundDate(): \DateTimeInterface
    {
        return $this->refund->getCreatedAt();
    }

    public function getCompany(): Company
    {
        return $this->company;
    }

    public function getCustomerId(): int
    {
        return $this->order->getUserId();
    }

    public function getBillingAddress(): OrderAddress
    {
        return $this->order->getBillingAddress();
    }

    public function getShippingAddress(): OrderAddress
    {
        return $this->order->getShippingAddress();
    }

    /** @return CreditNoteItem[] */
    public function getItems(): array
    {
        return $this->items;
    }

    public function getUserId(): int
    {
        return $this->order->getUserId();
    }

    public function getShipping(): string
    {
        return $this->order->getShippingName();
    }

    public function getOrderTotalExcludingTaxes(): Money
    {
        return $this->amounts->getAmountExcludingTaxes();
    }

    public function getOrderTotalIncludingTaxes(): Money
    {
        return $this->amounts->getAmountIncludingTaxes();
    }

    public function getSubtotal(): Money
    {
        return $this->amounts
            ->getAmountIncludingTaxes()
        ;
    }

    public function getOrderTotalGreenTax(): Money
    {
        return $this->orderTotalGreenTax;
    }

    public function getShippingCost(): Money
    {
        return $this->refund->getShippingAmount();
    }

    public function getShippingTaxAmount(): Money
    {
        return $this->order->getShippingTaxRate()->getTaxAmountFromIncludingTaxes($this->refund->getShippingAmount());
    }

    /** @return array [rate => TaxRate, total => Money] */
    public function getTaxes(): array
    {
        $taxes = [];
        foreach ($this->discountedAmounts->getTaxRates() as $taxRate) {
            $taxes[(string) $taxRate] = [
                'rate' => $taxRate,
                'total' => $this->discountedAmounts->getTaxRateAmount($taxRate),
            ];
        }

        if ($this->getShippingCost()->isPositive()) {
            $shippingVat = $this->order->getShippingTaxRate();
            if (\array_key_exists((string) $shippingVat, $taxes) === false) {
                $taxes[(string) $shippingVat]['rate'] = $shippingVat;
                $taxes[(string) $shippingVat]['total'] = new Money(0);
            }

            $amount = $shippingVat->applyTo($shippingVat->getAmountExcludingTax($this->getShippingCost()));
            $taxes[(string) $shippingVat]['total'] = $taxes[(string) $shippingVat]['total']->add($amount);
        }

        return array_values($taxes);
    }

    /**
     * This is the fun part. In order to retrieve the discount amount, we need to add the discount
     * part dispatched on every item of the refund + shipping discount.
     *
     * The calculation is cached, as the order and refund objects can't be set after construct.
     */
    public function getDiscountTotal(): Money
    {
        if ($this->discountTotal instanceof Money) {
            return $this->discountTotal;
        }

        if ($this->order->getDiscountTotal()->isZero() === true
            && $this->order->getMarketplaceDiscountTotal()->isZero() === true
        ) {
            return $this->order->getDiscountTotal();
        }

        /** @var OrderItem[] $orderItems */
        $orderItems = [];
        foreach ($this->order->getItems() as $orderItem) {
            $orderItems[$orderItem->getItemId()] = $orderItem;
        }

        $this->discountTotal = new Money(0);
        foreach ($this->refund->getItems() as $refundItem) {
            $discount = $orderItems[$refundItem->getItemId()]->getUnitPrice()->subtract(
                $orderItems[$refundItem->getItemId()]->getDiscountedUnitPriceForRefund()
            );
            $this->discountTotal = $this->discountTotal->add($discount->multiply($refundItem->getQuantity()));
        }

        return $this->discountTotal;
    }

    public function getTotalIncludingTaxes(): Money
    {
        return $this->refund->getAmount();
    }
}
