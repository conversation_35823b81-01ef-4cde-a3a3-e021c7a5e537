<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\CreditNote;

use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\Refund\Entity\Refund;

class CreditNoteService
{
    /** @var OrderService */
    protected $orderService;

    public function __construct(OrderService $orderService)
    {
        $this->orderService = $orderService;
    }

    public function buildCreditNote(Refund $refund, Order $order): CreditNote
    {
        return new CreditNote($refund, $order);
    }

    public function isAvailable(Order $order): bool
    {
        return $order->getInvoiceDate() instanceof \DateTimeInterface;
    }
}
