<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\CreditNote;

use Wizacha\Component\Order\Amounts;
use Wizacha\Component\Order\Line;
use Wizacha\Marketplace\Order\OrderItem;
use Wizacha\Money\Money;

/**
 * Known issue: CreditNoteItem does not support multiples taxes on a single item right now.
 *              Its implementation strongly rely on `OrderItem`, `Line` and `Amounts`,
 *              which don't support this feature either.
 */
class CreditNoteItem
{
    /** @var OrderItem */
    protected $orderItem;

    /** @var Amounts */
    protected $amounts;

    /** @var Line */
    protected $line;

    /** @var Line */
    protected $discountedLine;

    /** @var int */
    protected $quantity;

    public function __construct(OrderItem $orderItem, int $quantity)
    {
        $this->orderItem = $orderItem;
        $this->quantity = $quantity;
        $this->amounts = new Amounts();
        $this->line = new Line(
            $orderItem->getAmountItem()->getUnitPriceExclTaxes(),
            $quantity,
            $orderItem->getTaxRate()
        );
        $this->discountedLine = new Line(
            $orderItem->getAmountItem()->getDiscountedUnitPriceExclTaxes(),
            $quantity,
            $orderItem->getTaxRate()
        );
        $this->amounts = $this->amounts->withLine($this->line);
    }

    public function getItemId(): string
    {
        return $this->orderItem->getItemId();
    }

    public function getCode(): string
    {
        return $this->orderItem->getProductCode();
    }

    public function getName(): string
    {
        return $this->orderItem->getProductName();
    }

    public function getQuantity(): int
    {
        return $this->quantity;
    }

    public function getUnitPriceIncludingTaxes(): Money
    {
        return $this->orderItem->getPrice();
    }

    public function getUnitPriceExcludingTaxes(): Money
    {
        return $this->line->getUnitPrice();
    }

    public function getDiscountedUnitPrice(): Money
    {
        $money = $this->orderItem->getDiscountedUnitPriceForRefund();

        if ($this->orderItem->isTaxIncludedInPrice() === false) {
            $money = $this->orderItem->getTaxRate()->getAmountExcludingTax($money);
        }

        return $money;
    }

    public function getTaxAmount(): Money
    {
        return $this->line->getTaxAmount();
    }

    public function getGreenTax(): Money
    {
        return $this->orderItem->getGreenTaxAmount();
    }

    public function getTotal(): Money
    {
        return $this->amounts->getAmountExcludingTaxes();
    }

    public function getTotalIncludingTaxes(): Money
    {
        return $this->amounts->getAmountIncludingTaxes();
    }

    public function getLine(): Line
    {
        return $this->line;
    }

    public function getDiscountedLine(): ?Line
    {
        return $this->discountedLine;
    }
}
