<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\CreditNote;

use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Refund\Service\RefundService;

/** This is a legacy mechanism which has been migrated from existing Order returns/RMA. */
class CreditNoteReferenceGenerator
{
    /** @var RefundService */
    private $refundService;

    /** @var bool */
    private $isAutomaticBillingNumberGenerationActivated;

    public function __construct(
        RefundService $refundService,
        bool $isAutomaticBillingNumberGenerationActivated
    ) {
        $this->refundService = $refundService;
        $this->isAutomaticBillingNumberGenerationActivated = $isAutomaticBillingNumberGenerationActivated;
    }

    public function generate(Order $order): string
    {
        $company = $order->getCompany();
        $count = $this->refundService->countExistingReferences($company);

        return $company->getPrefixCreditNoteNumber() . (string) ($company->getInitialRmaNumber() + $count);
    }

    public function canGenerate(Order $order): bool
    {
        if ($this->isAutomaticBillingNumberGenerationActivated === false) {
            return false;
        }

        if ($order->getCompany()->hasAutomaticRmaNumber() === false) {
            return false;
        }

        return true;
    }
}
