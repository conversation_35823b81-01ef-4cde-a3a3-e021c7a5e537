<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\CreditNote;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\TransferException;
use Psr\Log\LoggerInterface;

class CreditNoteHelper
{
    /** @var LoggerInterface */
    protected $logger;

    /** @var Client */
    protected $httpClient;

    public function __construct(LoggerInterface $logger, Client $client)
    {
        $this->logger = $logger;
        $this->httpClient = $client;
    }

    public function buildCreditNoteFilename(int $orderId, int $refundId): string
    {
        return str_replace(' ', '_', strtolower(
            __('order') . '_' . $orderId . '_' . __('credit_note') . '_' . $refundId . '.pdf'
        ));
    }

    public function retrieveRemoteTemplate(?string $url): ?string
    {
        if ($url === null || \strlen($url) === 0) {
            return null;
        }

        try {
            $response = $this->httpClient->request('get', $url);
            $content = (string) $response->getBody();

            if (\strlen($content) === 0) {
                throw new TransferException();
            }
        } catch (TransferException $e) {
            $this->logger->warning('Could not retrieve remote twig template: ' . $url);

            return null;
        }


        return $content;
    }
}
