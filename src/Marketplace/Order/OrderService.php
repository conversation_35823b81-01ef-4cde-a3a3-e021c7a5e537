<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Statement;
use Doctrine\DBAL\Types\Type;
use Psr\Log\LoggerInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Wizacha\Component\Order\TaxRate;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Entities\Tax as TaxEntity;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Order\Action\Action;
use Wizacha\Marketplace\Order\Action\Cancel;
use Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem;
use Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts;
use Wizacha\Marketplace\Order\AmountsCalculator\Repository\OrderAmountsRepository;
use Wizacha\Marketplace\Order\Discount\OrderDiscountsCalculator;
use Wizacha\Marketplace\Order\Exception\NotCancellableOrderException;
use Wizacha\Marketplace\Order\Exception\OrderCorruptedException;
use Wizacha\Marketplace\Order\Exception\OrderNotFound;
use Wizacha\Marketplace\Order\OrderAttachment\Entity\OrderAttachment;
use Wizacha\Marketplace\Order\OrderAttachment\Service\OrderAttachmentService;
use Wizacha\Marketplace\Order\Refund\Enum\OrderRefundStatus;
use Wizacha\Marketplace\Order\Repository\OrderRepository;
use Wizacha\Marketplace\Order\Workflow\Exception\NoAdaptedWorkflow;
use Wizacha\Marketplace\Order\Workflow\ModuleName;
use Wizacha\Marketplace\Order\Workflow\Status;
use Wizacha\Marketplace\Order\Workflow\StepName;
use Wizacha\Marketplace\Order\Workflow\WorkflowService;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\PaymentService;
use Wizacha\Marketplace\Payment\Processor\TransactionProcessorInterface;
use Wizacha\Marketplace\Shipping\ShippingService;
use Wizacha\Marketplace\Tax\InternationalTaxService;
use Wizacha\Marketplace\Transaction\TransactionStatus;
use Wizacha\Marketplace\Transaction\TransactionType;
use Wizacha\Marketplace\User\User;
use Wizacha\Marketplace\User\UserAddress;
use Wizacha\Marketplace\User\UserService;
use Wizacha\Money\Money;
use Wizacha\Money\PreciseMoney;
use Wizacha\Order as LegacyOrder;
use Wizacha\OrderStatus as LegacyOrderStatus;

class OrderService
{
    private const CREDIT_CARD_DELAY = 'P1D';
    private const MAX_PRICE_ADJUSTMENT = 'max_price_adjustment';
    private const PRODUCTS = 'products';

    private const SQL_SELECT = [
        'o.*',
        'o.extra AS order_extra',
        'o.payment_id AS payment_method',
        'od.*',
        'od.extra AS item_extra',
        'code.data AS hand_delivery_code_data',
        'MIN(images.detailed_id) AS detailed_id',
        'p.w_supplier_ref',
        'p.max_price_adjustment',
        'p.is_subscription',
        'p.is_renewable',
        'category.id_path',
    ];

    private const SQL_FROM = <<<SQL
        cscart_orders AS o
        INNER JOIN cscart_order_details AS od ON
            od.order_id = o.order_id
        LEFT JOIN cscart_products_categories AS product_category ON
            product_category.product_id = od.product_id
        LEFT JOIN cscart_categories AS category ON
            category.category_id = product_category.category_id
        LEFT JOIN cscart_products as p ON
            od.product_id = p.product_id
        LEFT JOIN cscart_order_data as code ON
            code.order_id = o.order_id
            AND code.type = 'W'
        LEFT JOIN cscart_images_links AS images ON
            images.object_id = CAST(p.product_id AS CHAR)
            AND images.object_type = 'product'
            AND images.type = "M"
        SQL
    ;

    private const SQL_GROUP_BY = [
        'o.order_id',
        'od.item_id',
    ];

    private Connection $db;
    private PaymentService $paymentService;
    private WorkflowService $workflowService;
    private UserService $userService;
    private CompanyService $companyService;
    private ShippingService $shippingService;
    private LoggerInterface $logger;
    protected Cancel $cancelAction;
    private OrderDiscountsCalculator $orderDiscountsCalculator;
    private OrderRepository $orderRepository;
    private OrderAmountsRepository $orderAmountsRepository;
    private InternationalTaxService $internationalTaxService;

    /**
     * @var string
     */
    private $sqlWhereUserOrders = <<<SQL
        o.order_id > 0
        AND o.user_id = :userId
        AND o.is_parent_order = 'N'
        AND o.status != :status
SQL;

    protected bool $refundFlag;

    public function __construct(
        Connection $db,
        PaymentService $paymentService,
        WorkflowService $workflowService,
        UserService $userService,
        CompanyService $companyService,
        ShippingService $shippingService,
        LoggerInterface $logger,
        Cancel $cancelAction,
        OrderDiscountsCalculator $orderDiscountsCalculator,
        OrderRepository $orderRepository,
        OrderAmountsRepository $orderAmountsRepository,
        InternationalTaxService $internationalTaxService
    ) {
        $this->db = $db;
        $this->paymentService = $paymentService;
        $this->workflowService = $workflowService;
        $this->userService = $userService;
        $this->companyService = $companyService;
        $this->shippingService = $shippingService;
        $this->logger = $logger;
        $this->cancelAction = $cancelAction;
        $this->orderDiscountsCalculator = $orderDiscountsCalculator;
        $this->orderRepository = $orderRepository;
        $this->orderAmountsRepository = $orderAmountsRepository;
        $this->internationalTaxService = $internationalTaxService;
    }

    private function makeQuery(
        string $sqlWhere,
        ?array $orderBy = ['o.order_id ASC, od.product_id']
    ): string {
        return \sprintf(
            <<<SQL
            SELECT %s
            FROM %s
            WHERE
                o.order_id > 0
                %s
            GROUP BY %s
            ORDER BY %s
            SQL,
            \implode(', ', static::SQL_SELECT),
            static::SQL_FROM,
            $sqlWhere,
            \implode(', ', static::SQL_GROUP_BY),
            \implode(', ', $orderBy)
        );
    }

    /**
     * /!\ Doesn't support parent orders
     */
    public function getOrder(int $orderId): Order
    {
        $query = $this->makeQuery(
            <<<SQL
                AND o.order_id = :orderId
                AND o.is_parent_order = 'N'
            SQL
        );

        $rows = $this->db->fetchAll(
            $query,
            ['orderId' => $orderId]
        );

        if (empty($rows)) {
            throw new OrderNotFound($orderId);
        }

        $orders = $this->rowsToOrders($rows);

        if (\count($orders) === 0) {
            $this->logger->warning(
                'Order is corrupted: Workflow progression have values set to null.',
                ['orderId' => $orderId]
            );

            throw new OrderCorruptedException('Order ' . $orderId . ' is corrupted: Workflow progression have values set to null.');
        }

        return $orders[0];
    }

    /**
     * Can return any order, even parent order
     * Be careful: partially support parent order but some parts may not work with them
     * Example: parents have multiple shipping and no company id (id = 0) associated to them
     */
    public function getAnyOrder(int $orderId): Order
    {
        $query = $this->makeQuery(
            <<<SQL
                AND o.order_id = :orderId
            SQL
        );

        $rows = $this->db->fetchAll($query, [
            'orderId' => $orderId,
        ]);

        if (empty($rows)) {
            throw new OrderNotFound($orderId);
        }

        $orders = $this->rowsToOrders($rows);
        if (\count($orders) === 0) {
            throw new OrderNotFound($orderId);
        }
        return $orders[0];
    }

    public function isOrderAcceptedByVendor(int $orderId): string
    {
        $query =  <<<'SQL'
                SELECT
                IF(COUNT(*) > 0, 1, 0) AS isAcceptedByVendor
                FROM cscart_orders
                WHERE accepted_by_vendor = 1
                AND order_id = :orderId AND is_parent_order = 'N'
SQL;
        $rows = $this->db->fetchAll($query, ['orderId' => $orderId]);

        return $rows[0]['isAcceptedByVendor'];
    }

    public function getShipment(int $shipmentId): Shipment
    {
        $order = $this->getOrderFromShipmentId($shipmentId);
        $params['shipment_id'] = $shipmentId;
        $params['advanced_info'] = true;
        $cscartData = reset(reset(fn_get_shipments_info($params)));

        $items = $order->getItems();
        $shippedItems = [];
        foreach ($items as $item) {
            if (\in_array($item->getItemId(), array_keys($cscartData[static::PRODUCTS])) === true) {
                $shippedItems[] = new ShipmentItem($item, (int) $cscartData[static::PRODUCTS][$item->getItemId()]);
            }
        }

        if ($cscartData['delivery_date'] !== null) {
            $cscartData['delivery_date'] = new \DateTimeImmutable($cscartData['delivery_date']);
        }

        return new Shipment($shipmentId, $order, $cscartData['comments'], $shippedItems, $cscartData['tracking_number'], $cscartData['chronopost_skybill_number'], $cscartData['delivery_date']);
    }

    public function isUserHaveOrderFromCompany(int $userId, int $companyId): bool
    {
        $query = "SELECT count(order_id) FROM cscart_orders WHERE user_id = :userId AND company_id = :companyId AND is_parent_order = 'N'";

        $statement = $this->db->prepare($query);
        $statement->bindValue('userId', $userId, \PDO::PARAM_INT);
        $statement->bindValue('companyId', $companyId, \PDO::PARAM_INT);
        $statement->execute();

        return (bool) $statement->fetchColumn();
    }

    /**
     * Doesn't return parent orders and incompleted orders
     * @return Order[]
     */
    public function getUserOrders(int $userId, string $transactionReference = '', array $sorting, int $start = 0, int $limit = null): array
    {
        $sortDirection = [];
        foreach ($sorting as $sort) {
            switch ($sort->getCriteria()) {
                case 'id':
                    $sortDirection[] = 'o.order_id ' . $sort->getDirection();
                    break;
                default:
                    continue 2;
            }
        }
        $order = implode(',', $sortDirection);

        $where = <<<SQL
                ORDER BY {$order}
SQL;

        if (\is_int($limit) === true) {
            $where = $where . <<<SQL
                LIMIT {$limit}
SQL;
        }

        if ($start !== 0) {
            $where = $where . <<<SQL
                OFFSET {$start}
SQL;
        }

        $query = $this->getTransactionRefQuery(
            $transactionReference,
            \implode(', ', static::SQL_SELECT),
            static::SQL_FROM,
            $where
        );

        $query .= ' GROUP BY ' . \implode(', ', static::SQL_GROUP_BY);
        $query .= ' ORDER BY ' . $order;

        $rows = $this->db->fetchAll($query, [
            'userId' => $userId,
            'status' => OrderStatus::INCOMPLETED,
            'transRef' =>  $transactionReference,
            'transactionType' =>  TransactionType::BANK_WIRE(),
        ]);

        return $this->rowsToOrders($rows);
    }

    private function getTransactionRefQuery(
        string $transactionReference,
        string $select,
        string $from,
        string $additionalWhere = ''
    ): string {
        /**
         * Pour mangopay : la transaction est enregistrée dans 2 colonnes de la table doctrine_order_transactions
         * 1. transaction_reference
         * 2. Le libelle est enregistré dans la colonne processor_informations
         * NB: La colonne processor_informations contient des données sérialisées
         *
         * Pour lemonway : la transaction de référence est enregistrée dans une seule colonne
         * C'est la transaction_refernce
         *
         * Principe de fonctionnement du filtre
         * Récuperer la transaction de référence, et la comparer avec la valeur du filtre
         */
        $where = $this->sqlWhereUserOrders;

        $joinTransactionsTable = '';
        if ($transactionReference !== '') {
            $joinTransactionsTable .= <<<SQL
                LEFT JOIN doctrine_order_transactions OT ON
                  OT.order_id = o.order_id
SQL;

            $where .= <<<SQL
                AND OT.type = :transactionType
                AND (LOCATE (:transRef, (SELECT
                            -- Transaction de reference MANGOPAY
                            IF(processor_name = 'mangopay',
                            CONCAT(OT.transaction_label, "-" , OT.transaction_reference)
                                -- Transaction reference LEMONWAY
                                , OT.transaction_reference
                            )
                            FROM `doctrine_order_transactions` AS `dot`
                            where dot.order_id = o.order_id)
                        ) > 0)
SQL;
        }

        $where .= $additionalWhere;

        return \sprintf(
            <<<SQL
                SELECT %s
                FROM %s
                WHERE o.order_id  IN (
                    SELECT * FROM (
                            SELECT
                              o.order_id
                            FROM
                              cscart_orders AS o
                            %s
                            WHERE %s
                       ) AS orders
                    )
            SQL,
            $select,
            $from,
            $joinTransactionsTable,
            $where
        );
    }

    public function getCountUserOrders(int $userId, string $transactionReference = ''): int
    {
        $select = <<<SQL
        count(DISTINCT o.order_id)
SQL;
        $from = <<<SQL
        cscart_orders AS o
        INNER JOIN cscart_order_details AS od ON od.order_id = o.order_id
        LEFT JOIN cscart_products as p on od.product_id = p.product_id
        LEFT JOIN cscart_order_data as code ON code.order_id = o.order_id AND code.type = 'W'
        LEFT JOIN cscart_images_links AS images ON images.object_id = CAST(p.product_id AS CHAR) AND images.object_type = 'product' AND images.type = "M"
SQL;

        $query = $this->getTransactionRefQuery(
            $transactionReference,
            $select,
            $from
        );

        return (int) $this
            ->db
            ->executeQuery(
                $query,
                [
                    'userId' => $userId,
                    'status' => OrderStatus::INCOMPLETED,
                    'transRef' =>  $transactionReference,
                    'transactionType' =>  TransactionType::BANK_WIRE(),
                ]
            )
            ->fetchColumn(0);
    }

    public function userHasOrders(int $userId): bool
    {
        $query = "SELECT COUNT(order_id) as count FROM cscart_orders WHERE user_id = :userId";
        $statement = $this->db->prepare($query);
        $statement->bindValue('userId', $userId, \PDO::PARAM_INT);
        $statement->execute();

        return ((int) $statement->fetchColumn(0)) > 0;
    }

    public function companyHasOnlyIncompleteOrNoOrders(int $companyId): bool
    {
        $query = "SELECT COUNT(order_id) as count FROM cscart_orders WHERE company_id = :companyId AND status != :status";
        $statement = $this->db->prepare($query);
        $statement->bindValue('companyId', $companyId, \PDO::PARAM_INT);
        $statement->bindValue('status', OrderStatus::INCOMPLETED(), \PDO::PARAM_STR);
        $statement->execute();

        return ((int) $statement->fetchColumn(0)) > 0;
    }

    /**
     * Doesn't return parent orders and incompleted orders
     * @param LegacyOrder $order
     * @return int
     * @throws \Doctrine\DBAL\DBALException
     */
    public function getNumberOrdersForUser(LegacyOrder $order): int
    {
        $datetime = new \DateTime('NOW');
        $datetime->modify('-6 month');
        $interval = \intval($datetime->format("U"));
        $query = <<<'SQL'
        SELECT count(order_id)
        FROM cscart_orders AS o
        WHERE o.user_id = :userId
        AND parent_order_id = 0
        AND timestamp > :interval
        AND o.order_id != :orderId
SQL;

        return (int) $this->db->fetchColumn($query, [
            'userId' => $order->getId(),
            'orderId' => $order->getUserId(),
            'interval' => $interval
        ], 0);
    }

    /**
     * @param LegacyOrder $order
     * @param TransactionType $transactionType
     * @param int $numberDay
     *
     * @return int
     * @throws \Doctrine\DBAL\DBALException
     */
    public function getNumberCompleteOrdersForTransactionType(
        LegacyOrder $order,
        TransactionType $transactionType,
        int $numberDay
    ): int {
        $datetime = new \DateTime('NOW');
        $datetime->modify('-' . $numberDay . ' day');
        $interval = \intval($datetime->format("U"));
        $query = <<<SQL
            SELECT
                count(O.order_id)
            FROM
                cscart_orders as O
                LEFT JOIN doctrine_order_transactions AS T ON
                    O.order_id = T.order_id
            WHERE
                workflow_status = "completed"
                AND parent_order_id = 0
                AND O.user_id = :userId
                AND O.order_id != :orderId
                AND is_garbage != 1
                AND T.status = "SUCCESS"
                AND T.type = :transactionType
                AND timestamp > :interval
            SQL
        ;

        return (int) $this->db->fetchColumn(
            $query,
            [
                'orderId' => $order->getId(),
                'userId' => $order->getUserId(),
                'transactionType' => $transactionType->getValue(),
                'numberDay' => $numberDay,
                'interval' => $interval
            ],
            0
        );
    }

    /**
     * @param LegacyOrder $order
     * @param OrderAddress $orderAddress
     *
     * @return \DateTime|null
     * @throws \Doctrine\DBAL\DBALException
     */
    public function getDateFirstOrderForShippingAddress(LegacyOrder $order, OrderAddress $orderAddress): ?\DateTime
    {
        $query = <<<SQL
            SELECT
                MIN(O.timestamp)
            FROM
                cscart_orders as O
            WHERE
                workflow_status = "completed"
                AND parent_order_id = 0
                AND is_garbage !=1
                AND O.user_id = :userId
                AND s_address = :address
                AND s_city = :city
                AND s_country = :country
                AND s_zipcode = :zipcode
                AND O.order_id != :orderId
            SQL
        ;

        $timestamp =  $this->db->fetchColumn(
            $query,
            [
                'orderId' => $order->getId(),
                'userId' => $order->getUserId(),
                'address' => $orderAddress->getAddress(),
                'city' => $orderAddress->getCity(),
                'country' => $orderAddress->getCountry(),
                'zipcode' => $orderAddress->getZipcode(),
            ],
            0
        );

        if ($timestamp === null) {
            return null;
        }

        return \DateTime::createFromFormat('U', (string) $timestamp);
    }

    /**
     * @param LegacyOrder $order
     *
     * @return int
     * @throws OrderNotFound
     * @throws \Doctrine\DBAL\DBALException
     */
    public function getReorderIndicator(LegacyOrder $order): int
    {
        $ordersIds = $this->getChildOrdersIds($order->getId());
        $query = <<<SQL
            SELECT
                product_id,
                amount
            FROM
                cscart_order_details
            WHERE
                order_id IN (:ordersIds)
            SQL
        ;

        $types = [
            'ordersIds' => Connection::PARAM_INT_ARRAY,
        ];

        $params = [
            'ordersIds' => array_values($ordersIds),
        ];

        $rows = $this->db->fetchAll($query, $params, $types);

        $sql = <<<SQL
            SELECT
                COUNT(product_id) AS products
            FROM
                cscart_order_details AS d
                INNER JOIN cscart_orders AS O ON
                    O.order_id = d.order_id
            WHERE
                d.order_id NOT IN (:ordersIds)
                AND d.product_id = :productId
                AND d.amount = :amount
                AND O.user_id = :userId
            SQL
        ;

        $countRecommandedProduct = 0;
        foreach ($rows as $row) {
            $params['productId'] = $row['product_id'];
            $params['amount'] = $row['amount'];
            $params['userId'] = $order->getUserId();
            $count = $this->db->fetchAssoc($sql, $params, $types);
            $countRecommandedProduct = $countRecommandedProduct + $count['products'];
        }

        if ($countRecommandedProduct > 1) {
            return 2;
        } else {
            return 1;
        }
    }

    public function getChildOrdersIds(int $orderId): array
    {
        $query = <<<SQL
            SELECT order_id
            FROM cscart_orders AS o
            WHERE
                (
                    o.parent_order_id = :orderId
                    AND o.is_parent_order = 'N'
                ) OR (
                    o.order_id = :orderId
                    AND o.is_parent_order = 'N'
                )
            SQL
        ;

        $rows = $this->db->fetchAll($query, [
            'orderId' => $orderId,
        ]);

        if (empty($rows)) {
            // If we can't find the order or its sub-orders then (we assume) they don't exist
            throw new OrderNotFound($orderId);
        }

        $ordersIds = [];
        foreach ($rows as $row) {
            $ordersIds[] = $row["order_id"];
        }

        return $ordersIds;
    }

    public function getPreviousTransactionReference(int $userId): ?string
    {
        $query = <<<SQL
            SELECT
                transaction_reference,
                MAX(O.timestamp)
            FROM
                cscart_orders as O
                LEFT JOIN doctrine_order_transactions AS T ON
                    O.order_id = T.order_id
            WHERE
                workflow_status = "completed"
                AND O.user_id = :userId
                AND parent_order_id = 0
                AND is_garbage !=1
                AND T.processor_name != "hipay"
                AND T.processor_name != "none"
            SQL
        ;

        $row = $this->db->fetchAssoc(
            $query,
            [
                'userId' => $userId,
            ]
        );

        if ($row === null) {
            return null;
        }

        return $row['transaction_reference'];
    }

    public function hasProductIsDematerializedInOrdered(int $orderID): bool
    {
        $query = <<<SQL
            SELECT
                COUNT(p.product_id)
            FROM
                cscart_orders AS o
                INNER JOIN cscart_order_details AS od ON
                    od.order_id = o.order_id
                INNER JOIN cscart_products AS p ON
                    od.product_id = p.product_id
            WHERE
                p.is_edp = "Y"
                AND o.order_id = :orderId
            SQL
        ;

        $count = $this->db->fetchColumn(
            $query,
            [
                'orderId' => $orderID,
            ],
            0
        );

        return \intval($count) > 0;
    }

    /**
     * Returns true if that user has ordered that product before (ignoring parent and incomplete orders)
     *
     * @param int $userId
     * @param int $productId The id of product
     * @return bool
     */
    public function hasUserOrderedProduct(int $userId, int $productId): bool
    {
        $query = <<<SQL
            SELECT
                COUNT(*)
            FROM
                cscart_orders AS o
                INNER JOIN cscart_order_details AS od ON
                    od.order_id = o.order_id
                INNER JOIN cscart_products AS p ON
                    od.product_id = p.product_id
            WHERE
                o.user_id = :userId
                AND o.is_parent_order = 'N'
                AND o.status != :status
                AND od.product_id = :productId
            SQL
        ;

        $count = $this->db->fetchColumn(
            $query,
            [
                'userId' => $userId,
                'status' => OrderStatus::INCOMPLETED,
                'productId' => $productId,
            ],
            0
        );

        return \intval($count) > 0;
    }

    /**
     * Returns true if that user has ordered that product before (ignoring parent and incomplete orders)
     *
     * @param int $userId
     * @param string $multiVendorProductId
     * @return bool
     */
    public function hasUserOrderedMultiVendorProduct(int $userId, string $multiVendorProductId): bool
    {
        $query = <<<SQL
            SELECT
                COUNT(*)
            FROM cscart_orders AS o
                INNER JOIN cscart_order_details AS od ON
                    od.order_id = o.order_id
                INNER JOIN cscart_products AS p ON
                    od.product_id = p.product_id
                INNER JOIN doctrine_multi_vendor_product_link AS l ON
                    l.product_id=p.product_id
                INNER JOIN doctrine_multi_vendor_product as mvp ON
                    mvp.id=l.multi_vendor_product_id
            WHERE
                o.user_id = :userId
                AND o.is_parent_order = 'N'
                AND o.status != :status
                AND mvp.id = :multiVendorProductId
            SQL
        ;

        $count = $this->db->fetchColumn(
            $query,
            [
                'userId' => $userId,
                'status' => OrderStatus::INCOMPLETED,
                'multiVendorProductId' => $multiVendorProductId,
            ],
            0
        );

        return \intval($count) > 0;
    }

    /**
     * @return Order[]|\Generator
     */
    public function getOrders(bool $withIncomplete = false): \Generator
    {
        $query = $this->makeQuery(
            <<<SQL
                AND o.is_parent_order = 'N'
                AND o.status != :status
            SQL,
            [
                'o.order_id',
                'o.user_id',
                'od.product_id',
                'od.price ASC'
            ]
        );

        $result = $this->db->executeQuery(
            $query,
            [
                'status' => $withIncomplete ? '' : OrderStatus::INCOMPLETED,
            ]
        );

        $oneOrderRows = [];

        while ($row = $result->fetch(\PDO::FETCH_ASSOC)) {
            // The order ID of the row is different than the last row. We yield + reset data
            // Don't yield empty array, at the first iteration
            if ($row['order_id'] != ($oneOrderRows[0]['order_id'] ?? 0) && !empty($oneOrderRows)) {
                yield $this->rowsToOrders($oneOrderRows)[0];
                $oneOrderRows = [];
            }

            $oneOrderRows[] = $row;
        }

        if (!empty($oneOrderRows)) {
            yield $this->rowsToOrders($oneOrderRows)[0];
        }
    }

    public function getParentOrderId(int $orderId): int
    {
        $row = $this->db->fetchAssoc('SELECT o.parent_order_id FROM cscart_orders AS o WHERE o.order_id = :orderId', [
            'orderId' => $orderId,
        ]);
        if (!$row) {
            throw new OrderNotFound($orderId);
        }

        return (int) $row['parent_order_id'];
    }

    public function isParentOrder(int $orderId): bool
    {
        $row = $this->db->fetchAssoc('SELECT o.is_parent_order FROM cscart_orders AS o WHERE o.order_id = :orderId', [
            'orderId' => $orderId,
        ]);
        if (!$row) {
            throw new OrderNotFound($orderId);
        }

        return $row['is_parent_order'] === 'Y';
    }
    /**
     * @return Order[]
     */
    public function getOrdersFromParentOrder(int $parentOrderId): array
    {
        $query = $this->makeQuery(
            <<<SQL
                AND o.parent_order_id = :orderId
                AND o.is_parent_order = 'N'
            SQL
        );

        $rows = $this->db->fetchAll($query, [
            'orderId' => $parentOrderId,
        ]);

        if (empty($rows)) {
            // If we can't find sub-orders then (we assume) the parent order doesn't exist
            throw new OrderNotFound($parentOrderId);
        }

        return $this->rowsToOrders($rows);
    }

    /**
     * Return child orders if $orderId is a parent order OR the targeted order if not
     *
     * @return Order[]
     */
    public function getChildOrders(int $orderId): array
    {
        $query = $this->makeQuery(
            <<<SQL
                AND (
                    (
                        o.parent_order_id = :orderId
                        AND o.is_parent_order = 'N'
                    ) OR (
                        o.order_id = :orderId
                        AND o.is_parent_order = 'N'
                    )
                )
            SQL
        );

        $rows = $this->db->fetchAll(
            $query,
            ['orderId' => $orderId,]
        );

        if (empty($rows)) {
            // If we can't find the order or its sub-orders then (we assume) they don't exist
            throw new OrderNotFound($orderId);
        }

        return $this->rowsToOrders($rows);
    }

    /**
     * Return all child orders of an order
     *
     * @return Order[]
     */
    public function getOnlyChildOrders(int $orderId): array
    {
        $query = $this->makeQuery(
            <<<SQL
                AND (o.parent_order_id = :orderId AND o.is_parent_order = 'N')
            SQL
        );

        $rows = $this->db->fetchAll($query, [
            'orderId' => $orderId,
        ]);

        if ([] === $rows) {
            return [];
        }

        return $this->rowsToOrders($rows);
    }

    /**
     * Pour une raison encore obscure, si un client a gardé en mémoire la page de paiement du PSP et qu'il paye
     * la commande est bien marquée comme payée sur le dashboard du PSP mais comme elle est trash elle ne peut plus
     * changer de workflow.
     * Il faut donc la faire sortir du mode trash pour qu'elle puisse repasser dans les bons workflow
     *
     * @param Order[] $orders
     *
     * @throws \Doctrine\DBAL\DBALException
     */
    public function removeFromGarbage(array $orders): void
    {
        foreach ($orders as $order) {
            if ($order->isGarbage()) {
                $this->db->update('cscart_orders', ['is_garbage' => false], ['order_id' => $order->getId()]);
                $order->untrash();
            }
        }
    }

    /**
     * @return Order[]
     */
    public function getPendingDeliveryOrders(): array
    {
        return $this->getOrdersProcessingWorkflowStep(StepName::PENDING_DELIVERY());
    }

    /**
     * @return Order[]
     */
    public function getPendingWithdrawalPeriodEndOrders(): array
    {
        return $this->getOrdersProcessingWorkflowStep(StepName::PENDING_WITHDRAWAL_PERIOD_END());
    }

    public function getWorkflowStatuses(): array
    {
        $states = [
            'workflow_canceled' => __('workflow_canceled'),
            'workflow_refunded' => __('workflow_refunded'),
        ];

        $query = <<<'SQL'
            SELECT
                o.workflow_current_module_name as module,
                o.workflow_current_step_name as step,
                o.workflow_status AS status
            FROM
                cscart_orders o
            WHERE
                o.canceled = 0
                AND o.refunded = 0
                AND o.status != 'N'
            GROUP BY
                o.workflow_current_module_name,
                o.workflow_current_step_name,
                o.workflow_status
            SQL
        ;

        $rows = $this->db->fetchAll($query);

        foreach ($rows as $row) {
            if (!empty($row['module']) && !empty($row['step']) && !empty($row['status'])) {
                $key = "workflow_{$row['module']}_{$row['step']}_{$row['status']}";
                $states[$key] = __(str_replace('-', '_', $key));
            }
        }

        asort($states);

        return $states;
    }

    /**
     * @return Order[]
     */
    public function getPendingDispatchFundsOrders(): array
    {
        return \array_filter(
            $this->getOrdersProcessingWorkflowStep(
                StepName::PENDING_FUNDS_DISPATCH()
            ),
            function (Order $order): bool {
                return false === OrderStatus::COMPLETED()->equals(
                    $order->getStatus()
                );
            }
        );
    }

    /**
     * @return Order[]
     */
    public function getPendingOrderToBeCanceled(): array
    {
        $payments = $this->paymentService->getPaymentWithAcceptationDelay();

        if (\count($payments) === 0) {
            return [];
        }

        return array_filter(
            $this->getOrdersProcessingWorkflowStep(StepName::PENDING_VENDOR_VALIDATION()),
            function (Order $order) use ($payments): bool {
                if (\array_key_exists($order->getPayment()->getId(), $payments)) {
                    $nbDays = $payments[$order->getPayment()->getId()];

                    return (new \DateTime())->modify("-$nbDays day") > $order->getTimestamp();
                }

                return false;
            }
        );
    }

    /**
     * Récupération des commandes en attente de paiement à échéance, dont il faut
     * lancer le paiement (à l'aide de l'appel au PSP).
     *
     * @return \Generator
     */
    public function getPaymentDefermentOrdersToBePaid(PaymentProcessorName $paymentProcessorName = null): \Generator
    {
        $currentDateTime = new \DateTime();
        // Récupération des commandes en attente de redirection vers le PSP
        $orders = $this->getOrdersProcessingWorkflowStepGenerator(
            StepName::PENDING_REDIRECTION_TO_PAYMENT_PROCESSOR(),
            ModuleName::WAIT_PAYMENT_DEFERMENT()
        );

        foreach ($orders as $order) {
            // Filter by PaymentProcessorName
            if ($paymentProcessorName instanceof PaymentProcessorName
                && !$order->getPayment()->getProcessorName()->equals($paymentProcessorName)
            ) {
                continue;
            }
            // Filtrage sur la date d'engagement qui est la date à laquelle le
            // paiement doit être initié.
            if ($order->getCommitment()->getDate() <= $currentDateTime
                && !$order->isCanceled()
                && fn_get_order_info($order->getId())['isPaymentRefused'] === false
            ) {
                yield $order;
            }
        }
    }

    /**
     * Permet de savoir si une commande est payée.
     */
    public function isPaid(int $orderId): bool
    {
        // Nous nous basons pour l'instant sur le statut legacy de la commande,
        // mais nous utiliserons bientôt le workflow des commandes pour déduire
        // cette information.
        return is_order_status_equal_to($orderId, ...LegacyOrderStatus::getPaidStatuses());
    }

    /**
     * Permet de savoir si une commande a été refusée par le vendeur
     */
    public function isRefusedByVendor(int $orderId): bool
    {
        // Nous nous basons pour l'instant sur le statut legacy de la commande,
        // mais nous utiliserons bientôt le workflow des commandes pour déduire
        // cette information.
        return is_order_status_equal_to($orderId, LegacyOrderStatus::VENDOR_DECLINED);
    }

    /** Permet de savoir si une commande a été expédiée et livrée */
    public function isDelivered(int $orderId): bool
    {
        return is_order_status_equal_to($orderId, ...LegacyOrderStatus::getDeliveredStatuses());
    }

    /** Check if the order's payment has failed */
    public function isBillingFailed(int $orderId): bool
    {
        return is_order_status_equal_to($orderId, LegacyOrderStatus::BILLING_FAILED);
    }

    /**
     * @return Order[]
     */
    public function getAbandonedOrders(): array
    {
        $query = $this->makeQuery(
            <<<SQL
                AND o.workflow_current_module_name = :module
                AND o.workflow_status = 'processing'
                AND o.is_garbage = 0
                AND o.workflow_last_update < :lastUpdate
            SQL
        );

        $orders = [];
        $module = ModuleName::CREDIT_CARD_PAYMENT();
        $rows = $this->db->fetchAll(
            $query,
            [
                'module' => (string) $module,
                'lastUpdate' => (new \DateTime())->sub(new \DateInterval(static::CREDIT_CARD_DELAY)),
            ],
            [
                'module' => Type::STRING,
                'lastUpdate' => Type::DATETIME,
            ]
        );
        $rows = $this->rowsToOrders($rows);

        foreach ($rows as $order) {
            if ($order->getWorkflowProgress()->getModuleName()->equals($module) === true) {
                $orders[] = $order;
            }
        }

        return $orders;
    }

    /**
     * @return int
     */
    public function countInvoicedOrdersFromCompany(int $companyId): int
    {
        $query = <<<SQL
            SELECT
                count(order_id)
            FROM
                cscart_orders
            WHERE
                company_id = :companyId
                AND is_parent_order = 'N'
                AND invoice_number_provided = 1
            SQL
        ;

        $statement = $this->db->prepare($query);
        $statement->bindValue('companyId', $companyId, \PDO::PARAM_INT);
        $statement->execute();

        return (int) $statement->fetchColumn();
    }

    public function overrideOrderStatus(Order $order): void
    {
        $newStatus = (string) $order->deduceLegacyStatus();

        $query = <<<SQL
            UPDATE
                cscart_orders
            SET
                status = :status,
                w_last_status_change = NOW()
            WHERE
                order_id = :id
            SQL
        ;

        $statement = $this->db->prepare($query);
        $statement->bindValue('id', $order->getId());
        $statement->bindValue('status', $newStatus);
        $statement->execute();

        if (container()->getParameter('feature.enable_yavin') === true) {
            $this->publishOrderCreated((string) $order->getStatus(), $newStatus, $order);
        }

        $order->overrideLegacyStatus();
    }

    /**
     * Return an order after have done a check on legacy status.
     * This check may update order data so it must be done before returning the order to have the good data
     *
     * @param int $id
     * @return Order
     * @throws OrderNotFound
     */
    public function getOrderAndCheckStatus(int $id): Order
    {
        if (is_order_status_equal_to($id, OrderStatus::INCOMPLETED)) {
            throw new OrderNotFound($id);
        }

        return $this->getOrder($id);
    }

    public function getStatus(int $orderId): OrderStatus
    {
        return $this->getOrder($orderId)->getStatus();
    }

    public function cancelOrder(int $orderId, string $message = null): void
    {
        $order = $this->getOrder($orderId);

        $canCancel = true;

        if ($order->getStatus()->equals(OrderStatus::PROCESSED())) {
            $canCancel = false;
        }

        if ($this->cancelAction->isAllowed($order) === true) {
            $this->cancelAction->execute($order);
            fn_change_order_status($orderId, OrderStatus::CANCELED);

            if ($message !== null && \strlen($message) > 0) {
                db_query('UPDATE cscart_orders SET decline_reason = ?s WHERE order_id = ?i', $message, $orderId);
            }
        } else {
            $canCancel = false;
        }

        if (false === $canCancel) {
            throw new NotCancellableOrderException($orderId);
        }
    }

    public function refundOrder(Order $order, OrderRefundStatus $status): void
    {
        $order->setRefundStatus($status);

        db_query(
            <<<SQL
                UPDATE
                    cscart_orders
                SET
                    refund_status = ?i
                WHERE
                    order_id = ?i
            SQL,
            $status->getValue(),
            $order->getId()
        );
    }

    /**
     * Execute an action on an order and its childs orders.
     * And change the legacy status
     *
     * Note: cannot be used with CommitTo and ProvideInvoiceNumber because their execute() method is different.
     *
     * @param int[]|null $childOrders
     * @param mixed[] $forceNotification
     */
    public function markOrdersWithAction(
        int $mainOrderId,
        Action $action,
        OrderStatus $legacyStatus,
        array $childOrders = null,
        array $forceNotification = []
    ): self {
        if (\is_null($childOrders)) {
            $childOrders = $this->getChildOrders($mainOrderId);
        }

        $updateLegacyStatus = false;
        foreach ($childOrders as $order) {
            if ($action->isAllowed($order)) {
                if (\method_exists($action, 'execute')) {
                    // phpstan trick because execute is not declared in abstract class Action

                    $action->execute($order);
                }

                $updateLegacyStatus = true;
            }
        }

        if (true === $updateLegacyStatus) {
            $this->changeOrderStatus($mainOrderId, $legacyStatus->getValue(), $forceNotification);
        }

        return $this;
    }

    /**
     * This wrapper is just here to allow mock from other classes that use this service
     *
     * @param mixed[] $forceNotification
     */
    public function changeOrderStatus(int $orderId, string $newStatus, array $forceNotification = []): void
    {
        fn_change_order_status($orderId, $newStatus, '', $forceNotification);
    }

    public function getRefundFlag(): bool
    {
        return $this->refundFlag;
    }

    public function areAllShipmentsDelivered(int $orderId): bool
    {
        foreach ($this->getOrderShipments($orderId) as $shipment) {
            if ($shipment['delivery_date'] === null) {
                return false;
            }
        }

        return true;
    }

    /** @return array[] */
    public function getOrderShipments(int $orderId): array
    {
        $returnedData = [];

        $params['order_id'] = $orderId;
        $params['advanced_info'] = true;

        [$shipments] = fn_get_shipments_info($params);

        foreach ($shipments as $shipment) {
            $returnedData[] = [
                'shipment_id' => \intval($shipment['shipment_id']),
                'shipment_timestamp' => \intval($shipment['shipment_timestamp']),
                'delivery_date' => $shipment['delivery_date'] ? \date_create($shipment['delivery_date'])->format(\DateTime::RFC3339) : null,
                'comments' => $shipment['comments'],
                'order_id' => \intval($shipment['order_id']),
                'shipping_id' => \intval($shipment['shipping_id']),
                'shipping' => $shipment['shipping'],
                'tracking_number' => $shipment['tracking_number'],
                'label_url' => $shipment['label_url'],
                static::PRODUCTS => array_map("intval", $shipment[static::PRODUCTS]),
            ];
        }

        return $returnedData;
    }

    public function getIncompleteOrdersForProcessor(TransactionProcessorInterface $processor): array
    {
        $sql = <<<SQL
            SELECT
                O.order_id
            FROM
                cscart_orders as O
                LEFT JOIN doctrine_order_transactions AS T ON
                    O.order_id = T.order_id
            WHERE
                workflow_current_step_name = "pending-bank-validation"
                AND workflow_status != "failed"
                AND is_parent_order = "N"
                AND T.status != :transactionStatusFailed
                AND is_garbage !=1
                AND T.processor_name = :processorName
                AND T.type = :transactionType
            ORDER BY O.order_id DESC
            SQL
        ;

        $statement = $this->db->prepare($sql);
        $statement->bindValue('processorName', $processor->getName()->getValue());
        $statement->bindValue('transactionType', TransactionType::CREDITCARD()->getValue());
        $statement->execute();

        return $statement->fetchAll(\PDO::FETCH_COLUMN, 0);
    }

    public static function getAttachments(int $orderId, string $attachmentDelimiter): string
    {
        $container = container();
        // Get all attachments ids
        $order['attachments'] = [];
        $attachments = $container
            ->get(OrderAttachmentService::class)
            ->getAllByOrderId($orderId, null)
        ;
        /** @var OrderAttachment $attachment */
        foreach ($attachments as $attachment) {
            $order['attachments'][] = $container->get('router')->generate(
                'api_order_attachment_download',
                ['orderId' => $orderId, 'attachmentId' => $attachment->getId()],
                UrlGeneratorInterface::ABSOLUTE_URL
            );
        }

        return implode($attachmentDelimiter, $order['attachments']);
    }

    public function publishOrderCreated(string $statusFrom, string $statusTo, Order $order): void
    {
        if ($statusFrom === OrderStatus::INCOMPLETED
            && ($statusTo === OrderStatus::STANDBY_BILLING || $statusTo === OrderStatus::STANDBY_VENDOR)
        ) {
            container()
                ->get('Wizacha\Marketplace\Messenger\BroadcastPublisher')
                ->publish(
                    'order.created',
                    $order->toArray(),
                    ['companyId' => $order->getCompanyId()]
                )
            ;
        }
    }

    /**
     * @return Order[]
     */
    protected function getOrdersProcessingWorkflowStep(StepName $stepName): array
    {
        return \iterator_to_array($this->getOrdersProcessingWorkflowStepGenerator($stepName));
    }

    /**
     * @return \Generator
     */
    protected function getOrdersProcessingWorkflowStepGenerator(StepName $stepName, ModuleName $moduleName = null): \Generator
    {
        $sqlString = <<<SQL
            AND o.workflow_current_step_name = :workflowStepName
            AND o.workflow_status = :workflowStatus
        SQL;

        $sqlParams = [
            'workflowStepName' => $stepName->getValue(),
            'workflowStatus' => (string) Status::PROCESSING(),
        ];
        if ($moduleName) {
            $sqlString .= <<<SQL
                AND o.workflow_current_module_name = :workflowModuleName
            SQL;
            $sqlParams['workflowModuleName'] = $moduleName->getValue();
        }

        $query = $this->makeQuery($sqlString);

        /** @var Statement $rows */
        $rows = $this->db->executeQuery($query, $sqlParams);
        $aggregateRowByOrderId = [];
        $currentRow = $rows->fetchAssociative();

        if (false === $currentRow) {
            return;
        }

        do {
            $aggregateRowByOrderId[] = $currentRow;
            /** @var Statement $nextRow */
            $nextRow = $rows->fetchAssociative();
            $lastIteration = ($nextRow === false) ;
            if ($lastIteration || $nextRow['order_id'] !== $currentRow['order_id']) {
                $dbOrder = ($this->rowsToOrders($aggregateRowByOrderId))[0];
                if ($dbOrder->getWorkflowProgress()->getStepName()->equals($stepName)) {
                    yield $dbOrder;
                }
                $aggregateRowByOrderId = [];
            }

            $currentRow = $nextRow;
        } while ($lastIteration === false);
    }

    /**
     * @return Order[]
     */
    private function rowsToOrders(array $rows): array
    {
        // On choppe les data serialisées depuis la DB
        // On fait ca comme ca car avant on les récupérait via une jointure dans la requete principale mais du coup
        // toutes les infos (assez lourdes) étaient dupliquées sur chacune des lignes pour chaque item
        $orderSerializedData = $this->getSerializedData(array_unique(array_column($rows, 'order_id')));

        $items = [];
        foreach ($rows as $row) {
            $items[$row['order_id']][] = new OrderItem([
                'product_id' => $row['product_id'],
                'product_code' => $row['product_code'],
                'id_path' => $row['id_path'],
                'detailed_id' => $row['detailed_id'],
                static::MAX_PRICE_ADJUSTMENT => (int) $row[static::MAX_PRICE_ADJUSTMENT] ? (int) $row[static::MAX_PRICE_ADJUSTMENT] : null,
                'item_id' => $row['item_id'],
                'price' => $row['price'],
                'price_before_adjustment' => $row['price_before_adjustment'],
                'amount' => $row['amount'],
                'customer_comment' => $row['comment'],
                'extra' => $row['item_extra'],
                'tax_data' => (array) unserialize($orderSerializedData[$row['order_id']]['tax_data'] ?? ''),
                'supplier_ref' => $row['supplier_reference'],
                'is_subscription' => $row['is_subscription'],
                'is_renewable' => $row['is_renewable'],
            ]);
        }

        $lastOrderId = null;
        $orders = [];
        foreach ($rows as $row) {
            if ($lastOrderId !== null && $row['order_id'] === $lastOrderId) {
                continue;
            }

            $paymentProcessorName = $this->paymentService->getPaymentProcessorName((int) $row['payment_method']);
            $paymentType = $this->paymentService->getPaymentType((int) $row['payment_method']);
            $paymentExternalReference = $this->paymentService->getPaymentExternalReference((int) $row['payment_method']);

            $company = '0' !== $row['company_id']
                ? $this->companyService->get((int) $row['company_id'])
                : null;
            $companyCountry = $company === null ? '' : $company->getCountry();
            $companyVatNumber = $company === null ? '' : $company->getVatNumber();

            if ($row['user_id'] > 0) {
                $user = $this->userService->get((int) $row['user_id']);
            } else {
                $user = new User(new UserAddress([]), new UserAddress([]));
            }

            if (null !== $company) {
                $shippingTaxId = $this->internationalTaxService->getTaxId($companyCountry, $companyVatNumber, $user);
            } else {
                $shippingTaxId = \Wizacha\Tax::getFullRateId();
            }

            $tax = new TaxEntity($shippingTaxId);
            $shippingTaxRate = new TaxRate((float) $tax->getRate());

            $deliveryType = null;

            if ((string) $row['shipping_ids'] !== '') {
                $ids = explode(',', $row['shipping_ids']);

                if ('N' === $row['is_parent_order'] && \count($ids) > 1) {
                    throw new \RuntimeException(sprintf(
                        'Order #%s has %s shipping ids, it should at most have one',
                        $row['order_id'],
                        \count($ids)
                    ));
                }

                $deliveryType = $this->shippingService->getShippingDeliveryType((int) reset($ids));
            }

            $row['promotions'] = \strlen($row['promotions']) > 0 ? unserialize($row['promotions']) : null;

            $order = new Order([
                'order_id' => $row['order_id'],
                'company' => $company,
                'user' => $user,
                'basket_id' => $row['basket_id'],
                'email' => $row['email'],
                'total' => $row['total'],
                'subtotal' => $row['subtotal'],
                'marketplace_discount_id' => $row['marketplace_discount_id'],
                'marketplace_discount_total' => $row['marketplace_discount_total'],
                'customer_total' => $row['customer_total'],
                'timestamp' => $row['timestamp'],
                'status' => $row['status'],
                'w_last_status_change' => $row['w_last_status_change'],
                'data' => $orderSerializedData[$row['order_id']]['data'],
                'tax_data' => $orderSerializedData[$row['order_id']]['tax_data'],
                'items' => $items[$row['order_id']],
                'shipping_cost' => $row['shipping_cost'],
                'b_label' => $row['b_label'],
                'b_title' => $row['b_title'],
                'b_firstname' => $row['b_firstname'],
                'b_lastname' => $row['b_lastname'],
                'b_address' => $row['b_address'],
                'b_address_2' => $row['b_address_2'],
                'b_city' => $row['b_city'],
                'b_zipcode' => $row['b_zipcode'],
                'b_country' => $row['b_country'],
                'b_company' => $row['b_company'],
                'b_phone' => $row['b_phone'],
                'b_comment' => $row['b_comment'],
                's_label' => $row['s_label'],
                's_title' => $row['s_title'],
                's_firstname' => $row['s_firstname'],
                's_lastname' => $row['s_lastname'],
                's_address' => $row['s_address'],
                's_address_2' => $row['s_address_2'],
                's_city' => $row['s_city'],
                's_zipcode' => $row['s_zipcode'],
                's_country' => $row['s_country'],
                's_company' => $row['s_company'],
                's_phone' => $row['s_phone'],
                's_comment' => $row['s_comment'],
                's_pickup_point_id' => $row['s_pickup_point_id'],
                'customer_comment' => $row['notes'],
                'decline_reason' => $row['decline_reason'],
                'commitment_date' => $row['commitment_date'],
                'commitment_number' => $row['commitment_number'],
                'w_invoice_number' => $row['w_invoice_number'],
                'payment_id' => $row['payment_method'],
                'payment_type' => $paymentType,
                'payment_external_reference' => $paymentExternalReference,
                'payment_processor_name' => $paymentProcessorName,
                'delivery_type' => $deliveryType,
                'shipping_tax_rate' => $shippingTaxRate,
                'shipping_tax_id' => $shippingTaxId,
                // Order workflow flags
                'accepted_by_vendor' => $row['accepted_by_vendor'],
                'confirmed' => $row['confirmed'],
                'user_redirected_to_payment_processor' => $row['user_redirected_to_payment_processor'],
                'payment_deferment_authorized' => $row['payment_deferment_authorized'],
                'max_price_adjustment' => $row[static::MAX_PRICE_ADJUSTMENT],
                'is_paid' => $row['is_paid'],
                'is_capture' => $row['is_capture'],
                'shipping_date' => $row['shipping_date'],
                'delivered' => $row['delivered'],
                'delivery_date' => $row['delivery_date'],
                'funds_dispatched' => $row['funds_dispatched'] === null ? null : (bool) (int) $row['funds_dispatched'],
                'withdrawal_period_over' => $row['withdrawal_period_over'],
                'is_garbage' => $row['is_garbage'],
                'invoice_number_provided' => $row['invoice_number_provided'],
                'refunded' => $row['refunded'],
                'refund_status' => $row['refund_status'],
                'canceled' => $row['canceled'],
                // Order workflow progression
                'workflow_name' => $row['workflow_name'],
                'workflow_status' => $row['workflow_status'],
                'workflow_current_module_name' => $row['workflow_current_module_name'],
                'workflow_current_step_name' => $row['workflow_current_step_name'],
                'hand_delivery_code_data' => $row['hand_delivery_code_data'],
                'subscription_id' => $row['subscription_id'],
                'carriage_paid' => $row['carriage_paid'],
                'invoice_date' => $row['invoice_date'],
                'promotions' => $row['promotions'],
                'do_not_create_invoice' => $row['do_not_create_invoice'],
                'parent_order_id' => $row['parent_order_id'],
                'is_parent_order' => $row['is_parent_order'],
                'is_customer_professional' => $row['is_customer_professional'],
                'customer_company' => $row['customer_company'],
                'customer_legal_identifier' => $row['customer_legal_identifier'],
                'customer_intra_european_community_vat' => $row['customer_intra_european_community_vat'],
                'customer_job_title' => $row['customer_job_title'],
                'customer_account_comment' => $row['customer_account_comment'],
                'customer_external_identifier' => $row['customer_external_identifier'],
                'extra' => $row['order_extra'],
            ]);

            /**
             * Gestion du workflow de la commande :
             * - récupération du workflow si l'information est présente en DB,
             * - (ou) génération du workflow puis sauvegarde en DB,
             * Puis :
             * - exécution du workflow (opération idempotante: on peut le lancer
             *   plusieurs fois pour faire avancer le workflow, il se retrouvera
             *   toujours dans un état stable et vrai.
             *   ).
             */
            try {
                $this->workflowService->getWorkflow($order)->process($order);
            } catch (NoAdaptedWorkflow $e) {
                $this->logger->error('[No Adapted Workflow]', [
                    'orderId'   => $order->getId(),
                    'exception' => $e
                ]);

                continue;
            }

            $orders[] = $order;

            $lastOrderId = $row['order_id'];
        }

        return $orders;
    }

    private function getOrderFromShipmentId(int $shipmentId): Order
    {
        $query = $this->makeQuery(<<<SQL
            AND o.order_id = (
                SELECT
                    order_id
                FROM
                    cscart_shipment_items
                WHERE
                    shipment_id=:shipmentId
                LIMIT 1
            )
            AND o.is_parent_order = 'N'
            SQL
        );

        $rows = $this->db->fetchAll($query, [
            'shipmentId' => $shipmentId,
        ]);

        if (empty($rows)) {
            throw NotFound::fromId('Shipment', $shipmentId);
        }

        return $this->rowsToOrders($rows)[0];
    }

    /**
     * Recupere les data sérialisées de l'order a part de la grosse query pour alleger la mémoire (non duplication des lignes lourdes)
     */
    private function getSerializedData(array $orderIds): array
    {
        $stmt = $this->db->executeQuery(
            <<<SQL
            SELECT
                o.order_id,
                oda.data,
                oda_tax.data as tax_data
            FROM
                cscart_orders AS o
                LEFT JOIN cscart_order_data AS oda
                    ON oda.order_id = o.order_id
                    AND oda.type = :orderDataGroup
                LEFT JOIN cscart_order_data AS oda_tax
                    ON oda_tax.order_id = o.order_id
                    AND oda_tax.type = :orderDataTax
            WHERE
                o.order_id IN(:orderIds)
            SQL,
            [
                'orderDataGroup' => OrderDataType::GROUP_INFO(),
                'orderDataTax' => OrderDataType::TAX_INFO(),
                'orderIds' => $orderIds
            ],
            [
                'orderDataGroup' => \PDO::PARAM_STR,
                'orderDataTax' => \PDO::PARAM_STR,
                'orderIds' => Connection::PARAM_INT_ARRAY
            ]
        );

        $data = [];

        foreach ($stmt as $line) {
            $data[$line['order_id']] = [
                'data' => $line['data'],
                'tax_data' => $line['tax_data'],
            ];
        }

        return $data;
    }

    /** @return array */
    public function getOrdersWithEmptyEmail(): array
    {
        return db_get_array(
            <<<SQL
            SELECT
                o.order_id,
                u.email as user_email
            FROM
                cscart_orders AS o
                INNER JOIN ?:users u
                    ON o.user_id = u.user_id
            WHERE
                o.email = '' OR o.email = null
            SQL
        );
    }

    public function setOrderEmail(int $orderId, string $email): void
    {
        $data = [
            'email' => $email
        ];

        db_query("UPDATE ?:orders SET ?u WHERE order_id = ?i", $data, $orderId);
    }

    public function setInvoiceNumber(int $orderId, string $invoiceNumber): void
    {
        db_query("UPDATE ?:orders SET w_invoice_number = ?s WHERE order_id = ?i", $invoiceNumber, $orderId);
    }

    public function setExtraData(int $orderId, string $extra): void
    {
        db_query("UPDATE ?:orders SET extra = ?s WHERE order_id = ?i", $extra, $orderId);
    }


    /**
     * Calculate discounted prices
     *
     * @param mixed[] $orderInfo
     * @param mixed[] $items
     *
     * @return mixed[] Processed Items
     */
    public function applyDiscountOnItems(array $orderInfo, array &$items): array
    {
        $isTaxIncludedInPrice = false;
        if (\count($orderInfo['taxes'] ?? []) > 0) {
            $isTaxIncludedInPrice = reset($orderInfo['taxes'])['price_includes_tax'] === 'Y';
        }

        $discountedPrices = $this->orderDiscountsCalculator->getPricesFromLegacyOrderData($orderInfo, $isTaxIncludedInPrice);
        $order = $this->getAnyOrder((int) $orderInfo['order_id']);

        foreach ($items as $productId => &$product) {
            if (\array_key_exists($productId, $discountedPrices) === true) {
                $unitPrice = Money::fromVariable($product['price']);
                $amountItem = $order->getOrderAmounts()->getAmountItem($product['item_id']);
                $product['discounted_unit_price'] = $unitPrice->subtract($amountItem->getUnitDiscount()->add($amountItem->getUnitDiscountMarketplace()));
            }
        }

        return $items;
    }

    // Get Legacy Order and override data with frozen amounts from OrderAmountCalculator
    public function overrideLegacyOrder(
        int $orderId,
        $nativeLanguage = false,
        $formatInfo = true,
        $getEdpFiles = false,
        $skipStaticValue = false,
        $auth = null,
        $forceArea = null
    ) {
        $orderLegacy = fn_get_order_info($orderId, $nativeLanguage, $formatInfo, $getEdpFiles, $skipStaticValue, $auth, $forceArea);
        if ([] === $orderLegacy) {
            return [];
        }

        $orderAmounts = $this->orderAmountsRepository->find($orderId);
        if (null === $orderAmounts) {
            try {
                // If the order amounts doesn't exist yet, we need to generate it
                $order = $this->getAnyOrder($orderId);
                $orderAmounts = $order->getOrderAmounts();
            } catch (OrderNotFound $e) {
                // If we can't find the order with the orderService, its probably an old corrupted order
                // (workflow problem for example) and we can't override the data
                return $orderLegacy;
            }
        }

        // General
        $orderLegacy['total'] = $orderAmounts->getTotalInclTaxes()->getConvertedAmount();
        $orderLegacy['total_excl_tax'] = $orderAmounts->getTotalExclTaxes()->getConvertedAmount();
        $orderLegacy['display_subtotal_excl_taxes'] = $orderAmounts->getSubTotalExclTaxes()->getConvertedAmount();
        $orderLegacy['display_subtotal'] = $orderAmounts->getSubTotalInclTaxes()->getConvertedAmount();
        $orderLegacy['discount'] = $orderAmounts->getTotalDiscount()->getConvertedAmount();
        $orderLegacy['subtotal_discount'] = $orderAmounts->getTotalDiscount()->getConvertedAmount();
        $orderLegacy['shipping_cost'] = $orderAmounts
            ->getShippingAmount()
            ->getDiscountedTotalInclTaxes()
            ->getConvertedAmount();
        $orderLegacy['shipping_cost_without_tax'] = $orderAmounts
            ->getShippingAmount()
            ->getDiscountedTotalExclTaxes()
            ->getConvertedAmount();

        $orderLegacy['subtotal'] = $orderAmounts->getSubTotalInclTaxes()->getConvertedAmount();
        $orderLegacy['tax_subtotal'] = $orderAmounts->getTotalTaxAmount()->getConvertedAmount();
        $orderLegacy['marketplace_discount_total'] = $orderAmounts->getTotalMarketplaceDiscount()->getConvertedAmount();
        $orderLegacy['customer_total'] = $orderAmounts->getCustomerTotal()->getConvertedAmount();

        // Products
        /** @var AmountItem $item */
        foreach ($orderAmounts->getAmountItems() as $item) {
            $productData = $item->getOrderItemData();
            $productId = $productData->getItemId();

            if (true === \array_key_exists($productId, $orderLegacy['products'])) {
                $product = &$orderLegacy['products'][$productId];
                $product['price']
                    = true === $item->getOrderItemData()->getTaxIncludedInPrice()
                    ? $item->getUnitPriceInclTaxes()->getConvertedAmount()
                    : $item->getUnitPriceExclTaxes()->getConvertedAmount();

                $product['unit_price_excl_taxes'] = $item->getUnitPriceExclTaxes()->getConvertedAmount();
                $product['unit_price_incl_taxes'] = $item->getUnitPriceInclTaxes()->getConvertedAmount();
                $product['unit_tax_amount'] = $item->getUnitTaxAmount()->getConvertedAmount();

                $product['subtotal_excl_taxes'] = $item->getTotalExclTaxes()->getConvertedAmount();
                $product['subtotal_incl_taxes'] = $item->getTotalInclTaxes()->getConvertedAmount();
                $product['subtotal_tax_amount'] = $item->getTotalTaxAmount()->getConvertedAmount();

                $product['display_subtotal'] = $item->getTotalInclTaxes()->getConvertedAmount();
                $product['amount'] = $item->getQuantity();
                $product['discount'] = $item->getDiscount()->getConvertedAmount();
                $product['tax_value'] = $item->getTotalTaxAmount()->getConvertedAmount();
                $product['green_tax'] = $item->getGreenTax()->getConvertedAmount();

                $product['product'] = $item->getOrderItemData()->getProductName();
            }
        }

        $this->overrideLegacyOrderTaxes($orderAmounts, $orderLegacy['taxes']);
        return $orderLegacy;
    }

    /** @param mixed[] $taxesLegacy */
    private function overrideLegacyOrderTaxes(OrderAmounts $orderAmounts, array &$taxesLegacy): void
    {
        // Remove legacy taxes if they don't exist in order amounts
        // fn_calculate_taxes is obsolete and don't take international taxes into account
        $orderAmountsTaxIds = \array_keys($orderAmounts->getTaxes());
        foreach ($taxesLegacy as $taxId => $tax) {
            if (false === \in_array($taxId, $orderAmountsTaxIds)) {
                unset($taxesLegacy[$taxId]);
            }
        }

        foreach ($orderAmounts->getTaxes() as $taxId => $tax) {
            $taxesLegacy[$taxId]['tax_subtotal'] = $tax['total']->getConvertedAmount();
            // Only for display, on the views, all amounts are from getConvertedAmount()
            $taxesLegacy[$taxId]['display_tax_subtotal'] = $tax['total']->getConvertedAmount();

            $taxesLegacy[$taxId]['applies'] = [];
            foreach ($tax['applies'] as $taxDetailsKey => $taxDetails) {
                $taxesLegacy[$taxId]['applies'][$taxDetailsKey] = $taxDetails->getConvertedAmount();
            }
        }
    }

    /**
     * Faster method to override legacy order data for an exim export
     * @return mixed[]
     */
    public function overrideEximOrder(int $orderId): array
    {
        $orderLegacy = fn_get_order_info($orderId);

        if ([] === $orderLegacy) {
            return [];
        }

        $amounts = $this->orderAmountsRepository->findForExim($orderId);
        if ([] === $amounts) {
            return $orderLegacy;
        }

        $orderLegacy['total'] = (new PreciseMoney((int) $amounts['total_incl_taxes']))->getConvertedAmount();
        $orderLegacy['total_excl_tax'] = (new PreciseMoney((int) $amounts['total_excl_taxes']))->getConvertedAmount();
        $orderLegacy['discount'] = (new PreciseMoney((int) $amounts['total_discount']))->getConvertedAmount();
        $orderLegacy['shipping_cost'] = (new PreciseMoney((int) $amounts['discounted_total_incl_taxes']))
            ->getConvertedAmount();
        $orderLegacy['subtotal'] = (new PreciseMoney((int) $amounts['sub_total_incl_taxes']))->getConvertedAmount();
        $orderLegacy['tax_subtotal'] = (new PreciseMoney((int) $amounts['total_tax_amount']))->getConvertedAmount();
        $orderLegacy['marketplace_discount_total'] = (new PreciseMoney((int) $amounts['total_marketplace_discount']))
            ->getConvertedAmount();
        $orderLegacy['customer_total'] = (new PreciseMoney((int) $amounts['customer_total']))->getConvertedAmount();

        $taxes = \json_decode($amounts['taxes'], true);
        foreach ($taxes as $taxId => $tax) {
            if (true === \array_key_exists($taxId, $orderLegacy['taxes'])) {
                $orderLegacy['taxes'][$taxId]['tax_subtotal'] = (new PreciseMoney((int) $tax['total']))
                    ->getPreciseConvertedAmount();
                $orderLegacy['taxes'][$taxId]['display_tax_subtotal'] = (new PreciseMoney((int) $tax['total']))
                    ->getConvertedAmount();

                foreach ($tax['applies'] as $taxDetailsKey => $taxDetails) {
                    $orderLegacy['taxes'][$taxId]['applies'][$taxDetailsKey] = (new PreciseMoney($taxDetails))
                        ->getPreciseConvertedAmount();
                }
            }
        }

        $orderLegacy['promotions'] = \json_decode($amounts['promotions'] ?? "", true);

        return $orderLegacy;
    }

    public function getOrderDataById(int $orderId): array
    {
        $result = $this->orderRepository->getOrderById($orderId);

        if (\count($result) === 0) {
            throw new OrderNotFound($orderId);
        }

        return $result;
    }

    public function getOrderWorkflowLastUpdate(int $orderId): ?\DateTime
    {
        return  $this->orderRepository->getOrderWorkflowLastUpdate($orderId);
    }

    /**
     * @param string[] $filters
     *
     * @return \Generator
     */
    public function getOrdersWithCommissionByFilters(array $filters): \Generator
    {
        $statement = $this->orderRepository->getOrdersWithCommissionByFilters($filters);
        while ($row = $statement->fetch()) {
            yield $this->rowsToOrders([$row])[0];
        }
    }
}
