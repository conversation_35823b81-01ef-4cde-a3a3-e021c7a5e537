<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\OrderAction\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Wizacha\Marketplace\Order\OrderAction\Entity\OrderAction;

class OrderActionRepository extends ServiceEntityRepository
{
    public function save(OrderAction $orderAction): OrderAction
    {
        $this->getEntityManager()->persist($orderAction);
        $this->getEntityManager()->flush();

        return $orderAction;
    }

    /** @return OrderAction[] */
    public function getByOrderId(int $orderId): array
    {
        return $this->findBy(
            [
                'orderId' => $orderId
            ]
        );
    }
}
