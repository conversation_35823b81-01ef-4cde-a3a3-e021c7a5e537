<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\OrderAction\Service;

use Symfony\Component\Routing\RouterInterface;
use Wizacha\Marketplace\Order\OrderAction\Entity\OrderAction;
use Wizacha\Marketplace\Order\OrderAction\Exception\OrderActionNotFound;
use Wizacha\Marketplace\Order\OrderAction\Repository\OrderActionRepository;
use Wizacha\Marketplace\Order\OrderService;

class OrderActionService
{
    protected OrderActionRepository $orderActionRepository;

    protected OrderService $orderService;

    protected RouterInterface $router;

    public function __construct(
        OrderActionRepository $orderActionRepository,
        OrderService $orderService,
        RouterInterface $router
    ) {
        $this->orderActionRepository = $orderActionRepository;
        $this->orderService = $orderService;
        $this->router = $router;
    }

    /** @return OrderAction[] */
    public function getByOrderId(int $orderId): array
    {
        $order = $this->orderService->getOrder($orderId);
        return $this->orderActionRepository->getByOrderId($order->getId());
    }

    public function addOrder(OrderAction $orderAction): OrderAction
    {
        return $this->orderActionRepository->save($orderAction);
    }

    /** @return OrderAction[] */
    public function getAllOrders(): array
    {
        return $this->orderActionRepository->findAll();
    }
}
