<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\OrderAction\Entity;

use Wizacha\Marketplace\Order\OrderStatus;

class OrderAction
{
    private int $id;

    private int $orderId;

    private OrderStatus $status;

    private \DateTimeImmutable $date;

    private ?int $userId;

    private ?string $userName;

    public function getId(): int
    {
        return $this->id;
    }

    public function getOrderId(): int
    {
        return $this->orderId;
    }

    public function setOrderId(int $orderId): self
    {
        $this->orderId = $orderId;

        return $this;
    }

    public function getStatus(): OrderStatus
    {
        return $this->status;
    }

    public function setStatus(OrderStatus $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getDate(): \DateTimeImmutable
    {
        return $this->date;
    }

    public function defineCreatedAt(): self
    {
        $this->date =  new \DateTimeImmutable();

        return $this;
    }

    public function getUserId(): ?int
    {
        return $this->userId;
    }

    public function setUserId(?int $userId): self
    {
        $this->userId = $userId;

        return $this;
    }

    public function getUserName(): ?string
    {
        return $this->userName;
    }

    public function setUserName(?string $userName): self
    {
        $this->userName = $userName;

        return $this;
    }

    public function expose(): array
    {
        return [
            'id' => $this->getId(),
            'orderId' => $this->getOrderId(),
            'status' => $this->getStatus()->getValue(),
            'description' => __('order_status_' . $this->getStatus()->getValue()),
            'userId' => $this->getUserId(),
            'userName' => $this->getUserName(),
            'date' => $this->getDate()->format(\DateTimeImmutable::RFC3339)
        ];
    }
}
