Wizacha\Marketplace\Order\OrderAction\Entity\OrderAction:
    type: entity
    table: order_actions
    repositoryClass: Wizacha\Marketplace\Order\OrderAction\Repository\OrderActionRepository
    id:
        id:
            type: integer
            generator:
                strategy: AUTO
    fields:
        orderId:
            type: integer
            nullable: false
        status:
            type: php_enum_order_status
            nullable: false
        date:
            type: datetime_immutable
            nullable: false
        userId:
            type: integer
            nullable: true
        userName:
            type: string
            length: 255
            nullable: true
    lifecycleCallbacks:
        prePersist: [ defineCreatedAt ]
