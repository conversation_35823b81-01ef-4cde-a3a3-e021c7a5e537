<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\OrderAction\Event;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Wizacha\Marketplace\Order\Event\OrderStatusUpdated;
use Wizacha\Marketplace\Order\OrderAction\Entity\OrderAction;
use Wizacha\Marketplace\Order\OrderAction\Service\OrderActionService;
use Wizacha\Marketplace\Order\OrderEvents;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\AppBundle\Security\User\UserService as SecurityUserService;
use Wizacha\Marketplace\User\UserService;

class OrderStatusUpdatedEventSubscriber implements EventSubscriberInterface
{
    private OrderActionService $orderActionService;

    private SecurityUserService $securityUserService;

    private UserService $userService;

    public function __construct(
        OrderActionService $orderActionService,
        UserService $userService,
        SecurityUserService $securityUserService
    ) {
        $this->orderActionService = $orderActionService;
        $this->userService = $userService;
        $this->securityUserService = $securityUserService;
    }

    public static function getSubscribedEvents(): array
    {
         return [
             OrderEvents::UPDATED  => ['onOrderUpdate', 0],
         ];
    }

    public function onOrderUpdate(OrderStatusUpdated $event): void
    {
        $orderId = $event->getId();
        $orderAction = new OrderAction();
        $userData = $this->getLoggedUserInfo();

        $this->orderActionService->addOrder(
            $orderAction
                ->setOrderId($orderId)
                ->setStatus(new OrderStatus($event->getStatusTo()))
                ->setUserId($userData['userId'])
                ->setUserName($userData['userName'])
        );
    }

    /** return string[] */
    private function getLoggedUserInfo(): array
    {
        $loggedUserId = $this->securityUserService->getCurrentUserId();

        if (\is_null($loggedUserId) === true) {
            return [
                'userId' => null,
                'userName' => 'Auto'
            ];
        }

        return [
            'userId' => $loggedUserId,
            'userName' => $this->userService->get($loggedUserId)->getFullName()
        ];
    }
}
