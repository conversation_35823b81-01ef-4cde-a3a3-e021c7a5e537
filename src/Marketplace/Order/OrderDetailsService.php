<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order;

use Doctrine\DBAL\Connection;

class OrderDetailsService
{
    /** @var Connection */
    private $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
    }

    public function saveDetails(int $orderId, string $details): self
    {
        $this->connection->update('cscart_orders', ['details' => $details], ['order_id' => $orderId]);

        return $this;
    }
}
