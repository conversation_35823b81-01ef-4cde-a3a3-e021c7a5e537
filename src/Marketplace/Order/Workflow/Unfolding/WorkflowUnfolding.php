<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow\Unfolding;

use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Workflow\Module;
use Wizacha\Marketplace\Order\Workflow\ModuleName;

/**
 * Représente tous les status pour toutes les étapes du workflow
 *
 * Typiquement on aura quelque chose comme:
 *  - COMPLETED
 *  - COMPLETED
 *  - COMPLETED
 *  - PROCESSING | FAILED
 *  - NOT_REACHED_YET
 *  - NOT_REACHED_YET
 */
class WorkflowUnfolding
{
    /** @var Order */
    private $order;

    /** @var ModuleUnfolding[] */
    private $modules = [];

    public function __construct(Order $order)
    {
        $this->order = $order;
    }

    /**
     * On ajoute une étape dans la liste de progression pour la commande donnée
     */
    public function push(Module $module): self
    {
        $self = clone $this;

        // Si la dernière étape qui a été ajoutée est finie, alors on va calculer
        // l'état d'avancement de celle qu'on ajoute, sinon on marque les nouvelles
        // étapes comme n'ayant pas encore été atteintes
        if (empty($self->modules) || end($self->modules)->isCompleted()) {
            $self->modules[] = ModuleUnfolding::of($module, $this->order);
        } else {
            $self->modules[] = ModuleUnfolding::notReachedYet($module);
        }

        return $self;
    }

    public function getOrder(): Order
    {
        return $this->order;
    }

    /**
     * @return ModuleUnfolding[]
     */
    public function getModules(): array
    {
        return $this->modules;
    }

    public function hasCompleted(ModuleName $module): bool
    {
        $modules = array_filter(
            $this->modules,
            function (ModuleUnfolding $unfolding) use ($module): bool {
                return $unfolding->getName()->equals($module);
            }
        );

        if (\count($modules) === 0) {
            return false;
        }

        /** @var ModuleUnfolding $firstModule */
        $firstModule = reset($modules);

        return $firstModule->isCompleted();
    }

    public function expose(): array
    {
        return [
            'orderId' => $this->order->getId(),
            'currentModule' => (string) $this->order->getWorkflowProgress()->getModuleName(),
            'currentStep' => (string) $this->order->getWorkflowProgress()->getStepName(),
            'modules' => array_map(function (ModuleUnfolding $unfolding): array {
                return $unfolding->expose();
            }, $this->modules),
        ];
    }
}
