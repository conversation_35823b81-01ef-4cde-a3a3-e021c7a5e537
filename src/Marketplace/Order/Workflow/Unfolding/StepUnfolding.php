<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow\Unfolding;

use Wizacha\Marketplace\Order\Workflow\StepName;
use Wizacha\Marketplace\Order\Workflow\Status;

class StepUnfolding
{
    /** @var StepName */
    private $name;

    /** @var Status */
    private $status;

    public function __construct(StepName $name, Status $status)
    {
        $this->name = $name;
        $this->status = $status;
    }

    public function getName(): StepName
    {
        return $this->name;
    }

    public function getStatus(): Status
    {
        return $this->status;
    }

    public function expose(): array
    {
        return [
            'name' => (string) $this->name,
            'status' => (string) $this->status,
        ];
    }
}
