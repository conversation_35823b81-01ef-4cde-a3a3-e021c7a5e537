<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow\Unfolding;

use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Workflow\Module;
use Wizacha\Marketplace\Order\Workflow\ModuleName;
use Wizacha\Marketplace\Order\Workflow\Status;
use Wizacha\Marketplace\Order\Workflow\StepName;

class ModuleUnfolding
{
    /** @var ModuleName */
    private $module;

    /** @var StepUnfolding[] */
    private $steps = [];

    private function __construct(ModuleName $module)
    {
        $this->module = $module;
    }

    public static function of(Module $module, Order $order): self
    {
        $self = new self($module->getName());
        $progress = $module->check($order);

        $steps = $module->getSteps();

        while (\count($steps) > 0) {
            $stepName = array_shift($steps);

            if ($progress->isProcessing() && $progress->getStepName()->equals($stepName)) {
                //si le module est toujours en cours on marque la step comme PROCESSING
                $self = $self->push($stepName, Status::PROCESSING());

                //on flag toutes les steps qui suivent celle en PROCESSING comme
                //non atteintes
                array_reduce($steps, function (self $self, StepName $stepName): self {
                    return $self->push($stepName, Status::NOT_REACHED_YET());
                }, $self);
                break;
            }

            //par défaut on marque que la step est finie (ce qui est
            //potentiellement faux puisque qu'on ne peut pas determinée si elle
            //n'a pas échoué)
            $self = $self->push($stepName, Status::COMPLETED());
        }

        return $self;
    }

    public static function notReachedYet(Module $module): self
    {
        // On marque chaque step comme non atteinte
        return array_reduce(
            $module->getSteps(),
            function (self $progress, StepName $step): self {
                return $progress->push($step, Status::NOT_REACHED_YET());
            },
            new self($module->getName())
        );
    }

    public function getName(): ModuleName
    {
        return $this->module;
    }

    /**
     * @return StepUnfolding[]
     */
    public function getSteps(): array
    {
        return $this->steps;
    }

    public function hasStarted(): bool
    {
        /** @var StepUnfolding $firstStep */
        $firstStep = reset($this->steps);

        //l'étape n'a pas commencée si son premier step n'a pas encore été atteint
        return !$firstStep->getStatus()->equals(Status::NOT_REACHED_YET());
    }

    public function isCompleted(): bool
    {
        $completed = array_filter(
            $this->steps,
            function (StepUnfolding $progress): bool {
                return $progress->getStatus()->equals(Status::COMPLETED());
            }
        );

        //l'étape est considérée comme finie si toutes ses steps le sont
        return \count($completed) === \count($this->steps);
    }

    public function getCurrentStep(): StepName
    {
        if (!$this->hasStarted() || $this->isCompleted()) {
            throw new \LogicException();
        }

        $inProgress = array_filter(
            $this->steps,
            function (StepUnfolding $step): bool {
                return $step->getStatus()->equals(Status::PROCESSING());
            }
        );

        //la step en cours est la première avec le status PROCESSING (et il ne
        //devrait y en avoir qu'une de toute façon)
        /** @var StepUnfolding $firstStepInProgress */
        $firstStepInProgress = reset($inProgress);

        return $firstStepInProgress->getName();
    }

    public function push(StepName $step, Status $status): self
    {
        $self = clone $this;
        $self->steps[] = new StepUnfolding($step, $status);

        return $self;
    }

    public function expose(): array
    {
        return [
            'name' => (string) $this->module,
            'steps' => array_map(function (StepUnfolding $unfolding): array {
                return $unfolding->expose();
            }, $this->steps),
        ];
    }
}
