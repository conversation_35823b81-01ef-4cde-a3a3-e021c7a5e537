<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow;

use Wizacha\Marketplace\Order\Order;

/**
 * Un Module est une étape métier du processus de commande ("Prise d'empreinte CB",
 * "Validation de la commande", "Répartition de fond", etc.) composé de :
 *
 * - Un nom qui lui est propre et qui permet de l'identifier.
 *
 * - Une fonction 'check()' retournant en temps réel le statut
 *   interne du module (sans devoir le persister) : elle contient donc la ou
 *   les règles métier (vérification d'un champ en DB, d'un composant dans la
 *   commande,..) correspondant à l'étape.
 *
 * - {0,n} fonctions métier qui permettent à la marketplace d'interagir de
 *   manière évènementielle avec l'étape métier (ex: "validate", "allocate funds").
 *
 * Ce qui permet d'avoir tous le code métier, autant celui qui impacte réellement
 * le système que celui qui permet d'effectuer une vérification dans la même classe.
 */
abstract class Module
{
    // C'est une classe abstraite et pas une interface,
    // car il va y avoir des variables, telles qu'un
    // tableau de status internes au module que j'appelerai
    // Step.

    /**
     * C'est le module qui sera exécuté par le workflow si le module termine en
     * Status::FAILED. Ce module d'erreur peut-être différent pour le
     * même module, selon le workflow dans lequel il se trouve. C'est donc au
     * workflow de configurer le module d'erreur pour chaque module (lorsque
     * c'est nécessaire).
     *
     * ex: Le module "Remboursement" sera exécuté si le module "Validation Vendeur"
     * sort en Status::FAILED.
     *
     * @var Module|null
     */
    protected $moduleOnError = null;

    /**
     * Cette fonction permet d'identifier un module.
     */
    abstract public function getName(): ModuleName;

    /**
     * @return StepName[]
     */
    abstract public function getSteps(): array;

    /**
     * Cette fonction s'occupe de vérifier l'état du système par ses propres
     * moyens afin de remonter son statut interne pour permettre au workflow
     * de continuer d'avancer dans ses étapes.
     */
    abstract public function check(Order $order): Progress;

    /**
     * @throws \LogicException Dans le cas où il manquerait un module dans la stack
     */
    public function validate(ModuleStack $stack): void
    {
        // Par défaut on considère qu'un module peut être ajouté à n'importe quelle stack
    }

    public function setModuleOnError(Module $module): void
    {
        $this->moduleOnError = $module;
    }

    public function getModuleOnError(): ?Module
    {
        return $this->moduleOnError;
    }
}
