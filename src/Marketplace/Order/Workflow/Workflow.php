<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow;

use Wizacha\Marketplace\Company\Company;
use Wizacha\Marketplace\Order\Workflow\Unfolding\WorkflowUnfolding;
use Wizacha\Marketplace\Order\Order;

abstract class Workflow
{
    /**
     * @var WorkflowName
     */
    protected $name;

    /**
     * @var ModuleStack
     */
    protected $stack;

    /**
     * @var WorkflowService
     */
    protected $workflowService;

    /**
     * Récupération du nom du workflow, qui est en réalité le namespace de la
     * classe (ex: \Wizacha\Marketplace\Order\Workflow\Workflow\BasicCreditCard)
     */
    public function getName(): WorkflowName
    {
        return $this->name;
    }

    /**
     * Fonction implémentée par chaque workflow, permettant de spécifier :
     * - un nom,
     * - une liste de module.
     *
     * C'est le point d'entrée pour récupérer un workflow correctement
     * initialisé et c'est pourquoi le constructeur n'est pas public.
     */
    abstract public static function load(WorkflowService $workflowService): self;

    /**
     * Fonction vérifiant qu'un workflow est bien celui qu'il faut pour une
     * commande donnée : vérification des modes de paiement, des modes de
     * livraison, etc. mais aussi des features flags ou autres configurations.
     *
     * Passage de la Company en paramètre pour éviter de la recharger à chaque
     * fois dans la fonction (elle est utile pour la distinction C2C/B2x).
     */
    abstract public static function isSuitableFor(Order $order): bool;

    /**
     * Fonction permettant de vérifier le workflow et ainsi de le faire évoluer.
     */
    public function process(Order $order): Progress
    {
        // Traitement du workflow et récupération de la progression
        $progress = $this->stack->process($order);

        // Sauvegarde de la progression
        $this->saveProgress($order, $progress);

        // Gestion des modules
        return $progress;
    }

    public function progressOf(Order $order): WorkflowUnfolding
    {
        return array_reduce(
            $this->stack->getModules(),
            function (WorkflowUnfolding $unfold, Module $module): WorkflowUnfolding {
                return $unfold->push($module);
            },
            new WorkflowUnfolding($order)
        );
    }

    public function getStack(): ModuleStack
    {
        return $this->stack;
    }

    /**
     * Le constructeur n'est pas public car il faut utiliser la fonction static
     * `load` implémentée dans chaque définition des Workflow.
     */
    protected function __construct(WorkflowName $name, ModuleStack $stack, WorkflowService $workflowService)
    {
        $this->name = $name;
        $this->stack = $stack;
        $this->workflowService = $workflowService;
    }

    /**
     * Sauvegarde de la progression du workflow, dans la commande.
     */
    protected function saveProgress(Order $order, Progress $progress)
    {
        $this->workflowService->saveWorkflowProgress($order, $this->name, $progress);
    }
}
