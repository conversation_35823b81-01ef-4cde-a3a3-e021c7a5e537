<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow;

use MyCLabs\Enum\Enum;

/**
 * @method static WorkflowName ORDER_CONFIRMATION_MANUAL_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD()
 * @method static WorkflowName ORDER_CONFIRMATION_MANUAL_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY()
 * @method static WorkflowName ORDER_CONFIRMATION_MANUAL_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD()
 * @method static WorkflowName ORDER_CONFIRMATION_BANK_TRANSFER_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_FUNDS_DISPATCH()
 * @method static WorkflowName ORDER_CONFIRMATION_BANK_TRANSFER_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD_FUNDS_DISPATCH()
 * @method static WorkflowName ORDER_CONFIRMATION_BANK_TRANSFER_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_FUNDS_DISPATCH()
 * @method static WorkflowName ORDER_CONFIRMATION_BANK_TRANSFER_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD_FUNDS_DISPATCH()
 * @method static WorkflowName CREDIT_CARD_PAYMENT_AUTHORIZATION_ORDER_CONFIRMATION_VENDOR_VALIDATION_CREDIT_CARD_PAYMENT_CAPTURE_ORDER_PREPARATION_DELIVERY_FUNDS_DISPATCH()
 * @method static WorkflowName CREDIT_CARD_PAYMENT_AUTHORIZATION_ORDER_CONFIRMATION_VENDOR_VALIDATION_CREDIT_CARD_PAYMENT_CAPTURE_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD_FUNDS_DISPATCH()
 * @method static WorkflowName CREDIT_CARD_PAYMENT_AUTHORIZATION_ORDER_CONFIRMATION_VENDOR_VALIDATION_CREDIT_CARD_PAYMENT_CAPTURE_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD()
 * @method static WorkflowName CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_FUNDS_DISPATCH()
 * @method static WorkflowName CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD_FUNDS_DISPATCH()
 * @method static WorkflowName CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_FUNDS_DISPATCH()
 * @method static WorkflowName CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD_FUNDS_DISPATCH()
 * @method static WorkflowName CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD()
 * @method static WorkflowName CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD()
 * @method static WorkflowName PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WAIT_PAYMENT_DEFERMENT_WITHDRAWAL_PERIOD_FUNDS_DISPATCH()
 * @method static WorkflowName PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_WAIT_PAYMENT_DEFERMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD_FUNDS_DISPATCH()
 * @method static WorkflowName PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_WAIT_PAYMENT_DEFERMENT_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD_FUNDS_DISPATCH()
 * @method static WorkflowName ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD()
 * @method static WorkflowName ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD()
 * @method static WorkflowName ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY()
 * @method static WorkflowName CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_WITHDRAWAL_PERIOD_FUNDS_DISPATCH()
 * @method static WorkflowName PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_VENDOR_VALIDATION_ORDER_PREPARATION_WAIT_PAYMENT_DEFERMENT_WITHDRAWAL_PERIOD_FUNDS_DISPATCH()
 * @method static WorkflowName PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD_WAIT_PAYMENT_DEFERMENT_FUNDS_DISPATCH()
 */
class WorkflowName extends Enum
{
    private const ORDER_CONFIRMATION_BANK_TRANSFER_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_FUNDS_DISPATCH = 'order-confirmation-bank-transfer-payment-vendor-validation-order-preparation-delivery-funds_dispatch';
    private const ORDER_CONFIRMATION_BANK_TRANSFER_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD_FUNDS_DISPATCH = 'order-confirmation-bank-transfer-payment-vendor-validation-order-preparation-delivery-withdrawal-period-funds-dispatch';
    private const ORDER_CONFIRMATION_BANK_TRANSFER_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_FUNDS_DISPATCH = 'order-confirmation-bank-transfer-payment-vendor-validation-order-preparation-funds-dispatch';
    private const ORDER_CONFIRMATION_BANK_TRANSFER_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD_FUNDS_DISPATCH = 'order-confirmation-bank-transfer-payment-vendor-validation-order-preparation-withdrawal-period-funds-dispatch';
    private const CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_FUNDS_DISPATCH = 'credit-card-payment-order-confirmation-vendor-validation-order-preparation-delivery-funds-dispatch';
    private const CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD_FUNDS_DISPATCH = 'credit-card-payment-order-confirmation-vendor-validation-order-preparation-delivery-withdrawal-period-funds-dispatch';
    private const CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_FUNDS_DISPATCH = 'credit-card-payment-order-confirmation-vendor-validation-order-preparation-funds-dispatch';
    private const CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD_FUNDS_DISPATCH = 'credit-card-payment-order-confirmation-vendor-validation-order-preparation-withdrawal-period-funds-dispatch';
    private const CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD = 'credit-card-payment-order-confirmation-vendor-validation-order-preparation-delivery-withdrawal-period';
    private const CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD = 'credit-card-payment-order-confirmation-vendor-validation-order-preparation-withdrawal-period';
    private const PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WAIT_PAYMENT_DEFERMENT_WITHDRAWAL_PERIOD_FUNDS_DISPATCH = 'payment-deferment-authorization-order-confirmation-order-commitment-vendor-validation-order-preparation-delivery-wait-payment-deferment-withdrawal-period-funds-dispatch';
    private const PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_WAIT_PAYMENT_DEFERMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD_FUNDS_DISPATCH = 'payment-deferment-authorization-order-confirmation-order-commitment-wait-payment-deferment-vendor-validation-order-preparation-delivery-withdrawal-period-funds-dispatch';
    private const PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_WAIT_PAYMENT_DEFERMENT_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD_FUNDS_DISPATCH = 'payment-deferment-authorization-order-confirmation-order-commitment-wait-payment-deferment-vendor-validation-order-preparation-withdrawal-period-funds-dispatch';
    private const ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD = 'order-confirmation-vendor-validation-order-preparation-delivery-withdrawal-period';
    private const ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD = 'order-confirmation-vendor-validation-order-preparation-withdrawal-period';
    private const ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY = 'order-confirmation-vendor-validation-order-preparation-delivery';
    private const CREDIT_CARD_PAYMENT_AUTHORIZATION_ORDER_CONFIRMATION_VENDOR_VALIDATION_CREDIT_CARD_PAYMENT_CAPTURE_ORDER_PREPARATION_DELIVERY_FUNDS_DISPATCH = 'credit-card-payment-authorization-order-confirmation-vendor-validation-credit-card-payment-capture-order-preparation-delivery-funds-dispatch';
    private const CREDIT_CARD_PAYMENT_AUTHORIZATION_ORDER_CONFIRMATION_VENDOR_VALIDATION_CREDIT_CARD_PAYMENT_CAPTURE_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD_FUNDS_DISPATCH = 'credit-card-payment-authorization-order-confirmation-vendor-validation-credit-card-payment-capture-order-preparation-delivery-withdrawal-period-funds-dispatch';
    private const CREDIT_CARD_PAYMENT_AUTHORIZATION_ORDER_CONFIRMATION_VENDOR_VALIDATION_CREDIT_CARD_PAYMENT_CAPTURE_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD = 'credit-card-payment-authorization-order-confirmation-vendor-validation-credit-card-payment-capture-order-preparation-delivery-withdrawal-period';
    private const CREDIT_CARD_PAYMENT_AUTHORIZATION_ORDER_CONFIRMATION_VENDOR_VALIDATION_CREDIT_CARD_PAYMENT_CAPTURE_ORDER_PREPARATION_FUNDS_DISPATCH = 'credit-card-payment-authorization-order-confirmation-vendor-validation-credit-card-payment-capture-order-preparation-funds-dispatch';
    private const CREDIT_CARD_PAYMENT_AUTHORIZATION_ORDER_CONFIRMATION_VENDOR_VALIDATION_CREDIT_CARD_PAYMENT_CAPTURE_ORDER_PREPARATION_WITHDRAWAL_PERIOD_FUNDS_DISPATCH = 'credit-card-payment-authorization-order-confirmation-vendor-validation-credit-card-payment-capture-order-preparation-withdrawal-period-funds-dispatch';
    private const CREDIT_CARD_PAYMENT_AUTHORIZATION_ORDER_CONFIRMATION_VENDOR_VALIDATION_CREDIT_CARD_PAYMENT_CAPTURE_ORDER_PREPARATION_WITHDRAWAL_PERIOD = 'credit-card-payment-authorization-order-confirmation-vendor-validation-credit-card-payment-capture-order-preparation-withdrawal-period';

    // Workflow SEPA deferment without delivery LemonWay
    private const PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD_WAIT_PAYMENT_DEFERMENT_FUNDS_DISPATCH = 'payment-deferment-autorization-order-confirmation-order-commitment-vendor-validation-order-preparation-withdrawal-period-wait-payment-deferment-funds-dispatch';

    // Paiement manuel, "de main à la main", géré en dehors de la marketplace
    // nécessitant donc une action manuelle de l'admin pour indiquer que le
    // client a payé.
    private const ORDER_CONFIRMATION_MANUAL_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY = 'order-confirmation-manual-payment-vendor-validation-order-preparation-delivery';
    private const ORDER_CONFIRMATION_MANUAL_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD = 'order-confirmation-manual-payment-vendor-validation-order-preparation-withdrawal-period';
    private const ORDER_CONFIRMATION_MANUAL_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD = 'order-confirmation-manual-payment-vendor-validation-order-preparation-delivery-withdrawal_period';

    // Workflow pour renew les subscriptions
    private const CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_WITHDRAWAL_PERIOD_FUNDS_DISPATCH = 'credit-card-payment-order-confirmation-withdrawal-period-funds-dispatch';

    // Workflow SEPA deferment without delivery
    private const PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_VENDOR_VALIDATION_ORDER_PREPARATION_WAIT_PAYMENT_DEFERMENT_WITHDRAWAL_PERIOD_FUNDS_DISPATCH = 'payment-deferment-autorization-order-confirmation-order-commitment-vendor-validation-order-preparation-wait-payment-deferment-withdrawal-period-funds-dispatch';

    // Fonction "en dur" retournant le namespace associée.
    // Cela permet de ne pas stocker des namespaces dans la commande.
    public function getClassName(): string
    {
        switch ($this->getValue()) {
            case self::ORDER_CONFIRMATION_MANUAL_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD:
                return Workflow\OrderConfirmationManualPaymentVendorValidationOrderPreparationDeliveryWithdrawalPeriod::class;
            case static::ORDER_CONFIRMATION_MANUAL_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY:
                return Workflow\OrderConfirmationManualPaymentVendorValidationOrderPreparationDelivery::class;
            case static::ORDER_CONFIRMATION_MANUAL_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD:
                return Workflow\OrderConfirmationManualPaymentVendorValidationOrderPreparationWithdrawalPeriod::class;
            case static::ORDER_CONFIRMATION_BANK_TRANSFER_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_FUNDS_DISPATCH:
                return Workflow\OrderConfirmationBankTransferPaymentVendorValidationOrderPreparationDeliveryFundsDispatch::class;
            case static::ORDER_CONFIRMATION_BANK_TRANSFER_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD_FUNDS_DISPATCH:
                return Workflow\OrderConfirmationBankTransferPaymentVendorValidationOrderPreparationDeliveryWithdrawalPeriodFundsDispatch::class;
            case static::ORDER_CONFIRMATION_BANK_TRANSFER_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_FUNDS_DISPATCH:
                return Workflow\OrderConfirmationBankTransferPaymentVendorValidationOrderPreparationFundsDispatch::class;
            case static::ORDER_CONFIRMATION_BANK_TRANSFER_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD_FUNDS_DISPATCH:
                return Workflow\OrderConfirmationBankTransferPaymentVendorValidationOrderPreparationWithdrawalPeriodFundsDispatch::class;
            case static::CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_FUNDS_DISPATCH:
                return Workflow\CreditCardPaymentOrderConfirmationVendorValidationOrderPreparationDeliveryFundsDispatch::class;
            case static::CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD_FUNDS_DISPATCH:
                return Workflow\CreditCardPaymentOrderConfirmationVendorValidationOrderPreparationDeliveryWithdrawalPeriodFundsDispatch::class;
            case static::CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_FUNDS_DISPATCH:
                return Workflow\CreditCardPaymentOrderConfirmationVendorValidationOrderPreparationFundsDispatch::class;
            case static::CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD_FUNDS_DISPATCH:
                return Workflow\CreditCardPaymentOrderConfirmationVendorValidationOrderPreparationWithdrawalPeriodFundsDispatch::class;
            case static::CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD:
                return Workflow\CreditCardPaymentOrderConfirmationVendorValidationOrderPreparationDeliveryWithdrawalPeriod::class;
            case static::CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD:
                return Workflow\CreditCardPaymentOrderConfirmationVendorValidationOrderPreparationWithdrawalPeriod::class;
            case static::PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WAIT_PAYMENT_DEFERMENT_WITHDRAWAL_PERIOD_FUNDS_DISPATCH:
                return Workflow\PaymentDefermentAuthorizationOrderConfirmationOrderCommitmentVendorValidationOrderPreparationDeliveryWaitPaymentDefermentWithdrawalPeriodFundsDispatch::class;
            case static::PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_WAIT_PAYMENT_DEFERMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD_FUNDS_DISPATCH:
                return Workflow\PaymentDefermentAuthorizationOrderConfirmationOrderCommitmentWaitPaymentDefermentVendorValidationOrderPreparationDeliveryWithdrawalPeriodFundsDispatch::class;
            case static::PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_VENDOR_VALIDATION_ORDER_PREPARATION_WAIT_PAYMENT_DEFERMENT_WITHDRAWAL_PERIOD_FUNDS_DISPATCH:
                return Workflow\PaymentDefermentAuthorizationOrderConfirmationOrderCommitmentVendorValidationOrderPreparationWaitPaymentDefermentWithdrawalPeriodFundsDispatch::class;
            case static::PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_WAIT_PAYMENT_DEFERMENT_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD_FUNDS_DISPATCH:
                return Workflow\PaymentDefermentAuthorizationOrderConfirmationOrderCommitmentWaitPaymentDefermentVendorValidationOrderPreparationWithdrawalPeriodFundsDispatch::class;
            case static::PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD_WAIT_PAYMENT_DEFERMENT_FUNDS_DISPATCH:
                return Workflow\PaymentDefermentAuthorizationOrderConfirmationOrderCommitmentVendorValidationOrderPreparationWithdrawalPeriodWaitPaymentDefermentFundsDispatch::class;
            case static::ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD:
                return Workflow\OrderConfirmationVendorValidationOrderPreparationDeliveryWithdrawalPeriod::class;
            case static::ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD:
                return Workflow\OrderConfirmationVendorValidationOrderPreparationWithdrawalPeriod::class;
            case static::ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY:
                return Workflow\OrderConfirmationVendorValidationOrderPreparationDelivery::class;
            case static::CREDIT_CARD_PAYMENT_AUTHORIZATION_ORDER_CONFIRMATION_VENDOR_VALIDATION_CREDIT_CARD_PAYMENT_CAPTURE_ORDER_PREPARATION_DELIVERY_FUNDS_DISPATCH:
                return Workflow\CreditCardPaymentAuthorizationOrderConfirmationVendorValidationCreditCardPaymentCaptureOrderPreparationDeliveryFundsDispatch::class;
            case static::CREDIT_CARD_PAYMENT_AUTHORIZATION_ORDER_CONFIRMATION_VENDOR_VALIDATION_CREDIT_CARD_PAYMENT_CAPTURE_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD:
                return Workflow\CreditCardPaymentAuthorizationOrderConfirmationVendorValidationCreditCardPaymentCaptureOrderPreparationDeliveryWithdrawalPeriod::class;
            case static::CREDIT_CARD_PAYMENT_AUTHORIZATION_ORDER_CONFIRMATION_VENDOR_VALIDATION_CREDIT_CARD_PAYMENT_CAPTURE_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD_FUNDS_DISPATCH:
                return Workflow\CreditCardPaymentAuthorizationOrderConfirmationVendorValidationCreditCardPaymentCaptureOrderPreparationDeliveryWithdrawalPeriodFundsDispatch::class;
            case static::CREDIT_CARD_PAYMENT_AUTHORIZATION_ORDER_CONFIRMATION_VENDOR_VALIDATION_CREDIT_CARD_PAYMENT_CAPTURE_ORDER_PREPARATION_FUNDS_DISPATCH:
                return Workflow\CreditCardPaymentAuthorizationOrderConfirmationVendorValidationCreditCardPaymentCaptureOrderPreparationFundsDispatch::class;
            case static::CREDIT_CARD_PAYMENT_AUTHORIZATION_ORDER_CONFIRMATION_VENDOR_VALIDATION_CREDIT_CARD_PAYMENT_CAPTURE_ORDER_PREPARATION_WITHDRAWAL_PERIOD_FUNDS_DISPATCH:
                return Workflow\CreditCardPaymentAuthorizationOrderConfirmationVendorValidationCreditCardPaymentCaptureOrderPreparationWithdrawalPeriodFundsDispatch::class;
            case static::CREDIT_CARD_PAYMENT_AUTHORIZATION_ORDER_CONFIRMATION_VENDOR_VALIDATION_CREDIT_CARD_PAYMENT_CAPTURE_ORDER_PREPARATION_WITHDRAWAL_PERIOD:
                return Workflow\CreditCardPaymentAuthorizationOrderConfirmationVendorValidationCreditCardPaymentCaptureOrderPreparationWithdrawalPeriod::class;
            case static::CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_WITHDRAWAL_PERIOD_FUNDS_DISPATCH:
                return Workflow\CreditCardPaymentOrderConfirmationWithdrawalPeriodFundsDispatch::class;
        }

        throw new \LogicException("Class name not defined for {$this->getValue()}");
    }

    /**
     * Fonction retournant l'ensemble des noms des classes
     * correspondant à l'ensemble des constantes.
     *
     * @return string[]
     */
    public static function getClassNames(): array
    {
        $classNames = [];

        foreach (self::values() as $value) {
            $classNames[$value->getValue()] = $value->getClassName();
        }

        return $classNames;
    }

    public function getHumanVersion(): string
    {
        switch ($this->getValue()) {
            case static::ORDER_CONFIRMATION_MANUAL_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD:
                return 'Manual payment';
            case static::ORDER_CONFIRMATION_MANUAL_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY:
                return 'Manual payment without withdrawal period';
            case static::ORDER_CONFIRMATION_MANUAL_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD:
                return 'Manual payment without delivery';
            case static::ORDER_CONFIRMATION_BANK_TRANSFER_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_FUNDS_DISPATCH:
                return 'Bank transfer without withdrawal period';
            case static::ORDER_CONFIRMATION_BANK_TRANSFER_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD_FUNDS_DISPATCH:
                return 'Bank transfer';
            case static::ORDER_CONFIRMATION_BANK_TRANSFER_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_FUNDS_DISPATCH:
                return 'Bank transfer without delivery nor withdrawal period';
            case static::ORDER_CONFIRMATION_BANK_TRANSFER_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD_FUNDS_DISPATCH:
                return 'Bank transfer without delivery';
            case static::CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_FUNDS_DISPATCH:
                return 'Credit Card without withdrawal period';
            case static::CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD_FUNDS_DISPATCH:
                return 'Credit Card';
            case static::CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_FUNDS_DISPATCH:
                return 'Credit Card without delivery nor withdrawal period';
            case static::CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD_FUNDS_DISPATCH:
                return 'Credit card without delivery';
            case static::CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD:
                return 'SMoney';
            case static::CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD:
                return 'SMoney without delivery';
            case static::PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WAIT_PAYMENT_DEFERMENT_WITHDRAWAL_PERIOD_FUNDS_DISPATCH:
                return 'Payment deferment';
            case static::PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_WAIT_PAYMENT_DEFERMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD_FUNDS_DISPATCH:
                return 'Direct debit';
            case static::PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_WAIT_PAYMENT_DEFERMENT_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD_FUNDS_DISPATCH:
                return 'Direct debit without delivery';
            case static::ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD:
                return 'Order without payment';
            case static::ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD:
                return 'Order without payment and without delivery';
            case static::ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY:
                return 'Order without payment and without withdrawal period';
            case static::CREDIT_CARD_PAYMENT_AUTHORIZATION_ORDER_CONFIRMATION_VENDOR_VALIDATION_CREDIT_CARD_PAYMENT_CAPTURE_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD_FUNDS_DISPATCH:
                return 'Credit Card Authorization';
            case static::CREDIT_CARD_PAYMENT_AUTHORIZATION_ORDER_CONFIRMATION_VENDOR_VALIDATION_CREDIT_CARD_PAYMENT_CAPTURE_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD:
                return 'Credit Card Authorization without funds dispatch';
            case static::CREDIT_CARD_PAYMENT_AUTHORIZATION_ORDER_CONFIRMATION_VENDOR_VALIDATION_CREDIT_CARD_PAYMENT_CAPTURE_ORDER_PREPARATION_DELIVERY_FUNDS_DISPATCH:
                return 'Credit Card Authorization without withdrawal period';
            case static::CREDIT_CARD_PAYMENT_AUTHORIZATION_ORDER_CONFIRMATION_VENDOR_VALIDATION_CREDIT_CARD_PAYMENT_CAPTURE_ORDER_PREPARATION_FUNDS_DISPATCH:
                return 'Credit Card Authorization without delivery and without withdrawal period';
            case static::CREDIT_CARD_PAYMENT_AUTHORIZATION_ORDER_CONFIRMATION_VENDOR_VALIDATION_CREDIT_CARD_PAYMENT_CAPTURE_ORDER_PREPARATION_WITHDRAWAL_PERIOD_FUNDS_DISPATCH:
                return 'Credit Card Authorization without delivery';
            case static::CREDIT_CARD_PAYMENT_AUTHORIZATION_ORDER_CONFIRMATION_VENDOR_VALIDATION_CREDIT_CARD_PAYMENT_CAPTURE_ORDER_PREPARATION_WITHDRAWAL_PERIOD:
                return 'Credit Card Authorization without delivery and without funds dispatch';
            case static::CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_WITHDRAWAL_PERIOD_FUNDS_DISPATCH:
                return 'Renew a subscription';
            case static::PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_VENDOR_VALIDATION_ORDER_PREPARATION_WAIT_PAYMENT_DEFERMENT_WITHDRAWAL_PERIOD_FUNDS_DISPATCH:
                return 'SEPA deferment without delivery';
            case static::PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD_WAIT_PAYMENT_DEFERMENT_FUNDS_DISPATCH:
                return 'SEPA deferment without delivery LemonWay';
        }
    }

    public function contains(ModuleName $moduleName): bool
    {
        return preg_match('#' . $moduleName->getValue() . '#', $this->getValue()) > 0;
    }
}
