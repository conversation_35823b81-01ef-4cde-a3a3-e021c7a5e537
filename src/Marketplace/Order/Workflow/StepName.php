<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow;

use MyCLabs\Enum\Enum;

/**
 * @method static StepName PENDING_ORDER_CONFIRMATION()
 * @method static StepName PENDING_ORDER_COMMITMENT()
 * @method static StepName PENDING_MANUAL_PAYMENT()
 * @method static StepName PENDING_REDIRECTION_TO_PAYMENT_PROCESSOR()
 * @method static StepName PENDING_BANK_VALIDATION()
 * @method static StepName PENDING_AUTHORIZATION()
 * @method static StepName PENDING_VENDOR_VALIDATION()
 * @method static StepName PENDING_VENDOR_PREPARATION_END()
 * @method static StepName PENDING_INVOICE()
 * @method static StepName PENDING_SHIPPING()
 * @method static StepName PENDING_DELIVERY()
 * @method static StepName PENDING_WITHDRAWAL_PERIOD_END()
 * @method static StepName PENDING_FUNDS_DISPATCH()
 * @method static StepName PENDING_BANK_CAPTURE()
 *
 * Remarques :
 * - Le step 'PENDING_REDIRECTION_TO_PAYMENT_PROCESSOR' est utilisé :
 *   * lors de la redirection de l'utilisateur vers le PSP,
 *   * lors de l'envoi d'une demande de transaction au PSP (ex: paiement à échéance par prélèvement).
 */
class StepName extends Enum
{
    private const PENDING_ORDER_CONFIRMATION = 'pending-order-confirmation';
    private const PENDING_ORDER_COMMITMENT = 'pending-order-commitment';
    private const PENDING_MANUAL_PAYMENT = 'pending-manual-payment';
    private const PENDING_REDIRECTION_TO_PAYMENT_PROCESSOR = 'pending-redirection-to-payment-processor';
    private const PENDING_BANK_VALIDATION = 'pending-bank-validation';
    private const PENDING_AUTHORIZATION = 'pending-authorization';
    private const PENDING_VENDOR_VALIDATION = 'pending-vendor-validation';
    private const PENDING_VENDOR_PREPARATION_END = 'pending-vendor-preparation-end';
    private const PENDING_INVOICE = 'pending-invoice';
    private const PENDING_SHIPPING = 'pending-shipping';
    private const PENDING_DELIVERY = 'pending-delivery';
    private const PENDING_WITHDRAWAL_PERIOD_END = 'pending-withdrawal-period-end';
    private const PENDING_FUNDS_DISPATCH = 'pending-funds-dispatch';
    private const PENDING_BANK_CAPTURE = 'pending-bank-capture';
}
