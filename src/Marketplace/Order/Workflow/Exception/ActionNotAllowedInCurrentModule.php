<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow\Exception;

use Wizacha\Marketplace\Order\Workflow\ModuleName;

class ActionNotAllowedInCurrentModule extends \Exception implements Exception
{
    /**
     * @param ModuleName $currentModuleName
     * @param ModuleName[] $allowedModuleNames
     */
    public function __construct(ModuleName $currentModuleName, array $allowedModuleNames)
    {
        $message = sprintf(
            "Action not allowed outside modules %s : %s provided.",
            implode(', ', array_map(function (ModuleName $moduleName) {
                return $moduleName->getValue();
            }, $allowedModuleNames)),
            $currentModuleName
        );

        parent::__construct($message);
    }
}
