<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow;

/**
 * Définit l'avancement du module du workflow :
 * - statut du workflow,
 * - nom du module courant,
 * - nom du step courant (dans le module).
 *
 * Il est obligatoire d'utiliser les fonctions statiques pour instancier un objet.
 */
class Progress
{
    /** @var Status */
    private $status;

    /** @var ModuleName */
    private $moduleName;

    /** @var StepName */
    private $stepName;

    public static function processing(ModuleName $moduleName, StepName $stepName): self
    {
        return new self(Status::PROCESSING(), $moduleName, $stepName);
    }

    public static function completed(ModuleName $moduleName, StepName $stepName): self
    {
        return new self(Status::COMPLETED(), $moduleName, $stepName);
    }

    public static function failed(ModuleName $moduleName, StepName $stepName): self
    {
        return new self(Status::FAILED(), $moduleName, $stepName);
    }

    public function isProcessing(): bool
    {
        return $this->status->equals(Status::PROCESSING());
    }

    public function isCompleted(): bool
    {
        return $this->status->equals(Status::COMPLETED());
    }

    public function hasFailed(): bool
    {
        return $this->status->equals(Status::FAILED());
    }

    public function getModuleName(): ModuleName
    {
        return $this->moduleName;
    }

    public function getStepName(): StepName
    {
        return $this->stepName;
    }

    public function getStatus(): Status
    {
        return $this->status;
    }

    public function getTranslationKey(): string
    {
        return str_replace(
            '-',
            '_',
            "workflow_{$this->getModuleName()}_{$this->getStepName()}_{$this->getStatus()}"
        );
    }

    protected function __construct(
        Status $status,
        ModuleName $moduleName,
        StepName $stepName
    ) {
        $this->status = $status;
        $this->moduleName = $moduleName;
        $this->stepName = $stepName;
    }
}
