<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow;

use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Workflow\Exception\NoAdaptedWorkflow;
use Wizacha\Marketplace\Order\Workflow\Unfolding\WorkflowUnfolding;
use Doctrine\DBAL\Connection;

class WorkflowService
{
    /**
     * @var Connection
     */
    private $db;

    /**
     * @var CompanyService
     */
    private $companyService;

    public function __construct(Connection $db, CompanyService $companyService)
    {
        $this->db = $db;
        $this->companyService = $companyService;
    }

    public function saveWorkflowProgress(
        Order $order,
        WorkflowName $name,
        Progress $progress
    ): self {
        if (!$order->allowWorkflow($name)) {
            throw new \LogicException('It seems we are trying to change the workflow for the order');
        }

        // We don't need to update anything if the database workflow is identical
        if ((string) $order->getWorkflowName() === (string) $name
            && (string) $order->getWorkflowProgress()->getStatus() === (string) $progress->getStatus()
            && (string) $order->getWorkflowProgress()->getModuleName() === (string) $progress->getModuleName()
            && (string) $order->getWorkflowProgress()->getStepName() === (string) $progress->getStepName()
        ) {
            return $this;
        }

        // Mise à jour de la DB
        $this->db->update(
            'cscart_orders',
            [
                'workflow_name' => (string) $name,
                'workflow_status' => (string) $progress->getStatus(),
                'workflow_current_module_name' => (string) $progress->getModuleName(),
                'workflow_current_step_name' => (string) $progress->getStepName(),
            ],
            ['order_id' => $order->getId()]
        );

        if (!$order->isWorkflowSelected()) {
            $order->setWorkflowName($name);
        }

        // Mise à jour de l'objet
        $order->setWorkflowProgress($progress);

        return $this;
    }

    public function getWorkflowUnfoldingFor(Order $order): WorkflowUnfolding
    {
        return $this
            ->getWorkflow($order)
            ->progressOf($order);
    }

    public function getWorkflow(Order $order): Workflow
    {
        if ($order->isWorkflowSelected()) {
            $workflowName = $order->getWorkflowName();
        } else {
            $workflowName = $this->getAdaptedWorkflowName($order);
        }

        $class = $workflowName->getClassName();

        return $class::load($this);
    }

    protected function getAdaptedWorkflowName(Order $order): WorkflowName
    {
        foreach (WorkflowName::getClassNames() as $value => $workflowClassName) {
            if ($workflowClassName::isSuitableFor($order)) {
                return new WorkflowName($value);
            }
        }

        throw new NoAdaptedWorkflow('No workflow adapted to order #' . $order->getId());
    }
}
