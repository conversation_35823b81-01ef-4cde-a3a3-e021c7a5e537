<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow;

use Wizacha\Marketplace\Order\Order;

class ModuleStack
{
    /**
     * @var Module[]
     */
    private $modules = [];

    /**
     * Initialise une stack avec un premier module
     */
    public static function of(Module $start): self
    {
        return new self($start);
    }

    /**
     * @return Module[]
     */
    public function getModules(): array
    {
        return $this->modules;
    }

    public function contains(ModuleName $name): bool
    {
        foreach ($this->modules as $module) {
            if ($module->getName()->equals($name)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Ajoute une étape à notre stack
     */
    public function push(Module $module): self
    {
        // On s'assure que le module peut bien être ajouté à la stack
        $module->validate($this);

        $self = clone $this;
        $self->modules[] = $module;

        return $self;
    }

    public function process(Order $order): Progress
    {
        //on parcours toutes les étapes et on retourne le statut de la première
        //qui n'est pas finie
        $progress = array_reduce(
            $this->toProcess($order),
            function (?Progress $scannedProgress, Module $module) use ($order): ?Progress {
                // (voir le début plus bas)...alors on tourne dans le vide
                // jusqu'à la fin.
                if ($scannedProgress) {
                    return $scannedProgress;
                }

                $scannedProgress = $module->check($order);

                // Si le module est terminé, on passe au suivant.
                if ($scannedProgress->isCompleted()) {
                    return null;
                }

                // Sinon on retourne le progress... (voir la suite plus haut)
                return $scannedProgress;
            }
        );

        //si le array_reduce ne retourne rien c'est soit qu'il n'y a plus d'étape
        //à vérifier soit que toutes celles qu'on devait vérifier ont indiquées
        //qu'elles sont finies
        $lastModule = end($this->modules);
        $steps = $lastModule->getSteps();

        return $progress ?? Progress::completed(
            $lastModule->getName(),
            end($steps)
        );
    }

    /**
     * Le constructeur n'est pas accessible : il faut obligatoirement utiliser
     * les fonctions statiques pour initialiser un objet.
     */
    protected function __construct(Module $start)
    {
        $start->validate($this);

        $this->modules[] = $start;
    }

    /**
     * Récupère les étapes qu'on doit vérifier, en bypassant celles qu'on sait
     * qu'on a déjà vérifié en se basant sur l'état stocké dans la commande
     */
    private function toProcess(Order $order): array
    {
        //soit le premier à effectué est le premier de la stack soit le dernier
        //qui a été effectué à la dernière invocation
        /** @var Module $firstModule */
        $firstModule = reset($this->modules);
        $firstToProcess = $firstModule->getName();

        if ($order->isWorkflowSelected()) {
            $firstToProcess = $order->getWorkflowProgress()->getModuleName();
        }

        //on récupère tous les modules après le premier à effectuer
        $modules = array_reduce(
            $this->modules,
            function (array $modules, Module $module) use ($firstToProcess): array {
                if (\count($modules) || $module->getName()->equals($firstToProcess)) {
                    $modules[] = $module;
                }

                return $modules;
            },
            []
        );

        if (empty($modules)) {
            throw new \LogicException("Module $firstToProcess not found in this workflow");
        }

        return $modules;
    }
}
