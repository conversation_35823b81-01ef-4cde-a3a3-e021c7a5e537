<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow\Module;

use Wizacha\Marketplace\Order\Workflow\Module;
use W<PERSON>cha\Marketplace\Order\Workflow\Progress;
use Wizacha\Marketplace\Order\Workflow\ModuleName;
use Wizacha\Marketplace\Order\Workflow\StepName;
use Wizacha\Marketplace\Order\Order;

class FundsDispatch extends Module
{
    /**
     * {@inheritdoc}
     */
    public function getName(): ModuleName
    {
        return ModuleName::FUNDS_DISPATCH();
    }

    /**
     * {@inheritdoc}
     */
    public function getSteps(): array
    {
        return [
            StepName::PENDING_FUNDS_DISPATCH(),
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function check(Order $order): Progress
    {
        if ($this->dispatchSucceed($order)) {
            return Progress::completed($this->getName(), StepName::PENDING_FUNDS_DISPATCH());
        }

        if ($this->dispatchFailed($order)) {
            return Progress::failed($this->getName(), StepName::PENDING_FUNDS_DISPATCH());
        }

        return Progress::processing($this->getName(), StepName::PENDING_FUNDS_DISPATCH());
    }

    /**
     * Fonctions internes de vérification métier (dans les objets, la DB, ec.)
     * utilisées par la fonction 'check' pour décider de l'état du module.
     */
    protected function dispatchSucceed(Order $order): bool
    {
        return $order->isFundsDispatched() === true;
    }

    protected function dispatchFailed(Order $order): bool
    {
        return $order->isFundsDispatched() === false;
    }
}
