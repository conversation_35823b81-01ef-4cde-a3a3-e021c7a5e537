<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow\Module;

use Wizacha\Marketplace\Order\Workflow\Module;
use Wizacha\Marketplace\Order\Workflow\Progress;
use Wizacha\Marketplace\Order\Workflow\ModuleName;
use Wizacha\Marketplace\Order\Workflow\StepName;
use Wizacha\Marketplace\Order\Order;

class PaymentDefermentAuthorization extends Module
{
    /**
     * {@inheritdoc}
     */
    public function getName(): ModuleName
    {
        return ModuleName::PAYMENT_DEFERMENT_AUTHORIZATION();
    }

    /**
     * {@inheritdoc}
     */
    public function getSteps(): array
    {
        return [
            StepName::PENDING_AUTHORIZATION(),
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function check(Order $order): Progress
    {
        if ($order->getPayment()->isDefermentAuthorized()) {
            return Progress::completed($this->getName(), StepName::PENDING_AUTHORIZATION());
        }

        if ($order->getPayment()->isDefermentRefused()) {
            return Progress::failed($this->getName(), StepName::PENDING_AUTHORIZATION());
        }

        return Progress::processing($this->getName(), StepName::PENDING_AUTHORIZATION());
    }
}
