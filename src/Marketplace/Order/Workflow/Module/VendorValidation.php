<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow\Module;

use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Workflow\Module;
use Wizacha\Marketplace\Order\Workflow\ModuleName;
use Wizacha\Marketplace\Order\Workflow\StepName;
use Wizacha\Marketplace\Order\Workflow\Progress;

class VendorValidation extends Module
{
    /**
     * @inheritdoc
     */
    public function getName(): ModuleName
    {
        return ModuleName::ORDER_VALIDATION();
    }

    /**
     * {@inheritdoc}
     */
    public function getSteps(): array
    {
        return [
            StepName::PENDING_VENDOR_VALIDATION(),
        ];
    }

    /**
     * @inheritdoc
     */
    public function check(Order $order): Progress
    {
        if ($this->isValidated($order)) {
            return Progress::completed($this->getName(), StepName::PENDING_VENDOR_VALIDATION());
        }

        if ($this->isDeclined($order)) {
            return Progress::failed($this->getName(), StepName::PENDING_VENDOR_VALIDATION());
        }

        return Progress::processing($this->getName(), StepName::PENDING_VENDOR_VALIDATION());
    }

    /**
     * Fonctions internes de vérification métier (dans les objets, la DB, ec.)
     * utilisées par la fonction 'check' pour décider de l'état du module.
     */
    protected function isValidated(Order $order): bool
    {
        return $order->isAcceptedByVendor() === true;
    }

    protected function isDeclined(Order $order): bool
    {
        return $order->isAcceptedByVendor() === false;
    }
}
