<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow\Module;

use Wizacha\Marketplace\Order\Workflow\Module;
use W<PERSON>cha\Marketplace\Order\Workflow\Progress;
use Wizacha\Marketplace\Order\Workflow\ModuleName;
use Wizacha\Marketplace\Order\Workflow\ModuleStack;
use Wizacha\Marketplace\Order\Workflow\StepName;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderService;

class Delivery extends Module
{
    /**
     * {@inheritdoc}
     */
    public function getName(): ModuleName
    {
        return ModuleName::DELIVERY();
    }

    /**
     * {@inheritdoc}
     */
    public function getSteps(): array
    {
        return [
            StepName::PENDING_DELIVERY(),
        ];
    }

    /**
     * {@inheridoc}
     */
    public function validate(ModuleStack $stack): void
    {
        if ($stack->contains(ModuleName::ORDER_PREPARATION())) {
            return;
        }

        throw new \LogicException(sprintf(
            'Missing %s module',
            ModuleName::ORDER_PREPARATION()
        ));
    }

    /**
     * {@inheritdoc}
     */
    public function check(Order $order): Progress
    {
        if (!$order->hasDelivery()) {
            return Progress::processing($this->getName(), StepName::PENDING_DELIVERY());
        }

        if ($order->getDelivery()->isDelivered()) {
            return Progress::completed($this->getName(), StepName::PENDING_DELIVERY());
        }

        return Progress::failed($this->getName(), StepName::PENDING_DELIVERY());
    }
}
