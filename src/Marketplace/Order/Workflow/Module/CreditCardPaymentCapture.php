<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow\Module;

use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Workflow\Module;
use Wizacha\Marketplace\Order\Workflow\ModuleName;
use Wizacha\Marketplace\Order\Workflow\Progress;
use Wizacha\Marketplace\Order\Workflow\StepName;

class CreditCardPaymentCapture extends Module
{
    public function getName(): ModuleName
    {
        return ModuleName::CREDIT_CARD_PAYMENT_CAPTURE();
    }

    /**
     * @return StepName[]
     */
    public function getSteps(): array
    {
        return [
            StepName::PENDING_BANK_CAPTURE(),
        ];
    }

    public function check(Order $order): Progress
    {
        if ($order->getPayment()->isCreditCardCapture() === true) {
            return Progress::completed($this->getName(), StepName::PENDING_BANK_CAPTURE());
        }

        if ($order->getPayment()->isCreditCardCapture() === false) {
            return Progress::failed($this->getName(), StepName::PENDING_BANK_CAPTURE());
        }

        return Progress::processing($this->getName(), StepName::PENDING_BANK_CAPTURE());
    }
}
