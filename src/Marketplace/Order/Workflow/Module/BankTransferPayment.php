<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow\Module;

use Wizacha\Marketplace\Order\Workflow\Module;
use Wizacha\Marketplace\Order\Workflow\Progress;
use Wizacha\Marketplace\Order\Workflow\ModuleName;
use Wizacha\Marketplace\Order\Workflow\StepName;
use Wizacha\Marketplace\Order\Order;

class BankTransferPayment extends Module
{
    /**
     * {@inheritdoc}
     */
    public function getName(): ModuleName
    {
        return ModuleName::BANK_TRANSFER_PAYMENT();
    }

    /**
     * {@inheritdoc}
     */
    public function getSteps(): array
    {
        return [
            StepName::PENDING_REDIRECTION_TO_PAYMENT_PROCESSOR(),
            StepName::PENDING_BANK_VALIDATION(),
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function check(Order $order): Progress
    {
        if ($order->getPayment()->hasFailed()) {
            return Progress::failed($this->getName(), StepName::PENDING_BANK_VALIDATION());
        }

        if ($order->getPayment()->isCompleted()) {
            return Progress::completed($this->getName(), StepName::PENDING_BANK_VALIDATION());
        }

        if (!$order->paymentInitiatedThroughPaymentProcessor()) {
            return Progress::processing($this->getName(), StepName::PENDING_REDIRECTION_TO_PAYMENT_PROCESSOR());
        }

        return Progress::processing($this->getName(), StepName::PENDING_BANK_VALIDATION());
    }
}
