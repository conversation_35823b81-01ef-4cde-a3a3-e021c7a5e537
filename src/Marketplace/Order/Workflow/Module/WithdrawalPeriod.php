<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow\Module;

use W<PERSON>cha\Marketplace\Order\Workflow\Module;
use W<PERSON>cha\Marketplace\Order\Workflow\Progress;
use Wizacha\Marketplace\Order\Workflow\ModuleName;
use Wizacha\Marketplace\Order\Workflow\StepName;
use Wizacha\Marketplace\Order\Order;

class WithdrawalPeriod extends Module
{
    /**
     * {@inheritdoc}
     */
    public function getName(): ModuleName
    {
        return ModuleName::WITHDRAWAL_PERIOD();
    }

    /**
     * {@inheritdoc}
     */
    public function getSteps(): array
    {
        return [
            StepName::PENDING_WITHDRAWAL_PERIOD_END(),
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function check(Order $order): Progress
    {
        if (!$order->isWithdrawalPeriodOver()) {
            return Progress::processing($this->getName(), StepName::PENDING_WITHDRAWAL_PERIOD_END());
        }

        // TODO: vérifier qu'il n'y a pas eu de demande de retour

        return Progress::completed($this->getName(), StepName::PENDING_WITHDRAWAL_PERIOD_END());
    }
}
