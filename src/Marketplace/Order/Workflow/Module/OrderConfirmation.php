<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow\Module;

use W<PERSON>cha\Marketplace\Order\Workflow\Module;
use W<PERSON>cha\Marketplace\Order\Workflow\Progress;
use Wizacha\Marketplace\Order\Workflow\ModuleName;
use Wizacha\Marketplace\Order\Workflow\StepName;
use Wizacha\Marketplace\Order\Order;

class OrderConfirmation extends Module
{
    /**
     * {@inheritdoc}
     */
    public function getName(): ModuleName
    {
        return ModuleName::ORDER_CONFIRMATION();
    }

    /**
     * {@inheritdoc}
     */
    public function getSteps(): array
    {
        return [
            StepName::PENDING_ORDER_CONFIRMATION(),
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function check(Order $order): Progress
    {
        if (!$order->isConfirmed()) {
            return Progress::processing($this->getName(), StepName::PENDING_ORDER_CONFIRMATION());
        }

        return Progress::completed($this->getName(), StepName::PENDING_ORDER_CONFIRMATION());
    }
}
