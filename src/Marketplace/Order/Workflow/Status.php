<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow;

use MyCLabs\Enum\Enum;

/**
 * @method static Status PROCESSING()
 * @method static Status FAILED()
 * @method static Status COMPLETED()
 * @method static Status NOT_REACHED_YET()
 */
class Status extends Enum
{
    // Etats globaux correspondant à toutes les entités utilisées par le système
    // des workflow.
    public const PROCESSING = 'processing';
    public const FAILED = 'failed';
    public const COMPLETED = 'completed';

    // Etat particulier, utilisé par les modules mais pas par les workflow.
    public const NOT_REACHED_YET = 'not-reached-yet';
}
