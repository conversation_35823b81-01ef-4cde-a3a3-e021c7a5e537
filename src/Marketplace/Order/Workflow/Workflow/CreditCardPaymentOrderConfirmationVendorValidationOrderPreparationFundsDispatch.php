<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow\Workflow;

use Wizacha\Marketplace\Company\Company;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Workflow\Module\CreditCardPayment;
use Wizacha\Marketplace\Order\Workflow\Module\FundsDispatch;
use Wizacha\Marketplace\Order\Workflow\Module\OrderConfirmation;
use Wizacha\Marketplace\Order\Workflow\Module\OrderPreparation;
use Wizacha\Marketplace\Order\Workflow\Module\VendorValidation;
use Wizacha\Marketplace\Order\Workflow\ModuleStack;
use Wizacha\Marketplace\Order\Workflow\Workflow;
use Wizacha\Marketplace\Order\Workflow\WorkflowName;
use Wizacha\Marketplace\Order\Workflow\WorkflowService;
use Wizacha\Marketplace\Payment\PaymentType;

/**
 * Credit Card without delivery nor withdrawal period
 *
 * @see \Wizacha\Marketplace\Order\Workflow\WorkflowName
 */
class CreditCardPaymentOrderConfirmationVendorValidationOrderPreparationFundsDispatch extends Workflow
{
    public static function load(WorkflowService $workflowService): Workflow
    {
        return new self(
            WorkflowName::CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_FUNDS_DISPATCH(),
            ModuleStack::of(new CreditCardPayment())
                ->push(new OrderConfirmation())
                ->push(new VendorValidation())
                ->push(new OrderPreparation())
                ->push(new FundsDispatch()),
            $workflowService
        );
    }

    public static function isSuitableFor(Order $order): bool
    {
        if ($order->getTotal()->isZero()) {
            return false;
        }

        // Normalement on n'autorise pas ce workflow pour le B2C car il n'a pas
        // de délai de rétractation, hors dans le cas d'une vente de service
        // il n'y en a pas
        if ($order->hasWithdrawalPeriod() && !container()->getParameter('feature.marketplace_only_sell_services')) {
            return false;
        }

        if (!$order->isC2C()) {
            return false;
        }

        if ($order->getPayment()->isOfType(PaymentType::CREDIT_CARD()) === false) {
            return false;
        }

        if (\is_string($order->getSubscriptionId())) {
            return false;
        }

        if (!$order->hasDeliveryType()) {
            return true;
        }

        return $order->isHandDelivered();
    }
}
