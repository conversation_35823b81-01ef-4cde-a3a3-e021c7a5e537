<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow\Workflow;

use Wizacha\Marketplace\Company\Company;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Workflow\Module\Delivery;
use Wizacha\Marketplace\Order\Workflow\Module\OrderConfirmation;
use Wizacha\Marketplace\Order\Workflow\Module\OrderPreparation;
use Wizacha\Marketplace\Order\Workflow\Module\VendorValidation;
use Wizacha\Marketplace\Order\Workflow\ModuleStack;
use Wizacha\Marketplace\Order\Workflow\Workflow;
use Wizacha\Marketplace\Order\Workflow\WorkflowName;
use Wizacha\Marketplace\Order\Workflow\WorkflowService;
use Wizacha\Marketplace\Payment\PaymentType;

/**
 * Order without payment and without withdrawal period
 *
 * @see \Wizacha\Marketplace\Order\Workflow\WorkflowName
 */
class OrderConfirmationVendorValidationOrderPreparationDelivery extends Workflow
{
    public static function load(WorkflowService $workflowService): Workflow
    {
        return new self(
            WorkflowName::ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY(),
            ModuleStack::of(new OrderConfirmation())
                ->push(new VendorValidation())
                ->push(new OrderPreparation())
                ->push(new Delivery()),
            $workflowService
        );
    }

    public static function isSuitableFor(Order $order): bool
    {
        if ($order->hasWithdrawalPeriod()) {
            return false;
        }

        if (!$order->hasDeliveryType()) {
            return false;
        }

        if ($order->isHandDelivered()) {
            return false;
        }

        if (\is_string($order->getSubscriptionId())) {
            return false;
        }

        return $order->getTotal()->isZero() || $order->getPayment()->isOfType(PaymentType::NONE());
    }
}
