<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow\Workflow;

use Wizacha\Marketplace\Company\Company;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Workflow\Module\CreditCardPayment;
use Wizacha\Marketplace\Order\Workflow\Module\FundsDispatch;
use Wizacha\Marketplace\Order\Workflow\Module\OrderConfirmation;
use Wizacha\Marketplace\Order\Workflow\ModuleStack;
use Wizacha\Marketplace\Order\Workflow\Workflow;
use Wizacha\Marketplace\Order\Workflow\WorkflowName;
use Wizacha\Marketplace\Order\Workflow\WorkflowService;
use Wizacha\Marketplace\Payment\PaymentType;
use Wizacha\Marketplace\Order\Workflow\Module\WithdrawalPeriod;

class CreditCardPaymentOrderConfirmationWithdrawalPeriodFundsDispatch extends Workflow
{
    public static function load(WorkflowService $workflowService): Workflow
    {
        return new static(
            WorkflowName::CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_WITHDRAWAL_PERIOD_FUNDS_DISPATCH(),
            ModuleStack::of(new CreditCardPayment())
                ->push(new OrderConfirmation())
                ->push(new WithdrawalPeriod())
                ->push(new FundsDispatch()),
            $workflowService
        );
    }

    public static function isSuitableFor(Order $order): bool
    {
        return
            true === container()->getParameter('feature.subscription')
            && true === $order->isSubscriptionSubsequent()
            && false === $order->getTotal()->isZero()
            && $order->getPayment()->isOfType(PaymentType::CREDIT_CARD());
    }
}
