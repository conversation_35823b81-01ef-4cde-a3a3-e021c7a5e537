<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow\Workflow;

use Wizacha\Marketplace\Company\Company;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Workflow\Module\CreditCardPaymentAuthorization;
use Wizacha\Marketplace\Order\Workflow\Module\CreditCardPaymentCapture;
use Wizacha\Marketplace\Order\Workflow\Module\OrderConfirmation;
use Wizacha\Marketplace\Order\Workflow\Module\OrderPreparation;
use Wizacha\Marketplace\Order\Workflow\Module\VendorValidation;
use Wizacha\Marketplace\Order\Workflow\Module\WithdrawalPeriod;
use Wizacha\Marketplace\Order\Workflow\ModuleStack;
use Wizacha\Marketplace\Order\Workflow\Workflow;
use Wizacha\Marketplace\Order\Workflow\WorkflowName;
use Wizacha\Marketplace\Order\Workflow\WorkflowService;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\PaymentType;

/**
 * SMoney without delivery
 *
 * @see \Wizacha\Marketplace\Order\Workflow\WorkflowName
 */
class CreditCardPaymentAuthorizationOrderConfirmationVendorValidationCreditCardPaymentCaptureOrderPreparationWithdrawalPeriod extends Workflow
{
    public static function load(WorkflowService $workflowService): Workflow
    {
        return new static(
            WorkflowName::CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD(),
            ModuleStack::of(new CreditCardPaymentAuthorization())
                ->push(new OrderConfirmation())
                ->push(new VendorValidation())
                ->push(new CreditCardPaymentCapture())
                ->push(new OrderPreparation())
                ->push(new WithdrawalPeriod()),
            $workflowService
        );
    }

    public static function isSuitableFor(Order $order): bool
    {
        if ($order->getTotal()->isZero()
            || $order->hasWithdrawalPeriod() === false
            || $order->getPayment()->hasProcessorName(PaymentProcessorName::SMONEY()) === false
            || $order->getPayment()->isOfType(PaymentType::CREDIT_CARD_CAPTURE()) === false
            || \is_string($order->getSubscriptionId())
        ) {
            return false;
        }

        if ($order->hasDeliveryType() === false) {
            return true;
        }

        return $order->isHandDelivered();
    }
}
