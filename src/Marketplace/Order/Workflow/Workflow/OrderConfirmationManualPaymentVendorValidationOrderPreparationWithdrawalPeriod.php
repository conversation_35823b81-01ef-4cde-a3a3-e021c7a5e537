<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow\Workflow;

use Wizacha\Marketplace\Company\Company;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Workflow\Module\ManualPayment;
use Wizacha\Marketplace\Order\Workflow\Module\OrderConfirmation;
use Wizacha\Marketplace\Order\Workflow\Module\OrderPreparation;
use Wizacha\Marketplace\Order\Workflow\Module\VendorValidation;
use Wizacha\Marketplace\Order\Workflow\Module\WithdrawalPeriod;
use Wizacha\Marketplace\Order\Workflow\ModuleStack;
use Wizacha\Marketplace\Order\Workflow\Workflow;
use Wizacha\Marketplace\Order\Workflow\WorkflowName;
use Wizacha\Marketplace\Order\Workflow\WorkflowService;
use Wizacha\Marketplace\Payment\PaymentType;

/**
 * Manual payment without delivery
 *
 * @see \Wizacha\Marketplace\Order\Workflow\WorkflowName
 */
class OrderConfirmationManualPaymentVendorValidationOrderPreparationWithdrawalPeriod extends Workflow
{
    public static function load(WorkflowService $workflowService): Workflow
    {
        return new self(
            WorkflowName::ORDER_CONFIRMATION_MANUAL_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD(),
            ModuleStack::of(new OrderConfirmation())
                ->push(new ManualPayment())
                ->push(new VendorValidation())
                ->push(new OrderPreparation())
                ->push(new WithdrawalPeriod()),
            $workflowService
        );
    }

    public static function isSuitableFor(Order $order): bool
    {
        if ($order->getTotal()->isZero()) {
            return false;
        }

        if (!$order->getPayment()->isOfType(PaymentType::MANUAL())) {
            return false;
        }

        if (\is_string($order->getSubscriptionId())) {
            return false;
        }

        if (!$order->hasDeliveryType()) {
            return true;
        }

        return $order->isHandDelivered();
    }
}
