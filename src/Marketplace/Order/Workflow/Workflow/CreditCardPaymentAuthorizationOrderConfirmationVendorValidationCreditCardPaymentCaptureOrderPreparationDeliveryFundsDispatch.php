<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow\Workflow;

use Wizacha\Marketplace\Company\Company;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Workflow\Module\CreditCardPaymentAuthorization;
use Wizacha\Marketplace\Order\Workflow\Module\CreditCardPaymentCapture;
use Wizacha\Marketplace\Order\Workflow\Module\Delivery;
use Wizacha\Marketplace\Order\Workflow\Module\FundsDispatch;
use Wizacha\Marketplace\Order\Workflow\Module\OrderConfirmation;
use Wizacha\Marketplace\Order\Workflow\Module\OrderPreparation;
use Wizacha\Marketplace\Order\Workflow\Module\VendorValidation;
use Wizacha\Marketplace\Order\Workflow\ModuleStack;
use Wizacha\Marketplace\Order\Workflow\Workflow;
use Wizacha\Marketplace\Order\Workflow\WorkflowName;
use Wizacha\Marketplace\Order\Workflow\WorkflowService;
use Wizacha\Marketplace\Payment\PaymentType;

/**
 * Credit Card with Authorization without withdrawal period
 *
 * @see \Wizacha\Marketplace\Order\Workflow\WorkflowName
 */
class CreditCardPaymentAuthorizationOrderConfirmationVendorValidationCreditCardPaymentCaptureOrderPreparationDeliveryFundsDispatch extends Workflow
{
    public static function load(WorkflowService $workflowService): Workflow
    {
        return new static(
            WorkflowName::CREDIT_CARD_PAYMENT_AUTHORIZATION_ORDER_CONFIRMATION_VENDOR_VALIDATION_CREDIT_CARD_PAYMENT_CAPTURE_ORDER_PREPARATION_DELIVERY_FUNDS_DISPATCH(),
            ModuleStack::of(new CreditCardPaymentAuthorization())
                ->push(new OrderConfirmation())
                ->push(new VendorValidation())
                ->push(new CreditCardPaymentCapture())
                ->push(new OrderPreparation())
                ->push(new Delivery())
                ->push(new FundsDispatch()),
            $workflowService
        );
    }

    public static function isSuitableFor(Order $order): bool
    {
        if ($order->getTotal()->isZero()
            || $order->hasWithdrawalPeriod()
            || container()->getParameter('feature.marketplace_only_sell_services')
            || $order->hasDeliveryType() === false
            || $order->isHandDelivered()
            || \is_string($order->getSubscriptionId())
        ) {
            return false;
        }

        return $order->getPayment()->isOfType(PaymentType::CREDIT_CARD_CAPTURE());
    }
}
