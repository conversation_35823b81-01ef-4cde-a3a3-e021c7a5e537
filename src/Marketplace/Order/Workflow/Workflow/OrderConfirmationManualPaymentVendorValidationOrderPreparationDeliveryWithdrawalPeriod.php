<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow\Workflow;

use Wizacha\Marketplace\Company\Company;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Workflow\Module\Delivery;
use Wizacha\Marketplace\Order\Workflow\Module\ManualPayment;
use Wizacha\Marketplace\Order\Workflow\Module\OrderConfirmation;
use Wizacha\Marketplace\Order\Workflow\Module\OrderPreparation;
use Wizacha\Marketplace\Order\Workflow\Module\VendorValidation;
use Wizacha\Marketplace\Order\Workflow\Module\WithdrawalPeriod;
use Wizacha\Marketplace\Order\Workflow\ModuleStack;
use Wizacha\Marketplace\Order\Workflow\Workflow;
use Wizacha\Marketplace\Order\Workflow\WorkflowName;
use Wizacha\Marketplace\Order\Workflow\WorkflowService;
use Wizacha\Marketplace\Payment\PaymentType;

/**
 * Manual payment
 *
 * @see \Wizacha\Marketplace\Order\Workflow\WorkflowName
 */
class OrderConfirmationManualPaymentVendorValidationOrderPreparationDeliveryWithdrawalPeriod extends Workflow
{
    public static function load(WorkflowService $workflowService): Workflow
    {
        return new self(
            WorkflowName::ORDER_CONFIRMATION_MANUAL_PAYMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD(),
            ModuleStack::of(new OrderConfirmation())
                ->push(new ManualPayment())
                ->push(new VendorValidation())
                ->push(new OrderPreparation())
                ->push(new Delivery())
                ->push(new WithdrawalPeriod()),
            $workflowService
        );
    }

    public static function isSuitableFor(Order $order): bool
    {
        if ($order->getTotal()->isZero()) {
            return false;
        }

        //pas de délai de livraison ni rétractation quand on vend du service
        if (container()->getParameter('feature.marketplace_only_sell_services')) {
            return false;
        }

        if (\is_string($order->getSubscriptionId())) {
            return false;
        }

        return $order->getPayment()->isOfType(PaymentType::MANUAL());
    }
}
