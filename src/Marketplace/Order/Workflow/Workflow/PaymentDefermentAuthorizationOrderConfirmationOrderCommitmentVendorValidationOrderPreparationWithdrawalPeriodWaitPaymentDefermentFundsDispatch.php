<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow\Workflow;

use Wizacha\Marketplace\Company\Company;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Workflow\Module\FundsDispatch;
use Wizacha\Marketplace\Order\Workflow\Module\OrderCommitment;
use Wizacha\Marketplace\Order\Workflow\Module\OrderConfirmation;
use Wizacha\Marketplace\Order\Workflow\Module\OrderPreparation;
use Wizacha\Marketplace\Order\Workflow\Module\PaymentDefermentAuthorization;
use Wizacha\Marketplace\Order\Workflow\Module\VendorValidation;
use Wizacha\Marketplace\Order\Workflow\Module\WaitPaymentDeferment;
use Wizacha\Marketplace\Order\Workflow\Module\WithdrawalPeriod;
use Wizacha\Marketplace\Order\Workflow\ModuleStack;
use Wizacha\Marketplace\Order\Workflow\Workflow;
use Wizacha\Marketplace\Order\Workflow\WorkflowName;
use Wizacha\Marketplace\Order\Workflow\WorkflowService;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\PaymentType;

/**
 * Payment deferment
 *
 * @see \Wizacha\Marketplace\Order\Workflow\WorkflowName
 */
class PaymentDefermentAuthorizationOrderConfirmationOrderCommitmentVendorValidationOrderPreparationWithdrawalPeriodWaitPaymentDefermentFundsDispatch extends Workflow
{
    public static function load(WorkflowService $workflowService): Workflow
    {
        return new self(
            WorkflowName::PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD_WAIT_PAYMENT_DEFERMENT_FUNDS_DISPATCH(),
            ModuleStack::of(new PaymentDefermentAuthorization())
                ->push(new OrderConfirmation())
                ->push(new OrderCommitment())
                ->push(new VendorValidation())
                ->push(new OrderPreparation())
                ->push(new WithdrawalPeriod())
                ->push(new WaitPaymentDeferment())
                ->push(new FundsDispatch()),
            $workflowService
        );
    }

    public static function isSuitableFor(Order $order): bool
    {
        if ($order->getTotal()->isZero() === true) {
            return false;
        }

        if (\is_string($order->getSubscriptionId()) === true) {
            return false;
        }

        if ($order->isHandDelivered() === false && $order->hasDelivery() === true) {
            return false;
        }

        return $order->getPayment()->hasProcessorName(PaymentProcessorName::LEMONWAY()) === true
            && $order->getPayment()->isOfType(PaymentType::PAYMENT_DEFERMENT()) === true ;
    }
}
