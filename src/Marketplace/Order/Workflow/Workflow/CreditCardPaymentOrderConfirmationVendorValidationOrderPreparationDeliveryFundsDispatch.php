<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow\Workflow;

use Wizacha\Marketplace\Company\Company;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Workflow\Module\CreditCardPayment;
use Wizacha\Marketplace\Order\Workflow\Module\Delivery;
use Wizacha\Marketplace\Order\Workflow\Module\FundsDispatch;
use Wizacha\Marketplace\Order\Workflow\Module\OrderConfirmation;
use Wizacha\Marketplace\Order\Workflow\Module\OrderPreparation;
use Wizacha\Marketplace\Order\Workflow\Module\VendorValidation;
use Wizacha\Marketplace\Order\Workflow\ModuleStack;
use Wizacha\Marketplace\Order\Workflow\Workflow;
use Wizacha\Marketplace\Order\Workflow\WorkflowName;
use Wizacha\Marketplace\Order\Workflow\WorkflowService;
use Wizacha\Marketplace\Payment\PaymentType;

/**
 * Credit Card without withdrawal period
 *
 * @see \Wizacha\Marketplace\Order\Workflow\WorkflowName
 */
class CreditCardPaymentOrderConfirmationVendorValidationOrderPreparationDeliveryFundsDispatch extends Workflow
{
    public static function load(WorkflowService $workflowService): Workflow
    {
        return new self(
            WorkflowName::CREDIT_CARD_PAYMENT_ORDER_CONFIRMATION_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_FUNDS_DISPATCH(),
            ModuleStack::of(new CreditCardPayment())
                ->push(new OrderConfirmation())
                ->push(new VendorValidation())
                ->push(new OrderPreparation())
                ->push(new Delivery())
                ->push(new FundsDispatch()),
            $workflowService
        );
    }

    public static function isSuitableFor(Order $order): bool
    {
        if ($order->getTotal()->isZero()) {
            return false;
        }

        if ($order->hasWithdrawalPeriod()) {
            return false;
        }

        //pas de délai de livraison quand on vend du service
        if (container()->getParameter('feature.marketplace_only_sell_services')) {
            return false;
        }

        if (!$order->hasDeliveryType()) {
            return false;
        }

        if ($order->isHandDelivered()) {
            return false;
        }

        if (\is_string($order->getSubscriptionId())) {
            return false;
        }

        return $order->getPayment()->isOfType(PaymentType::CREDIT_CARD());
    }
}
