<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Workflow;

use MyCLabs\Enum\Enum;
use Wizacha\Marketplace\Order\Workflow\Module\BankTransferPayment;
use Wizacha\Marketplace\Order\Workflow\Module\CreditCardPayment;
use Wizacha\Marketplace\Order\Workflow\Module\Delivery;
use Wizacha\Marketplace\Order\Workflow\Module\FundsDispatch;
use Wizacha\Marketplace\Order\Workflow\Module\ManualPayment;
use Wizacha\Marketplace\Order\Workflow\Module\OrderCommitment;
use Wizacha\Marketplace\Order\Workflow\Module\OrderPreparation;
use Wizacha\Marketplace\Order\Workflow\Module\PaymentDefermentAuthorization;
use Wizacha\Marketplace\Order\Workflow\Module\VendorValidation;
use Wizacha\Marketplace\Order\Workflow\Module\WaitPaymentDeferment;
use Wizacha\Marketplace\Order\Workflow\Module\WithdrawalPeriod;

/**
 * @method static ModuleName ORDER_CONFIRMATION()
 * @method static ModuleName ORDER_COMMITMENT()
 *
 * @method static ModuleName MANUAL_PAYMENT()
 * @method static ModuleName CREDIT_CARD_PAYMENT()
 * @method static ModuleName BANK_TRANSFER_PAYMENT()
 * @method static ModuleName PAYMENT_DEFERMENT_AUTHORIZATION()
 * @method static ModuleName WAIT_PAYMENT_DEFERMENT()
 *
 * @method static ModuleName CREDIT_CARD_PAYMENT_AUTHORIZATION()
 * @method static ModuleName CREDIT_CARD_PAYMENT_CAPTURE()
 *
 * @method static ModuleName ORDER_VALIDATION()
 *
 * @method static ModuleName ORDER_PREPARATION()
 *
 * @method static ModuleName DELIVERY()
 *
 * @method static ModuleName WITHDRAWAL_PERIOD()
 *
 * @method static ModuleName FUNDS_DISPATCH()
 */
class ModuleName extends Enum
{
    // Confirmation de la commande
    private const ORDER_CONFIRMATION = 'order-confirmation';

    // Engagement de la commande
    private const ORDER_COMMITMENT = 'order-commitment';

    // Payment manuel
    private const MANUAL_PAYMENT = 'manual-payment';
    // Payment CB (standard)
    private const CREDIT_CARD_PAYMENT = 'credit-card-payment';
    // Paiement autorisation
    private const CREDIT_CARD_PAYMENT_AUTHORIZATION = 'credit-card-payment-authorization';
    // Paiement capturé
    private const CREDIT_CARD_PAYMENT_CAPTURE = 'credit-card-payment-capture';
    // Virement bancaire
    private const BANK_TRANSFER_PAYMENT = 'bank-transfer-payment';
    // Autorisation de paiement à échéance
    private const PAYMENT_DEFERMENT_AUTHORIZATION = 'payment-deferment-authorization';
    // Attente du paiement à échéance
    private const WAIT_PAYMENT_DEFERMENT = 'wait-payment-deferment';

    // Validation vendeur
    private const ORDER_VALIDATION = 'order-validation';

    // Préparation de la commande
    private const ORDER_PREPARATION = 'order-preparation';

    // Livraison
    private const DELIVERY = 'delivery';

    // Délai de rétractation
    private const WITHDRAWAL_PERIOD = 'withdrawal-period';

    // Répartition des fonds
    private const FUNDS_DISPATCH = 'funds-dispatch';
}
