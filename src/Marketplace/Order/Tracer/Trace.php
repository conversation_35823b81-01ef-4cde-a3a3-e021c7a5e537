<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Tracer;

class Trace
{
    /**
     * @var int
     */
    private $orderId;

    /**
     * @var string
     */
    private $action;

    /**
     * @var \DateTimeImmutable
     */
    private $date;

    /**
     * @var int|null
     */
    private $userId;

    /**
     * @var string|null
     */
    private $userName;

    public function __construct(
        int $orderId,
        string $action,
        \DateTimeImmutable $date,
        ?int $userId,
        ?string $userName
    ) {
        $this->orderId = $orderId;
        $this->action = $action;
        $this->date = $date;
        $this->userId = $userId;
        $this->userName = $userName;
    }

    public function getOrderId(): int
    {
        return $this->orderId;
    }

    public function getAction(): string
    {
        return $this->action;
    }

    public function getDate(): \DateTimeImmutable
    {
        return $this->date;
    }

    public function doneAutomatically(): bool
    {
        return \is_null($this->userId);
    }

    public function doneByAUser(): bool
    {
        return \is_int($this->userId);
    }

    public function getUserId(): int
    {
        return $this->userId;
    }

    public function getUserName(): string
    {
        return $this->userName;
    }
}
