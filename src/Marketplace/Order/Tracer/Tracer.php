<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Tracer;

use Wizacha\AppBundle\Security\User\UserService;
use Wizacha\Marketplace\User\UserService as DomainUserService;
use Doctrine\DBAL\Connection;

/**
 * Permet de sauvegarder toutes les actions faites sur les commandes
 */
class Tracer
{
    /**
     * @var Connection
     */
    private $connection;

    /**
     * @var UserService
     */
    private $userService;

    /**
     * @var DomainUserService
     */
    private $domainUserService;

    public function __construct(
        Connection $connection,
        UserService $userService,
        DomainUserService $domainUserService
    ) {
        $this->connection = $connection;
        $this->userService = $userService;
        $this->domainUserService = $domainUserService;
    }

    public function trace(int $orderId, string $action): void
    {
        $userId = $this->userService->getCurrentUserId();
        $userName = $userId ? $this->domainUserService->get($userId)->getFullName() : null;

        $this->connection->insert(
            'orders_actions_traces',
            [
                'order_id' => $orderId,
                'action' => $action,
                'date' => new \DateTimeImmutable(),
                'user_id' => $userId,
                'user_name' => $userName,
            ],
            [
                'order_id' => 'integer',
                'action' => 'string',
                'date' => 'datetime',
                'user_id' => 'integer',
                'user_name' => 'string',
            ]
        );
    }

    /**
     * @return Trace[]
     */
    public function list(int $orderId): array
    {
        $traces = $this->connection->fetchAll(
            'SELECT * FROM orders_actions_traces WHERE order_id = :orderId',
            ['orderId' => $orderId]
        );

        return array_map(function (array $trace) use ($orderId): Trace {
            return new Trace(
                $orderId,
                $trace['action'],
                new \DateTimeImmutable($trace['date']),
                \is_null($trace['user_id']) ? null : (int) $trace['user_id'],
                $trace['user_name']
            );
        }, $traces);
    }

    public function delete(int $orderId, string $action): void
    {
        db_query("DELETE FROM orders_actions_traces WHERE `order_id` = ?i AND `action` = ?s", $orderId, $action);
    }
}
