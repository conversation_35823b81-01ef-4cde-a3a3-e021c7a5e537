<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order;

use Wizacha\Marketplace\Order\Exception\LogicException;

class OrderBillingNumberGenerator
{
    /** @var OrderService */
    private $orderService;

    /** @var bool */
    private $isAutomaticBillingNumberGenerationActivated;

    public function __construct(
        OrderService $orderService,
        bool $isAutomaticBillingNumberGenerationActivated
    ) {
        $this->orderService = $orderService;
        $this->isAutomaticBillingNumberGenerationActivated = $isAutomaticBillingNumberGenerationActivated;
    }

    public function generate(int $orderId): string
    {
        if (!$this->isAutomaticBillingNumberGenerationActivated) {
            throw new LogicException('Automatic billing number generation feature is not enabled.');
        }

        $order = $this->orderService->getOrder($orderId);
        $company = $order->getCompany();

        if (!$company->hasAutomaticBillingNumber()) {
            throw new LogicException('Automatic billing number generation is not enabled for the company.');
        }

        $invoicedOrdersCounter = $this->orderService->countInvoicedOrdersFromCompany($company->getId());

        $billingNumber = $company->getInitialBillingNumber() + $invoicedOrdersCounter;

        return $company->getPrefixBillingNumber() . (string) $billingNumber;
    }
}
