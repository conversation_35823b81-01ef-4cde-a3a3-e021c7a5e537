<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order;

use MyCLabs\Enum\Enum;

/**
 * @method static OrderDataType GROUP_INFO()
 * @method static OrderDataType SHIPPING_INFO()
 * @method static OrderDataType TAX_INFO()
 * @method static OrderDataType PAYMENT_INFO()
 * @method static OrderDataType COUPON_INFO()
 * @method static OrderDataType RETURN_INFO()
 * @method static OrderDataType DELIVERY_DATE_INFO()
 * @method static OrderDataType GOOGLE_CHECKOUT_INFO()
 * @method static OrderDataType KEY_CODE_INFO()
 * @method static OrderDataType TIMESTAMP_INFO()
 */
class OrderDataType extends Enum
{
    /** Products group shipping informations */
    protected const GROUP_INFO = 'G';

    /** Shipping informations */
    protected const SHIPPING_INFO = 'L';

    /** Tax informations */
    protected const TAX_INFO = 'T';

    /** Payment informations */
    protected const PAYMENT_INFO = 'P';

    /** Code promotion ? */
    protected const COUPON_INFO = 'C';

    /** Retour / Remboursement ? */
    protected const RETURN_INFO = 'H';

    /** Delivery date ? */
    protected const DELIVERY_DATE_INFO = 'V';

    /** Google checkout dead code ? */
    protected const GOOGLE_CHECKOUT_INFO = 'O';

    /** Code de retrait en main propre */
    protected const KEY_CODE_INFO = 'W';

    /** Timestamp du checkout */
    protected const TIMESTAMP_INFO = 'S';
}
