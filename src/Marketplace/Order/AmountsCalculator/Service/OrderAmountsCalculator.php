<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\AmountsCalculator\Service;

use Psr\Log\LoggerInterface;
use Wizacha\Component\Order\TaxRate;
use Wizacha\Core\Concurrent\MutexService;
use Wizacha\Marketplace\Commission\CommissionService;
use Wizacha\Marketplace\Entities\Tax as TaxEntity;
use Wizacha\Marketplace\Order\AmountsCalculator\Entity\ShippingAmounts;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderItem;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem;
use Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts;
use Wizacha\Marketplace\Order\AmountsCalculator\Repository\OrderAmountsRepository;
use Wizacha\Marketplace\Promotion\BonusTarget\BonusTarget;
use Wizacha\Marketplace\Promotion\BonusTarget\BonusTargetBasket;
use Wizacha\Marketplace\Promotion\BonusTarget\BonusTargetProductCategoryInBasket;
use Wizacha\Marketplace\Promotion\BonusTarget\BonusTargetProductInBasket;
use Wizacha\Marketplace\Promotion\BonusTarget\BonusTargetShipping;
use Wizacha\Marketplace\Promotion\Exception\PromotionNotFound;
use Wizacha\Marketplace\Promotion\PromotionRepository;
use Wizacha\Marketplace\Promotion\PromotionType;
use Wizacha\Marketplace\Tax\Repository\TaxRepository;
use Wizacha\Money\Money;
use Wizacha\Tax;

class OrderAmountsCalculator
{
    private string $legacyPivotDate;

    public const PROMOTION_BASKET_TARGET = 'basket';
    public const PROMOTION_PRODUCT_TARGET = "product_in_basket";
    public const PROMOTION_PRODUCT_CATEGORY_TARGET = "product_category_in_basket";

    private OrderAmountsRepository $orderAmountsRepository;
    private OrderService $orderService;
    private CommissionService $commissionService;
    private PromotionRepository $promotionRepository;
    private LoggerInterface $logger;
    private MutexService $mutexService;

    /** @var OrderAmounts[]  */
    private array $batchProcessingOrderAmounts;
    private int $batchProcessingSize;

    private TaxRepository $taxRepository;

    public function __construct(
        string $legacyPivotDate,
        OrderAmountsRepository $orderAmountsRepository,
        PromotionRepository $promotionRepository,
        OrderService $orderService,
        CommissionService $commissionService,
        LoggerInterface $logger,
        TaxRepository $taxRepository,
        MutexService $mutexService
    ) {
        $this->orderAmountsRepository = $orderAmountsRepository;
        $this->promotionRepository = $promotionRepository;
        $this->orderService = $orderService;
        $this->commissionService = $commissionService;
        $this->mutexService = $mutexService;

        $this->batchProcessingOrderAmounts = [];
        $this->batchProcessingSize = 1;
        $this->logger = $logger;
        $this->legacyPivotDate = $legacyPivotDate;
        $this->taxRepository = $taxRepository;
    }

    public function calculateFromOrder(Order $order): OrderAmounts
    {
        return $this->process($order);
    }

    // Recalculate an order after adjusting an item price
    public function refreshAfterAdjustment(Order $order): OrderAmounts
    {
        foreach ($order->getItems() as $orderItem) {
            $amountsItem = $orderItem->getAmountItem();
            $taxRate = $orderItem->getTaxRate();

            // Unit Prices
            $unitPriceExclTaxes = $amountsItem->getUnitPriceExclTaxes();
            $unitPriceInclTaxes = $amountsItem->getUnitPriceInclTaxes();

            // Totals
            $totalExclTaxes = $unitPriceExclTaxes->multiply($amountsItem->getQuantity());
            $totalInclTaxes = $unitPriceInclTaxes->multiply($amountsItem->getQuantity());

            // Tax Amounts
            $unitTaxAmont = $taxRate->getTaxAmountFromIncludingTaxes($unitPriceInclTaxes);
            $totalTaxAmont = $taxRate->getTaxAmountFromIncludingTaxes($totalInclTaxes);

            $amountsItem
                ->setTotalExclTaxes($totalExclTaxes)
                ->setTotalInclTaxes($totalInclTaxes)
                ->setUnitTaxAmount($unitTaxAmont)
                ->setTotalTaxAmount($totalTaxAmont)
                ->setDiscount(Money::fromVariable(0))
                ->setDiscountedUnitPriceExclTaxes($unitPriceExclTaxes)
                ->setDiscountedUnitPriceInclTaxes($unitPriceInclTaxes)
                ->setDiscountedUnitTaxAmount($unitTaxAmont)
                ->setDiscountedTotalExclTaxes($totalExclTaxes)
                ->setDiscountedTotalInclTaxes($totalInclTaxes)
                ->setDiscountedTotalTaxAmount($totalTaxAmont);
        }
        $orderAmount = $order->getOrderAmounts();

        // Totals Excl and Incl taxes calculation
        $this->subTotalsCalculation($orderAmount);

        // Discounts calculation
        $this->processDiscounts($orderAmount, $order);

        // Taxes Calculation
        $this->processTaxes($orderAmount);

        // Totals Calculation
        $this->totalsCalculations($orderAmount);

        $this->save($orderAmount);

        return $orderAmount;
    }

    /** Freeze a parent order with the sum of its children */
    public function freezeParentOrder(int $orderId): OrderAmounts
    {
        $commissionTaxId = $this->taxRepository->getCommissiontaxId();

        $orderAmount = $this->orderAmountsRepository->findOneByOrderId($orderId);
        if (null !== $orderAmount) {
            return $orderAmount;
        }

        $parentOrder = $this->orderService->getAnyOrder($orderId);
        $promotions = $this->addProductIdsAndCategoryIdsToPromotion($parentOrder);
        $orderAmount = new OrderAmounts();

        $this->hydrateFromOrderLegacy($parentOrder, $orderAmount, $promotions, $commissionTaxId);
        $this->hydrateParentOrder($parentOrder, $orderAmount);
        $this->save($orderAmount);

        return $orderAmount;
    }

    public function setBatchProcessingSize(int $batchProcessingSize): void
    {
        $this->batchProcessingSize = $batchProcessingSize;
    }

    /**
     * @param mixed[] $arrayBonus
     */
    protected function totalDiscountCalc(Money $subTotalInclTaxes, ?array $arrayBonus, int $quantity = 1): Money
    {
        if (null !== $arrayBonus) {
            if (true === \array_key_exists('0', $arrayBonus)) {
                return $this->parseDiscountBonuses($subTotalInclTaxes, $arrayBonus, $quantity);
            }

            return $this->parseDiscountLegacyBonuses($subTotalInclTaxes, $arrayBonus);
        }

        return new Money(0);
    }

    /**
     * On old orders, discounts have only 1 level of bonus (we now have 2 levels)
     * They will disapear with time but we need to manage them
     *
     * @param mixed[] $arrayBonus
     */
    protected function parseDiscountLegacyBonuses(Money $subTotalInclTaxes, ?array $arrayBonus, int $quantity = 1)
    {
        if ($arrayBonus['type'] === 'free') {
            return $subTotalInclTaxes;
        }

        return $arrayBonus['type'] === 'fixed'
            ? Money::fromVariable($arrayBonus['reduction'] * $quantity)
            : $subTotalInclTaxes->multiply($arrayBonus['reduction'] / 100);
    }

    /**
     * @param mixed[] $arrayBonus
     */
    protected function parseDiscountBonuses(Money $subTotalInclTaxes, array $arrayBonus, int $quantity = 1)
    {
        $totalDiscount = new Money(0);
        $temporarySubTotal = $subTotalInclTaxes;

        foreach ($arrayBonus as $bonus) {
            if ($bonus['type'] === 'free') {
                return $subTotalInclTaxes;
            }

            $discount = $bonus['type'] === 'fixed'
                ? Money::fromVariable($bonus['reduction'] * $quantity)
                : $temporarySubTotal->multiply($bonus['reduction'] / 100);

            if (true === \array_key_exists('maxAmount', $bonus)
                && true === \is_integer($bonus['maxAmount'])
                && $discount->getAmount() > $bonus['maxAmount']
            ) {
                $discount = new Money($bonus['maxAmount']);
            }

            $totalDiscount = $totalDiscount->add($discount);
            $temporarySubTotal = $temporarySubTotal->subtract($discount);
        }

        return $totalDiscount;
    }

    protected function applyBasketDiscountTargetBasket(
        AmountItem $item,
        float $itemVolumeInPercent,
        TaxRate $taxComponent,
        OrderAmounts $orderAmounts,
        array $bonus
    ): void {
        $discountTotal = $this->totalDiscountCalc($orderAmounts->getSubTotalInclTaxes(), $bonus);
        // we calculate the amount of the discount for each item
        $discountAmount = $discountTotal->multiply($itemVolumeInPercent);

        // If the discount is greater than the price, we can't have negative amounts
        if ($discountAmount >= $item->getTotalInclTaxes()) {
            $item->setDiscount($item->getTotalInclTaxes())
                ->setDiscountedTotalInclTaxes(new Money(0))
                ->setDiscountedTotalTaxAmount(new Money(0))
                ->setDiscountedTotalExclTaxes(new Money(0));
        } else {
            // and we calculate the final total after discount
            $discountedTotalInclTaxes = $item->getTotalInclTaxes()->subtract($discountAmount);

            $item->setDiscount($discountAmount)
                ->setDiscountedTotalInclTaxes($discountedTotalInclTaxes)
                ->setDiscountedTotalTaxAmount($taxComponent->getTaxAmountFromIncludingTaxes($discountedTotalInclTaxes))
                ->setDiscountedTotalExclTaxes($taxComponent->getAmountExcludingTax($discountedTotalInclTaxes));
        }
        // we add the discount amount to the total discount of the order Amount
        $orderAmounts->setTotalDiscount($orderAmounts->getTotalDiscount()->add($item->getDiscount()));
    }

    /**
     * @param mixed[] $bonus
     * @param string[] $productIds
     */
    protected function applyBasketDiscountTargetProduct(
        AmountItem $item,
        TaxRate $taxComponent,
        OrderAmounts $orderAmounts,
        array $bonus,
        ?array $productIds
    ): void {
        $itemId = $item->getOrderItemData()->getProductId();

        // We check if the item is discounted or not
        if (true === \is_array($productIds) && true === \in_array($itemId, $productIds, true)) {
            $discountTotal = $this->totalDiscountCalc($item->getTotalInclTaxes(), $bonus, $item->getQuantity());

            // If the discount is greater than the price, we can't have negative amounts
            if ($discountTotal >= $item->getTotalInclTaxes()) {
                $item->setDiscount($item->getTotalInclTaxes())
                    ->setDiscountedTotalInclTaxes(new Money(0))
                    ->setDiscountedTotalTaxAmount(new Money(0))
                    ->setDiscountedTotalExclTaxes(new Money(0));
            } else {
                $discountedTotalInclTaxes = $item->getTotalInclTaxes()->subtract($discountTotal);
                $item->setDiscount($discountTotal)
                    ->setDiscountedTotalInclTaxes($discountedTotalInclTaxes)
                    ->setDiscountedTotalTaxAmount(
                        $taxComponent->getTaxAmountFromIncludingTaxes($discountedTotalInclTaxes)
                    )
                    ->setDiscountedTotalExclTaxes($taxComponent->getAmountExcludingTax($discountedTotalInclTaxes));
            }
        }
        $orderAmounts->setTotalDiscount($orderAmounts->getTotalDiscount()->add($item->getDiscount()));
    }

    /**
     * @param mixed[] $bonus
     * @param string[] $productIds
     */
    protected function applyMarketplaceDiscountTargetProduct(
        AmountItem $item,
        OrderAmounts $orderAmounts,
        array $bonus,
        ?array $productIds
    ): void {
        $itemId = $item->getOrderItemData()->getProductId();

        // We check if the item is discounted or not
        if (true === \is_array($productIds) && true === \in_array($itemId, $productIds, true)) {
            $discountTotal = $this->totalDiscountCalc($item->getTotalInclTaxes(), $bonus, $item->getQuantity());

            // If the discount is greater than the price, we can't have negative amounts
            if ($discountTotal >= $item->getTotalInclTaxes()) {
                $item->setDiscountMarketplace($item->getTotalInclTaxes());
            } else {
                $item->setDiscountMarketplace($discountTotal);
            }
        }
        $orderAmounts->setTotalMarketplaceDiscount($orderAmounts->getTotalMarketplaceDiscount()->add($item->getDiscountMarketplace()));
    }

    /**
     * @param mixed[] $bonus
     * @param string[] $categoryIds
     */
    protected function applyMarketplaceDiscountTargetProductCategory(
        AmountItem $item,
        OrderAmounts $orderAmounts,
        array $bonus,
        ?array $categoryIds
    ): void {
        $itemId = $item->getOrderItemData()->getProductId();

        // We check if the item is discounted or not
        if (true === \is_array($categoryIds)  && \in_array(fn_get_product_category_id($itemId), $categoryIds) === true) {
            $discountTotal = $this->totalDiscountCalc($item->getTotalInclTaxes(), $bonus, $item->getQuantity());

            // If the discount is greater than the price, we can't have negative amounts
            if ($discountTotal >= $item->getTotalInclTaxes()) {
                $item->setDiscountMarketplace($item->getTotalInclTaxes());
            } else {
                $item->setDiscountMarketplace($discountTotal);
            }
        }
        $orderAmounts->setTotalMarketplaceDiscount($orderAmounts->getTotalMarketplaceDiscount()->add($item->getDiscountMarketplace()));
    }

    protected function applyBasketDiscountTargetShipping(OrderAmounts $orderAmounts, array $bonus): void
    {
        $shippingAmount = $orderAmounts->getShippingAmount();
        $taxRate = $shippingAmount->getTaxRate();
        $taxComponent = new TaxRate($taxRate);
        $discountTotal = $this->totalDiscountCalc($orderAmounts->getShippingAmount()->getTotalInclTaxes(), $bonus);

        if ($discountTotal >= $shippingAmount->getTotalInclTaxes()) {
            $discountTotal = $shippingAmount->getTotalInclTaxes();
        }

        $discountedTotalInclTaxes = $shippingAmount->getTotalInclTaxes()->subtract($discountTotal);
        $discountedTaxAmount = $taxComponent->getDiscountedTaxAmountFromIncludingTaxes($discountedTotalInclTaxes);

        $shippingAmount
            ->setBasketDiscount($discountTotal)
            ->setDiscountedTotalInclTaxes($discountedTotalInclTaxes)
            ->setDiscountedTotalTaxAmount($discountedTaxAmount)
            ->setDiscountedTotalExclTaxes($discountedTotalInclTaxes->subtract($discountedTaxAmount));

        $orderAmounts->setTotalDiscount($discountTotal);
    }

    /**
     * @param mixed[] $bonus
     * @param string[] $productIds
     */
    protected function applyBasketDiscount(
        OrderAmounts $orderAmounts,
        array $bonus,
        string $target,
        ?array $productIds
    ): void {
        /** @var AmountItem $item */
        foreach ($orderAmounts->getAmountItems() as $item) {
            $taxComponent = new TaxRate($item->getTaxRate());
            // Calculate the item discount ratio
            $itemVolumeInPercent
                = $item->getTotalInclTaxes()->getValue() / $orderAmounts->getSubTotalInclTaxes()->getValue();

            // Basket Discount
            if (BonusTarget::fromString($target) instanceof BonusTargetBasket) {
                $this->applyBasketDiscountTargetBasket(
                    $item,
                    $itemVolumeInPercent,
                    $taxComponent,
                    $orderAmounts,
                    $bonus
                );
            } elseif (BonusTarget::fromString($target) instanceof BonusTargetProductInBasket) {
                // Discount on product in basket
                $this->applyBasketDiscountTargetProduct(
                    $item,
                    $taxComponent,
                    $orderAmounts,
                    $bonus,
                    $productIds
                );
            }
        }

        if (BonusTarget::fromString($target) instanceof BonusTargetShipping) {
            // Shipping Discount
            $this->applyBasketDiscountTargetShipping($orderAmounts, $bonus);
        }
    }

    protected function applyMarketplaceDiscount(
        OrderAmounts $orderAmounts,
        array $bonus,
        string $target,
        ?array $productIds,
        ?array $categoryIds
    ) {
        if (BonusTarget::fromString($target) instanceof BonusTargetBasket) {
            foreach ($orderAmounts->getAmountItems() as $item) {
                $itemVolumeInPercent
                    = $item->getTotalInclTaxes()->getValue() /
                    $orderAmounts->getSubTotalInclTaxes()->getValue();
                // Discount Marketplace
                $item->setDiscountMarketplace(
                    $orderAmounts->getTotalMarketplaceDiscount()->multiply($itemVolumeInPercent)
                );
            }
        } elseif (BonusTarget::fromString($target) instanceof BonusTargetShipping) {
            $orderAmounts->getShippingAmount()->setMarketplaceDiscount($orderAmounts->getTotalMarketplaceDiscount());
        } elseif (BonusTarget::fromString($target) instanceof BonusTargetProductInBasket === true) {
            foreach ($orderAmounts->getAmountItems() as $item) {
                // Discount Marketplace on product in basket
                $this->applyMarketplaceDiscountTargetProduct(
                    $item,
                    $orderAmounts,
                    $bonus,
                    $productIds
                );
            }
            if (\count($bonus) > 0
                && \array_key_exists('maxAmount', $bonus[0])
                && \is_int($bonus[0]['maxAmount']) === true
                && $orderAmounts->getTotalMarketplaceDiscount() > new Money($bonus[0]['maxAmount'])
            ) {
                $orderAmounts->setTotalMarketplaceDiscount(new Money($bonus[0]['maxAmount'])) ;
            }
        } elseif (BonusTarget::fromString($target) instanceof BonusTargetProductCategoryInBasket === true) {
            foreach ($orderAmounts->getAmountItems() as $item) {
                // Discount Marketplace on product category in basket
                $this->applyMarketplaceDiscountTargetProductCategory(
                    $item,
                    $orderAmounts,
                    $bonus,
                    $categoryIds
                );
            }
            if (\count($bonus) > 0
                && \array_key_exists('maxAmount', $bonus[0])
                && \is_int($bonus[0]['maxAmount']) === true
                && $orderAmounts->getTotalMarketplaceDiscount() > new Money($bonus[0]['maxAmount'])
            ) {
                $orderAmounts->setTotalMarketplaceDiscount(new Money($bonus[0]['maxAmount'])) ;
            }
        }
    }

    // Method to process discounts for old orders. Orders before August 2020 doesn't have
    // all needed discounts data, so we need to deduce values from what is saved in the database
    protected function processDiscountsLegacy(Order $order, OrderAmounts $orderAmounts): void
    {
        $orderAmounts->setTotalDiscount($order->getSubTotalDiscount());

        $orderAmounts->getShippingAmount()
            ->setDiscountedTotalExclTaxes($order->getShippingCostWithoutTax())
            ->setDiscountedTotalInclTaxes($order->getShippingCost())
            ->setDiscountedTotalTaxAmount($order->getShippingCost()->subtract($order->getShippingCostWithoutTax()))
            ->setBasketDiscount(
                $orderAmounts->getShippingAmount()->getTotalInclTaxes()->subtract($order->getShippingCost())
            );

        $customerTotalWithoutShippingAndMPDiscountWithTaxes
            = $order->getCustomerTotal()
                ->subtract($orderAmounts->getShippingAmount()->getDiscountedTotalInclTaxes())
                ->add($orderAmounts->getTotalMarketplaceDiscount())
            ;

        /** @var OrderItem $item */
        foreach ($order->getItems() as $item) {
            $amountItem = $item->getAmountItem();
            $taxComponent = new TaxRate($amountItem->getTaxRate());

            $itemRatio
                = $amountItem->getTotalInclTaxes()->getAmount()
                / $orderAmounts->getSubTotalInclTaxes()->getAmount();

            $discountedTotalIncludingTaxes = $customerTotalWithoutShippingAndMPDiscountWithTaxes->multiply($itemRatio);
            $discountedTotalExcludingTaxes = $taxComponent->getAmountExcludingTax($discountedTotalIncludingTaxes);
            $totalInclTaxes = $amountItem->getTotalInclTaxes();

            $discountedTotalTaxAmount = $discountedTotalIncludingTaxes->subtract($discountedTotalExcludingTaxes);
            $amountItem
                ->setDiscountedTotalInclTaxes($discountedTotalIncludingTaxes)
                ->setDiscountedTotalTaxAmount($discountedTotalTaxAmount)
                ->setDiscountedTotalExclTaxes($discountedTotalExcludingTaxes)
                ->setDiscount($totalInclTaxes->subtract($discountedTotalIncludingTaxes))
                ->setDiscountMarketplace($orderAmounts->getTotalMarketplaceDiscount()->multiply($itemRatio));
        }
    }

    /** Apply discounts to each product */
    protected function processDiscounts(OrderAmounts $orderAmounts, Order $order): void
    {
        $orderAmounts->setTotalDiscount(Money::fromVariable(0));

        foreach ($orderAmounts->getPromotions() as $promotionId => $promotion) {
            if (false === \array_key_exists('target', $promotion)) {
                continue;
            }

            if (PromotionType::BASKET === $promotion['promotion_type']) {
                $this->applyBasketDiscount(
                    $orderAmounts,
                    $promotion['bonus'],
                    $promotion['target'],
                    $promotion['product_ids']
                );
            } elseif (PromotionType::MARKETPLACE === $promotion['promotion_type']) {
                $productIds = $categoryIds = null;
                if ($promotion['target'] === static::PROMOTION_PRODUCT_TARGET && \array_key_exists('product_ids', $promotion) === true) {
                    $productIds = $promotion['product_ids'];
                } elseif ($promotion['target'] === static::PROMOTION_PRODUCT_CATEGORY_TARGET && \array_key_exists('category_ids', $promotion) === true) {
                    $categoryIds = $promotion['category_ids'];
                }
                if ($promotion['target'] === static::PROMOTION_PRODUCT_TARGET || $promotion['target'] === static::PROMOTION_PRODUCT_CATEGORY_TARGET) {
                    $orderAmounts->setTotalMarketplaceDiscount(Money::fromVariable(0));
                }
                $this->applyMarketplaceDiscount(
                    $orderAmounts,
                    $promotion['bonus'],
                    $promotion['target'],
                    $productIds,
                    $categoryIds
                );
            }
        }
    }

    protected function subTotalsCalculation(OrderAmounts $orderAmounts): void
    {
        $subTotalExclTaxes = $subTotalInclTaxes = $totalGreenTaxes = Money::fromVariable(0);

        /** @var AmountItem $item */
        foreach ($orderAmounts->getAmountItems() as $item) {
            $subTotalExclTaxes = $subTotalExclTaxes->add($item->getTotalExclTaxes());
            $subTotalInclTaxes = $subTotalInclTaxes->add($item->getTotalInclTaxes());
            $totalGreenTaxes = $totalGreenTaxes->add($item->getTotalGreenTax());
        }

        $orderAmounts->setSubTotalExclTaxes($subTotalExclTaxes);
        $orderAmounts->setSubTotalInclTaxes($subTotalInclTaxes);
        $orderAmounts->setTotalGreenTaxes($totalGreenTaxes);
    }

    protected function totalsCalculations(OrderAmounts $orderAmounts): void
    {
        // Incl taxes
        $totalInclTaxes = $orderAmounts->getSubTotalInclTaxes()
            ->add($orderAmounts->getShippingAmount()->getTotalInclTaxes())
            ->subtract($orderAmounts->getTotalDiscount());

        // Excl taxes
        $totalTaxes = new Money(0);

        foreach ($orderAmounts->getTaxes() as $key => $tax) {
            $totalTaxes = $totalTaxes->add($tax['total']);
        }
        $totalExclTaxes = $totalInclTaxes->subtract($totalTaxes);

        // Customer Total to pay
        $customerTotal = $totalInclTaxes->subtract($orderAmounts->getTotalMarketplaceDiscount());

        $orderAmounts->setTotalInclTaxes($totalInclTaxes);
        $orderAmounts->setTotalExclTaxes($totalExclTaxes);
        $orderAmounts->setCustomerTotal($customerTotal);
    }

    protected function processTaxes(OrderAmounts $orderAmounts): void
    {
        $arrayTaxes = [];

        foreach ($orderAmounts->getAmountItems() as $item) {
            $taxId = $item->getTaxId();

            if (true === \array_key_exists($taxId, $arrayTaxes)) {
                $arrayTaxes[$taxId]['total'] = $arrayTaxes[$taxId]['total']->add($item->getDiscountedTotalTaxAmount());
                $arrayTaxes[$taxId]['applies']['P_' . $item->getOrderItemData()->getItemId()]
                    = $item->getDiscountedTotalTaxAmount();
            } else {
                $arrayTaxes[$taxId]
                    = [
                        'total' => $item->getDiscountedTotalTaxAmount(),
                        'rate' => $item->gettaxRate(),
                        'applies' => [
                            'P_' . $item->getOrderItemData()->getItemId()
                                => $item->getDiscountedTotalTaxAmount()
                        ]
                    ];
            }
        }

        $shippingAmount = $orderAmounts->getShippingAmount();
        $shippingTaxId = $shippingAmount->getTaxId();
        $shippingDiscountedTaxAmount = $shippingAmount->getDiscountedTotalTaxAmount();

        if (0 < $shippingDiscountedTaxAmount->getConvertedAmount()) {
            if (true === \array_key_exists($shippingTaxId, $arrayTaxes)) {
                $arrayTaxes[$shippingTaxId]['total']
                    = $arrayTaxes[$shippingTaxId]['total']->add($shippingDiscountedTaxAmount);
                $arrayTaxes[$shippingTaxId]['applies']['S_0_' . $shippingAmount->getShippingId()]
                    = $shippingDiscountedTaxAmount;
            } else {
                $arrayTaxes[$shippingTaxId]
                    = [
                        'total' => $shippingAmount->getDiscountedTotalTaxAmount(),
                        'rate' => $shippingAmount->gettaxRate(),
                        'applies' => [
                            'S_0_' . $shippingAmount->getShippingId()
                                => $shippingDiscountedTaxAmount
                        ]
                    ];
            }
        }

        $totalTax = Money::fromVariable(0);
        foreach ($arrayTaxes as $tax) {
            $totalTax = $totalTax->add($tax['total']);
        }

        $orderAmounts->setTotalTaxAmount($totalTax);
        $orderAmounts->setTaxes($arrayTaxes);
    }

    protected function processCommission(Order $order, OrderAmounts $orderAmounts): void
    {
        $taxComponent = new TaxRate($orderAmounts->getCommissionTaxRate());

        // We calculate "excluding taxes" from the "including taxes" and the "tax rate"
        $commissionIncludingTaxes = $this->commissionService->calculateCommissions($order);
        $commissionExcludingTaxes = $taxComponent->getAmountExcludingTax($commissionIncludingTaxes);
        $commissionTaxesAmount = $commissionIncludingTaxes->subtract($commissionExcludingTaxes);

        $orderAmounts->setCommissionInclTaxes($commissionIncludingTaxes)
            ->setCommissionExclTaxes($commissionExcludingTaxes)
            ->setCommissionTaxAmount($commissionTaxesAmount);

        // To return the correct value and to include refunds, vendor share must use transactions values
        $vendorShareIncludingTaxes = $order->getBalanceTotal(false)->subtract($commissionIncludingTaxes);
        if ($vendorShareIncludingTaxes->getAmount() < 0) {
            $vendorShareIncludingTaxes = new Money(0);
        }

        $vendorShareExcludingTaxes = $taxComponent->getAmountExcludingTax($vendorShareIncludingTaxes);
        $vendorShareTaxesAmount    = $taxComponent->getTaxAmountFromIncludingTaxes($vendorShareIncludingTaxes);

        $orderAmounts->setVendorShareInclTaxes($vendorShareIncludingTaxes)
            ->setVendorShareExclTaxes($vendorShareExcludingTaxes)
            ->setVendorShareTaxAmount($vendorShareTaxesAmount);
    }

    /** @param mixed[] $promotions */
    public function hydrateFromOrder(Order $order, OrderAmounts $orderAmount, array $promotions, ?int $commissionTaxId): self
    {
        $orderAmount->setOrderId($order->getId());
        $orderAmount->setPromotions($promotions);

        foreach ($order->getItems() as $orderItem) {
            $amountsItem = new AmountItem();
            $amountsItem->hydrateFromOrderItem($orderAmount, $orderItem);

            $orderAmount->addAmountItem($amountsItem);
        }

        // Tax Amount
        $orderAmount->setTotalTaxAmount(Money::fromVariable(0));

        // Commission Tax Rate
        $this->defineCommissionTaxRate($orderAmount, $commissionTaxId);

        // MarketPlace Discount
        $orderAmount->setTotalMarketplaceDiscount($order->getMarketplaceDiscountTotal());

        return $this;
    }

    /**
     * Some old orders are broken in DB, so we need to hydrate totals from the DB instead of doing calculation
     * Once all the old orders hydrated, this method will be deprecated
     * @param mixed[] $promotion
     */
    protected function hydrateFromOrderLegacy(Order $order, OrderAmounts $orderAmount, array $promotion, ?int $commissionTaxId): OrderAmounts
    {
        $orderAmount->setOrderId($order->getId());
        $orderAmount->setPromotions($promotion);

        foreach ($order->getItems() as $orderItem) {
            $amountsItem = new AmountItem();
            $amountsItem->hydrateFromOrderItem($orderAmount, $orderItem);

            $orderAmount->addAmountItem($amountsItem);
        }

        $shippingTaxRate = $order->getShippingTaxRate()->toFloat();
        $taxComponent = new TaxRate($shippingTaxRate);

        $shippingCostInclTaxes = $order->getOriginalShippingCost();
        $shippingCostExclTaxes = $taxComponent->getAmountExcludingTax($shippingCostInclTaxes);
        $shippingTaxAmount = $shippingCostInclTaxes->subtract($shippingCostExclTaxes);

        $shippingAmounts = new ShippingAmounts();
        $shippingAmounts->setTaxId($order->getShippingTaxId())
            ->setShippingId($order->getShippingId())
            ->setTotalInclTaxes($shippingCostInclTaxes)
            ->setTotalExclTaxes($shippingCostExclTaxes)
            ->setTotalTaxAmount($shippingTaxAmount)
            ->setTaxRate($shippingTaxRate)
            ->setDiscountedTotalExclTaxes($order->getShippingCostWithoutTax())
            ->setDiscountedTotalInclTaxes($order->getShippingCost())
            ->setDiscountedTotalTaxAmount($order->getShippingCost()->subtract($order->getShippingCostWithoutTax()));

        $orderAmount->setShippingAmount($shippingAmounts);

        $this->subTotalsCalculation($orderAmount);

        // Commission Tax Rate
        $this->defineCommissionTaxRate($orderAmount, $commissionTaxId);

        // MarketPlace Discount
        $orderAmount->setTotalMarketplaceDiscount($order->getMarketplaceDiscountTotal());

        $this->subTotalsCalculation($orderAmount);
        // Discounts calculation, old orders don't have enough discount information for our system
        // so we have to hydrate and calculate manually some values in order to apply the discount
        $this->processDiscountsLegacy($order, $orderAmount);
        $this->processTaxes($orderAmount);

        $orderAmount->setTotalInclTaxes($order->getTotal());
        $orderAmount->setTotalExclTaxes($orderAmount->getTotalInclTaxes()->subtract($orderAmount->getTotalTaxAmount()));
        $orderAmount->setCustomerTotal($order->getCustomerTotal());

        return $orderAmount;
    }

    /** Hydrate a parent order with its children orders */
    protected function hydrateParentOrder(Order $order, OrderAmounts $orderAmount): OrderAmounts
    {
        $childOrders = $this->orderService->getChildOrders($order->getId());
        $subTotalExclTaxes = $subTotalInclTaxes = $totalGreenTaxes = $totalDiscount = new Money(0);
        $totalInclTaxes = $totalExclTaxes = $totalTaxAmount = $customerTotal = new Money(0);
        $shippingCostWithoutTax = $shippingCost = $shippingTotalTax = $basketDiscount = new Money(0);
        $shippingCostWithoutTaxDiscounted = $shippingCostDiscounted = $shippingTotalTaxDiscounted = new Money(0);

        foreach ($childOrders as $childOrder) {
            $childOrderAmount = $childOrder->getOrderAmounts();
            $shippingAmount = $childOrderAmount->getShippingAmount();

            // Shipping
            $shippingCostWithoutTax = $shippingCostWithoutTax->add($shippingAmount->getTotalExclTaxes());
            $shippingCost = $shippingCost->add($shippingAmount->getTotalInclTaxes());
            $shippingTotalTax = $shippingTotalTax->add($shippingAmount->getTotalTaxAmount());
            $shippingCostWithoutTaxDiscounted
                = $shippingCostWithoutTaxDiscounted->add($shippingAmount->getDiscountedTotalExclTaxes());
            $shippingCostDiscounted = $shippingCostDiscounted->add($shippingAmount->getDiscountedTotalInclTaxes());
            $shippingTotalTaxDiscounted
                = $shippingTotalTaxDiscounted->add($shippingAmount->getDiscountedTotalTaxAmount());
            $basketDiscount = $basketDiscount->add($shippingAmount->getBasketDiscount());

            // Totals and subtotals
            $subTotalExclTaxes = $subTotalExclTaxes->add($childOrderAmount->getSubTotalExclTaxes());
            $subTotalInclTaxes = $subTotalInclTaxes->add($childOrderAmount->getSubTotalInclTaxes());
            $totalGreenTaxes = $totalGreenTaxes->add($childOrderAmount->getTotalGreenTaxes());
            $totalDiscount = $totalDiscount->add(
                $childOrderAmount
                ->getTotalDiscount()
                ->reducePrecisionToCents()
            );
            $totalInclTaxes = $totalInclTaxes->add($childOrderAmount->getTotalInclTaxes());
            $totalExclTaxes = $totalExclTaxes->add($childOrderAmount->getTotalExclTaxes());
            $totalTaxAmount = $totalTaxAmount->add($childOrderAmount->getTotalTaxAmount());
            $customerTotal = $customerTotal->add(
                $childOrderAmount
                ->getCustomerTotal()
                ->reducePrecisionToCents()
            );
        }

        // Shipping
        $orderAmount
            ->getShippingAmount()
            ->setTotalExclTaxes($shippingCostWithoutTax)
            ->setTotalInclTaxes($shippingCost)
            ->setTotalTaxAmount($shippingTotalTax)
            ->setDiscountedTotalExclTaxes($shippingCostWithoutTaxDiscounted)
            ->setDiscountedTotalInclTaxes($shippingCostDiscounted)
            ->setDiscountedTotalTaxAmount($shippingTotalTaxDiscounted)
            ->setBasketDiscount($basketDiscount);

        // Totals and subtotals
        $orderAmount
            ->setSubTotalExclTaxes($subTotalExclTaxes)
            ->setSubTotalInclTaxes($subTotalInclTaxes)
            ->setTotalGreenTaxes($totalGreenTaxes)
            ->setTotalDiscount($totalDiscount)
            ->setTotalInclTaxes($totalInclTaxes)
            ->setTotalExclTaxes($totalExclTaxes)
            ->setTotalTaxAmount($totalTaxAmount)
            ->setCustomerTotal($customerTotal);

        return $orderAmount;
    }

    protected function process(Order $order): OrderAmounts
    {
        if (true === \array_key_exists($order->getId(), $this->batchProcessingOrderAmounts)) {
            return $this->batchProcessingOrderAmounts[$order->getId()];
        }

        $mutex = $this->mutexService->createBlockingMutex(OrderAmountsCalculator::class, \strval($order->getId()));
        $orderAmount = $this->orderAmountsRepository->findOneByOrderId($order->getId());
        $commissionTaxId = $this->taxRepository->getCommissionTaxId();

        if ($orderAmount instanceof OrderAmounts === true
            && $orderAmount->getAmountItems()->count() !== \count($order->getItems())
        ) {
            $this->logger->error(
                'Not all order amount items are created',
                [
                    'orderId' => $order->getId(),
                ]
            );

            $this->orderAmountsRepository->remove($orderAmount);
            $orderAmount = null;
        }

        // Check if orderAmount exist in DB
        if (null === $orderAmount) {
            $promotions = $this->addProductIdsAndCategoryIdsToPromotion($order);
            $orderAmount = new OrderAmounts();

            if (\DateTime::createFromFormat('d-m-Y H:i:s', $this->legacyPivotDate) > $order->getIssueDate()) {
                // Order before pivot date don't have all the information needed to fix them
                $this->hydrateFromOrderLegacy($order, $orderAmount, $promotions, $commissionTaxId);
                $this->save($orderAmount);
            } elseif (true === $order->isParentOrder()) {
                $this->hydrateFromOrderLegacy($order, $orderAmount, $promotions, $commissionTaxId);
            } else {
                // hydrate entity from order
                $this->hydrateFromOrder($order, $orderAmount, $promotions, $commissionTaxId);

                // shipping
                $orderAmount->hydrateShippingFromOrder($order);

                // Totals Excl and Incl taxes calculation
                $this->subTotalsCalculation($orderAmount);

                // Discounts calculation
                $this->processDiscounts($orderAmount, $order);

                // Taxes Calculation
                $this->processTaxes($orderAmount);

                // Totals Calculation
                $this->totalsCalculations($orderAmount);

                $this->save($orderAmount);
            }
        }

        $this->commissionService->saveOrderAmountsCommission($order);

        // Commissions use an order and need the above calculations
        $order->setOrderAmounts($orderAmount);
        foreach ($order->getItems() as $orderItem) {
            $orderItem->setAmountItem($orderAmount->getAmountItem($orderItem->getItemId()));
        }

        $this->processCommission($order, $orderAmount);

        return $orderAmount;
    }

    /**
     * 👺
     */
    public function recalculate(Order $order)
    {
        $orderAmounts = $order->getOrderAmounts();

        // Totals Excl and Incl taxes calculation
        $this->subTotalsCalculation($orderAmounts);
        // Discounts calculation
        $this->processDiscounts($orderAmounts, $order);
        // Taxes Calculation
        $this->processTaxes($orderAmounts);
        // Totals Calculation
        $this->totalsCalculations($orderAmounts);

        $this->save($orderAmounts);
    }

    /**
     * Add product ids and category ids into the promotion array for BonusTargetProductInBasket or BonusTargetProductCategoryInBasket promotions
     * @return mixed[]
     */
    protected function addProductIdsAndCategoryIdsToPromotion(Order $order): array
    {
        $promotions = $order->getPromotions();

        foreach ($promotions as $promotionId => &$promotion) {
            if (false === \array_key_exists('target', $promotion)) {
                continue;
            }

            if (BonusTarget::fromString($promotion['target']) instanceof BonusTargetProductInBasket) {
                try {
                    $productDiscount = $this->promotionRepository->get($promotionId);
                    /** @var BonusTargetProductInBasket $target */
                    $target = $productDiscount->getTarget();
                    $promotion['product_ids'] = $target->getProductIds();
                } catch (PromotionNotFound $exception) {
                    // If the product in basket promotion was deleted before this system was implemented,
                    // the product list for that promotion is lost forever
                    $this->logger->error(
                        'Product in basket promotion was deleted',
                        [
                            'promotionId' => $promotionId,
                            'orderId' => $order->getId(),
                        ]
                    );
                }
            }

            if (BonusTarget::fromString($promotion['target']) instanceof BonusTargetProductCategoryInBasket === true) {
                $productDiscount = $this->promotionRepository->get($promotionId);
                /** @var BonusTargetProductCategoryInBasket $target */
                $target = $productDiscount->getTarget();
                $promotion['category_ids'] = $target->getProductCategoryIdsWithChildren();
            }
        }
        unset($promotion);

        return $promotions;
    }

    protected function defineCommissionTaxRate(OrderAmounts $orderAmount, ?int $commissionTaxId): void
    {
        // Marketplace Tax Rate
        $marketplaceTaxRate = (float) (new TaxEntity(Tax::getFullRateId()))->getRate();
        $orderAmount->setMarketplaceTaxRate($marketplaceTaxRate);

        // If a commission tax rate is defined we use it for commissions calculations,
        // if not we use the marketplace tax rate instead
        null !== $commissionTaxId ?
            $orderAmount->setCommissionTaxRate((float) (new TaxEntity($commissionTaxId))->getRate())
            : $orderAmount->setCommissionTaxRate($marketplaceTaxRate);
    }

    protected function save(OrderAmounts $orderAmounts): void
    {
        $orderId = $orderAmounts->getOrderId();
        if (null !== $orderId) {
            if ($this->batchProcessingSize > 1) {
                $this->batchProcessingOrderAmounts[$orderId] = $orderAmounts;

                if (\count($this->batchProcessingOrderAmounts) >= $this->batchProcessingSize) {
                    $this->orderAmountsRepository->saveBatch($this->batchProcessingOrderAmounts);
                    $this->batchProcessingOrderAmounts = [];
                }
            } else {
                $this->orderAmountsRepository->save($orderAmounts);
            }
        }
    }
}
