<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\AmountsCalculator\Entity;

use Doctrine\ORM\Mapping as ORM;
use Wizacha\Marketplace\Order\OrderItem;

/**
 * This class represents the data of an order item. (Order = order confirmed by the vendor).
 *
 * @ORM\Table(name="order_item_data")
 * @ORM\Entity(repositoryClass="Wizacha\Marketplace\Order\AmountsCalculator\Repository\OrderItemDataRepository")
 */
class OrderItemData
{
    /**
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private int $id;

    /**
     * @ORM\Column(name="product_id", type="integer")
     */
    private int $productId;

    /**
     * @ORM\Column(name="full_category_path", type="string", nullable=true)
     */
    private ?string $fullCategoryPath;

    /**
     * @ORM\Column(name="declination_id", type="string")
     */
    private string $declinationId;

    /**
     * @ORM\Column(name="product_name", type="string")
     */
    private string $productName;

    /**
     * @ORM\Column(name="product_code", type="string")
     */
    private string $productCode;

    /**
     * @ORM\Column(name="tax_included_in_price", type="boolean")
     */
    private bool $taxIncludedInPrice;

    /**
     * @ORM\Column(name="max_price_adjustment", type="integer", nullable=true)
     */
    private ?int $maxPriceAdjustment;

    /**
     * @ORM\Column(name="item_id", type="string")
     */
    private string $itemId;

    /**
     * @ORM\Column(name="tax_data", type="array")
     * @var mixed[]
     */
    private $taxData;

    /**
     * @ORM\Column(name="raw_price", type="float")
     */
    private float $rawPrice;

    public function hydrateAmountItemDataFromOrder(OrderItem $orderItem): self
    {
        $this->setProductId($orderItem->getProductId())
            ->setFullCategoryPath($orderItem->getFullCategoryPath())
            ->setDeclinationId($orderItem->getDeclinationId())
            ->setProductName($orderItem->getProductName())
            ->setProductCode($orderItem->getProductCode())
            ->setTaxIncludedInPrice($orderItem->isTaxIncludedInPrice())
            ->setItemId($orderItem->getItemId())
            ->setTaxData($orderItem->getTaxData())
            ->setRawPrice($orderItem->getRawPrice())
            ->setMaxPriceAdjustment($orderItem->getMaxPriceAdjustment());

        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getProductId(): int
    {
        return $this->productId;
    }

    public function setProductId(int $productId): self
    {
        $this->productId = $productId;
        return $this;
    }

    public function getFullCategoryPath(): ?string
    {
        return $this->fullCategoryPath;
    }

    public function setFullCategoryPath(?string $fullCategoryPath): self
    {
        $this->fullCategoryPath = $fullCategoryPath;
        return $this;
    }


    public function getDeclinationId(): string
    {
        return $this->declinationId;
    }

    public function setDeclinationId(string $declinationId): self
    {
        $this->declinationId = $declinationId;
        return $this;
    }

    public function getProductName(): string
    {
        return $this->productName;
    }

    public function setProductName(string $productName): self
    {
        $this->productName = $productName;
        return $this;
    }

    public function getProductCode(): string
    {
        return $this->productCode;
    }

    public function setProductCode(string $productCode): self
    {
        $this->productCode = $productCode;
        return $this;
    }

    public function getTaxIncludedInPrice(): bool
    {
        return $this->taxIncludedInPrice;
    }

    public function setTaxIncludedInPrice(bool $taxIncludedInPrice): self
    {
        $this->taxIncludedInPrice = $taxIncludedInPrice;
        return $this;
    }

    public function getMaxPriceAdjustment(): ?int
    {
        return $this->maxPriceAdjustment;
    }

    public function setMaxPriceAdjustment(?int $maxPriceAdjustment): self
    {
        $this->maxPriceAdjustment = $maxPriceAdjustment;
        return $this;
    }

    public function getItemId(): string
    {
        return $this->itemId;
    }

    public function setItemId(string $itemId): self
    {
        $this->itemId = $itemId;
        return $this;
    }

    /**
     * @return mixed[]
     */
    public function getTaxData(): array
    {
        return $this->taxData;
    }

    /**
     * @param mixed[] $taxData
     * @return $this
     */
    public function setTaxData(array $taxData): self
    {
        $this->taxData = $taxData;
        return $this;
    }

    public function getRawPrice(): float
    {
        return $this->rawPrice;
    }

    public function setRawPrice(float $rawPrice): self
    {
        $this->rawPrice = $rawPrice;
        return $this;
    }
}
