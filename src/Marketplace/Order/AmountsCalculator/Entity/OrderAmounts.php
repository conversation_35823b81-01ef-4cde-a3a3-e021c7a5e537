<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\AmountsCalculator\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Wizacha\Component\Order\TaxRate;
use Wizacha\Marketplace\Exception\AmountItemNotFound;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Money\Money;
use Wizacha\Money\PreciseMoney;

/**
 * This class will represents the amounts of an order (Order = order confirmed by the vendor).
 *
 * @ORM\Table(name="order_amounts")
 * @ORM\Entity(repositoryClass="Wizacha\Marketplace\Order\AmountsCalculator\Repository\OrderAmountsRepository")
 */
class OrderAmounts
{
    /**
     * @ORM\Column(name="order_id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="NONE")
     */
    private int $orderId;

    /**
     * @var Collection<AmountItem>
     * @ORM\OneToMany(targetEntity="AmountItem", mappedBy="orderAmounts", cascade={"persist", "remove"})
     * @ORM\JoinColumn(onDelete="CASCADE")
     */
    private Collection $amountItem;

    /**
     * @ORM\OneToOne(targetEntity="ShippingAmounts", cascade={"persist"})
     * @ORM\JoinColumn(onDelete="CASCADE")
     */
    protected ShippingAmounts $shippingAmount;

    /** @ORM\Column(name="sub_total_excl_taxes", type="precise_money") */
    private PreciseMoney $subTotalExclTaxes;

    /** @ORM\Column(name="sub_total_incl_taxes", type="precise_money") */
    private PreciseMoney $subTotalInclTaxes;

    /** @ORM\Column(name="total_discount", type="precise_money") */
    private PreciseMoney $totalDiscount;

    /** @ORM\Column(name="total_excl_taxes", type="precise_money") */
    private PreciseMoney $totalExclTaxes;

    /** @ORM\Column(name="total_incl_taxes", type="precise_money") */
    private PreciseMoney $totalInclTaxes;

    /** @ORM\Column(name="total_green_taxes", type="precise_money") */
    private PreciseMoney $totalGreenTaxes;

    /**
     * @ORM\Column(name="taxes", type="json")
     * @var mixed[]
     */
    private $taxes;

    /** @ORM\Column(name="total_tax_amount", type="precise_money") */
    private PreciseMoney $totalTaxAmount;

    /** @ORM\Column(name="marketplace_tax_rate", type="float") */
    private float $marketplaceTaxRate;

    /** @ORM\Column(name="commission_tax_rate", type="float") */
    private float $commissionTaxRate;

    /** @ORM\Column(name="total_marketplace_discount", type="precise_money") */
    private PreciseMoney $totalMarketplaceDiscount;

    /** @ORM\Column(name="customer_total", type="precise_money") */
    private PreciseMoney $customerTotal;

    /** @ORM\Column(name="promotions", type="json") */
    protected array $promotions;

    /**
     * @ORM\OneToOne(targetEntity="OrderAmountsCommission", cascade={"persist"})
     * @ORM\JoinColumn(onDelete="CASCADE")
     */
    protected OrderAmountsCommission $orderAmountsCommission;

    private PreciseMoney $commissionExclTaxes;
    private PreciseMoney $commissionInclTaxes;
    private PreciseMoney $commissionTaxAmount;

    private PreciseMoney $vendorShareExclTaxes;
    private PreciseMoney $vendorShareInclTaxes;
    private PreciseMoney $vendorShareTaxAmount;

    public function __construct()
    {
        $this->amountItem = new ArrayCollection();
        $this->taxes = [];
    }

    public function hydrateShippingFromOrder(Order $order): self
    {
        $taxRate = $order->getShippingTaxRate()->toFloat();
        $taxComponent = new TaxRate($taxRate);

        $amountInclTaxes = $order->getOriginalShippingCost();
        $amountExclTaxes = $taxComponent->getAmountExcludingTax($amountInclTaxes);
        $taxAmount = $taxComponent->getTaxAmountFromIncludingTaxes($amountInclTaxes);

        $shippingAmounts = new ShippingAmounts();
        $shippingAmounts->setTaxId($order->getShippingTaxId())
                        ->setShippingId($order->getShippingId())
                        ->setTotalInclTaxes($amountInclTaxes)
                        ->setTotalExclTaxes($amountExclTaxes)
                        ->setTotalTaxAmount($taxAmount)
                        ->setTaxRate($taxRate)
                        ->setDiscountedTotalExclTaxes($amountExclTaxes)
                        ->setDiscountedTotalInclTaxes($amountInclTaxes)
                        ->setDiscountedTotalTaxAmount($taxAmount);

        $this->setShippingAmount($shippingAmounts);

        return $this;
    }

    public function getOrderId(): int
    {
        return $this->orderId;
    }

    public function setOrderId(int $orderId): self
    {
        $this->orderId = $orderId;

        return $this;
    }

    /** @return Collection<AmountItem> */
    public function getAmountItems(): Collection
    {
        return $this->amountItem;
    }

    public function getAmountItem(string $itemId): AmountItem
    {
        foreach ($this->getAmountItems() as $item) {
            if ($item->getOrderItemData()->getItemId() === $itemId) {
                return $item;
            }
        }

        throw new AmountItemNotFound("Item $itemId not found in orderAmount " . $this->getOrderId());
    }

    public function addAmountItem(AmountItem $amountItem): self
    {
        if (false === $this->amountItem->contains($amountItem)) {
            $this->amountItem[] = $amountItem;
            $amountItem->setOrderAmounts($this);
        }

        return $this;
    }

    public function getSubTotalExclTaxes(): Money
    {
        return Money::fromValue($this->subTotalExclTaxes->getValue());
    }

    public function setSubTotalExclTaxes(Money $subTotalExclTaxes): self
    {
        $this->subTotalExclTaxes = PreciseMoney::fromValue($subTotalExclTaxes->getValue());

        return $this;
    }

    public function getSubTotalInclTaxes(): Money
    {
        return Money::fromValue($this->subTotalInclTaxes->getValue());
    }

    public function setSubTotalInclTaxes(Money $subTotalInclTaxes): self
    {
        $this->subTotalInclTaxes = PreciseMoney::fromValue($subTotalInclTaxes->getValue());

        return $this;
    }

    public function getTotalDiscount(): Money
    {
        return Money::fromValue($this->totalDiscount->getValue());
    }

    public function setTotalDiscount(Money $totalDiscount): self
    {
        $this->totalDiscount = PreciseMoney::fromValue($totalDiscount->getValue());

        return $this;
    }

    public function getTotalExclTaxes(): Money
    {
        return Money::fromValue($this->totalExclTaxes->getValue());
    }

    public function setTotalExclTaxes(Money $totalExclTaxes): self
    {
        $this->totalExclTaxes = PreciseMoney::fromValue($totalExclTaxes->getValue());

        return $this;
    }

    public function getTotalInclTaxes(): Money
    {
        return Money::fromValue($this->totalInclTaxes->getValue());
    }

    public function setTotalInclTaxes(Money $totalInclTaxes): self
    {
        $this->totalInclTaxes = PreciseMoney::fromValue($totalInclTaxes->getValue());

        return $this;
    }

    public function getTotalGreenTaxes(): Money
    {
        return Money::fromValue($this->totalGreenTaxes->getValue());
    }

    public function setTotalGreenTaxes(Money $greenTaxes): self
    {
        $this->totalGreenTaxes = PreciseMoney::fromValue($greenTaxes->getValue());

        return $this;
    }

    /** @return array mixed[] */
    public function getTaxes(): array
    {
        $taxes = $this->taxes;

        // We need to convert the integers back into money objects
        foreach ($taxes as &$tax) {
            $tax['total'] = Money::fromValue($tax['total']);

            if (true === \array_key_exists('applies', $tax)) {
                foreach ($tax['applies'] as &$apply) {
                    $apply = Money::fromValue($apply);
                }
            }
        }
        unset($tax);

        return $taxes;
    }

    /** @param mixed[] $taxes */
    public function setTaxes(array $taxes): self
    {
        // We need to store the integer value to avoid money serialization
        foreach ($taxes as &$tax) {
            $tax['total'] = $tax['total']->getValue();

            if (true === \array_key_exists('applies', $tax)) {
                foreach ($tax['applies'] as &$apply) {
                    $apply = $apply->getValue();
                }
            }
        }
        unset($tax);

        $this->taxes = $taxes;

        return $this;
    }

    public function getTotalTaxAmount(): Money
    {
        return Money::fromValue($this->totalTaxAmount->getValue());
    }

    public function setTotalTaxAmount(Money $totalTaxAmount): self
    {
        $this->totalTaxAmount = PreciseMoney::fromValue($totalTaxAmount->getValue());

        return $this;
    }

    public function getMarketplaceTaxRate(): float
    {
        return $this->marketplaceTaxRate;
    }

    public function setMarketplaceTaxRate(float $marketplaceTaxRate): self
    {
        $this->marketplaceTaxRate = $marketplaceTaxRate;

        return $this;
    }

    public function getCommissionTaxRate(): float
    {
        return $this->commissionTaxRate;
    }

    public function setCommissionTaxRate(float $commissionTaxRate): self
    {
        $this->commissionTaxRate = $commissionTaxRate;

        return $this;
    }

    public function getTotalMarketplaceDiscount(): Money
    {
        return Money::fromValue($this->totalMarketplaceDiscount->getValue());
    }

    public function setTotalMarketplaceDiscount(Money $totalMarketplaceDiscount): self
    {
        $this->totalMarketplaceDiscount = PreciseMoney::fromValue($totalMarketplaceDiscount->getValue());

        return $this;
    }

    public function getCustomerTotal(): Money
    {
        return Money::fromValue($this->customerTotal->getValue());
    }

    public function setCustomerTotal(Money $customerTotal): self
    {
        $this->customerTotal = PreciseMoney::fromValue($customerTotal->getValue());

        return $this;
    }

    public function getShippingAmount(): ShippingAmounts
    {
        return $this->shippingAmount;
    }

    public function setShippingAmount(ShippingAmounts $shippingAmount): self
    {
        $this->shippingAmount = $shippingAmount;

        return $this;
    }

    public function getCommissionExclTaxes(): Money
    {
        return Money::fromValue($this->commissionExclTaxes->getValue());
    }

    public function setCommissionExclTaxes(Money $commissionExclTaxes): self
    {
        $this->commissionExclTaxes = PreciseMoney::fromValue($commissionExclTaxes->getValue());

        return $this;
    }

    public function getCommissionInclTaxes(): Money
    {
        return Money::fromValue($this->commissionInclTaxes->getValue());
    }

    public function setCommissionInclTaxes(Money $commissionInclTaxes): self
    {
        $this->commissionInclTaxes = PreciseMoney::fromValue($commissionInclTaxes->getValue());

        return $this;
    }

    public function getCommissionTaxAmount(): Money
    {
        return Money::fromValue($this->commissionTaxAmount->getValue());
    }

    public function setCommissionTaxAmount(Money $commissionTaxAmount): self
    {
        $this->commissionTaxAmount = PreciseMoney::fromValue($commissionTaxAmount->getValue());

        return $this;
    }

    public function getVendorShareExclTaxes(): Money
    {
        return Money::fromValue($this->vendorShareExclTaxes->getValue());
    }

    public function setVendorShareExclTaxes(Money $vendorShareExclTaxes): self
    {
        $this->vendorShareExclTaxes = PreciseMoney::fromValue($vendorShareExclTaxes->getValue());

        return $this;
    }

    public function getVendorShareInclTaxes(): Money
    {
        return Money::fromValue($this->vendorShareInclTaxes->getValue());
    }

    public function setVendorShareInclTaxes(Money $vendorShareInclTaxes): self
    {
        $this->vendorShareInclTaxes = PreciseMoney::fromValue($vendorShareInclTaxes->getValue());

        return $this;
    }

    public function getVendorShareTaxAmount(): Money
    {
        return Money::fromValue($this->vendorShareTaxAmount->getValue());
    }

    public function setVendorShareTaxAmount(Money $vendorShareTaxAmount): self
    {
        $this->vendorShareTaxAmount = PreciseMoney::fromValue($vendorShareTaxAmount->getValue());

        return $this;
    }

    /** @return mixed[] */
    public function getPromotions(): array
    {
        return $this->promotions;
    }

    /** @param mixed[] $promotions */
    public function setPromotions(array $promotions): self
    {
        $this->promotions = $promotions;

        return $this;
    }

    public function getOrderAmountsCommission(): OrderAmountsCommission
    {
        return $this->orderAmountsCommission;
    }

    public function setOrderAmountsCommission(OrderAmountsCommission $orderAmountsCommission): self
    {
        $this->orderAmountsCommission = $orderAmountsCommission;

        return $this;
    }
}
