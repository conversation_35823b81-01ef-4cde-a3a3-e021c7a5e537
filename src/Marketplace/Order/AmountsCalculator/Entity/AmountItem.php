<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\AmountsCalculator\Entity;

use Doctrine\ORM\Mapping as ORM;
use Wizacha\Component\Order\TaxRate;
use Wizacha\Marketplace\Order\OrderItem;
use Wizacha\Money\Money;
use Wizacha\Money\PreciseMoney;

/**
 * This class will represents the items of an order (Order = order confirmed by the vendor).
 *
 * @ORM\Table(name="amount_item")
 * @ORM\Entity(repositoryClass="Wizacha\Marketplace\Order\AmountsCalculator\Repository\AmountItemRepository")
 */
class AmountItem
{
    /**
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected int $id;

    /**
     * @ORM\ManyToOne(targetEntity="OrderAmounts", inversedBy="amountItem")
     * @ORM\JoinColumn(name="order_id", referencedColumnName="order_id")
     */
    protected OrderAmounts $orderAmounts;

    /**
     * @ORM\OneToOne(targetEntity="OrderItemData", cascade={"persist"})
     * @ORM\JoinColumn(onDelete="CASCADE")
     */
    protected OrderItemData $orderItemData;

    /** @ORM\Column(name="discount", type="precise_money", nullable=true) */
    protected PreciseMoney $discount;

    /** @ORM\Column(name="total_excl_taxes", type="precise_money") */
    protected PreciseMoney $totalExclTaxes;

    /** @ORM\Column(name="total_incl_taxes", type="precise_money") */
    protected PreciseMoney $totalInclTaxes;

    /** @ORM\Column(name="unit_price_excl_taxes", type="precise_money") */
    protected PreciseMoney $unitPriceExclTaxes;

    /** @ORM\Column(name="unit_price_incl_taxes", type="precise_money") */
    protected PreciseMoney $unitPriceInclTaxes;

    /** @ORM\Column(name="adjusted_unit_price_excl_taxes", type="precise_money", nullable=true) */
    protected ?PreciseMoney $adjustedUnitPriceExclTaxes = null;

    /** @ORM\Column(name="adjusted_unit_price_incl_taxes", type="precise_money", nullable=true) */
    protected ?PreciseMoney $adjustedUnitPriceInclTaxes = null;

    /** @ORM\Column(name="quantity", type="integer") */
    protected int $quantity;

    /** @ORM\Column(name="tax_id", type="integer") */
    protected int $taxId;

    /** @ORM\Column(name="tax_rate", type="float") */
    protected float $taxRate;

    /** @ORM\Column(name="green_tax", type="precise_money") */
    protected PreciseMoney $greenTax;

    /** @ORM\Column(name="total_green_tax", type="precise_money") */
    protected PreciseMoney $totalGreenTax;

    /** @ORM\Column(name="unit_tax_amount", type="precise_money") */
    protected PreciseMoney $unitTaxAmount;

    /** @ORM\Column(name="total_tax_amount", type="precise_money") */
    protected PreciseMoney $totalTaxAmount;

    /** @ORM\Column(name="discounted_unit_price_excl_taxes", type="precise_money") */
    protected PreciseMoney $discountedUnitPriceExclTaxes;

    /** @ORM\Column(name="discounted_unit_price_incl_taxes", type="precise_money") */
    protected PreciseMoney $discountedUnitPriceInclTaxes;

    /** @ORM\Column(name="discounted_unit_tax_amount", type="precise_money") */
    protected PreciseMoney $discountedUnitTaxAmount;

    /** @ORM\Column(name="discounted_total_excl_taxes", type="precise_money") */
    protected PreciseMoney $discountedTotalExclTaxes;

    /** @ORM\Column(name="discounted_total_incl_taxes", type="precise_money") */
    protected PreciseMoney $discountedTotalInclTaxes;

    /** @ORM\Column(name="discounted_total_tax_amount", type="precise_money") */
    protected PreciseMoney $discountedTotalTaxAmount;

    /** @ORM\Column(name="discount_marketplace", type="precise_money", nullable=false) */
    protected PreciseMoney $discountMarketplace;

    public function hydrateFromOrderItem(OrderAmounts $orderAmounts, OrderItem $orderItem): self
    {
        $this->orderAmounts = $orderAmounts;

        // AmountItemData
        $this->orderItemData = new OrderItemData();
        $this->orderItemData->hydrateAmountItemDataFromOrder($orderItem);

        $orderItem->setAmountItem(
            $this->hydrate(
                $orderItem->getPrice(),
                $orderItem->getTaxId(),
                $orderItem->getTaxRate()->toFloat(),
                $orderItem->getGreenTaxAmount(),
                $orderItem->isTaxIncludedInPrice(),
                $orderItem->getQuantity()
            )
        );

        return $this;
    }

    /**
     * OrderAmounts and OrderItem agnostic hydration
     */
    public function hydrate(
        Money $price,
        int $taxId,
        float $taxRate,
        Money $greenTaxAmount,
        bool $isTaxIncludedInPrice,
        int $quantity
    ): self {
        $this->discountMarketplace = PreciseMoney::fromVariable(0);

        $taxComponent = new TaxRate($taxRate);

        // Base values
        // Including taxes
        if (true === $isTaxIncludedInPrice) {
            $unitPriceInclTaxes = $price;
            $unitPriceExclTaxes = $taxComponent->getAmountExcludingTax($unitPriceInclTaxes);
            $this->setUnitPriceInclTaxes($unitPriceInclTaxes);
            $this->setUnitPriceExclTaxes($unitPriceExclTaxes);
        } else {
            //  Excluding Taxes
            $unitPriceExclTaxes = $price->reducePrecisionToCents();
            $unitPriceInclTaxes = $taxComponent->getAmountIncludingTax($unitPriceExclTaxes)->reducePrecisionToCents();
            $this->setUnitPriceExclTaxes($unitPriceExclTaxes);
            $this->setUnitPriceInclTaxes($unitPriceInclTaxes);
        }

        // Tax Rate and Tax ID
        $this->setTaxRate($taxRate);
        $this->setTaxId($taxId);

        // Quantity
        $this->setQuantity($quantity);

        // Green Tax
        $this->setGreenTax($greenTaxAmount);
        $this->setTotalGreenTax(
            $greenTaxAmount->multiply($quantity)
        );

        // Totals before discounts
        $totalExclTaxes = $unitPriceExclTaxes->multiply($quantity);
        $totalInclTaxes = $taxComponent->getAmountIncludingTax($totalExclTaxes)->reducePrecisionToCents();
        $this->setTotalExclTaxes($totalExclTaxes);
        $this->setTotalInclTaxes($totalInclTaxes);

        // Tax Amounts
        $unitTaxAmount = $taxComponent->applyTo($unitPriceExclTaxes)->reducePrecisionToCents();
        $totalTaxAmount = $totalInclTaxes->subtract($totalExclTaxes);

        // Pour des histoires d'arrondi, le montant de TVA unitaire peut ne pas etre équivalent au montant de TVA total divisé par la quantité
        // (et c'est normal
        $this->setUnitTaxAmount($unitTaxAmount);
        $this->setTotalTaxAmount($totalTaxAmount);

        $this->setDiscount(Money::fromVariable(0))
            ->setDiscountedUnitPriceExclTaxes($unitPriceExclTaxes)
            ->setDiscountedUnitPriceInclTaxes($unitPriceInclTaxes)
            ->setDiscountedUnitTaxAmount($unitTaxAmount)
            ->setDiscountedTotalExclTaxes($totalExclTaxes)
            ->setDiscountedTotalInclTaxes($totalInclTaxes)
            ->setDiscountedTotalTaxAmount($totalTaxAmount);

        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getOrderAmounts(): OrderAmounts
    {
        return $this->orderAmounts;
    }

    public function setOrderAmounts(OrderAmounts $orderAmounts): self
    {
        $this->orderAmounts = $orderAmounts;

        return $this;
    }

    public function getOrderItemData(): OrderItemData
    {
        return $this->orderItemData;
    }

    public function setOrderItemData(OrderItemData $orderItemData): self
    {
        $this->orderItemData = $orderItemData;

        return $this;
    }

    public function getDiscount(): Money
    {
        return Money::fromValue($this->discount->getValue());
    }

    public function getUnitDiscount(): Money
    {
        return $this->getDiscount()->divide($this->getQuantity());
    }

    public function setDiscount(Money $discount): self
    {
        $this->discount = PreciseMoney::fromValue($discount->getValue());

        return $this;
    }

    public function getTotalExclTaxes(): Money
    {
        return Money::fromValue($this->totalExclTaxes->getValue());
    }

    public function setTotalExclTaxes(Money $totalExclTaxes): self
    {
        $this->totalExclTaxes = PreciseMoney::fromValue($totalExclTaxes->getValue());

        return $this;
    }

    public function getTotalInclTaxes(): Money
    {
        return Money::fromValue($this->totalInclTaxes->getValue());
    }

    public function setTotalInclTaxes(Money $totalInclTaxes): self
    {
        $this->totalInclTaxes = PreciseMoney::fromValue($totalInclTaxes->getValue());

        return $this;
    }

    public function getUnitPriceExclTaxes(): Money
    {
        if (null !== $this->adjustedUnitPriceExclTaxes) {
            return Money::fromValue($this->adjustedUnitPriceExclTaxes->getValue());
        }

        return Money::fromValue($this->unitPriceExclTaxes->getValue());
    }

    public function setUnitPriceExclTaxes(Money $unitPriceExclTaxes): self
    {
        $this->unitPriceExclTaxes = PreciseMoney::fromValue($unitPriceExclTaxes->getValue());

        return $this;
    }

    public function getUnitPriceInclTaxes(): Money
    {
        if (null !== $this->adjustedUnitPriceInclTaxes) {
            return Money::fromValue($this->adjustedUnitPriceInclTaxes->getValue());
        }

        return Money::fromValue($this->unitPriceInclTaxes->getValue());
    }

    public function setUnitPriceInclTaxes(Money $unitPriceInclTaxes): self
    {
        $this->unitPriceInclTaxes = PreciseMoney::fromValue($unitPriceInclTaxes->getValue());

        return $this;
    }

    public function getAdjustedUnitPriceExclTaxes(): ?Money
    {
        return $this->adjustedUnitPriceExclTaxes === null
            ? null
            : Money::fromValue($this->adjustedUnitPriceExclTaxes->getValue());
    }

    protected function setAdjustedUnitPriceExclTaxes(Money $adjustedUnitPriceExclTaxes): self
    {
        $this->adjustedUnitPriceExclTaxes = PreciseMoney::fromValue($adjustedUnitPriceExclTaxes->getValue());

        return $this;
    }

    public function getAdjustedUnitPriceInclTaxes(): ?Money
    {
        return $this->adjustedUnitPriceInclTaxes === null
            ? null
            : Money::fromValue($this->adjustedUnitPriceInclTaxes->getValue());
    }

    protected function setAdjustedUnitPriceInclTaxes(Money $adjustedUnitPriceInclTaxes): self
    {
        $this->adjustedUnitPriceInclTaxes = PreciseMoney::fromValue($adjustedUnitPriceInclTaxes->getValue());

        return $this;
    }

    public function setAdjustedPrices(Money $adjustedPriceExclTaxes): self
    {
        $taxComponent = new TaxRate($this->getTaxRate());
        $unitPriceExclTaxes = $adjustedPriceExclTaxes->divide($this->getQuantity());
        $unitPriceInclTaxes = $taxComponent->getAmountIncludingTax($unitPriceExclTaxes);

        $this->setAdjustedUnitPriceExclTaxes($unitPriceExclTaxes);
        $this->setAdjustedUnitPriceInclTaxes($unitPriceInclTaxes);

        return $this;
    }

    public function getTaxId(): int
    {
        return $this->taxId;
    }

    public function setTaxId(int $taxId): self
    {
        $this->taxId = $taxId;

        return $this;
    }

    public function getQuantity(): int
    {
        return $this->quantity;
    }

    public function setQuantity(int $quantity): self
    {
        $this->quantity = $quantity;

        return $this;
    }

    public function getTaxRate(): float
    {
        return $this->taxRate;
    }

    public function setTaxRate(float $taxRate): self
    {
        $this->taxRate = $taxRate;

        return $this;
    }

    public function getGreenTax(): Money
    {
        return Money::fromValue($this->greenTax->getValue());
    }

    public function setGreenTax(Money $greenTax): self
    {
        $this->greenTax = PreciseMoney::fromValue($greenTax->getValue());

        return $this;
    }

    public function getTotalGreenTax(): Money
    {
        return Money::fromValue($this->totalGreenTax->getValue());
    }

    public function setTotalGreenTax(Money $totalGreenTax): self
    {
        $this->totalGreenTax = PreciseMoney::fromValue($totalGreenTax->getValue());

        return $this;
    }

    public function getUnitTaxAmount(): Money
    {
        return Money::fromValue($this->unitTaxAmount->getValue());
    }

    public function setUnitTaxAmount(Money $unitTaxAmount): self
    {
        $this->unitTaxAmount = PreciseMoney::fromValue($unitTaxAmount->getValue());

        return $this;
    }

    public function getTotalTaxAmount(): Money
    {
        return Money::fromValue($this->totalTaxAmount->getValue());
    }

    public function setTotalTaxAmount(Money $totalTaxAmount): self
    {
        $this->totalTaxAmount = PreciseMoney::fromValue($totalTaxAmount->getValue());

        return $this;
    }

    public function getDiscountedUnitPriceExclTaxes(): Money
    {
        return Money::fromValue($this->discountedUnitPriceExclTaxes->getValue());
    }

    public function setDiscountedUnitPriceExclTaxes(Money $discountedUnitPriceExclTaxes): self
    {
        $this->discountedUnitPriceExclTaxes = PreciseMoney::fromValue($discountedUnitPriceExclTaxes->getValue());

        return $this;
    }

    public function getDiscountedUnitPriceInclTaxes(): Money
    {
        return Money::fromValue($this->discountedUnitPriceInclTaxes->getValue());
    }

    public function setDiscountedUnitPriceInclTaxes(Money $discountedUnitPriceInclTaxes): self
    {
        $this->discountedUnitPriceInclTaxes = PreciseMoney::fromValue($discountedUnitPriceInclTaxes->getValue());

        return $this;
    }

    public function getDiscountedUnitTaxAmount(): Money
    {
        return Money::fromValue($this->discountedUnitTaxAmount->getValue());
    }

    public function setDiscountedUnitTaxAmount(Money $discountedUnitTaxAmount): self
    {
        $this->discountedUnitTaxAmount = PreciseMoney::fromValue($discountedUnitTaxAmount->getValue());

        return $this;
    }

    public function getDiscountedTotalExclTaxes(): Money
    {
        return Money::fromValue($this->discountedTotalExclTaxes->getValue());
    }

    public function setDiscountedTotalExclTaxes(Money $discountedTotalExclTaxes): self
    {
        $this->discountedTotalExclTaxes = PreciseMoney::fromValue($discountedTotalExclTaxes->getValue());
        $this->setDiscountedUnitPriceExclTaxes($discountedTotalExclTaxes->divide($this->quantity));

        return $this;
    }

    public function getDiscountedTotalInclTaxes(): Money
    {
        return Money::fromValue($this->discountedTotalInclTaxes->getValue());
    }

    public function setDiscountedTotalInclTaxes(Money $discountedTotalInclTaxes): self
    {
        $this->discountedTotalInclTaxes = PreciseMoney::fromValue($discountedTotalInclTaxes->getValue());
        $this->setDiscountedUnitPriceInclTaxes($discountedTotalInclTaxes->divide($this->quantity));

        return $this;
    }

    public function getDiscountedTotalTaxAmount(): Money
    {
        return Money::fromValue($this->discountedTotalTaxAmount->getValue());
    }

    public function setDiscountedTotalTaxAmount(Money $discountedTotalTaxAmount): self
    {
        $this->discountedTotalTaxAmount = PreciseMoney::fromValue($discountedTotalTaxAmount->getValue());
        $this->setDiscountedUnitTaxAmount($discountedTotalTaxAmount->divide($this->quantity));

        return $this;
    }

    public function getDiscountMarketplace(): Money
    {
        return Money::fromValue($this->discountMarketplace->getValue());
    }

    public function getUnitDiscountMarketplace(): Money
    {
        return $this->getDiscountMarketplace()->divide($this->getQuantity());
    }

    public function setDiscountMarketplace(Money $discountMarketplace): self
    {
        $this->discountMarketplace = PreciseMoney::fromValue($discountMarketplace->getValue());

        return $this;
    }
}
