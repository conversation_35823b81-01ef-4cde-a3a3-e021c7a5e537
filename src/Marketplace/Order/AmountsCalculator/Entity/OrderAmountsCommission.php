<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\AmountsCalculator\Entity;

use Doctrine\ORM\Mapping as ORM;
use Wizacha\Marketplace\Commission\Commission;

/**
 * @ORM\Table(name="order_amounts_commission")
 * @ORM\Entity(repositoryClass="Wizacha\Marketplace\Order\AmountsCalculator\Repository\OrderAmountsCommissionRepository")
 */
class OrderAmountsCommission
{
    /**
     * @ORM\Column(name="id", type="string")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="NONE")
     */
    private string $id;

    /** @ORM\Column(name="order_id", type="integer") */
    private int $orderId;

    /** @ORM\Column(name="company_id", type="integer") */
    private ?int $companyId;

    /** @ORM\Column(name="category_id", type="integer") */
    private ?int $categoryId;

    /** @ORM\Column(name="percent_amount", type="float") */
    private float $percentAmount;

    /** @ORM\Column(name="fix_amount", type="float") */
    private float $fixAmount;

    /** @ORM\Column(name="maximum_amount", type="float") */
    private ?float $maximumAmount;

    /** @ORM\Column(name="commission_type", type="string") */
    private string $commissionType;

    public function __construct(
        int $orderId,
        Commission $commission
    ) {
        $this->id = $orderId . '-' . $commission->getId();
        $this->orderId = $orderId;
        $this->companyId = $commission->getCompanyId();
        $this->categoryId = $commission->getcategoryId();
        $this->percentAmount = $commission->getPercentAmount();
        $this->fixAmount = $commission->getFixAmount();
        $this->maximumAmount = $commission->getMaximumAmount();
        $this->commissionType = $commission->getCommissionType();
    }

    public function getOrderId(): int
    {
        return $this->orderId;
    }

    public function setOrderId(int $orderId): self
    {
        $this->orderId = $orderId;
        return $this;
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function setId(string $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getCompanyId(): ?int
    {
        return $this->companyId;
    }

    public function setCompanyId(?int $companyId): self
    {
        $this->companyId = $companyId;
        return $this;
    }

    public function getCategoryId(): ?int
    {
        return $this->categoryId;
    }

    public function setCategoryId(?int $categoryId): self
    {
        $this->categoryId = $categoryId;
        return $this;
    }

    public function getFixAmount(): float
    {
        return $this->fixAmount;
    }

    public function setFixAmount(float $fixAmount): self
    {
        $this->fixAmount = $fixAmount;
        return $this;
    }

    public function getMaximumAmount(): ?float
    {
        return $this->maximumAmount;
    }

    public function setMaximumAmount(?float $maximumAmount): self
    {
        $this->maximumAmount = $maximumAmount;
        return $this;
    }

    public function getCommissionType(): string
    {
        return $this->commissionType;
    }

    public function setCommissionType(string $commissionType): self
    {
        $this->commissionType = $commissionType;
        return $this;
    }

    public function getPercentAmount(): float
    {
        return $this->percentAmount;
    }

    public function setPercentAmount(float $percentAmount): self
    {
        $this->percentAmount = $percentAmount;
        return $this;
    }
}
