<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\AmountsCalculator\Entity;

use Doctrine\ORM\Mapping as ORM;
use Wizacha\Money\Money;
use Wizacha\Money\PreciseMoney;

/**
 * This class will represents the shipping of an order (Order = order confirmed by the vendor).
 *
 * @ORM\Table(name="shipping_amounts")
 * @ORM\Entity(repositoryClass="Wizacha\Marketplace\Order\AmountsCalculator\Repository\ShippingAmountsRepository")
 */
class ShippingAmounts
{
    /**
     * @ORM\Column(name="id", type="integer")
     * @ORM\Id
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    protected int $id;

    /** @ORM\Column(name="shipping_id", type="integer") */
    protected int $shippingId;

    /** @ORM\Column(name="basket_discount", type="precise_money", nullable=true) */
    protected PreciseMoney $basketDiscount;

    /** @ORM\Column(name="marketplace_discount", type="precise_money") */
    protected PreciseMoney $marketplaceDiscount;

    /** @ORM\Column(name="total_excl_taxes", type="precise_money") */
    protected PreciseMoney $totalExclTaxes;

    /** @ORM\Column(name="total_incl_taxes", type="precise_money") */
    protected PreciseMoney $totalInclTaxes;

    /** @ORM\Column(name="tax_rate", type="float") */
    protected float $taxRate;

    /** @ORM\Column(name="tax_id", type="integer") */
    protected int $taxId;

    /** @ORM\Column(name="total_tax_amount", type="precise_money") */
    protected PreciseMoney $totalTaxAmount;

    /** @ORM\Column(name="discounted_total_excl_taxes", type="precise_money") */
    protected PreciseMoney $discountedTotalExclTaxes;

    /** @ORM\Column(name="discounted_total_incl_taxes", type="precise_money") */
    protected PreciseMoney $discountedTotalInclTaxes;

    /** @ORM\Column(name="discounted_total_tax_amount", type="precise_money") */
    protected PreciseMoney $discountedTotalTaxAmount;

    public function __construct()
    {
        $this->basketDiscount = PreciseMoney::fromVariable(0);
        $this->marketplaceDiscount = PreciseMoney::fromVariable(0);
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): ShippingAmounts
    {
        $this->id = $id;

        return $this;
    }

    public function getShippingId(): int
    {
        return $this->shippingId;
    }

    public function setShippingId(int $shippingId): self
    {
        $this->shippingId = $shippingId;

        return $this;
    }

    public function getBasketDiscount(): Money
    {
        return Money::fromValue($this->basketDiscount->getValue());
    }

    public function setBasketDiscount(Money $discount): self
    {
        $this->basketDiscount = PreciseMoney::fromValue($discount->getValue());

        return $this;
    }

    public function getMarketplaceDiscount(): Money
    {
        return Money::fromValue($this->marketplaceDiscount->getValue());
    }

    public function setMarketplaceDiscount(Money $marketplaceDiscount): self
    {
        $this->marketplaceDiscount = PreciseMoney::fromValue($marketplaceDiscount->getValue());

        return $this;
    }


    public function getTotalExclTaxes(): Money
    {
        return Money::fromValue($this->totalExclTaxes->getValue());
    }

    public function setTotalExclTaxes(Money $totalExclTaxes): self
    {
        $this->totalExclTaxes = PreciseMoney::fromValue($totalExclTaxes->getValue());

        return $this;
    }

    public function getTotalInclTaxes(): Money
    {
        return Money::fromValue($this->totalInclTaxes->getValue());
    }

    public function setTotalInclTaxes(Money $totalInclTaxes): self
    {
        $this->totalInclTaxes = PreciseMoney::fromValue($totalInclTaxes->getValue());

        return $this;
    }

    public function getTaxRate(): float
    {
        return $this->taxRate;
    }

    public function setTaxRate(float $taxRate): self
    {
        $this->taxRate = $taxRate;

        return $this;
    }

    public function getTaxId(): int
    {
        return $this->taxId;
    }

    public function setTaxId(int $taxId): self
    {
        $this->taxId = $taxId;

        return $this;
    }

    public function getTotalTaxAmount(): Money
    {
        return Money::fromValue($this->totalTaxAmount->getValue());
    }

    public function setTotalTaxAmount(Money $totalTaxAmount): self
    {
        $this->totalTaxAmount = PreciseMoney::fromValue($totalTaxAmount->getValue());

        return $this;
    }

    public function getDiscountedTotalExclTaxes(): Money
    {
        return Money::fromValue($this->discountedTotalExclTaxes->getValue());
    }

    public function setDiscountedTotalExclTaxes(Money $discountedTotalExclTaxes): self
    {
        $this->discountedTotalExclTaxes = PreciseMoney::fromValue($discountedTotalExclTaxes->getValue());

        return $this;
    }

    public function getDiscountedTotalInclTaxes(): Money
    {
        return Money::fromValue($this->discountedTotalInclTaxes->getValue());
    }

    public function setDiscountedTotalInclTaxes(Money $discountedTotalInclTaxes): self
    {
        $this->discountedTotalInclTaxes = PreciseMoney::fromValue($discountedTotalInclTaxes->getValue());

        return $this;
    }

    public function getDiscountedTotalTaxAmount(): Money
    {
        return Money::fromValue($this->discountedTotalTaxAmount->getValue());
    }


    public function setDiscountedTotalTaxAmount(Money $discountedTotalTaxAmount): self
    {
        $this->discountedTotalTaxAmount = PreciseMoney::fromValue($discountedTotalTaxAmount->getValue());

        return $this;
    }
}
