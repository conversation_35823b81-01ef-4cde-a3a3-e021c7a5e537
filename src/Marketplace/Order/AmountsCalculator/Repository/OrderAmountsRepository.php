<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\AmountsCalculator\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts;

/**
 * @extends ServiceEntityRepository<OrderAmounts>
 * @method OrderAmounts|null find($id, $lockMode = null, $lockVersion = null)
 * @method OrderAmounts|null findOneBy(array $criteria, array $orderBy = null)
 * @method OrderAmounts[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 * @method OrderAmounts|null findOneByOrderId($id);
 */
class OrderAmountsRepository extends ServiceEntityRepository
{
    public function save(OrderAmounts $orderAmounts)
    {
        $this->getEntityManager()->persist($orderAmounts);
        $this->getEntityManager()->flush();
    }

    /** @param OrderAmounts[] $orderAmounts  */
    public function saveBatch(array $orderAmounts): void
    {
        foreach ($orderAmounts as $orderAmount) {
            $this->getEntityManager()->persist($orderAmount);
        }

        $this->getEntityManager()->flush();
        $this->getEntityManager()->clear();
    }

    /**
     * @return string[]
     */
    public function findForExim(int $orderId): array
    {
        $amountsQuery = $this->_em->getConnection()->prepare(
            'SELECT oa.total_incl_taxes, oa.total_excl_taxes, oa.sub_total_excl_taxes, oa.total_incl_taxes,
               oa.total_discount, sa.discounted_total_incl_taxes, oa.sub_total_incl_taxes, oa.total_tax_amount,
               oa.total_marketplace_discount, oa.customer_total, oa.taxes, oa.promotions
            FROM doctrine_order_amounts oa
            JOIN doctrine_shipping_amounts sa ON oa.shipping_amount_id = sa.id
            WHERE order_id = :orderId'
        );
        $amountsQuery->bindValue('orderId', $orderId, \PDO::PARAM_INT);
        $amountsQuery->execute();
        $amounts = $amountsQuery->fetch();

        return \is_array($amounts) === true
            ? $amounts
            : [];
    }

    public function remove(OrderAmounts $orderAmount): void
    {
        $this->getEntityManager()->remove($orderAmount);
        $this->getEntityManager()->flush();
    }
}
