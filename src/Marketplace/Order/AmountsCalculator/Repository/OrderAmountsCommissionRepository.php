<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\AmountsCalculator\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ManagerRegistry;
use Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmountsCommission;

class OrderAmountsCommissionRepository extends ServiceEntityRepository
{
    private EntityManagerInterface $entityManager;

    public function __construct(ManagerRegistry $registry, EntityManagerInterface $entityManager)
    {
        parent::__construct($registry, OrderAmountsCommission::class);
        $this->entityManager = $entityManager;
    }

    public function save(OrderAmountsCommission $orderAmountsCommission): void
    {
        $this->entityManager->persist($orderAmountsCommission);
        $this->entityManager->flush();
    }
}
