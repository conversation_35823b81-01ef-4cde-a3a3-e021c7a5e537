<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order;

use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\PaymentType;

/**
 * Cette classe représente le paiement de l'order et contient les infos qui y
 * sont donc liées
 */
class Payment
{
    /** @var int  */
    private $id;
    /** @var PaymentType */
    private $type;

    /** @var PaymentProcessorName|null */
    private $processorName;

    /** @var string|null */
    private $externalReference;

    /** @var \DateTimeImmutable|null */
    private $commitmentDate;

    /** @var bool|null  */
    private $defermentAuthorized;

    /** @var bool|null  */
    private $isPaid;

    /** @var object */
    private $processorInformation;

    /** @var bool|null  */
    private $isCreditCardCapture;

    public function __construct(
        int $id,
        PaymentType $type,
        ?PaymentProcessorName $processorName,
        ?\DateTimeImmutable $commitmentDate,
        ?bool $defermentAuthorized,
        ?bool $isPaid,
        array $processorInformation = [],
        string $externalReference = null,
        ?bool $isCreditCardCapture = null
    ) {
        $this->id = $id;
        $this->type = $type;
        $this->processorName = $processorName;
        $this->commitmentDate = $commitmentDate;
        $this->defermentAuthorized = $defermentAuthorized;
        $this->isPaid = $isPaid;
        $this->processorInformation = (object) $processorInformation;
        $this->externalReference = $externalReference;
        $this->isCreditCardCapture = $isCreditCardCapture;
    }

    /**
     * @internal Ne pas utiliser.
     */
    public function setDefermentAuthorization(bool $isAuthorized): void
    {
        $this->defermentAuthorized = $isAuthorized;
    }

    /**
     * @internal Ne pas utiliser.
     */
    public function setPaymentCompletion(bool $isComplete): void
    {
        $this->isPaid = $isComplete;
    }

    public function setPaymentCapture(bool $isCreditCardCapture): void
    {
        $this->isCreditCardCapture = $isCreditCardCapture;
    }

    public function isOfType(PaymentType $type): bool
    {
        return $type->equals($this->type);
    }

    public function hasProcessor(): bool
    {
        return $this->processorName instanceof PaymentProcessorName;
    }

    public function hasProcessorName(PaymentProcessorName $processorName): bool
    {
        if (!$this->hasProcessor()) {
            return false;
        }

        return $processorName->equals($this->processorName);
    }

    public function isDefermentAuthorized(): bool
    {
        return $this->defermentAuthorized === true;
    }

    public function isDefermentRefused(): bool
    {
        return $this->defermentAuthorized === false;
    }

    public function isCompleted(): bool
    {
        return $this->isPaid === true;
    }

    public function hasFailed(): bool
    {
        return $this->isPaid === false;
    }

    public function isCreditCardCapture(): ?bool
    {
        return $this->isCreditCardCapture;
    }

    public function expose(): array
    {
        return [
            'type'                 => (string) $this->type,
            'processorName'        => $this->processorName,
            'commitmentDate'       => $this->commitmentDate ? $this->commitmentDate->format(DATE_RFC3339) : null,
            "processorInformation" => $this->getProcessorInformation(),
            'externalReference'    => $this->getExternalReference(),
        ];
    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return PaymentType
     */
    public function getType(): PaymentType
    {
        return $this->type;
    }

    /**
     * @return PaymentProcessorName|null
     */
    public function getProcessorName(): ?PaymentProcessorName
    {
        return $this->processorName;
    }

    /**
     * @return \DateTimeImmutable|null
     */
    public function getCommitmentDate(): ?\DateTimeImmutable
    {
        return $this->commitmentDate;
    }

    /**
     * @return object
     */
    public function getProcessorInformation(): object
    {
        return $this->processorInformation;
    }

    /**
     * @param array $processorInformation
     */
    public function setProcessorInformation(array $processorInformation): void
    {
        $this->processorInformation = (object) $processorInformation;
    }

    public function getExternalReference(): ?string
    {
        return $this->externalReference;
    }
}
