<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order;

use Wizacha\Marketplace\Exception\NotFound;

// phpcs:disable
// @codingStandardsIgnoreStart
/**
 * Cette fonction permet de comparer si le statut d'une commande est bien celui
 * spécifié (tel qu'on le fait via un simple if). Mais elle rajoute également
 * un check interne pour voir si le statut déduit à partir du système de
 * workflow correspond bien à celui qui est attendu.
 *
 * Dans le cas où le statut déduit est différent de celui attendu alors on log
 * une erreur pour nous avertir d'une erreur dans la conception de notre
 * workflow.
 *
 * Pour faire une validation en mode strict une exception sera levé plutôt que
 * de silencieusement outrepassser l'erreur (ce mode est activable via un flag
 * global). Ce mode sera intéressant à activer lors des tests unitaires où lors
 * d'un test fonctionnel à la main.
 *
 * Lorsque l'on sera suffisamment confiant sur la fiabilité du nouveau système
 * de workflow alors on ne se basera plus sur les statuts de cscart mais sur
 * celui déduit, ce qui permet de centraliser la bascule des systèmes.
 *
 * ----------------------------------------------------------------------------
 * Remarque : cette fonction ne vérifie que sur la/les commandes enfant, mais
 * pas sur les commandes parent.
 *
 * Chaque vérification nécessitant une commande parent est fait au niveau plus
 * "métier" avec une fonction dans le service :
 * - \Wizacha\Marketplace\Order\OrderService::isPaid
 * ----------------------------------------------------------------------------
 */
function is_order_status_equal_to(int $orderId, string ...$expectedStatuses): bool
{
    // Récupération des commandes enfants
    try {
        $orders = \Wizacha\Registry::defaultInstance()->container->get('marketplace.order.order_service')->getChildOrders($orderId);
    } catch (NotFound $e) {
        return false;
    }

    $allOrdersHaveTheExpectedLegacyStatus = true;

    foreach ($orders as $order) {
        // Vérification du statut déduit du workflow.
        $allOrdersHaveTheExpectedLegacyStatus &= in_array((string) $order->deduceLegacyStatus(), $expectedStatuses, true);

        if ($order->areStatusesDifferent()
            // A COMPLETED order can't have its status updated
            && $order->getStatus()->getValue() !== OrderStatus::COMPLETED
        ) {
            // The CsCart order status differ from the one deduced from the workflow
            // It means there is a bug either in the workflow or in the actions triggering its advancement!
            container()->get('logger')->warning(
                "Order status mismatch",
                [
                    'id' => $order->getId(),
                    'status' => $order->getStatus()->getKey(),
                    'deducedStatus' => $order->deduceLegacyStatus()->getKey(),
                    'workflow' => (string) $order->getWorkflowName(),
                    'human_name' => $order->getWorkflowName()->getHumanVersion(),
                    'workflow_module' => (string) $order->getWorkflowProgress()->getModuleName(),
                    'workflow_step' => (string) $order->getWorkflowProgress()->getStepName(),
                    'workflow_status' => (string) $order->getWorkflowProgress()->getStatus(),
                    'is_garbage' => $order->isGarbage(),
                ]
            );

            container()->get('marketplace.order.order_service')->overrideOrderStatus($order);
        }
    }

    // On retour pour l'instant le résultat de la comparaison du status legacy.
    return (bool) $allOrdersHaveTheExpectedLegacyStatus;
}
// @codingStandardsIgnoreEnd
// phpcs:enable
