<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\OrderReturn;

use Wizacha\Component\Order\Amounts;
use Wizacha\Component\Order\TaxRate;
use Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderItem;
use Wizacha\Marketplace\Price\PriceFormatter;
use Wizacha\Money\Money;

class OrderReturn
{
    /** @var  int */
    private $returnId;
    /** @var  int */
    private $orderId;
    /** @var  int */
    private $userId;
    /** @var  \DateTime */
    private $createdAt;
    /** @var  string */
    private $status;
    /** @var  string */
    private $comment;
    /** @var string */
    private $rmaNumber;
    /** @var ReturnItem[] */
    private $items;
    /** @var OrderItem[] */
    private $orderItemsForRma;
    /** @var Amounts */
    private $amounts;

    /**
     * @param ReturnItem[] $items
     */
    public function __construct(int $returnId, int $orderId, int $userId, \DateTime $createdAt, string $status, string $comment, ?string $rmaNumber, array $items)
    {
        $this->returnId = $returnId;
        $this->orderId = $orderId;
        $this->userId = $userId;
        $this->createdAt = $createdAt;
        $this->status = $status;
        $this->comment = $comment;
        $this->rmaNumber = $rmaNumber;
        //Controle du type des elements du tableau.
        $this->items = array_map(
            function (ReturnItem $item) {
                return $item;
            },
            $items
        );
    }

    public function getId(): int
    {
        return $this->returnId;
    }

    public function getOrderId(): int
    {
        return $this->orderId;
    }

    public function getUserId(): int
    {
        return $this->userId;
    }

    public function getCreatedAt(): \DateTime
    {
        return $this->createdAt;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function getComment(): string
    {
        return $this->comment;
    }

    public function getRmaNumber(): ?string
    {
        return $this->rmaNumber;
    }

    /**
     * @return ReturnItem[]
     */
    public function getItems(): array
    {
        return $this->items;
    }

    /**
     * @return mixed[]
     */
    public function exposeForCustomRma(Order $order, PriceFormatter $priceFormatter): array
    {
        $orderItems = $order->getItems();

        $countOrderItems = 0;
        foreach ($orderItems as $orderItem) {
            $countOrderItems += $orderItem->getAmount();
        }

        $customReturnItems = array_map(function ($item) {
            return ["itemId" => $item->getItemId(), "amount" => $item->getAmount()];
        }, $this->getItems());

        $rmaItems = [];
        $countRmaItems = 0;
        $rowsOrderItem = [];

        $this->setOrderItemsForRma($customReturnItems, $orderItems);

        foreach ($this->orderItemsForRma as $orderItem) {
            $data = [];
            $data["product_id"] = $orderItem->getProductId();
            $data["product_code"] = $orderItem->getProductCode();
            $data["price"] = $orderItem->getRawPrice();
            $data["item_id"] = $orderItem->getItemId();
            $data["customer_comment"] = $orderItem->getCustomerComment();
            $data["tax_data"] = $orderItem->getTaxData();
            $data["extra"] = $orderItem->getExtra();

            $orderItemItemId = $orderItem->getItemId();
            foreach ($customReturnItems as $customReturnItem) {
                if ($customReturnItem["itemId"] === $orderItemItemId) {
                    $data["amount"] = $customReturnItem["amount"];
                    break;
                }
            }

            $rawOrderItem = new OrderItem($data);
            (new AmountItem())->hydrateFromOrderItem($order->getOrderAmounts(), $rawOrderItem);

            $exposedOrderItem = $rawOrderItem->expose();
            $exposedOrderItem["amountExcludingTax"] = $rawOrderItem->getAmountExcludingTax();
            $exposedOrderItem["priceWithoutTaxes"] = $rawOrderItem->getPriceWithoutTaxes();
            $exposedOrderItem["greenTaxAmount"] = $rawOrderItem->getGreenTaxAmount();

            $rowsOrderItem[] = $rawOrderItem;
            $rmaItems[] = $exposedOrderItem;

            $countRmaItems += $data["amount"];
        }

        $this->setAmounts($rowsOrderItem);

        $exposedData = [];
        $exposedData['number'] = $this->getRmaNumber();
        $exposedData['amountExcludingTaxes'] = $this->amounts->getAmountExcludingTaxes();
        $exposedData['greenTaxAmount'] = $this->getGreenTaxAmountForRma();

        $taxRates = $this->amounts->getTaxRates();
        $taxRatesCount = \count($taxRates);

        /*
         * Afin d'éviter les problèmes d'arrondis sur l'affichage dans la partie des totaux du PDF,
         * la dernière ligne indiquant les montants relatifs aux taux des taxes est 'ajustée'
         * Ainsi si on fait le total du montant HT et des différentes montants des taxes on tombe bien sur le bon total TTC
         */

        // arrondit un 'Money' avec une faible précision
        $roundMoney = function (Money $money) {
            return new Money($money->getConvertedAmount() * 100);
        };

        // on ne conserve pas la dernière valeur qui sera recalculée en soustrayant le total des montants des autres taxes du total TTC
        $taxRatesButLastValue = $taxRates;
        array_pop($taxRatesButLastValue);

        $amountsFromTax = array_reduce(
            $taxRatesButLastValue ?? [],
            function (Money $amountsFromTax, TaxRate $taxRate) use ($roundMoney): Money {
                return $amountsFromTax->add($roundMoney($this->amounts->getTaxRateAmount($taxRate)));
            },
            new Money(0)
        );

        // créé les lignes des taxes et de leurs montants à afficher sur le PDF
        $exposedData['taxLines'] = array_map(
            function ($taxRate, $key) use ($roundMoney, $taxRatesCount, $amountsFromTax) {

                if ($key < $taxRatesCount - 1) {
                    $taxAmount = $roundMoney($this->amounts->getTaxRateAmount($taxRate));
                } else {
                    // la dernière itération permet d'ajuster l'arrondi
                    $taxAmount = $this->getAmountIncludingTaxes()
                        ->subtract($amountsFromTax)
                        ->subtract($this->amounts->getAmountExcludingTaxes());
                }

                return [
                    "taxRate" => $taxRate,
                    "taxAmount" => $taxAmount,
                ];
            },
            $taxRates,
            array_keys($taxRates)
        );

        // si tous les éléments de la commande ont été retournés, on rembourse également les frais de livraison
        $exposedData['shippingCostWithoutTax'] = new Money(0);
        $exposedData['orderIsFullyReturned'] = false;

        if ($countOrderItems === $countRmaItems) {
            $exposedData['shippingCostWithoutTax'] = $order->getShippingCostWithoutTax();
            $exposedData['orderIsFullyReturned'] = true;
        }

        $exposedData['items'] = $rmaItems;
        $exposedData['total'] = $this->getAmountIncludingTaxes()->add($exposedData['shippingCostWithoutTax']);

        // convertit les items de type Money en float pour l'affichage
        array_walk_recursive($exposedData, function (&$item, $key) use ($priceFormatter) {
            if ($item instanceof Money) {
                $item = $priceFormatter->formatFloat($item->getConvertedAmount());
            }
        });

        return $exposedData;
    }

    /**
     * The total calculated in `exposeForCustomRma()` is utterly complicated and... wrong,
     * as it includes the shipping costs, which are not included in orders returns screen nor in default RMA PDF.
     */
    public function getItemsTotal(): Money
    {
        return array_reduce(
            array_values($this->items),
            function (?Money $carry, ReturnItem $item): Money {
                return ($carry ?? new Money(0))
                    ->add(Money::fromVariable($item->getPrice())
                    ->multiply($item->getAmount()))
                ;
            }
        ) ?? new Money(0);
    }

    /** @return Money */
    protected function getAmountIncludingTaxes(): Money
    {
        $taxAmountsForRma = $this->getTaxAmountsForRma();
        $amountExcludingTaxes = $this->amounts->getAmountExcludingTaxes();

        return $amountExcludingTaxes->add($taxAmountsForRma);
    }

    private function setOrderItemsForRma($customReturnItems, $orderItems)
    {
        $orderItemsForRma = [];

        foreach ($customReturnItems as $customReturnItem) {
            foreach ($orderItems as $orderItem) {
                if ($customReturnItem["itemId"] === $orderItem->getItemId()) {
                    $orderItemsForRma[] = $orderItem;
                    break;
                }
            }
        }

        $this->orderItemsForRma = $orderItemsForRma;
    }

    private function setAmounts(array $orders = [])
    {
        // on ajoute chaque ligne de la commande dans l'objet Amounts afin de
        // calculer tous les montants nécessaire à la commande
        $this->amounts = array_reduce(
            $orders,
            function (Amounts $amounts, OrderItem $orderItem): Amounts {
                return $amounts->withLine($orderItem->asLine());
            },
            new Amounts()
        );
    }

    /**
     * @return Money
     */
    private function getTaxAmountsForRma(): Money
    {
        return array_reduce(
            $this->amounts->getTaxRates(),
            function (Money $taxRateAmounts, TaxRate $taxRate) {
                $amount = $this->amounts->getTaxRateAmount($taxRate);

                return $taxRateAmounts->add($amount);
            },
            new Money(0)
        );
    }

    private function getGreenTaxAmountForRma()
    {
        $greenTax = new Money(0);

        foreach ($this->orderItemsForRma as $orderItem) {
            $greenTaxAmount = $orderItem->getGreenTaxAmount();
            $quantity = $orderItem->getQuantity();
            $greenTax = $greenTax->add($greenTaxAmount->multiply($quantity));
        }

        return $greenTax;
    }
}
