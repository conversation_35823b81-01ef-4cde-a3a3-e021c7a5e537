<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Order\OrderReturn;

/**
 * Entité qui contient la raison du retour
 * (colis endommagé, contenu non conforme à la commande, etc)
 */
class OrderReturnReason
{
    /** @var  int */
    private $id;
    /** @var  int */
    private $position;
    /** @var  string */
    private $name;
    /** @var  string */
    private $status;

    public function __construct(int $id, int $position, string $name, string $status)
    {
        $this->id = $id;
        $this->position = $position;
        $this->name = $name;
        $this->status = $status;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getPosition(): int
    {
        return $this->position;
    }

    public function getName(): string
    {
        return $this->name;
    }
}
