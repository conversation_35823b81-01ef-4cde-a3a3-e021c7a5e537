<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Order\OrderReturn;

class ReturnItem
{
    /** @var  string */
    private $itemId;

    /** @var  string */
    private $productId;

    /** @var  integer */
    private $reason;

    /** @var  integer */
    private $amount;

    /** @var  string */
    private $product;

    /** @var  float */
    private $price;

    public function __construct(string $itemId, string $productId, int $reason, int $amount, string $product = null, float $price = null)
    {
        $this->itemId = $itemId;
        $this->productId = $productId;
        $this->reason = $reason;
        $this->amount = $amount;
        $this->product = $product;
        $this->price = $price;
    }

    public function getProduct(): string
    {
        return $this->product;
    }

    public function getPrice(): float
    {
        return $this->price;
    }

    public function getItemId(): string
    {
        return $this->itemId;
    }

    public function getProductId(): string
    {
        return $this->productId;
    }

    public function getReason(): int
    {
        return $this->reason;
    }

    public function getAmount(): int
    {
        return $this->amount;
    }

    public static function fromCscartData(array $returnItem): ReturnItem
    {
        $object = new self(
            (int) $returnItem['item_id'],
            (string) $returnItem['product_id'],
            (string) $returnItem['reason'],
            (int) $returnItem['amount'],
            $returnItem['product'] ?? null,
            (float) $returnItem['price'] ?? null
        );

        return $object;
    }
}
