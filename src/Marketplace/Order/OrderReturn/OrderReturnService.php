<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Order\OrderReturn;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\ParameterType;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderReturn\Exception\OrderReturnCantBeEmpty;
use Wizacha\Marketplace\Order\OrderService;

class OrderReturnService
{
    /** @var  Connection */
    private $db;

    /** @var OrderService */
    private OrderService $orderService;

    public function __construct(Connection $db, OrderService $orderService)
    {
        $this->db = $db;
        $this->orderService = $orderService;
    }

    /**
     * @return OrderReturnReason[]
     */
    public function getReasons(): array
    {
        $rmaTypes = fn_get_rma_properties('R', (string) GlobalState::contentLocale(), true);

        return array_map(
            function ($data) {
                return new OrderReturnReason(\intval($data['property_id']), \intval($data['position']), $data['property'], $data['status']);
            },
            $rmaTypes
        );
    }

    /**
     * @param ReturnItem[] $items
     * @throws OrderReturnCantBeEmpty
     * @return int OrderReturn id
     */
    public function createOrderReturn(int $orderId, int $userId, string $comment, array $items): int
    {
        if (empty($items)) {
            throw new OrderReturnCantBeEmpty();
        }
        $oderLangCode = db_get_field("SELECT lang_code FROM ?:orders WHERE order_id = ?i", $orderId);
        $action = '2'; // Rembourser. Obscure legacy value

        $totalAmount = 0;
        foreach ($items as $item) {
            $totalAmount += $item->getAmount();
        }

        $_data = array(
            'order_id' => $orderId,
            'user_id' => $userId,
            'action' => $action,
            'timestamp' => TIME,
            'status' => RMA_DEFAULT_STATUS,
            'total_amount' => $totalAmount,
            'comment' => $comment,
        );
        $returnId = db_query('INSERT INTO ?:rma_returns ?e', $_data);

        $orderItems = db_get_hash_array("SELECT item_id, order_id, extra, price, amount FROM ?:order_details WHERE order_id = ?i", 'item_id', $orderId);
        foreach ($items as $return) {
            if (true == fn_rma_declined_product_correction($orderId, $return->getItemId(), $orderItems[$return->getItemId()]['amount'], $return->getAmount())) {
                $_item = $orderItems[$return->getItemId()];
                $extra = unserialize($_item['extra']);
                $_data = array (
                    'return_id' => $returnId,
                    'item_id' => $return->getItemId(),
                    'product_id' => $return->getProductId(),
                    'reason' => !empty($return->getReason()) ? $return->getReason() : '',
                    'amount' => $return->getAmount(),
                    'product_options' => !empty($extra['product_options_value']) ? serialize($extra['product_options_value']) : '',
                    'price' => fn_format_price((((!isset($extra['exclude_from_calculate'])) ? $_item['price'] : 0) * $_item['amount']) / $_item['amount']),
                    'product' => !empty($extra['product']) ? $extra['product'] : fn_get_product_name($return->getProductId(), $oderLangCode),
                );

                db_query('INSERT INTO ?:rma_return_products ?e', $_data);

                if (!isset($extra['returns'])) {
                    $extra['returns'] = array();
                }
                $extra['returns'][$returnId] = array(
                    'amount' => $return->getAmount(),
                    'status' => RMA_DEFAULT_STATUS,
                );
                db_query('UPDATE ?:order_details SET ?u WHERE item_id = ?i AND order_id = ?i', array('extra' => serialize($extra)), $return->getItemId(), $orderId);
            }
        }

        //Send mail
        $returnInfo = fn_get_return_info($returnId);
        $orderInfo = $this->orderService->overrideLegacyOrder($orderId);

        fn_send_return_mail($returnInfo, $orderInfo, array('C' => true, 'A' => false, 'V' => true));

        return $returnId;
    }

    /** @return OrderReturn[] */
    public function getReturns(int $limit = null, $offset = null): array
    {
        $sql = 'SELECT return_id FROM cscart_rma_returns';
        $params = $types = [];
        if ($limit !== null) {
            $sql .= ' LIMIT :limit ';
            $params['limit'] = $limit;
            $types['limit'] = ParameterType::INTEGER;
        }

        if ($offset !== null) {
            $sql .= ' OFFSET :offset ';
            $params['offset'] = $offset;
            $types['offset'] = ParameterType::INTEGER;
        }

        $result = $this->db->fetchAll($sql, $params, $types);

        return $this->buildOrderReturnsFromIds(array_column($result, 'return_id'));
    }

    /** @return OrderReturn[] */
    public function getReturnsByUser(int $userId): array
    {
        $result = $this->db->fetchAll(
            'SELECT return_id
             FROM cscart_rma_returns
             WHERE user_id=:userId
            ',
            ['userId' => $userId]
        );

        return $this->buildOrderReturnsFromIds(array_column($result, 'return_id'));
    }

    /** @return OrderReturn[] */
    public function getReturnsByOrder(Order $order): array
    {
        $result = $this->db->fetchAll(
            'SELECT return_id
             FROM cscart_rma_returns
             WHERE order_id=:orderId
             ORDER BY timestamp ASC
            ',
            ['orderId' => $order->getId()]
        );

        return $this->buildOrderReturnsFromIds(array_column($result, 'return_id'));
    }

    public function getReturn(int $returnId): OrderReturn
    {
        return $this->buildOrderReturnFromId($returnId);
    }

    public function countRmaNumbersFromCompany(int $companyId): int
    {
        $query = <<<SQL
        SELECT COUNT(rma_number)
        FROM cscart_rma_returns r
        LEFT JOIN cscart_orders o ON r.order_id = o.order_id
        WHERE o.company_id = :companyId AND r.rma_number IS NOT NULL
SQL;

        $statement = $this->db->prepare($query);
        $statement->bindValue('companyId', $companyId, \PDO::PARAM_INT);
        $statement->execute();

        return (int) $statement->fetchColumn();
    }

    /**
     * @param int[] $orderReturnIds
     * @return OrderReturn[]
     */
    protected function buildOrderReturnsFromIds(array $orderReturnIds): array
    {
        return array_map(function (int $orderReturnId): OrderReturn {
            return $this->buildOrderReturnFromId($orderReturnId);
        }, $orderReturnIds);
    }

    protected function buildOrderReturnFromId(int $orderReturnId): OrderReturn
    {
        $csCartData = fn_get_return_info($orderReturnId);

        $dateTime = new \DateTime();
        $dateTime->setTimestamp($csCartData['timestamp']);
        $items = array_map(
            function ($item) {
                return ReturnItem::fromCscartData($item);
            },
            $csCartData['items']['A'] ?? []
        );

        if ($csCartData['extra'] !== null) {
            $extra = unserialize($csCartData['extra']);

            if ($csCartData['rma_number'] !== null) {
                $rmaNumber = $csCartData['rma_number'];
            } else {
                $rmaNumber = $extra['rma_number'] ?? null;
            }
        }

        return new OrderReturn(
            (int) $csCartData['return_id'],
            (int) $csCartData['order_id'],
            (int) $csCartData['user_id'],
            $dateTime,
            (string) $csCartData['status'],
            (string) $csCartData['comment'],
            $rmaNumber ?? null,
            $items
        );
    }
}
