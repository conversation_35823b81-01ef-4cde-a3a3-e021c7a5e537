<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\OrderReturn\Event;

use Symfony\Contracts\EventDispatcher\Event;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Wizacha\Component\Notification\NotificationEvent;

abstract class RmaEvent extends Event implements NotificationEvent
{
    /**
     * @var int
     */
    private $rmaId;

    /**
     * @var bool
     */
    private $notifyCustomer;

    /**
     * @var bool
     */
    private $notifyCompany;

    /**
     * @var bool
     */
    private $notifyAdmin;

    public function __construct(int $rmaId, bool $notifyCustomer, bool $notifyCompany, bool $notifyAdmin)
    {
        $this->rmaId = $rmaId;
        $this->notifyCustomer = $notifyCustomer;
        $this->notifyCompany = $notifyCompany;
        $this->notifyAdmin = $notifyAdmin;
    }

    public static function buildDebugForm(FormBuilder $form)
    {
        $form->add('rmaId', IntegerType::class);
    }

    public static function createFromForm(Form $form)
    {
        $rmaId = $form->getData()['rmaId'];

        return new static($rmaId, true, true, true);
    }

    public function getRmaId(): int
    {
        return $this->rmaId;
    }

    public function getNotifyCustomer(): bool
    {
        return $this->notifyCustomer;
    }

    public function getNotifyCompany(): bool
    {
        return $this->notifyCompany;
    }

    public function getNotifyAdmin(): bool
    {
        return $this->notifyAdmin;
    }
}
