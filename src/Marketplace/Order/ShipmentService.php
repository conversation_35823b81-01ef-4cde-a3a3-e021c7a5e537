<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order;

use Doctrine\DBAL\Connection;

class ShipmentService
{
    /** @var Connection */
    private $connection;

    /** @var OrderService */
    private $orderService;

    public function __construct(
        Connection $connection,
        OrderService $orderService
    ) {
        $this->connection = $connection;
        $this->orderService = $orderService;
    }

    public function updateShipmentDeliveryDate($shipmentId): void
    {
        $update = $this
            ->connection->prepare(
                'UPDATE cscart_shipments set delivery_date = Now() WHERE shipment_id = :shipmentId'
            );

        $update->execute([
            'shipmentId' => $shipmentId,
        ]);
    }

    public function updateAllShipmentDeliveryDate(int $orderId): void
    {
        $shipmentsToUpdate = [];

        foreach ($this->orderService->getOrderShipments($orderId) as $shipment) {
            if ($shipment['delivery_date'] === null) {
                \array_push($shipmentsToUpdate, $shipment['shipment_id']);
            }
        }

        if (\count($shipmentsToUpdate) > 0) {
            if (\count($shipmentsToUpdate) > 1) {
                $in = str_repeat('?,', \count($shipmentsToUpdate) - 1) . '?';
            } else {
                $in = '?';
            }

            $update = $this
                ->connection->prepare(
                    "UPDATE cscart_shipments set delivery_date = NOW() WHERE shipment_id IN($in)"
                );

            $update->execute($shipmentsToUpdate);
        }
    }
}
