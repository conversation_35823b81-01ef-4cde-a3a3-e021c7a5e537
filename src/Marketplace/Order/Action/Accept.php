<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Action;

use Doctrine\DBAL\Connection;
use Psr\Log\LoggerInterface;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Order\Exception\ActionNotAllowed;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Tracer\Tracer;
use Wizacha\Marketplace\Order\Workflow\ModuleName;
use Wizacha\Marketplace\Order\Workflow\WorkflowService;

/**
 * Le vendeur valide la commande
 */
class Accept extends Action
{
    protected Connection $connection;

    protected WorkflowService $workflowService;

    protected LoggerInterface $logger;

    private Tracer $tracer;

    private CompanyService $companyService;

    public function __construct(
        Connection $connection,
        WorkflowService $workflowService,
        LoggerInterface $logger,
        Tracer $tracer,
        CompanyService $companyService
    ) {
        parent::__construct($connection, $workflowService, $logger, $tracer);

        $this->companyService = $companyService;
    }

    public function execute(Order $order): void
    {
        if (!$this->isAllowedForExecution($order)) {
            throw new ActionNotAllowed();
        }

        $this->connection->update('cscart_orders', ['accepted_by_vendor' => true], ['order_id' => $order->getId()]);

        $order->acceptByVendor();

        //insert an automatic invoice_number if not present and requests are fullfilled
        $isAutomaticBillingNumberEnabled
            = container()->getParameter('feature.activate_billing_number_auto_generation') === true
            && $order->getCompany()->hasAutomaticBillingNumber() === true
            && $order->doNotCreateInvoiceNumber() !== true; // doNotCreateInvoiceNumber returns ?bool

        if ($order->getInvoiceNumber() === null
            && $isAutomaticBillingNumberEnabled === true
            && $order->pendingInvoice() === true
            && $this->companyService->isInvoicingDisabled($order->getCompanyId()) === false
        ) {
            $invoiceNumber = container()->get('marketplace.order.billing_number_generator')->generate($order->getId());

            $this->connection->update('cscart_orders', [
                'w_invoice_number' => $invoiceNumber,
                'invoice_number_provided' => true,
                'invoice_date' => 'NOW()'
            ], ['order_id' => $order->getId()]);
        }

        if ($this->companyService->isInvoicingDisabled($order->getCompanyId()) === true) {
            $this->connection->update('cscart_orders', ['do_not_create_invoice' => "1"], ['order_id' => $order->getId()]);
        }

        $this->log($order);
        $this->processWorkflow($order);
    }

    public function getAllowedModules(): array
    {
        return [
            ModuleName::ORDER_VALIDATION(),
        ];
    }

    public function getActionName(): ActionName
    {
        return ActionName::ACCEPT();
    }
}
