<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Action;

use Wizacha\Marketplace\Order\Exception\ActionNotAllowed;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Workflow\ModuleName;

/**
 * Indique que le client a saisi un numéro de facture
 */
class ProvideInvoiceNumber extends Action
{
    public function execute(Order $order, string $invoiceNumber): void
    {
        if (!$this->isAllowedForExecution($order)) {
            throw new ActionNotAllowed();
        }

        $this->connection->update(
            'cscart_orders',
            [
                'w_invoice_number' => $invoiceNumber,
                'invoice_number_provided' => true,
                'invoice_date' => (new \DateTime())->format('Y-m-d H:i:s'),
            ],
            ['order_id' => $order->getId()]
        );

        $order->provideInvoiceNumber($invoiceNumber);

        $this->log($order);
        $this->processWorkflow($order);
    }

    public function getAllowedModules(): array
    {
        return [
            ModuleName::ORDER_PREPARATION(),
        ];
    }

    public function getActionName(): ActionName
    {
        return ActionName::PROVIDEINVOICENUMBER();
    }
}
