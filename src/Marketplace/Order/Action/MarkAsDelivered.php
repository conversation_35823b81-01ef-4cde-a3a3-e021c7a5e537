<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Action;

use Wizacha\Marketplace\Order\Exception\ActionNotAllowed;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\ShipmentService;
use Wizacha\Marketplace\Order\Workflow\ModuleName;

/**
 * Le colis est livré
 */
class MarkAsDelivered extends Action
{
    /** @var ShipmentService */
    private $shipmentService;

    public function execute(Order $order): void
    {
        if (!$this->isAllowedForExecution($order)) {
            throw new ActionNotAllowed();
        }

        $currentDate = new \DateTimeImmutable();

        $this->connection->update(
            'cscart_orders',
            [
                'delivered' => true,
                'delivery_date' => $currentDate,
            ],
            ['order_id' => $order->getId()],
            [
                'delivered' => 'boolean',
                'delivery_date' => 'datetime',
            ]
        );

        $this->shipmentService->updateAllShipmentDeliveryDate($order->getId());

        $order->markAsDelivered($currentDate);

        $this->log($order);
        $this->processWorkflow($order);
    }

    public function getAllowedModules(): array
    {
        return [
            ModuleName::DELIVERY(),
        ];
    }

    public function setShipmentService(ShipmentService $shipmentService): void
    {
        $this->shipmentService = $shipmentService;
    }

    public function getActionName(): ActionName
    {
        return ActionName::MARKASDELIVERED();
    }
}
