<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Action;

use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\Order\Workflow\ModuleName;

class MarkAsFinished extends Action
{
    public function execute(Order $order): void
    {
        $this->connection->update('cscart_orders', ['funds_dispatched' => true], ['order_id' => $order->getId()]);
        $this->connection->update('cscart_orders', ['status' => OrderStatus::COMPLETED], ['order_id' => $order->getId()]);

        $this->log($order);
    }

    public function getAllowedModules(): array
    {
        return [
            ModuleName::FUNDS_DISPATCH(),
        ];
    }

    public function getActionName(): ActionName
    {
        return ActionName::MARKASFINISHED();
    }
}
