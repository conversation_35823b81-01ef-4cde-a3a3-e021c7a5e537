<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Action;

use Wizacha\Marketplace\Order\Exception\ActionNotAllowed;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Workflow\ModuleName;

/**
 * Le paiement de la commande est refusé
 */
class MarkPaymentAsRefused extends Action
{
    public function execute(Order $order): void
    {
        if (!$this->isAllowedForExecution($order)) {
            throw new ActionNotAllowed();
        }

        $this->connection->update('cscart_orders', ['is_paid' => false], ['order_id' => $order->getId()]);

        $order->markPaymentAsRefused();

        $this->log($order);
        $this->processWorkflow($order);
    }

    public function getAllowedModules(): array
    {
        return [
            ModuleName::CREDIT_CARD_PAYMENT(),
            ModuleName::WAIT_PAYMENT_DEFERMENT(),
            ModuleName::BANK_TRANSFER_PAYMENT(),
        ];
    }

    public function getActionName(): ActionName
    {
        return ActionName::MARKPAYMENTASREFUSED();
    }
}
