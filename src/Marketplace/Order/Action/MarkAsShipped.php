<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Action;

use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Exception\ActionNotAllowed;
use Wizacha\Marketplace\Order\Workflow\ModuleName;

/**
 * Le colis est envoyé
 */
class MarkAsShipped extends Action
{
    public function execute(Order $order): void
    {
        if (!$this->isAllowedForExecution($order)) {
            throw new ActionNotAllowed();
        }

        $currentDate = new \DateTimeImmutable();

        $this->connection->update('cscart_orders', ['shipping_date' => $currentDate], ['order_id' => $order->getId()], ['shipping_date' => 'datetime']);

        $order->markAsShipped($currentDate);

        $this->log($order);
        $this->processWorkflow($order);
    }

    public function getAllowedModules(): array
    {
        return [
            ModuleName::ORDER_PREPARATION(),
        ];
    }

    public function getActionName(): ActionName
    {
        return ActionName::MARKASSHIPPED();
    }
}
