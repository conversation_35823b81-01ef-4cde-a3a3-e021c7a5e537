<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Action;

use Wizacha\Marketplace\Order\Exception\ActionNotAllowed;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Workflow\ModuleName;

class MarkPaymentAuthorizationAsCaptured extends Action
{
    public function execute(Order $order): void
    {
        if ($this->isAllowedForExecution($order) === false) {
            throw new ActionNotAllowed();
        }

        $this->connection->update('cscart_orders', ['is_capture' => true], ['order_id' => $order->getId()]);

        $order->markPaymentAuthorizationAsCaptured();

        $this->log($order);
        $this->processWorkflow($order);
    }

    /**
     * @return ModuleName[]
     */
    public function getAllowedModules(): array
    {
        return [
            ModuleName::CREDIT_CARD_PAYMENT_CAPTURE(),
        ];
    }

    public function getActionName(): ActionName
    {
        return ActionName::MARKPAYMENTAUTHORIZATIONASCAPTURED();
    }
}
