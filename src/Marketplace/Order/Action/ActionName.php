<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Action;

use MyCLabs\Enum\Enum;

/**
 * @method static ActionName ACCEPT()
 * @method static ActionName CANCEL()
 * @method static ActionName COMMITTO()
 * @method static ActionName CONFIRM()
 * @method static ActionName DECLAREINVOICENUMBERGENERATEDELSEWHERE()
 * @method static ActionName DECLAREPARCELLOST()
 * @method static ActionName DISPATCHFUNDS()
 * @method static ActionName DISPATCHFUNDSFAILED()
 * @method static ActionName DISPATCHFUNDSSUCCEEDED()
 * @method static ActionName ENDWITHDRAWALPERIOD()
 * @method static ActionName MARKASDELIVERED()
 * @method static ActionName MARKASFINISHED()
 * @method static ActionName MARKASPAID()
 * @method static ActionName MARKASSHIPPED()
 * @method static ActionName MARKPAYMENTASREFUSED()
 * @method static ActionName MARKPAYMENTAUTHORIZATIONASCAPTURED()
 * @method static ActionName MARKPAYMENTAUTHORIZATIONASREFUSED()
 * @method static ActionName MARKPAYMENTDEFERMENTASAUTHORIZED()
 * @method static ActionName MARKPAYMENTDEFERMENTASREFUSED()
 * @method static ActionName PROVIDEINVOICENUMBER()
 * @method static ActionName REDIRECTTOPAYMENTPROCESSOR()
 * @method static ActionName REFUSE()
 * @method static ActionName TRASH()
 * @method static ActionName TRANSFERMARKETPLACEDISCOUNTFAILED()
 */
class ActionName extends Enum
{
    private const ACCEPT = 'accept';
    private const CANCEL = 'cancel';
    private const COMMITTO = 'commit_to';
    private const CONFIRM = 'confirm';
    private const DECLAREINVOICENUMBERGENERATEDELSEWHERE = 'declare_invoice_number_generated_elsewhere';
    private const DECLAREPARCELLOST = 'declare_parcel_lost';
    private const DISPATCHFUNDS = 'dispatch_funds';
    private const DISPATCHFUNDSFAILED = 'dispatch_funds_failed';
    private const DISPATCHFUNDSSUCCEEDED = 'dispatch_funds_succeeded';
    private const ENDWITHDRAWALPERIOD = 'end_withdrawal_period';
    private const MARKASDELIVERED = 'mark_as_delivered';
    private const MARKASFINISHED = 'mark_as_finished';
    private const MARKASPAID = 'mark_as_paid';
    private const MARKASSHIPPED = 'mark_as_shipped';
    private const MARKPAYMENTASREFUSED = 'mark_payment_as_refused';
    private const MARKPAYMENTAUTHORIZATIONASCAPTURED = 'mark_payment_authorization_captured';
    private const MARKPAYMENTAUTHORIZATIONASREFUSED = 'mark_payment_authorization_refused';
    private const MARKPAYMENTDEFERMENTASAUTHORIZED = 'mark_payment_deferment_as_authorized';
    private const MARKPAYMENTDEFERMENTASREFUSED = 'mark_payment_deferment_as_refused';
    private const PROVIDEINVOICENUMBER = 'provide_invoice_number';
    private const REDIRECTTOPAYMENTPROCESSOR = 'redirect_to_payment_processor';
    private const REFUSE = 'refuse';
    private const TRASH = 'trash';
    private const TRANSFERMARKETPLACEDISCOUNTFAILED = 'transfer_marketplace_discount_failed';
}
