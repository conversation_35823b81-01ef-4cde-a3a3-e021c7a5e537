<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Action;

use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Exception\ActionNotAllowed;
use Wizacha\Marketplace\Order\Workflow\ModuleName;

/**
 * Le colis est perdu/retourné
 */
class DeclareParcelLost extends Action
{
    public function execute(Order $order): void
    {
        if (!$this->isAllowedForExecution($order)) {
            throw new ActionNotAllowed();
        }

        $currentDate = new \DateTimeImmutable();

        $this->connection->update(
            'cscart_orders',
            [
                'delivered' => false,
                'delivery_date' => $currentDate,
            ],
            ['order_id' => $order->getId()],
            [
                'delivered' => 'boolean',
                'delivery_date' => 'datetime',
            ]
        );

        $order->declareParcelLost($currentDate);

        $this->log($order);
        $this->processWorkflow($order);
    }

    public function getAllowedModules(): array
    {
        return [
            ModuleName::DELIVERY(),
        ];
    }

    public function getActionName(): ActionName
    {
        return ActionName::DECLAREPARCELLOST();
    }
}
