<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Action;

use Tygh\Registry;
use Wizacha\Marketplace\Order\Exception\ActionNotAllowed;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Workflow\ModuleName;
use Wizacha\Marketplace\PIM\Stock\StockService;

/**
 * The order is confirmed by the marketplace
 */
class Confirm extends Action
{
    /** @var StockService */
    private $stockService;

    public function execute(Order $order): void
    {
        if (!$this->isAllowedForExecution($order)) {
            throw new ActionNotAllowed();
        }

        $this->connection->update('cscart_orders', ['confirmed' => true], ['order_id' => $order->getId()]);
        $order->confirm();

        if (Registry::get('settings.General.inventory_tracking') === 'Y') {
            $this->decrementStock($order);
        }

        $this->log($order);
        $this->processWorkflow($order);
    }

    public function getAllowedModules(): array
    {
        return [
            ModuleName::ORDER_CONFIRMATION(),
        ];
    }

    public function setStockService(StockService $stockService)
    {
        $this->stockService = $stockService;
    }

    private function decrementStock(Order $order)
    {
        $this->stockService->decrementStock($order);
    }

    public function getActionName(): ActionName
    {
        return ActionName::CONFIRM();
    }
}
