<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Action;

use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Exception\ActionNotAllowed;
use Wizacha\Marketplace\Order\Workflow\ModuleName;

/**
 * Le vendeur refuse la commande
 */
class Refuse extends Action
{
    public function execute(Order $order): void
    {
        if (!$this->isAllowedForExecution($order)) {
            throw new ActionNotAllowed();
        }

        $this->connection->update('cscart_orders', ['accepted_by_vendor' => false], ['order_id' => $order->getId()]);

        $order->refuseByVendor();

        $this->log($order);
        $this->processWorkflow($order);
    }

    public function getAllowedModules(): array
    {
        return [
            ModuleName::ORDER_VALIDATION(),
        ];
    }

    public function getActionName(): ActionName
    {
        return ActionName::REFUSE();
    }
}
