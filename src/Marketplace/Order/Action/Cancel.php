<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Action;

use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Workflow\ModuleName;
use Wizacha\Marketplace\Order\Workflow\StepName;

/**
 * Le vendeur annule la commande
 */
class Cancel extends Action
{
    public function execute(Order $order): void
    {
        $this->connection->update('cscart_orders', ['canceled' => true], ['order_id' => $order->getId()]);

        $order->cancel();

        $this->log($order);
    }

    public function isAllowed(Order $order): bool
    {
        return $order->getWorkflowProgress()->isProcessing() === true
            && $order->isCanceled() === false
            && $order->getWorkflowProgress()->getModuleName()->equals(ModuleName::WAIT_PAYMENT_DEFERMENT()) === false
            && $order->getWorkflowProgress()->getStepName()->equals(StepName::PENDING_BANK_VALIDATION()) === false;
    }

    public function getAllowedModules(): array
    {
        // On laisse ce tableau vide puisque pour l'instant car on autorise
        // toujours l'annulation d'une commande, je ne connais les règles de
        // gestion de cette action
        return [];
    }

    public function getActionName(): ActionName
    {
        return ActionName::CANCEL();
    }
}
