<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Action;

use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\Order\Workflow\ModuleName;

/**
 * Les fonds ne sont pas bien répartis entre les différentes parties (vendeur, admin et PSP)
 */
class DispatchFundsFailed extends Action
{
    public function execute(Order $order): void
    {
        $this->connection->update('cscart_orders', ['funds_dispatched' => false], ['order_id' => $order->getId()]);
        $this->connection->update('cscart_orders', ['status' => OrderStatus::PROCESSED], ['order_id' => $order->getId()]);

        $order->dispatchFundsFailed();

        $this->deleteTrace($order, 'DispatchFunds');
        $this->log($order);
    }

    public function getAllowedModules(): array
    {
        return [
            ModuleName::FUNDS_DISPATCH(),
        ];
    }

    public function getActionName(): ActionName
    {
        return ActionName::DISPATCHFUNDSFAILED();
    }
}
