<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Action;

use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Exception\ActionNotAllowed;
use Wizacha\Marketplace\Order\Workflow\ModuleName;

/**
 * La commande est payée
 */
class MarkAsPaid extends Action
{
    public function execute(Order $order): void
    {
        if (!$this->isAllowedForExecution($order)) {
            throw new ActionNotAllowed();
        }

        $this->connection->update('cscart_orders', ['is_paid' => true], ['order_id' => $order->getId()]);

        $order->markAsPaid();

        $this->log($order);
        $this->processWorkflow($order);
    }

    public function getAllowedModules(): array
    {
        return [
            ModuleName::MANUAL_PAYMENT(),
            ModuleName::CREDIT_CARD_PAYMENT(),
            ModuleName::CREDIT_CARD_PAYMENT_AUTHORIZATION(),
            ModuleName::BANK_TRANSFER_PAYMENT(),
            ModuleName::WAIT_PAYMENT_DEFERMENT(),
        ];
    }

    public function getActionName(): ActionName
    {
        return ActionName::MARKASPAID();
    }
}
