<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Action;

use Wizacha\Marketplace\Order\Order;

class TransferMarketplaceDiscountFailed extends Action
{
    public function execute(Order $order)
    {
        $this->log($order);
    }

    public function getAllowedModules(): array
    {
        return [];
    }

    public function getActionName(): ActionName
    {
        return ActionName::TRANSFERMARKETPLACEDISCOUNTFAILED();
    }
}
