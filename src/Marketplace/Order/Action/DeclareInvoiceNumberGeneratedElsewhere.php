<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Action;

use Wizacha\Marketplace\Order\Exception\ActionNotAllowed;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Workflow\ModuleName;

/**
 * La facture est générée en dehors de la marketplace,
 * ce n'est donc plus notre problème et nous ne stockons
 * pas de numéro de facture dans la marketplace.
 */
class DeclareInvoiceNumberGeneratedElsewhere extends Action
{
    public function execute(Order $order): void
    {
        if (!$this->isAllowedForExecution($order)) {
            throw new ActionNotAllowed();
        }

        $this->connection->update(
            'cscart_orders',
            [
                'w_invoice_number' => null,
                'invoice_number_provided' => false,
                'do_not_create_invoice' => true,
                'invoice_date' => (new \DateTime())->format('Y-m-d H:i:s'),
            ],
            ['order_id' => $order->getId()]
        );

        $order->declareInvoiceNumberGeneratedElsewhere();

        $this->log($order);
        $this->processWorkflow($order);
    }

    public function getAllowedModules(): array
    {
        return [
            ModuleName::ORDER_PREPARATION(),
        ];
    }

    public function getActionName(): ActionName
    {
        return ActionName::DECLAREINVOICENUMBERGENERATEDELSEWHERE();
    }
}
