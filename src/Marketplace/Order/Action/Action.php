<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Action;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Types\Type;
use Psr\Log\LoggerInterface;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Tracer\Tracer;
use Wizacha\Marketplace\Order\Workflow\Exception\Exception;
use Wizacha\Marketplace\Order\Workflow\ModuleName;
use Wizacha\Marketplace\Order\Workflow\WorkflowService;

abstract class Action implements OrderWorkflowActionInterface
{
    protected Connection $connection;

    protected WorkflowService $workflowService;

    protected LoggerInterface $logger;

    private Tracer $tracer;

    public function __construct(
        Connection $connection,
        WorkflowService $workflowService,
        LoggerInterface $logger,
        Tracer $tracer
    ) {
        $this->connection = $connection;
        $this->workflowService = $workflowService;
        $this->logger = $logger;
        $this->tracer = $tracer;
    }

    public function isAllowed(Order $order): bool
    {
        try {
            $order->assertIsInModules($this->getAllowedModules());

            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * @return ModuleName[]
     */
    abstract public function getAllowedModules(): array;

    abstract public function getActionName(): ActionName;

    protected function isAllowedForExecution(Order $order): bool
    {
        try {
            $order->assertIsInModules($this->getAllowedModules());
            $isAllowed = true;
        } catch (Exception $e) {
            $isAllowed = false;
        }

        return $isAllowed;
    }

    protected function processWorkflow(Order $order): void
    {
        $this->workflowService->getWorkflow($order)->process($order);

        // Sauvegarde la date de la dernière action effectuée sur le workflow
        // elle est notamment utilisée pour savoir si on doit trash la commande
        // au bout d'un certains temps
        $this->connection->update(
            'cscart_orders',
            ['workflow_last_update' => new \DateTime()],
            ['order_id' => $order->getId()],
            ['workflow_last_update' => Type::DATETIME]
        );
    }

    protected function log(Order $order): void
    {
        $namespace = explode('\\', static::class);
        $name = end($namespace);

        $this->tracer->trace($order->getId(), $name);
    }

    protected function deleteTrace(Order $order, $name): void
    {
        $this->tracer->delete($order->getId(), $name);
    }
}
