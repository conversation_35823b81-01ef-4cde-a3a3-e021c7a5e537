<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Action;

use Wizacha\Marketplace\Order\Exception\ActionNotAllowed;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\Order\Workflow\ModuleName;

class MarkPaymentAuthorizationAsRefused extends Action
{
    public function execute(Order $order): void
    {
        if ($this->isAllowedForExecution($order) === false) {
            throw new ActionNotAllowed();
        }

        $this->connection->update('cscart_orders', ['is_capture' => false], ['order_id' => $order->getId()]);

        $order->markPaymentAuthorizationAsRefused();

        fn_change_order_status($order->getId(), OrderStatus::BILLING_FAILED);

        $this->log($order);
        $this->processWorkflow($order);
    }

    /**
     * @return ModuleName[]
     */
    public function getAllowedModules(): array
    {
        return [
            ModuleName::CREDIT_CARD_PAYMENT_AUTHORIZATION(),
            ModuleName::CREDIT_CARD_PAYMENT_CAPTURE(),
            ModuleName::ORDER_PREPARATION(),
        ];
    }

    public function getActionName(): ActionName
    {
        return ActionName::MARKPAYMENTAUTHORIZATIONASREFUSED();
    }
}
