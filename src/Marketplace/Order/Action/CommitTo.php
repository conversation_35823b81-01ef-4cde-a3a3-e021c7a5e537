<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Action;

use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Exception\ActionNotAllowed;
use Wizacha\Marketplace\Order\OrderProjector;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\Order\Workflow\ModuleName;

/**
 * S'engage à payer la commande à une date précise (avec son numéro d'engagement)
 */
class CommitTo extends Action
{
    public function execute(Order $order, \DateTimeImmutable $date, string $number): void
    {
        if (!$this->isAllowedForExecution($order)) {
            throw new ActionNotAllowed();
        }

        $newStatus = (string) OrderStatus::STANDBY_VENDOR();

        $this->connection->update(
            'cscart_orders',
            [
                'commitment_date' => $date->format(\DateTime::ATOM),
                'commitment_number' => $number,
                'status' => $newStatus,
            ],
            ['order_id' => $order->getId()]
        );

        if (container()->getParameter('feature.enable_yavin') === true) {
            $orderService = container()->get('marketplace.order.order_service');
            $orderService->publishOrderCreated((string) $order->getStatus(), $newStatus, $order);
        }

        $order->commitTo($date, $number);

        $this->log($order);
        $this->processWorkflow($order);
    }

    public function getAllowedModules(): array
    {
        return [
            ModuleName::ORDER_COMMITMENT(),
        ];
    }

    public function getActionName(): ActionName
    {
        return ActionName::COMMITTO();
    }
}
