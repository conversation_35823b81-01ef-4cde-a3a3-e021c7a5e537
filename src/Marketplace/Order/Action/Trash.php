<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Action;

use Wizacha\Marketplace\Order\Order;

/**
 * La commande va à la poubelle
 */
class Trash extends Action
{
    public function execute(Order $order): void
    {
        $this->connection->update('cscart_orders', ['is_garbage' => true], ['order_id' => $order->getId()]);

        $order->trash();

        $this->log($order);
    }

    /**
     * Cette action est toujours autorisée.
     */
    public function isAllowed(Order $order): bool
    {
        return true;
    }

    /**
     * Cette action est toujours autorisée : nous n'utilisons donc pas la
     * fonction \Wizacha\Marketplace\Order\Action\Action::isAllowedForExecution
     * ce qui nous permet de retourner un tableau vide au lieu de retourner
     * un tableau contenant tous les modules (difficile à maintenir).
     */
    public function getAllowedModules(): array
    {
        return [];
    }

    public function getActionName(): ActionName
    {
        return ActionName::TRASH();
    }
}
