<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Action;

use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Exception\ActionNotAllowed;
use Wizacha\Marketplace\Order\Workflow\ModuleName;

/**
 * Le délai de rétractation est dépassé
 */
class EndWithdrawalPeriod extends Action
{
    public function execute(Order $order): void
    {
        if (!$this->isAllowedForExecution($order)) {
            throw new ActionNotAllowed();
        }

        $this->connection->update('cscart_orders', ['withdrawal_period_over' => true], ['order_id' => $order->getId()]);

        $order->endWithdrawalPeriod();

        $this->log($order);
        $this->processWorkflow($order);
    }

    public function getAllowedModules(): array
    {
        return [
            ModuleName::WITHDRAWAL_PERIOD(),
        ];
    }

    public function getActionName(): ActionName
    {
        return ActionName::ENDWITHDRAWALPERIOD();
    }
}
