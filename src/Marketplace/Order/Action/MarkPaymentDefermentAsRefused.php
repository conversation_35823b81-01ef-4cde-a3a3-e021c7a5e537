<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Action;

use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Exception\ActionNotAllowed;
use Wizacha\Marketplace\Order\Workflow\ModuleName;

/**
 * Le paiement à échéance est refusé
 */
class MarkPaymentDefermentAsRefused extends Action
{
    public function execute(Order $order): void
    {
        if (!$this->isAllowedForExecution($order)) {
            throw new ActionNotAllowed();
        }

        $this->connection->update('cscart_orders', ['payment_deferment_authorized' => false], ['order_id' => $order->getId()]);

        $order->markPaymentDefermentAsRefused();

        $this->log($order);
        $this->processWorkflow($order);
    }

    public function getAllowedModules(): array
    {
        return [
            ModuleName::PAYMENT_DEFERMENT_AUTHORIZATION(),
        ];
    }

    public function getActionName(): ActionName
    {
        return ActionName::MARKPAYMENTDEFERMENTASREFUSED();
    }
}
