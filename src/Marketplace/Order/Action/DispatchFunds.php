<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Action;

use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\Exception\ActionNotAllowed;
use Wizacha\Marketplace\Order\Workflow\ModuleName;

/**
 * Les fonds sont bien répartis entre les différentes parties (vendeur, admin et PSP)
 */
class DispatchFunds extends Action
{
    public function execute(Order $order): void
    {
        if (!$this->isAllowedForExecution($order)) {
            throw new ActionNotAllowed();
        }

        if ($order->isPaid() === false) {
            $this->logger->error(
                "DispatchFunds attempt before order payment",
                ['orderId' => $order->getId()]
            );

            throw new ActionNotAllowed();
        }

        $this->connection->update('cscart_orders', ['funds_dispatched' => true], ['order_id' => $order->getId()]);
        $order->dispatchFunds();

        $this->log($order);
        $this->processWorkflow($order);
    }

    public function getAllowedModules(): array
    {
        return [
            ModuleName::FUNDS_DISPATCH(),
        ];
    }

    public function getActionName(): ActionName
    {
        return ActionName::DISPATCHFUNDS();
    }
}
