<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order;

use LogicException;
use Wizacha\Marketplace\Order\OrderReturn\OrderReturn;
use Wizacha\Marketplace\Order\OrderReturn\OrderReturnService;

class OrderRmaNumberGenerator
{
    /** @var OrderService */
    private $orderService;

    /** @var OrderReturnService */
    private $orderReturnService;

    /** @var bool */
    private $isAutomaticBillingNumberGenerationActivated;

    public function __construct(
        OrderService $orderService,
        OrderReturnService $orderReturnService,
        bool $isAutomaticBillingNumberGenerationActivated
    ) {
        $this->orderService = $orderService;
        $this->orderReturnService = $orderReturnService;
        $this->isAutomaticBillingNumberGenerationActivated = $isAutomaticBillingNumberGenerationActivated;
    }

    public function generate(int $returnId): string
    {
        if (!$this->isAutomaticBillingNumberGenerationActivated) {
            throw new LogicException('Automatic billing number generation feature is not enabled.');
        }

        $orderReturn = $this->orderReturnService->getReturn($returnId);
        $order = $this->orderService->getOrder($orderReturn->getOrderId());

        $company = $order->getCompany();
        if (!$company->hasAutomaticRmaNumber()) {
            throw new LogicException('Automatic billing number generation is not enabled for the company.');
        }

        $rmaNumberCounter = $this->orderReturnService->countRmaNumbersFromCompany($company->getId());

        $rmaNumber = $company->getInitialRmaNumber() + $rmaNumberCounter;

        return $company->getPrefixCreditNoteNumber() . (string) $rmaNumber;
    }
}
