<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Wizacha\Marketplace\Order\Action\Confirm;
use Wizacha\Marketplace\Order\Action\MarkAsPaid;
use Wizacha\Marketplace\Order\Action\MarkPaymentAsRefused;
use Wizacha\Marketplace\Payment\Exception\UnsupportedApiException;
use Wizacha\Marketplace\Payment\LemonWay\LemonWayTransactionDetails;
use Wizacha\Marketplace\Payment\PaymentCallbackEventName;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\Processor\HiPay;
use Wizacha\Marketplace\Payment\Processor\ProcessorInterface;
use Wizacha\Marketplace\Payment\Processor\TransactionProcessorInterface;
use Wizacha\Marketplace\Payment\TransactionDetails;
use Wizacha\Marketplace\Transaction\TransactionType;
use Wizacha\Order;
use Wizacha\OrderStatus as LegacyOrderStatus;

class CheckIncompleteOrdersService
{
    /** @var iterable */
    private $processors;

    /** @var MarkPaymentAsRefused */
    private $markPaymentAsRefused;

    /** @var MarkAsPaid */
    private $markAsPaid;

    /** @var OrderService */
    private $orderService;

    /** @var Confirm */
    private $orderConfirm;

    /** @var EventDispatcherInterface */
    private $eventDispatcher;

    public function __construct(
        iterable $processors,
        MarkPaymentAsRefused $markPaymentAsRefused,
        MarkAsPaid $markAsPaid,
        OrderService $orderService,
        Confirm $orderConfirm,
        EventDispatcherInterface $eventDispatcher
    ) {
        $this->processors = $processors;
        $this->markPaymentAsRefused = $markPaymentAsRefused;
        $this->markAsPaid = $markAsPaid;
        $this->orderService = $orderService;
        $this->orderConfirm = $orderConfirm;
        $this->eventDispatcher = $eventDispatcher;
    }

    public function checkIncompleteOrders(OutputInterface $output): self
    {
        foreach ($this->processors as $processor) {
            if (true === $processor instanceof ProcessorInterface
                && true === $processor->isConfigured()
                && true === $processor instanceof TransactionProcessorInterface
            ) {
                $this->checkOrdersForTransactionStatusUpdate($output, $processor);
            }
        }

        return $this;
    }

    /**
     * Gets the orders for the given processor. If no order has been found, nothing happens. Otherwise,
     * for each order, gets its transaction details and depending on its status, updates the order status
     * to either SUCCESS or FAILED.
     *
     * If the transaction details status isn't SUCCESS nor FAILED, nothing happens.
     *
     * DIRECT_DEBIT type transaction is excluded
     */
    protected function checkOrdersForTransactionStatusUpdate(
        OutputInterface $output,
        TransactionProcessorInterface $processor
    ): CheckIncompleteOrdersService {
        $orders = $this->orderService->getIncompleteOrdersForProcessor($processor);
        $processorName = $processor->getName();

        if (false === $this->checkOrderCountAndDisplayInfo(\count($orders), $output, $processorName)) {
            return $this;
        }

        // Iterate through each order and update its transaction status if necessary.
        foreach ($orders as $order) {
            $orderId = \intval($order);
            $output->writeln("Checking order $orderId for $processorName");
            $transactionDetails = $processor->getTransactionDetailsByOrderId($orderId);

            if (null === $transactionDetails) {
                continue;
            }

            $this->updateOrderTransactionStatus($orderId, $processor, $transactionDetails);
        }

        return $this;
    }

    private function updateOrderTransactionStatus(
        int $orderId,
        TransactionProcessorInterface $processor,
        TransactionDetails $transactionDetails
    ): self {
        $processorName = $processor->getName();

        if (true === $processor->isTransactionDetailsSuccessful($transactionDetails)) {
            // Notion of refunded transaction not fully implemented yet for the other PSPs.
            if ($processor instanceof HiPay) {
                if (true === $processor->isTransactionDetailsNotRefunded($transactionDetails)) {
                    $this->markOrderAsPaidAndConfirmedTransactionDetails($orderId, $transactionDetails, $processorName);
                } // else : refunded, we do nothing
            } else {
                $this->markOrderAsPaidAndConfirmedTransactionDetails($orderId, $transactionDetails, $processorName);
            }
        } elseif (true === $processor->isTransactionDetailsFailed($transactionDetails)
            && false === $processor->isTransactionDetailsCancelled($transactionDetails)
        ) {
            // Cancelled orders must remain incomplete, to trash them later and avoid useless mails
            $this->markOrderAsFailedTransactionDetails($orderId);
        } // else Intermediate state: the order will end in to the garbage

        return $this;
    }

    protected function markOrderAsPaidAndConfirmedTransactionDetails(
        int $orderId,
        TransactionDetails $transaction,
        PaymentProcessorName $processorName
    ): self {
        $childrenOrders = $this->orderService->getChildOrders($orderId);
        $childMarkedAsPaid = false;

        // Mark the children order as paid if they weren't already.
        foreach ($childrenOrders as $childOrder) {
            if (true === $this->markAsPaid->isAllowed($childOrder)) {
                $this->markAsPaid->execute($childOrder);
                $childMarkedAsPaid = true;
            }

            if (true === $this->orderConfirm->isAllowed($childOrder)) {
                $this->orderConfirm->execute($childOrder);
            }

            try {
                $paymentCallbackEvent = PaymentCallbackEventName::getCallbackEventName($processorName);

                $this->eventDispatcher->dispatch(
                    new $paymentCallbackEvent(
                        new Order($childOrder->getId()),
                        (string) $transaction->getId(),
                        TransactionType::CREDITCARD()
                    )
                );
            } catch (UnsupportedApiException $exception) {
            }
        }

        // Update legacy status only if a child order has been marked as paid.
        if (true === $childMarkedAsPaid) {
            $this->orderService->changeOrderStatus($orderId, LegacyOrderStatus::STANDBY_VENDOR);
        }

        return $this;
    }

    protected function markOrderAsFailedTransactionDetails(int $orderId): self
    {
        $childMarkedAsRefused = false;

        foreach ($this->orderService->getChildOrders($orderId) as $childOrder) {
            if (true === $this->markPaymentAsRefused->isAllowed($childOrder)) {
                $this->markPaymentAsRefused->execute($childOrder);
                $childMarkedAsRefused = true;
            }
        }

        if (true === $childMarkedAsRefused) {
            $this->orderService->changeOrderStatus($orderId, LegacyOrderStatus::BILLING_FAILED);
        }

        return $this;
    }

    private function checkOrderCountAndDisplayInfo(
        int $orderCount,
        OutputInterface $output,
        PaymentProcessorName $processorName
    ): bool {
        if (0 === $orderCount) {
            $output->writeln(
                sprintf(
                    '<info>No incomplete order found for %s PSP</info>',
                    $processorName
                )
            );

            return false;
        }

        $output->writeln(
            sprintf(
                "<info>%d incomplete order found for %s PSP</info>",
                $orderCount,
                $processorName
            )
        );

        return true;
    }
}
