<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order;

use Wizacha\Marketplace\Order\Workflow\ModuleName;
use Wizacha\Marketplace\Order\Workflow\StepName;
use Wizacha\Marketplace\Order\Workflow\WorkflowName;

abstract class WorkflowToStatusMap
{
    /**
     * Utiliser ce statut qu'à des fins d'interface
     *
     * /!\ Surtout ne pas injecter cette valeur lors d'un update de commande
     * sinon ça va faire n'importe quoi /!\
     */
    public static function deduce(Order $order): OrderStatus
    {
        if ($order->isGarbage()) {
            return OrderStatus::INCOMPLETED();
        }

        if ($order->isCanceled()) {
            return OrderStatus::CANCELED();
        }

        $currentWorkflow = $order->getWorkflowName();

        $progress = $order->getWorkflowProgress();
        $currentModule = $progress->getModuleName();

        // Cas particulier pour le paiement manuel (hors marketplace)
        if ($currentModule->equals(ModuleName::MANUAL_PAYMENT())) {
            //si on est en attente de paiement => "attente de paiement"
            if ($progress->isProcessing()) {
                return OrderStatus::STANDBY_BILLING();
            }

            //si une étape de paiement a échouée => "commande échoué"
            if ($progress->hasFailed()) {
                return OrderStatus::BILLING_FAILED();
            }
        }

        if ($currentModule->equals(ModuleName::ORDER_CONFIRMATION())) {
            if ($progress->isProcessing()) {
                if ($order->getCustomerTotal()->isZero()
                    && false === $currentWorkflow->contains(ModuleName::MANUAL_PAYMENT())
                ) {
                    return OrderStatus::STANDBY_VENDOR();
                }

                return OrderStatus::STANDBY_BILLING();
            }

            if ($progress->hasFailed()) {
                return OrderStatus::BILLING_FAILED();
            }
        }

        if ($currentModule->equals(ModuleName::BANK_TRANSFER_PAYMENT())) {
            if ($progress->isProcessing()) {
                if ($progress->getStepName()->equals(StepName::PENDING_REDIRECTION_TO_PAYMENT_PROCESSOR())) {
                    return OrderStatus::INCOMPLETED();
                }

                return OrderStatus::STANDBY_BILLING();
            }

            //si une étape de paiement a échouée => "commande échoué"
            if ($progress->hasFailed()) {
                return OrderStatus::BILLING_FAILED();
            }
        }

        if ($currentModule->equals(ModuleName::ORDER_COMMITMENT())) {
            if ($progress->getStepName()->equals(StepName::PENDING_ORDER_COMMITMENT())) {
                return OrderStatus::INCOMPLETED();
            }

            if ($progress->isProcessing()) {
                return OrderStatus::STANDBY_VENDOR();
            }
        }

        if ($currentModule->equals(ModuleName::CREDIT_CARD_PAYMENT())
            || $currentModule->equals(ModuleName::CREDIT_CARD_PAYMENT_AUTHORIZATION())
            || $currentModule->equals(ModuleName::PAYMENT_DEFERMENT_AUTHORIZATION())
        ) {
            //si on est en attente d'une étape de paiement => "incomplete"
            if ($progress->isProcessing()) {
                if (\is_string($order->getSubscriptionId())) {
                    return OrderStatus::STANDBY_BILLING();
                }

                return OrderStatus::INCOMPLETED();
            }

            //si une étape de paiement a échouée => "commande échoué"
            if ($progress->hasFailed()) {
                return OrderStatus::BILLING_FAILED();
            }
        }

        // Paiement du paiement à échance
        if ($currentModule->equals(ModuleName::WAIT_PAYMENT_DEFERMENT())) {
            if ($progress->isProcessing()) {
                return OrderStatus::STANDBY_BILLING();
            }

            if ($progress->hasFailed()) {
                return OrderStatus::BILLING_FAILED();
            }
        }

        if ($currentModule->equals(ModuleName::CREDIT_CARD_PAYMENT_CAPTURE())) {
            if ($progress->isProcessing()) {
                return OrderStatus::PROCESSING_SHIPPING();
            }

            if ($progress->hasFailed()) {
                return OrderStatus::BILLING_FAILED();
            }
        }

        if ($currentModule->equals(ModuleName::ORDER_VALIDATION())) {
            // si le vendeur a refusé => "refusée"
            if ($progress->hasFailed()) {
                return OrderStatus::VENDOR_DECLINED();
            }

            //sinon => "payée en attente de validation"
            return OrderStatus::STANDBY_VENDOR();
        }

        if ($currentModule->equals(ModuleName::ORDER_PREPARATION())) {
            if ($progress->hasFailed()) {
                // TODO
            }

            //si on est à l'étape de preparation de commande => "traitement de la commande"
            return OrderStatus::PROCESSING_SHIPPING();
        }

        if ($currentModule->equals(ModuleName::DELIVERY())) {
            if ($progress->hasFailed()) {
                // TODO
            }

            if ($progress->isCompleted()) {
                return OrderStatus::COMPLETED();
            }

            //si on est à l'étape de livraison => "traitée"
            return OrderStatus::PROCESSED();
        }

        if ($currentModule->equals(ModuleName::WITHDRAWAL_PERIOD())) {
            if ($progress->hasFailed()) {
                return OrderStatus::CANCELED();
            }

            // Si pas de dispatch des fonds
            if ($progress->isCompleted()) {
                return OrderStatus::COMPLETED();
            }

            return OrderStatus::PROCESSED();
        }

        if ($currentModule->equals(ModuleName::FUNDS_DISPATCH())) {
            if ($progress->hasFailed()) {
                return OrderStatus::PROCESSED();
            }
        }

        //il peut y avoir un délai entre le moment où le délai de rétractation
        //de la commande est marqué comme terminé et le moment où les fonds sont
        //répartis, il faut donc garder le statut comme PROCESSED tant que les
        //fonds n'ont pas été répartis
        if ($progress->isProcessing()) {
            return OrderStatus::PROCESSED();
        }

        //si on en est à l'étape de distribution des fonds c'est que le délai de rétractation est fini => "terminée"
        return OrderStatus::COMPLETED();
    }
}
