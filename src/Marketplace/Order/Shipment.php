<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Order;

class Shipment
{
    /** int */
    private $shipmentId;

    /** @var  Order */
    private $order;

    /** @var ShipmentItem[] */
    private $items;

    /** @var  string */
    private $comments;

    /** @var  string */
    private $trackingNumber;

    /** @var string */
    private $chronopostSkybillNumber;

    /** @var null|\DateTimeImmutable */
    private $shippingDate;

    /**
     * @param int $shipmentId
     * @param Order $order
     * @param string $comments
     * @param ShipmentItem[] $items
     * @param string $trackingNumber
     * @param string $chronopostSkybillNumber
     * @param \DateTimeImmutable|null $shippingDate
     */
    public function __construct(
        int $shipmentId,
        Order $order,
        string $comments,
        array $items,
        string $trackingNumber,
        string $chronopostSkybillNumber,
        ?\DateTimeImmutable $shippingDate
    ) {
        $this->shipmentId = $shipmentId;
        $this->order = $order;
        $this->comments = $comments;
        $this->items = $items;
        $this->trackingNumber = $trackingNumber;
        $this->chronopostSkybillNumber = $chronopostSkybillNumber;
        $this->shippingDate = $shippingDate;
    }

    public function getOrder(): Order
    {
        return $this->order;
    }

    /**
     * @return ShipmentItem[]
     */
    public function getItems(): array
    {
        return $this->items;
    }

    public function getComments(): string
    {
        return $this->comments;
    }

    public function getTrackingNumber(): string
    {
        return $this->trackingNumber;
    }

    public function getChronopostSkybillNumber(): string
    {
        return $this->chronopostSkybillNumber;
    }

    /** @return  null|\DateTimeImmutable */
    public function getDeliveredDate(): ?\DateTimeImmutable
    {
        return $this->shippingDate;
    }
}
