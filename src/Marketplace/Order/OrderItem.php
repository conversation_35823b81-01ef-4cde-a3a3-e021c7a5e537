<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order;

use Wizacha\Component\Order\Line;
use Wizacha\Component\Order\TaxRate;
use Wizacha\Marketplace\Order\AmountsCalculator\Entity\AmountItem;
use Wizacha\Marketplace\Order\Discount\DiscountableItemInterface;
use Wizacha\Money\Money;

class OrderItem implements DiscountableItemInterface
{
    /** @var int */
    private $productId;

    /** @var null|string */
    private $fullCategoryPath;

    /** @var string */
    private $declinationId;

    /** @var string */
    private $productName;

    /** @var string */
    private $productCode;

    /** @var null|string */
    private $productImageId;

    /** @var Money */
    protected $price;

    /** @var null|int */
    private $maxPriceAdjustment;

    /** @var Money */
    protected $greenTax;

    /** @var string */
    private $itemId;

    /** @var int */
    private $amount;

    /** @var array */
    private $declinationOptions;

    /** @var string */
    private $customerComment;

    /** @var float */
    private $taxRate;

    /** @var int */
    protected $taxId;

    /** @var bool */
    protected $taxIncludedInPrice;

    /** @var array */
    private $taxData;

    /** @var string */
    private $extra;

    /** @var float */
    private $rawPrice;

    /** @var null|string */
    private $supplierRef;

    /** @var string */
    private $combination;

    /** @var bool */
    private $isSubscription;

    /** @var bool */
    private $isRenewable;

    /** @var Money */
    protected $discountedUnitPrice;

    /** @var AmountItem */
    protected $amountItem;

    public function __construct(array $data)
    {
        $this->productId = (int) $data['product_id'];
        $this->productCode = (string) $data['product_code'];
        $this->fullCategoryPath = $data['id_path'];
        $this->price = Money::fromVariable($data['price']);
        $this->maxPriceAdjustment = $data['max_price_adjustment'] ?? null;
        $this->amount = (int) $data['amount'];
        $this->itemId = $data['item_id'];
        $this->productImageId = $data['detailed_id'];
        $this->customerComment = $data['customer_comment'];
        $this->taxData = \is_array($data['tax_data']) ? $data['tax_data'] : [];
        $this->extra = $data['extra'];
        $this->rawPrice = (float) $data['price'];

        $taxes = array_filter($data['tax_data']);
        $this->taxId = 0;

        if (\count($taxes) > 0) {
            // sometimes filterTaxes() return empty array (if taxes = 0%)
            $taxes = $this->filterTaxes('P_' . $this->itemId, $taxes);
            if (\count($taxes) > 0) {
                $this->taxId = \array_key_first($taxes);
                $tax = reset($taxes);
            } else {
                $tax = [
                    'rate_value' => 0.0,
                    'price_includes_tax' => true,
                ];
            }
        } else {
            $tax = [
                'rate_value' => 0.0,
                'price_includes_tax' => true,
            ];
        }

        $this->taxRate = (float) $tax['rate_value'];
        $this->taxIncludedInPrice = $tax['price_includes_tax'] === 'Y';

        $extras = (array) @unserialize($data['extra']);
        $this->productName = $extras['product'] ?? '';

        $this->combination = fn_get_options_combination($extras['product_options'] ?? []) ?: '0';
        $this->declinationId = $data['product_id'] . '_' . $this->combination;
        $this->supplierRef = $data['supplier_ref'];
        $this->greenTax = Money::fromVariable($extras['w_green_tax'] ?? 0);

        // Si il y a au moins une options sélectionnée
        // (on utilise les checks fait dans la fonction CSCart pour ne pas dupliquer la logique
        $this->declinationOptions = [];
        if ($this->combination !== '' && \is_array($extras['product_options_value'])) {
            foreach ($extras['product_options_value'] as $selectedOption) {
                $this->declinationOptions[] = [
                    'optionId' => (int) $selectedOption['option_id'],
                    'optionName' => (string) $selectedOption['option_name'],
                    'variantId' => (int) $selectedOption['value'],
                    'variantName' => (string) $selectedOption['variant_name'],
                    'code' => $selectedOption['code'],
                ];
            }
        }

        $this->isSubscription = (bool) $data['is_subscription'];
        $this->isRenewable = (bool) $data['is_renewable'];
    }

    public function getProductId(): int
    {
        return $this->productId;
    }

    public function getProductCode(): string
    {
        return $this->productCode;
    }

    public function getProductImageId(): ?string
    {
        return $this->productImageId;
    }

    public function getFullCategoryPath(): ?string
    {
        return $this->fullCategoryPath;
    }

    public function getPrice(): Money
    {
        if (null !== $this->amountItem) {
            if (true === $this->taxIncludedInPrice) {
                return $this->amountItem->getUnitPriceInclTaxes();
            }
            return $this->amountItem->getUnitPriceExclTaxes();
        }

        return $this->price;
    }

    public function getAmountToAdjust(): Money
    {
        return $this->taxIncludedInPrice === true
            ? $this->amountItem->getTotalInclTaxes()
            : $this->amountItem->getTotalExclTaxes();
    }

    public function getPriceWithoutTaxes(): Money
    {
        return $this->amountItem->getUnitPriceExclTaxes();
    }

    public function getUnitPrice(): Money
    {
        return $this->amountItem->getUnitPriceInclTaxes();
    }

    public function getTaxAmount(): Money
    {
        return $this->amountItem->getTotalTaxAmount();
    }

    public function getTaxRate(): TaxRate
    {
        if (null !== $this->amountItem) {
            return new TaxRate($this->amountItem->getTaxRate());
        }

        return new TaxRate($this->taxRate);
    }

    public function getTaxId(): int
    {
        return $this->taxId;
    }

    public function getAmountExcludingTax(): Money
    {
        return $this->amountItem->getTotalExclTaxes();
    }

    public function getAmountIncludingTax(): Money
    {
        return $this->amountItem->getTotalInclTaxes();
    }

    public function getGreenTaxAmount(): Money
    {
        if (null !== $this->amountItem) {
            return $this->amountItem->getGreenTax();
        }

        return $this->greenTax;
    }

    public function getMaxPriceAdjustment(): ?int
    {
        if (null !== $this->amountItem) {
            return $this->amountItem->getOrderItemData()->getMaxPriceAdjustment();
        }

        return $this->maxPriceAdjustment;
    }

    /**
     * @deprecated
     * @see self::getQuantity()
     */
    public function getAmount(): int
    {
        return $this->getQuantity();
    }

    public function getQuantity(): int
    {
        if (null !== $this->amountItem) {
            return $this->amountItem->getQuantity();
        }

        return $this->amount;
    }

    public function getProductName(): string
    {
        return $this->productName;
    }

    public function getDeclinationId(): string
    {
        return $this->declinationId;
    }

    public function getItemId(): string
    {
        return $this->itemId;
    }

    public function getDeclinationOptions(): array
    {
        return $this->declinationOptions;
    }

    public function getCustomerComment(): string
    {
        return $this->customerComment;
    }

    public function asLine(): Line
    {
        return new Line(
            $this->amountItem->getUnitPriceExclTaxes(),
            $this->getQuantity(),
            $this->getTaxRate()
        );
    }

    public function getTaxData(): array
    {
        if (null !== $this->amountItem) {
            return $this->amountItem->getOrderItemData()->getTaxData();
        }

        return $this->taxData;
    }

    public function getExtra(): string
    {
        return $this->extra;
    }

    public function getRawPrice(): float
    {
        if (null !== $this->amountItem) {
            return $this->amountItem->getOrderItemData()->getRawPrice();
        }

        return $this->rawPrice;
    }

    public function getSupplierRef(): ?string
    {
        return $this->supplierRef;
    }

    public function getCombination(): string
    {
        return $this->combination;
    }

    public function isSubscription(): bool
    {
        return $this->isSubscription;
    }

    public function isRenewable(): bool
    {
        return $this->isRenewable;
    }

    public function isTaxIncludedInPrice(): bool
    {
        return $this->taxIncludedInPrice;
    }

    public function getDiscountedUnitPrice(): Money
    {
        return $this->amountItem->getDiscountedUnitPriceInclTaxes();
    }

    public function getDiscountedUnitPriceForRefund(): Money
    {
        return $this->amountItem
            ->getDiscountedUnitPriceInclTaxes()
            ->subtract($this->getAmountItem()->getDiscountMarketplace()->divide($this->getQuantity()));
    }

    public function setDiscountedUnitPrice(Money $money): DiscountableItemInterface
    {
        $this->discountedUnitPrice = $money;

        return $this;
    }

    public function getAmountItem(): ?AmountItem
    {
        return $this->amountItem;
    }

    public function setAmountItem(?AmountItem $amountItem): self
    {
        $this->amountItem = $amountItem;

        return $this;
    }

    public function setAdjustedPrices(Money $adjustedPriceExclTaxes): self
    {
        $this->amountItem->setAdjustedPrices($adjustedPriceExclTaxes);

        return $this;
    }

    public function expose()
    {
        return [
            'itemId' => $this->itemId,
            'declinationId' => $this->getDeclinationId(),
            'productName' => $this->getProductName(),
            'productCode' => $this->getProductCode(),
            'productImageId' => $this->getProductImageId(),
            'price' => $this->getPrice()->getConvertedAmount(),
            'maxPriceAdjustment' => $this->getMaxPriceAdjustment(),
            'amount' => $this->getAmount(),
            'options' => $this->getDeclinationOptions(),
            'customerComment' => $this->getCustomerComment(),
            'greenTax' => $this->getGreenTaxAmount()->getConvertedAmount(),
            'supplierRef' => $this->getSupplierRef(),
            'isSubscription' => $this->isSubscription(),
            'isRenewable' => $this->isRenewable(),
        ];
    }

    /**
     * Filters OrderItem taxes based on a given tax identifier (eg Pxxx for product tax, Sxxx for shipping, ...)
     * @param string|int $taxIdentifier
     * @param mixed[] $taxes
     * @return mixed[]
     */
    public function filterTaxes($taxIdentifier, array $taxes = null): array
    {
        return \array_filter(
            $taxes ?? $this->getTaxData(),
            function ($tax) use ($taxIdentifier): bool {
                return \is_array($tax)
                    && \array_key_exists('applies', $tax)
                    && \is_array($tax['applies'])
                    && \array_key_exists($taxIdentifier, $tax['applies']);
            }
        );
    }

    public function recalculateTaxes(): self
    {
        $taxDatas = $this->getTaxData();

        foreach ($taxDatas as $taxId => &$tax) {
            $tax['tax_subtotal'] = 0;

            foreach ($tax['applies'] as $code => $taxAmount) {
                if ($code === 'P_' . $this->itemId) {
                    $tax['applies'][$code] = $this->getTaxAmount()->getPreciseConvertedAmount();
                    $tax['tax_subtotal'] += $tax['applies'][$code];
                }
            }
        }

        $this->amountItem->getOrderItemData()->setTaxData($taxDatas);

        return $this;
    }
}
