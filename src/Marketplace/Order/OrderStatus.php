<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order;

use MyCLabs\Enum\Enum;

/**
 * @method static OrderStatus STANDBY_BILLING()
 * @method static OrderStatus STANDBY_VENDOR()
 * @method static OrderStatus PROCESSING_SHIPPING()
 * @method static OrderStatus PROCESSED()
 * @method static OrderStatus COMPLETED()
 * @method static OrderStatus BILLING_FAILED()
 * @method static OrderStatus VENDOR_DECLINED()
 * @method static OrderStatus STANDBY_SUPPLYING()
 * @method static OrderStatus UNPAID()
 * @method static OrderStatus REFUNDED()
 * @method static OrderStatus CANCELED()
 * @method static OrderStatus INCOMPLETED()
 * @method static OrderStatus PARENT_ORDER()
 */
class OrderStatus extends Enum
{
    /**
     * This function is used to validate input values, we override it to supports dynamic statuses
     * @see \MyCLabs\Enum\Enum::__construct
     * @see \MyCLabs\Enum\Enum::isValid
     */
    public static function toArray()
    {
        static $statusCache = [];

        $result = parent::toArray();

        if (empty($statusCache)) {
            $statusCache = fn_get_simple_statuses();

            // Remove all statuses which are hardcoded in the Enum
            $statusCache = array_filter($statusCache, function ($statusLetter) use ($result) {
                return !\in_array($statusLetter, $result);
            }, ARRAY_FILTER_USE_KEY);

            // Convert status name to constant
            $statusCache = array_map(function ($status) {
                $status = preg_replace('/[^A-Za-z0-9 ]/', '', $status);
                $status = preg_replace('/\s+/', '_', trim($status));

                return strtoupper($status);
            }, $statusCache);

            // Flip array to match Enum's format
            $statusCache = array_flip($statusCache);
        }

        return array_merge($result, $statusCache);
    }

    /**
     * The order is waiting to be payed.
     */
    public const STANDBY_BILLING = 'O';

    /**
     * The order is waiting for the vendor to process it.
     */
    public const STANDBY_VENDOR = 'P';

    /**
     * The order has been shipped but not received yet.
     */
    public const PROCESSING_SHIPPING = 'E';

    /**
     * The order has been shipped/processed, the client can still ask for a refund.
     */
    public const PROCESSED = 'C';

    /**
     * The order is finished.
     */
    public const COMPLETED = 'H';

    /**
     * The payment from the client failed.
     */
    public const BILLING_FAILED = 'F';

    /**
     * The vendor declined the order.
     */
    public const VENDOR_DECLINED = 'D';

    /**
     * The order is in back-order (some products need to be ordered first be the vendor).
     */
    public const STANDBY_SUPPLYING = 'B';

    /**
     * The order has been marked as unpaid
     */
    public const UNPAID = 'G';

    /**
     * The order has been marked as refunded
     */
    public const REFUNDED = 'A';

    /**
     * Canceled by the client.
     */
    public const CANCELED = 'I';

    /**
     * The checkout was started but not completed.
     */
    public const INCOMPLETED = 'N';

    /**
     * Special status for parent orders.
     */
    public const PARENT_ORDER = 'T';
}
