<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Adjustment;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;

class OrderAdjustmentRepository extends ServiceEntityRepository
{
    public function save(OrderAdjustment $adjustment): OrderAdjustment
    {
        $this->getEntityManager()->persist($adjustment);
        $this->getEntityManager()->flush();

        return $adjustment;
    }
}
