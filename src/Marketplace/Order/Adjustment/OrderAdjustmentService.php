<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Adjustment;

use Doctrine\DBAL\Connection;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Wizacha\AppBundle\Security\User\ApiSecurityUser;
use Wizacha\Marketplace\Order\AmountsCalculator\Service\OrderAmountsCalculator;
use Wizacha\Marketplace\Commission\CommissionService;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Order\Exception\AdjustmentException;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderItem;
use Wizacha\Marketplace\User\User;
use Wizacha\Marketplace\User\UserService;
use Wizacha\Money\Money;

class OrderAdjustmentService
{
    private OrderAdjustmentRepository $repository;
    private Connection $connection;
    private TokenStorageInterface $tokenStorage;
    private UserService $userService;
    private CommissionService $commissionService;
    private OrderAmountsCalculator $orderAmountsCalculator;

    public function __construct(
        OrderAdjustmentRepository $repository,
        Connection $connection,
        TokenStorageInterface $tokenStorage,
        UserService $userService,
        CommissionService $commissionService,
        OrderAmountsCalculator $orderAmountsCalculator
    ) {
        $this->repository = $repository;
        $this->connection = $connection;
        $this->tokenStorage = $tokenStorage;
        $this->userService = $userService;
        $this->commissionService = $commissionService;
        $this->orderAmountsCalculator = $orderAmountsCalculator;
    }

    public function adjust(Order $order, int $itemId, float $newTotal, User $user = null): self
    {
        try {
            $orderItem = $order->getItem($itemId);
        } catch (NotFound $e) {
            throw new AdjustmentException($e->getMessage());
        }

        $oldTotalAsMoney = $orderItem->getAmountExcludingTax();
        $newTotalAsMoney = Money::fromVariable($newTotal);
        $oldTotalOrder = $order->getTotal();

        // casting comparison problems in "Money::equals" function
        if ($oldTotalAsMoney->getAmount() === $newTotalAsMoney->getAmount()) {
            return $this;
        }

        if ($order->isAdjustable() === false) {
            throw new AdjustmentException(
                __('order_adjustment_cant_be_adjusted_at_status', ['status' => $order->getStatus()->getValue()])
            );
        }

        $this->assertAdjustmentIsCoherent($orderItem, $newTotal);

        $orderItem->setAdjustedPrices($newTotalAsMoney);
        $orderItem->recalculateTaxes();
        $this->orderAmountsCalculator->refreshAfterAdjustment($order);

        // this query must be done after the first transaction because the CommissionService::calculateCommissions
        // make db queries on orders (via legacy Order) to retrieve some amounts
        $this->connection->update(
            'cscart_vendor_payouts',
            [
                'order_amount' => $order->getTotal()->getConvertedAmount(),
                'commission_amount' => $this->commissionService->calculateCommissions($order)->getConvertedAmount()
            ],
            ['order_id' => $order->getId()]
        );

        $this->logAdjustment($order, $orderItem, $oldTotalOrder, $oldTotalAsMoney, $newTotalAsMoney, $user);

        return $this;
    }

    /**
     * @return OrderAdjustment[]
     */
    public function findByOrderId(int $orderId): array
    {
        return $this->repository->findBy(['orderId' => $orderId], ['createdAt' => 'ASC']);
    }

    private function assertAdjustmentIsCoherent(OrderItem $orderItem, float $newTotal): void
    {
        $amountToAdjust = $orderItem->getAmountToAdjust()->getConvertedAmount();
        $maxPriceAdjustment = $orderItem->getMaxPriceAdjustment();
        $percentToRemove = ($amountToAdjust - $newTotal) * 100 / $amountToAdjust;

        if ($maxPriceAdjustment <= 0) {
            throw new AdjustmentException(__('order_adjustment_product_not_adjustable'));
        }

        if ($orderItem->getPrice()->isPositive() === false) {
            throw new AdjustmentException(__('order_adjustment_price_greater_than_zero'));
        }

        if ($newTotal > $amountToAdjust) {
            throw new AdjustmentException(__('order_adjustment_adjustment_too_big', [
                '[newTotal]' => $newTotal,
                '[amountToAdjust]' => $amountToAdjust,
            ]));
        }

        if ($newTotal <= 0) {
            throw new AdjustmentException(__('order_adjustment_price_greater_than_zero'));
        }

        if ($percentToRemove > $maxPriceAdjustment) {
            throw new AdjustmentException(__('order_adjustment_bad_percent', [
                '[percentToRemove]' => round($percentToRemove, 2),
                '[maxPriceAdjustment]' => $maxPriceAdjustment,
                '[amountToAdjust]' => $amountToAdjust,
                '[newTotal]' => $newTotal,
            ]));
        }
    }

    private function logAdjustment(
        Order $order,
        OrderItem $orderItem,
        Money $oldTotalOrder,
        Money $oldPrice,
        Money $newPrice,
        ?User $user
    ): OrderAdjustment {
        if ($user === null) {
            $token = $this->tokenStorage->getToken();

            if ($token === null || $token->getUser() instanceof ApiSecurityUser === false) {
                throw new AdjustmentException('Adjustor user can\'t be determined, please provide one or log\'in.');
            }

            $user = $this->userService->get($token->getUser()->getId());
        }

        $orderAdjustment = new OrderAdjustment(
            $order->getId(),
            (int) $orderItem->getItemId(),
            $user,
            $oldPrice->getConvertedAmount(),
            $newPrice->getConvertedAmount(),
            $oldTotalOrder->getConvertedAmount(),
            $order->getTotal()->getConvertedAmount()
        );

        return $this->repository->save($orderAdjustment);
    }
}
