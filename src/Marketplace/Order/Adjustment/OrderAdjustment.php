<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Adjustment;

use Wizacha\Marketplace\User\User;

class OrderAdjustment
{
    /** @var null|int */
    private $id;

    /** @var int */
    private $orderId;

    /** @var int */
    private $itemId;

    /** @var float */
    private $oldPriceWithoutTaxes;

    /** @var float */
    private $newPriceWithoutTaxes;

    /** @var float */
    private $oldTotalIncludingTaxes;

    /** @var float */
    private $newTotalIncludingTaxes;

    /** @var \DateTimeInterface */
    private $createdAt;

    /** @var User  */
    private $user;

    public function __construct(
        int $orderId,
        int $itemId,
        User $user,
        float $oldPriceWithoutTaxes,
        float $newPriceWithoutTaxes,
        float $oldTotalIncludingTaxes,
        float $newTotalIncludingTaxes
    ) {
        $this->createdAt = new \DateTimeImmutable();
        $this->orderId = $orderId;
        $this->itemId = $itemId;
        $this->user = $user;
        $this->oldPriceWithoutTaxes = $oldPriceWithoutTaxes;
        $this->newPriceWithoutTaxes = $newPriceWithoutTaxes;
        $this->oldTotalIncludingTaxes = $oldTotalIncludingTaxes;
        $this->newTotalIncludingTaxes = $newTotalIncludingTaxes;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getOrderId(): int
    {
        return $this->orderId;
    }

    public function setOrderId(int $orderId): self
    {
        $this->orderId = $orderId;

        return $this;
    }

    public function getItemId(): int
    {
        return $this->itemId;
    }

    public function setItemId(int $itemId): self
    {
        $this->itemId = $itemId;

        return $this;
    }

    public function getOldPriceWithoutTaxes(): float
    {
        return $this->oldPriceWithoutTaxes;
    }

    public function setOldPriceWithoutTaxes(float $oldPriceWithoutTaxes): void
    {
        $this->oldPriceWithoutTaxes = $oldPriceWithoutTaxes;
    }

    public function getNewPriceWithoutTaxes(): float
    {
        return $this->newPriceWithoutTaxes;
    }

    public function setNewPriceWithoutTaxes(float $newPriceWithoutTaxes): void
    {
        $this->newPriceWithoutTaxes = $newPriceWithoutTaxes;
    }

    public function getOldTotalIncludingTaxes(): float
    {
        return $this->oldTotalIncludingTaxes;
    }

    public function setOldTotalIncludingTaxes(float $oldTotalIncludingTaxes): void
    {
        $this->oldTotalIncludingTaxes = $oldTotalIncludingTaxes;
    }

    public function getNewTotalIncludingTaxes(): float
    {
        return $this->newTotalIncludingTaxes;
    }

    public function setNewTotalIncludingTaxes(float $newTotalIncludingTaxes): void
    {
        $this->newTotalIncludingTaxes = $newTotalIncludingTaxes;
    }

    public function getCreatedAt(): \DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function setUser(User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function expose(): array
    {
        return [
            'itemId' => $this->getItemId(),
            'oldPriceWithoutTaxes' => $this->getOldPriceWithoutTaxes(),
            'newPriceWithoutTaxes' => $this->getNewPriceWithoutTaxes(),
            'oldTotalIncludingTaxes' => $this->getOldTotalIncludingTaxes(),
            'newTotalIncludingTaxes' => $this->getNewTotalIncludingTaxes(),
            'createdBy' => $this->getUser()->getUserId(),
            'createdAt' => $this->getCreatedAt()->format(\DateTime::RFC3339),
        ];
    }
}
