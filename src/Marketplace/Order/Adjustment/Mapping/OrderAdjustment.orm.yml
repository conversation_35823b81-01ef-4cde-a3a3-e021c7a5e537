Wizacha\Marketplace\Order\Adjustment\OrderAdjustment:
    type: entity
    table: order_adjustment
    id:
        id:
            type: integer
            nullable: false
            options:
                unsigned: false
            generator:
                strategy: IDENTITY
    fields:
        orderId:
            type: integer
            nullable: false
        itemId:
            type: integer
            nullable: false
        oldPriceWithoutTaxes:
            type: float
            nullable: false
        newPriceWithoutTaxes:
            type: float
            nullable: false
        oldTotalIncludingTaxes:
            type: float
            nullable: false
        newTotalIncludingTaxes:
            type: float
            nullable: false
        createdAt:
            type: datetime
            nullable: false
    oneToOne:
        user:
            targetEntity: Wizacha\Marketplace\User\User
            joinColumn:
                name: created_by
                referencedColumnName: user_id
