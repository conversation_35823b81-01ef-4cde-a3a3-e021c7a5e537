<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Discount;

use Wizacha\Money\Money;

interface DiscountableItemInterface
{
    public function isTaxIncludedInPrice(): bool;
    public function getUnitPrice(): Money;
    public function setDiscountedUnitPrice(Money $money): self;
    public function getDiscountedUnitPrice(): Money;
}
