<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order\Discount;

use Wizacha\Marketplace\Order\Discount\Exception\EmptyDiscountException;
use Wizacha\Marketplace\Promotion\BonusTarget\BonusTarget;
use Wizacha\Marketplace\Promotion\BonusTarget\BonusTargetShipping;
use Wizacha\Money\Money;

class OrderDiscountsCalculator
{
    /**
     * This methods takes a legacy order as input (large array from fn_get_order_info())
     * and calculate discounted prices, with basket discount dispatched proportionally onto every product.
     *
     * @return Money[]
     */
    public function getPricesFromLegacyOrderData(array $orderData, bool $isTaxIncludedInPrice): array
    {
        $discount = Money::fromVariable($orderData['discount'] ?? 0)
            ->add(Money::fromVariable($orderData['marketplace_discount_total'] ?? 0))
        ;
        if (\count($orderData['promotions'] ?? []) === 0 || $discount->isZero()) {
            return [];
        }

        $promotions = $orderData['promotions'];
        $shippingPromotions = array_filter(
            $orderData['promotions'],
            static function ($promotion) {
                return (BonusTarget::fromString($promotion['target']) instanceof BonusTargetShipping);
            }
        );

        // If we only have promotions on shipping costs, it has no impact on item unit prices
        if (\count($promotions) === \count($shippingPromotions)) {
            return [];
        }

        $shippingCost = Money::fromVariable((float) $orderData['shipping_cost']);
        $discountedTotal = Money::fromVariable((float) $orderData['customer_total'])->subtract($shippingCost);

        // We don't want to include shipping discounts in unit price discounted prices.
        $discount = $discount->subtract($orderData['shippingCostDiscount']);

        $initialTotal = $discountedTotal->add($discount);

        if ($initialTotal->equals($discountedTotal) === true) {
            return [];
        }

        $prices = [];
        foreach ($orderData['products'] as $key => $productData) {
            $unitPrice = Money::fromVariable($productData['price']);
            if ($isTaxIncludedInPrice === false) {
                $unitVat = Money::fromVariable($productData['tax_value'] ?? 0)->divide((float) $productData['amount']);
                $unitPrice = $unitPrice->add($unitVat);
            }

            $prices[$key] = $this->getItemDiscountedPrice(
                $initialTotal,
                $discountedTotal,
                $unitPrice
            );
        }

        return $prices;
    }

    /**
     * @param mixed[] $orderData
     *
     * @return mixed[]
     */
    public function buildInvoiceData(array $orderData, bool $isTaxIncludedInPrice): array
    {
        $discount = Money::fromVariable($orderData['discount'] ?? 0)
            ->add(Money::fromVariable($orderData['marketplace_discount_total'] ?? 0))
        ;
        if (true === $discount->isZero()) {
            throw new EmptyDiscountException();
        }

        $taxes = [];
        foreach ($orderData['taxes'] as $tax) {
            foreach ($tax['applies'] as $taxEffect => $amount) {
                if (preg_match('/P_(\d+)/', $taxEffect, $matches) !== 1) {
                    continue;
                }

                [, $itemId] = $matches;
                $taxes[$itemId] = (float) $tax['rate_value'] / 100;
            }
        }

        $shippingCost = Money::fromVariable((float) $orderData['shipping_cost']);
        $discountedTotal = Money::fromVariable((float) $orderData['customer_total']);
        $initialTotal = $discountedTotal->add($discount)->subtract($shippingCost);

        $orderTotal = [
            'orderId' => $orderData['order_id'],
            'companyId' => $orderData['company_id'],
            // subtotals do not include shipping costs
            'subtotalExclTaxes' => new Money(0),
            'subtotalInclTaxes' => new Money(0),
            'shippingCostExclTaxes' => $shippingCost->divide(1.2),
            'shippingCostInclTaxes' => $shippingCost,
            'discount' => $discount,
            'taxes' => [],
            'totalTaxes' => new Money(0),
            'totalExclTaxes' => new Money(0),
            'totalInclTaxes' => new Money(0),
        ];

        $shippingData = \is_array($orderData['shipping'] ?? null) ? reset($orderData['shipping']) : null;
        $shippingTax = \is_array($shippingData['taxes'] ?? null) ? reset($shippingData['taxes']) : null;
        if (null !== $shippingTax) {
            $shippingTaxAmount = Money::fromVariable($shippingTax['tax_subtotal']);
            $orderTotal['taxes'][((string) ((float) $shippingTax['rate_value'] / 100))] = $shippingTaxAmount;
            $orderTotal['totalTaxes'] = $orderTotal['totalTaxes']->add($shippingTaxAmount);
        }

        $orderLines = [];
        foreach ($orderData['products'] as $key => $productData) {
            $orderLine['quantity'] = (float) $productData['amount'];

            $unitPrice = Money::fromVariable($productData['price']);
            $unitVat = Money::fromVariable($productData['tax_value'] ?? 0)->divide((float) $productData['amount']);

            if ($isTaxIncludedInPrice === false) {
                $orderLine['unitPriceExclTaxes'] = $unitPrice;
                $orderLine['unitPriceInclTaxes'] = $unitPrice->add($unitVat);
            } else {
                $orderLine['unitPriceExclTaxes'] = $unitPrice->subtract($unitVat);
                $orderLine['unitPriceInclTaxes'] = $unitPrice;
            }

            $orderLine['totalExclTaxes'] = $orderLine['unitPriceExclTaxes']->multiply($orderLine['quantity']);
            $orderLine['totalTaxes'] = $orderLine['totalExclTaxes']->multiply($taxes[$key]);
            $orderLine['totalInclTaxes'] = $orderLine['totalExclTaxes']->add($orderLine['totalTaxes']);

            $orderLine['totalDiscountInclTaxes'] = $orderLine['totalInclTaxes']
                ->divide($initialTotal->getPreciseConvertedAmount())
                ->multiply($discount->getPreciseConvertedAmount())
            ;
            $orderLine['totalDiscountedInclTaxes'] = $orderLine['totalInclTaxes']->subtract(
                $orderLine['totalDiscountInclTaxes']
            );
            $orderLine['totalDiscountedTaxes'] = $orderLine['totalDiscountedInclTaxes']
                ->divide(1 + $taxes[$key])
                ->multiply($taxes[$key])
            ;
            $orderTotal['taxes'][(string) $taxes[$key]] = ($orderTotal['taxes'][(string) $taxes[$key]] ?? new Money(0))
                ->add($orderLine['totalDiscountedTaxes'])
            ;
            $orderTotal['totalTaxes'] = $orderTotal['totalTaxes']->add($orderLine['totalDiscountedTaxes']);

            $orderTotal['subtotalExclTaxes'] = $orderTotal['subtotalExclTaxes']->add($orderLine['totalExclTaxes']);
            $orderTotal['subtotalInclTaxes'] = $orderTotal['subtotalInclTaxes']->add($orderLine['totalInclTaxes']);

            $orderLines[$key] = $orderLine;
        }

        $orderTotal['totalInclTaxes'] = $orderTotal['subtotalInclTaxes']
            ->add($orderTotal['shippingCostInclTaxes'])
            ->subtract($orderTotal['discount'])
        ;
        $orderTotal['totalExclTaxes'] = $orderTotal['totalInclTaxes']->subtract($orderTotal['totalTaxes']);

        return [
            'lines' => $orderLines,
            'total' => $orderTotal,
        ];
    }

    public function getItemDiscountedPrice(Money $orderTotal, Money $discountedTotal, Money $itemUnitPrice): Money
    {
        $itemOrderRatio = $itemUnitPrice->getPreciseConvertedAmount() / $orderTotal->getPreciseConvertedAmount();

        return $discountedTotal->multiply($itemOrderRatio);
    }
}
