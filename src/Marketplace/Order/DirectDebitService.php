<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order;

use HiPay\Fullservice\Exception\ApiErrorException;
use HiPay\Fullservice\Exception\UnexpectedValueException;
use Symfony\Component\Validator\Constraints\Bic;
use Symfony\Component\Validator\Constraints\Choice;
use Symfony\Component\Validator\Constraints\Collection;
use Symfony\Component\Validator\Constraints\Iban;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\ConstraintViolationInterface;
use Symfony\Component\Validator\Validation;
use Wizacha\AppBundle\Security\User\UserService as UserSecurityService;
use Wizacha\Marketplace\Order\Exception\ActionNotAllowed;
use Wizacha\Marketplace\Order\Exception\FieldsValidationException;
use Wizacha\Marketplace\Order\Exception\UnknownPaymentProcessor;
use Wizacha\Marketplace\Payment\PaymentProcessorIdentifier;
use Wizacha\Marketplace\Payment\Processor\HiPay;
use Wizacha\Marketplace\Payment\Processor\LemonWay;
use Wizacha\Marketplace\Payment\Stripe\BankAccountIsMissing;
use Wizacha\Marketplace\Payment\UserPaymentInfoService;
use Wizacha\Marketplace\User\UserService;

class DirectDebitService
{
    /** @var mixed[] */
    protected array $constraintsByPsp;

    protected HiPay $hiPay;

    protected LemonWay $lemonway;

    protected UserPaymentInfoService $userPaymentInfoService;

    protected UserSecurityService $userSecurityService;

    protected UserService $userService;

    public function __construct(
        HiPay $hiPay,
        UserPaymentInfoService $userPaymentInfoService,
        UserSecurityService $userSecurityService,
        UserService $userService,
        LemonWay $lemonWay
    ) {
        $this->hiPay = $hiPay;
        $this->userPaymentInfoService = $userPaymentInfoService;
        $this->userSecurityService = $userSecurityService;
        $this->userService = $userService;
        $this->lemonway = $lemonWay;
        $this->generateConstraints();
    }

    protected function generateConstraints(): void
    {
        $lemonWayConstraints = [
            'firstName' => [new NotBlank()],
            'lastName' =>  [new NotBlank()],
            'gender' => [new Choice(['M', 'F'])],
            'bankName' => [new NotBlank()],
            'bic' => [new NotBlank()],
            'iban' => [new NotBlank(), new Iban()],
            'paymentId' =>  [new NotBlank()],
            'returnUrl' => [new NotBlank()],
            'errorUrl' => [new NotBlank()],
        ];
        // Array of all constraints, by PSP identifier
        $this->constraintsByPsp =  [
            PaymentProcessorIdentifier::HIPAY_SEPA_DIRECT_DEBIT()->getValue() =>
                [
                    'iban' => [new NotBlank(), new Iban()],
                    'bic' => [new NotBlank(), new Bic()],
                    'bankName' => [new NotBlank()],
                    'gender' => [new Choice(['M', 'F'])],
                    'firstName' => [new NotBlank()],
                    'lastName' =>  [new NotBlank()],
                    'paymentId' =>  [new NotBlank()],
                ],
            PaymentProcessorIdentifier::HIPAY_DEFERED_SEPA_DIRECT_DEBIT()->getValue() =>
                [
                    'iban' => [new NotBlank(), new Iban()],
                    'bic' => [new NotBlank(), new Bic()],
                    'bankName' => [new NotBlank()],
                    'gender' => [new Choice(['M', 'F'])],
                    'firstName' => [new NotBlank()],
                    'lastName' =>  [new NotBlank()],
                    'paymentId' =>  [new NotBlank()],
                ],
            PaymentProcessorIdentifier::LEMONWAY_SEPA_DIRECT_DEBIT()->getValue() =>
                $lemonWayConstraints,
            PaymentProcessorIdentifier::LEMONWAY_DEFERED_SEPA_DIRECT_DEBIT()->getValue() =>
                $lemonWayConstraints,
        ];
    }

    /**
     * @param string[] $requestData
     * @throws ApiErrorException
     * @throws UnexpectedValueException
     * @throws UnknownPaymentProcessor
     * @throws FieldsValidationException
     */
    public function createMandate(array $requestData): array
    {
        if (false === \array_key_exists('paymentId', $requestData) || false === \is_int($requestData['paymentId'])) {
            throw new FieldsValidationException("\"paymentId\" must be set. This field was not expected.");
        }

        $paymentProcessorId = $this->validatePaymentId((int) $requestData['paymentId']);
        $this->validateDirectDebitData($paymentProcessorId, $requestData);

        switch ($paymentProcessorId) {
            case PaymentProcessorIdentifier::HIPAY_SEPA_DIRECT_DEBIT()->getValue():
            case PaymentProcessorIdentifier::HIPAY_DEFERED_SEPA_DIRECT_DEBIT()->getValue() :
                $hipayData = [
                    'iban' => $requestData['iban'],
                    'issuer_bank_id' => $requestData['bic'],
                    'bank_name' => $requestData['bankName'],
                    'gender' => $requestData['gender'],
                    'firstname' => $requestData['firstName'],
                    'lastname' => $requestData['lastName'],
                ];

                $token = $this->hiPay->createSepaMandate($hipayData);
                $this->userPaymentInfoService->setHipaySepaAgreement(
                    $this->userSecurityService->getCurrentUserId(),
                    $token
                );
                $return = [
                    "token" => $token,
                ];
                break;
            case PaymentProcessorIdentifier::LEMONWAY_SEPA_DIRECT_DEBIT()->getValue() :
            case PaymentProcessorIdentifier::LEMONWAY_DEFERED_SEPA_DIRECT_DEBIT()->getValue() :
                $lemonwayData = [
                    'bankName' => $requestData['bankName'],
                    'iban' => $requestData['iban'],
                    'bic' => $requestData['bic'],
                    'firstName' => $requestData['firstName'],
                    'lastName' => $requestData['lastName'],
                    'gender' => $requestData['gender'],
                    'processor_id' => $paymentProcessorId,
                ];
                $userId = $this->userSecurityService->getCurrentUserId();
                $user = $this->userService->get($userId);
                $token = $this->lemonway->createSepaMandate($user, $lemonwayData, $requestData['returnUrl'], $requestData['errorUrl'], true);
                $electronicSignature = $this->userPaymentInfoService->getLemonwayElectronicSignature($userId);

                $return = [
                    "token" => $token,
                    "signDocumentUrl" => $this->lemonway->getLemonwayWebKitUrl() . '/?signingToken=' . $electronicSignature,
                ];
                break;
            default:
                $token = 0;
                $return = [
                    'token' => $token,
                ];
                break;
        }

        return $return;
    }

    /**
     * @throws BankAccountIsMissing
     */
    public function getMandates(): array
    {
        $data = [];
        $userId = $this->userSecurityService->getCurrentUserId();
        $lemonwayMandate = $this->lemonway->getMandate($userId);
        $hipayMandate = $this->hiPay->getMandate($userId);

        if ($lemonwayMandate !== null) {
            $data[] = $lemonwayMandate->expose();
        }

        if (\count($hipayMandate) > 0) {
            $data[] = $hipayMandate;
        }

        return $data;
    }

    /**
     * @param string[] $data
     * @throws UnknownPaymentProcessor
     * @throws FieldsValidationException
     */
    protected function validateDirectDebitData(int $paymentProcessorId, array $data): bool
    {
        if (null === $this->constraintsByPsp) {
            $this->generateConstraints();
        }

        if (false === \array_key_exists($paymentProcessorId, $this->constraintsByPsp)) {
            throw new UnknownPaymentProcessor('Invalid payment processor id');
        }

        $validator = Validation::createValidatorBuilder()->getValidator();
        $violations = $validator->validate(
            $data,
            new Collection(
                [
                    'fields' => $this->constraintsByPsp[$paymentProcessorId],
                    'allowExtraFields' => false,
                    'missingFieldsMessage' => "{{ field }} must be set.",
                ]
            )
        );

        if (\count($violations) > 0) {
            $errorMessage = array_map(function (ConstraintViolationInterface $violation): string {
                return $violation->getMessage();
            }, iterator_to_array($violations));
            throw new FieldsValidationException(implode(' ', $errorMessage));
        }

        return true;
    }

    protected function validatePaymentId(int $paymentId): int
    {
        $paymentProcessor = fn_get_processor_data($paymentId);

        if ($paymentProcessor !== false && true === \array_key_exists('processor_id', $paymentProcessor)) {
            $paymentData = fn_get_payment_method_data($paymentId);
            if ($paymentData['status'] !== 'A') {
                throw new ActionNotAllowed('This payment id is unavailable at this moment');
            }
            $paymentIdentifier = new PaymentProcessorIdentifier((int) $paymentProcessor['processor_id']);

            if (true === $paymentIdentifier->equals(PaymentProcessorIdentifier::HIPAY_DEFERED_SEPA_DIRECT_DEBIT())
                || true === $paymentIdentifier->equals(PaymentProcessorIdentifier::HIPAY_SEPA_DIRECT_DEBIT())
                || true === $paymentIdentifier->equals(PaymentProcessorIdentifier::LEMONWAY_DEFERED_SEPA_DIRECT_DEBIT())
                || true === $paymentIdentifier->equals(PaymentProcessorIdentifier::LEMONWAY_SEPA_DIRECT_DEBIT())
            ) {
                return (int) $paymentProcessor['processor_id'];
            }
        }
        throw new UnknownPaymentProcessor('Invalid payment id');
    }
}
