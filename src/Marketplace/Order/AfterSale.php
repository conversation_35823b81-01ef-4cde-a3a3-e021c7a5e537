<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Order;

class AfterSale
{
    public const LITIGATION_REASON_PACKET_NOT_RECIEVED    = 'w_litigation_not_received';
    public const LITIGATION_REASON_PACKET_DOES_NOT_COMPLY = 'w_litigation_packet_does_not_comply';
    public const LITIGATION_REASON_OTHER                  = 'w_litigation_other';

    public const SAV_REASON_BROKEN       = 'w_SAV_reason_broken';
    public const SAV_REASON_MALFUNCTION  = 'w_SAV_reason_malfunction';
    public const SAV_REASON_MONTAGE      = 'w_SAV_reason_montage';
    public const SAV_REASON_OTHER        = 'w_SAV_reason_other';
    public const SAV_REASON_OUT_OF_ORDER = 'w_SAV_reason_out_of_order';

    /**
     * @param string $type
     */
    public static function getType($type): array
    {
        if ($type === 'litigation') {
            return [
                self::LITIGATION_REASON_PACKET_DOES_NOT_COMPLY,
                self::LITIGATION_REASON_PACKET_NOT_RECIEVED,
                self::LITIGATION_REASON_OTHER,
            ];
        }

        return [
            self::SAV_REASON_BROKEN,
            self::SAV_REASON_MONTAGE,
            self::SAV_REASON_MALFUNCTION,
            self::SAV_REASON_OTHER,
            self::SAV_REASON_OUT_OF_ORDER,
        ];
    }
}
