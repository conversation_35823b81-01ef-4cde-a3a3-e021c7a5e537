<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Order;

use Wizacha\Money\Money;

interface OrderPricesInterface
{
    public function getId(): ?int;

    public function getUserId(): int;

    public function getTotal(): Money;

    public function getMarketplaceDiscountTotal(): Money;

    public function hasMarketplaceDiscount(): bool;

    public function getCustomerTotal(): Money;

    public function getSubtotal(): Money;

    public function getTaxTotal(): Money;

    public function getBalanceTotal(): Money;
}
