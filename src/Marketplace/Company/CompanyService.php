<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Company;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Driver\PDOStatement;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Response;
use Wizacha\Bridge\CsCart\ErrorNotification;
use Wizacha\Company as LegacyCompany;
use Wizacha\Events\IterableEvent;
use Wizacha\ImageManager;
use Wizacha\Marketplace\Company\Event\CompanyApplied;
use Wizacha\Marketplace\Company\Event\CompanyLegalDocumentsUpdated;
use Wizacha\Marketplace\Entities\Company as CompanyEntity;
use Wizacha\Marketplace\Exception\AlreadyExists;
use Wizacha\Marketplace\Exception\CompanyNotFound;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Image\Exception\BadImage;
use Wizacha\Marketplace\InvoicingSettings\InvoicingSettingsService;
use Wizacha\Marketplace\User\User;
use Wizacha\Marketplace\User\UserService;
use Wizacha\Storage\StorageService;

class CompanyService
{
    private const NEW_COMPANY_STATUS = 'N'; // @TODO : put it in a proper enum

    public const ID_CARD = "idCard";
    public const ADDRESS_PROOF = "addressProof";
    public const RIB = "rib";
    public const KBIS = "kbis";
    public const STATUS_DOCUMENT = "updated_and_signed_status_document";
    public const DIVISION_OF_POWER = "division_of_powers";
    public const DIVISION_OF_POWER_ID_CARDS = "division_of_powers_id_cards";
    public const EXTRA_ACTIVITY = "extra_activity";
    public const EXTRA_LEGAL_ID_CARD = "extra_legal_id_card";
    public const EXTRA_STATUS = "extra_status";
    public const SKIP_CACHE = 'skip_cache';

    private static $fileMapping = [
        self::ID_CARD => 'w_ID_card',
        self::RIB => 'w_RIB',
        self::KBIS => 'w_KBIS',
        self::ADDRESS_PROOF => 'w_address_proof',
        self::STATUS_DOCUMENT => 'w_updated_and_signed_status_document',
        self::DIVISION_OF_POWER => 'w_division_of_powers',
        self::DIVISION_OF_POWER_ID_CARDS => 'w_division_of_powers_id_cards',
    ];

    /** @var LoggerInterface */
    private $logger;

    /** @var UserService */
    private $userService;

    /** @var EventDispatcherInterface */
    private $eventDispatcher;

    /** @var ImageManager */
    private $imageManager;

    /** @var Connection */
    private $connection;

    private StorageService $vendorSubscriptionStorage;

    private CompanyRepository $companyRepository;

    private InvoicingSettingsService $invoicingSettingsService;

    public function __construct(
        LoggerInterface $logger,
        UserService $userService,
        EventDispatcherInterface $eventDispatcher,
        ImageManager $imageManager,
        Connection $connection,
        CompanyRepository $companyRepository,
        StorageService $vendorSubscriptionStorage,
        InvoicingSettingsService $invoicingSettingsService
    ) {
        $this->logger = $logger;
        $this->userService = $userService;
        $this->eventDispatcher = $eventDispatcher;
        $this->imageManager = $imageManager;
        $this->connection = $connection;
        $this->companyRepository = $companyRepository;
        $this->vendorSubscriptionStorage = $vendorSubscriptionStorage;
        $this->invoicingSettingsService = $invoicingSettingsService;
    }

    public function register(int $userId = null, array $data, string $companyType = null): Company
    {
        $companyType = $companyType ?? (string) CompanyType::PROFESSIONAL();
        $user = null;

        if ($userId === null) {
            if (empty($data['legalRepresentativeFirstName']) || empty($data['legalRepresentativeLastName'])) {
                throw new \InvalidArgumentException('legalRepresentativeFirstName and legalRepresentativeLastName are mandatory if you are not authenticated');
            }
        } else {
            $user = $this->userService->get($userId);
            if (!\is_null($user->getCompanyId())) {
                throw new Forbidden("You cannot register more than one company");
            }
            if ($user->getUserType() === \Wizacha\User::ADMIN_TYPE) {
                throw new Forbidden("Administrators cannot register a company");
            }
        }

        if (isset($data['nafCode']) && \strlen($data['nafCode']) > 6) {
            throw new \InvalidArgumentException(__('error_naf_code'));
        }

        $this->checkInvoicingDisabled($data);

        if (\array_key_exists('invoicingDisabled', $data) === false) {
            $data['invoicingDisabled'] = $this->invoicingSettingsService->getMarketplaceInvoicingDisplayed();
        }

        $cscartData = $this->mapDataToCscartData($user, $data, $companyType);

        $companyId = ErrorNotification::catchErrorNotifications(function () use ($cscartData) {
            return fn_update_company($cscartData);
        });
        if (!$companyId) {
            throw new \Exception('Unknown error');
        }

        $event = new CompanyApplied(
            fn_get_company_data($companyId),
            $user ? $user->getFirstname() : (string) $data['legalRepresentativeFirstName'],
            $user ? $user->getLastname() : (string) $data['legalRepresentativeLastName'],
            ''
        );
        $this->eventDispatcher->dispatch($event, CompanyApplied::class);
        $data['w_company_type'] = $companyType;

        return $this->get((int) $companyId);
    }

    public function update(int $companyId, int $userId, array $data): void
    {
        if (empty($data)) {
            return;
        }

        $this->checkUserAccessToCompany($userId, $companyId);

        $this->checkInvoicingDisabled($data);

        $cscartData = $this->mapUpdateDataToCscartData($data);

        ErrorNotification::catchErrorNotifications(function () use ($cscartData, $companyId) {
            return fn_update_company($cscartData, $companyId);
        });
    }

    /**
     * @param int   $userId
     * @param array $data
     *
     * @return Company
     * @throws CompanyNotFound
     * @throws ErrorNotification
     */
    public function registerC2C(int $userId, array $data): Company
    {
        $this->checkInvoicingDisabled($data);

        if (\array_key_exists('invoicingDisabled', $data) === false) {
            $data['invoicingDisabled'] = $this->invoicingSettingsService->getMarketplaceInvoicingDisplayed();
        }

        $cscartData = $this->mapUpdateDataToCscartData($data);

        $companyId = ErrorNotification::catchErrorNotifications(function () use ($userId, $cscartData) {
            return \Wizacha\Company::newC2C($userId, $cscartData['company'], $cscartData);
        });
        if (!$companyId) {
            throw new \Exception('Unknown error');
        }

        $company = $this->get((int) $companyId);

        $event = new CompanyApplied(
            fn_get_company_data($companyId),
            $company->getLegalRepresentativeFirstName(),
            $company->getLegalRepresentativeLastName(),
            ''
        );
        $this->eventDispatcher->dispatch($event, CompanyApplied::class);

        $this->eventDispatcher->dispatch(IterableEvent::fromElement((int) $companyId), CompanyEvents::SEND_TO_PSP);

        return $company;
    }

    /** @param UploadedFile[] $files */
    public function saveRegistrationFiles(int $userId = null, int $companyId, array $files, bool $isAdmin = false): array
    {
        $companyData = fn_get_company_data($companyId, (string) GlobalState::contentLocale(), [self::SKIP_CACHE => true]);
        if (!isset($companyData['company_id'])) {
            throw CompanyNotFound::fromCompanyId($companyId);
        }

        if (\array_key_exists('request_user_id', $companyData) === true
            && $companyData['request_user_id'] > 0
            && (int) $companyData['request_user_id'] !== $userId
            && $isAdmin === false
        ) {
            throw new Forbidden();
        }

        $namesMap = $this->getFileMapping();

        $result = [];
        foreach ($files as $key => $file) {
            $originalKey = $key;
            try {
                $key = $this->getFilenameFromFileMapping($originalKey, $file);

                $this->putFile($key, $companyId, $file);

                $result[$originalKey] = [
                    'success' => true,
                ];
            } catch (\Throwable $e) {
                $this->logger->error('Failed to save company registration file.', [
                    'exception' => $e,
                ]);

                $result[$originalKey] = [
                    'success' => false,
                    'error'   => $e->getMessage(),
                ];
            }
        }

        if (!empty($files)) {
            container()->get('event_dispatcher')->dispatch(
                new CompanyLegalDocumentsUpdated($companyId, array_map(function ($key) use ($namesMap) {
                    return $namesMap[$key] ?? $key;
                }, array_keys($files))),
                CompanyEvents::LEGAL_DOCUMENTS_UPDATED
            );
        }

        return $result;
    }

    public function getRegistrationFilesList(int $companyId, int $userId = null, bool $addFilename = false, array $updatedFiles = []): array
    {
        $this->checkUserAccessToCompanyFiles($userId, $companyId);
        $files = $this->vendorSubscriptionStorage->getList($companyId . '/');
        $legalDocuments = [];
        $map = $this->getFileMapping();

        if (\count($updatedFiles) > 0) {
            $map = \array_filter($map, function ($file) use ($updatedFiles) {
                return \in_array($file, $updatedFiles);
            });
        }

        $map = array_flip($map);

        foreach ($files as $file) {
            $matches = [];
            // Remove `w_` prefix on files
            if (preg_match('/(w_[^0-9]+)_/', $file, $matches)) {
                if ($map[$matches[1]]) {
                    if ($addFilename) {
                        $legalDocuments[$map[$matches[1]]] = $file;
                    } else {
                        $legalDocuments[] = $map[$matches[1]];
                    }
                }
            // extra files are seved with name : `extra_file_type_companyId_original_file_name`
            // The companyId is used to seperate the api_name and the filename
            } elseif (preg_match("/^(extra_.*)_{$companyId}_.*/", $file, $matches)) {
                if ($addFilename) {
                    $legalDocuments[$matches[1]] = $file;
                } else {
                    $legalDocuments[] = $matches[1];
                }
            }
        }

        return $legalDocuments;
    }

    public function getFile(int $companyId, string $filename, ?int $userId): Response
    {
        $this->checkUserAccessToCompanyFiles($userId, $companyId);

        if (strpos($filename, 'extra') !== 0) {
            $filename = $this->getFileMapping()[$filename] ?? null;
            if (!$filename) {
                throw new NotFound();
            }
        }
        $files = $this->vendorSubscriptionStorage->getList($companyId . '/');

        foreach ($files as $file) {
            if (strpos($file, $filename) === 0) {
                return $this->vendorSubscriptionStorage->get($companyId . '/' . $file);
            }
        }

        throw new NotFound();
    }

    public function updateFile(int $companyId, string $filename, UploadedFile $newFile): void
    {
        // On supprime l'ancien fichier pour eviter les doublons.
        $this->deleteFile($companyId, $filename);

        // On ajoute le nouveau fichier.
        $key = $this->getFilenameFromFileMapping($filename, $newFile);
        $this->putFile($key, $companyId, $newFile);

        $this->eventDispatcher->dispatch(
            new CompanyLegalDocumentsUpdated($companyId, [$key]),
            CompanyEvents::LEGAL_DOCUMENTS_UPDATED
        );
    }


    /**
     * Fonction qui permet de supprimer un fichier en fonction de son nom et de sa company
     *
     * @param int    $companyId
     * @param string $filename => idCard, rib, extra_custom_file
     *
     * @throws \Exception
     */
    public function deleteFile(int $companyId, string $filename): void
    {
        $fileMapping = $this->getFileMapping()[$filename] ?? null;

        if (strpos($filename, 'extra') !== 0) {
            if (!$fileMapping) {
                throw new NotFound();
            }
        }
        $files = $this->vendorSubscriptionStorage->getList($companyId . '/');



        $delete = false;
        $found = false;
        foreach ($files as $file) {
            if (strpos($file, $fileMapping) === 0) {
                $delete = $this->vendorSubscriptionStorage->delete($companyId . '/' . $file);
                $found = true;
            }
        }

        if (!$delete && $found) {
            throw new \Exception("Unable to delete file");
        }
    }

    public function get(int $id): Company
    {
        $data = fn_get_company_data($id);

        if (!\is_array($data) || !isset($data['company_id'])) {
            throw CompanyNotFound::fromCompanyId($id);
        }

        return new Company($data);
    }

    /**
     * @param int|null $userId
     * @param int $companyId
     * @throws CompanyNotFound
     * @throws Forbidden
     * @throws NotFound
     */
    public function checkUserAccessToCompany(?int $userId, int $companyId)
    {
        if (!\is_null($userId)) {
            $user = $this->userService->get($userId);

            if ($user->getCompanyId() !== $companyId) { // check if user is one of the company's admins
                // else we check if the user is the one which submitted the company creation request
                $companyData = fn_get_company_data($companyId, (string) GlobalState::contentLocale(), [self::SKIP_CACHE => true]);
                if (!isset($companyData['company_id'])) {
                    throw CompanyNotFound::fromCompanyId($companyId);
                }
                if (!isset($companyData['request_user_id']) || (int) $companyData['request_user_id'] !== $userId) {
                    throw new Forbidden();
                }
            }
        }
    }

    /**
     * @param int|null $userId
     * @param int $companyId
     * @throws CompanyNotFound
     * @throws Forbidden
     * @throws NotFound
     */
    public function checkUserAccessToCompanyFiles(?int $userId, int $companyId)
    {
        if (\is_null($userId) === false) {
            $user = $this->userService->get($userId);

            if ($user->getCompanyId() !== $companyId) {
                $companyData = fn_get_company_data(
                    $companyId,
                    (string) GlobalState::contentLocale(),
                    [self::SKIP_CACHE => true]
                );
                if (\array_key_exists('company_id', $companyData) === false) {
                    throw CompanyNotFound::fromCompanyId($companyId);
                }
                if ((\array_key_exists('request_user_id', $companyData) === false
                    || (int) $companyData['request_user_id'] !== $userId)
                    && $user->isMarketplaceAdministrator() === false
                ) {
                    throw new Forbidden();
                }
            }
        }
    }

    /**
     * @param int $companyId
     * @return int
     * @throws NotFound
     */
    public function getCompanyImageId(int $companyId): int
    {
        $pairs = $this->imageManager->getPairs('company', $companyId);
        $firstPair = reset($pairs);
        if ($firstPair === false || $firstPair['detailed_id'] === '0') {
            throw new NotFound("No image of company found");
        }

        return \intval($firstPair['detailed_id']);
    }

    /**
     * @param int $companyId
     * @param UploadedFile $imageFile
     * @return int
     * @throws AlreadyExists
     * @throws BadImage
     */
    public function addCompanyImage(int $companyId, UploadedFile $imageFile): int
    {
        $pairs = $this->imageManager->getPairs('company', $companyId);
        if (!empty($pairs)) {
            throw new AlreadyExists();
        }

        $imageId = $this->imageManager->createFromUploadedFile($imageFile);
        $result = $this->imageManager->createPairs([$imageId], 'company', $companyId);

        if ($result === false) {
            throw new BadImage('Format of uploaded file incorrect');
        }

        return $imageId;
    }

    /**
     * @param int $companyId
     * @return bool
     * @throws \Exception
     */
    public function deleteCompanyImage(int $companyId): bool
    {
        $companyImageId = $this->getCompanyImageId($companyId);

        //remove image links
        $this->imageManager->deletePairs([$companyImageId], 'company', $companyId);

        //remove image
        return $this->imageManager->delete($companyImageId);
    }

    /**
     * @return array ex: ["idCard", "rib", ...]
     */
    public static function getFilenames(): array
    {
        return array_keys(static::$fileMapping);
    }

    public function getPendingVendors($loadC2C = false): PDOStatement
    {
        $query = sprintf(
            "
          SELECT company_id
          FROM cscart_companies
          WHERE (status = '%s' AND w_company_type = '%s')
        ",
            \Wizacha\Company::STATUS_PENDING,
            CompanyType::PROFESSIONAL()->getValue()
        );

        if ($loadC2C) {
            $query .= sprintf(
                "OR (status = '%s' AND w_company_type = '%s')",
                \Wizacha\Company::STATUS_ENABLED,
                CompanyType::PRIVATE_INDIVIDUAL()->getValue()
            );
        }

        return $this->connection->executeQuery($query);
    }

    public function getAllVendorsExceptNew(): PDOStatement
    {
        $query = sprintf(
            "
          SELECT company_id, company
          FROM cscart_companies
          WHERE (status != '%s' AND w_company_type = '%s')
        ",
            \Wizacha\Company::STATUS_NEW,
            CompanyType::PROFESSIONAL()->getValue()
        );

        return $this->connection->executeQuery($query);
    }

    public function getLegacyEntity(int $companyId): CompanyEntity
    {
        return new CompanyEntity($companyId);
    }

    public function canRegisterIban(CompanyEntity $company): bool
    {
        return LegacyCompany::STATUS_NEW !== $company->getStatus()
               && false === \is_null($company->getIban()) && mb_strlen($company->getIban()) > 0
               && false === \is_null($company->getBic()) && mb_strlen($company->getBic()) > 0
               && \count($company->getAdmins()) > 0;
    }

    public function canCreatePspAccount(CompanyEntity $company): bool
    {
        $admins = $company->getAdmins();
        $status = $company->getStatus();
        return
            \count($admins) > 0
            && (
                LegacyCompany::STATUS_PENDING === $status
                || LegacyCompany::STATUS_ENABLED === $status
            );
    }

    public function canSendKyc(CompanyEntity $company): bool
    {
        return \count($company->getAdmins()) > 0
               && (
                   LegacyCompany::STATUS_PENDING === $company->getStatus()
                   || LegacyCompany::STATUS_ENABLED === $company->getStatus()
               );
    }

    public function getFileMapping(): array
    {
        return static::$fileMapping;
    }

    public function updateStatus(int $idCompany, CompanyStatus $status): void
    {
        ErrorNotification::catchErrorNotifications(function () use ($idCompany, $status): void {
            fn_companies_change_status($idCompany, $status->getValue());
        });
    }

    /**
     * Function changes company status To New where create company Failed in HiPay
     *
     * @param int $companyId
     * @param CompanyStatus $statusTo
     * @param string $message
     */
    public function ChangeStatusWhenFailed(int $companyId, CompanyStatus $statusTo, string $message): void
    {
        //Delete success notification
        fn_delete_notification('changes_saved');

        //Set error notification
        fn_set_notification('E', __('error'), __('vendor_hipay_account_has_not_been_created') . $message);

        $this->connection->executeUpdate(
            'UPDATE cscart_companies SET status = :status WHERE company_id = :companyId',
            ['status' => $statusTo, 'companyId' => $companyId]
        );
    }

    /**
     * @param mixed[] $data Company data provided through the REST API
     *
     * @return mixed[] Company data with extra data processed
     */
    protected function processExtraApiToCsCartData(array $data): array
    {
        if (\array_key_exists('extra', $data) === false || \count($data) === 0) {
            return $data;
        }

        if (\array_key_exists('hipayId', $data['extra']) && \strlen((string) $data['extra']['hipayId']) > 0) {
            $data['hipay_id'] = (string) $data['extra']['hipayId'];
            unset($data['extra']['hipayId']);
        }

        return $data;
    }

    private function mapDataToCscartData(User $user = null, array $data, string $companyType = null): array
    {
        $data = $this->processExtraApiToCsCartData($data);
        $companyType = $companyType ?? (string) CompanyType::PROFESSIONAL();

        return [
            'timestamp' => time(),
            'company' => (string) $data['name'],
            'corporate_name' => (string) $data['corporateName'],
            'email' => (string) $data['email'],
            'company_description' => (string) $data['description'],
            'address' => (string) $data['address'],
            'city' => (string) $data['city'],
            'state' => (string) $data['state'],
            'country' => (string) $data['country'],
            'zipcode' => (string) $data['zipcode'],
            'phone' => (string) $data['phoneNumber'],
            'w_legal_status' => (string) $data['legalStatus'],
            'w_siret_number' => (string) $data['siretNumber'],
            'w_vat_number' => (string) $data['vatNumber'],
            'w_extras' => [
                'w_capital' => (string) $data['capital'],
                'w_RCS' => (string) $data['rcs'],
            ],
            'fax' => (string) $data['fax'],
            'url' => (string) $data['url'],
            'seo_name' => isset($data['slug']) ? (string) $data['slug'] : null,
            'w_company_type' => $companyType,
            'status' => self::NEW_COMPANY_STATUS,
            'request_user_id' => $user ? $user->getUserId() : 0,
            'request_account_data' => serialize([
                'company' => (string) $data['name'],
                'admin_firstname' => $user ? $user->getFirstname() : (string) $data['legalRepresentativeFirstName'],
                'admin_lastname' => $user ? $user->getLastname() : (string) $data['legalRepresentativeLastName'],
            ]),
            'legal_representative_firstname' => $user ? $user->getFirstname() : (string) $data['legalRepresentativeFirstName'],
            'legal_representative_lastname'  => $user ? $user->getLastname() : (string) $data['legalRepresentativeLastName'],
            'iban' => $data['iban'] ?? null,
            'bic' => $data['bic'] ?? null,
            'extra' => $data['extra'] ?? [],
            'naf_code' => $data['nafCode'] ?? null,
            'w_company_meta_description' => $data['metaDescription'] ?? null,
            'w_company_meta_keywords' => $data['metaKeywords'] ?? null,
            'w_company_meta_title' => $data['metaTitle'] ?? null,
            'hipay_id' => $data['hipay_id'] ?? null,
            'invoicing_disabled' => $data['invoicingDisabled'],
        ];
    }

    private function mapUpdateDataToCscartData(array $data): array
    {
        $data = $this->processExtraApiToCsCartData($data);
        $newData = [];

        if (isset($data['name'])) {
            $newData['company'] = (string) $data['name'];
        }
        if (\array_key_exists('corporateName', $data) === true) {
            $newData['corporate_name'] = $data['corporateName'];
        }
        if (isset($data['email'])) {
            $newData['email'] = (string) $data['email'];
        }
        if (isset($data['description'])) {
            $newData['company_description'] = (string) $data['description'];
        }
        if (isset($data['address'])) {
            $newData['address'] = (string) $data['address'];
        }
        if (isset($data['city'])) {
            $newData['city'] = (string) $data['city'];
        }
        if (isset($data['state'])) {
            $newData['state'] = (string) $data['state'];
        }
        if (isset($data['country'])) {
            $newData['country'] = (string) $data['country'];
        }
        if (isset($data['zipcode'])) {
            $newData['zipcode'] = (string) $data['zipcode'];
        }
        if (isset($data['phoneNumber'])) {
            $newData['phone'] = (string) $data['phoneNumber'];
        }
        if (isset($data['legalStatus'])) {
            $newData['w_legal_status'] = (string) $data['legalStatus'];
        }
        if (isset($data['siretNumber'])) {
            $newData['w_siret_number'] = (string) $data['siretNumber'];
        }
        if (isset($data['vatNumber'])) {
            $newData['w_vat_number'] = (string) $data['vatNumber'];
        }
        if (isset($data['capital'])) {
            $newData['w_extras']['w_capital'] = (string) $data['capital'];
        }
        if (isset($data['rcs'])) {
            $newData['w_extras']['w_RCS'] = (string) $data['rcs'];
        }
        if (isset($data['fax'])) {
            $newData['fax'] = (string) $data['fax'];
        }
        if (isset($data['url'])) {
            $newData['url'] = (string) $data['url'];
        }
        if (isset($data['slug'])) {
            $newData['seo_name'] = (string) $data['slug'];
        }
        if (isset($data['legalRepresentativeFirstName'])) {
            $newData['legal_representative_firstname'] = (string) $data['legalRepresentativeFirstName'];
        }
        if (isset($data['legalRepresentativeLastName'])) {
            $newData['legal_representative_lastname'] = (string) $data['legalRepresentativeLastName'];
        }
        if (isset($data['iban'])) {
            $newData['iban'] = (string) $data['iban'];
        }
        if (isset($data['bic'])) {
            $newData['bic'] = (string) $data['bic'];
        }
        if (isset($data['commission_percent'])) {
            $newData['commission_percent'] = (string) $data['commission_percent'];
        }
        if (isset($data['extra']) && \count($data['extra']) > 0) {
            $newData['extra'] = (array) $data['extra'];
        }
        if (\is_string($data['nafCode'])) {
            $newData['naf_code'] = $data['nafCode'];
        }
        if (\array_key_exists('meta', $data)) {
            if (\array_key_exists('description', $data['meta'])) {
                $newData['w_company_meta_description'] = $data['meta']['description'];
            }
            if (\array_key_exists('keywords', $data['meta'])) {
                $newData['w_company_meta_keywords'] = $data['meta']['keywords'];
            }
            if (\array_key_exists('title', $data['meta'])) {
                $newData['w_company_meta_title'] = $data['meta']['title'];
            }
        }
        if (\array_key_exists('hipay_id', $data)) {
            $newData['hipay_id'] = $data['hipay_id'];
        }
        if (\array_key_exists('invoicingDisabled', $data)) {
            $newData['invoicing_disabled'] = $data['invoicingDisabled'];
        }

        return $newData;
    }

    private function getFilenameFromFileMapping(string $filename, UploadedFile $file): string
    {
        $filenameFromFileMapping = $filename;

        $namesMap = $this->getFileMapping();

        if (!isset($namesMap[$filename])) { // We don't support this file
            if (!preg_match('/^extra_[a-z_]+$/', $filename)) { // Is it an extra file ?
                // Not an extra => KO
                throw new \Exception("Invalid file");
            }
        } else {
            $filenameFromFileMapping = $namesMap[$filename];
        }


        if (!fn_w_check_apply_for_vendor_files($file)) {
            throw new \Exception('Invalid file');
        }

        return $filenameFromFileMapping;
    }

    private function putFile(string $key, int $companyId, UploadedFile $file)
    {
        $filename = $key . '_' . $companyId . '_' . $file->getClientOriginalName();

        $storageResult = $this->vendorSubscriptionStorage->put($companyId . '/' . $filename, [
            'file' => $file->getRealPath(),
        ]);
        if (!$storageResult) {
            throw new \Exception('Failed to save file');
        }
    }

    public function isSiretNumberValid(string $siretNumber): bool
    {
        return preg_match('/\s/', $siretNumber) === 0 && strpos($siretNumber, '.') === false;
    }

    /** @return int[] */
    public function getActiveCompanyAdmins(int $companyId): array
    {
        return $this->companyRepository->getActiveCompanyAdmins($companyId);
    }

    /** @return bool[]  ex: ['invoicing_disabled' => true|false, 'invoicing_disabled_by_admin' => true|false] */
    public function getInvoicingConfiguration(int $companyId): array
    {
        return $this->companyRepository->getInvoicingConfiguration($companyId);
    }

    public function isInvoicingDisabled(int $companyId): bool
    {
        return $this->getInvoicingConfiguration($companyId)['invoicing_disabled'];
    }

    public function canEditInvoicingDisplayed(int $companyId, bool $isAdmin): bool
    {
        $invoicingConfiguration = $this->getInvoicingConfiguration($companyId);

        return $invoicingConfiguration['invoicing_disabled'] === false || $isAdmin === true || $invoicingConfiguration['invoicing_disabled_by_admin'] === false;
    }

    /** @param mixed[] $data */
    private function checkInvoicingDisabled(array $data): void
    {
        if (\array_key_exists('invoicingDisabled', $data) === true
            && ((($this->invoicingSettingsService->getMarketplaceInvoicingDisplayed() === true
            && $data['invoicingDisabled'] === false)
            || \is_bool($data['invoicingDisabled']) === false))
        ) {
                throw new \InvalidArgumentException('Invalid InvoicingDisabled');
        }
    }

    public function getCompanyHipayId(?int $companyId): string
    {
        return $this->companyRepository->getCompanyHipayId($companyId);
    }

    public function getCompanyStripeId(?int $companyId): string
    {
        return $this->companyRepository->getCompanyStripeId($companyId);
    }
}
