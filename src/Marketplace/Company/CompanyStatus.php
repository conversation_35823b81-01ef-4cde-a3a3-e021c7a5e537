<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Company;

use MyCLabs\Enum\Enum;

/**
 * @method static CompanyStatus NEW()
 * @method static CompanyStatus PENDING()
 * @method static CompanyStatus ENABLED()
 * @method static CompanyStatus DISABLED()
 */
class CompanyStatus extends Enum
{
    public const NEW = 'N';
    public const PENDING = 'P';
    public const ENABLED = 'A';
    public const DISABLED = 'D';

    public static function createFromApiValue(string $value): self
    {
        if (static::isValidKey($value) === false) {
            throw new \UnexpectedValueException('Key \'key\' is not part of the enum ' . \get_called_class());
        }

        return static::{$value}();
    }

    public function getApiValue(): string
    {
        return $this->getKey();
    }
}
