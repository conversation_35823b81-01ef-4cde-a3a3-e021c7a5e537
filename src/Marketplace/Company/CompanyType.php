<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Company;

use MyCLabs\Enum\Enum;

/**
 * @method static CompanyType PROFESSIONAL()
 * @method static CompanyType PRIVATE_INDIVIDUAL()
 */
class CompanyType extends Enum
{
    /**
     * Le vendeur est un professionel, on rentre donc dans la catégorie B2x
     */
    private const PROFESSIONAL = 'V';

    /**
     * Le vendeur est un particulier, on rentre donc dans la catégorie C2x
     */
    private const PRIVATE_INDIVIDUAL = 'C';
}
