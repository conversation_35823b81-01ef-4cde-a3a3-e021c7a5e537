<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Company\Event;

use Symfony\Contracts\EventDispatcher\Event;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Wizacha\Component\Notification\NotificationEvent;

class CompanyApplied extends Event implements NotificationEvent
{
    /**
     * @var array Data returned by fn_get_company_data()
     */
    private $companyData;

    /**
     * @var string
     */
    private $adminFirstName;

    /**
     * @var string
     */
    private $adminLastName;

    /**
     * @var string
     */
    private $messageToAdmin;

    public function __construct(array $companyData, string $adminFirstName, string $adminLastName, string $message)
    {
        $this->companyData = $companyData;
        $this->adminFirstName = $adminFirstName;
        $this->adminLastName = $adminLastName;
        $this->messageToAdmin = $message;
    }

    public function getCompanyData(): array
    {
        return $this->companyData;
    }

    public function getAdminFirstName(): string
    {
        return $this->adminFirstName;
    }

    public function getAdminLastName(): string
    {
        return $this->adminLastName;
    }

    public function getMessageToAdmin(): string
    {
        return $this->messageToAdmin;
    }

    public static function buildDebugForm(FormBuilder $form)
    {
        $form->add('companyId', TextType::class);
    }

    public static function createFromForm(Form $form)
    {
        $companyId = $form->getData()['companyId'];
        $companyData = fn_get_company_data($companyId);

        return new self($companyData, __('first_name'), __('last_name'), '');
    }

    public static function getDescription(): string
    {
        return 'company_applied';
    }
}
