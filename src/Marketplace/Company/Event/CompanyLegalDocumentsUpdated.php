<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Company\Event;

use Symfony\Contracts\EventDispatcher\Event;

class CompanyLegalDocumentsUpdated extends Event
{
    /**
     * @var int
     */
    private $companyId;
    /**
     * @var array|string[]
     */
    private $documents;

    /**
     * CompanyLegalDocumentsUpdated constructor.
     * @param int $companyId
     * @param string[] $documents values like w_KBIS w_RIB w_ID_card
     */
    public function __construct(int $companyId, array $documents)
    {
        $this->companyId = $companyId;
        $this->documents = $documents;
    }

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    /**
     * @return string[]
     */
    public function getDocuments(): array
    {
        return $this->documents;
    }
}
