<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Company\Event;

use Symfony\Contracts\EventDispatcher\Event;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Wizacha\Component\Notification\NotificationEvent;

class CompanyPending extends Event implements NotificationEvent
{
    /** @var mixed[] returned by fn_get_company_data() */
    private array $companyData;

    /** @var mixed[] */
    private array $userData;

    public function __construct(
        array $companyData,
        array $userData
    ) {
        $this->companyData = $companyData;
        $this->userData = $userData;
    }

    /** @return mixed[] */
    public function getCompanyData(): array
    {
        return $this->companyData;
    }

    public static function buildDebugForm(FormBuilder $form): void
    {
        $form->add('companyId', TextType::class);
    }

    public static function createFromForm(Form $form): self
    {
        $companyId = $form->getData()['companyId'];
        $companyData = fn_get_company_data($companyId);
        $userData = fn_get_user_info(db_get_field('SELECT user_id FROM ?:users WHERE company_id = ?i', $companyId));
        $userData['password1'] = 'fakePass';

        return new self($companyData, $userData);
    }

    public static function getDescription(): string
    {
        return 'company_pending';
    }

    /** @return mixed[] */
    public function getUserData(): array
    {
        return $this->userData;
    }
}
