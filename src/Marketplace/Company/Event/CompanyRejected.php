<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Company\Event;

use Symfony\Contracts\EventDispatcher\Event;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Wizacha\Component\Notification\NotificationEvent;

class CompanyRejected extends Event implements NotificationEvent
{
    /**
     * @var array Data returned by fn_get_company_data()
     */
    private $companyData;

    /**
     * @var string
     */
    private $reason;

    public function __construct(array $companyData, string $reason)
    {
        $this->companyData = $companyData;
        $this->reason = $reason;
    }

    public function getCompanyData(): array
    {
        return $this->companyData;
    }

    public static function buildDebugForm(FormBuilder $form)
    {
        $form->add('companyId', TextType::class);
    }

    public static function createFromForm(Form $form)
    {
        $companyId = $form->getData()['companyId'];
        $companyData = fn_get_company_data($companyId);

        return new self($companyData, \Faker\Factory::create()->sentence());
    }

    public static function getDescription(): string
    {
        return 'company_rejected';
    }

    public function getReason(): string
    {
        return $this->reason;
    }
}
