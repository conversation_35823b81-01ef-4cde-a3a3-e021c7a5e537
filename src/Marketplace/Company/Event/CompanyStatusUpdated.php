<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Company\Event;

use Symfony\Contracts\EventDispatcher\Event;

class CompanyStatusUpdated extends Event
{
    private $id;
    private $statusFrom;
    private $statusTo;

    public function __construct(int $id, string $statusFrom, string $statusTo)
    {
        $this->id = $id;
        $this->statusFrom = $statusFrom;
        $this->statusTo = $statusTo;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getStatusFrom(): string
    {
        return $this->statusFrom;
    }

    public function getStatusTo(): string
    {
        return $this->statusTo;
    }
}
