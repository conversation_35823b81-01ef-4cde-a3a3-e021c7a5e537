<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Company\Event;

use Symfony\Contracts\EventDispatcher\Event;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Wizacha\Component\Notification\NotificationEvent;

class C2cCompanyApplied extends Event implements NotificationEvent
{
    /**
     * @var array Data returned by fn_get_company_data()
     */
    private $companyData;

    /**
     * @var string
     */
    private $firstName;

    /**
     * @var string
     */
    private $lastName;

    public function __construct(array $companyData, string $firstName, string $lastName)
    {
        $this->companyData = $companyData;
        $this->firstName = $firstName;
        $this->lastName = $lastName;
    }

    public function getCompanyData(): array
    {
        return $this->companyData;
    }

    public static function buildDebugForm(FormBuilder $form)
    {
        $form->add('companyId', TextType::class);
    }

    public static function createFromForm(Form $form)
    {
        $companyId = $form->getData()['companyId'];
        $companyData = fn_get_company_data($companyId);

        return new self($companyData, __('first_name'), __('last_name'));
    }

    public static function getDescription(): string
    {
        return 'c2c_company_applied';
    }

    public function getFirstName(): string
    {
        return $this->firstName;
    }

    public function getLastName(): string
    {
        return $this->lastName;
    }
}
