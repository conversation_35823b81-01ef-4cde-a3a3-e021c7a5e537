<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Company;

use Doctrine\DBAL\Connection;
use Wizacha\Marketplace\User\UserType;

class CompanyRepository
{
    private Connection $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
    }

    /** @return int[] */
    public function getActiveCompanyAdmins(int $companyId): array
    {
        $getStatement = $this->connection->prepare("SELECT user_id FROM cscart_users WHERE company_id = :companyId AND status = :status AND user_type = :userType");
        $getStatement->execute(
            [
                'companyId' => $companyId,
                'status' => CompanyStatus::ENABLED()->getValue(),
                'userType' => UserType::VENDOR()->getValue()
            ]
        );

        return array_map(
            function ($user) {
                return \intval($user['user_id']);
            },
            $getStatement->fetchAllAssociative()
        );
    }

    public function getNbCompaniesEnabled(): int
    {
        $statement = $this->connection->prepare(
            "SELECT COUNT(*) AS nb_vendors FROM cscart_companies WHERE status='" . CompanyStatus::ENABLED . "';"
        );
        $statement->execute();
        $result = $statement->fetchOne();

        return (int) $result;
    }

    /** @return bool[]  ex: ['invoicing_disabled' => true|false, 'invoicing_disabled_by_admin' => true|false] */
    public function getInvoicingConfiguration(int $companyId): array
    {
        $getStatement = $this->connection->prepare("SELECT invoicing_disabled, invoicing_disabled_by_admin FROM cscart_companies WHERE company_id = :companyId");
        $getStatement->execute(
            [
                'companyId' => $companyId,
            ]
        );

        $result = $getStatement->fetch();

        if ($result === false) {
            return ['invoicing_disabled' => false, 'invoicing_disabled_by_admin' => false];
        }

        return ['invoicing_disabled' => (bool) $result['invoicing_disabled'], 'invoicing_disabled_by_admin' => (bool) $result['invoicing_disabled_by_admin']];
    }

    public function getCompanyHipayId(?int $companyId): string
    {
        $getStatement = $this->connection->prepare("SELECT hipay_id FROM cscart_companies WHERE company_id = :companyId");
        $getStatement->execute(
            [
                'companyId' => $companyId,
            ]
        );

        $result = $getStatement->fetchFirstColumn();

        return \count($result) > 0 && $result[0] !== null ? (string) $result[0] : '';
    }

    public function getCompanyStripeId(?int $companyId): string
    {
        $getStatement = $this->connection->prepare("SELECT stripe_id FROM cscart_companies WHERE company_id = :companyId");
        $getStatement->execute(
            [
                'companyId' => $companyId,
            ]
        );

        $result = $getStatement->fetchFirstColumn();

        return \count($result) > 0 && $result[0] !== null ? (string) $result[0] : '';
    }

    /** @return string[] structured data */
    public function getFirstAdmin(int $companyId): array
    {
        $statement = $this->connection->prepare("SELECT * FROM cscart_users WHERE company_id = :companyId");
        $statement->execute(
            [
                'companyId' => $companyId
            ]
        );

        return $statement->fetchAssociative();
    }

    public function getCompanyNameById(int $companyId): ?string
    {
        $statement = $this->connection->prepare("SELECT company FROM cscart_companies WHERE company_id = :companyId");
        $statement->execute(
            [
                'companyId' => $companyId
            ]
        );

        $result =  $statement->fetchOne();

        return $result === false ? null : $result;
    }
}
