<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Company;

use Wizacha\Marketplace\Company\Exception\LogicException;
use Wizacha\Marketplace\Exception\IntegrityConstraintViolation;

class Company
{
    /** @var int */
    private $id;

    /** @var string */
    private $name;

    /** @var string */
    private $corporateName;

    /** @var string */
    private $email;

    /** @var string */
    private $description;

    /** @var string */
    private $address;

    /** @var string */
    private $city;

    /** @var string */
    private $state;

    /** @var string */
    private $country;

    /** @var string */
    private $zipcode;

    /** @var string */
    private $phoneNumber;

    /** @var string */
    private $fax;

    /** @var string */
    private $url;

    /** @var string */
    private $legalStatus;

    /** @var string */
    private $siretNumber;

    /** @var string */
    private $vatNumber;

    /** @var string */
    private $capital;

    /** @var string */
    private $rcs;

    /** @var string */
    private $slug;

    /** @var string */
    private $legalRepresentativeFirstName;

    /** @var string */
    private $legalRepresentativeLastName;

    /** @var CompanyType */
    private $type;

    /** @var string|null */
    private $iban;

    /** @var string|null */
    private $bic;

    /** @var int */
    private $initialBillingNumber;

    /** @var int */
    private $initialRmaNumber;

    private string $prefixBillingNumber;

    private string $prefixCreditNoteNumber;

    /** @var float */
    private $commissionPercent;

    /** @var float */
    private $commissionFixed;

    /** @var string */
    private $terms;

    /** @var string */
    private $rmaAddress;

    /** @var string */
    private $rmaZipcode;

    /** @var string */
    private $rmaCity;

    /** @var string */
    private $rmaCountry;

    /** @var array */
    private $extra;

    /** @var null|string */
    private $nafCode;

    /** @var null|string */
    private $metaDescription;

    /** @var null|string */
    private $metaKeywords;

    /** @var null|string */
    private $metaTitle;

    /** @var CompanyStatus */
    private $status;

    /** @var string */
    protected $hipayId;

    private bool $invoicingDisabled;

    /**
     * Company constructor.
     * @param array $data the array returned by fn_get_company_data
     * @see \Wizacha\Marketplace\Company\CompanyService::get
     * @throws IntegrityConstraintViolation
     */
    public function __construct(array $data)
    {
        $this->checkIntegrity($data);

        $this->id = (int) $data['company_id'];
        $this->name = (string) $data['company'];
        $this->corporateName = $data['corporate_name'];
        $this->email = (string) $data['email'];
        $this->description = (string) $data['company_description'];
        $this->address = (string) $data['address'];
        $this->city = (string) $data['city'];
        $this->state = (string) $data['state'];
        $this->country = (string) $data['country'];
        $this->zipcode = (string) $data['zipcode'];
        $this->phoneNumber = (string) $data['phone'];
        $this->fax = (string) $data['fax'];
        $this->url = (string) $data['url'];
        $this->legalStatus = (string) $data['w_legal_status'];
        $this->capital = (string) $data['w_extras']['w_capital'];
        $this->siretNumber = (string) $data['w_siret_number'];
        $this->vatNumber = (string) $data['w_vat_number'];
        $this->rcs = (string) $data['w_extras']['w_RCS'];
        $this->slug = (string) $data['seo_name'];
        $this->legalRepresentativeFirstName = (string) $data['legal_representative_firstname'];
        $this->legalRepresentativeLastName = (string) $data['legal_representative_lastname'];
        $this->iban = $data['iban'];
        $this->bic = $data['bic'];
        $this->initialBillingNumber = !\is_null($data['initial_billing_number']) ? (int) $data['initial_billing_number'] : null;
        $this->initialRmaNumber = !\is_null($data['initial_rma_number']) ? (int) $data['initial_rma_number'] : null;
        $this->prefixBillingNumber = $data['prefix_billing_number'];
        $this->prefixCreditNoteNumber = $data['prefix_credit_note_number'];
        $this->commissionPercent = (float) $data['commission_percent'];
        $this->commissionFixed = (float) $data['commission_fixed'];
        $this->terms = (string) $data['company_terms'];
        $this->rmaAddress = (string) $data['w_rma_address'];
        $this->rmaZipcode = (string) $data['w_rma_zipcode'];
        $this->rmaCity = (string) $data['w_rma_city'];
        $this->rmaCountry = (string) $data['w_rma_country'];
        try {
            $this->type = new CompanyType($data['w_company_type']);
        } catch (\UnexpectedValueException $e) {
            $this->type = CompanyType::PROFESSIONAL();
        }

        try {
            $this->status = new CompanyStatus($data['status']);
        } catch (\UnexpectedValueException $e) {
            $this->status = CompanyStatus::NEW();
        }

        $this->extra = $data['extra'] ?? [];
        $this->nafCode = $data['naf_code'] ?? null;
        $this->metaDescription = $data['w_company_meta_description'] ? (string) $data['w_company_meta_description'] : null;
        $this->metaKeywords = $data['w_company_meta_keywords'] ? (string) $data['w_company_meta_keywords'] : null;
        $this->metaTitle = $data['w_company_meta_title'] ? (string) $data['w_company_meta_title'] : null;
        $this->hipayId = $data['hipay_id'] ?? null;
        $this->invoicingDisabled = (bool) $data['invoicing_disabled'];
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getCorporateName(): string
    {
        return $this->corporateName;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function getAddress(): string
    {
        return $this->address;
    }

    public function getCity(): string
    {
        return $this->city;
    }

    public function getState(): string
    {
        return $this->state;
    }

    public function getCountry(): string
    {
        return $this->country;
    }

    public function getZipcode(): string
    {
        return $this->zipcode;
    }

    public function getPhoneNumber(): string
    {
        return $this->phoneNumber;
    }

    public function getLegalStatus(): string
    {
        return $this->legalStatus;
    }

    public function getSiretNumber(): string
    {
        return $this->siretNumber;
    }

    public function getVatNumber(): string
    {
        return $this->vatNumber;
    }

    public function getCapital(): string
    {
        return $this->capital;
    }

    public function getRcs(): string
    {
        return $this->rcs;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function getFax(): string
    {
        return $this->fax;
    }

    public function getUrl(): string
    {
        return $this->url;
    }

    public function getLegalRepresentativeFirstName(): string
    {
        return $this->legalRepresentativeFirstName;
    }

    public function getLegalRepresentativeLastName(): string
    {
        return $this->legalRepresentativeLastName;
    }

    public function isPrivateIndividual(): bool
    {
        return $this->type->equals(CompanyType::PRIVATE_INDIVIDUAL());
    }

    public function isProfessional(): bool
    {
        return $this->type->equals(CompanyType::PROFESSIONAL());
    }

    public function getIBAN(): ?string
    {
        return $this->iban;
    }

    public function getBIC(): ?string
    {
        return $this->bic;
    }

    public function hasAutomaticBillingNumber(): bool
    {
        return $this->initialBillingNumber !== null;
    }

    public function getInitialBillingNumber(): int
    {
        if ($this->initialBillingNumber === null) {
            throw new LogicException('Automatic billing number generation is not enabled for the company.');
        }

        return $this->initialBillingNumber;
    }

    public function getPrefixBillingNumber(): string
    {
        return $this->prefixBillingNumber;
    }

    public function hasAutomaticRmaNumber(): bool
    {
        return $this->initialRmaNumber !== null;
    }

    public function getInitialRmaNumber(): int
    {
        if ($this->initialRmaNumber === null) {
            throw new LogicException('Automatic RMA number generation is not enabled for the company.');
        }

        return $this->initialRmaNumber;
    }

    public function getPrefixCreditNoteNumber(): string
    {
        return $this->prefixCreditNoteNumber;
    }

    public function getCommissionPercent(): float
    {
        return $this->commissionPercent;
    }

    public function getCommissionFixed(): float
    {
        return $this->commissionFixed;
    }

    public function getTerms(): string
    {
        return $this->terms;
    }

    public function getRmaAddress(): string
    {
        return $this->rmaAddress;
    }

    public function getRmaZipcode(): string
    {
        return $this->rmaZipcode;
    }

    public function getRmaCity(): string
    {
        return $this->rmaCity;
    }

    public function getRmaCountry(): string
    {
        return $this->rmaCountry;
    }

    public function getExtra(): array
    {
        return $this->extra;
    }

    public function getNafCode(): ?string
    {
        return $this->nafCode;
    }

    public function getMetaDescription(): ?string
    {
        return $this->metaDescription;
    }

    public function getMetaKeywords(): ?string
    {
        return $this->metaKeywords;
    }

    public function getMetaTitle(): ?string
    {
        return $this->metaTitle;
    }

    public function getStatus(): CompanyStatus
    {
        return $this->status;
    }

    public function getHipayId(): ?string
    {
        return $this->hipayId;
    }

    public function setHipayId(string $hipayId): self
    {
        $this->hipayId = $hipayId;

        return $this;
    }

    public function isInvoicingDisabled(): bool
    {
        return $this->invoicingDisabled;
    }

    public function expose(): array
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'corporateName' => $this->getCorporateName(),
            'slug' => $this->getSlug(),
            'email' => $this->getEmail(),
            'description' => $this->getDescription(),
            'status' => $this->getStatus()->getApiValue(),
            'zipcode' => $this->getZipcode(),
            'address' => $this->getAddress(),
            'city' => $this->getCity(),
            'state' => $this->getState(),
            'country' => $this->getCountry(),
            'phoneNumber' => $this->getPhoneNumber(),
            'legalStatus' => $this->getLegalStatus(),
            'siretNumber' => $this->getSiretNumber(),
            'vatNumber' => $this->getVatNumber(),
            'capital' => $this->getCapital(),
            'rcs' => $this->getRcs(),
            'fax' => $this->getFax(),
            'url' => $this->getUrl(),
            'iban' => $this->getIBAN(),
            'bic' => $this->getBIC(),
            'legalRepresentativeFirstName' => $this->getLegalRepresentativeFirstName(),
            'legalRepresentativeLastName' => $this->getLegalRepresentativeLastName(),
            'extra' => $this->getExtra(),
            'nafCode' => $this->getNafCode(),
            'meta' => [
                'title' => $this->getMetaTitle(),
                'keywords' => $this->getMetaKeywords(),
                'description' => $this->getMetaDescription(),
            ],
            'invoicingDisabled' => $this->isInvoicingDisabled(),
        ];
    }

    /**
     * @throws IntegrityConstraintViolation
     */
    private function checkIntegrity(array $data)
    {
        $requiredFields = [
            'company_id',
            'company',
            'email',
        ];
        $missingRequiredFields = array_diff_key(array_flip($requiredFields), $data);
        if (!empty($missingRequiredFields)) {
            throw IntegrityConstraintViolation::areMissing(array_keys($missingRequiredFields));
        }

        if (!ctype_digit($data['company_id'])) {
            throw IntegrityConstraintViolation::isInvalid('company_id');
        }
    }
}
