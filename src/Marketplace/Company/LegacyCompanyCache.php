<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Company;

/**
 * Simple in-memory cache for CSCart company data.
 * Meant to be used by \fn_get_company_data
 */
class LegacyCompanyCache
{
    private $cache = [];

    public function set(string $key, $data): void
    {
        $this->cache[$key] = $data;
    }

    public function get(string $key)
    {
        return $this->cache[$key] ?? null;
    }
}
