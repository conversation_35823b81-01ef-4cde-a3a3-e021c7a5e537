<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Currency;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Wizacha\Marketplace\Currency\Exception\CurrencyNotFound;
use Doctrine\Common\Persistence\ManagerRegistry;
use Doctrine\ORM\QueryBuilder;
use Wizacha\Marketplace\Currency\QueryFilters\Factory;
use Wizacha\Marketplace\Currency\QueryFilters\QueryFilterInterface;

class CurrencyRepository extends ServiceEntityRepository
{
    /** @var Factory */
    private $filtersFactory;

    public function __construct(ManagerRegistry $registry, $entityClass, Factory $filtersFactory)
    {
        parent::__construct($registry, $entityClass);
        $this->filtersFactory = $filtersFactory;
    }

    public function save(Currency $currency): Currency
    {
        $this->getEntityManager()->persist($currency);
        $this->getEntityManager()->flush();

        return $currency;
    }

    public function getCurrencyForApi(string $code): array
    {
        $currency = $this
            ->createQueryBuilder('c')
            ->select('c.code')
            ->where('c.code = :code')
            ->setParameter('code', $code)
            ->getQuery()
            ->getOneOrNullResult();

        if (false === \is_array($currency)) {
            throw new CurrencyNotFound("Currency '$code' not found.");
        }

        return $currency;
    }

    public function get(string $code): Currency
    {
        $currency = $this
            ->createQueryBuilder('c')
            ->where('c.code = :code')
            ->setParameter('code', $code)
            ->getQuery()
            ->getOneOrNullResult();

        if (false === ($currency instanceof Currency)) {
            throw new CurrencyNotFound("Currency '$code' not found.");
        }

        return $currency;
    }

    public function assertExist(string $code): self
    {
        $currency = $this
            ->createQueryBuilder('c')
            ->select('COUNT(c.code)')
            ->where('c.code = :code')
            ->setParameter('code', $code)
            ->getQuery()
            ->getSingleScalarResult();

        if ($currency === 0) {
            throw new BadRequestHttpException("Currency '$code' must be ISO 4217.");
        }

        return $this;
    }

    public function list(array $filters = []): array
    {
        /** @var QueryBuilder $queryBuilder */
        $queryBuilder = $this
            ->createQueryBuilder('cr')
            ->leftJoin('cr.countryCodes', 'cc')
            ->orderBy('cr.code');

        $queryBuilder = $this->applyFilters($queryBuilder, $filters);

        return $queryBuilder
            ->getQuery()
            ->getResult();
    }

    protected function applyFilters(QueryBuilder $queryBuilder, array $filters): QueryBuilder
    {
        foreach ($filters as $name => $value) {
            $dqlFilter = $this->filtersFactory->get($name);
            if ($dqlFilter instanceof QueryFilterInterface === true) {
                $queryBuilder = $dqlFilter->apply($queryBuilder, $value);
            }
        }

        return $queryBuilder;
    }
}
