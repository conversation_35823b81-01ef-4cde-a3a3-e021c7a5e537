<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Currency;

use GuzzleHttp\Client;
use Wizacha\Marketplace\Currency\Exception\FixerRequestFailed;

/**
 * API client to use the FIXER IO API
 * @see https://fixer.io/documentation
 */
class FixerClientApi
{
    /** @var Client  */
    private $client;

    /** @var string  */
    private $currencyFixerApiKey;

    /** @var string  */
    private $currencyFixerBaseUrl;

    /** @var string  */
    private $currencyRatesProvider;

    public function __construct(
        string $currencyRatesProvider,
        string $currencyFixerApiKey,
        string $currencyFixerBaseUrl
    ) {
        $this->currencyRatesProvider = $currencyRatesProvider;
        $this->currencyFixerApiKey   = $currencyFixerApiKey;
        $this->currencyFixerBaseUrl  = $currencyFixerBaseUrl;
        $this->client = new Client();
    }

    public function assertApiKeyAndUrlValid(): void
    {
        if ($this->currencyRatesProvider !== "fixer") {
            throw new \InvalidArgumentException('Fixer provider is not enabled');
        }

        if ($this->currencyRatesProvider === 'fixer' && $this->currencyFixerApiKey === '') {
            throw new \InvalidArgumentException('CURRENCY_FIXER_API_KEY must not be empty');
        }

        if ($this->currencyRatesProvider === 'fixer' &&  $this->currencyFixerBaseUrl === '') {
            throw new \InvalidArgumentException('CURRENCY_FIXER_BASE_URL must not be empty');
        }
    }

    /** @return mixed[] */
    public function getLatestCurrencyRate(string $mainCode): array
    {
        $params = [
            'access_key' => $this->currencyFixerApiKey,
            'base' => $mainCode
        ];

        $response = $this->doRequest(
            'GET',
            $this->currencyFixerBaseUrl . '/latest',
            [
                'query' => $params
            ]
        );

        if ($response['success'] === true) {
            return $response;
        } else {
            $message = implode(" : ", $response['error']);
            throw new FixerRequestFailed($message, $response['error']['code']);
        }
    }

    /** @return mixed[] */
    private function doRequest(string $method, string $action, array $params): array
    {
        try {
            $request = $this->client->request($method, $action, $params);

            return \json_decode($request->getBody()->getContents(), true);
        } catch (\Exception $exception) {
            throw new FixerRequestFailed($exception->getMessage());
        }
    }
}
