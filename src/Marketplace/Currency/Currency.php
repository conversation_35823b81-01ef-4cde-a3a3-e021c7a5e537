<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Currency;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;

class Currency
{
    /**
     * @var string
     */
    private $code;

    /**
     * @var ?ExchangeRate
     */
    private $exchangeRate;

    /**
     * @var bool
     */
    private $enabled;

    /**
     * @var ?string
     */
    private $symbol;

    /**
     * @var Collection
     */
    private $countryCodes;

    /** @var \DateTime|null */
    private $updatedAt;

    public function __construct(
        string $code,
        string $symbol = null,
        ExchangeRate $exchangeRate = null,
        bool $enabled = false
    ) {
        $this->setCode($code);
        $this->setSymbol($symbol);
        $this->setExchangeRate($exchangeRate);
        $this->setEnabled($enabled);
        $this->countryCodes = new ArrayCollection();
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function getExchangeRate(): ?ExchangeRate
    {
        return $this->exchangeRate;
    }

    public function getExchangeRateFloatValue(): ?float
    {
        return $this->exchangeRate instanceof ExchangeRate ? $this->exchangeRate->getFloat() : null;
    }

    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    public function getSymbol(): ?string
    {
        return $this->symbol;
    }

    public function getCountryCodes(): Collection
    {
        return $this->countryCodes;
    }

    public function setSymbol(?string $symbol): self
    {
        $this->symbol = $symbol;

        return $this;
    }

    public function setExchangeRate(?ExchangeRate $exchangeRate): self
    {
        $this->exchangeRate = $exchangeRate;

        return $this;
    }

    public function setEnabled(bool $enabled): self
    {
        $this->enabled = $enabled;

        return $this;
    }

    public function setCountryCodes(iterable $countryCodes): self
    {
        $this->clearCountryCodes();
        /** @var CurrencyCountries $countryCode */
        foreach ($countryCodes as $countryCode) {
            $this->addCountryCode($countryCode);
        }

        return $this;
    }

    public function addCountryCode(CurrencyCountries $countryCode): self
    {
        if ($this->countryCodes->contains($countryCode) === false) {
            $this->countryCodes->add($countryCode);
            $countryCode->setCurrency($this);
        }

        return $this;
    }

    public function removeCountryCode(CurrencyCountries $countryCode): self
    {
        if ($this->countryCodes->contains($countryCode)) {
            $this->countryCodes->removeElement($countryCode);
            $countryCode->setCurrency(null);
        }

        return $this;
    }

    public function clearCountryCodes(): self
    {
        foreach ($this->getCountryCodes() as $countryCode) {
            $this->removeCountryCode($countryCode);
        }
        $this->countryCodes->clear();

        return $this;
    }

    public function expose(): array
    {
        return [
            "code" => $this->code,
            "symbol" => $this->getSymbol(),
            "enabled" => $this->isEnabled(),
            "exchangeRate" => $this->getExchangeRateFloatValue(),
            "updatedAt" => $this->getUpdatedAt() !== null ? $this->getUpdatedAt()->format(\DateTime::RFC3339) : null,
            "countries" => $this->exposeCountryCodes(),
        ];
    }

    /**
     * Call setters for fields in array $values
     */
    public function fromArray(array $values): self
    {
        foreach ($values as $key => $value) {
            $methodName = 'set' . $key;
            if (method_exists($this, $methodName)) {
                $this->$methodName($value);
            }
        }

        return $this;
    }

    protected function exposeCountryCodes(): array
    {
        $countryCodes = [];
        /** @var CurrencyCountries $countryCode */
        foreach ($this->getCountryCodes() as $countryCode) {
            $countryCodes[] = $countryCode->exposeCode();
        }

        return $countryCodes;
    }

    protected function setCode(string $code): self
    {
        if ($code === "") {
            throw new \Exception("Currency code must not be empty");
        }

        $this->code = mb_strtoupper($code);

        return $this;
    }

    /**
     * @param \DateTime|null $updatedAt
     * @return $this
     */
    public function setUpdatedAt(?\DateTime $updatedAt): self
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /** @return \DateTime|null */
    public function getUpdatedAt(): ?\DateTime
    {
        return $this->updatedAt;
    }
}
