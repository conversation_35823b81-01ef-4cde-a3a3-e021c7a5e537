<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Currency;

use Wizacha\Decimal\AbstractDecimal;

class ExchangeRate extends AbstractDecimal
{
    /** @var int */
    protected static $digitsPrecision = 6;

    /** @var int */
    protected $value;

    protected function getValue(): int
    {
        return $this->value;
    }

    protected function setValue(int $value): parent
    {
        $this->value = $value;

        return $this;
    }
}
