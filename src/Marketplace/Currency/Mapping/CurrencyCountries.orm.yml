Wizacha\Marketplace\Currency\CurrencyCountries:
    type: entity
    table: currencies_countries
    uniqueConstraints:
        unique_assoc:
            columns: [country_code, currency_code]
    id:
        id:
            type: integer
            column: id
            generator:
               strategy: AUTO
    fields:
        countryCode:
            type: string
            column: country_code
            nullable: false
    indexes:
        id:
            columns: [ id ]
        countryCode:
            columns: [ country_code]
        currencyCode:
            columns: [ currency_code]
    manyToOne:
        currency:
            targetEntity: Wizacha\Marketplace\Currency\Currency
            joinColumn:
                name: currency_code
                referencedColumnName: code
                nullable: false
            inversedBy: countryCodes
