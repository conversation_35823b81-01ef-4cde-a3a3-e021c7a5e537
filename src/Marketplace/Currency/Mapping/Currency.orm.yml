Wizacha\Marketplace\Currency\Currency:
    type: entity
    table: currencies
    id:
        code:
            type: string
            column: code
            generator:
                strategy: NONE
    fields:
        exchangeRate:
            type: exchange_rate
            column: exchange_rate
            nullable: true
        enabled:
            type: boolean
            column: enabled
            nullable: false
            options:
              default: 0
        symbol:
            type: string
            column: symbol
            nullable: true
        updatedAt:
            type: datetime
            column: updated_at
            nullable: true
    indexes:
        code:
            columns: [ code ]
    oneToMany:
        countryCodes:
            targetEntity: Wizacha\Marketplace\Currency\CurrencyCountries
            mappedBy: currency
            orphanRemoval: true
            cascade: [persist, remove]
