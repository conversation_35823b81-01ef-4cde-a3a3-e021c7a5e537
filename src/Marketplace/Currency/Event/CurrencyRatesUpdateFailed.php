<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Currency\Event;

use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Symfony\Contracts\EventDispatcher\Event;
use Wizacha\Component\Notification\NotificationEvent;

class CurrencyRatesUpdateFailed extends Event implements NotificationEvent
{
    /** @var string */
    private $messageToAdmin;

    public function __construct(string $messageToAdmin)
    {
        $this->messageToAdmin = $messageToAdmin;
    }

    public function getMessageToAdmin(): string
    {
        return $this->messageToAdmin;
    }

    public static function getDescription(): string
    {
        return 'currency-rates-update-failed';
    }

    public static function buildDebugForm(FormBuilder $form)
    {
        $form->add('messageToAdmin', TextType::class);
    }

    public static function createFromForm(Form $form)
    {
        return new static($form->getData()['messageToAdmin']);
    }
}
