<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Currency\QueryFilters;

use Doctrine\ORM\QueryBuilder;

class CountryCode implements QueryFilterInterface
{
    /** @var string */
    private $name = "countryCode";

    /** @var string */
    private $entity = "cc";

    /** @var string */
    private $field = "countryCode";

    public function apply(QueryBuilder $queryBuilder, string $value): QueryBuilder
    {
        return $queryBuilder
            ->andWhere("$this->entity.$this->field = :$this->name")
            ->setParameter(":$this->name", $value);
    }

    public function setEntity(string $entity): self
    {
        $this->entity = $entity;

        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }
}
