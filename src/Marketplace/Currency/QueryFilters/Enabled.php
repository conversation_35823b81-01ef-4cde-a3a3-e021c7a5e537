<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Currency\QueryFilters;

use Doctrine\ORM\QueryBuilder;

class Enabled implements QueryFilterInterface
{
    /** @var string */
    private $name = "enabled";

    /** @var string */
    private $entity = "cr";

    /** @var string */
    private $field = "enabled";

    public function apply(QueryBuilder $queryBuilder, string $value): QueryBuilder
    {
        $this->assertValueIsValid($value);

        return $queryBuilder
            ->andWhere("$this->entity.$this->field = :$this->name")
            ->setParameter(":$this->name", $value === 'true' ? 1 : 0);
    }

    public function assertValueIsValid($value): self
    {
        if (false === \in_array($value, ['true', 'false'])) {
            throw new \InvalidArgumentException("Invalid value '$value' for enabled filter. Expected 'true' or 'false'");
        }

        return $this;
    }

    public function setEntity(string $entity): self
    {
        $this->entity = $entity;

        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }
}
