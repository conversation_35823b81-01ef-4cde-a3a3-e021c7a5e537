<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Currency\QueryFilters;

class Factory
{
    /** @var iterable|QueryFilterInterface[] */
    private $queryFilters;

    public function __construct(iterable $queryFilters)
    {
        $this->queryFilters = $queryFilters;
    }

    public function get(string $name): ?QueryFilterInterface
    {
        foreach ($this->queryFilters as $queryFilter) {
            if ($queryFilter->getName() === $name) {
                return $queryFilter;
            }
        }

        return null;
    }
}
