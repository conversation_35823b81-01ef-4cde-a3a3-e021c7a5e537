<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Currency;

use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class CurrencyService
{
    /** @var CurrencyRepository */
    private $currencyRepository;

    /** @var CurrencyCountriesRepository */
    private $countriesRepository;

    /** @var string */
    private $regexpCountryCode;

    /** @var bool */
    private $featureCurrencyAdvanced;

    /** @var string */
    private $defaultCurrencyCode;

    /** @var string */
    private $defaultCurrencySymbol;

    public function __construct(
        CurrencyRepository $currencyRepository,
        CurrencyCountriesRepository $countriesRepository,
        string $defaultCurrencyCode,
        string $regexpCountryCode,
        bool $featureCurrencyAdvanced,
        string $defaultCurrencySymbol
    ) {
        $this->currencyRepository = $currencyRepository;
        $this->countriesRepository = $countriesRepository;
        $this->regexpCountryCode = $regexpCountryCode;
        $this->defaultCurrencyCode = $defaultCurrencyCode;
        $this->featureCurrencyAdvanced = $featureCurrencyAdvanced;
        $this->defaultCurrencySymbol = $defaultCurrencySymbol;
    }

    /** @return Currency[] */
    public function list(array $filters = []): array
    {
        return $this->currencyRepository->list($filters);
    }

    public function update(string $currencyCode, array $data): Currency
    {
        return $this
            ->currencyRepository
            ->save(
                $this
                    ->currencyRepository
                    ->get($currencyCode)
                    ->fromArray($data)
            );
    }

    public function save(Currency $currency): Currency
    {
        return $this
            ->currencyRepository
            ->save($currency);
    }

    public function getCurrencyCode(string $currencyCode): array
    {
        try {
            return $this->currencyRepository->getCurrencyForApi($currencyCode);
        } catch (\Throwable $e) {
            throw new NotFoundHttpException('Currency "' . $currencyCode . '" not found.');
        }
    }

    public function getCountryCodes(string $currencyCode): array
    {
        return $this->countriesRepository->getCountryCodes($currencyCode);
    }

    public function deleteCountryCode(string $currencyCode, string $countryCode): void
    {
        try {
            $currency = $this->currencyRepository->get($currencyCode);
        } catch (\Throwable $e) {
            throw new NotFoundHttpException('CurrencyCode "' . $currencyCode . '" not found.');
        }

        try {
            $currencyCountry = $this->countriesRepository->get($countryCode);
        } catch (\Throwable $e) {
            throw new NotFoundHttpException('CountryCode "' . $countryCode . '" not found.');
        }

        $currency->removeCountryCode($currencyCountry);
        $this->countriesRepository->save($currencyCountry);
    }

    public function addCountryCode(string $currencyCode, string $countryCode): CurrencyCountries
    {
        $currency = $this->currencyRepository->get($currencyCode);

        if (\in_array(preg_match("/{$this->regexpCountryCode}/", $countryCode), [0, false])) {
            throw new BadRequestHttpException("Country code '$countryCode' must be ISO 3166-1 alpha2.");
        }

        $currencyCountry = new CurrencyCountries($countryCode, $currency);

        try {
            $this->countriesRepository->save($currencyCountry);
        } catch (\Throwable $e) {
            throw new BadRequestHttpException('CountryCode "' . $countryCode . '" already exist.');
        }

        return $currencyCountry;
    }

    public function getCurrencyRepository(): CurrencyRepository
    {
        return $this->currencyRepository;
    }

    public function getCountryRepository(): CurrencyCountriesRepository
    {
        return $this->countriesRepository;
    }

    public function assertCurrencyExist(string $code): self
    {
        $this->currencyRepository->assertExist($code);

        return $this;
    }

    public function isFeatureCurrencyAdvancedActivated(): bool
    {
        return $this->featureCurrencyAdvanced;
    }

    public function getDefaultCurrencyCode(): string
    {
        return $this->defaultCurrencyCode;
    }

    public function getDefaultCurrency(): ?Currency
    {
        $currency  = new Currency(
            $this->defaultCurrencyCode,
            $this->defaultCurrencySymbol,
            ExchangeRate::fromVariable(1),
            true
        );
        $currency->setUpdatedAt((new \DateTime())->setTime(0, 0));

        return $currency;
    }

    public function getCurrency(string $currencyCode): Currency
    {
        return $this->currencyRepository->get($currencyCode);
    }
}
