<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Currency;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;

class CurrencyCountriesRepository extends ServiceEntityRepository
{
    public function save(CurrencyCountries $currencyCountries)
    {
        $this->getEntityManager()->persist($currencyCountries);
        $this->getEntityManager()->flush();

        return $currencyCountries;
    }

    public function getCountryCodes(string $currencyCode): array
    {
        $countries = $this->createQueryBuilder('cc')
            ->where('cc.currency = :currencyCode')
            ->setParameter('currencyCode', $currencyCode)
            ->orderBy('cc.countryCode', 'ASC')
            ->getQuery()
            ->getResult();

        return $countries;
    }

    public function get(string $countryCode): CurrencyCountries
    {
        $currency = $this
            ->createQueryBuilder('cc')
            ->where('cc.countryCode = :countryCode')
            ->setParameter('countryCode', $countryCode)
            ->getQuery()
            ->getOneOrNullResult();

        if (false === $currency instanceof CurrencyCountries) {
            throw new \Exception('Country code "' . $countryCode . '" not found.');
        }

        return $currency;
    }
}
