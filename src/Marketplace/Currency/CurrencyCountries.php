<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Currency;

class CurrencyCountries
{
    /**
     * @var int|null
     */
    private $id;

    /**
     * @var string
     */
    private $countryCode;

    /**
     * @var Currency
     */
    private $currency;

    public function __construct(string $countryCode, Currency $currency)
    {
        $this->setCountryCode($countryCode);
        $this->setCurrency($currency);
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCountryCode(): string
    {
        return $this->countryCode;
    }

    public function getCurrencyCode(): string
    {
        return $this->currency->getCode();
    }

    public function getCurrency(): Currency
    {
        return $this->currency;
    }

    public function setCountryCode(string $countryCode): self
    {
        if ($countryCode === '') {
            throw new \Exception("Country code must not be empty");
        }

        $this->countryCode = mb_strtoupper($countryCode);

        return $this;
    }

    public function setCurrency(Currency $currency = null): self
    {
        $this->currency = $currency;
        if ($currency instanceof Currency) {
            $currency->addCountryCode($this);
        }

        return $this;
    }

    public function exposeCode(): array
    {
        return [
            "code" => $this->countryCode,
        ];
    }
}
