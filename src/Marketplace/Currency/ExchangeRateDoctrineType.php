<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Currency;

use Wizacha\Decimal\AbstractDecimalDoctrineType;

class ExchangeRateDoctrineType extends AbstractDecimalDoctrineType
{
    public const NAME = 'exchange_rate';

    public function getName(): string
    {
        return static::NAME;
    }

    public function getPhpInstance(int $value): ExchangeRate
    {
        return new ExchangeRate($value);
    }
}
