<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace;

use Wizacha\Marketplace\ReadModel\ReadModelObjectId;

/**
 * Données SEO (titre d'une page HTML, meta description, etc.).
 */
class SeoData implements \JsonSerializable
{
    /**
     * @var string
     */
    private $title;

    /**
     * @var string
     */
    private $description;

    /**
     * @var string
     */
    private $keywords;

    /**
     * @var string
     */
    private $slug;

    public function __construct(string $title = '', string $description = '', string $keywords = '', ?string $slug = '')
    {
        $this->title = $title;
        $this->description = $description;
        $this->keywords = $keywords;
        $this->slug = $slug;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function getKeywords(): string
    {
        return $this->keywords;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    /**
     * @return array[]
     */
    public function jsonSerialize(): array
    {
        return [
            ReadModelObjectId::SEO_DATA()->getValue() => [
                $this->title,
                $this->description,
                $this->keywords,
                $this->slug,
            ],
        ];
    }
}
