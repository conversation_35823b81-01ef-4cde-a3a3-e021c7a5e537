<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Quotation\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\DBAL\Connection;
use Doctrine\ORM\QueryBuilder;
use Wizacha\Marketplace\Quotation\Entity\QuoteRequestSelection;

class QuoteRequestSelectionRepository extends ServiceEntityRepository
{
    /** @return QuoteRequestSelection[] */
    public function getActivesFilterByRole(int $userId, int $selectionId = null, bool $isAdmin = false): array
    {
        $query = $this->getEntityManager()->createQueryBuilder();
        $query->select('q')
            ->from(QuoteRequestSelection::class, 'q')
            ->where('q.active = :active')
            ->setParameter('active', true);

        if (null !== $selectionId) {
            $query->andWhere('q.id = :id')
                ->setParameter('id', $selectionId);
        }
        $this->filterByUser($query, $userId, $isAdmin);

        return $query->orderBy('q.id', 'DESC')->getQuery()->execute();
    }

    /**
     * @param mixed[]|null $params
     * @return QuoteRequestSelection[]
     */
    public function filter(int $userId, int $limit, int $offset, bool $isAdmin = false, ?array $params = null): array
    {
        $query = $this->getEntityManager()->createQueryBuilder();

        $query->select('q')
            ->from(QuoteRequestSelection::class, 'q')
            ->setFirstResult($offset)
            ->setMaxResults($limit);

        $this->filterByUser($query, $userId, $isAdmin);
        $this->queryFilter($query, $params);

        return $query->orderBy('q.id', 'DESC')->getQuery()->execute();
    }

    /** @param mixed[]|null $params */
    public function countFilter(int $userId, bool $isAdmin = false, array $params = null): string
    {
        $query = $this->getEntityManager()->createQueryBuilder();

        $query->select('count(q.id)')
            ->from(QuoteRequestSelection::class, 'q');

        $this->filterByUser($query, $userId, $isAdmin);
        $this->queryFilter($query, $params);

        return $query->orderBy('q.id', 'DESC')->getQuery()->getSingleScalarResult();
    }

    public function save(QuoteRequestSelection $quoteRequestSelection): QuoteRequestSelection
    {
        $this->getEntityManager()->persist($quoteRequestSelection);
        $this->getEntityManager()->flush();

        return $quoteRequestSelection;
    }

    public function getUserSelectionIds(int $userId): string
    {
        $connection = $this->getEntityManager()->getConnection();
        return $connection->executeQuery(
            "SELECT GROUP_CONCAT(quote_request_selection_id) ids
                   FROM doctrine_quote_request_selection
                  WHERE user_id = :user_id",
            ['user_id' => $userId]
        )->fetchAll()[0]['ids'] ?? '';
    }

    private function filterByUser(QueryBuilder $query, int $userId, bool $isAdmin): void
    {
        if (false === $isAdmin) {
            $query->andWhere('q.userId = :userId')
                ->setParameter('userId', $userId);
        }
    }

    /** @param mixed[]|null $params */
    private function queryFilter(QueryBuilder $query, ?array $params): void
    {
        if (true === \is_array($params['ids'])) {
            $query->andWhere('q.id IN (:ids)')
                ->setParameter('ids', $params['ids']);
        }

        if (true === \is_array($params['userIds'])) {
            $query->andWhere('q.userId IN (:userIds)')
                ->setParameter('userIds', $params['userIds']);
        }

        if (null !== $params['active']) {
            $query->andWhere('q.active = (:active)')
                ->setParameter('active', $params['active']);
        }

        if (true === \is_array($params['creationPeriod'])) {
            if (true === \array_key_exists('from', $params['creationPeriod'])) {
                $query->andWhere('q.createdAt >= (:creationPeriodFrom)')
                    ->setParameter('creationPeriodFrom', $params['creationPeriod']['from']);
            }
            if (true === \array_key_exists('to', $params['creationPeriod'])) {
                $query->andWhere('q.createdAt <= (:creationPeriodTo)')
                    ->setParameter('creationPeriodTo', $params['creationPeriod']['to']);
            }
        }

        if (true === \is_array($params['updatedPeriod'])) {
            if (true === \array_key_exists('from', $params['updatedPeriod'])) {
                $query->andWhere('q.updatedAt >= (:updatedPeriodFrom)')
                    ->setParameter('updatedPeriodFrom', $params['updatedPeriod']['from']);
            }
            if (true === \array_key_exists('to', $params['updatedPeriod'])) {
                $query->andWhere('q.updatedAt <= (:updatedPeriodTo)')
                    ->setParameter('updatedPeriodTo', $params['updatedPeriod']['to']);
            }
        }

        if (true === \is_array($params['declinationIds'])) {
            // Filter selections containing the right declinations
            $declinationSubQuery = $this->getEntityManager()->getConnection()->executeQuery(
                'SELECT q.quote_request_selection_id
                       FROM doctrine_quote_request_selection q
                       JOIN doctrine_quote_request_selection_declination qr
                         ON q.quote_request_selection_id = qr.quote_request_selection_id
                      WHERE declination_id IN (:declinationIds)',
                ['declinationIds' => $params['declinationIds']],
                ['declinationIds' => Connection::PARAM_STR_ARRAY]
            )->fetchAll();
            $ids = \array_map(fn($selection) => $selection['quote_request_selection_id'], $declinationSubQuery);

            $query->andWhere('q.id IN (:ids)')
                ->setParameter('ids', $ids);
        }
    }
}
