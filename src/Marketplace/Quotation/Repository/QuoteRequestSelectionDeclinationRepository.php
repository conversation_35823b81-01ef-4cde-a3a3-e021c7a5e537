<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Quotation\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Wizacha\Marketplace\Quotation\Entity\QuoteRequestSelectionDeclination;

class QuoteRequestSelectionDeclinationRepository extends ServiceEntityRepository
{
    public function save(QuoteRequestSelectionDeclination $selectionDeclination): void
    {
        $this->getEntityManager()->persist($selectionDeclination);
        $this->getEntityManager()->flush();
    }

    public function delete(int $quoteRequestSelectionId, string $declinationId): void
    {
        $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->delete(QuoteRequestSelectionDeclination::class, 'q')
            ->where('q.declinationId = :declination_id')
            ->andWhere('q.quoteRequestSelection = :quote_request_selection_id')
            ->setParameters([
                "declination_id" => $declinationId,
                "quote_request_selection_id" => $quoteRequestSelectionId
            ])
            ->getQuery()
            ->execute();
    }


    public function removeDeclinationFromSelections(string $productId): void
    {
        $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->delete(QuoteRequestSelectionDeclination::class, 'q')
            ->where("q.declinationId LIKE :declinationPrefix")
            ->setParameters(["declinationPrefix" => $productId . '_%'])
            ->getQuery()
            ->execute();
    }
}
