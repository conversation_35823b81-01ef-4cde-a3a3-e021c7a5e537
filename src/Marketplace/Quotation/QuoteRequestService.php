<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Quotation;

use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Quotation\Entity\QuoteRequestSelection;
use Wizacha\Marketplace\Quotation\Entity\QuoteRequestSelectionDeclination;
use Wizacha\Marketplace\Quotation\Repository\QuoteRequestSelectionDeclinationRepository;
use Wizacha\Marketplace\Quotation\Repository\QuoteRequestSelectionRepository;
use Wizacha\Marketplace\ReadModel\ProductRepository;
use Wizacha\Product;

class QuoteRequestService
{
    private QuoteRequestSelectionRepository $selectionRepository;
    private QuoteRequestSelectionDeclinationRepository $selectionDeclinationRepository;
    private ProductRepository $productRepository;

    public function __construct(
        QuoteRequestSelectionRepository $selectionRepository,
        QuoteRequestSelectionDeclinationRepository $selectionDeclinationRepository,
        ProductRepository $productRepository
    ) {
        $this->selectionRepository = $selectionRepository;
        $this->selectionDeclinationRepository = $selectionDeclinationRepository;
        $this->productRepository = $productRepository;
    }

    /** @param string[] $role */
    public function get(int $selectionId, int $userId, array $role): ?QuoteRequestSelection
    {
        $selections = $this->selectionRepository
            ->getActivesFilterByRole($userId, $selectionId, \in_array('ROLE_ADMIN', $role));

        return \count($selections) > 0
            ? $selections[0]
            : null;
    }

    public function updateDate(QuoteRequestSelection $quoteRequestSelection): void
    {
        $quoteRequestSelection->setUpdatedAt(new \DateTime());
        $this->selectionRepository->save($quoteRequestSelection);
    }

    /**
     * Create a new active selection if the user don't have any
     * @param string[] $role
     */
    public function getOrCreateActiveSelection(int $userId, array $role): ?int
    {
        // Admin need to specify the selection id
        if (true === \in_array('ROLE_ADMIN', $role)) {
            return null;
        }

        $quotesSelections = $this->selectionRepository
            ->getActivesFilterByRole($userId, null, \in_array('ROLE_ADMIN', $role));

        // Do not create new active selections for an admin
        if (\count($quotesSelections) === 0) {
            $quoteRequestSelection = new QuoteRequestSelection();
            $quoteRequestSelection
                ->setUserId($userId)
                ->setActive(true);
            $quoteRequestSelection = $this->selectionRepository->save($quoteRequestSelection);

            return $quoteRequestSelection->getId();
        }

        return \count($quotesSelections) === 0
            ? null
            : $quotesSelections[0]->getId();
    }

    /**
     * @param mixed[] $params
     * @param string[] $role
     * @return QuoteRequestSelection[]
     */
    public function filter(int $userId, array $params, array $role): array
    {
        return $this->selectionRepository->filter(
            $userId,
            $params['limit'] ?? 10,
            $params['offset'] ?? 0,
            \in_array('ROLE_ADMIN', $role),
            $params
        );
    }

    /**
     * @param mixed[] $params
     * @param string[] $role
     */
    public function count(int $userId, array $params, array $role): string
    {
        return $this->selectionRepository->countFilter($userId, \in_array('ROLE_ADMIN', $role), $params);
    }

    /** @return mixed[] */
    public function addDeclination(
        QuoteRequestSelection $quoteRequestSelection,
        string $declinationId,
        int $quantity
    ): array {
        $declination = $this->selectionDeclinationRepository->findOneBy(
            [
                'quoteRequestSelection' => $quoteRequestSelection->getId(),
                'declinationId' => $declinationId
            ]
        );
        if (null === $declination) {
            $declination = new QuoteRequestSelectionDeclination($quoteRequestSelection, $declinationId);
            $declination->setQuantity($quantity);
        } else {
            $declination->setQuantity($declination->getQuantity() + $quantity);
        }
        $this->selectionDeclinationRepository->save($declination);

        return [
            'declinationId' => $declinationId,
            'quantity' => $declination->getQuantity(),
            'added' => $quantity
        ];
    }

    /** @return mixed[] */
    public function updateDeclination(
        QuoteRequestSelection $quoteRequestSelection,
        string $declinationId,
        int $quantity
    ): array {
        $selectionDeclination = $this->selectionDeclinationRepository->findOneBy([
            'declinationId' => $declinationId,
            'quoteRequestSelection' => $quoteRequestSelection->getId()
        ]);
        $selectionDeclination->setQuantity($quantity);

        $this->selectionDeclinationRepository->save($selectionDeclination);
        return [
            'declinationId' => $declinationId,
            'quantity' => $selectionDeclination->getQuantity()
        ];
    }

    public function removeDeclination(
        QuoteRequestSelection $quoteRequestSelection,
        string $declinationId
    ): void {
        $this->selectionDeclinationRepository->delete($quoteRequestSelection->getId(), $declinationId);
    }

    /** @return int[] */
    public function getUserSelectionIds(int $userId): array
    {
        $selectionsIds = $this->selectionRepository->getUserSelectionIds($userId);
        return '' === $selectionsIds
            ? []
            : \array_map('intval', \explode(',', $selectionsIds));
    }

    public function checkIfDeclinationCanBeAdded(string $declinationId): void
    {
        $declinationArray = explode('_', $declinationId);

        $product = new Product($declinationArray[0]);
        $readModel = $this->productRepository->find($declinationArray[0]);

        if (null === $readModel) {
            throw new NotFoundHttpException('Product ' . $declinationArray[0] . ' not found');
        }

        try {
            $readModel->getDeclination($declinationId);
        } catch (NotFound $exception) {
            throw new NotFoundHttpException('Declination ' . $declinationId . ' not found');
        }

        if ($product->getQuoteRequestMinQuantity() === 0) {
            throw new BadRequestHttpException('Declination not available for quotes');
        }
    }

    public function isDeclinationInSelection(int $selectionId, string $declinationId): bool
    {
        $declination = $this->selectionDeclinationRepository->findOneBy(
            [
                'quoteRequestSelection' => $selectionId,
                'declinationId' => $declinationId
            ]
        );

        return $declination !== null;
    }

    public function removeDeclinationFromSelections(string $productId): void
    {
        $this->selectionDeclinationRepository->removeDeclinationFromSelections($productId);
    }
}
