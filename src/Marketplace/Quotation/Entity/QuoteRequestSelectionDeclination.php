<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Quotation\Entity;

use Doctrine\ORM\Mapping as ORM;

/**
 * A selection of products for creating a quote
 *
 * @ORM\Table(name="quote_request_selection_declination")
 * @ORM\Entity(repositoryClass="Wizacha\Marketplace\Quotation\Repository\QuoteRequestSelectionRepository")
 */
class QuoteRequestSelectionDeclination
{
    /**
     * @ORM\Id
     * @ORM\ManyToOne(targetEntity="QuoteRequestSelection", inversedBy="selectionDeclinations")
     * @ORM\JoinColumn(name="quote_request_selection_id", referencedColumnName="quote_request_selection_id")
     */
    protected QuoteRequestSelection $quoteRequestSelection;

    /**
     * @ORM\Id
     * @ORM\Column(name="declination_id", type="string")
     * @ORM\GeneratedValue(strategy="NONE")
     */
    private string $declinationId;

    /** @ORM\Column(name="quantity", type="integer") */
    private int $quantity;

    public function __construct(QuoteRequestSelection $quoteRequestSelection, string $declinationId)
    {
        $this->quoteRequestSelection = $quoteRequestSelection;
        $this->declinationId = $declinationId;
    }

    public function getQuoteRequestSelection(): QuoteRequestSelection
    {
        return $this->quoteRequestSelection;
    }

    public function getDeclinationId(): string
    {
        return $this->declinationId;
    }

    public function getQuantity(): int
    {
        return $this->quantity;
    }

    public function setQuantity(int $quantity): self
    {
        $this->quantity = $quantity;

        return $this;
    }
}
