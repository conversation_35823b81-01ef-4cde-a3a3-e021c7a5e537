<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Quotation\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Table(name="quote_request_selection")
 * @ORM\Entity(repositoryClass="Wizacha\Marketplace\Quotation\Repository\QuoteRequestSelectionRepository")
 */
class QuoteRequestSelection
{
    /**
     * @ORM\Id
     * @ORM\Column(name="quote_request_selection_id", type="integer")
     * @ORM\GeneratedValue(strategy="AUTO")
     */
    private int $id;

    /** @ORM\Column(name="user_id", type="integer") */
    private int $userId;

    /** @ORM\Column(name="active", type="boolean") */
    private bool $active;

    /** @ORM\Column(name="quote_request_ids", type="simple_array", nullable=true) */
    private ?array $quoteRequestIds;

    /** @ORM\Column(name="created_at", type="datetime") */
    private \DateTime $createdAt;

    /** @ORM\Column(name="updated_at", type="datetime", nullable=true) */
    private ?\DateTime $updatedAt;

    /**
     * @var Collection<QuoteRequestSelectionDeclination>
     * @ORM\OneToMany(
     *     targetEntity="QuoteRequestSelectionDeclination",
     *     mappedBy="quoteRequestSelection",
     *     cascade={"persist", "remove"}
     * )
     * @ORM\JoinColumn(onDelete="CASCADE")
     */
    private Collection $selectionDeclinations;

    public function __construct()
    {
        $this->selectionDeclinations = new ArrayCollection();
        $this->quoteRequestIds = [];
        $this->createdAt = new \DateTime('NOW');
        $this->updatedAt = null;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getUserId(): int
    {
        return $this->userId;
    }

    public function setUserId(int $userId): self
    {
        $this->userId = $userId;

        return $this;
    }

    public function isActive(): bool
    {
        return $this->active;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;

        return $this;
    }

    /** @return int[] */
    public function getQuoteRequestIds(): array
    {
        return $this->quoteRequestIds;
    }

    public function getCreatedAt(): \DateTime
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): ?\DateTime
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTime $updatedAt): self
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    /** @return mixed[]  */
    public function expose(): array
    {
        return [
            'id' => $this->getId(),
            'userId' => $this->getUserId(),
            'active' => $this->isActive(),
            'declinations' => \array_map(
                fn($declination) => [
                    "declinationId" => $declination->getDeclinationId(),
                    "quantity" => $declination->getQuantity(),
                ],
                $this->selectionDeclinations->toArray()
            ),
            'quoteRequestsIds' => $this->getQuoteRequestIds(),
            'createdAt' => $this->getCreatedAt()->getTimestamp(),
            'updatedAt' => $this->getUpdatedAt() === null ? '' : $this->getUpdatedAt()->getTimestamp()
        ];
    }
}
