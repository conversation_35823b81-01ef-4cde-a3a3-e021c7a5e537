<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Metrics;

use Doctrine\DBAL\Connection;

class SandboxMetrics
{
    private Connection $connection;
    private bool $limitDatabase;
    private int $databaseLimit;

    public function __construct(Connection $connection, bool $limitDatabase, int $databaseLimit)
    {
        $this->connection = $connection;
        $this->limitDatabase = $limitDatabase;
        $this->databaseLimit = $databaseLimit;
    }

    public function getDatabaseSize(): string
    {
        return $this->connection->executeQuery("
            SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) 'db_size'
            FROM information_schema.tables;")->fetch()['db_size'];
    }

    public function isSandboxFull(): bool
    {
        if (false === $this->limitDatabase) {
            return false;
        }

        return (float) $this->getDatabaseSize() >= $this->databaseLimit;
    }

    public function getDatabaseLimit(): int
    {
        return $this->databaseLimit;
    }
}
