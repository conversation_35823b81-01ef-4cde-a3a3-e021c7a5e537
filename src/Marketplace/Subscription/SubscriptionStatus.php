<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Subscription;

use MyCLabs\Enum\Enum;

/**
 * @method static SubscriptionStatus ACTIVE()
 * @method static SubscriptionStatus DEFAULTED()
 * @method static SubscriptionStatus DISABLED()
 * @method static SubscriptionStatus FINISHED()
 * @method static SubscriptionStatus SUSPENDED()
 * @method static SubscriptionStatus WAITING_RENEW()
 */
class SubscriptionStatus extends Enum
{
    protected const ACTIVE = "ACTIVE";
    protected const DEFAULTED = "DEFAULTED";
    protected const DISABLED = "DISABLED";
    protected const FINISHED = "FINISHED";
    protected const SUSPENDED = "SUSPENDED";
    protected const WAITING_RENEW = "WAITING_RENEW";
}
