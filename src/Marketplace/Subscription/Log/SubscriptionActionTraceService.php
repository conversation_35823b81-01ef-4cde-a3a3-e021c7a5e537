<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Subscription\Log;

use Wizacha\Marketplace\Subscription\Subscription;
use Wizacha\Marketplace\User\User;

class SubscriptionActionTraceService
{
    protected SubscriptionActionTraceRepository $repository;

    public function __construct(
        SubscriptionActionTraceRepository $repository
    ) {
        $this->repository = $repository;
    }

    public function create(
        Subscription $subscription,
        ?string $oldValue,
        ?string $newValue,
        string $action,
        ?User $user
    ): SubscriptionActionTrace {
            $subscriptionActionTrace = (new SubscriptionActionTrace())
                ->setUser($user)
                ->setSubscription($subscription)
                ->setAction($action)
                ->setValueBefore($oldValue)
                ->setValueAfter($newValue)
                ->setCreatedAt(new \DateTimeImmutable())
            ;

            return $this->repository->save($subscriptionActionTrace);
    }
}
