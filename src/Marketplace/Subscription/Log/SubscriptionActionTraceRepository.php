<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Subscription\Log;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;

class SubscriptionActionTraceRepository extends ServiceEntityRepository
{
    public function save(SubscriptionActionTrace $subscriptionActionTrace): SubscriptionActionTrace
    {
        $this->getEntityManager()->persist($subscriptionActionTrace);

        // Doctrine ne gérant pas correctement les cascades sur un flush d'entité ManyToMany,
        // on est obligé de faire un flush global de toutes les entités persistés
        $this->getEntityManager()->flush();

        return $subscriptionActionTrace;
    }

    public function findOneById(string $id): ?SubscriptionActionTrace
    {
        return $this
            ->createQueryBuilder('subscriptionLog')
            ->where('subscriptionLog.id = :id')
            ->setParameter('id', $id)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }

    /** @return array SubscriptionActionTrace[] */
    public function getLogsFromSubscriptionId(
        string $subscriptionId,
        int $limit = null,
        int $offset = null
    ): array {
        $builder = $this->getEntityManager()->createQueryBuilder();

        return $builder
            ->select('subscriptionLog')
            ->from(SubscriptionActionTrace::class, 'subscriptionLog')
            ->where('subscriptionLog.subscription = :subscriptionId')
            ->setParameter('subscriptionId', $subscriptionId)
            ->setMaxResults($limit)
            ->setFirstResult($offset)
            ->orderBy('subscriptionLog.id', 'DESC')
            ->getQuery()
            ->getResult()
        ;
    }

    public function countLogsFromSubscriptionId(string $subscriptionId): int
    {
        $builder = $this->getEntityManager()->createQueryBuilder();

        return \intval(
            $builder
            ->select('COUNT(subscriptionLog.subscription)')
            ->from(SubscriptionActionTrace::class, 'subscriptionLog')
            ->where('subscriptionLog.subscription = :subscriptionId')
            ->setParameter('subscriptionId', $subscriptionId)
            ->getQuery()
            ->getSingleScalarResult()
        );
    }
}
