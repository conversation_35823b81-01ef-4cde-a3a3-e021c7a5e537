Wizacha\Marketplace\Subscription\Log\SubscriptionActionTrace:
    type: entity
    table: subscription_actions_trace
    id:
        id:
            type: integer
            length: 36
            options:
                unsigned: true
            generator:
                strategy: AUTO
    fields:
        action:
            type: string
            length: 255
            nullable: false
        date:
            type: datetimetz_immutable
            nullable: false
        valueBefore:
            type: string
            length: 255
            nullable: true
        valueAfter:
            type: string
            length: 255
            nullable: true
    manyToOne:
        subscription:
            targetEntity: Wizacha\Marketplace\Subscription\Subscription
            inversedBy: subscriptionActionTrace
        user:
            targetEntity: Wizacha\Marketplace\User\User
            inversedBy: subscriptions
            joinColumn:
                referencedColumnName: user_id
                nullable: false
            cascade: [persist]
