<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Subscription\Log;

use DateTimeImmutable;
use Wizacha\Marketplace\Subscription\Subscription;
use Wizacha\Marketplace\Traits\HasUuid;
use Wizacha\Marketplace\User\User;

class SubscriptionActionTrace implements \JsonSerializable
{
    use HasUuid;

    protected ?string $id;
    protected Subscription $subscription;
    protected string $action;
    protected DateTimeImmutable $date;
    protected ?User $user;
    protected ?string $valueBefore;
    protected ?string $valueAfter;

    public function setCreatedAt(\DateTimeImmutable $date): self
    {
        $this->date = $date;

        return $this;
    }

    public function jsonSerialize(): array
    {
        return [
            'id' => $this->getId(),
            'subscription_id' => $this->getSubscription()->getId(),
            'action' => $this->getAction(),
            'date' => $this->getDate()->format(\DateTime::RFC3339),
            'user' =>  ($this->getUser() instanceof User === true) ? $this->getUser()->getFullName() : 'Auto',
            'userId' =>  ($this->getUser() instanceof User === true) ? $this->getUser()->getUserId() : 0,
            'valueBefore' => $this->getValueBefore(),
            'valueAfter' => $this->getValueAfter(),
        ];
    }

    public function getSubscription(): Subscription
    {
        return $this->subscription;
    }

    public function setSubscription(Subscription $subscriptions): self
    {
        $this->subscription = $subscriptions;

        return $this;
    }

    public function getAction(): string
    {
        return $this->action;
    }

    public function setAction(string $action): self
    {
        $this->action = $action;

        return $this;
    }

    public function getDate(): DateTimeImmutable
    {
        return $this->date;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        return $this;
    }

    public function getValueBefore(): ?string
    {
        return $this->valueBefore;
    }

    public function setValueBefore(?string $valueBefore): self
    {
        $this->valueBefore = $valueBefore;

        return $this;
    }

    public function getValueAfter(): ?string
    {
        return $this->valueAfter;
    }

    public function setValueAfter(?string $valueAfter): self
    {
        $this->valueAfter = $valueAfter;

        return $this;
    }
}
