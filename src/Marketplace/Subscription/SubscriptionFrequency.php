<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Subscription;

use MyCLabs\Enum\Enum;

/**
 * @method static SubscriptionStatus MONTHLY()
 * @method static SubscriptionStatus ANNUAL()
 * @method static SubscriptionStatus QUARTERLY()
 * @method static SubscriptionStatus BI_ANNUAL()
 */
class SubscriptionFrequency extends Enum
{
    protected const MONTHLY = 1;
    protected const ANNUAL = 12;
    protected const QUARTERLY = 3;
    protected const BI_ANNUAL = 6;
}
