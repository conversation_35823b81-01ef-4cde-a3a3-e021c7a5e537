<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Subscription\Exception;

use Wizacha\Marketplace\Subscription\Subscription;

class AttemptLimitReachedException extends SubscriptionException
{
    public function __construct(Subscription $subscription, int $limit)
    {
        parent::__construct(sprintf(
            "Subscription [%s] can't be renew again, because limit is [%s] and current counter is [%s].",
            $subscription->getId(),
            $limit,
            $subscription->getRenewAttemptsCount(),
        ));
    }
}
