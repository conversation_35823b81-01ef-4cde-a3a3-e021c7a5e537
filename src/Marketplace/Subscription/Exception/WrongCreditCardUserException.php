<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Subscription\Exception;

use Wizacha\Marketplace\CreditCard\CreditCard;
use Wizacha\Marketplace\Subscription\Subscription;

class WrongCreditCardUserException extends SubscriptionException
{
    /**
     * WrongCreditCardUserException constructor.
     */
    public function __construct(CreditCard $creditCard, Subscription $subscription)
    {
        parent::__construct(sprintf(
            "User for credit card [%s] does not match user of subscription [%s]",
            $creditCard->getId(),
            $subscription->getId()
        ));
    }
}
