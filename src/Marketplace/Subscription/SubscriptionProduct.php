<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Subscription;

use Wizacha\Category;
use Wizacha\Marketplace\Entities\ProductShippingRate;
use Wizacha\Marketplace\Entities\Tax;
use Wizacha\Marketplace\PIM\TransactionMode\TransactionMode;
use Wizacha\Money\Money;
use Wizacha\Product;
use Wizacha\Status;

class SubscriptionProduct extends Product
{
    /** @var string */
    protected $subscriptionId;

    /** @var int[] */
    protected $taxes = [];

    /** @var int */
    protected $categoryId;

    /** @var Money */
    protected $price;

    /** @var string */
    protected $productName;

    /** @var string */
    protected $productCode;

    /** @var int */
    protected $quantity;

    /** @var bool */
    protected $isRenewable;

    /** @var int */
    protected $companyId;

    /** @var string */
    protected $companyName;

    public function __construct(OrderItem $orderItem, Subscription $subscription)
    {
        parent::__construct($orderItem->getProductId());

        $this->categoryId = $orderItem->getCategoryId();
        $this->price = $orderItem->getUnitPrice();
        $this->productName = $orderItem->getProductName();
        $this->productCode = $orderItem->getProductCode();
        $this->quantity = $orderItem->getQuantity();
        $this->isRenewable = $orderItem->getProductIsRenewable();

        $this->companyId = $subscription->getCompanyId();
        $this->companyName = fn_get_company_name($this->companyId);

        $this->taxes = $orderItem->getTaxItems()->map(function (TaxItem $taxItem): int {
            return $taxItem->getTaxId();
        })->getValues();

        $this->subscriptionId = $subscription->getId();

        $this->getData();
    }

    /** @return mixed[] */
    public function getDataFromDatabase(): array
    {
        return array_merge(
            $this->getData(),
            [
                // phpcs:ignore
                'product_id' => $this->product_id,
                'category_ids' => $this->categoryId,
                'original_price' => $this->price->getConvertedAmount(),
                'company_name' => $this->companyName,
                'company_status' => 'A',
                'tax_ids' => implode(',', $this->taxes),
                'productClassName' => \get_class($this),
            ]
        );
    }

    public function getTransactionMode(): TransactionMode
    {
        return $this->getData()['transaction_mode'];
    }

    public function getOriginalPrice(): Money
    {
        return Money::fromVariable($this->getData()['price']);
    }

    public function getSubscriptionId(): string
    {
        return $this->subscriptionId;
    }

    /** @return Tax[] */
    public function getTaxes(string $status = null): array
    {
        return array_map(
            function (int $taxId): Tax {
                return new Tax($taxId);
            },
            $this->taxes
        );
    }

    /** @return ProductShippingRate[] */
    public function getProductShippingsRates(): array
    {
        return [];
    }

    /** @return ProductShippingRate[] */
    public function getProductActiveShippingsRates(): array
    {
        return $this->getProductShippingsRates();
    }

    public function getCategory(): Category
    {
        return new Category($this->categoryId);
    }

    /** @return mixed[] */
    protected function getData(): array
    {
        if (0 === \count($this->getterData)) {
            $this->getterData = [
                'subscription_id' => $this->subscriptionId,
                'base_price' => $this->price->getConvertedAmount(),
                'price' => $this->price->getConvertedAmount(),
                'product' => $this->productName,
                'product_code' => $this->productCode,
                'approved' => 'Y',
                'avail_since' => time(),
                'tracking' => static::TRACKING_TYPE_CLASSICAL,
                'status' => Status::ENABLED,
                'short_description' => '',
                'full_description' => '',
                'amount' => $this->quantity,
                'original_amount' => $this->quantity,
                'w_condition' => static::CONDITION_NEW,
                'company_id' => $this->companyId,
                'timestamp' => time(),
                'transaction_mode' => TransactionMode::TRANSACTIONAL(),
                'w_supplier_ref' => '',
                'weight' => 0.,
                'infinite_stock' => true,
                'product_template_type' => 'product',
                'max_price_adjustment' => 0,
                'is_subscription' => true,
                'is_renewable' => $this->isRenewable,
                'subtotal' => $this->price->multiply($this->quantity)->getConvertedAmount(),
            ];
        }

        return $this->getterData;
    }
}
