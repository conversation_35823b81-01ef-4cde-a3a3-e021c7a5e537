<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Subscription;

use MyCLabs\Enum\Enum;

/**
 * @method static SubscriptionLinkStatus NOT_LINKED()
 * @method static SubscriptionLinkStatus INITIATING()
 * @method static SubscriptionLinkStatus RECURRING()
 */
class SubscriptionLinkStatus extends Enum
{
    public const NOT_LINKED = 0;
    public const INITIATING = 1;
    public const RECURRING = 2;
}
