<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Subscription;

use Wizacha\Marketplace\Traits\HasUuid;
use Wizacha\Money\Money;

class TaxItem implements \JsonSerializable
{
    use HasUuid;

    protected ?string $id;
    protected ?OrderItem $orderItem;
    protected ?int $taxId;
    protected ?float $rate;
    protected ?string $name;
    protected ?Money $amount;

    /** @param TaxItem[] $taxes */
    public static function applyTaxesOnMoney(Money $price, array $taxes, bool $isPriceIncluded): Money
    {
        // inspired from \Wizacha\Marketplace\Entities\Tax::applyTaxes

        if (true === empty($taxes)) {
            return new Money(0);
        }

        $rate = 0;
        foreach ($taxes as $tax) {
            $rate += $tax->getRate();
        }

        if (true === $isPriceIncluded) {
            $taxValue = $price->multiply(1 - 1 / (1 + $rate / 100));
        } else {
            $taxValue = $price->multiply($rate / 100);
        }

        return $taxValue;
    }

    public function getOrderItem(): ?OrderItem
    {
        return $this->orderItem;
    }

    public function setOrderItem(?OrderItem $orderItem): self
    {
        $this->orderItem = $orderItem;

        if ($orderItem instanceof OrderItem) {
            $orderItem->addTaxItem($this);
        }

        return $this;
    }

    public function getTaxId(): ?int
    {
        return $this->taxId;
    }

    public function setTaxId(?int $taxId): self
    {
        $this->taxId = $taxId;

        return $this;
    }

    public function getRate(): ?float
    {
        return $this->rate;
    }

    public function setRate(float $rate): self
    {
        $this->rate = $rate;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getAmount(): ?Money
    {
        return $this->amount;
    }

    public function setAmount(Money $amount): self
    {
        $this->amount = $amount;

        return $this;
    }

    /** @return mixed[] */
    public function jsonSerialize(): array
    {
        return [
            "taxId" => $this->getTaxId(),
            "taxName" => $this->getName(),
            "amount" => (string) $this->getAmount()->multiply($this->orderItem->getQuantity())->getConvertedAmount(),
        ];
    }
}
