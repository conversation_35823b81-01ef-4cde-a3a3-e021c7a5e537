<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Subscription;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Wizacha\Marketplace\CreditCard\CreditCard;
use Wizacha\Marketplace\Subscription\Exception\WrongCreditCardUserException;
use Wizacha\Marketplace\Subscription\Log\SubscriptionActionTrace;
use Wizacha\Marketplace\Traits\HasUuid;
use Wizacha\Marketplace\User\User;
use Wizacha\Money\Money;

class Subscription implements \JsonSerializable
{
    use HasUuid;

    protected ?string $id;
    protected ?int $firstOrderId;
    protected ?CreditCard $creditCard;
    protected ?User $user;
    protected ?int $companyId;
    protected ?string $name;
    protected ?string $status = null;
    protected ?Money $price;
    protected ?bool $isAutoRenew;
    protected ?\DateTimeImmutable $createdAt;
    protected ?int $commitmentPeriod;
    protected ?int $paymentFrequency;
    protected ?\DateTimeImmutable $nextPaymentAt;
    protected ?\DateTimeImmutable $commitmentEndAt;

    /** @var Collection|OrderItem[] */
    protected $orderItems;

    /** @var Collection|SubscriptionActionTrace[] */
    protected $subscriptionActionTrace;

    protected int $renewAttemptsCount;

    public function __construct()
    {
        $this->orderItems = new ArrayCollection();
        $this->renewAttemptsCount = 0;
    }

    public function getFirstOrderId(): ?int
    {
        return $this->firstOrderId;
    }

    public function setFirstOrderId(int $firstOrderId): self
    {
        $this->firstOrderId = $firstOrderId;

        return $this;
    }

    public function getCreditCard(): ?CreditCard
    {
        return $this->creditCard ?? null;
    }

    public function setCreditCard(?CreditCard $creditCard): self
    {
        if ($creditCard->getUser()->getUserId() !== $this->getUser()->getUserId()) {
            throw new WrongCreditCardUserException($creditCard, $this);
        }

        $this->creditCard = $creditCard;

        if ($creditCard instanceof CreditCard) {
            $creditCard->addSubscription($this);
        }

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): self
    {
        $this->user = $user;

        if ($user instanceof User) {
            $user->addSubscription($this);
        }

        return $this;
    }

    public function getCompanyId(): ?int
    {
        return $this->companyId;
    }

    public function setCompanyId(int $companyId): self
    {
        $this->companyId = $companyId;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getStatus(): ?SubscriptionStatus
    {
        if (false === \is_string($this->status)) {
            return null;
        }

        return new SubscriptionStatus($this->status);
    }

    // use this method only if you don't want to add any subscriptionActionTrace changes.
    // Otherwise, please use SubscriptionService->updateSubscriptionStatus()
    public function setStatus(SubscriptionStatus $status): self
    {
        $this->status = $status->getValue();

        return $this;
    }

    public function getPrice(): ?Money
    {
        return $this->price;
    }

    public function setPrice(Money $price): self
    {
        $this->price = $price;

        return $this;
    }

    public function isAutoRenew(): ?bool
    {
        return $this->isAutoRenew;
    }

    public function setIsAutoRenew(bool $isAutoRenew): self
    {
        $this->isAutoRenew = $isAutoRenew;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function defineCreatedAt(): self
    {
        $this->createdAt = new \DateTimeImmutable();

        return $this;
    }

    public function getCommitmentPeriod(): ?int
    {
        return $this->commitmentPeriod;
    }

    public function setCommitmentPeriod(int $commitmentPeriod): self
    {
        $this->commitmentPeriod = $commitmentPeriod;

        return $this;
    }

    public function getPaymentFrequency(): ?int
    {
        return $this->paymentFrequency;
    }

    public function setPaymentFrequency(int $paymentFrequency): self
    {
        $this->paymentFrequency = $paymentFrequency;

        return $this;
    }

    public function getNextPaymentAt(): ?\DateTimeImmutable
    {
        return $this->nextPaymentAt;
    }

    public function setNextPaymentAt(\DateTimeImmutable $nextPaymentAt): self
    {
        $this->nextPaymentAt = $nextPaymentAt;

        return $this;
    }

    public function getCommitmentEndAt(): ?\DateTimeImmutable
    {
        return $this->commitmentEndAt;
    }

    public function setCommitmentEndAt(\DateTimeImmutable $commitmentEndAt): self
    {
        $this->commitmentEndAt = $commitmentEndAt;

        return $this;
    }

    public function getRenewAttemptsCount(): int
    {
        return $this->renewAttemptsCount;
    }

    public function setRenewAttemptsCount(int $renewAttemptsCount): void
    {
        $this->renewAttemptsCount = $renewAttemptsCount;
    }

    public function incrementRenewAttemptsCount(): void
    {
        $this->renewAttemptsCount++;
    }

    /** @return Collection|OrderItem[] */
    public function getOrderItems(): Collection
    {
        return $this->orderItems;
    }

    /** @param OrderItem[] $orderItems */
    public function setOrderItems(iterable $orderItems): self
    {
        $this->clearOrderItems();

        foreach ($orderItems as $orderItem) {
            $this->addOrderItem($orderItem);
        }

        return $this;
    }

    public function addOrderItem(OrderItem $orderItem): self
    {
        if ($this->orderItems->contains($orderItem) === false) {
            $this->orderItems->add($orderItem);
            $orderItem->addSubscription($this);
        }

        return $this;
    }

    public function removeOrderItem(OrderItem $orderItem): self
    {
        if ($this->orderItems->contains($orderItem)) {
            $this->orderItems->removeElement($orderItem);
            $orderItem->removeSubscription($this);
        }

        return $this;
    }

    public function clearOrderItems(): self
    {
        foreach ($this->getOrderItems() as $orderItem) {
            $this->removeOrderItem($orderItem);
        }

        $this->orderItems->clear();

        return $this;
    }

    /**
     * @param OrderItem[] $orderItems
     *
     * @return string[]
     */
    public function getPriceDetails(array $orderItems): array
    {
        $priceIncludingTaxes = new Money(0);
        $priceExcludingTaxes = new Money(0);
        $totalTaxesAmount = new Money(0);

        foreach ($orderItems as $orderItem) {
            $taxesAmount = new Money(0);

            foreach ($orderItem->getTaxItems() as $taxItem) {
                $taxesAmount = $taxesAmount->add($taxItem->getAmount());
            }

            $taxesAmount = $taxesAmount->multiply($orderItem->getQuantity());

            if ($orderItem->getPriceIncludeTaxes() === true) {
                $priceIncludingTaxes = $this->getPrice();
                $priceExcludingTaxes = $this->getPrice()->subtract($taxesAmount);
            } else {
                $priceIncludingTaxes = $this->getPrice()->add($taxesAmount);
                $priceExcludingTaxes = $this->getPrice();
            }

            $totalTaxesAmount = $totalTaxesAmount->add($taxesAmount);
        }

        return [
            'excludingTaxes' => (string) $priceExcludingTaxes->getConvertedAmount(),
            'taxes' => (string) $totalTaxesAmount->getConvertedAmount(),
            'includingTaxes' => (string) $priceIncludingTaxes->getConvertedAmount(),
        ];
    }

    /** @return Collection|SubscriptionActionTrace[] */
    public function getSubscriptionActionTraces(): Collection
    {
        return $this->subscriptionActionTrace;
    }

    /** @return mixed[] */
    public function expose(): array
    {
        $items = $this->getOrderItems();
        $taxes = [];

        foreach ($items as $item) {
            foreach ($item->getTaxItems() as $taxItem) {
                if (false === \array_key_exists($taxItem->getTaxId(), $taxes)) {
                    $taxes[$taxItem->getTaxId()] = [
                        "taxId" => $taxItem->getTaxId(),
                        "taxName" => $taxItem->getName(),
                        "amount" => 0,
                    ];
                }

                $amount = Money::fromVariable($taxes[$taxItem->getTaxId()]["amount"])->add($taxItem->getAmount());
                $taxes[$taxItem->getTaxId()]["amount"] = (string) $amount->multiply($item->getQuantity())->getConvertedAmount();
            }
        }

        return \array_merge(
            $this->jsonSerialize(),
            [
                'items' => \array_map(
                    /** @return mixed[] */
                    static function (OrderItem $orderItem): array {
                        return $orderItem->jsonSerialize();
                    },
                    $items->toArray()
                ),
                'taxes' => \array_values($taxes),
            ]
        );
    }

    /** @return mixed[] */
    public function jsonSerialize(): array
    {
        return [
            'id' => $this->getId(),
            'userId' => $this->getUser()->getUserId(),
            'companyId' => $this->getCompanyId(),
            'cardId' => $this->getCreditCard() instanceof CreditCard ? $this->getCreditCard()->getId() : null,
            'firstOrderId' => $this->getFirstOrderId(),
            'name' => $this->getName(),
            'status' => $this->getStatus(),
            'price' => $this->getPriceDetails($this->getOrderItems()->toArray()),
            'isAutorenew' => $this->isAutoRenew(),
            'commitmentPeriod' => $this->getCommitmentPeriod(),
            'paymentFrequency' => $this->getPaymentFrequency(),
            'nextPaymentAt' => $this->getNextPaymentAt()->format('Y-m-d'),
            'commitmentEndAt' => $this->getCommitmentEndAt()->format('Y-m-d'),
            'createdAt' => $this->getCreatedAt()->format(\DateTimeImmutable::RFC3339),
            'renewAttemptsCount' => $this->getRenewAttemptsCount(),
        ];
    }
}
