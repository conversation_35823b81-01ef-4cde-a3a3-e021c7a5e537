<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Subscription;

use Wizacha\Marketplace\ReadModel\Product as ReadModelProduct;

class SubscriptionReadModelProduct extends ReadModelProduct
{
    public function __construct()
    {
        parent::__construct(null);
    }

    public function getCreatedAt(): \DateTimeInterface
    {
        return new \DateTime();
    }

    public function getUpdatedAt(): \DateTimeInterface
    {
        return new \DateTime();
    }
}
