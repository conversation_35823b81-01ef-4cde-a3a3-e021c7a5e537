<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Subscription;

use MyCLabs\Enum\Enum;

/**
 * @method static SubscriptionSortBy CREATED_AT()
 * @method static SubscriptionSortBy NAME()
 * @method static SubscriptionSortBy USER()
 * @method static SubscriptionSortBy PRODUCT()
 * @method static SubscriptionSortBy NEXT_PAYMENT()
 * @method static SubscriptionSortBy TOTAL()
 * @method static SubscriptionSortBy STATUS()
 */
class SubscriptionSortBy extends Enum
{
    protected const CREATED_AT = 'createdAt';
    protected const NAME = 'name';
    protected const USER = 'user';
    protected const PRODUCT = 'product';
    protected const NEXT_PAYMENT = 'nextPayment';
    protected const TOTAL = 'total';
    protected const STATUS = 'status';
}
