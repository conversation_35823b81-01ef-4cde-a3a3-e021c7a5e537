Wizacha\Marketplace\Subscription\PriceTier:
    type: entity
    table: subscription_price_tiers
    indexes:
        subscription_order_item_id:
            columns:
                - subscription_order_item_id
    uniqueConstraints:
        subscription_order_item_lower_limit:
            columns:
                - subscription_order_item_id
                - lower_limit
    id:
        id:
            type: integer
            generator:
                strategy: AUTO
            options:
                unsigned: true
    fields:
        lowerLimit:
            type: integer
            options:
                unsigned: true
        includingTaxes:
            type: money
            options:
                unsigned: true
        excludingTaxes:
            type: money
            options:
                unsigned: true
        taxes:
            type: money
            options:
                unsigned: true
    manyToOne:
        subscriptionOrderItem:
            targetEntity: Wizacha\Marketplace\Subscription\OrderItem
            inversedBy: priceTiers
            joinColumn:
                referencedColumnName: id
                nullable: false
                cascade:
                    - persist
