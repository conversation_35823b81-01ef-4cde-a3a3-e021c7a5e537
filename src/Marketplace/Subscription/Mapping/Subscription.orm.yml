Wizacha\Marketplace\Subscription\Subscription:
    type: entity
    id:
        id:
            type: guid
            length: 36
            options:
                fixed: true
    indexes:
        first_order_id_company_id_index:
            columns: [ first_order_id, company_id ]
    fields:
        firstOrderId:
            type: integer
            options:
                unsigned: true
        companyId:
            type: integer
            options:
                unsigned: true
        name: ~
        status:
            length: 15
        price:
            type: money
            options:
                unsigned: true
        isAutoRenew:
            type: boolean
        createdAt:
            type: datetimetz_immutable
        commitmentPeriod:
            type: integer
            options:
                unsigned: true
        paymentFrequency:
            type: integer
            options:
                unsigned: true
        nextPaymentAt:

            type: datetimetz_immutable
        commitmentEndAt:
            type: datetimetz_immutable
        renewAttemptsCount:
            type: integer
            options:
                unsigned: true
    lifecycleCallbacks:
        prePersist: [ defineCreatedAt, defineGuid ]
    manyToOne:
        creditCard:
            targetEntity: Wizacha\Marketplace\CreditCard\CreditCard
            inversedBy: subscriptions
            joinColumn:
                referencedColumnName: id
            cascade: [persist]
        user:
            targetEntity: Wizacha\Marketplace\User\User
            inversedBy: subscriptions
            joinColumn:
                referencedColumnName: user_id
                nullable: false
            cascade: [persist]
    oneToMany:
        subscriptionActionTrace:
            targetEntity: Wizacha\Marketplace\Subscription\Log\SubscriptionActionTrace
            mappedBy: subscription
            orphanRemoval: true
            cascade: [ all ]
            fetch: EXTRA_LAZY
    manyToMany:
        orderItems:
            targetEntity: Wizacha\Marketplace\Subscription\OrderItem
            inversedBy: subscriptions
            joinTable:
                name: subscriptions_has_order_items
                joinColumns:
                    subscription_id:
                        referencedColumnName: id
                        nullable: false
                inverseJoinColumns:
                    order_items_id:
                        referencedColumnName: id
            cascade: [persist]
