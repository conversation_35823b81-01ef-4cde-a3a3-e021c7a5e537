Wizacha\Marketplace\Subscription\OrderItem:
    type: entity
    id:
        id:
            type: guid
            length: 36
            options:
                fixed: true
    indexes:
        category_id_index:
            columns: [ category_id ]
        product_id_index:
            columns: [ product_id ]
    fields:
        categoryId:
            type: integer
        productId:
            type: integer
        productCode: ~
        productName: ~
        productIsRenewable:
            type: boolean
        productOptionInventoryCode: ~
        quantity:
            type: integer
            options:
                unsigned: true
        unitPrice:
            type: money
            options:
                unsigned: true
        totalPrice:
            type: money
            options:
                unsigned: true
        priceIncludeTaxes:
            type: boolean
    lifecycleCallbacks:
        prePersist: [ defineGuid ]
    manyToMany:
        subscriptions:
            targetEntity: Wizacha\Marketplace\Subscription\Subscription
            mappedBy: orderItems
    oneToMany:
        taxItems:
            targetEntity: Wizacha\Marketplace\Subscription\TaxItem
            mappedBy: orderItem
            orphanRemoval: true
            cascade: [persist, remove]
        priceTiers:
            targetEntity: Wizacha\Marketplace\Subscription\PriceTier
            mappedBy: subscriptionOrderItem
            fetch: EAGER
            orphanRemoval: true
            cascade: [persist, remove]
