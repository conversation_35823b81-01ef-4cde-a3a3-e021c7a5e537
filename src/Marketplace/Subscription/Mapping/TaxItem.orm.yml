Wizacha\Marketplace\Subscription\TaxItem:
    type: entity
    id:
        id:
            type: guid
            length: 36
            options:
                fixed: true
    indexes:
        tax_id_index:
            columns: [ tax_id ]
    fields:
        taxId:
            type: integer
            options:
                unsigned: true
        rate:
            type: float
            precision: 10
            scale: 2
        name: ~
        amount:
            type: money
            options:
                unsigned: true
    lifecycleCallbacks:
        prePersist: [ defineGuid ]
    manyToOne:
        orderItem:
            targetEntity: Wizacha\Marketplace\Subscription\OrderItem
            inversedBy: taxItems
            joinColumn:
                referencedColumnName: id
                nullable: false
