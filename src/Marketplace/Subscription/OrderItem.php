<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Subscription;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Wizacha\Marketplace\Traits\HasUuid;
use Wizacha\Money\Money;

// This class is poorly named as it represents a subscription item, and not a a generic order item
class OrderItem implements \JsonSerializable
{
    use HasUuid;

    protected ?string $id;
    protected ?int $categoryId;
    protected ?int $productId;
    protected ?string $productCode;
    protected ?string $productName;
    protected bool $productIsRenewable;
    protected ?string $productOptionInventoryCode;
    protected ?int $quantity;
    protected ?Money $unitPrice;
    protected ?Money $totalPrice;
    protected bool $priceIncludeTaxes;

    /** @var Collection|Subscription[] */
    protected Collection $subscriptions;

    /** @var Collection|TaxItem[] */
    protected Collection $taxItems;

    /** @var Collection|PriceTier[] */
    protected $priceTiers;

    public function __construct()
    {
        $this->subscriptions = new ArrayCollection();
        $this->taxItems = new ArrayCollection();
        $this->priceTiers = new ArrayCollection();
    }

    public function getCategoryId(): ?int
    {
        return $this->categoryId;
    }

    public function setCategoryId(?int $categoryId): self
    {
        $this->categoryId = $categoryId;

        return $this;
    }

    public function getProductId(): ?int
    {
        return $this->productId;
    }

    public function setProductId(?int $productId): self
    {
        $this->productId = $productId;

        return $this;
    }

    public function getProductCode(): ?string
    {
        return $this->productCode;
    }

    public function setProductCode(string $productCode): self
    {
        $this->productCode = $productCode;

        return $this;
    }

    public function getProductName(): ?string
    {
        return $this->productName;
    }

    public function setProductName(string $productName): self
    {
        $this->productName = $productName;

        return $this;
    }

    public function getProductIsRenewable(): bool
    {
        return $this->productIsRenewable;
    }

    public function setProductIsRenewable(bool $productIsRenewable): self
    {
        $this->productIsRenewable = $productIsRenewable;

        return $this;
    }

    public function getProductOptionInventoryCode(): ?string
    {
        return $this->productOptionInventoryCode;
    }

    public function setProductOptionInventoryCode(string $productOptionInventoryCode): self
    {
        $this->productOptionInventoryCode = $productOptionInventoryCode;

        return $this;
    }

    public function getQuantity(): ?int
    {
        return $this->quantity;
    }

    public function setQuantity(int $quantity): self
    {
        $this->quantity = $quantity;

        return $this;
    }

    public function getUnitPrice(): ?Money
    {
        return $this->unitPrice;
    }

    public function setUnitPrice(Money $unitPrice): self
    {
        $this->unitPrice = $unitPrice;

        return $this;
    }

    public function getTotalPrice(): ?Money
    {
        return $this->totalPrice;
    }

    public function setTotalPrice(Money $totalPrice): self
    {
        $this->totalPrice = $totalPrice;

        return $this;
    }

    public function getPriceIncludeTaxes(): bool
    {
        return $this->priceIncludeTaxes;
    }

    public function setPriceIncludeTaxes(bool $priceIncludeTaxes): self
    {
        $this->priceIncludeTaxes = $priceIncludeTaxes;

        return $this;
    }

    /** @return Collection|Subscription[] */
    public function getSubscriptions(): Collection
    {
        return $this->subscriptions;
    }

    /** @param Subscription[] $subscriptions */
    public function setSubscriptions(iterable $subscriptions): self
    {
        $this->clearSubscriptions();

        foreach ($subscriptions as $subscription) {
            $this->addSubscription($subscription);
        }

        return $this;
    }

    public function addSubscription(Subscription $subscription): self
    {
        if ($this->subscriptions->contains($subscription) === false) {
            $this->subscriptions->add($subscription);
            $subscription->addOrderItem($this);
        }

        return $this;
    }

    public function removeSubscription(Subscription $subscription): self
    {
        if ($this->subscriptions->contains($subscription)) {
            $this->subscriptions->removeElement($subscription);
            $subscription->removeOrderItem($this);
        }

        return $this;
    }

    public function clearSubscriptions(): self
    {
        foreach ($this->getSubscriptions() as $subscription) {
            $this->removeSubscription($subscription);
        }

        $this->subscriptions->clear();

        return $this;
    }

    /** @return Collection|TaxItem[] */
    public function getTaxItems(): Collection
    {
        return $this->taxItems;
    }

    /** @param TaxItem[] $taxItems */
    public function setTaxItems(iterable $taxItems): self
    {
        $this->clearTaxItems();

        foreach ($taxItems as $taxItem) {
            $this->addTaxItem($taxItem);
        }

        return $this;
    }

    public function addTaxItem(TaxItem $taxItem): self
    {
        if ($this->taxItems->contains($taxItem) === false) {
            $this->taxItems->add($taxItem);
            $taxItem->setOrderItem($this);
        }

        return $this;
    }

    public function removeTaxItem(TaxItem $taxItem): self
    {
        if ($this->taxItems->contains($taxItem)) {
            $this->taxItems->removeElement($taxItem);
            $taxItem->setOrderItem(null);
        }

        return $this;
    }

    public function clearTaxItems(): self
    {
        foreach ($this->getTaxItems() as $taxItem) {
            $this->removeTaxItem($taxItem);
        }

        $this->taxItems->clear();

        return $this;
    }

    /** @return mixed[] */
    public function jsonSerialize(): array
    {
        return [
            "categoryId" => $this->getCategoryId(),
            "productId" => $this->getProductId(),
            "productCode" => $this->getProductCode(),
            "productName" => $this->getProductName(),
            "productIsRenewable" => $this->getProductIsRenewable(),
            "declinationId" => $this->getProductOptionInventoryCode(),
            "unitPrice" => $this->getPriceDetails($this->getTaxItems(), $this->getPriceIncludeTaxes(), $this->getUnitPrice()),
            "priceTiers" => $this->getPriceTiers()->toArray(),
            "quantity" => $this->getQuantity(),
            "totalPrice" => $this->getPriceDetails(
                $this->getTaxItems(),
                $this->getPriceIncludeTaxes(),
                $this->getTotalPrice(),
                $this->getQuantity()
            ),
        ];
    }

    /**
     * @param TaxItem[]|Collection $taxItems
     *
     * @return string[]
     */
    public function getPriceDetails(Collection $taxItems, bool $priceIncludeTaxes, Money $price, int $quantity = 1): array
    {
        $taxesAmount = new Money(0);

        foreach ($taxItems as $taxItem) {
            $taxesAmount = $taxesAmount->add($taxItem->getAmount());
        }

        $taxesAmount = $taxesAmount->multiply($quantity);

        if (true === $priceIncludeTaxes) {
            $priceIncludingTaxes = $price;
            $priceExcludingTaxes = $price->subtract($taxesAmount);
        } else {
            $priceIncludingTaxes = $price->add($taxesAmount);
            $priceExcludingTaxes = $price;
        }

        return [
            'excludingTaxes' => (string) $priceExcludingTaxes->getConvertedAmount(),
            'taxes' => (string) $taxesAmount->getConvertedAmount(),
            'includingTaxes' => (string) $priceIncludingTaxes->getConvertedAmount(),
        ];
    }

    /** @return Collection|PriceTier[] */
    public function getPriceTiers(): Collection
    {
        return $this->priceTiers;
    }

    /** @param PriceTier[] $priceTiers */
    public function setPriceTiers(iterable $priceTiers): self
    {
        $this->clearPriceTiers();

        foreach ($priceTiers as $priceTier) {
            $this->addPriceTiers($priceTier);
        }

        return $this;
    }


    public function addPriceTiers(PriceTier $priceTier): self
    {
        if ($this->priceTiers->contains($priceTier) === false) {
            $this->priceTiers->add($priceTier);
            $priceTier->setSubscriptionOrderItem($this);
        }

        return $this;
    }

    // only delete object relation between orderItem -> priceTier.
    // if you want to delete $priceTier from database,
    // please use a SubscriptionPriceTierRepository->remove($priceTier) call
    public function removePriceTiers(PriceTier $priceTier): self
    {
        if ($this->priceTiers->contains($priceTier)) {
            $this->priceTiers->removeElement($priceTier);
        }

        return $this;
    }

    public function clearPriceTiers(): self
    {
        foreach ($this->getPriceTiers() as $priceTier) {
            $this->removePriceTiers($priceTier);
        }

        $this->priceTiers->clear();

        return $this;
    }

    public function updateUnitPriceWithPriceTiers(): void
    {
        if (0 === \count($this->getPriceTiers())) {
            return;
        }

        $currentLimit = 0;
        $electedPriceTier = null;
        // looking for best priceTier to apply
        foreach ($this->getPriceTiers() as $priceTier) {
            $lowerLimit = $priceTier->getLowerLimit();
            if ($currentLimit <= $lowerLimit && $lowerLimit <= $this->getQuantity()) {
                $currentLimit = $lowerLimit;
                $electedPriceTier = $priceTier;
            }
        }
        // update price with elected priceTier
        if (null !== $electedPriceTier) {
            if (true === $this->getPriceIncludeTaxes()) {
                $this->setUnitPrice($electedPriceTier->getIncludingTaxes());
            } else {
                $this->setUnitPrice($electedPriceTier->getExcludingTaxes());
            }
            if (true === isset($this->getTaxItems()[0])) {
                $this->getTaxItems()[0]->setAmount($electedPriceTier->getTaxes());
            }
        }
    }
}
