<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Subscription;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\QueryBuilder;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Wizacha\Component\Pagination\Pagination;
use Wizacha\Marketplace\Company\Company;
use Wizacha\Marketplace\Order\Exception\OrderCorruptedException;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\User\User;
use Wizacha\Money\Money;

class SubscriptionRepository extends ServiceEntityRepository
{
    public function save(Subscription $subscription): Subscription
    {
        $this->getEntityManager()->persist($subscription);

        // To avoid ORMInvalidArgumentException::newEntitiesFoundThroughRelationships without cascade: persist
        // But with cascade: persist we've got the same issue ¯\_(ツ)_/¯
        foreach ($subscription->getOrderItems() as $orderItem) {
            foreach ($orderItem->getTaxItems() as $taxItem) {
                $this->getEntityManager()->persist($taxItem);
            }
        }

        // Doctrine ne gérant pas correctement les cascades sur un flush d'entité ManyToMany,
        // on est obligé de faire un flush global de toutes les entités persistés
        $this->getEntityManager()->flush();

        return $subscription;
    }

    public function findOneById(string $id): ?Subscription
    {
        return $this
            ->createQueryBuilder('sub')
            ->where('sub.id = :id')
            ->setParameter('id', $id)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }

    /**
     * @param int[] $ordersIds
     *
     * @return Subscription[]
     */
    public function findByFirstOrderIds(array $ordersIds): array
    {
        return $this
            ->createQueryBuilder('sub')
            ->where('sub.firstOrderId IN (:ordersId)')
            ->setParameter('ordersId', $ordersIds)
            ->getQuery()
            ->getResult()
        ;
    }

    /** @return null|Subscription[] */
    public function findByFirstOrderId(int $orderId): ?array
    {
        return $this
            ->createQueryBuilder('sub')
            ->where('sub.firstOrderId = :orderId')
            ->setParameter('orderId', $orderId)
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * @param mixed[] $filters
     *
     * @return Subscription[]
     */
    public function findByUser(User $user, int $limit = null, int $offset = null, array $filters = []): array
    {
        $filters['userId'] = $user->getUserId();

        return $this->findByFilters($limit, $offset, $filters);
    }

    /**
     * @param mixed[] $filters
     *
     * @return Subscription[]
     */
    public function findByCompany(
        Company $company,
        int $limit = null,
        int $offset = null,
        array $filters = [],
        string $sortBy = null,
        string $sortOrder = 'DESC'
    ): array {
        $filters['companyId'] = $company->getId();

        return $this->findByFilters($limit, $offset, $filters, $sortBy, $sortOrder);
    }

    /**
     * @param mixed[] $filters
     *
     * @return array [Subscription[], int $totalCount]
     */
    public function findByFilters(int $limit = null, int $offset = null, array $filters = [], string $sortBy = null, string $sortOrder = 'DESC'): array
    {
        $queryBuilder = $this
            ->createQueryBuilder('sub')
        ;

        $this->subscriptionFieldsSortBy($queryBuilder, $sortBy, $sortOrder);

        if (\array_key_exists('userId', $filters)) {
            $queryBuilder
                ->where('sub.user = :userId')
                ->setParameter('userId', $filters['userId'])
            ;
        }

        if (\array_key_exists('status', $filters)) {
            if ($filters['status'] !== '') {
                $queryBuilder
                    ->andWhere('sub.status = :status')
                    ->setParameter('status', $filters['status'])
                ;
            }
        }

        if (\array_key_exists('companyId', $filters)) {
            $queryBuilder
                ->andWhere('sub.companyId = :companyId')
                ->setParameter('companyId', $filters['companyId'])
            ;
        }

        if (\array_key_exists('productId', $filters)) {
            $queryBuilder
                ->join('sub.orderItems', 'orderItem')
                ->andWhere('orderItem.productId = :productId')
                ->setParameter('productId', $filters['productId'])
            ;
        }

        if (\array_key_exists('commitmentEndBefore', $filters)) {
            $queryBuilder
                ->andWhere('sub.commitmentEndAt <= :commitmentEndBefore')
                ->setParameter('commitmentEndBefore', $filters['commitmentEndBefore'])
            ;
        }

        if (\array_key_exists('commitmentEndAfter', $filters)) {
            $queryBuilder
                ->andWhere('sub.commitmentEndAt > :commitmentEndAfter')
                ->setParameter('commitmentEndAfter', $filters['commitmentEndAfter'])
            ;
        }

        if (\array_key_exists('isAutorenew', $filters)) {
            $queryBuilder
                ->andWhere('sub.isAutoRenew = :isAutorenew')
                ->setParameter('isAutorenew', $filters['isAutorenew'])
            ;
        }

        if ((\array_key_exists('customer_name', $filters) && $filters['customer_name'] !== '')
            || (\array_key_exists('email', $filters) && $filters['email'] !== '')
            || (\array_key_exists('all', $filters) && \trim($filters['all']) !== '')
        ) {
            $queryBuilder->join('sub.user', 'user');
        }

        if (\array_key_exists('customer_name', $filters) && $filters['customer_name'] !== '') {
            $queryBuilder
                ->andWhere($queryBuilder->expr()->orX(
                    $queryBuilder->expr()->like('user.firstname', ':customerName'),
                    $queryBuilder->expr()->like('user.lastname', ':customerName')
                ))
                ->setParameter('customerName', '%' . $filters['customer_name'] . '%')
            ;
        }

        if (\array_key_exists('email', $filters) && $filters['email'] !== '') {
            $queryBuilder
                ->andWhere($queryBuilder->expr()->like('user.email', ':email'))
                ->setParameter('email', '%' . $filters['email'] . '%')
            ;
        }

        if (\array_key_exists('total_from', $filters) && $filters['total_from'] !== '') {
            $queryBuilder
                ->andWhere('sub.price >= :total_from')
                ->setParameter('total_from', Money::fromVariable($filters['total_from'])->getAmount())
            ;
        }

        if (\array_key_exists('total_to', $filters) && $filters['total_to'] !== '') {
            $queryBuilder
                ->andWhere('sub.price <= :total_to')
                ->setParameter('total_to', Money::fromVariable($filters['total_to'])->getAmount())
            ;
        }

        if (\array_key_exists('all', $filters) && \trim($filters['all']) !== '') {
            $queryBuilder
                ->join('sub.orderItems', 'orderItem')
                ->andWhere($queryBuilder->expr()->orX(
                    $queryBuilder->expr()->like('sub.name', ':filter'),
                    $queryBuilder->expr()->like('user.email', ':filter'),
                    $queryBuilder->expr()->like('orderItem.productName', ':filter')
                ))
                ->setParameter('filter', '%' . $filters['all'] . '%')
            ;
        }

        if (\is_int($limit) && \is_int($offset)) {
            $queryBuilder
                ->setMaxResults($limit)
                ->setFirstResult($offset)
            ;
        }

        $paginator = new Paginator($queryBuilder);

        return [$paginator->getQuery()->getResult(), $paginator->count()];
    }

    /** @return Subscription[] */
    public function getAutoRenewable(): array
    {
        $today = (new \DateTimeImmutable())->setTime(0, 0, 0);
        $tomorrow = $today->modify("+1 day");
        $queryBuilder = $this->createQueryBuilder("subscription");

        return $queryBuilder
            ->where(
                $queryBuilder->expr()->orX(
                    $queryBuilder->expr()->andX(
                        $queryBuilder->expr()->in('subscription.status', ':renewTodayStatus'),
                        $queryBuilder->expr()->gte('subscription.nextPaymentAt', ':today'),
                        $queryBuilder->expr()->lt('subscription.nextPaymentAt', ':tomorrow')
                    ),
                    $queryBuilder->expr()->eq("subscription.status", ":defaulted")
                )
            )
            ->andWhere($queryBuilder->expr()->orX(
                $queryBuilder->expr()->eq('subscription.isAutoRenew', 1),
                $queryBuilder->expr()->andX(
                    $queryBuilder->expr()->eq('subscription.isAutoRenew', 0),
                    $queryBuilder->expr()->gte('subscription.commitmentEndAt', ':today')
                )
            ))
            ->setParameters([
                ':renewTodayStatus' => [
                    SubscriptionStatus::ACTIVE()->getValue(),
                    SubscriptionStatus::DEFAULTED()->getValue(),
                    SubscriptionStatus::WAITING_RENEW()->getValue(),
                ],
                ':defaulted' => SubscriptionStatus::DEFAULTED()->getValue(),
                ':today' => $today->format("Y-m-d H:i:s"),
                ':tomorrow' => $tomorrow->format("Y-m-d H:i:s"),
            ])
            ->getQuery()
            ->getResult()
        ;
    }

    /** @return Subscription[] */
    public function getNonAutoRenewable(): array
    {
        return $this
            ->createQueryBuilder("subscription")
            ->where("subscription.status IN (:active, :defaulted)")
            ->andWhere("subscription.isAutoRenew = :isAutoRenew")
            ->andWhere("subscription.commitmentEndAt <= :today")
            ->setParameters([
                ':active' => SubscriptionStatus::ACTIVE()->getValue(),
                ':defaulted' => SubscriptionStatus::DEFAULTED()->getValue(),
                ':isAutoRenew' => false,
                ':today' => (new \DateTimeImmutable())->setTime(23, 59, 59)->format("Y-m-d H:i:s"),
            ])
            ->getQuery()
            ->getResult()
        ;
    }

    /** @return Subscription[] */
    public function getActiveOrDefaulted(User $user): array
    {
        return $this
            ->createQueryBuilder("subscription")
            ->where("subscription.status IN (:active, :defaulted) and subscription.user = :user")
            ->setParameters([
                ':active' => SubscriptionStatus::ACTIVE()->getValue(),
                ':defaulted' => SubscriptionStatus::DEFAULTED()->getValue(),
                ':user' => $user
            ])
            ->getQuery()
            ->getResult()
        ;
    }

    /** @return TaxItem[] */
    public function getTaxesFromSubscriptionId(string $subscriptionId, int $limit = null, int $offset = null): array
    {
        $queryBuilder = $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->select("taxItem")
            ->from(TaxItem::class, 'taxItem')
            ->join('taxItem.orderItem', 'orderItem')
            ->join('orderItem.subscriptions', 'subscriptions')
            ->andWhere('subscriptions.id = :subscriptionId')
            ->setParameter('subscriptionId', $subscriptionId)
            ->orderBy('taxItem.rate', 'ASC')
            ->addOrderBy('taxItem.name', 'ASC')
        ;

        if (\is_int($limit) && \is_int($offset)) {
            $queryBuilder
                ->setMaxResults($limit)
                ->setFirstResult($offset)
            ;
        }

        return $queryBuilder
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * On récupère ici uniquement l'id de la commande initiale (commande parente ayant initié l'abonnement)
     * @return Order[]
     */
    public function getFirstOrderFromSubscriptionId(string $subscriptionId): ?array
    {
        $query = "SELECT order_id
                FROM cscart_orders as o
                LEFT JOIN doctrine_subscription as s ON o.order_id = s.first_order_id
                WHERE s.id = :subscriptionId";

        $params = [
            'subscriptionId' => $subscriptionId,
        ];

        $pdo = $this->getEntityManager()->getConnection();

        $statement = $pdo->prepare($query);
        $statement->execute($params);

        return $this->getOrderInfo((int) $statement->fetchColumn());
    }

    /**
     * @return Order[]|null
     */
    public function getLastOrderFromSubscriptionId(string $subscriptionId): ?array
    {
        $query = "SELECT order_id
                FROM cscart_orders o
                INNER JOIN doctrine_subscription s ON o.subscription_id = s.id
                WHERE o.subscription_id = :subscriptionId
                AND o.workflow_name IS NOT NULL
                ORDER BY o.timestamp DESC
                LIMIT 1;"
        ;

        $pdo = $this->getEntityManager()->getConnection();

        $statement = $pdo->prepare($query);
        $statement->execute(['subscriptionId' => $subscriptionId]);
        $orderId = $statement->fetchOne();

        if ($orderId === false) {
            return null;
        }

        return $this->getOrderInfo((int) $orderId);
    }

    /**
     * On récupère un tableau contenant uniquement les commandes subséquentes
     */
    public function getSubsequentOrdersFromSubscriptionId(string $subscriptionId, int $limit = null, int $offset = null, string $sortOrder = 'ASC'): Pagination
    {
        $params[] = $subscriptionId;
        $statusCondition = '';

        // uncomment this bloc if you want to filter subscriptions orders depending on companyId
//        $companyId = \Wizacha\Company::runtimeID('A', $_SESSION ?? [], \Wizacha\Registry::defaultInstance());
//        $accepted_statuses = \Wizacha\OrderStatus::getVendorDisplayedStatuses();
//        if (0 !== $companyId) {
//            $in  = str_repeat('?,', count($accepted_statuses) - 1) . '?';
//            $statusCondition = "AND status IN ($in)";
//            $params = array_merge(
//                $params,
//                $accepted_statuses
//            );
//        }

        $query = (
            "SELECT
                order_id,
                `timestamp`
            FROM
                cscart_orders
            WHERE
                subscription_id = ?
                $statusCondition
            ORDER BY `timestamp` " . $sortOrder
        );


        if (\is_int($limit) && \is_int($offset)) {
            $query .= " LIMIT $offset,$limit";
        }

        $pdo = $this->getEntityManager()->getConnection();

        $statement = $pdo->prepare($query);
        $statement->execute($params);

        $orders = [];
        while ($info = $statement->fetch()) {
            [
                'order_id' => $orderId,
                'timestamp' => $timestamp,
            ] = \array_map('intval', $info);

            $orders[] = \array_merge(
                $this->getOrderInfo((int) $orderId),
                ['timestamp' => $timestamp]
            );
        }

        $countQuery = "SELECT COUNT(order_id) AS total
                  FROM cscart_orders
                  WHERE subscription_id = ? $statusCondition";

        $total = $pdo->prepare($countQuery);
        $total->execute($params);

        return new Pagination($orders, (int) $total->fetchColumn(), $offset, $limit);
    }

    /** @return OrderItem[] */
    public function getItemsFromSubscriptionId(string $subscriptionId, int $limit = null, int $offset = null): array
    {
        $queryBuilder = $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->select("orderItem")
            ->from(OrderItem::class, 'orderItem')
            ->join('orderItem.subscriptions', 'subscriptions')
            ->andWhere('subscriptions.id = :subscriptionId')
            ->setParameter('subscriptionId', $subscriptionId)
            ->orderBy('orderItem.productId', 'ASC')
        ;

        if (\is_int($limit) && \is_int($offset)) {
            $queryBuilder
                ->setMaxResults($limit)
                ->setFirstResult($offset)
            ;
        }

        return $queryBuilder
            ->getQuery()
            ->getResult()
        ;
    }

    protected function getOrderInfo(int $orderId): ?array
    {
        try {
            $orderService = container()->get('marketplace.order.order_service');

            return fn_api_order_info($orderService->overrideLegacyOrder(
                $orderId,
                false,
                false,
                false,
                false,
                null,
                'A'
            ));
        } catch (OrderCorruptedException $e) {
            return null;
        }
    }

    protected function subscriptionFieldsSortBy(
        QueryBuilder $queryBuilder,
        string $sortBy = null,
        string $sortOrder = 'DESC'
    ): QueryBuilder {
        switch ($sortBy) {
            case 'name':
                $queryBuilder->orderBy('sub.name', $sortOrder);
                break;
            case 'user':
                $queryBuilder->orderBy('sub.user', $sortOrder);
                break;
            case 'product':
                $queryBuilder
                    ->join('sub.orderItems', 'orderItem')
                    ->orderBy('orderItem.productName', $sortOrder)
                ;
                break;
            case 'nextPayment':
                $queryBuilder->orderBy('sub.nextPaymentAt', $sortOrder);
                break;
            case 'total':
                $queryBuilder->orderBy('sub.price', $sortOrder);
                break;
            case 'status':
                $queryBuilder->orderBy('sub.status', $sortOrder);
                break;
            default:
                $queryBuilder->orderBy('sub.createdAt', $sortOrder);
                break;
        }

        // Add this to fix random sorting
        $queryBuilder->addOrderBy('sub.id', 'ASC');

        return $queryBuilder;
    }
}
