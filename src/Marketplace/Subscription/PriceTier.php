<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Subscription;

use Wizacha\Marketplace\PriceTier\PriceTier as MarketplacePriceTier;
use Wizacha\Marketplace\Order\OrderItem as MarketplaceOrderItem;
use Wizacha\Money\Money;

class PriceTier implements \JsonSerializable
{
    protected ?int $id;

    protected int $lowerLimit;

    protected Money $includingTaxes;

    protected Money $excludingTaxes;

    protected Money $taxes;

    protected OrderItem $subscriptionOrderItem;

    public static function createFromMarketplacePriceTier(
        MarketplacePriceTier $priceTiers,
        Money $taxesAmount,
        MarketplaceOrderItem $marketplaceOrderItem,
        Money $priceWithPromotion = null
    ): self {
        $subscriptionOrderItem = new self();
        $subscriptionOrderItem->lowerLimit = $priceTiers->getLowerLimit() ?? 0;

        $priceTiersPrice = $priceWithPromotion ?? $priceTiers->getPrice();

        if (true === $marketplaceOrderItem->isTaxIncludedInPrice()) {
            $subscriptionOrderItem->includingTaxes = $priceTiersPrice;
            $subscriptionOrderItem->excludingTaxes = $priceTiersPrice->subtract($taxesAmount);
        } else {
            $subscriptionOrderItem->includingTaxes = $priceTiersPrice->add($taxesAmount);
            $subscriptionOrderItem->excludingTaxes = $priceTiersPrice;
        }
        $subscriptionOrderItem->taxes = $taxesAmount;

        return $subscriptionOrderItem;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function hasId(): bool
    {
        return \is_int($this->id) && $this->id > 0;
    }

    public function getLowerLimit(): int
    {
        return $this->lowerLimit;
    }

    public function getIncludingTaxes(): Money
    {
        return $this->includingTaxes;
    }

    public function getExcludingTaxes(): Money
    {
        return $this->excludingTaxes;
    }

    public function getTaxes(): Money
    {
        return $this->taxes;
    }

    public function setSubscriptionOrderItem(OrderItem $subscriptionOrderItem): self
    {
        $this->subscriptionOrderItem = $subscriptionOrderItem;
        return $this;
    }

    /** @return mixed[] */
    public function jsonSerialize(): array
    {
        return [
            'lowerLimit' => $this->getLowerLimit(),
            'includingTaxes' => (string) $this->getIncludingTaxes()->getConvertedAmount(),
            'excludingTaxes' => (string) $this->getExcludingTaxes()->getConvertedAmount(),
            'taxes' => (string) $this->getTaxes()->getConvertedAmount(),
        ];
    }
}
