<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Subscription\Event;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Wizacha\AppBundle\Security\User\UserService;
use Wizacha\Marketplace\Subscription\Log\SubscriptionActionTraceService;
use Wizacha\Marketplace\Subscription\Log\SubscriptionEventType;
use Wizacha\Marketplace\User\UserRepository;

class SubscriptionEventSubscriber implements EventSubscriberInterface
{
    protected SubscriptionActionTraceService $subscriptionActionTraceService;
    protected UserService $userService;
    protected UserRepository $userRepository;

    public function __construct(
        SubscriptionActionTraceService $subscriptionActionTraceService,
        UserService $userService,
        UserRepository $userRepository
    ) {
        $this->subscriptionActionTraceService = $subscriptionActionTraceService;
        $this->userService = $userService;
        $this->userRepository = $userRepository;
    }

    /** @return string[] */
    public static function getSubscribedEvents(): array
    {
        return [
            SubscriptionEventType::SUBSCRIPTION_CREATED()->getValue()  => ['onCreateSubscriptionEvent', 0],
            SubscriptionEventType::STATUS_UPDATED()->getValue()  => ['onSubscriptionStatusUpdatedEvent', 0],
            SubscriptionEventType::AUTO_RENEW_UPDATED()->getValue()  => ['onSubscriptionAutoRenewUpdatedEvent', 0],
            SubscriptionEventType::PAYMENT_METHOD_RENEWED()->getValue()  => ['onPaymentMethodRenewedEvent', 0],
        ];
    }

    public function onCreateSubscriptionEvent(SubscriptionStatusEvent $event): void
    {
        $this->subscriptionActionTraceService->create(
            $event->getSubscription(),
            null,
            null,
            SubscriptionEventType::SUBSCRIPTION_CREATED()->getValue(),
            $event->getUser()
        );
    }

    public function onSubscriptionStatusUpdatedEvent(SubscriptionStatusEvent $event): void
    {
        $this->subscriptionActionTraceService->create(
            $event->getSubscription(),
            $event->getOldSubscriptionStatus()->getValue(),
            $event->getNewSubscriptionStatus()->getValue(),
            SubscriptionEventType::STATUS_UPDATED()->getValue(),
            $event->getUser()
        );
    }

    public function onSubscriptionAutoRenewUpdatedEvent(SubscriptionAutoRenewEvent $event): void
    {
        $this->subscriptionActionTraceService->create(
            $event->getSubscription(),
            \ucfirst(
                \var_export(
                    $event->getOldSubscriptionAutoRenew(),
                    true
                )
            ),
            \ucfirst(
                \var_export(
                    $event->getNewSubscriptionAutoRenew(),
                    true
                )
            ),
            SubscriptionEventType::AUTO_RENEW_UPDATED()->getValue(),
            $event->getUser()
        );
    }

    public function onPaymentMethodRenewedEvent(PaymentMethodRenewedEvent $event): void
    {
        $loggedUser = $event->getLoggedUser();
        $user = $this->userRepository->get($loggedUser);

        $this->subscriptionActionTraceService->create(
            $event->getSubscription(),
            $event->getOldPan(),
            $event->getNewPan(),
            SubscriptionEventType::PAYMENT_METHOD_RENEWED()->getValue(),
            $user
        );
    }
}
