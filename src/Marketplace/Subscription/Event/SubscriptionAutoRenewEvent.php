<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Subscription\Event;

use Symfony\Component\EventDispatcher\Event;
use Wizacha\Marketplace\Subscription\Subscription;
use Wizacha\Marketplace\User\User;

final class SubscriptionAutoRenewEvent extends Event
{
    protected Subscription $subscription;
    protected User $user;
    protected bool $oldSubscriptionAutoRenew;
    protected bool $newSubscriptionAutoRenew;

    public function __construct(
        Subscription $subscription,
        User $user,
        bool $oldSubscriptionAutoRenew,
        bool $newSubscriptionAutoRenew
    ) {
        $this->subscription = $subscription;
        $this->user = $user;
        $this->oldSubscriptionAutoRenew = $oldSubscriptionAutoRenew;
        $this->newSubscriptionAutoRenew = $newSubscriptionAutoRenew;
    }

    public function getSubscription(): Subscription
    {
        return $this->subscription;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function getOldSubscriptionAutoRenew(): bool
    {
        return $this->oldSubscriptionAutoRenew;
    }

    public function getNewSubscriptionAutoRenew(): bool
    {
        return $this->newSubscriptionAutoRenew;
    }
}
