<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Subscription\Event;

use Symfony\Component\EventDispatcher\Event;
use Wizacha\Marketplace\Subscription\Subscription;

final class PaymentMethodRenewedEvent extends Event
{
    protected Subscription $subscription;
    protected string $oldPan;
    protected string $newPan;
    protected int $loggedUser;

    public function __construct(
        Subscription $subscription,
        string $oldPan,
        string $newPan,
        int $loggedUser
    ) {
        $this->subscription = $subscription;
        $this->oldPan = $oldPan;
        $this->newPan = $newPan;
        $this->loggedUser = $loggedUser;
    }

    public function getSubscription(): Subscription
    {
        return $this->subscription;
    }

    public function getOldPan(): string
    {
        return $this->oldPan;
    }

    public function getNewPan(): string
    {
        return $this->newPan;
    }

    public function getLoggedUser(): int
    {
        return $this->loggedUser;
    }
}
