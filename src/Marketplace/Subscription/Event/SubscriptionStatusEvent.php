<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Subscription\Event;

use Symfony\Component\EventDispatcher\Event;
use Wizacha\Marketplace\Subscription\Subscription;
use Wizacha\Marketplace\Subscription\SubscriptionStatus;
use Wizacha\Marketplace\User\User;

final class SubscriptionStatusEvent extends Event
{
    protected Subscription $subscription;
    protected ?User $user;
    protected ?SubscriptionStatus $oldSubscriptionStatus;
    protected ?SubscriptionStatus $newSubscriptionStatus;

    public function __construct(
        Subscription $subscription,
        ?User $user,
        ?SubscriptionStatus $oldSubscriptionStatus,
        ?SubscriptionStatus $newSubscriptionStatus
    ) {
        $this->subscription = $subscription;
        $this->user = $user;
        $this->oldSubscriptionStatus = $oldSubscriptionStatus;
        $this->newSubscriptionStatus = $newSubscriptionStatus;
    }

    public function getSubscription(): Subscription
    {
        return $this->subscription;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function getOldSubscriptionStatus(): ?SubscriptionStatus
    {
        return $this->oldSubscriptionStatus;
    }

    public function getNewSubscriptionStatus(): ?SubscriptionStatus
    {
        return $this->newSubscriptionStatus;
    }
}
