<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Subscription;

use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Wizacha\Marketplace\Basket\CheckoutResult;
use Wizacha\Marketplace\Basket\Exception\UnavailablePaymentException;
use Wizacha\Marketplace\CreditCard\CreditCard;
use Wizacha\Marketplace\Date\DateService;
use Wizacha\Marketplace\Entities\Declination;
use Wizacha\Marketplace\Order\Action\MarkPaymentAsRefused;
use Wizacha\Marketplace\Order\CreateOrder;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderItem as MarketplaceOrderItem;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\Payment\PaymentProcessorIdentifier;
use Wizacha\Marketplace\PIM\Option\Handler\CommitmentPeriodHandler;
use Wizacha\Marketplace\PIM\Option\Handler\PaymentFrequencyHandler;
use Wizacha\Marketplace\PIM\Option\SystemOptionsRegistry;
use Wizacha\Marketplace\PIM\Product\ProductService;
use Wizacha\Marketplace\Price\PriceComposition;
use Wizacha\Marketplace\Price\PriceFields;
use Wizacha\Marketplace\PriceTier\Service\PriceTierService;
use Wizacha\Marketplace\Promotion\ProductPromotionApplier;
use Wizacha\Marketplace\Promotion\PromotionService;
use Wizacha\Marketplace\Subscription\Event\PaymentMethodRenewedEvent;
use Wizacha\Marketplace\Subscription\Event\SubscriptionStatusEvent;
use Wizacha\Marketplace\Subscription\Exception\AttemptLimitReachedException;
use Wizacha\Marketplace\Subscription\Exception\NoProductToRenewException;
use Wizacha\Marketplace\Subscription\Log\SubscriptionEventType;
use Wizacha\Marketplace\User\UserService;
use Wizacha\AppBundle\Security\User\UserService as SecurityUserService;
use Wizacha\Money\Money;

class SubscriptionService
{
    public const DATE_FORMAT = 'Y-m-d';
    protected UserService $userService;
    protected ProductService $productService;
    protected SubscriptionRepository $repository;
    protected CreateOrder $createOrder;
    protected bool $featureFlag;
    protected EntityManagerInterface $entityManager;
    protected DateService $dateService;
    protected OrderService $orderService;
    protected MarkPaymentAsRefused $markAsRefused;
    protected SystemOptionsRegistry $systemOptionRegistry;
    private PriceTierService $priceTierService;
    private ProductPromotionApplier $productPromotionApplier;
    private PromotionService $promotionService;
    protected EventDispatcherInterface $eventDispatcher;
    protected LoggerInterface $logger;
    protected SecurityUserService $securityUserService;
    private int $limitRenewAttempts;

    /** @var PaymentProcessorIdentifier[] */
    private array $configuredProcessors;

    public function __construct(
        UserService $userService,
        ProductService $productService,
        SubscriptionRepository $repository,
        EntityManagerInterface $entityManager,
        DateService $dateService,
        OrderService $orderService,
        MarkPaymentAsRefused $markAsRefused,
        SystemOptionsRegistry $systemOptionRegistry,
        PriceTierService $priceTierService,
        PromotionService $promotionService,
        ProductPromotionApplier $productPromotionApplier,
        EventDispatcherInterface $eventDispatcher,
        LoggerInterface $logger,
        SecurityUserService $securityUserService
    ) {
        $this->userService = $userService;
        $this->productService = $productService;
        $this->repository = $repository;
        $this->entityManager = $entityManager;
        $this->dateService = $dateService;
        $this->orderService = $orderService;
        $this->markAsRefused = $markAsRefused;
        $this->systemOptionRegistry = $systemOptionRegistry;
        $this->priceTierService = $priceTierService;
        $this->productPromotionApplier = $productPromotionApplier;
        $this->promotionService = $promotionService;
        $this->eventDispatcher = $eventDispatcher;
        $this->logger = $logger;
        $this->securityUserService = $securityUserService;
    }

    public function setFeatureFlag(bool $featureFlag, int $limitRenewAttempts = 1)
    {
        $this->featureFlag = $featureFlag;
        $this->limitRenewAttempts = $limitRenewAttempts;
    }

    /** @internal only call by symfony container to avoid ServiceCircularReferenceException */
    public function setCreateOrder(CreateOrder $createOrder): void
    {
        $this->createOrder = $createOrder;
    }

    /** @internal only call by symfony container to avoid ServiceCircularReferenceException */
    public function setConfiguredProcessors(array $configuredProcessors): void
    {
        $this->configuredProcessors = $configuredProcessors;
    }

    /**
     * @param Order[] $orders
     *
     * @return Subscription[]
     */
    public function createSubscription(array $orders): array
    {
        if (false === $this->featureFlag) {
            return [];
        }

        $subscriptions = [];

        if (\array_key_exists(0, $orders) && \is_string($orders[0]->getSubscriptionId())) {
            fn_set_subscription_id_on_order(
                $orders[0]->getSubscriptionId(),
                $orders[0]->getId()
            );

            return $subscriptions;
        }

        foreach ($orders as $order) {
            /** @var MarketplaceOrderItem $orderItem */
            foreach ($order->getItems() as $orderItem) {
                if ($orderItem->isSubscription() === true) {
                    $commitmentPeriod = null;
                    $paymentFrequency = null;
                    $totalPrice = $orderItem->getPrice()->multiply((float) $orderItem->getAmount());
                    $productInventoryCode = [];

                    foreach ($orderItem->getDeclinationOptions() as $option) {
                        $productInventoryCode[] = $option['optionId'] . '_' . $option['variantId'];

                        if (false === \is_null($option['code'])) {
                            $handler = $this->systemOptionRegistry->getHandler($option['code']);

                            if (false === \is_null($handler)) {
                                if (CommitmentPeriodHandler::getSystemOptionCode() === $handler->getSystemOptionCode()) {
                                    $commitmentPeriod = (int) $option['variantName'];
                                }

                                if (PaymentFrequencyHandler::getSystemOptionCode() === $handler->getSystemOptionCode()) {
                                    $paymentFrequency = (int) $option['variantName'];
                                }
                            }
                        }
                    }

                    if (\is_null($commitmentPeriod) || \is_null($paymentFrequency)) {
                        throw new \Exception('Subscription must have system options to be created.');
                    }

                    $nextPaymentDate = $this->dateService->addMonths($paymentFrequency);
                    $commitmentEndDate = (new \DateTimeImmutable())->modify('+' . $paymentFrequency * $commitmentPeriod . ' month');

                    $subscription = (new Subscription())
                        ->setUser($order->getUser())
                        ->setFirstOrderId($order->getId())
                        ->setCompanyId($order->getCompanyId())
                        ->setName($orderItem->getProductName())
                        ->setStatus(SubscriptionStatus::DISABLED())
                        ->setPrice($totalPrice)
                        ->setIsAutoRenew($orderItem->isRenewable())
                        ->setCommitmentPeriod($commitmentPeriod)
                        ->setPaymentFrequency($paymentFrequency)
                        ->setNextPaymentAt($nextPaymentDate)
                        ->setCommitmentEndAt($commitmentEndDate)
                    ;

                    $orderItemSubscription = (new OrderItem())
                        ->setCategoryId($this->productService->get($orderItem->getProductId())->getCategory()->getId())
                        ->setProductId($orderItem->getProductId())
                        ->setProductCode($orderItem->getProductCode())
                        ->setProductName($orderItem->getProductName())
                        ->setProductIsRenewable($orderItem->isRenewable())
                        ->setProductOptionInventoryCode(implode('_', $productInventoryCode))
                        ->setQuantity($orderItem->getAmount())
                        ->setUnitPrice($orderItem->getPrice())
                        ->setTotalPrice($totalPrice)
                        ->setPriceIncludeTaxes($orderItem->isTaxIncludedInPrice())
                    ;


                    $orderItemId = $orderItem->getItemId();
                    foreach ($orderItem->filterTaxes('P_' . $orderItemId) as $taxId => $tax) {
                        $orderItemSubscription->addTaxItem(
                            (new TaxItem())
                                ->setOrderItem($orderItemSubscription)
                                ->setTaxId((int) $taxId)
                                ->setRate((float) $tax['rate_value'])
                                ->setName($tax['description'])
                                ->setAmount(
                                    Money::fromVariable($tax['applies']['P_' . $orderItem->getItemId()])
                                        ->divide($orderItemSubscription->getQuantity())
                                )
                        );
                    }

                    $subscriptionTaxItems = $orderItemSubscription->getTaxItems()->toArray();
                    $priceTiers = $this->priceTierService->getPriceTiersOfProductOrCombination($orderItem->getProductId(), $orderItem->getCombination());
                    // if there is only one priceTier (price : 0 -> infinite), no need to store it
                    if (\count($priceTiers) > 1) {
                        $this->repository->save($subscription);

                        $declination = new Declination(
                            new SubscriptionProduct($orderItemSubscription, $subscription)
                        );
                        $promotions = $this->promotionService->getPromotionsCurrentlyApplyingToDeclination($declination);
                        $productPromotionApplier = $this->productPromotionApplier;
                        $priceTiers = array_map(
                            static function ($priceTier) use ($subscriptionTaxItems, $orderItemSubscription, $orderItem, $promotions, $productPromotionApplier) {
                                $bestPriceCompositionWithPromotion = (new PriceComposition())->set(PriceFields::BASE_PRICE(), $priceTier->getPrice());
                                $basePrice = $bestPriceCompositionWithPromotion;
                                foreach ($promotions as $promotion) {
                                    $tempPriceComposition = clone $basePrice;
                                    $tempPriceComposition = $productPromotionApplier->apply($promotion, $tempPriceComposition);

                                    // Keep the most favorable promotion
                                    if ($bestPriceCompositionWithPromotion->get(PriceFields::BASE_PRICE()) > $tempPriceComposition->get(PriceFields::BASE_PRICE())) {
                                        $bestPriceCompositionWithPromotion = $tempPriceComposition;
                                    }
                                }

                                return PriceTier::createFromMarketplacePriceTier(
                                    $priceTier,
                                    TaxItem::applyTaxesOnMoney($priceTier->getPrice(), $subscriptionTaxItems, $orderItemSubscription->getPriceIncludeTaxes()),
                                    $orderItem,
                                    $bestPriceCompositionWithPromotion->get(PriceFields::BASE_PRICE())
                                )->setSubscriptionOrderItem($orderItemSubscription);
                            },
                            $priceTiers
                        );
                        $orderItemSubscription->setPriceTiers($priceTiers);
                    }

                    $subscription->addOrderItem($orderItemSubscription);

                    $this->repository->save($subscription);

                    // Create Subscription Action Trace
                    $this->eventDispatcher->dispatch(
                        new SubscriptionStatusEvent($subscription, null, null, null),
                        SubscriptionEventType::SUBSCRIPTION_CREATED()->getValue()
                    );
                    $subscriptions[] = $subscription;
                }
            }
        }

        return $subscriptions;
    }

    /** @param Order[] $orders */
    public function updateStatus(array $orders, SubscriptionStatus $status): self
    {
        if (false === $this->hasFeatureFlag()) {
            return $this;
        }

        $orders = \array_filter(
            $orders,
            function (Order $order): bool {
                return $order->isSubscription();
            }
        );

        if (0 === \count($orders)) {
            return $this;
        }

        $subscriptions = $this->repository->findByFirstOrderIds($this->getOrdersIds($orders));

        foreach ($subscriptions as $subscription) {
            $this->updateSubscriptionStatus($subscription, $status);
        }

        return $this;
    }

    /**
     * @param Order[] $orders
     *
     * @return Subscription[]
     */
    public function addCreditCard(array $orders, CreditCard $creditCard): array
    {
        $subscriptions = $this->repository->findByFirstOrderIds($this->getOrdersIds($orders));

        foreach ($subscriptions as $subscription) {
            $subscription->setCreditCard($creditCard);
        }

        $this->entityManager->flush();

        return $subscriptions;
    }

    /** Renew a subscription from a product who does not exist from the product catalog */
    public function renew(Subscription $subscription, $force = false): CheckoutResult
    {
        $today = (new \DateTimeImmutable())->setTime(0, 0, 0);
        $basketId = $this->createOrder->createBasket();

        foreach ($subscription->getOrderItems() as $orderItem) {
            if (true === $subscription->isAutoRenew()
                && $today > $subscription->getCommitmentEndAt()
                && false === $orderItem->getProductIsRenewable()
            ) {
                continue;
            }

            $this->createOrder->addProduct(
                $basketId,
                new Declination(
                    new SubscriptionProduct($orderItem, $subscription)
                ),
                $orderItem->getQuantity()
            );
        }

        if (0 === $this->createOrder->countProducts($basketId)) {
            throw new NoProductToRenewException(
                "There is no product to renew for subscription '" . $subscription->getName() . "' ('" . $subscription->getId() . "')."
            );
        }


        if ($today->format('d/m/Y') === $subscription->getNextPaymentAt()->format('d/m/Y')) {
            $subscription->setRenewAttemptsCount(1);
            $subscription->setNextPaymentAt(
                $this->dateService
                    ->createFromImmutable($subscription->getNextPaymentAt())
                    ->addMonths($subscription->getPaymentFrequency())
            );
        } elseif (true === $force ||  0 === $this->limitRenewAttempts || $subscription->getRenewAttemptsCount() < $this->limitRenewAttempts) {
            $subscription->incrementRenewAttemptsCount();
        } else {
            throw new AttemptLimitReachedException($subscription, $this->limitRenewAttempts);
        }

        $this->updateSubscriptionStatus($subscription, SubscriptionStatus::WAITING_RENEW());

        $this->repository->save($subscription);

        // Today we are assuming that there is only one configured processor.
        // Tomorrow you will have to save and recover the processor via the subscription entity.
        $processor = $this->configuredProcessors[0];

        return $this->createOrder->checkout($basketId, $processor, $subscription->getUser());
    }

    public function get(string $id): ?Subscription
    {
        return $this->repository->findOneById($id);
    }

    public function replaceCreditCard(CreditCard $creditCard, int $loggedUser): self
    {
        foreach ($this->repository->getActiveOrDefaulted($creditCard->getUser()) as $subscription) {
            try {
                if ($subscription->getCreditCard() === null) {
                    continue;
                }
                $oldPan = $subscription->getCreditCard()->getPan();
                // Check Credit card and set it's value
                $newSubscription = $subscription->setCreditCard($creditCard);
                $this->repository->save($newSubscription);

                $this->eventDispatcher->dispatch(
                    new PaymentMethodRenewedEvent($subscription, $oldPan, $creditCard->getPan(), $loggedUser),
                    SubscriptionEventType::PAYMENT_METHOD_RENEWED()->getValue()
                );
            } catch (Exception\WrongCreditCardUserException $e) {
                $this->logger->error("[Replace CreditCard]", [
                    "exception" => $e->getMessage()
                ]);

                continue;
            }
        }

        return $this;
    }

    public function hasFeatureFlag(): bool
    {
        return $this->featureFlag;
    }

    /** @param Order[] $orders */
    public function updateUnpaidSubscription(array $orders): SubscriptionStatus
    {
        $status = SubscriptionStatus::DISABLED();

        foreach ($orders as $childOrder) {
            if (true === $childOrder->isSubscription() && false === $childOrder->isSubscriptionInitiator()) {
                if ($this->markAsRefused->isAllowed($childOrder)) {
                    $this->markAsRefused->execute($childOrder);
                }

                fn_change_order_status($childOrder->getId(), OrderStatus::BILLING_FAILED);

                $status = SubscriptionStatus::DEFAULTED();
            }
        }

        return $status;
    }

    /** @param Order[] $orders */
    public function getFirstOrders(array $orders): array
    {
        $firstOrders = [];
        foreach ($orders as $order) {
            if (true === $order->isSubscription()) {
                if (\is_string($order->getSubscriptionId())) {
                    $firstOrders[] = $this
                        ->orderService
                        ->getOrder($this->get($order->getSubscriptionId())->getFirstOrderId());
                } else {
                    $firstOrders[] = $order;
                }
            }
        }

        return $firstOrders;
    }

    public function updateQuantity(Subscription $subscription, int $quantity): Subscription
    {
        $totalPrice = new Money(0);

        foreach ($subscription->getOrderItems() as $orderItem) {
            $orderItem->setQuantity($quantity);
            $orderItem->updateUnitPriceWithPriceTiers();
            $itemPrice = $orderItem->getUnitPrice()->multiply((float) $quantity);
            $orderItem->setTotalPrice($itemPrice);
            $totalPrice = $totalPrice->add($itemPrice);
        }

        return $subscription->setPrice($totalPrice);
    }

    /** @return array[] */
    public function checkNextPaymentAt(
        Subscription $subscription,
        string $nextPaymentAtString,
        string $format
    ): array {
        $errors = [];

        $errorStatus = $this->checkSubscriptionStatus($subscription, 'nextPaymentAt');

        $nextPaymentAt = \DateTimeImmutable::createFromFormat($format, $nextPaymentAtString);
        $commitmentEndAt = $subscription->getCommitmentEndAt();

        $errorDateFormat = $this->checkDateFormat($nextPaymentAt, $nextPaymentAtString, $format);

        if ($nextPaymentAt instanceof \DateTimeImmutable
            && $nextPaymentAt->format($format) === $subscription->getNextPaymentAt()->format($format)
        ) {
            return [];
        }

        if ($nextPaymentAt instanceof \DateTimeImmutable
            && ((true === $commitmentEndAt instanceof \DateTimeImmutable
            && $nextPaymentAt > $commitmentEndAt
            && false === $subscription->isAutoRenew())
            || $nextPaymentAt < \DateTimeImmutable::createFromFormat(
                $format,
                (new \DateTime('now'))->format($format)
            ))
        ) {
            $errors[] = [
                'message' => \sprintf(
                    'Invalid date (%s). The date must be inferior or equals to the commitment end date (%s) and superior or equals to today (%s).',
                    $nextPaymentAt->format($format),
                    $commitmentEndAt->format($format),
                    (new \DateTime('now'))->format($format)
                )
            ];
        }

        return \array_merge($errors, $errorStatus, $errorDateFormat);
    }

    /** @return array[] */
    public function checkCommitmentEndAt(
        Subscription $subscription,
        string $commitmentEndAtString,
        string $format
    ): array {
        $errors = [];

        $errorStatus = $this->checkSubscriptionStatus($subscription, 'commitmentEndAt');

        $commitmentEndAt = \DateTimeImmutable::createFromFormat($format, $commitmentEndAtString);

        $errorDateFormat = $this->checkDateFormat($commitmentEndAt, $commitmentEndAtString, $format);

        if ($commitmentEndAt instanceof \DateTimeImmutable
            && $commitmentEndAt->format($format) === $subscription->getCommitmentEndAt()->format($format)
        ) {
            return [];
        }

        if ($commitmentEndAt instanceof \DateTimeImmutable
            && $commitmentEndAt < \DateTimeImmutable::createFromFormat(
                $format,
                (new \DateTime('now')
                )->format($format)
            )
        ) {
            $errors[] = [
                'message' => \sprintf(
                    'Invalid date (%s). The date must be superior or equals to today (%s).',
                    $commitmentEndAt->format($format),
                    (new \DateTime('now'))->format($format)
                )
            ];
        }

        return \array_merge($errors, $errorStatus, $errorDateFormat);
    }

    /** @return Subscription[] */
    public function getByFirstOrderId(int $orderId): array
    {
        return $this->repository->findByFirstOrderId($orderId);
    }

    /** @return array[] */
    public function checkQuantity(Request $request, Subscription $subscription): array
    {
        $quantity = $request->request->get('quantity');
        $errors = [];

        if (false === \is_int($quantity)) {
            $errors[] = ['message' => 'Parameter quantity must be an int.'];
        }

        if (0 >= $quantity) {
            $errors[] = ['message' => 'Parameter quantity must be superior to 0.'];
        }

        $subscriptionStatus = $subscription->getStatus()->getValue();
        if (SubscriptionStatus::ACTIVE()->getValue() !== $subscriptionStatus
            && SubscriptionStatus::WAITING_RENEW()->getValue() !== $subscriptionStatus
        ) {
            $errors[] = ['message' => 'The quantity can only be updated on an active or on renew subscription.'];
        }

        return $errors;
    }

    /**
     * @param Order[] $orders
     *
     * @return int[]
     */
    protected function getOrdersIds(array $orders): array
    {
        return array_map(
            function (Order $order): int {
                return $order->getId();
            },
            $orders
        );
    }

    public function updateSubscriptions(?OutputInterface $output): int
    {
        // On désactive toutes les subscriptions qui ont la date de fin d'engagement inférieure à aujourd'hui
        // et qui ne sont pas en renouvellement auto
        foreach ($this->repository->getNonAutoRenewable() as $subscription) {
            $this->updateSubscriptionStatus($subscription, SubscriptionStatus::FINISHED());
        }

        // On récupère toutes les subscriptions qui doivent être renouvellées
        foreach ($this->repository->getAutoRenewable() as $subscription) {
            try {
                //log subscription before renew Action
                $this->logger->notice('renew-subscription', [
                    'subscriptionId' => $subscription->getId(),
                    'ActionType' => 'before-renew-action',
                    'subscriptionStatus' => $subscription->getStatus()->getValue()
                ]);
                $result = $this->renew($subscription);
            } catch (NoProductToRenewException $exception) {
                $this->updateSubscriptionStatus($subscription, SubscriptionStatus::FINISHED());
                if (null !== $output) {
                    $output->writeln('<comment>' . $exception->getMessage() . '</comment>');
                }

                continue;
            } catch (UnavailablePaymentException $exception) {
                $message = 'Unable to renew subscription "' . $subscription->getName() . '" (' . $subscription->getId() . ').';

                if (null !== $output) {
                    $output->writeln('<error>' . $message . '</error>');
                }
                $this->logger->error($message, [
                    'exception' => $exception,
                    'basketId' => $exception->getBasketId(),
                    'orderId' => $exception->getOrderId(),
                ]);

                $error = 1;
                continue;
            } catch (AttemptLimitReachedException $exception) {
                $message = 'No renew, max attempt reached for subscription "' . $subscription->getName() . '" (' . $subscription->getId() . ').';

                if (null !== $output) {
                    $output->writeln('<error>' . $message . '</error>');
                }
                continue;
            } catch (\Throwable $exception) {
                $message = 'Unable to renew subscription "' . $subscription->getName() . '" (' . $subscription->getId() . ').';

                if (null !== $output) {
                    $output->writeln('<error>' . $message . '</error>');
                }
                $this->logger->error($message, [
                    'exception' => $exception,
                ]);

                $error = 1;
                continue;
            }

            $ordersId = implode(
                ', ',
                array_map(
                    function (Order $order): int {
                        return $order->getId();
                    },
                    $result->getOrders()
                )
            );

            if (null !== $output) {
                $output->writeln('<info>Subscription "' . $subscription->getName() . '" (' . $subscription->getId() . ') has been renewed successfully with order IDs "' . $ordersId . '".</info>');
            }

            $this->repository->save($subscription);
        }
        return $error ?? 0;
    }

    public function updateAutoSubscriptionStatusFromPsp(Subscription $subscription, SubscriptionStatus $status)
    {
        if ($subscription->getStatus()->equals(SubscriptionStatus::FINISHED()) === true
            || $subscription->getStatus()->equals(SubscriptionStatus::SUSPENDED()) === true
        ) {
            return;
        }

        $this->updateSubscriptionStatus($subscription, $status);
    }

    public function updateSubscriptionStatus(Subscription $subscription, SubscriptionStatus $status): Subscription
    {
        $oldStatus = $subscription->getStatus();
        $subscription->setStatus($status);
        $subscription = $this->repository->save($subscription);

        if (false === $subscription->getStatus()->equals($oldStatus)) {
            $loggedUserId = $this->securityUserService->getCurrentUserId();
            $loggedUser = null;
            if ($loggedUserId > 0) {
                $loggedUser = $this->userService->get($loggedUserId);
            }
            $this->eventDispatcher->dispatch(
                new SubscriptionStatusEvent(
                    $subscription,
                    $loggedUser,
                    $oldStatus,
                    $subscription->getStatus()
                ),
                SubscriptionEventType::STATUS_UPDATED()->getValue()
            );
        }

        return $subscription;
    }

    /** @return array[] */
    private function checkSubscriptionStatus(Subscription $subscription, string $parameter): array
    {
        if (false === $subscription->getStatus()->equals(SubscriptionStatus::ACTIVE())
            && false === $subscription->getStatus()->equals(SubscriptionStatus::DEFAULTED())
            && false === $subscription->getStatus()->equals(SubscriptionStatus::WAITING_RENEW())
        ) {
            return [
                ['message' => \sprintf(
                    'You are not allowed to update parameter %s if the subscription is not active.',
                    $parameter
                )
                ]
            ];
        }

        return [];
    }

    /** @return array[] */
    private function checkDateFormat($dateTimeImmutable, string $dateString, string $format): array
    {
        if (false === $dateTimeImmutable
            || $dateTimeImmutable->format($format) !== $dateString
        ) {
            return [['message' => 'Invalid date format. Use the format "YYYY-MM-DD".']];
        }

        return [];
    }
}
