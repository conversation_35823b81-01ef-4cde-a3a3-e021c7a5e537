<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Country\Exception;

use Symfony\Component\HttpFoundation\Response;
use Throwable;
use Wizacha\Marketplace\Exception\NotFound;

class CountryNotFound extends NotFound
{
    public function __construct($message = "Country not found", $code = 404, Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }

    public static function fromCode(string $code): self
    {
        return new self(sprintf("No country code found with this code %s", $code), Response::HTTP_NOT_FOUND);
    }
}
