<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Country;

use Wizacha\Marketplace\GlobalState\GlobalState;

class Country
{
    protected string $code;
    protected string $codeA3;
    protected string $codeN3;
    protected string $region;
    protected float $latitude;
    protected float $longitude;
    protected string $status;

    public function getCode(): string
    {
        return $this->code;
    }

    public function getCodeA3(): string
    {
        return $this->codeA3;
    }

    public function getCodeN3(): string
    {
        return $this->codeN3;
    }

    public function getRegion(): string
    {
        return $this->region;
    }

    public function getLatitude(): float
    {
        return $this->latitude;
    }

    public function getLongitude(): float
    {
        return $this->longitude;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function getDescription(string $langCode = null): ?string
    {
        if ($langCode === null) {
            $langCode = (string) GlobalState::interfaceLocale();
        }

        $countryDescription = container()->get('doctrine.orm.entity_manager')
            ->getRepository(CountryDescriptions::class)
            ->findOneBy(['code' => $this->code, 'langCode' => $langCode]);

        return $countryDescription instanceof CountryDescriptions === true
            ? $countryDescription->getCountry()
            : null;
    }

    public function __toString(): string
    {
        return $this->code;
    }
}
