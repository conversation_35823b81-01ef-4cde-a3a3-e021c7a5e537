<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Country;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Persistence\ManagerRegistry;
use Wizacha\Status;

class CountryRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry, $entityClass)
    {
        parent::__construct($registry, $entityClass);
    }

    public function findByCode(string $code): ?Country
    {
        return $this->getEntityManager()->find(Country::class, \strtoupper($code));
    }

    /** @return Country[] */
    public function getAllCountry(Status $status = null): array
    {
        $query = $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->select('country')
            ->from(Country::class, 'country')
            ->where("country.codeA3 != ''");

        if ($status !== null) {
            $query->andWhere('country.status = :status');
            $query->setParameters(['status' => $status->getValue()]);
        }

        return $query->getQuery()
            ->getResult();
    }

    /** @return Country[] */
    public function getCountryByListCodeA3(array $arrayCodeA3): array
    {
        $queryBuilder =  $this
            ->getEntityManager()
            ->createQueryBuilder();

        return $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->select('country')
            ->from(Country::class, 'country')
            ->where($queryBuilder->expr()->in('country.codeA3', ':arrayCodeA3'))
            ->setParameters(
                [
                    'arrayCodeA3' => $arrayCodeA3
                ]
            )
            ->getQuery()
            ->getResult();
    }
}
