<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Country;

use Doctrine\DBAL\Connection;
use Wizacha\Marketplace\Country\Exception\CountryNotFound;
use Wizacha\Marketplace\Country\Exception\InvalidNationalitiesException;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Status;

class CountryService
{
    private CountryRepository $countryRepository;

    protected Connection $connection;

    public function __construct(CountryRepository $countryRepository, Connection $connection)
    {
        $this->countryRepository = $countryRepository;
        $this->connection = $connection;
    }

    public function getByCode(string $code): Country
    {
        $country = $this->countryRepository->findByCode($code);

        if ($country instanceof Country === false) {
            throw CountryNotFound::fromCode($code);
        }

        return $country;
    }

    public function getCodeA3(string $code): string
    {
        $country = $this->countryRepository->findByCode($code);

        if ($country === null) {
            throw CountryNotFound::fromCode($code);
        }

        return $country->getCodeA3();
    }

    public function getCountryName(string $code, string $langCode): string
    {
        $country = $this->countryRepository->findByCode($code);

        if ($country === null) {
            throw CountryNotFound::fromCode($code);
        }

        $countryName = $country->getDescription($langCode);

        if ($countryName === null) {
            throw new \Exception("Country name not found with this code $code and $langCode.");
        }

        return $countryName;
    }

    public function isValidCountryCode(string $countryCode): bool
    {
        if (false === \is_string($countryCode)
            || 0 === \strlen($countryCode)
        ) {
            return false;
        }

        $country = $this->countryRepository->findByCode($countryCode);

        return $country !== null;
    }

    /**
     * @param string|null $langCode
     * @param Status|null $status
     *
     * @return null|array [string code, string country] ['code' => 'FR', 'country' => 'France']
     * @throws \Doctrine\DBAL\DBALException
     */
    public function getAll(string $langCode = null, Status $status = null): ?array
    {
        $countries = $this->countryRepository->getAllCountry($status);
        $langCode = $langCode ?? (string) GlobalState::interfaceLocale();

        return array_map(function (Country $country) use ($langCode): array {
            return ['code' => $country->getCode(), 'country' => $country->getDescription($langCode)];
        }, $countries);
    }

    /**
     * @param array $arrayCodeA3
     *
     * @return Country[]
     * @throws \Exception
     */
    public function assertValidArrayCodeA3(array $arrayCodeA3): array
    {
        $result = $this->countryRepository->getCountryByListCodeA3($arrayCodeA3);

        $arrayCodeA3Found = \array_map(static function ($value) {
            return  $value->getCodeA3();
        }, $result);

        $diff = \array_diff($arrayCodeA3, $arrayCodeA3Found);

        if (\count($diff) > 0) {
            throw new InvalidNationalitiesException('The nationalities [' . \implode(', ', $diff) . '] is invalid.');
        }

        return $result;
    }
}
