<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace;

/**
 * Contient les informations de l'entreprise qui opère la marketplace (i.e. notre client).
 */
class AdminCompany
{
    private $name;
    private $address;
    private $city;
    private $country;
    private $state;
    private $zipcode;
    private $phone;
    private $phone2;
    private $fax;
    private $website;
    private $startYear;
    private $usersDepartmentEmail;
    private $siteAdministratorEmail;
    private $ordersDepartmentEmail;
    private $supportDepartmentEmail;

    public function __construct()
    {
        $data = \Tygh\Registry::get('settings.Company');

        $this->name = $data['company_name'];
        $this->address = $data['company_address'];
        $this->city = $data['company_city'];
        $this->country = $data['company_country'];
        $this->state = $data['company_state'];
        $this->zipcode = $data['company_zipcode'];
        $this->phone = $data['company_phone'];
        $this->phone2 = $data['company_phone_2'];
        $this->fax = $data['company_fax'];
        $this->website = $data['company_website'];
        $this->startYear = $data['company_start_year'];
        $this->usersDepartmentEmail = $data['company_users_department'];
        $this->siteAdministratorEmail = $data['company_site_administrator'];
        $this->ordersDepartmentEmail = $data['company_orders_department'];
        $this->supportDepartmentEmail = $data['company_support_department'];
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getAddress(): string
    {
        return $this->address;
    }

    public function getCity(): string
    {
        return $this->city;
    }

    public function getCountry(): string
    {
        return $this->country;
    }

    public function getState(): string
    {
        return $this->state;
    }

    public function getZipcode(): string
    {
        return $this->zipcode;
    }

    public function getPhone(): string
    {
        return $this->phone;
    }

    public function getPhone2(): string
    {
        return $this->phone2;
    }

    public function getFax(): string
    {
        return $this->fax;
    }

    public function getWebsite(): string
    {
        return $this->website;
    }

    public function getStartYear(): string
    {
        return $this->startYear;
    }

    /**
     * Email address used to send emails to customers.
     */
    public function getUsersDepartmentEmail(): string
    {
        return $this->usersDepartmentEmail;
    }

    /**
     * Email address used to send emails to the administrators.
     */
    public function getSiteAdministratorEmail(): string
    {
        return $this->siteAdministratorEmail;
    }

    /**
     * Email address used to send emails to companies (vendors).
     */
    public function getOrdersDepartmentEmail(): string
    {
        return $this->ordersDepartmentEmail;
    }

    public function getSupportDepartmentEmail(): string
    {
        return $this->supportDepartmentEmail;
    }
}
