<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Entities;

use Wizacha\Product;

class DeclinationFactory
{
    public function makeDeclination(Product $product, string $combination = ''): Declination
    {
        return new Declination($product, $combination);
    }

    public function makeProduct(int $productId): Product
    {
        return new Product($productId, true);
    }
}
