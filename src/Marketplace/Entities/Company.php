<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Entities;

use Wizacha\Component\Locale\Locale;
use Wizacha\Marketplace\Company\CompanyStatus;
use Wizacha\Marketplace\Company\CompanyType;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Review\ReviewType;
use Wizacha\Status;
use Wizacha\User;

/**
 * @deprecated Use \Wizacha\Marketplace\Catalog\Company\CompanyService for the catalog view
 * @see \Wizacha\Marketplace\Catalog\Company\CompanyService
 */
class Company
{
    /**
     * @var integer
     */
    protected $id;

    protected $data = [];

    /** @var bool */
    protected $skipCache = false;


    public function __construct($id = 0)
    {
        if (!is_numeric($id)) {
            throw new \InvalidArgumentException('Integer expected');
        }
        $this->id = \intval($id);
    }

    /**
     * @param string $mangopayId
     */
    public function setMangopayId($mangopayId)
    {
        \Tygh\Database::query('UPDATE ?:companies SET mangopay_id=?s WHERE company_id=?i', $mangopayId, $this->id);
    }

    public function getMangopayId(): ?string
    {
        return \Tygh\Database::getField('SELECT mangopay_id FROM ?:companies WHERE company_id=?i', $this->id);
    }

    public function setHipayId(string $hipayId)
    {
        \Tygh\Database::query('UPDATE ?:companies SET hipay_id=?s WHERE company_id=?i', $hipayId, $this->id);
    }

    public function getHipayId(): ?string
    {
        return \Tygh\Database::getField('SELECT hipay_id FROM ?:companies WHERE company_id=?i', $this->id);
    }

    public function setStripeId(string $stripeId)
    {
        \Tygh\Database::query('UPDATE ?:companies SET stripe_id=?s WHERE company_id=?i', $stripeId, $this->id);
    }

    public function getStripeId(): ?string
    {
        return \Tygh\Database::getField('SELECT stripe_id FROM ?:companies WHERE company_id=?i', $this->id);
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getName()
    {
        return $this->fillData()['company'];
    }

    public function getCorporateName(): string
    {
        if (\array_key_exists('corporate_name', $this->fillData()) === true && \strlen($this->fillData()['corporate_name']) > 1) {
            return $this->fillData()['corporate_name'];
        }

        return $this->getName();
    }

    public function getAddress()
    {
        return $this->fillData()['address'];
    }

    public function getZipcode()
    {
        return $this->fillData()['zipcode'];
    }

    public function getCity()
    {
        return $this->fillData()['city'];
    }

    public function getCountry()
    {
        return $this->fillData()['country'];
    }

    public function getSiretNumber()
    {
        return $this->fillData()['w_siret_number'];
    }

    public function getRcsNumber()
    {
        return $this->fillData()['w_extras']['w_RCS'];
    }

    public function getVatNumber()
    {
        return $this->fillData()['w_vat_number'];
    }

    public function getPhone()
    {
        return $this->fillData()['phone'];
    }

    public function getIban(): ?string
    {
        return \Tygh\Database::getField('SELECT iban FROM cscart_companies WHERE company_id=?i;', $this->getId());
    }

    public function getBic(): ?string
    {
        return \Tygh\Database::getField('SELECT bic FROM cscart_companies WHERE company_id=?i;', $this->getId());
    }

    public function getMetaDescription()
    {
        return $this->fillData()['w_company_meta_description'];
    }

    public function getMetaKeywords()
    {
        return $this->fillData()['w_company_meta_keywords'];
    }

    public function getNafCode(): ?string
    {
        return $this->fillData()['naf_code'];
    }

    /**
     * @return string V or C
     */
    public function getType()
    {
        return $this->fillData()['w_company_type'];
    }

    public function getEmail()
    {
        if ($this->isPrivateIndividual() && \count($this->getAdmins()) > 0) {
            return $this->getFirstAdmin()->getEmail();
        }

        return $this->fillData()['email'];
    }

    public function getLocale(): Locale
    {
        $lang = $this->fillData()['lang_code'];

        if (\is_string($lang) && \strlen($lang) > 0) {
            return new Locale($lang);
        }

        if (\count($this->getAdmins()) > 0) {
            $locale = $this->getFirstAdmin()->getLocale();
        }

        return $locale ?? GlobalState::fallbackLocale();
    }

    public function isPrivateIndividual(): bool
    {
        return $this->getType() === (string) CompanyType::PRIVATE_INDIVIDUAL();
    }

    public function isProfessional(): bool
    {
        return $this->getType() === (string) CompanyType::PROFESSIONAL();
    }

    public function getDescription()
    {
        return $this->fillData()['company_description'];
    }

    public function getUrl()
    {
        return \trim($this->fillData()['url']);
    }

    /**
     * @return User[]
     */
    public function getAdmins()
    {
        return array_map(
            function ($userId) {
                return new User($userId);
            },
            \Tygh\Database::getColumn('SELECT user_id FROM ?:users WHERE company_id=?i', $this->id)
        );
    }

    /**
     * @return User[]
     */
    public function getDisabledAdmins(): array
    {
        return array_map(
            function ($userId) {
                return new User($userId);
            },
            \Tygh\Database::getColumn('SELECT user_id FROM ?:users WHERE company_id = ?i AND status="D"', $this->id)
        );
    }

    /**
     * @throws NotFound This company has no admins.
     */
    public function getFirstAdmin(): User
    {
        $admins = $this->getAdmins();
        if (empty($admins)) {
            throw new NotFound('No admin found for company ' . $this->getId());
        }

        return reset($admins);
    }

    public function getLegalRepresentativeFirstname(): ?string
    {
        if ($this->isPrivateIndividual()) {
            return $this->getFirstAdmin()->getFirstname();
        }

        return $this->fillData()['legal_representative_firstname'];
    }

    public function getLegalRepresentativeLastname(): ?string
    {
        if ($this->isPrivateIndividual()) {
            return $this->getFirstAdmin()->getLastname();
        }

        return $this->fillData()['legal_representative_lastname'];
    }

    public function getLegalStatus(): string
    {
        return $this->fillData()['w_legal_status'];
    }

    public function getStatus(): string
    {
        return $this->fillData()['status'];
    }

    public function getCompanyStatus(): CompanyStatus
    {
        return new CompanyStatus($this->getStatus());
    }
    public function getSlug()
    {
        return $this->fillData()['seo_name'];
    }

    public function getTerms()
    {
        return $this->fillData()['company_terms'];
    }

    public function getLatitude()
    {
        return $this->fillData()['latitude'];
    }

    public function getLongitude()
    {
        return $this->fillData()['longitude'];
    }

    public function getThread(): Thread
    {
        $threadId = \Tygh\Database::getField('SELECT thread_id FROM ?:discussion WHERE object_id=?i AND object_type="M";', $this->getId());

        return new Thread($threadId ?: 0);
    }

    public function isEnabled(): bool
    {
        return \Tygh\Database::getField('SELECT status FROM ?:companies WHERE company_id=?i', $this->id) === Status::ENABLED;
    }

    /**
     * @return ThreadPost[]
     */
    public function getReviews(): array
    {
        $sql = 'SELECT dp.post_id FROM cscart_discussion_posts AS dp
                INNER JOIN cscart_discussion AS d ON d.thread_id = dp.thread_id AND d.type != ?s
                WHERE d.object_id=?i AND d.object_type = "M" AND dp.status = ?s';

        // Récupération des ids
        $results = \Tygh\Database::getColumn($sql, ReviewType::DISABLED, $this->getId(), Status::ENABLED);

        // Récupération des objets à partir des ids
        $threadPost = array_map(function ($threadId) {
            return new ThreadPost($threadId);
        }, $results);

        return $threadPost;
    }

    /**
     * Returns the average rating or null if the product has no rating.
     */
    public function getAverageRating(): ?float
    {
        $sql = 'SELECT AVG(rating_value)
                FROM ?:discussion_rating AS rating
                INNER JOIN ?:discussion_posts as post ON post.post_id = rating.post_id
                INNER JOIN ?:discussion as discussion ON discussion.thread_id = post.thread_id
                WHERE discussion.object_id = ?s AND discussion.object_type = "M" AND post.status = ?s';

        $rating = \Tygh\Database::getField($sql, $this->getId(), Status::ENABLED);

        return ($rating !== null) ? round((float) $rating) : null;
    }

    public function isSkipCache(): bool
    {
        return $this->skipCache;
    }

    public function setSkipCache(bool $skipCache): Company
    {
        $this->skipCache = $skipCache;

        return $this;
    }

    public function getData(): array
    {
        return $this->fillData();
    }

    protected function fillData(): array
    {
        if (empty($this->data)) {
            $this->data = fn_get_company_data($this->getId(), null, [
                'skip_cache' => $this->skipCache,
            ]);
        }
        if (!\is_array($this->data)) {
            throw new \Exception('Error while loading company data ' . $this->getId());
        }

        return $this->data;
    }
}
