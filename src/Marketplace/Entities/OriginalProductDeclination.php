<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Entities;

use Wizacha\Marketplace\Image\Image;
use Wizacha\Marketplace\PIM\TransactionMode\TransactionMode;
use Wizacha\Marketplace\Price\PriceComposition;
use Wizacha\Marketplace\Price\PriceFields;
use Wizacha\Money\Money;
use Wizacha\Product;

/**
 * @deprecated because it extends Declination.
 * Anyway, you should not use that class: it has been created to be able to remove a product
 * from the basket added before declinations was created. So we needed a declination object
 * to force the id to the original product declination.
 */
class OriginalProductDeclination extends Declination
{
    public function getId(): string
    {
        return $this->product->getId() . '_0';
    }
}
