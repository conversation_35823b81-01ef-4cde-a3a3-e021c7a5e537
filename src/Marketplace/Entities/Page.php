<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Entities;

/**
 * @deprecated Use Wizacha\Marketplace\Cms\Page instead
 * @see \Wizacha\Marketplace\Cms\Page
 */
class Page
{
    /**
     * @var int
     */
    protected $id;
    protected $cscartData;

    public function __construct($id)
    {
        $this->id = $id;
    }

    public function getId()
    {
        return $this->id;
    }

    public function getTitle()
    {
        return $this->fillCscartData()['page'];
    }

    public function getMetaTitle()
    {
        return $this->fillCscartData()['page_title'] ?: $this->getTitle();
    }

    public function getMetaKeywords()
    {
        return $this->fillCscartData()['meta_keywords'];
    }

    public function getType()
    {
        return $this->fillCscartData()['page_type'];
    }

    public function getWType()
    {
        return $this->fillCscartData()['w_type'];
    }

    public function getIdPath()
    {
        return $this->fillCscartData()['id_path'];
    }

    public function getDescription()
    {
        return $this->fillCscartData()['description'];
    }

    public function getMetaDescription()
    {
        return $this->fillCscartData()['meta_description'] ?: substr(strip_tags($this->getDescription()), 0, 150);
    }

    public function getUrl()
    {
        return fn_url('pages.view?page_id=' . $this->getId());
    }

    public function getStatus()
    {
        return $this->fillCscartData()['status'];
    }

    public function getFormData()
    {
        return $this->fillCscartData()['form'];
    }

    protected function fillCscartData()
    {
        if (!$this->cscartData) {
            $this->cscartData = fn_get_page_data($this->getId());
        }

        return $this->cscartData;
    }
}
