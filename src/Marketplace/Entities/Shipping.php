<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Entities;

use Wizacha\Marketplace\Exception\NotFound;

/**
 * @deprecated
 * @todo Create a new class to replace this one in the PIM module.
 */
class Shipping
{
    /**
     * @var integer
     */
    protected $id;

    protected $data = [];

    /**
     * @throws \InvalidArgumentException
     * @throws NotFound
     */
    public function __construct($id = 0)
    {
        if (!is_numeric($id)) {
            throw new \InvalidArgumentException('Integer expected');
        }
        $this->id = \intval($id);

        if (!$this->fillData()) {
            throw NotFound::fromId('Shipping', $this->id);
        }
    }

    public function getId()
    {
        return $this->id;
    }

    public function fillData()
    {
        if (empty($this->data)) {
            $this->data = fn_get_shipping_info($this->getId());
        }

        return $this->data;
    }

    public function getDeliveryType(): string
    {
        return $this->fillData()['w_delivery_type'];
    }

    /**
     * @return Image
     */
    public function getImage()
    {
        return new Image($this->fillData()['icon']['image_id'] ?: 0);
    }

    /**
     * @return string
     */
    public function getName()
    {
        return $this->fillData()['shipping'];
    }

    public function getDeliveryTime(): string
    {
        return (string) $this->fillData()['delivery_time'];
    }

    /** @return int */
    public function getPosition(): int
    {
        return $this->fillData()['position'];
    }
}
