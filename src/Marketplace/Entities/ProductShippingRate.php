<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Entities;

use Wizacha\Product;

/**
 * @deprecated
 * @todo Create a new class to replace this one in the PIM module.
 */
class ProductShippingRate
{

    /**
     * @var integer
     */
    protected $id;

    /**
     * @var Shipping
     */
    protected $shipping;

    /**
     * @var Product
     */
    protected $product;

    /** @var float */
    protected $carriagePaidThreshold = null;

    protected $data = [];

    /**
     * @param Shipping $shipping
     * @param Product $product
     */
    public function __construct(Shipping $shipping, Product $product)
    {
        $this->shipping = $shipping;
        $this->product = $product;
    }

    public function getImage()
    {
        return $this->shipping->getImage();
    }


    public function getName()
    {
        return $this->shipping->getName();
    }

    public function getShipping()
    {
        return $this->shipping;
    }

    public function fillData()
    {
        if (!$this->data) {
            $rateValue = db_get_memoized_field(
                'SELECT rate_value
              FROM ?:w_product_shipping_rates
              WHERE product_id=?i
                AND shipping_id=?i',
                $this->product->getId(),
                $this->shipping->getId()
            );
            if (!$rateValue) {
                // If product has no specific rates, we get them from the company rates table
                $rateValue = db_get_memoized_row(
                    'SELECT rate_value, carriage_paid_threshold
                  FROM ?:w_company_shipping_rates
                  WHERE company_id=?i
                    AND shipping_id=?i',
                    $this->product->getCompany()->getId(),
                    $this->shipping->getId()
                );

                $this->carriagePaidThreshold = $rateValue['carriage_paid_threshold'];
                $rateValue = $rateValue['rate_value'];
            } else {
                // If product has specific rates, we get the free shipping threshold from the company rates table
                $carriagePaidThresholdReq = db_get_memoized_row(
                    'SELECT carriage_paid_threshold
                  FROM ?:w_company_shipping_rates
                  WHERE company_id=?i
                    AND shipping_id=?i',
                    $this->product->getCompany()->getId(),
                    $this->shipping->getId()
                );

                $this->carriagePaidThreshold = $carriagePaidThresholdReq['carriage_paid_threshold'];
            }
            $this->data = unserialize($rateValue)['I'];
        }

        return $this->data;
    }

    public function getRateForFirst(): float
    {
        return \floatval($this->fillData()[0]['value']);
    }

    public function getLocation(): ?string
    {
        return ((string) $this->fillData()[0]['location']) ?? null;
    }

    public function getLatitude()
    {
        return isset($this->fillData()[0]['latitude']) ? $this->fillData()[0]['latitude'] : null;
    }

    public function getLongitude()
    {
        return isset($this->fillData()[0]['longitude']) ? $this->fillData()[0]['longitude'] : null;
    }

    public function getRateForNext(): float
    {
        return \floatval($this->fillData()[1]['value']);
    }

    public function getCarriagePaidThreshold(): ?float
    {
        return $this->carriagePaidThreshold;
    }
}
