<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Entities;

/**
 * @deprecated Use \Wizacha\Marketplace\Image\Image and view helpers.
 * @see \Wizacha\Marketplace\Image\Image
 */
class Image
{
    /**
     * @var integer
     */
    protected $id;

    /**
     * @var array
     */
    protected $data;

    public function __construct($id = 0)
    {
        if (!is_numeric($id)) {
            throw new \InvalidArgumentException('Integer expected');
        }
        $this->id = \intval($id);
    }

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getPath()
    {
        return $this->fillData()['image_path'];
    }

    /**
     * @return array
     */
    protected function fillData()
    {
        if (!$this->data) {
            $this->data = \Tygh\Database::getRow("SELECT * FROM ?:images WHERE image_id=?i", $this->getId());
        }

        return $this->data;
    }
}
