<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Entities;

use Wizacha\User;

/**
 * @deprecated
 * @todo Create a new class to replace this one in the Review module.
 */
class ThreadPost
{
    /**
     *  @var int $postId
     */
    protected $id;

    /**
     * @var array
     */
    protected $data;


    public function __construct(int $id)
    {
        $this->id = $id;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getThreadId(): int
    {
        return $this->fillData()['thread_id'];
    }

    /**
     * @return string
     */
    public function getName()
    {
        return $this->fillData()['name'];
    }

    public function getDatetime(): \DateTime
    {
        return (new \DateTime())->setTimestamp($this->fillData()['timestamp']);
    }

    public function getUser(): User
    {
        return new User($this->fillData()['user_id']);
    }

    public function getUserId(): int
    {
        return $this->fillData()['user_id'];
    }

    /**
     * @return string
     */
    public function getIpAddress()
    {
        return $this->fillData()['ip_address'];
    }

    /**
     * @return string
     */
    public function getStatus()
    {
        return $this->fillData()['status'];
    }

    /**
     * @return string
     */
    public function getMessage()
    {
        return $this->fillData()['message'];
    }

    public function getRatingValue(): int
    {
        return (int) $this->fillData()['rating_value'];
    }

    /**
     * @return bool
     */
    public function isActive()
    {
        return $this->getStatus() == 'A';
    }

    /**
     * @return array
     */
    protected function fillData()
    {
        if (!$this->data) {
            $this->data = \Tygh\Database::getRow(
                'SELECT name, timestamp, user_id, ip_address, status, message, rating_value, dp.thread_id
             FROM ?:discussion_posts AS dp
                LEFT JOIN ?:discussion_messages AS dm
                  ON dp.post_id = dm.post_id
                LEFT JOIN ?:discussion_rating AS dr
                  ON dp.post_id = dr.post_id
              WHERE dp.post_id = ?i',
                $this->id
            );
        }

        return $this->data;
    }
}
