<?php

/**
* <AUTHOR> DevTeam <<EMAIL>>
* @copyright   Copyright (c) Wizacha
* @license     Proprietary
*/

namespace Wizacha\Marketplace\Entities;

use Wizacha\Marketplace\Review\ReviewType;

/**
 * @deprecated
 * @todo Create a new class to replace this one in the Review module.
 */
class Thread
{
    /**
     * @var int
     */
    protected $id;

    /**
     * @var ReviewType
     */
    protected $type;

    /**
     * @var array CustomerReviewPost
     */
    protected $posts = [];

    public function __construct(int $threadId)
    {
        $this->id = $threadId;
        $this->type = $this->loadReviewType();
    }

    /**
     * @return ThreadPost[]
     */
    public function getPosts()
    {
        if (!$this->posts) {
            $this->posts = array_map(
                function ($postId) {
                    return new ThreadPost($postId);
                },
                \Tygh\Database::getColumn('SELECT post_id FROM ?:discussion_posts WHERE thread_id=?i', $this->id)
            );
        }

        return $this->posts;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getReviewType(): ReviewType
    {
        return $this->type;
    }

    public function isEnabled(): bool
    {
        return !$this->getReviewType()->equals(ReviewType::DISABLED());
    }

    /**
     * @return array
     */
    public function getDiscussionRatings()
    {
        return fn_get_discussion_ratings();
    }

    protected function loadReviewType(): ReviewType
    {
        $type = \Tygh\Database::getField('SELECT type FROM cscart_discussion WHERE thread_id=?i', $this->getId());
        if (ReviewType::isValid($type)) {
            return new ReviewType($type);
        }

        return ReviewType::DISABLED();
    }
}
