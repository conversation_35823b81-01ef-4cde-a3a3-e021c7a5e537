<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Entities;

use Wizacha\Money\Money;
use Wizacha\Status;

/**
 * @deprecated
 * @todo Create a new class to replace this one in the PIM module.
 */
class Tax
{
    /**
     * @var int
     */
    protected $id;
    protected $cscartData;

    public function __construct($id)
    {
        $this->id = $id;
    }

    public function getId()
    {
        return $this->id;
    }

    public function getRate()
    {
        return $this->fillCscartData()['rate_value'];
    }

    public function isPriceIncluded(): bool
    {
        return $this->fillCscartData()['price_includes_tax'] == 'Y';
    }

    /**
     * @param string $status a constant from Wizacha\Status
     */
    public function hasStatus(string $status): bool
    {
        if (Status::isValid($status) === false) {
            throw new \InvalidArgumentException("Status $status is not a valid status.");
        }

        return $this->fillCscartData()['status'] === $status;
    }

    /**
     * @return array ['allTaxesPrice' => Money, 'tax' => Money]
     */
    public static function applyTaxes(Money $price, array $taxes)
    {
        if (empty($taxes)) {
            return ['allTaxesPrice' => $price, 'tax' => new Money(0)];
        }

        $allTaxesPrice = $price;
        $rate = 0;
        foreach ($taxes as $tax) {
            $rate += $tax->getRate();
        }

        // We do not support yet different values of "isPriceIncluded()"
        if (isset($tax) && $tax->isPriceIncluded()) {
            $taxValue = $price->multiply(1 - 1 / (1 + $rate / 100));
        } else {
            $taxValue = $price->multiply($rate / 100);
        }

        if (isset($tax) && !$tax->isPriceIncluded()) {
            $allTaxesPrice = $allTaxesPrice->add($taxValue);
        }

        return ['allTaxesPrice' => $allTaxesPrice, 'tax' => $taxValue];
    }

    protected function fillCscartData()
    {
        if (!$this->cscartData) {
            $this->cscartData = \Tygh\Database::getRow('SELECT * FROM ?:taxes LEFT JOIN ?:tax_rates ON ?:tax_rates.tax_id = ?:taxes.tax_id WHERE ?:taxes.tax_id=?i;', $this->id);
        }

        return $this->cscartData;
    }
}
