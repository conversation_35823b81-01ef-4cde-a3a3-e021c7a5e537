<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Entities;

use Wizacha\Marketplace\Image\Image;
use Wizacha\Marketplace\PIM\TransactionMode\TransactionMode;
use Wizacha\Marketplace\Price\PriceComposition;
use Wizacha\Marketplace\Price\PriceFields;
use Wizacha\Marketplace\PriceTier\PriceTier;
use Wizacha\Marketplace\Subscription\SubscriptionProduct;
use Wizacha\Marketplace\Traits\PriceTrait;
use Wizacha\Money\Money;
use Wizacha\Option;
use Wizacha\Product;
use Wizacha\ProductManager;
use Wizacha\Status;

/**
 * @deprecated Use Wizacha\Marketplace\Catalog\Declination\Declination for the catalog view
 * @see \Wizacha\Marketplace\Catalog\Declination\Declination
 */
class Declination
{
    use PriceTrait;

    /**
     * @var Product
     */
    protected $product;

    /**
     * @var string
     */
    protected $combination = '';

    /**
     * @var array Directly fill from database
     */
    protected $combinationData;

    /** @var ?array caching cscartData */
    protected $cscartData;

    /** @var ?array caching shippingRates */
    protected $productShippingsRates = null;

    /** @var ?array caching shippingRates */
    protected $productActiveShippingsRates = null;

    /**
     * @var PriceComposition|null
     */
    protected $priceCompositionCache;

    public function __construct(Product $product, $combination = '')
    {
        $this->product = $product;
        if (!$combination && $this->product->hasDeclinations()) {
            $combination = [];
            list($inventory) = $this->product->getSelfInventory();
            foreach (reset($inventory)['combination'] as $key => $value) {
                $combination[] = $key . '_' . $value;
            }
            $combination = implode('_', $combination);
        }
        $this->combination = $combination == '' ? '0' : $combination;
    }

    public function getId(): string
    {
        return $this->product->getId() . '_' . $this->combination;
    }

    /** @return SubscriptionProduct|Product */
    public function getProduct()
    {
        return $this->product;
    }

    public function getCompany(): Company
    {
        return $this->product->getCompany();
    }

    public function getProductId(): int
    {
        return $this->product->getId();
    }

    public function getProductWeight(): float
    {
        return $this->product->getWeight();
    }

    public function getCombinationCode(): string
    {
        return $this->combination;
    }

    public function parseCombinationCode(): array
    {
        if ('0' === $this->combination) {
            return [];
        }

        $combinations = \array_chunk(
            \explode(
                '_',
                $this->combination
            ),
            2
        );

        return
            \array_combine(
                \array_column($combinations, 0),
                \array_column($combinations, 1)
            )
            ?: []
        ;
    }

    public function getSupplierReference(): ?string
    {
        return $this->product->getSupplierRef();
    }

    public function getDivisions(): array
    {
        return $this->product->getDivisions();
    }

    /** @return string */
    public function getProductCode()
    {
        return $this->getDeclinationData()['product_code'] ?: $this->product->getProductCode();
    }

    /** @return string */
    public function getName()
    {
        return $this->product->getName();
    }

    /** @return string */
    public function getShortDescription()
    {
        return $this->product->getShortDescription();
    }

    /** @return string */
    public function getDescription()
    {
        return $this->product->getDescription();
    }

    /**
     * @param array|null $images Images of the Product
     * @return Image[] Images of the Declination
     */
    public function getImages(array $images = null)
    {
        if (\is_null($images)) {
            $images = $this->product->getImages();
        }
        $declinationId = \Tygh\Database::getField(
            "SELECT detailed_id
            FROM ?:images_links
            WHERE object_type='declinations'
              AND object_id=?s",
            $this->getId()
        );
        if ($declinationId) {
            array_unshift($images, new \Wizacha\Marketplace\Entities\Image($declinationId));
        }

        return $images;
    }

    public function getMainImage(): ?Image
    {
        $images = $this->getImages();
        if (empty($images)) {
            return null;
        }

        return new Image(reset($images)->getId());
    }

    /** @return Tax[] */
    public function getTaxes(): array
    {
        return $this->product->getTaxes(Status::ENABLED);
    }

    /**
     * Returns the base price of the declination.
     *
     * @return float
     */
    public function getPrice()
    {
        return $this->fillCscartData()['price'];
    }

    public function getPriceComposition(int $quantity = null): PriceComposition
    {
        $this->priceCompositionCache = new PriceComposition();
        // FIXME: here's a workaround to get a valid money (0) when fillCscartData returns false or base_price is null
        if ($quantity === null) {
            $this->priceCompositionCache->set(PriceFields::BASE_PRICE(), Money::fromVariable($this->getPrice() ?: 0));
        } else {
            $this->priceCompositionCache->set(PriceFields::BASE_PRICE(), Money::fromVariable($this->getOriginalPrice($quantity) ?: 0));
        }
        $this->priceCompositionCache->set(PriceFields::CROSSED_OUT_PRICE(), $this->getCrossedOutPrice() ?? new Money(0));

        $shippingPrices = array_map(
            function (ProductShippingRate $shippingValue) {
                return (new PriceComposition())
                    ->set(PriceFields::SHIPPING_FIRST_PRICE(), Money::fromVariable($shippingValue->getRateForFirst()))
                    ->set(PriceFields::SHIPPING_SECOND_PRICE(), Money::fromVariable($shippingValue->getRateForNext()))
                    ;
            },
            $this->getProductActiveShippingsRates()
        );
        $this->priceCompositionCache->set(PriceFields::SHIPPING(), $shippingPrices);

        $taxInfos = Tax::applyTaxes($this->priceCompositionCache->get(PriceFields::BASE_PRICE()), $this->getTaxes());
        $this->priceCompositionCache->set(PriceFields::PRODUCT_PRICE_WITH_TAX(), $taxInfos['allTaxesPrice']);
        $this->priceCompositionCache->set(PriceFields::PRODUCT_TAX(), $taxInfos['tax']);

        return $this->priceCompositionCache;
    }

    /**
     * @return mixed
     */
    public function getOriginalPrice(int $quantity = null)
    {
        if ($this->product instanceof SubscriptionProduct) {
            return $this->product->getOriginalPrice()->getConvertedAmount();
        }

        $productOptionInventoryId = \is_string($this->getCombinationCode()) && $this->getCombinationCode() !== '0' ? fn_inventoryId_from_string($this->getProductId(), $this->getCombinationCode()) : 0;

        if (\is_int($quantity)) {
            $priceTier = container()
                ->get("marketplace.price_tier.price_tier_repository")
                ->findByQuantityAndDeclination(
                    $this->getProductId(),
                    $productOptionInventoryId,
                    $quantity
                )
            ;

            if ($priceTier instanceof PriceTier) {
                return $priceTier->getPrice()->getConvertedAmount();
            }
        }

        return $this->fillCscartData()['base_price'];
    }

    public function getCrossedOutPrice(): ?Money
    {
        if ($this->combination) {
            if ($this->getDeclinationData()['crossed_out_price']) {
                $crossedOutPrice = Money::fromVariable($this->getDeclinationData()['crossed_out_price']);

                return $crossedOutPrice;
            }
        }

        return $this->product->getCrossedOutPrice();
    }

    /** @return float */
    public function getPriceWithShipping()
    {
        return $this->fillCscartData()['price'] + $this->getCheapestShipping();
    }

    public function getAmount(): int
    {
        if ($this->combination) {
            return \intval($this->getDeclinationData()['amount']);
        }

        return $this->product->getAmount();
    }

    public function getPosition(): int
    {
        if ($this->combination) {
            return \intval($this->getDeclinationData()['position']);
        }

        return 0;
    }

    public function getAvailabilityDate(): ?\DateTime
    {
        return $this->product->getAvailabilityDate();
    }

    /** @return array collection of productShippingRates */
    public function getProductShippingsRates()
    {
        $this->productShippingsRates = $this->productShippingsRates ?? $this->product->getProductShippingsRates();

        return $this->productShippingsRates;
    }

    /** @return ProductShippingRate[] */
    public function getProductActiveShippingsRates()
    {
        $this->productActiveShippingsRates = $this->productActiveShippingsRates ??  $this->product->getProductActiveShippingsRates();

        return $this->productActiveShippingsRates;
    }

    /**
     * Returns the following array:
     *
     * - float latitude
     * - float longitude
     * - string label
     * - string postal
     *
     * @return array
     */
    public function getGeoloc()
    {
        return $this->product->getGeoloc();
    }

    /** @return float */
    public function getCheapestShipping()
    {
        $shippings = $this->getProductActiveShippingsRates();
        $cheapest = empty($shippings) ? 0 : reset($shippings)->getRateForFirst();

        foreach ($shippings as $shipping) {
            if ($shipping->getRateForFirst() < $cheapest) {
                $cheapest = $shipping->getRateForFirst();
            }
        }

        return $cheapest;
    }

    /** @return string */
    public function getCondition()
    {
        return $this->product->getCondition();
    }

    /**
     * Permet de savoir si la déclinaison à des définitions : c'est donc une vraie déclinaison,
     * et non pas une déclinaison virtuelle créée uniquement pour gérer un product sans déclinaison.
     */
    public function hasDeclinationDefinition(): bool
    {
        return (bool) $this->combination;
    }

    /** @return array [[option_name = 'string', value = 'string'], ...] */
    public function getDeclinationDefinition()
    {
        if (!$this->hasDeclinationDefinition()) {
            return [];
        }

        //Parse combination to get [option_id => value_id,...]
        $parsedCombinations  = $this->parseCombinationCode();
        $return = [];
        foreach (fn_get_selected_product_options_info($parsedCombinations) as $option) {
            $return[$option['option_id']] = [
                'option_id'         => \intval($option['option_id']),
                'option_name'       => $option['option_name'],
                'option_position'   => \intval($option['option_position']),
                'value_id'          => \intval($option['value']),
                'value_name'        => $option['variant_name'],
                'image_id'          => $option['image_id'] ? (int) $option['image_id'] : null,
                'position'          => \intval($option['position']),
                'code'              => $option['code'],
            ];
        }

        return $return;
    }

    /** @return string */
    public function getUrl()
    {
        return $this->product->getFrontUrl();
    }

    /** @return string The brand name. */
    public function getBrand()
    {
        return $this->product->getBrand();
    }

    public function getGreenTax(): Money
    {
        return $this->product->getGreenTax();
    }

    public function getUpdatedTimestamp(): int
    {
        return $this->product->getUpdatedTimestamp();
    }

    public function getAffiliateLink(): ?string
    {
        $data = $this->getDeclinationData();

        return isset($data['affiliate_link']) ? $data['affiliate_link'] : $this->product->getAffiliateLink();
    }

    /**
     * @param string $declinationId
     *
     * @throws \Exception
     */
    public static function fromId($declinationId): self
    {
        $elements = static::parseId($declinationId);

        /** @var ProductManager */
        $productManager = container()->get('cscart.product_manager');

        return new self(
            $productManager->find($elements['product_id']),
            $elements['combination']
        );
    }

    /** Declination is active if base product is active and he got price and quantity */
    public function isActive(): bool
    {
        if ($this->product instanceof SubscriptionProduct) {
            return true;
        }

        if (!\Wizacha\Product::frontIds([$this->product->getId()])->valid()) {
            return false;
        }

        if ($this->combination) {
            return (
                ($this->getAmount() > 0 || $this->hasInfiniteStock() === true)
                && $this->isPriceValid($this->getPrice()) === true
            );
        }

        return true;
    }

    public function getStatus()
    {
        return $this->product->getStatus();
    }

    public function getTransactionMode(): TransactionMode
    {
        return $this->product->getTransactionMode();
    }

    public function hasInfiniteStock(): bool
    {
        return \boolval(
            $this->hasDeclinationDefinition()
                ? $this->getDeclinationData()['infinite_stock']
                : $this->product->hasInfiniteStock()
        );
    }

    public function hasMaxPriceAdjustment(): bool
    {
        return $this->product->hasMaxPriceAdjustment();
    }

    public function isAvailableToBasket(): bool
    {
        return true === $this->isActive()
            && 'D' !== $this->getStatus()
            && true === $this->isValidCombination()
            && true === $this->getTransactionMode()->equals(TransactionMode::TRANSACTIONAL())
            && false === $this->isQuoteExclusive();
    }

    public function isValidCombination(): bool
    {
        return Option::isCombinationValid(
            $this->parseCombinationCode(),
            Product::getExceptions($this->product->getId())
        );
    }

    public function isEdp(): bool
    {
        return $this->product->isEdp();
    }

    public function isQuoteExclusive(): bool
    {
        return $this->product->isQuoteExclusive();
    }

    /**
     * @param string $declinationId
     * @return array
     * @throws \Exception
     */
    protected static function parseId($declinationId)
    {
        if (!$declinationId) {
            if (\is_string($declinationId)) {
                throw new \Exception('Expect non empty string');
            }

            throw new \Exception(sprintf('Expect string, %s given', \gettype($declinationId)));
        }
        $elements = explode('_', $declinationId);
        $return['product_id'] = \intval(array_shift($elements));
        $return['combination'] = '0';
        if ($elements) {
            $return['combination'] = implode('_', $elements);
        }
        if ($return['combination'] == '') {
            $return['combination'] = '0';
        }

        return $return;
    }

    /** @return array */
    protected function getDeclinationData()
    {
        if (!$this->combinationData) {
            $this->combinationData = \Tygh\Database::getRow(
                'SELECT * FROM ?:product_options_inventory WHERE product_id=?i AND combination=?s',
                $this->product->getId(),
                $this->combination
            );
        }

        return $this->combinationData;
    }

    /**  @return array */
    protected function fillCscartData()
    {
        if (!$this->cscartData) {
            $this->cscartData = $this->product->getCscartData();
            if ($this->combination) {
                $this->cscartData['combination'] = $this->combination;
            }
            fn_gather_additional_product_data($this->cscartData);
        }

        return $this->cscartData;
    }
}
