<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\PIM\Product\Exim;

interface XmlConverterFormat
{
    public function convert(\SimpleXMLElement $productXml): array;
    public function supports(\XMLReader $rootXml): bool;

    /**
     * Returns the name of the XML tag that contains the product name.
     */
    public function getProductElementName(): string;
}
