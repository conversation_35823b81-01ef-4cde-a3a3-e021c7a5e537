<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\PIM\Product\Exim;

class CsvConverter
{
    /**
     * @var CsvConverterFormat[]
     */
    private $formats;

    /**
     * @param CsvConverterFormat[] $formats
     */
    public function __construct(array $formats)
    {
        $this->formats = $formats;
    }

    public function supports(string $uri): bool
    {
        return $this->findFormat($uri) !== null;
    }

    /**
     * @return null|resource
     */
    public function convertFile(string $uri)
    {
        if ($format = $this->findFormat($uri)) {
            $outFile = tmpfile();
            $fileStream = fopen($uri, 'r');
            $csvHeader = fgetcsv($fileStream);
            $isFirstLine = true;

            while ($inputData = fgetcsv($fileStream)) {
                $inputData = array_combine($csvHeader, $inputData);
                $productLine = $format->convert($inputData);
                if ($isFirstLine) {
                    // Write CSV header
                    fputcsv($outFile, array_keys($productLine), ';');
                    $isFirstLine = false;
                }
                fputcsv($outFile, $productLine, ';');
            }

            fclose($fileStream);
            rewind($outFile);

            return $outFile;
        }

        return null;
    }

    private function findFormat(string $uri): ?CsvConverterFormat
    {
        $fileStream = fopen($uri, 'r');
        $csvHeader = fgetcsv($fileStream);
        fclose($fileStream);

        foreach ($this->formats as $format) {
            if ($format->supports($csvHeader)) {
                return $format;
            }
        }

        return null;
    }
}
