<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\PIM\Product\Exim;

use Psr\Log\LoggerInterface;

class XmlConverter
{
    /**
     * @var XmlConverterFormat[]
     */
    private $formats;

    /**
     * @var LoggerInterface
     */
    private $logger;

    public function __construct(LoggerInterface $logger)
    {
        $this->registerFormats();
        $this->logger = $logger;
    }

    public function supports(string $uri): bool
    {
        $mime = finfo_file(finfo_open(FILEINFO_MIME_TYPE), $uri);

        return \in_array($mime, ['text/xml', 'application/xml']) && $this->findFormat($uri) !== null;
    }

    /**
     * @return null|resource
     */
    public function convertFile(string $uri)
    {
        if ($format = $this->findFormat($uri)) {
            $productElementName = $format->getProductElementName();

            $xml = new \XMLReader();
            $xml->open($uri);
            $outFile = tmpfile();
            $isFirstLine = true;

            while ($xml->read()) {
                if ($xml->nodeType === \XMLReader::ELEMENT && $xml->name === $productElementName) {
                    $doc = new \DOMDocument('1.0', 'UTF-8');
                    $productXml = simplexml_import_dom($doc->importNode($xml->expand(), true));

                    $productLine = $format->convert($productXml);
                    if ($isFirstLine) {
                        // Write CSV header
                        fputcsv($outFile, array_keys($productLine), ';');
                        $isFirstLine = false;
                    }
                    fputcsv($outFile, $productLine, ';');
                }
            }

            return $outFile;
        }

        // We should never get this message, because we call supports() before this method
        throw new \LogicException('Xml product import failed at step convertFile because no valid formats was found');
    }

    private function registerFormats()
    {
        // TODO move that to Symfony config later
        $this->formats = [];
    }

    /**
     * @return null|XmlConverterFormat
     */
    private function findFormat(string $uri)
    {
        foreach ($this->formats as $format) {
            $xml = new \XMLReader();
            $xml->open($uri);
            if ($format->supports($xml)) {
                return $format;
            }
        }
        $this->logger->notice('Xml product import failed because no valid formats was found');

        return null;
    }
}
