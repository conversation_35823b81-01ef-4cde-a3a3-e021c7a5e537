<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Product\Event;

use Symfony\Contracts\EventDispatcher\Event;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Symfony\Component\Validator\Constraints\NotBlank;
use Wizacha\Component\Notification\NotificationEvent;

/**
 * Évènement de modération d'un product.
 */
abstract class ProductModerationEvent extends Event implements NotificationEvent
{
    /**
     * @var int
     */
    private $companyId;

    /**
     * @var int[]
     */
    private $products;

    /**
     * @var string|null
     */
    private $reason;

    /**
     * @var string|null
     */
    private $status;

    /**
     * @param int[] $products
     */
    public function __construct(int $companyId, array $products, string $reason = null, string $status = null)
    {
        $this->products = $products;
        $this->companyId = $companyId;
        $this->reason = $reason;
        $this->status = $status;
    }

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    /**
     * @return int[]
     */
    public function getProducts(): array
    {
        return $this->products;
    }

    /**
     * @return string|null
     */
    public function getReason()
    {
        return $this->reason;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public static function buildDebugForm(FormBuilder $form)
    {
        $form->add('company', IntegerType::class)
            ->add('products', CollectionType::class, [
                'entry_type' => TextType::class,
                'allow_add' => true,
                'constraints' => new NotBlank(),
            ])
            ->add('reason', TextType::class, ['required' => false]);
    }

    public static function createFromForm(Form $form)
    {
        $data = $form->getData();

        return new static($data['company'], array_values($data['products']), $data['reason']);
    }
}
