<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Product\Event;

use Symfony\Contracts\EventDispatcher\Event;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Wizacha\Component\Notification\NotificationEvent;

class ProductStockThresholdReached extends Event implements NotificationEvent
{
    /**
     * @var int
     */
    private $productId;

    /**
     * @var string
     */
    private $combination;

    /**
     * @var int
     */
    private $newAmount;

    public function __construct(int $productId, string $combination, int $newAmount)
    {
        $this->productId = $productId;
        $this->combination = $combination;
        $this->newAmount = $newAmount;
    }

    public static function getDescription(): string
    {
        return 'product_stock_low';
    }

    /**
     * Build the Symfony Form that will be shown in the backend to simulate the event.
     */
    public static function buildDebugForm(FormBuilder $form)
    {
        $form->add('product', IntegerType::class);
        $form->add('amount', IntegerType::class);
    }

    /**
     * Create a instance of that class from the submitted form.
     *
     * @return static
     */
    public static function createFromForm(Form $form)
    {
        return new static($form->getData()['product'], '', $form->getData()['amount']);
    }

    public function getProductId(): int
    {
        return $this->productId;
    }

    public function getCombination(): string
    {
        return $this->combination;
    }

    public function getNewAmount(): int
    {
        return $this->newAmount;
    }
}
