<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Product;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Wizacha\Marketplace\Image\Image;
use Wizacha\Marketplace\PIM\Category\Category;
use Wizacha\Marketplace\PIM\MultiVendorProduct\Link;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct;
use Wizacha\Marketplace\PriceTier\PriceTier;
use Wizacha\Money\Money;

class Product
{
    /**
     * @var int
     */
    protected $id;

    /**
     * @var string
     */
    protected $code;

    /**
     * @var string
     */
    protected $type;

    /**
     * @var string
     */
    protected $status;

    /**
     * @var int
     */
    protected $companyId;

    /**
     * @var string
     */
    protected $approved;

    /**
     * @var string
     */
    protected $listPrice;

    /**
     * @var int
     */
    protected $amount;

    /**
     * @var string
     */
    protected $weight;

    /**
     * @var int
     */
    protected $length;

    /**
     * @var int
     */
    protected $width;

    /**
     * @var int
     */
    protected $height;

    /**
     * @var string
     */
    protected $shippingFreight;

    /**
     * @var int
     */
    protected $lowAvailableLimit;

    /**
     * @var int
     */
    protected $timestamp;

    /**
     * @var int
     */
    protected $updatedTimestamp;

    /**
     * @var string
     */
    protected $usergroupIds;

    /**
     * @var string
     */
    protected $isEdp;

    /**
     * @var string
     */
    protected $edpShipping;

    /**
     * @var string
     */
    protected $unlimitedDownload;

    /**
     * @var string
     */
    protected $tracking;

    /**
     * @var string
     */
    protected $freeShipping;

    /**
     * @var string
     */
    protected $featureComparison;

    /**
     * @var string
     */
    protected $zeroPriceAction;

    /**
     * @var string
     */
    protected $isPbp;

    /**
     * @var string
     */
    protected $isOp;

    /**
     * @var string
     */
    protected $isOper;

    /**
     * @var string
     */
    protected $isReturnable;

    /**
     * @var int
     */
    protected $returnPeriod;

    /**
     * @var int
     */
    protected $availableSince;

    /**
     * @var string
     */
    protected $outOfStockActions;

    /**
     * @var string
     */
    protected $localization;

    /**
     * @var int
     */
    protected $minQuantity;

    /**
     * @var int
     */
    protected $maxQuantity;

    /**
     * @var int
     */
    protected $quantityStep;

    /**
     * @var int
     */
    protected $listQuantityCount;

    /**
     * @var string
     */
    protected $taxIds;

    /**
     * @var string
     */
    protected $ageVerification;

    /**
     * @var bool
     */
    protected $ageLimit;

    /**
     * @var string
     */
    protected $optionsType;

    /**
     * @var string
     */
    protected $exceptionsType;

    /**
     * @var string
     */
    protected $detailsLayout;

    /**
     * @var string
     */
    protected $shippingParams;

    /**
     * @var string
     */
    protected $disableShippings;

    /**
     * @var string
     */
    protected $supplierRef;

    /**
     * @var string
     */
    protected $greenTax;

    /**
     * @var string
     */
    protected $condition;

    /**
     * @var string
     */
    protected $crossedOutPrice;

    /**
     * @var string
     */
    protected $affiliateLink;

    /**
     * @var string
     */
    protected $transactionMode;

    /**
     * @var null|string
     */
    protected $productTemplateType;

    /**
     * Lien vers le MVP auquel est associé le produit.
     * Peut donc être null, si le produit n'est pas associé un à MVP.
     *
     * @var Link|null
     */
    protected $link;

    /**
     * @var int|null
     */
    protected $maxPriceAdjustment;

    /** @var null|bool */
    protected $isSubscription;

    /** @var null|bool */
    protected $isRenewable;

    /**
     * @var Collection|Description[]
     */
    protected $descriptions;

    /**
     * @var Collection|Category[]
     */
    protected $categories;

    /**
     * @var Collection|Attachment[]
     */
    protected $attachments;

    /**
     * @var Collection|PriceTier[]
     */
    protected $priceTiers;

    protected int $quoteRequestMinQuantity;
    protected bool $isQuoteRequestExclusive;

    protected function __construct()
    {
        $this->priceTiers = new ArrayCollection();
    }

    /**
     * Nécessaire pour faire des comparaisons de tableaux (in_array, etc.)
     */
    public function __toString()
    {
        return 'product-' . $this->id;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function getSupplierRef(): string
    {
        return $this->supplierRef;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    public function getApproved(): string
    {
        return $this->approved;
    }

    /**
     * Lien vers le MVP auquel est associé le produit.
     * Peut donc être null, si le produit n'est pas associé un à MVP.
     */
    public function getLink(): ?Link
    {
        return $this->link;
    }

    public function setLink(Link $link)
    {
        $this->link = $link;
    }

    public function removeLink()
    {
        $this->link = null;
    }

    public function getMultiVendorProduct(): ?MultiVendorProduct
    {
        if (\is_null($this->link)) {
            return null;
        }

        return $this->link->getMultiVendorProduct();
    }

    public function getName(): string
    {
        foreach ($this->descriptions as $description) {
            if ($description->isCurrentLocale()) {
                return $description->getName();
            }
        }

        return '';
    }

    public function getShortDescription(): string
    {
        foreach ($this->descriptions as $description) {
            if ($description->isCurrentLocale()) {
                return $description->getShortDescription();
            }
        }

        return '';
    }

    public function getDescription(): string
    {
        foreach ($this->descriptions as $description) {
            if ($description->isCurrentLocale()) {
                return $description->getDescription();
            }
        }

        return '';
    }

    /**
     * @return Category|null
     */
    public function getCategory()
    {
        if ($this->categories->isEmpty()) {
            return null;
        }

        return $this->categories->first();
    }

    /**
     * @return Category[]
     */
    public function getCategories(): array
    {
        if ($this->categories) {
            return $this->categories->toArray();
        }

        return [];
    }

    /**
     * @var array|int[]
     */
    public function getImageIds(): array
    {
        $ids = \Tygh\Database::getColumn(
            "SELECT detailed_id
              FROM ?:images_links
              WHERE object_id=?s
                AND object_type='product'
              ORDER BY (IF(`type`='M',1,0)) DESC, position ASC, pair_id ASC",
            $this->getId()
        );

        return array_map(function ($id) {
            return (int) $id;
        }, $ids);
    }

    /**
     * @var array|Image[]
     */
    public function getImages(): array
    {
        return array_map(function ($id) {
            return new \Wizacha\Marketplace\Image\Image((int) $id);
        }, $this->getImageIds());
    }

    /**
     * @return Attachment[]|Collection
     */
    public function attachments(): Collection
    {
        return $this->attachments;
    }

    public function getGreenTax(): Money
    {
        return Money::fromVariable(\floatval($this->greenTax));
    }

    public function getProductTemplateType(): ?string
    {
        return $this->productTemplateType;
    }

    public function getWeight(): string
    {
        return $this->weight;
    }

    public function getMaxPriceAdjustment(): ?int
    {
        return $this->maxPriceAdjustment;
    }

    public function getPriceTiers(): Collection
    {
        return $this->priceTiers;
    }

    public function getUpdatedTimestamp(): int
    {
        return $this->updatedTimestamp;
    }

    public function addPriceTier(PriceTier $priceTier): self
    {
        $this->removePriceTier($priceTier);
        $this->priceTiers->add($priceTier);

        if ($priceTier->getProduct() !== $this) {
            $priceTier->setProduct($this);
        }

        return $this;
    }

    public function removePriceTier(PriceTier $priceTier): self
    {
        $this->priceTiers->removeElement($priceTier);

        return $this;
    }

    public function isSubscription(): ?bool
    {
        return $this->isSubscription;
    }

    public function setIsSubscription(bool $isSubscription): self
    {
        $this->isSubscription = $isSubscription;

        return $this;
    }

    public function isRenewable(): ?bool
    {
        return $this->isRenewable;
    }

    public function setIsRenewable(bool $isRenewable): self
    {
        $this->isRenewable = $isRenewable;

        return $this;
    }
}
