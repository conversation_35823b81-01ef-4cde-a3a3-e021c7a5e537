<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Product;

use Broadway\UuidGenerator\UuidGeneratorInterface;
use Doctrine\Common\Persistence\ObjectRepository;
use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Wizacha\Marketplace\Entities\Declination;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\Exception\ProductAttachmentNotFound;
use Wizacha\Marketplace\Exception\ProductNotFound;
use Wizacha\Marketplace\PIM\Attribute\AttributeService;
use Wizacha\Marketplace\PIM\Attribute\ManageAttributes;
use Wizacha\Marketplace\PIM\MultiVendorProduct\LinkService;
use Wizacha\Marketplace\PIM\Option\OptionRepository;
use Wizacha\Product as LegacyProduct;
use Wizacha\Storage\StorageService;

class ProductService
{
    use ManageAttributes;

    /**
     * @var EntityManager
     */
    private $entityManager;

    /**
    * @var Connection
    */
    protected $connection;

    /**
     * @var AttributeService
     */
    private $attributeService;

    /**
     * @var LinkService
     */
    private $linkService;

    /**
     * @var EventDispatcherInterface
     */
    private $eventDispatcher;

    /**
     * @var UuidGeneratorInterface
     */
    private $uuidGenerator;

    private ProductRepository $productRepository;
    private OptionRepository $optionRepository;
    private StorageService $productStorageService;

    public function __construct(
        EntityManager $entityManager,
        Connection $connection,
        AttributeService $attributeService,
        LinkService $linkService,
        EventDispatcherInterface $eventDispatcher,
        UuidGeneratorInterface $uuidGenerator,
        ProductRepository $productRepository,
        OptionRepository $optionRepository,
        StorageService $productStorageService
    ) {
        $this->entityManager = $entityManager;
        $this->connection = $connection;
        $this->attributeService = $attributeService;
        $this->linkService = $linkService;
        $this->eventDispatcher = $eventDispatcher;
        $this->uuidGenerator = $uuidGenerator;
        $this->productRepository = $productRepository;
        $this->optionRepository = $optionRepository;
        $this->productStorageService = $productStorageService;
    }

    public function get(int $productId): Product
    {
        $resource = $this->getRepository()->find($productId);

        if (!$resource) {
            throw new ProductNotFound($productId);
        }

        return $resource;
    }

    public function delete(int $productId): void
    {
        fn_delete_product($productId);
    }

    public function detach(int $productId): void
    {
        $this->linkService->detachProduct($productId);
    }

    /**
     * @param int[] $companyIds
     *
     * @return Declination[]
     */
    public function getDeclinationsFromProductCode(?string $productCode, array $companyIds = []): array
    {
        if (empty($productCode)) {
            return [];
        }

        $childlessProductIds = $this->productRepository->getChildlessProductIds($productCode, $companyIds);

        // Génération des déclinaisons des produits orphelins (sans déclinaison réelle)
        $fakeDeclinations = array_map(function ($productInfo) {
            return reset((new LegacyProduct($productInfo['product_id']))->getDeclinations());
        }, $childlessProductIds);

        $realDeclinationsInfo = $this->productRepository->getDeclinationWithEan($productCode, $companyIds);

        // Génération des déclinaisons
        $realDeclinations = array_map(function ($declinationInfo) {
            return new Declination(new LegacyProduct($declinationInfo['product_id']), $declinationInfo['combination']);
        }, $realDeclinationsInfo);

        // Merge avec les déclinaisons récupérées ci-dessus
        return array_merge($fakeDeclinations, $realDeclinations);
    }

    public function refresh(int $productId): void
    {
        $reference = $this->entityManager->getReference(Product::class, $productId);
        $this->entityManager->refresh($reference);
    }

    public function removeFromCache(int $productId): void
    {
        $reference = $this->entityManager->getReference(Product::class, $productId);
        $this->entityManager->detach($reference);
    }

    /**
     * @return Attachment[]
     */
    public function getAttachments(int $productId): array
    {
        return $this->entityManager->getRepository(Attachment::class)->findBy(['product' => $productId]);
    }

    /**
     * @return bool True if something has been added
     */
    public function addAttachments(int $productId, string $key): bool
    {
        $added = false;

        $attachments = fn_filter_uploaded_data($key);
        $names = $_REQUEST[$key . '_data'];
        $product = $this->get($productId);

        foreach ($attachments as $id => $file) {
            $attachment = $this->createAttachment($product, $file['path'], $file['name'], $names[$id], null);
            $this->entityManager->persist($attachment);

            $added = true;
        }

        if ($added) {
            $this->entityManager->flush();
        }

        return $added;
    }

    public function updateAttachmentsFromUrl(int $productId, array $attachments): void
    {
        $product = $this->get($productId);

        foreach ($attachments as $data) {
            if (!empty($data['id'])) {
                $attachment = $this->getAttachment($data['id']);

                if ($attachment->getProduct() != $product) {
                    throw new Forbidden(sprintf('Attachment #%s is not from product #%d', $data['id'], $productId));
                }

                if (isset($data['url'])) {
                    $file = fn_get_url_data($data['url']);
                    $filename = $productId . '/' . $file['name'];

                    $this->productStorageService->put($filename, [
                        'file' => $file['path'],
                        'overwrite' => true,
                    ]);

                    $attachment->setPath($file['name']);
                }

                if (!empty($data['label'])) {
                    $attachment->setName($data['label']);
                }
            } else {
                $file = fn_get_url_data($data['url']);
                $attachment = $this->createAttachment($product, $file['path'], $file['name'], $data['label'] ?? null, $data['url']);
                $this->entityManager->persist($attachment);
                $this->entityManager->flush($attachment);
            }
        }
    }

    public function createAttachment(Product $product, string $path, string $name, string $label = null, string $originalUrl = null): Attachment
    {
        $filename = $product->getId() . '/' . $name;

        $this->productStorageService->put($filename, [
            'file' => $path,
            'overwrite' => true,
        ]);

        $id = $this->uuidGenerator->generate();
        $attachment = new Attachment($product, $id, $name, $label, $originalUrl);

        return $attachment;
    }

    /**
     * @return bool True if something has been updated
     */
    public function updateAttachments(int $productId, string $key): bool
    {
        $updated = false;

        $attachments = fn_filter_uploaded_data($key);
        $names = $_REQUEST[$key . '_data'] ?? [];

        foreach ($this->getAttachments($productId) as $attachment) {
            $id = $attachment->getId();

            if (!empty($names[$id]) && $names[$id] !== $attachment->getName()) {
                $attachment->setName($names[$id]);

                $updated = true;
            }

            if (isset($attachments[$id])) {
                $file = $attachments[$id];
                $filename = $productId . '/' . $file['name'];

                $this->productStorageService->put($filename, [
                    'file' => $file['path'],
                    'overwrite' => true,
                ]);

                $attachment->setPath($file['name']);

                $updated = true;
            }
        }

        if ($updated) {
            $this->entityManager->flush();
        }

        return $updated;
    }

    public function getAttachment(string $id): Attachment
    {
        $resource = $this->entityManager->getRepository(Attachment::class)->findOneBy([
            'id' => $id,
        ]);

        if (!$resource) {
            throw new ProductAttachmentNotFound($id);
        }

        return $resource;
    }

    public function getAttachmentByOriginalUrl(Product $product, string $originalUrl): ?Attachment
    {
        return $this->entityManager->getRepository(Attachment::class)->findOneBy([
            'product' => $product,
            'originalUrl' => $originalUrl,
        ]);
    }

    public function removeAttachment(Attachment $resource): void
    {
        $filename = $resource->getProduct()->getId() . '/' . $resource->getName();
        $this->productStorageService->delete($filename);

        $this->entityManager->remove($resource);
        $this->entityManager->flush();
    }

    public function importAttachments(int $productId, string $data, string $attachmentDelimiter, string $fieldDelimiter): void
    {
        $attachments = array_filter(explode($attachmentDelimiter, $data));
        $attachments = array_map(
            function (string $attachment) use ($fieldDelimiter): string {
                if (false === filter_var($attachment, FILTER_VALIDATE_URL)) {
                    $_data = explode($fieldDelimiter, $attachment);
                    foreach ($_data as $d) {
                        if (\is_string(filter_var($d, FILTER_VALIDATE_URL))) {
                            $attachment = $d;
                            break;
                        }
                    }
                }

                return $attachment;
            },
            $attachments
        );
        $product = $this->get($productId);

        foreach ($product->attachments() as $attachment) {
            // If we already have this attachement, we don't need to remove
            if (false === \in_array($attachment->getOriginalUrl(), $attachments)) {
                $this->entityManager->remove($attachment);
            }
        }

        foreach ($attachments as $rawAttachment) {
            $this->importAttachment($product, $rawAttachment, $fieldDelimiter);
        }

        $this->entityManager->flush();
    }

    public function importAttachment(Product $product, string $rawAttachment, string $fieldDelimiter): void
    {
        $parts = explode($fieldDelimiter, $rawAttachment);

        if (!filter_var($parts[0], FILTER_VALIDATE_URL)) {
            throw new \InvalidArgumentException('Malformed URL for attachment.');
        }

        $file = fn_get_url_data($parts[0]);

        if ($file !== false && $this->getAttachmentByOriginalUrl($product, $parts[0]) === null) {
            $attachment = $this->createAttachment($product, $file['path'], $file['name'], $parts[1] ?? null, $parts[0]);
            $this->entityManager->persist($attachment);
        }
    }

    /** @return string[] */
    public function getProductOptionsInventory(int $productId): array
    {
        return $this->productRepository->getProductOptionsInventory($productId);
    }

    /** @return string[] */
    public function getProductOptionsIdByCombination(string $combination): array
    {
        $options = [];
        $combination = \explode('_', $combination);
        if (\is_array($combination) === true && \count($combination) > 0) {
            for ($i = 0; $i < \count($combination); $i += 2) {
                $options[$combination[$i]] = \array_key_exists($i + 1, $combination) == true ? $combination[$i + 1] : '';
            }
        }

        return $options;
    }

    public function updateProductCategoryHasImpactToDeclinations(int $productId, int $newCategoryId): bool
    {
        $productInventory = $this->getProductOptionsInventory($productId);
        foreach ($productInventory as $inventory) {
            $productOptions = $this->getProductOptionsIdByCombination($inventory['combination']);
            foreach ($productOptions as $key => $option) {
                $categoryOptions = $this->optionRepository->getOptionCategoriesByOptionId($key);
                if (\in_array($newCategoryId, \explode(',', $categoryOptions)) === false) {
                    return true;
                }
            }
        }

        return false;
    }

    public function deleteProductOptionException(int $productId): void
    {
        $this->productRepository->deleteProductOptionException($productId);
    }

    public function getIdByProductCode(string $productCode, int $companyId): ?string
    {
        $product = $this->entityManager
            ->getRepository(Product::class)
            ->findBy([
                'code' => $productCode,
                'companyId' => $companyId,
            ]);

        return (\count($product) === 1) ? (string) $product[0]->getId() : null;
    }

    /**
     * Getter used by 'ManageAttributes' trait
     */
    protected function getAttributeService(): AttributeService
    {
        return $this->attributeService;
    }

    /**
     * Getter used by 'ManageAttributes' trait
     */
    protected function getEntityManager(): EntityManagerInterface
    {
        return $this->entityManager;
    }

    /**
     * Getter used by 'ManageAttributes' trait
     */
    protected function getEventDispatcher(): EventDispatcherInterface
    {
        return $this->eventDispatcher;
    }

    private function getRepository(): ObjectRepository
    {
        return $this->entityManager->getRepository(Product::class);
    }
}
