<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Product;

use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Wizacha\Marketplace\Traits;

class Attachment
{
    use Traits\HasUuid;

    /**
     * @var string|null
     */
    protected $name;

    /**
     * @var string
     */
    protected $path;

    /**
     * @var string|null
     */
    protected $originalUrl;

    /**
     * @var Product
     */
    protected $product;

    public function __construct(Product $product, string $id, string $path, string $name = null, string $originalUrl = null)
    {
        self::checkUuidIntegrity($id);

        $this->product = $product;
        $this->id = $id;
        $this->path = $path;
        $this->originalUrl = $originalUrl;

        if (!empty($name)) {
            $this->name = $name;
        }
    }

    public function getLabel(): string
    {
        return $this->name ?: $this->getPathWithoutExtension();
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function getPath(): string
    {
        return $this->path;
    }

    public function getOriginalUrl(): ?string
    {
        return $this->originalUrl;
    }

    public function getProduct(): Product
    {
        return $this->product;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function setPath(string $path): self
    {
        $this->path = $path;

        return $this;
    }

    public function setOriginalUrl(string $originalUrl = null): self
    {
        $this->originalUrl = $originalUrl;

        return $this;
    }

    public function expose(): array
    {
        return [
            'id' => $this->getId(),
            'label' => $this->getLabel(),
            'originalUrl' => $this->getOriginalUrl(),
            'publicUrl' => $this->getUrl(),
        ];
    }

    public function getFile(): Response
    {
        $filename = $this->product->getId() . '/' . $this->path;

        return container()->get("Wizacha\Storage\ProductAttachmentsStorageService")->get($filename);
    }

    public function getUrl(): string
    {
        return container()->get("router")->generate('api_catalog_product_attachments', ['id' => $this->getId()], UrlGeneratorInterface::ABSOLUTE_URL);
    }

    public function export($delimiter): string
    {
        $data = [
            $this->id,
            $this->getLabel(),
            $this->getOriginalUrl(),
            $this->getUrl(),
        ];

        return implode($delimiter, $data);
    }

    public function getPathWithoutExtension(): string
    {
        return pathinfo($this->path, PATHINFO_FILENAME);
    }

    public function getNameOrLabel(): string
    {
        return $this->name ?: $this->getLabel();
    }
}
