<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Product;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Wizacha\Marketplace\Category\CategoryStatus;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Company\CompanyStatus;
use Wizacha\Marketplace\Exception\CompanyNotFound;
use Wizacha\Marketplace\PIM\Category\Category;
use Wizacha\Marketplace\Product\ProductApprovalStatus;
use Wizacha\Marketplace\Product\ProductStatus;
use Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory;
use Wizacha\Marketplace\ProductOptionInventory\Repository\ProductOptionInventoryRepository;
use Wizacha\Marketplace\Shipping\ShippingStatus;
use Wizacha\Marketplace\Traits\PriceTrait;
use Wizacha\Marketplace\User\UserType;
use Wizacha\Premoderation\ProductModeration\ProductModerationInProgress;
use Wizacha\Premoderation\ProductModeration\ProductModerationInProgressRepository;

/**
 * This service creates a visibility report for a product.
 *
 * WARNING!!
 *
 * If you modify some conditions in this file, you must modify them in these 2 following places:
 * - src/Marketplace/Catalog/Product/ProductService.php : function getSearcheableProductIdInFront
 * - src/Product.php : function frontIds
 *
 * These 3 places must reflect the same behavior at all time! Else we could be out of step with the real behavior
 *
 * @see checkMainVisibility for the detailed conditions
 */
class ProductVisibilityReportService
{
    use PriceTrait;

    private EntityManagerInterface $entityManager;
    private CompanyService $companyService;
    private TranslatorInterface $translator;
    private ProductModerationInProgressRepository $productModerationInProgressRepository;
    private ProductOptionInventoryRepository $productOptionInventoryRepository;
    private bool $showOutOfStockProducts;
    private bool $showZeroPriceProducts;

    private UserType $userType;
    private string $productName;
    private ProductStatus $productStatus;
    private CategoryStatus $categoryStatus;
    private string $categoryName;
    private ?CompanyStatus $companyStatus;
    private ?ProductModerationInProgress $productModerationInProgress;
    private ProductApprovalStatus $productApprovalStatus;
    private ?string $productPrice;
    private int $amount;
    private bool $infiniteStock;
    /** @var ProductOptionInventory[] */
    private array $productOptionsInventory;
    private array $shippings;

    private ProductVisibilityReport $productVisibilityReport;

    public function __construct(
        EntityManagerInterface $entityManager,
        CompanyService $companyService,
        TranslatorInterface $translator,
        ProductModerationInProgressRepository $productModerationInProgressRepository,
        ProductOptionInventoryRepository $productOptionInventoryRepository
    ) {
        $this->entityManager = $entityManager;
        $this->companyService = $companyService;
        $this->translator = $translator;
        $this->productModerationInProgressRepository = $productModerationInProgressRepository;
        $this->productOptionInventoryRepository = $productOptionInventoryRepository;

        $this->showOutOfStockProducts = false;
        $this->showZeroPriceProducts = false;
    }

    public function setShowOutOfStockProducts(bool $showOutOfStockProducts): ProductVisibilityReportService
    {
        $this->showOutOfStockProducts = $showOutOfStockProducts;

        return $this;
    }

    public function setShowZeroPriceProducts(bool $showZeroPriceProducts): ProductVisibilityReportService
    {
        $this->showZeroPriceProducts = $showZeroPriceProducts;

        return $this;
    }

    public function getVisibilityReportFromCsCartData(array $productData, array $userInfo): ProductVisibilityReport
    {
        // Set the user type
        $this->userType = new UserType($userInfo['user_type']);

        // Set the product name
        $this->productName = $productData['product'];

        // Set the product status
        $this->productStatus = new ProductStatus($productData['status']);

        // Set the product approval status with the in progress moderation if there is one
        $this->productApprovalStatus = new ProductApprovalStatus($productData['approved']);
        $this->productModerationInProgress = $this->productModerationInProgressRepository
            ->find($productData['product_id']);

        // Set the main category status and name
        /** @var Category $mainCategory */
        $mainCategory = $this->entityManager->getRepository(Category::class)->find($productData['main_category']);
        $this->categoryStatus = new CategoryStatus($mainCategory->getStatus());
        $this->categoryName = $mainCategory->getName();

        // Set the company status
        // We must use the CompanyService to get a Wizacha\Marketplace\Company\Company instance
        // which handles the "New" status within the Wizacha\Marketplace\Company\CompanyStatus type
        // instead of the Wizacha\Marketplace\PIM\Company\Company which does not handle this status using Wizacha\Status type...
        try {
            $company = $this->companyService->get((int) $productData['company_id']);
            $this->companyStatus = $company->getStatus();
        } catch (CompanyNotFound $exception) {
            // We only need to catch the exception without raising it, to set the companyStatus to null
            // It will be interpreted as an unknown company in the visibility report
            $this->companyStatus = null;
        }

        // Set the product price
        $this->productPrice = $productData['price'];

        // Set the product stock info
        $this->amount = (int) $productData['amount'];
        $this->infiniteStock = $productData['infinite_stock'] === '1';

        // Set the product declinations
        $this->productOptionsInventory = $this->productOptionInventoryRepository
            ->findByProductId($productData['product_id']);

        // Set the product shipping methods
        $this->shippings = $productData['shippings'] ?? [];

        $this->buildVisibilityReport();

        return $this->productVisibilityReport;
    }

    /**
     * Creates a new ProductVisibilityReport
     * Checks all the properties and states to set the booleans in the visibility report
     * and set all the appropriate messages which can be displayed to the user
     */
    private function buildVisibilityReport(): void
    {
        $this->productVisibilityReport = new ProductVisibilityReport();

        // Check all the product properties and states
        $this->checkProductStatus();
        $this->checkCategoryStatus();
        $this->checkProductModeration();
        $this->checkPriceAndStock();
        $this->checkCompanyStatus();
        $this->checkShippings();

        // Now we can tell if the product is in the catalog, available for search and orderable
        $this->checkMainVisibility();
    }

    private function checkProductStatus(): void
    {
        if ($this->productStatus->getValue() === ProductStatus::DISABLED) {
            $this->productVisibilityReport->addReportDetail($this->translator->trans('off_status'));
            $this->productVisibilityReport->isProductStatusDisabled = true;
        } elseif ($this->productStatus->getValue() === ProductStatus::HIDDEN) {
            $this->productVisibilityReport->addReportDetail($this->translator->trans('hidden_status'));
            $this->productVisibilityReport->isProductStatusHidden = true;
        }
    }

    private function checkCategoryStatus(): void
    {
        if ($this->categoryStatus->getValue() === CategoryStatus::DISABLED) {
            $this->addReportDetailCategoryStatus($this->translator->trans('off_category_%s'));
            $this->productVisibilityReport->isCategoryDisabled = true;
        } elseif ($this->categoryStatus->getValue() === CategoryStatus::HIDDEN) {
            $this->addReportDetailCategoryStatus($this->translator->trans('hidden_category_%s'));
            $this->productVisibilityReport->isCategoryHidden = true;
        }
    }

    private function checkProductModeration(): void
    {
        if ($this->productModerationInProgress !== null) {
            // For this case, we cannot redirect to a moderation page, as this is not a ProductApprovalStatus
            // The product won't appear on the moderation page, even if we set the correct filters
            // Therefore just add the detail without any link
            $this->productVisibilityReport->addReportDetail(
                $this->translator->trans('moderation_status_with_semicolon')
                . $this->translator->trans('moderation_in_progress')
            );
            $this->productVisibilityReport->isModerationInProgress = true;
        } elseif ($this->productApprovalStatus->getValue() === ProductApprovalStatus::PENDING) {
            $this->addReportDetailModeration(
                $this->translator->trans('moderation_status_with_semicolon'),
                $this->translator->trans('pending')
            );
            $this->productVisibilityReport->isModerationPending = true;
        } elseif ($this->productApprovalStatus->getValue() === ProductApprovalStatus::REJECTED) {
            $this->addReportDetailModeration(
                $this->translator->trans('moderation_status_with_semicolon'),
                $this->translator->trans('disapproved')
            );
            $this->productVisibilityReport->isModerationRejected = true;
        } elseif ($this->productApprovalStatus->getValue() === ProductApprovalStatus::STANDBY) {
            $this->addReportDetailModeration(
                $this->translator->trans('moderation_status_with_semicolon'),
                $this->translator->trans('w_premoderation_standby')
            );
            $this->productVisibilityReport->isModerationStandby = true;
        }
    }

    private function checkPriceAndStock(): void
    {
        $hasValidPrice = false;
        $hasStock = false;

        if (\count($this->productOptionsInventory) === 0) {
            $hasValidPrice = $this->isPriceValid($this->productPrice, $this->showZeroPriceProducts);
            $hasStock = $this->amount > 0 || $this->infiniteStock === true;
        } else {
            foreach ($this->productOptionsInventory as $productOptionInventory) {
                if (true === $this->isPriceValid($productOptionInventory->getWPrice()->getValue(), $this->showZeroPriceProducts)) {
                    $hasValidPrice = true;
                }

                if ($productOptionInventory->getAmount() > 0 || $productOptionInventory->getInfiniteStock() === true) {
                    $hasStock = true;
                }
            }
        }

        $this->productVisibilityReport->isProductPriceZeroOrNegative = $hasValidPrice === false;
        $this->productVisibilityReport->hasNoStock = $hasStock === false;

        if ($this->productVisibilityReport->isProductPriceZeroOrNegative === true) {
            $this->productVisibilityReport->addReportDetail($this->translator->trans('price_at_0'));
        }

        if ($this->productVisibilityReport->hasNoStock === true) {
            $this->productVisibilityReport->addReportDetail($this->translator->trans('stock_at_0'));
        }
    }

    private function checkCompanyStatus(): void
    {
        if (\is_null($this->companyStatus) === true) {
            $this->productVisibilityReport->addReportDetail($this->translator->trans('unknown_company'));
            $this->productVisibilityReport->isUnknownCompany = true;
        } elseif ($this->companyStatus->getValue() === CompanyStatus::NEW) {
            $this->productVisibilityReport->addReportDetail($this->translator->trans('inactive_company_new'));
            $this->productVisibilityReport->isCompanyNew = true;
        } elseif ($this->companyStatus->getValue() === CompanyStatus::PENDING) {
            $this->productVisibilityReport->addReportDetail($this->translator->trans('inactive_company_pending'));
            $this->productVisibilityReport->isCompanyPending = true;
        } elseif ($this->companyStatus->getValue() === CompanyStatus::DISABLED) {
            $this->productVisibilityReport->addReportDetail($this->translator->trans('inactive_company_disabled'));
            $this->productVisibilityReport->isCompanyDisabled = true;
        }
    }

    private function checkShippings(): void
    {
        if (\count($this->shippings) === 0) {
            $this->productVisibilityReport->addReportDetail($this->translator->trans('no_available_shipping'));
            $this->productVisibilityReport->hasNoAvailableShipping = true;
            $this->productVisibilityReport->hasNoActiveShipping = true;
        } else {
            $this->productVisibilityReport->hasNoActiveShipping = true;

            foreach ($this->shippings as $shipping) {
                if ($shipping['status'] === ShippingStatus::ENABLED) {
                    $this->productVisibilityReport->hasNoActiveShipping = false;
                    break;
                }
            }

            if ($this->productVisibilityReport->hasNoActiveShipping === true) {
                $this->productVisibilityReport->addReportDetail($this->translator->trans('no_active_shipping'));
            }
        }
    }

    /**
     * This function checks all the required conditions for a product to be visible.
     * If all the conditions are not fulfilled, it compiles a reportMessage.
     */
    private function checkMainVisibility(): void
    {
        $this->productVisibilityReport->isPresentInCatalog = $this->productVisibilityReport->isProductStatusDisabled === false
            && $this->productVisibilityReport->isProductPriceZeroOrNegative === false
            && $this->productVisibilityReport->isCategoryDisabled === false
            && $this->productVisibilityReport->isCategoryHidden === false
            && $this->productVisibilityReport->isModerationInProgress === false
            && $this->productVisibilityReport->isModerationPending === false
            && $this->productVisibilityReport->isModerationRejected === false
            && $this->productVisibilityReport->isModerationStandby === false
            && $this->productVisibilityReport->isUnknownCompany === false
            && $this->productVisibilityReport->isCompanyNew === false
            && $this->productVisibilityReport->isCompanyPending === false
            && $this->productVisibilityReport->isCompanyDisabled === false
            && ($this->showOutOfStockProducts === true || $this->productVisibilityReport->hasNoStock === false);

        $this->productVisibilityReport->isAvailableForSearch = $this->productVisibilityReport->isPresentInCatalog === true
            && $this->productVisibilityReport->isProductStatusHidden === false;

        $this->productVisibilityReport->isOrderable = $this->productVisibilityReport->isPresentInCatalog === true
            && $this->productVisibilityReport->hasNoAvailableShipping === false
            && $this->productVisibilityReport->hasNoActiveShipping === false
            && $this->productVisibilityReport->hasNoStock === false;

        if ($this->productVisibilityReport->isPresentInCatalog === false) {
            $this->productVisibilityReport->reportMessage = $this->translator
                ->trans('the_product_is_not_present_in_the_catalog');

            return;
        }

        if ($this->productVisibilityReport->isAvailableForSearch === false) {
            $this->productVisibilityReport->reportMessage = $this->translator
                ->trans('the_product_does_not_appear_in_search_results');
        }

        if ($this->productVisibilityReport->isOrderable === false) {
            if ($this->productVisibilityReport->reportMessage !== '') {
                $this->productVisibilityReport->reportMessage .= ' ';
            }

            $this->productVisibilityReport->reportMessage .= $this->translator->trans('the_product_cannot_be_ordered');
        }
    }

    /**
     * Will add the category name in the translation in place of %s
     *
     * @param string $message
     */
    private function addReportDetailCategoryStatus(string $message): void
    {
        $this->productVisibilityReport->addReportDetail(sprintf($message, $this->categoryName));
    }

    /**
     * Will add a link to the report detail on the moderation status translation
     * If the user is not an Admin, the moderation status will be a simple string
     */
    private function addReportDetailModeration(string $messagePart1, string $moderationStatusLabel)
    {
        if ($this->userType->getValue() === UserType::ADMIN) {
            $this->productVisibilityReport->addReportDetailWithUrl(
                $messagePart1,
                $moderationStatusLabel,
                $this->getModerationUrl()
            );
        } else {
            $this->productVisibilityReport->addReportDetail($messagePart1, $moderationStatusLabel);
        }
    }

    private function getModerationUrl(): string
    {
        return $_SERVER['DOCUMENT_URI'] . '?' . http_build_query([
            'companies_status[]' => CompanyStatus::ENABLED,
            'subcats' => 'Y',
            'q' => $this->productName,
            'cid' => '0',
            'approval_status' => $this->productApprovalStatus->getValue(),
            'image_criteria' => 'A',
            'company_id' => '',
            'hint_' => $this->translator->trans('search') . '...',
            'dispatch[premoderation.products_approval]' => $this->translator->trans('search'),
        ]);
    }
}
