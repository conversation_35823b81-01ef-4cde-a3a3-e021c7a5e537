<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Product\Template;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;

/**
 * Form field type for choosing a category.
 * The list is loaded via AJAX, so no need to pass it anything.
 */
final class CategoriesType extends AbstractType
{
    public function getParent()
    {
        return HiddenType::class;
    }
}
