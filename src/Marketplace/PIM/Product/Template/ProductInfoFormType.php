<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Product\Template;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\CallbackTransformer;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\MoneyType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilder;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\Form\NativeRequestHandler;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Wizacha\Marketplace\PIM\MultiVendorProduct\Status\Status;
use Wizacha\Marketplace\PIM\Tax\Tax;
use Wizacha\Marketplace\PIM\Tax\TaxService;
use Wizacha\Marketplace\PIM\TransactionMode\TransactionModeService;
use Wizacha\Marketplace\PriceTier\Service\PriceTierService;
use Wizacha\Product;
use Tygh\Registry;

/**
 * Form type for creating/updating a product in the BackOffice.
 */
final class ProductInfoFormType extends AbstractType
{
    /**
     * @var TaxService
     */
    private $taxService;

    /**
     * @var TemplateService
     */
    private $templateService;

    /**
     * @var TransactionModeService
     */
    private $transactionModeService;

    /**
     * @var bool
     */
    private $orderAdjustmentFlag;

    /** @var string[] */
    private $mvpRules;

    /** @var bool */
    private $featureSubscription;

    public function __construct(
        TaxService $taxService,
        TemplateService $templateService,
        TransactionModeService $transactionModeService,
        bool $orderAdjustmentFlag = false,
        string $mvpRules = "",
        bool $featureSubscription = false
    ) {
        $this->taxService = $taxService;
        $this->templateService = $templateService;
        $this->transactionModeService = $transactionModeService;
        $this->orderAdjustmentFlag = $orderAdjustmentFlag;
        $this->mvpRules = \explode(",", $mvpRules);
        $this->featureSubscription = $featureSubscription;
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        /** @var FormBuilder $builder */
        $builder->setRequestHandler(new NativeRequestHandler());

        $templates = $this->templateService->getTemplates();
        if (\count($templates) === 1) {
            // If there is only one template, we hide the switch and force the value
            $builder->add('product_template_type', HiddenType::class);
            $builder->get('product_template_type')->setData(reset($templates)->getId());
        } else {
            $builder->add('product_template_type', ChoiceType::class, [
                'required' => true,
                'choices' => array_flip(array_map('strval', $templates)),
                'expanded' => false,
                'multiple' => false,
                'label_attr' => ['tooltip-text' => 'w_tt_select_product_template_type'],
                'label' => 'product_template_type',
            ]);
        }

        $builder
            ->add(
                'status',
                ChoiceType::class,
                [
                    'required' => true,
                    'choices' => Status::toArray(),
                    'expanded' => true,
                    'multiple' => false,
                    'label_attr' => ['tooltip-text' => 'w_tt_select_product_status'],
                    'empty_data' => Status::ENABLED()->getValue(),
                ]
            )
            ->add('category_ids', CategoriesType::class, [
                'required' => true,
                'label_attr' => ['tooltip-text' => 'tt_views_products_update_categories'],
                'label' => 'categories',
                'empty_data' => null,
            ])
            ->add('transaction_mode', ChoiceType::class, [
                'choices' => $this->transactionModeService->getAvailablesModesAsArray(),
                'expanded' => true,
                'choice_label' => function (string $mode, string $label): string {
                    return 'transaction_mode_' . strtolower($label);
                },
                'label_attr' => ['tooltip-text' => 'transaction_mode'],
                'label' => 'transaction_mode',
            ])
            ->add('affiliate_link', TextType::class, ['required' => false, 'label' => 'affiliate_link', 'attr' => ['maxlength' => 128], 'label_attr' => ['tooltip-text' => 'tt_views_products_update_name']]) // product affiliate_link
            ->add('product', TextType::class, ['required' => true, 'label' => 'name', 'attr' => ['maxlength' => 128], 'label_attr' => ['tooltip-text' => 'tt_views_products_update_name']]) // product name
            ->add('product_code', TextType::class, ['required' => false, 'label' => 'sku', 'attr' => ['maxlength' => 128], 'label_attr' => ['tooltip-text' => 'tt_views_products_update_sku']])
            ->add(
                'w_supplier_ref',
                TextType::class,
                [
                    'required' => \in_array('supplier_reference', $this->mvpRules, true),
                    'label' => 'w_supplier_ref',
                    'attr' => ['maxlength' => 128],
                    'label_attr' => ['tooltip-text' => 'ttc_w_supplier_ref'],
                    'empty_data' => '',
                ]
            )
            ->add('company_id', CompanyType::class, [
                'required' => true,
                'label_attr' => ['tooltip-text' => 'ttc_vendor'],
                'label' => 'vendor',
            ])
            ->add('price', ProductPriceType::class, ['required' => true, 'currency' => 'EUR', 'label_attr' => ['tooltip-text' => 'tt_views_products_update_price'], 'data' => 0.0])
            ->add(
                'crossed_out_price',
                MoneyType::class,
                [
                    'required' => false,
                    'currency' => 'EUR',
                    'label_attr' => ['tooltip-text' => 'ttc_crossed_out_price'],
                    'label' => 'crossed_out_price',
                    'empty_data' => '0.00'
                ]
            )
            ->add('tax_ids', ChoiceType::class, [
                'choices' => array_reduce($this->taxService->listEnable(), static function (array $choices, Tax $tax): array {
                    $choices[$tax->getName()] = $tax->getId();

                    return $choices;
                }, []),
                'expanded' => true,
                'multiple' => false,
                'required' => true,
                'label' => 'taxes',
                'label_attr' => ['tooltip-text' => 'tt_views_products_update_taxes'],
                'choice_translation_domain' => false,
            ])
            ->add(
                'w_green_tax',
                MoneyType::class,
                [
                    'required' => false,
                    'currency' => 'EUR',
                    'label_attr' => ['tooltip-text' => 'ttc_w_green_tax'],
                    'label' => 'w_green_tax',
                    'empty_data' => '0.00'
                ]
            )
            ->add(
                'w_condition',
                ChoiceType::class,
                [
                    'required' => true,
                    'choices' => ['new' => Product::CONDITION_NEW, 'used' => Product::CONDITION_USED],
                    'empty_data' => Product::CONDITION_NEW,
                    'expanded' => true,
                    'multiple' => false,
                    'label' => 'w_product_condition',
                    'label_attr' => ['tooltip-text' => 'ttc_w_product_condition'],
                ]
            )
            ->add('amount', IntegerType::class, ['required' => true, 'attr' => ['min' => 0, 'max' => PHP_INT_MAX], 'label_attr' => ['tooltip-text' => 'tt_views_products_update_in_stock'], 'label' => 'in_stock', 'empty_data' => 0])
            ->add('infinite_stock', CheckboxType::class, ['required' => false, 'label' => 'infinite_stock', 'label_attr' => ['tooltip-text' => 'ttc_infinite_stock'], 'value' => '1'])
            ->add('geoloc_lat', NumberType::class, ['label' => 'latitude', 'required' => false, 'scale' => 4]) // will be filled via JS when the geoloc_label field is filled
            ->add('geoloc_lng', NumberType::class, ['label' => 'longitude', 'required' => false, 'scale' => 4]) // will be filled via JS when the geoloc_label field is filled
            ->add('hidden_geoloc_lat', HiddenType::class, ['required' => false]) // will be filled via JS when the geoloc_label field is filled
            ->add('hidden_geoloc_lng', HiddenType::class, ['required' => false]) // will be filled via JS when the geoloc_label field is filled
            ->add('geoloc_postal', HiddenType::class, ['required' => false]) // will be filled via JS when the geoloc_label field is filled
            ->add('geoloc_label', TextType::class, [
                'label' => 'geolocation',
                'label_attr' => ['tooltip-text' => 'ttc_w_localization'],
                'required' => false,
            ])
            ->add('is_edp', CheckboxType::class, [
                'required' => false,
                'label_attr' => ['tooltip-text' => 'tt_views_products_update_downloadable'],
                'label' => 'downloadable',
                'value' => 'Y',
                'attr' => ['onclick' => 'Tygh.$(\'#edp_shipping\').toggleBy(); Tygh.$(\'#product_data_unlimited_download\').closest(\'.control-group\').toggleBy();'], // the onclick hides/shows the unlimited_download field when this field is unchecked/checked
            ])
            ->add('unlimited_download', CheckboxType::class, ['required' => false, 'label_attr' => ['tooltip-text' => 'tt_views_products_update_time_unlimited_download'], 'label' => 'time_unlimited_download', 'value' => 'Y'])
            ->add('short_description', WysiwygType::class, ['required' => false, 'label_attr' => ['tooltip-text' => 'tt_views_products_update_short_description'], 'label' => 'short_description', 'empty_data' => ''])
            ->add('full_description', WysiwygType::class, ['required' => false, 'label_attr' => ['tooltip-text' => 'tt_views_products_update_full_description'], 'label' => 'full_description', 'empty_data' => ''])
            ->add('avail_since', DateType::class, ['required' => false, 'widget' => 'single_text', 'label_attr' => ['tooltip-text' => 'tt_views_products_update_available_since'], 'label' => 'available_since'])
            ->add('is_returnable', CheckboxType::class, ['required' => false, 'label_attr' => ['tooltip-text' => 'ttc_returnable'], 'label' => 'returnable', 'value' => 'Y'])
           // Delivery section
            ->add('weight', NumberType::class, ['required' => false, 'attr' => ['size' => 10], 'label_attr' => ['tooltip-text' => 'ttc_weight'], 'empty_data' => '0']) // @TODO: pass $settings.General.weight_symbol to label trad
        ;

        if (!Registry::get('runtime.company_id')) {
            // SEO section
            $builder->add('seo_name', TextType::class, ['required' => false, 'label_attr' => ['tooltip-text' => 'ttc_seo_name'], 'label' => 'seo_name'])
                ->add('page_title', TextType::class, ['required' => false, 'label_attr' => ['tooltip-text' => 'ttc_page_title'], 'label' => 'page_title', 'empty_data' => ''])
                ->add('meta_description', TextareaType::class, ['required' => false, 'label_attr' => ['tooltip-text' => 'ttc_meta_description'], 'label' => 'meta_description', 'empty_data' => ''])
                ->add('meta_keywords', TextareaType::class, ['required' => false, 'label_attr' => ['tooltip-text' => 'ttc_meta_keywords'], 'label' => 'meta_keywords', 'empty_data' => ''])
            ;
        }

        if ($this->featureSubscription) {
            $builder
                ->add(
                    'is_subscription',
                    CheckboxType::class,
                    [
                        'required' => false,
                        'label' => 'product_has_subscription_type',
                        'label_attr' => [
                            'tooltip-text' => 'ttc_product_has_subscription_type',
                        ],
                        'value' => '1',
                    ]
                )
                ->add(
                    'is_renewable',
                    CheckboxType::class,
                    [
                        'required' => false,
                        'label' => 'product_has_renewal',
                        'label_attr' => [
                            'tooltip-text' => 'ttc_product_has_renewal',
                        ],
                        'value' => '1',
                    ]
                );
        }

        if ($this->orderAdjustmentFlag === true) {
            $builder->add('max_price_adjustment', IntegerType::class, [
                'required' => false,
                'attr' => ['min' => 0, 'max' => 100],
                'label_attr' => ['tooltip-text' => 'tt_views_products_update_max_price_adjustment'],
                'label' => 'max_price_adjustment',
            ]);
        }

        $checkboxTransformer = new CallbackTransformer( // CsCart uses 'Y'/'N' instead of a real boolean
            static function (?string $value): bool {
                return $value === 'Y';
            },
            static function (?bool $value): string {
                return $value ? 'Y' : 'N';
            }
        );
        $builder->get('is_edp')->addModelTransformer($checkboxTransformer);
        $builder->get('unlimited_download')->addModelTransformer($checkboxTransformer);
        $builder->get('is_returnable')->addModelTransformer($checkboxTransformer);
        $builder->get('infinite_stock')->addModelTransformer(new CallbackTransformer(
            static function (?string $value): bool {
                return $value === '1';
            },
            static function (?bool $value): string {
                return $value ? '1' : '0';
            }
        ));
        if ($this->featureSubscription) {
            $builder->get('is_subscription')->addModelTransformer(new CallbackTransformer(
                static function (?string $value): bool {
                    return $value === '1';
                },
                static function (?bool $value): string {
                    return $value ? '1' : '0';
                }
            ));
            $builder->get('is_renewable')->addModelTransformer(new CallbackTransformer(
                static function (?string $value): bool {
                    return $value === '1';
                },
                static function (?bool $value): string {
                    return $value ? '1' : '0';
                }
            ));
        }

        $builder->get('avail_since')->addModelTransformer(new CallbackTransformer( // CsCart needs a nullable unix timestamp
            static function (?string $value): ?\DateTimeImmutable {
                if (empty($value)) {
                    return null;
                }

                return \DateTimeImmutable::createFromFormat('U', $value);
            },
            static function (?\DateTimeInterface $value): string {
                if ($value === null) {
                    return '';
                }

                return (string) $value->getTimestamp();
            }
        ));

        // Form customisation depending on the selected template.
        $builder->addEventSubscriber($this->templateService);

        $builder->addEventListener(FormEvents::PRE_SET_DATA, static function (FormEvent $event) {
            $productData = $event->getData();
            $form = $event->getForm();

            if (!empty($productData['product_id'])) { // if we have a product ID (ie. this is a product update, not a creation)
                if ($productData['tracking'] === 'O') {
                    // if the product has declinations, we don't show directly the price and amount but put a link pointing to the declinations page
                    $form->add('price', LinkToInventoryType::class, ['required' => 'false', 'label_attr' => ['tooltip-text' => 'tt_views_products_update_price'], 'product_id' => $productData['product_id']]);
                    $form->add('amount', LinkToInventoryType::class, ['label_attr' => ['tooltip-text' => 'tt_views_products_update_in_stock'], 'label' => 'in_stock', 'product_id' => $productData['product_id']]);
                } else {
                    // if the product has no declinations, we add a link to add some under the price field
                    $form->add(
                        'price',
                        ProductPriceType::class,
                        [
                            'required' => true,
                            'currency' => 'EUR',
                            'label_attr' => ['tooltip-text' => 'tt_views_products_update_price'],
                            'empty_data' => 0.0,
                            'product_id' => $productData['product_id'],
                            ProductPriceType::PRICE_TIERS_FIRST_INTERVAL_QUANTITY_MAX_LIMIT => $productData[ProductPriceType::PRICE_TIERS_FIRST_INTERVAL_QUANTITY_MAX_LIMIT],
                        ]
                    );
                }
            }
        });
    }

    public function getBlockPrefix(): string
    {
        return 'product_data';
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefault('csrf_protection', false);// CSRF protection is still handled by CsCart
    }
}
