<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Product\Template;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\Form\FormView;
use Symfony\Component\OptionsResolver\OptionsResolver;

/**
 * Fake form field type. Displays a link to the inventory page (the page where you can set stock and price by declination).
 */
final class LinkToInventoryType extends AbstractType
{
    public const PRODUCT_ID = 'product_id';

    public function buildView(FormView $view, FormInterface $form, array $options)
    {
        $view->vars[self::PRODUCT_ID] = $options[self::PRODUCT_ID];
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setRequired(self::PRODUCT_ID);
        $resolver->setAllowedTypes(self::PRODUCT_ID, ['int']);
    }

    public function getParent()
    {
        return TextType::class;
    }
}
