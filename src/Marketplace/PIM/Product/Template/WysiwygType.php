<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Product\Template;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\Form\FormView;

/**
 * Form field type for What You See Is What You Get (WYSIWYG). Uses tinymce via CsCart JS.
 */
final class WysiwygType extends AbstractType
{
    public function buildView(FormView $view, FormInterface $form, array $options)
    {
        $view->vars['attr']['class'] = ($view->vars['attr']['class'] ?? '') . ' cm-wysiwyg input-large';
    }

    public function getParent()
    {
        return TextareaType::class;
    }
}
