<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Product\Template;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;

class TemplateService implements EventSubscriberInterface
{
    /**
     * @var Template[]
     */
    private $templates;

    public function __construct(string $templates, array $templatesConfig)
    {
        $templates = explode(',', $templates);

        foreach ($templatesConfig as $templateId => $templateConfig) {
            if (!\in_array($templateId, $templates)) {
                continue;
            }
            $this->templates[$templateId] = Template::denormalize($templateId, $templateConfig);
        }

        if (\count($this->templates) < 1) {
            throw new \Exception('at least 1 product template is required');
        }
    }

    /**
     * @return Template[]
     */
    public function getTemplates(): array
    {
        return $this->templates;
    }

    /**
     * @param string $template
     * @return Template|null
     */
    public function getTemplate(string $template): ?Template
    {
        return $this->templates[$template] ?? null;
    }

    /**
     * @inheritdoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            FormEvents::PRE_SET_DATA => ['customizeForm', 0],
        ];
    }

    /**
     * Customizes a \Wizacha\Marketplace\PIM\Product\Template\ProductInfoFormType based on the selected template
     * @link https://symfony.com/doc/3.4/form/dynamic_form_modification.html
     */
    public function customizeForm(FormEvent $event): void
    {
        // Existing data (product edit)
        $productTemplateType = $event->getData()['product_template_type'] ?? null;
        // Empty data (product add)
        if (empty($productTemplateType)) {
            $productTemplateType = $event->getForm()['product_template_type']->getData() ?? null;
        }

        if (empty($productTemplateType)) {
            if (\count($this->templates) === 1) {
                $productTemplateType = current($this->templates)->getId();
            } else {
                return;
            }
        }

        if (!isset($this->templates[$productTemplateType])) {
            return;
        }

        $template = $this->templates[$productTemplateType];

        self::hideFields($event, $template);
    }

    public static function hideFields(FormEvent $event, Template $template): void
    {
        $data = $event->getData();

        foreach ($template->getHiddenFields() as $hiddenField) {
            $event->getForm()->add($hiddenField->getName(), HiddenType::class);

            $data[$hiddenField->getName()] = $hiddenField->getValue();
        }

        $data['product_template_type'] = $template->getId();

        $event->setData($data);
    }

    public function getInvalidHiddenFields(
        Template $template,
        array $productData
    ): array {
        $invalidFields = [];

        foreach ($productData as $key => $value) {
            foreach ($template->getHiddenFields() as $importTemplateHiddenField) {
                if ($key === $importTemplateHiddenField->getName()
                    && $value !== $importTemplateHiddenField->getValue()
                    && $value !== ''
                ) {
                    $invalidFields[$key] = $value;
                }
            }
        }

        return $invalidFields;
    }

    public function validateHiddenFields(
        Template $template,
        array $productData
    ): bool {
        return 0 === \count(
            $this->getInvalidHiddenFields($template, $productData)
        );
    }

    public function resetProductOldTemplateHiddenFields(Template $oldTemplate, array $productData): array
    {
        foreach ($oldTemplate->getHiddenFields() as $oldTemplateHiddenField) {
            $productData[$oldTemplateHiddenField->getName()] = '';
        }

        return $productData;
    }
}
