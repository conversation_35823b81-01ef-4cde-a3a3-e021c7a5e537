<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Product\Template;

/**
 * Value object for the template config.
 */
final class Template
{
    /**
     * @var string
     */
    private $id;

    /**
     * @var HiddenField[]
     */
    private $hiddenFields;

    /**
     * @param string $id
     * @param HiddenField[] $hiddenFields
     */
    public function __construct(string $id, array $hiddenFields)
    {
        $this->id = $id;
        $this->setHiddenFields(...$hiddenFields);
    }

    public static function denormalize(string $id, array $data): self
    {
        $hiddenFields = array_map(static function ($name, $value): HiddenField {
            return new HiddenField($name, $value);
        }, array_keys($data['hidden_fields'] ?? []), $data['hidden_fields'] ?? []);

        return new self($id, $hiddenFields);
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function __toString(): string
    {
        return 'product_template_' . $this->id; // meant to be used as a translation key
    }

    /**
     * @return HiddenField[]
     */
    public function getHiddenFields(): array
    {
        return $this->hiddenFields;
    }

    private function setHiddenFields(HiddenField ...$hiddenFields): void
    {
        $this->hiddenFields = $hiddenFields;
    }
}
