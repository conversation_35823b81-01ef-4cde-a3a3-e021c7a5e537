<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Product\Template;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\MoneyType;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\Form\FormView;
use Symfony\Component\OptionsResolver\OptionsResolver;

/**
 * Form field type for the price field.
 * If a product id is given, will also display a link to the "allowed options" page.
 * If the price tier management flag is activated, will also display a link to the "price tiers management" page
 */
final class ProductPriceType extends AbstractType
{
    public const PRODUCT_ID = 'product_id';
    public const PRICE_TIER_MANAGEMENT_FLAG = 'price_tier_management_flag';
    public const PRICE_TIERS_FIRST_INTERVAL_QUANTITY_MAX_LIMIT = 'price_tiers_first_interval_quantity_max_limit';

    public function buildView(FormView $view, FormInterface $form, array $options)
    {
        $view->vars[self::PRODUCT_ID] = $options[self::PRODUCT_ID];
        $view->vars[self::PRICE_TIERS_FIRST_INTERVAL_QUANTITY_MAX_LIMIT] = $options[self::PRICE_TIERS_FIRST_INTERVAL_QUANTITY_MAX_LIMIT];
        $view->vars[self::PRICE_TIER_MANAGEMENT_FLAG] = container()->getParameter('feature.tier_pricing');
    }

    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefined(self::PRODUCT_ID);
        $resolver->setAllowedTypes(self::PRODUCT_ID, ['null', 'int']);
        $resolver->setDefined(self::PRICE_TIERS_FIRST_INTERVAL_QUANTITY_MAX_LIMIT);
        $resolver->setAllowedTypes(self::PRICE_TIERS_FIRST_INTERVAL_QUANTITY_MAX_LIMIT, ['null', 'int']);
    }

    public function getParent()
    {
        return MoneyType::class;
    }
}
