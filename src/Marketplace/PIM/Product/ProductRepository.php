<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Product;

use Doctrine\DBAL\Connection;
use Wizacha\Marketplace\Entities\Declination;
use Wizacha\Product as LegacyProduct;

class ProductRepository
{
    private Connection $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
    }

    /** @return string[] structured data */
    public function getProductOptionsInventory(int $productId): array
    {
        $getStatement = $this->connection->prepare("
            SELECT *
            FROM cscart_product_options_inventory
            WHERE `product_id` = :productId
        ");

        $getStatement->execute(
            [
                'productId' => $productId
            ]
        );

        return $getStatement->fetchAllAssociative();
    }

    public function deleteProductOptionException(int $productId): void
    {
        $query = $this->connection->prepare('DELETE FROM cscart_product_options_exceptions WHERE product_id = :productId');
        $query->execute(
            [
                'productId' => $productId
            ]
        );
    }

    public function getNbProductsApproved(): int
    {
        $statement = $this->connection->prepare(
            "SELECT COUNT(*) AS nb_products FROM cscart_products WHERE approved='Y' AND status='A';"
        );
        $statement->execute();
        $result = $statement->fetchOne();

        return (int) $result;
    }

    /**
     * @param int[] $companyIds
     *
     * @return Declination[]
     */
    public function getChildlessProductIds(string $productCode, array $companyIds): array
    {
        // Products with the desired EAN code AND not having a declination (tracking = 'B')
        $sql = "SELECT product_id FROM cscart_products WHERE product_code = :ean AND tracking = :trackingCode";
        $params = [
            'ean' => $productCode,
            'trackingCode' => LegacyProduct::TRACKING_TYPE_CLASSICAL,
        ];
        $paramsTypes = [];

        if (\count($companyIds) > 0) {
            $sql = $sql . " and company_id IN (:companyIds)";
            $params = \array_merge($params, ['companyIds' => $companyIds]);
            $paramsTypes = ['companyIds' => Connection::PARAM_STR_ARRAY];
        }

        return $this->connection->fetchAll($sql, $params, $paramsTypes);
    }

    /**
     * @param int[] $companyIds
     *
     * @return Declination[]
     */
    public function getDeclinationWithEan(string $productCode, array $companyIds): array
    {
        $sql = <<<SQL
            SELECT inventory.product_id, combination
            FROM cscart_product_options_inventory as inventory
            LEFT JOIN cscart_products as product ON inventory.product_id = product.product_id
            WHERE (inventory.product_code = '' AND product.product_code = :ean)
            OR inventory.product_code = :ean
SQL;
        $params = ['ean' => $productCode];
        $paramsTypes = [];

        if (\count($companyIds) > 0) {
            $sql = <<<SQL
            SELECT inventory.product_id, combination
            FROM cscart_product_options_inventory as inventory
            LEFT JOIN cscart_products as product ON inventory.product_id = product.product_id
            WHERE product.company_id IN (:companyIds)
            AND ((inventory.product_code = '' AND product.product_code = :ean)
            OR inventory.product_code = :ean)
SQL;
            $params = \array_merge($params, ['companyIds' => $companyIds]);
            $paramsTypes = ['companyIds' => Connection::PARAM_STR_ARRAY];
        }

        return $this->connection->fetchAll($sql, $params, $paramsTypes);
    }
}
