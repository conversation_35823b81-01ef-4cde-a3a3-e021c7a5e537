<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Product;

class ProductVisibilityReport
{
    public bool $isProductStatusDisabled = false;
    public bool $isProductStatusHidden = false;
    public bool $isProductPriceZeroOrNegative = false;
    public bool $isCategoryDisabled = false;
    public bool $isCategoryHidden = false;
    public bool $isModerationInProgress = false;
    public bool $isModerationPending = false;
    public bool $isModerationRejected = false;
    public bool $isModerationStandby = false;
    public bool $isUnknownCompany = false;
    public bool $isCompanyNew = false;
    public bool $isCompanyPending = false;
    public bool $isCompanyDisabled = false;
    public bool $hasNoAvailableShipping = false;
    public bool $hasNoActiveShipping = false;
    public bool $hasNoStock = false;

    public bool $isPresentInCatalog = true;
    public bool $isAvailableForSearch = true;
    public bool $isOrderable = true;

    public string $reportMessage = '';
    /** @var array[] */
    public array $reportDetails = [];

    /**
     * Get the product visibility according to the 3 booleans:
     * - isPresentInCatalog
     * - isAvailableForSearch
     * - IsOrderable
     * If we have any of these conditions at False, we may inform the user
     *
     * @return bool
     */
    public function isProductVisible(): bool
    {
        return $this->isPresentInCatalog && $this->isAvailableForSearch && $this->isOrderable;
    }

    /**
     * @param string $messagePart1 First part of the report detail entry
     * @param string|array|null $messagePart2 Second part of the report detail entry (used to add a link and/or a variable part of the message)
     */
    public function addReportDetail(string $messagePart1, $messagePart2 = null): void
    {
        $newReportDetail = ['message_part_1' => $messagePart1];

        if ($messagePart2 !== null) {
            $newReportDetail['message_part_2'] = $messagePart2;
        }

        $this->reportDetails[] = $newReportDetail;
    }

    /**
     * Adds a report detail with a url on the second part of the message that can be displayed as a link
     */
    public function addReportDetailWithUrl(string $messagePart1, string $messagePart2, string $url): void
    {
        $messagePart2 = [
            'message' => $messagePart2,
            'url' => $url,
        ];

        $this->addReportDetail($messagePart1, $messagePart2);
    }
}
