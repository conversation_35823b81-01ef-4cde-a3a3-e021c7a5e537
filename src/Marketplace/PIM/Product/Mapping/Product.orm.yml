Wizacha\Marketplace\PIM\Product\Product:
    type: entity
    table: products
    indexes:
        age_verification:
            columns:
                - age_verification
                - age_limit
        status:
            columns:
                - status
        w_condition:
            columns:
                - w_condition
    uniqueConstraints:
        product_code_company:
            columns:
                - product_code
                - company_id
    id:
        id:
            type: integer
            nullable: false
            id: true
            column: product_id
            generator:
                strategy: IDENTITY
    fields:
        code:
            type: string
            nullable: false
            length: 128
            options:
                fixed: false
                default: ''
            column: product_code
        type:
            type: string
            nullable: false
            length: 1
            options:
                fixed: true
                default: P
            column: product_type
        status:
            type: string
            nullable: false
            length: 1
            options:
                fixed: true
                default: A
        companyId:
            type: integer
            nullable: false
            options:
                unsigned: true
                default: '0'
            column: company_id
        approved:
            type: string
            nullable: false
            length: 1
            options:
                fixed: true
                default: 'Y'
        listPrice:
            type: decimal
            nullable: false
            precision: 12
            scale: 2
            options:
                default: '0.00'
            column: list_price
        amount:
            type: integer
            nullable: false
            options:
                unsigned: false
                default: '0'
        weight:
            type: decimal
            nullable: false
            precision: 12
            scale: 2
            options:
                default: '0.00'
        length:
            type: integer
            nullable: false
            options:
                unsigned: true
                default: '0'
        width:
            type: integer
            nullable: false
            options:
                unsigned: true
                default: '0'
        height:
            type: integer
            nullable: false
            options:
                unsigned: true
                default: '0'
        shippingFreight:
            type: decimal
            nullable: false
            precision: 12
            scale: 2
            options:
                default: '0.00'
            column: shipping_freight
        lowAvailableLimit:
            type: integer
            nullable: false
            options:
                unsigned: true
                default: '0'
            column: low_avail_limit
        timestamp:
            type: integer
            nullable: false
            options:
                unsigned: true
                default: '0'
        updatedTimestamp:
            type: integer
            nullable: false
            options:
                unsigned: true
                default: '0'
            column: updated_timestamp
        isEdp:
            type: string
            nullable: false
            length: 1
            options:
                fixed: true
                default: 'N'
            column: is_edp
        edpShipping:
            type: string
            nullable: false
            length: 1
            options:
                fixed: true
                default: 'N'
            column: edp_shipping
        unlimitedDownload:
            type: string
            nullable: false
            length: 1
            options:
                fixed: true
                default: 'N'
            column: unlimited_download
        tracking:
            type: string
            nullable: false
            length: 1
            options:
                fixed: true
                default: B
        freeShipping:
            type: string
            nullable: false
            length: 1
            options:
                fixed: true
                default: 'N'
            column: free_shipping
        zeroPriceAction:
            type: string
            nullable: false
            length: 1
            options:
                fixed: true
                default: R
            column: zero_price_action
        isPbp:
            type: string
            nullable: false
            length: 1
            options:
                fixed: true
                default: 'N'
            column: is_pbp
        isOp:
            type: string
            nullable: false
            length: 1
            options:
                fixed: true
                default: 'N'
            column: is_op
        isOper:
            type: string
            nullable: false
            length: 1
            options:
                fixed: true
                default: 'N'
            column: is_oper
        isReturnable:
            type: string
            nullable: false
            length: 1
            options:
                fixed: true
                default: 'Y'
            column: is_returnable
        returnPeriod:
            type: integer
            nullable: false
            options:
                unsigned: true
                default: '10'
            column: return_period
        availableSince:
            type: integer
            nullable: false
            options:
                unsigned: true
                default: '0'
            column: avail_since
        outOfStockActions:
            type: string
            nullable: false
            length: 1
            options:
                fixed: true
                default: 'N'
            column: out_of_stock_actions
        localization:
            type: string
            nullable: false
            length: 255
            options:
                fixed: false
                default: ''
        minQuantity:
            type: smallint
            nullable: false
            options:
                unsigned: false
                default: '0'
            column: min_qty
        maxQuantity:
            type: smallint
            nullable: false
            options:
                unsigned: false
                default: '0'
            column: max_qty
        quantityStep:
            type: smallint
            nullable: false
            options:
                unsigned: false
                default: '0'
            column: qty_step
        listQuantityCount:
            type: smallint
            nullable: false
            options:
                unsigned: false
                default: '0'
            column: list_qty_count
        taxIds:
            type: string
            nullable: false
            length: 255
            options:
                fixed: false
                default: ''
            column: tax_ids
        ageVerification:
            type: string
            nullable: false
            length: 1
            options:
                fixed: true
                default: 'N'
            column: age_verification
        ageLimit:
            type: boolean
            nullable: false
            options:
                default: '0'
            column: age_limit
        optionsType:
            type: string
            nullable: false
            length: 1
            options:
                fixed: true
                default: P
            column: options_type
        exceptionsType:
            type: string
            nullable: false
            length: 1
            options:
                fixed: true
                default: F
            column: exceptions_type
        detailsLayout:
            type: string
            nullable: false
            length: 50
            options:
                fixed: false
                default: ''
            column: details_layout
        shippingParams:
            type: string
            nullable: false
            length: 255
            options:
                fixed: false
                default: ''
            column: shipping_params
        disableShippings:
            type: text
            nullable: true
            length: 65535
            options:
                fixed: false
            column: w_disable_shippings
        supplierRef:
            type: string
            nullable: false
            length: 128
            options:
                fixed: false
            column: w_supplier_ref
        greenTax:
            type: decimal
            nullable: false
            precision: 12
            scale: 2
            column: w_green_tax
        condition:
            type: string
            nullable: false
            length: 1
            options:
                fixed: true
                default: 'N'
            column: w_condition
        crossedOutPrice:
            type: decimal
            nullable: true
            precision: 12
            scale: 2
            column: crossed_out_price
        affiliateLink:
            type: text
            nullable: true
            length: 65535
            options:
                fixed: false
            column: affiliate_link
        transactionMode:
            type: string
            nullable: false
            length: 1
            options:
                fixed: true
                default: H
            column: transaction_mode
        productTemplateType:
            type: string
            nullable: true
            length: 255
            options:
                fixed: false
                default: 'product'
            column: product_template_type
        maxPriceAdjustment:
            type: integer
            nullable: true
            column: max_price_adjustment
            options:
                unsigned: true
        isSubscription:
            type: boolean
            column: is_subscription
            nullable: false
            options:
                default: false
        isRenewable:
            type: boolean
            column: is_renewable
            nullable: false
            options:
                default: false
        quoteRequestMinQuantity:
            type: integer
            nullable: false
            column: quote_requests_min_quantity
            options:
                unsigned: true
                default: 1
        isQuoteRequestExclusive:
            type: boolean
            column: is_exclusive_to_quote_requests
            nullable: false
            options:
                default: false
    oneToOne:
        link:
            targetEntity: Wizacha\Marketplace\PIM\MultiVendorProduct\Link
            mappedBy: product
            cascade: [ all ]
            orphanRemoval: true
    oneToMany:
        descriptions:
            targetEntity: Wizacha\Marketplace\PIM\Product\Description
            mappedBy: product
            cascade: [ all ]
            fetch: EAGER
            orphanRemoval: true
        attachments:
            targetEntity: Wizacha\Marketplace\PIM\Product\Attachment
            mappedBy: product
            cascade: [ all ]
            fetch: EAGER
        priceTiers:
            targetEntity: Wizacha\Marketplace\PriceTier\PriceTier
            mappedBy: product
            orphanRemoval: true
            cascade:
                - remove
                - persist
    manyToMany:
        categories:
            targetEntity: Wizacha\Marketplace\PIM\Category\Category
            joinTable:
                name: products_categories
                joinColumns:
                    product_id:
                        referencedColumnName: product_id
                inverseJoinColumns:
                    category_id:
                        referencedColumnName: category_id
