Wizacha\Marketplace\PIM\Product\Attachment:
    type: entity
    table: product_attachments
    indexes:
        attachment_import_idx:
            columns:
                - product_id
                - original_url
    id:
        id:
            type: guid
    fields:
        name:
            type: string
            size: 255
            nullable: true
        path:
            type: string
            size: 255
            nullable: false
        originalUrl:
            type: string
            size: 255
            nullable: true
    manyToOne:
        product:
            targetEntity: Wizacha\Marketplace\PIM\Product\Product
            inversedBy: attachments
            joinColumn:
                name: product_id
                referencedColumnName: product_id
                nullable: false
