Wizacha\Marketplace\PIM\Product\Description:
    type: entity
    table: product_descriptions
    id:
        product:
            associationKey: true
        locale:
            type: locale
            length: 2
            column: lang_code
            nullable: false
            options:
                default: ''
                fixed: true
    fields:
        name:
            type: string
            length: 255
            column: product
            nullable: false
            options:
                default: ''
        shortDescription:
            type: text
            column: short_description
            nullable: false
            options:
                default: ''
        description:
            type: text
            column: full_description
            nullable: false
            options:
                default: ''
    #@TODO : complete the mapping
    manyToOne:
        product:
            targetEntity: Wizacha\Marketplace\PIM\Product\Product
            inversedBy: descriptions
            joinColumn:
                name: product_id
                referencedColumnName: product_id
                nullable: false
