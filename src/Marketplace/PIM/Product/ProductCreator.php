<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Product;

use Wizacha\Company;
use Wizacha\ImageManager;
use Wizacha\Marketplace\PIM\ProductFileService;
use Wizacha\Product;
use Wizacha\Shipping;
use Wizacha\Status;
use Wizacha\Tax;
use Wizacha\Marketplace\Shipping\DeliveryType;

/**
 * Create a product.
 */
class ProductCreator
{
    /**
     * @var ImageManager
     */
    private $imageManager;

    /**
     * @var ProductFileService
     */
    private $productFileService;

    public function __construct(ImageManager $imageManager, ProductFileService $productFileService)
    {
        $this->imageManager = $imageManager;
        $this->productFileService = $productFileService;
    }

    public function createProductForC2C(array $data): Product
    {
        $companyId = Company::getOrCreateC2C($data['company'] ?? '');

        return $this->createProduct($data, $companyId);
    }

    /**
     * Create a product.
     *
     * TODO: refactor this into something understandable.
     */
    public function createProduct(array $data, $companyId): Product
    {

        $product = new Product($data['product_id'] ?: 0);

        $overrideableFields = [
            'full_description',
            'amount',
            'product',
            'price',
            'crossed_out_price',
            'short_description',
            'w_condition',
            'w_supplier_ref',
            'product_code',
        ];

        $newData = array_intersect_key($data, array_flip($overrideableFields));

        $newData['category_ids'][0] = $data['category_id'];
        $newData['short_description'] = $data['short_description'] ?: $data['full_description'];

        if (!empty($data['features']['product_features'])) {
            $newData['product_features'] = $data['features']['product_features'];
        }
        if (!empty($data['features']['add_new_variant'])) {
            $newData['add_new_variant'] = $data['features']['add_new_variant'];
        }

        if (empty($data['product_id'])) {
            $c2cTax = Tax::getLowestRateId();
            $newData['company_id'] = $companyId;
            // Random product code
            if (empty($newData['product_code'])) {
                $newData['product_code'] = 'REF_' . md5(uniqid('', true));
            }
            $newData['is_edp'] = 'N';
            $newData['tax_ids'][$c2cTax] = $c2cTax;
            $newData['approved'] = 'P';
            $newData['is_returnable'] = 'N';
            $newData['discussion_type'] = 'B';
        }

        $shippings = Shipping::getByType(Shipping::TYPE_C2C);
        $shippingsStatus = [];
        foreach ($shippings as $shipping) {
            $shippingsStatus[$shipping['shipping_id']] = Status::DISABLED;
            if ($shipping['w_delivery_type'] == (string) DeliveryType::HAND_WITH_CODE()
                && $data['shipping_handDelivery']
            ) {
                $ship = [];
                $ship['rates'][1]['rate_value']['I'][0]['value'] = 0;
                $ship['rates'][1]['rate_value']['I'][0]['location'] = $data['shipping_handDelivery_label'];
                $ship['rates'][1]['rate_value']['I'][0]['latitude'] = $data['shipping_handDelivery_lat'];
                $ship['rates'][1]['rate_value']['I'][0]['longitude'] = $data['shipping_handDelivery_lng'];
                $ship['rates'][1]['rate_value']['I'][0]['postal'] = $data['shipping_handDelivery_postal'];
                $ship['rates'][1]['rate_value']['I'][1]['value'] = 0;
                $newData['shippings'][$shipping['shipping_id']] = $ship;
                $shippingsStatus[$shipping['shipping_id']] = Status::ENABLED;
            }

            if ($shipping['w_delivery_type'] == (string) DeliveryType::STANDARD()
                && $data['shipping_standardDelivery']
                && ($data['shipping_standardDelivery_price'] || $data['shipping_standardDelivery_price'] === '0')
            ) {
                $ship = [];
                $ship['rates'][1]['rate_value']['I'][0]['value'] = $data['shipping_standardDelivery_price'];
                $ship['rates'][1]['rate_value']['I'][1]['value'] = $data['shipping_standardDelivery_price'];
                $newData['shippings'][$shipping['shipping_id']] = $ship;
                $shippingsStatus[$shipping['shipping_id']] = Status::ENABLED;
            }
        }

        $product->setData($newData);
        $product->save();

        if (!$product->getId()) {
            throw new \Exception('Product ID not generated');
        }

        if ($data['session_images']) {
            $this->imageManager->createPairs($data['session_images'], 'product', $product->getId());
        }

        if (!empty($data['session_attached_file'])) {
            $this->productFileService->attachFile(
                $product,
                $data['session_attached_file']['filename'],
                $data['session_attached_file']['filesize']
            );
        }

        $this->updateGeolocation($data, $product);

        foreach ($newData['shippings'] ?? [] as $key => $rates) {
            fn_wizacha_shippings_w_update_rate(
                'w_product_shipping_rates',
                $key,
                $rates['rates'],
                'product_id',
                $product->getId(),
                $companyId
            );
        }
        foreach ($shippingsStatus as $key => $status) {
            Shipping::changeStatusOnProduct($key, $product->getId(), $status);
        }

        return $product;
    }

    private function updateGeolocation(array $data, Product $product)
    {
        if (isset($data['geoloc_lat'], $data['geoloc_lng'], $data['geoloc_label'], $data['geoloc_postal'])) {
            // Explicit geolocation of the product
            $product->setGeoloc(
                $data['geoloc_lat'],
                $data['geoloc_lng'],
                $data['geoloc_label'],
                $data['geoloc_postal']
            );
        } elseif (isset($data['shipping_handDelivery_lat'], $data['shipping_handDelivery_lng'], $data['shipping_handDelivery_label'], $data['shipping_handDelivery_postal'])
        ) {
            // Else try to infer geolocation from hand delivery
            $product->setGeoloc(
                $data['shipping_handDelivery_lat'],
                $data['shipping_handDelivery_lng'],
                $data['shipping_handDelivery_label'],
                $data['shipping_handDelivery_postal']
            );
        }
    }
}
