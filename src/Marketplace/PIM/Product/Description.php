<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Product;

use Wizacha\Component\Locale\Locale;
use Wizacha\Marketplace\GlobalState\GlobalState;

class Description
{
    /**
     * @var Product
     */
    protected $product;

    /**
     * @var Locale
     */
    protected $locale;

    /**
     * @var string
     */
    protected $name;

    /**
     * @var string
     */
    protected $shortDescription;

    /**
     * @var string
     */
    protected $description;

    //@TODO : complete the mapping

    protected function __construct()
    {
    }

    public function getProduct(): Product
    {
        return $this->product;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getShortDescription(): string
    {
        return $this->shortDescription;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function isCurrentLocale(): bool
    {
        return $this->locale->equals(GlobalState::contentLocale());
    }
}
