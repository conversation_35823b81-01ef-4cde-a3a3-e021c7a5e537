<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Option\Handler;

interface SystemOptionsHandlerInterface
{
    public function up();
    public function down();
    public static function getSystemOptionCode(): string;
    public static function getSystemOptionName(string $locale): string;
    public static function getSystemOptionValues(string $locale): array;
}
