<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Option\Handler;

use Doctrine\DBAL\Driver\Connection;

class PaymentFrequencyHand<PERSON> extends AbstractSqlSystemOptionsHandler implements SystemOptionsHandlerInterface
{
    protected const PAYMENT_FREQUENCY_CODE = 'payment_frequency';

    protected const PAYMENT_FREQUENCY_NAME = [
        'fr' => 'Fréquence de paiement',
        'en' => 'Payment frequency',
    ];

    protected const PAYMENT_FREQUENCY_VALUE = [
        1,
        3,
        6,
        12,
    ];

    public function __construct(Connection $connection)
    {
        parent::__construct($connection);
    }

    /**  @return PaymentFrequencyHandler */
    public function up()
    {
        return $this->upSql($this);
    }

    /**  @return PaymentFrequencyHandler */
    public function down()
    {
        return $this->downSql($this);
    }

    public static function getSystemOptionCode(): string
    {
        return static::PAYMENT_FREQUENCY_CODE;
    }

    public static function getSystemOptionName(string $locale): string
    {
        return static::PAYMENT_FREQUENCY_NAME[$locale];
    }

    public static function getSystemOptionValues(string $locale): array
    {
        return static::PAYMENT_FREQUENCY_VALUE;
    }
}
