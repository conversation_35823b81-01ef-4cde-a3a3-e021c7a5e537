<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Option\Handler;

use Doctrine\DBAL\Driver\Connection;
use Tygh\Languages\Languages;

abstract class AbstractSqlSystemOptionsHandler
{
    /** @var Connection */
    protected $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
    }

    protected function upSql(SystemOptionsHandlerInterface $handler): self
    {
        $statement = $this->connection->prepare('SELECT option_id FROM cscart_product_options WHERE code = ?;');
        $statement->execute([$handler->getSystemOptionCode()]);
        $optionId = $statement->fetch()['option_id'];

        if (false === \is_null($optionId)) {
            return $this;
        }

        $this
            ->connection
            ->prepare('INSERT INTO cscart_product_options (w_categories_path, code) VALUES (?, ?);')
            ->execute(["", $handler->getSystemOptionCode()]);

        $optionId = $this->connection->lastInsertId();

        $languages = Languages::getAvailable();

        if (\array_key_exists('fr', $languages)) {
            $this
                ->connection
                ->prepare("
                    INSERT INTO cscart_product_options_descriptions (option_id, lang_code, option_name)
                    VALUES (?, ?, ?);
                ")
                ->execute([$optionId, 'fr', $handler->getSystemOptionName('fr')]);
        }

        if (\array_key_exists('en', $languages)) {
            $this
                ->connection
                ->prepare("
                    INSERT INTO cscart_product_options_descriptions (option_id, lang_code, option_name)
                    VALUES (?, ?, ?);
                ")
                ->execute([$optionId, 'en', $handler->getSystemOptionName('en')]);
        }

        foreach ($handler->getSystemOptionValues('') as $key => $optionVariantName) {
            $this
                ->connection
                ->prepare('INSERT INTO cscart_product_option_variants (`option_id`, `position`) VALUES (?, ?);')
                ->execute([$optionId, $key]);

            $optionVariantId = $this->connection->lastInsertId();

            if (\array_key_exists('fr', $languages)) {
                $this
                    ->connection
                    ->prepare("
                        INSERT INTO cscart_product_option_variants_descriptions (variant_id, lang_code, variant_name)
                        VALUES (?, ?, ?);
                    ")
                    ->execute([$optionVariantId, 'fr', $optionVariantName]);
            }

            if (\array_key_exists('en', $languages)) {
                $this
                    ->connection
                    ->prepare("
                        INSERT INTO cscart_product_option_variants_descriptions (variant_id, lang_code, variant_name)
                        VALUES (?, ?, ?);
                    ")
                    ->execute([$optionVariantId, 'en', $optionVariantName]);
            }
        }

        return $this;
    }

    protected function downSql(SystemOptionsHandlerInterface $handler): self
    {
        $statement = $this->connection->prepare('SELECT option_id FROM cscart_product_options WHERE code = ?;');
        $statement->execute([$handler->getSystemOptionCode()]);
        $optionId = $statement->fetch()['option_id'];

        if (\is_null($optionId)) {
            return $this;
        }

        $statement = $this->connection->prepare("SELECT variant_id FROM cscart_product_option_variants WHERE option_id = ?;");
        $statement->execute([$optionId]);
        $variantsIdRows = $statement->fetchAll();

        $variantsIds = array_map(
            function (array $row): int {
                return (int) $row['variant_id'];
            },
            $variantsIdRows
        );

        $this
            ->connection
            ->prepare('DELETE FROM cscart_product_option_variants_descriptions WHERE variant_id IN (' . implode(',', $variantsIds) . ');')
            ->execute();

        $this
            ->connection
            ->prepare('DELETE FROM cscart_product_option_variants WHERE option_id = ?;')
            ->execute([$optionId]);

        $languages = Languages::getAvailable();

        if (\array_key_exists('fr', $languages)) {
            $this
                ->connection
                ->prepare('DELETE FROM cscart_product_options_descriptions WHERE option_name = ?;')
                ->execute([$handler->getSystemOptionName('fr')]);
        }

        if (\array_key_exists('en', $languages)) {
            $this
                ->connection
                ->prepare('DELETE FROM cscart_product_options_descriptions WHERE option_name = ?')
                ->execute([$handler->getSystemOptionName('en')]);
        }

        $this
            ->connection
            ->prepare('DELETE FROM cscart_product_options WHERE code = ?;')
            ->execute([$handler->getSystemOptionCode()]);

        return $this;
    }
}
