<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Option\Handler;

use Doctrine\DBAL\Driver\Connection;

class CommitmentPeriodHandler extends AbstractSqlSystemOptionsHandler implements SystemOptionsHandlerInterface
{
    protected const COMMITMENT_PERIOD_CODE = 'commitment_period';

    protected const COMMITMENT_PERIOD_NAME = [
        'fr' => 'Engagement (périodes)',
        'en' => 'Commitment period',
    ];

    protected const COMMITMENT_PERIOD_VALUE = [
        1,
        2,
        3,
        4,
        5,
        6,
        7,
        8,
        9,
        10,
        11,
        12,
        13,
        14,
        15,
        16,
        17,
        18,
        19,
        20,
        21,
        22,
        23,
        24,
        36,
        48,
        60,
        72,
        120,
    ];

    public function __construct(Connection $connection)
    {
        parent::__construct($connection);
    }

    /**  @return CommitmentPeriodHandler */
    public function up()
    {
        return $this->upSql($this);
    }

    /**  @return CommitmentPeriodHandler */
    public function down()
    {
        return $this->downSql($this);
    }

    public static function getSystemOptionCode(): string
    {
        return static::COMMITMENT_PERIOD_CODE;
    }

    public static function getSystemOptionName(string $locale): string
    {
        return static::COMMITMENT_PERIOD_NAME[$locale];
    }

    public static function getSystemOptionValues(string $locale): array
    {
        return static::COMMITMENT_PERIOD_VALUE;
    }
}
