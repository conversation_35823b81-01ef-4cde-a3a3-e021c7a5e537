<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\PIM\Option;

use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Status;

class OptionService
{
    private OptionRepository $optionRepository;

    public function __construct(OptionRepository $optionRepository)
    {
        $this->optionRepository = $optionRepository;
    }

    /**
     * Returns the list of options that are configured to be used for search facets.
     */
    public function getOptionsForFaceting(): array
    {
        list($options) = fn_get_product_global_options(
            [
                'display_on_faceting' => true,
                'status' => [Status::ENABLED],
            ],
            0,
            (string) GlobalState::contentLocale()
        );

        if (\is_array($options) === false) {
            return [];
        }

        return $options;
    }

    public function flattenDetailedLegacyOptions(): array
    {
        $flattenOptions = [];
        foreach ($this->getOptionsForFaceting() as $option) {
            $flattenOptions[$option['option_id']] = $option;
        }

        return $flattenOptions;
    }

    public function getOptionVariants(int $optionId): array
    {
        return fn_get_option_variants($optionId) ?? [];
    }

    /*
     * Transforme une chaine de la forme
     *      'Couleur:Bleu ; taille:XL'
     * en :
     *      ['Couleur' => 'bleu', 'taille'=>'XL']
     *
     */
    public function parseCombination(?string $combination): array
    {
        if ($combination === "") {
            return [];
        }
        $combinations = \explode(';', $combination);
        $options = [];
        foreach ($combinations as $comb) {
            [$option, $value] =  \explode(':', $comb);
            $options[\trim($option)] = \trim($value);
        }

        return $options;
    }

    public function getProductOptionsNameByCombination(?string $combination): array
    {
        return \array_keys($this->parseCombination($combination));
    }

    /** @var string[] */
    public function isCombinationCompatibleWithCategory(array $declinations, int $categoryId): bool
    {
        if (\count($declinations) === 0) {
            return false;
        }

        foreach ($declinations as $declinationData) {
            if (\array_key_exists('combination', $declinationData) === true) {
                $optionsName = $this->getProductOptionsNameByCombination($declinationData['combination']);
                foreach ($optionsName as $name) {
                    $categoryOptions = $this->optionRepository->getOptionCategoriesByOptionName($name);
                    if (\in_array($categoryId, \explode(',', $categoryOptions)) === false) {
                        return false;
                    }
                }
            } else {
                return false;
            }
        }

        return true;
    }
}
