<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Option\Event;

use Symfony\Contracts\EventDispatcher\Event;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Symfony\Component\Validator\Constraints\NotBlank;
use Wizacha\Component\Notification\NotificationEvent;

/**
 * Évènement de modération d'une option.
 */
abstract class OptionModerationEvent extends Event implements NotificationEvent
{
    /**
     * @var int
     */
    private $companyId;

    /**
     * @var array
     */
    private $options;

    /**
     * @var string|null
     */
    private $reason;

    public function __construct(int $companyId, array $options, string $reason = null)
    {
        $this->options = $options;
        $this->companyId = $companyId;
        $this->reason = $reason;
    }

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    public function getOptions(): array
    {
        return $this->options;
    }

    /**
     * @return string|null
     */
    public function getReason()
    {
        return $this->reason;
    }

    public static function buildDebugForm(FormBuilder $form)
    {
        $form->add('company', IntegerType::class)
            ->add('options', CollectionType::class, [
                'entry_type' => TextType::class,
                'allow_add' => true,
                'constraints' => new NotBlank(),
            ])
            ->add('reason', TextType::class, ['required' => false]);
    }

    public static function createFromForm(Form $form)
    {
        $data = $form->getData();

        return new static($data['company'], array_values($data['options']), $data['reason']);
    }
}
