<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Option;

use Doctrine\DBAL\Connection;

class OptionRepository
{
    private const TABLE_PRODUCT_OPTIONS = 'cscart_product_options';
    private const TABLE_PRODUCT_OPTIONS_EXCEPTIONS = 'cscart_product_options_exceptions';

    private Connection $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
    }

    public function getOptionCategoriesByOptionId(int $optionId): string
    {
        $getStatement = $this->connection->prepare(
            \sprintf(
                <<<SQL
                SELECT w_categories_path
                FROM %s
                WHERE `option_id` = :optionId
                SQL,
                static::TABLE_PRODUCT_OPTIONS
            )
        );

        $getStatement->execute(
            [
                'optionId' => $optionId
            ]
        );

        $result = $getStatement->fetchFirstColumn();

        if ($result !== false && \count($result) > 0) {
            return $result[0];
        }

        return '';
    }

    public function getOptionCategoriesByOptionName(string $optionName): string
    {
        $getStatement = $this->connection->prepare(
            \sprintf(
                <<<SQL
                SELECT w_categories_path
                FROM %s po
                INNER JOIN cscart_product_options_descriptions pod ON(po.option_id = pod.option_id)
                WHERE pod.option_name = :optionName
                SQL,
                static::TABLE_PRODUCT_OPTIONS
            )
        );

        $getStatement->execute(
            [
                'optionName' => $optionName
            ]
        );

        $result = $getStatement->fetchFirstColumn();

        if ($result !== false && \count($result) > 0) {
            return $result[0];
        }

        return '';
    }

    public function isOptionIdLinkedToCategories(int $optionId): bool
    {
        return '' !== $this->getOptionCategoriesByOptionId($optionId);
    }

    public function getOptionsExceptionsByOptionIds(int $optionId): \Generator
    {
        $statement = $this->connection->prepare(
            \sprintf(
                <<<SQL
                SELECT *
                FROM %s
                WHERE option_id = :option_id
                SQL,
                static::TABLE_PRODUCT_OPTIONS_EXCEPTIONS
            )
        );

        $statement->execute(
            ['option_id' => $optionId]
        );

        return $statement->iterateAssociative();
    }

    /**
     * Check if there is any product declination configured with an Option
     */
    public function isOptionIdLinkedToProducts(int $optionId): bool
    {
        return false === \is_null(
            $this->getOptionsExceptionsByOptionIds(
                $optionId
            )->current()
        );
    }
}
