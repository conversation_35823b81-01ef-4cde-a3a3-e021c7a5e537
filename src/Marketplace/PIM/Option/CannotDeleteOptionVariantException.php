<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Option;

use Wizacha\Marketplace\Exception\ErrorCode;
use Wizacha\Marketplace\Exception\MarketplaceExceptionInterface;

class CannotDeleteOptionVariantException extends \RuntimeException implements MarketplaceExceptionInterface
{
    /** @var int[] */
    private array $variantIds;

    public function __construct(array $variantIds)
    {
        $this->variantIds = $variantIds;

        parent::__construct("Cannot delete option variant used in current catalog");
    }

    public function getContext(): array
    {
        return [
            'variantIds' => $this->variantIds
        ];
    }

    public function getErrorCode(): ErrorCode
    {
        return ErrorCode::CANNOT_DELETE_OPTION_VARIANT();
    }
}
