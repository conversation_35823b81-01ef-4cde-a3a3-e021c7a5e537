<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Option;

use Wizacha\Marketplace\PIM\Option\Handler\SystemOptionsHandlerInterface;

class SystemOptionsRegistry
{
    /** @var SystemOptionsHandlerInterface[] */
    protected $handlers = [];

    /** @param SystemOptionsHandlerInterface[] $handlers */
    public function __construct(iterable $handlers)
    {
        foreach ($handlers as $handler) {
            if (false === $handler instanceof SystemOptionsHandlerInterface) {
                continue;
            }

            $this->handlers[$handler->getSystemOptionCode()] = $handler;
        }
    }

    public function getHandler(string $code): ?SystemOptionsHandlerInterface
    {
        if (\array_key_exists($code, $this->handlers)) {
            return $this->handlers[$code];
        }

        return null;
    }

    public function getHandlersCodes(): array
    {
        return $this->handlers;
    }
}
