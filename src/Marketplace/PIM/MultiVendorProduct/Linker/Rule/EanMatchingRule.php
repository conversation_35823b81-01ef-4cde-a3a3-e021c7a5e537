<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\MultiVendorProduct\Linker\Rule;

use Doctrine\ORM\EntityManagerInterface;
use Wizacha\Marketplace\PIM\MultiVendorProduct\Linker\Rule;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct;
use Wizacha\Marketplace\PIM\Product\Product;

class EanMatchingRule implements Rule
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function findMatchingProducts(MultiVendorProduct $mvp): array
    {
        if (\strlen($mvp->getCode()) === 0) {
            return [];
        }

        return $this->entityManager->getRepository(Product::class)->findBy([
            'code' => $mvp->getCode(),
        ]);
    }

    public function findMatchingMvp(Product $product): ?MultiVendorProduct
    {
        if (\strlen($product->getCode()) === 0) {
            return null;
        }

        return $this->entityManager->getRepository(MultiVendorProduct::class)->findOneBy([
            'code' => $product->getCode(),
        ]);
    }
}
