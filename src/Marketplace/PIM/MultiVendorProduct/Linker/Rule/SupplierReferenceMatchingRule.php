<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\MultiVendorProduct\Linker\Rule;

use Doctrine\ORM\EntityManagerInterface;
use Wizacha\Marketplace\PIM\MultiVendorProduct\Linker\Rule;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct;
use Wizacha\Marketplace\PIM\Product\Product;

class SupplierReferenceMatchingRule implements Rule
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    public function findMatchingProducts(MultiVendorProduct $mvp): array
    {
        if (\is_null($mvp->getSupplierReference()) || \strlen($mvp->getSupplierReference()) === 0) {
            return [];
        }

        return $this->entityManager->getRepository(Product::class)->findBy([
            'supplierRef' => $mvp->getSupplierReference(),
        ]);
    }

    public function findMatchingMvp(Product $product): ?MultiVendorProduct
    {
        if (\strlen($product->getSupplierRef()) === 0) {
            return null;
        }

        return $this->entityManager->getRepository(MultiVendorProduct::class)->findOneBy([
            'supplierReference' => $product->getSupplierRef(),
        ]);
    }
}
