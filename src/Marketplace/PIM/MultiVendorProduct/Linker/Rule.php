<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\MultiVendorProduct\Linker;

use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct;
use Wizacha\Marketplace\PIM\Product\Product;

interface Rule
{
    /**
     * @return Product[]
     */
    public function findMatchingProducts(MultiVendorProduct $mvp): array;

    public function findMatchingMvp(Product $product): ?MultiVendorProduct;
}
