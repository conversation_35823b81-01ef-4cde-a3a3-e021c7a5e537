<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\MultiVendorProduct\Linker;

use Doctrine\ORM\EntityManagerInterface;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Wizacha\Async\Dispatcher;
use Wizacha\Events\IterableEvent;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\PIM\MultiVendorProduct\Event\MultiVendorProductEvent;
use Wizacha\Marketplace\PIM\MultiVendorProduct\Link;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProductService;
use Wizacha\Marketplace\PIM\Product\Product;
use Wizacha\Marketplace\PIM\Product\ProductService;

/**
 * Ce service écoute les events de modification de produits. S'il trouve une FPU correspondant (cf rules) il rattache le produit à la fiche.
 * Sur option: si on ne trouve pas de FPU, on en créé une.
 *
 * Le service écoute aussi les events de modifications de FPU. S'il trouve des produits correspondants, ils sont rattachés à la FPU.
 *
 * Dans les deux cas, les produits ne corespondants pas aux règles sont détachés (risque de perte de saisie manuelle, donc)
 *
 * @see \Wizacha\Marketplace\PIM\MultiVendorProduct\Linker\Rule
 */
class MultiVendorProductLinker implements EventSubscriberInterface
{
    /**
     * @var MultiVendorProductService
     */
    private $multiVendorProductService;

    /**
     * @var ProductService
     */
    private $productService;

    /**
     * @var string[]
     */
    private $rules;

    /**
     * @var bool
     */
    private $automaticMatchingEnabled;

    /**
     * @var bool
     */
    private $createNewMvpIfNoMatch;

    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var Dispatcher
     */
    private $jobDispatcher;

    /**
     * @var ContainerInterface
     */
    private $ruleLocator;

    public function __construct(
        MultiVendorProductService $multiVendorProductService,
        ProductService $productService,
        string $rules,
        bool $mvpEnabled,
        bool $automaticMatchingEnabled,
        bool $createNewMvpIfNoMatch,
        EntityManagerInterface $entityManager,
        LoggerInterface $logger,
        Dispatcher $jobDispatcher,
        ContainerInterface $ruleLocator
    ) {
        $this->multiVendorProductService = $multiVendorProductService;
        $this->productService = $productService;
        $this->rules = array_map(function (string $rule): string {
            return trim($rule);
        }, explode(',', $rules));
        $this->entityManager = $entityManager;
        $this->logger = $logger;
        $this->jobDispatcher = $jobDispatcher;
        $this->ruleLocator = $ruleLocator;

        // Génération des flag d'activation
        $this->automaticMatchingEnabled = $mvpEnabled && $automaticMatchingEnabled;
        $this->createNewMvpIfNoMatch = $mvpEnabled && $automaticMatchingEnabled && $createNewMvpIfNoMatch;
    }

    public function attachProductToMatchingMvp(Product $product): void
    {
        if (!$this->automaticMatchingEnabled) {
            return;
        }

        foreach ($this->rules as $ruleName) {
            $rule = $this->ruleLocator->get($ruleName);

            if ($mvp = $rule->findMatchingMvp($product)) {
                if ($product->getMultiVendorProduct()) {
                    if ($product->getMultiVendorProduct()->getId() === $mvp->getId()) {
                        // The product is already attached to the right MVP, nothing to do
                        return;
                    }
                }

                $this->multiVendorProductService->attach($mvp->getId(), $product->getId());

                return;
            }
        }

        // We didn't find a match, we create a MVP from this product (without links...recursive issue...)
        if ($this->createNewMvpIfNoMatch) {
            // Non approved product cannot trigger auto create
            if ($product->getApproved() !== 'Y') {
                return;
            }

            $this->multiVendorProductService->createFromProductWithoutLink($product->getId());
        }
    }

    public function attachMatchingProductsToMvpId(string $id): void
    {
        if (!$this->automaticMatchingEnabled) {
            return;
        }

        if ($this->jobDispatcher->delayExec('marketplace.multi_vendor_product.linker::' . __FUNCTION__, \func_get_args())) {
            return;
        }

        try {
            $mvp = $this->multiVendorProductService->get($id);
        } catch (NotFound $ex) {
            return;
        }

        $this->attachMatchingProductsToMvp($mvp);
    }

    public function attachMatchingProductsToMvp(MultiVendorProduct $mvp): void
    {
        if (!$this->automaticMatchingEnabled) {
            return;
        }

        // @codingStandardsIgnoreStart
        // PHP_CS croit que le return type de la closure est celui de la fonction parente
        // Récupération des Products actuellement liés
        $oldProducts = array_map(function (Link $link) {
            return $link->getProduct();
        }, $mvp->getLinks()->toArray());
        // @codingStandardsIgnoreEnd

        // Récupération de l'ensemble des produits matchant au moins une des règles
        $matchingProducts = [];
        foreach ($this->rules as $ruleName) {
            $rule = $this->ruleLocator->get($ruleName);

            foreach ($rule->findMatchingProducts($mvp) as $product) {
                if (!\in_array($product, $matchingProducts, true)) {
                    $matchingProducts[] = $product;
                }
            }
        }

        // Déduction des Products à détacher car liés avant, mais plus liés maintenant
        $productsToDetach = array_diff($oldProducts, $matchingProducts);
        foreach ($productsToDetach as $productToDetach) {
            $this->multiVendorProductService->detach($mvp->getId(), $productToDetach->getId());
        }

        // Déduction des Products à attacher en retirant de la liste ceux qui le sont déjà
        $productsToAttach = array_diff($matchingProducts, $oldProducts);
        // Parcours et traitement des Products à lier
        foreach ($productsToAttach as $matchingProduct) {
            // Le produit est déjà attaché à un MVP
            if ($matchingProduct->getMultiVendorProduct()) {
                // Soit c'est déjà le bon...
                if ($matchingProduct->getMultiVendorProduct()->getId() === $mvp->getId()) {
                    continue;
                }

                // ...soit ce n'est pas le bon, et il faut le détacher
                $this->multiVendorProductService->detach($matchingProduct->getMultiVendorProduct()->getId(), $matchingProduct->getId());
            }

            // Lorsque le Product est orphelin, on peut l'attacher au MVP
            $this->multiVendorProductService->attach($mvp->getId(), $matchingProduct->getId());
        }
    }

    public function attachProductIdToMatchingMvp(int $productId): void
    {
        if ($this->jobDispatcher->delayExec('marketplace.multi_vendor_product.linker::' . __FUNCTION__, \func_get_args())) {
            return;
        }

        try {
            $product = $this->productService->get((int) $productId);
        } catch (NotFound $exception) {
            // We received a product update event but it doesn't exists
            // This is not critical, skip it... Maybe it was deleted in DB since the event was fired
            return;
        }

        // Reset du produit pour être certain qu'il soit bien rechargé de la base
        // car s'il a été modifié par l'objet legacy, les modifications ne seront
        // pas prises en compte (ex: les tests)
        $this->entityManager->refresh($product);
        $this->attachProductToMatchingMvp($product);
    }

    public function onProductChange(IterableEvent $event): void
    {
        if (!$this->automaticMatchingEnabled) {
            return;
        }

        foreach ($event as $productId) {
            $this->attachProductIdToMatchingMvp((int) $productId);
        }
    }

    public function onMultiVendorProductChange(MultiVendorProductEvent $event): void
    {
        if (!$this->automaticMatchingEnabled) {
            return;
        }

        if ($mvp = $event->getMultiVendorProduct()) {
            $this->attachMatchingProductsToMvpId($mvp->getId());
        }
    }

    public static function getSubscribedEvents()
    {
        return [
            \Wizacha\Product::EVENT_CREATE => ['onProductChange', 0],
            \Wizacha\Product::EVENT_UPDATE => ['onProductChange', 0],
            MultiVendorProductEvent::UPDATED => ['onMultiVendorProductChange', 0],
        ];
    }
}
