<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\MultiVendorProduct;

use Broadway\UuidGenerator\UuidGeneratorInterface;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\EventDispatcher\LegacyEventDispatcherProxy;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Tygh\Languages\Languages;
use Wizacha\Async\Dispatcher;
use Wizacha\Component\Import\EximJobService;
use Wizacha\Component\Locale\Locale;
use Wizacha\Core\Concurrent\MutexService;
use Wizacha\Exim\Import\ImportReportMessage\MultiVendorProductMessage;
use Wizacha\ImageManager;
use Wizacha\Marketplace\Exception\IntegrityConstraintViolation;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Image\Exception\BadImage;
use Wizacha\Marketplace\PIM\Attribute\AttributeService;
use Wizacha\Marketplace\PIM\Attribute\Exception\AttributeCannotHaveMultipleValues;
use Wizacha\Marketplace\PIM\Attribute\ManageAttributes;
use Wizacha\Marketplace\PIM\Category\Category;
use Wizacha\Marketplace\PIM\Category\CategoryService;
use Wizacha\Marketplace\PIM\MultiVendorProduct\Event\MultiVendorProductEvent;
use Wizacha\Marketplace\PIM\MultiVendorProduct\Status\Status;
use Wizacha\Marketplace\PIM\Product\Product;
use Wizacha\Marketplace\PIM\Product\ProductCreator;
use Wizacha\Marketplace\PIM\Product\ProductService;
use Wizacha\Marketplace\PIM\Video\Exception\FileSizeException;
use Wizacha\Marketplace\PIM\Video\Video;
use Wizacha\Marketplace\PIM\Video\VideoService;
use Wizacha\Marketplace\Review\ReviewType;
use Wizacha\Marketplace\Seo\SeoService;
use Wizacha\Marketplace\Seo\SlugTargetType;
use Wizacha\Product as LegacyProduct;

class MultiVendorProductService
{
    use ManageAttributes;

    public const DEFAULT_STATUS = Status::HIDDEN;

    /**
     * @var EntityManagerInterface
     */
    protected $entityManager;

    /**
     * @var LinkService
     */
    protected $linkService;

    /**
     * @var ProductService
     */
    protected $productService;

    /**
     * @var UuidGeneratorInterface
     */
    protected $uuidGenerator;

    /**
     * @var SeoService
     */
    protected $seoService;

    /**
     * @var EventDispatcherInterface
     */
    protected $eventDispatcher;

    /**
     * @var ImageManager
     */
    protected $imageManager;

    /**
     * @var CategoryService
     */
    protected $categoryService;

    /**
     * @var ProductCreator
     */
    protected $productCreator;

    /**
     * @var AttributeService
     */
    protected $attributeService;

    /**
     * @var Dispatcher
     */
    protected $jobDispatcher;

    /**
     * @var VideoService
     */
    private $videoService;

    private MutexService $mutexService;

    public function __construct(
        EntityManagerInterface $entityManager,
        LinkService $linkService,
        ProductService $productService,
        UuidGeneratorInterface $uuidGenerator,
        SeoService $seoService,
        EventDispatcherInterface $eventDispatcher,
        ImageManager $imageManager,
        CategoryService $categoryService,
        ProductCreator $productCreator,
        AttributeService $attributeService,
        Dispatcher $jobDispatcher,
        VideoService $videoService,
        MutexService $mutexService
    ) {
        $this->entityManager = $entityManager;
        $this->linkService = $linkService;
        $this->productService = $productService;
        $this->uuidGenerator = $uuidGenerator;
        $this->seoService = $seoService;
        $this->eventDispatcher =  LegacyEventDispatcherProxy::decorate($eventDispatcher);
        $this->imageManager = $imageManager;
        $this->categoryService = $categoryService;
        $this->productCreator = $productCreator;
        $this->attributeService = $attributeService;
        $this->jobDispatcher = $jobDispatcher;
        $this->videoService = $videoService;
        $this->mutexService = $mutexService;
    }

    public function list(int $page = 1, int $resultsPerPage = 50, array $filters = []): Paginator
    {
        $queryBuilder = $this->getRepository()->createQueryBuilder('mvp');
        // Renames a filter with a name that is different from the field
        foreach ($filters as $search => $value) {
            //La boucle ne permettant que du tri direct sur les noms des champs,  pour trier <> sur une date, on gère après
            if ($search !== 'updatedAfter' && $search !== 'updatedBefore') {
                $queryBuilder->orWhere('mvp.' . $search . ' IN (:' . $search . ')')
                ->setParameter($search, \is_array($value) ? $value : [$value]);
            }
        }

        if (\array_key_exists('updatedAfter', $filters) && \is_string($filters['updatedAfter'])) {
            $dateAfter = static::getDateFromParameters($filters['updatedAfter'], 'updatedAfter');
            $queryBuilder->andWhere("mvp.updatedAt >= :updatedAfter")->setParameter('updatedAfter', $dateAfter);
        }

        if (\array_key_exists('updatedBefore', $filters) && \is_string($filters['updatedBefore'])) {
            $dateBefore = static::getDateFromParameters($filters['updatedBefore'], 'updatedBefore');
            $queryBuilder->andWhere("mvp.updatedAt <= :updatedBefore")->setParameter('updatedBefore', $dateBefore);
        }

        $query = $queryBuilder->orderBy('mvp.createdAt', 'asc')->getQuery();
        $paginator = new Paginator($query);
        $paginator->getQuery()
            ->setFirstResult($resultsPerPage * ($page - 1))
            ->setMaxResults($resultsPerPage);

        return $paginator;
    }

    /**
     * Return the result of the search of `$query` in translations.name, mvp.code and mvp.supplier_reference
     *
     * @param string $query
     * @param int $page
     * @param int $resultsPerPage
     * @return Paginator
     */
    public function search(string $query, int $page = 1, int $resultsPerPage = 50): Paginator
    {
        $query = '%' . $query . '%';
        $query = $this->getRepository()->createQueryBuilder('mvp')
            ->join('mvp.translations', 'translation')
            ->where('translation.name LIKE :query')
            ->orWhere('mvp.code LIKE :query')
            ->orWhere('mvp.supplierReference LIKE :query')
            ->setParameter('query', $query)
            ->getQuery();

        $paginator = new Paginator($query);

        $paginator->getQuery()
            ->setFirstResult($resultsPerPage * ($page - 1))
            ->setMaxResults($resultsPerPage);

        return $paginator;
    }

    /**
     * @throws NotFound
     */
    public function get(string $id): MultiVendorProduct
    {
        if (!$resource = $this->getRepository()->find($id)) {
            throw NotFound::fromId('MultiVendorProduct', $id);
        }

        return $resource;
    }

    /**
     * @throws AttributeCannotHaveMultipleValues
     * @throws IntegrityConstraintViolation
     */
    public function save(array $data, MultiVendorProduct $existingResource = null): MultiVendorProduct
    {
        $mutex = $this->mutexService->createThrowingMutex(
            static::class . '::save',
            \hash(
                'md5',
                \json_encode($data)
            ),
        );

        // Récupération de la catégorie
        if (isset($data['categoryId'])) {
            if (!is_numeric($data['categoryId'])) {
                throw IntegrityConstraintViolation::isInvalid('categoryId');
            }

            $data['category'] = $this->entityManager->getRepository(Category::class)->find($data['categoryId']);
            // Not found category is handled by \Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct::checkIntegrity
        }

        if (\is_array($data['imageIds']) === true) {
            $data['imageIds'] = array_values($this->getValidImageIds($data['imageIds']));
        }

        if (\is_null($existingResource)) {
            $resource = new MultiVendorProduct($this->setDefaultValues($data));
        } else {
            $data['id'] = $existingResource->getId();
            $resource = $existingResource->loadData($this->setDefaultValues($data, $existingResource), $existingResource);
        }

        $this->persist($resource);
        $this->seoService->registerSlug(SlugTargetType::PRODUCT(), $resource->getId(), $resource->getSlug());

        if (isset($data['attributes'])) {
            $this->setLegacyAttributesValues($resource->getId(), $data['attributes']);
        }

        if (isset($data['freeAttributes'])) {
            $this->setFreeLegacyAttributes($resource->getId(), $data['freeAttributes']);
        }

        // Ensure review thread is created
        $discussionData = [
            'discussion_type' => ReviewType::RATE_AND_COMMENT,
        ];
        fn_discussion_update_product_post($discussionData, $resource->getId());

        return $resource;
    }

    /**
     * @throws IntegrityConstraintViolation
     * @throws AttributeCannotHaveMultipleValues
     */
    public function patch(array $data, MultiVendorProduct $existingResource): MultiVendorProduct
    {
        if (isset($data['categoryId'])) {
            if (!is_numeric($data['categoryId'])) {
                throw IntegrityConstraintViolation::isInvalid('categoryId');
            }

            $data['category'] = $this->entityManager->getRepository(Category::class)->find($data['categoryId']);
            // Not found category is handled by \Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct::checkIntegrity
        }

        $data['id'] = $existingResource->getId();
        $resource = $existingResource->mergeData($data);

        $this->persist($resource);

        if (isset($data['attributes'])) {
            $this->setLegacyAttributesValues($resource->getId(), $data['attributes']);
        }

        if (isset($data['freeAttributes'])) {
            $this->setFreeLegacyAttributes($resource->getId(), $data['freeAttributes']);
        }

        // Ensure review thread is created
        $discussionData = [
            'discussion_type' => ReviewType::RATE_AND_COMMENT,
        ];
        fn_discussion_update_product_post($discussionData, $resource->getId());

        return $resource;
    }

    public function delete(string $id)
    {
        $resource = $this->get($id);

        foreach ($resource->getLinks() as $link) {
            $link->getProduct()->removeLink();
        }

        foreach ($resource->getImageIds() as $imageId) {
            $this->removeImage($id, (int) $imageId);
        }

        $this->attributeService->removeAllProductValues($id);

        $this->entityManager->remove($resource);
        $this->entityManager->flush();

        $this->seoService->removeSlug(SlugTargetType::PRODUCT(), $id);

        $this->eventDispatcher->dispatch((new MultiVendorProductEvent($resource)), MultiVendorProductEvent::DELETED);
    }

    /**
     * Créé une FPU à partir d'un produit et créé un lien entre les deux
     */
    public function createFromProduct(int $productId): MultiVendorProduct
    {
        // Sauvegarde du MVP
        $resource = $this->createFromProductWithoutLink($productId);

        // Ajout du lien
        $this->attach($resource->getId(), $productId);

        return $resource;
    }

    public function updateFromFirstProduct(string $multiVendorProductId)
    {
        if ($this->jobDispatcher->delayExec('marketplace.multi_vendor_product.service::' . __FUNCTION__, \func_get_args())) {
            return;
        }
        $fpu = $this->get($multiVendorProductId);

        if ($link = reset($fpu->getLinks()->toArray())) {
            if (false === $link instanceof Link) {
                return false;
            }
            $product = $link->getProduct();

            // On set à null tous les attributs présent.
            // Si on ne reset pas tous les attributs actuellement sur le MVP,
            // les valeurs actuelles mais qui ne sont pas renseignées sur le produit
            // ne seront pas supprimées
            $mvpAttribute = array_map(
                function () {
                    return null;
                },
                $this->getLegacyAttributesValues($multiVendorProductId)
            );
            $productAttribute = $this->productService->getLegacyAttributesValues($product->getId());
            $newMvpAttributes = $productAttribute + $mvpAttribute;
            $data = [
                'id' => $fpu->getId(),
                'category' => $fpu->getCategory(),
                //'supplierReference' => $product->getSupplierRef(),
                'code' => $fpu->getCode(),
                'slug' => $fpu->getSlug(),
                'status' => $fpu->getStatus()->getValue(),
                'name' => $product->getName(),
                'shortDescription' => $product->getShortDescription(),
                'description' => $product->getDescription(),
                'attributes' => $newMvpAttributes,
                'freeAttributes' => $this->productService->getFreeLegacyAttributes($product->getId()),
                'imageIds' => $product->getImageIds(),
                'productTemplateType' => $product->getProductTemplateType(),
            ];
            $this->save($data, $fpu);
        }
    }

    public function updateAllFromFirstProduct()
    {
        $query = $this->getRepository()->createQueryBuilder('mvp')
            ->orderBy('mvp.createdAt', 'asc')
            ->getQuery();

        $paginator = new Paginator($query);
        $paginator->getQuery();
        foreach ($paginator as $mvp) {
            $this->updateFromFirstProduct($mvp->getId());
        }
    }

    /**
     * Créé une FPU à partir d'un produit mais sans les lier
     * On a besoin de ne pas lier les produits quand le Linker des FPU est en mode automatique
     * @see \Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProductService::createFromProduct
     */
    public function createFromProductWithoutLink(int $productId): MultiVendorProduct
    {
        // Récupération de langue courante
        $currentLanguage = GlobalState::contentLocale();

        $product = null;
        $resource = null;
        foreach (array_keys(Languages::getAvailable()) as $language) {
            $scannedLocale = new Locale($language);

            // Switch dans la langue pour modifier la ligne de readmodel associée
            GlobalState::switchContentTo($scannedLocale);

            // Récupération du produit dans la langue souhaitée
            $product = $this->productService->get($productId);

            // Il n'y a pas de traduction pour cette langue
            if ($product->getName() === '') {
                continue;
            }

            // Récupération des champs statics
            $data['name'] = $product->getName();
            $data['shortDescription'] = $product->getShortDescription();
            $data['description'] = $product->getDescription();
            $data['code'] = $product->getCode();
            $data['supplierReference'] = \strlen($product->getSupplierRef()) ? $product->getSupplierRef() : null;
            $data['category'] = $product->getCategory();
            $data['productTemplateType'] = $product->getProductTemplateType();

            // Gestion du status : un MVP créé depuis un Product est actif par défaut
            $data['status'] = Status::ENABLED;

            // Sauvegarde du MVP
            $resource = $this->save($data, $resource ?? null);

            // Mise à jour des "free attributs" récupérés depuis le Product legacy
            $this->setFreeLegacyAttributes($resource->getId(), $this->productService->getFreeLegacyAttributes($productId));

            // Mise à jour des "attributs" récupérés depuis le Product
            $this->setLegacyAttributesValues($resource->getId(), $this->productService->getLegacyAttributesValues($productId));
        }

        // Retour à la langue initiale
        GlobalState::switchContentTo($currentLanguage);

        // Gestion des images
        $imageAdded = false;
        foreach ($product->getImageIds() as $imageId) {
            $internalUrl = $this->imageManager->getInternalUrl($imageId);

            try {
                $newImageId = $this->imageManager->createByUrl($internalUrl, false);
                $resource->addImageId($newImageId);
                $imageAdded = true;
            } catch (BadImage $e) {
                // Should not happen
            }
        }

        if ($imageAdded) {
            $this->persist($resource);
        }

        return $resource;
    }

    public function attach(string $multiVendorProductId, int $productId): Link
    {
        try {
            // Le produit est déjà rattaché au bon MVP, on récupère le lien
            return $this->linkService->get($multiVendorProductId, $productId);
        } catch (NotFound $notFoundException) {
            // if product have link we get the link to update
            try {
                $existingLink = $this->linkService->getByProduct($productId);
            } catch (NotFound $notFoundException) {
                // if product not found set link to null to create new link
                $existingLink = null;
            }

            // Le produit n'est rattaché à aucun MVP, on l'attache
            return $this->linkService->save(
                [
                    'multiVendorProductId' => $multiVendorProductId,
                    'productId' => $productId,
                ],
                $existingLink
            );
        }
    }

    /**
     * Detach a product from its MVP using their IDs.
     *
     * @param string $multiVendorProductId
     * @param int $productId
     *
     * @return bool true if the resource was properly detached, false otherwise.
     */
    public function detach(string $multiVendorProductId, int $productId): bool
    {
        return $this->linkService->delete($multiVendorProductId, $productId);
    }

    public function getValidImageIds(array $imagesIds): array
    {
        return $this->imageManager->getValidImageIds($imagesIds);
    }

    /**
     * @param string $multiVendorProductId
     * @param UploadedFile $imageFile
     * @param string $altText
     * @return MultiVendorProduct
     * @throws BadImage if the file is not an image, or the image is too large
     * @throws NotFound
     */
    public function addImage(string $multiVendorProductId, UploadedFile $imageFile, string $altText = ''): MultiVendorProduct
    {
        // Vérification de la présence de la resource
        $resource = $this->get($multiVendorProductId);

        // Sauvegarde de l'image
        $imageId = $this->imageManager->createFromUploadedFile($imageFile);

        $this->setImageAltText($imageId, $altText, (string) GlobalState::contentLocale());

        if ($resource->addImageId($imageId)) {
            $this->persist($resource);
        }

        return $resource;
    }

    public function removeImage(string $multiVendorProductId, int $imageId): MultiVendorProduct
    {
        // Vérification de la présence de la resource
        $resource = $this->get($multiVendorProductId);

        // Vérification que l'image appartient bien au MVP
        if (!\in_array($imageId, $resource->getImageIds())) {
            throw NotFound::fromId('Image', $imageId);
        }

        // Suppression du texte alternatif de l'image
        $this->imageManager->deleteImageAltText($imageId);

        // Suppression de l'image physique (si plus de lien vers celle-ci)
        $this->imageManager->delete($imageId, $multiVendorProductId);

        // Suppression de l'id de l'image
        $resource->removeImageId($imageId);

        // ATTENTION : la ressource n'est pas sauvegardée
        return $resource;
    }

    public function createProductFromMVP(string $mvpId, int $companyId): Product
    {
        $mvp = $this->get($mvpId);

        $data = [
            'product' => $mvp->getName(),
            'product_code' => $mvp->getCode(),
            'full_description' => $mvp->getDescription(),
            'short_description' => $mvp->getShortDescription(),
            'status' => $mvp->getStatus(),
            'w_supplier_ref' => $mvp->getSupplierReference() ?? '',
            'seo_name' => $mvp->getSlug(),
            'category_id' => $mvp->getCategory()->getId(),
            'product_template_type' => $mvp->getProductTemplateType(),
        ];

        $legacyProduct = $this->productCreator->createProduct($data, $companyId);
        $product = $this->productService->get($legacyProduct->getId());
        $imageIds = [];

        foreach ($mvp->getImageIds() as $imageId) {
            $internalUrl = $this->imageManager->getInternalUrl($imageId);

            try {
                $newImageId = $this->imageManager->createByUrl($internalUrl, false);
                $imageIds[] = $newImageId;
            } catch (BadImage $e) {
                // Should not happen
            }
        }

        $this->imageManager->createPairs($imageIds, 'product', $product->getId());

        // handle free attributes
        $this->productService->setFreeLegacyAttributes($legacyProduct->getId(), $this->getFreeLegacyAttributes($mvpId));

        // handle legacy attributes
        $attributesValues = $this->getLegacyAttributesValues($mvpId);
        $this->productService->setLegacyAttributesValues($product->getId(), $attributesValues);

        $this->attach($mvpId, $product->getId());

        return $product;
    }

    public function getProductsIdsFromMvp(string $mvpId): array
    {
        $mvp = $this->get($mvpId);
        $products = $mvp->getLinks()->map(function (Link $link) {
            return $link->getProduct();
        });
        $ids = [];
        foreach ($products as $product) {
            $ids[] = (int) $product->getId();
        }

        return $ids;
    }

    /**
     * @todo diff des attributs en rajoutant les nouveaux du MVP
     */
    public function updateAttachedProducts(string $mvpId)
    {
        $mvp = $this->get($mvpId);
        $products = $mvp->getLinks()->map(function (Link $link) {
            return $link->getProduct();
        });

        // data to override from MVP to product
        $data = [
            'product_code' => $mvp->getCode(),
            'status' => $mvp->getStatus(),
            'w_supplier_ref' => $mvp->getSupplierReference(),
            'seo_name' => $mvp->getSlug(),
            'main_category' => $mvp->getCategory()->getId(),
            'product_template_type' => $mvp->getProductTemplateType(),
        ];

        $attributesValues = $this->getLegacyAttributesValues($mvpId);
        $freeAttributes = $this->getFreeLegacyAttributes($mvpId);

        foreach ($products as $product) {
            $this->productService->setLegacyAttributesValues($product->getId(), $attributesValues);
            $this->productService->setFreeLegacyAttributes($product->getId(), $freeAttributes);
            // TODO: pourquoi n'utilise-t-on pas le productService pour mettre à jour le produit ?
            $p = new LegacyProduct($product->getId());
            $p->setData($data);
            $p->save();
        }
    }

    public function getVideo(?string $videoId): Video
    {
        return $this->videoService->getFromStorage($videoId);
    }

    public function addVideo(string $id, string $url): string
    {
        $videoId = $this->videoService->startUploadFromUrl(0, $url);

        $resource = $this->get($id);

        if (\is_string($resource->getVideo())) {
            $this->videoService->deleteFromStorage($resource->getVideo());
        }

        $this->patch(['video' => $videoId], $resource);

        return $videoId;
    }

    public function deleteVideo(string $id): self
    {
        $resource = $this->get($id);

        if (\is_null($idVideo = $resource->getVideo())) {
            throw NotFound::fromId('Video', $id);
        }

        $this->patch(['video' => null], $resource);

        $this->videoService->delete($idVideo);

        return $this;
    }

    public static function getDateFromParameters(string $dateValue, string $fieldName): \DateTime
    {
        $date = \DateTime::createFromFormat(\DateTime::RFC3339, $dateValue);
        if ($date instanceof \DateTime === false) {
            throw new \InvalidArgumentException("Invalid date format for parameter $fieldName.");
        }

        return $date;
    }

    protected function setDefaultValues(array $data, MultiVendorProduct $multiVendorProduct = null): array
    {
        if (\is_null($data['id'])) {
            $data['id'] = $multiVendorProduct instanceof MultiVendorProduct ? $multiVendorProduct->getId() : $this->uuidGenerator->generate();
        }

        if (\is_null($data['code'])) {
            $data['code'] = $multiVendorProduct instanceof MultiVendorProduct ? $multiVendorProduct->getCode() : uniqid();
        }

        if (\is_null($data['status'])) {
            $data['status'] = $multiVendorProduct instanceof MultiVendorProduct ? $multiVendorProduct->getStatus() : MultiVendorProductService::DEFAULT_STATUS;
        }

        if (\is_null($data['slug']) || mb_strlen($data['slug']) === 0) {
            if ($multiVendorProduct instanceof MultiVendorProduct) {
                $toSlugify = $multiVendorProduct->getSlug();
            } elseif (\is_string($data['name']) && mb_strlen($data['name']) > 0) {
                $toSlugify = $data['name'];
            } else {
                $toSlugify = '';
            }
        } else {
            $toSlugify = $data['slug'];
        }

        if (mb_strlen($toSlugify) > 0 && $multiVendorProduct instanceof MultiVendorProduct === false) {
            $data['slug'] = $this->seoService->registerSlug(SlugTargetType::PRODUCT(), $data['id'], $toSlugify);
        } elseif (mb_strlen($toSlugify) > 0) {
            $data['slug'] = $toSlugify;
        }

        return $data;
    }

    protected function persist(MultiVendorProduct $resource)
    {
        $this->entityManager->persist($resource);
        $this->entityManager->flush();

        $this->eventDispatcher->dispatch(
            new MultiVendorProductEvent($resource),
            MultiVendorProductEvent::UPDATED
        );
    }

    protected function getRepository(): EntityRepository
    {
        return $this->entityManager->getRepository(MultiVendorProduct::class);
    }


    /**
     * Getter used by 'ManageAttributes' trait
     */
    protected function getAttributeService(): AttributeService
    {
        return $this->attributeService;
    }

    /**
     * Getter used by 'ManageAttributes' trait
     */
    protected function getEntityManager(): EntityManagerInterface
    {
        return $this->entityManager;
    }

    /**
     * Getter used by 'ManageAttributes' trait
     */
    protected function getEventDispatcher(): EventDispatcherInterface
    {
        return $this->eventDispatcher;
    }

    public function multiVendorProductImportFromUrl(string $id, string $videoUrl, string $jobId, int $line = 0): void
    {
        try {
            $resource = $this->get($id);

            if (\is_string($resource->getVideo()) === true) {
                $this->deleteVideo($id);
                $this->patch(['video' => null], $resource);
            }

            if (\strlen($videoUrl) > 0) {
                $tmpPathOnS3 = $this->videoService->getVideoStorage()->transferRemoteVideoToS3($videoUrl);

                $video = $this->videoService->importFromS3($tmpPathOnS3);
                $this->patch(['video' => $video->getId()], $resource);
            }
        } catch (NotFound $e) {
            EximJobService::error(MultiVendorProductMessage::EXIM_ERROR_MVP_UNKNOWN_ID, $jobId, $line);
            fn_set_notification('E', __(MultiVendorProductMessage::EXIM_ERROR_MVP_UNKNOWN_ID), $videoUrl);

            return;
        } catch (\InvalidArgumentException $e) {
            EximJobService::warning(MultiVendorProductMessage::EXIM_UNVALID_URL_VIDEO, $jobId, $line, null, $videoUrl);
            fn_set_notification('E', __(MultiVendorProductMessage::EXIM_UNVALID_URL_VIDEO), $videoUrl);

            return;
        } catch (FileSizeException $e) {
            EximJobService::warning(MultiVendorProductMessage::EXIM_VIDEO_SIZE_EXCEPTION, $jobId, $line, null, $videoUrl);
            fn_set_notification('E', __(MultiVendorProductMessage::EXIM_VIDEO_SIZE_EXCEPTION), $videoUrl);

            return;
        } catch (\Exception $e) {
            EximJobService::warning(MultiVendorProductMessage::EXIM_PRODUCT_VIDEO_FAILED, $jobId, $line, null, $videoUrl);
            fn_set_notification('E', __(MultiVendorProductMessage::EXIM_PRODUCT_VIDEO_FAILED), $videoUrl);

            return;
        }
    }

    public function setImageAltText(int $imagesId, string $altText, string $langCode): void
    {
        $this->imageManager->setImageAltText($imagesId, $altText, $langCode);
    }

    public function getImageAltText(int $imagesId, string $langCode): ?string
    {
        return $this->imageManager->getImageAltText($imagesId, $langCode);
    }

    public function updateImageAltText(int $imageId, string $altText, string $langCode): void
    {
        $this->imageManager->updateImageAltText($imageId, $altText, $langCode);
    }
}
