<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\MultiVendorProduct;

use Doctrine\Common\Persistence\ManagerRegistry;
use Doctrine\Common\Persistence\ObjectRepository;
use Doctrine\DBAL\Exception\UniqueConstraintViolationException;
use Doctrine\ORM\EntityManager;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\EventDispatcher\LegacyEventDispatcherProxy;
use Wizacha\Core\Concurrent\MutexService;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\PIM\MultiVendorProduct\Event\LinkEvent;
use Wizacha\Marketplace\PIM\Product\Product;

class LinkService
{
    /**
     * @var EntityManager
     */
    protected $entityManager;

    /**
     * @var EventDispatcherInterface
     */
    protected $eventDispatcher;

    protected MutexService $mutexService;

    public function __construct(
        EntityManager $entityManager,
        EventDispatcherInterface $eventDispatcher,
        MutexService $mutexService
    ) {
        $this->entityManager = $entityManager;
        $this->eventDispatcher = LegacyEventDispatcherProxy::decorate($eventDispatcher);
        $this->mutexService = $mutexService;
    }

    /**
     * @return Link[]
     */
    public function list(MultiVendorProduct $multiVendorProduct): array
    {
        return $this->getRepository()->findBy(['multiVendorProduct' => $multiVendorProduct], ['createdAt' => 'asc']);
    }

    public function get(string $multiVendorProductId, int $productId): Link
    {
        $resource = $this->getRepository()->findOneBy([
            'multiVendorProduct' => $multiVendorProductId,
            'product' => $productId,
        ]);

        if (!$resource) {
            throw NotFound::fromId('Link', $productId . ' / ' . $multiVendorProductId);
        }

        return $resource;
    }

    public function getByProduct(int $productId): Link
    {
        $resource = $this->getRepository()->findOneBy([
            'product' => $productId,
        ]);

        if (!$resource) {
            throw NotFound::fromId('Link', $productId);
        }

        return $resource;
    }

    public function save(array $data, Link $existingResource = null): Link
    {
        $data = $this->loadForeignKeyObjects($data);

        $mvpMutex = $this->mutexService->createBlockingMutex(
            static::class . '::save',
            \hash(
                'md5',
                \json_encode($data)
            )
        );

        if (\is_null($existingResource)) {
            $resource = new Link($data);
            $event = LinkEvent::CREATED;
        } else {
            $resource = $existingResource->loadData($data);
            $event = LinkEvent::UPDATED;
        }

        try {
            $this->entityManager->persist($resource);
            $this->entityManager->flush();
        } catch (UniqueConstraintViolationException $exception) {
            $this->getDoctrine()->resetManager();
        }

        $this->eventDispatcher->dispatch(
            new LinkEvent($resource),
            $event
        );

        return $resource;
    }

    /**
     * Deletes a Link from its MVP ID and product ID.
     *
     * @param string $multiVendorProductId
     * @param int $productId
     *
     * @return bool true if the resource was properly deleted, false otherwise.
     */
    public function delete(string $multiVendorProductId, int $productId): bool
    {
        $resource = $this->getRepository()->findOneBy([
            'multiVendorProduct' => $multiVendorProductId,
            'product' => $productId,
        ]);

        if ($resource instanceof Link) {
             $this->doDetach($resource);

             return true;
        }

        return false;
    }

    public function detachProduct(int $productId)
    {
        $resource = $this->getByProduct($productId);
        $this->doDetach($resource);
    }

    protected function doDetach(Link $resource)
    {
        $resource->getProduct()->removeLink();
        $resource->getMultiVendorProduct()->removeLink($resource);

        // pas besoin de supprimer la ressource explicitement étant donné que
        // les entités MVP et Product sont configuré en [_orphan removal_](https://www.doctrine-project.org/projects/doctrine-orm/en/2.6/reference/working-with-associations.html#orphan-removal)
        // et que le simple fait de supprimer les références au Link va
        // déclencher la suppression
        $this->entityManager->flush();

        $this->eventDispatcher->dispatch((new LinkEvent($resource)), LinkEvent::DELETED);
    }

    protected function loadForeignKeyObjects(array $data): array
    {
        if (isset($data['productId'])) {
            $data['product'] = $this->entityManager->getRepository(Product::class)->find($data['productId']);
        }

        if (isset($data['multiVendorProductId'])) {
            $data['multi_vendor_product'] = $this->entityManager->getRepository(MultiVendorProduct::class)->find($data['multiVendorProductId']);
        }

        return $data;
    }

    protected function getRepository(): ObjectRepository
    {
        return $this->entityManager->getRepository(Link::class);
    }

    private function getDoctrine(): ManagerRegistry
    {
        return container()->get('doctrine');
    }
}
