<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\MultiVendorProduct\Event;

use Symfony\Contracts\EventDispatcher\Event;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct;

class MultiVendorProductEvent extends Event
{
    public const UPDATED = 'multiVendorProduct_updated';
    public const DELETED = 'multiVendorProduct_deleted';

    /**
     * @var MultiVendorProduct
     */
    private $multiVendorProduct;

    public function __construct(MultiVendorProduct $multiVendorProduct)
    {
        $this->multiVendorProduct = $multiVendorProduct;
    }

    public function getMultiVendorProduct(): MultiVendorProduct
    {
        return $this->multiVendorProduct;
    }
}
