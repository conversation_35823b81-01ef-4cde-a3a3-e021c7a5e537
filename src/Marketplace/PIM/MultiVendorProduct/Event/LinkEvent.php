<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\MultiVendorProduct\Event;

use Symfony\Contracts\EventDispatcher\Event;
use Wizacha\Marketplace\PIM\MultiVendorProduct\Link;

class LinkEvent extends Event
{
    public const CREATED = 'link_created';
    public const UPDATED = 'link_updated';
    public const DELETED = 'link_deleted';

    /**
     * @var Link
     */
    private $link;

    public function __construct(Link $link)
    {
        $this->link = $link;
    }

    public function getLink(): Link
    {
        return $this->link;
    }
}
