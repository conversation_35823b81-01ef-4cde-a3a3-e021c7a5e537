<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\MultiVendorProduct\ProductSynchronization;

use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerAwareTrait;
use Psr\Log\NullLogger;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\PIM\MultiVendorProduct\Event\MultiVendorProductEvent;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProductService;

class EventSubscriber implements EventSubscriberInterface
{
    use LoggerAwareTrait;

    /**
     * @var RulesService
     */
    private $rulesService;

    /**
     * @var MultiVendorProductService
     */
    private $multiVendorProductService;

    /**
     * @var bool
     */
    private $isActivated = false;
    /**
     * @var EntityManagerInterface
     */
    private $em;

    public function __construct(RulesService $rulesService, MultiVendorProductService $multiVendorProductService, EntityManagerInterface $em)
    {
        $this->rulesService = $rulesService;
        $this->multiVendorProductService = $multiVendorProductService;
        $this->setLogger(new NullLogger());
        $this->em = $em;
    }

    public function setIsActivated(bool $isActivated): void
    {
        $this->isActivated = $isActivated;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            MultiVendorProductEvent::UPDATED => ['onMultiVendorProductUpdate', 0],
            MultiVendorProductEvent::DELETED => ['onMultiVendorProductDelete', 0],
        ];
    }

    public function onMultiVendorProductUpdate(MultiVendorProductEvent $event): void
    {
        if (!$this->isActivated) {
            return;
        }

        $mvp = $event->getMultiVendorProduct();
        $mvpId = $mvp->getId();

        foreach ($mvp->getLinks() as $link) {
            // Delete product if it doesn't match the rules anymore.
            $product = $link->getProduct();
            $rules = $this->rulesService->get($product->getCompanyId());
            if (!$this->rulesService->multiVendorProductMatches($mvp, $rules)) {
                $this->multiVendorProductService->detach($mvp->getId(), $product->getId());
                try {
                    fn_delete_product($product->getId(), true);
                } catch (Forbidden $e) {
                    $this->logger->error('[ACCESS DENIED] Product #' . $product->getId());
                }
            }
        }

        // Update existing products which are left.
        $this->updateAttachedProducts($mvpId);

        $this->em->transactional(function () use ($mvpId): void {
            try {
                $companiesIdsForCreation = array_flip(fn_get_all_companies_ids(true));
                $mvp = $this->multiVendorProductService->get($mvpId); // We get a fresh MVP to be sure to have the latest links

                foreach ($mvp->getLinks() as $link) {
                    unset($companiesIdsForCreation[$link->getProduct()->getCompanyId()]); // Existing links don't need to be considered for creation.
                }

                // Create missing products if the MVP matches the company's rules
                foreach (array_keys($companiesIdsForCreation) as $companyId) {
                    $rules = $this->rulesService->get($companyId);
                    if ($this->rulesService->multiVendorProductMatches($mvp, $rules)) {
                        $this->createProductFromMVP($mvp->getId(), $companyId);
                    }
                }
            } catch (\Throwable $e) {
                $this->logger->error('MVP Sync: failed to create products from a MVP', ['mvp_id' => $mvpId, 'exception' => $e]);
            }
        });
    }

    public function onMultiVendorProductDelete(MultiVendorProductEvent $event): void
    {
        if (!$this->isActivated) {
            return;
        }

        // Delete all attached products
        $exceptions = [];
        foreach ($event->getMultiVendorProduct()->getLinks() as $link) {
            try {
                $this->deleteProduct($link->getProduct()->getId());
            } catch (\Exception $e) {
                $exceptions[] = $e;
            }
        }

        $exceptionsCount = \count($exceptions);
        if ($exceptionsCount === 1) {
            throw reset($exceptions);
        }

        if ($exceptionsCount > 1) {
            throw new \Exception("MVP sync: $exceptionsCount product deletion failed:\n\n" . implode("\n\n", array_map('strval', $exceptions)), 0, reset($exceptions));
        }
    }

    private function updateAttachedProducts(string $mvpId)
    {
        try {
            $this->multiVendorProductService->updateAttachedProducts($mvpId);
        } catch (\Throwable $e) {
            $this->logger->error('MVP Sync: failed to update attached products of a MVP', ['mvp_id' => $mvpId, 'exception' => $e]);
        }
    }

    private function createProductFromMVP(string $mvpId, int $companyId)
    {
        try {
            $this->multiVendorProductService->createProductFromMVP($mvpId, $companyId);
        } catch (\Throwable $e) {
            $this->logger->error('MVP Sync: failed to create product from a MVP', ['mvp_id' => $mvpId, 'company_id' => $companyId, 'exception' => $e]);
        }
    }

    private function deleteProduct(int $productId)
    {
        $deleted = fn_delete_product($productId);
        if (!$deleted) {
            throw new \Exception('Product wasn\'t deleted for an unknown reason');
        }
    }

    // @TODO : on rule change, create and delete as necessary
}
