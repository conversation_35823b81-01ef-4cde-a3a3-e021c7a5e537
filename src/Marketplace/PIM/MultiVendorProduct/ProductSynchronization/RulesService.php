<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\MultiVendorProduct\ProductSynchronization;

use Doctrine\Common\Persistence\ObjectRepository;
use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManagerInterface;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\PIM\Attribute\AttributeType;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProductService;
use Wizacha\Marketplace\PIM\Product\ProductService;

class RulesService
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    /**
     * @var Connection
     */
    private $dbal;

    /**
     * @var MultiVendorProductService
     */
    private $multiVendorProductService;

    /**
     * @var ProductService
     */
    private $productService;

    public function __construct(EntityManagerInterface $entityManager, MultiVendorProductService $multiVendorProductService, Connection $dbal, ProductService $productService)
    {
        $this->entityManager = $entityManager;
        $this->multiVendorProductService = $multiVendorProductService;
        $this->dbal = $dbal;
        $this->productService = $productService;
    }

    public function get(int $companyId): Rules
    {
        $rules = $this->getRepository()->find($companyId);

        return $rules ?: new Rules($companyId);
    }

    public function save(Rules $rules): void
    {
        $this->entityManager->persist($rules);
        $this->entityManager->flush();
        $this->sync($rules);
    }

    /**
     * @return string[]
     */
    public function findMVPsMatchingRules(Rules $rules): iterable
    {
        $qb = $this->dbal->createQueryBuilder()
            ->select('mvp.id')
            ->from('doctrine_multi_vendor_product', 'mvp')
            ->join('mvp', 'cscart_categories', 'c', 'mvp.category_id = c.category_id')
            ->leftJoin('mvp', 'cscart_product_features_values', 'brand', 'brand.product_id = mvp.id AND brand.lang_code = :locale')
            ->leftJoin('brand', 'cscart_product_features', 'attrs', 'attrs.feature_id = brand.feature_id')
            ->where("(attrs.feature_type = :brandAttributeType OR brand.feature_id IS NULL)")
            ->setParameter('locale', (string) GlobalState::contentLocale())
            ->setParameter('brandAttributeType', AttributeType::LIST_BRAND);

        // Inclusion rules
        $condition = $qb->expr()->orX();
        if (!empty($rules->getIncludedProducts())) {
            $condition->add($qb->expr()->in('mvp.id', array_map([$qb, 'createNamedParameter'], $rules->getIncludedProducts())));
        }
        if (!empty($rules->getIncludedBrands())) {
            $condition->add($qb->expr()->in('brand.variant_id', array_map([$qb, 'createNamedParameter'], $rules->getIncludedBrands())));
        }
        if (!empty($rules->getIncludedCategories())) {
            $categoryIdsStr = implode('|', array_unique($rules->getIncludedCategories()));
            $regexp = "^(.+/)?({$categoryIdsStr})(/.+)?$";
            $condition->add("c.id_path REGEXP {$qb->createNamedParameter($regexp)}");
        }

        if (\count($condition) === 0) {
            // Without inclusion rules, we already know there can't be any results
            return [];
        }

        // Exclusion rules
        $condition = $qb->expr()->andX($condition);
        if (!empty($rules->getExcludedProducts())) {
            $condition->add($qb->expr()->notIn('mvp.id', array_map([$qb, 'createNamedParameter'], $rules->getExcludedProducts())));
        }
        if (!empty($rules->getExcludedBrands())) {
            $condition->add($qb->expr()->notIn('brand.variant_id', array_map([$qb, 'createNamedParameter'], $rules->getExcludedBrands())));
        }
        if (!empty($rules->getExcludedCategories())) {
            $categoryIdsStr = implode('|', array_unique($rules->getExcludedCategories()));
            $regexp = "^(.+/)?({$categoryIdsStr})(/.+)?$";
            $condition->add("c.id_path NOT REGEXP {$qb->createNamedParameter($regexp)}");
        }

        $qb->where($condition);

        return $qb->execute()->fetchAll(\PDO::FETCH_COLUMN, 0);
    }

    public function multiVendorProductMatches(MultiVendorProduct $mvp, Rules $rules): bool
    {
        $categoryIds = array_map('intval', explode('/', $mvp->getCategory()->getPathId()));
        $isIncluded = false;
        if (\in_array($mvp->getId(), $rules->getIncludedProducts())) {
            $isIncluded = true;
        } elseif (!empty(array_intersect($categoryIds, $rules->getIncludedCategories()))) {
            $isIncluded = true;
        } else {
            $mvpBrandId = $this->getBrandIdFromMvpId($mvp->getId());
            if (\in_array($mvpBrandId, $rules->getIncludedBrands())) {
                $isIncluded = true;
            }
        }

        if (!$isIncluded) {
            // Les règles d'inclusion n'ont pas matché, inutile de tester les règles d'exclusion.
            return false;
        }

        if (\in_array($mvp->getId(), $rules->getExcludedProducts())) {
            return false;
        }

        if (!empty(array_intersect($categoryIds, $rules->getExcludedCategories()))) {
            return false;
        }

        if (!isset($mvpBrandId)) {
            $mvpBrandId = $this->getBrandIdFromMvpId($mvp->getId());
        }

        if (\in_array($mvpBrandId, $rules->getExcludedBrands())) {
            return false;
        }

        return true;
    }

    private function sync(Rules $rules): void
    {
        $companyId = $rules->getCompanyId();
        $mvpIds = $this->findMVPsMatchingRules($rules);

        // create products
        foreach ($mvpIds as $mvpId) {
            $mvp = $this->multiVendorProductService->get($mvpId);
            $createProduct = true;

            foreach ($mvp->getLinks() as $link) {
                $product = $link->getProduct();

                // is a product, for the company, is already linked to the MVP we do not need to create one
                if ($companyId == $product->getCompanyId()) {
                    $createProduct = false;
                    break;
                }
            }

            if ($createProduct) {
                $this->multiVendorProductService->createProductFromMVP($mvpId, $companyId);
            }
        }


        // delete products
        $products = $this->findNonMatchingProducts($companyId, $mvpIds);

        array_walk($products, function ($product) {
            $this->productService->delete((int) $product['product_id']);
        });
    }

    /**
     * Retourne la liste des produits, attachés à des MVP, mais qui ne sont pas dans la liste des MVP à synchroniser
     */
    private function findNonMatchingProducts(int $companyId, array $mvpIds): array
    {

        $params = [
            'companyId' => $companyId,
            'matchingMVPsIds' => $mvpIds,
        ];
        $types = [
            'companyId' => \PDO::PARAM_INT,
            'matchingMVPsIds' => Connection::PARAM_INT_ARRAY,
        ];
        $sql = 'SELECT p.product_id FROM cscart_products AS p JOIN doctrine_multi_vendor_product_link AS link ON link.product_id = p.product_id WHERE company_id = :companyId';

        if (\count($mvpIds)) {
            $sql .= ' AND link.multi_vendor_product_id NOT IN (:matchingMVPsIds)';
        }

        return $this->dbal->fetchAll($sql, $params, $types);
    }

    private function getBrandIdFromMvpId(string $mvpId): ?int
    {
        $attributes = $this->multiVendorProductService->getDetailedLegacyAttributes($mvpId, true, true);
        list(, $mvpBrandId) = \Wizacha\Product::getBrandInfos($attributes);

        return $mvpBrandId;
    }

    private function getRepository(): ObjectRepository
    {
        return $this->entityManager->getRepository(Rules::class);
    }
}
