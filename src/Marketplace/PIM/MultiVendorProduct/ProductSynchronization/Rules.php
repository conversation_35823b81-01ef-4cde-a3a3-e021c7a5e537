<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\MultiVendorProduct\ProductSynchronization;

/**
 * Rules
 */
class Rules
{
    /**
     * @var int
     */
    private $companyId;

    /**
     * @var int[]
     */
    private $includedCategories = [];

    /**
     * @var int[]
     */
    private $excludedCategories = [];

    /**
     * @var int[]
     */
    private $includedBrands = [];

    /**
     * @var int[]
     */
    private $excludedBrands = [];

    /**
     * @var string[]
     */
    private $includedProducts = [];

    /**
     * @var string[]
     */
    private $excludedProducts = [];


    public function __construct(int $companyId)
    {
        $this->companyId = $companyId;
    }

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    /**
     * @return int[]
     */
    public function getIncludedCategories(): array
    {
        return $this->includedCategories;
    }

    /**
     * @param int[] $includedCategories
     */
    public function setIncludedCategories(array $includedCategories): void
    {
        $this->includedCategories = $includedCategories;
    }

    /**
     * @return int[]
     */
    public function getExcludedCategories(): array
    {
        return $this->excludedCategories;
    }

    /**
     * @param int[] $excludedCategories
     */
    public function setExcludedCategories(array $excludedCategories): void
    {
        $this->excludedCategories = $excludedCategories;
    }

    /**
     * @return int[]
     */
    public function getIncludedBrands(): array
    {
        return $this->includedBrands;
    }

    /**
     * @param int[] $includedBrands
     */
    public function setIncludedBrands(array $includedBrands): void
    {
        $this->includedBrands = $includedBrands;
    }

    /**
     * @return int[]
     */
    public function getExcludedBrands(): array
    {
        return $this->excludedBrands;
    }

    /**
     * @param int[] $excludedBrands
     */
    public function setExcludedBrands(array $excludedBrands): void
    {
        $this->excludedBrands = $excludedBrands;
    }

    /**
     * @return string[]
     */
    public function getIncludedProducts(): array
    {
        return $this->includedProducts;
    }

    /**
     * @param string[] $includedProducts
     */
    public function setIncludedProducts(array $includedProducts): void
    {
        $this->includedProducts = $includedProducts;
    }

    /**
     * @return string[]
     */
    public function getExcludedProducts(): array
    {
        return $this->excludedProducts;
    }

    /**
     * @param string[] $excludedProducts
     */
    public function setExcludedProducts(array $excludedProducts): void
    {
        $this->excludedProducts = $excludedProducts;
    }
}
