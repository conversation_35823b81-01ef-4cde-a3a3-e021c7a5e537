Wizacha\Marketplace\PIM\MultiVendorProduct\ProductSynchronization\Rules:
    type: entity
    table: product_sync_from_mvp_rules
    id:
        companyId:
            type: integer
            column: company_id
            options:
                unsigned: true
    fields:
        includedCategories:
            type: json_array
            nullable: true
            column: included_categories
        excludedCategories:
            type: json_array
            nullable: true
            column: excluded_categories
        includedBrands:
            type: json_array
            nullable: true
            column: included_brands
        excludedBrands:
            type: json_array
            nullable: true
            column: excluded_brands
        includedProducts:
            type: json_array
            nullable: true
            column: included_products
        excludedProducts:
            type: json_array
            nullable: true
            column: excluded_products
