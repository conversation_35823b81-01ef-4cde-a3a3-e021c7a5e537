<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct;

use W<PERSON>cha\Component\Locale\Locale;
use Wizacha\Marketplace\Exception\IntegrityConstraintViolation;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct;

/**
 * This class MUST never be used outside the MultiVendorProduct entity
 */
class Translation
{
    /**
     * @var Locale
     */
    protected $locale;

    /**
     * @var string
     */
    protected $name;

    /**
     * @var string
     */
    protected $shortDescription;

    /**
     * @var string
     */
    protected $description;

    /** @var  string */
    protected $seoTitle;

    /** @var  string */
    protected $seoDescription;

    /** @var  string */
    protected $seoKeywords;

    /**
     * @var MultiVendorProduct
     */
    protected $multiVendorProduct;

    public function __construct(MultiVendorProduct $mvp, Locale $locale)
    {
        $this->multiVendorProduct = $mvp;
        $this->locale = $locale;
        //when the translation is created in the mvp we can't know the name
        //so we add empty ones so getters works
        $this->loadData([
            'name' => '',
        ]);
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getShortDescription(): string
    {
        return $this->shortDescription;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function getSeoTitle(): string
    {
        return (string) $this->seoTitle;
    }

    public function getSeoDescription(): string
    {
        return (string) $this->seoDescription;
    }

    public function getSeoKeywords(): string
    {
        return (string) $this->seoKeywords;
    }

    public function loadData(array $data): self
    {
        $this->checkIntegrity($data);

        $this->name = $data['name'];
        $this->shortDescription = $data['shortDescription'] ?? '';
        $this->description = $data['description'] ?? '';
        $this->seoTitle = $data['seoTitle'];
        $this->seoDescription = $data['seoDescription'];
        $this->seoKeywords = $data['seoKeywords'];

        return $this;
    }

    public function mergeData(array $data): self
    {
        $fields = ['name', 'shortDescription', 'description', 'seoTitle', 'seoDescription', 'seoKeywords'];

        foreach ($fields as $field) {
            if (isset($data[$field])) {
                $this->$field = $data[$field];
            }
        }

        return $this;
    }

    public function isCurrentLocale(): bool
    {
        return $this->isLocaleEquals(GlobalState::contentLocale());
    }

    public function isLocaleEquals(Locale $locale): bool
    {
        return $this->locale->equals($locale);
    }

    /**
     * @Deprecated
     * @param array $data
     */
    protected function checkIntegrity(array $data): void
    {
        // Deprecated
    }
}
