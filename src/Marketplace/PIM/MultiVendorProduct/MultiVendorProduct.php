<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\MultiVendorProduct;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Wizacha\Component\Locale\Locale;
use Wizacha\Marketplace\Exception\IntegrityConstraintViolation;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Image\Image;
use Wizacha\Marketplace\PIM\Category\Category;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct\Translation;
use Wizacha\Marketplace\PIM\MultiVendorProduct\Status\Status;
use Wizacha\Marketplace\PIM\Product\Product;
use Wizacha\Marketplace\Seo\SlugTargetType;
use Wizacha\Marketplace\SeoData;
use Wizacha\Marketplace\Traits;

class MultiVendorProduct
{
    use Traits\HasTimestamp;
    use Traits\HasUuid;

    /**
     * @const string[]
     */
    public const STATUSES_ALLOWING_PRODUCT_PAGE = [
        Status::ENABLED,
        Status::HIDDEN,
    ];

    /**
     * @const string[]
     */
    public const SEARCHABLE_STATUSES = [
        Status::ENABLED,
    ];

    /**
     * @var string
     */
    protected $code;

    /**
     * @var string|null
     */
    protected $supplierReference;

    /**
     * @var Status
     */
    protected $status;

    /**
     * @var int[]
     */
    protected $imageIds;

    /**
     * @var Category
     */
    protected $category;

    /**
     * @var Collection|Link[]
     */
    protected $links;

    /**
     * @var Collection|Translation[]
     */
    protected $translations;

    /**
     * @var string|null
     */
    protected $slug;

    /**
     * @var string
     */
    protected $productTemplateType;

    /**
     * @var string|null
     */
    protected $video;

    public static function isMultiVendorProductId($id): bool
    {
        if (!\is_string($id)) {
            return false;
        }

        return preg_match('/^[0-9A-Fa-f]{8}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{12}$/', $id) === 1;
    }

    /**
     * @throws IntegrityConstraintViolation
     */
    public function __construct(array $data)
    {
        // Il est impossible de créer un MVP avec des liens déjà associés,
        // il est ainsi nécessaire d'utiliser le service pour réaliser les liens.
        $this->links = new ArrayCollection();
        $this->translations = new ArrayCollection();
        $this->loadData($data);
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function getSupplierReference(): ?string
    {
        return $this->supplierReference;
    }

    public function getName(): string
    {
        return $this->getTranslation()->getName();
    }

    public function getSlug(): ?string
    {
        return $this->slug;
    }

    public function getShortDescription(): string
    {
        return $this->getTranslation()->getShortDescription();
    }

    public function getDescription(): string
    {
        return $this->getTranslation()->getDescription();
    }

    public function getSeoTitle(): string
    {
        return $this->getTranslation()->getSeoTitle();
    }

    public function getSeoDescription(): string
    {
        return $this->getTranslation()->getSeoDescription();
    }

    public function getSeoKeywords(): string
    {
        return $this->getTranslation()->getSeoKeywords();
    }

    public function getStatus(): Status
    {
        return $this->status;
    }

    public function canHaveAProductPage(): bool
    {
        return \in_array($this->status->getValue(), self::STATUSES_ALLOWING_PRODUCT_PAGE);
    }

    public function isSearchable(): bool
    {
        return \in_array($this->status->getValue(), self::SEARCHABLE_STATUSES);
    }

    public function getProductTemplateType(): string
    {
        return $this->productTemplateType;
    }

    public function getVideo(): ?string
    {
        return $this->video;
    }

    public function getImageIds(): array
    {
        return array_filter($this->imageIds);
    }

    public function addImageId(int $imageId): bool
    {
        if (\in_array($imageId, $this->imageIds)) {
            return false;
        }

        $this->imageIds[] = $imageId;

        return true;
    }

    public function removeImageId(int $imageId): bool
    {
        if (($key = array_search($imageId, $this->imageIds)) !== false) {
            // Supprime l'élément
            unset($this->imageIds[$key]);

            // Remise à zéro des index du tableau
            $this->imageIds = array_values($this->imageIds);

            return true;
        }

        return false;
    }

    public function getCategory(): Category
    {
        return $this->category;
    }

    /**
     * @return Collection|Link[]
     */
    public function getLinks(): Collection
    {
        return $this->links;
    }

    public function addLink(Link $link): bool
    {
        if ($this->links->contains($link)) {
            return false;
        }

        return $this->links->add($link);
    }

    public function removeLink(Link $link): bool
    {
        return $this->links->removeElement($link);
    }

    /**
     * @return Product[]
     */
    public function getProducts(): array
    {
        $products = [];

        foreach ($this->getLinks() as $link) {
            $products[] = $link->getProduct();
        }

        return $products;
    }

    /**
     * @return int[]
     */
    public function getProductIds(): array
    {
        return array_map(function (Product $product): int {
            return $product->getId();
        }, $this->getProducts());
    }

    /**
     * @throws IntegrityConstraintViolation
     */
    public function loadData(array $data, MultiVendorProduct $existingResource = null): MultiVendorProduct
    {
        static::checkIntegrity($data);

        $this->updateTimestamp();

        if ($existingResource instanceof MultiVendorProduct) {
            $this->loadDataFromExistingResource($data, $existingResource);
        }

        $this->id = \is_string($data['id']) ? $data['id'] : $this->id;
        $this->code = \is_string($data['code']) ? $data['code'] : $this->code;
        $this->supplierReference = \is_string($data['supplierReference']) ? $data['supplierReference'] : $this->supplierReference;
        $this->status = \is_string($data['status']) ? new Status($data['status']) : $this->status;
        $this->category = $data['category'] instanceof Category ? $data['category'] : $this->category;
        $this->slug = \is_string($data['slug']) ? $data['slug'] : $this->slug;
        $this->video = \is_string($data['video']) ? $data['video'] : $this->video;

        if (\is_string($data['productTemplateType'])) {
            $this->productTemplateType = $data['productTemplateType'];
        } elseif (\is_null($this->productTemplateType)) {
            $this->productTemplateType = 'product';
        }

        if (\is_array($data['imageIds'])) {
            $this->imageIds = array_map(function (int $imageId): int {
                return $imageId;
            }, $data['imageIds']);
        } elseif (\is_null($this->imageIds) || (\is_string($data['images_urls']) && \is_null($data['imageIds']))) {
            $this->imageIds = [];
        }

        $this->getTranslation($data['locale'])->loadData([
            'name' => $data['name'] ?? $this->getTranslation($data['locale'])->getName(),
            'shortDescription' => $data['shortDescription'] ?? $this->getTranslation($data['locale'])->getShortDescription(),
            'description' => $data['description'] ?? $this->getTranslation($data['locale'])->getDescription(),
            'seoTitle' => $data['seoTitle'] ?? $this->getTranslation($data['locale'])->getSeoTitle(),
            'seoDescription' => $data['seoDescription'] ?? $this->getTranslation($data['locale'])->getSeoDescription(),
            'seoKeywords' => $data['seoKeywords'] ?? $this->getTranslation($data['locale'])->getSeoKeywords(),
        ]);

        return $this;
    }

    public function loadDataFromExistingResource(array $data, MultiVendorProduct $existingResource): MultiVendorProduct
    {
        $this->id = $existingResource->getId();
        $this->code = $existingResource->getCode();
        $this->supplierReference = $existingResource->getSupplierReference();
        $this->status = $existingResource->getStatus();
        $this->category = $existingResource->getCategory();
        $this->imageIds = $existingResource->getImageIds();
        $this->slug = $existingResource->getSlug();
        $this->productTemplateType = $existingResource->getProductTemplateType();
        $this->video = $existingResource->getVideo();

        $this->getTranslation($data['locale'])->loadData([
            'name' => $existingResource->getName(),
            'shortDescription' => $existingResource->getShortDescription(),
            'description' => $existingResource->getDescription(),
            'seoTitle' => $existingResource->getSeoTitle(),
            'seoDescription' => $existingResource->getSeoDescription(),
            'seoKeywords' => $existingResource->getSeoKeywords(),
        ]);

        return $this;
    }

    /**
     * @throws IntegrityConstraintViolation
     */
    public function mergeData(array $data): MultiVendorProduct
    {
        $this->updateTimestamp();

        if (isset($data['code'])) {
            $this->code = $data['code'];
        }

        if (isset($data['supplierReference'])) {
            $this->supplierReference = $data['supplierReference'];
        }

        if (isset($data['status'])) {
            $this->status = new Status($data['status']);
        }

        if (isset($data['category'])) {
            $this->category = $data['category'];
        }

        if (isset($data['imageIds'])) {
            $this->imageIds = array_map(function ($imageId) {
                return (int) $imageId;
            }, $data['imageIds']);
        }

        if (isset($data['slug'])) {
            $this->slug = $data['slug'];
        }

        if (isset($data['productTemplateType'])) {
            $this->productTemplateType = $data['productTemplateType'];
        }

        if (\array_key_exists('video', $data)) {
            $this->video = $data['video'] ?? null;
        }

        $this->getTranslation()->mergeData($data);

        return $this;
    }

    /**
     * @param MultiVendorProductService $multiVendorProductService it is necessary to get the attributes in the exposed data
     */
    public function expose(MultiVendorProductService $multiVendorProductService): array
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'code' => $this->getCode(),
            'productTemplateType' => $this->getProductTemplateType(),
            'supplierReference' => $this->getSupplierReference(),
            'slug' => $this->getSlug(),
            'shortDescription' => $this->getShortDescription(),
            'description' => $this->getDescription(),
            'seoTitle' => $this->getSeoTitle(),
            'seoDescription' => $this->getSeoDescription(),
            'seoKeywords' => $this->getSeoKeywords(),
            'status' => (string) $this->getStatus(),
            'category' => [
                'id' => $this->getCategory()->getId(),
                'name' => $this->getCategory()->getName(),
            ],
            'categoryId' => $this->getCategory()->getId(), // ajouté pour la transitivité de l'entité
            'imageIds' => $multiVendorProductService->getValidImageIds($this->getImageIds()),
            'images' => array_map(
                function ($image) {
                    return [
                        'id' => $image->getId(),
                        'altText' => $image->getAltText()
                    ];
                },
                $this->getImagesData($multiVendorProductService)
            ),
            'attributes' => $multiVendorProductService->getLegacyAttributesValues($this->getId()),
            'freeAttributes' => $multiVendorProductService->getFreeLegacyAttributes($this->getId()),
            'video' => $multiVendorProductService->getVideo($this->getVideo())->expose(),
        ];
    }

    /** @param MultiVendorProductService $multiVendorProductService it is necessary to get the attributes in the exposed data */
    public function exposeTranslatedData(MultiVendorProductService $multiVendorProductService): array
    {
        return [
            'id' => $this->getId(),
            'name' => $this->getTranslation(GlobalState::interfaceLocale())->getName(),
            'code' => $this->getCode(),
            'productTemplateType' => $this->getProductTemplateType(),
            'supplierReference' => $this->getSupplierReference(),
            'slug' => $this->getSlug(),
            'shortDescription' => $this->getTranslation(GlobalState::interfaceLocale())->getShortDescription(),
            'description' => $this->getTranslation(GlobalState::interfaceLocale())->getDescription(),
            'seoTitle' => $this->getTranslation(GlobalState::interfaceLocale())->getSeoTitle(),
            'seoDescription' => $this->getTranslation(GlobalState::interfaceLocale())->getSeoDescription(),
            'seoKeywords' => $this->getTranslation(GlobalState::interfaceLocale())->getSeoKeywords(),
            'status' => (string) $this->getStatus(),
            'category' => [
                'id' => $this->getCategory()->getId(),
                'name' => $this->getCategory()->getName(),
            ],
            'categoryId' => $this->getCategory()->getId(), // ajouté pour la transitivité de l'entité
            'imageIds' => $this->getImageIds(),
            'attributes' => $multiVendorProductService->getLegacyAttributesValues($this->getId()),
            'freeAttributes' => $multiVendorProductService->getFreeLegacyAttributes($this->getId()),
            'video' => $multiVendorProductService->getVideo($this->getVideo())->expose(),
        ];
    }

    /**
     * @throws IntegrityConstraintViolation
     */
    public static function checkIntegrity(array $data)
    {
        static::checkUuidIntegrity($data['id']);

        if (!isset($data['name'])) {
            throw IntegrityConstraintViolation::isMissing('name');
        }

        if (!isset($data['slug'])) {
            throw IntegrityConstraintViolation::isMissing('slug');
        }

        if (!isset($data['code'])) {
            throw IntegrityConstraintViolation::isMissing('code');
        }

        if (!isset($data['status'])) {
            throw IntegrityConstraintViolation::isMissing('status');
        }

        if (!Status::isValid($data['status'])) {
            throw IntegrityConstraintViolation::isInvalid('status');
        }

        if (!\array_key_exists('category', $data)) {
            throw IntegrityConstraintViolation::isMissing('category');
        }

        if (!$data['category'] instanceof Category) {
            throw IntegrityConstraintViolation::isInvalid('category');
        }

        if (isset($data['attributes']) && !\is_array($data['attributes'])) {
            throw IntegrityConstraintViolation::isInvalid('attributes');
        }

        if (isset($data['freeAttributes']) && !\is_array($data['freeAttributes'])) {
            throw IntegrityConstraintViolation::isInvalid('free attributes');
        }
    }

    public function getSeoData(): SeoData
    {
        return new SeoData(
            $this->getSeoTitle(),
            $this->getSeoDescription(),
            $this->getSeoKeywords(),
            $this->getSlug()
        );
    }

    /**
     * @internal Called by doctrine ( see lifecycleCallbacks in yaml )
     */
    public function loadSlug()
    {
        $stmt = container()->get('doctrine.dbal.default_connection')->executeQuery(
            'SELECT name FROM cscart_seo_names WHERE object_id = :id AND type = :type',
            [
                'id' => $this->getId(),
                'type' => SlugTargetType::PRODUCT,
            ]
        );

        $this->slug = $stmt->fetchColumn() ?: null;
    }

    /**
     * @param Locale|null $locale
     * @return Translation
     */
    private function getTranslation(Locale $locale = null): Translation
    {
        $locale = $locale ?? GlobalState::contentLocale();

        foreach ($this->translations as $translation) {
            if ($translation->isLocaleEquals($locale)) {
                return $translation;
            }
        }

        $this->translations->add(
            $translation = new Translation($this, $locale)
        );

        return $translation;
    }

    /** @return array */
    public function getImagesData(MultiVendorProductService $mvpService): array
    {
        $images = [];
        $imageIds = $mvpService->getValidImageIds($this->getImageIds());
        foreach ($imageIds as $imageId) {
            $images[] = new Image($imageId, $mvpService->getImageAltText($imageId, (string) GlobalState::contentLocale()));
        }

        return $images;
    }
}
