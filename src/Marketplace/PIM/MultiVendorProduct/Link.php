<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\MultiVendorProduct;

use Wizacha\Marketplace\Exception\IntegrityConstraintViolation;
use Wizacha\Marketplace\PIM\Product\Product;
use Wizacha\Marketplace\Traits;

/*
 * Les Links sont les liens entre les MVP et les produits.
 * Ils sont plus qu'un simple élément de jointure car ils contiendront aussi les information de modération.
 * Même si c'est une table de jointure n-n entre les MVP et les produits, elle est utilisé en 1-n (1 MVP - n produits)
 */
class Link
{
    use Traits\HasTimestamp;

    /**
     * @var MultiVendorProduct
     */
    protected $multiVendorProduct;

    /**
     * @var Product
     */
    protected $product;

    public function __construct(array $data)
    {
        $this->loadData($data);
    }

    public function getMultiVendorProduct(): MultiVendorProduct
    {
        return $this->multiVendorProduct;
    }

    public function getProduct(): Product
    {
        return $this->product;
    }

    public function loadData(array $data): Link
    {
        static::checkIntegrity($data);

        $this->updateTimestamp();

        $this->product = $data['product'];
        $this->multiVendorProduct = $data['multi_vendor_product'];

        $this->product->setLink($this);
        $this->multiVendorProduct->addLink($this);

        return $this;
    }

    public function expose(): array
    {
        return [
            'product' => [
                'id' => $this->product->getId(),
            ],
            'multi_vendor_product' => [
                'id' => $this->multiVendorProduct->getId(),
                'name' => $this->multiVendorProduct->getName(),
                'code' => $this->multiVendorProduct->getCode(),
            ],
        ];
    }

    public static function checkIntegrity(array $data)
    {
        if (!\array_key_exists('product', $data)) {
            throw IntegrityConstraintViolation::isMissing('product');
        }

        if (!$data['product'] instanceof Product) {
            throw IntegrityConstraintViolation::isInvalid('product');
        }

        if (!\array_key_exists('multi_vendor_product', $data)) {
            throw IntegrityConstraintViolation::isMissing('multi_vendor_product');
        }

        if (!$data['multi_vendor_product'] instanceof MultiVendorProduct) {
            throw IntegrityConstraintViolation::isInvalid('multi_vendor_product');
        }
    }
}
