<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\MultiVendorProduct\Status;

use Acelaya\Doctrine\Type\AbstractPhpEnumType;
use Doctrine\DBAL\Platforms\AbstractPlatform;

class StatusType extends AbstractPhpEnumType
{
    protected $enumType = Status::class;

    public function getSQLDeclaration(array $fieldDeclaration, AbstractPlatform $platform)
    {
        return sprintf(
            "%s%s",
            $platform->getVarcharTypeDeclarationSQL(['length' => 1, 'fixed' => true]), // CHAR(1)
            $platform->getDefaultValueDeclarationSQL(['default' => 'H']) // DEFAULT 'H'
        );
    }

    protected function getSpecificName(): string
    {
        return 'multi_vendor_product_status';
    }
}
