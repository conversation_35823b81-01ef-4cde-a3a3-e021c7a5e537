<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\MultiVendorProduct\Status;

use MyCLabs\Enum\Enum;

/**
 * @method static Status ENABLED()
 * @method static Status DISABLED()
 * @method static Status HIDDEN()
 */
class Status extends Enum
{
    public const ENABLED   = 'A';
    public const DISABLED  = 'D';
    public const HIDDEN    = 'H';
}
