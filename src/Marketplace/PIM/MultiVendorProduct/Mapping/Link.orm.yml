Wizacha\Marketplace\PIM\MultiVendorProduct\Link:
    type: entity
    table: multi_vendor_product_link
    id:
        multiVendorProduct:
            associationKey: true
        product:
            associationKey: true
    fields:
        createdAt:
            type: datetime
            column: created_at
            nullable: false
        updatedAt:
            type: datetime
            column: updated_at
            nullable: false
    manyToOne:
        multiVendorProduct:
            targetEntity: Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct
            inversedBy: links
            joinColumn:
                nullable: false
    oneToOne:
        product:
            targetEntity: Wizacha\Marketplace\PIM\Product\Product
            inversedBy: link
            joinColumn:
                name: product_id
                referencedColumnName: product_id
                nullable: false
    uniqueConstraints:
        unique_product_index:
            columns: [ product_id ]
