Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct:
    type: entity
    id:
        id:
            type: guid
    fields:
        code:
            type: string
            size: 255
            nullable: false
        status:
            type: multi_vendor_product_status
            nullable: false
            default: D
        imageIds:
            type: json_array
            column: image_ids
            nullable: true
        createdAt:
            type: datetime
            column: created_at
            nullable: false
        updatedAt:
            type: datetime
            column: updated_at
            nullable: false
        supplierReference:
            type: string
            size: 255
            nullable: true
        productTemplateType:
            type: string
            size: 255
            nullable: true
        video:
            type: string
            size: 255
            nullable: true
    manyToOne:
        category:
            targetEntity: Wizacha\Marketplace\PIM\Category\Category
            inversedBy: multiVendorProducts
            fetch: EAGER
            joinColumn:
                name: category_id
                referencedColumnName: category_id
                nullable: false
    oneToMany:
        links:
            targetEntity: Wizacha\Marketplace\PIM\MultiVendorProduct\Link
            mappedBy: multiVendorProduct
            cascade: [ all ]
            fetch: EAGER
            orphanRemoval: true
        translations:
            targetEntity: Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct\Translation
            mappedBy: multiVendorProduct
            cascade: [ all ]
            fetch: EAGER
    lifecycleCallbacks:
        postLoad: [ loadSlug ]
