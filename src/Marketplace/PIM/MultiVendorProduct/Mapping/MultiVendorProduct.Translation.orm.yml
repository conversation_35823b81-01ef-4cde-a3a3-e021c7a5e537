Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct\Translation:
    type: entity
    table: multi_vendor_product_translations
    id:
        multiVendorProduct:
            associationKey: true
        locale:
            type: locale
            length: 10
    fields:
        name:
            type: string
            size: 255
            nullable: false
        shortDescription:
            type: text
            column: short_description
            nullable: true
        description:
            type: text
            nullable: true
        seoTitle:
            type: string
            size: 255
            nullable: true
            column: seo_title
        seoDescription:
            type: string
            size: 255
            column: seo_description
            nullable: true
        seoKeywords:
            type: string
            size: 255
            column: seo_keywords
            nullable: true
    manyToOne:
        multiVendorProduct:
            targetEntity: Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct
            inversedBy: translations
            joinColumn:
                nullable: false
