<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\PIM;

use Doctrine\DBAL\Connection;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Product;
use Wizacha\Storage\StorageService;

class ProductFileService
{
    private Connection $connection;

    private string $cscartTablePrefix;

    private ?StorageService $downloadsStorageService;

    public function __construct(Connection $connection, string $cscartTablePrefix, StorageService $downloadsStorageService)
    {
        $this->connection = $connection;
        $this->cscartTablePrefix = $cscartTablePrefix;
        $this->downloadsStorageService = $downloadsStorageService;
    }

    public function attachFile(Product $product, string $pathOnStorage, int $fileSize)
    {
        $filename = $product->getId() . '/' . basename($pathOnStorage);

        $this->downloadsStorageService->copy($pathOnStorage, $filename);
        $this->downloadsStorageService->delete($pathOnStorage);

        $this->connection->insert(
            $this->cscartTablePrefix . 'product_files',
            [
                'product_id' => $product->getId(),
                'file_path' => basename($pathOnStorage),
                'file_size' => $fileSize,
                'agreement' => 'N',
                'activation_type' => 'P',
                'status' => 'A',
            ]
        );
        $this->connection->insert(
            $this->cscartTablePrefix . 'product_file_descriptions',
            [
                'file_id' => $this->connection->lastInsertId(),
                'lang_code' => (string) GlobalState::contentLocale(),
                'file_name' => basename($pathOnStorage),
            ],
            []
        );
    }

    public function saveTmpFile(UploadedFile $file): array
    {
        $filename = 'tmp/' . $file->getClientOriginalName();

        list($filesize, $filename) = $this->downloadsStorageService->put($filename, [
            'file' => $file->getPathname(),
        ]);

        return [
            'filesize' => $filesize,
            'filename' => $filename,
        ];
    }
}
