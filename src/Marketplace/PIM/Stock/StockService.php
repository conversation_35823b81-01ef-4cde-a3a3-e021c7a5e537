<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\PIM\Stock;

use Doctrine\DBAL\Connection;
use Wizacha\Cache\RedisDecorator;
use Wizacha\Marketplace\Entities\Declination;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Product;

/**
 * Gestion des stocks des produits et déclinaisons.
 */
class StockService
{
    /**
     * @var Connection
     */
    protected $connection;

    /**
     * @var RedisDecorator|\Redis
     */
    protected $redisClient;

    /**
     * @var integer
     */
    protected $timeout;

    /**
     * @var string
     */
    private $prefix;

    /**
     * Redis client is not typed for problematique of class between prod and dev environment.
     * @param \Redis $redisClient
     * @param int $timeout = 900 (in seconds)
     */
    public function __construct(Connection $connection, $redisClient, string $prefix, int $timeout = 900)
    {
        $this->connection = $connection;
        $this->redisClient = $redisClient;
        $this->timeout = $timeout;
        $this->prefix = $prefix;
    }

    /**
     * Réserve temporairement un produit ou une déclinaison.
     *
     * Affecte uniquement le stock temps réel. Le stock BDD n'est pas changé.
     *
     * @param integer $quantity
     * @param string $bookingId
     * @return integer Quantity really booked
     */
    public function book(
        Declination $item,
        int $quantity,
        string $bookingId
    ): int {
        if (true === $item->hasInfiniteStock()) {
            return $quantity;
        }

        $this->removeBooking($item, $bookingId);
        $availableQuantity = \min(
            $this->getRealTimeStock($item),
            $quantity
        );
        $this->redisClient->setex(
            $this->getKey($item, $bookingId),
            $this->timeout,
            $availableQuantity
        );

        return $availableQuantity;
    }

    /**
     * Annule une réservation de produit/déclinaison.
     *
     * Affecte uniquement le stock temps réel. Le stock BDD n'est pas changé.
     *
     * @param string $bookingId
     */
    public function removeBooking(Declination $item, $bookingId)
    {
        $this->redisClient->del($this->getKey($item, $bookingId));
    }

    /**
     * Retourne le stock temps réel qui prend en compte le stock BDD + les réservations temporaires via les paniers.
     */
    public function getRealTimeStock(Declination $item): int
    {
        $keys = $this->redisClient->keys(preg_quote($this->getKeyPrefix($item)) . '*');
        $values = $this->redisClient->mget(\preg_replace('/^' . $this->prefix . '/', '', $keys));

        return $item->getAmount() - (!empty($values) ? array_sum($values) : 0);
    }

    /**
     * @param Declination[] $declinations
     * @return int[]
     */
    public function getMultipleRealtimeStock(array $declinations): array
    {
        // List all bookings' keys
        $keys = $this->redisClient->keys('booking.*');

        // Init return data with DB stock data
        $returnData = [];
        foreach ($declinations as $declination) {
            /** @var Declination $declination */
            $returnData[$declination->getId()] = $declination->getAmount();
        }

        // Keep only the bookings' keys we are interested in
        $keysToGet = [];
        $declinationIdsBooked = [];
        foreach ($keys as $key) {
            list(, , ,$keyDeclinationId, ) = explode('.', $key, 5);
            if (isset($returnData[$keyDeclinationId])) {
                $keysToGet[] = $key;
                $declinationIdsBooked[] = $keyDeclinationId; // Yes, we can store more than once the same ID
            }
        }
        // Get the actual bookings
        $values = $this->redisClient->mget(\preg_replace('/^' . $this->prefix . '/', '', $keysToGet));

        // And, for every booked declination, we subtract the Redis value
        foreach ($declinationIdsBooked as $k => $declinationId) {
            $returnData[$declinationId] -= (int) $values[$k];
        }

        return $returnData;
    }

    /**
     * Met à jour le stock d'une déclinaison,
     * qu'elle soit réelle ou virtuelle (créée dynamiquement depuis un produit child-less).
     *
     * @return bool La déclinaison a-t-elle été modifiée
     */
    public function setDeclinationStock(Declination $declination, int $stock): bool
    {
        // Si le stock est déjà au bon niveau
        if ($declination->getAmount() == $stock) {
            return false;
        }

        // Si c'est une déclinaison standard
        if ($declination->hasDeclinationDefinition()) {
            $sql = "update cscart_product_options_inventory set amount = :amount where product_id = :productId and combination = :combination";
        // Si c'est une déclinaison 'virtuelle', représentant un produit
        } else {
            $sql = "update cscart_products set amount = :amount where product_id = :productId";
        }

        $params = [
            'amount' => $stock,
            'productId' => $declination->getProductId(),
            'combination' => $declination->getCombinationCode(),
        ];

        return (bool) $this->connection->executeUpdate($sql, $params);
    }

    public function decrementStock(Order $order)
    {
        $error = false;
        $updated = [];

        foreach ($order->getItems() as $item) {
            $extras = (array) unserialize($item->getExtra());

            if (isset($extras['is_edp']) && $extras['is_edp'] === 'Y') {
                continue;
            }

            if (false === fn_get_product_data($item->getProductId(), $_SESSION['auth'])) {
                // Product does not exists
                continue;
            }

            if (true === $item->isSubscription() && \is_string($order->getSubscriptionId())) {
                // C'est un produit de type souscription et la commande est en cours de renouvellement
                // Il n'est donc pas utile de décrémenter le stock
                continue;
            }

            if (fn_update_product_amount($item->getProductId(), $item->getAmount(), $extras['product_options'] ?? [], '-') === false) {
                $error = true;
                fn_set_notification('W', __('warning'), __('low_stock_subj', [
                    '[product]' => $item->getProductName() . ' #' . $item->getProductId(),
                ]));
                fn_change_order_status($order->getId(), OrderStatus::STANDBY_SUPPLYING);

                break;
            }

            $this->removeBooking(
                new Declination(
                    new Product($item->getProductId()),
                    $item->getCombination()
                ),
                $order->getBasketId()
            );

            $updated[] = $item;
        }

        if ($error) {
            foreach ($updated as $item) {
                fn_update_product_amount($item->getProductId(), $item->getAmount(), $extras['product_options'] ?? [], '+');
            }
        }
    }

    private function getKeyPrefix(Declination $item): string
    {
        return sprintf("booking.%s.%s.", \get_class($item), $item->getId());
    }

    private function getKey(Declination $item, $bookingId): string
    {
        return $this->getKeyPrefix($item) . $bookingId;
    }
}
