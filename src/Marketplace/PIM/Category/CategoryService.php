<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Category;

use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Wizacha\Marketplace\Catalog\Product\ProductService;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\GlobalState\GlobalState;

class CategoryService
{
    /**
     * @var Connection
     */
    private $connection;

    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    /**
     * @var ProductService
     */
    private $productService;

    public function __construct(EntityManagerInterface $entityManager, Connection $connection, ProductService $productService)
    {
        $this->entityManager = $entityManager;
        $this->connection = $connection;
        $this->productService = $productService;
    }

    public function get(int $id): Category
    {
        if (!$resource = $this->getRepository()->find($id)) {
            throw NotFound::fromId('Category', $id);
        }

        return $resource;
    }

    /**
     * Returns category names indexed by the category ID.
     *
     * If a category doesn't exist it isn't returned in the array.
     *
     * @param int[] $ids
     * @return string[] Array indexed by category ID.
     */
    public function getCategoryNamesFromIds(array $ids): array
    {
        $query = 'SELECT category_id, category FROM cscart_category_descriptions WHERE category_id IN (?) AND lang_code = ?';
        $rows = $this->connection->fetchAll($query, [$ids, (string) GlobalState::contentLocale()], [
            Connection::PARAM_INT_ARRAY,
            \PDO::PARAM_STR,
        ]);

        return array_combine(array_column($rows, 'category_id'), array_column($rows, 'category'));
    }

    public function getCategories()
    {
        [$categories, ] = fn_get_categories([], (string) GlobalState::contentLocale());

        return $categories;
    }

    /**
     * Met a jour le nombre de produit par catégories
     * Toutes les catégories en dessous de $categoryId NE seront PAS recalculées: doit etre appelé que sur des catégories feuilles
     * Les catégories parentes seront mise à jour uniquement avec le diff avant / apres ( pas de recalcul global )
     *
     * @param array $categoryIds List of categories identifiers for update
     */
    public function updateProductCount(array $categoryIds)
    {
        $categoryIds = array_filter(array_map('intval', $categoryIds));

        foreach ($categoryIds as $categoryId) {
            if ($this->connection->fetchColumn('SELECT COUNT(*) FROM cscart_categories WHERE parent_id = ?', [$categoryId])) {
                // On saute les catégories non feuille. Vaut mieux pas mettre a jour le compteur plutot que de mal le mettre a jour
                continue;
            }

            try {
                $this->connection->beginTransaction();

                $current = $this->connection->fetchAssoc('SELECT product_count, visible_product_count, id_path FROM cscart_categories WHERE category_id = ?', [$categoryId]);
                // 310 = 0 / 0
                // 311 = 0 / 0
                // On initialise le compteur avec le nombre de produit de $category_id
                $count = (int) $this->connection->fetchColumn('SELECT COUNT(*) FROM cscart_products_categories WHERE category_id = ?', [$categoryId]);
                // 310 = 21
                // 311 = 0
                // Et un deuxieme compteur avec le nombre de produits actif, dans la catégorie active
                echo "$categoryId have count : $count";
                $countVisible = $this->productService->getSearcheableProductIdInFront([], [$categoryId])->count();

                $this->connection->executeUpdate('UPDATE cscart_categories SET product_count = ?, visible_product_count = ? WHERE category_id = ?', [
                    $count,
                    $countVisible,
                    $categoryId,
                ]);

                // J'ai le nombre de produit de la catégorie voulue
                // Sauf que, en faisant changer ce compteur j'impacte le compteur des catégories au dessus de moi!
                // je calcule la diff de produit entre avant et après et je répercute ca a tous mes parents!
                $diff = $count - $current['product_count'];
                $diffvisible = $countVisible - $current['visible_product_count'];
                foreach (explode('/', $current['id_path']) as $parentId) {
                    if ($parentId == $categoryId) {
                        // on est allé trop loin dans l'id_path, on sort! (On le parcours de la racine vers les enfants)
                        break;
                    }

                    $this->connection->executeUpdate(
                        'UPDATE cscart_categories
                        SET product_count = GREATEST(CAST(product_count AS signed) + ?, 0),
                        visible_product_count = GREATEST(CAST(visible_product_count AS signed) + ?, 0)
                        WHERE category_id = ?',
                        [
                            $diff,
                            $diffvisible,
                            $parentId,
                        ]
                    );
                }

                $this->connection->commit();
            } catch (\Exception $e) {
                $this->connection->rollBack();
                throw $e;
            }
        }
    }

    protected function getRepository(): EntityRepository
    {
        return $this->entityManager->getRepository(Category::class);
    }
}
