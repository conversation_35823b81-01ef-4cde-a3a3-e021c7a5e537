<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Category;

use Doctrine\Common\Collections\Collection;
use Wizacha\Marketplace\PIM\Product\Product;

class Category
{
    /**
     * @var int
     */
    protected $id;

    /**
     * @var int
     */
    protected $parentId;

    /**
     * @var string
     */
    protected $pathId;

    /**
     * @var string
     */
    protected $status;

    /**
     * @var int
     */
    protected $productCount;

    /**
     * @var int
     */
    protected $position;

    /**
     * @var int
     */
    protected $timestamp;

    /**
     * @var string
     */
    protected $ageVerification;

    /**
     * @var int
     */
    protected $ageLimit;

    /**
     * @var string
     */
    protected $parentAgeVerification;

    /**
     * @var int
     */
    protected $parentAgeLimit;

    /**
     * @var int
     */
    protected $productColumns;

    /**
     * @var string
     */
    protected $transactionMode;

    /**
     * @var string
     */
    protected $transactionModeOverridable;

    /**
     * @var Collection
     */
    protected $multiVendorProducts;

    /**
     * @var Collection
     */
    protected $descriptions;

    /**
    * @var Collection|Product[]
    */
    protected $products;

    protected function __construct()
    {
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getMultiVendorProducts(): Collection
    {
        return $this->multiVendorProducts;
    }

    public function getName(): string
    {
        foreach ($this->descriptions->toArray() as $description) {
            /** @var Description $description */
            if ($description->isCurrentLocale()) {
                return $description->getName();
            }
        }

        return '';
    }

    public function getParentId(): int
    {
        return $this->parentId;
    }

    /**
     * Return IDs of the category and its parents, ordered from root to direct parent, delimited by / char
     */
    public function getPathId(): string
    {
        return $this->pathId;
    }

    public function getStatus(): string
    {
        return $this->status;
    }
}
