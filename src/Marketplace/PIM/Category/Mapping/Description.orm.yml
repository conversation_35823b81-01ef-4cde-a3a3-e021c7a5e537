Wizacha\Marketplace\PIM\Category\Description:
    type: entity
    table: category_descriptions
    id:
        category:
            associationKey: true
        locale:
            type: locale
            length: 2
            column: lang_code
            nullable: false
            options:
                default: ''
                fixed: true
    fields:
        name:
            type: string
            length: 255
            column: category
            nullable: false
            options:
                default: ''
        description:
            type: text
            nullable: false
        metaKeywords:
            type: text
            column: meta_keywords
            nullable: false
        metaDescription:
            type: string
            length: 255
            column: meta_description
            nullable: false
            options:
                default: ''
        pageTitle:
            type: string
            length: 255
            column: page_title
            nullable: false
            options:
                default: ''
        ageWarningMessage:
            type: text
            column: age_warning_message
            nullable: false
    manyToOne:
        category:
            targetEntity: Wizacha\Marketplace\PIM\Category\Category
            inversedBy: descriptions
            joinColumn:
                name: category_id
                referencedColumnName: category_id
                nullable: false
