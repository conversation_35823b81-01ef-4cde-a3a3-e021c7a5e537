Wizacha\Marketplace\PIM\Category\Category:
    type: entity
    table: categories
    id:
        id:
            type: integer
            column: category_id
            generator:
                strategy: AUTO
    fields:
        parentId:
            type: integer
            column: parent_id
            nullable: false
            options:
                unsigned: true
                default: 0
        pathId:
            type: string
            size: 255
            column: id_path
            nullable: false
            options:
                default: ''
        status:
            type: string
            length: 1
            nullable: false
            options:
                fixed: true
                default: 'A'
        productCount:
            type: integer
            column: product_count
            nullable: false
            options:
                unsigned: true
                default: 0
        position:
            type: smallint
            nullable: false
            options:
                unsigned: true
                default: 0
        timestamp:
            type: integer
            nullable: false
            options:
                unsigned: true
                default: 0
        ageVerification:
            type: string
            length: 1
            column: age_verification
            nullable: false
            options:
                fixed: true
                default: 'N'
        ageLimit:
            type: smallint
            column: age_limit
            nullable: false
            options:
                default: 0
        parentAgeVerification:
            type: string
            length: 1
            column: parent_age_verification
            nullable: false
            options:
                fixed: true
                default: 'N'
        parentAgeLimit:
            type: smallint
            column: parent_age_limit
            nullable: false
            options:
                default: 0
        transactionMode:
            type: string
            length: 1
            column: transaction_mode
            nullable: true
            options:
                fixed: false
                default: 'H'
        transactionModeOverridable:
            type: string
            length: 1
            column: transaction_mode_overridable
            nullable: false
            options:
                fixed: true
                default: 'N'
    indexes:
        c_status:
            columns: [ status, parent_id ]
        position:
            columns: [ position ]
        parent:
            columns: [ parent_id ]
        id_path:
            columns: [ id_path ]
        age_verification:
            columns: [ age_verification, age_limit ]
        parent_age_verification:
            columns: [ parent_age_verification, parent_age_limit ]
        p_category_id:
            columns: [ category_id, status ]
    oneToMany:
        multiVendorProducts:
            targetEntity: Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct
            mappedBy: category
            cascade: [ persist ]
        descriptions:
            targetEntity: Wizacha\Marketplace\PIM\Category\Description
            mappedBy: category
            cascade: [ all ]
            fetch: EAGER
            orphanRemoval: true
