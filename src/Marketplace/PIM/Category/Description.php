<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Category;

use Wizacha\Component\Locale\Locale;
use Wizacha\Marketplace\GlobalState\GlobalState;

class Description
{
    /**
     * @var Category
     */
    protected $category;

    /**
     * @var Locale
     */
    protected $locale;

    /**
     * @var string
     */
    protected $name;

    /**
     * @var string
     */
    protected $description;

    /**
     * @var string
     */
    protected $metaKeywords;

    /**
     * @var string
     */
    protected $metaDescription;

    /**
     * @var string
     */
    protected $pageTitle;

    /**
     * @var string
     */
    protected $ageWarningMessage;

    protected function __construct()
    {
    }

    public function getCategory(): Category
    {
        return $this->category;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function isCurrentLocale(): bool
    {
        return $this->locale->equals(GlobalState::contentLocale());
    }
}
