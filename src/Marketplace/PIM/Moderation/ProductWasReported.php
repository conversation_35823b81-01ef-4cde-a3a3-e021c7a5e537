<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Moderation;

use Symfony\Contracts\EventDispatcher\Event;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Wizacha\Component\Notification\NotificationEvent;

class ProductWasReported extends Event implements NotificationEvent
{
    /** @var int|string */
    private $productId;

    /** @var string */
    private $visitorEmail;

    /** @var string */
    private $visitorName;

    /** @var string */
    private $visitorMessage;

    /** @var bool */
    private $isMvp;

    public function __construct(
        $productId,
        string $visitorEmail,
        string $visitorName,
        string $visitorMessage,
        bool $isMvp = false
    ) {
        $this->productId = $productId;
        $this->visitorEmail = $visitorEmail;
        $this->visitorName = $visitorName;
        $this->visitorMessage = $visitorMessage;
        $this->isMvp = $isMvp;
    }

    /**
     * @return int|string
     */
    public function getProductId()
    {
        return $this->productId;
    }

    public function getVisitorEmail(): string
    {
        return $this->visitorEmail;
    }

    public function getVisitorName(): string
    {
        return $this->visitorName;
    }

    public function getVisitorMessage(): string
    {
        return $this->visitorMessage;
    }

    public function isMvp(): bool
    {
        return $this->isMvp;
    }

    public static function buildDebugForm(FormBuilder $form)
    {
        $form->add('productId', IntegerType::class);
        $form->add('message', TextareaType::class);
    }

    public static function createFromForm(Form $form)
    {
        return new static((int) $form->getData()['productId'], '<EMAIL>', 'John Doe', $form->getData()['message']);
    }

    public static function getDescription(): string
    {
        return 'product_was_reported';
    }
}
