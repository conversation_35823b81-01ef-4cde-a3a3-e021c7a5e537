<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Moderation;

use Doctrine\DBAL\Connection;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Wizacha\AppBundle\Notification\Exception\UnknownProductModerationStatus;
use Wizacha\Async\Dispatcher;
use Wizacha\Events\IterableEvent;
use Wizacha\Marketplace\PIM\Product\Event\ProductApproved;
use Wizacha\Marketplace\PIM\Product\Event\ProductChangesRequested;
use Wizacha\Marketplace\PIM\Product\Event\ProductModerationInProgressEvent;
use Wizacha\Marketplace\PIM\Product\Event\ProductRejected;
use Wizacha\Premoderation;
use Wizacha\Premoderation\ProductModeration\Exception\ProductAlreadyInModerationException;
use Wizacha\Premoderation\ProductModeration\Exception\ProductNotFoundException;
use Wizacha\Premoderation\ProductModeration\ProductModerationInProgressService;
use Wizacha\Product;

class ModerationService
{
    /**
     * @var ProductModerationInProgressService
     */
    protected $productModerationInProgressService;
    /**
     * @var EventDispatcherInterface
     */
    protected $eventDispatcher;
    /**
     * @var Dispatcher
     */
    protected $jobDispatcher;
    /**
     * @var Connection
     */
    protected $connection;

    public function __construct(
        ProductModerationInProgressService $productModerationInProgressService,
        EventDispatcherInterface $eventDispatcher,
        Dispatcher $jobDispatcher,
        Connection $connection
    ) {
        $this->productModerationInProgressService = $productModerationInProgressService;
        $this->eventDispatcher = $eventDispatcher;
        $this->jobDispatcher = $jobDispatcher;
        $this->connection = $connection;
    }

    /**
     * Change status of all pending products for a company ID
     * @throws UnknownProductModerationStatus
     */
    public function setCompanyPendingProductsStatus(int $companyId, string $status): int
    {
        // Get all products waiting for moderation
        $updateProducts = $this->productModerationInProgressService->getProductsNotInProgressByCompanyId(
            $companyId
        );

        if (0 === $updateProducts->rowCount()) {
            throw new ProductNotFoundException('No product to moderate');
        }

        // Moderate product by product
        $productsId = [];
        while ($product = $updateProducts->fetch()) {
            $productId = (int) $product['product_id'];
            $productsId[] = $productId;

            $this->productModerationInProgressService->create($productId, false);
        }

        $this->productModerationInProgressService->flush();

        foreach ($productsId as $productId) {
            $this->moderateProduct($status, $productId, true);
        }

        // Send mail
        $this->sendMailEvent($status, $companyId, $productsId, null);

        return $updateProducts->rowCount();
    }

    /**
     * Parse product by product for moderating products, sending mails to companies and create
     * data in moderation table
     * @param int[] $productsId
     * @throws ProductAlreadyInModerationException
     * @throws ProductNotFoundException
     */
    public function prepareModerateProduct(
        array $productsId,
        string $status,
        string $reason = '',
        bool $sendMail = true
    ): int {
        // Get product with their moderation status
        $updateProducts = $this->productModerationInProgressService->getProductsWithModerationDetails($productsId);

        if (0 === $updateProducts->rowCount()) {
            throw new ProductNotFoundException('Product not found');
        }
        $companiesProductsCounter = [];
        $inModerationCount = 0;

        /*
          We want call productModerationInProgressService for all product and flush before starting to call
         `moderateProduct`. Else, the line in `doctrine_product_moderation_in_progress` can be flush after the worker handle
         the message for this line.
         We can't use $productId : getProductsWithModerationDetails could be filtered some id. And rewind a dbal statement
         seem hazardous.
         So we store valid id in $productIdToModerate
        */
        $productIdToModerate = [];

        // Moderate product by product
        while ($product = $updateProducts->fetch()) {
            if (null !== $product['moderation_date']) {
                $inModerationCount++;
            } else {
                $this->productModerationInProgressService->create((int) $product['product_id'], false);
                $productIdToModerate[] = (int) $product['product_id'];
                $companiesProductsCounter[$product['company_id']][] = $product['product_id'];
            }
        }

        // If all products are in moderation
        if ($inModerationCount === $updateProducts->rowCount()) {
            throw new ProductAlreadyInModerationException('Product already in moderation');
        }

        $this->productModerationInProgressService->flush();
        foreach ($productIdToModerate as $productId) {
            $this->moderateProduct($status, $productId, true);
        }

        // Send one mail by company
        if (true === $sendMail) {
            $this->sendMailToCompanies($companiesProductsCounter, $reason, $status);
        }

        return $updateProducts->rowCount();
    }

    /**
     * Will approve or disapprove all products in moderation table for more than 24h
     */
    public function processAll(string $reason, bool $approve = true): void
    {
        $status = true === $approve
            ? Premoderation::STATUS_APPROVED
            : Premoderation::STATUS_DISAPPROVED;

        // Get all products in moderation table for more than 24h
        $iterable = $this->productModerationInProgressService->getOldPendingModeration();

        $companiesProductsCounter = [];
        while ($productModeration = $iterable->fetch()) {
            $productId = (int) $productModeration['product_id'];

            // Set product moderation status, and delete data in moderation table
            $this->setProductsStatus([$productModeration['product_id']], $status);
            $this->productModerationInProgressService->delete($productId);
            $companiesProductsCounter[$productModeration['company_id']][] = $productId;
        }

        $this->sendMailToCompanies($companiesProductsCounter, $reason, $status);
    }

    /**
     * Change the premoderation status of a products set.
     * @note Product::EVENT_UPDATE event is triggered
     * @param int[] $productsId
     */
    public function setProductsStatus(array $productsId, string $status): void
    {
        $this->connection->createQueryBuilder()
            ->update('cscart_products', 'p')
            ->set('p.approved', ':status')
            ->where('p.product_id IN(:productsId)')
            ->setParameter('status', $status)
            ->setParameter('productsId', $productsId, \Doctrine\DBAL\Connection::PARAM_INT_ARRAY)
            ->execute();

        $this->eventDispatcher->dispatch(
            (new IterableEvent())->setArray($productsId),
            Product::EVENT_UPDATE
        );
    }

    /**
     * Launch event for sending mails to the companies
     * @param int[] $companies
     * @throws UnknownProductModerationStatus
     */
    protected function sendMailToCompanies(array $companies, string $reason, string $status): void
    {
        foreach ($companies as $companyId => $companyProducts) {
            $this->sendMailEvent($status, $companyId, $companyProducts, $reason);
        }
    }

    /**
     * @param integer[] $companyProducts
     * @throws UnknownProductModerationStatus
     */
    protected function sendMailEvent(
        string $status,
        int $companyId,
        array $companyProducts,
        ?string $reason
    ): void {
        if (Premoderation::STATUS_APPROVED === $status) {
            $this->eventDispatcher->dispatch(
                new ProductApproved($companyId, $companyProducts, $reason ?: null),
                ProductApproved::class
            );
        } elseif (Premoderation::STATUS_DISAPPROVED === $status) {
            $this->eventDispatcher->dispatch(
                new ProductRejected($companyId, $companyProducts, $reason ?: null),
                ProductRejected::class
            );
        } elseif (Premoderation::STATUS_STANDBY === $status) {
            $this->eventDispatcher->dispatch(
                new ProductChangesRequested($companyId, $companyProducts, $reason ?: null),
                ProductChangesRequested::class
            );
        } elseif (Premoderation::STATUS_PENDING === $status) {
            $this->eventDispatcher->dispatch(
                new ProductModerationInProgressEvent($companyId, $companyProducts, $reason ?: null),
                ProductModerationInProgressEvent::class
            );
        } else {
            throw new UnknownProductModerationStatus(
                'Incorrect product moderation status :' . $status
            );
        }
    }

    /**
     * Async method to change product status, and deleting data in moderation table
     */
    public function moderateProduct(string $status, int $productId, bool $asynchronous): bool
    {
        if (true === $asynchronous
            && true === $this->jobDispatcher->delayExec(
                'marketplace.moderation.moderation_service::' . __FUNCTION__,
                \func_get_args()
            )
        ) {
            return true;
        }

        $this->productModerationInProgressService->delete($productId);
        $this->setProductsStatus([$productId], $status);

        return true;
    }
}
