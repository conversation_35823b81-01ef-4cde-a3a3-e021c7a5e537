<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\PIM\Attribute;

class AttributeVariant
{
    private $id;
    private $position;
    private $featureId;
    private $name;
    private $description;

    public function __construct(int $id, int $position, int $featureId, string $name, string $description)
    {
        $this->id = $id;
        $this->position = $position;
        $this->featureId = $featureId;
        $this->name = $name;
        $this->description = $description;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getPosition(): int
    {
        return $this->position;
    }

    public function getFeatureId(): int
    {
        return $this->featureId;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getDescription(): string
    {
        return $this->description;
    }
}
