<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Attribute;

use Wizacha\Marketplace\Exception\IntegrityConstraintViolation;

class Variant
{
    /** @var int */
    protected $id;

    /** @var Attribute */
    protected $attribute;

    /** @var string */
    protected $name;

    /** @var string */
    protected $description;

    /** @var string */
    protected $pageTitle;

    /** @var string */
    protected $metaKeywords;

    /** @var string */
    protected $metaDescription;

    /** @var int */
    protected $position;

    protected string $slug;

    public function __construct(array $data)
    {
        $this->loadData($data);
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id)
    {
        return $this->id = $id;
    }

    public function getAttribute(): ?Attribute
    {
        return $this->attribute;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function getPageTitle(): string
    {
        return $this->pageTitle;
    }

    public function getMetaKeywords(): string
    {
        return $this->metaKeywords;
    }

    public function getMetaDescription(): string
    {
        return $this->metaDescription;
    }

    public function getPosition(): int
    {
        return $this->position;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function loadData(array $data): Variant
    {
        static::checkIntegrity($data);

        $this->id = $data['id'] ?? null;
        $this->attribute = $data['attribute'];
        $this->name = $data['name'];
        $this->description = $data['description'] ?? '';
        $this->pageTitle = $data['pageTitle'] ?? '';
        $this->metaKeywords = $data['metaKeywords'] ?? '';
        $this->metaDescription = $data['metaDescription'] ?? '';
        $this->position = $data['position'] ?? 0;
        $this->slug = $data['slug'] ?? '';

        return $this;
    }

    public function expose(): array
    {
        return [
            'id' => $this->id,
            'attribute' => [
                'id' => $this->attribute->getId(),
                'name' => $this->attribute->getName(),
            ],
            'name' => $this->name,
            'description' => $this->description,
            'pageTitle' => $this->pageTitle,
            'metaKeywords' => $this->metaKeywords,
            'metaDescription' => $this->metaDescription,
            'position' => $this->position,
            'slug' => $this->slug,
        ];
    }

    public static function checkIntegrity(array $data)
    {
        if (\array_key_exists('id', $data) && !\is_int($data['id'])) {
            throw IntegrityConstraintViolation::isInvalid('id');
        }

        if (\array_key_exists('attribute', $data) && !$data['attribute'] instanceof Attribute) {
            throw IntegrityConstraintViolation::isInvalid('attribute');
        }

        if (!\array_key_exists('name', $data)) {
            throw IntegrityConstraintViolation::isMissing('name');
        }

        if (!\is_string($data['name'])) {
            throw IntegrityConstraintViolation::isInvalid('name');
        }

        if (\array_key_exists('description', $data) && !\is_string($data['description'])) {
            throw IntegrityConstraintViolation::isInvalid('description');
        }

        if (\array_key_exists('pageTitle', $data) && !\is_string($data['pageTitle'])) {
            throw IntegrityConstraintViolation::isInvalid('pageTitle');
        }

        if (\array_key_exists('metaKeywords', $data) && !\is_string($data['metaKeywords'])) {
            throw IntegrityConstraintViolation::isInvalid('metaKeywords');
        }

        if (\array_key_exists('metaDescription', $data) && !\is_string($data['metaDescription'])) {
            throw IntegrityConstraintViolation::isInvalid('metaDescription');
        }

        if (\array_key_exists('position', $data) && !\is_int($data['position'])) {
            throw IntegrityConstraintViolation::isInvalid('position');
        }

        if (\array_key_exists('seo_name', $data) && !\is_string($data['seo_name'])) {
            throw IntegrityConstraintViolation::isInvalid('seo_name');
        }
    }
}
