<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Attribute;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\PIM\Attribute\Exception\AttributeCannotHaveMultipleValues;
use Wizacha\Marketplace\PIM\Category\Category;
use Wizacha\Marketplace\PIM\MultiVendorProduct\Event\MultiVendorProductEvent;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct;
use Wizacha\Marketplace\PIM\Product\Product;
use Wizacha\Product as LegacyProduct;
use Wizacha\Status;

/**
 * Fonctions utiles pour la gestion des attributs PIM
 *
 * Pour plus de précisions, voir la [doc](/docs/ATTRIBUTE.md)
 */
trait ManageAttributes
{
    /**
     * Fonctions imposant la présence de certains paramètres utiles
     */
    abstract protected function getAttributeService(): AttributeService;
    abstract protected function getEntityManager(): EntityManagerInterface;
    abstract protected function getEventDispatcher(): EventDispatcherInterface;

    /**
     * Récupération des attributs détaillés d'un Product ou d'un MVP
     *
     * @param int|string $id Product or MVP id
     * @param bool $existentOnly
     * @param bool $variantsSelectedOnly
     * @param null|string $locale
     * @return array
     */
    public function getDetailedLegacyAttributes(
        $id,
        bool $existentOnly = true,
        bool $variantsSelectedOnly = true,
        $locale = null
    ): array {
        $locale = $locale ?? (string) GlobalState::contentLocale();

        // hybrid way to get categories ids
        $categoriesIds
            = $this->getCategoriesIdsFromStatement($id)
            ?? $this->getCategoriesIdsFromEntities($id)
        ;

        return fn_get_product_features(
            [
                'product_id' => $id,
                'category_ids' => $categoriesIds,
                'statuses' => [Status::ENABLED, Status::HIDDEN],
                'variants' => true,
                'plain' => false,
                'existent_only' => $existentOnly,
                'variants_selected_only' => $variantsSelectedOnly,
            ],
            0,
            $locale
        )[0];
    }

    /**
     * Récupération des valeurs des attributs d'un Product ou d'un MVP
     *
     * @param int|string $id Product or MVP id
     */
    public function getLegacyAttributesValues($id): array
    {
        // Récupération des attributs
        $detailedAttributes = $this->getDetailedLegacyAttributes($id);

        // Suppression des groupes
        $flattenDetailedAttributes = $this->getAttributeService()->flattenDetailedLegacyAttributes($detailedAttributes);

        // Récupération des valeurs des attributs
        return $this->getAttributeService()->extractLegacyAttributesValues($flattenDetailedAttributes);
    }

    /**
     * Sauvegarde des valeurs des attributs d'un Product ou d'un MVP
     *
     * @param int|string $id Product or MVP id
     * @throws AttributeCannotHaveMultipleValues
     */
    public function setLegacyAttributesValues($id, array $values, array $newVariants = [], $lang = null)
    {
        $lang = $lang ?? (string) GlobalState::contentLocale();

        $success = fn_update_product_features_value(
            $id,
            $values,
            $newVariants,
            $lang
        );

        if (!$success) {
            if ($notifications = fn_get_notifications()) {
                $notification = reset($notifications);
                if ($notification['extra'] === 'cannot_save_multiple_values') {
                    throw new AttributeCannotHaveMultipleValues($notification['message']);
                }
            }
        }

        // Trick pour envoyer un event de modification de MVP
        if (MultiVendorProduct::isMultiVendorProductId($id)) {
            $this->getEventDispatcher()->dispatch((new MultiVendorProductEvent($this->get($id))), MultiVendorProductEvent::UPDATED);
        }
    }

    /**
     * Récupération des attributs libres d'un Product ou d'un MVP
     *
     * @param int|string $id Product or MVP id
     * @param null|string $locale
     * @return array
     * @throws \Doctrine\DBAL\DBALException
     */
    public function getFreeLegacyAttributes($id, $locale = null): array
    {
        $locale = $locale ?? (string) GlobalState::contentLocale();

        $query = 'SELECT `metadata` FROM `cscart_product_metadata` WHERE `product_id`=? AND `metadata_type`=? AND `lang_code`=?';
        $params = [$id, LegacyProduct::METADATA_KEY_FREE_FEATURES, $locale];

        $serializedFreeAttributes = $this->getEntityManager()
            ->getConnection()
            ->executeQuery($query, $params)
            ->fetchColumn();

        if (empty($serializedFreeAttributes)) {
            return [];
        }

        $freeAttributes = unserialize((string) $serializedFreeAttributes);
        if ($freeAttributes === false && $serializedFreeAttributes != serialize(false)) {
            // see http://php.net/manual/en/function.unserialize.php#refsect1-function.unserialize-notes
            $freeAttributes = [];
        }

        if (!\is_array($freeAttributes)) {
            return [];
        }

        return $freeAttributes;
    }

    /**
     * Mise à jour des attributs libres d'un Product ou d'un MVP
     *
     * @param int|string $id Product or MVP id
     */
    public function setFreeLegacyAttributes($id, array $attributes)
    {
        $locale = (string) GlobalState::contentLocale();

        $attributes = array_map(
            function ($value) {
                return \is_array($value) ? $value : [$value];
            },
            $attributes
        );

        $query = 'REPLACE `cscart_product_metadata` SET `product_id` = ?, `metadata_type` = ?, `metadata` = ?, lang_code = ?;';
        $params = [$id, LegacyProduct::METADATA_KEY_FREE_FEATURES, serialize($attributes), $locale];

        $this->getEntityManager()->getConnection()->executeQuery($query, $params);

        // Trick pour envoyer un event de modification de MVP
        if (MultiVendorProduct::isMultiVendorProductId($id)) {
            $this->getEventDispatcher()->dispatch((new MultiVendorProductEvent($this->get($id))), MultiVendorProductEvent::UPDATED);
        }
    }

    /**
     * Récupération de l'ensemble des attributs, "Detailed attributes + Free attributes" d'un Product ou MVP :
     * - optionnellement filtrer sur leur visibilité (public ou tous) et leur recherchabilité (oui monsieur)
     * - optionnellement aplati, sans notion de groupe
     * - toujours formaté dans un format...adapté...
     *
     * @param int|string $id Product or MVP id
     */
    public function getAllLegacyAttributes($id, bool $publicOnly = false, bool $searchableOnly = false, bool $leavesOnly = false): array
    {
        // Récupération des attributs libres
        $freeAttributes = $this->getFreeLegacyAttributes($id);

        // Formatage des attributs libres pour qu'ils soient dans le format attendu
        $formattedFreeAttributes = [];
        foreach ($freeAttributes as $attributeName => $attributeValue) {
            $formattedFreeAttributes[] = ['id' => null, 'name' => $attributeName, 'value' => $attributeValue];
        }

        // Récupération des attributs
        $attributes = $this->getDetailedLegacyAttributes($id);

        // Aplatissement si on ne veut que les feuilles
        if ($leavesOnly) {
            $attributes = $this->getAttributeService()->flattenDetailedLegacyAttributes($attributes);
        }

        // Filtrage des attributs
        $filteredAttributes = $this->getAttributeService()->filterDetailedLegacyAttributes($attributes, $publicOnly, $searchableOnly);

        // Formatage "mais pourquoi donc ???" des attributs
        $filteredAndFormattedAttributes = $this->getAttributeService()->formatDetailedLegacyAttributes($filteredAttributes);

        return array_merge($filteredAndFormattedAttributes, $formattedFreeAttributes);
    }

    /**
     * Memory + perf leak with this approach :(
     *
     *  @return int[]
     * */
    private function getCategoriesIdsFromEntities($id): array
    {
        /** @var Product|MultiVendorProduct */
        $productOrMvp = $this->get($id);

        /** @var Category|null $category */
        $category = $productOrMvp->getCategory();

        return $category instanceof Category ? explode('/', $category->getPathId()) : [];
    }

    /**
     * Leak free, but not working with non persisted entities
     *
     *  @return int[]
     */
    private function getCategoriesIdsFromStatement($id): ?array
    {
        /** @var EntityManagerInterface */
        $entityManager = $this->getEntityManager();
        $statement = $entityManager->getConnection()->prepare(
            'SELECT
                cc.id_path
            FROM
                doctrine_multi_vendor_product dmvp
                JOIN cscart_categories cc
                    ON cc.category_id = dmvp.category_id
            WHERE dmvp.id = :id
            '
        );
        $statement->execute(
            ['id' => $id]
        );
        $pathIds = $statement->fetchColumn();

        return
            $pathIds
            ? array_map(
                function (string $categoryId): int {
                    return (int) $categoryId;
                },
                explode(
                    '/',
                    $pathIds
                )
            )
            : null
        ;
    }
}
