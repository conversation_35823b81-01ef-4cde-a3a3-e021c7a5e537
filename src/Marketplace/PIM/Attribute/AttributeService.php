<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\PIM\Attribute;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Types\Type;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Tygh\Registry;
use Wizacha\Core\Iterator\PdoColumnIterator;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Status;
use Wizacha\Events\IterableEvent;

/**
 * Fonctions 'stateless' de manipulation des attributs.
 */
class AttributeService
{
    public const DELIMITER_SET = ';';
    public const DELIMITER_PAIR = ':';

    public const EVENT_DELETE = "attribute.delete";
    public const EVENT_UPDATE = "attribute.update";
    public const EVENT_UPDATE_VARIANT = "attribute.update_variant";

    protected $dbal;

    protected EventDispatcherInterface $dispatcher;

    public function __construct(Connection $dbal, EventDispatcherInterface $dispatcher)
    {
        $this->dbal = $dbal;
        $this->dispatcher  = $dispatcher;
    }

    public function get(int $id): Attribute
    {
        $legacyAttribute = fn_get_product_feature_data(
            $id,
            false,
            false,
            (string) GlobalState::contentLocale()
        );

        if (\count($legacyAttribute) == 0) {
            throw NotFound::fromId('Attribute', $id);
        }

        $attributeData = $this->transformCSCartData($legacyAttribute);

        return new Attribute($this->setDefaultValues($attributeData));
    }

    public function list(): array
    {
        $legacyAttributes = fn_get_product_features();

        if (!isset($legacyAttributes[0]) || empty($legacyAttributes[0])) {
            return [];
        }

        $legacyAttributes = $this->flattenDetailedLegacyAttributes($legacyAttributes[0], true, true);

        $attributes = array_map(function (array $legacyAttribute) {
            $attributeData = $this->transformCSCartData($legacyAttribute);

            return new Attribute($this->setDefaultValues($attributeData));
        }, $legacyAttributes);

        return $attributes;
    }

    public function save(array $data, Attribute $existingResource = null): Attribute
    {
        // Récupération de l'attribut parent
        if (isset($data['parentId']) && $data['parentId'] !== "") {
            $data['parent'] = $this->get($data['parentId']);
        }

        // On ne veut pas ajouter des variantes en passant par le tableau de données,
        // il faut utiliser les autres fonctions du service qui sont faites pour ça.
        unset($data['variants']);

        // Création de la ressource
        if (\is_null($existingResource)) {
            $resource = new Attribute($this->setDefaultValues($data));
        } else {
            // Récupération des champs en lecture seule
            $data['id'] = $existingResource->getId();
            $data['type'] = $existingResource->getType()->getValue();

            $resource = $existingResource->loadData($this->setDefaultValues($data));
        }

        // Sauvegarde interne de l'attribut
        $id = $this->saveToCSCart($resource);

        // Il est nécessaire de récupérer à nouveau l'attribut par le service,
        // car il est possible que certaines règles surchargent les données.
        return $this->get($id);
    }

    public function delete(int $id): void
    {
        fn_delete_feature($id);
    }

    /**
     * @return Variant[]
     */
    public function getVariantsFromAttribute(int $attributeId, int $resultsPerPage = 0, int $page = 1): array
    {
        // Récupération des variantes legacy
        $legacyVariants = fn_get_product_feature_variants(
            [
                'feature_id' => $attributeId,
                'items_per_page' => $resultsPerPage,
                'page' => $page,
            ],
            0,
            (string) GlobalState::contentLocale()
        );

        // Transformation en Variants PIM, sans les information concernant l'attribut,
        // pour éviter une boucle infinie.
        $variants = array_map(function (array $legacyVariant) {
            return $this->getVariant($legacyVariant['variant_id'], false);
        }, $legacyVariants[0]);

        // Reset des clés, pour supprimer les clés contenant les id
        $variants = array_values($variants);

        return $variants;
    }

    public function getVariant(int $id, $withAttributeInfo = true): Variant
    {
        $legacyAttribute = fn_get_product_feature_variant(
            $id,
            (string) GlobalState::contentLocale()
        );

        if ($legacyAttribute === false) {
            throw NotFound::fromId('Variant', $id);
        }

        $data = [
            'id' => (int) $legacyAttribute['variant_id'],
            'name' => $legacyAttribute['variant'],
            'description' => $legacyAttribute['description'],
            'pageTitle' => $legacyAttribute['page_title'],
            'metaKeywords' => $legacyAttribute['meta_keywords'],
            'metaDescription' => $legacyAttribute['meta_description'],
            'position' => (int) $legacyAttribute['position'],
            'slug' => fn_seo_get_name('e', $id),
        ];

        if (!empty($legacyAttribute['feature_id']) && $withAttributeInfo) {
            $data['attribute'] = $this->get($legacyAttribute['feature_id']);
        }

        return new Variant($data);
    }

    public function saveVariant(array $data, Variant $existingResource = null): Variant
    {
        // Création de la ressource
        if (\is_null($existingResource)) {
            // Récupération de l'attribut parent
            if (isset($data['attributeId'])) {
                $data['attribute'] = $this->get($data['attributeId']);
            }

            $resource = new Variant($data);
        } else {
            $data['id'] = $existingResource->getId();
            $data['attribute'] = $existingResource->getAttribute();
            $resource = $existingResource->loadData($data);
        }

        // Sauvegarde interne de l'attribut
        $id = $this->saveVariantToCSCart($resource);

        // Il est (peut-être) nécessaire de récupérer à nouveau la variante par le service,
        // car il est possible que certaines règles surchargent les données (cf. les attributs).
        return $this->getVariant($id);
    }

    public function deleteVariant(int $id): void
    {
        $this->getVariant($id);
        fn_delete_product_feature_variants(0, [$id]);
    }

    public function getAttributesFromCategory(int $categoryId, bool $excludeAdminOnly = false): array
    {
        $path = explode(
            '/',
            \Tygh\Database::getField(
                "SELECT id_path FROM ?:categories WHERE category_id = ?i",
                $categoryId
            )
        );
        $params = [
            'category_ids'  => $path,
            'statuses'      => ['A'],
            'variants'      => true,
            'plain'         => false,
            'all_variants'  => true,
            'exclude_admin_only' => $excludeAdminOnly,
        ];

        return fn_get_product_features(
            $params,
            0,
            (string) GlobalState::contentLocale()
        )[0] ?: [];
    }

    public function getRequiredAttributesFromCategory(int $categoryId, bool $excludeAdminOnly = false): array
    {
        return array_filter(
            $this->getAttributesFromCategory($categoryId, $excludeAdminOnly),
            function ($attribute) {
                return $attribute['is_required'] == 'Y';
            }
        );
    }

    public function getProductsFromFeatureId($featureId): \Iterator
    {
        return new PdoColumnIterator(\Tygh\Database::prepare(
            "SELECT DISTINCT pf.product_id
              FROM ?:product_features_values as pf
              WHERE feature_id = ?i",
            $featureId
        ));
    }

    public function getProductsFromVariantsId(array $variantsId): \Iterator
    {
        return new PdoColumnIterator(\Tygh\Database::prepare(
            "SELECT DISTINCT pf.product_id
              FROM ?:product_features_values as pf
              WHERE variant_id in (?a)",
            $variantsId
        ));
    }
    /**
     * @param int|string $productId MVP / Product id
     */
    public function removeAllProductValues($productId)
    {
        $this->dbal->executeQuery('DELETE FROM cscart_product_features_values WHERE product_id = :productId', [
            'productId' => $productId,
        ]);
    }

    /**
     * Returns the list of attributes that are configured to be used for search facets.
     *
     * The array returned contains only leaf attributes, i.e. it doesn't contain groups.
     */
    public function getAttributesForFaceting(): array
    {
        list($attributes) = fn_get_product_features(
            [
                'display_on' => 'faceting',
                'statuses' => [Status::ENABLED],
            ],
            0,
            (string) GlobalState::contentLocale()
        );

        if (!\is_array($attributes)) {
            return [];
        }

        return $this->flattenDetailedLegacyAttributes($attributes, true);
    }

    /**
     * Returns the list of attributes that are shown in front, facets and are searchable
     *
     * The array returned contains only leaf attributes, i.e. it doesn't contain groups.
     */
    public function getPublicAttributesForSearchAndFaceting(): array
    {
        //@todo handle locale in function when implementing multilingual catalog
        list($facetAttributes) = fn_get_product_features([
            'display_on' => 'faceting',
            'statuses' => [Status::ENABLED],
        ]);
        list($publicAttributes) = fn_get_product_features([
            'display_on' => 'product',
            'statuses' => [Status::ENABLED],
        ]);
        list($searchAttributes) = fn_get_product_features([
            'is_searchable' => 'Y',
            'statuses' => [Status::ENABLED],
        ]);
        $attributes = $facetAttributes + $publicAttributes + $searchAttributes;

        return $this->flattenDetailedLegacyAttributes($attributes, true);
    }

    /**
     * @deprecated
     */
    public function getAttributeVariants(int $attributeId): array
    {
        list($variants) = fn_get_product_feature_variants(
            ['feature_id' => $attributeId],
            0,
            (string) GlobalState::contentLocale()
        );

        return $variants;
    }

    public function getAttributeVariant(int $variantId): ?AttributeVariant
    {
        $sql = <<<SQL
            SELECT pfv.feature_id, pfv.position, pfv.url, pfv.variant_id, pfvd.variant, pfvd.description, pfvd.lang_code,
            pfvd.meta_description, pfvd.meta_keywords, pfvd.page_title
            FROM cscart_product_feature_variants pfv
            INNER JOIN cscart_product_feature_variant_descriptions pfvd
                ON pfv.variant_id = pfvd.variant_id
                AND pfvd.lang_code = :locale
            WHERE pfv.variant_id = :id
            LIMIT 1
SQL;

        $return = $this->dbal->fetchAll($sql, [
            'id' => $variantId,
            'locale' => (string) GlobalState::contentLocale(),
        ]);

        if (empty($return)) {
            return null;
        }

        $data = $return[0];
        $attributeVariant = new AttributeVariant((int) $data['variant_id'], (int) $data['position'], (int) $data['feature_id'], $data['variant'], $data['description']);

        return $attributeVariant;
    }

    public function searchBrand(string $query, int $maxResults = 50): array
    {
        $keyword = '%' . $query . '%';
        $query = <<<SQL
            SELECT pfvd.*
            FROM cscart_product_features pf
            INNER JOIN cscart_product_feature_variants pfv
                ON pf.feature_id = pfv.feature_id
            INNER JOIN cscart_product_feature_variant_descriptions pfvd
                ON pfv.variant_id = pfvd.variant_id
            WHERE pf.feature_type = :type
            AND pfvd.variant LIKE :keyword
            AND pfvd.lang_code = :locale
            LIMIT :maxResults
SQL;

        return $this->dbal->fetchAll(
            $query,
            [
                'type' => AttributeType::LIST_BRAND,
                'keyword' => $keyword,
                'maxResults' => $maxResults,
                'locale' => (string) GlobalState::contentLocale(),
            ],
            [
                'type' => Type::STRING,
                'keyword' => Type::STRING,
                'maxResults' => Type::INTEGER,
                'locale' => Type::STRING,
            ]
        );
    }

    /**
     * Récupèration des valeurs d'attributs, en :
     * - gérant les différents types de valeurs
     * - retournant un tableau aplati de type [id-attribute => value attribute]
     */
    public function extractLegacyAttributesValues(array $flattenDetailedAttributes): array
    {
        // Gestion des différents types de valeur
        $values = [];
        foreach ($flattenDetailedAttributes as $attribute) {
            // Si c'est un groupe, on skip
            if ($attribute['feature_type'] === AttributeType::GROUP) {
                continue;
                // For multiple checkboxes, we return a list of ID
            } elseif ($attribute['feature_type'] === AttributeType::CHECKBOX_MULTIPLE && \is_array($attribute['variants'])) {
                $value = array_keys($attribute['variants']);
                // For attributes with a single variant, we return the variant ID
            } elseif (!empty($attribute['variant_id'])) {
                $value = (int) $attribute['variant_id'];
                // For all others (attributes without variants: they have values instead) we return the value
            } else {
                if (!empty($attribute['value_int'])) {
                    $value = $attribute['value_int'];
                } else {
                    $value = $attribute['value'];
                }
            }

            $values[$attribute['feature_id']] = $value;
        }

        return $values;
    }

    /**
     * Fonction permettant d'aplatir les attributs détaillés en supprimant la notion de groupes
     * et en ne récupérant donc que les "feuilles" : les attributs qui contiennent des valeurs
     *
     * Le flag $resetKeys permet de ne pas conserver l'id de l'attribut en 'key' du tableau mais d'avoir un tableau 0-based
     * Le flag $keepGroup permet de conserver les groupes, qui se retrouve donc au même niveau que le fils
     */
    public function flattenDetailedLegacyAttributes(array $attributes, bool $resetKeys = false, bool $keepGroup = false): array
    {
        $flattenAttributes = [];

        foreach ($attributes as $attribute) {
            // Attribut final
            if ($attribute['feature_type'] !== AttributeType::GROUP) {
                $flattenAttributes[$attribute['feature_id']] = $attribute;
            // Groupe d'attributs
            } else {
                // Si il y a des attributs dans le groupe
                if (isset($attribute['subfeatures'])) {
                    $flattenAttributes = $flattenAttributes + $this->flattenDetailedLegacyAttributes($attribute['subfeatures']);
                }

                // Si on conserve le groupe
                if ($keepGroup) {
                    // Suppression des fils car ils sont déjà gérés (voir ci-dessus)
                    unset($attribute['subfeatures']);

                    $flattenAttributes[$attribute['feature_id']] = $attribute;
                }
            }
        }

        if ($resetKeys) {
            $flattenAttributes = array_values($flattenAttributes);
        }

        return $flattenAttributes;
    }

    /**
     * Fonction permettant de filtrer les attributs :
     * - viewable : visible dans le front, par les utilisateurs,
     * - searchable : apparaissant dans la recherche.
     */
    public function filterDetailedLegacyAttributes(array $attributes, bool $viewableOnly, bool $searchableOnly): array
    {
        $filteredAttributes = [];

        foreach ($attributes as $attribute) {
            // Si c'est un groupe avec des fils
            if ($attribute['feature_type'] === AttributeType::GROUP) {
                if (isset($attribute['subfeatures'])) {
                    $attribute['subfeatures'] = $this->filterDetailedLegacyAttributes($attribute['subfeatures'], $viewableOnly, $searchableOnly);
                }
            } elseif ($attribute['display_on_product'] == 'N' && $viewableOnly) {
                // On demande du 'viewable' et ce n'est pas le cas
                continue;
            } elseif ($attribute['display_on_faceting'] == 'N' && $attribute['is_searchable'] == 'N' && $searchableOnly) {
                // On demande du 'searchable' et ce n'est pas le cas
                continue;
            }



            // Stockage de l'attribut filtré
            $filteredAttributes[$attribute['feature_id']] = $attribute;
        }

        return $filteredAttributes;
    }

    /**
     * ???. Juste. Disons que ça formate. Dans un format. Voilà.
     * Utilisé dans la génération du 'readmodel' et dans l'export
     */
    public function formatDetailedLegacyAttributes(array $attributes): array
    {
        return array_filter(array_map(function (array $attribute) {
            $name = $attribute['description'];
            $code = $attribute['feature_code'];
            $value = null;
            $subAttributes = [];

            if ($attribute['feature_type'] == AttributeType::GROUP && !empty($attribute['subfeatures'])) {
                $subAttributes = $this->formatDetailedLegacyAttributes($attribute['subfeatures']);
            } elseif (!empty($attribute['variant_id']) && !empty($attribute['variants'])) {
                $value = array_column($attribute['variants'], 'variant');
            } elseif (!empty($attribute['value'])) {
                $value = $attribute['value'];
            } elseif (isset($attribute['value_int'])) {
                if ($attribute['feature_type'] == AttributeType::FREE_DATE) {
                    $value = date('d/m/Y', (int) $attribute['value_int']);
                } else {
                    $value = \floatval($attribute['value_int']);
                }
            } else {
                return null;
            }

            $imageUrls = [];
            if (isset($attribute['variants'])) {
                $imageUrls = array_filter(array_map(
                    function ($variant) {
                        if (!isset($variant['image_pair']['icon']['relative_path'])) {
                            return null;
                        }

                        if (!$variant['image_pair']['icon']['relative_path']) {
                            return null;
                        }

                        return [
                            'url' => $variant['image_pair']['icon']['relative_path'],
                            'name' => $variant['variant'],
                            'id' => (int) ($variant['image_pair']['detailed_id'] ?: $variant['image_pair']['image_id']),
                        ];
                    },
                    $attribute['variants']
                ));
            }

            return [
                'id' => (int) $attribute['feature_id'],
                'name' => $name,
                'code' => $code,
                'value' => $value,
                'valueIds' => array_map(
                    'intval',
                    array_column($attribute['variants'] ?: [], 'variant_id')
                ),
                'type' => $attribute['feature_type'],
                'subfeatures' => $subAttributes,
                'imageUrls' => $imageUrls,
                'slugs' => array_column($attribute['variants'] ?: [], 'seo_name'),
                'position' => (int) $attribute['position'],
                'parent_id' => (int) $attribute['parent_id'] ?: null,
            ];
        }, $attributes));
    }

    protected function setDefaultValues(array $data): array
    {
        foreach (['isDisplayedOnProduct', 'isRequired', 'isOnlyEditableByAdmin', 'isASearchFilter', 'isIndexedForTextSearch'] as $field) {
            if (!isset($data[$field])) {
                $data[$field] = true;
            }
        }

        if (!isset($data['isUsedForRecommendations'])) {
            $data['isUsedForRecommendations'] = false;
        }

        if (!isset($data['status'])) {
            $data['status'] = Status::ENABLED;
        }

        return $data;
    }

    protected function transformCSCartData(array $legacyAttribute): array
    {
        $data = [
            'id' => (int) $legacyAttribute['feature_id'],
            'name' => $legacyAttribute['description'] ?? '',
            'code' => $legacyAttribute['feature_code'] ?? '',
            'description' => $legacyAttribute['full_description'] ?? '',
            'type' => $legacyAttribute['feature_type'],
            'isDisplayedOnProduct' => $legacyAttribute['display_on_product'] == 'Y' ? true : false,
            'isRequired' => $legacyAttribute['is_required'] == 'Y' ? true : false,
            'isOnlyEditableByAdmin' => $legacyAttribute['editable_only_by_admin'] == 'Y' ? true : false,
            'isASearchFilter' => $legacyAttribute['display_on_faceting'] == 'Y' ? true : false,
            'isIndexedForTextSearch' => $legacyAttribute['is_searchable'] == 'Y' ? true : false,
            'isUsedForRecommendations' => $legacyAttribute['used_for_recommendations'] == 'Y' ? true : false,
            'status' => $legacyAttribute['status'],
            'position' => (int) $legacyAttribute['position'] ?? 0,
            'variants' => $this->getVariantsFromAttribute((int) $legacyAttribute['feature_id']),
        ];

        if (!empty($legacyAttribute['categories_path'])) {
            $data['categoryIds'] = explode(',', $legacyAttribute['categories_path']);
        }

        if (!empty($legacyAttribute['parent_id'])) {
            $data['parent'] = $this->get($legacyAttribute['parent_id']);
        }

        return $data;
    }

    protected function saveToCSCart(Attribute $resource): int
    {
        // Préparation des données legacy pour pouvoir créer un attribut
        $legacyData = [
            'description' => $resource->getName(),
            'feature_code' => $resource->getCode(),
            'full_description' => $resource->getDescription(),
            'feature_type' => $resource->getType()->getValue(),
            'parent_id' => $resource->getParent() ? $resource->getParent()->getId() : 0,
            'categories_path' => implode(',', $resource->getCategoryIds()),
            'display_on_product' => $resource->isDisplayedOnProduct() ? 'Y' : 'N',
            'is_required' => $resource->isRequired() ? 'Y' : 'N',
            'editable_only_by_admin' => $resource->isOnlyEditableByAdmin() ? 'Y' : 'N',
            'display_on_faceting' => $resource->isASearchFilter() ? 'Y' : 'N',
            'is_searchable' => $resource->isIndexedForTextSearch() ? 'Y' : 'N',
            'used_for_recommendations' => $resource->isUsedForRecommendations() ? 'Y' : 'N',
            'status' => $resource->getStatus()->getValue(),
            'position' => $resource->getPosition(),
        ];

        // Création de l'attribut par CSCart
        $id = fn_update_product_feature($legacyData, $resource->getId() ?? 0);

        if ($id === false) {
            throw new \Exception('Error while creating attribute ' . $resource->getName());
        }

        return (int) $id;
    }

    protected function saveVariantToCSCart(Variant $resource): int
    {
        if (!$resource->getAttribute()) {
            throw new \Exception(sprintf("Error while creating attribute %s : attribute must be set.", $resource->getName()));
        }

        // Préparation des données legacy pour pouvoir créer une variante
        $legacyData = [
            'variant' => $resource->getName(),
            'description' => $resource->getDescription(),
            'page_title' => $resource->getPageTitle(),
            'meta_keywords' => $resource->getMetaKeywords(),
            'meta_description' => $resource->getMetaDescription(),
            'position' => $resource->getPosition(),
        ];

        if (\is_int($resource->getId())) {
            $legacyData['variant_id'] = $resource->getId();
        }

        $featureId = $resource->getAttribute()->getId();

        // Création de la variante par CSCart
        // Event for updated variant is throw in this function.
        $id = fn_update_product_feature_variant($featureId, $resource->getAttribute()->getType()->getValue(), $legacyData);

        if ($id === false) {
            throw new \Exception('Error while creating attribute ' . $resource->getName());
        }

        // Update the slug (cscart_seo_names)
        $result = fn_seo_update_object(
            ['seo_name' => $resource->getSlug()],
            $id,
            'e',
            (string) GlobalState::contentLocale()
        );

        if (false === $result) {
            throw new \Exception('Error while creating slug on attribute ' . $resource->getName());
        }

        // Remove the slug registry value, otherwise the new value will not be considered
        Registry::del('runtime.seo.e.' . $id);

        return (int) $id;
    }
}
