<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Attribute;

use MyCLabs\Enum\Enum;

/**
 * @method static AttributeType FREE()
 * @method static AttributeType FREE_NUMBER()
 * @method static AttributeType FREE_TEXT()
 * @method static AttributeType FREE_DATE()
 * @method static AttributeType LIST_NUMBER()
 * @method static AttributeType LIST_TEXT()
 * @method static AttributeType LIST_BRAND()
 * @method static AttributeType CHECKBOX_UNIQUE()
 * @method static AttributeType CHECKBOX_MULTIPLE()
 * @method static AttributeType GROUP()
 */
class AttributeType extends Enum
{
    // Sans variantes ------------------------------------------------------
    /**
     * @deprecated Inutilisé ? En tout cas pas utilisable dans le BO.
     */
    public const FREE = 'F';
    /**
     * Nombre libre.
     */
    public const FREE_NUMBER = 'O';
    /**
     * Texte libre.
     */
    public const FREE_TEXT = 'T';
    /**
     * Date libre.
     * @deprecated Pas supporté dans le front-office, la recherche, etc.
     */
    public const FREE_DATE = 'D';
    /**
     * Case à cocher unique : booléen (Y/N)
     */
    public const CHECKBOX_UNIQUE = 'C';

    // Avec variantes ------------------------------------------------------
    /**
     * Sélection d'une variante (de type texte).
     */
    public const LIST_TEXT = 'S';
    /**
     * Sélection d'une variante (de type numérique).
     */
    public const LIST_NUMBER = 'N';
    /**
     * Sélection d'une variante (de type texte, mais c'est une marque).
     */
    public const LIST_BRAND = 'E';
    /**
     * Sélection de plusieurs variantes.
     */
    public const CHECKBOX_MULTIPLE = 'M';

    // Spécial ------------------------------------------------------
    /**
     * Groupe d'attributs.
     */
    public const GROUP = 'G';
}
