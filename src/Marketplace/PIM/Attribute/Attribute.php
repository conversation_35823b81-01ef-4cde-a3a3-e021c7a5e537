<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Attribute;

use Wizacha\Marketplace\Exception\IntegrityConstraintViolation;
use Wizacha\Status;

class Attribute
{
    /** @var int */
    protected $id;

    /** @var string */
    protected $name;

    /** @var string */
    protected $code;

    /** @var string */
    protected $description;

    /** @var AttributeType */
    protected $type;

    /** @var array|int[] */
    protected $categoryIds;

    /** @var Attribute */
    protected $parent;

    /** @var bool */
    protected $isDisplayedOnProduct;

    /** @var bool */
    protected $isRequired;

    /** @var bool */
    protected $isOnlyEditableByAdmin;

    /** @var bool */
    protected $isASearchFilter;

    /** @var bool */
    protected $isIndexedForTextSearch;

    /** @var bool */
    protected $isUsedForRecommendations;

    /** @var Status */
    protected $status;

    /** @var int */
    protected $position;

    /** @var Variant[] */
    protected $variants = [];

    public function __construct(array $data)
    {
        $this->loadData($data);
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id)
    {
        return $this->id = $id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function getType(): AttributeType
    {
        return $this->type;
    }

    public function getCategoryIds(): array
    {
        return $this->categoryIds;
    }

    public function getParent(): ?Attribute
    {
        return $this->parent;
    }

    public function isDisplayedOnProduct(): bool
    {
        return $this->isDisplayedOnProduct;
    }

    public function isRequired(): bool
    {
        return $this->isRequired;
    }

    public function isOnlyEditableByAdmin(): bool
    {
        return $this->isOnlyEditableByAdmin;
    }

    public function isASearchFilter(): bool
    {
        return $this->isASearchFilter;
    }

    public function isIndexedForTextSearch(): bool
    {
        return $this->isIndexedForTextSearch;
    }

    public function isUsedForRecommendations(): bool
    {
        return $this->isUsedForRecommendations;
    }

    public function getStatus(): Status
    {
        return $this->status;
    }

    public function getPosition(): int
    {
        return $this->position;
    }

    /**
     * @return Variant[]
     */
    public function getVariants(): array
    {
        return $this->variants;
    }

    public function loadData(array $data): Attribute
    {
        static::checkIntegrity($data);

        $this->id = $data['id'] ?? null;
        $this->name = $data['name'];
        $this->code = $data['code'] ?? '';
        $this->description = $data['description'] ?? '';
        $this->type = new AttributeType($data['type']);
        $this->categoryIds = $data['categoryIds'] ?? [];
        $this->parent = $data['parent'] ?? null;
        $this->isDisplayedOnProduct = $data['isDisplayedOnProduct'];
        $this->isRequired = $data['isRequired'];
        $this->isOnlyEditableByAdmin = $data['isOnlyEditableByAdmin'];
        $this->isASearchFilter = $data['isASearchFilter'];
        $this->isIndexedForTextSearch = $data['isIndexedForTextSearch'];
        $this->isUsedForRecommendations = $data['isUsedForRecommendations'];
        $this->status = new Status($data['status']);
        $this->position = $data['position'] ?? 0;
        $this->variants = $data['variants'];

        return $this;
    }

    public function expose(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'code' => $this->code,
            'description' => $this->description,
            'type' => $this->type->getValue(),
            'categoryIds' => $this->categoryIds,
            'parent' => $this->parent ? $this->parent->expose() : null,
            'isDisplayedOnProduct' => $this->isDisplayedOnProduct,
            'isRequired' => $this->isRequired,
            'isOnlyEditableByAdmin' => $this->isOnlyEditableByAdmin,
            'isASearchFilter' => $this->isASearchFilter,
            'isIndexedForTextSearch' => $this->isIndexedForTextSearch,
            'isUsedForRecommendations' => $this->isUsedForRecommendations,
            'status' => $this->status->getValue(),
            'position' => $this->position,
            'variants' => array_map(function (Variant $variant) {
                return [
                    'id' => $variant->getId(),
                    'name' => $variant->getName(),
                    'description' => $variant->getDescription(),
                    'pageTitle' => $variant->getPageTitle(),
                    'metaKeywords' => $variant->getMetaKeywords(),
                    'metaDescription' => $variant->getMetaDescription(),
                    'position' => $variant->getPosition(),
                    'slug' => $variant->getSlug(),
                ];
            }, $this->variants),
        ];
    }

    public static function checkIntegrity(array $data)
    {
        if (\array_key_exists('id', $data) && !\is_int($data['id'])) {
            throw IntegrityConstraintViolation::isInvalid('id');
        }

        if (!\array_key_exists('name', $data)) {
            throw IntegrityConstraintViolation::isMissing('name');
        }

        if (!\is_string($data['name'])) {
            throw IntegrityConstraintViolation::isInvalid('name');
        }

        if (\array_key_exists('code', $data) && !\is_string($data['code'])) {
            throw IntegrityConstraintViolation::isInvalid('code');
        }

        if (\array_key_exists('description', $data) && !\is_string($data['description'])) {
            throw IntegrityConstraintViolation::isInvalid('description');
        }

        if (!\array_key_exists('type', $data)) {
            throw IntegrityConstraintViolation::isMissing('type');
        }

        if (!AttributeType::isValid($data['type'])) {
            throw IntegrityConstraintViolation::isInvalid('type');
        }

        if (\array_key_exists('categoryIds', $data) && !\is_array($data['categoryIds'])) {
            throw IntegrityConstraintViolation::isInvalid('categoryIds');
        }

        if (\array_key_exists('parent', $data) && !$data['parent'] instanceof Attribute) {
            throw IntegrityConstraintViolation::isInvalid('parent');
        }

        foreach (['isDisplayedOnProduct', 'isRequired', 'isOnlyEditableByAdmin', 'isASearchFilter', 'isIndexedForTextSearch', 'isUsedForRecommendations'] as $field) {
            if (!\array_key_exists($field, $data)) {
                throw IntegrityConstraintViolation::isMissing($field);
            }

            if (!\is_bool($data[$field])) {
                throw IntegrityConstraintViolation::isInvalid($field);
            }
        }

        if (!\array_key_exists('status', $data)) {
            throw IntegrityConstraintViolation::isMissing('status');
        }

        if (!Status::isValid($data['status'])) {
            throw IntegrityConstraintViolation::isInvalid('status');
        }

        if (\array_key_exists('position', $data) && !\is_int($data['position'])) {
            throw IntegrityConstraintViolation::isInvalid('position');
        }

        if (\array_key_exists('variants', $data) && !\is_array($data['variants'])) {
            throw IntegrityConstraintViolation::isInvalid('variants');
        }
    }
}
