<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\TransactionMode;

/**
 * Liste les types de produits activés :
 *
 * - transactionnel
 * - affiliation
 * - mise en contact
 *
 * Dans le futur il faudrait peut-être renommer ce concept en "Product Type" ?
 */
class TransactionModeService
{
    /**
     * @var bool
     */
    private $transactionalEnabled;

    /**
     * @var bool
     */
    private $affiliateEnabled;

    /**
     * @var bool
     */
    private $contactOnly;

    public function __construct(bool $transactionalEnabled, bool $affiliateEnabled, bool $contactOnly)
    {
        $this->transactionalEnabled = $transactionalEnabled;
        $this->affiliateEnabled = $affiliateEnabled;
        $this->contactOnly = $contactOnly;
    }

    /**
     * @return array Key is enum key and value is enum value (a letter)
     */
    public function getAvailablesModesAsArray(): array
    {
        $modes = TransactionMode::toArray();

        if (!$this->transactionalEnabled) {
            unset($modes['TRANSACTIONAL']);
        }
        if (!$this->affiliateEnabled) {
            unset($modes['AFFILIATE']);
        }
        if (!$this->contactOnly) {
            unset($modes['CONTACT_ONLY']);
        }

        return $modes;
    }

    public function getDefaultMode(): TransactionMode
    {
        if (!$this->transactionalEnabled) {
            return $this->affiliateEnabled ? TransactionMode::AFFILIATE() : TransactionMode::CONTACT_ONLY();
        }

        return TransactionMode::TRANSACTIONAL();
    }
}
