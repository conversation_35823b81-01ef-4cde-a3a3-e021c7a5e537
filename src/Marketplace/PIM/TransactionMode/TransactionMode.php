<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\PIM\TransactionMode;

use MyCLabs\Enum\Enum;

/**
* @method static TransactionMode TRANSACTIONAL()
* @method static TransactionMode AFFILIATE()
* @method static TransactionMode CONTACT_ONLY()
* @method static TransactionMode INHERITED()
*/
final class TransactionMode extends Enum
{
    public const TRANSACTIONAL = 'T';
    public const AFFILIATE     = 'A';
    public const CONTACT_ONLY  = 'N';
    public const INHERITED     = 'H';
}
