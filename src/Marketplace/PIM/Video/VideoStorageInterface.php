<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\PIM\Video;

interface VideoStorageInterface
{
    /**
     * @param int $id
     *
     * Delete the video stored in the initial bucket
     */
    public function deleteFromBucket($id);

    /**
     * @param int $id
     *
     * Delete the video stored in the temporary bucket
     */
    public function deleteFromTmp($id);

    /**
     * @param Video $video
     * @return array
     *
     * Generate public links (path and thumbnail) and return them
     */
    public function generatePublicLinks(Video $video): array;

    /**
     * @return array
     *
     * Return aws data to upload video
     */
    public function generateUploadFormData(): array;

    /**
     * @param string $tmpPathOnS3
     * @param string $uuid
     * @return string
     *
     * Transcode the video and return the video id
     */
    public function transcodeVideo(string $tmpPathOnS3, string $uuid): string;

    /**
     * @param string $tmpPathOnS3
     * @param string $uuid
     *
     * Copy file from temporary bucket to bucket
     */
    public function copyFileFromTmpToBucket(string $tmpPathOnS3, string $uuid);

    /**
     * @param string $url
     * @return string
     *
     * Transfer video to aws
     */
    public function transferRemoteVideoToS3(string $url): string;
}
