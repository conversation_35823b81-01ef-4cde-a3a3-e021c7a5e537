<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\PIM\Video;

use Doctrine\ORM\EntityManagerInterface;
use Wizacha\Marketplace\Exception\NotFound;

class VideoRepository
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    /**
     * @throws NotFound Video not found
     */
    public function get(string $id): Video
    {
        $video = $this->entityManager->find(Video::class, $id);
        if (!$video) {
            throw NotFound::fromId('Video', $id);
        }

        return $video;
    }

    public function save(Video $video)
    {
        $this->entityManager->persist($video);
        $this->entityManager->flush();
    }

    /**
     * @param string $id
     */
    public function delete($id)
    {
        $video = $this->entityManager->find(Video::class, $id);

        if ($video) {
            $this->entityManager->remove($video);
            $this->entityManager->flush();
        }
    }


    public function findOneByProductId(int $productId): ?Video
    {
        $builder = $this->entityManager->createQueryBuilder();
        $builder
            ->select('v')
            ->from('Wizacha\Marketplace\PIM\Video\Video', 'v')
        ;

        $builder->where('v.productId = :productId');
        $builder->setParameter('productId', $productId);

        return $builder->getQuery()->getOneOrNullResult();
    }
}
