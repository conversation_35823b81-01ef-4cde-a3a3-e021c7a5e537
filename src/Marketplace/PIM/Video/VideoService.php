<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\PIM\Video;

use Aws\S3\Exception\S3Exception;
use Broadway\UuidGenerator\UuidGeneratorInterface;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Wizacha\Async\Dispatcher;
use Wizacha\Component\Import\EximJobService;
use Wizacha\Exim\Import\ImportReportMessage\ProductMessage;
use Wizacha\Marketplace\PIM\Video\Exception\FileSizeException;

class VideoService
{
    /**
     * @var UuidGeneratorInterface
     */
    private $idGenerator;

    /**
     * @var VideoRepository
     */
    private $repository;

    /**
     * @var Dispatcher
     */
    private $jobDispatcher;

    /**
     * @var VideoStorage
     */
    private $videoStorage;

    public function __construct(
        UuidGeneratorInterface $idGenerator,
        VideoRepository $repository,
        Dispatcher $jobDispatcher,
        VideoStorageInterface $videoStorage
    ) {
        $this->idGenerator = $idGenerator;
        $this->repository = $repository;
        $this->jobDispatcher = $jobDispatcher;
        $this->videoStorage = $videoStorage;
    }

    /**
     * @throws \Wizacha\Marketplace\Exception\NotFound
     */
    public function get(string $id): Video
    {
        $video = $this->repository->get($id);
        $video->setLinks($this->videoStorage->generatePublicLinks($video));

        return $video;
    }

    public function delete(string $id)
    {
        $this->repository->delete($id);

        $this->videoStorage->deleteFromBucket($id);
    }

    public function findOneByProductId(int $productId)
    {
        $video = $this->repository->findOneByProductId($productId);

        if ($video) {
            $video->setLinks($this->videoStorage->generatePublicLinks($video));
        }

        return $video;
    }

    public function getFromStorage(?string $videoId): Video
    {
        $video = new Video($videoId);

        if (\is_string($video->getId())) {
            $video->setLinks($this->videoStorage->generatePublicLinks($video));
        }

        return $video;
    }

    public function deleteFromStorage(string $videoId): self
    {
        $this->videoStorage->deleteFromBucket($videoId);

        return $this;
    }

    /**
     * @throws \Exception
     */
    public function importFromS3(string $tmpPathOnS3): Video
    {
        $uuid = $this->idGenerator->generate();

        $this->videoStorage->transcodeVideo($tmpPathOnS3, $uuid);
        $this->videoStorage->copyFileFromTmpToBucket($tmpPathOnS3, $uuid);
        $this->videoStorage->deleteFromTmp($uuid);

        $video = new Video($uuid);
        $this->repository->save($video);

        return $video;
    }

    public function addVideoToProduct(Video $video, int $productId)
    {
        $currentProductVideo = $this->findOneByProductId($productId);
        if ($currentProductVideo !== null) {
            $this->delete($currentProductVideo->getId());
        }

        $video->setProductId($productId);
        $this->repository->save($video);
    }

    public function startImportFromUrl(int $productId, string $videoUrl, string $jobId, int $line = 0)
    {
        if ($this->jobDispatcher->delayExec('marketplace.pim.video_service::' . __FUNCTION__, \func_get_args())) {
            return;
        }

        try {
            $tmpPathOnS3 = $this->videoStorage->transferRemoteVideoToS3($videoUrl);
        } catch (\Exception $e) {
            EximJobService::warning(ProductMessage::EXIM_PRODUCT_VIDEO_FAILED, $jobId, $line, $productId, $videoUrl);

            // Wrong value in csv field, ignore video import
            return;
        }

        $video = $this->importFromS3($tmpPathOnS3);
        $this->addVideoToProduct($video, $productId);
    }

    public function startUploadFromUrl(int $productId, string $videoUrl): ?string
    {
        try {
            $tmpPathOnS3 = $this->videoStorage->transferRemoteVideoToS3($videoUrl);
            $video = $this->importFromS3($tmpPathOnS3);
        } catch (FileSizeException $e) {
            throw new BadRequestHttpException(__('exim_video_size_exception'));
        } catch (\Exception $e) {
            throw new BadRequestHttpException(__('video_not_found'));
        }

        if ($productId > 0) {
            $this->addVideoToProduct($video, $productId);
        }

        return $video->getId();
    }

    public function getVideoStorage(): VideoStorageInterface
    {
        return $this->videoStorage;
    }
}
