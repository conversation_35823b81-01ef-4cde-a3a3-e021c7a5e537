<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\PIM\Video;

use Aws\S3\BatchDelete;
use Aws\S3\S3Client;
use Aws\Sdk;
use Wizacha\AppBundle\Validator\UrlValidatorInterface;
use Wizacha\Marketplace\PIM\Video\Exception\FileSizeException;

class VideoStorage implements VideoStorageInterface
{
    /**
     * @var Sdk
     */
    private $awsTranscoder;

    /**
     * @var Sdk
     */
    private $awsTranscoderBucket;

    /**
     * @var Sdk
     */
    private $awsCustomerBucket;

    /**
     * @var string
     */
    private $bucket;

    /**
     * @var string
     */
    private $temporaryBucket;

    /** @var string */
    private $transcodingRegion;

    /**
     * @var int
     */
    private $maxSize;

    /**
     * @var string
     */
    private $pipeline;

    /**
     * @var string
     */
    private $preset;

    /**
     * @var string
     */
    private $prefix;

    /**
     * @var int
     */
    private $maxDuration;

    private UrlValidatorInterface $urlValidator;

    public function __construct(
        Sdk $awsTranscoder,
        Sdk $awsTranscoderBucket,
        Sdk $awsCustomerBucket,
        $bucket,
        $temporaryBucket,
        $transcodingRegion,
        $maxSize,
        $pipeline,
        $preset,
        $prefix,
        $maxDuration,
        UrlValidatorInterface $urlValidator
    ) {
        $this->awsTranscoder = $awsTranscoder;
        $this->awsTranscoderBucket = $awsTranscoderBucket;
        $this->awsCustomerBucket = $awsCustomerBucket;
        $this->bucket = $bucket;
        $this->temporaryBucket = $temporaryBucket;
        $this->transcodingRegion = $transcodingRegion;
        $this->maxSize = $maxSize;
        $this->pipeline = $pipeline;
        $this->preset = $preset;
        $this->prefix = $prefix;
        $this->maxDuration = $maxDuration;
        $this->urlValidator = $urlValidator;
    }

    public function deleteFromBucket($id)
    {
        $this->delete($this->awsCustomerBucket->createS3(), $id, $this->bucket);
    }

    public function deleteFromTmp($id)
    {
        $this->delete($this->awsTranscoderBucket->createS3(['region' => $this->transcodingRegion]), $id, $this->temporaryBucket);
    }

    public function generatePublicLinks(Video $video): array
    {
        $s3Url = sprintf(
            '//s3-%s.amazonaws.com/%s/%s%s/',
            $this->awsCustomerBucket->createS3()->getRegion(),
            $this->bucket,
            $this->prefix,
            $video->getId()
        );

        return [
            'thumb' => $s3Url . '480-00001.png',
            'path'  => $s3Url . '480.mp4',
        ];
    }

    public function generateUploadFormData(): array
    {
        //dates
        $shortDate = gmdate('Ymd'); //short date
        $isoDate = gmdate('Ymd\THis\Z'); //iso format date
        $expirationDate = gmdate('Y-m-d\TG:i:s\Z', strtotime('+1 hours')); //policy expiration 1 hour from now

        $s3 = $this->awsTranscoderBucket->createS3();
        $region = $this->transcodingRegion;
        $awsAccessKeyId = $s3->getCredentials()->wait()->getAccessKeyId();
        $awsSecretKey = $s3->getCredentials()->wait()->getSecretKey();

        $key = $this->prefix . 'tmp/' . md5(uniqid('', true)) . '/';

        //POST Policy required in order to control what is allowed in the request
        //For more info http://docs.aws.amazon.com/AmazonS3/latest/API/sigv4-HTTPPOSTConstructPolicy.html
        $policyBase64 = base64_encode(utf8_encode(json_encode([
            'expiration' => $expirationDate,
            'conditions' => [
                ['acl' => 'public-read'],
                ['bucket' => $this->temporaryBucket],
                ['success_action_status' => '201'],
                ['starts-with', '$key', $key],
                ['content-length-range', '1', $this->maxSize],
                ['x-amz-credential' => $awsAccessKeyId . '/' . $shortDate . '/' . $region . '/s3/aws4_request'],
                ['x-amz-algorithm' => 'AWS4-HMAC-SHA256'],
                ['X-amz-date' => $isoDate],
            ],
        ])));

        //Signature calculation (AWS Signature Version 4)
        //For more info http://docs.aws.amazon.com/AmazonS3/latest/API/sig-v4-authenticating-requests.html
        $kDate = hash_hmac('sha256', $shortDate, 'AWS4' . $awsSecretKey, true);
        $kRegion = hash_hmac('sha256', $region, $kDate, true);
        $kService = hash_hmac('sha256', 's3', $kRegion, true);
        $kSigning = hash_hmac('sha256', 'aws4_request', $kService, true);
        $signature = hash_hmac('sha256', $policyBase64, $kSigning);

        return [
            'awsBucket' => $this->temporaryBucket,
            'awsKey' => $awsAccessKeyId,
            'awsRegion' => $region,
            'shortDate' => $shortDate,
            'isoDate' => $isoDate,
            'policyBase64' => $policyBase64,
            'signature' => $signature,
            'key' => $key,
            'videoMaxDuration' => $this->maxDuration / 60,
            'videoMaxSize' => $this->maxSize / 1048576,
        ];
    }

    /**
     * @throws \Exception
     */
    public function transcodeVideo(string $tmpPathOnS3, string $uuid): string
    {
        if (!preg_match('#^' . $this->prefix . 'tmp/[a-f0-9]#', $tmpPathOnS3)) {
            throw new \InvalidArgumentException('The S3 path must begin with ' . $this->prefix . 'tmp');
        }

        $client = $this->awsTranscoder->createElasticTranscoder(['region' => $this->transcodingRegion]);
        $result = $client->createJob([
            'Input' => [
                'Key' => $tmpPathOnS3,
            ],
            'Outputs' => [
                [
                    'Key' => '480.mp4',
                    'PresetId' => $this->preset,
                    'ThumbnailPattern' => '480-{count}',
                    'Composition' => [
                        [
                            'TimeSpan' => [
                                'Duration' => $this->maxDuration . '.000',
                                'StartTime' => '0.000',
                            ],
                        ],
                    ],
                ],
            ],
            'OutputKeyPrefix' => $this->prefix . $uuid . '/',
            'PipelineId' => $this->pipeline,
        ]);

        $job = $result->get('Job');
        if (\in_array($job['Status'], ['Canceled', 'Error'])) {
            throw new \Exception('The transcoder job failed');
        }

        // check that the job is complete every 5 seconds for 2 minutes (24 = 2 * 60 / 5)
        $client->waitUntil('JobComplete', [
            'Id' => $job['Id'],
            '@waiter' => [
                'delay'       => 5,
                'maxAttempts' => 24,
            ],
        ]);

        return $uuid;
    }

    public function copyFileFromTmpToBucket(string $tmpPathOnS3, string $uuid)
    {
        $s3 = $this->awsCustomerBucket->createS3();
        $s3Tmp = $this->awsCustomerBucket->createS3(['region' => $this->transcodingRegion]);

        if ($this->temporaryBucket !== $this->bucket) {
            // Move original file from tmp to client's bucket
            $s3->copyObject([
                'Bucket' => $this->bucket,
                'Key' => $this->prefix . $uuid . '/' . basename($tmpPathOnS3),
                'CopySource' => "{$this->temporaryBucket}/{$tmpPathOnS3}"
            ]);
            $s3Tmp->deleteObject([
                'Bucket' => $this->temporaryBucket,
                'Key' => $tmpPathOnS3,
            ]);

            $s3->copyObject([
                'Bucket' => $this->bucket,
                'Key' => $this->prefix . $uuid . '/480.mp4',
                'CopySource' => "{$this->temporaryBucket}/" . $this->prefix . $uuid . '/480.mp4',
                'ACL' => 'public-read'
            ]);
            $s3->copyObject([
                'Bucket' => $this->bucket,
                'Key' => $this->prefix . $uuid . '/480-00001.png',
                'CopySource' => "{$this->temporaryBucket}/" . $this->prefix . $uuid . '/480-00001.png',
                'ACL' => 'public-read'
            ]);
        }
    }

    /**
     * @param string $url
     * @return string The tmp path on S3
     *
     * @throws \InvalidArgumentException
     * @throws FileSizeException
     */
    public function transferRemoteVideoToS3(string $url): string
    {
        if (!filter_var($url, FILTER_VALIDATE_URL) && false === fopen($url, 'r')) {
            throw new \InvalidArgumentException();
        }

        if ($this->urlValidator->isUrlValid($url) === true) {
            $headers = get_headers($url, 1);
            $headers = array_change_key_case($headers);
            $fileSize = -1;

            // Check that the URL return a valid response
            $code = explode(' ', $headers[0])[1];
            if (\intval($code) >= 400) {
                throw new \InvalidArgumentException();
            }
            if (\array_key_exists('content-length', $headers) === true) {
                $fileSize = (int) $headers['content-length'];
            }

            if ($fileSize > $this->maxSize) {
                throw new FileSizeException();
            }
        }

        $remoteFile = fopen($url, 'r');

        $this->awsTranscoderBucket->createS3()->registerStreamWrapper();
        $tmpPath = $this->prefix . 'tmp/' . md5(uniqid('', true)) . '/' . basename($url);
        $s3File = fopen('s3://' . $this->temporaryBucket . '/' . $tmpPath, 'w');

        stream_copy_to_stream($remoteFile, $s3File);

        fclose($remoteFile);
        fclose($s3File);

        return $tmpPath;
    }

    private function delete(S3Client $s3Client, $id, $bucket)
    {
        BatchDelete::fromListObjects($s3Client, [
            'Bucket' => $bucket,
            'Prefix' => $this->prefix . $id,
        ])->delete();
    }
}
