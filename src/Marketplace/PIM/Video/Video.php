<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\PIM\Video;

class Video
{
    /**
     * @var null|string
     */
    private $id;

    /**
     * @var int|null
     */
    private $productId;

    /**
     * @var array
     */
    private $links;

    /**
     * @param null|string $id
     * @param int|null $productId
     * @param array $links
     */
    public function __construct(?string $id = null, $productId = null, array $links = [])
    {
        $this->id = $id;
        $this->productId = $productId;
        $this->links = $links;
    }

    /**
     * @return null|string
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param string $id
     * @return $this
     */
    public function setId($id)
    {
        $this->id = (string) $id;

        return $this;
    }

    /**
     * @return int|null
     */
    public function getProductId()
    {
        return $this->productId;
    }

    /**
     * @param int|null $productId
     */
    public function setProductId($productId)
    {
        $this->productId = $productId;
    }

    /**
     * @return array
     */
    public function getLinks()
    {
        return $this->links;
    }

    /**
     * @param array $links
     */
    public function setLinks(array $links)
    {
        $this->links = $links;
    }

    /**
     * @return string
     */
    public function getThumbLink()
    {
        return isset($this->links['thumb']) ? $this->links['thumb'] : '';
    }

    /**
     * @return array
     */
    public function expose(): array
    {
        return [
            'id' => $this->getId(),
            'thumb' => $this->links['thumb'] ?? null,
            'path' => $this->links['path'] ?? null,
        ];
    }
}
