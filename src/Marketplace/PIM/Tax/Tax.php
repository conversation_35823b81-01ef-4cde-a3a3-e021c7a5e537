<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Tax;

use Doctrine\Common\Collections\Collection;

class Tax
{
    /** @var int */
    protected $id;

    /** @var ?string */
    protected $status;

    /** @var Collection|Description[] */
    protected $descriptions;

    /** @var TaxRate[] */
    protected $rates;

    private function __construct()
    {
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getName(): string
    {
        foreach ($this->descriptions->toArray() as $description) {
            /** @var Description $description */
            if ($description->isCurrentLocale()) {
                return $description->getName();
            }
        }

        return '';
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    /** @return TaxRate[] */
    public function getRates(): iterable
    {
        return $this->rates;
    }

    /** @param TaxRate[] $rates */
    public function setRates(iterable $rates): self
    {
        $this->rates = $rates;

        return $this;
    }
}
