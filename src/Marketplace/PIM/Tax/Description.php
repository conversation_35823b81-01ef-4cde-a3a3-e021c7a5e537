<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Tax;

use Wizacha\Component\Locale\Locale;
use Wizacha\Marketplace\GlobalState\GlobalState;

class Description
{
    /**
     * @var Tax
     */
    private $tax;

    /**
     * @var Locale
     */
    private $locale;

    /**
     * @var string
     */
    private $name;

    private function __construct()
    {
    }

    public function getTax(): Tax
    {
        return $this->tax;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function isCurrentLocale(): bool
    {
        return $this->locale->equals(GlobalState::contentLocale());
    }
}
