Wizacha\Marketplace\PIM\Tax\Description:
    type: entity
    table: tax_descriptions
    id:
        tax:
            associationKey: true
        locale:
            type: locale
            length: 2
            column: lang_code
            nullable: false
            options:
                default: ''
                fixed: true
    fields:
        name:
            type: string
            length: 255
            column: tax
            nullable: false
            options:
                default: ''
    manyToOne:
        tax:
            targetEntity: Wizacha\Marketplace\PIM\Tax\Tax
            inversedBy: descriptions
            joinColumn:
                name: tax_id
                referencedColumnName: tax_id
                nullable: false

