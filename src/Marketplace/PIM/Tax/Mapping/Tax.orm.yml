Wizacha\Marketplace\PIM\Tax\Tax:
    type: entity
    table: taxes
    id:
        id:
            type: integer
            column: tax_id
            generator:
                strategy: AUTO
            options:
                unsigned: true
    fields:
        status:
            type: string
            nullable: false
    oneToMany:
        descriptions:
            targetEntity: Wizacha\Marketplace\PIM\Tax\Description
            mappedBy: tax
            cascade: [ all ]
            fetch: EAGER
            orphanRemoval: true
        rates:
            targetEntity: Wizacha\Marketplace\PIM\Tax\TaxRate
            mappedBy: tax
            fetch: EAGER
