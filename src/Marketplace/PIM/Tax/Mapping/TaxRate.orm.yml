Wizacha\Marketplace\PIM\Tax\TaxRate:
    type: entity
    table: tax_rates
    id:
        tax:
            associationKey: true
    fields:
        rate:
            type: float
            column: rate_value
            nullable: false
            options:
                default: 0.0
        type:
            type: php_enum_tax_rate_type
            column: rate_type
            nullable: false
    manyToOne:
        tax:
            targetEntity: Wizacha\Marketplace\PIM\Tax\Tax
            inversedBy: rates
            joinColumn:
                name: tax_id
                referencedColumnName: tax_id
                nullable: false

