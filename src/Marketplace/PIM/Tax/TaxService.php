<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Tax;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;

class TaxService
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    /**
     * @return iterable|Tax[]
     */
    public function listAll(): iterable
    {
        return $this->getRepository()->findAll();
    }

    /**
     * @return iterable|Tax[]
     */
    public function listEnable(): iterable
    {
        return $this->getRepository()->findBy(['status' => 'A']);
    }

    private function getRepository(): EntityRepository
    {
        return $this->entityManager->getRepository(Tax::class);
    }
}
