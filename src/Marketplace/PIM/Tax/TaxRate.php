<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Tax;

use Wizacha\Marketplace\PIM\Tax\Enum\TaxRateType;

class TaxRate
{
    /** @var Tax */
    protected $tax;

    /** @var float */
    protected $rate;

    /** @var TaxRateType */
    protected $type;

    public function getTax(): Tax
    {
        return $this->tax;
    }

    public function setTax(Tax $tax): self
    {
        $this->tax = $tax;

        return $this;
    }

    public function getRate(): float
    {
        return $this->rate;
    }

    public function setRate(float $rate): self
    {
        $this->rate = $rate;

        return $this;
    }

    public function getType(): TaxRateType
    {
        return $this->type;
    }

    public function setType(TaxRateType $type): self
    {
        $this->type = $type;

        return $this;
    }
}
