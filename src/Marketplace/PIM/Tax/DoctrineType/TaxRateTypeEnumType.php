<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PIM\Tax\DoctrineType;

use Acelaya\Doctrine\Type\AbstractPhpEnumType;
use Wizacha\Marketplace\PIM\Tax\Enum\TaxRateType;

class TaxRateTypeEnumType extends AbstractPhpEnumType
{
    protected $enumType = TaxRateType::class;

    protected function getSpecificName(): string
    {
        return 'tax_rate_type';
    }
}
