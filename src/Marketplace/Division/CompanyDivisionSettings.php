<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Division;

use Doctrine\DBAL\Connection;
use Wizacha\Marketplace\Division\Repository\DivisionRepository;
use Wizacha\Marketplace\Division\Service\DivisionBlacklistsService;
use Wizacha\Marketplace\User\UserType;

final class CompanyDivisionSettings extends AbstractDivisionSettings
{
    /** @var string Used at company creation */
    public const DEFAULT_DIVISION = 'ALL';

    /** @var string */
    protected $table = 'cscart_companies';

    /** @var int */
    protected $companyId;

    private const EXCLUDED_DIVISIONS_COLUMN = 'divisions_excluded';
    private const INCLUDED_DIVISIONS_COLUMN = 'divisions_included';

    /** @var DivisionBlacklistsService */
    private $divisionBlacklistsService;

    public function __construct(
        Connection $connection,
        DivisionRepository $divisionRepository,
        DivisionSet $includedDivisions,
        DivisionSet $excludedDivisions,
        DivisionBlacklistsService $divisionBlacklistsService
    ) {
        parent::__construct($connection, $divisionRepository, $includedDivisions, $excludedDivisions);

        $this->divisionBlacklistsService = $divisionBlacklistsService;
    }

    public function setCompanyId(int $companyId): self
    {
        $this->companyId = $companyId;

        return $this;
    }

    /** @inheritDoc */
    public function hydrate(): self
    {
        if (false === \is_integer($this->companyId)) {
            throw new \LogicException('Please set a valid company id.');
        }

        $queryBuilder = $this->connection->createQueryBuilder();

        $excludedDivisions = $queryBuilder
            ->select(self::EXCLUDED_DIVISIONS_COLUMN)
            ->from($this->table)
            ->where($queryBuilder->expr()->eq('company_id', ':companyId'))
            ->setParameter('companyId', $this->companyId)
            ->execute()
            ->fetch()
        ;

        $this->excludedDivisions = (new DivisionSet())
            ->unserialize($excludedDivisions[self::EXCLUDED_DIVISIONS_COLUMN])
        ;

        $includedDivisions = $queryBuilder
            ->select(self::INCLUDED_DIVISIONS_COLUMN)
            ->from($this->table)
            ->where($queryBuilder->expr()->eq('company_id', ':companyId'))
            ->setParameter('companyId', $this->companyId)
            ->execute()
            ->fetch()
        ;

        $this->includedDivisions = (new DivisionSet())
            ->unserialize($includedDivisions[self::INCLUDED_DIVISIONS_COLUMN])
        ;

        return $this;
    }

    /** @inheritDoc */
    public function save(): self
    {
        $this->assertCompanyIdIsValid();

        $queryBuilder = $this->connection->createQueryBuilder();

        $queryBuilder
            ->update($this->table)
            ->set(self::EXCLUDED_DIVISIONS_COLUMN, ':excludedDivisions')
            ->where($queryBuilder->expr()->eq('company_id', ':companyId'))
            ->setParameter('companyId', $this->companyId)
            ->setParameter('excludedDivisions', $this->excludedDivisions->serialize())
            ->execute()
        ;

        $queryBuilder
            ->update($this->table)
            ->set(self::INCLUDED_DIVISIONS_COLUMN, ':includedDivisions')
            ->where($queryBuilder->expr()->eq('company_id', ':companyId'))
            ->setParameter('companyId', $this->companyId)
            ->setParameter('includedDivisions', $this->includedDivisions->serialize())
            ->execute()
        ;

        return $this;
    }

    /** @inheritDoc */
    public function dispatchSettings(): self
    {
        $this->assertCompanyIdIsValid();

        $this->divisionBlacklistsService->save(
            $this->divisionRepository->getCodesToBlackList(
                $this->getAllIncludedCodes(),
                $this->getAllExcludedCodes()
            ),
            $this->companyId,
            $this->userId,
            UserType::VENDOR
        );

        return $this;
    }

    /** @return mixed[] */
    public function toArray(): array
    {
        $result = parent::toArray();
        $result['company_id'] = $this->companyId;

        return $result;
    }

    protected function assertCompanyIdIsValid(): self
    {
        if (false === $this->isValidId($this->companyId)) {
            throw new \LogicException('CompanyId is not valid : ' . $this->companyId);
        }

        return $this;
    }
}
