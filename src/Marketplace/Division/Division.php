<?php

/**
 *  <AUTHOR> DevTeam <<EMAIL>>
 *  @copyright   Copyright (c) Wizacha
 *  @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Division;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Wizacha\Marketplace\Division\Exception\DescriptionNotFoundException;
use Wizacha\Marketplace\GlobalState\GlobalState;

/**
 * A Division Entity
 */
class Division
{
    /** @var string */
    private $code;

    /** @var string */
    private $alpha3;

    /** @var int */
    private $level;

    /** @var int */
    private $lft;

    /** @var int */
    private $rgt;

    /** @var bool */
    private $isEnabled;

    /** @var \DateTime */
    private $updatedAt;

    /** @var int */
    private $updatedBy;

    /** @var Collection|DivisionDescription[] */
    private $descriptions;

    /** @var null|Division */
    private $root;

    /** @var null|Division */
    private $parent;

    /** @var Collection|Division[] */
    private $children;

    public function __construct(
        string $code,
        ?Division $parent = null,
        int $level = 0,
        bool $isEnabled = true,
        \DateTime $updatedAt = null,
        int $updatedBy = null
    ) {
        $this->code = $code;
        $this->parent = $parent;
        $this->level = $level;
        $this->isEnabled = $isEnabled;
        $this->updatedAt = $updatedAt ?? new \DateTime();
        $this->updatedBy = $updatedBy;
        $this->descriptions = new ArrayCollection();
        $this->children = new ArrayCollection();
    }

    public function addDescription(DivisionDescription $description): self
    {
        if ($this->descriptions->contains($description) === false) {
            $this->descriptions->add($description);
            $description->setDivision($this);
        }

        return $this;
    }

    /**
     * Get Division description by language code
     * If asked language is null, we get the current content locale
     */
    public function getDescription(string $askedLanguage = null): ?DivisionDescription
    {
        $lang = $askedLanguage ?? (string) GlobalState::contentLocale();

        foreach ($this->getDescriptions() as $description) {
            if ($description->getLanguage() === $lang) {
                return $description;
            }
        }

        throw new DescriptionNotFoundException($lang, $this->getCode());
    }

    /**
     * @param iterable|DivisionDescription[] $descriptions
     */
    public function setDescriptions(iterable $descriptions): self
    {
        $this->clearDescriptions();

        foreach ($descriptions as $description) {
            $this->addDescription($description);
        }

        return $this;
    }

    /**
     * @return Collection|DivisionDescription[]
     */
    public function getDescriptions(): Collection
    {
        return $this->descriptions;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function setCode(string $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getAlpha3(): string
    {
        return $this->alpha3;
    }

    public function setAlpha3(string $alpha3): Division
    {
        $this->alpha3 = $alpha3;

        return $this;
    }

    public function getParentCode(): ?string
    {
        return $this->parent instanceof Division ? $this->parent->getCode() : null;
    }

    public function getLevel(): int
    {
        return $this->level;
    }

    public function setLevel(int $level): self
    {
        $this->level = $level;

        return $this;
    }

    public function isEnabled(): bool
    {
        return $this->isEnabled;
    }

    public function setIsEnabled(bool $isEnabled): self
    {
        $this->isEnabled = $isEnabled;

        return $this;
    }

    public function getUpdatedAt(): \DateTime
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(\DateTime $updatedAt): self
    {
        $this->updatedAt = $updatedAt;

        return $this;
    }

    public function getUpdatedBy(): int
    {
        return $this->updatedBy;
    }

    public function setUpdatedBy(int $updatedBy): self
    {
        $this->updatedBy = $updatedBy;

        return $this;
    }

    public function getParent(): ?Division
    {
        return $this->parent;
    }

    public function setParent(?Division $parent): self
    {
        $this->parent = $parent;

        return $this;
    }

    /**
     * @return Collection|Division[]
     */
    public function getChildren(): Collection
    {
        return $this->children;
    }

    /**
     * @param Collection|Division[] $children
     */
    public function setChildren(Collection $children): self
    {
        $this->children = $children;

        return $this;
    }

    public function addChild(Division $child): self
    {
        if ($this->children->contains($child)) {
            return $this;
        }

        $child->setParent($this);
        $this->children->add($child);

        return $this;
    }

    public function removeChild(Division $child): self
    {
        if (!$this->children->contains($child)) {
            return $this;
        }

        $this->children->removeElement($child);

        return $this;
    }

    public function removeDescription(DivisionDescription $description): self
    {
        if ($this->descriptions->contains($description)) {
            $this->descriptions->removeElement($description);
            $description->setDivision(null);
        }

        return $this;
    }

    public function clearDescriptions(): self
    {
        foreach ($this->getDescriptions() as $description) {
            $this->removeDescription($description);
        }
        $this->descriptions->clear();

        return $this;
    }

    public function getLeft(): int
    {
        return $this->lft;
    }

    public function getRight(): int
    {
        return $this->rgt;
    }
}
