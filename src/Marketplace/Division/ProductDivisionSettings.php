<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Division;

use Doctrine\DBAL\Connection;
use Wizacha\Marketplace\Division\Repository\DivisionRepository;
use Wizacha\Marketplace\Division\Service\DivisionBlacklistsService;
use Wizacha\Marketplace\Division\Service\DivisionProductsService;
use Wizacha\Marketplace\PIM\Product\ProductService;

final class ProductDivisionSettings extends AbstractDivisionSettings
{
    /** @var string The default included division  */
    public const DEFAULT_DIVISION = 'ALL';

    /** @var string */
    protected $table = 'cscart_products';

    /** @var int */
    protected $productId;

    private const EXCLUDED_DIVISIONS_COLUMN = 'divisions_excluded';
    private const INCLUDED_DIVISIONS_COLUMN = 'divisions_included';

    private DivisionBlacklistsService $divisionBlacklistsService;
    private DivisionProductsService $divisionProductsService;
    private ProductService $productService;

    public function __construct(
        Connection $connection,
        DivisionRepository $divisionRepository,
        DivisionSet $includedDivisions,
        DivisionSet $excludedDivisions,
        DivisionBlacklistsService $divisionBlacklistsService,
        DivisionProductsService $divisionProductsService,
        ProductService $productService
    ) {
        parent::__construct($connection, $divisionRepository, $includedDivisions, $excludedDivisions);

        $this->divisionBlacklistsService = $divisionBlacklistsService;
        $this->divisionProductsService = $divisionProductsService;
        $this->productService = $productService;
    }

    public function setProductId(int $productId): self
    {
        $this->productId = $productId;

        return $this;
    }

    /** @inheritDoc */
    public function hydrate(): self
    {
        $this->assertProductIdIsValid();

        $queryBuilder = $this->connection->createQueryBuilder();

        $excludedDivisions = $queryBuilder
            ->select(self::EXCLUDED_DIVISIONS_COLUMN)
            ->from($this->table)
            ->where($queryBuilder->expr()->eq('product_id', ':productId'))
            ->setParameter('productId', $this->productId)
            ->execute()
            ->fetch()
        ;

        $this->excludedDivisions = (new DivisionSet())
            ->unserialize($excludedDivisions[self::EXCLUDED_DIVISIONS_COLUMN])
        ;

        $includedDivisions = $queryBuilder
            ->select(self::INCLUDED_DIVISIONS_COLUMN)
            ->from($this->table)
            ->where($queryBuilder->expr()->eq('product_id', ':productId'))
            ->setParameter('productId', $this->productId)
            ->execute()
            ->fetch()
        ;

        $this->includedDivisions = (new DivisionSet())
            ->unserialize($includedDivisions[self::INCLUDED_DIVISIONS_COLUMN])
        ;

        return $this;
    }

    /** @inheritDoc */
    public function save(): self
    {
        $this->assertProductIdIsValid();

        $queryBuilder = $this->connection->createQueryBuilder();

        $queryBuilder
            ->update($this->table)
            ->set(self::EXCLUDED_DIVISIONS_COLUMN, ':excludedDivisions')
            ->where($queryBuilder->expr()->eq('product_id', ':productId'))
            ->setParameter('productId', $this->productId)
            ->setParameter('excludedDivisions', $this->excludedDivisions->serialize())
            ->execute()
        ;

        $queryBuilder
            ->update($this->table)
            ->set(self::INCLUDED_DIVISIONS_COLUMN, ':includedDivisions')
            ->where($queryBuilder->expr()->eq('product_id', ':productId'))
            ->setParameter('productId', $this->productId)
            ->setParameter('includedDivisions', $this->includedDivisions->serialize())
            ->execute()
        ;

        return $this;
    }

    /** @inheritDoc */
    public function dispatchSettings(): self
    {
        $this->assertProductIdIsValid();

        $companyId = $this->productService->get($this->productId)->getCompanyId();

        // We need a valid companyId
        if (false === $this->isValidId($companyId)) {
            throw new \LogicException('CompanyId is not valid : ' . $companyId);
        }

        // Get company blacklisted codes
        $blacklistedCodes = $this->divisionRepository->getDivisionBlacklistsCodes($companyId);

        // Merge blacklisted codes and $this->excluded codes
        $excludedCodes = \array_unique(
            \array_merge(
                $blacklistedCodes,
                $this->getAllExcludedCodes()
            )
        );

        // Get codes to enable
        $codes = \array_diff(
            $this->getAllIncludedCodes(),
            $excludedCodes
        );

        $this->divisionProductsService->save($codes, $this->productId, $this->userId);

        return $this;
    }

    /** @return mixed[] */
    public function toArray(): array
    {
        $result = parent::toArray();
        $result['product_id'] = $this->productId;

        return $result;
    }

    protected function assertProductIdIsValid(): self
    {
        if (false === $this->isValidId($this->productId)) {
            throw new \LogicException('productId is not valid : ' . $this->productId);
        }

        return $this;
    }
}
