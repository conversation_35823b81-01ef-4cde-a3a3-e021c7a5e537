<?php

/**
 *  <AUTHOR> DevTeam <<EMAIL>>
 *  @copyright   Copyright (c) Wizacha
 *  @license     Proprietary
 */

namespace Wizacha\Marketplace\Division\Service;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Wizacha\Events\IterableEvent;
use Wizacha\Marketplace\Division\Division;
use Wizacha\Marketplace\Division\Exception\DivisionFeatureNotEnabled;
use Wizacha\Marketplace\Division\Repository\DivisionRepository;
use Wizacha\Marketplace\ReadModel\ProductProjector;
use Wizacha\Product;

class DivisionProductsService implements EventSubscriberInterface
{
    /**
     * @var DivisionRepository
     */
    private $divisionRepository;

    /**
     * @var DivisionService
     */
    private $divisionService;

    /**
     * @var EventDispatcherInterface
     */
    private $eventDispatcher;

    /**
     * @var ProductProjector
     */
    private $productProjector;

    /**
     * @var bool
     */
    private $featureIsEnabled;

    /** @var Connection */
    private $connection;

    public function __construct(
        DivisionRepository $divisionRepository,
        DivisionService $divisionService,
        EventDispatcherInterface $eventDispatcher,
        ProductProjector $productProjector,
        Connection $connection
    ) {
        $this->divisionRepository = $divisionRepository;
        $this->divisionService    = $divisionService;
        $this->eventDispatcher    = $eventDispatcher;
        $this->productProjector   = $productProjector;
        $this->connection = $connection;
    }

    public function featureIsEnabled(bool $enabled)
    {
        $this->featureIsEnabled = $enabled;
    }

    public function getAllDivisionsCode(int $productId): array
    {
        if (!$this->featureIsEnabled) {
            return [];
        }

        return $this->divisionRepository->getDivisionsCodeFromProductId($productId);
    }

    /**
     * Create all division for productIds
     *
     * @param array $productsIds
     *
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     * @throws DivisionFeatureNotEnabled
     */
    public function createDivisionForProducts(array $productsIds, ProgressBar $progressBar = null): void
    {
        if (!$this->featureIsEnabled) {
            throw new DivisionFeatureNotEnabled();
        }

        $createdAt = (new \DateTime())->format("Y-m-d H:i:s");

        $divisionsEnabled = $this->divisionRepository->getAllIds(true);
        $divisionsChunk = array_chunk($divisionsEnabled, 250);

        if ($progressBar instanceof ProgressBar) {
            $progressBar->start(\count($productsIds) * \count($divisionsEnabled));
        }

        foreach ($productsIds as $productId) {
            foreach ($divisionsChunk as $divisions) {
                $count = 0;

                /** @var Division $division */
                foreach ($divisions as $divisionCode) {
                    $this
                        ->connection
                        ->prepare("
                            INSERT IGNORE INTO doctrine_division_products (division_code, product_id, created_at, created_by)
                            VALUE (:code, :pid, :created_at, 1);
                        ")
                        ->execute([
                            'code' => $divisionCode,
                            'pid' => $productId,
                            'created_at' => $createdAt,
                        ]);

                    ++$count;
                }

                if ($progressBar instanceof ProgressBar) {
                    $progressBar->advance($count);
                }
            }

            $this->productProjector->projectProduct($productId);
        }

        if ($progressBar instanceof ProgressBar) {
            $progressBar->finish();
        }
    }

    /**
     * Remove and save product's divisions
     *
     * @param null|string[] $divisions
     * @param int           $productId
     * @param int           $userId
     */
    public function save(?array $divisions, int $productId, int $userId)
    {
        $this->divisionRepository->deleteDivisionsProducts($productId);

        if (\is_array($divisions)) {
            $this->divisionRepository->saveDivisionsProducts($divisions, $productId, $userId);
        }

        $this->productProjector->projectProduct($productId);
    }

    /**
     * @param array|null $divisions
     */
    public function deleteProductsFromAdmin(?array $divisions): void
    {
        if (\is_null($divisions)) {
            $divisions = [];
        }

        $this->divisionRepository->deleteProductsFromAdmin($divisions);
    }

    public static function getSubscribedEvents()
    {
        return [
            Product::EVENT_DELETE => ['onProductDelete', 0],
        ];
    }

    /**
     * Delete product from `division_products` when a product is deleted
     *
     * @param IterableEvent $event
     *
     * @throws \Doctrine\ORM\ORMException
     */
    public function onProductDelete(IterableEvent $event): void
    {
        if (!$this->featureIsEnabled) {
            return;
        }

        foreach ($event as $productId) {
            $this->divisionRepository->deleteDivisionsProducts($productId);
        }
    }
}
