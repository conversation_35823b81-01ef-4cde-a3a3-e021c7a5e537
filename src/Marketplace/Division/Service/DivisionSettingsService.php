<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Division\Service;

use Wizacha\AppBundle\Controller\Api\Division\Dto\ApiDivisionSettingsDto;
use Wizacha\Async\Dispatcher;
use Wizacha\Async\Exception\UnavailableDispatcherException;
use Wizacha\Component\Log\LogServiceInterface;
use Wizacha\Marketplace\Catalog\Company\CompanyService;
use Wizacha\Marketplace\Division\AbstractDivisionSettings;
use Wizacha\Marketplace\Division\CompanyDivisionSettings;
use Wizacha\Marketplace\Division\Division;
use Wizacha\Marketplace\Division\DivisionSet;
use Wizacha\Marketplace\Division\Exception\InvalidDivisionCodeException;
use Wizacha\Marketplace\Division\Exception\InvalidProductDivisionCodeException;
use Wizacha\Marketplace\Division\Exception\InvalidProductDivisionExcludedCodeException;
use Wizacha\Marketplace\Division\Exception\InvalidProductDivisionIncludeCodeException;
use Wizacha\Marketplace\Division\MarketplaceDivisionSettings;
use Wizacha\Marketplace\Division\ProductDivisionSettings;
use Wizacha\Marketplace\Division\Repository\DivisionRepository;

class DivisionSettingsService
{
    /** @var MarketplaceDivisionSettings */
    private $marketplaceDivisionSettings;

    /** @var CompanyDivisionSettings */
    private $companyDivisionSettings;

    /** @var ProductDivisionSettings */
    private $productDivisionSettings;

    /** @var DivisionRepository */
    protected $divisionRepository;

    /** @var ?Dispatcher */
    private $asyncDispatcher;

    /** @var CompanyService */
    private $companyService;

    /** @var LogServiceInterface */
    private $logService;

    public function __construct(
        MarketplaceDivisionSettings $marketplaceDivisionSettings,
        CompanyDivisionSettings $companyDivisionSettings,
        ProductDivisionSettings $productDivisionSettings,
        DivisionRepository $divisionRepository,
        Dispatcher $asyncDispatcher,
        CompanyService $companyService
    ) {
        $this->marketplaceDivisionSettings = $marketplaceDivisionSettings;
        $this->companyDivisionSettings = $companyDivisionSettings;
        $this->productDivisionSettings = $productDivisionSettings;
        $this->divisionRepository = $divisionRepository;
        $this->asyncDispatcher = $asyncDispatcher;
        $this->companyService = $companyService;
    }

    public function disableAsync(): self
    {
        $this->asyncDispatcher = null;

        return $this;
    }

    public function setLogger(LogServiceInterface $logService): self
    {
        $this->logService = $logService;

        return $this;
    }

    public function getMarketplaceDivisionSettings(): MarketplaceDivisionSettings
    {
        return $this->marketplaceDivisionSettings->hydrate();
    }

    public function updateMarketplaceDivisionSettings(ApiDivisionSettingsDto $apiDivisionSettingsDto): void
    {
        $includedDivisionSet = $apiDivisionSettingsDto->getIncluded();
        $excludedDivisionSet = $apiDivisionSettingsDto->getExcluded();

        $this->assertCodesAreValid($includedDivisionSet, $excludedDivisionSet);

        // Save marketplace divisions
        $this->marketplaceDivisionSettings
            ->setIncludedDivisions(new DivisionSet($includedDivisionSet))
            ->setExcludedDivisions(new DivisionSet($excludedDivisionSet))
            ->save()
        ;

        // Dispatch marketplace divisions update job
        $this->delayExec(
            'onAsyncMarketplaceDivisionSettingsUpdate',
            $this->marketplaceDivisionSettings
        );
    }

    public function getCompanyDivisionSettings(int $companyId): CompanyDivisionSettings
    {
        return $this->companyDivisionSettings
            ->setCompanyId($companyId)
            ->hydrate()
        ;
    }

    public function updateCompanyDivisionSettings(int $companyId, ApiDivisionSettingsDto $apiDivisionSettingsDto): void
    {
        $includedDivisionSet = $apiDivisionSettingsDto->getIncluded();
        $excludedDivisionSet = $apiDivisionSettingsDto->getExcluded();

        $this->assertCodesAreValid($includedDivisionSet, $excludedDivisionSet);

        // Save company divisions
        $this->companyDivisionSettings
            ->setCompanyId($companyId)
            ->setIncludedDivisions(new DivisionSet($includedDivisionSet))
            ->setExcludedDivisions(new DivisionSet($excludedDivisionSet))
            ->save()
        ;

        // Dispatch company divisions update job
        $this->delayExec(
            'onAsyncCompanyDivisionSettingsDispatch',
            [
                'company_id' => (int) $companyId,
                'products_id' => fn_find_all_product_ids_by_company_id((int) $companyId),
            ]
        );
    }

    public function getProductDivisionSettings(int $productId): ProductDivisionSettings
    {
        return $this->productDivisionSettings
            ->setProductId($productId)
            ->hydrate()
        ;
    }

    public function updateProductDivisionSettings(
        int $productId,
        ApiDivisionSettingsDto $apiDivisionSettingsDto
    ): void {
        $includedDivisionValidation = $excludedDivisionValidation = [
            'valid_divisions' => [],
            'invalid_divisions' => []
        ];

        $productSetting = $this->getProductDivisionSettings($productId);
        $this->productDivisionSettings
            ->setProductId($productId)
            ->setIncludedDivisions($productSetting->getIncludedDivisions())
            ->setExcludedDivisions($productSetting->getExcludedDivisions());

        $includedDivisionValidationData = $this->repairArray($apiDivisionSettingsDto->getIncluded());
        $excludedDivisionValidationData = $this->repairArray($apiDivisionSettingsDto->getExcluded());

        if (\count($includedDivisionValidationData) > 0) {
            $includedDivisionValidation = $this->validateProductCodes($includedDivisionValidationData);
        }

        if (\count($excludedDivisionValidationData) > 0) {
            $excludedDivisionValidation = $this->validateProductCodes($excludedDivisionValidationData);
        }

        $doUpdateIncludedDivision = $doUpdateExcludedDivisions = false;

        if (\count($includedDivisionValidation['valid_divisions']) > 0
            || \count($includedDivisionValidationData) === 0
        ) {
            $doUpdateIncludedDivision = true;
            $this->productDivisionSettings
                ->setIncludedDivisions(new DivisionSet($includedDivisionValidation['valid_divisions']));
        }

        if (\count($excludedDivisionValidation['valid_divisions']) > 0
            || \count($excludedDivisionValidationData) === 0
        ) {
            $doUpdateExcludedDivisions = true;
            $this->productDivisionSettings
                ->setExcludedDivisions(new DivisionSet($excludedDivisionValidation['valid_divisions']));
        }

        if (($doUpdateExcludedDivisions || $doUpdateIncludedDivision) === true) {
            $this->productDivisionSettings
                ->save();

            // Dispatch product divisions update job
            $this->delayExec(
                'onAsyncProductDivisionSettingsUpdate',
                $this->productDivisionSettings
            );
        }

        $isAllDivisionIncludedInvalid = \count($includedDivisionValidationData) > 0
            && \count($includedDivisionValidationData) === \count($includedDivisionValidation['invalid_divisions']);

        $isAllDivisionExcludedInvalid = \count($excludedDivisionValidationData) > 0
            && \count($excludedDivisionValidationData) === \count($excludedDivisionValidation['invalid_divisions']);

        if ($isAllDivisionIncludedInvalid === true
            && $isAllDivisionExcludedInvalid === true
        ) {
            throw new InvalidProductDivisionCodeException(
                __('division_invalid_code'),
                $productId
            );
        } elseif ($isAllDivisionIncludedInvalid === true) {
            throw new InvalidProductDivisionIncludeCodeException(
                __('division_included_invalid_code'),
                $productId
            );
        } elseif ($isAllDivisionExcludedInvalid === true) {
            throw new InvalidProductDivisionExcludedCodeException(
                __('division_excluded_invalid_code'),
                $productId
            );
        }
    }

    /**
     * Called when a marketplace division update is found by an AMQP worker
     * or the sync-way if AMQP message has not been successfully sent
     *
     * @param mixed[] $payload
     */
    public function onAsyncMarketplaceDivisionSettingsUpdate(array $payload = null): void
    {
        $this->log('Dispatch marketplace division settings.');

        $this
            ->getMarketplaceDivisionSettings()
            ->dispatchSettings()
        ;

        // Dispatch company settings
        foreach ($this->companyService->getCompaniesId() as $companyId) {
            // Dispatch company setting to its products
            $this->delayExec(
                'onAsyncCompanyDivisionSettingsDispatch',
                [
                    'company_id' => (int) $companyId,
                    'products_id' => fn_find_all_product_ids_by_company_id((int) $companyId),
                ]
            );
        }
    }

    /**
     * Called when company division update is found by AMQP worker
     * @param mixed[] $amqpMessage
     */
    public function onAsyncCompanyDivisionSettingsDispatch($amqpMessage): void
    {
        $companyId = $amqpMessage['company_id'];

        $this->log(
            'Dispatch company division settings with id:' .
            ($companyId ?? 'NULL') .
            ' division settings.'
        );

        if (\array_key_exists('company_id', $amqpMessage)
            && \is_int($companyId)
            && $companyId > 0
        ) {
            $this->progress();

            // Dispatch company settings
            $this->delayExec(
                'onAsyncCompanyDivisionSettingsUpdate',
                [
                    'company_id' => $companyId,
                ]
            );

            if (\array_key_exists('products_id', $amqpMessage)) {
                // Dispatch company setting to its products
                foreach ($amqpMessage['products_id'] as $productId) {
                    $this->delayExec(
                        'onAsyncProductDivisionSettingsUpdate',
                        [
                            'product_id' => $productId,
                        ]
                    );
                }
            }
        } else {
            $this->progress();
        }
    }

    /** @param int[] $amqpMessage */
    public function onAsyncCompanyDivisionSettingsUpdate(array $amqpMessage): void
    {
        $this
            ->getCompanyDivisionSettings((int) $amqpMessage['company_id'])
            ->dispatchSettings()
        ;
    }

    /**
     * Called when product division update is found by AMQP worker
     * @param int[] $amqpMessage
     */
    public function onAsyncProductDivisionSettingsUpdate(array $amqpMessage): void
    {
        $this->log(
            'Dispatch product division settings with id:' .
            ($amqpMessage['product_id'] ?? 'NULL') .
            ' division settings.'
        );

        if (true === \array_key_exists('product_id', $amqpMessage)
            && true === \is_int($amqpMessage['product_id'])
            && true === $amqpMessage['product_id'] > 0
        ) {
            $this
                ->getProductDivisionSettings((int) $amqpMessage['product_id'])
                ->dispatchSettings()
            ;
        }

        $this->progress();
    }
    /**
     * @param string[] $includedDivisions
     * @param string[] $excludedDivisions
     */
    private function assertCodesAreValid(array $includedDivisions, array $excludedDivisions): self
    {
        $divisionCodes = \array_filter(\array_merge($includedDivisions, $excludedDivisions));

        if ($this->divisionRepository->countValidCodes($divisionCodes) !== \count($divisionCodes)) {
            throw new InvalidDivisionCodeException(__('division_invalid_code_provided'));
        }

        return $this;
    }

    /**
     * @param array $divisions
     *
     * @return array[array valid_divisions, array invalid_divisions]
     */
    private function validateProductCodes(array $divisions): array
    {
        $divisionsFound = \array_map(static function (Division $division): string {
            return $division->getCode();
        }, $this->divisionRepository->getDivisionsByCodes($divisions));

        $result['valid_divisions'] = \array_intersect($divisions, $divisionsFound);
        $result['invalid_divisions'] = \array_diff($divisions, $divisionsFound);

        return $result;
    }

    /** @param AbstractDivisionSettings|array $args */
    private function delayExec(string $method, $args): void
    {
        if (false === \method_exists($this, $method)) {
            throw new UnavailableDispatcherException(__('async_invalid_callback'));
        }

        if ($args instanceof AbstractDivisionSettings) {
            $args = $args->toArray();
        }

        try {
            if (\is_null($this->asyncDispatcher)) {
                throw new \Exception('Async dispatcher is not configured.');
            }

            $asyncResponse = $this->asyncDispatcher->delayExec(
                'marketplace.divisions_settings.service::' . $method,
                [$args]
            );
        } catch (\Exception $exception) {
            $asyncResponse = false;
        }

        // If async call has failed for any reason, execute it in sync mode
        if (false === $asyncResponse) {
            \call_user_func([$this, $method], $args);
        }
    }

    private function log(string $message)
    {
        if ($this->logService instanceof LogServiceInterface) {
            $this->logService->write($message);
        }
    }

    private function progress()
    {
        if ($this->logService instanceof LogServiceInterface
            && \method_exists($this->logService, 'progress')
        ) {
            $this->logService->progress();
        }
    }

    private function repairArray(array $array): array
    {
        if (\count($array) === 1
            && \reset($array) === ''
        ) {
            $array = [];
        }

        return $array;
    }
}
