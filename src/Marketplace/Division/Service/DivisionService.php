<?php

/**
 *  <AUTHOR> DevTeam <<EMAIL>>
 *  @copyright   Copyright (c) Wizacha
 *  @license     Proprietary
 */

namespace Wizacha\Marketplace\Division\Service;

use Wizacha\Marketplace\Division\Division;
use Wizacha\Marketplace\Division\DivisionDescription;
use Wizacha\Marketplace\Division\Repository\DivisionRepository;
use Wizacha\Marketplace\ReadModel\ProductRepository;

class DivisionService
{
    /**
     * The root division code
     */
    public const ROOT_CODE = 'ALL';

    /**
     * @var DivisionRepository
     */
    private $repo;

    /**
     * @var bool
     */
    private $featureIsEnabled;

    private const INCLUDED_DIVISIONS_LABEL = 'Divisions included';
    private const EXCLUDED_DIVISIONS_LABEL = 'Divisions excluded';
    private const DIVISIONS_SEPARATOR = '|';

    /** @var string[] */
    private const DIVISION_DTO_MAPPING = [
        'code',
        'level',
        'isEnabled',
        'name',
    ];

    /** @var ProductRepository */
    private $productRepository;

    public function __construct(DivisionRepository $divisionRepository, ProductRepository $productRepository)
    {
        $this->repo = $divisionRepository;
        $this->productRepository = $productRepository;
    }

    public function featureIsEnabled(bool $enabled)
    {
        $this->featureIsEnabled = $enabled;
    }

    /**
     * @param Division $division
     *
     * @return Division
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function save(Division $division): Division
    {
        return $this->repo->saveDivision($division);
    }

    /**
     * @param DivisionDescription $divisionDescription
     *
     * @return DivisionDescription
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function createDivisionDescription(DivisionDescription $divisionDescription): DivisionDescription
    {
        return $this->repo->saveDescription($divisionDescription);
    }

    /**
     * Imbricate array to get a tree of divisions
     *
     * @param \stdClass[] $divisions
     * @param bool     $snakeCase
     *
     * @return object[]
     */
    public function imbricate(array $divisions, $snakeCase = false): array
    {
        $parentCode = 'parentCode';
        if ($snakeCase) {
            $parentCode = 'parent_code';
        }

        $objectsById = array();
        foreach ($divisions as $d) {
            $objectsById[$d->code] = $d;
        }

        foreach ($divisions as $key => $d) {
            if (\is_string($d->{$parentCode})
                && \array_key_exists($d->{$parentCode}, $objectsById)
            ) {
                // Ajout de l'enfant dans le parent
                $objectsById[$d->{$parentCode}]->children[] = $d;
                unset($divisions[$key]);
            }
        }

        return $divisions;
    }

    /**
     * Get Divisions for the marketplace.
     * Can be filtered by isEnabled and rootCode.
     *
     * @return mixed[] The divisions in array format, can be a tree
     */
    public function getMarketplaceDivisions(
        string $locale,
        bool $needImbricate,
        bool $isEnabled = null,
        string $rootCode = null
    ): array {
        $divisions = $this->repo->getDivisions(null, null, $locale, $isEnabled, null, null, $rootCode);

        if ($needImbricate) {
            $divisions = $this->imbricate($divisions);
        }

        return $this->filterStdClassFields($divisions);
    }

    /**
     * Get Divisions for a company.
     * Can be filtered by isEnabled and rootCode.
     *
     * @return mixed[] The divisions in array format, can be a tree
     */
    public function getCompanyDivisions(
        int $companyId,
        string $locale,
        bool $needImbricate,
        bool $isEnabled = null,
        string $rootCode = null
    ): array {
        $divisions = $this->repo->getDivisions($companyId, null, $locale, null, null, null, $rootCode);

        $checkIsEnabled = function (\stdClass $division): bool {
            return false === \is_int($division->companyId);
        };

        return $this->filterDivisions($divisions, $checkIsEnabled, $needImbricate, $isEnabled);
    }

    /**
     * Get Divisions for a product.
     * Can be filtered by isEnabled and rootCode.
     *
     * @return mixed[] The divisions in array format, can be a tree
     */
    public function getProductDivisions(
        int $productId,
        string $locale,
        bool $needImbricate,
        bool $isEnabled = null,
        string $rootCode = null
    ): array {
        $divisions = $this->repo->getDivisions(null, $productId, $locale, null, null, null, $rootCode);

        $checkIsEnabled = function (\stdClass $division): bool {
            return \is_int($division->productId);
        };

        return $this->filterDivisions($divisions, $checkIsEnabled, $needImbricate, $isEnabled);
    }

    /**
     * @param \stdClass[] $divisions
     * @param callable $checkIsEnabled
     * @param bool $needImbricate
     * @param bool|null $isEnabled
     *
     * @return mixed[] The divisions in array format, can be a tree
     */
    public function filterDivisions(
        array $divisions,
        callable $checkIsEnabled,
        bool $needImbricate,
        ?bool $isEnabled
    ): array {
        foreach ($divisions as $key => &$division) {
            // set enabled/disabled
            $division->isEnabled = $checkIsEnabled($division);

            // filter
            if (\is_bool($isEnabled) && $isEnabled !== $division->isEnabled) {
                unset($divisions[$key]);
            }
        }
        unset($division);

        if ($needImbricate) {
            $divisions = $this->imbricate($divisions);
        }

        return $this->filterStdClassFields($divisions);
    }

    /**
     * Get divisions to export them in a CSV (vendor + admin)
     */
    public function getDivisionsCSV(int $companyId = null): array
    {
        /** @var DivisionSettingsService $divisionsSettingsService */
        $divisionsSettingsService = container()->get('marketplace.divisions_settings.service');

        $divisionsSettings = (\is_null($companyId))
            ? $divisionsSettingsService->getMarketplaceDivisionSettings()
            : $divisionsSettingsService->getCompanyDivisionSettings($companyId)
        ;

        $divisionsSettings->hydrate();

        return
            [
                [
                    self::INCLUDED_DIVISIONS_LABEL => implode(
                        static::DIVISIONS_SEPARATOR,
                        $divisionsSettings
                            ->getIncludedDivisions()
                            ->toArray()
                    ),
                    self::EXCLUDED_DIVISIONS_LABEL => implode(
                        static::DIVISIONS_SEPARATOR,
                        $divisionsSettings
                            ->getExcludedDivisions()
                            ->toArray()
                    ),
                ]
            ];
    }

    public function fetchDivisionsParentCode(array $codes): array
    {
        if (empty($codes)) {
            return [];
        }

        return $this->repo->getDivisionsParentCode($codes);
    }

    /**
     * @param \stdClass[] $stdDivisions
     * @return \stdClass[]
     */
    public function filterStdClassFields(array $stdDivisions): array
    {
        $result = [];

        foreach ($stdDivisions as $stdDivision) {
            $division = new \stdClass();

            foreach (static::DIVISION_DTO_MAPPING as $mappingField) {
                $division->{$mappingField} = $stdDivision->{$mappingField};
            }

            if (\is_array($stdDivision->children)) {
                $division->children = $this->filterStdClassFields($stdDivision->children);
            }

            $result[] = $division;
        }

        return $result;
    }
}
