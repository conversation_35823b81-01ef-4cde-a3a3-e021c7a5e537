<?php

/**
 *  <AUTHOR> DevTeam <<EMAIL>>
 *  @copyright   Copyright (c) Wizacha
 *  @license     Proprietary
 */

namespace Wizacha\Marketplace\Division\Service;

use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Wizacha\Events\IterableEvent;
use Wizacha\Marketplace\Company\CompanyEvents;
use Wizacha\Marketplace\Division\DivisionBlacklist;
use Wizacha\Marketplace\Division\Repository\DivisionRepository;
use Wizacha\Marketplace\User\UserType;

class DivisionBlacklistsService implements EventSubscriberInterface
{
    /**
     * @var DivisionRepository
     */
    private $divisionRepository;

    /**
     * @var DivisionProductsService
     */
    private $divisionProductsService;

    /**
     * @var DivisionService
     */
    private $divisionService;

    /**
     * @var EventDispatcherInterface
     */
    private $eventDispatcher;

    /**
     * @var bool
     */
    private $featureIsEnabled;

    public function __construct(
        DivisionRepository $divisionRepository,
        DivisionProductsService $divisionProductsService,
        DivisionService $divisionService,
        EventDispatcherInterface $eventDispatcher
    ) {
        $this->divisionRepository      = $divisionRepository;
        $this->divisionProductsService = $divisionProductsService;
        $this->divisionService         = $divisionService;
        $this->eventDispatcher         = $eventDispatcher;
    }

    public function featureIsEnabled(bool $enabled)
    {
        $this->featureIsEnabled = $enabled;
    }

    /** @return string[] */
    public function getDivisionBlacklistsByCompanyId(int $companyId): array
    {
        return $this->divisionRepository->getDivisionBlacklistsCodes($companyId);
    }

    /**
     * Remove and save blacklist's divisions
     *
     * @param string[] $divisionsCodeToBlacklist
     * @param int $companyId
     * @param int $userId
     * @param string $userType
     * @param array $adminBlacklists
     */
    public function save(
        array $divisionsCodeToBlacklist,
        int $companyId,
        int $userId,
        string $userType,
        array $adminBlacklists = []
    ): self {
        $currentBlackListVendor = $this->divisionRepository->getDivisionBlacklistsDisabledBy(
            $companyId,
            UserType::VENDOR()->getValue()
        );

        $this->updateBlackListFromLists($currentBlackListVendor, $divisionsCodeToBlacklist, $companyId, $userId, $userType);

        // Edit of BlackList divisions only if current usr is Admin
        if ($userType === UserType::ADMIN()->getValue()) {
            $currentBlackListAdmin = $this->divisionRepository->getDivisionBlacklistsDisabledBy(
                $companyId,
                UserType::ADMIN()->getValue()
            );

            $this->updateBlackListFromLists(
                $currentBlackListAdmin,
                $adminBlacklists,
                $companyId,
                $userId,
                UserType::ADMIN()->getValue()
            );
        }

        $divisionsCodeToBlacklist = array_merge($divisionsCodeToBlacklist, $adminBlacklists);

        $this->blacklistCompanyProductsDivisions(
            $this->getDivisionBlacklistsByCompanyId($companyId),
            $divisionsCodeToBlacklist,
            $companyId
        );

        return $this;
    }

    /**
     * @param DivisionBlacklist[] $blacklistedDivisionCodes
     * @param string[]            $divisionsToBlacklist
     * @param int                 $companyId
     */
    public function blacklistCompanyProductsDivisions(
        array $blacklistedDivisionCodes,
        array $divisionsToBlacklist,
        int $companyId
    ): void {
        foreach ($blacklistedDivisionCodes as $divisionCode) {
            if ($key = \array_search($divisionCode, $divisionsToBlacklist)) {
                unset($divisionsToBlacklist[$key]);
            }
        }

        if (\count($divisionsToBlacklist) > 0) {
            $productsId = fn_find_all_product_ids_by_company_id($companyId);
            $this->divisionRepository->deleteProductsFromBlacklist($divisionsToBlacklist, $productsId);
        }
    }

    /**
     * @param string[] $divisionsCode
     */
    public function insert(array $divisionsCode, int $companyId, int $userId, string $userType): void
    {
        if (\count($divisionsCode) > 0) {
            $this->divisionRepository->saveDivisionBlacklists($divisionsCode, $companyId, $userId, $userType);
        }
    }

    /** @param string[] $divisionCodes */
    public function delete(array $divisionCodes, int $companyId): void
    {
        $this->divisionRepository->deleteDivisionBlacklist($divisionCodes, $companyId);
    }

    public static function getSubscribedEvents()
    {
        return [
            CompanyEvents::DELETED => ['onCompanyDelete', 0],
        ];
    }

    public function onCompanyDelete(IterableEvent $event): void
    {
        if (!$this->featureIsEnabled) {
            return;
        }

        foreach ($event as $companyId) {
            $this->divisionRepository->deleteDivisionsBlacklists($companyId);
        }
    }

    /**
     * @param string[] $oldListCodes
     * @param string[] $blacklistCodes
     */
    protected function updateBlackListFromLists(
        array $oldListCodes,
        array $blacklistCodes,
        int $companyId,
        int $userId,
        string $userType
    ): self {
        // 1st: Check Missing blackList (It's deleted)
        $this->delete(\array_diff($oldListCodes, $blacklistCodes), $companyId);
        // 2nd: Check new Entries
        $this->insert(\array_diff($blacklistCodes, $oldListCodes), $companyId, $userId, $userType);

        return $this;
    }
}
