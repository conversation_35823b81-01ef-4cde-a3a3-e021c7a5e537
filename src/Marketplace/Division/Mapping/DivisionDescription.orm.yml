Wizacha\Marketplace\Division\DivisionDescription:
    type: entity
    table: division_descriptions
    id:
        id:
            type: integer
            generator:
                strategy: IDENTITY
    fields:
        language:
            type: string
        description:
            type: string
            nullable: false
    manyToOne:
        division:
            targetEntity: Wizacha\Marketplace\Division\Division
            inversedBy: descriptions
            joinColumn:
                name: division_code
                referencedColumnName: code
                onDelete: CASCADE
    uniqueConstraints:
        uniq_division_language:
            columns: [ division_code, language ]
