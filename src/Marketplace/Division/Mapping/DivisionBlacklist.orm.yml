Wizacha\Marketplace\Division\DivisionBlacklist:
    type: entity
    table: division_blacklists
    id:
        id:
            type: integer
            generator:
                strategy: AUTO
    fields:
        divisionCode:
            type: string
            nullable: false
        companyId:
            type: integer
            nullable: false
        disabledBy:
            type: user_type
            nullable: false
        createdAt:
            type: datetime
            nullable: false
        createdBy:
            type: integer
            nullable: false
    oneToOne:
        division:
            targetEntity: Wizacha\Marketplace\Division\Division
            joinColumn:
                name: division_code
                referencedColumnName: code
