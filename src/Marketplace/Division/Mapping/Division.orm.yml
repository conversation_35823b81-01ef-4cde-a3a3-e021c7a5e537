Wizacha\Marketplace\Division\Division:
    type: entity
    table: divisions
    gedmo:
        tree:
            type: nested
    id:
        code:
            type: string
            length: 25
            generator:
                strategy: NONE
    fields:
        alpha3:
            type: string
            length: 25
            nullable: true
        level:
            type: smallint
            nullable: false
            gedmo:
                - treeLevel
        lft:
            type: integer
            nullable: false
            gedmo:
                - treeLeft
        rgt:
            type: integer
            nullable: false
            gedmo:
                - treeRight
        isEnabled:
            type: boolean
            default: true
            nullable: false
        updatedAt:
            type: datetime
            nullable: true
        updatedBy:
            type: integer
            default: 1
            nullable: false
    manyToOne:
        root:
            targetEntity: Wizacha\Marketplace\Division\Division
            joinColumn:
                name: tree_root
                referencedColumnName: code
                onDelete: CASCADE
            gedmo:
                - treeRoot
        parent:
            targetEntity: Wizacha\Marketplace\Division\Division
            inversedBy: children
            joinColumn:
                name: parent_code
                referencedColumnName: code
                onDelete: CASCADE
            gedmo:
                - treeParent
    oneToMany:
        children:
            targetEntity: Wizacha\Marketplace\Division\Division
            mappedBy: parent
            orderBy:
                lft: ASC
        descriptions:
            targetEntity: Wizacha\Marketplace\Division\DivisionDescription
            mappedBy: division
            orphanRemoval: true
            cascade:
                - persist
                - remove
            orderBy:
                id: ASC
    indexes:
        idx_alpha3:
            columns: [ alpha3 ]
