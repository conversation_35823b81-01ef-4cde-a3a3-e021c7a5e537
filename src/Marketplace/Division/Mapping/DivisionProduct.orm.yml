Wizacha\Marketplace\Division\DivisionProduct:
    type: entity
    table: division_products
    id:
        id:
            type: integer
            generator:
                strategy: AUTO
    fields:
        divisionCode:
            type: string
            nullable: false
        productId:
            type: integer
            nullable: false
        createdAt:
            type: datetime
            nullable: false
        createdBy:
            type: integer
            nullable: false
    oneToOne:
        division:
            targetEntity: Wizacha\Marketplace\Division\Division
            joinColumn:
                name: division_code
                referencedColumnName: code
        product:
            targetEntity: Wizacha\Marketplace\PIM\Product\Product
            joinColumn:
                name: id
                referencedColumnName: product_id
