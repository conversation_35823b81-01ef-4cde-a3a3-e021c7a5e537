<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Division;

use Doctrine\DBAL\Connection;
use Wizacha\Marketplace\Division\Repository\DivisionRepository;

abstract class AbstractDivisionSettings
{
    /** @var int default userId if not provided */
    public const DEFAULT_USER_ID = 1;

    /** @var Connection */
    protected $connection;

    /** @var DivisionSet */
    protected $excludedDivisions;

    /** @var DivisionSet */
    protected $includedDivisions;

    /** @var string */
    protected $table;

    /** @var DivisionRepository */
    protected $divisionRepository;

    /** @var int */
    protected $userId = self::DEFAULT_USER_ID;

    public function __construct(
        Connection $connection,
        DivisionRepository $divisionRepository,
        DivisionSet $includedDivisions,
        DivisionSet $excludedDivisions
    ) {
        $this->connection = $connection;
        $this->divisionRepository = $divisionRepository;
        $this->setIncludedDivisions($includedDivisions);
        $this->setExcludedDivisions($excludedDivisions);
    }

    public function setExcludedDivisions(DivisionSet $excludedDivisions): self
    {
        $this->excludedDivisions = $excludedDivisions;

        return $this;
    }

    public function getExcludedDivisions(): DivisionSet
    {
        return $this->excludedDivisions;
    }

    public function setIncludedDivisions(DivisionSet $includedDivisions): self
    {
        $this->includedDivisions = $includedDivisions;

        return $this;
    }

    public function setUserId(int $userId): self
    {
        $this->userId = $userId;

        return $this;
    }

    public function getIncludedDivisions(): DivisionSet
    {
        return $this->includedDivisions;
    }

    /**
     * Get All codes matching included settings
     *
     * @return string[]
     */
    protected function getAllIncludedCodes(): array
    {
        return $this->divisionRepository->getAllCodesFromRootCodes(
            $this->includedDivisions->toArray()
        );
    }

    /**
     * Get All codes matching excluded settings
     *
     * @return string[]
     */
    protected function getAllExcludedCodes(): array
    {
        return $this->divisionRepository->getAllCodesFromRootCodes(
            $this->excludedDivisions->toArray()
        );
    }

    /**
     * Provides DivisionSettings hydration
     *
     * @return static
     */
    abstract public function hydrate();

    /**
     * Allows DivisionSettings persistancy
     *
     * @return static
     */
    abstract public function save();

    /**
     * Dispatch settings
     *
     * @return static
     */
    abstract public function dispatchSettings();

    /** @return mixed[] */
    public function toArray(): array
    {
        return [
            'included' => $this->includedDivisions instanceof DivisionSet
                ? $this->includedDivisions->toArray()
                : []
            ,
            'excluded' => $this->excludedDivisions instanceof DivisionSet
                ? $this->excludedDivisions->toArray()
                : []
            ,
        ];
    }

    /** Use to check id before actions */
    protected function isValidId(?int $id): bool
    {
        return \is_int($id) && $id > 0;
    }
}
