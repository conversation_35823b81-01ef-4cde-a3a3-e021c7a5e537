<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Division;

abstract class Collection
{
    /** @var mixed[] */
    protected $items = [];

    /** @param mixed[] $items */
    public function __construct(array $items = [])
    {
        $this->fromArray($items);
    }

    /** @param string[] $items */
    public function fromArray(array $items): self
    {
        foreach ($items as $item) {
            $this->add($item);
        }

        return $this;
    }

    /** @param mixed $item */
    public function add($item): Collection
    {
        if (false === \in_array($item, $this->items)) {
            $this->items[] = $item;
        }

        return $this;
    }

    public function remove($removableItem): Collection
    {
        $this->items = array_filter(
            $this->items,
            function ($item) use ($removableItem) {
                return $item !== $removableItem;
            }
        );

        return $this;
    }

    public function count(): int
    {
        return \count($this->items);
    }

    /** @return mixed[] */
    public function toArray(): array
    {
        return $this->items;
    }
}
