<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Division;

class DivisionSet extends Collection
{
    private const SEPARATOR = ',';

    public function serialize(): string
    {
        return \implode(static::SEPARATOR, $this->items);
    }

    public function unserialize(?string $serializedItems): self
    {
        if (\is_string($serializedItems)
            && \mb_strlen(\trim($serializedItems)) > 0
        ) {
            $this->items = \explode(static::SEPARATOR, \trim($serializedItems));
        }

        return $this;
    }
}
