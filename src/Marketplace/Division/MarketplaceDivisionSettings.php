<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Division;

class MarketplaceDivisionSettings extends AbstractDivisionSettings
{
    /** @var string */
    protected $table = 'cscart_settings_objects';

    private const EXCLUDED_DIVISIONS_COLUMN = 'divisions_excluded';
    private const INCLUDED_DIVISIONS_COLUMN = 'divisions_included';

    /** @inheritDoc */
    public function hydrate(): self
    {
        $queryBuilder = $this->connection->createQueryBuilder();

        $excludedDivisions = $queryBuilder
            ->select('value')
            ->from($this->table)
            ->where($queryBuilder->expr()->eq('name', ':name'))
            ->setParameter('name', self::EXCLUDED_DIVISIONS_COLUMN)
            ->execute()
            ->fetch()
        ;
        $this->excludedDivisions = (new DivisionSet())
            ->unserialize($excludedDivisions['value'])
        ;

        $includedDivisions = $queryBuilder
            ->select('value')
            ->from($this->table)
            ->where($queryBuilder->expr()->eq('name', ':name'))
            ->setParameter('name', self::INCLUDED_DIVISIONS_COLUMN)
            ->execute()
            ->fetch()
        ;
        $this->includedDivisions = (new DivisionSet())
            ->unserialize($includedDivisions['value'])
        ;

        return $this;
    }

    /** @inheritDoc */
    public function save(): self
    {
        $queryBuilder = $this->connection->createQueryBuilder();

        $queryBuilder
            ->update($this->table)
            ->set('value', ':value')
            ->where($queryBuilder->expr()->eq('name', ':name'))
            ->setParameter('name', self::EXCLUDED_DIVISIONS_COLUMN)
            ->setParameter('value', $this->excludedDivisions->serialize())
            ->execute()
        ;

        $queryBuilder
            ->update($this->table)
            ->set('value', ':value')
            ->where($queryBuilder->expr()->eq('name', ':name'))
            ->setParameter('name', self::INCLUDED_DIVISIONS_COLUMN)
            ->setParameter('value', $this->includedDivisions->serialize())
            ->execute()
        ;

        return $this;
    }

    /** @inheritDoc */
    public function dispatchSettings(): self
    {
        // Get codes to enable
        $codes = \array_diff(
            $this->getAllIncludedCodes(),
            $this->getAllExcludedCodes()
        );

        $this->divisionRepository->updateEnabledDivisions($codes, $this->userId);

        return $this;
    }
}
