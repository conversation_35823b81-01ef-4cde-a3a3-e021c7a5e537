<?php

/**
 *  <AUTHOR> DevTeam <<EMAIL>>
 *  @copyright   Copyright (c) Wizacha
 *  @license     Proprietary
 */

namespace Wizacha\Marketplace\Division;

use Wizacha\Marketplace\PIM\Product\Product;

class DivisionProduct
{
    /** @var int|null */
    private $id;

    /** @var string */
    private $divisionCode;

    /** @var int */
    private $productId;

    /** @var Product */
    private $product;

    /** @var \DateTime */
    private $createdAt;

    /** @var int */
    private $createdBy;

    /** @var Division */
    private $division;

    public function __construct(
        Division $division,
        int $productId,
        \DateTime $createdAt,
        int $createdBy = 1
    ) {
        $this->division = $division;
        $this->productId = $productId;
        $this->createdAt = $createdAt;
        $this->createdBy = $createdBy;
    }

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @param int $id
     *
     * @return DivisionProduct
     */
    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    /**
     * @return string
     */
    public function getDivisionCode(): string
    {
        return $this->divisionCode;
    }

    /**
     * @param string $divisionCode
     *
     * @return DivisionProduct
     */
    public function setDivisionCode(string $divisionCode): self
    {
        $this->divisionCode = $divisionCode;

        return $this;
    }

    /**
     * @return int
     */
    public function getProductId(): int
    {
        return $this->productId;
    }

    /**
     * @param int $productId
     *
     * @return DivisionProduct
     */
    public function setProductId(int $productId): self
    {
        $this->productId = $productId;

        return $this;
    }

    /**
     * @return Product
     */
    public function getProduct(): Product
    {
        return $this->product;
    }

    /**
     * @param Product $product
     *
     * @return DivisionProduct
     */
    public function setProduct(Product $product): self
    {
        $this->product = $product;

        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getCreatedAt(): \DateTime
    {
        return $this->createdAt;
    }

    /**
     * @param \DateTime $createdAt
     *
     * @return DivisionProduct
     */
    public function setCreatedAt(\DateTime $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * @return int
     */
    public function getCreatedBy(): int
    {
        return $this->createdBy;
    }

    /**
     * @param int $createdBy
     *
     * @return DivisionProduct
     */
    public function setCreatedBy(int $createdBy): self
    {
        $this->createdBy = $createdBy;

        return $this;
    }

    /**
     * @return Division
     */
    public function getDivision(): Division
    {
        return $this->division;
    }

    /**
     * @param Division $division
     *
     * @return DivisionProduct
     */
    public function setDivision(Division $division): self
    {
        $this->division = $division;

        return $this;
    }
}
