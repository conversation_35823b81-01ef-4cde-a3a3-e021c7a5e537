<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Division\Exception;

class InvalidProductDivisionCodeException extends InvalidDivisionCodeException
{
    /** @var int product id concerned by this exception */
    private $productId;

    public function __construct(string $message, int $productId)
    {
        parent::__construct($message);

        $this->productId = $productId;
    }

    public function getProductId(): int
    {
        return $this->productId;
    }
}
