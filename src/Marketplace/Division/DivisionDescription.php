<?php

/**
 *  <AUTHOR> DevTeam <<EMAIL>>
 *  @copyright   Copyright (c) Wizacha
 *  @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Division;

class DivisionDescription
{
    /** @var null|int */
    private $id;

    /** @var null|Division */
    private $division;

    /** @var string */
    private $language;

    /** @var string */
    private $description;

    public function __construct(
        Division $division,
        string $language,
        string $description
    ) {
        $this->division = $division;
        $this->language = $language;
        $this->description = $description;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getDivision(): ?Division
    {
        return $this->division;
    }

    public function setDivision(?Division $division): self
    {
        $this->division = $division;
        if ($division instanceof Division) {
            $division->addDescription($this);
        }

        return $this;
    }

    public function getLanguage(): string
    {
        return $this->language;
    }

    public function setLanguage(string $language): self
    {
        $this->language = $language;

        return $this;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): self
    {
        $this->description = $description;

        return $this;
    }
}
