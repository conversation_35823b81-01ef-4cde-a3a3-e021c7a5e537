<?php

/**
 *  <AUTHOR> DevTeam <<EMAIL>>
 *  @copyright   Copyright (c) Wizacha
 *  @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Division;

use Wizacha\Marketplace\User\UserType;

class DivisionBlacklist
{
    /** @var int|null */
    private $id;

    /** @var string */
    private $divisionCode;

    /** @var int */
    private $companyId;

    /** @var UserType */
    private $disabledBy;

    /** @var \DateTime */
    private $createdAt;

    /** @var int */
    private $createdBy;

    /** @var Division */
    private $division;

    public function __construct(
        Division $division,
        int $companyId,
        UserType $disabledBy,
        \DateTime $createdAt,
        int $createdBy
    ) {
        $this->division = $division;
        $this->companyId = $companyId;
        $this->disabledBy = $disabledBy;
        $this->createdAt = $createdAt;
        $this->createdBy = $createdBy;
    }

    public function __toString(): string
    {
        return $this->getDivisionCode();
    }

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @param int $id
     *
     * @return DivisionBlacklist
     */
    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    /**
     * @return string
     */
    public function getDivisionCode(): string
    {
        return $this->divisionCode ?? '';
    }

    /**
     * @param string $divisionCode
     *
     * @return self
     */
    public function setDivisionCode(string $divisionCode): self
    {
        $this->divisionCode = $divisionCode;

        return $this;
    }

    /**
     * @return int
     */
    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    /**
     * @param int $companyId
     *
     * @return DivisionBlacklist
     */
    public function setCompanyId(int $companyId): self
    {
        $this->companyId = $companyId;

        return $this;
    }

    /**
     * @return UserType
     */
    public function getDisabledBy(): UserType
    {
        return $this->disabledBy;
    }

    /**
     * @param UserType $disabledBy
     *
     * @return DivisionBlacklist
     */
    public function setDisabledBy(UserType $disabledBy): self
    {
        $this->disabledBy = $disabledBy;

        return $this;
    }

    /**
     * @return \DateTime
     */
    public function getCreatedAt(): \DateTime
    {
        return $this->createdAt;
    }

    /**
     * @param \DateTime $createdAt
     *
     * @return DivisionBlacklist
     */
    public function setCreatedAt(\DateTime $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /**
     * @return int
     */
    public function getCreatedBy(): int
    {
        return $this->createdBy;
    }

    /**
     * @param int $createdBy
     *
     * @return DivisionBlacklist
     */
    public function setCreatedBy(int $createdBy): self
    {
        $this->createdBy = $createdBy;

        return $this;
    }

    /**
     * @return Division
     */
    public function getDivision(): Division
    {
        return $this->division;
    }

    /**
     * @param Division $division
     *
     * @return DivisionBlacklist
     */
    public function setDivision(Division $division): self
    {
        $this->division = $division;

        return $this;
    }
}
