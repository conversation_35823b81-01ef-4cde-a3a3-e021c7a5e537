<?php

/**
 *  <AUTHOR> DevTeam <<EMAIL>>
 *  @copyright   Copyright (c) Wizacha
 *  @license     Proprietary
 */

namespace Wizacha\Marketplace\Division\Repository;

use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\QueryBuilder;
use Gedmo\Tree\Entity\Repository\NestedTreeRepository;
use Wizacha\Marketplace\Division\Division;
use Wizacha\Marketplace\Division\DivisionBlacklist;
use Wizacha\Marketplace\Division\DivisionDescription;
use Wizacha\Marketplace\Division\DivisionProduct;
use Wizacha\Marketplace\User\UserType;

class DivisionRepository extends NestedTreeRepository
{
    public function __construct(EntityManagerInterface $manager)
    {
        parent::__construct($manager, $manager->getClassMetadata(Division::class));

        // Tree array childrens key (default is __children)
        $this->setChildrenIndex('children');
    }

    /**
     * @param Division $division
     *
     * @return Division
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function saveDivision(Division $division): Division
    {
        $this->getEntityManager()->persist($division);
        $this->getEntityManager()->flush();

        return $division;
    }

    /**
     * @param DivisionDescription $divisionDescription
     *
     * @return DivisionDescription
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function saveDescription(DivisionDescription $divisionDescription): DivisionDescription
    {
        $this->getEntityManager()->persist($divisionDescription);
        $this->getEntityManager()->flush();

        return $divisionDescription;
    }

    public function getDoctrineEntityManager(): EntityManager
    {
        return $this->getEntityManager();
    }

    /** @param string[] $divisionsCode */
    public function saveDivisionsProducts(array $divisionsCode, int $productId, int $userId): self
    {
        if (\count($divisionsCode) > 0) {
            /**
             * Execute only one global INSERT as adviced by MySQL
             * @see https://dev.mysql.com/doc/refman/5.7/en/insert-optimization.html
             */
            $query = "
INSERT IGNORE INTO
    doctrine_division_products
    (division_code, product_id, created_at, created_by)
VALUES ";
            $parameters = [
                'product_id' => $productId,
                'created_at' => (new \DateTime())->format("Y-m-d H:i:s"),
                'user_id' => $userId,
            ];

            foreach ($divisionsCode as $i => $divisionCode) {
                $query .= "(:code_$i, :product_id, :created_at, :user_id),";
                $parameters['code_' . $i] = $divisionCode;
            }

            $this
                ->getEntityManager()
                ->getConnection()
                ->executeUpdate(mb_substr($query, 0, -1), $parameters)
            ;
        }

        return $this;
    }

    /** @param string[] $divisionsCode */
    public function saveDivisionBlacklists(array $divisionsCode, int $companyId, int $userId, string $area): self
    {
        if (\count($divisionsCode) > 0) {
            /**
             * Execute only one global INSERT as adviced by MySQL
             * @see https://dev.mysql.com/doc/refman/5.7/en/insert-optimization.html
             */
            $query = "
INSERT IGNORE INTO
    doctrine_division_blacklists
    (division_code, company_id, disabled_by, created_at, created_by)
VALUES ";
            $parameters = [
                'company_id' => $companyId,
                'disabled_by' => ($area === "A") ? UserType::ADMIN() : UserType::VENDOR(),
                'created_at' => (new \DateTime())->format("Y-m-d H:i:s"),
                'user_id' => $userId,
            ];

            foreach ($divisionsCode as $i => $divisionCode) {
                $query .= "(:code_$i, :company_id, :disabled_by, :created_at, :user_id),";
                $parameters['code_' . $i] = $divisionCode;
            }

            $this
                ->getEntityManager()
                ->getConnection()
                ->executeUpdate(mb_substr($query, 0, -1), $parameters)
            ;
        }

        return $this;
    }

    public function getDivisionsCodeFromProductId(int $productId): array
    {
        $qb = $this->getEntityManager()->createQueryBuilder();

        $divisions = $qb->select('dp.divisionCode')
            ->from(DivisionProduct::class, 'dp')
            ->where('dp.productId = :productId')
            ->setParameter('productId', $productId)
            ->orderBy('dp.divisionCode', 'ASC')
            ->getQuery()
            ->getResult();

        return array_column($divisions, 'divisionCode');
    }

    /**
     * @param string $code
     * @param string $language
     *
     * @return null|DivisionDescription
     */
    public function getDescription(string $code, string $language): ?DivisionDescription
    {
        return $this->getEntityManager()->getRepository(DivisionDescription::class)
            ->findOneBy([
                'divisionCode' => $code,
                'language'     => $language,
            ]);
    }

    /**
     * @param int $companyId
     * @return string[]
     */
    public function getDivisionBlacklistsCodes(int $companyId): array
    {
        $queryBuilder = $this
            ->getQueryBuilder();

        return \array_column(
            $queryBuilder
                ->select('db.divisionCode')
                ->from(DivisionBlacklist::class, 'db')
                ->where($queryBuilder->expr()->eq('db.companyId', ':company_id'))
                ->setParameter('company_id', $companyId)
                ->getQuery()
                ->getArrayResult(),
            'divisionCode'
        );
    }

    /** @return string[] */
    public function getDivisionBlacklistsDisabledBy(int $companyId, string $disabledBy): array
    {
        $queryBuilder = $this->getEntityManager()
            ->createQueryBuilder()
        ;
        return \array_column(
            $queryBuilder
                ->select('bl.divisionCode')
                ->from(DivisionBlacklist::class, 'bl')
                ->where($queryBuilder->expr()->eq('bl.companyId', ':companyId'))
                ->andWhere($queryBuilder->expr()->eq('bl.disabledBy', ':disabledBy'))
                ->setParameter('companyId', $companyId)
                ->setParameter('disabledBy', $disabledBy)
                ->getQuery()
                ->getArrayResult(),
            'divisionCode'
        );
    }

    /**
     * Get all divisions for a product and a company
     *
     * @param null|int $companyId
     * @param null|int $productId
     * @param string   $locale
     * @param bool     $isEnabled
     * @param bool     $loadLevel0
     * @param bool     $snakeCase
     * @param null|string $rootCode
     *
     * @return array
     */
    public function getDivisions(
        ?int $companyId,
        ?int $productId,
        string $locale,
        bool $isEnabled = null,
        $loadLevel0 = false,
        $snakeCase = false,
        $rootCode = null
    ): array {
        $queryBuilder = $this->getEntityManager()->createQueryBuilder();

        $queryBuilder
            ->select('d.code')
            ->addSelect('p.code as parentCode')
            ->addSelect('d.level')
            ->addSelect('d.isEnabled')
            ->addSelect('dd.description')
            ->from(Division::class, 'd')
            ->leftJoin('d.parent', 'p')
            ->leftJoin('d.descriptions', 'dd', Join::WITH, 'dd.language = :language')
            ->orderBy('d.lft', 'ASC')
            ->addOrderBy('dd.description', 'ASC')
            ->setParameter('language', $locale)
        ;

        if (\is_bool($isEnabled)) {
            $queryBuilder
                ->andWhere('d.isEnabled = :isEnabled')
                ->setParameter('isEnabled', $isEnabled)
            ;
        }

        if (\is_int($companyId)) {
            $queryBuilder
                ->addSelect('db.companyId')
                ->addSelect('db.disabledBy')
                ->leftJoin(DivisionBlacklist::class, 'db', Join::WITH, 'db.divisionCode = d.code AND db.companyId = :companyId')
                ->setParameter('companyId', $companyId)
            ;
        }

        if (\is_int($productId)) {
            $queryBuilder
                ->addSelect('dp.productId')
                ->leftJoin(DivisionProduct::class, 'dp', Join::WITH, 'dp.divisionCode = d.code AND dp.productId = :productId')
                ->setParameter('productId', $productId)
            ;
        }

        if (\is_string($rootCode)) {
            $queryBuilder
                ->innerJoin(Division::class, 'root', Join::WITH, 'root.code = :rootCode')
                ->andWhere('d.lft >= root.lft AND d.lft <= root.rgt')
                ->setParameter('rootCode', $rootCode)
            ;
        }

        return $this->toObject(
            $queryBuilder->getQuery()->getResult(),
            $snakeCase
        );
    }

    public function getDivisionsParentCode(array $_codes): array
    {
        $qb = $this->getEntityManager()->createQueryBuilder();

        $qb->select('d')
            ->from(Division::class, 'd');

        foreach ($_codes as $key => $code) {
            $qb->orWhere("d.code = :code_{$key}")
                ->setParameter("code_{$key}", $code);
        }


        $codes = [];
        foreach ($qb->getQuery()->getResult() as $division) { /** @var Division $division */
            $codes[$division->getCode()] = $division->getCode();

            if (!\is_null($division->getParent())) {
                $this->runGetParentsCodes($codes, $division->getParent());
            }
        }

        return array_values($codes);
    }

    public function deleteDivisionsProducts(int $productId): void
    {
        $queryBuilder = $this
            ->getEntityManager()
            ->createQueryBuilder();

        $queryBuilder
            ->delete(DivisionProduct::class, 'dp')
            ->where($queryBuilder->expr()->eq('dp.productId', ':product_id'))
            ->setParameters(
                [
                    'product_id' => $productId,
                ]
            )
            ->getQuery()
            ->execute()
        ;
    }

    public function deleteDivisionsBlacklists(int $companyId): void
    {
        $queryBuilder = $this
            ->getEntityManager()
            ->createQueryBuilder();

        $queryBuilder
            ->delete(DivisionBlacklist::class, 'db')
            ->where($queryBuilder->expr()->eq('db.companyId', ':company_id'))
            ->setParameters(
                [
                    'company_id' => $companyId,
                ]
            )
            ->getQuery()
            ->execute()
        ;
    }

    /** @param string[] $divisionsCode */
    public function deleteDivisionBlacklist(array $divisionsCode, int $companyId): void
    {
        if (\count($divisionsCode) > 0) {
            $queryBuilder = $this
                ->getEntityManager()
                ->createQueryBuilder();

            $queryBuilder
                ->delete(DivisionBlacklist::class, 'db')
                ->where($queryBuilder->expr()->in('db.divisionCode', ':divisions_code'))
                ->andWhere($queryBuilder->expr()->eq('db.companyId', ':company_id'))
                ->setParameters(
                    [
                        'divisions_code' => $divisionsCode,
                        'company_id' => $companyId,
                    ]
                )
                ->getQuery()
                ->execute()
            ;
        }
    }

    /**
     * When a blacklist is updated, some products might be enabled and now they must be disabled
     *
     * @param string[] $divisionsCode
     * @param int[] $productsId
     */
    public function deleteProductsFromBlacklist(array $divisionsCode, array $productsId): void
    {
        if (\count($divisionsCode) > 0 && \count($productsId) > 0) {
            $queryBuilder = $this
                ->getEntityManager()
                ->createQueryBuilder();

            $queryBuilder
                ->delete(DivisionProduct::class, 'dp')
                ->where($queryBuilder->expr()->in('dp.divisionCode', ':divisions_code'))
                ->andWhere($queryBuilder->expr()->in('dp.productId', ':products_id'))
                ->setParameters(
                    [
                        'divisions_code' => $divisionsCode,
                        'products_id' => $productsId,
                    ]
                )
                ->getQuery()
                ->execute()
            ;
        }
    }

    /**
     * When an admin MP update the list, some products might be enabled and now they must be disabled
     *
     * @param array $divisions
     */
    public function deleteProductsFromAdmin(array $divisions): void
    {
        $queryBuilder = $this->getEntityManager()->createQueryBuilder();

        foreach ($divisions as $division) {
            $qb = clone $queryBuilder;

            $qb->delete(DivisionProduct::class, 'divisionProduct')
                ->where("divisionProduct.divisionCode = :code")
                ->setParameter('code', $division);

            $qb->getQuery()->execute();
        }
    }

    public function getAllIds(bool $enabled): array
    {
        return array_column(
            $this
                ->getEntityManager()
                ->createQueryBuilder()
                ->select("division.code")
                ->from(Division::class, "division")
                ->where("division.isEnabled = ?0")
                ->setParameter(0, $enabled ? 1 : 0)
                ->getQuery()
                ->getResult(),
            'code'
        );
    }

    /** @param string[] $codes */
    public function updateEnabledDivisions(array $codes, int $userId): void
    {
        $this
            ->getDoctrineEntityManager()
            ->transactional(
                function ($em) use ($codes, $userId): void {
                    $this
                        // Reset Divisions
                        ->resetEnabledDivisions($em, $codes)
                        // Update divisions
                        ->enableDivisions($em, $codes, $userId)
                    ;
                }
            )
        ;
    }

    /**
     * @param string[] $codes
     * @return string[][]
     */
    public function getDivisionsNestedSetRanges(array $codes): array
    {
        if (\count($codes) === 0) {
            return [];
        }

        $queryBuilder = $this
            ->getQueryBuilder()
        ;
        return $queryBuilder
            ->select('d.lft')
            ->addSelect('d.rgt')
            ->from(Division::class, 'd')
            ->where($queryBuilder->expr()->in('d.code', ':codes'))
            ->setParameter('codes', $codes)
            ->getQuery()
            ->getArrayResult()
        ;
    }

    /**
     * Get codes to send in DivisionBlacklist from company included and exluded codes lists.
     *
     * @param string[] $included
     * @param string[] $excluded
     * @return string[]
     */
    public function getCodesToBlackList(array $included, array $excluded): array
    {
        $queryBuilder = $this
            ->getQueryBuilder()
            ->select('d.code')
            ->from(Division::class, 'd')
        ;

        $orX = $queryBuilder->expr()->orX();

        if (\count($included) > 0) {
            $orX->add(
                $queryBuilder->expr()->orX(
                    $queryBuilder->expr()->eq('d.isEnabled', ':isEnabled'),
                    $queryBuilder->expr()->notIn('d.code', ':included')
                )
            );
            $queryBuilder
                ->setParameter('isEnabled', false)
                ->setParameter('included', $included, Connection::PARAM_STR_ARRAY)
            ;
        }

        if (\count($excluded) > 0) {
            $orX->add(
                $queryBuilder->expr()->in('d.code', ':excluded')
            );
            $queryBuilder
                ->setParameter('excluded', $excluded, Connection::PARAM_STR_ARRAY)
            ;
        }

        if ($orX->count()) {
            $queryBuilder->where($orX);
        }

        return \array_column(
            $queryBuilder
                ->getQuery()
                ->getArrayResult(),
            'code'
        );
    }

    /** Get the Division QueryBuilder whith basic params */
    public function getDivisionsQueryBuilder(): QueryBuilder
    {
        return $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->from(Division::class, 'd')
            ->orderBy('d.lft', 'ASC')
        ;
    }

    /**
     * Return all codes included in the roots code list
     *
     * @param string[] $roots
     * @return string[]
     */
    public function getAllCodesFromRootCodes(array $roots): array
    {
        return $this->getDivisionCodesWithinNestedSetRange(
            $this->getDivisionsNestedSetRanges($roots)
        );
    }

    /** @param mixed[] $divisionsNestedSetRanges */
    public function getDivisionCodesWithinNestedSetRange(array $divisionsNestedSetRanges): array
    {
        if (\count($divisionsNestedSetRanges) < 1) {
            return [];
        }

        $queryBuilder = $this
            ->getQueryBuilder()
        ;
        $orX = $queryBuilder->expr()->orX();

        foreach ($divisionsNestedSetRanges as $divisionsNestedSetRange) {
            $orX->add(
                $queryBuilder->expr()->andX(
                    $queryBuilder->expr()->gte('d.lft', $divisionsNestedSetRange['lft']),
                    $queryBuilder->expr()->lte('d.lft', $divisionsNestedSetRange['rgt'])
                )
            );
        }

        return \array_column(
            $queryBuilder
                ->select('d.code')
                ->from(Division::class, 'd')
                ->where($orX)
                ->getQuery()
                ->getArrayResult(),
            'code'
        );
    }

    /** @param string[] $codes */
    public function countValidCodes(array $codes): int
    {
        $queryBuilder = $this->getEntityManager()->createQueryBuilder();
        $queryCount = $queryBuilder
            ->select('COUNT(d.code)')
            ->from(Division::class, 'd')
            ->where($queryBuilder->expr()->in('d.code', ':codes'))
            ->setParameter('codes', $codes, Connection::PARAM_STR_ARRAY)
            ->getQuery()
            ->getSingleScalarResult()
        ;

        return \intval($queryCount);
    }

    /**
     * @param array $divisions
     *
     * @return object[]
     */
    private function toObject(array $divisions, $snakeCase): array
    {
        $object = [];
        foreach ($divisions as $division) {
            $o = new \stdClass();
            foreach ($division as $key => $d) {
                if ($key === "description") {
                    $key = "name";
                }

                if ($snakeCase) {
                    $key = $this->toSnakeCase($key);
                }

                $o->{$key} = $d;
            }
            $object[] = $o;
        }

        return $object;
    }

    private function toSnakeCase(string $input)
    {
        preg_match_all('!([A-Z][A-Z0-9]*(?=$|[A-Z][a-z0-9])|[A-Za-z][a-z0-9]+)!', $input, $matches);
        $ret = $matches[0];
        foreach ($ret as &$match) {
            $match = $match == strtoupper($match) ? strtolower($match) : lcfirst($match);
        }

        return implode('_', $ret);
    }

    private function runGetParentsCodes(array &$codes, Division $division)
    {
        $codes[$division->getCode()] = $division->getCode();

        if (!\is_null($division->getParent())) {
            $this->runGetParentsCodes($codes, $division->getParent());
        }
    }

    /**
     * Disable all divisions of the marketplace
     *
     * @param string[] $immutableCodes
     */
    private function resetEnabledDivisions(EntityManagerInterface $em, array $immutableCodes = [], $userId = 1): self
    {
        $queryBuilder = $em->createQueryBuilder();

        $queryBuilder
            ->update(Division::class, 'd')
            ->set('d.isEnabled', ':isEnabled')
            ->set('d.updatedAt', ':updatedAt')
            ->set('d.updatedBy', ':updatedBy')
            ->setParameters(
                [
                    'isEnabled' => false,
                    'updatedAt' => (new \DateTime())->format("Y-m-d H:i:s"),
                    'updatedBy' => $userId,
                ]
            )
        ;

        if (\count($immutableCodes) > 0) {
            $queryBuilder
                ->where($queryBuilder->expr()->notIn('d.code', ':codes'))
                ->setParameter('codes', $immutableCodes, Connection::PARAM_STR_ARRAY)
            ;
        }

        $queryBuilder
            ->getQuery()
            ->execute()
        ;

        return $this;
    }

    /**
     * Enable divisions from a code list
     *
     * @param string[] $codes
     */
    private function enableDivisions(EntityManagerInterface $em, array $codes, int $userId): self
    {
        $queryBuilder = $em->createQueryBuilder();

        $queryBuilder
            ->update(Division::class, 'd')
            ->set('d.isEnabled', ':isEnabled')
            ->set('d.updatedAt', ':updatedAt')
            ->set('d.updatedBy', ':updatedBy')
            ->where($queryBuilder->expr()->in('d.code', ':codes'))
            ->setParameters(
                [
                    'isEnabled' => true,
                    'updatedAt' => (new \DateTime())->format("Y-m-d H:i:s"),
                    'updatedBy' => $userId,
                ]
            )
            ->setParameter('codes', $codes, Connection::PARAM_STR_ARRAY)
            ->getQuery()
            ->execute()
        ;

        return $this;
    }

    /**
     * @param string[] $arrayCodes
     *
     * @return Division[]
     */
    public function getDivisionsByCodes(array $arrayCodes): array
    {
        $queryBuilder =  $this
            ->getEntityManager()
            ->createQueryBuilder();

        return $queryBuilder
            ->select('division')
            ->from(Division::class, 'division')
            ->where($queryBuilder->expr()->in('division.code', ':arrayCodes'))
            ->setParameters(
                [
                    'arrayCodes' => $arrayCodes
                ]
            )
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * Return code list from a query builder result whith code column
     * ie: [['code' => 'AA'], ['code' => 'BB'], ]
     *     will return: ['AA', 'BB']
     *
     * @param string[][] $result
     * @return string[]
     */
    protected function extractCodeListFromResult(array $result, string $columnName = null): array
    {
        return \array_column($result, $columnName ?? 'code');
    }
}
