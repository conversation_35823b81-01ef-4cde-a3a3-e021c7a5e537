<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Traits;

trait MonitoringTrait
{
    public function freeMemoryRatio(): float
    {
        preg_match(
            '/(?<value>\d+)(?<prefix>\w?)/',
            ini_get('memory_limit'),
            $matches
        );

        $exponant
            = [
                'K' => 1,
                'M' => 2,
                'G' => 3,
            ][$matches['prefix']]
        ;

        $multiplier = $exponant
            ? 1024 ** $exponant
            : 1
        ;

        return
            memory_get_usage(true)
            / (
                (int) $matches['value'] * $multiplier
            )
        ;
    }
}
