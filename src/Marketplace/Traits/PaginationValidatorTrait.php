<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Traits;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Validator\Constraints;
use Symfony\Component\Validator\ConstraintViolationListInterface;

trait PaginationValidatorTrait
{
    /** @var int */
    protected static $defaultOffset = 0;

    /** @var int */
    protected static $defaultLimit = 10;

    /** @var int */
    protected static $defaultLimitAddressBook = 20;

    /** @var int */
    protected static $maxLimit = 100;

    /** @var int */
    protected static $minLimit = 0;

    protected function paginationValidator(Request $request): ConstraintViolationListInterface
    {
        $offset = 'offset';
        $limit = 'limit';

        return $this->validator->validate($request->query->all(), new Constraints\Collection([
            'fields' => [
                $offset => [
                    new Constraints\Type(['type' => 'numeric', 'message' => "'${offset}' must be a number"]),
                    new Constraints\NotBlank(['message' => "'${offset}' must not be blank"]),
                ],
                $limit => [
                    new Constraints\Type(['type' => 'numeric', 'message' => "'${limit}' must be a number"]),
                    new Constraints\NotBlank(['message' => "'${limit}' must not be blank"]),
                    new Constraints\Range(['min' => static::getMinLimit(), 'max' => static::getMaxLimit()]),
                ],
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true,
        ]));
    }

    /** @return mixed[] */
    public function paginationResolver(Request $request): array
    {
        return [
            'offset' => $request->query->getInt('offset', static::getDefaultOffset()),
            'limit' => $request->query->getInt('limit', static::getDefaultLimit()),
        ];
    }

    /** @return mixed[] */
    public function paginationAddressBook(Request $request): array
    {
        return [
            'offset' => $request->query->getInt('offset', static::getDefaultOffset()),
            'limit' => $request->query->getInt('limit', static::getLimitAddressBook()),
        ];
    }

    /** @return mixed[] */
    public function UserPaginationResolver(Request $request): array
    {
        return [
            'page' => $request->query->getInt('page', static::getDefaultOffset()),
            'elements' => $request->query->getInt('elements', static::getDefaultLimit()),
        ];
    }

    public static function getMaxLimit(): int
    {
        return static::$maxLimit;
    }

    public static function getMinLimit(): int
    {
        return static::$minLimit;
    }

    public static function getDefaultLimit(): int
    {
        return static::$defaultLimit;
    }

    public static function getLimitAddressBook(): int
    {
        return static::$defaultLimitAddressBook;
    }

    public static function getDefaultOffset(): int
    {
        return static::$defaultOffset;
    }
}
