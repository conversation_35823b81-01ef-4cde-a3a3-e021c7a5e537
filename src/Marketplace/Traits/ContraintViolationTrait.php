<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright Copyright (c) Wizaplace
 * @license   Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Traits;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolationInterface;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;

trait ContraintViolationTrait
{
    protected function badRequest(ConstraintViolationListInterface $violations): JsonResponse
    {
        return new JsonResponse([
            'errors' => array_map(function (ConstraintViolationInterface $violation): array {
                return [
                    'message' => $violation->getMessage(),
                ];
            }, iterator_to_array($violations)),
        ], Response::HTTP_BAD_REQUEST);
    }

    protected function invalidFields(ConstraintViolationListInterface $violations): BadRequestJsonResponse
    {
        $fields = \array_map(
            function (ConstraintViolationInterface $violation): array {
                return [
                    'field' => $violation->getPropertyPath(),
                    'message' => $violation->getMessage(),
                ];
            },
            \iterator_to_array($violations)
        );

        return BadRequestJsonResponse::invalidFields($fields);
    }
}
