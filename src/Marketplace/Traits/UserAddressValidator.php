<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Traits;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Validator\Constraints;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Wizacha\Marketplace\Subscription\SubscriptionStatus;
use Wizacha\Marketplace\User\AddressStatus;
use Wizacha\Marketplace\User\UserTitle;

trait UserAddressValidator
{
    protected function UserAddressFilterValidator(Request $request): ConstraintViolationListInterface
    {
        $fromUserAddress = 'fromUserAddress';

        $status = AddressStatus::toArray();
        $status2 = SubscriptionStatus::toArray();

        $t = $this->validator->validate(
            $request->request->all(),
            new Constraints\Collection([
                'fields' => [
                    $fromUserAddress => [
                        new Constraints\Type(['type' => 'string', 'message' => "'${$fromUserAddress}' must be a string"]),
                        new Constraints\NotBlank(['message' => "'${$fromUserAddress}' must not be blank"]),
                        new Constraints\Choice(['choices' => ["billing" => "billing","shipping" => "shipping"]]),
                    ],
                    'title' => [
                        new Constraints\Type(['type' => 'string', 'message' => "title must be a string"]),
                        new Constraints\Choice(['choices' => UserTitle::toArray(), 'message' => 'title is not part of the enum [' . \implode(',', UserTitle::values()) . '].']),
                    ]
                ],
                'allowExtraFields' => true,
                'allowMissingFields' => true,
            ])
        );

        return $t;
    }

    protected function userAddressBookCreatedFilterValidator(Request $request): ConstraintViolationListInterface
    {
        return $this->validator->validate($request->request->all(), new Constraints\Collection([
            'fields' => [
                'title' => [
                    new Constraints\Type(['type' => 'string', 'message' => "title must be a string"]),
                    new Constraints\Choice(['choices' => UserTitle::toArray(), 'message' => 'title is not part of the enum [' . \implode(',', UserTitle::values()) . '].']),
                ],
                'label' => [
                    new Constraints\Type(['type' => 'string', 'message' => "label must be a string"]),
                    new Constraints\Length(['min' => 0, 'max' => 128, 'maxMessage' => 'label is limited to 128 characters.']),
                ],
                'comment' => [
                    new Constraints\Type(['type' => 'string', 'message' => "label must be a string"]),
                    new Constraints\Length(['min' => 0, 'max' => 255, 'maxMessage' => 'comment is limited to 255 characters.']),
                ],
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true,
        ]));
    }
}
