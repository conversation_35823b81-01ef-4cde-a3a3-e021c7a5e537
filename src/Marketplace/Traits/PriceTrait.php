<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Traits;

trait PriceTrait
{
    /**
     * @param null|string|int|float $price
     */
    protected function isPriceValid($price, ?bool $showZeroPriceProducts = null): bool
    {
        if (\is_null($price) === true) {
            return false;
        }

        if ($showZeroPriceProducts === null) {
            $showZeroPriceProducts = container()->getParameter('feature.catalog.show_zero_price_products');
        }

        return $showZeroPriceProducts === true ? $price >= 0 : $price > 0;
    }
}
