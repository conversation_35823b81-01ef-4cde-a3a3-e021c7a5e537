<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Traits;

use Wizacha\Marketplace\User\UserType;

trait VariablesTypeValidator
{
    /** @param mixed[]|null $data */
    public function checkArrayOfUserType(?array $data): void
    {
        if ($data !== null && \count($data) > 0) {
            foreach ($data as $value) {
                if (UserType::isValid($value) === false) {
                    throw new \UnexpectedValueException(
                        'Type ' . $value . ' is not part of the enum [ ' . \implode(',', UserType::values()) . ' ].'
                    );
                }
            }
        }
    }

    public function checkIntType(?string $data): void
    {
        if ($data !== null && \filter_var($data, FILTER_VALIDATE_INT) === false) {
            throw new \UnexpectedValueException('Expected a integer, got [' . $data . ']');
        }
    }
}
