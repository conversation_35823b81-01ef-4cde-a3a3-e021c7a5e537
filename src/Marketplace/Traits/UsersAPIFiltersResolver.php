<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Traits;

use Symfony\Component\OptionsResolver\OptionsResolver;
use Wizacha\Marketplace\User\UserType;

trait UsersAPIFiltersResolver
{
    /**
     * @param mixed[] $parameters
     *
     * @return mixed[]
     */
    public function resolveOptions(array $parameters): array
    {
        return (new OptionsResolver())
            ->setDefaults([
                'name' => null,
                'email' => null,
                'phone' => null,
                'external' => null,
                'loyalty' => null,
                'type' => null,
                'companyId' => null,
                'isProfessional' => null,
                'elements' => null,
                'page' => null,
                'extra' => null,
                'extraStartWith' => null
            ])
            ->setAllowedTypes('name', ['null', 'string'])
            ->setAllowedTypes('email', ['null','string'])
            ->setAllowedTypes('phone', ['null','string'])
            ->setAllowedTypes('external', ['null','string'])
            ->setAllowedTypes('loyalty', ['null','string'])
            ->setAllowedTypes('companyId', ['null', 'string'])
            ->setAllowedTypes('isProfessional', ['null','string'])
            ->setAllowedTypes('elements', ['null','string'])
            ->setAllowedTypes('page', ['null','string'])
            ->setAllowedTypes('type', ['null','string[]'])
            ->setAllowedTypes('extra', ['null','array'])
            ->setAllowedTypes('extraStartWith', ['null','array'])
            ->setAllowedValues('isProfessional', [null, '0','1'])
            ->setNormalizer('type', function (OptionsResolver $options, $array) {
                if ($array !== null && \count($array) > 0) {
                    foreach ($array as $value) {
                        if (UserType::isValid($value) === false) {
                            throw new \InvalidArgumentException(
                                'Value ' . $value . ' is not part of the enum [ ' . \implode(',', UserType::values()) . ' ].'
                            );
                        } else {
                            return $array;
                        }
                    }
                }
            })
            ->resolve($parameters)
            ;
    }
}
