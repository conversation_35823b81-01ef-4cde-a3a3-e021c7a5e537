<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Traits;

use Wizacha\AppBundle\Controller\DomainUserTrait;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Exception\Unauthorized;
use Wizacha\Marketplace\User\User;
use Wizacha\Marketplace\User\UserType;
use Wizacha\Marketplace\User\AddressBookService;

trait AssertCanAccessUserAccountTrait
{
    use DomainUserTrait;

    private AddressBookService $addressBookService;

    /** @required  */
    public function setAddressBookService(AddressBookService $addressBookService): void
    {
        $this->addressBookService = $addressBookService;
    }

    public function assertCanAccessUserAccount(int $targetUserId): User
    {
        $authorizationChecker = $this->container->get('security.authorization_checker');
        $loggedUser = $this->getDomainUser($this->getUser()->getId());
        $targetUser = $this->getDomainUser($targetUserId);

        // An admin can access all users
        if ($authorizationChecker->isGranted('ROLE_ADMIN')) {
            return $targetUser;
        }

        // A non-admin can only access its own account
        if ($loggedUser->getUserId() === $targetUserId) {
            return $targetUser;
        }

        if ($loggedUser->belongsToAnOrganisation() === true
            && $loggedUser->getOrganisation()->isAdministrator($loggedUser) === true
            && $targetUser->belongsToOrganisation($loggedUser->getOrganisation()) === true
        ) {
            return $targetUser;
        }

        // 404 instead of 403 to avoid leaking information
        throw NotFound::fromId('User', $targetUserId);
    }

    public function assertCanAccessToEditUserAccount(int $targetUserId): User
    {
        $authorizationChecker = $this->container->get('security.authorization_checker');
        $loggedUser = $this->getDomainUser($this->getUser()->getId());
        $targetUser = $this->getDomainUser($targetUserId);

        // An admin support can access and edit all users
        if ($loggedUser->isMarketplaceSupportAdministrator() === true) {
            return $targetUser;
        }

        // An admin can't edit another  admin
        if ($authorizationChecker->isGranted('ROLE_ADMIN') === true
            && $targetUser->isMarketplaceAdministrator() === true
            && $loggedUser->getUserId() !== $targetUserId
        ) {
            throw  new Forbidden(__('edit_admin_forbidden'));
        }

        // An admin can access all users
        if ($authorizationChecker->isGranted('ROLE_ADMIN') === true) {
            return $targetUser;
        }

        // A non-admin can only access its own account
        if ($loggedUser->getUserId() === $targetUserId) {
            return $targetUser;
        }

        if ($loggedUser->belongsToAnOrganisation() === true
            && $loggedUser->getOrganisation()->isAdministrator($loggedUser) === true
            && $targetUser->belongsToOrganisation($loggedUser->getOrganisation()) === true
        ) {
            return $targetUser;
        }

        // 404 instead of 403 to avoid leaking information
        throw NotFound::fromId('User', $targetUserId);
    }

    public function assertCanAccessUserAddressBook(int $targetUserId): User
    {
        $authorizationChecker = $this->container->get('security.authorization_checker');
        $loggedUser = $this->getDomainUser($this->getUser()->getId());
        $targetUser = $this->getDomainUser($targetUserId);

        // An admin can access all users
        if ($authorizationChecker->isGranted('ROLE_ADMIN') === true) {
            return $targetUser;
        }

        // A non-admin can only access its own account
        if ($loggedUser->getUserId() === $targetUserId) {
            return $targetUser;
        }

        // 403 to avoid leaking information
        throw Unauthorized::fromId('user', $targetUserId);
    }

    public function assertUserHaveAddressBook(int $targetUserId, string $addressId): User
    {
        $addressBook = $this->addressBookService->get($addressId);
        $targetUser = $this->getDomainUser($targetUserId);

        if ($addressBook->getUser()->getUserId() === $targetUser->getUserId()) {
            return $targetUser;
        }

        throw new NotFound("AddressId does not belong to user.");
    }

    public function assertUserHaveBasket(int $targetUserId, string $basketId): User
    {
        $targetUser = $this->getDomainUser($targetUserId);
        if ($targetUser->getBasketId() === $basketId) {
            return $targetUser;
        }

        throw new NotFound("Basket does not belong to user.");
    }

    public function assertCanAccessToAffiliateUser(string $email, int $companyId): User
    {
        $loggedUser = $this->assertUserIsAuthorizedToAffiliationUser();

        $this->companyService->get($companyId);

        if ($loggedUser->isMarketplaceAdministrator() === false
            && $loggedUser->getCompanyId() !== $companyId
        ) {
            throw new Forbidden('You cannot affiliate user to another company.');
        }

        $user = $this->assertEmailExists($email);

        if ($user->getUserType() === UserType::ADMIN()->getValue()) {
            throw new Forbidden('You cannot affiliate an admin to company.');
        }

        if ($loggedUser->isMarketplaceAdministrator() === false
            && $user->getUserType() === UserType::VENDOR()->getValue()
            && $user->getCompanyId() !== null
            && $user->getCompanyId() !== $loggedUser->getCompanyId()
        ) {
            throw new Forbidden('You cannot affiliate another vendor to your company.');
        }

        return $user;
    }

    public function assertCanAccessToDisaffiliateUser(string $email): User
    {
        $loggedUser = $this->assertUserIsAuthorizedToAffiliationUser();

        $user = $this->assertEmailExists($email);

        if ($user->getEmail() === $loggedUser->getEmail()) {
            throw new Forbidden('You cannot self disaffiliate from company.');
        }

        if (\in_array($user->getUserType(), [UserType::ADMIN()->getValue(), UserType::CLIENT()->getValue()]) === true) {
            throw new Forbidden('You cannot disaffiliate an admin or customer from company.');
        }

        if ($loggedUser->isMarketplaceAdministrator() === false
            && $user->getCompanyId() !== $loggedUser->getCompanyId()
            && $user->getCompanyId() !== null
        ) {
            throw new Forbidden('You cannot disaffiliate user affiliated to another company.');
        }

        if ($user->getCompanyId() !== null) {
            // check if user is the only active admin fo company
            $admins = $this->companyService->getActiveCompanyAdmins($user->getCompanyId());
            if (\in_array($user->getUserId(), $admins) && \count($admins) === 1) {
                throw new Forbidden('You cannot disaffiliate user from company. You must have more than one active admin.');
            }
        }

        return $user;
    }

    private function assertUserIsAuthorizedToAffiliationUser(): User
    {
        if ($this->getUser() === null) {
            throw new Unauthorized();
        }

        $loggedUser = $this->getDomainUser($this->getUser()->getId());

        if ($loggedUser->isMarketplaceAdministrator() === false
            && ($loggedUser->getUserType() !== UserType::VENDOR()->getValue() || $loggedUser->isEnabled() === false)
        ) {
            throw new Forbidden('Only admin or active vendor can affiliate/disaffiliate user.');
        }

        return $loggedUser;
    }

    private function assertEmailExists(string $email): User
    {
        $user = $this->userService->findOneByEmail($email);
        if ($user === null) {
            throw new NotFound('User not found');
        }

        return $user;
    }

    public function assertLoggedUserIsTarget(int $targetUserId): bool
    {
        $loggedUser = $this->getDomainUser($this->getUser()->getId());

        return $loggedUser->getUserId() === $targetUserId;
    }
}
