<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Traits;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Validator\Constraints;
use Symfony\Component\Validator\ConstraintViolationListInterface;
use Wizacha\Marketplace\Subscription\SubscriptionStatus;

trait SubscriptionFilterValidatorTrait
{
    protected function subscriptionFilterValidator(Request $request): ConstraintViolationListInterface
    {
        $status = 'status';
        $companyId = 'companyId';
        $userId = 'userId';
        $productId = 'productId';
        $commitmentEndBefore = 'commitmentEndBefore';
        $commitmentEndAfter = 'commitmentEndAfter';
        $isAutorenew = 'isAutorenew';

        return $this->validator->validate($request->query->all(), new Constraints\Collection([
            'fields' => [
                $status => [
                    new Constraints\Type(['type' => 'string', 'message' => "'${status}' must be a string"]),
                    new Constraints\NotBlank(['message' => "'${status}' must not be blank"]),
                    new Constraints\Choice(['choices' => SubscriptionStatus::toArray()]),
                ],
                $companyId => [
                    new Constraints\Type(['type' => 'numeric', 'message' => "'${companyId}' must be a int"]),
                    new Constraints\NotBlank(['message' => "'${companyId}' must not be blank"]),
                ],
                $userId => [
                    new Constraints\Type(['type' => 'numeric', 'message' => "'${userId}' must be a int"]),
                    new Constraints\NotBlank(['message' => "'${userId}' must not be blank"]),
                ],
                $productId => [
                    new Constraints\Type(['type' => 'numeric', 'message' => "'${productId}' must be a int"]),
                    new Constraints\NotBlank(['message' => "'${productId}' must not be blank"]),
                ],
                $commitmentEndBefore => [
                    new Constraints\Date(['message' => "'${commitmentEndBefore}' must be a string"]),
                    new Constraints\NotBlank(['message' => "'${commitmentEndBefore}' must not be blank"]),
                ],
                $commitmentEndAfter => [
                    new Constraints\Date(['message' => "'${commitmentEndAfter}' must be a string"]),
                    new Constraints\NotBlank(['message' => "'${commitmentEndAfter}' must not be blank"]),
                ],
                $isAutorenew => [
                    new Constraints\Type(['type' => 'string', 'message' => "'${isAutorenew}' must be a boolean"]),
                    new Constraints\NotBlank(['message' => "'${isAutorenew}' must not be blank"]),
                    new Constraints\Choice(['choices' => ['true', '1', 'false', '0'], 'message' => "'${isAutorenew}' must be a boolean"]),
                ],
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true,
        ]));
    }
}
