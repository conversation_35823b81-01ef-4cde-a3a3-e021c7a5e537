<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Traits;

use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Exception\CompanyNotFound;

trait CompanyTrait
{
    public static function checkCompanyId(?int $companyId, array $data): ?int
    {
        if (\array_key_exists('company_id', $data) === true && $companyId === 0) {
            $companyId = \intval($data['company_id']);
            // check if company id is valid
            try {
                container()->get(CompanyService::class)->get($companyId);
            } catch (CompanyNotFound $e) {
                static::exception(__(
                    'error_exim_invalid_fields',
                    [
                        '[fields]' => 'Company id'
                    ]
                ));
            }
        }

        return $companyId;
    }
}
