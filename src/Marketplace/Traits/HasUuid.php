<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Traits;

use Rhumsaa\Uuid\Uuid;
use Wizacha\Marketplace\Exception\IntegrityConstraintViolation;

trait HasUuid
{
    protected ?string $id;

    public function getId(): ?string
    {
        return $this->id;
    }

    public function defineGuid(): self
    {
        $this->id = Uuid::uuid4()->toString();

        return $this;
    }

    protected static function checkUuidIntegrity(string $id)
    {
        if (\strlen($id) != 36) {
            throw IntegrityConstraintViolation::isInvalid('id');
        }
    }
}
