<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Date;

class DateService extends \DateTimeImmutable
{
    public function createFromImmutable(\DateTimeImmutable $date): self
    {
        return (new DateService())
            ->setDate((int) $date->format("Y"), (int) $date->format("m"), (int) $date->format("d"))
            ->setTime((int) $date->format("H"), (int) $date->format("i"), (int) $date->format("s"))
        ;
    }

    public function addMonths(int $nbMonth): self
    {
        $newDate = null;
        $day = (int) $this->format("d");
        $nbDaysInMonth = (int) $this->format('t');

        switch ($day) {
            case 28:
            case 29:
                if (2 === (int) $this->format("m") && $day === $nbDaysInMonth) {
                    $newDate = $this->modify("last day of this month +" . $nbMonth . " month");
                }
                break;
            case 30:
            case 31:
                if ($day === $nbDaysInMonth) {
                    $newDate = $this->modify("last day of this month +" . $nbMonth . " month");
                }
                break;
        }

        if (false === $newDate instanceof self) {
            $newDate = $this->modify("+" . $nbMonth . " month");
        }

        return $newDate;
    }
}
