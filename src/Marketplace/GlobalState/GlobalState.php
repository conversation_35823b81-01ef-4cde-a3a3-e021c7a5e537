<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\GlobalState;

use Symfony\Contracts\Translation\TranslatorInterface;
use Tygh\Languages\Languages;
use Wizacha\Component\Locale\Locale;

/**
 * Use this singleton wisely, think twice before adding responsibility to it
 */
class GlobalState
{
    private static $instance;

    private $interfaceLocale;
    private $contentLocale;
    private $fallbackLocale;
    private $translator;

    private function __construct(TranslatorInterface $translator, Locale $fallbackLocale)
    {
        $this->translator = $translator;
        $this->fallbackLocale = $fallbackLocale;
    }

    public static function init(
        Locale $currentLocale,
        Locale $fallbackLocale,
        TranslatorInterface $translator
    ): void {
        self::$instance = new self($translator, $fallbackLocale);
        self::switchInterfaceTo($currentLocale);
        self::switchContentTo($currentLocale);
    }

    public static function switchInterfaceTo(Locale $locale): void
    {
        self::getInstance()->interfaceLocale = $locale;
        self::getInstance()->translator->setLocale((string) $locale);
        \Locale::setDefault((string) $locale);
    }

    public static function switchContentTo(Locale $locale): void
    {
        self::getInstance()->contentLocale = $locale;
    }

    public static function setFallbackLocale(Locale $locale): void
    {
        self::getInstance()->fallbackLocale = $locale;
    }

    public static function runWithInterface(Locale $locale, callable $callable)
    {
        try {
            $interface = self::interfaceLocale();
            self::switchInterfaceTo($locale);
            $result = $callable();
        } finally {
            if ($interface instanceof Locale) {
                self::switchInterfaceTo($interface);
            }
        }

        return $result;
    }

    public static function runWithContent(Locale $locale, callable $callable)
    {
        try {
            $content = self::contentLocale();
            self::switchContentTo($locale);
            $result = $callable();
        } finally {
            if ($content instanceof Locale) {
                self::switchContentTo($content);
            }
        }

        return $result;
    }

    public static function runWithAllLanguages(callable $callable): void
    {
        self::runWithLanguages($callable, Languages::getAll());
    }

    public static function runWithAvailableLanguages(callable $callable): void
    {
        self::runWithLanguages($callable, Languages::getAvailable());
    }

    /**
     * Cette locale est à utiliser pour manipuler tout ce qui est l'interface
     * dans l'admin (en gros tous les menus) et à utiliser pour avoir la langue
     * de l'utilisateur dans le front
     */
    public static function interfaceLocale(): Locale
    {
        return self::getInstance()->interfaceLocale;
    }

    /**
     * Cette locale correspond à la langue du contenu que l'on veut modifier
     * dans l'admin
     *
     * Par exemple je peux avoir l'interface en français et modifier un produit
     * en anglais, cette locale sera donc en anglais
     */
    public static function contentLocale(): Locale
    {
        return self::getInstance()->contentLocale;
    }

    /**
     * Retourne la locale par défaut de la marketplace
     */
    public static function fallbackLocale(): Locale
    {
        return self::getInstance()->fallbackLocale;
    }

    protected static function getInstance(): self
    {
        if (!self::$instance instanceof self) {
            throw new \LogicException('Global state not initialized');
        }

        return self::$instance;
    }

    protected static function runWithLanguages(callable $callable, array $languages): void
    {
        $interface = self::interfaceLocale();
        $content = self::contentLocale();
        try {
            foreach ($languages as $language) {
                $locale = new Locale($language['lang_code']);
                self::switchContentTo($locale);
                self::switchInterfaceTo($locale);
                $callable();
            }
        } finally {
            self::switchContentTo($content);
            self::switchInterfaceTo($interface);
        }
    }
}
