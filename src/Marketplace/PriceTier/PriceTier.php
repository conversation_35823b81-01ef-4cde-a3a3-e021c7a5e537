<?php

/**
 *  <AUTHOR> DevTeam <<EMAIL>>
 *  @copyright   Copyright (c) Wizacha
 *  @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PriceTier;

use Wizacha\Marketplace\PIM\Product\Product;
use Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory;
use Wizacha\Money\Money;

class PriceTier
{
    /** @var null|int */
    protected $id;

    /** @var Product */
    protected $product;

    /** @var null|ProductOptionInventory */
    protected $productOptionInventory;

    /** @var null|int */
    protected $lowerLimit;

    /** @var null|Money */
    protected $price;

    /** @var null|Money */
    protected $taxes;

    /** @var bool */
    protected $priceIncludeTax;

    /** @var null|string */
    protected $productIdLowerLimitOptionId;

    public function __construct(
        Product $product,
        ProductOptionInventory $productOptionInventory = null,
        $priceIncludeTax = true
    ) {
        $this->setProductOptionInventory($productOptionInventory);
        $this->setPriceIncludeTax($priceIncludeTax ?? false);
        $this->setProduct($product);
    }

    public function __clone()
    {
        $this->id = null;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function hasId(): bool
    {
        return \is_int($this->id) && $this->id > 0;
    }

    public function isDefault(): bool
    {
        return (
            false === $this->getProductOptionInventory() instanceof ProductOptionInventory
            && 0 < $this->getLowerLimit()
        );
    }

    public function setProduct(Product $product): self
    {
        $this->product = $product;

        $product->addPriceTier($this);

        return $this;
    }

    public function getProduct(): Product
    {
        return $this->product;
    }

    public function setProductOptionInventory(?ProductOptionInventory $productOptionInventory): self
    {
        $this->productOptionInventory = $productOptionInventory;
        if ($productOptionInventory instanceof ProductOptionInventory) {
            $productOptionInventory->addPriceTier($this);
        }

        return $this;
    }

    public function getProductOptionInventory(): ?ProductOptionInventory
    {
        return $this->productOptionInventory;
    }

    public function setLowerLimit(int $lowerLimit): self
    {
        $this->lowerLimit = $lowerLimit;

        return $this;
    }

    public function getLowerLimit(): ?int
    {
        return $this->lowerLimit;
    }

    public function setPrice(Money $price): self
    {
        $this->price = $price;

        return $this;
    }

    public function getPrice(): ?Money
    {
        return $this->price;
    }

    public function setTaxes(?Money $taxes): self
    {
        $this->taxes = $taxes;

        return $this;
    }

    public function getTaxes(): ?Money
    {
        return $this->taxes;
    }

    public function setPriceIncludeTax(bool $priceIncludeTax): self
    {
        $this->priceIncludeTax = $priceIncludeTax;

        return $this;
    }

    public function getPriceIncludeTax(): bool
    {
        return $this->priceIncludeTax ?? false;
    }

    public function expose(bool $minimalInformation = false): array
    {
        if (true === $minimalInformation) {
            $data = [
                "lowerLimit" => $this->getLowerLimit(),
                "price" => $this->getPrice()->getConvertedAmount(),
            ];
        } else {
            if (true === $this->getPriceIncludeTax()) {
                $includingTaxes = $this->getPrice()->getConvertedAmount();
                $excludingTaxes = $this->getPrice()->subtract($this->getTaxes())->getConvertedAmount();
            } else {
                $includingTaxes = $this->getPrice()->add($this->getTaxes())->getConvertedAmount();
                $excludingTaxes = $this->getPrice()->getConvertedAmount();
            }

            $data = [
                "lowerLimit" => $this->getLowerLimit(),
                "includingTaxes" => $includingTaxes,
                "excludingTaxes" => $excludingTaxes,
                "taxes" => $this->getTaxes()->getConvertedAmount(),
            ];
        }

        return $data;
    }

    /**
     * @param Money $price
     * @param int $lowerLimit
     *
     * @return PriceTier
     */
    public function setLowerLimitAndPrice(Money $price, int $lowerLimit = 0): self
    {
        $this->setLowerLimit($lowerLimit);
        $this->setPrice($price);

        return $this;
    }

    public function toArray(): array
    {
        return [
            'id' => $this->getId(),
            'product_id' => $this->getProduct()->getId(),
            'product_option_inventory' => ($this->getProductOptionInventory() instanceof ProductOptionInventory) ? $this->getProductOptionInventory()->getId() : null,
            'lowerLimit' => $this->getLowerLimit(),
            'price' => $this->getPrice()->getConvertedAmount(),
        ];
    }

    public function clonePriceTier(Product $product, ?ProductOptionInventory $combination): self
    {
        $priceClone = clone $this;

        return $priceClone
            ->setProduct($product)
            ->setProductOptionInventory($combination);
    }

    public function getProductIdLowerLimitOptionId(): ?string
    {
        return $this->productIdLowerLimitOptionId;
    }

    /** Called on prePersist & preUpdate of PriceTier Object */
    public function setProductIdLowerLimitOptionId(): void
    {
        $this->productIdLowerLimitOptionId = $this->product->getId() . '_' . $this->lowerLimit;

        if ($this->getProductOptionInventory() instanceof ProductOptionInventory) {
            $this->productIdLowerLimitOptionId .= '_' . $this->productOptionInventory->getId();
        }
    }
}
