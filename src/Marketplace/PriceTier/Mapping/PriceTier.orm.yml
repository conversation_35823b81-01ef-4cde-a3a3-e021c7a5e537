Wizacha\Marketplace\PriceTier\PriceTier:
    type: entity
    table: product_price_tiers
    indexes:
        product_id:
            columns:
                - product_id
        product_option_inventory_id:
            columns:
                - product_option_inventory_id
    uniqueConstraints:
        product_option_lower_limit:
            columns:
                - product_id
                - product_option_inventory_id
                - lower_limit
        product_id_lower_limit_option_id:
            columns:
                - product_id_lower_limit_option_id
    lifecycleCallbacks:
        prePersist: [ setProductIdLowerLimitOptionId ]
        preUpdate: [ setProductIdLowerLimitOptionId ]
    id:
        id:
            type: integer
            generator:
                strategy: AUTO
            options:
                unsigned: true
    fields:
        lowerLimit:
            type: integer
            options:
                unsigned: true
        price:
            type: money
            options:
                unsigned: true
        productIdLowerLimitOptionId:
            type: text
            column: product_id_lower_limit_option_id
    manyToOne:
        product:
            targetEntity: Wizacha\Marketplace\PIM\Product\Product
            inversedBy: priceTiers
            joinColumn:
                referencedColumnName: product_id
                nullable: false
                cascade:
                    - persist
        productOptionInventory:
            targetEntity: Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory
            inversedBy: priceTiers
