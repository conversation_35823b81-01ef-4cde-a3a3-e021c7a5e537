<?php

/**
 *  <AUTHOR> DevTeam <<EMAIL>>
 *  @copyright   Copyright (c) Wizacha
 *  @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PriceTier\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\Query\ResultSetMappingBuilder;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Wizacha\Marketplace\PriceTier\PriceTier;
use Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory;

class PriceTierRepository extends ServiceEntityRepository
{
    public function save(PriceTier $priceTier): PriceTier
    {
        $this->getEntityManager()->persist($priceTier);
        $this->getEntityManager()->flush($priceTier);

        return $priceTier;
    }

    public function remove(PriceTier $priceTier): self
    {
        $this->getEntityManager()->remove($priceTier);
        $this->getEntityManager()->flush($priceTier);

        return $this;
    }

    /**
     * Get priceTiers from a productId
     * @param int|string $productId : On productService, legacyCode have id as string. Use INT if you can.
     * @return PriceTier[]
     */
    public function findByProductIdWithoutInventory($productId): array
    {
        return $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->select('pt')
            ->from(PriceTier::class, 'pt')
            ->where("pt.product = ?0")
            ->andWhere("pt.productOptionInventory IS NULL")
            ->setParameter(0, $productId)
            ->orderBy("pt.lowerLimit", 'ASC')
            ->getQuery()
            ->getResult();
    }

    /** @return PriceTier[] */
    public function findByProductId(int $productId): array
    {
        return $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->select('pt')
            ->from(PriceTier::class, 'pt')
            ->where("pt.product = ?0")
            ->setParameter(0, $productId)
            ->orderBy("pt.lowerLimit", 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * @param int $productId
     * @param int $lowerLimit
     * @param int|null $productOptionInventoryId
     *
     * @throws NonUniqueResultException
     *
     * @return mixed
     */
    public function findOneByLowerLimitAndProductOptionId(int $productId, int $lowerLimit, int $productOptionInventoryId = null)
    {
        $query = $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->select('PARTIAL pt.{id, lowerLimit, price}')
            ->from(PriceTier::class, 'pt')
            ->where("pt.product = ?0")
            ->andWhere("pt.lowerLimit = ?1");

        if ($productOptionInventoryId === null) {
            $query
                ->andWhere("pt.productOptionInventory IS NULL")
                ->setParameters([
                    $productId,
                    $lowerLimit,
                ]);
        } else {
            $query
                ->andWhere("pt.productOptionInventory = ?2")
                ->setParameters([
                    $productId,
                    $lowerLimit,
                    $productOptionInventoryId,
                ]);
        }

        return $query
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Get priceTiers from a ProductOptionInventory
     *
     * @return PriceTier[]
     */
    public function findByProductOptionInventory(ProductOptionInventory $productOptionInventory): array
    {
        return $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->select('pt')
            ->from(PriceTier::class, 'pt')
            ->where("pt.productOptionInventory = ?0")
            ->setParameter(0, $productOptionInventory)
            ->orderBy("pt.lowerLimit", 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get a priceTier from its id
     *
     * @throws NoResultException
     * @throws NonUniqueResultException
     *
     * @return PriceTier
     */
    public function get(int $id): PriceTier
    {
        return $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->select('pt')
            ->from(PriceTier::class, 'pt')
            ->where("pt.id = ?0")
            ->setParameter(0, $id)
            ->setMaxResults(1)
            ->getQuery()
            ->getSingleResult();
    }

    /**
     * @param int $productId
     * @return array
     */
    public function getPriceTiersIdByProductId(int $productId): array
    {
        return $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->select('pt.id')
            ->from(PriceTier::class, 'pt')
            ->where("pt.product = ?0")
            ->andWhere("pt.productOptionInventory IS NULL")
            ->setParameter(0, $productId)
            ->getQuery()
            ->getResult();
    }

    /**
     * @param int $productId
     * @param int $lowerLimit
     *
     * @throws NoResultException
     * @throws NonUniqueResultException
     *
     * @return null|PriceTier
     */
    public function findOneByProductIdAndLowerLimit(int $productId, int $lowerLimit): ?PriceTier
    {
        return $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->select('pt')
            ->from(PriceTier::class, 'pt')
            ->where("pt.product = ?0")
            ->andWhere("pt.lowerLimit = ?1")
            ->andWhere("pt.productOptionInventory IS NULL")
            ->setParameters([
                $productId,
                $lowerLimit,
            ])
            ->orderBy("pt.lowerLimit", 'ASC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * @param int $productId
     * @param int|null $inventoryId
     *
     * @throws \Doctrine\ORM\NoResultException
     * @throws \Doctrine\ORM\NonUniqueResultException
     *
     * @return array
     */
    public function getPricesTiersPartialForAPiByProductAndInventory(int $productId, int $inventoryId = null): array
    {
        return $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->select('PARTIAL pt.{id, lowerLimit, price}')
            ->from(PriceTier::class, 'pt')
            ->where('pt.product = :productId')
            ->andWhere('pt.productOptionInventory = :inventoryId')
            ->setParameter('productId', $productId)
            ->setParameter('inventoryId', $inventoryId)
            ->orderBy('pt.lowerLimit', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /** @return PriceTier[] */
    public function findByProductAndProductOptionInventory(int $productId, ?ProductOptionInventory $productOptionInventory): array
    {
        $qb = $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->select('pt')
            ->from(PriceTier::class, 'pt')
            ->where('pt.product = :productId')
            ->setParameter('productId', $productId);

        if ($productOptionInventory instanceof ProductOptionInventory) {
            $qb
                ->andWhere('pt.productOptionInventory = :inventoryId')
                ->setParameter('inventoryId', $productOptionInventory);
        } else {
            $qb->andWhere('pt.productOptionInventory IS NULL');
        }

        return $qb
            ->orderBy('pt.lowerLimit', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /** @return PriceTier[] */
    public function findAllPriceTiers(): array
    {
        return $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->select('pt.id')
            ->from(PriceTier::class, 'pt')
            ->getQuery()
            ->getResult();
    }

    /**
     * @param string $combination
     * @param int $productId
     *
     * @return PriceTier[]
     */
    public function findPriceTiersByCombinationCode(string $combination, int $productId): array
    {
        $rsm = new ResultSetMappingBuilder($this->getEntityManager());
        $rsm->addRootEntityFromClassMetadata(PriceTier::class, 'pt');
        $query = $this->getEntityManager()->createNativeQuery(
            'SELECT pt.id, pt.lower_limit, pt.price, pt.product_id, pt.product_option_inventory_id
                FROM doctrine_product_price_tiers pt
                INNER JOIN cscart_product_options_inventory inv
                ON inv.combination = :combination AND inv.id = pt.product_option_inventory_id AND inv.product_id = :productId
                WHERE pt.product_option_inventory_id IS NOT NULL',
            $rsm
        );
        $query->setParameters(['combination' => $combination, 'productId' => $productId]);

        return $query->getResult();
    }

    public function getEntityManager(): EntityManager
    {
        return $this->_em;
    }

    public function findByQuantityAndDeclination(int $productId, int $productOptionInventoryId, int $quantity): ?PriceTier
    {
        $queryBuilder = $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->select('priceTier')
            ->from(PriceTier::class, 'priceTier')
            ->where('priceTier.product = :productId')
            ->andWhere('priceTier.lowerLimit <= :quantity')
            ->setParameter('productId', $productId)
            ->setParameter('quantity', $quantity)
        ;

        if ($productOptionInventoryId !== 0) {
            $queryBuilder
                ->andWhere('priceTier.productOptionInventory = :inventoryId')
                ->setParameter('inventoryId', $productOptionInventoryId)
            ;
        } else {
            $queryBuilder->andWhere('priceTier.productOptionInventory IS NULL');
        }

        return $queryBuilder
            ->orderBy('priceTier.lowerLimit', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }

    /**
     * @param int $productId
     * @param int $combination
     *
     * @return PriceTier[]
     */
    public function findByProductAndDeclination(int $productId, int $combination): array
    {
        $queryBuilder = $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->select('priceTier')
            ->from(PriceTier::class, 'priceTier')
            ->where('priceTier.product = :productId')
            ->setParameter('productId', $productId)
        ;

        if ($combination !== 0) {
            $queryBuilder
                ->andWhere('priceTier.productOptionInventory = :inventoryId')
                ->setParameter('inventoryId', $combination);
        } else {
            $queryBuilder->andWhere('priceTier.productOptionInventory IS NULL');
        }

        return $queryBuilder
            ->orderBy('priceTier.lowerLimit', 'ASC')
            ->getQuery()
            ->getResult()
        ;
    }

    /** @return PriceTier[] */
    public function findByProductIdAndLowerLimitNotZero(int $productId): array
    {
        return $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->select('pt')
            ->from(PriceTier::class, 'pt')
            ->where("pt.product = ?0")
            ->andWhere("pt.lowerLimit > 0")
            ->andWhere("pt.productOptionInventory IS NULL")
            ->setParameters([
                $productId,
            ])
            ->orderBy("pt.lowerLimit", 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function deletePriceTiersLowerLimitNot0(): void
    {
        $this->getEntityManager()
            ->createQueryBuilder()
            ->delete('PriceTier:PriceTier', 'pt')
            ->where('pt.lowerLimit > 0')
            ->getQuery()
            ->execute();
    }

    public function deletePriceTiersDuplications(): self
    {
        $rawSql = "DELETE doctrine_product_price_tiers
        FROM doctrine_product_price_tiers
        LEFT OUTER JOIN (
            SELECT MIN(id) as product_id, product_option_inventory_id, lower_limit
            FROM doctrine_product_price_tiers
            GROUP BY product_id, product_option_inventory_id, lower_limit
            ) AS table_1
            ON doctrine_product_price_tiers.id = table_1.product_id
        WHERE table_1.product_id IS NULL
        ";

        $stmt = $this->getEntityManager()->getConnection()->prepare($rawSql);
        $stmt->execute();

        return $this;
    }

    public function realignBasicProductOnlyPriceTiers(): self
    {
        $rawSql = "UPDATE doctrine_product_price_tiers AS pt
            INNER JOIN cscart_product_prices AS pp ON pt.product_id = pp.product_id
            SET pt.price = (pp.price*100)
            WHERE pt.product_option_inventory_id IS NULL;
        ";

        $stmt = $this->getEntityManager()->getConnection()->prepare($rawSql);
        $stmt->execute();

        return $this;
    }

    public function realignProductOptionInventoryPriceTiers(): self
    {
        $rawSql = "UPDATE doctrine_product_price_tiers AS pt
            INNER JOIN cscart_product_options_inventory AS poi ON pt.product_option_inventory_id = poi.id
            SET pt.price = (poi.w_price*100)
            WHERE pt.product_option_inventory_id IS NOT NULL;
        ";

        $stmt = $this->getEntityManager()->getConnection()->prepare($rawSql);
        $stmt->execute();

        return $this;
    }

    public function createMissingProductOnlyPricetiers(): self
    {
        $rawSql = "INSERT INTO doctrine_product_price_tiers (product_id, product_option_inventory_id, lower_limit, price)
            SELECT DISTINCT pp.product_id, poi.id,
                0,
                IF(pp.product_id = poi.product_id, poi.w_price, pp.price) * 100
            FROM cscart_product_prices as pp
            LEFT JOIN cscart_product_options_inventory AS poi ON pp.product_id = poi.product_id
            WHERE pp.product_id IN (SELECT pp.product_id  FROM cscart_product_prices pp
                WHERE pp.product_id NOT IN (SELECT pt.product_id FROM doctrine_product_price_tiers pt))
                OR poi.id IN (SELECT poi.id  FROM cscart_product_options_inventory poi
                    WHERE poi.id NOT IN (SELECT pt.product_option_inventory_id AS id FROM doctrine_product_price_tiers pt));";

        $stmt = $this->getEntityManager()->getConnection()->prepare($rawSql);
        $stmt->execute();

        return $this;
    }

    public function createMissingProductOptionInventoryPriceTiers(): self
    {
        $rawSql = "INSERT INTO doctrine_product_price_tiers (product_id, product_option_inventory_id, lower_limit, price)
            SELECT DISTINCT pp.product_id, poi.id,
                0,
                IF(pp.product_id = poi.product_id, poi.w_price, pp.price) * 100
            FROM cscart_product_prices as pp
            LEFT JOIN cscart_product_options_inventory AS poi ON pp.product_id = poi.product_id
                WHERE pp.product_id IN (SELECT pp.product_id  FROM cscart_product_prices pp
                    WHERE pp.product_id NOT IN (SELECT pt.product_id FROM doctrine_product_price_tiers pt))
                    OR poi.id IN (SELECT poi.id  FROM cscart_product_options_inventory poi
                        WHERE poi.id NOT IN (SELECT pt.product_option_inventory_id AS id FROM doctrine_product_price_tiers pt WHERE pt.product_option_inventory_id IS NOT NULL));
        ";

        $stmt = $this->getEntityManager()->getConnection()->prepare($rawSql);
        $stmt->execute();

        return $this;
    }
}
