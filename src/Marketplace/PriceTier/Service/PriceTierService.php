<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright Copyright (c) Wizaplace
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PriceTier\Service;

use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Symfony\Component\HttpKernel\KernelInterface;
use Wizacha\AppBundle\Controller\Api\Taxes;
use Wizacha\Core\Accumulator;
use Wizacha\Core\Helper;
use Wizacha\Events\Config;
use Wizacha\Events\IterableEvent;
use Wizacha\Marketplace\Entities\Tax;
use Wizacha\Marketplace\PIM\Product\Product;
use Wizacha\Marketplace\PIM\Product\ProductService;
use Wizacha\Marketplace\PriceTier\Exception\PriceTierException;
use Wizacha\Marketplace\PriceTier\PriceTier;
use Wizacha\Marketplace\PriceTier\Repository\PriceTierRepository;
use Wizacha\Marketplace\PriceTier\Validator\PriceTierValidator;
use Wizacha\Marketplace\ProductOptionInventory\ProductOptionInventory;
use Wizacha\Marketplace\ProductOptionInventory\Repository\ProductOptionInventoryRepository;
use Wizacha\Marketplace\ProductOptionInventory\Service\ProductOptionInventoryService;
use Wizacha\Money\Money;
use Wizacha\Product as WizachaProduct;

class PriceTierService
{
    protected const COMMIT_BULK = 64;

    /** @var ProductService */
    protected $productService;

    /** @var PriceTierRepository */
    protected $priceTierRepository;

    /** @var ProductOptionInventoryRepository */
    protected $productOptionInventoryRepository;

    /** @var ProductOptionInventoryService */
    protected $productOptionInventoryService;

    /** @var bool */
    protected $priceTierFlag;

    public function __construct(
        PriceTierRepository $priceTierRepository,
        ProductService $productService,
        ProductOptionInventoryRepository $productOptionInventoryRepository,
        ProductOptionInventoryService $productOptionInventoryService,
        bool $priceTierFlag
    ) {
        $this->priceTierRepository = $priceTierRepository;
        $this->productService = $productService;
        $this->productOptionInventoryRepository = $productOptionInventoryRepository;
        $this->productOptionInventoryService = $productOptionInventoryService;
        $this->priceTierFlag = $priceTierFlag;
    }

    /**
     * @param array $inventory
     *
     * @return PriceTierException[] $errors
     */
    public function savePriceTiers(int $productId, array $inventory): array
    {
        $product = $this->productService->get($productId);
        $priceTierValidator = new PriceTierValidator();
        $errors = [];

        $this->controlInventoryFormat($inventory);

        foreach ($inventory as &$singleItem) {
            if (\array_key_exists("priceTiers", $singleItem) && \is_array($singleItem["priceTiers"])) {
                $priceTierValidator->validateNumberOfPriceTiers($singleItem['priceTiers']);

                foreach ($singleItem['priceTiers'] as $priceTier) {
                    $priceTierValidator->validateLowerLimit($priceTier['lowerLimit'], $product);
                    $priceTierValidator->validatePrice($priceTier['price'], $product);
                }

                // There is an error, we don't break, but stock it and save with no priceTiers
                if ($priceTierValidator->getError() !== null) {
                    $errors[] = $priceTierValidator->getError();
                    unset($singleItem['priceTiers']);
                }

                $priceTierValidator->cleanLowerLimit();
            }
        }

        $this->buildNewPriceTiers($product, $inventory);
        $this->alignPricesOnPriceTiers($product);

        Config::dispatch(
            WizachaProduct::EVENT_UPDATE,
            (new IterableEvent())->setElement($product->getId())
        );

        return $errors;
    }

    /** For testing purposes only */
    public function setFeatureFlag(bool $forcedFeatureFlag): self
    {
        $kernel = container()->get('kernel');

        if (false === $kernel instanceof KernelInterface || 'test' !== $kernel->getEnvironment()) {
            throw new \LogicException('The setFeatureFlag method is for testing purposes only');
        }

        $this->priceTierFlag = $forcedFeatureFlag;

        return $this;
    }

    /**
     * Updates price tiers for a single combination without changing other price tiers
     *
     * @param mixed[] $combinationInventory
     */
    public function updateCombinationPriceTiers(Product $product, array $combinationInventory): void
    {
        $productOptionInventory = $this->getProductOptionInventory($combinationInventory['combination'], $product);

        foreach ($this->priceTierRepository->findByProductAndProductOptionInventory($product->getId(), $productOptionInventory) as $oldPriceTier) {
            $this->priceTierRepository->remove($oldPriceTier);
        }

        foreach ($combinationInventory['priceTiers'] as $singlePriceTier) {
            $priceTier = new PriceTier($product);
            $priceTier
                ->setProductOptionInventory($productOptionInventory)
                ->setLowerLimitAndPrice(Money::fromVariable($singlePriceTier['price']), $singlePriceTier['lowerLimit']);

            $this->priceTierRepository->save($priceTier);
        }

        $this->alignPricesOnPriceTiers($product, $combinationInventory['combination']);

        Config::dispatch(
            WizachaProduct::EVENT_UPDATE,
            (new IterableEvent())->setElement($product->getId())
        );
    }

    /** @param array|string $combination */
    public function combinationToString($combination): ?string
    {
        $return = null;
        if (\is_string($combination) && mb_strlen($combination) > 0) {
            $return = $combination;
        } elseif (\is_array($combination) && \count($combination) > 0) {
            $return = fn_get_options_combination($combination);
        }

        return $return;
    }

    /**
     * Adds combinations absent from inventory but present in allowed_options_variants to the inventory
     *
     * @param int[] $allowedOptionsVariants
     * @param mixed[] $inventory
     *
     * @return array
     */
    public function createEmptyMissingCombinations(array $allowedOptionsVariants, array $inventory): array
    {
        $allCombinations = $this->productOptionInventoryService->allowedOptionsVariantsToString($allowedOptionsVariants);
        $existingCombinations = array_column($inventory, 'combination');

        foreach (array_diff($allCombinations, $existingCombinations) as $missingCombination) {
            $inventory[] = [
                'amount' => 0,
                'combination' => $missingCombination,
                'product_code' => '',
                'w_price' => 0,
            ];
        }

        return $inventory;
    }

    /**
     * Adds combinations absent from inventory but already existing to the inventory
     *
     * @param int[] $allowedOptionsVariants
     * @param mixed[] $inventory
     *
     * @return mixed[]
     */
    public function createExistingCombinations(array $allowedOptionsVariants, array $inventory, ?Product $product = null): array
    {
        // Creates combination non-posted but authorized with their existing amount, price, crossed-out price
        $allCombinations = $this->productOptionInventoryService->allowedOptionsVariantsToString($allowedOptionsVariants);
        $existingCombinations = array_column($inventory, 'combination');

        foreach (array_diff($allCombinations, $existingCombinations) as $missingCombination) {
            $combination = container()->get('marketplace.price_tier.product_option_inventory_repository')->findOneByCombination($missingCombination, $product->getId());
            $combinationPriceTiers = [];

            foreach ($combination->getPriceTiers() as $priceTier) {
                $combinationPriceTiers[] = [
                    'lowerLimit' => $priceTier->getLowerLimit(),
                    'price' => $priceTier->getPrice()->getConvertedAmount(),
                ];
            }

            $crossedOutPrice = $combination->getCrossedOutPrice() instanceof Money ? $combination->getCrossedOutPrice()->getConvertedAmount() : null;
            $inventory[] = [
                'amount' => $combination->getAmount(),
                'combination' => $missingCombination,
                'product_code' => $combination->getProductCode(),
                'w_price' => $combination->getWPrice()->getConvertedAmount(),
                'price_tiers' => $combinationPriceTiers,
                'crossed_out_price' => $crossedOutPrice,
                'infinite_stock' => $combination->getInfiniteStock(),
            ];
        }

        return $inventory;
    }

    /**
     * Remove Options which are not allowed on the product's category
     *
     * @param array $allowedOptions
     *
     * @return array
     */
    public function filterAllowedOptions(int $productId, array $allowedOptions): array
    {
        $categoryOptions = fn_w_get_category_options(fn_w_get_product_main_category($productId)) ?: [];

        // Format category_options
        $categoryOptionsFormatted = [];
        foreach ($categoryOptions as $key => $value) {
            $categoryOptionsFormatted[$key] = $value['variants'];
        }

        if (\count($categoryOptionsFormatted) > 1) {
            // Remove options that are not among categoryOptions from allowedOptions
            foreach ($allowedOptions as $optionKey => $variants) {
                if (\array_key_exists($optionKey, $categoryOptionsFormatted) === false) {
                    unset($allowedOptions[$optionKey]);
                }
            }

            // Remove variant options that are not among categoryOptions variants from allowedOptions
            foreach ($allowedOptions as $allowedOptionKey => $allowedOptionValues) {
                foreach ($allowedOptionValues as $allowedVariantKey => $allowedVariantValue) {
                    if (\array_key_exists($allowedVariantKey, $categoryOptionsFormatted[$allowedOptionKey]) === false) {
                        unset($allowedOptions[$allowedOptionKey][$allowedVariantKey]);
                    }
                }
            }

            return $allowedOptions;
        }

        return [];
    }

    /**
     * Get price tiers belonging to a product or a combination
     *
     * @return PriceTier[]
     */
    public function getPriceTiersOfProductOrCombination(int $productId, ?string $combination): array
    {
        if (false === \is_string($combination) || 0 === $combination) {
            return $this
                ->priceTierRepository
                ->findByProductIdWithoutInventory($productId);
        }

        return $this
            ->priceTierRepository
            ->findByProductOptionInventory(
                $this
                    ->productOptionInventoryRepository
                    ->findOneByCombination($combination, $productId)
            );
    }

    public function hasPriceTiers(
        int $productId,
        ?string $combination
    ): bool {
        $productPriceTiers = $this->getPriceTiersOfProductOrCombination($productId, $combination);

        return \count($productPriceTiers) > 1;
    }

    /**
     * @return array[]
     */
    public function buildAndExposePriceTiersWithTaxesForReadModel($product, string $combinationCode): array
    {
        // Get PriceTiers List
        if ($product instanceof WizachaProduct === false) {
            $product = new WizachaProduct($product, true);
        }
        $priceTiers = $this->priceTierRepository->findByProductAndDeclination($product->getId(), fn_inventoryId_from_string($product->getId(), $combinationCode) ?? 0);

        return $this->buildAndExposePriceTiersWithTaxes($product, $priceTiers);
    }

    /**
     * Checks if inventory contains a 'combination' key, which is the only mandatory key for a correct price tiers management
     *
     * @param mixed[] $inventory
     */
    protected function controlInventoryFormat(array $inventory): self
    {
        foreach ($inventory as $singleItemInventory) {
            if (\array_key_exists('combination', $singleItemInventory) === false) {
                throw new PriceTierException('The price tiers inventory format sent to price tier service is incorrect. Please report it to your administrator.');
            }
        }

        return $this;
    }

    /**
     * Updates product & product_options_inventory prices depending on price_tiers prices
     */
    protected function alignPricesOnPriceTiers(Product $product, string $combination = null): void
    {
        /** @var PriceTier[] */
        $priceTiers = $product->getPriceTiers();

        $connection = $this->priceTierRepository->getEntityManager()->getConnection();

        $updateAccumulator = new Accumulator(
            static::COMMIT_BULK,
            function (\ArrayObject $bag) use ($connection): void {
                $updateStatement = $connection->prepare(
                    <<<SQL
                    UPDATE
                        cscart_product_options_inventory
                    SET
                        w_price= :price
                    WHERE
                        id = :id
                    SQL
                );

                $connection->beginTransaction();
                foreach ($bag as $params) {
                    $updateStatement->execute($params);
                }
                $connection->commit();
            }
        );

        $insertAccumulator = new Accumulator(
            static::COMMIT_BULK,
            function (\ArrayObject $bag) use ($connection): void {
                $insertStatement = $connection->prepare(
                    \sprintf(
                        <<<SQL
                        INSERT INTO
                            cscart_product_prices(
                                product_id,
                                price,
                                lower_limit
                            )
                        VALUES
                            %s
                        ON DUPLICATE KEY UPDATE
                            price = VALUES(price)
                        SQL,
                        \implode(
                            ',',
                            \array_fill(0, $bag->count(), '(?, ?, ?)')
                        )
                    )
                );
                $params = \iterator_to_array(
                    Helper::arrayFlatten(
                        $bag->getArrayCopy()
                    ),
                    false
                );

                $insertStatement->execute($params);
            }
        );

        foreach ($priceTiers as $singlePriceTier) {
            $productOptionInventory = $singlePriceTier->getProductOptionInventory();

            if ($singlePriceTier->getLowerLimit() === 0) {
                $singlePriceTierPrice = $singlePriceTier->getPrice()->getConvertedAmount();

                $productId = $product->getId();

                if ($productOptionInventory instanceof ProductOptionInventory) {
                    if (true === \is_null($combination)
                        || $combination === $productOptionInventory->getCombination()
                    ) {
                        $updateAccumulator->trigger(
                            [
                                'price' => $singlePriceTierPrice,
                                'id' => $productOptionInventory->getId(),
                            ]
                        );
                    }
                } else {
                    // Careful here : 'lower_limit' set to "1" is mandatory if we want fn_get_product_data to retrieve the price of the product
                    // The field 'lower_limit' is a legacy one, and has nothing to do with the priceTiers feature
                    $insertAccumulator->trigger(
                        [
                            'product_id' => $productId,
                            'price' => $singlePriceTierPrice,
                            'lower_limit' => 1,
                        ]
                    );
                }
            }
        }

        $insertAccumulator->finalize();
        $updateAccumulator->finalize();
    }

    /**
     * Creates, updates and/or deletes pricetiers for a given product
     *
     * @param array   $newPriceTiers
     *
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    protected function save(Product $product, array $newPriceTiers): self
    {
        $this->deleteOldPriceTiers($product, $newPriceTiers);

        foreach ($newPriceTiers as $newPriceTier) {
            $priceTier = \is_int($newPriceTier['id'])
                ? $this->priceTierRepository->get($newPriceTier['id'])
                : new PriceTier($product);

            if ($priceTier instanceof PriceTier) {
                $priceTier
                    ->setProductOptionInventory($this->productOptionInventoryRepository->findOneById($newPriceTier['product_option_inventory']))
                    ->setLowerLimit($newPriceTier['lowerLimit'])
                    ->setPrice(Money::fromVariable($newPriceTier['price']));

                $this->priceTierRepository->save($priceTier);
            }
        }

        return $this;
    }

    /**
     * @param array $newPriceTiers
     *
     * @throws NoResultException
     * @throws NonUniqueResultException
     */
    protected function deleteOldPriceTiers(Product $product, array $newPriceTiers): self
    {
        $oldPriceTiers = $this->priceTierRepository->findByProductId($product->getId());
        $oldPriceTiersIds = [];

        foreach ($oldPriceTiers as $oldPriceTier) {
            /**
             * We never delete the default declination priceTier
             * (priceTier with productOptionInventory === null and a lower Limit === 0)
             */
            if (false === $oldPriceTier->isDefault()) {
                $oldPriceTiersIds[] = $oldPriceTier->getId();
            }
        }

        $newPriceTiersIds = [];

        foreach ($newPriceTiers as $newPriceTier) {
            $newPriceTiersIds[] = $newPriceTier['id'];
        }

        foreach ($oldPriceTiersIds as $oldPriceTiersId) {
            if (\in_array($oldPriceTiersId, $newPriceTiersIds, true) === false) {
                $this->priceTierRepository->remove(
                    $this->priceTierRepository->get($oldPriceTiersId)
                );
            }
        }

        return $this;
    }

    /**
     * @param array $inventory
     */
    protected function buildNewPriceTiers(Product $product, array $inventory): void
    {
        foreach ($inventory as $singleItem) {
            $productOptionInventory = null;
            $combination = \is_string($singleItem['combination'])
                ? $singleItem['combination']
                : $this->combinationToString($singleItem['combination'])
            ;

            // If it's a combination, we need the productOption and the current Linked PriceTiers, if it's not, we need the priceTiers.
            if ($combination !== null && $combination > 0) {
                // We have a combination, then we need to find the ProductOptionInventory linked and the priceTiers
                $productOptionInventory = $this->getProductOptionInventory(
                    $combination,
                    $product
                );

                if ($productOptionInventory instanceof ProductOptionInventory === false) {
                    return;
                }
            }

            if (\array_key_exists('priceTiers', $singleItem)
                && \is_array($singleItem["priceTiers"])
                && \count($singleItem["priceTiers"]) > 0
            ) {
                $oldPriceTiers = $this->priceTierRepository->findBy([
                    'product' => $product,
                    'productOptionInventory' => $productOptionInventory,
                ]);

                foreach ($oldPriceTiers as $toDelete) {
                    $this->priceTierRepository->getEntityManager()->remove($toDelete);
                }

                $this->priceTierRepository->getEntityManager()->flush();

                foreach ($singleItem['priceTiers'] as $singlePriceTier) {
                    if ($this->checkFeatureFlagAndLowerLimit($singlePriceTier['lowerLimit']) === true) {
                        $this->addPriceTiersBasedOnUserData($product, $singlePriceTier, $productOptionInventory);
                    }
                }
            } else {
                // We don't have any priceTiers on the PUT.
                // First we check in base if we have one, else, let's create one.
                $doesThisCombinationHaveAPriceTierZero = $this->priceTierRepository->findBy(
                    [
                        'product' => $product,
                        'lowerLimit' => 0,
                        'productOptionInventory' => $productOptionInventory,
                    ]
                );

                if (\count($doesThisCombinationHaveAPriceTierZero) < 1) {
                    $price = Money::fromVariable($singleItem['w_price'] ?? $singleItem['price'] ?? 0);

                    if ($this->checkFeatureFlagAndLowerLimit(0) === true) {
                        $this->addSinglePriceTier($product, $price, $productOptionInventory);
                    }
                } elseif (\array_key_exists('w_price', $singleItem)) {
                    // If price tiers exist on the item, & none is present in the inventory for the item, & 'w_price' is in the inventory
                    // We remove all the existing price tiers, and create a new one with lower limit = 0 & price = 'w_price'
                    foreach ($this->priceTierRepository->findByProductAndProductOptionInventory($product->getId(), $productOptionInventory) as $oldPriceTier) {
                        $this->priceTierRepository->getEntityManager()->remove($oldPriceTier);
                    }

                    $this->priceTierRepository->getEntityManager()->flush();

                    if ($this->checkFeatureFlagAndLowerLimit(0) === true) {
                        $price = Money::fromVariable($singleItem['w_price'] ?? 0);

                        $this->addSinglePriceTier($product, $price, $productOptionInventory);
                    }
                }
            }
        }

        $this->priceTierRepository->getEntityManager()->persist($product);
        $this->priceTierRepository->getEntityManager()->flush();
    }

    protected function checkFeatureFlagAndLowerLimit(int $lowerLimit): bool
    {
        return (
            $this->priceTierFlag === true
            || $lowerLimit === 0
        );
    }

    /** @var mixed[] $singlePriceTier*/
    protected function addPriceTiersBasedOnUserData(Product $product, array $singlePriceTier, ?ProductOptionInventory $productOptionInventory): void
    {
        $priceTier = new PriceTier($product, $productOptionInventory);
        $priceTier->setLowerLimitAndPrice(Money::fromVariable($singlePriceTier['price']), $singlePriceTier['lowerLimit']);
    }

    protected function addSinglePriceTier(Product $product, Money $price, ?ProductOptionInventory $productOptionInventory): void
    {
        $priceTier = new PriceTier($product, $productOptionInventory);
        $priceTier->setLowerLimitAndPrice($price);
    }

    protected function getProductOptionInventory(?string $combination, Product $product): ?ProductOptionInventory
    {
        if (\is_string($combination)
            && mb_strlen($combination) > 0
        ) {
            return $this->productOptionInventoryRepository->findOneByCombination(
                $combination,
                $product->getId()
            );
        }

        return null;
    }

    /**
     * @param Taxes[] $taxesOrigin
     * @param Pricetier[] $pricetiers
     *
     * @return array[]
     */
    protected function applyTaxesAndExposePt(array $taxesOrigin, array $pricetiers): array
    {
        $x = 0;
        $arrayWritter = $taxes = [];
        $tax = null;

        foreach ($taxesOrigin as $monoTax) {
            $tax = new Tax($monoTax['tax_id']);
            $taxes[] = $tax;
        }

        $taxIsPriceIncluded = true === $tax instanceof Tax ? $tax->isPriceIncluded() : true;

        foreach ($pricetiers as &$singlePriceTier) {
            $taxesForSinglePriceTier = Tax::applyTaxes($singlePriceTier->getPrice(), $taxes);
            $singlePriceTier->setTaxes($taxesForSinglePriceTier['tax']);
            $singlePriceTier->setPriceIncludeTax($taxIsPriceIncluded);

            $arrayWritter[$x++] = $singlePriceTier->expose();
        }

        return $arrayWritter;
    }

    /** @return array[] */
    protected function buildAndExposePriceTiersWithTaxes(WizachaProduct $product, array $priceTiers): array
    {
        $taxes = \array_map(function (Tax $tax): array {
            return fn_get_tax($tax->getId());
        }, $product->getTaxes());

        return $this->applyTaxesAndExposePt($taxes, $priceTiers);
    }
}
