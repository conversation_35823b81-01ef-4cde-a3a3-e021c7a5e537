<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright Copyright (c) Wizaplace
 * @license   Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\PriceTier\Validator;

use Wizacha\Marketplace\PIM\Product\Product;
use Wizacha\Marketplace\PriceTier\Exception\PriceTierException;

class PriceTierValidator
{
    /** @var array lowerLimits */
    protected $lowerLimits;

    /** @var mixed */
    protected $error;

    /** @var string invalidPriceTiersIntro */
    protected $invalidPriceTiersIntro;

    public function __construct()
    {
        $this->lowerLimits = [];
        $this->error = null;
        $this->invalidPriceTiersIntro = "The product has been created/updated. But the price tiers informations sent are invalid : ";
    }

    public function validateLowerLimit(int $lowerLimit, Product $product): ?PriceTierException
    {
        if (\count($this->lowerLimits) > 0) {
            $lastLowerLimit = end($this->lowerLimits);

            if ($lastLowerLimit >= $lowerLimit) {
                // The priceTiers are broken, we need to clean them (And we will create a basic PriceTier)
                $this->error = new PriceTierException($this->invalidPriceTiersIntro . "All lower limits must be greater than the last (current: $lowerLimit, last: $lastLowerLimit). Product id = " . $product->getId());
            }
        } elseif (\count($this->lowerLimits) === 0 && 0 !== $lowerLimit) {
            $this->error = new PriceTierException($this->invalidPriceTiersIntro . "The first lower limit must be equal to 0. Product id = " . $product->getId());
        }

        $this->lowerLimits[] = $lowerLimit;

        return $this->error;
    }

    public function validatePrice($price, Product $product): ?PriceTierException
    {
        if (\is_float($price) === false && \is_int($price) === false) {
            $this->error = new PriceTierException($this->invalidPriceTiersIntro . "One of the price fields has invalid content. Product id = " . $product->getId());
        }

        return $this->error;
    }

    /**
     * @param mixed[] $priceTiers
     */
    public function validateNumberOfPriceTiers(array $priceTiers): void
    {
        if (\count($priceTiers) > 10) {
            $this->error = new PriceTierException($this->invalidPriceTiersIntro . "You cannot have more than 10 price tiers for a single product or declination.");
        }
    }

    public function getError(): ?PriceTierException
    {
        return $this->error;
    }

    public function cleanLowerLimit(): void
    {
        $this->lowerLimits = [];
        $this->error = null;
    }
}
