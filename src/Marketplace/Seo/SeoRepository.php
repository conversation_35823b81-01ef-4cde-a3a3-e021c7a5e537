<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Seo;

use Doctrine\DBAL\Connection;

class SeoRepository
{
    private Connection $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
    }

    public function getSeoName(int $objectId): ?string
    {
        $query = "SELECT name
                FROM cscart_seo_names
                WHERE type = :type
                AND object_id = :object_id
                LIMIT 1;"
        ;

        $statement = $this->getConnection()->prepare($query);
        $statement->execute([
            'type' => SlugTargetType::CATEGORY()->getValue(),
            'object_id' => $objectId,
        ]);

        $seoName = $statement->fetchOne();

        if ($seoName === false) {
            return null;
        }

        return $seoName;
    }

    protected function getConnection(): Connection
    {
        return $this->connection;
    }
}
