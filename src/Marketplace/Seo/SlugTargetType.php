<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Seo;

use MyCLabs\Enum\Enum;

/**
 * @method static SlugTargetType PRODUCT()
 * @method static SlugTargetType CATEGORY()
 * @method static SlugTargetType ATTRIBUTE_VARIANT()
 * @method static SlugTargetType COMPANY()
 * @method static SlugTargetType CMS_PAGE()
 * @method static SlugTargetType REDIRECT()
 * @method static SlugTargetType NEWS()
 */
class SlugTargetType extends Enum
{
    public const PRODUCT = 'p';
    public const CATEGORY = 'c';
    public const ATTRIBUTE_VARIANT = 'e';
    public const COMPANY = 'm';
    public const CMS_PAGE = 'a';
    public const REDIRECT = 's';
}
