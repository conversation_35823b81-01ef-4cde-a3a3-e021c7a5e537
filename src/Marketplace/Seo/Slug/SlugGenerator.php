<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Seo\Slug;

use Cocur\Slugify\Slugify;

/**
 * Generate url-safe names, aka "slugs".
 */
class SlugGenerator
{
    public const DELIMITER = '-';

    private static $slugify = null;

    public function getSlugify()
    {
        if (\is_null(self::$slugify)) {
            /*
             * La derniere régle s'applique si une régle est présente deux fois. Normalement, les ruleset "additionnels
             * contiennent seulement des ajouts au ruleset par défaut donc on pourrait charger dans n'importe quel ordre.
             * Par contre, si un jour on veut prendre en charge d'autres langues, on risque de se retrouver avec des
             * collisions.
             */
            self::$slugify = new Slugify(['rulesets' => ['german', 'default']]);
        }

        return self::$slugify;
    }

    /**
     * Generate url-safe names.
     *
     * Example:
     *
     * - Hello, World! => hello-world
     * - Русский код => russky-kod
     *
     * Imported mostly from legacy
     */
    public function slugFromString(string $string): string
    {
        $slug = $this->getSlugify()->slugify($string);

        return $slug;
    }

    /**
     * Check if a string could be a valid slug.
     */
    public function isSlug(string $string): bool
    {
        return preg_match('/^[a-z0-9-]+$/', $string) === 1;
    }
}
