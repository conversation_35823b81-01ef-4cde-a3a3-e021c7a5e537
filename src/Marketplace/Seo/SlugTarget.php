<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Seo;

/**
 * Identifies the target of a slug.
 */
class SlugTarget
{
    /**
     * @var SlugTargetType
     */
    private $targetType;

    /**
     * @var mixed
     */
    private $id;

    public function __construct(SlugTargetType $targetType, $id)
    {
        $this->targetType = $targetType;
        $this->id = $id;
    }

    public function getTargetType(): SlugTargetType
    {
        return $this->targetType;
    }

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    public function expose(): array
    {
        return [
            'type' => strtolower($this->getTargetType()->getKey()),
            'id' => $this->getId(),
        ];
    }
}
