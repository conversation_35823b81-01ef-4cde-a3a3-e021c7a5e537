<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Seo;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Driver\PDOStatement;
use Doctrine\DBAL\Driver\Result;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Routing\Exception\MethodNotAllowedException;
use Symfony\Component\Routing\Exception\ResourceNotFoundException;
use Symfony\Component\Routing\RouterInterface;
use Wizacha\Category;
use Wizacha\Core\Iterator\PdoIterator;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\PIM\Attribute\AttributeService;
use Wizacha\Marketplace\Seo\Slug\SlugGenerator;
use Wizacha\Product;
use Wizacha\Status;
use Doctrine\DBAL\ParameterType;
use Wizacha\Marketplace\Cms\PageType;

/**
 * Le service SEO gère les URL "SEO-friendly".
 *
 * Il est principalement responsable de générer et enregistrer les "slugs" en BDD pour pouvoir
 * les résoudre ensuite. Par exemple si on crée un produit "iPhone 7", on appelle ce service
 * pour enregistrer un slug: le slug généré sera probablement "iphone-7".
 *
 * Plus tard lorsqu'on visite l'URL du produit (contenant "iphone-7") on demande à ce service
 * de résoudre le slug pour savoir quelle page on veut afficher.
 */
class SeoService
{
    public const SLUG_DELIMITER = '-';
    /**
     * 250 est la taille de la colonne VARCHAR en BDD. Dans le futur il serait probablement judicieux
     * de diminuer la taille des slugs parce que les URLs avec 250 caractères ne ressemblent à rien.
     */
    public const MAX_SLUG_LENGTH = 250;

    /**
     * 65.536 limit for the insane unpaginate
     * seo/slug/catalog api controller
     */
    private const LIMIT = 2 ** 16;

    public const SEO_NAME_PATTERN = '/[A-Za-z0-9]/';

    /**
     * @var Connection
     */
    private $db;

    /**
     * @var SlugGenerator
     */
    private $slugGenerator;

    /**
     * @var RouterInterface
     */
    private $router;

    private $categorySlugCache = [];

    private SeoRepository $seoRepository;
    private EventDispatcherInterface  $eventDispatcher;

    public function __construct(Connection $db, SlugGenerator $slugGenerator, RouterInterface $router, SeoRepository $seoRepository, EventDispatcherInterface $eventDispatcher)
    {
        $this->db = $db;
        $this->slugGenerator = $slugGenerator;
        $this->router = $router;
        $this->seoRepository = $seoRepository;
        $this->eventDispatcher = $eventDispatcher;
    }

    /**
     * Retrieve the object (object type + object ID) matching the given slug.
     */
    public function resolveSlug(string $slug): ?SlugTarget
    {
        $sql = 'SELECT object_id, type FROM cscart_seo_names WHERE name = :slug';
        $row = $this->db->fetchAssoc($sql, [
            'slug' => $slug,
        ]);

        if (!$row) {
            return null;
        }

        return new SlugTarget(new SlugTargetType($row['type']), $row['object_id']);
    }

    /**
     * Faster offset/limit pagination
     */
    private function getPaginatedSlugNames(
        int $offset,
        int $limit
    ): array {
        $query = ('SELECT
                `name`
            FROM
                cscart_seo_names
            ORDER BY
                `type`,`name`
            LIMIT :offset, :limit
            ');
        $statement = $this->db->prepare($query);
        $statement->bindParam('offset', $offset, \PDO::PARAM_INT);
        $statement->bindParam('limit', $limit, \PDO::PARAM_INT);

        $statement->execute();

        return $statement->fetchAll(\PDO::FETCH_COLUMN);
    }

    /**
     * List the whole slugs' catalog.
     * Generated arrays contain 'name', 'object_id', 'type', 'category_id_path'.
     * @return iterable|(array[])
     */
    public function listValidSlugs(
        int $offset = 0,
        int $limit = 100
    ) {
        $mainQuery = ("SELECT
                seo.name,
                seo.object_id,
                seo.type,
                c.id_path as category_id_path,

                # flag as enabled
                (
                    (
                        seo.type = {$this->db->quote(SlugTargetType::PRODUCT)}
                        AND pcat.id IS NOT NULL
                        AND (
                            p.status IS NULL
                            OR p.status = 'A'
                        )
                    )
                    OR (
                        seo.type = {$this->db->quote(SlugTargetType::CATEGORY)}
                        AND c.status = 'A'
                    )
                    OR (
                        seo.type = {$this->db->quote(SlugTargetType::CMS_PAGE)}
                        AND cms.page_id IS NOT NULL
                    )
                    OR (
                        seo.type = {$this->db->quote(SlugTargetType::ATTRIBUTE_VARIANT)}
                        AND v.variant_id IS NOT NULL
                    )
                    OR (
                        seo.type = {$this->db->quote(SlugTargetType::COMPANY)}
                        AND comp.company_id IS NOT NULL
                        AND comp.status = {$this->db->quote(Status::ENABLED)}
                    )
                ) as enabled
            FROM
                cscart_seo_names seo

                # FP status
                LEFT JOIN cscart_products p
                    ON seo.type = {$this->db->quote(SlugTargetType::PRODUCT)}
                    AND seo.object_id = p.product_id


                # FP/FPU available in readmodel catalog
                LEFT JOIN product_catalog pcat
                    ON seo.type = {$this->db->quote(SlugTargetType::PRODUCT)}
                    AND seo.object_id = pcat.id

                # FPU category path
                LEFT JOIN doctrine_multi_vendor_product mvp
                    ON seo.type = {$this->db->quote(SlugTargetType::PRODUCT)}
                    AND seo.object_id = mvp.id

                # FP category path
                LEFT JOIN cscart_products_categories pc
                    ON seo.type = {$this->db->quote(SlugTargetType::PRODUCT)}
                    AND seo.object_id = pc.product_id

                # Category path
                LEFT JOIN cscart_categories c
                    ON c.category_id = IF(
                        seo.type = {$this->db->quote(SlugTargetType::CATEGORY)},
                        seo.object_id,
                        IFNULL(mvp.category_id, pc.category_id)
                    )
                # Attributes
                LEFT JOIN cscart_product_feature_variants v
                    ON seo.type = {$this->db->quote(SlugTargetType::ATTRIBUTE_VARIANT)}
                    AND seo.object_id = v.variant_id

                # Companies
                LEFT JOIN cscart_companies comp
                    ON seo.type = {$this->db->quote(SlugTargetType::COMPANY)}
                    AND seo.object_id = comp.company_id

                # CMS
                LEFT JOIN cscart_pages cms
                    ON seo.type = {$this->db->quote(SlugTargetType::CMS_PAGE)}
                    AND seo.object_id = cms.page_id
                    AND cms.page_type = {$this->db->quote(PageType::TEMPLATE)}
                    AND cms.status = {$this->db->quote(Status::ENABLED)}
                    AND (
                        cms.use_avail_period = 'N'
                        OR (
                            cms.use_avail_period = 'Y'
                            AND NOW() BETWEEN
                                FROM_UNIXTIME(cms.avail_from_timestamp)
                                AND FROM_UNIXTIME(cms.avail_till_timestamp)
                        )
                    )
            ");

        $whereQuery = '';
        $parameters = [];

        /**
         * No limit for the legacy api /seo/catalog ... YOLO
         */
        if ($limit > 0) {
            $names = $this->getPaginatedSlugNames($offset, $limit);
            $whereQuery = sprintf(
                ' WHERE seo.name IN(%s) ',
                \str_repeat(
                    '?, ',
                    \count($names) - 1
                ) . '?'
            );
            $parameters = \array_merge(
                $parameters,
                $names
            );
        }

        $fullQuery = \sprintf(
            'SELECT
                a.*
            FROM (
                    %s %s
                    ORDER BY seo.type, seo.name
                    LIMIT %d
                ) a
            GROUP BY a.name
            ',
            $mainQuery,
            $whereQuery,
            static::LIMIT
        );

        /** @var PDOStatement */
        $pdoStatement = $this->db->getWrappedConnection()->prepare($fullQuery);
        $pdoStatement->execute(
            $parameters
        );

        return new PdoIterator($pdoStatement, \PDO::FETCH_ASSOC);
    }

    public function countListValidSlugs(): int
    {
        return (int) $this
            ->db
            ->query(
                'SELECT
                    count(*) as total
                FROM
                    cscart_seo_names
                '
            )
            ->fetchColumn();
    }

    /*
     * Get cached or query category slug
     */
    public function getCategorySlug(int $categoryId): array
    {
        $this->categorySlugCache[$categoryId]
            = $this->categorySlugCache[$categoryId]
            ?? $this->processCategorySlug($categoryId);

        return $this->categorySlugCache[$categoryId];
    }

    public function getSeoName(int $objectId): ?string
    {
        return $this->seoRepository->getSeoName($objectId);
    }

    private function getSlugFromTypeAndId(SlugTargetType $type, string $objectId): ?string
    {
        $qb = $this->db->createQueryBuilder();
        $qb
            ->select('seo.name')
            ->from('cscart_seo_names', 'seo')
            ->where(
                'seo.type = ' . $qb->createNamedParameter(
                    $type->getValue()
                )
                . ' AND seo.object_id = ' . $qb->createNamedParameter(
                    $objectId,
                    ParameterType::STRING
                )
            );
        $statement = $qb->execute();

        return \is_a($statement, Result::class)
            ? ($statement->fetchOne() ?: null)
            : null;
    }
    // query category slug
    public function processCategorySlug(int $categoryId): array
    {
        $qb = $this->db->createQueryBuilder();
        $qb
            ->select('seo.name')
            ->from('cscart_seo_names', 'seo')
            ->innerJoin(
                'seo',
                'cscart_categories',
                'categories',
                ('seo.type = ' . $qb->createNamedParameter(
                    SlugTargetType::CATEGORY()->getValue()
                )
                    . ' AND CAST(seo.object_id AS UNSIGNED) = categories.category_id'
                    . ' AND categories.status = ' . $qb->createNamedParameter(
                        Status::ENABLED
                    ))
            )
            ->where(
                'seo.type = ' . $qb->createNamedParameter(
                    SlugTargetType::CATEGORY()->getValue()
                )
                    . ' AND seo.object_id = ' . $qb->createNamedParameter(
                        $categoryId,
                        ParameterType::INTEGER
                    )
            );

        return $qb->execute()->fetch() ?: [];
    }

    /**
     * Retrieve the objects (object type + object ID) matching the given slugs.
     *
     * Watch out: if some slugs are not found, they will not be returned in the resulting array.
     *
     * @param string[] $slugs
     * @return SlugTarget[] Slug targets indexed by slugs.
     */
    public function resolveSlugs(array $slugs): array
    {
        $sql = 'SELECT name, object_id, type FROM cscart_seo_names WHERE name IN (:slugs)';
        $rows = $this->db->fetchAll(
            $sql,
            [
                'slugs' => $slugs,
            ],
            [
                'slugs' => Connection::PARAM_STR_ARRAY,
            ]
        );

        $targets = [];
        foreach ($rows as $row) {
            $targets[$row['name']] = new SlugTarget(new SlugTargetType($row['type']), $row['object_id']);
        }

        return $targets;
    }

    /**
     * Given an object (object type + object ID), generate a slug from the object name (e.g. product name)
     * and save it in database.
     *
     * @param string|int $targetId
     */
    public function registerSlug(SlugTargetType $targetType, $targetId, string $nameToSlugify): string
    {
        $oldSlug = null;
        if ($targetType->equals(SlugTargetType::ATTRIBUTE_VARIANT())) {
            $oldSlug = $this->getSlugFromTypeAndId(SlugTargetType::ATTRIBUTE_VARIANT(), \strval($targetId));
            if ($oldSlug === $nameToSlugify) {
                return $oldSlug;
            }
        }

        $newSlug = $this->registerSlugLegacy($targetId, $targetType->getValue(), $nameToSlugify);

        if ($targetType->equals(SlugTargetType::ATTRIBUTE_VARIANT()) && \is_null($oldSlug) === false) {
            $this->eventDispatcher->dispatch(
                (new \Wizacha\Events\IterableEvent())->setElement($targetId),
                AttributeService::EVENT_UPDATE_VARIANT
            );
        }

        return $newSlug;
    }

    public function removeSlug(SlugTargetType $targetType, $targetId): void
    {
        $stmt = $this->db->prepare('DELETE FROM cscart_seo_names WHERE object_id = :objectId AND type = :type');
        $stmt->bindValue('objectId', $targetId, \PDO::PARAM_STR);
        $stmt->bindValue('type', $targetType->getValue());

        if (!$stmt->execute()) {
            throw new \Exception('failed to delete slug for ' . $targetType->getKey() . ' objet #' . $targetId);
        }
    }

    /**
     * @param mixed $objectId
     * @param string $objectType
     * @param string $objectName
     * @param int $index DEPRECATED
     * @param string $dispatch DEPRECATED
     * @deprecated Used by CS Cart with a lot of (soon-to-be useless) parameters.
     * @see \Wizacha\Marketplace\Seo\SeoService::registerSlug()
     */
    public function registerSlugLegacy($objectId, $objectType, string $objectName = null, int $index = 0, string $dispatch = ''): string
    {
        $slug = $this->slugGenerator->slugFromString((string) $objectName);
        $slug = substr($slug, 0, self::MAX_SLUG_LENGTH);

        if (empty($slug)) {
            $slug = fn_get_seo_vars($objectType)['description'] . '-' . (empty($objectId) ? $dispatch : $objectId);
        }

        $sql = 'SELECT name FROM cscart_seo_names WHERE name = :slug
                AND (object_id != :objectId OR type != :type OR dispatch != :dispatch)';
        $slugAlreadyTaken = $this->db->fetchColumn(
            $sql,
            [
                'slug' => $slug,
                'objectId' => $objectId,
                'type' => $objectType,
                'dispatch' => $dispatch,
            ],
            0,
            [
                'objectId' => \PDO::PARAM_STR,
            ]
        );

        if (false === $slugAlreadyTaken) {
            $slashSlug = '/' . $slug;
            $slashSlugHtml = $slashSlug . '.html';

            $slugAlreadyTaken = $this->pathExists($slashSlug)
                || $this->pathExists($slashSlugHtml);
        }

        if ($slugAlreadyTaken) {
            // Try creating a slug with a suffix (slug-2, slug-3, ...)

            //Hotfix use old system when an index is set and seo_name exist
            if ($index) {
                $index++;
            } else {
                /** @var int $index */
                $index = \Wizacha\Misc::getSEOIndex($objectId, $objectType, $slug, $dispatch, (string) GlobalState::interfaceLocale()) ?: 2;
            }

            if ($index > 1) {
                // I wonder if this line is absolutely necessary
                $tmp = preg_replace("/-\d+$/", "", $objectName);
                // Make sure we have enough room to add the suffix and stay under the limit
                $tmp = substr($tmp, 0, self::MAX_SLUG_LENGTH - (\strlen(SlugGenerator::DELIMITER) + \strlen((string) $index)));
                // Append number suffix to the name (e.g. "-2")
                $objectName = $tmp . SlugGenerator::DELIMITER . $index;
            }

            return $this->registerSlugLegacy($objectId, $objectType, $objectName, $index, $dispatch);
        }

        $this->db
            ->prepare('INSERT INTO `cscart_seo_names` SET `name` = :name, `type` = :type, `object_id` = :object_id, `dispatch` = :dispatch ON DUPLICATE KEY UPDATE `name` = :name')
            ->execute([
                'name' => $slug,
                'type' => $objectType,
                'object_id' => (string) $objectId,
                'dispatch' => $dispatch,
            ]);

        return $slug;
    }

    public function refreshNames(): self
    {
        foreach (Category::allIds() as $categoryId) {
            Category::frontUrl($categoryId);
        }

        foreach (Product::allFrontIds() as $productId) {
            Product::frontUrl($productId);
        }

        return $this;
    }

    private function pathExists(string $pathinfo): bool
    {
        try {
            $this->router->match($pathinfo);
        } catch (MethodNotAllowedException $e) {
            return true;
        } catch (ResourceNotFoundException $e) {
            return false;
        }

        return true;
    }

    public function isSeoNameValid(string $seoName): bool
    {
        return preg_match(self::SEO_NAME_PATTERN, $seoName) === 1;
    }
}
