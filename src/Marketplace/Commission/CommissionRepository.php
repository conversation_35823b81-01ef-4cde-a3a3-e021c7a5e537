<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Commission;

use Broadway\UuidGenerator\UuidGeneratorInterface;
use Doctrine\ORM\EntityManagerInterface;
use Wizacha\Marketplace\Exception\NotFound;

class CommissionRepository
{
    /**
     * @var EntityManagerInterface
     */
    private $entityManager;

    /**
     * @var UuidGeneratorInterface
     */
    private $idGenerator;

    public function __construct(EntityManagerInterface $entityManager, UuidGeneratorInterface $idGenerator)
    {
        $this->entityManager = $entityManager;
        $this->idGenerator = $idGenerator;
    }

    public function get(string $id): Commission
    {
        $commission = $this->entityManager->getRepository(Commission::class)->find($id);

        if ($commission instanceof Commission) {
            return $commission;
        }

        throw NotFound::fromId('Commission', $id);
    }

    /** @return Commission[] */
    public function findAllCommissions(): array
    {
        return $this->entityManager->getRepository(Commission::class)->findAll();
    }

    /** @return Commission[] */
    public function findAllCategoryCommissions(): array
    {
        return $this
            ->entityManager
            ->createQueryBuilder()
            ->select('commission')
            ->from(Commission::class, 'commission')
            ->where('commission.commissionType = :type')
            ->setParameter('type', CommissionType::COMMISSION_CATEGORY())
            ->getQuery()
            ->getResult()
        ;
    }

    /** @return Commission[] */
    public function findAllMarketplaceCommissions(): array
    {
        return $this
            ->entityManager
            ->createQueryBuilder()
            ->select('commission')
            ->from(Commission::class, 'commission')
            ->where('commission.commissionType IN(:type)')
            ->setParameter('type', [CommissionType::COMMISSION_CATEGORY(), CommissionType::COMMISSION_MARKETPLACE()])
            ->getQuery()
            ->getResult()
        ;
    }

    public function findByCompany(int $companyId): array
    {
        return $this->entityManager->getRepository(Commission::class)->findByCompanyId($companyId);
    }

    /** @return Commission[] */
    public function findCommissionsByCategory(int $categoryId): array
    {
        return $this
            ->entityManager
            ->createQueryBuilder()
            ->select('commission')
            ->from(Commission::class, 'commission')
            ->where('commission.categoryId = :categoryId')
            ->setParameter('categoryId', $categoryId)
            ->getQuery()
            ->getResult()
        ;
    }

    /** @return Commission[] */
    public function findCategoryCommissionsByCompany(int $companyId): array
    {
        return $this
            ->entityManager
            ->createQueryBuilder()
            ->select('commission')
            ->from(Commission::class, 'commission')
            ->where('commission.companyId = :companyId')
            ->andWhere('commission.categoryId is not null')
            ->setParameter('companyId', $companyId)
            ->getQuery()
            ->getResult()
        ;
    }

    /** @return Commission[] */
    public function findDefaultCategoryCommissions(): array
    {
        return $this
            ->entityManager
            ->createQueryBuilder()
            ->select('commission')
            ->from(Commission::class, 'commission')
            ->where('commission.commissionType = :type')
            ->setParameter('type', CommissionType::COMMISSION_CATEGORY())
            ->getQuery()
            ->getResult()
        ;
    }

    public function findOneByCategoryAndCompany(?int $categoryId, ?int $companyId): ?Commission
    {
        $queryBuilder = $this
            ->entityManager
            ->createQueryBuilder()
            ->select('commission')
            ->from(Commission::class, 'commission')
        ;

        if ($categoryId === null) {
            $queryBuilder->where('commission.categoryId IS NULL');
        } else {
            $queryBuilder
                ->where('commission.categoryId = :categoryId')
                ->setParameter('categoryId', $categoryId)
            ;
        }

        if ($companyId === null) {
            $queryBuilder->andWhere('commission.companyId IS NULL');
        } else {
            $queryBuilder
                ->andWhere('commission.companyId = :companyId')
                ->setParameter('companyId', $companyId)
            ;
        }

        return $queryBuilder->getQuery()->getOneOrNullResult();
    }

    public function findDefaultCommission(): ?Commission
    {
        return $this
            ->entityManager
            ->createQueryBuilder()
            ->select('commission')
            ->from(Commission::class, 'commission')
            ->where('commission.commissionType = :type')
            ->setParameter('type', CommissionType::COMMISSION_MARKETPLACE())
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }

    public function findCompanyCommission(int $companyId): ?Commission
    {
        return $this
            ->entityManager
            ->createQueryBuilder()
            ->select('commission')
            ->from(Commission::class, 'commission')
            ->where('commission.companyId = :companyId')
            ->andWhere('commission.commissionType = :type')
            ->setParameters(['companyId' => $companyId, 'type' => CommissionType::COMMISSION_COMPANY()])
            ->getQuery()
            ->getOneOrNullResult()
        ;
    }

    /** @return Commission[] */
    public function findAllCompanyCommissions(): array
    {
        return $this
            ->entityManager
            ->createQueryBuilder()
            ->select('commission')
            ->from(Commission::class, 'commission')
            ->where('commission.commissionType = :type')
            ->setParameter('type', CommissionType::COMMISSION_COMPANY())
            ->getQuery()
            ->getResult()
        ;
    }

    /** @return Commission[] */
    public function findAllCategoryCommissionsByCompany(): array
    {
        return $this
            ->entityManager
            ->createQueryBuilder()
            ->select('commission')
            ->from(Commission::class, 'commission')
            ->where('commission.commissionType = :type')
            ->setParameter('type', CommissionType::COMMISSION_CATEGORY_COMPANY())
            ->getQuery()
            ->getResult()
        ;
    }

    public function delete(string $id): void
    {
        $commission = $this->get($id);

        if ($commission instanceof Commission) {
            $this->entityManager->remove($commission);
            $this->entityManager->flush();
        }
    }

    public function deleteCommissionsByCompanyId(int $companyId): void
    {
        $queryBuilder = $this
            ->entityManager
            ->createQueryBuilder();

        $queryBuilder
            ->delete(Commission::class, 'commission')
            ->where($queryBuilder->expr()->eq('commission.companyId', ':company_id'))
            ->setParameters(
                [
                    'company_id' => $companyId,
                ]
            )
            ->getQuery()
            ->execute()
        ;
    }

    public function save(Commission $commission): string
    {
        if (empty($commission->getId())) {
            $commission->setId($this->idGenerator->generate());
        }
        if ($commission->getCompanyId() === null && $commission->getCategoryId() === null) {
            $commission->setCommissionType(CommissionType::COMMISSION_MARKETPLACE());
        } elseif ($commission->getCompanyId() === null && \is_int($commission->getCategoryId())) {
            $commission->setCommissionType(CommissionType::COMMISSION_CATEGORY());
        } elseif (\is_int($commission->getCompanyId()) && $commission->getCategoryId() === null) {
            $commission->setCommissionType(CommissionType::COMMISSION_COMPANY());
        } else {
            $commission->setCommissionType(CommissionType::COMMISSION_CATEGORY_COMPANY());
        }

        $this->entityManager->persist($commission);
        $this->entityManager->flush();

        return $commission->getId();
    }
}
