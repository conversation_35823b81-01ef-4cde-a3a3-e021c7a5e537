<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Commission;

use MyCLabs\Enum\Enum;

/**
 * @method static CommissionType COMMISSION_MARKETPLACE()
 * @method static CommissionType COMMISSION_CATEGORY()
 * @method static CommissionType COMMISSION_COMPANY()
 * @method static CommissionType COMMISSION_CATEGORY_COMPANY()
 */
final class CommissionType extends Enum
{
    private const COMMISSION_MARKETPLACE = 'marketplace';
    private const COMMISSION_CATEGORY = 'category';
    private const COMMISSION_COMPANY = 'company';
    private const COMMISSION_CATEGORY_COMPANY = 'category_company';
}
