<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Commission;

use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Connection;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Wizacha\AppBundle\Service\SettingStorage;
use Wizacha\Events\IterableEvent;
use Wizacha\Marketplace\Commission\Exception\CommissionAlreadyExists;
use Wizacha\Marketplace\Commission\Exception\CommissionByCategoryException;
use Wizacha\Marketplace\Commission\Exception\CommissionNotFound;
use Wizacha\Marketplace\Company\CompanyEvents;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts;
use Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmountsCommission;
use Wizacha\Marketplace\Order\AmountsCalculator\Repository\OrderAmountsCommissionRepository;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\PIM\Category\CategoryService;
use Wizacha\Money\Money;
use Wizacha\Marketplace\Order\Order;

class CommissionService implements EventSubscriberInterface
{
    /**
     * @var CommissionRepository
     */
    private $commissionRepository;

    /**
     * @var Connection
     */
    private $dbConnection;

    /**
     * @var bool
     */
    protected $includeShippingsInCommission;

    /**
     * @var bool
     */
    protected $addFullShippingsToCommission;

    /**
     * @var SettingStorage
     */
    private $settingStorage;

    /** @var CategoryService */
    private $categoryService;

    /** @var LoggerInterface */
    protected $logger;

    protected OrderService $orderService;

    private OrderAmountsCommissionRepository $orderAmountsCommissionRepository;

    public function __construct(
        CommissionRepository $commissionRepository,
        Connection $dbConnection,
        SettingStorage $settingStorage,
        bool $includeShippingsInCommission,
        bool $addFullShippingsToCommission,
        CategoryService $categoryService,
        LoggerInterface $logger,
        OrderService $orderService,
        OrderAmountsCommissionRepository $orderAmountsCommissionRepository
    ) {
        $this->commissionRepository = $commissionRepository;
        $this->dbConnection = $dbConnection;
        // force includeShippingIncludeCommision to false if addFullShippingToCommission is true
        $this->includeShippingsInCommission = $addFullShippingsToCommission ? false : $includeShippingsInCommission;
        $this->addFullShippingsToCommission = $addFullShippingsToCommission;
        $this->settingStorage = $settingStorage;
        $this->categoryService = $categoryService;
        $this->logger = $logger;
        $this->orderService = $orderService;
        $this->orderAmountsCommissionRepository = $orderAmountsCommissionRepository;
    }

    public function getCommissionById(string $id): Commission
    {
        return $this->commissionRepository->get($id);
    }

    public function saveCommission(Commission $commission): string
    {
        return $this->commissionRepository->save($commission);
    }

    public function deleteCommission(string $id): void
    {
        $this->commissionRepository->delete($id);
    }

    public function deleteCommissionsByCompanyId(int $companyId): void
    {
        $this->commissionRepository->deleteCommissionsByCompanyId($companyId);
    }

    /** @param mixed[] $commissionData */
    public function addCommissionByCategory(array $commissionData): string
    {
        if (\intval($commissionData['category_id']) === 0) {
            throw CommissionByCategoryException::invalidCategoryId();
        }

        if ($this->getCommissionByCategoryAndCompany(\intval($commissionData['category_id']), null) instanceof Commission) {
            throw new CommissionAlreadyExists();
        }

        $newCommission = new Commission(
            '',
            null,
            \intval($commissionData['category_id']),
            $commissionData['percent_amount'] === "" ? 0 : \floatval($commissionData['percent_amount']),
            $commissionData['fixed_amount'] === "" ? 0 : \floatval($commissionData['fixed_amount']),
            $commissionData['maximum_amount'] === "" ? null : \floatval($commissionData['maximum_amount'])
        );

        return $this->saveCommission($newCommission);
    }

    /** @param mixed[] $commissionData */
    public function updateCommissionByCategory(array $commissionData): string
    {
        if (\intval($commissionData['category_id']) === 0) {
            throw CommissionByCategoryException::invalidCategoryId();
        }

        try {
            $commission = $this->getCommissionById($commissionData['id']);
        } catch (NotFound $exception) {
            throw CommissionNotFound::fromCommissionId($commissionData['id']);
        }
        $commission
            ->setCategoryId(\intval($commissionData['category_id']))
            ->setPercentAmount(
                $commissionData['percent_amount'] === ""
                    ? 0
                    : \floatval($commissionData['percent_amount'])
            )
            ->setFixAmount(
                $commissionData['fixed_amount'] === ""
                    ? 0
                    : \floatval($commissionData['fixed_amount'])
            )
            ->setMaximumAmount(
                $commissionData['maximum_amount'] === ""
                    ? null
                    : \floatval($commissionData['maximum_amount'])
            )
        ;

        return $this->saveCommission($commission);
    }

    /* Do NOT set $calculateCommission to true in a normal use */
    public function getMarketplaceCommission(Order $order, bool $calculateCommission = false): Money
    {
        if (($commissionAmount = $this->loadOrderCommission($order)) && $calculateCommission === false) {
            return $commissionAmount;
        }

        return $this->calculateCommissions($order);
    }

    public function getCompanyCommission(int $companyId): ?Commission
    {
        return $this->commissionRepository->findCompanyCommission($companyId);
    }

    /** @return Commission[] */
    public function getCategoryCommissionsByCompany(int $companyId): array
    {
        return $this->commissionRepository->findCategoryCommissionsByCompany($companyId);
    }

    /** @return Commission[] */
    public function getDefaultCategoryCommissions(): array
    {
        return $this->commissionRepository->findDefaultCategoryCommissions();
    }

    public function getApplicableCategoryCommisionForCompany(int $companyId): array
    {
        $defaultCategorCommissions = $this->getDefaultCategoryCommissions();
        $companyCategoryCommissions = $this->getCategoryCommissionsByCompany($companyId);

        // Pour pouvoir simplement faire un array_merge sur les deux tableaux on va les indexer par la categorie qui s'applique
        // (en rajoutant un prefixe pour qu'array_merge ne pense pas que ce soit simplement une liste

        $getArrayPrefix = fn($item) => 'c' . $item->getCategoryId();

        $defaultCategorCommissions = array_combine(
            array_map($getArrayPrefix, $defaultCategorCommissions),
            $defaultCategorCommissions
        );
        $companyCategoryCommissions = array_combine(
            array_map($getArrayPrefix, $companyCategoryCommissions),
            $companyCategoryCommissions
        );


        return  array_merge($defaultCategorCommissions, $companyCategoryCommissions);
    }


    /**
     * @return OrderAmountsCommission[]
     */
    public function getOrderAmountsCommissionByOrderId(int $orderId): array
    {
        return $this->orderAmountsCommissionRepository->findBy(['orderId' => $orderId]);
    }

    public function saveOrderAmountsCommission(Order $order): void
    {
        $appliedCommission = $this->getOrderAmountsCommissionByOrderId($order->getId());
        if (\count($appliedCommission) !== 0) {
            return;
        }
        $companyId = $order->getCompanyId();
        $companyCommission = $this->getCompanyCommission($companyId);
        $defaultCommission = $this->getDefaultCommission();
        $appliedCommission = $companyCommission instanceof Commission ? $companyCommission : $defaultCommission;

        // Get all commission applicables
        $comissions = $this->getApplicableCategoryCommisionForCompany($companyId);

        $comissions = array_map(function ($commission) use ($order) {
            return new OrderAmountsCommission($order->getId(), $commission);
        }, $comissions);

        // Init the commission amount with the vendor's fixed commission
        array_map(function ($commission) {
            $this->orderAmountsCommissionRepository->save($commission);
        }, $comissions);

        $this->orderAmountsCommissionRepository->save(new OrderAmountsCommission($order->getId(), $appliedCommission));
    }

    public function calculateCommissions(Order $order): Money
    {
        if ($order->isParentOrder()) {
            $totalCommission = new Money(0);
            $subOrders = $this->orderService->getOnlyChildOrders($order->getId());
            foreach ($subOrders as $subOrder) {
                $totalCommission = $totalCommission->add($this->getMarketplaceCommission($subOrder));
            }

            return $totalCommission;
        }

        if ($order->getTotal()->isZero()) {
            return new Money(0);
        }

        $appliedCommission = $this->getOrderAmountsCommissionByOrderId($order->getId());
        if (\count($appliedCommission) === 0) {
            $this->saveOrderAmountsCommission($order);
            $appliedCommission = $this->getOrderAmountsCommissionByOrderId($order->getId());
        }

        $defaultCommission = reset($appliedCommission);
        foreach ($appliedCommission as $commission) {
            if ($commission->getCommissionType() === CommissionType::COMMISSION_COMPANY()->getValue()
                || $commission->getCommissionType() === CommissionType::COMMISSION_MARKETPLACE()->getValue()
            ) {
                $defaultCommission = $commission;
            }
        }

        // Init the commission amount with the vendor's fixed commission
        $commissionAmount = Money::fromVariable($defaultCommission->getFixAmount());
        $commissionAmount = $commissionAmount->add($this->getCommissionAmount($order, $appliedCommission));

        if ($this->addFullShippingsToCommission) {
            $commissionAmount = $commissionAmount->add($order->getTotal()->subtract($order->getSubtotal()));
        }

        if ($commissionAmount->greaterThan($order->getBalanceTotal(false))) {
            $commissionAmount = $order->getBalanceTotal(false);
        }

        if (\is_float($defaultCommission->getMaximumAmount())) {
            $maximumAmount = Money::fromVariable($defaultCommission->getMaximumAmount());

            if ($commissionAmount->greaterThan($maximumAmount)) {
                $commissionAmount = $maximumAmount;
            }
        }

        return $commissionAmount;
    }

    public function getTotalCommission(Order $order): Money
    {
        return $this->getMarketplaceCommission($order);
    }

    public function getCommissionByCategoryAndCompany(?int $categoryId, ?int $companyId): ?Commission
    {
        return $this->commissionRepository->findOneByCategoryAndCompany($categoryId, $companyId);
    }

    /** @return Commission[] */
    public function getAllCommissionsByCategory(): array
    {
        return $this->commissionRepository->findAllCategoryCommissions();
    }

    public function getDefaultCommission(): Commission
    {
        $defaultCommission = $this->commissionRepository->findDefaultCommission();

        if (false === $defaultCommission instanceof Commission) {
            $defaultCommission = new Commission('', null, null, 0, 0, null);
            $defaultCommission->setCommissionType((string) CommissionType::COMMISSION_MARKETPLACE());
        }

        return $defaultCommission;
    }

    public function getDefaultCommissionFixed(): Money
    {
        return new Money((int) $this->settingStorage->get('default_commission_fixed', 0));
    }

    public function getDefaultCommissionPercent(): float
    {
        return (float) $this->settingStorage->get('default_commission_percent', 0);
    }

    public function getDefaultCommissionMaximum(): ?float
    {
        $maximum = $this->settingStorage->get('default_commission_maximum', null);

        return false === \is_null($maximum) ? (float) $maximum : null;
    }

    public function setDefaultCommission(Commission $commission): void
    {
        $defaultCommission = $this->getDefaultCommission();

        $defaultCommission
            ->setPercentAmount($commission->getPercentAmount())
            ->setFixAmount($commission->getFixAmount())
            ->setMaximumAmount($commission->getMaximumAmount());

        $this->commissionRepository->save($defaultCommission);
    }

    public function setDefaultCommissionFixed(Money $fixed)
    {
        $this->settingStorage->set('default_commission_fixed', $fixed->getAmount());
    }

    public function setDefaultCommissionPercent(float $percent)
    {
        $this->settingStorage->set('default_commission_percent', $percent);
    }

    public function setDefaultCommissionMaximum(float $maximum): void
    {
        $this->settingStorage->set('default_commission_maximum', $maximum);
    }

    public function applyCommissionToAllCompanies(Money $fixed, float $percent)
    {
        return $this->dbConnection->executeUpdate(
            'UPDATE cscart_companies
            SET commission_fixed = ?, commission_percent = ?;',
            [$fixed->getConvertedAmount(), $percent],
            [\PDO::PARAM_STR, \PDO::PARAM_STR]
        );
    }

    public function deleteAllCompanyCommissions(): void
    {
        $companyCommissions = $this->commissionRepository->findAllCompanyCommissions();
        $companyCategoryCommissions = $this->commissionRepository->findAllCategoryCommissionsByCompany();

        foreach ($companyCommissions as $companyCommission) {
            $this->commissionRepository->delete($companyCommission->getId());
        }

        foreach ($companyCategoryCommissions as $companyCategoryCommission) {
            $this->commissionRepository->delete($companyCategoryCommission->getId());
        }
    }

    public function applyToAllCompanies(Commission $commission): void
    {
        $companies = $this->dbConnection->executeQuery('SELECT company_id FROM cscart_companies')->fetchAll();

        foreach ($companies as $company) {
            $this->saveCommission(
                new Commission(
                    '',
                    $company['company_id'],
                    $commission->getCategoryId(),
                    $commission->getPercentAmount(),
                    $commission->getFixAmount(),
                    $commission->getMaximumAmount()
                )
            );
        }
    }

    /** @return Commission[] */
    public function getCategoriesCommissionsByOrder(Order $order): array
    {
        $commissions = [];
        $company = $order->getCompany();

        $companyCommission = $this->getCompanyCommission($company->getId());
        $categoryCommissions = $this->commissionRepository->findCategoryCommissionsByCompany($company->getId());
        $defaultCategoryCommissions = $this->commissionRepository->findDefaultCategoryCommissions();

        foreach ($order->getItems() as $item) {
            $itemData = $item->getAmountItem()->getOrderItemData();
            // If product was deleted before 9304, product's category was not frozen
            if (null === $itemData->getFullCategoryPath()) {
                continue;
            }

            $categoryCommission = $this->getCategoryCommissionFromProduct($itemData->getFullCategoryPath(), $categoryCommissions);

            if ($categoryCommission === null && $companyCommission instanceof Commission === false) {
                $categoryCommission = $this->getCategoryCommissionFromProduct($itemData->getFullCategoryPath(), $defaultCategoryCommissions);
            }

            if ($categoryCommission instanceof Commission) {
                $category = $this->categoryService->get($categoryCommission->getCategoryId());
                $commissions[$category->getName()] = $categoryCommission;
            }
        }

        return $commissions;
    }

    public function saveMarketPlaceCommission(Order $order): self
    {
        /** @var \PDOStatement $statement */
        $statement = $this->dbConnection->executeQuery(
            'SELECT 1 FROM cscart_vendor_payouts WHERE order_id = ?',
            [$order->getId()],
            [\PDO::PARAM_INT]
        );
        $recordExists = $statement->rowCount() != 0;

        $commissionAmount = $this->calculateCommissions($order);

        $data = [
            'company_id' => $order->getCompany()->getId(),
            'order_id' => $order->getId(),
            'payout_date' => TIME,
            'start_date' => TIME,
            'end_date' => TIME,
            'commission' => $commissionAmount->getPreciseConvertedAmount(),
            'order_amount' => $order->getTotal()->getPreciseConvertedAmount(),
            'commission_amount' => $commissionAmount->getPreciseConvertedAmount(),
        ];


        if (false === $recordExists) {
            $this->dbConnection->insert('cscart_vendor_payouts', $data);
        } else {
            $this->dbConnection->update('cscart_vendor_payouts', $data, ['order_id' => $order->getId()]);
        }

        return $this;
    }

    /** @return Commission[] */
    public function getAllCompanyCommissions(): array
    {
        return $this->commissionRepository->findAllCompanyCommissions();
    }

    /**
     * @param Commission[] $categoryCommissions
     */
    private function getCategoryCommissionFromProduct(string $fullCategoryPath, array $categoryCommissions): ?Commission
    {
        $categories = \array_reverse(\explode('/', $fullCategoryPath));
        $commissions = [];

        foreach ($categories as $category) {
            foreach ($categoryCommissions as $categoryCommission) {
                if ($categoryCommission->getCategoryId() === \intval($category)
                    && \array_key_exists($categoryCommission->getId(), $commissions) === false
                ) {
                    return $categoryCommission;
                }
            }
        }

        return null;
    }

    /**
     * @throws \Doctrine\DBAL\DBALException
     */
    private function loadPaymentCommission(Order $order): ?Money
    {
        $paymentCommissionAmount = $this->dbConnection->executeQuery(
            'SELECT w_commission
            FROM cscart_orders
            WHERE order_id = ?',
            [$order->getId()],
            [\PDO::PARAM_INT]
        )->fetchColumn();

        if ($paymentCommissionAmount !== false && $paymentCommissionAmount !== null) {
            return Money::fromVariable($paymentCommissionAmount);
        }

        return null;
    }

    /**
     * @throws \Doctrine\DBAL\DBALException
     */
    private function loadOrderCommission(Order $order): ?Money
    {
        $commissionAmount = $this->dbConnection->executeQuery(
            <<<SQL
            SELECT
                commission_amount
            FROM
                cscart_vendor_payouts
            WHERE
                order_id = ?
            SQL,
            [$order->getId()],
            [\PDO::PARAM_INT]
        )->fetchColumn();

        if ($commissionAmount !== false) {
            return Money::fromVariable($commissionAmount);
        }

        return null;
    }

    private function getCommissionFromSpecificCommission(float $productPrice, int $productAmount, OrderAmountsCommission $commission): Money
    {
        $commissionAmount = new Money(0);
        $commissionAmount = $commissionAmount->add(
            $this->calculateProductCommissionPercentAmount($commission->getPercentAmount(), $productPrice, $productAmount)
        );
        if ($commission->getCommissionType() === CommissionType::COMMISSION_CATEGORY()->getValue()
            || $commission->getCommissionType() === CommissionType::COMMISSION_CATEGORY_COMPANY()->getValue()
        ) {
            $commissionAmount = $commissionAmount->add(
                Money::fromVariable($commission->getFixAmount())->multiply($productAmount)
            );
        }

        if (\is_float($commission->getMaximumAmount())) {
            $maximumCommission = Money::fromVariable($commission->getMaximumAmount())->multiply($productAmount);

            if ($commissionAmount->greaterThan($maximumCommission)) {
                $commissionAmount = $maximumCommission;
            }
        }

        return $commissionAmount;
    }

    /** @param Commission[] $categoryCommissions */
    private function getCommissionsByCategoryAmount(float $productPrice, int $productAmount, string $productIdPath, array $categoryCommissions, OrderAmountsCommission $defaultCommission): Money
    {
        $isDefaultCommissionVendorType = $defaultCommission->getCommissionType() === CommissionType::COMMISSION_COMPANY()->getValue();

        $categories = array_reverse(explode('/', $productIdPath));

        foreach ($categories as $category) {
            foreach ($categoryCommissions as $categoryCommission) {
                if ($isDefaultCommissionVendorType
                    && $categoryCommission->getCommissionType() === CommissionType::COMMISSION_CATEGORY()->getValue()
                ) {
                    continue;
                }
                if ($categoryCommission->getCategoryId() === \intval($category)) {
                     return $this->getCommissionFromSpecificCommission($productPrice, $productAmount, $categoryCommission);
                }
            }
        }

        return $this->getCommissionFromSpecificCommission($productPrice, $productAmount, $defaultCommission);
    }

    private function getProductCommissionAmount(float $productPrice, int $productAmount, OrderAmountsCommission $appliedCommission): Money
    {
        $commissionAmount = $this->calculateProductCommissionPercentAmount($appliedCommission->getPercentAmount(), $productPrice, $productAmount);

        if (\is_float($appliedCommission->getMaximumAmount())) {
            $maximumAmount = Money::fromVariable($appliedCommission->getMaximumAmount())->multiply($productAmount);

            if ($commissionAmount->greaterThan($maximumAmount)) {
                $commissionAmount = $maximumAmount;
            }
        }

        return $commissionAmount;
    }

    /** @param Commission[] $categoriesCommissions */
    private function getCommissionAmountByProducts(
        Order $order,
        array $categoriesCommissions,
        OrderAmountsCommission $appliedCommission
    ): Money {
        $commissionAmount = new Money(0);
        $defaultCategoriesCommissions = $categoriesCommissions;

        foreach ($order->getItems() as $item) {
            if (null === $item->getAmountItem()) {
                continue;
            }
            $itemData = $item->getAmountItem()->getOrderItemData();

            $categoryCommissionAmount = null !== $itemData->getFullCategoryPath()
                ? $this->getCommissionsByCategoryAmount(
                    $item->getDiscountedUnitPrice()->getPreciseConvertedAmount(),
                    $item->getQuantity(),
                    $itemData->getFullCategoryPath(),
                    $categoriesCommissions,
                    $appliedCommission
                )
                : new Money(0)
            ;

            if ($categoryCommissionAmount->getConvertedAmount() > 0) {
                $commissionAmount = $commissionAmount->add($categoryCommissionAmount);
            } elseif (null !== $appliedCommission
                && CommissionType::COMMISSION_COMPANY()->getValue() === $appliedCommission->getCommissionType()
            ) {
                $companyCommissionAmount = $this->getProductCommissionAmount(
                    $item->getDiscountedUnitPrice()->getPreciseConvertedAmount(),
                    $item->getQuantity(),
                    $appliedCommission
                );
                $commissionAmount = $commissionAmount->add($companyCommissionAmount);
            } else {
                $categoryCommissionAmount = $itemData->getFullCategoryPath()
                    ? $this->getCommissionsByCategoryAmount(
                        $item->getDiscountedUnitPrice()->getPreciseConvertedAmount(),
                        $item->getQuantity(),
                        $itemData->getFullCategoryPath(),
                        $defaultCategoriesCommissions,
                        $appliedCommission
                    )
                    : new Money(0)
                ;

                if ($categoryCommissionAmount->getConvertedAmount() > 0) {
                    $commissionAmount = $commissionAmount->add($categoryCommissionAmount);
                } else {
                    $commissionAmount = $commissionAmount->add(
                        $this->calculateProductCommissionPercentAmount(
                            $appliedCommission->getPercentAmount(),
                            $item->getDiscountedUnitPrice()->getPreciseConvertedAmount(),
                            $item->getQuantity()
                        )
                    );
                }
            }
        }

        return $commissionAmount;
    }

    /**
     * @param Order $order
     * @param OrderAmountsCommission[] $appliedCommission
     * @return Money
     */
    private function getCommissionAmount(Order $order, array $appliedCommission): Money
    {
        $commissionAmount = new Money(0);
        $orderTotal = $this->includeShippingsInCommission ? $order->getTotal() : $order->getSubtotal();
        $categoriesCommissions = [];
        /** @var OrderAmountsCommission $globalCommission */
        $globalCommission = reset($appliedCommission);

        foreach ($appliedCommission as $commission) {
            if ($commission->getCommissionType() === CommissionType::COMMISSION_CATEGORY()->getValue()
                || $commission->getCommissionType() === CommissionType::COMMISSION_CATEGORY_COMPANY()->getValue()
            ) {
                $categoriesCommissions[] = $commission;
            } else {
                $globalCommission = $commission;
            }
        }


        if (\count($categoriesCommissions) === 0) {
            $commissionAmount = $commissionAmount->add(
                $orderTotal
                    ->multiply($globalCommission->getPercentAmount())
                ->divide(100)
            );

            return $commissionAmount;
        }

        $commissionAmount = $commissionAmount->add(
            $this->getCommissionAmountByProducts($order, $categoriesCommissions, $globalCommission)
        );

        if (true === $this->includeShippingsInCommission) {
            $commissionAmount = $commissionAmount->add(
                $order
                    ->getShippingCost()
                    ->multiply($globalCommission->getPercentAmount())
                    ->divide(100)
            );
        }

        return $commissionAmount;
    }

    private function calculateProductCommissionPercentAmount(float $percentAmount, float $productPrice, int $productAmount): Money
    {
        return Money::fromVariable($percentAmount)
            ->divide(100)
            ->multiply($productPrice)
            ->multiply($productAmount)
        ;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            CompanyEvents::DELETED => ['onCompanyDelete', 0],
        ];
    }

    public function onCompanyDelete(IterableEvent $event): void
    {
        foreach ($event as $companyId) {
            $this->deleteCommissionsByCompanyId($companyId);
        }
    }
}
