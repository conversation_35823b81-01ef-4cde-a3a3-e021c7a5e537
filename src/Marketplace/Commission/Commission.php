<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Commission;

class Commission
{
    /** @var string */
    private $id;

    /** @var int|null */
    private $companyId;

    /** @var int|null */
    private $categoryId;

    /** @var float */
    private $percentAmount;

    /** @var float */
    private $fixAmount;

    /** @var float|null */
    private $maximumAmount;

    /** @var string */
    private $commissionType;

    public function __construct(
        string $id,
        ?int $companyId,
        ?int $categoryId,
        float $percentAmount,
        float $fixAmount,
        ?float $maximumAmount
    ) {
        $this->id = $id;
        $this->companyId = $companyId;
        $this->categoryId = $categoryId;
        $this->percentAmount = $percentAmount;
        $this->fixAmount = $fixAmount;
        $this->maximumAmount = $maximumAmount;
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function setId(string $id)
    {
        $this->id = $id;

        return $this;
    }

    public function getCompanyId(): ?int
    {
        return $this->companyId;
    }

    public function setCompanyId(?int $companyId): self
    {
        $this->companyId = $companyId;

        return $this;
    }

    public function getCategoryId(): ?int
    {
        return $this->categoryId;
    }

    public function setCategoryId(?int $categoryId): self
    {
        $this->categoryId = $categoryId;

        return $this;
    }

    public function getPercentAmount(): float
    {
        return $this->percentAmount;
    }

    public function setPercentAmount(float $percentAmount): self
    {
        $this->percentAmount = $percentAmount;

        return $this;
    }

    public function getFixAmount(): float
    {
        return $this->fixAmount;
    }

    public function setFixAmount(float $fixAmount): self
    {
        $this->fixAmount = $fixAmount;

        return $this;
    }

    public function getMaximumAmount(): ?float
    {
        return $this->maximumAmount;
    }

    public function setMaximumAmount(?float $maximumAmount): self
    {
        $this->maximumAmount = $maximumAmount;

        return $this;
    }

    public function getCommissionType(): string
    {
        return $this->commissionType;
    }

    public function setCommissionType(string $commissionType): self
    {
        $this->commissionType = $commissionType;

        return $this;
    }

    /** @return mixed[] */
    public function expose(): array
    {
        return [
            'id' => $this->getId(),
            'category' => $this->getCategoryId(),
            'company' => $this->getCompanyId(),
            'percent' => $this->getPercentAmount(),
            'fixed' => $this->getFixAmount(),
            'maximum' => $this->getMaximumAmount(),
            'type' => $this->getCommissionType(),
        ];
    }
}
