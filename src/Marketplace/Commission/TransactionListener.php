<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Commission;

use Doctrine\DBAL\Connection;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Wizacha\Marketplace\Order\Refund\Repository\RefundRepository;
use Wizacha\Marketplace\Transaction\TransactionUpdatedEvent;
use Wizacha\Order;

class TransactionListener implements EventSubscriberInterface
{
    /** @var CommissionService */
    private $commissionService;

    /** @var Connection */
    private $dbConnection;

    /** @var string */
    private $tablePrefix;

    /** @var LoggerInterface */
    protected $logger;

    /** @var RefundRepository */
    protected $refundRepository;

    public function __construct(
        CommissionService $commissionService,
        Connection $dbConnection,
        string $tablePrefix,
        RefundRepository $refundRepository,
        LoggerInterface $logger
    ) {
        $this->commissionService = $commissionService;
        $this->dbConnection = $dbConnection;
        $this->tablePrefix = $tablePrefix;
        $this->refundRepository = $refundRepository;
        $this->logger = $logger;
    }

    public static function getSubscribedEvents()
    {
        return [
            TransactionUpdatedEvent::class => ['onTransactionUpdated', 0]
        ];
    }

    public function onTransactionUpdated(TransactionUpdatedEvent $event)
    {
        if (\is_null($event->getTransaction()->getOrderId()) === false) {
            $order = new Order($event->getTransaction()->getOrderId());

            $refundId = $event->getTransaction()->getRefundId();
            $isRefundedAfterWithdrawalPeriod = false;
            if ($refundId !== null) {
                $refundData = $this->refundRepository->get($refundId);
                $isRefundedAfterWithdrawalPeriod = $refundData->isRefundedAfterWithdrawalPeriod();
            }

            if ($isRefundedAfterWithdrawalPeriod === false
                && $order->isParentOrder() === false
            ) {
                $this->saveMarketplaceCommission($order);
            }
        }
    }

    private function saveMarketplaceCommission(Order $order): self
    {
        $this->commissionService->saveMarketPlaceCommission($order->getOrderV2());

        return $this;
    }
}
