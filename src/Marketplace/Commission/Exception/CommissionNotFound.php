<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Commission\Exception;

use Symfony\Component\HttpFoundation\Response;
use Throwable;
use Wizacha\Marketplace\Exception\NotFound;

class CommissionNotFound extends NotFound
{
    public function __construct($message = "Commission not found", $code = 404, Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }

    public static function fromCommissionId(string $commissionId): self
    {
        return new self(sprintf("Commission '%s' not found", $commissionId), Response::HTTP_NOT_FOUND);
    }
}
