<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\ReadModel\Denormalizer;

/**
 * JSON Read Model Denormalizer - use to load RM from DB scalar data
 */
interface ReadModelDenormalizerInterface
{
    /**
     * @param mixed[] $data Read Model data with only scalar values
     *
     * @return mixed[] $data with objects instantiated
     */
    public function denormalize(array $data): array;

    /** Checks if the current ReadModelDenormalizerInterface supports given version */
    public function supports(int $version): bool;
}
