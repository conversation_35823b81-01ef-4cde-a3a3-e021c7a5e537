<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\ReadModel\Denormalizer;

use Wizacha\Marketplace\ReadModel\Exception\DenormalizerNotFoundException;

class ReadModelDenormalizerRegistry
{
    /** @var ReadModelDenormalizerInterface[] */
    protected $denormalizers;

    public function __construct(iterable $denormalizers)
    {
        $this->denormalizers = $denormalizers;
    }

    public function getInstance(int $version): ReadModelDenormalizerInterface
    {
        foreach ($this->denormalizers as $denormalizer) {
            if (true === $denormalizer->supports($version)) {
                return $denormalizer;
            }
        }

        throw new DenormalizerNotFoundException('Unable to find denormalizer for version ' . $version);
    }
}
