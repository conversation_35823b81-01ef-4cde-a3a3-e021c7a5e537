<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\ReadModel\Denormalizer;

use Wizacha\Marketplace\Catalog\Attribute;
use W<PERSON>cha\Marketplace\Catalog\AttributesValuesGroup;
use Wizacha\Marketplace\Catalog\AttributeValue;
use Wizacha\Marketplace\Catalog\AttributeVariant;
use Wizacha\Marketplace\Image\Image;
use Wizacha\Marketplace\PIM\Attribute\AttributeType;
use Wizacha\Marketplace\Price\PriceComposition;
use Wizacha\Marketplace\Price\PriceFields;
use Wizacha\Marketplace\ReadModel\Exception\UnknownPropertyException;
use Wizacha\Marketplace\ReadModel\ReadModelObjectId;
use Wizacha\Marketplace\SeoData;
use Wizacha\Money\Money;

class ReadModelDenormalizerV1 implements ReadModelDenormalizerInterface
{
    protected const READ_MODEL_VERSION = 1;

    /** @inheritDoc */
    public function denormalize(array $data): array
    {
        unset($data['version']);

        return $this->process($data);
    }

    /** @inheritDoc */
    public function supports(int $version): bool
    {
        return $version === static::READ_MODEL_VERSION;
    }

    /**
     * Recursive function iterating over every RM field, checking if it is an object
     * which requires instantiation. It's more or less similar with what `unserialize` might do,
     * except we know exactly what type of object we might want to instantiate.
     *
     * From initial benchmarks, this approach seemed to be about 3x time faster than regular `unserialize()`.
     * Also, we went with recursive iterations instead of mapping because:
     *   1. We don't really know what's supposed to be where in the RM
     *   2. Its structure might change depending on which feature flags are enabled
     *
     * Note: please make sure that any object key you create starts with an `_` (it helps checking it faster).
     *
     * @param array $readModel Raw content extracted from JSON array
     *
     * @return mixed JSON raw content with objects instantiated.
     */
    protected function process(array &$readModel)
    {
        // ce $readModel correspond à un DateTime normalisé
        if (\count($readModel) === 3
            && \array_key_exists('date', $readModel)
            && \array_key_exists('timezone_type', $readModel)
            && \array_key_exists('timezone', $readModel)
        ) {
            return new \DateTime($readModel['date'], new \DateTimeZone($readModel['timezone']));
        }

        foreach ($readModel as $key => $value) {
            if ($key[0] === '_') {
                $field = $this->processField($key, $value);
                if (null !== $field) {
                    return $field;
                }
            }

            if (\is_array($value)) {
                $readModel[$key] = $this->process($value);
            }
        }

        return $readModel;
    }

    /**
     * @param mixed $value Raw content extracted from JSON array
     *
     * @return mixed Processed field
     */
    protected function processField($key, $value)
    {
        switch ($key) {
            case ReadModelObjectId::MONEY()->getValue():
                return new Money($value);
            case ReadModelObjectId::SEO_DATA()->getValue():
                return new SeoData(...$value);
            case ReadModelObjectId::PRICE_COMPOSITION()->getValue():
                $compo = new PriceComposition();
                foreach ($value as $field => $price) {
                    $compo->set(new PriceFields($field), $this->process($price));
                }

                return $compo;
            case ReadModelObjectId::ATTRIBUTE()->getValue():
                $value['type'] = new AttributeType($value['type']);

                return new Attribute(...array_values($value));
            case ReadModelObjectId::ATTRIBUTE_GROUP()->getValue():
                return new AttributesValuesGroup(
                    $this->process($value['groupAttribute']),
                    $this->process($value['attributesValues'])
                );
            case ReadModelObjectId::ATTRIBUTE_VALUE()->getValue():
                $variants = array_map(function (array $variantData): AttributeVariant {
                    $variantData['image'] = isset($variantData['image']['id'])
                        ? new Image($variantData['image']['id'])
                        : null;

                    return new AttributeVariant(...array_values($variantData));
                }, $value['values']);

                return new AttributeValue($this->process($value['attribute']), $variants);
            default:
                return null;
        }
    }
}
