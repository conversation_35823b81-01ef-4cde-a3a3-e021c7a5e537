<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\ReadModel;

use Broadway\ReadModel\Projector;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Routing\Router;
use Tygh\Languages\Languages;
use Wizacha\Async\Debouncer\DebouncedJobType;
use Wizacha\Async\Dispatcher;
use Wizacha\Category;
use Wizacha\Component\Locale\Locale;
use Wizacha\Events\IterableEvent;
use Wizacha\ImageManager;
use Wizacha\Marketplace\Catalog\Attribute;
use Wizacha\Marketplace\Catalog\AttributesValuesGroup;
use Wizacha\Marketplace\Catalog\AttributeValue;
use Wizacha\Marketplace\Catalog\AttributeVariant;
use Wizacha\Marketplace\Catalog\Category\CategoryService;
use Wizacha\Marketplace\Catalog\Company\CompanyService;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Entities\DeclinationFactory;
use Wizacha\Marketplace\Entities\Image as LegacyImage;
use Wizacha\Marketplace\Entities\ProductShippingRate;
use Wizacha\Marketplace\Entities\Tax;
use Wizacha\Marketplace\Exception\CategoryNotFound;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Exception\ReadmodelProjectionException;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Image\Image;
use Wizacha\Marketplace\PIM\Attribute\AttributeType;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProductService;
use Wizacha\Marketplace\PIM\Product\Attachment;
use Wizacha\Marketplace\PIM\Product\ProductService;
use Wizacha\Marketplace\PIM\TransactionMode\TransactionMode;
use Wizacha\Marketplace\PIM\Video\VideoService;
use Wizacha\Marketplace\PriceTier\Service\PriceTierService;
use Wizacha\Marketplace\Promotion\PromotionService;
use Wizacha\Marketplace\ReadModel\Product as ReadModelProduct;
use Wizacha\Marketplace\RelatedProduct\RelatedProduct;
use Wizacha\Marketplace\RelatedProduct\RelatedProductService;
use Wizacha\Marketplace\Review\ProductReviewService;
use Wizacha\Misc;
use Wizacha\Product;
use Wizacha\Search\Index\AbstractIndex;
use Wizacha\Status;

class ProductProjector extends Projector
{
    /**
     * @var ProductRepository
     */
    private $repository;

    /** @var PriceTierService  */
    private $priceTiersService;

    /**
     * @var Router
     */
    private $router;

    /**
     * @var PromotionService
     */
    private $promotionService;

    /**
     * @var VideoService
     */
    private $videoService;

    /**
     * @var string
     */
    private $readModelClass;

    /**
     * @var EventDispatcherInterface
     */
    private $eventDispatcher;

    /**
     * @var CategoryService
     */
    private $categoryService;

    /**
     * @var CompanyService
     */
    private $companyService;

    /**
     * @var Dispatcher
     */
    private $jobDispatcher;

    /**
     * @var MultiVendorProductService
     */
    private $multiVendorProductService;

    /**
     * @var ProductService
     */
    private $productService;

    /**
     * @var ImageManager
     */
    private $imageManager;

    /**
     * @var AbstractIndex
     */
    private $productIndex;

    private $productReviewService;

    /**
     * @var bool
     */
    private $multiVendorProductFlag;

    /**
     * @var DeclinationFactory
     */
    private $declinationFactory;

    /**
     * @var RelatedProductService
     */
    private $relatedProductService;

    public function __construct(
        ProductRepository $repository,
        PromotionService $promotionService,
        VideoService $videoService,
        EventDispatcherInterface $eventDispatcher,
        Router $router,
        CategoryService $categoryService,
        CompanyService $companyService,
        Dispatcher $jobDispatcher,
        MultiVendorProductService $multiVendorProductService,
        ProductService $productService,
        ImageManager $imageManager,
        AbstractIndex $productIndex,
        ProductReviewService $productReviewService,
        bool $multiVendorProductFlag,
        PriceTierService $priceTiersService,
        DeclinationFactory $declinationFactory,
        RelatedProductService $relatedProductService,
        $readModelClass = ReadModelProduct::class
    ) {
        $this->repository = $repository;
        $this->promotionService = $promotionService;
        $this->videoService = $videoService;
        $this->readModelClass = $readModelClass;
        $this->eventDispatcher = $eventDispatcher;
        $this->router = $router;
        $this->categoryService = $categoryService;
        $this->companyService = $companyService;
        $this->jobDispatcher = $jobDispatcher;
        $this->multiVendorProductService = $multiVendorProductService;
        $this->productService = $productService;
        $this->imageManager = $imageManager;
        $this->productIndex = $productIndex;
        $this->productReviewService = $productReviewService;
        $this->priceTiersService = $priceTiersService;
        $this->multiVendorProductFlag = $multiVendorProductFlag;
        $this->declinationFactory = $declinationFactory;
        $this->relatedProductService = $relatedProductService;
    }

    /**
     * @param int $ignoreIfModifiedAfter The readmodel is generated only if the time is older (lower) than the update time of the read model. Give 0 to ignore this param
     */
    public function projectProduct($productId, bool $synchronous = false, int $ignoreIfModifiedAfter = 0): void
    {
        if (!$synchronous) {
            // En cas d'exec asynchrone, on va tenter de pousser un message dans les files, et si ca marche, on sort simplement de la fonction car ca sera traité plus tard
            // Sinon, on a pas pu pousser en asynchrone donc on le fait en synchrone
            // La fonction sera appelée (par le worker) avec $synchronous = false pourtant, c'est normal car le job dispatcher du worker shuntera l'appel de delayExec (@see \Wizacha\Async\Dispatcher::nextExecIsSynchronous)
            // Le dernier argument, time(), "fige" le temps lors de l'appel. Ainsi, lors du traitement du message ou pourra voir si le readmodel a été régénéré depuis ce temps la, pour éconimiser une regen
            if ($this->jobDispatcher->delayExec(
                'marketplace.product.projector' . '::' . __FUNCTION__,
                [$productId, false, time()],
                null,
                [],
                DebouncedJobType::READMODEL_PRODUCT . ':' . $productId
            )
            ) {
                return;
            }
        }

        /** @var ReadModelProduct $oldReadModel */
        if ($oldReadModel = $this->repository->find($productId)) {
            if ($ignoreIfModifiedAfter && $oldReadModel->getReadmodelUpdateTime()->getTimestamp() > $ignoreIfModifiedAfter) {
                // Si on est ici, c'est que le readmodel a été généré entre le temps ou le message a été poussé dans les files et maintenant => on sort!
                return;
            }
        }

        /*
         * Si le produit n'est pas "disponible" pour le front,
         * alors on le supprime du readmodel et on s'en va.
         */
        if (\count(Product::frontIds([$productId])) == 0
        ) {
            $this->removeProduct($productId);
        } else {
            // The readmodel updated timestamp is defined before retrieving the product,
            // in place of defining it when creating the readmodel (buildProductData method).
            // By this way, it avoids missing a product update (same product, happens with multi-languages)
            // in case of the current buildProductData() is still running while the second product update
            // is started and completed (readmodel job created)
            $readmodelUpdatedTimestamp = time();

            // Récupération du produit dans la langue par défaut
            $defaultProduct = GlobalState::runWithContent(GlobalState::fallbackLocale(), function () use ($productId) {
                return new Product($productId, true);
            });

            GlobalState::runWithAvailableLanguages(function () use ($productId, $defaultProduct, $readmodelUpdatedTimestamp) {
                try {
                    //Chargement du produit pour avoir accès à ses informations
                    $product = new Product($productId, true);
                    // Récupération / transformation des données du produit afin de les projeter
                    $data = $this->buildProductData(
                        $product,
                        $defaultProduct,
                        $readmodelUpdatedTimestamp,
                        null
                    );
                    // Projection (sauvegarde dans la DB)
                    $this->saveProduct($data);
                } catch (ReadmodelProjectionException $exception) {
                    return;
                }
            });
        }

        // Update the products that are parent in the related products (same process)
        $this->updateParentRelatedProductOffersProjector($productId);

        // If we have a multi-vendor product with a product page, we forward the event to it.
        try {
            $multiVendorProduct = $this->productService->get($productId)->getMultiVendorProduct();
        } catch (NotFound $e) {
            $multiVendorProduct = null;
        }

        if ($this->canProjectMvp($multiVendorProduct)) {
            $this->projectMultiVendorProduct($multiVendorProduct->getId(), true);
        }
    }

    /**
     * Synchronous Product or MVP projection
     *
     * @param int|string $id
     */
    public function rebuildReadmodel($id, bool $sync = true): void
    {
        if (\is_numeric($id)) {
            $this->projectProduct($id, $sync);
        } else {
            $this->projectMultiVendorProduct($id, $sync);
        }
    }

    public function canProjectMvp(?MultiVendorProduct $multiVendorProduct): bool
    {
        return
            $this->multiVendorProductFlag
            && $multiVendorProduct instanceof MultiVendorProduct
            && $multiVendorProduct->canHaveAProductPage()
        ;
    }

    /**
     * Update options data in an existing read model object (Product or MVP) from a productId
     */
    public function updateOptionProjector(int $productId): self
    {
        $readModelIds = [$productId];
        // Check if we need to update Mvp read-model as well
        $mvp = $this->productService->get($productId)->getMultiVendorProduct();
        if (false === \is_null($mvp) && true === $this->canProjectMvp($mvp)) {
            $readModelIds[] = $mvp->getId();
        }

        // We need to get basic Product data to use getDeclinationDefinition() methode
        $basicProduct = $this->declinationFactory->makeProduct($productId);

        // Update existing readmodel
        foreach ($readModelIds as $readModelId) {
            $readModel = $this->repository->find($readModelId);
            if (false === \is_null($readModel)) {
                $newDeclinationData = [];
                foreach ($readModel->getDeclinations() as $declinationData) {
                    preg_match(
                        '/(?<productId>\d+)_(?<combinationCode>.+)/',
                        $declinationData['objectID'],
                        $matches
                    );

                    $declinationData['declination'] = $this->declinationFactory
                        ->makeDeclination($basicProduct, $matches['combinationCode'])
                        ->getDeclinationDefinition();
                    $newDeclinationData[] = $declinationData;
                }
                $readModel->setDeclinations($newDeclinationData);

                $this->repository->save($readModel);
            }
        }

        return $this;
    }

    /**
     * Projects a multi-vendor product and its associated products.
     * @param int $ignoreIfModifiedAfter The readmodel is generated only if the time is older (lower) than the update time of the read model. Give 0 to ignore this param
     */
    public function projectMultiVendorProduct(string $multiVendorProductId, $synchronous = false, int $ignoreIfModifiedAfter = 0): void
    {
        if (!$synchronous) {
            // On "fige" le temps lors de l'appel. Ainsi, lors du traitement du message ou pourra voir si le readmodel a été régénéré depuis ce temps la, pour éconimiser une regen
            if ($this->jobDispatcher->delayExec(
                'marketplace.product.projector' . '::' . __FUNCTION__,
                [$multiVendorProductId, false, time()],
                null,
                [],
                DebouncedJobType::READMODEL_MVP . ':' . $multiVendorProductId
            )
            ) {
                return;
            }
        }

        /** @var ReadModelProduct $oldReadModel */
        if ($oldReadModel = $this->repository->find($multiVendorProductId)) {
            if ($ignoreIfModifiedAfter && $oldReadModel->getReadmodelUpdateTime()->getTimestamp() > $ignoreIfModifiedAfter) {
                // Si on est ici, c'est que le readmodel a été généré entre le temps ou le message a été poussé dans les files et maintenant => on sort!
                return;
            }
        }

        try {
            $multiVendorProduct = $this->multiVendorProductService->get($multiVendorProductId);
        } catch (NotFound $e) {
            // MVP removed on PIM => remove the MVP from catalog
            $this->removeProduct($multiVendorProductId);

            return;
        }

        // A multi-vendor product with no products has no projection in the catalog.
        if ($multiVendorProduct->getLinks()->isEmpty()) {
            $this->removeProduct($multiVendorProductId);

            return;
        }

        if ($multiVendorProduct->getLinks()->count() > 400) {
            return;
        }

        // The multi-vendor product has no product page but has attached products. We forward the update event to them.
        if ($multiVendorProduct->canHaveAProductPage() === false) {
            $this->eventDispatcher->dispatch(
                IterableEvent::fromArray($multiVendorProduct->getProductIds()),
                Product::EVENT_UPDATE
            );
            $this->removeProduct($multiVendorProductId);

            return;
        }

        // remove invalid (?) products from the catalog
        $validProductIds = Product::frontIds($multiVendorProduct->getProductIds());
        $invalidProductIds = array_diff($multiVendorProduct->getProductIds(), iterator_to_array($validProductIds));

        foreach ($invalidProductIds as $invalidId) {
            $this->removeProduct($invalidId);
        }

        // A multi-vendor product with no products has no projection in the catalog.
        if (\count($validProductIds) === 0) {
            $this->removeProduct($multiVendorProductId);

            return;
        }

        // Parcours de toutes les langues disponibles
        $currentLanguage = GlobalState::contentLocale();
        foreach (array_keys(Languages::getAvailable()) as $language) {
            // Switch dans la langue pour modifier la ligne de readmodel associée
            GlobalState::switchContentTo(new Locale($language));

            // Génération du readmodel
            $multiVendorProductData = [];
            foreach ($validProductIds as $productId) {
                // See projectProduct() for the explanation relative to the variable $readmodelUpdatedTimestamp
                $readmodelUpdatedTimestamp = time();

                // Récupération du produit dans la langue par défaut,
                // ce qui est nécessaire pour qu'il puisse obtenir les champs de traduction par défaut.
                GlobalState::switchContentTo(GlobalState::fallbackLocale());
                $defaultProduct = new Product($productId, true);
                GlobalState::switchContentTo(new Locale($language));

                $oldReadModel = $this->repository->find($productId);
                if (false === $oldReadModel instanceof ReadModelProduct) {
                    try {
                        $productData = $this->buildProductData(
                            new Product($productId, true),
                            $defaultProduct,
                            $readmodelUpdatedTimestamp,
                            $multiVendorProduct
                        );
                    } catch (ReadmodelProjectionException $exception) {
                        continue;
                    }
                } else {
                    $productData = $oldReadModel->getInternalData();
                }
                unset($productData['multi_vendor_product_id']);

                // Merge them all in one to form the multi-vendor product's data
                $multiVendorProductData = Misc::arrayMergeRecursiveDistinct($multiVendorProductData, $productData);

                // Save individual product's projection with link to multi-vendor product
                $productData['multi_vendor_product_id'] = $multiVendorProductId;
                $this->saveProduct($productData);
            }

            // @codingStandardsIgnoreStart
            // PHP_CS croit que le return type de la closure est celui de la fonction parente
            $images = array_map(
                function ($imageId): array {
                    return [
                        'id' => $imageId,
                        'path' => $this->imageManager->getFullPath(new LegacyImage($imageId)),
                        'altText' => $this->imageManager->getImageAltText($imageId, (string) GlobalState::contentLocale()),
                    ];
                },
                $this->imageManager->getValidImageIds($multiVendorProduct->getImageIds())
            );
            $this->addMVPFallbackImagesToDeclinations($multiVendorProductData, $images);

            if (empty($multiVendorProductData['video'])) {
                $videoId = $multiVendorProduct->getVideo();
                if (\is_string($videoId)) {
                    $videoEntity = $this->videoService->get($videoId);
                    $video = $videoEntity->getLinks();
                    $multiVendorProductData['video'] = $video;
                }
            }

            // Si le nom du MVP est vide dans la langue scannée,
            // on sélectionne la langue par défaut pour remplir les champs à traduire.
            if ($multiVendorProduct->getName() == '') {
                GlobalState::switchContentTo(GlobalState::fallbackLocale());
            }

            // Build data proper to the multiVendorProduct
            $multiVendorProductData = array_merge($multiVendorProductData, [
                'product_id' => $multiVendorProduct->getId(),
                'product_name' => $multiVendorProduct->getName(),
                'product_code' => $multiVendorProduct->getCode(),
                'product_template_type' => $multiVendorProduct->getProductTemplateType(),
                'short_description' => $multiVendorProduct->getShortDescription(),
                'description' => $multiVendorProduct->getDescription(),
                'seo_data' => $multiVendorProduct->getSeoData(),
                'images' => $images,
                'attributes' => $this->getCatalogAttributesValuesForProductProjection($multiVendorProductId),
                'canonical_url' => fn_url('products.view?product_id=' . $multiVendorProduct->getId(), 'C', 'rel'),
                'average_rating' => $this->productReviewService->getAverageRating($multiVendorProduct->getId()),
            ]);
            [, $multiVendorProductData['category_path']] = $this->buildBreadcrumbsAndCategoryPath(new Category($multiVendorProduct->getCategory()->getId()));
            [$multiVendorProductData['ungroupedAttributesValues'], $multiVendorProductData['groupedAttributesValues']] = $this->getGroupedCatalogAttributesValuesForProductProjection($multiVendorProduct->getId());

            // On re-re-witch sur la langue pour laquelle on doit enregistrer la ligne
            GlobalState::switchContentTo(new Locale($language));

            // Update relatedOffers for the MVP - need to retrieve parentId which is not present in the products details
            $multiVendorProductData['relatedOffers'] = $this->findRelatedOffers(iterator_to_array($validProductIds));
            $this->saveProduct($multiVendorProductData);
        }

        // Retour à la langue initiale
        GlobalState::switchContentTo($currentLanguage);
    }

    protected function addMVPFallbackImagesToDeclinations(array &$data, array $images): void
    {
        foreach ($data['declinations'] as &$declinationData) {
            if (empty($declinationData['images'])) {
                $declinationData['images'] = $images;
            }
        }
    }

    protected function removeProduct($id)
    {
        $this->repository->remove($id);

        // Mise à jour de la recherche
        $this->productIndex->update(new \ArrayIterator([$id]));
    }

    protected function saveProduct(array $data): void
    {
        $class = $this->readModelClass;

        $readModel = new $class($data);

        $this->repository->save($readModel);

        // Mise à jour de la recherche
        $this->productIndex->update(new \ArrayIterator([$readModel->getId()]));
    }

    protected function buildProductData(
        Product $product,
        Product $defaultProduct,
        int $updatedTimestamp,
        MultiVendorProduct $mvp = null
    ): array {
        // Check de traduction pour savoir si on fallback sur le produit par défaut
        if ($product->getName() == '') {
            $product = $defaultProduct;
        }

        $imageShippingManager = new \Wizacha\ImageShipping(container()->get("Wizacha\Storage\ImagesStorageService"));

        $companyId = $product->getCompanyId();
        $company = $this->companyService->getCompany($companyId);
        $pimCompany = new Company($companyId);

        $shippings = $product->getProductActiveShippingsRates();
        $shippings = array_map(
            function (ProductShippingRate $shippingRate) use ($imageShippingManager): array {
                return [
                    'shipping_id' => $shippingRate->getShipping()->getId(),
                    'name' => $shippingRate->getName(),
                    'image' => $imageShippingManager->normalizeImage($shippingRate->getImage()),
                    'delivery_time' => $shippingRate->getShipping()->getDeliveryTime(),
                    'first_rate' => $shippingRate->getRateForFirst(),
                    'next_rate' => $shippingRate->getRateForNext(),
                    'location' => $shippingRate->getLocation(),
                    'latitude' => $shippingRate->getLatitude(),
                    'longitude' => $shippingRate->getLongitude(),
                    'position' => $shippingRate->getShipping()->getPosition(),
                    'delivery_type' => $shippingRate->getShipping()->getDeliveryType(),
                    'carriagePaidThreshold' => $shippingRate->getCarriagePaidThreshold(),
                ];
            },
            $shippings
        );

        $images = [];
        $videoEntity = $this->videoService->findOneByProductId($product->getId());
        $video = $videoEntity ? $videoEntity->getLinks() : null;
        if ($mvp instanceof MultiVendorProduct && \is_null($video) && false === \is_null($mvp->getVideo())) {
            try {
                $videoEntity = $this->videoService->get($mvp->getVideo());
                $video = $videoEntity->getLinks();
            } catch (\Exception $e) {
                // Prevent NotFound Exception from VideoService::get
            }
        }

        try {
            [$breadcrumbs, $categoryPath] = $this->buildBreadcrumbsAndCategoryPath($product->getCategory());
        } catch (CategoryNotFound $exception) {
            throw new ReadmodelProjectionException();
        }

        $productUrl = $product->getFrontUrl('rel');
        $productImages = $product->getImages();

        $transactionMode = $product->getTransactionMode();
        $isTransactional = $transactionMode == TransactionMode::TRANSACTIONAL();
        $isAffiliate = $transactionMode == TransactionMode::AFFILIATE();
        $isContactOnly = $transactionMode == TransactionMode::CONTACT_ONLY();

        $declinationData = [];
        foreach ($product->getDeclinations() as $declination) {
            $tags = [
                'parent_id=' . $declination->getProductId(),
                'combination_code=' . $declination->getCombinationCode(),
            ];

            $priceCalendar = $this->promotionService->getProductPriceCalendar($declination);
            $crossedOutPrice = $declination->getCrossedOutPrice();
            $affiliateLink = null;

            if ($isAffiliate) {
                $affiliateLink = '/affiliate-link/' . $declination->getId();
            }

            $priceTiers = $this->priceTiersService->buildAndExposePriceTiersWithTaxesForReadModel($product, $declination->getCombinationCode());

            if ($declination->getPrice() == 0 && \count($priceTiers) > 0 && $priceTiers[0]['excludingTaxes'] != 0) {
                container()->get('logger')->error("Readmodel price mismatch", [
                    'price' => $declination->getPrice(),
                    'priceTiers' => $priceTiers,
                    'productId' => $product->getId(),
                ]);
                throw new ReadmodelProjectionException();
            }

            $declinationData[] = [
                'objectID' => $declination->getId(),
                '_tags' => $tags,
                'product_id' => $product->getId(),
                'code' => $declination->getProductCode(),
                'supplierReference' => $declination->getSupplierReference(),
                'divisions' => $declination->getDivisions(),
                'canonical_url' => $productUrl,
                'images' => $this->imageManager->normalizeImageCollection($declination->getImages($productImages)),
                'video' => $video,
                'company_id' => $company->getId(),
                'company_type' => $company->isProfessional() ? 'V' : 'C', // TODO remove ?
                'company_name' => $company->getName(),
                'company_description' => $company->getShortDescription(),
                'company_address' => $company->getAddress(),
                'company_zipcode' => $company->getZipcode(),
                'company_city' => $company->getCity(),
                'company_phone' => $company->getPhoneNumber(),
                'company_image' => $company->getImage() ? $company->getImage()->toArray() : null,
                'company_rating' => $pimCompany->getAverageRating(),
                'company_slug' => $pimCompany->getSlug(),
                'product_name' => $declination->getName(),
                'condition' => $declination->getCondition(),
                'short_description' => $declination->getShortDescription(),
                'description' => $declination->getDescription(),
                'price' => $declination->getPrice(),
                'price_with_shipping' => $declination->getPriceWithShipping(),
                'price_calendar' => $priceCalendar,
                'crossed_out_price' => $crossedOutPrice ? $crossedOutPrice->getConvertedAmount() : null,
                'geoloc' => $declination->getGeoloc(),
                'amount' => $declination->getAmount(),
                'infinite_stock' => $declination->hasInfiniteStock(),
                'declination' => $declination->getDeclinationDefinition(),
                'shippings' => $shippings,
                'cheapest_shipping' => $declination->getCheapestShipping(),
                'features' => $this->getCatalogAttributesValuesForDeclinationProjection($declination->getProductId()),
                'breadcrumbs' => $breadcrumbs,
                'status' => $product->getStatus(),
                'is_transactional' => $isTransactional,
                'is_affiliate' => $isAffiliate,
                'is_contact_only' => $isContactOnly,
                'brand' => $declination->getBrand(),
                'affiliate_link' => $affiliateLink,
                'position' => $declination->getPosition(),
                'green_tax' => $declination->getGreenTax()->getConvertedAmount(),
                'price_tiers' => $priceTiers,
            ];

            if ($product->getStatus() !== Status::ENABLED) {
                unset($declinationData['declination']);
            }
        }

        [$ungroupedAttributesValues, $groupedAttributesValues] = $this->getGroupedCatalogAttributesValuesForProductProjection($product->getId());

        $attachments = array_map(function (Attachment $attachment) {
            return [
                'id' => $attachment->getId(),
                'label' => $attachment->getLabel(),
                'originalUrl' => $attachment->getOriginalUrl(),
                'publicUrl' => $attachment->getUrl(),
            ];
        }, $this->productService->getAttachments($product->getId()));

        // Get the related products list (child)
        $forMvp = false;
        if ($mvp instanceof MultiVendorProduct) {
            $forMvp = true;
        }

        $relatedOffers = $this->getRelatedProductOffers($product->getId(), $forMvp);

        return [
            'product_id' => $product->getId(),
            'product_code' => $product->getProductCode(),
            'max_price_adjustment' => $product->getMaxPriceAdjustment(),
            'supplierReference' => $product->getSupplierRef(),
            'canonical_url' => $productUrl,
            'product_name' => $product->getName(),
            'short_description' => $product->getShortDescription(),
            'description' => $product->getDescription(),
            'timestamp' => $product->getTimestamp(),
            'updated_timestamp' => $product->getUpdatedTimestamp(),
            'readmodel_updated_timestamp' => $updatedTimestamp,
            'images' => $this->imageManager->normalizeImageCollection($productImages),
            'video' => $video,
            'status' => $product->getStatus(),
            'availability_date' => $product->getAvailabilityDate(),
            'declinations' => $declinationData,
            'geoloc' => $product->getGeoloc(),
            'seo_data' => $product->getSeoData(),
            'average_rating' => $this->productReviewService->getAverageRating($product->getId()),
            'category_path' => $categoryPath,
            'is_transactional' => $isTransactional,
            'is_affiliate' => $isAffiliate,
            'is_contact_only' => $isContactOnly,
            'transaction_mode' => $transactionMode->getValue(),
            'green_tax' => $product->getGreenTax()->getConvertedAmount(),
            'attributes' => $this->getCatalogAttributesValuesForProductProjection($product->getId()),
            'groupedAttributesValues' => $groupedAttributesValues,
            'ungroupedAttributesValues' => $ungroupedAttributesValues,
            'weight' => $product->getWeight(),
            'attachments' => $attachments,
            'infinite_stock' => $product->hasInfiniteStock(),
            'product_template_type' => $product->getProductTemplateType(),
            'divisions' => $product->getDivisions(),
            'taxes' => array_map(function (Tax $tax): array {
                return fn_get_tax($tax->getId());
            }, $product->getTaxes()),
            'is_subscription' => $product->isSubscription(),
            'is_renewable' => $product->isRenewable(),
            'relatedOffers' => $relatedOffers,
            'quoteRequestsMinQuantity' => $product->getQuoteRequestMinQuantity(),
            'isExclusiveToQuoteRequests' => $product->isQuoteExclusive(),
        ];
    }

    protected function getRelatedProductOffers(int $fromProductId, bool $forMvp = false): array
    {
        $related = [];

        $relatedProducts = $this->relatedProductService->findByFromProductId($fromProductId);

        $relatedProductIds = array_map(
            function (RelatedProduct $relatedProduct): int {
                return $relatedProduct->getToProduct()->getId();
            },
            $relatedProducts
        );

        // Filter only products in catalog
        $validRelatedProductIds = iterator_to_array(
            Product::frontIds($relatedProductIds)
        );

        foreach ($relatedProducts as $relatedProduct) {
            // Ignore products which are not existing in the catalog
            if (false === \in_array($relatedProduct->getToProduct()->getId(), $validRelatedProductIds)) {
                continue;
            }

            // Get the product data from the PIM
            $product = new Product($relatedProduct->getToProduct()->getId(), true);

            $element = [
                'type' => $relatedProduct->getType()->getValue(),
                'productId' => $product->getId(),
                'description' => $relatedProduct->getDescription(),
                'extra' => $relatedProduct->getExtra(),
                'name' => $product->getName(),
                'status' => $product->getStatus(),
                'url' => $product->getFrontUrl(),
                'minPrice' => $product->getMinPrice(),
                'code' => $product->getProductCode(),
                'supplierReference' => $product->getSupplierRef(),
                'images' => array_map(
                    function (Image $image): array {
                        return $image->toArray();
                    },
                    $product->getAllImages()
                ),
                'company' => $product->getCompany()->getName(),
            ];

            if (true === $forMvp) {
                $element = array_merge(['parentProductId' => $relatedProduct->getFromProduct()->getId()], $element);
            }

            $related[] = $element;
        }

        return $related;
    }

    public function updateParentRelatedProductOffersProjector(int $productId): self
    {
        $parents = $this->relatedProductService->findByToProductId($productId);

        foreach ($parents as $parent) {
            $id = $parent->getFromProduct()->getId();

            // update current parent product related property
            $this->updateRelatedProductOffersProjector($id);

            // update MVP product related property, if it exists
            $this->updateMVPRelatedProductOffers($id);
        }

        return $this;
    }

    public function updateRelatedProductOffersProjector(int $productId): self
    {
        // Get readmodel corresponding to the product id
        $readModel = $this->repository->find($productId);

        // Update existing readmodel
        if ($readModel instanceof ReadModelProduct) {
            $related = $this->getRelatedProductOffers($productId);

            $readModel->setRelatedOffers($related);

            $this->repository->save($readModel);
        }

        return $this;
    }

    protected function updateMVPRelatedProductOffers(int $productId): self
    {
        // If we have a multi-vendor product containing this product,
        // update its related property
        try {
            $multiVendorProduct = $this->productService->get($productId)->getMultiVendorProduct();
        } catch (NotFound $e) {
            $multiVendorProduct = null;
        }

        if ($this->canProjectMvp($multiVendorProduct)) {
            $this->updateMVPRelatedProductOffersProjector($multiVendorProduct);
        }

        return $this;
    }

    public function updateMVPRelatedProductOffersProjector(MultiVendorProduct $multiVendorProduct): self
    {
        $productsIds = $multiVendorProduct->getProductIds();

        // Filter only parent products in catalog
        $validProductIds = iterator_to_array(
            Product::frontIds($productsIds)
        );

        $related = $this->findRelatedOffers($validProductIds);

        $mvpId = $multiVendorProduct->getId();

        // Get readmodel corresponding to the MVP product id
        $readModel = $this->repository->find($mvpId);

        if ($readModel instanceof ReadModelProduct) {
            // Update MVP existing readmodel
            $readModel->setRelatedOffers($related);

            $this->repository->save($readModel);
        }

        return $this;
    }

    /**
     * @params int[]
     * @return mixed[]
     */
    protected function findRelatedOffers(array $productsIds): array
    {
        $related = [];

        foreach ($productsIds as $productId) {
            $related = array_merge($related, $this->getRelatedProductOffers($productId, true));
        }

        return $related;
    }

    protected function buildBreadcrumbsAndCategoryPath(Category $category): array
    {
        $breadcrumbs = [];
        $categoryPath = [];
        foreach ($category->getCategoriesPath() as $category) {
            // TODO remove the breadcrumb and use the category path instead
            $breadcrumbs[] = [
                'name' => $category->getName(),
                'link' => Category::frontUrl($category->getId(), 'rel'),
            ];
            try {
                $catalogCategory = $this->categoryService->getCategory($category->getId());
                $categoryPath[] = [
                    'id' => $catalogCategory->getId(),
                    'name' => $catalogCategory->getName(),
                    'slug' => $catalogCategory->getSlug(),
                    'position' => $catalogCategory->getPosition(),
                ];
            } catch (NotFound $e) {
            }
        }

        return [$breadcrumbs, $categoryPath];
    }

    /**
     * Récupération des attributs prêt à être exportés dans les déclinaisons :
     * - public
     * - searchable
     * - non aplati (avec les groupes)
     *
     * @param int|string $id Product or MVP id
     */
    private function getCatalogAttributesValuesForDeclinationProjection($id): array
    {
        if (MultiVendorProduct::isMultiVendorProductId($id)) {
            return $this->multiVendorProductService->getAllLegacyAttributes($id, true, false, false);
        }

        return $this->productService->getAllLegacyAttributes($id, true, false, false);
    }

    /**
     * Récupération des attributs prêt à être exportés :
     * - public
     * - non searchable
     * - aplati (sans les groupes)
     *
     * Puis récupération de leurs valeurs, transformées en objet 'catalog'
     *
     * @param int|string $id Product or MVP id
     *
     * @return AttributeValue[]
     * @deprecated
     */
    private function getCatalogAttributesValuesForProductProjection($id): array
    {
        if (MultiVendorProduct::isMultiVendorProductId($id)) {
            $attributes = $this->multiVendorProductService->getAllLegacyAttributes($id, true, false, true);
        } else {
            $attributes = $this->productService->getAllLegacyAttributes($id, true, false, true);
        }

        return $this->transformLegacyAttributesToCatalogAttributeObjects($attributes);
    }

    /**
     * @return array [AttributeValue[] $ungroupedAttributesValues, AttributesValuesGroup[] $groups]
     * $ungroupedAttributesValues are all the AttributeValue which do not belong in a group.
     * For $groups,
     * @see AttributesValuesGroup
     */
    private function getGroupedCatalogAttributesValuesForProductProjection($id): array
    {
        if (MultiVendorProduct::isMultiVendorProductId($id)) {
            $legacyAttributes = $this->multiVendorProductService->getAllLegacyAttributes($id, true, false, false);
        } else {
            $legacyAttributes = $this->productService->getAllLegacyAttributes($id, true, false, false);
        }

        $attributesValues = $this->transformLegacyAttributesToCatalogAttributeObjects($legacyAttributes);

        /** @var AttributeValue[] $ungrouped All attributes which do not belong in a group */
        $ungrouped = [];
        /** @var AttributeValue[][] $groupArrays Array of groups' children attributes, indexed by group id */
        $groupArrays = [];
        foreach ($attributesValues as $attributeValue) {
            $parentId = $attributeValue->getAttribute()->getParentId();
            if (!$parentId) {
                $ungrouped[] = $attributeValue;
            } else {
                if (!isset($groupArrays[$parentId])) {
                    $groupArrays[$parentId] = [];
                }
                $groupArrays[$parentId][] = $attributeValue;
            }
        }

        if (empty($groupArrays)) {
            return [$ungrouped, []];
        }

        /** @var Attribute $groupAttributes Group attributes objects (without their children), indexed by group id */
        $groupAttributes = [];
        foreach ($legacyAttributes as $legacyAttribute) {
            if ($legacyAttribute['type'] === AttributeType::GROUP) {
                $groupAttributes[$legacyAttribute['id']] = $this->createAttributeObjectFromLegacyAttribute($legacyAttribute);
            }
        }

        /** @var AttributesValuesGroup[] $groupObjects Group objects (including children attributes), indexed by group id */
        $groupObjects = [];
        foreach ($groupArrays as $groupId => $attributeValues) {
            if (empty($groupAttributes[$groupId])) {
                // The group can be absent in the array if it's disabled.
                // We just ignore all attributes in this group
                continue;
            }

            $groupObjects[$groupId] = new AttributesValuesGroup($groupAttributes[$groupId], $attributeValues);
        }

        return [$ungrouped, $groupObjects];
    }

    /**
     * Takes a legacy attributes array and transform it into proper objects.
     *
     * @return AttributeValue[]
     */
    private function transformLegacyAttributesToCatalogAttributeObjects(array $legacyAttributes): array
    {
        $attributes = [];
        foreach ($legacyAttributes as $legacyAttribute) {
            if (!empty($legacyAttribute['subfeatures'])) {
                $attributes = array_merge($attributes, $this->transformLegacyAttributesToCatalogAttributeObjects($legacyAttribute['subfeatures']));
                continue;
            }
            $attribute = $this->createAttributeObjectFromLegacyAttribute($legacyAttribute);

            $values = [];
            if ($legacyAttribute['value'] !== null) {
                $legacyAttribute['value'] = \is_array($legacyAttribute['value']) ? $legacyAttribute['value'] : [$legacyAttribute['value']];
                foreach ($legacyAttribute['value'] as $i => $value) {
                    $variantId = $legacyAttribute['valueIds'][$i] ?? null;
                    $image = null;
                    if ($variantId !== null && isset($legacyAttribute['imageUrls'][$variantId]['id'])) {
                        $image = new Image($legacyAttribute['imageUrls'][$variantId]['id']);
                    }

                    $values[] = new AttributeVariant(
                        $variantId,
                        $legacyAttribute['id'] ? (int) $legacyAttribute['id'] : null,
                        (string) $value,
                        $legacyAttribute['slugs'][$i] ?? '',
                        $image
                    );
                }
            }

            $attributes[] = new AttributeValue($attribute, $values);
        }

        return $attributes;
    }

    private function createAttributeObjectFromLegacyAttribute(array $legacyAttribute): Attribute
    {
        return new Attribute(
            $legacyAttribute['id'],
            $legacyAttribute['name'],
            !empty($legacyAttribute['type']) ? new AttributeType($legacyAttribute['type']) : AttributeType::FREE(),
            $legacyAttribute['position'] ?? 0,
            $legacyAttribute['parent_id'] ??  null,
            (\array_key_exists('code', $legacyAttribute) === true) ? $legacyAttribute['code'] : null
        );
    }
}
