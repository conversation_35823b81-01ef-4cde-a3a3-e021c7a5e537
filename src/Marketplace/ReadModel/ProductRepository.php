<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\ReadModel;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\ParameterType;
use Wizacha\Component\Locale\Locale;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Metrics\SandboxMetrics;
use Wizacha\Marketplace\ReadModel\Exception\DeserializationException;
use Wizacha\Marketplace\ReadModel\Denormalizer\ReadModelDenormalizerRegistry;

class ProductRepository
{
    /** @var Connection */
    private $connection;

    /** @var ReadModelDenormalizerRegistry */
    protected $denormalizerRegistry;

    /** @var int */
    public const READ_MODEL_CURRENT_VERSION = 1;

    private SandboxMetrics $sandboxMetrics;

    public function __construct(
        Connection $connection,
        ReadModelDenormalizerRegistry $denormalizerRegistry,
        SandboxMetrics $sandboxMetrics
    ) {
        $this->connection = $connection;
        $this->denormalizerRegistry = $denormalizerRegistry;
        $this->sandboxMetrics = $sandboxMetrics;
    }

    /**
     * Sauvegarde d'un produit dans le catalogue.
     *
     * @param Product     $data
     * @param Locale|null $locale
     */
    public function save(Product $data, Locale $locale = null): void
    {
        // Security to avoid sandbox overloading
        if (true === $this->sandboxMetrics->isSandboxFull()) {
            return;
        }

        $data->setVersion(static::READ_MODEL_CURRENT_VERSION);

        $contentLocale = $locale ? (string) $locale : (string) GlobalState::contentLocale();

        $this->connection->executeUpdate(
            "INSERT INTO product_catalog(`id`, `locale`, `product_json`, `version`, `updated_at`)
        VALUES (?, ?, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE
            `product_json` = VALUES(`product_json`),
            `version` = VALUES(`version`),
            `updated_at` = VALUES(`updated_at`)
        ",
            [
                $data->getId(),
                $contentLocale,
                json_encode($data, JSON_PRESERVE_ZERO_FRACTION),
                static::READ_MODEL_CURRENT_VERSION,
            ],
            [
                \PDO::PARAM_STR,
                \PDO::PARAM_STR,
                \PDO::PARAM_STR,
                \PDO::PARAM_INT,
            ]
        );
    }

    /**
     * @param array $readmodel
     * @return Product|null
     */
    protected function deserialize(array $readmodel): ?Product
    {
        return $this->jsonDecodeReadModel($readmodel['product_json'], $readmodel['version'])
            ?? $this->unserializeReadModel($readmodel['product'])
            ;
    }

    /**
     * Récupération d'un produit du catalogue.
     *
     * @param int|string  $id
     * @param Locale|null $locale
     *
     * @return Product|null
     * @throws \Exception
     */
    public function find($id, Locale $locale = null): ?Product
    {
        $readmodel = $this->connection->fetchAssoc(
            "
                SELECT product, product_json, version
                FROM product_catalog
                WHERE `id` = ?
                  AND `locale` = ?
                ",
            [
                $id,
                $locale ? (string) $locale : (string) GlobalState::contentLocale(),
            ],
            [
                \PDO::PARAM_STR,
                \PDO::PARAM_STR,
                \PDO::PARAM_INT,
            ]
        );

        if (false === $readmodel) {
            return null;
        }

        return $this->deserialize($readmodel);
    }

    /**
     * @param array $ids
     * @param Locale|null $locale
     * @return Product[]
     * @throws \Exception
     */
    public function findByIds(array $ids = [], Locale $locale = null): array
    {
        $readModels = $this->connection->fetchAll(
            "
                SELECT id, product, product_json, version
                FROM product_catalog
                WHERE `id` IN (?)
                  AND `locale` = ?
                ORDER BY FIELD(id, ?)
                ",
            [
                $ids,
                $locale ? (string) $locale : (string) GlobalState::contentLocale(),
                $ids,
            ],
            [
                Connection::PARAM_STR_ARRAY,
                \PDO::PARAM_STR,
                Connection::PARAM_STR_ARRAY,
            ]
        );

        return array_map(function (array $readmodel) {
            return $this->deserialize($readmodel);
        }, $readModels);
    }

    /**
     * Récupération de tous les produits du catalogue en tant que données normalisées
     * Méthode actuellement utilisée uniquement dans le test de génération du catalogue.
     *
     * @return array
     */
    public function findAll(): array
    {
        $results = $this->connection->fetchAll(
            "SELECT product, product_json, version FROM product_catalog"
        );

        return array_map(function ($readmodel) {
            return $this->deserialize($readmodel);
        }, $results);
    }

    /**
     * Suppression d'un produit du catalogue.
     *
     * @param int|string  $id
     * @param Locale|null $locale
     *
     */
    public function remove($id, Locale $locale = null): void
    {
        $query = "DELETE FROM product_catalog WHERE `id` = ?";
        $params = [
            $id,
        ];
        $types = [
            \PDO::PARAM_STR,
        ];

        if (!\is_null($locale)) {
            $query .= " AND `locale` = ?";
            $params[] = (string) $locale;
            $types[] = \PDO::PARAM_STR;
        }

        $this->connection->executeUpdate($query, $params, $types);
    }

    /**
     * Supprime toutes les données présentes dans le read model
     *
     * /!\ ATTENTION COMMANDE DANGEREUSE EN PROD
     */
    public function clear()
    {
        $this->connection->executeQuery("TRUNCATE TABLE `product_catalog`");
    }

    public function jsonDecodeReadModel(?string $jsonReadmodel, string $version): ?Product
    {
        if (null === $jsonReadmodel || 0 === \strlen($jsonReadmodel)) {
            return null;
        }

        $readmodel = json_decode($jsonReadmodel, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new DeserializationException(json_last_error_msg());
        }

        if (false === \is_array($readmodel)) {
            throw new DeserializationException("Readmodel did not deserialize into an array.");
        }

        $data = $this->denormalizerRegistry->getInstance($version)->denormalize($readmodel);

        return new Product($data);
    }

    public function unserializeReadModel(?string $readModel): ?Product
    {
        if (null === $readModel || 0 === \strlen($readModel)) {
            return null;
        }

        $product = unserialize($readModel);

        if ($product === false && $readModel != serialize(false)) {
            throw new DeserializationException("Native php unserialize failed.");
        }

        return $product;
    }
}
