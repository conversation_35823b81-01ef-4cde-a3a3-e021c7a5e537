<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\ReadModel;

use Wizacha\Money\Money;

interface BasketPriceInterface
{
    public function getSubTotal(): Money;

    public function getShippingTotal(): Money;

    public function getTotal(): Money;

    public function getMarketplaceDiscountTotal(): Money;
}
