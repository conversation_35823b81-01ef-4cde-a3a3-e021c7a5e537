<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\ReadModel;

use Wizacha\Marketplace\Price\PriceComposition;
use Wizacha\Marketplace\Price\PriceFields;
use Wizacha\Money\Money;

/**
 * Prices in data basket
 */
class BasketPrices implements BasketPriceInterface
{
    /** @var PriceComposition */
    private $prices;

    /** @var string[] */
    private $promotions = [];

    /** @var string[]  */
    private $coupons;

    /** @var string|null */
    private $marketplaceDiscountId;

    public function __construct(PriceComposition $prices, array $promotions = [], array $coupons = [])
    {
        $this->prices = $prices;
        $this->promotions = $promotions;
        $this->coupons = $coupons;
    }

    public function getPrices(): PriceComposition
    {
        return $this->prices;
    }

    public function setPrices(PriceComposition $prices): self
    {
        $this->prices = $prices;

        return $this;
    }

    public function getShippingTotal(): Money
    {
        return $this->prices->get(PriceFields::SHIPPING_TOTAL());
    }

    public function getShippingTax(): Money
    {
        return $this->prices->get(PriceFields::SHIPPING_TAX());
    }

    public function getDiscountTotal(): Money
    {
        return $this->prices->get(PriceFields::BASKET_DISCOUNT());
    }

    public function addDiscountTotal(Money $money): self
    {
        $this->prices->set(PriceFields::BASKET_DISCOUNT(), $this->getDiscountTotal()->add($money));

        return $this;
    }

    public function getSubTotal(): Money
    {
        return $this->prices->get(PriceFields::BASKET_TOTAL());
    }

    public function getOriginaSubTotal(): Money
    {
        return $this->prices->get(PriceFields::BASKET_SUBTOTAL());
    }

    public function getTax(): Money
    {
        return $this->prices->get(PriceFields::BASKET_TOTAL_TAXES());
    }

    public function getTotal(): Money
    {
        return $this->getSubTotal()->add($this->getShippingTotal());
    }

    public function getMarketplaceDiscountTotal(): Money
    {
        return $this->prices->get(PriceFields::BASKET_DISCOUNT_MARKETPLACE());
    }

    public function getPromotions(): array
    {
        return $this->promotions;
    }

    public function getMarketplaceDiscountId(): ?string
    {
        return $this->marketplaceDiscountId;
    }

    public function setMarketplaceDiscountId(string $id): self
    {
        $this->marketplaceDiscountId = $id;
        $this->addPromotions($id);

        return $this;
    }

    public function addPromotions(string $id): self
    {
        $this->promotions[] = $id;

        return $this;
    }

    public function getCoupons(): array
    {
        return $this->coupons;
    }

    public function expose(): array
    {
        return [
            'prices' => $this->getPrices(),
            'total' => $this->getTotal()->getConvertedAmount(),
            'shipping_total' => $this->getShippingTotal()->getConvertedAmount(),
            'subtotal' => $this->getSubTotal(),
            'original_subtotal' => $this->getOriginaSubTotal(),
            'subtotal_discount' => $this->getDiscountTotal(),
            'tax' => number_format($this->getTax()->getPreciseConvertedAmount(), 4, '.', ''),
            'marketplace_discount_total' => $this->getMarketplaceDiscountTotal()->getConvertedAmount(PHP_ROUND_HALF_EVEN),
            'promotions' => $this->promotions,
            'marketplace_discount_id' => $this->getMarketplaceDiscountId(),
        ];
    }
}
