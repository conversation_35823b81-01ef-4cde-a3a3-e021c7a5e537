<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright Copyright (c) Wizaplace
 * @license   Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\ReadModel;

use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Tygh\Database;
use Wizacha\Core\Hash\Hash;
use Wizacha\Core\Iterator\PdoColumnIterator;
use Wizacha\Events\IterableEvent;
use Wizacha\Product;

class ReadModelService
{
    /** @var ProductRepository */
    protected $productRepository;

    /** @var EventDispatcherInterface */
    protected $eventDispatcher;

    public function __construct(
        ProductRepository $productRepository,
        EventDispatcherInterface $eventDispatcher
    ) {
        $this->productRepository = $productRepository;
        $this->eventDispatcher = $eventDispatcher;
    }

    /**
     * Remove all records from the Read Model
     *
     * @return ReadModelService
     */
    public function clear(): self
    {
        $this->productRepository->clear();

        return $this;
    }

    /**
     * Create all records in the Read Model, from the PIM
     *
     * @return ReadModelService
     */
    public function create(): self
    {
        $this->eventDispatcher->dispatch(
            (new IterableEvent())->setIterator(
                new PdoColumnIterator(
                    Database::prepare("SELECT products.product_id FROM ?:products as products")
                )
            ),
            Product::EVENT_UPDATE
        );

        return $this;
    }
}
