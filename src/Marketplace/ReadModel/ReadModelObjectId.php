<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\ReadModel;

use MyCLabs\Enum\Enum;

/**
 * Enum containing all object identifier used to hydrate JSON read model.
 *
 * @method static ReadModelObjectId MONEY()
 * @method static ReadModelObjectId SEO_DATA()
 * @method static ReadModelObjectId PRICE_COMPOSITION()
 * @method static ReadModelObjectId ATTRIBUTE()
 * @method static ReadModelObjectId ATTRIBUTE_GROUP()
 * @method static ReadModelObjectId ATTRIBUTE_VALUE()
 */
class ReadModelObjectId extends Enum
{
    public const MONEY = '_MONEY_';
    public const SEO_DATA = '_SEO_DATA_';
    public const PRICE_COMPOSITION = '_PRICE_COMPO_';
    public const ATTRIBUTE = '_ATTR_';
    public const ATTRIBUTE_GROUP = '_ATTR_GROUP_';
    public const ATTRIBUTE_VALUE = '_ATTR_VALUE_';
}
