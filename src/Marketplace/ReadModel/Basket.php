<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\ReadModel;

use Broadway\ReadModel\ReadModelInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Wizacha\AppBundle\Security\User\UserService;
use Wizacha\Marketplace\Address\Address;
use Wizacha\Marketplace\Address\Exception\AddressFieldsException;
use Wizacha\Marketplace\Basket\BasketProjector;
use Wizacha\Marketplace\Basket\ReadModel\Basket as ReadmodelBasket;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Entities\Declination;
use Wizacha\Marketplace\Entities\Shipping;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Price\PriceComposition;
use Wizacha\Marketplace\Price\PriceFields;
use Wizacha\Marketplace\Promotion\PromotionService;
use Wizacha\Marketplace\Promotion\PromotionType;
use Wizacha\Marketplace\Subscription\Exception\SubscriptionException;
use Wizacha\Marketplace\Subscription\OrderItem;
use Wizacha\Marketplace\Subscription\Subscription;
use Wizacha\Marketplace\Subscription\SubscriptionProduct;
use Wizacha\Marketplace\Subscription\SubscriptionRepository;
use Wizacha\Money\Money;
use Wizacha\Marketplace\Entities\Tax;

/**
 * This is the readmodel record stored in the database.
 *
 * It contains very raw information and should not be used directly.
 *
 * @deprecated Use \Wizacha\Marketplace\Basket\ReadModel\Basket instead
 * @see \Wizacha\Marketplace\Basket\ReadModel\Basket
 * @see \Wizacha\Marketplace\Basket\BasketService::getReadmodel()
 */
class Basket implements ReadModelInterface, BasketPriceInterface
{
    protected $id;

    /**
     * @var array [['id' => declination_id, 'quantity' => quantity in cart], ...]
     */
    protected $products;
    protected array $coupons = [];
    protected array $groups = [];
    protected array $selectedShippings = [];
    protected $orderId;
    protected ?Address $billingAddress = null;
    protected ?Address $shippingAddress = null;
    private $data;
    private ?string $comment;
    private bool $hasAdjustableProducts = false;
    /** @var array|int[] */
    private array $customShippingPrices = [];

    public function __construct($id)
    {
        $this->id = $id;
        $this->products = array();
    }

    /**
     * {@inheritDoc}
     */
    public function getId()
    {
        return $this->id;
    }

    public function getProducts(): array
    {
        return $this->products;
    }

    public function modifyProductQuantity($productId, $quantity, string $productClassName, string $subscriptionId = null)
    {
        if ($quantity == 0) {
            unset($this->products[$productId]);
        } else {
            $this->products[$productId]['id'] = $productId;
            $this->products[$productId]['quantity'] = $quantity;
            $this->products[$productId]['className'] = $productClassName;
            $this->products[$productId]['subscriptionId'] = $subscriptionId;
        }

        $this->invalidateData();
    }

    public function modifyProductComment(string $productId, string $comment)
    {
        $this->products[$productId]['comment'] = $comment;

        $this->invalidateData();
    }

    public function getComment(): string
    {
        return $this->comment ?? '';
    }

    public function setComment(string $comment)
    {
        $this->comment = $comment;
    }

    public function addCoupon(string $coupon)
    {
        $this->coupons[] = $coupon;

        $this->invalidateData();
    }

    public function removeCoupon(string $coupon)
    {
        $this->coupons = array_diff($this->coupons, [$coupon]);

        $this->invalidateData();
    }

    /**
     * @return string[]
     */
    public function getCoupons(): array
    {
        return $this->coupons;
    }

    /**
     * Gestion de l'adresse de facturation
     */
    public function setBillingAddress(Address $address)
    {
        $this->billingAddress = $address;

        $this->invalidateData();
    }

    public function getBillingAddress(): ?Address
    {
        try {
            if (null !== $this->billingAddress) {
                $this->billingAddress->assertValidBasket();
            }
        } catch (AddressFieldsException $exception) {
            return null;
        }

        return $this->billingAddress;
    }

    /**
     * Gestion de l'adresse de livraison
     */
    public function setShippingAddress(Address $address)
    {
        $this->shippingAddress = $address;

        $this->invalidateData();
    }

    public function getShippingAddress(): ?Address
    {
        try {
            if (null !== $this->shippingAddress) {
                $this->shippingAddress->assertValidBasket();
            }
        } catch (AddressFieldsException $exception) {
            return null;
        }

        return $this->shippingAddress;
    }

    public function setGroups($groups)
    {
        $this->groups = $groups;

        $this->invalidateData();
    }

    /**
     * Returns the total amount excluding shipping.
     */
    public function getSubTotal(): Money
    {
        return $this->getData()['subtotal'];
    }

    public function getShippingTotal(): Money
    {
        return Money::fromVariable(
            $this->getData()['shipping_total']
        );
    }

    public function getTaxTotal(): Money
    {
        return Money::fromVariable(
            $this->getData()['tax']
        );
    }

    public function getTotal(): Money
    {
        return Money::fromVariable(
            $this->getData()['total']
        );
    }

    public function getMarketplaceDiscountTotal(): Money
    {
        return Money::fromVariable(
            $this->getData()['marketplace_discount_total']
        );
    }

    public function getMarketplaceDiscountId(): ?string
    {
        return $this->getData()['marketplace_discount_id'] ?? null;
    }

    public function getTotalQuantity(): int
    {
        return (int) $this->getData()['quantity_total'];
    }

    public function getAllItems(): array
    {
        return $this->getData()['all_items'];
    }

    /**
     * Vérifie si le panier est en "mode" points relais, en vérifiant qu'au moins un mode
     * de livraison est de type points relais.
     * Il est pour l'instant convenu que comme tous les modes de livraisons doivent être
     * en mode point relais pour pouvoir le sélectionner, si l'un d'eux est en points relais
     * cela veut dire qu'ils le sont tous.
     */
    public function isPickupPointsShipping(): bool
    {
        foreach ($this->selectedShippings as $shippingId) {
            if (\Wizacha\Shipping::isPickupPoint($shippingId)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Vérifie qu'un panier est éligible à l'expédition en points relais,
     * en vérifiant que tous les shipping groups dispose d'un mode de livraison
     * de type adapté
     */
    public function isEligibleToPickupPointsShipping(): bool
    {
        $basketIsEligible = !empty($this->groups);

        foreach ($this->groups as $companyGroups) {
            foreach ($companyGroups as $shippingGroups) {
                $shippingGroupIsEligible = false;
                foreach ($shippingGroups['shippings'] as $shippingId) {
                    if (\Wizacha\Shipping::isPickupPoint($shippingId)) {
                        return true;
                    }
                }

                $basketIsEligible = $basketIsEligible && $shippingGroupIsEligible;
            }
        }

        return $basketIsEligible;
    }

    /**
     * Récupère l'id du mode de livraison par points relais
     */
    public function getPickupPointsShippingId(): ?int
    {
        foreach ($this->groups as $companyGroups) {
            foreach ($companyGroups as $shippingGroups) {
                foreach ($shippingGroups['shippings'] as $shippingId) {
                    if (\Wizacha\Shipping::isPickupPoint($shippingId)) {
                        return (int) $shippingId;
                    }
                }
            }
        }

        return null;
    }

    /**
     * Check if the products belongs to user shipping division
     * @param null|string $division
     * @return bool
     */
    public function hasValidProductDivision(?string $division): bool
    {
        if (!$division) {
            return false;
        }

        foreach ($this->getAllItems() as $item) {
            $divisions = new ArrayCollection($item['declination']['divisions']);

            if (!$divisions->contains($division)) {
                return false;
            }
        }

        return true;
    }

    public function getInvalidProductDivision(string $division): array
    {
        $products = [];
        foreach ($this->getAllItems() as $item) {
            $divisions = new ArrayCollection($item['declination']['divisions']);

            if ($divisions->contains($division) === false) {
                array_push($products, $item['declination']['product_id']);
            }
        }

        return $products;
    }

    public function getReadModel(): ReadmodelBasket
    {
        $this->invalidateData();

        return new ReadmodelBasket(
            $this->getData()
        );
    }

    /**
     * @deprecated Use methods on this object instead.
     */
    public function getData(): array
    {
        $this->data = $this->data ?? $this->processData();

        return $this->data;
    }

    private function invalidateData(): void
    {
        $this->data = null;
    }

    private function processData(): array
    {
        $data = [];
        $this->fillGroupData($data);
        $data['comment'] = $this->getComment();
        $data['coupons'] = $this->coupons;
        $data['billing_address'] = $this->getBillingAddress();
        $data['shipping_address'] = $this->getShippingAddress();
        $data['is_eligible_to_pickup_points_shipping'] = $this->isEligibleToPickupPointsShipping();
        $data['is_pickup_points_shipping'] = $this->isPickupPointsShipping();
        $data['has_adjustable_products'] = $this->hasAdjustableProducts();

        // Refresh prices by applying promotions
        /** @var PromotionService */
        $promotionService = container()->get('marketplace.promotion.promotionservice');
        /** @var UserService */
        $userService = container()->get('marketplace.user_service');

        $userId = $userService->getCurrentUserId();

        $allItems = array_column($data['groups'], 'all_items');
        $allItems = empty($allItems) ? [] : array_merge(...$allItems);
        $productsQuantities = [];

        foreach ($allItems as $item) {
            $productsQuantities[$item['declination']['objectID']] = $item['quantity'];
        }

        $data = $promotionService->refreshBasketPrices($data, $userId, $productsQuantities);

        // Calculate the total price and the total shipping with discount applied
        $total = new Money(0);
        $tax = new Money(0);
        $originalTotal = new Money(0);
        $shippingTotal = new Money(0);
        $shippingOriginalTotal = new Money(0);
        $quantityTotal = 0;
        foreach ($data['groups'] as &$group) {
            /** @var PriceComposition $prices */
            $prices = $group['prices'];
            /** @var PriceComposition $originalPrices */
            $originalPrices = $group['original_prices'];

            // Ici, on ne fait pas simplement $total = $total->add($prices->get(PriceFields::BASKET_TOTAL()));
            // Pour arrondir chacun des groupes et ne pas avoir de surprise lors du dispatch de fonds
            $total = $total->add(Money::fromVariable($prices->get(PriceFields::BASKET_TOTAL())->getConvertedAmount()));

            // Pareil, on arrondi bien avant la somme
            $tax = $tax->add(Money::fromVariable($prices->get(PriceFields::TAXES())->getConvertedAmount()));

            // Toujours le meme trick
            $originalTotal = $originalTotal->add(Money::fromVariable($originalPrices->get(PriceFields::BASKET_TOTAL())->getConvertedAmount()));

            $shippingTotal = $shippingTotal->add(Money::fromVariable($prices->get(PriceFields::SHIPPING_TOTAL())->getConvertedAmount()));
            $shippingOriginalTotal = $shippingOriginalTotal->add(Money::fromVariable($originalPrices->get(PriceFields::SHIPPING_TOTAL())->getConvertedAmount()));

            $quantityTotal += array_sum(array_column($group['all_items'], 'quantity'));
        }

        $data['id'] = $this->getId();
        $data['quantity_total'] = $quantityTotal;
        $data['marketplace_discount_total'] = 0;
        // Flatten the list of all items in the basket for ease of use in templates
        $allItems = array_column($data['groups'], 'all_items');
        $allItems = empty($allItems) ? [] : array_merge(...$allItems);
        $data['all_items'] = $allItems;

        $productsQuantities = [];
        foreach ($allItems as $item) {
            $productsQuantities[$item['declination']['objectID']] = $item['quantity'];
        }

        /** $prices PriceComposition */
        $prices = $data['prices'];
        $prices->set(PriceFields::BASKET_DISCOUNT(), $originalTotal->subtract($total)->add($shippingOriginalTotal->subtract($shippingTotal)));
        $prices->set(PriceFields::BASKET_TOTAL_TAXES(), $tax);
        $prices->set(PriceFields::BASKET_SUBTOTAL(), $originalTotal);
        $prices->set(PriceFields::BASKET_TOTAL(), $total);
        $prices->set(PriceFields::BASKET_DISCOUNT_MARKETPLACE(), new Money(0));
        $prices->set(
            PriceFields::SHIPPING_REAL(),
            $prices->get(PriceFields::SHIPPING_REAL())->getConvertedAmount() > 0 ? $prices->get(PriceFields::SHIPPING_REAL()) : $shippingTotal
        );
        $basketPrices = $promotionService->refreshBasketTotals(
            new BasketPrices($prices, $data['promotions'], $this->coupons),
            $userId,
            $quantityTotal,
            $productsQuantities
        );

        return array_merge(
            $data,
            $basketPrices->expose()
        );
    }

    public function setCustomShippingPrice(int $groupId, int $shippingId, ?float $price): void
    {
        $this->customShippingPrices[$groupId][$shippingId] = $price;
        $this->invalidateData();
    }

    public function setSelectedGroupShipping($groupId, $shippingId)
    {
        $this->selectedShippings[$groupId] = $shippingId;

        $this->invalidateData();
    }

    public function removeSelectedShippings()
    {
        $this->selectedShippings = [];

        $this->invalidateData();
    }

    /**
     * Return of this function can be used in fn_place_order.
     *
     * @param int $userId
     * @return array
     * @throws \Wizacha\Marketplace\Promotion\Exception\PromotionNotFound
     */
    public function getCscartData($userId)
    {
        /** @var PromotionService */
        $promotionService = container()->get('marketplace.promotion.promotionservice');

        $basket = $this->getData();

        $appliedPromotionIds = $basket['promotions'] ?? [];
        $order = [];
        $productDataToAdd = [];
        $totalWeight = .0;

        foreach ($this->products as $productData) {
            if (SubscriptionProduct::class === $productData['className']) {
                $productId = (int) explode("_", $productData['id'])[0];
                $orderItem = null;

                /** @var Subscription $subscription */
                $subscription = container()->get(SubscriptionRepository::class)->findOneById($productData['subscriptionId']);

                foreach ($subscription->getOrderItems() as $item) {
                    if ($item->getProductId() === $productId) {
                        $orderItem = $item;
                        break;
                    }
                }

                if (false === $orderItem instanceof OrderItem) {
                    throw new SubscriptionException("Unable to find orderItem for product ID '" . $productId . "'.");
                }

                $declination = new Declination(
                    new SubscriptionProduct($orderItem, $subscription)
                );
            } else {
                $declination = Declination::fromId($productData['id']);
            }

            if ($declination->isActive()) {
                $companyId = $declination->getCompany()->getId();
                // Explorer tous les shipping groups pour trouver celui qui contient le produit
                $productPrice = null;
                foreach ($basket['groups'][$companyId]['products'] as $shippingGroup) {
                    $prices = $shippingGroup['prices']->get(PriceFields::PRODUCTS());
                    if ($productPrice = $prices[$declination->getProductId()][$declination->getId()]) {
                        break;
                    }
                }

                $product = $declination->getProduct();

                if ($product instanceof SubscriptionProduct) {
                    /** @var SubscriptionProduct $product */
                    $price = $product->getOriginalPrice()->getConvertedAmount();
                    $_productData = $product->getDataFromDatabase();
                } else {
                    /** @var Product $product */
                    $price = $promotionService
                        ->getProductFinalPrice(
                            $declination,
                            $productData['quantity']
                        )
                        ->get(
                            PriceFields::BASE_PRICE()
                        )
                        ->getConvertedAmount()
                    ;
                    $_productData = [
                        'productClassName' => \get_class($product),
                    ];
                }

                $productData = array_merge(
                    $_productData,
                    [
                        'product_id'        => $declination->getProductId(),
                        'price'             => $price,
                        'amount'            => $productData['quantity'],
                        'product_options'   => fn_get_product_options_by_combination($declination->getCombinationCode()),
                        'is_edp'            => false,
                        'extra'             => ['reduced_price_basket_promotion' => $productPrice[PriceFields::PRODUCT_PRICE_WITH_TAX]->getAmount()],
                        'comment'           => $productData['comment'] ?? '',
                        'divisions'         => $productData['divisions'],
                        'is_subscription'   => $product->isSubscription(),
                        'is_renewable'      => $product->isRenewable(),
                    ]
                );

                $productDataToAdd[fn_generate_cart_id($declination->getId(), [])] = $productData;

                $totalWeight += $declination->getProductWeight() * $productData['amount'];
            }
        }
        $order['total_weight'] = $totalWeight;
        $order['chosen_shipping'] = array_combine(
            array_values($this->selectedShippings),
            array_values($this->selectedShippings)
        );
        $order['calculate_shipping'] = true;
        $order['order_id'] = $this->orderId;
        // We are using the key "new_promotion_ids" because "promotions" or "promotion_ids" would conflict with existing CS cart keys...
        $order['new_promotion_ids'] = $appliedPromotionIds;

        //Replace potentiel cscart promo by ours
        $order['promotions'] = [];
        $promotionSerializer = container()->get('marketplace.promotion.serializer');
        foreach ($appliedPromotionIds as $promotionId) {
            $promotion = $promotionService->get($promotionId);
            $promotionType = $promotion->getType()->getValue();

            $value = ($promotionType === 'basket') ?
                $basket['prices']->get(PriceFields::BASKET_DISCOUNT())->getConvertedAmount() :
                $basket['prices']->get(PriceFields::BASKET_DISCOUNT_MARKETPLACE())->getConvertedAmount();

            $bonuses = $promotionSerializer->serialize($promotion)['bonuses'];

            foreach ($bonuses as &$bonus) {
                if ($bonus['type'] === 'fixed') {
                    $bonus['reduction'] = Money::fromVariable($bonus['reduction'] / 100)->getConvertedAmount();
                }
            }
            unset($bonus);

            $order['promotions'][$promotion->getId()] = [
                'id' => $promotion->getId(),
                'name' => $promotion->getName(),
                'target' => $promotion->getTarget()->getName(),
                'promotion_type' => $promotion->getType()->getValue(),
                'bonus' => $bonuses,
            ];

            if ($promotionType === PromotionType::MARKETPLACE()->getValue()) {
                $promotion = $promotionService->getMarketplacePromotion($promotionId);
                array_push($order['promotions'][$promotion->getId()], ['coupon' => $promotion->getCoupon()]);
            }
        }

        fn_add_product_to_cart($productDataToAdd, $order);

        // Create a fake $auth until we remove it completely
        $auth = [
            'user_id' => $userId,
            'usergroup_ids' => [0],
            'area' => 'C',
        ];


        $order['user_data'] = fn_get_user_info($userId, true, $order['profile_id']);
        fn_calculate_cart_content($order, $auth, 'S', true, 'F', true, $this, $basket);

        $order = array_merge(
            $order,
            fn_get_user_info($userId)
        );

        if (empty($order['profile_id'])) {
            $order['profile_id'] = db_get_field(
                "SELECT
                    profile_id
                FROM
                    ?:user_profiles
                WHERE
                    user_id = ?i
                    AND profile_type='P'
                ",
                $userId
            );
        }

        // Passage de l'addresse de facturation du readmodel
        $order['billing_address'] = $basket['billing_address'];

        // Passage de l'addresse de livraison du readmodel
        $order['shipping_address'] = $basket['shipping_address'];

        // Passage de l'id du panier, qui sera ensuite sauvegardé dans l'order
        $order['basket_id'] = $basket['id'];

        return $order;
    }

    public function setOrderId($orderId)
    {
        $this->orderId = $orderId;
    }

    /**
     * @return array
     */
    public function getSelectedShippings()
    {
        return $this->selectedShippings;
    }

    public function hasAdjustableProducts(): bool
    {
        return $this->hasAdjustableProducts;
    }

    public function __sleep()
    {
        // Return all properties except $data
        return array_diff(
            array_keys(
                get_object_vars($this)
            ),
            ['data']
        );
    }

    /**
     * Permet de recalculer les prix des paniers par companies et le prix global du panier à partir des shippingsGroups
     */
    public static function recalculateBasketDataPricesFromShippingGroup(array &$basketData)
    {
        /**
         * Soustrait le prix actuel d'un group à son prix d'origine pour avoir le montant total de
         * réduction sur le groupe
         * @param array $group
         * @return Money
         */
        $getSubtotalDiscount = function (array $group): Money {
            $finalTotal = $group['prices']->get(PriceFields::BASKET_TOTAL())->add($group['prices']->get(PriceFields::SHIPPING_TOTAL()));
            $originalTotal = $group['original_prices']->get(PriceFields::BASKET_TOTAL())->add($group['original_prices']->get(PriceFields::SHIPPING_TOTAL()));

            return $originalTotal->subtract($finalTotal);
        };


        $basketPrices = Basket::createCleanPriceComposition();
        foreach ($basketData['groups'] as &$companyGroup) {
            $companyGroupPrices = Basket::createCleanPriceComposition();
            $companyGroupOriginalPrices = Basket::createCleanPriceComposition();

            // On ajoute les données de prix de chaque shippinGroup au companyGroup
            foreach ($companyGroup['products'] as &$shippingGroup) {
                static::incrementPriceComposition($companyGroupPrices, $shippingGroup['prices']);
                static::incrementPriceComposition($companyGroupOriginalPrices, $shippingGroup['original_prices']);
                $shippingGroup['subtotal_discount'] = $getSubtotalDiscount($shippingGroup)->getConvertedAmount();
            }
            $companyGroupPrices->set(PriceFields::TAXES(), $companyGroupPrices->get(PriceFields::BASKET_TAX())->add($companyGroupPrices->get(PriceFields::SHIPPING_TAX())));
            $companyGroupOriginalPrices->set(PriceFields::TAXES(), $companyGroupOriginalPrices->get(PriceFields::BASKET_TAX())->add($companyGroupOriginalPrices->get(PriceFields::SHIPPING_TAX())));
            $companyGroup['prices'] = $companyGroupPrices;
            $companyGroup['original_prices'] = $companyGroupOriginalPrices;
            $companyGroup['subtotal_discount'] = $getSubtotalDiscount($companyGroup)->getConvertedAmount();

            // On ajoute les prix de chaque groupe au panier
            static::incrementPriceComposition($basketPrices, $companyGroupPrices);
        }
        $basketPrices->set(PriceFields::TAXES(), $basketPrices->get(PriceFields::BASKET_TAX())->add($basketPrices->get(PriceFields::SHIPPING_TAX())));
        $basketData['prices'] = $basketPrices;
    }

    protected function setHasAdjustableProducts(bool $hasAdjustableProducts): self
    {
        $this->hasAdjustableProducts = $hasAdjustableProducts;

        return $this;
    }

    /**
     * @return array<mixed>|bool
     */
    protected function getDeclinationData($declinationId)
    {
        return BasketProjector::getDeclinationData(
            $declinationId,
            null,
            $this->getProducts()[$declinationId]['quantity'],
            $this->getProducts()[$declinationId]['className'],
            $this->getProducts()[$declinationId]['subscriptionId'],
            container()->get(SubscriptionRepository::class)
        );
    }

    /**
     * Return the basket price for a company and shipping group, without shipping costs
     * @throws SubscriptionException
     */
    protected function getTotalBasketByCompanyIdAndShippingGroupId(
        int $companyId,
        int $shippingGroupId,
        array &$declinationsCache
    ): float {
        $total = 0;

        if (!\array_key_exists($companyId, $this->groups)
            || !\array_key_exists($shippingGroupId, $this->groups[$companyId])
            || !\is_array($this->groups[$companyId][$shippingGroupId]['products'])
        ) {
            return $total;
        }

        foreach ($this->groups[$companyId][$shippingGroupId]['products'] as $declinationId) {
            $declination = $declinationsCache[$declinationId] ?? $this->getDeclinationData($declinationId);
            $declinationsCache[$declinationId] = $declination; # memoization

            if (!$declination) {
                // The declination can be disabled
                continue;
            }

            $subtotal = Money::fromVariable($declination['price'])
                ->multiply($this->products[$declinationId]['quantity'] ?? 0);

            $total += $subtotal->getPreciseConvertedAmount();
        }

        return $total;
    }

    protected function fillGroupData(&$data)
    {
        $internationalTaxService = container()->get('marketplace.international_tax.shipping');
        $userServiceLegacy = container()->get('marketplace.user_service');
        $userService = container()->get('marketplace.user.user_service');

        $data['groups'] = [];
        $shippingTaxId = null;
        $user = null;
        $userId = $userServiceLegacy->getCurrentUserId();
        if (null !== $userId) {
            $user =  $userService->get($userId);
        }

        // Memoization of companies totals
        $totalBasketByCompanyIdAndSimpleGroup = [];

        // Memoization of declinations data
        $declinationsCache = [];

        //Generate company Group with company data
        foreach ($this->groups as $companyId => $group) {
            $company = new Company($companyId);
            $data['groups'][$company->getId()] = [
                'companyId' => $company->getId(),
                'companyName' => $company->getName(),
                'companyType' => $company->getType(),
                'companySlug' => $company->getSlug(),
                'companyTerms' => $company->getTerms(),
            ];

            //For each group, add product prices and shippings prices to $prices
            foreach ($group as $simpleGroupId => $simpleGroup) {
                $shippingGroupPrices =  Basket::createCleanPriceComposition();

                $data['groups'][$company->getId()]['products'][$simpleGroupId] = [
                    'simpleGroupId' => $simpleGroupId,
                    'products' => [],
                ];

                //Add product price/tax to $prices
                $basketTotal = new Money(0);
                $tax = 0;
                foreach ($simpleGroup['products'] as $declinationId) {
                    $declination = $declinationsCache[$declinationId] ?? $this->getDeclinationData($declinationId);
                    $declinationsCache[$declinationId] = $declination; # memoization

                    if (!$declination) {
                        // The declination can be disabled
                        continue;
                    }
                    $data['groups'][$company->getId()]['products'][$simpleGroupId]['products'][] = $this->fillGroupProductData(
                        $shippingGroupPrices,
                        $basketTotal,
                        $tax,
                        $simpleGroup,
                        $declinationId,
                        $declination
                    );

                    if ($declination['hasMaxPriceAdjustment']) {
                        $this->setHasAdjustableProducts(true);
                    }
                }
                // Set total and tax total
                $shippingGroupPrices->set(PriceFields::BASKET_TOTAL(), $basketTotal);
                $shippingGroupPrices->set(PriceFields::BASKET_TAX(), Money::fromVariable($tax));


                //Calculate shipping price for products in group.
                //inspired by fn_w_calculate_package_rate
                $max = [];
                foreach ($simpleGroup['shippings'] as $shippingId) {
                    $max[$shippingId] = ['id' => null, 'value' => 0];
                }
                $condensed = $this->generateShippingRateByProduct($data['groups'][$company->getId()]['products'][$simpleGroupId]['products'] ?: [], $max);

                //Fill shipping data and shipping price for this group
                $selectedShippingId = false;
                $minShipping = [];
                foreach ($simpleGroup['shippings'] as $shippingId) {
                    try {
                        $shipping = new Shipping($shippingId);
                    } catch (NotFound $e) {
                        // Shipping has been deleted since it was added to this basket
                        continue;
                    }
                    $shippingPrice = $this->getShippingPriceForShippingId($condensed, $shippingId, $max);
                    $carriagePaidThreshold = $this->getCarriagePaidThreshold($shippingId, $company->getId());
                    $customShippingPrice = $isCustomShippingPrice = null;

                    // Calculation of custom price (cf. route POST /basket/{{basketId}}/shipping-price)
                    if (\array_key_exists($simpleGroupId, $this->customShippingPrices)
                        && \array_key_exists($shippingId, $this->customShippingPrices[$simpleGroupId])
                    ) {
                        $customShippingPrice = $this->customShippingPrices[$simpleGroupId][$shippingId];
                        $isCustomShippingPrice = $customShippingPrice !== null;
                    }

                    // Original shipping price before free shipping
                    $originalPrice = $shippingPrice;
                    $shippingPrice = $customShippingPrice ?? $shippingPrice;
                    $carriagePaid = false;

                    // Free carriage threshold calculations
                    if (null !== $carriagePaidThreshold) {
                        $cacheKey = $company->getId() . "#" . $simpleGroupId;
                        // memoize
                        if (false === \array_key_exists($cacheKey, $totalBasketByCompanyIdAndSimpleGroup)) {
                            $totalBasketByCompanyIdAndSimpleGroup[$cacheKey]
                                = $this->getTotalBasketByCompanyIdAndShippingGroupId(
                                    $company->getId(),
                                    $simpleGroupId,
                                    $declinationsCache
                                );
                        }
                        if ($carriagePaidThreshold <= $totalBasketByCompanyIdAndSimpleGroup[$cacheKey]) {
                            $shippingPrice = 0;
                            $carriagePaid = true;
                        }
                    }

                    $selected = ($this->selectedShippings[$simpleGroupId] == $shippingId);

                    // Get min shipping price if no selected shipping
                    $selectedShippingId = $selected ? $shippingId : $selectedShippingId;
                    if (empty($minShipping) || $shippingPrice < $minShipping['price']) {
                        $minShipping = [
                            'id' => $shippingId,
                            'price' => $shippingPrice,
                        ];
                    }

                    $shippingTaxId
                        = $internationalTaxService->getTaxId($company->getCountry(), $company->getVatNumber(), $user);

                    if (0 === $shippingTaxId) {
                        $shippingTaxData = [0 => ['tax_subtotal' => '0']];
                    } else {
                        $shippingTaxes = [$shippingTaxId => fn_get_tax($shippingTaxId)];
                        $shippingTaxData = fn_calculate_tax_rates($shippingTaxes, $shippingPrice, 1, [], $cart);
                    }

                    // Fill data
                    $data['groups'][$company->getId()]['products'][$simpleGroupId]['shippings'][$shippingId] = [
                        'shippingName' => $shipping->getName(),
                        'shippingId' => $shipping->getId(),
                        'shippingPrice' => $shippingPrice,
                        'isCustomShippingPrice' => $isCustomShippingPrice ?? false,
                        'shippingTax' => $shippingTaxData[$shippingTaxId]['tax_subtotal'] ?: 0,
                        'shippingDeliveryTime' => $shipping->getDeliveryTime(),
                        'selected' => $selected,
                        'shippingType' => $shipping->getDeliveryType(),
                        'carriagePaidThreshold' => $carriagePaidThreshold,
                        'carriagePaid' => $carriagePaid,
                        'originalPrice' => $originalPrice,
                        'pickupPointId' => $this->groups[$company->getId()][$simpleGroupId]['pickupPointId'],
                        'title' => $this->groups[$company->getId()][$simpleGroupId]['title'],
                        'firstName' => $this->groups[$company->getId()][$simpleGroupId]['firstName'],
                        'lastName' => $this->groups[$company->getId()][$simpleGroupId]['lastName'],
                    ];
                }
                if (!$selectedShippingId && !empty($minShipping)) {
                    $data['groups'][$company->getId()]['products'][$simpleGroupId]['shippings'][$minShipping['id']]['selected'] = true;
                    $selectedShippingId = $minShipping['id'];
                }
                $shippingGroupTotal = $data['groups'][$company->getId()]['products'][$simpleGroupId]['shippings'][$selectedShippingId]['shippingPrice'];
                if ($shippingGroupTotal && null !== $shippingTaxId) {
                    if (0 === $shippingTaxId) {
                        $shippingGroupPrices->set(PriceFields::SHIPPING_TOTAL(), Money::fromVariable($shippingGroupTotal));
                        $shippingGroupPrices->set(PriceFields::SHIPPING_TAX(), new Money(0));
                    } else {
                        $taxInfos = \Wizacha\Marketplace\Entities\Tax::applyTaxes(Money::fromVariable($shippingGroupTotal), [new Tax($shippingTaxId)]);
                        $shippingGroupPrices->set(PriceFields::SHIPPING_TOTAL(), $taxInfos['allTaxesPrice']);
                        $shippingGroupPrices->set(PriceFields::SHIPPING_TAX(), $taxInfos['tax']);
                    }
                }

                $shippingGroupPrices->set(
                    PriceFields::TAXES(),
                    $shippingGroupPrices->get(PriceFields::BASKET_TAX())
                        ->add($shippingGroupPrices->get(PriceFields::SHIPPING_TAX()))
                );

                // subtotal for the shipping group
                $data['groups'][$company->getId()]['products'][$simpleGroupId]['prices'] = $shippingGroupPrices;
                $data['groups'][$company->getId()]['products'][$simpleGroupId]['original_prices'] = clone $shippingGroupPrices;
                $data['groups'][$company->getId()]['products'][$simpleGroupId]['pickupPointId'] = $this->groups[$company->getId()][$simpleGroupId]['pickupPointId'];
                $data['groups'][$company->getId()]['products'][$simpleGroupId]['title'] = $this->groups[$company->getId()][$simpleGroupId]['title'];
                $data['groups'][$company->getId()]['products'][$simpleGroupId]['firstName'] = $this->groups[$company->getId()][$simpleGroupId]['firstName'];
                $data['groups'][$company->getId()]['products'][$simpleGroupId]['lastName'] = $this->groups[$company->getId()][$simpleGroupId]['lastName'];
            }

            // Flatten all the basket items of the sub-groups
            $allItems = array_column($data['groups'][$companyId]['products'], 'products');
            $allItems = empty($allItems) ? [] : array_merge(...$allItems);
            $data['groups'][$companyId]['all_items'] = $allItems;
        }
        Basket::recalculateBasketDataPricesFromShippingGroup($data);

        foreach (array_keys($this->groups) as $companyId) {
            $data['groups'][$companyId]['original_prices'] = clone $data['groups'][$companyId]['prices']; // original prices without promotions
        }
    }

    protected function fillGroupProductData(PriceComposition $prices, Money &$basketTotal, &$tax, $simpleGroup, $productId, array $declination)
    {
        //Get only shippings possibly selected in this group
        $declination['shippings'] = array_filter(
            (array) $declination['shippings'],
            function ($productShippingRate) use ($simpleGroup) {
                return \in_array($productShippingRate->getShipping()->getId(), $simpleGroup['shippings']);
            }
        );
        $subtotal = Money::fromVariable($declination['reduced_price'])->multiply($this->products[$productId]['quantity'] ?? 0);
        $tax += $declination['tax'] * $this->products[$productId]['quantity'];
        $basketTotal = $basketTotal->add($subtotal);

        /** @var PriceComposition $productPrices */
        $productPrices = $prices->get(PriceFields::PRODUCTS());
        $productPrices[$declination['product_id']][$productId] = [
            PriceFields::PRODUCT_PRICE_WITH_TAX => $subtotal,
            PriceFields::PRODUCT_TAX => Money::fromVariable($declination['tax'] * $this->products[$productId]['quantity']),
        ];
        $prices->set(PriceFields::PRODUCTS(), $productPrices);

        return [
            'quantity' => $this->products[$productId]['quantity'],
            'declination' => $declination,
            'subtotal' => $subtotal->getPreciseConvertedAmount(),
            'comment' => $this->products[$productId]['comment'] ?? '',
        ];
    }

    protected function getShippingPriceForShippingId($condensed, $shippingId, $max)
    {
        if (\array_key_exists($shippingId, $condensed)) {
            $shippingData = $condensed[$shippingId];
        } else {
            $shippingData = [];
        }

        return array_reduce(
            $shippingData,
            function (&$result, $product) use ($max, $shippingId) {
                if ($product['product_id'] == $max[$shippingId]['id']) {
                    $result += $product['rate'][0] + $product['rate'][1] * ($product['amount'] - 1);
                } else {
                    $result +=  $product['rate'][1] * $product['amount'];
                }

                return $result;
            },
            0
        );
    }

    private function getCarriagePaidThreshold(int $shippingId, int $companyId): ?float
    {
        $request = db_get_row("SELECT carriage_paid_threshold
                                FROM cscart_w_company_shipping_rates
                                WHERE shipping_id = ?i
                                AND company_id = ?i", $shippingId, $companyId);

        return null !== $request['carriage_paid_threshold']
            ? (float) $request['carriage_paid_threshold']
            : null;
    }

    /**
     * @param array $datas
     * @param array $max
     * @return array
     */
    protected function generateShippingRateByProduct(array $datas, array &$max)
    {
        /*
         * return an array of type :  [
         *      shipping_id => [
         *          product_id => [
         *              product_id,
         *              rate,
         *              amount
         *          ]
         *      ]
         * ],
         *
         * And update max.
         */
        return array_reduce(
            $datas,
            function (&$result, $productData) use (&$max) {
                $productId = $productData['declination']['objectID'];
                foreach ($productData['declination']['shippings'] as $shippingRate) {
                    $shippingId = $shippingRate->getShipping()->getId();
                    $result[$shippingId][$productId] = [
                        'product_id' => $productId,
                        'rate' => [$shippingRate->getRateForFirst(), $shippingRate->getRateForNext()],
                        'amount' => $productData['quantity'],
                    ];
                    if ($result[$shippingId][$productId]['rate'][0] > $max[$shippingId]['value']) {
                        $max[$shippingId] = [
                            'id' => $productId,
                            'value' => $result[$shippingId][$productId]['rate'][0],
                        ];
                    }
                }

                return $result;
            },
            []
        );
    }

    /**
     * Rajoute les prix de panier et de shipping de  $added à $original si le champs existe dans $original
     */
    private static function incrementPriceComposition(PriceComposition $original, PriceComposition $added)
    {
        foreach (static::getDefaultFields() as $field) {
            if ($original->has($field) && $added->has($field)) {
                $original->set($field, $original->get($field)->add($added->get($field)));
            }
        }

        if ($added->has(PriceFields::PRODUCTS())) {
            $original->set(PriceFields::PRODUCTS(), ($original->get(PriceFields::PRODUCTS()) ?? []) + $added->get(PriceFields::PRODUCTS()));
        }
    }

    /**
     * Juste une factorisation de la création d'un priceComposition avec les données de shipping et de panier
     *
     * @return PriceComposition
     */
    private static function createCleanPriceComposition()
    {
        $priceComposition = new PriceComposition();
        foreach (static::getDefaultFields() as $field) {
            $priceComposition->set($field, new Money(0));
        }

        return $priceComposition;
    }

    /**
     * Retourne la liste des PriceFields attendu (a minima) dans un shippingGroup et un companyGroup
     */
    private static function getDefaultFields(): array
    {
        return [
            PriceFields::BASKET_TOTAL(),
            PriceFields::BASKET_TAX(),
            PriceFields::SHIPPING_TOTAL(),
            PriceFields::SHIPPING_TAX(),
            PriceFields::SHIPPING_REAL(),
        ];
    }
}
