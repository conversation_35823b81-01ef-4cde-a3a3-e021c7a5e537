<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\ReadModel;

use DateTimeImmutable;
use Wizacha\Marketplace\Catalog\AttributesValuesGroup;
use Wizacha\Marketplace\Catalog\AttributeValue;
use Wizacha\Marketplace\Catalog\Category\CategorySummary;
use Wizacha\Marketplace\Catalog\Company\CompanySummary;
use Wizacha\Marketplace\Catalog\Declination\Declination;
use Wizacha\Marketplace\Catalog\Declination\DeclinationImages;
use Wizacha\Marketplace\Catalog\Declination\DeclinationOption;
use Wizacha\Marketplace\Catalog\Geolocation;
use Wizacha\Marketplace\Catalog\Product\ProductCondition;
use Wizacha\Marketplace\Catalog\Product\ProductSummary;
use Wizacha\Marketplace\Company\CompanyType;
use Wizacha\Marketplace\Exception\DeclinationIdNotFoundInReadModelException;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Image\Image;
use Wizacha\Marketplace\PIM\Attribute\AttributeType;
use Wizacha\Marketplace\PIM\Stock\StockService;
use Wizacha\Marketplace\PIM\Tax\Tax;
use Wizacha\Marketplace\PIM\TransactionMode\TransactionMode;
use Wizacha\Marketplace\Price\PriceComposition;
use Wizacha\Marketplace\Price\PriceFields;
use Wizacha\Marketplace\SeoData;
use Wizacha\Marketplace\Traits\PriceTrait;
use Wizacha\Money\Money;
use Wizacha\Product as ProductEntity;
use Wizacha\Status;

/**
 * Catalog representation of a product.
 */
class Product implements ProductSummary, \JsonSerializable
{
    use PriceTrait;

    protected $data;

    /**
     * @var array
     * @see \Wizacha\Marketplace\ReadModel\Product::getData
     * It contains runtime data like stock and reduced prices
     * It's filled the first time getData is called
     */
    protected $cacheRuntimeData;

    public function __construct($data)
    {
        $this->data = $data;
    }

    public function __sleep(): array
    {
        // We don't want to serialize $cacheRuntimeData
        return ['data'];
    }

    /**
     * @return int|string|bool
     */
    public function getId()
    {
        if (\array_key_exists('product_id', $this->data)) {
            return $this->data['product_id'];
        }

        log_unused_code(__FILE__, __LINE__);

        return false;
    }

    public function getMultiVendorProductId(): ?string
    {
        return isset($this->data['multi_vendor_product_id']) ? (string) $this->data['multi_vendor_product_id'] : null;
    }

    /**
     * @return int|string
     */
    public function getProductId()
    {
        return $this->getId();
    }

    /**
     * Return true if that Product is in fact a MultiVendorProduct
     *
     * @return bool
     */
    public function isMultiVendorProduct(): bool
    {
        return (isset($this->data['product_id']) && \is_string($this->data['product_id']));
    }

    public function getName(): string
    {
        return (string) $this->data['product_name'];
    }

    public function getCode(): string
    {
        return (string) ($this->data['product_code'] ?? '');
    }

    public function getSupplierReference(): string
    {
        return (string) ($this->data['supplierReference'] ?? '');
    }

    public function getMaxPriceAdjustment(): ?int
    {
        return $this->data['max_price_adjustment'];
    }

    public function getSubtitle(): string
    {
        $description = strip_tags($this->getDescription());
        $description = html_entity_decode($description, ENT_QUOTES);

        return mb_strcut($description, 0, 300);
    }

    public function getMinimumPrice(): Money
    {
        return $this->getCurrentPrice();
    }

    /**
     * Returns the current minimum crossed out price of the product.
     * Priorité de renvoi du prix barré :
     *      Le prix barré de la déclinaison
     *      Le prix avant promotion s'il est différent du prix avec promotion
     *      Null sinon
     */
    public function getCrossedOutPrice(): ?Money
    {
        $crossedOutPrice = $this->getCurrentPriceComposition()->get(PriceFields::CROSSED_OUT_PRICE());
        if ($crossedOutPrice !== null && !$crossedOutPrice->isZero()) {
            return $crossedOutPrice;
        }

        return null;
    }

    public function isAvailable(): bool
    {
        if ($this->data['infinite_stock']) {
            return true;
        }

        foreach ($this->data['declinations'] as $declination) {
            if ($declination['amount'] > 0) {
                return true;
            }
        }

        return false;
    }

    /** @return float */
    public function getAverageRating(): float
    {
        return $this->data['average_rating'] ?? 0;
    }

    public function getDeclinationCount(): int
    {
        return \count($this->data['declinations']);
    }

    public function hasMultipleDeclinations(): bool
    {
        return \count($this->data['declinations']) > 1;
    }

    public function getAffiliateLink(): ?string
    {
        if (isset(reset($this->data['declinations'])['affiliate_link'])) {
            return reset($this->data['declinations'])['affiliate_link'];
        }

        return null;
    }

    /**
     * @return string
     */
    public function getDescription()
    {
        return $this->data['description'];
    }

    /**
     * @param bool $truncated If true, the content will be text only (no html) and truncated to 300 chars. Useful for Algolia
     */
    public function getShortDescription(bool $truncated = false): string
    {
        if ($truncated) {
            $description = strip_tags((string) $this->data['short_description']);
            $description = html_entity_decode($description, ENT_QUOTES);

            return mb_strcut($description, 0, 300);
        }

        return (string) $this->data['short_description'];
    }

    public function getMainImage(): ?Image
    {
        $images = $this->getImages();
        if (empty($images)) {
            return null;
        }

        $mainImage = reset($images);

        return \is_array($mainImage) === true && \array_key_exists('id', $mainImage) === true
            ? new Image($mainImage['id'], $mainImage['altText'] ?? null)
            : null;
    }

    /**
     * @return array[]
     */
    public function getImages(): array
    {
        return $this->data['images'];
    }

    /**
     * @return DeclinationImages[]
     */
    public function getDeclinationsImages(): array
    {
        $declinationsImages = [];

        foreach ($this->data['declinations'] as $declination) {
            // extract only declination image
            $images = array_diff(
                array_column($declination['images'], 'path', 'id'),
                array_column($this->getImages(), 'path', 'id')
            );

            if (!empty($images)) {
                $imagesId = \array_keys($images);

                $images = \array_filter(
                    $declination['images'],
                    function (array $image) use ($imagesId) {
                        if (\in_array($image['id'], $imagesId) === true) {
                            return true;
                        }
                        return false;
                    }
                );
                $declinationsImages[] = new DeclinationImages(
                    $declination['objectID'],
                    array_map(
                        function (array $image): Image {
                            return new Image($image['id'], $image['altText']);
                        },
                        $images
                    )
                );
            }
        }

        return $declinationsImages;
    }

    public function getVideo(): ?array
    {
        return $this->data['video'];
    }

    /**
     * @return string
     */
    public function getStatus()
    {
        return $this->data['status'];
    }


    public function getAvailabilityDate(): ?\DateTime
    {
        return $this->data['availability_date'];
    }

    public function getSeoData(): SeoData
    {
        if ($this->data['seo_data']) {
            return $this->data['seo_data'];
        }

        return new SeoData();
    }

    public function getUrl(): string
    {
        return $this->data['canonical_url'];
    }

    public function getShippings(): array
    {
        return reset($this->data['declinations'])['shippings'];
    }

    /**
     * Return companies sorted by lower product price
     *
     * @return CompanySummary[]
     */
    public function getCompanies(): array
    {
        // We isolate declinations with company informations
        $declinations = [];
        foreach ($this->data['declinations'] as $declination) {
            if (!isset($declination['company_id'], $declination['company_name'], $declination['company_slug'])) {
                continue;
            }
            $declinations[] = $declination;
        }

        // We sort declinations by lower price
        usort($declinations, function ($declinationA, $declinationB) {
            return (float) $declinationA['price'] <=> (float) $declinationB['price'];
        });

        // We construct the companies summaries array
        $summaries = [];
        $companiesIds = [];
        foreach ($declinations as $declination) {
            if (isset($companiesIds[$declination['company_id']])) {
                continue;
            }
            $summaries[] = CompanySummary::createFromDeclinationReadmodel($declination);
            $companiesIds[$declination['company_id']] = true;
        }

        return $summaries;
    }

    public function hasCompany(int $companyId): bool
    {
        foreach ($this->getCompanies() as $company) {
            if ($company->getId() == $companyId) {
                return true;
            }
        }

        return false;
    }
    /**
     * @return ProductCondition[]
     */
    public function getConditions(): array
    {
        $conditions = array_unique(array_column($this->getData(), 'condition'));

        return array_map(function ($condition) {
            return new ProductCondition($condition);
        }, $conditions);
    }

    /**
     * @deprecated
     * @see getAllDeclinations()
     */
    public function getDeclinations(): array
    {
        return $this->data['declinations'];
    }

    /**
     * @return Declination[]
     */
    public function getAllDeclinations(): array
    {
        return array_map(function (array $data) {
            return new Declination($data);
        }, $this->getData());
    }

    public function getMainDeclination(): Declination
    {
        $id = reset($this->data['declinations'])['objectID'];

        return $this->getDeclination($id);
    }

    public function getDeclination(string $declinationId): Declination
    {
        foreach ($this->getData() as $declination) {
            if ($declination['objectID'] === $declinationId) {
                return new Declination($declination);
            }
        }
        throw NotFound::fromId('Declination', $declinationId);
    }

    /**
     * Get all declinations corresponding to the given declination
     *
     * @param DeclinationOption[] $options<int, DeclinationOption>
     * @return Declination[]
     */
    public function filterDeclinationsByOptions(array $options): array
    {
        /** @var DeclinationOption[] $optionsMap<int, DeclinationOption> */
        $optionsMap = [];
        foreach ($options as $option) {
            $optionsMap[$option->getId()] = $option;
        }

        return array_filter($this->getAllDeclinations(), function (Declination $productDeclination) use ($optionsMap): bool {
            $productDeclinationOptions = $productDeclination->getOptions();
            if (\count($optionsMap) !== \count($productDeclinationOptions)) {
                // Number of options doesn't match, skip this declination
                return false;
            }

            // Search for other declinations with options 100% matching those of the given offer
            foreach ($productDeclinationOptions as $productDeclinationOption) {
                $referenceOption = $optionsMap[$productDeclinationOption->getId()] ?? null;
                if ($referenceOption === null) {
                    return false;
                }

                if ($referenceOption->getValueId() !== $productDeclinationOption->getValueId()) {
                    return false;
                }
            }

            return true;
        });
    }

    /**
     * Change the option of a declination of this product to return the new declination.
     *
     * For example given the "T-Shirt black XL" declination, when setting the "color" (option) to "red" (value)
     * we get the "T-Shirt red XL" declination. Other options (e.g. the size: XL) are not changed.
     */
    public function changeOption(Declination $declination, int $optionId, int $valueId): Declination
    {
        foreach ($this->getAllDeclinations() as $availableDeclination) {
            $found = true;
            foreach ($availableDeclination->getOptions() as $option) {
                if ($option->getId() === $optionId) {
                    $found = $found && ($option->getValueId() === $valueId);
                } else {
                    $currentOptionValue = $declination->getOption($option->getId())->getValueId();
                    $found = $found && ($option->getValueId() === $currentOptionValue);
                }
            }
            if ($found) {
                return $availableDeclination;
            }
        }
        throw NotFound::fromId('Declination option', $optionId);
    }

    /**
     * Take a declination and returns the similar declination sold by another company
     */
    public function getCompanyDeclination(Declination $declination, int $companyId): ?Declination
    {
        foreach ($this->getAllDeclinations() as $availableDeclination) {
            if ($availableDeclination->getCompany()->getId() === $companyId && $declination->getOptions() == $availableDeclination->getOptions()) {
                return $availableDeclination;
            }
        }

        return null;
    }

    public function getCreatedAt(): \DateTimeInterface
    {
        return DateTimeImmutable::createFromFormat('U', $this->data['timestamp'] ?? 0);
    }

    public function getUpdatedAt(): \DateTimeInterface
    {
        return DateTimeImmutable::createFromFormat('U', $this->data['updated_timestamp'] ?? 0);
    }

    public function getReadmodelUpdateTime(): \DateTimeInterface
    {
        return DateTimeImmutable::createFromFormat('U', $this->data['readmodel_updated_timestamp'] ?? 0);
    }

    public function getBestSellersScore(): int
    {
        return $this->data['best_sellers_score'] ?: 0;
    }

    public function isTransactional(): bool
    {
        return $this->data['is_transactional'];
    }

    public function getBrandName(): string
    {
        if ($brand = $this->getBrandAttribute()) {
            $values = $brand->getValues();

            return !empty($values[0]) ? $values[0]->getName() : '';
        }

        return '';
    }

    public function getBrandImage(): ?Image
    {
        if ($brand = $this->getBrandAttribute()) {
            $values = $brand->getValues();

            return !empty($values[0]) ? $values[0]->getImage() : null;
        }

        return null;
    }

    public function getDeclinationOptions(): array
    {
        $options = [];

        foreach ($this->getDeclinations() as $declination) {
            foreach ($declination['declination'] as $option) {
                $optionId = $option['option_id'];
                $options[$optionId]['name'] = $option['option_name'];
                $options[$optionId]['code'] = $option['code'];
                $options[$optionId]['values'][$option['value_id']] = $option['value_name'];
                $options[$optionId]['option_position'] = $option['option_position'];
                $options[$optionId]['images'][$option['value_id']] = $option['image_id'];
                $options[$optionId]['positions'][$option['value_id']] = $option['position'];
            }
        }

        return $options;
    }

    /**
     * Merged all declination prices and keep the *minimum* price for each interval.
     * @return PriceComposition[]
     */
    public function getPriceCalendar(array $declinations = []): array
    {
        $dataDeclinations = \count($declinations) > 0 ? $declinations : $this->data['declinations'];

        // Get only declination with amount greater than 0 or has infinite stock
        $declinations = array_filter($dataDeclinations, function ($declination) {
            return $declination['infinite_stock'] || (int) $declination['amount'] > 0;
        });

        if (\count($declinations) === 0) {
            $declinations = $dataDeclinations;
        }
        // Regroup all price_calendar in same array
        $prices = array_column($declinations, 'price_calendar');

        // Get all times intervals
        $dates = array_map('array_keys', $prices);
        $dates = array_unique(\call_user_func_array('array_merge', $dates));
        sort($dates);
        $currentKeys = [];

        $final = [];
        foreach ($dates as $date) {
            //Update key for each declination
            foreach ($prices as $key => $price) {
                if (\array_key_exists($date, $price)) {
                    $currentKeys[$key] = $date;
                }
            }
            //Get prices for the current date
            $currentPrices = array_map(function ($key, $prices) {
                return $prices[$key];
            }, $currentKeys, $prices);
            $final[$date] = $this->getMinPriceComposition($currentPrices);
        }

        return $final;
    }

    public function getInternalData(): array
    {
        return $this->data;
    }

    public function getData(): array
    {
        if ($this->cacheRuntimeData !== null) {
            return $this->cacheRuntimeData;
        }

        $stockService = container()->get('marketplace.stock.domain_service');

        $this->cacheRuntimeData = $this->data['declinations'];

        if (false === \is_array($this->cacheRuntimeData)) {
            return [];
        }

        // Recalculate stock : Hard to stock up to date information about promotion in readmodel.
        // If we have more than 10 declinations, we use an other method to get the realtime stock: this mode have a constant time but higher than one simple get
        if (\count($this->cacheRuntimeData) > 10) {
            $declinations = array_map(function ($declination) {
                return \Wizacha\Marketplace\Entities\Declination::fromId($declination['objectID']);
            }, $this->cacheRuntimeData);

            $stocks = $stockService->getMultipleRealtimeStock($declinations);

            foreach ($this->cacheRuntimeData as &$declinationData) {
                $declinationData['amount'] = $stocks[$declinationData['objectID']] ?? 0;
            }
        } else {
            foreach ($this->cacheRuntimeData as &$declinationData) {
                $declination = \Wizacha\Marketplace\Entities\Declination::fromId($declinationData['objectID']);
                $declinationData['amount'] = $stockService->getRealTimeStock($declination);
            }
        }

        foreach ($this->cacheRuntimeData as &$declinationData) {
            $declinationData = $this->formattedDeclination($declinationData);
        }

        usort(
            $this->cacheRuntimeData,
            function ($declinationA, $declinationB) {
                if ($declinationA['amount'] > 0 == $declinationB['amount'] > 0) {
                    // Stock status is the same, check the price
                    if ($declinationA['final_price'] != $declinationB['final_price']) {
                        // Price are different, sort by price
                        return $declinationA['final_price'] <=> $declinationB['final_price'];
                    }

                    // Same prices, sort by position
                    return $declinationA['position'] <=> $declinationB['position'];
                }
                if (!$declinationA['amount']) {
                    // Stock status are different, and A doesn't have stock. B is better, 1
                    return 1;
                }
                // Stock status are different, and B doesn't have stock. A is better, -1
                return -1;
            }
        );

        return $this->cacheRuntimeData;
    }

    /**
     * Return raw declination info from readmodel
     */
    public function getRawDeclinationInfo(string $declinationId): array
    {
        foreach ($this->data['declinations'] as $declination) {
            if ($declinationId === $declination['objectID']) {
                return $declination;
            }
        }

        throw new DeclinationIdNotFoundInReadModelException($declinationId);
    }

    /**
     * Returns the current minimum price of the product.
     */
    public function getCurrentPrice(): Money
    {
        return $this->getCurrentPriceComposition()->get(PriceFields::BASE_PRICE());
    }

    /**
     * @return CategorySummary[]
     */
    public function getCategoryPath(): array
    {
        return array_map(function (array $categoryData) {
            return new CategorySummary($categoryData['id'], $categoryData['name'], $categoryData['slug'], $categoryData['position'] ?? 0);
        }, $this->data['category_path'] ?? []);
    }

    public function getSlug(): string
    {
        return $this->getSeoData()->getSlug();
    }

    public function getCategorySlugPath(): string
    {
        $categoriesData = $this->data['category_path'] ?? [];

        return implode('/', array_column($categoriesData, 'slug'));
    }

    /**
     * @return AttributeValue[]
     * @deprecated
     * @see getGroupedAttributesValues
     * @see getUngroupedAttributesValues
     */
    public function getAttributes(): array
    {
        return $this->data['attributes'];
    }

    /**
     * Get all attributes and their values, aggregated in their respective groups.
     * For attributes not in a group, @see getUngroupedAttributesValues()
     * @return AttributesValuesGroup[]
     */
    public function getGroupedAttributesValues(): array
    {
        return $this->data['groupedAttributesValues'] ?? [];
    }

    /**
     * Get all attributes and their values which are not in a group.
     * @return AttributeValue[]
     */
    public function getUngroupedAttributesValues(): array
    {
        return $this->data['ungroupedAttributesValues'] ?? [];
    }

    public function getGreenTax(): Money
    {
        $value = $this->data['green_tax'] ?? 0;

        return  Money::fromVariable($value);
    }

    /** @return Tax[] */
    public function getTaxes(): array
    {
        return $this->data['taxes'];
    }

    public function getBrandAttribute(): ?AttributeValue
    {
        foreach ($this->getAttributes() as $attribute) {
            if ($attribute->getAttribute()->getType()->equals(AttributeType::LIST_BRAND())) {
                return $attribute;
            }
        }

        return null;
    }

    public function getTransactionMode(): TransactionMode
    {
        return new TransactionMode($this->data['transaction_mode'] ?? TransactionMode::TRANSACTIONAL);
    }

    public function getGeolocation(): ?Geolocation
    {
        if (!empty($this->data['geoloc']) && $this->data['geoloc'][0] !== null) {
            return new Geolocation(
                (float) $this->data['geoloc'][0],
                (float) $this->data['geoloc'][1],
                (string) $this->data['geoloc'][2],
                (string) $this->data['geoloc'][3]
            );
        }

        return null;
    }

    /**
     * Return geolocation coordinates
     *
     * @return Geolocation[]
     */
    public function getGeolocations(): ?array
    {
        // return an array of geolocations
        $geolocs = [];
        foreach ($this->getData() as $data) {
            $geoloc = $data['geoloc'];
            // for some reason getDate returns twice each lines, so i use a md5 checksum.
            $checksum = md5(serialize($data['geoloc']));
            if (!empty($geoloc) && $geoloc[0] !== null) {
                $geolocs[$checksum] = new Geolocation(...$geoloc);
            }
        }

        return array_values($geolocs);
    }

    public function getWeight(): float
    {
        return $this->data['weight'] ?? .0;
    }

    /**
     * @return CompanyType[]
     */
    public function getCompaniesType(): array
    {
        $types = [];
        foreach ($this->data['declinations'] as $declination) {
            $id = (int) $declination['company_id'];
            if (isset($types[$id])) {
                continue;
            }

            $types[$id] = new CompanyType($declination['company_type']);
        }

        return array_values(array_unique(($types)));
    }

    public function getAttachments(): array
    {
        return $this->data['attachments'] ?? [];
    }

    public function getMainDeclinationId(): ?string
    {
        return $this->getMainDeclination()->getId();
    }

    public function hasInfiniteStock(): bool
    {
        return $this->data['infinite_stock'] ?? false;
    }

    public function isSubscription(): bool
    {
        return $this->data['is_subscription'] ?? false;
    }

    public function isRenewable(): bool
    {
        return $this->data['is_renewable'] ?? false;
    }

    public function getProductTemplateType(): ?string
    {
        return $this->data['product_template_type'] ?? null;
    }

    public function getOffers(): array
    {
        $divisions = [];
        $priceTiers = null;
        $keyForCorrectPriceInOffers = 'includingTaxes';
        if (\is_array($this->data['taxes'])
            && \count($this->data['taxes']) > 0
            && \reset($this->data['taxes'])['price_includes_tax'] == 'N'
        ) {
            $keyForCorrectPriceInOffers = 'excludingTaxes';
        }

        foreach ($this->data['declinations'] as $declination) {
            $price = $this->getCurrentPriceComposition($this->getDeclinationsByProductId($declination['product_id']))
                ->get(PriceFields::BASE_PRICE())
                ->getConvertedAmount();

            // In case of old ReadModel, we need to know if the key exist, else build it manually
            if (\array_key_exists('price_tiers', $declination) === false) {
                $declination['price_tiers'] = container()->get('marketplace.price_tier.price_tier_service')->buildAndExposePriceTiersWithTaxesForReadModel($declination['product_id'], $declination['objectID']);
            }

            // Set it the first time, and overide if PriceTiers[0] is smaller.
            if ($priceTiers === null || $declination['price_tiers'][0][$keyForCorrectPriceInOffers] === $price) {
                $priceTiers = $declination['price_tiers'];
            }

            $divisions[$declination['product_id']] = [
                'productId' => $declination['product_id'],
                'companyId' => $declination['company_id'],
                'price' => $price,
                'status' => $declination['status'],
                'divisions' => $declination['divisions'],
                'priceTiers' => $priceTiers,
            ];
        }

        return array_values($divisions);
    }

    public function getDeclinationsByProductId(string $productId): array
    {
        return array_filter(
            $this->data['declinations'],
            function (array $declination) use ($productId): bool {
                return strpos($declination['objectID'], $productId . '_') === 0;
            }
        );
    }

    /**
     * Returns the quantity of product (amount in declinaison)
     */
    public function getInventoryAmount(): int
    {
        $amount = 0;
        foreach ($this->getDeclinations() as $inventory) {
            $amount += \intval($inventory['amount']);
        }

        return $amount;
    }

    public function formattedDeclination($declinationData)
    {
        $declinationData['seo_data'] = $this->getSeoData();
        $declinationData['category_path'] = $this->getCategoryPath();

        $declinationData['in_stock'] = false;
        $declinationData['out_stock'] = $declinationData['in_stock'];
        $declinationData['unavailable'] = $declinationData['out_stock'];
        $declinationData['hide_add_to_cart_data'] = $declinationData['unavailable'];

        if (($declinationData['status'] === Status::ENABLED || $declinationData['status'] === Status::HIDDEN)
            && $this->isPriceValid($declinationData['price']) === true
        ) {
            if ($declinationData['amount']) {
                $declinationData['in_stock'] = true;
            } else {
                $declinationData['out_stock'] = true;
            }
        } else {
            $declinationData['unavailable'] = true;
            $declinationData['hide_add_to_cart_data'] = true;
        }

        // Calculate the current reduced_price using the price calendar
        $now = time();
        /** @var PriceComposition $price */
        if (\count($declinationData['price_calendar']) > 1) {
            $priceEnd = end(array_keys($declinationData['price_calendar']));

            foreach ($declinationData['price_calendar'] as $priceStart => $price) {
                if ($now > $priceStart && $now < $priceEnd) {
                    $this->setPriceData($declinationData, $price);
                }
            }
        }

        if (\count($declinationData['price_calendar']) === 0 || false === \array_key_exists('prices', $declinationData)) {
            $this->setPriceData($declinationData, reset($declinationData['price_calendar']));
        }

        // Si il n'y a pas de prix barré explicitement défini, alors on on regarde si y'a une promo
        if (empty($declinationData['crossed_out_price']) && !empty($declinationData['price'])) {
            $priceBeforePromotion = Money::fromVariable($declinationData['price']);
            $priceAfterPromotion = Money::fromVariable($declinationData['final_price']);
            if (!$priceBeforePromotion->equals($priceAfterPromotion)) {
                $declinationData['crossed_out_price'] = $priceBeforePromotion->getConvertedAmount();
            }
        }

        $declinaison = str_replace("combination_code=", '', $declinationData['_tags'][1]);
        $product_option_inventory = reset(ProductEntity::getInventoryByFilter(['product_id' => $declinationData['product_id'], 'combination' => $declinaison]));
        $declinationData['supplierReference'] = $product_option_inventory['supplier_reference'];

        return $declinationData;
    }

    public function setDeclinations(array $declinations): self
    {
        $this->data['declinations'] = $declinations;

        return $this;
    }

    public function jsonSerialize(): array
    {
        return $this->data;
    }

    private function setPriceData(array &$declinationData, PriceComposition $price): self
    {
        $declinationData['final_price'] = $price->get(PriceFields::BASE_PRICE())->getConvertedAmount();
        $declinationData['final_price_with_shipping'] = $declinationData['final_price'] + $declinationData['cheapest_shipping'];

        if ($price->has(PriceFields::PRODUCT_PRICE_WITH_TAX()) && $price->has(PriceFields::PRODUCT_TAX())) {
            // On stock tout dans un tableau prices, notamment pour l'API.
            // On garde pour l'instant l'ancien tableau pour garantir le
            // fonctionnement du code.
            $declinationData['prices'] = [
                'priceWithTaxes' => $price->get(PriceFields::PRODUCT_PRICE_WITH_TAX())->getConvertedAmount(),
                'priceWithoutVat' => $price->get(PriceFields::PRODUCT_PRICE_WITH_TAX())->subtract($price->get(PriceFields::PRODUCT_TAX()))->getConvertedAmount(),
                'vat' => $price->get(PriceFields::PRODUCT_TAX())->getConvertedAmount(),
            ];
            $declinationData['final_price_with_tax'] = $declinationData['prices']['priceWithTaxes'];
            $declinationData['final_price_tax'] = $declinationData['prices']['vat'];

            //On ne fait pas encore une distinction fine entre les Taxes de manière générale et la TVA
            $declinationData['final_price_without_tax'] = $declinationData['prices']['priceWithoutVat'];
        }

        return $this;
    }

    /**
     * Like min() but for PriceComposition (use only BASE_PRICE to make comparison)
     * Exclude price at 0 from calcul
     *
     * @param PriceComposition[] $pricesComposition
     * @return PriceComposition
     */
    private function getMinPriceComposition(array $pricesComposition)
    {
        $showZeroPriceProducts = container()->getParameter('feature.catalog.show_zero_price_products');

        //array_reduce commence par initialiser $min avec un `reset($priceComposition)`
        //$min peut donc être à 0
        return  array_reduce(
            $pricesComposition,
            function (PriceComposition $minPrice, PriceComposition $currentPrice) use ($showZeroPriceProducts) {
                if ($showZeroPriceProducts === false) {
                    if ($currentPrice->get(PriceFields::BASE_PRICE())->isZero()) {
                        return $minPrice;
                    }

                    if ($minPrice->get(PriceFields::BASE_PRICE())->isZero()) {
                        return $currentPrice;
                    }
                } else {
                    if ($currentPrice->get(PriceFields::BASE_PRICE())->isNegative()) {
                        return $minPrice;
                    }

                    if ($minPrice->get(PriceFields::BASE_PRICE())->isNegative()) {
                        return $currentPrice;
                    }
                }

                if ($currentPrice->get(PriceFields::BASE_PRICE())->lessThan($minPrice->get(PriceFields::BASE_PRICE()))) {
                    return $currentPrice;
                }

                return $minPrice;
            },
            reset($pricesComposition)
        );
    }

    /**
     * Returns the current minimum price composition
     */
    private function getCurrentPriceComposition(array $declinations = []): PriceComposition
    {
        $priceCalendar = $this->getPriceCalendar($declinations);
        ksort($priceCalendar);
        $priceCalendar = array_reverse($priceCalendar, true);
        $currentTime = time();

        foreach ($priceCalendar as $priceTime => $price) {
            if ($currentTime >= $priceTime) {
                return $price;
            }
        }

        return reset($priceCalendar);
    }

    public function getRelatedOffers(): array
    {
        return $this->data['relatedOffers'] ?? [];
    }

    public function setRelatedOffers(array $related): self
    {
        $this->data['relatedOffers'] = $related;

        return $this;
    }

    public function getQuoteRequestsMinQuantity(): int
    {
        return $this->data['quoteRequestsMinQuantity'] ?? 1;
    }

    public function isExclusiveToQuoteRequests(): bool
    {
        return $this->data['isExclusiveToQuoteRequests'] ?? false;
    }

    public function setVersion(int $version): self
    {
        $this->data['version'] = $version;

        return $this;
    }
}
