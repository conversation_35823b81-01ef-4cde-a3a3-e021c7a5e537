<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Favorite;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Exception\ConstraintViolationException;
use Wizacha\Marketplace\Catalog\Product\ProductService;
use Wizacha\Marketplace\Entities\Declination;
use Wizacha\User;

/**
 * Class FavoriteService
 *
 */
class FavoriteService
{
    /**
     * @var Connection
     */
    private $db;

    /** @var ProductService  */
    private $productService;

    public function __construct(Connection $db, ProductService $productService)
    {
        $this->db = $db;
        $this->productService = $productService;
    }

    /**
     * @throws CannotFavoriteDisabledOrInexistantDeclination
     * @throws FavoriteAlreadyExists
     */
    public function addDeclinationToUserFavorites(User $user, Declination $declination)
    {
        if (!$declination->isActive()) {
            throw new CannotFavoriteDisabledOrInexistantDeclination($declination->getId());
        }
        try {
            $this->db->executeUpdate(
                'INSERT INTO doctrine_declination_favorite
                    (`user_id`, `declination_id`) 
                    VALUES (?, ?)',
                [$user->getId(), $declination->getId()]
            );
        } catch (ConstraintViolationException $e) {
            throw new FavoriteAlreadyExists($declination->getId(), $user->getId(), $e);
        }
    }

    /**
     * @return UserDeclinationFavorite[]
     */
    public function getFavoritesByUser(User $user): array
    {
        $stmt = $this->db->executeQuery('SELECT user_id, declination_id FROM doctrine_declination_favorite WHERE `user_id`=?', [$user->getId()]);
        $favorites = $stmt->fetchAll();


        //Return un tableau
        return array_map(function (array $favorite) use ($user): UserDeclinationFavorite {
            $declination = Declination::fromId($favorite['declination_id']);
            if ($declination->isActive()) {
                return new UserDeclinationFavorite($user->getId(), $declination->getId(), $this->productService->getProduct($declination->getProductId()));
            }

            return new UserDeclinationFavorite($user->getId(), $declination->getId());
        }, $favorites);
    }

    public function removeDeclinationFromUserFavorites(User $user, Declination $declination)
    {
        $this->db->executeQuery('DELETE FROM doctrine_declination_favorite WHERE `user_id`=? AND `declination_id`=?', [$user->getId(), $declination->getId()]);
    }
}
