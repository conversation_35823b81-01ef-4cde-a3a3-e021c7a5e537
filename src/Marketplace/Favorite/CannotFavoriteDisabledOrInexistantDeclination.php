<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Favorite;

use Symfony\Component\HttpFoundation\Response;
use Wizacha\AppBundle\Controller\Api\Error\ApiErrorResponseProvider;
use Wizacha\Marketplace\Exception\ErrorCode;
use Wizacha\AppBundle\Controller\Api\Error\ErrorResponse;

class CannotFavoriteDisabledOrInexistantDeclination extends \Exception implements ApiErrorResponseProvider
{
    /**
     * @var string
     */
    private $declinationId;

    public function __construct(string $declinationId)
    {
        parent::__construct('declination is not active');
        $this->declinationId = $declinationId;
    }

    public function toApiErrorResponse(): ErrorResponse
    {
        return new ErrorResponse(
            ErrorCode::DECLINATION_IS_NOT_ACTIVE(),
            'declination is not active',
            [
                'declinationId' => $this->declinationId,
            ],
            Response::HTTP_BAD_REQUEST
        );
    }
}
