<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Favorite;

use Symfony\Component\HttpFoundation\Response;
use Wizacha\AppBundle\Controller\Api\Error\ApiErrorResponseProvider;
use Wizacha\Marketplace\Exception\ErrorCode;
use Wizacha\AppBundle\Controller\Api\Error\ErrorResponse;

class FavoriteAlreadyExists extends \Exception implements ApiErrorResponseProvider
{
    /**
     * @var string
     */
    private $declinationId;

    /**
     * @var int
     */
    private $userId;

    public function __construct(string $declinationId, int $userId, \Throwable $previous = null)
    {
        parent::__construct('favorite already exists', ErrorCode::FAVORITE_ALREADY_EXISTS()->getValue(), $previous);
        $this->declinationId = $declinationId;
        $this->userId = $userId;
    }


    public function toApiErrorResponse(): ErrorResponse
    {
        return new ErrorResponse(
            ErrorCode::FAVORITE_ALREADY_EXISTS(),
            'favorite already exists',
            [
                'declinationId' => $this->declinationId,
                'userId' => $this->userId,
            ],
            Response::HTTP_CONFLICT
        );
    }
}
