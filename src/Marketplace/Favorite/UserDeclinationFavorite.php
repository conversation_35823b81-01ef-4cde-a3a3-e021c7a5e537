<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Favorite;

use Wizacha\Marketplace\ReadModel\Product;
use Wizacha\User;

class UserDeclinationFavorite
{
    /** @var  User */
    private $userId;

    /** @var string  */
    private $declinationId;

    /** @var Product  */
    private $productReadmodel;

    public function __construct(int $userId, string $declinationId, Product $productReadmodel = null)
    {
        $this->userId = $userId;
        $this->declinationId = $declinationId;
        $this->productReadmodel = $productReadmodel;
    }

    /**
     * @return string
     */
    public function getDeclinationId(): string
    {
        return $this->declinationId;
    }

    /**
     * @return Product
     */
    public function getProductReadmodel(): Product
    {
        return $this->productReadmodel;
    }

    public function hasReadmodel(): bool
    {
        return \boolval($this->productReadmodel);
    }
}
