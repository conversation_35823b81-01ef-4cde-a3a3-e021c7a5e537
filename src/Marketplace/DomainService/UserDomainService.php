<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\DomainService;

use Symfony\Component\DependencyInjection\ContainerAwareInterface;
use Symfony\Component\DependencyInjection\ContainerAwareTrait;
use Wizacha\User;

/**
 * TODO Déplacer dans AppBundle
 */
class UserDomainService implements ContainerAwareInterface
{
    use ContainerAwareTrait;

    /**
     * @return User
     */
    public function getLoggedUser()
    {
        $userInfo = $this->container->get('wizacha.registry')->get(['user_info']);
        if ($userInfo) {
            return new User($userInfo['user_id']);
        }
    }

    /**
     * @param User $user
     * @param bool $inCheckout
     */
    public function setUserBasket(User $user, $inCheckout = false)
    {
        if ($inCheckout) {
            $this->setSessionBasketToUser($user);
        } else {
            $this->mergeUserBasketWithSessionBasket($user);
        }
    }

    /**
     * @param User $user
     */
    public function mergeUserBasketWithSessionBasket(User $user)
    {
        $session = $this->container->get('session');
        $basketService = $this->container->get('marketplace.basket.domain_service');
        if ($user->getBasketId()) {
            if ($session->get('basketId') && $user->getBasketId() != $session->get('basketId')) {
                $basketService->mergeBasket($session->get('basketId'), $user->getBasketId());
                $user->setBasketId($session->get('basketId'));
            } else {
                $session->set('basketId', $user->getBasketId());
            }
        } elseif ($session->get('basketId')) {
            $user->setBasketId($session->get('basketId'));
        }
    }

    /**
     * @param User $user
     */
    public function setSessionBasketToUser(User $user)
    {
        $session = $this->container->get('session');
        if ($session->get('basketId') && $user->getBasketId() != $session->get('basketId')) {
            $user->setBasketId($session->get('basketId'));
        }
    }
}
