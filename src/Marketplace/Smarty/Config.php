<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Smarty;

use Tygh\Backend\Cache\ABackend;
use Wizacha\Cache\Handler;

/**
 * @deprecated
 * @see \Wizacha\Smarty\SmartyFactory
 */
class Config
{
    public const CONF_SERVICE              = 'smarty';
    public const CONF_CACHE_DIRECTORY      = 'cache';
    public const CONF_COMPILE_DIRECTORY    = 'compile';

    /**
     * @param ABackend $cache
     * @param string[] $subKeys Set of keys to construct the cache id
     * @return string
     */
    public static function cacheId(ABackend $cache, array $subKeys = [])
    {
        $globalCacheId = $cache->getHandlerId(Handler::SMARTY);
        array_unshift($subKeys, $globalCacheId);
        $cacheId = implode('', $subKeys);

        return base_convert(md5($cacheId), 16, 36);
    }
}
