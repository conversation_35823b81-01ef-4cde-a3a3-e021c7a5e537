<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Price;

use MyCLabs\Enum\Enum;

/**
 * @method static PriceFields SHIPPING()
 * @method static PriceFields BASE_PRICE()
 * @method static PriceFields SHIPPING_FIRST_PRICE()
 * @method static PriceFields SHIPPING_SECOND_PRICE()
 * @method static PriceFields SHIPPING_TOTAL()
 * @method static PriceFields SHIPPING_REAL()
 * @method static PriceFields BASKET_TOTAL()
 * @method static PriceFields PRODUCTS()
 * @method static PriceFields TAXES()
 * @method static PriceFields BASKET_TAX()
 * @method static PriceFields SHIPPING_TAX()
 * @method static PriceFields PRODUCT_PRICE_WITH_TAX()
 * @method static PriceFields PRODUCT_TAX()
 * @method static PriceFields CROSSED_OUT_PRICE()
 * @method static PriceFields BASKET_DISCOUNT()
 * @method static PriceFields BASKET_DISCOUNT_MARKETPLACE()
 * @method static PriceFields BASKET_TOTAL_TAXES()
 * @method static PriceFields BASKET_SUBTOTAL()
 */
class PriceFields extends Enum
{
    // The shipping contains SHIPPING_FIRST_PRICE and SHIPPING_SECOND_PRICE
    public const SHIPPING = 'shipping';

    //Product price, with taxes
    public const BASE_PRICE = 'base_price';

    // The shipping price for the first item
    public const SHIPPING_FIRST_PRICE = 'shipping_first_price';

    // The shipping price for each additional item
    public const SHIPPING_SECOND_PRICE = 'shipping_second_price';

    // The shipping total
    public const SHIPPING_TOTAL = 'shipping_total';

    // The shipping without promotion
    public const SHIPPING_REAL = 'shipping_real';

    public const SHIPPING_TAX = 'shipping_tax';

    // La notion de 'basket' s'entend comme liste de produit qu'on aurait dans un panier. Les frais de ports sont une
    // problématique de la commande et ne font pas partie de la notion de basket.
    // Globalement, la séparation est la même que dans les promos où un bonus s'applique soit :
    //     au 'produit du panier' (donc l'info stockée dans BASKET_TOTAL)
    //     au 'frais de livraison' (donc SHIPPING_TOTAL)
    public const BASKET_TOTAL = 'basket_total';

    //Basket Tax (voir l'explication de BASKET_TOTAL)
    public const BASKET_TAX = 'basket_tax';

    //Use for products
    public const PRODUCT_PRICE_WITH_TAX = "product_price_with_tax";

    //Use for products
    public const PRODUCT_TAX = "product_tax";

    //Represents sum of basket and shipping tax
    public const TAXES = 'taxes';

    // The basket products
    public const PRODUCTS = 'products';

    // The crossed out price
    public const CROSSED_OUT_PRICE = 'crossed_out_price';

    // The basket discounts
    public const BASKET_DISCOUNT = 'subtotal_discount';

    // The basket marketplace discount
    public const BASKET_DISCOUNT_MARKETPLACE = 'marketplace_discount_total';

    public const BASKET_TOTAL_TAXES = 'tax';

    public const BASKET_SUBTOTAL = 'original_subtotal';
}
