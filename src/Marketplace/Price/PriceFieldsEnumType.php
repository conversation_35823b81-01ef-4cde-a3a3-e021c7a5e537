<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Price;

use Acelaya\Doctrine\Type\AbstractPhpEnumType;

class PriceFieldsEnumType extends AbstractPhpEnumType
{
    protected $enumType = PriceFields::class;

    /**
     * @return string
     */
    protected function getSpecificName()
    {
        return 'price_fields';
    }
}
