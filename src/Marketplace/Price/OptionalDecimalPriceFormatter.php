<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Price;

class OptionalDecimalPriceFormatter implements PriceFormatter
{
    /**
     * @var string
     */
    protected $currencySign;

    public function __construct(string $currencySign)
    {
        $this->currencySign = $currencySign;
    }

    public function formatFloat(float $price): string
    {
        $decimal = floor($price) == round($price, 2) ? 0 : 2;

        return number_format($price, $decimal, ',', ' ') . ' ' . $this->currencySign;
    }
}
