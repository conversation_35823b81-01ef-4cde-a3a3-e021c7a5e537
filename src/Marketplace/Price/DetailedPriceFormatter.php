<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Price;

use Wizacha\Marketplace\GlobalState\GlobalState;

class DetailedPriceFormatter implements PriceFormatter
{
    /**
     * @var string
     */
    protected $currencyCode;

    /**
     * @var \NumberFormatter
     */
    private $formatter;

    public function __construct(string $currencyCode)
    {
        $this->currencyCode = $currencyCode;
    }

    public function formatFloat(float $price): string
    {
        /*
         * replace NARROW NO-BREAK SPACE (unicode 8239) with NO-BREAK SPACE (unicode 160)
         * NARROW NO-BREAK SPACE is introduced by ICU 63 as the grouping separator
         * @see http://site.icu-project.org/download/63
         */
        return str_replace(' ', ' ', $this->getFormatter()->formatCurrency($price, $this->currencyCode));
    }

    private function getFormatter(): \NumberFormatter
    {
        $locale = (string) GlobalState::interfaceLocale();

        // On met en cache l'instance du number formatter car ça prend ~9ms
        // pour 50 appels sur la home de cash converters
        if ($this->formatter && $this->formatter->getLocale() === $locale) {
            return $this->formatter;
        }

        $this->formatter = \NumberFormatter::create($locale, \NumberFormatter::CURRENCY);

        return $this->formatter;
    }
}
