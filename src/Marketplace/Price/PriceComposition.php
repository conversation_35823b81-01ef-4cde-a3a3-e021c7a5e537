<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Price;

use Wizacha\Marketplace\ReadModel\ReadModelObjectId;
use Wizacha\Money\Money;

class PriceComposition implements \JsonSerializable
{
    protected $prices = [];

    /**
     * @param PriceFields $field
     * @return bool
     */
    public function has(PriceFields $field)
    {
        return \array_key_exists($field->getValue(), $this->prices);
    }

    /**
     * @param PriceFields $field
     * @return Money|array|null
     */
    public function get(PriceFields $field)
    {
        return $this->has($field) ? $this->prices[$field->getValue()] : null;
    }

    /**
     * @param PriceFields $field
     * @param Money|PriceComposition|array $value
     * @return $this
     */
    public function set(PriceFields $field, $value)
    {
        if (!$this->isValidValue($value)) {
            throw new \InvalidArgumentException();
        }

        $this->prices[$field->getValue()] = $value;

        return $this;
    }

    /**
     * @param PriceFields $field
     */
    public function remove(PriceFields $field)
    {
        unset($this->prices[$field->getValue()]);
    }

    /**
     * @return array[]
     */
    public function jsonSerialize(): array
    {
        return [
            ReadModelObjectId::PRICE_COMPOSITION()->getValue() => $this->prices,
        ];
    }

    /**
     * Value must be a Money or an array of Money
     * @param mixed $value
     * @return bool
     */
    protected function isValidValue($value)
    {
        if (is_a($value, Money::class) || is_a($value, PriceComposition::class)) {
            return true;
        }
        if (\is_array($value)) {
            foreach ($value as $subvalue) {
                if (!$this->isValidValue($subvalue)) {
                    return false;
                }
            }

            return true;
        }

        return false;
    }
}
