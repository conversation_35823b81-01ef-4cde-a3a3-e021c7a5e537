Wizacha\Marketplace\Group\Group:
    type: entity
    table: groups
    id:
        id:
            type: guid
            generator:
                strategy: UUID
    fields:
        name:
            type: string
            nullable: false
            unique: true
            length: 255
            column: name
    manyToMany:
        users:
            targetEntity: Wizacha\Marketplace\User\User
            inversedBy: groups
            joinTable:
                name: user_groups
                joinColumns:
                    group_id:
                        referencedColumnName: id
                        onDelete: CASCADE
                inverseJoinColumns:
                    user_id:
                        referencedColumnName: user_id
