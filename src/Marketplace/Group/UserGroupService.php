<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Group;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Wizacha\Async\Dispatcher;
use Wizacha\Component\Import\EximJob;
use Wizacha\Component\Import\EximJobService;
use Wizacha\Component\Import\Uploader\UploaderInterface;
use Wizacha\Component\Pagination\Pagination;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Group\Exception\NotEmptyGroupException;
use Wizacha\Marketplace\User\UserService;

class UserGroupService
{
    private GroupRepository $groupRepository;
    private UserService $userService;
    private bool $featureUserGroupEnabled;
    private UploaderInterface $uploader;
    private Dispatcher $asyncDispatcher;

    public function __construct(
        GroupRepository $groupRepository,
        bool $featureUserGroupEnabled,
        UserService $userService,
        UploaderInterface $uploader,
        Dispatcher $asyncDispatcher
    ) {
        $this->groupRepository = $groupRepository;
        $this->featureUserGroupEnabled = $featureUserGroupEnabled;
        $this->userService = $userService;
        $this->uploader = $uploader;
        $this->asyncDispatcher = $asyncDispatcher;
    }

    public function list(int $page = 1, int $resultsPerPage = 10): Paginator
    {
        return $this->groupRepository->findWithPagination($page, $resultsPerPage);
    }

    public function isFeatureGroupActivated(): bool
    {
        return $this->featureUserGroupEnabled;
    }

    public function get(string $id): Group
    {
        return $this->groupRepository->get($id);
    }

    /**
     * @param string[] $ids
     *
     * @return Group[]
     */
    public function getByIds(array $ids): array
    {
        return $this->groupRepository->getByIds($ids);
    }

    /** @return string[] */
    public function getGroupIdsByUserId(int $userId): array
    {
        return $this->groupRepository->getGroupIdsByUserId($userId);
    }

    /** @return Group[] */
    public function getAll(): array
    {
        return $this->groupRepository->getAll();
    }

    /** @return string[] */
    public function getGroupsToArray(): array
    {
        $groups = [];
        foreach ($this->getAll() as $group) {
            $groups[$group->getId()] = $group->getName();
        }

        return $groups;
    }

    public function getAllArray(): array
    {
        $groups = [];
        foreach ($this->groupRepository->getAll() as $group) {
            $groups[$group->getId()] = $group->getName();
        }

        return $groups;
    }

    public function save(Group $group): void
    {
        $this->groupRepository->save($group);
    }

    public function saveAll(array $data): void
    {
        $this->groupRepository->saveAll($data);
    }

    /**
     * @param string[] $data
     *
     * @return bool
     */
    public function importUsers(array $data, string $groupId, EximJob $job): bool
    {
        if ($this->asyncDispatcher->delayExec('marketplace.user_group.service' . '::' . __FUNCTION__, \func_get_args())) {
            return true;
        }

        $group = $this->groupRepository->get($groupId);
        $validUsers = new ArrayCollection();
        $line = 1;

        foreach ($data as $row) {
            if ($row[0] === 'Email') {
                continue;
            }
            $userToAdd = null;
            if (\is_string($row[0]) === true) {
                $userToAdd = $this->userService->findOneByEmail($row[0]);
            }
            if ($userToAdd === null) {
                EximJobService::warning('user_groups_import_warning_not_found', $job->getId(), $line, null, $row[0]);
            } elseif ($validUsers->contains($userToAdd) === true) {
                EximJobService::warning('user_groups_import_warning_duplicate', $job->getId(), $line, null, $row[0]);
            } else {
                $validUsers->add($userToAdd);
                EximJobService::info('user_groups_import_success', $job->getId(), $line, null, $row[0]);
            }
            $line++;
        }

        $group->setUsers($validUsers);
        $this->groupRepository->save($group);

        return true;
    }

    public function exportUsersData(string $groupId): array
    {
        $users = $this->get($groupId)->getUsers();
        $data = [];

        if (\count($users) === 0) {
            $data[] = [
                'Email' => '',

            ];
        }

        foreach ($users as $user) {
            $data[] = [
                'Email' => $user->getEmail(),

            ];
        }

        return $data;
    }

    /**
     * @param string $groupId
     * @param int[] $usersIds
     *
     * @return int[]
     */
    public function addUsers(string $groupId, array $usersIds): array
    {
        $group = $this->groupRepository->get($groupId);

        if ($group->getUsers()->isEmpty() === false) {
            throw new NotEmptyGroupException('Group must be empty');
        }

        $validUsers = new ArrayCollection();
        $validIds = [];

        foreach ($usersIds as $userId) {
            try {
                $user = $this->userService->get($userId);
                $validUsers->add($user);
                $validIds[] = $userId;
            } catch (NotFound $exception) {
                //nothing to do
            }
        }

        $group->setUsers($validUsers);
        $this->groupRepository->save($group);

        return $validIds;
    }

    public function addUser(string $groupId, int $userId): void
    {
        $group = $this->groupRepository->get($groupId);
        $user = $this->userService->get($userId);
        $group->addUser($user);
        $this->groupRepository->save($group);
    }

    public function deleteUser(string $groupId, int $userId): void
    {
        $group = $this->groupRepository->get($groupId);
        $user = $this->userService->get($userId);
        $group->deleteUser($user);
        $this->groupRepository->save($group);
    }

    public function listUsers(string $groupId, int $offset = 0, int $limit = 10): Pagination
    {
        return $this->groupRepository->findUsersWithPagination($groupId, $offset, $limit);
    }

    public function deleteAllUsers(string $groupId): void
    {
        $group = $this->groupRepository->get($groupId);
        $group->deleteUsers();
        $this->groupRepository->save($group);
    }

    public function getUserGroups(int $userId): array
    {
        return $this->groupRepository->getUserGroups($userId);
    }
}
