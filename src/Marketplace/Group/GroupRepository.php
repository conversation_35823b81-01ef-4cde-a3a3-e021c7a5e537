<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Group;

use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Wizacha\Component\Pagination\Pagination;
use Wizacha\Marketplace\Exception\NotFound;

class GroupRepository
{
    private EntityManagerInterface $entityManager;

    /**
     * @var Connection
     */
    private $dbal;

    public function __construct(EntityManagerInterface $entityManager, Connection $dbal)
    {
        $this->entityManager = $entityManager;
        $this->dbal = $dbal;
    }

    public function get(string $id): Group
    {
        $group = $this->entityManager->find(Group::class, $id);

        if (null === $group) {
            throw NotFound::fromId('Group', $id);
        }

        return $group;
    }

    /** @return Group[] */
    public function getAll(): array
    {
        return $this->entityManager->createQueryBuilder()
            ->select("g")
            ->from(Group::class, "g")
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * @param string[] $ids
     *
     * @return Group[]
     */
    public function getByIds(array $ids): array
    {
        return $this->entityManager->createQueryBuilder()
            ->select("g")
            ->from(Group::class, "g")
            ->where('g.id in (:ids)')
            ->setParameter('ids', $ids)
            ->getQuery()
            ->getResult()
            ;
    }

    /** @return string[] */
    public function getGroupIdsByUserId(int $userId): array
    {
        $statement = $this->entityManager->getConnection()->prepare(
            <<<SQL
            SELECT group_id
            FROM doctrine_user_groups
            WHERE user_id = :user_id
            SQL
        );

        $statement->execute(['user_id' => $userId]);

        return \array_column(
            $statement->fetchAllAssociative(),
            'group_id'
        );
    }

    public function save(Group $group): Group
    {
        $this->entityManager->persist($group);
        $this->entityManager->flush();

        return $group;
    }

    public function saveAll(array $data): void
    {
        foreach ($data as $key => $groupName) {
            // The same behavior if the name is empty or contains only spaces
            if (\mb_strlen(str_replace(' ', '', $groupName)) === 0) {
                //do not add or update
                continue;
            }

            $group = $this->entityManager->find(Group::class, $key);
            if ($group instanceof Group === true) {
                //update group
                $group->setName($groupName);
            } else {
                //new group
                $group = new Group($groupName);
            }

            $this->entityManager->persist($group);
        }

        $this->entityManager->flush();
    }

    public function findWithPagination(int $page, int $resultsPerPage): Paginator
    {
        $query = $this->entityManager->createQueryBuilder()
            ->select("g")
            ->from(Group::class, "g")
        ;

        $paginator = new Paginator($query);

        $paginator->getQuery()
            ->setFirstResult($resultsPerPage * ($page - 1))
            ->setMaxResults($resultsPerPage);

        return $paginator;
    }

    public function findUsersWithPagination(string $groupId, int $offset, int $limit): Pagination
    {
        $queryBuilder = $this->entityManager->getConnection()->createQueryBuilder();
        $queryBuilder
            ->select("ug.user_id")
            ->from('doctrine_groups', 'g')
            ->innerJoin('g', 'doctrine_user_groups', 'ug', 'g.id = ug.group_id')
            ->where('g.id = :groupId')
            ->setParameter('groupId', $groupId)
        ;

        $total = \count($queryBuilder->execute()->fetchAll());

        $queryBuilder
            ->setMaxResults($limit)
            ->setFirstResult($offset);

        $results = $queryBuilder->execute()->fetchAll();

        return new Pagination(
            $this->exposeUsers($results),
            $total,
            $offset,
            $limit
        );
    }

    private function exposeUsers($users): array
    {
        $items = [];
        foreach ($users as $user) {
            $items[] = (int) $user['user_id'];
        }

        return $items;
    }

    public function getUserGroups(int $userId): array
    {
        $result = [];

        $query = $this->dbal->createQueryBuilder()
            ->select('ug.group_id')
            ->from('doctrine_user_groups', 'ug')
            ->andWhere('ug.user_id = :userId')
            ->setParameter('userId', $userId)

        ;
        $results = $query->execute();

        while ($row = $results->fetch()) {
            $result[] = $row['group_id'];
        }

        return $result;
    }
}
