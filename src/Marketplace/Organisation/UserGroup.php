<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Organisation;

use Doctrine\Common\Collections\ArrayCollection;
use Rhumsaa\Uuid\Uuid;
use TypeError;
use Wizacha\Marketplace\Organisation\Exception\AdministratorInformationRequired;
use Wizacha\Marketplace\Organisation\Exception\CannotAddUserToGroup;
use Wizacha\Marketplace\Organisation\Exception\UserAlreadyAttachedToTheUserGroup;
use Wizacha\Marketplace\Organisation\Exception\UserGroupTypeAlreadyExistsInOrganisation;
use Wizacha\Marketplace\Organisation\Exception\UserNotAttachedToUserGroup;
use Wizacha\Marketplace\User\User;
use Wizacha\Marketplace\User\UserType;

class UserGroup
{
    public const ADMIN_TYPE = 'admin';

    /** @var string */
    private $id;

    /** @var string */
    private $name;

    /** @var string */
    private $organisationId;

    /** @var string */
    private $type;

    /** @var Organisation */
    private $organisation;

    /** @var ArrayCollection|User[] */
    private $users;

    public static function createAdminGroup(
        Organisation $organisation,
        User $user,
        string $occupation,
        Uuid $identityCard,
        Uuid $proofOfAppointment
    ): self {
        try {
            if ($organisation->getAdminGroup() instanceof UserGroup) {
                throw new UserGroupTypeAlreadyExistsInOrganisation(self::ADMIN_TYPE);
            }
        } catch (TypeError $e) {
            // Une organisation ne peut être créée sans un groupe administrateur. Ce dernier est donc obligatoire
            // et ne peux être ni modifié, ni supprimé. Lors de la création d'un nouveau groupe utilisateur
            // on vérifie donc que l'organisation n'a pas de groupe administrateur renseigné. Si ce n'est pas le cas
            // une `TypeError` est levée car le champs est obligatoire.
        }

        $adminGroup = new self($organisation, 'organisation_group_admin_label', self::ADMIN_TYPE);
        $adminGroup->addUser($user, $occupation, $identityCard, $proofOfAppointment);

        return $adminGroup;
    }

    public function __construct(Organisation $organisation, string $name, string $type)
    {
        $this->name = $name;
        $this->type = $type;
        $this->organisation = $organisation;
        $this->users = new ArrayCollection();

        $this->organisation->addUserGroup($this);
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function hasType(string $type): bool
    {
        return $this->getType() === $type;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function getOrganisation(): Organisation
    {
        return $this->organisation;
    }

    public function belongsToOrganisation(Organisation $organisation): bool
    {
        return $this->organisation === $organisation;
    }

    public function addUser(
        User $user,
        string $occupation = null,
        Uuid $identityCard = null,
        Uuid $proofOfAppointment = null
    ): void {
        if ($this->users->contains($user)) {
            throw new UserAlreadyAttachedToTheUserGroup($this->getName());
        }

        if (\in_array($user->getUserType(), [UserType::ADMIN, UserType::VENDOR], true)) {
            throw new CannotAddUserToGroup('Only a customer can be attached to a user group');
        }

        $isAdminGroup = $this->type === self::ADMIN_TYPE;

        if ($isAdminGroup) {
            $message = [];

            if (\is_null($occupation)) {
                $message[] = "Occupation must not be null.";
            }

            if (\is_null($identityCard)) {
                $message[] = "Identity card must not be null.";
            }

            if (\is_null($proofOfAppointment)) {
                $message[] = "Proof of appointment card must not be null.";
            }

            if (!empty($message)) {
                throw new AdministratorInformationRequired(implode(" ", $message));
            }
        }

        $this->users->add($user);

        if ($isAdminGroup) {
            $this->organisation->addAdministrator($user, $occupation, $identityCard, $proofOfAppointment);
        }
    }

    public function removeUser(User $user): void
    {
        if (!$this->users->contains($user)) {
            throw new UserNotAttachedToUserGroup($this->getName());
        }

        $this->users->removeElement($user);
    }

    public function hasUser(User $user): bool
    {
        return $this->users->contains($user);
    }

    /**
     * @return User[]
     */
    public function getUsers(): array
    {
        return $this->users->toArray();
    }

    public function isAdminGroup(): bool
    {
        return $this->getOrganisation()->getAdminGroup() === $this;
    }

    public function expose(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'type' => $this->type,
        ];
    }

    /**
     * @return string
     */
    public function getOrganisationId(): string
    {
        return $this->organisationId;
    }

    /**
     * @param string $organisationId
     */
    public function setOrganisationId(string $organisationId): void
    {
        $this->organisationId = $organisationId;
    }
}
