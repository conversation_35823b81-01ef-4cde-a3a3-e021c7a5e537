<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Organisation;

use Doctrine\ORM\EntityManagerInterface;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Organisation\Exception\CannotAddUserToGroup;
use Wizacha\Marketplace\Organisation\Exception\CannotRemoveUserFromGroup;
use Wizacha\Marketplace\User\User;
use Wizacha\Component\Storage\Storage;

class UserGroupService
{
    private $em;
    private $identityCardStorage;
    private $proofOfAppointmentStorage;

    public function __construct(
        EntityManagerInterface $em,
        Storage $identityCardStorage,
        Storage $proofOfAppointmentStorage
    ) {
        $this->em = $em;
        $this->identityCardStorage = $identityCardStorage;
        $this->proofOfAppointmentStorage = $proofOfAppointmentStorage;
    }

    public function get(string $groupId): UserGroup
    {
        $group = $this->em->find(UserGroup::class, $groupId);
        if (!$group instanceof UserGroup) {
            throw NotFound::fromId(UserGroup::class, $groupId);
        }

        return $group;
    }

    public function create(Organisation $organisation, string $name, string $type): UserGroup
    {
        $group = new UserGroup($organisation, $name, $type);
        $this->save($group);

        return $group;
    }

    public function attachUser(
        UserGroup $group,
        User $user,
        string $occupation = null,
        \SplFileObject $identityCard = null,
        \SplFileObject $proofOfAppointment = null
    ): void {
        if (!$user->belongsToOrganisation($group->getOrganisation())) {
            throw new CannotAddUserToGroup("The user doesn't belong to the organisation");
        }

        $group->addUser(
            $user,
            $occupation,
            $identityCard ? $this->identityCardStorage->store($identityCard) : null,
            $proofOfAppointment ? $this->proofOfAppointmentStorage->store($proofOfAppointment) : null
        );
        $this->save($group);
    }

    public function removeUser(UserGroup $group, User $user): void
    {
        if (!$user->belongsToOrganisation($group->getOrganisation())) {
            throw new CannotRemoveUserFromGroup("The user doesn't belong to the organisation");
        }

        $group->removeUser($user);
        $this->save($group);
    }

    public function save(UserGroup $group): void
    {
        $this->em->persist($group);
        $this->em->flush();
    }

    /**
     * @param User $user
     * @return UserGroup[]
     * @throws NotFound
     */
    public function getGroupsForUser(User $user): array
    {
        $organisation = $user->getOrganisation();
        $organisationGroups = $organisation->getUserGroups();

        $userGroups = array_filter($organisationGroups, function (UserGroup $group) use ($user) {
            return $group->hasUser($user);
        });

        return array_values($userGroups);
    }
}
