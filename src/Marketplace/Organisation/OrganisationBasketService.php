<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Organisation;

use Doctrine\ORM\EntityManagerInterface;
use Wizacha\Marketplace\Basket\BasketService;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\User\User;
use Wizacha\Marketplace\User\UserService;

class OrganisationBasketService
{
    /**
     * @var BasketService
     */
    private $basketService;

    /**
     * @var UserService
     */
    private $userService;

    /**
     * @var EntityManagerInterface
     */
    private $em;

    public function __construct(
        BasketService $basketService,
        UserService $userService,
        EntityManagerInterface $em
    ) {
        $this->basketService = $basketService;
        $this->userService = $userService;
        $this->em = $em;
    }

    public function get(string $basketId): OrganisationBasket
    {
        $organisationBasket = $this->em->find(OrganisationBasket::class, $basketId);
        if (!$organisationBasket instanceof OrganisationBasket) {
            throw NotFound::fromId(OrganisationBasket::class, $basketId);
        }

        return $organisationBasket;
    }

    public function createOrganisationBasket(User $user, string $name): string
    {
        if (!$user->belongsToAnOrganisation()) {
            throw new Forbidden('You don\'t belong to an organisation');
        }

        // Génération d'un nouveau panier
        $basketId = $this->basketService->generateNewBasket();

        // Attachement du nouveau panier à l'organisation de l'utilisateur
        $organisation = $user->getOrganisation();
        $organisation->attachBasket($basketId, $user, $name);
        $this->em->persist($organisation);
        $this->em->flush();

        return $basketId;
    }

    /**
     * @return OrganisationBasket[]
     */
    public function getOrganisationBaskets(User $user): array
    {
        if (!$user->belongsToAnOrganisation()) {
            return [];
        }

        return $user->getOrganisation()->getOrganisationBaskets();
    }

    public function lock(string $organisationBasketId): void
    {
        $organisationBasket = $this->get($organisationBasketId);
        $organisationBasket->lock();

        $this->persist($organisationBasket);
    }

    public function validate(string $organisationBasketId): void
    {
        $organisationBasket = $this->get($organisationBasketId);
        $organisationBasket->accept();

        $this->persist($organisationBasket);
    }

    public function checkout(string $organisationBasketId): void
    {
        $organisationBasket = $this->get($organisationBasketId);
        $organisationBasket->checkout();

        $this->persist($organisationBasket);
    }

    public function hide(string $organisationBasketId): void
    {
        $organisationBasket = $this->get($organisationBasketId);
        $organisationBasket->hide();

        $this->persist($organisationBasket);
    }

    private function persist(OrganisationBasket $organisationBasket)
    {
        $this->em->persist($organisationBasket);
        $this->em->flush();
    }
}
