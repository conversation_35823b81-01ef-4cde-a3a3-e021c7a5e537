<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Organisation;

class OrganisationOrder
{
    /**
     * @var int
     */
    private $orderId;

    /** @var Organisation */
    private $organisation;

    public function __construct(Organisation $organisation, int $orderId)
    {
        $this->organisation = $organisation;
        $this->orderId = $orderId;
    }

    public function getOrderId(): int
    {
        return $this->orderId;
    }

    public function getOrganisation(): Organisation
    {
        return $this->organisation;
    }
}
