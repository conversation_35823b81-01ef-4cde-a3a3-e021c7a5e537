<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Organisation;

use Wizacha\Marketplace\User\User;
use Rhumsaa\Uuid\Uuid;

/**
 * Rescence toutes les informations nécessaires aux admin d'une organisation
 */
class Administrator
{
    /**
     * Valeur nécessaire pour l'orm mais jamais utilisée
     */
    private $id;

    /**
     * @var User
     */
    private $user;

    /**
     * Propriété nécessaire à l'orm mais non utilisée
     */
    private $organisation;

    /**
     * Fonction
     *
     * @var string
     */
    private $occupation;

    /**
     * Identifiant du fichier
     *
     * @var Uuid
     */
    private $identityCard;

    /**
     * Justificatif de nomination (document légal)
     *
     * Indentifiant du fichier
     *
     * @var Uuid
     */
    private $proofOfAppointment;

    public function __construct(
        User $user,
        string $occupation,
        Uuid $identityCard,
        Uuid $proofOfAppointment
    ) {
        $this->user = $user;
        $this->organisation = $user->getOrganisation();
        $this->occupation = $occupation;
        $this->identityCard = $identityCard;
        $this->proofOfAppointment = $proofOfAppointment;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function getOccupation(): string
    {
        return $this->occupation;
    }

    public function getIdentityCard(): Uuid
    {
        return $this->identityCard;
    }

    public function getProofOfAppointment(): Uuid
    {
        return $this->proofOfAppointment;
    }
}
