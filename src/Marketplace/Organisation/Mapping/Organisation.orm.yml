Wizacha\Marketplace\Organisation\Organisation:
    type: entity
    table: organisation
    id:
        id:
            type: guid
            generator:
                strategy: UUID
    fields:
        name:
            type: string
            nullable: false
        status:
            type: organisation_status
            nullable: false
    embedded:
        address:
            class: Wizacha\Marketplace\Organisation\OrganisationAddress
            columnPrefix: false
        shippingAddress:
            class: Wizacha\Marketplace\Organisation\OrganisationAddress
        legalInformation:
            class: Wizacha\Marketplace\Organisation\LegalInformation
        businessUnit:
            class: Wizacha\Marketplace\Organisation\BusinessUnit
    oneToMany:
        organisationBaskets:
            targetEntity: Wizacha\Marketplace\Organisation\OrganisationBasket
            mappedBy: organisation
            cascade: [all]
        userGroups:
            targetEntity: Wizacha\Marketplace\Organisation\UserGroup
            mappedBy: organisation
            cascade: [all]
        administrators:
            targetEntity: Wizacha\Marketplace\Organisation\Administrator
            mappedBy: organisation
            cascade: [all]
        orders:
            targetEntity: Wizacha\Marketplace\Organisation\OrganisationOrder
            mappedBy: organisation
            orderBy: {'orderId': 'DESC'}
            cascade: [all]
    oneToOne:
        adminGroup:
            targetEntity: Wizacha\Marketplace\Organisation\UserGroup
            joinColumn:
                referencedColumnName: id
                onDelete: SET NULL
            cascade: [all]
