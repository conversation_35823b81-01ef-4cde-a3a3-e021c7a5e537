Wizacha\Marketplace\Organisation\OrganisationBasket:
    type: entity
    table: organisation_basket
    id:
        basketId:
            type: guid
    fields:
        name:
            type: string
        isLocked:
            type: boolean
            nullable: false
            options:
                default: false
        isAccepted:
            type: boolean
            nullable: true
        isCheckout:
            type: boolean
            nullable: false
            options:
                default: false
        isHidden:
            type: boolean
            nullable: false
            options:
                default: false
        createdAt:
            type: datetime_immutable
            nullable: false
        lockedAt:
            type: datetime_immutable
            nullable: true
    manyToOne:
        organisation:
            targetEntity: Wizacha\Marketplace\Organisation\Organisation
            joinColumn:
                name: organisation_id
                onDelete: CASCADE
            referencedColumnName: id
            inversedBy: organisationBaskets
        user:
            targetEntity: Wizacha\Marketplace\User\User
            joinColumn:
                name: user_id
                referencedColumnName: user_id
                nullable: false
