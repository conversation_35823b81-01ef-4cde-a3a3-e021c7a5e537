Wizacha\Marketplace\Organisation\UserGroup:
    type: entity
    table: user_group
    id:
        id:
            type: guid
            generator:
                strategy: UUID
    fields:
        organisationId:
            type: string
            nullable: false
        name:
            type: string
            nullable: false
        type:
            type: string
            nullable: false
    manyToOne:
        organisation:
            targetEntity: Wizacha\Marketplace\Organisation\Organisation
            inversedBy: userGroups
            joinColumn:
                referencedColumnName: id
                nullable: false
                onDelete: CASCADE
    manyToMany:
        users:
            targetEntity: Wizacha\Marketplace\User\User
            joinTable:
                name: user_group_users
                joinColumns:
                    group_id:
                        referencedColumnName: id
                        onDelete: CASCADE
                inverseJoinColumns:
                    user_id:
                        referencedColumnName: user_id
