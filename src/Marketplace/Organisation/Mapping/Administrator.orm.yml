Wizacha\Marketplace\Organisation\Administrator:
    type: entity
    table: organisation_administrator
    id:
        id:
            type: guid
            generator:
                strategy: UUID
    fields:
        occupation:
            type: string
            nullable: false
        identityCard:
            type: ramsey_uuid
            nullable: false
        proofOfAppointment:
            type: ramsey_uuid
            nullable: false
    oneToOne:
        user:
            targetEntity: Wizacha\Marketplace\User\User
            joinColumn:
                referencedColumnName: user_id
            cascade: [all]
    manyToOne:
        organisation:
            targetEntity: Wizacha\Marketplace\Organisation\Organisation
            inversedBy: administrators
            joinColumn:
                referencedColumnName: id
                nullable: false
                onDelete: CASCADE
