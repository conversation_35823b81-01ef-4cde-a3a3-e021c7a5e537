<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Organisation;

use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Organisation\Exception\OrganisationAlreadyApproved;
use Wizacha\Marketplace\Organisation\Exception\OrganisationAlreadyDisapproved;
use Wizacha\Marketplace\Organisation\Exception\UserGroupAlreadyAttachedToOrganisation;
use Wizacha\Marketplace\Organisation\Exception\UserGroupTypeAlreadyExistsInOrganisation;
use Wizacha\Marketplace\User\User;
use Rhumsaa\Uuid\Uuid;

/**
 * Entité morale
 */
class Organisation
{
    /** @var string */
    private $id;

    /** @var string */
    private $name;

    /** @var OrganisationAddress */
    private $address;

    /** @var OrganisationAddress */
    private $shippingAddress;

    /** @var ArrayCollection|OrganisationBasket[] */
    private $organisationBaskets;

    /** @var UserGroup */
    private $adminGroup;

    /** @var ArrayCollection|UserGroup[] */
    private $userGroups;

    /** @var ArrayCollection|OrganisationOrder[] */
    private $orders;

    /** @var LegalInformation */
    private $legalInformation;

    /** @var BusinessUnit */
    private $businessUnit;

    /**
     * @var ArrayCollection|Administrator[]
     */
    private $administrators;

    /** @var OrganisationStatus */
    private $status;

    public function __construct(
        string $name,
        OrganisationAddress $address,
        OrganisationAddress $shippingAddress,
        User $adminUser,
        string $siret,
        string $vatNumber,
        string $occupation,
        Uuid $identityCard,
        Uuid $proofOfAppointment,
        string $businessName,
        string $businessUnitCode,
        string $businessUnitName
    ) {
        $adminUser->setOrganisation($this);

        $this->name = $name;
        $this->address = $address;
        $this->shippingAddress = $shippingAddress;
        $this->organisationBaskets = new ArrayCollection();
        $this->userGroups = new ArrayCollection();
        $this->orders = new ArrayCollection();
        $this->administrators = new ArrayCollection();
        $this->adminGroup = UserGroup::createAdminGroup($this, $adminUser, $occupation, $identityCard, $proofOfAppointment);
        $this->legalInformation = LegalInformation::declare($siret, $vatNumber, $businessName);
        $this->businessUnit = new BusinessUnit($businessUnitCode, $businessUnitName);
        $this->status = OrganisationStatus::PENDING();
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return Administrator[]
     */
    public function getAdministrators(): array
    {
        return $this->administrators->toArray();
    }

    public function getAdministrator(User $user): Administrator
    {
        foreach ($this->administrators as $administrator) {
            if ($administrator->getUser() === $user) {
                return $administrator;
            }
        }
    }

    public function isAdministrator(User $user): bool
    {
        if (!$this->getAdminGroup()->hasUser($user)) {
            return false;
        }

        return !$this
            ->administrators
            ->filter(function (Administrator $administrator) use ($user): bool {
                return $administrator->getUser() === $user;
            })
            ->isEmpty();
    }

    public function getAddress(): OrganisationAddress
    {
        return $this->address;
    }

    public function setAddress(OrganisationAddress $address): self
    {
        $this->address = $address;

        return $this;
    }

    public function getShippingAddress(): OrganisationAddress
    {
        return $this->shippingAddress;
    }

    public function setShippingAddress(OrganisationAddress $shippingAddress): self
    {
        $this->shippingAddress = $shippingAddress;

        return $this;
    }

    public function rename(string $name): void
    {
        $this->name = $name;
    }

    public function attachBasket(string $basketId, User $user, string $name): self
    {
        if (!$this->ownsBasket($basketId)) {
            $this->organisationBaskets[] = new OrganisationBasket(
                $basketId,
                $this,
                $user,
                $name,
                new DateTimeImmutable('now')
            );
        }

        return $this;
    }

    /**
     * @return OrganisationBasket[]
     */
    public function getOrganisationBaskets(): array
    {
        return $this->organisationBaskets->toArray();
    }

    public function ownsBasket(string $basketId): bool
    {
        try {
            $this->getBasket($basketId);

            return true;
        } catch (NotFound $e) {
            return false;
        }
    }

    public function getBasket(string $basketId): OrganisationBasket
    {
        $baskets = $this
            ->organisationBaskets
            ->filter(function (OrganisationBasket $basket) use ($basketId): bool {
                return $basket->getBasketId() === $basketId;
            });

        if ($baskets->isEmpty()) {
            throw NotFound::fromId(OrganisationBasket::class, $basketId);
        }

        return $baskets->first();
    }

    public function getAdminGroup(): UserGroup
    {
        return $this->adminGroup;
    }

    /**
     * @return UserGroup[]
     */
    public function getUserGroups(): array
    {
        return $this->userGroups->toArray();
    }

    public function getUserGroup(string $type): UserGroup
    {
        return $this
            ->userGroups
            ->filter(function (UserGroup $userGroup) use ($type): bool {
                return $userGroup->hasType($type);
            })
            ->first();
    }

    public function hasGroupType(string $type): bool
    {
        return $this->userGroups->exists(function (int $index, UserGroup $group) use ($type): bool {
            return $group->hasType($type);
        });
    }

    public function addUserGroup(UserGroup $group): void
    {
        if ($this->userGroups->contains($group)) {
            throw new UserGroupAlreadyAttachedToOrganisation($group->getName());
        }

        if ($this->hasGroupType($group->getType())) {
            throw new UserGroupTypeAlreadyExistsInOrganisation($group->getType());
        }

        $this->userGroups->add($group);
    }

    public function declareLegalInformation(string $siret, string $vatNumber, string $businessName): void
    {
        $this->legalInformation = LegalInformation::declare($siret, $vatNumber, $businessName);
    }

    public function getLegalInformation(): LegalInformation
    {
        return $this->legalInformation;
    }

    public function getBusinessUnit(): BusinessUnit
    {
        return $this->businessUnit;
    }

    public function registerBusinessUnit(string $code, string $name): void
    {
        $this->businessUnit = new BusinessUnit($code, $name);
    }

    public function getStatus(): OrganisationStatus
    {
        return $this->status;
    }

    public function approve(): void
    {
        if ($this->isApproved()) {
            throw new OrganisationAlreadyApproved($this->getId());
        }

        $this->status = OrganisationStatus::APPROVED();
    }

    public function isApproved(): bool
    {
        return $this->status->equals(OrganisationStatus::APPROVED());
    }

    public function disapprove(): void
    {
        if ($this->isDisapproved()) {
            throw new OrganisationAlreadyDisapproved($this->getId());
        }

        $this->status = OrganisationStatus::DISAPPROVED();
    }

    public function isDisapproved(): bool
    {
        return $this->status->equals(OrganisationStatus::DISAPPROVED());
    }

    /**
     * @internal Ne doit être appelé que depuis le UserGroup
     */
    public function addAdministrator(
        User $user,
        string $occupation,
        Uuid $identityCard,
        Uuid $proofOfAppointment
    ): void {
        $this->administrators->add(new Administrator(
            $user,
            $occupation,
            $identityCard,
            $proofOfAppointment
        ));
    }

    public function expose(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'businessName' => $this->legalInformation->getBusinessName(),
            'siret' => $this->legalInformation->getSiret(),
            'vatNumber' => $this->legalInformation->getVATNumber(),
            'businessUnitCode' => $this->businessUnit->getCode(),
            'businessUnitName' => $this->businessUnit->getName(),
            'address' => $this->address->expose(),
            'shippingAddress' => $this->shippingAddress->expose(),
            'status' => (string) $this->status,
        ];
    }

    /**
     * @return OrganisationOrder[]
     */
    public function getOrders(): array
    {
        return $this->orders->toArray();
    }
}
