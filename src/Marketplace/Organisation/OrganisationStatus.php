<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Organisation;

use MyCLabs\Enum\Enum;

/**
 * @method static OrganisationStatus PENDING()
 * @method static OrganisationStatus APPROVED()
 * @method static OrganisationStatus DISAPPROVED()
 */
class OrganisationStatus extends Enum
{
    private const PENDING = 'pending';
    private const APPROVED = 'approved';
    private const DISAPPROVED = 'disapproved';
}
