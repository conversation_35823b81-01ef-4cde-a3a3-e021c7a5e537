<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Organisation\Event;

use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Contracts\EventDispatcher\Event;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Wizacha\Component\Notification\NotificationEvent;
use Wizacha\Marketplace\Organisation\Organisation;

abstract class OrganisationEvent extends Event implements NotificationEvent
{
    protected $organisation;

    public function __construct(Organisation $organisation)
    {
        $this->organisation = $organisation;
    }

    public static function buildDebugForm(FormBuilder $form)
    {
        $form->add('organisation', EntityType::class, [
            'class' => Organisation::class,
            'choice_label' => 'name',
        ]);
    }

    public static function createFromForm(Form $form)
    {
        $organisation = $form->getData()['organisation'];

        return new static($organisation);
    }

    public function getOrganisation(): Organisation
    {
        return $this->organisation;
    }
}
