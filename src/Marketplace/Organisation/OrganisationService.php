<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Organisation;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Wizacha\Component\Storage\Storage;
use Wizacha\Marketplace\Basket\BasketService;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Organisation\Event\OrganisationApproved;
use Wizacha\Marketplace\Organisation\Event\OrganisationDeleted;
use Wizacha\Marketplace\Organisation\Event\OrganisationDisapproved;
use Wizacha\Marketplace\Organisation\Event\OrganisationRegistered;
use Wizacha\Marketplace\Organisation\Exception\OrganisationNotDeletable;
use Wizacha\Marketplace\Organisation\Exception\UnknownUserAddressType;
use Wizacha\Marketplace\User\User;
use Wizacha\Marketplace\User\UserAddress;
use Wizacha\Marketplace\User\UserService;
use Wizacha\Marketplace\User\UserTitle;
use Doctrine\DBAL\Query\QueryBuilder;
use Wizacha\Marketplace\Transaction\TransactionType;

class OrganisationService
{
    private BasketService $basketService;
    private UserService $userService;
    private UserGroupService $groupService;
    private EntityManagerInterface $em;
    private Storage $identityCardStorage;
    private Storage $proofOfAppointmentStorage;
    private EventDispatcherInterface $eventDispatcher;

    public function __construct(
        BasketService $basketService,
        UserService $userService,
        UserGroupService $groupService,
        EntityManagerInterface $em,
        Storage $identityCardStorage,
        Storage $proofOfAppointmentStorage,
        EventDispatcherInterface $eventDispatcher
    ) {
        $this->basketService = $basketService;
        $this->userService = $userService;
        $this->groupService = $groupService;
        $this->em = $em;
        $this->identityCardStorage = $identityCardStorage;
        $this->proofOfAppointmentStorage = $proofOfAppointmentStorage;
        $this->eventDispatcher = $eventDispatcher;
    }

    public function register(
        string $name,
        string $email,
        string $password,
        array $address,
        array $shippingAddress,
        string $siret,
        string $vatNumber,
        string $occupation,
        \SplFileObject $identityCard,
        \SplFileObject $proofOfAppointment,
        string $businessName,
        string $businessUnitCode,
        string $businessUnitName,
        UserTitle $title,
        string $firstName,
        string $lastName,
        string $phone = null
    ): Organisation {
        $user = $this->em->transactional(function () use ($name, $email, $password, $firstName, $lastName, $address, $shippingAddress, $occupation, $identityCard, $proofOfAppointment, $businessName, $businessUnitCode, $businessUnitName, $siret, $vatNumber): User {
            $userId = $this->userService->createUser($email, $password, $firstName, $lastName);
            $user = $this->userService->get($userId);

            $organisation = $this->create($name, $address, $shippingAddress, $user, $siret, $vatNumber, $occupation, $identityCard, $proofOfAppointment, $businessName, $businessUnitCode, $businessUnitName);

            return $user;
        });
        $organisation = $user->getOrganisation();

        $user->setTitle($title ?? UserTitle::MR());
        $user->setPhone($phone);
        $this->userService->save($user);
        $this->copyOrganisationPostalAddressesIntoUser($organisation, $user);
        $this->userService->save($user);

        $this->eventDispatcher->dispatch(
            new OrganisationRegistered($user->getOrganisation()),
            OrganisationRegistered::class
        );

        return $organisation;
    }

    public function rename(string $organisationId, string $name): void
    {
        $organisation = $this->get($organisationId);
        $organisation->rename($name);

        $this->persist($organisation);
    }

    public function delete(string $organisationId): void
    {
        $organisation = $this->get($organisationId);

        if (\count($this->getOrders($organisationId))) {
            throw new OrganisationNotDeletable($organisationId);
        }

        $this->em->remove($organisation);
        $this->em->flush();

        $this->eventDispatcher->dispatch(new OrganisationDeleted($organisation), OrganisationDeleted::class);
    }

    public function registerBusinessUnit(string $organisationId, string $code, string $name): void
    {
        $organisation = $this->get($organisationId);
        $organisation->registerBusinessUnit($code, $name);

        $this->persist($organisation);
    }

    public function get(string $organisationId): Organisation
    {
        $organisation = $this->em->find(Organisation::class, $organisationId);

        if (!$organisation instanceof Organisation) {
            throw NotFound::fromId(Organisation::class, $organisationId);
        }

        return $organisation;
    }

    /**
     * @return Organisation[]
     */
    public function list(): array
    {
        return $this
            ->em
            ->getRepository(Organisation::class)
            ->findAll();
    }

    public function addNewUser(
        Organisation $organisation,
        string $groupId,
        string $email,
        string $password,
        string $status,
        string $firstName,
        string $lastName,
        UserTitle $title,
        string $occupation = null,
        \SplFileObject $identityCard = null,
        \SplFileObject $proofOfAppointment = null,
        string $phone = null,
        array $shippingAddress = null
    ): User {
        $group = $this->groupService->get($groupId);
        if (!$group->belongsToOrganisation($organisation)) {
            throw new Forbidden("The group doesn't belong to the organisation");
        }

        $userId = $this->userService->createUser($email, $password, $firstName, $lastName);
        $user = $this->userService->get($userId);

        $user->setStatus($status);
        $user->setTitle($title);
        $user->setOrganisation($organisation);
        $user->setPhone($phone);
        $this->userService->save($user);

        $this->copyOrganisationPostalAddressesIntoUser($organisation, $user);

        if ($shippingAddress !== null) {
            $user->setShippingAddress(new UserAddress($shippingAddress));
            $billingAddressFields = $this->getOrganisationAddressFields($organisation, $user, 'billing');
            $this->userService->updateAddresses($user->getUserId(), $billingAddressFields, $shippingAddress);
        }

        $this->userService->save($user);

        $this->groupService->attachUser($group, $user, $occupation, $identityCard, $proofOfAppointment);

        return $user;
    }

    public function updateAddresses(string $organisationId, array $address, array $shippingAddress): void
    {
        $organisation = $this->get($organisationId);

        $organisationAddress = new OrganisationAddress($address);
        $organisation->setAddress($organisationAddress);

        $organisationShippingAddress = new OrganisationAddress($shippingAddress);
        $organisation->setShippingAddress($organisationShippingAddress);

        $this->persist($organisation);
    }

    /**
     * @return User[]
     */
    public function getUsers(Organisation $organisation): array
    {
        return $this
            ->em
            ->getRepository(User::class)
            ->findByOrganisation($organisation);
    }

    /**
     * @return UserGroup[]
     */
    public function getUserGroups(Organisation $organisation): array
    {
        return $this
            ->em
            ->getRepository(UserGroup::class)
            ->findByOrganisation($organisation);
    }

    public function linkOrder(string $organisationId, int $orderId): void
    {
        $order = new OrganisationOrder(
            $this->get($organisationId),
            $orderId
        );
        $this->em->persist($order);
        $this->em->flush();
    }

    /**
     * @return OrganisationOrder[]
     */
    public function getOrders(string $organisationId): array
    {
        return $this
            ->em
            ->getRepository(OrganisationOrder::class)
            ->findByOrganisation($this->get($organisationId));
    }

    public function declareLegalInformation(
        string $organisationId,
        string $siret,
        string $vatNumber,
        string $businessName
    ): void {
        $organisation = $this->get($organisationId);
        $organisation->declareLegalInformation($siret, $vatNumber, $businessName);

        $this->persist($organisation);
    }

    public function approve(string $organisationId): void
    {
        $organisation = $this->get($organisationId);
        $organisation->approve();

        $this->persist($organisation);

        $this->eventDispatcher->dispatch(new OrganisationApproved($organisation), OrganisationApproved::class);
    }

    public function disapprove(string $organisationId): void
    {
        $organisation = $this->get($organisationId);
        $organisation->disapprove();

        $this->persist($organisation);

        $this->eventDispatcher->dispatch(new OrganisationDisapproved($organisation), OrganisationDisapproved::class);
    }

    /**
     * DRYer way getting address fields
     */
    private function getOrganisationAddressFields(Organisation $organisation, User $user, $type = 'billing'): array
    {
        $avalaibleTypes = [
            'billing' => [
                'def' => 'getAddress',
                'get' => 'getBillingAddress',
                'set' => 'setBillingAddress',
            ],
            'shipping' => [
                'def' => 'getShippingAddress',
                'get' => 'getShippingAddress',
                'set' => 'setShippingAddress',
            ],
        ];

        if (!\in_array($type, array_keys($avalaibleTypes))) {
            throw new UnknownUserAddressType($type);
        }

        $methods = $avalaibleTypes[$type];

        $organisationAddress = $organisation->{$methods['def']}();
        $addressFields = $user->{$methods['get']}()->getAllFields();
        $addressFields['title'] = (string) $user->getTitle();
        $addressFields['firstname'] = $user->getFirstname();
        $addressFields['lastname'] = $user->getLastname();
        $addressFields['company'] = $organisation->getName();
        $addressFields['address'] = $organisationAddress->getAddress();
        $addressFields['address_2'] = $organisationAddress->getAdditionalAddress();
        $addressFields['zipcode'] = $organisationAddress->getZipCode();
        $addressFields['city'] = $organisationAddress->getCity();
        $addressFields['country'] = $organisationAddress->getCountry();
        $user->{$methods['set']}(new UserAddress($addressFields));

        return $addressFields;
    }

    /**
     * Copie des informations postales de l'organisation dans celles de
     * l'utilisateur que l'on vient d'attacher.
     * Il est obligatoire que l'utilisateur dispose des adresses de
     * facturation et de livraison pour pouvoir passer une commande car il
     * y a une vérification tout au fond du processus de checkout.
     * Et même si celles-ci ne sont pas forcément vraies, ce n'est pas
     * important car les adresses de facturation et de livraison réelles
     * sont re-générées lors de la commande.
     */
    private function copyOrganisationPostalAddressesIntoUser(Organisation $organisation, User $user): void
    {
        $billingAddressFields = $this->getOrganisationAddressFields($organisation, $user, 'billing');
        $shippingAddressFields = $this->getOrganisationAddressFields($organisation, $user, 'shipping');
        $this->userService->updateAddresses($user->getUserId(), $billingAddressFields, $shippingAddressFields);
    }

    private function create(
        string $name,
        array $address,
        array $shippingAddress,
        User $user,
        string $siret,
        string $vatNumber,
        string $occupation,
        \SplFileObject $identityCard,
        \SplFileObject $proofOfAppointment,
        string $businessName,
        string $businessUnitCode,
        string $businessUnitName
    ): Organisation {
        $organisation = new Organisation(
            $name,
            new OrganisationAddress($address),
            new OrganisationAddress($shippingAddress),
            $user,
            $siret,
            $vatNumber,
            $occupation,
            $identityCardId = $this->identityCardStorage->store($identityCard),
            $proofOfAppointmentId = $this->proofOfAppointmentStorage->store($proofOfAppointment),
            $businessName,
            $businessUnitCode,
            $businessUnitName
        );

        $this->persist($organisation);

        return $organisation;
    }

    private function persist(Organisation $organisation): void
    {
        $this->em->persist($organisation);
        $this->em->flush();
    }

    private function getOrganisationOrdersQuery(
        string $alias,
        string $organisationId,
        array $filter
    ): QueryBuilder {
        $queryBuilder = $this->em->getConnection()->createQueryBuilder();
        $queryBuilder
            ->select($alias)
            ->from('doctrine_organisation_order', 'oo')
            ->innerJoin('oo', 'cscart_orders', 'o', 'o.order_id = oo.order_id')
            ->where('oo.organisation_id = :organisationId')
        ;

        if (\array_key_exists('transaction_reference', $filter) === true) {
            $queryBuilder
                ->innerJoin('o', 'doctrine_order_transactions', 'ot', 'o.order_id = ot.order_id')
                ->andWhere('ot.type = :transactionType')
                ->andWhere('LOCATE (:transRef,
                        (SELECT
                        -- Transaction de reference MANGOPAY
                        IF(processor_name = \'mangopay\',
                           CONCAT(
                           SUBSTRING_INDEX(SUBSTRING_INDEX(SUBSTRING_INDEX(dot.processor_informations, \'label\' , -1),\'";}\', 1),\'"\',-1)
                            , "-" , transaction_reference)
                            -- Transaction reference LEMONWAY
                            , transaction_reference
                        )
                        FROM `doctrine_order_transactions` AS `dot`
                        where dot.order_id = o.order_id)
                        ) > 0')
                ->setParameters([
                    'transRef' => $filter['transaction_reference'],
                    'transactionType' => TransactionType::BANK_WIRE(),
                ])
            ;
        }
        $queryBuilder->setParameter('organisationId', $organisationId);

        return $queryBuilder;
    }

    public function getOrderIdsByFilter(string $organisationId, array $filter, int $start, int $limit): array
    {
        $queryBuilder = $this->getOrganisationOrdersQuery('oo.order_id', $organisationId, $filter);
        $queryBuilder
            ->orderBy('oo.order_id', 'DESC')
            ->setFirstResult($start)
            ->setMaxResults($limit);

        $iterable = $queryBuilder->execute();

        $organisationOrders = [];
        while ($raw = $iterable->fetch()) {
            \array_push($organisationOrders, $raw['order_id']);
        }

        return $organisationOrders;
    }

    public function getCountOrderIdsByFilter(string $organisationId, array $filter): int
    {
        $queryBuilder = $this->getOrganisationOrdersQuery('count(oo.order_id)', $organisationId, $filter);

        return (int) $queryBuilder->execute()->fetchColumn();
    }
}
