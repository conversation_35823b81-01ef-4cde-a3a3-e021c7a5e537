<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Organisation;

use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManagerInterface;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Order\Exception\OrderNotFound;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderService;

class OrganisationOrderService
{
    private $db;
    private $orderService;
    private $em;

    public function __construct(
        Connection $db,
        OrderService $orderService,
        EntityManagerInterface $em
    ) {
        $this->db = $db;
        $this->orderService = $orderService;
        $this->em = $em;
    }

    /**
     * Return an OrganisationOrder from an orderId
     *
     * @param int $orderId
     * @return OrganisationOrder
     * @throws NotFound
     */
    public function get(int $orderId): OrganisationOrder
    {
        $organisationOrder = $this->em->find(OrganisationOrder::class, $orderId);
        if (!$organisationOrder instanceof OrganisationOrder) {
            throw NotFound::fromId(OrganisationOrder::class, $orderId);
        }

        return $organisationOrder;
    }

    /**
     * Create and return an OrganisationOrder
     *
     * @param Organisation $organisation
     * @param int $orderId
     * @return OrganisationOrder
     */
    public function create(Organisation $organisation, int $orderId): OrganisationOrder
    {
        /** @var OrganisationOrder $organisationOrder */
        $organisationOrder = new OrganisationOrder($organisation, $orderId);

        $this->em->persist($organisationOrder);
        $this->em->flush();

        return $organisationOrder;
    }
}
