<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Organisation\Exception;

use Exception;

class UserGroupTypeAlreadyExistsInOrganisation extends Exception
{
    public function __construct(string $type)
    {
        $message = sprintf('Organisation already have "%s" type user group', $type);

        parent::__construct($message);
    }
}
