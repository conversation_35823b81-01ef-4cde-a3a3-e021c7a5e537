<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Organisation\Exception;

class OrganisationNotDeletable extends LogicException
{
    public function __construct(string $organisationId)
    {
        parent::__construct(sprintf('Organisation "%s" cannot be deleted', $organisationId));
    }
}
