<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Organisation\Exception;

class OrganisationAlreadyApproved extends LogicException
{
    public function __construct(string $organisationId)
    {
        parent::__construct(sprintf('Organisation "%s" is already approved', $organisationId));
    }
}
