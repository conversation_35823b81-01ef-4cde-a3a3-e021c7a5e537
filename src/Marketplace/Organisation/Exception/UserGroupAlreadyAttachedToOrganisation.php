<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Organisation\Exception;

use LogicException;

class UserGroupAlreadyAttachedToOrganisation extends LogicException
{
    public function __construct(string $name)
    {
        $message = sprintf('User group "%s" already attached to the organisation.', $name);

        parent::__construct($message);
    }
}
