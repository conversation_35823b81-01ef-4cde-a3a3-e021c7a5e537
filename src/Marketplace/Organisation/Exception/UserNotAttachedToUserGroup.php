<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Organisation\Exception;

use LogicException;

class UserNotAttachedToUserGroup extends LogicException
{
    public function __construct(string $name)
    {
        $message = sprintf('The user is not attached to the "%s" group', $name);

        parent::__construct($message);
    }
}
