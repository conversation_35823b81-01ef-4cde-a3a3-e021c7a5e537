<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Organisation\Exception;

use Exception;

class UserAlreadyAttachedToTheUserGroup extends Exception
{
    public function __construct(string $name)
    {
        $message = sprintf('The user is already attached the "%s" group', $name);

        parent::__construct($message);
    }
}
