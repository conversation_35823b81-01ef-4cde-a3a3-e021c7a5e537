<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Organisation;

use DomainException;

class OrganisationAddress
{
    /** @var string */
    private $address;

    /** @var string */
    private $additionalAddress;

    /** @var string */
    private $zipCode;

    /** @var string */
    private $city;

    /** @var string */
    private $state;

    /** @var string */
    private $country;

    public function __construct(array $data)
    {
        $this->validate($data);

        $this->address = $data['address'];
        $this->additionalAddress = $data['additionalAddress'] ?? '';
        $this->zipCode = $data['zipCode'];
        $this->city = $data['city'];
        $this->state = $data['state'] ?? '';
        $this->country = $data['country'] ?? '';
    }

    public function getAddress(): string
    {
        return $this->address;
    }

    public function getAdditionalAddress(): string
    {
        return $this->additionalAddress;
    }

    public function getZipCode(): string
    {
        return $this->zipCode;
    }

    public function getState(): string
    {
        return $this->state;
    }

    public function getCity(): string
    {
        return $this->city;
    }

    public function getCountry(): string
    {
        return $this->country;
    }

    public function expose(): array
    {
        return [
            'address' => $this->address,
            'additionalAddress' => $this->additionalAddress,
            'zipCode' => $this->zipCode,
            'city' => $this->city,
            'state' => $this->state,
            'country' => $this->country,
        ];
    }

    /**
     * @throws DomainException
     */
    public function validate(array $data): void
    {
        $requiredFields = [
            'address',
            'zipCode',
            'city',
        ];

        $missingRequired = array_diff_key(array_flip($requiredFields), $data);
        if (!empty($missingRequired)) {
            throw new DomainException(sprintf('Missing required field(s) : %s', implode(', ', array_keys($missingRequired))));
        }
    }
}
