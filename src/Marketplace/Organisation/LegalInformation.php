<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Organisation;

class LegalInformation
{
    /** @var string */
    private $siret;

    /** @var string */
    private $vatNumber;

    /** @var string Raison sociale */
    private $businessName;

    private function __construct(string $siret, string $vatNumber, string $businessName)
    {
        $this->siret = $siret;
        $this->vatNumber = $vatNumber;
        $this->businessName = $businessName;
    }

    public static function declare(string $siret, string $vatNumber, string $businessName): self
    {
        return new self($siret, $vatNumber, $businessName);
    }

    public function getBusinessName(): string
    {
        return $this->businessName;
    }

    public function getSiret(): string
    {
        return $this->siret;
    }

    public function getVATNumber(): string
    {
        return $this->vatNumber;
    }
}
