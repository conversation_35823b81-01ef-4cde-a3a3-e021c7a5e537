<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Organisation\Doctrine;

use Acelaya\Doctrine\Type\AbstractPhpEnumType;
use Wizacha\Marketplace\Organisation\OrganisationStatus;

class OrganisationStatusType extends AbstractPhpEnumType
{
    protected $enumType = OrganisationStatus::class;

    protected function getSpecificName(): string
    {
        return 'organisation_status';
    }
}
