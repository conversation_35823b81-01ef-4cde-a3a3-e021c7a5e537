<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Organisation;

use DateTimeImmutable;
use Wizacha\Marketplace\Basket\ReadModel\Basket;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\User\User;

class OrganisationBasket
{
    /** @var string */
    private $basketId;

    /** @var Organisation */
    private $organisation;

    /** @var User */
    private $user;

    /** @var string */
    private $name;

    /** @var bool */
    private $isLocked = false;

    /** @var DateTimeImmutable|null */
    private $lockedAt;

    /** @var bool|null */
    private $isAccepted;

    /** @var bool */
    private $isCheckout = false;

    /** @var bool */
    private $isHidden = false;

    /** @var DateTimeImmutable */
    private $createdAt;

    /**
     * Cet id vient du panier qui vient d'être créé, il n'est pas généré au sein
     * du contexte des organisations
     */
    public function __construct(
        string $basketId,
        Organisation $organisation,
        User $user,
        string $name,
        DateTimeImmutable $createdAt
    ) {
        $this->basketId = $basketId;
        $this->organisation = $organisation;
        $this->user = $user;
        $this->name = $name;
        $this->createdAt = $createdAt;
    }

    public function getBasketId(): string
    {
        return $this->basketId;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function isOwnedBy(User $user): bool
    {
        return $this->user === $user;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function isLocked(): bool
    {
        return $this->isLocked === true;
    }

    public function lock(): void
    {
        $this->isLocked = true;
        $this->lockedAt = new \DateTimeImmutable(); // todo : utiliser une horloge
    }

    public function getLockedAt(): \DateTimeImmutable
    {
        return $this->lockedAt;
    }

    public function isAccepted(): bool
    {
        return $this->isAccepted === true;
    }

    public function isRefused(): bool
    {
        return $this->isAccepted === false;
    }

    public function accept(): void
    {
        $this->isAccepted = true;
    }

    public function isCheckout(): bool
    {
        return $this->isCheckout;
    }

    public function checkout(): void
    {
        $this->isCheckout = true;
    }


    public function isHidden(): bool
    {
        return $this->isHidden;
    }

    public function hide(): void
    {
        $this->isHidden = true;
    }

    public function getOrganisation(): Organisation
    {
        return $this->organisation;
    }

    public function getBasket(): ?Basket
    {
        // Ne pouvant pas utiliser les relations Doctrine car le panier
        // n'est pas mappé, et ne devant pas injecter de service dans les
        // entités, et pour éviter à chaque controller de devoir peupler
        // le champ _basket_ lors de chaque appel, nous utilisons le meilleur
        // outil à notre disposition...
        $basketService = container()->get('marketplace.basket.domain_service');

        try {
            $basketService->checkIntegrity($this->getBasketId());
        } catch (NotFound $e) {
            // The basket was probably corrupted (e.g. containing unknown products), skip it
            return null;
        }

        /** @var null|\Wizacha\Marketplace\ReadModel\Basket $basket */
        $basket = $basketService->getById($this->getBasketId());
        if ($basket === null) {
            // The readmodel of the basket was deleted somehow
            return null;
        }

        return $basket->getReadModel();
    }

    public function getCreatedAt(): DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function expose(): array
    {
        return [
            'userId' => $this->getUser()->getUserId(),
            'basketId' => $this->getBasketId(),
            'name' => $this->getName(),
            'locked' => $this->isLocked(),
            'accepted' => $this->isAccepted(),
            'checkout' => $this->isCheckout(),
            'hidden' => $this->isHidden(),
            'createdAt' => $this->getCreatedAt()->format(\DateTime::ATOM),
        ];
    }
}
