<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\CompanyPerson;

use Wizacha\Marketplace\CompanyPerson\Exception\CompanyPersonNotFound;

class CompanyPersonService
{
    private CompanyPersonRepository $companyPersonRepository;

    public function __construct(CompanyPersonRepository $companyPersonRepository)
    {
        $this->companyPersonRepository = $companyPersonRepository;
    }

    public function get(int $id): CompanyPerson
    {
        $companyPerson = $this->companyPersonRepository->get($id);

        if ($companyPerson instanceof CompanyPerson === false) {
            throw new CompanyPersonNotFound("CompanyPerson '$id' not found.");
        }

        return $companyPerson;
    }

    public function getByCompanyId(int $companyId): array
    {
        return $this->companyPersonRepository->findByCompanyId($companyId);
    }

    public function save(CompanyPerson $companyPerson): CompanyPerson
    {
        return $this->companyPersonRepository->save($companyPerson);
    }

    public function delete(int $id): void
    {
        $companyPerson = $this->companyPersonRepository->get($id);

        if (($companyPerson instanceof CompanyPerson) === false) {
            throw new CompanyPersonNotFound("CompanyPerson '$id' not found.");
        }

        $this->companyPersonRepository->delete($companyPerson);
    }

    public function checkPersonsAreValidToBeSubmitted(array $companyPersonList): void
    {
        $partsTotal = 0;
        foreach ($companyPersonList as $companyPerson) {
            if ($companyPerson->getType()->equals(CompanyPersonType::OWNER()) === false) {
                throw new \InvalidArgumentException(__('company_invalid_person_without_ownership'));
            }
            $partsTotal += $companyPerson->getOwnershipPercentage();
        }

        if ($partsTotal < 25 || $partsTotal > 100) {
            throw new \InvalidArgumentException(__('company_invalid_person_wrong_ownership'));
        }
    }
}
