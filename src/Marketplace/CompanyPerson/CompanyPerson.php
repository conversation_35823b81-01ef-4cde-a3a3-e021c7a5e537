<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\CompanyPerson;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Wizacha\Marketplace\Country\Country;

class CompanyPerson
{
    private ?int $id;
    private ?int $companyId;
    private ?string $firstname;
    private ?string $lastname;
    private ?string $title;
    private ?string $address;
    private ?string $address2;
    private ?string $city;
    private ?string $state;
    private ?string $zipcode;
    private ?string $country;
    private ?\DateTime $birthdate;
    private ?string $birthplaceCity;
    private ?string $birthplaceCountry;
    private ?CompanyPersonType $type;
    private ?float $ownershipPercentage;
    private ?\DateTime $createdAt;
    private ?Collection $nationalities;

    public function __construct()
    {
        $this->id = null;
        $this->firstname = null;
        $this->lastname = null;
        $this->title = null;
        $this->address = null;
        $this->address2 = null;
        $this->city = null;
        $this->state = null;
        $this->zipcode = null;
        $this->country = null;
        $this->birthdate = null;
        $this->birthplaceCity = null;
        $this->birthplaceCountry = null;
        $this->type = null;
        $this->ownershipPercentage = null;
        $this->createdAt = new \DateTime();
        $this->nationalities = new ArrayCollection();
    }

    public function setId(?int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function setCompanyId(?int $companyId): self
    {
        $this->companyId = $companyId;

        return $this;
    }

    public function setFirstName(?string $firstname): self
    {
        $this->firstname = $firstname;

        return $this;
    }

    public function setLastName(?string $lastname): self
    {
        $this->lastname = $lastname;

        return $this;
    }

    public function setTitle(?string $title): self
    {
        $this->title = $title;

        return $this;
    }

    public function setAddress(?string $address): self
    {
        $this->address = $address;

        return $this;
    }

    public function setAddress2(?string $address2): self
    {
        $this->address2 = $address2;

        return $this;
    }

    public function setCity(?string $city): self
    {
        $this->city = $city;

        return $this;
    }

    public function setState(?string $state): self
    {
        $this->state = $state;

        return $this;
    }

    public function setZipCode(?string $zipCode): self
    {
        $this->zipcode = $zipCode;

        return $this;
    }

    public function setCountry(?string $country): self
    {
        $this->country = $country;

        return $this;
    }

    public function setBirthdate(?\DateTime $birthdate): self
    {
        $this->birthdate = $birthdate;

        return $this;
    }

    public function setBirthplaceCity(?string $birthplaceCity): self
    {
        $this->birthplaceCity = $birthplaceCity;

        return $this;
    }

    public function setBirthplaceCountry(?string $birthplaceCountry): self
    {
        $this->birthplaceCountry = $birthplaceCountry;

        return $this;
    }

    public function setOwnershipPercentage(?float $ownershipPercentage): self
    {
        $this->ownershipPercentage = $ownershipPercentage;

        return $this;
    }

    public function setType(?CompanyPersonType $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function setNationalities(iterable $nationalities): self
    {
        foreach ($nationalities as $nationality) {
            $this->addNationality($nationality);
        }

        return $this;
    }

    public function addNationality(?Country $nationality): self
    {
        if ($this->nationalities->contains($nationality) === false) {
            $this->nationalities->add($nationality);
        }

        return $this;
    }

    public function removeNationality(?Country $nationality): self
    {
        $this->nationalities->removeElement($nationality);

        return $this;
    }

    public function setCreatedAt(?\DateTime $createdAt): void
    {
        $this->createdAt = $createdAt;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCompanyId(): ?int
    {
        return $this->companyId;
    }

    public function getFirstName(): ?string
    {
        return $this->firstname;
    }

    public function getLastName(): ?string
    {
        return $this->lastname;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function getAddress(): ?string
    {
        return $this->address;
    }

    public function getAddress2(): ?string
    {
        return $this->address2;
    }

    public function getCity(): ?string
    {
        return $this->city;
    }

    public function getState(): ?string
    {
        return $this->state;
    }

    public function getZipCode(): ?string
    {
        return $this->zipcode;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function getBirthdate(): ?\DateTime
    {
        return $this->birthdate;
    }

    public function getBirthplaceCity(): ?string
    {
        return $this->birthplaceCity;
    }

    public function getBirthplaceCountry(): ?string
    {
        return $this->birthplaceCountry;
    }

    public function getOwnershipPercentage(): ?float
    {
        return $this->ownershipPercentage;
    }

    public function getType(): ?CompanyPersonType
    {
        return $this->type;
    }

    public function getNationalities(): ?Collection
    {
        return $this->nationalities;
    }

    public function exposeNationalities(): array
    {
        $array = [];
        foreach ($this->getNationalities() as $nationality) {
            $array[] = $nationality->getCode();
        }

        return $array;
    }


    public function getCreatedAt(): ?\DateTime
    {
        return $this->createdAt;
    }

    public function expose(): array
    {
        return [
            "id"                  => $this->getId(),
            "firstname"           => $this->getFirstName(),
            "lastname"            => $this->getLastName(),
            "title"               => $this->getTitle(),
            "address"             => $this->getAddress(),
            "address2"            => $this->getAddress2(),
            "city"                => $this->getCity(),
            "state"               => $this->getState(),
            "zipcode"             => $this->getZipCode(),
            "country"             => $this->getCountry(),
            "nationalities"       => $this->exposeNationalities(),
            "birthdate"           => $this->getBirthdate()->format(\DateTime::RFC3339),
            "birthplaceCity"      => $this->getBirthplaceCity(),
            "birthplaceCountry"   => $this->getBirthplaceCountry(),
            "type"                => $this->getType(),
            "ownershipPercentage" => $this->getOwnershipPercentage()
        ];
    }
}
