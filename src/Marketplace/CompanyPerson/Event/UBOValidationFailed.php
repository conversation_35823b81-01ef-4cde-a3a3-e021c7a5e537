<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\CompanyPerson\Event;

use Doctrine\DBAL\Types\TextType;
use Symfony\Contracts\EventDispatcher\Event;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Wizacha\Component\Notification\NotificationEvent;

class UBOValidationFailed extends Event implements NotificationEvent
{
    private int $companyId;
    private string $declarationId;
    private string $status;
    private string $reason;
    private ?string $message;

    public function __construct(
        int $companyId,
        string $declarationId,
        string $status,
        string $reason,
        string $message = null
    ) {
        $this->companyId = $companyId;
        $this->declarationId = $declarationId;
        $this->status = $status;
        $this->reason = $reason;
        $this->message = $message;
    }

    /** Build the Symfony Form that will be shown in the backend to simulate the event. */
    public static function buildDebugForm(FormBuilder $form): void
    {
        $form
            ->add('companyId', IntegerType::class)
            ->add('declarationId', TextType::class)
            ->add('status', TextType::class)
            ->add('reason', TextType::class)
            ->add('message', TextType::class)
        ;
    }

    /** Create a instance of that class from the submitted form. */
    public static function createFromForm(Form $form): self
    {
        return new static(
            $form->getData()['companyId'],
            $form->getData()['declarationId'],
            $form->getData()['status'],
            $form->getData()['reason'],
            $form->getData()['message']
        );
    }

    public static function getDescription(): string
    {
        return 'ubo_validation_failed';
    }

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    public function getDeclarationId(): string
    {
        return $this->declarationId;
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function getReason(): string
    {
        return $this->reason;
    }

    public function getMessage(): ?string
    {
        return $this->message;
    }
}
