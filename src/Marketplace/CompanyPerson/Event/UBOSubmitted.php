<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\CompanyPerson\Event;

use Symfony\Contracts\EventDispatcher\Event;

class UBOSubmitted extends Event
{
    private int $companyId;

    public function __construct(int $companyId)
    {
        $this->companyId = $companyId;
    }

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    public static function getDescription(): string
    {
        return 'ubo_submitted';
    }
}
