<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\CompanyPerson\DoctrineType;

use Acelaya\Doctrine\Type\AbstractPhpEnumType;
use Wizacha\Marketplace\CompanyPerson\CompanyPersonType;

/**
 * Doctrine Type for CompanyPersonType
 */
class CompanyPersonEnumType extends AbstractPhpEnumType
{
    protected $enumType = CompanyPersonType::class;

    protected function getSpecificName(): string
    {
        return 'company_person_type';
    }
}
