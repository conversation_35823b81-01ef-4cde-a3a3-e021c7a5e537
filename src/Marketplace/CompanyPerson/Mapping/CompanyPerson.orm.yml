Wizacha\Marketplace\CompanyPerson\CompanyPerson:
    type: entity
    table: company_person
    id:
        id:
            type: integer
            column: id
            generator:
                strategy: AUTO
    fields:
        companyId:
            type: integer
            column: company_id
        firstname:
            type: string
            column: firstname
            length:
                max: 255
            notBlank: ~
        lastname:
            type: string
            column: lastname
            length:
                max: 255
            notBlank: ~
        title:
            type: string
            column: title
            notBlank: ~
        address:
            type: string
            column: address
            length:
                max: 255
            notBlank: ~
        address2:
            type: string
            column: address_2
            length:
                max: 255
        city:
            type: string
            column: city
            length:
                max: 255
            notBlank: ~
        state:
            type: string
            column: state
            length:
                max: 255
            notBlank: ~
        zipcode:
            type: string
            column: zipcode
            notBlank: ~
        country:
            type: string
            column: country
            length:
                max: 255
            notBlank: ~
            Regex:
                pattern: '%regexp_country_code%'
                message: Country code must be ISO 3166-1 alpha2.
        birthdate:
            type: datetime
            column: birthdate
            notBlank: ~
            LessThan: today
        birthplaceCity:
            type: string
            column: birthplace_city
            length:
                max: 255
            notBlank: ~
        birthplaceCountry:
            type: string
            column: birthplace_country
            length:
                max: 255
            notBlank: ~
            Regex:
                pattern: '%regexp_country_code%'
                message: Country code must be ISO 3166-1 alpha2.
        ownershipPercentage:
            type: decimal
            column: ownership_percentage
            LessThanOrEqual: 100
            GreaterThan: 0
        type:
            type: php_enum_company_person_type
            column: type
            notBlank: ~
        createdAt:
            type: datetime
            column: created_at
    manyToMany:
        nationalities:
            targetEntity: Wizacha\Marketplace\Country\Country
            joinTable:
                name: company_person_has_nationalities
                joinColumns:
                    company_person_id:
                        referencedColumnName: id
                inverseJoinColumns:
                    country_code:
                        referencedColumnName: code
