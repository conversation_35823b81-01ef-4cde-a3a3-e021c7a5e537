<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\CompanyPerson;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;

class CompanyPersonUBO
{
    private Collection $companyPersons;

    public function __construct()
    {
        $this->companyPersons = new ArrayCollection();
    }

    public function addCompanyPerson(CompanyPerson $companyPerson): self
    {
        if ($this->getCompanyPersons()->contains($companyPerson) === false) {
            $this->companyPersons->add($companyPerson);
        }

        return $this;
    }

    public function removeCompanyPerson(CompanyPerson $companyPerson): self
    {
        if ($this->getCompanyPersons()->contains($companyPerson) === true) {
            $this->companyPersons->remove($companyPerson);
        }

        return $this;
    }

    public function getCompanyPersons(): Collection
    {
        return $this->companyPersons;
    }
}
