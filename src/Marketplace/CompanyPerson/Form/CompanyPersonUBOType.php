<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\CompanyPerson\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Wizacha\Marketplace\CompanyPerson\CompanyPersonUBO;

class CompanyPersonUBOType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('companyPersons', CollectionType::class, [
                'entry_type' => CompanyPersonType::class,
                'label' => false,
                'entry_options' => [
                    'label' => false,
                    'timezone' => $options['timezone'],
                ],
                'by_reference' => false,
                'allow_add' => true,
                'allow_delete' => true,
                'disabled' => $options['formIsDisabled'],
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => CompanyPersonUBO::class,
            'formIsDisabled' => false,
            'timezone' => date_default_timezone_get(),
        ]);
    }
}
