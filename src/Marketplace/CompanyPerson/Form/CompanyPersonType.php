<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\CompanyPerson\Form;

use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Count as AssertCount;
use Symfony\Component\Validator\Constraints\GreaterThan;
use Symfony\Component\Validator\Constraints\LessThan;
use Symfony\Component\Validator\Constraints\LessThanOrEqual;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Type;
use Wizacha\Marketplace\CompanyPerson\CompanyPerson;
use Wizacha\Marketplace\Country\Country;

class CompanyPersonType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('title', ChoiceType::class, [
                'label' => 'user_title',
                'label_attr' => ['class' => 'control-label'],
                'placeholder' => 'select_title',
                'choices' => [
                    'company_mr' => 'mr',
                    'company_mrs' => 'mrs',
                ],
                'constraints' => [
                    new NotBlank(),
                ]
            ])
            ->add('firstname', TextType::class, [
                'label' => 'first_name',
                'label_attr' => ['class' => 'control-label'],
                'attr' => [
                    'maxlength' => 255
                ],
                'constraints' => [
                    new NotBlank(),
                ]
            ])
            ->add('lastname', TextType::class, [
                'label' => 'last_name',
                'label_attr' => ['class' => 'control-label'],
                'attr' => [
                    'maxlength' => 255
                ],
                'constraints' => [
                    new NotBlank(),
                ]
            ])
            ->add('address', TextType::class, [
                'label' => 'address',
                'label_attr' => ['class' => 'control-label'],
                'attr' => [
                    'maxlength' => 255
                ],
                'constraints' => [
                    new NotBlank(),
                ]
            ])
            ->add('address2', TextType::class, [
                'label' => 'address2',
                'label_attr' => ['class' => 'control-label'],
                'required' => false,
                'attr' => [
                    'maxlength' => 255
                ],
            ])
            ->add('city', TextType::class, [
                'label' => 'city',
                'label_attr' => ['class' => 'control-label'],
                'attr' => [
                    'maxlength' => 255
                ],
                'constraints' => [
                    new NotBlank(),
                ]
            ])
            ->add('state', TextType::class, [
                'label' => 'region',
                'label_attr' => ['class' => 'control-label'],
                'attr' => [
                    'maxlength' => 255
                ],
                'constraints' => [
                    new NotBlank(),
                ]
            ])
            ->add('zipcode', TextType::class, [
                'label' => 'zipcode',
                'label_attr' => ['class' => 'control-label'],
                'attr' => [
                    'maxlength' => 255
                ],
                'constraints' => [
                    new NotBlank(),
                ]
            ])
            ->add('country', ChoiceType::class, [
                'label' => 'country',
                'label_attr' => ['class' => 'control-label'],
                'placeholder' => 'select_country',
                'choices' => $this->getCountries(),
                'choice_translation_domain' => false,
                'constraints' => [
                    new NotBlank(),
                ],
            ])
            ->add('nationalities', EntityType::class, [
                'label' => 'nationality',
                'label_attr' => ['class' => 'control-label'],
                'class' => Country::class,
                'attr' => [
                    'class' => 'nationalities'
                ],
                'multiple' => true,
                'choice_label' => function (Country $country) {
                    return $country !== null ? $country->getDescription() : '';
                },
                'constraints' => [
                    new NotBlank(),
                    new AssertCount(
                        [
                            'max' => 3,
                            'maxMessage' => 'You cannot specify more than {{ limit }} nationality',
                        ]
                    ),
                ]
            ])
            ->add('birthdate', DateType::class, [
                'label' => 'date_of_birth',
                'label_attr' => ['class' => 'control-label'],
                'widget' => 'single_text',
                'view_timezone' => $options['timezone'],
                'constraints' => [
                    new NotBlank(),
                    new LessThan(['value' => new \DateTime("today")]),
                ]
            ])
            ->add('birthplaceCity', TextType::class, [
                'label' => 'birthplace_city',
                'label_attr' => ['class' => 'control-label'],
                'attr' => [
                    'maxlength' => 255
                ],
                'constraints' => [
                    new NotBlank(),
                ]
            ])
            ->add('birthplaceCountry', ChoiceType::class, [
                'label' => 'birthplace_country',
                'label_attr' => ['class' => 'control-label'],
                'placeholder' => 'select_country',
                'choices' => $this->getCountries(),
                'choice_translation_domain' => false,
                'constraints' => [
                    new NotBlank(),
                ]
            ])
            ->add('ownershipPercentage', NumberType::class, [
                'label' => 'ownership_percentage',
                'label_attr' => ['class' => 'control-label'],
                'attr' => [
                    'min' => 1,
                    'max' => 100
                ],
                'required' => true,
                'constraints' => [
                    new NotBlank(['message' => __('error_ownership_required')]),
                    new Type(['type' => 'float']),
                    new GreaterThan(['value' => 0]),
                    new LessThanOrEqual(['value' => 100]),
                ]
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => CompanyPerson::class,
            'csrf_protection' => false,
            'allow_extra_fields' => true,
            'timezone' => date_default_timezone_get(),
        ]);
    }

    private function getCountries(): array
    {
        $choices = [];
        foreach (fn_get_simple_countries() as $key => $country) {
            $choices[$country] = $key;
        }

        return $choices;
    }
}
