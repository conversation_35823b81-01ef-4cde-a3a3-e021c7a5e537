<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\CompanyPerson;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Wizacha\Marketplace\Company\Company;
use Wizacha\Marketplace\CompanyPerson\CompanyPersonType as DoctrineCompanyPersonType;
use Wizacha\Marketplace\CompanyPerson\Form\CompanyPersonType;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\Exception\IntegrityConstraintViolation;
use Wizacha\Marketplace\Exception\Unauthorized;

trait CompanyPersonTrait
{
    protected function assertCanAccessCompanyPerson(Company $company): self
    {
        if ($this->getUser() === null) {
            throw new Forbidden();
        }

        if ($this->isGranted('ROLE_VENDOR') === false) {
            throw new Unauthorized('Access is limited to vendors.');
        }

        if ($this->getUser()->getCompanyId() !== $company->getId()) {
            throw new Unauthorized('Cannot access to ubo of another company.');
        }

        if ($company->isPrivateIndividual() === true) {
            throw new Unauthorized('Access is limited to professional vendors.');
        }

        if ($this->uboMangopayService->isUBOSubmitted($company->getId()) === true) {
            throw new Unauthorized('Cannot access to submitted persons.');
        }

        return $this;
    }

    protected function assertCanGetCompanyPerson(Company $company): self
    {
        if ($this->getUser() === null) {
            throw new Forbidden();
        }

        if ($this->isGranted('ROLE_VENDOR') === false
            && $this->isGranted('ROLE_ADMIN') === false
        ) {
            throw new Unauthorized('Access is limited to admins.');
        }

        if ($this->isGranted('ROLE_VENDOR') === true
            && $this->getUser()->getCompanyId() !== $company->getId()
        ) {
            throw new Unauthorized('You are not allowed To get persons of another vendor.');
        }

        if ($company->isPrivateIndividual() === true) {
            throw new Unauthorized('Access is limited to professional vendors.');
        }

        return $this;
    }

    protected function validateForm(CompanyPerson $companyPerson, Request $request): CompanyPerson
    {
        $form = $this->createForm(CompanyPersonType::class, $companyPerson);

        $data = json_decode($request->getContent(), true);
        $form->submit($data);

        if ($form->isSubmitted() === true && $form->isValid() === false) {
            $errors = [];
            foreach ($form->getErrors(true) as $error) {
                if ($error->getOrigin() !== null) {
                    $errors[$error->getOrigin()->getName()][] = $error->getMessage();
                }
            }

            throw new IntegrityConstraintViolation('Invalid parameters.', $errors);
        }

        $companyPerson = $form->getData();
        $companyPerson->setType(new DoctrineCompanyPersonType($data['type']));

        return $companyPerson;
    }

    public function validateOwnershipPercentage(int $companyId, CompanyPerson $companyPerson): self
    {
        $companyPersonList = $this->companyPersonService->getByCompanyId($companyId);
        $sumOwnershipPercentage = array_reduce($companyPersonList, function ($sumOwnershipPercentage, CompanyPerson $companyPerson) {
            $sumOwnershipPercentage += $companyPerson->getOwnershipPercentage();
            return $sumOwnershipPercentage;
        });

        $sumOwnershipPercentage = $companyPerson->getId() === null
            ? $sumOwnershipPercentage + $companyPerson->getOwnershipPercentage()
            : $sumOwnershipPercentage;

        if ($companyPerson->getOwnershipPercentage() < 25) {
            throw new BadRequestHttpException(__('error_ownership_percentage_minimum'));
        }

        if ($sumOwnershipPercentage > 100) {
            throw new BadRequestHttpException(__('error_ownership_percentage'));
        }

        return $this;
    }
}
