<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\CompanyPerson;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Persistence\ManagerRegistry;

class CompanyPersonRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry, $entityClass)
    {
        parent::__construct($registry, $entityClass);
    }

    public function get(int $id): ?CompanyPerson
    {
        return $this->getEntityManager()->find(CompanyPerson::class, $id);
    }

    public function findByCompanyId(int $companyId): array
    {
        return $this
            ->getEntityManager()
            ->createQueryBuilder()
            ->select('c')
            ->from(CompanyPerson::class, 'c')
            ->where('c.companyId = :companyId')
            ->setParameter('companyId', $companyId)
            ->orderBy('c.createdAt', 'ASC')
            ->getQuery()
            ->getResult()
            ;
    }

    public function save(CompanyPerson $companyPerson): CompanyPerson
    {
        if ($this->getEntityManager()->contains($companyPerson) === false) {
            $this->getEntityManager()->persist($companyPerson);
        }
        $this->getEntityManager()->flush();

        return $companyPerson;
    }

    public function delete(CompanyPerson $companyPerson): void
    {
        $this->getEntityManager()->remove($companyPerson);
        $this->getEntityManager()->flush();
    }
}
