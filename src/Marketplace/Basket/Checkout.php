<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Basket;

use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\Response;
use Wizacha\AgeVerification;
use Wizacha\Component\Chronopost\Exceptions\SystemError;
use Wizacha\Component\MondialRelay\Exception\ApiException;
use Wizacha\Marketplace\Basket\Exception\CannotCheckoutEmptyBasket;
use Wizacha\Marketplace\Basket\Exception\CustomerIsTooYoung;
use Wizacha\Marketplace\Basket\Exception\MissingPaymentException;
use Wizacha\Marketplace\Basket\Exception\ProductNotBelongsToUserDivision;
use Wizacha\Marketplace\Basket\Exception\UnavailablePaymentException;
use Wizacha\Marketplace\Order\Action\Cancel;
use Wizacha\Marketplace\Order\Action\CommitTo;
use Wizacha\Marketplace\Order\Action\Confirm;
use Wizacha\Marketplace\Order\Action\RedirectToPaymentProcessor;
use Wizacha\Marketplace\Order\Action\Trash;
use Wizacha\Marketplace\Order\OrderDataType;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\Payment\Event\BankwirePaymentEvent;
use Wizacha\Marketplace\Payment\Event\NoPaymentEvent;
use Wizacha\Marketplace\Payment\Event\OfflinePaymentEvent;
use Wizacha\Marketplace\Payment\Exception\SepaMandateNotCreatedException;
use Wizacha\Marketplace\Payment\Exception\SepaMandateNotSignedException;
use Wizacha\Marketplace\Payment\MandateService;
use Wizacha\Marketplace\Payment\MandateStatus;
use Wizacha\Marketplace\Payment\LemonWay\Exception\LemonWayException;
use Wizacha\Marketplace\Payment\PaymentProcessorIdentifier;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\PaymentService;
use Wizacha\Marketplace\Payment\PaymentType;
use Wizacha\Marketplace\Payment\Response\NoPaymentResponse;
use Wizacha\Marketplace\Payment\Stripe\BankAccountIsMissing;
use Wizacha\Marketplace\Payment\UserMandate;
use Wizacha\Marketplace\Payment\UserPaymentInfoService;
use Wizacha\Marketplace\Promotion\PromotionService;
use Wizacha\Marketplace\ReadModel\Basket as BasketReadModel;
use Wizacha\Marketplace\Subscription\SubscriptionService;
use Wizacha\Marketplace\User\Exception\InvalidProfileFields;
use Wizacha\Marketplace\User\UserService;
use Wizacha\Order;
use Wizacha\OrderStatus as LegacyOrderStatus;
use Wizacha\Sentinel\AlertManager;

class Checkout
{
    /** @var PromotionService */
    private $promotionService;

    /** @var PaymentService */
    private $paymentService;

    /** @var OrderService */
    private $orderService;

    /** @var UserService */
    private $userService;

    /** @var Trash */
    private $trashAction;

    /** @var Confirm */
    private $confirm;

    /** @var EventDispatcherInterface */
    private $eventDispatcher;

    /** @var SubscriptionService */
    private $subscriptionService;

    /** @var UserPaymentInfoService */
    protected $userPaymentInfoService;

    /** @var CommitTo */
    protected $commitTo;

    /** @var RedirectToPaymentProcessor */
    protected $redirectToPaymentProcessor;

    /** @var int */
    protected $paymentDeadline;

    /** @var AlertManager */
    private $alertManager;

    private MandateService $mandateService;

    private Cancel $cancel;

    public function __construct(
        PromotionService $promotionService,
        PaymentService $paymentService,
        OrderService $orderService,
        UserService $userService,
        MandateService $mandateService,
        Trash $trashAction,
        Confirm $confirm,
        EventDispatcherInterface $eventDispatcher,
        SubscriptionService $subscriptionService,
        CommitTo $commitTo,
        ?string $featurePaymentDeadline,
        RedirectToPaymentProcessor $redirectToPaymentProcessor,
        AlertManager $alertManager,
        Cancel $cancel
    ) {
        $this->promotionService = $promotionService;
        $this->paymentService = $paymentService;
        $this->orderService = $orderService;
        $this->userService = $userService;
        $this->trashAction = $trashAction;
        $this->confirm = $confirm;
        $this->eventDispatcher = $eventDispatcher;
        $this->subscriptionService = $subscriptionService;
        $this->commitTo = $commitTo;

        if (\is_numeric($featurePaymentDeadline) === true) {
            $this->paymentDeadline = (int) $featurePaymentDeadline;
        } else {
            $this->paymentDeadline = 0;
        }

        $this->redirectToPaymentProcessor = $redirectToPaymentProcessor;
        $this->alertManager = $alertManager;
        $this->mandateService = $mandateService;

        $this->cancel = $cancel;
    }

    public function setUserPaymentInfoService(UserPaymentInfoService $userPaymentInfoService): self
    {
        $this->userPaymentInfoService = $userPaymentInfoService;
        return $this;
    }

    /**
     * Checkout a basket into an order.
     *
     * @throws MissingPaymentException
     * @throws CannotCheckoutEmptyBasket
     * @throws CustomerIsTooYoung
     * @throws InvalidProfileFields
     * @throws ProductNotBelongsToUserDivision
     * @throws SystemError
     * @throws ApiException
     * @throws \Exception all payments exception are thrown here (Lemonway, Mangopay, HiPay, ...)
     */
    public function checkout(
        BasketReadModel $basket,
        int $paymentId = null,
        int $userId,
        string $redirectUrl = null,
        string $cssUrl = null,
        $cart = null,
        string $locale = null
    ): CheckoutResult {
        $cart = $this->validateBasket($basket, $userId, $cart);

        $cart['subscriptionId'] = array_shift(array_values($basket->getProducts()))['subscriptionId'];

        //add pickupInformation
        foreach ($cart['product_groups'] as $key => $shipping) {
            foreach ($basket->getData()['groups'] as $groupsInfo) {
                foreach ($groupsInfo['products'] as $productsInfo) {
                    if (\is_array($shipping['shippings']) === true
                        && \is_array($productsInfo['shippings']) === true
                        && \count(\array_diff_key($shipping['shippings'], $productsInfo['shippings'])) === 0
                    ) {
                        $cart['product_groups'][$key]['pickupPointId'] = $productsInfo['pickupPointId'];
                        $cart['product_groups'][$key]['title'] = $productsInfo['title'];
                        $cart['product_groups'][$key]['firstName'] = $productsInfo['firstName'];
                        $cart['product_groups'][$key]['lastName'] = $productsInfo['lastName'];
                    }
                }
            }
        }

        if ($basket->getTotal()->isZero()) {
            $paymentId = 0;
        }
        // Prevent using disabled payment method by challenging HTTP data
        if (!empty($paymentId)) {
            $cart['payment_id'] = $paymentId;
        } elseif ($basket->getTotal()->isPositive()) {
            throw new MissingPaymentException();
        }

        if (isset($cart['payment_id'])) {
            $paymentMethodData = fn_get_payment_method_data($cart['payment_id']);
            if (!empty($paymentMethodData['status']) && $paymentMethodData['status'] != 'A') {
                throw new \Exception('Disabled payment');
            }

            /**
             * Check if payment method is SEPA_DIRECT_DEBIT or DEFERED_SEPA_DIRECT_DEBIT
             * and if it is we check if user have mandate registered in UserPaymentInfoService
             * if not mandate can be found, we cannot continue payment.
             */
            if (true === \array_key_exists('processor_id', $paymentMethodData)
                && true === PaymentProcessorIdentifier::isValid((int) $paymentMethodData['processor_id'])
            ) {
                $paymentMethod = new PaymentProcessorIdentifier((int) $paymentMethodData['processor_id']);

                if (true === $paymentMethod->equals(PaymentProcessorIdentifier::HIPAY_DEFERED_SEPA_DIRECT_DEBIT())
                    || true === $paymentMethod->equals(PaymentProcessorIdentifier::HIPAY_SEPA_DIRECT_DEBIT())
                ) {
                    $this->checkUserHiPayMandate($userId);
                }

                if (true === $paymentMethod->equals(PaymentProcessorIdentifier::LEMONWAY_DEFERED_SEPA_DIRECT_DEBIT())
                    || true === $paymentMethod->equals(PaymentProcessorIdentifier::LEMONWAY_SEPA_DIRECT_DEBIT())
                ) {
                    $this->checkUserLemonWayMandate($userId);
                }
            }
        }

        // Trash previous failed order
        if (!empty($cart['failed_order_id']) || !empty($cart['processed_order_id'])) {
            $orderIds = !empty($cart['failed_order_id']) ? $cart['failed_order_id'] : $cart['processed_order_id'];

            foreach ($orderIds as $orderId) {
                $orderToTrash = $this->orderService->getOrder($orderId);
                if ($this->trashAction->isAllowed($orderToTrash)) {
                    $this->trashAction->execute($orderToTrash);
                }
            }

            unset($cart['failed_order_id'], $cart['processed_order_id']);
        }

        if (!empty($cart['extra_payment_info'])) {
            $cart['payment_info'] = empty($cart['payment_info']) ? array() : $cart['payment_info'];
            $cart['payment_info'] = array_merge($cart['extra_payment_info'], $cart['payment_info']);
        }

        $cart['payment_info']['locale'] = $locale;

        // Simulate CS Cart's auth with what's necessary for this method
        $auth = [
            'user_id' => $userId,
            'usergroup_ids' => [0],
            'area' => 'C',
            'tax_exempt' => 'N',
        ];
        list($orderId) = fn_place_order($cart, $auth);

        if (empty($orderId)) {
            throw new \Exception('Placing order failed (no reason given by CS Cart)');
        }
        $orderId = (int) $orderId;

        // Record promotion usage
        foreach ($cart['new_promotion_ids'] as $promotionId) {
            $this->promotionService->recordPromotionUsage($userId, $promotionId, $orderId);
        }

        // TODO: what does this do?
        // PS: no need to post that on slack. Already done by may be one hundred developers before you.
        db_query("REPLACE INTO ?:order_data ?e", [
            'order_id' => $orderId,
            'type' => OrderDataType::TIMESTAMP_INFO()->getValue(),
            'data' => TIME,
        ]);

        if (isset($cart['payment_id'])) {
            /**
             * According to the API documentation: "This endpoint will create the order in a state waiting for payment"...
             * But in fact, for any direct debit (SEPA) payment, if the user already as a mandate, the payment request
             * will be done here...
             */
            try {
                $paymentResponse = $this->paymentService->pay($orderId, $cart['payment_id'], $redirectUrl, $cssUrl);
            } catch (LemonWayException $lemonWayException) {
                throw new UnavailablePaymentException($basket->getId(), $orderId, $lemonWayException->getResponseMessage(), $lemonWayException->getResponseCode(), $lemonWayException);
            } catch (\Exception $exception) {
                throw new UnavailablePaymentException($basket->getId(), $orderId, $exception->getMessage(), Response::HTTP_SERVICE_UNAVAILABLE, $exception);
            }

            // We want to return the list of all orders (not the virtual parent order)
            $orders = $this->orderService->getChildOrders($orderId);
            foreach ($orders as $order) {
                container()->get('Wizacha\Marketplace\Commission\CommissionService')->saveOrderAmountsCommission($order);
            }


            if (PaymentType::BANK_TRANSFER()->equals($this->paymentService->getPaymentType($cart['payment_id'])) === true
                && \in_array($this->paymentService->getPaymentProcessorName($cart['payment_id'])->getValue(), [
                    PaymentProcessorName::MANGOPAY()->getValue(),
                    PaymentProcessorName::LEMONWAY()->getValue()
                ]) === true
            ) {
                foreach ($orders as $order) {
                    $statusUpdated = false;

                    if ($this->paymentService->getPaymentProcessorName($cart['payment_id'])->getValue() === PaymentProcessorName::LEMONWAY()->getValue()) {
                        if ($this->confirm->isAllowed($order)) {
                            $this->confirm->execute($order);
                        }

                        if ($this->redirectToPaymentProcessor->isAllowed($order)) {
                            $this->redirectToPaymentProcessor->execute($order);
                        }

                        if ($order->getStatus()->equals(OrderStatus::INCOMPLETED()) === true) {
                            fn_change_order_status($orderId, LegacyOrderStatus::STANDBY_BILLING);
                            $statusUpdated = true;
                        }
                    }

                    if (false === $statusUpdated) {
                        $this->orderService->overrideOrderStatus($order);
                    }
                }
            }

            if (PaymentType::PAYMENT_DEFERMENT()->equals($this->paymentService->getPaymentType($cart['payment_id'])) === true
                && $this->paymentDeadline > 0
            ) {
                foreach ($orders as $order) {
                    if ($this->commitTo->isAllowed($order) === true) {
                        $this->commitTo->execute(
                            $order,
                            $order->getTimestamp()->modify('+' . $this->paymentDeadline . ' day'),
                            static::generateCommitmentNumber()
                        );
                    }
                }
            }
        } else {
            // We want to return the list of all orders (not the virtual parent order)
            $orders = $this->orderService->getChildOrders($orderId);

            // Special case for orders with a total of 0 Euros: no payment processor is executed
            if ($basket->getTotal()->isZero()) {
                try {
                    $this->eventDispatcher->dispatch(
                        new OfflinePaymentEvent(new Order($orderId)),
                        OfflinePaymentEvent::class
                    );

                    foreach ($orders as $order) {
                        if ($this->confirm->isAllowed($order)) {
                            $this->confirm->execute($order);
                        }
                    }
                } catch (\Exception $exception) {
                    foreach ($orders as $order) {
                        if ($this->cancel->isAllowed($order)) {
                            $this->cancel->execute($order);
                        }
                    }

                    throw $exception;
                }
            }

            $paymentResponse = new NoPaymentResponse();
        }

        $this->validateShipping($cart, $orderId);

        $this->subscriptionService->createSubscription($orders);

        return new CheckoutResult($orders, $paymentResponse);
    }

    /**
     * Check if a basket is valid for the checkout.
     *
     * @throws CannotCheckoutEmptyBasket
     * @throws CustomerIsTooYoung
     * @throws InvalidProfileFields
     * @throws \Wizacha\Marketplace\Promotion\Exception\PromotionNotFound
     * @throws ProductNotBelongsToUserDivision
     *
     * @return mixed[]
     */
    public function validateBasket(BasketReadModel $basket, int $userId, $cart = null): array
    {
        if ($basket->getTotalQuantity() === 0) {
            throw new CannotCheckoutEmptyBasket($basket->getId());
        }

        $cart = $cart ?? $basket->getCscartData($userId);

        if (!AgeVerification::check($cart['user_data'], $cart['age_verification'])) {
            throw new CustomerIsTooYoung((int) $cart['age_verification']);
        }

        if (container()->getParameter('feature.available_offers')
            && !$basket->hasValidProductDivision($cart['user_data']['s_division_code'])
        ) {
            $invalidProducts = $basket->getInvalidProductDivision($cart['user_data']['s_division_code']);

            $message = 'User division ' . $cart['user_data']['s_division_code'] . ' does not give acces to product(s) ' . implode(',', $invalidProducts);

            throw new ProductNotBelongsToUserDivision($message);
        }

        // I think "O" means order -> check fields required for the checkout view
        $this->userService->validateProfileFields($userId, (int) $cart['user_data']['profile_id']);

        return $cart;
    }

    public function checkUserHiPayMandate(int $userId): void
    {
        $debitMandateAgreement = $this
            ->userPaymentInfoService
            ->getHipaySepaAgreement($userId)
        ;

        if (true === \is_null($debitMandateAgreement)) {
            throw new SepaMandateNotCreatedException(__('sepa_mandate_not_found'));
        }
    }

    public function checkUserLemonWayMandate(int $userId): void
    {
        $debitMandateAgreement = $this
            ->userPaymentInfoService
            ->getLemonwaySepaAgreement($userId)
        ;

        if (true === \is_null($debitMandateAgreement)) {
            throw new SepaMandateNotCreatedException(__('sepa_mandate_not_found'));
        }

        $userMandate = $this->mandateService->findOneMandateBy(["agreementId" => $debitMandateAgreement, 'status' => MandateStatus::ENABLED()]);

        if ($userMandate instanceof UserMandate === false) {
            throw new SepaMandateNotSignedException(__('sepa_mandate_not_signed'));
        }
    }

    public static function generateCommitmentNumber(): string
    {
        return uniqid('commitment_');
    }

    /**
     * @param mixed[] $cart
     */
    private function validateShipping(array $cart, int $orderId): void
    {
        foreach ($this->orderService->getChildOrders($orderId) as $order) {
            if (\in_array($order->getShippingId(), array_map('intval', $cart['chosen_shipping']), true) === false
                && $order->getShippingId() !== 0
            ) {
                $this->alertManager->send(
                    'Shipping Id inconsistency between a basket and its order',
                    [
                        'cart_basket_id' => $cart['basket_id'],
                        'cart_chosen_shipping' => $cart['chosen_shipping'],
                        'order_id' => $order->getId(),
                        'order_shipping_id' => $order->getShippingId(),
                    ]
                );
            }
        }
    }
}
