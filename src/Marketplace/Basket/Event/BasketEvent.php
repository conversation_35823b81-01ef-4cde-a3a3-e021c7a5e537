<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Basket\Event;

use Broadway\Serializer\SerializableInterface;

abstract class BasketEvent implements SerializableInterface
{
    protected $basketId;

    public function __construct($basketId)
    {
        $this->basketId = $basketId;
    }

    /**
     * @return string
     */
    public function getBasketId()
    {
        return $this->basketId;
    }
}
