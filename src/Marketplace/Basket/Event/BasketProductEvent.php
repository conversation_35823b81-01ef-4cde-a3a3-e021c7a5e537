<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Basket\Event;

use Symfony\Contracts\EventDispatcher\Event;
use Wizacha\Marketplace\Entities\Declination;

class BasketProductEvent extends Event
{
    private $basketId;

    private $declination;

    private $quantity;

    public function __construct(string $basketId, Declination $declination, int $quantity)
    {
        $this->basketId = $basketId;
        $this->declination = $declination;
        $this->quantity = $quantity;
    }

    public function getBasketId(): string
    {
        return $this->basketId;
    }

    public function getDeclination(): Declination
    {
        return $this->declination;
    }

    public function getQuantity(): int
    {
        return $this->quantity;
    }
}
