<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Basket\Event;

use BroadwaySerialization\Serialization\Serializable;

class OrderIdWasGeneratedOnBasket extends BasketEvent
{
    use Serializable;

    protected $orderId;

    public function __construct($basketId, $orderId)
    {
        parent::__construct($basketId);
        $this->orderId = $orderId;
    }

    public function getOrderId()
    {
        return $this->orderId;
    }
}
