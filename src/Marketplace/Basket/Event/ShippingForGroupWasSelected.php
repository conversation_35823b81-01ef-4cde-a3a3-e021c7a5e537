<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Basket\Event;

use BroadwaySerialization\Serialization\Serializable;

class ShippingForGroupWasSelected extends BasketEvent
{
    use Serializable;

    protected $groupId;

    protected $shippingId;

    public function __construct($basketId, $groupId, $shippingId)
    {
        $this->groupId = $groupId;
        $this->shippingId = $shippingId;
        parent::__construct($basketId);
    }

    public function getGroupId()
    {
        return $this->groupId;
    }

    public function getShippingId()
    {
        return $this->shippingId;
    }
}
