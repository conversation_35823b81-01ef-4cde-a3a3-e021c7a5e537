<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Basket\Event;

use BroadwaySerialization\Serialization\Serializable;

class CouponWasRemoved extends BasketEvent
{
    use Serializable;

    /**
     * @var string
     */
    protected $coupon;

    public function __construct(string $basketId, string $coupon)
    {
        parent::__construct($basketId);
        $this->coupon = $coupon;
    }

    public function getCoupon(): string
    {
        return $this->coupon;
    }
}
