<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Basket\Event;

use BroadwaySerialization\Serialization\Serializable;

class CouponWasApplied extends BasketEvent
{
    use Serializable;

    /**
     * @var string
     */
    protected $coupon;

    /**
     * @param string $basketId
     * @param string $coupon
     */
    public function __construct($basketId, $coupon)
    {
        parent::__construct($basketId);
        $this->coupon = $coupon;
    }

    /**
     * @return string
     */
    public function getCoupon()
    {
        return $this->coupon;
    }
}
