<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Basket\Event;

use BroadwaySerialization\Serialization\Serializable;

class CommentWasApplied extends BasketEvent
{
    use Serializable;

    protected $comment;

    public function __construct(string $basketId, string $comment)
    {
        parent::__construct($basketId);
        $this->comment = $comment;
    }

    public function getComment(): string
    {
        return $this->comment;
    }
}
