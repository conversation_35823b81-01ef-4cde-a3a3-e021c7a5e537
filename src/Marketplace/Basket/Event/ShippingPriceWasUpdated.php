<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Basket\Event;

use BroadwaySerialization\Serialization\Serializable;

class ShippingPriceWasUpdated extends BasketEvent
{
    use Serializable;

    protected int $groupId;
    protected int $shippingId;
    protected ?float $price;

    public function __construct(string $basketId, int $groupId, int $shippingId, ?float $price)
    {
        $this->groupId = $groupId;
        $this->shippingId = $shippingId;
        $this->price = $price;
        parent::__construct($basketId);
    }

    public function getGroupId(): int
    {
        return $this->groupId;
    }

    public function getShippingId(): int
    {
        return $this->shippingId;
    }

    public function getPrice(): ?float
    {
        return $this->price;
    }
}
