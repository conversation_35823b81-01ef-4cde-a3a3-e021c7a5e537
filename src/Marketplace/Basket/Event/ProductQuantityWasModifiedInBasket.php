<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Basket\Event;

use BroadwaySerialization\Serialization\Serializable;
use Wizacha\Marketplace\Entities\Declination;
use Wizacha\Marketplace\Entities\ProductShippingRate;
use Wizacha\Marketplace\Subscription\SubscriptionProduct;

class ProductQuantityWasModifiedInBasket extends BasketEvent
{
    use Serializable;

    /** @var string */
    protected $productId;

    /** @var int */
    protected $companyId;

    /** @var int[] */
    protected $shippingsIds;

    /** @var int */
    protected $quantity;

    /** @var string */
    protected $productClass;

    /** @var string */
    protected $subscriptionId;

    /**
     * @param string $basketId
     * @param Declination $product
     * @param integer $quantity
     */
    public function __construct($basketId, Declination $product, $quantity)
    {
        parent::__construct($basketId);

        $this->productId    = $product->getId();
        $this->shippingsIds = array_map(function (ProductShippingRate $shippingRate) {
            return $shippingRate->getShipping()->getId();
        }, $product->getProductActiveShippingsRates());
        $this->companyId    = $product->getCompany()->getId();
        $this->quantity   = $quantity;
        $this->productClass = \get_class($product->getProduct());

        if ($product->getProduct() instanceof SubscriptionProduct) {
            $this->subscriptionId = $product->getProduct()->getSubscriptionId();
        }
    }

    /**
     * @return string
     */
    public function getProductId()
    {
        return $this->productId;
    }

    /**
     * @return string
     */
    public function getCompanyId()
    {
        return $this->companyId;
    }

    /**
     * @return string
     */
    public function getShippingsIds()
    {
        return $this->shippingsIds;
    }


    /**
     * @return int|integer
     */
    public function getQuantity()
    {
        return $this->quantity;
    }

    public function getProductClass(): string
    {
        return $this->productClass;
    }

    public function getSubscriptionId(): ?string
    {
        return $this->subscriptionId;
    }
}
