<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Basket\Event;

use BroadwaySerialization\Serialization\Serializable;
use Wizacha\Marketplace\Entities\Declination;

class ProductCommentWasModifiedInBasket extends BasketEvent
{
    use Serializable;

    protected $productId;
    protected $comment;

    public function __construct(string $basketId, Declination $product, string $comment)
    {
        parent::__construct($basketId);

        $this->productId = $product->getId();
        $this->comment   = $comment;
    }

    public function getProductId(): string
    {
        return $this->productId;
    }

    public function getComment(): string
    {
        return $this->comment;
    }
}
