<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Basket\Event;

use BroadwaySerialization\Serialization\Serializable;

class ProductsWasGrouped extends BasketEvent
{
    use Serializable;

    /**
     * @var array
     */
    protected $groups;
    protected bool $removeSelectedShippings;


    public function __construct($basketId, $groups, $removeSelectedShippings = true)
    {
        $this->groups = $groups;
        $this->removeSelectedShippings = $removeSelectedShippings;
        parent::__construct($basketId);
    }

    public function getGroups()
    {
        return $this->groups;
    }

    public function hasRemoveSelectedShippings(): bool
    {
        return $this->removeSelectedShippings;
    }
}
