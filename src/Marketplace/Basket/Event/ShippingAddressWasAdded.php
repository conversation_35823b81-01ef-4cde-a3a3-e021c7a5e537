<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Basket\Event;

use BroadwaySerialization\Serialization\Serializable;
use Wizacha\Marketplace\Address\Address;

class ShippingAddressWasAdded extends BasketEvent
{
    use Serializable;

    /**
     * @var Address
     */
    protected $address;

    public function __construct(string $basketId, Address $address)
    {
        parent::__construct($basketId);

        $this->address = $address;
    }

    public function getAddress(): Address
    {
        return $this->address;
    }

    protected static function deserializationCallbacks()
    {
        return [
            'address' => [Address::class, 'deserialize'],
        ];
    }
}
