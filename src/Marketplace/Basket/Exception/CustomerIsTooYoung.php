<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Basket\Exception;

/**
 * Some products have a minimum age limit, they cannot be bought unless you are old enough.
 */
class CustomerIsTooYoung extends \Exception
{
    /**
     * @var int
     */
    private $requiredAge;

    public function __construct(int $requiredAge)
    {
        $this->requiredAge = $requiredAge;

        parent::__construct();
    }

    public function getRequiredAge(): int
    {
        return $this->requiredAge;
    }
}
