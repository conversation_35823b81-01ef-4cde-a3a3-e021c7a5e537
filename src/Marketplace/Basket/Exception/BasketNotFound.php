<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Basket\Exception;

use Symfony\Component\HttpFoundation\Response;
use Wizacha\AppBundle\Controller\Api\Error\ApiErrorResponseProvider;
use Wizacha\Marketplace\Exception\ErrorCode;
use Wizacha\AppBundle\Controller\Api\Error\ErrorResponse;
use Wizacha\Marketplace\Exception\NotFound;

class BasketNotFound extends NotFound implements ApiErrorResponseProvider
{
    /**
     * @var string
     */
    private $basketId;

    public function __construct(string $basketId)
    {
        parent::__construct(sprintf('Basket "%s" not found', $basketId), ErrorCode::BASKET_NOT_FOUND()->getValue());
        $this->basketId = $basketId;
    }

    public function getBasketId(): string
    {
        return $this->basketId;
    }

    public function toApiErrorResponse(): ErrorResponse
    {
        return new ErrorResponse(ErrorCode::BASKET_NOT_FOUND(), 'basket not found', ['basket_id' => $this->getBasketId()], Response::HTTP_NOT_FOUND);
    }
}
