<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Basket\Exception;

use Symfony\Component\HttpFoundation\Response;
use Wizacha\AppBundle\Controller\Api\Error\ApiErrorResponseProvider;
use Wizacha\Marketplace\Exception\ErrorCode;
use Wizacha\AppBundle\Controller\Api\Error\ErrorResponse;
use Wizacha\Marketplace\Exception\NotFound;

class CouponNotFound extends NotFound implements ApiErrorResponseProvider
{
    /**
     * @var string
     */
    private $coupon;

    public function __construct(string $coupon)
    {
        parent::__construct(sprintf('Coupon "%s" not found', $coupon), ErrorCode::COUPON_CODE_DOES_NOT_APPLY()->getValue());

        $this->coupon = $coupon;
    }

    public function getCoupon(): string
    {
        return $this->coupon;
    }

    public function toApiErrorResponse(): ErrorResponse
    {
        return new ErrorResponse(ErrorCode::COUPON_CODE_DOES_NOT_APPLY(), 'basket not found', ['coupon' => $this->getCoupon()], Response::HTTP_NOT_FOUND);
    }
}
