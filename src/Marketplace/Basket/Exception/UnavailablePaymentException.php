<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Basket\Exception;

use Wizacha\AppBundle\Controller\Api\Error\ApiErrorResponseProvider;
use Wizacha\Marketplace\Exception\ErrorCode;
use Wizacha\AppBundle\Controller\Api\Error\ErrorResponse;
use Symfony\Component\HttpFoundation\Response;

class UnavailablePaymentException extends \Exception implements ApiErrorResponseProvider
{
    /** @var string */
    private $basketId;

    /** @var int */
    private $orderId;

    public function __construct(string $basketId, int $orderId, $message = '', int $code = Response::HTTP_SERVICE_UNAVAILABLE, \Throwable $previousException = null)
    {
        parent::__construct($message, $code, $previousException);
        $this->basketId = $basketId;
        $this->orderId = $orderId;
    }

    public function getBasketId(): string
    {
        return $this->basketId;
    }

    public function getOrderId(): int
    {
        return $this->orderId;
    }

    public function toApiErrorResponse(): ErrorResponse
    {
        return new ErrorResponse(
            ErrorCode::UNAVAILABLE_PAYMENT(),
            $this->getMessage(),
            [
                'basket_id' => $this->getBasketId(),
                'order_id' => $this->getOrderId()
            ],
            $this->getCode()
        );
    }
}
