<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Basket\Exception;

use Symfony\Component\HttpFoundation\Response;
use Wizacha\AppBundle\Controller\Api\Error\ApiErrorResponseProvider;
use Wizacha\Marketplace\Exception\ErrorCode;
use Wizacha\AppBundle\Controller\Api\Error\ErrorResponse;

class CannotCheckoutEmptyBasket extends \Exception implements ApiErrorResponseProvider
{
    /**
     * @var string
     */
    private $basketId;

    public function __construct(string $basketId)
    {
        parent::__construct('Cannot checkout empty basket', ErrorCode::BASKET_IS_EMPTY()->getValue());
        $this->basketId = $basketId;
    }

    public function toApiErrorResponse(): ErrorResponse
    {
        return new ErrorResponse(
            ErrorCode::BASKET_IS_EMPTY(),
            'Cannot checkout empty basket',
            [
                'basketId' => $this->basketId,
            ],
            Response::HTTP_BAD_REQUEST
        );
    }
}
