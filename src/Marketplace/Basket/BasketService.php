<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Basket;

use Broadway\CommandHandling\CommandBusInterface;
use Broadway\ReadModel\RepositoryInterface as ReadModelRepository;
use Broadway\Repository\AggregateNotFoundException;
use Broadway\Repository\RepositoryInterface;
use Broadway\UuidGenerator\UuidGeneratorInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Wizacha\Bridge\Broadway\DatabaseRepository;
use Wizacha\Marketplace\Address\Address;
use Wizacha\Marketplace\Basket\Command as Commands;
use Wizacha\Marketplace\Basket\Event\BasketProductEvent;
use Wizacha\Marketplace\Basket\Exception\BasketNotFound;
use Wizacha\Marketplace\Basket\Exception\CannotAddCommentToMissingProduct;
use Wizacha\Marketplace\Basket\Exception\CannotAddNonTransactionalProduct;
use Wizacha\Marketplace\Basket\Exception\CannotMergeSameBasket;
use Wizacha\Marketplace\Basket\Exception\CouponNotFound;
use Wizacha\Marketplace\Basket\ReadModel\Basket as ReadModelBasket;
use Wizacha\Marketplace\Entities\Declination;
use Wizacha\Marketplace\Entities\OriginalProductDeclination;
use Wizacha\Marketplace\Exception\DeclinationIdNotFoundInReadModelException;
use Wizacha\Marketplace\Exception\InvalidDeclinationException;
use Wizacha\Marketplace\Exception\InvalidGroupException;
use Wizacha\Marketplace\Exception\InvalidShippingException;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Group\UserGroupService;
use Wizacha\Marketplace\PIM\Stock\StockService;
use Wizacha\Marketplace\PIM\TransactionMode\TransactionMode;
use Wizacha\Marketplace\Promotion\BasketPromotion;
use Wizacha\Marketplace\Promotion\MarketplacePromotion;
use Wizacha\Marketplace\Promotion\PromotionService;
use Wizacha\Marketplace\ReadModel\Basket as BasketReadModel;
use Wizacha\Marketplace\ReadModel\ProductRepository;
use Wizacha\Marketplace\Shipping\DeliveryType;

class BasketService
{
    /**
     * @var CommandBusInterface
     */
    protected $commandBus;

    /**
     * @var UuidGeneratorInterface
     */
    protected $idGenerator;

    /**
     * @var RepositoryInterface
     */
    private $aggregateRepository;

    /**
     * @var ReadModelRepository
     */
    private $readModelRepository;

    /**
     * @var \Wizacha\Marketplace\PIM\Stock\StockService
     */
    protected $stockService;

    /**
     * @var EventDispatcherInterface
     */
    private $eventDispatcher;

    /** @var  PromotionService */
    private $promotionService;

    /** @var ProductRepository */
    protected $productRepository;

    /** @var LoggerInterface */
    private $logger;

    private UserGroupService $userGroupService;

    public function __construct(
        CommandBusInterface $commandBus,
        UuidGeneratorInterface $idGenerator,
        RepositoryInterface $aggregateRepository,
        ReadModelRepository $readModelRepository,
        StockService $stockService,
        EventDispatcherInterface $eventDispatcher,
        PromotionService $promotionService,
        ProductRepository $productRepository,
        LoggerInterface $logger,
        UserGroupService $userGroupService
    ) {
        $this->commandBus = $commandBus;
        $this->idGenerator = $idGenerator;
        $this->aggregateRepository = $aggregateRepository;
        $this->readModelRepository = $readModelRepository;
        $this->stockService = $stockService;
        $this->eventDispatcher = $eventDispatcher;
        $this->promotionService = $promotionService;
        $this->productRepository = $productRepository;
        $this->logger = $logger;
        $this->userGroupService = $userGroupService;
    }

    /**
     * @return bool True if the basket is OK, false if it has been modified
     * @throws BasketNotFound
     */
    public function checkIntegrity(string $basketId): bool
    {
        $basket = $this->getAggregateById($basketId);

        return $this->checkProductsQuantity($basket) && $this->checkShippings($basket);
    }

    /**
     * @return bool True if the basket is OK, false if it has been modified
     */
    public function checkShippings(Basket $basket): bool
    {
        $hasBeenModified = false;
        $newBasket = new Basket();
        $newBasket->importProducts($basket->getProductCountById());

        if ($newBasket->generateGroups() != $basket->generateGroups()) {
            $oldProductCountById = $basket->getProductCountById();
            foreach ($oldProductCountById as $productId => $productData) {
                $declination = Declination::fromId($productId);
                $command = new Commands\ModifyProductQuantityInBasket($basket->getAggregateRootId(), $declination, $productData['quantity']);
                $this->commandBus->dispatch($command);
            }
            $hasBeenModified = true;
            fn_set_notification('W', __('warning'), __('modify_shipping_group_in_basket'));
        }

        return !$hasBeenModified;
    }

    /**
     * @return bool True if the basket is OK, false if it has been modified
     */
    public function checkProductsQuantity(Basket $basket): bool
    {
        $hasBeenModified = false;
        foreach ($basket->getProductCountById() as $productId => $productData) {
            $declination = Declination::fromId($productId);

            // a declination was created after original product was added in the basket: we must remove the original from the basket
            $originalProductInBasket = $declination->getCombinationCode() !== "0" && $productId === $declination->getProduct()->getOriginalDeclinationId();

            if (false === $declination->isAvailableToBasket() || true === $originalProductInBasket) {
                $quantityAddable = 0;
            } else {
                $quantityAddable = $this->stockService->book($declination, $productData['quantity'], $basket->getAggregateRootId());
            }
            if ($quantityAddable < 1 || $quantityAddable < $productData['quantity']) {
                if ($quantityAddable < 1) {
                    // if the quantity is negative, we delete the product from the basket
                    $quantityAddable = 0 ;
                    fn_set_notification('W', __('error'), __('out_of_stock_product_in_cart'));
                } else {
                    fn_set_notification('W', __('warning'), __('modify_product_quantity_in_basket'));
                }

                $hasBeenModified = true;
                $declinationToAdd = $originalProductInBasket ? new OriginalProductDeclination($declination->getProduct()) : $declination;
                $command = new Commands\ModifyProductQuantityInBasket($basket->getAggregateRootId(), $declinationToAdd, $quantityAddable);
                $this->commandBus->dispatch($command);
            }
        }

        return !$hasBeenModified;
    }

    /**
     * @param string $basketId
     * @param Declination $declination
     * @param integer $quantity
     * @return int Actual quantity added to the basket
     * @throws CannotAddNonTransactionalProduct
     * @throws BasketNotFound
     */
    public function addProductToBasket($basketId, Declination $declination, $quantity): int
    {
        if (!$declination->isActive()) {
            return 0;
        }

        if (!$declination->getTransactionMode()->equals(TransactionMode::TRANSACTIONAL())) {
            throw new CannotAddNonTransactionalProduct();
        }

        $basket = $this->getAggregateById($basketId);
        $isAlreadyInBasket = $basket->productIsInBasket($declination->getId());
        $totalQuantity = $basket->getQuantityAfterAdding($declination->getId(), $quantity);
        $bookedQuantity = $this->stockService->book($declination, $totalQuantity, $basketId);

        $command = new Commands\ModifyProductQuantityInBasket(
            $basketId,
            $declination,
            $bookedQuantity
        );
        $this->commandBus->dispatch($command);

        if ($isAlreadyInBasket) {
            $this->eventDispatcher->dispatch(
                new BasketProductEvent($basketId, $declination, $bookedQuantity),
                BasketEvents::PRODUCT_MODIFIED
            );
        } else {
            $this->eventDispatcher->dispatch(
                new BasketProductEvent($basketId, $declination, $bookedQuantity),
                BasketEvents::PRODUCT_ADDED
            );
        }

        return $bookedQuantity;
    }

    /**
     * @throws CannotAddCommentToMissingProduct
     */
    public function setProductComment(string $basketId, Declination $declination, string $comment)
    {
        $basket = $this->getAggregateById($basketId);
        if (!$basket->productIsInBasket($declination->getId())) {
            throw new CannotAddCommentToMissingProduct();
        }

        $command = new Commands\ModifyProductCommentInBasket(
            $basketId,
            $declination,
            $comment
        );
        $this->commandBus->dispatch($command);
    }

    public function setComment(string $basketId, string $comment): void
    {
        $basket = $this->getAggregateById($basketId);
        $basket->setComment($comment);
        $this->aggregateRepository->save($basket);
    }

    /**
     * @param string $basketId
     * @param string $groupId
     * @param string $shippingId
     */
    public function selectShippingForGroup($basketId, $groupId, $shippingId)
    {
        $command = new Commands\SelectShippingForGroup($basketId, $groupId, $shippingId);

        $this->assertValidShippingIdInShippingGroup($basketId, $groupId, $shippingId);

        $this->commandBus->dispatch($command);
    }

    public function selectShippingForAllGroups(string $basketId, $shippingId)
    {
        foreach ($this->getReadmodel($basketId)->getGroups() as $companyGroup) {
            foreach ($companyGroup->getShippingGroups() as $shippingGroup) {
                $this->selectShippingForGroup($basketId, $shippingGroup->getGroupId(), $shippingId);
            }
        }
    }

    public function updateCustomShippingPrice(string $basketId, int $shippingGroupId, int $shippingId, ?float $price): void
    {
        $command = new Commands\UpdateCustomShippingPrice($basketId, $shippingGroupId, $shippingId, $price);

        $this->commandBus->dispatch($command);
    }

    public function resetCustomShippingPrice(string $basketId): void
    {
        $basket = $this->getAggregateById($basketId);
        foreach ($basket->generateGroups() as $company) {
            foreach ($company as $shippingGroupIndex => $shippinggroup) {
                foreach ($shippinggroup['shippings'] as $shippingId) {
                    $command = new Commands\UpdateCustomShippingPrice($basketId, $shippingGroupIndex, $shippingId, null);
                    $this->commandBus->dispatch($command);
                }
            }
        }
    }

    /**
     * @param string $basketId
     * @param Declination $declination
     * @param integer $quantity
     */
    public function modifyProductQuantityInBasket($basketId, Declination $declination, $quantity): int
    {
        if (false === $declination->isValidCombination()) {
            throw new InvalidDeclinationException($declination);
        }

        $bookedQuantity = $this->stockService->book($declination, $quantity, $basketId);

        $command = new Commands\ModifyProductQuantityInBasket(
            $basketId,
            $declination,
            $bookedQuantity
        );
        $this->dispatchCommand($command);

        if ($bookedQuantity == 0) {
            $this->eventDispatcher->dispatch(
                new BasketProductEvent($basketId, $declination, $bookedQuantity),
                BasketEvents::PRODUCT_REMOVED
            );
        } else {
            $this->eventDispatcher->dispatch(
                new BasketProductEvent($basketId, $declination, $bookedQuantity),
                BasketEvents::PRODUCT_MODIFIED
            );
        }

        if ($bookedQuantity < $quantity) {
            fn_set_notification(
                'W',
                __('important'),
                __('w_text_cart_amount_corrected')
            );
        }

        return $bookedQuantity;
    }

    public function deleteAllProductsInBasket(string $basketId): self
    {
        foreach ($this->getReadmodel($basketId)->getAllItems() as $item) {
            $this->modifyProductQuantityInBasket(
                $basketId,
                Declination::fromId($item->getDeclinationId()),
                0
            );
        }

        $this->readModelRepository->remove($basketId);

        return $this;
    }

    /**
     * Merge content of second basket in first basket
     * @param string $basketId
     * @param string $basketIdToMerge
     * @throws BasketNotFound One of the basket was not found.
     * @throws CannotMergeSameBasket if $basketId === $basketIdToMerge
     */
    public function mergeBasket($basketId, $basketIdToMerge)
    {
        $basket = $this->getAggregateById($basketId);
        $basketToMerge = $this->getAggregateById($basketIdToMerge);

        if ($basketId === $basketIdToMerge) {
            throw new CannotMergeSameBasket();
        }

        $productsBasket = $basket->getProductCountById();
        $productsBasketToMerge = $basketToMerge->getProductCountById();
        foreach ($productsBasketToMerge as $productId => $productBasketData) {
            if (!(                \array_key_exists($productId, $productsBasket)
                && $productBasketData['quantity'] <= $productsBasket[$productId]['quantity'])
            ) {
                // remove products from temporary basket to get the real time stock
                $this->stockService->removeBooking(Declination::fromId($productId), $basketToMerge->getAggregateRootId());

                $this->modifyProductQuantityInBasket(
                    $basket->getAggregateRootId(),
                    Declination::fromId($productId),
                    $productBasketData['quantity']
                );
            }
        }

        // Check again every coupon after merging
        foreach ($basket->getCoupons() as $coupon) {
            $this->removeCoupon($basketId, $coupon);
            $this->addCoupon($basketId, $coupon);
        }
    }

    /**
     * @return string Basket ID
     */
    public function generateNewBasket(): string
    {
        $basketId = $this->idGenerator->generate();
        $command = new Commands\PickUpBasket($basketId);
        $this->commandBus->dispatch($command);

        return $basketId;
    }

    /**
     * Return first matching coupon as recorded in DB
     *
     * @param string $coupon user input coupon
     */
    public function getNormalizedCoupon(string $coupon): string
    {
        $basketPromotion = \current(
            $this->promotionService->findBasketPromotionByCoupon($coupon)
        );
        if ($basketPromotion instanceof BasketPromotion) {
            return $basketPromotion->getCoupon();
        }

        $marketplacePromotion = \current(
            $this->promotionService->findMarketplacePromotionByCoupon($coupon)
        );
        if ($marketplacePromotion instanceof MarketplacePromotion) {
            return $marketplacePromotion->getCoupon();
        }

        throw new CouponNotFound($coupon);
    }

    /**
     * Add a coupon to the basket ONLY IF IT'S VALID ( = only if the total is different after apply it )
     * @return bool True if coupon has been applied, false elsewhere
     */
    public function addCoupon(string $basketId, string $coupon, $user = null): bool
    {
        $promotionsBefore = $this->getReadmodel($basketId)->getPromotions();
        sort($promotionsBefore);
        $promotionsBefore = implode($promotionsBefore);

        $basket = $this->getAggregateById($basketId);
        $basket->addCoupon($coupon);
        $this->aggregateRepository->save($basket);

        $promotionsAfter = $this->getReadmodel($basketId)->getPromotions();
        sort($promotionsAfter);
        $promotionsAfter = implode($promotionsAfter);

        if ($promotionsBefore === $promotionsAfter) {
            $this->removeCoupon($basketId, $coupon);

            return false;
        }

        $rules = $this->promotionService->getRules($promotionsAfter);

        //Multiple rules
        if (\is_array($rules) === true && \array_key_exists('items', $rules) === true && \count($rules['items']) > 0) {
            foreach ($rules['items'] as $item) {
                if ($item['type'] === 'basket_has_users_groups') {
                    $rules = $item;
                    break;
                }
            }
        }

        if (\is_array($rules) === true && \array_key_exists('type', $rules) === true && $rules['type'] === 'basket_has_users_groups') {
            $validGroup = [];
            if ($user !== null) {
                $validGroup = \array_intersect($this->userGroupService->getUserGroups($user->getId()), $rules['groups_ids']);
            }

            if (\count($validGroup) === 0) {
                $this->removeCoupon($basketId, $coupon);

                return false;
            }
        }

        return true;
    }

    public function removeCoupon(string $basketId, string $coupon)
    {
        $basket = $this->getAggregateById($basketId);
        $basket->removeCoupon($coupon);
        $this->aggregateRepository->save($basket);
    }

    public function setBillingAddress(string $basketId, Address $address)
    {
        $command = new Commands\SetBillingAddress($basketId, $address);
        $this->commandBus->dispatch($command);
    }

    public function setShippingAddress(string $basketId, Address $address)
    {
        $command = new Commands\SetShippingAddress($basketId, $address);
        $this->commandBus->dispatch($command);
    }

    /**
     * get plain readmodel data without any process
     */
    public function getRecord(string $basketId): BasketReadModel
    {
        /** @var BasketReadModel $record */
        $record = $this->readModelRepository->find($basketId);
        if (!$record) {
            throw new BasketNotFound($basketId);
        }

        return $record;
    }

    /**
     * @return \Wizacha\Marketplace\Basket\ReadModel\Basket
     * @throws BasketNotFound
     */
    public function getReadmodel(string $basketId): ReadModelBasket
    {
        return $this->getRecord($basketId)->getReadModel();
    }

    /**
     * @deprecated Use getReadmodel() instead
     * @see \Wizacha\Marketplace\Basket\BasketService::getReadmodel()
     */
    public function getById(string $basketId): ?BasketReadModel
    {
        return $this->readModelRepository->find($basketId);
    }

    public function getBasketItems(string $basketId): array
    {
        return $this->basketItemsExpose($basketId);
    }

    public function assertExists(string $basketId): bool
    {
        $basketExists = ($this->readModelRepository instanceof DatabaseRepository)
            ? $this->readModelRepository->exists($basketId)
            : (null !== $this->readModelRepository->find($basketId));

        if (false === $basketExists) {
            throw new BasketNotFound($basketId);
        }

        return $basketExists;
    }

    public function assertValidShippingIdInShippingGroup(string $basketId, int $groupId, int $shippingId): void
    {
        $readModel = $this->getReadmodel($basketId);

        $isValidGroupId = $isValidShippingId = false;

        foreach ($readModel->getGroups() as $companyGroup) {
            foreach ($companyGroup->getShippingGroups() as $shippingGroup) {
                $exposedShippingGroup = $shippingGroup->expose();
                if ($exposedShippingGroup['id'] !== $groupId) {
                    continue;
                } else {
                    $isValidGroupId = true;
                }
                foreach ($exposedShippingGroup['shippings'] as $shipping) {
                    if ($shippingId === $shipping['id']) {
                        $isValidShippingId = true;
                    }
                }
            }
        }

        if ($isValidGroupId === false) {
            throw new InvalidGroupException($groupId);
        }

        if ($isValidShippingId === false) {
            throw new InvalidShippingException($shippingId);
        }
    }

    public function setPickupPointId(
        string $basketId,
        string $pickupPointId = null,
        string $title = null,
        string $firstName = null,
        string $lastName = null,
        DeliveryType $deliveryType,
        ?array $shippingGroupsIds
    ): void {
        $basket = $this->getAggregateById($basketId);
        $basket->modifyPickupPointId($pickupPointId, $title, $firstName, $lastName, $deliveryType, $shippingGroupsIds);
        $this->aggregateRepository->save($basket);
    }


    /**
     * @param string $basketId
     * @throws BasketNotFound
     */
    private function getAggregateById($basketId): Basket
    {
        try {
            return $this->aggregateRepository->load($basketId);
        } catch (\Throwable | AggregateNotFoundException $e) {
            container()->get('logger')->error('Error get Basket', ['exception' => $e]);
            throw new BasketNotFound($basketId);
        }
    }

    // get basket readmodel
    private function getBasketReadModel(string $basketId)
    {
        return array_map(
            function (array $product): array {
                return [
                    'declinationId' => $product['id'],
                    'productId' => explode('_', $product['id'])[0],
                    'quantity' => $product['quantity'],
                ];
            },
            array_values(
                $this->getRecord($basketId)
                    ->getProducts()
            )
        );
    }

    // keep only useful info
    private function basketItemsExpose(string $basketId): array
    {
        $basket = $this->getBasketReadModel($basketId);
        $productsById = $this->getProductsIndexedById($basket);

        return array_filter(array_map(
            function ($basketItem) use ($productsById, $basketId) {
                if (\array_key_exists($basketItem['productId'], $productsById) === true
                    && $productsById[$basketItem['productId']]->getTransactionMode()->equals(TransactionMode::TRANSACTIONAL()) === true
                ) {
                    try {
                        $declinationInfo = $productsById[$basketItem['productId']]->getRawDeclinationInfo(
                            $basketItem['declinationId']
                        );
                    } catch (DeclinationIdNotFoundInReadModelException $exception) {
                        $this->logger->error('GetItemBasketNotFound', [
                            'basketId' => $basketId,
                            'declinationId' => $basketItem['declinationId'],
                            'exception' => $exception
                        ]);

                        return false;
                    }
                    $images = $declinationInfo['images'] ?? ['id' => null];

                    $individualPrice = $this->getPrice($declinationInfo['price_tiers'], $basketItem['quantity']);

                    return [
                        'declinationId' => $declinationInfo['objectID'],
                        'productId' => $declinationInfo['product_id'],
                        'productName' => $declinationInfo['product_name'],
                        'canonicalUrl' => $declinationInfo['canonical_url'],
                        'individualPrice' => $individualPrice,
                        'crossedOutPrice' => $declinationInfo['crossed_out_price'],
                        'imageId' => reset($images)['id'],
                        'options' => $this->itemOptionsNormalize($declinationInfo),
                        'company' => [
                            'companyId' => $declinationInfo['company_id'],
                            'companyName' => $declinationInfo['company_name'],
                            'companySlug' => $declinationInfo['company_slug'],
                            'imageId' => $declinationInfo['company_image']['id'],
                        ],
                        'quantity' => $basketItem['quantity'],
                        'total' => $basketItem['quantity'] * $individualPrice,
                    ];
                }
            },
            $basket
        ));
    }

    private function getProductsIndexedById(array $basket): array
    {
        $products = $this->productRepository->findByIds(
            array_column($basket, 'productId')
        );
        $productsGroupById = [];
        foreach ($products as $product) {
            $productsGroupById[$product->getId()] = $product;
        }

        return $productsGroupById;
    }

    private function itemOptionsNormalize(array $declinationInfo): array
    {
        return array_values(
            array_map(
                function (array $declinationOption): array {
                    return [
                        'optionId' => $declinationOption['option_id'],
                        'optionName' => $declinationOption['option_name'],
                        'optionCode' => $declinationOption['code'],
                        'valueName' => $declinationOption['value_name'],
                        'imageId' => $declinationOption['image_id'],
                    ];
                },
                $declinationInfo['declination']
            )
        );
    }

    /**
     * @param int $cpt
     * @param mixed $command
     */
    private function dispatchCommand($command, int $cpt = 0): void
    {
        $hombreTentative = 3;
        try {
            $this->commandBus->dispatch($command);
        } catch (\Exception $exception) {
            $cpt++;
            if ($cpt < $hombreTentative) {
                $this->dispatchCommand($command, $cpt);
            } else {
                $this->logger->error('DBALEventStoreException', [
                    'message' => 'Data not saved after three failed attempts',
                ]);
            }
        }
    }

    /** @param mixed[] $priceTiers */
    private function getPrice(array $priceTiers, int $quantity): float
    {
        $price = 0;

        // Get the price corresponding to the quantity
        // If the price tiers feature is not activated, the $priceTiers variable contains only one record
        foreach ($priceTiers as $priceTier) {
            if ($priceTier['lowerLimit'] > $quantity) {
                break;
            }

            $price = $priceTier['includingTaxes'];
        }

        return $price;
    }
}
