<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Basket\Command;

use Wizacha\Bridge\Broadway\Exceptions\InvalidCommandException;

abstract class BasketCommand
{
    protected $basketId;

    public function __construct($basketId)
    {
        $this->basketId = $basketId;
        self::validate();
        $this->validate();
    }

    /**
     * @return string
     */
    public function getBasketId()
    {
        return $this->basketId;
    }

    /**
     * @throws \Wizacha\Bridge\Broadway\Exceptions\InvalidCommandException
     */
    protected function validate()
    {
        if (empty($this->basketId)) {
            throw new InvalidCommandException("Basket ID cannot be empty.");
        }
    }
}
