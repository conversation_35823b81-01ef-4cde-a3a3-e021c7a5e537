<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Basket\Command;

use Wizacha\Marketplace\Entities\Declination;

class ModifyProductCommentInBasket extends BasketCommand
{
    /**
     * @var Declination
     */
    protected $product;
    protected $comment;

    public function __construct(string $basketId, Declination $product, string $comment)
    {
        $this->product   = $product;
        $this->comment = $comment;
        parent::__construct($basketId);
    }

    public function getProduct(): Declination
    {
        return $this->product;
    }

    public function getComment(): string
    {
        return $this->comment;
    }
}
