<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Basket\Command;

use Wizacha\Bridge\Broadway\Exceptions\InvalidCommandException;

class UpdateCustomShippingPrice extends BasketCommand
{
    protected int $groupId;
    protected int $shippingId;
    protected ?float $price;

    public function __construct(string $basketId, int $groupId, int $shippingId, ?float $price)
    {
        $this->groupId   = $groupId;
        $this->shippingId = $shippingId;
        $this->price = $price;
        parent::__construct($basketId);
    }

    public function getGroupId(): int
    {
        return $this->groupId;
    }

    public function getShippingId(): int
    {
        return $this->shippingId;
    }

    public function getPrice(): ?float
    {
        return $this->price;
    }

    /**
     * @inheritdoc
     */
    protected function validate()
    {
        if (empty($this->groupId)) {
            throw new InvalidCommandException("GroupId cannot be empty.");
        }

        if (empty($this->shippingId)) {
            throw new InvalidCommandException("ShippingId cannot be empty.");
        }
    }
}
