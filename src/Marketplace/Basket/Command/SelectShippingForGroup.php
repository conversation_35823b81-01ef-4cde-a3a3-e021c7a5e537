<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Basket\Command;

use Wizacha\Bridge\Broadway\Exceptions\InvalidCommandException;

class SelectShippingForGroup extends BasketCommand
{
    protected $groupId;
    protected $shippingId;

    /**
     * @param int $basketId
     * @param int $groupId
     * @param int $shippingId
     */
    public function __construct($basketId, $groupId, $shippingId)
    {
        $this->groupId   = $groupId;
        $this->shippingId = $shippingId;
        parent::__construct($basketId);
    }

    public function getGroupId()
    {
        return $this->groupId;
    }

    public function getShippingId()
    {
        return $this->shippingId;
    }

    /**
     * @inheritdoc
     */
    protected function validate()
    {
        if (empty($this->groupId)) {
            throw new InvalidCommandException("GroupId cannot be empty.");
        }

        if (empty($this->shippingId)) {
            throw new InvalidCommandException("ShippingId cannot be empty.");
        }
    }
}
