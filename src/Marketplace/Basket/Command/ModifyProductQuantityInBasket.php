<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Basket\Command;

use Wizacha\Bridge\Broadway\Exceptions\InvalidCommandException;
use Wizacha\Marketplace\Entities\Declination;

class ModifyProductQuantityInBasket extends BasketCommand
{
    /**
     * @var Declination
     */
    protected $product;
    protected $quantity;

    /**
     * @param int $basketId
     * @param Declination $product
     * @param int $quantity
     */
    public function __construct($basketId, Declination $product, $quantity)
    {
        $this->product   = $product;
        $this->quantity = $quantity;
        parent::__construct($basketId);
    }

    /**
     * @return Declination
     */
    public function getProduct()
    {
        return $this->product;
    }

    /**
     * @return string
     */
    public function getQuantity()
    {
        return $this->quantity;
    }

    /**
     * @inheritdoc
     */
    protected function validate()
    {
        if (!((string) (int) $this->quantity == $this->quantity && $this->quantity >= 0)) {
            throw new InvalidCommandException(__('exceptions_invalid_quantity'));
        }
    }
}
