<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Basket\Command;

use Wizacha\Marketplace\Address\Address;

class SetBillingAddress extends BasketCommand
{
    /**
     * @var Address
     */
    private $address;

    public function __construct($basketId, Address $address)
    {
        parent::__construct($basketId);

        $this->address = $address;
    }

    public function getAddress(): Address
    {
        return $this->address;
    }
}
