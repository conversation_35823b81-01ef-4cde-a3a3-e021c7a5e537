<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Basket;

use Wizacha\Marketplace\Basket\Exception\LogicException;
use Wizacha\Marketplace\Promotion\BonusTarget\BonusTarget;
use Wizacha\Marketplace\Promotion\BonusTarget\BonusTargetProductInBasket;
use Wizacha\Money\Money;

/**
 * Dispatch discount marketplace between all orders
 */
final class Prorata
{
    /** @var Money  */
    private $total;

    /** @var Money  */
    private $discount;

    /** @var Money  */
    private $originalTotal;

    public function __construct(Money $originalTotal, Money $total, Money $discount)
    {
        $this->total = $total;
        $this->discount = $discount;
        $this->originalTotal = $originalTotal;
    }

    public function getTotal(): Money
    {
        return $this->total->multiply($this->getProrata()->getPreciseConvertedAmount());
    }

    public function getCustomerTotal(): Money
    {
        return $this->total->subtract($this->getDiscount());
    }

    public function getDiscount(): Money
    {
        return $this->discount->multiply($this->getProrata()->getPreciseConvertedAmount());
    }

    private function getProrata(): Money
    {
        if ($this->originalTotal->isPositive() === false) {
            throw new LogicException('The original total must be greater than 0.');
        }

        return $this->total->divide($this->originalTotal->getConvertedAmount());
    }
}
