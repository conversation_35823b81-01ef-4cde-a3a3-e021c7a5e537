<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Basket;

use Broadway\EventSourcing\EventSourcedAggregateRoot;
use Wizacha\Component\Chronopost\Exceptions\PickupPointException;
use Wizacha\Marketplace\Address\Address;
use Wizacha\Marketplace\Basket\Event as Events;
use Wizacha\Marketplace\Entities\Declination;
use Wizacha\Marketplace\Shipping\DeliveryType;
use Wizacha\Shipping;

/**
 * Aggregate root.
 */
class Basket extends EventSourcedAggregateRoot
{
    private $basketId;
    private $productCountById = array();
    /**
     * @var string[]
     */
    private $coupons = [];
    private $groups;
    private $selectedShippings;
    private $orderId;

    /**
     * @var Address
     */
    private $billingAddress;

    /**
     * @var Address
     */
    private $shippingAddress;

    /**
     * @return string
     */
    public function getAggregateRootId()
    {
        return $this->basketId;
    }

    public function getOrCreateOrderId()
    {
        if (!$this->orderId) {
            $orderId = '';
            $data = [];
            try {
                list($orderId) = fn_update_order($data);
                $this->apply(new Events\OrderIdWasGeneratedOnBasket($this->getAggregateRootId(), $orderId));
            } catch (PickupPointException $exception) {
                container()->get('logger')->error(
                    'PickupPoint Exception in order' . $orderId . ' : ' . $exception->getMessage(),
                    ['exception' => $exception]
                );
            }
        }

        return $this->orderId;
    }

    /**
     * @return array
     */
    public function getProductCountById()
    {
        return $this->productCountById;
    }

    /**
     * @return string[]
     */
    public function getCoupons()
    {
        return $this->coupons;
    }

    /**
     * @param array $productCountById
     * @return $this
     */
    public function importProducts($productCountById)
    {
        foreach ($productCountById as $productId => $productData) {
            $declination = Declination::fromId($productId);

            $this->applyProductQuantityWasModifiedInBasket(
                new Events\ProductQuantityWasModifiedInBasket($this->getAggregateRootId(), $declination, $productData['quantity'])
            );
        }

        return $this;
    }

    /**
     * @param int $id
     * @return Basket
     */
    public static function pickUpBasket($id)
    {
        $basket = new Basket();
        $basket->pickUp($id);

        return $basket;
    }

    public function modifyProductQuantity(Declination $product, $quantity)
    {
        $this->apply(
            new Events\ProductQuantityWasModifiedInBasket(
                $this->getAggregateRootId(),
                $product,
                $quantity
            )
        );

        $groups = $this->generateGroups();
        if ($this->groups != $groups) {
            $this->apply(new Events\ProductsWasGrouped($this->getAggregateRootId(), $groups));
        }
    }

    public function getQuantityAfterAdding($productId, $quantity)
    {
        if ($this->productIsInBasket($productId)) {
            $quantity += $this->productCountById[$productId]['quantity'];
        }

        return $quantity;
    }

    public function modifyProductComment(Declination $product, string $comment)
    {
        $this->apply(
            new Events\ProductCommentWasModifiedInBasket(
                $this->getAggregateRootId(),
                $product,
                $comment
            )
        );
    }

    public function setComment(string $comment)
    {
        $this->apply(new Events\CommentWasApplied($this->getAggregateRootId(), $comment));
    }

    public function addCoupon($coupon)
    {
        if (\in_array($coupon, $this->coupons)) {
            return;
        }
        $this->apply(new Events\CouponWasApplied($this->getAggregateRootId(), $coupon));
    }

    public function removeCoupon($coupon)
    {
        if (!\in_array($coupon, $this->coupons)) {
            return;
        }
        $this->apply(new Events\CouponWasRemoved($this->getAggregateRootId(), $coupon));
    }

    public function selectShippingForGroup($groupId, $shippingId)
    {
        $this->apply(new Events\ShippingForGroupWasSelected($this->getAggregateRootId(), $groupId, $shippingId));
    }

    public function updateCustomShippingPrice(int $groupId, int $shippingId, ?float $price): void
    {
        $this->apply(new Events\ShippingPriceWasUpdated($this->getAggregateRootId(), $groupId, $shippingId, $price));
    }

    public function applyCouponWasApplied(Events\CouponWasApplied $event)
    {
        $this->coupons[] = $event->getCoupon();
    }

    public function applyCouponWasRemoved(Events\CouponWasRemoved $event)
    {
        $this->coupons = array_diff($this->coupons, [$event->getCoupon()]);
    }

    /**
     * Gestion de l'adresse de facturation
     */
    public function setBillingAddress(Address $address)
    {
        $this->apply(new Events\BillingAddressWasAdded($this->getAggregateRootId(), $address));
    }

    public function applyBillingAddressWasAdded(Events\BillingAddressWasAdded $event)
    {
        $this->billingAddress = $event->getAddress();
    }

    /**
     * Gestion de l'adresse de livraison
     */
    public function setShippingAddress(Address $address)
    {
        $this->apply(new Events\ShippingAddressWasAdded($this->getAggregateRootId(), $address));
    }

    public function applyShippingAddressWasAdded(Events\ShippingAddressWasAdded $event)
    {
        $this->shippingAddress = $event->getAddress();
    }

    public function applyProductsWasGrouped(Events\ProductsWasGrouped $event)
    {
        $this->groups = $event->getGroups();
        $this->selectedShippings = [];
    }

    public function productIsInBasket($productId): bool
    {
        return isset($this->productCountById[$productId]) && $this->productCountById[$productId] > 0;
    }

    /**
     * @return array
     */
    public function generateGroups()
    {
        $groups = [];
        $incremntGroup = 0;
        foreach ($this->productCountById as $productId => $product) {
            $grouped = false;
            if (!empty($groups[$product['company_id']])) {
                foreach ($groups[$product['company_id']] as &$group) {
                    if (array_intersect($product['shippings'], $group['shippings'])) {
                        $group['products'][] = $productId;
                        $group['shippings'] = array_intersect($product['shippings'], $group['shippings']);
                        $grouped = true;
                        break;
                    }
                }
            }
            if (!$grouped) {
                $groups[$product['company_id']][++$incremntGroup] = [
                    'shippings' => $product['shippings'],
                    'products' => [$productId],
                ];
            }
        }

        return $groups;
    }

    public function modifyPickupPointId(
        string $pickupPointId,
        string $title,
        string $firstName,
        string $lastName,
        DeliveryType $deliveryType,
        ?array $shippingGroupsIds
    ) {
        $shippingGroups = [];
        $hasSelectedShipping = \count($this->selectedShippings) > 0 ;
        foreach ($this->groups as $companyId => $groups) {
            $shippingGroups[$companyId] = $groups;
            foreach ($groups as $key => $group) {
                if ($shippingGroupsIds === null || \in_array($key, $shippingGroupsIds) === true) {
                    foreach ($group['shippings'] as $shippingId) {
                        if (Shipping::isPickupPoint($shippingId) === true
                            && Shipping::getDeliveryType($shippingId)->equals($deliveryType) === true
                            && ($hasSelectedShipping === false
                            || ($hasSelectedShipping === true && (int) $this->selectedShippings[$key] === $shippingId))
                        ) {
                            $shippingGroups[$companyId][$key]['pickupPointId'] = $pickupPointId;
                            $shippingGroups[$companyId][$key]['title'] = $title;
                            $shippingGroups[$companyId][$key]['firstName'] = $firstName;
                            $shippingGroups[$companyId][$key]['lastName'] = $lastName;
                        }
                    }
                }
            }
        }

        $this->apply(new Events\ProductsWasGrouped($this->getAggregateRootId(), $shippingGroups, false));
    }

    protected function pickUp($id)
    {
        $this->apply(new Events\BasketWasPickedUp($id));
    }

    protected function applyBasketWasPickedUp(Events\BasketWasPickedUp $event)
    {
        $this->basketId = $event->getBasketId();
    }

    protected function applyProductQuantityWasModifiedInBasket(Events\ProductQuantityWasModifiedInBasket $event)
    {
        $productId = $event->getProductId();
        if ($event->getQuantity() == 0) {
            unset($this->productCountById[$productId]);
        } else {
            $this->productCountById[$productId] = [
                'quantity' => $event->getQuantity(),
                'company_id' => $event->getCompanyId(),
                'shippings' => $event->getShippingsIds(),
            ];
        }
    }

    protected function applyShippingForGroupWasSelected(Events\ShippingForGroupWasSelected $event)
    {
        $this->selectedShippings[$event->getGroupId()] = $event->getShippingId();
    }

    protected function applyOrderIdWasGeneratedOnBasket(Events\OrderIdWasGeneratedOnBasket $event)
    {
        $this->orderId = $event->getOrderId();
    }
}
