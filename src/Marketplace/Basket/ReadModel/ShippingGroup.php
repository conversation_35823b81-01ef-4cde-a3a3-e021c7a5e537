<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Basket\ReadModel;

use Wizacha\Marketplace\Price\PriceComposition;
use Wizacha\Marketplace\Price\PriceFields;
use Wizacha\Money\Money;

use function iter\reduce;

class ShippingGroup
{
    private int $groupId;
    /** @var array|BasketItem[] */
    private array $items;
    /** @var array|Shipping[] */
    private array $shippings = [];
    private ?Shipping $selectedShipping;
    private Money $productTotal;
    private Money $taxTotal;
    private Money $shippingTotal;
    private Money $shippingTaxTotal;
    private bool $carriagePaid;
    private ?string $pickupPointId;
    private ?string $firstName;
    private ?string $lastName;
    private ?string $title;

    public function __construct(array $data)
    {
        $this->groupId = $data['simpleGroupId'];
        $this->carriagePaid = false;

        /** @var PriceComposition $prices */
        $prices = $data['prices'];

        $this->items = array_map(function (array $itemData) {
            return new BasketItem($itemData);
        }, $data['products']);

        $shippings = $data['shippings'] ?? [];
        foreach ($shippings as $shippingData) {
            $shipping = new Shipping(
                (int) $shippingData['shippingId'],
                $shippingData['shippingName'],
                Money::fromVariable($shippingData['shippingPrice']),
                $shippingData['shippingDeliveryTime'],
                $shippingData['shippingType'],
                Money::fromVariable($shippingData['shippingTax'] ?? 0),
                $shippingData['carriagePaidThreshold'],
                $shippingData['carriagePaid'],
                $shippingData['originalPrice'],
                $shippingData['isCustomShippingPrice'],
            );

            $this->shippings[] = $shipping;
            if ($shippingData['selected']) {
                $this->selectedShipping = $shipping;

                $this->carriagePaid = $shippingData['carriagePaid'];
            }
        }

        $this->productTotal = $prices->get(PriceFields::BASKET_TOTAL());
        $this->taxTotal = $prices->get(PriceFields::TAXES());
        $this->shippingTotal = $prices->get(PriceFields::SHIPPING_TOTAL());
        $this->shippingTaxTotal = $prices->get(PriceFields::SHIPPING_TAX());
        $this->pickupPointId = $data['pickupPointId'] ?? null;
        $this->firstName = $data['firstName'] ?? null;
        $this->lastName = $data['lastName'] ?? null;
        $this->title =   $data['title'] ?? null;
    }

    public function getGroupId(): int
    {
        return $this->groupId;
    }

    /**
     * @return BasketItem[]
     */
    public function getItems(): array
    {
        return $this->items;
    }

    /**
     * @return Shipping[]
     */
    public function getShippings(): array
    {
        return $this->shippings;
    }

    public function getSelectedShipping(): ?Shipping
    {
        return $this->selectedShipping;
    }

    public function isSelected(Shipping $shipping): bool
    {
        return $this->selectedShipping === $shipping;
    }

    public function getTotalQuantity(): int
    {
        return reduce(function (int $total, BasketItem $item) {
            return $total + $item->getQuantity();
        }, $this->items, 0);
    }

    /**
     * @return BasketItem[]|\Iterator
     */
    public function getAllItems(): \Iterator
    {
        yield from $this->items;
    }

    /**
     * The total price of products (so, excluding shipping).
     */
    public function getProductTotal(): Money
    {
        return $this->productTotal;
    }

    /**
     * The total amount of taxes (shipping and products)
     */
    public function getTaxTotal(): Money
    {
        return $this->taxTotal;
    }

    /**
     * The total price of shipping.
     */
    public function getShippingTotal(): Money
    {
        return $this->shippingTotal;
    }

    /**
     * The total amount of taxes on the shipping only.
     */
    public function getShippingTaxTotal(): Money
    {
        return $this->shippingTaxTotal;
    }

    public function getPickupPointId(): ?string
    {
        return $this->pickupPointId;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function expose(): array
    {
        return [
            'id' => $this->getGroupId(),
            'items' => array_map(function (BasketItem $item) {
                return $item->expose();
            }, $this->getItems()),

            'shippings' => array_map(function (Shipping $shipping) {
                return $shipping->expose() + [
                    'selected' => $this->isSelected($shipping),
                ];
            }, $this->getShippings()),
            'carriagePaid' => $this->carriagePaid,
            'productTotalWithTaxes' => $this->getProductTotal()->getConvertedAmount(),
            'productTaxes' => $this->getTaxTotal()->subtract($this->getShippingTaxTotal())->getConvertedAmount(),
            'shippingTotalWithTaxes' => $this->getShippingTotal()->getConvertedAmount(),
            'shippingTaxes' => $this->getShippingTaxTotal()->getConvertedAmount(),
            'taxTotal' => $this->getTaxTotal()->getConvertedAmount(),
            'itemsPrice' => [
                'priceWithoutVat' => $this->getProductTotal()->subtract($this->getTaxTotal()->subtract($this->getShippingTaxTotal()))->getConvertedAmount(),
                'priceWithTaxes' => $this->getProductTotal()->getConvertedAmount(),
                'vat' => $this->getTaxTotal()->subtract($this->getShippingTaxTotal())->getConvertedAmount(),
            ],
            'selectedShippingPrice' => [
                'priceWithoutVat' => $this->getShippingTotal()->subtract($this->getShippingTaxTotal())->getConvertedAmount(),
                'priceWithTaxes' => $this->getShippingTotal()->getConvertedAmount(),
                'vat' => $this->getShippingTaxTotal()->getConvertedAmount(),
            ],
            'totalPrice' => [
                'priceWithoutVat' => $this->getProductTotal()->add($this->getShippingTotal())->subtract($this->getTaxTotal())->getConvertedAmount(),
                'priceWithTaxes' => $this->getProductTotal()->add($this->getShippingTotal())->getConvertedAmount(),
                'vat' => $this->getTaxTotal()->getConvertedAmount(),
            ],
            'pickupPointId' => $this->getPickupPointId(),
            'firstName' => $this->getFirstName(),
            'lastName' => $this->getLastName(),
            'title' => $this->getTitle(),
        ];
    }
}
