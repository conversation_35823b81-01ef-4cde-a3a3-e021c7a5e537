<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Basket\ReadModel;

use Wizacha\Money\Money;

/**
 * Represents a shipping method (for a shipping group) in a basket.
 */
class Shipping
{
    private int $id;
    private string $name;
    private Money $price;
    private Money $tax;
    private int $position;
    private string $deliveryTime;
    private string $type;
    private ?float $carriagePaidThreshold;
    private bool $carriagePaid;
    private float $originalPrice;
    private bool $isExternalPrice;

    public function __construct(
        int $id,
        string $name,
        Money $price,
        string $deliveryTime,
        string $type,
        Money $tax,
        ?float $carriagePaidThreshold,
        bool $carriagePaid,
        float $originalPrice,
        bool $isCustomShippingPrice
    ) {
        $this->id = $id;
        $this->name = $name;
        $this->price = $price;
        $this->tax = $tax;
        $this->deliveryTime = $deliveryTime;
        $this->type = $type;
        $this->carriagePaidThreshold = $carriagePaidThreshold;
        $this->carriagePaid = $carriagePaid;
        $this->originalPrice = $originalPrice;
        $this->isExternalPrice = $isCustomShippingPrice;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getPrice(): Money
    {
        return $this->price;
    }

    public function getTax(): Money
    {
        return $this->tax;
    }

    public function getDeliveryTime(): string
    {
        return $this->deliveryTime;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function getPosition(): int
    {
        return $this->position;
    }

    public function getCarriagePaidThreshold(): ?float
    {
        return $this->carriagePaidThreshold;
    }

    public function getCarriagePaid(): bool
    {
        return $this->carriagePaid;
    }

    public function getOriginalPrice(): float
    {
        return $this->originalPrice;
    }

    public function isExternalPrice(): bool
    {
        return $this->isExternalPrice;
    }

    public function getImagePath(): ?string
    {
        $image = fn_get_image_pairs($this->getId(), 'shipping', 'M', true, true);

        return $image['icon']['image_path'] ?? null;
    }

    public function expose(): array
    {
        // Dans le cas où dans toutes les taxes, les prix des produits n'inclus pas la taxe,
        // cela veut dire que la MP est configurée en HT.
        //
        // Le prix des frais de port n'est donc pas calculé correctement
        // car dans ce cas les frais de port sont en HT et non en TTC
        $priceWithoutVat = $priceWithTaxes = $this->getPrice();
        $tax = $this->getTax();
        if ($this->pricesIncludesTaxes()) {
            $priceWithoutVat = $priceWithTaxes->subtract($tax);
        } else {
            $priceWithTaxes  = $priceWithoutVat->add($tax);
        }

        return [
            'id' => $this->getId(),
            'name' => $this->getName(),
            'type' => $this->getType(),
            'price' => $this->getPrice()->getConvertedAmount(),
            'deliveryTime' => $this->getDeliveryTime(),
            'image' => $this->getImagePath(),
            'shippingPrice' => [
                'priceWithoutVat' => $priceWithoutVat->getConvertedAmount(),
                'priceWithTaxes' => $priceWithTaxes->getConvertedAmount(),
                'vat' => $this->getTax()->getConvertedAmount(),
            ],
            'carriagePaidThreshold' => $this->getCarriagePaidThreshold(),
            'carriagePaid' => $this->getCarriagePaid(),
            'originalPrice' => $this->getOriginalPrice(),
            'externalPrice' => $this->isExternalPrice(),
        ];
    }

    private function pricesIncludesTaxes(): bool
    {
        // On récupère toutes les taxes disponibles au sein de la MP
        foreach (fn_get_taxes() as $tax) {
            if ($tax['price_includes_tax'] === "Y") {
                return true;
            }
        }

        return false;
    }
}
