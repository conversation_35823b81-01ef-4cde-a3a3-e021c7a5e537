<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Basket\ReadModel;

use DateTimeInterface;
use Wizacha\Marketplace\Division\Division;
use Wizacha\Marketplace\Image\Image;
use Wizacha\Money\Money;

class BasketItem
{
    /**
     * @var int
     */
    private $quantity;

    /**
     * @var string
     */
    private $declinationId;

    /**
     * @var array
     */
    private $declinationOptions;

    /**
     * @var int
     */
    private $productId;

    /**
     * @var string
     */
    private $productName;

    /**
     * @var string
     */
    private $productCode;

    /**
     * @var Money
     */
    private $individualPrice;

    /**
     * @var Money
     */
    private $individualTax;

    /**
     * @var Money|null
     */
    private $crossedOutPrice;

    /**
     * @var Money|null
     */
    private $greenTax;

    /**
     * @var string
     */
    private $productUrl;

    /**
     * @var DateTimeInterface
     */
    private $createdAt;

    /**
     * @var DateTimeInterface
     */
    private $updatedAt;

    /**
     * @var Image|null
     */
    private $mainImage;

    /**
     * @var string
     */
    private $comment;

    /**
     * @var Division[]
     */
    private $divisions;


    public function __construct(array $data)
    {
        $this->quantity = (int) $data['quantity'];
        $this->comment = $data['comment'];

        $this->declinationId = $data['declination']['objectID'];
        $this->productId = $data['declination']['product_id'];
        $this->productName = $data['declination']['product_name'];
        $this->productCode = $data['declination']['product_code'];
        $this->individualPrice = Money::fromVariable($data['declination']['reduced_price']);
        $this->individualTax = Money::fromVariable($data['declination']['tax']);
        $this->greenTax = Money::fromVariable($data['declination']['green_tax']);
        $this->crossedOutPrice = $data['declination']['crossed_out_price'];
        $this->productUrl = $data['declination']['url'];
        $this->createdAt = $data['declination']['created_at'];
        $this->updatedAt = $data['declination']['updated_at'];
        $this->mainImage = $data['declination']['main_image'];
        $this->declinationOptions = $data['declination']['declination'];
        $this->divisions = $data['declination']['divisions'];
    }

    public function getDeclinationId(): string
    {
        return $this->declinationId;
    }

    public function getDeclinationOptions(): array
    {
        return $this->declinationOptions;
    }

    public function getQuantity(): int
    {
        return $this->quantity;
    }

    public function getTotalWithoutTax(): Money
    {
        return $this->getTotal()->subtract($this->getTax());
    }

    public function getTotal(): Money
    {
        return $this->getIndividualPrice()->multiply($this->getQuantity());
    }

    public function getTax(): Money
    {
        return $this->getIndividualTax()->multiply($this->getQuantity());
    }

    public function getProductId(): int
    {
        return $this->productId;
    }

    public function getProductName(): string
    {
        return $this->productName;
    }

    public function getProductCode(): string
    {
        return $this->productCode;
    }

    public function getIndividualPrice(): Money
    {
        return $this->individualPrice;
    }

    public function getIndividualTax(): Money
    {
        return $this->individualTax;
    }

    public function getIndividualPriceWithoutTaxes(): Money
    {
        return $this->getIndividualPrice()->subtract($this->getIndividualTax());
    }

    public function getCrossedOutPrice(): ?Money
    {
        return $this->crossedOutPrice;
    }

    public function getGreenTax(): ?Money
    {
        return $this->greenTax;
    }

    public function getProductUrl(): string
    {
        return $this->productUrl;
    }

    public function getMainImage(): ?Image
    {
        return $this->mainImage;
    }

    public function getComment(): string
    {
        return $this->comment;
    }

    /**
     * @return Division[]
     */
    public function getDivisions(): array
    {
        return $this->divisions ?? [];
    }

    public function expose(): array
    {
        return [
            'declinationId' => $this->getDeclinationId(),
            'productId' => $this->getProductId(),
            'productName' => $this->getProductName(),
            'productCode' => $this->getProductCode(),
            'productUrl' => $this->getProductUrl(),
            'individualPrice' => $this->getIndividualPrice()->getConvertedAmount(),
            'individualTax' => $this->getIndividualTax()->getConvertedAmount(),
            'crossedOutPrice' => $this->getCrossedOutPrice() ? $this->getCrossedOutPrice()->getConvertedAmount() : null,
            'greenTax' =>  $this->getGreenTax() ? $this->getGreenTax()->getConvertedAmount() : null,
            'mainImage' => $this->getMainImage() ? $this->getMainImage()->toArray() : null,
            'quantity' => $this->getQuantity(),
            'total' => $this->getTotal()->getConvertedAmount(),
            'options' => $this->exposeDeclinationOptions(),
            'comment' => $this->getComment(),
            'divisions' => $this->getDivisions(),
            'unitPrice' => [
                'priceWithoutVat' => $this->getIndividualPriceWithoutTaxes()->getConvertedAmount(),
                'priceWithTaxes' => $this->getIndividualPrice()->getConvertedAmount(),
                'vat' => $this->getIndividualTax()->getConvertedAmount(),
            ],
            'totalPrice' => [
                'priceWithoutVat' => $this->getTotal()->subtract($this->getTax())->getConvertedAmount(),
                'priceWithTaxes' => $this->getTotal()->getConvertedAmount(),
                'vat' => $this->getTax()->getConvertedAmount(),
            ],
        ];
    }

    private function exposeDeclinationOptions(): array
    {
        return array_map(function (array $legacyFormattedDeclinationOption) {
            return [
                'optionId' => $legacyFormattedDeclinationOption['option_id'],
                'optionName' => $legacyFormattedDeclinationOption['option_name'],
                'optionCode' => $legacyFormattedDeclinationOption['code'],
                'variantId' => $legacyFormattedDeclinationOption['value_id'],
                'variantName' => $legacyFormattedDeclinationOption['value_name'],
            ];
        }, $this->getDeclinationOptions());
    }
}
