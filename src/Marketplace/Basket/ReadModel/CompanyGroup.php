<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Basket\ReadModel;

use Wizacha\Marketplace\Price\PriceComposition;
use Wizacha\Marketplace\Price\PriceFields;
use Wizacha\Marketplace\Company\CompanyType;
use Wizacha\Marketplace\Company\Company;
use Wizacha\Money\Money;

use function iter\reduce;

class CompanyGroup
{
    /**
     * @var int
     */
    private $companyId;

    /**
     * @var string
     */
    private $companyName;

    /**
     * @var string
     */
    private $companySlug;

    /**
     * @var string
     */
    private $companyTerms;

    /**
     * @var boolean
     */
    private $isProfessional;

    /**
     * @var ShippingGroup[]
     */
    private $shippingGroups;

    /**
     * @var Money
     */
    private $productTotal;

    /**
     * @var Money
     */
    private $taxTotal;

    /**
     * @var Money
     */
    private $shippingTotal;

    /**
     * @var Money
     */
    private $shippingTaxTotal;

    public function __construct(array $data)
    {
        $this->companyId = (int) $data['companyId'];
        $this->companyName = $data['companyName'];
        $this->companySlug = $data['companySlug'];
        $this->companyTerms = $data['companyTerms'];
        $this->isProfessional = $data['companyType'] === (string) CompanyType::PROFESSIONAL();
        $this->shippingGroups = array_values(array_map(function (array $groupData) {
            return new ShippingGroup($groupData);
        }, $data['products']));

        /** @var PriceComposition $prices */
        $prices = $data['prices'];
        $this->productTotal = $prices->get(PriceFields::BASKET_TOTAL());
        $this->taxTotal = $prices->get(PriceFields::TAXES());
        $this->shippingTotal = $prices->get(PriceFields::SHIPPING_TOTAL());
        $this->shippingTaxTotal = $prices->get(PriceFields::SHIPPING_TAX());
    }

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    public function ofCompany(Company $company): bool
    {
        return $this->companyId === $company->getId();
    }

    public function getCompanyName(): string
    {
        return $this->companyName;
    }

    public function getCompanySlug(): string
    {
        return $this->companySlug;
    }

    public function getCompanyTerms(): string
    {
        return $this->companyTerms;
    }

    public function isProfessional(): bool
    {
        return $this->isProfessional;
    }

    /**
     * @return ShippingGroup[]
     */
    public function getShippingGroups(): array
    {
        return $this->shippingGroups;
    }

    public function getTotalQuantity(): int
    {
        return reduce(function (int $total, ShippingGroup $shippingGroup) {
            return $total + $shippingGroup->getTotalQuantity();
        }, $this->shippingGroups, 0);
    }

    /**
     * @return BasketItem[]|\Iterator
     */
    public function getAllItems(): \Iterator
    {
        foreach ($this->shippingGroups as $group) {
            yield from $group->getAllItems();
        }
    }

    /**
     * The total price of products (so, excluding shipping).
     */
    public function getProductTotal(): Money
    {
        return $this->productTotal;
    }

    /**
     * The total amount of taxes (shipping and products)
     */
    public function getTaxTotal(): Money
    {
        return $this->taxTotal;
    }

    /**
     * The total price of shipping.
     */
    public function getShippingTotal(): Money
    {
        return $this->shippingTotal;
    }

    /**
     * The total amount of taxes on the shipping only.
     */
    public function getShippingTaxTotal(): Money
    {
        return $this->shippingTaxTotal;
    }

    public function expose(): array
    {
        return [
            'company' => [
                'id' => $this->getCompanyId(),
                'name' => $this->getCompanyName(),
                'slug' => $this->getCompanySlug(),
            ],
            'productTotalWithTaxes' => $this->getProductTotal()->getConvertedAmount(),
            'productTaxes' => $this->getTaxTotal()->subtract($this->getShippingTaxTotal())->getConvertedAmount(),
            'shippingTotalWithTaxes' => $this->getShippingTotal()->getConvertedAmount(),
            'shippingTaxes' => $this->getShippingTaxTotal()->getConvertedAmount(),
            'taxTotal' => $this->getTaxTotal()->getConvertedAmount(),
            'shippingGroups' => array_values(array_map(function (ShippingGroup $shippingGroup) {
                return $shippingGroup->expose();
            }, $this->getShippingGroups())),
        ];
    }
}
