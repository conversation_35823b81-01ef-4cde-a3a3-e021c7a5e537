<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Basket\ReadModel;

use Wizacha\Marketplace\Basket\Exception\LogicException;
use Wizacha\Marketplace\Address\Address;
use Wizacha\Marketplace\Price\PriceFields;
use Wizacha\Marketplace\Company\Company;
use Wizacha\Marketplace\ReadModel\BasketPriceInterface;
use Wizacha\Money\Money;

use function iter\reduce;

/**
 * Basket read model.
 */
class Basket implements BasketPriceInterface
{
    /**
     * @var string
     */
    private $id;

    /**
     * @var string[]
     */
    private $coupons;

    /**
     * @var string
     */
    private $comment;

    /**
     * @var Address
     */
    private $billingAddress;

    /**
     * @var Address
     */
    private $shippingAddress;

    /**
     * Contient la réduction : si panier à 100€ avec un réduction de 10€, contient 90€
     * @var Money
     */
    private $subTotal;

    /**
     * @var Money
     */
    private $discountTotal;

    /**
     * @var Money
     */
    private $shippingTotal;

    /**
     * @var Money
     */
    private $shippingTax;

    /**
     * @var Money
     */
    private $taxTotal;

    /**
     * @var Money
     */
    private $total;

    /**
     * @var string[]
     */
    private $promotions;

    /**
     * @var bool
     */
    private $isEligibleToPickupPointsShipping;

    /**
     * @var bool
     */
    private $isPickupPointsShipping;

    /**
     * @var CompanyGroup[]
     */
    private $groups;

    /**
     * @var bool
     */
    private $hasAdjustableProducts;

    /**
     * @var Money
     */
    private $marketplaceDiscountTotal;

    public function __construct(array $data)
    {
        $this->id = $data['id'];
        $this->coupons = $data['coupons'];
        $this->comment = $data['comment'];
        $this->billingAddress = $data['billing_address'] ?? new Address();
        $this->shippingAddress = $data['shipping_address'] ?? new Address();
        $this->subTotal = $data['subtotal'];
        $this->discountTotal = $data['subtotal_discount'];
        $this->shippingTotal = Money::fromVariable($data['shipping_total']);
        $this->shippingTax = $data['prices']->get(PriceFields::SHIPPING_TAX());
        $this->taxTotal = Money::fromVariable($data['tax']);
        $this->total = Money::fromVariable($data['total']);
        $this->marketplaceDiscountTotal = Money::fromVariable($data['marketplace_discount_total']);
        $this->promotions = $data['promotions'] ?? [];
        $this->isEligibleToPickupPointsShipping = (bool) $data['is_eligible_to_pickup_points_shipping'] ?? false;
        $this->isPickupPointsShipping = (bool) $data['is_pickup_points_shipping'] ?? false;
        $this->hasAdjustableProducts = (bool) ($data['has_adjustable_products'] ?? false);

        $this->groups = array_values(array_map(function (array $groupData) {
            return new CompanyGroup($groupData);
        }, $data['groups']));
    }

    public function getId(): string
    {
        return $this->id;
    }

    /**
     * @return CompanyGroup[]
     */
    public function getGroups(): array
    {
        return $this->groups;
    }

    public function getGroup(Company $company): CompanyGroup
    {
        foreach ($this->groups as $group) {
            if ($group->ofCompany($company)) {
                return $group;
            }
        }

        throw new LogicException('Company group not found');
    }

    /**
     * @return string[]
     */
    public function getCoupons(): array
    {
        return $this->coupons;
    }

    public function getComment(): string
    {
        return $this->comment;
    }

    public function getBillingAddress(): Address
    {
        return $this->billingAddress;
    }

    public function getShippingAddress(): Address
    {
        return $this->shippingAddress;
    }

    /**
     * Returns the total amount excluding shipping.
     */
    public function getSubTotal(): Money
    {
        return $this->subTotal;
    }

    /**
     * Returns the total amount of promotional discount in the basket.
     */
    public function getDiscountTotal(): Money
    {
        return $this->discountTotal;
    }

    public function getShippingTotal(): Money
    {
        return $this->shippingTotal;
    }

    public function getTaxTotal(): Money
    {
        return $this->taxTotal;
    }

    public function getTotal(): Money
    {
        return $this->total;
    }

    public function getMarketplaceDiscountTotal(): Money
    {
        return $this->marketplaceDiscountTotal;
    }

    public function getTotalQuantity(): int
    {
        return reduce(function (int $total, CompanyGroup $group) {
            return $total + $group->getTotalQuantity();
        }, $this->groups, 0);
    }

    /**
     * @return BasketItem[]|\Iterator
     */
    public function getAllItems(): \Iterator
    {
        foreach ($this->groups as $group) {
            yield from $group->getAllItems();
        }
    }

    public function hasItem(string $declinationId): bool
    {
        foreach ($this->getAllItems() as $item) {
            if ($item->getDeclinationId() === $declinationId) {
                return true;
            }
        }

        return false;
    }

    public function getItem(string $declinationId): BasketItem
    {
        foreach ($this->getAllItems() as $item) {
            if ($item->getDeclinationId() === $declinationId) {
                return $item;
            }
        }

        throw new LogicException('Item not found, check the existence via self::hasItem before trying to access it');
    }

    /**
     * @return string[]
     */
    public function getPromotions(): array
    {
        return $this->promotions;
    }

    public function getShippingTax(): Money
    {
        return $this->shippingTax;
    }

    public function isEligibleToPickupPointsShipping(): bool
    {
        return $this->isEligibleToPickupPointsShipping;
    }

    public function isPickupPointsShipping(): bool
    {
        return $this->isPickupPointsShipping;
    }

    public function hasAdjustableProducts(): bool
    {
        return $this->hasAdjustableProducts;
    }

    public function getTotalItemsPrice(): array
    {
        $vat = $this->getTaxTotal()->subtract($this->getShippingTax());
        $priceWithoutVat = $this->getTotal()
            ->subtract($this->getTaxTotal())
            ->subtract($this->getShippingTotal()->subtract($this->getShippingTax()));

        if ($priceWithoutVat->isNegative()) {
            $priceWithoutVat = new Money(0);
            $vat = new Money(0);
        } elseif ($priceWithoutVat->isZero()) {
            $vat = new Money(0);
        }

        return [
            'priceWithoutVat' => $priceWithoutVat->getConvertedAmount(),
            'priceWithTaxes' => $this->getTotal()->subtract($this->getShippingTotal())->getConvertedAmount(),
            'vat' => $vat->getConvertedAmount(),
        ];
    }

    public function getTotalShippingsPrice(): array
    {
        $vat = $this->getShippingTax();
        $priceWithoutVat =  $this->getShippingTotal()->subtract($this->getShippingTax());

        if ($priceWithoutVat->isNegative()) {
            $priceWithoutVat = new Money(0);
            $vat = new Money(0);
        } elseif ($priceWithoutVat->isZero()) {
            $vat = new Money(0);
        }

        return [
            'priceWithoutVat' => $priceWithoutVat->getConvertedAmount(),
            'priceWithTaxes' => $this->getShippingTotal()->getConvertedAmount(),
            'vat' => $vat->getConvertedAmount(),
        ];
    }

    public function getTotalGlobalPrice(): array
    {
        $vat = $this->getTaxTotal();
        $priceWithoutVat = $this->getTotal()->subtract($this->getTaxTotal());

        if ($priceWithoutVat->isNegative()) {
            $priceWithoutVat = new Money(0);
            $vat = new Money(0);
        } elseif ($priceWithoutVat->isZero()) {
            $vat = new Money(0);
        }

        return [
            'priceWithoutVat' => $priceWithoutVat->getConvertedAmount(),
            'priceWithTaxes' => $this->getTotal()->getConvertedAmount(),
            'vat' => $vat->getConvertedAmount(),
        ];
    }

    public function hasEmptyShippingAddress(): bool
    {
        $shippingAddress = $this->getShippingAddress();
        if (\mb_strlen($shippingAddress->getTitle()) === 0
            && \mb_strlen($shippingAddress->getFirstName()) === 0
            && \mb_strlen($shippingAddress->getLastName()) === 0
        ) {
            return true;
        }

        return false;
    }

    public function expose(): array
    {
        $taxTotal = $this->getTaxTotal()->isNegative() || $this->getTotal()->isZero()
            ?  new Money(0)
            : $this->getTaxTotal();

        return [
            'id' => $this->getId(),
            'comment' => $this->getComment(),
            'coupons' => array_values($this->getCoupons()),
            'billingAddress' => $this->getBillingAddress()->expose(),
            'shippingAddress' => $this->getShippingAddress()->expose(),
            'companyGroups' => array_values(array_map(function (CompanyGroup $companyGroup) {
                return $companyGroup->expose();
            }, $this->getGroups())),
            'subtotal' => $this->getSubTotal()->getConvertedAmount(),
            'totalDiscount' => $this->getDiscountTotal()->getConvertedAmount(),
            'totalShipping' => $this->getShippingTotal()->getConvertedAmount(),
            'totalShippingTax' => $this->getShippingTax()->getConvertedAmount(),
            'totalTax' => $taxTotal->getConvertedAmount(),
            'total' => $this->getTotal()->getConvertedAmount(),
            'totalMarketplaceDiscount' => $this->getMarketplaceDiscountTotal()->getConvertedAmount(),
            'totalQuantity' => $this->getTotalQuantity(),
            'totalItemsPrice' => $this->getTotalItemsPrice(),
            'totalShippingsPrice' => $this->getTotalShippingsPrice(),
            'totalGlobalPrice' => $this->getTotalGlobalPrice(),
            'isEligibleToPickupPointsShipping' => $this->isEligibleToPickupPointsShipping(),
            'isPickupPointsShipping' => $this->isPickupPointsShipping(),
        ];
    }
}
