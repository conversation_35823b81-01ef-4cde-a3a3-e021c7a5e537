<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Basket;

use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Payment\Response\PaymentResponse;

class CheckoutResult
{
    /**
     * @var Order[]
     */
    private $orders;

    /**
     * @var ?PaymentResponse
     */
    private $paymentResponse;

    public function __construct(array $orders, ?PaymentResponse $paymentResponse)
    {
        $this->orders = $orders;
        $this->paymentResponse = $paymentResponse;
    }

    public function getParentOrderId(): int
    {
        if (\count($this->orders) > 1) {
            // dans le cas où un panier a entrainé la création de plusieurs commandes, l'identifiant
            // de la comamnde parente correspond à l'identifiant de la première commande - 1
            return $this->orders[0]->getId() - 1;
        }

        return $this->orders[0]->getId();
    }

    /**
     * @return Order[]
     */
    public function getOrders(): array
    {
        return $this->orders;
    }

    public function getPaymentResponse(): ?PaymentResponse
    {
        return $this->paymentResponse;
    }
}
