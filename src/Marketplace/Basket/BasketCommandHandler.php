<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Basket;

use Broadway\CommandHandling\CommandHandler;
use Broadway\EventSourcing\EventSourcingRepository;
use Wizacha\Core\Concurrent\BlockingMutex;
use Wizacha\Core\Concurrent\MutexService;
use Wizacha\Marketplace\Basket\Command as Commands;
use Wizacha\Marketplace\Basket\Command\BasketCommand;

class BasketCommandHandler extends CommandHandler
{
    private EventSourcingRepository $repository;
    private MutexService $mutexService;

    public function __construct(
        EventSourcingRepository $repository,
        MutexService $mutexService
    ) {
        $this->repository = $repository;
        $this->mutexService = $mutexService;
    }

    public function handle($command): void
    {
        $mutex = $this->getBasketMutex($command);

        parent::handle($command);
    }

    public function handlePickUpBasket(Commands\PickUpBasket $command): void
    {
        $basket = Basket::pickUpBasket($command->getBasketId());

        $this->repository->save($basket);
    }

    public function handleSetBillingAddress(Commands\SetBillingAddress $command): void
    {
        /** @var Basket $basket */
        $basket = $this->repository->load($command->getBasketId());
        $basket->setBillingAddress($command->getAddress());

        $this->repository->save($basket);
    }

    public function handleSetShippingAddress(Commands\SetShippingAddress $command): void
    {
        /** @var Basket $basket */
        $basket = $this->repository->load($command->getBasketId());
        $basket->setShippingAddress($command->getAddress());

        $this->repository->save($basket);
    }

    public function handleSelectShippingForGroup(Commands\SelectShippingForGroup $command): void
    {
        /** @var Basket $basket */
        $basket = $this->repository->load($command->getBasketId());
        $basket->selectShippingForGroup($command->getGroupId(), $command->getShippingId());

        $this->repository->save($basket);
    }

    public function handleUpdateCustomShippingPrice(Commands\UpdateCustomShippingPrice $command): void
    {
        /** @var Basket $basket */
        $basket = $this->repository->load($command->getBasketId());
        $basket->updateCustomShippingPrice($command->getGroupId(), $command->getShippingId(), $command->getPrice());

        $this->repository->save($basket);
    }

    public function handleModifyProductQuantityInBasket(Commands\ModifyProductQuantityInBasket $command): void
    {
        /** @var Basket $basket */
        $basket = $this->repository->load($command->getBasketId());
        $product = $command->getProduct();
        $quantity = $command->getQuantity();
        $basket->modifyProductQuantity($product, $quantity);

        $this->repository->save($basket);
    }

    public function handleRemoveProductFromBasket(Commands\RemoveProductFromBasket $command): void
    {
        /** @var Basket $basket */
        $basket = $this->repository->load($command->getBasketId());
        $basket->modifyProductQuantity($command->getProduct(), 0);

        $this->repository->save($basket);
    }

    public function handleModifyProductCommentInBasket(Commands\ModifyProductCommentInBasket $command): void
    {
        /** @var Basket $basket */
        $basket = $this->repository->load($command->getBasketId());
        $product = $command->getProduct();
        $comment = $command->getComment();
        $basket->modifyProductComment($product, $comment);

        $this->repository->save($basket);
    }

    /**
     * Protect from concurrent Broadway event (uuid, playhead) writing
     */
    private function getBasketMutex(BasketCommand $command): BlockingMutex
    {
        return $this->mutexService->createBlockingMutex(
            Basket::class,
            $command->getBasketId()
        );
    }
}
