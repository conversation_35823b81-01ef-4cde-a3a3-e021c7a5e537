<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Basket;

use Broadway\ReadModel\Projector;
use Broadway\ReadModel\RepositoryInterface;
use Psr\Log\LoggerInterface;
use Wizacha\Marketplace\Catalog\Declination\Declination as CatalogDeclination;
use Wizacha\Marketplace\Catalog\Product\ProductService;
use Wizacha\Marketplace\Entities\Declination;
use Wizacha\Marketplace\Entities\Tax;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Price\PriceFields;
use Wizacha\Marketplace\Promotion\PromotionService;
use Wizacha\Marketplace\ReadModel\Basket;
use Wizacha\Marketplace\Subscription\Exception\SubscriptionException;
use Wizacha\Marketplace\Subscription\OrderItem;
use Wizacha\Marketplace\Subscription\SubscriptionProduct;
use Wizacha\Marketplace\Subscription\SubscriptionReadModelProduct;
use Wizacha\Marketplace\Subscription\SubscriptionRepository;
use Wizacha\Money\Money;
use Wizacha\Registry;

/**
 * The basket was implemented using CQRS.
 *
 * This class generates the "read model" of the basket.
 */
class BasketProjector extends Projector
{
    /**
     * @var RepositoryInterface
     */
    private $repository;

    /**
     * @var LoggerInterface
     */
    private $logger;

    public function __construct(RepositoryInterface $repository, LoggerInterface $logger)
    {
        $this->repository = $repository;
        $this->logger = $logger;
    }

    public function applyBasketWasPickedUp(Event\BasketWasPickedUp $event)
    {
        $readModel = new Basket($event->getBasketId());
        $this->repository->save($readModel);
    }

    public function applyCouponWasApplied(Event\CouponWasApplied $event)
    {
        $readModel = $this->getBasket($event->getBasketId());
        if (!$readModel) {
            return;
        }
        $readModel->addCoupon($event->getCoupon());
        $this->repository->save($readModel);
    }

    public function applyCouponWasRemoved(Event\CouponWasRemoved $event)
    {
        $readModel = $this->getBasket($event->getBasketId());
        if (!$readModel) {
            return;
        }
        $readModel->removeCoupon($event->getCoupon());
        $this->repository->save($readModel);
    }

    public function applyBillingAddressWasAdded(Event\BillingAddressWasAdded $event)
    {
        $readModel = $this->getBasket($event->getBasketId());
        if (!$readModel) {
            return;
        }
        $readModel->setBillingAddress($event->getAddress());
        $this->repository->save($readModel);
    }

    public function applyShippingAddressWasAdded(Event\ShippingAddressWasAdded $event)
    {
        $readModel = $this->getBasket($event->getBasketId());
        if (!$readModel) {
            return;
        }
        $readModel->setShippingAddress($event->getAddress());
        $this->repository->save($readModel);
    }

    public function applyProductQuantityWasModifiedInBasket(Event\ProductQuantityWasModifiedInBasket $event)
    {
        $readModel = $this->getBasket($event->getBasketId());
        if (!$readModel) {
            return;
        }

        $readModel->modifyProductQuantity(
            $event->getProductId(),
            $event->getQuantity(),
            $event->getProductClass(),
            $event->getSubscriptionId()
        );
        $this->repository->save($readModel);
    }

    public function applyProductCommentWasModifiedInBasket(Event\ProductCommentWasModifiedInBasket $event)
    {
        $readModel = $this->getBasket($event->getBasketId());
        if (!$readModel) {
            return;
        }
        $readModel->modifyProductComment($event->getProductId(), $event->getComment());
        $this->repository->save($readModel);
    }

    public function applyCommentWasApplied(Event\CommentWasApplied $event)
    {
        $readModel = $this->getBasket($event->getBasketId());
        if (!$readModel) {
            return;
        }
        $readModel->setComment($event->getComment());
        $this->repository->save($readModel);
    }

    public function applyProductsWasGrouped(Event\ProductsWasGrouped $event)
    {
        $readModel = $this->getBasket($event->getBasketId());
        if (!$readModel) {
            return;
        }
        $readModel->setGroups($event->getGroups());
        if ($event->hasRemoveSelectedShippings() === true) {
            $readModel->removeSelectedShippings();
        }
        $this->repository->save($readModel);
    }

    public function applyShippingForGroupWasSelected(Event\ShippingForGroupWasSelected $event)
    {
        $readModel = $this->getBasket($event->getBasketId());
        if (!$readModel) {
            return;
        }
        $readModel->setSelectedGroupShipping($event->getGroupId(), $event->getShippingId());
        $this->repository->save($readModel);
    }

    public function applyShippingPriceWasUpdated(Event\ShippingPriceWasUpdated $event): void
    {
        $readModel = $this->getBasket($event->getBasketId());
        if (!$readModel) {
            return;
        }

        $readModel->setCustomShippingPrice($event->getGroupId(), $event->getShippingId(), $event->getPrice());
        $this->repository->save($readModel);
    }

    public function applyOrderIdWasGeneratedOnBasket(Event\OrderIdWasGeneratedOnBasket $event)
    {
        $readModel = $this->getBasket($event->getBasketId());
        if (!$readModel) {
            return;
        }
        $readModel->setOrderId($event->getOrderId());
        $this->repository->save($readModel);
    }

    /**
     * $promotion is a array of Entity promotion
     *
     * @param string                      $declinationId
     * @param PromotionService            $promotionService
     * @param int                         $quantity
     * @param string|null                 $productClassName
     * @param string|null                 $subscriptionId
     * @param SubscriptionRepository|null $subscriptionRepository
     *
     * @throws SubscriptionException
     * @return array|bool
     */
    public static function getDeclinationData(
        $declinationId,
        PromotionService $promotionService = null,
        int $quantity = null,
        string $productClassName = null,
        string $subscriptionId = null,
        SubscriptionRepository $subscriptionRepository = null
    ) {
        if (!$promotionService) {
            $promotionService = Registry::defaultInstance()->container->get('marketplace.promotion.promotionservice');
        }
        if (SubscriptionProduct::class === $productClassName
            && $subscriptionRepository instanceof SubscriptionRepository
        ) {
            $productId = (int) explode("_", $declinationId)[0];
            $orderItem = null;
            $subscription = $subscriptionRepository->findOneById($subscriptionId);

            foreach ($subscription->getOrderItems() as $item) {
                if ($item->getProductId() === $productId) {
                    $orderItem = $item;
                    break;
                }
            }

            if (false === $orderItem instanceof OrderItem) {
                throw new SubscriptionException("Unable to find orderItem for product ID '" . $productId . "'.");
            }

            $declination = new Declination(
                new SubscriptionProduct($orderItem, $subscription)
            );
        } else {
            $declination = Declination::fromId($declinationId);
        }
        if (!$declination->isActive()) {
            return false;
        }

        if ($declination->getProduct() instanceof SubscriptionProduct) {
            $product = new SubscriptionReadModelProduct();

            $reducedPrice = $declination->getProduct()->getOriginalPrice()->getPreciseConvertedAmount();
            $crossedOutPrice = null;
        } else {
            /** @var ProductService $productService */
            $productService = Registry::defaultInstance()->container->get('marketplace.product.productservice');
            $product = $productService->getProduct($declination->getProductId());

            if ($product->getMultiVendorProductId() !== null) {
                $product = $productService->getProduct($product->getMultiVendorProductId());
            }

            $reducedPrice = $promotionService->getProductFinalPrice($declination, $quantity)->get(PriceFields::BASE_PRICE())->getPreciseConvertedAmount();
            $crossedOutPrice = $declination->getCrossedOutPrice() ?? (
                $declination->getOriginalPrice($quantity) != $reducedPrice ? Money::fromVariable($declination->getOriginalPrice($quantity)) : null
            );
        }

        $divisions = Registry::defaultInstance()->container->get('marketplace.division.products.service')->getAllDivisionsCode(
            $declination->getProductId()
        );

        try {
            $productDeclination = $product->getDeclination($declination->getId());
        } catch (NotFound $exception) {
            $productDeclination = null;
        } catch (\Throwable $exception) {
            Registry::defaultInstance()->container->get('logger')->error("Error Product::getDeclination", [
                'exception' => $exception,
            ]);

            $productDeclination = null;
        }

        $data = [
            'objectID'              => $declination->getId(),
            'product_id'            => $declination->getProductId(),
            'image_id'              => null,
            'main_image'            => ($productDeclination instanceof CatalogDeclination) ? $productDeclination->getMainImage() : null,
            'company_name'          => $declination->getCompany()->getName(),
            'product_name'          => $declination->getName(),
            'product_code'          => $declination->getProductCode(),
            'product_description'   => $declination->getShortDescription(),
            'price'                 => $declination->getOriginalPrice($quantity),
            'crossed_out_price'     => $crossedOutPrice,
            'reduced_price'         => $reducedPrice,
            'green_tax'             => $declination->getGreenTax()->getPreciseConvertedAmount(),
            'amount'                => $declination->getAmount(),
            'availability_date'     => $declination->getAvailabilityDate(),
            'declination'           => $declination->getDeclinationDefinition(),
            'shippings'             => $declination->getProductActiveShippingsRates(),
            'url'                   => $declination->getUrl(),
            'created_at'            => $product->getCreatedAt(),
            'updated_at'            => $product->getUpdatedAt(),
            'divisions'             => $divisions,
            'hasMaxPriceAdjustment' => $declination->hasMaxPriceAdjustment(),
        ];

        $isTransnational = false;
        if (null !== $declination->getCompany()->getId()) {
            $userServiceLegacy = container()->get('marketplace.user_service');
            $userService = container()->get('marketplace.user.user_service');
            $internationalTaxService = container()->get('marketplace.international_tax.shipping');

            $userId = $userServiceLegacy->getCurrentUserId();
            $user = null;
            if (null !== $userId) {
                $user = $userService->get($userId);
            }

            $isTransnational = $internationalTaxService->isTransnational(
                $user,
                $declination->getCompany()->getVatNumber(),
                $declination->getCompany()->getCountry(),
                $declination->isEdp()
            );
        }

        if (false === $isTransnational) {
            $taxinfo = Tax::applyTaxes(Money::fromVariable($data['reduced_price']), $declination->getTaxes());
            $data['tax'] = $taxinfo['tax']->getPreciseConvertedAmount();
            $data['reduced_price'] = $taxinfo['allTaxesPrice']->getPreciseConvertedAmount();
        } else {
            $data['tax'] = 0;
        }

        $images = $declination->getImages();
        if (\count($images)) {
            $data['image_id'] = $images[0]->getId();
        }

        return $data;
    }

    private function getBasket(string $basketId): ?Basket
    {
        $basket = $this->repository->find($basketId);

        if ($basket === null) {
            $this->logger->warning('Basket readmodel not found (id: {basket}) in BasketProjector', [
                'basket' => $basketId,
                'exception' => new \Exception(), // to log the stack trace
            ]);
        }

        return $basket;
    }
}
