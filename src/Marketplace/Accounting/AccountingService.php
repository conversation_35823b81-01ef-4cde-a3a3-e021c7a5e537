<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Accounting;

use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\PIM\Tax\Enum\TaxRateType;
use Wizacha\Tax;

class AccountingService
{
    /** @var OrderService */
    private OrderService $orderService;

    public function __construct(OrderService $orderService)
    {
        $this->orderService = $orderService;
    }

    /**
     * Return the taxes amount applied to totals and shipping
     *
     * @param array $data Data returned by getPayments or getVendorPayments functions in Wizacha\Marketplace\Accounting\Payment
     * @param bool $isAdmin
     * @return array
     */
    public function getOrderTaxes(array $data, bool $isAdmin): array
    {
        // $isAdmin is used to define a key that can be different if $data was provide by getPayments or getVendorPayments
        $key = $isAdmin ? 'company_id' : 'period_id';

        $tax = Tax::getFullRateValue();
        $totalsTaxes = [];
        $shippingTaxes = [];
        $orders = [];
        foreach ($data as $companies) {
            foreach ($companies as $payments) {
                $shippingTaxes[$payments[$key]][$payments['payment_id']]['shippingTaxe'] = $data[$payments[$key]][$payments['payment_id']]['shipping_cost'] / (1 + $tax);
                foreach (explode(';', $payments['order_list']) as $orderId) {
                    $orders[$payments[$key]][$payments['payment_id']][$orderId] = $orderId;
                    foreach ($this->orderService->overrideLegacyOrder((int) $orderId)['taxes'] as $orderTax) {
                        if ($orderTax['rate_type'] === TaxRateType::PERCENT()->getValue()) {
                            $totalsTaxes[$payments[$key]][$payments['payment_id']]['totalsTaxe'] += $orderTax['tax_subtotal'];
                        }
                    }
                }
            }
        }

        return [$totalsTaxes, $shippingTaxes, $orders];
    }
}
