<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Accounting;

use Doctrine\DBAL\Connection;
use Wizacha\Marketplace\Commission\CommissionService;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\Refund\Service\RefundService;
use Wizacha\Marketplace\Payment\PaymentService;
use Wizacha\OrderStatus;

class AccountingPayment implements AccountingPaymentInterface
{
    /** @var Connection  */
    protected $dbConnection;

    /** @var CommissionService  */
    protected $commissionService;

    /** @var OrderService  */
    protected $orderService;

    /** @var RefundService */
    protected $refundService;

    /** @var PaymentService */
    private $paymentService;

    public function __construct(
        Connection $dbConnection,
        CommissionService $commissionService,
        OrderService $orderService,
        RefundService $refundService,
        PaymentService $paymentService
    ) {
        $this->dbConnection = $dbConnection;
        $this->commissionService = $commissionService;
        $this->orderService = $orderService;
        $this->refundService = $refundService;
        $this->paymentService = $paymentService;
    }

    /** @return array[] */
    public function getPayments(int $begin, int $end): array
    {
        $orderIds = $this->getAdminOrderIds($begin, $end);

        $accountingInformationAggregation = new AccountingInformationsAggregation(
            $this->generateAccountingInformations($orderIds)
        );

        return $accountingInformationAggregation->buildArray();
    }

    /** @return array[] */
    public function getVendorPayments(int $begin, int $end, int $companyId): array
    {
        $queryResults = $this->getVendorOrderIds($begin, $end, $companyId);
        $orderIds = [];

        foreach ($queryResults as $result) {
            $orderIds[] = ['order_id' => $result['order_id']];
        }
        $periodId = $queryResults[0]['period_id'] ?? '';

        $accountingInformationAggregation = new AccountingInformationsAggregation(
            $this->generateAccountingInformations($orderIds),
            true,
            $periodId
        );

        $finalResult = $accountingInformationAggregation->buildArray();

        foreach ($finalResult as &$period) {
            foreach ($period as &$paymentMethod) {
                $paymentMethod['period_id'] = $periodId;
            }
        }

        return $finalResult;
    }

    /** @return AccountingInformation[] */
    protected function generateAccountingInformations(array $orderIds): array
    {
        $accountingInformations = [];

        foreach ($orderIds as $orderId) {
            $existingRefund = $this->refundService->getByOrderId($orderId['order_id']);
            $order = $this->orderService->getOrder($orderId['order_id']);
            $accountingInformations[$orderId['order_id']] = new AccountingInformation(
                $this->commissionService->getTotalCommission($order),
                $order,
                $existingRefund,
                $this->paymentService
            );
        }

        return $accountingInformations;
    }

    /** @return array[] */
    protected function getAdminOrderIds(int $begin, int $end): array
    {
        $query = "SELECT order_id FROM cscart_orders WHERE UNIX_TIMESTAMP(w_last_status_change) >= :start AND UNIX_TIMESTAMP(w_last_status_change) <= :end AND status = :status";
        $statement = $this->dbConnection->executeQuery(
            $query,
            [
                ':start' => $begin,
                ':end' => $end,
                ':status' => OrderStatus::COMPLETED,
            ],
            [
                \PDO::PARAM_INT,
                \PDO::PARAM_INT,
                \PDO::PARAM_STR,
            ]
        );

        return $statement->fetchAll();
    }

    /** @return array[] */
    protected function getVendorOrderIds(int $begin, int $end, int $companyId): array
    {
        $query = "SELECT CONCAT(DATE_FORMAT(w_last_status_change,\"%Y-%m-\"), DATE_FORMAT(w_last_status_change,\"%e\")>=16) as period_id, order_id FROM cscart_orders WHERE UNIX_TIMESTAMP(w_last_status_change) >= :start AND UNIX_TIMESTAMP(w_last_status_change) <= :end AND status = :status AND company_id = :companyId";
        $statement = $this->dbConnection->executeQuery(
            $query,
            [
                ':start' => $begin,
                ':end' => $end,
                ':status' => OrderStatus::COMPLETED,
                ':companyId' => $companyId,
            ],
            [
                \PDO::PARAM_INT,
                \PDO::PARAM_INT,
                \PDO::PARAM_STR,
                \PDO::PARAM_INT,
            ]
        );

        return $statement->fetchAll();
    }
}
