<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Accounting;

use Wizacha\Component\Order\TaxRate;
use Wizacha\Marketplace\Commission\CommissionService;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\Refund\Entity\Refund;
use Wizacha\Marketplace\Payment\PaymentService;
use Wizacha\Marketplace\Payment\PaymentType;
use Wizacha\Money\Money;
use Wizacha\Tax;

class AccountingInformation
{
    /** @var Order */
    protected $order;

    /** @var Money */
    protected $commission;

    /** @var Money */
    protected $vat;

    /** @var Refund[] */
    protected $existingRefund;

    /** @var PaymentService */
    private $paymentService;

    public function __construct(Money $commission, Order $order, array $existingRefund, PaymentService $paymentService)
    {
        $this
            ->setOrder($order)
            ->setCommission($commission)
            ->setVat($this->calculateVat())
        ;
        $this->existingRefund = $existingRefund;
        $this->paymentService = $paymentService;
    }

    /** @return mixed[] */
    public function expose(): array
    {
        return [
            'company_id' => $this->getOrder()->getCompanyId(),
            'payment_id' => $this->getOrder()->getPayment()->getId(),
            'donated' => $this->getOrder()->getBalanceTotal()->getConvertedAmount(),
            'w_commission' => $this->getCommission()->getConvertedAmount(),
            'vat' => $this->getVat()->getConvertedAmount(),
            'shipping_cost' => $this->calculateShippingCost()->getConvertedAmount(),
            'order_list' => (string) $this->getOrder()->getId(),
            'payment_type' => $this->getOrder()->getPayment()->getType()->getValue(),
            'payment_name' => $this->paymentService->getPaymentName($this->getOrder()->getPayment()->getId()),
        ];
    }

    public function getOrder(): Order
    {
        return $this->order;
    }

    public function setOrder(Order $order): self
    {
        $this->order = $order;

        return $this;
    }

    public function getCommission(): Money
    {
        return $this->commission;
    }

    public function setCommission(Money $commission): self
    {
        $this->commission = $commission;

        return $this;
    }

    public function getVat(): Money
    {
        return $this->vat;
    }

    public function setVat(Money $vat): self
    {
        $this->vat = $vat;

        return $this;
    }

    public function getCompanyId(): int
    {
        return $this->order->getCompanyId();
    }

    public function getPaymentId(): int
    {
        return $this->order->getPayment()->getId();
    }

    protected function calculateVat(): Money
    {
        return (new TaxRate(Tax::getFullRateValue() * 100))->getTaxAmountFromIncludingTaxes($this->getCommission());
    }

    protected function calculateShippingCost(): Money
    {
        $shippingCost = new Money(0);
        $shippingCost = $shippingCost->add($this->getOrder()->getShippingCost());

        foreach ($this->existingRefund as $refund) {
            if ($refund->isRefundedAfterWithdrawalPeriod() === true) {
                continue;
            }

            $shippingCost = $shippingCost->subtract($refund->getShippingAmount());
        }

        return $shippingCost;
    }
}
