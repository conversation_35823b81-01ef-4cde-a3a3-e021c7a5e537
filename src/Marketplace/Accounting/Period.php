<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Accounting;

use Tygh\Database;
use Wizacha\OrderStatus;

class Period
{
    public const NEXT_PERIOD_DAY = 16;

    /** @return int timestamp */
    public static function timestampFirstOrderCompleted(): int
    {
        return
            (int) (
                Database::getField(
                    "SELECT
                    UNIX_TIMESTAMP(w_last_status_change)
                FROM
                    ?:orders
                WHERE
                    status = ?s
                ORDER BY
                    w_last_status_change
                LIMIT 1;
                ",
                    OrderStatus::COMPLETED
                )
                ?? (new \DateTime())->getTimestamp()
            )
        ;
    }

    /**
     * @param integer $refDate (timestamp)
     * @param int $offset
     * @return array ['start' => int, 'end' => int]
     */
    public static function getPeriod(int $refDate, int $offset = 1)
    {
        [$day, $month, $year] = static::parseDate($refDate);

        $bDay = 1;
        $eDay = self::NEXT_PERIOD_DAY;
        //Revert the day if we are in second part of month
        if ($day >= self::NEXT_PERIOD_DAY) {
            list($bDay, $eDay) = [$eDay, $bDay];
        }
        if ($offset % 2) {
            //In second part of month, remove one suplementary month.
            $delta = ($bDay - $eDay < 0) ? -1 : 1;
            $month -= ($offset - $delta) / 2;
            //The offset is impair so switch the period.
            list($bDay, $eDay) = [$eDay, $bDay];
        } else {
            $month -= $offset / 2;
        }

        $return['start'] = mktime(0, 0, 0, $month, $bDay, $year);
        //If the end of period is the first, she is in the next month.
        if ($eDay === 1) {
            $eMonth = $month + 1;
        } else {
            $eMonth = $month;
        }
        $return['end'] = mktime(0, 0, 0, $eMonth, $eDay, $year);

        return $return;
    }

    /**
     * If $end > $start return 0;
     */
    public static function getNumberOfPeriod(int $end, int $start = null): int
    {
        if (\is_null($start)) {
            $start = self::timestampFirstOrderCompleted();
        }

        if ($start > $end) {
            return 0;
        }

        $period1 = self::getPeriod($start, 0);
        $period2 = self::getPeriod($end, 0);
        if ($period1['start'] == $period2['start']) {
            return 1;
        }

        [$day1, $month1, $year1] = static::parseDate($period1['start']);
        [$day2, $month2, $year2] = static::parseDate($period2['start']);

        $dMonth = $month2 + 12 * ($year2 - $year1) - $month1;
        $interval = ($dMonth) * 2;

        //Same month but different day so 2 periods
        if ($interval == 0) {
            return 2;
        }
        if ($day1 == $day2) {
            ++$interval;
        }

        return $interval;
    }

    /**
     * @return array [join_string, group_by_string]
     */
    public static function getSQLQueryForPeriods()
    {
        return [
            'CONCAT(DATE_FORMAT(w_last_status_change,"%Y-%m-"), DATE_FORMAT(w_last_status_change,"%e")>=' . self::NEXT_PERIOD_DAY . ') as period_id',
            'period_id',
        ];
    }

    /**
     * @param string $periodId
     * @return array|false [start => timestamp, end => timestamp]
     */
    public static function getPeriodByID(string $periodId)
    {
        $date = explode('-', $periodId);
        if (\count($date) != 3) {
            return false;
        }

        $timestamp = mktime(0, 0, 0, $date[1], ((int) $date[2] - 1) + self::NEXT_PERIOD_DAY, $date[0]);
        if ($timestamp) {
            return self::getPeriod($timestamp, 0);
        }

        return false;
    }

    /** @return int[] */
    public static function parseDate(int $timestamp): array
    {
        return \array_map(
            'intval',
            explode('-', date('j-n-Y', $timestamp))
        );
    }
}
