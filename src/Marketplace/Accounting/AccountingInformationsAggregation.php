<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Accounting;

class AccountingInformationsAggregation
{
    /** @var AccountingInformation[]  */
    protected $accountingInformations;

    /** @var bool */
    protected $vendor;

    /** @var string */
    protected $periodId;

    public function __construct(array $accountingInformations, bool $vendor = false, string $periodId = null)
    {
        $this->accountingInformations = $accountingInformations;
        $this->vendor = $vendor;
        $this->periodId = $periodId;
    }

    /** @return mixed[] */
    public function buildArray(): array
    {
        $companyIds = $this->getCompanyIds();

        $informationsByCompany = $this->initializeArray($companyIds);

        return $this->aggregateOrderByPaymentMethod($informationsByCompany);
    }

    /**
     * @param int[] $companyIds
     *
     * @return array[]
     */
    public function initializeArray(array $companyIds): array
    {
        $groupedInformations = [];

        foreach ($companyIds as $companyId) {
            if (false === $this->vendor) {
                $groupedInformations[$companyId] = [];
            }

            foreach ($this->accountingInformations as $accountingInformation) {
                if (true === $this->vendor && $accountingInformation->getCompanyId() === $companyId) {
                    $groupedInformations[$this->periodId][$accountingInformation->getPaymentId()][] = $accountingInformation->expose();
                } elseif ($accountingInformation->getCompanyId() === $companyId) {
                    $groupedInformations[$companyId][$accountingInformation->getPaymentId()][] = $accountingInformation->expose();
                }
            }
        }

        return $groupedInformations;
    }

    /**
     * @param mixed[] $groupedInformations
     *
     * @return mixed[]
     */
    public function aggregateOrderByPaymentMethod(array $groupedInformations): array
    {

        foreach ($groupedInformations as &$group) {
            foreach ($group as $id => &$payment) {
                $countPayment = \count($payment);
                if ($countPayment > 1) {
                    $aggregation = $payment[0];
                    for ($i = 1; $i < $countPayment; $i++) {
                        $aggregation['donated'] += $payment[$i]['donated'];
                        $aggregation['w_commission'] += $payment[$i]['w_commission'];
                        $aggregation['vat'] += $payment[$i]['vat'];
                        $aggregation['shipping_cost'] += $payment[$i]['shipping_cost'];
                        $aggregation['order_list'] .= ';' . $payment[$i]['order_list'];
                    }
                    $payment = $aggregation;
                } elseif ($countPayment === 0) {
                    unset($group[$id]);
                } else {
                    $payment = reset($payment);
                }
            }
        }

        return $groupedInformations;
    }

    /** @return int[] */
    public function getCompanyIds(): array
    {
        return \array_unique(\array_map(function (AccountingInformation $accountingInformation): int {
            return $accountingInformation->getCompanyId();
        }, $this->accountingInformations));
    }
}
