<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Theme;

use Wizacha\AppBundle\Service\ThemeCustomizer;
use Wizacha\Registry;

class DevAssetManager implements AssetManager
{
    private $localAssetManager;
    private $cache;

    public function __construct(string $kernelProjectDir, Registry $registry, string $baseUrl)
    {
        $this->localAssetManager = new LocalAssetManager($kernelProjectDir, uniqid(), $baseUrl);
        $this->cache = $registry->cache();
    }

    public function getAssetUrl(string $file): string
    {
        $url = $this->localAssetManager->getAssetUrl($file);
        $this->cache->regenerateHandlerId(ThemeCustomizer::THEME_CUSTOMIZER_HANDLER);

        return $url;
    }

    public function writeAsset(string $file, string $content, string $mimeType): void
    {
        $this->localAssetManager->writeAsset($file, $content, $mimeType);
    }
}
