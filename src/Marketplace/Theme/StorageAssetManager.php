<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Theme;

use Tygh\Backend\Cache\ABackend as CacheBackend;
use GuzzleHttp\Psr7\Uri;
use Wizacha\AppBundle\Service\ThemeCustomizer;
use Wizacha\Storage\StorageService;

class StorageAssetManager implements AssetManager
{
    private ?StorageService $storage;

    /**
     * @var CacheBackend
     */
    private $cache;

    /**
     * @var string
     */
    private $version;

    public function __construct(StorageService $storage, CacheBackend $cache, string $version)
    {
        $this->storage = $storage;
        $this->cache = $cache;
        $this->version = $version;
    }

    public function getAssetUrl(string $file): string
    {
        $url = new Uri($this->storage->getUrl($this->injectVersionInPath($file)));
        $themeVersion = $this->cache->getHandlerId(ThemeCustomizer::THEME_CUSTOMIZER_HANDLER);

        return (string) Uri::withQueryValue($url, 'v', $themeVersion);
    }

    public function writeAsset(string $file, string $content, string $mimeType): void
    {
        $this->storage->put($this->injectVersionInPath($file), [
            'contents' => $content,
            'mime-type' => $mimeType,
        ]);
    }

    private function injectVersionInPath(string $file): string
    {
        return str_replace('css/', 'css/' . $this->version . '/', $file);
    }
}
