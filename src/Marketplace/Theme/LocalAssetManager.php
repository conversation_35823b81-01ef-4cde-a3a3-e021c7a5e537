<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Marketplace\Theme;

class LocalAssetManager implements AssetManager
{
    private $localAssetPath;
    private $version;
    private $baseUrl;

    public function __construct(string $kernelProjectDir, string $version, string $baseUrl)
    {
        $this->localAssetPath = $kernelProjectDir . '/assets/';
        $this->version = $version;
        $this->baseUrl = $baseUrl;
    }

    public function getAssetUrl(string $file): string
    {
        return $this->baseUrl . 'assets/' . $file . '?v=' . $this->version;
    }

    public function writeAsset(string $file, string $content, string $mimeType): void
    {
        $fullFilePath = $this->localAssetPath . $file;
        $fullDirPath = \dirname($fullFilePath);

        if (!is_dir($fullDirPath)) {
            mkdir($fullDirPath, 0777, true);
        }

        file_put_contents($fullFilePath, $content);
    }
}
