<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Theme;

use Less_Parser;

/**
 * Wrapper around Less.php parser to fix bugs
 */
class LessCompiler
{
    /**
     * Compiles a less file into CSS.
     *
     * @param string $filename Less file.
     * @param array $variables Less variables to override.
     *
     * @return string The compiled CSS.
     */
    public function compile(string $filename, array $variables, string $baseUrl): string
    {
        // Create a new instance every time because the parser is not stateless so using it multiple times might create bugs
        $parser = $this->createLessParser();

        $parser->parseFile($filename);
        $parser->ModifyVars($variables);
        $css = $parser->getCss();

        return $this->convertRelativePaths($css, $baseUrl);
    }

    private function createLessParser(): Less_Parser
    {
        return new Less_Parser([
            'compress' => true,
            'strictMath' => true,
            'relativeUrls' => false,
        ]);
    }

    /**
     * Turn relative links into absolute URLs.
     *
     * We cannot use the Less.php parameter for this because it doesn't work on imported files.
     * @see https://github.com/oyejorge/less.php/issues/185
     *
     * @param string $css CSS content.
     * @param string $baseUrl
     *
     * @return string Updated CSS content.
     */
    private function convertRelativePaths(string $css, string $baseUrl): string
    {
        // replaces url('../foo') by url('//host/path/assets/foo')
        $css = preg_replace('#url\s*\(\s*"?\.\./([^"\)]+)"?#', 'url(\'' . $baseUrl . '$1\'', $css);
        $css = preg_replace('#url\s*\(\s*\'\.\./([^\'\)]+)\'#', 'url(\'' . $baseUrl . '$1\'', $css);

        return $css;
    }
}
