<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\RelatedProduct;

use MyCLabs\Enum\Enum;

/**
 * @method static RelatedProductsType RECOMMENDED()
 * @method static RelatedProductsType MANDATORY()
 * @method static RelatedProductsType PAIRED()
 * @method static RelatedProductsType SIMILAR()
 * @method static RelatedProductsType BUNDLE()
 * @method static RelatedProductsType ACCESSORY()
 * @method static RelatedProductsType SERVICE()
 * @method static RelatedProductsType SHIPPING()
 * @method static RelatedProductsType OPTION()
 * @method static RelatedProductsType LINK()
 * @method static RelatedProductsType OTHER()
 */
class RelatedProductsType extends Enum
{
    public const RECOMMENDED = 'recommended';
    public const MANDATORY = 'mandatory';
    public const PAIRED = 'paired';
    public const SIMILAR = 'similar';
    public const BUNDLE = 'bundle';
    public const ACCESSORY = 'accessory';
    public const SERVICE = 'service';
    public const SHIPPING = 'shipping';
    public const OPTION = 'option';
    public const LINK = 'link';
    public const OTHER = 'other';

    public const RELATED_PRODUCT_TYPE_PREFIX = 'related_product_type_';

    public function getTranslated(): string
    {
        return __(self::RELATED_PRODUCT_TYPE_PREFIX . $this->getValue());
    }
}
