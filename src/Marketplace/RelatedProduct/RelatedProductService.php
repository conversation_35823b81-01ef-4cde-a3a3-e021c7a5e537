<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\RelatedProduct;

use Tygh\Database;
use Wizacha\AppBundle\Controller\Api\RelatedProduct\RelatedProductDto;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\RelatedProduct\Exception\InvalidRelatedProductException;
use Wizacha\Marketplace\RelatedProduct\Exception\NotFoundRelatedProductException;
use Wizacha\Product;

class RelatedProductService
{
    /** @var RelatedProductsType[]  */
    protected array $availableTypes;

    protected RelatedProductRepository $relatedProductRepository;

    public function __construct(
        ConfigService $relatedProductConfigService,
        RelatedProductRepository $relatedProductRepository
    ) {
        $this->availableTypes = $relatedProductConfigService->getAvailableTypes();

        $this->relatedProductRepository = $relatedProductRepository;
    }

    /** @return RelatedProductsType[] */
    public function getAvailableTypes(): array
    {
        return $this->availableTypes;
    }

    public function getAvailableTypesToString(): string
    {
        return implode(
            ',',
            array_map(
                function (RelatedProductsType $type): string {
                    return $type->getValue();
                },
                $this->availableTypes
            )
        );
    }

    public function isValidType(string $type): bool
    {
        try {
            $relatedType = new RelatedProductsType($type);
        } catch (\UnexpectedValueException $exception) {
            return false;
        }

        return \in_array($relatedType, $this->getAvailableTypes());
    }

    /**
     * @throws InvalidRelatedProductException
     * @throws \Exception
     */
    public function addRelatedProduct(
        RelatedProductDto $dto,
        ?int $userId
    ): RelatedProduct {
        if ($dto->fromProduct === $dto->toProduct) {
            throw new InvalidRelatedProductException('Parent product and related product must be different.');
        }

        if (false === $this->isValidType($dto->type)) {
            throw new InvalidRelatedProductException(
                sprintf(
                    "The type '%s' is not available in the configuration. The available types are %s.",
                    $dto->type,
                    $this->getAvailableTypesToString()
                )
            );
        }

        $relatedProductType = new RelatedProductsType($dto->type);

        $relatedProduct = new RelatedProduct(
            $dto->fromProduct,
            $dto->toProduct,
            $relatedProductType
        );

        $relatedProduct->setDescription($dto->description);
        $relatedProduct->setExtra($dto->extra);
        $relatedProduct->setUserId($userId);

        $this->relatedProductRepository->save($relatedProduct);

        // Dispatch an event to update the parent readmodel synchronously (related property only)
        \Wizacha\Events\Config::dispatch(
            RelatedProduct::EVENT_CREATE,
            (new \Wizacha\Events\IterableEvent())->setElement($dto->fromProduct->getId())
        );

        return $relatedProduct;
    }

    public function deleteRelatedProduct(
        int $productId,
        ?int $relatedProductId,
        ?string $type
    ): void {
        if (null !== $type
            && false === $this->isValidType($type)
        ) {
            throw new InvalidRelatedProductException(
                sprintf(
                    "The type '%s' is not available in the configuration. The available types are %s.",
                    $type,
                    $this->getAvailableTypesToString()
                )
            );
        }

        $filter['fromProduct'] = $productId;

        if (\is_int($relatedProductId) && 0 !== $relatedProductId) {
            $filter['toProduct'] = $relatedProductId;
        }

        if (\is_string($type)) {
            $filter['type'] = $type;
        }

        $relatedProducts = $this->relatedProductRepository->findBy($filter);

        if (0 === \count($relatedProducts)) {
            throw new NotFoundRelatedProductException('No corresponding related product found.');
        }

        foreach ($relatedProducts as $related) {
            $this->relatedProductRepository->delete($related);

            // Dispatch an event to update the parent readmodel synchronously (related property only)
            \Wizacha\Events\Config::dispatch(
                RelatedProduct::EVENT_DELETE,
                (new \Wizacha\Events\IterableEvent())->setElement($related->getFromProduct()->getId())
            );
        }
    }

    public function deleteRelatedProductById(int $relatedProductId): void
    {
        $relatedProduct = $this->relatedProductRepository->findBy(['id' => $relatedProductId]);

        if (0 === \count($relatedProduct)) {
            throw new NotFoundRelatedProductException('No corresponding related product found.');
        }

        $this->relatedProductRepository->delete($relatedProduct[0]);

        // Dispatch an event to update the parent readmodel synchronously (related property only)
        \Wizacha\Events\Config::dispatch(
            RelatedProduct::EVENT_DELETE,
            (new \Wizacha\Events\IterableEvent())->setElement($relatedProduct[0]->getFromProduct()->getId())
        );
    }

    public function updateRelatedProduct(
        RelatedProductDto $dto,
        ?int $userId
    ): RelatedProduct {
        $relatedProduct = $this->findBy(
            [
                'fromProduct' => $dto->fromProduct->getId(),
                'toProduct' => $dto->toProduct->getId(),
                'type' => $dto->type,
            ]
        );

        if (1 !== \count($relatedProduct)) {
            throw new NotFound('The related product does not exist.');
        }

        $relatedProduct = $relatedProduct[0];

        $relatedProduct->setDescription($dto->description);
        $relatedProduct->setExtra($dto->extra);
        $relatedProduct->setUserId($userId);

        $this->relatedProductRepository->save($relatedProduct);

        // Dispatch an event to update the parent readmodel synchronously (related property only)
        \Wizacha\Events\Config::dispatch(
            RelatedProduct::EVENT_UPDATE,
            (new \Wizacha\Events\IterableEvent())->setElement($dto->fromProduct->getId())
        );

        return $relatedProduct;
    }

    /** @return RelatedProduct[] */
    public function findByFromProductId(int $productId): array
    {
        return $this->relatedProductRepository->findByFromProductId($productId);
    }

    /** @return mixed[] */
    public function findByFromProductIdForBO(int $productId): array
    {
        $relatedProducts = $this->findByFromProductId($productId);

        $relatedProductsForBO = [];

        foreach ($relatedProducts as $related) {
            $product = new Product($related->getToProduct()->getId(), true);

            $relatedProductsForBO[] = $this->toArrayForBO($related, $product);
        }

        return (new RelatedProductSorter())->sort($relatedProductsForBO);
    }

    /**
     * @param mixed[] $filters
     *
     * @return RelatedProduct[]
     */
    public function findBy(array $filters): array
    {
        return $this->relatedProductRepository->findBy($filters);
    }

    /** @return mixed[] */
    protected function toArrayForBO(
        RelatedProduct $relatedProduct,
        Product $product
    ): array {
        return [
            'id' => $relatedProduct->getId(),
            'toProduct' => [
                'id' => $product->getId(),
                'name' => $product->getName(),
                'code' => $product->getProductCode(),
                'image' => $product->getMainImage()
                    ? $product->getMainImage()->getId()
                    : null,
                'companyId' => $product->getCompany()->getId(),
                //in case of vendor connection, the company name can't be retrieve via the product
                //(force use company_id in session)
                'companyName' => fn_get_company_name($product->getCompany()->getId()),
            ],
            'type' => $relatedProduct->getType()->getTranslated(),
            'description' => $relatedProduct->getDescription(),
            'extra' => $relatedProduct->getExtra(),
        ];
    }

    /** @return RelatedProduct[] */
    public function findByToProductId(int $productId): array
    {
        return $this->relatedProductRepository->findByToProductId($productId);
    }
}
