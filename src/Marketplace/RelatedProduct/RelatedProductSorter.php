<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\RelatedProduct;

class RelatedProductSorter
{
    /** @params mixed[] $relatedProducts */
    public function sort(
        array $relatedProducts,
        ?string $orderType = 'Asc',
        ?string $orderName = 'Asc'
    ): array {
        $sortFunction = 'sort' . $orderType . $orderName;

        $result = $this->$sortFunction($relatedProducts);

        return $result;
    }

    /** @return mixed[] */
    protected function sortAscAsc(array $relatedProducts): array
    {
        \usort($relatedProducts, function ($a, $b) {
            $retVal = strcasecmp($a['type'], $b['type']);
            if ($retVal == 0) {
                $retVal = strcasecmp($a['toProduct']['name'], $b['toProduct']['name']);
            }
            return $retVal;
        });

        return $relatedProducts;
    }
}
