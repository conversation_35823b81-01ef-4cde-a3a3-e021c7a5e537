<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\RelatedProduct;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;

class RelatedProductRepository extends ServiceEntityRepository
{
    public function save(RelatedProduct $relatedProduct): RelatedProduct
    {
        $this->getEntityManager()->persist($relatedProduct);
        $this->getEntityManager()->flush();

        return $relatedProduct;
    }

    public function delete(RelatedProduct $relatedProduct): void
    {
        $this->getEntityManager()->remove($relatedProduct);
        $this->getEntityManager()->flush();
    }

    /** @return RelatedProduct[] */
    public function findByFromProductId(int $fromProductId): array
    {
        return $this
            ->getEntityManager()
            ->getRepository(RelatedProduct::class)
            ->findBy(['fromProduct' => $fromProductId]);
    }

    /** @return RelatedProduct[] */
    public function findByToProductId(int $toProductId): array
    {
        return $this
            ->getEntityManager()
            ->getRepository(RelatedProduct::class)
            ->findBy(['toProduct' => $toProductId]);
    }
}
