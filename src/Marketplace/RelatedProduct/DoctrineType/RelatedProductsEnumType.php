<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\RelatedProduct\DoctrineType;

use Acelaya\Doctrine\Type\AbstractPhpEnumType;
use Wizacha\Marketplace\RelatedProduct\RelatedProductsType;

/**
 * Doctrine Type for RelatedProducts
 */
class RelatedProductsEnumType extends AbstractPhpEnumType
{
    protected $enumType = RelatedProductsType::class;

    protected function getSpecificName(): string
    {
        return 'related_products_type';
    }
}
