Wizacha\Marketplace\RelatedProduct\RelatedProduct:
    type: entity
    table: related_products
    uniqueConstraints:
        unique_assoc:
            columns:
                - from_product_id
                - to_product_id
                - type
    id:
        id:
            type: integer
            column: id
            generator:
                strategy: AUTO
    fields:
        type:
            type: php_enum_related_products_type
            column: type
            notBlank: ~
            nullable: false
        description:
            type: string
            column: description
            nullable: true
        extra:
            type: string
            column: extra
            nullable: true
        createdAt:
            type: datetime_immutable
            nullable: false
        updatedAt:
            type: datetime_immutable
            nullable: true
        userId:
            type: integer
            nullable: true
    indexes:
        from_product_id_to_product_id_type:
            columns:
                - from_product_id
                - to_product_id
                - type
        from_product_id:
            columns:
                - from_product_id
        to_product_id:
            columns:
                - to_product_id
    manyToOne:
        fromProduct:
            targetEntity: Wizacha\Marketplace\PIM\Product\Product
            joinColumn:
                name: from_product_id
                referencedColumnName: product_id
                nullable: false
        toProduct:
            targetEntity: Wizacha\Marketplace\PIM\Product\Product
            joinColumn:
                name: to_product_id
                referencedColumnName: product_id
                nullable: false
    lifecycleCallbacks:
        prePersist:
            - defineCreatedAt
        preUpdate:
            - defineUpdatedAt
