<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\RelatedProduct;

use Psr\Log\LoggerAwareTrait;
use Psr\Log\LoggerInterface;

class ConfigService
{
    use LoggerAwareTrait;

    protected string $types;

    public function __construct(
        string $types,
        LoggerInterface $logger
    ) {
        $this->types = $types;
        $this->setLogger($logger);
    }

    protected function checkAvailableTypes(): array
    {
        $availableTypes = explode(',', $this->types);

        // check the types defined in the feature flag are valid
        $typesInError = [];
        foreach ($availableTypes as $index => $type) {
            if ('' === $type) {
                unset($availableTypes[$index]);
                continue;
            }

            if (false === RelatedProductsType::isValid($type)) {
                $typesInError[] = $type;

                // remove the invalid type from the available types
                unset($availableTypes[$index]);
            }
        }

        if (\count($typesInError) > 0) {
            $context = [
                'valid-types' => array_values(RelatedProductsType::toArray()),
                'invalid-types' => $typesInError,
            ];

            $this->logger->error(
                'Invalid type in the definition of available product association types',
                $context
            );
        }

        if (0 === \count($availableTypes)) {
            $this->logger->warning(
                'No definition of available product association types'
            );
        }

        return $availableTypes;
    }

    /** @return RelatedProductsType[] */
    public function getAvailableTypes(): array
    {
        $availableTypes = $this->checkAvailableTypes();

        return array_map(
            function (string $type): RelatedProductsType {
                return new RelatedProductsType($type);
            },
            $availableTypes
        );
    }
}
