<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\RelatedProduct;

use Wizacha\Marketplace\PIM\Product\Product;

class RelatedProduct
{
    public const EVENT_CREATE = 'relatedProduct.create';
    public const EVENT_DELETE = 'relatedProduct.delete';
    public const EVENT_UPDATE = 'relatedProduct.update';

    private int $id;

    private Product $fromProduct;

    private Product $toProduct;

    private RelatedProductsType $type;

    private ?string $description = null;

    private ?string $extra = null;

    private \DateTimeImmutable $createdAt;

    private ?\DateTimeImmutable $updatedAt;

    private ?int $userId = null;

    public function __construct(
        Product $fromProduct,
        Product $toProduct,
        RelatedProductsType $type
    ) {
        $this
            ->setFromProduct($fromProduct)
            ->setToProduct($toProduct)
            ->setType($type);
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getFromProduct(): Product
    {
        return $this->fromProduct;
    }

    public function getToProduct(): Product
    {
        return $this->toProduct;
    }

    public function getType(): RelatedProductsType
    {
        return $this->type;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function getExtra(): ?string
    {
        return $this->extra;
    }

    public function getCreatedAt(): \DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): ?\DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function getUserId(): ?int
    {
        return $this->userId;
    }

    public function setFromProduct(Product $fromProduct): self
    {
        $this->fromProduct = $fromProduct;

        return $this;
    }

    public function setToProduct(Product $toProduct): self
    {
        $this->toProduct = $toProduct;

        return $this;
    }

    public function setType(RelatedProductsType $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function setExtra(?string $extra): self
    {
        $this->extra = $extra;

        return $this;
    }

    public function defineCreatedAt(): self
    {
        $this->createdAt =  new \DateTimeImmutable();

        return $this;
    }

    public function defineUpdatedAt(): self
    {
        $this->updatedAt =  new \DateTimeImmutable();

        return $this;
    }

    public function setUserId(int $userId = null): self
    {
        $this->userId = $userId;

        return $this;
    }

    /** @return mixed[] */
    public function expose(): array
    {
        return [
            'productId' => $this->getToProduct()->getId(),
            'type' => $this->getType(),
            'description' => $this->getDescription(),
            'extra' => $this->getExtra(),
        ];
    }
}
