<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Address;

use Broadway\Serializer\SerializableInterface;
use Wizacha\Marketplace\Address\Exception\AddressFieldsException;

class Address implements SerializableInterface
{
    protected const REQUIRED_FIELDS = [
        'firstname',
        'lastname',
        'address',
        'city',
        'zipcode',
    ];

    protected const REQUIRED_FIELDS_BASKET = [
        'firstname',
        'lastname',
    ];

    /**
     * @var string
     */
    protected $title;

    /**
     * @var string
     */
    protected $firstName;

    /**
     * @var string
     */
    protected $lastName;

    /**
     * @var string
     */
    protected $company;

    /**
     * @var string
     */
    protected $address;

    /**
     * @var string
     */
    protected $additionalAddress;

    /**
     * @var string
     */
    protected $zipCode;

    /**
     * @var string
     */
    protected $city;

    /**
     * @var string
     */
    protected $state;

    /**
     * @var string
     */
    protected $country;

    /**
     * @var string
     */
    protected $phone;

    /**
     * @var string
     */
    protected $email;

    /**
     * @var string
     */
    protected $division;

    /**
     * @var string
     */
    protected $pickupPointId;

    /** @var string */
    protected $label;

    /** @var string */
    protected $comment;

    public function __construct(
        string $title = '',
        string $firstName = '',
        string $lastName = '',
        string $company = '',
        string $address = '',
        string $additionalAddress = '',
        string $zipCode = '',
        string $city = '',
        string $state = '',
        string $country = '',
        string $phone = '',
        string $email = '',
        string $division = '',
        string $pickupPointId = '',
        string $label = '',
        string $comment = ''
    ) {
        $this->title = $title;
        $this->firstName = $firstName;
        $this->lastName = $lastName;
        $this->company = $company;
        $this->address = $address;
        $this->additionalAddress = $additionalAddress;
        $this->zipCode = $zipCode;
        $this->city = $city;
        $this->state = $state;
        $this->country = $country;
        $this->phone = $phone;
        $this->email = $email;
        $this->division = $division;
        $this->pickupPointId = $pickupPointId;
        $this->label = $label;
        $this->comment = $comment;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function getFirstName(): string
    {
        return $this->firstName;
    }

    public function getLastName(): string
    {
        return $this->lastName;
    }

    public function getCompany(): string
    {
        return $this->company;
    }

    public function getAddress(): string
    {
        return $this->address;
    }

    public function getAdditionalAddress(): string
    {
        return $this->additionalAddress;
    }

    public function getZipCode(): string
    {
        return $this->zipCode;
    }

    public function getCity(): string
    {
        return $this->city;
    }

    public function getState(): string
    {
        return $this->state;
    }

    public function getCountry(): string
    {
        return $this->country;
    }

    public function getPhone(): string
    {
        return $this->phone;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function getPickupPointId(): string
    {
        return $this->pickupPointId;
    }

    public function getDivision(): string
    {
        return $this->division;
    }

    public function getLabel(): string
    {
        return $this->label;
    }

    public function getComment(): string
    {
        return $this->comment;
    }

    public function expose(): array
    {
        return [
            'title' => $this->title ?? '',
            'firstname' => $this->firstName ?? '',
            'lastname' => $this->lastName ?? '',
            'company' => $this->company ?? '',
            'address' => $this->address ?? '',
            'address2' => $this->additionalAddress ?? '',
            'zipcode' => $this->zipCode ?? '',
            'city' => $this->city ?? '',
            'state' => $this->state ?? '',
            'country' => $this->country ?? '',
            'phone' => $this->phone ?? '',
            'email' => $this->email ?? '',
            'division' => $this->division ?? '',
            'pickupPointId' => $this->pickupPointId ?? '',
            'label' => $this->label ?? '',
            'comment' => $this->comment ?? '',
        ];
    }

    public function serialize(): array
    {
        return [
            'title' => $this->title ?? '',
            'firstName' => $this->firstName ?? '',
            'lastName' => $this->lastName ?? '',
            'company' => $this->company ?? '',
            'address' => $this->address ?? '',
            'additionalAddress' => $this->additionalAddress ?? '',
            'zipCode' => $this->zipCode ?? '',
            'city' => $this->city ?? '',
            'state' => $this->state ?? '',
            'country' => $this->country ?? '',
            'phone' => $this->phone ?? '',
            'email' => $this->email ?? '',
            'division' => $this->division ?? '',
            'pickupPointId' => $this->pickupPointId ?? '',
            'label' => $this->label ?? '',
            'comment' => $this->comment ?? '',
        ];
    }

    public static function deserialize(array $serialized): self
    {
        return new self(
            $serialized['title'] ?? '',
            $serialized['firstName'] ?? '',
            $serialized['lastName'] ?? '',
            $serialized['company'] ?? '',
            $serialized['address'] ?? '',
            $serialized['additionalAddress'] ?? '',
            $serialized['zipCode'] ?? '',
            $serialized['city'] ?? '',
            $serialized['state'] ?? '',
            $serialized['country'] ?? '',
            $serialized['phone'] ?? '',
            $serialized['email'] ?? '',
            $serialized['division'] ?? '',
            $serialized['pickupPointId'] ?? '',
            $serialized['label'] ?? '',
            $serialized['comment'] ?? ''
        );
    }

    public function assertValid(): self
    {
        return $this->assertValidField(static::REQUIRED_FIELDS);
    }

    public function assertValidBasket(): self
    {
        return $this->assertValidField(static::REQUIRED_FIELDS_BASKET);
    }

    private function assertValidField(array $required): self
    {
        $fields = $this->expose();
        $invalidFields = array_filter($required, function (string $name) use ($fields): bool {
            return "" === trim($fields[$name]);
        });

        if (\count($invalidFields) > 0) {
            throw new AddressFieldsException($invalidFields);
        }

        return $this;
    }
}
