<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright Copyright (c) Wizaplace
 * @license   Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Address\Exception;

use Wizacha\Marketplace\User\Exception\InvalidProfileFields;

class AddressFieldsException extends InvalidProfileFields
{
    public function __construct(array $missingFields)
    {
        parent::__construct($missingFields);
    }
}
