<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Shipping;

use Doctrine\DBAL\Connection;

class ShippingService
{
    /**
     * @var Connection
     */
    private $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
    }

    public function getShippingDeliveryType(int $shippingId): ?DeliveryType
    {
        $row = $this->connection->fetchArray(
            'SELECT w_delivery_type FROM cscart_shippings WHERE shipping_id = :id',
            ['id' => $shippingId]
        );

        if ($row[0] == '') {
            return null;
        }

        return new DeliveryType($row[0]);
    }
}
