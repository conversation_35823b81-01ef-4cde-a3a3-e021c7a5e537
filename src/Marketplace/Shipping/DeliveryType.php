<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Marketplace\Shipping;

use MyCLabs\Enum\Enum;

/**
 * @method static DeliveryType STANDARD()
 * @method static DeliveryType HAND_WITH_CODE()
 * @method static DeliveryType HAND_WITHOUT_CODE()
 * @method static DeliveryType CHRONO_13()
 * @method static DeliveryType CHRONO_RELAIS()
 * @method static DeliveryType MONDIAL_RELAY()
 */
class DeliveryType extends Enum
{
    /**
     * Le mode de livraison "standard" permet de gérer manuellement les livraisons : le
     * vendeur effectue manuellement toutes les formalités relatives à la livraison
     * (génération des étiquettes si nécessaires, paiement du transporteur, etc.)
     */
    private const STANDARD = 'S';

    /**
     * Le mode de livraison "en main propre" inclus un échange de code entre l'acheteur
     * et le vendeur permettant de sécuriser la transaction financière.
     * Ce mode de livraison est toujours gratuit.
     */
    private const HAND_WITH_CODE = 'H';

    /**
     * Le mode de livraison "en main propre sans code" est un mode de livraison gratuit
     * ne nécessitant aucune action système de la part ni du vendeur, ni de l'acheteur.
     * TODO: implémenter ce nouveau mode.
     */
    private const HAND_WITHOUT_CODE = 'N';

    /**
     * Le mode livraison "Chrono 13" permet d'utiliser l'API de Chronopost (qu'il faut
     * configurer) pour livrer en moins de 24h avec Chronopost à une adresse postale.
     * Les étiquettes et les paiements des livraisons se font automatiquement par l'API.
     */
    private const CHRONO_13 = 'C';

    /**
     * Le mode livraison "Chrono Relais (13)" permet d'utiliser l'API de Chronopost (qu'il
     * faut configurer) pour livrer en moins de 24h avec Chronopost dans un point relais.
     * Les étiquettes et les paiements des livraisons se font automatiquement par l'API.
     */
    private const CHRONO_RELAIS = 'R';

    /**
     * Le mode livraison "Mondial Relay" permet d'utiliser l'API de Mondial Relay
     * pour livraison dans un point relais.
     * Les étiquettes et les paiements des livraisons se font automatiquement par l'API.
     */
    private const MONDIAL_RELAY = 'M';
}
