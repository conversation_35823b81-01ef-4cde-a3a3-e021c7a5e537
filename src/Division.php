<?php

/**
 *  <AUTHOR> DevTeam <<EMAIL>>
 *  @copyright   Copyright (c) Wizacha
 *  @license     Proprietary
 */

namespace Wizacha;

use Wizacha\Marketplace\Division\Exception\DivisionFeatureNotEnabled;

class Division
{
    /**
     * Install the divisions and subdivisions for products
     */
    public function installProducts()
    {
        $productsIds = fn_find_all_product_ids();
        try {
            container()->get('marketplace.division.products.service')->createDivisionForProducts($productsIds);

            fn_set_notification('N', __('information'), "Division's products successfully created!");
        } catch (DivisionFeatureNotEnabled $e) {
            fn_set_notification('E', __('error'), "This feature is not enabled");
        } catch (\Exception $e) {
            fn_set_notification('E', __('error'), $e->getMessage());
        }
    }
}
