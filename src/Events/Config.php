<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Events;

use Symfony\Contracts\EventDispatcher\Event;
use Wizacha\Registry;

class Config
{
    /**
     * Helper function to dispatch an event
     * @param string $event_id
     * @deprecated Use event dispatcher in the container
     * @return mixed
     */
    public static function dispatch($event_id, Event $event, Registry $registry = null)
    {
        $registry = $registry ? : Registry::defaultInstance();
        return $registry->container->get('event_dispatcher')->dispatch($event, $event_id);
    }
}
