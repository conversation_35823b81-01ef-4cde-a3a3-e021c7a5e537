<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Events;

use Symfony\Contracts\EventDispatcher\Event;

/**
 * Class IterableEvent
 * An event that we can easily use in a foreach loop.
 * @package Wizacha\Events
 */
class IterableEvent extends Event implements \IteratorAggregate
{
    protected $iterator = null;

    /**
     * @var \Wizacha\Registry
     */
    public $registry;

    public array $metaData = [];

    /**
     * Constructs an empty event
     * @param \Wizacha\Registry $registry
     */
    public function __construct($registry = null)
    {
        $this->registry = ($registry ?: \Wizacha\Registry::defaultInstance());
        $this->iterator = new \EmptyIterator();
    }

    /**
     * Set a single element
     * @param mixed $element
     * @return $this
     */
    public function setElement($element)
    {
        return $this->setArray([$element]);
    }

    public function setMetaData(array $metaData): self
    {
        $this->metaData = $metaData;

        return $this;
    }

    /**
     * Set several elements from an array
     * @param array $elements
     * @return $this
     */
    public function setArray(array $elements)
    {
        $this->iterator = new \ArrayIterator($elements);

        return $this;
    }

    /**
     * @param array $elements
     * @return IterableEvent
     */
    public static function fromArray(array $elements)
    {
        return (new self())
            ->setArray($elements)
        ;
    }

    public static function fromElement($element, array $metaData = []): IterableEvent
    {
        return
            (new self())
            ->setElement($element)
            ->setMetaData($metaData)
        ;
    }

    /**
     * Retrieve elements from an external iterator
     * @param \Iterator $iterator
     * @return $this
     */
    public function setIterator(\Iterator $iterator)
    {
        $this->iterator = $iterator;
        return $this;
    }

    /**
     * @see \IteratorAggregate::getIterator
     */
    public function getIterator()
    {
        return $this->iterator;
    }

    public function getMetaData(): array
    {
        return $this->metaData;
    }
}
