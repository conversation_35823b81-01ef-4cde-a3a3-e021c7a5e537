<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Premoderation;

/**
 * Class BooleanSwitcher
 * Use the RAII pattern to invert a boolean value during the lifetime
 * of an instance.
 * @package Wizacha\Premoderation
 */
class BooleanSwitcher
{
    /**
     * Invert the boolean value
     * @note If input value is not a boolean, it will be cast
     * and a notice will be triggered.
     * @param boolean $boolean
     */
    public function __construct(&$boolean)
    {
        if (! \is_bool($boolean)) {
            trigger_error('Unexpected boolean cast');
            $boolean = (bool) $boolean;
        }
        $this->previous = $boolean;
        $this->boolean  = &$boolean;
        $boolean = !$boolean;
    }

    /**
     * Restore initial boolean value
     */
    public function __destruct()
    {
        $this->boolean = $this->previous;
    }

    private $previous = null;
    private $boolean  = null;
}
