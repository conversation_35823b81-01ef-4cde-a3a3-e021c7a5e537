<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Premoderation;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Tygh\Languages\Languages;
use Wizacha\Component\Locale\Locale;
use Wizacha\Core\Hash\Hash;
use Wizacha\Events\IterableEvent;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\PIM\Moderation\ModerationService;
use Wizacha\Marketplace\PIM\Product\ProductService;
use Wizacha\Product;

class EventSubscriber implements EventSubscriberInterface
{
    public const CHUNK_SIZE = 1000;
    public const HASH_TYPE = 'product_premoderation';
    public const STATUS_STANDBY_WITHOUT_HASH   = 'swoh';
    public const STATUS_STANDBY_WITH_HASH      = 'swh';
    public const STATUS_OTHER                  = 'so';

    private $enabled             = true;
    private $propagation_stopped = false;

    private $productService;
    /**
     * @var ModerationService
     */
    private $moderationService;

    public function __construct(ProductService $productService, ModerationService $moderationService)
    {
        $this->productService = $productService;
        $this->moderationService = $moderationService;
    }

    /**
     * {@inheritdoc}
     */
    public static function getSubscribedEvents()
    {
        return [
            Product::EVENT_CREATE => ['onProductUpdate', 10000],    //Must be the first executed, in order to modify objects
            Product::EVENT_UPDATE => ['onProductUpdate', 10000],    //Must be the first executed, in order to modify objects
        ];
    }

    /**
     * Returns a new BooleanSwitcher that you have to store in a variable
     * in order to lock all premoderation actions during its lifetime.
     * Should be used if you want to modify premoderation status manually.
     * @return BooleanSwitcher
     */
    public function createSwitcher()
    {
        return new BooleanSwitcher($this->enabled);
    }

    /**
     * Route product update event before to check premoderation status
     * @param IterableEvent $event
     */
    public function onProductUpdate(IterableEvent $event)
    {
        if ($this->propagation_stopped) {
            $event->stopPropagation();
            return;
        }
        if (!$this->enabled) {
            return;
        }

        foreach (\iter\chunk($event, self::CHUNK_SIZE, true) as $products) {
            foreach ($this->splitProducts($products) as $step => $ids) {
                switch ($step) {
                    case self::STATUS_STANDBY_WITHOUT_HASH:
                        $this->initializeHash($ids);
                        break;
                    case self::STATUS_STANDBY_WITH_HASH:
                        $this->checkProductsPremodarationStatus($ids);
                        break;
                    case self::STATUS_OTHER:
                        Hash::deleteByList($ids, self::HASH_TYPE);
                }
            }
        }
    }

    /**
     * Modify premoderation status of products with STATUS_STANDBY premoderation status
     * @param array $products on standby with an hash
     */
    public function checkProductsPremodarationStatus(array $products)
    {
        $contentLocale = GlobalState::contentLocale();
        $locales = array_keys(Languages::getAll());
        $products_to_modify = array_filter(
            $products,
            function ($product_id) use ($locales) {
                //si ne serait-ce qu'une des traductions du produit a changé alors
                //il faut remettre le produit en modération
                foreach ($locales as $locale) {
                    GlobalState::switchContentTo(new Locale($locale));

                    if (Hash::hasChanged($product_id, $this->getDataForHash($product_id), self::HASH_TYPE)) {
                        return true;
                    }
                }

                return false;
            }
        );

        if ($products_to_modify) {
            //Prevent to trigger same event twice
            $switcher = new BooleanSwitcher($this->propagation_stopped);
            Hash::deleteByList($products_to_modify, self::HASH_TYPE);
            $this->moderationService->setProductsStatus(
                $products_to_modify,
                \Wizacha\Premoderation::STATUS_PENDING
            );
        }
    }

    /**
     * Update hash for products. Rxpect product on standby without hash
     *
     * @param array $products
     */
    public function initializeHash(array $products)
    {
        $contentLocale = GlobalState::contentLocale();

        foreach ($products as $product_id) {
            foreach (array_keys(Languages::getAll()) as $locale) {
                GlobalState::switchContentTo(new Locale($locale));
                Hash::updateHash($product_id, $this->getDataForHash($product_id), self::HASH_TYPE);
            }
        }

        GlobalState::switchContentTo($contentLocale);
    }

    /**
     * Filter fn_get_product_data for the hash
     *
     * @param int $product_id
     * @return array
     */
    public function getDataForHash($product_id)
    {
        $data = fn_get_product_data($product_id, $_SESSION['auth'], (string) GlobalState::contentLocale(), '', true, true, true, false, false, true, true, true);
        $data['brand'] = fn_product_get_brand($data['product_features']);
        $data['free_features'] = $this->productService->getFreeLegacyAttributes($product_id);

        // Dont send back in premoderation if only those fields were modified:
        $exclude = ['tax_ids', 'amount', 'price', 'base_price', 'crossed_out_price', 'infinite_stock', 'updated_timestamp'];

        return array_diff_key(
            $data,
            array_flip($exclude)
        );
    }

    /**
     * Return an array where products are split by approval status and if they got an hash
     *
     * @param array $products
     * @return array
     */
    public function splitProducts(array $products)
    {
        if (empty($products)) {
            return [];
        }

        $raw = \Tygh\Database::getArray(
            'SELECT
                GROUP_CONCAT(`?:products`.`product_id`) as `ids`,
                IF(`approved` = ?s,
                    IF(`hash` IS NULL, ?s, ?s),
                    ?s
                ) as `step`
            FROM ?:products
                LEFT JOIN ?:w_hashes ON `object_id` = CAST(`product_id` AS CHAR) AND `object_type` = ?s AND `locale` = ?s
            WHERE `product_id` IN (?a)
            GROUP BY `step`',
            \Wizacha\Premoderation::STATUS_STANDBY,
            self::STATUS_STANDBY_WITHOUT_HASH,
            self::STATUS_STANDBY_WITH_HASH,
            self::STATUS_OTHER,
            self::HASH_TYPE,
            (string) GlobalState::contentLocale(),
            $products
        );

        $return = [];
        foreach ($raw as $datas) {
            $return[$datas['step']] = explode(',', $datas['ids']);
        }
        return $return;
    }
}
