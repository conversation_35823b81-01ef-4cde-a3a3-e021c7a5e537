<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Premoderation\ProductModeration;

class ProductModerationInProgress
{
    /**
     * @var int
     */
    private $productId;

    /**
     * @var \DateTimeInterface
     */
    private $createdAt;

    public function __construct(int $productId)
    {
        $this->productId = $productId;
        $this->createdAt = new \DateTime();
    }

    public function getProductId(): int
    {
        return $this->productId;
    }

    public function setProductId(string $productId): self
    {
        $this->productId = $productId;

        return $this;
    }

    public function getCreatedAt(): \DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }
}
