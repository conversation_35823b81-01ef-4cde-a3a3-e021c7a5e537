<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Premoderation\ProductModeration;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Persistence\ManagerRegistry;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Driver\Statement;
use Wizacha\Premoderation;

/**
 * @method ProductModerationInProgress|null find($id, $lockMode = null, $lockVersion = null)
 */
class ProductModerationInProgressRepository extends ServiceEntityRepository
{
    /**
     * @var Connection
     */
    protected $connection;

    public function __construct(ManagerRegistry $registry, string $entityClass)
    {
        parent::__construct($registry, $entityClass);
        $this->connection = $this->getEntityManager()->getConnection();
    }

    /**
     * Save entity into database
     */
    public function save(ProductModerationInProgress $productModeration, bool $flush): ProductModerationInProgress
    {
        $this->getEntityManager()->persist($productModeration);
        if (true === $flush) {
            $this->flush();
        }

        return $productModeration;
    }

    public function flush(): void
    {
        $this->getEntityManager()->flush();
    }

    /**
     * Get all products waiting in moderation table for more than 24h
     */
    public function getOldPendingModeration(): Statement
    {
        $datetime = new \DateTime();
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder->select('m.product_id, p.company_id')
            ->from('doctrine_product_moderation_in_progress', 'm')
            ->innerJoin('m', 'cscart_products', 'p', 'm.product_id = p.product_id')
            ->where('m.created_at <= :created_at')
            ->setParameter('created_at', $datetime->modify('-1 day')->format(\DateTime::RFC3339));

        return $queryBuilder->execute();
    }

    public function delete(int $productId): void
    {
        $productModeration = $this->getEntityManager()->find(ProductModerationInProgress::class, $productId);

        if (null !== $productModeration) {
            $this->getEntityManager()->remove($productModeration);
            $this->flush();
        }
    }

    /**
     * Return products with moderation information
     * @param int[] $productsId
     */
    public function getProductsWithModeration(array $productsId): Statement
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder->select('p.product_id, p.company_id, m.created_at moderation_date')
            ->from('cscart_products', 'p')
            ->leftJoin('p', 'doctrine_product_moderation_in_progress', 'm', 'm.product_id = p.product_id')
            ->where('p.product_id IN (:products_id)')
            ->setParameter('products_id', $productsId, \Doctrine\DBAL\Connection::PARAM_INT_ARRAY)
            ->orderBy('p.product_id');

        return $queryBuilder->execute();
    }

    /**
     * Return all products of a company that not in moderation table
     */
    public function getProductsNotInProgressByCompanyId(int $companyId): Statement
    {
        $queryBuilder = $this->connection->createQueryBuilder();
        $queryBuilder->select('p.product_id')
            ->from('cscart_products', 'p')
            ->leftJoin('p', 'doctrine_product_moderation_in_progress', 'm', 'm.product_id = p.product_id')
            ->where('p.approved = :status')
            ->andWhere('p.company_id = :company_id')
            ->andWhere('m.product_id IS NULL')
            ->setParameter('status', Premoderation::STATUS_PENDING)
            ->setParameter('company_id', $companyId);

        return $queryBuilder->execute();
    }

    /** @return array */
    public function getAllProductsInProgress(): array
    {
        return $this->getEntityManager()->getRepository(ProductModerationInProgress::class)->findAll();
    }

    public function removeAll(): void
    {
        $this->connection->exec('TRUNCATE doctrine_product_moderation_in_progress');
    }
}
