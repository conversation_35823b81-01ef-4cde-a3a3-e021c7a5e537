<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Premoderation\ProductModeration;

use Doctrine\DBAL\Driver\Statement;
use Wizacha\Marketplace\Exception\NotFound;

class ProductModerationInProgressService
{
    /**
     * @var ProductModerationInProgressRepository
     */
    protected $moderationRepository;

    public function __construct(
        ProductModerationInProgressRepository $moderationRepository
    ) {
        $this->moderationRepository = $moderationRepository;
    }

    public function get(int $productId): ProductModerationInProgress
    {
        /** @var ProductModerationInProgress $productModeration */
        $productModeration = $this->moderationRepository->find($productId);

        if (null === $productModeration) {
            throw new NotFound();
        }

        return $productModeration;
    }

    public function create(int $productId, bool $flush = true): ProductModerationInProgress
    {
        $productModeration = new ProductModerationInProgress($productId);

        return $this->moderationRepository->save($productModeration, $flush);
    }

    /**
     * Get all products waiting in moderation table for more than 24h
     */
    public function getOldPendingModeration(): Statement
    {
        return $this->moderationRepository->getOldPendingModeration();
    }

    public function flush(): void
    {
        $this->moderationRepository->flush();
    }

    public function delete(int $productId): void
    {
        $this->moderationRepository->delete($productId);
    }

    /**
     * Return products with moderation information
     * @param int[] $productsId
     */
    public function getProductsWithModerationDetails(array $productsId): Statement
    {
        return $this->moderationRepository->getProductsWithModeration($productsId);
    }

    /**
     * Return all products of a company that not in moderation table
     */
    public function getProductsNotInProgressByCompanyId(int $companyId): Statement
    {
        return $this->moderationRepository->getProductsNotInProgressByCompanyId($companyId);
    }

    /**
     * Return all products in moderation table
     */
    public function getAllProductsInProgress(): array
    {
        return $this->moderationRepository->getAllProductsInProgress();
    }

    public function removeAll(): void
    {
        $this->moderationRepository->removeAll();
    }
}
