<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Core\Iterator;

/**
 * Class PdoColumnIterator
 * Allows to easily iterates on a column of PDOStatement results.
 * You can use it in a foreach, or with the count() function.
 * If you intend to perform several rewind operations, performance will
 * be improved with a prepared statement.
 * @package Wizacha\Core\Iterator
 */
class PdoColumnIterator extends PdoIterator
{

    public function next()
    {
        $this->value = $this->stmt->fetchColumn($this->item);
        ++$this->key;
    }

    public function toArray(): array
    {
        $return = [];

        foreach ($this as $key => $value) {
            $return[$key] = $value;
        }

        return $return;
    }
}
