<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Core\Iterator;

/**
 * Class PdoIterator
 * Allows to easily iterates on PDOStatement results.
 * You can use it in a foreach, or with the count() function.
 * If you intend to perform several rewind operations, performance will
 * be improved with a prepared statement.
 * @package Wizacha\Core\Iterator
 */
class PdoIterator implements \Countable, \Iterator
{
    protected $stmt   = null;
    protected $value  = false;
    protected $item = null;
    protected $key    = null;

    public function __construct(\PDOStatement $stmt, $item = 0)
    {
        $this->stmt   = $stmt;
        $this->item = $item;
        $this->rewind();
    }

    public function next()
    {
        $this->value = $this->stmt->fetch($this->item);
        ++$this->key;
    }

    public function count()
    {
        return $this->stmt->rowCount();
    }

    public function rewind()
    {
        if ($this->key !== 0) {
            $this->stmt->execute();
            $this->key = -1;
            $this->next();
        }
    }

    public function key()
    {
        return $this->valid() ? $this->key : null;
    }

    public function current()
    {
        return $this->value;
    }

    public function valid()
    {
        return false !== $this->value;
    }
}
