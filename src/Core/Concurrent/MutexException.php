<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Core\Concurrent;

/**
 * Class MutexException
 * A simple wrapper for corresponding Mutex class exceptions
 * @package Wizacha\Core\Concurrent
 */
class MutexException extends \Exception
{
    public string $lockName;

    public function __construct(string $lockName)
    {
        $this->lockName = $lockName;

        parent::__construct(
            \sprintf(
                'Impossible to acquire lock %s',
                $this->lockName
            )
        );
    }
}
