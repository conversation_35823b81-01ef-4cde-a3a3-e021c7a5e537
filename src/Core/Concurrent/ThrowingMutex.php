<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Core\Concurrent;

class ThrowingMutex extends AbstractMutex
{
    protected function lockGuard(): void
    {
        $lockAcquired = $this->acquireLock(0);

        if (false === $lockAcquired) {
            throw new ThrowingMutexException(
                $this->getName()
            );
        }
    }
}
