<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Core\Concurrent;

use Doctrine\DBAL\Connection;
use Wizacha\Core\Concurrent\MutexException;

/**
 * Class Mutex
 * Allows to define critical section according to a MySql lock
 * @package Wizacha\Core\Concurrent
 */
abstract class AbstractMutex
{
    public const LOCK_PREFIX = 'wLock_';
    public const HASH_ALGO = 'sha1';

    protected string $name;
    protected Connection $connection;
    protected ?int $timeout;

    /**
     * Try to acquire the lock corresponding to input name. Throw a MutexException
     * in case of failure.
     *
     * ⚠ Before MySQL 5.7, only a single simultaneous lock can be acquired
     * and GET_LOCK() releases any existing lock.
     * @see https://dev.mysql.com/doc/refman/5.7/en/locking-functions.html
     *
     * @throws MutexException
     */
    public function __construct(
        string $name,
        Connection $connection,
        ?int $timeout = null
    ) {
        $this->name = $name;
        $this->connection = $connection;
        $this->timeout = $timeout;

        $this->lockGuard();
    }

    /**
     * Release the lock during the destruction
     */
    public function __destruct()
    {
        $this->connection->executeQuery(
            'DO RELEASE_LOCK(:name)',
            ['name' => $this->getHash()],
            ['name' => \PDO::PARAM_STR]
        );
    }

    public function getName(): string
    {
        return $this->name;
    }

    protected function getHash(): string
    {
        return
            self::LOCK_PREFIX
            . \hash(self::HASH_ALGO, $this->name)
        ;
    }

    protected function acquireLock(int $timeout): bool
    {
        $result = $this->connection->executeQuery(
            'SELECT GET_LOCK(:name, :timeout)',
            [
                'name' => $this->getHash(),
                'timeout' => $timeout,
            ],
            [
                'name' => \PDO::PARAM_STR,
                'timeout' => \PDO::PARAM_INT,
            ]
        );

        return (bool) \intval(
            $result->fetchColumn()
        );
    }

    abstract protected function lockGuard(): void;
}
