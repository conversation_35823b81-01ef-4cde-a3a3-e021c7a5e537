<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Core\Concurrent;

use Doctrine\DBAL\Connection;

class MutexService
{
    private Connection $connection;

    public function __construct(
        Connection $connection
    ) {
        $this->connection = $connection;
    }

    /**
     * 🛑 BlockingMutex
     * waiting for release
     * throwing exception at time out
     */
    public function createBlockingMutex(
        string $name,
        string $key,
        ?int $timeout = null
    ): BlockingMutex {
        return new BlockingMutex(
            "$name:$key",
            $this->connection,
            $timeout
        );
    }

    /**
     * ⛔ ThrowingMutex
     * instant throwing exception
     */
    public function createThrowingMutex(
        string $name,
        string $key
    ): ThrowingMutex {
        return new ThrowingMutex(
            "$name:$key",
            $this->connection
        );
    }
}
