<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Core\Concurrent;

class BlockingMutex extends AbstractMutex
{
    public const DEFAULT_TIMEOUT = 8;

    protected function lockGuard(): void
    {
        $lockAcquired = $this->acquireLock(
            $this->timeout ?? self::DEFAULT_TIMEOUT
        );

        if (false === $lockAcquired) {
            throw new BlockingMutexException(
                $this->getName()
            );
        }
    }
}
