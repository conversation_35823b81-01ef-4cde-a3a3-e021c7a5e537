<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Core\Hash;

use Wizacha\Component\Locale\Locale;
use Wizacha\Marketplace\GlobalState\GlobalState;

class Hash
{
    /**
     * Computes a hash according to $data and store it in database
     *
     * @param int $id
     * @param array|\Serializable $datas
     * @param string $type
     * @param Locale $locale
     *
     * @return boolean true if hash is updated because different of previous value, false else
     */
    public static function updateHash($id, $datas, $type, Locale $locale = null): bool
    {
        $old_md5 = \Tygh\Database::getField(
            'SELECT hash FROM ?:w_hashes WHERE object_type= ?s AND object_id=?s AND `locale`=?s',
            $type,
            $id,
            (string) GlobalState::contentLocale()
        );

        $new_md5 = md5(serialize($datas));

        if ($new_md5 != $old_md5) {
            \Tygh\Database::query(
                'REPLACE INTO ?:w_hashes ?e',
                [
                    'object_type' => $type,
                    'object_id' => $id,
                    'hash' => $new_md5,
                    'locale' => (string) ($locale ?? GlobalState::contentLocale()),
                ]
            );

            return true;
        }
        return false;
    }

    /**
     * @param integer $id
     * @param array|\Serializable $datas
     * @param string $type
     * @return bool
     */
    public static function hasChanged($id, $datas, $type)
    {
        $current_md5 = \Tygh\Database::getField(
            'SELECT hash FROM ?:w_hashes WHERE object_type= ?s AND object_id=?s AND `locale`=?s',
            $type,
            $id,
            (string) GlobalState::contentLocale()
        );
        $new_md5 = md5(serialize($datas));

        return $new_md5 != $current_md5;
    }

    /**
     * Removed hash in database
     *
     * @param array|string|int $ids
     * @param string $type
     */
    public static function deleteByList($ids, $type)
    {
        if (!$ids) {
            return;
        }
        if (\is_array($ids)) {
            // Convertit avec la fonction de CsCart toutes les valeurs de $ids en chaine pour etre conforme
            // à l'index de la table w_hashes
            $ids = array_map(function ($id) {
                return db_quote('?s', $id);
            }, $ids);

            $in = implode(',', $ids);
        } else {
            $in = db_quote('?s', $ids);
        }

        //on ne conditionne pas sur la langue parce qu'on ne veut pas faire de
        //distinction d'un même objet dans ses différentes langues
        \Tygh\Database::query("DELETE FROM ?:w_hashes WHERE object_type = ?s AND object_id IN ({$in})", $type);
    }

    /**
     * Removed all hashes in database of a given type
     *
     * @param string $type
     */
    public static function deleteByType($type)
    {
        \Tygh\Database::query("DELETE FROM ?:w_hashes WHERE object_type = ?s", $type);
    }
}
