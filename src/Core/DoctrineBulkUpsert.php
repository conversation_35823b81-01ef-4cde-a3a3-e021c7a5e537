<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Core;

use Doctrine\DBAL\Driver\Connection;
use Doctrine\DBAL\Statement;
use Wizacha\Core\TempBag\TempBag;

class DoctrineBulkUpsert
{
    private Connection $connection;
    private string $upsertQuery;
    private Statement $getUpsertStatement;
    private TempBag $tempBag;

    public function __construct(
        Connection $connection,
        string $upsertQuery
    ) {
        $this->connection = $connection;
        $this->upsertQuery = $upsertQuery;

        $this->initTempBag();
    }

    private function getUpsertStatement(): Statement
    {
        return $this->getUpsertStatement ??= $this->connection->prepare($this->upsertQuery);
    }

    private function initTempBag()
    {
        $this->tempBag = new TempBag();
    }

    public function add(array $parameters): void
    {
        $this->tempBag->append($parameters);
    }

    public function commit()
    {
        if ($this->tempBag->count() === 0) {
            return;
        }

        $this->connection->beginTransaction();

        foreach ($this->tempBag as $parameters) {
            $this->getUpsertStatement()->execute(
                $parameters
            );
        }
        $this->connection->commit();

        /** reinit empty TempBag */
        $this->initTempBag();
    }
}
