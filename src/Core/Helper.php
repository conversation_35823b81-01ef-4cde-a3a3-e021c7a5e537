<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Core;

class Helper
{
    protected const FLATTEN_SEP_CHAR = '.';
    protected const FLATTEN_ESCAPE_STR = '\.';

    /**
     * flatten a nested array tree
     *
     * @return \Generator<mixed>
     */
    public static function arrayFlatten(array $data, ?string $path = null): \Generator
    {
        foreach ($data as $key => $value) {
            $escapedKey = \str_replace(
                static::FLATTEN_SEP_CHAR,
                static::FLATTEN_ESCAPE_STR,
                $key
            );

            $currentPath = \is_null($path)
                ? $escapedKey
                : $path . static::FLATTEN_SEP_CHAR . $escapedKey
            ;

            if (true === \is_array($value)) {
                yield from static::arrayFlatten($value, $currentPath);
            } else {
                yield $currentPath => $value;
            }
        }
    }

    /**
     * Range as generator
     */
    public static function xrange(int $size): \Generator
    {
        for ($i = 0; $i < $size; ++$i) {
            yield $i;
        }
    }
}
