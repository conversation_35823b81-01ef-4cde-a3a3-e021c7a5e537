<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Core;

class Looper
{
    private \Generator $generator;

    public function __construct(iterable $iterable)
    {
        $this->generator = $this->generate($iterable);
    }

    /**
     * @return mixed
     */
    public function step()
    {
        $value = $this->generator->current();
        $this->generator->next();

        return $value;
    }

    /**
     * Infinite round robin looper
     */
    private static function generate(iterable $iterable): \Generator
    {
        while (true) {
            foreach ($iterable as $value) {
                yield $value;
            }
        }
    }
}
