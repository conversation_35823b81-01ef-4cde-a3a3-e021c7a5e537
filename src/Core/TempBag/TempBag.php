<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Core\TempBag;

/**
 * A RAM friendly \Iterator with php://temp file pointer.
 *
 * See this as a non associative array that can make use of the file system
 * when the $maxMemory usage value is reached.
 */
final class TempBag implements \Iterator, \Countable
{
    public const MAX_MEMORY = 64 * (1024 ** 2); # 64 MB

    /** @var resource */
    private $filePointer;

    private int $position = 0;
    private int $size = 0;

    private TempBagCodec $codec;

    /**
     * @param int $maxMemory when reached, php://temp use file system
     */
    public function __construct(int $maxMemory = self::MAX_MEMORY)
    {
        $path = \sprintf(
            'php://temp/maxmemory:%s',
            $maxMemory
        );

        $this->filePointer = \fopen($path, 'r+');
        $this->position = 0;

        $this->codec = new TempBagCodec();
    }

    public function append($data)
    {
        \fwrite(
            $this->filePointer,
            $this->codec->encode($data) . PHP_EOL
        );
        $this->next();
        $this->size = $this->key();
    }

    private function read()
    {
        $data = \fgets($this->filePointer);

        return $this->codec->decode($data);
    }

    public function rewind(): void
    {
        $this->position = 0;
        \rewind($this->filePointer);
    }

    public function key()
    {
        return $this->position;
    }

    public function next(): void
    {
        ++$this->position;
    }

    public function valid(): bool
    {
        return $this->position < $this->size;
    }

    public function current()
    {
        $data = $this->read();

        return $data;
    }

    public function count(): int
    {
        return $this->size;
    }
}
