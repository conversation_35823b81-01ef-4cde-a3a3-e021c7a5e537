<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Core\TempBag;

class TempBagCodec
{
    /**
     * @param mixed $data
     * @return string serialized data as one line string
     */
    public function encode($data): string
    {
        return \base64_encode(
            \serialize(
                $data
            )
        );
    }

    /**
     * @param string $data serialized data as one line string
     * @return mixed
     */
    public function decode(string $data)
    {
        return \unserialize(
            \base64_decode(
                $data
            )
        );
    }
}
