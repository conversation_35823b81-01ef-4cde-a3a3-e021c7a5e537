<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Core;

class Truncate
{
    /**
     * Cuts a string to the length of $length if the text is longer than length and keep spaces.
     * Ex:
     *   $text = "Illud tamen clausos vehementer angebat"
     *   $length = 25
     *   return => "Illud tamen clausos"
     * Word "vehementer" won't cut in mid-word.
     **/
    public static function cut(string $text, int $length, int $nbLines): array
    {
        $text = trim($text);
        $lines = [];
        $result = null;

        for ($i = 0; $i < $nbLines; $i++) {
            if (\is_string($result)) {
                $text = trim(mb_substr($text, mb_strlen($result)));
            }
            $result = static::cutter($text, $length);
            $lines[] = $result;
        }

        return $lines;
    }

    protected static function cutter(string $text, int $length): string
    {
        if (mb_strlen($text) > $length) {
            $text = mb_substr($text, 0, $length);

            $spacePosition = mb_strrpos($text, ' ');
            if (false !== $spacePosition) {
                $text = mb_substr($text, 0, $spacePosition);
            }
        }

        return $text;
    }
}
