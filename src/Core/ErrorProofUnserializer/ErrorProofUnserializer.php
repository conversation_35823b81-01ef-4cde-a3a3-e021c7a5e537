<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Core\ErrorProofUnserializer;

use <PERSON><PERSON><PERSON><PERSON>n\Diff\Differ;

class ErrorProofUnserializer
{
    private string $serialized;

    public function __construct(
        string $serialized
    ) {
        $this->serialized = $serialized;
    }

    public function unserialize()
    {
        if ($this->isTruncated()) {
            throw new TruncatedSerializedStringException();
        }

        $unserialized = @\unserialize($this->serialized);

        /** early return properly unserialized string*/
        if ($unserialized !== false) {
            return $unserialized;
        }

        $repairedLengthUnserialized = @\unserialize(
            $this->repairIncorrectLength()
        );

        if (false === $repairedLengthUnserialized) {
            throw new InvalidSerializedStringException();
        }

        return $repairedLengthUnserialized;
    }

    public function diff(): array
    {
        $differ = new Differ();

        return \array_chunk(
            \array_filter(
                $differ->diffToArray(
                    $this->serialized,
                    static::repairIncorrectLength()
                ),
                fn(array $tuple) => $tuple[1] !== 0
            ),
            2
        );
    }

    /**
     * Attempt to repair incorrect length in serialized data
     */
    public function repairIncorrectLength(): string
    {
        return \preg_replace_callback(
            $this->getPattern(),
            function (array $matches) {
                $actualString = $matches['actual_string'];
                $expectedLength = \strlen($actualString);

                return \sprintf(
                    's:%s:"%s',
                    $expectedLength,
                    $actualString
                );
            },
            $this->serialized
        );
    }

    private function getPattern(): string
    {

        $commonSignals = [
            's:\d+:"',  # string
            'i:\d+;',  # integer
            'd:\d+(\.\d+)?;',  # decimal (float)
            'a:\d+:{',  # array
            'N;',  # null
            'O:\d+:"',  # object
            'b:\d;',  # boolean
            '$',  # string end
        ];

        $nextSignals = [
            ...$commonSignals,
            \sprintf(
                '}+(%s)',
                \implode(
                    '|',
                    $commonSignals
                )
            ),  # array/object stop signals
        ];

        $lookAheadPattern = \sprintf(
            '(?=";(%s))',
            \implode('|', $nextSignals)
        );

        return \sprintf(
            '/(s\:(?<actual_length>\d+):"(?<actual_string>.*?))%s/s',
            $lookAheadPattern
        );
    }

    private function isTruncated(): bool
    {
        $lastChar = $this->serialized[-1];

        return (
            $lastChar !== ';'
            && $lastChar !== '}'
        );
    }
}
