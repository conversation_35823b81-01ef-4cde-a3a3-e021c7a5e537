<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Core;

/**
 * RAM friendly
 */
class LimitedLogCounter implements \Countable
{
    public const DEFAULT_LIMIT = 64;

    private int $limit;
    private \ArrayObject $data;

    private int $count = 0;
    private bool $isFull = false;

    public function __construct(int $limit = self::DEFAULT_LIMIT)
    {
        $this->limit = $limit;
        $this->data = new \ArrayObject();
    }

    public function getArray(): array
    {
        return $this->data->getArrayCopy();
    }

    public function append($data): void
    {
        if ($this->count >= $this->limit) {
            $this->isFull = true;
        }

        if (false === $this->isFull) {
            $this->data->append($data);
        }

        ++$this->count;
    }

    public function count(): int
    {
        return $this->count;
    }

    public function isFull(): bool
    {
        return $this->isFull;
    }

    public function more(): int
    {
        return max(
            0,
            $this->count - $this->limit
        );
    }
}
