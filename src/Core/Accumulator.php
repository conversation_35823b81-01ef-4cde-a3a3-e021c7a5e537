<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Core;

/**
 * A bulk processing helper
 * trigger a closure each static::DEFAULT_SIZE steps
 */
final class Accumulator
{
    public const DEFAULT_SIZE = 16;

    private int $size;
    private \Closure $initCallback;
    private \Closure $lapCallback;
    private \Closure $finalizeCallback;

    private int $counter = 0;

    private \ArrayObject $bag;

    public function __construct(
        int $size = self::DEFAULT_SIZE,
        \Closure $initCallback,
        ?\Closure $lapCallback = null,
        ?\Closure $finalizeCallback = null
    ) {
        $this->size = $size;
        $this->initCallback = $initCallback;
        $this->lapCallback = $lapCallback ?? $this->initCallback;
        $this->finalizeCallback = $finalizeCallback ?? $this->lapCallback;

        $this->clearBag();
    }

    public function init(
        $data = null,
        ...$args
    ) {
        $this->addBag($data);

        return $this->getInitCallback()(
            $this->bag,
            ...$args
        );
    }

    public function trigger(
        $data = null,
        ...$args
    ) {
        $this->addBag($data);
        $this->increment();

        if (true === $this->isNewLap()) {
            return $this->getLapCallback()(
                $this->getAndClearBag(),
                ...$args
            );
        }
    }

    public function finalize(
        $data = null,
        ...$args
    ) {
        $this->addBag($data);

        if (false === $this->isNewLap()) {
            return $this->getFinalizeCallback()(
                $this->getAndClearBag(),
                ...$args
            );
        }
    }

    private function getInitCallback(): \Closure
    {
        return $this->initCallback;
    }

    private function getLapCallback(): \Closure
    {
        return $this->lapCallback;
    }

    private function getFinalizeCallback(): \Closure
    {
        return $this->finalizeCallback;
    }

    private function increment(): void
    {
        $this->counter++;
    }

    private function isNewLap(): bool
    {
        return 0 === ($this->getCounter() % $this->getSize());
    }

    private function getSize(): int
    {
        return $this->size;
    }

    private function getCounter(): int
    {
        return $this->counter;
    }

    private function addBag($data = null): void
    {
        if (false === \is_null($data)) {
            $this->bag->append($data);
        }
    }

    public function getBag(): \ArrayObject
    {
        return $this->bag;
    }

    private function clearBag(): void
    {
        $this->bag = new \ArrayObject();
    }

    private function getAndClearBag(): \ArrayObject
    {
        $bag = $this->getBag();
        $this->clearBag();

        return $bag;
    }
}
