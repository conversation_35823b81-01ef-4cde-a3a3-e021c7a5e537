<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha;

use Tygh\Database;
use Wizacha\Marketplace\Entities\Company;

use function Wizacha\Marketplace\Order\is_order_status_equal_to;

/**
 * @deprecated
 * @see \Wizacha\Marketplace\Order\OrderStatus
 */
class OrderStatus
{
    //Standard statuses
    public const STANDBY_BILLING = 'O';
    public const STANDBY_VENDOR = 'P';
    public const PROCESSING_SHIPPING = 'E';
    public const PROCESSED = 'C';
    public const COMPLETED = 'H';

    //Others
    public const BILLING_FAILED = 'F';
    public const VENDOR_DECLINED = 'D';
    public const STANDBY_SUPPLYING = 'B'; # STATUS_BACKORDERED_ORDER; // B
    public const UNPAID = 'G';
    public const REFUND = 'A';
    public const CANCEL = 'I'; # STATUS_CANCELED_ORDER; // I

    public const DELAY_FOR_VENDOR_TYPE_COMPANIES = 21;
    public const DELAY_FOR_CLIENT_TYPE_COMPANIES = 7;

    public static function getPaidStatuses(): array
    {
        return [
            self::STANDBY_VENDOR,
            self::STANDBY_SUPPLYING,
            self::PROCESSING_SHIPPING,
            self::PROCESSED,
            self::COMPLETED
        ];
    }

    public static function getVendorDisplayedStatuses(): array
    {
        $addedStatus = Database::getColumn("SELECT status FROM cscart_statuses WHERE type='O' AND is_default='N'");
        return array_merge([
            OrderStatus::VENDOR_DECLINED,
            OrderStatus::STANDBY_VENDOR,
            OrderStatus::REFUND,
            OrderStatus::COMPLETED,
            OrderStatus::PROCESSED,
            OrderStatus::PROCESSING_SHIPPING,
            OrderStatus::STANDBY_SUPPLYING,
        ], $addedStatus);
    }

    public static function getStandbyStatuses(): array
    {
        return [
            self::STANDBY_VENDOR,
            self::STANDBY_SUPPLYING
        ];
    }

    public static function getDeliveredStatuses(): array
    {
        return [
            self::PROCESSED,
        ];
    }

    /**
     * Change the status "processed" to "completed" for all orders respecting the conditions
     */
    public static function completeProcessed()
    {

        $query = "SELECT order_id FROM ?:orders as o INNER JOIN ?:companies as c ON o.company_id=c.company_id
                  WHERE c.w_company_type = ?s AND o.status = ?s AND (TO_DAYS(NOW()) - TO_DAYS(w_last_status_change)) >= ?i";
        $vendor_type_order_ids = \Tygh\Database::getColumn(
            $query,
            \Wizacha\User::VENDOR_TYPE,
            \Wizacha\OrderStatus::PROCESSED,
            self::DELAY_FOR_VENDOR_TYPE_COMPANIES
        );
        $client_type_order_ids = \Tygh\Database::getColumn(
            $query,
            \Wizacha\User::CLIENT_TYPE,
            \Wizacha\OrderStatus::PROCESSED,
            self::DELAY_FOR_CLIENT_TYPE_COMPANIES
        );

        $markAsDelivered = container()->get('marketplace.order.action.mark_as_delivered');
        $endWithDrawalPeriod = container()->get('marketplace.order.action.end_withdrawal_period');

        array_map(
            function ($order_id) use ($markAsDelivered, $endWithDrawalPeriod) {
                $order = container()->get('marketplace.order.order_service')->getOrder($order_id);

                if ($markAsDelivered->isAllowed($order)) {
                    $markAsDelivered->execute($order);
                }

                if ($endWithDrawalPeriod->isAllowed($order)) {
                    $endWithDrawalPeriod->execute($order);
                }

                fn_change_order_status($order_id, OrderStatus::COMPLETED);
            },
            array_merge($vendor_type_order_ids, $client_type_order_ids)
        );
    }

    /**
     * Change le statut de la commande si nécessaire.
     *
     * Le statut de la commande parent n'est jamais modifié.
     * Ce n'est donc que le statut des commande enfant qui est modifié.
     */
    public static function automaticUpdate(int $order_id): bool
    {
        $order_info = container()
            ->get('marketplace.order.order_service')
            ->overrideLegacyOrder($order_id, false, true, true, true);

        //----------------------------------------------------
        // Evolution du status de la commande, si :
        // - la commande est en état 'En cours de traitement',
        // - et que tous les shipments sont déjà réalisés.
        //----------------------------------------------------
        if (is_order_status_equal_to($order_id, self::PROCESSING_SHIPPING) && !isset($order_info['shipping'][0]['need_shipment'])) {
            $order = container()->get('marketplace.order.order_service')->getOrder($order_id);
            $company = new Company($order_info['company_id']);

            //-------------------------------
            // C2C : pas de numéro de facture
            //-------------------------------
            if ($company->isPrivateIndividual()) {
                // Avancement du workflow
                if (container()->get('marketplace.order.action.mark_as_shipped')->isAllowed($order)) {
                    container()->get('marketplace.order.action.mark_as_shipped')->execute($order);
                }

                return static::changeOrderStatus($order_id, self::PROCESSED, ['C' => false]);
            //---------------------------------------------------
            // B2x : changement de statut autorisé si :
            // - si un numéro de facture est présent,
            // - ou la case "Ne pas créer de facture" est cochée.
            //---------------------------------------------------
            } elseif ($order_info['w_invoice_number'] || $order_info['do_not_create_invoice']) {
                // Avancement du workflow
                if ($order_info['w_invoice_number']) {
                    if (container()->get('marketplace.order.action.provide_invoice_number')->isAllowed($order)) {
                        container()->get('marketplace.order.action.provide_invoice_number')->execute($order, $order_info['w_invoice_number']);
                    }
                } elseif ($order_info['do_not_create_invoice']) {
                    if (container()->get('marketplace.order.action.declare_invoice_number_generated_elsewhere')->isAllowed($order)) {
                        container()->get('marketplace.order.action.declare_invoice_number_generated_elsewhere')->execute($order);
                    }
                }

                // Récupération des informations de livraison
                $shippingId = $order_info['shipping'][0]['shipping_id'] ?? null;

                // Si on est dans un mode de livraison en main propre
                if (!\is_null($shippingId) && \Wizacha\Shipping::isHandDelivery($shippingId)) {
                    // TODO: Faire avancer le workflow adéquat lorsqu'il existera

                    // On ne change rien pour l'instant, mais c'est là qu'il faudra le faire
                    // lorsque les nouveaux status de workflow seront disponibles.
                    // La notification est désactivé : le client ne reçoit pas d'email l'informant
                    // que la totalité de sa commande a été expédiée.
                    return static::changeOrderStatus($order_id, self::PROCESSED);
                }

                // Si on est dans un fonctionnement normal

                // Avancement du workflow
                if (container()->get('marketplace.order.action.mark_as_shipped')->isAllowed($order)) {
                    container()->get('marketplace.order.action.mark_as_shipped')->execute($order);
                }

                return static::changeOrderStatus($order_id, self::PROCESSED);
            }
        }

        return false;
    }

    /**
     * Created for tests
     */
    protected static function changeOrderStatus($id, $status, $force_notifications = [])
    {
        return fn_change_order_status($id, $status, '', $force_notifications);
    }
}
