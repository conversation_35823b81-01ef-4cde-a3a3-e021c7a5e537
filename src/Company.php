<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha;

use Wizacha\Marketplace\Company\CompanyStatus;
use Wizacha\Marketplace\Company\CompanyType;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Storage\StorageService;

/**
 * @deprecated
 * - For managing the companies, there is no alternative for now (TODO)
 * - For catalog: use Wizacha\Marketplace\Catalog\Company
 * @see \Wizacha\Marketplace\Catalog\Company
 */
class Company
{
    public const STATUS_NEW = CompanyStatus::NEW;
    public const STATUS_ENABLED = CompanyStatus::ENABLED;
    public const STATUS_DISABLED = CompanyStatus::DISABLED;
    public const STATUS_PENDING = CompanyStatus::PENDING;
    public const COMPANY_TYPE = 'company_type';
    public const SHIPPING_ID = 'shipping_id';

    public const MAX_LENGTH_TERMS = 16 * 1024 * 1024; // 16Mo - MEDIUMTEXT

    /**
     * @return array
     */
    public static function getAllType()
    {
        return [User::VENDOR_TYPE, User::CLIENT_TYPE];
    }

    public static function checkApplyForVendorAdditionalFields($fields = [], $files = [])
    {
        $mandatory_fields = [
            $fields['w_legal_status'],
            $fields['w_extras']['w_capital'],
            $fields['w_extras']['w_RCS'],
            $fields['w_siret_number'],
        ];
        $mandatory_files = [
            $files['w_ID_card'],
            $files['w_KBIS'],
            $files['w_RIB'],
        ];

        return static::_checkMandatoryFieldsAndFiles($mandatory_fields, $mandatory_files);
    }

    public static function checkApplyForC2CAdditionalFields($fields = [], $files = [])
    {
        $mandatory_fields = [];
        $mandatory_files = [
            $files['w_ID_card'],
            $files['w_RIB'],
        ];

        return static::_checkMandatoryFieldsAndFiles($mandatory_fields, $mandatory_files);
    }

    protected static function _checkMandatoryFieldsAndFiles($mandatory_fields, $mandatory_files)
    {
        foreach ($mandatory_fields as $m_field) {
            if (empty($m_field) && $m_field !== '0') {
                return false;
            }
        }

        foreach ($mandatory_files as $m_file) {
            if (empty($m_file) || $m_file['error'] !== UPLOAD_ERR_OK) {
                return false;
            }
        }
        return true;
    }


    /**
     * @param integer $company_id
     * @return bool
     */
    public static function hasShipping($company_id)
    {
        $info = fn_w_get_shipping_info_for_company($company_id);
        foreach ($info as $shipping) {
            if ($shipping['status'] == \Wizacha\Status::ENABLED) {
                return true;
            }
        }
        return false;
    }

    /**
     * Create a *free type* option for each company having none
     */
    public static function updateCompanyOptions()
    {
        //Retrieve root categories
        $root_categories = fn_get_subcategories(0);
        $root_categories = array_column($root_categories, 'category_id');
        $root_categories = implode(',', $root_categories);

        //Create missing options
        \Tygh\Database::query(
            "INSERT INTO `?:product_options` (`company_id`, `position`, `w_categories_path`)
            SELECT company_id, ?i AS position, ?s AS w_categories_path
            FROM `?:companies` AS c
            WHERE NOT EXISTS (SELECT * FROM `?:product_options` WHERE company_id=c.company_id AND `status`='A' AND position=?i)",
            Option::OPTION_POSITION,
            $root_categories,
            Option::OPTION_POSITION
        );

        foreach (array_keys(\Tygh\Languages\Languages::getAll()) as $locale) {
            \Tygh\Database::query(
                "INSERT INTO `?:product_options_descriptions` (`option_id`, `lang_code`, `option_name`)
                SELECT option_id, ?s AS lang_code, ?s AS option_name
                FROM `?:product_options` AS po
                WHERE NOT EXISTS (SELECT * FROM `?:product_options_descriptions` WHERE option_id=po.option_id AND lang_code = ?s) AND `status`='A' AND position=?i",
                $locale,
                Option::OPTION_NAME,
                $locale,
                Option::OPTION_POSITION
            );
        }
    }

    /**
     * @param array $company_ids
     * @return array
     */
    public static function getApiIds(array $company_ids)
    {
        if (empty($company_ids)) {
            return [];
        }

        return  \Tygh\Database::getHash(
            "SELECT user_id, company_id, email, api_key FROM ?:users
            WHERE user_type = 'V'
            AND company_id IN (?a)
            AND status = 'A'
            AND api_key <> ''
            GROUP BY company_id",
            'company_id',
            $company_ids
        );
    }

    /**
     * Update feeds if valid URL given, or delete if URL is empty
     *
     * @param array $feeds
     * @param int $company_id
     * @return array
     */
    public static function updateFeeds(array $feeds, $company_id)
    {
        $urlValidator = Registry::defaultInstance()->container->get('app.validator.url_validator');
        $results = [];
        $errors = [];
        foreach ($feeds as $datas) {
            if (empty($datas['URL'])) {
                $results[] = \Tygh\Database::query(
                    'DELETE FROM ?:w_automated_feeds WHERE company_id = ?i AND pattern_id = ?s LIMIT 1;',
                    $company_id,
                    $datas['pattern_id']
                );
            } elseif ($urlValidator->isUrlValid($datas['URL'])) {
                $results[] = \Tygh\Database::query(
                    'REPLACE ?:w_automated_feeds(`company_id`,`pattern_id`,`url`)
                    VALUES (?i, ?s, ?s)',
                    $company_id,
                    $datas['pattern_id'],
                    $datas['URL']
                );
            } else {
                $results[] = false;
                $errors[] = $datas['URL'];
            }
        }
        return [$results, $errors];
    }

    /**
     * Get feeds
     *
     * @param int $company_id
     * @return array
     */
    public static function getFeeds($company_id)
    {
        $feed_infos = \Tygh\Database::getArray(
            'SELECT pattern_id, url FROM ?:w_automated_feeds WHERE company_id = ?i',
            $company_id
        );

        foreach ($feed_infos as $k => $feed) {
            $feed_infos[$feed['pattern_id']]['URL'] = $feed['url'];
            unset($feed_infos[$k]);
        }

        return $feed_infos;
    }

    /**
     * Update companies information according to their administrators
     * @param integer $company_id Single company to update, or 0 if all companies must be treated
     */
    public static function synchronizeDataC2C($company_id = 0)
    {
        $cond = '';
        if ($company_id) {
            $cond = \Tygh\Database::quote('AND c.company_id = ?i', $company_id);
        }
        //Update type
        \Tygh\Database::query(
            "UPDATE ?:companies as c set `w_company_type` = IF(
              (SELECT true FROM ?:users as u WHERE u.company_id = c.company_id AND user_type = ?s LIMIT 1),
              ?s,
              ?s
            ) WHERE 1 $cond",
            \Wizacha\User::CLIENT_TYPE,
            \Wizacha\User::CLIENT_TYPE,
            \Wizacha\User::VENDOR_TYPE
        );
        //Update email
        \Tygh\Database::query(
            "UPDATE ?:companies AS c SET `email` = (SELECT email FROM ?:users AS u WHERE u.company_id = c.company_id LIMIT 1)
            WHERE w_company_type = ?s $cond",
            \Wizacha\User::CLIENT_TYPE
        );
    }

    /**
     * @param int $company_id
     * @param int $shipping_id
     * @return bool|mixed
     */
    public static function addShipping($company_id, $shipping_id)
    {
        $shippings = \Tygh\Database::getField("SELECT shippings FROM ?:companies WHERE company_id = ?i", $company_id);
        $shippings = empty($shippings) ? [] : explode(',', $shippings);

        if (\in_array($shipping_id, $shippings)) {
            return true;
        }

        $shippings[] = $shipping_id;
        $shippings   = implode(',', $shippings);

        if (\Tygh\Database::query("UPDATE ?:companies SET shippings = ?s WHERE company_id = ?i", $shippings, $company_id)) {
            \Wizacha\Events\Config::dispatch(
                \Wizacha\Shipping::EVENT_VENDOR_UPDATE,
                (new \Wizacha\Events\IterableEvent())->setElement([static::SHIPPING_ID => $shipping_id, 'company_id' => $company_id])
            );
            return true;
        }
        return false;
    }

    /**
     * @param array $company_data
     * @return bool
     */
    public static function isPrivateIndividual($company_data = [])
    {
        if (!empty($company_data['w_company_type'])) {
            return $company_data['w_company_type'] == (string) CompanyType::PRIVATE_INDIVIDUAL();
        }
        return false;
    }

    public static function isProfessional(array $company_data = []): bool
    {
        if (!empty($company_data['w_company_type'])) {
            return $company_data['w_company_type'] == (string) CompanyType::PROFESSIONAL();
        }

        return true;
    }

    /**
     * @param int $company_id
     * @param int $shipping_id
     * @return bool|mixed
     */
    public static function removeShipping($company_id, $shipping_id)
    {
        $shippings = \Tygh\Database::getField("SELECT shippings FROM ?:companies WHERE company_id = ?i", $company_id);
        $shippings = empty($shippings) ? [] : explode(',', $shippings);

        $key = array_search($shipping_id, $shippings);
        if (false === $key) {
            return true;
        }

        unset($shippings[$key]);
        $shippings = implode(',', $shippings);

        if (\Tygh\Database::query("UPDATE ?:companies SET shippings = ?s WHERE company_id = ?i", $shippings, $company_id)) {
            \Wizacha\Events\Config::dispatch(
                \Wizacha\Shipping::EVENT_VENDOR_UPDATE,
                (new \Wizacha\Events\IterableEvent())->setElement([static::SHIPPING_ID => $shipping_id, 'company_id' => $company_id])
            );
            return true;
        }
        return false;
    }

     /**
     * @param array $params
     * @return string
     */
    public static function getTypeCondition(array $params)
    {
        if (isset($params[static::COMPANY_TYPE]) && \is_array($params[static::COMPANY_TYPE]) && !empty($params[static::COMPANY_TYPE])) {
            $cond = array_map(
                function ($company_type) {
                    return \Tygh\Database::quote('companies.w_company_type = ?s', $company_type);
                },
                $params[static::COMPANY_TYPE]
            );
            return ' AND (' . implode(' OR ', $cond) . ')';
        }
        return '';
    }

    /**
     * @param string $area
     * @param array $session
     * @param Registry $registry
     * @return integer
     */
    public static function runtimeID($area, array $session, Registry $registry)
    {
        if ('A' == $area) {
            return $registry->get(['runtime', 'company_id']);
        } else {
            return !empty($session['auth']['company_id']) ? $session['auth']['company_id'] : 0;
        }
    }

    /**
     * @param integer $user_id
     * @param string  $company_name
     * @param array   $cscartData
     *
     * @return bool|integer
     */
    public static function newC2C(int $user_id, string $company_name = null, array $cscartData = [])
    {
        //create new company for this client
        $user_info = fn_get_user_info($user_id);

        $company_name = $company_name ?: substr(preg_replace("/[^A-Za-z0-9 ]/", '', $user_info['email']), 0, 3) . date('szY');

        $company_datas = [
            'w_company_type' => (string) CompanyType::PRIVATE_INDIVIDUAL(),
            'email' => $user_info['email'],
            'company' => $company_name,
            'status' => \Wizacha\Company::STATUS_ENABLED,
            'w_legal_status' => __('w_type_product_C'),
        ];
        $company_datas = array_merge($cscartData, $company_datas);

        //même si cette méthode est appelée à la fois en front et back on
        //utilise contentLocale puisque cette valeur fallback sur interfaceLocale
        //dans la partie front
        $company_id = fn_update_company(
            $company_datas,
            0,
            (string) GlobalState::contentLocale()
        );

        if (!$company_id) {
            return false;
        }

        if (empty($user_info['firstname']) === true) {
            \Wizacha\User::setFirstname($company_datas['legal_representative_firstname'], $user_id);
        }
        if (empty($user_info['lastname']) === true) {
            \Wizacha\User::setLastname($company_datas['legal_representative_lastname'], $user_id);
        }

        \Wizacha\User::setCompanyId($company_id, $user_id);

        //set default c2c shippings
        $shippings = \Wizacha\Shipping::getByType(\Wizacha\Shipping::TYPE_C2C);
        foreach ($shippings as $shipping) {
            \Wizacha\Company::addShipping($company_id, $shipping[static::SHIPPING_ID]);
        }


        return $company_id;
    }

    /**
     * @param string $pseudo
     * @param \Wizacha\Registry $registry
     * @return int
     * @throws \Exception
     */
    public static function getOrCreateC2C($pseudo = '', $registry = null)
    {
        if (!($company_id = static::runtimeID(AREA, $_SESSION, ($registry ?: \Wizacha\Registry::defaultInstance())))) {
            $_SESSION['auth']['company_id'] = $company_id = static::newC2C($_SESSION['auth']['user_id'], $pseudo);
            if (!$company_id) {
                throw new \Exception('Company ID not found.');
            }
        }
        return $company_id;
    }

    /**
     * Returns a PdoColumnIterator to iterate on all valid ids
     * @return \Wizacha\Core\Iterator\PdoColumnIterator
     */
    public static function frontIds(array $ids = [])
    {
        $sub_list = empty($ids) ? '' : \Tygh\Database::quote('AND company_id IN(?a)', $ids);
        return new \Wizacha\Core\Iterator\PdoColumnIterator(\Tygh\Database::prepare(
            'SELECT company_id FROM ?:companies WHERE status=?s ?p',
            \Wizacha\Status::ENABLED,
            $sub_list
        ));
    }

    /**
     * Returns the front-end url of a company
     * @param integer $company_id
     * @return string
     */
    public static function frontUrl($company_id)
    {
        return fn_url(
            'companies.view?company_id=' . $company_id,
            'C',
            'https'
        );
    }

    public static function hasFiles(int $company_id, StorageService $storage = null): bool
    {
        if (!$storage) {
            $storage = container()->get('Wizacha\Storage\VendorSubscriptionStorageService');
        }
         return !(empty($storage->getList($company_id)));
    }

    public static function getStatus(): array
    {
        return [
            static::STATUS_NEW,
            static::STATUS_ENABLED,
            static::STATUS_DISABLED,
            static::STATUS_PENDING,
        ];
    }

    public static function isValidStatus(string $statusToTest): bool
    {
        return \in_array($statusToTest, static::getStatus());
    }
}
