<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\CashconvertersBundle\Notification;

use Wizacha\Component\Dolist\DolistNotifier;
use W<PERSON>cha\Component\Dolist\DolistTemplateType;
use Wizacha\Component\Dolist\Entities\Comment;
use Wizacha\Component\Dolist\Entities\Option;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\PIM\Option\Event\OptionApproved;
use Wizacha\Marketplace\PIM\Option\Event\OptionRejected;

class OptionNotifier extends DolistNotifier
{
    public function optionApproved(OptionApproved $event)
    {
        $this->sendEmailToAddress(
            (new Company($event->getCompanyId()))->getEmail(),
            DolistTemplateType::OPTION_APPROVED(),
            [
                array_map(function (string $name): Option {
                    return new Option($name);
                }, $event->getOptions()),
                new Comment($event->getReason() ?? ''),
            ]
        );
    }

    public function optionRejected(OptionRejected $event)
    {
        $this->sendEmailToAddress(
            (new Company($event->getCompanyId()))->getEmail(),
            DolistTemplateType::OPTION_REJECTED(),
            [
                array_map(function (string $name): Option {
                    return new Option($name);
                }, $event->getOptions()),
                new Comment($event->getReason() ?? ''),
            ]
        );
    }
}
