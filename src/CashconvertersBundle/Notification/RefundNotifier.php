<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\CashconvertersBundle\Notification;

use Wizacha\Component\Dolist\DolistNotifier;
use Wizacha\Component\Dolist\DolistTemplateType;
use Wizacha\Component\Dolist\Entities\RefundTemplate;
use Wizacha\Marketplace\Order\Refund\Event\OrderRefundedEvent;

class RefundNotifier extends DolistNotifier
{
    /** @var string */
    protected $currencySign;

    public function orderRefundedEvent(OrderRefundedEvent $event): void
    {
        $order = $event->getOrder();
        $refund = $event->getRefund();

        $user = $this->container->get('marketplace.user.user_repository')->get($order->getUserId());

        $refundTemplate = new RefundTemplate(
            $order->getId(),
            $order->getCustomerTotal()->subtract($refund->getAmount())->getConvertedAmount(),
            $this->currencySign,
            $refund->getAmount()->getConvertedAmount()
        );

        $this->sendEmailToCustomer(
            $user,
            DolistTemplateType::ORDER_REFUNDED_CUSTOMER(),
            [
                $refundTemplate,
                $order,
                $user,
            ]
        );
    }

    public function setCurrencySign(string $currencySign): self
    {
        $this->currencySign = $currencySign;

        return $this;
    }
}
