<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\CashconvertersBundle\Notification;

use Wizacha\Component\Dolist\DolistNotifier;
use Wizacha\Component\Dolist\DolistTemplateType;
use Wizacha\Marketplace\Cms\Event\ContactFormSubmitted;

class ContactNotifier extends DolistNotifier
{
    public function contactFormSubmitted(ContactFormSubmitted $event)
    {
        $this->sendEmailToAddress(
            $event->getReceiver(),
            DolistTemplateType::CONTACT(),
            [
                $event,
            ]
        );
    }
}
