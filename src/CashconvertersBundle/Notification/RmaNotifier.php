<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\CashconvertersBundle\Notification;

use Wizacha\Component\Dolist\DolistNotifier;
use Wizacha\Component\Dolist\DolistTemplateType;
use Wizacha\Component\Dolist\Entities\Comment;
use Wizacha\Component\Dolist\Entities\OrderBillingAddress;
use Wizacha\Component\Dolist\Entities\OrderShippingAddress;
use Wizacha\Component\Dolist\Entities\ReturnAddress;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Order\AfterSales\Event\AfterSalesServiceRequested;
use Wizacha\Marketplace\Order\AfterSales\Event\LitigationCreated;
use Wizacha\Marketplace\Order\OrderItem;
use Wizacha\Marketplace\Order\OrderReturn\Event\RmaDeclined;
use Wizacha\Marketplace\Order\OrderReturn\Event\RmaReceived;
use Wizacha\Marketplace\Order\OrderReturn\Event\RmaRequested;

class RmaNotifier extends DolistNotifier
{
    public function rmaRequested(RmaRequested $event)
    {
        $return = $this->container->get('marketplace.order_return.service')->getReturn($event->getRmaId());
        $order = $this->container->get('marketplace.order.order_service')->getOrder($return->getOrderId());
        $rmaAddress = new ReturnAddress(fn_w_get_rma_address($order->getCompanyId()));

        $returnItems = $return->getItems();
        $company = new Company($order->getCompanyId());
        $user = $this->container->get('marketplace.user.user_repository')->get($order->getUserId());

        if ($event->getNotifyCompany()) {
            $this->sendEmailToAddress(
                $company->getEmail(),
                DolistTemplateType::VENDOR_RETURN_REQUESTED(),
                [
                    $return,
                    $returnItems,
                    $order,
                    $rmaAddress,
                    new Comment($return->getComment()),
                    $company,
                    $user,
                ]
            );
        }

        if ($event->getNotifyCustomer()) {
            $this->sendEmailToAddress(
                $order->getEmail(),
                DolistTemplateType::USER_RETURN_REQUESTED(),
                [
                    $return,
                    $returnItems,
                    $order,
                    $rmaAddress,
                    new Comment($return->getComment()),
                    $company,
                    $user,
                ]
            );
        }

        if ($event->getNotifyAdmin()) {
            $this->sendEmailToAdmin(
                DolistTemplateType::ADMIN_RETURN_REQUESTED(),
                [
                    $return,
                    $returnItems,
                    $order,
                    $rmaAddress,
                    new Comment($return->getComment()),
                    $company,
                    $user,
                ]
            );
        }
    }

    public function rmaReceived(RmaReceived $event)
    {
        $return = $this->container->get('marketplace.order_return.service')->getReturn($event->getRmaId());
        $order = $this->container->get('marketplace.order.order_service')->getOrder($return->getOrderId());
        $rmaAddress = new ReturnAddress(fn_w_get_rma_address($order->getCompanyId()));

        $returnItems = $return->getItems();
        $company = new Company($order->getCompanyId());
        $user = $this->container->get('marketplace.user.user_repository')->get($order->getUserId());

        if ($event->getNotifyCompany()) {
            $this->sendEmailToAddress(
                $company->getEmail(),
                DolistTemplateType::VENDOR_RETURN_RECEIVED(),
                [
                    $return,
                    $returnItems,
                    $order,
                    $rmaAddress,
                    new Comment($return->getComment()),
                    $company,
                    $user,
                ]
            );
        }

        if ($event->getNotifyCustomer()) {
            $this->sendEmailToAddress(
                $order->getEmail(),
                DolistTemplateType::USER_RETURN_RECEIVED(),
                [
                    $return,
                    $returnItems,
                    $order,
                    $rmaAddress,
                    new Comment($return->getComment()),
                    $company,
                    $user,
                ]
            );
        }

        if ($event->getNotifyAdmin()) {
            $this->sendEmailToAdmin(
                DolistTemplateType::ADMIN_RETURN_RECEIVED(),
                [
                    $return,
                    $returnItems,
                    $order,
                    $rmaAddress,
                    new Comment($return->getComment()),
                    $company,
                    $user,
                ]
            );
        }
    }

    public function rmaDeclined(RmaDeclined $event)
    {
        $return = $this->container->get('marketplace.order_return.service')->getReturn($event->getRmaId());
        $order = $this->container->get('marketplace.order.order_service')->getOrder($return->getOrderId());
        $rmaAddress = new ReturnAddress(fn_w_get_rma_address($order->getCompanyId()));

        $returnItems = $return->getItems();
        $company = new Company($order->getCompanyId());
        $user = $this->container->get('marketplace.user.user_repository')->get($order->getUserId());

        if ($event->getNotifyCompany()) {
            $this->sendEmailToAddress(
                $company->getEmail(),
                DolistTemplateType::VENDOR_RETURN_DECLINED(),
                [
                    $return,
                    $returnItems,
                    $order,
                    $rmaAddress,
                    new Comment($return->getComment()),
                    $company,
                    $user,
                ]
            );
        }

        if ($event->getNotifyCustomer()) {
            $this->sendEmailToAddress(
                $order->getEmail(),
                DolistTemplateType::USER_RETURN_DECLINED(),
                [
                    $return,
                    $returnItems,
                    $order,
                    $rmaAddress,
                    new Comment($return->getComment()),
                    $company,
                    $user,
                ]
            );
        }

        if ($event->getNotifyAdmin()) {
            $this->sendEmailToAdmin(
                DolistTemplateType::ADMIN_RETURN_DECLINED(),
                [
                    $return,
                    $returnItems,
                    $order,
                    $rmaAddress,
                    new Comment($return->getComment()),
                    $company,
                    $user,
                ]
            );
        }
    }

    public function litigationCreated(LitigationCreated $event)
    {
    }

    public function afterSalesServiceRequested(AfterSalesServiceRequested $event)
    {
        $order = $this->container->get('marketplace.order.order_service')->getOrder($event->getOrderId());

        /** @var OrderItem[] $items */
        $items = [];
        $ids = array_flip($event->getDeclinations());
        foreach ($order->getItems() as $item) {
            if (isset($ids[$item->getDeclinationId()])) {
                $items[] = $item;
            }
        }

        $this->sendEmailToAddress(
            (new Company($order->getCompanyId()))->getEmail(),
            DolistTemplateType::AFTER_SALES_SERVICE_REQUESTED(),
            [
                $order,
                $items,
                OrderBillingAddress::fromOrderAddress($order->getBillingAddress()),
                OrderShippingAddress::fromOrderAddress($order->getShippingAddress()),
                new Comment($event->getComment()),
            ]
        );
    }
}
