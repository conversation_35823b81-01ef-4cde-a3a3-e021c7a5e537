<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\CashconvertersBundle\Notification;

use Wizacha\Component\Dolist\DolistNotifier;
use Wizacha\Component\Dolist\DolistTemplateType;
use Wizacha\Component\Dolist\Entities\PasswordRecoveryLink;
use Wizacha\Marketplace\User\Event\UserAskedToRecoverPassword;
use Wizacha\Marketplace\User\Event\UserProfileActivated;
use Wizacha\Marketplace\User\Event\UserProfileActivationRequested;
use Wizacha\Marketplace\User\Event\UserProfileCreated;
use Wizacha\Marketplace\User\Event\UserProfileUpdated;

class UserNotifier extends DolistNotifier
{
    public function userAskedToRecoverPassword(UserAskedToRecoverPassword $event)
    {
        $this->sendEmailToCustomer($event->getUser(), DolistTemplateType::PASSWORD_RECOVER(), [
            $event->getUser(),
            new PasswordRecoveryLink($event->getToken()),
        ]);
    }

    public function userProfileActivated(UserProfileActivated $event)
    {
        $this->sendEmailToCustomer($event->getUser(), DolistTemplateType::USER_PROFILE_ACTIVATED(), [
            $event->getUser(),
        ]);
    }

    public function userProfileCreated(UserProfileCreated $event)
    {
        $this->sendEmailToCustomer($event->getUser(), DolistTemplateType::USER_PROFILE_CREATED(), [
            $event->getUser(),
        ]);
    }

    public function userProfileUpdated(UserProfileUpdated $event)
    {
        $this->sendEmailToCustomer($event->getUser(), DolistTemplateType::USER_PROFILE_UPDATED(), [
            $event->getUser(),
        ]);
    }

    public function userProfileActivationRequested(UserProfileActivationRequested $event)
    {
    }
}
