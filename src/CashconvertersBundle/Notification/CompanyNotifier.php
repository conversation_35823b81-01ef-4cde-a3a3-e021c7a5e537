<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\CashconvertersBundle\Notification;

use Wizacha\Component\Dolist\DolistNotifier;
use Wizacha\Component\Dolist\DolistTemplateType;
use Wizacha\Component\Dolist\Entities\Comment;
use Wizacha\Marketplace\Company\Event\C2cCompanyApplied;
use Wizacha\Marketplace\Company\Event\CompanyApplied;
use Wizacha\Marketplace\Company\Event\CompanyDisabled;
use Wizacha\Marketplace\Company\Event\CompanyRejected;
use Wizacha\Marketplace\Entities\Company;

class CompanyNotifier extends DolistNotifier
{
    public function companyApplied(CompanyApplied $event)
    {
        $company = new Company($event->getCompanyData()['company_id']);

        $this->sendEmailToAdmin(DolistTemplateType::ADMIN_COMPANY_APPLIED(), [
            $company,
            new Comment($event->getMessageToAdmin()),
        ]);

        $this->sendEmailToAddress($company->getEmail(), DolistTemplateType::COMPANY_APPLIED(), [
            $company,
        ]);
    }

    public function c2cCompanyApplied(C2cCompanyApplied $event)
    {
        // Pas de C2C pour cashconverters
    }

    public function companyRejected(CompanyRejected $event)
    {
        $company = new Company($event->getCompanyData()['company_id']);

        $this->sendEmailToAddress($company->getEmail(), DolistTemplateType::COMPANY_REJECTED(), [
            $company,
            new Comment($event->getReason()),
        ]);
    }

    public function companyDisabled(CompanyDisabled $event)
    {
        $company = new Company($event->getCompanyData()['company_id']);

        $this->sendEmailToAddress($company->getEmail(), DolistTemplateType::COMPANY_DISABLED(), [
            $company,
            new Comment($event->getReason()),
        ]);
    }
}
