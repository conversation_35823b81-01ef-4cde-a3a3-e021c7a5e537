<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\CashconvertersBundle\Notification;

use Wizacha\Component\Dolist\DolistNotifier;
use Wizacha\Component\Dolist\DolistTemplateType;
use Wizacha\Component\Dolist\Entities\ProductReport;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\PIM\Moderation\ProductWasReported;

class ModerationNotifier extends DolistNotifier
{
    public function productWasReported(ProductWasReported $event)
    {
        $this->sendEmailToAdmin(
            DolistTemplateType::PRODUCT_REPORTED(),
            [
                new ProductReport(
                    $event->getVisitorName(),
                    $event->getVisitorEmail(),
                    $event->getVisitorMessage(),
                    fn_url('products.view?product_id=' . $event->getProductId(), 'C'),
                    fn_url('products.update?product_id=' . $event->getProductId(), 'A', 'https', (string) GlobalState::interfaceLocale(), true)
                ),
            ]
        );
    }
}
