<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\CashconvertersBundle\Notification;

use Wizacha\Component\Dolist\DolistNotifier;
use Wizacha\Component\Dolist\DolistTemplateType;
use Wizacha\Component\Dolist\Entities\Comment;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\PIM\Product\Event\ProductApproved;
use Wizacha\Marketplace\PIM\Product\Event\ProductChangesRequested;
use Wizacha\Marketplace\PIM\Product\Event\ProductRejected;
use Wizacha\Marketplace\PIM\Product\Event\ProductStockThresholdReached;

class ProductNotifier extends DolistNotifier
{
    // TODO: use PIM product service to always have the product! When using catalog service, we can have null if product is not viewable!

    public function productApproved(ProductApproved $event)
    {
        $productService = $this->container->get('marketplace.product.productservice');

        $products = array_filter(array_map(function ($productId) use ($productService) {
            return $productService->getProduct($productId);
        }, $event->getProducts()));

        $company = new Company($event->getCompanyId());

        $this->sendEmailToAddress(
            $company->getEmail(),
            DolistTemplateType::PRODUCT_APPROVED(),
            [
                $products,
                new Comment($event->getReason() ?? ''),
            ]
        );
    }

    public function productRejected(ProductRejected $event)
    {
        $productService = $this->container->get('marketplace.product.productservice');

        $products = array_filter(array_map(function ($productId) use ($productService) {
            return $productService->getProduct($productId);
        }, $event->getProducts()));

        $company = new Company($event->getCompanyId());

        $this->sendEmailToAddress(
            $company->getEmail(),
            DolistTemplateType::PRODUCT_REJECTED(),
            [
                $products,
                new Comment($event->getReason() ?? ''),
            ]
        );
    }

    public function productChangesRequested(ProductChangesRequested $event)
    {
        $productService = $this->container->get('marketplace.product.productservice');

        $products = array_filter(array_map(function ($productId) use ($productService) {
            return $productService->getProduct($productId);
        }, $event->getProducts()));

        $company = new Company($event->getCompanyId());

        $this->sendEmailToAddress(
            $company->getEmail(),
            DolistTemplateType::PRODUCT_REQUIRES_CHANGES(),
            [
                $products,
                new Comment($event->getReason() ?? ''),
            ]
        );
    }

    public function productStockThresholdReached(ProductStockThresholdReached $event)
    {
    }
}
