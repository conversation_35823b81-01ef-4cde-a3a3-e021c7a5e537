<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\CashconvertersBundle\Notification;

use Wizacha\Component\Dolist\DolistNotifier;
use Wizacha\Component\Dolist\DolistTemplateType;
use Wizacha\Component\Dolist\Entities\DiscussionLink;
use Wizacha\Discuss\Event\DiscussMessagePosted;
use Wizacha\Marketplace\User\UserType;
use Symfony\Component\Routing\RouterInterface;

class DiscussNotifier extends DolistNotifier
{
    /**
     * @var RouterInterface
     */
    private $router;

    public function discussMessagePosted(DiscussMessagePosted $event)
    {
        $discussionId = $event->getMessage()->getDiscussion()->getId();

        $area = $event->getUser()->getUserType() === UserType::VENDOR ? 'V' : 'C';
        if ($area === 'C') {
            $link = fn_url('discuss.view?discussion_id=' . $discussionId, $area, 'https');
        } else {
            $oldBaseUrl = $this->router->getContext()->getBaseUrl();
            $this->router->getContext()->setBaseUrl('/' . container()->getParameter('entrypoint.vendor'));
            $link = $this->router->generate('admin_Discuss_view', ['discussionId' => $discussionId], RouterInterface::ABSOLUTE_URL);
            $this->router->getContext()->setBaseUrl($oldBaseUrl);
        }

        $this->sendEmailToCustomer($event->getUser(), DolistTemplateType::DISCUSSION_RECEIVED(), [
            $event->getUser(),
            new DiscussionLink($link),
        ]);
    }

    public function setRouter(RouterInterface $router)
    {
        $this->router = $router;
    }
}
