<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\CashconvertersBundle\Notification;

use Wizacha\AppBundle\Notification\DTO\InternalTransferErrorDTO;
use Wizacha\AppBundle\Notification\OrderNotifierInterface;
use Wizacha\Component\Dolist\DolistNotifier;
use Wizacha\Component\Dolist\DolistTemplateType;
use Wizacha\Component\Dolist\Entities\OrderBillingAddress;
use Wizacha\Component\Dolist\Entities\OrderShippingAddress;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Order\Event\OrderCodeFailed;
use Wizacha\Marketplace\Order\Event\OrderCodeGenerated;
use Wizacha\Marketplace\Payment\Event\DispatchFundsFailedEvent;
use Wizacha\Marketplace\Order\Event\OrderStatusChanged;
use Wizacha\Marketplace\Order\Event\ShipmentCreated;
use Wizacha\Marketplace\Payment\Event\BankwireFailedToRetrieveOrderEvent;
use Wizacha\Marketplace\Payment\Event\BankwireNotificationFailedStatusEvent;
use Wizacha\Marketplace\Payment\Event\BankwirePaymentEvent;
use Wizacha\Marketplace\Payment\Event\HipayTransactionChargedbackEvent;
use Wizacha\Marketplace\Tax\Event\MissingTaxConfigurationEvent;

class OrderNotifier extends DolistNotifier implements OrderNotifierInterface
{
    /**
     * @var bool $notifyShipmentCreated
     */
    private $notifyShipmentCreated;

    public function orderCodeGenerated(OrderCodeGenerated $event)
    {
        $order = $event->getOrder();
        $company = new Company($order->getCompanyId());
        $user = $this->container->get('marketplace.user.user_repository')->get($order->getUserId());

        $this->sendEmailToAddress(
            $order->getEmail(),
            DolistTemplateType::ORDER_CODE_GENERATED(),
            [
                $event->getCode(),
                $order,
                $order->getItems(),
                OrderBillingAddress::fromOrderAddress($order->getBillingAddress()),
                OrderShippingAddress::fromOrderAddress($order->getShippingAddress()),
                $company,
                $user,
            ]
        );
    }

    public function orderCodeFailed(OrderCodeFailed $event)
    {
        // Pas de code pour cashconverters
    }

    public function orderStatusChanged(OrderStatusChanged $event)
    {
        $order = $this->container->get('marketplace.order.order_service')->getOrder((int) $event->getOrderInfo()['order_id']);
        $company = new Company($order->getCompanyId());
        $user = $this->container->get('marketplace.user.user_repository')->get($order->getUserId());

        if ($event->getNotifyCustomer()) {
            $this->sendEmailToAddress(
                $order->getEmail(),
                DolistTemplateType::ORDER_STATUS(),
                [
                    $order,
                    $order->getItems(),
                    OrderBillingAddress::fromOrderAddress($order->getBillingAddress()),
                    OrderShippingAddress::fromOrderAddress($order->getShippingAddress()),
                    $company,
                    $user,
                ]
            );
        }

        if ($event->getNotifyCompany()) {
            $this->sendEmailToAddress(
                (new Company($order->getCompanyId()))->getEmail(),
                DolistTemplateType::COMPANY_ORDER_STATUS(),
                [
                    $order,
                    $order->getItems(),
                    OrderBillingAddress::fromOrderAddress($order->getBillingAddress()),
                    OrderShippingAddress::fromOrderAddress($order->getShippingAddress()),
                    $company,
                    $user,
                ]
            );
        }

        if ($event->getNotifyAdmin()) {
            $this->sendEmailToAdmin(
                DolistTemplateType::ADMIN_ORDER_STATUS(),
                [
                    $order,
                    $order->getItems(),
                    OrderBillingAddress::fromOrderAddress($order->getBillingAddress()),
                    OrderShippingAddress::fromOrderAddress($order->getShippingAddress()),
                    $company,
                    $user,
                ]
            );
        }
    }

    public function dispatchFundsFailedEvent(DispatchFundsFailedEvent $event): OrderNotifierInterface
    {
        // No code for cash converter

        return $this;
    }

    public function missingTaxConfigurationEvent(MissingTaxConfigurationEvent $event): OrderNotifierInterface
    {
        // No code for cash converter

        return $this;
    }

    public function shipmentCreated(ShipmentCreated $event)
    {
        if (false === $this->notifyShipmentCreated) {
            return;
        }

        $order = $event->getShipment()->getOrder();
        $company = new Company($order->getCompanyId());
        $user = $this->container->get('marketplace.user.user_repository')->get($order->getUserId());

        $this->sendEmailToAddress(
            $order->getEmail(),
            DolistTemplateType::SHIPMENT_CREATED(),
            [
                $event->getShipment(),
                $order,
                $event->getShipment()->getItems(),
                OrderBillingAddress::fromOrderAddress($order->getBillingAddress()),
                OrderShippingAddress::fromOrderAddress($order->getShippingAddress()),
                $company,
                $user,
            ]
        );
    }

    public function setNotifyShipmentCreated(bool $notify)
    {
        $this->notifyShipmentCreated = $notify;
    }

    public function bankwireNotificationFailedStatusEvent(BankwireNotificationFailedStatusEvent $event): OrderNotifierInterface
    {
        // No code for cash converter

        return $this;
    }

    public function bankwireFailedToRetrieveOrderEvent(BankwireFailedToRetrieveOrderEvent $event): OrderNotifierInterface
    {
        // No code for cash converter

        return $this;
    }

    public function internalTransferError(InternalTransferErrorDTO $error): OrderNotifierInterface
    {
        // No code for cash converter

        return $this;
    }

    public function hipayTransactionChargedBackEvent(HipayTransactionChargedbackEvent $event): OrderNotifierInterface
    {
        // No code for cash converter

        return $this;
    }

    public function bankwirePaymentEvent(BankwirePaymentEvent $event)
    {
        // No code for cash converter

        return $this;
    }
}
