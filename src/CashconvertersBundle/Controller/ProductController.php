<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\CashconvertersBundle\Controller;

use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Wizacha\Events\IterableEvent;
use Wizacha\Product;

class ProductController extends \Wizacha\AppBundle\Controller\ProductController
{
    public function viewProductAction($productId)
    {
        // product read model
        $productReadModel = $this->get('marketplace.product.productservice')->getProduct($productId);
        if (!$productReadModel) {
            throw new NotFoundHttpException();
        }

        // declinations
        $declinations = $this->getDeclinations($productReadModel, true);

        $event = IterableEvent::fromElement($productId);
        $this->container->get('event_dispatcher')->dispatch($event, Product::EVENT_VIEWED);

        $recommendedProducts = $this->getProductsOfTheSameCompany($productId, 10);

        return $this->render('@App/frontend/product/view.html.twig', [
            'productReadModel' => $productReadModel,
            'declinations' => $declinations,
            'recommendedProducts' => $recommendedProducts,
        ]);
    }
}
