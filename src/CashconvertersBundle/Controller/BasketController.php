<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\CashconvertersBundle\Controller;

use Wizacha\AppBundle\Controller\BaseController;
use Wizacha\Shipping;

class BasketController extends BaseController
{
    public function viewAction()
    {
        $userService = $this->get('marketplace.user_service');
        $basket = $userService->getUserBasket();
        $clickAndCollectShippingId = $this->getParameter('click_and_collect_shipping_id');

        if ($basket) {
            $readModel = $basket->getReadModel();
            /*
             * Aucun mode de livraison n'a été sélectionné volontairement,
             * il faut donc assigner le mode de livraison par défaut à chaque shipping groups,
             * en vérifiant bien que ce n'est pas une mode compatible avec les pickup points.
             *
             * Ou alors, on arrive sur le panier alors qu'on est déjà en mode pickup points,
             * donc on reset tout, pour avoir un panier consistant et logique.
             */
            if (empty($basket->getSelectedShippings()) || $readModel->isPickupPointsShipping()) {
                foreach ($readModel->getGroups() as $companyGroup) {
                    foreach ($companyGroup->getShippingGroups() as $shippingGroup) {
                        foreach ($shippingGroup->getShippings() as $shipping) {
                            if ($shipping->getId() == $this->getParameter('click_and_collect_shipping_id')) {
                                $this->get('marketplace.basket.domain_service')->selectShippingForGroup(
                                    $readModel->getId(),
                                    $shippingGroup->getGroupId(),
                                    $shipping->getId()
                                );

                                break;
                            }
                        }
                    }
                }
            }
        }


        // Quoi qu'il arrive on (re)charge le readmodel
        $basket = $userService->getUserBasket();
        if ($basket) {
            $basket = $basket->getReadModel();
        }

        return $this->render('@App/frontend/basket/basket.html.twig', [
            'basket' => $basket,
            'clickAndCollectShippingId' => $clickAndCollectShippingId,
        ]);
    }
}
