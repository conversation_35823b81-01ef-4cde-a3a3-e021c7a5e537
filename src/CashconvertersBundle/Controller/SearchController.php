<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\CashconvertersBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Wizacha\AppBundle\Controller\BaseController;
use Wizacha\CashconvertersBundle\Service\StoreService;
use Wizacha\Category;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Entities\ThreadPost;
use GuzzleHttp\Exception\GuzzleException;
use Wizacha\Search\Pagination;
use Wizacha\Search\Product\Sorting;
use Wizacha\Search\SortDirection;

class SearchController extends BaseController
{
    public function searchAction(Request $request): Response
    {
        $catalogCategories = $this->get('marketplace.catalog.category_service')->getTree();
        $categories = $this->serializeCategories($catalogCategories);

        $categoryId = $request->query->getInt('categories');
        $filters = [];
        if ($categoryId) {
            $filters['categories'] = $categoryId;
        }

        return $this->render('@App/frontend/search/search.html.twig', [
            'searchQuery' => $request->query->get('q'),
            'filters' => $filters,
            'currentCategory' => $categoryId ? new Category($categoryId) : null,
            'categories' => $categories,
        ]);
    }

    public function categoryAction($categoryId): Response
    {
        $catalogCategories = $this->get('marketplace.catalog.category_service')->getTree();
        $categories = $this->serializeCategories($catalogCategories);

        $category = new Category($categoryId);

        return $this->render('@App/frontend/search/category.html.twig', [
            'currentCategory' => $category,
            'categories' => $categories,
        ]);
    }

    public function companyAction($companyId): Response
    {
        $companyId = (int) $companyId;

        if (!\Wizacha\Company::frontIds([$companyId])->valid()) {
            throw new NotFoundHttpException();
        }

        // Récupération de l'utilisateur courant
        $user = $this->getUser();

        // Génération des flags d'affichage
        $hasOrder = null;
        if (!\is_null($user)) {
            $hasOrder = $this->get('marketplace.order.order_service')->isUserHaveOrderFromCompany($user->getId(), $companyId);
        }

        // get company opening hours
        $company = $this->get('marketplace.catalog.company_service')->getCompany($companyId);
        /** @var StoreService $storeService */
        $storeService = $this->get('app.store_service');

        $salesOpeningHours = [];
        $purchaseOpeningHours = [];
        $isPurchaseCurrentlyOpen = false;
        $isSalesCurrentlyOpen = false;
        try {
            $salesOpeningHours = $storeService->getSalesOpeningHours($companyId);
            $purchaseOpeningHours = $storeService->getPurchaseOpeningHours($companyId);
            $isPurchaseCurrentlyOpen = $storeService->isPurchaseCurrentlyOpen($companyId);
            $isSalesCurrentlyOpen = $storeService->isSalesCurrentlyOpen($companyId);
        } catch (GuzzleException $e) {
        }

        // format reviews for ease of use in front template
        $pimCompany = new Company($companyId);
        $reviews = array_map(function (ThreadPost $review) {
            return [
                "name" => $review->getName(),
                "ratingValue" => $review->getRatingValue(),
                "datetime" => $review->getDatetime(),
                "message" => $review->getMessage(),
            ];
        }, $pimCompany->getReviews());

        $latestProducts = $this->get('marketplace.search.product_index')->search('', new Pagination(1, 10), [
            'companies' => $companyId,
        ], Sorting::creationDate(SortDirection::DESC), null, ['noFacets' => true])->getProducts();

        return $this->render('@App/frontend/search/company/company.html.twig', [
            'company' => $company,
            'thread' => $pimCompany->getThread(),
            'reviews' => $reviews,
            'averageRating' => $pimCompany->getAverageRating(),
            'user' => $user,
            'hasOrder' => $hasOrder,
            'salesOpeningHours' => $salesOpeningHours,
            'purchaseOpeningHours' => $purchaseOpeningHours,
            'isPurchaseCurrentlyOpen' => $isPurchaseCurrentlyOpen,
            'isSalesCurrentlyOpen' => $isSalesCurrentlyOpen,
            'currentDay' => (int) date('N') - 1,
            'latestProducts' => $latestProducts,
        ]);
    }

    public function attributeAction($variantId): Response
    {
        $catalogCategories = $this->get('marketplace.catalog.category_service')->getTree();
        $categories = $this->serializeCategories($catalogCategories);

        $variantInfo = fn_get_product_feature_variant($variantId);
        if (!$variantInfo) {
            throw new NotFoundHttpException();
        }

        return $this->render('@App/frontend/search/attribute.html.twig', [
            'variantInfo' => $variantInfo,
            'categories' => $categories,
        ]);
    }

    private function serializeCategories($categoryNode)
    {
        return array_map(function ($category) {
            /** @var \Wizacha\Marketplace\Catalog\Category\Category $categoryObj */
            $categoryObj = $category['category'];
            return [
                'category' => [
                    'id' => $categoryObj->getId(),
                    'name' => $categoryObj->getName(),
                    'slug' => $categoryObj->getSlug(),
                    'productCount' => $categoryObj->getProductCount(),
                ],
                'children' => $this->serializeCategories($category['children'])
            ];
        }, $categoryNode);
    }
}
