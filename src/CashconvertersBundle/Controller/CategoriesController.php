<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\CashconvertersBundle\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Response;
use Wizacha\Marketplace\Catalog\Category\Category;
use Wizacha\Search\Pagination;
use Wizacha\Search\Product\Sorting;
use Wizacha\Search\SortDirection;

class CategoriesController extends Controller
{
    use CategoryProductBlocks;

    public function displayCategoriesAction(): Response
    {
        $productIndex = $this->get('marketplace.search.product_index');
        $sortByDate = Sorting::creationDate(SortDirection::DESC);
        $latestProducts = $productIndex->search('', new Pagination(1, 50), [], $sortByDate)->getProducts();
        $tree = $this->get('marketplace.catalog.category_service')->getTree();
        $categoriesList = array_map(function (array $branch) {
            return $branch['category'];
        }, $tree);
        $categories = $this->addCategoryProductsBlocks($categoriesList);
        $categoriesData = [];
        foreach ($categories as $categoryData) {
            /** @var Category $category */
            $category = $categoryData['category'];
            foreach ($tree as $branch) {
                /** @var Category $branchCategory */
                $branchCategory = $branch['category'];
                if ($branchCategory->getId() === $category->getId()) {
                    $categoryData['children'] = $branch['children'];
                }
            }
            $categoriesData[] = $categoryData;
        }

        return $this->render('@Cashconverters/frontend/categories/categories.html.twig', [
            'latestProducts' => $latestProducts,
            'categories' => $categoriesData,
        ]);
    }
}
