<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\CashconvertersBundle\Controller;

use Symfony\Component\HttpFoundation\Response;
use Wizacha\AppBundle\Controller\BaseController;
use Wizacha\Marketplace\Catalog\Category\Category;
use Wizacha\Search\Pagination;
use Wizacha\Search\Product\Sorting;

class HomeController extends BaseController
{
    use CategoryProductBlocks;

    public function homeAction(): Response
    {
        // latest products
        $search = $this->get('marketplace.search.product_index');

        $sorting = Sorting::fromString('createdAt', 'desc');

        $latestProducts = $this->get('marketplace.cache')->getOrSet(
            function () use ($search, $sorting) {
                return $search->search('', new Pagination(1, 50), [], $sorting)->getProducts();
            },
            'cash_home_50_products',
            60
        );

        return $this->render('@Cashconverters/frontend/home/<USER>', [
            'desktopBanners' => $this->get('marketplace.banner.banner_service')->getHomepageActiveBanners(),
            'latestProducts' => $latestProducts,
            'topCategories' => $this->addCategoryProductsBlocks($this->getTopCategories()),
        ]);
    }

    /**
     * Retourne les catégories à mettre en avant sur la home.
     *
     * On retourne les 6 premières "RootCategories" triées par position
     *
     * @return Category[]
     */
    private function getTopCategories(): array
    {
        $rootCategories = $this->get('marketplace.catalog.category_service')->getRootCategories();

        return \array_slice($rootCategories, 0, 6);
    }
}
