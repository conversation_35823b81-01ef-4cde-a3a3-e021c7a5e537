<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\CashconvertersBundle\Controller;

use Wizacha\Marketplace\Catalog\Category\Category;
use Wizacha\Search\Pagination;
use Wizacha\Search\Product\ProductSearchResult;

trait CategoryProductBlocks
{
    /**
     * @param Category[] $categories
     */
    private function addCategoryProductsBlocks(array $categories): array
    {
        $cacheKey = implode('_', array_map(function (Category $category) {
            return $category->getId();
        }, $categories));

        $cache = $this->get('marketplace.cache');
        if ($cachedData = $cache->get('category_product_block_' . $cacheKey)) {
            return reset($cachedData);
        }

        $result = [];
        foreach ($categories as $category) {
            /** @var ProductSearchResult $searchResults */
            $searchResults = $this->get('marketplace.search.product_index')->search('', new Pagination(1, 6), [
                'categories' => $category->getId(),
            ]);
            $result[] = [
                'category' => $category,
                'products' => $searchResults->getProducts(),
                'totalProductCount' => $searchResults->getTotalProductCount(),
            ];
        }

        $cache->set('category_product_block_' . $cacheKey, $result, 60); // 1 min TTL

        return $result;
    }
}
