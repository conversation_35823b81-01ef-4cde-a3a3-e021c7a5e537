<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\CashconvertersBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Wizacha\AppBundle\Controller\BaseController;
use Wizacha\Marketplace\Cms\Event\ContactFormSubmitted;

class ContactController extends BaseController
{
    public function emailAction(Request $request): Response
    {
        if ($request->isMethod('POST')) {
            $sender = $request->get('sender');
            $content = $request->get('content');
            $username = "{$request->get('firstname')} {$request->get('lastname')}";
            $phone = $request->get('phone');
            $submittedToken = $request->get('csrf_token');

            if (!$sender || !$content) {
                fn_set_notification('E', __('error'), __('send_contact_missing_field'));
            } elseif (!$this->isCsrfTokenValid('contact_form_token', $submittedToken)) {
                fn_set_notification('E', __('error'), __('invalid_csrf_token'));
            } elseif (!$this->getUser() && !$this->get('app.captcha')->isHuman($request->request->all())) {
                fn_set_notification('W', __('warning'), __('error_captcha_required'));
            } else {
                $eventDispatcher = $this->get('event_dispatcher');
                $adminCompany = $this->get('marketplace.admin_company');

                //Reconstruction du formulaire comme s'il venait d'une page CMS pour utiliser le même système de notification
                $event = new ContactFormSubmitted(
                    __('subject_contact_by_client'),
                    $adminCompany->getSiteAdministratorEmail(),
                    [
                        [
                            'element_type' => FORM_EMAIL,
                            'value' => $sender,
                            'description' => __('sender'),
                        ],
                        [
                            'element_type' => FORM_INPUT,
                            'value' => 'contact',
                            'description' => __('contact_type'),
                        ],
                        [
                            'element_type' => FORM_INPUT,
                            'value' => $username,
                            'description' => __('username'),
                        ],
                        [
                            'element_type' => FORM_PHONE,
                            'value' => $phone,
                            'description' => __('phone_number'),
                        ],
                        [
                            'element_type' => FORM_TEXTAREA,
                            'value' => $content,
                            'description' => __('body'),
                        ],
                    ]
                );

                $eventDispatcher->dispatch($event, ContactFormSubmitted::class);
                fn_set_notification('N', __('notice'), __('w_message_send'));
            }

            return $this->redirect($request->headers->get('referer') ?: $this->generateUrl('home'));
        }

        return $this->render('@App/frontend/cms/contact_form.html.twig');
    }
}
