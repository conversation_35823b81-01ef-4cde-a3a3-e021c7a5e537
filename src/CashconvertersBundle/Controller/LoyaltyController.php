<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\CashconvertersBundle\Controller;

use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use Symfony\Component\HttpFoundation\Request;
use Wizacha\AppBundle\Controller\BaseController;
use Wizacha\CashconvertersBundle\Service\LoyaltyService;

class LoyaltyController extends BaseController
{
    public function viewAction(Request $request)
    {
        if (!$this->getUser()) {
            return $this->redirectToRoute('home');
        }

        $user = $this->get('marketplace.user.user_repository')->get($this->getUser()->getId());
        /** @var LoyaltyService $loyaltyService */
        $loyaltyService = $this->get('app.loyalty_service');

        if ($request->isMethod('POST')) {
            $inputLoyaltyIdentifier = $request->request->get('loyalty_number');

            if ($user->getLoyaltyIdentifier() != $inputLoyaltyIdentifier) {
                try {
                    $loyaltyService->getLoyaltyPoints($inputLoyaltyIdentifier, $user->getEmail());
                    $user->setLoyaltyIdentifier($inputLoyaltyIdentifier);
                    $this->get('marketplace.user.user_repository')->save($user);
                } catch (ClientException $e) {
                    // Wrong loyalty identifier
                    fn_set_notification('E', __('error'), __('loyalty_identifier_invalid'));
                } catch (GuzzleException $e) {
                    // Api is down?
                    fn_set_notification('E', __('error'), __('an_error_occured'));
                }
            }

            return $this->redirectToRoute('loyalty');
        }

        $points = null;
        if ($user->getLoyaltyIdentifier()) {
            try {
                $points = $loyaltyService->getLoyaltyPoints($user->getLoyaltyIdentifier(), $user->getEmail());
            } catch (ClientException $e) {
                // Wrong loyalty identifier
                fn_set_notification('E', __('error'), __('loyalty_identifier_invalid'));
            } catch (GuzzleException $e) {
                // Api is down?
                fn_set_notification('E', __('error'), __('an_error_occured'));
            }
        }

        return $this->render('@Cashconverters/frontend/views/profile/loyalty.html.twig', [
            'loyaltyIdentifier' => $user->getLoyaltyIdentifier(),
            'loyaltyPoints' => $points,
        ]);
    }
}
