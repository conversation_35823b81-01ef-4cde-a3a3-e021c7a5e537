/** NORMALIZED ELEMENTS **/
[v-cloak] {
    display: none;
}

body {
    overflow-x: hidden;
    overflow-y: auto;
    font-weight: 200;
    font-family: @font-baseline;
}

h1 {
    font-family: @font-bebas;
    letter-spacing: 14px;
    text-indent: 14px;
    font-weight: 400;
    margin: 70px 0 11px 0;

    &:hover {
        color: @purple-redish;
    }

    @media (max-width: @screen-sm){
        margin: 40px 0 11px 0;
    }
}

select {
    border: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background: transparent;
}

/** GLOBAL CLASSES **/

.pagination {
    width: 100%;

    &-search {
        margin: 0;

        ul{
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    li {
        a {
            padding-top: 5px;
            color: black;
        }

        .glyphicon {
            font-size: 20px;
        }
    }
}


.price {
    color: @purple-redish;
    font-size: 16px;
    font-weight: bold;
}

.truncated, .ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    display: inline-block;
}

.purple-text {
    color: @purple;
}
.purple-redish-text {
    color: @purple-redish;
}

.product-image {
    margin: 0;
}

.product-image-no-image {
    img {
        width: 100%;
        margin: 0;
    }
}

hr.under-title {
    border-top-color: @light-grey;

    margin-top: 0;
    margin-bottom: 55px;
    width: 545px;

    @media (max-width: @screen-sm){
        margin: 0 auto 25px auto;
        width: 270px;
    }
}

#mobile-change-page {
    font-size: 20px;

    div {
        border-top: 1px solid @light-grey;
        border-bottom: 1px solid @light-grey;
        margin-bottom: 20px;
        height: 50px;
        line-height: 50px;
    }

    .mobile-change-page-arrow {
        font-size: 35px;
        color: @white;
        background: @black;

        .glyphicon {
            color: @white;
        }

        &.disabled {
            background: @light-grey;
        }
    }
}

/** popup div **/

.modal-content {
    border-radius: 0;
}

/** BUTTONS **/

.btn {
    font-size: 20px;
    padding: 2px 7px;

    &.default-btn {
        font-weight: 200;
        font-family: @font-baseline;
        border-radius: 0;
        padding: 6px 7px;
        min-height: 40px;

        @media (max-width: @screen-xs-max) {
            margin-bottom: 20px;
            width: 80%;
            font-size: 1.2em;
        }

        &.no-hover {
            cursor: default;
        }

        &.small {
            height: 30px;
            padding: 2px 7px 0 7px;
            font-size: 17px;
        }

        &.purple-btn {
            background: @purple-redish;
            color: @white;
            padding: .5em 1em;

            &:hover {
                background-color: @purple;
                color: @white;
            }

            @media screen and (max-width: @screen-xs-max) {
                margin: 0;
            }
        }

        &.purple-btn-outline {
            color: @purple;
            border: 1px solid @purple;

            &hover:not(.no-hover):hover {
                background: @purple;
                color: @black;
            }
        }

        &.light-purple-btn {
            font-weight: 100;
            width: 100%;

            background: @purple-redish;
            color: @white;

            &:not(.no-hover):hover {
                background: @black;
            }

        }

        &.light-purple-btn-outline {
            font-weight: 100;
            width: 100%;

            color: @purple-redish;
            border: 1px solid @purple-redish;

            &:not(.no-hover):hover {
                color: @black;
                border-color: @black;
            }

            text-decoration: none;
        }

        &.black-btn {
            background: @black;
            color: @white;
            padding: .5em;

            &:not(.no-hover):hover {
                background: @white;
                color: @black;
            }

            @media screen and (max-width: @screen-xs-max) {
                margin: 0 auto 1rem;
                padding: 0;
                width: 80%;
                line-height: 2.5em;
            }
        }

        &.black-btn-outline {
            background: @white;
            border: 1px solid @black;
            color: @black;

            &:not(.no-hover):hover {
                border: 1px solid #fff;
                background: @light-grey;
                color: @white;
            }

            &.dismiss {
                padding: .5em 1em;

                &:hover {
                    background: black;
                    color: white;
                }
            }
        }
    }

    &.square-btn {
        a {
            text-decoration: none;
            color: black;
        }

        padding: 2px 0;
        background: @light-grey;

        &.no-background {
            background: transparent;
        }

        font-size: 14px;
        width: 26px;

        margin: 0;

        &.square-btn-active {
            a {
                text-decoration: none;
                color: @white;
            }

            background: @purple;
            color: @white;
        }
    }

    &.trade-btn {
        color: @black;
        border: 1px solid @black;
        font-weight: 200;
        font-family: @font-baseline;
        font-size: 17px;
        border-radius: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 30px;
        transition: 0.15s ease;

        &:hover{
            background: @black;
            color: @white;
        }

        &.black {
            background: @black;
            color: @white;

            &:hover {
                background: @white;
                color: @black;
            }
        }
    }

    &.profile-btn,
    &.merchant-btn,
    &.contact-btn {
        color: @white;
        background: @purple;
        border: 1px solid @purple;
        font-weight: 200;
        font-family: @font-baseline;
        font-size: 17px;
        border-radius: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: 0.15s ease;

        &:hover {
            background: @white;
            color: @purple;
        }

        &.black {
            background: @black;
            border-color: @black;
            color: @white;

            &:hover {
                background: @white;
                color: @black;
            }
        }

        &.pink {
            background: @purple-redish;
            border-color: @purple-redish;
            color: @white;

            &:hover {
                background: @white;
                color: @purple-redish;
            }
        }

        &.large {
            padding: 10px 0;
        }
    }

    &.checkout-btn {
        background: @purple-redish;
        color: @white;
        border: 1px solid @purple-redish;
        border-radius: 0;
        font-weight: 200;
        font-size: 18px;
        font-family: @font-baseline;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: .15s ease;
        width: 80%;

        &:hover{
            background: @white;
            color: @purple-redish;
            text-decoration: none;
        }

        &.white{
            background: @white;
            border: 1px solid @purple;
            color: @purple;

            &:hover{
                background: @purple;
                color: @white;
            }
        }
    }

}

.glyphicon-star {
    font-size: 20px;

    &.on {
        color: @purple;
    }
    &.off {
        color: @light-grey;
    }
}

// rating widget
.rating-container .rating-stars {
    color: @purple-redish;
}

.flip-x {
    transform: scaleX(-1);
    -moz-transform: scaleX(-1);
    -webkit-transform: scaleX(-1);
    -ms-transform: scaleX(-1);
}

.flip-y {
    transform: scaleY(-1);
    -moz-transform: scaleY(-1);
    -webkit-transform: scaleY(-1);
    -ms-transform: scaleY(-1);
}

.rotate-cw {
    transform: rotate(90deg);
    -moz-transform: rotate(90deg); /* IE 9 */
    -webkit-transform: rotate(90deg); /* Chrome, Safari, Opera */
    -ms-transform: rotate(90deg); /* IE 9 */
}

.rotate-ccw {
    transform: rotate(-90deg);
    -moz-transform: rotate(-90deg); /* IE 9 */
    -webkit-transform: rotate(-90deg); /* Chrome, Safari, Opera */
    -ms-transform: rotate(-90deg); /* IE 9 */
}

/** Mobile Overlay **/
.overlay {
    height: 0;
    width: 100%;
    position: fixed;
    z-index: 99;
    top: 0;
    left: 0;
    background-color: @purple;
    overflow-y: hidden;
    transition: 0.5s;

    .overlay-content {
        color: @purple;

        position: relative;
        width: 100%;
        text-align: center;
        margin-top: 30px;

        p{
            color: #fff;
            font-weight: bold;
            font-size: 20pt;
            margin-bottom: 25px;
        }

        a {
            color: #fff;
        }

        .mobile-menu-category {
            height: 45px;
            border-top: 1px solid #fff;

            span {
                line-height: 45px;
                font-size: 16pt;
            }
        }
    }

    .closebtn {
        color: #fff;
        position: absolute;
        top: 0;
        right: 20px;
        font-size: 40px;
        z-index: 999;

        &:hover {
            text-decoration: none;
        }
    }

    .container {
        width: 80%;
    }
}


/**
*** PLUGINS
**/

/** SLICK **/

.slick-dots{
    bottom: 16px;

    li {
        margin: 0 8px;

        button:before {
            font-size: 30px;
            opacity: 1;
            color: @white;
        }

        &.slick-active button:before {
            color: @darker-gray!important;
        }
    }
}

.slick-arrow {
    display: block;
    position: absolute;
    color: @black;
    z-index: 9;
    font-size: 40px;
    font-weight: 900;
    height: 0;
    top: calc(~"50% - 20px");

    &:hover {
        color: @purple;
        cursor: pointer;
    }

    &.slick-arrow-white {
        color: white;

        &:hover {
            color: @purple;
        }
    }
}

/** SCROLLBARJS **/

.scroll-wrapper {
    overflow: hidden !important;
    padding: 0 !important;
    position: relative;
}

.scroll-wrapper > .scroll-content {
    border: none !important;
    box-sizing: content-box !important;
    height: auto;
    left: 0;
    margin: 0;
    max-height: none;
    max-width: none !important;
    overflow: scroll !important;
    padding: 0;
    position: relative !important;
    top: 0;
    width: auto !important;
}

.scroll-wrapper > .scroll-content::-webkit-scrollbar {
    height: 0;
    width: 0;
}

.scroll-element {
    display: none;
}
.scroll-element, .scroll-element div {
    box-sizing: content-box;
}

.scroll-element.scroll-x.scroll-scrollx_visible,
.scroll-element.scroll-y.scroll-scrolly_visible {
    display: block;
}

.scroll-element .scroll-bar,
.scroll-element .scroll-arrow {
    cursor: default;
}

.scroll-textarea {
    border: 1px solid #cccccc;
    border-top-color: #999999;
}
.scroll-textarea > .scroll-content {
    overflow: hidden !important;
}
.scroll-textarea > .scroll-content > textarea {
    border: none !important;
    box-sizing: border-box;
    height: 100% !important;
    margin: 0;
    max-height: none !important;
    max-width: none !important;
    overflow: scroll !important;
    outline: none;
    padding: 2px;
    position: relative !important;
    top: 0;
    width: 100% !important;
}
.scroll-textarea > .scroll-content > textarea::-webkit-scrollbar {
    height: 0;
    width: 0;
}

.scrollbar-inner > .scroll-element,
.scrollbar-inner > .scroll-element div
{
    border: none;
    margin: 0;
    padding: 0;
    position: absolute;
    z-index: 10;
}

.scrollbar-inner > .scroll-element div {
    display: block;
    height: 100%;
    left: 0;
    top: 0;
    width: 100%;
}

.scrollbar-inner > .scroll-element.scroll-x {
    bottom: 2px;
    height: 8px;
    left: 0;
    width: 100%;
}

.scrollbar-inner > .scroll-element.scroll-y {
    height: 100%;
    right: 2px;
    top: 0;
    width: 8px;
}

.scrollbar-inner > .scroll-element .scroll-element_outer {
    overflow: hidden;
}

.scrollbar-inner > .scroll-element .scroll-element_outer,
.scrollbar-inner > .scroll-element .scroll-element_track,
.scrollbar-inner > .scroll-element .scroll-bar {
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    border-radius: 8px;
}

.scrollbar-inner > .scroll-element .scroll-element_track,
.scrollbar-inner > .scroll-element .scroll-bar {
    -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=40)";
    filter: alpha(opacity=40);
    opacity: 0.4;
}

.scrollbar-inner > .scroll-element .scroll-element_track { background-color: #e0e0e0; }
.scrollbar-inner > .scroll-element .scroll-bar { background-color: #c2c2c2; }
.scrollbar-inner > .scroll-element:hover .scroll-bar { background-color: #919191; }
.scrollbar-inner > .scroll-element.scroll-draggable .scroll-bar { background-color: #919191; }


/* update scrollbar offset if both scrolls are visible */

.scrollbar-inner > .scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_track { left: -12px; }
.scrollbar-inner > .scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_track { top: -12px; }


.scrollbar-inner > .scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_size { left: -12px; }
.scrollbar-inner > .scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_size { top: -12px; }

/*************** SCROLLBAR MAC OS X ***************/

.scrollbar-macosx > .scroll-element,
.scrollbar-macosx > .scroll-element div
{
    background: none;
    border: none;
    margin: 0;
    padding: 0;
    position: absolute;
    z-index: 10;
}

.scrollbar-macosx > .scroll-element div {
    display: block;
    height: 100%;
    left: 0;
    top: 0;
    width: 100%;
}

.scrollbar-macosx > .scroll-element .scroll-element_track { display: none; }
.scrollbar-macosx > .scroll-element .scroll-bar {
    background-color: #6C6E71;
    display: block;

    -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    filter: alpha(opacity=0);
    opacity: 0;

    -webkit-border-radius: 7px;
    -moz-border-radius: 7px;
    border-radius: 7px;

    -webkit-transition: opacity 0.2s linear;
    -moz-transition: opacity 0.2s linear;
    -o-transition: opacity 0.2s linear;
    -ms-transition: opacity 0.2s linear;
    transition: opacity 0.2s linear;
}
.scrollbar-macosx:hover > .scroll-element .scroll-bar,
.scrollbar-macosx > .scroll-element.scroll-draggable .scroll-bar {
    -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=70)";
    filter: alpha(opacity=70);
    opacity: 0.7;
}


.scrollbar-macosx > .scroll-element.scroll-x {
    bottom: 0px;
    height: 0px;
    left: 0;
    min-width: 100%;
    overflow: visible;
    width: 100%;
}

.scrollbar-macosx > .scroll-element.scroll-y {
    height: 100%;
    min-height: 100%;
    right: 0px;
    top: 0;
    width: 0px;
}

/* scrollbar height/width & offset from container borders */
.scrollbar-macosx > .scroll-element.scroll-x .scroll-bar { height: 7px; min-width: 10px; top: -9px; }
.scrollbar-macosx > .scroll-element.scroll-y .scroll-bar { left: -9px; min-height: 10px; width: 7px; }

.scrollbar-macosx > .scroll-element.scroll-x .scroll-element_outer { left: 2px; }
.scrollbar-macosx > .scroll-element.scroll-x .scroll-element_size { left: -4px; }

.scrollbar-macosx > .scroll-element.scroll-y .scroll-element_outer { top: 2px; }
.scrollbar-macosx > .scroll-element.scroll-y .scroll-element_size { top: -4px; }

/* update scrollbar offset if both scrolls are visible */
.scrollbar-macosx > .scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_size { left: -11px; }
.scrollbar-macosx > .scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_size { top: -11px; }

// email protection (right to left mirroring)
.email-rtl {
    unicode-bidi:bidi-override;
    direction: rtl;
}


.wysiwyg-output {
    p {
        margin: 0;
    }
}


//===========
// CHECKBOX & RADIO

// common
.checkbox-type-input(@checked-image, @unchecked-image) {
    label {
        padding-left: 0; // reset Bootstrap default
    }

    display: block;
    max-width: 100%;
    margin: 0 0 10px;
    position: relative;
    padding: 0 0 0 26px;
    font-weight: @normal;
    cursor: pointer;

    // if replacement for actual input
    input {
        position: absolute;
        height: 1px;
        width: 1px;
        text-align: center;
        margin: 0;
        left: 0;
        opacity: 0;

        & + span {
            position: absolute;
            content: '';
            left: 0;
            top: 2px;
            width: 16px;
            height: 16px;
            background: transparent url("../images/@{unchecked-image}") no-repeat;
            background-size: 100%;
        }

        &:checked + span {
            background: transparent url("../images/@{checked-image}") no-repeat;
            background-size: 100%;
        }
    }

    // for fake inputs
    .checkbox-image {
        position: absolute;
        content: '';
        left: 0;
        top: 2px;
        width: 16px;
        height: 16px;
        background: transparent url("../images/@{unchecked-image}") no-repeat;
        background-size: 100%;
        &.checked {
            background: transparent url("../images/@{checked-image}") no-repeat;
            background-size: 100%;
        }
    }
}

.checkbox {
    .checkbox-type-input("checkbox_checked.png", "checkbox.png");
}

.radio {
    .checkbox-type-input("radio_checked.png", "radio.png");
}

.global-overlay {
    display: none; // starts off hidden
    position: fixed;
    z-index: @index-overlay;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: #000;
    opacity: .8;

    &.is-visible {
        display: block;
    }
}

#cookie-consent {
    .cc-window {
        color: @white;
        background: fade(@black, 95%);

        .cc-link, .cc-dismiss {
            color: @white;
            text-decoration: none;
        }

        .cc-btn {
            border: none;

            &:hover {
                background: @purple;
            }
        }

        .cc-compliance {
            background: @purple-redish;
        }
    }
}

.form-required {
    position: relative;
    margin-right: 10px;

    &:after {
        content: " *";
        font-size: .8em;
        position: absolute;
        right: -8px;
        top: -5px;
    }
}

.breadcrumb {
    padding-left: 0;

    > .breadcrumb-item {
        &:last-of-type {
            font-weight: bold;
        }
    }
}

.modal-footer {
    text-align: center;
}

.no-margin {
    margin: 0 !important;
}

.text-purple-redish {
    color: @purple-redish;
}

.single-row {
    margin: 0 -6px;
    padding: 0 20px 20px;
    text-align: justify;
}
