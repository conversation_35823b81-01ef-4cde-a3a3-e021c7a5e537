.contact-content {

    .contact-main-title {
        h1 {
            text-transform: uppercase;
            margin: 50px 0 0;
        }
        hr {
            border-bottom: 1px solid @light-grey;
            margin: 0 0 40px;
        }
    }

    .contact-content {

        padding: 20px;

        &-title {
            h2 {
                display: inline-block;
                text-transform: uppercase;
                color: @purple-redish;
                font-family: @font-bebas;
                position: relative;
                margin: 20px auto;

                &:after {
                    content: " ";
                    border: none;
                    height: 10px;
                    background: linear-gradient(-45deg, @white 8px, transparent 0) 0 5px, linear-gradient(135deg, @white 5px, @darker-gray 0) 0 5px;
                    background-color: #fff;
                    background-position: left bottom;
                    background-repeat: repeat-x;
                    background-size: 10px 10px;
                    width: 100%;
                    position: absolute;
                    left: 0;
                    bottom: -10px;
                }
            }
        }

        h3,
        .btn {
            text-transform: uppercase;
        }

        p {
            text-align: justify;
        }

        .contact-infos {
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            justify-content: space-between;
            margin-bottom: 10px;
            span {
                font-size: 1.5em;
                flex: 1;
                color: @purple;
            }
            p {
                flex: 5;
            }
        }

        button {
            display: block;
            text-transform: uppercase;
            width: 100%;
        }
    }
}
