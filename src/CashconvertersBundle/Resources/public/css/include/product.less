section.breadcrumb {
    background: @white;
    font-style: italic;

    a {
        color: @black;

        &:hover {
            text-decoration: underline;
        }
    }

    span {
        font-weight: bold;
    }
}

section.product-content {

    h2 {
        font-family: @font-family-base;
        margin-top: 0;
    }

    h3 {
        font-size: 1.4em;
        margin: 1.2em 0 .8em;
        font-weight: bold;
    }

    .product-main-image {
        max-width: 318px;
        max-height: 318px;
        margin-bottom: 1em;

        img {
            max-width: 318px;
            height: auto;
        }

        @media screen and (max-width: @screen-xs-max) {
            margin: 30px auto 1em;
            max-width: 318px;
        }
    }

    .product-thumbnails {
        .slick-list {
            padding: 0 !important;
            display: flex;
            justify-content: center;
            align-items: flex-start;

            .slick-track {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;

                img {
                    height: auto;
                    max-width: 100px;
                }
            }
        }
    }

    hr {
        width: 100%;
        border-top: 1px solid @light-grey;
    }

    .single-product-cta {
        span {
            color: @black;
        }

        .single-product-icon-hover {
            display: none;
        }

        .single-product-icon {
            display: block;
        }

        &:hover {
            span {
                color: @purple-redish;
            }

            .single-product-icon-hover {
                display: block;
            }

            .single-product-icon {
                display: none;
            }
        }

        @media (max-width: @screen-sm){
            margin-bottom: 20px;

            span {
                color: @purple-redish;
            }
        }
    }

    .single-product-cta-mid {
        border-left: 1px solid @light-grey;
        border-right: 1px solid @light-grey;
    }

    .single-product-icon,
    .single-product-icon-hover {
        width: 100%;
        padding-top: 50%;

        // 'fix' cropped icons
        @media screen and (max-width: 900px) { padding-top: 100%; }
        @media screen and (max-width: 767px) { padding-top: 35%; }
        @media screen and (max-width: 450px) { padding-top: 50%; }
        @media screen and (max-width: 320px) { padding-top: 75%; }
    }

    .tab-content {
        height: 300px;
        overflow: hidden;
    }

    .product-description {
        overflow-y: auto;

        h1#product-title {
            letter-spacing: 0;
            text-indent: 0;
        }

        span {
            font-family: @font-baseline;
        }

        .in-stock,.out-of-stock {
            a {
                color: @purple-redish;

                &:hover {
                    color: @purple;
                    text-decoration: underline;
                }
            }

            @media (max-width: @screen-sm){
                width: 100%;
                text-align: center;
            }
        }

        .attribute-name {
            font-weight: bold;
        }
    }

    .product-main-info {
        margin-bottom: 50px;
    }

    .nav-tabs {
        border-bottom: 5px solid @purple;
        margin-bottom: 1.2em;

        li {
            a {
                font-family: @font-bebas;
                background: @black;
                color: @white;
                border-radius: 0;
                border: none;
                padding: 4px 10px 0;
                font-size: 20px;

                &:hover,
                &:focus {
                    background: @purple;
                    color: white;
                    border: none;
                }
            }

            &.active a {
                background: @purple;
            }
        }
    }
}

// declinations
section.product-offers {

    // buttons used to be positioned and centered using bootstrap (...)
    // this const helps to homogenize at least this area
    @xs-width: 82%;
    margin: 3em 0;

    hr {
        &.under-title {
            border-color: @black;
            margin-bottom: 15px;
        }

        &:not(.under-title) {
            margin: 2px 0;
        }
    }

    h1 {
        color: @black;
        margin-top: 0;
    }

    .no-offers {
        margin: 5rem 0 300px;
        font-size: 1.7rem;
        text-align: center;
        color: @purple-redish;
    }

    .btn.display-filters {
        width: @xs-width;
        margin: 2em auto;
        line-height: 1em;
    }

    .filters {
        .select-container {
            border: 1px solid @darker-gray;
            border-radius: 3px;
            padding: .25em 0;
            position: relative;
            overflow: hidden;

            select {
                padding: 0 2.5em 0 .5em;
                cursor: pointer;
            }

            .select-icon {
                position: absolute;
                right: 0;
                top: 0;
                z-index: 2;
                pointer-events: none;
                color: #fff;
                background-color: #000;
                padding: .46em;
            }
        }

        //small screens
        @media screen and (max-width: @screen-xs-max) {
            display: none; // starts off hidden

            &.is-visible {
                display: flex;
            }

            width: @xs-width;
            margin: 1em auto;
            font-size: 1.5em;
            justify-content: center;
            flex-direction: column;

            .select-container {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                margin-bottom: 1.5em;
                overflow: hidden;

                select{
                    flex: 1 0 95%;
                }
            }
        }

        // medium and large screens
        @media screen and (min-width: @screen-sm-min) {
            border-top: 1px solid @light-grey;
            border-bottom: 1px solid @light-grey;
            padding: .5em;
            margin-bottom: 2em;

            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    }

    .declination {
        font-size: 1.2em;
        margin-bottom: 3em;
        padding-bottom: 3em;

        &:not(:last-of-type) {
            border-bottom: 1px solid rgba(0,0,0,.05);
        }

        @media screen and (max-width: @screen-sm-max) {
            margin: 0 2em 3em;
        }

        @media screen and (max-width: @screen-xs-max) {
            &:last-of-type {
                margin-bottom: 0;
            }
        }

        .main-data {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1em;

            @media screen and (max-width: @screen-xs-max) {
                flex-direction: column-reverse;
                align-items: center;
            }

            .featured-images-block {
                width: 250px;
                margin-right: 1em;

                // fixes for slick
                img {
                    display: block;
                    max-width: 100% !important;
                    width: 100%;
                    height: auto;
                }

                @media screen and (max-width: @screen-xs-max) {
                    margin: 0;
                    width: 300px;
                }
            }

            .declination-details {
                flex: 1 1 auto;

                .vendor-detail {
                    font-weight: bold;

                    .company-link {
                        color: @purple;
                        text-decoration: underline;
                        text-transform: uppercase;
                    }

                    .rank {
                        margin-left: 1em;
                    }

                    @media screen and (max-width: @screen-xs-max) {
                        display: flex;
                        flex-direction: column;
                        align-items: center;

                        font-size: 1.3em;

                        .rank {
                            margin-left: 0;
                            font-size: 1.6em;
                        }
                    }
                }

                .contact-link {
                    color: #333;
                    text-decoration: underline;
                }
            }
        }

        .actions {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;

            @media screen and (max-width: @screen-sm-max) {
                flex-direction: column;
            }

            @media screen and (max-width: @screen-xs-max) {
                align-items: center;
            }

            .thumbnails-price-group {
                display: flex;
                justify-content: flex-start;
                align-items: flex-start;

                @media screen and (max-width: @screen-xs-max) {
                    flex-direction: column;
                    align-items: center;
                }

                .thumbnails-block {
                    width: 250px;
                    margin-right: 1em;

                    @media screen and (max-width: @screen-xs-max) {
                        margin: 0 0 1em 0;
                        width: 300px;
                    }

                    .declination-thumbnails {
                        .slick-list {
                            padding: 0 !important;
                            display: flex;
                            justify-content: center;
                            align-items: flex-start;

                            .slick-track {
                                display: flex;
                                justify-content: space-between;
                                align-items: flex-start;

                                // fixes for slick
                                .declination-thumbnail {
                                    width: 75px !important;
                                    height: auto;
                                    margin: 0 3px;

                                    @media screen and (max-width: @screen-xs-max) {
                                        width: 90px !important;
                                    }
                                }
                            }
                        }
                    }
                }

                .price {
                    font-size: 1.25em;
                }
            }

            .detail-buttons-group {
                display: flex;
                justify-content: space-between;

                width: 60%;

                @media screen and (max-width: @screen-sm-max) {
                    width: 100%;
                }

                @media screen and (max-width: @screen-xs-max) {
                    flex-direction: column-reverse;
                }

                .see-detail {
                    font-size: .95em;
                    font-style: italic;
                    text-decoration: underline;
                    color: #333;
                    flex: 0 1 auto;
                    align-self: flex-end;

                    @media screen and (max-width: @screen-xs-max) {
                        align-self: inherit;
                    }
                }

                .buttons {
                    min-width: 220px;

                    .add-to-basket-form {
                        margin-bottom: .7em;
                    }

                    @media screen and (max-width: @screen-xs-max) {

                        [type="submit"] {
                            margin: 1em 0 .25em 0;
                            line-height: 1.5em;
                        }

                        .light-purple-btn-outline {
                            line-height: 1.75em;
                        }
                    }
                }
            }
        }
    }
}

section.similar-products {
    margin-bottom: 4em;

    @media screen and (max-width: @screen-xs-max) {
        display: none;
    }

    .recommended-product-card {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .products-slideshow {
        overflow: visible;

        .slick-arrow-left {
            left: -1.5em;
        }

        .slick-arrow-right {
            right: -1.5em;
        }
    }
}

.show-hide-btn {
    @media screen and(max-width: @screen-xs-max) {
        width: 82%;
        margin: auto;
    }
}

.disabled-button {
    .single-product-cta {

        .single-product-icon {
            display: none !important;
        }

        .single-product-icon-hover {
            display: block !important;
            opacity: 0.5;
        }

        span {
            color: grey !important;
        }
    }

    .single-product-cta-mid {

        span {
            color: grey !important;
        }
    }
}

.product-picture-mini {
    max-width: 100%;
}

.slick-slider {
    touch-action: auto !important;
    -ms-touch-action: auto !important;
}
