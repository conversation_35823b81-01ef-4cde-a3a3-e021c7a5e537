section#vendor-display {

    h1 {
        margin: 40px 0 10px;
    }

    hr {
        background: @light-grey;
        margin-bottom: 20px;
    }

    h3 {
        font-family: @font-bebas;
        font-size: 2.5em;
    }

    .store-info {
        a {
            color: @black;
            text-decoration: none;
            font-weight: bold;

            &:hover {
                text-decoration: underline;
            }
        }

        img#store-front {
            margin-top: 40px;
            max-width: 100%;
            max-height: 400px;
            margin-bottom: 1em;
        }

        .vendor-description {
            text-align: center;
            font-style: italic;
        }

        .follow-facebook {

            img {
                height: 55px;
            }

            .follow-us-text {
                font-family: Waiting;
                margin: 0;
                color: @purple-redish;
                font-size: 20px;
            }
        }
    }

    .hours {
        [v-cloak] {
            display: none;
        }

        .weekday {
            color: @purple;
            font-weight: bold;
        }

        .glyphicon {
            margin: 0 1rem;
            vertical-align: baseline;
        }

        .is-open {
            .glyphicon-record;
            color: #83d79b;
        }

        .is-closed {
            .glyphicon-record;
            color: darkred;
        }

        th, td {
            padding: .1em 1rem;
        }

        table {
            margin-bottom: 2rem;
        }
    }

    .location {
        font-size: 15px;

        .glyphicon {
            font-size: 20px;
            margin: 15px;
            color: @purple;
        }

        #go-button {
            margin-left: 20px;

            @media screen and (max-width: @screen-desktop){
                margin: 20px 0 0;
            }

            @media (min-width: @screen-tablet){
                width: 50%;
            }

            @media (min-width: @screen-desktop){
                width: 33%;
            }
        }
    }

    .comments {
        .emphasis {
            color: @purple-redish;
        }

        .review {
            margin-bottom: 2em;
        }
    }

    .products {
        .slick-arrow {
            &.slick-arrow-right {
                right: 0;
            }

            &.slick-arrow-left {
                left: 0;
            }

            @media (max-width: @screen-xs-max) {
                display: none!important;
            }
        }

        .products-slideshow {
            margin-bottom: 20px;
            font-family: @font-baseline;

            a {
                color: @black;
                text-decoration: none;
            }

            img {
                display: block;
                margin: auto;
            }

            .slick-list {
                @media screen and (min-width: @screen-sm) {
                    margin: auto;
                    width: 90%;
                }
            }
        }
    }
}

/*GOOGLE MAP */

.merchant-map{
    display: flex;
    align-items: flex-start;
    flex-direction: row;
    justify-content: center;

    @media screen and (max-width: @screen-desktop){
        flex-direction: column;
    }
}

#map {
    height: 400px;
    width: 60%;

    @media (max-width: @screen-desktop){
        width: 100%;
    }
}
