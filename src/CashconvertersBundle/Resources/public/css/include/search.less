.search-page {
    .category-block {
        &.visible-xs-block {
            h1 {
                font-size: 1.5em;
            }
        }

        &.visible-sm-block,
        &.visible-md-block,
        &.visible-lg-block {
            h1 {
                margin-top: 0;
            }

            margin-bottom: 50px;

        }

        p {
            margin: 20px auto;
        }

        img {
            margin: auto;
        }
    }

    // reset btn (simplified)
    .btn {
        color: #fff;
        font-size: 1.5em;
        margin-bottom: 1.75em;
        padding: .5em 0;
        border-radius: 0;
        width: 100%;

        &.btn-black {
            background-color: #000;
        }

        &.btn-purple {
            background-color: @purple-redish;
        }

        &.btn-white {
            background-color: #fff;
            color: #000;
            border: 1px solid #000;
        }
    }

    // results info and small screens buttons
    .results-header {
        margin: 3em 0 3em;
        font-size: 1em;

        @media screen and (max-width: @screen-xs-max) {
            text-align: center;
            font-size: 1.2em;
            margin: 1.75em 0;
        }

        .search-keyword {
            font-family: @font-bebas;
            font-size: 2em;
            font-weight: bold;
        }

        .results-numbers {
            font-size: 1.2em;
        }

        hr:first-of-type {
            margin-bottom: 3rem;
        }

        hr:last-of-type {
            margin: 2rem 0;
        }
    }

    // FILTERS
    // =======

    // category in sidebar AND mobile filters
    .category {
        color: @black;
        cursor: pointer;

        &.is-current {
            color: @purple-redish;
        }

        .glyphicon {
            font-size: .8em;
        }
    }

    // sidebar
    // =======

    .sidebar-title {
        color: #fff;
        font-family: @font-bebas;
        font-size: 1.6em;
        font-weight: bold;
        padding: .1em .25em;
        margin-bottom: 1em;
        display: inline-block;
        width: 100%;
        border-radius: 0;
        background-color: @purple;
        text-align: left;
    }

    .search-sidebar {
        border-right: 1px solid @light-grey;
        padding-bottom: 5em; // keep border going on a while after actual sidebar content

        .filter-block {
            .title {
                font-size: 1.3em;
                font-weight: bold;
                text-transform: uppercase;
                margin-bottom: .75em;
            }

            .content {
                padding: 0 1em;
                margin-bottom: 1.5em;

                em {
                    color: @purple-redish;
                    font-weight: bold;
                    font-style: normal;
                }
            }

            .indented {
                padding-left: 1em;
            }

            &.vendor-facet > .content {
                margin-right: 12px;
                max-height: 300px;
                overflow: auto;
            }
        }

        // category tree
        .branch-node {
            .leaf-link {
                text-decoration: none;

                &:hover {
                    color: @purple;
                }
            }

            .branch {
                display: none; // starts off hidden
            }

            &.is-open {
                > .branch {
                    display: block;
                }

                > .category {
                    .glyphicon {
                        .glyphicon-menu-down(); // change icon on open branches
                    }

                    .category-name {
                        font-weight: bold;
                    }
                }
            }
        }

        // price sorting
        .price-sort label {
            display: block;
            margin: 0;
            font-weight: normal;
            cursor: pointer;
        }
    }


    // mobile filters
    // ==============

    .xs-filters {
        display: none;
        position: fixed;
        top: -100%;
        width: 100%;
        z-index: 10;
        background-color: @white;
        padding: 1em;
        box-shadow: 0 0 2px 2px rgba(0,0,0,.5);
        height: 100%;
        overflow: scroll;

        &.is-visible {
            display: block;
            animation: slide-down .450s forwards;
        }

        .filter-triggers {
            .filter-trigger, .select-category {
                cursor: pointer;
                border-top: 1px solid @light-grey;
                padding: .75em 0;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .filter-detail {
                text-align: center;
                color: @light-grey;
                font-size: .9em;
                margin-top: -.75em;
                padding-bottom: .6em;
            }

            .select-category {
                justify-content: center;

                .glyphicon {
                    color: #fff;
                }
            }

            border-bottom: 1px solid @light-grey;
            margin-bottom: 2em;
        }

        .filters-close, .panel-close {
            color: @purple-redish;
            position: absolute;
            top: 16px;
            right: 16px;
            z-index: 11;
            font-size: 1.7em;
            cursor: pointer;
        }

        .panel-close {
            top: 16px;
            right: 24px;
        }

        .filters-title {
            font-size: 1.7em;
            font-weight: bold;
            text-align: center;
            text-transform: uppercase;
            margin-bottom: .75em;
        }

        .filter-block {
            .title {
                font-size: 1.5em;
                font-weight: bold;
                text-transform: uppercase;
                margin-bottom: 1em;
            }

            .content {
                font-size: 1.2em;
                padding: 0;
                margin-bottom: 2em;

                .price-sort > .radio {
                    margin-bottom: 1em;
                }

                em {
                    color: @purple-redish;
                    font-weight: bold;
                    font-style: normal;
                }
            }

            &.vendor-facet > .content {
                margin-right: 12px;
                max-height: 300px;
                overflow: auto;

                > .checkbox-image {
                    top: 13%; // optical sugar
                }
            }
        }

        .btn {
            margin-bottom: 1em;
        }

        .clear-filter {
            font-size: 1.3em;
            width: 100%;
            margin-bottom: .5em;
        }

        .select-container {
            border: 1px solid @darker-gray;
            border-radius: 3px;
            padding: .25em 0;
            position: relative;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-bottom: 2em;

            select {
                padding: 0 2.5em 0 .5em;
                cursor: pointer;
                flex: 0 0 99%;
            }

            .select-icon {
                position: absolute;
                right: 0;
                top: 0;
                z-index: 2;
                pointer-events: none;
                color: #fff;
                background-color: #000;
                padding: .46em;
            }
        }

        .mobile-panel {
            background: @white;
            height: 100vh;
            width: 100vw;
            padding: 1em;
            position: fixed;
            z-index: 11;
            top: 0;
            left: 100%;
            transition: left .45s;
            will-change: left;
            height: 100%;
            overflow: scroll;

            &.is-in {
                left: 0;
            }

            .panel-title {
                font-weight: normal;
                text-transform: capitalize;
            }

            .filter-trigger, .select-category {
                font-size: 1.2em;
            }

            .select-category {
                color: @black;
                text-decoration: none;
            }

            .mobile-geoloc {
                padding: 1em;
                display: flex;
                justify-content: space-between;
                align-items: stretch;

                & > * {
                    height: 50px;
                }

                .btn {
                    margin: 0 0 0 1em;
                    width: 55px;
                    background: @purple-redish;
                    border-radius: @border-radius-base;
                }
            }

            .filter-title {
                text-align: center;
                font-size: 1.4em;
                text-transform: uppercase;
            }

            .filter-block {
                padding: 1em;
            }
        }
    }

    // results
    // =======

    // actual search results: list of products
    .search-results {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        flex-wrap: wrap;

        .no-result {
            width: 100%;

            h2 {
                margin: 0;
            }
        }

        .product {
            width: 30%; // display 3 cards with some space between them
            text-align: center;
            margin-bottom: 2em;

            .clickable-area {
                color: @black;

                .footer {
                    min-height: 60px;
                    height: auto;
                }
                
                &:hover {
                    text-decoration: none;

                    img {
                        opacity: .5;
                    }
                }

                img  {
                    width: 150px;
                    margin-bottom: .25em;

                    @media screen and (max-width: @screen-xs-max) {
                        width: 200px;
                    }

                    @media screen and (max-width: @screen-xs) {
                        width: 150px;
                    }
                }

                .footer-label {
                    display: block;
                    font-weight: bold;
                    font-size: .9em;
                    color: @black;
                }

                .offer-count {
                    display: block;
                    font-size: .9em;
                    color: @purple-redish;
                }

                .price {
                    font-size: 1.3em;
                }
            }

            @media screen and (max-width: @screen-xs-max) {
                width: 45%; // display 2 cards with some space between them
            }
        }
    }
}

.top-filters {
    display: flex;
    flex-wrap: wrap;
    position: relative;

    a {
        color: @black;
        float: right;
        position: absolute;
        top: -2.5rem;
        right: 0;
    }

    .dropdown-filter {
        margin-right: 4rem;
        margin-bottom: 1rem;

        .dropdown-filter-button {
            border: 1px solid @darker-gray;
            border-radius: 3px;
            padding: 0 0 0 1rem;
            position: relative;
            background: #fff;
            display: flex;
            align-items: center;

            .select-icon {
                color: #fff;
                background: #000;
                padding: .55rem;
                top: 0;
                margin-left: 1rem;
            }
        }

        .dropdown-menu {
            padding: 1rem;
        }

        li {
            cursor: pointer;
            padding: 0 .5rem;

            &:hover {
                background: #ddd;
            }

            &.selected-filter {
                font-weight: bold;
                color: @purple;
            }
        }
    }
}

@media screen and (max-width: @screen-xs-max) {
    .top-filters {
        display: block;
        width: 100%;
        border-top: 1px solid #d2d2d2;
        padding: .75em 0;
        padding-top: 45px;
        a {
            top: 1rem;
            width: 100%;
            text-align: center;
            float: none;
        }
        .dropdown-filter{
            margin-right: 0;
            .dropdown-filter-button {
                width: 100%;
                min-height: 26px;
                .select-icon {
                    right: 0;
                    position: absolute;
                }
            }
        }
        .dropdown-menu{
            width: 100%;
        }
    }
}
