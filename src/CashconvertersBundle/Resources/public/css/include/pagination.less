
// pagination
// ==========

.pagination-block {
    display: flex;
    justify-content: center;

    .page-item {
        margin: 0 .2em;

        .btn-pagination {
            color: @black;
            background: transparent;
            border-radius: @border-radius-base;
            padding: 0;
            margin: 0;
            height: 1.5em;
            width: 1.5em;
            outline: 0;

            // being clicked
            &:active {
                background-color: @light-grey;
            }
        }

        // currently selected
        .active {
            color: #fff;
            background-color: @purple;
        }
    }

    &.screen-xs {
        @media screen and (min-width: @screen-sm-min) {
            display: none;
        }

        margin-bottom: 1.75em;

        justify-content: flex-start;
        flex-wrap: nowrap;

        .btn-pagination {
            color: #fff;
            background-color: #000;
            font-size: 2.2em;
            border-radius: 0;
            padding: .25em 0;
            margin: 0;
            width: 3em;

            .glyphicon {
                font-weight: bold;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }

        .page {
            font-size: 1.5em;
            border-top: 1px solid @light-grey;
            border-bottom: 1px solid @light-grey;
            position: relative;
            cursor: pointer;
            -moz-user-select: none;
            user-select: none;
            flex: 1 0 auto;
            height: 50px;

            i {
                margin-left: 1.5em;

                @media screen and (max-width: 480px) {
                    margin: 0;
                }
            }

            &-dropdown {
                display: flex;
                align-items: center;
                justify-content: space-around;
                line-height: 50px;
            }

            // display pages list dropdown
            &.is-open .pages {
                display: flex;
                flex-direction: column;
                justify-content: center;
            }

            // small screens pages dropdown
            .pages {
                display: none; // starts off hidden
                width: 100%;
                position: absolute;
                z-index: 2;
                top: 2.4em;
                text-align: center;
                font-size: 1em;
                background-color: #fff;
                border: 1px solid fade(@light-grey, 50%);
                box-shadow: 2px 2px 2px rgba(0,0,0,.2);

                .page-number {
                    padding: .35em 0;

                    &:not(:last-of-type) {
                        border-bottom: 1px solid fade(@light-grey, 50%);
                    }

                    &.active {
                        color: @purple-redish;
                    }
                }
            }
        }
    }
}
