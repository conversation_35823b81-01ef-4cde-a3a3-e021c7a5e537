.slick-track {
    img {
        width: 85px;
    }
}

section#banner {
    img {
        width: 100%;
        height: auto;
        max-height: 420px;
    }

    .slick-arrow-left {
        left: 25px;
    }

    .slick-arrow-right {
        right: 25px;
    }

    #news-slideshow {
        margin: 15px 0;
        overflow: hidden;
        position: relative;
    }
}


section#latest {
    .slick-arrow {
        &.slick-arrow-right {
            right: 0;
        }

        &.slick-arrow-left {
            left: 0;
        }

        @media (max-width: @screen-xs-max) {
            display: none!important;
        }
    }

    .products-slideshow {
        margin-bottom: 20px;
        font-family: @font-baseline;

        a {
            color: @black;
            text-decoration: none;
        }

        img {
            display: block;
            margin: auto;
        }

        .slick-list {
            @media screen and (min-width: @screen-sm) {
                margin: auto;
                width: 90%;
            }
        }
    }
}

section#categories {

    .row:not(.img-row) {
        overflow: inherit;
    }

    .desktop-category-container {
        margin: 0 0 50px;
    }

    span:not(.glyphicon) {
        font-family: @font-baseline;
    }

    .category-title-container {
        height: 30px;
        margin-bottom: 7px;

        &:hover > :not(.vert-delim),
        &.hovered > :not(.vert-delim) {
            color: @purple-redish;
        }

        a {
            text-decoration: none;
            color: @black;
        }
    }

    .category-title-separator {
        margin-top: 6px;
        border: 2px solid #000;
    }

    .featured-category {
        margin-bottom: 2em;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;

        @media screen and (min-width: @screen-lg-min) {
            margin-bottom: .5em;
        }

        & .featured-category-products {
            flex: 0 1 auto;
        }

        .featured-category-image {
            flex: 1 0 auto;
            max-height: 435px;

            // force height on large screens
            @media screen and (min-width: @screen-lg-min) {
                max-height: 345px;
            }

            &:hover, &.hovered {
                opacity: 0.75;
            }

            & .category-image {
                width: auto;
                max-height: 435px;

                // force height on large screens
                @media screen and (min-width: @screen-lg-min) {
                    max-height: 345px;
                }
            }

            // md
            @media screen and (max-width: @screen-md-max) {
                & .category-image {
                    width: 100%;
                }
            }
        }

        // sm
        @media screen and (min-width: @screen-sm-min) and (max-width: @screen-md-max) {
            .featured-category-image { flex: 1 1 75%; }
            .featured-category-products { flex: 1 1 25%; }
        }

        .featured-category-products {
            font-size: 10px;
            color: #000;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            flex-wrap: wrap;

            // align thumbnails area height with main category picture on large screens
            @media screen and (min-width: @screen-lg-min) {
                height: 345px; // same as category image height

                // align 3 last product cards on the bottom
                & > :nth-last-child(-n+3) {
                    align-self: flex-end;
                }
            }

            .ellipsis {
                height: 3em;
            }

            .product-card {
                text-align: center;
                margin-bottom: 1em;
                color: #000;

                @media screen and (min-width: @screen-lg-min) {
                    padding-left: .5em;
                    margin-bottom: 0;
                }

                .product-image {
                    width: 95px;
                    margin: 0 0 .75em 0;

                    @media screen and (max-width: @screen-md-max) {
                        width: 150px;
                    }

                    @media screen and (max-width: @screen-sm-max) {
                        width: 115px;
                    }
                }

                &:only-child .price-block {
                    width: 115%; // quick fix
                }
            }

            // desktop
            // each card is 1/3 of the total (3 cards on a row)
            & > * { flex: 0 0 33%; }

            // tablet
            // a card takes full space (direction column)
            @media screen and (max-width: @screen-md-max) {
                height: 450px;
                flex-direction: column;
                justify-content: space-between;
                align-items: center;
                flex-wrap: nowrap;

                .ellipsis {
                    height: 1.7em;
                }
            }

            @media screen and (max-width: @screen-sm-max) {
                height: 380px;
                flex-direction: column;
                justify-content: space-between;
                align-items: center;
                flex-wrap: nowrap;

                .ellipsis {
                    height: 1.4em;
                }
            }

            @media screen and (max-width: 1020px) {
                height: 410px;
            }

            @media screen and (max-width: 950px) {
                height: 380px;
            }

            @media screen and (max-width: 800px) {
                height: 280px;
            }
        }
    }

    .featured-category-row {
        .featured-category-products {
            height: auto;
            flex-direction: row;
            justify-content: space-between;

            & > * {
                flex: 0 0 25%;
            }
        }

        @media screen and (max-width: @screen-md-max) {
            .featured-category-products { flex: 0 0 100%; }
        }
    }

    h3 {
        font-size: 30px;
        font-family: @font-bebas;
        display: inline-block;
        margin: 0;
    }

    .category-top-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .category-title-container {
            margin: 0;
            display: flex;
            justify-content: space-between;
            align-items: baseline;
        }

        .category-buttons {
            display: flex;
            justify-content: space-between;
            align-items: baseline;
        }

        .vert-delim {
            color: #000;
            font-size: 26px;
        }
    }

    .category-description {
        margin-top: 10px;
    }

    .mobile-category-container {
        .mobile-single-cat {
            height: 45px;
            border-top: 1px solid @light-grey;

            a {
                text-decoration: none;
                color: @black;
            }

            div {
                line-height: 45px;

                .glyphicon { margin-top: 10px; }
            }

            span { font-size: 18px; }

            &.last {
                border-bottom: 1px solid @light-grey;
                margin-bottom: 15px;
            }
        }

        img {
            max-height: 35px;
            max-width: 35px;
            margin: 5px 0;
        }
    }
}

#show-cat-button, .see_new_products {
    margin: 15px auto 0;
    text-align: center;
}

section#CTAs {

    .cta-container{
        align-items: center;
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin-bottom: 20px;
    }

    .cta-1,
    .cta-2{
        align-items: stretch;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        @media screen and (min-width: @screen-desktop) {
            flex-direction: row;
        }
    }

    .cta-1{
        margin-bottom: 20px;

        @media screen and (min-width: @screen-desktop) {
            div:last-child {
                margin-left: 20px;
            }
        }

        @media screen and (max-width: @screen-desktop) {
            div:first-child {
                margin-bottom: 20px;
            }
        }

        #videoContainer {
            @media screen and (min-width: @screen-xs-min) and (max-width: @screen-sm-max) {
                min-height: 450px;

                iframe {
                    width: 100%;
                    min-height: 450px;
                    height: auto;
                }
            }
        }
    }

    .cta-2{

        @media screen and (max-width: @screen-desktop) {
            div:nth-child(2) {
                margin-bottom: 20px;
            }
        }
    }

    .right-cta { float: right; }

    .left-cta { float: left; }

    .img-cta { width: 59%; }

    .text-cta {
        padding: 10px;
    }

    .dark-cta {
        background: @black;

        a:hover { color: @purple; }
    }

    .light-cta {
        background: @purple;
        width: 39%;

        a:hover { color: @black; }
    }

    .sub-cta {
        color: @white;

        position: relative;

        img { max-width: 100%; }

        .cta-text-container {
            font-size: 1.15em;
            overflow: hidden;
        }

        h3{
            font-family: @font-bebas;
            margin-top: 0;
        }

        a {
            color: @white;
            font-weight: bold;
            float: right;

            &.cta-link {
                position: absolute;
                bottom: 10px;
                right: 10px;
            }

            &:hover{
                text-decoration: none;
            }
        }

        @media (max-width: @screen-md){
            width: 100%;
        }
    }

    .mobile-ctas {
        color: @black;

        .mobile-text-cta {
            width: 100%;
            margin: auto;

            .mobile-cta-text-container {
                font-size: 1.1em;
            }

            a {
                color: @black;
                float: right;
                text-decoration: none;
                font-weight: bold;
                font-size: 1.2em;
                margin: 10px auto;
            }
        }

        .mobile-cta-img-container {
            height: 162px;
            width: 100%;
            margin: 22px auto 30px auto;
            overflow: hidden;

            img {
                margin: auto;
                width: 100%;
            }

            @media screen and (max-width: @screen-xs-max) {
                height: auto;
            }
        }
    }
}



