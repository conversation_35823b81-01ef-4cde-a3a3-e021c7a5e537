@font-face {
  font-family: 'Century Gothic';
  src: url('@{font-path}century-gothic.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Century Gothic';
  src: url('@{font-path}century-gothic-bold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
}

@font-face {
    font-family: 'Bebas Kai';
    src: url('@{font-path}bebaskai-regular-webfont.eot');
    src: url('@{font-path}bebaskai-regular-webfont.woff2') format('woff2'),
    url('@{font-path}bebaskai-regular-webfont.woff') format('woff'),
    url('@{font-path}bebaskai-regular-webfont.ttf') format('truetype'),
    url('@{font-path}bebaskai-regular-webfont.svg#kautiva_probold') format('svg');
    font-weight: 600;
    font-style: normal;
}

@font-face {
    font-family: 'Waiting';
    src: url('@{font-path}WaitingfortheSunrise.ttf');
    font-weight: 600;
    font-style: normal;
}