// Font variables
@font-userbar: Helvetica, Arial, Verdana, sans-serif;
@font-baseline: 'Century Gothic', Helvetica, Arial, Verdana, sans-serif;
@font-bebas: 'Bebas Kai', Helvetica, Arial, Verdana, sans-serif;

@normal: 400;
@bold:600;
@black: 900;
@font-size-base: 14px;
@font-path: "../fonts/";

// Colors
@black: #030404;
@darker-gray: #313131;

@purple: #86133a;
@purple-redish: #bc1851;

@light-grey: #d2d2d2;
@white: #fff;

// Media queries breakpoints
@screen-xs: 480px;
@screen-sm: 768px;
@screen-md: 1024px;
@screen-lg: 1240px;

//border radiuses
@border-radius-base: 5px;

//** Padding between columns.
@grid-gutter-width: 12px;

// overlay z-index
@index-overlay: 9;

.container {
    max-width: 990px;
}
@media (min-width: @screen-lg-min) {
    .container {
        width: @screen-lg;
    }
}
@media (max-width: @screen-md-max) {
    .container {
        width: @screen-md;
    }
}

@media (max-width: @screen-sm-max) {
    .container {
        width: 100%;
        max-width: 100%;
    }
}
