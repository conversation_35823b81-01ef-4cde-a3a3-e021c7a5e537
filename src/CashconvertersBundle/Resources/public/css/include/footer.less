footer {
    #footer{
        margin-top: 1em; // so at least page content cannot touch footer
        background: @black;
        color: @white;
    }
    .black-footer{
        align-items: stretch;
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        a {
            color: @white;
            text-decoration: none;
            flex: 1;

            &:hover{
                background-color: @white;
                color: @black;

                .white-icon{
                    display: none;
                }

                .black-icon{
                    display: block;
                }
            }
        }

        .black-footer-delivery {
            cursor: pointer;

            &:hover {
                background-color: @white;
                color: @black;

                a {
                    background-color: @white;
                    color: @black;

                    &.click-collect:hover {
                        text-decoration: underline;
                    }
                }

                .white-icon{
                    display: none;
                }

                .black-icon{
                    display: block;
                }
            }
        }

        &-item{
            align-items: stretch;
            display: flex;
            flex-direction: column;
            flex: 1;
            height: 100%;
            justify-content: flex-start;
            padding: 10px;

            &-title{
                align-items: center;
                display: flex;
                flex-direction: row;
                justify-content: flex-start;
                margin-bottom: 10px;
                font-family: @font-bebas;
                font-size: 1.3em;
                font-weight: 900;

                .white-icon,
                .black-icon{
                    width: 40px;
                    margin-right: 5px;
                }

                .black-icon{
                    display: none;
                }
            }
        }
    }

    #bottom-footer {
        margin-top: 30px;

        a {
            color: @black;

            &:hover {
                color: @purple;
                text-decoration: none;
            }
        }

        .footer-cat-title {
            font-family: @font-bebas;
            font-size: 14pt;

            @media (max-width: @screen-xs) {
                margin: 0;
            }
        }

        #follow-facebook {
            font-size: 1em;

            .facebook-container{
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;

                @media screen and (min-width: @screen-tablet) {
                    flex-direction: row;
                }

                img {
                    float: left;
                    height: 55px;
                    margin-top: -10px;
                }

                #follow-us-text {
                    font-family: Waiting;
                    text-align: center;
                    margin: 0;

                    @media screen and (min-width: @screen-tablet) {
                        text-align: left;
                    }
                }
            }
        }

        hr {
            margin: 11px 10%;
            width: 80%;

            @media (min-width: @screen-xs) {
                display: none;
            }
        }

        .footer-left-line {
            border-left: 1px solid @black;
            font-size: 12px;
            text-align: left;
        }

        #copyright {
            margin-top: 30px;
            font-family: @font-baseline;
        }

        /* MOBILE */
        @media (max-width: @screen-sm) {
            text-align: center;

            #follow-facebook {
                img {
                    float: initial;
                    margin-bottom: 9px;
                }

            }

            .footer-left-line {
                border-left: 0;
            }

            #copyright {
                font-size: 10pt;
            }

            .footer-link {
                margin-top: 12px;
            }

        }

        /* TABLETTE */
        @media screen and (min-width: @screen-tablet) {
            .footer-left-line {
                border-left: 0;
            }
        }
    }
}
