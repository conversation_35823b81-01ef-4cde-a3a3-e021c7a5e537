.checkout-process {
    min-height: 800px;

    label{
        font-weight: 400;
    }

    a {
        color: @black;
        text-decoration: underline;

        &:hover {
            color: @purple;
            text-decoration: underline;
        }
    }

    .checkout-steps-breadcrumb {
        .mobile-list {
            margin: 15px 5% 0 5%;

            .checkout-breadcrumb {
                width: 10%;
                height: 44px;
                line-height: 44px;

                &:not(.active) {
                    border: 1px solid @light-grey;
                }

                &.active {
                    border: 1px solid @black;
                    width: 66%;
                    background: @black;
                    color: white;
                }
            }
        }

        .checkout-breadcrumb {
            font-family: @font-bebas;
            font-weight: bold;
            font-size: 24px;
            color: @light-grey;
            height: 80px;
            line-height: 80px;

            &.active { color: @black }
        }
    }

    section.basket {
        min-height: 800px;

        .basket-items { margin-bottom: 4em }

        .product-group {
            .product {

                margin-top: 10px;

                input.product-quantity {
                    width: 30px;
                    border-radius: 5px;
                    border: 1px solid;
                    text-align: right;
                    padding-right: 5px;
                }

                .basket-single-price {
                    margin-right: -7px;
                }

                img {
                    max-height: 200px;
                }
            }
        }

        hr { margin: 0 0 10px }

        .basket-select-shipping {
            clear: both;
            width: 100%;
            padding: 20px 0;
            text-align: right;

            &-inside {
                width: 50%;
                padding-left: 5px;
                display: inline-block;
                text-align: left;
                position: relative;
                @media(max-width: @screen-xs-max) { width: 100% }
            }

            strong {
                display: block;
                margin-bottom: 10px;
                font-size: 20px;
                font-family: 'Bebas Kai', Helvetica, Arial, Verdana, sans-serif;
                @media(max-width: @screen-xs-max) { text-align: center }
            }
        }

        .basket-radio {

            &-inside {
                position: relative;
                overflow: hidden;
            }

            &-input {
                position: absolute;
                left: -20px;

                &:checked + label::before { background-image: url(../images/radio_checked.png) }
            }

            &-label {
                padding-left: 25px;
                cursor: pointer;

                &::before {
                    content: "";
                    position: absolute;
                    left: 0;
                    top: 1px;
                    width: 16px;
                    height: 16px;
                    background: transparent url(../images/radio.png) no-repeat center;
                    background-size: 100%;
                }
            }
        }

        #delivery-time { float: right }

        .reinsurance {
            img {
                height: 20px;
            }

            @media(max-width: @screen-xs-max) {
                img {
                    height: 40px;
                    margin-top: 40px;
                }

                text-align: center;
            }
        }

        .pickup-points {
            font-size: 1.1em;
            padding: 1em;
            margin: 1em 0;
            border: 2px solid @purple-redish;
            text-align: center;
        }
    }

    section.checkout-payment {
        .payment-means {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1.5em;

            @media screen and (max-width: 480px) {
                flex-direction: column;
            }

            .payment-mean {
                .radio {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    padding-left: 0;
                    padding-top: 24px;

                    @media screen and (max-width: @screen-xs-max) {
                        flex-direction: row;
                        padding-left: 24px;
                        padding-top: 0;
                    }
                }

                .custom-radio {
                    transform: translateX(-50%);
                    left: 50%;

                    @media screen and (max-width: @screen-xs-max) {
                        transform: translate(0, -50%);
                        left: 0;
                        top: 50%;
                    }
                }

                img {
                    width: 55px;
                    height: 35px;
                }

                [type="radio"] {
                    position: relative;
                    margin-left: 0;
                }
            }
        }

        button {
            width: 100%;
            font-size: 1.3em;
        }
    }

    .vat {
        font-size: 13px;
    }


    h2.title {
        font-size: 20px;
        margin-top: 0;
        font-family: @font-bebas;

        @media (max-width: @screen-sm) {
            text-align: center;
        }
    }

    .payment-recap {
        margin-bottom: 30px;
    }

    .btn {
        margin: 20px 0;
        text-decoration: none;
    }

    @media (max-width: @screen-sm) {
        .btn {
            text-align: center!important;
            margin-left: auto!important;
            margin-right: auto!important;
        }
    }

    .select-container {
        border: 1px solid @black;
        border-radius: 5px;

        select {
            width: 100%;
        }

        span {
            background: @black;
            color: @white;

            position: absolute;
            top: 0;
            right: 0;
            padding: 0 5px;
            line-height: 20px;

            z-index: -1;
        }
    }

    .checkout-login {
        label,
        button {
            width: 100%;
        }
    }
}


/** Checkout "global" classes **/

h2.checkout-main-title {
    color: @purple-redish;
    font-size: 18px;
    font-weight: 800;
    font-family: @font-baseline;
}

hr.checkout-main-title-under {
    border-color: @light-grey;
}

/* complete : after checkout */
.tygh-content {

    @media screen and (max-width: @screen-desktop){
        padding: 0 10px;
    }

    .basket-summary {

        .order-summary{

            margin: 40px 0;

            .title{
                font-family: @font-bebas;
                margin-top: 50px;

                @media screen and (max-width: @screen-tablet){
                    text-align: center;
                }
            }
        }
    }

    .main-title{
        font-family: @font-bebas;
        margin: 50px 0 20px;
    }

    a {
        color: @black;
        text-decoration: underline;

        &:hover {
            color: @black;
            text-decoration: underline;
        }
    }
}

.chrono-relais {
    &.chrono-relais-modal {
        .modal-header, .modal-footer {
            padding: 2rem;
        }

        .modal-footer .btn {
            padding: .5rem 1.5rem;
        }
    }

    .google-map {

        // update address form
        .user-inputs {
            margin-bottom: 2em;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: flex-start;

            // Address field
            & > :first-child {
                width: 100%;
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: space-between;
                margin-bottom: .5em;

                & [type="text"] {
                    margin-left: 1em;
                }
            }

            // Zipcode and city fields
            & > :nth-child(2) {
                width: 100%;
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                align-items: flex-end;

                // override defaults
                .btn {
                    margin: 0;
                    padding: .25em .65em;
                    height: auto;
                    font-size: 1.22em;

                    &:hover {
                        background-color: darken(@light-grey, 5%);
                    }
                }
            }
        }
    }

    // pickup locations
    .pickup-locations {
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        // pickup list
        & > :first-child {
            position: relative; // used for animation

            max-height: 400px;
            padding-right: .5em;
            overflow-y: auto;

            flex: 1 0 45%;
            margin-right: 1em;
            font-size: .9em;

            @media screen and (max-width: @screen-sm-max) {
                flex: 1 0 30%;
            }

            @media screen and (max-width: @screen-xs-max) {
                font-size: 1.1em;
            }

            .pickup {
                position: relative; // used for animation

                padding-bottom: .5em;
                border-bottom: 1px solid rgba(0,0,0,.05);
                margin-bottom: 1em;

                .pickup-name {
                    font-weight: bold;
                    margin-bottom: .25em;
                }

                .distance {
                    margin-bottom: .5em;
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;

                    & > :first-child {
                        display: flex;
                        flex-wrap: wrap;
                    }

                    .pickup-distance {
                        color: @purple-redish;
                        justify-self: flex-end;
                    }
                }

                .opening-times-group {
                    .title {
                        font-weight: bold;
                        text-decoration: underline;
                        margin-bottom: .25em;
                    }

                    .opening-times {
                        display: flex;
                        flex-direction: column-reverse; // chronopost api returns week days in reverse

                        .week-day {
                            text-decoration: underline;
                        }
                    }
                }

                .btn {
                    margin: .5em 0;
                    height: auto;
                    font-size: 1.2em;

                    &.active {
                        color: #fff;
                        background-color: @purple;
                    }

                    @media screen and (max-width: @screen-xs-max) {
                        width: 100%;
                    }
                }
            }
        }

        #map {
            flex: 1 1 auto;

            @media screen and (max-width: @screen-xs-max) {
                display: none;
            }
        }
    }

}

.modal-body .product-image img {
    max-height: 200px;
}

.prices {
    border: 4px solid @light-grey;
    padding: 10px 5px;

    .price-tag {
        float: right;

        &.price {
            font-size: 25px;
        }
    }
}

#click-collect-modal {
    text-align: center;
    font-size: 1.6rem;

    .modal-body {
        margin-bottom: 3.5rem;
    }

    .warning {
        margin: 2.5rem 0 1rem;
        font-weight: bold;
        text-transform: uppercase;
    }

    #click-collect-warning-message {
        margin-bottom: 1.75rem;
    }
}

