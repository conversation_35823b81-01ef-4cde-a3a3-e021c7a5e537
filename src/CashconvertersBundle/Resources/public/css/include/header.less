header {

    #header-bandeau {
        background: @black;
        text-align: center;
        color: @white;
        padding: 2px 5px;
        font-size: 12px;
        a {
            color: @white;
            text-decoration: underline;
        }
    }
    #user-bar {
        color: @white;
        background: @black;
        font: @font-userbar;

        padding: 5px 0;
        font-size: 15px;
        width: 100%;
        z-index: 100;

        &.is-scrolled {
            position: fixed;
            top: 0;
            .container { animation: appear .15s }
        }

        .slim-header{
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-direction: row;

            #top-search-bar {
                height: 35px;
                position: relative;
                flex: 0 1 50%;

                .btn:hover {
                    background-color: @purple;
                    color: white;
                }

                .product-search-input {
                    @keyframes appear {
                        from {
                            opacity: 0;
                        }

                        to {
                            opacity: 1;
                        }
                    }

                    .search, .btn {
                        height: 35px;
                    }

                    .suggestions{
                        position: absolute;
                        width: 100%;
                        background: @white;
                        border: 1px solid black;

                        ul{
                            margin: 0;
                            text-align: center;

                            a {
                                white-space: nowrap;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                width: 100%;

                                text-decoration: none;
                                color: @black;
                                display: block;
                                padding: 5px 0;
                                &:hover {
                                    background: @purple;
                                    color: @white;
                                }
                            }
                        }
                    }
                }
            }
        }

        .connection-basket {
            flex: 0 0 auto;
            position: relative;

            .userbar-link {
                color: @white;

                &:hover {
                    color: @white;
                }
            }

            .userbar-icon {
                height: 15px;
            }

            .basket-dropdown,
            .account-dropdown {
                display: inline-block;
                position: relative;
                z-index: 11;

                &> a{
                    color: @white;

                    img{
                        height: 15px;
                        margin-bottom: 5px;
                    }
                }

                ul{
                    padding: 0 5px;
                    list-style: none;
                    margin: 0;

                    li{
                        padding-top: 5px;

                        &:last-child{
                            padding-bottom: 5px;
                        }

                        a{
                            display: block;
                            color: @black;
                            text-align: center;
                        }
                    }
                }

                &-menu {
                    color: @black;

                    .dropdown {
                        right: 0;
                        z-index: 10;
                        max-height: 60vh;
                        overflow: auto;

                        .empty {
                            display: flex;
                            align-items: center;
                            justify-content: center;

                            p{
                                margin: 0;
                                text-align: center;
                            }
                        }
                    }
                }
            }

            .basket-dropdown-menu {
                .actions {
                    .action {
                        position: relative;
                    }
                }
            }
        }

        #menu-elem-lang {
            .header-flag {
                height: 20px;
                padding-bottom: 4px;
            }

            .glyphicon-menu-down {
                font-size: 10px;
            }
        }

        #menu-elem-lang {
            display: inline-block;
            cursor: pointer;

            span:nth-child(2) {
                padding-right: 5px;
            }
        }

        #lang-btn {
            border: solid 1px @white;
            margin-top: 20px;
        }
    }

    #header-main {
        color: @white;
        background: @black;
        padding: 25px 0 10px;

        img {
            height: 100px;
            margin-left: 6px;
        }

        #header-inputs-text {
            float: right;
            white-space: nowrap;

            a {
                color: @black;
                display: block;
                padding: 5px 0;
                &:hover {
                    background: @purple;
                    color: @white;
                }
            }

            width: calc(~"100% - 280px");
            margin-top: 27px;
            margin-right: 6px;
            font-size: .9em;

            @media screen and (min-width: @screen-desktop) {
                font-size: 1.2em;
            }

            font-weight: 900;
            letter-spacing: 0.5px;

            #header-inputs{
                margin-top: 19px;
                position: relative;
                overflow: hidden;

                .search, .btn {
                    height: 35px;
                }

                .reset-geoloc {
                    color: @black;
                    position: absolute;
                    font-size: 1.5em;
                    right: .5em;
                    top: 0;
                    cursor: pointer;
                }

                .autocomplete {
                    background: @white;
                    position: absolute;
                    width: 88%;
                    top: 34px;
                    left: 10px;
                    z-index: 10;

                    a {
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        width: 100%;

                        text-decoration: none;
                        font-weight: 400;
                    }
                }

                .btn:hover {
                    color: #fff;
                    background-color: @purple;
                }
            }

            #header-text {
                font-family: @font-baseline;
                font-weight: 100;
                bottom: 10px;
                height: 50px;
            }
        }
    }

    #header-menu {
        background: @purple;
        color: @white;

        height: 40px;
        margin-top: 4px;
        position: relative;

        .menu-category-list{
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;

            margin: 0;
        }

        .menu-category {
            height: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            flex: 1;

            a {
                padding: 5px 10px;
                text-align: left;
                font-size: 12px;
            }

            &.active, &:hover {
                background: @black;
            }

            > a, > span.menu-link {
                font-family: @font-bebas;
                font-size: 23px;
                color: white;
                cursor: pointer;

                &:hover, &:active {
                    text-decoration: none;
                }
            }

            &:last-child {
                .dropdown-content.large-content {
                    right: 0;
                    left: auto;
                }
                .dropdown-lvl2 {
                    left: auto;
                    right: 100%;
                }
            }
        }
    }

    #mobile-header {
        background: @black;
        color: @white;
        font-family: @font-baseline;

        height: 106px;
        padding: 6px 0;

        #mobile-menu-button {
            width: 50px;

            img {
                width: 100%;
                cursor: pointer;
            }
        }

        #mobile-header-top {
            height: 50%;
            position: relative;

            p {
                margin: 0;
                font-size: 10.46px;
            }

            #mobile-localize {
                span{
                    font-size: 33px;
                }
                #localize {
                    float: right;
                    margin-right: 6px;
                }

            }

            #mobile-logo {
                img {
                    height: 47px;
                }
                &.col-xs-6 { width: auto }
            }

            #mobile-cart {

                &:hover {
                    #cart-before-hover {
                        display: none;
                    }
                    #cart-after-hover {
                        display: inline;
                    }
                    p {
                        color: @purple;
                    }
                }

                #cart-after-hover {
                    display: none;
                }

                img {
                    height: 24px;
                }

                p {
                    color: @white;
                    font-size: 17px;
                    margin-left: 4px;
                }
            }

        }

        #mobile-header-bot {
            padding: 8px 0;
            height: 50%;

            input, button {
                height: 40px;
            }

            input {
                font-size: 20px;
            }

            .btn-search {
                padding: 0 7px;

                span {
                    font-size: 34px;
                }
            }

            #mobile-localize {
                float: right;
                margin-right: 6px;
            }

            .autocomplete {
                background: @white;
                position: absolute;
                width: 88%;
                top: 40px;
                left: 10px;
                border-top: none;
                z-index: 10;

                a {
                    color: @black;
                    display: block;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    text-align: center;
                    width: 100%;
                    padding: 5px 0;

                    text-decoration: none;
                    font-weight: 400;

                    &:hover{
                        background: @purple;
                        color: @white;
                    }
                }
            }
        }

        button {
            &:hover {
                background: @purple;
                span {
                    color: @white;
                }
            }
        }
    }

    /* input placeholders */
    .form-control::-webkit-input-placeholder {
        color: @black;
        font-style: italic;
        font-weight: 400;
    }

    /* DROPDOWNS */
    .menu-category:hover .dropdown-content {
        visibility: visible;
        opacity: 1;
    }

    .dropdown-content {
        background: @black;
        display: block;
        position: absolute;
        top: 40px;
        left: 0;
        z-index: 99;
        visibility: hidden;
        opacity: 0;
        transition: opacity .2s ease;
        width: 100%;

        &.large-content {
            width: 100%;
        }

        ul {
            padding: 0;
            list-style-type: none;
            display: flex;
            flex-direction: column;

            &.large-content {
                flex-direction: row;
                flex-wrap: wrap;

                // this "after" is here to set the last item on the left (and not centered)
                &:after {
                    content: "";
                    flex: auto;
                }
            }

            li {
                &.large-content {
                    width: 50%;
                }

                a
                {
                    color: @white;
                    display: block;
                    padding: 5px 0;
                }
            }
        }

        .display-dropdown-lvl2 {
            position: relative;
            padding-right: 5px;

            &::after {
                content: "";
                position: absolute;
                top: 50%;
                right: 5px;
                width: 0;
                height: 0;
                margin-top: -4px;
                border-top: 4px solid transparent;
                border-bottom: 4px solid transparent;
                border-left: 4px solid #fff;
            }

            &:hover .dropdown-lvl2 {
                visibility: visible;
                opacity: 1;
            }

            .dropdown-lvl2 {
                display: block;
                background: @black;
                position: absolute;
                left: 100%;
                width: 100%;
                top: 0;
                visibility: hidden;
                opacity: 0;

                transition: .2s ease;
            }
        }

        .active {
            text-decoration: underline;
            font-weight: bold;
        }

        button {
            height: 30px;
            padding: 2px 7px;
        }
    }

    .dropdown {
        position: absolute;
        right: 10px;
        width: 300px;
        border-radius: 4px;
        background-color: #fff;
        z-index: 5;
        text-align: left;
        box-shadow: 0 2px 6px -1px rgba(0, 0, 0, 0.4);

        .product {
            padding: 15px;

            .product-info {

                .product-name {
                    display: block;
                }

                .product-attributes {
                    font-size: 15px;
                }

                .product-price {
                }

                .product-delete {
                    padding-top: 4px;
                    float: right;
                    font-size: 14px;

                    &:hover {
                        font-weight: 800;
                        color: @black;
                    }
                }
            }
        }

        .login-actions{
            .forget-password-link{
                color: @black;
                display: block;
                text-align: right;
            }
            .btn{
                width: 100%;
            }
            h3 {
                margin: 15px 0 0;
            }

            .form-group {
                padding: 15px;
                input{
                    width: 100%;
                }
            }
        }
    }
}

/* Filter Seller Component in MasterHead */
#filter-seller-show-modal {
    display: none;
    width: 45px;
    height: 45px;
    padding: 5px;
    position: absolute;
    top: 0;
    right: 65px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #fff;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);

    svg { width: 100% }

    path { fill: #333 }
}

#filter-seller-hide-modal {
    display: none;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
    width: 70px;
    height: 70px;

    span {
        position: absolute;
        top: 50%;
        left: 50%;
        color: #fff;
        font-weight: bold;
        font-size: 35px;
        line-height: 35px;
        transform: translate(-50%, -50%);
    }
}

#filter-seller {
    position: relative;
    z-index: 2;
    background: #030404;

    &.is-scrolled {
        width: 100%;
        position: fixed;
        top: 45px;
        left: 0;

        .filter-seller-inside {
            max-width: 980px;
            padding: 0;
        }

        .filter-seller-label { display: none }

        .filter-seller-items span { animation: appear .2s }
    }
}

.filter-seller {

    &-inside {
        max-width: 990px;
        width: 100%;
        margin: 0 auto;
        padding: 0 72px 10px 0;
        text-align: right;
        font-size: 0;
    }

    &-label {
        width: 224px;
        display: inline-block;
        vertical-align: top;
        margin-left: 12px;
        position: relative;
        z-index: 1;
        border: 1px solid #ccc;
        border-radius: 5px;
        background: #fff;
        box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
        text-align: left;

        span {
            display: block;
            height: 32px;
            padding: 7px 15px 0;
            color: #030404;
            font-style: italic;
            font-size: 14px;
            position: relative;
            z-index: 2;
            cursor: pointer;
            border-top-left-radius: 5px;
            border-bottom-left-radius: 5px;

            &::first-letter { text-transform: capitalize }
        }

        ul {
            display: none;
            max-height: 170px;
            height: auto;
            overflow-x: hidden;
            overflow-y: auto;
            margin: 0;
            padding: 0;
            position: absolute;
            z-index: 1;
            top: 32px;
            right: 3px;
            left: 3px;
            list-style-type: none;
            text-align: left;
            background: #fff;
            border-bottom-right-radius: 5px;
            border-bottom-left-radius: 5px;
            border-right: 1px solid #ccc;
            border-bottom: 1px solid #ccc;
            border-left: 1px solid #ccc;
            box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
        }

        li {
            display: block;

            span,
            a {
                display: block;
                height: auto;
                padding: 8px 12px;
                color: #030404;
                font-size: 12px;
                font-weight: bold;
                cursor: pointer;
                text-decoration: none;
                border-bottom: 1px solid #ccc;
                border-radius: 0;
                font-style: normal;

                &:hover {
                    color: #fff;
                    background-color: #86133a;
                }
            }

            &:last-child {

                span, a { border: 0 }
            }
        }

        &.active {

            ul { display: block }
        }
    }

    &-clear {
        width: 26px;
        height: 32px;
        position: absolute;
        top: 0;
        right: 0;
        z-index: 3;
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;
        cursor: pointer;

        span {
            height: auto;
            padding: 0;
            position: absolute;
            top: 50%;
            left: 50%;
            color: #030404;
            font-weight: bold;
            font-size: 25px;
            font-style: normal;
            line-height: 25px;
            transform: translate(-50%, -50%);
        }

        &:hover {
            color: #fff;
            background-color: #86133a;

            span { color: #fff }
        }
    }

    &-items {
        display: inline-block;
        vertical-align: top;
        position: relative;
        z-index: 1;

        span {
            display: inline-block;
            margin: 0 0 5px 5px;
            padding: 2px 22px 2px 8px;
            border: 1px solid #ccc;
            border-radius: 5px;
            font-size: 10px;
            color: #333;
            background-color: #fff;
            box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
            position: relative;
            cursor: pointer;

            &:hover {
                color: #fff;
                background-color: #86133a;

                em::before { color: #fff }
            }
        }

        em {
            width: 10px;
            height: 100%;
            position: absolute;
            top: 0;
            right: 5px;

            &::before {
                content: "×";
                position: absolute;
                top: 50%;
                left: 50%;
                color: #030404;
                font-style: normal;
                font-weight: bold;
                font-size: 18px;
                line-height: 18px;
                transform: translate(-50%, -50%);
            }
        }
    }
}

@media screen and (max-width: @screen-tablet) {

    #filter-seller-show-modal { display: block }

    #filter-seller-hide-modal { display: block }

    #filter-seller {
        display: none;
        width: 100%;
        height: 100%;
        position: fixed;
        top: 0;
        left: 0;
        background: #86133a;
        overflow-x: hidden;
        overflow-y: auto;

        &.active { display: block }
    }

    .filter-seller {

        &-inside {
            max-width: none;
            height: 100vh;
            padding: 0;
        }

        &-label {
            width: 100%;
            margin: 0;
            padding: 20px 20px 80px;
            box-shadow: none;
            background: none;
            border: 0;

            ul {
                display: block;
                height: auto;
                border: 0;
                position: static;
                box-shadow: none;
                background: none;
                text-align: center;
            }

            li {
                position: relative;

                span,
                a {
                    border: 0;
                    border-bottom: 1px solid #fff;
                    font-size: 16px;
                    color: #fff;
                    font-style: normal;
                    padding: 10px 20px;
                }
            }

            span,
            a {
                height: auto;
                padding: 0 20px 20px;
                box-shadow: none;
                background: none;
                border: 0;
                border-bottom: 1px solid #fff;
                border-radius: 0;
                text-align: center;
                line-height: 30px;
                font-size: 25px;
                font-style: normal;
                font-weight: bold;
                color: #fff;
            }
        }

        &-clear,
        &-items { display: none }
    }
}

