.user-account {

    margin: 50px 0;

    @media screen and (max-width: @screen-desktop) {
        margin: 50px 20px;
    }

    .breadcrumb{
        padding: 0;
    }

    .shipping-address-block{
        display: none;
    }

    a {
        color: @black;
        text-decoration: none;

        &:hover {
            color: @black;
        }
    }

    label.checkbox{

        display: flex;
        align-items: center;

        input[type=checkbox]{
            position: relative;
            margin: 0;
        }
    }

    .profile-btn{
        text-transform: uppercase;
        width: 100%;
        padding: 5px 0;
        margin-top: 20px;
    }

    .input-group {
        .glyphicon {
            top: 0;
        }
    }

    .profile-page{

        @media screen and (min-width: @screen-tablet) {
            padding-left: 40px;
        }

        .profile-content-block{
            padding: 20px 0;
        }
    }

    .side-menu {

        .menu-link{

            a{
                &:hover{
                    color: @purple;
                }
            }
        }

        .logout-link {
            .menu-title {
                background-color: #000;
            }

            a {
                display: block;
                color: #fff;
            }
        }

        .menu-title {
            padding: .25em;
            background: @purple;
            color: @white;
            font-family: @font-bebas;
            width: 100%;
            text-align: left;
            font-size: 26px;
            line-height: 1em;
        }

        a {
            font-weight: 400;
        }
    }
}

.recover-password-title {
    text-indent: 0;
}

.required::after {
    content: '*';
    color: @purple-redish;
}

#generic_modal button {
    color: @white;
    background: @purple;
    border: 1px solid @purple;
    font-weight: 200;
    font-family: @font-baseline;
    font-size: 17px;
    border-radius: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: 0.15s ease;

    &:hover {
        background: @white;
        color: @purple;
    }
}

.rma-register a {
    color:#86133a!important;
}

.discuss-create-message {
    textarea {
        margin-bottom: 1.5rem;
    }
}
