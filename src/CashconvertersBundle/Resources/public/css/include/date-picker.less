#ui-datepicker-div {
    box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.3);
    display: none;
    margin: 5px auto 0;
    height: auto;
    width: 300px;

    a {
        text-decoration: none;
    }

    .ui-datepicker-header {
        background: @purple;
        color: #fff;
        font-size: 14px;
        font-weight: 600;
        font-family: @font-baseline;

        select {
            height: 28px;
        }

        .ui-datepicker-title {
            text-align: center;
        }

        .ui-state-hover, .ui-widget-content .ui-state-hover, .ui-widget-header .ui-state-hover, .ui-state-focus, .ui-widget-content .ui-state-focus, .ui-widget-header .ui-state-focus, .ui-button:hover, .ui-button:focus {
            background: transparent;
            border: 0;
            font-size: 0;
        }

        .ui-datepicker-month, .ui-datepicker-year {
            color: #fff;
            font-size: 13px;

            option {
                color: @black;
            }
        }

        .ui-datepicker-prev, .ui-datepicker-next {
            position: relative;
        }

        .ui-datepicker-prev span, .ui-datepicker-next span { display: none; }

        .ui-datepicker-prev:before {
            content: "\e257";
            color: #fff;
            cursor: pointer;
            float: left;
            font-family: 'Glyphicons Halflings';
            font-size: 14px;
            font-weight: normal;
            margin: -1px 0 0 3px;
            padding: 6px 0;
        }

        .ui-datepicker-next:before {
            content: "\e258";
            color: #fff;
            cursor: pointer;
            float: right;
            font-family: 'Glyphicons Halflings';
            font-size: 14px;
            font-weight: normal;
            margin: -1px 3px 0 0;
            padding: 6px 0;
        }
    }

    .ui-datepicker table {
        font-family: @font-baseline;
        font-weight: normal;
        width: 100%;


        thead {
            background-color: #f7f7f7;
            border-bottom: 1px solid #bbb;

            th {
                color: #1a242b;
                font-size: 12px;
                padding: 5px 0;
                text-transform: uppercase;
                text-align: center;
            }
        }
        tbody {
            tr {
                border-bottom: 1px solid #bbb;

                td {
                    padding: 0;
                    border-right: 1px solid #bbb;

                    span, a {
                        color: #1a242b;
                        display: inline-block;
                        font-weight: bold;
                        height: 34px;
                        line-height: 34px;
                        text-align: center;
                        width: 100%;
                    }
                    span {
                        background: #ccc;
                    }
                    &:last-child {
                        border-right: 0;
                    }
                }
                &:last-child {
                    border-bottom: 0;
                }
            }
        }
    }

    .ui-datepicker-calendar {
        .ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default, .ui-button, html .ui-button.ui-state-disabled:hover, html .ui-button.ui-state-disabled:active {
            border: 0;
            font-weight: bold;
        }
        .ui-state-default {
            background: #ededed;
        }
        .ui-state-hover {
            background: #f7f7f7;
        }
        .ui-state-highlight {
            background: #ededed;
            color: #454545;
        }
        .ui-state-active {
            background: @purple;
            color: #fff;
            position: relative;
        }
        .ui-state-disabled {
            .ui-state-default {
                background: #ccc;
                color: #6a7379;
            }
        }

        tr:last-child .ui-state-active {
            height: 34px;
            margin-bottom: 0;
        }
        td:first-child .ui-state-active {
            margin-left: 0;
        }
        td:last-child .ui-state-active {
            margin-right: 0;
        }
    }
}
