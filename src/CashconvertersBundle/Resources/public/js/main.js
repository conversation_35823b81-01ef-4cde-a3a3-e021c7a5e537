// Change VueJS delimiters to avoid conflicting with Twig
Vue.config.delimiters = ['${', '}'];

var W = {
    // For search results
    toggleLoadingBox: function toggleLoadingBox (action) {
        $('.ajax-loading-box').toggle(action);
    },
    truncate: function truncate (string, length) {
        if (string && string.length > length) {
            string = string.substring(0, length-1)+'…';
        }
        return string;
    },
    formatPrice: function formatPrice (price) {
        if (!$.isNumeric(price)) {
            return '';
        }
        //fix firing of "toFixed is not a function"
        price = parseFloat(price);

        price = price.toFixed(2) + '';
        return price.replace('.', ',');
    },
    generateSlug: function (string) {
        // Replace anything that isn't a word character by an underscore
        return string.replace(/\W/g,'_');
    },
    /**
     * Render the ratings with stars
     */
    renderRatings: function () {
        $('.js-rating').rating({
            min: 0,
            max: 5,
            step: 1,
            size: 'sm',
            showClear: false,
            showCaption: false
        });
    },

    areObjectsEquivalents: function(a, b) {
        // Create arrays of property names
        var aProps = Object.getOwnPropertyNames(a);
        var bProps = Object.getOwnPropertyNames(b);

        // If number of properties is different,
        // objects are not equivalent
        if (aProps.length !== bProps.length) {
            return false;
        }

        for (var i = 0; i < aProps.length; i++) {
            var propName = aProps[i];

            // If values of same property are not equal,
            // objects are not equivalent
            if (a[propName] !== b[propName]) {
                return false;
            }
        }

        // If we made it this far, objects
        // are considered equivalent
        return true;
    }
};


$(function() {
    var nProds = 5;
    if (window.innerWidth < 1024) nProds = 4;
    if (window.innerWidth < 768) nProds = 3;


    //homepage news slideshow (Banner)
    $('#news-slideshow').css('display', '');
    $('#news-slideshow-placeholder').css('display', 'none');
    $('#news-slideshow').slick({
        slidesToShow: 1,
        slidesToScroll: 1,
        autoplay: true,
        autoplaySpeed: 4000,
        prevArrow: "<span id='news-prev-arrow' class='glyphicon glyphicon-menu-left slick-arrow-left slick-arrow-white'></span>",
        nextArrow: "<span id='news-next-arrow' class='glyphicon glyphicon-menu-right slick-arrow-right slick-arrow-white'></span>",
        dots: true,
        lazyLoad: 'ondemand'
    });

    // products slideshow
    $('.products-slideshow').css('display', '');
    $('.products-slideshow').slick({
        slidesToShow: nProds,
        slidesToScroll: 1,
        autoplay: true,
        autoplaySpeed: 2000,
        prevArrow: "<span id='news-prev-arrow' class='glyphicon glyphicon-menu-left slick-arrow-left slick-arrow'></span>",
        nextArrow: "<span id='news-next-arrow' class='glyphicon glyphicon-menu-right slick-arrow-right slick-arrow'></span>",
        lazyLoad: 'ondemand'
    });

    //hover on home img in categories toggles hover on title and vice-versa
    $('.category-title-container').hover(function(){
        $(this).parent().siblings().find('.main-img').toggleClass('hovered');
    });
    $('.main-img').hover(function(){
        $(this).parentsUntil('.container').find('.category-title-container').toggleClass('hovered');
    });

    //change hide/sho details buttons
    $('.show-hide-btn').click(function () {
        $(this).children('span.glyphicon').toggleClass('glyphicon-menu-down');
        $(this).children('span.glyphicon').toggleClass('glyphicon-menu-up');
    });

    // scrollbar behaviour on page load
    $('.scrollbar-inner').scrollbar();
    $('.scrollbar-macosx').scrollbar();

    //=======
    // Modals

    // add modals to document body
    $('#basket-popup').appendTo('body');

    // open modals
    $('.js-open-modal-on-load').each(function (i, elt) {
        $(elt).modal('show');
    });

    // force overlay to disappear when modal is gone
    $('#basket-popup').on('hidden.bs.modal', function() {
        $('.modal-backdrop.in').hide();
        $('.js-open-modal-on-load').hide();
    });

    // Show the rating stars
    W.renderRatings();

    // dotdotdot
    $(".ellipsis").dotdotdot();


    // move header search input
    // ========================

    // move header search input text regarding screen size, to avoid conflicts with the query

    var $searchInput = $('.product-search-input');
    var $mobileContainer = $('#mobile-header-search-container');
    var $desktopContainer = $('#desktop-header-search-container');

    function moveSearchInput() {
        if ($(window).width() < 768 ) {
            if ($mobileContainer.children().length < 1) {
                $mobileContainer.append($searchInput);
            }

        } else {
            if ($desktopContainer.children().length < 1) {
                $desktopContainer.append($searchInput);
            }
        }
    }

    // first execution on page load
    moveSearchInput();

    // execute again if window is resized
    $( window ).resize(function() {
        moveSearchInput();
    });
});
