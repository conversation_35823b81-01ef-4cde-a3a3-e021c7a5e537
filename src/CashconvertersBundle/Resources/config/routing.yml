home:
    path: /
    defaults: { _controller: CashconvertersBundle:Home:home }
    methods: [GET]

product:
    path: /{categoryPath}/{productSlug}.html
    defaults: { _controller: CashconvertersBundle:Product:viewProduct }
    methods: [GET]
    requirements:
        categoryPath: ".+"
        productSlug: "[^/]+"
    condition: "request.attributes.has('productId')"

basket_view:
    path: /basket
    defaults: { _controller: CashconvertersBundle:Basket:view }

search:
    path: /recherche
    defaults: { _controller: CashconvertersBundle:Search:search }
    methods: [GET]

contact:
    path: /contact
    defaults: { _controller: CashconvertersBundle:Contact:email }
    methods: [GET, POST]
    options: { overrideLegacySlug: true } # see \Wizacha\Marketplace\Seo\SeoService::registerSlugLegacy

category:
    path: /{categoryPath}
    defaults: { _controller: CashconvertersBundle:Search:category }
    methods: [GET]
    condition: "request.attributes.get('categoryId') > 0"
    requirements:
        categoryPath: ".+"

vendor:
    path: /{slug}
    defaults: { _controller: CashconvertersBundle:Search:company }
    methods: [GET]
    condition: "request.attributes.get('companyId') > 0"

categories:
    path: /categories
    defaults: { _controller: CashconvertersBundle:Categories:displayCategories }
    methods: [GET]

variant:
    path: /{slug}
    defaults: { _controller: CashconvertersBundle:Search:attribute }
    methods: [GET]
    condition: "request.attributes.get('variantId') > 0"
    options:
        expose: true

company_new:
    path: /vendeur-pro.html
    defaults: { _controller: AppBundle:Company:create }

loyalty:
    path: /fidelite
    defaults: { _controller: CashconvertersBundle:Loyalty:view }
    methods: [GET, POST]
