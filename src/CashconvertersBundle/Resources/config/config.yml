parameters:
    env(ENABLE_MVP): true
    env(MULTI_VENDOR_PRODUCT_RULES): 'supplier_reference'
    feature.carrier.chronopost.chrono13: true
    feature.carrier.chronopost.chronorelais: true
    feature.commission.include_shipping_in_commission: true

    # Désactivation du front office lorsque celui-ci est externalisé
    feature.disable_front_office: true

    # Fidelité
    loyalty_api_url: 'https://centralepresta.prd.cashconverters.fr/api/wizaplace/'
    loyalty_api_login: 'wizaplace'
    loyalty_api_password: 'Zen5jaRvQNE5334'
    loyalty_api_marketplace_identifier: '%marketplace.project_name%'

    feature.professional_clients: false

    # HiPay
    hipay.css: 'assets/bundles/cashconverters/css/css_hipay.css'

services:
    # Mails
    app.dolist_client:
        public: true
        class: Wizacha\Component\Dolist\DolistClient
        arguments:
            - '@marketplace.cache'
            - '@marketplace.async_dispatcher'
            - '%dolist.account_id%'
            - '%dolist.authentication_key%'
            - '%dolist.debug%'

    app.dolist_client.debug:
        public: true
        class: Wizacha\Component\Dolist\DolistClient
        arguments:
            - '@marketplace.cache'
            - '@marketplace.async_dispatcher'
            - '%dolist.account_id%'
            - '%dolist.authentication_key%'
            - true

    Wizacha\Component\Dolist\DolistNotifier:
        abstract: true
        arguments:
            - '@service_container'
            - '@app.dolist_client'
            - '@marketplace.dolist_template_repository'

    app.notification.user_notifier:
        public: true
        class: Wizacha\CashconvertersBundle\Notification\UserNotifier
        parent: 'Wizacha\Component\Dolist\DolistNotifier'

    app.notification.user_notifier.debug:
        public: true
        class: Wizacha\CashconvertersBundle\Notification\UserNotifier
        parent: 'Wizacha\Component\Dolist\DolistNotifier'

    app.notification.option_notifier:
        public: true
        class: Wizacha\CashconvertersBundle\Notification\OptionNotifier
        parent: 'Wizacha\Component\Dolist\DolistNotifier'

    app.notification.option_notifier.debug:
        public: true
        class: Wizacha\CashconvertersBundle\Notification\OptionNotifier
        parent: 'Wizacha\Component\Dolist\DolistNotifier'

    app.notification.order_notifier:
        public: true
        class: Wizacha\CashconvertersBundle\Notification\OrderNotifier
        parent: 'Wizacha\Component\Dolist\DolistNotifier'
        calls:
            - method: setNotifyShipmentCreated
              arguments:
                  - '%feature.notify_shipment_created%'

    app.notification.order_notifier.debug:
        public: true
        class: Wizacha\CashconvertersBundle\Notification\OrderNotifier
        parent: 'Wizacha\Component\Dolist\DolistNotifier'
        calls:
            - method: setNotifyShipmentCreated
              arguments:
                  - '%feature.notify_shipment_created%'

    app.notification.rma_notifier:
        public: true
        class: Wizacha\CashconvertersBundle\Notification\RmaNotifier
        parent: 'Wizacha\Component\Dolist\DolistNotifier'

    app.notification.rma_notifier.debug:
        public: true
        class: Wizacha\CashconvertersBundle\Notification\RmaNotifier
        parent: 'Wizacha\Component\Dolist\DolistNotifier'

    app.notification.product_notifier:
        public: true
        class: Wizacha\CashconvertersBundle\Notification\ProductNotifier
        parent: 'Wizacha\Component\Dolist\DolistNotifier'

    app.notification.product_notifier.debug:
        public: true
        class: Wizacha\CashconvertersBundle\Notification\ProductNotifier
        parent: 'Wizacha\Component\Dolist\DolistNotifier'

    app.notification.refund_notifier:
        public: true
        class: Wizacha\CashconvertersBundle\Notification\RefundNotifier
        parent: 'Wizacha\Component\Dolist\DolistNotifier'
        calls:
            - [setCurrencySign, ['%currency.sign%']]

    app.notification.refund_notifier.debug:
        public: true
        class: Wizacha\CashconvertersBundle\Notification\RefundNotifier
        parent: 'Wizacha\Component\Dolist\DolistNotifier'
        calls:
            - [setCurrencySign, ['%currency.sign%']]

    app.notification.company_notifier:
        public: true
        class: Wizacha\CashconvertersBundle\Notification\CompanyNotifier
        parent: 'Wizacha\Component\Dolist\DolistNotifier'

    app.notification.company_notifier.debug:
        public: true
        class: Wizacha\CashconvertersBundle\Notification\CompanyNotifier
        parent: 'Wizacha\Component\Dolist\DolistNotifier'

    app.notification.moderation_notifier:
        public: true
        class: Wizacha\CashconvertersBundle\Notification\ModerationNotifier
        parent: 'Wizacha\Component\Dolist\DolistNotifier'

    app.notification.moderation_notifier.debug:
        public: true
        class: Wizacha\CashconvertersBundle\Notification\ModerationNotifier
        parent: 'Wizacha\Component\Dolist\DolistNotifier'

    app.notification.contact_notifier:
        public: true
        class: Wizacha\CashconvertersBundle\Notification\ContactNotifier
        parent: 'Wizacha\Component\Dolist\DolistNotifier'

    app.notification.contact_notifier.debug:
        public: true
        class: Wizacha\CashconvertersBundle\Notification\ContactNotifier
        parent: 'Wizacha\Component\Dolist\DolistNotifier'

    app.notification.discuss_notifier:
        public: true
        class: Wizacha\CashconvertersBundle\Notification\DiscussNotifier
        parent: 'Wizacha\Component\Dolist\DolistNotifier'
        calls:
            - [setRouter, ['@router']]

    app.notification.discuss_notifier.debug:
        public: true
        class: Wizacha\CashconvertersBundle\Notification\DiscussNotifier
        parent: 'Wizacha\Component\Dolist\DolistNotifier'
        calls:
            - [setRouter, ['@router']]

    app.loyalty_service:
        public: true
        class: Wizacha\CashconvertersBundle\Service\LoyaltyService
        arguments:
            - '%loyalty_api_url%'
            - '%loyalty_api_login%'
            - '%loyalty_api_password%'

    app.store_service:
        public: true
        class: Wizacha\CashconvertersBundle\Service\StoreService
        arguments:
            - '@marketplace.cache'
            - '%loyalty_api_url%'
            - '%loyalty_api_login%'
            - '%loyalty_api_password%'
            - '%loyalty_api_marketplace_identifier%'
