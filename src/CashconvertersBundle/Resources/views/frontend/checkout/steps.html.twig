<div class="container">
    <div class="checkout-steps-breadcrumb">
        <ul class="list-inline hidden-xs">
            <li class="checkout-breadcrumb col-sm-3 text-center {% if step == 1 %}active{% endif %}"><span>1 - {{ 'checkout_step1'|trans|upper }}</span></li>
            <li class="checkout-breadcrumb col-sm-3 text-center {% if step == 2 %}active{% endif %}"><span>2 - {{ 'checkout_step2'|trans|upper }}</span></li>
            <li class="checkout-breadcrumb col-sm-3 text-center {% if step == 3 %}active{% endif %}"><span>3 - {{ 'checkout_step3'|trans|upper }}</span></li>
            <li class="checkout-breadcrumb col-sm-3 text-center {% if step >= 4 %}active{% endif %}"><span>4 - {{ 'checkout_step4'|trans|upper }}</span></li>
        </ul>

        <ul class="mobile-list list-inline visible-xs">
            {% for i in 1..4 %}
                {% if step == i %}
                    <li class="checkout-breadcrumb text-center active"><span>{{ i }} - {{ ('checkout_step'~i)|trans|upper }}</span></li>
                {% else %}
                    <li class="checkout-breadcrumb text-center"><span>{{ i }}</span></li>
                {% endif %}
            {% endfor %}
            <hr>
        </ul>
    </div>
</div>
