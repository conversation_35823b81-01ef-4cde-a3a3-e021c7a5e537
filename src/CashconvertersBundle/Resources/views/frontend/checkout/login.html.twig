{% extends '@App/frontend/layout.html.twig' %}

{% block content %}
    <main class="checkout-process test-checkout">

        {# checkout steps #}
        {% include '@App/frontend/checkout/steps.html.twig' with { step: 2 } %}

        <section class="checkout-login">
            <div class="container">

                <div class="row">
                    {# register / login #}
                    <div class="col-xs-12 col-sm-4">
                        {# Login #}
                        <div id="login-form" class="block">
                            <form class="login test-login-form" action="{{ cscart_url('index.php?dispatch=auth.login') }}" method="post">
                                <input type="hidden" name="return_url" value="{{ path(redirectUrl) }}">
                                <input type="hidden" name="redirect_url" value="{{ path(redirectUrl) }}">
                                <input type="hidden" name="remember_me" value="1">

                                <h2 class="checkout-main-title hidden-xs">{{ 'if_client'|trans|raw }}</h2>
                                <hr class="checkout-main-title-under hidden-xs">

                                {# email #}
                                <div class="form-group">
                                    <label>
                                        {{ 'email'|trans }} :
                                        <input type="email" name="user_login" required="required" class="form-control" />
                                    </label>
                                </div>

                                {# password #}
                                <div class="form-group">
                                    <label>
                                        {{ 'password'|trans }} :
                                        <input type="password" name="password" required="required" class="form-control" />
                                    </label>
                                </div>

                                {# recover password #}
                                <div class="form-group">
                                    <a href="{{ path('recover_password') }}">{{ 'forgot_password_question'|trans }}</a>
                                </div>

                                {# submit #}
                                <div class="form-group">
                                    <button type="submit" name="dispatch[auth.login]" class="btn checkout-btn">{{ 'sign_in'|trans|upper }}</button>
                                </div>
                            </form>

                            <div class="visible-xs text-center">
                                <a href="#" class="" onclick="$('#login-form').hide(); $('#register-form').show()">{{ 'create_account'|trans }}</a>
                            </div>

                        </div>


                        {# Register #}
                        <div class="block hidden-xs">
                            <h2 class="checkout-main-title">{{ 'new_client'|trans|raw }}</h2>
                            <hr class="checkout-main-title-under">

                            <span>{{ 'new_client_text'|trans }}</span>

                            <a href="{{ path('login') }}" class="btn checkout-btn">{{ 'register'|trans|upper }}</a>
                        </div>

                        {# Register mobile#}
                        <div id="register-form" style="display: none">
                            <form class="register" action="{{ cscart_url('index.php?dispatch=checkout.add_profile') }}" method="post">

                                <input type="hidden" name="result_ids" value="checkout*,account*"/>
                                <input type="hidden" name="return_to" value="{{ path('checkout_addresses') }}">
                                <input type="hidden" name="redirect_url" value="{{ path('login') }}">
                                <input type="hidden" name="user_data[register_at_checkout]" value="Y"/>

                                <div class="form-group">
                                    <label>
                                        {{ 'email'|trans }} :
                                        <input type="text" name="user_data[email]" value="" class="form-control" />
                                    </label>
                                </div>

                                <div class="form-group">
                                    <label>
                                        {{ 'password'|trans }} :
                                        <input type="password" name="user_data[password1]" value="" class="form-control" />
                                    </label>
                                </div>

                                <div class="form-group">
                                    <label class="checkbox">
                                        <input type="checkbox" class="custom-input" required="required" name="terms_approved" value="1">
                                        <span></span>
                                        {{ 'register_accept_terms'|trans({'[url]': cscart_url('pages.view?page_id=8')})|raw }}
                                    </label>
                                </div>

                                <div class="form-group">
                                    {{ captcha() }}
                                </div>

                                <div class="form-group">
                                    <button type="submit" name="dispatch[checkout.add_profile]" class="btn default-btn light-purple-btn">{{ 'register'|trans }}</button>
                                </div>

                            </form>

                        </div>

                    </div>

                    {# basket summary #}
                    <div class="visible-sm visible-md visible-lg col-xs-4 col-xs-offset-4">
                        {% include '@App/frontend/checkout/basket-summary.html.twig' %}
                    </div>
                </div>
            </div>
        </section>
    </main>
{% endblock %}
