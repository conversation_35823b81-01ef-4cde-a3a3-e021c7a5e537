{% extends '@App/frontend/layout-search.html.twig' %}

{% block realytics %}
    {% if not constant('DEVELOPMENT')|default(false) %}
        <script type="text/javascript">
            RY_ORDER_ID = {{ order_info.order_id|json_encode|raw }};
            RY_ORDER_AMOUNT = {{  order_info.total|json_encode|raw }};

            RY.track("validation_panier", {"transaction": {"amount": RY_ORDER_AMOUNT, "orderId": RY_ORDER_ID }});
        </script>
    {% endif %}
{% endblock %}

{% block content %}
    <div class="tygh-content clearfix">
        <div class="container ">
            <div class="row">
                <div class="span16 ">
                    <h2 class="main-title">{{ 'command_received'|trans }}</h2>
                    {% set account_link = '<a href="'~path('login')~'">'~'myaccount'|trans|lower~'</a>' %}
                    {% if order_info.need_shipping %}
                        {{ 'text_order_placed_successfully'|trans({'[my_account_link]': account_link})|raw }}
                    {% else %}
                        {{ 'text_order_placed_successfully_for_edp'|trans({'[my_account_link]': account_link})|raw }}
                    {% endif %}

                    <b>{{ 'command_number'|trans }} : {% if order_info.child_ids is defined and order_info.child_ids is not empty%}{{ order_info.child_ids }}{% else %}{{ order_info.order_id }}{% endif %}</b><br>
                    <b> {{ 'ordered'|trans }} {{ order_info.timestamp|date('d/m/Y') }} {{ 'at'|trans }} {{ order_info.timestamp|date('H:i') }}</b>

                    {% include '@App/frontend/checkout/complete-summary.html.twig' %}

                    {#
                    {{ render_smarty_legacy('views/checkout/complete.tpl', {
                        order_info: order_info,
                    }) }}
                    #}
                </div>
            </div>

        </div>
    </div>
{% endblock %}

{% block scripts %}
    {{ parent() }}

    {% set googleAnalyticsId = 'google_analytics_id'|trans %}
    {# Check if the translation exists, which tells us if Google Analytics is enabled #}
    {% if googleAnalyticsId is not empty and googleAnalyticsId != 'google_analytics_id' %}
        {{ render(controller('AppBundle:Checkout:orderCompleteAnalytics', { 'orderInfo': order_info })) }}
    {% endif %}
{% endblock %}
