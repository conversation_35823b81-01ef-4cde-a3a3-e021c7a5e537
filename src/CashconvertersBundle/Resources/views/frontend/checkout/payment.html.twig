{% extends '@App/frontend/layout.html.twig' %}

{% block content %}
    <main class="checkout-process">

        {# checkout steps #}
        {% include '@App/frontend/checkout/steps.html.twig' with { step: 4 } %}

        <section class="checkout-payment">
            <div class="container">

                <div class="row">
                    {# payment methods #}
                    <div class="col-xs-12 col-sm-6">

                        {# payment #}
                        <div class="block">
                            <h2 class="checkout-main-title">{{ 'payment_method_choice'|trans|raw }}</h2>
                            <hr class="checkout-main-title-under">

                            <form action="{{ path('checkout_payment_update') }}" method="post" class="payment-form">

                                {# payment means #}
                                {% if paymentNeeded %}
                                    {% include '@App/frontend/checkout/payment_methods.html.twig' %}
                                {% else %}
                                    <p>{{ 'text_no_payments_needed'|trans }}</p>
                                {% endif %}

                                {# check conditions #}
                                <label class="checkbox">
                                    <input type="checkbox" name="accept_terms" value="Y" class="test-terms-and-conditions" required><span></span>
                                    {{ 'checkout_terms_and_conditions'|trans({'%use_terms_link%': 'use_terms_link'|trans,'%sales_terms_link%': 'sales_terms_link'|trans})|raw }}
                                    {% if companyTerms is not empty %}
                                        <br />{{ 'accept_company_terms'|trans }}
                                        {% for term in companyTerms %}
                                            <a href="{{ term.link }}" target="_blank">{{ term.name }}</a>
                                        {% endfor %}
                                    {% endif %}
                                </label>

                                {# submit #}
                                <button class="btn checkout-btn test-next-step" type="submit">
                                    {{ 'proceed_to_payment'|trans|upper }}
                                </button>

                            </form>
                        </div>

                    </div>

                    {# basket summary #}
                    <div class="hidden-xs col-sm-offset-2 col-sm-4">
                        {% include '@App/frontend/checkout/basket-summary.html.twig' %}
                    </div>
                </div>

            </div>
        </section>
    </main>
{% endblock %}
