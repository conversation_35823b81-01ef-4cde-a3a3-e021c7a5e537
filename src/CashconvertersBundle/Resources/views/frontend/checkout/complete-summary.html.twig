<aside class="basket-summary">

    {# basket summary #}
    <div class="row">
        <div class="block col-xs-12 col-sm-4">
            <h2 class="checkout-main-title">{{ 'my_order'|trans|upper }}</h2>
            <hr class="checkout-main-title-under">

            <div class="products-line">
                {% for product_group in order_info.product_groups %}
                    {% for item in product_group.products %}
                        <div class="product-line row">
                            {% if item.main_pair is not empty %}
                                <img class="col-xs-4 product-img"  src="{{ item.main_pair.detailed.http_image_path }}">
                            {% else %}
                                <p class="no-image col-xs-4 product-image-no-image"><img src="{{ asset('images/no-image.jpg') }}"></p>
                            {% endif %}

                            <div class="col-xs-8">
                                <p><span class="product-name">{{ item.product|capitalize }}</span></p>

                                <p class="item-price">
                                    <span class="product-qty"><b>{{ 'quantity'|trans }}</b> : {{ item.amount }}</span>
                                    <br>
                                    <span class="product-price"><b>{{ 'price'|trans }}</b> : {{ item.price|price }}</span>
                                </p>
                            </div>
                        </div>
                    {% endfor %}
                {% endfor %}
            </div>
        </div>
        <div class="block col-xs-12 col-sm-4 col-sm-offset-4">
            <h2 class="checkout-main-title">{{ 'my_delivery'|trans|upper }}</h2>
            <hr class="checkout-main-title-under">

            <p>
                {{ order_info.s_lastname }} {{ order_info.s_firstname }}
                <br>
                {% if order_info.s_company|length > 0 %}
                    {{ order_info.s_company }}
                    <br>
                {% endif %}
                {{ order_info.s_address }} {{ order_info.s_address_2 }}
                <br>
                {{ order_info.s_zipcode }} {{ order_info.s_city }}
            </p>

            {% for product_group in order_info.product_groups %}
                {% set chosenShipping = product_group.chosen_shippings[0] %}
                <p class="no-margin">
                    <b>{{ 'vendor'|trans }} :</b>
                    <br>
                    {{ product_group.name }}
                    <br>
                    <b>{{ 'delivery_mean'|trans }} :</b>
                    <br>
                    {{ chosenShipping.shipping}} - {{ chosenShipping.rate|price }}
                </p>

                {# for hand delivery type ("Click and Collect") - display shop location #}
                {% if chosenShipping.w_delivery_type == "H" %}
                    {% set shopInfo = product_group.package_info.origination %}
                    <p class="no-margin">{{ 'collect_location'|trans }}&nbsp;:&nbsp;</p>
                    <ul class="list-unstyled">
                        <li>{{ shopInfo.name }}</li>
                        <li>{{ shopInfo.address }}</li>
                        <li>{{ shopInfo.zipcode }} {{ shopInfo.city }}</li>
                    </ul>
                {% endif %}
            {% endfor %}

        </div>
    </div>
    <div class="row">
        <div class="block col-xs-12 col-sm-4">
            <div class="order-summary">
                <h2 class="title">{{ 'summary'|trans }}</h2>

                {# product and shipping summary #}
                <div class="prices">
                    <div class="product-shipping-summary">
                        {# products total #}
                        <div class="row line valign-middle">
                            <div class="col-xs-6">
                                <div class="line-label">
                                    {{ 'products_total'|trans }}
                                </div>
                            </div>
                            <div class="col-xs-6 text-right">
                                <div class="line-amount">
                                    {{ order_info.subtotal|price }}
                                </div>
                            </div>
                        </div>

                        {# shipping total #}
                        <div class="row line valign-middle">
                            <div class="col-xs-6">
                                <div class="line-label">
                                    {{ 'w_shipping_total_amount'|trans }}
                                </div>
                            </div>
                            <div class="col-xs-6 text-right">
                                <div class="line-amount">
                                    {{ order_info.shipping_cost|price }}
                                </div>
                            </div>
                        </div>
                    </div>

                    {# order total #}
                    <div class="total">
                        <div class="row line valign-middle">
                            <div class="col-xs-6">
                                <div class="line-label">
                                    {{ 'total'|trans }}&nbsp;TTC
                                </div>
                            </div>
                            <div class="col-xs-6 text-right">
                                <div class="line-amount">
                                    {{ order_info.total|price }}
                                </div>
                                <span class="vat">{{ 'included'|trans }} {{ order_info.taxes|first.tax_subtotal|price }} {{ 'TVA'|trans }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {# newsletter subscription #}
    {{ render(controller('AppBundle:MailingList:form')) }}

    <script>
        $(function() {
            // change newsletter explanation text
            $('.newsletter-label').html("<b>{{ 'subscribe_to_newsletter'|trans|upper|e('js') }}</b>");
        });
    </script>

</aside>
