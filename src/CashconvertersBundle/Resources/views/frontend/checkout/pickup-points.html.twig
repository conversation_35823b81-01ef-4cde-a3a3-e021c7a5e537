{% extends '@App/frontend/layout.html.twig' %}

{% block content %}

    <main class="checkout-process">
        {# checkout steps #}
        {% include '@App/frontend/checkout/steps.html.twig' with { step: 3 } %}

        <section class="checkout-addresses">
            <div class="container">
                <div class="row">

                    {# addresses #}
                    <div class="col-xs-12 col-md-4">
                        <div class="block clearfix">

                            <form method="post" action="{{ path('checkout_pickup_points_update') }}" @submit.prevent="isValidPickupPoint">
                                <input type="hidden" name="chrono_point_relais" :value="selectedPickupLocationId" required>

                                {# billing address #}
                                <div class="billing-address">
                                    <h2 class="checkout-main-title">{{ 'billing_address'|trans }}</h2>
                                    <hr class="checkout-main-title-under">
                                    {% include '@App/frontend/views/profile/address-fields.html.twig' with {'section': 'B'} %}

                                    {# submit button #}
                                    <div class="form-group submit">
                                        <button type="submit" class="btn default-btn purple-btn col-xs-12" :class="{'disabled': !selectedPickupLocationId}">{{ 'save_addresses'|trans }}</button>
                                    </div>
                                </div>
                            </form>

                        </div>
                    </div>

                    <div class="col-xs-12 col-md-offset-1 col-md-7">

                        {# chrono relais #}
                        <div class="chrono-relais">

                            <h2 class="checkout-main-title">{{ 'w_select_pickup_location'|trans }}</h2>
                            <p>{{ 'chrono_relais_title'|trans }}</p>
                            <p>{{ 'chrono_relais_warning'|trans }}</p>
                            <hr class="checkout-main-title-under">
                            <p>{{ 'w_text_enter_address'|trans }}&nbsp;:</p>

                            {# user shipping address update inputs #}
                            <div class="google-map">
                                <form class="user-inputs" @submit.prevent="return false;"> {# doesn't reload the page on submit #}

                                    <div class="gmap-group">
                                        <label for="gmap-address">{{ 'Address'|trans }}</label>
                                        <input type="text" class="form-control" id="gmap-address" v-model="userLocation.address" required>
                                    </div>

                                    <div>
                                        <div class="gmap-group">
                                            <label for="gmap-zipcode">{{ 'zip_postal_code'|trans }}</label>
                                            <input type="text" class="form-control" id="gmap-zipcode" v-model="userLocation.zipcode" required>
                                        </div>

                                        <div class="gmap-group">
                                            <label for="gmap-city">{{ 'City'|trans }}</label>
                                            <input type="text" class="form-control" id="gmap-city" v-model="userLocation.city" required>
                                        </div>

                                        <button type="submit" class="btn default-btn" @click="refreshMap">{{ 'w_search_button'|trans }}</button>
                                    </div>
                                </form>
                            </div>

                            {# pickup locations #}
                            <div class="pickup-locations">

                                {# pickup locations list #}
                                <ul class="pickup-locations-list list-unstyled">
                                    <li v-for="pickup in pickupLocations" class="pickup" :id="pickup.identifiant">

                                        {# pickup location name #}
                                        <div class="pickup-name" v-text="pickup.nom"></div>

                                        {# pickup location address #}
                                        <div class="pickup-address" v-text="pickup.adresse1"></div>

                                        {# pickup location distance from user address #}
                                        <div class="distance">
                                            <div>
                                                <span class="pickup-zipcode" v-text="pickup.codePostal"></span>
                                                <span>&nbsp;</span>
                                                <span class="pickup-city" v-text="pickup.localite"></span>
                                            </div>
                                            <span class="pickup-distance" v-text="'À ' + distance(pickup.distanceEnMetre)"></span>
                                        </div>

                                        {# pickup location opening times #}
                                        <div class="opening-times-group" v-if="pickup.listeHoraireOuverture">
                                            <p class="title">{{ "opening_times"|trans }}</p>
                                            <ul class="opening-times list-unstyled">
                                                <li v-for="day in pickup.listeHoraireOuverture">
                                                    <span class="week-day" v-text="dayTranslation(day.jour)"></span>
                                                    <span v-text="day.horairesAsString"></span>
                                                </li>
                                            </ul>
                                        </div>

                                        {# select button #}
                                        <button class="btn default-btn black-btn-outline" :class="{ 'active': (pickup.identifiant == selectedPickupLocationId) }" @click.prevent="(selectPickup(pickup))">{{ 'w_select_this_pickup_location'|trans }}</button>
                                    </li>
                                </ul>

                                {# pickup locations on map #}
                                <div id="map"></div>
                            </div>
                        </div>

                        {# basket summary #}
                        <div class="col-xs-12 col-md-7 hidden-xs">
                            {% include '@App/frontend/checkout/basket-summary.html.twig' %}
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script>
        // selection of pickup location with Google Maps
        $(function() {

            var vm = new Vue({
                el: '.checkout-addresses',

                data: {
                    map: new google.maps.Map(document.getElementById('map'), {
                        zoom: 5,
                        center: { lng: 2.213749, lat: 46.227638 } // centered on France
                    }),
                    geocoder: new google.maps.Geocoder(),
                    userCoordinates: {},
                    pickupLocations: {},
                    userLocation: {
                        address: '{{ profile.b_address|e('js') }}',
                        zipcode: '{{ profile.b_zipcode|e('js') }}',
                        city: '{{ profile.b_city|e('js') }}',
                        country: '{{ profile.b_country|e('js') }}'
                    },
                    mapMarkers: [],
                    selectedPickupLocationId: "",
                },

                computed: {
                    userCompleteAddress: function() {
                        return (this.userLocation.address + ' ' + this.userLocation.zipcode + ' ' + this.userLocation.city);
                    },

                    // trimmed Chrono Relais pickup locations
                    simplePickups: function() {
                        return this.pickupLocations.map(function(pickup) {
                            return {
                                id: pickup.identifiant,
                                coordinates: {
                                    lat: parseFloat(pickup.coordGeolocalisationLatitude),
                                    lng: parseFloat(pickup.coordGeolocalisationLongitude)
                                }
                            }
                        });
                    }
                },

                methods: {
                    refreshMap: function() {

                        // cleanup: there is no longer a chosen pickup point
                        this.selectedPickupLocationId = "";

                        var userAddress = this.userCompleteAddress;

                        // avoid error if user hasn't set their addresses yet
                        if (! this.userLocation.address || ! this.userLocation.zipcode || ! this.userLocation.city) {
                            return false;
                        }

                        this.geocoder.geocode({
                                'address': userAddress
                            },

                            function(location, status) {
                                if (status === 'OK') {
                                    // update user coordinates
                                    vm.userCoordinates = location[0].geometry.location;

                                    // update centered map
                                    vm.map.setZoom(14);
                                    vm.map.panTo(vm.userCoordinates);

                                    // set and display initial pickup locations
                                    $.get(
                                        "/api/v1/chronopost/points-relais", {
                                            address: vm.userLocation.address,
                                            zipcode: vm.userLocation.zipcode,
                                            city: vm.userLocation.city,
                                            country: vm.userLocation.country,
                                            weight: {{ totalWeightKg }},
                                        }

                                    ).done(function(locations) {
                                        // update locations property
                                        vm.pickupLocations = locations;

                                        //cleanup
                                        vm.clearMarkers();

                                        // display pickup markers
                                        vm.pickupLocations.forEach(function(location) {
                                            vm.addMarker(location);
                                        });

                                    }).fail(function() {
                                        alert('Erreur de localisation'); // TODO: créer une notification
                                    });

                                } else {
                                    alert('Erreur de localisation : ' + status); // TODO: créer une notification
                                }
                            });
                    },

                    // add a marker on the map
                    addMarker: function(location) {

                        var markerPosition = {
                            lat: parseFloat(location.coordGeolocalisationLatitude),
                            lng: parseFloat(location.coordGeolocalisationLongitude)
                        };

                        var marker = new google.maps.Marker({
                            map: vm.map,
                            position: markerPosition,
                            title: location.nom
                        });

                        // update markers array
                        vm.mapMarkers.push(marker);

                        marker.addListener('click', function() {

                            // center map on clicked marker
                            vm.map.setZoom(18);
                            vm.map.panTo(marker.getPosition());

                            // update selected pickup location
                            vm.setSelectedPickupLocationId(markerPosition);
                        });
                    },

                    // Chrono Relais pickup ID from Google Maps marker coordinates
                    setSelectedPickupLocationId: function(markerPosition) {

                        vm.simplePickups.forEach(function(pickup) {
                            if (W.areObjectsEquivalents(pickup.coordinates, markerPosition)) {

                                // update selected pickup location
                                vm.selectedPickupLocationId = pickup.id;

                                // show pickup location in list (animation)
                                var $pickupElement = $("#"+pickup.id);
                                var $pickupList = $(".pickup-locations-list");

                                $pickupList.scrollTop(0); // reset list position

                                $pickupList.animate({
                                    scrollTop: $pickupElement.position().top
                                });
                            }
                        });
                    },

                    // cleanup
                    clearMarkers: function() {
                        vm.mapMarkers.forEach(function(marker) {
                            marker.setMap(null);
                        });
                    },

                    // parameter value is in meters
                    distance: function(value) {
                        var returnInMeters = (value < 1000);

                        if (returnInMeters) {
                            return value + "m";

                        } else {
                            return (value/1000).toFixed(2).replace('.', ',') + "km";
                        }
                    },

                    isValidPickupPoint: function(e) {
                        if(this.selectedPickupLocationId) {
                            $(e.target).submit();
                        }
                    },

                    dayTranslation: function(dayNumber) {
                        switch (dayNumber) {
                            case 1:
                                return "{{ ('week-day-1')|trans|e('js') }}";
                                break;
                            case 2:
                                return "{{ ('week-day-2')|trans|e('js') }}";
                                break;
                            case 3:
                                return "{{ ('week-day-3')|trans|e('js') }}";
                                break;
                            case 4:
                                return "{{ ('week-day-4')|trans|e('js') }}";
                                break;
                            case 5:
                                return "{{ ('week-day-5')|trans|e('js') }}";
                                break;
                            case 6:
                                return "{{ ('week-day-6')|trans|e('js') }}";
                                break;
                            case 7:
                                return "{{ ('week-day-7')|trans|e('js') }}";
                                break;
                            default:
                                break;
                        }
                    },

                    selectPickup: function(pickup) {
                        this.selectedPickupLocationId = pickup.identifiant;

                        // center map on selected pickup location
                        vm.map.setZoom(18);
                        vm.map.setCenter({
                                lat: parseFloat(pickup.coordGeolocalisationLatitude),
                                lng: parseFloat(pickup.coordGeolocalisationLongitude)
                            }
                        );
                    }
                },

                ready: function () {
                    // set initial map (based on user billing address)
                    this.refreshMap();
                }
            });

            {# create custom radio button #}
            var $radioButtonLabels = $(".radio").not('input').children('label');
            $radioButtonLabels.addClass('radio');
            $radioButtonLabels.append('<span></span>');

            {# remove unwanted radio classes #}
            $('#elm_37').removeClass('radio');
            $('#elm_38').removeClass('radio');

        });
    </script>
{% endblock %}
