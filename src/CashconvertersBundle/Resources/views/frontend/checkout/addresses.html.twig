{% extends '@App/frontend/layout.html.twig' %}

{% block content %}

    <main class="checkout-process">
        {# checkout steps #}
        {% include '@App/frontend/checkout/steps.html.twig' with { step: 3 } %}

        <section class="checkout-addresses">
            <div class="container">

                <div class="row">
                    <div class="col-xs-12 col-sm-4">
                        <div class="block clearfix">
                            <form method="post" action="{{ path('checkout_addresses_update') }}">
                                {# billing address #}
                                <div class="billing-address">
                                    <h2 class="checkout-main-title">{{ 'billing_address'|trans }}</h2>
                                    <hr class="checkout-main-title-under">

                                    {% include '@App/frontend/views/profile/address-fields.html.twig' with {'section': 'B'} %}

                                    <label class="checkbox">
                                        <input class="add-address" type="checkbox" id="differentShippingAddress" name="ship_to_another" value="1" {% if shipToAnother %} checked="checked" {% endif %}>
                                        <span></span>
                                        {{ 'different_shipping_address'|trans }}
                                    </label>
                                </div>

                                {# shipping address #}
                                <div class="shipping-address-block" style="display: none">
                                    <h2 class="checkout-main-title">{{ 'shipping_address'|trans }}</h2>
                                    <hr class="checkout-main-title-under">

                                    {% include '@App/frontend/views/profile/address-fields.html.twig' with {'section': 'S', 'fakeRequired': true} %}

                                </div>

                                {# submit button #}
                                <div class="form-group submit">
                                    <button type="submit" class="btn checkout-btn">{{ 'save_addresses'|trans|upper }}</button>
                                </div>
                            </form>
                        </div>
                    </div>

                    {# basket summary #}
                    <div class="col-xs-12 col-sm-offset-4 col-sm-4 hidden-xs">
                        {% include '@App/frontend/checkout/basket-summary.html.twig' %}
                    </div>
                </div>
            </div>
        </section>
    </main>

    <script>
        $(function () {
            var $differentShippingAddressCheckbox = $('#differentShippingAddress');
            var $shippingAddressBlock = $('.shipping-address-block');

            {# open shipping block on page load if 'shipToAnother' is true (meaning checkbox is actually checked) #}
            if($differentShippingAddressCheckbox.is(':checked')) {
                $shippingAddressBlock.toggle('fast');

                {# update required fields #}
                toggleRequiredFields();
            }

            {# toggle 'different shipping address' block on click #}
            $differentShippingAddressCheckbox.click(function() {
                $shippingAddressBlock.toggle('fast');

                {# update required fields #}
                toggleRequiredFields();
            });

            {# toggle shipping address required fields regarding shipToAnother boolean (meaning checkbox is actually checked) #}
            function toggleRequiredFields() {

                {# data-required is provided by 'render_smarty_legacy' #}
                var $requiredFields = $shippingAddressBlock.find('[data-required]');

                if(! $differentShippingAddressCheckbox.is(':checked')) {
                    $requiredFields.each(function() {
                        $(this).attr('required', false)
                    });
                } else {
                    $requiredFields.each(function() {
                        $(this).attr('required', true)
                    });
                }
            }

            {# manage required fields after page load #}
            toggleRequiredFields();
        });
    </script>

{% endblock %}
