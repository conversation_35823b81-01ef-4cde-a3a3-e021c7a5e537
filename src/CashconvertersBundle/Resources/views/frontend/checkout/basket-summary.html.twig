{% set basket = basket ?? getBasket(true) %}
<aside class="basket-summary">

    {# basket summary #}
    <div class="block">
        <h2 class="checkout-main-title">{{ 'my_basket'|trans|upper }}</h2>
        <hr class="checkout-main-title-under">

        {# products block #}
        <div class="products-line">
            {% for item in basket.allItems %}
            <div class="product-line row">
                {% if item.declination.main_image is not null %}
                    <img class="col-xs-4 product-img"  src="{{ item.declination.main_image|image_url(120, 120) }}">
                {% else %}
                    <p class="no-image col-xs-4 product-image-no-image"><img src="{{ asset('images/no-image.jpg') }}"></p>
                {% endif %}

                <div class="col-xs-8">
                    <p><span class="product-name">{{ item.declination.product_name }}</span></p>

                    <div class="item-price">
                        <p class="product-qty">{{ 'quantity'|trans }} : {{ item.quantity }}</p>
                        <p class="product-price">{{ 'price'|trans }} : <span class="price">{{ item.subtotal|price }}</span></p>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <div class="block">
        <div class="order-summary">
            <h2 class="title">{{ 'summary'|trans|upper }} :</h2>

            {# product and shipping summary #}
            <div class="prices">
            <div class="product-shipping-summary">
                {# products total #}
                <div class="row line valign-middle">
                    <div class="col-md-8 col-xs-8">
                        <div class="line-label">
                            {{ 'products_total'|trans }}
                        </div>
                    </div>
                    <div class="col-md-4 col-xs-4 text-right">
                        <div class="line-amount">
                            {{ basket.subtotal|money }}
                        </div>
                    </div>
                </div>

                {# shipping total #}
                <div class="row line valign-middle">
                    <div class="col-md-8 col-xs-8">
                        <div class="line-label">
                            {{ 'w_shipping_total_amount'|trans }}
                        </div>
                    </div>
                    <div class="col-md-4 col-xs-4 text-right">
                        <div class="line-amount">
                            {{ basket.shippingTotal|money }}
                        </div>
                    </div>
                </div>
            </div>

            {# order total #}
            <div class="total">
                <div class="row line valign-middle">
                    <div class="col-xs-6">
                        <div class="line-label">{{ 'total_basket'|trans }}</div>
                    </div>
                    <div class="col-xs-6 text-right">
                        <div class="line-amount">
                            {{ basket.total|money }}
                        </div>
                        <span class="vat"><i>{{ 'included'|trans }} {{  basket.getTaxTotal|money }} {{ 'TVA'|trans }}</i></span>
                    </div>
                </div>
            </div>
            </div>
        </div>
    </div>
</aside>
