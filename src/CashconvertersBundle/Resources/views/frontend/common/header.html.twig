{# HEADER #}
{% set userBasket = getBasket(true) %}
<script> var currentRoute = '{{ app.request.attributes.get('_route') }}'; </script>
<header id="masthead">
    {% set headband = 'headband_content'|trans %}
    {% if headband is not empty %}
        <div id="header-bandeau">
                {{ 'headband_content'|trans|raw }}
        </div>
    {% endif %}
    {# User bar #}
    <div id="user-bar" class="hidden-xs">
        <div class="container">
            <div class="slim-header" v-cloak>
                {# searchbar - short version#}
                <form action="{{ path('search') }}" method="get" autocomplete="off" id="top-search-bar"></form> {# will hold search bar when window is scrolled #}

                {# Connection + Basket #}
                <div class="connection-basket">
                    {# Account link + dropdown/popup #}
                    {{ include ("@App/frontend/common/header/account.html.twig") }}
                    &emsp;|&emsp;
                    {# Basket link + dropdown/popup #}
                    {{ include ("@App/frontend/common/header/basket-popup.html.twig") }}
                </div>
            </div>
        </div>
    </div>
    <form id="header-form" action="{{ path('search') }}" method="get" autocomplete="off">
        {# Logo and inputs #}
        <div id="header-main" class="hidden-xs">
            <div class="container">
                <div class="row">
                    {# Logo #}
                    <a href="/"><img src="{{ asset('images/logo.png') }}"></a>
                    {# Inputs #}
                    <div id="header-inputs-text" class="text-center" v-cloak>
                        <span>{{ 'baseline1'|trans }}</span>
                        {{ include("@App/frontend/common/header/search-bar.html.twig") }}
                    </div>
                </div>
            </div>
        </div>
        {# Header mobile (Annule et remplace) #}
        {{ include("@App/frontend/common/header/mobile.html.twig") }}
        {# Filter seller #}
        <filter-seller></filter-seller>
    </form>
    {# Barre des catégories #}
    {{ include("@App/frontend/common/header/categories-menu.html.twig") }}
</header>
{{ include("@App/frontend/common/header/filter-seller.html.twig") }}
<script>
    var viewHeader;

    $(function () {
        var search = new SearchClient('{{ path('api_search_products') }}', '{{ path('api_search_products_autocomplete') }}');
        urlParameters = search.restoreSearchFromUrl();

        const DEFAULT_GEO_RADIUS = 20000;
        var homePage = currentRoute === 'home';

        viewHeader = new Vue({
            el: '#masthead',
            data: {
                currentCategoryId: {{ currentCategory|default(null) ? currentCategory.id : 'null' }},
                currentCategoryName: '{{ currentCategory|default(null) ? currentCategory.name|e('js') : 'all_categories'|trans|e('js') }}',
                query: urlParameters.query || '',
                geoFilter: urlParameters.geoFilter || {},
                suggestions: [],
                googleMapsAutocomplete: null,
                geocoder: {}
            },

            methods: {

                selectCategory: function (categoryId, categoryName) {
                    this.currentCategoryId = categoryId;
                    this.currentCategoryName = categoryName;
                },

                autocomplete: function (query) {
                    var self = this;

                    search.autocomplete(query, function (results) {
                        self.suggestions = results;
                    });
                },

                // called on Google Maps autocomplete
                geolocAutocompleted: function () {
                    var geolocInput = document.getElementById('geoloc-address-input');
                    var geolocInputValue = geolocInput.value;
                    var place = this.googleMapsAutocomplete.getPlace();

                    // input is empty
                    if (geolocInputValue === '') {
                        this.geoFilter = {};
                        return;
                    }

                    // User pressed enter before selecting a suggestion
                    if (! place.geometry) {

                        var self = this;

                        // get 'prediction' from user's incomplete input
                        this.geocoder.geocode({
                            'address': geolocInputValue
                        }, function(results, status) {

                            if (status === google.maps.GeocoderStatus.OK) {

                                self.geoFilter = {
                                    lat: results[0].geometry.location.lat(),
                                    lng: results[0].geometry.location.lng(),
                                    label: geolocInputValue,
                                    radius: DEFAULT_GEO_RADIUS
                                };
                            }
                        });
                        return;
                    }

                    this.geoFilter = {
                        lat: place.geometry.location.lat(),
                        lng: place.geometry.location.lng(),
                        label: place.name,
                        radius: DEFAULT_GEO_RADIUS
                    };

                    // set formatted address in input field
                    var $geolocInput = $('#geoloc-address-input');
                    $geolocInput.val(place.formatted_address);
                    $geolocInput.blur();
                },

                // Get geolocation from the user's device
                geolocateMe: function () {
                    if (!'geolocation' in navigator) {
                        alert('{{ 'geoloc_not_supported'|trans|e('js') }}');
                        return;
                    }
                    var self = this;
                    navigator.geolocation.getCurrentPosition(function (position) {

                        // get coordinates corresponding label
                        var latlng = new google.maps.LatLng(position.coords.latitude, position.coords.longitude);
                        self.geocoder.geocode({
                            'latLng': latlng
                        }, function (results, status) {
                            if (status === google.maps.GeocoderStatus.OK) {
                                if (results[1]) {

                                    var response = results[1];

                                    self.geoFilter = {
                                        lat: position.coords.latitude,
                                        lng: position.coords.longitude,
                                        label: response.formatted_address,
                                        radius: DEFAULT_GEO_RADIUS
                                    };

                                } else {
                                    alert("{{ 'geoloc_error'|trans }}");
                                }
                            } else {
                                alert("{{ 'geoloc_error'|trans }}");
                            }
                        });
                    }, function (error) {
                        switch(error.code) {
                            case error.PERMISSION_DENIED:
                                alert('{{ 'geoloc_denied'|trans|e('js') }}');
                                break;
                            default:
                                alert('{{ 'geoloc_not_supported'|trans|e('js') }}');
                                break;
                        }
                    });
                },

                formValidation: function(e) {
                    e.preventDefault();

                    // there's a race condition between form submission and lat/lng change
                    // -> submit form after a small timeout
                    var $form = $(e.target);

                    setTimeout(function() {
                        $form.unbind().submit();
                    }, 100);
                },

                removeGeoloc: function(e) {
                    var $removeButton = $(e.target);
                    $removeButton.siblings().val('');
                    this.geoFilter = {}; // will also trigger 'refreshGeoFilter' event
                },

                bindEvents: function() {

                    // submit form after validation
                    $('#header-form').on('submit', this.formValidation.bind(this));

                    // reset geoloc
                    $('.reset-geoloc').on('click', this.removeGeoloc.bind(this));

                    // Filter Seller: show/hide the filter seller except for the desktop version without home page.
                    if (!homePage) {

                        var filterSellerShowModal = document.getElementById('filter-seller-show-modal');
                        var filterSellerHideModal = document.getElementById('filter-seller-hide-modal');

                        filterSellerShowModal.addEventListener('click', function() {
                            cptfilterSeller.toggleMenu();
                        });

                        filterSellerHideModal.addEventListener('click', function() {
                            cptfilterSeller.toggleMenu();
                        });

                    }

                }

            },

            ready: function () {

                // Initialize the Google Maps autocomplete in the search bar
                var geolocInput = document.getElementById('geoloc-address-input');
                this.googleMapsAutocomplete = new google.maps.places.Autocomplete(geolocInput);
                this.googleMapsAutocomplete.addListener('place_changed', this.geolocAutocompleted);

                // Google maps geocoder for inverted geolocation
                this.geocoder = new google.maps.Geocoder();

                // register events
                this.bindEvents();
            }

        });

        // Display the autocomplete popup when the query changes
        viewHeader.$watch('query', function (query) {
            if (query.length >= 3) {
                this.autocomplete(query);
            } else {
                this.suggestions = [];
            }
        });

        viewHeader.$watch('geoFilter', function () {
            // trigger a document based event to let 'body Vue' know when to refresh search results
            $(document).trigger('refreshGeoFilter', [this.geoFilter]);
        });

        // watch for geoloc reset on search page
        $(document).on('reset-geolocation', function() {
            viewHeader.geoFilter = {};
        });

        // watch for geoloc launch on search page
        $(document).on('launch-geolocation', function() {
            viewHeader.geolocateMe();
        });

        // DOM manipulations
        // =================

        // change selected category
        $('#categorySelector').on("change", function(){
            var categoryId = $(this).find(":selected").data("id");
            var categoryName = $(this).find(":selected").text();

            viewHeader.selectCategory(categoryId, categoryName);
        });

        // move slim search-bar on window scroll
        var $topSearchbarContainer = $('#top-search-bar');
        var $bottomSearchbarContainer = $('.search-field');

        var $searchBar = $('#header-inputs-text').find('.product-search-input');
        var $breakPoint = 100;

        // moving search bar only if it's not mobile
        if (!$('#mobile-header').is(':visible')) {
            $(window).scroll(function() {
                if ($(document).scrollTop() > $breakPoint) {

                    // doesn't try to prepend it when it's already a child
                    // (avoid animation flickering)
                    if(! $topSearchbarContainer.find('.product-search-input').length) {
                        // fix userbar to the top
                        $('#user-bar').addClass('is-scrolled');

                        $topSearchbarContainer.prepend($searchBar);

                        // hide remaining parts of the form
                        $('#header-inputs').addClass('hidden');

                        // Filter seller
                        $('#filter-seller').addClass('is-scrolled');
                    }
                } else {
                    $('#user-bar').removeClass('is-scrolled');
                    $bottomSearchbarContainer.append($searchBar);
                    $('#header-inputs').removeClass('hidden');
                    $('#filter-seller').removeClass('is-scrolled');
                }
            });
        }

        // hide search suggestions when user has clicked somewhere else
        $(document).on('click', function(e) {
            var $target = $(e.target);

            // do nothing if user clicked inside input field or a suggestion
            if($target.closest('#header-inputs').length || $target.closest('#top-search-bar').length || $target.closest('#mobile-header-bot').length ) {
                return;
            }

            $('.suggestions').addClass('hidden');
        });

        // show suggestions when input field is focused
        $('[name="q"]').on('focus', function() {
            $('.suggestions').removeClass('hidden');
        });

    });
</script>
