{% set profil_update = cscart_url('profiles.update') %}

{# account #}
<div class="account-dropdown">
    <a href="#" class="trigger-account-popup">
        <img src="{{ asset('images/icons/User_header.png') }}">
        {% if currentUser %}
            {% if currentUser.firstname %}
                <span>{{ currentUser.firstname|upper }}</span>
            {% else %}
                <span>{{ currentUser.email|upper }}</span>
            {% endif %}
        {% else %}
            <span>{{ 'connexion'|trans|upper }}</span>
        {% endif %}
    </a>
    <div class="account-dropdown-menu hidden">
        <div class="dropdown">

            {# user is logged in #}
            {% if currentUser %}
                <div class="actions">
                    <ul>
                        <li><a href="{{ profil_update }}" class="action profile-link">{{ 'profile_details'|trans|capitalize }}</a></li>
                        <li><a href="{{ cscart_url('discuss.list') }}" class="action profile-link">{{ 'discuss_menu'|trans|capitalize }}</a></li>
                        <li><a href="{{ cscart_url('orders.search') }}" class="action profile-link">{{ 'w_my_purchases'|trans|capitalize }}</a></li>
                        <li><a href="{{ cscart_url('auth.logout') }}" class="action logout">{{ 'profile_sign_out'|trans|capitalize }}</a></li>
                    </ul>
                </div>

                {# user is not logged in #}
            {% else %}

                {# user wants to log in #}
                <div class="login-actions actions-block">
                    <div class="actions">

                        {# login form #}
                        <form action="{{ cscart_url('index.php?dispatch=auth.login') }}" method="post">
                            <input type="hidden" name="return_url" value="{{ profil_update }}">
                            <input type="hidden" name="redirect_url" value="{{ profil_update }}">
                            <input type="hidden" name="remember_me" value="1">

                            <div class="block-title text-center"><h3>{{ 'login_title'|trans|raw }}</h3></div>

                            {# inputs #}
                            <div class="form-group">
                                <input type="email" name="user_login" required="required" placeholder="{{ 'email'|trans }}" class="input_text bloc_existing_user_email bloc_width_full text-left">
                                <input type="password" name="password" required="required" placeholder="{{ 'password'|trans }}" class="input_text bloc_existing_user_password bloc_width_full">

                                {# recover password #}
                                <div class="forget-password-block">
                                    <a class="forget-password-link" href="{{ path('recover_password') }}">{{ 'forgot_password_question'|trans }}</a>
                                </div>
                            </div>

                            {# submit #}
                            <button type="submit" name="dispatch[auth.login]" class="action login btn profile-btn black">{{ 'sign_in'|trans }}</button>
                        </form>

                        {# go to registration form #}
                        <a href="{{ path('login') }}" class="action signup btn profile-btn pink">{{ 'register'|trans }}</a>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>


<script>
    $(function(){
        $('.trigger-account-popup').on('click', function(e){
            e.preventDefault();

            $('.account-dropdown-menu').toggleClass('hidden');
        });
    });
</script>

