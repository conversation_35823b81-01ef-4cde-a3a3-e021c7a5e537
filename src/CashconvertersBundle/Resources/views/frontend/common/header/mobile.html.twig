{% set userBasket = getBasket(true) %}
{# Mobile header#}
<div id="mobile-header" class="visible-xs">

    {# Mobile header top part #}
    <div id="mobile-header-top" class="text-center">

        {# Menu button #}
        <div id="mobile-menu-button" class="col-xs-2">
            <img src="{{ asset('images/menu.png') }}" onclick="openNav()">
        </div>

        {# Logo #}
        <div id="mobile-logo" class="col-xs-6">
            <a href="/"><img src="{{ asset('images/logo.png') }}"></a>
        </div>

        {# Cart + item counter #}
        <a href="{{ path('basket_view') }}">
            <div id="mobile-cart" class="col-xs-2">
                <img id="cart-before-hover" src="{{ asset('images/icons/Shopping_cart.png') }}">
                <img id="cart-after-hover" src="{{ asset('images/icons/Shopping_cart_bordeau.png') }}">
                <p>
                    {% if userBasket and userBasket.totalQuantity > 0 %}
                        ({{ userBasket.totalQuantity }})
                    {% endif %}
                </p>
            </div>
        </a>

        {# Localize button #}
        <div id="mobile-localize" class="hidden">
            <button id="localize" type="button" class="btn btn-default" aria-label="{{ 'Localize'|trans }}" @click="geolocateMe">
                <span class="glyphicon glyphicon-map-marker"></span>
            </button>
        </div>

        {# Filter Seller: show/hide the button except for the desktop version without home page. #}
        {% set currentRoute = app.request.attributes.get('_route') %}
        {% if currentRoute is defined and currentRoute != 'home' %}
            <div id="filter-seller-show-modal">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" preserveAspectRatio="xMidYMid meet"><path d="M0 208c0 29.781 20.438 54.594 48 61.75V480H16c-8.813 0-16 7.156-16 16s7.188 16 16 16h480c8.875 0 16-7.156 16-16s-7.125-16-16-16h-32V269.75c27.562-7.156 48-31.969 48-61.75v-16H0v16zm320 64c35.375 0 64-28.656 64-64 0 29.781 20.438 54.594 48 61.75V480H192V272c35.375 0 64-28.656 64-64 0 35.344 28.688 64 64 64zm-144-2.25V480H80V269.75c27.563-7.156 48-31.969 48-61.75 0 29.781 20.438 54.594 48 61.75zM448 48H64L0 176h512L448 48zM135.188 83.563l-32 64c-1.438 2.813-4.25 4.438-7.188 4.438-1.188 0-2.406-.25-3.563-.844-3.938-1.969-5.563-6.781-3.563-10.719l32-64c2-3.938 6.781-5.531 10.719-3.594 3.97 1.969 5.532 6.781 3.595 10.719zm64 0l-32 64c-1.438 2.813-4.25 4.438-7.188 4.438-1.188 0-2.406-.25-3.563-.844-3.938-1.969-5.563-6.781-3.563-10.719l32-64c2-3.938 6.813-5.531 10.719-3.594 3.97 1.969 5.532 6.781 3.595 10.719zM264 144c0 4.438-3.562 8-8 8-4.406 0-8-3.563-8-8V80c0-4.438 3.594-8 8-8 4.438 0 8 3.563 8 8v64zm91.875 7c-1.25.688-2.562 1-3.875 1-2.812 0-5.562-1.5-7-4.156l-35-64c-2.125-3.875-.688-8.75 3.188-10.844 3.813-2.125 8.75-.75 10.875 3.156l35 64c2.062 3.875.687 8.75-3.188 10.844zm63.687.156c-1.124.594-2.312.844-3.562.844-2.938 0-5.75-1.625-7.125-4.438l-32-64c-2-3.938-.375-8.75 3.562-10.719 3.875-1.969 8.75-.375 10.75 3.594l32 64c1.938 3.938.375 8.751-3.625 10.719zM136 386.438v-36.875c-4.688-2.812-8-7.688-8-13.562 0-8.844 7.188-16 16-16 8.875 0 16 7.156 16 16 0 5.875-3.281 10.75-8 13.562v36.875c4.719 2.813 8 7.688 8 13.563 0 8.844-7.125 16-16 16-8.813 0-16-7.156-16-16 0-5.876 3.313-10.751 8-13.563zM64 16c0-8.844 7.188-16 16-16h352c8.875 0 16 7.156 16 16s-7.125 16-16 16H80c-8.812 0-16-7.156-16-16zm216.438 341.656l-11.312-11.313 45.25-45.25 11.312 11.313-45.25 45.25zm0 45.25l-11.312-11.313 90.5-90.5 11.312 11.313-90.5 90.5zm79.187-56.562l11.312 11.313-45.25 45.25-11.312-11.313 45.25-45.25z"/></svg>
            </div>
        {% endif %}

    </div>

    {# Mobile header bottom part#}
    <div id="mobile-header-bot">
        <div class="col-xs-12">

            {# Search input + icon #}
            {# to avoid code conflict, element will be moved between desktop and mobile via javascript #}
            <div id="mobile-header-search-container" v-cloak></div> {# placeholder for product-search-input.html.twig #}
        </div>
    </div>
</div>

{# Mobile menu overlay #}
<div id="mobile-nav" class="overlay">
    <div class="container">
        <a href="javascript:void(0)" class="closebtn" onclick="closeNav()">&times;</a>
        <div class="overlay-content">
            <p><b>{{ 'menu'|trans|upper }}</b></p>
            <a href="{{ path('categories') }}">
                <div class="mobile-menu-category col-xs-1"></div>
                <div class="mobile-menu-category col-xs-10"><span>{{ 'categories'|trans }}</span></div>
                <div class="mobile-menu-category col-xs-1">
                    <span class="glyphicon glyphicon-menu-right"></span>
                </div>
            </a>
            <a href="{{ 'our_shops_url'|trans }}">
                <div class="mobile-menu-category col-xs-12"><span>{{ 'ourshops'|trans }}</span></div>
            </a>
            <a href="#">{# TODO: put the right link #}
                <div class="mobile-menu-category col-xs-12"><span>{{ 'mywishlist'|trans }}</span></div>
            </a>
            <a href="#">{# TODO: put the right link #}
                <div class="mobile-menu-category col-xs-12"><span>{{ 'my_alerts'|trans }}</span></div>
            </a>
            <a href="{{ path('login') }}">
                <div class="mobile-menu-category col-xs-12"><span>{{ 'myaccount'|trans }}</span></div>
            </a>
            <a href="{% if currentUser %}{{ cscart_url("orders.search") }}{% else %}{{ path('login') }}{% endif %}">
                <div class="mobile-menu-category col-xs-12"><span>{{ 'suivi'|trans }}</span></div>
            </a>
            <a href="{% if currentUser %}{{ cscart_url('rma.sav') }}{% else %}{{ path('login') }}{% endif %}">
                <div class="mobile-menu-category col-xs-12"><span>{{ 'sav'|trans }}</span></div>
            </a>
            <a href="{{ 'services_link'|trans }}">
                <div class="mobile-menu-category col-xs-1"></div>
                <div class="mobile-menu-category col-xs-10"><span>{{ 'ourservices'|trans }}</span></div>
                <div class="mobile-menu-category col-xs-1">
                    <span class="glyphicon glyphicon-menu-right"></span>
                </div>
            </a>
            <a href="{{ 'franchise_link'|trans }}">
                <div class="mobile-menu-category col-xs-12"><span>{{ 'franchise'|trans }}</span></div>
            </a>
            <a href="{{ "contact_page_url"|trans }}">
                <div class="mobile-menu-category col-xs-12"><span>{{ 'contact'|trans }}</span></div>
            </a>
        </div>
    </div>
</div>

<script>
    function openNav() {
        document.getElementById("mobile-nav").style.height = "100%";
    }

    function closeNav() {
        document.getElementById("mobile-nav").style.height = "0%";
    }
</script>
