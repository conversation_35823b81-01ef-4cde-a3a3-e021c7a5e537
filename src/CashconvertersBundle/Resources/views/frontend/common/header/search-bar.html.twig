{# Header search fields #}
<div id="header-inputs">
    {# Un produit, une marque...#}
    <div id="desktop-header-search-container" class="col-md-7 col-xs-11 search-field">
        {{ include ("@App/frontend/common/header/product-search-input.html.twig") }}
    </div>

    {# Ville, département #}
    <div id="header-geoloc-address-container" class="col-xs-4 hidden">
        {# Hidden fields use to forward the geoloc to the search page #}
        <input type="hidden" name="geo[lat]" v-model="geoFilter.lat">
        <input type="hidden" name="geo[lng]" v-model="geoFilter.lng">
        <input type="hidden" name="geo[radius]" v-model="geoFilter.radius">
        <input id="geoloc-address-input" name="geo[label]" v-model="geoFilter.label" type="text"
               class="form-control full-btn" placeholder="{{ 'search_loc'|trans }}">
        <span class="reset-geoloc">&times;</span>
    </div>

    {# Bouton localisation #}
    <div id="header-geoloc-button-container" class="col-xs-1 hidden">
        <button id="geoloc-button" type="button" class="btn btn-default" aria-label="{{ 'Localize'|trans }}" @click="geolocateMe">
            <span class="glyphicon glyphicon-map-marker"></span>
        </button>
    </div>
</div>
