<template id="filter-seller-template">
    {# Filter seller component remaining pages without home page. #}
    <div id="filter-seller" v-if="!homePage" :class="{'active': modal}">
        <div class="filter-seller-inside">
            <div class="filter-seller-items">
                <span v-for="company in getCompaniesSelected" @click="removeFilterSeller(company.id)">${company.name}<em></em></span>
            </div>
            <div class="filter-seller-label" :class="{active: isOpen}" @mouseleave="closeFilterSeller()">
                <span @click="openFilterSeller()">{{ 'filter_seller_label'|trans }}</span>
                <ul>
                    <li v-for="company in getCompaniesUnselected">
                        <span v-text="getCompleteName(company)" @click="addFilterSeller(company.id)"></span>
                    </li>
                </ul>
                <div class="filter-seller-clear" @click="removeFilterSellerAll(company)"><span>×</span></div>
            </div>
        </div>
        <div id="filter-seller-hide-modal"><span>×</span></div>
    </div>
</template>

<script>
var companiesData = {
    /** @var int[] **/
    companiesIdSelected: [],
    /** @var object[] **/
    listCompanies: [],
    /** @var int[] **/
    listCompaniesIdEach: [],
    homePage: (currentRoute === 'home'),
    modal: false
};

var cptfilterSeller;

$(function() {

    companiesMixin = {};

    companiesMixin.install = function(Vue, options) {
        Vue.mixin({
            created: function() {
                this.getCompaniesSelectedByStore();
                this.loadCompanies();
            },
            methods: {
                /** @param int[] **/
                setCompaniesIdEach: function(idCompanies) {
                    this.listCompaniesIdEach = idCompanies;
                    cptfilterSeller.listCompaniesIdEach = this.listCompaniesIdEach;
                },
                getCompaniesSelectedByStore: function ()
                {
                    var companiesIdByLocal = localStorage.getItem('company');

                    try {
                        var data = JSON.parse(companiesIdByLocal);
                        this.setCompaniesSelected(data);
                    }
                    catch(error) {
                        this.cleanCompanySelected();
                    }
                },
                /**
                * @param companiesSelected array
                */
                setCompaniesSelected: function (companiesSelected)
                {
                    this.companiesIdSelected = companiesSelected;
                    this.setCompaniesSelectedInLocalStorage();
                },
                setCompaniesSelectedInLocalStorage: function()
                {
                    localStorage.setItem('company', JSON.stringify(this.companiesIdSelected));

                    if (typeof vm !== 'undefined') {
                        vm.filters['companies'] = this.companiesIdSelected.map(function (id) {
                            return id.toString();
                        });
                        vm.companiesIdSelected = this.companiesIdSelected;
                        vm.refresh();
                    }

                    if (typeof cptfilterSeller !== 'undefined') {
                        cptfilterSeller.companiesIdSelected = this.companiesIdSelected;
                    }
                },
                /**
                * @param int companyId
                * @return int
                */
                findCompanyIdSelected: function(companyId)
                {
                    return this.companiesIdSelected.indexOf(companyId);
                },
                toggleCompanyIdSelected: function(companyId)
                {
                    var index =  this.companiesIdSelected.indexOf(companyId);

                    if (index > -1) {
                        this.removeCompanySelectedById(companyId);
                    } else {
                        this.addCompanySelectedById(companyId);
                    }
                },
                /**
                * @param companyId int
                */
                removeCompanySelectedById: function(companyId)
                {
                    var index = this.findCompanyIdSelected(companyId);

                    if (index !== -1) {
                        this.companiesIdSelected.splice(index, 1);
                        this.setCompaniesSelectedInLocalStorage();
                    }
                },
                /**
                * @param companyId int
                */
                addCompanySelectedById: function(companyId)
                {
                    var index = this.findCompanyIdSelected(companyId);

                    if (index === -1) {
                        this.companiesIdSelected.push(companyId);
                        this.setCompaniesSelectedInLocalStorage();
                    }
                },
                cleanCompanySelected: function()
                {
                    this.companiesIdSelected = [];
                    this.setCompaniesSelectedInLocalStorage();
                },
                loadCompanies: function ()
                {
                    var self = this;

                    $.get('{{ path('api_catalog_company_list') }}')
                        .done(function (listCompanies) {
                            self.setListCompanies(listCompanies);
                        })
                },
                /**
                * @param array listCompanies
                */
                setListCompanies: function(listCompanies)
                {
                    this.listCompanies = listCompanies;
                }
            },
            data: function () {
                return companiesData;
            }
        });
    };

    Vue.use(companiesMixin);

});

var filterSeller = {
    template: '#filter-seller-template',
    data: function() {
        return {
            name: 'filter-seller',
            isOpen: false
        }
    },
    computed: {
        getCompaniesSelected: function () {
            var self = this;

            return this.listCompanies.filter(function (company) {
                return self.companiesIdSelected.includes(company.id);
            });
        },
        getCompaniesUnselected: function() {
            var self = this;

            var listCompaniesUnselected = this.listCompanies.filter(function (company) {
                return !self.companiesIdSelected.includes(company.id);
            });

            if (this.listCompaniesIdEach.length > 0) {
                listCompaniesUnselected = listCompaniesUnselected.filter(function (company) {
                    return self.listCompaniesIdEach.includes(company.id);
                });
            }

            return listCompaniesUnselected.sort(function(a, b) {
                var cashConverters = 'Cash Converters ';
                a = a.name.replace(cashConverters, '');
                b = b.name.replace(cashConverters, '');
                return a.localeCompare(b);
            });
        }
    },
    methods: {
        getCompleteName: function(company) {
            return company.name;
        },
        openFilterSeller: function() {
            return this.isOpen = !this.isOpen;
        },
        closeFilterSeller: function() {
            return this.isOpen = false;
        },
        /**
        * @param int companyId
        */
        addFilterSeller: function(companyId) {
            this.addCompanySelectedById(companyId);
        },
        /**
        * @param int companyId
        */
        removeFilterSeller: function(companyId) {
            this.removeCompanySelectedById(companyId);
        },
        removeFilterSellerAll: function() {
            this.cleanCompanySelected();
        },
        toggleMenu: function() {
            this.modal = !this.modal;
        }
    },
    created: function () {
        cptfilterSeller = this;
    }
};

$(function() { Vue.component('filter-seller', filterSeller) });

</script>

