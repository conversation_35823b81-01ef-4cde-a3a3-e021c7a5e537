<div id="header-menu" class="hidden-xs">
    <div class="container text-center">

        {# Menu:
            + Nos produits (messages.fr.json -> menu_item_1)
            + Nos magasins (messages.fr.json -> menu_item_2)
            + Achetez (messages.fr.json -> menu_item_3)
            + Vendez (messages.fr.json -> menu_item_4)
            + Nos services (messages.fr.json -> menu_item_5)
            + Les franchises (messages.fr.json -> menu_item_6)
            + Contact (messages.fr.json -> menu_item_7)
            + Estim'online (messages.fr.json -> menu_item_8) #}

        {% set menus = getMenus() %}

        {# listItems: change the order
            array containing the items from the backoffice without 'Nos produits' (category) #}

        {% set listItems = [
            "menu_item_2"|trans,
            "menu_item_3"|trans,
            "menu_item_4"|trans,
            "menu_item_5"|trans,
            "menu_item_6"|trans,
            "menu_item_7"|trans,
            "menu_item_8"|trans
        ] %}

        <ul class="list-unstyled menu-category-list">
            {# Static: Nos produits #}
            <li class="menu-category">
                <span class="menu-link">{{ 'menu_item_1'|trans|upper }}</span>
                <div class="dropdown-content large-content">
                    <ul>
                        {% set categories = getRootCategories() %}
                        {% for category in categories %}
                            <li><a href="{{ path('category', {categoryPath: category.slug}) }}">{{ category.name }}</a></li>
                        {% endfor %}
                    </ul>
                </div>
            </li>
            {# Dynamic: listItems[] #}
            {% for itemMenu in listItems %}
                {% for menu in menus if menu.name == itemMenu %}
                    <li class="menu-category">
                        <span class="menu-link">{{ menu.name|upper }}</span>
                        <div class="dropdown-content large-content">
                            <ul>
                                {% for item in menu.items %}
                                    {% if item.hasChildren() %}
                                        <li class="display-dropdown-lvl2">
                                            <a href="{{ item.url }}">{{ item.name }}</a>
                                            <div class="dropdown-lvl2">
                                                <ul>
                                                    {% for subItem in item.items %}
                                                        <li><a href="{{ subItem.url }}">{{ subItem.name }}</a></li>
                                                    {% endfor %}
                                                </ul>
                                            </div>
                                        </li>
                                    {% else %}
                                        <li><a href="{{ item.url }}">{{ item.name }}</a></li>
                                    {% endif %}
                                {% endfor %}
                            </ul>
                        </div>
                    </li>
                {% endfor %}
            {% endfor %}
        </ul>

    </div>
</div>
