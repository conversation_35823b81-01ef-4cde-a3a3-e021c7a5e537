{% set userBasket = getBasket(true) %}

{# basket #}
<div class="basket-dropdown">
    <a id="basket-dropdown-btn" href="{{ path('basket_view') }}">
    <img src="{{ asset('images/icons/Shopping_cart.png') }}">
    <span>
        {% if userBasket and userBasket.totalQuantity > 0 %}
            {{ userBasket.totalQuantity }}
        {% endif %}
        {{ 'basket'|trans|upper }}
    </span>
    </a>

    <div class="basket-dropdown-menu hidden">
        <div class="dropdown">
            {# basket 'full' #}
            {% if userBasket %}
                {% for item in userBasket.allItems %}
                    <div class="product">
                        <div class="product-info">
                            <div class="row">
                                <div class="col-xs-4">
                                    {# thumbnail #}
                                    {% if item.mainImage %}
                                        <img src="{{ image_url_by_id(item.mainImage.id|default(0), 80) }}" alt="" class="product-thumbnail">
                                    {% else %}
                                        <div class="no-image-block"><p class="product-no-image"><img src="{{ asset('images/no-image.jpg') }}" width="80px"></p></div>
                                    {% endif %}
                                </div>
                                <div class="col-xs-8">
                                    {# name #}
                                    <span class="product-name">
                                        {% if item.quantity > 1 %}{{ item.quantity~' x ' }}{% endif %}
                                        {{ item.productName|truncate('25', true, '...') }}
                                    </span>

                                    {# options #}
                                    {% set options = getDeclinationOptions(item.productId, item.declinationId) %}
                                    <ul class="product-attributes list-unstyled">
                                        {% for option in options %}
                                            <li>{{ option.option_name }} : {{ option.value_name }}</li>
                                        {% endfor %}
                                    </ul>

                                    {# price #}
                                    <span class="product-price">{{ item.total|money }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}

                <div class="actions">
                    <a href="{{ path('basket_view') }}" class="action checkout btn merchant-btn black">{{ 'place_order'|trans }}</a>
                    <a href="{{ path('basket_view') }}" class="action view-cart btn merchant-btn pink">{{ 'view_cart'|trans }}</a>
                </div>

                {# empty basket #}
            {% else %}
                <div class="empty">
                    <p>{{ 'text_cart_empty'|trans }}</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>


<script>
    $(function(){
        $('.basket-dropdown').hover(function(){
            $('.basket-dropdown-menu').toggleClass('hidden');
        });
    });
</script>
