<div class="product-search-input">
    <div class="input-group">
        <input class="search form-control" type="text" name="q" placeholder="{{ 'search_main'|trans }}"
               v-model="query" debounce="500" @keyup.esc="query = ''">

        <div class="input-group-btn">
            <button type="submit" class="btn btn-default btn-search">
                <span class="glyphicon glyphicon-search"></span>
            </button>
        </div>
    </div>

    <div class="suggestions">
        <ul class="autocomplete list-unstyled" v-if="suggestions">
            <li v-for="suggestion in suggestions">
                <a href="{{ path('search') }}?q=${ encodeURIComponent(suggestion.name) }">${ suggestion.name }</a>
            </li>
        </ul>
    </div>
</div>
