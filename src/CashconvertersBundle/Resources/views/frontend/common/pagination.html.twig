{# medium and large screens #}
<ul class="pagination-block list-inline hidden-xs"
    v-if="totalPages > 1"> {# no need for pagination if there is only one page #}

    {# go to previous page (if there are at least 2 pages) #}
    <li class="page-item" v-if="totalPages >= 2">
        <button class="btn btn-pagination"
                @click="goToPage(pagination.page - 1)"
                :class="{disabled: pagination.page <= 1}">

            <span class="glyphicon glyphicon-menu-left"></span>
        </button>
    </li>

    {# go to first page #}
    <li class="page-item" v-if="pagination.page > 2">
        <button class="btn btn-pagination"
                @click="goToPage(1)">1
        </button>
    </li>

    {# dots #}
    <li v-if="pagination.page > 3" class="page-item">
        ...
    </li>

    {# page preceding current page #}
    <li class="page-item" v-if="pagination.page > 1">
        <button class="btn btn-pagination"
                @click="goToPage(pagination.page - 1)"
                v-text="pagination.page - 1">
        </button>
    </li>

    {# current (active) page #}
    <li class="page-item">
        <button class="btn btn-pagination active"
                v-text="pagination.page">
        </button>
    </li>

    {# page following current page #}
    <li class="page-item" v-if="pagination.page < totalPages">
        <button class="btn btn-pagination"
                @click="goToPage(pagination.page + 1)"
                v-text="pagination.page + 1">
        </button>
    </li>

    <li v-if="pagination.page < (totalPages - 2)">
        ...
    </li>

    {# go to last page #}
    <li class="page-item" v-if="pagination.page < (totalPages - 1)">
        <button class="btn btn-pagination"
                @click="goToPage(totalPages)"
                v-text="totalPages">
        </button>
    </li>

    {# go to following page (if there are at least 2 pages) #}
    <li class="page-item" v-if="totalPages >= 2">
        <button class="btn btn-pagination"
                @click="goToPage(pagination.page + 1)"
                :class="{disabled: pagination.page == totalPages}">

            <span class="glyphicon glyphicon-menu-right"></span>
        </button>
    </li>
</ul>


{# small screens #}
<div class="pagination-block screen-xs" v-if="totalPages > 1">
    <button class="btn btn-pagination" :class="{ 'disabled': pagination.page <= 1 }" @click="goToPage(pagination.page - 1)"><span class="glyphicon glyphicon-menu-left"></span></button>

    {# page dropdown #}
    <div class="page" @click="togglePageList">
        <div class="page-dropdown">
            <span v-text="'{{ 'page'|trans }} ' + pagination.page"></span>
            <i class="glyphicon glyphicon-triangle-bottom" v-if="totalPages > 1"></i>
        </div>

        {# dropdown pages #}
        <ul class="list-unstyled pages">

            {# show 'n' pages (mobilePageNumbersConstraint) before current page #}
            <li class="page-number"
                v-for="index in mobilePageNumbersConstraint"
                v-if="pagination.page > (mobilePageNumbersConstraint - index)"
                v-text="'{{ 'page'|trans }} ' + (pagination.page - (mobilePageNumbersConstraint - index))"
                @click="goToPage(pagination.page - (mobilePageNumbersConstraint - index))">
            </li>

            {# show current page #}
            <li class="page-number active"
                v-text="'{{ 'page'|trans }} ' + pagination.page">
            </li>

            {# show 'n' pages (mobilePageNumbersConstraint) after current page #}
            <li class="page-number"
                v-for="index in mobilePageNumbersConstraint"
                v-if="pagination.page < (totalPages - index)"
                v-text="'{{ 'page'|trans }} ' + (pagination.page + (index + 1))"
                @click="goToPage(pagination.page + (index + 1))">
            </li>
        </ul>
    </div>

    <button class="btn btn-pagination" :class="{ 'disabled': pagination.page == totalPages }" @click="goToPage(pagination.page + 1)"><span class="glyphicon glyphicon-menu-right"></span></button>
</div>
