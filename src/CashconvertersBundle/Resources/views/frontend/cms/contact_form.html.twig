{% extends '@App/frontend/layout.html.twig' %}

{% block content %}

{# breadcrumb #}
<div class="container">
    <section class="breadcrumb hidden-xs">
        <a href="{{ path('home') }}">{{ 'home'|trans }}</a> >
        <b><a href="{{ path('contact') }}">{{ "contact"|trans }}</a></b>
    </section>
</div>

{# Main content #}
<section class="contact-content">
    <div class="container">
        <div class="row">
            <div class="col-xs-8 col-xs-offset-2 contact-main-title">
                <h1 class="text-center">{{ 'contact'|trans }}</h1>
                <hr>
            </div>
        </div>
        <div class="row">
            <img class="img-responsive" src="{{ asset('bundles/cashconverters/images/contact.png') }}">
        </div>
        <div class="row">
            <div class="col-xs-12 col-sm-6 pull-right contact-content">
                <div class="contact-content-title text-center">
                    <h2>{{ 'contact_title_2'|trans }}</h2>
                </div>
                <p>{{ 'contact_paragraph_2'|trans }}</p>
                <a class="btn contact-btn pink" href="{{ 'our_shops_url'|trans }}">{{ 'contact_link_text'|trans }}</a>
            </div>
            <div class="col-xs-12 col-sm-6 push-left contact-content">
                <div class="contact-content-title text-center">
                    <h2>{{ 'contact_title_1'|trans }}</h2>
                </div>
                <p>{{ 'contact_paragraph_1'|trans }}</p>
                <div class="contact-infos">
                    <span class="glyphicon glyphicon-home" aria-hidden="true"></span>
                    <p>
                        {{ 'contact_address_1'|trans }}<br>
                        {{ 'contact_address_2'|trans }}<br>
                        {{ 'contact_address_3'|trans }}
                    </p>
                </div>
                <div class="contact-infos">
                    <span class="glyphicon glyphicon-earphone" aria-hidden="true"></span>
                    <p>{{ 'contact_phone'|trans }}</p>
                </div>
                <div class="contact-infos">
                    <span class="glyphicon glyphicon-send" aria-hidden="true"></span>
                    <p>{{ 'contact_email'|trans }}</p>
                </div>
                <h3 class="text-center">{{ 'send_message'|trans }}</h3>
                <form class="contact-form" method="post" action="{{ path('contact') }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token('contact_form_token') }}">

                    <div class="row">
                        <div class="col-xs-12">
                            <div class="row">
                                <div class="form-group col-xs-12">
                                    <label><span class="form-required">{{ 'last_name'|trans }}</span> : </label>
                                    <input class="form-control" type="text" value="" name="lastname" required>
                                </div>

                                <div class="form-group col-xs-12">
                                    <label><span class="form-required">{{ 'first_name'|trans }}</span> : </label>
                                    <input class="form-control" type="text" value="" name="firstname" required>
                                </div>

                                <div class="form-group col-xs-12">
                                    <label><span class="form-required">{{ 'email'|trans }}</span> : </label>
                                    <input class="form-control" type="email" value="" name="sender" required>
                                </div>

                                <div class="form-group col-xs-12">
                                    <label>{{ 'phone'|trans }} : </label>
                                    <input class="form-control" type="phone" value="" name="phone">
                                </div>

                                <div class="form-group col-xs-12">
                                    <label><span class="form-required">{{ 'discuss_your_message'|trans }}</span> : </label>
                                    <textarea class="form-control" name="content" rows="5" required></textarea>
                                </div>

                                <div class="form-group col-xs-12">
                                    {% if currentUser is null %}
                                        {{ captcha() }}
                                    {% endif %}
                                </div>

                                <div class="form-group col-xs-12">
                                    <button class="btn contact-btn pink" type="submit">{{'send'|trans}}</button>
                                </div>

                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        {% if 'contact_mandatory_information_paragraph_1'|trans|length > 0 %}
            <div class="single-row">
                <p>{{ 'contact_mandatory_information_paragraph_1'|trans }}</p>
                {% if 'contact_mandatory_information_paragraph_2'|trans|length > 0 %}
                    <p>{{ 'contact_mandatory_information_paragraph_2'|trans }}</p>
                {% endif %}
                {% if 'contact_mandatory_information_paragraph_3'|trans|length > 0 %}
                    <p>{{ 'contact_mandatory_information_paragraph_3'|trans }}
                        {% if 'contact_mandatory_information_link'|trans|length > 0 %}
                            <a href="{{ 'contact_mandatory_information_link'|trans }}" target="_blank" rel="noopener noreferrer">{{ 'contact_mandatory_information_link'|trans }}</a>
                        {% endif %}
                    </p>
                {% endif %}
            </div>
        {% endif %}

    </div>
</section>
{% endblock %}
