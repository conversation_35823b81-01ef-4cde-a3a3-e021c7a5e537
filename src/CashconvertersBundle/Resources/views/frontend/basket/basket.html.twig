{% extends '@App/frontend/layout.html.twig' %}

{% block meta %}
    {{ parent() }}
    <meta name="robots" content="noindex">
{% endblock %}

{% block content %}
    {# flag div to be give javascript some data #}
    <div class="device-lg visible-lg"></div>

    <main class="checkout-process">

        {% set basketIsNotEmpty = basket and (basket.totalQuantity > 0) %}

        {# checkout steps #}
        {% if basketIsNotEmpty %}
            {% include '@App/frontend/checkout/steps.html.twig' with { step: 1 } %}
        {% endif %}

        {# Basket content #}
        <section class="basket">
            <div class="container">

                <div class="row full-main-content"> {# full main content row #}
                    {# products - left column #}
                    <div class="col-xs-12 basket-items">
                        {% if basketIsNotEmpty %}
                            {% for companyGroup in basket.groups %}
                                <div class="merchant-title">
                                    <h2 class="checkout-main-title">{{ 'Merchant'|trans|upper }} : {{ companyGroup.getCompanyName() }}</h2>
                                    <hr class="checkout-main-title-under">
                                </div>
                                {% for shippingGroup in companyGroup.shippingGroups %}
                                    <div class="product-group">
                                        {% for item in shippingGroup.items %}
                                            <div class="product">
                                                <div class="row">
                                                    <div class="col-xs-4 col-sm-2">
                                                        {% if item.mainImage is null %}
                                                            <div class="product-image-no-image">
                                                                <img src="{{ asset('images/no-image.jpg') }}">
                                                            </div>
                                                        {% else %}
                                                            <div class="product-img">
                                                                <img class="product-img" src="{{ item.mainImage|image_url(120) }}">
                                                            </div>
                                                        {% endif %}
                                                    </div> {# // image column #}

                                                    <div class="col-xs-8 col-sm-10">
                                                        <div class="product-details test-basket-item">

                                                            {# product name #}
                                                            <div class="product-main-info col-xs-12 col-sm-7 col-md-8">
                                                                <p class="product-name">{{ item.productName }}</p>
                                                            </div>

                                                            {# quantity and amount row #}
                                                            <div class="col-xs-12 col-sm-5 col-md-4">

                                                                {# quantity selector column #}
                                                                <div class="col-xs-6 col-sm-7">
                                                                    <span class="hidden-xs">{{ 'quantity'|trans }} : </span>
                                                                    <input data-product-id="{{ item.declinationId }}" type="text" class="product-quantity test-basket-item-quantity" value="{{ item.quantity }}" />
                                                                    <a href="#" data-product-id="{{ item.declinationId }}" class="product-quantity-trash"><i class="glyphicon glyphicon-trash"></i></a>
                                                                </div> {# // quantity selector column #}

                                                                {# amount column #}
                                                                <div class="basket-single-price text-right">
                                                                    <span class="price"> {{ item.total|money }}</span><br>
                                                                    <span><i>{{ item.quantity }} x {{ item.individualPrice|money }}</i></span>
                                                                </div> {# // amount column #}
                                                            </div> {# // quantity and amount row #}
                                                        </div> {# // product details #}
                                                    </div> {# // product details column #}
                                                </div> {# // product main row #}
                                            </div> {# // single product #}
                                        {% endfor %} {# // item in shippingGroup.items #}

                                        {# shipping group block #}
                                    </div> {# // product-group #}
                                    <hr>

                                    {% if basketIsNotEmpty and basket.isEligibleToPickupPointsShipping %}
                                        <div class="col-xs-12 pickup-points">{{ 'can_choose_pickup_points'|trans }}</div>
                                    {% endif %}

                                    <div class="basket-select-shipping">
                                        <div class="basket-select-shipping-inside">
                                            <strong>{{ 'select_shipping_mode'|trans }} : </strong>
                                            <div class="basket-radio" data-company="{{ companyGroup.getCompanyName() }}">
                                            {% for shipping in shippingGroup.shippings %}
                                                {% if shipping.type != getChronoRelaisDeliveryConstant() or basket.isEligibleToPickupPointsShipping %}
                                                    <div class="basket-radio-inside">
                                                        <input
                                                                type="radio"
                                                                id="basket-radio-{{ shippingGroup.groupId }}-{{ loop.index }}"
                                                                name="basket-radio-{{ shippingGroup.groupId }}"
                                                                class="basket-radio-input"
                                                                value="{{ shipping.id }}"
                                                                data-type="{{ shipping.type }}"
                                                                data-group-id="{{ shippingGroup.groupId }}"
                                                                data-id="{{ shipping.id }}"
                                                                data-price="{{ shipping.price.amount }}"
                                                                {% if shippingGroup.isSelected(shipping) %}
                                                                    checked
                                                                    data-checked="checked"
                                                                {% endif %}>
                                                        <label class="basket-radio-label" for="basket-radio-{{ shippingGroup.groupId }}-{{ loop.index }}">{{ shipping.price|money }} - {{ shipping.name }}</label>
                                                    </div>
                                                {% endif %}
                                            {% endfor %}
                                            </div>
                                        </div>
                                    </div>
                                    <hr>
                                    {% for shipping in shippingGroup.shippings %}
                                        {% if shippingGroup.isSelected(shipping) and shipping.deliveryTime is not empty %}
                                            <div id="delivery-time"><i>{{ 'delivery_in'|trans }} : {{ shipping.deliveryTime }}</i></div>
                                        {% endif %}
                                    {% endfor %}
                                    {# // shipping group block #}

                                {% endfor %} {# // shippingGroup in companyGroup.shippingGroups #}
                            {% endfor %} {# companyGroup in basket.groups #}
                        {% else %} {# // basketIsNotEmpty #}
                            <h3 class="text-center">{{ 'your_basket_is_empty'|trans }}</h3>
                        {% endif %}
                    </div> {# // left column #}

                    {% if basketIsNotEmpty %}
                        <div class="col-xs-12 payment-recap">
                            {# order summary - right column #}
                            <div class="col-xs-12 col-sm-push-6 col-sm-6">
                                <div class="block validation">
                                    <h2 class="title">{{ 'your_order'|trans|upper }} : </h2>

                                    {# total w/o shipping #}
                                    <div class="prices">
                                        {% if basket.coupons is defined and basket.coupons is not empty %}
                                            <p>{{ 'discount'|trans }} ({{ 'code'|trans }} {{ basket.coupons|default([])|join(', ') }}) : <span>-&nbsp;{{ basket.discountTotal|money }}</span> </p>
                                        {% endif %}
                                        <span>{{ 'subtotal'|trans }} :</span><span class="price-tag">{{ basket.subtotal|money }}</span><br>
                                        <span>{{ 'shipping_total_amount'|trans }} : </span><span class="price-tag">{{ basket.shippingTotal|money }}</span><br><br>

                                        {# order total #}
                                        <div class="total">
                                            <span>{{ 'total_inc_tax'|trans }} : </span><span class="price price-tag">{{ basket.total|money }}</span><br>
                                        </div>

                                        <span>{{ 'tva_included'|trans({'[tax]': basket.taxTotal|money})|raw }}</span>
                                    </div>
                                </div>
                            </div> {# // right column #}

                            <div class="col-xs-12 col-sm-pull-6 col-sm-6 reinsurance">
                                <p class="col-xs-4 col-sm-12"><img src="{{ asset('images/icons/Icone_PAIEMENT_SECURISE_noir.png') }}"> <br class="visible-xs"> {{ 'secured_payment'|trans }} <img src="{{ asset('images/icons/securedpayment.jpg') }}" class="hidden-xs"> </p>
                                <p class="col-xs-4 col-sm-12"><img src="{{ asset('images/icons/Icone_LIVRAISON_noir.png') }}"> <br class="visible-xs"> {{ 'on_demand_delivery'|trans }}</p>
                                <p class="col-xs-4 col-sm-12"><img src="{{ asset('images/icons/Icone_GARANTIE_noir.png') }}"> <br class="visible-xs"> {{ 'warranty'|trans }}</p>
                            </div>

                        </div>
                    {% endif %} {# // if basketIsNotEmpty #}

                    {% if basketIsNotEmpty %}
                        <div class="row">
                            <div class="col-xs-12 col-sm-5 col-sm-offset-1">
                                <a href="{{ path('home') }}" class="continue btn default-btn purple-btn-outline checkout-btn white">
                                    {{ 'continue_my_shopping'|trans|upper }}
                                </a>
                            </div>

                            <div class="col-xs-12 col-sm-4 col-sm-offset-2">
                                <a href="{{ path('checkout_addresses') }}" class="btn checkout-btn js-confirm-checkout">
                                    {{ 'w_validate_command'|trans|upper }}
                                </a>
                            </div>
                        </div>
                    {% else %}
                        <div class="col-xs-12 col-sm-4">
                            <a href="{{ path('home') }}" class="continue btn default-btn purple-btn-outline checkout-btn white">
                                {{ 'continue_my_shopping'|trans|upper }}
                            </a>
                        </div>
                    {% endif %}

                </div> {# // full main content row #}
            </div> {# // container #}
        </section>
    </main>

    <div class="ajax-loading-box test-waiting-quantity-change"></div>

    {# modal to inform about Chrono Relais shipping method #}
    {% include('@App/frontend/basket/_chronorelais_modal.html.twig') %}

    {# modal to inform about 'Click and Collect' shipping method #}
    {% include('@App/frontend/basket/_click-collect_modal.html.twig') %}

    <script>
        $(function () {

            {# information updates / page reload -------------------------------- #}

            {# update quantity with custom buttons #}
            $('.down-qty').on('click', function(){
                var $inputField = $(this).parents('.quantity').find('input');
                $inputField.val(parseInt($inputField.val()) - 1);
                modifyBasketQuantity($inputField);
            });

            $('.up-qty').on('click', function(){
                var $inputField = $(this).parents('.quantity').find('input');
                $inputField.val(parseInt($inputField.val()) + 1);
                modifyBasketQuantity($inputField);
            });

            {# update quantity with manual input #}
            $('.product-quantity').on('change', function(){
                modifyBasketQuantity($(this));
            });
            $('.product-quantity-trash').on('click', function(e) {
                e.preventDefault();
                removeItemFromBasket($(this));
            });

            function modifyBasketQuantity($inputField){
                W.toggleLoadingBox();
                $.ajax({
                    method: "POST",
                    url: "{{ url('basket_modify_product_quantity') }}",
                    data: {
                        product_id: $inputField.attr('data-product-id'),
                        quantity: $inputField.val()
                    }
                }).done(function () {
                    window.location.reload();
                });
            }

            function removeItemFromBasket($trashLink){
                W.toggleLoadingBox();
                $.ajax({
                    method: "POST",
                    url: "{{ url('basket_modify_product_quantity') }}",
                    data: {
                        product_id: $trashLink.attr('data-product-id'),
                        quantity: 0
                    }
                }).done(function () {
                    window.location.reload();
                });
            }

            {# shipping -------------------------------- #}

            // Select shipping mode, radio button group
            var $selectShipping = $('.basket-radio');

            // Will help to reset chrono relais select element
            var chronorelaisSelector;

            // Register chrono relais cancellation to reset selectors
            $('#chrono-relais-modal').find('[data-dismiss="modal"]').on('click', function() {
                // reset select element (first element is cheapest)
                chronorelaisSelector.selectedIndex = 0;
            });

            // Select chrono relais from modal
            $('#choose-chrono-relais').on('click', function() {
                window.location = '{{ path('checkout_pickup_points') }}';
            });

            // Shipping mode
            $selectShipping.find('input[type=radio]').on('change', function (e) {
                e.preventDefault();
                var $input = $(this);
                var groupId = $input.attr('data-group-id');
                var shippingId = $input.attr('data-id');
                // Register which selector has been triggered to reset it if user cancel their action
                if ($(this).attr('data-type') === '{{ getChronoRelaisDeliveryConstant() }}') {
                    chronorelaisSelector = $(this);
                    $('#chrono-relais-modal').modal('show');
                    return;
                }
                $.ajax({
                    method: "POST",
                    url: "{{ url('basket_select_shipping') }}",
                    data: {
                        group_id: groupId,
                        shipping_id: shippingId
                    }
                }).done(function () {
                    window.location.reload();
                });
            });

            {# Click and collect #}
            {# Au clic sur le bouton "validation du panier",
               on vérifie qu'aucun des moyens de livraison choisis ne sont du type 'click-and-collect'
               si c'est le cas on intercepte l'action pour afficher une popup d'explication et de demande de confirmation #}

            $('.js-confirm-checkout').on('click', function (e) {
                e.preventDefault();

                var clickNCollectSelectedShippingCompanyNames = [];
                var $messageElement = $("#click-collect-warning-message");
                var $companyListElement = $("#company-list");

                {# Select to shipping mode #}
                $selectShipping.find('input[type=radio]').each(function (index, el) {
                    if ($(this).is(':checked')) {
                        var id = $(el).data('id').toString();
                        var company = $(el).closest('.basket-radio').data('company');
                        if (id === '{{ clickAndCollectShippingId }}') {
                            clickNCollectSelectedShippingCompanyNames.push(company);
                        }
                    }
                });

                {# Si 'click and collect' n'a été choisi pour aucun produit, le checkout se poursuit normalement #}
                if (clickNCollectSelectedShippingCompanyNames.length < 1) {
                    window.location = '{{ path('checkout_addresses') }}';
                    return;
                }

                {# Sélection de la traduction en fonction du nombre de fois que 'click and collect' a été choisi #}
                if (clickNCollectSelectedShippingCompanyNames.length === 1) {
                    $messageElement.html("{{ 'click_and_collect_popup.warning.single_shop'|trans|e('js') }}");
                } else {
                    $messageElement.html("{{ 'click_and_collect_popup.warning.multiple_shops'|trans|e('js') }}");
                }

                {# Injection de la liste des magasins concernés #}
                $companyListElement.html('');
                clickNCollectSelectedShippingCompanyNames.forEach(function (companyName) {
                    $companyListElement.append("<div>" + companyName + "</div>");
                });

                {# Affiche la modale explicative 'Click and Collect' #}
                $('#click-collect-modal').modal('show');

                {# Le choix de 'Click and Collect' est validé dans la modale #}
                $('#choose-click-collect').on('click', function() {
                    window.location = '{{ path('checkout_addresses') }}';
                });
            });

            {# DOM manipulation -------------------------------- #}

            {# generated coupon code form - moving #}
            var $couponButton = $('.discount-coupon button');
            $couponButton.addClass('btn-coupon');
            $couponButton.appendTo($('.discount-coupon .form-group'));

            {# put coupon code div before order validation div on small screens #}
            var moveCoupon = function() {
                var $couponCode = $('.coupon');

                if($('.device-lg').is(':hidden')) { // uses the <div> flag
                    $('.sidebar').prepend($couponCode);
                } else {
                    $($couponCode).insertBefore('.sidebar .reinsurance-block');
                }
            };

            {# check screen size on start up for moving coupon div... #}
            moveCoupon();

            {# ...and also on window rezising #}
            $(window).resize(moveCoupon);

        });
    </script>

    <script type="text/javascript">
        {# analytics -------------------------------- #}

        {# Tag commander variables #}
        analytics.pageCode = "basket";
    </script>
{% endblock %}
