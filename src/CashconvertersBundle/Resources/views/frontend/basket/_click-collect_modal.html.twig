<div id="click-collect-modal" class="click-collect click-collect-modal modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-body">
                <div>{{ 'click_and_collect_popup.text1'|trans|raw }}</div>
                <div class="text-purple-redish">{{ 'click_and_collect_popup.text2'|trans }}</div>
                <div class="text-purple-redish warning">{{ 'click_and_collect_popup.warning.title'|trans }}</div>

                {# le message est différent en fonction du nombre de livraisons 'Click and Collect' sélectionnées #}
                <div id="click-collect-warning-message"></div>

                {# le nom des magasins est injecté dynamiquement #}
                <div id="company-list"></div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn default-btn black-btn-outline dismiss" data-dismiss="modal">{{ 'click_and_collect_popup.cancel'|trans }}</button>
                <button id="choose-click-collect" type="button" class="btn default-btn purple-btn">{{ 'click_and_collect_popup.confirm'|trans }}</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
