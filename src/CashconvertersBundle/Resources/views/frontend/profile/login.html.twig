{% extends '@App/frontend/layout.html.twig' %}

{% block content %}
    <div class="login-page">
        <div class="container">
            <div class="row login-content">

                {# Breadcrumb #}
                <section class="breadcrumb col-sm-offset-1">
                    <div class="container">
                        <a href="{{ path('home') }}">{{ 'home'|trans }}</a> >
                        <b class="active">{{ 'account'|trans }}</b>
                    </div>
                </section>

                {# Register #}
                <div class="col-sm-4 col-sm-offset-1">
                    <form class="register test-register" action="{{ cscart_url('index.php?dispatch=profiles.update') }}" method="post">
                        <input type="hidden" name="return_url" value="{{ cscart_url('profiles.update') }}">

                        <h3>{{ 'register_title'|trans|raw }}</h3>
                        <p>{{ 'register_text'|trans }}</p>
                        <div class="form-group">
                            {{ form_row(register_form.user_data.email, {attr: { class: 'form-control secondary-form'}, label: 'email'}) }}
                        </div>
                        <div class="form-group">
                            {{ form_row(register_form.user_data.password1, {attr: { class: 'form-control secondary-form js-show-password'}, label: 'password'}) }}
                        </div>
                        {% if register_form.user_data.c2c_vendor_pseudo is defined %}
                            <div class="form-group">
                                {{ form_row(register_form.user_data.c2c_vendor_pseudo, {attr: { class: 'form-control secondary-form'}, label: 'w_c2c_company_name'}) }}
                            </div>
                        {% endif %}
                        {{ form_widget(register_form.return_url) }}
                        <div class="form-group">
                            <label class="checkbox">
                                {{ form_widget(register_form.terms_approved) }}
                                <span></span>
                                {% if register_form.user_data.c2c_vendor_pseudo is defined %}
                                    {{ 'register_company_accept_terms'|trans({'[usageUrl]': cscart_url('pages.view?page_id=12'), '[saleUrl]': cscart_url('pages.view?page_id=8')})|raw }}
                                {% else %}
                                    {{ 'register_accept_terms'|trans({'[url]': cscart_url('pages.view?page_id=12')})|raw }}
                                {% endif %}
                            </label>
                        </div>
                        <div class="form-group captcha-form">
                            {{ render_smarty('common/recaptcha.tpl', { 'recaptcha_id':'register' }) }}
                        </div>

                        <div class="form-group">
                            {{ form_widget(register_form.register, {attr: { class: 'btn profile-btn black' }}) }}
                        </div>
                    </form>
                </div>

                {# Login #}
                <div class="col-sm-4 col-sm-offset-2">
                    <form class="login test-login-form" action="{{ cscart_url('index.php?dispatch=auth.login') }}" method="post">
                        <input type="hidden" name="return_url" value="{{ login_form.vars.data.return_url }}">
                        <input type="hidden" name="redirect_url" value="{{ login_form.vars.data.redirect_url }}">
                        <input type="hidden" name="remember_me" value="1">

                        <h3>{{ 'login_title'|trans|raw }}</h3>

                        {# email #}
                        <div class="form-group">
                            <label>{{ 'email'|trans }} :</label>
                            <input type="email" name="user_login" required="required" class="form-control" />
                        </div>

                        {# password #}
                        <div class="form-group">
                            <label>{{ 'password'|trans }} :</label>
                            <input type="password" name="password" required="required" class="form-control" />
                        </div>

                        {# recover password #}
                        <div class="form-group">
                            <a href="{{ path('recover_password') }}">{{ 'forgot_password_question'|trans }}</a>
                        </div>

                        {# submit #}
                        <div class="form-group">
                            <button type="submit" name="dispatch[auth.login]" class="btn profile-btn">{{ 'sign_in'|trans|upper }}</button>
                        </div>
                    </form>
                </div>

            </div>
        </div>
    </div>
{% endblock %}
