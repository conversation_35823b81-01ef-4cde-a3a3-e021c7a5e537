{% extends '@App/frontend/layout.html.twig' %}

{% block realytics %}
    {% if not constant('DEVELOPMENT')|default(false) %}
        <script type="text/javascript">
            RY.track('access_beta');
        </script>
    {% endif %}
{% endblock %}

{% block content %}

    {# HOME #}

    {# BANNER #}
    <section id="banner" class="hidden-xs container">
        <div id="news-slideshow" class="container" style="display: none">
            {% for desktopBanner in desktopBanners %}
                {% if desktopBanner.hasLink() %}
                    <a href="{{ cscart_url(desktopBanner.link) }}" {% if desktopBanner.shouldOpenInNewWindow() %}target="_blank"{% endif %}>
                        <img data-lazy="{{ desktopBanner.image|image_url(990) }}">
                    </a>
                {% else %}
                    <div>
                        <img data-lazy="{{ desktopBanner.image|image_url(990) }}">
                    </div>
                {% endif %}
            {% endfor %}
        </div>
        <div id="news-slideshow-placeholder">
            {% if desktopBanners.0 is defined %}
                {% if desktopBanners.0.hasLink() %}
                    <a href="{{ cscart_url(desktopBanners.0.link) }}" {% if desktopBanners.0.shouldOpenInNewWindow() %}target="_blank"{% endif %}>
                        <img src="{{ desktopBanners.0.image|image_url(990) }}">
                    </a>
                {% else %}
                    <div>
                        <img src="{{ desktopBanners.0.image|image_url(990) }}">
                    </div>
                {% endif %}
            {% endif %}
        </div>
    </section>

    {# LATEST #}
    <section id="latest">
        <div class="container text-center">

            {# Title #}
            <div class="row">
                <div class="body-title col-lg-12">
                    <h1>{{ 'latest'|trans|upper }}</h1>
                    <hr class="under-title">
                </div>
            </div>

            {# Slideshow (note : reload page for responsive) #}
            <div class="col-xs-12 products-slideshow" style="display: none">
                {% for product in latestProducts %}
                    <div class="col-md-2 product-card-container text-center">
                        <a href="{{ product|product_url }}">
                            <div class="row">
                                {% if product.mainImage is defined and product.mainImage is not empty %}
                                    <div class="row"><img data-lazy="{{ product.mainImage|image_url(92, 92) }}" style="width: 92px; height: 92px;"></div>
                                {% else %}
                                    <div class="row"><img data-lazy="{{ asset('images/no-image.jpg') }}" style="width: 92px; height: 92px;"></div>
                                {% endif %}

                                <span class="prod-title truncated">{{ product.name }}</span><br>
                                <span class="price-before">{{ 'from'|trans }}</span><br><span class="price"> {{ product.minimumPrice|money }}</span>
                            </div>
                        </a>
                    </div>
                {% endfor %}
            </div>

            {# Button #}
            <div class="see_new_products">
                <a href="{{ path('search') }}" class="btn default-btn purple-btn">{{ 'see_new'|trans|upper }}</a>
            </div>

        </div>
    </section>

    {# CATEGORIES #}
    <section id="categories">
        <div class="container">

            {# Title #}
            <div class="row">
                <div class="body-title col-lg-12">
                    <h1 class="text-center text-uppercase hidden-xs">{{ 'most_sold'|trans }}</h1>
                    <h1 class="text-center text-uppercase visible-xs">{{ 'most_sold_mobile'|trans }}</h1>
                    <hr class="under-title">
                </div>
            </div>
            {% include '@Cashconverters/frontend/categories/categories-list.html.twig' with { categories: topCategories } only %}
            {# Show categories button#}
            <div id="show-cat-button">
                <a class="btn default-btn purple-btn" href="{{ path('categories') }}">{{ 'show_categories'|trans|upper }}</a>
            </div>
        </div>
    </section>

    {# CTAS #}
    <section id="CTAs">
        <div class="container">

            {# Title #}
            <div class="row">
                <h1 class="text-center hidden-xs">{{ 'about'|trans|upper }}</h1>
                <h1 class="text-center visible-xs">{{ 'about_mobile'|trans|upper }}</h1>
                <div class="body-title col-lg-12">
                    <hr class="under-title">
                </div>
            </div>

            {# Desktop + Tablet #}
            <div class="desktop-ctas hidden-xs">

                <div class="cta-container">
                    <div class="cta-1">
                        {# CTA text #1 #}
                        <div class="sub-cta text-cta dark-cta">
                            <h3>{{ 'home_bottom_text_1_title'|trans|upper }}</h3>
                            <div class="cta-text-container">
                        <span>

                        {{ 'home_bottom_text_1'|trans|raw }}
                        </span>
                            </div>
                            <a href="{{ 'cta_concept_link'|trans }}" class="cta-link">{{ '>>> '~'more'|trans }}</a>
                        </div>

                        {# CTA video #}
                        <div id="videoContainer" class="">
                            <iframe class="right-cta" width="560" height="315" src="{{ 'publish_sidebar_1_link'|trans }}" frameborder="0" allowfullscreen></iframe>
                        </div>
                    </div>
                    <div class="cta-2">
                        {# CTA image desktop #}
                        <div class="sub-cta img-cta left-cta hidden-sm">
                            <a href="{{ 'cta_franchise_link'|trans }}"><img src="{{ asset('images/CTA2.jpg') }}"></a>
                        </div>

                        {# CTA text #2 #}
                        <div class="sub-cta text-cta light-cta right-cta">
                            <h3>{{ 'home_bottom_text_2_title'|trans|upper }}</h3>
                            <div class="cta-text-container">
                                <span>
                                    {{ 'home_bottom_text_2'|trans|raw }}
                                </span>
                            </div>
                            <a href="{{ 'home_bottom_text_2_link'|trans }}" class="cta-link">{{ '>>> '~'ourshops'|trans }}</a>
                        </div>

                        {# CTA image tablet #}
                        <div class="sub-cta img-cta left-cta visible-sm">
                            <a href="{{ 'cta_franchise_link'|trans }}"><img id="cta-tab-and-above" src="{{ asset('images/CTA2.jpg') }}"></a>
                        </div>

                    </div>
                </div>
            </div>

            {# Mobile #}
            <div class="mobile-ctas visible-xs">

                {# CTA text mobile #}
                <div class="col-md-12 mobile-text-cta">
                    <div class="mobile-cta-text-container">
                        <span> {{ 'cta_mobile_text'|trans|raw }}</span>
                    </div>
                    <a href="{{ 'cta_concept_link'|trans }}#"><i>{{ '>>> '~'more'|trans }}</i></a>
                </div>

                {# CTA image mobile #}
                <div class="mobile-cta-img-container col-md-12">
                    <a href="{{ 'cta_franchise_link'|trans }}"><img class="cta-mobile" src="{{ asset('images/CTA2.jpg') }}"></a>
                </div>

            </div>
        </div>
    </section>
{% endblock %}
