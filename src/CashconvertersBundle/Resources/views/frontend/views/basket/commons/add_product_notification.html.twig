<div id="basket-popup" class="modal fade js-open-modal-on-load" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">

            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label=" {{ 'close'|trans }}"><span aria-hidden="true" class="icon-cross"></span></button>
                <p class="modal-title">{{ 'product_added_to_cart'|trans|upper }}</p>
            </div>

            <div class="modal-body">
                <div class="row">

                    {# image #}
                    <div class="col-tn-12 col-xs-2">
                        {% if product.declination.main_image %}
                            <div class="product-image"><img class="product-picture product-picture-mini" src="{{ product.declination.main_image|image_url(90) }}" alt=""></div>
                        {% else %}
                            <div class="product-image-no-image"><img src="{{ asset('images/no-image.jpg') }}"></div>
                        {% endif %}
                    </div>

                    {# name and options #}
                    <div class="col-tn-12 col-xs-6">
                        <div class="product-name">{{ product.declination.product_name|upper }}</div>
                        {% for option in product.declination.declination %}
                            <div class="product-options">{{ option.option_name }} : {{ option.value_name }}</div>
                        {% endfor %}
                    </div>

                    {# price #}
                    <div class="col-tn-12 col-xs-4">
                        <div class="product-price">{{ product.declination.price|price }}</div>
                    </div>
                </div>
            </div>

            {# buttons #}
            <div class="modal-footer">
                <div class="modal-buttons">
                    <button type="button" class="btn default-btn black-btn" data-dismiss="modal">{{ 'continue_shopping'|trans }}</button>
                    <a href="{{ path('basket_view') }}" class="btn default-btn purple-btn">{{ 'w_validate_command'|trans }}</a>
                </div>
            </div>
        </div>
    </div>
</div>
