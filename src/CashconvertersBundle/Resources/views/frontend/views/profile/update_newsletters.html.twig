{% extends '@App/frontend/views/profile/layout.html.twig' %}

{% set accountActivePage = 'my_account'|trans %}

{% block profile_content %}

    <div class="profile-page newsletter-update">

        {# top menu (tabs) #}
        <section class="tab-menu">
            <ul class="nav nav-tabs">
                <li><a href="{{ cscart_url('profiles.update') }}">{{ 'profile'|trans }}</a></li>
                <li><a href="{{ cscart_url('profiles.update.address') }}">{{ 'addresses'|trans }}</a></li>
                <li class="active"><a href="{{ cscart_url('profiles.update.newsletters') }}">{{ 'newsletter'|trans }}</a></li>
                <li><a href="{{ path('loyalty') }}">{{ 'loyalty'|trans }}</a></li>
            </ul>
        </section>

        {# page main content #}
        <section class="profile-content-block">
            <div>
                {{ render(controller('AppBundle:MailingList:form')) }}
            </div>
        </section>
    </div>
{% endblock %}
