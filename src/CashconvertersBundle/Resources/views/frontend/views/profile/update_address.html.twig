{% extends '@App/frontend/views/profile/layout.html.twig' %}

{% set accountActivePage = 'my_account'|trans %}

{% block profile_content %}
    <div class="profile-page addresses-update">

        {# top menu (tabs) #}
        <section class="tab-menu">
            <ul class="nav nav-tabs">
                <li><a href="{{ cscart_url('profiles.update') }}">{{ 'profile'|trans }}</a></li>
                <li class="active"><a href="{{ cscart_url('profiles.update.address') }}">{{ 'addresses'|trans }}</a></li>
                <li><a href="{{ cscart_url('profiles.update.newsletters') }}">{{ 'newsletter'|trans }}</a></li>
                <li><a href="{{ path('loyalty') }}">{{ 'loyalty'|trans }}</a></li>
            </ul>
        </section>

        {# page main content #}
        <section class="profile-content-block">

            {# update profile addresses form #}
            <form name="profile_form" action="{{ cscart_url('profiles.update.address') }}" method="post" class="clearfix">

                {# billing address #}
                <div class="billing-address">
                    <h2>{{ 'billing_address'|trans }}</h2>

                    {% include '@App/frontend/views/profile/address-fields.html.twig' with {'section': 'B'} %}

                    <label class="checkbox">
                        <input class="add-address" type="checkbox" id="differentShippingAddress" name="ship_to_another" value="1" {% if shipToAnother %} checked="checked" {% endif %}>
                        <span class="custom-checkbox"></span>
                        {{ 'different_shipping_address'|trans }}
                    </label>
                </div>

                {# shipping address #}
                <div class="shipping-address-block">
                    <h2>{{ 'shipping_address'|trans }}</h2>

                    {% include '@App/frontend/views/profile/address-fields.html.twig' with {'section': 'S', 'fakeRequired': true} %}

                </div>

                {# submit button #}
                <div class="form-group">
                    <input type="submit" class="btn profile-btn pink" value="{{ 'save_profile_update'|trans|upper }}">
                </div>
            </form>
        </section>
    </div>

    <script type="text/javascript">
        $(function () {
            var $differentShippingAddressCheckbox = $('#differentShippingAddress');
            var $shippingAddressBlock = $('.shipping-address-block');

            {# open shipping block on page load if 'shipToAnother' is true (meaning checkbox is actually checked) #}
            if($differentShippingAddressCheckbox.is(':checked')) {
                $shippingAddressBlock.toggle('fast');

                {# update required fields #}
                toggleRequiredFields();
            }

            {# toggle 'different shipping address' block on click #}
            $differentShippingAddressCheckbox.click(function() {
                $shippingAddressBlock.toggle('fast');

                {# update required fields #}
                toggleRequiredFields();
            });

            {# toggle shipping address required fields regarding shipToAnother boolean (meaning checkbox is actually checked) #}
            function toggleRequiredFields() {

                {# data-required is provided by 'render_smarty_legacy' #}
                var $requiredFields = $shippingAddressBlock.find('[data-required]');

                if(! $differentShippingAddressCheckbox.is(':checked')) {
                    $requiredFields.each(function() {
                        $(this).attr('required', false)
                    });
                } else {
                    $requiredFields.each(function() {
                        $(this).attr('required', true)
                    });
                }
            }

            {# manage required fields after page load #}
            toggleRequiredFields();
        });
    </script>
{% endblock %}
