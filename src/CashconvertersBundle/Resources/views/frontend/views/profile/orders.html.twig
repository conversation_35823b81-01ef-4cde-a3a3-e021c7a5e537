{% extends '@App/frontend/views/profile/layout.html.twig' %}

{% set accountActivePage = 'w_my_purchases'|trans %}

{% block profile_content %}
    <div class="profile-page orders">

        {# top menu (tabs) #}
        <section class="tab-menu">
            <ul class="nav nav-tabs">
                <li class="active"><a href="{{ cscart_url('orders.search') }}">{{ 'w_my_purchases'|trans }}</a></li>
                <li><a href="{{ cscart_url('rma.returns') }}">{{ 'w_see_rma'|trans }}</a></li>
                <li><a href="{{ cscart_url('rma.sav') }}">{{ 'after_sales'|trans|upper }}</a></li>
            </ul>
        </section>

        {# page main content #}
        <section class="profile-content-block">

            <div class="orders-in-progress">
                <h2>{{ 'my_orders_in_progress'|trans }}</h2>

                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>{{ 'order_ref'|trans }}</th>
                                <th class="hidden-xs">{{ 'date'|trans }}</th>
                                <th class="hidden-xs">{{ 'vendor'|trans }}</th>
                                <th>{{ 'total'|trans }}</th>
                                <th>{{ 'status'|trans }}</th>
                            </tr>
                        </thead>

                        <tbody>
                            {% for order in orders %}
                                {% if order.status != "C" %}
                                    <tr>
                                        <td><a href="{{ cscart_url('orders.details?order_id=' ~ order.order_id) }}">{{ order.order_id }}</a></td>
                                        <td class="hidden-xs">{{ order.timestamp|date("d/m/Y") }}</td>
                                        <td class="hidden-xs">{{ order.company_name }}</td>
                                        <td class="price">{{ order.total|price }}</td>
                                        <td>{{ render_smarty_legacy('common/status.tpl', { status: order.status, display: 'view' }) }}</td>
                                    </tr>
                                {% endif %}
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="orders-completed">
                <h2 class="title">{{ 'my_orders_completed'|trans }}</h2>

                <div class="table-responsive">
                    <table class="table">
                        <thead>
                        <tr>
                            <th>{{ 'order_ref'|trans }}</th>
                            <th class="hidden-xs">{{ 'date'|trans }}</th>
                            <th class="hidden-xs">{{ 'vendor'|trans }}</th>
                            <th>{{ 'total'|trans }}</th>
                            <th>{{ 'status'|trans }}</th>
                        </tr>
                        </thead>

                        <tbody>
                        {% for order in orders %}
                            {% if order.status == "C" %}
                                <tr>
                                    <td><a href="{{ cscart_url('orders.details?order_id=' ~ order.order_id) }}">{{ order.order_id }}</a></td>
                                    <td class="hidden-xs">{{ order.timestamp|date("d/m/Y") }}</td>
                                    <td class="hidden-xs">{{ order.company_name }}</td>
                                    <td class="price">{{ order.total|price }}</td>
                                    <td>{{ render_smarty_legacy('common/status.tpl', { status: order.status, display: 'view' }) }}</td>
                                </tr>
                            {% endif %}
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </section>
    </div>
{% endblock %}
