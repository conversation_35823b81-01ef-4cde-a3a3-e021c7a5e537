{% extends '@App/frontend/views/profile/layout.html.twig' %}

{% block profile_content %}

    <div class="profile-page">

        {# top menu (tabs) #}
        <section class="tab-menu">
            <ul class="nav nav-tabs">
                <li><a href="{{ cscart_url('profiles.update') }}">{{ 'profile'|trans }}</a></li>
                <li><a href="{{ cscart_url('profiles.update.address') }}">{{ 'addresses'|trans }}</a></li>
                <li><a href="{{ cscart_url('profiles.update.newsletters') }}">{{ 'newsletter'|trans }}</a></li>
                <li class="active"><a href="{{ path('loyalty') }}">{{ 'loyalty'|trans }}</a></li>
            </ul>
        </section>

        {# page main content #}
        <section class="profile-content-block">
            <p>{{ 'loyalty_text_block'|trans }} :</p>
            <form action="" method="POST">

                <div class="row form-group">
                    <div class="col-sm-3">
                        <label for="input_loyalty_number">{{ 'loyalty_input'|trans }} :</label>
                    </div>
                    <div class="col-sm-9">
                        <input class="form-control" type="text" name="loyalty_number" id="input_loyalty_number" value="{{ loyaltyIdentifier }}" />
                    </div>
                </div>

                <div class="row form-group">
                    <div class="col-sm-3">
                        <label for="input_loyalty_result">{{ 'loyalty_result_input'|trans }} :</label>
                    </div>
                    <div class="col-sm-9">
                        <input class="form-control" type="text" disabled="disabled" id="input_loyalty_result" value="{{ loyaltyPoints }}" />
                    </div>
                </div>

                <input type="submit" class="btn profile-btn pink" value="{{ 'validate'|trans }}">
            </form>
        </section>
    </div>
{% endblock %}
