{% set section = section|lower %}
{% set idSuffix = '_'~section~'_'~random(1000) %}

{% if fakeRequired|default(false) %}
    {% set required = 'data-required="true"' %}
{% else %}
    {% set required = 'required' %}
{% endif %}

<div class="form-group">
    <label class="required" for="address-title{{ idSuffix }}">{{ 'user_title'|trans }}</label>
    <div id="address-title{{ idSuffix }}">
        <label class="radio">
            <input class="radio valign" type="radio" name="user_data[{{ section~'_title' }}]" value="mr" {% if attribute(profile, section~'_title')|default('mr') == 'mr' %}checked="checked"{% endif %} required="required" {{ required|raw }}>
            {{ 'user_title_mr'|trans }}
            <span></span>
        </label>
        <label class="radio">
            <input class="radio valign" type="radio" name="user_data[{{ section~'_title' }}]" value="mrs" {% if attribute(profile, section~'_title') == 'mrs' %}checked="checked"{% endif %} required="required" {{ required|raw }}>
            {{ 'user_title_mrs'|trans }}
            <span></span>
        </label>
    </div>
</div>

<div class="form-group">
    <label class="control-label required" for="address-firstname{{ idSuffix }}">{{ 'first_name'|trans }}</label>
    <input id="address-firstname{{ idSuffix }}" x-autocompletetype="given-name" type="text" name="user_data[{{ section~'_firstname' }}]" value="{{ attribute(profile, section~'_firstname') }}" class="form-control secondary-form" {{ required|raw }}>
</div>
<div class="form-group">
    <label class="control-label required" for="address-lastname{{ idSuffix }}">{{ 'last_name'|trans }}</label>
    <input id="address-lastname{{ idSuffix }}" x-autocompletetype="surname" type="text" name="user_data[{{ section~'_lastname' }}]" value="{{ attribute(profile, section~'_lastname') }}" class="form-control secondary-form" {{ required|raw }}>
</div>

<div class="form-group">
    <label class="control-label" for="address-company{{ idSuffix }}">{{ 'company'|trans }}</label>
    <input id="address-company{{ idSuffix }}" type="text" name="user_data[{{ section~'_company' }}]" value="{{ attribute(profile, section~'_company') }}" class="form-control secondary-form">
</div>
<div class="form-group">
    <label class="control-label required" for="address-phone{{ idSuffix }}">{{ 'phone'|trans }}</label>
    <input id="address-phone{{ idSuffix }}" x-autocompletetype="phone-full" type="text" name="user_data[{{ section~'_phone' }}]" value="{{ attribute(profile, section~'_phone') }}" class="form-control secondary-form" {{ required|raw }}>
</div>

<div class="form-group">
    <label class="control-label required" for="address-address{{ idSuffix }}">{{ 'address'|trans }}</label>
    <input id="address-address{{ idSuffix }}" x-autocompletetype="street-address" type="text" name="user_data[{{ section~'_address' }}]" value="{{ attribute(profile, section~'_address') }}" class="form-control secondary-form" {{ required|raw }}>
</div>
<div class="form-group">
    <label class="control-label" for="address-address2{{ idSuffix }}">{{ 'address_2'|trans }}</label>
    <input id="address-address2{{ idSuffix }}" x-autocompletetype="address-line2" type="text" name="user_data[{{ section~'_address_2' }}]" value="{{ attribute(profile, section~'_address_2') }}" class="form-control secondary-form">
</div>

<div class="form-group">
    <label class="control-label required" for="address-zipcode{{ idSuffix }}">{{ 'zip_postal_code'|trans }}</label>
    <input id="address-zipcode{{ idSuffix }}" x-autocompletetype="postal-code" type="text" name="user_data[{{ section~'_zipcode' }}]" value="{{ attribute(profile, section~'_zipcode') }}" class="form-control secondary-form" pattern="\d{5}" title="{{ 'zip_postal_code_format'|trans }}" maxlength="5" {{ required|raw }}>
</div>
<div class="form-group">
    <label class="control-label required" for="address-city{{ idSuffix }}">{{ 'city'|trans }}</label>
    <input id="address-city{{ idSuffix }}" x-autocompletetype="city" type="text" name="user_data[{{ section~'_city' }}]" value="{{ attribute(profile, section~'_city') }}" class="form-control secondary-form" {{ required|raw }}>
</div>

<div class="form-group">
    <label for="address-country{{ idSuffix }}" class="required">{{ 'country'|trans }}</label>
    <select x-autocompletetype="country" id="address-country{{ idSuffix }}" class="form-control" name="user_data[{{ section~'_country' }}]" {{ required|raw }}>
        <option value="">- {{ 'select_country'|trans }} -</option>
        {% for countryCode, country in getCountries(true) %}
            <option value="{{ countryCode }}" {% if attribute(profile, section~'_country') == countryCode %}selected="selected"{% endif %}>{{ country }}</option>
        {% endfor %}
    </select>
</div>
