{% extends '@App/frontend/views/profile/returns.html.twig' %}

{% block profile_content %}
    <div class="profile-page order_detail">

        {# top menu (tabs) #}
        <section class="tab-menu">
            <ul class="nav nav-tabs">
                <li><a href="{{ cscart_url('orders.search') }}">{{ 'w_my_purchases'|trans }}</a></li>
                <li class="active"><a href="{{ cscart_url('rma.returns') }}">{{ 'w_see_rma'|trans }}</a></li>
                <li><a href="{{ cscart_url('rma.sav') }}">{{ 'after_sales'|trans|upper }}</a></li>
            </ul>
        </section>

        {# page main content #}
        <section class="profile-content-block">

            {# section title #}
            <h3>{{ 'w_title_popin_rma_detail'|trans({'[return_id]': return_info.return_id})|upper }}</h3>

            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th style="width: 30%">{{ 'product'|trans }}</th>
                            <th style="width: 15%">{{ 'options'|trans }}</th>
                            <th style="width: 8%">{{ 'amount'|trans }}</th>
                            <th style="width: 8%">{{ 'price'|trans }}</th>
                            <th style="width: 39%">{{ 'reason'|trans }}</th>
                        </tr>
                    </thead>

                    <tbody>
                        {% for products in return_info.items %}
                            {% for product in products %}
                                 <tr>
                                    <td>{{ product.product }}</td>
                                    <td>
                                        {% for option in product.product_options %}
                                            <span>{{ option.variant_name }}</span>
                                        {% endfor %}
                                    </td>
                                    <td>{{ product.amount }}</td>
                                    <td>{{ product.price|price }}</td>
                                    <td>{{ reasons[product.reason].property }}</td>
                                </tr>
                            {% endfor %}
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </section>

    </div> {# // profile-page #}
{% endblock %}
