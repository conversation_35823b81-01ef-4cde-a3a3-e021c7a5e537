{% extends '@App/frontend/views/profile/layout.html.twig' %}

{% block profile_content %}
    <div class="profile-page mails">

        {# top menu (tabs) #}
        <section class="tab-menu">
            <ul class="nav nav-tabs">
                <li class="active"><a href="{{ cscart_url('discuss.list') }}">{{ 'discuss_my_messages'|trans }}</a></li>
            </ul>
        </section>

        {# page main content #}
        <section class="profile-content-block">
            <p>{{ 'discuss_front_header'|trans }}</p>

            {% if discussions.count > 0 %}
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th class="hidden-xs">{{ 'vendor'|trans }}</th>
                                <th class="hidden-xs">{{ 'discuss_last_message'|trans }}</th>
                                <th>{{ 'title'|trans }}</th>
                                <th>&nbsp;</th>
                            </tr>
                        </thead>

                        <tbody>
                            {% for discussion in discussions %}
                                {% set last_message = (last_messages[discussion.id]) %}
                                {% set discussion_url = cscart_url("discuss.view?discussion_id=" ~ discussion.id ) %}
                                <tr>
                                    <td class="hidden-xs"><a href="{{ discussion_url }}">{{ (interlocutors[discussion.id]) }}</a></td>
                                    <td class="hidden-xs">
                                        <a href="{{ discussion_url }}" class="message-short">
                                            {{ last_message.sendDate|date('d/m/Y') }} -
                                            {{ last_message.content|raw|truncate('40', true, '...')}}
                                        </a>
                                    </td>
                                    <td><a href="{{ discussion_url }}">
                                            {{ discussion.metaData('title')|truncate('40', true, '...') }}
                                        </a>
                                    </td>
                                    {% set hide_discussion_url = cscart_url("discuss.hide?discussion_id=" ~ discussion.id ) %}
                                    <td class="delete">
                                        <a href="{{ hide_discussion_url }}">{{ 'delete'|trans }}</a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <p>{{ 'no_message'|trans }}</p>
            {% endif %}
        </section>
    </div>
{% endblock %}
