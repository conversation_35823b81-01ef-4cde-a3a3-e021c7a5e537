{% extends '@App/frontend/views/profile/layout.html.twig' %}

{% block profile_content %}
    <div class="profile-page mails">

        {# top menu (tabs) #}
        <section class="tab-menu">
            <ul class="nav nav-tabs">
                <li class="active"><a href="{{ cscart_url('discuss.list') }}">{{ 'discuss_my_messages'|trans }}</a></li>
            </ul>
        </section>

        {# page main content #}
        <section class="profile-content-block">
            <p class="close-detail"><a href="{{ cscart_url('discuss.list') }}"><span class="glyphicon glyphicon-menu-left"></span>&nbsp;{{ 'message_detail_back_link'|trans }}</a></p>

            {# message title #}
            <div class="discuss-title">
                <p>{{ 'title'|trans }} : {% if product_url %}<a href="{{ product_url }}">{% endif %}{{ discussion.metaData('title') }}{% if product_url %}</a>{% endif %}</p>
            </div>

            <div class="box-content" id="discuss-container">
                {% for message in messages %}
                    {% set class = "message-author" %}
                    {% set date_message = "discuss_send_date" %}

                    {% if currentUser.id != message.author %}
                        {% set class = "message-recipient" %}
                        {% set date_message = "discuss_reception_date" %}
                    {% endif %}

                    <div class="message-container {{ class }}">
                        {% set message_date = message.sendDate|date('d/m/Y') %}
                        {% set message_time = message.sendDate|date('H:i') %}
                        <small>{{ date_message|trans({ '[date]': message_date, '[time]': message_time }) }}</small>
                        <div class="message">{{ message.content|raw }}</div>
                    </div>
                    <hr/>
                {% endfor %}
            </div>

            <div class="discuss-create-message">
                <form name="profile_form" action="{{ path('home') }}" method="post">
                    <input type="hidden" name="discussion_id" value="{{ discussion.id|default(0) }}">
                    <input type="hidden" name="product_id" value="{{ product_id|default(0) }}">
                    <input type="hidden" name="company_id" value="{{ company_id|default(0) }}">
                    <textarea class="form-control" placeholder="{{ 'discuss_your_message'|trans }}" name="content" rows="5" cols="80"></textarea><br>
                    <button class="btn profile-btn pink" name="dispatch[discuss.update]" id="add_message_but">{{ 'send'|trans }}</button>
                </form>
            </div>
        </section>
    </div>

    <script>
        $(function(){
            $('html, body').animate({
                scrollTop: $("#discuss-container")[0].scrollHeight
            }, 600);
        });
    </script>
{% endblock %}
