{% extends '@App/frontend/views/profile/layout.html.twig' %}

{% block profile_content %}
    <div class="profile-page returns">

        {# top menu (tabs) #}
        <section class="tab-menu">
            <ul class="nav nav-tabs">
                <li><a href="{{ cscart_url('orders.search') }}">{{ 'w_my_purchases'|trans }}</a></li>
                <li class="active"><a href="{{ cscart_url('rma.returns') }}">{{ 'w_see_rma'|trans }}</a></li>
                <li><a href="{{ cscart_url('rma.sav') }}">{{ 'after_sales'|trans|upper }}</a></li>
            </ul>
        </section>

        {# page main content #}
        <section class="profile-content-block">

            {# completed orders #}
            {% for orderType, orders in w_orders %}
                <h2 class="title">{{ orderType }}</h2>
                    {% if orders is not empty %}
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                <tr>
                                    <th>{{ 'order_ref'|trans }}</th>
                                    <th class="hidden-tn hidden-xs">{{ 'date'|trans }}</th>
                                    <th class="hidden-tn hidden-xs">{{ 'vendor'|trans }}</th>
                                    <th>{{ 'total'|trans }}</th>
                                    <th>{{ 'status'|trans }}</th>
                                    <th>&nbsp;</th>
                                </tr>
                                </thead>

                                <tbody>
                                {% for order in orders %}
                                    <tr>
                                        <td>
                                            <a href="{{ cscart_url('orders.details?order_id=' ~ order.order_id) }}">
                                                {{ order.order_id }}
                                            </a>
                                        </td>
                                        <td class="hidden-tn hidden-xs">{{ order.timestamp|date("d/m/Y") }}</td>
                                        <td class="hidden-tn hidden-xs">{{ order.company_name }}</td>
                                        <td class="price">{{ order.total|price }}</td>
                                        <td>{{ render_smarty_legacy('common/status.tpl', { status: order.status, display: 'view' }) }}</td>
                                        <td>
                                            <a  href="{{ cscart_url('rma.create_return?order_id=' ~ order.order_id) }}"
                                                data-remote="{{ cscart_url('rma.create_return?order_id=' ~ order.order_id) }} .mainbox-body"
                                                data-toggle="modal"
                                                data-target="#generic_modal">

                                                {{ 'return_registration'|trans }}
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                                </tbody>
                            </table>
                        </div>

                    {% else %}
                        <div>{{ 'text_no_orders'|trans }}</div>
                    {% endif %}
            {% endfor %}

            {# rma block #}
            <h2 class="title">{{ 'w_rma_history'|trans }}</h2>
            {% if return_requests is not empty %}
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                        <tr>
                            <th>{{ 'order_ref'|trans }}</th>
                            <th class="hidden-tn hidden-xs">{{ 'date'|trans }}</th>
                            <th class="hidden-tn hidden-xs">{{ 'vendor'|trans }}</th>
                            <th>{{ 'total'|trans }}</th>
                            <th>{{ 'status'|trans }}</th>
                        </tr>
                        </thead>

                        <tbody>
                        {% for rma in return_requests %}
                            <tr>
                                <td>
                                    <a href="{{ cscart_url('rma.details?return_id=' ~ rma.return_id) }}">
                                        {{ rma.return_id }}
                                    </a>
                                </td>
                                <td class="hidden-tn hidden-xs">{{ rma.timestamp|date("d/m/Y") }}</td>
                                <td class="hidden-tn hidden-xs">{{ rma.company }}</td>
                                <td class="price">{{ rma.refund_sum|price }}</td>
                                <td>{{ render_smarty_legacy('common/status.tpl', { status: rma.status, status_type: 'R', display: 'view' }) }}</td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>

            {% else %}
                <div>{{ 'text_no_orders'|trans }}</div>
            {% endif %}
        </section>
    </div>
{% endblock %}
