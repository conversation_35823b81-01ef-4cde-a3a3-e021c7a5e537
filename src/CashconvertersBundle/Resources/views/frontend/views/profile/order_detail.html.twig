{% extends '@App/frontend/views/profile/orders.html.twig' %}

{% block profile_content %}
    <div class="profile-page order_detail">

        {# top menu (tabs) #}
        <section class="tab-menu">
            <ul class="nav nav-tabs">
                <li class="active"><a href="{{ cscart_url('orders.search') }}">{{ 'w_my_purchases'|trans }}</a></li>
                <li><a href="{{ cscart_url('rma.returns') }}">{{ 'w_see_rma'|trans }}</a></li>
                <li><a href="{{ cscart_url('rma.sav') }}">{{ 'after_sales'|trans|upper }}</a></li>
            </ul>
        </section>

        {# page main content #}
        <section class="profile-content-block">

            {# section title #}
            <h3>{{ 'order_details'|trans|upper }} n°{{ order.order_id }}</h3>

            {# order detail block #}
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th class="hidden-xs">{{ 'order_ref'|trans }}</th>
                            <th class="hidden-xs">{{ 'date'|trans }}</th>
                            <th>{{ 'total'|trans }}</th>
                            <th>{{ 'status'|trans }}</th>
                            <th>{{ 'invoice'|trans }}</th>
                        </tr>
                    </thead>

                    <tbody>
                        <tr>
                            <td class="hidden-xs">{{ order.order_id }}</td>
                            <td class="hidden-xs">{{ order.timestamp|date("d/m/Y") }}</td>
                            <td class="price">{{ order.total|price }}</td>
                            <td>{{ render_smarty_legacy('common/status.tpl', { status: order.status, display: 'view' }) }}</td>
                            <td><a href="{{ cscart_url('orders.print_invoice?format=pdf&order_id=' ~ order.order_id) }}">{{ 'pdf'|trans }}</a></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            {# products block #}
            <div class="product-list">

                <div class="table-responsive">
                    <table class="table">
                        <thead>
                        <tr>
                            <th class="hidden-tn hidden-xs hidden-sm">{{ 'product'|trans }}</th>
                            <th>{{ 'product_name'|trans }}</th>
                            <th class="hidden-tn hidden-xs hidden-sm">{{ 'merchant'|trans }}</th>
                            <th>{{ 'quantity'|trans }}</th>
                            <th>{{ 'price'|trans }}</th>
                        </tr>
                        </thead>

                        <tbody>
                            {% for productGroup in order.product_groups %}
                                {% for key, product in productGroup.products if cart.products[key].extra.parent is not defined %}
                                    <tr>
                                        <td class="hidden-tn hidden-xs hidden-sm">
                                            {% if product.main_pair.detailed_id is defined %}
                                                <img src="{{ image_url_by_id(product.main_pair.detailed_id|default(0), 100) }}" alt="">
                                            {% else %}
                                                <img src="{{ asset('images/no-image.jpg') }}">
                                            {% endif %}
                                        </td>
                                        <td>{{ product.product }}</td>
                                        <td class="hidden-tn hidden-xs hidden-sm">{{ productGroup.name }}</td>
                                        <td>{{ product.amount }}</td>
                                        <td>{{ product.price|price }}</td>
                                    </tr>
                                {% endfor %}
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div> {# //produc-list #}
        </section>

        {# Addresses #}
        <div class="addresses">
            <div class="address col-tn-6">
                <h4 class="title">{{ 'shipping_address_title'|trans }}</h4>

                <div class="title-underline"></div>

                <ul class="list-unstyled">
                    <li>{{ order.s_firstname }} {{ order.s_lastname }}</li>
                    {% if order.s_company %}
                        <li>{{ order.s_company }}</li>
                    {% endif %}
                    <li>{{ order.s_address }}</li>
                    {% if order.s_address_2 is not empty %}
                        <li>{{ order.s_address_2 }}</li>
                    {% endif %}
                    <li>{{ order.s_zipcode }} {{ order.s_city }}</li>
                    <li>{{ order.s_country_descr }}</li>
                    {% if order.s_phone is not empty %}
                        <li>{{ order.s_phone }}</li>
                    {% endif %}
                </ul>
            </div>

            <div class="address col-tn-6">
                <h4 class="title">{{ 'billing_address_title'|trans }}</h4>

                <div class="title-underline"></div>

                <ul class="list-unstyled">
                    <li>{{ order.b_firstname }} {{ order.b_lastname }}</li>
                    <li>{{ order.b_address }}</li>
                    {% if order.b_address_2 is not empty %}
                        <li>{{ order.b_address_2 }}</li>
                    {% endif %}
                    <li>{{ order.b_zipcode }} {{ order.b_city }}</li>
                    <li>{{ order.b_country_descr }}</li>
                    {% if order.b_phone is not empty %}
                        <li>{{ order.b_phone }}</li>
                    {% endif %}
                </ul>
            </div>
        </div>

    </div> {# // profile-page #}
{% endblock %}
