{% extends '@App/frontend/views/profile/layout.html.twig' %}

{% block profile_content %}
    <div class="profile-page profile-update">

        {# top menu (tabs) #}
        <section class="tab-menu">
            <ul class="nav nav-tabs">
                <li class="active"><a href="{{ cscart_url('profiles.update') }}">{{ 'profile'|trans }}</a></li>
                <li><a href="{{ cscart_url('profiles.update.address') }}">{{ 'addresses'|trans }}</a></li>
                <li><a href="{{ cscart_url('profiles.update.newsletters') }}">{{ 'newsletter'|trans }}</a></li>
                <li><a href="{{ path('loyalty') }}">{{ 'loyalty'|trans }}</a></li>
            </ul>
        </section>

        {# page main content #}
        <section class="profile-content-block">

            {# update profile form #}
            <form name="profile_form" action="{{ cscart_url('profiles.update') }}" method="post">

                {# hidden inputs #}
                <input id="selected_section" type="hidden" value="general" name="selected_section"/>
                <input id="default_card_id" type="hidden" value="" name="default_cc"/>
                <input type="hidden" name="profile_id" value="{{ profile.profile_id }}"/>

                <div class="form-group">
                    <label for="user-title">{{ 'user_title'|trans }}</label>
                    <div id="user-title" class="col-sm-offset-3">
                        <label class="radio">
                            <input class="radio valign" type="radio" name="user_data[title]" value="mr" {% if profile.title|default('mr') == 'mr' %}checked="checked"{% endif %} required="required">
                            {{ 'user_title_mr'|trans }}
                            <span></span>
                        </label>
                        <label class="radio">
                            <input class="radio valign" type="radio" name="user_data[title]" value="mrs" {% if profile.title == 'mrs' %}checked="checked"{% endif %} required="required">
                            {{ 'user_title_mrs'|trans }}
                            <span></span>
                        </label>
                    </div>
                </div>

                {# user firstname #}
                <div class="form-group">
                    <div class="row">
                        {# label #}
                        <div class="col-tn-12 col-sm-3">
                            <label for="user-firstname" class="required">{{ 'first_name'|trans }}</label>
                        </div>

                        {# input #}
                        <div class="col-tn-12 col-sm-9">
                            <input id="user-firstname" class="form-control input-text-medium" type="text" name="user_data[firstname]" value="{{ profile.firstname }}" required>
                        </div>

                    </div>
                </div>

                {# user name #}
                <div class="form-group">
                    <div class="row">
                        {# label #}
                        <div class="col-tn-12 col-sm-3">
                            <label for="user-lastname" class="required">{{ 'last_name'|trans }}</label>
                        </div>

                        {# input #}
                        <div class="col-tn-12 col-sm-9">
                            <input id="user-lastname" class="form-control input-text-medium" type="text" name="user_data[lastname]" value="{{ profile.lastname }}" required>
                        </div>
                    </div>
                </div>

                {# user birthday #}
                <div class="form-group">
                    <div class="row">
                        {# label #}
                        <div class="col-tn-12 col-sm-3">
                            <label for="user-birthday" class="required">{{ 'date_of_birth'|trans }}</label>
                        </div>

                        {# input #}
                        <div class="col-tn-12 col-sm-9">
                            <div class="input-group">
                                <input id="user-birthday" class="form-control input-text-medium cm-calendar" type="text" name="user_data[birthday]" value="{{ currentUser.birthday|date('d/m/Y') }}" required>
                                <span class="input-group-addon glyphicon glyphicon-calendar"></span>
                            </div>
                        </div>
                    </div>
                </div>

                {# user email #}
                <div class="form-group">
                    <div class="row">
                        {# label #}
                        <div class="col-tn-12 col-sm-3">
                            <label for="user-email" class="required">{{ 'email'|trans }}</label>
                        </div>

                        {# input #}
                        <div class="col-tn-12 col-sm-9">
                            <input id="user-email" class="form-control input-text-medium" type="email" name="user_data[email]"
                                   value="{{ profile.email }}"
                                   pattern="^([^\x00-\x20\x22\x28\x29\x2c\x2e\x3a-\x3c\x3e\x40\x5b-\x5d\x7f-\xff]+|\x22([^\x0d\x22\x5c\x80-\xff]|\x5c[\x00-\x7f])*\x22)(\x2e([^\x00-\x20\x22\x28\x29\x2c\x2e\x3a-\x3c\x3e\x40\x5b-\x5d\x7f-\xff]+|\x22([^\x0d\x22\x5c\x80-\xff]|\x5c[\x00-\x7f])*\x22))*\x40([^\x00-\x20\x22\x28\x29\x2c\x2e\x3a-\x3c\x3e\x40\x5b-\x5d\x7f-\xff]+|\x5b([^\x0d\x5b-\x5d\x80-\xff]|\x5c[\x00-\x7f])*\x5d)(\x2e([^\x00-\x20\x22\x28\x29\x2c\x2e\x3a-\x3c\x3e\x40\x5b-\x5d\x7f-\xff]+|\x5b([^\x0d\x5b-\x5d\x80-\xff]|\x5c[\x00-\x7f])*\x5d))*(\.\w{2,})+$"
                                   required>
                        </div>
                    </div>
                </div>

                {# trigger password change #}
                <div class="row">
                    <div class="col-tn-12 col-sm-9 col-sm-offset-3">
                        <label class="checkbox">
                            <input class="modify-password-trigger normal" type="checkbox"><span></span>
                            <span>{{ 'modify_password'|trans }}</span>
                        </label>
                    </div>
                </div>


                <div id="password-change" style="display: none">
                    {% if auth.w_recover_password is not defined %}
                        <div class="form-group">
                            <div class="row">
                                <div class="col-tn-12 col-sm-3">
                                    <label for="password-old" class="required">{{ 'w_old_password'|trans }}</label>
                                </div>

                                <div class="col-tn-12 col-sm-9">
                                    <input id="password-old" class="form-control input-text-medium" type="password" name="user_data[old_password]" disabled="disabled">
                                </div>
                            </div>
                        </div>
                    {% endif %}

                    <div class="form-group">
                        <div class="row">
                            <div class="col-tn-12 col-sm-3">
                                <label for="password-new" class="required">{{ 'w_new_password'|trans }}</label>
                            </div>

                            <div class="col-tn-12 col-sm-9">
                                <input class="form-control input-text-medium" minlength="5" type="password" name="user_data[password1]" disabled="disabled">
                            </div>
                        </div>
                    </div>
                </div>

                {# submit #}
                <div class="row">
                    <div class="col-tn-12 col-sm-9 col-sm-offset-3">
                        <div class="form-group">
                            <input type="submit" class="btn profile-btn pink" value="{{ 'w_save_modifications'|trans|upper }}">
                        </div>
                    </div>
                </div>
            </form>
        </section>
    </div>

    <script type="text/javascript">
        $(function () {

            {# enable / disable password inputs #}
            $(".modify-password-trigger").on("change", function() {
                $('#password-change').find('input').prop('disabled', function(i, v) { return !v; });
                $('#password-change').toggle("slow");
            });

            {# datepicker config #}
            $('#user-birthday').datepicker({
                inline: true,
                dateFormat: "dd/mm/yy",
                showOtherMonths: true,
                changeMonth: true,
                changeYear: true, minDate: '-100Y', maxDate: '-10Y',
                yearRange: '-100:+10',
                monthNames: ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'],
                monthNamesShort: ['Jan.', 'Fév.', 'Mars', 'Avr.', 'Mai', 'Juin', 'Juil.', 'Août', 'Sept.', 'Oct.', 'Nov.', 'Déc.'],
                dayNames: ['Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'],
                dayNamesMin: ['Di', 'Lu', 'Ma', 'Me', 'Je', 'Ve', 'Sa'],
                firstDay: 1,
                nextText: '{{ 'next'|trans }}',
                prevText: '{{ 'previous'|trans }}'
            });

            {# datepicker also triggered on icon click #}
            $('.glyphicon-calendar').click(function() {
                $('#user-birthday').focus();
            });
        });
    </script>
{% endblock %}
