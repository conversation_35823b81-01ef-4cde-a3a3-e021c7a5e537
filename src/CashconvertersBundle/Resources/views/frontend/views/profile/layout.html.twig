{% extends '@App/frontend/layout.html.twig' %}

{% block content %}

    <div class="user-account">

        {# Breadcrumb #}
        <section class="breadcrumb">
            <div class="container">
                <a href="{{ path('home') }}">{{ 'home'|trans }}</a> >
                <b class="active">{{ 'my_account'|trans }}</b>
            </div>
        </section>

        <div class="container">
            <div class="row">

                {# aside menu #}
                <aside class="col-tn-12 col-sm-3">
                    <nav role="navigation" class="side-menu">

                        {# my account #}
                        <div class="menu-item">
                            {# menu title #}
                            <p class="menu-title">{{ 'my_account'|trans }}<span class="glyphicon glyphicon-menu-right hidden-sm hidden-md hidden-lg pull-right"></span></p>

                            {# menu links #}
                            <ul class="list-unstyled menu-links">
                                <li class="menu-link"><a href="{{ cscart_url("profiles.update") }}">{{ 'profile'|trans }}</a></li>
                                <li class="menu-link"><a href="{{ cscart_url("profiles.update.address") }}">{{ 'addresses'|trans }}</a></li>
                                <li class="menu-link"><a href="{{ cscart_url('profiles.update.newsletters') }}">{{ 'newsletter'|trans }}</a></li>
                                <li class="menu-link"><a href="{{ path('loyalty') }}">{{ 'loyalty'|trans }}</a></li>
                            </ul>
                        </div>

                        {# my orders #}
                        <div class="menu-item">
                            {# menu title #}
                            <p class="menu-title">{{ 'w_my_purchases'|trans }}<span class="glyphicon glyphicon-menu-right hidden-sm hidden-md hidden-lg pull-right"></span></p>

                            {# menu links #}
                            <ul class="list-unstyled menu-links">
                                <li class="menu-link"><a href="{{ cscart_url("orders.search") }}">{{ 'orders'|trans }}</a></li>
                                <li class="menu-link"><a href="{{ cscart_url('rma.returns') }}">{{ 'returns'|trans }}</a></li>
                                <li class="menu-link"><a href="{{ cscart_url('rma.sav') }}">{{ 'after_sales'|trans }}</a></li>
                            </ul>
                        </div>

                        {# messages #}
                        <div class="menu-item">
                            {# menu title #}
                            <p class="menu-title">{{ 'discuss_menu'|trans}}<span class="glyphicon glyphicon-menu-right hidden-sm hidden-md hidden-lg pull-right"></span></p>

                            {# menu links #}
                            <ul class="list-unstyled menu-links">
                                <li class="menu-link"><a href="{{ cscart_url('discuss.list') }}">{{ 'discuss_my_messages'|trans }}</a></li>
                            </ul>
                        </div>

                        {# logout #}
                        <div class="logout-link">
                            <p class="menu-title">
                                <a href="{{ cscart_url('auth.logout') }}">
                                    {{ 'logout'|trans }}
                                    <span class="pull-right">&times;</span>
                                </a>
                            </p>
                        </div>
                    </nav>
                </aside>

                {# profile content #}
                <div class="col-tn-12 col-sm-9">
                    {% block profile_content %}{% endblock %}
                </div>
            </div>
        </div>
    </div>

    <script>
        $(function() {

            // menu accordion behaviour on small screens
            $('.menu-title').on("click", function() {

                // clean up
                var $menuItems = $('.menu-title');
                $menuItems.removeClass('open');
                $menuItems.find('.glyphicon').removeClass('glyphicon-menu-down');
                $menuItems.find('.glyphicon').addClass('glyphicon-menu-right');

                // open menu
                $(this).addClass('open');
                $(this).find('.glyphicon').addClass('glyphicon-menu-down');
            });
        });
    </script>
{% endblock %}
