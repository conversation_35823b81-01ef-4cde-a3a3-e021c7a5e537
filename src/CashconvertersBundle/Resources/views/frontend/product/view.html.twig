{% extends '@App/frontend/layout-search.html.twig' %}

{% block meta_title %}{{ productReadModel.seo_data.title|default(productReadModel.name) }}{% endblock %}

{% block meta %}
    {{ parent() }}

    <link rel="canonical" href="{{ productReadModel|product_url(productReadModel.id) }}"
          xmlns="http://www.w3.org/1999/html"/>

    {% for image in productReadModel.images %}
        <meta property="og:image" content="{{ render(controller('AppBundle:Image:retrieveUrl', { image_id: image.id })) }}" />
    {% endfor %}
    <meta property="og:title" content="{{ productReadModel.name }}" />

    <meta property="og:url" content="{{ productReadModel|product_url(productReadModel.id) }}" />

    {% if productReadModel.mainImage %}
        <meta name="twitter:image" content="{{ productReadModel.mainImage|image_url }}" />
    {% endif %}

    {% if productReadModel.status == constant('Wizacha\\Status::HIDDEN') %}
        <meta name="robots" content="noindex">
    {% endif %}
{% endblock %}

{% block content %}

    {# SEO #}
    <section class="product-description-header">
        <div class="product-top" itemscope itemtype="http://schema.org/Product">

            {# Metadata #}
            <meta itemprop="name" content="{{ productReadModel.name|striptags }}" />
            <meta itemprop="description" content="{{ productReadModel.shortDescription|striptags }}" />

            {% if productReadModel.mainImage %}
                <meta itemprop="image" content="{{ productReadModel.mainImage|image_url }}" />
            {% endif %}

            <div itemprop="offers" itemscope itemtype="http://schema.org/Offer">
                <meta itemprop="price" content="{{ productReadModel.currentPrice.convertedAmount }}" />
                <meta itemprop="pricecurrency" content="{{ currencyCode }}" />
            </div>
        </div>
    </section>

    {# Breadcrumb #}
    <section class="breadcrumb hidden-xs">
        <div class="container">

            {# 'go back home' link #}
            <a href="{{ path('home') }}">{{ 'home'|trans }}</a> >

            {# category page link #}
            <a href="{{ path('categories') }}">{{ 'categories'|trans }}</a> >

            {% for category in productReadModel.categoryPath %}
                <a href="{{ cscart_url("categories.view?category_id=" ~ category.id) }}">{{ category.name }}</a> >
            {% endfor %}

            <b>{{ productReadModel.name }}</b>
        </div>
    </section>

    {# Main content #}
    {% include '@App/frontend/product/product-description.html.twig' %}
    {# Declinations #}
    {% include '@App/frontend/product/declinations-list.html.twig' %}

    {# Recommended products #}
    {# % include '@App/frontend/product/similar-products.html.twig' % #}

    <script>
        $(function() {

            // Vue filters
            // ===========

            Vue.filter('round', function(value) {
                return Math.round(value);
            });

            Vue.filter('slice', function (value, begin, end) {
                return value.slice(begin, end)
            });

            Vue.filter('price', function (value) {
                return W.formatPrice(value);
            });

            // declinations behaviour
            // ======================

            new Vue({
                el: '#declinations',
                data: {

                    // all of the MVP declinations
                    declinations: [],

                    companies: [],

                    // defaults values for pagination
                    pagination: {
                        page: 1,
                        resultsPerPage: 1
                    },

                    mobilePageNumbersConstraint: 5,

                    // price sort declination direction
                    priceDirection: 1,

                    // filter declinations by selecting one merchant
                    selectedVendorId: null,

                    // zoom elevate lib config
                    zoomConfig: {
                        zoomWindowWidth: 500,
                        zoomWindowHeight: 500
                    }
                },
                created() {
                    this.initSelectedVendorId();
                },

                computed: {

                    // declinations are always filtered by vendor: either all vendors or a specific one
                    declinationsByVendor: function() {

                        this.pagination.page = 1;

                        var self = this;

                        if (this.companiesIdSelected.length === 0) {
                            return this.declinations;
                        } else {
                            return this.declinations.filter(function(declination) {
                                return self.companiesIdSelected.includes(declination.company_id);
                            });
                        }

                    },

                    // pagination - offset
                    // calculate how many declinations to skip regarding current page
                    offset: function() {
                        return ((this.pagination.page - 1) * this.pagination.resultsPerPage);
                    },

                    // pagination - total number of pages regarding the number of declinations and the number of results per page requested
                    // the number of declinations takes applied filters into account
                    totalPages: function() {
                        var resultsPerPage = this.pagination.resultsPerPage ? this.pagination.resultsPerPage : 20;

                        return Math.ceil(this.declinationsByVendor.length / resultsPerPage);
                    }
                },

                methods: {

                    // refresh view
                    refresh: function () {
                        this.carousels();

                        var self = this;
                        Vue.nextTick(function() {
                            self.setDeclinationsImageZoom();
                        });
                    },

                    // order declinations by price
                    sortByPrice: function() {
                        var self = this;

                        // custom sort by price
                        this.declinationsByVendor.sort(function(a, b) {
                            return (a['price'] - b['price']) * self.priceDirection;
                        });
                    },

                    resetToFirstPage: function() {
                        this.cleanCompanySelected();

                        if(this.selectedVendorId) {
                            this.addCompanySelectedById(this.selectedVendorId);
                        }
                        this.refresh();
                    },

                    initSelectedVendorId: function() {
                        if (typeof this.companiesIdSelected !== 'undefined' && this.companiesIdSelected.length === 1) {
                            this.selectedVendorId = this.companiesIdSelected[0];
                        }
                    },

                    // get declination image from API
                    imageUrl: function (declinationImageId, size) {
                        return Routing.generate('api_image_get', {
                            id: declinationImageId,
                            w: size,
                            h: size
                        });
                    },

                    // used for rendering rating stars
                    range: function (a, b) {
                        var rangeArray = [];
                        for(var i = a; i < b; i++) {
                            rangeArray.push(i);
                        }
                        return rangeArray;
                    },

                    // get back to the top of declinations list
                    scrollToTop: function() {
                        $('html, body').animate({
                            scrollTop: $('#offers').offset().top - 60 // 60 corresponds to searchbar height
                        }, 450);
                    },

                    // pagination
                    goToPage: function (page) {
                        page = Math.max(1, page); // cannot go below page 1
                        page = Math.min(this.totalPages, page); // cannot go over total number of pages

                        this.pagination.page = page;
                        this.scrollToTop();
                        this.refresh();
                    },

                    togglePageList: function(e) {
                        var $target = $(e.currentTarget);
                        $target.toggleClass('is-open');
                    },

                    getDeclinationCondition: function(declination) {
                        return (declination.condition === "N") ? '{{ 'w_product_condition_front_N'|trans|e('js') }}' : '{{ 'w_product_condition_front_U'|trans|e('js') }}';
                    },

                    getDeclinationDeliveryTime: function(declination) {
                        return declination.shippings[Object.keys(declination.shippings)[0] ].delivery_time;
                    },

                    // set up slick carousel
                    carousels: function() {

                        // Slick behaviour
                        Vue.nextTick(function() {

                            var $productImage = $('.product-main-image');
                            var $productThumbnails = $("#product-thumbnails");
                            var $featuredImages = $(".featured-image");
                            var $declinationThumbnails = $(".declination-thumbnails");

                            // main product
                            // ============

                            // product featured image
                            $productImage.not('.slick-initialized').slick({
                                slidesToShow: 1,
                                slidesToScroll: 1,
                                asNavFor: $productThumbnails,
                                arrows: false,
                                fade: true
                            });

                            // product thumbnails
                            $productThumbnails.not('.slick-initialized').slick({
                                slidesToShow: 3,
                                slidesToScroll: 1,
                                asNavFor: $productImage,
                                arrows: false,
                                dots: false,
                                centerMode: true,
                                focusOnSelect: true
                            });

                            // declinations
                            // ============

                            // declination featured image
                            $featuredImages.each(function() {
                                $(this).not('.slick-initialized').slick({
                                    slidesToShow: 1,
                                    slidesToScroll: 1,
                                    arrows: false,
                                    fade: true,
                                    asNavFor: $( $(this).data("nav") )
                                });
                            });

                            // declination thumbnails
                            $declinationThumbnails.each(function() {
                                $(this).not('.slick-initialized').slick({
                                    slidesToShow: 3,
                                    slidesToScroll: 3,
                                    asNavFor: $( $(this).data("featured") ),
                                    centerMode: true,
                                    focusOnSelect: true
                                });
                            });
                        });
                    },

                    setDeclinationsImageZoom: function() {
                        var md = new MobileDetect(window.navigator.userAgent);
                        if (md.mobile() || md.tablet()) { return; } // no zoom feature on mobile or tablet

                        var self = this;
                        $('.featured-image img').first().elevateZoom(self.zoomConfig); // on page load
                        $('.featured-image').on('afterChange', function(event, slick, currentSlide){
                            $(this).find('img:eq('+currentSlide+')').elevateZoom(self.zoomConfig);
                        });
                    },

                    setProductImageZoom: function() {
                        var md = new MobileDetect(window.navigator.userAgent);
                        if (md.mobile() || md.tablet()) { return; } // no zoom feature on mobile or tablet

                        var self = this;

                        // Product carousel: register zoom behaviour on correct featured image
                        $('.product-main-image img').first().elevateZoom(self.zoomConfig); // on page load

                        $('.product-main-image').on('afterChange', function(event, slick, currentSlide){
                            $(this).find('img:eq('+currentSlide+')').elevateZoom(self.zoomConfig);
                        });
                    }
                },

                // triggered when page is fully loaded and Vue is ready to fire
                ready: function () {

                    // get declinations from Backend
                    var sourceDeclinations = {{ declinations|json_encode|raw }};

                    // convert declination object into array for ease of use
                    var declinations = Object.keys(sourceDeclinations).map(function (key) { return sourceDeclinations[key]; });
                    this.declinations = declinations;

                    var companies = [];
                    var companyNames = [];
                    for (var i = 0; i < declinations.length; i++) {
                        if ($.inArray(declinations[i].company_name, companyNames) === -1){
                            companyNames.push(declinations[i].company_name);
                            companies.push(
                                {
                                    "name": declinations[i].company_name,
                                    "id": declinations[i].company_id
                                }
                            );
                        }
                    }
                    this.companies = companies;

                    // sort results by price (for user convenience)
                    this.sortByPrice();

                    // first refresh
                    this.refresh();

                    // product image zoom
                    this.setProductImageZoom();

                    // declinations images zoom (wait for them to be on DOM)
                    var self = this;
                    Vue.nextTick(function() {
                        self.setDeclinationsImageZoom();
                    });
                }
            });

            // facebook share
            $(".facebook-share").on("click", function(e) {
                e.preventDefault();
                window.open('https://www.facebook.com/sharer/sharer.php?u=' + encodeURIComponent(window.location.href), '_blank', 'width=550, height=400');
            });

            // elements behaviour
            $('.display-filters').on('click', function() {
                $('.filters').toggleClass('is-visible');
            });
        });
    </script>
{% endblock %}
