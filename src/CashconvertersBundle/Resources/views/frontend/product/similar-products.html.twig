{% if recommendedProducts | default %}
    <section class="similar-products">
        <div class="container">
            <div class="row">
                <h1 class="text-center">{{ 'you_will_also_like'|trans|upper }}</h1>
                <hr class="under-title">
                <div class="col-xs-10 col-xs-offset-1 products-slideshow">

                    {% for recommendedProduct in recommendedProducts %}
                        <div class="col-md-2 product-card-container text-center">
                            <a href="{{ recommendedProduct|product_url }}">
                                <div class="row">
                                    <div class="recommended-product-card">
                                        <div class="recommended-product-card-image">
                                            {% if recommendedProduct.mainImage|default %}
                                                <img data-lazy="{{ recommendedProduct.mainImage|image_url(85, 85) }}" style="width: 85px; height: 85px;">
                                            {% else %}
                                                <img data-lazy="{{ asset('images/no-image.jpg') }}" style="width: 85px; height: 85px;">
                                            {% endif %}
                                        </div>
                                        <div class="recommended-product-card-title truncated">
                                            {{ recommendedProduct.name }}
                                        </div>
                                        <div class="recommended-product-card-price">
                                            {{ 'from'|trans }} <span class="price">{{ recommendedProduct.minimumPrice|money }}</span>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </section>
{% endif %}
