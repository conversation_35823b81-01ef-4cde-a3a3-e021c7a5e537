<section class="product-content">
    <div class="container">
        <div class="row">

            {# Product specifications #}
            <div id="main-description" class="product-description col-sm-8 col-sm-push-4">

                {# Infos #}
                <div class="product-main-info">
                    <h2 id="product-title">{{ productReadModel.name }}</h2>

                    {% if productReadModel.brandName %}
                        <p><span>{{ 'by'|trans|lower }}&nbsp;:</span> <span><b>{{ productReadModel.brandName|upper }}</b></span></p>
                    {% endif %}

                    {% if productReadModel.getCode %}
                        <p><span>{{ 'ref'|trans|upper }}&nbsp;: {{ productReadModel.getCode }}</span></p>
                    {% endif %}

                    <div class="pull-right">
                        {% if declinations|length > 0 %}
                            <p class="in-stock"><a href="#offers">{{ declinations|length }} {{ 'offer'|trans|lower }}</a> <span>{{ 'from'|trans|lower }}</span><span class="price"> {{ productReadModel.minimumPrice|money }}</span></p>
                        {% else %}
                            <p class="out-of-stock">{{ 'out_of_stock_products'|trans }}</p>
                        {% endif %}
                    </div>
                </div>

                {# Mobile button #}
                <a href="#offers">
                    <div class="col-xs-10 col-xs-offset-1">
                        <div class="btn merchant-btn pink large visible-xs">
                            <span>{{ 'see_offers'|trans|upper }}</span>
                        </div>
                    </div>
                </a>

                {# Product text #}
                <div class="block product-tab hidden-xs">
                    <!-- Nav tabs -->
                    <ul class="nav nav-tabs" role="tablist">
                        <li role="presentation"><a href="#description" aria-controls="description" role="tab" data-toggle="tab">{{ 'product_description'|trans }}</a></li>
                        <li role="presentation" class="active"><a href="#attributes" aria-controls="attributes" role="tab" data-toggle="tab">{{ 'features'|trans }}</a></li>
                    </ul>

                    <!-- Tab panes -->
                    {# product characteristics and attributes #}
                    <div class="tab-content scrollbar-macosx">
                        <div role="tabpanel" class="tab-pane wysiwyg-output" id="description">
                            {{ productReadModel.description | striptags('<br><p>') | raw }}
                        </div>

                        <div role="tabpanel" class="tab-pane active" id="attributes">
                            {# general caracteristics #}
                            <h3>{{ 'general_characteristics'|trans }}&nbsp;:</h3>
                            <ul class="list-unstyled">
                                {# regular attributes #}
                                {% for attribute in productReadModel.ungroupedAttributesValues %}
                                    <li>
                                        <span class="attribute-name">{{ attribute.attribute.name }}&nbsp;:</span>
                                        <span>
                                            {% for value in attribute.values %}
                                                {{ value.name }}{% if not loop.last %}, {% endif %}
                                            {% endfor %}
                                        </span>
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            {# Product images + action buttons #}
            <div id="product-left" class="col-sm-4 col-sm-pull-8 text-center">

                {# Main image #}
                {% if productReadModel.images %}
                    <div class="product-main-image">
                        {% for image in productReadModel.images | slice(0,3) %}
                            <img src="{{ image_url_by_id(image.id, 318) }}">
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="product-image-no-image">
                        <img src="{{ asset('images/no-image.jpg') }}" style="max-width: 260px;">
                    </div>
                {% endif %}

                {# Thumbnails #}
                <div id="product-thumbnails" class="product-thumbnails">
                    {% for image in productReadModel.images | slice(0,3) %}
                        <img src="{{ image_url_by_id(image.id, 100) }}">
                    {% endfor %}
                </div>

                <p><strong>{{ 'photos_subtext'|trans }}</strong></p>
                <hr>

                {# User buttons #}
                <a href="#" class="facebook-share">
                    <div class="col-xs-4 single-product-cta text-center">
                        <div class="single-product-icon" style="background: url('{{ asset('images/icons/share.png') }}') no-repeat center;"></div>
                        <div class="single-product-icon-hover" style="background: url('{{ asset('images/icons/share-hover.png') }}') no-repeat center;"></div>
                        <span>{{ 'share'|trans }}</span>
                    </div>
                </a>

                <span href="#" class="disabled-button"> {# TODO: change back to a button when feature is done #}
                    <div class="col-xs-4 single-product-cta single-product-cta-mid  text-center">
                        <div class="single-product-icon" style="background: url('{{ asset('images/icons/alert.png') }}') no-repeat center;"></div>
                        <div class="single-product-icon-hover" style="background: url('{{ asset('images/icons/alert-hover.png') }}') no-repeat center;"></div>
                        <span>{{ 'my_alerts'|trans }}</span>
                    </div>
                </span>

                <span href="#" class="disabled-button"> {# TODO: change back to a button when feature is done #}
                    <div class="col-xs-4 single-product-cta  text-center">
                        <div class="single-product-icon" style="background: url('{{ asset('images/icons/wish.png') }}') no-repeat center;"></div>
                        <div class="single-product-icon-hover" style="background: url('{{ asset('images/icons/wish-hover.png') }}') no-repeat center;"></div>
                        <span>{{ 'wishlist'|trans }}</span>
                    </div>
                </span>
                <hr class="visible-xs">
            </div>
        </div>
    </div>

    {# Mobile buttons for product text #}
    <div class="container">
        <div class="row">
            <div id="mobile-buttons" class="visible-xs">

                {# Product description button #}
                <div class="btn default-btn black-btn visible-xs show-hide-btn" data-toggle="collapse" data-target="#mobile-product-desc">
                    <span>{{ 'product_description'|trans|upper }}</span>
                    <span class="glyphicon glyphicon-menu-down"></span>
                </div>

                {# Product description text #}
                <div id="mobile-product-desc" class="collapse">
                    {{ productReadModel.description|raw }}
                </div>

                {# Product characteristics button #}
                <div class="btn default-btn black-btn visible-xs show-hide-btn" data-toggle="collapse" data-target="#mobile-product-charac">
                    <span>{{ 'characteristics'|trans|upper }}</span>
                    <span class="glyphicon glyphicon-menu-down"></span>
                </div>

                {# Product characteristics text #}
                <div id="mobile-product-charac" class="collapse">
                    {%  for group in productReadModel.groupedAttributesValues %}
                        <h3>{{ group.name }}&nbsp;:</h3>
                        <ul class="list-unstyled">
                            {% for attribute in group.attributesValues %}
                                <li>
                                    <span class="attribute-name">{{ attribute.label }}&nbsp;:</span>
                                    <span>{{ attribute.values|join(', ') }}</span>
                                </li>
                            {% endfor %}
                        </ul>
                    {% endfor %}

                    {# general caracteristics #}
                    <h3>{{ 'general_characteristics'|trans }}&nbsp;:</h3>
                    <ul class="list-unstyled">
                        {# regular attributes #}
                        {% for attribute in productReadModel.ungroupedAttributesValues %}
                            <li>
                                <span class="attribute-name">{{ attribute.label }}&nbsp;:</span>
                                <span>{{ attribute.values|join(', ') }}</span>
                            </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
    </div>

</section>
