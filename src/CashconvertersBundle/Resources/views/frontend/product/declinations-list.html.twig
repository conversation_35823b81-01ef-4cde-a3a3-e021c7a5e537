<section id="offers" class="product-offers">
    <div class="container">
        <div class="row">

            {# Block title #}
            <h1 class="text-center">{{ 'our_offers'|trans|upper }}</h1>
            <hr class="under-title">

            <section id="declinations">
                {% if declinations|length == 0 %}
                    <p class="no-offers"><strong>{{ 'no_offer'|trans|upper }}</strong></p>
                {% else %}

                {# show/hide filters on small screens #}
                <button class="btn default-btn light-purple-btn visible-xs display-filters">{{ 'refine'|trans|upper }}</button>

                {# filtering #}
                <div class="filters">

                    {# Order by price #}
                    <div class="select-container">
                        <select v-model="priceDirection" @change="sortByPrice">
                            <option :value="1">{{ 'ascending_price'|trans }}</option>
                            <option :value="-1">{{ 'descending_price'|trans }}</option>
                        </select>
                        <span class="select-icon glyphicon glyphicon-triangle-bottom"></span>
                    </div>

                    {# Filter by vendor #}
                    <div class="select-container" v-cloak>
                        <select v-model="selectedVendorId" @change="resetToFirstPage">
                            <option value="" :selected="companiesIdSelected.length == 0 || companiesIdSelected.length > 1">{{ 'all_shops'|trans }}</option>
                            <option :value="company.id"
                                    v-for="company in companies"
                                    v-text="company.name"
                                    :selected="companiesIdSelected.length == 1 && companiesIdSelected[0] === company.id">
                            </option>
                        </select>
                        <span class="select-icon glyphicon glyphicon-triangle-bottom"></span>
                    </div>

                    {# Results per page #}
                    <div class="select-container">
                        <select v-model="pagination.resultsPerPage" @change="resetToFirstPage">
                            <option selected :value="20">20 {{ 'results_by_page'|trans }}</option>
                            <option :value="10">10 {{ 'results_by_page'|trans }}</option>
                            <option :value="5">5 {{ 'results_by_page'|trans }}</option>
                        </select>
                        <span class="select-icon glyphicon glyphicon-triangle-bottom"></span>
                    </div>
                </div>

                {# pagination #}
                {% include '@App/frontend/common/pagination.html.twig' %}

                {# Declinations #}
                {# ============ #}

                <div class="declinations">

                    <div class="declination" v-cloak
                         v-for="(declinationId, declination) in declinationsByVendor
                                 | orderBy sorting.criteria sorting.direction
                                 | limitBy pagination.resultsPerPage offset">

                        <div class="main-data">

                            {# main image block #}
                            <div class="featured-images-block">

                                {# featured image #}
                                <div v-if="declination.images[0].id">
                                    <div :id="declinationId + '-featured'" class="featured-image" :data-nav="'#' + declinationId + '-nav'">
                                        <img :src="imageUrl(declination.images[i].id, 900)" v-for="i in declination.images.length" v-if="i < 3">
                                    </div>
                                </div>

                                {# no image #}
                                <div v-else class="product-image-no-image">
                                    <img src="{{ asset('images/no-image.jpg') }}" style="max-width: 260px">
                                </div>
                            </div>


                            <div class="declination-details">

                                {# Vendor #}
                                <div class="vendor">

                                    <div class="vendor-detail">
                                        <span>{{ 'sold_by'|trans }}&nbsp;:&nbsp;</span><a :href="'/' + declination.company_slug" class="company-link">${ declination.company_name }</a>

                                        {# Company rating #}
                                        <span class="rank">
                                            <span v-for="j in range(1, 6)">
                                                <i v-if="j <= declination.company_rating" class="glyphicon glyphicon-star on"></i>
                                                <i v-else class="glyphicon glyphicon-star off"></i>
                                            </span>
                                        </span>
                                    </div>

                                    {# Contact info #}
                                    <div class="hidden-xs">
                                        <div>${ declination.company_address } - ${ declination.company_zipcode }&nbsp;<span class="text-uppercase">${ declination.company_city }</span></div>
                                        <div>Téléphone&nbsp;:&nbsp;${ declination.company_phone } / <a href="{{ cscart_url("discuss.view?discussion_id=0?product_id=") }}${ declination.product_id }" class="contact-link">Contacter</a></div>
                                    </div>
                                </div>

                                {# under the images on small screens #}
                                <div class="hidden-xs">
                                    {# Status #}
                                    <div><b>{{ 'status'|trans }}&nbsp;:&nbsp;</b>${ getDeclinationCondition(declination) }</div>

                                    {# Available from #}
                                    <div><b>{{ 'delivery_if_stock'|trans }}&nbsp;:&nbsp;</b>${ getDeclinationDeliveryTime(declination) }</div>

                                    <div v-html="declination['description']"></div>
                                </div>

                            </div>
                        </div>

                        <div class="actions">

                            <div class="thumbnails-price-group">

                                {# thumbnails #}
                                <div class="thumbnails-block">
                                    <div v-if="declination.images[0].id">
                                        <div :id="declinationId + '-nav'" class="declination-thumbnails list-inline" :data-featured="'#' + declinationId + '-featured'">
                                            <img class="declination-thumbnail" :src="imageUrl(declination.images[i].id, 100)" v-for="i in declination.images.length" v-if="i < 3">
                                        </div>
                                    </div>
                                </div>

                                {# duplicated code for layout purpose #}
                                <div class="status-shipping-group text-center visible-xs">

                                    {# Status #}
                                    <div><b>{{ 'status'|trans }}&nbsp;:&nbsp;</b>${getDeclinationCondition(declination)}</div>

                                    {# Available from #}
                                    <div><b>{{ 'delivery_if_stock'|trans }}&nbsp;:&nbsp;</b>${ getDeclinationDeliveryTime(declination) }</div>

                                    <div v-html="declination['description']"></div>
                                </div>

                                {# Price #}
                                <div class="price-block"><span><b>{{ 'price'|trans }}&nbsp;:&nbsp;</b></span><span class="price">${ declination.price|price } {{ currencySign }}</span></div>
                            </div>


                            <div class="detail-buttons-group">

                                {# See desc link #}
                                <a href="#main-description" class="see-detail">{{ 'see_description'|trans }}</a>

                                {# buttons #}
                                <div class="buttons">

                                    {# Basket button #}
                                    <form action="{{ path('basket_add_product') }}" method="post" class="add-to-basket-form">
                                        <input type="hidden" name="redirect_url" value="{{ productReadModel|product_url(productReadModel.id) }}">
                                        <input type="hidden" name="product_id" :value="declination.objectID">
                                        <input type="hidden" name="quantity" value="1">


                                        <div class="padding-left-btn-add">
                                            {% if productReadModel.affiliateLink %}
                                                <a :href="declination.affiliateLink" class="btn basket-submission" v-if="declination.affiliateLink">
                                                    {{ 'add_to_card'|trans|upper }}
                                                </a>
                                            {% else %}
                                                {# tracking avec realitycs de l'ajout au panier #}
                                                <button type="submit" class="btn default-btn light-purple-btn" {% if not constant('DEVELOPMENT')|default(false) %}onclick="RY.track('ajout_panier');"{% endif %}>
                                                    <span class="glyphicon glyphicon-shopping-cart"></span>
                                                    <span>{{ 'add_to_cart'|trans|upper }}</span>
                                                </button>
                                            {% endif %}
                                        </div>
                                    </form>

                                    {# Sell mine button #}
                                    <a href="{{ "sell_min_url"|trans }}" class="btn default-btn light-purple-btn-outline">
                                        <span class="glyphicon glyphicon-share-alt flip-y"></span>
                                        <span>{{ 'sell_mine'|trans }}</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {# pagination (again, for user convenience) #}
                {% include '@App/frontend/common/pagination.html.twig' %}

            {% endif %}
            </section>
        </div>
    </div>
</section>
