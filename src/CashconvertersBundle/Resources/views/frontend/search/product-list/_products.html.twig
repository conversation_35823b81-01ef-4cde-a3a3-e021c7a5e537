<div class="search-results">
    <div v-for="product in products" class="product">
        <a class="clickable-area" :href="getUrlProduct(product)">
            {# with image #}
            <img v-if="product.mainImage && product.mainImage.id"
                 class="product-image"
                 :src="imageUrl(product, 250)">
            {# without image #}
            <img v-else class="no-image" src="{{ asset('images/no-image.jpg') }}">
            <div class="name truncated">${ product.name }</div>
            <div class="footer">
                {# offer from: display when at least one vendor is selected we hide the offers #}
                <div v-show="!getCompanySelected">
                    <template v-if="product.declinationCount > 0">
                        <div class="offer-count">${ product.declinationCount } {{ 'offers'|trans }}</div>
                        <div class="footer-label">{{ 'from'|trans }}</div>
                    </template>
                    <span class="price">${ product.minimumPrice|price }<sup>{{ currencySign }}</sup></span>
                </div>
            </div>
        </a>
    </div>
    <div v-if="products.length == 0" class="no-result">
        <h2 class="text-center">{{ 'no_result_found'|trans }}</h2>
    </div>
</div>
