<script>
    var vm;
    // executed only after page load
    $(function() {
        const GEO_MAX_RADIUS = 100000;
        const DEFAULT_GEO_RADIUS = 20000;
        var search = new SearchClient('{{ path('api_search_products') }}', '{{ path('api_search_products_autocomplete') }}');
        var urlParameters = search.restoreSearchFromUrl();
        const lang = '{{ app.request.locale }}';

        // Vue components
        // ==============

        // price slider component
        Vue.component('price-slider', { // name of the element (eg.: <slider>)

            template: '#slider-template',

            props: ['min', 'max'],

            data: function () {
                // this syntax checks properties level by level
                var currentMax = ((this.$parent.filters || {}).price || {}).max;
                var currentMin = ((this.$parent.filters || {}).price || {}).min;

                return {
                    currentMin: currentMin ? currentMin : this.min,
                    currentMax: currentMax ? currentMax : this.max
                }
            },

            activate: function (done) {
                var view = this;

                $(this.$el).find('#price-slider').slider({
                    range: true,
                    min: Math.floor(view.min),
                    max: Math.ceil(view.max),
                    values: [view.currentMin, view.currentMax],
                    slide: function (event, ui) {
                        view.currentMin = ui.values[0];
                        view.currentMax = ui.values[1];
                    },
                    stop: function () {
                        // Dispatch an event to the main view to update the search
                        view.$dispatch('update', view.currentMin, view.currentMax);
                    }
                });
                done();
            }
        });

        Vue.component("geo-slider", {

            template: '#geo-slider-template',

            props: ['min', 'max', 'step'],

            data: function () {
                return {
                    currentMin: 0, // fixed
                    currentMax: this.getMaxRadius()
                }
            },

            methods: {
                getFormattedCurrentMax: function() {
                    return this.currentMax / 1000;
                },

                getMaxRadius: function() {
                    if (urlParameters.geoFilter && urlParameters.geoFilter.radius) {
                        return parseInt(urlParameters.geoFilter.radius);
                    }

                    return DEFAULT_GEO_RADIUS;
                },

                updateSlider: function() {
                    var view = this;

                    $(this.$el).find('#distance-slider').slider({
                        range: 'min',
                        min: view.min,
                        max: view.max,
                        step: view.step,
                        value: view.currentMax,
                        slide: function(event, ui) {
                            view.currentMax = ui.value;
                        },

                        stop: function () {
                            // Dispatch an event to the main view to update the search
                            view.$dispatch('update', view.currentMax);
                        }
                    });
                }
            },

            activate: function (done) {
                var view = this;

                // reset slider on event
                this.$on('reset-geolocation', function() {
                    view.max = GEO_MAX_RADIUS;
                    view.currentMax = DEFAULT_GEO_RADIUS;
                    view.updateSlider();
                });

                view.updateSlider();

                done();
            }
        });

        // Vue
        // ===

        {# we merge the filters from the URL query with the ones from other sources (like the category slug for exemple) #}
        var initialFilters = urlParameters.filters || {};
        var otherInitialFilters = {{ filters|default({})|json_encode|raw }};
        for (var key in otherInitialFilters) {
            if (!otherInitialFilters.hasOwnProperty(key)) {
                continue;
            }
            initialFilters[key] = otherInitialFilters[key];
        }
        vm = new Vue({
            el: '#search',

            data: {
                loading: true,

                products: [],

                displayedCategoryPanel: { category: { name: '' } }, // set default value to avoid warning

                // for information purpose
                currentCategoryName: '{{ currentCategory.name|default|raw|e('js') }}' || '',

                query: urlParameters.query || '',

                pagination: {
                    page: urlParameters.page || 1,
                    resultsPerPage: urlParameters.resultsPerPage || 24
                },

                mobilePageNumbersConstraint: 5,

                filters: initialFilters,

                filterLimit: 5,
                showAllFilters: false,

                geoFilter: urlParameters.geoFilter || {},

                facets: [],

                sorting: urlParameters.sorting || 'createdAt-desc',

                company: ''
            },
            methods: {
                refresh: function() {
                    var self = this;

                    if(typeof(self.sorting) === "object") {
                        var realSorting = self.sorting;
                        self.sorting = Object.keys(self.sorting)[0]+'-'+self.sorting[Object.keys(self.sorting)[0]];
                    }
                    else {
                        // here we can rebuild sorting
                        var realSorting = {};

                        var tempString = self.sorting.split("-");
                        var orderField = tempString[0];
                        realSorting[orderField] = tempString[1];
                    }
                    // toggle category tree regarding current selected category
                    self.refreshCategoryTree();

                    // set back loading state to true
                    self.loading = true;

                    // actual product search
                    search.searchProducts(
                        self.query,
                        self.pagination.page,
                        self.pagination.resultsPerPage,
                        self.filters,
                        realSorting,
                        self.geoFilter,
                        {},

                        // callback
                        function(response) {

                            // set page data from search result
                            self.loading = false; // loading is done
                            self.products = response.results;
                            self.pagination = response.pagination;
                            self.facets = response.facets;

                            var facetCompaniesId = [];

                            var facetCompanies = self.facets.filter(function (facet) {
                                return facet.name === 'companies';
                            });

                            if (facetCompanies.length > 0) {
                                facetCompaniesId = Object.keys(facetCompanies.values).map(function (id) {
                                    return parseInt(id);
                                });
                            }

                            self.setCompaniesIdEach(facetCompaniesId);

                            // save search result into URL to be used / manipulated
                            search.saveSearchInUrl(
                                self.query,
                                self.pagination.page,
                                self.pagination.resultsPerPage,
                                self.filters,
                                realSorting,
                                self.geoFilter
                            );

                            // si pour une raison ou une autre la page stockée dans l'url est supérieure
                            // au nombre de pages contenant des résultats, on repasse sur la page 1 et on rafraîchit le tout
                            if (self.pagination.page != 1 && response.pagination.nbPages < self.pagination.page) {
                                self.pagination.page = 1;
                                self.refresh();
                            }

                            // optional - add a placeholder to align last row off products to the left (flexbox)
                            self.alignLastRow();
                        }
                    );
                },

                updateGeoFilter: function(range) {
                    if (!this.geoFilter) {
                        this.geoFilter = {};
                    }

                    if(this.geoFilter['lat'] == undefined || this.geoFilter['lng'] == undefined) {
                        $(document).trigger('launch-geolocation');
                    }

                    this.geoFilter['radius'] = range;
                    this.refresh();
                },

                // style helper
                facetIsChecked: function(facetName, variantName) {
                    if (this.filters[facetName]) {
                        if (typeof this.filters[facetName] === 'string') {
                            return this.filters[facetName] == variantName;
                        }
                        return this.filters[facetName].indexOf(variantName) > -1;
                    }

                    return false;
                },

                // order facets by label
                orderFacets: function (values) {

                    var facetValues = [];
                    // save facet key as it will be lost on ordering process
                    _.forEach(values, function (value, key) {
                        value.key = key;
                        facetValues.push(value);
                    });

                    facetValues.sort(function (a, b) {
                        return new Intl.Collator(lang).compare(a.label, b.label);
                    });

                    return facetValues;
                },

                // pagination helper functions
                // ===========================
                goToPage: function (page) {

                    page = Math.max(1, page); // cannot go below page 1
                    page = Math.min(this.pagination.nbPages, page); // cannot go over total number of pages

                    this.pagination.page = page;
                    this.scrollToTop(); // scroll page to result list top
                    this.refresh();
                },

                togglePageList: function(e) {
                    var $target = $(e.currentTarget);
                    $target.toggleClass('is-open');
                },

                toggleCompany: function(variantName) {
                    // Filter Seller Component: allows the exchange of data
                    this.toggleCompanyIdSelected(parseInt(variantName));
                },

                /**
                 * One filter can hold multiple values
                 * keepOtherVariant option is for specific case
                 * eg.: "color: blue, red, green"
                 * will show blue, red and green products
                 * but WILL NOT exclude products that are not: blue AND red AND green
                 */
                toggleFilter: function (facetName, variantName, keepOtherVariant) {
                    // Make sure this.filters[facetName] is an array
                    if (!this.filters.hasOwnProperty(facetName) || !Array.isArray(this.filters[facetName])) {
                        //specific case for companies
                        if(this.filters[facetName] === variantName) {
                            Vue.set(this.filters, facetName, [variantName]);
                        }
                        else {
                            Vue.set(this.filters, facetName, []);
                        }
                    }

                    // first case, toggle off (clear filter)
                    var indexOf = this.filters[facetName].indexOf(variantName);
                    if (indexOf > -1) {
                        this.filters[facetName].splice(indexOf, 1);

                    // second case: toggle on (push the value into the filter array)
                    } else {
                        if(!keepOtherVariant) {
                            Vue.set(this.filters, facetName, []);
                        }
                        this.filters[facetName].push(variantName);
                    }

                    this.refresh();
                },

                showMoreFilters: function() {
                    this.showAllFilters = ! this.showAllFilters;
                },

                imageUrl: function (product, size) {
                    return Routing.generate('api_image_get', {
                        id: product.mainImage.id,
                        w: size,
                        h: size
                    });
                },

                // to avoid multiple instances of geolocation inputs (there's already one in the header)
                moveMobileAddressInputs: function() {
                    var $geolocAddressInput = $('#geoloc-address-input');
                    var $geolocButton = $('#geoloc-button');
                    var $mobileFilterContainer = $('#filter-geoloc-container');
                    var $headerAddressContainer = $('#header-geoloc-address-container');
                    var $headerButtonContainer = $('#header-geoloc-button-container');

                    // if filters modal is displayed, inputs have to be in filter specific container
                    if($('.xs-filters').hasClass('is-visible')) {
                        $mobileFilterContainer.append($geolocAddressInput);
                        $mobileFilterContainer.append($geolocButton);

                    // else they have to be in the header
                    } else {
                        $headerAddressContainer.append($geolocAddressInput);
                        $headerButtonContainer.append($geolocButton);
                    }
                },

                // price slider has been updated
                updateNumericFilter: function (facetName, min, max) {
                    if (!this.filters[facetName]) {
                        this.filters[facetName] = {};
                    }
                    this.filters[facetName]['min'] = min;
                    this.filters[facetName]['max'] = max;
                    this.refresh();
                },

                // close page list when user clicks somewhere else on the page
                // (used on small screens)
                dismissPageListBehaviour: function() {
                    $(document).on('click', function(e) {
                        var $target = $(e.target);

                        if(! $target.closest('.page').length) {
                            $('.page').removeClass('is-open');
                        }
                    });
                },

                // get back to the top of content on page change
                scrollToTop: function() {
                    $('html, body').animate({
                        scrollTop: $('.results-numbers').offset().top
                    }, 450);
                },

                // open branches leading to currently selected category
                refreshCategoryTree: function() {
                    var $currentCategory = $('.is-current');
                    $currentCategory.parents('.branch-node').addClass('is-open');
                },

                range: function (a, b) {
                    var rangeArray = [];

                    for(var i = a; i < b; i++) {
                        rangeArray.push(i);
                    }

                    return rangeArray;
                },

                clearFilters: function() {

                    // reset geofilter slider in child component
                    this.$broadcast('reset-geolocation');

                    // tell header Vue to clean itself
                    $(document).trigger('reset-geolocation');

                    // clear filters (except the selected category)
                    for (var property in this.filters) {
                        if (property !== 'categories' && this.filters.hasOwnProperty(property)) {
                            Vue.delete(this.filters, property);
                        }
                    }

                    // clear facets
                    this.facets = [];

                    // refresh
                    this.refresh();
                },

                alignLastRow: function() {

                    // cleanup
                    $('.product.placeholder').remove();

                    // check if last row is only 2 products (instead of 3)
                    if (this.products.length % 3 === 2) {

                        // append a placeholder to force left alignment
                        $('.search-results').append('<div class="product placeholder"></div>');
                    }
                },

                getFormattedFilteredValues: function(i, value) {
                    if(i !== this.companiesFilter().length -1) {
                        return value + ' / ';
                    }

                    // last value hasn't got a trailing '/'
                    return value;
                },

                headerGeolocHasChanged: function(e, geoFilter) {
                    this.geoFilter = geoFilter;
                    this.refresh();
                },

                bindExternalEvents: function () {

                    // wait for geoloc change in header element
                    $(document).on('refreshGeoFilter', this.headerGeolocHasChanged.bind(this));
                },

                // build target path with filters for category links
                buildPath: function(originalPath) {
                    var uri = new URI();
                    var currentQuery = uri.search(true);

                    var query = '';

                    //parsing the query, rebuilding it without selected categories and empty parameters
                    for (var param in currentQuery) {
                        if (!currentQuery.hasOwnProperty(param)) {
                            continue;
                        }

                        if (paramIsElligible(param, currentQuery)) {
                            if (Array.isArray(currentQuery[param])) {
                                for (var valueKey in currentQuery[param]) {
                                    if (!currentQuery[param].hasOwnProperty(valueKey)) {
                                        continue;
                                    }

                                    query += addToQuery(param, currentQuery[param][valueKey]);
                                }
                            } else {
                                query += addToQuery(param, currentQuery[param]);
                            }
                        }
                    }

                    // redirect to the right page with filters
                    window.location.replace(originalPath+'?'+query);
                },

                // Get the url of a product and add the ID company
                getUrlProduct: function (product) {
                    return product.url + '?company=' + product.companies[0].id;
                }
            },
            computed: {
                attributesFilters: function() {
                    return this.facets.filter(function(facet) {
                        return facet.name !== 'categories' && facet.name !== 'companies' && facet.name !== 'price';
                    });
                },

                companiesFilter: function() {
                    var self = this;
                    var values = [];

                    self.facets.forEach(function(facet) {
                        // discard 'categories' and price
                        if(facet.name !== "companies") {
                            return false;
                        }

                        // if current facet is used in filters, push facet label into companiesFilter array
                        if(self.filters.hasOwnProperty(facet.name)) {

                            for(var index in self.filters[facet.name]) {
                                if(self.filters[facet.name].hasOwnProperty(index)) {
                                    values.push( facet['values'][self.filters[facet.name][index]].label );
                                }
                            }
                        }
                    });

                    return values;
                },

                totalPages: function() {

                    // pagination maxed out at 30 because of Algolia's '1000 products request' limitation
                    return Math.min(this.pagination.nbPages, 30);
                },

                // Get company selected from filter seller
                getCompanySelected: function () {
                    return this.companiesIdSelected.length > 0;
                }
            },

            ready: function() {
                this.refresh();
                this.dismissPageListBehaviour(); // called only once

                // register outside events (eg. header geoloc)
                this.bindExternalEvents();
            },

            filters: {
                price: W.formatPrice
            }
        });

        // Events
        // ======

        vm.$watch('sorting', function() {
            this.refresh();
        });

        // DOM behaviour
        // ==============

        // toggle category tree behaviour
        $('.category').on('click', function() {
            $(this).closest('.branch-node').toggleClass('is-open');
        });

        // toggle small screens filters
        $('.xs-filter-buttons').find('.refine').on('click', function() {
            $('.global-overlay').toggleClass('is-visible');
            $('.xs-filters').toggleClass('is-visible');
        });

        $('.filter-trigger').on('click', function() {
            var $target = $(this).data('target');

            // if there was already a 'current' panel, it's not the current one anymore
            $('.mobile-panel.is-current').removeClass('.is-current');

            $($target).addClass('is-in is-current');
        });

        $('.filters-close, .close-modal').on('click', function() {
            $('.mobile-panel').removeClass('is-in');
            closeFilterModal();
        });

        $('.panel-close, .close-panel').on('click', function() {
            var $panel = $(this).closest('.mobile-panel');
            $panel.removeClass('is-current');
            $panel.removeClass('is-in');
        });

        $('.global-overlay').on('click', function() {
            $('.mobile-panel').removeClass('is-in');
            closeFilterModal();
        });

        // close filter modal
        function closeFilterModal() {
            $('.xs-filters').removeClass('is-visible');

            // optional delay
            setTimeout(function() {
                $('.global-overlay').toggleClass('is-visible');
            }, 150);

            // move back geoloc address inputs
            vm.moveMobileAddressInputs();
        }

        function paramIsElligible(param, query) {
            return (param.search('geo') !== -1
                || param === 'companies')
            && query[param] !== null;
        }

        function addToQuery(param, value) {
            return '&'+param+'='+value;
        }
    });
</script>
