<div id="categories-panel" class="mobile-panel">
    <div class="container">
        {# panel close button #}
        <div class="panel-close"><span class="glyphicon glyphicon-remove"></span></div>

        <div class="filters-title">
            <div>{{ 'refine'|trans }}</div>

            {# display search key word if it exists #}
            <div class="search-keyword" v-if="query">«&nbsp;${ query }&nbsp;»</div>

            {# panel label #}
            <div class="panel-title">{{ 'categories'|trans }}</div>
        </div>

        <div class="filter-triggers">
            {% for category in categories %}
                {% if category.children|default %}

                    <div class="filter-trigger"
                         @click="displayedCategoryPanel = {{ category|json_encode }}"
                         data-target="#sub-categories-panel">

                        <span></span> {# placeholder #}
                        <span class="category" :class="{ 'is-current': (filters['categories'] == {{ category.category.id }}) }">
                            {{ category.category.name }} ({{ category.category.productCount }})
                        </span>
                        <span class="glyphicon glyphicon-menu-right"></span>
                    </div>

                {% else %}

                    <a class="select-category" href="{{ path('category', {categoryPath: category.category.slug}) }}">
                        <span class="category" :class="{ 'is-current': (filters['categories'] == {{ category.category.id }}) }">
                            {{ category.category.name }} ({{ category.category.productCount }})
                        </span>
                    </a>

                {% endif %}
            {% endfor %}
        </div>
    </div>
</div>
