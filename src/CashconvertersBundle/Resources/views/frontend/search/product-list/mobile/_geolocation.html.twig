<div id="geolocation-panel" class="mobile-panel">
    {# panel close button #}
    <div class="panel-close"><span class="glyphicon glyphicon-remove"></span></div>

    <div class="filters-title">
        <div>{{ 'refine'|trans }}</div>

        {# display search key word if it exists #}
        <div class="search-keyword" v-if="query">«&nbsp;${ query }&nbsp;»</div>

        {# panel label #}
        <div class="panel-title">{{ 'my_position'|trans }}</div>
    </div>

    <div id="filter-geoloc-container" class="mobile-geoloc"></div> {# will hold moved geoloc inputs #}

    {# geographical distance #}
    <div class="filter-block">
        <div class="content">
            {# geographical distance slider #}
            <geo-slider
                    :min="0"
                    :max="100000"
                    :step="10000"
                    @update="updateGeoFilter($arguments[0])">
            </geo-slider>
        </div>
    </div>

    <button class="btn btn-purple close-panel">{{ 'close'|trans|upper }}</button>
</div>
