<div id="attributes-panel" class="mobile-panel">
    {# panel close button #}
    <div class="panel-close"><span class="glyphicon glyphicon-remove"></span></div>

    <div class="filters-title">
        <div>{{ 'refine'|trans }}</div>

        {# display search key word if it exists #}
        <div class="search-keyword" v-if="query">«&nbsp;${ query }&nbsp;»</div>

        {# panel label #}
        <div class="panel-title">{{ 'filter_by'|trans }}</div>
    </div>

    {# vendor facets #}
    <div v-for="facet in facets" track-by="name">
        <div class="filter-block vendor-facet" v-if="facet.name == 'companies'">
            <div class="title" v-text="facet.label"></div>

            <ul class="content list-unstyled">
                <li class="checkbox" v-for="(valueId, value) in facet.values"
                    @click="toggleCompany(valueId)">
                    <span class="checkbox-image" :class="{ 'checked': facetIsChecked(facet.name, valueId) }"></span>
                    <span v-text="value.label"></span>
                </li>
            </ul>
        </div>
    </div>

    <button class="btn btn-purple close-panel">{{ 'close'|trans|upper }}</button>
</div>
