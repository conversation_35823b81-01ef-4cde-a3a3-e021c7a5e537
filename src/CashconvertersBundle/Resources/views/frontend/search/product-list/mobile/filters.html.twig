<div class="xs-filters">
    <div class="filters-close"><span class="glyphicon glyphicon-remove"></span></div>

    <div class="filters-title">
        <div>{{ 'refine'|trans }}</div>

        {# display search key word if it exists #}
        <div class="search-keyword" v-if="query">«&nbsp;${ query }&nbsp;»</div>
    </div>

    {# loop in facets to get price slider #}
    <div v-for="facet in facets" track-by="name">

        {# price #}
        <div class="filter-block" v-if="facet.name == 'price'">
            <div class="title">{{ 'search_price'|trans }}</div>

            <div class="content">

                {# range slider #}
                <price-slider
                    :min="facet.values.min"
                    :max="facet.values.max"
                    @update="updateNumericFilter(facet.name, $arguments[0], $arguments[1])">
                </price-slider>

                {# price sorting #}
                <div class="select-container">
                    <select v-model="sorting">
                        <option value="">{{ 'order_by'|trans }}</option>
                        <option value="price-asc">{{ 'ascending_price'|trans }}</option>
                        <option value="price-desc">{{ 'descending_price'|trans }}</option>
                        <option value="createdAt-desc">{{ 'date_desc'|trans }}</option>
                        <option value="createdAt-asc">{{ 'date_asc'|trans }}</option>
                    </select>
                    <span class="select-icon glyphicon glyphicon-triangle-bottom"></span>
                </div>
            </div>
        </div>
    </div>

    {# sub filter triggers #}
    <div class="filter-triggers">
        <div class="filter-item">
            <div class="filter-trigger" data-target="#categories-panel">
                <span></span> {# placeholder #}
                <span>{{ 'categories_sub_categories'|trans }}</span>
                <span class="glyphicon glyphicon-menu-right"></span>
            </div>
            <div class="filter-detail" v-text="currentCategoryName"></div>
        </div>

        <div class="filter-item">
            <div class="filter-trigger geolocation-menu" data-target="#geolocation-panel" @click="moveMobileAddressInputs">
                <span></span> {# placeholder #}
                <span>{{ 'my_position'|trans }}</span>
                <span class="glyphicon glyphicon-menu-right"></span>
            </div>
            <div class="filter-detail" v-text="geoFilter.label"></div>
        </div>

        <div class="filter-item">
            <div class="filter-trigger attributes-menu" data-target="#attributes-panel" @click="moveMobileAddressInputs">
                <span></span> {# placeholder #}
                <span>{{ 'vendors'|trans }}</span>
                <span class="glyphicon glyphicon-menu-right"></span>
            </div>
            <div class="filter-detail">
                <span v-for="(i, value) in companiesFilter" v-text="getFormattedFilteredValues(i, value)"></span>
            </div>
        </div>
        <div class="filter-item top-filters">
            <div v-for="(index, facet) in attributesFilters" v-if="index < filterLimit || showAllFilters">
                    <div class="dropdown dropdown-filter">
                        <button class="dropdown-filter-button" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <span>${facet.label}</span>
                            <span class="select-icon glyphicon glyphicon-triangle-bottom"></span>
                        </button>
                        <ul class="dropdown-menu">
                            <li v-for="(valueId, value) in facet.values"
                                @click="toggleFilter(facet.name, valueId, true)"
                                :class="{'selected-filter' : facetIsChecked(facet.name, valueId)}"
                                v-text="value.label"></li>
                        </ul>
                    </div>
                </div>
                <a href="#" @click.prevent="showMoreFilters" v-if="! showAllFilters"><span class="glyphicon glyphicon-menu-down">&nbsp;</span>{{ 'see_more_filters'|trans }}</a>
                <a href="#" @click.prevent="showMoreFilters" v-else><span class="glyphicon glyphicon-menu-up">&nbsp;</span>{{ 'see_less_filters'|trans }}</a>
        </div>

    </div>

    <button class="btn btn-purple close-modal">{{ 'close'|trans|upper }}</button>
    <button class="btn btn-white clear-filter" @click="clearFilters">{{ 'clear_filter'|trans|upper }}</button>

    {# sub modals #}
    {% include('@App/frontend/search/product-list/mobile/_categories.html.twig') %}
    {% include('@App/frontend/search/product-list/mobile/_sub-categories.html.twig') %}
    {% include('@App/frontend/search/product-list/mobile/_geolocation.html.twig') %}
    {% include('@App/frontend/search/product-list/mobile/_vendors.html.twig') %}
</div>
