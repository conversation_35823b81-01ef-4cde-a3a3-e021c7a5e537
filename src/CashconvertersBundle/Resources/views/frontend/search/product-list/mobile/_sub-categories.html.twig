<div id="sub-categories-panel" class="mobile-panel">

    {# panel close button #}
    <div class="panel-close"><span class="glyphicon glyphicon-remove"></span></div>

    <div class="filters-title">
        <div>{{ 'refine'|trans }}</div>

        {# display search key word if it exists #}
        <div class="search-keyword" v-if="query">«&nbsp;${ query }&nbsp;»</div>

        {# panel label #}
        <div class="panel-title"
             v-text="'{{ 'category'|trans }} ' + displayedCategoryPanel.category.name">
        </div>
    </div>

    <div class="filter-triggers">
        <a class="select-category" :href="category.category.slug" v-for="category in displayedCategoryPanel.children">
            <span class="category"
                  :class="{ 'is-current': (filters['categories'] == category.category.id) }"
                  v-text="category.category.name + ' (' + category.category.productCount + ')'">
            </span>
        </a>
    </div>
</div>
