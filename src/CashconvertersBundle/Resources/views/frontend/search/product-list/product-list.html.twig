<section id="search" class="search-page" v-cloak>

    <div class="container">

        {% macro block(currentCategory, breakpoint) %}
            <div class="category-block visible-{{ breakpoint }}-block">
                <div class="row">
                    <div class="text-center col-xs-10 col-xs-offset-1">
                        <h1>{{ currentCategory.name }}</h1>
                        <span>{{ currentCategory.description|raw }}</span>
                        {% if currentCategory.mainImage|default %}
                            <img class="img-responsive" src="{{ currentCategory.mainImage|image_url }}">
                        {% endif %}
                    </div>
                </div>
            </div>
        {% endmacro %}

        {# HEADER #}
        {# ====== #}
        {% if currentCategory|default %}
            {% import _self as category %}
            {{ category.block(currentCategory, 'xs') }}
        {% endif %}


        {% if currentCategory|default %}
            {% import _self as category %}
            {{ category.block(currentCategory, 'sm') }}
            {{ category.block(currentCategory, 'md') }}
            {{ category.block(currentCategory, 'lg') }}
        {% endif %}

        {# results summary #}
        <div class="results-header">

            {# display search key word if it exists #}
            <div class="search-keyword" v-if="query">«&nbsp;${ query }&nbsp;»</div>

            {# display number of results #}
            <div class="results-numbers">
                <span v-if="pagination.nbResults">${ pagination.nbResults }&nbsp;{{ 'products'|trans|lower }}</span>
                <span v-else>{{ 'no_result'|trans }}</span>

                {# display category if the filter exists #}
                {% if currentCategory|default %}
                    <span v-if="currentCategoryName" v-text="'&nbsp;{{ 'in'|trans }}&nbsp;«&nbsp;' + currentCategoryName + '&nbsp;»'"></span>
                {% endif %}
            </div>
            <hr class="hidden-xs">

            {# top filters #}
            <div class="top-filters hidden-xs">
                <div v-for="(index, facet) in attributesFilters" v-if="index < filterLimit || showAllFilters">
                    <div class="dropdown dropdown-filter">
                        <button class="dropdown-filter-button" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <span>${facet.label}</span>
                            <span class="select-icon glyphicon glyphicon-triangle-bottom"></span>
                        </button>
                        <ul class="dropdown-menu">
                            <li v-for="(valueId, value) in facet.values"
                                @click="toggleFilter(facet.name, valueId, true)"
                                :class="{'selected-filter' : facetIsChecked(facet.name, valueId)}"
                                v-text="value.label"></li>
                        </ul>
                    </div>
                </div>
                <a href="#" @click.prevent="showMoreFilters" v-if="! showAllFilters"><span class="glyphicon glyphicon-menu-down">&nbsp;</span>{{ 'see_more_filters'|trans }}</a>
                <a href="#" @click.prevent="showMoreFilters" v-else><span class="glyphicon glyphicon-menu-up">&nbsp;</span>{{ 'see_less_filters'|trans }}</a>
            </div>
            <hr class="hidden-xs">
        </div>

        {# small screens filter buttons #}
        <div class="xs-filter-buttons visible-xs">
            {# enter refine section #}
            <button class="btn btn-purple refine">{{ 'refine'|trans|upper }}</button>
        </div>

        {# small screens pagination #}
        <div class="visible-xs">
            {% include('@App/frontend/common/pagination.html.twig') %}
        </div>

        {# PRODUCT LIST HEADER (MEDIUM AND LARGE SCREENS)#}
        {# ============================================= #}

        <div class="row hidden-xs">
            {# sidebar title #}
            <div class="col-sm-4">
                <span class="sidebar-title">{{ 'refinesearch'|trans|upper }}</span>
            </div>

            {# pagination #}
            <div class="col-sm-8">
                {# medium and large screens pagination #}
                {% include('@App/frontend/common/pagination.html.twig') %}
            </div>
        </div>


        {# FILTERS AND RESULTS #}
        {# =================== #}

        <div class="row">
            {# sidebar #}
            <div class="hidden-xs col-sm-4">
                {% include('@App/frontend/search/product-list/_sidebar.html.twig') %}
            </div>


            {# RESULTS #}
            {# ======= #}

            <div class="col-xs-12 col-sm-8">

                {% include('@App/frontend/search/product-list/_products.html.twig') %}

                {# pagination (again for user convenience): #}
                {% include('@App/frontend/common/pagination.html.twig') %}
            </div>
        </div>
    </div>

    {# mobile filters (popin windows) #}
    {% include('@App/frontend/search/product-list/mobile/filters.html.twig') %}

    {# price slider template (to be used with Vue) #}
    <template id="slider-template">
        <div class="range-slider">
            <div class="range-slider-wrapper">

                <div id="price-slider"></div>
                <div class="range-values">
                    <span class="min-value" v-text="currentMin + '{{ currencySign }}'"></span>
                    <span class="max-value" v-text="currentMax + '{{ currencySign }}'"></span>
                </div>
            </div>
        </div>
    </template>

    {# geographical distance slider template (to be used with Vue) #}
    <template id="geo-slider-template">
        <div class="range-slider">
            <p>{% trans with {'%radius%': '${ currentMax/1000 }'} %}search_radius{% endtrans %}</p>

            <div class="range-slider-wrapper">

                <div id="distance-slider"></div>
                <div class="range-values">
                    <span class="min-value" v-text="currentMin + '{{ 'km'|trans }}'"></span>
                    <span class="max-value" v-text="currentMax/1000 + '{{ 'km'|trans }}'"></span>
                </div>
            </div>
        </div>
    </template>
</section>

{% include('@App/frontend/search/product-list/_script.html.twig') %}
