<ul class="list-unstyled">
    {% for category in categories %}

        {# category - root level #}
        <li class="branch-node">

            {% if category.children|default %}

                {# category #}
                <span class="category" :class="{ 'is-current': (filters['categories'] == {{ category.category.id }}) }">
                    <span class="glyphicon glyphicon-menu-right"></span>
                    <span class="category-name">{{ category.category.name }} ({{ category.category.productCount }})</span>
                </span>

                {# sub-categories - level 1 #}
                <ul class="branch list-unstyled">
                    {% for category in category.children|default %}
                        <li class="branch-node indented">
                            {% if category.children|default %}

                                {# category #}
                                <span class="category" :class="{ 'is-current': (filters['categories'] == {{ category.category.id }}) }">
                                    <span class="glyphicon glyphicon-menu-right"></span>
                                    <span class="category-name">{{ category.category.name }} ({{ category.category.productCount }})</span>
                                </span>

                                {# sub-categories - level 2 #}
                                <ul class="branch list-unstyled">
                                    {% for category in category.children|default %}
                                        <li class="branch-node indented">

                                            {# category #}
                                            <a
                                                class="leaf-link"
                                                href="{{ path('category', {categoryPath: category.category.slug}) }}"
                                                @click.prevent="buildPath('{{ path('category', {categoryPath: category.category.slug}) }}')">
                                                <span class="category" :class="{ 'is-current': (filters['categories'] == {{ category.category.id }}) }">
                                                    <span class="category-name">{{ category.category.name }} ({{ category.category.productCount }})</span>
                                                </span>
                                            </a>
                                        </li>
                                    {% endfor %}
                                </ul>

                            {% else %}

                                {# category #}
                                <a
                                    class="leaf-link"
                                    href="{{ path('category', {categoryPath: category.category.slug}) }}"
                                    @click.prevent="buildPath('{{ path('category', {categoryPath: category.category.slug}) }}')">
                                    <span class="category" :class="{ 'is-current': (filters['categories'] == {{ category.category.id }}) }">
                                        <span class="category-name">{{ category.category.name }} ({{ category.category.productCount }})</span>
                                    </span>
                                </a>

                            {% endif %}
                        </li>
                    {% endfor %}
                </ul>

            {% else %}

                {# category #}
                <a
                    class="leaf-link"
                    href="{{ path('category', {categoryPath: category.category.slug}) }}"
                    @click.prevent="buildPath('{{ path('category', {categoryPath: category.category.slug}) }}')">
                    <span class="category" :class="{ 'is-current': (filters['categories'] == {{ category.category.id }}) }">
                        <span class="category-name">{{ category.category.name }} ({{ category.category.productCount }})</span>
                    </span>
                </a>

            {% endif %}
        </li>
    {% endfor %}
</ul>
