<div class="search-sidebar">

    {# categories #}
    <div class="filter-block">
        <div class="title">{{ 'search_categories'|trans }}</div>

        <div class="content">
            {% include('@App/frontend/search/product-list/_categories.html.twig') %}
        </div>
    </div>

    {# loop in facets to get price slider #}
    <form class="sort">
    <div v-for="facet in facets" track-by="name">

        {# price #}
        <div class="filter-block" v-if="facet.name == 'price'">
            <div class="title">{{ 'search_price'|trans }}</div>

            <div class="content">

                {# range slider #}
                <price-slider
                        :min="facet.values.min"
                        :max="facet.values.max"
                        @update="updateNumericFilter(facet.name, $arguments[0], $arguments[1])">
                </price-slider>

                {# price sorting #}
                <form class="sort">
                    <label class="radio">
                        <input type="radio" name="sort" v-model="sorting" value="price-asc"><span></span>
                        <span>{{ 'price_asc'|trans }}</span>
                    </label>

                    <label class="radio">
                        <input type="radio" name="sort" v-model="sorting" value="price-desc"><span></span>
                        <span>{{ 'price_desc'|trans }}</span>
                    </label>
                </form>
            </div>
        </div>
    </div>
    <div class="filter-block">
        <div class="title">{{ 'date_order'|trans }}</div>

        <div class="content">
            {# date order #}
            <div class="filter-block">
                <form class="date-sort">
                    <label class="radio">
                        <input type="radio" name="sort" v-model="sorting" value="createdAt-desc"><span></span>
                        <span>{{ 'date_desc'|trans }}</span>
                    </label>
                    <label class="radio">
                        <input type="radio" name="sort" v-model="sorting" value="createdAt-asc"><span></span>
                        <span>{{ 'date_asc'|trans }}</span>
                    </label>
                </form>
            </div>
        </div>
    </div>
    </form>
    {# geographical distance #}
    <div class="filter-block">
        <div class="title"><span class="glyphicon glyphicon-map-marker"></span> {{ 'search_localization'|trans }}</div>

        <div class="content">
            {# geographical distance slider #}
            <geo-slider
                    :min="0"
                    :max="100000"
                    :step="10000"
                    @update="updateGeoFilter($arguments[0])">
            </geo-slider>
        </div>
    </div>

    {# vendor facets #}
    <div v-for="facet in facets" track-by="name">
        <div class="filter-block vendor-facet" v-if="facet.name == 'companies'">
            <div class="title" v-text="facet.label"></div>

            <ul class="content list-unstyled">
                <li class="checkbox" v-for="value in orderFacets(facet.values)" v-if="facet.name='companies'"
                    type="checkbox" @click="toggleCompany(value.key)">
                    <span class="checkbox-image" :class="{ 'checked': facetIsChecked(facet.name, value.key) }"></span>
                    <span v-text="value.label"></span>
                </li>
            </ul>
        </div>
    </div>
</div>
