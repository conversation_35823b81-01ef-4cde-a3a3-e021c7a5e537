{% extends '@App/frontend/layout-search.html.twig' %}

{% set currentCategorySeo = currentCategory.seoData %}

{% block meta_title %}
    {{ currentCategorySeo.title|default(parent()) }}
{% endblock %}

{% block meta %}
    {% if currentCategorySeo.description %}
        <meta name="description" content="{{ currentCategorySeo.description }}" />
    {% else %}
        {{ parent() }}
    {% endif %}
{% endblock %}

{% block content %}

    {# breadcrumb #}
    <div class="container">
        <section class="breadcrumb hidden-xs">
            {# 'go back home' link #}
            <a href="{{ path('home') }}">{{ 'home'|trans }}</a> >

            {# category page link #}
            <a href="{{ path('categories') }}">{{ 'categories'|trans }}</a> >

            {# actual categories breadcrumb #}
            {% for parentCategory in currentCategory.categoriesPath %}
                <a href="{{ cscart_url("categories.view?category_id=" ~ parentCategory.id) }}"
                    class="breadcrumb-item"
                >{{ parentCategory.name }}</a>

                {# add '>' character after each item (but the last one) #}
                {% if not loop.last %} > {% endif %}
            {% endfor %}
        </section>
    </div>

    <div class="search-page">
        {% include '@App/frontend/search/product-list/product-list.html.twig' with {
            filters: {
                'categories': currentCategory.id ~ '',
            },
        } %}
    </div>

{% endblock %}
