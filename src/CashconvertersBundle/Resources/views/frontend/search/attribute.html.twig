{% extends '@App/frontend/layout.html.twig' %}

{% block content %}
    <div class="search-page">

        {# breadcrumb #}
        <section class="breadcrumb">
            <div class="container">
                <ol>
                    <li><a href="{{ path('home') }}">{{ 'home'|trans }}</a></li>
                    <li class="active">{{ variantInfo.variant }}</li>
                </ol>
            </div>
        </section>

        <section class="search-page-header hidden-xs">
            <h2>{{ variantInfo.variant }}</h2>
        </section>

        {% include '@App/frontend/search/product-list/product-list.html.twig' with {
            filters: {
                (variantInfo.feature_id): variantInfo.variant_id,
            },
        } %}
    </div>
{% endblock %}
