{% extends '@App/frontend/layout-search.html.twig' %}
{% set image = company.getImage() %}

{% block content %}
    <section id="vendor-display">
        <div class="container">
            <h1 class="text-center">{{ "vendor"|trans }}</h1>
            <hr class="under-title">

            {# vendor general info #}
            {% include('@App/frontend/search/company/_vendor-info.html.twig') %}

            {# vendor opening hours #}
            {% include('@App/frontend/search/company/_opening-hours.html.twig') %}

            {# vendor location #}
            {% include('@App/frontend/search/company/_vendor-location.html.twig') %}

            {# latests products #}
            {% include('@App/frontend/search/company/_latest-products.html.twig') %}

            {# reviews #}
            {% include('@App/frontend/search/company/_reviews.html.twig') %}
        </div>
    </section>

    {# VueJs object to manage pagination #}
    {% include('@App/frontend/search/company/_script.html.twig') %}

{% endblock %}

{% block googleMapScript %}
    <script
        src="https://maps.googleapis.com/maps/api/js?key={{ googleMapsApiKey }}&libraries=places&callback=initMap">
    </script>
{% endblock %}
