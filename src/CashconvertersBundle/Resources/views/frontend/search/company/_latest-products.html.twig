<div class="products col-md-12">
    <h3>{{ 'latest_products'|trans }}</h3>

    <div class="products-slideshow" class="col-xs-10 col-xs-offset-1">
        {% for product in latestProducts %}
            <div class="col-md-2 product-card-container text-center">
                <a href="{{ product|product_url }}">
                    <div class="row">
                        {% if product.mainImage is defined and product.mainImage is not empty %}
                            <div class="row"><img data-lazy="{{ product.mainImage|image_url(92, 92) }}" style="width: 92px; height: 92px;"></div>
                        {% else %}
                            <div class="row"><img data-lazy="{{ asset('images/no-image.jpg') }}" style="width: 92px; height: 92px;"></div>
                        {% endif %}

                        <span class="prod-title truncated">{{ product.name }}</span><br>
                        <span class="price-before">{{ 'from'|trans }}</span><br><span class="price"> {{ product.minimumPrice|money }}</span>
                    </div>
                </a>
            </div>
        {% endfor %}
    </div>
</div>

<hr class="col-md-12">
