<div class="location col-md-12">
    <h3>{{ 'contact_info'|trans }}</h3>

    <div>
        <span class="glyphicon glyphicon-home"></span>
        <span>{{ company.address }} - {{ company.zipcode }} {{ company.city }}</span><br>
        <span class="glyphicon glyphicon-phone"></span> <span>{{ company.phoneNumber }}</span><br>
        <span class="glyphicon glyphicon-print"></span> <span>{{ company.faxNumber }}</span><br>
        <span class="glyphicon glyphicon-envelope"></span> <span class="email-rtl">{{ company.email|reverse }}</span>
    </div>

    <div class="merchant-map">
        {# GOOGLE MAP #}
        {% if company.latitude is not null and company.longitude is not null %}
            <div id="map"></div>
            <script>
                function initMap() {
                    var coordinates = {lat: {{ company.latitude }}, lng: {{ company.longitude }}};
                    var map = new google.maps.Map(document.getElementById('map'), {
                        zoom: 18,
                        center: coordinates,
                    });
                    new google.maps.Marker({
                        position: coordinates,
                        map: map,
                        title: {{ company.name|json_encode|raw }},
                    });
                }
            </script>
        {% else %}
            <script>
                function initMap() { }
            </script>
        {% endif %}
        {#// GOOGLE MAP #}

        <a id="go-button" href="http://maps.google.com/?q={{ (company.address ~ ' ' ~ company.zipcode)|e('url') }}"
           target="_blank" class="btn merchant-btn pink">
            {{ "go_to_store"|trans|upper }}
        </a>
    </div>
</div>

<hr class="col-md-12">
