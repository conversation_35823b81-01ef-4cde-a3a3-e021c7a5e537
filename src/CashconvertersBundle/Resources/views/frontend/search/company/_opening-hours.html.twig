<div class="hours">
    <div class="col-md-12 text-left">
        <h3>{{ "hours"|trans }}</h3>
    </div>

    <div class="row">
        {# sales #}
        <div class="col-xs-12 col-md-6">
            <table class="sales-opening-hours">
                <thead>
                    <tr>
                        <th></th>
                        <th>{{ 'shop'|trans|upper }}</th>
                        <th></th>
                    </tr>
                </thead>

                <tbody>
                    {% for i in 0..6 %}
                        <tr>
                            <td class="weekday">{{ ('week_day_'~i)|trans|upper|e('js') }}</td>
                            <td>
                                {% for period in attribute(salesOpeningHours, i) %}
                                    {{ period.opens|date('H\\hi') }}
                                    -
                                    {{ period.closes|date('H\\hi') }}
                                    {% if not loop.last %}
                                        /
                                    {% endif %}
                                {% else %}
                                    {{ 'closed_store'|trans }}
                                {% endfor %}
                            </td>
                            {% if i == currentDay %}
                                <td class="glyphicon {{ isSalesCurrentlyOpen ? 'is-open' : 'is-closed' }}"></td>
                            {% else %}
                                <td></td>
                            {% endif %}
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        {# purchase #}
        <div class="col-xs-12 col-md-6">
            <table class="purchase-opening-hours">
                <thead>
                    <tr>
                        <th class="hidden-md hidden-lg"></th>
                        <th>{{ 'purchase_room'|trans|upper }}</th>
                        <th></th>
                    </tr>
                </thead>

                <tbody>
                    {% for i in 0..6 %}
                        <tr>
                            <td class="weekday hidden-md hidden-lg">{{ ('week_day_'~i)|trans|upper|e('js') }}</td>
                            <td>
                                {% for period in attribute(purchaseOpeningHours, i) %}
                                    {{ period.opens|date('H\\hi') }}
                                    -
                                    {{ period.closes|date('H\\hi') }}
                                    {% if not loop.last %}
                                        /
                                    {% endif %}
                                {% else %}
                                    {{ 'closed_store'|trans }}
                                {% endfor %}
                            </td>
                            {% if i == currentDay %}
                                <td class="glyphicon {{ isPurchaseCurrentlyOpen ? 'is-open' : 'is-closed' }}"></td>
                            {% else %}
                                <td></td>
                            {% endif %}
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    {% if isPurchaseCurrentlyOpen and isSalesCurrentlyOpen %}
        <p><span class="glyphicon is-open"></span>{{ 'sales_and_purchase_open'|trans }}</p>
    {% elseif isSalesCurrentlyOpen %}
        <p><span class="glyphicon is-open"></span>{{ 'sales_open'|trans }}</p>
    {% endif %}
</div>

<hr class="col-md-12">
