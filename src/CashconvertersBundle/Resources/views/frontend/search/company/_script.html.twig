<script>
    $(function() {

        {# reviews pagination #}
        var vm = new Vue({
            el: '#company-rating',

            data: {
                reviews: [],

                // defaults values for pagination
                pagination: {
                    page: 1,
                    resultsPerPage: 4
                },

                mobilePageNumbersConstraint: 5
            },

            computed: {

                // pagination - offset
                // calculate how many reviews to skip regarding current page
                offset: function() {
                    return ((this.pagination.page - 1) * this.pagination.resultsPerPage);
                },

                // pagination - total number of pages regarding the number of reviews and the number of results per page requested
                totalPages: function() {
                    return Math.ceil(this.reviews.length / this.pagination.resultsPerPage);
                }
            },

            methods: {
                goToPage: function (page) {
                    page = Math.max(1, page); // cannot go below page 1
                    page = Math.min(this.totalPages, page); // cannot go over total number of pages

                    this.pagination.page = page;
                },

                togglePageList: function(e) {
                    var $target = $(e.currentTarget);
                    $target.toggleClass('is-open');
                },

                // helper method
                range: function (a, b) {
                    var rangeArray = [];

                    for(var i = a; i < b; i++) {
                        rangeArray.push(i);
                    }

                    return rangeArray;
                }
            },

            ready: function() {
                this.reviews = {{ reviews|json_encode|raw }};
            },

            filters: {
                date: function(rawDate) {
                    return moment(rawDate).format('L');
                }
            }
        });
    });
</script>
