<div class="comments col-xs-12" id="company-rating">
    <h3 id="reviews">{{ 'client_comments'|trans }}</h3><br>

    {% if reviews|length > 0 %}
        <div class="review" v-for="review in reviews | limitBy pagination.resultsPerPage offset">

            {# rating #}
            <div class="rating">
                <span class="rank" v-for="i in range(1, 6)">
                    <i class="glyphicon glyphicon-star on" v-if="i <= review.ratingValue"></i>
                    <i class="glyphicon glyphicon-star off" v-else></i>
                </span>
            </div>

            <p>{{ 'by'|trans }} <span class="emphasis" v-text="review.name"></span> {{ "published"|trans }} <span class="emphasis" v-text="review.datetime.date|date"></span></p>
            <div v-text="review.message"></div>
        </div>

        {# pagination #}
        {% include('@App/frontend/common/pagination.html.twig') %}

    {% else %}
        <div class="text-muted">{{ 'company_no_comment'|trans }}</div>
    {% endif %}
</div>

<div class="col-xs-12">
    {% if user is not null and hasOrder %}
        <h3 id="add-review">{{ 'add_client_comments'|trans }}</h3><br>
        <div class="row">
            <div class="col-md-6">
                <form name="add-reviews" method="post" action="{{ path('home') }}">
                    <input type="hidden" name="redirect_url" value="{{ app.request.uri }}">
                    <input type="hidden" name="post_data[thread_id]" value="{{ thread.id }}">
                    <input type="hidden" name="post_data[name]" value="{{ user.fullname }}" />
                    <div class="form-group">
                        <label for="review-rate" class="control-label text-right w-label">{{ 'rating'|trans }}</label>
                        <input id="review-rate" name="post_data[rating_value]" class="js-rating form-control hide" value="3" />
                    </div>
                    <div class="form-group">
                        <label for="review-message" class="control-label text-right w-label">{{ 'message'|trans }}</label>
                        <textarea id="review-message" name="post_data[message]" class="form-control" rows="5" cols="72"></textarea>
                    </div>
                    <input type="submit" class="btn merchant-btn pink" name="dispatch[discussion.add]" value="{{ 'send'|trans|upper }}" />
                </form>
            </div>
        </div>
    {% endif %}
</div>
