{% extends '@App/frontend/layout-search.html.twig' %}

{% block meta_title %}{{ 'search_page_title'|trans({ '%q%': searchQuery }) }}{% endblock %}

{% block content %}

    {# breadcrumb #}
    <div class="container">
        <section class="breadcrumb hidden-xs">

            {# 'go back home' link #}
            <a href="{{ path('home') }}">{{ 'home'|trans }}</a> >

            {# category page link #}
            <a href="{{ path('categories') }}">{{ 'categories'|trans }}</a> >

            <b>{{ "search"|trans }}</b>
        </section>
    </div>

    <div class="search-page">
        {% include '@App/frontend/search/product-list/product-list.html.twig' with {
            filters: filters
        } %}
    </div>

{% endblock %}
