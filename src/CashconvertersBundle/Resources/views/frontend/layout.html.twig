<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>{% block meta_title %}{{ 'page_title_default'|trans }}{% endblock %}</title>

    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','GTM-WTJW944');</script>
    <!-- End Google Tag Manager -->

    <link rel="shortcut icon" type="image/x-icon" href="{{ asset('images/favicon.ico') }}" />
    <link rel="icon" type="image/x-icon" href="{{ asset('images/favicon.ico') }}" />
    <link rel="apple-touch-icon" sizes="192x192" href="{{ asset('images/apple-icon.png') }}" />
    <link rel="icon" type="image/png" sizes="192x192" href="{{ asset('images/android-icon.png') }}" />
    {% block meta %}
        <meta name="description" content="{{ 'meta_description_default'|trans }}" />
    {% endblock %}

    <link rel="canonical" href="https://www.cashconverters.fr{{ app.request.pathInfo }}{% if app.request.query.has('page') %}?page={{ app.request.query.get('page') }}{% endif %}" />

    {{ includeStyle() }}
    {% if not constant('DEVELOPMENT')|default(false) %}
        <script type="text/javascript">
            window.RY=(function(e){var t=["identify","track","trackLink","trackForm","transaction","page","profile","sync"];var n="realytics";var r=function(e){return!!(e&&(typeof e=="function"||typeof e=="object"))};var i=function(e,t){return function(){var n=Array.prototype.slice.call(arguments);if(!e[t])e[t]=[];e[t].push(n?n:[]);if(!e["_q"])e["_q"]=[];e["_q"].push(t)}};var s=function(r){for(var s=0;s < t.length;s++){var o=t[s];if(r)e[r][o]=i(e._q[r],o);else e[o]=e[n][o]=i(e._q[n],o)}};var o=function(t,r,i){var o=t?t:n;if(!e[o])e[o]={};if(!e._q[o])e._q[o]={};if(r)e._q[o]["init"]=[[r,i?i:null]];s(t)};if(!e._v){if(!e._q){e._q={};o(null,null,null)}e.init=function(e,n){var i=n?r(n)?n["name"]?n["name"]:null:n:null;if(i&&t)for(var s=0;s < t.length;s++)if(i==t[s]||i=="init")return;o(i,e,r(n)?n:null);var u=function(e){var t=document.createElement("script");t.type="text/javascript";t.async=true;t.src=("https:"==document.location.protocol?"https://":"http://")+e;var n=document.getElementsByTagName("script")[0];n.parentNode.insertBefore(t,n)};u("i.realytics.io/tc.js?cb="+(new Date).getTime());u("cdn-eu.realytics.net/realytics-1.2.min.js")}}return e})(window.RY||{});
            RY.init("ry-c4shc0nv");
            RY.page();
        </script>
    {% endif %}

    {% block realytics %}{% endblock %}

    <script src="//code.jquery.com/jquery-2.1.3.min.js"></script>
    <script type="text/javascript">
        /**
         * Stores analytics data.
         * Can be used for example to post advanced data to Google Analytics, Tag Commander, etc.
         */
        var analytics = {};
    </script>
</head>
<body>
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-WTJW944"
                      height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->
    {% block header %}{{ include("@App/frontend/common/header.html.twig") }}{% endblock %}

    <div class="container">
        {% include '@App/frontend/common/notifications.html.twig' %}
    </div>

    {% block content %}{% endblock %}

    {% block footer %}{{ include("@App/frontend/common/footer.html.twig") }}{% endblock %}

    {% block scripts %}
        <script type="text/javascript" src="//cdnjs.cloudflare.com/ajax/libs/URI.js/1.11.2/URI.min.js"></script>
        <script type="text/javascript" src="//cdnjs.cloudflare.com/ajax/libs/vue/1.0.25/vue.min.js"></script>
        <script src="//cdnjs.cloudflare.com/ajax/libs/mobile-detect/1.4.1/mobile-detect.min.js"></script>
        {{ includeScripts() }}
        <script src="{{ asset('bundles/fosjsrouting/js/router.js') }}"></script>
        <script src="{{ path('fos_js_routing_js', { callback: 'fos.Router.setData' }) }}"></script>
        {{ include('@App/frontend/common/cookie-consent.html.twig') }}
        {{ include('@App/frontend/common/google-analytics.html.twig') }}
    {% endblock %}

    {% block googleMapScript %}
        <script src="//maps.googleapis.com/maps/api/js?key={{ googleMapsApiKey }}&libraries=places"></script>
    {% endblock %}

    {# Required for modal loading (for order info among others) #}
    {{ include("@App/frontend/common/generic-modal.html.twig") }}

    {# custom overlay (starts off hidden) #}
    <div class="global-overlay">&nbsp;</div>
</body>
</html>
