{% for categoryData in categories %}
{% set category = categoryData.category %}
{% set products = categoryData.products %}
{# desktop + tablet #}
<div class="desktop-category-container row hidden-xs">

    {# Title bar #}
    <div class="category-top-bar">

        {# Title + count #}
        <div class="category-title-container">
            <a href="{{ cscart_url("categories.view?category_id=" ~ category.id) }}"><h3>{{ category.name|upper }}</h3></a>
            <span class="vert-delim">&#124;</span>
            <a href="{{ cscart_url("categories.view?category_id=" ~ category.id) }}"><span>{{ categoryData.totalProductCount~' objets' }}</span></a>
        </div>

        {# Buy + Sell buttons #}
        <div class="category-buttons col-xs-6 col-sm-4 col-md-3">
            <a href="{{ cscart_url("categories.view?category_id=" ~ category.id) }}" class="col-xs-5 btn trade-btn black">{{ 'buy'|trans }}</a>
            <div class="col-xs-2 text-center"><span class="vert-delim">&#124;</span></div>
            <a href="{{ 'sell_mine_url'|trans }}" class="col-xs-5 btn trade-btn">{{ 'sell'|trans }}</a>
        </div>

    </div>

    <hr class="category-title-separator">

    <div class="featured-category">

        {# Main image #}
        <div class="featured-category-image">
            <a href="{{ cscart_url("categories.view?category_id=" ~ category.id) }}">
                <img class="category-image" src="{{ category.image|image_url(590) }}">
            </a>
        </div>

        {# Featured category products (desktop) #}
        <div class="hidden-xs hidden-sm hidden-md featured-category-products">
            {% for product in products|slice(0, 6) %}

                <a href="{{ product|product_url }}" class="product-card">
                    <img src="{{ product.mainImage|image_url(95, 95) }}" class="product-image">
                    <div>{{ product.categoryPath|first.name|upper }}</div>
                    <div class="ellipsis">{{ product.name }}</div>
                    <div class="price-block">
                        <span>{{ 'from'|trans }}</span>
                        <span class="price"> {{ product.minimumPrice|money }}</span>
                    </div>
                </a>

            {% endfor %}
        </div>

        {# Featured category products - right column (tablet) #}
        <div class="hidden-xs hidden-lg featured-category-products">
            {% for product in products|slice(0, 2) %}

                <a href="{{ product|product_url }}" class="product-card">
                    <img src="{{ product.mainImage|image_url(150, 150) }}" class="product-image">
                    <div>{{ product.categoryPath|first.name|upper }}</div>
                    <div class="ellipsis">{{ product.name }}</div>
                    <div>
                        <span>{{ 'from'|trans }}</span>
                        <span class="price"> {{ product.minimumPrice|money }}</span>
                    </div>
                </a>

            {% endfor %}
        </div>
    </div>

    {# Featured category products - under column (tablet) #}
    <div class="featured-category featured-category-row">
        <div class="hidden-xs hidden-lg featured-category-products">
            {% for product in products|slice(2, 4) %}

                <a href="{{ product|product_url }}" class="product-card">
                    <img src="{{ product.mainImage|image_url(150, 150) }}" class="product-image">
                    <div>{{ product.categoryPath|first.name|upper }}</div>
                    <div class="ellipsis">{{ product.name }}</div>
                    <div>
                        <span>{{ 'from'|trans }}</span>
                        <span class="price">{{ product.minimumPrice|money }}</span>
                    </div>
                </a>

            {% endfor %}
        </div>
    </div>

    {# Category description #}
    <div class="row">
        <div class="category-description col-xs-12 wysiwyg-output">
        <span>
            {% if category.description is not empty %}
                {{ category.description | raw }}
            {% endif %}
        </span>
        </div>
    </div>

    {# display subcategories #}
    {% if categoryData.children|default %}
        <div class="subcategories">
            {# 5 items / list are displayed #}
            {% set length = 5 %}
            {# 5 lists are displayed (the remaining ones are hidden) #}
            {% for i in 0..4 %}
                {# the last list should only have 4 items and a "show_categories" link as fifth item #}
                {% if loop.last %}{% set length = 4 %}{% endif %}
                <ul class="subcategories-list">
                    {% for child in categoryData.children|slice(i*5,length) %}
                        <li><a href="{{ cscart_url("categories.view?category_id=" ~ child['category'].id) }}">{{ child['category'].name|upper|truncate(20) }}</a></li>
                    {% endfor %}
                    {% if length == 4 and categoryData.children|slice(i*5,length) is not empty %}
                        <li><a href="{{ cscart_url("categories.view?category_id=" ~ category.id) }}">{{ 'show_categories'|trans|upper }}</a></li>
                    {% endif %}
                </ul>
            {% endfor %}
        </div>
    {% endif %}

</div>

{% endfor %}

{# Mobile view #}
{% for categoryData in categories %}
{% set category = categoryData.category %}
<div class="mobile-category-container row visible-xs">
    <div class="mobile-single-cat {% if loop.last %}last{% endif %} col-xs-10 col-xs-offset-1">
        <a href="{{ cscart_url("categories.view?category_id=" ~ category.id) }}">
            <div class="col-xs-2">
                <img src="{{ asset('images/icons/mob-img-cat-0'~loop.index~'.png') }}">
            </div>
            <div class="col-xs-1"></div>
            <div class="col-xs-8">
                <span class="truncated">{{ category.name|trans }}</span>
            </div>
            <div class="col-xs-1">
                <span class="glyphicon glyphicon-menu-right"></span>
            </div>
        </a>
    </div>
</div>
{% endfor %}
