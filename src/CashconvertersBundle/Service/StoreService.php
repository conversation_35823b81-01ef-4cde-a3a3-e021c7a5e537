<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\CashconvertersBundle\Service;

use GuzzleHttp\Client;
use Tygh\Backend\Cache\ABackend;

class StoreService
{
    /**
     * @var Client Guzzle
     */
    private $httpClient;

    /**
     * @var array
     * Keep a local cache to prevent 2 hits to Cache backend when calling a method 2 times on the same request
     */
    private $storeInformationEphemeralCache = [];

    /**
     * @var ABackend
     */
    private $cache;

    /**
     * @var string
     */
    private $apiMarketplaceIdentifier;

    public function __construct(ABackend $cache, string $apiUrl, string $apiLogin, string $apiPassword, string $apiMarketplaceIdentifier)
    {
        $this->httpClient = new Client([
            'base_uri' => $apiUrl,
            'auth' => [$apiLogin, $apiPassword],
            'connect_timeout' => 5,
            'read_timeout' => 5,
            'timeout' => 5,
        ]);

        $this->apiMarketplaceIdentifier = $apiMarketplaceIdentifier;
        $this->cache = $cache;
    }

    /**
     * @return array An array with keys from 0 to 6 (Mon to Sun) with values = an array containing periods.
     * Each period is an array with two keys: opens / closes. The values are \DateTime objects
     * If there is no periods in the array, the store is closed this day.
     * If there is one period, it can be a non stop opening or just a half of the day
     * If there is two periods, this is morning / afternoon
     */
    public function getPurchaseOpeningHours(int $storeId): array
    {
        return $this->getOpeningHours($storeId, 'purchase');
    }

    /**
     * @see getPurchaseOpeningHours
     */
    public function getSalesOpeningHours(int $storeId): array
    {
        return $this->getOpeningHours($storeId, 'sales');
    }

    public function isPurchaseCurrentlyOpen(int $storeId): bool
    {
        return $this->isCurrentlyOpen($storeId, 'purchase');
    }

    public function isSalesCurrentlyOpen(int $storeId): bool
    {
        return $this->isCurrentlyOpen($storeId, 'sales');
    }

    /**
     * @param string $type Must be sales or purchase
     */
    private function getOpeningHours(int $storeId, string $type): array
    {
        $data = $this->getStoreInformation($storeId)[$type . '_opening_hours'];

        // Generate array with keys 0 .. 6 with [] value
        $result = array_fill(0, 7, []);

        foreach ($data as $line) {
            $result[(int) $line['day_of_week']][] = [
                'opens' => \DateTime::createFromFormat('H:i:s', $line['opens']),
                'closes' => \DateTime::createFromFormat('H:i:s', $line['closes']),
            ];
        }

        // Ensure periods are sorted (ASC)
        foreach ($result as &$periods) {
            usort($periods, function ($previousPeriod, $period) {
                return $previousPeriod['opens'] <=> $period['opens'];
            });
        }

        return $result;
    }

    /**
     * @param string $type Must be sales or purchase
     */
    private function isCurrentlyOpen(int $storeId, string $type): bool
    {
        $data = $this->getOpeningHours($storeId, $type);
        $today = $data[(int) date('N') - 1];
        $now = new \DateTime();

        foreach ($today as $period) {
            if ($now >= $period['opens'] && $now < $period['closes']) {
                return true;
            }
        }

        return false;
    }

    private function getStoreInformation(int $storeId): array
    {
        // First, check ephemeral cache
        if (!empty($this->storeInformationEphemeralCache[$storeId])) {
            return $this->storeInformationEphemeralCache[$storeId];
        }

        // Second, check Redis
        $this->storeInformationEphemeralCache[$storeId] = $this->cache->get('cashconverters_store_hours_' . $storeId)[0] ?? [];

        // Third, call API
        if (empty($this->storeInformationEphemeralCache[$storeId])) {
            $response = $this->httpClient->get('stores/' . $storeId, ['query' => ['marketplace' => $this->apiMarketplaceIdentifier]]);
            $this->storeInformationEphemeralCache[$storeId] = (array) json_decode((string) $response->getBody(), true);

            $this->cache->set(
                'cashconverters_store_hours_' . $storeId,
                $this->storeInformationEphemeralCache[$storeId],
                3600 // 1 hour expiry
            );
        }

        return $this->storeInformationEphemeralCache[$storeId];
    }
}
