<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\CashconvertersBundle\Service;

use GuzzleHttp\Client;

class LoyaltyService
{
    /**
     * @var Client
     */
    private $httpClient;

    public function __construct(string $apiUrl, string $apiLogin, string $apiPassword)
    {
        $this->httpClient = new Client([
            'base_uri' => $apiUrl,
            'auth' => [$apiLogin, $apiPassword],
            'connect_timeout' => 5,
            'read_timeout' => 5,
            'timeout' => 5,
        ]);
    }

    /**
     * @throws \GuzzleHttp\Exception\ClientException Wrong identifier
     */
    public function getLoyaltyPoints(string $id, string $email): int
    {
        $response = $this->httpClient->get('loyalty_cards/' . $id, ['query' => ['email' => $email]]);

        $response = json_decode((string) $response->getBody(), true);

        return $response['number_of_points'] ?? 0;
    }
}
