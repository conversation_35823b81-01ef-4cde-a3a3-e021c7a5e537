<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha;

/**
 * @deprecated Legacy class.
 */
class Page
{
    /**
     * @param string $w_type
     * @return int
     */
    public static function getIdByWType($w_type)
    {
        $result = \Tygh\Database::getField("SELECT page_id FROM ?:pages WHERE status = 'A' AND w_type = ?s LIMIT 0,1;", $w_type);

        if (!$result) {
            return '';
        }

        return $result;
    }
}
