<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha;

use Psr\Log\LoggerInterface;

/**
 * @deprecated Use the logger of the container instead.
 */
class Logger
{
    protected $company_id;
    protected $user_id;
    protected $messages = [];
    protected $type;
    protected $action;
    protected $timestamp;
    protected $logger;

    /**
     * @param integer $company_id
     * @param integer $user_id
     * @param string $type
     * @param string $action
     * @param \DateTime $dateTime
     */
    public function __construct($company_id, $user_id, $type, $action, $dateTime = null, LoggerInterface $logger = null)
    {
        if (!($dateTime instanceof \DateTime)) {
            $dateTime = new \DateTime();
        }
        $this->timestamp = $dateTime->getTimestamp();
        $this->company_id = \intval($company_id);
        $this->user_id = \intval($user_id);
        $this->type = $type;
        $this->action = $action;
        $this->logger = $logger ?: Registry::defaultInstance()->container->get('logger');
    }

    /**
     * @param string $key
     * @param string $value //CsCart can't display array in log.
     */
    public function addMessage($key, $value)
    {
        $this->messages[$key][] = (string) $value;
    }

    /**
     * @return array
     */
    public function getMessages()
    {
        return $this->messages;
    }

    /**
     * Save message in database
     */
    public function log()
    {
        if (empty($this->messages)) {
            return;
        }
        $content = array_map(
            function (&$message) {
                return implode(' ; ', $message);
            },
            $this->messages
        );

        $row = [
            'user_id' => $this->user_id,
            'company_id' => $this->company_id,
            'timestamp' => $this->timestamp,
            'content' => $content,
        ];
        $this->logger->info("{$this->type}.{$this->action}", $row);
        $this->messages = [];
    }

    /**
     * Saved unlogged message.
     */
    public function __destruct()
    {
        $this->log();
    }
}
