<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\BlockManager;

/**
 * @package Wizacha\BlockManager
 */
class RenderManager
{
    public const KEY_CACHE_EXTEND         = 'cache_extend';
    public const KEY_CACHE_NAME_TYPE      = 'cache_name_type';
    public const VALUE_CACHE_NAME_STATIC  = 'static';
    public const VALUE_CACHE_NAME_DYNAMIC = 'dynamic';

    /**
     * Permit cache handler over fillings instead over block type.
     * @param array $block_scheme
     * @param array $block
     * @return array
     */
    public static function rebuildScheme(array $block_scheme, array $block)
    {
        if (!empty($block['content']['items']['filling'])) {
            $filling = $block['content']['items']['filling'];

            $has_filling_cache = !empty($block_scheme['content']['items']['fillings'][$filling]['cache']);
            $is_extended_global_cache = !empty($block_scheme['content']['items']['fillings'][$filling][self::KEY_CACHE_EXTEND]);

            if ($has_filling_cache) {
                if ($is_extended_global_cache) {
                    $block_scheme['cache'] = array_merge_recursive(
                        $block_scheme['cache'],
                        $block_scheme['content']['items']['fillings'][$filling]['cache']
                    );
                    foreach ($block_scheme['cache'] as &$value) {
                        if (\is_array($value)) {
                            $value = array_unique($value);
                        }
                    }
                } else {
                    $block_scheme['cache']  = $block_scheme['content']['items']['fillings'][$filling]['cache'];
                }
            }
        }
        return $block_scheme;
    }

    /**
     * Permit a non dynamic block (same cache for different object_type/object_id
     * @param array $block
     * @param array $block_scheme
     * @return string
     */
    public static function generateBlockCacheName($block_scheme, $block)
    {
        if (empty($block_scheme['cache'][self::KEY_CACHE_NAME_TYPE])
            || $block_scheme['cache'][self::KEY_CACHE_NAME_TYPE] == self::VALUE_CACHE_NAME_DYNAMIC
        ) {
            return 'block_content_' . $block['block_id'] . '_' . $block['snapping_id'] . '_' . $block['type'] . '_' . $block['grid_id'] . '_' . $block['object_id'] . '_' . $block['object_type'];
        } elseif ($block_scheme['cache'][self::KEY_CACHE_NAME_TYPE] == self::VALUE_CACHE_NAME_STATIC) {
            return 'block_content_' . $block['block_id'] . '_' . $block['snapping_id'] . '_' . $block['type'] . '_' . $block['grid_id'];
        }
    }
}
