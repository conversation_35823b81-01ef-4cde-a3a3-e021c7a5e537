<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha;

/**
 * Various static utilities around arrays to complement the standard library
 */
class ArrayUtils
{
    /**
     * Creates an array of elements split into two groups,
     * the first of which contains elements predicate returns truthy for,
     * the second of which contains elements predicate returns falsey for.
     * The predicate is invoked with one argument: (value).
     *
     * @param array $array
     * @param callable $callback
     * @return array
     */
    public static function partition(array $array, callable $callback): array
    {
        $truthy = [];
        $falsy = [];

        foreach ($array as $item) {
            if ($callback($item)) {
                $truthy[] = $item;
            } else {
                $falsy[] = $item;
            }
        }

        return [$truthy, $falsy];
    }
}
