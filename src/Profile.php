<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha;

use Wizacha\Marketplace\User\UserTitle;

/**
 * @deprecated Legacy class. Should be moved into a proper service.
 */
class Profile
{
    public static function correlateProfileDatas(&$user_data, $magical_merge = true)
    {
        // BC old title fields
        if (isset($user_data['fields'][36])) {
            if (empty($user_data['title'])) {
                $user_data['title'] = $user_data['fields'][36] == 1 ? UserTitle::MR : ($user_data['fields'][36] == 2 ? UserTitle::MRS : '');
            }
            unset($user_data['fields'][36]);
        }

        if (isset($user_data['fields'][37])) {
            if (empty($user_data['b_title'])) {
                $user_data['b_title'] = $user_data['fields'][37] == 4 ? UserTitle::MR : ($user_data['fields'][37] == 3 ? UserTitle::MRS : '');
            }
            unset($user_data['fields'][37]);
        }

        if (isset($user_data['fields'][38])) {
            if (empty($user_data['s_title'])) {
                $user_data['s_title'] = $user_data['fields'][38] == 4 ? UserTitle::MR : ($user_data['fields'][38] == 3 ? UserTitle::MRS : '');
            }
            unset($user_data['fields'][38]);
        }

        // BC old company fields
        if (isset($user_data['fields'][39])) {
            if (empty($user_data['b_company'])) {
                $user_data['b_company'] = $user_data['fields'][39];
            }
            unset($user_data['fields'][39]);
        }

        if (isset($user_data['fields'][40])) {
            if (empty($user_data['s_company'])) {
                $user_data['s_company'] = $user_data['fields'][40];
            }
            unset($user_data['fields'][40]);
        }

        $profile_fields = fn_get_profile_fields();

        $mapping = [];

        foreach (['C','B'] as $section) {
            foreach ($profile_fields[$section] as $field) {
                $_data = &$mapping[$section][$field['description']];

                $_data['field_type'] = $field['field_type'];
                switch ($field['field_type']) {
                    case 'R':
                    case 'I':
                        $_data['ref'] = &$user_data[$field['field_name']];
                        break;
                    default:
                        break;
                }
            }
        }

        if ($magical_merge) {
            $intersection = array_intersect_key($mapping['C'], $mapping['B']);
            foreach ($intersection as $desc => $field) {
                $from = null;
                $to = null;

                if (empty($mapping['C'][$desc]['ref'])
                    && !empty($mapping['B'][$desc]['ref'])
                ) {
                    $from = 'B';
                    $to = 'C';
                } elseif (empty($mapping['B'][$desc]['ref'])
                    && !empty($mapping['C'][$desc]['ref'])
                ) {
                    $from = 'C';
                    $to = 'B';
                }

                if ($from && $to) {
                    $value = $mapping[$from][$desc]['ref'];
                    $mapping[$to][$desc]['ref'] = $value;
                }
            }
        }

        $user_data = \Wizacha\Misc::array_filter_recursive(
            $user_data,
            function ($field) {
                return !\is_null($field);
            }
        );
    }
}
