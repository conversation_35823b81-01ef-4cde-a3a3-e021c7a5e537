<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha;

use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Money\Money;

/**
 * @deprecated Its methods should be moved somewhere less random than a "Misc" class
 */
class Misc
{

    public const MAX_SEO_INDEX  = 30000;
    public const KEY_TEMP_FILES = 'temp_fs_data';

    protected static function getAllowedTags($area)
    {
        $common_tags = '
<p>
<sup>
<pre>
<blockquote>
<div>
<sub>
<code>
<h1>
<h2>
<h3>
<h4>
<h5>
<h6>
<span>
<strong>
<em>
<ol>
<ul>
<li>
<table>
<thead>
<tbody>
<td>
<tr>
<th>
<br>';
        return $area == 'A' ? $common_tags . '<img>' : $common_tags;
    }

    public static function getAuthorizedHTMLAttribute()
    {
        return [
            'alt' => true,
            'src' => true,
            'href' => true,
            'height' => true,
            'width' => true,
            //values are used as pattern in regex
            'style' => '/(' . implode('|', [
                ';',
                ' ',
                'display',
                ':',
                'margin',
                '-',
                'left',
                'right',
                'top',
                'bottom',
                'auto',
                'block',
                'text-align',
                'center',
                'left',
                'right',
                'justify',
                'font-size',
                '\d+pt', //all font size
                'text-decoration',
                'underline',
            ]) . ')/',
        ];
    }

    /**
     * Encode data to Json, but ensure that float without floating part
     * won't be encoded like an integer (1.0 for example)
     * @param mixed $data
     * @return string
     */
    public static function jsonEncode($data)
    {
        //Encode float as a special string
        $replaceSpecialChars = false;
        if (\is_array($data)) {
            array_walk_recursive($data, function (&$v) use (&$replaceSpecialChars) {
                // Misc::jsonEncode is only used for legacyApi. In this context we want Money object as float
                // instead of exposing internals objects
                if ($v instanceof Money) {
                    $v = $v->getConvertedAmount();
                }
                if (\is_float($v)) {
                    $s = (string) $v;
                    if (false === strpos($s, '.')) {
                        $v = "#!#$s.0#!#";
                        $replaceSpecialChars = true;
                    }
                }
            });
        }
        //Remove special flags, ton keep only real floats
        $encoded = json_encode($data);
        return $replaceSpecialChars ?
            str_replace(['"#!#', '#!#"'], ['', ''], $encoded)
            : $encoded;
    }

    /**
     * @param array $input
     * @param callable|null $callback
     * @return array
     *
     * Based on php basic array_filter. Name is not in camel case, to be consistent with array_walk / array_walk_recursive, etc...
     */
    public static function array_filter_recursive(array $input, callable $callback = null)
    {
        foreach ($input as &$value) {
            if (\is_array($value)) {
                if ($callback) {
                    $value = self::array_filter_recursive($value, $callback);
                } else {
                    $value = self::array_filter_recursive($value);
                }
            }
        }

        if ($callback) {
            return array_filter($input, $callback);
        }
        return array_filter($input);
    }

    /**
     * Recursively merges array2 into array1
     * See http://php.net/manual/en/function.array-merge-recursive.php#92195
     */
    public static function arrayMergeRecursiveDistinct(array $array1, array $array2): array
    {
        $merged = $array1;

        foreach ($array2 as $key => &$value) {
            if (\is_array($value) && isset($merged[$key]) && \is_array($merged[$key])) {
                $tmp = array_merge($merged[$key], $value);
                if (\count($tmp) === \count($merged[$key]) + \count($value)) {
                    // That means the array is numeric, so we do a normal merge
                    $merged[$key] = $tmp;
                } else {
                    $merged[$key] = self::arrayMergeRecursiveDistinct($merged[$key], $value);
                }
            } else {
                $merged[$key] = $value;
            }
        }

        return $merged;
    }

    /**
     * Retrieve <img> markups in HTML, downloads corresponding images on 'elfinder'
     * storage, and modify corresponding <img> source.
     * @param string $input Html content to analyze/modify
     */
    public static function downloadImagesFromHtmlToStorage(&$input)
    {
        if (empty($input) || false === stripos($input, '<img')) {
            return;
        }
        $description = new \DOMDocument();
        $description->loadHtml(
            mb_convert_encoding($input, 'HTML-ENTITIES', "UTF-8")
        );
        $img_list = $description->getElementsByTagName('img');
        static $shutdown_inited = false;

        foreach ($img_list as $img) {
            $img_src = $img->attributes->getNamedItem('src');

            //Check image path with storage and save file in storage if needed
            if (strpos(
                $img_src->value,
                container()->get("Wizacha\Storage\ElfinderStorageService")->getUrl('')
            ) !== 0
            ) {
                //Check extension
                if (!\in_array(fn_strtolower(fn_get_file_ext($img_src->value)), ['jpg', 'png', 'gif', 'jpeg', 'ico'])) {
                    fn_set_notification('E', __('error'), __('text_not_allowed_to_upload_file_extension', array(
                        '[ext]' => fn_get_file_ext($img_src->value)
                    )));
                    $img->parentNode->removeChild($img);
                    continue;
                }

                $temp = fn_get_url_data($img_src->nodeValue);

                //Check size
                if ($temp['size'] > TINY_UPLOAD_MAX_SIZE) {
                    fn_set_notification('E', __('error'), __('text_forbidden_uploaded_file_size', array(
                        '[size]' => TINY_UPLOAD_MAX_SIZE / (1024 * 1024) . 'Mo'
                    )));
                    $img->parentNode->removeChild($img);
                    continue;
                }

                if ($temp['size'] == 0) {
                    fn_set_notification('E', __('error'), __('w_text_empty_uploaded_file_size', array(
                        '[url]' => $img_src->value
                    )));
                    $img->parentNode->removeChild($img);
                    continue;
                }

                $filename = 'companies/' . \Tygh\Registry::get('runtime.company_id') . '/' . $temp['name'];
                container()->get("Wizacha\Storage\ElfinderStorageService")->put($filename, ['file' => $temp['path']]);
                $img_src->nodeValue = container()->get("Wizacha\Storage\ElfinderStorageService")->getUrl($filename);

                /*
                * In some case (in product with new images and images in description) fn_remove_temp_data can be called twice.
                */
                if (!$shutdown_inited) {
                    $shutdown_inited = true;
                    register_shutdown_function('fn_remove_temp_data');
                }
            }
        }

        $input = self::_getInnerBodyDomDocument($description);
    }

    /**
     * Allows to *hack* the redirection done by CsCart after a 'save' operation on an object.
     * By default, you are redirected to "$current_controller.manage.last_view" .
     *
     * @note This function should be considered as a *last resort* solution
     *
     * @param string $current_controller Check if the current redirection matches this controller
     * @param string $redirect_url The expected redirection URL
     * @param array $request The request array to modify, should be $_REQUEST
     */
    public static function redirectAfterSave($current_controller, $redirect_url, array &$request)
    {
        if (isset($request['redirect_url']) && $request['redirect_url'] == $current_controller . '.manage.last_view') {
            $request['redirect_url'] = $redirect_url;
        }
    }

    /**
     * @param mixed $var variable name
     * @param mixed $key variable value
     */
    public static function processSubmittedContent(&$var, $key, $area = null)
    {
        if (\is_null($area)) {
            $area = \Wizacha\Registry::defaultInstance()->get([\Wizacha\Config::REG_AREA]);
        }

        //Admin.php is in 'trusted' zone, no strip
        if (!($area == 'A' && \Tygh\Registry::get('runtime')['company_id'] === 0)) {
            $var = strip_tags($var, self::getAllowedTags($area));
            if (strpos($var, '<') !== false) {
                $dom = new \DOMDocument();
                // Don't remove @ or atoum tests will fail.
                @$dom->loadHtml(
                    mb_convert_encoding($var, 'HTML-ENTITIES', "UTF-8")
                );
                foreach ($dom->documentElement->childNodes ?? [] as $node) {
                    self::_removeDomAttributes($node);
                }
                $var = self::_getInnerBodyDomDocument($dom);
            }
        }
        //Customers can't upload image through text fields
        if ($area == 'A') {
            static::downloadImagesFromHtmlToStorage($var);
        }
    }

    /**
     * @param \DOMNode $parentNode
     */
    protected static function _removeDomAttributes(\DOMNode $parentNode)
    {

        if ($parentNode->childNodes) {
            foreach ($parentNode->childNodes as $node) {
                self::_removeDomAttributes($node);
            }
        }

        $valid_attributes = self::getAuthorizedHTMLAttribute();
        if ($parentNode instanceof \DOMElement && $parentNode->attributes) {
            foreach ($parentNode->attributes as $attribute) {
                if (!isset($valid_attributes[$attribute->name])) {
                    $parentNode->removeAttribute($attribute->name);
                } elseif (\is_string($valid_attributes[$attribute->name])) {
                    $matches = [];
                    preg_match_all($valid_attributes[$attribute->name], $parentNode->getAttribute($attribute->name), $matches, PREG_SET_ORDER);
                    $parentNode->setAttribute($attribute->name, implode('', array_column($matches, 0)));
                }
            }
        }
    }

    /**
     * @param \DOMDocument $document
     * @return string
     */
    protected static function _getInnerBodyDomDocument(\DOMDocument $document)
    {
        return str_replace(
            ['<body>', '</body>'],
            ['', ''],
            $document->saveHTML($document->getElementsByTagName('body')->item(0))
        );
    }

    /**
     * Hotfix : fn_create_seo_name use an recursive call to fn_create_seo_name with an incremental index.
     * If the n-first index are already taken, php exit.
     * This method get directly an free index.
     * @param integer $object_id
     * @param string $object_type
     * @param string $object_name
     * @param string $dispatch
     * @param string $lang_code
     * @return integer
     */
    public static function getSEOIndex($object_id, $object_type, $object_name, $dispatch, $lang_code): int
    {
        $nameLength = mb_strlen($object_name);
        $maxUsedNumber = null;

        if ($nameLength > 0) {
            $maxUsedNumber = \Tygh\Database::getField(
                "SELECT MAX(CAST(SUBSTR(name, ?i) AS UNSIGNED INTEGER)) as nb FROM ?:seo_names WHERE name REGEXP ?s AND (object_id != ?s OR type != ?s OR dispatch != ?s)",
                $nameLength + 2,
                '^' . $object_name . '-[0-9]+$',
                $object_id,
                $object_type,
                $dispatch
            );
        }

        return $maxUsedNumber > 0 ? ++$maxUsedNumber : 2;
    }

    /**
     * @param Registry $registry
     */
    public static function removeTempData(\Wizacha\Registry $registry)
    {
        $fs_data = $registry->get([self::KEY_TEMP_FILES]);
        if (!empty($fs_data) && \is_array($fs_data)) {
            array_map('fn_rm', $fs_data);
        }
    }

    /**
     * @param string $file
     * @param Registry $registry
     */
    public static function addFileToDeleteInCleanup($file, \Wizacha\Registry $registry)
    {
        $temp_files = $registry->get([self::KEY_TEMP_FILES]);
        if ($temp_files) {
            $temp_files[] = $file;
        } else {
            $temp_files = [$file];
        }

        $registry->set([self::KEY_TEMP_FILES], $temp_files);
    }

    /**
     * GZIPs a file on disk without loading it entirely in memory
     *
     * Based on http://stackoverflow.com/questions/6073397/how-do-you-create-a-gz-file-using-php
     *
     * @param string $source Path to file that should be compressed
     * @param string $dest Path to result file. If empty, extension .gz will be added to source file
     * @return string New filename (with .gz appended) if success, or false if operation fails
     **/
    public static function gzCompressFile($source, $dest = '')
    {
        $dest = $dest ? : $source . '.gz';
        if (!($fp_out = @gzopen($dest, 'wb'))) {
            return false;
        }
        if (!($fp_in = @fopen($source, 'rb'))) {
            return false;
        }
        while (!feof($fp_in)) {
            gzwrite($fp_out, fread($fp_in, 1024 * 512));
        }
        fclose($fp_in);
        gzclose($fp_out);

        return $dest;
    }

    public static function updateSitemapFiles($storage)
    {
        $tmp_folder = sys_get_temp_dir()
            . DIRECTORY_SEPARATOR
            . uniqid('sitemap_', true)
            . DIRECTORY_SEPARATOR;
        if (!mkdir($tmp_folder) && !is_dir($tmp_folder)) {
            throw new \RuntimeException(sprintf('Directory "%s" was not created', $tmp_folder));
        }
        fn_google_sitemap_get_content($tmp_folder);
        $storage->putDir($tmp_folder);

        foreach (new \FilesystemIterator($tmp_folder) as $file) {
            unlink($file->getRealPath());
        }
        rmdir($tmp_folder);
    }

    /**
     * Update all sitemap files
     */
    public static function updateSitemapFilesDelayed()
    {
        if (container()->get('marketplace.async_dispatcher')->delayExec(__METHOD__, \func_get_args())
        ) {
            return;
        }
        self::updateSitemapFiles(container()->get("Wizacha\Storage\SitemapStorageService"));
    }

    /**
     * @param array $menu_identifiers
     * @param string $section
     * @param string|null $lang_code
     * @return array
     */
    public static function getMenus(array $menu_identifiers, $section = 'A', $lang_code = null)
    {
        $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
        $menus = array();
        foreach ($menu_identifiers as $menu_identifier) {
            $menu_id = \Wizacha\Registry::defaultInstance()->get(['config', 'menus', $menu_identifier]);
            if ($menu_id) {
                $title = \Tygh\Database::getField(
                    "SELECT name FROM ?:menus_descriptions WHERE lang_code = ?s AND menu_id = ?i",
                    $lang_code,
                    $menu_id
                );
                $items = \Tygh\Database::getArray(
                    "SELECT sd.param as url, ?:static_data_descriptions.descr as title
                        FROM ?:static_data AS sd LEFT JOIN ?:static_data_descriptions
                        ON sd.param_id = ?:static_data_descriptions.param_id AND ?:static_data_descriptions.lang_code = ?s
                        WHERE sd.section = ?s AND sd.param_5 = ?i AND sd.status = 'A' ORDER BY sd.position",
                    $lang_code,
                    $section,
                    $menu_id
                );
                array_walk(
                    $items,
                    function (&$item) {
                        $item['url'] = fn_url($item['url']);
                    }
                );
                $menus[$title] = $items;
            }
        }
        return $menus;
    }

    /**
     * remove elements for superadmin.
     * @param mixed $data
     * @return mixed array
     */
    public static function removeSuperadminOnlyValues($data)
    {
        if (!\is_array($data)) {
            return $data;
        }
        if (!empty($data['superadmin_only'])) {
            return [];
        }
        return array_map('\Wizacha\Misc::removeSuperadminOnlyValues', $data);
    }
}
