<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\DumpAnonymizer\Services;

use Aws\S3\S3Client;
use GuzzleHttp\Psr7\Uri;

class OutputS3Manager
{
    private string $bucket;
    private string $clientDirectory;
    private S3Client $s3Client;

    public function __construct(
        S3Client $s3Client,
        string $bucket,
        string $clientDirectory
    ) {
        $this->s3Client = $s3Client;
        $this->bucket = $bucket;
        $this->clientDirectory = $clientDirectory;
    }

    public function uploadFile(string $localFilePath, string $outputFilename): Uri
    {
        $result = $this->s3Client->upload(
            $this->bucket,
            $this->buildOutputPath($outputFilename),
            \fopen($localFilePath, 'r')
        );

        return new Uri(
            $result->get('@metadata')['effectiveUri']
        );
    }

    private function buildOutputPath($filename): string
    {
        return $this->clientDirectory . "/" . $filename;
    }
}
