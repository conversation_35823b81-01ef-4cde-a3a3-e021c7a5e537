<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Storage;

use League\Flysystem\Filesystem;
use League\Flysystem\Memory\MemoryAdapter;

class StorageFactory
{
    private string $storageType;
    private string $localRootPath;

    private Adapter\Aws $awsAdapter;
    private Adapter\Local $localAdapter;
    private Adapter\Ovh $ovhAdapter;
    private Adapter\Azure $azureAdapter;

    public function __construct(
        string $localRootPath,
        string $storageType,
        Adapter\Aws $awsAdapter,
        Adapter\Local $localAdapter,
        Adapter\Ovh $ovhAdapter,
        Adapter\Azure $azureAdapter
    ) {
        $this->storageType = $storageType;
        $this->localRootPath = $localRootPath;

        $this->awsAdapter = $awsAdapter;
        $this->localAdapter = $localAdapter;
        $this->ovhAdapter = $ovhAdapter;
        $this->azureAdapter = $azureAdapter;
    }

    public function __invoke(string $name, array $options): ?StorageService
    {
        // mandatory for some local operations (resizing, compression, etc.)
        $options['name'] = $name;
        $options['rootDir'] = $this->localRootPath;
        $options['storageType'] = $this->storageType;
        $storageAdditionalOptions = [];


        if ($this->storageType === 'file') {
            $adapter = ($this->localAdapter)();
            $storageAdditionalOptions = [
                'secured' => false
            ];
        } elseif ($this->storageType === 'amazon') {
            $adapter = ($this->awsAdapter)();
            $storageAdditionalOptions = [
                'key' => container()->getParameter('aws.s3.credentials.key'),
                'secret' => container()->getParameter('aws.s3.credentials.secret'),
                'bucket' => container()->getParameter('aws.bucket'),
                'region' => container()->getParameter('aws.s3.credentials.region'),
            ];
        } elseif ($this->storageType === 'memory') {
            $adapter = new MemoryAdapter();
        } elseif ($this->storageType === 'ovh') {
            $adapter = ($this->ovhAdapter)($options['secured'] ?? false);
            $storageAdditionalOptions = [
                'publicBucket' => container()->getParameter('ovh.s3.public_bucket'),
                'privateBucket' => container()->getParameter('ovh.s3.private_bucket'),
                'publicUrlToken' => container()->getParameter('ovh.s3.public_url_token'),
                'region' => container()->getParameter('ovh.s3.region'),
            ];
        } elseif ($this->storageType === 'azure') {
            $adapter = ($this->azureAdapter)($options['prefix'], $options['secured'] ?? false);
            $storageAdditionalOptions = [
                'azure' => container()->get('marketplace.azure.client'),
                'sasHelper' => container()->get('marketplace.azure.sas_helper'),
            ];
        } else {
            return null;
        }

        $flysystem = new Filesystem($adapter);
        return new StorageService($flysystem, array_merge($options, $storageAdditionalOptions));
    }
}
