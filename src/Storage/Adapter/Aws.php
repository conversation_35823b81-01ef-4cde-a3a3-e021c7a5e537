<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Storage\Adapter;

use Aws\S3\S3Client;
use League\Flysystem\AwsS3v3\AwsS3Adapter;

class Aws
{
    private string $awsBucket;
    private string $prefix;
    private S3Client $client;

    public function __construct(S3Client $client, string $awsBucket, string $prefix = '')
    {
        $this->client = $client;
        $this->awsBucket = $awsBucket;
        $this->prefix = $prefix;
    }

    public function __invoke(): AwsS3Adapter
    {
        // change the default visibility to public (because elfinder directly uses the adapter instead of our StorageService)
        return new AwsS3Adapter($this->client, $this->awsBucket, $this->prefix, ['ACL' => 'public-read']);
    }
}
