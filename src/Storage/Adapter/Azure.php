<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Storage\Adapter;

use League\Flysystem\AzureBlobStorage\AzureBlobStorageAdapter;
use MicrosoftAzure\Storage\Blob\BlobRestProxy;
use MicrosoftAzure\Storage\Blob\Models\CreateContainerOptions;
use MicrosoftAzure\Storage\Blob\Models\ListContainersOptions;
use MicrosoftAzure\Storage\Blob\Models\PublicAccessType;

class Azure
{
    private BlobRestProxy $azureClient;

    public function __construct(BlobRestProxy $azureClient)
    {
        $this->azureClient = $azureClient;
    }

    public function __invoke(string $prefix, bool $secured): AzureBlobStorageAdapter
    {
        $prefix = preg_replace('/[^a-z]+/', '', strtolower($prefix));

        //Vérification de l'existence du container
        $listOptions = new ListContainersOptions();
        $listOptions->setPrefix($prefix);

        if (empty($this->azureClient->listContainers($listOptions)->getContainers())) {
            //Création du container lorsqu'il n'existe pas
            $createOptions = new CreateContainerOptions();

            if (true === $secured) {
                $createOptions->setPublicAccess(PublicAccessType::NONE);
            } else {
                $createOptions->setPublicAccess(PublicAccessType::BLOBS_ONLY);
            }

            $this->azureClient->createContainer($prefix, $createOptions);
        }

        return new AzureBlobStorageAdapter($this->azureClient, $prefix);
    }
}
