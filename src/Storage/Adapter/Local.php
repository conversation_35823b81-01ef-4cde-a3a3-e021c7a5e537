<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Storage\Adapter;

class Local
{
    private string $localRootPath;

    public function __construct(string $localRootPath)
    {
        $this->localRootPath = $localRootPath;
    }

    public function __invoke(): \League\Flysystem\Adapter\Local
    {
        return new \League\Flysystem\Adapter\Local($this->localRootPath);
    }
}
