<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Storage\Adapter;

use Aws\S3\S3Client;
use League\Flysystem\AwsS3v3\AwsS3Adapter;

class Ovh
{
    private string $ovhPrivateBucket;
    private string $prefix;
    private S3Client $client;
    private string $ovhPublicBucket;

    public function __construct(S3Client $client, string $ovhPrivateBucket, string $ovhPublicBucket, string $prefix = '')
    {
        $this->ovhPrivateBucket = $ovhPrivateBucket;
        $this->ovhPublicBucket = $ovhPublicBucket;
        $this->prefix = $prefix;
        $this->client = $client;
    }

    public function __invoke(bool $secure = true): AwsS3Adapter
    {
        if (false === $secure) {
            return new AwsS3Adapter($this->client, $this->ovhPublicBucket, $this->prefix);
        }

        return new AwsS3Adapter($this->client, $this->ovhPrivateBucket, $this->prefix);
    }
}
