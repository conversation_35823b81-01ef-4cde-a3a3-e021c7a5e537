<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Storage;

use League\Flysystem\FilesystemInterface;
use MicrosoftAzure\Storage\Common\Internal\Resources;
use Symfony\Component\HttpFoundation\File\Exception\FileNotFoundException;
use Symfony\Component\HttpFoundation\HeaderUtils;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Tygh\Registry;

/** decorate FlysystemStorage */
class StorageService
{
    public const FILE_SUFFIX_LENGTH = 6;
    public FilesystemInterface $flysystemStorage;

    private array $options;

    public function __construct(FilesystemInterface $flysystemStorage, array $options)
    {
        $this->flysystemStorage = $flysystemStorage;
        $this->options = $options;
    }

    /** Passes all unknown calls to the imageStorage. */
    public function __call(string $method, array $args)
    {
        return \call_user_func_array([$this->flysystemStorage, $method], $args);
    }

    /** Copy file outside the storage to local file */
    public function export(string $src, string $dest): bool
    {
        try {
            return (bool) file_put_contents($dest, $this->flysystemStorage->readStream($this->prefix($src)));
        } catch (\League\Flysystem\FileNotFoundException $e) {
            return false;
        }
    }

    /** Put file to storage */
    public function put(string $file, array $params): ?array
    {
        if (empty($params['overwrite'])) {
            $file = $this->_generateName($file);
        }
        $file = $this->prefix($file);

        if (empty($params['mime-type'])) {
            $params['ContentType'] = fn_get_file_type($file);
        } else {
            $params['ContentType'] = $params['mime-type'];
        }

        if (null !== $this->getOption('max-age')) {
            $params['CacheControl'] = 'max-age=' . $this->getOption('max-age');
        }

        // File can not be accessible via direct link
        if (true === $this->options['secured']
            && 'amazon' === $this->options['storageType']
        ) {
            $params['ContentDisposition'] = 'attachment; filename="' . preg_replace('/[^a-zA-Z0-9._-]+/', '-', fn_basename($file)) . '"';
        }

        if (false === $this->flysystemStorage->createDir(\dirname($file))) {
            return null;
        }

        if (false === empty($params['file']) && file_exists($params['file'])) {
            if ($this->flysystemStorage->has($file)) {
                $this->flysystemStorage->delete($file);
            }
            $putStatus = $this->flysystemStorage->put($file, file_get_contents($params['file']), $params);
        } else {
            $putStatus = $this->flysystemStorage->put($file, $params['contents'], $params);
        }

        if (false === $putStatus) {
            return null;
        }

        if ('amazon' === $this->options['storageType']) {
            // visibility is supported only by AWS adapter
            $visibility = ( true === $this->options['secured'] ) ? 'private' : 'public';
            $this->flysystemStorage->setVisibility($file, $visibility);
        }

        if (false === empty($params['file'])) {
            $filesize = filesize($params['file']);

            if (true === empty($params['keep_origins'])) {
                fn_rm($params['file']);
            }
        } else {
            $filesize = \strlen($params['contents']);
        }
        return [$filesize, str_replace($this->prefix(), '', $file)];
    }

    /** Put directory to storage */
    public function putDir(string $dir, array $params = array()): bool
    {
        $files = fn_get_dir_contents($dir, false, true, '', '', true);
        fn_set_progress('step_scale', sizeof($files));

        foreach ($files as $source_file) {
            fn_set_progress('echo', '.');

            $this->put($source_file, [
                'contents' => file_get_contents($dir . '/' . $source_file)
            ]);
        }

        return true;
    }

    public function getUrl(string $file = '', string $protocol = '', string $disposition = 'inline'): string
    {
        if (strpos($file, '://') !== false) {
            return $file;
        }
        $url = '';

        if ('file' === $this->options['storageType']) {
            if ($protocol === 'http') {
                $prefix = Registry::get('config.http_location');
            } elseif ($protocol === 'https') {
                $prefix = Registry::get('config.https_location');
            } elseif ($protocol === 'short') {
                $prefix = '//' . Registry::get('config.http_host') . Registry::get('config.http_path'); // FIXME
            } else {
                $prefix = Registry::get('config.https_path');
            }

            $path = $this->urlEncodePathElements(str_replace(Registry::get('config.dir.root'), '', $this->prefix($file)));
            $url = $prefix . $path;
        }

        if ('amazon' === $this->options['storageType'] || 'ovh' === $this->options['storageType']) {
            if ($protocol === 'http') {
                $prefix = 'http://';
            } elseif ($protocol === 'https') {
                $prefix = 'https://';
            } elseif ($protocol === 'short') {
                $prefix = '//';
            } else {
                $prefix = \defined('HTTPS') ? 'https://' : 'http://';
            }

            if ('ovh' === $this->options['storageType']) {
                $host = 'storage.' . strtolower($this->getOption('region')) . '.cloud.ovh.net/v1/' . $this->getOption('publicUrlToken') . '/' . $this->getOption('publicBucket');
                $privateBucket = $this->options['privateBucket'];
            } else {
                $host = 's3-' . $this->getOption('region') . '.amazonaws.com' . '/' . $this->getOption('bucket');
                $privateBucket = $this->options['bucket'];
            }
            $url = $prefix . $host . '/' . $this->urlEncodePathElements($this->prefix($file));

            if (true === $this->options['secured']
                || $disposition !== 'inline'
            ) {
                $file = ltrim($this->prefix($file), '/');

                $s3 = $this->flysystemStorage->getAdapter()->getClient();

                $getObject = [
                    'Bucket' => $privateBucket,
                    'Key' => $file,
                    'ResponseContentDisposition' => $disposition
                ];

                $command = $s3->getCommand('GetObject', $getObject);

                $url = (string) $s3->createPresignedRequest($command, '+60 minutes')->getUri();
            }
        }

        if ('azure' === $this->options['storageType']) {
            $azureClient = $this->options['azure'];
            $containerName = preg_replace('/[^a-z]+/', '', strtolower($this->options['prefix']));
            $url = $azureClient->getBlobUrl($containerName, $this->prefix($file));

            if ((true === $this->options['secured'] || $disposition !== 'inline' )
                && $file !== ''
            ) {
                $resName = $containerName . '/' . $this->urlEncodePathElements($this->prefix($file));
                $query = $this->options['sasHelper']->generateBlobServiceSharedAccessSignatureToken(
                    Resources::RESOURCE_TYPE_BLOB,
                    $resName,
                    'r',
                    new \DateTime('+60 minutes'),
                    '',
                    '',
                    '',
                    '',
                    '',
                    $disposition
                );
                $url .= '?' . $azureClient->getPsrPrimaryUri()->withQuery($query)->getQuery();
            }
        }

        return $url;
    }

    public function getAbsolutePath(string $file): string
    {
        return rtrim($this->options['rootDir'], '/') . '/' . $this->prefix($file);
    }

    /** Push file contents to browser */
    public function get(string $file, string $filename = null, string $disposition = HeaderUtils::DISPOSITION_ATTACHMENT): Response
    {
        if ('file' !== $this->options['storageType']) {
            return new RedirectResponse($this->getUrl($file));
        }

        try {
            $response = new Response(stream_get_contents($this->flysystemStorage->readStream($this->prefix($file))));
        } catch (\League\Flysystem\FileNotFoundException $e) {
            throw new FileNotFoundException($this->prefix($file));
        }

        $utf8Name = $filename ?? basename($file);
        $asciiName = preg_replace('/[^\x20-\x7E]/', '', $utf8Name) ?: 'filename';
        $dispositionHeader = HeaderUtils::makeDisposition($disposition, $utf8Name, $asciiName);
        $response->headers->set('Content-Disposition', $dispositionHeader);
        $response->headers->set('Content-Type', $this->flysystemStorage->getMimetype($this->prefix($file)));
        return $response;
    }

    public function delete(string $file): bool
    {
        if (empty($file) || !$this->flysystemStorage->has($this->prefix($file))) {
            return false;
        }

        return $this->flysystemStorage->delete($this->prefix($file));
    }

    public function deleteDir(string $dir = ''): bool
    {
        return $this->flysystemStorage->deleteDir($this->prefix($dir));
    }

    public function isExist(string $file): bool
    {
        return $this->flysystemStorage->has($this->prefix($file));
    }

    public function getList(string $prefix = ''): array
    {
        $flySystemContents = $this->flysystemStorage->listContents($this->prefix($prefix), true);
        $filenames = [];

        foreach ($flySystemContents as $flySystemContent) {
            $filenames[] = $flySystemContent['basename'];
        }

        return $filenames;
    }

    public function listContents(string $prefix = ''): array
    {
        return $this->flysystemStorage->listContents($this->prefix($prefix), true);
    }

    protected function prefix(string $file = ''): string
    {
        $prefix = '';
        if ('file' === $this->options['storageType']) {
            $prefix .= rtrim($this->getOption('dir'), '/') . '/';
        }

        return $prefix . rtrim($this->getOption('prefix'), '/') . '/' . $file;
    }

    public function getElFinderOpts(string $extra_path = ''): array
    {
        fn_mkdir($this->prefix($extra_path));
        return [
            'driver' => 'Flysystem',
            'path' => rtrim($this->prefix($extra_path), '/'),
            'URL' => $this->getUrl($extra_path),
            'filesystem' => $this->flysystemStorage
        ];
    }

    /**
     * @return resource|null The path resource or false on failure.
     * @throws \League\Flysystem\FileNotFoundException
     */
    public function readStream(string $file)
    {
        return $this->flysystemStorage->readStream($this->prefix($file));
    }

    public function getSize(string $path): ?int
    {
        return (int) $this->flysystemStorage->getSize($this->prefix($path));
    }

    public function getLastModifiedTimestamp(string $path): ?int
    {
        return $this->flysystemStorage->getTimestamp($this->prefix($path));
    }

    /** @return mixed option value */
    public function getOption(string $key)
    {
        return $this->options[$key] ?? null;
    }

    public function getOptions(): array
    {
        return $this->options;
    }

    /** Checks if file with this name is already exist and generate new name if it is so */
    protected function _generateName(string $file): string
    {
        if ($this->flysystemStorage->has($this->prefix($file))) {
            $parts = explode('.', $file);
            $parts[0] .= '_' . fn_strtolower(fn_generate_code('', self::FILE_SUFFIX_LENGTH));

            $file = implode('.', $parts);
        }

        return $file;
    }

    public function urlEncodePathElements(string $path): string
    {
        return $path ? implode('/', array_map('rawurlencode', explode('/', $path))) : '';
    }

    public function copy(string $path, string $newpath): bool
    {
        return $this->flysystemStorage->copy($path, $newpath);
    }
}
