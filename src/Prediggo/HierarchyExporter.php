<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Prediggo;

use Psr\Log\LoggerInterface;

class HierarchyExporter
{
    /**
     * @var LoggerInterface
     */
    private $logger;

    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    /**
     * @return resource The stream where the CSV has been written (it's a temporary file)
     * @throws \Exception if we cannot write to disk
     */
    public function export()
    {
        $tmpFile = tmpfile();
        if ($tmpFile === false) {
            throw new \Exception('Prediggo / Hierarchy export: cannot create tmp file');
        }

        fputcsv($tmpFile, [
            'parent_att_name',
            'parent_att_id',
            'child_att_name',
            'child_att_id'
        ], '|');

        list($categories) = fn_get_categories([
            'group_by_level' => false,
            'simple' => false,
            'status' => 'A',
        ]);
        foreach (array_reverse($categories) as $category) {
            fputcsv($tmpFile, [
                'categories',
                $category['parent_id'] ?: '',
                'categories',
                $category['category_id']
            ], '|');
        }

        return $tmpFile;
    }
}
