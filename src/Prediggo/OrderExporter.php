<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Prediggo;

use Wizacha\Marketplace\Order\OrderService;

class OrderExporter
{
    /**
     * @var OrderService
     */
    private $orderService;

    public function __construct(OrderService $orderService)
    {
        $this->orderService = $orderService;
    }

    /**
     * @return resource The stream where the XMl has been written (it's a temporary file)
     * @throws \Exception if we cannot write to disk
     */
    public function export()
    {
        $tmpFile = tmpfile();
        if ($tmpFile === false) {
            throw new \Exception('Prediggo / order export: cannot create tmp file');
        }

        $tmpUri = stream_get_meta_data($tmpFile)['uri'];
        $xmlWriter = new \XMLWriter();
        $xmlWriter->openUri($tmpUri);

        $xmlWriter->setIndent(\defined('DEVELOPMENT') && DEVELOPMENT);
        $xmlWriter->startDocument('1.0', 'UTF-8');

        $xmlWriter->startElement('transactions');
        $xmlWriter->writeAttribute('generationDate', date('c'));

        foreach ($this->orderService->getOrders() as $order) {
            $xmlWriter->startElement('transaction');
            $xmlWriter->writeAttribute('date', $order->getTimestamp()->format('c'));

            // ID
            $xmlWriter->startElement('transactionid');
            $xmlWriter->text($order->getId());
            $xmlWriter->endElement();

            // User
            $xmlWriter->startElement('userid');
            $xmlWriter->text($order->getUserId());
            $xmlWriter->endElement();

            $xmlWriter->startElement('items');

            foreach ($order->getItems() as $item) {
                $xmlWriter->startElement('item');

                $xmlWriter->startElement('itemid');
                $xmlWriter->text(explode('_', $item->getDeclinationId())[0]);
                $xmlWriter->endElement();

                $xmlWriter->startElement('price');
                $xmlWriter->text($item->getPrice()->getConvertedAmount());
                $xmlWriter->endElement();

                $xmlWriter->startElement('quantity');
                $xmlWriter->text($item->getAmount());
                $xmlWriter->endElement();

                $xmlWriter->endElement(); // </item>
            }

            $xmlWriter->endElement(); // </items>

            $xmlWriter->endElement(); // </transaction>
        }

        $xmlWriter->endElement(); // </transactions>
        $xmlWriter->endDocument();

        return $tmpFile;
    }
}
