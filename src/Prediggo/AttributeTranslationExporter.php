<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Prediggo;

use Psr\Log\LoggerInterface;
use Wizacha\Marketplace\Catalog\Company\CompanyService;
use Wizacha\Marketplace\Company\CompanyType;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\PIM\Attribute\AttributeService;

class AttributeTranslationExporter
{
    /**
     * @var AttributeService
     */
    private $attributeService;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var CompanyService
     */
    private $companyService;

    public function __construct(AttributeService $attributeService, CompanyService $companyService, LoggerInterface $logger)
    {
        $this->attributeService = $attributeService;
        $this->logger = $logger;
        $this->companyService = $companyService;
    }

    /**
     * @return resource The stream where the CSV has been written (it's a temporary file)
     * @throws \Exception if we cannot write to disk
     */
    public function exportNames()
    {
        $tmpFile = tmpfile();
        if ($tmpFile === false) {
            throw new \Exception('Prediggo / Attributes names export: cannot create tmp file');
        }

        fputcsv($tmpFile, [
            'lang_code',
            'att_name',
            'att_value',
            'att_property'
        ], '|');

        fputcsv($tmpFile, [
            (string) GlobalState::interfaceLocale(),
            'categories',
            __('category'),
            ''
        ], '|');

        fputcsv($tmpFile, [
            (string) GlobalState::interfaceLocale(),
            'companiesType',
            __('w_company_type'),
            ''
        ], '|');

        fputcsv($tmpFile, [
            (string) GlobalState::interfaceLocale(),
            'companies',
            __('companies'),
            ''
        ], '|');

        $attributes = $this->attributeService->getPublicAttributesForSearchAndFaceting();

        foreach ($attributes as $attribute) {
            fputcsv($tmpFile, [
                (string) GlobalState::interfaceLocale(),
                $attribute['feature_id'],
                trim($attribute['description']),
                ''
            ], '|');
        }

        return $tmpFile;
    }

    /**
     * @return resource The stream where the CSV has been written (it's a temporary file)
     * @throws \Exception if we cannot write to disk
     */
    public function exportValues()
    {
        $tmpFile = tmpfile();
        if ($tmpFile === false) {
            throw new \Exception('Prediggo / Attributes values export: cannot create tmp file');
        }

        fputcsv($tmpFile, [
            'lang_code',
            'attribute',
            'id',
            'value',
            'property'
        ], '|');

        // Categories
        list($categories) = fn_get_categories([
            'group_by_level' => false,
            'simple' => false,
            'status' => 'A',
        ]);
        foreach (array_reverse($categories) as $category) {
            fputcsv($tmpFile, [
                (string) GlobalState::interfaceLocale(),
                'categories',
                $category['category_id'],
                trim($category['category']),
                ''
            ], '|');
        }

        // Company type
        foreach (CompanyType::toArray() as $type) {
            fputcsv($tmpFile, [
                (string) GlobalState::interfaceLocale(),
                'companiesType',
                $type,
                __('w_type_product_' . $type),
                ''
            ], '|');
        }

        // Companies
        $companies = $this->companyService->getCompanies();
        foreach ($companies as $company) {
            fputcsv($tmpFile, [
                (string) GlobalState::interfaceLocale(),
                'companies',
                $company->getId(),
                $company->getName(),
                ''
            ], '|');
        }

        // Attributes with value IDS
        $attributes = $this->attributeService->getPublicAttributesForSearchAndFaceting();
        foreach ($attributes as $attribute) {
            foreach ($this->attributeService->getAttributeVariants($attribute['feature_id']) as $variant) {
                fputcsv($tmpFile, [
                    (string) GlobalState::interfaceLocale(),
                    $attribute['feature_id'],
                    $variant['variant_id'],
                    trim($variant['variant']),
                    ''
                ], '|');
            }
        }

        return $tmpFile;
    }
}
