<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Prediggo;

use Wizacha\Marketplace\User\User;
use Wizacha\Marketplace\User\UserService;

class UserExporter
{
    /**
     * @var UserService
     */
    private $userService;

    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
    }

    /**
     * @return resource The stream where the XMl has been written (it's a temporary file)
     * @throws \Exception if we cannot write to disk
     */
    public function export()
    {
        $tmpFile = tmpfile();
        if ($tmpFile === false) {
            throw new \Exception('Prediggo / User export: cannot create tmp file');
        }

        $tmpUri = stream_get_meta_data($tmpFile)['uri'];
        $xmlWriter = new \XMLWriter();
        $xmlWriter->openUri($tmpUri);

        $xmlWriter->setIndent(\defined('DEVELOPMENT') && DEVELOPMENT);
        $xmlWriter->startDocument('1.0', 'UTF-8');

        $xmlWriter->startElement('users');
        $xmlWriter->writeAttribute('generationDate', date('c'));

        foreach ($this->getUsers() as $user) {
            $xmlWriter->startElement('user');

            // ID
            $xmlWriter->startElement('id');
            $xmlWriter->text($user->getUserId());
            $xmlWriter->endElement();

            $xmlWriter->startElement('attributes');

            // Email
            $xmlWriter->startElement('attribute');
            $xmlWriter->writeAttribute('name', 'email');
            $xmlWriter->startElement('value');
            $xmlWriter->text($user->getEmail());
            $xmlWriter->endElement();
            $xmlWriter->endElement(); // </attribute>

            // Title, if known
            if ($title = $user->getTitle()) {
                $xmlWriter->startElement('attribute');
                $xmlWriter->writeAttribute('name', 'title');
                $xmlWriter->startElement('value');
                $xmlWriter->text($title->getValue());
                $xmlWriter->endElement();
                $xmlWriter->endElement(); // </attribute>
            }

            $xmlWriter->endElement(); // </attributes>

            $xmlWriter->endElement(); // </user>
        }

        $xmlWriter->endElement(); // </users>
        $xmlWriter->endDocument();

        return $tmpFile;
    }

    /**
     * @return \Generator|User[]
     */
    private function getUsers(): \Generator
    {
        $page = 0;
        $chunkSize = 100;

        while ($data = $this->userService->findWithPagination($page++, $chunkSize)) {
            yield from $data;
        }
    }
}
