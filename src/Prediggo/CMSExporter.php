<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Prediggo;

use Wizacha\Marketplace\Cms\PageService;

class CMSExporter
{
    /**
     * @var PageService
     */
    private $pageService;

    public function __construct(PageService $pageService)
    {
        $this->pageService = $pageService;
    }

    /**
     * @return resource The stream where the XMl has been written (it's a temporary file)
     * @throws \Exception if we cannot write to disk
     */
    public function export()
    {
        $tmpFile = tmpfile();
        if ($tmpFile === false) {
            throw new \Exception('Prediggo / Product export: cannot create tmp file');
        }

        $date = date('c');
        $tmpUri = stream_get_meta_data($tmpFile)['uri'];
        $xmlWriter = new \XMLWriter();
        $xmlWriter->openUri($tmpUri);

        $xmlWriter->setIndent(\defined('DEVELOPMENT') && DEVELOPMENT);
        $xmlWriter->startDocument('1.0', 'UTF-8');

        $xmlWriter->startElement('items');
        $xmlWriter->writeAttribute('generationDate', $date);
        $xmlWriter->writeAttribute('isFull', 'true');

        foreach ($this->pageService->list() as $page) {
            $xmlWriter->startElement('item');

            $xmlWriter->startElement('id');
            $xmlWriter->text($page->getId());
            $xmlWriter->endElement();

            $xmlWriter->startElement('price');
            $xmlWriter->text(0);
            $xmlWriter->endElement();

            $xmlWriter->startElement('type');
            $xmlWriter->text('cms');
            $xmlWriter->endElement();

            $xmlWriter->startElement('recommendable');
            $xmlWriter->text('true');
            $xmlWriter->endElement();

            $xmlWriter->startElement('searchable');
            $xmlWriter->text('true');
            $xmlWriter->endElement();

            $xmlWriter->startElement('attributesByValue');

            // Title
            $xmlWriter->startElement('attributeByValue');
            $xmlWriter->writeAttribute('name', 'title');
            $xmlWriter->startElement('value');
            $xmlWriter->text($page->getTitle());
            $xmlWriter->endElement();
            $xmlWriter->endElement(); // </attributeByValue>

            // Content
            $xmlWriter->startElement('attributeByValue');
            $xmlWriter->writeAttribute('name', 'content');
            $xmlWriter->startElement('value');
            if ($tmp = $page->getContent()) {
                $xmlWriter->text($tmp);
            } else {
                // ❤ Prediggo
                $xmlWriter->writeRaw(' ');
            }
            $xmlWriter->endElement();
            $xmlWriter->endElement(); // </attributeByValue>

            // Slug
            $xmlWriter->startElement('attributeByValue');
            $xmlWriter->writeAttribute('name', 'slug');
            $xmlWriter->startElement('value');
            $xmlWriter->text($page->getSeoData()->getSlug());
            $xmlWriter->endElement();
            $xmlWriter->endElement(); // </attributeByValue>

            $xmlWriter->endElement(); // </attributesByValue>

            $xmlWriter->startElement('attributesById');
            $xmlWriter->endElement(); // </attributesById>

            $xmlWriter->endElement(); // </item>
        }

        $xmlWriter->endElement(); // </items>
        $xmlWriter->endDocument();

        return $tmpFile;
    }
}
