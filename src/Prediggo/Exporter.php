<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Prediggo;

use Wizacha\Storage\StorageService;

class Exporter
{
    /**
     * @var ProductExporter
     */
    private $productExporter;

    /**
     * @var HierarchyExporter
     */
    private $hierarchyExporter;

    /**
     * @var AttributeTranslationExporter
     */
    private $attributeTranslationExporter;

    /**
     * @var UserExporter
     */
    private $userExporter;

    /**
     * @var OrderExporter
     */
    private $orderExporter;

    /**
     * @var CMSExporter
     */
    private $cmsExporter;

    private ?StorageService $prediggoStorageService;

    public function __construct(
        ProductExporter $productExporter,
        HierarchyExporter $hierarchyExporter,
        AttributeTranslationExporter $attributeTranslationExporter,
        UserExporter $userExporter,
        OrderExporter $orderExporter,
        CMSExporter $cmsExporter,
        StorageService $prediggoStorageService
    ) {
        $this->productExporter = $productExporter;
        $this->hierarchyExporter = $hierarchyExporter;
        $this->attributeTranslationExporter = $attributeTranslationExporter;
        $this->userExporter = $userExporter;
        $this->orderExporter = $orderExporter;
        $this->cmsExporter = $cmsExporter;
        $this->prediggoStorageService = $prediggoStorageService;
    }

    public function export()
    {
        $tmpFile = $this->productExporter->export();
        $tmpFilePath = stream_get_meta_data($tmpFile)['uri'];
        // Sha hash ignores 100 first characters. This is approximately the length of the 2 first lines with the timestamp.
        $integrityHash = sha1(stream_get_contents($tmpFile, -1, 100));
        $saveResult = $this->prediggoStorageService->put('products.xml', [
            'file' => $tmpFilePath,
            'overwrite' => true
        ]);
        fclose($tmpFile);

        if (!$saveResult) {
            throw new \Exception('Prediggo / Global export: cannot save products.xml');
        }

        $tmpFile = $this->hierarchyExporter->export();
        $tmpFilePath = stream_get_meta_data($tmpFile)['uri'];
        $integrityHash .= sha1_file($tmpFilePath);
        $saveResult = $this->prediggoStorageService->put('Hierarchy.csv', [
            'file' => $tmpFilePath,
            'overwrite' => true
        ]);
        fclose($tmpFile);

        if (!$saveResult) {
            throw new \Exception('Prediggo / Global export: cannot save hierarchy.csv');
        }

        $tmpFile = $this->attributeTranslationExporter->exportNames();
        $tmpFilePath = stream_get_meta_data($tmpFile)['uri'];
        $integrityHash .= sha1_file($tmpFilePath);
        $saveResult = $this->prediggoStorageService->put('AttributesTranslations.csv', [
            'file' => $tmpFilePath,
            'overwrite' => true
        ]);
        fclose($tmpFile);

        if (!$saveResult) {
            throw new \Exception('Prediggo / Global export: cannot save AttributesTranslations.csv');
        }

        $tmpFile = $this->attributeTranslationExporter->exportValues();
        $tmpFilePath = stream_get_meta_data($tmpFile)['uri'];
        $integrityHash .= sha1_file($tmpFilePath);
        $saveResult = $this->prediggoStorageService->put('ValuesTranslations.csv', [
            'file' => $tmpFilePath,
            'overwrite' => true
        ]);
        fclose($tmpFile);

        if (!$saveResult) {
            throw new \Exception('Prediggo / Global export: cannot save ValuesTranslations.csv');
        }

        $tmpFile = $this->userExporter->export();
        $tmpFilePath = stream_get_meta_data($tmpFile)['uri'];
        // Sha hash ignores 100 first characters. This is approximately the length of the 2 first lines with the timestamp.
        $integrityHash .= sha1(stream_get_contents($tmpFile, -1, 100));
        $saveResult = $this->prediggoStorageService->put('users.xml', [
            'file' => $tmpFilePath,
            'overwrite' => true
        ]);
        fclose($tmpFile);

        if (!$saveResult) {
            throw new \Exception('Prediggo / Global export: cannot save users.xml');
        }

        $tmpFile = $this->orderExporter->export();
        $tmpFilePath = stream_get_meta_data($tmpFile)['uri'];
        // Sha hash ignores 100 first characters. This is approximately the length of the 2 first lines with the timestamp.
        $integrityHash .= sha1(stream_get_contents($tmpFile, -1, 100));
        $saveResult = $this->prediggoStorageService->put('transactions.xml', [
            'file' => $tmpFilePath,
            'overwrite' => true
        ]);
        fclose($tmpFile);

        if (!$saveResult) {
            throw new \Exception('Prediggo / Global export: cannot save transactions.xml');
        }

        $tmpFile = $this->cmsExporter->export();
        $tmpFilePath = stream_get_meta_data($tmpFile)['uri'];
        // Sha hash ignores 100 first characters. This is approximately the length of the 2 first lines with the timestamp.
        $integrityHash .= sha1(stream_get_contents($tmpFile, -1, 100));
        $saveResult = $this->prediggoStorageService->put('CMSItems.xml', [
            'file' => $tmpFilePath,
            'overwrite' => true
        ]);
        fclose($tmpFile);

        if (!$saveResult) {
            throw new \Exception('Prediggo / Global export: cannot save CMSItems.xml');
        }

        $integrityTmpFile = tmpfile();
        fwrite($integrityTmpFile, sha1($integrityHash));
        $saveResult = $this->prediggoStorageService->put('integrity.txt', [
            'file' => stream_get_meta_data($integrityTmpFile)['uri'],
            'overwrite' => true
        ]);
        fclose($integrityTmpFile);

        if (!$saveResult) {
            throw new \Exception('Prediggo / Global export: cannot save integrity.txt');
        }
    }
}
