<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Prediggo;

use Psr\Log\LoggerInterface;
use Wizacha\Marketplace\Catalog\AttributeValue;
use Wizacha\Marketplace\Catalog\Category\CategorySummary;
use Wizacha\Marketplace\Catalog\Product\ProductService;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProductService;
use Wizacha\Marketplace\PIM\Product\ProductService as PIMProductService;

class ProductExporter
{
    /**
     * @var ProductService
     */
    private $productService;

    /**
     * @var MultiVendorProductService
     */
    private $multiVendorProductService;

    /**
     * @var PIMProductService
     */
    private $pimProductService;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var int
     */
    private $imageHeight;

    /**
     * @var int
     */
    private $imageWidth;

    public function __construct(
        ProductService $productService,
        MultiVendorProductService $multiVendorProductService,
        PIMProductService $pimProductService,
        LoggerInterface $logger,
        int $imageWidth,
        int $imageHeight
    ) {
        $this->productService = $productService;
        $this->multiVendorProductService = $multiVendorProductService;
        $this->pimProductService = $pimProductService;
        $this->logger = $logger;
        $this->imageHeight = $imageHeight;
        $this->imageWidth = $imageWidth;
    }

    /**
     * @return resource The stream where the XMl has been written (it's a temporary file)
     * @throws \Exception if we cannot write to disk
     */
    public function export()
    {
        $tmpFile = tmpfile();
        if ($tmpFile === false) {
            throw new \Exception('Prediggo / Product export: cannot create tmp file');
        }

        $tmpUri = stream_get_meta_data($tmpFile)['uri'];
        $xmlWriter = new \XMLWriter();
        $xmlWriter->openUri($tmpUri);

        $xmlWriter->setIndent(\defined('DEVELOPMENT') && DEVELOPMENT);
        $xmlWriter->startDocument('1.0', 'UTF-8');

        $xmlWriter->startElement('items');
        $xmlWriter->writeAttribute('generationDate', date('c'));
        $xmlWriter->writeAttribute('isFull', 'true');

        foreach ($this->productService->getSearcheableProductIdInFront() as $productId) {
            $product = $this->productService->getProduct($productId);

            if ($product === null) {
                $this->logger->warning('Product ID {productId} was returned by getSearcheableProductIdInFront, but is not found.', [
                    'productId' => $productId
                ]);
                continue;
            }

            $xmlWriter->startElement('item');
            if ($product->getCreatedAt()->getTimestamp() > 0) {
                $xmlWriter->writeAttribute('visibilityDate', $product->getCreatedAt()->format('c'));
            }

            $xmlWriter->startElement('id');
            $xmlWriter->text($product->getId());
            $xmlWriter->endElement();

            $xmlWriter->startElement('price');
            $xmlWriter->text($product->getCurrentPrice()->getConvertedAmount());
            $xmlWriter->endElement();

            $xmlWriter->startElement('type');
            $xmlWriter->text('product');
            $xmlWriter->endElement();

            $xmlWriter->startElement('recommendable');
            $xmlWriter->text('true');
            $xmlWriter->endElement();

            $xmlWriter->startElement('searchable');
            $xmlWriter->text('true');
            $xmlWriter->endElement();

            $xmlWriter->startElement('attributesByValue');

            // Name
            $xmlWriter->startElement('attributeByValue');
            $xmlWriter->writeAttribute('name', 'name');
            $xmlWriter->startElement('value');
            $xmlWriter->text($product->getName());
            $xmlWriter->endElement();
            $xmlWriter->endElement(); // </attributeByValue>

            // Subtitle
            $xmlWriter->startElement('attributeByValue');
            $xmlWriter->writeAttribute('name', 'subtitle');
            $xmlWriter->startElement('value');
            $this->writeNonEmptyText($xmlWriter, $product->getSubtitle());
            $xmlWriter->endElement();
            $xmlWriter->endElement(); // </attributeByValue>

            // Short description
            $xmlWriter->startElement('attributeByValue');
            $xmlWriter->writeAttribute('name', 'shortDescription');
            $xmlWriter->startElement('value');
            $this->writeNonEmptyText($xmlWriter, $product->getShortDescription(true));
            $xmlWriter->endElement();
            $xmlWriter->endElement(); // </attributeByValue>

            // Url
            $xmlWriter->startElement('attributeByValue');
            $xmlWriter->writeAttribute('name', 'url');
            $xmlWriter->startElement('value');
            $xmlWriter->text($product->getUrl());
            $xmlWriter->endElement();
            $xmlWriter->endElement(); // </attributeByValue>

            // Crossed out price
            $crossedOutPrice = $product->getCrossedOutPrice();
            $xmlWriter->startElement('attributeByValue');
            $xmlWriter->writeAttribute('name', 'crossedOutPrice');
            $xmlWriter->startElement('value');
            $xmlWriter->text($crossedOutPrice ? $crossedOutPrice->getConvertedAmount() : 0);
            $xmlWriter->endElement();
            $xmlWriter->endElement(); // </attributeByValue>

            // Code
            $xmlWriter->startElement('attributeByValue');
            $xmlWriter->writeAttribute('name', 'code');
            $xmlWriter->startElement('value');
            $this->writeNonEmptyText($xmlWriter, $product->getCode());
            $xmlWriter->endElement();
            $xmlWriter->endElement(); // </attributeByValue>

            // Is in stock
            $xmlWriter->startElement('attributeByValue');
            $xmlWriter->writeAttribute('name', 'inStock');
            $xmlWriter->startElement('value');
            $xmlWriter->text($product->isAvailable() ? 1 : 0);
            $xmlWriter->endElement();
            $xmlWriter->endElement(); // </attributeByValue>

            // Transaction mode
            $xmlWriter->startElement('attributeByValue');
            $xmlWriter->writeAttribute('name', 'transactionMode');
            $xmlWriter->startElement('value');
            $xmlWriter->text($product->getTransactionMode()->getValue());
            $xmlWriter->endElement();
            $xmlWriter->endElement(); // </attributeByValue>

            // SEO data
            $xmlWriter->startElement('attributeByValue');
            $xmlWriter->writeAttribute('name', 'slug');
            $xmlWriter->startElement('value');
            $xmlWriter->text($product->getSlug());
            $xmlWriter->endElement();
            $xmlWriter->endElement(); // </attributeByValue>
            $xmlWriter->startElement('attributeByValue');
            $xmlWriter->writeAttribute('name', 'categorySlugPath');
            $xmlWriter->startElement('value');
            $xmlWriter->text($product->getCategorySlugPath());
            $xmlWriter->endElement();
            $xmlWriter->endElement(); // </attributeByValue>

            // Image
            $mainImage = $product->getMainImage();
            if ($mainImage) {
                $xmlWriter->startElement('attributeByValue');
                $xmlWriter->writeAttribute('name', 'imageId');
                $xmlWriter->startElement('value');
                $xmlWriter->text($mainImage->getId());
                $xmlWriter->endElement();
                $xmlWriter->endElement(); // </attributeByValue>
            }

            // Creation date
            $xmlWriter->startElement('attributeByValue');
            $xmlWriter->writeAttribute('name', 'createdAt');
            $xmlWriter->startElement('value');
            $xmlWriter->text($product->getCreatedAt()->getTimestamp());
            $xmlWriter->endElement();
            $xmlWriter->endElement(); // </attributeByValue>

            // Modification date
            $xmlWriter->startElement('attributeByValue');
            $xmlWriter->writeAttribute('name', 'updatedAt');
            $xmlWriter->startElement('value');
            $xmlWriter->text($product->getUpdatedAt()->getTimestamp());
            $xmlWriter->endElement();
            $xmlWriter->endElement(); // </attributeByValue>

            // Conditions
            $xmlWriter->startElement('attributeByValue');
            $xmlWriter->writeAttribute('name', 'conditions');
            foreach ($product->getConditions() as $condition) {
                $xmlWriter->startElement('value');
                $xmlWriter->text($condition->getValue());
                $xmlWriter->endElement();
            }
            $xmlWriter->endElement(); // </attributeByValue>

            // Companies type
            $xmlWriter->startElement('attributeByValue');
            $xmlWriter->writeAttribute('name', 'companiesType');
            foreach ($product->getCompaniesType() as $type) {
                $xmlWriter->startElement('value');
                $xmlWriter->text($type->getValue());
                $xmlWriter->endElement();
            }
            $xmlWriter->endElement(); // </attributeByValue>

            // Companies summaries
            $xmlWriter->startElement('attributeByValue');
            $xmlWriter->writeAttribute('name', 'companiesSummaries');
            foreach ($product->getCompanies() as $companySummary) {
                $xmlWriter->startElement('value');
                $xmlWriter->text(json_encode($companySummary->expose()));
                $xmlWriter->endElement();
            }
            $xmlWriter->endElement(); // </attributeByValue>

            // Category path
            // We store category data manually because Prediggo's hierarchy doesn't let us store
            // custom data (slug, images, etc.)
            $xmlWriter->startElement('attributeByValue');
            $xmlWriter->writeAttribute('name', 'categoryPath');
            $xmlWriter->startElement('value');
            $xmlWriter->text(json_encode(array_map(function (CategorySummary $category) {
                return [
                    'id' => $category->getId(),
                    'name' => $category->getName(),
                    'slug' => $category->getSlug(),
                    'position' => $category->getPosition(),
                ];
            }, $product->getCategoryPath())));
            $xmlWriter->endElement();
            $xmlWriter->endElement(); // </attributeByValue>

            // Attributes with values, in json format
            $xmlWriter->startElement('attributeByValue');
            $xmlWriter->writeAttribute('name', 'attributes');
            $xmlWriter->startElement('value');
            $xmlWriter->text(json_encode(array_map(function (AttributeValue $attribute) {
                return $attribute->expose();
            }, $product->getAttributes())));
            $xmlWriter->endElement();
            $xmlWriter->endElement(); // </attributeByValue>

            // Geoloc
            if ($geoloc = $product->getGeolocation()) {
                $xmlWriter->startElement('attributeByValue');
                $xmlWriter->writeAttribute('name', 'geolocation');
                $xmlWriter->startElement('value');
                $xmlWriter->text(json_encode($geoloc->expose()));
                $xmlWriter->endElement();
                $xmlWriter->endElement(); // </attributeByValue>
            }

            // Attributs
            $attributes = $this->getAttributesToExport($productId);

            foreach ($attributes as $attribute) {
                if (empty($attribute['value']) || !empty($attribute['valueIds'])) {
                    continue;
                }

                $xmlWriter->startElement('attributeByValue');

                // Free feature: name is string, feature: name is ID (translated with AttributesTranslations.csv)
                $xmlWriter->writeAttribute('name', $attribute['id'] ?: $attribute['name']);

                if (\is_array($attribute['value'])) {
                    foreach ($attribute['value'] as $value) {
                        $xmlWriter->startElement('value');
                        $this->writeNonEmptyText($xmlWriter, $value);
                        $xmlWriter->endElement();
                    }
                } else {
                    $xmlWriter->startElement('value');
                    $this->writeNonEmptyText($xmlWriter, $attribute['value']);
                    $xmlWriter->endElement();
                }
                $xmlWriter->endElement(); // </attributeByValue>
            }

            // Main declination id
            $xmlWriter->startElement('attributeByValue');
            $xmlWriter->writeAttribute('name', 'mainDeclinationId');
            $xmlWriter->startElement('value');
            $xmlWriter->text($product->getMainDeclinationId());
            $xmlWriter->endElement();
            $xmlWriter->endElement(); // </attributeByValue>

            $xmlWriter->endElement(); // </attributesByValue>

            $xmlWriter->startElement('attributesById');

            $xmlWriter->startElement('attributeById');
            $xmlWriter->writeAttribute('name', 'categories');
            $xmlWriter->startElement('id');
            $xmlWriter->text(end($product->getCategoryPath())->getId());
            $xmlWriter->endElement();
            $xmlWriter->endElement(); // </attributeById>

            // Companies
            $xmlWriter->startElement('attributeById');
            $xmlWriter->writeAttribute('name', 'companies');
            foreach ($product->getCompanies() as $company) {
                $xmlWriter->startElement('id');
                $xmlWriter->text($company->getId());
                $xmlWriter->endElement();
            }
            $xmlWriter->endElement(); // </attributeById>

            // Features with valueIds
            foreach ($attributes as $attribute) {
                if (empty($attribute['valueIds'])) {
                    continue;
                }

                $xmlWriter->startElement('attributeById');
                $xmlWriter->writeAttribute('name', $attribute['id']);

                foreach ($attribute['valueIds'] as $value) {
                    $xmlWriter->startElement('id');
                    $xmlWriter->text($value);
                    $xmlWriter->endElement();
                }
                $xmlWriter->endElement(); // </attributeById>
            }

            $xmlWriter->endElement(); // </attributesById>

            $xmlWriter->endElement(); // </item>
        }
        $xmlWriter->endElement(); // </items>
        $xmlWriter->endDocument();

        return $tmpFile;
    }

    /**
     * Récupération des attributs prêt à être exportés :
     * - public
     * - searchable
     * - aplati (sans les groupes)
     *
     * @param int|string $id Product or MVP id
     */
    private function getAttributesToExport($id): array
    {
        if (MultiVendorProduct::isMultiVendorProductId($id)) {
            return $this->multiVendorProductService->getAllLegacyAttributes($id, false, false, true);
        }

        return $this->pimProductService->getAllLegacyAttributes((int) $id, false, false, true);
    }

    private function writeNonEmptyText(\XMLWriter $xmlWriter, string $text): void
    {
        // ❤ Prediggo
        if ($text === '') {
            $xmlWriter->writeRaw(' ');
        } else {
            $xmlWriter->text($text);
        }
    }
}
