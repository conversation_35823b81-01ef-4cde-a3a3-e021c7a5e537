<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Import;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Monolog\Logger;
use Doctrine\ORM\Query;

class EximJobLogRepository extends ServiceEntityRepository
{
    public const START = 0;
    public const LIMIT = 100;

    /**
     * Paginate data and add filters from callback
     * @param int $start
     * @param int $limit
     * @param callable|null $callback
     * @return Paginator
     */
    public function paginate(int $start = self::START, int $limit = self::LIMIT, callable $callback = null): Paginator
    {
        $query = $this->createQueryBuilder('l')
            ->orderBy('l.code', 'ASC')
            ->orderBy('l.id', 'ASC')
            ->setFirstResult($start)
            ->setMaxResults($limit);

        if ($callback) {
            \call_user_func($callback, $query);
        }

        return new Paginator($query);
    }

    /**
     * @param string $jobId
     * @return Counter
     */
    public function countByJobId(string $jobId): Counter
    {
        $query = $this->getEntityManager()->createQuery(
            'SELECT l.code, count(l) as counter FROM Wizacha\Component\Import\EximJobLog l WHERE l.jobId = :id GROUP BY l.code'
        );

        $results = $query->setParameter('id', $jobId)->getResult();

        $counter = new Counter();

        array_map(function ($data) use ($counter) {
            if (\in_array(Logger::INFO, $data)) {
                $counter->setInfos((int) $data['counter']);
            }

            if (\in_array(Logger::WARNING, $data)) {
                $counter->setWarnings((int) $data['counter']);
            }

            if (\in_array(Logger::ERROR, $data)) {
                $counter->setErrors((int) $data['counter']);
            }
        }, $results);

        return $counter;
    }

    public function countProcessedLines(string $jobId): int
    {
        return (int) $this
            ->getEntityManager()
            ->createQuery(
                'SELECT
                    count(distinct(l.line)) as total
                FROM
                    Wizacha\Component\Import\EximJobLog l
                WHERE
                    l.jobId = :id
                '
            )
            ->setParameter('id', $jobId)
            ->getSingleScalarResult()
        ;
    }

    public function getLogBounds(string $jobId): array
    {
        $query = $this
            ->getEntityManager()
            ->createQuery(
                'SELECT
                    MIN(l.createdAt) as firstLog,
                    MAX(l.createdAt) as lastLog
                FROM
                    Wizacha\Component\Import\EximJobLog l
                WHERE
                    l.jobId = :jobId
                '
            )
        ;

        $query ->setParameter('jobId', $jobId);

        return $query->getResult()[0];
    }

    public function getByProductId($productId)
    {
        $query = $this->getEntityManager()->createQuery(
            \sprintf(
                <<<SQL
                SELECT
                    ejl
                FROM
                    %s ej
                JOIN
                    %s ejl
                WHERE
                    ejl.entityId = :entityId
                    AND ej.type IN (:type)
                    AND ej.id = ejl.jobId
                ORDER BY
                    ejl.createdAt ASC
                SQL,
                EximJob::class,
                EximJobLog::class
            )
        );

        $query->execute(
            [
                'type' => [
                    JobType::PRODUCTS(),
                    JobType::RELATED_PRODUCTS(),
                    JobType::PRODUCT_PRICES(),
                    JobType::PRODUCT_QUANTITIES(),
                ],
                'entityId' => $productId,
            ]
        );

        return $query->getResult();
    }
}
