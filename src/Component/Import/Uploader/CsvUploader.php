<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Import\Uploader;

use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Wizacha\Component\Import\Exception\InvalidFileException;
use Wizacha\Marketplace\PIM\Product\Exim\CsvConverter;
use Wizacha\Marketplace\PIM\Product\Exim\XmlConverter;
use Wizacha\Storage\StorageService;

class CsvUploader implements UploaderInterface
{

    private ?StorageService $storage;
    /**
     * @var XmlConverter
     */
    private $xmlConverter;
    /**
     * @var CsvConverter
     */
    private $csvConverter;
    /**
     * @var array
     * some csv files detected as text/html because some fields are allowed to contain html, like a product description with "<a href=...>".
     * this is a system problem and not a code problem, so we must allowed text/html as input (still as .csv)
     */
    private $allowedMimeTypes = [
        'text/csv',
        'text/xml',
        'text/plain',
        'application/vnd.ms-excel',
        'text/x-algol68',
        'text/html',
    ];

    public function __construct(
        StorageService $storage,
        XmlConverter $xmlConverter,
        CsvConverter $csvConverter
    ) {
        $this->storage = $storage;
        $this->xmlConverter = $xmlConverter;
        $this->csvConverter = $csvConverter;
    }

    /**
     * @param Request $request
     * @return void
     * @throws InvalidFileException
     */
    public function check(Request $request): void
    {
        $isLocal = $this->isLocal($request);
        $resolver = new OptionsResolver();
        $resolver->setRequired(['file'])
            ->setAllowedTypes(
                'file',
                $isLocal ? UploadedFile::class : 'string'
            );

        if (sizeof($request->request->all()) === 0) {
            $resolver->resolve(array_merge(
                $request->request->all(),
                $request->files->all()
            ));
        }

        if ($isLocal) {
            /** @var UploadedFile $file */
            $file = $request->files->get('file');

            if (!$file->isValid()) {
                throw new InvalidFileException(__('exim_error_invalid_csv'));
            }

            $fileMimeType = $file->getMimeType();

            if (\in_array(strtolower($fileMimeType), $this->allowedMimeTypes) === false || strtolower($file->getClientOriginalExtension()) !== 'csv') {
                throw new InvalidFileException(__('exim_error_mimetype_csv', ['%s' => $fileMimeType]));
            }
        } else {
            $timeout = 10;
            $context = stream_context_create([
                'http' => [
                    'timeout' => $timeout
                ]
            ]);
            $startTime = microtime(true);
            $resource = @fopen($request->request->get('file'), 'r', false, $context);

            if (!$resource) {
                $endTime = microtime(true);
                if ($timeout < $endTime - $startTime) {
                    throw new InvalidFileException(__('exim_error_timeout_read_csv'));
                }
                throw new InvalidFileException(__('exim_error_not_access_csv'));
            }

            @fclose($resource);
        }
    }

    public function upload(Request $request, string $name): UploadedFile
    {
        if ($this->isLocal($request)) {
            $pathname = $request->files->get('file')->getPathname();
        } else {
            $handler = $this->getHandler($request->request->get('file'));
            $pathname = $this->getStreamUri($handler);
        }

        [$pathname, $filename] = $this->store($pathname, $name);

        return new UploadedFile($pathname, $filename);
    }

    public function getHandler(string $pathname)
    {
        $tempFile = tempnam(sys_get_temp_dir(), 'csvfile');
        $handler = fopen($tempFile, 'w+');
        $stream = (!\is_resource($handler) || get_resource_type($handler) != 'stream') ? false : true;
        $resource = fopen($pathname, 'r');

        if (false === \is_resource($resource)) {
            throw new \RuntimeException('Unable to open file ' . $pathname);
        }

        if ($stream) {
            stream_copy_to_stream($resource, $handler);
        }

        $uri = $this->getStreamUri($handler);

        if ($this->xmlConverter->supports($uri)) {
            $handler = $this->xmlConverter->convertFile($uri);
        } elseif ($this->csvConverter->supports($uri)) {
            $handler = $this->csvConverter->convertFile($uri);
        }

        return $handler;
    }

    private function getStreamUri($handler): string
    {
        return stream_get_meta_data($handler)['uri'];
    }

    /**
     * @param string $path
     * @param string $filename
     * @return array Absolute path, filename
     */
    protected function store(string $path, string $filename): array
    {
        $filename = str_replace(' ', '_', $filename) . '_' . time();
        $filename = $this->storage->put("$filename.csv", ['file' => $path, 'keep_origins' => true])[1];

        return [$path, $filename];
    }

    private function isLocal(Request $request)
    {
        return $request->files->has('file');
    }
}
