<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Import\Uploader;

use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;

interface UploaderInterface
{
    /**
     * Check if request contain good parameters
     * Check if file is valid
     * @param Request $request
     * @return mixed
     */
    public function check(Request $request);

    /**
     * Must save uploaded file
     * @param Request $request
     * @param string $name
     * @return UploadedFile
     */
    public function upload(Request $request, string $name): UploadedFile;

    /**
     * Must return a resource handler
     * @param string $pathname
     * @return resource
     */
    public function getHandler(string $pathname);
}
