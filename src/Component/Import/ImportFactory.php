<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Import;

use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Wizacha\AppBundle\Security\User\ApiSecurityUser;
use Wizacha\Component\Import\Exception\InvalidStructureException;
use Wizacha\Component\Import\Uploader\UploaderInterface;
use Wizacha\Exim\CsvFile;
use Wizacha\Marketplace\Exception\ImportAlreadyExistsException;
use Wizacha\Exim\Exception\AutomatedFeedNotImplementedException;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Order\Exception\LogicException;

class ImportFactory
{
    public const CSV_FILE = 'file_csv_file';
    public const PATTERN_ID_PRODUCTS = 'products';
    public const PATTERN_ID_INVENTORY_PRICE = 'inventory_prices';
    public const PATTERN_ID_INVENTORY_QUANTITIES = 'inventory_quantities';

    private UploaderInterface $uploader;
    private EximJobService $jobService;
    private LoggerInterface $logger;

    public function __construct(UploaderInterface $uploader, EximJobService $jobService, LoggerInterface $logger)
    {
        $this->uploader = $uploader;
        $this->jobService = $jobService;
        $this->logger = $logger;
    }

    /**
     * @param Request $request
     * @param JobType $type
     * @param ApiSecurityUser $user
     * @param array $pattern
     * @return ImportHandler
     * @throws InvalidStructureException
     */
    public function getHandler(
        Request $request,
        JobType $type,
        ApiSecurityUser $user,
        array $pattern = []
    ): ImportHandler {
        try {
            $this->uploader->check($request);
        } catch (\Throwable $e) {
            throw new BadRequestHttpException($e->getMessage(), $e);
        }

        /** @var UploadedFile $file */
        $fileName = $type->getValue();
        if ($fileName === JobType::PRODUCTS()->getValue() && $user->getCompanyId() !== null) {
            $fileName = $fileName . '_' . $user->getCompanyId();
        }
        $file = $this->uploader->upload($request, $fileName);

        $handler = fopen($file->getPathname(), 'r');

        $csv = new CsvFile($handler, $pattern);
        unlink($file->getPathname());

        $countLines = $csv->countLines();

        if ($countLines === 0) {
            throw new LogicException(__('exim_error_import_file_without_lines'));
        }

        $job = $this->jobService->create(
            $type,
            $user->getId(),
            $user->getCompanyId(),
            $file->getClientOriginalName(),
            $countLines
        );

        return new ImportHandler($job, $csv->generator(), $pattern);
    }

    /**
     * @param Request $request
     * @param JobType $type
     * @param int $userId
     * @param int|null $companyId
     * @param array $pattern
     * @param string $separator
     * @return ImportHandler
     * @throws InvalidStructureException
     */
    public function getHandlerFromLegacy(
        Request $request,
        JobType $type,
        int $userId,
        ?int $companyId,
        array $pattern,
        string $separator = ';'
    ): ImportHandler {
        $pendingJobIds = $this->jobService->getPendingImportJobIds($companyId);
        if (true === $this->jobService->hasPendingJobIds($companyId, $pendingJobIds)) {
            throw new ImportAlreadyExistsException($pendingJobIds);
        }

        if ($request->files->has(static::CSV_FILE) === true && \count($request->files->get(static::CSV_FILE)) > 0) {
            $request->files->set('file', current($request->files->get(static::CSV_FILE)));
            $request->files->remove(static::CSV_FILE);
            $request->request->remove(static::CSV_FILE);
        } elseif ($request->request->has(static::CSV_FILE) === true && \count($request->request->get(static::CSV_FILE)) > 0) {
            $request->request->set('file', $request->request->all()[static::CSV_FILE][0]);
        } else {
            throw new BadRequestHttpException('parameter file_csv_file is missing');
        }

        $request->request->remove('section');
        $request->request->remove('pattern_id');
        $request->request->remove('result_ids');
        $request->request->remove(static::CSV_FILE);
        $request->request->remove('type_csv_file');
        $request->request->remove('import_options');
        $request->request->remove('dispatch');
        $request->request->remove('is_ajax');

        try {
            $this->uploader->check($request);
        } catch (\Throwable $e) {
            throw new BadRequestHttpException($e->getMessage());
        }

        /** @var UploadedFile $file */
        $fileName = $type->getValue();
        if ($fileName === JobType::PRODUCTS()->getValue() && $companyId !== null) {
            $fileName = $fileName . '_' . $companyId;
        }
        $file = $this->uploader->upload($request, $fileName);

        if ($request->files->has('file') === true) {
            $handler = $this->uploader->getHandler($file->getPathname());
        } else {
            $handler = $this->uploader->getHandler($request->request->get('file'));
        }

        $csv = new CsvFile($handler, $pattern, $separator);
        $countLines = $csv->countLines();

        if ($countLines === 0) {
            throw new LogicException(__('exim_error_import_file_without_lines'));
        }

        $job = $this->jobService->create(
            $type,
            $userId,
            $companyId,
            $file->getClientOriginalName(),
            $countLines
        );

        return new ImportHandler($job, $csv->generator(), $pattern);
    }

    /**
     * @param mixed[] $companyFeeds
     *
     * @return mixed[]
     */
    public function getHandlersFromAutomatedFeeds(int $companyId, array $companyFeeds): array
    {
        $company = new Company($companyId);
        $user = $company->getFirstAdmin();

        $pendingJobIds = $this->jobService->getPendingImportJobIds($companyId);
        if (true === $this->jobService->hasPendingJobIds($companyId, $pendingJobIds)) {
            throw new LogicException(__('exim_error_import_already_exist'));
        }

        $automatedFeedHandlers = [];
        foreach ($companyFeeds as $feed) {
            switch ($feed['pattern_id']) {
                case static::PATTERN_ID_PRODUCTS:
                    $jobType = JobType::PRODUCTS();
                    $importType = 'products';
                    break;
                case static::PATTERN_ID_INVENTORY_PRICE:
                    $jobType = JobType::PRODUCT_PRICES();
                    $importType = 'prices';
                    break;
                case static::PATTERN_ID_INVENTORY_QUANTITIES:
                    $jobType = JobType::PRODUCT_QUANTITIES();
                    $importType = 'quantities';
                    break;
                default:
                    throw new AutomatedFeedNotImplementedException('Pattern not found');
            }

            try {
                $importHandler = $this->createHandlerForAutomatedFeed($user->getId(), $companyId, $jobType, $feed);
                $automatedFeedHandlers[] = ['handler' => $importHandler, 'importType' => $importType];
            } catch (\Exception $exception) {
                $this->logger->error(
                    'Automated Feeds: import failed',
                    [
                        'company_id' => $companyId,
                        'url' => $feed['url'],
                        'code' => $exception->getCode(),
                        'message' => $exception->getMessage(),
                    ]
                );
            }
        }

        return $automatedFeedHandlers;
    }

    /** @param mixed[] $feed */
    private function createHandlerForAutomatedFeed(int $userId, int $companyId, JobType $jobType, array $feed): ImportHandler
    {
        $pattern = fn_get_schema('exim', $feed['pattern_id']);
        $request = new Request();
        $request->request->set('file', $feed['url']);

        try {
            $this->uploader->check($request);
        } catch (\Throwable $e) {
            throw new BadRequestHttpException($e->getMessage(), $e);
        }

        /** @var UploadedFile $file */
        $fileName = $jobType->getValue() . '_' . $companyId;
        $file = $this->uploader->upload($request, $fileName);
        $handler = $this->uploader->getHandler($request->request->get('file'));
        $csv = new CsvFile($handler, $pattern);
        unlink($file->getPathname());
        $countLines = $csv->countLines();

        if ($countLines === 0) {
            throw new LogicException(__('exim_error_import_file_without_lines'));
        }

        $job = $this->jobService->create(
            $jobType,
            $userId,
            $companyId,
            $file->getClientOriginalName(),
            $countLines
        );

        return new ImportHandler($job, $csv->generator(), $pattern);
    }
}
