<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Import;

use Doctrine\ORM\QueryBuilder;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Monolog\Logger;
use Symfony\Component\HttpFoundation\Response;
use Wizacha\Component\Import\Exception\EximJobNotFoundException;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Storage\StorageService;

class EximJobService
{
    public const LOG_DATE_FORMAT = 'Y-m-d H:i:s';

    /**
     * @var EximJobRepository
     */
    private $jobRepository;
    /**
     * @var EximJobLogRepository
     */
    private $logRepository;

    private ?StorageService $storage;

    /**
     * @var array
     */
    private $showLevel = [Logger::ERROR, Logger::WARNING];

    public function __construct(
        EximJobRepository $jobRepository,
        EximJobLogRepository $logRepository,
        StorageService $storage
    ) {
        $this->jobRepository = $jobRepository;
        $this->logRepository = $logRepository;
        $this->storage = $storage;
    }

    public function create(
        JobType $type,
        int $userId,
        int $companyId = null,
        string $file = null,
        int $nbLines = 0
    ): EximJob {
        $job = new EximJob($type, $userId);
        $job->setCompanyId($companyId)
            ->setFile($file)
            ->setNbLines($nbLines);

        return $this->jobRepository->save($job);
    }

    public function save(EximJob $job): EximJob
    {
        return $this->jobRepository->save($job);
    }

    /**
     * @param string $jobId
     * @return EximJob
     * @throws NotFound
     */
    public function get(string $jobId): EximJob
    {
        /** @var EximJob $job */
        $job = $this->jobRepository->find($jobId);

        if (!$job) {
            throw new EximJobNotFoundException($jobId);
        }

        return $job;
    }

    /**
     * Get all pending jobs from a company
     *
     * @return string[]
     */
    public function getPendingImportJobIds(?int $companyId): array
    {
        return $this->jobRepository->getPendingImportJobIdsByCompany($companyId);
    }

    /**
     * @return string[]
     */
    public function getPendingExportJobIds(?int $companyId): array
    {
        return $this->jobRepository->getPendingExportJobIdsByCompany($companyId);
    }

    /**
     * Checks if the companyId is > 0:
     * - For an admin user, this will accept the new import and add it to the queue even if other imports are pending
     * - For a vendor user, this will refuse the new import with an error message
     *
     * Then if yes checks if the pendingJobs array is not empty
     *
     * @param int|null $companyId
     * @param string[] $pendingJobs
     * @return bool
     */
    public function hasPendingJobIds(?int $companyId, array $pendingJobs): bool
    {
        return $companyId !== null && $companyId > 0 && \count($pendingJobs) > 0;
    }

    /**
     * @param int $start
     * @param int $max
     * @param callable|null $callback
     * @return Paginator
     */
    public function getJobsPaginate(
        int $start = EximJobRepository::START,
        int $max = EximJobRepository::LIMIT,
        callable $callback = null
    ): Paginator {
        return $this->jobRepository->paginate($start, $max, $callback);
    }

    /**
     * @param int $start
     * @param int $limit
     * @param string $jobId
     * @param int|null $code
     * @return Paginator
     */
    public function getLogsPaginate(
        int $start = EximJobLogRepository::START,
        int $limit = EximJobLogRepository::LIMIT,
        string $jobId,
        int $code = null
    ): Paginator {
        return $this
            ->logRepository
            ->paginate(
                $start,
                $limit,
                function (QueryBuilder $query) use ($jobId, $code) {
                    $query->where('l.jobId = :jobId')->setParameter('jobId', $jobId);

                    if ($code && \in_array($code, $this->showLevel)) {
                        $query->andWhere('l.code = :code')->setParameter('code', $code);
                    } else {
                        $query->andWhere('l.code != :code')->setParameter('code', Logger::INFO);
                    }
                }
            )
        ;
    }


    public function getFile(string $path): Response
    {
        return $this->storage->get($path);
    }

    /**
     * Get a report to csv format
     *
     * @param EximJob $job
     * @return string|null the file path to the report
     */
    public function getReport(EximJob $job): ?string
    {
        $logs = $this->logRepository->findBy(['jobId' => $job->getId()]);

        if (\count($logs) === 0) {
            return null;
        }

        $file = tempnam(sys_get_temp_dir(), 'report');
        $handle = fopen($file, 'w');

        // Set first line
        fputcsv($handle, array_keys(current($logs)->expose()), ';');

        // Set all logs
        array_map(function (EximJobLog $log) use ($handle) {
            fputcsv($handle, $log->expose(), ';');
        }, $logs);

        fclose($handle);

        return $file;
    }


    /* Delete job, logs and file */
    public function delete(EximJob $job): void
    {
        if (\is_string($job->getFile())) {
            $this->storage->delete($job->getFile());
        }

        $this->jobRepository->delete($job);
    }

    /** Count all jobs */
    public function count(): int
    {
        return $this->jobRepository->count([]);
    }

    /**
     * Is use to mark that the line was rejected
     *
     * @param mixed $line
     * @param string[]|null $additionalData
     */
    public static function error(
        string $message,
        ?string $jobId,
        $line,
        int $entityId = null,
        string $extra = null,
        array $additionalData = null
    ) {
        $log = container()->get('marketplace.import.logger');

        if ($jobId) {
            $log->error($message, ['log' => new EximJobLog($jobId, (string) $line, $entityId, $extra, $additionalData)]);
        }
    }

    /**
     * Is use to mark that the line contains problems
     * Is catch by Wizacha\Component\Import\Handler\EximJobHandler to increment the "nbWarnings"
     *
     * @param mixed $line
     * @param string[]|null $additionalData
     */
    public static function warning(
        string $message,
        ?string $jobId,
        $line,
        int $entityId = null,
        string $extra = null,
        array $additionalData = null
    ) {
        $log = container()->get('marketplace.import.logger');

        if ($jobId) {
            $log->warning($message, ['log' => new EximJobLog($jobId, (string) $line, $entityId, $extra, $additionalData)]);
        }
    }

    /**
     * Is use to mark that the line was imported
     * Is catch by Wizacha\Component\Import\Handler\EximJobHandler to increment the "nbImported"
     *
     * @param mixed $line
     * @param string[]|null $additionalData
     */
    public static function info(
        string $message,
        ?string $jobId,
        $line,
        int $entityId = null,
        string $extra = null,
        array $additionalData = null
    ) {
        $log = container()->get('marketplace.import.logger');

        if ($jobId) {
            $log->info(
                $message,
                [
                    'log' => new EximJobLog(
                        $jobId,
                        (string) $line,
                        $entityId,
                        $extra,
                        $additionalData
                    )
                ]
            );
        }
    }

    public function getProcessDurationInfo(EximJob $job): array
    {
        $info = $this->logRepository->getLogBounds($job->getId());
        [$firstLog, $lastLog] = \array_values(
            \array_map(
                function (?string $dateString): ?\DateTimeInterface {
                    return \is_string($dateString)
                        ? \DateTimeImmutable::createFromFormat(
                            'Y-m-d H:i:s',
                            $dateString
                        )
                        : null
                    ;
                },
                $info
            )
        );

        if (false === ($firstLog instanceof \DateTimeInterface
            && $lastLog instanceof \DateTimeInterface)
        ) {
            return [];
        }

        return [
            'workDuration' => $lastLog->getTimestamp() - $firstLog->getTimestamp(),
            'waitDuration' => $firstLog->getTimestamp() - $job->getCreatedAt()->getTimeStamp(),
        ];
    }

    public function getCountProcessedLines(EximJob $job): int
    {
        return $this->logRepository->countProcessedLines($job->getId());
    }

    public function cancel(EximJob $job): void
    {
        $this->jobRepository->cancel($job);
    }

    /** @return EximJob[] */
    public function getCompanyJobByStatus(?int $companyId, ?JobStatus $status): array
    {
        return $this->jobRepository->getCompanyJobByStatus($companyId, $status);
    }
}
