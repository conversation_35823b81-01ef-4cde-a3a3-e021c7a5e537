<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Import;

use MyCLabs\Enum\Enum;

/**
 * @method static JobStatus SUCCESS()
 * @method static JobStatus ERROR()
 * @method static JobStatus WARNING()
 * @method static JobStatus PENDING()
 * @method static JobStatus CANCELED()
 */
class JobStatus extends Enum
{
    public const SUCCESS   = 'S';
    public const ERROR    = 'E';
    public const WARNING  = 'W';
    public const PENDING   = 'P';
    public const CANCELED   = 'C';
}
