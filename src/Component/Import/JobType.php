<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Import;

use MyCLabs\Enum\Enum;

/**
 * @method static JobType PRODUCTS()
 * @method static JobType ATTRIBUTES()
 * @method static JobType VARIANTS()
 * @method static JobType LINK_CATEGORIES()
 * @method static JobType AVAILABLE_OFFERS()
 * @method static JobType CATEGORIES()
 * @method static JobType MULTI_VENDOR_PRODUCTS()
 * @method static JobType ORDERS()
 * @method static JobType TRANSLATIONS()
 * @method static JobType NEWSLETTER()
 * @method static JobType ACCOUNTING()
 * @method static JobType COMPANIES()
 * @method static JobType USERS()
 * @method static JobType PRODUCT_PRICES()
 * @method static JobType PRODUCT_QUANTITIES()
 * @method static JobType MARKETPLACE_DISCOUNT()
 * @method static JobType GROUP_USERS()
 * @method static JobType RELATED_PRODUCTS()
 * @method static JobType PRODUCT_ATTRIBUTES()
 */
class JobType extends Enum
{
    public const PRODUCTS = 'Products';
    public const ATTRIBUTES = 'Attributes';
    public const VARIANTS = 'Variants';
    public const LINK_CATEGORIES = 'Link Categories';
    public const AVAILABLE_OFFERS = 'Available Offers';
    public const CATEGORIES = 'Categories';
    public const MULTI_VENDOR_PRODUCTS = 'Multi Vendor Products';
    public const ORDERS = 'Orders';
    public const TRANSLATIONS = 'Translations';
    public const NEWSLETTER = 'Newsletter';
    public const ACCOUNTING = 'Accounting';
    public const COMPANIES = 'Companies';
    public const USERS = 'Users';
    public const PRODUCT_PRICES = 'Inventory Prices';
    public const PRODUCT_QUANTITIES = 'Product Quantities';
    public const MARKETPLACE_DISCOUNTS = 'Marketplace Discounts';
    public const GROUP_USERS = 'Group Users';
    public const RELATED_PRODUCTS = 'Related Products';
    public const PRODUCT_ATTRIBUTES = 'Product Attributes';
}
