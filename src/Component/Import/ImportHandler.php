<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Import;

class ImportHandler
{
    /**
     * @var EximJob
     */
    private $job;
    /**
     * @var resource
     */
    private $iterator;
    /**
     * @var array
     */
    private $pattern;

    public function __construct(EximJob $job, \Iterator $iterator, array $pattern = [])
    {
        $this->job = $job;
        $this->iterator = $iterator;
        $this->pattern = $pattern;
    }

    /**
     * @return EximJob
     */
    public function getJob(): EximJob
    {
        return $this->job;
    }

    /**
     * @return \Iterator
     */
    public function getIterator(): \Iterator
    {
        return $this->iterator;
    }

    /**
     * @return array
     */
    public function getPattern(): array
    {
        return $this->pattern;
    }
}
