Wizacha\Component\Import\EximJob:
    type: entity
    table: exim_jobs
    id:
        id:
            type: guid
            generator:
                strategy: UUID
    fields:
        type:
            type: string
            nullable: false
        status:
            type: string
            nullable: false
        userId:
            type: integer
            nullable: false
        companyId:
            type: integer
            nullable: true
        file:
            type: string
            nullable: true
        createdAt:
            type: datetime
            nullable: false
        startedAt:
            type: datetime
            nullable: true
        finishedAt:
            type: datetime
            nullable: true
        nbLines:
            type: integer
            nullable: false
        nbImported:
            type: integer
            nullable: false
        nbWarnings:
            type: integer
            nullable: false
        isImport:
            type: boolean
            nullable: false
