<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Import;

class Counter
{
    /** @var int */
    private $infos = 0;

    /** @var int  */
    private $warnings = 0;

    /** @var int  */
    private $errors = 0;

    public function setInfos(int $count): void
    {
        $this->infos = $count;
    }

    public function setWarnings(int $count): void
    {
        $this->warnings = $count;
    }

    public function setErrors(int $count): void
    {
        $this->errors = $count;
    }

    public function getInfos(): int
    {
        return $this->infos;
    }

    public function getWarnings(): int
    {
        return $this->warnings;
    }

    public function getErrors(): int
    {
        return $this->errors;
    }

    public function getTotal(): int
    {
        return $this->infos + $this->errors;
    }
}
