<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Import\Exception;

use Wizacha\Marketplace\Exception\NotFound;

class EximJobNotFoundException extends NotFound
{
    public string $jobId;

    public function __construct(string $jobId)
    {
        $this->jobId = $jobId;

        parent::__construct(
            \sprintf(
                '%s EximJob not found (gone)',
                $jobId
            )
        );
    }
}
