<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Import;

use Monolog\Logger;

class EximJobLog
{
    /**
     * @var null|int
     */
    private $id;

    /**
     * @var string
     */
    private $jobId;

    /**
     * @var int|null
     */
    private $entityId;

    /**
     * @var string
     */
    private $line;

    /**
     * @var int
     */
    private $code;

    /**
     * @var string
     */
    private $message;

    /**
     * @var null|string
     */
    private $extra;

    /**
     * @var \DateTimeInterface
     */
    private $createdAt;

    /** @var string[]|null  */
    private ?array $additionalData;

    /** @param string[]|null $additionalData */
    public function __construct(
        string $jobId,
        string $line,
        int $entityId = null,
        string $extra = null,
        ?array $additionalData = null
    ) {
        $this->jobId = $jobId;
        $this->entityId = $entityId;
        $this->line = $line;
        $this->extra = $extra;
        $this->createdAt = new \DateTime();
        $this->additionalData = $additionalData;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getJobId(): string
    {
        return $this->jobId;
    }

    public function setJobId(string $jobId): self
    {
        $this->jobId = $jobId;

        return $this;
    }

    public function getEntityId(): ?int
    {
        return $this->entityId;
    }

    public function setEntityId(?int $entityId): self
    {
        $this->entityId = $entityId;

        return $this;
    }

    public function getLine(): string
    {
        return $this->line;
    }


    public function getLastLine(): ?int
    {
        return (int) end(explode(',', $this->getLine()));
    }

    public function setLine(int $line): self
    {
        $this->line = $line;

        return $this;
    }

    public function getCode(): int
    {
        return $this->code;
    }

    public function setCode(int $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getStringCode(): string
    {
        return Logger::getLevelName($this->getCode());
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function setMessage(string $message): self
    {
        $this->message = $message;

        return $this;
    }

    public function getExtra(): ?string
    {
        return $this->extra;
    }

    public function setExtra(?string $extra): self
    {
        $this->extra = $extra;

        return $this;
    }

    public function getCreatedAt(): \DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    /** @return string[]|null */
    public function getAdditionalData(): ?array
    {
        return $this->additionalData;
    }

    public function expose(): array
    {
        $jobDetails = [
            'line' => $this->getLine(),
            'message' => __($this->getMessage(), ['%' => $this->getExtra()]),
            'code' => $this->getStringCode(),
            'entityId' => $this->getEntityId(),
        ];

        if (null !== $this->additionalData) {
            $jobDetails = \array_merge($jobDetails, $this->additionalData);
        }

        return $jobDetails;
    }
}
