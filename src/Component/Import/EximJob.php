<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Import;

class EximJob
{
    /** @var string */
    private $id;

    /** @var JobType */
    private $type;

    /** @var JobStatus */
    private $status;

    /** @var int */
    private $userId;

    /** @var int|null */
    private $companyId = null;

    /** @var string|null */
    private $file = null;

    /** @var \DateTimeInterface */
    private $createdAt;

    /** @var null|\DateTimeInterface */
    private $startedAt;

    /** @var null|\DateTimeInterface */
    private $finishedAt;

    /** @var int */
    private $nbLines = 0;

    /** @var int */
    private $nbImported = 0;

    /** @var int */
    private $nbWarnings = 0;

    /** @var bool */
    private $isImport;

    public function __construct(JobType $type, int $userId, bool $isImport = true)
    {
        $this->type = $type;
        $this->userId = $userId;
        $this->status = JobStatus::PENDING();
        $this->createdAt = new \DateTime();
        $this->isImport = $isImport;
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(string $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getType(): JobType
    {
        if ($this->type instanceof JobType) {
            return $this->type;
        }

        return new JobType($this->type);
    }

    public function setType(JobType $type): self
    {
        $this->type = $type;

        return $this;
    }

    public function getStatus(): JobStatus
    {
        if ($this->status instanceof JobStatus) {
            return $this->status;
        }

        return new JobStatus($this->status);
    }

    public function setStatus(JobStatus $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getUserId(): int
    {
        return $this->userId;
    }

    public function setUserId(int $userId): self
    {
        $this->userId = $userId;

        return $this;
    }

    public function getCompanyId(): ?int
    {
        return $this->companyId;
    }

    public function setCompanyId(?int $companyId): self
    {
        $this->companyId = $companyId;

        return $this;
    }

    public function getFile(): ?string
    {
        return $this->file;
    }

    public function setFile(?string $file): self
    {
        $this->file = $file;

        return $this;
    }

    public function getCreatedAt(): \DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;

        return $this;
    }

    public function getStartedAt(): ?\DateTimeInterface
    {
        return $this->startedAt;
    }

    public function setStartedAt(\DateTimeInterface $startedAt): self
    {
        $this->startedAt = $startedAt;

        return $this;
    }

    public function getFinishedAt(): ?\DateTimeInterface
    {
        return $this->finishedAt;
    }

    public function setFinishedAt(?\DateTimeInterface $finishedAt): void
    {
        $this->finishedAt = $finishedAt;
    }

    public function getNbLines(): int
    {
        return $this->nbLines;
    }

    public function setNbLines(int $nbLines): self
    {
        $this->nbLines = $nbLines;

        return $this;
    }

    public function getNbImported(): int
    {
        return $this->nbImported;
    }

    public function setNbImported(int $nbImported): self
    {
        $this->nbImported = $nbImported;

        return $this;
    }

    public function getNbRejected(): int
    {
        return $this->getNbLines() - $this->getNbImported();
    }

    public function getNbWarnings(): int
    {
        return $this->nbWarnings;
    }

    public function setNbWarnings(int $nbWarnings): self
    {
        $this->nbWarnings = $nbWarnings;

        return $this;
    }

    public function getPercent(): float
    {
        if ($this->getNbLines() === 0) {
            return 0;
        }

        return round($this->getNbImported() / $this->getNbLines() * 100);
    }

    public function finish()
    {
        $this->finishedAt = new \DateTime();
    }

    public function getDuration(): int
    {
        if (false === ($this->getFinishedAt() instanceof \DateTimeInterface)) {
            return 0;
        }

        return $this->getFinishedAt()->getTimestamp() - $this->getCreatedAt()->getTimestamp();
    }

    public function expose(): array
    {
        return [
            'id' => $this->getId(),
            'type' => $this->getType()->getKey(),
            'status' => $this->getStatus()->getKey(),
            'userId' => $this->getUserId(),
            'companyId' => $this->getCompanyId(),
            'nbLines' => $this->getNbLines(),
            'nbImported' => $this->isImport() ? $this->getNbImported() : 0,
            'nbExported' => $this->isImport() ? 0 : $this->getNbImported(),
            'nbRejected' => $this->getNbRejected(),
            'nbWarnings' => $this->getNbWarnings(),
            'created' => $this->getCreatedAt()->format(\DateTime::RFC3339),
            'started' => $this->getStartedAt() === null ? null : $this->getStartedAt()->format(\DateTime::RFC3339),
            'finished' => $this->getFinishedAt() === null ? null : $this->getFinishedAt()->format(\DateTime::RFC3339),
            'duration' => $this->getDuration(),
            'isImport' => $this->isImport(),
        ];
    }

    public function isImport(): bool
    {
        return $this->isImport;
    }

    public function setIsImport(bool $isImport): void
    {
        $this->isImport = $isImport;
    }
}
