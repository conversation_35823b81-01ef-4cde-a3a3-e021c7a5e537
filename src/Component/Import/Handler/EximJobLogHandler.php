<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Import\Handler;

use Doctrine\ORM\EntityManager;
use Monolog\Handler\AbstractProcessingHandler;
use Monolog\Logger;
use Wizacha\Component\Import\Exception\MissingLogImportException;
use Wizacha\Component\Import\EximJobLog;

class EximJobLogHandler extends AbstractProcessingHandler
{
    /**
     * @var EntityManager
     */
    private $entityManager;

    public function __construct(EntityManager $entityManager)
    {
        parent::__construct(Logger::DEBUG, true);
        $this->entityManager = $entityManager;
    }

    /**
     * @param array $record
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    protected function write(array $record): void
    {
        if (!$record['context']['log'] instanceof EximJobLog) {
            throw new MissingLogImportException();
        }

        /** @var EximJobLog $log */
        $log = $record['context']['log'];
        $log->setCode($record['level']);
        $log->setMessage($record['message']);
        $log->setCreatedAt($record['datetime']);

        $this->entityManager->persist($log);
        $this->entityManager->flush();
    }
}
