<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Import\Handler;

use Monolog\Handler\AbstractProcessingHandler;
use Monolog\Logger;
use Wizacha\Component\Import\Exception\MissingLogImportException;
use Wizacha\Component\Import\EximJob;
use Wizacha\Component\Import\EximJobLog;
use Wizacha\Component\Import\EximJobLogRepository;
use Wizacha\Component\Import\EximJobRepository;
use Wizacha\Component\Import\JobStatus;

class EximJobHandler extends AbstractProcessingHandler
{
    /**
     * @var EximJobRepository
     */
    private $jobRepository;
    /**
     * @var EximJobLogRepository
     */
    private $logRepository;

    public function __construct(EximJobRepository $jobRepository, EximJobLogRepository $logRepository)
    {
        parent::__construct(Logger::DEBUG, true);

        $this->jobRepository = $jobRepository;
        $this->logRepository = $logRepository;
    }


    /**
     * Writes the record down to the log of the implementing handler
     *
     * @param  array $record
     * @return void
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    protected function write(array $record)
    {
        if (!$record['context']['log'] instanceof EximJobLog) {
            throw new MissingLogImportException();
        }

        /** @var EximJobLog $log */
        $log = $record['context']['log'];

        $this->jobRepository->increment($log->getJobId(), $log->getCode());
        $this->setStatus($log->getJobId());
    }

    /**
     * When the import as finished, set status job
     *
     * @param string $jobId
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    private function setStatus(string $jobId): void
    {
        /** @var EximJob $job */
        $job = $this->jobRepository->find($jobId);

        if ($job->getStartedAt() === null) {
            $job->setStartedAt(new \DateTime());
            $this->jobRepository->save($job);
        }

        $counter = $this->logRepository->countByJobId($jobId);
        $total = $this->logRepository->countProcessedLines($jobId);

        if ($total === $job->getNbLines()) {
            if ($counter->getErrors() > 0) {
                $status = JobStatus::ERROR();
            } elseif ($counter->getWarnings() > 0) {
                $status = JobStatus::WARNING();
            } else {
                $status = JobStatus::SUCCESS();
            }

            $job->setStatus($status);
            $job->finish();

            $this->jobRepository->save($job);
        }
    }
}
