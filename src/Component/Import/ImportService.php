<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Import;

use Symfony\Component\HttpFoundation\JsonResponse;
use Wizacha\Async\Dispatcher;
use Wizacha\Exim\Import\Entities\EntitiesImporter;
use Wizacha\Exim\Import\Product\Csv;
use Wizacha\Exim\Import\Product\ProductImporter;
use Wizacha\Exim\Import\Inventory\Csv as CsvInventory ;
use Wizacha\Exim\Import\Inventory\Quantities;
use Wizacha\Exim\Import\Inventory\Prices;
use Wizacha\Async\Config;
use Wizacha\Exim\Import\TranslationImporter;

class ImportService
{
     /** @var ProductImporter */
    private $importer;

    /** @var Dispatcher */
    private $dispatcher;

    /** @var Prices */
    private $pricesImporter;

    /** @var Quantities */
    private $quantitiesImporter;

    private TranslationImporter $translationImporter;

    public function __construct(
        ProductImporter $importer,
        Dispatcher $dispatcher,
        Prices $priceImporter,
        Quantities $quantitiesImporter,
        TranslationImporter $translationImporter
    ) {
        $this->importer = $importer;
        $this->dispatcher = $dispatcher;
        $this->pricesImporter = $priceImporter;
        $this->quantitiesImporter = $quantitiesImporter;
        $this->translationImporter = $translationImporter;
    }

    /**
     * @param ImportHandler $handler
     * @throws \Exception
     */
    public function catalog(ImportHandler $handler): void
    {
        $iterator = new Csv($handler->getIterator());
        $pattern = $handler->getPattern();
        $options = $this->formatOptions($pattern);

        /** @var \Wizacha\Exim\Import\Product $product */
        foreach ($iterator as $product) {
            foreach ($product->getLocales() as $locale) {
                $this->importer->import(
                    $product->getTranslation($locale),
                    $options,
                    $locale,
                    $pattern[\Wizacha\Async\Config::FIELD_PROGRESS_ID] = [],
                    (string) $handler->getJob()->getId(),
                    $handler->getJob()->getId(),
                    $product->getLines($locale)
                );
            }
        }
    }

    public function translations(ImportHandler $handler): void
    {
        $this->translationImporter->startImport($handler);
    }

    public function entities(ImportHandler $handler)
    {
        $pattern = $handler->getPattern();
        $options = $this->formatOptions($pattern);
        $importer = new EntitiesImporter($pattern, $options, $handler->getJob());

        foreach ($handler->getIterator() as $line) {
            if ($this->dispatcher->delayExec(
                [$importer, 'import'],
                [$line['line'], $line],
                \Wizacha\Registry::defaultInstance(),
                ['#progress_id' => $handler->getJob()->getId()]
            )
            ) {
                continue;
            }

            $importer->import($line['line'], $line);
        }
    }

    public function importInventory(ImportHandler $handler, string $importType): void
    {
        $iterator = new CsvInventory($handler->getIterator());
        $pattern = $handler->getPattern();
        $options = $this->formatOptions($pattern);

        /** @var \Wizacha\Exim\Import\Inventory $inventory */
        foreach ($iterator as $inventory) {
            foreach ($inventory->getLocales() as $locale) {
                if ($importType === 'quantities') {
                    $this->quantitiesImporter->import(
                        $inventory->getTranslation($locale),
                        $options,
                        $locale,
                        $pattern[Config::FIELD_PROGRESS_ID] = [],
                        $handler->getJob()->getId(),
                        $inventory->getLines($locale)
                    );
                } else {
                    $this->pricesImporter->import(
                        $inventory->getTranslation($locale),
                        $options,
                        $locale,
                        $pattern[Config::FIELD_PROGRESS_ID] = [],
                        $handler->getJob()->getId(),
                        $inventory->getLines($locale)
                    );
                }
            }
        }
    }

    private function formatOptions(array &$pattern = []): array
    {
        array_walk(
            $pattern['options'],
            function (&$value, $key) {
                $value['option_name'] = $key;
            }
        );

        return array_column(
            array_filter(
                $pattern['options'],
                function ($option) {
                    return !empty($option['description']);
                }
            ),
            'default_value',
            'option_name'
        );
    }
}
