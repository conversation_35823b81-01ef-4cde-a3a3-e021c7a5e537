<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Import;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Monolog\Logger;

class EximJobRepository extends ServiceEntityRepository
{
    public const START = 0;
    public const LIMIT = 100;

    /**
     * @param EximJob $job
     * @return EximJob
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function save(EximJob $job): EximJob
    {
        $this->getEntityManager()->persist($job);
        $this->getEntityManager()->flush();

        return $job;
    }

    /**
     * @param EximJob $job
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function delete(EximJob $job)
    {
        $this->getEntityManager()->remove($job);
        $this->getEntityManager()->flush();
    }

    /**
     * Paginate data and add filters from callback
     * @param int $start
     * @param int $limit
     * @param callable|null $callback
     * @return Paginator
     */
    public function paginate(int $start = self::START, int $limit = self::LIMIT, callable $callback = null): Paginator
    {
        $query = $this->createQueryBuilder('job');
        $query->select('job');
        $query->setFirstResult($start);
        $query->setMaxResults($limit);
        $query->orderBy('job.createdAt', 'DESC');

        if ($callback) {
            \call_user_func($callback, $query);
        }

        return new Paginator($query);
    }

    public function increment(string $jobId, int $code): void
    {
        switch ($code) {
            case Logger::WARNING:
                $query = $this->getEntityManager()->createQuery(
                    "UPDATE Wizacha\Component\Import\EximJob j SET j.nbWarnings = j.nbWarnings + 1 WHERE j.id = :id"
                );
                break;
            case Logger::INFO:
                $query = $this->getEntityManager()->createQuery(
                    "UPDATE Wizacha\Component\Import\EximJob j SET j.nbImported = j.nbImported + 1 WHERE j.id = :id"
                );
                break;
        }

        if (isset($query)) {
            $query->setParameter('id', $jobId)->execute();
            $this->clear();
        }
    }

    /**
     * @return string[]
     */
    public function getPendingImportJobIdsByCompany(?int $companyId): array
    {
        $queryBuilder = $this->createQueryBuilder('job')
            ->select('job.id');

        $queryBuilder->where($queryBuilder->expr()->eq('job.companyId', ':companyId'))
            ->andWhere($queryBuilder->expr()->eq('job.status', ':jobStatus'))
            ->andWhere($queryBuilder->expr()->eq('job.isImport', ':isImport'))
            ->orderBy('job.createdAt', 'DESC')
            ->setParameter('companyId', $companyId)
            ->setParameter('isImport', true)
            ->setParameter('jobStatus', JobStatus::PENDING()->getValue());

        $results = $queryBuilder->getQuery()->getScalarResult();

        return array_column($results, 'id');
    }

    /**
     * @return string[]
     */
    public function getPendingExportJobIdsByCompany(?int $companyId): array
    {
        $queryBuilder = $this->createQueryBuilder('job')
            ->select('job.id');

        $queryBuilder->where($queryBuilder->expr()->eq('job.companyId', ':companyId'))
            ->andWhere($queryBuilder->expr()->eq('job.status', ':jobStatus'))
            ->andWhere($queryBuilder->expr()->eq('job.isImport', ':isImport'))
            ->orderBy('job.createdAt', 'DESC')
            ->setParameter('companyId', $companyId)
            ->setParameter('jobStatus', JobStatus::PENDING()->getValue())
            ->setParameter('isImport', false);

        $results = $queryBuilder->getQuery()->getScalarResult();

        return array_column($results, 'id');
    }

    public function cancel(EximJob $job): EximJob
    {
        $job->setStatus(JobStatus::CANCELED());
        $this->getEntityManager()->persist($job);
        $this->getEntityManager()->flush();

        return $job;
    }

    /** @return EximJob[] */
    public function getCompanyJobByStatus(?int $companyId, ?JobStatus $status): array
    {
        $query = $this->createQueryBuilder('job');
        $query->select();

        if (\is_int($companyId) === true) {
            $query->andWhere('job.companyId = :companyId')
                ->setParameter('companyId', $companyId)
            ;
        }

        if ($status instanceof JobStatus === true) {
            $query->andWhere('job.status = :status')
                ->setParameter('status', $status->getValue())
            ;
        }

        return $query
            ->getQuery()
            ->getResult();
    }
}
