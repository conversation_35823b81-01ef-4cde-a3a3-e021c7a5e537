<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Html;

class HtmlService
{
    private $httpHost;

    public function __construct(string $httpHost)
    {
        $this->httpHost = $httpHost;
    }

    public function transformImagesToAbsoluteUrl(string $content): string
    {
        if (empty($content)) {
            return $content;
        }

        // On convertit les caractères utf8 en entité HTML car DomDocument traite l'entrée comme ISO-8859-1
        $content = mb_convert_encoding($content, 'HTML-ENTITIES', 'UTF-8');

        $doc = new \DOMDocument();
        // désactive le reporting libxml des warnings, obligatoire pour permettre
        // à DOMDocument de charger des document HTML5, exemple de tag non
        // reconnu : <wbr>
        libxml_use_internal_errors(true);
        // ne pas inclure les tags <html> ni le <!doctype>
        $doc->loadHTML('<div>' . $content . '</div>', LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        // on vide les erreurs qui ont pu être levées pendant le load
        libxml_clear_errors();

        $container = $doc->getElementsByTagName('div')->item(0);
        $container = $container->parentNode->removeChild($container);

        while ($doc->firstChild) {
            $doc->removeChild($doc->firstChild);
        }

        while ($container->firstChild) {
            $doc->appendChild($container->firstChild);
        }

        foreach ($doc->getElementsByTagName('img') as $img) {
            $src = $this->relativeToAbsolute($img->getAttribute('src'), $this->httpHost);
            $img->setAttribute('src', $src);
        }

        return trim($doc->saveHTML());
    }

    private function relativeToAbsolute($rel, $base)
    {
        /* return if already absolute URL */
        if (parse_url($rel, PHP_URL_SCHEME) != '') {
            return $rel;
        }

        /* queries and anchors */
        if ($rel[0] == '#' || $rel[0] == '?') {
            return '//' . $base . $rel;
        }

        $path = '/';

        /* destroy path if relative url points to root */
        if ($rel[0] == '/') {
            $path = '';
        }

        /* dirty absolute URL */
        $abs = "$base$path$rel";

        /* replace '//' or '/./' or '/foo/../' with '/' */
        $re = array('#(/\.?/)#', '#/(?!\.\.)[^/]+/\.\./#');
        for ($n = 1; $n > 0; $abs = preg_replace($re, '/', $abs, -1, $n)) {
        }

        /* absolute URL is ready! */
        return '//' . $abs;
    }
}
