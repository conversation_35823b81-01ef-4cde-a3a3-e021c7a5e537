<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Language;

use Symfony\Component\OptionsResolver\OptionsResolver;
use Tygh\Languages\Languages;

/**
 * Normalize a "language" from DB to be exposed in API
 * As there is no language entity, we work with an array as input
 */
class LanguageNormalizer
{
    /**
     * @param array $language a single language from database
     */
    public function normalize(array $language): array
    {
        $language = (new OptionsResolver())
            ->setDefined('lang_id')
            ->setRequired(['name', 'lang_code', 'country_code', 'status'])
            ->resolve($language);

        return [
            'name' => $language['name'],
            'langCode' => $language['lang_code'],
            'countryCode' => $language['country_code'],
            'status' => $this->normalizeStatus($language['status']),
        ];
    }

    private function normalizeStatus(string $status): string
    {
        switch ($status) {
            case Languages::ACTIVE:
                return 'active';

            case Languages::HIDDEN:
                return 'hidden';

            case Languages::DISABLED:
                return 'disabled';
        }

        throw new \InvalidArgumentException("Status '$status' is not a valid language status");
    }
}
