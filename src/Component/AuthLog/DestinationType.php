<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\AuthLog;

use MyCLabs\Enum\Enum;

/**
 * @method static DestinationType FRONT()
 * @method static DestinationType VENDOR_BACKOFFICE()
 * @method static DestinationType ADMINISTRATOR_BACKOFFICE()
 */
class DestinationType extends Enum
{
    private const FRONT = 'FRONT';
    private const VENDOR_BACKOFFICE = 'VENDOR_BACKOFFICE';
    private const ADMINISTRATOR_BACKOFFICE = 'ADMINISTRATOR_BACKOFFICE';

    // mapping legacy ACCOUNT_VALUE to DestinationType enum
    public function mapLegacy(string $accountType): self
    {
        $map = [
            'admin' => self::ADMINISTRATOR_BACKOFFICE(),
            'vendor' => self::VENDOR_BACKOFFICE(),
            'customer' => self::FRONT(),
        ];

        if (!\array_key_exists($accountType, $map)) {
            throw new \Exception("No matching DestinationType for: $accountType");
        }

        return $map[$accountType];
    }
}
