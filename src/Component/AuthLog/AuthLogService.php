<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\AuthLog;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\QueryBuilder;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Wizacha\Component\AuthLog\AuthLog;
use Wizacha\Component\AuthLog\AuthLogRepository;
use Wizacha\Marketplace\Exception\NotFound;

class AuthLogService
{
    /** @var AuthLogRepository */
    private $authLogRepository;

    /** @var EntityManagerInterface */
    private $em;

    public function __construct(AuthLogRepository $authLogRepository, EntityManagerInterface $entityManager)
    {
        $this->authLogRepository = $authLogRepository;
        $this->em = $entityManager;
    }

    public function get(int $id): AuthLog
    {
        /** @var AuthLog $log */
        $authLog = $this->authLogRepository->find($id);

        if ($authLog instanceof AuthLog === false) {
            throw new NotFound();
        }

        return $authLog;
    }

    public function search(array $options): array
    {
        $paginate = $this->getAuthLogPaginate(
            (int) $options['start'],
            (int) $options['limit'],
            function (QueryBuilder $query) use ($options) {
                $this->callbackJob($query, $options);
            }
        );

        return [
            $paginate->getQuery()->getResult(),
            $paginate->count(),
        ];
    }

    // Memory friendly CSV export
    public function exportCSV(array $options): StreamedResponse
    {
        // query filters
        $callback = function (QueryBuilder $query) use ($options) {
            $this->callbackJob($query, $options);
        };
        $query = $this->authLogRepository->exportQuery($callback);

        // no limit
        set_time_limit(0);

        // pretty filename
        $filename = sprintf(
            'authlog-%s.csv',
            (new \DateTime())->format('Y-m-d')
        );

        $response = new StreamedResponse();
        $response->headers->set('Content-Type', 'text/csv');
        $response->headers->set(
            'Content-Disposition',
            sprintf('filename="%s"', $filename)
        );

        // output row per row
        $response->setCallback(
            function () use ($query) {
                $fh = fopen('php://output', 'w');

                // header
                fputcsv(
                    $fh,
                    [
                        __('auth_logs_id'),
                        __('auth_logs_createdat'),
                        __('auth_logs_login'),
                        __('auth_logs_source'),
                        __('auth_logs_destination'),
                        __('auth_logs_status'),
                    ],
                    ';'
                );

                foreach ($query->iterate() as $key => $row) {
                    fputcsv($fh, $row[0]->expose(), ';');
                    // send to browser per 4096 chunks
                    if ($key % 2 ** 12 == 0) {
                        ob_flush();
                    }
                    // remove entity from manager (free the RAM)
                    $this->em->detach($row[0]);
                }
                fclose($fh);
            }
        );

        return $response;
    }

    public function getAuthLogPaginate(
        int $start = AuthLogRepository::START,
        int $limit = AuthLogRepository::LIMIT,
        callable $callback = null
    ): Paginator {
        return $this->authLogRepository->paginate($start, $limit, $callback);
    }

    private function callbackJob(QueryBuilder $query, array $options = []): void
    {
        $parameters = [];

        // multiple values field filters
        $whereInKeys = [
            'login',
            'status',
            'source',
            'destination',
        ];
        foreach ($whereInKeys as $whereInKey) {
            $value = $options[$whereInKey];
            if (\is_array($value) || \is_string($value)) {
                $query->andWhere("log.$whereInKey IN(:$whereInKey)");
                $parameters[$whereInKey] = $value;
            }
        }

        // period filter
        $operatorMap = [
            'from' => '>=',
            'to' => '<',
        ];
        foreach ($options['period'] as $key => $val) {
            if (\is_string($val)) {
                $datetime = \DateTime::createFromFormat(\DateTime::RFC3339, $val);
                $query->andWhere("log.createdAt {$operatorMap[$key]} :$key");
                $parameters[$key] = $datetime;
            }
        }

        $query->setParameters($parameters);
        $query->orderBy("log.{$options['sort_by']}", $options['sort_order']);
    }
}
