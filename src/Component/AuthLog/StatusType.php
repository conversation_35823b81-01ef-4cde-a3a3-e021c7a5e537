<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\AuthLog;

use MyCLabs\Enum\Enum;

class StatusType extends Enum
{
    private const SUCCESS = 'SUCCESS';
    private const ACCESS_DENIED = 'ACCESS_DENIED';
    private const UNKNOWN_LOGIN = 'UNKNOWN_LOGIN';
    private const WRONG_PASSWORD = 'WRONG_PASSWORD';
    private const PENDING_ACCOUNT = 'PENDING_ACCOUNT';
    private const DESACTIVATED_ACCOUNT = 'DESACTIVATED_ACCOUNT';
}
