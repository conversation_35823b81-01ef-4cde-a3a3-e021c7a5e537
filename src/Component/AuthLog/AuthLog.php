<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\AuthLog;

class AuthLog
{
    /** @var int|null $id */
    private $id;

    /** @var \DateTime $createdAt */
    private $createdAt;

    /** @var string $login */
    private $login;

    /** @var StatusType $status */
    private $status;

    /** @var SourceType $source */
    private $source;

    /** @var DestinationType $destination */
    private $destination;

    public function __construct(
        string $login,
        StatusType $status,
        SourceType $source,
        DestinationType $destination
    ) {
        $this->createdAt = new \DateTime();
        $this->login = $login;
        $this->status = $status;
        $this->source = $source;
        $this->destination = $destination;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCreatedAt(): \DateTimeInterface
    {
        return $this->createdAt;
    }

    public function getLogin(): string
    {
        return $this->login;
    }

    public function getStatus(): StatusType
    {
        if ($this->status instanceof StatusType) {
            return $this->status;
        }

        return new StatusType($this->status);
    }

    public function getSource(): SourceType
    {
        if ($this->source instanceof SourceType) {
            return $this->source;
        }

        return new SourceType($this->source);
    }

    public function getDestination(): DestinationType
    {
        if ($this->destination instanceof DestinationType) {
            return $this->destination;
        }

        return new DestinationType($this->destination);
    }

    public function expose(): array
    {
        return [
            'id' => $this->getId(),
            'created' => $this->getCreatedAt()->format(\DateTime::RFC3339),
            'login' => $this->getLogin(),
            'source' => $this->getSource()->getKey(),
            'destination' => $this->getDestination()->getKey(),
            'status' => $this->getStatus()->getKey(),
        ];
    }
}
