<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\AuthLog;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Doctrine\Persistence\ManagerRegistry;
use Psr\Log\LoggerInterface;

class AuthLogRepository extends ServiceEntityRepository
{
    public const START = 0;
    public const LIMIT = 100;
    public const MAX = 1048575 ; // excel/libreoffice max, 2^20 - 1 (csv header)
    public const TTL_MAX = 12;
    public const TTL_MIN = 1;

    /** @var LoggerInterface  */
    private $logger;

    public function __construct(ManagerRegistry $registry, string $entityClass, LoggerInterface $logger)
    {
        parent::__construct($registry, $entityClass);
        $this->logger = $logger;
    }

    public function save(
        string $login,
        StatusType $status,
        SourceType $source,
        DestinationType $destination
    ): AuthLog {
        $authLog = new AuthLog(
            $login,
            $status,
            $source,
            $destination
        );
        $this->getEntityManager()->persist($authLog);
        $this->getEntityManager()->flush();

        $this->logger->warning('user login', [
            'login' => $login,
            'status' => $status->getValue(),
            'source' => $source->getValue(),
            'destination' => $destination->getValue(),
        ]);

        return $authLog;
    }

    public function purge(int $ttl): int
    {
        if ($ttl > static::TTL_MAX || $ttl < static::TTL_MIN) {
            throw new \InvalidArgumentException(
                sprintf('Time must be between %d and %d', static::TTL_MIN, static::TTL_MAX)
            );
        }

        $em = $this->_em;
        $metadata = $em->getClassMetadata(AuthLog::class);

        $sql = sprintf(
            "DELETE FROM %s WHERE %s < DATE_SUB(NOW(), INTERVAL :ttl MONTH)",
            $metadata->getTableName(),
            $metadata->getFieldMapping('createdAt')["columnName"]
        );

        $stmt = $em->getConnection()->prepare($sql);
        $stmt->execute(['ttl' => $ttl]);

        return $stmt->rowCount();
    }

    public function deleteAll(): int
    {
        $em = $this->_em;
        $metadata = $em->getClassMetadata(AuthLog::class);

        $sql = sprintf(
            "DELETE FROM %s",
            $metadata->getTableName(),
        );

        $stmt = $em->getConnection()->prepare($sql);
        $stmt->execute();

        return $stmt->rowCount();
    }

    /**
     * Paginate data and add filters from callback
     * @param int $start
     * @param int $limit
     * @param callable|null $callback
     * @return Paginator
     */
    public function paginate(
        int $start = self::START,
        int $limit = self::LIMIT,
        callable $callback = null
    ): Paginator {
        if ($limit > static::LIMIT) {
            throw new \Exception(
                sprintf('Limit must be <= %d', static::LIMIT)
            );
        }

        $query = $this->createQueryBuilder('log')
            ->orderBy('log.id', 'DESC')
            ->setFirstResult($start)
            ->setMaxResults(min([$limit, static::LIMIT]));

        if (\is_callable($callback)) {
            \call_user_func($callback, $query);
        }

        return new Paginator($query);
    }

    /**
     * Memory friendly export method
     * @param callable|null $callback
     */
    public function exportQuery(callable $callback = null): Query
    {
        $queryBuilder = $this->createQueryBuilder('log')
            ->orderBy('log.id', 'DESC')
            ->setMaxResults(static::MAX);

        // filters
        if (\is_callable($callback)) {
            \call_user_func($callback, $queryBuilder);
        }

        return $queryBuilder->getQuery();
    }
}
