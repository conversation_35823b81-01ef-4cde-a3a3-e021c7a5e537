<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\OAuth;

use Wizacha\Marketplace\User\User;
use Psr\Http\Message\UriInterface;

interface Provider
{
    public function getAuthorizeUrl(): UriInterface;
    public function authorize(Token $token): User;
    public function logout(Token $token): UriInterface;
}
