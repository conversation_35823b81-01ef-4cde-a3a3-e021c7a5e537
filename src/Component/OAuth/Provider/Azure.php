<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\OAuth\Provider;

use Wizacha\Component\OAuth\Exception\NotImplementedException;
use Wizacha\Component\OAuth\Exception\UserEmailNotFoundException;
use Wizacha\Component\OAuth\Provider;
use Wizacha\Component\OAuth\ResponseType;
use Wizacha\Component\OAuth\Token;
use Wizacha\Marketplace\User\OAuthToken;
use Wizacha\Marketplace\User\User;
use Wizacha\Marketplace\User\UserService;
use Psr\Http\Message\UriInterface;
use GuzzleHttp\Client as Http;
use GuzzleHttp\Psr7\Uri;
use Wizacha\Marketplace\User\UserType;

class Azure implements Provider
{
    private UserService $userService;
    private Http $http;
    private string $discoveryUri;
    private UserType $userType;
    private string $clientId;
    private string $responseType;
    private string $redirectUri;
    private string $clientSecret;
    private const AUTH_SCOPE = 'openid profile email';


    public function __construct(
        UserService $userService,
        Http $http,
        string $discoveryUri,
        string $clientId,
        string $responseType,
        string $redirectUri,
        string $clientSecret,
        string $userType
    ) {
        $this->userService = $userService;
        $this->http = $http;
        $this->discoveryUri = $discoveryUri;
        $this->clientId = $clientId;
        $this->responseType = $responseType;
        $this->redirectUri = $redirectUri;
        $this->clientSecret = $clientSecret;
        $this->setUserType($userType);
    }

    public function getAuthorizeUrl(): UriInterface
    {
        if ($this->discoveryUri === '') {
            throw new \LogicException();
        }

        return new Uri($this->configuration()['authorization_endpoint'] . '?' . http_build_query([
            'client_id' => $this->clientId,
            'response_type' => $this->responseType,
            'scope' => self::AUTH_SCOPE,
            'redirect_uri' => $this->redirectUri,
        ]));
    }

    public function authorize(Token $token): User
    {
        if ($this->responseType === ResponseType::CODE) {
            $oauthToken = $this->generateOAuthCode($token);
        } else {
            $oauthToken = new OAuthToken(
                (string) $token,
                ''
            );

            $oauthToken->expiresAt((new \DateTimeImmutable())->modify('+1 hour'));
        }

        $userData = $this->getUserInformation($oauthToken);
        $email = $userData['email'];

        $user = $this->userService->getOAuthUser($email, $oauthToken, $this->userType);
        if (\is_string($userData['firstname']) === true) {
            $user->setFirstname($userData['firstname']);
        }
        if (\is_string($userData['lastname']) === true) {
            $user->setLastname($userData['lastname']);
        }
        $this->userService->save($user);

        return $user;
    }

    public function logout(Token $token): UriInterface
    {
        throw new NotImplementedException('API not implemented');
    }

    private function generateOAuthCode(Token $token): OAuthToken
    {
        $response = $this->http->request(
            'post',
            $this->configuration()['token_endpoint'],
            [
                'headers' => [
                    'accept' => 'application/json',
                ],
                'form_params' => [
                    'client_id' => $this->clientId,
                    'scope' => self::AUTH_SCOPE,
                    'code' => (string) $token,
                    'redirect_uri' => $this->redirectUri,
                    'grant_type' => 'authorization_code',
                    'client_secret' => $this->clientSecret,
                ],
            ]
        );

        $token = \json_decode((string) $response->getBody(), true);
        $oauthToken = new OAuthToken(
            $token['access_token'],
            $token['token_type'] ?? '',
            $token['refresh_token'] ?? null
        );

        if (isset($token['expires_in'])) {
            $oauthToken->expiresAt((new \DateTimeImmutable())->modify('+' . $token['expires_in'] . ' seconds'));
        }

        return $oauthToken;
    }

    private function getUserInformation(OAuthToken $token): array
    {
        $response = $this->http->request(
            'post',
            $this->configuration()['userinfo_endpoint'],
            [
                'headers' => [
                    'accept' => 'application/json',
                    'authorization' => 'Bearer ' . $token->getToken(),
                ],
            ]
        );

        $info = \json_decode((string) $response->getBody(), true);

        if (\array_key_exists('unique_name', $info) === true && \mb_strlen($info['unique_name']) > 0) {
            $email = $info['unique_name'];
        } elseif (\array_key_exists('email', $info) === true && \mb_strlen($info['email']) > 0) {
            $email = $info['email'];
        } else {
            throw new UserEmailNotFoundException(\array_keys($info));
        }

        return [
            'email' => $email,
            'firstname' => $info['name'],
            'lastname' => $info['given_name'],
        ];
    }

    private function configuration(): array
    {
        $response = $this->http->request('get', $this->discoveryUri);
        return \json_decode((string) $response->getBody(), true);
    }

    private function setUserType(string $userType): self
    {
        if (UserType::isValid($userType) === true) {
            $this->userType = new UserType($userType);
        } else {
            throw new \UnexpectedValueException("Value '$userType' is not part of the enum " . UserType::class);
        }

        return $this;
    }
}
