<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\OAuth\Provider;

use GuzzleHttp\Client as Http;
use GuzzleHttp\Psr7\Uri;
use <PERSON><PERSON><PERSON>cci\JWT\Parser;
use Psr\Http\Message\UriInterface;
use Rhumsaa\Uuid\Uuid;
use Wizacha\Component\OAuth\Exception\NotImplementedException;
use Wizacha\Component\OAuth\Provider;
use Wizacha\Component\OAuth\Token;
use Wizacha\Marketplace\User\OAuthToken;
use Wizacha\Marketplace\User\User;
use Wizacha\Marketplace\User\UserService;
use Wizacha\Marketplace\User\UserType;

class Openid implements Provider
{
    private $userService;
    private $http;
    private $parser;
    private $discoveryUri;
    private $clientId;
    private $clientSecret;
    private $redirectUri;
    private $defaultUserType;

    public function __construct(
        UserService $userService,
        Http $http,
        Parser $parser,
        string $discoveryUri,
        string $clientId,
        string $clientSecret,
        string $redirectUri,
        string $defaultUserType = null
    ) {
        $this->userService = $userService;
        $this->http = $http;
        $this->parser = $parser;
        $this->discoveryUri = $discoveryUri;
        $this->clientId = $clientId;
        $this->clientSecret = $clientSecret;
        $this->redirectUri = $redirectUri;
        $this->defaultUserType = new UserType($defaultUserType ?: UserType::CLIENT);
    }

    /**
     * @see https://developer.okta.com/authentication-guide/implementing-authentication/auth-code
     */
    public function getAuthorizeUrl(): UriInterface
    {
        if ($this->discoveryUri === '') {
            throw new \LogicException();
        }

        return new Uri(sprintf(
            '%s?%s',
            $this->configuration()['authorization_endpoint'],
            http_build_query([
                'response_type' => 'code',
                'scope' => 'openid',
                'state' => (string) Uuid::uuid4(),
                'nonce' => (string) Uuid::uuid4(),
                'client_id' => $this->clientId,
                'redirect_uri' => $this->redirectUri,
            ])
        ));
    }

    public function authorize(Token $token): User
    {
        [$token, $email, $firstname, $lastname] = $this->getUserInformation($token);

        $user = $this
            ->userService
            ->getOAuthUser($email, $token, $this->defaultUserType)
            ->setFirstname($firstname)
            ->setLastname($lastname);
        $this->userService->save($user);

        return $user;
    }

    public function logout(Token $token): UriInterface
    {
        throw new NotImplementedException('API not implemented');
    }

    /**
     * @return array[OAuthToken, string, string, string]
     */
    private function getUserInformation(Token $token): array
    {
        $response = $this->http->post(
            $this->configuration()['token_endpoint'] . '?' . http_build_query([
                'grant_type' => 'authorization_code',
                'redirect_uri' => $this->redirectUri,
                'code' => (string) $token,
            ]),
            [
                'headers' => [
                    'accept' => 'application/json',
                    'authorization' => 'Basic ' . base64_encode($this->clientId . ':' . $this->clientSecret),
                ],
            ]
        );
        $tokens = json_decode((string) $response->getBody(), true);
        $token = new OAuthToken(
            $tokens['access_token'],
            $tokens['token_type'],
            $tokens['refresh_token'] ?? null
        );
        $token->expiresAt((new \DateTimeImmutable())->modify('+' . $tokens['expires_in'] . ' seconds'));

        $jwt = $this->parser->parse($tokens['id_token']);

        return [
            $token,
            $jwt->getClaim('email'),
            $jwt->getClaim("given_name"),
            $jwt->getClaim("family_name"),
        ];
    }

    private function configuration(): array
    {
        $response = $this->http->get($this->discoveryUri);

        return json_decode((string) $response->getBody(), true);
    }
}
