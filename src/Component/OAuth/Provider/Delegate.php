<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\OAuth\Provider;

use Wizacha\Component\OAuth\Provider;
use Wizacha\Component\OAuth\Token;
use Wizacha\Marketplace\User\User;
use Psr\Http\Message\UriInterface;

class Delegate implements Provider
{
    private $provider;

    public function __construct(string $current, array $providers)
    {
        if (!isset($providers[$current])) {
            throw new \LogicException();
        }

        $this->provider = $providers[$current];
    }

    public function getAuthorizeUrl(): UriInterface
    {
        return $this->provider->getAuthorizeUrl();
    }

    public function authorize(Token $token): User
    {
        return $this->provider->authorize($token);
    }

    public function logout(Token $token): UriInterface
    {
        return $this->provider->logout($token);
    }
}
