<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\OAuth\Provider;

use Wizacha\Component\OAuth\Exception\NotImplementedException;
use Wizacha\Component\OAuth\Provider;
use Wizacha\Component\OAuth\Token;
use Wizacha\Marketplace\User\User;
use Wizacha\Marketplace\User\UserType;
use Wizacha\Marketplace\User\OAuthToken;
use Wizacha\Marketplace\User\UserService;
use Psr\Http\Message\UriInterface;
use GuzzleHttp\Psr7\Uri;

/**
 * Provider ici uniquement afin de pouvoir faire des tests pour les devs
 */
class Google implements Provider
{
    private $client;
    private $userService;

    public function __construct(
        \Google_Client $client,
        UserService $userService,
        string $redirectUri
    ) {
        $client->setRedirectUri($redirectUri);
        $this->client = $client;
        $this->userService = $userService;
    }

    public function getAuthorizeUrl(): UriInterface
    {
        return new Uri($this->client->createAuthUrl(
            'https://www.googleapis.com/auth/userinfo.email'
        ));
    }

    public function authorize(Token $token): User
    {
        $tokens = $this->client->fetchAccessTokenWithAuthCode((string) $token);
        $token = new OAuthToken(
            $tokens['access_token'],
            $tokens['token_type'],
            $tokens['refresh_token'] ?? null
        );
        $token->expiresAt((new \DateTimeImmutable())->modify('+' . $tokens['expires_in'] . ' seconds'));
        $service = new \Google_Service_Oauth2($this->client);

        // le type de user est hardcodé à CLIENT puisqu'il s'agit d'un provider
        // de test pour les devs, donc si on se loggue pour un autre type
        // d'utilisateur il faut changer cette valeur pour la période de test
        return $this->userService->getOAuthUser(
            $service->userinfo->get()->email,
            $token,
            UserType::CLIENT()
        );
    }

    public function logout(Token $token): UriInterface
    {
        throw new NotImplementedException('API not implemented');
    }
}
