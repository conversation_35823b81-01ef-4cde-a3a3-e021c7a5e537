<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\OAuth\Provider;

use DateTimeImmutable;
use Psr\Http\Message\UriInterface;
use Wizacha\Component\OAuth\Exception\NotImplementedException;
use Wizacha\Component\OAuth\Provider;
use Wizacha\Component\OAuth\Token;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\User\OAuthToken;
use Wizacha\Marketplace\User\User;
use Wizacha\Marketplace\User\UserService;
use GuzzleHttp\Client as Http;
use Wizacha\Marketplace\User\UserType;

class Somfy implements Provider
{
    /**
     * @var UserService
     */
    private $userService;
    /**
     * @var Http
     */
    private $http;
    /**
     * @var string
     */
    private $authorizationServer;

    public function __construct(
        UserService $userService,
        Http $http,
        string $authorizationServer
    ) {
        $this->userService = $userService;
        $this->http = $http;
        $this->authorizationServer = $authorizationServer;
    }

    public function getAuthorizeUrl(): UriInterface
    {
        throw new Forbidden();
    }

    public function authorize(Token $token): User
    {
        $oauthToken = new OAuthToken((string) $token, '');
        $oauthToken->expiresAt((new DateTimeImmutable())->modify('+24 hours'));

        $userData = $this->getUserInformation($oauthToken);

        $user = $this->userService->getOAuthUser($userData['email'], $oauthToken, UserType::CLIENT());
        $user->setFirstname($userData['firstname']);
        $user->setLastname($userData['lastname']);
        $this->userService->save($user);

        return $user;
    }

    public function logout(Token $token): UriInterface
    {
        throw new NotImplementedException('API not implemented');
    }

    private function getUserInformation(OAuthToken $token): array
    {
        $response = $this->http->get($this->authorizationServer . '?' . http_build_query([
            'for' => 'marketplace',
            't' => $token->getToken(),
        ]));

        $info = json_decode((string) $response->getBody(), true);

        container()->get('logger')->notice('Somfy userinfo endpoint', [
            'token' => $token->getToken(),
            'response' => $info,
        ]);

        return [
            'email' => $info['email'],
            'firstname' => $info['firstname'],
            'lastname' => $info['lastname'],
        ];
    }
}
