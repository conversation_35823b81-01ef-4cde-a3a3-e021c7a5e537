<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\OAuth\Provider;

use Wizacha\Component\OAuth\Exception\ResponseTypeException;
use Wizacha\Component\OAuth\Exception\NotImplementedException;
use Wizacha\Component\OAuth\Provider;
use Wizacha\Component\OAuth\ResponseType;
use Wizacha\Component\OAuth\Token;
use Wizacha\Marketplace\User\User;
use Wizacha\Marketplace\User\UserType;
use Wizacha\Marketplace\User\OAuthToken;
use Wizacha\Marketplace\User\UserService;
use Psr\Http\Message\UriInterface;
use GuzzleHttp\Psr7\Uri;
use GuzzleHttp\Client as Http;
use Rhumsaa\Uuid\Uuid;

class Okta implements Provider
{
    private $userService;
    private $http;
    private $authorizationServer;
    private $clientId;
    private $secretId;
    private $redirectUri;
    private $adminGroup;
    private $vendorGroup;
    private $responseType;

    public function __construct(
        UserService $userService,
        Http $http,
        string $authorizationServer,
        string $clientId,
        string $secretId,
        string $redirectUri,
        string $adminGroup,
        string $vendorGroup,
        string $responseType
    ) {
        $this->userService = $userService;
        $this->http = $http;
        $this->authorizationServer = $authorizationServer;
        $this->clientId = $clientId;
        $this->secretId = $secretId;
        $this->redirectUri = $redirectUri;
        $this->adminGroup = $adminGroup;
        $this->vendorGroup = $vendorGroup;

        if (!\in_array($responseType, ResponseType::toArray())) {
            throw new ResponseTypeException();
        }

        $this->responseType = $responseType;
    }

    /**
     * @see https://developer.okta.com/authentication-guide/implementing-authentication/auth-code
     */
    public function getAuthorizeUrl(): UriInterface
    {
        if ($this->authorizationServer === '') {
            throw new \LogicException();
        }

        return new Uri($this->configuration()['authorization_endpoint'] . '?' . http_build_query([
            'client_id' => $this->clientId,
            'response_type' => $this->responseType,
            'scope' => 'openid profile email groups phone address',
            'state' => (string) Uuid::uuid4(),
            'nonce' => (string) Uuid::uuid4(),
            'redirect_uri' => $this->redirectUri,
        ]));
    }

    public function authorize(Token $token): User
    {
        if ($this->responseType === ResponseType::CODE) {
            $oauthToken = $this->generateOAuthCode($token);
        } else {
            $oauthToken = new OAuthToken(
                (string) $token,
                ''
            );

            $oauthToken->expiresAt((new \DateTimeImmutable())->modify('+1 hour'));
        }

        $userData = $this->getUserInformation($oauthToken);
        $email = $userData['email'];

        switch (true) {
            case \in_array($this->adminGroup, $userData['groups'], true):
                $userType = UserType::ADMIN();
                break;

            case \in_array($this->vendorGroup, $userData['groups'], true):
                $userType = UserType::VENDOR();
                break;

            default:
                $userType = UserType::CLIENT();
                break;
        }

        $user = $this->userService->getOAuthUser($email, $oauthToken, $userType);
        $user->setFirstname($userData['firstname']);
        $user->setLastname($userData['lastname']);
        $this->userService->save($user);

        return $user;
    }

    public function logout(Token $token): UriInterface
    {
        throw new NotImplementedException('API not implemented');
    }

    private function generateOAuthCode(Token $token): OAuthToken
    {
        $response = $this->http->post(
            $this->configuration()['token_endpoint'],
            [
                'headers' => [
                    'accept' => 'application/json',
                    'authorization' => 'Basic ' . base64_encode($this->clientId . ':' . $this->secretId),
                ],
                'form_params' => [
                    'grant_type' => 'authorization_code',
                    'code' => (string) $token,
                    'redirect_uri' => $this->redirectUri,
                ],
            ]
        );

        $token = json_decode((string) $response->getBody(), true);
        $oauthToken = new OAuthToken(
            $token['access_token'],
            $token['token_type'] ?? '',
            $token['refresh_token'] ?? null
        );

        if (isset($token['expires_in'])) {
            $oauthToken->expiresAt((new \DateTimeImmutable())->modify('+' . $token['expires_in'] . ' seconds'));
        }

        return $oauthToken;
    }

    private function getUserInformation(OAuthToken $token): array
    {
        $response = $this->http->post(
            $this->configuration()['userinfo_endpoint'],
            [
                'headers' => [
                    'accept' => 'application/json',
                    'authorization' => 'Bearer ' . $token->getToken(),
                ],
            ]
        );

        $info = json_decode((string) $response->getBody(), true);
        container()->get('logger')->notice('Okta userinfo endpoint', [
            'token' => $token->getToken(),
            'response' => $info,
        ]);

        return [
            'email' => $info['email'],
            'groups' => $info['groups'] ?? [],
            'firstname' => $info['given_name'],
            'lastname' => $info['family_name'],
        ];
    }

    private function configuration(): array
    {
        $response = $this->http->get($this->authorizationServer);

        return json_decode((string) $response->getBody(), true);
    }
}
