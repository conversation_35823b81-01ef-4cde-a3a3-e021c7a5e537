<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\OAuth\Exception;

use Wizacha\Marketplace\Exception\ErrorCode;
use Wizacha\Marketplace\Exception\MarketplaceExceptionInterface;

class UserEmailNotFoundException extends \Exception implements MarketplaceExceptionInterface
{
    public array $infoKeys;
    private ErrorCode $errorCode;

    public function __construct(array $infoKeys)
    {
        $this->errorCode = ErrorCode::USER_EMAIL_NOT_FOUND();

        parent::__construct("An error occurred while recovering User email from Provider", $this->errorCode->getValue());

        $this->infoKeys = $infoKeys;
    }

    public function getContext(): array
    {
        return ['infoKeys' => $this->infoKeys];
    }

    public function getErrorCode(): ErrorCode
    {
        return $this->errorCode;
    }
}
