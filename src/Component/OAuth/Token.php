<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\OAuth;

use Wizacha\Component\OAuth\Exception\DomainException;

final class Token
{
    private $value;

    public function __construct(string $value)
    {
        if ($value === '') {
            throw new DomainException();
        }

        $this->value = $value;
    }

    public function __toString(): string
    {
        return $this->value;
    }
}
