<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\PurgeResources;

/**
 * A Purge resource Report
 */
class Report
{
    /** @var int $nbItems */
    protected $nbItems;

    /** @var int $filesSize */
    protected $filesSize;

    public function __construct()
    {
        $this->nbItems = 0;
        $this->filesSize = 0;
    }

    public function getNbItems(): int
    {
        return $this->nbItems;
    }

    public function getFilesSize(): int
    {
        return $this->filesSize;
    }

    /**
     * Increment items count and filesize
     */
    public function addOneFile(int $size): self
    {
        $this->nbItems++;
        $this->filesSize += $size;

        return $this;
    }
}
