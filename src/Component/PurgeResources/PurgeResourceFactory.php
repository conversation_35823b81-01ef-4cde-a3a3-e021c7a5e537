<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\PurgeResources;

use Wizacha\Component\PurgeResources\Exceptions\NotFoundResourceType;
use Wizacha\Component\PurgeResources\Types\AbstractPurgeResource;

/**
 * Factory to return the PurgeResource instance for a given resource type
 */
class PurgeResourceFactory
{
    /** @var AbstractPurgeResource[] */
    private $purgeResources;

    public function __construct(iterable $purgeResources)
    {
        $this->purgeResources = $purgeResources;
    }

    /**
     * Return the corresponding PurgeResource object
     */
    public function get(string $type): AbstractPurgeResource
    {
        foreach ($this->purgeResources as $purgeResource) {
            if ($purgeResource->getType() === $type) {
                return $purgeResource;
            }
        }

        throw new NotFoundResourceType($type);
    }
}
