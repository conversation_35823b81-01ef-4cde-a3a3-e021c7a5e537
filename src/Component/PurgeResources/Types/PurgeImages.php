<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\PurgeResources\Types;

use Tygh\Database;
use Wizacha\Component\PurgeResources\StorageFile;
use W<PERSON>cha\ImageManager;
use Wizacha\Storage\StorageService;

/**
 * Manage purge image resource type
 */
class PurgeImages extends AbstractPurgeResource
{
    protected const TYPE = "images";

    protected const ENTITIES_TYPES = [
        "detailed",
        "feature_variant",
        "variant_image",
        "payment",
        "promo",
    ];

    protected const ORIGINAL_FOLDER = 'originals';

    public function getType(): string
    {
        return static::TYPE;
    }

    public function getStorage(): StorageService
    {
        return container()->get("Wizacha\Storage\ImagesStorageService");
    }

    public function process(): AbstractPurgeResource
    {
        // EntityType
        foreach (static::ENTITIES_TYPES as $entityType) {
            $this->purgeByEntity($entityType);
        }

        // Originals
        $this->purgeOriginalFolder();

        return $this;
    }

    /**
     * Purge for a given entity type
     * ie: detailed / feature_variant / ....
     */
    public function purgeByEntity(string $entityType)
    {
        foreach ($this->storage->listContents($entityType) as $subfolderPath) {
            $image = $this->getStorageFile($subfolderPath['path']);

            if ($this->needToDelete($image)) {
                $this->deleteRelatedThumbnails($image->getPath());

                // Delete the master image file
                $this->report->addOneFile($image->getSize());

                $this->delete($image->getPath());

                // Delete parent folder if empty
                $this->deleteEmptyFolders(
                    pathinfo($image->getPath())['dirname']
                );
            }
        }
    }

    /**
     * Delete thumbnails related to an image
     */
    public function deleteRelatedThumbnails(string $imagePath): self
    {
        $thumbnailsRootPath = ImageManager::thumbnailsRootPath($imagePath);

        // Delete thumbnails relateds to this image
        foreach ($this->storage->listContents($thumbnailsRootPath) as $thumbnailSubPath) {
            $thumbnail = $this->getStorageFile($thumbnailSubPath['path']);

            if ($thumbnail->isDirectory() === false) {
                $this->report->addOneFile($thumbnail->getSize());
                $this->delete($thumbnail->getPath());
            }
        }

        // Delete thumbnails empty folder
        $this->deleteEmptyFolders($thumbnailsRootPath);

        return $this;
    }

    /**
     * Return true if we need to delete this file
     */
    public function needToDelete(StorageFile $file): bool
    {
        return false === $this->isImagePathExistsInDb($file->getName())
            && true === $this->isOlderThanOffset($file->getLastModifiedTimestamp());
    }

    /**
     * Specific purge process for the 'original' folder
     */
    public function purgeOriginalFolder(): self
    {
        foreach ($this->storage->listContents(static::ORIGINAL_FOLDER) as $storageContent) {
            $image = $this->getStorageFile($storageContent['path']);

            $nbResultsInDb = Database::getField(
                'SELECT COUNT(image_id) FROM cscart_images WHERE image_id = ?i',
                \intval($image->getNameWithoutExtension())
            );

            if (\intval($nbResultsInDb) === 0 && $image->isDirectory() === false) {
                $this->report->addOneFile($image->getSize());

                $this->delete($image->getPath());
            }
        }

        return $this;
    }

    /**
     * Check by imagePath if this image exists in DB
     */
    public function isImagePathExistsInDb(string $imagePath): bool
    {
        $nbResults = Database::getField(
            'SELECT COUNT(image_id) FROM cscart_images WHERE image_path = ?s',
            $imagePath
        );

        return \intval($nbResults) >= 1;
    }

    /**
     * Check if a given timestamp is older than the Days offset
     */
    public function isOlderThanOffset(int $timestamp): bool
    {
        // 86400 = nb sec in a day
        return $timestamp < time() - ($this->delay * 86400);
    }

    /**
     * Return an instance of Storage file
     */
    protected function getStorageFile(string $filePath): StorageFile
    {
        return new StorageFile(
            $filePath,
            $this->storage
        );
    }
}
