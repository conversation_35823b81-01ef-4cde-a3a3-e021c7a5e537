<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\PurgeResources\Types;

use Wizacha\Component\PurgeResources\Report;
use Wizacha\Storage\StorageService;

/**
 * Abstract class for resource types
 * ie: PurgeImage
 */
abstract class AbstractPurgeResource
{
    /** @var Report */
    protected $report;

    protected ?StorageService $storage;

    /** @var bool */
    protected $dryRun;

    /** @var int in days */
    protected $delay;

    public function __construct(Report $report, int $delay)
    {
        $this->report = $report;
        $this->storage = $this->getStorage();
        $this->dryRun = false;
        $this->delay = $delay;
    }

    /**
     * Used in a factory to retrieve the asked PurgeResource
     */
    abstract public function getType(): string;

    /**
     * Used to instantiate the Storage
     */
    abstract public function getStorage(): StorageService;

    /**
     * The purge process
     */
    abstract public function process(): self;

    /**
     * Delete a file or a directory with a dry run check
     */
    public function delete(string $item, bool $isDirectory = false): self
    {
        if ($this->isDryRun()) {
            return $this;
        }

        if ($isDirectory) {
            $this->storage->deleteDir($item);
        } else {
            $this->storage->delete($item);
        }

        return $this;
    }

    /**
     * Remove recursivly parent folders of a path if they are empty
     */
    public function deleteEmptyFolders(string $path): self
    {
        // Clean '/' at start and end of path
        $path = trim($path, "/");

        // We don't want to delete Root folder
        if (strpos($path, '/') === false) {
            return $this;
        }

        if (\count($this->storage->getList($path)) < 1) {
            $this->delete($path, true);

            $this->deleteEmptyFolders(pathinfo($path)['dirname']);
        }

        return $this;
    }

    public function setStorage(StorageService $storage): self
    {
        $this->storage = $storage;

        return $this;
    }

    public function setDryRun(bool $dryRun): void
    {
        $this->dryRun = $dryRun;
    }

    public function setDelay(int $delay): void
    {
        $this->delay = $delay;
    }

    protected function isDryRun(): bool
    {
        return $this->dryRun;
    }
}
