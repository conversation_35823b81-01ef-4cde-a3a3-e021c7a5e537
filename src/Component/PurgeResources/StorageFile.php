<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\PurgeResources;

use Wizacha\Storage\StorageService;

/**
 * A file with its useful properties
 */
class StorageFile
{
    /** @var string */
    protected $name;

    /** @var string */
    protected $nameWithoutExtension;

    /** @var string */
    protected $path;

    /** @var int|null */
    protected $size;

    /** @var int|null */
    protected $lastModifiedTimestamp;

    protected ?StorageService $storage;

    public function __construct(string $storagePath, StorageService $storage)
    {
        $pathinfo = pathinfo($storagePath);

        $this->name = $pathinfo['basename'];
        $this->nameWithoutExtension = $pathinfo['filename'];
        // remove prefix part of the path :
        $this->path = ltrim(strstr($storagePath, '/'), '/');
        $this->storage = $storage;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getNameWithoutExtension(): string
    {
        return $this->nameWithoutExtension;
    }

    public function getPath(): string
    {
        return $this->path;
    }

    public function getSize(): int
    {
        if (false === \is_int($this->size)) {
            $this->setSizeFromStorage();
        }

        return $this->size;
    }

    public function getLastModifiedTimestamp(): int
    {
        if (false === \is_int($this->lastModifiedTimestamp)) {
            $this->setLastModifiedTimestampFromStorage();
        }

        return $this->lastModifiedTimestamp;
    }

    public function setSize(?int $size): self
    {
        $this->size = $size;

        return $this;
    }

    public function setLastModifiedTimestamp(?int $lastModifiedTimestamp): self
    {
        $this->lastModifiedTimestamp = $lastModifiedTimestamp;

        return $this;
    }

    protected function setLastModifiedTimestampFromStorage(): self
    {
        return $this->setLastModifiedTimestamp(
            $this->storage->getLastModifiedTimestamp($this->getPath()) ?? 0
        );
    }

    protected function setSizeFromStorage(): self
    {
        return $this->setSize(
            $this->storage->getSize($this->getPath()) ?? 0
        );
    }

    public function isDirectory()
    {
        return $this->storage->getSize($this->getPath()) === 0;
    }
}
