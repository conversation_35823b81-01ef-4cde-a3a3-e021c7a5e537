<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Order;

use Wizacha\Money\Money;

/**
 * Cette classe représente une collection de ligne d'une commande, son but est
 * de permettre de calculer correctement tous les montants (ttc, ht, tva) de la
 * commande
 *
 * La classe étant immutable l'ajout de chaque ligne va créer une nouvelle
 * instance de cette classe
 */
class Amounts
{
    /** @var Money */
    private $amountIncludingTaxes;

    /** @var Money */
    private $amountExcludingTaxes;

    /** @var Money */
    private $taxAmount;

    /** @var array<string, Money> */
    private $taxAmountByRate = [];

    /** @var array<string, Money> */
    private $amountExcludingTaxByRate = [];

    /** @var Line[] */
    private $lines = [];

    /** @var TaxRate[] */
    private $taxRates = [];

    public function __construct()
    {
        $this->amountIncludingTaxes = new Money(0);
        $this->amountExcludingTaxes = new Money(0);
        $this->taxAmount = new Money(0);
    }

    public function withLine(Line $line): self
    {
        $self = clone $this;
        $self->lines[] = $line;
        $self->computeAmounts();

        return $self;
    }

    public function getAmountIncludingTaxes(): Money
    {
        return $this->amountIncludingTaxes;
    }

    public function getAmountExcludingTaxes(): Money
    {
        return $this->amountExcludingTaxes;
    }

    public function getTaxAmount(): Money
    {
        return $this->taxAmount;
    }

    public function getTaxRateAmount(TaxRate $rate): Money
    {
        return $this->taxAmountByRate[(string) $rate];
    }

    public function getAmountExcludingTaxForRate(TaxRate $rate): Money
    {
        return $this->amountExcludingTaxByRate[(string) $rate];
    }

    /**
     * @return TaxRate[]
     */
    public function getTaxRates(): array
    {
        return array_values($this->taxRates);
    }

    private function computeAmounts(): void
    {
        // @codingStandardsIgnoreStart
        // PHP_CS croit que le return type de la closure est celui de la fonction parente
        $this->taxRates = array_reduce(
            $this->lines,
            function (array $rates, Line $line): array {
                $rates[(string) $line->getTaxRate()] = $line->getTaxRate();

                return $rates;
            },
            []
        );

        //on regroupe les lignes de la commande par taxe
        $linesByTax = array_reduce(
            $this->lines,
            function (array $linesByTax, Line $line): array {
                $tax = (string) $line->getTaxRate();
                $group = $linesByTax[$tax] ?? [];
                $group[] = $line;
                $linesByTax[$tax] = $group;

                return $linesByTax;
            },
            []
        );

        //on calcule le montant hors taxe pour chaque taux
        $this->amountExcludingTaxByRate = array_map(
            function (array $lines): Money {
                return array_reduce(
                    $lines,
                    function (Money $sum, Line $line): Money {
                        return $sum->add($line->getAmountExcludingTax());
                    },
                    new Money(0)
                );
            },
            $linesByTax
        );

        //on calcule le montant de chaque taxe (somme des hors taxe, pour un
        //même taux, fois le taux)
        $this->taxAmountByRate = array_map(
            function (array $lines): Money {
                $amountExcludingTaxes = array_reduce(
                    $lines,
                    function (Money $sum, Line $line): Money {
                        return $sum->add($line->getAmountExcludingTax());
                    },
                    new Money(0)
                );

                return reset($lines)->getTaxRate()->applyTo($amountExcludingTaxes);
            },
            $linesByTax
        );

        //on calcule la taxe global en sommant l'ensemble des sous taux
        $this->taxAmount = array_reduce(
            $this->taxAmountByRate,
            function (Money $sum, Money $tax): Money {
                return $sum->add($tax);
            },
            new Money(0)
        );

        //on calcule le montant hors taxe (en sommant toutes les lignes)
        $this->amountExcludingTaxes = array_reduce(
            $this->lines,
            function (Money $sum, Line $line): Money {
                return $sum->add($line->getAmountExcludingTax());
            },
            new Money(0)
        );
        // @codingStandardsIgnoreEnd

        //on calcule le total global
        $this->amountIncludingTaxes = $this->amountExcludingTaxes->add($this->taxAmount);
    }
}
