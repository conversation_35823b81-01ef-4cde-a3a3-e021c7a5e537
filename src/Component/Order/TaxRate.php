<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Order;

use Wizacha\Money\Money;

class TaxRate
{
    /** @var float  */
    private $rate;

    /**
     * @param float $rate Doit être au format 5.5% et non 0.55
     */
    public function __construct(float $rate)
    {
        $this->rate = $rate;
    }

    /** @return Money The tax amount depending on the provided amount - NOT the amount with the tax applied  */
    public function applyTo(Money $amountExcludingTaxes): Money
    {
        return $amountExcludingTaxes->multiply($this->rate / 100);
    }

    public function getAmountExcludingTax(Money $amountIncludingTax): Money
    {
        return $amountIncludingTax->divide(1 + ($this->rate / 100));
    }

    public function getAmountIncludingTax(Money $amountExcludingTax): Money
    {
        return $amountExcludingTax->multiply(1 + ($this->rate / 100));
    }

    public function getDiscountedTaxAmountFromIncludingTaxes(Money $amountIncludingTaxes): Money
    {
        return ($amountIncludingTaxes->divide(1 + ($this->rate / 100))->multiply($this->rate / 100));
    }

    public function toFloat(): float
    {
        return $this->rate;
    }

    public function getTaxAmountFromIncludingTaxes(Money $amountIncludingTax): Money
    {
        return $amountIncludingTax->subtract($this->getAmountExcludingTax($amountIncludingTax));
    }

    public function __toString(): string
    {
        return (string) $this->rate;
    }
}
