<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Order;

use Wizacha\Money\Money;

class Line
{
    /** @var Money Prix hors taxe */
    private $unitPrice;

    /** @var float */
    private $quantity;

    /** @var TaxRate */
    private $taxRate;

    public function __construct(Money $unitPrice, float $quantity, TaxRate $taxRate)
    {
        $this->unitPrice = $unitPrice;
        $this->quantity = $quantity;
        $this->taxRate = $taxRate;
    }

    public function getUnitPrice(): Money
    {
        return $this->unitPrice;
    }

    public function getQuantity(): float
    {
        return $this->quantity;
    }

    public function getAmountExcludingTax(): Money
    {
        return $this->unitPrice->multiply($this->quantity);
    }

    /**
     * /!\ Ne JAMAIS utiliser cette méthode pour des calculs de montant dans
     * notre app, cette méthode n'a pas de sens comptable
     *
     * Elle existe purement à titre informatif, comme c'est souvent le cas sur
     * une commande ou une facture d'afficher le prix hors taxe pour chaque
     * ligne
     */
    public function getAmountIncludingTax(): Money
    {
        return $this->getTaxAmount()->add($this->getAmountExcludingTax());
    }

     /**
     * /!\ Ne JAMAIS utiliser cette méthode pour des calculs de montant dans
     * notre app, cette méthode n'a pas de sens comptable
     *
     * Elle existe purement à titre informatif, comme c'est souvent le cas sur
     * une commande ou une facture d'afficher le montant de tva pour chaque
     * ligne
     */
    public function getTaxAmount(): Money
    {
        return $this->taxRate->applyTo($this->getAmountExcludingTax());
    }

    public function getTaxRate(): TaxRate
    {
        return $this->taxRate;
    }
}
