<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\MondialRelay;

use Wizacha\Component\MondialRelay\Exception\ApiException;
use W<PERSON>cha\Component\MondialRelay\Model\Contact;
use Wizacha\Component\MondialRelay\Model\PickupPoint;
use Wizacha\Component\MondialRelay\Model\Search;
use Wizacha\Component\MondialRelay\Model\StickerResult;
use Wizacha\Core\Truncate;

class Client
{
    public const LABEL_BASE_URL = 'http://www.mondialrelay.com'; // This is the base url used for the labels

    /**
     * @var string
     */
    private $userId;

    /**
     * @var string
     */
    private $password;

    /**
     * https://connect-api.mondialrelay.com/
     *
     * @var string
     */
    private $endpoint;

    /**
     * @var \SoapClient
     */
    private $soapClient;

    public function __construct(string $userId, string $password, string $endpoint)
    {
        $this->userId = str_pad($userId, 8);
        $this->password = $password;
        $this->endpoint = $endpoint;
    }

    /**
     * @param  Search  $search
     *
     * @return PickupPoint[]
     *
     * @throws ApiException
     */
    public function listPickupPoints(Search $search): array
    {
        $params = ['Enseigne'  => $this->userId] + $search->toSoapParams();
        $params = $this->secure($params);
        $result = $this->getSoapClient()->WSI4_PointRelais_Recherche($params);

        // phpcs:disable
        if ($result->WSI4_PointRelais_RechercheResult->STAT !== '0') {
            throw new ApiException(sprintf('An error occured while requesting the remote API. Error code was "%s".', $result->WSI4_PointRelais_RechercheResult->STAT));
        }

        $pickupPoints = [];

        foreach ($result->WSI4_PointRelais_RechercheResult->PointsRelais->PointRelais_Details as $pointData) {
            $pickupPoints[] = ModelFactory::createPickupPoint($pointData);
        }
        // phpcs:enable

        return $pickupPoints;
    }

    /**
     * @param  string $id
     * @param  string $country
     *
     * @return PickupPoint
     *
     * @throws ApiException
     */
    public function getPickupPoint(string $id, string $country = 'FR'): PickupPoint
    {
        $params = [
            'NumPointRelais' => $id,
            'Pays' => $country,
            'Enseigne'  => $this->userId,
        ];

        $params = $this->secure($params);
        $result = $this->getSoapClient()->WSI4_PointRelais_Recherche($params);

        // phpcs:disable
        if ($result->WSI4_PointRelais_RechercheResult->STAT !== '0') {
            throw new ApiException(
                sprintf('An error occured while requesting the remote API. Error code was "%s".', $result->WSI4_PointRelais_RechercheResult->STAT),
                (int) $result->WSI4_PointRelais_RechercheResult->STAT
            );
        }

        $pickupPoint = ModelFactory::createPickupPoint($result->WSI4_PointRelais_RechercheResult->PointsRelais->PointRelais_Details);
        // phpcs:enable

        return $pickupPoint;
    }

    /**
     * @param  Contact  $shipper
     * @param  Contact  $recipient
     * @param  int      $orderId
     * @param  int      $customerId
     * @param  int      $weight         in grams.
     * @param  string   $pickupPointId
     *
     * @return StickerResult
     *
     * @throws ApiException
     */
    public function createShipment(Contact $shipper, Contact $recipient, int $orderId, int $customerId, int $weight, string $pickupPointId): StickerResult
    {
        $destAd = Truncate::cut($recipient->getTitle() . ' ' . $recipient->getName(), 32, 2);
        $expAd1 = \substr($shipper->getTitle() . ' ' . $shipper->getName(), 0, 29);

        // Warning: The order of the fields must be the same as in the MondialRelay documentation!
        $params = [
            'Enseigne' => $this->userId,
            'ModeCol' => 'CCC', // Mode collecte - Chez le client (l'enseigne)
            'ModeLiv' => '24R', // Point relai <30kg
            'NDossier' => $orderId,
            'NClient' => $customerId,
            'Expe_Langage' => 'FR',
            'Expe_Ad1' => $expAd1,
            //Expe_Ad2
            'Expe_Ad3' => $shipper->getAddress(),
            'Expe_Ville' => $shipper->getCity(),
            'Expe_CP' => $shipper->getZipCode(),
            'Expe_Pays' => strtoupper($shipper->getCountry()),
            'Expe_Tel1' => $this->filterPhoneNumber($shipper->getPhone()),
            'Dest_Langage' => 'FR',
            'Dest_Ad1' => $destAd[0],
            'Dest_Ad2' => $destAd[1],
            'Dest_Ad3' => $recipient->getAddress(),
            'Dest_Ad4' => $recipient->getAddress2() ?? null,
            'Dest_Ville' => $recipient->getCity(),
            'Dest_CP' => $recipient->getZipCode(),
            'Dest_Pays' => strtoupper($recipient->getCountry()),
            'Dest_Tel1' => $this->filterPhoneNumber($recipient->getPhone()),
            'Poids' => ($weight > 100) ? $weight : 100, // 100g minimum
            'NbColis' => 1,
            'CRT_Valeur' => 0, // Contre-remboursement
            'LIV_Rel_Pays' => 'FR',
            'LIV_Rel' => $pickupPointId, // id relai
        ];

        $params = array_filter($params, function ($value) {
            return $value !== null;
        });

        if (empty($params['Dest_Ad3'])) {
            $params['Dest_Ad3'] = $params['Dest_Ad4'];
            unset($params['Dest_Ad4']);
        }

        $params = $this->secure($params);
        $result = $this->getSoapClient()->WSI2_CreationEtiquette($params);

        // phpcs:disable
        if ($result->WSI2_CreationEtiquetteResult->STAT !== '0') {
            throw new ApiException(sprintf('An error occured while requesting the remote API. Error code was "%s".', $result->WSI2_CreationEtiquetteResult->STAT), (int) $result->WSI2_CreationEtiquetteResult->STAT);
        }

        return new StickerResult(
            $result->WSI2_CreationEtiquetteResult->ExpeditionNum,
            self::LABEL_BASE_URL.$result->WSI2_CreationEtiquetteResult->URL_Etiquette
        );
        // phpcs:enable
    }

    public function setSoapClient(\SoapClient $soapClient): self
    {
        $this->soapClient = $soapClient;

        return $this;
    }

    public function secure(array $params = []): array
    {
        $params['Security'] = strtoupper(md5(utf8_decode(implode($params) . $this->password)));

        return $params;
    }

    public function getShippingAddress(array $shippinginfo, string $country = 'FR'): array
    {
        $pickupPoint = $this->getPickupPoint($shippinginfo['pickupPointId'], $country);
        $address = $pickupPoint->getAddress();
        $address0 = \array_key_exists(0, $address) === true ? $pickupPoint->getAddress()[0] : '';
        $address1 = \array_key_exists(1, $address) === true ? $pickupPoint->getAddress()[1] : '';

        return [
            's_title' => $shippinginfo['title'],
            's_firstname' => $shippinginfo['firstName'],
            's_lastname' => $shippinginfo['lastName'],
            's_company' => trim($address0 . ' ' . $address1),
            's_address' => \array_key_exists(2, $address) === true ? $address[2] : '' ,
            's_address_2' => \array_key_exists(3, $address) === true ? $address[3] : '',
            's_zipcode' => $pickupPoint->getZipCode(),
            's_city' => $pickupPoint->getCity(),
            's_county' => $pickupPoint->getCountry(),
            's_pickup_point_id' => $pickupPoint->getId()
        ];
    }

    private function filterPhoneNumber(?string $phone): ?string
    {
        return !empty($phone) && preg_match('/^((00|\+)33|0)[0-9][0-9]{8}$/', $phone) ? $phone : null;
    }

    private function getSoapClient(): \SoapClient
    {
        if ($this->soapClient === null) {
            $this->soapClient = new \SoapClient($this->endpoint);
        }

        return $this->soapClient;
    }
}
