<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\MondialRelay;

use Wizacha\Component\MondialRelay\Model\Opening;
use Wizacha\Component\MondialRelay\Model\PickupPoint;
use Wizacha\Component\MondialRelay\Model\Unavailability;

class ModelFactory
{
    public static $mondialRelayDays = [
        '<PERSON><PERSON><PERSON>_Lundi',
        '<PERSON><PERSON><PERSON>_Mardi',
        '<PERSON><PERSON>res_Mercredi',
        '<PERSON><PERSON><PERSON>_Jeudi',
        'Ho<PERSON><PERSON>_Vendredi',
        '<PERSON><PERSON><PERSON>_Samedi',
        '<PERSON><PERSON>res_Dimanche',
    ];

    public static function createPickupPoint(\stdClass $data): PickupPoint
    {
        $openingHours = array_map(function ($k, $v) use ($data) {
            return self::createOpening($k, $data->$v);
        }, array_keys(self::$mondialRelayDays), self::$mondialRelayDays);

        // phpcs:disable
        return new PickupPoint(
            $data->Num,
            [
                trim($data->LgAdr1),
                trim($data->LgAdr2),
                trim($data->LgAdr3),
                trim($data->LgAdr4),
            ],
            $data->CP,
            trim($data->Ville),
            $data->Pays,
            $data->Localisation1,
            $data->Localisation2,
            $data->Latitude,
            $data->Longitude,
            $data->TypeActivite,
            $data->Information,
            $openingHours,
            self::createUnavailability($data->Informations_Dispo),
            $data->URL_Photo,
            $data->URL_Plan,
            (int) $data->Distance
        );
        // phpcs:enable
    }

    public static function createOpening(int $day, \stdClass $data): Opening
    {
        return new Opening(
            $day,
            $data->string[0],
            $data->string[1],
            $data->string[2],
            $data->string[3]
        );
    }

    public static function createUnavailability(\stdClass $data)
    {
        // phpcs:ignore
        if (empty($data->Debut) || empty($data->Fin)) {
            return null;
        }

        // phpcs:ignore
        return new Unavailability($data->Debut, $data->Fin);
    }
}
