<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\MondialRelay\Model;

class Search
{
    /**
     * @var string
     */
    private $zipCode;

    /**
     * @var string
     */
    private $country = 'FR';

    /**
     * @var int
     */
    private $limit = 10;

    /**
     * @var int|null
     */
    private $weight;

    /**
     * @var int|null
     */
    private $distance;

    public function setZipCode(string $zipCode): self
    {
        $this->zipCode = $zipCode;

        return $this;
    }

    public function setCountry(string $country): self
    {
        $this->country = $country;

        return $this;
    }

    public function setLimit(?int $limit): self
    {
        $this->limit = $limit;

        return $this;
    }

    public function setDistance(?int $distance): self
    {
        $this->distance = $distance;

        return $this;
    }

    public function setWeight(?int $weight): self
    {
        $this->weight = $weight;

        return $this;
    }

    public function toSoapParams(): array
    {
        return array_filter([
            'Pays'  => $this->country,
            //'Ville' => $search['city'] ?? '',
            'CP'    => $this->zipCode,
            //'Taille'    => "",
            'Poids' => (!empty($this->weight) && $this->weight > 100) ? $this->weight : null,
            //'Action'    => $search['deliveryMode'] ?? '',
            'RayonRecherche'    => $this->distance,
            //'TypeActivite'  => $ParcelShopActivityCode,
            //'DelaiEnvoi' => $SearchOpenningDelay,
            'NombreResultats' => $this->limit,
        ]);
    }
}
