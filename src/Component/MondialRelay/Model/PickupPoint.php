<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\MondialRelay\Model;

class PickupPoint implements \JsonSerializable
{
    /**
     * @var string
     */
    private $id;

    /**
     * @var string[]
     */
    private $address;

    /**
     * @var string
     */
    private $zipCode;

    /**
     * @var string
     */
    private $city;

    /**
     * @var string
     */
    private $country;

    /**
     * @var string|null
     */
    private $location1;

    /**
     * @var string|null
     */
    private $location2;

    /**
     * @var string
     */
    private $latitude;

    /**
     * @var string
     */
    private $longitude;

    /**
     * @var string
     */
    private $activityType;

    /**
     * @var string
     */
    private $information;

    /**
     * @var Opening[]
     */
    private $openingHours;

    /**
     * @var string
     */
    private $availabilityInformation;

    /**
     * @var string
     */
    private $urlPicture;

    /**
     * @var string
     */
    private $urlMap;

    /**
     * @var int
     *
     * The distance in meters.
     */
    private $distance;

    public function __construct(
        string $id,
        array $address,
        string $zipCode,
        string $city,
        string $country,
        ?string $location1,
        ?string $location2,
        string $latitude,
        string $longitude,
        string $activityType,
        ?string $information,
        array $openingHours,
        ?string $availabilityInformation,
        string $urlPicture,
        string $urlMap,
        int $distance
    ) {
        $this->id = $id;
        $this->address = $address;
        $this->zipCode = $zipCode;
        $this->city = $city;
        $this->country = $country;
        $this->location1 = $location1;
        $this->location2 = $location2;
        $this->latitude = $latitude;
        $this->longitude = $longitude;
        $this->activityType = $activityType;
        $this->information = $information;
        $this->openingHours = $openingHours;
        $this->availabilityInformation = $availabilityInformation;
        $this->urlPicture = $urlPicture;
        $this->urlMap = $urlMap;
        $this->distance = $distance;
    }

    public function jsonSerialize()
    {
        return [
            'id' => $this->id,
            'address' => $this->address,
            'zipCode' => $this->zipCode,
            'city' => $this->city,
            'country' => $this->country,
            'location1' => $this->location1,
            'location2' => $this->location2,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
            'activityType' => $this->activityType,
            'information' => $this->information,
            'openingHours' => $this->openingHours,
            'availabilityInformation' => $this->availabilityInformation,
            'urlPicture' => $this->urlPicture,
            'urlMap' => $this->urlMap,
            'distance' => $this->distance,
        ];
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function getAddress(): array
    {
        return $this->address;
    }

    public function getZipCode(): string
    {
        return $this->zipCode;
    }

    public function getCity(): string
    {
        return $this->city;
    }

    public function getCountry(): string
    {
        return $this->country;
    }

    public function getLocation1(): ?string
    {
        return $this->location1;
    }

    public function getLocation2(): ?string
    {
        return $this->location2;
    }

    public function getLatitude(): string
    {
        return $this->latitude;
    }

    public function getLongitude(): string
    {
        return $this->longitude;
    }

    public function getActivityType(): string
    {
        return $this->activityType;
    }

    public function getInformation(): ?string
    {
        return $this->information;
    }

    public function getOpeningHours(): array
    {
        return $this->openingHours;
    }

    public function getAvailabilityInformation(): ?string
    {
        return $this->availabilityInformation;
    }

    public function getUrlPicture(): string
    {
        return $this->urlPicture;
    }

    public function getUrlMap(): string
    {
        return $this->urlMap;
    }

    public function getDistance(): int
    {
        return $this->distance;
    }
}
