<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\MondialRelay\Model;

class StickerResult implements \JsonSerializable
{
    /**
     * @var string
     */
    private $trackingNumber;

    /**
     * @var string
     */
    private $labelUrl;

    public function __construct(string $trackingNumber, string $labelUrl)
    {
        $this->trackingNumber = $trackingNumber;
        $this->labelUrl = $labelUrl;
    }

    public function getTrackingNumber(): string
    {
        return $this->trackingNumber;
    }

    public function getLabelUrl(): string
    {
        return $this->labelUrl;
    }

    public function jsonSerialize()
    {
        return [
            'tracking_number' => $this->trackingNumber,
            'label_url' => $this->labelUrl,
        ];
    }
}
