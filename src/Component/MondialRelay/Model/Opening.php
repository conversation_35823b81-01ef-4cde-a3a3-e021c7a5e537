<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\MondialRelay\Model;

class Opening implements \JsonSerializable
{
    /**
     * Day of the week. 0 = Monday
     *
     * @var int
     */
    private $day;

    /**
     * The hour at which the relay point opens for the 1st time interval of the day.
     * Ex: 0930
     *
     * @var string
     */
    private $openingHour1;

    /**
     * The hour at which the relay point closes for the 1st time interval of the day.
     *
     * @var string
     */
    private $openingHour2;

    /**
     * The hour at which the relay point opens for the 2nd time interval of the day.
     *
     * @var string
     */
    private $openingHour3;

    /**
     * The hour at which the relay point closes for the 2nd time interval of the day.
     *
     * @var string
     */
    private $openingHour4;

    public function __construct(
        int $day,
        string $openingHour1,
        string $openingHour2,
        string $openingHour3,
        string $openingHour4
    ) {
        $this->day = $day;
        $this->openingHour1 = $openingHour1;
        $this->openingHour2 = $openingHour2;
        $this->openingHour3 = $openingHour3;
        $this->openingHour4 = $openingHour4;
    }

    public function jsonSerialize()
    {
        return [
            'day' => $this->day,
            'openingHour1' => $this->openingHour1,
            'openingHour2' => $this->openingHour2,
            'openingHour3' => $this->openingHour3,
            'openingHour4' => $this->openingHour4,
        ];
    }

    public function getDay(): int
    {
        return $this->day;
    }

    public function getOpeningHour1(): string
    {
        return $this->openingHour1;
    }

    public function getOpeningHour2(): string
    {
        return $this->openingHour2;
    }

    public function getOpeningHour3(): string
    {
        return $this->openingHour3;
    }

    public function getOpeningHour4(): string
    {
        return $this->openingHour4;
    }
}
