<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Log;

use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Encapsulation of console output and progressbar as a service in a Symfony Command context
 */
final class CommandLogService implements LogServiceInterface
{
    /** @var OutputInterface */
    private $output;

    /** @var ProgressBar */
    private $progressbar;

    public function setOutput(OutputInterface $output): self
    {
        $this->output = $output;

        return $this;
    }

    public function setProgressbar(ProgressBar $progressBar): self
    {
        $progressBar->setBarCharacter('<fg=green>-</>');
        $progressBar->setEmptyBarCharacter("<fg=black>-</>");
        $progressBar->setProgressCharacter("<fg=green>➤</>");

        $this->progressbar = $progressBar;

        return $this;
    }

    public function write(string $message, $level = 'info'): void
    {
        if ($this->output->isVeryVerbose()) {
            $this->output->writeln('');
            $this->output->writeln("<$level>$message</$level>");
        }
    }

    public function progress(): void
    {
        if ($this->progressbar instanceof ProgressBar) {
            $this->progressbar->advance();
        }
    }
}
