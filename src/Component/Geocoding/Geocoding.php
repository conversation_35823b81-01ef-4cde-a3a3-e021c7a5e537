<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Component\Geocoding;

use AlgoliaSearch\Client;
use AlgoliaSearch\Index;
use Psr\Log\LoggerInterface;
use Wizacha\Search\Config;

class Geocoding
{
    /** @var Index */
    protected $index;

    /** @var LoggerInterface */
    protected $logger;

    public function __construct(
        ?string $applicationId,
        ?string $algoliaApiKey,
        LoggerInterface $logger
    ) {
        if (\is_string($applicationId)
            && \is_string($algoliaApiKey)
            && mb_strlen($applicationId) > 0
            && mb_strlen($algoliaApiKey) > 0
        ) {
            $this->index = (new Client($applicationId, $algoliaApiKey))->initIndex(Config::INDEX_GEOCODING);
        }

        $this->logger = $logger;
    }

    /**
     * @return null|array
     * * 'lat' => float
     * * 'lng' => float
     * * 'label' => string
     * * 'postal' => string
     */
    public function getGeocoding(string $postal, string $label): ?array
    {
        $label = $this->escapeLabel($label);

        if (false === $this->index instanceof Index) {
            $this->logger->info("[Geocoding] You must enable Algolia to be able to search the location for $postal $label.");

            return null;
        }

        $result = $this->index->search("$postal $label");

        if (\count($result['hits']) === 0) {
            return null;
        }

        return [
            'label' => $result['hits'][0]['name'],
            'postal' => $postal,
            'lat' => $result['hits'][0]['_geoloc']['lat'],
            'lng' => $result['hits'][0]['_geoloc']['lng'],
        ];
    }

    /**
     * @param string $label
     * @return string mixed
     */
    protected function escapeLabel($label)
    {
        return preg_replace(
            '/[^ [:alnum:]]+/',
            ' ',
            transliterator_transliterate(
                'Any-Latin; Latin-ASCII; Upper()',
                $label
            )
        );
    }
}
