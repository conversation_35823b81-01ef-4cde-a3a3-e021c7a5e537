<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Dolist\SoapEntities;

class AuthenticationRequest
{
    /**
     * @var int
     */
    public $AccountID;

    /**
     * @var string
     */
    public $AuthenticationKey;

    public function __construct(int $AccountID, string $AuthenticationKey)
    {
        $this->AccountID = $AccountID;
        $this->AuthenticationKey = $AuthenticationKey;
    }
}
