<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Dolist\SoapEntities;

use MyCLabs\Enum\Enum;

/**
 * @method static ContentType MULTIPART()
 * @method static ContentType HTML()
 * @method static ContentType TEXT()
 */
class ContentType extends Enum
{
    public const MULTIPART = 'EmailMultipart';
    public const HTML = 'EmailHtmlOnly';
    public const TEXT = 'EmailTextOnly';
}
