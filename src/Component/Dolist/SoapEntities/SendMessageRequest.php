<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Dolist\SoapEntities;

class SendMessageRequest
{
    /**
     * @var array La collection des pièces jointes du message
     * @todo: Not yet implemented
     */
    public $Attachments = [];

    /**
     * @var string Données variables du message : chaine XML ( se référer à l'onglet Envoi de test dans le module de creation de campagne)
     */
    public $Data;

    /**
     * @var bool (optionnel) indique si on envoie un message de test, faux par défaut (si votre template n'est pas validé, vos envois sont limités aux 10 adresses définis dans l'onglet administration
     */
    public $IsTest = false;

    /**
     * @var ContentType Le type d'envoi (string): 3 valeurs possibles -> EmailMultipart, EmailHtmlOnly, EmailTextOnly
     */
    public $MessageContentType;

    /**
     * @var string L'adresse du destinataire
     */
    public $Recipient;

    /**
     * @var string (optionnel) Identifiant externe du destinataire fourni par votre SI, par défaut l'email
     */
    public $RecipientExternalID;

    /**
     * @var int L'identifiant du template
     */
    public $TemplateID;

    /**
     * @var string (optionnel) Identifiant unique de message fourni par votre SI
     */
    public $UniqueExternalID;
}
