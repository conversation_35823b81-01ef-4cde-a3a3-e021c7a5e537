<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Dolist\SoapEntities;

class AuthenticationTokenContext
{
    /**
     * @var int
     */
    public $AccountID;

    /**
     * @var string
     */
    public $Key;

    public function __construct(int $AccountID, string $Key)
    {
        $this->AccountID = $AccountID;
        $this->Key = $Key;
    }
}
