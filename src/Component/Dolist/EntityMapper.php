<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Dolist;

use Wizacha\C2C\Code;
use Wizacha\Component\Dolist\Entities\Comment;
use Wizacha\Component\Dolist\Entities\DiscussionLink;
use Wizacha\Component\Dolist\Entities\Option;
use Wizacha\Component\Dolist\Entities\OrderBillingAddress;
use Wizacha\Component\Dolist\Entities\OrderShippingAddress;
use Wizacha\Component\Dolist\Entities\PasswordRecoveryLink;
use Wizacha\Component\Dolist\Entities\PlainTextPassword;
use Wizacha\Component\Dolist\Entities\ProductReport;
use Wizacha\Component\Dolist\Entities\RefundTemplate;
use Wizacha\Component\Dolist\Entities\ReturnAddress;
use Wizacha\Component\Dolist\Entities\Settings;
use Wizacha\Marketplace\Cms\Event\ContactFormSubmitted;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderAddress;
use Wizacha\Marketplace\Order\OrderItem;
use Wizacha\Marketplace\Order\OrderReturn\OrderReturn;
use Wizacha\Marketplace\Order\OrderReturn\ReturnItem;
use Wizacha\Marketplace\Order\Shipment;
use Wizacha\Marketplace\Order\ShipmentItem;
use Wizacha\Marketplace\ReadModel\Product;
use Wizacha\Marketplace\User\User;

class EntityMapper
{
    /**
     * @var array
     */
    private $entities;

    public function __construct(array $entities)
    {
        $this->entities = $entities;

        $settings = new Settings();
        if (!\in_array($settings, $this->entities)) {
            $this->entities[] = $settings;
        }
    }

    public function getXml(): string
    {
        $xml = new \DOMDocument('1.0', 'utf-8');
        $emtRoot = $xml->createElement('emtroot');
        $xml->appendChild($emtRoot);

        foreach ($this->entities as $entity) {
            $domElement = $this->mapEntity($xml, $entity);
            if ($domElement) {
                $emtRoot->appendChild($domElement);
            }
        }

        return $xml->saveXML();
    }

    private function mapEntity(\DOMDocument $document, $entity): ?\DOMElement
    {
        if (empty($entity)) {
            return null;
        }

        if (\is_array($entity)) {
            return $this->mapCollection($document, $entity);
        }

        $type = $this->getDolistType($entity);
        $domElement = $document->createElement('Marketplace' . $type);
        $method = 'mapType' . $type;
        $children = $this->$method($document, $entity);
        foreach ($children as $child) {
            $domElement->appendChild($child);
        }

        return $domElement;
    }

    private function mapCollection(\DOMDocument $document, $collection): \DOMElement
    {
        $type = $this->getDolistType(reset($collection));

        $domElement = $document->createElement("Marketplace{$type}COLLECTION");

        foreach ($collection as $entity) {
            $domSubElement = $this->mapEntity($document, $entity);
            if ($domSubElement) {
                $domElement->appendChild($domSubElement);
            }
        }

        return $domElement;
    }

    private function getDolistType($entity): string
    {
        switch (\get_class($entity)) {
            case User::class:
                return 'User';
            case PasswordRecoveryLink::class:
                return 'PasswordRecoveryLink';
            case DiscussionLink::class:
                return 'DiscussionLink';
            case Company::class:
                return 'Company';
            case PlainTextPassword::class:
                return 'PlainTextPassword';
            case Comment::class:
                return 'Comment';
            case ProductReport::class:
                return 'ProductReport';
            case Option::class:
                return 'Option';
            case Order::class:
                return 'Order';
            case OrderItem::class:
                return 'OrderItem';
            case OrderBillingAddress::class:
                return 'OrderBillingAddress';
            case OrderShippingAddress::class:
                return 'OrderShippingAddress';
            case Shipment::class:
                return 'Shipment';
            case ShipmentItem::class:
                return 'ShipmentItem';
            case Product::class:
                return 'Product';
            case OrderReturn::class:
                return 'Return';
            case ReturnAddress::class:
                return 'ReturnAddress';
            case ReturnItem::class:
                return 'ReturnItem';
            case ContactFormSubmitted::class:
                return 'ContactForm';
            case Code::class:
                return 'OrderCode';
            case Settings::class:
                return 'Settings';
            case RefundTemplate::class:
                return 'Refund';
        }

        throw new \Exception('Dolist: unrecognized entity', ['entity' => $entity]);
    }

    private function escape(string $str): string
    {
        return htmlspecialchars($str, ENT_NOQUOTES);
    }

    /** Methods bellow are called with ->$method() */

    private function mapTypeUser(\DOMDocument $document, User $user): array
    {
        return [
            $document->createElement('Email', $this->escape($user->getEmail())),
            $document->createElement('Title', $user->getTitle() ? $this->escape(__($user->getTitle()->getTranslationKey())) : ''),
            $document->createElement('Firstname', $this->escape($user->getFirstname())),
            $document->createElement('Lastname', $this->escape($user->getLastname())),
            $document->createElement('Fullname', $this->escape($user->getFullName())),
        ];
    }

    private function mapTypePasswordRecoveryLink(\DOMDocument $document, PasswordRecoveryLink $token): array
    {
        return [
            $document->createElement('Link', $this->escape($token->getLink())),
        ];
    }

    private function mapTypeDiscussionLink(\DOMDocument $document, DiscussionLink $token): array
    {
        return [
            $document->createElement('Link', $this->escape($token->getLink())),
        ];
    }

    private function mapTypeCompany(\DOMDocument $document, Company $company): array
    {
        return [
            $document->createElement('Name', $this->escape($company->getName())),
            $document->createElement('Email', $this->escape($company->getEmail())),
            $document->createElement('Phone', $this->escape($company->getPhone())),
            $document->createElement('Website', $this->escape($company->getUrl())),
            $document->createElement('Address', $this->escape($company->getAddress())),
            $document->createElement('Zipcode', $this->escape($company->getZipcode())),
            $document->createElement('City', $this->escape($company->getCity())),
            $document->createElement('Nation', $this->escape($company->getCountry())),
            $document->createElement('Latitude', (string) $company->getLatitude()),
            $document->createElement('Longitude', (string) $company->getLongitude()),
        ];
    }

    private function mapTypePlainTextPassword(\DOMDocument $document, PlainTextPassword $token): array
    {
        return [
            $document->createElement('Password', $this->escape($token->getPassword())),
        ];
    }

    private function mapTypeComment(\DOMDocument $document, Comment $token): array
    {
        return [
            $document->createElement('Comment', $this->escape($token->getComment())),
        ];
    }

    private function mapTypeProductReport(\DOMDocument $document, ProductReport $report): array
    {
        return [
            $document->createElement('UserName', $this->escape($report->getUserName())),
            $document->createElement('UserEmail', $this->escape($report->getUserEmail())),
            $document->createElement('Message', $this->escape($report->getMessage())),
            $document->createElement('AdminLink', $this->escape($report->getAdminUrl())),
            $document->createElement('ShopLink', $this->escape($report->getShopUrl())),
        ];
    }

    private function mapTypeOption(\DOMDocument $document, Option $option): array
    {
        return [
            $document->createElement('Name', $this->escape($option->getName())),
        ];
    }

    private function mapTypeOrder(\DOMDocument $document, Order $order): array
    {
        $statusHeader = html_entity_decode(strip_tags(__('order_email_header_' . $order->getStatus()->getValue())));

        return [
            $document->createElement('Id', (string) $order->getId()),
            $document->createElement('Total', (string) $order->getTotal()->getConvertedAmount()),
            $document->createElement('Subtotal', (string) $order->getSubtotal()->getConvertedAmount()),
            $document->createElement('TaxTotal', (string) $order->getTaxTotal()->getConvertedAmount()),
            $document->createElement('ShippingCost', (string) $order->getShippingCost()->getConvertedAmount()),
            $document->createElement('ShippingName', $this->escape($order->getShippingName())),
            $document->createElement('Date', $this->escape($order->getTimestamp()->setTimezone(new \DateTimeZone(date_default_timezone_get()))->format('d/m/Y H:i:s'))),
            $document->createElement('StatusHeader', $this->escape($statusHeader)),
            $document->createElement('DeclineReason', $this->escape((string) $order->getDeclineReason())),
        ];
    }

    private function mapTypeOrderItem(\DOMDocument $document, OrderItem $item): array
    {
        return [
            $document->createElement('Name', $this->escape($item->getProductName())),
            $document->createElement('Price', (string) $item->getPrice()->getConvertedAmount()),
            $document->createElement('Amount', (string) $item->getAmount()),
        ];
    }

    private function mapTypeOrderBillingAddress(\DOMDocument $document, OrderAddress $item): array
    {
        return [
            $document->createElement('Title', $this->escape(__($item->getTitle()->getTranslationKey()))),
            $document->createElement('Firstname', $this->escape($item->getFirstname())),
            $document->createElement('Lastname', $this->escape($item->getLastname())),
            $document->createElement('Address', $this->escape($item->getAddress())),
            $document->createElement('Address2', $this->escape($item->getAddress2())),
            $document->createElement('City', $this->escape($item->getCity())),
            $document->createElement('Zipcode', $this->escape($item->getZipcode())),
            $document->createElement('Nation', $this->escape($item->getCountry())),
            $document->createElement('Company', $this->escape($item->getCompany())),
        ];
    }

    private function mapTypeOrderShippingAddress(\DOMDocument $document, OrderAddress $item): array
    {
        return $this->mapTypeOrderBillingAddress($document, $item);
    }

    private function mapTypeShipment(\DOMDocument $document, Shipment $item): array
    {
        return [
            $document->createElement('TrackingNumber', $this->escape($item->getChronopostSkybillNumber() ?: $item->getTrackingNumber())),
            $document->createElement('Comment', $this->escape($item->getComments())),
        ];
    }

    private function mapTypeShipmentItem(\DOMDocument $document, ShipmentItem $item): array
    {
        return [
            $document->createElement('Amount', (string) $item->getAmount()),
            $document->createElement('Name', $this->escape($item->getItem()->getProductName())),
            $document->createElement('Price', (string) $item->getItem()->getPrice()->getConvertedAmount()),
        ];
    }

    private function mapTypeProduct(\DOMDocument $document, Product $item): array
    {
        return [
            $document->createElement('Name', $this->escape($item->getName())),
        ];
    }

    private function mapTypeReturn(\DOMDocument $document, OrderReturn $item): array
    {
        return [
            $document->createElement('Date', $this->escape($item->getCreatedAt()->setTimezone(new \DateTimeZone(date_default_timezone_get()))->format('d/m/Y H:i:s'))),
        ];
    }

    private function mapTypeReturnAddress(\DOMDocument $document, ReturnAddress $item): array
    {
        return [
            $document->createElement('Name', $this->escape($item->getName())),
            $document->createElement('Address', $this->escape($item->getAddress())),
            $document->createElement('City', $this->escape($item->getCity())),
            $document->createElement('Zipcode', $this->escape($item->getZipcode())),
            $document->createElement('Nation', $this->escape($item->getCountry())),
        ];
    }

    private function mapTypeReturnItem(\DOMDocument $document, ReturnItem $item): array
    {
        static $rmaReasons;
        if (empty($rmaReasons)) {
            $rmaReasons = fn_get_rma_properties(RMA_REASON);
        }

        $reason = $rmaReasons[$item->getReason()]['property'] ?? '';

        return [
            $document->createElement('Name', $this->escape($item->getProduct())),
            $document->createElement('Amount', (string) $item->getAmount()),
            $document->createElement('Price', (string) $item->getPrice()),
            $document->createElement('Reason', $this->escape($reason)),
        ];
    }

    private function mapTypeContactForm(\DOMDocument $document, ContactFormSubmitted $item): array
    {
        $contactType = $username = $phone = $body = $sender = '';

        foreach ($item->getElements() as $element) {
            switch ($element['description']) {
                case __('contact_type'):
                    $contactType = $element['value'];
                    break;
                case __('username'):
                    $username = $element['value'];
                    break;
                case __('phone_number'):
                    $phone = $element['value'];
                    break;
                case __('body'):
                    $body = $element['value'];
                    break;
                case __('sender'):
                    $sender = $element['value'];
                    break;
            }
        }

        return [
            $document->createElement('Subject', $this->escape($item->getSubject())),
            $document->createElement('Type', $this->escape($contactType)),
            $document->createElement('Username', $this->escape($username)),
            $document->createElement('Phone', $this->escape($phone)),
            $document->createElement('Body', $this->escape($body)),
            $document->createElement('Sender', $this->escape($sender)),
        ];
    }

    private function mapTypeOrderCode(\DOMDocument $document, Code $code): array
    {
        return [
            $document->createElement('Line1Col1', $this->escape((string) $code->full()[0])),
            $document->createElement('Line1Col2', $this->escape((string) $code->full()[1])),
            $document->createElement('Line1Col3', $this->escape((string) $code->full()[2])),
            $document->createElement('Line2Col1', $this->escape((string) $code->full()[3])),
            $document->createElement('Line2Col2', $this->escape((string) $code->full()[4])),
            $document->createElement('Line2Col3', $this->escape((string) $code->full()[5])),
            $document->createElement('Line3Col1', $this->escape((string) $code->full()[6])),
            $document->createElement('Line3Col2', $this->escape((string) $code->full()[7])),
            $document->createElement('Line3Col3', $this->escape((string) $code->full()[8])),
        ];
    }

    private function mapTypeSettings(\DOMDocument $document, Settings $settings): array
    {
        return [
            $document->createElement('BaseURL', $this->escape($settings->getBaseUrl())),
        ];
    }

    private function mapTypeRefund(\DOMDocument $document, RefundTemplate $template): array
    {
        return [
            $document->createElement('OrderId', (string) $template->getOrderId()),
            $document->createElement('OrderBalance', (string) $template->getOrderBalance()),
            $document->createElement('Currency', $this->escape($template->getCurrency())),
            $document->createElement('RefundAmount', (string) $template->getRefundAmount()),
        ];
    }
}
