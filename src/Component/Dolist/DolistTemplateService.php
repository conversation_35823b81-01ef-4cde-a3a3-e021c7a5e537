<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Dolist;

use Wizacha\Component\Dolist\Entities\DolistTemplate;

class DolistTemplateService
{
    /**
     * @var DolistTemplateRepository
     */
    private $repository;

    public function __construct(DolistTemplateRepository $repository)
    {
        $this->repository = $repository;
    }

    public function find()
    {
        return $this->repository->findAll();
    }

    /**
     * @param DolistTemplateType $templateType
     * @param int $templateId
     * @return null|DolistTemplate
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function save(DolistTemplateType $templateType, int $templateId): ?DolistTemplate
    {
        /** @var null|DolistTemplate $template */
        $template = $this->repository->find($templateType);

        if ($template) {
            $template->setTemplateId($templateId);
            $this->repository->save($template);
        }

        return $template;
    }
}
