<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Dolist;

use MyCLabs\Enum\Enum;

/**
 * @method static DolistTemplateType PASSWORD_RECOVER()
 * @method static DolistTemplateType USER_PROFILE_ACTIVATED()
 * @method static DolistTemplateType USER_PROFILE_CREATED()
 * @method static DolistTemplateType USER_PROFILE_UPDATED()
 * @method static DolistTemplateType DISCUSSION_RECEIVED()
 * @method static DolistTemplateType COMPANY_APPLIED()
 * @method static DolistTemplateType ADMIN_COMPANY_APPLIED()
 * @method static DolistTemplateType COMPANY_APPROVED()
 * @method static DolistTemplateType COMPANY_REJECTED()
 * @method static DolistTemplateType COMPANY_DISABLED()
 * @method static DolistTemplateType PRODUCT_REPORTED()
 * @method static DolistTemplateType OPTION_APPROVED()
 * @method static DolistTemplateType OPTION_REJECTED()
 * @method static DolistTemplateType ORDER_STATUS()
 * @method static DolistTemplateType COMPANY_ORDER_STATUS()
 * @method static DolistTemplateType ADMIN_ORDER_STATUS()
 * @method static DolistTemplateType SHIPMENT_CREATED()
 * @method static DolistTemplateType PRODUCT_APPROVED()
 * @method static DolistTemplateType PRODUCT_REJECTED()
 * @method static DolistTemplateType PRODUCT_REQUIRES_CHANGES()
 * @method static DolistTemplateType VENDOR_RETURN_REQUESTED()
 * @method static DolistTemplateType USER_RETURN_REQUESTED()
 * @method static DolistTemplateType ADMIN_RETURN_REQUESTED()
 * @method static DolistTemplateType VENDOR_RETURN_COMPLETED()
 * @method static DolistTemplateType USER_RETURN_COMPLETED()
 * @method static DolistTemplateType ADMIN_RETURN_COMPLETED()
 * @method static DolistTemplateType VENDOR_RETURN_RECEIVED()
 * @method static DolistTemplateType USER_RETURN_RECEIVED()
 * @method static DolistTemplateType ADMIN_RETURN_RECEIVED()
 * @method static DolistTemplateType VENDOR_RETURN_DECLINED()
 * @method static DolistTemplateType USER_RETURN_DECLINED()
 * @method static DolistTemplateType ADMIN_RETURN_DECLINED()
 * @method static DolistTemplateType CONTACT()
 * @method static DolistTemplateType ORDER_CODE_GENERATED()
 * @method static DolistTemplateType AFTER_SALES_SERVICE_REQUESTED()
 * @method static DolistTemplateType ORDER_REFUNDED_ADMIN()
 * @method static DolistTemplateType ORDER_REFUNDED_VENDOR()
 * @method static DolistTemplateType ORDER_REFUNDED_CUSTOMER()
 */
class DolistTemplateType extends Enum
{
    public const PASSWORD_RECOVER = 'password_recover';
    public const USER_PROFILE_ACTIVATED = 'user_profile_activated';
    public const USER_PROFILE_CREATED = 'user_profile_created';
    public const USER_PROFILE_UPDATED = 'user_profile_updated';
    public const DISCUSSION_RECEIVED = 'discussion_received';
    public const COMPANY_APPLIED = 'company_applied';
    public const ADMIN_COMPANY_APPLIED = 'admin_company_applied';
    public const COMPANY_APPROVED = 'company_approved';
    public const COMPANY_REJECTED = 'company_rejected';
    public const COMPANY_DISABLED = 'company_disabled';
    public const PRODUCT_REPORTED = 'product_reported';
    public const OPTION_APPROVED = 'option_approved';
    public const OPTION_REJECTED = 'option_rejected';
    public const ORDER_STATUS = 'order_status';
    public const COMPANY_ORDER_STATUS = 'company_order_status';
    public const ADMIN_ORDER_STATUS = 'admin_order_status';
    public const SHIPMENT_CREATED = 'shipment_created';
    public const PRODUCT_APPROVED = 'product_approved';
    public const PRODUCT_REJECTED = 'product_rejected';
    public const PRODUCT_REQUIRES_CHANGES = 'product_requires_changes';
    public const VENDOR_RETURN_REQUESTED = 'vendor_return_requested';
    public const USER_RETURN_REQUESTED = 'user_return_requested';
    public const ADMIN_RETURN_REQUESTED = 'admin_return_requested';
    public const VENDOR_RETURN_COMPLETED = 'vendor_return_completed';
    public const USER_RETURN_COMPLETED = 'user_return_completed';
    public const ADMIN_RETURN_COMPLETED = 'admin_return_completed';
    public const VENDOR_RETURN_RECEIVED = 'vendor_return_received';
    public const USER_RETURN_RECEIVED = 'user_return_received';
    public const ADMIN_RETURN_RECEIVED = 'admin_return_received';
    public const VENDOR_RETURN_DECLINED = 'vendor_return_declined';
    public const USER_RETURN_DECLINED = 'user_return_declined';
    public const ADMIN_RETURN_DECLINED = 'admin_return_declined';
    public const CONTACT = 'contact';
    public const ORDER_CODE_GENERATED = 'order_code_generated';
    public const AFTER_SALES_SERVICE_REQUESTED = 'after_sales_service_requested';
    public const ORDER_REFUNDED_ADMIN = 'order_refunded_admin';
    public const ORDER_REFUNDED_VENDOR = 'order_refunded_vendor';
    public const ORDER_REFUNDED_CUSTOMER = 'order_refunded_customer';
}
