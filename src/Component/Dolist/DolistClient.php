<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Dolist;

use Tygh\Backend\Cache\ABackend;
use Wizacha\Async\Dispatcher;
use Wizacha\Component\Dolist\Exception\CannotSendMessage;
use Wizacha\Component\Dolist\SoapEntities\AuthenticationRequest;
use Wizacha\Component\Dolist\SoapEntities\AuthenticationTokenContext;
use Wizacha\Component\Dolist\SoapEntities\ContentType;
use Wizacha\Component\Dolist\SoapEntities\SendMessageRequest;

class DolistClient
{
    /**
     * @var \SoapClient|null
     */
    private $msgSoapClient;

    /**
     * @var ABackend
     */
    private $cache;

    /**
     * @var int
     */
    private $accountId;

    /**
     * @var string
     */
    private $authenticationKey;

    /**
     * @var bool
     */
    private $isTest;

    /**
     * @var Dispatcher
     */
    private $jobDispatcher;

    public function __construct(
        ABackend $cache,
        Dispatcher $jobDispatcher,
        int $accountId,
        string $authenticationKey,
        bool $isTest
    ) {
        $this->cache = $cache;
        $this->accountId = $accountId;
        $this->authenticationKey = $authenticationKey;
        $this->isTest = $isTest;
        $this->jobDispatcher = $jobDispatcher;
    }

    public function sendMessage(string $email, int $templateId, array $data)
    {
        if ($this->accountId === 0 || $templateId === 0) {
            return;
        }

        $message = new SendMessageRequest();
        $message->IsTest = $this->isTest;
        $message->Recipient = $email;
        $message->TemplateID = $templateId;
        $message->MessageContentType = ContentType::MULTIPART();

        $mapper = new EntityMapper($data);
        $message->Data = $mapper->getXml();

        try {
            $token = $this->getAuthToken();
        } catch (\SoapFault $e) {
            throw new CannotSendMessage('Cannot generate authentication token', 0, $e);
        }

        $this->sendMessageRequest($token, $message);
    }

    public function sendMessageRequest(AuthenticationTokenContext $tokenContext, SendMessageRequest $messageRequest)
    {
        if ($this->jobDispatcher->delayExec('app.dolist_client' . '::' . __FUNCTION__, \func_get_args())) {
            return;
        }

        try {
            $this->getMessageClient()->SendMessage([
                'token' => $tokenContext,
                'message' => $messageRequest,
            ]);
        } catch (\SoapFault $e) {
            // Remove token and throw exception to mark message as failed. Worker will retry it
            $this->removeAuthTokenInCache();

            throw new CannotSendMessage('An error occurred while sending the message', 0, $e);
        }
    }

    private function getMessageClient(): \SoapClient
    {
        if ($this->msgSoapClient) {
            return $this->msgSoapClient;
        }

        $this->msgSoapClient = new \SoapClient('https://api.emt.dolist.net/V3/MessageService.svc?wsdl', [
            'soap_version' => SOAP_1_1,
            'location' => 'https://api.emt.dolist.net/V3/MessageService.svc/soap1.1',
        ]);

        return $this->msgSoapClient;
    }

    private function getAuthToken(): AuthenticationTokenContext
    {
        $cachedToken = $this->cache->get('dolist_auth_token');

        if (!empty($cachedToken)) {
            return new AuthenticationTokenContext($this->accountId, reset($cachedToken));
        }

        $authSoapClient = new \SoapClient('https://api.emt.dolist.net/V3/AuthenticationService.svc?wsdl', [
            'soap_version' => SOAP_1_1,
            'location' => 'https://api.emt.dolist.net/V3/AuthenticationService.svc/soap1.1',
        ]);

        $authRequest = new AuthenticationRequest($this->accountId, $this->authenticationKey);

        $result = $authSoapClient->GetAuthenticationToken(['authenticationRequest' => $authRequest]);

        // Set the token in cache with 1800 seconds lifetime. The token is valid 3600 seconds but we remove 1800s to be sure
        $this->cache->set('dolist_auth_token', $result->GetAuthenticationTokenResult->Key, 1800);

        return new AuthenticationTokenContext($this->accountId, $result->GetAuthenticationTokenResult->Key);
    }

    private function removeAuthTokenInCache()
    {
        $this->cache->clear('dolist_auth_token');
    }
}
