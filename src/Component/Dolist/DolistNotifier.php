<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Dolist;

use Symfony\Component\DependencyInjection\ContainerInterface;
use Wizacha\Component\Dolist\Entities\DolistTemplate;
use Wizacha\Component\Dolist\Exception\NotFoundDolistTemplate;
use Wizacha\Marketplace\User\User;

abstract class DolistNotifier
{
    /**
     * @var ContainerInterface
     */
    protected $container;

    /**
     * @var DolistClient
     */
    private $dolistClient;

    /**
     * @var DolistTemplateRepository
     */
    private $repository;

    public function __construct(ContainerInterface $container, DolistClient $dolistClient, DolistTemplateRepository $repository)
    {
        $this->container = $container;
        $this->dolistClient = $dolistClient;
        $this->repository = $repository;
    }

    protected function sendEmailToCustomer(User $customer, DolistTemplateType $template, array $templateVariables = [])
    {
        $templateId = $this->resolveTemplate($template);

        $this->dolistClient->sendMessage($customer->getEmail(), $templateId, $templateVariables);
    }

    protected function sendEmailToAdmin(DolistTemplateType $template, array $templateVariables = [])
    {
        $templateId = $this->resolveTemplate($template);
        $adminCompany = $this->container->get('marketplace.admin_company');

        $this->dolistClient->sendMessage($adminCompany->getSiteAdministratorEmail(), $templateId, $templateVariables);
    }

    protected function sendEmailToAddress(string $address, DolistTemplateType $template, array $templateVariables = [])
    {
        $templateId = $this->resolveTemplate($template);

        $this->dolistClient->sendMessage($address, $templateId, $templateVariables);
    }

    /**
     * @param DolistTemplateType $template
     * @return int Dolist template ID
     * @throws NotFoundDolistTemplate
     */
    private function resolveTemplate(DolistTemplateType $template): int
    {
        /** @var DolistTemplate $entity */
        $entity = $this->repository->find($template->getValue());

        if (\is_null($entity->getTemplateId())) {
            throw new NotFoundDolistTemplate();
        }

        return $entity->getTemplateId();
    }
}
