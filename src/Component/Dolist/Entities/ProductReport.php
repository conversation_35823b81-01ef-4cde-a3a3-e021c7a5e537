<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Dolist\Entities;

class ProductReport
{
    /**
     * @var string
     */
    private $userName;

    /**
     * @var string
     */
    private $userEmail;

    /**
     * @var string
     */
    private $message;

    /**
     * @var string
     */
    private $shopUrl;

    /**
     * @var string
     */
    private $adminUrl;

    public function __construct(string $userName, string $userEmail, string $message, string $shopUrl, string $adminUrl)
    {
        $this->userName = $userName;
        $this->userEmail = $userEmail;
        $this->message = $message;
        $this->shopUrl = $shopUrl;
        $this->adminUrl = $adminUrl;
    }

    public function getUserName(): string
    {
        return $this->userName;
    }

    public function getUserEmail(): string
    {
        return $this->userEmail;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function getShopUrl(): string
    {
        return $this->shopUrl;
    }

    public function getAdminUrl(): string
    {
        return $this->adminUrl;
    }
}
