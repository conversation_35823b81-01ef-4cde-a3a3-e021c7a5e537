<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Dolist\Entities;

use Wizacha\Marketplace\Order\OrderAddress;

class OrderShippingAddress extends OrderAddress
{
    public static function fromOrderAddress(OrderAddress $address)
    {
        return new self([
            'title' => $address->getTitle(),
            'firstname' => $address->getFirstname(),
            'lastname' => $address->getLastname(),
            'company' => $address->getCompany(),
            'address' => $address->getAddress(),
            'address2' => $address->getAddress2(),
            'city' => $address->getCity(),
            'zipcode' => $address->getZipcode(),
            'country' => $address->getCountry(),
        ]);
    }
}
