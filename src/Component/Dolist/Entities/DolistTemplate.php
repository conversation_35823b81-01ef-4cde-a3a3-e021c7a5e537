<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Dolist\Entities;

use Wizacha\Component\Dolist\DolistTemplateType;

class DolistTemplate
{
    /** @var DolistTemplateType */
    private $templateType;

    /** @var int */
    private $templateId;

    public function __construct(DolistTemplateType $templateType, int $templateId)
    {
        $this->templateType = $templateType;
        $this->templateId = $templateId;
    }

    public function getTemplateType(): DolistTemplateType
    {
        if ($this->templateType instanceof DolistTemplateType === false) {
            $this->setTemplateType(new DolistTemplateType($this->templateType));
        }

        return $this->templateType;
    }

    public function setTemplateType(DolistTemplateType $templateType): self
    {
        $this->templateType = $templateType;

        return $this;
    }

    public function getTemplateId(): int
    {
        return $this->templateId;
    }

    public function setTemplateId(int $templateId): self
    {
        $this->templateId = $templateId;

        return $this;
    }
}
