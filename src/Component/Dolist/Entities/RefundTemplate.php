<?php

declare(strict_types=1);

namespace Wizacha\Component\Dolist\Entities;

class RefundTemplate
{
    /** @var int */
    protected $orderId;

    /** @var float */
    protected $orderBalance;

    /** @var string */
    protected $currency;

    /** @var float */
    protected $refundAmount;

    public function __construct(int $orderId, float $orderBalance, string $currency, float $refundAmount)
    {
        $this->orderId = $orderId;
        $this->orderBalance = $orderBalance;
        $this->currency = $currency;
        $this->refundAmount = $refundAmount;
    }

    public function getOrderId(): int
    {
        return $this->orderId;
    }

    public function setOrderId(int $orderId): self
    {
        $this->orderId = $orderId;

        return $this;
    }

    public function getOrderBalance(): float
    {
        return $this->orderBalance;
    }

    public function setOrderBalance(float $orderBalance): self
    {
        $this->orderBalance = $orderBalance;

        return $this;
    }

    public function getCurrency(): string
    {
        return $this->currency;
    }

    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;

        return $this;
    }

    public function getRefundAmount(): float
    {
        return $this->refundAmount;
    }

    public function setRefundAmount(float $refundAmount): self
    {
        $this->refundAmount = $refundAmount;

        return $this;
    }
}
