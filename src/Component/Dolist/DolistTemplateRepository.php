<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Dolist;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Wizacha\Component\Dolist\Entities\DolistTemplate;

class DolistTemplateRepository extends ServiceEntityRepository
{
    /**
     * @param DolistTemplate $dolistTemplate
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function save(DolistTemplate $dolistTemplate)
    {
        $this->getEntityManager()->persist($dolistTemplate);
        $this->getEntityManager()->flush();
    }
}
