<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Storage;

use Rhumsaa\Uuid\Uuid;

/**
 * Cette interface décrit une manière simple pour la persistence des fichiers
 *
 * Dans le principe de séparation des responsabilités de lecture/écriture, il
 * n'est ici qu'une question d'écriture
 */
interface Storage
{
    /**
     * @return Uuid Identifiant qui permettra de référencer le fichier pour la lecture
     */
    public function store(\SplFileObject $file): Uuid;
}
