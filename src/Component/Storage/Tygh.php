<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Storage;

use Rhumsaa\Uuid\Uuid;
use Wizacha\Storage\StorageService;

class Tygh implements Storage
{
    private ?StorageService $storageService;

    private string $path;

    public function __construct(StorageService $storageService, string $path)
    {
        $this->storageService = $storageService;
        $this->path = trim($path, '/');
    }

    public function store(\SplFileObject $file): Uuid
    {
        do {
            $uuid = Uuid::uuid4();
            $path = $this->path . '/' . $uuid; // ltrim au cas où this->path est vide
        } while ($this->storageService->isExist($path));

        $file->fseek(0);
        $content = '';

        while (!$file->eof()) {
            $content .= $file->fread(4096);
        }

        $this->storageService->put(
            $path,
            ['contents' => $content]
        );

        return $uuid;
    }
}
