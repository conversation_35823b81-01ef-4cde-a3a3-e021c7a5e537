<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\PdfGenerator;

use Knp\Snappy\Pdf;

class PdfGenerator
{
    /** @var Pdf */
    protected $knappy;

    public function __construct(Pdf $knappy)
    {
        $this->knappy = $knappy;
    }

    /** @param string[] $parameters */
    public function convertHtmlToPdf(string $htmlContent, array $parameters = []): string
    {
        return $this->knappy->getOutputFromHtml($htmlContent, $this->sanitizeParameters($parameters));
    }

    /**
     * @param string[] $parameters
     * @return string[]
     */
    protected function sanitizeParameters(array $parameters): array
    {
        // str to int
        foreach (['margin-bottom', 'margin-top'] as $key) {
            if (\array_key_exists($key, $parameters) === true && \is_string($parameters[$key])) {
                $parameters[$key] = \strlen($parameters[$key]) > 0 ? (int) $parameters[$key] : 0;
            }
        }

        return $parameters;
    }
}
