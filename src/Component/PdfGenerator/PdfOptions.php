<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\PdfGenerator;

use MyCLabs\Enum\Enum;

/**
 * @method static PdfOptions ENCODING()
 * @method static PdfOptions ENCODING_UTF8()
 * @method static PdfOptions HEADER_HTML()
 * @method static PdfOptions FOOTER_HTML()
 * @method static PdfOptions MARGIN_TOP()
 * @method static PdfOptions MARGIN_BOTTOM()
 */
class PdfOptions extends Enum
{
    protected const ENCODING = 'encoding';
    protected const ENCODING_UTF8 = 'UTF-8';
    protected const HEADER_HTML = 'header-html';
    protected const FOOTER_HTML = 'footer-html';
    protected const MARGIN_TOP = 'margin-top';
    protected const MARGIN_BOTTOM = 'margin-bottom';
}
