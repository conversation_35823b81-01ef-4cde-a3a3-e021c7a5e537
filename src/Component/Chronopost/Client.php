<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Chronopost;

use Wizacha\Component\Chronopost\Exceptions\MalformedAddress;
use Wizacha\Component\Chronopost\Exceptions\ParcelAlreadyShipped;
use Wizacha\Component\Chronopost\Exceptions\SystemError;

class Client
{
    /**
     * @var string
     */
    protected $accountNumber;

    /**
     * @var string
     */
    protected $password;

    /**
     * @var string
     */
    protected $subAccount;


    public function __construct(string $accountNumber, string $password)
    {
        $this->accountNumber = $accountNumber;
        $this->password = $password;
        $this->subAccount = '000';
    }

    public function setSubAccount(string $subAccount): self
    {
        $this->subAccount = $subAccount;

        return $this;
    }

    /**
     * Récupération de la liste des points relais disponibles en fonction des paramètres.
     *
     * @return \stdClass[] Liste des points relais (cf. getPointRelais pour les détails)
     */
    public function listChronoPointsRelais($address, $zipCode, $city, $countryCode, $weightKg): array
    {
        // Création du client SOAP
        $soapClient = new \SoapClient('https://ws.chronopost.fr/recherchebt-ws-cxf/PointRelaisServiceWS?wsdl');

        // Génération des données
        $soapData = [
            'accountNumber' => $this->accountNumber,
            'password' => $this->password,
            'address' => $address,
            'zipCode' => $zipCode,
            'city' => $city,
            'countryCode' => $countryCode,
            'type' => 'P',
            'service' => 'L', // Livraison en point Chronopost
            'weight' => $weightKg * 1000, // En gramme : [0-9](0,5)
            'shippingDate' => date('d/m/Y'),
            'maxPointChronopost' => 10, // max : 25
            'maxDistanceSearch' => 20, // max : 40
            'holidayTolerant' => 1,
            'language' => 'FR',
        ];

        // Appel à l'API Chronopost pour récupérer les points relais
        $soapResponse = $soapClient->recherchePointChronopost($soapData);

        // Vérification des erreurs
        if ($soapResponse->return->errorCode != 0) {
            throw new SystemError($soapResponse->return->errorMessage);
        }

        return $soapResponse->return->listePointRelais ?? [];
    }

    /**
     * Récupération des détails d'un point relais.
     *
     * @return \stdClass Détails d'un point relais
     *
     * - "accesPersonneMobiliteReduite" => true
     * - "actif" => true
     * - "adresse1" => "32 RUE DE LA CLAIRE"
     * - "adresse2" => ""
     * - "adresse3" => ""
     * - "codePays" => "FR"
     * - "codePostal" => "69009"
     * - "coordGeolocalisationLatitude" => "45.**********"
     * - "coordGeolocalisationLongitude" => "4.************"
     * - "distanceEnMetre" => 448
     * - "identifiant" => "3883R"
     * - "indiceDeLocalisation" => ""
     * - "listeHoraireOuverture" => array:7
     *  0 => [
     *      "horairesAsString": "09:00-12:00 12:00-22:00"
     *      "jour": 7
     *      "listeHoraireOuverture": array:2
     *      0 => [
     *          debut": "09:00"
     *          fin": "12:00"
     *      ]
     *      1 => [
     *          debut": "12:00"
     *          fin": "22:00"
     *      ]
     *  ]...
     * - "localite" => "LYON"
     * - "nom" => "SABORES DE PORTUGAL"
     * - "poidsMaxi" => 20
     * - "typeDePoint" => "P"
     * - "urlGoogleMaps" => "http://maps.google.fr/maps?q=45.**********,4.************"
     */
    public function getPointRelais(string $id, string $country = 'FR'): \stdClass
    {
        // Création du client SOAP
        $soapClient = new \SoapClient('https://ws.chronopost.fr/recherchebt-ws-cxf/PointRelaisServiceWS?wsdl');

        // Génération des données
        $soapData = [
            'accountNumber' => $this->accountNumber,
            'password' => $this->password,
            'identifiant' => $id,
            'countryCode' => $country,
            'language' => 'FR',
        ];

        // Appel à l'API Chronopost pour récupérer les points relais
        $soapResponse = $soapClient->rechercheDetailPointChronopostInter($soapData);

        // Vérification des erreurs
        if ($soapResponse->return->errorCode != 0) {
            throw new SystemError($soapResponse->return->errorMessage);
        }

        return $soapResponse->return->listePointRelais;
    }

    /**
     * Création d'un shipment dans le SI de Chronopost
     *
     * @return array
     * - le numéro de tracking
     * - le numéro skybill
     * */
    public function createShipment(array $shipper, array $recipient, array $customer, $orderId, $invoiceNumber, $weightKg, $productCode): array
    {
        // Création du client SOAP
        $soapClient = new \SoapClient('https://ws.chronopost.fr/shipping-cxf/ShippingServiceWS?wsdl');

        // Génération des données
        $soapData = [
            'headerValue' => [
                'accountNumber' => $this->accountNumber,
                'subAccount' => $this->subAccount,
            ],
            'password' => $this->password,
            'shipperValue' => $shipper + ['shipperPreAlert' => 0],
            'recipientValue' => $recipient + ['recipientPreAlert' => 0],
            'customerValue' => $customer + ['customerPreAlert' => 0],
            'refValue' => [
                'shipperRef' => $orderId,
                'recipientRef' => $invoiceNumber,
            ],
            'skybillValue' => [
                'productCode' => $productCode,
                'shipDate' => date('d/m/Y H:i:s'),
                'shipHour' => date('H'),
                'weight' => $weightKg,
                'service' => 0, // Jour de livraison : 0 = normal
                'objectType' => 'MAR',
                'evtCode' => 'DC',
            ],
            'skybillParamsValue' => [
                'mode' => 'PDF',
            ],
            'modeRetour' => 2,
        ];

        // Appel à l'API Chronopost de création de shipment
        $soapResponse = $soapClient->shippingWithReservationAndESDWithRefClient($soapData);

        // Si une addresse est mal-formatée
        if ($soapResponse->return->errorCode == 38) {
            throw new MalformedAddress($soapResponse->return->errorMessage . ' : ' . implode(',', $soapData));
        }

        // Autres erreurs
        if ($soapResponse->return->errorCode != 0) {
            throw new SystemError($soapResponse->return->errorMessage);
        }

        return [
            $soapResponse->return->errorCode == 0 ? $soapResponse->return->reservationNumber : '',
            $soapResponse->return->errorCode == 0 ? $soapResponse->return->skybillNumber : '',
        ];
    }

    /**
     * Annulation d'un shipment dans le SI de Chronopost
     * */
    public function deleteShipment(string $skybillNumber)
    {
        // Création du client SOAP
        $soapClient = new \SoapClient('https://ws.chronopost.fr/tracking-cxf/TrackingServiceWS?wsdl');

        // Génération des données
        $soapData = [
            'accountNumber' => $this->accountNumber,
            'password' => $this->password,
            'language' => 'fr_FR',
            'skybillNumber' => $skybillNumber,
        ];

        // Appel à l'API Chronopost d'annulation de shipment
        $soapResponse = $soapClient->cancelSkybill($soapData);

        // Le colis est déjà pris en charge par Chronopost
        if ($soapResponse->return->errorCode == 3) {
            throw new ParcelAlreadyShipped($soapResponse->return->errorMessage);
        }

        // Autres erreurs
        if ($soapResponse->return->errorCode != 0) {
            throw new SystemError($soapResponse->return->errorMessage);
        }
    }

    public function getShippingAddress(array $shippinginfo, string $country = 'FR'): array
    {
        $pickupPoint = $this->getPointRelais($shippinginfo['pickupPointId'], $country);

        return [
            's_title' => $shippinginfo['title'],
            's_firstname' => $shippinginfo['firstName'],
            's_lastname' => $shippinginfo['lastName'],
            's_company' => $pickupPoint->nom,
            's_address' => $pickupPoint->adresse1,
            's_address_2' => $pickupPoint->adresse2,
            's_zipcode' => $pickupPoint->codePostal,
            's_city' => $pickupPoint->localite,
            's_county' => $pickupPoint->codePays,
            's_pickup_point_id' => $shippinginfo['pickupPointId']
        ];
    }
}
