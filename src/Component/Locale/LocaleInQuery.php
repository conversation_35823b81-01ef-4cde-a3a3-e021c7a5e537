<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Locale;

use Wizacha\Component\Locale\Exception\LocaleNotFound;
use Symfony\Component\HttpFoundation\Request;

class LocaleInQuery implements LocaleDetectorInterface
{
    private $parameter;

    public function __construct(string $parameter)
    {
        $this->parameter = $parameter;
    }

    /**
     * {@inheritdoc}
     */
    public function detect(Request $request): Locale
    {
        $locale = $request->query->get($this->parameter);

        if (!\is_string($locale)) {
            throw new LocaleNotFound();
        }

        return new Locale($locale);
    }
}
