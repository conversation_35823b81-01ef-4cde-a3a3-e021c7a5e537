<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Locale;

use Negotiation\AcceptHeader;
use Negotiation\LanguageNegotiator;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\ConflictHttpException;
use Wizacha\Component\Locale\Exception\LocaleNotAllowed;
use Wizacha\Component\Locale\Exception\LocaleNotFound;

class LocaleInHeader implements LocaleDetectorInterface
{
    private $locales;
    private $negotiator;

    public function __construct(array $locales)
    {
        $this->locales = $locales;
        $this->negotiator = new LanguageNegotiator();
    }

    /**
     * {@inheritdoc}
     */
    public function detect(Request $request): Locale
    {
        $header = 'Accept-Language';
        $counterPart = 'Content-Language';

        if (\in_array($request->getMethod(), [Request::METHOD_POST, Request::METHOD_PUT], true)) {
            $header = 'Content-Language';
            $counterPart = 'Accept-Language';
        }

        if (!$request->headers->has($header)
            || empty($request->headers->get($header))
        ) {
            throw new LocaleNotFound();
        }

        $bestLanguage = $this->negotiator->getBest($request->headers->get($header), $this->locales);

        if (!$bestLanguage instanceof AcceptHeader) {
            throw new LocaleNotAllowed(new Locale(
                $request->headers->get($header)
            ));
        }

        $this->checkConflict($request, $bestLanguage, $counterPart);

        return new Locale($bestLanguage->getValue());
    }

    private function checkConflict(Request $request, AcceptHeader $language, string $counterPart): void
    {
        if ($request->isMethodSafe()) {
            return;
        }

        if (!$request->headers->has($counterPart)) {
            return;
        }

        if (empty($request->headers->get($counterPart))) {
            return;
        }

        $counterPartLanguage = $this->negotiator->getBest(
            $request->headers->get($counterPart),
            $this->locales
        );

        if (!$counterPartLanguage instanceof AcceptHeader) {
            throw new LocaleNotAllowed(new Locale(
                $request->headers->get($counterPart)
            ));
        }

        if ($counterPartLanguage->getValue() !== $language->getValue()) {
            throw new ConflictHttpException(
                'Independent languages between request and response content not supported'
            );
        }
    }
}
