<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Locale;

use Wizacha\Component\Locale\Exception\LocaleNotFound;
use Symfony\Component\HttpFoundation\Request;

class LocaleDetectorDelegate implements LocaleDetectorInterface
{
    /** @var LocaleDetectorInterface[] */
    private $delegate = [];

    public function __construct(LocaleDetectorInterface ...$delegate)
    {
        $this->delegate = $delegate;
    }

    /**
     * {@inheritdoc}
     */
    public function detect(Request $request): Locale
    {
        $locale = null;

        foreach ($this->delegate as $delegate) {
            try {
                return $delegate->detect($request);
            } catch (LocaleNotFound $e) {
                //try next detector
            }
        }

        throw new LocaleNotFound();
    }
}
