<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Locale;

use Wizacha\Component\Locale\Exception\DomainException;

class Locale
{
    /**
     * @var string
     */
    private $language;

    /**
     * @var string
     */
    private $region;

    /**
     * The value can be any value accepted in an Accept-Language http header
     *
     * Examples:
     * "fr" => "fr"
     * "fr_FR" => "fr_FR"
     * "fr-FR" => "fr_FR"
     *
     * Otherwise any other value will raise the exception
     *
     * @throws \DomainException
     */
    public function __construct(string $value)
    {
        if (!($value = \Locale::acceptFromHttp($value))) {
            throw new DomainException();
        }

        $sections = \Locale::parseLocale($value);
        $this->language = $sections['language'];
        $this->region = $sections['region'] ?? null;
    }

    /**
     * String as ISO 3166-1 alpha-2 format
     */
    public function getRegion(): ?string
    {
        return $this->region;
    }

    public function equals(self $locale): bool
    {
        return (string) $this === (string) $locale && $this->region === $locale->getRegion();
    }

    public function __toString(): string
    {
        return $this->language;
    }
}
