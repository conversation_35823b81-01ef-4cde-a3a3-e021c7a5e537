<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Locale;

use Wizacha\Component\Locale\Exception\LocaleNotFound;
use Symfony\Component\HttpFoundation\Request;

class LocaleInSession implements LocaleDetectorInterface
{
    private $parameter;

    public function __construct(string $parameter)
    {
        $this->parameter = $parameter;
    }

    /**
     * {@inheritdoc}
     */
    public function detect(Request $request): Locale
    {
        $locale = fn_get_session_data($this->parameter);

        if (!\is_string($locale)) {
            throw new LocaleNotFound();
        }

        return new Locale($locale);
    }
}
