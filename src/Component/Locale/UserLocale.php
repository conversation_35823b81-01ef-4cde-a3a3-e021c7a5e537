<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Locale;

use Symfony\Component\HttpFoundation\Request;
use Wizacha\Component\Locale\Exception\LocaleNotFound;

class UserLocale implements LocaleDetectorInterface
{
    public function detect(Request $request): Locale
    {
        if (isset($_SESSION['auth']) && !empty($_SESSION['auth']['locale'])) {
            return new Locale($_SESSION['auth']['locale']);
        }

        throw new LocaleNotFound();
    }
}
