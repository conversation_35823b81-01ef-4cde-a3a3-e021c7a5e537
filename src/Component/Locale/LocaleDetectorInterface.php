<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Locale;

use Wizacha\Component\Locale\Exception\LocaleNotFound;
use Symfony\Component\HttpFoundation\Request;

interface LocaleDetectorInterface
{
    /**
     * @throws LocaleNotFound
     */
    public function detect(Request $request): Locale;
}
