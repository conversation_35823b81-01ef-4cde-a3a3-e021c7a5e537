<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Locale\Exception;

use Wizacha\Component\Locale\Locale;

class LocaleNotAllowed extends \RuntimeException implements ExceptionInterface
{
    /**
     * @var Locale
     */
    private $locale;

    public function __construct(Locale $locale)
    {
        parent::__construct();

        $this->locale = $locale;
    }

    public function locale(): Locale
    {
        return $this->locale;
    }
}
