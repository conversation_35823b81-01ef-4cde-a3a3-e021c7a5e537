<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Locale;

use Wizacha\Component\Locale\Exception\LocaleNotAllowed;
use Symfony\Component\HttpFoundation\Request;

class AllowedLocalesDetector implements LocaleDetectorInterface
{
    private $try;
    private $allowedLocales;

    public function __construct(
        LocaleDetectorInterface $try,
        array $allowedLocales
    ) {
        $this->try = $try;
        $this->allowedLocales = $allowedLocales;
    }

    /**
     * {@inheritdoc}
     */
    public function detect(Request $request): Locale
    {
        $locale = $this->try->detect($request);

        if (\in_array((string) $locale, $this->allowedLocales, true)) {
            return $locale;
        }

        throw new LocaleNotAllowed($locale);
    }
}
