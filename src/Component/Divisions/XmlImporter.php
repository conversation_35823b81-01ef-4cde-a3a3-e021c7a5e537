<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Divisions;

use Doctrine\ORM\EntityManagerInterface;
use Wizacha\Marketplace\Division\Division;
use Wizacha\Marketplace\Division\DivisionDescription;
use Wizacha\Marketplace\Division\Service\DivisionService;

/**
 * Divisions Xml parser and importer
 */
class XmlImporter
{
    private const SUB_FOLDERS_PATH = 'migrations/Divisions';

    private const FILENAME = 'iso_country_codes.xml';

    // Alpha 2 node key
    private const ALPHA_2_CODE = 'alpha-2-code';

    // Subdivision node key
    private const SUBDIVISION = 'subdivision';

    /** @var string */
    private $filePath;

    /** @var DivisionService */
    private $service;

    /** @var array */
    private $descriptions;

    /** @var EntityManagerInterface */
    private $entityManager;

    /** @var string[] */
    private $localesToImport;

    /** @var string */
    private $fallbackLocale;

    public function __construct(EntityManagerInterface $entityManager, DivisionService $service, string $kernelDir)
    {
        $this->filePath = $kernelDir . '/' . static::SUB_FOLDERS_PATH . '/' . static::FILENAME;
        $this->service = $service;
        $this->descriptions = [];
        $this->entityManager = $entityManager;
    }

    /**
     * Locales used to create DivisionDescriptions
     * @param string[] $locales
     */
    public function setLocalesToImport(array $locales): self
    {
        $this->localesToImport = $locales;

        return $this;
    }

    /** Used to set description value if the wanted locale is not found */
    public function setFallbackLocale(string $fallbackLocale): self
    {
        $this->fallbackLocale = $fallbackLocale;

        return $this;
    }

    /** Process parse Xml, create Divisions and Descriptions entities */
    public function process(): self
    {
        if (false === file_exists($this->filePath)) {
            throw new FileNotFound("Division xml file doesn't exists [{$this->filePath}]");
        }

        $xml = simplexml_load_file($this->filePath);

        $rootDivision = new Division(DivisionService::ROOT_CODE);
        $rootDivision->setUpdatedBy(1);
        $rootDivision->setDescriptions(
            [
                new DivisionDescription($rootDivision, 'fr', 'Toutes les divisions'),
                new DivisionDescription($rootDivision, 'en', 'All divisions'),
            ]
        );
        $this->entityManager->persist($rootDivision);

        foreach ($xml->country as $countryNode) {
            if (false === $this->checkValidityDate($countryNode)) {
                continue;
            }

            if (false === $this->hasSubdivisions($countryNode)) {
                continue;
            }

            $division = new Division((string) $countryNode->{static::ALPHA_2_CODE});
            $division->setAlpha3((string) $countryNode->{'alpha-3-code'});
            $division->setParent($rootDivision);
            $division->setUpdatedBy(1);
            $this->entityManager->persist($division);

            $this->addCountryDescriptions($division, $countryNode);

            $this->processSubDivisions($countryNode->{static::SUBDIVISION}, $division);

            // Batch flush subdivisons on each country
            $this->entityManager->flush();
        }

        return $this;
    }

    /** Look for subdivisions */
    protected function processSubDivisions(\SimpleXMLElement $nodes, Division $parent): self
    {
        foreach ($nodes as $node) {
            $code = (string) $node->{'subdivision-code'};

            $division = new Division($code);
            $division->setParent($parent);
            $division->setUpdatedBy(1);
            $this->entityManager->persist($division);

            $this->addSubdivisionDescriptions($division, $node);

            $this->processSubDivisions($node->{static::SUBDIVISION}, $division);
        }

        return $this;
    }

    /**
     * Add the country descriptions to the divisions entity
     *
     * We try to imports locales in $this->localesToImport array
     * If a locale is not found in the node, we try set this description with the fallback locale
     */
    protected function addCountryDescriptions(Division $division, \SimpleXMLElement $countryNode): self
    {
        foreach ($this->localesToImport as $wantedLocale) {
            $name = null;

            $key = 0;
            foreach ($countryNode->{'language'} as $language) {
                $nodeLocale = (string) $language->{'language-alpha-2-code'};

                if ($nodeLocale === $wantedLocale) {
                    $name = (string) $countryNode->{'short-name-upper-case'}[$key];
                } elseif ($this->fallbackLocale  === $nodeLocale && \is_null($name)) {
                    $name = (string) $countryNode->{'short-name-upper-case'}[$key];
                }

                $key++;
            }

            $division->addDescription(
                new DivisionDescription($division, $wantedLocale, $this->convertName($name))
            );
        }

        return $this;
    }

    /**
     * Add the subdivision descriptions to the divisions entity
     *
     * We try to import locales in $this->localesToImport array
     * If a locale is not found in the node, we try set this description with the fallback locale
     * and if it's still empty, we set with the first name found in the node.
     */
    protected function addSubdivisionDescriptions(Division $division, \SimpleXMLElement $node): self
    {
        foreach ($this->localesToImport as $wantedLocale) {
            $name = null;

            if (isset($node->{'subdivision-related-country'})) {
                $name = (string) $node->{'subdivision-related-country'};
            } else {
                $name = null;
                foreach ($node->{'subdivision-locale'} as $subDivisionLocaleNode) {
                    $nodeLocale = (string) $subDivisionLocaleNode->attributes('xml', true)->lang;

                    if ($nodeLocale === $wantedLocale) {
                        $name = (string) $subDivisionLocaleNode->{'subdivision-locale-name'};
                    } elseif ($this->fallbackLocale === $nodeLocale && \is_null($name)) {
                        $name = (string) $subDivisionLocaleNode->{'subdivision-locale-name'};
                    } elseif (\is_null($name)) {
                        $name = (string) $subDivisionLocaleNode->{'subdivision-locale-name'};
                    }
                }
            }

            $division->addDescription(
                new DivisionDescription($division, $wantedLocale, $this->convertName($name))
            );
        }

        return $this;
    }

    /** Check if division end date is pasted */
    protected function checkValidityDate(\SimpleXMLElement $node): bool
    {
        if (false === \array_key_exists('validity-end-date', $node)) {
            return true;
        }

        return (new \DateTime((string) $node->{'validity-end-date'})) >= (new \DateTime());
    }

    protected function hasSubdivisions(\SimpleXMLElement $countryNode): bool
    {
        return \count($countryNode->{static::SUBDIVISION}) > 0;
    }

    /** Format Description */
    protected function convertName(?string $name): string
    {
        return mb_convert_case(
            mb_strtolower($name, 'UTF-8'),
            MB_CASE_TITLE,
            "UTF-8"
        );
    }
}
