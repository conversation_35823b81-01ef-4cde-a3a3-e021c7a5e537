<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Notification;

use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Symfony\Contracts\EventDispatcher\Event;

class TestNotificationRequested extends Event implements NotificationEvent
{

    public static function buildDebugForm(FormBuilder $form)
    {
        // TODO: Implement buildDebugForm() method.
    }

    public static function createFromForm(Form $form)
    {
        return new TestNotificationRequested();
    }

    public static function getDescription(): string
    {
        return 'test';
    }
}
