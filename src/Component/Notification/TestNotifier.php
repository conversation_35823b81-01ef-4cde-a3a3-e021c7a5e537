<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Notification;

use Wizacha\Bridge\Swiftmailer\Mailer;
use Wizacha\Marketplace\AdminCompany;

class TestNotifier
{
    /**
     * @var Mailer
     */
    private $mailer;

    /**
     * @var AdminCompany
     */
    private $adminCompany;

    public function __construct(Mailer $mailer, AdminCompany $adminCompany)
    {
        $this->mailer = $mailer;
        $this->adminCompany = $adminCompany;
    }

    public function testNotificationRequested(TestNotificationRequested $event)
    {
        $message = new \Swift_Message();
        $message
            ->setSubject('Mail test prod ' . THEME)
            ->setFrom($this->adminCompany->getUsersDepartmentEmail(), $this->adminCompany->getName())
            ->setTo('<EMAIL>')
            ->setBody('It works!', 'text/plain')
        ;
        $this->mailer->send($message);
    }
}
