<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Notification;

use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;

/**
 * Event that triggers a notification.
 *
 * Those events need to implement that interface because we provide a debug tool in the back-office -> you can
 * trigger/simulate those events to see which notifications would be sent.
 *
 * How it works:
 *
 * - buildDebugForm() will generate the form to show the admin (in the back-office)
 * - the admin will fill the form and submit it
 * - createFromForm() will be called and return a new event instance
 * - the event will be dispatched in "debug" mode and the resulting notifications will be displayed to the user
 */
interface NotificationEvent
{
    /**
     * Build the Symfony Form that will be shown in the backend to simulate the event.
     */
    public static function buildDebugForm(FormBuilder $form);

    /**
     * Create a instance of that class from the submitted form.
     *
     * @return static
     */
    public static function createFromForm(Form $form);

    /**
     * Returns the translation that describes the event.
     *
     * It will be used in the back-office to list all notifications.
     *
     * You must return a translation ID!
     */
    public static function getDescription(): string;
}
