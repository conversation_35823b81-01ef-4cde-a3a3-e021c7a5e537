<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Notification;

use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Contracts\EventDispatcher\Event;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Wizacha\AppBundle\Notification\NotificationConfig;
use Wizacha\AppBundle\Notification\NotificationConfigService;
use Wizacha\Discuss\Event\DiscussMessagePosted;
use Wizacha\Exim\AsyncExportHasFinished;
use Wizacha\Marketplace\Cms\Event\ContactFormSubmitted;
use Wizacha\Marketplace\Company\Event\C2cCompanyApplied;
use Wizacha\Marketplace\Company\Event\CompanyApplied;
use Wizacha\Marketplace\Company\Event\CompanyDisabled;
use Wizacha\Marketplace\Company\Event\CompanyPending;
use Wizacha\Marketplace\Company\Event\CompanyRejected;
use Wizacha\Marketplace\CompanyPerson\Event\UBOValidationFailed;
use Wizacha\Marketplace\Currency\Event\CurrencyRatesUpdateFailed;
use Wizacha\Marketplace\Order\AfterSales\Event\AfterSalesServiceRequested;
use Wizacha\Marketplace\Order\AfterSales\Event\LitigationCreated;
use Wizacha\Marketplace\Order\Event\OrderCodeFailed;
use Wizacha\Marketplace\Order\Event\OrderCodeGenerated;
use Wizacha\Marketplace\Order\Event\OrderStatusChanged;
use Wizacha\Marketplace\Order\Event\ShipmentCreated;
use Wizacha\Marketplace\Order\OrderReturn\Event\RmaDeclined;
use Wizacha\Marketplace\Order\OrderReturn\Event\RmaReceived;
use Wizacha\Marketplace\Order\OrderReturn\Event\RmaRequested;
use Wizacha\Marketplace\Order\Refund\Event\OrderRefundedEvent;
use Wizacha\Marketplace\Organisation\Event\OrganisationApproved;
use Wizacha\Marketplace\Organisation\Event\OrganisationDisapproved;
use Wizacha\Marketplace\Organisation\Event\OrganisationRegistered;
use Wizacha\Marketplace\Payment\Event\BankwireFailedToRetrieveOrderEvent;
use Wizacha\Marketplace\Payment\Event\BankwireNotificationFailedStatusEvent;
use Wizacha\Marketplace\Payment\Event\BankwirePaymentEvent;
use Wizacha\Marketplace\Payment\Event\DispatchFundsFailedEvent;
use Wizacha\Marketplace\Payment\Event\HipayTransactionChargedbackEvent;
use Wizacha\Marketplace\Payment\Event\InternalTransferErrorEvent;
use Wizacha\Marketplace\PIM\Moderation\ProductWasReported;
use Wizacha\Marketplace\PIM\Option\Event\OptionApproved;
use Wizacha\Marketplace\PIM\Option\Event\OptionRejected;
use Wizacha\Marketplace\PIM\Product\Event\ProductApproved;
use Wizacha\Marketplace\PIM\Product\Event\ProductChangesRequested;
use Wizacha\Marketplace\PIM\Product\Event\ProductRejected;
use Wizacha\Marketplace\PIM\Product\Event\ProductStockThresholdReached;
use Wizacha\Marketplace\Security\Event\IpBlockedEvent;
use Wizacha\Marketplace\Tax\Event\MissingTaxConfigurationEvent;
use Wizacha\Marketplace\User\Event\UserAskedToRecoverPassword;
use Wizacha\Marketplace\User\Event\UserProfileActivated;
use Wizacha\Marketplace\User\Event\UserProfileActivationRequested;
use Wizacha\Marketplace\User\Event\UserProfileBlocked;
use Wizacha\Marketplace\User\Event\UserProfileCreated;
use Wizacha\Marketplace\User\Event\UserProfileUpdated;

class NotificationDispatcher implements EventSubscriberInterface
{
    /**
     * @var ContainerInterface
     */
    private $container;

    private NotificationConfigService $notificationConfigService;

    public function __construct(
        ContainerInterface $container,
        NotificationConfigService $notificationConfigService
    ) {
        $this->container = $container;
        $this->notificationConfigService = $notificationConfigService;
    }

    public function dispatch(Event $event)
    {
        $notification = $this->getNotification(\get_class($event));

        if ($notification instanceof NotificationConfig
            && true === $notification->isEnabled()
        ) {
            $notifier = $this->container->get($notification->getNotifier());
            $method = lcfirst(substr(strrchr(\get_class($event), '\\'), 1));
            $notifier->$method($event);
        }
    }

    public function dispatchDebug(Event $event): array
    {
        $notification = $this->getNotification(\get_class($event));

        if ($notification instanceof NotificationConfig) {
            $notifier = $this->container->get($notification->getNotifier() . '.debug');
            $method = lcfirst(substr(strrchr(\get_class($event), '\\'), 1));
            $notifier->$method($event);

            // The message logger is get with the container rather than injected because this service is not available (??) at the first boot
            $messages = $this->container->get('swiftmailer.mailer.fake_mailer.plugin.messagelogger')->getMessages();

            return [
                'mail' => $messages,
                'enabled' => $notification->isEnabled(),
            ];
        }

        return [
            'enabled' => false,
        ];
    }

    public static function getSubscribedEvents()
    {
        $events = self::getNotificationList();
        // Returns a map of all events to 'dispatch' (which is the `$this->dispatch()` function)
        return array_combine($events, array_fill(0, \count($events), ['dispatch', 0]));
    }

    public static function getNotificationList(): array
    {
        return array_keys(self::getMapping());
    }

    public static function getMapping(): array
    {
        return [
            TestNotificationRequested::class => 'app.notification.test_notifier',
            ShipmentCreated::class => 'app.notification.order_notifier',
            UserAskedToRecoverPassword::class => 'app.notification.user_notifier',
            UserProfileActivated::class => 'app.notification.user_notifier',
            UserProfileCreated::class => 'app.notification.user_notifier',
            UserProfileUpdated::class => 'app.notification.user_notifier',
            UserProfileActivationRequested::class => 'app.notification.user_notifier',
            UserProfileBlocked::class => 'app.notification.user_notifier',
            OptionApproved::class => 'app.notification.option_notifier',
            OptionRejected::class => 'app.notification.option_notifier',
            OrderCodeGenerated::class => 'app.notification.order_notifier',
            OrderCodeFailed::class => 'app.notification.order_notifier',
            OrderRefundedEvent::class => 'app.notification.refund_notifier',
            OrderStatusChanged::class => 'app.notification.order_notifier',
            DispatchFundsFailedEvent::class => 'app.notification.order_notifier',
            MissingTaxConfigurationEvent::class => 'app.notification.order_notifier',
            ProductApproved::class => 'app.notification.product_notifier',
            ProductRejected::class => 'app.notification.product_notifier',
            ProductChangesRequested::class => 'app.notification.product_notifier',
            ProductStockThresholdReached::class => 'app.notification.product_notifier',
            CompanyApplied::class => 'app.notification.company_notifier',
            C2cCompanyApplied::class => 'app.notification.company_notifier',
            CompanyPending::class => 'app.notification.company_notifier',
            CompanyDisabled::class => 'app.notification.company_notifier',
            CompanyRejected::class => 'app.notification.company_notifier',
            ProductWasReported::class => 'app.notification.moderation_notifier',
            ContactFormSubmitted::class => 'app.notification.contact_notifier',
            DiscussMessagePosted::class => 'app.notification.discuss_notifier',
            RmaRequested::class => 'app.notification.rma_notifier',
            RmaReceived::class => 'app.notification.rma_notifier',
            RmaDeclined::class => 'app.notification.rma_notifier',
            LitigationCreated::class => 'app.notification.rma_notifier',
            AfterSalesServiceRequested::class => 'app.notification.rma_notifier',
            AsyncExportHasFinished::class => 'app.notification.export_notifier',
            OrganisationRegistered::class => 'app.notification.organisation_notifier',
            OrganisationApproved::class => 'app.notification.organisation_notifier',
            OrganisationDisapproved::class => 'app.notification.organisation_notifier',
            InternalTransferErrorEvent::class => 'app.notification.order_notifier',
            BankwireFailedToRetrieveOrderEvent::class => 'app.notification.order_notifier',
            BankwireNotificationFailedStatusEvent::class => 'app.notification.order_notifier',
            HipayTransactionChargedbackEvent::class => 'app.notification.order_notifier',
            CurrencyRatesUpdateFailed::class => 'app.notification.currency_notifier',
            BankwirePaymentEvent::class => 'app.notification.order_notifier',
            UBOValidationFailed::class => 'app.notification.company_person_notifier',
            IpBlockedEvent::class => 'app.notification.security_notifier',
        ];
    }

    public function getNotification(string $className): ?NotificationConfig
    {
        $notification = $this->notificationConfigService->findBy(['className' => $className]);

        if (1 === \count($notification)) {
            return $notification[0];
        }

        // If the event is not present in DB, but is present in the getMapping method,
        // consider it as correct and enabled
        if (\array_key_exists($className, self::getMapping())) {
            if (false === method_exists($className, 'getDescription')) {
                $description = $className;
                // If the getDescription method does not exist it should not block the notification sending
                // but a warning must be logged
                $this->container->get('logger')->warning("Method getDescription is missing in $className");
            } else {
                $description = $className::getDescription();
            }

            return new NotificationConfig(
                $className,
                $description,
                self::getMapping()[$className],
                true
            );
        }

        return null;
    }
}
