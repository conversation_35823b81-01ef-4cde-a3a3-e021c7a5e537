<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Component\Crypto;

use Exception;
use Psr\Log\LoggerInterface;

/**
 * Performs cryptography operations.
 */
class Crypto
{
    /**
     * @var LoggerInterface
     */
    private $logger;

    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    /**
     * Generate a signature for the given data.
     *
     * Receivers of the data can verify the signature of the data to ensure that the data is valid
     * and has not been modified.
     *
     * Example:
     *
     *     $signature = $crypto->sign($data, '/foo/bar/private_key.pem');
     *
     * @param string $data Data to sign.
     * @param string $privateKeyFile Path of the file containing the private key.
     *
     * @return string Signature.
     */
    public function sign($data, $privateKeyFile): string
    {
        $privateKey = openssl_pkey_get_private('file://' . $privateKeyFile);
        if (!$privateKey) {
            throw new Exception(sprintf('Unable to read private key %s: %s', $privateKeyFile, openssl_error_string()));
        }

        $success = openssl_sign($data, $signature, $privateKey);

        openssl_free_key($privateKey);

        if (!$success) {
            throw new Exception('Error while signing data: ' . openssl_error_string());
        }

        return $signature;
    }

    /**
     * Check the signature of some data.
     *
     * It allows use to verify that the data is valid, comes from the correct 3rd party and wasn't altered.
     *
     * Example:
     *
     *     $signature = $crypto->verifySignature($data, $signature, '/foo/bar/public_key');
     *
     * @param string $data Data that was signed.
     * @param string $signature Signature of the data.
     * @param string $publicKeyFile Path of the file containing the public key.
     *
     * @return bool True if the data is valid, false otherwise.
     */
    public function verifySignature($data, $signature, $publicKeyFile): bool
    {
        $publicKey = openssl_pkey_get_public('file://' . $publicKeyFile);
        if (!$publicKey) {
            throw new Exception(sprintf('Unable to read public key %s: %s', $publicKeyFile, openssl_error_string()));
        }

        $result = openssl_verify($data, $signature, $publicKey);

        openssl_free_key($publicKey);

        if ($result === 1) {
            return true;
        }
        if ($result === 0) {
            return false;
        }

        $this->logger->error('Error while verifying a signature: {error}', [
            'error' => openssl_error_string(),
        ]);

        return false;
    }

    /**
     * Encrypt data so that it can only be read by someone with the private key.
     *
     * Example:
     *
     *     $encryptedData = $crypto->encrypt($data, '/foo/bar/public_key');
     *
     * @param string $data Data to encrypt.
     * @param string $publicKey Public key, or path to the file containing it.
     *
     * @return string Encrypted data.
     */
    public function encrypt($data, $publicKey): string
    {
        if (file_exists($publicKey)) {
            $publicKey = file_get_contents($publicKey);
        }
        $result = openssl_public_encrypt($data, $encryptedData, $publicKey);

        if (!$result) {
            throw new Exception('Error while encrypting data: ' . openssl_error_string());
        }

        return $encryptedData;
    }

    /**
     * Decrypt data that was encrypted with a private key.
     *
     * The data must have been encrypted with a public key matching the private key.
     *
     * Example:
     *
     *     $decryptedData = $crypto->decrypt($data, '/foo/bar/private_key.pem');
     *
     * @param string $data Encrypted data.
     * @param string $privateKey Private key, or path to the file containing it.
     *
     * @return string Decrypted data.
     */
    public function decrypt($data, $privateKey): string
    {
        if (file_exists($privateKey)) {
            $privateKey = file_get_contents($privateKey);
        }
        $result = openssl_private_decrypt($data, $decryptedData, $privateKey);

        if (!$result) {
            throw new Exception('Error while decrypting data: ' . openssl_error_string());
        }

        return $decryptedData;
    }
}
