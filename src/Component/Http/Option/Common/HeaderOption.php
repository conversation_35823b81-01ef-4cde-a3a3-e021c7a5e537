<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Http\Option\Common;

use Wizacha\Component\Http\Authorization\AuthorizationInterface;
use Wizacha\Component\Http\Option\OptionInterface;

class HeaderOption implements OptionInterface
{
    private AuthorizationInterface $authorization;

    public function __construct(AuthorizationInterface $authorization)
    {
        $this->authorization = $authorization;
    }

    public function getValue(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Authorization' => $this->authorization->getValue(),
        ];
    }

    public function getKey(): string
    {
        return OptionInterface::HEADER_OPTION_KEY;
    }
}
