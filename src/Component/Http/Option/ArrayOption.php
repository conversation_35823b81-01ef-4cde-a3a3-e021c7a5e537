<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Http\Option;

class ArrayOption implements OptionInterface
{
    private string $key;

    private array $value;

    public function __construct(string $key, array $value)
    {
        $this->value = $value;
        $this->key = $key;
    }

    public function getValue(): array
    {
        return $this->value;
    }

    public function getKey(): string
    {
        return $this->key;
    }
}
