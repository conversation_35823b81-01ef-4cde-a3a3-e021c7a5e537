<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Http;

use Exception;
use GuzzleHttp\Exception\RequestException;

class YavinClientException extends Exception
{
    private string $requestMethod;

    private string $requestUri;

    public function __construct(
        string $message,
        RequestException $previous
    ) {
        parent::__construct($message, 0, $previous);
    }

    public function getRequestMethod(): string
    {
        return $this->requestMethod;
    }

    public function setRequestMethod(string $requestMethod): self
    {
        $this->requestMethod = $requestMethod;

        return $this;
    }

    public function getRequestUri(): string
    {
        return $this->requestUri;
    }

    public function setRequestUri(string $requestUri): self
    {
        $this->requestUri = $requestUri;

        return $this;
    }
}
