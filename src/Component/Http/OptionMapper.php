<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Http;

use Wizacha\Component\Http\Option\OptionInterface;

class OptionMapper
{
    /**
     * @param OptionInterface[] $options
     *
     * @return string[]
     */
    public function toArray(array $options): array
    {
        $mappedAsArray = [];

        foreach ($options as $option) {
            $mappedAsArray[$option->getKey()] = $option->getValue();
        }

        return $mappedAsArray;
    }
}
