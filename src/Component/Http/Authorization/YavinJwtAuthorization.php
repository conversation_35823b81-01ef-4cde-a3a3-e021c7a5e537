<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Http\Authorization;

/** This class is used only in dev. It allow you to give a JWT to display Webhooks in the back office. */
class YavinJwtAuthorization implements AuthorizationInterface
{
    private string $yavinApiJwt;

    public function __construct(string $yavinApiJwt)
    {
        $this->yavinApiJwt = $yavinApiJwt;
    }

    public function getValue(): string
    {
        return 'Bearer ' . $this->yavinApiJwt;
    }
}
