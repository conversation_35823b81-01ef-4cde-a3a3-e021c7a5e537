<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Http\Authorization;

use Wizacha\Marketplace\User\User;

class UserApiKeyAuthorization implements AuthorizationInterface
{
    private User $user;

    public function __construct(User $user)
    {
        $this->user = $user;
    }

    public function getValue(): string
    {
        return "token " . $this->user->getApiKey();
    }
}
