<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Http\Response;

use Psr\Http\Message\ResponseInterface as PsrResponseInterface;
use Psr\Http\Message\StreamInterface;

class YavinResponse implements ResponseInterface
{
    private PsrResponseInterface $wrappedPsrResponse;

    public function __construct(PsrResponseInterface $wrappedPsrResponse)
    {
        $this->wrappedPsrResponse = $wrappedPsrResponse;
    }

    public function getStatusCode(): int
    {
        return $this->wrappedPsrResponse->getStatusCode();
    }

    public function getBody(): StreamInterface
    {
        return $this->wrappedPsrResponse->getBody();
    }

    /**
     * @return string[][]
     */
    public function getHeaders(): array
    {
        return $this->wrappedPsrResponse->getHeaders();
    }
}
