<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Http\Response;

use Psr\Http\Message\StreamInterface;

interface ResponseInterface
{
    public function getStatusCode(): int;

    public function getBody(): StreamInterface;

    /**
     * @return string[][]
     */
    public function getHeaders(): array;
}
