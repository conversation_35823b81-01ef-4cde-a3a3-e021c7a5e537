<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Http\Request;

use Wizacha\Component\Http\Option\OptionInterface;

interface RequestInterface
{
    public function getMethod(): string;

    public function getUri(): string;

    /**
     * @return OptionInterface[]
     */
    public function getOptions(): array;
}
