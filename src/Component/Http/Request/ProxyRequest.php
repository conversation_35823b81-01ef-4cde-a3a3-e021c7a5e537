<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Http\Request;

use Symfony\Component\HttpFoundation\Request;
use Wizacha\Component\Http\Option\OptionInterface;

class ProxyRequest implements RequestInterface
{
    private Request $httpRequest;

    private string $apiUri;

    private array $options = [];

    /**
     * ProxyRequest constructor.
     *
     * @param string $apiUri
     * @param Request $httpRequest
     */
    public function __construct(
        string $apiUri,
        Request $httpRequest
    ) {
        $this->apiUri = $apiUri;
        $this->httpRequest = $httpRequest;
    }

    public function getMethod(): string
    {
        return $this->httpRequest->getMethod();
    }

    public function getUri(): string
    {
        return '/' . $this->apiUri;
    }

    public function getOptions(): array
    {
        return $this->options;
    }

    /**
     * @param OptionInterface[] $options
     */
    public function setOptions(array $options)
    {
        $this->options = $options;
    }
}
