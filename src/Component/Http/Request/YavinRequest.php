<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Http\Request;

use Wizacha\Component\Http\Option\Common\HeaderOption;
use Wizacha\Component\Http\Option\OptionInterface;

class YavinRequest implements RequestInterface
{
    private RequestInterface $decoratedRequest;

    private string $baseUri;

    /**
     * @var OptionInterface[]
     */
    private array $options = [];

    /**
     * YavinRequest constructor.
     *
     * @param RequestInterface $decoratedRequest
     * @param string $baseUri
     * @param HeaderOption $headerOption
     */
    public function __construct(
        RequestInterface $decoratedRequest,
        string $baseUri,
        HeaderOption $headerOption
    ) {
        $this->decoratedRequest = $decoratedRequest;
        $this->baseUri = $baseUri;

        // we add the header option on init
        $this->addOption($headerOption);
        // we add the decorated request options
        foreach ($this->decoratedRequest->getOptions() as $currentOption) {
            $this->addOption($currentOption);
        }
    }

    public function getMethod(): string
    {
        return $this->decoratedRequest->getMethod();
    }

    public function getUri(): string
    {
        return $this->baseUri . $this->decoratedRequest->getUri();
    }

    public function addOption(OptionInterface $option): self
    {
        // we add option only if not already added
        if (false === $this->optionExist($option)) {
            array_push($this->options, $option);
        }

        return $this;
    }

    public function overrideOption(
        OptionInterface $existingOption,
        OptionInterface $newOption
    ): self {
        $index = array_search($existingOption, $this->options, true);

        if (false !== $index) {
            $this->options[$index] = $newOption;
        }

        return $this;
    }

    /**
     * @return OptionInterface[]
     */
    public function getOptions(): array
    {
        return $this->options;
    }

    private function optionExist(OptionInterface $option): bool
    {
        foreach ($this->options as $currentOption) {
            if ($option->getKey() === $currentOption->getKey()) {
                return true;
            }
        }

        return false;
    }
}
