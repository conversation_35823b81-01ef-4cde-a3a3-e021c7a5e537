<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Http\Factory;

use Symfony\Component\HttpFoundation\Request;
use Wizacha\Component\Http\Option\StringOption;
use Wizacha\Component\Http\Request\ProxyRequest;
use Wizacha\Component\Http\Request\RequestInterface;

class ProxyRequestFactory
{
    public function create(string $apiUri, Request $request): RequestInterface
    {
        $options = [];

        $content = $request->getContent();
        $query = $request->getQueryString();

        if (\is_string($content)) {
            array_push($options, new StringOption('body', $content));
        }

        if (\is_string($query)) {
            array_push($options, new StringOption('query', $query));
        }

        $proxyRequest = new ProxyRequest(
            $apiUri,
            $request,
        );

        $proxyRequest->setOptions($options);

        return $proxyRequest;
    }
}
