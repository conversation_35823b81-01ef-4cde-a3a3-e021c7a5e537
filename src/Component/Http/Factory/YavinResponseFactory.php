<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Http\Factory;

use Psr\Http\Message\ResponseInterface as PsrResponseInterface;
use Wizacha\Component\Http\Response\ResponseInterface;
use Wizacha\Component\Http\Response\YavinResponse;

class YavinResponseFactory
{
    public function create(PsrResponseInterface $response): ResponseInterface
    {
        return new YavinResponse($response);
    }
}
