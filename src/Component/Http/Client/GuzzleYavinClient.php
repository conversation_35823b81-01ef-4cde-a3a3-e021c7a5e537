<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Http\Client;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Wizacha\Component\Http\OptionMapper;
use Wizacha\Component\Http\Request\RequestInterface;
use Wizacha\Component\Http\Response\ResponseInterface;
use Wizacha\Component\Http\YavinClientException;
use Wizacha\Component\Http\Factory\YavinResponseFactory;

class GuzzleYavinClient implements YavinClientInterface
{
    public const EXCEPTION_MESSAGE = 'Yavin Http client occurred an error when trying to send a Request.';

    private Client $guzzleClient;

    private YavinResponseFactory $yavinResponseFactory;

    private OptionMapper $optionMapper;

    /**
     * GuzzleYavinClient constructor.
     *
     * @param Client $guzzleClient
     * @param YavinResponseFactory $yavinResponseFactory
     * @param OptionMapper $optionMapper
     */
    public function __construct(
        Client $guzzleClient,
        YavinResponseFactory $yavinResponseFactory,
        OptionMapper $optionMapper
    ) {
        $this->optionMapper = $optionMapper;
        $this->yavinResponseFactory = $yavinResponseFactory;
        $this->guzzleClient = $guzzleClient;
    }

    public function send(RequestInterface $yavinRequest): ResponseInterface
    {
        $method = $yavinRequest->getMethod();
        $uri = $yavinRequest->getUri();

        $mappedOptionsToArray = $this->optionMapper
            ->toArray($yavinRequest->getOptions());

        try {
            $currentResponse = $this->guzzleClient->request(
                $method,
                $uri,
                $mappedOptionsToArray
            );

            return $this->yavinResponseFactory->create($currentResponse);
        } catch (RequestException $requestException) {
            throw (new YavinClientException(self::EXCEPTION_MESSAGE, $requestException))
                ->setRequestMethod($method)
                ->setRequestUri($uri);
        }
    }
}
