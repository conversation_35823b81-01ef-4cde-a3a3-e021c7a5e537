<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\Pagination;

class Pagination implements PaginationInterface
{
    /** @var array  */
    protected $results;

    /** @var int  */
    protected $total;

    /** @var int  */
    protected $offset;

    /** @var int  */
    protected $limit;

    public function __construct(array $results, int $total, ?int $offset, ?int $limit)
    {
        $this->results = $results;
        $this->total = $total;
        $this->offset = $offset;
        $this->limit = $limit;
    }

    public function getResults(): array
    {
        return $this->results;
    }

    public function getTotal(): int
    {
        return $this->total;
    }

    public function getOffset(): int
    {
        return $this->offset;
    }

    public function getLimit(): int
    {
        return $this->limit;
    }

    public function expose(): array
    {
        return [
            "limit" => $this->getLimit(),
            "offset" => $this->getOffset(),
            "total" => $this->getTotal(),
            "items" => $this->getResults(),
        ];
    }
}
