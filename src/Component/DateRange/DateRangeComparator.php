<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Component\DateRange;

/**
 * This class compare 2 Ranges
 */
class DateRangeComparator
{
    public static function isOverlap(DateRange $rangeA, DateRange $rangeB): bool
    {
        return $rangeA->getStart() < $rangeB->getEnd() && $rangeA->getEnd() > $rangeB->getStart();
    }

    public static function contains(DateRange $dateRange, \DateTime $dateTime): bool
    {
        return $dateRange->getStart() <= $dateTime && $dateRange->getEnd() >= $dateTime;
    }
}
