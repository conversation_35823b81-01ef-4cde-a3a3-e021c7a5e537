<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha;

use Wizacha\Bridge\CsCart\MemoryCache;

/**
 * @deprecated Use Symfony's container.
 */
class Registry
{
    /**
     * @var null|\Wizacha\Registry
     */
    private static $default_instance = null;
    /**
     * @var array
     */
    private $local_cache = [];

    /**
     * @var null|\Tygh\Backend\Cache\ABackend
     */
    private $cache = null; //

    /**
     * Timeout for $cache->set();
     * @var integer
     */
    private $default_timeout = 0;

    /**
     * Link between short key and key used in $cache
     * @var array
     */
    private $alias_keys = [];

    /**
     * @var \Symfony\Component\DependencyInjection\Container
     */
    public $container;

    /**
     * @param \Tygh\Backend\Cache\ABackend $cache
     * @param integer $timeout
     */
    public function __construct(\Tygh\Backend\Cache\ABackend $cache = null, $timeout = \Wizacha\Config::CACHE_TIMEOUT)
    {
        $this->default_timeout = $timeout;
        $this->cache = $cache ?: new MemoryCache();
    }

    /**
     * @return Registry
     */
    public static function defaultInstance()
    {
        if (!self::$default_instance) {
            self::$default_instance = new Registry();
        }

        return self::$default_instance;
    }

    /**
     * @param \Tygh\Backend\Cache\ABackend $cache
     */
    public function setCache(\Tygh\Backend\Cache\ABackend $cache)
    {
        $this->cache = $cache;
    }

    /**
     * Clean local cache
     */
    public function cleanLocal()
    {
        $this->alias_keys = [];
        $this->local_cache = [];
    }

    /**
     * @return string[]
     */
    public function getLocalKeys(): array
    {
        return array_keys($this->local_cache);
    }

    /**
     * @return string[]
     */
    public function getAliasKeys(): array
    {
        return array_keys($this->alias_keys);
    }

    /**
     * @param string $key
     * @return string
     */
    public function getPermanentCacheKey(string $key): string
    {
        return $this->alias_keys[$key]['id'];
    }

    /**
     * @param string $key
     * @param array $handlers (list of handlers)
     * @param integer $timeout
     */
    public function registerPermanentCache($key, $handlers = [], $timeout = 0)
    {
        sort($handlers);
        $handlers = array_map(
            [$this->cache, 'getHandlerId'],
            array_unique($handlers)
        );

        //Timeout shouldn't be unlimited
        $this->alias_keys[$key] = [
            'timeout' => \intval($timeout) ?: $this->default_timeout,
            'id' => $key . '_' . implode('_', $handlers),
        ];

        $this->local_cache[$key] = $this->cache->get($this->alias_keys[$key]['id'])[0];
    }

    /**
     * @param array $keys
     * @param mixed $value
     */
    public function set(array $keys, $value)
    {
        if (empty($keys)) {
            return false;
        }

        $current = &$this->_getRef($keys, true);
        $current = $value;

        $this->savePermanent(reset($keys));
        return $value;
    }

    /**
     * @param array $keys
     * @return mixed
     */
    public function get(array $keys)
    {
        return $this->_getRef($keys);
    }

    /**
     * @param array $keys
     * @return boolean True if final key exists.
     */
    public function del(array $keys)
    {
        if (empty($keys)) {
            return false;
        }
        $first_key = reset($keys);
        $last_key = array_pop($keys);
        $current = & $this->_getRef($keys);

        if (!\is_array($current) || !\array_key_exists($last_key, $current)) {
            return false;
        }

        unset($current[$last_key]);
        $this->savePermanent($first_key);
        return true;
    }

    /**
     * If final key doesn't exists : create it if $create_ref is true, else, return null;
     *
     * @param array $keys
     * @param bool $create_ref
     * @return null|mixed
     */
    private function & _getRef(array $keys, $create_ref = false)
    {
        $current = & $this->local_cache;

        foreach ($keys as $key) {
            if (!\is_array($current) || !\array_key_exists($key, $current)) {
                if ($create_ref) {
                    $current[$key] = [];
                } else {
                    $var = null;
                    return $var;
                }
            }
            $current = & $current[$key];
        }
        return $current;
    }
    /**
     * Try to save cache.
     * @param string $root_key
     */
    private function savePermanent($root_key)
    {
        if (\array_key_exists($root_key, $this->alias_keys)) {
            if (\is_null($this->local_cache[$root_key])) {
                $this->cache->clear([$this->alias_keys[$root_key]['id']]);
            } else {
                $this->cache->set(
                    $this->alias_keys[$root_key]['id'],
                    $this->local_cache[$root_key],
                    $this->alias_keys[$root_key]['timeout']
                );
            }
        }
    }

    /**
     * @param string $handler
     */
    public function invalidHandler($handler)
    {
        $this->cache->regenerateHandlerId($handler);
    }

    /**
     * Clear all local data related to 'real' cache
     */
    public function unregisterPermanentCache()
    {
        $keys = array_keys($this->alias_keys);
        foreach ($keys as $key) {
            unset($this->local_cache[$key]);
        }
        $this->alias_keys = [];
    }

    /**
     * @param array $keys
     * @return integer
     */
    public function cntIncr(array $keys)
    {
        return $this->cache->increment(implode(':', $keys));
    }

    /**
     * @param array $keys
     * @return integer
     */
    public function cntDecr(array $keys)
    {
        return $this->cache->decrement(implode(':', $keys));
    }

    /**
     * @param array $keys
     * @return integer|null
     */
    public function cntGet($keys)
    {
        $value = $this->cache->get(implode(':', $keys));
        if (!\is_array($value)) {
            return null;
        }
        return reset($value);
    }

    /**
     * @param Registry $registry
     */
    public static function setDefaultRegistry(Registry $registry)
    {
        self::$default_instance = $registry;
    }

    /**
     * @return \Tygh\Backend\Cache\ABackend
     */
    public function cache()
    {
        return $this->cache;
    }
}
