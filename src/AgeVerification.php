<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha;

use Tygh\Database;

/**
 * @deprecated Legacy class
 * TODO turn it into a service and move it to the basket module?
 */
class AgeVerification
{
    public const HUMAN_READABLE_DATE = 'd/m/Y H:i:s';
    public const DEFAULT_TIME = ' 00:00:01';
    public const TIMESTAMP_DATE = 'U';
    public const DEFAULT_AGE_VERIFICATION = '0';
    private const BIRTHDAY = 'birthday';

    /**
     * Format human date (d/m/Y) to timestamp
     *
     * @param string $date
     *
     * @return int timestamp
     */
    public static function formatHumanReadableToTimestamp($date)
    {
        if (is_numeric($date)) {
            return $date;
        }

        $timestamp = 0;
        $formatted_date = \DateTime::createFromFormat(self::HUMAN_READABLE_DATE, $date . self::DEFAULT_TIME, new \DateTimeZone('UTC'));

        if (0 === \DateTime::getLastErrors()['error_count']) {
            $timestamp = $formatted_date->getTimestamp();
        }

        return $timestamp;
    }

    /** Recursively update function tree to fill parent_age_limit */
    private static function updateParentData(int $categoryId, int $ageLimit): void
    {
        Database::query("UPDATE ?:categories SET parent_age_limit = ?i WHERE parent_id = ?i", $ageLimit, $categoryId);

        $childCategories = Database::getArray(
            "SELECT category_id, age_limit FROM ?:categories WHERE parent_id = ?i",
            $categoryId
        );

        foreach ($childCategories as $childCategory) {
            self::updateParentData(
                $childCategory['category_id'],
                max((int) $childCategory['age_limit'], $ageLimit)
            );
        }
    }

    /**
     * Update newly created category_data with correct (parent_)age_limit infos
     *
     * @param array $category_data
     */
    public static function onCategoryCreate(&$category_data)
    {
        if ($category_data['parent_id']) {
            $parent_category_data = Database::getRow("SELECT age_limit, parent_age_limit FROM ?:categories WHERE category_id = ?i", $category_data['parent_id']);

            $category_data['parent_age_limit'] = max($parent_category_data['age_limit'], $parent_category_data['parent_age_limit']);
        }
    }

    /**
     * Update category_data with correct (parent_)age_limit infos, and update subtree
     *
     * @param array $category_data
     * @param int $category_id
     */
    public static function onCategoryUpdate(&$category_data, $category_id)
    {
        $age_datas = Database::getRow("SELECT age_limit, parent_age_limit FROM ?:categories WHERE category_id = ?i", $category_id);

        $age_limit = !empty($category_data['age_limit']) ? $category_data['age_limit'] : self::DEFAULT_AGE_VERIFICATION;

        $age_limit = max($age_limit, $age_datas['parent_age_limit']);

        if ($age_datas['age_limit'] != $age_limit) {
            self::updateParentData($category_id, $age_limit);
        }
    }

    /**
     * Set info age_verification in $cart
     *
     * @param array $cart
     */
    public static function updateCart(&$cart)
    {
        foreach ($cart['products'] as &$product) {
            if (!isset($product['age_verification'])) {
                $product['age_verification'] = self::fromProduct($product['product_id']);
            }
        }

        $all_ages = array_column($cart['products'], 'age_verification');
        $all_ages[] = self::DEFAULT_AGE_VERIFICATION;
        $cart['age_verification'] = max($all_ages);
    }

    /**
     * Retrieve min age for buying product, taking into account each category
     *
     * @param int $product_id
     * @return int
     */
    public static function fromProduct($product_id)
    {
        if (!is_numeric($product_id)) {
            return self::DEFAULT_AGE_VERIFICATION;
        }

        return Database::getField(
            'SELECT GREATEST(MAX(?:categories.age_limit), MAX(?:categories.parent_age_limit)) AS age_limit
            FROM ?:categories
            LEFT JOIN ?:products_categories ON ?:categories.category_id = ?:products_categories.category_id
            WHERE ?:products_categories.product_id = ?i
            GROUP BY ?:products_categories.product_id',
            $product_id
        );
    }

    /**
     * @param array $user_data
     * @param int $age_limit
     * @return bool
     */
    public static function check(array $user_data, $age_limit): bool
    {
        if (!$age_limit) {
            return true;
        }

        if ($user_data[static::BIRTHDAY] === null
            || empty($user_data[static::BIRTHDAY]) === true
            || $user_data[static::BIRTHDAY] instanceof \DateTime === false
        ) {
            return false;
        }

        $age = $user_data[static::BIRTHDAY]->diff(new \DateTime(), true);
        return $age->y >= $age_limit;
    }
}
