<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Smarty;

use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Templating\EngineInterface;
use Twig\Environment;

class SmartyEngine implements EngineInterface
{

    /**
     * @var \Smarty
     */
    private $smarty;
    /**
     * @var Environment
     */
    private $twigEnvironment;

    public function __construct(\Smarty $smarty, Environment $twig)
    {
        $this->smarty = $smarty;
        $this->twigEnvironment = $twig;
    }

    public function renderResponse($view, array $parameters = [], Response $response = null)
    {
        $response = $response ?: new Response();
        $response->setContent($this->render($view, $parameters));

        return $response;
    }

    public function render($name, array $parameters = [])
    {
        $name = $this->getSmartyTemplateFilename((string) $name);

        foreach ($parameters as $parameter => $value) {
            $this->smarty->assign($parameter, $value, true);
        }

        return $this->smarty->fetch($name);
    }

    public function exists($name): ?bool
    {
        $name = $this->getSmartyTemplateFilename((string) $name);

        try {
            return $this->smarty->templateExists($name);
        } catch (\SmartyException $e) {
            return false;
        }
    }

    public function supports($name): bool
    {
        // impossible to use FileNameParser here, because when @App/frontend/views/index/index.html.twig is searched
        // sometimes, there is no @App/frontend/views/index/index.html.twig but only @App/frontend/views/index/index.tpl
        // So, we then have to check if Twig can render it
        if ($this->twigEnvironment->getLoader()->exists($name)) {
            return false;
        }
        return $this->exists($name);
    }

    private function getSmartyTemplateFilename($twigFilename)
    {
        return str_replace(array('@App/frontend/', '.html.twig', '.twig'), array('', '.tpl', '.tpl'), $twigFilename);
    }
}
