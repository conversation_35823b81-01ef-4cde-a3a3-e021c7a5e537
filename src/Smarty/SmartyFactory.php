<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Smarty;

use Doctrine\Instantiator\Exception\UnexpectedValueException;
use Wizacha\AppBundle\Service\ThemeCustomizer;
use Wizacha\Marketplace\Smarty\Config;
use Wizacha\Registry;

/**
 * This is the factory for the new Smarty instance.
 *
 * This new instance replaces the legacy Smarty that is provided with CS Cart. It has a simpler
 * configuration and less magic.
 */
class SmartyFactory
{
    public static function create(Registry $registry, bool $development = null): \Smarty
    {
        $development ??= (bool) DEVELOPMENT;
        $smarty = new \Smarty();

        $cacheDirectory = $registry->get(['config', Config::CONF_SERVICE, Config::CONF_CACHE_DIRECTORY]);
        if ($cacheDirectory) {
            $smarty->setCacheDir($cacheDirectory);
        }

        $compileDirectory = $registry->get(['config', Config::CONF_SERVICE, Config::CONF_COMPILE_DIRECTORY]);
        if ($compileDirectory) {
            $smarty->setCompileDir($compileDirectory);
        }

        $smarty->setTemplateDir($registry->container->getParameter('smarty.template_directories'));

        $smarty->addPluginsDir($registry->container->getParameter('smarty.plugin_directories'));

        $smarty->assign('config', $registry->get(['config']));

        $smarty->debugging     = false;
        $smarty->compile_check = false;
        if ($development) { // rather than defined('DEVELOPMENT') && DEVELOPMENT to permit unit testing ( \Wizacha\Test\PHPUnit\Smarty\SmartyFactoryTest::testDevMode )
            $smarty->compile_check  = true;
            $smarty->debugging_ctrl = 'URL';
        }

        //Cache
        $smarty->cache_lifetime = -1;
        $smarty->cache_id = Config::cacheId(
            $registry->cache(),
            [
                $registry->cache()->getHandlerId(ThemeCustomizer::THEME_CUSTOMIZER_HANDLER),
                $registry->cache()->getHandlerId('language_values')
            ]
        );
        $smarty->setCaching(\Smarty::CACHING_LIFETIME_CURRENT);

        return $smarty;
    }
}
