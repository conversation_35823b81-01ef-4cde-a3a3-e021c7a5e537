<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Smarty;

use Tygh\Registry;
use Tygh\SmartyEngine\Core;
use Wizacha\Marketplace\Accounting\AccountingPayment;
use W<PERSON>cha\Marketplace\Cms\PageType;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Order\RmaStatus;
use Wizacha\Search\Config;
use Wizacha\Search\Engine\Algolia;
use Wizacha\Marketplace\Shipping\DeliveryType;

/**
 * This is the factory for the legacy Smarty instance used by CS Cart internally.
 *
 * @see SmartyFactory
 */
class LegacySmartyFactory
{
    public static function create($area): Core
    {
        $view = new Core();

        \SmartyException::$escape = false;

        self::registerClasses($view);
        $view->assign('discussClient', container()->get('app.discuss_service')->getDiscussClient());

        if ($area == 'A' && !empty($_SESSION['auth']['user_id'])) {
            // Auto-tooltips for admin panel
            $view->registerFilter('pre', array('Tygh\SmartyEngine\Filters', 'preFormTooltip'));
        }

        // Language variable retrieval optimization
        $view->registerFilter('post', array('Tygh\SmartyEngine\Filters', 'postTranslation'));

        if (Registry::get('settings.General.debugging_console') == 'Y') {
            if (empty($_SESSION['debugging_console']) && !empty($_SESSION['auth']['user_id'])) {
                $user_type = db_get_field("SELECT user_type FROM ?:users WHERE user_id = ?i", $_SESSION['auth']['user_id']);
                if ($user_type == 'A') {
                    $_SESSION['debugging_console'] = true;
                }
            }

            if (isset($_SESSION['debugging_console']) && $_SESSION['debugging_console'] == true) {
                error_reporting(0);
                $view->debugging = true;
            }
        }

        $smarty_plugins_dir = $view->getPluginsDir();
        $view->setPluginsDir(Registry::get('config.dir.functions') . 'smarty_plugins');
        $view->addPluginsDir(
            \Wizacha\Registry::defaultInstance()->get(
                [
                    'config',
                    \Wizacha\Marketplace\Smarty\Config::CONF_SERVICE,
                ]
            )
        );
        $view->addPluginsDir(container()->getParameter('smarty.plugin_directories'));
        $view->addPluginsDir($smarty_plugins_dir);

        $view->error_reporting = E_ALL & ~E_NOTICE;

        $view->registerDefaultPluginHandler(array('Tygh\SmartyEngine\Filters', 'smartyDefaultHandler'));

        $view->setArea($area);
        $view->use_sub_dirs = false;
        $view->compile_check = \defined('DEVELOPMENT') && DEVELOPMENT;
        $view->setLanguage((string) GlobalState::interfaceLocale());

        $view->assign('ldelim', '{');
        $view->assign('rdelim', '}');

        $view->assign('currencies', Registry::get('currencies'), false);
        $view->assign('primary_currency', CART_PRIMARY_CURRENCY, false);
        $view->assign('secondary_currency', CART_SECONDARY_CURRENCY, false);
        $view->assign('languages', Registry::get('languages'));
        $view->assign('superadmin', \array_key_exists('superadmin', $_GET));

        // Initialize Algolia credentials in view (if any)
        $config = \Wizacha\Registry::defaultInstance()->get(['config', Config::CFG_SEARCH_ENGINE]);
        if (!empty($config[Algolia::CFG_API_IDENTIFIER_GEOCODING]) && !empty($config[Algolia::CFG_API_KEY_LIMITED_RIGHTS_GEOCODING])) {
            $view->assign('AlgoliaLogin', $config[Algolia::CFG_API_IDENTIFIER_GEOCODING]);
            $view->assign('AlgoliaPassword', $config[Algolia::CFG_API_KEY_LIMITED_RIGHTS_GEOCODING]);
        }

        $view->assign('localizations', fn_get_localizations((string) GlobalState::interfaceLocale(), true));
        if (\defined('CART_LOCALIZATION')) {
            $view->assign('localization', fn_get_localization_data(CART_LOCALIZATION));
        }

        $view->assign(
            'isSandboxFull',
            container()->get('Wizacha\Marketplace\Metrics\SandboxMetrics')->isSandboxFull()
        );

        return $view;
    }

    private static function registerClasses(Core $view)
    {
        $view->registerClass('C2COrder', 'Wizacha\C2C\Order');
        $view->registerClass('OrderStatus', 'Wizacha\OrderStatus');
        $view->registerClass('RmaStatus', RmaStatus::class);
        $view->registerClass('PageType', PageType::class);
        $view->registerClass('Page', 'Wizacha\Page');
        $view->registerClass('Order', 'Wizacha\Order');
        $view->registerClass('Option', 'Wizacha\Option');
        $view->registerClass('User', 'Wizacha\User');
        $view->registerClass('WizachaProduct', 'Wizacha\Product');
        $view->registerClass('Shipping', 'Wizacha\Shipping');
        $view->registerClass('WizachaCompany', 'Wizacha\Company');
        $view->registerClass('WizachaAccountingPayment', AccountingPayment::class);
        $view->registerClass('User', 'Wizacha\User');
        $view->registerClass('FunctionPayload', 'Wizacha\Async\FunctionPayload');
        $view->registerClass('C2CCode', 'Wizacha\C2C\Code');
        $view->registerClass('RenderManager', 'Wizacha\BlockManager\RenderManager');
        $view->registerClass('SearchConfig', Config::class);
        $view->registerClass('Record', \Wizacha\Search\Record\AbstractRecord::class);
        $view->registerClass('RecordProduct', \Wizacha\Search\Record\Product::class);
        $view->registerClass('Premoderation', 'Wizacha\Premoderation');
        $view->registerClass('Category', 'Wizacha\Category');
        $view->registerClass('DeliveryType', DeliveryType::class);
    }
}
