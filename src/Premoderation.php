<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha;

use Wizacha\Marketplace\GlobalState\GlobalState;

/**
 * @deprecated Legacy class. Should be moved to the PIM module into proper services.
 */
class Premoderation
{
    //Standard statuses
    public const STATUS_STANDBY        = 'S';
    public const STATUS_PENDING        = 'P';
    public const STATUS_APPROVED       = 'Y';
    public const STATUS_DISAPPROVED    = 'N';

    //actions
    public const ACTION_APPROVE        = 'approve';
    public const ACTION_DISAPPROVE     = 'disapprove';
    public const ACTION_STANDBY        = 'standby';
    public const ACTION_PENDING        = 'pending';

    /**
     * @param string $action
     * @return string status
     */
    public static function getStatusByAction($action)
    {
        switch ($action) {
            case self::ACTION_DISAPPROVE:
                return self::STATUS_DISAPPROVED;
                break;
            case self::ACTION_STANDBY:
                return self::STATUS_STANDBY;
                break;
            case self::ACTION_APPROVE:
                return self::STATUS_APPROVED;
                break;
            case self::ACTION_PENDING:
            default:
                return self::STATUS_PENDING;
                break;
        }
    }
}
