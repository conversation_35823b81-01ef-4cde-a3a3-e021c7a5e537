<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\ActionLogger;

use MyCLabs\Enum\Enum;

/**
 * @method static ActionLoggerActionType CREATE()
 * @method static ActionLoggerActionType UPDATE()
 * @method static ActionLoggerActionType DELETE()
 */
class ActionLoggerActionType extends Enum
{
    public const CREATE = 'An object has been created';
    public const UPDATE = 'An object has been updated';
    public const DELETE = 'An object has been deleted';
}
