<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\ActionLogger\EventSubscribers;

use <PERSON>ymfony\Component\EventDispatcher\EventSubscriberInterface;
use Wizacha\ActionLogger;
use Wizacha\ActionLogger\ActionLoggerActionType;
use Wizacha\ActionLogger\ActionLoggerFactory;
use Wizacha\Marketplace\Order\Event\OrderStatusChanged;
use Wizacha\Marketplace\Order\Order;

class OrderSubscriber implements EventSubscriberInterface
{
    private ActionLogger $logger;

    public function __construct(ActionLoggerFactory $factory)
    {
        $this->logger = $factory->create(Order::class);
    }

    public static function getSubscribedEvents(): array
    {
        return [
            OrderStatusChanged::class => ['onUpdate', 0],
        ];
    }

    public function onUpdate(OrderStatusChanged $event): void
    {
        $this->logger->log(
            ActionLoggerActionType::UPDATE(),
            (string) $event->getOrderInfo()["order_id"]
        );
    }
}
