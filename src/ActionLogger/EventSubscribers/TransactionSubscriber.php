<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\ActionLogger\EventSubscribers;

use <PERSON>ymfony\Component\EventDispatcher\EventSubscriberInterface;
use Wizacha\ActionLogger;
use W<PERSON>cha\ActionLogger\ActionLoggerActionType;
use Wizacha\ActionLogger\ActionLoggerFactory;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Transaction\TransactionUpdatedEvent;

class TransactionSubscriber implements EventSubscriberInterface
{
    private ActionLogger $logger;

    public function __construct(ActionLoggerFactory $factory)
    {
        $this->logger = $factory->create(Order::class);
    }

    public static function getSubscribedEvents(): array
    {
        return [
            TransactionUpdatedEvent::class => ['onUpdate', 0],
        ];
    }

    public function onUpdate(TransactionUpdatedEvent $event): void
    {
        $this->logger->log(
            ActionLoggerActionType::UPDATE(),
            (string) $event->getTransaction()->getOrderId(),
            null,
            [
                'Debug BackTrace' => debug_backtrace(2),
                'Event Class' =>  \get_class($event),
            ]
        );
    }
}
