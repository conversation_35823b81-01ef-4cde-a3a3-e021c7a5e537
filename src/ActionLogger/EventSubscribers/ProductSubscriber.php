<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\ActionLogger\EventSubscribers;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Wizacha\ActionLogger;
use Wizacha\ActionLogger\ActionLoggerFactory;
use Wizacha\Events\IterableEvent;
use Wizacha\Product;

class ProductSubscriber implements EventSubscriberInterface
{
    private ActionLogger $logger;

    public function __construct(ActionLoggerFactory $factory)
    {
        $this->logger = $factory->create(Product::class);
    }

    public static function getSubscribedEvents(): array
    {
        return [
            Product::EVENT_CREATE => ['onCreate', 0],
            Product::EVENT_UPDATE => ['onUpdate', 0],
            Product::EVENT_DELETE => ['onDelete', 0],
        ];
    }

    public function onUpdate(IterableEvent $event): void
    {
        foreach ($event as $eventInfos) {
            $this->logger->log(
                ActionLogger\ActionLoggerActionType::UPDATE(),
                (string) $eventInfos
            );
        }
    }

    public function onCreate(IterableEvent $event): void
    {
        foreach ($event as $eventInfos) {
            $this->logger->log(
                ActionLogger\ActionLoggerActionType::CREATE(),
                (string) $eventInfos
            );
        }
    }

    public function onDelete(IterableEvent $event): void
    {
        foreach ($event as $eventInfos) {
            $this->logger->log(
                ActionLogger\ActionLoggerActionType::DELETE(),
                (string) $eventInfos
            );
        }
    }
}
