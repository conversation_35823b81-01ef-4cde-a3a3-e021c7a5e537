<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\ActionLogger\EventSubscribers;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Wizacha\Marketplace\User\Event\UserPasswordChanged;
use Wizacha\Marketplace\User\UserPasswordHistory;
use Wizacha\Marketplace\User\UserPasswordHistoryService;

class UserEventSubscriber implements EventSubscriberInterface
{
    private UserPasswordHistoryService $userPasswordHistoryService;

    public function __construct(UserPasswordHistoryService $userPasswordHistoryService)
    {
        $this->userPasswordHistoryService = $userPasswordHistoryService;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            UserPasswordChanged::class => ['onUserPasswordChanged', 10],
        ];
    }

    public function onUserPasswordChanged(UserPasswordChanged $event): void
    {
        $user = $event->getUser();
        $this->userPasswordHistoryService->save(new UserPasswordHistory($user, $user->getPassword()));
    }
}
