<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\ActionLogger\EventSubscribers;

use <PERSON>ymfony\Component\EventDispatcher\EventSubscriberInterface;
use Wizacha\ActionLogger;
use Wizacha\ActionLogger\ActionLoggerActionType;
use Wizacha\ActionLogger\ActionLoggerFactory;
use Wizacha\Events\IterableEvent;
use Wizacha\Shipping;

class ShippingSubscriber implements EventSubscriberInterface
{
    private ActionLogger $logger;

    public function __construct(ActionLoggerFactory $factory)
    {
        $this->logger = $factory->create(Shipping::class);
    }

    public static function getSubscribedEvents(): array
    {
        return [
            Shipping::EVENT_VENDOR_UPDATE => ['onUpdate', 0],
        ];
    }

    public function onUpdate(IterableEvent $event): void
    {
        foreach ($event as $eventInfos) {
            $this->logger->log(
                ActionLoggerActionType::UPDATE(),
                (string) $eventInfos['shipping_id']
            );
        }
    }
}
