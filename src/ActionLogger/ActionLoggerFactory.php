<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\ActionLogger;

use Psr\Log\LoggerInterface;
use Wizacha\ActionLogger;
use Wizacha\AppBundle\Security\User\UserService;

class ActionLoggerFactory
{
    private LoggerInterface $logger;
    private UserService $userService;

    public function __construct(LoggerInterface $logger, UserService $userService)
    {
        $this->logger = $logger;
        $this->userService = $userService;
    }

    public function create(string $objectClass): ActionLogger
    {
        return new ActionLogger(
            $objectClass,
            $this->logger,
            $this->userService
        );
    }
}
