<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\ActionLogger;

use MyCLabs\Enum\Enum;
use Psr\Log\LogLevel;

/**
 * @method static ActionLoggerLevel EMERGENCY()
 * @method static ActionLoggerLevel ALERT()
 * @method static ActionLoggerLevel CRITICAL()
 * @method static ActionLoggerLevel ERROR()
 * @method static ActionLoggerLevel WARNING()
 * @method static ActionLoggerLevel NOTICE()
 * @method static ActionLoggerLevel INFO()
 * @method static ActionLoggerLevel DEBUG()
 */
class ActionLoggerLevel extends Enum
{
    public const EMERGENCY = LogLevel::EMERGENCY;
    public const ALERT = LogLevel::ALERT;
    public const CRITICAL = LogLevel::CRITICAL;
    public const ERROR = LogLevel::ERROR;
    public const WARNING = LogLevel::WARNING;
    public const NOTICE = LogLevel::NOTICE;
    public const INFO = LogLevel::INFO;
    public const DEBUG = LogLevel::DEBUG;
}
