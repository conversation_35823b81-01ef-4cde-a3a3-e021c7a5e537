<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Decimal;

use Doctrine\DBAL\Platforms\AbstractPlatform;
use Doctrine\DBAL\Types\Type;

/**
 * Maps Decimal instances to BIGINT in database.
 *
 * For the record Doctrine\DBAL\Types\BigIntType maps BIGINT to PHP strings (to avoid loosing precision on 32b systems).
 * Since we only use 64b systems we will not loose precision (and we want to store the amounts as integers,
 * not strings).
 */
abstract class AbstractDecimalDoctrineType extends Type
{
    abstract public function getName(): string;

    /**
     * @return AbstractDecimal child as Money or ExchangeRate
     */
    abstract public function getPhpInstance(int $value);

    public function getSQLDeclaration(array $fieldDeclaration, AbstractPlatform $platform)
    {
        // store in BIGINT on the SQL side
        return $platform->getBigIntTypeDeclarationSQL($fieldDeclaration);
    }

    public function convertToDatabaseValue($value, AbstractPlatform $platform)
    {
        if ($value === null) {
            return null;
        }

        if (false === ($value instanceof AbstractDecimal)) {
            throw new \InvalidArgumentException('Can only map Decimal objects');
        }

        return $value->getInt();
    }

    public function convertToPHPValue($value, AbstractPlatform $platform)
    {
        if ($value === null) {
            return null;
        }

        // MySQL BIGINT are returned as string by PDO
        if (\is_string($value)) {
            $strValue = $value;
            $value = (int) $value;

            // Check that we didn't loose any precision because of the cast
            if (strcmp((string) $value, $strValue) !== 0) {
                throw new \RuntimeException(
                    'The decimal value stored in database ("%s") cannot be stored in a PHP integer without loosing precision (got "%s")',
                    (string) $value,
                    $strValue
                );
            }
        }

        return $this->getPhpInstance($value);
    }

    public function getBindingType()
    {
        return \PDO::PARAM_INT;
    }

    public function requiresSQLCommentHint(AbstractPlatform $platform)
    {
        return true;
    }
}
