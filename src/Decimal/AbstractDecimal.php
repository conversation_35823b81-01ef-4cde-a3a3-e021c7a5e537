<?php

/**
 * This file is based on the Money package: https://github.com/se<PERSON><PERSON><PERSON><PERSON>/money
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file https://github.com/sebas<PERSON><PERSON><PERSON>/money/blob/master/LICENSE
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * @license BSD 3-clause
 */

declare(strict_types=1);

namespace Wizacha\Decimal;

use InvalidArgumentException;
use OverflowException;

/**
 * Value object that represents a decimal value
 */
abstract class AbstractDecimal
{
    /** @var int */
    protected static $digitsPrecision = 4;

    /** @var int[] */
    protected static $roundingModes = [
        PHP_ROUND_HALF_UP,
        PHP_ROUND_HALF_DOWN,
        PHP_ROUND_HALF_EVEN,
        PHP_ROUND_HALF_ODD,
    ];

    public function __construct(int $value)
    {
        $this->setValue($this->castToInt($value));
    }

    abstract protected function getValue(): int;

    /**
     * @return $this
     */
    abstract protected function setValue(int $value): self;

    /**
     * Creates a Decimal object from a string or a float
     *
     * This method is designed to take into account the errors that can arise
     * from manipulating floating point numbers.
     *
     * If the number of decimals in the string is higher than the decimal's
     * number of fractional digits then the value will be rounded to the
     * decimal's number of fractional digits.
     *
     * @param  string|float|int $value
     * @return $this
     */
    public static function fromVariable($value): self
    {
        $value = static::convertToInt($value);

        // Bypass le constructeur pour conserver la précision à 4 décimales si on nous passe un float assez précis.
        $decimal = new static(0);
        $decimal->setValue($value);

        return $decimal;
    }

    /** Creates a Decimal object from a full integer value */
    public static function fromValue(int $value): self
    {
        $decimal = new static(0);
        $decimal->setValue($value);

        return $decimal;
    }

    protected static function getDefaultPrecisionCoeff(): int
    {
        return pow(10, static::$digitsPrecision);
    }

    /**
     * Convert a decimal number into an integer depending on a precision coeff
     * eg: if coeff = 10000 :
     *     18.46428 => 184643
     *
     * @param  string|float|int $value
     * @throws InvalidArgumentException
     * @return int
     */
    protected static function convertToInt($value): int
    {
        if (false === (\is_string($value) || \is_int($value) || \is_float($value))) {
            throw new InvalidArgumentException('$value must be a string / int / float, ' . \gettype($value) . ' given');
        }

        return (int) round(
            $value * static::getDefaultPrecisionCoeff(),
            0,
            PHP_ROUND_HALF_UP
        );
    }

    /**
     * By default its the same than getDefaultPrecisionCoeff but it can be useful to override it
     * ie: In class Money some instance haven't the same precision
     */
    protected function getPrecisionCoeff(): int
    {
        return static::getDefaultPrecisionCoeff();
    }

    public function getInt(): int
    {
        return $this->castToInt(round($this->getValue(), 0));
    }

    public function getFloat(int $outputDigits = null): float
    {
        return round(
            $this->getValue() / $this->getPrecisionCoeff(),
            $outputDigits ?? static::$digitsPrecision,
            PHP_ROUND_HALF_ODD
        );
    }

    /**
     * Returns a new Decimal object that represents the value
     * of the sum of this Decimal object and another.
     * @return $this
     */
    public function add(AbstractDecimal $other): self
    {
        $value = $this->getValue() + $other->getValue();
        $this->assertIsInteger($value);

        return $this->newInstance($value);
    }

    /**
     * Returns a new Decimal object that represents the value
     * of the difference of this Decimal object and another.
     * @return $this
     */
    public function subtract(AbstractDecimal $other): self
    {
        $value = $this->getValue() - $other->getValue();
        $this->assertIsInteger($value);

        return $this->newInstance($value);
    }

    /**
     * Returns a new Decimal object that represents the value
     * of this Decimal object multiplied by a given factor.
     *
     * @throws InvalidArgumentException Invalid rounding mode
     * @return $this
     */
    public function multiply(float $factor, int $roundingMode = PHP_ROUND_HALF_UP): self
    {
        if (false === \in_array($roundingMode, static::$roundingModes)) {
            throw new InvalidArgumentException(
                '$roundingMode [' . $roundingMode . '] must be a valid rounding mode (PHP_ROUND_XX)'
            );
        }

        return $this->newInstance(
            $this->castToInt(
                round($factor * $this->getValue(), 0, $roundingMode)
            )
        );
    }

    /**
     * Returns a new Decimal object that represents the value
     * of this Decimal object divided by a given divisor.
     *
     * @throws InvalidArgumentException Invalid rounding mode
     * @return $this
     */
    public function divide(float $divisor, int $roundingMode = PHP_ROUND_HALF_UP): self
    {
        if (false === \in_array($roundingMode, static::$roundingModes)) {
            throw new InvalidArgumentException(
                '$roundingMode [' . $roundingMode . '] must be a valid rounding mode (PHP_ROUND_XX)'
            );
        }

        return $this->newInstance(
            $this->castToInt(
                round($this->getValue() / $divisor, 0, $roundingMode)
            )
        );
    }

    /**
     * Returns the absolute value of this Decimal object.
     */
    public function absolute(): self
    {
        if ($this->isNegative()) {
            return $this->multiply(-1);
        }

        return $this->newInstance($this->getValue());
    }

    /**
     * Compares this Decimal object to another.
     *
     * Returns an int less than, equal to, or greater than zero
     * if the value of this Decimal object is considered to be respectively
     * less than, equal to, or greater than the other Decimal object.
     *
     * @return int -1|0|1
     */
    public function compareTo(AbstractDecimal $other): int
    {
        if ($this->getValue() === $other->getValue()) {
            return 0;
        }

        return $this->getValue() < $other->getValue() ? -1 : 1;
    }

    public function equals(AbstractDecimal $other): bool
    {
        return $this->compareTo($other) === 0;
    }

    public function greaterThan(AbstractDecimal $other): bool
    {
        return $this->compareTo($other) === 1;
    }

    public function greaterThanOrEqual(AbstractDecimal $other): bool
    {
        return $this->greaterThan($other) || $this->equals($other);
    }

    public function lessThan(AbstractDecimal $other): bool
    {
        return $this->compareTo($other) === -1;
    }

    public function lessThanOrEqual(AbstractDecimal $other): bool
    {
        return $this->lessThan($other) || $this->equals($other);
    }

    public function isZero(): bool
    {
        return $this->getValue() === 0;
    }

    public function isPositive(): bool
    {
        return $this->getValue() > 0;
    }

    public function isNegative(): bool
    {
        return $this->getValue() < 0;
    }

    /**
     * Raises an exception if the value is not an integer
     *
     * @param number $value
     * @throws OverflowException
     * @return $this
     */
    protected function assertIsInteger($value): self
    {
        if (false === \is_int($value)) {
            throw new OverflowException();
        }

        return $this;
    }

    /**
     * Raises an exception if the value is outside of the int bounds
     *
     * @param number $value
     * @throws OverflowException
     * @return $this
     */
    protected function assertInsideIntegerBounds($value): self
    {
        if (abs($value) > PHP_INT_MAX) {
            throw new OverflowException();
        }

        return $this;
    }

    /**
     * Cast an value to an int but ensure that the operation won't hide overflow
     *
     * @param number $value
     * @throws OverflowException
     * @return int
     */
    protected function castToInt($value): int
    {
        $this->assertInsideIntegerBounds($value);

        return \intval($value);
    }

    /**
     * @return $this
     */
    protected function newInstance(int $value): self
    {
        $decimal = new static(0);
        $decimal->setValue($value);

        return $decimal;
    }
}
