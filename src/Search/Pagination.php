<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Search;

/**
 * Pagination helper.
 */
class Pagination
{
    public const DEFAULT_RESULTS_PER_PAGE = 10;

    /**
     * Do not allow fetching more than 100 results per page.
     */
    public const MAX_RESULTS_PER_PAGE = 100;

    private $page;
    private $resultsPerPage;
    private $totalResultCount;
    private $nbPages;

    public function __construct(
        int $page = 1,
        int $resultsPerPage = self::DEFAULT_RESULTS_PER_PAGE,
        ?int $totalResultCount = null,
        ?int $nbPages = null
    ) {
        $this->page = max($page, 1);
        $this->resultsPerPage = max(min($resultsPerPage, self::MAX_RESULTS_PER_PAGE), 1);
        $this->totalResultCount = $totalResultCount;
        $this->nbPages = $nbPages;
    }

    public function getPage(): int
    {
        return $this->page;
    }

    public function getResultsPerPage(): int
    {
        return $this->resultsPerPage;
    }

    public function getTotalResultCount(): ?int
    {
        return $this->totalResultCount;
    }

    public function getNbPages(): ?int
    {
        if (null === $this->nbPages) {
            $this->calculateNbPages();
        }

        return $this->nbPages;
    }

    /**
     * Returns the offset, i.e. the number of results to skip.
     */
    public function getOffset(): int
    {
        return ($this->page - 1) * $this->resultsPerPage;
    }

    /**
     * @param int $totalResultCount
     */
    public function setTotalResultCount(int $totalResultCount): void
    {
        $this->totalResultCount = $totalResultCount;
    }

    private function calculateNbPages(): void
    {
        // If we can calculate it
        if (null !== $this->totalResultCount
            && null !== $this->resultsPerPage
            && 0 < $this->resultsPerPage
        ) {
            $this->nbPages = (int) ceil($this->totalResultCount / $this->resultsPerPage);
        }
    }
}
