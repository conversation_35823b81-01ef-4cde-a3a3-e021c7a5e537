<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Search;

use Symfony\Component\HttpFoundation\Request;

class GeoFilter
{
    public const DEFAULT_RADIUS = 15000;

    /**
     * @var float
     */
    private $latitude;

    /**
     * @var float
     */
    private $longitude;

    /**
     * @var int
     */
    private $radius;

    /**
     * @param int $radius Radius in meters
     */
    public function __construct(float $latitude, float $longitude, int $radius = self::DEFAULT_RADIUS)
    {
        $this->latitude = $latitude;
        $this->longitude = $longitude;
        $this->radius = $radius;
    }

    public static function fromHttpRequest(Request $request): ?self
    {
        $inputGeoFilter = $request->query->get('geo');

        // validate input
        $isValidInput = \is_array($inputGeoFilter) && isset($inputGeoFilter['lat']) && isset($inputGeoFilter['lng']);
        if (!$isValidInput) {
            return null;
        }

        // normalize coordinates
        $latitude = (float) $inputGeoFilter['lat'];
        $longitude = (float) $inputGeoFilter['lng'];
        $radius = (int) $inputGeoFilter['radius'];

        // validate coordinates
        $isValidLatitude = ($latitude > -90) && ($latitude < 90);
        $isValidLongitude = ($longitude > -180) && ($longitude < 180);
        if (!($isValidLatitude && $isValidLongitude)) {
            return null;
        }

        return new self($latitude, $longitude, $radius ?: self::DEFAULT_RADIUS);
    }

    public function getLatitude(): float
    {
        return $this->latitude;
    }

    public function getLongitude(): float
    {
        return $this->longitude;
    }

    public function getRadius(): int
    {
        return $this->radius;
    }
}
