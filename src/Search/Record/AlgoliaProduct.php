<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Search\Record;

use DateTimeImmutable;
use DateTimeInterface;
use Tygh\Languages\Languages;
use Wizacha\Component\Locale\Locale;
use Wizacha\Marketplace\Catalog\Attribute;
use Wizacha\Marketplace\Catalog\AttributeValue;
use Wizacha\Marketplace\Catalog\AttributeVariant;
use Wizacha\Marketplace\Catalog\Category\CategorySummary;
use Wizacha\Marketplace\Catalog\Company\CompanySummary;
use Wizacha\Marketplace\Catalog\Declination\DeclinationImages;
use Wizacha\Marketplace\Catalog\Geolocation;
use Wizacha\Marketplace\Catalog\Product\ProductCondition;
use Wizacha\Marketplace\Catalog\Product\ProductSummary;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Image\Image;
use Wizacha\Marketplace\PIM\Attribute\AttributeType;
use Wizacha\Marketplace\PIM\TransactionMode\TransactionMode;
use Wizacha\Money\Money;

class AlgoliaProduct extends AbstractRecord implements ProductSummary
{
    private $categoryPath;
    private $conditions;
    private $attributes;

    public function __construct(array $data = [])
    {
        $this->data = $data;
    }

    /**
     * {@inheritdoc}
     */
    public function indices(\Wizacha\Registry $registry): array
    {
        $contentLocale = GlobalState::contentLocale();
        $indices = [];

        foreach (array_keys(Languages::getAvailable()) as $locale) {
            GlobalState::switchContentTo(new Locale($locale));
            $indices[] = $this->index($registry);
        }

        GlobalState::switchContentTo($contentLocale);

        return $indices;
    }

    /**
     * @return int|string
     */
    public function getProductId()
    {
        return $this->data()['productId'];
    }

    public function getName(): string
    {
        return $this->data()['name'];
    }

    public function getCode(): string
    {
        return $this->data()['code'];
    }

    public function getMinimumPrice(): Money
    {
        return Money::fromVariable($this->data()['price']);
    }

    public function getMaxPriceAdjustment(): ?int
    {
        return $this->data()['max_price_adjustment'];
    }

    public function getCrossedOutPrice(): ?Money
    {
        $price = $this->data()['crossedOutPrice'] ?? null;

        return $price ? Money::fromVariable($price) : null;
    }

    public function isAvailable(): bool
    {
        return (bool) $this->data()['isAvailable'];
    }

    /** @return float */
    public function getAverageRating(): float
    {
        return $this->data()['averageRatingFloat'] ?? 0;
    }

    public function getUrl(): string
    {
        return $this->data()['url'];
    }

    public function getCreatedAt(): DateTimeInterface
    {
        return DateTimeImmutable::createFromFormat('U', $this->data()['createdAt'] ?? 0);
    }

    public function getUpdatedAt(): DateTimeInterface
    {
        return DateTimeImmutable::createFromFormat('U', $this->data()['updatedAt'] ?? 0);
    }

    public function getDeclinationCount(): int
    {
        return (int) $this->data()['declinationCount'];
    }

    public function getAffiliateLink(): ?string
    {
        return $this->data()['affiliateLink'];
    }

    public function getMainImage(): ?Image
    {
        $image = $this->data()['mainImage'];

        return $image !== null && \array_key_exists('id', $image) === true
            ? new Image($image['id'], $image['altText'] ?? null)
            : null;
    }

    public function getSubtitle(): string
    {
        return (string) $this->data()['subtitle'];
    }

    public function getShortDescription(): string
    {
        return (string) $this->data()['shortDescription'];
    }

    public function getCategoryPath(): array
    {
        if (!$this->categoryPath) {
            $this->categoryPath = array_map(function (array $data) {
                return new CategorySummary($data['id'], $data['name'], $data['slug'], $data['position'] ?? 0);
            }, $this->data()['categoryPath'] ?? []);
        }

        return $this->categoryPath;
    }

    public function getSlug(): string
    {
        return (string) $this->data()['slug'];
    }

    public function getCategorySlugPath(): string
    {
        return (string) $this->data()['categorySlugPath'];
    }

    /**
     * @return AttributeValue[]
     */
    public function getAttributes(): array
    {
        if ($this->attributes !== null) {
            return $this->attributes;
        }

        $this->attributes = array_map(function (array $data) {
            return new AttributeValue(
                new Attribute(
                    $data['attribute']['id'],
                    $data['attribute']['name'],
                    new AttributeType($data['attribute']['type']),
                    $data['attribute']['position'],
                    $data['attribute']['parentId'],
                    $data['attribute']['code'],
                    $data['attribute']['isUsedForRecommendations']
                ),
                array_map(function ($value) {
                    return new AttributeVariant(
                        $value['id'],
                        $value['attributeId'] ?? null, // For BC
                        $value['name'],
                        $value['slug'],
                        $value['image'] ? new Image($value['image']['id']) : null
                    );
                }, $data['values'])
            );
        }, $this->data()['attributes'] ?? []);

        return $this->attributes;
    }

    /**
     * @return ProductCondition[]
     */
    public function getConditions(): array
    {
        if ($this->conditions !== null) {
            return $this->conditions;
        }

        $this->conditions = array_map(
            function ($condition) {
                return new ProductCondition($condition);
            },
            $this->data()['conditions'] ?? []
        );

        return $this->conditions;
    }

    public function getTransactionMode(): TransactionMode
    {
        return new TransactionMode($this->data()['transactionMode'] ?? TransactionMode::TRANSACTIONAL);
    }

    /**
     * @return CompanySummary[]
     */
    public function getCompanies(): array
    {
        return array_map(function ($companyData): CompanySummary {
            return new CompanySummary(
                (int) $companyData['id'],
                (string) $companyData['name'],
                (string) $companyData['slug'],
                (bool) $companyData['isProfessional'] ?? true, // Valeur par défaut ne servant que pour le passage en prod
                isset($companyData['image']['id']) ? new Image($companyData['image']['id']) : null,
                isset($companyData['averageRating']) && !\is_null($companyData['averageRating']) ? (float) $companyData['averageRating'] : null
            );
        }, $this->data()['companiesSummaries'] ?? []);
    }

    // For backward compatibility.
    public function getGeolocation(): ?Geolocation
    {
        if (\is_array($this->data()['_geoloc'])) {
            $geoloc = reset($this->data()['_geoloc']);
            if (isset($geoloc['lat']) && $geoloc['lat'] !== null) {
                return new Geolocation(
                    (float) $geoloc['lat'],
                    (float) $geoloc['lng'],
                    (string) $geoloc['label'],
                    (string) $geoloc['zipcode']
                );
            }
        }

        return null;
    }

    /**
     * Return geolocation coordinates
     *
     * @return Geolocation[]
     */
    public function getGeolocations(): ?array
    {
        // return an array of geolocations
        return $this->data()['_geoloc'] ?? [];
    }

    public function getMainDeclinationId(): ?string
    {
        return $this->data['mainDeclinationId'] ?? null;
    }

    public function getProductTemplateType(): ?string
    {
        return $this->data['product_template_type'] ?? null;
    }

    public function getOffers(): array
    {
        return $this->data['offers'] ?? [];
    }

    public function isSubscription(): bool
    {
        return $this->data()['isSubscription'] ?? false;
    }

    public function isRenewable(): bool
    {
        return $this->data()['isRenewable'] ?? false;
    }

    public function getQuoteRequestsMinQuantity(): int
    {
        return $this->data['quoteRequestsMinQuantity'] ?? 1;
    }

    public function isExclusiveToQuoteRequests(): bool
    {
        return $this->data['isExclusiveToQuoteRequests'] ?? false;
    }

    protected function indexKey()
    {
        return \Wizacha\Search\Config::INDEX_PRODUCTS;
    }

    protected function getRecordType(): string
    {
        return Product::class;
    }

    /**
     * @return array[]
     */
    public function getImages(): array
    {
        return $this->data()['images'] ?? [];
    }

    /**
     * @return DeclinationImages[]
     */
    public function getDeclinationsImages(): array
    {
        return array_map(
            function (array $declinationImage): DeclinationImages {
                return new DeclinationImages(
                    $declinationImage['declinationId'],
                    array_map(
                        function (array $image): Image {
                            return new Image($image['id'], $image['altText']);
                        },
                        $declinationImage['images'] ?? []
                    )
                );
            },
            $this->data()['declinationsImages'] ?? []
        );
    }

    public function summary(): array
    {
        return [
            'productId' => $this->getProductId(),
            'supplierReference' => $this->data()['supplierReference'],
            'name' => $this->getName(),
            'code' => $this->getCode(),
            'minimumPrice' => $this->getMinimumPrice(),
            'maxPriceAdjustement' => $this->getMaxPriceAdjustment(),
            'crossedOutPrice' => $this->getMinimumPrice(),
            'isAvailable' => $this->isAvailable(),
            'averageRating' => $this->getAverageRating(),
            'url' => $this->getUrl(),
            'createdAt' => $this->getCreatedAt(),
            'updatedAt' => $this->getUpdatedAt(),
            'declinationCount' => $this->getDeclinationCount(),
            'mainImage' => $this->getMainImage(),
            'subtitle' => $this->getSubtitle(),
            'shortDescription' => $this->getShortDescription(),
            'categoryPath' => $this->getCategoryPath(),
            'slug' => $this->getSlug(),
            'categorySlugPath' => $this->getCategorySlugPath(),
            'attributes' => $this->getAttributes(),
            'conditions' => $this->getConditions(),
            'transactionMode' => $this->getTransactionMode(),
            'companies' => $this->getCompanies(),
            'geolocation' => $this->getGeolocation(),
            'geolocations' => $this->getGeolocations(),
            'mainDeclinationId' => $this->getMainDeclinationId(),
            'productTemplateType' => $this->getProductTemplateType(),
            'offers' => $this->getOffers(),
            'isSubscription' => $this->isSubscription(),
            'isRenewable' => $this->isRenewable(),
            'images' => $this->getImages(),
            'declinationsImages' => $this->getDeclinationsImages(),
        ];
    }
}
