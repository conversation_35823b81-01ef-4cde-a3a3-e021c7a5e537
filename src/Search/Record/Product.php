<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Search\Record;

use DateTimeImmutable;
use DateTimeInterface;
use Wizacha\Marketplace\Catalog\Company\CompanySummary;
use Wizacha\Marketplace\Catalog\Product\ProductCondition;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Image\Image;
use Wizacha\Marketplace\PIM\Attribute\AttributeType;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct;
use Wizacha\Money\Money;
use Wizacha\Search\Index\AbstractIndex;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Component\Locale\Locale;
use Tygh\Languages\Languages;

/**
 * @deprecated Replaced with AlgoliaProduct, still used in the legacy Algolia search
 * @see AlgoliaProduct
 */
class Product extends AbstractRecord
{
    public const KEY_PRODUCT_ID            = 'product_id';
    public const KEY_SUPPLIER_REFERENCE    = 'supplier_reference';
    public const MAX_SIZE_DESCR            = 1000;
    public const KEY_PRICE                 = 'price';
    public const KEY_CROSSED_OUT_PRICE     = 'crossed_out_price';
    public const KEY_PRICE_WITH_SHIPPING   = 'price_with_shipping';
    public const KEY_CHEAPEST_SHIPPING     = 'cheapest_shipping';
    public const KEY_VENDOR                = 'vendor';
    public const KEY_VENDOR_TYPE           = 'vendor_type';
    public const KEY_CONDITION             = 'condition';
    public const KEY_GEOLOC                = '_geoloc';
    public const KEY_FREE_FEATURES         = 'free_features';
    public const KEY_TIMESTAMP             = 'timestamp'; //Used for sorting by newest
    public const KEY_UPDATE_TIMESTAMP      = 'update_timestamp';
    public const KEY_CATEGORY_1            = 'category_1';
    public const KEY_CATEGORY_2            = 'category_2';
    public const KEY_CATEGORY_3            = 'category_3';
    public const KEY_CATEGORY_MAIN         = 'main_category';
    public const TAG_GEOLOC                = 'geoloc';
    public const NAMESPACE_VENDOR          = 'v';
    public const FEATURE_EMPTY_VALUE       = '∅';
    public const KEY_AVERAGE_RATING        = 'average_rating';
    public const KEY_HAS_STOCK             = 'has_stock';  // Use to sorting no stock at end of list
    public const KEY_BRAND                 = 'brand';
    public const KEY_AFFILIATE_LINK        = 'affiliate_link';
    public const KEY_BEST_SELLERS_SCORE    = 'best_sellers_score';
    public const KEY_HAS_GEOLOC            = 'hasGeoloc';

    public function __construct(array $data = [], \Wizacha\Registry $registry = null)
    {
        if (empty($data['product_id'])) {
            return;
        }
        $registry = $registry ? : \Wizacha\Registry::defaultInstance();

        $product = new \Wizacha\Product($data['product_id']);
        $freeFeatureData = [];
        foreach ($product->getFreeFeatures() as $featureName => $values) {
            foreach ($values as $value) {
                $freeFeatureData[] = $featureName . ' ' . $value;
            }
        }

        $companyData = fn_get_company_data($data['company_id'] ?? $product->getCompanyId());
        $company = new Company($companyData['company_id']);
        $companyData['rating'] = $company->getAverageRating();

        $imageWidth = $registry->container->hasParameter('search_record_product_image_width') ? $registry->container->getParameter('search_record_product_image_width') : 175;
        $imageHeight = $registry->container->hasParameter('search_record_product_image_height') ? $registry->container->getParameter('search_record_product_image_height') : 175;

        $productFeatures = (array) $product->getCscartData()['product_features'];

        if (MultiVendorProduct::isMultiVendorProductId($data['product_id'])) {
            $productService = $registry->container->get('marketplace.multi_vendor_product.service');
        } else {
            $productService = $registry->container->get('marketplace.pim.product.service');
        }
        $productCategory = $product->getCategory();

        $this->data = [
            static::KEY_OBJECT_ID               => $data[self::KEY_OBJECT_ID],
            static::KEY_PRODUCT_ID              => $data['product_id'],
            static::KEY_NAME                    => $product->getName(),
            static::KEY_IMAGE_URL               => $product->getMainImageUrl($imageWidth, $imageHeight),
            'main_image'                        => $product->getMainImage(),
            static::KEY_URL                     => $product->getFrontUrl('rel'),
            static::KEY_DESCR                   => self::formatDescription($data['full_description']),
            static::KEY_PRICE                   => $data['price'],
            static::KEY_TAGS                    => [static::NAMESPACE_VENDOR . $data['company_id']],
            static::KEY_FREE_FEATURES           => $freeFeatureData,
            static::KEY_AVERAGE_RATING          => $registry->container->get('marketplace.review.product.service')->getAverageRating($data['product_id']),
            static::KEY_HAS_STOCK               => 0 < $product->getInventoryAmount(),
            'variant_images'                    => $this->getVariantImages($productFeatures),
            'rating_count'                      => $registry->container->get('marketplace.review.product.service')->getRatingCount($data['product_id']),
            'categories'                        => $productCategory->getPathIds(),
            'publicFeatures'                    => $productService->getAllLegacyAttributes($data['product_id'], true, true, false),
            'companiesSummaries'                => [
                new CompanySummary(
                    (int) $companyData['company_id'],
                    (string) $companyData['company'],
                    (string) $companyData['seo_name'],
                    $company->isProfessional(),
                    isset($companyData['main_pair']['image_id']) ? new Image((int) $companyData['main_pair']['image_id']) : null,
                    isset($companyData['rating']) && !\is_null($companyData['rating']) ? (float) $companyData['rating'] : null
                ),
            ],
        ];
        if ($companyData['w_company_type']) {
            $this->data[static::KEY_VENDOR_TYPE]  = [__("w_company_type_filter_{$companyData['w_company_type']}")];
        }
        if ($data[\Wizacha\Product::CONDITION_DB_FIELD]) {
            $this->data[static::KEY_CONDITION] = [__("w_product_condition_{$data[\Wizacha\Product::CONDITION_DB_FIELD]}")];
        }

        $this->data = array_merge(
            $this->data,
            $this->convertFeatures($productFeatures),
            $data
        );

        if (array_filter($product->getGeoloc())) {
            list($lat, $lng, ) = $product->getGeoloc();
            if ($lat && $lng) {
                $this->data[static::KEY_GEOLOC] = [
                    'lat' => $lat,
                    'lng' => $lng,
                ];
            }
        } elseif ($productCategory->isTransactional()) {
            //Store first available Geoloc data
            foreach ($data['shippings'] as $shipping) {
                if (empty($shipping['latitude']) || empty($shipping['longitude'])) {
                    continue;
                }
                $this->data[static::KEY_GEOLOC] = [
                    'lat' => \floatval($shipping['latitude']),
                    'lng' => \floatval($shipping['longitude']),
                ];
                break;
            }
        }

        $productIndex = $registry->container->get('marketplace.search.product_index');
        $this->data = array_merge($this->data, $productIndex->getFacetsFromCategory($productCategory));

        if (isset($this->data[static::KEY_GEOLOC])) {
            $this->data[static::KEY_TAGS][] = static::TAG_GEOLOC;
        }

        // Set attribut 'hasGeoloc' for Algolia
        $this->data[static::KEY_HAS_GEOLOC] = isset($this->data[static::KEY_GEOLOC]) ? true : false;
    }

    /**
     * @param array $productFeatures (like features in fn_get_product_data)
     * @return array mixed [PREFIX.parent_group_name.feature_name => value,...]
     */
    public function convertFeatures(array $productFeatures)
    {
        //Moved subfeature on features level and filter empty values
        $productFeatures = AbstractIndex::linearizeFeatures($productFeatures);

        return array_reduce(
            $productFeatures,
            function ($return, $feature) {
                $name = AbstractIndex::getNameFromLinearizedFeature($feature);
                if (!empty($feature['variant_id'])) {
                    $value = array_column($feature['variants'], 'variant');
                    if ($feature['feature_type'] == AttributeType::LIST_NUMBER) {
                        $value = array_map('floatval', $value);
                    }
                } elseif (!empty($feature['value'])) {
                    $value = $feature['value'];
                } elseif (isset($feature['value_int'])) {
                    $value = \floatval($feature['value_int']);
                } else {
                    $value = self::FEATURE_EMPTY_VALUE;
                }
                $return[$name] = $value;

                return $return;
            },
            []
        );
    }

    public static function getBrandFacetName(): string
    {
        return self::getAttributeFacetName(fn_w_get_brand_id());
    }

    public static function getAttributeFacetName($attributeId): string
    {
        $feature = fn_get_product_feature_data($attributeId);

        return AbstractIndex::getNameFromLinearizedFeature($feature);
    }

    /**
     * @deprecated Use ProductRecordFactory instead
     * @see ProductRecordFactory
     * @param \Wizacha\Marketplace\ReadModel\Product $product
     * @return Product
     */
    public static function create(\Wizacha\Marketplace\ReadModel\Product $product)
    {
        $declinations = $product->getData();
        $firstDeclinationData = $declinations[0];

        $data = [
            static::KEY_OBJECT_ID               => $product->getId(),
            static::KEY_PRODUCT_ID              => $product->getId(),
            static::KEY_NAME                    => $product->getName(),
            static::KEY_URL                     => $product->getUrl(),
            static::KEY_SUPPLIER_REFERENCE      => $product->getSupplierReference(),
            static::KEY_DESCR                   => self::formatDescription($product->getDescription()),
            static::KEY_PRICE                   => $product->getCurrentPrice()->getConvertedAmount(),
            static::KEY_TIMESTAMP               => $product->getCreatedAt()->getTimestamp(),
            static::KEY_UPDATE_TIMESTAMP        => $product->getUpdatedAt()->getTimestamp(),
            static::KEY_PRICE_WITH_SHIPPING     => $firstDeclinationData['final_price_with_shipping'],
            static::KEY_CHEAPEST_SHIPPING       => $firstDeclinationData['cheapest_shipping'],
            'declination_count'                 => \count($declinations),
            // TODO later: remove compatibility with old read model that may not have 'brand'
            static::KEY_BRAND                   => $firstDeclinationData['brand'] ?? '',
            static::KEY_AFFILIATE_LINK          => $firstDeclinationData['affiliate_link'],
            static::KEY_BEST_SELLERS_SCORE      => $product->getBestSellersScore(),
            'slug'                              => $product->getSlug(),
            'category_slug_path'                => $product->getCategorySlugPath(),
        ];
        if ($firstDeclinationData['crossed_out_price']) {
            $data[static::KEY_CROSSED_OUT_PRICE] = $firstDeclinationData['crossed_out_price'];
        }

        $data[static::KEY_TAGS] = array_map(function (CompanySummary $company) {
            return static::NAMESPACE_VENDOR . $company->getId();
        }, $product->getCompanies());

        $data[static::KEY_VENDOR_TYPE] = array_unique(array_map(function (CompanySummary $company) {
            return $company->isProfessional() ? __('w_company_type_filter_V') : __('w_company_type_filter_C');
        }, $product->getCompanies()));

        $data[static::KEY_CONDITION] = array_unique(array_filter(array_map(
            function (ProductCondition $productCondition) {
                return $productCondition ? __("w_product_condition_{$productCondition->getValue()}") : null;
            },
            $product->getConditions()
        )));

        $data['shippings'] = $product->getShippings();


        //BC
        /** @var CompanySummary $company */
        $company = reset($product->getCompanies());
        $data['vendor'] = $company ? $company->getName() : '';

        return new self($data, \Wizacha\Registry::defaultInstance());
    }

    /**
     * {@inheritdoc}
     */
    public function indices(\Wizacha\Registry $registry): array
    {
        $contentLocale = GlobalState::contentLocale();
        $indices = [];

        foreach (array_keys(Languages::getAvailable()) as $locale) {
            GlobalState::switchContentTo(new Locale($locale));
            $indices[] = $this->index($registry);
        }

        GlobalState::switchContentTo($contentLocale);

        return $indices;
    }

    public function getProductId(): int
    {
        return (int) $this->data()['product_id'];
    }

    public function getName(): string
    {
        return $this->data()['name'];
    }

    public function getSupplierReference(): string
    {
        return $this->data()['supplier_reference'];
    }

    public function getSubtitle(): string
    {
        return $this->data()['description'];
    }

    public function getMinimumPrice(): Money
    {
        return Money::fromVariable($this->data()['price']);
    }

    public function getCrossedOutPrice(): ?Money
    {
        $price = $this->data()['crossed_out_price'] ?? null;

        return $price ? Money::fromVariable($price) : null;
    }

    public function isAvailable(): bool
    {
        return (bool) $this->data()['has_stock'];
    }

    /**
     * @return int|null
     */
    public function getAverageRating()
    {
        return $this->data()['average_rating'];
    }

    public function getUrl(): string
    {
        return $this->data()['url'];
    }

    public function getCreatedAt(): DateTimeInterface
    {
        return DateTimeImmutable::createFromFormat('U', $this->data()['timestamp']);
    }

    public function getUpdatedAt(): DateTimeInterface
    {
        return DateTimeImmutable::createFromFormat('U', $this->data()['update_timestamp']);
    }

    public function getDeclinationCount(): int
    {
        return $this->data()['declination_count'];
    }

    /**
     * @return string|null
     */
    public function getAffiliateLink()
    {
        return $this->data()['affiliate_link'];
    }

    public function getMainImage(): ?Image
    {
        return $this->data()['main_image'];
    }

    public function getSlug(): string
    {
        return $this->data()['slug'] ?? '';
    }

    public function getCategorySlugPath(): string
    {
        return $this->data()['category_slug_path'] ?? '';
    }

    /**
     * @return CompanySummary[]
     */
    public function getCompanies(): array
    {
        return $this->data()['companiesSummaries'];
    }

    protected function getFeatures($productFeatures)
    {
        return $this->convertFeatures($productFeatures);
    }

    protected function indexKey()
    {
        return \Wizacha\Search\Config::INDEX_PRODUCTS;
    }

    protected function getRecordType(): string
    {
        return Product::class;
    }

    /**
     * Returns the list of all the variant images.
     *
     * @return string[]
     */
    private function getVariantImages(array $productFeatures): array
    {
        $images = [];
        foreach ($productFeatures as $productFeature) {
            foreach ($productFeature['variants'] ?? [] as $variant) {
                if (isset($variant['image_pair']['icon']['http_image_path'])) {
                    $images[] = [
                        'name' => $variant['variant'],
                        'url' => $variant['image_pair']['icon']['http_image_path'],
                    ];
                }
            }
            if (!empty($productFeature['subfeatures'])) {
                $images = array_merge($images, $this->getVariantImages($productFeature['subfeatures']));
            }
        }

        return $images;
    }

    private static function formatDescription($description): string
    {
        $description = strip_tags($description);
        $description = html_entity_decode($description, ENT_QUOTES);

        return mb_strcut($description, 0, static::MAX_SIZE_DESCR);
    }
}
