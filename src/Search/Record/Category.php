<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Search\Record;

use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Component\Locale\Locale;
use Tygh\Languages\Languages;

class Category extends AbstractRecord
{
    public const KEY_CHILDREN = 'children';
    public const KEY_CHILDREN_STATUS = 'status';
    public const KEY_HEIGHT = 'height';
    public const KEY_TRANSACTIONAL = 'transactional';
    public const PREFIX_PARENT_ID = 'pid_';
    public const PREFIX_STATUS    = 'status_';

    public function __construct(\Wizacha\Category $category = null)
    {
        if (!$category || !$category->getId()) {
            return;
        }

        //Get all parents ID and names
        $tags = $fullPath = [];
        foreach ($category->getCategoriesPath() as $parentCategory) {
            $fullPath[] = $parentCategory->getName();
            $tags[] = static::PREFIX_PARENT_ID . $parentCategory->getId();
        }
        $tags[] = static::PREFIX_STATUS . $category->getStatus();

        $this->data = [
            static::KEY_OBJECT_ID => $category->getId(),
            static::KEY_TAGS      => $tags,
            static::KEY_NAME      => $category->getName(),
            static::KEY_PATH      => $fullPath,
            static::KEY_DESCR     => $category->getMetaKeywords(),
            static::KEY_TRANSACTIONAL => $category->isTransactional(),
        ];

        $generateChildren = function ($category, $depth = 0) use (&$generateChildren) {
            $return = [];
            if ($depth == 1) {
                $return['id'] = $category->getId();
                $return['name'] = $category->getName();
                $return[static::KEY_TRANSACTIONAL] = $category->isTransactional();
                $return[static::KEY_CHILDREN_STATUS] = $category->getStatus();
            }

            $subcategories = $category->getSubCategoriesVendor();
            if (!$subcategories) {
                $return[static::KEY_HEIGHT] = 0;

                return $return;
            }

            $children = array_map(
                $generateChildren,
                $subcategories,
                array_fill(0, \count($subcategories), ($depth + 1))
            );

            if ($depth == 0) {
                $return[static::KEY_CHILDREN] = $children;
            }
            $return[static::KEY_HEIGHT] = max(array_column($children, static::KEY_HEIGHT)) + 1;

            return $return;
        };

        $this->data = array_merge(
            $this->data,
            array_intersect_key(
                $generateChildren($category, 0),
                [static::KEY_HEIGHT => true, static::KEY_CHILDREN => true]
            )
        );
    }

    public static function fromId($categoryId)
    {
        $category = new \Wizacha\Category($categoryId);
        if ($category->isStored()) {
            return new self($category);
        }

        return self::deletable($categoryId);
    }

    /**
     * {@inheritdoc}
     */
    public function indices(\Wizacha\Registry $registry): array
    {
        $contentLocale = GlobalState::contentLocale();
        $indices = [];

        foreach (array_keys(Languages::getAll()) as $locale) {
            GlobalState::switchContentTo(new Locale($locale));
            $indices[] = $this->index($registry);
        }

        GlobalState::switchContentTo($contentLocale);

        return $indices;
    }

    protected function indexKey()
    {
        return \Wizacha\Search\Config::INDEX_CATEGORIES;
    }

    protected function getRecordType(): string
    {
        return Category::class;
    }
}
