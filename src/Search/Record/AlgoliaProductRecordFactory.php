<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Search\Record;

use Tygh\Registry;
use Wizacha\Marketplace\Catalog\Declination\DeclinationImages;
use Wizacha\Marketplace\Catalog\Geolocation;
use Wizacha\Marketplace\Company\CompanyType;
use Wizacha\Marketplace\Catalog\AttributeValue;
use Wizacha\Marketplace\Image\Image;
use Wizacha\Search\Product\AlgoliaProductIndex;
use Wizacha\Marketplace\PIM\Attribute\AttributeType;
use Wizacha\Marketplace\Catalog\Company\CompanySummary;
use Wizacha\Marketplace\Catalog\Product\ProductService;
use Wizacha\Marketplace\Catalog\Category\CategorySummary;
use Wizacha\Marketplace\Catalog\Product\ProductCondition;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProductService;
use Wizacha\Marketplace\Product\ProductStatus;
use Wizacha\Marketplace\ReadModel\Product as ReadModelProduct;
use Wizacha\Product as PimProduct;

class AlgoliaProductRecordFactory implements ProductRecordFactory
{
    /**
     * @var MultiVendorProductService
     */
    private $multiVendorProductService;

    /**
     * @var bool
     */
    private $enableCompanyTypeFacet;

    /** @var int */
    protected $algoliaFpuMaxFp;

    protected bool $showOutOfStockProducts;

    public function __construct(
        MultiVendorProductService $multiVendorProductService,
        bool $enableCompanyTypeFacet,
        int $algoliaFpuMaxFp,
        bool $showOutOfStockProducts
    ) {
        $this->multiVendorProductService = $multiVendorProductService;
        $this->enableCompanyTypeFacet = $enableCompanyTypeFacet;
        $this->algoliaFpuMaxFp = $algoliaFpuMaxFp;
        $this->showOutOfStockProducts = $showOutOfStockProducts;
    }


    public function create(\Wizacha\Marketplace\ReadModel\Product $product): Record
    {
        $offers = \array_filter(
            $product->getOffers(),
            fn(array $row) => $row['status'] === ProductStatus::ENABLED
        );

        $companyIds = \array_map(
            fn(array $row) => $row['companyId'],
            $offers
        );

        $companySummaries = \array_filter(
            $product->getCompanies(),
            fn (CompanySummary $companySummary) => \in_array(
                $companySummary->getId(),
                $companyIds
            )
        );

        if ($product->isMultiVendorProduct() === true
            && \count($offers) > $this->algoliaFpuMaxFp
        ) {
            $offers = \array_slice($offers, 0, $this->algoliaFpuMaxFp);
        }

        $data = [
            'objectID' => $product->getId(),
            'productId' => $product->getId(),
            'code' => $product->getCode(),
            'product_template_type' => $product->getProductTemplateType(),
            'name' => $product->getName(),
            'supplierReference' => $product->getSupplierReference(),
            'price' => $product->getCurrentPrice()->getConvertedAmount(),
            'max_price_adjustment' => $product->getMaxPriceAdjustment(),
            'crossedOutPrice' => $product->getCrossedOutPrice() ? $product->getCrossedOutPrice()->getConvertedAmount() : null,
            'isAvailable' => $product->isAvailable(),
            'averageRatingFloat' => $product->getAverageRating(),
            'averageRating' => (int) round($product->getAverageRating()),
            'url' => $product->getUrl(),
            'createdAt' => $product->getCreatedAt()->getTimestamp(),
            'updatedAt' => $product->getUpdatedAt()->getTimestamp(),
            'conditions' => array_map(function (ProductCondition $condition) {
                return $condition->getValue();
            }, $product->getConditions()),
            'declinationCount' => $product->getDeclinationCount(),
            'affiliateLink' => $product->getAffiliateLink(),
            'transactionMode' => $product->getTransactionMode()->getValue(),
            'mainImage' => $product->getMainImage() !== null ? $product->getMainImage()->toArray() : null,
            'images' => array_map(
                function (array $image) {
                    return (new Image($image['id'], $image['altText']))->toArray();
                },
                $product->getImages()
            ),
            'declinationsImages' => array_map(
                function (DeclinationImages $image) {
                    return $image->toArray();
                },
                $product->getDeclinationsImages()
            ),
            'subtitle' => $product->getSubtitle(),
            'shortDescription' => $product->getShortDescription(true),
            'slug' => $product->getSlug(),
            'companies' => $companyIds,
            'companiesSummaries' => array_map(function (CompanySummary $companySummary): array {
                return $companySummary->expose();
            }, $companySummaries),
            'categoryPath' => array_map(function (CategorySummary $category) {
                return [
                    'id' => $category->getId(),
                    'name' => $category->getName(),
                    'slug' => $category->getSlug(),
                    'position' => $category->getPosition(),
                ];
            }, $product->getCategoryPath()),
            'categorySlugPath' => $product->getCategorySlugPath(),
            'attributes' => array_map(function (AttributeValue $attribute) {
                return $attribute->expose();
            }, $product->getAttributes()),
            'mainDeclinationId' => $product->getMainDeclinationId(),
            'hasStock' => 0 < $product->getInventoryAmount(),
            'offers' => $offers,
        ];

        // map to algolia geoloc labels
        $callback = function (Geolocation $geoloc) {
            return [
                'lat' => $geoloc->getLatitude(),
                'lng' => $geoloc->getLongitude(),
                'label' => $geoloc->getLabel(),
                'zipcode' => $geoloc->getZipcode(),
            ];
        };
        $geolocs = $product->getGeolocations();
        $data['_geoloc'] = array_values(array_map($callback, $geolocs));
        $data['hasGeoloc'] = (\count($data['_geoloc']) > 0) ? true : false;

        $data['isSubscription'] = $product->isSubscription();
        $data['isRenewable'] = $product->isRenewable();
        $data['quoteRequestsMinQuantity'] = $product->getQuoteRequestsMinQuantity();
        $data['isExclusiveToQuoteRequests'] = $product->isExclusiveToQuoteRequests();

        $id = $product->getId();
        $productOptions = [];
        if (ProductService::isProductId($id)) {
            $pimProduct = new PimProduct((int) $id);
            $productAttributes = $pimProduct->getCscartData()['product_features'] ?: [];
            $productOptions = $pimProduct->getCscartData()['options'] ?: [];
        } else {
            $productAttributes = $this->multiVendorProductService->getDetailedLegacyAttributes((string) $id, false, true);
            $productIds = $this->multiVendorProductService->getProductsIdsFromMvp($id);
            foreach ($productIds as $productId) {
                $pimProduct = new PimProduct($productId);
                if (\count($pimProduct->getCscartData()['options']) > 0) {
                    $productOptions = \array_merge($productOptions, $pimProduct->getCscartData()['options']) ;
                }
            }
        }

        // if feature.catalog.show_out_of_stock_products is false,
        // remove options and values related to inactive declination
        if (false === $this->showOutOfStockProducts) {
            $productOptions = $this->removeInactiveOptions($productOptions, $product);
        }

        $data = array_merge(
            $data,
            $this->createFacetsFromOptions($productOptions),
            $this->createFacetsFromAttributes($productAttributes),
            $this->createCategoryFacets($product->getCategoryPath()),
            $this->createCompaniesFacets($companySummaries),
            $this->createCompanyTypeFacets($product->getCompaniesType())
        );

        return new AlgoliaProduct($data);
    }

    /**
     * @param mixed[] $productOptions
     * @retrun mixed[]
     */
    public function removeInactiveOptions(array $productOptions, ReadModelProduct $readmodelProduct): array
    {
        $availableCombinations = [];
        // retrieve all active combinations
        foreach ($readmodelProduct->getAllDeclinations() as $declination) {
            if (true === $declination->isAvailable()) {
                foreach ($declination->getOptions() as $option) {
                    $availableCombinations[$option->getId()][] = $option->getValueId();
                }
            }
        }

        foreach ($productOptions as $option) {
            // remove the option if no active combination
            if (false === \array_key_exists($option['option_id'], $availableCombinations)) {
                unset($productOptions[$option['option_id']]);
                continue;
            }

            foreach ($option['variants'] as $variant) {
                // remove the option value if no active combination
                if (false === \in_array($variant['variant_id'], $availableCombinations[$option['option_id']])) {
                    unset($productOptions[$option['option_id']]['variants'][$variant['variant_id']]);
                }
            }
        }

        return $productOptions;
    }

    private function createFacetsFromOptions(array $productOptions): array
    {
        return array_reduce(
            $productOptions,
            function ($return, $option) {
                if (\array_key_exists('option_id', $option) === true && !\is_null($option['variants'])) {
                    $value = array_column($option['variants'], 'variant_name');

                    $valueIds = array_column($option['variants'], 'variant_id');
                    $valuePositions = array_column($option['variants'], 'position');
                    $valueForFacet = array_map(function ($valueLabel, $valueId, $valuePosition) {
                        return str_replace(',', '%2C', implode(
                            Product::NAMESPACE_SEPARATOR,
                            [
                                Product::NAMESPACE_FACET_VALUE,
                                $valueId,
                                $valueLabel,
                                $valuePosition,
                            ]
                        ));
                    }, $value, $valueIds, $valuePositions);
                        $valueForFilter = $valueIds;
                } else {
                    $valueForFacet = Product::FEATURE_EMPTY_VALUE;
                    $valueForFilter = Product::FEATURE_EMPTY_VALUE;
                }

                $return[Product::NAMESPACE_FACET . Product::NAMESPACE_SEPARATOR . Product::NAMESPACE_OPTION_VALUE . Product::NAMESPACE_SEPARATOR . $option['option_id']] = $valueForFilter;

                $return[AlgoliaProductIndex::getNameFromLinearizedOption($option)] = $valueForFacet;

                return $return;
            },
            []
        );
    }

    private function createFacetsFromAttributes(array $productAttributes)
    {
        // Supprime les groupes d'attributs de la liste (ne garde que les attributs "feuilles")
        $productAttributes = AlgoliaProductIndex::linearizeFeatures($productAttributes);

        return array_reduce(
            $productAttributes,
            function ($return, $feature) {
                if (!empty($feature['variant_id']) && !\is_null($feature['variants'])) {
                    // La valeur de l'attribut est un ID de variante (ou plusieurs IDs)
                    $value = array_column($feature['variants'], 'variant');
                    if ($feature['feature_type'] == AttributeType::LIST_NUMBER) {
                        $valueForFacet = array_map('floatval', $value);
                        $valueForFilter = array_map('floatval', $value);
                    } else {
                        $valueIds = array_column($feature['variants'], 'variant_id');
                        $valuePositions = array_column($feature['variants'], 'position');

                        // Pour les facettes on stocke pas seulement l'ID, on stocke aussi le label et la position
                        // C'est nécessaire car pour les facettes on a besoin de tout ça pour l'affichage
                        $valueForFacet = array_map(function ($valueLabel, $valueId, $valuePosition) {
                            return str_replace(',', '%2C', implode(
                                Product::NAMESPACE_SEPARATOR,
                                [
                                    Product::NAMESPACE_FACET_VALUE,
                                    $valueId,
                                    $valueLabel,
                                    $valuePosition,
                                ]
                            ));
                        }, $value, $valueIds, $valuePositions);
                        // A l'inverse pour les filtre on a besoin d'indexer que l'ID (car quand on filtre on passe que l'ID,
                        // pas le label ni la position)
                        $valueForFilter = $valueIds;
                    }
                } elseif (!empty($feature['value'])) {
                    // La valeur de l'attribut est une valeur textuelle
                    $valueForFacet = $feature['value'];
                    $valueForFilter = $feature['value'];
                } elseif (isset($feature['value_int'])) {
                    // La valeur de l'attribut est un nombre
                    $valueForFacet = \floatval($feature['value_int']);
                    $valueForFilter = \floatval($feature['value_int']);
                } else {
                    // Pas de variante sélectionnée pour cette attribut
                    $valueForFacet = Product::FEATURE_EMPTY_VALUE;
                    $valueForFilter = Product::FEATURE_EMPTY_VALUE;
                }

                // Facette contenant uniquement l'ID de l'attribut, utilisée pour filtrer (le filtrage est fait via les IDs d'attributs)
                // par exemple : f##9
                $return[Product::NAMESPACE_FACET . Product::NAMESPACE_SEPARATOR . $feature['feature_id']] = $valueForFilter;

                // Facette contenant l'ID de l'attribut + nom + position, utilisée pour afficher les facettes dans la recherche
                // par exemple : f##9##Designer##1
                $return[AlgoliaProductIndex::getNameFromLinearizedFeature($feature)] = $valueForFacet;

                return $return;
            },
            []
        );
    }

    /**
     * @param CategorySummary[] $categories
     */
    private function createCategoryFacets(array $categories): array
    {
        $tmp = [];

        $categoriesAlgoliaBaseName = str_replace(',', '%2C', implode(
            Product::NAMESPACE_SEPARATOR,
            [
                Product::NAMESPACE_FACET,
                'categories',
            ]
        ));

        $categoriesAlgoliaName = str_replace(',', '%2C', implode(
            Product::NAMESPACE_SEPARATOR,
            [
                Product::NAMESPACE_FACET,
                'categories',
                __('categories'),
                0,
            ]
        ));

        $tmp[$categoriesAlgoliaBaseName] = array_map(function (CategorySummary $category) {
            return $category->getId();
        }, $categories);

        $tmp[$categoriesAlgoliaName] = array_map(function (CategorySummary $category) {
            return str_replace(',', '%2C', implode(
                Product::NAMESPACE_SEPARATOR,
                [
                    Product::NAMESPACE_FACET_VALUE,
                    $category->getId(),
                    $category->getName(),
                    $category->getPosition(),
                ]
            ));
        }, $categories);

        return $tmp;
    }

    /**
     * @param CompanySummary[] $companiesSummaries
     */
    private function createCompaniesFacets(array $companiesSummaries): array
    {
        $tmp = [];

        $companiesAlgoliaName = str_replace(',', '%2C', implode(
            Product::NAMESPACE_SEPARATOR,
            [
                Product::NAMESPACE_FACET,
                'companies',
                __('companies'),
                0,
            ]
        ));

        $tmp[$companiesAlgoliaName] = array_map(function (CompanySummary $company) {
            return str_replace(',', '%2C', implode(
                Product::NAMESPACE_SEPARATOR,
                [
                    Product::NAMESPACE_FACET_VALUE,
                    $company->getId(),
                    $company->getName(),
                    0,
                ]
            ));
        }, $companiesSummaries);

        return $tmp;
    }

    /**
     * @param CompanyType[] $types
     */
    private function createCompanyTypeFacets(array $types): array
    {
        if (!$this->enableCompanyTypeFacet) {
            return [];
        }

        $types = array_map(function (CompanyType $type) {
            return $type->getValue();
        }, $types);

        $tmp = [];

        $companyTypeAlgoliaBaseName = str_replace(',', '%2C', implode(
            Product::NAMESPACE_SEPARATOR,
            [
                Product::NAMESPACE_FACET,
                'companyType',
            ]
        ));

        $companyTypeAlgoliaName = str_replace(',', '%2C', implode(
            Product::NAMESPACE_SEPARATOR,
            [
                Product::NAMESPACE_FACET,
                'companyType',
                __('w_company_type'),
                0,
            ]
        ));

        $tmp[$companyTypeAlgoliaBaseName] = $types;

        $tmp[$companyTypeAlgoliaName] = array_map(function ($type) {
            return str_replace(',', '%2C', implode(
                Product::NAMESPACE_SEPARATOR,
                [
                    Product::NAMESPACE_FACET_VALUE,
                    $type,
                    __('w_type_product_' . $type),
                    0,
                ]
            ));
        }, $types);

        return $tmp;
    }
}
