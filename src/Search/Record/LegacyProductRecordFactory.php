<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Search\Record;

/**
 * @deprecated
 * @see \Wizacha\Search\Record\AlgoliaProductRecordFactory
 */
class LegacyProductRecordFactory implements ProductRecordFactory
{
    public function create(\Wizacha\Marketplace\ReadModel\Product $product): Record
    {
        return Product::create($product);
    }
}
