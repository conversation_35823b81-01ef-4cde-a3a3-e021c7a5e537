<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Search\Record;

interface Record
{
    public const KEY_OBJECT_ID     = 'objectID';
    public const KEY_NAME          = 'name';
    public const KEY_PATH          = 'path';
    public const KEY_IMAGE_URL     = 'image_url';
    public const KEY_URL           = 'url';
    public const KEY_DESCR         = 'description';
    public const KEY_TAGS              = '_tags';
    public const NAMESPACE_FACET       = 'f';
    public const NAMESPACE_FACET_VALUE = 'fv';
    public const NAMESPACE_OPTION_VALUE = 'options';
    public const NAMESPACE_SEPARATOR   = '##';

    public const SIZE_IMAGE_X      = 50;
    public const SIZE_IMAGE_Y      = 50;

    /**
     * Creates a deletable instance from an id
     * @param integer $objectId
     * @return Record
     */
    public static function deletable($objectId);

    /**
     * Returns the identifier of the index where to insert this record
     * @param \Wizacha\Registry $registry
     * @return string
     */
    public function index(\Wizacha\Registry $registry);

    /**
     * Retourne tous les noms d'index où le Record peut se trouver
     *
     * C'est utilisé lors du delete du record pour pouvoir supprimer par exemple
     * un produit dans tous les différents indexs de langue
     *
     * @param \Wizacha\Registry $registry
     *
     * @return string[]
     */
    public function indices(\Wizacha\Registry $registry): array;

    /**
     * Returns the id of the record, or null if invalid
     * @return integer
     */
    public function id();

    /**
     * Returns the data to store in the index as an associative array.
     * @return array
     */
    public function data();

    /**
     * Returns true if this record should be deleted from search engine
     * @return bool
     */
    public function isDeletable();

    /**
     * Return true if record has changed from last update.
     * @return bool
     */
    public function hasChanged();

    /**
     * Update record reference
     * @return null
     */
    public function updateHash();
}
