<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Search\Record;

use Wizacha\Core\Hash\Hash;
use Wizacha\Search\Config;

abstract class AbstractRecord implements Record
{
    protected $data  = [];

    public static function deletable($objectId)
    {
        $deletable = new static();
        $deletable->data[self::KEY_OBJECT_ID] = $objectId;

        return $deletable;
    }

    public function index(\Wizacha\Registry $registry)
    {
        return container()
            ->get('marketplace.search_engine')
            ->getIndexKey(
                $registry->get(
                    [
                        'config',
                        Config::CFG_SEARCH_ENGINE,
                        $this->indexKey(),
                    ]
                )
            )
        ;
    }

    /**
     * {@inheritdoc}
     */
    public function indices(\Wizacha\Registry $registry): array
    {
        //par défaut on retourne que l'index historique pour assurer la retrocompatibilité
        return [$this->index($registry)];
    }

    public function id()
    {
        return isset($this->data[self::KEY_OBJECT_ID]) ? $this->data[self::KEY_OBJECT_ID] : null;
    }

    public function data()
    {
        if (!empty($this->data[self::KEY_OBJECT_ID])) {
            return $this->data;
        }

        return [];
    }

    public function isDeletable()
    {
        return \count($this->data()) == 1;
    }

    /**
     * Determine if record has changed from last update and if the update is required.
     * @return bool
     */
    public function hasChanged()
    {
        return Hash::hasChanged($this->id(), $this->data(), static::getRecordType());
    }

    /**
     * @return bool
     */
    public function updateHash()
    {
        return Hash::updateHash($this->id(), $this->data(), static::getRecordType());
    }

    /**
     * Returns the name of the configuration key for the
     * index related to this Record class
     * @return string
     */
    abstract protected function indexKey();

    protected function getRecordType(): string
    {
        return \get_called_class();
    }
}
