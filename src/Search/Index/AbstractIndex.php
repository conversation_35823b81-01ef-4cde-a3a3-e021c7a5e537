<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Search\Index;

use Wizacha\AppBundle\Exception\Search\CannotRemoveSearchRecordException;
use Wizacha\Category;
use Wizacha\Marketplace\Catalog\Product\ProductService;
use Wizacha\Marketplace\PIM\Attribute\AttributeService;
use Wizacha\Marketplace\PIM\Category\CategoryService;
use Wizacha\Marketplace\PIM\Option\OptionService;
use Wizacha\Registry;
use Wizacha\Search\Config;
use Wizacha\Search\Engine\SearchEngine;
use Wizacha\Search\Product\SearchableInterface;
use Wizacha\Search\Record\Product;
use Wizacha\Search\Record\ProductRecordFactory;
use Wizacha\Search\Record\Record;
use Wizacha\Status;

abstract class AbstractIndex implements SearchIndex, SearchableInterface
{
    public const AUTOCOMPLETE_MAX_RESULTS = 5;

    public const AUTOCOMPLETE_MIN_LENGTH = 2;

    public const FACET_OPTION_PREFIX = 'opt';

    /**
     * @var ProductService
     */
    protected $productService;

    /**
     * @var SearchEngine
     */
    protected $searchEngine;

    /**
     * @var Registry
     */
    protected $registry;

    /**
     * @var AttributeService
     */
    protected $attributeService;

    /** @var OptionService */
    protected $optionService;

    /**
     * @var ProductRecordFactory
     */
    protected $productRecordFactory;

    /**
     * @var CategoryService
     */
    protected $categoryService;

    /**
     * @var bool
     */
    protected $enableCompanyTypeFacet;

    /**
     * @var bool
     */
    protected $enableCompaniesFacet;

    public function __construct(
        ProductService $productService,
        SearchEngine $searchEngine,
        Registry $registry,
        AttributeService $attributeService,
        ProductRecordFactory $productRecordFactory,
        CategoryService $categoryService,
        bool $enableCompanyTypeFacet,
        bool $enableCompaniesFacet,
        OptionService $optionService
    ) {
        $this->productService = $productService;
        $this->searchEngine = $searchEngine;
        $this->registry = $registry;
        $this->attributeService = $attributeService;
        $this->productRecordFactory = $productRecordFactory;
        $this->categoryService = $categoryService;
        $this->enableCompanyTypeFacet = $enableCompanyTypeFacet;
        $this->enableCompaniesFacet = $enableCompaniesFacet;
        $this->optionService = $optionService;
    }

    public function getName()
    {
        return $this
            ->searchEngine
            ->getIndexKey(
                $this->registry->get(
                    [
                        'config',
                        Config::CFG_SEARCH_ENGINE,
                        Config::INDEX_PRODUCTS,
                    ]
                )
            )
        ;
    }

    public function updateAll()
    {
        $this->update($this->productService->getSearcheableProductIdInFront());
    }

    public function update(\Traversable $productIds)
    {
        if ($this->chunkSize() <= 0) {
            return;
        }

        foreach (\iter\chunk($productIds, $this->chunkSize()) as $chunk) {
            $this->updateByIds($chunk);
        }
    }

    /**
     * Return product facets to filter by category.
     * @param Category $category
     * @return array
     */
    public function getFacetsFromCategory(Category $category)
    {
        $facets = [];
        $categoryLevel = 1;
        $categories = $category->getCategoriesPath();

        while (($cat = array_shift($categories)) && $categoryLevel < 4) {
            $constant = \constant(Product::class . '::KEY_CATEGORY_' . $categoryLevel);
            $facets[$constant] = $cat->getName();
            ++$categoryLevel;
        }

        //Use last category has main category.
        $facets[Product::KEY_CATEGORY_MAIN] = array_pop($category->getCategoriesPath())->getName();

        return $facets;
    }

    /**
     * Returns the index configuration.
     *
     * @return array [
     *                   Config::PARAMS_TO_INDEX => [string $attribute_for_indexing, ...],
     *                   Config::PARAMS_FACET => [string $attribute_for_facet, ...]
     *               ]
     */
    public function getConfig()
    {
        [$featuresIndex] = fn_get_product_features([
            'is_searchable' => 'Y',
            'statuses' => [Status::ENABLED],
        ]);
        if (!\is_array($featuresIndex)) {
            $featuresIndex = [];
        }
        $featuresIndex = self::linearizeFeatures($featuresIndex);
        $featuresIndex = array_map(function ($feature) {
            return self::getNameFromLinearizedFeature($feature);
        }, $featuresIndex);

        [$featuresFaceting] = fn_get_product_features([
            'display_on' => 'faceting',
            'statuses' => [Status::ENABLED],
        ]);
        if (!\is_array($featuresFaceting)) {
            $featuresFaceting = [];
        }
        $featuresFaceting = self::linearizeFeatures($featuresFaceting);
        $featuresFaceting = array_map(function ($feature) {
            return self::getNameFromLinearizedFeature($feature);
        }, $featuresFaceting);

        return [
            Config::PARAMS_TO_INDEX => array_merge(
                [
                    Product::KEY_NAME,
                    Product::KEY_DESCR,
                    Product::KEY_VENDOR,
                    Product::KEY_CONDITION,
                    Product::KEY_FREE_FEATURES,
                ],
                $featuresIndex
            ),
            /**
             * @deprecated Will be replaced by getFacetNames()
             * @see AbstractIndex::getFacetNames()
             */
            Config::PARAMS_FACET => array_merge(
                [
                    Product::KEY_PRICE,
                    Product::KEY_CONDITION,
                    Product::KEY_VENDOR_TYPE,
                    Product::KEY_CATEGORY_1,
                    Product::KEY_CATEGORY_2,
                    Product::KEY_CATEGORY_3,
                    Product::KEY_CATEGORY_MAIN,
                ],
                $featuresFaceting
            ),
        ];
    }

    /**
     * @param array $productFeatures first level Features second level sub features
     * @return array one level with ungrouped features and sub_features
     */
    public static function linearizeFeatures(array $productFeatures)
    {
        return array_reduce(
            $productFeatures,
            function (&$result, $feature) {
                if (!empty($feature['subfeatures']) && \is_array($feature['subfeatures'])) {
                    $result = array_merge($result, $feature['subfeatures']);
                } else {
                    $result[] = $feature;
                }

                return $result;
            },
            []
        );
    }

    /**
     * An standardizeFeature is required before getStandardFeatureName
     */
    public static function getNameFromLinearizedFeature(array $feature): string
    {
        if (!isset($feature['position'], $feature['description'])) {
            return '';
        }

        //str_replace is a workaround for the algolia bug
        return str_replace(',', '%2C', implode(
            Product::NAMESPACE_SEPARATOR,
            [
                Product::NAMESPACE_FACET,
                $feature['position'],
                $feature['description'],
            ]
        ));
    }

    public function updateIndexParams()
    {
        $this->searchEngine->setIndexParams($this->getName(), $this->getConfig());
    }

    protected function updateByIds(array $productIds)
    {
        $records = [];
        foreach ($productIds as $productId) {
            // Non-existent product: remove all records
            if (!$this->productService->isProductSearchableInFront($productId)) {
                $this->removeRecord($productId);
                continue;
            }

            $product = $this->productService->getProduct($productId);

            if ($product === null) {
                // The product has been deleted since the creation of the async job
                // Skip it silently
                continue;
            }

            $record = $this->productRecordFactory->create($product);

            if ($record->hasChanged()) {
                $records[] = $record;
            }
        }

        if (empty($records)) {
            return;
        }

        // Push in search engine
        $this->searchEngine->pushMultipleRecords($records);

        array_map(function (Record $record) {
            $record->updateHash();
        }, $records);
    }

    /**
     * @return array with key 0 = filters and key 1 = numericFilters
     */
    protected function extractFiltersFromQuery(array $_filters): array
    {
        // Split filters and numeric filters in two arrays
        $filters = $numericFilters = [];
        foreach ($_filters as $name => $filter) {
            if (isset($filter['min']) || isset($filter['max'])) {
                $numericFilters[$name] =  $filter;
            } elseif ($name === 'offers') {
                $filters['offers.divisions'] = $filter;
            } else {
                $filters[$name] =  $filter;
            }
        }

        return [$filters, $numericFilters];
    }

    /**
     * Returns the facets that are not dynamically built on product attributes.
     */
    protected function getFacetNames(): array
    {
        $attributeFacets = $this->attributeService->getAttributesForFaceting();
        $attributeFacets = array_map([__CLASS__, 'getNameFromLinearizedFeature'], $attributeFacets);

        // Add facets that are not built on attributes
        $attributeFacets[] = 'categories';
        $attributeFacets[] = 'price';
        $attributeFacets[] = 'companies';

        return $attributeFacets;
    }

    private function chunkSize()
    {
        return min(20, $this->searchEngine->chunkSize());
    }

    private function removeRecord($productId)
    {
        $record = Product::deletable($productId);

        try {
            $this->searchEngine->deleteRecord($record);
        } catch (\Exception $e) {
            throw new CannotRemoveSearchRecordException($productId, $e);
        }

        $record->updateHash();
    }
}
