<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Search\Index;

use Wizacha\Async\Dispatcher;
use Wizacha\Registry;
use Wizacha\Search\Config;
use Wizacha\Search\Engine\SearchEngine;
use Wizacha\Search\Record\Category;

class CategoryIndex implements SearchIndex
{
    /**
     * @var SearchEngine
     */
    private $searchEngine;

    /**
     * @var Dispatcher
     */
    private $jobDispatcher;

    /**
     * @var Registry
     */
    private $registry;

    public function __construct(SearchEngine $searchEngine, Dispatcher $jobDispatcher, Registry $registry)
    {
        $this->searchEngine = $searchEngine;
        $this->jobDispatcher = $jobDispatcher;
        $this->registry = $registry;
    }

    public function getName()
    {
        return $this
            ->searchEngine
            ->getIndexKey(
                $this->registry->get(
                    [
                        'config',
                        Config::CFG_SEARCH_ENGINE,
                        Config::INDEX_CATEGORIES,
                    ]
                )
            )
        ;
    }

    public function update(\Traversable $categoryIds)
    {
        if ($this->chunkSize() <= 0) {
            return;
        }

        foreach (\iter\chunk($categoryIds, $this->chunkSize()) as $chunk) {
            $this->updateByIds($chunk);
        }
    }

    /**
     * Public method so that the job dispatcher can call it.
     * @internal
     */
    public function updateByIds(array $categoryIds)
    {
        if ($this->jobDispatcher->delayExec('marketplace.search.category_index::' . __FUNCTION__, [$categoryIds])) {
            return;
        }

        // Create a new record instance for each data
        $records = [];
        foreach ($categoryIds as $id) {
            $record = Category::fromId($id);

            if (!$record->hasChanged()) {
                continue;
            }

            if ($record->isDeletable()) {
                if (!$this->searchEngine->deleteRecord($record)) {
                    throw new \Exception("Unable to delete record $id in search engine [category]");
                }
                continue;
            }

            $records[] = $record;
        }

        // Push in search engine
        $this->searchEngine->pushMultipleRecords($records);

        array_map(function (Category $record) {
            $record->updateHash();
        }, $records);
    }

    private function chunkSize()
    {
        return min(1000, $this->searchEngine->chunkSize());
    }
}
