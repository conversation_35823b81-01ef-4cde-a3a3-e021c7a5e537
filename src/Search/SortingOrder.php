<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Search;

use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;

/**
 * List of sorting available for products.
 */
class SortingOrder
{
    /** @var string */
    private $criteria;

    /** @var SortDirection */
    private $direction;

    private function __construct(string $criteria, SortDirection $direction)
    {
        $this->criteria = $criteria;
        $this->direction = $direction;
    }

    public static function fromString(string $criteria, string $direction): ?self
    {
        if ($criteria === null || $direction === null) {
            return null;
        }

        return new self($criteria, new SortDirection($direction));
    }

    public function getCriteria(): string
    {
        return $this->criteria;
    }

    public function getDirection(): SortDirection
    {
        return $this->direction;
    }

    public static function parseSort(?string $sort): array
    {
        if (\is_string($sort) === false) {
            return [];
        }

        return array_map(
            function (string $sort) {
                $sortInformation = explode(':', $sort);
                $sortCriteria = $sortInformation[0];

                if (\is_string($sortCriteria) === false) {
                    return BadRequestJsonResponse::invalidField('sorting', 'Sort criteria must be a string');
                }

                return SortingOrder::fromString($sortCriteria, $sortInformation[1] ?? 'asc');
            },
            explode(',', $sort)
        );
    }
}
