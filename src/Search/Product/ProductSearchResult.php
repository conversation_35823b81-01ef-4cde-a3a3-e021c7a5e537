<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Search\Product;

use Wizacha\Marketplace\Catalog\Product\ProductSummary;
use Wizacha\Search\Pagination;

/**
 * Represents the data and metadata resulting of a product search.
 */
class ProductSearchResult
{
    /**
     * @var array
     */
    private $products;

    /**
     * @var array
     */
    private $facets;

    /**
     * @var Pagination
     */
    private $pagination;

    /**
     * ProductSearchResult constructor.
     * @param array $products
     * @param Pagination $pagination
     * @param array $facets
     */
    public function __construct(array $products, Pagination $pagination, array $facets)
    {
        $this->products = $products;
        $this->facets = $facets;
        $this->pagination = $pagination;
    }

    /**
     * @return ProductSummary[]
     */
    public function getProducts(): array
    {
        return $this->products;
    }

    public function getTotalProductCount(): int
    {
        return (null !== $this->pagination->getTotalResultCount() ? $this->pagination->getTotalResultCount() : 0);
    }

    /**
     * @return array Key: facet name, Value: array with key=facetValue, value=count
     */
    public function getFacets(): array
    {
        return $this->facets;
    }

    public function getPagination(): Pagination
    {
        return $this->pagination;
    }
}
