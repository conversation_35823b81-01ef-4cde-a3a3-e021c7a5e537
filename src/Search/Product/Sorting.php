<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Search\Product;

use Wizacha\Search\SortDirection;

/**
 * List of sorting available for products.
 */
class Sorting
{
    public const PRICE = 'price';
    public const CREATION_DATE = 'createdAt';
    public const NAME = 'name';

    public const CRITERIAS = [
        self::PRICE,
        self::CREATION_DATE,
        self::NAME,
    ];

    /**
     * @var string
     */
    private $criteria;

    /**
     * @var SortDirection
     */
    private $direction;

    private function __construct(string $criteria, SortDirection $direction)
    {
        if (!\in_array($criteria, self::CRITERIAS)) {
            $criteria = self::PRICE;
        }

        $this->criteria = $criteria;
        $this->direction = $direction;
    }

    public static function fromString(string $criteria, string $direction): self
    {
        return new self($criteria, new SortDirection($direction));
    }

    public function getCriteria(): string
    {
        return $this->criteria;
    }

    public function getDirection(): SortDirection
    {
        return $this->direction;
    }

    public static function price(string $direction): self
    {
        return new self(self::PRICE, new SortDirection($direction));
    }

    public static function creationDate(string $direction): self
    {
        return new self(self::CREATION_DATE, new SortDirection($direction));
    }

    public static function name(string $direction): self
    {
        return new self(self::NAME, new SortDirection($direction));
    }
}
