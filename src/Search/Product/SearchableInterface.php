<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Search\Product;

use Wizacha\Search\GeoFilter;
use Wizacha\Search\Pagination;

interface SearchableInterface
{
    /**
     * @param string $query
     * @param Pagination|null $pagination
     * @param array $filters Key-value map.
     * @param Sorting|null $sorting
     * @param GeoFilter|null $geoFilter
     * @param array $extra Extra parameters for implementation-specific behaviors.
     * @return ProductSearchResult
     */
    public function search(
        string $query,
        Pagination $pagination = null,
        array $filters = [],
        Sorting $sorting = null,
        GeoFilter $geoFilter = null,
        array $extra = []
    ): ProductSearchResult;

    public function autocomplete(string $query, array $filters = []): array;
}
