<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Search\Product;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Driver\Statement;
use Doctrine\DBAL\Query\QueryBuilder;
use Wizacha\Marketplace\Catalog\Product\ProductSummary;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct;
use Wizacha\Search\GeoFilter;
use Wizacha\Search\Index\AbstractIndex;
use Wizacha\Search\Pagination;

class SqlProductIndex extends AbstractIndex
{
    /**
     * @var Connection
     */
    private $db;

    public function setConnection(Connection $db): void
    {
        $this->db = $db;
    }

    public function search(
        string $query,
        Pagination $pagination = null,
        array $filters = [],
        Sorting $sorting = null,
        GeoFilter $geoFilter = null,
        array $extra = []
    ): ProductSearchResult {
        $pagination = $pagination ?: new Pagination();

        $qb = $this->baseQueryBuilder();
        $qb->where('p.status = "A"')// keep active products
            ->andWhere('p.approved = "Y"') // keep approved products
            ->setFirstResult($pagination->getOffset())
            ->setMaxResults(1000); // Just to avoid fetching too many results

        if ($query) {
            $qb->innerJoin('p', 'cscart_product_descriptions', 'pd', 'pd.product_id = p.product_id');
            $qb->leftJoin(
                'mvp',
                'doctrine_multi_vendor_product_translations',
                'trans',
                'mvp.id = trans.multi_vendor_product_id and trans.locale = ' . $qb->createNamedParameter((string) GlobalState::contentLocale())
            );
            $qb->andWhere('(product LIKE ' . $qb->createNamedParameter('%' . $query . '%') . ' OR trans.name LIKE ' . $qb->createNamedParameter('%' . $query . '%') . ')');
        }

        foreach ($filters as $filter => $value) {
            if ($value === "true") {
                $value = 1;
            } elseif ($value === "false") {
                $value = 0;
            }
            if ($filter === 'companies') {
                $value = (array) $value; // force an array of companies IDs
                $qb->andWhere('company_id IN (' . $qb->createNamedParameter($value, Connection::PARAM_INT_ARRAY) . ')');
            } elseif ($filter === 'categories') {
                $value = (array) $value; // force an array of category IDs
                $qb->innerJoin('p', 'cscart_products_categories', 'categories', 'categories.product_id = p.product_id');
                $qb->andWhere('categories.category_id IN (' . $qb->createNamedParameter($value, Connection::PARAM_INT_ARRAY) . ')');
            } elseif ($filter === 'price' && \is_array($value)) {
                $this->addPriceFilter($value, $qb);
            } elseif ($filter === 'code') {
                $qb->andWhere('product_code LIKE ' . $qb->createNamedParameter($value . '%'));
            } elseif ($filter === 'isSubscription') {
                $qb->andWhere('is_subscription = :is_subscription')
                    ->setParameter('is_subscription', $value);
            } elseif ($filter === 'offers') {
                $qb
                    ->innerJoin('p', 'doctrine_division_products', 'divisions', 'divisions.product_id = p.product_id')
                    ->andWhere('division_code = :division_code')
                    ->setParameter('division_code', $value)
                ;
            } else {
                $value = (array) $value; // force an array of values
                // The table alias contains the attribute ID because we can join multiple times with this
                // table (as many times as we have attribute filters)
                $alias = 'attribute_' . $filter;
                $qb->innerJoin('p', 'cscart_product_features_values', $alias, $alias . '.product_id = CAST(p.product_id AS CHAR)');
                $qb->andWhere($alias . '.feature_id = ' . $qb->createNamedParameter($filter));
                $qb->andWhere($alias . '.variant_id IN (' . $qb->createNamedParameter($value, Connection::PARAM_INT_ARRAY) . ')');
            }
        }

        /** @var Statement */
        $statement = $qb->execute();
        $productIds = $this->filterResultsForValidProducts($statement, $pagination->getResultsPerPage());
        $products = array_filter(array_values(array_map([$this->productService, 'getProduct'], $productIds)));

        if ($sorting) {
            $products = $this->sortResults($products, $sorting);
        }

        // Total number of products
        $pagination->setTotalResultCount($pagination->getOffset() + $statement->rowCount());

        $facets = empty($extra['noFacets']) ? $this->createFacets($products, $filters) : [];

        return new ProductSearchResult($products, $pagination, $facets);
    }

    public function autocomplete(string $query, array $filters = []): array
    {
        $qb = $this->baseQueryBuilder();
        $qb
            ->innerJoin(
                'p',
                'cscart_product_descriptions',
                'pd',
                'pd.product_id = p.product_id and pd.lang_code = ' . $qb->createNamedParameter((string) GlobalState::contentLocale())
            )
            ->andWhere('product LIKE ' . $qb->createNamedParameter('%' . $query . '%'));

        $productIds = $this->filterResultsForValidProducts($qb->execute(), self::AUTOCOMPLETE_MAX_RESULTS);

        $products = array_map([$this->productService, 'getProduct'], $productIds);

        return array_map(function (ProductSummary $product) {
            return [
                'name' => $product->getName(),
            ];
        }, $products);
    }

    private function baseQueryBuilder(): QueryBuilder
    {
        $qb = $this->db->createQueryBuilder();
        $statuses = array_map([$qb, 'createNamedParameter'], MultiVendorProduct::SEARCHABLE_STATUSES);
        $qb->select('IFNULL(mvp.id, p.product_id) AS catalog_id')
            ->from('cscart_products', 'p')
            ->leftJoin('p', 'doctrine_multi_vendor_product_link', 'link', 'p.product_id = link.product_id')
            ->leftJoin(
                'link',
                'doctrine_multi_vendor_product',
                'mvp',
                'link.multi_vendor_product_id = mvp.id AND ' . $qb->expr()->in('mvp.status', $statuses)
            )
            ->groupBy('catalog_id');

        return $qb;
    }

    private function filterResultsForValidProducts(Statement $statement, int $maxResults)
    {
        $productIds = [];

        // Keep fetching new product IDs to keep only the ones visible in front (until we are full)
        while (($productId = $statement->fetchColumn()) && (\count($productIds) < $maxResults)) {
            if ($this->productService->isProductSearchableInFront($productId)) {
                $productIds[] = $productId;
            }
        }

        return $productIds;
    }

    private function addPriceFilter(array $value, QueryBuilder $qb)
    {
        $exprProduct = $exprDeclinations = '';
        if (!empty($value['min'])) {
            $exprProduct .= ' AND pp.price >= ' . $qb->createNamedParameter($value['min']);
            $exprDeclinations .= ' AND poi.w_price >= ' . $qb->createNamedParameter($value['min']);
        }
        if (!empty($value['max'])) {
            $exprProduct .= ' AND pp.price <= ' . $qb->createNamedParameter($value['max']);
            $exprDeclinations .= ' AND poi.w_price <= ' . $qb->createNamedParameter($value['max']);
        }

        $qb->leftJoin(
            'p',
            'cscart_product_prices',
            'pp',
            'pp.product_id = p.product_id ' . $exprProduct
        );
        $qb->leftJoin(
            'p',
            'cscart_product_options_inventory',
            'poi',
            'poi.product_id = p.product_id ' . $exprDeclinations
        );
        $qb->andWhere('pp.price IS NOT NULL OR poi.w_price IS NOT NULL');
        $qb->groupBy('p.product_id');
    }

    /**
     * @param ProductSummary[] $products
     */
    private function createFacets(array $products, array $filters): array
    {
        $facets = [];
        $facets = $this->categoryFacet($products, $facets, $filters);
        $facets = $this->priceFacet($products, $facets, $filters);
        if ($this->enableCompanyTypeFacet) {
            $facets = $this->companyTypeFacet($products, $facets, $filters);
        }
        if ($this->enableCompaniesFacet) {
            $facets = $this->companiesFacet($products, $facets, $filters);
        }

        // Attributes
        $attributes = $this->attributeService->getAttributesForFaceting();
        usort($attributes, function ($a, $b) {
            return $a['position'] <=> $b['position'];
        });
        foreach ($attributes as $attribute) {
            $attributeId = (int) $attribute['feature_id'];
            $variants = $this->attributeService->getAttributeVariants($attributeId);
            $values = [];
            $position = 0;
            foreach ($variants as $variant) {
                $values[$variant['variant_id']] = [
                    'label' => $variant['variant'],
                    'count' => 0, // not implemented
                    'position' => $position++,
                ];
            }
            $facets[] = [
                'name' => $attributeId,
                'label' => $attribute['description'],
                'values' => $values,
                'isNumeric' => false,
            ];
        }

        return $facets;
    }

    /**
     * @return array Sorted records.
     */
    private function sortResults(array $products, Sorting $sorting): array
    {
        usort($products, function (ProductSummary $product1, ProductSummary $product2) use ($sorting) {
            switch ($sorting->getCriteria()) {
                case Sorting::PRICE:
                    $method = 'getMinimumPrice';
                    break;
                case Sorting::CREATION_DATE:
                    $method = 'getCreatedAt';
                    break;
                case Sorting::NAME:
                    $method = 'getName';
                    break;
                default:
                    return 0; // Unknown criteria
            }
            if ($sorting->getDirection()->isAsc()) {
                return $product1->$method() <=> $product2->$method();
            }

            return $product2->$method() <=> $product1->$method();
        });

        return $products;
    }

    private function priceFacet(array $products, array $facets, array $filters): array
    {
        $prices = array_map(function (ProductSummary $product) {
            return $product->getMinimumPrice()->getConvertedAmount();
        }, $products);

        // We force filters to always appear in facets
        if (isset($filters['price']['min'])) {
            $prices[] = (float) $filters['price']['min'];
        }
        if (isset($filters['price']['max'])) {
            $prices[] = (float) $filters['price']['max'];
        }

        $prices = array_unique($prices);

        // We want to avoid min == max
        if (\count($prices) > 1) {
            $facets[] = [
                'name' => 'price',
                'label' => __('price'),
                'values' => [
                    'min' => min($prices),
                    'max' => max($prices),
                ],
                'isNumeric' => true,
            ];
        }

        return $facets;
    }

    private function categoryFacet(array $products, array $facets, array $filters): array
    {
        $productIds = array_map(function (ProductSummary $product) {
            return $product->getProductId();
        }, $products);

        // We force filters to always appear in facets
        $categoryIds = [];
        if (isset($filters['categories'])) {
            $categoryIds = (array) $filters['categories'];
        }

        $rows = $this->db->fetchAll(
            'SELECT c.category_id, c.category, COUNT(pc.product_id) AS product_count, cat.position
             FROM cscart_category_descriptions AS c
             INNER JOIN cscart_categories AS cat ON cat.category_id = c.category_id
             LEFT JOIN cscart_products_categories AS pc ON pc.category_id = c.category_id
             WHERE c.lang_code = ?
                 /* We must return a facet that was present in filters */
                 AND (pc.product_id IN (?) OR c.category_id IN (?))
             GROUP BY c.category_id',
            [(string) GlobalState::contentLocale(), $productIds, $categoryIds],
            [\PDO::PARAM_STR, Connection::PARAM_INT_ARRAY, Connection::PARAM_INT_ARRAY]
        );

        $categoriesFacet = [];
        foreach ($rows as $item) {
            $categoriesFacet[$item['category_id']] = [
                'label' => $item['category'],
                'count' => $item['product_count'],
                'position' => $item['position'],
            ];
        }
        if (!empty($categoriesFacet)) {
            $facets[] = [
                'name' => 'categories',
                'label' => __('category'),
                'values' => $categoriesFacet,
                'isNumeric' => false,
            ];
        }

        return $facets;
    }

    private function companyTypeFacet(array $products, array $facets, array $filters): array
    {
        $productIds = array_map(function (ProductSummary $product) {
            return $product->getProductId();
        }, $products);

        // We force filters to always appear in facets
        $types = [];
        if (isset($filters['companyType'])) {
            $types = (array) $filters['companyType'];
        }

        $rows = $this->db->fetchAll(
            'SELECT c.w_company_type, COUNT(p.product_id) AS product_count
             FROM cscart_products AS p
             LEFT JOIN cscart_companies AS c ON c.company_id = p.company_id
             WHERE
                 /* We must return a facet that was present in filters */
                 (p.product_id IN (?) OR c.w_company_type IN (?))
             GROUP BY c.w_company_type',
            [$productIds, $types],
            [Connection::PARAM_INT_ARRAY, Connection::PARAM_STR_ARRAY]
        );

        $companyTypeFacet = [];
        $position = 0;
        foreach ($rows as $item) {
            $companyTypeFacet[$item['w_company_type']] = [
                'label' => __('w_type_product_' . $item['w_company_type']),
                'count' => $item['product_count'],
                'position' => $position++,
            ];
        }
        if (!empty($companyTypeFacet)) {
            $facets[] = [
                'name' => 'company_type',
                'label' => __('w_company_type'),
                'values' => $companyTypeFacet,
                'isNumeric' => false,
            ];
        }

        return $facets;
    }

    private function companiesFacet(array $products, array $facets, array $filters): array
    {
        $productIds = array_map(function (ProductSummary $product) {
            return $product->getProductId();
        }, $products);

        // We force filters to always appear in facets
        $companies = [];
        if (isset($filters['companies'])) {
            $companies = (array) $filters['companies'];
        }

        $rows = $this->db->fetchAll(
            'SELECT p.company_id, c.company, COUNT(p.product_id) AS product_count
             FROM cscart_products AS p
             LEFT JOIN cscart_companies AS c ON c.company_id = p.company_id
             WHERE
                 /* We must return a facet that was present in filters */
                 (p.product_id IN (?) OR p.company_id IN (?))
             GROUP BY p.company_id',
            [$productIds, $companies],
            [Connection::PARAM_INT_ARRAY, Connection::PARAM_INT_ARRAY]
        );

        $companiesFacet = [];
        $position = 0;
        foreach ($rows as $item) {
            $companiesFacet[$item['company_id']] = [
                'label' => $item['company'],
                'count' => $item['product_count'],
                'position' => $position++,
            ];
        }
        if (!empty($companiesFacet)) {
            $facets[] = [
                'name' => 'companies',
                'label' => __('companies'),
                'values' => $companiesFacet,
                'isNumeric' => false,
            ];
        }

        return $facets;
    }
}
