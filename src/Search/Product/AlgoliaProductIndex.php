<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Search\Product;

use Wizacha\Marketplace\PIM\Attribute\AttributeType;
use Wizacha\Search\Config;
use Wizacha\Search\GeoFilter;
use Wizacha\Search\Index\AbstractIndex;
use Wizacha\Search\Pagination;
use Wizacha\Search\Record\AlgoliaProduct;
use Wizacha\Search\Record\Product;
use Wizacha\Search\Record\Record;
use Wizacha\Status;

class AlgoliaProductIndex extends AbstractIndex
{
    public const NON_FACETED_FILTER = [
        'code',
        'supplierReference',
        'hasGeoloc',
        'hasStock',
        'offers.divisions',
        'offers.productId',
        'offers.companyId',
        'offers.price',
        'offers.status',
        'offers.priceTiers.excludingTaxes',
        'offers.priceTiers.includingTaxes',
        'offers.priceTiers.taxes',
        'isSubscription',
        'offers.priceTiers.lowerLimit',
    ];

    /**
     * @var array of attributes
     * @see \Wizacha\Marketplace\PIM\Attribute\AttributeService::getPublicAttributesForSearchAndFaceting
     */
    private $cacheRealFilters;

    public function search(
        string $query,
        Pagination $pagination = null,
        array $filters = [],
        Sorting $sorting = null,
        GeoFilter $geoFilter = null,
        array $extra = []
    ): ProductSearchResult {
        $pagination = $pagination ?: new Pagination();
        //check if result requested > 1000
        if ($pagination->getPage() * $pagination->getResultsPerPage() > 1000) {
            $pagination->setTotalResultCount(0);

            return new ProductSearchResult([], $pagination, []);
        }
        // Extract numeric filters
        list ($filters, $numericFilters) = $this->extractFiltersFromQuery($filters);

        // Get filters labels from their ids
        $refinements = $this->convertFiltersForSearch($filters);

        // Format numeric filters
        array_walk($numericFilters, function (&$value, $key) {
            if ($key !== 'price') {
                $key = Record::NAMESPACE_FACET . Record::NAMESPACE_SEPARATOR . $key;
            }
            // If we have both bounds
            if (isset($value['min'], $value['max'])) {
                $value = $key . ':' . $value['min'] . ' TO ' . $value['max'];
            } elseif (isset($value['min'])) {
                $value = $key . '>=' . $value['min'];
            } else { // we only have max
                $value = $key . '<=' . $value['max'];
            }
        });

        $parameters = [
            'page' => $pagination->getPage() - 1, // Algolia's first page is 0, not 1
            'hitsPerPage' => $pagination->getResultsPerPage(),
            'filters' => implode(' AND ', $numericFilters),
            'facets' => '*',
        ];

        if ($geoFilter !== null) {
            $parameters['aroundLatLng'] = $geoFilter->getLatitude() . ',' . $geoFilter->getLongitude();
            $parameters['aroundRadius'] = $geoFilter->getRadius();
        }

        $indexName = $this->getName();
        if ($sorting) {
            $indexName .= '_by_' . $sorting->getCriteria() . '_' . strtolower($sorting->getDirection()->getValue());
        }

        $disjunctiveFacets = array_keys($refinements);

        $results = $this->searchEngine->searchDisjunctiveFacets($indexName, $query, $disjunctiveFacets, $parameters, $refinements);

        if ($parameters['filters'] !== '' && !\is_null($results['facets_stats'])) {
            // This necessary when we have multiple numeric Filters, and because we send requests with facets set to '*'
            // so there is no way to know which numeric facet will be returned
            // Example : Suppose we have Price and Size as numeric filters, if we filter by price, the size filter will
            // have the same problem, but there is no way to know if we have size numeric filter in advance.
            $numericFacets = $this->numericFacetsFromAlgoliaResult($results['facets'], $results['facets_stats']);

            if (\count($numericFacets) > 0) {
                $numericFilterStats = $this->searchEngine->getNumericFilterStats($indexName, $numericFacets, $disjunctiveFacets, $refinements);

                foreach ($numericFacets as $value) {
                    // For each numeric filter, We update its stats so that we have the stats with only the refinements
                    // without taking in consideration the filter restriction
                    // FYI : Filters are numeric (numericFilter) and refinements are by values (facetFilters)
                    // Example : With a refinement 'color:white' we want all the prices available, without the numeric
                    // filter restriction (that we are replacing here)
                    $results["facets_stats"]["$value"]["max"] = $numericFilterStats["facets_stats"]["$value"]["max"];
                    $results["facets_stats"]["$value"]["min"] = $numericFilterStats["facets_stats"]["$value"]["min"];
                }
            }
        }

        // We create a new pagination with Algolia returned values to stay coherent with returned data
        $resultPagination = new Pagination(
            (isset($results['page']) && is_numeric($results['page'])) ? $results['page'] + 1 : $pagination->getPage(), // Algolia pages start with 0
            $results['hitsPerPage'] ?? $pagination->getResultsPerPage(),
            $results['nbHits'] ?? 0
        );

        //On doit renvoyer les filtres qui ont été saisis par l'utilisateur, meme si certain ne sont plus
        //valide. Ca lui permet de les décocher
        $refinementsAsFacet = array_map(function ($facet) {
            return array_combine(
                array_values($facet),
                array_fill(0, \count($facet), 0)
            );
        }, $refinements);

        // Dans nos retours, on ne distingue pas les facettes des "disjunctive" facettes.
        // On a aussi besoin d'avoir les filtres saisis par l'utilisateur, donc on merge tous les tableaux.
        $facetsAndDisjunctives = \Wizacha\Misc::arrayMergeRecursiveDistinct($refinementsAsFacet, $results['facets'] ?? []);
        $facetsAndDisjunctives = \Wizacha\Misc::arrayMergeRecursiveDistinct($facetsAndDisjunctives, $results['disjunctiveFacets'] ?? []);

        // On convertit ce tableau dans le resultat attendu par l'API
        $facets = $this->buildFacets($facetsAndDisjunctives, $results['facets_stats'] ?? [], $filters, $resultPagination->getTotalResultCount());

        $products = array_map(function ($algoliaResult) {
            return new AlgoliaProduct($algoliaResult);
        }, $results['hits'] ?? []);

        return new ProductSearchResult($products, $resultPagination, $facets);
    }

    public function autocomplete(string $query, array $filters = []): array
    {
        $parameters = [
            'hitsPerPage' => self::AUTOCOMPLETE_MAX_RESULTS,
        ];

        if (\count($filters) > 0 && \array_key_exists('offers', $filters) === true) {
            $parameters['facetFilters'] = 'offers.divisions:' . $filters['offers'];
        }

        $results = $this->searchEngine->search($this->getName(), $query, $parameters);

        return $results['hits'];
    }

    public function getConfig()
    {
        list($featuresIndex) = fn_get_product_features([
            'is_searchable' => 'Y',
            'statuses' => [Status::ENABLED],
        ]);
        if (!\is_array($featuresIndex)) {
            $featuresIndex = [];
        }
        //Algolia ne gére que 64 attributs pour la recherche
        $featuresIndex = \array_slice($featuresIndex, 0, 64);
        $featuresIndex = self::linearizeFeatures($featuresIndex);
        $featuresIndex = array_map(function ($feature) {
            return self::getNameFromLinearizedFeature($feature);
        }, $featuresIndex);

        return [
            Config::PARAMS_MAX_VALUES_PER_FACET => $this->getMaxValuesPerFacet(),
            Config::PARAMS_TO_INDEX => array_merge(
                [
                    'name',
                    'subtitle',
                    'shortDescription',
                    'code',
                    'supplierReference',
                ],
                $featuresIndex
            ),
            Config::PARAMS_FACET => $this->getFacetNames(),
        ];
    }

    public static function getNameFromLinearizedFeature(array $feature): string
    {
        if (!isset($feature['position'], $feature['description'])) {
            return '';
        }

        //str_replace is a workaround for the algolia bug
        return str_replace(',', '%2C', implode(
            Product::NAMESPACE_SEPARATOR,
            [
                Product::NAMESPACE_FACET,
                $feature['feature_id'],
                $feature['description'],
                $feature['position'],
            ]
        ));
    }

    public static function getNameFromLinearizedOption(array $option): string
    {
        if (\array_key_exists('option_name', $option) === false) {
            return '';
        }

        //str_replace is a workaround for the algolia bug
        return str_replace(',', '%2C', implode(
            Product::NAMESPACE_SEPARATOR,
            [
                Product::NAMESPACE_FACET,
                Product::NAMESPACE_OPTION_VALUE,
                $option['option_id'],
                $option['option_name'],
                $option['position']
            ]
        ));
    }

    public static function getShortNameFromLinearizedFeature(array $feature): string
    {
        return Product::NAMESPACE_FACET . Product::NAMESPACE_SEPARATOR . $feature['feature_id'];
    }

    public static function getShortNameFromLinearizedOption(array $option): string
    {
        return Product::NAMESPACE_FACET . Product::NAMESPACE_SEPARATOR . Product::NAMESPACE_OPTION_VALUE . Product::NAMESPACE_SEPARATOR . $option['option_id'];
    }

    protected function getFacetNames(): array
    {
        $rawAttributeFacets = $this->attributeService->getAttributesForFaceting();
        $attributesFacets = array_map([__CLASS__, 'getNameFromLinearizedFeature'], $rawAttributeFacets);
        $attributesFacets = array_merge(
            array_map([__CLASS__, 'getShortNameFromLinearizedFeature'], $rawAttributeFacets),
            $attributesFacets
        );

        $rawOptionFacets = $this->optionService->getOptionsForFaceting();
        $optionsFacets = array_map([__CLASS__, 'getNameFromLinearizedOption'], $rawOptionFacets);
        $optionsFacets = array_merge(
            array_map([__CLASS__, 'getShortNameFromLinearizedOption'], $rawOptionFacets),
            $optionsFacets
        );

        $attributeFacets = array_merge($attributesFacets, $optionsFacets);
        // Add facets that are not built on attributes

        // f##categories : utilisée pour filtrer car quand on filtre on ne connait pas le nom de la catégorie
        array_unshift($attributeFacets, Product::NAMESPACE_FACET . Product::NAMESPACE_SEPARATOR . 'categories');
        // f##categories##Catégories : utilisé pour afficher la facette avec le nom des catégories + la position
        array_unshift($attributeFacets, str_replace(',', '%2C', implode(
            Product::NAMESPACE_SEPARATOR,
            [
                Product::NAMESPACE_FACET,
                'categories',
                __('categories'),
                0,
            ]
        )));

        if ($this->enableCompanyTypeFacet) {
            // f##companyType : utilisée pour filtrer car quand on filtre on utilise 'V' ou 'C'
            array_unshift($attributeFacets, Product::NAMESPACE_FACET . Product::NAMESPACE_SEPARATOR . 'companyType');
            // f##companyType##Type de vendeur : utilisé pour afficher la facette avec la trad 'Vendeur particulier', pro...
            array_unshift($attributeFacets, str_replace(',', '%2C', implode(
                Product::NAMESPACE_SEPARATOR,
                [
                    Product::NAMESPACE_FACET,
                    'companyType',
                    __('w_company_type'),
                    0,
                ]
            )));
        }

        // f##companies##Marchands : Utilisée ET pour le search ET pour l'affichage (car mode disjunctive)
        // Cette facette est toujours envoyée à Algolia car on s'en sert meme quand elle est désactivée, pour le filtre company (page marchand, etc)
        array_unshift($attributeFacets, str_replace(',', '%2C', implode(
            Product::NAMESPACE_SEPARATOR,
            [
                Product::NAMESPACE_FACET,
                'companies',
                __('companies'),
                0,
            ]
        )));

        array_unshift($attributeFacets, 'price');
        array_unshift($attributeFacets, 'code');
        array_unshift($attributeFacets, 'supplierReference');
        array_unshift($attributeFacets, 'hasGeoloc');
        array_unshift($attributeFacets, 'hasStock');
        array_unshift($attributeFacets, 'offers');
        array_unshift($attributeFacets, 'offers.divisions');
        array_unshift($attributeFacets, 'offers.companyId');
        array_unshift($attributeFacets, 'offers.productId');
        array_unshift($attributeFacets, 'offers.price');
        array_unshift($attributeFacets, 'offers.status');
        array_unshift($attributeFacets, 'offers.priceTiers.excludingTaxes');
        array_unshift($attributeFacets, 'offers.priceTiers.includingTaxes');
        array_unshift($attributeFacets, 'offers.priceTiers.taxes');
        array_unshift($attributeFacets, 'isSubscription');

        return $attributeFacets;
    }

    private function numericFacetsFromAlgoliaResult(array $algoliaFacets, array $facetStats): array
    {
        $numericFacets = [];
        foreach ($algoliaFacets as $facetName => $values) {
            if (preg_match('/^' . Record::NAMESPACE_FACET . Record::NAMESPACE_SEPARATOR . '.+' . Record::NAMESPACE_SEPARATOR . '/', $facetName)) {
                $facetId = explode(Record::NAMESPACE_SEPARATOR, $facetName)[1];
            } elseif (preg_match('/^' . Record::NAMESPACE_FACET . Record::NAMESPACE_SEPARATOR . '/', $facetName)) {
                // Skip all facets like 'f##468' or 'f##categories' because they are used only to filter
                continue;
            } else {
                $facetId = $facetName;
            }

            if ($facetId === 'companyType' && !$this->enableCompanyTypeFacet) {
                continue;
            }

            if ($facetId === 'companies' && !$this->enableCompaniesFacet) {
                continue;
            }

            if ($facetId !== 'categories' && isset($facetStats[$facetName])) {
                $numericFacets[] = $facetName;
            }
        }

        return $numericFacets;
    }

    private function facetsFromAlgoliaResult(array $algoliaFacets, array $facetStats): array
    {
        $facets = [];
        foreach ($algoliaFacets as $facetName => $values) {
            $facetName = \strval($facetName);

            if (preg_match('/^' . Record::NAMESPACE_FACET . Record::NAMESPACE_SEPARATOR . Record::NAMESPACE_OPTION_VALUE . Record::NAMESPACE_SEPARATOR . '.+' . Record::NAMESPACE_SEPARATOR . '/', $facetName)) {
                $parts = explode(Record::NAMESPACE_SEPARATOR, $facetName);
                $facetId = self::FACET_OPTION_PREFIX . $parts[2];
                $facetLabel = $parts[3];
                $position = (int) $parts[4];
            } elseif (preg_match('/^' . Record::NAMESPACE_FACET . Record::NAMESPACE_SEPARATOR . '.+' . Record::NAMESPACE_SEPARATOR . '/', $facetName)) {
                // If it's an attribute facet, the ID, label, position are stored in the facet name
                $parts = explode(Record::NAMESPACE_SEPARATOR, $facetName);
                $facetId = $parts[1];
                $facetLabel = $parts[2];
                $position = (int) $parts[3];
            } elseif (\preg_match('/^' . Record::NAMESPACE_FACET . Record::NAMESPACE_SEPARATOR . '/', $facetName) > 0 || \in_array($facetName, self::NON_FACETED_FILTER, true) === true) {
                // Skip all facets like 'f##468' or 'f##categories' because they are used only to filter
                continue;
            } else {
                $facetId = $facetName;
                $position = 0;
                $facetLabel = __($facetName);
            }

            // Force the "categories" facet at the top
            if ($facetId === 'categories') {
                $position = -2;
            }

            if ($facetId === 'companyType') {
                if ($this->enableCompanyTypeFacet) {
                    // Force the "companyType" facet at the top
                    $position = -1;
                } else {
                    continue;
                }
            }

            if ($facetId === 'companies' && !$this->enableCompaniesFacet) {
                continue;
            }

            if ($facetId === 'options') {
                continue;
            }

            $tmpValues = [];
            $isNumericFacet = $facetId !== 'categories' && isset($facetStats[$facetName]);

            if ($isNumericFacet) {
                $tmpValues['min'] = (float) $facetStats[$facetName]['min'];
                $tmpValues['max'] = (float) $facetStats[$facetName]['max'];

                if ($tmpValues['min'] == $tmpValues['max']) {
                    continue;
                }
            } else {
                foreach ($values as $value => $valueCount) {
                    if (preg_match('/^' . Record::NAMESPACE_FACET_VALUE . Record::NAMESPACE_SEPARATOR . '/', (string) $value)) {
                        $parts = explode(Record::NAMESPACE_SEPARATOR, $value);
                        $valueId = $facetId !== 'companyType' ? (int) $parts[1] : $parts[1]; // ID is not numeric for companyType
                        //Revert l'échapement fait dans getFacetNames et getNameFromLinearizedFeature
                        $valueLabel = str_replace('%2C', ',', $parts[2]);
                        $valuePosition = (int) $parts[3];

                        $tmpValues[$valueId] = [
                            'label' => $valueLabel,
                            'count' => $valueCount,
                            'position' => $valuePosition,
                        ];
                    } else {
                        $tmpValues[$value] = [
                            //Revert l'échapement fait dans getFacetNames et getNameFromLinearizedFeature
                            'label' => str_replace('%2C', ',', $value),
                            'count' => $valueCount,
                            'position' => 0,
                        ];
                    }
                }
            }

            $facets[] = [
                'name' => $facetId,
                'label' => $facetLabel,
                'values' => $tmpValues,
                'position' => $position,
                'isNumeric' => $isNumericFacet,
            ];
        }

        // Sort by position and then remove it from the facets
        uasort($facets, function (array $a, array $b): int {
            $result = $a['position'] <=> $b['position'];
            if ($result === 0) { // use the name as a second sorting criteria
                $result = $a['name'] <=> $b['name'];
            }

            return $result;
        });
        foreach ($facets as &$facet) {
            unset($facet['position']);
        }

        return array_values($facets);
    }

    private function buildFacets(array $facets, array $facetsStats, array $filters, int $productCount): array
    {
        $facets = $this->facetsFromAlgoliaResult($facets, $facetsStats);

        // When a search has no results, Algolia doesn't return facets: we have to add it back ourselves
        $facetNames = array_column($facets, 'name');
        foreach ($filters as $filterName => $filterValue) {
            if (!\in_array($filterName, $facetNames)) {
                // Don't show company facet if facet is disabled (but filtering is still possible)
                if ($filterName === 'companies' && !$this->enableCompaniesFacet) {
                    continue;
                }

                // Never show those facets. They are used only to filter
                if (\in_array($filterName, self::NON_FACETED_FILTER)) {
                    continue;
                }

                // Prepend the new facet at the top of the other facets
                array_unshift($facets, $this->facetFromFilter($filterName, $filterValue, $productCount));
            }
        }

        return $facets;
    }

    /**
     * Create a facet from a filter
     */
    private function facetFromFilter($filterName, $filterValue, int $totalProductCount): array
    {
        switch ($filterName) {
            case 'categories':
                $facetLabel = __('category');
                $categoryNames = $this->categoryService->getCategoryNamesFromIds([$filterValue]);
                $facetValueLabel = $categoryNames[$filterValue] ?? __('unknown_category');
                break;
            case 'companies':
                $facetLabel = __('companies');
                $facetValueLabel = fn_get_company_name($filterValue) ?: __('text_nothing_found');
                break;
            case 'price':
                $facetLabel = __('price');
                $facetValueLabel = null;
                break;
            // TODO attributes
            default:
                $facetLabel = $filterName;
                $facetValueLabel = $filterValue;
        }

        // Range
        if (\is_array($filterValue)) {
            return [
                'name' => $filterName,
                'label' => $facetLabel,
                'values' => $filterValue,
                'isNumeric' => true,
            ];
        }

        return [
            'name' => $filterName,
            'label' => $facetLabel,
            'values' => [
                $filterValue => [
                    'label' => $facetValueLabel,
                    'count' => $totalProductCount,
                ],
            ],
            'isNumeric' => false,
        ];
    }

    private function convertFiltersForSearch($filters): array
    {
        $refinements = [];

        //Transforme les filters qui sont de format [id_filtre => [id_value]] en format long.
        if ($filters) {
            //On a une requete qui récupere tous les attributs en une seule fois, on en profite. On cache tout ca car ca ne bougera pas pendant la request
            if (empty($this->cacheRealFilters)) {
                foreach ($this->attributeService->getPublicAttributesForSearchAndFaceting() as $attribute) {
                    $this->cacheRealFilters[$attribute['feature_id']] = $attribute;
                }
                foreach ($this->optionService->flattenDetailedLegacyOptions() as $option) {
                    $this->cacheRealFilters[self::FACET_OPTION_PREFIX . $option['option_id']] = $option;
                }
            }
            $realFiltersById = [];
            foreach ($this->cacheRealFilters as $filter) {
                if (\array_key_exists('feature_id', $filter) === true) {
                    $realFiltersById[$filter['feature_id']] = $filter;
                }
                if (\array_key_exists('option_id', $filter) === true) {
                    $realFiltersById[self::FACET_OPTION_PREFIX . $filter['option_id']] = $filter;
                }
            }
            //On prend chaque filtre, et s'il existe en tant qu'attribut pour la recherche, on récupere ses valeurs
            foreach ($filters as $filterId => $values) {
                $isOption = false;

                if (\is_array($values) === false) {
                    $values = [$values];
                }

                //Cas particulier de la categorie/companyType qui n'est pas lié à un attribut
                if ($filterId === 'categories' || $filterId === 'companyType') {
                    $refinements[Record::NAMESPACE_FACET . Record::NAMESPACE_SEPARATOR . $filterId] = $values;
                    continue;
                }

                // Cas particuliers
                if (\in_array($filterId, self::NON_FACETED_FILTER)) {
                    $refinements[$filterId] = $values;
                    continue;
                }

                // Cas particulier de la facette companies pour laquelle on fait la requête de la meme manière que les
                // attributs pour avoir le mode disjunctive (requiert malheuresement un call BDD)
                if ($filterId === 'companies') {
                    //str_replace is a workaround for the algolia bug
                    $facetName = str_replace(',', '%2C', implode(
                        Product::NAMESPACE_SEPARATOR,
                        [
                            Product::NAMESPACE_FACET,
                            'companies',
                            __('companies'),
                            0,
                        ]
                    ));

                    foreach ($values as $value) {
                        $refinements[$facetName][] = str_replace(',', '%2C', implode(
                            Product::NAMESPACE_SEPARATOR,
                            [
                                Product::NAMESPACE_FACET_VALUE,
                                $value,
                                fn_get_company_name($value),
                                0,
                            ]
                        ));
                    }

                    continue;
                }

                if (!empty($realFiltersById[$filterId])) {
                    $keyFilterId = (int) $filterId;
                    if (\is_string($filterId) === true && \strpos($filterId, self::FACET_OPTION_PREFIX) === 0) {
                        $keyFilterId = (int) \substr_replace($filterId, '', 0, 3);
                        $isOption = true;
                    }
                    if ($isOption === false) {
                        $facetName = static::getNameFromLinearizedFeature($realFiltersById[$filterId]);

                        // TODO: Remplacer ce call qui `in fine` fait appel a un gros truc cscart par un
                        // appel plus leger pour la BDD.
                        // Ceci dit ce n'est réalisé qu'une fois par filtre par recherche
                        $variants = $this->attributeService->getAttributeVariants($keyFilterId);

                        foreach ($values as $value) {
                            if (!empty($variants[$value])) {
                                $refinements[$facetName][] = str_replace(',', '%2C', implode(
                                    Product::NAMESPACE_SEPARATOR,
                                    [
                                        Product::NAMESPACE_FACET_VALUE,
                                        $value,
                                        $variants[$value]['variant'],
                                        $variants[$value]['position'],
                                    ]
                                ));
                            } else {
                                $isAnAttributeWithoutVariants = !\in_array($realFiltersById[$filterId]['feature_type'], [
                                    AttributeType::LIST_TEXT,
                                    AttributeType::LIST_BRAND,
                                    AttributeType::LIST_NUMBER,
                                    AttributeType::CHECKBOX_MULTIPLE,
                                ]);

                                if ($isAnAttributeWithoutVariants) { //Les attributs de type texte libre ne sont pas associé à un ID.
                                    $refinements[$facetName][] = $value;
                                }
                                // Pas de else parce qu'on tomberait dedans si on a pas trouvé la variante et que l'attribut est bien un attribut de type liste
                                // Ce qui fait des choses très bizare par la suite (avant ca passait comme free text). On le sabre maintenant et c'est tout.
                            }
                        }
                    } else {
                        $facetName = static::getNameFromLinearizedOption($realFiltersById[$filterId]);
                        $variants = $this->optionService->getOptionVariants($keyFilterId);
                        foreach ($values as $value) {
                            if (\array_key_exists($value, $variants) === true && \count($variants[$value]) > 0) {
                                $refinements[$facetName][] = str_replace(
                                    ',',
                                    '%2C',
                                    implode(
                                        Product::NAMESPACE_SEPARATOR,
                                        [
                                            Product::NAMESPACE_FACET_VALUE,
                                            $value,
                                            $variants[$value]['variant_name'],
                                            $variants[$value]['position'],
                                        ]
                                    )
                                );
                            }
                        }
                    }
                }
            }
        }

        return $refinements;
    }

    private function getMaxValuesPerFacet(): int
    {
        return (int) $this->registry->container->getParameter('algolia.config.max_values_per_facet');
    }
}
