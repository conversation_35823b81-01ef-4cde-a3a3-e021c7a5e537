<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Search;

use MyCLabs\Enum\Enum;

/**
 * List of directions allowed for sorting search results.
 *
 * @method static SortDirection ASC()
 * @method static SortDirection DESC()
 */
class SortDirection extends Enum
{
    public const ASC = 'asc';
    public const DESC = 'desc';

    /**
     * We override the constructor to avoid any error of the order is unknown: we fallback
     * to the default one.
     */
    public function __construct($value)
    {
        if (!$this->isValid($value)) {
            $value = self::ASC;
        }

        parent::__construct($value);
    }

    public function isAsc(): bool
    {
        return $this->value === self::ASC;
    }
}
