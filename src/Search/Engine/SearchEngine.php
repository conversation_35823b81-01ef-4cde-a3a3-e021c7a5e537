<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Search\Engine;

use Wizacha\Search\Record\Record;

interface SearchEngine
{
    /**
     * Returns the filename of the tpl file to include in order to initialize
     * the javascript global variable Tygh.search_client.
     * @return string
     * @deprecated To be removed, will be replaced by a common JS helper that uses the search API.
     */
    public function getJsClientTemplate();

    /**
     * @param Record $record
     * @return bool success
     */
    public function pushRecord(Record $record);

    /**
     * @param Record[] $records
     * @return bool success
     */
    public function pushMultipleRecords(array $records);

    /**
     * @param Record $record
     * @return bool success
     */
    public function deleteRecord(Record $record);

    /**
     * Returns the number of records in an index
     * @param string $index
     * @return mixed
     */
    public function getNbRecords($index);

    /**
     * Returns the maximum number of item to send for optimal performances
     * @return integer
     */
    public function chunkSize();

    /**
     * Set index in Algolia
     * @param string $lang
     * @return mixed
     */
    public function setIndex(string $lang);

    /**
     * @param string $index
     * @param array $params [Config::PARAMS_FACET => list_of_facet, Config::PARAMS_TO_INDEX => list of attribute to index]
     * @return boolean
     */
    public function setIndexParams($index, array $params);

    /**
     * Retrieve object ids stored in the search engine
     * @param string $index
     * @param int $page
     * @return int[]
     */
    public function getIdsByPage($index, $page = 0);

    /**
     * @param string $index
     * @param string $query
     * @param array $params [Config::PARAMS_FACET_FITLER => '', Config::PARAMS_TAG_FILTER => '', Config::PARAMS_HITS_PER_PAGE => n, Config::PARAMS_PAGE => n]
     * @return array
     */
    public function search($index, $query = '', array $params = []);

    public function getIndexKey(string $index, string $lang = null): string;

    /**
     * Delete indexes for a language
     *
     * @param string $lang
     *
     * @return mixed
     */
    public function deleteIndex(string $lang);
}
