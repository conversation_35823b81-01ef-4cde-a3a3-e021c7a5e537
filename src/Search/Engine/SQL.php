<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Search\Engine;

use Wizacha\Marketplace\GlobalState\GlobalState;
use Exception;

class SQL implements SearchEngine
{
    public const EXCEPTION_MESSAGE = 'Not implemented';

    public function getJsClientTemplate()
    {
        return 'common/search/sql.tpl';
    }

    public function pushRecord(\Wizacha\Search\Record\Record $record)
    {
        //Nothing to do
        return true;
    }

    public function pushMultipleRecords(array $records)
    {
        //Nothing to do
        return true;
    }

    public function deleteRecord(\Wizacha\Search\Record\Record $record)
    {
        //Nothing to do
        return true;
    }

    public function getNbRecords($index)
    {
        return 0;
    }

    public function chunkSize()
    {
        return 0;
    }

    public function setIndexParams($index, array $params)
    {
        return true;
    }

    /**
     * @inheritdoc
     */
    public function getIdsByPage($index, $page = 0)
    {
        return [];
    }

    public function search($index, $query = '', array $params = [])
    {
        return [];
    }

    public function setIndex(string $lang)
    {
        //Nothing to do
        return true;
    }

    public function getIndexKey(string $index, string $lang = null): string
    {
        if (false === \is_string($lang)) {
            $lang = GlobalState::contentLocale();
        }

        return $index . "_" . $lang;
    }

    public function deleteIndex(string $lang)
    {
        //Nothing to do
        return true;
    }
}
