<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Search\Engine;

use Wizacha\Component\Locale\Locale;
use Wizacha\Marketplace\Catalog\Product\ProductService;
use Wizacha\Search\Record\AlgoliaProduct;
use Wizacha\Search\Record\AlgoliaProductRecordFactory;

/**
 * Service to check for Catalog readmodel : Algolia record sync
 */
class AlgoliaSyncService
{
    private const DIFF_KEY_ACTUAL = 'actual';
    private const DIFF_KEY_EXPECTED = 'expected';

    private Algolia $algolia;
    private AlgoliaProductRecordFactory $algoliaProductRecordFactory;
    private ProductService $productService;

    public function __construct(
        Algolia $algolia,
        AlgoliaProductRecordFactory $algoliaProductRecordFactory,
        ProductService $productService
    ) {
        $this->algolia = $algolia;
        $this->algoliaProductRecordFactory = $algoliaProductRecordFactory;
        $this->productService = $productService;
    }

    /**
     * Check if Algolia Record match current Readmodel
     */
    public function isSync(Locale $lang, $productId): bool
    {
        foreach ($this->getRecordDiffGenerator($lang, $productId) as $_) {
            return false;
        }

        return true;
    }

    /**
     * List Readmodel:Algolia record diff
     */
    public function getRecordDiffGenerator(
        Locale $lang,
        $productId
    ): \Generator {
        $product = $this->productService->getProduct($productId);

        if (\is_null($product)) {
            return; # No readmodel, no problem
        }

        $expectedRecord = $this->algoliaProductRecordFactory->create(
            $product
        );
        $actualRecord = $this->algolia->pullRecord($lang, $productId);

        yield from $this->processDiffGenerator(
            $expectedRecord,
            $actualRecord
        );
    }

    private function processDiffGenerator(
        AlgoliaProduct $expectedRecord,
        AlgoliaProduct $actualRecord
    ): \Generator {
        $expectedArray = $expectedRecord->summary();
        $actualArray = $actualRecord->summary();

        foreach ($expectedArray as $key => $expectedValue) {
            $actualValue = $actualArray[$key];

            if (\is_scalar($expectedValue) ? $actualValue !== $expectedValue : $actualValue != $expectedValue
            ) {
                yield $key => [
                    static::DIFF_KEY_ACTUAL => $actualValue,
                    static::DIFF_KEY_EXPECTED => $expectedValue,
                ];
            }
        }
    }
}
