<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Search\Engine;

/**
 * A collection of replicas for a primary index
 */
class ReplicaCollection
{
    protected const ORDER_LIST = ['asc', 'desc'];

    /** @var Replica[] */
    protected $collection;

    /**
     * @param string $indexName Name of the primary index
     * @param array $criterias  Attributes for which to create replicas
     */
    public function __construct(string $indexName, array $criterias)
    {
        foreach ($criterias as $name) {
            foreach (self::ORDER_LIST as $order) {
                $this->collection[] = new Replica($indexName, $name, $order);
            }
        }
    }

    /** @return string[] */
    public function getReplicaNames(): array
    {
        return array_map(function (Replica $replica) {
            return $replica->getName();
        }, $this->collection);
    }

    /** @return Replica[] */
    public function getCollection(): array
    {
        return $this->collection;
    }
}
