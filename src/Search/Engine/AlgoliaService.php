<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright Copyright (c) Wizaplace
 * @license   Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Search\Engine;

use Tygh\Database;
use Wizacha\Core\Hash\Hash;
use Wizacha\Core\Iterator\PdoColumnIterator;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Search\Index\AbstractIndex;

class AlgoliaService
{
    /** @var SearchEngine */
    private $engine;

    /** @var AbstractIndex */
    protected $abstractIndex;

    /** @var bool Are we allowed to do automatic updates of Algolia indices? */
    private bool $featureEnableUpdateAlgolia;

    public function __construct(SearchEngine $engine, AbstractIndex $abstractIndex, bool $featureEnableUpdateAlgolia)
    {
        $this->engine = $engine;
        $this->abstractIndex = $abstractIndex;
        $this->featureEnableUpdateAlgolia = $featureEnableUpdateAlgolia;
    }

    /**
     * @return bool
     */
    public function isFeatureEnableUpdateAlgolia(): bool
    {
        return $this->featureEnableUpdateAlgolia;
    }

    public function clearIndex(): self
    {
        GlobalState::runWithAllLanguages([
            $this->engine, 'clearIndex',
        ]);
        Hash::deleteByType('Wizacha\Search\Record\Product');

        return $this;
    }

    public function createIndex(string $langCode): self
    {
        $this->engine->setIndex($langCode);

        return $this;
    }

    public function pushProducts(): self
    {
        Database::query("TRUNCATE TABLE ?:w_hashes");

        $this->abstractIndex->update(new PdoColumnIterator(Database::prepare(
            'SELECT id FROM `doctrine_multi_vendor_product`'
        )));

        $this->abstractIndex->update(new PdoColumnIterator(Database::prepare(
            'SELECT product_id FROM `cscart_products`'
        )));

        return $this;
    }

    public function pushConfig(): self
    {
        GlobalState::runWithAllLanguages([
            $this->abstractIndex, 'updateIndexParams',
        ]);

        return $this;
    }


    /**
     * Delete indexes for a language
     *
     * @param string $lang
     *
     * @return $this
     */
    public function deleteIndex(string $lang): self
    {
        $this->engine->deleteIndex($lang);

        return $this;
    }
}
