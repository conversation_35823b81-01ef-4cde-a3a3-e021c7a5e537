<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Search\Engine;

/**
 * A replica
 *
 * @see https://www.algolia.com/doc/api-reference/api-parameters/replicas/
 */
class Replica
{
    /** @var string */
    protected $indexName;

    /** @var string */
    protected $criteria;

    /** @var string */
    protected $order;

    /**
     * @param string $indexName Name of the primary index
     * @param string $criteria  Attribute on which to create this replica
     * @param string $order     Sort order of the attribute for this replica
     */
    public function __construct(string $indexName, string $criteria, string $order)
    {
        $this->indexName = $indexName;
        $this->criteria = $criteria;
        $this->order = $order;
    }

    /** @return string */
    public function getName(): string
    {
        return sprintf("%s_by_%s_%s", $this->indexName, $this->criteria, $this->order);
    }

    /**
     * @param string[] $defaultRanking
     * @return string[]
     */
    public function getRanking(array $defaultRanking): array
    {
        return array_merge([$this->getOrderedCriteria()], $defaultRanking);
    }

    /** @return string */
    protected function getOrderedCriteria(): string
    {
        return sprintf("%s(%s)", $this->order, $this->criteria);
    }
}
