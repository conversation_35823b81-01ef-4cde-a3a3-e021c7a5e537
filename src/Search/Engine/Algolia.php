<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Search\Engine;

use AlgoliaSearch\AlgoliaException;
use AlgoliaSearch\Client;
use Wizacha\AppBundle\Exception\Algolia\MissingClientException;
use Wizacha\Component\Locale\Locale;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Search\Config;
use Wizacha\Search\Product\Sorting;
use Wizacha\Search\Record\AlgoliaProduct;
use Wizacha\Search\Record\Record;

class Algolia implements SearchEngine
{
    public const CFG_CLIENT_INSTANCE = 'algolia_client_instance';
    public const CFG_API_KEY_FULL_RIGHTS = 'full_rights_key';
    public const CFG_API_IDENTIFIER = 'api_identifier';
    public const ALGOLIA_REPLICAS = 'replicas';
    public const ALGOLIA_RANKING = 'ranking';

    public const CFG_API_KEY_LIMITED_RIGHTS_GEOCODING = 'limited_rights_key_geocoding';
    public const CFG_API_IDENTIFIER_GEOCODING = 'api_identifier_geocoding';

    public const BATCH_CHUNK_SIZE = 1000;

    protected const DEFAULT_RANKING = ["typo", "geo", "words", "filters", "proximity", "attribute", "exact", "custom"];
    protected const REPLICA_CRITERIAS = Sorting::CRITERIAS;

    private const PRODUCT_INDEX_KEY = 'products';
    private const CATEGORY_INDEX_KEY = 'categories';

    /** @var Client */
    protected $client;
    protected $registry;
    /**
     * @var string
     */
    private $prefix;

    /**
     * Uses following configuration keys to create an instance
     *   -['config', \Wizacha\Search\Config::CFG_SEARCH_ENGINE, static::CFG_API_IDENTIFIER]
     *   -['config', \Wizacha\Search\Config::CFG_SEARCH_ENGINE, static::CFG_API_KEY_FULL_RIGHTS]
     *   -['config', \Wizacha\Search\Config::CFG_SEARCH_ENGINE, static::CFG_CLIENT_INSTANCE]
     *  If a client instance is set, credentials are not used.
     * @param \Wizacha\Registry $reg
     * @param string $prefix
     * @throws \Exception
     */
    public function __construct(\Wizacha\Registry $reg, string $prefix)
    {
        $config = $reg->get(['config', \Wizacha\Search\Config::CFG_SEARCH_ENGINE]);
        if (true === \is_array($config)
            && null !== $config[self::CFG_CLIENT_INSTANCE]
        ) {
            $this->client = $config[self::CFG_CLIENT_INSTANCE];
            if (!$this->client instanceof Client) {
                throw new \Exception('Algolia instance is not a valid \AlgoliaSearch\Client');
            }
        } else {
            if (!empty($config[static::CFG_API_IDENTIFIER]) && !empty($config[static::CFG_API_KEY_FULL_RIGHTS])) {
                $this->client = new Client(
                    $config[static::CFG_API_IDENTIFIER],
                    $config[static::CFG_API_KEY_FULL_RIGHTS]
                );
            }
        }
        $this->registry = $reg;
        $this->prefix = $prefix;
    }

    public function getClient()
    {
        return $this->client;
    }

    public function getJsClientTemplate()
    {
        return 'common/search/algolia.tpl';
    }

    public function pushRecord(Record $record)
    {
        if (!$this->client) {
            return false;
        }

        $index = $this->client->initIndex($record->index($this->registry));
        $index->saveObject($record->data());

        return true;
    }

    /**
     * Please don't call this more that once in a same request,
     * use self::pullRecords if you need to query more than one Algolia record
     *
     * @return AlgoliaProduct
     */
    public function pullRecord(Locale $lang, $id): AlgoliaProduct
    {
        foreach ($this->pullRecords($lang, [$id]) as $algoliaProduct) {
            return $algoliaProduct;
        };
    }

    /**
     * @return \Generator<AlgoliaProduct>
     */
    public function pullRecords(Locale $lang, array $ids): \Generator
    {
        $index = $this->client->initIndex(
            $this->getIndexKey($this->prefix . static::PRODUCT_INDEX_KEY, $lang)
        );

        $normalized = \array_map('strval', $ids);
        $response = $index->getObjects($normalized);

        if ($response['message']) {
            throw new AlgoliaException($response["message"]);
        }

        foreach ($response['results'] as $object) {
            yield new AlgoliaProduct($object);
        }
    }

    /**
     * @param Record[] $records
     * @return bool
     */
    public function pushMultipleRecords(array $records)
    {
        if (!$this->client) {
            return false;
        }
        //Group records per index
        /** @var Record[][] $groups */
        $groups = [];
        foreach ($records as $record) {
            $groups[$record->index($this->registry)][] = $record;
        }
        //Send groups
        foreach ($groups as $index => $group) {
            //Retrieve data
            foreach ($group as &$record) {
                $record = $record->data();
            }
            $index = $this->client->initIndex($index);
            //Split & Send data
            $batches = array_chunk($group, self::BATCH_CHUNK_SIZE);
            array_map([$index, 'saveObjects'], $batches);
        }

        return true;
    }

    public function deleteRecord(Record $record)
    {
        if (!$this->client) {
            throw new MissingClientException();
        }

        $indices = $record->indices($this->registry);

        foreach ($indices as $name) {
            $index = $this->client->initIndex($name);
            $index->deleteObject($record->id());
        }

        return true;
    }

    public function getNbRecords($index)
    {
        if (!$this->client) {
            return 0;
        }
        //Perform a minimal request to retrieve number of results
        $data = $this->client->initIndex($index)->search(
            '',
            [
                'attributesToRetrieve' => Record::KEY_OBJECT_ID,
                'attributesToHighlight' => '',
                'hitsPerPage' => 1,
            ]
        );

        return \intval($data['nbHits']);
    }

    public function chunkSize()
    {
        return self::BATCH_CHUNK_SIZE;
    }

    public function setIndex(string $lang)
    {
        $client = $this->getClient();
        $indexCategoriesKey = $this->getIndexKey($this->prefix . static::CATEGORY_INDEX_KEY, $lang);
        $indexProductKey = $this->getIndexKey($this->prefix . static::PRODUCT_INDEX_KEY, $lang);

        // searchableAttributes: If set to null, all textual and numerical attributes of your objects are indexed
        $client->initIndex($indexCategoriesKey)->setSettings([
            'searchableAttributes' => null,
        ]);

        $replicaCollection = new ReplicaCollection($indexProductKey, static::REPLICA_CRITERIAS);

        $client
            ->initIndex($indexProductKey)
            ->setSettings([
                self::ALGOLIA_REPLICAS => $replicaCollection->getReplicaNames(),
                self::ALGOLIA_RANKING => static::DEFAULT_RANKING,
            ]);

        foreach ($replicaCollection->getCollection() as $replica) {
            $client
                ->initIndex($replica->getName())
                ->setSettings([
                    self::ALGOLIA_RANKING => $replica->getRanking(static::DEFAULT_RANKING),
                ]);
        }
    }

    public function setIndexParams($index, array $params)
    {
        if (!$this->client) {
            return false;
        }

        if (empty($params[Config::PARAMS_TO_INDEX])) {
            return false;
        }

        $formattedParams = $this->formatSettings($params);

        if (false === $this->needToUpdateSettings($index, $formattedParams)) {
            return false;
        }

        $this->client->initIndex($index)->setSettings($formattedParams, true);

        return true;
    }

    /**
     * @inheritdoc
     */
    public function getIdsByPage($index, $page = 0)
    {
        if (!$this->client) {
            return [];
        }

        $startTime = microtime(true);

        $records = $this->client->initIndex($index)->browse($page, self::BATCH_CHUNK_SIZE);
        $result = array_map('intval', array_column($records['hits'], Record::KEY_OBJECT_ID));

        return $result;
    }

    public function search($index, $query = '', array $params = [])
    {
        if (!$this->client) {
            return [];
        }

        $startTime = microtime(true);

        $result = $this->client->initIndex($index)->search($query, $params);

        return $result;
    }

    /**
     * Retourne un tableau vide en cas d'echec.
     * Renvoi la reponse algolia sinon. Le tableau est le même que pour un search classique
     * avec une clé "disjunctiveFacets" en plus.
     *
     * @see \AlgoliaSearch\Index::searchDisjunctiveFaceting and https://www.algolia.com/doc/guides/search/filtering-faceting/#disjunctive-faceting about disjunctive facets
     */
    public function searchDisjunctiveFacets(string $index, string $query, array $disjunctiveFacets, array $params, array $refinements): array
    {
        if (!$this->client) {
            return [];
        }

        $startTime = microtime(true);

        $result = $this->client->initIndex($index)->searchDisjunctiveFaceting($query, $disjunctiveFacets, $params, $refinements);

        return $result;
    }

    public function getNumericFilterStats(string $index, array $numericFacets, array $disjunctiveFacets, array $refinements): array
    {
        $startTime = microtime(true);

        // Minimal search request
        $result = $this->client->initIndex($index)->searchDisjunctiveFaceting(
            '',
            $disjunctiveFacets, // In case we want stats with other facets
            [
                "facets" => $numericFacets, // We limit the facet stats to only numeric ones
                "attributesToHighlight" => '', // We don't want any highlight
                "attributesToRetrieve" => "productId", // Just product Id, to lighten the request
                'page' => 0, // Algolia's first page is 0, not 1
                'hitsPerPage' => 1, // Just one result to lighten the request
            ],
            $refinements
        );
        // Example : with a refinement like 'color:white'  we want only the price stats of white products

        return $result;
    }

    /**
     * get the params of the remote settings
     *
     * @param mixed[] $params
     *
     * @return mixed[]
     */
    public function formatSettings(array $params): array
    {
        $searchableAttributesIndex = [];
        if (\array_key_exists(Config::PARAMS_TO_INDEX, $params) === true) {
            $searchableAttributes = $params[Config::PARAMS_TO_INDEX];
            foreach ($searchableAttributes as $key => $searchableAttribute) {
                if (substr($searchableAttribute, 0, 3) === "f##") {
                    $searchableAttributesIndex[] = "unordered(" . $searchableAttribute . ")";
                } else {
                    $searchableAttributesIndex[] = $searchableAttribute;
                }
            }
        }

        return [
            'maxValuesPerFacet' => $params[Config::PARAMS_MAX_VALUES_PER_FACET],
            'searchableAttributes' => $searchableAttributesIndex,
            'attributesForFaceting' => $params[Config::PARAMS_FACET] ?: [],
            'typoTolerance' => 'min', // see https://www.algolia.com/doc/api-reference/api-parameters/typoTolerance/?language=php#typotolerance
            'disableTypoToleranceOnAttributes' => ['code', 'supplierReference'],
        ];
    }

    /**
     * Use compareSettings for the formatted params before Remote settings update
     *
     * @param mixed[] $formattedParams
     *
     * see this->formatSettings() for an example of settings array
     */
    public function needToUpdateSettings(string $index, array $formattedParams): bool
    {
        return false === $this->compareSettings(
            $formattedParams,
            $this->client->initIndex($index)->getSettings()
        );
    }

    /**
     * Compare local settings with the remote ones
     * Usefull to known if we need to push an update
     *
     * @param mixed[] $localSettings
     * @param mixed[] $remoteSettings
     *
     * see this->formatSettings() for an example of settings array
     */
    public function compareSettings(array $localSettings, array $remoteSettings): bool
    {
        $areEquals = true;

        foreach ($localSettings as $name => $value) {
            if (false === \array_key_exists($name, $remoteSettings)) {
                $areEquals = false;
            }

            if (true === \is_array($value)
                && false === $this->compareArrayValueSettings($value, $remoteSettings[$name])
            ) {
                $areEquals = false;
            }

            if ((true === \is_scalar($value) || true === \is_null($value))
                && $value !== $remoteSettings[$name]
            ) {
                $areEquals = false;
            }
        }

        return $areEquals;
    }

    /**
     * Compare array values settings
     *
     * @param mixed[] $localValue
     * @param mixed[]|null $remoteValue
     *
     * see this->formatSettings() for an example of settings array
     */
    public function compareArrayValueSettings(array $localValue, ?array $remoteValue): bool
    {
        if (\is_null($remoteValue)
            || \count($localValue) !== \count($remoteValue)
        ) {
            return false;
        }

        sort($localValue);
        sort($remoteValue);

        foreach ($localValue as $index => $finalValue) {
            if ($finalValue !== $remoteValue[$index]) {
                return false;
            }
        }

        return true;
    }

    public function clearIndex(): self
    {
        $indexCategoriesKey = $this->getIndexKey($this->prefix . static::CATEGORY_INDEX_KEY);
        $indexProductKey = $this->getIndexKey($this->prefix . static::PRODUCT_INDEX_KEY);

        $this->client->initIndex($indexCategoriesKey)->clearIndex();
        $this->client->initIndex($indexProductKey)->clearIndex();

        return $this;
    }

    public function getIndexKey(string $index, string $lang = null): string
    {
        if (false === \is_string($lang)) {
            $lang = GlobalState::contentLocale();
        }

        return $index . "_" . $lang;
    }

    /**
     * Delete indexes for a language
     *
     * @param string $lang
     *
     * @return $this
     */
    public function deleteIndex(string $lang): self
    {
        $indexCategories = $this->getIndexKey($this->prefix . static::CATEGORY_INDEX_KEY, $lang);
        $indexProducts = $this->getIndexKey($this->prefix . static::PRODUCT_INDEX_KEY, $lang);

        $this->removeReplicaFromIndex($indexProducts);

        $this->client->deleteIndex($indexProducts);
        $this->client->deleteIndex($indexCategories);

        return $this;
    }

    /**
     * Delete Replica from index
     *
     * @param string $index
     *
     * @return $this
     */
    private function removeReplicaFromIndex(string $index): self
    {
        $replicaCollection = new ReplicaCollection($index, static::REPLICA_CRITERIAS);
        $result = $this->client->initIndex($index)->setSettings([$this::ALGOLIA_REPLICAS => []]);
        $this->client->initIndex($index)->waitTask($result['taskID']);

        foreach ($replicaCollection->getCollection() as $replica) {
            $this->client->deleteIndex($replica->getName());
        }

        return $this;
    }
}
