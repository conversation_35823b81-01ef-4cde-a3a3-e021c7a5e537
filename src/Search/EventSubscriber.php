<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Search;

use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Tygh\Database;
use Wizacha\Async\Dispatcher;
use Wizacha\Category;
use Wizacha\Core\Iterator\PdoColumnIterator;
use Wizacha\Events\IterableEvent;
use Wizacha\Marketplace\Company\CompanyEvents;
use Wizacha\Marketplace\Entities\DeclinationFactory;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\PIM\Attribute\AttributeService;
use Wizacha\Marketplace\PIM\MultiVendorProduct\Event\LinkEvent;
use Wizacha\Marketplace\PIM\MultiVendorProduct\Event\MultiVendorProductEvent;
use Wizacha\Marketplace\PIM\MultiVendorProduct\Link;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProductService;
use Wizacha\Marketplace\PIM\Product\ProductService;
use Wizacha\Marketplace\ReadModel\ProductProjector;
use Wizacha\Marketplace\RelatedProduct\RelatedProduct;
use Wizacha\Option;
use Wizacha\Product;
use Wizacha\Search\Index\CategoryIndex;
use Wizacha\Search\Index\AbstractIndex;
use Wizacha\Shipping;
use Wizacha\Component\Locale\Locale;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Tygh\Languages\Languages;

/**
 * Keep the search indexes up to date by listening to events and updating indexes.
 */
class EventSubscriber implements EventSubscriberInterface
{
    /**
     * @var CategoryIndex
     */
    private $categoryIndex;

    /**
     * @var EventDispatcherInterface
     */
    private $eventDispatcher;

    /**
     * @var AttributeService
     */
    private $attributeService;

    /**
     * @var ProductProjector
     */
    private $productProjector;

    /**
     * @var ProductService
     */
    private $productService;

    /**
     * @var MultiVendorProductService
     */
    private $multiVendorProductService;

    /**
     * @var AbstractIndex
     */
    private $productIndex;

    /**
     * @var Dispatcher
     */
    private $asyncDispatcher;

    /**
     * @var DeclinationFactory
     */
    private $declinationFactory;

    public function __construct(
        CategoryIndex $categoryIndex,
        EventDispatcherInterface $eventDispatcher,
        AttributeService $attributeService,
        ProductProjector $productProjector,
        ProductService $productService,
        MultiVendorProductService $multiVendorProductService,
        AbstractIndex $productIndex,
        Dispatcher $asyncDispatcher,
        DeclinationFactory $declinationFactory
    ) {
        $this->categoryIndex = $categoryIndex;
        $this->eventDispatcher = $eventDispatcher;
        $this->attributeService = $attributeService;
        $this->productProjector = $productProjector;
        $this->productService = $productService;
        $this->multiVendorProductService = $multiVendorProductService;
        $this->productIndex = $productIndex;
        $this->asyncDispatcher = $asyncDispatcher;
        $this->declinationFactory = $declinationFactory;
    }

    public static function getSubscribedEvents()
    {
        return [
            Product::EVENT_CREATE  => ['onProductUpdate', 0],
            Product::EVENT_UPDATE  => ['onProductUpdate', 0],
            Product::EVENT_DELETE  => ['onProductUpdate', 0],

            RelatedProduct::EVENT_CREATE => ['onRelatedProductOffersUpdate', 0],
            RelatedProduct::EVENT_DELETE => ['onRelatedProductOffersUpdate', 0],
            RelatedProduct::EVENT_UPDATE => ['onRelatedProductOffersUpdate', 0],

            MultiVendorProductEvent::UPDATED => ['onMultiVendorProductUpdate', 0],
            MultiVendorProductEvent::DELETED => ['onMultiVendorProductDelete', 0],

            LinkEvent::CREATED => ['onMultiVendorProductLinkUpdate', 0],
            LinkEvent::UPDATED => ['onMultiVendorProductLinkUpdate', 0],
            LinkEvent::DELETED => ['onMultiVendorProductLinkDelete', 0],

            // Permet de faire la propagation sur les produits en dernier
            CompanyEvents::UPDATED => ['onCompanyUpdate', -100],

            AttributeService::EVENT_UPDATE => ['onFeatureUpdate', 0],
            AttributeService::EVENT_DELETE => ['onFeatureUpdate', 0],
            AttributeService::EVENT_UPDATE_VARIANT => ['onFeatureVariantUpdate', 0],

            Category::EVENT_UPDATE => ['onCategoryUpdate', 0],
            Category::EVENT_DELETE => ['onCategoryUpdate', 0],

            Option::EVENT_UPDATE => ['onOptionUpdate', 0],

            Shipping::EVENT_VENDOR_UPDATE => ['onShippingVendorUpdate', 0],
        ];
    }

    public function onMultiVendorProductUpdate(MultiVendorProductEvent $event)
    {
        $multiVendorProduct = $event->getMultiVendorProduct();
        $this->productProjector->projectMultiVendorProduct($multiVendorProduct->getId());
    }

    public function onMultiVendorProductDelete(MultiVendorProductEvent $event)
    {
        $multiVendorProduct = $event->getMultiVendorProduct();
        $this->productProjector->projectMultiVendorProduct($multiVendorProduct->getId());

        // Trigger products update
        $productIds = array_map(function (Link $link): int {
            return $link->getProduct()->getId();
        }, $multiVendorProduct->getLinks()->toArray());
        $this->eventDispatcher->dispatch(IterableEvent::fromArray($productIds), Product::EVENT_UPDATE);
    }

    public function onMultiVendorProductLinkUpdate(LinkEvent $event)
    {
        $multiVendorProduct = $event->getLink()->getMultiVendorProduct();
        $this->productProjector->projectMultiVendorProduct($multiVendorProduct->getId());
    }

    public function onMultiVendorProductLinkDelete(LinkEvent $event)
    {
        // Update du MVP
        $multiVendorProduct = $event->getLink()->getMultiVendorProduct();
        $this->productProjector->projectMultiVendorProduct($multiVendorProduct->getId());

        // Update du Product
        $product = $event->getLink()->getProduct();
        $this->productProjector->projectProduct($product->getId());
    }

    public function onProductUpdate(IterableEvent $event)
    {
        foreach ($event as $productId) {
            if (MultiVendorProduct::isMultiVendorProductId($productId) === false) {
                $this->productProjector->projectProduct((int) $productId);
            }
        }
    }

    public function onProductUpdateAsync(array $productIds): void
    {
        if ($this->asyncDispatcher->delayExec('event.subscriber.search' . '::' . __FUNCTION__, \func_get_args())) {
            return;
        }

        foreach ($productIds as $productId) {
            if (MultiVendorProduct::isMultiVendorProductId($productId) === false) {
                $this->productProjector->projectProduct((int) $productId);
            } else {
                $this->productProjector->projectMultiVendorProduct($productId);
            }
        }
    }

    public function onRelatedProductOffersUpdate(IterableEvent $event)
    {
        foreach ($event as $productId) {
            if (MultiVendorProduct::isMultiVendorProductId($productId) === false) {
                // Only the related property is updated
                $this->productProjector->updateRelatedProductOffersProjector((int) $productId);

                // If we have a multi-vendor product containing this product,
                // update its related property
                try {
                    $multiVendorProduct = $this->productService->get($productId)->getMultiVendorProduct();
                } catch (NotFound $e) {
                    $multiVendorProduct = null;
                }

                if ($this->productProjector->canProjectMvp($multiVendorProduct)) {
                    $this->productProjector->updateMVPRelatedProductOffersProjector($multiVendorProduct);
                }
            }
        }
    }

    public function onCategoryUpdate(IterableEvent $event)
    {
        $contentLocale = GlobalState::contentLocale();

        foreach (array_keys(Languages::getAll()) as $locale) {
            GlobalState::switchContentTo(new Locale($locale));
            $this->categoryIndex->update($event);
        }

        GlobalState::switchContentTo($contentLocale);

        // Update products in the category
        foreach ($event as $categoryId) {
            $productIds = new PdoColumnIterator(
                Database::prepare(
                    "
                    SELECT pc.product_id
                    FROM  ?:products_categories as pc
                    WHERE pc.category_id IN (
                        SELECT c.category_id
                        FROM ?:categories as c
                        WHERE  id_path  LIKE CONCAT((SELECT c2.id_path FROM ?:categories as c2 WHERE c2.category_id = ?i),'%')
                        )
                    ORDER BY pc.product_id
                    ",
                    $categoryId
                )
            );
            $this->onProductUpdate((new IterableEvent())->setIterator($productIds));
        }
    }

    public function onOptionUpdate(IterableEvent $event)
    {
        foreach ($event as $optionId) {
            $productIds = new PdoColumnIterator(
                Database::prepare(
                    "
                    SELECT DISTINCT product_id
                    FROM  ?:product_global_option_links
                    WHERE option_id=?i
                    ",
                    $optionId
                )
            );

            $this->onProductUpdateAsync($productIds->toArray());
        }
    }

    /**
     * When a feature is edited, update the search engine config and restore products consistency
     */
    public function onFeatureUpdate(IterableEvent $event)
    {
        // on collecte et sépare les ids de produits et MVPs qui ont cet attribut
        $productIds = [];
        foreach ($event as $featureId) {
            $ids = $this->attributeService->getProductsFromFeatureId($featureId);
            foreach ($ids as $id) {
                $productIds[] = $id;
            }
        }

        // on trigger l'update des produits concernés
        $productIds = array_unique($productIds);
        $this->onProductUpdateAsync($productIds);
    }

    public function onFeatureVariantUpdate(IterableEvent $event)
    {
        // on collecte et sépare les ids de produits et MVPs qui ont cet attribut
        $productIds = iterator_to_array(
            $this->attributeService->getProductsFromVariantsId(
                iterator_to_array($event)
            )
        );

        $this->onProductUpdateAsync($productIds);
    }

    public function onCompanyUpdate(IterableEvent $event)
    {
        if ($event->getMetaData()['needProjection'] === false) {
            return false;
        }

        foreach ($event as $eventInfos) {
            $productIds = new PdoColumnIterator(
                Database::prepare(
                    "
                    SELECT product_id
                    FROM  ?:products
                    WHERE company_id=?i
                    ",
                    $eventInfos
                )
            );

            $this->eventDispatcher->dispatch(
                (new IterableEvent())->setIterator($productIds),
                Product::EVENT_UPDATE
            );
        }
    }

    public function onShippingVendorUpdate(IterableEvent $event)
    {
        foreach ($event as $eventInfos) {
            $productIds = new PdoColumnIterator(
                Database::prepare(
                    "
                    SELECT product_id
                    FROM  ?:products
                    WHERE company_id=?i
                      AND (
                        NOT FIND_IN_SET(?s, w_disable_shippings)
                        OR ISNULL(w_disable_shippings)
                      )
                    ",
                    $eventInfos['company_id'],
                    $eventInfos['shipping_id']
                )
            );
            $this->eventDispatcher->dispatch(
                (new IterableEvent())->setIterator($productIds),
                Product::EVENT_UPDATE
            );
        }
    }
}
