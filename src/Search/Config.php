<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Search;

class Config
{
    //Keys for configuration parameters of search engine
    public const CFG_SEARCH_ENGINE   = 'search_engine';
    public const SEARCH_ENGINE_CLASS = 'search_engine_class';

    //Indexes
    public const INDEX_PRODUCTS   = 'products';
    public const INDEX_CATEGORIES = 'categories';
    public const INDEX_GEOCODING  = 'geocoding';

    public const PARAMS_FACET      = 'facets';
    public const PARAMS_FACET_FILTER  = 'facetFilters';
    public const PARAMS_TAG_FILTER    = 'tagFilters';
    public const PARAMS_NUMERIC_FILTER    = 'numericFilters';
    public const PARAMS_HITS_PER_PAGE = 'hitsPerPage';
    public const PARAMS_PAGE          = 'page';
    public const PARAMS_TO_INDEX   = 'index';
    public const PARAMS_MAX_VALUES_PER_FACET = 'maxValuesPerFacet';
}
