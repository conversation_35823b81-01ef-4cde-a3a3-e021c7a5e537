<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha;

use MyCLabs\Enum\Enum;

/**
 * WARNING!!
 *
 * If you are using the following related resource contexts, please use the appropriate status type instead:
 * - company status: Wizacha\Marketplace\Company\CompanyStatus
 * - category status: Wizacha\Marketplace\Category\CategoryStatus
 * - product approved: Wizacha\Marketplace\Product\ProductApprovalStatus
 * - product status: Wizacha\Marketplace\Product\ProductStatus
 * - shipping status: Wizacha\Marketplace\Shipping\ShippingStatus
 * - etc...
 *
 * @method static Status ENABLED()
 * @method static Status DISABLED()
 * @method static Status HIDDEN()
 * @method static Status PENDING()
 */
final class Status extends Enum
{
    public const ENABLED   = 'A';
    public const DISABLED  = 'D';
    public const HIDDEN    = 'H';
    public const PENDING   = 'P';
}
