<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Cscart;

use MyCLabs\Enum\Enum;

/**
 * @method static self NOTICE()
 * @method static self WARNING()
 * @method static self ERROR()
 * @method static self SUCCESS()
 * @method static self INFORMATION()
 */
class NotificationType extends Enum
{
    private const NOTICE = 'N';
    private const WARNING = 'W';
    private const ERROR = 'E';
    private const SUCCESS = 'S';
    private const INFORMATION = 'I';

    public function __construct(string $value)
    {
        // Fallback value
        if (false === static::isValid($value)) {
            $value = static::WARNING()->getValue();
        }

        parent::__construct($value);
    }
}
