<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Cscart;

use MyCLabs\Enum\Enum;

/**
 * @method static AppearanceType INVOICE()
 * @method static AppearanceType CREDIT_MEMO()
 * @method static AppearanceType ORDER_OVERVIEW()
 */
class AppearanceType extends Enum
{
    public const INVOICE = 'I';
    public const CREDIT_MEMO = 'C';
    public const ORDER_OVERVIEW = 'O';
}
