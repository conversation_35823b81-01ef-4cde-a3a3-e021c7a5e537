<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Cscart;

use GuzzleHttp\Psr7\Uri;

//Untested methods and snake case for emphasis in code
class Common
{
    protected const FLATTEN_SEP_CHAR = '.';
    protected const FLATTEN_ESCAPE_STR = '\.';

    public static function fn_is_empty($var)
    {
        if (!\is_array($var)) {
            return (empty($var));
        } else {
            foreach ($var as $k => $v) {
                if (empty($v)) {
                    unset($var[$k]);
                    continue;
                }

                if (\is_array($v) && fn_is_empty($v)) {
                    unset($var[$k]);
                }
            }

            return (empty($var)) ? true : false;
        }
    }

    /**
     * Pour des soucis de générations des liens cscart avec le routeur Symfony,
     * notre RouterListener fait un
     *          $this->requestContext->setBaseUrl($request->server->get('SCRIPT_NAME'));
     * lorsque l'AREA est en A. Du coup, si on ne veut pas que pour les images
     * les urls soient de la forme '/admin.php/api/v1/image/XX' on doit
     * retirer manuellement le script_name
    */
    public static function normalizeApiUri(Uri $uri): Uri
    {
        return $uri->withPath(
            \preg_replace(
                "#^{$_SERVER['SCRIPT_NAME']}#",
                '',
                $uri->getPath()
            )
        );
    }

    public static function normalizeApiUriString(string $url): string
    {
        return (string) static::normalizeApiUri(
            new Uri($url)
        );
    }

    /**
     * flatten a nested array tree
     *
     * @return \Generator<mixed>
     */
    public static function arrayFlatten(array $data, ?string $path = null): \Generator
    {
        foreach ($data as $key => $value) {
            $escapedKey = \str_replace(
                static::FLATTEN_SEP_CHAR,
                static::FLATTEN_ESCAPE_STR,
                $key
            );

            $currentPath = \is_null($path)
                ? $escapedKey
                : $path . static::FLATTEN_SEP_CHAR . $escapedKey
            ;

            if (true === \is_array($value)) {
                yield from static::arrayFlatten($value, $currentPath);
            } else {
                yield $currentPath => $value;
            }
        }
    }

    // don't project everything
    public static function needProductsOfCompanyToBeprojected(array $from, array $to): bool
    {
        /**
         * Company data exposed in readmodel/algolia
         */
        $map = [
            'company',
            'corporate_name',
            'status'
        ];

        foreach ($map as $key) {
            if ($from[$key] != $to[$key]) {
                return true;
            }
        }

        return false;
    }
}
