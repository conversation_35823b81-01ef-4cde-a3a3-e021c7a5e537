<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Cscart;

/**
 * This class is a wrapper for cs cart functions.
 * Its purpose is only to pass calls to these functions, and allowing easy mocks in PHPUnit.
 * Do not add any logic here!
 */
class FnFunctions
{
    public function fn_get_profile_fields(...$args) // No return type, as the original one
    {
        return \fn_get_profile_fields(...$args);
    }

    public function fn_get_user_info(...$args) // No return type, as the original one
    {
        return \fn_get_user_info(...$args);
    }

    public function fn_check_shipping_billing(...$args) // No return type, as the original one
    {
        return \fn_check_shipping_billing(...$args);
    }

    public function fn_update_user(...$args) // No return type, as the original one
    {
        return \fn_update_user(...$args);
    }

    public function fn_get_notifications(...$args) // No return type, as the original one
    {
        return \fn_get_notifications(...$args);
    }
}
