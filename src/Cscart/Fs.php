<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Cscart;

//Untested methods and snake case for emphasis in code
class Fs
{
    public static function fn_mkdir($dir, $perms = DEFAULT_DIR_PERMISSIONS)
    {
        $result = false;

        if (!empty($dir)) {
            clearstatcache(true, $dir);
            if (@is_dir($dir)) {
                $result = true;
            } else {
                // Truncate the full path to related to avoid problems with some buggy hostings
                if (strpos($dir, DIR_ROOT) === 0) {
                    $dir = './' . substr($dir, \strlen(DIR_ROOT) + 1);
                    $old_dir = getcwd();
                    chdir(DIR_ROOT);
                }

                $dir = self::fn_normalize_path($dir, '/');
                $path = '';
                $dir_arr = array();
                if (strstr($dir, '/')) {
                    $dir_arr = explode('/', $dir);
                } else {
                    $dir_arr[] = $dir;
                }

                foreach ($dir_arr as $k => $v) {
                    $path .= (empty($k) ? '' : '/') . $v;
                    clearstatcache(true, $path);
                    if (!is_dir($path)) {
                        umask(0);
                        $result = @mkdir($path, $perms);
                        if (!$result) {
                            $parent_dir = \dirname($path);
                            $parent_perms = fileperms($parent_dir);
                            @chmod($parent_dir, 0777);
                            $result = @mkdir($path, $perms);
                            @chmod($parent_dir, $parent_perms);
                            if (!$result) {
                                break;
                            }
                        }
                    }
                }

                if (!empty($old_dir)) {
                    @chdir($old_dir);
                }
            }
        }

        return $result;
    }

    public static function fn_normalize_path($path, $separator = '/')
    {

        $result = array();
        $path = preg_replace("/[\\\\\/]+/S", $separator, $path);
        $path_array = explode($separator, $path);
        if (!$path_array[0]) {
            $result[] = '';
        }

        foreach ($path_array as $key => $dir) {
            if ($dir == '..') {
                if (end($result) == '..') {
                    $result[] = '..';
                } elseif (!array_pop($result)) {
                    $result[] = '..';
                }
            } elseif ($dir != '' && $dir != '.') {
                $result[] = $dir;
            }
        }

        if (!end($path_array)) {
            $result[] = '';
        }

        return \Wizacha\Cscart\Common::fn_is_empty($result) ? '' : implode($separator, $result);
    }
}
