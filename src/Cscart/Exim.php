<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Cscart;

//Untested methods and snake case for emphasis in code

use Psr\Log\LoggerInterface;
use Tygh\Bootstrap;
use Tygh\Languages\Languages;
use Tygh\Registry as TyghRegistry;
use Wizacha\Component\Import\EximJobService;
use Wizacha\Exim\Import\ImportReportMessage\EximMessage;
use Wizacha\Exim\Import\ImportReportMessage\ProductPriceAndStockMessage;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Image\Exception\BadImage;
use Wizacha\Marketplace\PIM\Option\Handler\SystemOptionsHandlerInterface;
use Wizacha\Marketplace\PIM\Option\SystemOptionsRegistry;
use Wizacha\Registry;
use Wizacha\Component\Import\Exception\InvalidStructureException;
use Wizacha\Exim\Misc;
use Wizacha\ImageManager;
use Wizacha\Marketplace\PriceTier\Repository\PriceTierRepository;
use Wizacha\Marketplace\PriceTier\Service\PriceTierService;

class Exim
{
    public static function exim_get_product_combination($product_id, $combination, $lang_code = null)
    {
        $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
        $selected_options = fn_get_product_options_by_combination($combination);
        $options = fn_get_selected_product_options($product_id, $selected_options, $lang_code);

        $return = array();

        if (!empty($options)) {
            foreach ($options as $option) {
                if (isset($selected_options[$option['option_id']])) {
                    $return[] = $option['option_name'] . ': ' . $option['variants'][$selected_options[$option['option_id']]]['variant_name'];
                }
            }
        }

        return implode('; ', $return);
    }
    public static function get_pattern_definition($pattern_id, $get_for = '')
    {
        // First, check basic patterns
        $schema = fn_get_schema('exim', $pattern_id);

        if (empty($schema)) {
            fn_set_notification('E', __('error'), __('error_exim_pattern_not_found'));

            return false;
        }

        if ((!empty($schema['export_only']) && $get_for == 'import') || (!empty($schema['import_only']) && $get_for == 'export')) {
            return array();
        }

        $has_alt_keys = false;

        foreach ($schema['export_fields'] as $field_id => $field_data) {
            if (!empty($field_data['table'])) {
                // Table exists in export fields, but doesn't exist in references definition
                if (empty($schema['references'][$field_data['table']])) {
                    fn_set_notification('E', __('error'), __('error_exim_pattern_definition_references'));

                    return false;
                }
            }

            // Check if schema has alternative keys to import basic data
            if (!empty($field_data['alt_key'])) {
                $has_alt_keys = true;
            }

            if ((!empty($field_data['export_only']) && $get_for == 'import') || (!empty($field_data['import_only']) && $get_for == 'export')) {
                unset($schema['export_fields'][$field_id]);
            }
        }

        if ($has_alt_keys == false
            && (empty($schema['import_skip_check_alt_keys']) || false === $schema['import_skip_check_alt_keys'])
        ) {
            fn_set_notification('E', __('error'), __('error_exim_pattern_definition_alt_keys'));

            return false;
        }

        return $schema;
    }

    //Change fonction to take product_code instead product_name
    public static function exim_put_product_combination(
        $product_id,
        $product_code,
        $combination_code,
        $combination,
        $amount,
        &$counter,
        $price = null,
        $crossedOutPrice = null,
        $affiliateLink = null,
        $jobId = null,
        $line = null,
        $urlImageCombination = null,
        $imagesDelimiter = null,
        $supplier_reference = null,
        $updateOnlyPriceAndStock = false,
        $altTextCombination = null,
        $langCode = null,
        $infiniteStock = null
    ) {
        $pair_delimiter = ':';
        $set_delimiter = ';';

        $multi_lang = array_keys($combination);
        $main_lang = reset($multi_lang);

        if (!empty($combination)) {
            // Get product_id
            $object_id = 0;
            if (!empty($product_id)) {
                $object_exists = db_get_field('SELECT COUNT(*) FROM ?:products WHERE product_id = ?i', $product_id);
                if ($object_exists) {
                    $object_id = $product_id;
                }
            }

            if (empty($object_id) && !empty($product_code)) {
                $object_id = db_get_field(
                    'SELECT product_id FROM ?:products WHERE product_code = ?s AND company_id = ?i',
                    $product_code,
                    \Tygh\Registry::get('runtime.company_id')
                );
            }

            if (empty($object_id)) {
                $counter['S']++;

                return false;
            }

            $options = array();
            foreach ($multi_lang as $lang_code) {
                $_options = explode($set_delimiter, $combination[$lang_code]);

                foreach ($_options as $key => $value) {
                    $options[$key][$lang_code] = $value;
                }
            }

            if (!empty($options)) {
                $_combination = array();
                $_combinationCopy = [];
                $_combinationWithAllVariantIds = [];

                $combinationByCode = null;
                if (\mb_strlen($combination_code) > 0 && $updateOnlyPriceAndStock === true) {
                    $combinationByCode = db_get_field("SELECT combination FROM cscart_product_options_inventory WHERE product_code = ?s AND product_id= ?s LIMIT 1", $combination_code, $product_id);
                }

                foreach ($options as $option_pair) {
                    $pair = explode($pair_delimiter, $option_pair[$main_lang]);
                    if (\is_array($pair)) {
                        array_walk($pair, 'fn_trim_helper');
                        $option_id = db_get_field("SELECT o.option_id FROM ?:product_options_descriptions as d INNER JOIN ?:product_options as o ON o.option_id = d.option_id AND o.product_id = ?i WHERE d.option_name = ?s AND d.lang_code = ?s LIMIT 1", $object_id, $pair[0], $main_lang);
                        if (empty($option_id)) {
                            // Search for the global product options
                            $option_id = db_get_field("SELECT o.option_id FROM ?:product_options_descriptions as d INNER JOIN ?:product_options as o ON o.option_id = d.option_id AND o.product_id = ?i WHERE d.option_name = ?s AND d.lang_code = ?s LIMIT 1", 0, $pair[0], $main_lang);
                        }

                        $optionCode = fn_get_system_option_code((int) $option_id);

                        if (false === \is_null($jobId) && false === \is_null($line) && false === \is_null($optionCode)) {
                            $registry = container()->get(SystemOptionsRegistry::class);
                            /** @var SystemOptionsHandlerInterface $handler */
                            $handler = $registry->getHandler($optionCode);

                            if ($optionCode === $handler->getSystemOptionCode() && \in_array($pair[1], $handler->getSystemOptionValues($main_lang)) === false) {
                                EximJobService::warning(EximMessage::EXIM_IMPORT_OPTION_SYSTEM_VARIANTS_FAIL, $jobId, $line, $product_id);

                                return false;
                            }
                        }

                        $variant_id = db_get_field("SELECT v.variant_id FROM ?:product_option_variants_descriptions as d INNER JOIN ?:product_option_variants as v ON v.variant_id = d.variant_id AND v.option_id = ?i WHERE d.variant_name = ?s AND d.lang_code = ?s LIMIT 1", $option_id, $pair[1], $main_lang);

                        // fn_wizacha_backend_design_w_exim_put_product_combination_set_option_variant will remove $option_id and $variant_id by reference on his process for return false.
                        // For priceTiers we need to return $combination. We stock datas and, if empty, return the combination
                        $copyOptionId = $option_id;
                        $copyVariant_id = $variant_id;

                        $variantIds = fn_wizacha_backend_design_w_exim_put_product_combination_set_option_variant($object_id, $pair, $option_id, $variant_id, $main_lang, $updateOnlyPriceAndStock);

                        if (empty($option_id) || empty($variant_id)) {
                            $counter['S']++;

                            if ($copyOptionId !== null && $copyVariant_id !== null) {
                                $_combinationCopy[$copyOptionId] = $copyVariant_id;
                            }

                            if (\is_string($urlImageCombination) === true && empty($urlImageCombination) === false) {
                                EximJobService::warning(
                                    EximMessage::EXIM_FAILED_TO_PROCESS_IMAGE_DECLINATION,
                                    $jobId,
                                    $line,
                                    $product_id,
                                    $urlImageCombination
                                );
                            }

                            if (\mb_strlen($combinationByCode) === 0 && $updateOnlyPriceAndStock === true) {
                                return false;
                            }
                        }

                        $variantStatus = db_get_field("SELECT v.status FROM ?:product_option_variants as v WHERE v.variant_id = ?i LIMIT 1", $variant_id);

                        if ($variantStatus === 'D') {
                            EximJobService::warning(EximMessage::EXIM_IMPORT_OPTION_SYSTEM_VARIANTS_DISABLED_FAIL, $jobId, $line, $product_id);

                            return false;
                        }

                        $_combination[$option_id] = $variant_id;
                        $_combinationWithAllVariantIds[$option_id] = $variantIds;
                    }
                }

                if (\count($_combinationCopy) > 1) {
                    return fn_get_options_combination($_combinationCopy);
                }

                $combination = fn_get_options_combination($_combination);
                $isValidCombinationCode = self::isValidCombinationCode($combinationByCode, $combination, $_combinationWithAllVariantIds);
                $combination_hash = fn_generate_cart_id($object_id, array('product_options' => $_combination));

                if ($isValidCombinationCode === false
                    && \mb_strlen($combination) > 0
                    && \mb_strlen($combination_code) > 0
                    && $updateOnlyPriceAndStock === true
                ) {
                    EximJobService::warning(ProductPriceAndStockMessage::EXIM_WRONG_PRODUCT_DECLINATION, $jobId, $line, $product_id);

                    return false;
                }

                if (\mb_strlen($combinationByCode) > 0
                    && $updateOnlyPriceAndStock === true
                ) {
                    $combination = $combinationByCode;
                }

                if (\mb_strlen($combination) === 0) {
                    return false;
                }

                $_data = array(
                    'product_id' => $object_id,
                    'combination_hash' => $combination_hash,
                    'combination' => $combination,
                );

                if (\is_string($supplier_reference) === true && \strlen($supplier_reference) > 0) {
                    $_data['supplier_reference'] = $supplier_reference;
                }

                if (\is_string($combination_code) === true && \strlen($combination_code) > 0) {
                    $_data['product_code'] = $combination_code;
                }

                if (is_numeric($amount)) {
                    $_data['amount'] = $amount;
                }

                if (isset($crossedOutPrice)) {
                    $_data['crossed_out_price'] = $crossedOutPrice;
                }

                if (!empty($affiliateLink)) {
                    $_data['affiliate_link'] = $affiliateLink;
                }

                if (empty($_data['product_code'])) {
                    unset($_data['product_code']);
                }
                if (!empty($price) || $price === "0") {
                    $_data['w_price'] = $price;
                }
                //Fix cscart bug : when amount is empty, cscart set the amount to 0.
                if ($_data['amount'] === '' || $_data['amount'] === null) {
                    unset($_data['amount']);
                }

                if (\is_string($infiniteStock)) {
                    if (\strtolower($infiniteStock) === 'y') {
                        $_data['infinite_stock'] = 1;
                    } elseif (\strtolower($infiniteStock) === 'n') {
                        $_data['infinite_stock'] = 0;
                    }
                }

                if ($_data['supplier_reference'] === '' || $_data['supplier_reference'] === null) {
                    unset($_data['supplier_reference']);
                }

                $hasOptionInventory = 0 < db_get_field(
                    'SELECT
                        COUNT(*)
                    FROM
                        ?:product_options_inventory
                    WHERE
                        combination = ?s
                        AND product_id = ?i
                    ',
                    $combination,
                    $object_id
                );

                if ($hasOptionInventory) {
                    db_query('UPDATE ?:product_options_inventory SET ?u WHERE combination = ?s AND product_id =?i', $_data, $combination, $object_id);

                    $counter['E']++;
                } elseif ($updateOnlyPriceAndStock === false) {
                    db_query('INSERT INTO ?:product_options_inventory ?e', $_data);

                    $counter['N']++;
                } else {
                    return false;
                }

                //set images combination
                //or delete images combination if the column exists and is empty
                if (\is_string($urlImageCombination) === true) {
                    static::putImageCombination($object_id . '_' . $combination, $urlImageCombination, $imagesDelimiter, $jobId, $line, $altTextCombination, $langCode);
                }

                return $combination;
            }
        }

        $counter['S']++;

        return false;
    }

    /** @param mixed[] $combinationWithAllVariantIds */
    private static function isValidCombinationCode(?string $combinationByCode, ?string $combination, array $combinationWithAllVariantIds): bool
    {
        if (\is_null($combinationByCode) === true || \mb_strlen($combinationByCode) === 0) {
            return false;
        }

        if (\mb_strlen($combination) > 0) {
            $productService = container()->get('marketplace.pim.product.service');
            $formattedCombinationsByCode = $productService->getProductOptionsIdByCombination($combinationByCode);

            $combinations = \array_map(
                static function ($variant): array {
                    return [$variant];
                },
                $formattedCombinationsByCode
            );

            foreach ($combinations as $key => $singleCombination) {
                if (\in_array(reset($singleCombination), $combinationWithAllVariantIds[$key]) === false) {
                    return false;
                }
            }
        }

        return true;
    }

    /** @param int[]|null $categoryIds */
    public static function exim_product_feature_variants(array $feature, int $feature_id, array $variants, string $lang_code, array $categoryIds = null): array
    {
        if (!$feature_id) {
            return $variants;
        }

        $feature_type = $feature['type'];

        if ($feature_type && strpos('MSNE', $feature_type) !== false) { // variant IDs
            $vars = array();
            foreach ($feature['variants'] as $variant) {
                $vars[] = $variant;
            }

            $existent_variants = db_get_hash_single_array(
                'SELECT pfvd.variant_id, variant FROM ?:product_feature_variant_descriptions AS pfvd ' .
                'LEFT JOIN ?:product_feature_variants AS pfv ON pfv.variant_id = pfvd.variant_id ' .
                'WHERE feature_id = ?i AND variant IN (?a) AND lang_code = ?s',
                array('variant_id', 'variant'),
                $feature_id,
                $vars,
                $lang_code
            );

            if ($feature_id == fn_w_get_brand_id($categoryIds)) {
                foreach ($feature['variants'] as $variant_data) {
                    if (!\in_array($variant_data, $existent_variants)) {
                        $variant_id = fn_add_feature_variant($feature_id, array('variant' => $variant_data), $categoryIds);
                        $existent_variants[$variant_id] = $variant_data;
                    }
                }
            }

            if ($feature_type == 'M') {
                foreach ($feature['variants'] as $variant_data) {
                    if (\in_array($variant_data, $existent_variants)) {
                        $variant_id = array_search($variant_data, $existent_variants);
                        $variants[$feature_id][$variant_id] = $variant_id;
                    }
                }
            } else {
                if (!\is_array($feature['variants'])) {
                    Registry::defaultInstance()->container->get('logger')->warning(
                        'Empty $feature[variants] for feature {feature_id}',
                        [
                            'feature_id' => $feature_id
                        ]
                    );
                } else {
                    $variant_data = reset($feature['variants']);

                    if (\in_array($variant_data, $existent_variants)) {
                        $variant_id = array_search($variant_data, $existent_variants);
                        $variants[$feature_id] = $variant_id;
                    }
                }
            }
        } else {
            if (!\is_array($feature['variants'])) {
                $variants[$feature_id] = null;
                Registry::defaultInstance()->container->get('logger')->warning(
                    'Empty $feature[variants] for feature {feature_id}',
                    [
                        'feature_id' => $feature_id
                    ]
                );
            } else {
                $variant_data = reset($feature['variants']);
                $variants[$feature_id] = $variant_data;
            }
        }

        return $variants;
    }

    public static function exim_check_feature_group($group, $company_id, $lang_code)
    {
        $group_id = db_get_field("SELECT feature_id FROM ?:product_features_descriptions WHERE description = ?s AND lang_code = ?s LIMIT 1", $group, $lang_code);

        if (empty($group_id)) {
            $group_data = array(
                'feature_id' => 0,
                'description' => $group,
                'lang_code' => $lang_code,
                'feature_type' => 'G',
                'company_id' => $company_id,
                'status' => 'A'
            );

            $group_id = fn_update_product_feature($group_data, 0, $lang_code);
        }

        return $group_id;
    }


    /**
     * @throws InvalidStructureException
     */
    public static function fn_analyze_schema(&$schema, $pattern): bool
    {
        [$schema_match, $schema, $failed_fields] = Misc::analyseSchema($schema, $pattern);

        if (0 < \count($failed_fields)) {
            $error_message = __(
                'error_exim_pattern_required_fields',
                [
                    '[fields]' => \implode(', ', $failed_fields)
                ]
            );

            fn_set_notification(
                'E',
                __('error'),
                $error_message
            );

            throw new InvalidStructureException($error_message);
        }

        if (false === $schema_match) {
            $error_message = __('error_exim_pattern_dont_match');

            fn_set_notification(
                'E',
                __('error'),
                $error_message
            );

            throw new InvalidStructureException($error_message);
        }

        return true;
    }

    /**
     * @return bool|array
     */
    public static function get_csv(array $pattern, string $file, array $options, bool $displayNotification = true)
    {
        $max_line_size = 65536; // 64 Кб
        $result = array();

        if ($options['delimiter'] == 'C') {
            $delimiter = ',';
        } elseif ($options['delimiter'] == 'T') {
            $delimiter = "\t";
        } elseif ($options['delimiter'] == 'P') {
            $delimiter = "|";
        } else {
            $delimiter = ';';
        }

        if (!empty($file) && file_exists($file)) {
            $encoding = fn_detect_encoding($file, 'F', !empty($options['lang_code']) ? $options['lang_code'] : (string) GlobalState::interfaceLocale());

            if (!empty($encoding)) {
                 $file = fn_convert_encoding($encoding, 'UTF-8', $file, 'F');
            } elseif ($displayNotification) {
                fn_set_notification('W', __('warning'), __('text_exim_utf8_file_format'));
            }

            $f = false;
            if ($file !== false) {
                $f = fopen($file, 'rb');
            }

            if ($f) {
                // Get import schema

                $import_schema = fgets($f, $max_line_size);

                // Remove BOM if exist
                if (substr($import_schema, 0, 3) == pack("CCC", 0xef, 0xbb, 0xbf)) {
                    $import_schema = substr($import_schema, 3);
                }

                $delimiter = \Wizacha\Exim\Misc::checkSeparator($import_schema, $delimiter);

                $import_schema = str_getcsv($import_schema, $delimiter);

                if (empty($import_schema)) {
                    $displayNotification && fn_set_notification('E', __('error'), __('error_exim_cant_read_file'));

                    return false;
                }

                // Check if we selected correct delimiter
                // If line was read without delimition, array size will be == 1.
                if (sizeof($import_schema) == 1) {
                    // we could export one column if it is correct, otherwise show error
                    if (!\in_array($import_schema[0], array_keys($pattern['export_fields']))) {
                        $displayNotification && fn_set_notification('E', __('error'), __('error_exim_incorrent_delimiter'));

                        return false;
                    }
                }

                // Analyze schema - check for required fields
                if (self::fn_analyze_schema($import_schema, $pattern) == false) {
                    return false;
                }

                // Collect data
                $schema_size = sizeof($import_schema);
                $skipped_lines = array();
                $line_it = 1;
                $count = 2;
                while (($data = fgetcsv($f, 0, $delimiter)) !== false) {
                    $line_it++;
                    if (fn_is_empty($data)) {
                        continue;
                    }

                    if (sizeof($data) != $schema_size) {
                        $skipped_lines[] = $line_it;
                        continue;
                    }

                    $datas = array_combine($import_schema, Bootstrap::stripSlashes($data));
                    $datas['line'] = $count++;

                    $result[] = $datas;
                }

                if (!empty($skipped_lines)) {
                    $displayNotification && fn_set_notification('W', __('warning'), __('error_exim_incorrect_lines', array(
                        '[lines]' => implode(', ', $skipped_lines)
                    )));
                }

                return $result;
            } else {
                $displayNotification && fn_set_notification('E', __('error'), __('error_exim_cant_open_file'));

                return false;
            }
        } else {
            $displayNotification && fn_set_notification('E', __('error'), __('error_exim_file_doesnt_exist'));

            return false;
        }
    }

    public static function fn_import_parse_languages(array $pattern, array &$import_data): bool
    {
        foreach ($pattern['export_fields'] as $field_name => $field) {
            if (!empty($field['type']) && $field['type'] == 'languages') {
                if (empty($field['db_field'])) {
                    $field_lang = $field_name;
                } else {
                    $field_lang = $field['db_field'];
                }
            }
        }

        // Languages
        $langs = array();

        // Get all lang from data
        foreach ($import_data as $k => $v) {
            if (!isset($v['lang_code']) || \in_array($v['lang_code'], $langs)) {
                break;
            }
            $langs[] = strtolower($v['lang_code']);
        }

        if (empty($langs)) {
            foreach ($import_data as $key => $data) {
                $import_data[$key]['lang_code'] = DEFAULT_LANGUAGE;
            }

            $langs[] = DEFAULT_LANGUAGE;
        }

        $langs = array_intersect($langs, array_keys(Languages::getAll()));

        if (empty($langs)) {
            container()->get('logger')->warning(
                'The following languages where not found in the database : {langs}',
                [
                    'langs' => $langs
                ]
            );

            return false;
        }

        $count_langs = \count($langs);

        $count_lang_data = array();
        foreach ($langs as $lang) {
            $count_lang_data[$lang] = 0;
        }

        $data = array();
        $result = true;
        if (isset($field_lang)) {
            foreach ($import_data as $v) {
                if (!empty($v[$field_lang]) && \in_array(strtolower($v[$field_lang]), $langs)) {
                    $data[] = $v;
                    $count_lang_data[strtolower($v[$field_lang])]++;
                }
            }

            // Check
            $count_data = reset($count_lang_data);
            foreach ($langs as $lang) {
                if ($count_lang_data[$lang] != $count_data) {
                    $result = false;
                    break;
                }
            }

            if ($result) {
                // Chunk on languages
                $data_lang = array_chunk($data, $count_langs);
                $data = array();

                foreach ($data_lang as $data_key => $data_value) {
                    foreach ($data_value as $v) {
                        $lower_field_lang = strtolower($v[$field_lang]);
                        $data[$data_key][$lower_field_lang] = $v;
                        $data[$data_key][$lower_field_lang][$field_lang] = strtolower($data[$data_key][$lower_field_lang][$field_lang]);
                    }
                }

                $import_data = $data;
            }
        } else {
            $main_lang = reset($langs);
            foreach ($import_data as $data_key => $data_value) {
                $data[$data_key][$main_lang] = $data_value;
            }
            $import_data = $data;
        }

        return $result;
    }

    public static function fn_import_build_groups(string $type_group, array $export_fields): array
    {
        $groups = array();
        if (!empty($type_group)) {
            foreach ($export_fields as $field => $data) {
                $db_field = (empty($data['db_field']) ? $field : $data['db_field']);
                if (!empty($data[$type_group])) {
                    $args = $data[$type_group];
                    $function = array_shift($args);
                    $groups[] = array (
                        'function' => $function,
                        'this_field' => $db_field,
                        'args' => $args,
                        'table' => !empty($data['table']) ? $data['table'] : '',
                        'multilang' => !empty($data['multilang']) ? true : false,
                        'return_result' => !empty($data['return_result']) ? $data['return_result'] : false,
                    );
                }
            }
        }

        return $groups;
    }

    public static function fn_import_prepare_groups(array &$data, array $groups, array $options, bool $skip_record = false): bool
    {
        if (!empty($groups)) {
            foreach ($groups as $group) {
                if (!isset($data[$group['this_field']])) {
                    continue;
                }
                $vars = array(
                    'lang_code' => !empty($data['lang_code']) ? $data['lang_code'] : '',
                    'field' => $group['this_field']
                );

                $params = self::fn_exim_get_values($group['args'], array(), $options, $vars, $data, '');
                $params[] = & $skip_record;

                $data[$group['this_field']] = \call_user_func_array($group['function'], $params);
            }
        }

        return true;
    }

    public static function fn_exim_get_values(array $values, array $pattern, array $options, array $vars = [], array $data = [], string $quote = "'"): array
    {
        $val = array();

        foreach ($values as $field => $value) {
            if (\is_array($value)) {
                $val[$field] = self::fn_exim_set_quotes($value);
            } else {
                $operator = substr($value, 0, 1);

                if ($operator === '@') {
                    $opt = str_replace('@', '', $value);
                    $val[$field] = (isset($options[$opt])) ? self::fn_exim_set_quotes($options[$opt], $quote) : '';
                } elseif ($value === '#this') {
                    if (!empty($vars['field'])) {
                        $val[$field] = self::fn_exim_set_quotes($data[$vars['field']], $quote);
                    } else {
                        $val[$field] = self::fn_exim_set_quotes($data[$field], $quote);
                    }
                } elseif ($value === '#key') {
                    $val[$field] = (sizeof($vars['key']) == 1) ? reset($vars['key']) : (isset($vars['key'][$field]) ? $vars['key'][$field] : $vars['key']);
                } elseif ($value === '#key.to_char') {
                    $key = (sizeof($vars['key']) == 1) ? reset($vars['key']) : (isset($vars['key'][$field]) ? $vars['key'][$field] : $vars['key']);
                    $val[$field] = "CONVERT({$key} USING utf8)";
                } elseif ($operator === '&') {
                    $val[$field] = $pattern['table'] . '.' . substr($value, 1);
                } elseif ($value === '#field') {
                    if (!empty($vars['field'])) {
                        $val[$field] = $vars['field'];
                    } else {
                        $val[$field] = $field;
                    }
                } elseif ($value === '#lang_code') {
                    $val[$field] = !empty($vars['lang_code']) ? self::fn_exim_set_quotes($vars['lang_code'], $quote) : '';
                } elseif ($value === '#row') {
                    $val[$field] = $data;
                } elseif ($value === '#company_id') {
                    $val[$field] = \Tygh\Registry::get('runtime.company_id');
                } elseif ($operator === '$') {
                    $opt = str_replace('$', '', $value);
                    if (\array_key_exists($opt, $data)) {
                        $data[$opt] = self::fn_exim_set_quotes($data[$opt], $quote);
                        $val[$field] = &$data[$opt];
                    } else {
                        $val[$field] = '';
                    }
                } else {
                    $val[$field] = self::fn_exim_set_quotes($value, $quote);
                }
            }
        }

        return $val;
    }

    public static function fn_exim_set_quotes($value, string $quote = "'")
    {
        if (!$quote) {
            return $value;
        }
        if (\is_array($value) && !empty($value)) {
            $result = [];
            foreach ($value as $k => $v) {
                $result[$k] = self::fn_exim_set_quotes($v, $quote);
            }
        } elseif (\gettype($value) == 'string') {
            $result = $quote . $value . $quote;
        } else {
            $result = $value;
        }

        return $result;
    }

    public static function fn_exim_processing($type_processing, $processing, $options, $vars = [])
    {
        $result = true;

        foreach ($processing as $data) {
            if ((!empty($data['import_only']) && $type_processing == 'export') || (!empty($data['export_only']) && $type_processing == 'import')) {
                continue;
            }

            $args = self::fn_exim_get_values($data['args'], array(), $options, array(), $vars, '');
            $result = $result && \call_user_func_array($data['function'], $args);
        }

        return $result;
    }

    /**
     * @param string $declinationId
     * @param string $data images URL separated with $images_delimiter
     * @param string $imagesDelimiter
     * @param string $jobId
     * @param string $lines
     * @param string $dataAltText
     * @param string $langCode
     * @throws \Exception
     */
    public static function putImageCombination(string $declinationId, string $data, string $imagesDelimiter, string $jobId = null, string $lines = null, string $dataAltText = null, string $langCode = null): void
    {
        if (ImageManager::BYPASS_URL === $data) {
            return;
        }

        $start = microtime(true);

        $images_manager = new ImageManager(container()->get("Wizacha\Storage\ImagesStorageService"));
        $images = explode($imagesDelimiter, $data);

        if (\count($images) > 1) {
            EximJobService::warning(EximMessage::EXIM_FAILED_TO_PROCESS_IMAGE_DECLINATION, $jobId, $lines, $declinationId, $data);

            return;
        }

        $images = array_filter(
            $images,
            function ($image_url) use ($declinationId, $jobId, $lines): bool {
                $valid = \in_array(fn_strtolower(fn_get_file_ext(parse_url($image_url, PHP_URL_PATH))), ['', 'jpg', 'png', 'gif', 'jpeg', 'ico']);
                if ($valid === false) {
                    EximJobService::warning(EximMessage::EXIM_UNSUPPORTED_IMAGE_EXTENSION, $jobId, $lines, $declinationId, $image_url);
                    fn_set_notification('E', __(EximMessage::EXIM_UNSUPPORTED_IMAGE_EXTENSION), $image_url);
                }

                return $valid;
            }
        );

        $images_id = array_map(function ($url) use ($images_manager) {
            try {
                return $images_manager->createByUrl($url, false);
            } catch (BadImage $e) {
                return null;
            }
        }, $images);

        $images_id = array_filter(
            $images_id,
            function ($id, $index) use ($images, $declinationId, $jobId, $lines): bool {
                if ($id === null) {
                    EximJobService::warning(EximMessage::EXIM_FAILED_TO_PROCESS_IMAGE_DECLINATION, $jobId, $lines, $declinationId, $images[$index]);
                }

                return (bool) $id;
            },
            ARRAY_FILTER_USE_BOTH
        );
        $images_manager->createPairs($images_id, 'declinations', $declinationId);

        $elapsed = microtime(true) - $start;

        /** @var LoggerInterface */
        $logger = container()->get('logger');
        $logger->notice(
            sprintf(
                "%s::%s stats",
                __NAMESPACE__,
                __FUNCTION__
            ),
            [
                "duration" => $elapsed,
                "jobId" => $jobId,
                "urls" => $images,
            ]
        );

        // save Alt Text
        if ($dataAltText !== null && \count($images_id) > 0) {
            $images_manager->setImageAltText($images_id[0], $dataAltText, $langCode);
        }
    }

    public static function eximUpdatePriceTiers(
        ?string $combination,
        bool $priceTierflag,
        array $declination_data,
        array $datas,
        bool $priceTierRepository,
        ?int $productId,
        array $productsInventories,
        string $jobId = null,
        string $line = null
    ): void {
        if ($combination === null) {
            $combination = false;
        }

        if (\array_key_exists('Price tiers', $declination_data) === true
            && \strlen($declination_data['Price tiers']) > 0
            && $priceTierflag === true
        ) {
            $declination_data['Price tiers']
                = str_replace('[', '', $declination_data['Price tiers']);
            $declination_data['Price tiers']
                = str_replace(']', '', $declination_data['Price tiers']);
            $priceTiers = explode(
                $options['feature_delimiter'] ?? '///',
                $declination_data['Price tiers']
            );

            // if Empty, the priceTiers have a bad format
            if (\is_string($priceTiers[0]) === true) {
                $first = true;
                // Build the imported PriceTiers
                foreach ($priceTiers as $singlePriceTier) {
                    $singlePriceTier = explode(':', $singlePriceTier);
                    if ($combination === false) {
                        $productsInventories[$combination]['combination'] = [];
                    } else {
                        $productsInventories[$combination]['combination'] = $combination;
                    }

                    if (is_numeric($singlePriceTier[0]) === false
                        || is_numeric($singlePriceTier[1]) === false
                    ) {
                        EximJobService::warning(
                            EximMessage::EXIM_SAVE_PRICE_TIERS_FAIL,
                            $jobId,
                            $line,
                            $product_data['product_code'] ?? $productId
                        );
                        unset($productsInventories[$combination]);
                        break;
                    }

                    // Log if price != PriceTier[0], price is ignored.
                    if ($first === true
                        && \array_key_exists('price', $datas) === true
                        && $datas['price'] !== ""
                        && (float) $singlePriceTier[1] !== (float) $datas['price']
                    ) {
                        EximJobService::warning(EximMessage::EXIM_PRICE_TIERS_AND_PRICE, $jobId, $line);
                    }

                    $productsInventories[$combination]['priceTiers'][] = [
                        'lowerLimit' => (int) $singlePriceTier[0],
                        'price' => (float) $singlePriceTier[1],
                    ];
                    $first = false;
                }
            } else {
                EximJobService::warning(
                    EximMessage::EXIM_SAVE_PRICE_TIERS_FAIL,
                    $jobId,
                    $line,
                    $product_data['product_code'] ?? $productId
                );
            }
        } elseif ($priceTierflag === false
            && \array_key_exists('Price tiers', $declination_data) === true
            && \strlen($declination_data['Price tiers']) > 0
        ) {
            EximJobService::warning(
                EximMessage::EXIM_SAVE_PRICE_TIERS_NO_FEATURE_FLAG,
                $jobId,
                $line,
                $product_data['product_code'] ?? $productId
            );
        }

        // The if is here to avoid chain binding, it's a foreach, nothing else.
        if ($priceTierRepository === false) {
            /** @var PriceTierRepository */
            $priceTierRepository = container()->get('marketplace.price_tier.price_tier_repository');
        }

        $priceTiersByCombinationCode = $priceTierRepository->findPriceTiersByCombinationCode(
            $combination,
            $productId
        );

        if (isset($productsInventories[$combination]) === false
            || (\array_key_exists('Price tiers', $declination_data) === false
            && \count($priceTiersByCombinationCode) === 0)
        ) {
            $productsInventories[$combination]['combination'] = $combination;
            $productsInventories[$combination]['priceTiers'][0]['lowerLimit'] = 0;

            if (\array_key_exists('Price tiers', $declination_data) === false
                && \array_key_exists('price', $declination_data) === false
            ) {
                $productsInventories[$combination]['priceTiers'][0]['price'] = fn_get_product_price(
                    $productId,
                    1
                );
            } else {
                $productsInventories[$combination]['priceTiers'][0]['price']
                    = $declination_data['price'] ? (float) $declination_data['price'] : 0;
            }
        }

        $savePriceTiersErrors = [];
        if (isset($productsInventories[$combination])) {
            $productsInventories[$combination]['combination'] = $combination;
            $productsInventories[$combination]['price'] = $datas['price'] ?? 0;

            /** @var PriceTierService */
            $priceTierService = container()->get('marketplace.price_tier.price_tier_service');
            $savePriceTiersErrors = $priceTierService->savePriceTiers(
                $productId,
                $productsInventories
            );
        }

        if (\count($savePriceTiersErrors) > 0) {
            foreach ($savePriceTiersErrors as $error) {
                EximJobService::warning(
                    EximMessage::EXIM_SAVE_PRICE_TIERS_FAIL,
                    $jobId,
                    $line,
                    $product_data['product_code'] ?? $productId,
                    $error->getMessage()
                );
            }
        }
    }

    public static function getCsvOutputFilePath(string $filename): string
    {
        static $path = [];

        return
            $path[$filename]
            ??= static::processCsvOutputFilePath($filename)
        ;
    }

    private static function processCsvOutputFilePath(string $filename): string
    {
        $dir = TyghRegistry::get('config.dir.exim');
        fn_mkdir($dir);

        return $dir . $filename;
    }

    public static function getCsvOutputFileDescriptor(string $filename)
    {
        return \fopen(
            static::getCsvOutputFilePath($filename),
            'wb'
        );
    }
}
