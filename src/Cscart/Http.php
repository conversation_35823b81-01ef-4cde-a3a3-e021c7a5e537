<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Cscart;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use Guz<PERSON>Http\Psr7\Response;
use GuzzleHttp\RequestOptions;
use GuzzleHttp\TransferStats;
use Psr\Log\LoggerInterface;
use Wizacha\AppBundle\Exception\Validator\InvalidUrlException;
use Wizacha\AppBundle\Validator\UrlValidator;

class Http
{
    public const CONNECTION_TIMEOUT = 5; #seconds

    public static function get(
        string $path,
        bool $decodeContent = true
    ): ?Response {
        // Prepare url
        $path = \str_replace(' ', '%20', $path);

        /** @var Client */
        $client = container()->get('guzzle.client');
        /** @var UrlValidator */
        $urlValidator = container()->get('app.validator.url_validator');
        /** @var LoggerInterface */
        $logger = container()->get('logger');

        try {
            return $client
                ->get(
                    $path,
                    [
                        'decode_content' => $decodeContent,
                        'version' => 2, # enable HTTP/2
                        'connection_timeout' => static::CONNECTION_TIMEOUT,
                        RequestOptions::ON_STATS => function (TransferStats $stats) use ($urlValidator) {
                            $uri = $stats->getEffectiveUri();
                            if (false === $urlValidator->isUrlFqdnValid($uri)) {
                                throw new InvalidUrlException($uri);
                            }
                        }
                    ]
                )
            ;
        } catch (InvalidUrlException $exception) {
            $logger->error(
                'URL provided does not pass SSRF security check',
                ['exception' => $exception]
            );
        } catch (ClientException $exception) {
            $logger->notice(
                'File download error',
                ['exception' => $exception]
            );
        } catch (\Exception $exception) {
            $logger->error(
                \sprintf(
                    'Unknown %s exception',
                    __CLASS__
                ),
                [
                    'url' => $path,
                    'exception' => $exception,
                ]
            );
        }

        return null;
    }
}
