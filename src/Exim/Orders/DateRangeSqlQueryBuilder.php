<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\Orders;

use Wizacha\Exim\Exception\InvalidFormatDateTimeException;

class DateRangeSqlQueryBuilder
{
    public const ALLOWED_SECTION_NAME = 'orders';

    private string $currentFrom = '';

    private string $currentTo = '';

    private string $currentSection = '';

    private string $currentDateInputFormat = '';

    /**
     * DateRangeSqlQueryBuilder constructor.
     *
     * @param string $currentFrom An input from date from client
     * @param string $currentTo An input to date from client
     * @param string $currentSection The current section from client (e.g : orders, accounting ...)
     * @param string $currentDateInputFormat The current input date format from client (eg: d/m/Y ...)
     */
    public function __construct(
        string $currentFrom,
        string $currentTo,
        string $currentSection,
        string $currentDateInputFormat
    ) {
        $this->currentFrom = $currentFrom;
        $this->currentTo = $currentTo;
        $this->currentSection = $currentSection;
        $this->currentDateInputFormat = $currentDateInputFormat;
    }

    public function buildRangeQuery(): string
    {
        $builtRequest = '';

        if (true === $this->currentSectionIsOrders()
            && false === $this->currentDateRangeValueAreEmpty()
        ) {
            // we use the creation date (timestamp field in DB) to make the query because this
            // field is used in the exported data file
            $builtRequest = "orders.timestamp"
                . " BETWEEN "
                . $this->getTimestamp('currentFrom')
                . " AND "
                . $this->getTimestamp('currentTo');
        }

        return $builtRequest;
    }

    private function currentSectionIsOrders(): bool
    {
        return $this->currentSection === self::ALLOWED_SECTION_NAME;
    }

    private function currentDateRangeValueAreEmpty(): bool
    {
        return '' === $this->currentFrom
            || '' === $this->currentTo;
    }

    private function getTimestamp(string $type): int
    {
        /*
          we force the static format for H:i:s to 00:00:00 for type === currentFrom and 23:59:59  for type === currentTo
          because the input format is only a string for type === currentFrom
          like : d/m/Y and if we use the dynamic format the from and the to date will be the same
        */
        $dateTime = \DateTime::createFromFormat($this->currentDateInputFormat, $type === 'currentFrom' ? $this->currentFrom : $this->currentTo);

        if ($dateTime instanceof \DateTime === true) {
            $formatted = $dateTime->format($type === 'currentFrom' ? 'Y-m-d 00:00:00' : 'Y-m-d 23:59:59');

            return (new \DateTime($formatted))->getTimestamp();
        }

        throw new InvalidFormatDateTimeException($this->currentDateInputFormat, $type === 'currentFrom' ? $this->currentFrom : $this->currentTo, \DateTime::getLastErrors());
    }
}
