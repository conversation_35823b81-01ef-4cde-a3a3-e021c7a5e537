<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim;

use Tygh\Languages\Languages;
use Tygh\Registry;
use Wizacha\Component\Import\EximJobService;
use Wizacha\Component\Locale\Locale;
use Wizacha\Cscart\Exim;
use Wizacha\Exim\Import\ImportReportMessage\ProductAttributesMessage;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\PIM\Attribute\AttributeType;
use Wizacha\Marketplace\PIM\Product\ProductService;
use Wizacha\Product;

class ProductAttributes
{
    private static string $jobId;

    private static ?int $line;

    private static ?ProductService $currentProductService = null;

    private static ?array $allAttributes = null;

    private static string $currentLangCode;

    private static int $companyId;

    private static int $productId;

    private static bool $freeAttribute;

    /** @var mixed[]|null */
    private static ?array $currentAttribute;

    private static string $companyCondition;

    private static string $languages;

    private static int $progress = 1;

    /** @var string[]|null */
    private static ?array $allLanguages = null;

    /** @var int[]|null */
    private static ?array $allCompanies = null;

    /** @var int[] */
    private static array $productCategories;

    /**
     * @var string[] $cols
     * @return mixed[]
     */
    public static function getData(array $cols, $options): array
    {
        static::$languages = implode(', ', Exim::fn_exim_set_quotes($options['lang_code']));

        static::$companyCondition = '';
        if (0 !== $options['company_id']) {
            static::$companyCondition = ' AND cscart_products.company_id = ' . $options['company_id'];
        }

        static::$companyId = $options['company_id'];

        $data = [];

        // Retrieve all attributes
        static::getAttributes($data);

        // Retrieve all free attributes (metadata)
        static::getFreeAttributes($data);

        $result = [];

        foreach ($data as $line) {
            // Returns an array of attributes and free attributes,
            // filtered on the selected columns
            $result[] = array_intersect_key($line, array_flip($cols));
        }

        return $result;
    }

    /** @param mixed[] $data */
    private static function getAttributes(array &$data): void
    {
        $query = sprintf(
            "SELECT a.lang_code, a.feature_id,
group_concat(a.variant_id separator '') AS variant_id,
group_concat(b.variant separator ';') AS variants,
group_concat(a.value separator '') AS value,
group_concat(a.value_int separator '') AS value_int,
d.feature_type, d.categories_path, c.description, cscart_products.product_code, cscart_products.company_id, cscart_products.product_id
FROM cscart_product_features_values a
LEFT JOIN cscart_product_feature_variant_descriptions b ON b.variant_id = a.variant_id AND b.lang_code = a.lang_code
LEFT JOIN cscart_product_features_descriptions c ON c.feature_id = a.feature_id AND c.lang_code = a.lang_code
LEFT JOIN cscart_product_features d ON d.feature_id = a. feature_id
RIGHT JOIN cscart_products ON cscart_products.product_id = a.product_id %s
WHERE a.lang_code in (%s) and d.status = 'A' and CHAR_LENGTH(a.product_id) < 36
GROUP BY a.lang_code, a.product_id, a.feature_id
ORDER BY a.product_id, a.feature_id, a.lang_code",
            static::$companyCondition,
            static::$languages
        );

        $attributes = db_get_array($query);

        foreach ($attributes as $attribute) {
            if (false === static::checkCategories($attribute)) {
                continue;
            }

            $value = static::getValue($attribute);

            // Case of a Date attribute which is not deleted in DB in case of reset
            if ('' === $value) {
                continue;
            }

            fn_set_progress('echo', __('exporting_data') . ':&nbsp;<b>' . (static::$progress++)  . '</b>');

            $data[] = [
                'Language' => $attribute['lang_code'],
                'Product code' => $attribute['product_code'],
                'Company id' => $attribute['company_id'],
                'Free' => '',
                'Id' => $attribute['feature_id'],
                'Name' => $attribute['description'],
                'Value' => $value ?? '',
            ];
        }
    }

    /**
     * Get the product categories and check the matching with the attribute categories
     * @param mixed[] $attribute
     */
    private static function checkCategories(array $attribute): bool
    {
        $categories = db_get_field("SELECT c.id_path FROM ?:categories AS c LEFT JOIN ?:products_categories AS pc ON c.category_id = pc.category_id WHERE pc.product_id = ?i", $attribute['product_id']);

        $productCategories = (true === \is_string($categories))
            ? explode('/', $categories)
            : [];

        if ('' !== $attribute['categories_path']) {
            return static::areCategoriesMatching(
                explode(',', $attribute['categories_path']),
                $productCategories
            );
        }

        return true;
    }

    /**
     * Check the product categories match the attribute categories
     * @param string[] $attributeCategories
     * @param string[] $productCategories
     */
    private static function areCategoriesMatching(array $attributeCategories, array $productCategories): bool
    {
        foreach ($attributeCategories as $category) {
            if (true === \in_array($category, $productCategories)) {
                return true;
            }
        }

        return false;
    }

    /**
     * @param mixed[] $attribute
     */
    private static function getValue(array $attribute): string
    {
        switch ($attribute['feature_type']) {
            case 'M':
            case 'N':
            case 'S':
            case 'E':
                return $attribute['variants'];
            case 'D':
                // Specific case: when the date is removed, the record stays in DB with NULL value
                if (\is_null($attribute['value_int'])) {
                    return '';
                }

                $date = new \DateTime();
                $date->setTimestamp((int) $attribute['value_int']);

                return $date->format('Y-m-d');
            case 'O':
                return $attribute['value_int'];
            case 'C':
            case 'T':
            default:
                return $attribute['value'];
        }
    }

    /** @param mixed[] $data */
    private static function getFreeAttributes(array &$data): void
    {
        $query = sprintf(
            "SELECT a.lang_code, a.product_id, a.metadata,
cscart_products.product_code, cscart_products.company_id
FROM cscart_product_metadata a
RIGHT JOIN cscart_products ON cscart_products.product_id = a.product_id %s
WHERE a.lang_code in (%s) and metadata_type = '%s' and CHAR_LENGTH(a.product_id) < 36
ORDER BY a.product_id, a.lang_code",
            static::$companyCondition,
            static::$languages,
            Product::METADATA_KEY_FREE_FEATURES
        );

        $metadata = db_get_array($query);

        foreach ($metadata as $meta) {
            $freeAttributes = unserialize($meta['metadata']);

            foreach ($freeAttributes as $name => $values) {
                foreach ($values as $value) {
                    fn_set_progress('echo', __('exporting_data') . ':&nbsp;<b>' . (static::$progress++)  . '</b>');

                    $data[] = [
                        'Language' => $meta['lang_code'],
                        'Product code' => $meta['product_code'],
                        'Company id' => $meta['company_id'],
                        'Free' => 'Y',
                        'Id' => '',
                        'Name' => $name,
                        'Value' => $value,
                    ];
                }
            }
        }
    }

    public static function put(array $data, string $jobId)
    {
        static::$jobId = $jobId;
        static::$line = $data['line'];

        static::$currentAttribute = null;

        // Get and check the Language
        if (false === static::getAndCheckLanguage($data)) {
            return false;
        }

        $runtimeCompanyId = Registry::get('runtime.company_id');

        // Get and check the Company id
        if (false === static::getAndCheckCompanyId($runtimeCompanyId, $data)) {
            return false;
        }

        if (false === static::$currentProductService instanceof ProductService) {
            static::$currentProductService = container()->get(ProductService::class);
        }

        // Get and check the Product code
        if (false === static::getAndCheckProductCode($data)) {
            return false;
        }

        // Get and check the Free
        if (false === static::getAndCheckFree($data)) {
            return false;
        }

        // Create/Delete Free attribute
        if (true === static::$freeAttribute) {
            $product = new Product(static::$productId);

            $free = $product->getFreeFeatures(
                new Locale(static::$currentLangCode)
            );

            if ('' === $data['Value']) {
                if (false === \in_array($data['Name'], \array_keys($free))) {
                    static::warning(
                        __(
                            ProductAttributesMessage::EXIM_PRODUCT_ATTRIBUTES_NOT_FOUND_ATTRIBUTE,
                            [
                                '[name]' => empty($data['Id'])
                                    ? $data['Name']
                                    : 'feature_id ' . $data['Id'],
                                '[product_code]' => $data['Product code']
                            ]
                        )
                    );

                    return false;
                }

                unset($free[$data['Name']]);
            } else {
                // If multiple values exist for the attribute name,
                // they are replaced by the current value (only one value at the end).
                $free[$data['Name']] = $data['Value'];
            }

            $product->setFreeFeatures(
                $free,
                new Locale(static::$currentLangCode)
            );

            if ('' === $data['Value']) {
                static::info(__(ProductAttributesMessage::EXIM_PRODUCT_ATTRIBUTES_DELETE));
            } else {
                static::info(__(ProductAttributesMessage::EXIM_PRODUCT_ATTRIBUTES_CREATE));
            }

            return true;
        }

        // Get and check name/id attribute
        if (false === static::getAndCheckNameOrIdAttribute($data)) {
            return false;
        }

        // Get all the attributes (multi languages)
        static::getAllAttributes($data);

        // Get and check the attribute name and value
        if (false === static::getAndCheckAttribute($data)) {
            return false;
        }

        // Set the attribute
        fn_update_product_features_value(
            static::$productId,
            static::$currentAttribute,
            [],
            static::$currentLangCode
        );

        if ('' === $data['Value']) {
            static::info(__(ProductAttributesMessage::EXIM_PRODUCT_ATTRIBUTES_DELETE));
        } else {
            static::info(__(ProductAttributesMessage::EXIM_PRODUCT_ATTRIBUTES_CREATE));
        }

        return true;
    }

    /** @param mixed[] $data */
    private static function getAndCheckLanguage(array $data): bool
    {
        if (\is_null(static::$allLanguages)) {
            static::$allLanguages = array_keys(Languages::getAll());
        }

        if (true === empty($data['Language'])) {
            static::$currentLangCode = (string) GlobalState::interfaceLocale();

            return true;
        }

        if (false === \in_array($data['Language'], static::$allLanguages)) {
            static::error(__(
                ProductAttributesMessage::EXIM_PRODUCT_ATTRIBUTES_INVALID_FIELDS,
                [
                    '[value]' => $data['Language'],
                    '[name]' => 'Language',
                    '[accepted]' => implode(', ', static::$allLanguages),
                ]
            ));

            return false;
        }

        static::$currentLangCode = $data['Language'];

        return true;
    }

    /** @param mixed[] $data */
    private static function getAndCheckCompanyId(int $runtimeCompanyId, array $data): bool
    {
        $companyId = $runtimeCompanyId;

        // no runtime company_id (admin)
        if (0 === $companyId) {
            // Read the company from the data
            $companyId = (int) $data['Company id'] ?? 0;
        }

        // Error: neither runtime company, neither Company id in the csv file
        if (0 === $companyId) {
            static::error(__(
                ProductAttributesMessage::EXIM_PRODUCT_ATTRIBUTES_REQUIRED_FIELD,
                [
                    '%' => 'Company id',
                ]
            ));

            return false;
        }

        // Check the company id exists
        return static::isValidCompanyId($companyId);
    }

    private static function isValidCompanyId(int $companyId): bool
    {
        if (\is_null(static::$allCompanies)) {
            static::$allCompanies = \array_keys(
                db_get_hash_array("SELECT company_id FROM ?:companies", 'company_id')
            );
        }

        if (false === \in_array($companyId, static::$allCompanies)) {
            static::error(__(
                ProductAttributesMessage::EXIM_PRODUCT_ATTRIBUTES_NOT_FOUND_COMPANY_ID,
                [
                    '[company_id]' => $companyId
                ]
            ));

            return false;
        }

        static::$companyId = $companyId;

        return true;
    }

    /** @param mixed[] $data */
    private static function getAndCheckProductCode(array $data): bool
    {
        $sql = <<<'SQL'
SELECT a.product_id, c.id_path
FROM ?:products as a
LEFT JOIN ?:products_categories AS b ON b.product_id = a.product_id
LEFT JOIN ?:categories c ON c.category_id = b.category_id
WHERE a.product_code = ?s AND a.company_id = ?i
SQL;
        $product = db_get_row($sql, $data['Product code'], static::$companyId);

        $productId = (int) $product['product_id'];

        static::$productCategories = (true === \is_string($product['id_path']))
            ? explode('/', $product['id_path'])
            : [];

        if (0 === $productId) {
            static::error(__(
                ProductAttributesMessage::EXIM_PRODUCT_ATTRIBUTES_NOT_FOUND_PRODUCT_CODE,
                [
                    '[product_code]' => $data['Product code'],
                    '[company_id]' => static::$companyId,
                ]
            ));

            return false;
        }

        static::$productId = $productId;

        return true;
    }

    /** @param mixed[] $data */
    private static function getAndCheckFree(array $data): bool
    {
        if (false === empty($data['Free'])
            && 'Y' !== $data['Free']
            && 'N' !== $data['Free']
        ) {
            static::error(__(
                ProductAttributesMessage::EXIM_PRODUCT_ATTRIBUTES_INVALID_FIELDS,
                [
                    '[value]' => $data['Free'],
                    '[name]' => 'Free',
                    '[accepted]' => 'Y, N, (vide)'
                ]
            ));

            return false;
        }

        if ('Y' === $data['Free']
            && false === empty($data['Id'])
        ) {
            static::error(__(ProductAttributesMessage::EXIM_PRODUCT_ATTRIBUTES_INCOHERENCY));

            return false;
        }

        static::$freeAttribute = ('Y' === $data['Free']);

        return true;
    }

    /** @param mixed[] $data */
    private static function getAndCheckNameOrIdAttribute(array $data): bool
    {
        if (empty($data['Id'])
            && empty($data['Name'])
        ) {
            static::error(__(ProductAttributesMessage::EXIM_PRODUCT_ATTRIBUTES_INVALID_ID));

            return false;
        }

        return true;
    }

    /** @var mixed[] $data */
    private static function getAllAttributes(array $data): void
    {
        $sql = <<<'SQL'
SELECT
  pf.feature_id,
  pf.feature_type,
  ?:product_features_descriptions.description,
  pf.categories_path
FROM
  ?:product_features AS pf
  LEFT JOIN ?:product_features_descriptions ON ?:product_features_descriptions.feature_id = pf.feature_id
  AND ?:product_features_descriptions.lang_code = ?s
  LEFT JOIN ?:product_features AS groups ON pf.parent_id = groups.feature_id
WHERE
  pf.status IN ('A', 'H')
SQL;

        // Load one time for performance optimisation
        if (\is_null(static::$allAttributes)) {
            $languages = [static::$currentLangCode];

            if (false === empty($data['Language'])) {
                $languages = array_keys(Languages::getAll());
            }

            foreach ($languages as $language) {
                $attributes = db_get_hash_array($sql, 'feature_id', $language);

                foreach ($attributes as $key => $attribute) {
                    if ($attribute['feature_type'] === AttributeType::LIST_BRAND
                        || $attribute['feature_type'] === AttributeType::LIST_NUMBER
                        || $attribute['feature_type'] === AttributeType::LIST_TEXT
                        || $attribute['feature_type'] === AttributeType::CHECKBOX_MULTIPLE
                    ) {
                        $attributes[$key]['variants'] = fn_get_product_feature_variants(
                            ['feature_id' => $attribute['feature_id']],
                            0,
                            $language
                        )[0];
                    }
                }

                static::$allAttributes[$language] = array_values($attributes);
            }
        }
    }

    /** @param mixed[] $data */
    private static function getAndCheckAttribute(array $data): bool
    {
        $attributeFound = false;
        foreach (static::$allAttributes[static::$currentLangCode] as $attribute) {
            // Ignore Group attribute
            if (AttributeType::GROUP === $attribute['feature_type']) {
                continue;
            }

            if (empty($data['Id'])) {
                // Check name matching
                if ($attribute['description'] !== $data['Name']) {
                    continue;
                }
            } else {
                // Check feature_id matching
                if ($attribute['feature_id'] !== $data['Id']) {
                    continue;
                }
            }

            if ('' !== $attribute['categories_path']) {
                $categoryMatched = static::areCategoriesMatching(
                    explode(',', $attribute['categories_path']),
                    static::$productCategories
                );

                if (false === $categoryMatched) {
                    continue;
                }
            }

            // At this step, the attribute name exists for the product category
            $attributeFound = true;

            list($isValid, $value) = static::checkAttributeValue($data, $attribute);

            if (false === $isValid) {
                continue;
            }

            static::$currentAttribute[$attribute['feature_id']] = $value;
        }

        if (false === $attributeFound) {
            static::error(__(
                ProductAttributesMessage::EXIM_PRODUCT_ATTRIBUTES_NOT_FOUND_ATTRIBUTE,
                [
                    '[name]' => empty($data['Id'])
                        ? $data['Name']
                        : 'feature_id ' . $data['Id'],
                    '[product_code]' => $data['Product code']
                ]
            ));

            return false;
        }

        if (\is_null(static::$currentAttribute)) {
            return false;
        }

        return true;
    }

    /**
     * @var mixed[] $data
     * @var mixed[] $attribute
     * @return mixed[]
     */
    private static function checkAttributeValue(array $data, array $attribute): array
    {
        // If value is empty, no check (= reset)
        if (empty($data['Value'])) {
            return [true, null];
        }

        switch ($attribute['feature_type']) {
            case AttributeType::LIST_TEXT:
            case AttributeType::LIST_NUMBER:
            case AttributeType::LIST_BRAND:
                // Check variant value (one value)
                return static::checkSingleVariant($data, $attribute);
            case AttributeType::CHECKBOX_MULTIPLE:
                // Check variant values (multiple values)
                return static::checkMultipleVariants($data, $attribute);
            case AttributeType::FREE_DATE:
                // Check date format
                return static::checkDate($data, $attribute['feature_id']);
            case AttributeType::CHECKBOX_UNIQUE:
                // Check boolean value
                return static::checkUniqueBoolean($data, $attribute['feature_id']);
            case AttributeType::FREE_NUMBER:
                // Check number value
                return static::checkNumber($data, $attribute['feature_id']);
            case AttributeType::FREE_TEXT:
                return [true, $data['Value']];
            default:
                return [false, null];
        }
    }

    /**
     * @param mixed[] $data
     * @param mixed[] $attribute
     * @return mixed[]
     */
    private static function checkSingleVariant(array $data, array $attribute): array
    {
        // Only one value is possible
        if (true === \is_string($data['Value'])
            && \count(explode(';', $data['Value'])) > 1
        ) {
            static::error(__(
                ProductAttributesMessage::EXIM_PRODUCT_ATTRIBUTES_TOO_MUCH_VALUES,
                [
                    '[name]' => $data['Name'] . ' (id = ' . $attribute['feature_id'] . ')',
                ]
            ));

            return [false, null];
        }

        $variantId = static::checkAndGetVariantId($data, $attribute);

        if (0 === \count($variantId)
            && AttributeType::LIST_BRAND === $attribute['feature_type']
        ) {
            // The variant value is created on the fly when the attribute type is Brand
            $variantId[] = fn_add_feature_variant(
                (int) $attribute['feature_id'],
                ['variant' => $data['Value'],]
            );

            return [true, $variantId[0]];
        } elseif (0 === \count($variantId)) {
            static::error(__(
                ProductAttributesMessage::EXIM_PRODUCT_ATTRIBUTES_INVALID_FIELDS,
                [
                    '[value]' => $data['Value'],
                    '[name]' => $data['Name'] . ' (id = ' . $attribute['feature_id'] . ')',
                    '[accepted]' => implode(
                        ', ',
                        array_column($attribute['variants'], 'variant')
                    ),
                ]
            ));

            return [false, null];
        }

        return [true, $variantId[0]];
    }

    /**
     * @param mixed[] $data
     * @param mixed[] $attribute
     * @return mixed[]
     */
    private static function checkMultipleVariants(array $data, array $attribute): array
    {
        $variantIds = static::checkAndGetVariantId($data, $attribute);

        if (0 === \count($variantIds)) {
            static::error(__(
                ProductAttributesMessage::EXIM_PRODUCT_ATTRIBUTES_INVALID_FIELDS,
                [
                    '[value]' => $data['Value'],
                    '[name]' => $data['Name'] . ' (id = ' . $attribute['feature_id'] . ')',
                    '[accepted]' => implode(
                        ', ',
                        array_column($attribute['variants'], 'variant')
                    ),
                ]
            ));

            return [false, null];
        }

        return [true, $variantIds];
    }

    /**
     * @param mixed[] $data
     * @param mixed[] $attribute
     * @return int[]
     */
    private static function checkAndGetVariantId(array $data, array $attribute): array
    {
        if (0 === \count($attribute['variants'])) {
            return [];
        }

        $listValues = (true === \is_string($data['Value']))
            ? explode(';', $data['Value'])
            : [];

        $variantIds = [];
        foreach ($listValues as $value) {
            foreach ($attribute['variants'] as $variant) {
                if ($value === $variant['variant']) {
                    $variantIds[] = (int) $variant['variant_id'];
                }
            }
        }

        if (\count($variantIds) > 0
            && \count($listValues) !== \count($variantIds)
        ) {
            static::error(__(
                ProductAttributesMessage::EXIM_PRODUCT_ATTRIBUTES_INVALID_FIELDS,
                [
                    '[value]' => $data['Value'],
                    '[name]' => $data['Name'] . ' (id = ' . $attribute['feature_id'] . ')',
                    '[accepted]' => implode(
                        ', ',
                        array_column($attribute['variants'], 'variant')
                    ),
                ]
            ));

            return [];
        }

        if (\count($variantIds) > 0) {
            return $variantIds;
        }

        return [];
    }

    /**
     * @param mixed[] $data
     * @return mixed[]
     */
    private static function checkDate(array $data, string $featureId): array
    {
        $parsedTime = \DateTime::createFromFormat('Y#m#d', trim($data['Value']));
        // In case of incorrect date, the $parsedTime contains a real date (not the given date)
        // and the 'warning_count' element contains one element with 'The parsed date was invalid'
        $value = ($parsedTime && 0 == \DateTime::getLastErrors()['warning_count'])
            ? $parsedTime->format('U')
            : null;

        if (\is_null($value)) {
            static::error(__(
                ProductAttributesMessage::EXIM_PRODUCT_ATTRIBUTES_INVALID_VALUE,
                [
                    '[value]' => $data['Value'],
                    '[name]' => $data['Name'] . ' (id = ' . $featureId . ')',
                ]
            ));

            return [false, null];
        }

        return [true, $value];
    }

    /**
     * @param mixed[] $data
     * @return mixed[]
     */
    private static function checkUniqueBoolean(array $data, string $featureId): array
    {
        if ('Y' !== $data['Value']
            && 'N' !== $data['Value']
            && '' !== $data['Value']
        ) {
            static::error(__(
                ProductAttributesMessage::EXIM_PRODUCT_ATTRIBUTES_INVALID_FIELDS,
                [
                    '[value]' => $data['Value'],
                    '[name]' => $data['Name'] . ' (id = ' . $featureId . ')',
                    '[accepted]' => 'Y, N, (vide)'
                ]
            ));

            return [false, null];
        }

        return [true, $data['Value']];
    }

    /**
     * @param mixed[] $data
     * @return mixed[]
     */
    private static function checkNumber(array $data, string $featureId): array
    {
        if (false === \is_numeric($data['Value'])) {
            static::error(__(
                ProductAttributesMessage::EXIM_PRODUCT_ATTRIBUTES_INVALID_VALUE,
                [
                    '[value]' => $data['Value'],
                    '[name]' => $data['Name'] . ' (id = ' . $featureId . ')',
                ]
            ));

            return [false, null];
        }

        return [true, $data['Value']];
    }

    private static function error(string $message): void
    {
        EximJobService::error($message, static::$jobId, static::$line);
    }

    private static function warning(string $message): void
    {
        EximJobService::warning($message, static::$jobId, static::$line);
    }

    private static function info(string $message): void
    {
        EximJobService::info($message, static::$jobId, static::$line);
    }
}
