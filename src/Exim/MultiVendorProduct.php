<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Exim;

use Doctrine\ORM\EntityManagerInterface;
use Monolog\Logger;
use Wizacha\Component\Import\Exception\EximJobNotFoundException;
use Wizacha\Component\Locale\Exception\DomainException;
use Wizacha\Component\Locale\Locale;
use Wizacha\Component\Import\EximJobService;
use Wizacha\Exim\Import\ImportReportMessage\MultiVendorProductMessage;
use Wizacha\Marketplace\PIM\Attribute\AttributeService;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct as PimMultiVendorProduct;
use Wizacha\Marketplace\Image\Exception\BadImage;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProductService;
use Wizacha\Marketplace\PIM\Video\Video;

class MultiVendorProduct
{
    public static $mappedFields = [
        'category_id' => 'categoryId',
        'short_description' => 'shortDescription',
        'seo_title' => 'seoTitle',
        'seo_description' => 'seoDescription',
        'seo_keywords' => 'seoKeywords',
        'supplier_reference' => 'supplierReference',
    ];

    public const MULTI_VALUE_DELIMITER = '; ';
    public const PRODUCT_LINK_DELIMITER = ';';

    public const ERROR_IMAGE_NOT_FOUND = 'image_not_found';

    /**
     * @var PimMultiVendorProduct
     * Cache property for Current MVP to be able to detach that MVP from entity manager
     * to avoid memory leak on mass export
     */
    private static $currentMvp;

    /**
     * Export MVP slug
     *
     * @param string $mvpId
     * @return mixed
     */
    public static function getMvpSlug($mvpId)
    {
        return static::getMvp($mvpId)->getSlug();
    }

    /**
     * Export MVP categories
     *
     * @param int $mvpId MVP ID
     * @param string $categoryDelimiter path delimiter
     * @param string $langCode 2 letters language code
     * @return string|false
     */
    public static function getMvpCategoryPath($mvpId, $categoryDelimiter, $langCode = '')
    {
        return fn_get_category_path(static::getMvp($mvpId)->getCategory()->getId(), $langCode, $categoryDelimiter);
    }

    /**
     * Export MVP free attributes
     *
     * @param int $mvpId
     * @param string $attributeDelimiter
     * @param string $langCode
     * @return string
     */
    public static function getMvpFreeAttributes($mvpId, $attributeDelimiter, $langCode = '')
    {
        $pairDelimiter = AttributeService::DELIMITER_PAIR;

        $mvpService = container()->get('marketplace.multi_vendor_product.service');
        $freeAttributes = $mvpService->getFreeLegacyAttributes($mvpId, $langCode);

        foreach ($freeAttributes as $freeAttribute => $value) {
            $result[] = $freeAttribute . $pairDelimiter . " " . implode($attributeDelimiter, $value);
        }

        return !empty($result) ? implode(self::MULTI_VALUE_DELIMITER, $result) : '';
    }

    /**
     * Export MVP attributes
     *
     * @param string $mvpId
     * @param string $attributeDelimiter
     * @param string $langCode
     * @return string
     */
    public static function getMvpAttributes($mvpId, $attributeDelimiter, $langCode = '')
    {
        /** @var MultiVendorProductService */
        $mvpService = container()->get('marketplace.multi_vendor_product.service');
        /** @var AttributeService */
        $attributeService = container()->get('marketplace.pim.attribute_service');

        $pairDelimiter = AttributeService::DELIMITER_PAIR;

        $detailedAttributes = $mvpService->getDetailedLegacyAttributes($mvpId, false, true, $langCode);
        $flattenDetailedAttributes = $attributeService->flattenDetailedLegacyAttributes($detailedAttributes);

        $result = [];
        if (!empty($flattenDetailedAttributes)) {
            foreach ($flattenDetailedAttributes as $attribute) {
                if (!isset($attribute['variants'])) {
                    if (\strpos('SNE', $attribute['feature_type']) !== false) {
                        $values = $attribute['variants'][$attribute['variant_id']]['variant'];
                    } elseif (\strpos('OD', $attribute['feature_type']) !== false) {
                        $values = $attribute['value_int'];
                    } else {
                        $values = $attribute['value'];
                    }
                    $result[] = $attribute['description'] . $pairDelimiter . " " . $values ;
                } else {
                    $values = [];
                    foreach ($attribute['variants'] as $variant) {
                        $values[] = $variant['variant'];
                    }
                    $result[] = $attribute['description'] . $pairDelimiter . " " . implode($attributeDelimiter, $values);
                }
            }
        }

        return !empty($result) ? implode(self::MULTI_VALUE_DELIMITER, $result) : '';
    }

    /**
     * Export MVP product links
     *
     * @param string $mvpId
     * @return string
     */
    public static function getMvpProductLinks($mvpId)
    {
        $result = [];

        foreach (static::getMvp($mvpId)->getLinks() as $link) {
            $result[] = $link->getProduct()->getId();
        }

        return !empty($result) ? '[' . implode(self::PRODUCT_LINK_DELIMITER, $result) . ']' : '';
    }

    /**
     * Export MVP images urls
     *
     * @param string $mvpId
     * @param string $urlImageDelimiter
     * @return string
     */
    public static function getMvpImagesUrls($mvpId, $urlImageDelimiter)
    {
        $imageManager = container()->get('image.manager');

        $result = [];
        foreach (static::getMvp($mvpId)->getImageIds() as $imageId) {
            try {
                $result[] = $imageManager->getInternalUrl($imageId, null, null, 'https');
            } catch (\Throwable $e) {
                $result[] = static::ERROR_IMAGE_NOT_FOUND;

                $logger = container()->get('logger');
                $logger->error('[MVP Export] Error at image getting', [
                    'mvpId' => $mvpId,
                    'imageId' => $imageId,
                    'exception' => $e
                ]);
            }
        }

        return !empty($result) ? implode($urlImageDelimiter, $result) : '';
    }

    /**
     * Export MVP video
     *
     * @param string $mvpId
     *
     * @return string
     */
    public static function getMvpVideo($mvpId): string
    {
        $videoId = static::getMvp($mvpId)->getVideo();

        if ($videoId === null) {
            return '';
        }

        $video = container()->get('marketplace.pim.video_service')->getFromStorage($videoId);

        return ($video instanceof Video === true) ? container()->get('marketplace.pim.video_storage')->generatePublicLinks($video)['path'] : '';
    }

    /** Export MVP images alt text */
    public static function getMvpImagesAltText(string $mvpId, string $urlImageDelimiter, string $langCode): string
    {
        $imageManager = container()->get('image.manager');

        $result = [];
        foreach (static::getMvp($mvpId)->getImageIds() as $imageId) {
            try {
                $result[] = $imageManager->getImageAltText($imageId, $langCode);
            } catch (\Throwable $e) {
                $result[] = static::ERROR_IMAGE_NOT_FOUND;

                container()->get('logger')->error('[MVP Export] Error at image getting', [
                    'mvpId' => $mvpId,
                    'imageId' => $imageId,
                    'exception' => $e
                ]);
            }
        }

        return \count($result) > 0 ? implode($urlImageDelimiter, $result) : '';
    }


    /**
     * Process Import a line ($data)
     * Insert and update
     *
     * @param array $data
     * @param string|null $jobId
     * @param array $options
     *
     * @return bool|void    false in error case
     */
    public static function put(array $data, ?string $jobId, array $options)
    {
        /** @var EximJobService */
        $eximJobService = container()->get('marketplace.import.job_service');
        if (false === \is_null($jobId)) {
            try {
                $eximJobService->get($jobId);
            } catch (EximJobNotFoundException $exception) {
                /** @var Logger */
                $logger = container()->get('logger');
                $logger->debug(
                    'EximJob is gone',
                    [
                        'exception' => $exception
                    ]
                );

                return false;
            }
        }

        /** @var MultiVendorProductService */
        $mvpService = container()->get('marketplace.multi_vendor_product.service');
        $mvpId = null;

        // Map csv keys for use in MvpService
        $data = self::mapDbFieldsToMvpService($data);

        // Try to get the existing mvp
        $existingMvp = null;
        if (isset($data['id']) && !empty($data['id'])) {
            $mvpId = $data['id'];

            try {
                // Update case
                $existingMvp = static::getMvp($data['id']);
            } catch (NotFound $e) {
                EximJobService::error(
                    MultiVendorProductMessage::EXIM_ERROR_MVP_UNKNOWN_ID,
                    $jobId,
                    (string) $data['line'],
                    null,
                    null,
                    ['Id' => $mvpId]
                );
                return false;
            }
        } else {
            // Insert case
            unset($data['id']);
        }

        // Language
        try {
            $data['locale'] = isset($data['locale']) ? new Locale($data['locale']) : GlobalState::interfaceLocale();
        } catch (DomainException $e) {
            EximJobService::error(
                MultiVendorProductMessage::EXIM_ERROR_MVP_LANGUAGE_NOT_VALID,
                $jobId,
                (string) $data['line'],
                null,
                null,
                ['Id' => $mvpId]
            );
            return false;
        }
        $language = (string) $data['locale'];
        unset($data['lang_code']); // clean incorrect lang_code var

        // Code
        if (empty($data['code'])) {
            // It will be generate by mvp entity
            unset($data['code']);
        }

        // Product Template
        if (isset($data['product_template_type'])) {
            $tplService = container()->get('Wizacha\Marketplace\PIM\Product\Template\TemplateService');
            $templates = $tplService->getTemplates();

            $templateFounded = false;
            foreach ($templates as $template) {
                if ($template->getId() === $data['product_template_type']) {
                    $templateFounded = true;
                }
            }

            if (!$templateFounded) {
                EximJobService::error(
                    MultiVendorProductMessage::EXIM_WARNING_MVP_PRODUCT_TEMPLATE_NOT_VALID,
                    $jobId,
                    (string) $data['line'],
                    null,
                    null,
                    ['Id' => $mvpId]
                );
                return false;
            }
        }

        // We extract attributes data to don't treat them in $mvpService::save()
        $attributes = $data['attributes'] ?? null;
        $freeAttributes = $data['freeAttributes'] ?? null;
        unset($data['attributes'], $data['freeAttributes']);

        // Images
        if ($data["images_urls"]) {
            self::putImages($data, $jobId, $options, $existingMvp, $mvpId);
        }

        $urlvideo = "";
        $hasVideoKey = false;
        if (\array_key_exists('video', $data) === true) {
            $urlvideo = $data['video'];
            $hasVideoKey = true;
            unset($data['video']);
        }

        // Save MVP
        try {
            /** @var PimMultiVendorProduct $mvp */
            $mvp = $mvpService->save($data, $existingMvp);
            $mvpId = $mvp->getId();

            EximJobService::info(
                MultiVendorProductMessage::EXIM_SUCCESS_MVP_IMPORT,
                $jobId,
                (string) $data['line'],
                null,
                null,
                ['Id' => $mvpId]
            );
        } catch (\Throwable $e) {
            EximJobService::error(
                MultiVendorProductMessage::EXIM_ERROR_MVP_SAVE,
                $jobId,
                (string) $data['line'],
                null,
                $e->getMessage(),
                ['Id' => $mvpId]
            );
            return false;
        }

        // Attributes
        if (isset($attributes)) {
            try {
                $parsedAttributes = Product::parseFeatures($mvp->getId(), $attributes, $options['attributes_delimiter'], $language, $mvp->getCategory()->getId());

                // Get an empty variants array and we merge it with data to reset old variants
                $emptyVariants = Product::getEmptyFeaturesVariants($mvp->getId());

                if (!empty($parsedAttributes)) {
                    $newVariants = $parsedAttributes['variants'] + $emptyVariants;
                } else {
                    $newVariants = [];
                }

                $mvpService->setLegacyAttributesValues($mvp->getId(), $newVariants, [], $language);
            } catch (\Throwable $e) {
                EximJobService::warning(
                    MultiVendorProductMessage::EXIM_WARNING_MVP_ATTRIBUTES,
                    $jobId,
                    (string) $data['line'],
                    null,
                    $e->getMessage(),
                    ['Id' => $mvpId]
                );
            }
        }

        // Free attributes
        if (isset($freeAttributes)) {
            try {
                $parsedAttributes = Product::parseFeatures($mvp->getId(), $freeAttributes, $options['attributes_delimiter'], $language);
                $parsedFreeAttributes = $parsedAttributes['free_features'] ?? [] ;
                $mvpService->setFreeLegacyAttributes($mvp->getId(), $parsedFreeAttributes);
            } catch (\Throwable $e) {
                EximJobService::warning(
                    MultiVendorProductMessage::EXIM_WARNING_MVP_FREE_ATTRIBUTES,
                    $jobId,
                    (string) $data['line'],
                    null,
                    $e->getMessage(),
                    ['Id' => $mvpId]
                );
            }
        }

        // Video
        if (\strlen($urlvideo) > 0) {
            try {
                $videoValidator = container()->get('app.validator.video_validator');
                $videoValidator->assertIsUrlValid($urlvideo);
                $mvpService->multiVendorProductImportFromUrl($mvp->getId(), $urlvideo, $jobId, $data['line']);
            } catch (\Exception $exception) {
                EximJobService::warning(
                    $exception->getMessage(),
                    $jobId,
                    (string) $data['line'],
                    null,
                    null,
                    ['Id' => $mvpId]
                );
                fn_set_notification('E', $exception->getMessage(), $urlvideo);
            }
        }
        if ($hasVideoKey === true && \mb_strlen($urlvideo) === 0) {
            $videoId = $mvp->getVideo();
            if ($videoId !== null) {
                $mvpService->deleteVideo($mvp->getId());
            }
        }
    }

    /**
     * Map dbFields to EntityFields
     *
     * @param array $data
     * @return mixed
     */
    public static function mapDbFieldsToMvpService($data)
    {
        $mappedFields = self::$mappedFields;

        foreach ($mappedFields as $keyFrom => $keyTo) {
            if (isset($data[$keyFrom])) {
                $data[$keyTo] = $data[$keyFrom];
            }
        }

        return $data;
    }

    /**
     * Parse and import images
     *
     * @param array $data data line to update
     */
    public static function putImages(
        array &$data,
        ?string $jobId,
        array $options,
        ?PimMultiVendorProduct $existingMvp = null,
        ?string $mvpId
    ) {
        $imageManager = container()->get('image.manager');

        // Get old images urls
        $oldImages = [];
        if (isset($existingMvp)) {
            foreach ($existingMvp->getImageIds() as $id) {
                try {
                    $url = $imageManager->getInternalUrl($id, null, null, 'https');
                    $oldImages[$id] = self::getUrlWithoutScheme($url);
                } catch (NotFound $e) {
                    //Si une image n'existe plus, on ne s'en soucie pas
                }
            }
        }

        $parsedUrls = explode($options['images_delimiter'], $data["images_urls"]);
        if (false !== $parsedUrls) {
            foreach ($parsedUrls as $newUrl) {
                // Check if this image is already attached to this mvp
                $urlToTest = self::getUrlWithoutScheme($newUrl);
                $alreadyExists = false;
                foreach ($oldImages as $oldId => $oldUrl) {
                    if ($urlToTest === $oldUrl) {
                        // Founded, we add it to $data and remove it from old images
                        $data["imageIds"][] = $oldId;
                        unset($oldImages[$oldId]);
                        $alreadyExists = true;
                    }
                }

                if (false === $alreadyExists) {
                    try {
                        // We try to download it (or use its cache, see createByUrl function)
                        $newId = $imageManager->createByUrl($newUrl);
                        $data["imageIds"][] = $newId;
                        // in case we use a cache image, the Id didn't change, so we remove it from the old images
                        if (isset($oldImages[$newId])) {
                            unset($oldImages[$newId]);
                        }
                    } catch (BadImage $e) {
                        EximJobService::warning(
                            MultiVendorProductMessage::EXIM_WARNING_MVP_IMAGE,
                            $jobId,
                            (string) $data['line'],
                            null,
                            $e->getMessage(),
                            ['Id' => $mvpId]
                        );
                    }
                }
            }
        }

        // Save Alt Text
        if (\array_key_exists('Alt Text', $data) === true
            && $data['imageIds'] !== null
            && \count($data['imageIds']) > 0
        ) {
            $parsedAltText = explode($options['images_delimiter'], $data['Alt Text']);
            if ($parsedAltText !== false) {
                foreach ($parsedAltText as $key => $newAltText) {
                    $imageManager->setImageAltText($data['imageIds'][$key], $newAltText, $data['locale']);
                }
            }
        }

        // Deleting useless images
        foreach ($oldImages as $id => $url) {
            $imageManager->delete($id);
        }
    }

    public static function getUrlWithoutScheme($url)
    {
        $urlValidator = container()->get('app.validator.url_validator');

        if (false === $urlValidator->isUrlValid($url)) {
            return null;
        }

        $urlExplode = explode('//', $url);
        array_shift($urlExplode);

        return implode($urlExplode);
    }

    /**
     * keep only the current MVP (the one being exported) on the entity manager to avoid memory leak
     * (otherwise 60k MVP to export => 60k MVP on entitymanager => fatal error memory limit )
     */
    private static function getMvp(string $mvpId): PimMultiVendorProduct
    {
        if (static::$currentMvp instanceof PimMultiVendorProduct) {
            if (static::$currentMvp->getId() === $mvpId) {
                return static::$currentMvp;
            }

            // detach all related entities of the previous MVP from the entity manager
            /** @var EntityManagerInterface */
            $em = container()->get('doctrine.orm.entity_manager');
            $em->detach(static::$currentMvp);
            $em->detach(static::$currentMvp->getCategory());
            foreach (static::$currentMvp->getLinks() as $link) {
                $em->detach($link->getProduct());
                $em->detach($link);
            }
        }

        static::$currentMvp = container()->get('marketplace.multi_vendor_product.service')->get($mvpId);
        // do not detach here : we need to keep that MVP in doctrine because we can't avoid
        // some "find($id)" in the export process which will re put it in the entity manager for ever

        return static::$currentMvp;
    }
}
