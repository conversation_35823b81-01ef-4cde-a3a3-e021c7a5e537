<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Exim;

use Doctrine\DBAL\Statement;
use Doctrine\ORM\EntityManagerInterface;
use Wizacha\Marketplace\User\UserStatus;
use Wizacha\Marketplace\User\UserType;

class CompanyTokenCsv
{
    protected const SEPARATOR = ';';

    /** @var EntityManagerInterface */
    protected $entityManager;
    protected $csvHeader = false;
    /** @var resource */
    protected $fileHandler;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
        $this->fileHandler = fopen('php://stdout', 'w');
    }

    /** output to php://stdout as CSV */
    public function drawCsv(): void
    {
        $preparedStatement = $this->getCompanyResultSet();

        while ($company = $preparedStatement->fetch()) {
            $row = $this->parseCompanyRow($company);
            $this->csvHeader($row);
            fputcsv(
                $this->fileHandler,
                $row,
                static::SEPARATOR
            );
        }

        fclose($this->fileHandler);
    }

    public function getCompanyResultSet(): Statement
    {
        $preparedStatement = $this->entityManager
            ->getConnection()
            ->prepare(
                'SELECT
                    cc.company_id,
                    cc.company,
                    cu.api_key,
                    cc.extra
                FROM
                    cscart_companies cc
                    JOIN cscart_users cu ON
                        cc.company_id = cu.company_id
                        AND cu.status = :status
                        AND cu.user_type = :user_type
                        AND cc.extra IS NOT NULL
                        AND cc.extra NOT IN ("a:0:{}", "")
                '
            );

        $preparedStatement->execute(
            [
                'status' => UserStatus::ACTIVE(),
                'user_type' => UserType::VENDOR(),
            ]
        );

        return $preparedStatement;
    }

    public function parseCompanyRow(array $company): array
    {
        return array_merge(
            [
                'company_id' => $company['company_id'],
                'company_name' => $company['company'],
                'api_key' => $company['api_key'],
            ],
            unserialize($company['extra'])
        );
    }

    /** Draw header only once */
    private function csvHeader(array $row): void
    {
        if (false === $this->csvHeader) {
            $this->csvHeader = fputcsv(
                $this->fileHandler,
                array_keys($row),
                static::SEPARATOR
            );
        }
    }
}
