<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\MiraklOrder;

use Doctrine\DBAL\Connection;
use Wizacha\AppBundle\Controller\Api\Shippings;
use Wizacha\Money\Money;
use Wizacha\Status;
use Wizacha\Test\Fixture\Shipping\CreateShipping;
use Wizacha\Test\Fixture\Shipping\ShippingLoader;

class MiraklOrderShippingHandler
{
    /** @var Shippings */
    protected $legacyShippingController;

    /** @var ShippingLoader */
    protected $shippingLoader;

    /** @var MiraklOrderTaxHandler */
    protected $taxHandler;

    /** @var Connection */
    protected $connection;

    public function __construct(
        Shippings $legacyShippingController,
        ShippingLoader $shippingLoader,
        MiraklOrderTaxHandler $taxHandler,
        Connection $connection
    ) {
        $this->legacyShippingController = $legacyShippingController;
        $this->legacyShippingController->setParentName('products');

        $this->shippingLoader = $shippingLoader;
        $this->taxHandler = $taxHandler;
        $this->connection = $connection;
    }

    /** @var mixed[] $taxData */
    public function setProductShipping(int $productId, int $shippingId, Money $amount, array $taxData): void
    {
        $taxId = $this->taxHandler->getRateId($taxData);
        $this->updateShippingTaxId($shippingId, $taxId);

        $this->legacyShippingController->setParentData(['product_id' => $productId]);
        $this->legacyShippingController->update(
            $shippingId,
            [
                'status' => Status::ENABLED()->getValue(),
                'product_id' => $productId,
                'shipping_id' => $shippingId,
                'rates' => [
                    [
                        'amount' => 0,
                        'value' => $amount->getConvertedAmount(),
                    ],
                    [
                        'amount' => 1,
                        'value' => 0,
                    ],
                ],
            ]
        );
    }

    /**
     * Note that shipping taxes will be updated in item pre-processing.
     *
     * @return int Data migration temporary shipping ID.
     */
    public function createShipping(): int
    {
        $command = new CreateShipping('Reprise de données');

        return $this->shippingLoader->create($command);
    }

    public function deleteShipping(int $shippingId): void
    {
        $this->legacyShippingController->delete($shippingId);
    }

    protected function updateShippingTaxId(int $shippingId, int $taxId): void
    {
        $taxId = $taxId !== 0 ? $taxId : '';

        $preparedStatement = $this->connection->prepare(
            'UPDATE cscart_shippings SET tax_ids = :taxId WHERE shipping_id = :shippingId'
        );
        $preparedStatement->bindValue(':taxId', $taxId);
        $preparedStatement->bindValue(':shippingId', $shippingId);
        $preparedStatement->execute();
    }

    public function enableCompanyShipping(int $shippingId): void
    {
        $preparedStatement = $this->connection->prepare(
            'UPDATE
                cscart_companies
            SET
                shippings = CONCAT(shippings, ",", :shippingId)
            '
        );
        $preparedStatement->bindValue(':shippingId', $shippingId);
        $preparedStatement->execute();
    }
}
