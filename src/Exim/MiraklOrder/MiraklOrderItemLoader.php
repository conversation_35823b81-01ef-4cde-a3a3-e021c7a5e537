<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\MiraklOrder;

use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\AppBundle\Controller\Api\Products;
use Wizacha\Exim\MiraklOrder\Exception\MiraklImportException;
use Wizacha\Exim\MiraklOrder\Exception\ProductUpdateException;
use Wizacha\Marketplace\Catalog\Exceptions\MultipleProductsFoundException;
use Wizacha\Marketplace\Catalog\Product\ProductService;
use Wizacha\Marketplace\Exception\ProductNotFound;
use Wizacha\Money\Money;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Test\Fixture\Order\CreateOrder;

class MiraklOrderItemLoader
{
    protected const KEY_TAXES = 'taxes';

    /** @var Products */
    protected $productLegacyController;

    /** @var ProductService */
    protected $productService;

    /** @var MiraklTemporaryProductCreator */
    protected $productCreator;

    /** @var MiraklOrderTaxHandler */
    protected $taxHandler;

    /** @var MiraklOrderShippingHandler */
    protected $shippingHandler;

    /** @var OutputInterface */
    protected $output;

    /** @var int[] */
    protected $createdProducts = [];

    public function __construct(
        ProductService $productService,
        Products $productLegacyController,
        MiraklTemporaryProductCreator $productCreator,
        MiraklOrderTaxHandler $taxHandler,
        MiraklOrderShippingHandler $shippingHandler,
        OutputInterface $output
    ) {
        $this->productLegacyController = $productLegacyController;
        $this->productService = $productService;
        $this->productCreator = $productCreator;
        $this->taxHandler = $taxHandler;
        $this->shippingHandler = $shippingHandler;
        $this->output = $output;
    }

    public function initialize(): void
    {
        $this->productCreator->createDefaultCategory();
    }

    /** @param mixed[] $orderLine */
    public function load(array $orderLine, int $companyId, int $shippingId, CreateOrder $createOrder): CreateOrder
    {
        try {
            $productId = (int) $this->getOrderLineProductId($orderLine['product_sku'], $companyId);
        } catch (ProductNotFound $e) {
            // If the product doesn't exist anymore, we create it dynamically
            $taxId = $this->taxHandler->getRateId($orderLine[static::KEY_TAXES]);
            $productId = $this->productCreator->createProduct($orderLine, $companyId, $taxId);
            $this->createdProducts[] = $productId;
            $this->output->writeln("[PRODUCT CREATED] #$productId");
        } catch (MultipleProductsFoundException $e) {
            $this->output->writeln("[PRODUCT ERROR] SKU {$orderLine['product_sku']}");

            throw new MiraklImportException();
        }

        try {
            $this->preProcessProduct($orderLine, $productId, $shippingId);
        } catch (\Exception $e) {
            throw new ProductUpdateException($productId);
        }

        $createOrder->addProductToBasket(
            (int) $productId,
            null,
            $this->getOrderLineQuantity($orderLine)
        );

        return $createOrder;
    }

    public function finalize(): void
    {
        $this->output->writeln('[DELETE] Default product category');
        $this->productCreator->deleteDefaultCategory();

        foreach ($this->createdProducts as $productId) {
            $this->output->writeln('[DELETE] Product #' . $productId);
            try {
                fn_delete_product($productId);
            } catch (Forbidden $e) {
                $this->output->writeln('[ACCESS DENIED] Product #' . $productId);
            }
        }

        $this->output->writeln('[DELETE] Infinite stock value on every product');
        $this->productCreator->resetInfiniteStock();
    }

    /**
     * In order to checkout the order properly with Mirakl data,
     * we update products on the fly, and make sure they match their state at the time of the real order.
     * The update supports (field that might have changed):
     *   - product name
     *   - unit price
     *   - tax
     *
     * We also update the product shipping cost with:
     *   - main shipping cost: the shipping cost of the orderline
     *   - additional cost per item: 0
     *
     * For the import to succeed, we also set:
     *   - infinite stock
     *   - product is active
     *
     * Note that this means that the read model is regenerated twice for every order line :screaming cat:.
     *
     * @param mixed[] $orderLine
     */
    protected function preProcessProduct(array $orderLine, int $productId, int $shippingId): void
    {
        $rateId = $this->taxHandler->getRateId($orderLine[static::KEY_TAXES]);
        $this->taxHandler->updateProductTaxId($productId, $rateId);

        $this
            ->productLegacyController
            ->update(
                $productId,
                [
                    'status' => 'A',
                    'product' => $orderLine['product_title'],
                    'infinite_stock' => true,
                    'supplier_ref' => $orderLine['product_sku'],
                    'inventory' => [
                        [
                            'amount' => $this->getOrderLineQuantity($orderLine),
                            'price' => $orderLine['price_unit'],
                            'infinite_stock' => true,
                        ]
                    ]
                ]
            );

        $this->shippingHandler->setProductShipping(
            $productId,
            $shippingId,
            Money::fromVariable($this->getShippingTotal($orderLine)),
            $orderLine[static::KEY_TAXES] ?? []
        );
    }

    protected function getOrderLineQuantity(array $orderLine): int
    {
        return
            $orderLine['quantity']
            + array_sum(
                array_column($orderLine['cancelations'] ?? [], 'quantity')
            );
    }

    protected function getShippingTotal(array $orderLine): float
    {
        return
            $orderLine['shipping_price']
            + array_sum(
                array_column($orderLine['cancelations'] ?? [], 'shipping_amount')
            );
    }

    protected function getOrderLineProductId(string $productSku, int $companyId): int
    {
        return $this->productService->getCompanyProductBySupplierRef(
            $companyId,
            $productSku
        );
    }
}
