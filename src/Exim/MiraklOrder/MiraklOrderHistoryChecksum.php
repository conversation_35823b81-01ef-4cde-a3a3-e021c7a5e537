<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\MiraklOrder;

/**
 * This class calculates a checksum for every CSV import file line, and then writes it in another CSV file.
 * The output file contains for each line:
 *   - Mirakl Order ID (aka checksum identifier)
 *   - the line checksum
 *   - Wizaplace Order ID
 */
class MiraklOrderHistoryChecksum
{
    protected const KEY_CHECKSUM = 'checksum';

    protected const KEY_ORDER_ID = 'orderId';

    protected const DELIMITER = ';';

    /** @var mixed[] [Mirakl Order ID => ['checksum' => value, 'orderId' => Wizaplace Order ID]] */
    protected $checksumMap;

    /** @var string */
    protected $outputFilePath;

    public function initialize(string $outputFilePath): void
    {
        $this->outputFilePath = $outputFilePath;

        $this->reloadChecksumsFile();
    }

    public function addProcessedLine(string $identifier, string $line, int $orderId, bool $save = true): void
    {
        $dumpFullFile = $this->hasLine($identifier);

        $this->checksumMap[$identifier][static::KEY_ORDER_ID] = $orderId;
        $this->checksumMap[$identifier][static::KEY_CHECKSUM] = $this->generateLineHash($line);

        if (true === $save) {
            // If the line has been updated, we rewrite the entire file
            // because we don't know were the specific line is,
            // otherwise we can just append the new entry at the end of file.
            if (true === $dumpFullFile) {
                $this->writeChecksumFile();
            } else {
                $this->writeNewLine($identifier);
            }
        }
    }

    public function hasLine(string $identifier): bool
    {
        return \array_key_exists($identifier, $this->checksumMap);
    }

    public function isLineProcessed(string $identifier, string $line): bool
    {
        if (false === $this->hasLine($identifier)) {
            return false;
        }

        return $this->checksumMap[$identifier][static::KEY_CHECKSUM] === $this->generateLineHash($line);
    }

    public function getOrderId(string $identifier): int
    {
        return $this->checksumMap[$identifier][static::KEY_ORDER_ID];
    }

    protected function generateLineHash(string $line): string
    {
        return hash('sha1', $line);
    }

    protected function reloadChecksumsFile(): void
    {
        $this->checksumMap = [];

        if (is_file($this->outputFilePath) && is_readable($this->outputFilePath)) {
            $fileHandler = fopen($this->outputFilePath, 'r');

            while ([$identifier, $checksum, $orderId] = fgetcsv($fileHandler, 0, static::DELIMITER)) {
                $this->checksumMap[$identifier] = [
                    static::KEY_CHECKSUM => $checksum,
                    static::KEY_ORDER_ID => (int) $orderId
                ];
            }

            fclose($fileHandler);
        }
    }

    protected function writeNewLine(string $identifier): void
    {
        $fileHandler = fopen($this->outputFilePath, 'a');

        fputcsv($fileHandler, $this->csvLineFromMap($identifier), static::DELIMITER);
        fclose($fileHandler);
    }

    protected function writeChecksumFile(): void
    {
        $fileHandler = fopen($this->outputFilePath, 'w');

        foreach ($this->checksumMap as $identifier => $data) {
            fputcsv($fileHandler, $this->csvLineFromMap($identifier), static::DELIMITER);
        }

        fclose($fileHandler);
    }

    /** @return string[] */
    protected function csvLineFromMap(string $identifier): array
    {
        return array_merge(
            [$identifier],
            array_values([
                $this->checksumMap[$identifier][static::KEY_CHECKSUM],
                $this->checksumMap[$identifier][static::KEY_ORDER_ID],
            ])
        );
    }
}
