<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\MiraklOrder;

use Doctrine\DBAL\Driver\Connection;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Transaction\TransactionType;

class MiraklOrderPostProcessor
{
    protected const PREPARED_STATEMENT_MAPPING = [
        'details' => 'details',
        'payment_id' => 'paymentId',
        'timestamp' => 'createdAt',
        'w_last_status_change' => 'updatedAt',
        'workflow_last_update' => 'updatedAt',
        'shipping_date' => 'shippedAt',
        'delivery_date' => 'receivedAt'
    ];

    /** @var Connection */
    protected $connection;

    /** @var MiraklOrderPaymentHandler */
    protected $paymentHandler;

    /** @var OrderService */
    protected $orderService;

    public function __construct(
        Connection $connection,
        MiraklOrderPaymentHandler $paymentHandler,
        OrderService $orderService
    ) {
        $this->connection = $connection;
        $this->paymentHandler = $paymentHandler;
        $this->orderService = $orderService;
    }

    public function updateOrder(
        Order $order,
        string $miraklOrderId,
        string $paymentType,
        int $createdAt,
        ?string $updatedAt,
        ?string $shippedAt,
        ?string $receivedAt
    ): Order {
        $payment = $this->paymentHandler->getPayment($paymentType);

        [$partialSqlStatement, $data]
            = $this->prepareData(
                [
                    'details' => "miraklOrderId: $miraklOrderId",
                    'paymentId' => $payment->getId(),
                    'createdAt' => $createdAt,
                    'updatedAt' => $updatedAt,
                    'shippedAt' => $shippedAt,
                    'receivedAt' => $receivedAt,
                ]
            )
        ;

        $preparedStatement = $this->connection->prepare(
            "UPDATE cscart_orders
            SET
                $partialSqlStatement
            WHERE
                `order_id` = :orderId
            "
        );

        $preparedStatement->execute(
            \array_merge(
                ['orderId' => $order->getId()],
                $data
            )
        );

        $preparedStatement = $this->connection->prepare('
            UPDATE doctrine_order_transactions
            SET `type` = :transactionType
            WHERE `order_id` = :orderId AND `type` = :offlineType
        ');

        $preparedStatement->execute([
            'orderId' => $order->getId(),
            'transactionType' => $this->paymentHandler->getTransactionType($payment)->getValue(),
            'offlineType' => TransactionType::OFFLINE()->getValue(),
        ]);

        // Refresh the order after manipulating it directly with SQL requests
        $order = $this->orderService->getOrder($order->getId());

        return $order;
    }

    /**
     * @param mixed[] $rawData
     *
     * @return array<string,array>
     * */
    protected function prepareData(array $rawData): array
    {
        // filter out null values
        $data = \array_filter(
            $rawData,
            function (?string $value): bool {
                return null !== $value;
            }
        );

        // filter out unset keys
        $availableKeys = \array_keys($data);
        $partials = \array_filter(
            static::PREPARED_STATEMENT_MAPPING,
            function ($key) use ($availableKeys) {
                return \in_array($key, $availableKeys);
            }
        );

        // partial prepared statement query array
        $partialsSql = \array_map(
            function ($key, $value) {
                return "`$key` = :$value";
            },
            \array_keys($partials),
            \array_values($partials)
        );

        return [
            \implode(', ', $partialsSql), // partial prepared statement query string
            $data, // prepared statement input parameters
        ];
    }
}
