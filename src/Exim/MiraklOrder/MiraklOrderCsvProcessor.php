<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\MiraklOrder;

use Doctrine\ORM\EntityManagerInterface;

class MiraklOrderCsvProcessor
{
    public const HISTORY_FILE_PATH = 'var/mirakl_order_history_checksums.csv';

    public const SEPARATOR = ';';

    /** @var MiraklOrderLoader */
    protected $orderLoader;

    /** @var MiraklRefundLoader */
    protected $refundLoader;

    /** @var MiraklOrderRemover */
    protected $orderRemover;

    /** @var EntityManagerInterface */
    protected $entityManager;

    /** @var MiraklOrderHistoryChecksum */
    protected $historyChecksum;

    public function __construct(
        MiraklOrderLoader $orderLoader,
        MiraklRefundLoader $refundLoader,
        MiraklOrderRemover $orderRemover,
        EntityManagerInterface $entityManager,
        MiraklOrderHistoryChecksum $historyChecksum
    ) {
        $this->orderLoader = $orderLoader;
        $this->refundLoader = $refundLoader;
        $this->orderRemover = $orderRemover;
        $this->entityManager = $entityManager;
        $this->historyChecksum = $historyChecksum;

        $this->historyChecksum->initialize(static::HISTORY_FILE_PATH);
    }

    /** @return \Generator|string[] Success and error messages */
    public function process(string $inputFile): \Generator
    {
        $this->setProductsApproved();

        $this->orderLoader->initialize();

        $line = 1;
        $fileHandler = fopen($inputFile, 'r');
        $header = fgetcsv($fileHandler, 0, static::SEPARATOR);
        while ($rawRow = fgets($fileHandler)) {
            ++$line;

            $data = $this->parseCsvRow($header, $rawRow);

            $miraklOrderId = $data['order_id'];

            // If the order has already been seen in a previous import
            if (true === $this->historyChecksum->hasLine($miraklOrderId)) {
                // If the checksum hasn't changed, we skip the line
                if (true === $this->historyChecksum->isLineProcessed($miraklOrderId, $rawRow)) {
                    yield sprintf('Order #%05d already imported. Skipping', $miraklOrderId);
                    continue;
                }

                // Otherwise the line is known but the checksum has changed - we delete the order, and re-import it
                $this->orderRemover->removeOrder($this->historyChecksum->getOrderId($miraklOrderId));
            }

            try {
                $order = $this->orderLoader->load($data);
                $refund = $this->refundLoader->load($order, $data);

                $this->historyChecksum->addProcessedLine($miraklOrderId, $rawRow, $order->getId());

                yield sprintf(
                    'Order #%05d succcessfully imported%s',
                    $order->getId(),
                    $refund !== null ? ' (with refund)' : ''
                );
            } catch (\Throwable $e) {
                yield sprintf(
                    '<error>[ERROR ON LINE %5d] %s: %s</error>',
                    $line,
                    \get_class($e),
                    $e->getMessage()
                );
            }
        }

        $this->orderLoader->finalize();
    }

    // FIXME: Temporaire, en attendant d'avoir les produits importé déjà approved
    protected function setProductsApproved(): void
    {
        $preparedStatement = $this->entityManager
            ->getConnection()
            ->prepare(
                'UPDATE
                    cscart_products
                SET
                    approved = "Y"
                WHERE
                    approved != "Y"
                '
            );
        $preparedStatement->execute();
    }

    /**
     * @param string[] $header
     *
     * @return string[]
     */
    protected function parseCsvRow(array $header, string $rawRow): array
    {
        $row = str_getcsv($rawRow, static::SEPARATOR);

        // CSV file contains JSON encoded content
        $normalizedRow = array_map(
            function (string $value) {
                $value = preg_replace(
                    '/[[:cntrl:]]/',
                    '',
                    $value
                );

                // Switch to JSON_THROW_ON_ERROR when we switch to PHP 7.3+
                return json_decode($value, true) ?? $value;
            },
            $row
        );

        return array_combine($header, $normalizedRow);
    }
}
