<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\MiraklOrder;

use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Exim\MiraklOrder\Exception\CompanyNotFoundException;
use Wizacha\Exim\MiraklOrder\Exception\CustomerNotFoundException;
use Wizacha\Exim\MiraklOrder\Exception\MiraklImportException;
use Wizacha\Exim\MiraklOrder\Exception\OrderCheckoutException;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderMutator;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Test\Fixture\Order\CreateOrder;
use Wizacha\Test\Fixture\Order\OrderLoader;

class MiraklOrderLoader
{
    /** @var OrderStatus[] */
    protected static $orderStatusMapping;

    /** @var MiraklOrderItemLoader */
    protected $itemLoader;

    /** @var OrderLoader */
    protected $orderLoader;

    /** @var MiraklOrderCustomerHandler */
    protected $customerHandler;

    /** @var OrderMutator */
    protected $orderMutator;

    /** @var MiraklOrderShippingHandler */
    protected $shippingHandler;

    /** @var MiraklOrderPaymentHandler */
    protected $paymentHandler;

    /** @var MiraklOrderPostProcessor */
    protected $miraklOrderPostProcessor;

    /** @var OutputInterface */
    protected $output;

    /** @var int */
    protected $shippingId;

    /** @var int[] */
    protected $createdCustomers = [];

    public function __construct(
        MiraklOrderItemLoader $itemLoader,
        OrderLoader $orderLoader,
        OrderMutator $orderMutator,
        MiraklOrderCustomerHandler $customerHandler,
        MiraklOrderShippingHandler $shippingHandler,
        MiraklOrderPaymentHandler $paymentHandler,
        MiraklOrderPostProcessor $miraklOrderPostProcessor,
        OutputInterface $output
    ) {
        $this->itemLoader = $itemLoader;
        $this->orderLoader = $orderLoader;
        $this->customerHandler = $customerHandler;
        $this->orderMutator = $orderMutator;
        $this->shippingHandler = $shippingHandler;
        $this->miraklOrderPostProcessor = $miraklOrderPostProcessor;
        $this->output = $output;

        static::$orderStatusMapping = [
            'CANCELED' => OrderStatus::CANCELED(),
            'WAITING_ACCEPTANCE' => OrderStatus::STANDBY_VENDOR(),
            'WAITING_DEBIT_PAYMENT' => OrderStatus::STANDBY_BILLING(),
            'CLOSED' => OrderStatus::COMPLETED(),
            'REFUSED' => OrderStatus::VENDOR_DECLINED(),
            'RECEIVED' => OrderStatus::COMPLETED(),
            'SHIPPING' => OrderStatus::PROCESSING_SHIPPING(),
            'SHIPPED' => OrderStatus::PROCESSED(),
        ];
        $this->paymentHandler = $paymentHandler;
    }

    public function initialize(): void
    {
        $this->itemLoader->initialize();
        $this->shippingId = $this->shippingHandler->createShipping();
        $this->shippingHandler->enableCompanyShipping($this->shippingId);
    }

    /** @param mixed[] $orderData */
    public function load(array $orderData): Order
    {
        $externalId = $orderData['customer']['customer_id'];
        $email = $orderData['customer']['email'];

        try {
            $userId = $this->customerHandler->getUserIdByEmail($email);
        } catch (CustomerNotFoundException $e) {
            try {
                $userId = $this->customerHandler->getUserIdByExternalId($externalId);
            } catch (CustomerNotFoundException $e) {
                $this->output->writeln(sprintf(
                    '[UNKNOWN CUSTOMER] External ID #%d - Creating with email address: %s',
                    $externalId,
                    $email
                ));
                $userId = $this->customerHandler->create($email);
                $this->createdCustomers[] = $userId;
            }
        }

        $createOrder = new CreateOrder($userId);
        $createOrder->paymentId = $this->paymentHandler
            ->getPayment('offline')
            ->getId()
        ;
        $createOrder->shippingId = $this->shippingId;

        $createOrder = $this->processOrderLines(
            $orderData,
            $createOrder
        );

        try {
            $result = $this->orderLoader->createAndGetCheckoutResult(
                $createOrder
            );
        } catch (\Exception $e) {
            throw new OrderCheckoutException($e);
        }

        $order = $result->getOrders()[0];

        // change order status
        $this
            ->orderMutator
            ->setOrderStatus(
                $order,
                $this->orderStatusMapping($orderData['order_state'])
            )
        ;

        // Validate order payment transaction's status
        $this->paymentHandler->validateOrderTransaction($order);

        // change order dates
        try {
            return $this
                ->miraklOrderPostProcessor
                ->updateOrder(
                    $order,
                    $orderData['order_id'],
                    $orderData['payment_type'],
                    \DateTimeImmutable::createFromFormat(
                        \DateTimeImmutable::ATOM,
                        $orderData['created_date']
                    )->getTimestamp(),
                    $orderData['last_update_date'],
                    $orderData['shipping_date'],
                    $orderData['received_date']
                )
                ;
        } catch (\Exception $e) {
            throw new MiraklImportException($e->getMessage(), $e->getCode(), $e);
        }
    }

    public function finalize(): void
    {
        $this->itemLoader->finalize();

        $this->output->writeln('[DELETE] Disable dynamically created customers');
        $this->customerHandler->disable($this->createdCustomers);

        $this->output->writeln('[DELETE] Created shipping');
        $this->shippingHandler->deleteShipping($this->shippingId);
    }

    /** @param mixed[] $data Order data */
    protected function processOrderLines(array $data, CreateOrder $createOrder): CreateOrder
    {
        $companyId = $data['wizaplace_id'];

        if (null === $companyId || 'NOT_FOUND' === $companyId) {
            throw new CompanyNotFoundException((string) $companyId);
        }

        foreach ($data['order_lines'] as $orderLine) {
            $createOrder = $this->itemLoader->load($orderLine, (int) $companyId, $this->shippingId, $createOrder);
        }

        return $createOrder;
    }

    protected function orderStatusMapping(string $miraklOrderStatus): OrderStatus
    {
        return static::$orderStatusMapping[$miraklOrderStatus];
    }
}
