<?php

declare(strict_types=1);

namespace Wizacha\Exim\MiraklOrder;

use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderItem;
use Wizacha\Marketplace\Order\Refund\Entity\Refund;
use Wizacha\Marketplace\Order\Refund\Entity\RefundItem;
use Wizacha\Marketplace\Order\Refund\Enum\RefundStatus;
use Wizacha\Marketplace\Order\Refund\Repository\RefundRepository;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\Processor\RefundableProcessorTrait;
use Wizacha\Marketplace\Transaction\Transaction;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Money\Money;

class MiraklRefundLoader
{
    use RefundableProcessorTrait;

    /** @var RefundRepository */
    protected $refundRepository;

    /** @var TransactionService */
    protected $transactionService;

    public function __construct(RefundRepository $refundRepository, TransactionService $transactionService)
    {
        $this->refundRepository = $refundRepository;
        $this->transactionService = $transactionService;
    }

    public function load(Order $order, array $data): ?Refund
    {
        // If there isn't any order line containing a cancellation, then there is no refund to create.
        if (false === $this->hasCancellations($data['order_lines'])) {
            return null;
        }

        /** @var OrderItem[] $orderItems */
        $orderItems = [];
        // Store order items in a map to retrieve them easily.
        foreach ($order->getItems() as $item) {
            $orderItems[$item->getSupplierRef()] = $item;
        }

        $refund = new Refund();
        $refund
            ->setOrderId($order->getId())
            ->setMessage('Reprise de données')
            ->setAmount(new Money(0))
            ->setShippingAmount(new Money(0))
            ->setHasShipping(false)
            ->setIsPartial(true)
            ->setIsLegacy(false)
            ->setStatus(RefundStatus::PAID())
        ;

        foreach ($data['order_lines'] as $orderLine) {
            if (0 === \count($orderLine['cancelations'] ?? [])) {
                continue;
            }

            $refund = $this->processOrderLine($order, $refund, $orderLine, $orderItems);
        }

        $refund = $this->refundRepository->save($refund);
        $transaction = $this->transactionService->retrievePaymentTransaction($order)[0];

        // Create the refund transaction based on payment transaction.
        $this->refundTransaction($transaction, $refund, function (Transaction $transaction): Transaction {
            return $transaction;
        });

        return $refund;
    }

    public function getName(): PaymentProcessorName
    {
        return PaymentProcessorName::NONE();
    }

    /**
     * @param mixed[] $orderLine
     * @param OrderItem[] $orderItems
     */
    protected function processOrderLine(Order $order, Refund $refund, array $orderLine, array $orderItems): Refund
    {
        $totalQuantity = 0;

        // Foreach cancellation line, we add a new RefundItem to our created Refund.
        // We also add the increase the total amount of the refund, and eventually include shipping costs.
        foreach ($orderLine['cancelations'] as $cancelation) {
            $orderItem = $orderItems[$orderLine['product_sku']];
            $refundItem = new RefundItem();

            // We reduce the precision, because the unit price was calculated from tax percentage,
            // so we may be adding unnecessary amount the the final refund amount.
            $itemAmount = $orderItem->getDiscountedUnitPrice()->reducePrecisionToCents();

            $refundItem
                ->setAmount($itemAmount)
                ->setItemId((int) $orderItem->getItemId())
                ->setQuantity($cancelation['quantity'])
                ->setRefund($refund)
            ;

            $refund->setAmount($refund->getAmount()->add($itemAmount));
            $refund->addItem($refundItem);

            if ((float) $cancelation['shipping_amount'] > 0 && $refund->hasShipping() === false) {
                $refund
                    ->setHasShipping(true)
                    ->setShippingAmount($order->getShippingCost())
                ;
                $refund->setAmount($refund->getAmount()->add($refund->getShippingAmount()));
            }

            $totalQuantity += $refundItem->getQuantity();
        }

        /*
         * Quick'n'dirty: if tax calculation end up having less that a €0.01 difference per item, let's just fix it.
         */
        $totalDifference = $order->getCustomerTotal()->subtract($refund->getAmount());
        if ($totalDifference->isNegative()
            && $totalDifference->absolute()->lessThanOrEqual(Money::fromVariable(.01)->multiply($totalQuantity))
        ) {
            $refund->setAmount($order->getCustomerTotal());
        }

        return $refund;
    }

    /** @param mixed[] $orderLines */
    protected function hasCancellations(array $orderLines): bool
    {
        $linesWithCancellation = array_filter(
            $orderLines,
            function (array $orderLine): bool {
                return 0 < \count($orderLine['cancelations'] ?? []);
            }
        );

        return 0 < \count($linesWithCancellation);
    }
}
