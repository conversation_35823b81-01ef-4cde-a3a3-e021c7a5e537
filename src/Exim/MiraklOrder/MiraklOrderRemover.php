<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\MiraklOrder;

use Doctrine\DBAL\Connection;

class MiraklOrderRemover
{
    /** @var Connection */
    protected $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
    }

    public function removeOrder(int $orderId): void
    {
        $tables = [
            'cscart_order_data',
            'cscart_order_details',
            'cscart_order_docs',
            'doctrine_order_transactions',
            'orders_actions_traces',
            'cscart_orders',
        ];

        foreach ($tables as $table) {
            $query = $this->connection->prepare('DELETE FROM ' . $table . ' WHERE order_id = :orderId');
            $query->bindValue(':orderId', $orderId);

            $query->execute();
        }
    }
}
