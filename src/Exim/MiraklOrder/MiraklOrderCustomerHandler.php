<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\MiraklOrder;

use Doctrine\DBAL\Connection;
use Wizacha\Exim\MiraklOrder\Exception\CustomerNotFoundException;
use Wizacha\Marketplace\User\User;
use Wizacha\Marketplace\User\UserService;
use Wizacha\Marketplace\User\UserTitle;
use Wizacha\Test\Fixture\User\CreateUser;
use Wizacha\Test\Fixture\User\UserLoader;

class MiraklOrderCustomerHandler
{
    protected const DEFAULT_EMPTY = 'Unknown';

    /** @var UserLoader */
    protected $loader;

    /** @var UserService */
    protected $userService;

    /** @var Connection */
    protected $connection;

    public function __construct(UserLoader $loader, UserService $userService, Connection $connection)
    {
        $this->loader = $loader;
        $this->userService = $userService;
        $this->connection = $connection;
    }

    public function getUserIdByEmail(string $email): int
    {
        $user = $this->userService->findOneByEmail($email);

        if (false === $user instanceof User) {
            throw new CustomerNotFoundException($email);
        }

        return $user->getUserId();
    }

    public function getUserIdByExternalId(string $externalId): int
    {
        $userId = $this->findUserIdExternalId($externalId);

        if (null === $userId) {
            throw new CustomerNotFoundException($externalId);
        }

        return $userId;
    }

    public function create(string $email): int
    {
        $command = new CreateUser($email, static::DEFAULT_EMPTY, static::DEFAULT_EMPTY, UserTitle::MR());
        $command->fillBillingAddress = true;
        $command->fillShippingAddress = true;
        $command->fillAddressWithDefault = false;

        $command->address = static::DEFAULT_EMPTY;
        $command->city = static::DEFAULT_EMPTY;
        $command->zipCode = static::DEFAULT_EMPTY;
        $command->country = static::DEFAULT_EMPTY;

        $command->password = bin2hex(random_bytes(32));

        return $this->loader->createCustomer($command);
    }

    /** @param int[] $customerIds */
    public function disable(array $customerIds): void
    {
        if (\count($customerIds) > 0) {
            $this->userService->bulkDisable($customerIds);
        }
    }

    protected function findUserIdExternalId(string $externalId): ?int
    {
        // Profile table is not mapped.
        $query = $this->connection->prepare(
            'SELECT user_id FROM cscart_user_profiles WHERE external_identifier = :externalId'
        );
        $query->bindValue(':externalId', $externalId);

        return ((int) $query->fetchColumn()) ?: null;
    }
}
