<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\MiraklOrder;

use Doctrine\DBAL\Connection;
use Wizacha\Status;
use Wizacha\Test\Fixture\Category\CategoryLoader;
use Wizacha\Test\Fixture\Category\CreateCategory;
use Wizacha\Test\Fixture\Product\CreateProduct;
use Wizacha\Test\Fixture\Product\ProductLoader;

class MiraklTemporaryProductCreator
{
    /** @var ProductLoader */
    protected $loader;

    /** @var CategoryLoader */
    protected $categoryLoader;

    /** @var Connection */
    protected $connection;

    /** @var int */
    protected $defaultCategoryId;

    public function __construct(ProductLoader $loader, CategoryLoader $categoryLoader, Connection $connection)
    {
        $this->loader = $loader;
        $this->categoryLoader = $categoryLoader;
        $this->connection = $connection;
    }

    public function createDefaultCategory(): int
    {
        $this->defaultCategoryId = $this->categoryLoader->create(new CreateCategory());

        return $this->defaultCategoryId;
    }

    public function deleteDefaultCategory(): void
    {
        fn_delete_category($this->defaultCategoryId);
    }

    /**
     * @param mixed[] $orderLineData
     *
     * @return int Created product ID.
     */
    public function createProduct(array $orderLineData, int $companyId, int $taxId): int
    {
        $command = new CreateProduct($orderLineData['product_title']);

        $command->companyId = $companyId;
        $command->status = Status::ENABLED()->getValue();
        $command->price = $orderLineData['price'];
        $command->infiniteStock = true;
        $command->approved = true;
        $command->productCode = $orderLineData['offer_sku'];
        $command->supplierRef = $orderLineData['product_sku'];
        $command->taxIds = [$taxId];
        $command->categoryId = $this->defaultCategoryId;

        return $this->loader->create($command);
    }

    public function resetInfiniteStock(): void
    {
        $this->connection->exec('UPDATE cscart_products SET infinite_stock = 0');
    }
}
