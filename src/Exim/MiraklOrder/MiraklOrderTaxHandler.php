<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\MiraklOrder;

use Doctrine\DBAL\Connection;
use Wizacha\Exim\MiraklOrder\Exception\MultipleTaxesException;
use Wizacha\Exim\MiraklOrder\Exception\TaxNotFoundException;
use Wizacha\Marketplace\PIM\Tax\Enum\TaxRateType;
use Wizacha\Marketplace\PIM\Tax\Tax;
use Wizacha\Marketplace\PIM\Tax\TaxService;
use Wizacha\Status;

class MiraklOrderTaxHandler
{
    /** @var TaxService */
    protected $taxService;

    /** @var Connection */
    protected $connection;

    /** @var Tax[] */
    protected $taxes = null;

    public function __construct(TaxService $taxService, Connection $connection)
    {
        $this->taxService = $taxService;
        $this->connection = $connection;
    }

    public function initialize(): void
    {
        $taxList = $this->taxService->listAll();
        $taxList = array_filter(
            $taxList,
            function (Tax $tax): bool {
                return
                    $tax->getStatus() === Status::ENABLED()->getValue()
                    && \count($tax->getRates()) > 0
                    && $tax->getRates()[0]->getType()->equals(TaxRateType::PERCENT())
                ;
            }
        );

        foreach ($taxList as $tax) {
            $this->taxes[number_format($tax->getRates()[0]->getRate(), 2)] = $tax;
        }
    }

    /** @param mixed[] $taxesData */
    public function getRateId(array $taxesData): int
    {
        if ($this->taxes === null) {
            $this->initialize();
        }

        if (\count($taxesData) > 1) {
            throw new MultipleTaxesException();
        }

        if (\count($taxesData) === 0) {
            return 0;
        }

        // The rate is not available in the input file, so we use the codes which are properly formatted.
        $rate = preg_replace('/[^\d]+/', '', reset($taxesData)['code']);
        $tax = number_format($rate === '55' ? 5.5 : (float) $rate, 2);

        if (\array_key_exists($tax, $this->taxes) === false) {
            throw new TaxNotFoundException();
        }

        return $this->taxes[$tax]->getId();
    }

    public function updateProductTaxId(int $productId, int $taxId): void
    {
        // Taxes are not supported by the API, so we'll set them here.
        if ($taxId !== 0) {
            $query = $this->connection->prepare('
                UPDATE cscart_products
                SET tax_ids = :taxes
                WHERE product_id = :productId
            ');
            $query->bindValue(':taxes', $taxId);
            $query->bindValue(':productId', $productId);
            $query->execute();
        }
    }

    public function getTaxes(): ?array
    {
        return $this->taxes;
    }
}
