<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\MiraklOrder\Exception;

class MissingPaymentException extends \Exception
{
    public function __construct(array $processorIds)
    {
        parent::__construct(
            'Can not process the import: you must create payment method beforehand for the following PSP'
            . implode(', ', $processorIds)
        );
    }
}
