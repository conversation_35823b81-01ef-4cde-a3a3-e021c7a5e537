<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\MiraklOrder\Exception;

class CustomerNotFoundException extends MiraklImportException
{
    public function __construct(string $identifier)
    {
        parent::__construct('Could NOT retrieve customer with identifier: ' . $identifier);
    }
}
