<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\MiraklOrder\Exception;

use Throwable;

class OrderCheckoutException extends MiraklImportException
{
    public function __construct(Throwable $previous = null)
    {
        parent::__construct('An error occurred while checking out the order: ' . $previous->getMessage(), 0, $previous);
    }
}
