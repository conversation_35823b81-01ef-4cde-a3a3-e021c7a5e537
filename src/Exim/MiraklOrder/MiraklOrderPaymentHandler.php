<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\MiraklOrder;

use Wizacha\Exim\MiraklOrder\Exception\MissingPaymentException;
use Wizacha\Exim\MiraklOrder\Exception\UnknownTransactionType;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Payment\Payment;
use Wizacha\Marketplace\Payment\PaymentProcessorIdentifier;
use Wizacha\Marketplace\Payment\PaymentService;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionStatus;
use Wizacha\Marketplace\Transaction\TransactionType;

class MiraklOrderPaymentHandler
{
    /** @var PaymentService */
    protected $paymentService;

    /** @var PaymentProcessorIdentifier[] */
    protected $paymentTypes;

    /** @var Payment[] */
    protected $payments;

    /** @var TransactionService */
    protected $transactionService;

    public function __construct(PaymentService $paymentService, TransactionService $transactionService)
    {
        $this->paymentService = $paymentService;
        $this->transactionService = $transactionService;

        $this->paymentTypes = [
            'offline' => PaymentProcessorIdentifier::OFFLINE(),
            'hipay_sdd' => PaymentProcessorIdentifier::HIPAY_DEFERED_SEPA_DIRECT_DEBIT(),
            'hipay_sddrecurring' => PaymentProcessorIdentifier::HIPAY_DEFERED_SEPA_DIRECT_DEBIT(),
            'recurringsdd' => PaymentProcessorIdentifier::HIPAY_DEFERED_SEPA_DIRECT_DEBIT(),
            'hipay_hosted' => PaymentProcessorIdentifier::HIPAY_CARD(),
        ];
    }

    public function getPayment(string $type): Payment
    {
        if (null === $this->payments) {
            $this->loadPayments();
        }

        if (false === \array_key_exists($type, $this->paymentTypes)) {
            $type = 'offline';
        }

        return $this->payments[$this->paymentTypes[$type]->getValue()];
    }

    public function validateOrderTransaction(Order $order): void
    {
        $transaction = $this->transactionService->findByOrderId($order->getId())[0];

        $transaction->setStatus(TransactionStatus::SUCCESS());

        $this->transactionService->save($transaction);
    }

    public function getTransactionType(Payment $payment): TransactionType
    {
        switch ($payment->getProcessorId()) {
            case PaymentProcessorIdentifier::HIPAY_DEFERED_SEPA_DIRECT_DEBIT()->getValue():
                return TransactionType::DIRECT_DEBIT();
            case PaymentProcessorIdentifier::HIPAY_CARD()->getValue():
                return TransactionType::CREDITCARD();
            default:
                throw new UnknownTransactionType($payment->getProcessorId());
        }
    }

    protected function loadPayments(): void
    {
        $paymentTypeIds = array_map(
            function (PaymentProcessorIdentifier $identifier) {
                return $identifier->getValue();
            },
            $this->paymentTypes
        );

        $this->payments = array_filter(
            $this->paymentService->getActivePayments(),
            function (Payment $payment) use ($paymentTypeIds): bool {
                return \in_array($payment->getProcessorId(), $paymentTypeIds);
            }
        );

        // Set processor ID as array keys.
        $this->payments = array_combine(
            array_map(function (Payment $payment): int {
                return $payment->getProcessorId();
            }, $this->payments),
            $this->payments
        );

        if (\count($this->payments) !== \count(array_unique($paymentTypeIds))) {
            throw new MissingPaymentException($this->paymentTypes);
        }
    }
}
