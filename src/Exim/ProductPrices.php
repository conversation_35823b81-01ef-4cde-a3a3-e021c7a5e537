<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Exim;

use Wizacha\Component\Import\EximJobService;
use Wizacha\Component\Import\JobStatus;
use Wizacha\Component\Locale\Locale;
use Wizacha\Cscart\Exim;
use Wizacha\Exim\Exception\EximJobException;
use Wizacha\Exim\Import\ImportReportMessage\ProductPriceAndStockMessage;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\PriceTier\PriceTier;
use Wizacha\Marketplace\Traits\CompanyTrait;
use Wizacha\Money\Money;
use Wizacha\Product as WizachaProduct;
use Wizacha\Registry;

/**
 * Class Product: Helpers for import/export csv
 * @package Wizacha\Exim
 */
class ProductPrices
{
    use CompanyTrait;

    public const DECLINATION_LIST = 'declinations';

    /** @var int */
    private static $jobId;
    /** @var int */
    private static $line;
    /** @var ?int */
    private static $productId;
    /** @var bool Determine if EximJob counter has been incremented */
    private static $incremented = false;

    private static string $productCode;
    private static ?int $companyId;

    public static function ImportProductPrices(
        array $datas,
        array $options,
        string $lang_code,
        array $progress_id = [],
        string $jobId = null,
        array $lines = []
    ): bool {
        static::$jobId = $jobId;
        static::$line = current($lines);
        static::$incremented = false;

        $productsInventories = [];
        $groupLines = implode(',', $lines);

        $container = container();
        $delayExec = $container
            ->get('marketplace.async_dispatcher')
            ->delayExec(
                __METHOD__,
                \func_get_args(),
                \Wizacha\Registry::defaultInstance(),
                $progress_id
            )
        ;
        if (true === $delayExec) {
            return true;
        }

        // If job not exist then stop the process
        $jobService = $container->get('marketplace.import.job_service');
        try {
            $job = $jobService->get($jobId);
        } catch (NotFound $exception) {
            return true;
        }

        if ($job->getStatus()->equals(JobStatus::CANCELED()) === true) {
            return true;
        }

        // check company id
        $companyId = self::checkCompanyId(Registry::defaultInstance()->get(['runtime', 'company_id']), $datas);

        static::$productId = WizachaProduct::getIdByProductCode($datas['product_code'], $companyId) ?: 0;
        static::$productCode = $datas['product_code'];
        static::$companyId = $companyId;

        if (static::$productId === 0) {
            array_map(function ($line) {
                static::$line = $line;
                static::warning(ProductPriceAndStockMessage::EXIM_WRONG_PRODUCT_DECLINATION);
            }, $lines);

            return false;
        }
        $lang_code = $lang_code ?? ((string) GlobalState::contentLocale());

        //retrieve correctly formatted datas
        $product_data = array_intersect_key(
            $datas,
            array_flip(
                [
                    'product_code',
                    'price',
                    'company_id',
                    'lang_code',
                    'crossed_out_price',
                    'Price tiers'
                ]
            )
        );

        try {
            // Update product prices
            fn_update_product_prices(static::$productId, $product_data);

            // Update product crossed out prices
            if (\array_key_exists('crossed_out_price', $product_data) === true) {
                fn_update_product_crossed_out_prices(static::$productId, (float) $product_data['crossed_out_price']);
            }

            static::info(ProductPriceAndStockMessage::EXIM_PRODUCT_PRICE_UPDATE);

            //update datas
            $datas['product_id'] = static::$productId;

            /* @var PriceTierService $priceTierService */
            $priceTierService = $container->get('marketplace.price_tier.price_tier_service');

            if ($datas[ProductPrices::DECLINATION_LIST]) {
                $fakeCounter = ['E' => 0, 'N' => 0, 'S' => 0];
                foreach ($datas[ProductPrices::DECLINATION_LIST] as $key => $declination_data) {
                    // we need to update this static to keep track of the right csv line number
                    static::$line = $lines[$key];
                    $combination = Exim::exim_put_product_combination(
                        static::$productId,
                        $datas['product_code'],
                        $declination_data['Combination Code'],
                        [
                            $lang_code => $declination_data['Combination']
                            //need combination separately for each language
                        ],
                        null,
                        $fakeCounter,
                        (true === \array_key_exists('price', $declination_data)
                            && '' !== $declination_data['price']
                        ) ? $declination_data['price'] : null,
                        (true === \array_key_exists('crossed_out_price', $declination_data)
                            && '' !== $declination_data['crossed_out_price']
                        ) ? $declination_data['crossed_out_price'] : null,
                        null,
                        $jobId,
                        $lines[$key],
                        null,
                        null,
                        null,
                        true
                    );

                    if ($key > 0) {
                        static::info(ProductPriceAndStockMessage::EXIM_PRODUCT_DECLINATION_PRICE_UPDATE, $product_data['product_code'], true);
                    }

                    // If we don't ask for combination or we have a combination found.
                    if (($combination === null && $declination_data['combination'] === "") || $combination !== null) {
                        Exim::eximUpdatePriceTiers(
                            $combination,
                            $container->getParameter('feature.tier_pricing'),
                            $declination_data,
                            $datas,
                            false,
                            static::$productId,
                            $productsInventories,
                            $jobId,
                            $lines[$key]
                        );

                        // Clean all datas for next loop clean
                        unset(
                            $productsInventories[$combination],
                            $priceTiers,
                            $declination_data,
                            $savePriceTiersErrors
                        );
                    }
                }
            }

            // Create a single priceTier with lowerLimit & price=0 for an automatically created option inventory
            $allProductCombinations = $container
                ->get('marketplace.price_tier.product_option_inventory_repository')
                ->findByProductId(static::$productId)
            ;

            foreach ($allProductCombinations as $singleProductCombination) {
                if (\count($singleProductCombination->getPriceTiers()->getValues()) === 0) {
                    $priceTier = (new PriceTier($container->get('marketplace.pim.product.service')->get(static::$productId)))
                        ->setProductOptionInventory($singleProductCombination)
                        ->setLowerLimitAndPrice(Money::fromVariable(0.00), 0)
                    ;

                    $container->get('marketplace.price_tier.price_tier_repository')->save($priceTier);
                };
            }

            if (WizachaProduct::hasChanged(static::$productId, new Locale($lang_code))) {
                \Wizacha\Events\Config::dispatch(
                    WizachaProduct::EVENT_UPDATE,
                    (new \Wizacha\Events\IterableEvent())->setElement(static::$productId)
                );
            }
        } catch (EximJobException $exception) {
            static::error($exception->getMessage(), $exception->getExtra());

            return false;
        }

        return true;
    }

    /**
     * Logger::info() is bound to EximJob done-jobs incremental
     * So we have to ensure only one call is made by product
     * @see EximJobRepository::increment()
     * @param bool $bypass Allows to bypass incremented verification and log (used for declinations import)
     */
    private static function info(string $message, ?string $extra = null, bool $bypass = false): void
    {
        if (false === static::$incremented || true === $bypass) {
            EximJobService::info(
                $message,
                static::$jobId,
                static::$line,
                static::$productId,
                $extra,
                [
                    'productCode' => static::$productCode,
                    'companyId' => static::$companyId ?? ''
                ]
            );
            static::$incremented = true;
        }
    }

    private static function warning(string $message, ?string $extra = null): void
    {
        EximJobService::warning(
            $message,
            static::$jobId,
            static::$line,
            static::$productId,
            $extra,
            [
                'productCode' => static::$productCode,
                'companyId' => static::$companyId ?? ''
            ]
        );
    }

    private static function error(string $message, ?string $extra = null): void
    {
        EximJobService::error(
            $message,
            static::$jobId,
            static::$line,
            static::$productId,
            $extra,
            [
                'productCode' => static::$productCode,
                'companyId' => static::$companyId ?? ''
            ]
        );
    }

    private static function exception(string $message, ?string $extra = null): void
    {
        throw new EximJobException($message, static::$jobId, static::$line, null, $extra);
    }
}
