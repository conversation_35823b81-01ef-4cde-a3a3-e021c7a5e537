<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim;

use Wizacha\Marketplace\{
    RelatedProduct\RelatedProduct,
    RelatedProduct\RelatedProductService,
};
use Tygh\Registry;
use Wizacha\AppBundle\Controller\Api\RelatedProduct\RelatedProductDto;
use Wizacha\Component\Import\EximJobService;
use Wizacha\Exim\Import\ImportReportMessage\RelatedProductMessage;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Exception\CompanyNotFound;
use Wizacha\Marketplace\PIM\Product\Product as PimProduct;
use Wizacha\Marketplace\PIM\Product\ProductService;

/*** Class Related Product Helper class used for Csv Export/Import of Related Products */
class RelatedProducts
{
    /**
     * @var RelatedProduct
     * Cache property for Current Related Product to be able to detach that Related Product from entity manager
     * to avoid memory leak on mass export
     */
    private static $currentRelatedProduct;

    /** @var ProductService */
    private static $currentProductService = null;

    /** @var RelatedProductService */
    private static $currentRelatedProductService = null;

    /** @var ?int */
    private static $jobId;

    /** @var int */
    private static $line;

    /** @var ?RelatedProduct */
    private static $relatedProduct;

    /** @var int */
    private static $companyId;

    /** @var PimProduct */
    private static $fromProduct;

    /** @var int */
    private static $relatedProductId;

    /** @var string */
    private static $type;

    /** @var PimProduct */
    private static $toProduct;

    /**
     * @param int $relatedProductId
     * @param string $key
     * @return string|int|null
     */
    public static function getField(int $relatedProductId, string $key)
    {
        if (false === \array_key_exists($key, static::getRelatedProductData($relatedProductId))) {
            return null;
        }

        return static::getRelatedProductData($relatedProductId)[$key];
    }

    /** @return mixed[] */
    private static function getRelatedProductData(int $relatedProductId): array
    {
        if (static::$currentRelatedProduct instanceof RelatedProduct
            && static::$currentRelatedProduct->getId() === $relatedProductId
        ) {
            return static::exposeExportedData(static::$currentRelatedProduct);
        }

        /** @var RelatedProduct[] $relatedProducts */
        $relatedProducts = container()->get(RelatedProductService::class)->findBy(['id' => $relatedProductId]);
        // On récupère un seul élément avec son ID car il est unique
        static::$currentRelatedProduct = $relatedProducts[0];


        return static::exposeExportedData(static::$currentRelatedProduct);
    }

    private static function exposeExportedData(RelatedProduct $relatedProduct): array
    {
        return [
            'companyId' => (string) ($relatedProduct->getFromProduct()->getCompanyId()),
            'productCode' => $relatedProduct->getFromProduct()->getCode(),
            'type' => $relatedProduct->getType()->getValue(),
            'relatedProduct' => $relatedProduct->getToProduct()->getCode(),
            'relatedCompany' => (string) ($relatedProduct->getToProduct()->getCompanyId()),
            'description' => (string) ($relatedProduct->getDescription()),
            'extra' => (string) ($relatedProduct->getExtra()),
        ];
    }

    public static function put(array $data, string $jobId)
    {
        static::$jobId = $jobId;
        static::$line = $data['line'];
        static::$relatedProduct = null;

        $runtimeCompanyId = Registry::get('runtime.company_id');

        // Get and check the Company id
        if (false === static::getAndCheckCompanyId($runtimeCompanyId, $data)) {
            return false;
        }

        // Get useful services (ProductService and RelatedProductsService)
        static::getServices();

        // Get and check the From product (from the Product code)
        if (false === static::getAndCheckFromProduct($data)) {
            return false;
        }

        // Get and check the related product id (from the Related product)
        if (false === static::getAndCheckRelatedProductId($runtimeCompanyId, $data)) {
            return false;
        }

        // Get and check the Type
        if (false === static::getAndCheckType($data)) {
            return false;
        }

        // Get and check the Delete value
        if (false === static::getAndCheckDelete($data)) {
            return false;
        }

        // Delete = 'Y'
        // Delete the Related Products corresponding to the criteria
        if ($data['Delete'] === 'Y') {
            return static::deleteRelatedProducts($runtimeCompanyId, $data);
        }

        // Get To product (from the related product id)
        if (false === static::getAndCheckToProduct()) {
            return false;
        }

        // Update or create the related product
        return static::upsertRelatedProducts($data);
    }

    /** @param mixed[] $data */
    private static function getAndCheckCompanyId(int $runtimeCompanyId, array $data): bool
    {
        $companyId = $runtimeCompanyId;

        // no runtime company_id (admin)
        if (0 === $companyId) {
            // Read the company from the data
            $companyId = (int) $data['Company id'] ?? 0;
        }

        // Error: neither runtime company, neither Company id in the csv file
        if (0 === $companyId) {
            static::error(__(
                RelatedProductMessage::EXIM_RELATED_PRODUCT_REQUIRED_FIELD,
                [
                    '%' => 'Company id',
                ]
            ));

            return false;
        }

        // Check the company id exists
        return static::isValidCompanyId($companyId);
    }

    private static function isValidCompanyId(int $companyId): bool
    {
        try {
            container()->get(CompanyService::class)->get($companyId);
        } catch (CompanyNotFound $e) {
            static::error(__(
                RelatedProductMessage::EXIM_RELATED_PRODUCT_NOT_FOUND_COMPANY_ID,
                [
                    '[company_id]' => $companyId
                ]
            ));

            return false;
        }

        static::$companyId = $companyId;

        return true;
    }

    private static function getServices(): void
    {
        if (false === static::$currentProductService instanceof ProductService) {
            static::$currentProductService = container()->get(ProductService::class);
        }

        if (false === static::$currentRelatedProductService instanceof RelatedProductService) {
            static::$currentRelatedProductService = container()->get(RelatedProductService::class);
        }
    }

    /** @param mixed[] $data */
    private static function getAndCheckFromProduct(array $data): bool
    {
        $productId = (int) static::$currentProductService->getIdByProductCode($data['Product code'], static::$companyId) ?: 0;

        if (0 === $productId) {
            static::error(__(
                RelatedProductMessage::EXIM_RELATED_PRODUCT_NOT_FOUND_PRODUCT_CODE,
                [
                    '[product_code]' => $data['Product code'],
                    '[company_id]' => static::$companyId,
                ]
            ));

            return false;
        }

        static::$fromProduct = static::$currentProductService->get($productId);

        return true;
    }

    /** @param mixed[] $data */
    private static function getAndCheckRelatedProductId(int $runtimeCompanyId, array $data): bool
    {
        static::$relatedProductId = null;

        if (!empty($data['Related product'])) {
            $relatedCompanyId = (null !== $data['Related company'] && !empty($data['Related company']))
                ? (int) $data['Related company']
                : static::$companyId;

            // Error: the Related company is not the company the user is currently working on
            // (vendor or admin on a specific company)
            if (0 !== $runtimeCompanyId && $relatedCompanyId != static::$companyId) {
                static::error(__(RelatedProductMessage::EXIM_RELATED_PRODUCT_EXTERNAL_COMPANY));

                return false;
            }

            // Check the Related company id exists (case of external company, not already checked)
            if ((0 === $runtimeCompanyId)
                && (false === static::isValidCompanyId($relatedCompanyId))
            ) {
                return false;
            }

            static::$relatedProductId = (int) static::$currentProductService->getIdByProductCode(
                $data['Related product'],
                $relatedCompanyId
            ) ?: 0;

            if (0 === static::$relatedProductId) {
                static::error(__(
                    RelatedProductMessage::EXIM_RELATED_PRODUCT_NOT_FOUND_RELATED_PRODUCT,
                    [
                        '[product_code]' => $data['Related product'],
                        '[company_id]' => $relatedCompanyId,
                    ]
                ));

                return false;
            }
        }

        if (!\is_int(static::$relatedProductId) && 'Y' !== $data['Delete']) {
            static::error(__(
                RelatedProductMessage::EXIM_RELATED_PRODUCT_REQUIRED_FIELD,
                [
                    '%' => 'Related product',
                ]
            ));

            return false;
        }

        return true;
    }

    /** @param mixed[] $data */
    private static function getAndCheckType(array $data): bool
    {
        static::$type = null;

        if (!empty($data['Type'])) {
            static::$type = $data['Type'];

            if (false === static::$currentRelatedProductService->isValidType(static::$type)) {
                static::error(__(
                    RelatedProductMessage::EXIM_RELATED_PRODUCT_INVALID_FIELDS,
                    [
                        '[value]' => static::$type,
                        '[name]' => 'Type',
                        '[accepted]' => static::$currentRelatedProductService->getAvailableTypesToString(),
                    ]
                ));

                return false;
            }
        }

        if (!\is_string(static::$type) && 'Y' !== $data['Delete']) {
            static::error(__(
                RelatedProductMessage::EXIM_RELATED_PRODUCT_REQUIRED_FIELD,
                [
                    '%' => 'Type',
                ]
            ));

            return false;
        }

        return true;
    }

    /** @param mixed[] $data */
    private static function getAndCheckDelete(array $data): bool
    {
        // Incorrect value in the Delete column
        if ('Y' !== $data['Delete'] && 'N' !== $data['Delete'] && !empty($data['Delete'])) {
            static::error(__(
                RelatedProductMessage::EXIM_RELATED_PRODUCT_INVALID_FIELDS,
                [
                    '[value]' => $data['Delete'],
                    '[name]' => 'Delete',
                    '[accepted]' => '(vide), Y, N',
                ]
            ));

            return false;
        }

        return true;
    }

    /** @param mixed[] $data */
    private static function deleteRelatedProducts(int $runtimeCompanyId, array $data): bool
    {
        $criteria = ['fromProduct' => static::$fromProduct->getId()];

        if (\is_int(static::$relatedProductId)) {
            $criteria['toProduct'] = static::$relatedProductId;
        }

        if (\is_string(static::$type)) {
            $criteria['type'] = static::$type;
        }

        $relatedProducts = static::$currentRelatedProductService->findBy($criteria);

        if (empty($relatedProducts)) {
            static::$relatedProduct = null;

            static::warning(__(
                RelatedProductMessage::EXIM_RELATED_PRODUCT_DELETE_NOT_FOUND,
                [
                    '[criteria]' => static::criteriaToString($criteria, $data),
                ]
            ));

            return false;
        }

        foreach ($relatedProducts as $relatedProduct) {
            // In case of the user is logged on a company,
            // ignore the related products from other companies (no error)
            if (0 !== $runtimeCompanyId
                && $relatedProduct->getToProduct()->getCompanyId() !== $runtimeCompanyId
            ) {
                continue;
            }

            static::$relatedProduct = clone $relatedProduct;

            static::$currentRelatedProductService->deleteRelatedProduct(
                $relatedProduct->getFromProduct()->getId(),
                $relatedProduct->getToProduct()->getId(),
                $relatedProduct->getType()->getValue()
            );

            static::info(RelatedProductMessage::EXIM_RELATED_PRODUCT_DELETE);
        }

        return true;
    }

    /** @param mixed[] $criteria */
    /** @param mixed[] $data */
    private static function criteriaToString(array $criteria, array $data): string
    {
        $result = '';

        foreach ($criteria as $key => $val) {
            switch ($key) {
                case 'fromProduct':
                    $name = 'Product code';
                    $value = $data['Product code'];
                    break;
                case 'toProduct':
                    $name = 'Related product';
                    $value = $data['Related product'];
                    break;
                default:
                    $name = $key;
                    $value = $val;
            }

            $result .= ('' === $result)
                ? sprintf("%s = %s", $name, $value)
                : sprintf(", %s = %s", $name, $value);
        }

        return $result;
    }

    /** @var mixed $data */
    private static function upsertRelatedProducts(array $data): bool
    {
        // Check if the related product already exists (update) or not (create)
        $related = static::$currentRelatedProductService->findBy([
            'fromProduct' => static::$fromProduct->getId(),
            'toProduct' => static::$toProduct->getId(),
            'type' => static::$type,
        ]);

        $relatedProductDto = new RelatedProductDto(
            static::$fromProduct,
            static::$toProduct,
            static::$type,
            $data['Description'] ?? null,
            $data['Extra'] ?? null
        );

        $user = Registry::get('user_info');

        if (\count($related) === 1) {
            static::$relatedProduct = static::$currentRelatedProductService->updateRelatedProduct(
                $relatedProductDto,
                (int) $user['user_id']
            );

            static::info(RelatedProductMessage::EXIM_RELATED_PRODUCT_UPDATE);
        } else {
            static::$relatedProduct = static::$currentRelatedProductService->addRelatedProduct(
                $relatedProductDto,
                (int) $user['user_id']
            );

            static::info(RelatedProductMessage::EXIM_RELATED_PRODUCT_CREATE);
        }

        return true;
    }

    private static function getAndCheckToProduct(): bool
    {
        static::$toProduct = static::$currentProductService->get(static::$relatedProductId);

        if (static::$fromProduct === static::$toProduct) {
            static::error(__(RelatedProductMessage::EXIM_RELATED_PRODUCT_SAME_PRODUCTS));

            return false;
        }

        return true;
    }

    private static function error(string $message): void
    {
        EximJobService::error($message, static::$jobId, static::$line);
    }

    private static function warning(string $message): void
    {
        EximJobService::warning($message, static::$jobId, static::$line);
    }

    private static function info(string $message): void
    {
        EximJobService::info(
            $message,
            static::$jobId,
            static::$line,
            static::$relatedProduct ? static::$relatedProduct->getId() : null
        );
    }
}
