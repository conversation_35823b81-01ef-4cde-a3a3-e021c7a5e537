<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\Exception;

class EximJobException extends \Exception
{
    /** @var string */
    protected $message;

    /** @var string */
    protected $jobId;

    /** @var ?int */
    protected $entityId;

    /** @var ?string */
    protected $extra;

    public function __construct(
        string $message,
        string $jobId,
        string $line,
        int $entityId = null,
        string $extra = null
    ) {
        parent::__construct($message);

        $this->jobId = $jobId;
        $this->line = $line;
        $this->entityId = $entityId;
        $this->extra = $extra;
    }

    public function getJobId(): string
    {
        return $this->jobId;
    }

    public function getEntityId()
    {
        return $this->entityId;
    }

    public function getExtra()
    {
        return $this->extra;
    }
}
