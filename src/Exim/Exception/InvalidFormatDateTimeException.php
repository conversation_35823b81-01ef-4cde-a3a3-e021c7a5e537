<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\Exception;

class InvalidFormatDateTimeException extends \ErrorException
{
    public string $format;
    public string $datetime;
    public array $errorType;

    /** @var mixed[] $errorType must have getLastErrors format */
    public function __construct(string $format, string $datetime, array $errorType = [])
    {
        parent::__construct();
        $this->format = $format;
        $this->datetime = $datetime;
        $this->errorType = $errorType;
    }

    public function getFormat(): string
    {
        return $this->format;
    }

    public function getDatetime(): string
    {
        return $this->datetime;
    }

    /** @return mixed[] */
    public function getErrorType(): array
    {
        return $this->errorType;
    }

    public function errorTypeToString(): string
    {
        return \implode(
            ' - ',
            \array_map(
                function (string $type, array $messages) {
                    return \sprintf(
                        '%s: %s',
                        $type,
                        \implode(', ', $messages)
                    );
                },
                \array_keys($this->errorType),
                \array_values($this->errorType)
            )
        );
    }
}
