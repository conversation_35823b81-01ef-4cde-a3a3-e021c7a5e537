<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim;

use Wizacha\Marketplace\Promotion\Bonus\Bonus;
use Wizacha\Marketplace\Promotion\Bonus\FixedBonus;
use Wizacha\Marketplace\Promotion\Bonus\PercentageBonus;
use Wizacha\Marketplace\Promotion\BonusTarget\BonusTargetProductInBasket;
use Wizacha\Marketplace\Promotion\MarketplacePromotion;
use Wizacha\AppBundle\Controller\Api\Promotion\BasketRuleType;

class MarketplaceDiscounts
{
    public const TRUE = "Y";
    public const FALSE = "N";

    /**
     * @var MarketplacePromotion
     * Cache property for Current Promotion to be able to detach that MVP from entity manager
     * to avoid memory leak on mass export
     */
    private static $currentPromotion;
    private static $normalizedPromotion;

    public static function getName(string $id): string
    {
        return static::getMarketplacePromotion($id)->getName();
    }

    public static function getActive(string $id): string
    {
        return static::getMarketplacePromotion($id)->isActive() ? static::TRUE : static::FALSE;
    }

    public static function getStartDate(string $id): string
    {
        return static::getMarketplacePromotion($id)->getStartTime()->format(\DateTime::RFC3339);
    }

    public static function getEndDate(string $id): string
    {
        return static::getMarketplacePromotion($id)->getEndTime()->format(\DateTime::RFC3339);
    }

    public static function getCoupon(string $id): string
    {
        return static::getMarketplacePromotion($id)->getCoupon();
    }

    public static function getRuleBasketHasProductInList(string $id): string
    {
        return static::hasRuleType(
            static::getNormalizedPromotion(static::getMarketplacePromotion($id)),
            (string) BasketRuleType::BASKET_HAS_PRODUCT_IN_LIST()
        ) ? static::TRUE : static::FALSE;
    }

    public static function getRuleProductList(string $id, string $productIdsDelimiter): string
    {
        $rule = static::getRuleDataByType(
            static::getNormalizedPromotion(static::getMarketplacePromotion($id)),
            (string) BasketRuleType::BASKET_HAS_PRODUCT_IN_LIST()
        );

        if (\is_array($rule) && $rule['products_ids'] && \is_array($rule['products_ids'])) {
            return implode($productIdsDelimiter, $rule['products_ids']);
        }

        return "";
    }

    public static function getRuleBasketPriceSuperiorTo(string $id): string
    {
        return static::getFormattedPriceRule($id, BasketRuleType::BASKET_PRICE_SUPERIOR_TO());
    }

    public static function getRuleBasketPriceInferiorTo(string $id): string
    {
        return static::getFormattedPriceRule($id, BasketRuleType::BASKET_PRICE_INFERIOR_TO());
    }

    public static function getRuleBasketPriceSuperiorOrEqualTo(string $id): string
    {
        return static::getFormattedPriceRule($id, BasketRuleType::BASKET_PRICE_SUPERIOR_OR_EQUAL_TO());
    }

    public static function getRuleBasketPriceInferiorOrEqualTo(string $id): string
    {
        return static::getFormattedPriceRule($id, BasketRuleType::BASKET_PRICE_INFERIOR_OR_EQUAL_TO());
    }

    public static function getRuleMaxUsageCount(string $id): string
    {
        return static::getFormattedPriceRule($id, BasketRuleType::MAX_USAGE_COUNT());
    }

    public static function getRuleMaxUsageCountPerUser(string $id): string
    {
        return static::getFormattedPriceRule($id, BasketRuleType::MAX_USAGE_COUNT_PER_USER());
    }

    public static function getDiscountType(string $id): string
    {
        // Only one bonus in a marketplace promotion
        $bonus = static::getMarketplacePromotion($id)
            ->getBonuses()
            ->first();

        return $bonus instanceof Bonus ? ucfirst($bonus->getType()) : "";
    }

    public static function getDiscountValue(string $id): string
    {
        // Only one bonus in a marketplace promotion
        $bonus = static::getMarketplacePromotion($id)
            ->getBonuses()
            ->first();

        if ($bonus instanceof FixedBonus) {
            $discountValue = static::formatPrice($bonus->getReduction()->getConvertedAmount());
        } elseif ($bonus instanceof PercentageBonus) {
            $discountValue = (string) $bonus->getReduction();
        } else {
            $discountValue = "";
        }

        return $discountValue;
    }

    public static function getBonusAppliedOn(string $id): string
    {
        return ucfirst(static::getMarketplacePromotion($id)->getTarget()->getName());
    }

    private static function getNormalizedPromotion(MarketplacePromotion $promotion): array
    {
        if (\is_array(static::$normalizedPromotion) === false
            || static::$normalizedPromotion['promotion_id'] !== $promotion->getId()
        ) {
            static::$normalizedPromotion = container()->get('serializer')->normalize($promotion);
        }

        return static::$normalizedPromotion;
    }

    /** @param array $promotion normalized promotion data */
    private static function hasRuleType(array $promotion, string $ruleType): bool
    {
        return \is_array(static::getRuleDataByType($promotion, $ruleType));
    }

    private static function getRuleDataByType(array $promotion, string $ruleType): ?array
    {
        if (isset($promotion['rule']) === false
            || \is_array($promotion['rule']) === false
        ) {
            return null;
        }

        if (isset($promotion['rule']['type'])
            && (isset($promotion['rule']['value']) || isset($promotion['rule']['products_ids']))
        ) {
            if ($ruleType === $promotion['rule']['type']) {
                return $promotion['rule'];
            }

            return null;
        }

        foreach ($promotion['rule']['items'] as $item) {
            if ($ruleType === $item['type']) {
                return $item;
            }
        }

        return null;
    }

    private static function getRuleValue(string $id, BasketRuleType $type): string
    {
        $rule = static::getRuleDataByType(
            static::getNormalizedPromotion(static::getMarketplacePromotion($id)),
            (string) $type
        );

        return \is_array($rule) && isset($rule['value']) ? (string) $rule['value'] : "";
    }

    private static function getFormattedPriceRule(string $id, BasketRuleType $type): string
    {
        return static::formatPrice(static::getRuleValue($id, $type) !== "" ? (float) static::getRuleValue($id, $type) : null);
    }

    /**
     * Keep only the current MarketplacePromotion (the one being exported) on the entity manager to avoid memory leak
     */
    private static function getMarketplacePromotion(string $id): MarketplacePromotion
    {
        if (static::$currentPromotion instanceof MarketplacePromotion) {
            if (static::$currentPromotion->getId() === $id) {
                return static::$currentPromotion;
            }

            // detach all related entities of the previous MVP from the entity manager
            $em = container()->get('doctrine.orm.entity_manager');
            $em->detach(static::$currentPromotion);
        }

        static::$currentPromotion = container()->get('marketplace.promotion.promotionservice')->get($id);
        // do not detach here : we need to keep that MPV in doctrine because we can't avoid
        // some "find($id)" in the export process which will re put it in the entity manager for ever

        return static::$currentPromotion;
    }

    private static function formatPrice(?float $price): string
    {
        return \is_null($price) === false ? number_format($price, 2, '.', '') : "";
    }
}
