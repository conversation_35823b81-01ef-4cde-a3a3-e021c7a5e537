<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Exim;

use Doctrine\DBAL\Driver\Connection;
use Wizacha\Component\Import\Exception\InvalidStructureException;
use Wizacha\Core\Accumulator;
use Wizacha\Core\Helper;
use Wizacha\Cscart\Exim;
use Wizacha\Exim\Exception\InvalidCsvLineException;

/**
 * Class CsvFile: Helpers for import/export csv
 * @package Wizacha\Exim
 */
class CsvFile
{
    protected const PRELOADER_INSERT_BULK = 256;

    /** @var resource */
    protected $handler;
    protected string $separator;
    protected array $pattern;

    protected Connection $connection;

    protected array $first_line = [];
    protected bool $is_utf8 = false;
    protected array $required;
    protected int $totalLine;

    protected bool $preload;
    protected ?string $table;

    /**
     * @param resource $handler
     *
     * @throws \Exception
     */
    public function __construct(
        $handler,
        array $pattern,
        string $separator = ';',
        bool $preload = true,
        Connection $connection = null
    ) {
        if (false === \is_resource($handler)
            || get_resource_type($handler) !== 'stream'
        ) {
            throw new \Exception('Expect stream resource');
        }

        $this->handler = $handler;
        $this->pattern = $pattern;
        $this->separator = $separator;
        $this->preload = $preload;

        /** TODO: proper injection */
        $this->connection = $connection ?? container()->get('database_connection');

        $this->required = [];
        if (\is_array($pattern['export_fields'])) {
            foreach ($pattern['export_fields'] as $name => $field) {
                if (false === empty($field['required'])) {
                    $this->required[] = empty($field['db_field']) ? $name : $field['db_field'];
                }
            }
        }
    }

    /**
     * Init handler and check first line
     *
     * @throws InvalidStructureException
     */
    protected function initGenerator(): bool
    {
        if (!rewind($this->handler)) {
            return false;
        }

        $first_line = fgets($this->handler);
        if ($first_line == false) {
            return false;
        }

        if (substr($first_line, 0, 3) == pack("CCC", 0xef, 0xbb, 0xbf)) {
            $first_line = substr($first_line, 3);
            $this->is_utf8 = true;
        } else {
            $this->is_utf8 = false;
        }

        $this->separator = Misc::checkSeparator($first_line, $this->separator);
        $this->first_line = \str_getcsv($first_line, $this->separator);

        $this->incorrectDelimiterInFirstLineGuard();

        if (true === \in_array('', $this->first_line)) {
            throw new InvalidStructureException(__('exim_error_empty_column_csv'));
        }

        return Exim::fn_analyze_schema($this->first_line, $this->pattern);
    }

    protected function getFirstLine(): array
    {
        return \array_merge(
            $this->first_line,
            ['line']
        );
    }

    protected function getOrderKeys(): array
    {
        $groupByKeys = \array_map(
            function (string $key): string {
                preg_match('/(?<table>\w+\.)?(?<column>\w+)/', $key, $matches);

                return $matches['column'];
            },
            $this->pattern['group_by']
            ?? $this->pattern['order_by']
            ?? $this->pattern['key']
            ?? $this->getFirstLine()
        );

        $orderByKeys = \array_intersect(
            $groupByKeys,
            $this->getFirstLine()
        );

        return \array_map(
            function (string $fieldIndex): string {
                return \sprintf(
                    '`f_%s` ASC',
                    \array_flip($this->getFirstLine())[$fieldIndex]
                );
            },
            $orderByKeys
        ) ?: [1];
    }

    protected function getFields(): array
    {
        return \array_map(
            function (string $field): string {
                return \sprintf(
                    '`f_%s` text NOT NULL',
                    $field
                );
            },
            \array_keys(
                $this->getFirstLine()
            )
        );
    }

    protected function getParameters(): array
    {
        return \array_map(
            function (): string {
                return '?';
            },
            $this->getFirstLine()
        );
    }

    protected function initTemporaryTable(): string
    {
        $table = \uniqid('exim_');

        $query = \sprintf(
            'CREATE TEMPORARY TABLE `%s` (%s)',
            $table,
            \implode(', ', $this->getFields())
        );
        $this->connection->exec(
            $query
        );

        return $table;
    }

    protected function readFile(): \Generator
    {
        $lineNumber = 1;
        $previous = null;

        while ($line = \fgetcsv($this->handler, 0, $this->separator)) {
            $lineNumber++;

            try {
                $data = $this->normalizeLine($line);
            } catch (\ErrorException $e) {
                continue;
            }

            if (false === $this->validateLine($data)
                || $data === $previous
            ) {
                continue;
            }

            $previous = $data;
            $data['line'] = $lineNumber;

            yield $data;
        }
    }

    private function getTemporaryTable(): string
    {
        return $this->table ??= $this->recordTemporaryTable();
    }

    /**
     * preload CSV in a temporary table
     */
    private function recordTemporaryTable(): string
    {
        $table = $this->initTemporaryTable();

        $insertAccumulator = $this->getInsertAccumulator($table);

        $this->connection->beginTransaction();

        foreach ($this->readFile() as $data) {
            $insertAccumulator->trigger($data);
        }
        $insertAccumulator->finalize();

        $this->connection->commit();

        return $table;
    }

    /**
     * Bulk insert queries
     */
    private function getInsertAccumulator(string $table): Accumulator
    {
        $insertClosure = function (\ArrayObject $bag) use ($table): void {
            $placeHolderLine = \sprintf(
                '(%s)',
                \implode(', ', $this->getParameters())
            );

            $placeHolders = \implode(
                ', ',
                \array_fill(
                    0,
                    $bag->count(),
                    $placeHolderLine
                )
            );

            $query =  \sprintf(
                <<<SQL
                INSERT INTO
                    `%s`
                VALUES
                    %s
                SQL,
                $table,
                $placeHolders
            );

            $insertStatement = $this->connection->prepare(
                $query
            );

            $params = \iterator_to_array(
                Helper::arrayFlatten(
                    $bag->getArrayCopy()
                ),
                false
            );

            $insertStatement->execute($params);
        };

        return new Accumulator(
            static::PRELOADER_INSERT_BULK,
            $insertClosure
        );
    }

    protected function readPreloadedFile(): \Generator
    {
        $temporaryTable = $this->getTemporaryTable();

        $statement = $this->connection->prepare(
            \sprintf(
                <<<SQL
                SELECT *
                FROM `%s`
                ORDER BY %s
                SQL,
                $temporaryTable,
                \implode(', ', $this->getOrderKeys())
            )
        );

        $statement->execute();

        while ($row = $statement->fetch()) {
            yield $row;
        }
    }

    /**
     * Yield each csv_line with pattern merge
     *
     * return \Generator<string[]>|bool
     * @throws InvalidStructureException
     */
    public function generator(): \Generator
    {
        if (true !== $this->initGenerator()) {
            throw new InvalidStructureException(__('exim_error_structure_csv'));
        }

        $rowGenerator = $this->preload
            ? $this->readPreloadedFile()
            : $this->readFile()
        ;

        foreach ($rowGenerator as $row) {
            $combinedRow = \array_combine(
                \array_values($this->getFirstLine()),
                \array_values($row)
            );
            $combinedRow['line'] = (int) $combinedRow['line'];

            yield $combinedRow;
        }

        return true;
    }

    /**
     * @param string[] $line
     */
    protected function validateLine(array $line): bool
    {
        return
            false === $this->isBlankLine($line)
            && true === $this->validateFields($line)
            && true === $this->checkRequired($line)
        ;
    }

    /**
     * @param string[] $line
     */
    protected function validateFields(array $line): bool
    {
        return \count($this->first_line) === \count($line);
    }

    /**
     * Return the number of lines without the header as returned by generator()
     */
    public function countLines(): int
    {
        return $this->totalLine ??= $this->processCountLines();
    }
    protected function processCountLines(): int
    {
        return \iterator_count($this->generator());
    }

    /**
     * Return true if all required field are not empty
     *
     * @param string[] $line
     */
    public function checkRequired(array $line): bool
    {
        if (\count($this->first_line) !== \count($line)) {
            return false;
        }

        $data = \array_combine($this->first_line, $line);

        foreach ($this->required as $field) {
            $fieldValue = trim($data[$field]);
            if (\strlen($fieldValue) === 0) {
                return false;
            }
        }

        return true;
    }

    /**
     * @param string[] $line
     *
     * @return string[] $line
     */
    private function normalizeLine(array $line): array
    {
        \array_walk(
            $line,
            function (&$field) {
                if (false === $this->is_utf8) {
                    $encoding = fn_detect_encoding($field);
                    if (false === empty($encoding)) {
                        $field = fn_convert_encoding($encoding, 'UTF-8', $field);
                    }
                }
                $field = \html_entity_decode($field, ENT_NOQUOTES);
            }
        );

        if (\count($this->first_line) !== \count($line)) {
            throw new InvalidCsvLineException();
        }

        return \array_combine(
            $this->first_line,
            $line
        );
    }

    /**
     * @param string[] $line
     */
    private function isBlankLine(array $line): bool
    {
        return 0 === \count(
            \array_filter($line)
        );
    }

    private function incorrectDelimiterInFirstLineGuard(): void
    {
        if (1 === \count($this->first_line)
            && false === \in_array(\current($this->first_line), array_keys($this->pattern['export_fields']))
        ) {
            throw new InvalidStructureException(__('exim_error_delimiter_csv'));
        }
    }
}
