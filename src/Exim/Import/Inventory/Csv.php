<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\Import\Inventory;

use Wizacha\Exim\Import\Inventory;
use Wizacha\Exim\Import\ImportCsv;
use Wizacha\Exim\Exception\DifferentProductCodeNotRegroupable;

class Csv extends importCsv
{
    /** @var Inventory */
    private $inventory;

    public function current(): Inventory
    {
        $this->accumulate();

        return $this->inventory;
    }

    public function key(): string
    {
        return $this->inventory->getCode();
    }

    /** Regroupement des lignes à importer pour avoir toutes les déclinaisons d'un produit ensemble */
    private function accumulate(): void
    {
        try {
            $this->inventory = new Inventory($this->file->current(), $this->getLineNumber());
            $this->file->next();
            $this->lineNumber++;

            while ($this->file->valid() && $this->inventory->getCode() != '') {
                $this->inventory = $this->inventory->regroup($this->file->current(), $this->getLineNumber());
                $this->file->next();
                $this->lineNumber++;
            }
        } catch (DifferentProductCodeNotRegroupable $e) {
            //stop regrouping as soon as another product is found
        }
    }
}
