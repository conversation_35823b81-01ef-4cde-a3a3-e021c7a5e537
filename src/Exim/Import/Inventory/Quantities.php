<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Exim\Import\Inventory;

use Wizacha\Marketplace\User\User;
use Wizacha\Exim\ProductStock;

class Quantities
{
    public function import(array $datas, array $options, string $langCode, array $progressId = [], string $jobId, $lines): void
    {
        ProductStock::ImportProductStocks($datas, $options, $langCode, $progressId, $jobId, $lines);
    }
}
