<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Exim\Import\Inventory;

use Wizacha\Marketplace\User\User;
use Wizacha\Exim\ProductPrices;

class Prices
{
    public function import(array $datas, array $options, string $langCode, array $progressId = [], string $jobId, $lines): void
    {
        ProductPrices::ImportProductPrices($datas, $options, $langCode, $progressId, $jobId, $lines);
    }
}
