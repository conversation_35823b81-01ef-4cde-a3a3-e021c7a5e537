<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\Import;

use Doctrine\DBAL\Connection;
use Psr\Log\LoggerInterface;
use Wizacha\Component\Import\ImportFactory;
use Wizacha\Component\Import\ImportService;
use Wizacha\Component\Import\JobType;
use Wizacha\Registry;

class AutomatedFeedsService
{
    private Connection $connection;
    private LoggerInterface $logger;
    private ImportFactory $importFactory;
    private ImportService $importService;
    private Registry $registry;

    public function __construct(
        Connection $connection,
        LoggerInterface $logger,
        ImportFactory $importFactory,
        ImportService $importService,
        Registry $registry
    ) {
        $this->connection = $connection;
        $this->logger = $logger;
        $this->importFactory = $importFactory;
        $this->importService = $importService;
        $this->registry = $registry;
    }

    /**
     * Treat product flows via API calls
     *
     * @return int[]
     */
    public function treatAutomatedFeeds(): ?array
    {
        $success = 0;

        $feeds = $this->getAutomatedFeeds();
        $total = \count($feeds);

        if ($total === 0) {
            return null;
        }

        $companiesFeeds  = [];

        foreach ($feeds as $feed) {
            $companiesFeeds[$feed['company_id']][] = $feed;
        }

        foreach ($companiesFeeds as $companyId => $companyFeeds) {
            // Set companyId which is used by the importer
            $this->registry->set(
                ['runtime', 'company_id'],
                $companyId
            );

            try {
                $importHandlers = $this->importFactory->getHandlersFromAutomatedFeeds($companyId, $companyFeeds);
            } catch (\Exception $exception) {
                $this->logger->error(
                    'Automated Feeds For Company: import failed',
                    [
                        'company_id' => $companyId,
                        'code' => $exception->getCode(),
                        'message' => $exception->getMessage(),
                    ]
                );

                continue;
            }

            foreach ($importHandlers as $importHandler) {
                if (JobType::PRODUCTS()->equals($importHandler['handler']->getJob()->getType()) === true) {
                    $this->importService->catalog($importHandler['handler']);
                } else {
                    $this->importService->importInventory($importHandler['handler'], $importHandler['importType']);
                }
                $success++;
            }
        }

        return [
            $success,
            $total,
        ];
    }

    /**
     * @return string[][]
     */
    private function getAutomatedFeeds()
    {
        return $this->connection->executeQuery(
            'SELECT
                company_id,
                pattern_id,
                url
            FROM
                cscart_w_automated_feeds
            WHERE
                pattern_id in (?)
            ORDER BY
                company_id,
                FIELD(pattern_id, ?, ?, ?)
            ',
            [
                [
                    ImportFactory::PATTERN_ID_PRODUCTS,
                    ImportFactory::PATTERN_ID_INVENTORY_PRICE,
                    ImportFactory::PATTERN_ID_INVENTORY_QUANTITIES
                ],
                ImportFactory::PATTERN_ID_PRODUCTS,
                ImportFactory::PATTERN_ID_INVENTORY_PRICE,
                ImportFactory::PATTERN_ID_INVENTORY_QUANTITIES
            ],
            [Connection::PARAM_STR_ARRAY]
        )->fetchAll();
    }
}
