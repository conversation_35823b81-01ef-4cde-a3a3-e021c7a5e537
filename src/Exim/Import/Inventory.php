<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\Import;

use Wizacha\Exim\Exception\DifferentProductCodeNotRegroupable;
use Wizacha\Marketplace\GlobalState\GlobalState;

class Inventory extends ImportProduct
{
    /**
     * Merge de $data avec le produit actuel
     * @param array $data
     * @param int $lineNumber
     */
    protected function merge(array $data, int $lineNumber): void
    {
        $data2 = fn_product_set_default_values_for_line($data);
        $data['declinations'] = [];

        $locale = \array_key_exists('lang_code', $data) === true && \strlen($data['lang_code']) > 0
            ? $data['lang_code']
            : (string) GlobalState::contentLocale();

        if (\array_key_exists($locale, $this->data) === false) {
            $this->data[$locale] = $data2;
        }

        //construction du tableau des declinaisons, on ne retient que ces clés la
        $declination = array_intersect_key(
            $data,
            [
                'Combination' => 1,
                'Combination Code' => 1,
                'price' => 1,
                'crossed_out_price' => 1,
                'Price tiers' => 1,
                'amount' => 1
            ]
        );

        //ajout dans le produit principale, pour la langue $locale, d'une declinaison
        $this->data[$locale]['declinations'][] = $declination;
        $this->data[$locale]['lines'][] = $lineNumber;
    }
}
