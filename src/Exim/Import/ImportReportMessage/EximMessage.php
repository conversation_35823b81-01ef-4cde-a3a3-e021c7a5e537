<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\Import\ImportReportMessage;

use MyCLabs\Enum\Enum;

class EximMessage extends Enum
{
    public const EXIM_IMPORT_OPTION_SYSTEM_VARIANTS_DISABLED_FAIL = 'exim_import_option_system_variants_disabled_fail';
    public const EXIM_FAILED_TO_PROCESS_IMAGE_DECLINATION = 'exim_failed_to_process_image_declination';
    public const EXIM_UNSUPPORTED_IMAGE_EXTENSION = 'exim_unsupported_image_extension';
    public const EXIM_PRICE_TIERS_AND_PRICE = 'exim_price_tiers_and_price';
    public const EXIM_SAVE_PRICE_TIERS_FAIL = 'exim_save_price_tiers_fail';
    public const EXIM_SAVE_PRICE_TIERS_NO_FEATURE_FLAG = 'exim_save_price_tiers_no_feature_flag';
    public const EXIM_IMPORT_OPTION_SYSTEM_VARIANTS_FAIL = 'exim_import_option_system_variants_fail';
}
