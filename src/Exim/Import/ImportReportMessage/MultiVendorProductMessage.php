<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\Import\ImportReportMessage;

use MyCLabs\Enum\Enum;

class MultiVendorProductMessage extends Enum
{
    public const EXIM_ERROR_MVP_LANGUAGE_NOT_VALID = 'exim_error_mvp_language_not_valid';
    public const EXIM_ERROR_MVP_UNKNOWN_ID = 'exim_error_mvp_unknown_id';
    public const EXIM_WARNING_MVP_PRODUCT_TEMPLATE_NOT_VALID = 'exim_warning_mvp_product_template_not_valid';
    public const EXIM_SUCCESS_MVP_IMPORT = 'exim_success_mvp_import';
    public const EXIM_ERROR_MVP_SAVE = 'exim_error_mvp_save';
    public const EXIM_WARNING_MVP_ATTRIBUTES = 'exim_warning_mvp_attributes';
    public const EXIM_WARNING_MVP_FREE_ATTRIBUTES = 'exim_warning_mvp_free_attributes';
    public const EXIM_WARNING_MVP_IMAGE = 'exim_warning_mvp_image';
    public const EXIM_UNVALID_URL_VIDEO = 'exim_unvalid_url_video';
    public const EXIM_VIDEO_SIZE_EXCEPTION = 'exim_video_size_exception';
    public const EXIM_PRODUCT_VIDEO_FAILED = 'exim_product_video_failed';
}
