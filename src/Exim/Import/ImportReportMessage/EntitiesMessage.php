<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\Import\ImportReportMessage;

use MyCLabs\Enum\Enum;

class EntitiesMessage extends Enum
{
    public const EXIM_MISSING_REQUIRED_FIELDS = 'exim_missing_required_fields';
    public const EXIM_LINK_CATEGORIES_CREATE_FAILED = 'exim_link_categories_create_failed';
    public const EXIM_LINK_CATEGORIES_UPDATE = 'exim_link_categories_update';
    public const EXIM_LINK_CATEGORIES_CREATE = 'exim_link_categories_create';
}
