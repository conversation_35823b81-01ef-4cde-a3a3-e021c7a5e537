<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\Import\ImportReportMessage;

use MyCLabs\Enum\Enum;

class RelatedProductMessage extends Enum
{
    public const EXIM_RELATED_PRODUCT_UPDATE = 'exim_related_product_update';
    public const EXIM_RELATED_PRODUCT_CREATE = 'exim_related_product_create';
    public const EXIM_RELATED_PRODUCT_DELETE = 'exim_related_product_delete';
    public const EXIM_RELATED_PRODUCT_DELETE_NOT_FOUND = 'exim_related_product_delete_not_found';
    public const EXIM_RELATED_PRODUCT_INVALID_FIELDS = 'exim_related_product_error_invalid_fields';
    public const EXIM_RELATED_PRODUCT_NOT_FOUND_COMPANY_ID = 'exim_related_product_error_not_found_company_id';
    public const EXIM_RELATED_PRODUCT_NOT_FOUND_PRODUCT_CODE = 'exim_related_product_error_not_found_product_code';
    public const EXIM_RELATED_PRODUCT_NOT_FOUND_RELATED_PRODUCT = 'exim_related_product_error_not_found_related_product';
    public const EXIM_RELATED_PRODUCT_REQUIRED_FIELD = 'exim_related_product_error_required_field';
    public const EXIM_RELATED_PRODUCT_EXTERNAL_COMPANY = 'exim_related_product_error_external_company';
    public const EXIM_RELATED_PRODUCT_SAME_PRODUCTS = 'exim_related_product_error_same_products';
}
