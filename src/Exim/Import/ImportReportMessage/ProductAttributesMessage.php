<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\Import\ImportReportMessage;

use MyCLabs\Enum\Enum;

class ProductAttributesMessage extends Enum
{
    public const EXIM_PRODUCT_ATTRIBUTES_CREATE = 'exim_product_attributes_create';
    public const EXIM_PRODUCT_ATTRIBUTES_DELETE = 'exim_product_attributes_delete';
    public const EXIM_PRODUCT_ATTRIBUTES_INCOHERENCY = 'exim_product_attributes_error_incoherency';
    public const EXIM_PRODUCT_ATTRIBUTES_INVALID_FIELDS = 'exim_product_attributes_error_invalid_fields';
    public const EXIM_PRODUCT_ATTRIBUTES_INVALID_ID = 'exim_product_attributes_error_invalid_id';
    public const EXIM_PRODUCT_ATTRIBUTES_INVALID_VALUE = 'exim_product_attributes_error_invalid_value';
    public const EXIM_PRODUCT_ATTRIBUTES_NOT_FOUND_ATTRIBUTE = 'exim_product_attributes_error_not_found_attribute';
    public const EXIM_PRODUCT_ATTRIBUTES_NOT_FOUND_COMPANY_ID = 'exim_product_attributes_error_not_found_company_id';
    public const EXIM_PRODUCT_ATTRIBUTES_NOT_FOUND_PRODUCT_CODE = 'exim_product_attributes_error_not_found_product_code';
    public const EXIM_PRODUCT_ATTRIBUTES_REQUIRED_FIELD = 'exim_product_attributes_error_required_field';
    public const EXIM_PRODUCT_ATTRIBUTES_TOO_MUCH_VALUES = 'exim_product_attributes_error_too_much_values';
}
