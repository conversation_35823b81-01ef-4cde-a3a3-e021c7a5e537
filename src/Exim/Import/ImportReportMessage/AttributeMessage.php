<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\Import\ImportReportMessage;

use MyCLabs\Enum\Enum;

class AttributeMessage extends Enum
{
    public const EXIM_ATTRIBUTES_IGNORED = 'exim_attributes_ignored';
    public const EXIM_ATTRIBUTES_CREATE = 'exim_attributes_create';
    public const EXIM_ATTRIBUTES_CREATE_FAILED = 'exim_attributes_create_failed';
    public const EXIM_ATTRIBUTES_UPDATE = 'exim_attributes_update';
}
