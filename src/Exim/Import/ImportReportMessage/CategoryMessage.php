<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\Import\ImportReportMessage;

use MyCLabs\Enum\Enum;

class CategoryMessage extends Enum
{
    public const EXIM_CATEGORY_UPDATE = 'exim_category_update';
    public const EXIM_CATEGORY_CREATE = 'exim_category_create';
    public const EXIM_CATEGORY_CREATE_FAILED = 'exim_category_create_failed';
    public const EXIM_CATEGORY_IMAGE_WARNING = 'exim_category_image_warning';
    public const EXIM_CATEGORY_DELETE = 'exim_category_delete';
}
