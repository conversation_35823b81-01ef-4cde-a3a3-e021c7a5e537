<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\Import\ImportReportMessage;

use MyCLabs\Enum\Enum;

class ProductMessage extends Enum
{
    public const ERROR_EXIM_INVALID_FIELDS = 'error_exim_invalid_fields';
    public const ERROR_EXIM_COULD_NOT_PROCESS_LINE = 'error_exim_could_not_process_line';
    public const EXIM_EMPTY_INCLUDED_DIVISIONS = 'exim_empty_included_divisions';
    public const EXIM_FAILED_TO_PROCESS_IMAGE = 'exim_failed_to_process_image';
    public const EXIM_FIELD_VALUE_NOT_MATCH_TEMPLATE = 'exim_field_value_not_match_template';
    public const EXIM_INVALID_EXCLUDED_DIVISIONS = 'exim_invalid_excluded_divisions';
    public const EXIM_INVALID_INCLUDED_DIVISIONS = 'exim_invalid_included_divisions';
    public const EXIM_MISSING_DIVISIONS = 'exim_missing_divisions';
    public const EXIM_MISSING_REQUIRED_FIELDS = 'exim_missing_required_fields';
    public const EXIM_PRODUCT_ATTACHMENTS_FAILED = 'exim_product_attachments_failed';
    public const EXIM_PRODUCT_CREATE = 'exim_product_create';
    public const EXIM_PRODUCT_CREATE_FAILED = 'exim_product_create_failed';
    public const EXIM_PRODUCT_DECLINATION_CREATE = 'exim_product_declination_create';
    public const EXIM_PRODUCT_INVALID_MAX_PRICE_ADJUSTMENT = 'exim_product_invalid_max_price_adjustment';
    public const EXIM_PRODUCT_NOT_MATCH_CATEGORY = 'exim_product_not_match_category';
    public const EXIM_PRODUCT_NOT_MATCH_TEMPLATE = 'exim_product_not_match_template';
    public const EXIM_PRODUCT_UNABLE_RETRIEVE_SHIPPING = 'exim_product_unable_retrieve_shipping';
    public const EXIM_PRODUCT_UPDATE = 'exim_product_update';
    public const EXIM_UNSUPPORTED_IMAGE_EXTENSION = 'exim_unsupported_image_extension';
    public const EXIM_UNSUPPORTED_SHIPPING_FORMAT = 'exim_unsupported_shipping_format';
    public const EXIM_PRODUCT_VIDEO_FAILED = 'exim_product_video_failed';
}
