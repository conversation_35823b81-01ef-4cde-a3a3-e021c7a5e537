<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\Import\ImportReportMessage;

use MyCLabs\Enum\Enum;

class AttributeVariantMessage extends Enum
{
    public const EXIM_VARIANTS_CREATE_FAILED = 'exim_variants_create_failed';
    public const EXIM_VARIANTS_UPDATE = 'exim_variants_update';
    public const EXIM_VARIANTS_CREATE = 'exim_variants_create';
    public const EXIM_FAILED_TO_PROCESS_IMAGE = 'exim_failed_to_process_image';
}
