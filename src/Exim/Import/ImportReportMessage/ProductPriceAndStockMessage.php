<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\Import\ImportReportMessage;

use MyCLabs\Enum\Enum;

class ProductPriceAndStockMessage extends Enum
{
    public const EXIM_WRONG_PRODUCT_DECLINATION = 'exim_wrong_product_declination';
    public const EXIM_PRODUCT_PRICE_UPDATE = 'exim_product_price_update';
    public const EXIM_PRODUCT_DECLINATION_PRICE_UPDATE = 'exim_product_declination_price_update';
    public const EXIM_PRODUCT_QUANTITIES_UPDATE = 'exim_product_quantities_update';
    public const EXIM_PRODUCT_QUANTITIES_PRICE_UPDATE = 'exim_product_quantities_price_update';
}
