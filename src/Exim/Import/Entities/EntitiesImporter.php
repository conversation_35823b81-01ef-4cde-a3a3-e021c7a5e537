<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\Import\Entities;

use Tygh\Languages\Languages;
use Wizacha\Component\Import\EximJob;
use Wizacha\Component\Import\EximJobService;
use Wizacha\Component\Import\JobStatus;
use Wizacha\Cscart\Exim;
use Wizacha\Exim\Import\ImportReportMessage\EntitiesMessage;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\User\User;

class EntitiesImporter
{
    private $pattern = [];
    private $options = [];
    private $lang;

    private $altFields = [];
    private $altKeys = [];
    private $defaultGroups = [];
    private $groupFields = [];
    private $primaryFields = [];
    private $requiredFields = [];
    private $tableGroups = [];

    /**
     * @var null|int
     */
    private $lastInsertId = null;

    /**
     * @var EximJob
     */
    private $job;

    public function __construct(array $pattern, array $options, EximJob $job)
    {
        $this->pattern = $pattern;
        $this->options = $options;
        $this->job = $job;
        $this->setKeys($this->pattern);
    }

    public function import(int $lineNumber, array $line)
    {
        // If job not exist then stop the process
        $jobService = container()->get('marketplace.import.job_service');
        try {
            $job = $jobService->get($this->job->getId());
        } catch (NotFound $exception) {
            return true;
        }

        if ($job->getStatus()->equals(JobStatus::CANCELED()) === true) {
            return true;
        }

        $user = $this->getUser();
        $exist = true;
        $skip = false;
        $line = $this->preProcessing($line, $user, $lineNumber);
        $line = $this->parseLang($line);

        // Check required fields
        foreach ($this->requiredFields as $field) {
            if (empty($line[$this->lang][$field]) && !is_numeric($line[$this->lang][$field])) {
                if (empty($this->altFields[$field]) || empty($line[$this->lang][$this->altFields[$field]])) {
                    EximJobService::error(
                        EntitiesMessage::EXIM_MISSING_REQUIRED_FIELDS,
                        $this->job->getId(),
                        (string) $lineNumber,
                        null,
                        $this->getRealFields($field)
                    );

                    return false;
                }
            }
        }

        // @only pattern link_categories
        // Check if converting groups exist and convert fields if it is so
        Exim::fn_import_prepare_groups(
            $line[$this->lang],
            Exim::fn_import_build_groups('convert_put', $this->pattern['export_fields']),
            $this->options
        );

        // Get $primaryObjectId
        $primaryObjectId = "";
        if (false === $this->skipGetPrimaryObjectId() && isset($this->pattern['table'])) {
            $altKeys = $this->getAltKeys($line[$this->lang]);
            if (\count($altKeys) === 0) {
                throw new \Exception("Alt Keys must not be empty");
            }

            $primaryObjectId = db_get_row('SELECT ' . implode(', ', $this->pattern['key']) . ' FROM ?:' . $this->pattern['table'] . ' WHERE ?w', $altKeys);
        }

        // @not categories
        if ($this->hasProcessData()) {
            Exim::fn_exim_processing('import', $this->pattern['import_process_data'], $this->options, [
                'object' => &$line[$this->lang],
                'options' => &$this->options,
                'skip_record' => &$skip,
                'data' => &$line,
                'jobId' => $this->job->getId(),
            ]);
        }

        if ($skip) {
            if ($this->pattern['pattern_id'] === 'link_categories') {
                EximJobService::error(
                    EntitiesMessage::EXIM_LINK_CATEGORIES_CREATE_FAILED,
                    $this->job->getId(),
                    (string) $lineNumber,
                    null,
                    $line[$this->lang]['vendor_category']
                );
            }

            return false;
        }

        // @not categories
        $primaryObjectId = $this->processingDb($line[$this->lang], $primaryObjectId, $exist);

        if ($primaryObjectId === false) {
            return false;
        }

        // @only categories
        $this->processingGroups($line, $primaryObjectId);

        // @not categories
        $this->processingPostDb($line, $primaryObjectId);
    }

    private function getUser(): ?User
    {
        $userId = $this->job->getUserId();

        try {
            return container()->get('marketplace.user.user_service')->get($userId);
        } catch (\Exception $e) {
            return null;
        }
    }

    private function getRealFields($field): string
    {
        foreach ($this->pattern['export_fields'] as $fields => $data) {
            if (isset($data['db_field']) && $data['db_field'] === $field) {
                return $fields;
            }
        }

        $keys = array_flip($this->altKeys);

        return $keys[$field] ?? $field;
    }

    /**
     * Format line before import
     *
     * @param array     $line
     * @param null|User $user
     * @param int       $lineNumber
     *
     * @return array
     */
    private function preProcessing(array $line, ?User $user, int $lineNumber): array
    {
        if ($this->hasPreProcessing()) {
            $result = true;
            foreach ($this->pattern['pre_processing'] as $schema) {
                $newLine = [$line];
                $pattern = $this->pattern;

                $args = Exim::fn_exim_get_values($schema['args'], [], $this->options, [], [
                    'import_data' => &$newLine,
                    'pattern'     => &$pattern,
                    'user'        => $user,
                    'jobId'       => $this->job->getId(),
                    'lineNumber'  => $lineNumber,
                ], '');
                $result = $result && \call_user_func_array($schema['function'], $args);

                $this->pattern = $pattern;
                $line = current($newLine);
            }
        }

        return $line;
    }

    /**
     * Rebuild line with lang as key
     * @param array $line
     */
    private function parseLang($line): array
    {
        $newLine = [$line];
        $translate = Exim::fn_import_parse_languages($this->pattern, $newLine);
        $line = current($newLine);

        if ($translate) {
            $this->lang = strtolower(reset(array_keys($line)));
        } else {
            $this->lang = (string) GlobalState::fallbackLocale();

            $this->requiredFields[] = 'lang_code';

            return [strtolower($this->lang) => $line];
        }

        return $line;
    }

    /**
     * Set variables from array pattern
     * @param array $pattern
     */
    private function setKeys(array $pattern): void
    {
        if (!empty($pattern['references'])) {
            $this->tableGroups = $pattern['references'];
        }

        foreach ($pattern['export_fields'] as $field => $data) {
            $dbField = empty($data['db_field']) ? $field : $data['db_field'];

            // Collect fields with default values
            if (isset($data['default'])) {
                if (\is_array($data['default'])) {
                    $this->defaultGroups[$dbField] = \call_user_func_array(array_shift($data['default']), $data['default']);
                } else {
                    $this->defaultGroups[$dbField] = $data['default'];
                }
            }

            // Get alt keys for primary table
            if (!empty($data['alt_key'])) {
                $this->altKeys[$field] = $dbField;
            }

            if (!empty($data['alt_field'])) {
                $this->altFields[$dbField] = $data['alt_field'];
            }

            if (!empty($data['required']) && $data['required'] == true) {
                $this->requiredFields[] = $dbField;
            }

            if (!isset($data['linked']) || $data['linked'] == true) {
                // Get fields for primary table
                if (empty($data['table']) || $data['table'] == $pattern['table']) {
                    $this->primaryFields[$field] = $dbField;
                }

                // Group fields by tables
                if (!empty($data['table'])) {
                    $this->tableGroups[$data['table']]['fields'][$dbField] = true;
                }
            }

            // @only categories
            if (!empty($data['process_put'])) {
                $args = $data['process_put'];
                $function = array_shift($args);
                $this->groupFields[] = [
                    'function' => $function,
                    'this_field' => empty($data['db_field']) ? $field : $data['db_field'],
                    'args' => $args,
                    'table' => !empty($data['table']) ? $data['table'] : '',
                    'multilang' => !empty($data['multilang']) ? true : false,
                    'return_result' => !empty($data['return_result']) ? $data['return_result'] : false,
                ];
            }
        }
    }

    private function getAltKeys(array $line): array
    {
        $altKeys = [];

        foreach ($this->altKeys as $field) {
            if (!isset($line[$field])) {
                continue;
            }

            //Added || for admin company_id and case '' for mvp imports
            if (!empty($line[$field]) || $line[$field] === 0) {
                $altKeys[$field] = $line[$field];
            } elseif (!empty($this->altFields[$field])) {
                $altKeys[$this->altFields[$field]] = $line[$this->altFields[$field]];
            }
        }

        return $altKeys;
    }

    private function processingGroups(array $line, $primaryObjectId): void
    {
        // Check if lang had changed to previous line to set primaryKey
        if ($this->lang !== (string) GlobalState::fallbackLocale() && empty($line[$this->lang][$this->getPrimaryKey()])) {
            $line[$this->lang][$this->getPrimaryKey()] = $this->lastInsertId;
        }

        if (\count($this->groupFields) > 0) {
            foreach ($this->groupFields as $group) {
                $args = [];
                $useGroup = true;

                foreach ($group['args'] as $ak => $av) {
                    foreach ($line as $langCode => &$value) {
                        if ($av == '#key') {
                            $args[$ak] = (sizeof($primaryObjectId) >= 1) ? reset($primaryObjectId) : $primaryObjectId;
                        } elseif ($av == '#lang_code') {
                            $args[$ak] = $langCode;
                        } elseif ($av == '#row') {
                            $thisId = $group['this_field'];
                            $multiLang = false;

                            if (!empty($this->pattern['export_fields'][$thisId]['multilang'])) {
                                $multiLang = true;
                            } else {
                                foreach ($this->pattern['export_fields'] as $field) {
                                    if (!empty($field['multilang']) && !empty($field['db_field']) && $field['db_field'] == $thisId) {
                                        $multiLang = true;
                                        break;
                                    }
                                }
                            }

                            if ($multiLang) {
                                $args[$ak][$langCode] = &$value;
                            } else {
                                $args[$ak] = $value;
                                break;
                            }
                        } elseif ($av == '#this') {
                            // If we do not have this field in the import line, do not apply the function
                            $thisId = $group['this_field'];
                            $multiLang = false;

                            if (!isset($value[$thisId])) {
                                $useGroup = false;
                                break;
                            }

                            if (!empty($this->pattern['export_fields'][$thisId]['multilang'])) {
                                $multiLang = true;
                            } else {
                                foreach ($this->pattern['export_fields'] as $field) {
                                    if (!empty($field['multilang']) && !empty($field['db_field']) && $field['db_field'] == $thisId) {
                                        $multiLang = true;
                                        break;
                                    }
                                }
                            }

                            if ($multiLang) {
                                $args[$ak][$langCode] = $value[$group['this_field']];
                            } else {
                                $args[$ak] = $value[$group['this_field']];
                                break;
                            }
                        } elseif ($av == '#company_id') {
                            $args[$ak] = $this->job->getCompanyId();
                        } elseif (strpos($av, '@') !== false) {
                            $opt = str_replace('@', '', $av);
                            $args[$ak] = $this->options[$opt];
                        } elseif ($av == '#jobId') {
                            $args[$ak] = $this->job->getId();
                        } else {
                            $args[$ak] = $av;
                        }

                        if (empty($group['multilang'])) {
                            break;
                        }
                    }
                }

                if ($useGroup == false) {
                    continue;
                }

                $result = \call_user_func_array($group['function'], $args);

                $this->lastInsertId = $line[$this->lang][$this->getPrimaryKey()];

                if ($group['return_result'] == true) {
                    foreach (array_keys($line) as $lang) {
                        $line[$lang][$group['this_field']] = $result;
                    }
                } else {
                    // Remove processed fields from table groups
                    if (!empty($group['table'])) {
                        unset($this->tableGroups[$group['table']]['fields'][$group['this_field']]);
                    }
                }
            }
        }
    }

    private function processingDb(array $line, $primaryObjectId, bool &$exists)
    {
        if (!(isset($this->pattern['import_skip_db_processing']) && $this->pattern['import_skip_db_processing'])) {
            if (empty($primaryObjectId)) {
                $exists = false;

                // For new objects - fill the default values
                if (!empty($this->defaultGroups)) {
                    foreach ($this->defaultGroups as $field => $value) {
                        if (empty($line[$field])) {
                            $line[$field] = $value;
                        }
                    }
                }
            }

            if ($exists == true) {
                db_query('UPDATE ?:' . $this->pattern['table'] . ' SET ?u WHERE ?w', $line, $primaryObjectId);

                if ($this->pattern['pattern_id'] === 'link_categories') {
                    EximJobService::info(
                        EntitiesMessage::EXIM_LINK_CATEGORIES_UPDATE,
                        $this->job->getId(),
                        (string) $line['line'],
                        (int) $primaryObjectId,
                        $line['vendor_category']
                    );
                }
            } else {
                $id = db_query('INSERT INTO ?:' . $this->pattern['table'] . ' ?e', $line);

                if ($this->pattern['pattern_id'] === 'link_categories') {
                    EximJobService::info(
                        EntitiesMessage::EXIM_LINK_CATEGORIES_CREATE,
                        $this->job->getId(),
                        (string) $line['line'],
                        (int) $primaryObjectId,
                        $line['vendor_category']
                    );
                }

                if ($id !== true) {
                    $primaryObjectId = [reset($this->pattern['key']) => $id];
                } else {
                    foreach ($this->pattern['key'] as $value) {
                        $primaryObjectId[$value] = $line[$value];
                    }
                }
            }
        }

        return $primaryObjectId;
    }

    private function processingPostDb(array $line, $primaryObjectId)
    {
        if (!(isset($this->pattern['import_skip_db_processing']) && $this->pattern['import_skip_db_processing'])) {
            foreach ($this->tableGroups as $table => $tdata) {
                if (isset($tdata['import_skip_db_processing']) && $tdata['import_skip_db_processing']) {
                    break;
                }

                foreach ($line as $value) {
                    $data = [];
                    $where = [];

                    // If alternative key is defined, use it
                    if (!empty($tdata['alt_key'])) {
                        foreach ($tdata['alt_key'] as $key) {
                            if (\strval($key) == '#key') {
                                $where = fn_array_merge($where, $primaryObjectId);
                            } elseif (strpos($key, '@') !== false) {
                                $opt = str_replace('@', '', $key);
                                $where[$key] = $this->options[$opt];
                            } else {
                                $where[$key] = $value[$key];
                            }
                        }
                    } else {
                        $vars = ['key' => $primaryObjectId];
                        if (!empty($value['lang_code'])) {
                            $vars['lang_code'] = $value['lang_code'];
                        }
                        $where = Exim::fn_exim_get_values($tdata['reference_fields'], $this->pattern, $this->options, $vars);
                    }

                    // Now, build update fields array
                    if (!empty($tdata['fields'])) {
                        foreach ($tdata['fields'] as $importField => $set) {
                            if (!isset($value[$importField])) {
                                continue;
                            }
                            $data[$importField] = $value[$importField];
                        }
                    }

                    // Check if object exists
                    $isExists = db_get_field("SELECT COUNT(*) FROM ?:$table WHERE ?w", $where);
                    if ($isExists == true && !empty($data)) {
                        db_query("UPDATE ?:$table SET ?u WHERE ?w", $data, $where);
                    } elseif (empty($isExists)) { // if reference does not exist, we should insert it anyway to avoid inconsistency
                        $data = fn_array_merge($data, $where);

                        if (substr($table, -13) == '_descriptions' && isset($data['lang_code'])) {
                            // add description for all cart languages when adding object line
                            foreach (Languages::getAll() as $data['lang_code'] => $langV) {
                                db_query("REPLACE INTO ?:$table ?e", $data);
                            }
                        } else {
                            db_query("INSERT INTO ?:$table ?e", $data);
                        }
                    }

                    if (empty($data['lang_code'])) {
                        break;
                    }
                }
            }
        }
    }

    private function hasPreProcessing(): bool
    {
        return !empty($this->pattern['pre_processing']);
    }

    private function hasProcessData(): bool
    {
        return !empty($this->pattern['import_process_data']);
    }

    private function getPrimaryKey(): string
    {
        return current($this->pattern['key']);
    }

    private function skipGetPrimaryObjectId(): bool
    {
        return isset($this->pattern['import_skip_get_primary_object_id']) && true === $this->pattern['import_skip_get_primary_object_id'];
    }
}
