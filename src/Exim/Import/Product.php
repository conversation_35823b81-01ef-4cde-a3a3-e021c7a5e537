<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\Import;

use Wizacha\Marketplace\GlobalState\GlobalState;

class Product extends ImportProduct
{
    protected const DECLINATION_KEYS = [
        'Combination Code',
        'combination',
        'Combination image URL',
        'Combination Supplier Ref',
        'Combination Alt Text',
    ];

    protected const SHARED_KEYS = [
        'amount',
        'price',
        'crossed_out_price',
        'Price tiers',
        'affiliate_link',
        'infinite_stock'
    ];

    /**
     * Merge de $data avec le produit actuel
     * @param array $data
     * @param int $lineNumber
     */
    protected function merge(array $data, int $lineNumber): void
    {
        $data = fn_product_set_default_values_for_line($data);
        $data['declinations'] = [];

        $locale =  \array_key_exists('lang_code', $data) && mb_strlen($data['lang_code']) > 0
            ? $data['lang_code']
            : (string) GlobalState::contentLocale();

        //construction du tableau des declinaisons, on ne retient que ces clés la
        $declination = array_intersect_key(
            $data,
            \array_flip(
                \array_merge(
                    static::DECLINATION_KEYS,
                    static::SHARED_KEYS
                )
            )
        );

        $product = \array_filter(
            $data,
            function (string $key) {
                return false === \in_array($key, static::DECLINATION_KEYS);
            },
            ARRAY_FILTER_USE_KEY
        );

        //si il n'y a pas de données pour la langue $locale, on enregistre les données $data dans la langue $locale
        if (false === isset($this->data[$locale])) {
            $this->data[$locale] = $product;
        } else {
            $this->data[$locale] = \array_merge(
                $this->data[$locale],
                \array_filter($product)
            );
        }

        //ajout dans le produit principale, pour la langue $locale, d'une declinaison
        $this->data[$locale]['declinations'][] = $declination;
        $this->data[$locale]['lines'][] = $lineNumber;
    }
}
