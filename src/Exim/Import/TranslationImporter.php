<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\Import;

use Wizacha\AppBundle\Service\TranslationService;
use W<PERSON>cha\Async\Dispatcher;
use Wizacha\Component\Import\EximJob;
use Wizacha\Component\Import\EximJobService;
use Wizacha\Component\Import\ImportHandler;
use Wizacha\Component\Import\JobStatus;
use Wizacha\Component\Locale\Exception\LocaleNotFound;

class TranslationImporter
{
    private Dispatcher $dispatcher;
    private TranslationService $translationService;

    /** @var array mixed[][] */
    private array $translationsBatch = [];
    /** @var array int[][]  */
    private array $translationsBatchLines = [];

    public function __construct(Dispatcher $dispatcher, TranslationService $translationService)
    {
        $this->dispatcher = $dispatcher;
        $this->translationService = $translationService;
    }

    public function startImport(ImportHandler $handler): void
    {
        foreach ($handler->getIterator() as $line) {
            $this->batchImport(
                $handler->getJob(),
                $line['name'],
                $line['value'],
                $line['lang_code'],
                $line['line']
            );
        }

        $this->finishImport($handler->getJob());
    }

    /** Regroup translations into array before importing them, to optimize cache clearing */
    private function batchImport(EximJob $job, string $name, string $value, string $langCode, int $line): void
    {
        $this->translationsBatch[$langCode][$name] = $value;
        $this->translationsBatchLines[$langCode][$name] = $line;

        if (\count($this->translationsBatch[$langCode]) >= TranslationService::TRANSLATION_BATCH_SIZE) {
            $this->import($job, $langCode, $this->translationsBatch, $this->translationsBatchLines);
            $this->translationsBatch[$langCode] = [];
            $this->translationsBatchLines[$langCode] = [];
        }
    }

    /** Import last translations, when TranslationService::TRANSLATION_BATCH_SIZE is not reached */
    private function finishImport(EximJob $job): void
    {
        foreach ($this->translationsBatch as $language => $batch) {
            $this->import($job, $language, $this->translationsBatch, $this->translationsBatchLines);
            $this->translationsBatch[$language] = [];
            $this->translationsBatchLines[$language] = [];
        }
    }

    /**
     * Must be public to be accessed by the async process
     * @param mixed[][] $translationsBatch
     * @param int[][] $translationsBatchLines
     */
    public function import(
        EximJob $job,
        string $langCode,
        array $translationsBatch,
        array $translationsBatchLines
    ): void {
        $delayExec = $this->dispatcher
            ->delayExec(
                __METHOD__,
                \func_get_args(),
                \Wizacha\Registry::defaultInstance(),
                ['#progress_id' => $job->getId()]
            );

        if (true === $delayExec) {
            return;
        }

        if ($job->getStatus()->equals(JobStatus::CANCELED()) === true) {
            return;
        }

        $jobId = $job->getId();
        try {
            $this->translationService
                ->setTranslations($translationsBatch[$langCode], $langCode, true);

            foreach ($translationsBatchLines[$langCode] as $name => $line) {
                EximJobService::info(
                    'exim_translations_create',
                    $jobId,
                    $line,
                    null,
                    (string) $name
                );
            }
        } catch (LocaleNotFound $e) {
            foreach ($translationsBatchLines[$langCode] as $name => $line) {
                EximJobService::error(
                    'exim_translations_create_failed_local',
                    $jobId,
                    $line,
                    null,
                    $langCode
                );
            }
        } catch (\Exception $e) {
            foreach ($translationsBatchLines[$langCode] as $name => $line) {
                EximJobService::error(
                    'exim_translations_create_failed',
                    $jobId,
                    $line,
                    null,
                    (string) $name
                );
            }
        }
    }
}
