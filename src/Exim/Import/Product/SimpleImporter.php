<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Exim\Import\Product;

use Wizacha\Marketplace\User\User;

//If product already exist and has another declination, add the new declinations without modification on current declinations
class SimpleImporter implements ProductImporter
{
    public function import(array $datas, array $options, string $langCode, array $progressId = [], string $importUniqueId = "", string $jobId, $lines, ?User $user = null)
    {
        \Wizacha\Exim\Product::handleCSVProduct($datas, $options, $langCode, $progressId, \Wizacha\Exim\Product::INVENTORY_KEPT, $importUniqueId, $jobId, $lines, $user);
    }
}
