<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\Import\Product;

use Wizacha\Exim\Exception\DifferentProductCodeNotRegroupable;
use Wizacha\Exim\Import\ImportCsv;
use Wizacha\Exim\Import\Product;

class Csv extends ImportCsv
{
    /** @var Product */
    private $product;

    public function current(): Product
    {
        $this->accumulate();

        return $this->product;
    }

    public function key(): string
    {
        return $this->product->getCode();
    }

    /**
     * Regroupement des lignes à importer pour avoir toutes les déclinaisons d'un produit ensemble
     */
    private function accumulate(): void
    {
        try {
            $this->product = new Product($this->file->current(), $this->getLineNumber());
            $this->file->next();
            $this->lineNumber = $this->lineNumber + 1;

            while ($this->file->valid() && $this->product->getCode() != '') {
                $this->product = $this->product->regroup($this->file->current(), $this->getLineNumber());
                $this->file->next();
                $this->lineNumber = $this->lineNumber + 1;
            }
        } catch (DifferentProductCodeNotRegroupable $e) {
            //stop regrouping as soon as another product is found
        }
    }
}
