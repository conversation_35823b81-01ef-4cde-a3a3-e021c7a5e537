<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Exim\Import\Product;

use Wizacha\Exim\Product;

/**
 * Should be used if $datas contains all declinations of product.
 */
class ImportAndRemoveUnspecifiedDeclinations implements ProductImporter
{
    public function import(array $datas, array $options, string $langCode, array $progressId = [], string $importUniqueId = "", string $jobId, $lines)
    {
        Product::handleCSVProduct($datas, $options, $langCode, $progressId, Product::INVENTORY_REMOVE, $importUniqueId, $jobId, $lines);
    }
}
