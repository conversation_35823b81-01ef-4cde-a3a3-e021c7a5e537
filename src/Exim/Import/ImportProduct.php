<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\Import;

use Wizacha\Exim\Exception\DifferentProductCodeNotRegroupable;
use Wizacha\Exim\Import\Product;
use Wizacha\Exim\Import\Inventory;

abstract class ImportProduct
{
    /** @var string */
    protected $code;

    /** @var array */
    protected $data = [];

    protected int $companyId;

    /**
     * @param array $data
     * @param int $lineNumber
     */
    public function __construct(array $data, int $lineNumber)
    {
        $this->code = $data['product_code'];
        $this->companyId =  \array_key_exists('company_id', $data) === true ? \intval($data['company_id']) : 0;
        $this->merge($data, $lineNumber);
    }

    /** @return string[] */
    public function getLocales(): array
    {
        return \array_keys($this->data);
    }

    /** @return string[] */
    public function getTranslation(string $locale): array
    {
        return $this->data[$locale] ?? [];
    }

    /** @return string[] */
    public function getLines(string $locale): array
    {
        return $this->data[$locale]['lines'] ?? [];
    }

    /** @return string */
    public function getCode(): string
    {
        return $this->code;
    }

    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    /**
     * On vérifie si le tableau $data correspond au produit actuel et on les merge ensemble si c'est le cas
     * @param array $data
     * @param int $lineNumber
     * @return Inventory|Product
     */
    public function regroup(array $data, int $lineNumber): self
    {
        if ($this->code !== $data['product_code']
            || (\array_key_exists('company_id', $data) === true
            && $this->companyId !== (int) $data['company_id'])
        ) {
            throw new DifferentProductCodeNotRegroupable();
        }

        $self = clone $this;
        $self->merge($data, $lineNumber);

        return $self;
    }

 /**
 * Merge de $data avec le produit actuel
 * @param array $data
 * @param int $lineNumber
 */
    abstract protected function merge(array $data, int $lineNumber): void;
}
