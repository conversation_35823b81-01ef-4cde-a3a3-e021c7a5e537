<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\Import;

use Wizacha\Exim\Import\Product;
use Wizacha\Exim\Import\Inventory;

abstract class ImportCsv implements \Iterator
{
    /** @var \Generator */
    protected $file;

    /** @var int $lineNumber */
    protected $lineNumber;

    public function __construct(\Generator $file)
    {
        $this->file = $file;
        $this->lineNumber = 2;//on commence a la ligne 2 qui est la 1ere ligne de données
    }

    public function next(): void
    {
    }

    public function rewind(): void
    {
        $this->file->rewind();
        $this->lineNumber = 2;
    }

    /** @return bool */
    public function valid(): bool
    {
        return $this->file->valid();
    }

    /** @return int */
    public function getLineNumber(): int
    {
        $currentData = $this->file->current();
        if (isset($currentData['line'])) {
            return \intval($currentData['line']);
        }

        return $this->lineNumber;
    }

    /** @return Inventory|Product */
    abstract public function current();

    /** @return string */
    abstract public function key(): string;
}
