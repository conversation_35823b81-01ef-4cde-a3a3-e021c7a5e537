<?php

/**
 *  <AUTHOR> DevTeam <<EMAIL>>
 *  @copyright   Copyright (c) Wizacha
 *  @license     Proprietary
 */

namespace Wizacha\Exim;

use Wizacha\AppBundle\Controller\Api\Division\Dto\ApiDivisionSettingsDto;
use Wizacha\Component\Import\EximJobService;
use Wizacha\Exim\Exception\EximJobException;
use Wizacha\Exim\Import\ImportReportMessage\DivisionMessage;
use Wizacha\Marketplace\Division\Exception\InvalidDivisionCodeException;
use Wizacha\Marketplace\User\User;
use Wizacha\Registry;

class Division
{
    private const INCLUDED_DIVISIONS_LABEL = 'Divisions included';
    private const EXCLUDED_DIVISIONS_LABEL = 'Divisions excluded';
    private const DIVISIONS_SEPARATOR = '|';

    public static function put(array $import_data, ?User $user, ?string $jobId, int $lineNumber)
    {
        try {
            if (\is_null($user)) {
                throw new EximJobException('exim_available_offers_user_not_found', $jobId, $lineNumber);
            }

            $apiDivisionSettingsDto = new ApiDivisionSettingsDto(
                explode(
                    self::DIVISIONS_SEPARATOR,
                    $import_data[0][self::INCLUDED_DIVISIONS_LABEL]
                ),
                explode(
                    self::DIVISIONS_SEPARATOR,
                    $import_data[0][self::EXCLUDED_DIVISIONS_LABEL]
                )
            );
            $divisionSettingsService = container()->get('marketplace.divisions_settings.service');
            $companyId = Registry::defaultInstance()->get(['runtime', 'company_id']);

            if ($user->isMarketplaceAdministrator() && $companyId === 0) {
                // Import divisions as a Marketplace Administrator
                $divisionSettingsService->updateMarketplaceDivisionSettings($apiDivisionSettingsDto);
            } else {
                // Import divisions as a Merchant
                $divisionSettingsService->updateCompanyDivisionSettings(
                    $companyId,
                    $apiDivisionSettingsDto
                );
            }

            // Everything is allright we can mark the job as success
            EximJobService::info(
                DivisionMessage::EXIM_AVAILABLE_OFFERS_DIVISION_IMPORT_SUCCESS,
                $jobId,
                (string) $lineNumber
            );
        } catch (EximJobException | InvalidDivisionCodeException $exception) {
            EximJobService::error(
                $exception->getMessage(),
                $jobId,
                (string) $lineNumber
            );
        }
    }
}
