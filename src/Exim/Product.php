<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Exim;

use Psr\Log\LoggerInterface;
use Tygh\Database;
use Wizacha\Component\Geocoding\Geocoding;
use Wizacha\Component\Import\EximJobService;
use Wizacha\Component\Import\ImportFactory;
use Wizacha\Component\Import\ImportService;
use Wizacha\Component\Import\JobType;
use Wizacha\Component\Import\JobStatus;
use Wizacha\Component\Locale\Locale;
use Wizacha\Cscart\Exim;
use Wizacha\Cscart\NotificationType;
use Wizacha\Exim\Exception\EximJobException;
use Wizacha\ImageManager;
use Wizacha\Exim\Import\ImportReportMessage\ProductMessage;
use Wizacha\Marketplace\Division\Exception\InvalidProductDivisionCodeException;
use Wizacha\Marketplace\Division\Exception\InvalidProductDivisionExcludedCodeException;
use Wizacha\Marketplace\Division\Exception\InvalidProductDivisionIncludeCodeException;
use Wizacha\Marketplace\Division\Service\DivisionSettingsService;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Image\Exception\BadImage;
use Wizacha\Marketplace\Language\LanguageRepository;
use Wizacha\Marketplace\PIM\Attribute\AttributeService;
use Wizacha\Marketplace\PIM\Attribute\AttributeType;
use Wizacha\Marketplace\PIM\Option\OptionService;
use Wizacha\Marketplace\PIM\Product\ProductService;
use Wizacha\Marketplace\PIM\Product\Template\TemplateService;
use Wizacha\Marketplace\PriceTier\PriceTier;
use Wizacha\Marketplace\Traits\CompanyTrait;
use Wizacha\Money\Money;
use Wizacha\Product as WizachaProduct;
use Wizacha\Registry;

/**
 * Class Product: Helpers for import/export csv
 * @package Wizacha\Exim
 */
class Product
{
    use CompanyTrait;

    public const DECLINATION_LIST = 'declinations';
    public const CATEGORY_DELIMITER = ';';
    private const INCLUDED_DIVISIONS_LABEL = 'divisions_included';
    private const EXCLUDED_DIVISIONS_LABEL = 'divisions_excluded';
    private const DIVISIONS_SEPARATOR = '|';

    /**
     * Constant for handleCSVProduct
     */
    public const INVENTORY_REMOVE  = false;
    public const INVENTORY_KEPT    = true;

    /** @var int */
    private static $jobId;
    /** @var int */
    private static $line;
    /** @var ?int */
    private static $productId;
    /** @var bool Determine if EximJob counter has been incremented */
    private static $incremented = false;

    private static string $productCode;
    private static ?int $companyId;
    private static ?string $combinationCode = null;

    /** @var int[] */
    private static array $lines;

    /**
     * Disabled shippings are not displayed.
     * If ship1 is disabled, ship2 as 2€ for first product and 0.5€ for next and ship3 as the default value
     * and the delimiter ///
     * return "ship2: [2///0.5]; ship3: []"
     * @param integer $product_id
     * @param string $delimiter
     * @return string
     */
    public static function getShippings($product_id, $delimiter)
    {
        $auth = [];
        $set_delimiter = '; ';
        $product_data = fn_get_product_data($product_id, $auth);
        $shippings = [];
        if (\is_array($product_data['shippings'])) {
            $shippings = array_filter(
                $product_data['shippings'],
                function ($shipping) {
                    if ($shipping['status'] == 'A') {
                        return true;
                    }
                }
            );
        }

        foreach ($shippings as &$shipping) {
            if ($shipping['specific_rate']) {
                $rates = $shipping['rates'][0]['rate_value']['I'];
                $rates = $rates[0]['value'] . $delimiter . $rates[1]['value'];
            } else {
                $rates = "";
            }
            $shipping = "{$shipping['shipping']}: [$rates]";
        }
        return implode($set_delimiter, $shippings);
    }

    /**
     * If shipping is not present, he is disabled ;
     * If there is no data in field, do nothing;
     * If rate is empty, the default value is set
     * @param integer $product_id
     * @param string $data
     * @param string $delimiter
     * @param integer $company_id
     * @return bool
     */
    public static function setShippings($product_id, $data, $delimiter, $company_id)
    {
        if (empty($data)) {
            return true;
        }

        $_data = self::parseData($data, $delimiter);

        array_walk(
            $_data,
            function (&$_d) {
                $_d['id'] = self::getShippingId($_d['name']);
                if (!empty($_d['rates'])) {
                    $_d['rates'] = fn_w_complexify_rates([
                        0 => [
                            'amount' => 0,
                            'value' => $_d['rates'][0] ?: 0,
                        ],
                        1 => [
                            'amount' => 1,
                            'value' => $_d['rates'][1] ?: 0,
                        ],
                    ]);
                }
            }
        );
        $_data = array_filter(
            $_data,
            function ($_d) {
                if (!empty($_d['id'])) {
                    return true;
                }
            }
        );
        if (empty($_data)) {
            return false;
        }
        $auth = [];
        $lang_code = (string) GlobalState::contentLocale();
        $product_data = db_get_row('SELECT product_id, company_id, is_edp FROM ?:products WHERE product_id = ?i', $product_id);
        fn_w_set_shippings_product_data($product_data);
        $shippings_data = $product_data['shippings'];
        //Vendor has no shippings
        if (\is_null($shippings_data)) {
            return false;
        }

        $disabled_shippings = array_flip(array_keys($shippings_data));
        foreach ($_data as $shipping) {
            unset($disabled_shippings[$shipping['id']]);
            if (empty($shipping['rates'])) {
                fn_w_delete_rates_for_product($product_id, $shipping['id']);
            } else {
                fn_wizacha_shippings_w_update_rate(
                    'w_product_shipping_rates',
                    $shipping['id'],
                    $shipping['rates'],
                    'product_id',
                    $product_id,
                    $company_id
                );
            }
        }
        \Tygh\Database::query(
            "UPDATE ?:products SET w_disable_shippings = ?s WHERE product_id = ?i",
            implode(',', array_flip($disabled_shippings)),
            $product_id
        );
        WizachaProduct::checkStatusAfterChangeShippings($product_id);
    }

    /**
     * @param string $name
     * @return integer|null
     */
    public static function getShippingId($name)
    {
        return \Tygh\Database::getField('SELECT shipping_id FROM ?:shipping_descriptions WHERE shipping = ?s', $name);
    }

    /**
     * Convert CSV field in array
     * @param string $data
     * @param string $rate_delimiter
     * @return array
     */
    public static function parseData($data, $rate_delimiter = ',')
    {
        $set_delimiter = ';';
        $_data = array();
        $o_position = 0;

        /**
         * Expression régulière pour valider la valeur de shipping
         * {nom du shipping}: [] pour un shipping au prix par défaut
         * {nom du shipping}: [{rate_first}///{rate_next}] pour un shipping avec un prix surchargé
         * {nom du shipping}: [{rate}] pour un shipping avec un prix surchargé
         */
        $regexShippingValue = '/\[((\d+([.,]\d+)?\/{3})?\d+([.,]\d+)?)?\]/';

        $shippings = explode($set_delimiter, $data);

        foreach ($shippings as $option) {
            $o_position += 10;

            // Explode only by last delimiter
            $pair = \preg_split('~:(?=[^:]*$)~', $option);

            $isValidShipping = \count($pair) === 2;

            if ($isValidShipping === true) {
                \preg_match($regexShippingValue, $pair[1], $matches);
                $isValidShipping = trim($pair[1]) === $matches[0] ;
            }

            if ($isValidShipping === false) {
                fn_set_notification('E', __('error'), __(ProductMessage::EXIM_UNSUPPORTED_SHIPPING_FORMAT, ['%shipping%' => $option]));
                return [];
            }

            if (\is_array($pair)) {
                array_walk(
                    $pair,
                    function (&$v) {
                        $v = trim($v, ' []');
                    }
                );
                $rates = [];
                if (!empty($pair[1])) {
                    $rates = explode($rate_delimiter, $pair[1]);
                }

                $_data[] = [
                    'name' => $pair[0],
                    'rates' => $rates
                ];
            }
        }
        return $_data;
    }

    public static function getSpecificFields()
    {
        return [
            'Combination Code' => ['condition' => ['Combination Code'], 'export_fields' => ['Combination']],
            'Remove Combination' => [
                'missing_condition' => ['Combination', 'Combination Code'],
                'remove_pattern' => [
                    'references' => ['product_options_inventory'],
                    'export_fields' => ['Combination', 'Combination Code'],
                ]
            ],
            'Combination Price' => [
                'condition' => ['Combination', 'Price'],
                'export_fields' => ['Combination Price'],
                'pattern' => [
                    'export_fields' => [
                        'Combination Price' => [
                            'table' => 'product_options_inventory',
                            'db_field' => 'w_price',
                        ]
                    ]
                ]
            ],
            'Combination quantity' => [
                'condition' => ['Combination', 'Quantity'],
                'export_fields' => ['Combination Quantity'],
                'pattern' => [
                    'export_fields' => [
                        'Combination Quantity' => [
                            'table' => 'product_options_inventory',
                            'db_field' => 'amount',
                        ]
                    ]
                ]
            ],
            'Combination Affiliate link' => [
                'condition' => ['Combination', 'Affiliate link'],
                'export_fields' => ['Combination Affiliate link'],
                'pattern' => [
                    'export_fields' => [
                        'Combination Affiliate link' => [
                            'table' => 'product_options_inventory',
                            'db_field' => 'affiliate_link',
                        ]
                    ]
                ]
            ],
        ];
    }

    /**
     * Check each element of $conditions. If the $elemcondition[condition] passed : modify $pattern and $field like
     * $elem_condition['pattern'] and $elem_condition[export_fields] indicates.
     * See self::getSpecificFields() for an exemple of $conditions.
     * @param array $conditions
     * @param array $pattern
     * @param array $fields
     */
    protected static function changePatternAndFieldsByConditions($conditions, &$pattern, &$fields)
    {
        foreach ($conditions as $condition) {
            $present = (!isset($condition['condition']) ||
                empty(array_diff($condition['condition'], $fields)));
            $missing = (!isset($condition['missing_condition']) ||
                (0 == \count(array_intersect($fields, $condition['missing_condition']))));

            if ($present && $missing) {
                if (isset($condition['pattern'])) {
                    $pattern = array_merge_recursive($pattern, $condition['pattern']);
                }
                if (isset($condition['export_fields'])) {
                    $fields = array_unique(array_merge($fields, $condition['export_fields']));
                }
                if (isset($condition['remove_pattern'])) {
                    foreach ($condition['remove_pattern'] as $key => $sub_keys) {
                        $pattern[$key] = array_diff_key($pattern[$key], array_flip($sub_keys));
                    }
                }
            }
        }
    }

    /**
     * Add Combination price and quantity in pattern if combination ask, removed combination if not necessary
     * @param array $pattern
     */
    public static function preModerateExport(&$pattern, &$export_fields)
    {
        unset($pattern['export_fields']['Combination']['linked']);
        $pattern['export_fields']['Combination Code'] = [
            'table' => 'product_options_inventory',
            'db_field' => 'product_code',
            'multilang' => true,
        ];
        $pattern['export_fields']['Combination Supplier Ref'] = [
            'table' => 'product_options_inventory',
            'db_field' => 'supplier_reference',
            'multilang' => true,
        ];
        self::changePatternAndFieldsByConditions(self::getSpecificFields(), $pattern, $export_fields);
    }

    /**
     * Return product price or combination price.
     * @param array $row
     * @param string $decimals_separator
     * @return string
     */
    public static function getPrice($row, $decimals_separator)
    {
        if (!empty($row['Combination'])) {
            return str_replace('.', $decimals_separator, $row['Combination Price']);
        }
        return str_replace('.', $decimals_separator, $row['Price']);
    }

    /**
     * Return product price or combination price.
     * @param array $row
     * @return string
     */
    public static function getAffiliateLink($row)
    {
        if (!empty($row['Combination'])) {
            return $row['Combination Affiliate link'];
        }
        return $row['Affiliate link'];
    }

    /**
     * Return combination or product quantity
     * @param array $row
     * @return string
     */
    public static function getQuantity($row)
    {
        if (!empty($row['Combination'])) {
            return $row['Combination Quantity'];
        }
        return $row['Quantity'];
    }

    public static function getIncludedDivisions(int $productId): string
    {
        /** @var DivisionSettingsService $divisionsSettingsService */
        $divisionsSettingsService = container()->get('marketplace.divisions_settings.service');

        return implode(
            static::DIVISIONS_SEPARATOR,
            $divisionsSettingsService
                ->getProductDivisionSettings($productId)
                ->getIncludedDivisions()
                ->toArray()
        );
    }

    public static function getExcludedDivisions(int $productId): string
    {
        /** @var DivisionSettingsService $divisionsSettingsService */
        $divisionsSettingsService = container()->get('marketplace.divisions_settings.service');

        return implode(
            static::DIVISIONS_SEPARATOR,
            $divisionsSettingsService
                ->getProductDivisionSettings($productId)
                ->getExcludedDivisions()
                ->toArray()
        );
    }

    public static function exportResultPost(&$export_fields)
    {
        $to_remove = ['Combination Price', 'Combination Quantity', 'Combination Affiliate link'];
        $export_fields = array_diff($export_fields, $to_remove);
    }

    /**
     * Copy/paste fn_exim_product_features with simplification of the output
     *
     * @param int $product_id
     * @param string $features_delimiter
     * @param string|null $lang_code
     * @return string
     */
    public static function getFeatures($product_id, $features_delimiter, $lang_code = null)
    {
        $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();

        if (!$product_id) {
            return '';
        }
        static $features;

        if (!isset($features[$lang_code])) {
            list($features[$lang_code]) = fn_get_product_features(array('plain' => true), 0, $lang_code);
        }

        $main_category = fn_w_get_product_main_category($product_id);

        $product = array(
            'product_id' => $product_id,
            'main_category' => $main_category
        );

        $product_features = fn_get_product_features_list($product, 'A', $lang_code);

        $pair_delimiter = AttributeService::DELIMITER_PAIR;
        //Added space for visibility
        $set_delimiter = AttributeService::DELIMITER_SET . ' ';

        $result = array();


        foreach ($product_features as $f) {
            $f['value_int'] = (empty($f['value_int'])) ? 0 : \floatval($f['value_int']);
            //We transform timestamps into readable dates
            if ($f['feature_type'] == 'D' && !empty($f['value_int'])) {
                $result[] = "{$f['description']}{$pair_delimiter} " . date('Y-m-d', $f['value_int']);
            } elseif (!empty($f['value']) || !empty($f['value_int'])) {
                $result[] = "{$f['description']}{$pair_delimiter} " . (!empty($f['value']) ? $f['value'] : $f['value_int']);
            } else {
                $_params = array(
                    'feature_id' => $f['feature_id'],
                    'product_id' => $product_id,
                    'feature_type' => $f['feature_type'],
                    'selected_only' => true
                );

                list($variants) = fn_get_product_feature_variants($_params, 0, $lang_code);

                if ($variants) {
                    $values = array_column($variants, 'variant');
                    $result[] = "{$f['description']}{$pair_delimiter} " . implode($features_delimiter, $values);
                }
            }
        }
        $p = new WizachaProduct($product_id);
        foreach ($p->getFreeFeatures(new Locale($lang_code)) as $free_feature => $values) {
            $result[] = "{$free_feature}{$pair_delimiter} " . implode($features_delimiter, $values);
        }

        return !empty($result) ? implode($set_delimiter, $result) : '';
    }

    /**
     * Replaces fn_exim_parse_data but group_name is auto-generated
     *
     * @param int $product_id
     * @param array $data
     * @param string $variants_delimiter
     * @param string $lang_code
     * @param int|null $product_category_id Force parse attribut for a given category (ex: used for mvp categories)
     * @return array
     */
    public static function parseFeatures($product_id, $data, $variants_delimiter, $lang_code, int $product_category_id = null)
    {
        $pair_delimiter = AttributeService::DELIMITER_PAIR;
        $set_delimiter = AttributeService::DELIMITER_SET;
        $_data = [];
        $o_position = 0;
        if (empty($data)) {
            return $_data;
        }

        $sql = <<<'SQL'
SELECT
  pf.feature_id,
  pf.parent_id,
  pf.feature_type,
  ?:product_features_descriptions.description
FROM
  ?:product_features AS pf
  LEFT JOIN ?:product_features_descriptions ON ?:product_features_descriptions.feature_id = pf.feature_id
  AND ?:product_features_descriptions.lang_code = ?s
  LEFT JOIN ?:product_features AS groups ON pf.parent_id = groups.feature_id
WHERE
  pf.status IN ('A', 'H')
  AND (
    pf.categories_path = ''
    OR %s
  )
SQL;

        if (\is_null($product_category_id)) {
            $categoryPath = explode('/', db_get_field("SELECT c.id_path FROM ?:categories AS c LEFT JOIN ?:products_categories AS pc ON c.category_id = pc.category_id WHERE pc.product_id = ?i", $product_id));
        } else {
            $categoryPath = explode('/', db_get_field("SELECT c.id_path FROM ?:categories AS c WHERE c.category_id = ?i", $product_category_id));
        }

        $categories = implode(' OR ', array_map(function ($categoryId) {
            return sprintf('FIND_IN_SET(%d, pf.categories_path)', $categoryId);
        }, $categoryPath));

        $sql = sprintf($sql, $categories);
        $productFeatures = db_get_hash_array($sql, 'feature_id', $lang_code);
        $features = [];

        foreach ($productFeatures as $feature) {
            if ($feature['feature_type'] == 'G') {
                continue;
            }

            if ($feature['parent_id'] != 0) {
                $features[strtolower(trim($feature['description']))] = [
                    'group_name' => $productFeatures[$feature['parent_id']]['description'],
                    'type' => $feature['feature_type'],
                    'name' => $feature['description'],
                    'id'   => $feature['feature_id'],
                ];
            } else {
                $features[strtolower(trim($feature['description']))] = [
                    'type' => $feature['feature_type'],
                    'name' => $feature['description'],
                    'id'   => $feature['feature_id'],
                ];
            }
        }

        $options = array_filter(explode($set_delimiter, $data));

        foreach ($options as $option) {
            $o_position += 10;
            // == : if strpos return 0, name is empty
            if (($pos = strpos($option, $pair_delimiter)) == false) {
                continue;
            }

            $name = trim(substr($option, 0, $pos));
            $normalized_name = strtolower($name);
            $variants = substr($option, $pos + 1);

            if (empty(trim($variants))) {
                continue;
            }

            $variants = explode($variants_delimiter, $variants);

            if (\array_key_exists($normalized_name, $features)) {
                //We only do this conversion if it's a Date field
                if ($features[$normalized_name]['type'] == 'D') {
                    $parsedTime = \DateTime::createFromFormat('Y#m#d', trim($variants[0]));
                    $variants[0] = $parsedTime ? $parsedTime->format('U') : null;
                }
                $_data[$o_position] = $features[$normalized_name];
            } else {
                //Create a new feature
                $_data[$o_position]['name'] = $name;
                $_data[$o_position]['type'] = AttributeType::CHECKBOX_MULTIPLE;
            }

            $position = 0;

            foreach ($variants as $variant) {
                $position += 10;
                $_data[$o_position]['variants'][$position] = trim($variant);
            }
        }

        $variants = [];
        $free_features = [];

        foreach ($_data as $feature) {
            if (!\array_key_exists('id', $feature)) {
                $free_features[$feature['name']] = array_values($feature['variants']);
                continue;
            }
            $variants = Exim::exim_product_feature_variants($feature, $feature['id'], $variants, $lang_code, $categoryPath);
            if (!isset($variants[$feature['id']])) {
                $free_features[$feature['name']] = array_values($feature['variants']);
            }
        }

        return ['variants' => $variants, 'free_features' => $free_features];
    }

    /**
     * Replaces fn_exim_set_product_features but simplified input taken
     * @param int $product_id
     * @param array $data
     * @param string $features_delimiter
     * @param string $lang_code
     * @param bool|null $is_brand
     * @return bool
     */
    public static function setFeatures($product_id, $data, $features_delimiter, $lang_code, $is_brand = null)
    {
        if (!fn_is_empty($data)) {
            // Parse csv data to an array
            $data = Product::parseFeatures($product_id, $data, $features_delimiter, $lang_code);

            // We merge data with empty variant values to erase old values
            $emptyVariants = self::getEmptyFeaturesVariants($product_id, $is_brand);

            // Update features values
            fn_update_product_features_value($product_id, $data['variants'] + $emptyVariants, array(), $lang_code);

            $p = new WizachaProduct($product_id);
            $p->setFreeFeatures($data['free_features'], new Locale($lang_code));
        }

        return true;
    }

    /**
     * Get an empty variants array. Can be use to reset a product's features
     *
     * @param int $product_id
     * @param bool|null $is_brand
     * @return array
     */
    public static function getEmptyFeaturesVariants($product_id, $is_brand = null)
    {
        $variants = array();
        $conditions = '';
        if (isset($is_brand)) {
            $conditions = $is_brand ? "=" : "!=";
            $conditions = db_quote(" AND feature_id $conditions ?i", fn_w_get_brand_id());
        }

        $products_features = db_get_array("SELECT feature_id, variant_id FROM ?:product_features_values WHERE product_id = ?s " . $conditions, $product_id);
        //Remove old features or brand
        foreach ($products_features as $key => $variant) {
            $variants[$variant['feature_id']] = '';
        }

        return $variants;
    }

    /**
     * @param int $product_id
     * @param string $data images URL separated with $images_delimiter
     * @param string $images_delimiter
     * @param \Wizacha\ImageManager|null $images_manager
     * @param string $jobId
     * @param string $lines
     * @param string|null $dataAltText
     * @param string|null $langCode
     * @throws \Exception
     */
    public static function putImages(
        int $product_id,
        string $data,
        string $images_delimiter,
        ImageManager $images_manager = null,
        string $jobId = null,
        string $lines = null,
        string $dataAltText = null,
        string $langCode = null
    ) {
        if (ImageManager::BYPASS_URL === $data) {
            return;
        }

        $start = microtime(true);

        if (\is_null($images_manager)) {
            $images_manager = new \Wizacha\ImageManager(container()->get('Wizacha\Storage\ImagesStorageService'));
        }
        $images = explode($images_delimiter, $data);
        $images = array_filter(
            $images,
            function ($image_url) use ($product_id, $jobId, $lines): bool {
                $valid = \in_array(fn_strtolower(fn_get_file_ext(parse_url($image_url, PHP_URL_PATH))), ['', 'jpg', 'png', 'gif', 'jpeg', 'ico']);
                if (!$valid) {
                    EximJobService::warning(ProductMessage::EXIM_UNSUPPORTED_IMAGE_EXTENSION, $jobId, $lines, $product_id, $image_url);
                    fn_set_notification('E', __(ProductMessage::EXIM_UNSUPPORTED_IMAGE_EXTENSION), $image_url);
                }
                return $valid;
            }
        );
        $images_id = array_map(function ($url) use ($images_manager) {
            try {
                return $images_manager->createByUrl($url, false);
            } catch (BadImage $e) {
                return null;
            }
        }, $images);
        $images_id = array_filter(
            $images_id,
            function ($id, $index) use ($images, $product_id, $jobId, $lines): bool {
                if ($id === null
                    && \array_key_exists($index, $images) === true
                    && \strlen($images[$index]) > 0
                ) {
                    EximJobService::warning(ProductMessage::EXIM_FAILED_TO_PROCESS_IMAGE, $jobId, $lines, $product_id, $images[$index]);
                    fn_set_notification('E', __(ProductMessage::EXIM_FAILED_TO_PROCESS_IMAGE), $images[$index]);
                }
                return (bool) $id;
            },
            ARRAY_FILTER_USE_BOTH
        );
        $images_manager->createPairs($images_id, 'product', $product_id);

        $elapsed = microtime(true) - $start;

        /** @var LoggerInterface */
        $logger = container()->get('logger');
        $logger->notice(
            sprintf(
                "%s::%s stats",
                __NAMESPACE__,
                __FUNCTION__
            ),
            [
                "duration" => $elapsed,
                "jobId" => $jobId,
                "urls" => $images,
            ]
        );
        // save Alt Text
        if ($dataAltText !== null && \count($images_id) > 0) {
            $parsedAltText = explode($images_delimiter, $dataAltText);
            if (\count($parsedAltText) > \count($images_id)) {
                EximJobService::warning('exim_failed_to_add_extra_alt_text', $jobId, $lines, $product_id);
                fn_set_notification('W', __('warning'), __('exim_failed_to_add_extra_alt_text'));
            }
            if (\is_array($parsedAltText) === true && \count($parsedAltText) > 0) {
                foreach ($parsedAltText as $key => $newAltText) {
                    if (\array_key_exists($key, $images_id) === true) {
                        $images_manager->setImageAltText($images_id[$key], $newAltText, $langCode);
                    }
                }
            }
        }
    }

    /**
     * This function is an overlay to HandleCSVProduct, to allow both synchronous call with counter,
     * and asynchronous without references
     *
     * @param int $product_id
     * @param array $datas CSV line content
     * @param array $options CSV options (delimiters)
     * @param string $lang_code
     * @param array $counter
     * @param array $progress_id
     * @param string $jobId id du job d'import
     * @param array $lines
     * @return void
     * @throws \Wizacha\Core\Concurrent\MutexException
     * @throws \Wizacha\Marketplace\Exception\NotFound
     */
    public static function handleCSVLineWithCounter($product_id, $datas, $options, $lang_code, &$counter, array $progress_id = [], string $jobId, array $lines = [])
    {
        if (empty($product_id)) {
            $counter['N'] += 1;
        } else {
            $counter['E'] += 1;
        }
        //product id is not given, it needs to be refreshed for each line to avoid key duplications
        self::handleCSVProduct(static::mergedCSVLineWithDeclination($datas), $options, $lang_code, $progress_id, self::INVENTORY_KEPT, "", $jobId, $lines);
    }

    /**
     * @param array       $datas            ['csv_field'=> string value,..., Product::DECLINATION_LIST => [['csv_field' =>string value],..]]
     * @param array       $options          CSV options (delimiters)
     * @param string      $lang_code
     * @param array       $progress_id
     * @param bool        $kept_existent_inventory
     * @param string      $import_unique_id for metrics purposes
     * @param string|null $jobId            id of import job
     * @param array  $lines
     *
     * @return bool
     * @throws \Wizacha\Core\Concurrent\MutexException
     * @throws \Wizacha\Marketplace\Exception\NotFound
     * @deprecated
     * @see \Wizacha\Exim\Import\Product\Importer
     */
    public static function handleCSVProduct($datas, $options, $lang_code, array $progress_id = [], $kept_existent_inventory = self::INVENTORY_KEPT, $import_unique_id = "", string $jobId = null, array $lines = [])
    {
        static::$jobId = $jobId;
        static::$lines = $lines;
        static::$line = current($lines);
        static::$incremented = false;
        static::$productCode = $datas['product_code'];

        $productsInventories = [];
        $updated = $priceTierRepository = false;
        $groupLines = implode(',', $lines);

        $container = container();
        $delayExec = $container
            ->get('marketplace.async_dispatcher')
            ->delayExec(
                __METHOD__,
                \func_get_args(),
                \Wizacha\Registry::defaultInstance(),
                $progress_id
            )
        ;
        if (true === $delayExec) {
            return true;
        }

        // If job not exist then stop the process
        $jobService = $container->get('marketplace.import.job_service');
        try {
            $job = $jobService->get($jobId);
        } catch (NotFound $exception) {
            return true;
        }

        if ($job->getStatus()->equals(JobStatus::CANCELED()) === true) {
            return true;
        }

        $start =  microtime(true);

        fn_get_schema('exim', 'products');

        array_walk_recursive(
            $datas,
            function (&$value, $key) {
                // Not an HTML field, is allowed to contain < or >
                if ($key == 'Features') {
                    return;
                }

                if ($key == 'Category') {
                    return;
                }

                if ($key === 'full_description') {
                    return;
                }

                if ($key === 'short_description') {
                    return;
                }

                \Wizacha\Misc::processSubmittedContent($value, $key);
            }
        );

        $columns = [
            'product_code',
            'price',
            'weight',
            'amount',
            'is_edp',
            'product',
            'full_description',
            'short_description',
            'status',
            'w_supplier_ref',
            'w_green_tax',
            'w_condition',
            'tracking',
            'zero_price_action',
            'company_id',
            'lang_code',
            'discussion_type',
            'crossed_out_price',
            'affiliate_link',
            'category id',
            'product_template_type',
            'infinite_stock',
            'max_price_adjustment',
            'price_tiers',
            'is_subscription',
            'is_renewable',
            'supplier_reference'
        ];

        // check company id
        $companyId = self::checkCompanyId(Registry::defaultInstance()->get(['runtime', 'company_id']), $datas);

        static::$productId = (int) WizachaProduct::getIdByProductCode($datas['product_code'], $companyId) ?: 0;
        static::$companyId = $companyId;
        static::$combinationCode = \array_key_exists('declinations', $datas) === true
            && \count($datas['declinations']) > 0
            && \array_key_exists('Combination Code', $datas['declinations'][0]) === true
                ? $datas['declinations'][0]['Combination Code']
                : null;

        // If it is done by an admin, import SEO columns
        if (Registry::defaultInstance()->get(['runtime', 'company_id']) === 0) {
            \array_push($columns, 'seo_name', 'page_title', 'meta_description', 'meta_keywords');
        }

        //retrieve correctly formatted datas
        $product_data = \array_intersect_key($datas, \array_flip($columns));

        $lang_code = $lang_code ?? ((string) GlobalState::contentLocale());

        //check if language is valid
        if ($container->get(LanguageRepository::class)->findByLangCode($lang_code) === null) {
            static::exception(__('lang_code_not_valid'));
        }

        if (static::areDeclinationsValid($datas['declinations']) === false) {
            static::error(__('w_csv_error_combination_consistency'));
            return false;
        };

        $error = null;
        $context = \json_encode(
            [
                'productId' => static::$productId,
                'productCode' => $product_data['product_code'],
                'companyId' => $companyId,
            ]
        );

        try {
            $oldTemplate = null;
            if (static::$productId > 0) {
                $oldTemplate = static::getTemplateService()->getTemplate(
                    static::getProductService()->get(static::$productId)->getProductTemplateType()
                );
            }

            $product_data = static::setOldOrDefaultTemplate($product_data, static::$productId);

            if (isset($product_data['product_template_type'])) {
                $template = static::getTemplateService()->getTemplate($product_data['product_template_type'] ?? null);
            } else {
                $template = null;
            }

            if (\is_null($template)) {
                static::exception(ProductMessage::EXIM_PRODUCT_NOT_MATCH_TEMPLATE, $product_data['product_template_type']);
            }

            if (static::getTemplateService()->validateHiddenFields($template, $product_data) === false) {
                static::exception(ProductMessage::EXIM_FIELD_VALUE_NOT_MATCH_TEMPLATE);
            }

            if (static::$productId > 0 && $template !== $oldTemplate && \is_null($oldTemplate) === false) {
                $product_data = array_merge(
                    $product_data,
                    static::getTemplateService()->resetProductOldTemplateHiddenFields(
                        $oldTemplate,
                        $product_data
                    )
                );
            }

            $productTemplate = static::getTemplateService()->getTemplate($product_data['product_template_type']);

            foreach ($productTemplate->getHiddenFields() as $importField) {
                $product_data[$importField->getName()] = $importField->getValue();
            }

            //Set approved status
            if (!static::$productId) {
                $product_data['approved'] = (container()->getParameter('feature.config_enable_auto_validate_product') === true) ? 'Y' : 'P';
            }

            if (Registry::defaultInstance()->get(['runtime', 'company_id']) === 0) {
                $product_data['approved'] = 'Y';
            }

            //prepare main category data
            if (!empty($datas['Category']) && !empty($datas['category id'])) {
                static::exception(ProductMessage::EXIM_MISSING_REQUIRED_FIELDS, 'Category, category_id');
            }

            if (\array_key_exists('Category', $datas) === true) {
                $categories = explode(static::CATEGORY_DELIMITER, $datas['Category']);
                $garbageId = \Wizacha\Category::getGarbageCategoryId();
                foreach ($categories as $category) {
                    $categoryId = Category::getId(
                        $category,
                        $options['category_delimiter'],
                        $datas['company_id'],
                        $lang_code,
                        true
                    );

                    if ($categoryId !== $garbageId && (new \Wizacha\Category($categoryId))->isLeaf() === true) {
                        if (container()->get(ProductService::class)->updateProductCategoryHasImpactToDeclinations(static::$productId, $categoryId) === true
                            && container()->get(OptionService::class)->isCombinationCompatibleWithCategory($datas[Product::DECLINATION_LIST], $categoryId) === false
                        ) {
                            static::warning('exim_warning_updating_category', $product_data['product_code']);
                        } else {
                            $product_data['category_ids'][] = $categoryId;
                        }
                    } else {
                        $product_data['category_ids'][] = $garbageId;
                        static::warning(ProductMessage::EXIM_PRODUCT_NOT_MATCH_CATEGORY, $product_data['product_code']);
                    }
                }
            } elseif ($datas['category id']) {
                $category = new \Wizacha\Category($datas['category id']);
                if ($category->exists() && $category->isLeaf()) {
                    $product_data['category_ids'] = [$category->getId()];
                } else {
                    $product_data['category_ids'] = [\Wizacha\Category::getGarbageCategoryId()];
                    static::warning(ProductMessage::EXIM_PRODUCT_NOT_MATCH_CATEGORY, $product_data['product_code']);
                }
            } elseif (0 == static::$productId) {
                $product_data['category_ids'][] = \Wizacha\Category::getGarbageCategoryId();
                static::warning(ProductMessage::EXIM_PRODUCT_NOT_MATCH_CATEGORY, $product_data['product_code']);
            }

            //prepare tax data
            if (isset($datas['tax_ids'])) {
                foreach (array_map('trim', explode(',', $datas['tax_ids'])) as $tax) {
                    $product_data['tax_ids'][] = \Wizacha\Tax::getIdFromTax($tax, $lang_code);
                }
            }

            if (isset($datas['avail_since'])) {
                try {
                    $product_data['avail_since'] = (new \DateTime($datas['avail_since']))
                        ->setTime(0, 0, 0)
                        ->getTimestamp()
                    ;
                } catch (\Exception $e) {
                    unset($product_data['avail_since']);
                }
            }

            if ($product_data['infinite_stock'] === 1) {
                $datas['infinite_stock'] = 'Y';
            }

            if (isset($datas['infinite_stock'])) {
                $product_data['infinite_stock'] = strcmp($datas['infinite_stock'], 'Y') === 0;
            }

            if ($container->getParameter('feature.subscription') === true) {
                if ($product_data['is_subscription'] === 1) {
                    $datas['is_subscription'] = 'Y';
                }

                if (\array_key_exists('is_subscription', $datas)) {
                    $product_data['is_subscription'] = $datas['is_subscription'] === "Y";
                }

                if ($product_data['is_renewable'] === 1) {
                    $datas['is_renewable'] = 'Y';
                }

                if (\array_key_exists('is_renewable', $datas)) {
                    $product_data['is_renewable'] = $datas['is_renewable'] === "Y";
                }
            }

            if (static::$productId > 0) {
                $updated = true;
            }

            if (\array_key_exists('max_price_adjustment', $product_data)) {
                $maxPriceAdjustment = $product_data['max_price_adjustment'];

                if (is_numeric($maxPriceAdjustment) && $maxPriceAdjustment >= 0 && $maxPriceAdjustment <= 100) {
                    // from a CSV it may be a string
                    $maxPriceAdjustment = (int) $maxPriceAdjustment;
                } elseif ($maxPriceAdjustment === '') {
                    $maxPriceAdjustment = null;
                } else {
                    $maxPriceAdjustment = null;
                    static::warning(ProductMessage::EXIM_PRODUCT_INVALID_MAX_PRICE_ADJUSTMENT);
                }

                $product_data['max_price_adjustment'] = $maxPriceAdjustment;
            }

            // Set product divisions if $product has an ID (required for divisionSettingsService cascade update)
            $issetIncludedDivisions = \array_key_exists(self::INCLUDED_DIVISIONS_LABEL, $datas);
            $issetExcludedDivisions = \array_key_exists(self::EXCLUDED_DIVISIONS_LABEL, $datas);
            $issetDivisions = $issetIncludedDivisions || $issetExcludedDivisions;

            if (\is_int(static::$productId)
                && $issetDivisions
                && container()->getParameter('feature.available_offers')
            ) {
                // Both excluded and included divisions shall be defined
                if (false === $issetExcludedDivisions || false === $issetIncludedDivisions) {
                    static::exception(ProductMessage::EXIM_MISSING_DIVISIONS);
                }

                //set available offers (Divisions)
                $includedDivisionsRawData = \trim($datas[self::INCLUDED_DIVISIONS_LABEL]);
                $excludedDivisionsRawData = \trim($datas[self::EXCLUDED_DIVISIONS_LABEL]);

                if ("" === $includedDivisionsRawData) {
                    static::warning(ProductMessage::EXIM_EMPTY_INCLUDED_DIVISIONS);
                }

                $includedDivisions = \explode(self::DIVISIONS_SEPARATOR, $includedDivisionsRawData) ?? [];

                if (\count($includedDivisions) < 1) {
                    static::warning(ProductMessage::EXIM_INVALID_INCLUDED_DIVISIONS);

                    $includedDivisions = $container
                        ->get('marketplace.division.division_repository')
                        ->getDivisionsCodeFromProductId(static::$productId)
                    ;
                }

                if ("" === $includedDivisionsRawData) {
                    static::warning(ProductMessage::EXIM_EMPTY_INCLUDED_DIVISIONS);
                }

                $excludedDivisions = \explode(self::DIVISIONS_SEPARATOR, $excludedDivisionsRawData) ?? [];

                if (\count($excludedDivisions) < 1) {
                    static::warning(ProductMessage::EXIM_INVALID_EXCLUDED_DIVISIONS);
                }

                $product_data['divisions'] = [
                    'included' => $includedDivisions,
                    'excluded' => $excludedDivisions,
                ];
            }

            // Insert or update product in database
            static::$productId = fn_update_product($product_data, static::$productId, $lang_code, false);

            static::logCscartNotifications();

            if (false === static::$productId) {
                static::exception(ProductMessage::EXIM_PRODUCT_CREATE_FAILED, $product_data['product_code']);
            } else {
                static::info(
                    $updated
                    ? ProductMessage::EXIM_PRODUCT_UPDATE
                    : ProductMessage::EXIM_PRODUCT_CREATE
                );
            }

            //update datas
            $datas['product_id'] = static::$productId;

            //set combinations
            if (!$kept_existent_inventory) {
                self::removeInventory(static::$productId);
            }

            if ($datas[Product::DECLINATION_LIST]) {
                $fakeCounter = ['E' => 0, 'N' => 0, 'S' => 0];
                foreach ($datas[Product::DECLINATION_LIST] as $key => $declination_data) {
                    // we need to update this static to keep track of the right csv line number
                    static::$line = $lines[$key];
                    static::$combinationCode
                        = true === \array_key_exists('Combination Code', $declination_data)
                        ? $declination_data['Combination Code']
                        : null;

                    $combination = \Wizacha\Cscart\Exim::exim_put_product_combination(
                        static::$productId,
                        $datas['product_code'],
                        isset($declination_data['Combination Code'])
                            ? $declination_data['Combination Code'] : null,
                        [
                            $lang_code => $declination_data['combination']
                            //need combination separately for each language
                        ],
                        isset($declination_data['amount']) ? $declination_data['amount'] : null,
                        $fakeCounter,
                        isset($declination_data['price']) ? $declination_data['price'] : null,
                        $declination_data['crossed_out_price'] ?? null,
                        $declination_data['affiliate_link'] ?? null,
                        $jobId,
                        $lines[$key],
                        $declination_data['Combination image URL'] ?? null,
                        $options['w_images_delimiter'] ?? null,
                        (true === \array_key_exists('Combination Supplier Ref', $declination_data)
                            && '' !== $declination_data['Combination Supplier Ref']
                        ) ? $declination_data['Combination Supplier Ref'] : null,
                        false,
                        $declination_data['Combination Alt Text'] ?? null,
                        $lang_code,
                        $declination_data['infinite_stock'] ?? null
                    );

                    if ($key > 0) {
                        static::info(ProductMessage::EXIM_PRODUCT_DECLINATION_CREATE, $product_data['product_code'], true);
                    }

                    // If we don't ask for combination or we have a combination found.
                    if (($combination === null && $declination_data['combination'] === "") || $combination !== null) {
                        Exim::eximUpdatePriceTiers(
                            $combination,
                            $container->getParameter('feature.tier_pricing'),
                            $declination_data,
                            $datas,
                            false,
                            static::$productId,
                            $productsInventories,
                            $jobId,
                            $lines[$key]
                        );

                        // Clean all datas for next loop clean
                        unset(
                            $productsInventories[$combination],
                            $priceTiers,
                            $declination_data,
                            $savePriceTiersErrors
                        );
                    }
                }
            }

            //set shippings
            if (isset($datas['Shippings'])) {
                $hasSetShipping = static::setShippings(
                    static::$productId,
                    $datas['Shippings'],
                    $options['shipping_price_delimiter'],
                    $datas['company_id']
                );

                if ($hasSetShipping === false) {
                    static::warning(ProductMessage::EXIM_PRODUCT_UNABLE_RETRIEVE_SHIPPING);
                }
            }

            //set images
            if (isset($datas['Image URL'])) {
                static::putImages(
                    static::$productId,
                    $datas['Image URL'],
                    $options['w_images_delimiter'],
                    null,
                    $jobId,
                    current($lines),
                    $datas['Alt Text'],
                    $lang_code
                );
            }

            //set brand
            if (isset($datas['Brand'])) {
                fn_w_exim_put_brand(static::$productId, $datas['Brand'], $options['features_delimiter'], $lang_code);
            }

            //set features
            if (isset($datas['Features'])) {
                static::setFeatures(static::$productId, $datas['Features'], $options['features_delimiter'], $lang_code, false);
            }


            //create options links
            fn_w_exim_create_product_exceptions([$datas]);

            static::logCscartNotifications();

            // Create a single priceTier with lowerLimit & price=0 for an automatically created option inventory
            $allProductCombinations = $container
                ->get('marketplace.price_tier.product_option_inventory_repository')
                ->findByProductId(static::$productId)
            ;

            foreach ($allProductCombinations as $singleProductCombination) {
                if (\count($singleProductCombination->getPriceTiers()->getValues()) === 0) {
                    $priceTier = (new PriceTier(static::getProductService()->get(static::$productId)))
                        ->setProductOptionInventory($singleProductCombination)
                        ->setLowerLimitAndPrice(Money::fromVariable(0.00), 0)
                    ;

                    $container->get('marketplace.price_tier.price_tier_repository')->save($priceTier);
                };
            }

            //set discussion type
            fn_update_discussion(
                [
                    'object_type' => 'P',
                    'object_id' => static::$productId,
                    'type' => 'B',
                ]
            );

            //set geocoding metadata.
            //Si le code postal existe on fait appel au service geocoding de algolia avec le code postal et la ville si elle existe
            if (\array_key_exists('Code postal annonce', $datas) === true && \strlen($datas['Code postal annonce']) > 0) {
                $ville = '';
                if (isset($datas['Ville annonce'])) {
                    $ville = $datas['Ville annonce'];
                }

                $geoloc = container()->get(Geocoding::class)->getGeocoding($datas['Code postal annonce'], $ville);

                if (\is_array($geoloc)) {
                    (new WizachaProduct(static::$productId))->setGeoloc(
                        $geoloc['lat'],
                        $geoloc['lng'],
                        $geoloc['label'],
                        $geoloc['postal']
                    );
                }
                //Si la ville d'annonce existe et on n'a pas de code postal, on fait appel au service geocoding de algolia avec un code postal vide
            } elseif (\array_key_exists('Ville annonce', $datas) === true  && \strlen($datas['Ville annonce']) > 0) {
                $geoloc = container()->get(Geocoding::class)->getGeocoding('', $datas['Ville annonce']);

                if (\is_array($geoloc)) {
                    (new WizachaProduct(static::$productId))->setGeoloc(
                        $geoloc['lat'],
                        $geoloc['lng'],
                        $geoloc['label'],
                        $geoloc['postal']
                    );
                }
                //Si on a seulement la latitude et longitude on ne fait appel au service algolia et on met directement les valeurs
            } elseif (\array_key_exists('Latitude', $datas) === true && \array_key_exists('Longitude', $datas) === true && \strlen($datas['Latitude']) > 0 && \strlen($datas['Longitude']) > 0) {
                (new WizachaProduct(static::$productId))->setGeoloc(
                    $datas['Latitude'],
                    $datas['Longitude'],
                    '',
                    ''
                );
            }

            if (!empty($datas['Video'])) {
                $container->get('marketplace.pim.video_service')->startImportFromUrl(
                    static::$productId,
                    $datas['Video'],
                    $jobId,
                    current($lines)
                )
                ;
            }

            if (\array_key_exists('Attachments', $datas)) {
                try {
                    static::getProductService()->importAttachments(
                        (int) static::$productId,
                        $datas['Attachments'],
                        $options['attachment_delimiter'],
                        $options['field_delimiter']
                    )
                    ;
                } catch (\Throwable $exception) {
                    static::warning(
                        ProductMessage::EXIM_PRODUCT_ATTACHMENTS_FAILED,
                        $exception->getMessage()
                    );
                }
            }

            if (WizachaProduct::hasChanged(static::$productId, new Locale($lang_code))) {
                \Wizacha\Events\Config::dispatch(
                    WizachaProduct::EVENT_UPDATE,
                    (new \Wizacha\Events\IterableEvent())->setElement(static::$productId)
                );
            }
        } catch (InvalidProductDivisionExcludedCodeException $exception) {
            $error = 'division_excluded_invalid_code';
        } catch (InvalidProductDivisionIncludeCodeException $exception) {
            $error = 'division_included_invalid_code';
        } catch (InvalidProductDivisionCodeException $exception) {
            $error = 'division_invalid_code';
        } catch (EximJobException $exception) {
            $error = $exception->getMessage();
            $context = $exception->getExtra();
        } catch (\Exception $exception) {
            static::getLogger()->error(
                'Exim unknown error',
                ['exception' => $exception]
            );

            $error = __(ProductMessage::ERROR_EXIM_COULD_NOT_PROCESS_LINE);
        }

        if (false === \is_null($error)) {
            static::error($error, $context);
            return false;
        }

        $elapsed = microtime(true) - $start;

        static::getLogger()->notice(
            sprintf(
                "%s::%s stats",
                __NAMESPACE__,
                __FUNCTION__
            ),
            [
                'duration' => $elapsed,
                'jobId' => $jobId,
                'declinations' => \count($datas[Product::DECLINATION_LIST] ?? []),
            ]
        );

        return true;
    }

    private static function logCscartNotifications()
    {
        // handle Cscart error notifications
        \array_map(
            function ($notification) {
                $notificationType = new NotificationType($notification['type']);
                $message = $notification['message'];

                switch ($notificationType) {
                    case NotificationType::ERROR():
                        static::error($message);
                        break;
                    case NotificationType::WARNING():
                        static::warning($message);
                        break;
                    case NotificationType::NOTICE():
                    default:
                        static::info($message);
                        break;
                }
            },
            fn_get_notifications()
        );
    }

    private static function setOldOrDefaultTemplate(array $product_data, int $product_id): array
    {
        if ($product_data['product_template_type'] === "" || \array_key_exists('product_template_type', $product_data) === false) {
            if ($product_id > 0) {
                $product_data['product_template_type'] = static::getProductService()->get($product_id)->getProductTemplateType();
            } else {
                $product_data['product_template_type'] = array_keys(static::getTemplateService()->getTemplates())[0];
            }
        }

        return $product_data;
    }

    /**
     * @param integer $product_id
     */
    public static function removeInventory($product_id)
    {
        db_query("DELETE FROM ?:product_options_inventory WHERE product_id = ?i", $product_id);
    }

    /**
     * @param array $line
     * @param array $declination
     * @return array line with declinations
     */
    public static function mergedCSVLineWithDeclination($line, array $declination = [])
    {
        if (isset($line['product_code']) && !empty($line['combination'])) {
            $declination[] = array_intersect_key(
                $line,
                [
                    'Combination Code' => 1,
                    'combination' => 1,
                    'amount' => 1,
                    'price' => 1,
                    'crossed_out_price' => 1,
                    'affiliate_link' => 1,
                    'Price tiers' => 1,
                    'Combination Supplier Ref' => 1,
                ]
            );
        }

        $line[Product::DECLINATION_LIST] = $declination;

        return $line;
    }


    /**
     * Create a new task in progress and set the progress_id in pattern.
     * Not tested
     * @param integer $nb_steps
     * @param array $pattern
     * @param string $task_id
     */
    public static function createProgress($nb_steps, &$pattern, $task_id)
    {
        $company_id = \Wizacha\Registry::defaultInstance()->get(['runtime', 'company_id']);
        $progress_id = container()
                        ->get('marketplace.async_progress')
                            ->start(\Wizacha\Async\Config::Q_CSV_ID, $company_id, $task_id, $nb_steps);
        $pattern[\Wizacha\Async\Config::FIELD_PROGRESS_ID] = $progress_id;
    }

    /**
     * Deprecated helper to create a progress from file data
     * @deprecated
     * @param array $import_data
     * @param array $pattern
     * @param string $task_id
     */
    public static function createProgressFromData($import_data, &$pattern, $task_id)
    {
        self::createProgress(\count($import_data), $pattern, $task_id);
    }

    public static function getPriceTiers(
        int $productId,
        array $row,
        string $glue
    ): ?string {
        $combination = $row["Combination"] ?? null;
        $priceTiers = container()
            ->get('marketplace.price_tier.price_tier_service')
            ->getPriceTiersOfProductOrCombination(
                $productId,
                $combination
            );
        $result = array_map(
            function (PriceTier $priceTier): string {
                return implode(
                    ':',
                    [
                        $priceTier->getLowerLimit(),
                        $priceTier->getPrice()->getConvertedAmount(),
                    ]
                );
            },
            $priceTiers
        );

        return "[" . implode($glue, $result) . "]";
    }

    public static function getDivisions(int $productId, string $glue): string
    {
        $divisions = container()->get('marketplace.division.products.service')->getAllDivisionsCode($productId);

        return implode($glue, $divisions);
    }

    public static function getCity($productId)
    {
        if ($productId) {
            return (new WizachaProduct($productId))->getGeoloc()[2];
        }
    }

    public static function getPostal($productId)
    {
        if ($productId) {
            return (new WizachaProduct($productId))->getGeoloc()[3];
        }
    }

    public static function getLatitude(int $productId): ?float
    {
        if ($productId > 0) {
            return (new WizachaProduct($productId))->getGeoloc()[0];
        }

        return null;
    }

    public static function getLongitude(int $productId): ?float
    {
        if ($productId > 0) {
            return (new WizachaProduct($productId))->getGeoloc()[1];
        }

        return null;
    }

    /**
     * Logger::info() is bound to EximJob done-jobs incremental
     * So we have to ensure only one call is made by product
     * @see EximJobRepository::increment()
     * @param bool $bypass Allows to bypass incremented verification and log (used for declinations import)
     */
    private static function info(string $message, ?string $extra = null, bool $bypass = false): void
    {
        if (false === static::$incremented || true === $bypass) {
            EximJobService::info(
                $message,
                static::$jobId,
                static::$line,
                static::$productId,
                $extra,
                [
                    'productCode' => static::$productCode,
                    'companyId' => static::$companyId ?? '',
                    'combinationCode' => static::$combinationCode ?? ''
                ]
            );
            static::$incremented = true;
        }
    }

    private static function warning(string $message, ?string $extra = null): void
    {
        EximJobService::warning(
            $message,
            static::$jobId,
            static::$line,
            static::$productId,
            $extra,
            [
                'productCode' => static::$productCode,
                'companyId' => static::$companyId ?? '',
                'combinationCode' => static::$combinationCode ?? ''
            ]
        );
    }

    private static function error(string $message, ?string $extra = null): void
    {
        \array_map(
            function (int $line) use ($message, $extra): void {
                EximJobService::error(
                    $message,
                    static::$jobId,
                    $line,
                    static::$productId,
                    $extra,
                    [
                        'productCode' => static::$productCode,
                        'companyId' => static::$companyId ?? '',
                        'combinationCode' => static::$combinationCode ?? ''
                    ]
                );
            },
            static::$lines
        );
    }

    private static function exception(string $message, ?string $extra = null): void
    {
        static::error($message, $extra);

        throw new EximJobException($message, static::$jobId, static::$line, null, $extra);
    }

    private static function getProductService(): ProductService
    {
        return container()->get('marketplace.pim.product.service');
    }

    private static function getTemplateService(): TemplateService
    {
        return container()->get(TemplateService::class);
    }

    private static function getLogger(): LoggerInterface
    {
        return container()->get('logger');
    }

    /**
     * Vérifie que le tableau de déclinaisons n'est pas incohérent avec lui même pour éviter la génération de
     * données incohérentes en BDD
     */
    private static function areDeclinationsValid($declinations): bool
    {
        if (\is_array($declinations) !== true) {
            return true;
        }
        $optionService = container()->get('marketplace.pim.option_service');
        $combinations = array_column($declinations, 'combination');

        $parsedCombinations = array_map([$optionService, 'parseCombination'], $combinations);

        foreach ($parsedCombinations as $combination) {
            foreach ($combination as $combinationValue) {
                if ($combinationValue === "") {
                    return false;
                }
            }
        }

        /*
         * Transforme un tableau de la forme
         *      ['Couleur:Bleu ; taille:XL', 'Couleur:Noir']
         * en :
         *      [['Couleur', 'taille'], ['Couleur']]
         *
         */
        $combinationsKeys = array_map([$optionService, 'getProductOptionsNameByCombination'], $combinations);

        $baseCombination = reset($combinationsKeys);
        foreach ($combinationsKeys as $combinationKey) {
            if ($baseCombination != $combinationKey) {
                return false;
            }
        }

        return true;
    }
}
