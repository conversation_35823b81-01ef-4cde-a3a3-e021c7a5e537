<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Exim;

use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\Promotion\Exception\PromotionNotFound;
use Wizacha\Marketplace\Promotion\MarketplacePromotion;
use Wizacha\Marketplace\Promotion\Promotion;

include_once(__DIR__ . '/../../app/schemas/exim/product_combinations.functions.php');

/**
 * Class Order Helper class used for Csv Import/Export of orders
 * @package Wizacha\Exim
 */
class Order
{
    /**
     * Return the list of product with quantity :
     * EAN55: 5[Couleur: Blanc]: EAN65: 2
     * @param integer $order_id
     * @return string
     */
    public static function getProducts($order_id)
    {
        $order_data = self::getOrderInfo($order_id);
        $return = array_map(
            function ($product) {
                $options = '';
                if (!empty($product['extra']['product_options'])) {
                    $options = fn_exim_get_product_combination(
                        $product['product_id'],
                        fn_get_options_combination($product['extra']['product_options'])
                    );
                    $options = "[{$options}]";
                }
                return "{$product['product_code']}: {$product['amount']}{$options}";
            },
            $order_data['products']
        );

        return implode('; ', $return);
    }

    public static function getProductsComments($order_id)
    {
        $order_data = self::getOrderInfo($order_id);
        $return = array_map(
            function ($product) {
                $options = '';
                if (!empty($product['extra']['product_options'])) {
                    $options = fn_exim_get_product_combination(
                        $product['product_id'],
                        fn_get_options_combination($product['extra']['product_options'])
                    );
                    $options = "[{$options}]";
                }
                return $product['product_code'] . ': "' . str_replace('"', '\"', $product['comment']) . '"' . $options;
            },
            $order_data['products']
        );

        return implode('; ', $return);
    }

    /**
     * Return the name of status.
     * @param integer $order_id
     * @param bool $workflowTranslation
     * @return string
     */
    public static function getStatus($order_id, $workflowTranslation = false)
    {
        static $status_cache = [];
        $order_data = self::getOrderInfo($order_id);

        if ($workflowTranslation) {
            return self::getWorkflowTranslationKey($order_data);
        }

        if (!\array_key_exists($order_data['status'], $status_cache)) {
            if ($order_data['status'] === OrderStatus::INCOMPLETED) {
                $status_cache[$order_data['status']] = __('incompleted');
            } else {
                $status_cache[$order_data['status']] = fn_get_status_data(
                    $order_data['status'],
                    STATUSES_ORDER
                )['description'];
            }
        }

        return $status_cache[$order_data['status']];
    }

    public static function getTotal(int $orderId): ?float
    {
        $orderData = self::getOrderInfo($orderId);
        return $orderData['total'];
    }

    public static function getSubTotal(int $orderId): ?float
    {
        $orderData = self::getOrderInfo($orderId);
        return $orderData['subtotal'];
    }

    public static function getDiscount(int $orderId): ?float
    {
        $orderData = self::getOrderInfo($orderId);
        return $orderData['discount'];
    }

    public static function getMarketplaceDiscountTotal(int $orderId): ?float
    {
        $orderData = self::getOrderInfo($orderId);
        return $orderData['marketplace_discount_total'];
    }

    public static function getCustomerTotal(int $orderId): ?float
    {
        $orderData = self::getOrderInfo($orderId);
        return $orderData['customer_total'];
    }

    public static function getShippingCost(int $orderId): ?float
    {
        $orderData = self::getOrderInfo($orderId);
        return $orderData['shipping_cost'];
    }

    public static function getTotalExcludingTaxes(int $orderId): ?float
    {
        $orderData = self::getOrderInfo($orderId);
        return $orderData['total_excl_tax'];
    }

    /**
     * Return the sum of all taxes for one order (products and shippings)
     * @param integer $order_id
     * @return float
     */
    public static function getTaxes($order_id)
    {
        $order_data = self::getOrderInfo($order_id);
        return fn_w_get_subtotal_tax_from_order($order_data);
    }

    /**
     * Return the name of shippings
     * @param integer $order_id
     * @return string
     */
    public static function getShipping($order_id)
    {
        $order_data = self::getOrderInfo($order_id);

        return $order_data['shipping'][0]['shipping'] ?: '';
    }

    /**
     * Return promotion id applied to the order
     *
     * @param int $orderId
     * @return string
     */
    public static function getOrderPromotionId($orderId)
    {
        $orderData = static::getOrderInfo($orderId);
        $promotionIds = static::extractPromotionMarketplace($orderData['promotion_ids'], $orderData['marketplace_discount_id']);

        if (\is_null($promotionIds) || $promotionIds === '') {
            return '';
        }

        return strpos($promotionIds, ',') === false ? $promotionIds : '';
    }

    /**
     * Return the promotion name applied to the order
     * If the promotion is not found, return a translation (used in order export)
     * In others cases (ex: no promotion), return ""
     *
     * @param int $orderId
     * @return string
     */
    public static function getOrderPromotionName($orderId): string
    {
        $orderData = static::getOrderInfo($orderId);
        $promotionId = static::extractPromotionMarketplace($orderData['promotion_ids'], $orderData['marketplace_discount_id']);

        if (\is_null($promotionId) || $promotionId === '' || strpos($promotionId, ',')) {
            return '';
        }

        $orderDataPromotion = array_filter(
            $orderData['promotions'] ?? [],
            function ($promotion) use ($promotionId): bool {
                return $promotion['id'] === $promotionId;
            }
        );
        $promotion = null;

        // if there is no frozen promotions we get them from the repository.
        if (\count($orderDataPromotion) === 0) {
            try {
                /** @var Promotion $promotion */
                $promotion = container()
                    ->get('marketplace.promotion.promotionservice')
                    ->get($promotionId)
                ;
                $returnValue = $promotion->getName();
            } catch (PromotionNotFound $e) {
                $returnValue =  __('promotions_removed');
            }

            if ($promotion instanceof MarketplacePromotion) {
                $returnValue = '';
            }

            return $returnValue;
        }

        return reset($orderDataPromotion)['name'];
    }

    private static function extractPromotionMarketplace(string $promotionIds = null, string $promotionMarketplaceId = null): ?string
    {
        $promotionIds = explode(',', $promotionIds);

        if (false === \is_array($promotionIds)) {
            return '';
        }

        return implode(',', array_filter($promotionIds, function (?string $id) use ($promotionMarketplaceId): bool {
            return $id !== $promotionMarketplaceId;
        }));
    }

    public static function getOrderMarketplacePromotionId(int $orderId): ?string
    {
        $orderData = static::getOrderInfo($orderId);
        $promotionId = $orderData['marketplace_discount_id'];

        if (\is_null($promotionId) || $promotionId === '' || strpos($promotionId, ',')) {
            return '';
        }

        return $promotionId;
    }

    public static function getOrderMarketplacePromotionCoupon(int $orderId): string
    {
        $orderData = static::getOrderInfo($orderId);
        $promotionId = $orderData['marketplace_discount_id'];

        if (\is_null($promotionId) || $promotionId === '') {
            return '';
        }

        if (strpos($promotionId, ',') === false) {
            try {
                $promotionCoupon = container()
                    ->get('marketplace.promotion.promotionservice')
                    ->get($promotionId);
                if ($promotionCoupon instanceof MarketplacePromotion) {
                    $promotionCoupon = $promotionCoupon->getCoupon();
                }
            } catch (PromotionNotFound $e) {
                $promotionCoupon = __('promotions_removed');
            }
        }

        return !empty($promotionCoupon) ? $promotionCoupon : '';
    }

    public static function fnEximOrdersGetExtraData(int $orderId): string
    {
        $orderData = static::getOrderInfo($orderId);
        $extra = \json_decode($orderData['extra'], true);
        if (\is_array($extra) === true && \count($extra) > 0) {
            $return = [];
            foreach ($extra as $key => $field) {
                $return[] = $key . ': ' . $field;
            }

            return \implode('; ', $return);
        }

        return '';
    }

    /**
     * Return fn_get_order_info but it's cached
     * @param int $order_id
     * @return array
     */
    protected static function getOrderInfo($order_id)
    {
        // By using a cache here we avoid having orders with a mix of old and new columns if
        // it has been updated during the export
        static $orderCached = [];

        // A small cache to avoid requesting DB multiple times for the same order
        if (\count($orderCached) > 10) {
            $orderCached = [];
        }

        if (false === \array_key_exists($order_id, $orderCached)) {
            $orderCached[$order_id] = container()
                ->get('marketplace.order.order_service')
                ->overrideEximOrder($order_id);
        }

        return $orderCached[$order_id];
    }

    protected static function getWorkflowTranslationKey(array $order = [])
    {
        if ($order['canceled']) {
            return __('workflow_canceled');
        }

        if ($order['refunded']) {
            return __('workflow_refunded');
        }

        $key = "workflow_{$order['workflow_current_module_name']}_{$order['workflow_current_step_name']}_{$order['workflow_status']}";

        return __(str_replace('-', '_', $key));
    }
}
