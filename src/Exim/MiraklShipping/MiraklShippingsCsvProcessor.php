<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim\MiraklShipping;

use Doctrine\DBAL\Driver\Connection;
use Wizacha\Exim\MiraklShipping\Exception\InvalidData;
use Wizacha\Exim\MiraklShipping\Exception\InvalidHeader;
use Wizacha\Marketplace\Shipping\DeliveryType;
use Wizacha\Shipping;
use Wizacha\Status;

class MiraklShippingsCsvProcessor
{
    public const DELIMITER = ';';
    public const POSITION_COL = 'position';
    public const KEY_COL = 'key';
    public const INFO_COL = 'info';
    public const DESCRIPTION_SUBCOL = 'description';

    protected const REQUIRED_COLS = [
        self::POSITION_COL,
        self::KEY_COL,
        self::INFO_COL
    ];

    /** @var Connection */
    protected $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
    }

    public function process(string $path): \Generator
    {
        yield 'Reset Wizaplace shippings';

        $this->resetShippings();

        foreach ($this->read($path) as $miraklShipping) {
            $info = \json_decode($miraklShipping[static::INFO_COL], true);
            if (false === \is_array($info)) {
                throw new InvalidData('info must be json encoded data');
            }

            $descriptions = implode(
                "\n",
                array_column(
                    $info,
                    static::DESCRIPTION_SUBCOL
                )
            );

            yield sprintf(
                'Import Mirakl Shipping: %s',
                $miraklShipping[static::KEY_COL]
            );

            $this->insertShipping(
                $miraklShipping[static::KEY_COL],
                (int) $miraklShipping[static::POSITION_COL],
                $descriptions
            );
        }
    }

    protected function insertShipping(
        string $name,
        int $position,
        string $description = '',
        ?Status $status = null,
        ?string $shippingType = null,
        ?DeliveryType $deliveryType = null,
        array $taxIds = [2]
    ) {
        return fn_update_shipping(
            [
                'shipping' => $name,
                'position' => $position,
                'w_description' => $description,
                'status' => $status ?? Status::ENABLED(),
                'w_type' => $shippingType ?? Shipping::TYPE_ALL,
                'w_delivery_type' => $deliveryType ?? DeliveryType::STANDARD(),
                'tax_ids' => $taxIds,
            ],
            0
        );
    }

    protected function resetShippings(): void
    {
        $tables = [
            'cscart_shippings',
            'cscart_shipping_descriptions',
        ];

        foreach ($tables as $table) {
            $statement = $this->connection->prepare(
                sprintf(
                    'TRUNCATE TABLE %s',
                    $table
                )
            );

            $statement->execute();
        }
    }

    protected function read(string $path): \Generator
    {
        $fileHandler = fopen($path, 'r');

        $header = fgetcsv($fileHandler, 0, static::DELIMITER);

        $this->validateHeader($header);

        while ($row = fgetcsv($fileHandler, 0, static::DELIMITER)) {
            if ($row) {
                yield array_combine(
                    $header,
                    $row
                );
            }
        }

        fclose($fileHandler);
    }

    /**
     * @throws InvalidHeader
     */
    protected function validateHeader(array $header): void
    {
        $missingKeys = array_filter(
            static::REQUIRED_COLS,
            function (string $value) use ($header): bool {
                return false === \in_array($value, $header);
            }
        );

        if (0 < \count($missingKeys)) {
            throw new InvalidHeader(
                sprintf(
                    'Invalid header, missing key(s): %s',
                    implode(', ', $missingKeys)
                )
            );
        }
    }
}
