<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Exim;

use Wizacha\Component\Import\JobType;

class Misc
{
    private const JOBTYPE_MAPPING = [
        'features' => 'ATTRIBUTES',
        'feature_variants' => 'VARIANTS',
        'categories' => 'CATEGORIES',
        'link_categories' => 'LINK_CATEGORIES',
        'available_offers_divisions' => 'AVAILABLE_OFFERS',
        'multi_vendor_products' => 'MULTI_VENDOR_PRODUCTS',
        'products' => 'PRODUCTS',
        'orders' => 'ORDERS',
        'translations' => 'TRANSLATIONS',
        'newsletter' => 'NEWSLETTER',
        'accounting' => 'ACCOUNTING',
        'companies' => 'COMPANIES',
        'users' => 'USERS',
        'inventory_prices' => 'PRODUCT_PRICES',
        'inventory_quantities' => 'PRODUCT_QUANTITIES',
        'marketplace_discounts' => 'MARKETPLACE_DISCOUNTS',
        'related_products' => 'RELATED_PRODUCTS',
        'product_attributes' => 'PRODUCT_ATTRIBUTES',
    ];

    /**
     * @param array $schema
     * @param array $pattern
     * @return array [bool $schema_match, array $new_schema, $failed_fields]
     */
    public static function analyseSchema($schema, $pattern)
    {
        $failed_fields = [];
        array_walk($schema, 'fn_trim_helper');
        $schema = array_map('strtolower', $schema);
        $schema_match = false;

        foreach ($pattern['export_fields'] as $field => $data) {
            if (!empty($data['required']) && $data['required'] == true && !\in_array(strtolower($field), $schema)) {
                if (empty($data['db_field']) || $data['db_field'] != 'lang_code') {
                    $failed_fields[] = $field;
                }
            }

            $key = array_search(strtolower($field), $schema);
            if ($key !== false) {
                $schema_match = true;
                $schema[$key] = !empty($data['db_field']) ? $data['db_field'] : $field;
            }
        }
        return [$schema_match, $schema, $failed_fields];
    }

    /**
     * If default_separator isn't in line and exist alternate separator in line, return this separator.
     * Else, return default_separator
     * @param string $line
     * @param string $default_separator
     * @return string
     */
    public static function checkSeparator($line, $default_separator)
    {
        $alternate_separator = ['|'];
        if (strpos($line, $default_separator) === false) {
            foreach ($alternate_separator as $separator) {
                if (strpos($line, $separator) !== false) {
                    return $separator;
                }
            }
        }
        return $default_separator;
    }

    /** map legacy section pattern definition to JobTypes */
    public static function patternJobTypeMapping(string $section): JobType
    {
        return JobType::{static::JOBTYPE_MAPPING[$section]}();
    }

    /** @param bool|string $value */
    public static function getLegacyTrue($value): string
    {
        return ($value === '1' || $value === true) ? 'Y' : 'N';
    }

    public static function ConvertDateFormat(?string $date): string
    {
        return $date !== null ? (new \DateTime($date))->format('d/m/Y') : '';
    }
}
