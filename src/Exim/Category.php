<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Exim;

use Wizacha\Component\Import\Exception\ImageNotFoundException;
use Wizacha\Component\Import\EximJobService;
use Wizacha\ImageManager;
use Wizacha\Exim\Import\ImportReportMessage\CategoryMessage;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Status;

/**
 * Class Category: Helpers for import/export csv
 * @package Wizacha\Exim
 */
class Category
{

    public const DEFAULT_SEPARATOR = '///';

    private static $created = false;

    /**
     * Le déplacement des categories se fait lorsque
     *   il n'y a pas la colonne 'category id'
     *   ET que la colonne 'old category' est renseignée
     * @param string $category
     * @param string $path_delimiter
     * @param array $data
     * @param string $lang_code
     * @param string $jobId
     * @return bool
     */
    public static function put($category, $path_delimiter, array $data, string $lang_code = null, string $jobId)
    {
        $lang_code = $lang_code ?? (string) GlobalState::contentLocale();

        if (!\is_array($category)) {
            $category = [$lang_code => $category];
            $data = [$lang_code => $data];
        }

        foreach ($category as $lang_code => $name) {
            self::$created = false;
            $translation = $data[$lang_code];
            $new_tree = explode($path_delimiter, $name);
            $category_id = $category_id ?? ($translation['category_id'] ?: 0);
            // unset status only if not valid
            if (!Status::isValid($translation['status'])) {
                unset($translation['status']);
            }

            if ($translation['Delete'] === '1') {
                $category_id = $category_id ?: self::getIdFromPath($new_tree, $lang_code);
                if ($category_id) {
                    fn_delete_category($category_id);
                    EximJobService::info(CategoryMessage::EXIM_CATEGORY_DELETE, $jobId, (string) $translation['line'], $category_id, $name);
                }

                return;
            }

            if ($category_id) {
                $translation['category_id'] = $category_id;
                $translation['category'] = array_pop($new_tree);
            } elseif ($translation['Old Category']) {
                //Move category : get category_id
                $old_tree = explode($path_delimiter, $translation['Old Category']);
                $category_id = $translation['category_id'] = self::getIdFromPath($old_tree, $lang_code);
                if (!$category_id) {
                    return false;
                }
                $translation['category'] = array_pop($new_tree);
                $translation['parent_id'] = self::getIdFromPath($new_tree, $lang_code, true);
            } else {
                $category_id = $translation['category_id'] = self::getIdFromPath($new_tree, $lang_code, true);
                $translation['category'] = array_pop($new_tree);
            }

            $category_id = (int) $category_id;

            // Set category_id in $data passed by reference
            // Use on multilangue to connect different line
            $data[$lang_code]['category_id'] = $category_id;

            if (fn_update_category($translation, $category_id, $lang_code)) {
                if (!self::$created) {
                    EximJobService::info(CategoryMessage::EXIM_CATEGORY_UPDATE, $jobId, (string) $translation['line'], $category_id, $translation['category']);
                } else {
                    EximJobService::info(CategoryMessage::EXIM_CATEGORY_CREATE, $jobId, (string) $translation['line'], $category_id, $translation['category']);
                }
            } else {
                EximJobService::warning(CategoryMessage::EXIM_CATEGORY_CREATE_FAILED, $jobId, (string) $translation['line'], null, $translation['category']);
                return false;
            }

            if (\array_key_exists('image', $data[$lang_code])) {
                try {
                    static::handleImage($category_id, $data[$lang_code]['image']);
                } catch (\Throwable $e) {
                    EximJobService::warning(CategoryMessage::EXIM_CATEGORY_IMAGE_WARNING, $jobId, (string) $translation['line'], null, $e->getMessage());
                }
            }
        }

        return true;
    }

    /**
     * @param array $path ['parent', 'sub_parent', 'sub_sub_parent'...]
     * @param string $lang_code
     * @param bool $create_if_not_exists
     * @param int $parent_id
     * @return bool|int last element of tree or false if category doesn't exist and $create is false..
     */
    public static function getIdFromPath(array $path, $lang_code = null, $create_if_not_exists = false, $parent_id = 0)
    {
        $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();

        if (empty($path)) {
            return (int) $parent_id;
        }
        $elem = trim(array_shift($path));
        $next_parent = \Tygh\Database::getField(
            "SELECT c.category_id
             FROM ?:categories as c
             LEFT JOIN ?:category_descriptions as cd ON c.category_id=cd.category_id
             WHERE lang_code=?s
             AND c.parent_id = ?i
             AND cd.category = ?s",
            $lang_code,
            $parent_id,
            $elem
        );
        if (!$next_parent) {
            if (!$create_if_not_exists) {
                return false;
            }
            $next_parent = fn_update_category(['parent_id' => $parent_id, 'category' => $elem, 'status' => \Wizacha\Status::HIDDEN]);
            self::$created = true;
        }

        return (int) self::getIdFromPath($path, $lang_code, $create_if_not_exists, $next_parent);
    }

    /**
     * On preprocessing for import, add category_id if field is not in CSV
     * @param array $data
     */
    public static function addCategoryId(&$data)
    {
        if (empty($data)) {
            return;
        }

        if (\array_key_exists('category_id', reset($data))) {
            return;
        }

        array_walk(
            $data,
            function (&$line) {
                $line['category_id'] = '';
            }
        );
    }

    public static function getImageUrl(int $categoryId): ?string
    {
        $pairs = fn_get_image_pairs($categoryId, 'category', 'M');

        return $pairs['detailed']['http_image_path'] ?? null;
    }

    public static function handleImage(int $categoryId, string $newImageUrl): void
    {
        if (ImageManager::BYPASS_URL === $newImageUrl) {
            return;
        }

        $currentPairs = fn_get_image_pairs($categoryId, 'category', 'M');
        $currentImageUrl = $currentPairs['detailed']['http_image_path'] ?? '';

        if ($currentImageUrl === $newImageUrl) {
            return;
        }

        if ($newImageUrl !== "") {
            $newImageFile = tempnam(sys_get_temp_dir(), uniqid());
            file_put_contents($newImageFile, static::getImageContent($newImageUrl));

            $detailed = [0 => [
                'name' => pathinfo($newImageUrl, PATHINFO_BASENAME),
                'type' => mime_content_type($newImageFile),
                'path' => $newImageFile,
                'error' => 0,
                'size' => filesize($newImageFile),
            ]
            ];

            $pairsData = [0 => [
                'pair_id' => $currentPairs['pair_id'] ?? null,
                'type' => 'M',
                'object_id' => $categoryId,
            ]
            ];

            fn_update_image_pairs([], $detailed, $pairsData, $categoryId, 'category', [], false);
        } else {
            fn_delete_image_pairs($categoryId, 'category', 'M');
        }
    }

    /**
     * For links import : Add company in pattern and data.
     * @param array $data
     * @param array $pattern
     * @param integer $company_id
     */
    public static function addCompanyId(&$data, &$pattern, $company_id)
    {
        $pattern['key'][] = 'company_id';
        $pattern['export_fields']['company_id'] = [
            'db_field' => 'company_id',
            'required' => true,
            'alt_key' => true,
        ];

        array_walk(
            $data,
            function (&$line) use ($company_id) {
                $line['company_id'] = $company_id;
            }
        );
    }

    /**
     * @param string $category Category full path
     * @param string $path_delimiter
     * @return false|int
     */
    public static function getIdByFullPath($category, $path_delimiter, string $lang_code = null)
    {
        return self::getIdFromPath(explode($path_delimiter, $category), $lang_code ?? (string) GlobalState::contentLocale());
    }

    /**
     * Caching is not do for workers
     * @param string $vendor_category
     * @param integer $company_id
     * @return (string) int|null category_id
     */
    public static function getIdByVendorCategory($vendor_category, $company_id)
    {
        return \Tygh\Database::getField(
            'SELECT category_id FROM ?:w_links_vendor_categories
            WHERE company_id IN (?a) AND vendor_category = ?s
            ORDER BY company_id DESC
            LIMIT 1',
            [0, $company_id],
            $vendor_category
        );
    }

    /**
     * If row has no category_id, skip database query
     * (used for link_categories)
     *
     * @param array $row
     * @param bool $skip_record
     */
    public static function checkId($row, &$skip_record)
    {
        if (!$row['category_id']) {
            $skip_record = true;
        };
    }

    /**
     * Get id for category and create a link if the category don't exist.
     * @param string $category
     * @param string $delimiter
     * @param integer  $company_id
     * @return integer
     */
    public static function getId($category, $delimiter, $company_id = 0, string $lang_code = null, bool $clearCache = false)
    {
        $lang_code = $lang_code ?? (string) GlobalState::contentLocale();
        $category_id = self::getIdByFullPath($category, $delimiter, $lang_code);

        if (!empty($category_id)) {
            return (int) $category_id;
        }

        //Try to match the category with vendor's data
        $category_id = \Wizacha\Exim\Category::getIdByVendorCategory($category, $company_id);

        if ($category_id) {
            return (int) $category_id;
        }

        $category_id = \Wizacha\Category::getGarbageCategoryId($clearCache);

        //Create the link between the vendor's category and the failure category
        fn_w_update_links_vendor_categories([['category_id' => $category_id, 'vendor_category' => $category]]);
        return (int) $category_id;
    }

    /**
     * If the feature isn't global and the category_id isn't linked, link it.
     * @param integer $category_id
     * @param integer $feature_id
     */
    public static function associateFeature($category_id, $feature_id)
    {
        \Tygh\Database::query(
            "SET @cat_id = ?i;
            SET  @feat_id = ?i;
            UPDATE ?:product_features
            SET categories_path = CONCAT(categories_path,',',@cat_id)
            WHERE feature_id=@feat_id AND
                !(FIND_IN_SET(@cat_id, categories_path) OR categories_path='') ;",
            $category_id,
            $feature_id
        );
    }

    private static function getImageContent(string $url): string
    {
        $context = stream_context_create();
        stream_context_set_params($context, [
            'notification' => function (int $notificationCode, int $severity, ?string $message, ?int $message_code, ?int $bytesTransferred, ?int $bytesMax) use ($url): void {
                if ($severity === STREAM_NOTIFY_SEVERITY_ERR) {
                    throw new ImageNotFoundException("Error code $notificationCode - $message_code while getting image $url ($message)");
                }
            }
        ]);

        return file_get_contents($url, false, $context);
    }
}
