<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Exim;

use Wizacha\Component\Import\EximJobService;
use Wizacha\Component\Import\JobStatus;
use Wizacha\Component\Locale\Locale;
use Wizacha\Cscart\Exim;
use Wizacha\Exim\Exception\EximJobException;
use Wizacha\Exim\Import\ImportReportMessage\ProductPriceAndStockMessage;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Traits\CompanyTrait;
use Wizacha\Product as WizachaProduct;
use Wizacha\Registry;
use Wizacha\Events\Config;

/**
 * Class Product: Helpers for import/export csv
 * @package Wizacha\Exim
 */
class ProductStock
{
    use CompanyTrait;

    public const DECLINATION_LIST = 'declinations';

    /** @var int */
    private static $jobId;
    /** @var int */
    private static $line;
    /** @var ?int */
    private static $productId;
    /** @var bool Determine if EximJob counter has been incremented */
    private static $incremented = false;

    private static string $productCode;
    private static ?int $companyId;

    public static function ImportProductStocks(
        array $datas,
        array $options,
        string $lang_code,
        array $progress_id = [],
        string $jobId = null,
        array $lines = []
    ): bool {
        static::$jobId = $jobId;
        static::$line = current($lines);
        static::$incremented = false;

        $productsInventories = [];
        $groupLines = implode(',', $lines);

        $container = container();
        $delayExec = $container
            ->get('marketplace.async_dispatcher')
            ->delayExec(
                __METHOD__,
                \func_get_args(),
                Registry::defaultInstance(),
                $progress_id
            );
        if (true === $delayExec) {
            return true;
        }

        // If job not exist then stop the process
        $jobService = $container->get('marketplace.import.job_service');
        try {
            $job = $jobService->get($jobId);
        } catch (NotFound $exception) {
            return true;
        }

        if ($job->getStatus()->equals(JobStatus::CANCELED()) === true) {
            return true;
        }

        // check company id
        $companyId = self::checkCompanyId(Registry::defaultInstance()->get(['runtime', 'company_id']), $datas);

        static::$productId = (int) WizachaProduct::getIdByProductCode($datas['product_code'], $companyId) ?: 0;
        static::$productCode = $datas['product_code'];
        static::$companyId = $companyId;

        if (static::$productId === 0) {
            array_map(function ($line) {
                static::$line = $line;
                static::warning(ProductPriceAndStockMessage::EXIM_WRONG_PRODUCT_DECLINATION);
            }, $lines);

            return false;
        }
        $lang_code = $lang_code ?? ((string) GlobalState::contentLocale());

        try {
            if ($container->get('marketplace.pim.product.service')->get(static::$productId)->getProductTemplateType() === 'service') {
                array_map(function ($line) {
                    static::$line = $line;
                    static::warning(ProductPriceAndStockMessage::EXIM_WRONG_PRODUCT_DECLINATION);
                }, $lines);


                return false;
            }

            // Update product amount
            if (\array_key_exists('amount', $datas) === true) {
                fn_update_product_quantity(static::$productId, (int) $datas['amount']);
                static::info(ProductPriceAndStockMessage::EXIM_PRODUCT_QUANTITIES_UPDATE);
            }

            if ($datas[ProductStock::DECLINATION_LIST]) {
                $fakeCounter = ['E' => 0, 'N' => 0, 'S' => 0];
                foreach ($datas[ProductStock::DECLINATION_LIST] as $key => $declination_data) {
                    // we need to update this static to keep track of the right csv line number
                    static::$line = $lines[$key];
                    $combination = Exim::exim_put_product_combination(
                        static::$productId,
                        $datas['product_code'],
                        $declination_data['Combination Code'],
                        [
                            $lang_code => $declination_data['Combination']
                            //need combination separately for each language
                        ],
                        isset($declination_data['amount']) ? $declination_data['amount'] : null,
                        $fakeCounter,
                        null,
                        null,
                        null,
                        $jobId,
                        $lines[$key],
                        null,
                        null,
                        null,
                        true
                    );

                    if ($key > 0) {
                        static::info(ProductPriceAndStockMessage::EXIM_PRODUCT_QUANTITIES_PRICE_UPDATE, $datas['product_code'], true);
                    }
                }
            }

            if (WizachaProduct::hasChanged(static::$productId, new Locale($lang_code))) {
                Config::dispatch(
                    WizachaProduct::EVENT_UPDATE,
                    (new \Wizacha\Events\IterableEvent())->setElement(static::$productId)
                );
            }
        } catch (EximJobException $exception) {
            static::error($exception->getMessage(), $exception->getExtra());

            return false;
        }

        return true;
    }

    /**
     * Logger::info() is bound to EximJob done-jobs incremental
     * So we have to ensure only one call is made by product
     * @param bool $bypass Allows to bypass incremented verification and log (used for declinations import)
     * @see EximJobRepository::increment()
     */
    private static function info(string $message, ?string $extra = null, bool $bypass = false): void
    {
        if (false === static::$incremented || true === $bypass) {
            EximJobService::info(
                $message,
                static::$jobId,
                static::$line,
                static::$productId,
                $extra,
                [
                    'productCode' => static::$productCode,
                    'companyId' => static::$companyId ?? ''
                ]
            );
            static::$incremented = true;
        }
    }

    private static function warning(string $message, ?string $extra = null): void
    {
        EximJobService::warning(
            $message,
            static::$jobId,
            static::$line,
            static::$productId,
            $extra,
            [
                'productCode' => static::$productCode,
                'companyId' => static::$companyId ?? ''
            ]
        );
    }

    private static function error(string $message, ?string $extra = null): void
    {
        EximJobService::error(
            $message,
            static::$jobId,
            static::$line,
            static::$productId,
            $extra,
            [
                'productCode' => static::$productCode,
                'companyId' => static::$companyId ?? ''
            ]
        );
    }

    private static function exception(string $message, ?string $extra = null): void
    {
        throw new EximJobException($message, static::$jobId, static::$line, null, $extra);
    }
}
