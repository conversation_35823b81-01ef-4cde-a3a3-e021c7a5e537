<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim;

use Symfony\Contracts\EventDispatcher\Event;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Wizacha\Component\Notification\NotificationEvent;

class AsyncExportHasFinished extends Event implements NotificationEvent
{
    /** @var int */
    private $userId;

    /** @var string */
    protected $url;

    public function __construct(string $url, int $userId)
    {
        $this->url = $url;
        $this->userId = $userId;
    }

    public static function buildDebugForm(FormBuilder $form)
    {
        $form->add('url', TextType::class);
        $form->add('userId', IntegerType::class);
    }

    public static function createFromForm(Form $form)
    {
        return new static($form->getData()['url'], $form->getData()['userId']);
    }

    public static function getDescription(): string
    {
        return 'csv_export_complete';
    }

    public function getUrl(): string
    {
        return $this->url;
    }

    public function getUserId(): int
    {
        return $this->userId;
    }
}
