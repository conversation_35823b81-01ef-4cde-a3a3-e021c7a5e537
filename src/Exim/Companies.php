<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim;

use Wizacha\Marketplace\Company\Company;
use Wizacha\Marketplace\Company\CompanyService;

/**
 * Class Company Helper class used for Csv Export of Companies
 */
class Companies
{
    /** @var Company */
    private static $currentCompany = null;

    public static function getField(int $id, string $key): ?string
    {
        if (false === \array_key_exists($key, static::getCompanyData($id))) {
            return null;
        }

        return static::getCompanyData($id)[$key];
    }

    public static function getMetaField(int $id, string $key): ?string
    {
        if (false === \array_key_exists($key, static::getCompanyData($id)['meta'])) {
            return null;
        }

        return static::getCompanyData($id)['meta'][$key];
    }

    public static function getExtraField(int $id, $delimiter): ?string
    {
        if (false === \is_array(static::getCompanyData($id)['extra'])) {
            return null;
        }

        $extras = static::getCompanyData($id)['extra'];
        $concatExtras = '';
        $i = 1;
        $countExtras = \count($extras);
        foreach ($extras as $key => $extra) {
            $concatExtras .= $key . ':' . $extra . ( $i < $countExtras ? $delimiter : '');
            $i++;
        }

        return $concatExtras;
    }

    public static function getStatus(int $id): string
    {
        return strtolower(static::getCompany($id)->getStatus()->getKey());
    }

    public static function getInvoicingDisabled(int $id): ?string
    {
        $companyData = static::getCompanyData($id);

        if (false === \array_key_exists('invoicingDisabled', $companyData)) {
            return null;
        }

        return $companyData['invoicingDisabled'] === true ? "Y" : "N";
    }

    /**
     * Get company
     * Note: We set current company to don't make querys for each fields
     */
    private static function getCompany(int $id): Company
    {
        if (static::$currentCompany instanceof Company && static::$currentCompany->getId() === $id) {
            return static::$currentCompany;
        }

        static::$currentCompany = container()->get(CompanyService::class)->get($id);

        return static::$currentCompany;
    }

    /** @return mixed[] */
    private static function getCompanyData(int $id): array
    {
        return static::getCompany($id)->expose();
    }
}
