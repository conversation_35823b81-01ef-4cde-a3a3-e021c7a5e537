<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Exim;

/**
 * Class VendorCategory: Helpers for link between vendors category and wizacha categories
 * @package Wizacha\Exim
 */
class VendorCategory
{
    /**
     * @param integer $company_id
     * @return integer
     */
    public static function countLinks($company_id)
    {
        return \Tygh\Database::getField(
            "SELECT COUNT(*) FROM ?:w_links_vendor_categories WHERE company_id=?i",
            $company_id
        );
    }

    /**
     * @param integer $company_id
     * @param integer $begin
     * @param integer $end
     * @return array
     */
    public static function getLinks($company_id, $begin = 0, $end = 10)
    {
        return \Tygh\Database::getHash(
            'SELECT w_link_id, vendor_category, category_id, IF(category_id = ?i, 1, 0) as category_order
             FROM ?:w_links_vendor_categories
             WHERE company_id=?i
             ORDER BY category_order DESC, vendor_category ASC
             LIMIT ?i, ?i
             ',
            'w_link_id',
            \Wizacha\Category::getGarbageCategoryId(),
            $company_id,
            $begin,
            $end
        );
    }

    /**
     * @param integer $company_id
     * @param integer $link_id
     */
    public static function delete($company_id, $link_id)
    {
        \Tygh\Database::query(
            "DELETE FROM ?:w_links_vendor_categories
             WHERE company_id = ?i AND w_link_id = ?i",
            $company_id,
            $link_id
        );
    }
}
