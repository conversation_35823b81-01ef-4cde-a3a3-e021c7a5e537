<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Exim;

use Wizacha\Marketplace\Order\AmountTaxesDetail\AmountsTaxesDetails;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Promotion\Exception\PromotionNotFound;
use Wizacha\Marketplace\Promotion\PromotionService;

class Accounting
{
    /**
     * @var OrderService
     */
    protected static $orderService;

    /**
     * @var PromotionService
     */
    protected static $promotionService;

    public static function getOrderService(): OrderService
    {
        if (\is_null(static::$orderService)) {
            static::$orderService = container()->get('marketplace.order.order_service');
        }

        return static::$orderService;
    }

    public static function getPromotionService(): PromotionService
    {
        if (\is_null(static::$promotionService)) {
            static::$promotionService = container()->get('marketplace.promotion.promotionservice');
        }

        return static::$promotionService;
    }

    public static function getTotalTaxes(int $orderId): float
    {
        return static::getOrderService()
            ->getOrder($orderId)
            ->getAmountsTaxesDetails()
            ->get(AmountsTaxesDetails::TOTALS)
            ->exposeTaxes();
    }

    public static function getTotalExcludingTaxes(int $orderId): float
    {
        return static::getOrderService()
            ->getOrder($orderId)
            ->getAmountsTaxesDetails()
            ->get(AmountsTaxesDetails::TOTALS)
            ->exposeExcludingTaxes();
    }

    public static function getCommissionsIncludingTaxes(int $orderId): float
    {
        return static::getOrderService()
            ->getOrder($orderId)
            ->getAmountsTaxesDetails()
            ->get(AmountsTaxesDetails::COMMISSIONS)
            ->exposeIncludingTaxes();
    }

    public static function getCommissionsTaxes(int $orderId): float
    {
        return static::getOrderService()
            ->getOrder($orderId)
            ->getAmountsTaxesDetails()
            ->get(AmountsTaxesDetails::COMMISSIONS)
            ->exposeTaxes();
    }

    public static function getCommissionsExcludingTaxes(int $orderId): float
    {
        return static::getOrderService()
            ->getOrder($orderId)
            ->getAmountsTaxesDetails()
            ->get(AmountsTaxesDetails::COMMISSIONS)
            ->exposeExcludingTaxes();
    }

    public static function getVendorShareIncludingTaxes(int $orderId): float
    {
        return static::getOrderService()
            ->getOrder($orderId)
            ->getAmountsTaxesDetails()
            ->get(AmountsTaxesDetails::VENDOR_SHARE)
            ->exposeIncludingTaxes();
    }

    public static function getVendorShareTaxes(int $orderId): float
    {
        return static::getOrderService()
            ->getOrder($orderId)
            ->getAmountsTaxesDetails()
            ->get(AmountsTaxesDetails::VENDOR_SHARE)
            ->exposeTaxes();
    }

    public static function getVendorShareExcludingTaxes(int $orderId): float
    {
        return static::getOrderService()
            ->getOrder($orderId)
            ->getAmountsTaxesDetails()
            ->get(AmountsTaxesDetails::VENDOR_SHARE)
            ->exposeExcludingTaxes();
    }

    public static function getShippingCostTaxes(int $orderId): float
    {
        return static::getOrderService()
            ->getOrder($orderId)
            ->getAmountsTaxesDetails()
            ->get(AmountsTaxesDetails::SHIPPING_COSTS)
            ->exposeTaxes();
    }

    public static function getShippingCostExcludingTaxes(int $orderId): float
    {
        return static::getOrderService()
            ->getOrder($orderId)
            ->getAmountsTaxesDetails()
            ->get(AmountsTaxesDetails::SHIPPING_COSTS)
            ->exposeExcludingTaxes();
    }

    public static function getMarketplaceDiscountCode(int $orderId): ?string
    {
        $promotionId = static::getOrderService()->getOrder($orderId)->getMarketplaceDiscountId();

        if (\is_null($promotionId)) {
            return null;
        }

        try {
            return static::getPromotionService()->getMarketplacePromotion((string) $promotionId)->getCoupon();
        } catch (PromotionNotFound $exception) {
            return null;
        }
    }

    public static function getMarketplaceDiscountTotal(int $orderId): float
    {
        return static::getOrderService()
            ->getOrder($orderId)
            ->getMarketplaceDiscountTotal()
            ->getConvertedAmount();
    }

    public static function getCompanyName(int $orderId): string
    {
        return static::getOrderService()
            ->getOrder($orderId)
            ->getCompany()
            ->getName();
    }
}
