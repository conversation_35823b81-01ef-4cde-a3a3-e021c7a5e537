<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Discuss\Internal\Entity;

use Wizacha\Discuss\Entity\Discussion\Status;
use Wizacha\Discuss\Entity\DiscussionInterface;

/**
 * Class DiscussionUser
 * @package Wizacha\Discuss\Internal\Entity
 */
class DiscussionUser
{
    /**
     * @var integer
     */
    protected $id;

    /**
     * @var DiscussionInterface
     */
    protected $discussion;

    /**
     * @var int
     */
    protected $user_id;

    /**
     * @var Status
     */
    protected $status;

    /**
     * @var bool
     */
    protected $is_initiator;

    /**
     * @param DiscussionInterface $discussion
     * @param int $user_id
     * @param Status $status
     * @param bool $is_initiator
     */
    public function __construct(DiscussionInterface $discussion, $user_id, Status $status, $is_initiator)
    {
        $this->discussion   = $discussion;
        $this->user_id      = (int) $user_id;
        $this->status       = $status;
        $this->is_initiator = (bool) $is_initiator;
    }

    /**
     * @return boolean
     */
    public function isInitiator()
    {
        return $this->is_initiator;
    }

    /**
     * @return Status
     */
    public function getStatus()
    {
        return $this->status;
    }

    /**
     * @param Status $status
     * @return $this
     */
    public function setStatus(Status $status)
    {
        $this->status = $status;
        return $this;
    }

    /**
     * @return int
     */
    public function getUserId()
    {
        return $this->user_id;
    }
}
