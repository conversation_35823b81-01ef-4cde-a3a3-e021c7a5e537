<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Discuss\Internal\Entity;

use Wizacha\Discuss\Entity\DiscussionInterface;

/**
 * Class MetaData
 * @package Wizacha\Discuss\Internal\Entity
 */
class MetaData
{
    protected $id;

    /**
     * @var DiscussionInterface
     */
    protected $discussion;

    /**
     * @var string
     */
    protected $name;

    /**
     * @var string
     */
    protected $value;

    /**
     * @param DiscussionInterface $discussion
     * @param string $name
     * @param string $value
     */
    public function __construct(DiscussionInterface $discussion, $name, $value)
    {
        $this->discussion = $discussion;
        $this->name       = $name;
        $this->value      = $value;
    }

    /**
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @return string
     */
    public function getValue()
    {
        return $this->value;
    }

    /**
     * @return string
     */
    public function __toString()
    {
        return (string) $this->value;
    }
}
