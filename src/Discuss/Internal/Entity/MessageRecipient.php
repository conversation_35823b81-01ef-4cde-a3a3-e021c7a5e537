<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Discuss\Internal\Entity;

use Wizacha\Discuss\Entity\MessageInterface;

/**
 * Class MessageRecipient
 * @package Wizacha\Discuss\Internal\Entity
 */
class MessageRecipient
{
    /**
     * @var integer
     */
    protected $id;

    /**
     * @var MessageInterface
     */
    protected $message;

    /**
     * @var int
     */
    protected $user_id;

    /**
     * @var \DateTime
     */
    protected $read_date;

    /**
     * @param MessageInterface $message
     * @param int $recipient_id
     */
    public function __construct(MessageInterface $message, $recipient_id)
    {
        $this->message = $message;
        $this->user_id = (int) $recipient_id;
    }

    /**
     * @return int
     */
    public function getRecipientId()
    {
        return $this->user_id;
    }

    /**
     * @return \DateTime
     */
    public function getReadDate()
    {
        return $this->read_date;
    }

    /**
     * @return bool
     */
    public function isRead()
    {
        return null !== $this->read_date;
    }

    /**
     * @param \DateTime $date
     * @return $this
     */
    public function setReadDate(\DateTime $date)
    {
        $this->read_date = $date;
        return $this;
    }

    /**
     * @return $this
     */
    public function setAsRead()
    {
        $this->read_date = new \DateTime();
        return $this;
    }
}
