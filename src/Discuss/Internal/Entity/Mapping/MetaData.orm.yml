Wizacha\Discuss\Internal\Entity\MetaData:
    type: entity
    table: discuss_meta_data
    uniqueConstraints:
        unique_name_by_discussion:
            columns:
                - discussion_id
                - name
    id:
        id:
            type: integer
            scale: 0
            length: null
            unique: false
            nullable: false
            precision: 0
            id: true
            generator:
                strategy: IDENTITY
    fields:
        name:
            type: string
            scale: 0
            length: null
            unique: false
            nullable: false
            precision: 0
        value:
            type: string
            scale: 0
            length: null
            unique: false
            nullable: false
            precision: 0
    manyToOne:
        discussion:
            targetEntity: Wizacha\Discuss\Entity\Discussion
            cascade: {  }
            fetch: LAZY
            mappedBy: null
            inversedBy: meta_data
            joinColumns:
                discussion_id:
                    referencedColumnName: id
            orphanRemoval: false
    lifecycleCallbacks: {  }
