Wizacha\Discuss\Internal\Entity\DiscussionUser:
    type: entity
    table: discuss_discussion_user
    id:
        id:
            type: integer
            scale: 0
            length: null
            unique: false
            nullable: false
            precision: 0
            id: true
            generator:
                strategy: IDENTITY
    fields:
        user_id:
            type: integer
            scale: 0
            length: null
            unique: false
            nullable: false
            precision: 0
        status:
            type: string
            scale: 0
            length: 1
            unique: false
            nullable: false
            precision: 0
        is_initiator:
            type: boolean
            scale: 0
            length: null
            unique: false
            nullable: false
            precision: 0
    manyToOne:
        discussion:
            targetEntity: Wizacha\Discuss\Entity\Discussion
            cascade: {  }
            fetch: LAZY
            mappedBy: null
            inversedBy: users
            joinColumns:
                discussion_id:
                    referencedColumnName: id
            orphanRemoval: false
    lifecycleCallbacks: {  }
