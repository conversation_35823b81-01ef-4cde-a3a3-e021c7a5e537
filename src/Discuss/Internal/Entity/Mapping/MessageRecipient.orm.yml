Wizacha\Discuss\Internal\Entity\MessageRecipient:
    type: entity
    table: discuss_message_recipient
    id:
        id:
            type: integer
            scale: 0
            length: null
            unique: false
            nullable: false
            precision: 0
            id: true
            generator:
                strategy: IDENTITY
    fields:
        user_id:
            type: integer
            scale: 0
            length: null
            unique: false
            nullable: false
            precision: 0
        read_date:
            type: datetime
            scale: 0
            length: null
            unique: false
            nullable: true
            precision: 0
    manyToOne:
        message:
            targetEntity: Wizacha\Discuss\Entity\Message
            cascade: {  }
            fetch: LAZY
            mappedBy: null
            inversedBy: recipients
            joinColumns:
                message_id:
                    referencedColumnName: id
            orphanRemoval: false
    lifecycleCallbacks: {  }
