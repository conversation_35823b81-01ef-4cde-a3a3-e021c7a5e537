<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Discuss;

use Doctrine\ORM\EntityManager;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Routing\RouterInterface;
use Wizacha\AppBundle\Controller\Api\Exception\MissingFieldsHttpException;
use Wizacha\AppBundle\Security\User\ApiSecurityUser;
use Wizacha\Cache\RedisDecorator;
use Wizacha\Discuss\Entity\DiscussionInterface;
use Wizacha\Discuss\Event\DiscussMessagePosted;
use Wizacha\Discuss\Event\MessageEvent;
use Wizacha\Discuss\Exception\CompanyHasNoAdministrator;
use Wizacha\Discuss\Exception\SenderIsAlsoRecipient;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Entities\Declination;
use Wizacha\Marketplace\Exception\CompanyNotFound;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Exception\ProductNotFound;
use Wizacha\Marketplace\Exception\Unauthorized;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct;
use Wizacha\Marketplace\User\Exception\InvalidFieldException;
use Wizacha\Marketplace\User\UserRepository;
use Wizacha\Marketplace\User\UserType;
use Wizacha\Status;
use Wizacha\User;
use Wizacha\Marketplace\User\User as MarketplaceUser;

class DiscussService implements EventSubscriberInterface
{
    private const GET_DISCUSS_COUNT_CACHE_KEY = 'GET_DISCUSS_COUNT_CACHE_KEY';
    private const GET_DISCUSS_COUNT_CACHE_TIMEOUT_SECONDS = 3600;

    private Client $discuss;
    private EventDispatcherInterface $eventDispatcher;
    private UserRepository $userRepository;
    private RouterInterface $router;
    private RedisDecorator $redis;
    private OrderService $orderService;
    private CompanyService $companyService;

    public function __construct(
        EventDispatcherInterface $eventDispatcher,
        UserRepository $userRepository,
        EntityManager $entityManager,
        RedisDecorator $redis,
        OrderService $orderService,
        CompanyService $companyService
    ) {
        $this->discuss = new Client(['event_dispatcher' => $eventDispatcher], $entityManager);
        $this->eventDispatcher = $eventDispatcher;
        $this->userRepository = $userRepository;
        $this->redis = $redis;
        $this->orderService = $orderService;
        $this->companyService = $companyService;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            DiscussEvents::MESSAGE_NEW  => ['onNewMessage', 0],
        ];
    }

    /**
     * Send an email when a message is created.
     */
    public function onNewMessage(MessageEvent $event)
    {
        //Todo: Use helper from discuss to get all users
        $users_discussion = [
            $event->getMessage()->getDiscussion()->getInitiator(),
            $event->getMessage()->getDiscussion()->getRecipient(),
        ];
        $message_recipient = array_diff($users_discussion, [$event->getMessage()->getAuthor()]);

        foreach ($message_recipient as $recipient) {
            //Check if email must be send for user.
            if (!(new User($recipient))->getNotificationsSetting()) {
                continue;
            }

            $user = $this->userRepository->get((int) $recipient);
            $this->eventDispatcher->dispatch(
                new DiscussMessagePosted($user, $event->getMessage()),
                DiscussMessagePosted::class
            );
        }
    }

    public function getDiscussClient(): Client
    {
        return $this->discuss;
    }

    /**
     * @throws SenderIsAlsoRecipient
     * @throws CompanyHasNoAdministrator
     * @throws CompanyNotFound
     */
    public function createDiscussionWithCompany(int $userInitiatorId, int $companyId): DiscussionInterface
    {
        $company = fn_get_company_data($companyId);

        if (!isset($company['company'])) {
            throw CompanyNotFound::fromCompanyId($companyId);
        }

        $userId = $this->getCompanyFirstAdmin($companyId);

        if ($userId === $userInitiatorId) {
            throw new SenderIsAlsoRecipient();
        }

        $title = __('discuss_title_company', ['[company]' => $company['company']]);

        $discussion = $this->discuss->getDiscussionRepository()->create();
        $discussion->setInitiator($userInitiatorId);
        $discussion->setRecipient($userId);
        $discussion->setMetaData('title', $title);
        $discussion->setMetaData('company_id', $companyId);

        return $discussion;
    }

    /**
     * @throws SenderIsAlsoRecipient
     * @throws CompanyHasNoAdministrator
     * @throws NotFound
     */
    public function createDiscussionWithProductSeller(int $userInitiatorId, int $productId): DiscussionInterface
    {
        //determine recipient id
        $product = fn_get_product_data($productId, $auth = []);

        if (!$product) {
            throw new ProductNotFound($productId);
        }

        $userId = $this->getCompanyFirstAdmin($product['company_id']);

        if ($userId === $userInitiatorId) {
            throw new SenderIsAlsoRecipient();
        }

        $title = __('discuss_title_about_product', ['[product]' => $product['product']]);

        $discussion = $this->discuss->getDiscussionRepository()->create();
        $discussion->setInitiator($userInitiatorId);
        $discussion->setRecipient($userId);
        $discussion->setMetaData('title', $title);
        $discussion->setMetaData('product_id', $productId);
        $discussion->setMetaData('company_id', $product['company_id']);

        return $discussion;
    }

    public function createDiscussionWithCustomer(ApiSecurityUser $user, int $customerId): DiscussionInterface
    {
        $vendor = $this->userRepository->get($user->getId());

        $customer = $this->userRepository->get($customerId);

        $this->assertUserIsAuthorizedToStartDiscussionWithCustomer($user, $customer, $vendor->getCompanyId());

        $title = 'Contact ' . $customer->getFirstname() . ' ' . $customer->getLastname();

        $discussion = $this->discuss->getDiscussionRepository()->create();
        $discussion->setInitiator($user->getId());
        $discussion->setRecipient($customerId);
        $discussion->setMetaData('title', $title);
        $discussion->setMetaData('company_id', $vendor->getCompanyId());
        $discussion->setMetaData('customer_id', $customerId);

        return $discussion;
    }

    public function createDiscussionFromOrderWithCompany(ApiSecurityUser $userInitiator, int $orderId, int $companyId): DiscussionInterface
    {
        $order = $this->orderService->getOrder($orderId);
        $this->assertStartDiscussionFromOrder($order);

        // check if companyId exist
        $this->companyService->get($companyId);

        $userId = $this->getCompanyFirstAdmin($companyId);

        if ($userId === $userInitiator->getId()) {
            throw new SenderIsAlsoRecipient();
        }

        if ($order->getUserId() !== $userInitiator->getId()) {
            throw new \InvalidArgumentException('User is not related to this order.');
        }

        if ($order->getCompanyId() !== $companyId) {
            throw new \InvalidArgumentException('CompanyId does not match the order one.');
        }

        $title = __('discuss_title_order', ['[orderId]' => $orderId]);

        $discussion = $this->discuss->getDiscussionRepository()->create();
        $discussion
            ->setInitiator($userInitiator->getId())
            ->setRecipient($userId)
            ->setMetaData('title', $title)
            ->setMetaData('company_id', $companyId)
            ->setMetaData('order_id', $orderId)
        ;

        return $discussion;
    }

    public function createDiscussionFromOrderWithCustomer(ApiSecurityUser $userInitiator, int $orderId, int $customerId): DiscussionInterface
    {
        $order = $this->orderService->getOrder($orderId);
        $this->assertStartDiscussionFromOrder($order);
        $vendor = $this->userRepository->get($userInitiator->getId());
        $customer = $this->userRepository->get($customerId);

        if ($order->getCompanyId() !== $vendor->getCompanyId()) {
            throw new \InvalidArgumentException('Order must be owned by the initiator.');
        }

        $this->assertUserIsAuthorizedToStartDiscussionWithCustomer($userInitiator, $customer, $vendor->getCompanyId());

        $title = __('discuss_title_order', ['[orderId]' => $orderId]);

        $discussion = $this->discuss->getDiscussionRepository()->create();
        $discussion
            ->setInitiator($userInitiator->getId())
            ->setRecipient($customerId)
            ->setMetaData('title', $title)
            ->setMetaData('order_id', $orderId)
            ->setMetaData('company_id', $vendor->getCompanyId())
            ->setMetaData('customer_id', $customerId)
        ;

        return $discussion;
    }

    public function setRouter(RouterInterface $router)
    {
        $this->router = $router;
    }

    private function getCompanyFirstAdmin($companyId): int
    {
        $auth = [];

        $userParams = [
            'company_id' => $companyId,
            'status' => Status::ENABLED
        ];
        $companyUsers = fn_get_users($userParams, $auth)[0] ?? [];

        if (!$companyUsers) {
            throw new CompanyHasNoAdministrator((int) $companyId);
        }

        return (int) reset($companyUsers)['user_id'];
    }

    /**
     * @return int Number of unread messages in all discussions concerning the user
     */
    public function getDiscussCount(int $userId): int
    {
        $userInfo = fn_get_user_short_info($userId);

        // If user is admin, get unreadCount on himself
        if ($userInfo['user_type'] === 'A') {
            return $this->discuss->getMessageRepository()->getUnreadCount($userId);
        }

        // If user is vendor get unreadCount on his company
        if ($userInfo['user_type'] === 'V') {
            return $this->discuss->getMessageRepository()->getUnreadCount($userId, null, (int) $userInfo['company_id']);
        }

        return $this->redis->get(
            self::GET_DISCUSS_COUNT_CACHE_KEY . ":$userId",
            function () {
                $discussions = $this->discuss->getDiscussionRepository()->getAll();

                $total = 0;
                foreach ($discussions as $discussion) {
                    $recipientId = $discussion->getRecipient();
                    $totalItems = $this->discuss
                        ->getMessageRepository()
                        ->getUnreadCount($recipientId, $discussion->getId());
                    $total +=  $totalItems;
                }

                return (int) $total;
            },
            self::GET_DISCUSS_COUNT_CACHE_TIMEOUT_SECONDS
        );
    }

    private function assertUserIsAuthorizedToStartDiscussionWithCustomer(
        ApiSecurityUser $user,
        MarketplaceUser $customer,
        ?int $companyId
    ): void {
        if (\in_array('ROLE_VENDOR', $user->getRoles()) === false) {
            throw Unauthorized::fromId('user', $user->getId(), 'only a vendor authenticated can start a discussion with a customer.');
        }

        if ($customer->getUserType() !== UserType::CLIENT()->getValue()) {
            throw new InvalidFieldException('userId', 'userId must belong to a Customer.');
        }

        // Check that the user has already placed an order with this vendor
        if (!$this->orderService->isUserHaveOrderFromCompany($customer->getUserId(), $companyId)) {
            throw Unauthorized::fromId('user', $customer->getUserId(), 'must have made a purchase to the company.');
        }
    }

    private function assertStartDiscussionFromOrder(Order $order): void
    {
        if ($order->getStatus()->equals(OrderStatus::INCOMPLETED()) === true) {
            throw new \InvalidArgumentException("Couldn't start a discussion from an incomplete order.");
        }

        // check if there is a discussion created from an order
        if ($this->discuss->getDiscussionRepository()->getByOrderId($order->getId()) instanceof DiscussionInterface === true) {
            throw new \InvalidArgumentException('A discussion linked to this orderId already exists.');
        }
    }

    public function createDiscussionFilteredByParams(array $params, ApiSecurityUser $user): DiscussionInterface
    {
        if (\array_key_exists('orderId', $params) === true && \array_key_exists('companyId', $params) === true) {
            return $this->createDiscussionFromOrderWithCompany($user, \intval($params['orderId']), \intval($params['companyId']));
        } elseif (\array_key_exists('orderId', $params) === true && \array_key_exists('userId', $params) === true) {
            return $this->createDiscussionFromOrderWithCustomer($user, \intval($params['orderId']), \intval($params['userId']));
        } elseif (\array_key_exists('companyId', $params) === true) {
            return $this->createDiscussionWithCompany($user->getId(), (int) $params['companyId']);
        } elseif (\array_key_exists('declinationId', $params) === true) {
            // Only a declination ID was provided, so we extract the product ID from it.
            // The discussion will NOT be about the declination, but about the associated product.
            $productId = Declination::fromId($params['declinationId'])->getProductId();
            return $this->createDiscussionWithProductSeller($user->getId(), $productId);
        } elseif (\array_key_exists('productId', $params) === true) {
            if (MultiVendorProduct::isMultiVendorProductId($params['productId'])) {
                throw new InvalidFieldException('productId', 'You must provide a product id, not a multi vendor product id.');
            }

            return $this->createDiscussionWithProductSeller($user->getId(), (int) $params['productId']);
        } elseif (\array_key_exists('userId', $params) === true) {
            return $this->createDiscussionWithCustomer($user, (int) $params['userId']);
        } else {
            throw new MissingFieldsHttpException('productId', 'declinationId', 'companyId', 'userId');
        }
    }
}
