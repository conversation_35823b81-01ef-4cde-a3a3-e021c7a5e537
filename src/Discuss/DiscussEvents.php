<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Discuss;

/**
 * Class DiscussEvents
 * List all events sent by <PERSON>uss
 * @package Wizacha\Discuss
 */
final class DiscussEvents
{
    /**
     * Triggered when a new message is recorded, with an instance of
     * \Wizacha\Discuss\Event\MessageEvent
     */
    public const MESSAGE_NEW   = 'message.new';
}
