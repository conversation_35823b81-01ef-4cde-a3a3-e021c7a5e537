Wizacha\Discuss\Entity\Discussion:
    type: entity
    table: discuss_discussion
    id:
        id:
            type: integer
            scale: 0
            length: null
            unique: false
            nullable: false
            precision: 0
            id: true
            generator:
                strategy: IDENTITY
    fields:
        open:
            type: boolean
            scale: 0
            length: null
            unique: false
            nullable: false
            precision: 0
    oneToMany:
        users:
            targetEntity: Wizacha\Discuss\Internal\Entity\DiscussionUser
            cascade:
                - all
            fetch: LAZY
            mappedBy: discussion
            inversedBy: null
            orphanRemoval: false
            indexBy: user_id
            orderBy: null
        meta_data:
            targetEntity: Wizacha\Discuss\Internal\Entity\MetaData
            cascade:
                - all
            fetch: LAZY
            mappedBy: discussion
            inversedBy: null
            indexBy: name
            orphanRemoval: false
            orderBy: null
        messages:
            targetEntity: Wizacha\Discuss\Entity\Message
            cascade: {  }
            fetch: LAZY
            mappedBy: discussion
            inversedBy: null
            orphanRemoval: false
            orderBy: null
    lifecycleCallbacks: {  }
