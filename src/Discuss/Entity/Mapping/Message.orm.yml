Wizacha\Discuss\Entity\Message:
    type: entity
    table: discuss_message
    id:
        id:
            type: integer
            scale: 0
            length: null
            unique: false
            nullable: false
            precision: 0
            id: true
            generator:
                strategy: IDENTITY
    fields:
        author:
            type: integer
            scale: 0
            length: null
            unique: false
            nullable: false
            precision: 0
        send_date:
            type: datetime
            scale: 0
            length: null
            unique: false
            nullable: false
            precision: 0
        content:
            type: text
            scale: 0
            length: null
            unique: false
            nullable: false
            precision: 0
    manyToOne:
        discussion:
            targetEntity: Wizacha\Discuss\Entity\Discussion
            cascade:
                - all
            fetch: LAZY
            mappedBy: null
            inversedBy: messages
            joinColumns:
                discussion_id:
                    referencedColumnName: id
            orphanRemoval: false
    oneToMany:
        recipients:
            targetEntity: Wizacha\Discuss\Internal\Entity\MessageRecipient
            cascade:
                - all
            fetch: LAZY
            mappedBy: message
            inversedBy: null
            orphanRemoval: false
            orderBy: null
    lifecycleCallbacks: {  }
