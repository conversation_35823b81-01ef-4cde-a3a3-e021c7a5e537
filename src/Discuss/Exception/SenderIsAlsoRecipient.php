<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Discuss\Exception;

use Wizacha\AppBundle\Controller\Api\Error\ApiErrorResponseProvider;
use Wizacha\Marketplace\Exception\ErrorCode;
use Wizacha\AppBundle\Controller\Api\Error\ErrorResponse;

class SenderIsAlsoRecipient extends \Exception implements ApiErrorResponseProvider
{
    public function toApiErrorResponse(): ErrorResponse
    {
        return new ErrorResponse(ErrorCode::SENDER_IS_ALSO_RECIPIENT(), 'message\'s sender is also recipient');
    }
}
