<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Discuss\Exception;

use Symfony\Component\HttpFoundation\Response;
use Wizacha\AppBundle\Controller\Api\Error\ApiErrorResponseProvider;
use Wizacha\Marketplace\Exception\ErrorCode;
use Wizacha\AppBundle\Controller\Api\Error\ErrorResponse;

class CompanyHasNoAdministrator extends \Exception implements ApiErrorResponseProvider
{
    /** @var int */
    private $companyId;

    public function __construct(int $companyId)
    {
        parent::__construct('company does not have any administrator');
        $this->companyId = $companyId;
    }

    public function toApiErrorResponse(): ErrorResponse
    {
        return new ErrorResponse(
            ErrorCode::COMPANY_HAS_NO_ADMINISTRATOR(),
            'company does not have any administrator',
            [
                'companyId' => $this->companyId,
            ],
            Response::HTTP_INTERNAL_SERVER_ERROR
        );
    }
}
