<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\Discuss\Event;

use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Contracts\EventDispatcher\Event;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilder;
use Wizacha\Component\Notification\NotificationEvent;
use Wizacha\Discuss\Entity\Discussion;
use Wizacha\Discuss\Entity\Message;
use Wizacha\Discuss\Entity\MessageInterface;
use Wizacha\Marketplace\User\User;
use Wizacha\Registry;

class DiscussMessagePosted extends Event implements NotificationEvent
{
    /**
     * @var User
     */
    private $user;

    /**
     * @var MessageInterface
     */
    private $message;

    public function __construct(User $user, MessageInterface $message)
    {
        $this->user = $user;
        $this->message = $message;
    }

    public static function buildDebugForm(FormBuilder $form)
    {
        $form->add('user', EntityType::class, [
            'class' => User::class,
            'choice_label' => 'email',
        ]);
    }

    public static function createFromForm(Form $form)
    {
        $user = $form->getData()['user'];
        // Get User with its repository rather than Doctrine only to ensure address is filled
        $user = Registry::defaultInstance()->container->get('marketplace.user.user_repository')->get($user->getUserId());

        $fakeMessage = new Message();
        $fakeMessage->setDiscussion(new Discussion());

        return new self($user, $fakeMessage);
    }

    public static function getDescription(): string
    {
        return 'discuss_message_posted';
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function getMessage(): MessageInterface
    {
        return $this->message;
    }
}
