<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\Discuss\Event;

use Symfony\Component\EventDispatcher\Event;
use Wizacha\Discuss\Entity\MessageInterface;

/**
 * Class MessageEvent
 * @package Wizacha\Discuss\Event
 */
class MessageEvent extends Event
{
    /**
     * @var MessageInterface
     */
    protected $_msg;

    /**
     * @param MessageInterface $msg
     */
    public function __construct(MessageInterface $msg)
    {
        $this->_msg = $msg;
    }

    /**
     * @return MessageInterface
     */
    public function getMessage()
    {
        return $this->_msg;
    }
}
