<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha;

use Psr\Log\LoggerInterface;
use Wizacha\ActionLogger\ActionLoggerActionType;
use Wizacha\ActionLogger\ActionLoggerLevel;
use Wizacha\AppBundle\Security\User\UserService;

class ActionLogger
{
    private LoggerInterface $logger;
    private UserService $userService;
    private string $objectClass;

    public function __construct(string $objectClass, LoggerInterface $logger, UserService $userService)
    {
        $this->objectClass = $objectClass;
        $this->logger = $logger;
        $this->userService = $userService;
    }

    public function log(
        ActionLoggerActionType $actionType,
        string $objectId,
        ActionLoggerLevel $level = null,
        array $additionalContext = []
    ): void {
        $this->logger->log(
            ($level ?? ActionLoggerLevel::INFO())->getValue(),
            $actionType->getValue(),
            [
                'objectClass' => $this->objectClass,
                'objectId' => $objectId,
                'userId' => $this->userService->getCurrentUserId(),
                'additionalContext' => $additionalContext
            ]
        );
    }
}
