<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha;

use Tygh\Database;
use Wizacha\Component\Locale\Locale;
use Wizacha\Core\Hash\Hash;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Entities\Declination;
use Wizacha\Marketplace\Entities\Image;
use Wizacha\Marketplace\Entities\ProductShippingRate;
use Wizacha\Marketplace\Entities\Tax;
use Wizacha\Marketplace\Exception\CategoryNotFound;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Image\ImageLinkType;
use Wizacha\Marketplace\PIM\TransactionMode\TransactionMode;
use Wizacha\Marketplace\SeoData;
use Wizacha\Marketplace\Shipping\DeliveryType;
use Wizacha\Money\Money;

/**
 * @deprecated
 * - For PIM: use Wizacha\Marketplace\PIM\Product\Product
 * - For catalog: use Wizacha\Marketplace\ReadModel\Product
 * @see \Wizacha\Marketplace\PIM\Product\Product
 * @see \Wizacha\Marketplace\ReadModel\Product
 */
class Product
{
    public const EVENT_CREATE = 'product.create';
    public const EVENT_UPDATE = 'product.update';
    public const EVENT_DELETE = 'product.delete';
    public const EVENT_VIEWED = 'product.viewed';

    public const SUFFIX_LENGTH = 4;
    public const EAN_MAX_LENGTH = 128;

    public const CONDITION_NEW         = 'N';
    public const CONDITION_USED        = 'U';
    public const CONDITION_DB_FIELD    = 'w_condition';
    public const CONDITION_API_NAME    = 'condition';

    public const METADATA_KEY_FREE_FEATURES = '_priv_free_features';

    public const FIELD_FREE_FEATURES = 'free_features';
    public const FIELD_GEOLOCATION = 'geolocation';

    public const OBJECT_TYPE   = 'product';

    public const TRACKING_TYPE_CLASSICAL = 'B';
    public const TRACKING_TYPE_INVENTORY = 'O';

    public const MAX_PRICE_ADJUSTMENT = 'max_price_adjustment';

    /**
     * @var integer
     */
    protected $product_id = 0;

    /**
     * @var array
     */
    private $data = [];

    /**
     * @var array
     */
    protected $getterData = [];

    /**
     * @var array
     */
    protected $metadata = [];

    /**
     * @var array
     */
    protected $cscartData;

    /** @var Locale */
    protected $defaultLocale;

    /**
     * @param integer $product_id
     *
     * @throws \InvalidArgumentException
     */
    public function __construct($product_id, bool $preloadData = false, Locale $locale = null)
    {
        $this->defaultLocale = $locale ?? GlobalState::contentLocale();

        if (!is_numeric($product_id)) {
            throw new \InvalidArgumentException('Integer expected');
        }
        $this->product_id = \intval($product_id);

        if ($preloadData) {
            $this->getData();
        }
    }

    /**
     * @param float $lat
     * @param float $lng
     * @param string $label
     * @param string $postal
     * @return $this
     */
    public function setGeoloc($lat, $lng, $label, $postal)
    {
        return $this->setMetadata('_priv_geoloc', [\floatval($lat), \floatval($lng), (string) $label, (string) $postal]);
    }

    /**
     * Returns 3 elements:
     * * float latitude
     * * float longitude
     * * string label
     * * string postal
     */
    public function getGeoloc(): array
    {
        $geoloc = $this->getMetadata('_priv_geoloc');
        if (!\is_array($geoloc) || !array_filter($geoloc)) {
            return [null, null, null, null];
        }
        return $geoloc;
    }

    /**
     * @param array       $datas
     * @param Locale|null $locale
     *
     * @return $this
     */
    public function setFreeFeatures(array $datas, Locale $locale = null)
    {
        $datas = array_map(
            function ($value) {
                return \is_array($value) ? $value : [$value];
            },
            $datas
        );

        return $this->setMetadata(self::METADATA_KEY_FREE_FEATURES, $datas, $locale);
    }

    public function getFreeFeatures(Locale $locale = null): array
    {
        $freeFeatures = $this->getMetadata(self::METADATA_KEY_FREE_FEATURES, $locale ?? $this->defaultLocale);

        if (!\is_array($freeFeatures)) {
            return [];
        }

        return $freeFeatures;
    }

    public function getProductCode(): string
    {
        return $this->getData()['product_code'] ?: '';
    }

    /**
     * @param string      $type
     * @param Locale|null $locale
     *
     * @return mixed
     */
    public function getMetadata($type, Locale $locale = null)
    {
        if (false === \array_key_exists($type, $this->metadata)
            || (            null !== $locale
            && false === $locale->equals($this->defaultLocale))
        ) {
            $serialized_data = Database::getField(
                'SELECT metadata FROM ?:product_metadata WHERE product_id=?s AND metadata_type=?s AND lang_code=?s;',
                $this->product_id,
                $type,
                (string) ($locale ?? $this->defaultLocale)
            );
            if (!\is_null($serialized_data)) {
                $data = unserialize($serialized_data);
                if ($data === false && $serialized_data != serialize(false)) {
                    // see http://php.net/manual/en/function.unserialize.php#refsect1-function.unserialize-notes
                    $data = null;
                }
            } else {
                $data = null;
            }
            $this->metadata[$type] = $data;
        }
        return $this->metadata[$type];
    }

    /**
     * @param string      $type
     * @param mixed       $value (must be serializable)
     * @param Locale|null $locale
     *
     * @return $this
     */
    public function setMetadata($type, $value, Locale $locale = null)
    {
        $this->metadata[$type] = $value;
        Database::query(
            "REPLACE ?:product_metadata
                   SET product_id = ?s, metadata_type = ?s, metadata=?s, lang_code = ?s",
            $this->product_id,
            $type,
            serialize($this->metadata[$type]),
            (string) ($locale ?? $this->defaultLocale)
        );

        $this->productManagerInvalidate();

        return $this;
    }

    /**
     * @param mixed $data
     * @return $this
     */
    public function setData($data)
    {
        $this->data = (array) $data;
        return $this;
    }

    /**
     * TODO move that into controllers
     * @param \Symfony\Component\HttpFoundation\Session\Session $session
     * @return bool|static
     * @throws \Exception
     */
    public static function createFromSession(\Symfony\Component\HttpFoundation\Session\Session $session)
    {
        $data = $session->get('create_product_form', []);
        if (!$data) {
            return false;
        }

        $auth = $session->get('auth');
        if ($auth['user_type'] == 'A') {
            fn_set_notification('E', __('error'), __('error_admin_cannot_create_c2c_product'));
            return false;
        }

        $productCreator = Registry::defaultInstance()->container->get('marketplace.pim.product_creator');
        $product = $productCreator->createProductForC2C($data);

        $session->remove('create_product_form');
        fn_set_notification('N', __('notice'), __('w_c2c_product_updated'));

        return $product;
    }

    public function getAvailabilityDate(): ?\DateTime
    {
        if (!$this->getData()['avail_since'] || !\intval($this->getData()['avail_since'])) {
            return null;
        }
        $date = new \DateTime();
        $date->setTimestamp($this->getData()['avail_since']);
        return $date;
    }

    public function fillFormArray()
    {

        $return = [];
        $raw_data = $this->fillCscartData();
        fn_gather_additional_product_data($raw_data, false, true, false, false, true);
        $shippings = \Wizacha\Shipping::getByType(\Wizacha\Shipping::TYPE_C2C);
        $geoloc = $this->getGeoloc();

        foreach ($shippings as $shipping) {
            if (!$raw_data['shippings'][$shipping['shipping_id']]['rates'][0]['rate_value']) {
                continue;
            }
            if (Shipping::isHandDelivery($shipping['shipping_id'])) {
                $hand_shipping = &$raw_data['shippings'][$shipping['shipping_id']]['rates'][0]['rate_value']['I'][0];
                $return['shipping_handDelivery'] = true;
                $return['shipping_handDelivery_label'] = $hand_shipping['location'];
                $return['shipping_handDelivery_lat'] = $hand_shipping['latitude'];
                $return['shipping_handDelivery_lng'] = $hand_shipping['longitude'];
                $return['shipping_handDelivery_postal'] = $hand_shipping['postal'];
            }

            if ($shipping['w_delivery_type'] == (string) DeliveryType::STANDARD()) {
                $std_shipping = &$raw_data['shippings'][$shipping['shipping_id']]['rates'][0]['rate_value']['I'][0];
                $return['shipping_standardDelivery'] = true;
                $return['shipping_standardDelivery_price'] = $std_shipping['value'];
            }
        }
        $filterDescription = function ($description) {
            if (!$description) {
                return '';
            }

            $description = str_replace('&nbsp;', ' ', $description);
            $description = str_replace(['<br>', '<br />', '<br/>'], PHP_EOL, $description);
            $description = html_entity_decode($description);
            return strip_tags($description);
        };
        $return += [
            'category_id' => $this->getCategory()->getId(),
            'w_condition' => $raw_data['w_condition'],
            'product' => $this->getName(),
            'product_id' => $this->product_id,
            'price' => $raw_data['price'],
            'crossed_out_price' => $raw_data['crossed_out_price'],
            'amount' => $raw_data['amount'],
            'full_description' => $filterDescription($raw_data['full_description']),
            'short_description' => $filterDescription($raw_data['short_description']),

            'session_images' => array_merge([$raw_data['main_pair']['detailed_id']], $raw_data['image_pairs'] ? array_column($raw_data['image_pairs'], 'detailed_id') : []),
            'features' => $raw_data['product_features'],
            'geoloc_lat' => $geoloc[0],
            'geoloc_lng' => $geoloc[1],
            'geoloc_postal' => $geoloc[2],
            'geoloc_label' => $geoloc[3],
        ];
        return $return;
    }

    /**
     * Return only main category
     * @return Category
     * @throws CategoryNotFound
     */
    public function getCategory()
    {
        try {
            return new Category(Database::getField("SELECT category_id FROM ?:products_categories WHERE product_id = ?i AND link_type = 'M'", $this->product_id));
        } catch (\Exception $exception) {
            throw new CategoryNotFound("Cannot create Category for Product [" . $this->product_id . "]", 0, $exception);
        }
    }

    /**
     * Set the main category
     * @param Category $category
     * @return $this
     */
    public function setCategory(Category $category)
    {
        Database::query(
            "UPDATE
                ?:products_categories
            SET
                category_id = ?i
            WHERE
                product_id = ?i
                AND link_type = 'M'
            ",
            $category->getId(),
            $this->product_id
        );

        $this->productManagerInvalidate();

        return $this;
    }

    public function getId(): int
    {
        return $this->product_id;
    }

    public function getMinPrice(): float
    {
        return \floatval($this->getData()['price']);
    }

    public function getSeoData(): SeoData
    {
        return new SeoData(
            (string) $this->getCscartData()['page_title'],
            (string) $this->getCscartData()['meta_description'],
            (string) $this->getCscartData()['meta_keywords'],
            (string) $this->getCscartData()['seo_name']
        );
    }

    /**
     * @return string
     */
    public function getName()
    {
        return $this->getData()['product'];
    }

    public function getMainImage(): ?\Wizacha\Marketplace\Image\Image
    {
        $images = $this->getAllImages();

        return empty($images) ? null : reset($images);
    }

    /**
     * @param int $width
     * @param int $height
     * @deprecated Use getMainImage()
     * @see \Wizacha\Product::getMainImage
     */
    public function getMainImageUrl($width = 0, $height = 0): ?string
    {
        $image = fn_image_to_display(
            fn_get_image_pairs($this->product_id, 'product', 'M'),
            $width,
            $height
        );

        if (empty($image)) {
            $video = Registry::defaultInstance()->container->get('marketplace.pim.video_service')->findOneByProductId($this->product_id);
            if ($video !== null) {
                return fn_generate_thumbnail('http:' . $video->getThumbLink(), $width, $height);
            }
        }

        return isset($image['image_path']) ? $image['image_path'] : null;
    }

    /**
     * @param string $protocol see fn_url for valid protocols
     * @return string
     */
    public function getFrontUrl($protocol = 'https')
    {
        return fn_url(
            'products.view?product_id=' . $this->product_id,
            'C',
            $protocol
        );
    }

    public function getStatus()
    {
        return $this->getData()['status'];
    }

    public function getWeight(): float
    {
        return $this->getData()['weight'] ?? .0;
    }

    public function hasMaxPriceAdjustment(): bool
    {
        return \is_numeric($this->getData()[static::MAX_PRICE_ADJUSTMENT]) && $this->getData()[static::MAX_PRICE_ADJUSTMENT] > 0;
    }

    /**
     * @return $this
     */
    public function save()
    {
        if (!$this->data) {
            return $this;
        }

        $this->product_id = fn_update_product(
            $this->data,
            $this->product_id,
            (string) $this->defaultLocale
        );

        if (!empty($this->data[$this::FIELD_FREE_FEATURES])) {
            $this->setFreeFeatures($this->data[$this::FIELD_FREE_FEATURES]);
        }

        if (!empty($this->data[$this::FIELD_GEOLOCATION])) {
            $this->setGeoloc(
                $this->data[$this::FIELD_GEOLOCATION]['latitude'] ?? 0,
                $this->data[$this::FIELD_GEOLOCATION]['longitude'] ?? 0,
                $this->data[$this::FIELD_GEOLOCATION]['label'] ?? '',
                $this->data[$this::FIELD_GEOLOCATION]['zipcode'] ?? ''
            );
        }

        return $this;
    }

    /**
     * Load data from database if not already done
     * @return array
     */
    protected function getData(): array
    {
        $fallbackLocale = GlobalState::fallbackLocale();

        if (!$this->getterData) {
            $priceOperator = (container()->getParameter('feature.catalog.show_zero_price_products') === true) ? '>=' : '>';

            $this->getterData = \Tygh\Database::getRow(
                "SELECT IF(p.tracking = ?s, pp.price, poi.w_price) as price,
                    IF(COALESCE(pd.product, '') = '', pdfl.product, pd.product) as product,
                    p.product_code as product_code,
                    p.approved as approved,
                    p.avail_since as avail_since,
                    p.tracking as tracking,
                    p.status as status,
                    IF(COALESCE(pd.short_description, '') = '', pdfl.short_description, pd.short_description) as short_description,
                    IF(COALESCE(pd.full_description, '') = '', pdfl.full_description, pd.full_description) as full_description,
                    p.amount as amount,
                    p.w_condition as w_condition,
                    p.company_id as company_id,
                    p.timestamp as timestamp,
                    p.transaction_mode,
                    p.w_supplier_ref,
                    p.weight,
                    p.infinite_stock,
                    p.product_template_type,
                    p.max_price_adjustment,
                    p.is_subscription,
                    p.is_renewable,
                    p.is_edp,
                    p.quote_requests_min_quantity,
                    p.is_exclusive_to_quote_requests,
                    poi.supplier_reference
                 FROM ?:products as p
                  LEFT JOIN ?:product_prices as pp ON pp.product_id = p.product_id
                  LEFT JOIN ?:product_options_inventory as poi ON poi.product_id = p.product_id
                  LEFT JOIN ?:product_descriptions as pd ON pd.product_id = p.product_id AND pd.lang_code = ?s
                  LEFT JOIN ?:product_descriptions as pdfl ON pdfl.product_id = p.product_id AND pdfl.lang_code = ?s
                 WHERE p.product_id = ?i
                  AND IF(p.tracking = ?s,(poi.w_price " . $priceOperator . " 0 AND poi.amount > 0),TRUE)
                 ORDER BY poi.w_price ASC",
                self::TRACKING_TYPE_CLASSICAL,
                (string) $this->defaultLocale,
                $fallbackLocale,
                $this->product_id,
                self::TRACKING_TYPE_INVENTORY
            );

            if (!empty($this->getterData)) {
                return $this->getterData;
            }

            // If no declination data was found it means all declinations are un-sellable
            // we remove the filter and get data from those declinations anyway
            $this->getterData = \Tygh\Database::getRow(
                "SELECT IF(p.tracking = ?s, pp.price, poi.w_price) as price,
                    IF(COALESCE(pd.product, '') = '', pdfl.product, pd.product) as product,
                    p.approved as approved,
                    p.tracking as tracking,
                    p.status as status,
                    IF(COALESCE(pd.short_description, '') = '', pdfl.short_description, pd.short_description) as short_description,
                    IF(COALESCE(pd.full_description, '') = '', pdfl.full_description, pd.full_description) as full_description,
                    p.amount as amount,
                    p.w_condition as w_condition,
                    p.company_id as company_id,
                    p.timestamp as timestamp,
                    p.transaction_mode,
                    p.weight
                 FROM ?:products as p
                  LEFT JOIN ?:product_prices as pp ON pp.product_id = p.product_id
                  LEFT JOIN ?:product_options_inventory as poi ON poi.product_id = p.product_id
                  LEFT JOIN ?:product_descriptions as pd ON pd.product_id = p.product_id AND pd.lang_code = ?s
                  LEFT JOIN ?:product_descriptions as pdfl ON pdfl.product_id = p.product_id AND pdfl.lang_code = ?s
                 WHERE p.product_id = ?i
                 ORDER BY poi.w_price ASC",
                self::TRACKING_TYPE_CLASSICAL,
                (string) $this->defaultLocale,
                $fallbackLocale,
                $this->product_id,
                self::TRACKING_TYPE_INVENTORY
            );
        }
        return $this->getterData;
    }

    /**
     * Change EAN in product's data.
     * @param array $data
     */
    public static function setDataBeforeClonning(&$data)
    {
        $data['product_code'] = self::getNewEAN($data['product_code'], $data['company_id']);
        $data['status'] = \Wizacha\Status::HIDDEN;
    }

    /**
     * if $old_ean = 'toto'
     * Test toto-1 to toto-9999 (depend on suffix_length) and then tot-1 to tot-9999 etc.
     *
     * @param string $old_ean
     * @param integer $company_id
     * @param integer $suffix_length
     * @return string
     */
    public static function getNewEAN($old_ean, $company_id, $suffix_length = self::SUFFIX_LENGTH)
    {
        if (empty($old_ean)) {
            return "";
        }
        //If EAN ending with -number, kept only the part before the -number
        $temp_ean = preg_split("/-[0-9]{1,{$suffix_length}}$/", $old_ean)[0];
        $temp_ean = substr($temp_ean, 0, (self::EAN_MAX_LENGTH - 5));

        $similar_EAN = \Tygh\Database::getColumn(
            "SELECT product_code FROM ?:products WHERE
            company_id = ?i AND
            product_code LIKE ?l",
            $company_id,
            $temp_ean . '%'
        );
        $similar_EAN = array_flip($similar_EAN);
        for ($i = 1; $i < 10 * $suffix_length; ++$i) {
            if (!\array_key_exists($temp_ean . "-$i", $similar_EAN)) {
                return $temp_ean . "-$i";
            }
        }

        return self::getNewEAN(substr($temp_ean, 0, -1), $company_id, $suffix_length);
    }

    /**
     * Copy all information about shippings to new product.
     * @param integer $id
     * @param integer $clone_id
     */
    public static function cloneShippings($id, $clone_id)
    {
        $data = \Tygh\Database::getArray(
            "SELECT shipping_id, destination_id, rate_value
            FROM ?:w_product_shipping_rates
            WHERE product_id=?i",
            $id
        );

        if ($data) {
            array_walk(
                $data,
                function (&$_d) use ($clone_id) {
                    $_d['product_id'] = $clone_id;
                }
            );

            \Tygh\Database::query("INSERT INTO ?:w_product_shipping_rates ?m", $data);
        }
    }

    /**
     * Changed status in $product_data if the product is classical and has no shipping
     * Return false if the status has benn changed
     *
     * @param array $product_data
     * @param integer $product_id
     * @param integer $company_id
     * @return bool
     */
    public static function checkShippingsBeforeUpdate(&$product_data, $product_id, $company_id = 0)
    {
        //New status is not active or the product is downloadable, don't check
        if (self::isExemptShippings($product_data)) {
            return true;
        }

        //On BO, the controller check the shippings but in csv, the can directly create product
        if (!$product_id) {
            //For API and CSV, company_id is not in product_data
            $company_id = $product_data['company_id'] ?: $company_id;
            if (!\Wizacha\Company::hasShipping($company_id)) {
                $product_data['status'] = \Wizacha\Status::HIDDEN;
                fn_set_notification('E', __('error'), __('w_cant_active_product_without_shippings'), '', 'w_invalid_status');
                return false;
            }
            return true;
        }

        //The product is not create but updated

        if (isset($product_data['status']) || isset($product_data['is_edp'])) {
            $auth = isset($_SESSION['auth']) ? $_SESSION['auth'] : [];
            $old_data = fn_get_product_data($product_id, $auth) ?: [];
            $new_data = array_merge($old_data, $product_data);

            //If the future product is downloadable or not displayed:
            if (self::isExemptShippings($new_data)) {
                return true;
            }



            //Switch between downloadable and not downloadable require an specific approval if the product has no shippings
            if ($old_data['is_edp'] == 'Y'
                && \array_key_exists('is_edp', $new_data['shippings'])
            ) {
                fn_set_notification('W', __('warning'), __('w_check_shippings_before_active_product'), '', 'w_check_shippings_before_active_product');
                $product_data['status'] = Status::HIDDEN;
                return false;
            }

            //Check shippings
            if (self::hasActiveShipping($new_data)) {
                return true;
            }

            //If there is no activated shippings :
            $product_data['status'] = Status::HIDDEN;
            fn_set_notification('E', __('error'), __('w_cant_active_product_without_shippings'), '', 'w_invalid_status');
            return false;
        }

        return true;
    }

    /**
     * Check one product. If there is no shipping, hide the product and return false.
     * @param integer $product_id
     * @return bool
     */
    public static function checkStatusAfterChangeShippings($product_id)
    {
        // only necessary product data
        $product_data = db_get_row("SELECT p.product_id, p.status, p.is_edp, p.company_id, GROUP_CONCAT(IF(pc.link_type = 'M', CONCAT(pc.category_id, 'M'), pc.category_id)) AS category_ids FROM ?:products AS p INNER JOIN ?:products_categories AS pc ON p.product_id = pc.product_id WHERE p.product_id = ?i GROUP BY p.product_id", $product_id);

        // parse categories
        list($product_data['category_ids'], $product_data['main_category']) = fn_convert_categories($product_data['category_ids']);

        if (self::isExemptShippings($product_data)) {
            return true;
        }

        // fetch shippings
        fn_w_set_shippings_product_data($product_data);

        if (self::hasActiveShipping($product_data)) {
            return true;
        }

        fn_update_product(['status' => \Wizacha\Status::HIDDEN], $product_id);
        fn_set_notification('W', __('warning'), __('w_change_product_status_on_shippings'), '', 'w_change_product_status_on_shippings');
        return false;
    }

    /**
     * Hide all products without shipping and return the number of affected products
     * @param integer $company_id
     * @return integer
     */
    public static function checkAllProductsShippings($company_id)
    {
        $query = \Tygh\Database::quote(
            "UPDATE ?:products SET status = ?s WHERE is_edp='N' AND status=?s AND company_id = ?i AND ",
            \Wizacha\Status::HIDDEN,
            \Wizacha\Status::ENABLED,
            $company_id
        );
        $allowed_shippings = \Tygh\Database::getField("SELECT shippings FROM ?:companies WHERE company_id=?i", $company_id);
        if (!empty($allowed_shippings)) {
            $condition = array_map(
                function ($id) {
                    return "FIND_IN_SET($id, w_disable_shippings)";
                },
                explode(',', $allowed_shippings)
            );
            $condition = implode(' AND ', $condition);
        } else {
            $condition = '1';
        }

        $query .= $condition;
        $affected = \Tygh\Database::query($query)->rowCount();
        if ($affected) {
            fn_set_notification('W', __('warning'), __('w_products_has_been_hidden'), '', 'w_products_has_been_hidden');
        }

        return $affected;
    }

    /**
     * Return true if product is downloadable or not displayed
     * @param array $product_data
     * @return bool
     */
    public static function isExemptShippings($product_data)
    {
        if ((isset($product_data['status']) && Status::ENABLED != $product_data['status'])
            || (isset($product_data['is_edp']) && 'Y' == $product_data['is_edp'])
        ) {
            return true;
        }
        if (isset($product_data['category_ids'])) {
            $category_id = \is_array($product_data['category_ids']) ?
                reset($product_data['category_ids'])
                : $product_data['category_ids'];
            return !(new Category($category_id))->isTransactional();
        }
        return false;
    }

    /**
     * @param array $product_data
     * @return bool
     */
    public static function hasActiveShipping($product_data)
    {
        if (\is_array($product_data['shippings'])
            && false !== array_search(
                \Wizacha\Status::ENABLED,
                array_column($product_data['shippings'], 'status')
            )
        ) {
            return true;
        }
        return false;
    }

    /**
     * @param array $params
     * @param int $items_per_page
     * @param string|null $lang_code
     * @return array
     */
    public static function getInventory($params = [], $items_per_page = 0, $lang_code = null)
    {
        $lang_code = $lang_code ?? (string) GlobalState::contentLocale();
        $default_params = array (
            'page' => 1,
            'product_id' => 0,
            'items_per_page' => $items_per_page
        );

        $params = array_merge($default_params, $params);

        $limit = '';
        if (!empty($params['items_per_page'])) {
            $params['total_items'] = db_get_field("SELECT COUNT(*) FROM ?:product_options_inventory WHERE product_id = ?i", $params['product_id']);
            $limit = db_paginate($params['page'], $params['items_per_page']);
        }

        $inventory = db_get_array("SELECT * FROM ?:product_options_inventory WHERE product_id = ?i ORDER BY position $limit", $params['product_id']);

        foreach ($inventory as $k => $v) {
            $declination = new Declination(new Product($v['product_id']), $v['combination']);
            $inventory[$k]['combination'] = fn_get_product_options_by_combination($v['combination']);
            $inventory[$k]['combination_string'] = $v['combination'];
            $inventory[$k]['image_pairs'] = fn_get_image_pairs($declination->getId(), 'declinations', 'M', true, true, $lang_code);
            $inventory[$k]['product_price_tiers'] = container()
                ->get('marketplace.price_tier.price_tier_service')
                ->getPriceTiersOfProductOrCombination($v['product_id'], $v['combination'])
            ;
        }

        $product_options = fn_get_product_options($params['product_id'], $lang_code, true, true);
        $product_inventory = db_get_field("SELECT tracking FROM ?:products WHERE product_id = ?i", $params['product_id']);

        return array($inventory, $params, $product_options, $product_inventory);
    }
    /**
     * @param array $params
     * @param string|null $lang_code
     * @return array
     */
    public static function getInventoryByFilter($params = [], $lang_code = null): array
    {
        $lang_code = $lang_code ?? (string) GlobalState::contentLocale();
        $inventory = db_get_array("SELECT * FROM ?:product_options_inventory WHERE product_id = ?i  AND combination = ?s", $params['product_id'], $params['combination']);

        foreach ($inventory as $k => $v) {
            $declination = new Declination(new Product($v['product_id']), $v['combination']);
            $inventory[$k]['combination'] = fn_get_product_options_by_combination($v['combination']);
            $inventory[$k]['combination_string'] = $v['combination'];
            $inventory[$k]['image_pairs'] = fn_get_image_pairs($declination->getId(), 'declinations', 'M', true, true, $lang_code);
            $inventory[$k]['product_price_tiers'] = container()
                ->get('marketplace.price_tier.price_tier_service')
                ->getPriceTiersOfProductOrCombination($v['product_id'], $v['combination'])
            ;
        }

        return $inventory;
    }

    public function getSelfInventory()
    {
        return static::getInventory(['product_id' => $this->getId()]);
    }

    /**
     * filter unsalable option variants (all prices or amount = 0)
     *
     * @param array $product_data
     */
    public static function filterUnsalableProductOptionVariants(array &$product_data)
    {
        if (isset($product_data['product_options'])) {
            $params = ['product_id' => $product_data['product_id']];
            list($combinations) = \Wizacha\Product::getInventory($params);

            //Filter valid combinations (with price and amount)
            $valid_combinations = array_filter(
                $combinations,
                function ($combination) {
                    return $combination['amount'] > 0 && $combination['w_price'] > 0;
                }
            );
            $valid_combinations = array_column($valid_combinations, 'combination');

            //Retrieve corresponding variant ids
            $variants_in_use = array_reduce(
                $valid_combinations,
                function ($result, $variants) {
                    return array_merge($result, $variants);
                },
                []
            );
            $variants_in_use = array_flip($variants_in_use);

            //Intersect possible variants with used ones
            foreach ($product_data['product_options'] as &$option) {
                $option['variants'] = array_intersect_key(
                    $option['variants'],
                    $variants_in_use
                );
            }
        }
    }

    /**
     * Retrieve brand infos from product header_features (first feature type E)
     */
    public static function getBrandInfos(array $productFeatures): array
    {
        $brand = '';
        $brandId = 0;

        foreach ($productFeatures as $value) {
            // "Brand attribute"
            if ($value['feature_type'] === 'E') {
                if (isset($value['variant'])) {
                    $brand = $value['variant'];
                } elseif (isset($value['variants'][$value['variant_id']]['variant'])) {
                    $brand = $value['variants'][$value['variant_id']]['variant'];
                }
                $brandId = $value['variant_id'];
                break;
            } elseif ($value['feature_type'] === 'G' && !empty($value['subfeatures'])) {
                // If it's a group try to find the brand in sub-attributes
                list($brand, $brandId) = self::getBrandInfos($value['subfeatures']);
                if ($brandId > 0) {
                    break;
                }
            }
        }

        return [$brand, $brandId];
    }

    /**
     * @return string The brand name.
     */
    public function getBrand()
    {
        $features = $this->fillCscartData()['product_features'] ?: [];
        list($brand) = self::getBrandInfos($features);
        return $brand;
    }

    public function getGreenTax(): Money
    {
        return Money::fromVariable($this->fillCscartData()['w_green_tax'] ?? 0);
    }

    public function getUpdatedTimestamp(): int
    {
        return \intval($this->fillCscartData()['updated_timestamp']);
    }

    public static function getIdByProductCode(?string $product_code, int $companyId = 0): ?string
    {
        // Même si exite un $company_id dans le fichier CSV d'un vendeur on l'écrase avec son propre company_id
        if (Registry::defaultInstance()->get(['runtime', 'company_id']) > 0) {
            $companyId = Registry::defaultInstance()->get(['runtime', 'company_id']);
        }

        static $product_id = [];
        //if 0, check if value has changed (product has been created since)
        if (empty($product_id[$companyId][$product_code])) {
            $product_id[$companyId][$product_code] = \Tygh\Database::getField(
                "SELECT product_id FROM ?:products WHERE product_code = ?s AND company_id = ?i",
                $product_code,
                $companyId
            );
        }
        return $product_id[$companyId][$product_code];
    }

    /**
     * Returns the front-end url of a product
     *
     * @param integer $product_id
     * @param bool    $useHttps
     *
     * @return string
     */
    public static function frontUrl($product_id, bool $useHttps = true)
    {
        return fn_url(
            'products.view?product_id=' . $product_id,
            'C',
            $useHttps ? 'https' : 'http'
        );
    }

    public static function frontIds(array $ids)
    {
        if (\count($ids) <= 0) {
            return new \ArrayIterator();
        }

        return self::getFrontByIds($ids);
    }

    public static function allFrontIds()
    {
        return self::getFrontByIds([]);
    }

    /**
     * Returns a PdoColumnIterator to iterate on all valid ids
     *
     * WARNING!!
     *
     * If you modify some conditions in this function, you must modify them in these 2 following places:
     * - src/Marketplace/Catalog/Product/ProductService.php : function getSearcheableProductIdInFront
     * - src/Marketplace/PIM/Product/ProductVisibilityReport.php
     *
     * These 3 places must reflect the same behavior at all time! Else we could be out of step with the real behavior
     *
     * @return \Wizacha\Core\Iterator\PdoColumnIterator
     */
    private static function getFrontByIds(array $ids = [])
    {
        $showZeroPriceProducts = container()->getParameter('feature.catalog.show_zero_price_products');
        $showOutOfStockProducts = container()->getParameter('feature.catalog.show_out_of_stock_products');

        $priceOperator = ($showZeroPriceProducts === true) ? '>=' : '>';

        if ($showOutOfStockProducts) {
            $extra_condition = \Tygh\Database::quote(
                "IF(products.tracking = ?s,
                    (poi.w_price " . $priceOperator . " 0),
                    (pp.price " . $priceOperator . " 0)
                 )",
                self::TRACKING_TYPE_INVENTORY
            );
        } else {
            $extra_condition = \Tygh\Database::quote(
                "IF(products.tracking = ?s,
                    (poi.w_price " . $priceOperator . " 0 AND (poi.infinite_stock OR poi.amount > 0)),
                    (pp.price " . $priceOperator . " 0 AND (products.infinite_stock OR products.amount > 0))
                 )",
                self::TRACKING_TYPE_INVENTORY
            );
        }

        $sub_list = empty($ids) ? '' : \Tygh\Database::quote('AND products.product_id IN(?a)', $ids);

        return new \Wizacha\Core\Iterator\PdoColumnIterator(\Tygh\Database::prepare(
            "SELECT products.product_id FROM ?:products as products
             LEFT JOIN ?:product_prices AS pp ON pp.product_id = products.product_id
             LEFT JOIN ?:product_options_inventory AS poi ON poi.product_id = products.product_id
             INNER JOIN ?:products_categories AS w_prod_cat ON products.product_id = w_prod_cat.product_id AND link_type = 'M'
             INNER JOIN ?:categories AS w_cat ON w_cat.category_id = w_prod_cat.category_id
             INNER JOIN ?:companies AS companies ON companies.company_id = products.company_id
             WHERE w_cat.status=?s
             AND companies.status IN ('A') AND products.status IN ('A', 'H') AND products.approved = 'Y'
             AND ?p
             ?p
             GROUP BY products.product_id",
            \Wizacha\Status::ENABLED,
            $extra_condition,
            $sub_list
        ));
    }

    /**
     * Determine if product has changed from last update.
     * @param integer  $product_id
     *
     * @return boolean
     */
    public static function hasChanged($product_id, Locale $locale = null): bool
    {
        $product_data = fn_get_product_data(
            $product_id,
            $_SESSION['auth'],
            (string) ($locale ?? GlobalState::contentLocale()),
            '',
            true,
            true,
            true,
            true,
            false,
            true,
            true,
            true,
            false
        );

        unset($product_data['timestamp'], $product_data['updated_timestamp']);
        $p = new self($product_id);
        $product_data['geocoding'] = $p->getGeoloc();
        $product_data['free_features'] = $p->getFreeFeatures($locale);

        if (container()->getParameter('feature.available_offers')) {
            $product_data['divisions'] = container()->get('marketplace.division.products.service')->getAllDivisionsCode($product_id);
        }

        return Hash::updateHash($product_id, $product_data, self::OBJECT_TYPE, $locale);
    }

    /**
     * Return if product has at least one mess category
     *
     * @param int $id product id
     * @return bool
     */
    public static function inMessCategory($id)
    {
        $ids = \Tygh\Database::getColumn('SELECT category_id FROM ?:products_categories WHERE product_id = ?i', $id);
        foreach ($ids as $id) {
            if (\Wizacha\Category::isMessCategory($id)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Delete ALL products of a company
     * @param int $company_id
     */
    public static function deleteFromCompany($company_id)
    {
        $product_ids = new \Wizacha\Core\Iterator\PdoColumnIterator(
            \Tygh\Database::query('SELECT product_id FROM ?:products WHERE company_id = ?i', $company_id)
        );
        foreach ($product_ids as $product_id) {
            try {
                fn_delete_product($product_id, true);
            } catch (Forbidden $e) {
                fn_set_notification('W', __('warning'), __('access_denied'));
            }
        }
    }

    public static function getAllConditions()
    {
        return [
            self::CONDITION_NEW,
            self::CONDITION_USED
        ];
    }

    /**
     * @param array $params
     * @return string
     */
    public static function getProductCondition(array $params)
    {
        if (isset($params[self::CONDITION_DB_FIELD])
            && \is_array($params[self::CONDITION_DB_FIELD])
            && !empty($params[self::CONDITION_DB_FIELD])
        ) {
            $cond = array_map(
                function ($condition) {
                    return \Tygh\Database::quote(self::CONDITION_DB_FIELD . ' = ?s', $condition);
                },
                $params[self::CONDITION_DB_FIELD]
            );
            return ' AND (' . implode(' OR ', $cond) . ')';
        }
        return '';
    }

    /**
     * Returns the exceptions associated to a product
     * @param int $product_id
     * @return array [option_id1 => [ variant_id1 => variant_id1, ...], ...]
     */
    public static function getExceptions($product_id)
    {
        $combinations = \Tygh\Database::getArray(
            'SELECT option_id, variant_id FROM ?:product_options_exceptions WHERE product_id = ?i',
            $product_id
        );
        $exceptions   = [];
        foreach ($combinations as $combination) {
            $exceptions[$combination['option_id']][$combination['variant_id']] = $combination['variant_id'];
        }

        return $exceptions;
    }

    /**
     * Returns the main_category field if set, or first category_ids.
     * Returns null if both fields are not set
     * Returns false if value is not an integer
     * @param array $product_data
     * @return int|false|null
     */
    public static function getMainCategoryFromData(array $product_data)
    {
        if (\array_key_exists('main_category', $product_data)) {
            return $product_data['main_category'] && is_numeric($product_data['main_category'])
                ? (int) $product_data['main_category']
                : false;
        }
        if (\array_key_exists('category_ids', $product_data)) {
            if (\is_array($product_data['category_ids'])) {
                $category_id = reset($product_data['category_ids']);
                if ($category_id && is_numeric($category_id)) {
                    return (int) $category_id;
                }
            }
            return false;
        }
        return null;
    }

    public function getCompanyId(): int
    {
        return \intval($this->getData()['company_id']);
    }

    public function canUserUpdate(User $user): bool
    {
        return $this->getCompanyId() == $user->getCompanyId();
    }

    /**
     * Return true if product has declination in cscart definition
     */
    public function hasDeclinations(): bool
    {
        return $this->getData()['tracking'] == static::TRACKING_TYPE_INVENTORY;
    }

    public function getCompany(): Company
    {
        return new Company($this->getCompanyId());
    }

    /**
     * @return string
     */
    public function getShortDescription()
    {
        return $this->getData()['short_description'];
    }


    public function getTimestamp(): int
    {
        return (int) $this->getData()['timestamp'];
    }

    /**
     * @return string
     */
    public function getDescription()
    {
        return $this->getData()['full_description'];
    }


    /** @return int[] */
    private function getAllImageIds(): array
    {
        return \Tygh\Database::getColumn(
            <<<SQL
            SELECT
                detailed_id
            FROM
                ?:images_links
            WHERE
                object_id=?s
                AND object_type='product'
            ORDER BY
                (IF(`type`=?s,1,0)) DESC,
                position ASC,
                pair_id ASC
            SQL,
            $this->getId(),
            ImageLinkType::MAIN()
        );
    }

    /**
     * @return \Wizacha\Marketplace\Image\Image[]
     */
    public function getAllImages(): array
    {
        return array_map(
            function ($id) {
                return new \Wizacha\Marketplace\Image\Image($id);
            },
            $this->getAllImageIds()
        );
    }

    /**
     * @return Image[]
     * @deprecated Use getAllImages()
     * @see \Wizacha\Product::getAllImages()
     */
    public function getImages(): array
    {
        return array_map(
            function ($image_id) {
                return new Image($image_id);
            },
            $this->getAllImageIds()
        );
    }

    public function getAmount(): int
    {
        return \intval($this->getData()['amount']);
    }

    public function hasInfiniteStock(): bool
    {
        return \boolval($this->getData()['infinite_stock']);
    }

    /**
     * @return array list of ProductShippingRate
     */
    public function getProductShippingsRates()
    {
        if (!$this->getId()) {
            return [];
        }

        return array_filter(array_map(
            function ($shipping) {
                if ($shipping['shipping_id']) {
                    return new ProductShippingRate(
                        new \Wizacha\Marketplace\Entities\Shipping($shipping['shipping_id']),
                        $this
                    );
                }
                return null;
            },
            $this->fillCscartData()['shippings'] ?: []
        ));
    }

    /**
     * @return ProductShippingRate[]
     */
    public function getProductActiveShippingsRates(): array
    {
        if (!$this->getId()) {
            return [];
        }

        return array_filter(array_map(
            function ($shipping) {
                if ($shipping['shipping_id'] && $shipping['status'] == Status::ENABLED) {
                    return new ProductShippingRate(
                        new \Wizacha\Marketplace\Entities\Shipping($shipping['shipping_id']),
                        $this
                    );
                }
                return null;
            },
            $this->fillCscartData()['shippings'] ?: []
        ));
    }

    /**
     * @return string
     */
    public function getCondition()
    {
        return $this->getData()['w_condition'];
    }

    /**
     * @return Declination[] list of Declinations
     */
    public function getDeclinations()
    {
        // Don't fetch inventory if tracking is not TRACKING_TYPE_INVENTORY
        if (!$this->hasDeclinations()) {
            return [new Declination($this)];
        }

        $combinations = \Tygh\Database::getColumn(
            'SELECT combination FROM ?:product_options_inventory WHERE product_id=?i ORDER BY position',
            $this->getId()
        );
        if (!$combinations) {
            return [new Declination($this)];
        }
        return array_map(
            function ($combination) {
                return new Declination($this, $combination);
            },
            $combinations
        );
    }

    /**
     * @return array|mixed
     */
    protected function fillCscartData()
    {
        if (!$this->cscartData) {
            $this->cscartData = fn_get_product_data($this->getId(), $_SESSION['auth'], (string) $this->defaultLocale, '', true, true, true, true, false, true, true, true, true, false);
        }

        return $this->cscartData;
    }

    /** invalidate ProductManager cache */
    protected function productManagerInvalidate(): void
    {
        /** @var ProductManager */
        $productManager = container()->get('cscart.product_manager');
        $productManager->invalidate($this->product_id);
    }

    /**
     * @deprecated
     * @return array
     */
    public function getCscartData()
    {
        return $this->fillCscartData();
    }

    /**
     * @param string|null $status a constant from Wizacha\Status to filter
     * @return Tax[]
     */
    public function getTaxes(string $status = null): array
    {
        if (empty($this->fillCscartData()['tax_ids'])) {
            return [];
        }

        $taxes = array_map(
            function ($taxId) {
                return new Tax($taxId);
            },
            $this->fillCscartData()['tax_ids']
        );

        if (\is_string($status)) {
            $taxes = array_filter($taxes, function (Tax $tax) use ($status): bool {
                return $tax->hasStatus($status);
            });
        }

        return $taxes;
    }

    public function getCrossedOutPrice(): ?Money
    {
        if ($this->fillCscartData()['crossed_out_price']) {
            $crossedOutPrice = Money::fromVariable($this->fillCscartData()['crossed_out_price']);
            if (!$crossedOutPrice->isZero()) {
                return $crossedOutPrice;
            }
        }
        return null;
    }

    public function getAffiliateLink(): ?string
    {
        $data = $this->fillCscartData();
        return isset($data['affiliate_link']) ? $data['affiliate_link'] : null;
    }

    public function isApproved(): bool
    {
        return $this->getData()['approved'] == 'Y';
    }

    /**
     * Returns the transaction mode to use for the product.
     *
     * It can be defined on the product directly if the category allows it, else it is
     * inherited from the category.
     */
    public function getTransactionMode(): TransactionMode
    {
        $category = $this->getCategory();
        if ($category->isTransactionModeOverridable()) {
            $transactionMode = new TransactionMode($this->getData()['transaction_mode']);
            if ($transactionMode == TransactionMode::INHERITED()) {
                return $category->getTransactionMode();
            }

            return $transactionMode;
        }

        return $category->getTransactionMode();
    }

    public function getSupplierRef(): ?string
    {
        return $this->getData()['w_supplier_ref'];
    }

    public function getProductTemplateType(): ?string
    {
        return $this->getData()['product_template_type'] ?? null;
    }

    /**
     * Returns the quantity in inventory
     */
    public function getInventoryAmount(): int
    {
        // Récupération de la clé "inventory" du produit -> déclinaison d'un produit
        list($productInventory) = self::getInventory(['product_id' => $this->product_id]);

        // S'il y a des déclinaisons, la quantité est la somme des quantités pour chaque déclinaison
        if (\count($productInventory) > 0) {
            $amount = 0;
            foreach ($productInventory as $inventory) {
                $amount += \intval($inventory['amount']);
            }
        } else {
            // Pas de déclinaisons, récupération de la quantité du produit
            $amount = $this->getAmount();
        }

        return $amount;
    }

    public function getDivisions(): array
    {
        return $this->getCscartData()['divisions'] ?? [];
    }

    public function getOriginalDeclinationId(): string
    {
        return $this->product_id . '_0';
    }

    public function isSubscription(): bool
    {
        return $this->getData()['is_subscription'] ?? false;
    }

    public function isRenewable(): bool
    {
        return $this->getData()['is_renewable'] ?? false;
    }

    public function getMaxPriceAdjustment(): ?int
    {
        return $this->getData()[static::MAX_PRICE_ADJUSTMENT] ?? null;
    }

    public function isEdp(): bool
    {
        return $this->getData()['is_edp'] === 'Y';
    }

    public function getQuoteRequestMinQuantity(): int
    {
        return \intval($this->getData()['quote_requests_min_quantity']);
    }

    public function isQuoteExclusive(): bool
    {
        return $this->getData()['is_exclusive_to_quote_requests'] ?? false;
    }
}
