<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha;

use Tygh\Database;
use Wizacha\Marketplace\Entities\Company as EntityCompany;
use Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmounts;
use Wizacha\Marketplace\Order\Order as OrderV2;
use Wizacha\Marketplace\Order\OrderDataType;
use Wizacha\Marketplace\Order\OrderPricesInterface;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\PaymentType;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionStatus;
use Wizacha\Marketplace\Transaction\TransactionType;
use Wizacha\Money\Money;

/**
 * @deprecated See \Wizacha\Marketplace\Order\OrderService instead
 * @see \Wizacha\Marketplace\Order\OrderService
 */
class Order implements OrderPricesInterface
{
    protected ?int $id;

    /** @var array|false */
    protected $data;
    protected Money $balance;
    protected TransactionService $transactionService;

    /** An entity to store calculations about orders */
    protected ?OrderAmounts $orderAmounts;
    protected ?OrderV2 $orderV2;

    public function __construct(int $id = 0, OrderV2 $orderV2 = null)
    {
        $this->id = $id;
        $this->orderV2 = $orderV2;
        $this->orderAmounts = null;

        return $this;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getOrderAmounts(): OrderAmounts
    {
        if (null === $this->orderAmounts) {
            $this->orderAmounts =  $this->getOrderV2()->getOrderAmounts();
        }

        return $this->orderAmounts;
    }

    /**
     * @return EntityCompany
     */
    public function getCompany()
    {
        $companyId = \Tygh\Database::getField('SELECT company_id FROM ?:orders WHERE order_id = ?i', $this->id);
        return new EntityCompany(\intval($companyId));
    }

    /**
     * @return Order[]
     */
    public function getSubOrders()
    {
        $suborder =  \Tygh\Database::getColumn('SELECT order_id FROM ?:orders WHERE parent_order_id = ?i', $this->id);
        if (!$suborder) {
            return [];
        }
        return array_map(
            function ($orderId) {
                return new Order($orderId);
            },
            $suborder
        );
    }

    public function isParentOrder()
    {
        return db_get_field("SELECT is_parent_order FROM ?:orders WHERE order_id = ?i", $this->id) === 'Y';
    }

    /**
     * @return null|Order
     */
    public function getParentOrder()
    {
        $parentId = $this->getCscartData()['parent_order_id'];
        return $parentId == 0 ? null : new Order($parentId);
    }

    public function getTotal(): Money
    {
        return $this->getOrderAmounts()->getTotalInclTaxes();
    }

    public function getSubtotal(): Money
    {
        return $this->getOrderAmounts()->getSubTotalInclTaxes()->subtract($this->getOrderAmounts()->getTotalDiscount());
    }

    public function getMarketplaceDiscountTotal(): Money
    {
        return $this->getOrderAmounts()->getTotalMarketplaceDiscount();
    }

    public function hasMarketplaceDiscount(): bool
    {
        return $this->getMarketplaceDiscountTotal()->isPositive();
    }

    public function getCustomerTotal(): Money
    {
        return $this->getOrderAmounts()->getCustomerTotal();
    }

    public function getTaxTotal(): Money
    {
        return $this->getOrderAmounts()->getTotalTaxAmount();
    }

    public function getEmail(): ?string
    {
        return $this->getCscartData()['email'];
    }

    public function getUserId(): int
    {
        return $this->getCscartData()['user_id'];
    }

    /**
     * @return Money Get the subtotal, without promotions
     */
    public function getRealSubtotal(): Money
    {
        return $this->getOrderAmounts()->getSubTotalInclTaxes();
    }

    public function getShippingCost(): Money
    {
        return $this->getOrderAmounts()->getShippingAmount()->getDiscountedTotalInclTaxes();
    }

    public function getProductsIds(): array
    {
        $orderProducts = $this->getCscartData()['products'];
        return array_unique(array_column($orderProducts, 'product_id'));
    }

    public function getCouponMarketplaceDiscount(): ?string
    {
        if ($this->hasMarketplaceDiscount() === false) {
            return null;
        }

        return db_get_field("SELECT coupon FROM doctrine_marketplace_promotion WHERE id = ?s", $this->getCscartData()['marketplace_discount_id']);
    }

    /**
     * @return array
     */
    public function getPaymentInformation()
    {
        $_payment_info = db_get_field(
            "SELECT data FROM ?:order_data WHERE order_id = ?i AND type = ?s",
            $this->id,
            OrderDataType::PAYMENT_INFO()->getValue()
        );
        if (empty($_payment_info)) {
            return [];
        }

        $data = unserialize(fn_decrypt_text($_payment_info));

        if (false === $data) {
            container()->get('logger')->error('Order: Unable to decrypt payment information, incorrect crypt_key.');
            $data = [];
        }

        return $data;
    }

    public function hasProcessorName(PaymentProcessorName $processorName): bool
    {
        $paymentId = $this->getCscartData()['payment_id'] > 0 ? (int) $this->getCscartData()['payment_id'] : 0;

        if ($paymentId === 0) {
            return false;
        }

        /** @var ?PaymentProcessorName $processor */
        $processor = container()
            ->get('marketplace.payment.payment_service')
            ->getPaymentProcessorName($paymentId);

        return $processor instanceof PaymentProcessorName ? $processor->equals($processorName) : false;
    }

    /** @deprecated Use the order transaction table instead, through TransactionService. */
    public function setPaymentInformation($key, $value)
    {
        $data = $this->getPaymentInformation();
        $data[$key] = $value;
        Database::query(
            "REPLACE INTO ?:order_data ?e;",
            [
                'order_id' => $this->id,
                'type' => OrderDataType::PAYMENT_INFO()->getValue(),
                'data' => fn_encrypt_text(serialize($data)),
            ]
        );

        foreach ($this->getSubOrders() as $subOrder) {
            $subOrder->setPaymentInformation($key, $value);
        }
    }

    /**
     * This static is used in some RMA template, so, despite the class name, we can inject the statusType.
     * Only STATUSES_ORDER and STATUSES_RETURN are used *but* it seems exist a statusType 'G' but nobody knows for what purpose.
     * @param int $company_id
     * @param mixed $statusType
     */
    public static function getStatuses($company_id = 0, $statusType = STATUSES_ORDER): array
    {
        $statuses = fn_get_simple_statuses($statusType);
        if ($statusType == STATUSES_ORDER && $company_id) {
            $statuses = array_intersect_key($statuses, array_flip(\Wizacha\OrderStatus::getVendorDisplayedStatuses()));
        }

        return $statuses;
    }

    /**
     * In area A and C, the vendors can see only orders in specific status.
     * @param integer $company_id
     * @param string  $area
     * @param string  $original_condition
     * @return string
     */
    public static function statusCondition($company_id, $area, $user_id, $original_condition, $useCscartPrefix = true)
    {
        $prefix = $useCscartPrefix ? '?:' : '';
        if ((!$company_id && $area == 'A') || \defined('SKIP_SESSION_VALIDATION') || \defined('PAYMENT_NOTIFICATION')) {
            return $original_condition;
        }

        if (!empty(trim($original_condition))) {
            $original_condition .= ' AND ';
        }

        // Filter with allowed vendor status from getVendorDisplayedStatuses,
        // and add a condition to allow vendor to see STANDBY_BILLING orders in case of payment deferment
        $status_condition = \Tygh\Database::quote(
            "(${prefix}orders.status IN (?a) OR (${prefix}orders.status = 'O' AND accepted_by_vendor = 1))",
            \Wizacha\OrderStatus::getVendorDisplayedStatuses()
        );

        if ($area == 'C') {
            $status_condition = '(' . $status_condition . \Tygh\Database::quote(" OR ${prefix}orders.user_id=?i)", $user_id);
        }

        return $original_condition . $status_condition;
    }

    public function getBalanceTotal(): Money
    {
        return
            $this
            ->getTransactionService()
            ->getBalanceByOrderId($this->getId())
        ;
    }

    protected function getTransactionService(): TransactionService
    {
        return
            $this->transactionService
            ??= container()->get(
                'marketplace.transaction.transaction_service'
            )
        ;
    }

    public function getOrderV2(): OrderV2
    {
        if (null === $this->orderV2) {
            $this->orderV2 = container()->get('marketplace.order.order_service')->getAnyOrder($this->id);
        }

        return $this->orderV2;
    }

    /**
     * @return array
     */
    protected function getCscartData()
    {
        if (!$this->data) {
            $orderService = container()->get('marketplace.order.order_service');
            $this->data = $orderService->overrideLegacyOrder($this->id);
        }
        return $this->data;
    }

    public function getPaymentType(): ?PaymentType
    {
        $orderData = $this->getCscartData();

        if (\array_key_exists('payment_id', $orderData) === true
            && $orderData['payment_id'] > 0
        ) {
            return container()
                ->get('marketplace.payment.payment_service')
                ->getPaymentType($orderData['payment_id']);
        }

        return null;
    }

    public function getCreatedAt(): \DateTimeImmutable
    {
        return \DateTimeImmutable::createFromFormat('U', $this->getCscartData()['timestamp'] ?? 0);
    }

    public function getTransactionReference(): string
    {
        return \array_key_exists('transaction_reference', $this->getCscartData()) === true
            && $this->getCscartData()['transaction_reference'] !== null
            ? $this->getCscartData()['transaction_reference']
            : ''
        ;
    }
}
