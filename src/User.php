<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha;

use Tygh\Database;
use Wizacha\Component\Locale\Locale;

/**
 * @deprecated Use Wizacha\Marketplace\User\User instead.
 * @see \Wizacha\Marketplace\User\User
 */
class User
{
    public const ADMIN_TYPE = 'A';
    public const VENDOR_TYPE = 'V';
    public const CLIENT_TYPE = 'C';
    private const BIRTHDAY = 'birthday';

    public const USER_ADDITIONAL_DATA_TYPE = 'W';

    /**
     * @var integer $id
     */
    protected $id;

    /**
     * @var array
     */
    protected $additionnal_data;

    /**
     * @var array
     */
    protected $data;

    /**
     * @param integer $id
     */
    public function __construct($id)
    {
        $this->id = (int) $id;
    }

    /**
     * @param string $pseudo
     * @return $this
     */
    public function setPseudo($pseudo)
    {
        if (empty($this->_getCompanyName())) {
            $this->_saveAdditionnalData('pseudo', (string) $pseudo);
        }
        return $this;
    }

    /**
     * @return string
     */
    public function getPseudo()
    {
        $company_name = (string) $this->_getCompanyName();
        return $company_name ?: (string) $this->_getAdditionnalData()['pseudo'];
    }

    /**
     * @param bool $enable
     * @return $this
     */
    public function setNotificationsSetting($enable)
    {
        $this->_saveAdditionnalData('settings_notifications', (bool) $enable);
        return $this;
    }

    /**
     * @return bool
     */
    public function getNotificationsSetting()
    {
        $enabled = $this->_getAdditionnalData()['settings_notifications'];
        return \is_null($enabled) ? true : (bool) $enabled;
    }

    /**
     * @return string
     */
    public function getFirstname()
    {
        return $this->_getData()['firstname'];
    }

    /**
     * @return string
     */
    public function getLastname()
    {
        return $this->_getData()['lastname'];
    }

    public function getFullname(): string
    {
        return trim("{$this->getFirstname()} {$this->getLastname()}");
    }

    /**
     * @return string
     */
    public function getEmail()
    {
        return $this->_getData()['email'];
    }

    public function isVendor(): bool
    {
        return \boolval($this->_getCompanyName());
    }

    public function isAdmin(): bool
    {
        return $this->_getData()['user_type'] == static::ADMIN_TYPE;
    }

    public function isProfessionalVendor(): bool
    {
        return $this->_getData()['user_type'] == static::VENDOR_TYPE;
    }

    public function getBirthday(): ?\DateTimeInterface
    {
        return $this->_getData()[static::BIRTHDAY] instanceof \DateTime === true
            ? $this->_getData()[static::BIRTHDAY]
            : null;
    }

    public function getNationality(): string
    {
        return 'FR';
    }

    public function getCountryOfResidence(): string
    {
        return 'FR';
    }

    public function getLocale(): ?Locale
    {
        if (\is_null($this->_getData()['lang_code'])) {
            return null;
        }

        return new Locale($this->_getData()['lang_code']);
    }

    /**
     * @return string
     */
    private function _getCompanyName()
    {
        return (string) Database::getField(
            "SELECT c.company FROM ?:companies AS c JOIN ?:users AS u ON u.company_id = c.company_id
              WHERE u.user_id = ?i",
            $this->id
        );
    }

    public function getDivision(): ?string
    {
        return $this->_getData()['division_code'] ?? null;
    }

    private function _getData(): array
    {
        if (! $this->data) {
            $this->data = fn_get_user_info($this->id) ?: [];
        }
        return $this->data;
    }

    private function _unsetData(): void
    {
        $this->data = null;
    }

    private function _getAdditionnalData(): array
    {
        if (! $this->additionnal_data) {
            $this->additionnal_data = fn_get_user_additional_data(\Wizacha\User::USER_ADDITIONAL_DATA_TYPE, $this->id) ?: [];
        }
        return $this->additionnal_data;
    }

    private function _saveAdditionnalData($name, $value): void
    {
        $this->_getAdditionnalData();
        $this->additionnal_data[$name] = $value;
        fn_save_user_additional_data(\Wizacha\User::USER_ADDITIONAL_DATA_TYPE, $this->additionnal_data, $this->id);
    }

    /**
     * @param int $company_id
     * @param int $user_id
     */
    public static function setCompanyId($company_id, $user_id): void
    {
        Database::query('UPDATE ?:users SET company_id = ?i WHERE user_id = ?i', $company_id, $user_id);
    }

    /**
     * @param int $firstname
     * @param int $user_id
     */
    public static function setFirstname($firstname, $user_id): void
    {
        Database::query('UPDATE ?:users SET firstname = ?s WHERE user_id = ?i', $firstname, $user_id);
    }

    /**
     * @param int $lastname
     * @param int $user_id
     */
    public static function setLastname($lastname, $user_id): void
    {
        Database::query('UPDATE ?:users SET lastname = ?s WHERE user_id = ?i', $lastname, $user_id);
    }

    public function getCompanyId(): int
    {
        return \intval($this->_getData()['company_id']);
    }

    /**
     * @param string $basket_id UUID
     */
    public function setBasketId($basket_id)
    {
        Database::query('UPDATE ?:users SET basket_id = ?s WHERE user_id = ?i', $basket_id, $this->id);
        $this->_unsetData();
    }

    /**
     * @return string UUID
     */
    public function getBasketId()
    {
        return $this->_getData()['basket_id'];
    }

    public function getId(): int
    {
        return $this->id;
    }

    /**
     * Returns true if the user has already placed orders through the marketplace, false otherwise.
     */
    public function hasOrders(): bool
    {
        $orderCount = Database::query('SELECT COUNT(*) FROM ?:orders WHERE user_id = ?i', $this->id);

        return $orderCount > 0;
    }
}
