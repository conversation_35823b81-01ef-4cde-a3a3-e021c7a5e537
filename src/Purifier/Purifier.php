<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\Purifier;

class Purifier
{
    /** @var \HTMLPurifier */
    private $purifier;

    /** @var bool $featureIsActive */
    private $featureIsActive;

    public function __construct(\HTMLPurifier $purifier, string $featureIsActive)
    {
        $this->purifier = $purifier;
        $this->featureIsActive = \filter_var($featureIsActive, FILTER_VALIDATE_BOOLEAN);
    }

    public function purify(string $text): string
    {
        return $this->featureIsActive === true ? $this->purifier->purify($text) : $text;
    }
}
