<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha;

use Symfony\Component\HttpFoundation\File\UploadedFile;
use Tygh\Database;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Image\Exception\BadImage;
use Wizacha\Marketplace\Seo\Slug\SlugGenerator;
use Wizacha\Storage\StorageService;

/**
 * @deprecated Use \Wizacha\Marketplace\Image\Image instead
 * @see \Wizacha\Marketplace\Image\Image
 */
class ImageManager
{
    public const BYPASS_URL = '-';

    protected ?StorageService $storage = null;

    protected bool $serveFakeImages;

    public const DEFAULT_IMAGE_DIRECTORY = 'detailed';

    public const MAX_IMAGE_SURFACE = 5000 * 5000;

    public function __construct(
        StorageService $storage,
        bool $serveFakeImages = false
    ) {
        $this->storage = $storage;
        $this->serveFakeImages = $serveFakeImages;
    }

    /**
     * Todo: Use storage when S3 SDK would be updated or remove if all image are in same subdirectory
     * @return array
     */
    public function getRootPrefixes()
    {
        return [
            'detailed',
            'feature_variant',
            'logos',
            'payment',
            'product',
            'promo',
            'shipping',
        ];
    }

    /**
     * @param integer $pair_id
     * @param string $area
     * @param integer $company_id
     * @return bool
     */
    public static function isPairDeletable($pair_id, $area, $company_id)
    {
        if (!$company_id) {
            return 'A' == $area;
        }
        $data = Database::getRow("SELECT object_id, object_type FROM ?:images_links WHERE pair_id=?i", $pair_id);

        if (empty($data)) {
            return false;
        }

        switch ($data['object_type']) {
            case 'product':
                $object_company_id = Database::getField(
                    'SELECT company_id FROM ?:products WHERE product_id=?i',
                    $data['object_id']
                );
                break;
            case 'product_option':
                $object_company_id = Database::getField(
                    'SELECT company_id
                    FROM ?:products as p
                    LEFT JOIN ?:product_options_inventory as po ON p.product_id=po.product_id
                    WHERE po.combination_hash=?i',
                    $data['object_id']
                );
                break;
            case 'declinations':
                $object_company_id = Database::getField(
                    'SELECT company_id
                    FROM ?:products as p
                    LEFT JOIN ?:product_options_inventory as po ON p.product_id=po.product_id
                    WHERE CONCAT_WS("_", po.product_id, po.combination) = ?s',
                    $data['object_id']
                );
                break;
            default:
                return false;
        }

        return $company_id == $object_company_id;
    }

    /**
     * Returns a *unique* path to store thumbnails of an image
     * @param string $image_path
     * @return string
     */
    public static function thumbnailsRootPath($image_path)
    {
        return 'thumbnails/' . implode('/', str_split(md5($image_path), 3)) . '/';
    }


    public static function isSizeCorrect($x, $y): bool
    {
        if (empty($x) || empty($y)) {
            return false;
        }

        return ($x * $y) < self::MAX_IMAGE_SURFACE;
    }

    /**
     * Returns the path of a particular image thumbnail
     * @param string $image_path
     * @param int $width
     * @param int $height
     * @return string
     */
    public static function thumbnailPath($image_path, $width, $height = 0)
    {
        return self::thumbnailsRootPath($image_path) . $width . (empty($height) ? '' : '/' . $height) . '/' . $image_path;
    }

    public function delete(int $imageId, string $multiVendorProductId = null): bool
    {
        if ($this->countLinks($imageId) > 0
            || ($multiVendorProductId !== null && $this->haveImageInOtherMultiVendorProduct($imageId, $multiVendorProductId) === true)
        ) {
            return false;
        }
        $data = $this->imageInfo($imageId);

        Database::query("DELETE FROM ?:images_seo WHERE image_id = ?i", $imageId);
        Database::query('DELETE FROM ?:images WHERE image_id=?i', $imageId);

        $this->deleteThumbnails($imageId);
        foreach ($this->getRootPrefixes() as $base_path) {
            if ($this->storage->delete($base_path . '/' . $this->subdirectoryPathFromID($imageId) . '/' . $data['image_path'])) {
                break;
            }
        }

        //New images path deletion
        $extension = pathinfo($data['image_path'])['extension'];
        $this->storage->delete('originals/' . $this->subdirectoryPathFromID($imageId) . "/{$imageId}.{$extension}");

        fn_w_invalidate_cloudimage_cache($imageId);

        return true;
    }

    /**
     * @param array $images Images for which we must remove the links
     * @param string $object_type
     * @param int $object_id
     * @return int
     */
    public function deletePairs(array $images, string $object_type, int $object_id): int
    {
        $pairs = $this->getPairs($object_type, $object_id);
        $nbDeleted = 0;

        foreach ($pairs as $pair) {
            if (\in_array($pair['detailed_id'], $images)) {
                Database::query(
                    'DELETE FROM ?:images_links WHERE object_type=?s AND object_id=?s',
                    $object_type,
                    $object_id
                );

                $nbDeleted++;
            }
        }

        return $nbDeleted;
    }

    /**
     * @param integer $image_id
     */
    public function deleteThumbnails($image_id)
    {
        $path = Database::getField('SELECT image_path FROM ?:images WHERE image_id=?i', $image_id);
        if (!empty($path)) {
            $this->storage->deleteDir(\Wizacha\ImageManager::thumbnailsRootPath($path));
        }
    }


    /**
     * Save image in database and in storage
     * @param array  $file_data (formated like return of self::getUrlData)
     * @param array  $image_data (formated like return of self::getImageSize)
     * @param string $url
     * @return integer image_id
     * @throws BadImage if the file is not an image, or the image is too large
     */
    public function create($file_data, $image_data, $url = '', $params = []): int
    {
        if (!static::isSizeCorrect($image_data[0], $image_data[1])) {
            throw new BadImage('Incorrect image size. It must be smaller than ' . self::MAX_IMAGE_SURFACE . 'px.');
        }

        $keepOrigin = !empty($params['keep_origins']);
        $file_data['name'] = self::escapeName($file_data['name']);
        $extension = pathinfo($file_data['name'])['extension'];

        $image_id = (int) Database::query(
            'INSERT INTO ?:images (image_path, image_x, image_y, image_url) VALUES (?s, ?i, ?i, ?s)',
            $file_data['name'],
            $image_data[0],
            $image_data[1],
            $url
        );

        $file_path = $this->pathForOriginalImage($image_id) . '/' . $file_data['name'];
        $params['file'] = $file_data['path'];

        $params['keep_origins'] = true; //force to keep original file, else, we can't duplicate it in originals folder
        list(,$storage_file_name) = $this->storage->put($file_path, $params);
        if (basename($storage_file_name) != basename($file_path)) {
            Database::query(
                'UPDATE ?:images set image_path=?s WHERE image_id=?i',
                basename($storage_file_name),
                $image_id
            );
        }

        //Put image on new path
        $new_location = 'originals/' . $this->subdirectoryPathFromID($image_id) . "/{$image_id}." . $extension;
        $params['overwrite'] = true;
        $params['keep_origins'] = $keepOrigin;
        fn_delete_image_thumbnails($new_location);
        $this->storage->put($new_location, $params);

        return $image_id;
    }

    /**
     * @param UploadedFile $file
     * @return false|integer image_id
     * @throws BadImage if the file is not an image, or the image is too large
     */
    public function createFromUploadedFile(UploadedFile $file)
    {
        return $this->create(
            [
                'name' => $file->getClientOriginalName(),
                'path' => $file->getPathname()
            ],
            $this->getImageSize($file->getPathname()),
            '' /* $url */,
            ['keep_origins' => true]
        );
    }

    /**
     * Create an image. If $enable_cache is true, check in database if image already exist.
     * @param string  $url
     * @param boolean $enable_cache
     * @return integer image_id
     * @throws BadImage
     */
    public function createByUrl(string $url, bool $enable_cache = true): int
    {
        if (!$url) {
            throw new BadImage('URL is empty');
        }
        if ($enable_cache && $id = Database::getField('SELECT image_id FROM ?:images WHERE image_url = ?s', $url)) {
            return $id;
        }
        $file_data  = $this->getUrlData($url);
        if (empty($file_data)) {
            throw new BadImage('Cannot GET the file');
        }

        if (!\in_array($this->getImageType($file_data['path']), [IMAGETYPE_GIF, IMAGETYPE_JPEG, IMAGETYPE_PNG, IMAGETYPE_ICO])) {
            throw new BadImage('The file is not an image');
        }

        $image_data = $this->getImageSize($file_data['path']);

        if ($image_data === false) {
            throw new BadImage('The file is not an image');
        }

        return $this->create($file_data, $image_data, $url);
    }

    public function getImageType($filename)
    {
        return exif_imagetype($filename);
    }

    /**
     * If pairs exists on object, reuse ids.
     * @param array    $images
     * @param string   $object_type
     * @param integer  $object_id
     * @return boolean true
     */
    public function createPairs(array $images, $object_type, $object_id)
    {
        $current_pairs = $this->getPairs($object_type, $object_id);
        $pairs_id = array_column($current_pairs, 'pair_id');
        $old_images_id = array_column($current_pairs, 'detailed_id');
        if ($old_images_id === $images) {
            return true;
        }
        $deleted_image = array_diff($old_images_id, $images);

        $insert_data  = [];
        foreach ($images as $image_id) {
            if ($object_type === 'logos') {
                $insert_data[] = [
                    'pair_id' => array_pop($pairs_id),
                    'object_id' => $object_id,
                    'object_type' => $object_type,
                    'image_id' => $image_id,
                    'type' => 'A',
                ];
            } else {
                $insert_data[] = [
                    'pair_id' => array_pop($pairs_id),
                    'object_id' => $object_id,
                    'object_type' => $object_type,
                    'detailed_id' => $image_id,
                    'type' => 'A',
                ];
            }
        }
        if (!empty($insert_data)) {
            $insert_data[0]['type'] = 'M';
        }

        try {
            Database::query(
                'DELETE FROM ?:images_links WHERE object_type=?s AND object_id=?s',
                $object_type,
                $object_id
            );
            if (!empty($insert_data)) {
                Database::query('INSERT INTO ?:images_links ?m', $insert_data);
            }
        } catch (\Exception $e) {
            trigger_error($e->getMessage(), E_USER_WARNING);
        }

        array_map([$this, 'delete'], $deleted_image);
        return true;
    }

    /**
     * Not compatible with cscart fn_get_image_pairs
     * @param  string  $object_type
     * @param  integer $object_id
     * @return array   pair_ids
     */
    public function getPairs($object_type, $object_id)
    {
        return Database::getHash(
            'SELECT pair_id, object_id, object_type, image_id, detailed_id, type, position FROM ?:images_links WHERE object_type = ?s AND object_id = ?s',
            'pair_id',
            $object_type,
            $object_id
        );
    }

    /**
     * @param integer $image_id
     */
    protected function countLinks($image_id): int
    {
        return (int) Database::getField(
            "SELECT COUNT(pair_id) FROM ?:images_links WHERE image_id = ?i OR detailed_id = ?i",
            $image_id,
            $image_id
        );
    }

    protected function haveImageInOtherMultiVendorProduct(int $imageId, string $idMultiVendorProduct): bool
    {
        $listImageIds = Database::getField(
            "SELECT image_ids FROM doctrine_multi_vendor_product WHERE id != ?s AND image_ids REGEXP ?s",
            $idMultiVendorProduct,
            "[[:<:]]" . $imageId . "[[:>:]]"
        );

        if ($listImageIds !== null && \in_array($imageId, json_decode($listImageIds)) === true) {
            return true;
        }

        return false;
    }

    /**
     * @param integer $image_id
     *
     * @return string
     */
    public function pathForOriginalImage($image_id)
    {
        return static::DEFAULT_IMAGE_DIRECTORY . '/' . $this->subdirectoryPathFromID($image_id);
    }

    /**
     * @param integer $image_id
     * @return float
     */
    protected function subdirectoryPathFromID($image_id)
    {
        return \strval(floor($image_id / MAX_FILES_IN_DIR));
    }

    /**
     * Helper for database query
     * @param integer $image_id
     * @return array
     */
    protected function imageInfo($image_id)
    {
        return Database::getRow('SELECT * FROM ?:images WHERE image_id = ?i', $image_id);
    }

    /**
     * Present to be mocked. It's not tested
     * @param string $location
     * @return false|array
     */
    public function getUrlData($location)
    {
        return fn_get_url_data($location, true);
    }

    /**
     * Present to be mocked. It's not tested
     * @param string $filename
     * @return array
     */
    public function getImageSize($filename)
    {
        return getimagesize($filename);
    }

    /**
     * @return string|null
     */
    public function getFullPath(\Wizacha\Marketplace\Entities\Image $image)
    {
        if (!$image->getId()) {
            return null;
        }
        return $this->pathForOriginalImage($image->getId()) . '/' . $image->getPath();
    }

    /**
     * @param \Wizacha\Marketplace\Entities\Image $image
     * @return array
     */
    public function normalizeImage(\Wizacha\Marketplace\Entities\Image $image): array
    {
        return [
            'id' => $image->getId(),
            'path' => $this->getFullPath($image),
            'altText' => $this->getImageAltText($image->getId(), (string) GlobalState::contentLocale()),
        ];
    }

    /**
     * @param \Wizacha\Marketplace\Entities\Image[] $images
     * @return array
     */
    public function normalizeImageCollection(array $images): array
    {
        return array_map(
            function ($image) {
                return $this->normalizeImage($image);
            },
            $images
        );
    }

    /**
     * @param int[] $ids
     * @return Marketplace\Entities\Image[]
     */
    public function getImagesFromIds(array $ids)
    {
        return array_map(function ($id) {
            return new \Wizacha\Marketplace\Entities\Image($id);
        }, $ids);
    }

    /**
     * Return a formatted file name
     */
    public static function escapeName($name): string
    {
        $fileArray = pathinfo($name);

        $slugGenerator = new SlugGenerator();
        $nameFormatted = $slugGenerator->slugFromString($fileArray['filename']);

        return $nameFormatted . '.' . $fileArray['extension'];
    }

    public function getInternalUrl(int $imageId, int $width = null, int $height = null, string $protocol = 'short'): string
    {
        // Image ID is unique, but we also need its type to get it. So we get the type in DB
        // Null = image doesn't exists / is used with detailed_id in this table => type = detailed
        $type = Database::getField('SELECT object_type FROM cscart_images_links WHERE image_id = ?i', $imageId) ?? 'detailed';

        $image = fn_get_image($imageId, $type);

        if ($image && !empty($image['relative_path'])) {
            $imagePath = $image['relative_path'];
        } else {
            if ($this->serveFakeImages) {
                $width = $width ?? 1000;
                $height = $height ?? 1000;

                return sprintf("https://unsplash.it/%d/%d?image=%d", $width, $height, crc32($imageId) % 1084);
            }

            throw NotFound::fromId('Image', $imageId);
        }

        if ($width || $height) {
            $url = fn_generate_thumbnail($imagePath, $width, $height, false, $protocol);
            if (!$url) {
                throw NotFound::fromId('Image', $imageId);
            }

            return $url;
        }

        return $this->storage->getUrl($imagePath, $protocol);
    }

    public function getValidImageIds(array $imagesIds): array
    {
        $validImagesIds = db_get_fields("SELECT image_id FROM ?:images WHERE image_id IN (?n)", $imagesIds);

        return array_map(function ($imageId): int {
            return $imageId;
        }, $validImagesIds);
    }

    public function setImageAltText(int $imageId, string $altText, string $langCode): void
    {
        $seoImagesData = [
            'image_id' => $imageId,
            'altText' => $altText,
            'lang_code' => $langCode,
        ];

        db_query("INSERT INTO ?:images_seo ?e", $seoImagesData);
    }

    public function updateImageAltText(int $imageId, string $altText, string $langCode): void
    {
        $seoImagesData = [
            'altText' => $altText,
        ];

        db_query('UPDATE ?:images_seo SET ?u WHERE image_id = ?i AND lang_code = ?s', $seoImagesData, $imageId, $langCode);
    }

    /** @return string|null */
    public function getImageAltText(int $imageId, string $langCode): ?string
    {
        return db_get_field("SELECT altText FROM ?:images_seo WHERE image_id = ?i AND lang_code = ?s", $imageId, $langCode);
    }

    public function deleteImageAltText(int $imageId): void
    {
        db_query("DELETE FROM ?:images_seo WHERE image_id = ?i", $imageId);
    }
}
