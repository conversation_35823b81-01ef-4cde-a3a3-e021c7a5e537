<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha;

use Doctrine\DBAL\ParameterType;
use Wizacha\Marketplace\Shipping\DeliveryType;

/**
 * @deprecated Legacy class. Should be moved into a proper service.
 */
class Shipping
{
    public const EVENT_UPDATE          = 'shipping.update';
    public const EVENT_VENDOR_UPDATE   = 'shipping_vendor.update';

    public const TYPE_STD = 'standard';
    public const TYPE_C2C = 'c2c';
    public const TYPE_ALL = 'all';

    /**
     * Return all availables types in array [type1 , type2, ... ]
     *
     * @return array
     */
    public static function getTypes()
    {
        return [self::TYPE_STD , self::TYPE_C2C, self::TYPE_ALL];
    }

    /**
     * @param string $type
     * @return array
     */
    public static function getByType($type)
    {
        if (!\is_string($type)) {
            return [];
        }
        return \Tygh\Database::getArray(
            "SELECT * FROM ?:shippings
            LEFT JOIN ?:shipping_descriptions ON ?:shippings.shipping_id = ?:shipping_descriptions.shipping_id
            WHERE (w_type = ?s OR w_type = ?s)
                AND status = 'A'
            ;",
            $type,
            self::TYPE_ALL
        );
    }

    /**
     * @param integer $shippingId
     */
    public static function isHandDelivery($shippingId): bool
    {
        return static::isHandDeliveryWithCode($shippingId) || static::isHandDeliveryWithoutCode($shippingId);
    }

    /**
     * @param integer $shippingId
     */
    public static function isHandDeliveryWithCode($shippingId): bool
    {
        return \Tygh\Database::getField(
            'SELECT w_delivery_type FROM ?:shippings WHERE shipping_id = ?i',
            $shippingId
        ) == (string) DeliveryType::HAND_WITH_CODE();
    }

    /**
     * @param integer $shippingId
     */
    public static function isHandDeliveryWithoutCode($shippingId): bool
    {
        return \Tygh\Database::getField(
            'SELECT w_delivery_type FROM ?:shippings WHERE shipping_id = ?i',
            $shippingId
        ) == (string) DeliveryType::HAND_WITHOUT_CODE();
    }

    public static function isChronopost(string $shippingType): bool
    {
        return $shippingType == (string) DeliveryType::CHRONO_13() || $shippingType == (string) DeliveryType::CHRONO_RELAIS();
    }

    /**
     * @param integer $shippingId
     */
    public static function isChrono13($shippingId): bool
    {
        return \Tygh\Database::getField(
            'SELECT w_delivery_type FROM ?:shippings WHERE shipping_id = ?i',
            $shippingId
        ) == (string) DeliveryType::CHRONO_13();
    }

    /**
     * @param integer $shippingId
     */
    public static function isChronoRelais($shippingId): bool
    {
        return \Tygh\Database::getField(
            'SELECT w_delivery_type FROM ?:shippings WHERE shipping_id = ?i',
            $shippingId
        ) === (string) DeliveryType::CHRONO_RELAIS();
    }

    /**
     * @param int $shippingId
     */
    public static function isMondialRelay(int $shippingId): bool
    {
        return \Tygh\Database::getField(
            'SELECT w_delivery_type FROM ?:shippings WHERE shipping_id = ?i',
            $shippingId
        ) === (string) DeliveryType::MONDIAL_RELAY();
    }

    /**
     * @param integer $shippingId
     */
    public static function isPickupPoint($shippingId): bool
    {
        $shippingType = \Tygh\Database::getField(
            'SELECT w_delivery_type FROM ?:shippings WHERE shipping_id = ?i',
            $shippingId
        );

        return $shippingType === (string) DeliveryType::CHRONO_RELAIS()
            || $shippingType === (string) DeliveryType::MONDIAL_RELAY();
    }

    public static function getDeliveryType(int $shippingId): ?DeliveryType
    {
        $deliveryType = \Tygh\Database::getField(
            'SELECT w_delivery_type FROM ?:shippings WHERE shipping_id = ?i',
            $shippingId
        );

        if (\strlen($deliveryType) === 0) {
            return null;
        }

        return new DeliveryType($deliveryType);
    }

    public static function isStandardDelivery(int $shippingId): bool
    {
        if (self::getDeliveryType($shippingId) === null) {
            return false;
        }

        return self::getDeliveryType($shippingId)->equals(DeliveryType::STANDARD());
    }

    /**
     * Is this shipping meant to be free ? (value forced to 0)
     *
     * @param int $shippingId
     * @return bool
     */
    public static function isFree($shippingId)
    {
        return static::isHandDelivery($shippingId);
    }

    /**
     * All handle delivery required code
     * @param integer $shippingId
     * @return boolean
     */
    public static function requiredCode($shippingId)
    {
        return static::isHandDeliveryWithCode($shippingId);
    }

    /**
     * @param string $table
     * @param int $shipping_id
     * @param array $rates
     * @param string $extra_column
     * @param int $extra_column_id
     * @param int $company_id
     * @param float|null $carriagePaidThreshold
     * @return bool
     */
    public static function updateRate(
        $table,
        $shipping_id,
        $rates,
        $extra_column,
        $extra_column_id,
        $company_id = 0,
        $carriagePaidThreshold = null
    ) {
        $success = true;

        foreach ($rates as $destination_id => $rate) {
            if (!empty($rate['destination_id'])) {
                $destination_id = $rate['destination_id'];
            }
            $normalized_data = array();

            if ((trim($rate['rate_value']['I'][0]['value'])) === '') {
                \Tygh\Database::query("DELETE FROM ?:$table WHERE shipping_id = ?i AND destination_id = ?i AND ?f = ?i", $shipping_id, $destination_id, $extra_column, $extra_column_id);
                continue;
            }

            $shipping_is_free = self::isFree($shipping_id);

            for ($i = 0; $i < 2; ++$i) {
                if ($rate['rate_value']['I'][$i]['value'] < 0) {
                    $rate['rate_value']['I'][$i]['value'] = 0;
                    fn_set_notification('E', __('error'), __('w_rate_value_negative'));
                    $success = false;
                }

                //if shipping is meant to be free, force all amounts to 0
                if ($shipping_is_free) {
                    $rate['rate_value']['I'][$i]['value'] = 0;
                }
            }

            for ($i = 0; $i < \count($rate['rate_value']['I']); $i++) {
                $rate['rate_value']['I'][$i]['value'] = str_replace(',', '.', $rate['rate_value']['I'][$i]['value']);
            }

            if ($rate['rate_value']['I'][1]['value'] > $rate['rate_value']['I'][0]['value']) {
                $rate['rate_value']['I'][1]['value'] = $rate['rate_value']['I'][0]['value'];
                fn_set_notification('E', __('error'), __('w_set_rate1_to_rate0'));
                $success = false;
            }

            // Update rate values
            if (!empty($rate['rate_value']['I']) && \is_array($rate['rate_value']['I'])) {
                fn_normalized_shipping_rate($normalized_data, $rate['rate_value']['I'], 'I');
            }

            // Add new rate values
            if (!empty($shipping_data['add_rates']) && \is_array($shipping_data['add_rates'][$destination_id]['rate_value']['I'])) {
                fn_normalized_shipping_rate($normalized_data, $shipping_data['add_rates'][$destination_id]['rate_value']['I'], 'I');
            }

            if (isset($rate['rate_value']['I'][0]['location'])) {
                $normalized_data['I'][0]['location'] = $rate['rate_value']['I'][0]['location'];
                if ($rate['rate_value']['I'][0]['latitude'] && $rate['rate_value']['I'][0]['longitude']) {
                    $normalized_data['I'][0]['latitude'] = \floatval($rate['rate_value']['I'][0]['latitude']);
                    $normalized_data['I'][0]['longitude'] = \floatval($rate['rate_value']['I'][0]['longitude']);
                    if ($rate['rate_value']['I'][0]['postal']) {
                        $normalized_data['I'][0]['postal'] = $rate['rate_value']['I'][0]['postal'];
                    }
                }
            }

            if (!empty($normalized_data['I']) && \is_array($normalized_data['I'])) {
                ksort($normalized_data['I'], SORT_NUMERIC);
            }

            $normalized_data = serialize($normalized_data);
            $connection = container()->get('database_connection');

            // We only want to manage free shipping threshold for the companies for now
            if ('w_company_shipping_rates' === $table) {
                // Company rates update
                $connection->executeQuery(
                    "INSERT INTO cscart_{$table}(`rate_value`, `shipping_id`, `destination_id`, `carriage_paid_threshold`, `company_id`)
                    VALUES (?, ?, ?, ?, ?)
                    ON DUPLICATE KEY UPDATE
                        `rate_value` = VALUES(`rate_value`),
                        `destination_id` = VALUES(`destination_id`),
                        `carriage_paid_threshold` = VALUES(`carriage_paid_threshold`)

                ",
                    [
                        $normalized_data,
                        $shipping_id,
                        $destination_id,
                        $carriagePaidThreshold,
                        $extra_column_id
                    ]
                );
            } else {
                $connection->executeQuery(
                    "INSERT INTO cscart_{$table}(`rate_value`, `shipping_id`, `destination_id`, `product_id`)
                    VALUES (?, ?, ?, ?)
                    ON DUPLICATE KEY UPDATE
                        `rate_value` = VALUES(`rate_value`),
                        `destination_id` = VALUES(`destination_id`)
                    ",
                    [
                        $normalized_data,
                        $shipping_id,
                        $destination_id,
                        $extra_column_id
                    ]
                );
            }
        }

        if ($extra_column == 'product_id') {
            \Wizacha\Events\Config::dispatch(
                \Wizacha\Product::EVENT_UPDATE,
                (new \Wizacha\Events\IterableEvent())->setElement($extra_column_id)
            );
        } else {
            \Wizacha\Events\Config::dispatch(
                \Wizacha\Shipping::EVENT_VENDOR_UPDATE,
                (new \Wizacha\Events\IterableEvent())->setElement(['shipping_id' => $shipping_id, 'company_id' => $company_id])
            );
        }
        return $success;
    }

    /**
     * @param int $shipping_id
     * @param int $product_id
     * @param string $new_status
     * @return bool
     */
    public static function changeStatusOnProduct($shipping_id, $product_id, $new_status)
    {
        /** @var ProductManager */
        $productManager = container()->get('cscart.product_manager');
        // invalidate productManager cache
        $productManager->invalidate($product_id);

        $DB_content = \Tygh\Database::getField(
            "SELECT w_disable_shippings FROM ?:products WHERE product_id = ?i",
            $product_id
        );

        $disabled_shippings = empty($DB_content) ? [] : explode(',', $DB_content);

        $disabled = \in_array($shipping_id, $disabled_shippings);

        if ($disabled && $new_status == \Wizacha\Status::ENABLED) {
            $key = array_search($shipping_id, $disabled_shippings);
            unset($disabled_shippings[$key]);
            $shippings_req = implode(',', $disabled_shippings);
        } elseif (!$disabled && $new_status == \Wizacha\Status::DISABLED) {
            $disabled_shippings[] = $shipping_id;
            $shippings_req = implode(',', $disabled_shippings);
        } else {
            return true;
        }

        return \boolval(\Tygh\Database::query(
            "UPDATE ?:products SET w_disable_shippings = ?s WHERE product_id = ?i",
            $shippings_req,
            $product_id
        ));
    }
}
