<?php

use Wizacha\Money\Money;

/**
 * Smarty modifier that formats a `Wizacha\Money` instance.
 *
 * To use it in templates: `{ $price|format_money }`
 *
 * @return string
 */
function smarty_modifier_format_money(Money $money)
{
    $serviceName = AREA === 'C' ? 'marketplace.price.formatter' : 'marketplace.backend.price.formatter';
    return container()->get($serviceName)->formatFloat($money->getConvertedAmount());
}
