<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

/**
 * @param array $params
 * * route string Name of the route
 * @return string
 * @throws Exception
 */
function smarty_function_path(array $params)
{
    $router = container()->get('router');
    $route = $params['route'];
    unset($params['route']);
    return $router->generate($route, $params, \Symfony\Component\Routing\Generator\UrlGeneratorInterface::ABSOLUTE_PATH);
}
