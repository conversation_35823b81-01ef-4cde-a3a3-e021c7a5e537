<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

/**
 * @see \Wizacha\AppBundle\Twig\AppExtension::includeScripts
 */
function smarty_function_include_scripts(): string
{
    $storage = container()->get("Wizacha\Storage\AssetsStorageService");

    $files = array_filter(
        $storage->getList(),
        function ($file) {
            return (new \SplFileInfo($file))->getExtension() === 'js';
        }
    );

    $html = '';
    foreach ($files as $file) {
        $url = $storage->getUrl($file, 'short');
        $html .= sprintf('<script type="text/javascript" src="%s"></script>', $url);
    }

    $html .= container()->get('app.captcha')->getJS();

    return $html;
}
