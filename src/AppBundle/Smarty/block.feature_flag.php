<?php
/**
 * Smarty plugin
 * @package Smarty
 * @subpackage plugins
 */

function smarty_block_feature_flag($params, $content, &$smarty, &$repeat)
{
    if ($repeat) {
        return;
    }
    $container = container();
    $name = 'feature.' . $params['feature'];
    if (!empty($params['feature'])) {
        if ($container->hasParameter($name)
            && $container->getParameter($name) == true
        ) {
            return $content;
        }
    }
}
