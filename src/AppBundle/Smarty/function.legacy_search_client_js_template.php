<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

use Wizacha\Registry;

/**
 * @param array $params
 * @param $smarty
 * @return mixed
 * @throws string
 */
function smarty_function_legacy_search_client_js_template(array $params)
{
    static $jsTemplate;

    if (!$jsTemplate) {
        $registry = Registry::defaultInstance();
        $jsTemplate = $registry->container->get('marketplace.search_engine')->getJsClientTemplate();
    }

    return $jsTemplate;
}
