<?php

/*
 * Smarty plugin
 * -------------------------------------------------------------
 * File:     modifier.is_order_refused_by_vendor.php
 * Type:     modifier
 * Name:     is_order_refused_by_vendor
 * Purpose:  Vérifie si une commande est refusée par le vendeur
 * -------------------------------------------------------------
 */

function smarty_modifier_is_order_refused_by_vendor(int $orderId): bool
{
    return container()->get('marketplace.order.order_service')->isRefusedByVendor($orderId);
}
