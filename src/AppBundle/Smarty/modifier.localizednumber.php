<?php
/*
 * Smarty plugin
 * -------------------------------------------------------------
 * File:     modifier.localizednumber.php
 * Type:     modifier
 * Name:     localizednumber
 * Purpose:  Formate un nombre
 * -------------------------------------------------------------
 */

function smarty_modifier_localizednumber($number, $style = 'decimal', $type = 'default', $locale = null)
{
    static $typeValues = array(
        'default' => NumberFormatter::TYPE_DEFAULT,
        'int32' => NumberFormatter::TYPE_INT32,
        'int64' => NumberFormatter::TYPE_INT64,
        'double' => NumberFormatter::TYPE_DOUBLE,
        'currency' => NumberFormatter::TYPE_CURRENCY,
    );

    $formatter = fn_get_number_formatter($locale, $style);

    if (!isset($typeValues[$type])) {
        throw new InvalidArgumentException(sprintf('The type "%s" does not exist. Known types are: "%s"', $type, implode('", "', array_keys($typeValues))));
    }

    return $formatter->format($number, $typeValues[$type]);
}
