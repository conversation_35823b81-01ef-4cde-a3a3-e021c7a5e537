<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

/**
 * @param array $params
 * * route string Name of the route
 * @return string
 * @throws Exception
 */
function smarty_function_url(array $params)
{
    $router = container()->get('router');
    $route = $params['route'];
    unset($params['route']);


    $ignoreBaseUrl = !empty($params['ignoreBaseUrl']);
    if ($ignoreBaseUrl) {
        unset($params['ignoreBaseUrl']);
        $oldBaseUrl = $router->getContext()->getBaseUrl();
        $router->getContext()->setBaseUrl('');
    }

    $url = $router->generate($route, $params, \Symfony\Component\Routing\Generator\UrlGeneratorInterface::ABSOLUTE_URL);

    if ($ignoreBaseUrl) {
        $router->getContext()->setBaseUrl($oldBaseUrl);
    }

    return $url;
}
