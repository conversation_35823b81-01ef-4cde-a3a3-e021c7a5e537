<?php

/**
 * Smarty plugin
 * @package Smarty
 * @subpackage plugins
 */

function smarty_function_format_price($params, &$smarty)
{
    $price = $params["price"] ?: 0;

    $serviceName = AREA === 'C' ? 'marketplace.price.formatter' : 'marketplace.backend.price.formatter';
    $response = container()->get($serviceName)->formatFloat(\floatval($price));

    if ($params["assign"]) {
        $smarty->assign($params["assign"], $response);
    } else {
        return $response;
    }
}
