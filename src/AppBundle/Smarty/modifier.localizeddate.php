<?php

/*
 * Smarty plugin
 * -------------------------------------------------------------
 * File:     modifier.localizeddate.php
 * Type:     modifier
 * Name:     localizeddate
 * Purpose:  Formate la date et l'heure sous forme de chaîne
 * -------------------------------------------------------------
 */

function smarty_modifier_localizeddate($date, $dateFormat = 'medium', $timeFormat = 'medium', $locale = null, $timezone = null, $format = null, $calendar = 'gregorian')
{
    $date = fn_date_converter($date, $timezone);

    $formatValues = array(
        'none' => IntlDateFormatter::NONE,
        'short' => IntlDateFormatter::SHORT,
        'medium' => IntlDateFormatter::MEDIUM,
        'long' => IntlDateFormatter::LONG,
        'full' => IntlDateFormatter::FULL,
    );

    $formatter = IntlDateFormatter::create(
        $locale,
        $formatValues[$dateFormat],
        $formatValues[$timeFormat],
        $date->getTimezone(),
        'gregorian' === $calendar ? IntlDateFormatter::GREGORIAN : IntlDateFormatter::TRADITIONAL,
        $format
    );

    return $formatter->format($date->getTimestamp());
}
