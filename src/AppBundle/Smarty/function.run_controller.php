<?php

use Symfony\Component\HttpKernel\Controller\ControllerReference;
use Wizacha\Registry;

/**
 * Render a Symfony controller and return its response content.
 *
 * @return string
 */
function smarty_function_run_controller(array $params)
{
    $fragmentHandler = Registry::defaultInstance()->container->get('fragment.handler');

    $controller = (string) $params['controller'];
    $attributes = array_diff_key($params, array_flip(['controller']));

    $uri = new ControllerReference($controller, $attributes);

    return $fragmentHandler->render($uri);
}
