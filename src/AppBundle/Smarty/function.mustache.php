<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

use Wizacha\Registry;

/**
 * @param array $params
 * @param $smarty
 * @return mixed
 * @throws string
 */
function smarty_function_mustache(array $params, &$smarty)
{
    $registry = Registry::defaultInstance();
    $mustache = $registry->container->get('templating.mustache');

    $mustacheParams = (\array_key_exists('params', $params) && \is_array($params['params'])) ? $params['params'] : [];

    return $mustache->render($smarty->fetch($params['template']), $mustacheParams);
}
