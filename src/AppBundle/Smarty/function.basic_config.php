<?php

/**
 * @param array $params
 * @return string|void
 *
 * Smarty function for reading saved configuration variables.
 *
 * If the key parameter is valid, the value is return.
 * If the var parameter is passed, the value is stored in a variable with the given name and an empty string is returned
 */
function smarty_function_basic_config($params, Smarty_Internal_Template $smarty)
{
    static $values;

    if (null === $values) {
        $service = container()->get("app.configuration_service");
        $values = $service->readConfiguration();
    }

    $hasVarExport = isset($params["var"]);
    $varName = $hasVarExport ? (string) $params["var"] : "";
    $configValue = "";

    if (isset($params["key"]) && isset($values[$params["key"]])) {
        $configValue = $values[$params["key"]];
    }

    if ($hasVarExport) {
        $smarty->assign($varName, $configValue);
        return "";
    }

    return $configValue;
}
