<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

use Tygh\Languages\Values;

/**
 * Export a translation to JS.
 *
 * Returns valid Javascript code, so must be inside Javascript tags.
 *
 * @return string
 */
function smarty_function_export_trans($params)
{
    $safeId = json_encode($params['id']);
    $safeTranslation = json_encode(Values::getLangVar($params['id']));

    return "translations[$safeId] = $safeTranslation;";
}
