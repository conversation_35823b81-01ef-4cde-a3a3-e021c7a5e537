<?php

use Symfony\Component\VarDumper\Dumper\HtmlDumper;
use Wizacha\Registry;

/**
 * Dump a variable.
 *
 * Inspired by Symfony/Twig's dump() function.
 */
function smarty_function_dump($params): string
{
    if (\defined('DEVELOPMENT') && DEVELOPMENT) {
        $cloner = Registry::defaultInstance()->container->get('var_dumper.cloner');

        $dump = fopen('php://memory', 'r+b');
        $dumper = new HtmlDumper($dump);
        $dumper->dump($cloner->cloneVar($params['var']));
        rewind($dump);

        return stream_get_contents($dump);
    }

    return '';
}
