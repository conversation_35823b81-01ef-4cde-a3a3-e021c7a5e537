<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Translation;

use Symfony\Component\Translation\Extractor\PhpExtractor;
use Symfony\Component\Finder\Finder;
use Symfony\Component\Translation\MessageCatalogue;

class SmartyExtractor extends PhpExtractor
{
    private $smarty;

    /**
     * The sequence that captures translation messages.
     *
     * @var array
     */
    protected $sequences = [
        [
            '__',
            '(',
            self::MESSAGE_TOKEN,
            ',',
            self::METHOD_ARGUMENTS_TOKEN,
        ],
        [
            '__',
            '(',
            self::MESSAGE_TOKEN,
        ],
    ];

    public function __construct(\Smarty $smarty, string $projectDir)
    {
        $this->smarty = $smarty;
        // This plugin directory is needed for backend design
        $this->smarty->addPluginsDir($projectDir . '/app/functions/smarty_plugins/');
    }

    public function extract($resource, MessageCatalogue $catalog)
    {
        $files = $this->extractFiles($resource);

        foreach ($files as $file) {
            try {
                $code = $this->parseTemplate($file->getRealPath());
            } catch (\Exception $ex) {
                // Sometimes smarty can't parse templates. For example, the tag
                // {dropdown}
                continue;
            }

            $this->parseTokens(token_get_all($code), $catalog);

            if (\PHP_VERSION_ID >= 70000) {
                // PHP 7 memory manager will not release after token_get_all(), see https://bugs.php.net/70098
                gc_mem_caches();
            }
        }
    }

    protected function canBeExtracted($file)
    {
        return $this->isFile($file) && 'tpl' === pathinfo($file, PATHINFO_EXTENSION);
    }

    protected function extractFromDirectory($resource)
    {
        $finder = new Finder();

        return $finder
            ->files()
            ->name('*.tpl')
            ->in($resource)
        ;
    }

    /**
     * Get PHP code from smarty template
     *
     * @return string
     */
    private function parseTemplate(string $pathname)
    {
        $template = $this->smarty->createTemplate($pathname);

        return $template->compiler->compileTemplate($template);
    }
}
