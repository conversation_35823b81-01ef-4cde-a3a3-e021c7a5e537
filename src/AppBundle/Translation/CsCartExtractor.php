<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Translation;

use Symfony\Component\Translation\Extractor\PhpExtractor;
use Symfony\Component\Finder\Finder;

class CsCartExtractor extends PhpExtractor
{
    /**
     * The sequence that captures translation messages.
     *
     * @var array
     */
    protected $sequences = [
        [
            '__',
            '(',
            self::MESSAGE_TOKEN,
            ',',
            self::METHOD_ARGUMENTS_TOKEN,
        ],
        [
            '__',
            '(',
            self::MESSAGE_TOKEN,
        ],
    ];

    /**
     * @param string|array $directory
     *
     * @return array
     */
    protected function extractFromDirectory($directory)
    {
        $finder = new Finder();

        return $finder
            ->files()
            ->name('*.php')
            ->in($directory)
        ;
    }
}
