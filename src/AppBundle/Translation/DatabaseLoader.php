<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Translation;

use Doctrine\DBAL\Connection;
use Symfony\Component\Translation\Loader\LoaderInterface;
use Symfony\Component\Translation\MessageCatalogue;

class DatabaseLoader implements LoaderInterface
{
    /** @var Connection */
    private $dbal;

    public function __construct(Connection $dbal)
    {
        $this->dbal = $dbal;
    }
    /**
     * {@inheritdoc}
     */
    public function load($resource, $locale, $domain = 'messages')
    {
        $catalogue = new MessageCatalogue($locale);

        $statement = $this->dbal->executeQuery('SELECT * FROM cscart_language_values WHERE lang_code = :locale', [
            'locale' => $locale,
        ]);

        while ($var = $statement->fetch()) {
            // cscart uses [n] for number placeholder, symfony needs %count%
            $value = str_replace('[n]', '%count%', $var['value']);
            $catalogue->set($var['name'], $value, $domain);
        }

        return $catalogue;
    }
}
