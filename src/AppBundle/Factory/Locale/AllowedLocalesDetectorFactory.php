<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Factory\Locale;

use Wizacha\Component\Locale\AllowedLocalesDetector;
use Wizacha\Component\Locale\LocaleDetectorInterface;

class AllowedLocalesDetectorFactory
{
    public static function make(LocaleDetectorInterface $detector): AllowedLocalesDetector
    {
        $showHiddenLanguages = AREA != 'C' ? true : false;
        $availLanguages = fn_get_avail_languages($area, $showHiddenLanguages);

        return new AllowedLocalesDetector(
            $detector,
            array_keys($availLanguages)
        );
    }
}
