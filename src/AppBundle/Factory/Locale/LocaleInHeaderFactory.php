<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Factory\Locale;

use Wizacha\Component\Locale\LocaleInHeader;

class LocaleInHeaderFactory
{
    public static function make(): LocaleInHeader
    {
        $showHiddenLanguages = AREA != 'C' ? true : false;
        $availLanguages = fn_get_avail_languages($area, $showHiddenLanguages);

        return new LocaleInHeader(array_keys($availLanguages));
    }
}
