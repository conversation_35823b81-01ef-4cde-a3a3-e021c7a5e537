<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Backend\Menu;

use Symfony\Component\HttpFoundation\Session\Session;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Payment\Processor\MangoPay;
use Wizacha\Marketplace\Payment\Processor\Stripe;
use Wizacha\Marketplace\PSP\UboMangopay\UboMangopayService;
use Wizacha\Registry;
use Wizacha\User;

class MenuExtension
{
    /** @var Session */
    protected $session;

    /** @var bool */
    protected $multiVendorProductFlag;

    /** @var bool */
    protected $multiVendorProductSyncFlag;

    /** @var bool */
    private $enableOrganisations;

    /** @var bool */
    private $stripeConfigured;

    /** @var bool */
    private $enableAvailableOffers;

    /** @var bool */
    private $marketplaceCouponsFlag;

    /** @var bool */
    private $enableSubscription;

    /** @var bool */
    private $enableYavin;

    /** @var User */
    private $user = null;

    /** @var bool */
    private $featureCurrencyAdvanced;

    private bool $mangopayConfigured;
    private CompanyService $companyService;
    private UboMangopayService $uboMangopayService;
    private bool $featureUserGroupEnabled;

    public function __construct(
        Session $session,
        bool $multiVendorProductFlag,
        bool $multiVendorProductSyncFlag,
        bool $enableOrganisations,
        Stripe $stripe,
        bool $enableAvailableOffers,
        bool $marketplaceCouponsFlag,
        bool $enableSubscription,
        bool $featureCurrencyAdvanced,
        MangoPay $mangopay,
        CompanyService $companyService,
        UboMangopayService $uboMangopayService,
        bool $featureUserGroupEnabled,
        bool $enableYavin
    ) {
        $this->session = $session;
        $this->multiVendorProductFlag = $multiVendorProductFlag;
        $this->multiVendorProductSyncFlag = $multiVendorProductSyncFlag;
        $this->enableOrganisations = $enableOrganisations;
        $this->stripeConfigured = $stripe->isConfigured();
        $this->enableAvailableOffers = $enableAvailableOffers;
        $this->marketplaceCouponsFlag = $marketplaceCouponsFlag;
        $this->enableSubscription = $enableSubscription;
        $this->featureCurrencyAdvanced = $featureCurrencyAdvanced;
        $this->mangopayConfigured = $mangopay->isConfigured();
        $this->companyService = $companyService;
        $this->uboMangopayService = $uboMangopayService;
        $this->featureUserGroupEnabled = $featureUserGroupEnabled;
        $this->enableYavin = $enableYavin;
    }

    public function customizeMenu(array $menuSchema): array
    {
        $companyId = Registry::defaultInstance()->get(['runtime', 'company_id']);

        $menuSchema['top']['administration']['items']['emails'] = [
            'href' => 'admin_Notification_list',
            'symfony' => true,
            'position' => 705,
            'superadmin_only' => true,
        ];

        $menuSchema['top']['administration']['items']['api_access'] = [
            'href' => 'admin_api_tokens',
            'position' => 1500,
            'symfony' => true,
        ];

        if ($this->enableYavin === true) {
            $menuSchema['top']['administration']['items']['notifications'] = [
                'href' => 'admin_notifications_manage',
                'position' => 1650,
                'symfony' => true,
            ];
        }

        $menuSchema['top']['administration']['items']['mail_variables'] = [
            'href' => 'admin_mail_variables_list',
            'position' => 705,
            'symfony' => true,
            'superadmin_only' => true,
        ];

        if ($this->multiVendorProductFlag) {
            // Ce menu est uniquement accessible aux administrateurs de la marketplace
            if ($this->getUser() && $this->getUser()->isAdmin()) {
                $menuSchema['central']['catalog']['items']['multi_vendor_products'] = [
                    'href' => 'admin_MultiVendorProduct_list',
                    'symfony' => true,
                    'position' => 150,
                ];
            }

            // Ce menu est uniquement accessible aux vendeurs, alors on ne l'affiche pas pour les admin
            if ($this->multiVendorProductSyncFlag && $this->getUser() && $this->getUser()->isVendor()) {
                $menuSchema['central']['catalog']['items']['catalog_sync_rules'] = [
                    'href' => 'admin_MultiVendorProduct_productSyncFromCatalog',
                    'position' => 200,
                    'symfony' => true,
                ];
            }
        }

        $menuSchema['top']['administration']['items']['order_statuses']['superadmin_only'] = true;

        if ($this->getUser() && $this->getUser()->isProfessionalVendor()) {
            $menuSchema['top']['administration']['items']['discuss_menu'] = [
                'href' => 'admin_Discuss_list',
                'position' => 150,
                'symfony' => true,
                'count_function' => 'getDiscussCount',
            ];
        }

        if ($this->enableOrganisations) {
            $menuSchema['central']['organisations'] = [
                'items' => [
                    'organisations' => [
                        'href' => 'admin_organisation_list',
                        'symfony' => true,
                    ],
                ],
                'position' => 700,
            ];
        }

        if ($this->stripeConfigured && $companyId = Registry::defaultInstance()->get(['runtime', 'company_id'])) {
            $menuSchema['central']['my_account']['items']['stripe_tos_acceptance'] = [
                'href' => 'admin_stripe_tos_acceptance',
                'symfony' => true,
                'count_function' => \is_null((new Company($companyId))->getStripeId()) ? 'returnAlwaysOne' : null,
            ];
        }

        if ($this->mangopayConfigured === true
            && $companyId > 0
            && $this->companyService->get($companyId)->isPrivateIndividual() === false
            && $this->getUser() !== null
            && ($this->getUser()->isVendor() === true || $this->getUser()->isAdmin() === true)
        ) {
            $menuSchema['central']['my_account']['items']['mangopay_inscription'] = [
                'href' => 'company_mangopay_inscription',
                'symfony' => true,
                'count_function' => 'isUBONotSubmitted',
            ];
        }

        if ($this->enableAvailableOffers) {
            $menuSchema['top']['administration']['items']['export_data']['subitems']['available_offers_divisions'] = [
                'href' => 'exim.export?section=divisions',
                'position' => 50,
            ];

            $menuSchema['top']['administration']['items']['import_data']['subitems']['available_offers_divisions'] = [
                'href' => 'exim.import?section=available_offers_divisions',
                'position' => 950,
            ];

            if ($this->getUser() && $this->getUser()->isAdmin()) {
                $menuSchema['top']['administration']['items']['available_offers_divisions'] = [
                    'href' => 'available_offers.update',
                    'position' => 1600,
                ];
            }
        }

        if ($this->multiVendorProductFlag) {
            $menuSchema['top']['administration']['items']['export_data']['subitems']['multi_vendor_products'] = [
                'href' => 'exim.export?section=multi_vendor_products',
                'position' => 600,
            ];

            $menuSchema['top']['administration']['items']['import_data']['subitems']['multi_vendor_products'] = [
                'href' => 'exim.import?section=multi_vendor_products',
                'position' => 1100,
            ];
        }

        if ($this->marketplaceCouponsFlag && Registry::defaultInstance()->get(['runtime', 'company_id']) === 0) {
            $menuSchema['central']['marketing']['items']['marketplace_promotions'] = [
                'href' => 'promotions_marketplace.manage',
                'position' => 300,
            ];

            $menuSchema['top']['administration']['items']['export_data']['subitems']['marketplace_discounts'] = [
                'href' => 'exim.export?section=marketplace_discounts',
                'position' => 700,
            ];
        }

        if (Registry::defaultInstance()->get(['runtime', 'company_id']) === 0) {
            $menuSchema['top']['administration']['items']['export_data']['subitems']['companies'] = [
                'href' => 'exim.export?section=companies',
                'position' => 800,
            ];
        }

        $menuSchema['top']['administration']['items']['import_data']['subitems']['users'] = [
            'href' => 'exim.import?section=users',
            'position' => 1500,
            'superadmin_only' => true,
        ];

        $menuSchema['top']['administration']['items']['export_data']['subitems']['related_products'] = [
            'href' => 'exim.export?section=related_products',
            'position' => 250,
        ];

        $menuSchema['top']['administration']['items']['import_data']['subitems']['related_products'] = [
            'href' => 'exim.import?section=related_products',
            'position' => 250,
        ];

        $menuSchema['top']['administration']['items']['import_data']['subitems']['product_attributes'] = [
            'href' => 'exim.import?section=product_attributes',
            'position' => 260,
        ];

        $menuSchema['top']['administration']['items']['import_data']['subitems']['features'] = [
            'href' => 'exim.import?section=features',
            'position' => 270,
        ];

        if (true === $this->enableSubscription) {
            $menuSchema['central']['subscriptions'] = [
                'items' => [
                    'subscriptions' => [
                        'href' => 'subscriptions.manage',
                    ],
                ],
                'position' => 800,
            ];
        }

        $menuSchema['top']['administration']['items']['export_data']['subitems']['product_attributes'] = [
            'href' => 'exim.export?section=product_attributes',
            'position' => 250,
        ];

        if ($this->getUser() !== null
            && $this->getUser()->isAdmin() === true
            && $this->featureCurrencyAdvanced === true
            && Registry::defaultInstance()->get(['runtime', 'company_id']) === 0
        ) {
            $menuSchema['top']['administration']['items']['currencies_name'] = [
                'href' => 'currency_management',
                'symfony' => true,
                'position' => 500,
            ];
        }

        if ($this->getUser() !== null
            && $this->getUser()->isAdmin() === true
            && $this->featureUserGroupEnabled === true
            && Registry::defaultInstance()->get(['runtime', 'company_id']) === 0
        ) {
            $menuSchema['top']['administration']['items']['import_data']['subitems']['user_groups_list'] = [
                'href' => 'user_groups_list',
                'symfony' => true,
                'position' => 1500,
            ];
        }

        return $menuSchema;
    }

    /**
     * Returns the CS Cart user.
     */
    public function getUser(): ?User
    {
        if ($this->user === null) {
            $userId = $this->session->get('auth')['user_id'];

            return $userId ? new User($userId) : null;
        }

        return $this->user;
    }
}
