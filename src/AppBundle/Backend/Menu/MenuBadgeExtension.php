<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Backend\Menu;

use Doctrine\DBAL\Connection;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\Order\RmaStatus;
use Wizacha\Marketplace\PSP\UboMangopay\UboMangopayService;
use Wizacha\Premoderation;
use Wizacha\Status;

class MenuBadgeExtension
{
    /**
     * @var Connection
     */
    private $db;

    /** @var UboMangopayService */
    private $uboMangopayService;

    public function __construct(
        Connection $db,
        UboMangopayService $uboMangopayService
    ) {
        $this->db = $db;
        $this->uboMangopayService = $uboMangopayService;
    }

    public function getOrderCount(int $companyId = null): int
    {
        if ($companyId) {
            return (int) $this->db->fetchColumn(
                'SELECT COUNT(1) FROM cscart_orders WHERE status = :status AND company_id = :companyId',
                ['status' => OrderStatus::STANDBY_VENDOR, 'companyId' => $companyId]
            );
        }

        return (int) $this->db->fetchColumn(
            'SELECT COUNT(1) FROM cscart_orders WHERE status = :status',
            ['status' => OrderStatus::STANDBY_VENDOR]
        );
    }

    public function getDiscussCount(): int
    {
        return container()->get('app.discuss_service')->getDiscussCount((int) $_SESSION['auth']['user_id']);
    }

    public function getRmaRequestedCount(int $companyId = null): int
    {
        if (\is_int($companyId)) {
            return (int) $this->db->fetchColumn(
                'SELECT COUNT(1) FROM cscart_rma_returns r
                INNER JOIN cscart_orders o ON o.order_id = r.order_id
                WHERE r.status = :status AND o.company_id = :companyId',
                ['status' => RmaStatus::REQUESTED, 'companyId' => $companyId]
            );
        }

        return (int) $this->db->fetchColumn(
            'SELECT COUNT(1) FROM cscart_rma_returns WHERE status = :status',
            ['status' => RmaStatus::REQUESTED]
        );
    }

    public function getRmaReceivedCount(int $companyId = null): int
    {
        if (\is_int($companyId)) {
            return (int) $this->db->fetchColumn(
                'SELECT COUNT(1) FROM cscart_rma_returns returns
                INNER JOIN cscart_orders orders ON orders.order_id = returns.order_id
                WHERE returns.status = :status AND company_id = :company',
                ['status' => RmaStatus::RECEIVED, 'company' => $companyId]
            );
        }

        return (int) $this->db->fetchColumn(
            'SELECT COUNT(1) FROM cscart_rma_returns WHERE status = :status',
            ['status' => RmaStatus::RECEIVED]
        );
    }

    public function getApprovalProductsCount(): int
    {
        return (int) $this->db->fetchColumn(
            'SELECT COUNT(1) FROM cscart_products p INNER JOIN cscart_companies c ON c.company_id = p.company_id WHERE p.approved = :status AND c.status = :companyStatus',
            ['status' => Premoderation::STATUS_PENDING, 'companyStatus' => Status::ENABLED]
        );
    }

    public function getApprovalOptionsCount(): int
    {
        return (int) $this->db->fetchColumn(
            'SELECT COUNT(1) FROM cscart_product_options WHERE status != :status AND company_id != 0',
            ['status' => Status::ENABLED]
        );
    }

    public function getCompaniesNewCount(): int
    {
        return (int) $this->db->fetchColumn(
            'SELECT COUNT(1) FROM cscart_companies WHERE status = :status',
            ['status' => 'N']
        );
    }

    public function getDiscussionNewCount(): int
    {
        return (int) $this->db->fetchColumn(
            'SELECT COUNT(1) FROM cscart_discussion_posts cdp INNER JOIN cscart_users u on u.user_id = cdp.user_id WHERE cdp.status != :status',
            ['status' => Status::ENABLED]
        );
    }

    public function getUserNewCount(): int
    {
        return (int) $this->db->fetchColumn(
            'SELECT COUNT(1) FROM cscart_users cu WHERE cu.status = :status',
            ['status' => Status::PENDING]
        );
    }

    /**
     * Permet de highlight certains menu, tout le temps
     * L'affichage de ces menus est conditionné, et s'ils sont affichés c'est que c'est important
     */
    public function returnAlwaysOne(): int
    {
        return 1;
    }

    public function isUBONotSubmitted(int $companyId): bool
    {
        return $this->uboMangopayService->isUBOSubmitted($companyId) === true ? false : true;
    }
}
