<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\EventListener;

use Symfony\Component\HttpKernel\Event\ControllerEvent;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Wizacha\FeatureFlag\FeatureFlaggableInterface;
use Wizacha\FeatureFlag\FeatureFlagService;

class FeatureFlagListener
{
    private FeatureFlagService $featureFlagService;

    public function __construct(FeatureFlagService $featureFlagService)
    {
        $this->featureFlagService = $featureFlagService;
    }

    public function onKernelController(ControllerEvent $event)
    {
        $callableController = $event->getController();

        /*
        * $controller passed can be either a class or a Closure.
        * This is not usual in Symfony but it may happen.
        * If it is a class, it comes in array format
        */
        if (\is_array($callableController) === false) {
            return;
        }

        [$controller] = $callableController;

        if ($controller instanceof FeatureFlaggableInterface
            && false === $this->featureFlagService->get($controller->getFeatureFlag())
        ) {
            throw new AccessDeniedHttpException('Feature flag must be activated.');
        }
    }
}
