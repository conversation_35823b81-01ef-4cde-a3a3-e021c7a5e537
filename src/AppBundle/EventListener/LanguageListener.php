<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\EventListener;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Wizacha\AppBundle\Locale\Detector;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Symfony\Component\HttpKernel\Event\ResponseEvent;

class LanguageListener implements EventSubscriberInterface
{
    private $detector;

    public function __construct(Detector $detector)
    {
        $this->detector = $detector;
    }

    public static function getSubscribedEvents()
    {
        return [
            KernelEvents::REQUEST => [['detectLanguage', 254]],
            KernelEvents::RESPONSE => ['injectContentLanguage', 0],
        ];
    }

    public function detectLanguage(RequestEvent $event): void
    {
        if (!$event->isMasterRequest()) {
            return;
        }

        $this->detector->detect($event->getRequest());
    }

    public function injectContentLanguage(ResponseEvent $event): void
    {
        if (!$event->isMasterRequest()) {
            return;
        }

        if ($event->getResponse()->headers->has('Content-Language')) {
            return;
        }

        $event->getResponse()->headers->set(
            'Content-Language',
            (string) GlobalState::contentLocale()
        );
    }
}
