<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\EventListener;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\EventListener\RouterListener as BaseRouterListener;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\Routing\RequestContext;
use Tygh\Registry;

/**
 * Implémentation de la logique de routing entre CsCart et Symfony
 */
class RouterListener implements EventSubscriberInterface
{
    public const CSCART_CONTROLLER = 'fn_dispatch';

    private $routerListener;
    private $requestContext;

    public function __construct(BaseRouterListener $routerListener, RequestContext $requestContext)
    {
        $this->routerListener = $routerListener;
        $this->requestContext = $requestContext;
    }

    /**
     * Pour router sur du CsCart il faut que le paramètre "dispatch" soit présent dans la query, POST, etc
     */
    public function onKernelRequest(RequestEvent $event)
    {
        $request = $event->getRequest();

        if ($request->attributes->has('_controller')) {
            if ($request->attributes->get('_controller') === self::CSCART_CONTROLLER) {
                // process dispatch parameter
                $this->computeDispatch($request);
            }

            // routing is already done
            return;
        }

        // On est dans le cas de l'API legacy
        if (\defined('API')) {
            $this->legacyApi($event);

            return;
        }

        // Dans les tests qui valident qu'on a une 404 on est emmerdé avec l'AREA qui vaut A dans les test
        // Du coup on force le traitement comme s'il n'y avait pas d'AREA si l'on est en prefixe API
        if (strpos($event->getRequest()->getRequestUri(), '/api/v1/') === 0) {
            $this->routerListener->onKernelRequest($event);

            return;
        }

        switch (AREA) {
            case 'A':
            case 'V':
                $this->backend($event);
                break;

            case 'C':
                $this->frontend($event);
                break;

            default:
                $this->routerListener->onKernelRequest($event);
                break;
        }
    }

    public static function getSubscribedEvents()
    {
        return [
            KernelEvents::REQUEST => [['onKernelRequest', 35]]
        ];
    }

    private function legacyApi(RequestEvent $event)
    {
        $request = $event->getRequest();
        $controller = Registry::get('api');
        $request->attributes->set('_controller', [$controller, 'handleRequest']);

        // On rajoute un peu de debug
        $request->attributes->set('_route', $request->query->get('_d'));
        $request->attributes->set('_format', 'json');
    }

    private function backend(RequestEvent $event)
    {
        $request = $event->getRequest();

        // On rajoute le nom du fichier en baseUrl
        // On en a besoin pour générer des routes Symfony potables depuis CsCart
        $this->requestContext->setBaseUrl($request->server->get('SCRIPT_NAME'));

        if ($request->getPathInfo() === '/') {
            return $this->computeDispatch($request, 'index.index');
        }

        if (false !== $this->computeSymfony($event)) {
            return;
        }

        $this->computeDispatch($request);
    }

    private function frontend(RequestEvent $event)
    {
        $request = $event->getRequest();

        if ($request->getPathInfo() === '/') {
            if (null !== $this->matchDispatch($request)) {
                return $this->computeDispatch($request);
            }

            return $this->computeSymfony($event);
        }

        if (false !== $this->computeSymfony($event)) {
            return;
        }

        $this->computeDispatch($request);
    }

    private function computeSymfony(RequestEvent $event)
    {
        try {
            return $this->routerListener->onKernelRequest($event);
        } catch (NotFoundHttpException $ex) {
            return false;
        }
    }

    private function computeDispatch(Request $request, $default = null)
    {
        $dispatch = $this->matchDispatch($request, $default);

        // Si on a pas de dispatch dans le query, on ne set pas le controller
        if (null === $dispatch) {
            $message = sprintf('No dispatch found for "%s %s"', $request->getMethod(), $request->getPathInfo());
            throw new NotFoundHttpException($message);
        }

        $route = $dispatch;

        if (\is_array($dispatch)) {
            $route = key($dispatch);
        }

        // Le ControllerResolver de Symfony va regarder ce paramètre pour instancier le controller
        $request->attributes->set('_controller', self::CSCART_CONTROLLER);

        // On rajoute un peu de debug
        $request->attributes->set('_route', $route);
        $request->attributes->set('dispatch', $route);
        $request->attributes->set('area', AREA);

        $dispatchParts = explode('.', $route);

        // On met en attribut les paramètres de la fonction fn_dispatch pour
        // que Symfony appelle le controller aves ces paramètres
        $request->attributes->set('controller', empty($dispatchParts[0]) ? 'index' : $dispatchParts[0]);
        $request->attributes->set('mode', empty($dispatchParts[1]) ? 'index' : $dispatchParts[1]);
        $request->attributes->set('action', $dispatchParts[2] ?? null);
        $request->attributes->set('dispatch_extra', $dispatchParts[3] ?? null);
    }

    private function matchDispatch(Request $request, $default = null)
    {
        if (fn_get_route($_REQUEST)) {
            return $_REQUEST['dispatch'];
        }

        if ($request->query->has('dispatch')) {
            $_REQUEST['dispatch'] = $request->query->get('dispatch');

            return $_REQUEST['dispatch'];
        }

        return $default;
    }
}
