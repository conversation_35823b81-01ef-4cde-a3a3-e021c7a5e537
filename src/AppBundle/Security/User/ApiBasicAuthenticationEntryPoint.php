<?php

namespace Wizacha\AppBundle\Security\User;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Core\Exception\CustomUserMessageAuthenticationException;
use Symfony\Component\Security\Http\EntryPoint\BasicAuthenticationEntryPoint;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * Custom Entry Point to send an authentication failed message
 */
class ApiBasicAuthenticationEntryPoint extends BasicAuthenticationEntryPoint
{

    private $translator;

    public function __construct($realmName, TranslatorInterface $translator)
    {
        parent::__construct($realmName);
        $this->translator = $translator;
    }

    public function start(Request $request, AuthenticationException $authException = null)
    {
        $response = parent::start($request, $authException);
        $response->headers->set('Content-Type', 'application/json');

        if (null !== $authException
            && ($e = $authException->getPrevious()) instanceof CustomUserMessageAuthenticationException
        ) {
            $message = $this->translator->trans($e->getMessageKey(), $e->getMessageData());
            $response->setContent(json_encode(['message' => $message, 'code' => $e->getCode()]));
            // Note: We should send a http code 403 as the login/password is correct, but we keep 401 for don't troubling front side.
        }

        if (null !== $authException
            && $authException instanceof CustomUserMessageAuthenticationException
        ) {
            $message = $this->translator->trans($authException->getMessageKey(), $authException->getMessageData());
            $response->setContent(json_encode(['message' => $message, 'code' => $authException->getCode()]));
        }

        return $response;
    }
}
