<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Security\User;

use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Wizacha\Marketplace\Basket\BasketService;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\ReadModel\Basket;

class UserService
{
    /**
     * @var TokenStorageInterface
     */
    private $tokenStorage;

    /**
     * @var BasketService
     */
    private $basketService;

    /**
     * @var SessionInterface
     */
    private $session;
    /**
     * @var LoggerInterface
     */
    private $logger;

    public function __construct(
        TokenStorageInterface $tokenStorage,
        BasketService $basketService,
        SessionInterface $session,
        LoggerInterface $logger
    ) {
        $this->tokenStorage = $tokenStorage;
        $this->basketService = $basketService;
        $this->session = $session;
        $this->logger = $logger;
    }

    public function getCurrentUserId(): ?int
    {
        // Symfony
        $token = $this->tokenStorage->getToken();
        if ($token !== null) {
            $user = $token->getUser();
            // Check that the user isn't anonymous
            if ($user instanceof ApiSecurityUser) {
                return $user->getId();
            }
        }

        // Cscart
        $csCartUserId = null;
        if (session_status() === PHP_SESSION_ACTIVE) {
            $csCartUserId = (int) ($this->session->get('auth')['user_id'] ?? null);
        }

        return $csCartUserId ? $csCartUserId : null;
    }

    /**
     * Returns the basket for the current user.
     *
     * @param bool $useNewModel
     * @return Basket|\Wizacha\Marketplace\Basket\ReadModel\Basket|null
     * @throws NotFound
     */
    public function getUserBasket(bool $useNewModel = false)
    {
        $basketId = $this->session->get('basketId');
        if (!$basketId) {
            return null;
        }

        try {
            $this->basketService->checkIntegrity($basketId);
        } catch (NotFound $e) {
            // The basket was probably corrupted (e.g. containing unknown products), we create a new one
            $basketId = $this->basketService->generateNewBasket();
            $this->session->set('basketId', $basketId);
        }

        $basket = $this->basketService->getById($basketId);

        if (!$basket) {
            // The readmodel of the basket was deleted somehow
            $this->logger->warning('Basket readmodel not found (id: {basket}), a new basket will be used', [
                'basket' => $basketId,
            ]);
            $basketId = $this->basketService->generateNewBasket();
            $this->session->set('basketId', $basketId);
            $basket = $this->basketService->getById($basketId);
            \assert($basket !== null); // safeguard
        }

        return $useNewModel ? $basket->getReadModel() : $basket;
    }

    /**
     * Abstraction de la méthode cscart.
     * Ne fait aucun contrôle de mot de passe ou de quoi que ce soit.
     */
    public function setUserInSession(int $userId)
    {
        fn_login_user($userId);
    }
}
