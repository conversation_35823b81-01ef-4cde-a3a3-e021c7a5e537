<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Security\User;

use Symfony\Component\Security\Core\Exception\UnsupportedUserException;
use Symfony\Component\Security\Core\Exception\UsernameNotFoundException;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Security\Core\User\UserProviderInterface;
use Wizacha\AppBundle\Security\Exception\ExpiredPasswordException;
use Wizacha\AppBundle\Security\Exception\NoOAuthUserAlreadyExist;
use Wizacha\AppBundle\Security\User\Throttling\AuthenticatorThrottlingService;
use Wizacha\AppBundle\Security\User\Throttling\Exception\ThrottlingException;
use Wizacha\AppBundle\Security\Exception\UserDisabledException;
use Wizacha\AppBundle\Security\Exception\UserPendingException;
use Wizacha\Marketplace\User\UserService;

class UserProvider implements UserProviderInterface
{
    private UserService $userService;
    private AuthenticatorThrottlingService $authenticatorThrottling;

    public function __construct(
        UserService $userService,
        AuthenticatorThrottlingService $authenticatorThrottling
    ) {
        $this->userService = $userService;
        $this->authenticatorThrottling = $authenticatorThrottling;
    }

    public function loadUserByUsername($username)
    {
        $timeToWait = $this->authenticatorThrottling->checkThrottlingApi($username);
        if ($timeToWait > 0) {
            throw new ThrottlingException(
                __('authenticator_throttling', ['%waitingTime%' => $timeToWait])
            );
        }

        $user = $this->userService->findOneByEmail($username);

        if (null === $user) {
            throw new UsernameNotFoundException(sprintf('User "%s" does not exist.', $username));
        }

        if ($user->isPending()) {
            throw new UserPendingException();
        }

        if (!$user->isEnabled()) {
            throw new UserDisabledException();
        }

        if ($this->userService->isUserPasswordExpired($user) === true) {
            throw new ExpiredPasswordException();
        }

        return SecurityUser::fromUser($user);
    }

    public function refreshUser(UserInterface $user)
    {
        if (!$user instanceof SecurityUser) {
            throw new UnsupportedUserException(
                sprintf('Instances of "%s" are not supported.', \get_class($user))
            );
        }

        return $this->loadUserByUsername($user->getUsername());
    }

    public function supportsClass($class)
    {
        return $class === SecurityUser::class;
    }
}
