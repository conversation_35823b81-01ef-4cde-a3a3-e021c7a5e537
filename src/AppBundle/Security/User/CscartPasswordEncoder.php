<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Security\User;

use Symfony\Component\Security\Core\Encoder\PasswordEncoderInterface;

class CscartPasswordEncoder implements PasswordEncoderInterface
{
    public function encodePassword($raw, $salt)
    {
        return fn_encode_password($raw);
    }

    public function isPasswordValid($encoded, $raw, $salt)
    {
        return fn_verify_password($raw, $salt, $encoded);
    }
}
