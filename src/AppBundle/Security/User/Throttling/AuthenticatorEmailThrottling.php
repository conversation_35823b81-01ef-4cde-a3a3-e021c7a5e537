<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Security\User\Throttling;

use Wizacha\Cache\RedisDecorator;

class AuthenticatorEmailThrottling extends AuthenticatorThrottling
{
    /** @var int[] */
    protected array $throttleMap;

    /** @param int[] $throttleMap */
    public function __construct(RedisDecorator $redisClient, array $throttleMap, int $throttleWindow)
    {
        parent::__construct($redisClient, $throttleWindow, 'mail');

        $this->throttleMap = $throttleMap;
    }

    /**
     * Return how much time the user must wait before trying to login
     */
    public function checkThrottling(string $key): int
    {
        // Get all login attempts
        $keys = $this->redisClient->keys('login_throttling:mail:' . $key . ':*');

        $numberAttempts = \count($keys);
        $lastIndex = $this->getLastIndex($keys);

        if ($numberAttempts > 0) {
            // Get last attempt timestamp
            $lastTimestamp = $this->redisClient->get('login_throttling:mail:' . $key . ':' . $lastIndex);

            // Calculate how much time the user need to wait
            $timeToWait = $this->getRequiredTimeDuration($numberAttempts, $this->throttleMap);
            $timeToWaitDiff = $lastTimestamp + $timeToWait - time();

            if ($timeToWaitDiff > 0) {
                return $timeToWaitDiff;
            }
        }

        return 0;
    }

    /**
     * Return the highest waiting time depending on the throttle map and attempts number
     * @param int[] $emailThrottleMap
     */
    protected function getRequiredTimeDuration(int $attemptsNumber, array $throttleMap): int
    {
        $finalTimeDuration = 0;
        foreach ($throttleMap as $limitAttempts => $timeDuration) {
            if ($limitAttempts <= $attemptsNumber) {
                $finalTimeDuration = $timeDuration;
            } else {
                break;
            }
        }

        return $finalTimeDuration;
    }
}
