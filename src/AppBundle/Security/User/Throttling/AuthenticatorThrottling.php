<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Security\User\Throttling;

use Wizacha\Cache\RedisDecorator;

abstract class AuthenticatorThrottling
{
    protected RedisDecorator $redisClient;
    protected int $throttleWindow;
    private string $checkType;

    /** @var mixed|null */
    protected $prefix;

    public function __construct(RedisDecorator $redisClient, int $throttleWindow, string $checkType)
    {
        $this->redisClient = $redisClient;
        $this->throttleWindow = $throttleWindow;
        $this->checkType = $checkType;
        $this->prefix = $this->redisClient->getOption(\Redis::OPT_PREFIX);
    }

    /**
     * Return how much time the user must wait before trying to login
     */
    abstract protected function checkThrottling(string $checkType): int;

    /**
     * Increment last index and save attempt to redis
     */
    public function saveAttempt(string $key): void
    {
        // Get all login attempts
        $keys = $this->redisClient->keys('login_throttling:' . $this->checkType . ':' . $key . ':*');
        $lastIndex = $this->getLastIndex($keys);

        // Save new attempt
        $newMailKey = 'login_throttling:' . $this->checkType . ':' . $key . ':' . ($lastIndex + 1);
        $this->redisClient->setex($newMailKey, $this->throttleWindow, time());
    }

    public function resetAttempt(string $key): int
    {
        return $this->redisClient->delFromPattern(
            'login_throttling:' . $this->checkType . ':' . $key . ':*',
            $this->prefix
        );
    }

    /**
     * Return the highest index value from a list of redis keys formatted like key1:key2:key3:index
     * @param string[] $keys
     */
    protected function getLastIndex(array $keys): int
    {
        $lastIndex = 0;
        foreach ($keys as $key) {
            $index = (int) (explode(':', $key)[3]);
            if ($index > $lastIndex) {
                $lastIndex = $index;
            }
        }

        return $lastIndex;
    }
}
