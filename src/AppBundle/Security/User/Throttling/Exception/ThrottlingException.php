<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Security\User\Throttling\Exception;

use Symfony\Component\Security\Core\Exception\CustomUserMessageAuthenticationException;

class ThrottlingException extends CustomUserMessageAuthenticationException
{
    public const CODE = 5;

    /** @param mixed[] $messageData */
    public function __construct(
        string $message = '',
        array $messageData = [],
        \Throwable $previous = null
    ) {
        parent::__construct($message, $messageData, static::CODE, $previous);
    }
}
