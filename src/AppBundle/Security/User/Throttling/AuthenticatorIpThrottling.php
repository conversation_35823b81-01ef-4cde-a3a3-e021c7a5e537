<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Security\User\Throttling;

use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Wizacha\Cache\RedisDecorator;
use Wizacha\Marketplace\Security\Event\IpBlockedEvent;

class AuthenticatorIpThrottling extends AuthenticatorThrottling
{
    protected int $maxAttempts;
    protected int $waitingDuration;
    protected EventDispatcherInterface $eventDispatcher;

    public function __construct(
        RedisDecorator $redisClient,
        EventDispatcherInterface $eventDispatcher,
        int $maxAttempts,
        int $waitingDuration,
        int $throttleWindow
    ) {
        parent::__construct($redisClient, $throttleWindow, 'ip');

        $this->eventDispatcher = $eventDispatcher;
        $this->maxAttempts = $maxAttempts;
        $this->waitingDuration = $waitingDuration;
    }

    /**
     * Return how much time the user must wait before trying to login
     */
    public function checkThrottling(string $key): int
    {
        // Get all login attempts
        $keys = $this->redisClient->keys('login_throttling:ip:' . $key . ':*');

        $numberAttempts = \count($keys);
        $lastIndex = $this->getLastIndex($keys);

        if ($numberAttempts >= $this->maxAttempts) {
            // Get last attempt timestamp
            $lastTimestamp = $this->redisClient->get('login_throttling:ip:' . $key . ':' . $lastIndex);

            // Calculate how much time the user need to wait
            $timeToWaitDiff = $lastTimestamp + $this->waitingDuration - time();

            if ($timeToWaitDiff > 0) {
                return $timeToWaitDiff;
            } else {
                // For IP Throttling, we delete old try if time to wait is 0
                $this->resetAttempt($key);
            }
        }

        return 0;
    }

    public function saveAttempt(string $key): void
    {
        parent::saveAttempt($key);

        // If the IP has been blocked, we send an email to the admin
        if ($this->checkThrottling($key) > 0) {
            $this->eventDispatcher->dispatch(
                new IpBlockedEvent($key, $this->maxAttempts, $this->waitingDuration),
                IpBlockedEvent::class
            );
        }
    }
}
