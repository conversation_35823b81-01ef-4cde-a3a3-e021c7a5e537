<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Security\User\Throttling;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;

class AuthenticatorThrottlingService
{
    protected AuthenticatorEmailThrottling $authenticatorEmailThrottling;
    protected AuthenticatorEmailThrottling $authenticatorEmailApiThrottling;
    protected AuthenticatorIpThrottling $authenticatorIpThrottling;
    protected ?Request $request;
    protected bool $throttlingByIpEnabled;
    protected bool $throttlingByEmailApiEnabled;

    public function __construct(
        AuthenticatorEmailThrottling $authenticatorEmailThrottling,
        AuthenticatorEmailThrottling $authenticatorEmailApiThrottling,
        AuthenticatorIpThrottling $authenticatorIpThrottling,
        RequestStack $requestStack,
        bool $throttlingByIpEnabled,
        bool $throttlingByEmailApiEnabled
    ) {
        $this->authenticatorEmailThrottling = $authenticatorEmailThrottling;
        $this->authenticatorEmailApiThrottling = $authenticatorEmailApiThrottling;
        $this->authenticatorIpThrottling = $authenticatorIpThrottling;
        $this->request = $requestStack->getCurrentRequest();
        $this->throttlingByIpEnabled = $throttlingByIpEnabled;
        $this->throttlingByEmailApiEnabled = $throttlingByEmailApiEnabled;
    }

    public function saveAttempt(string $userLogin = null): void
    {
        if (null !== $userLogin) {
            $this->authenticatorEmailThrottling->saveAttempt($userLogin);
        }

        if (true === $this->throttlingByIpEnabled && null !== $this->request) {
            $this->authenticatorIpThrottling->saveAttempt($this->request->getClientIp());
        }
    }

    public function saveAttemptApi(string $userLogin = null): void
    {
        if (null !== $userLogin) {
            $this->authenticatorEmailApiThrottling->saveAttempt($userLogin);
        }

        if (true === $this->throttlingByIpEnabled && null !== $this->request) {
            $this->authenticatorIpThrottling->saveAttempt($this->request->getClientIp());
        }
    }

    public function resetAttempts(string $userLogin = null): void
    {
        if (null !== $userLogin) {
            $this->authenticatorEmailThrottling->resetAttempt($userLogin);
        }

        if (true === $this->throttlingByIpEnabled && null !== $this->request) {
            $this->authenticatorIpThrottling->resetAttempt($this->request->getClientIp());
        }
    }

    public function checkThrottling(string $userLogin = null): int
    {
        $timeToWait = 0;

        if (true === $this->throttlingByIpEnabled && null !== $this->request) {
            $timeToWait = $this->authenticatorIpThrottling->checkThrottling($this->request->getClientIp());
        }

        if ($timeToWait <= 0 && null !== $userLogin) {
            $timeToWait = $this->authenticatorEmailThrottling->checkThrottling($userLogin);
        }

        return $timeToWait;
    }


    public function checkThrottlingApi(string $userLogin = null): int
    {
        $timeToWait = 0;

        if (true === $this->throttlingByIpEnabled && null !== $this->request) {
            $timeToWait = $this->authenticatorIpThrottling->checkThrottling($this->request->getClientIp());
        }

        if ($timeToWait <= 0 && true === $this->throttlingByEmailApiEnabled && null !== $userLogin) {
            $timeToWait = $this->authenticatorEmailApiThrottling->checkThrottling($userLogin);
        }

        return $timeToWait;
    }
}
