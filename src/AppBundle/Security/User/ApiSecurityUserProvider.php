<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Security\User;

use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Core\Exception\UnsupportedUserException;
use Symfony\Component\Security\Core\Exception\UsernameNotFoundException;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Security\Core\User\UserProviderInterface;
use Wizacha\Marketplace\User\UserRepository;

class ApiSecurityUserProvider implements UserProviderInterface
{
    private UserRepository $userRepository;

    public function __construct(UserRepository $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    public function getUsernameForApiKey(string $apiKey)
    {
        if ($user = $this->userRepository->findOneByApiKey($apiKey)) {
            return $user->getEmail();
        }

        return null;
    }

    public function loadUserByUsername($username)
    {
        $user = $this->userRepository->findOneByEmail($username);

        if (null === $user) {
            throw new UsernameNotFoundException(sprintf('User "%s" does not exist.', $username));
        }

        if (!$user->isEnabled()) {
            throw new AuthenticationException(sprintf('User "%s" is not enabled.', $username));
        }

        if ($user->belongsToAnOrganisation() && !$user->getOrganisation()->isApproved()) {
            throw new AuthenticationException('The organisation attached to the user is not approved.');
        }

        return ApiSecurityUser::fromUser($user);
    }

    public function refreshUser(UserInterface $user)
    {
        if (!$user instanceof ApiSecurityUser) {
            throw new UnsupportedUserException(
                sprintf('Instances of "%s" are not supported.', \get_class($user))
            );
        }

        return $this->loadUserByUsername($user->getUsername());
    }

    public function supportsClass($class)
    {
        return $class === ApiSecurityUser::class;
    }
}
