<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Security\User;

use Wizacha\Marketplace\User\UserService;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Authentication\Token\PreAuthenticatedToken;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\CustomUserMessageAuthenticationException;
use Symfony\Component\Security\Core\User\UserProviderInterface;
use Symfony\Component\Security\Http\Authentication\SimplePreAuthenticatorInterface;
use Psr\Log\LoggerInterface;

class ApiKeyAuthenticator implements SimplePreAuthenticatorInterface
{
    private $userService;
    private $logger;

    public function __construct(UserService $userService, LoggerInterface $logger)
    {
        $this->userService = $userService;
        $this->logger = $logger;
    }

    public function createToken(Request $request, $providerKey)
    {
        $authHeader = $request->headers->get('Authorization');
        if ($authHeader !== null && strpos($authHeader, ' ') !== false) {
            list ($authScheme, $apiKey) = explode(' ', $authHeader);

            if ($authScheme === 'token') {
                return new PreAuthenticatedToken(
                    'anon.',
                    $apiKey,
                    $providerKey
                );
            }
        }

        // No api key was found, skip this authenticator and fallback to Basic auth
        return null;
    }

    public function supportsToken(TokenInterface $token, $providerKey)
    {
        return $token instanceof PreAuthenticatedToken && $token->getProviderKey() === $providerKey;
    }

    public function authenticateToken(TokenInterface $token, UserProviderInterface $userProvider, $providerKey)
    {
        if (!$userProvider instanceof ApiSecurityUserProvider) {
            throw new \InvalidArgumentException(
                sprintf(
                    'The user provider must be an instance of ApiSecurityUserProvider (%s was given).',
                    \get_class($userProvider)
                )
            );
        }

        $apiKey = $token->getCredentials();
        $username = $userProvider->getUsernameForApiKey($apiKey);

        if (!$username) {
            throw new CustomUserMessageAuthenticationException(
                sprintf('API Key "%s" does not exist.', $apiKey)
            );
        }

        $user = $userProvider->loadUserByUsername($username);
        $domainUser = $this->userService->get($user->getId());

        if ($domainUser->hasOAuthToken() && $domainUser->getOAuthToken()->isExpired()) {
            throw new CustomUserMessageAuthenticationException(
                'OAuth token has expired'
            );
        }

        return new PreAuthenticatedToken(
            $user,
            $apiKey,
            $providerKey,
            $user->getRoles()
        );
    }
}
