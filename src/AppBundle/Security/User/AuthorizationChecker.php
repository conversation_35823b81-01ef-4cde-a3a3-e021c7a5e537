<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Security\User;

use Wizacha\AppBundle\Security\Exception\OrganisationUserIsNotApprovedException;
use Wizacha\AppBundle\Security\Exception\VendorHasNoCompanyException;
use Wizacha\User;
use Wizacha\Marketplace\User\UserStatus;
use Wizacha\Marketplace\User\User as UserMarketplace;

class AuthorizationChecker
{
    public function check(UserMarketplace $user)
    {
        if ($user->getStatus() === UserStatus::ACTIVE && $user->getUserType() === User::VENDOR_TYPE && empty($user->getCompanyId())) {
            throw new VendorHasNoCompanyException();
        }

        if ($user->belongsToAnOrganisation() && !$user->getOrganisation()->isApproved()) {
            throw new OrganisationUserIsNotApprovedException();
        }
    }
}
