<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Security\User;

use Symfony\Component\Security\Core\User\EquatableInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Wizacha\Marketplace\User\User;
use Wizacha\Marketplace\User\WizaplaceUserInterface;

class SecurityUser implements UserInterface, EquatableInterface, WizaplaceUserInterface
{
    private $id;
    private $username;
    private $password;
    private $salt;
    private $roles;
    private $companyId;
    private $apiKey;

    public function __construct(int $id, string $username, string $password, string $salt, array $roles, string $apiKey, int $companyId = null)
    {
        $this->id = $id;
        $this->username = $username;
        $this->password = $password;
        $this->salt = $salt;
        $this->roles = $roles;
        $this->companyId = $companyId;
        $this->apiKey = $apiKey;
    }

    public function getRoles(): array
    {
        return $this->roles;
    }

    public function getPassword(): string
    {
        return $this->password;
    }

    public function getSalt(): string
    {
        return $this->salt;
    }

    public function getUsername(): string
    {
        return $this->username;
    }

    public function eraseCredentials()
    {
    }

    public function isEqualTo(UserInterface $user): bool
    {
        if (!$user instanceof self) {
            return false;
        }

        if ($this->password !== $user->getPassword()) {
            return false;
        }

        if ($this->salt !== $user->getSalt()) {
            return false;
        }

        if ($this->username !== $user->getUsername()) {
            return false;
        }

        return true;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getCompanyId(): ?int
    {
        return $this->companyId;
    }

    public function getApiKey(): string
    {
        return $this->apiKey;
    }

    public static function fromUser(User $user)
    {
        switch ($user->getUserType()) {
            case 'A':
                $roles = ['ROLE_ADMIN'];
                break;
            case 'V':
                $roles = ['ROLE_VENDOR'];
                break;
            case 'C':
            default:
                $roles = ['ROLE_USER'];
                break;
        }

        return new self($user->getUserId(), $user->getEmail(), $user->getPassword(), $user->getSalt(), $roles, $user->getApiKey(), $user->getCompanyId());
    }
}
