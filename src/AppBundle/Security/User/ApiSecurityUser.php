<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Security\User;

use Symfony\Component\Security\Core\User\EquatableInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Wizacha\Marketplace\User\User;
use Wizacha\Marketplace\User\WizaplaceUserInterface;

class ApiSecurityUser implements UserInterface, EquatableInterface, WizaplaceUserInterface
{
    private $id;
    private $username;
    private $password;
    private $salt;
    private $roles;
    private $companyId;

    public function __construct(int $id, $username, $password, $salt, array $roles, $companyId)
    {
        $this->id = $id;
        $this->username = $username;
        $this->password = $password;
        $this->salt = $salt;
        $this->roles = $roles;
        $this->companyId = (int) $companyId;
    }

    public function getRoles(): array
    {
        return $this->roles;
    }

    public function getPassword()
    {
        return $this->password;
    }

    public function getSalt()
    {
        return $this->salt;
    }

    public function getUsername()
    {
        return $this->username;
    }

    public function eraseCredentials()
    {
    }

    public function isEqualTo(UserInterface $user)
    {
        if (!$user instanceof self) {
            return false;
        }

        if ($this->password !== $user->getPassword()) {
            return false;
        }

        if ($this->salt !== $user->getSalt()) {
            return false;
        }

        if ($this->username !== $user->getUsername()) {
            return false;
        }

        return true;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getCompanyId(): ?int
    {
        return $this->companyId;
    }

    public static function fromUser(User $user)
    {
        switch ($user->getUserType()) {
            case 'A':
                $roles = ['ROLE_ADMIN'];
                break;
            case 'V':
                $roles = ['ROLE_VENDOR'];
                break;
            case 'C':
                if ($user->getCompanyId() > 0) {
                    $roles = ['ROLE_VENDOR'];
                    break;
                }
            default:
                $roles = ['ROLE_USER'];
                break;
        }

        return new self($user->getUserId(), $user->getEmail(), $user->getApiKey(), null, $roles, $user->getCompanyId());
    }
}
