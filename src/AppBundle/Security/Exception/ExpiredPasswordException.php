<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Security\Exception;

use Symfony\Component\Security\Core\Exception\CustomUserMessageAuthenticationException;

class ExpiredPasswordException extends CustomUserMessageAuthenticationException
{
    public const MESSAGE_KEY = 'error_expired_password';
    public const CODE = 5;

    public function __construct()
    {
        parent::__construct(
            __(static::MESSAGE_KEY),
            [],
            static::CODE
        );
    }
}
