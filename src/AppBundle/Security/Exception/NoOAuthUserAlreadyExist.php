<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Security\Exception;

use Symfony\Component\Security\Core\Exception\CustomUserMessageAuthenticationException;

class NoOAuthUserAlreadyExist extends CustomUserMessageAuthenticationException
{
    public const MESSAGE_KEY = 'Non oauth user already exist';
    public const CODE = 4;

    public function __construct()
    {
        parent::__construct(
            __(static::MESSAGE_KEY),
            [],
            static::CODE
        );
    }
}
