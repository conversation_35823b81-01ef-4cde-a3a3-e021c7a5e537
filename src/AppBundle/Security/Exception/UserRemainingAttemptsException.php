<?php

namespace Wizacha\AppBundle\Security\Exception;

use Symfony\Component\Security\Core\Exception\CustomUserMessageAuthenticationException;

class UserRemainingAttemptsException extends CustomUserMessageAuthenticationException
{
    public const MESSAGE_KEY = 'error_account_remaining_try';
    public const CODE = 6;

    public function __construct(int $remainingTry)
    {
        parent::__construct(
            __(static::MESSAGE_KEY),
            ['remaining_try' => $remainingTry],
            static::CODE
        );
    }
}
