<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Security\Listener;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;
use Symfony\Component\Security\Core\AuthenticationEvents;
use Symfony\Component\Security\Core\Event\AuthenticationEvent;
use Symfony\Component\Security\Core\Event\AuthenticationFailureEvent;
use Symfony\Component\Security\Core\Exception\BadCredentialsException;
use Wizacha\AppBundle\Security\Exception\UserRemainingAttemptsException;
use Wizacha\Marketplace\User\UserSecurity;
use Wizacha\AppBundle\Security\User\Throttling\AuthenticatorThrottlingService;

/**
 * Save login attempt if throttle feature flag is active for get token api route
 */
class UserAuthenticationFailureListener implements EventSubscriberInterface
{
    protected UserSecurity $userSecurity;
    protected AuthenticatorThrottlingService $authenticatorThrottling;

    public function __construct(
        UserSecurity $userSecurity,
        AuthenticatorThrottlingService $authenticatorThrottling
    ) {
        $this->userSecurity = $userSecurity;
        $this->authenticatorThrottling = $authenticatorThrottling;
    }

    /** @return string[] */
    public static function getSubscribedEvents(): array
    {
        return [AuthenticationEvents::AUTHENTICATION_FAILURE => ['authenticationFailure', 0]];
    }

    public function authenticationFailure(AuthenticationEvent $event): void
    {
        $token = $event->getAuthenticationToken();
        if ($token instanceof UsernamePasswordToken === false
            || $token->getProviderKey() !== 'api_get_key'
        ) {
            return;
        }

        if ($this->authenticatorThrottling->checkThrottlingApi($token->getUsername()) <= 0) {
            $this->authenticatorThrottling->saveAttemptApi($token->getUsername());
        }

        if (true === $this->userSecurity->isBlockAccountFeatureActivated()
            && $event instanceof AuthenticationFailureEvent
            && $event->getAuthenticationException() instanceof BadCredentialsException
        ) {
            $remainingTry = $this->userSecurity->registerFailedLoginAttempt($token->getUsername());
            throw new UserRemainingAttemptsException($remainingTry);
        }
    }
}
