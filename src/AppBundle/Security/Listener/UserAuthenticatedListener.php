<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Security\Listener;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;
use Symfony\Component\Security\Core\AuthenticationEvents;
use Symfony\Component\Security\Core\Event\AuthenticationEvent;
use Symfony\Component\Security\Core\Event\AuthenticationFailureEvent;
use Wizacha\AppBundle\Security\Constants\SecurityConstants;
use Wizacha\AppBundle\Security\User\SecurityUser;
use Wizacha\AppBundle\Security\User\Throttling\Exception\ThrottlingException;
use Wizacha\Marketplace\User\UserSecurity;
use Wizacha\AppBundle\Security\User\Throttling\AuthenticatorThrottlingService;
use Wizacha\Marketplace\User\UserService;
use Wizacha\Component\AuthLog\AuthLogRepository;
use Wizacha\Component\AuthLog\DestinationType;
use Wizacha\Component\AuthLog\SourceType;
use Wizacha\Component\AuthLog\StatusType;

/**
 * PasswordUpgraderInterface legacy style 💫
 *
 * Note that this mechanism currently only works with /users/authenticate API endpoint.
 */
class UserAuthenticatedListener implements EventSubscriberInterface
{
    protected UserService $userService;
    protected ?Request $request;
    protected UserSecurity $userSecurity;
    protected AuthLogRepository $authLogRepository;
    protected AuthenticatorThrottlingService $authenticatorThrottling;
    protected bool $throttleFeatureActivated;

    public function __construct(
        UserService $userService,
        UserSecurity $userSecurity,
        RequestStack $requestStack,
        AuthLogRepository $authLogRepository,
        AuthenticatorThrottlingService $authenticatorThrottling
    ) {
        $this->request = $requestStack->getCurrentRequest();
        $this->userService = $userService;
        $this->userSecurity = $userSecurity;
        $this->authLogRepository = $authLogRepository;
        $this->authenticatorThrottling = $authenticatorThrottling;
    }

    /** @inheritDoc */
    public static function getSubscribedEvents(): array
    {
        return [
            AuthenticationEvents::AUTHENTICATION_SUCCESS => [
                ['migrateLegacyPasswords', 0],
                ['onAuthenticationSuccess', 0],
                ['clearLoginAttempt', 0],
                ['resetLoginAttempts', 0]
            ],
            AuthenticationEvents::AUTHENTICATION_FAILURE => ['onAuthenticationFailure', 0]
        ];
    }

    public function migrateLegacyPasswords(AuthenticationEvent $event): void
    {
        if ($event->getAuthenticationToken() instanceof UsernamePasswordToken === false
            || $this->request->request->get(SecurityConstants::LEGACY_PASSWORD) === null
        ) {
            return;
        }

        /** @var SecurityUser $securityUser */
        $securityUser = $event->getAuthenticationToken()->getUser();

        $user = $this->userService->get($securityUser->getId());

        // If this user does not have a legacy password password configured, skip.
        if (fn_is_new_password($user->getPassword()) === true) {
            $this->request->request->remove(SecurityConstants::LEGACY_PASSWORD);

            return;
        }

        $user->setPassword(fn_encode_password($this->request->request->get(SecurityConstants::LEGACY_PASSWORD)));
        $user->setSalt('');

        $this->userService->save($user);

        $this->request->request->remove(SecurityConstants::LEGACY_PASSWORD);
    }

    public function onAuthenticationSuccess(AuthenticationEvent $event): void
    {
        if ($this->isApiAuthentication($event) === false) {
            return;
        }

        $securityUser = $event->getAuthenticationToken()->getUser();
        $this->authLogRepository->save(
            $securityUser->getUsername(),
            StatusType::SUCCESS(),
            SourceType::BASIC(),
            DestinationType::FRONT()
        );
    }

    public function onAuthenticationFailure(AuthenticationEvent $event): void
    {
        if ($this->isApiAuthentication($event) === false) {
            return;
        }

        // To avoid DDOS we don't log throttling errors
        if ($event instanceof AuthenticationFailureEvent
            && $event->getAuthenticationException()->getPrevious() instanceof ThrottlingException
        ) {
            return;
        }

        $securityUser = $event->getAuthenticationToken()->getUser();
        $this->authLogRepository->save(
            $securityUser,
            StatusType::WRONG_PASSWORD(),
            SourceType::BASIC(),
            DestinationType::FRONT()
        );
    }

    private function isApiAuthentication(AuthenticationEvent $event): bool
    {
        $token = $event->getAuthenticationToken();
        if ($token instanceof UsernamePasswordToken === false
            || $token->getProviderKey() !== 'api_get_key'
        ) {
            return false;
        }

        return true;
    }

    public function clearLoginAttempt(AuthenticationEvent $event): void
    {
        $token = $event->getAuthenticationToken();
        if ($token instanceof UsernamePasswordToken === false
            || $token->getProviderKey() !== 'api_get_key'
        ) {
            return;
        }

        $this->authenticatorThrottling->resetAttempts($token->getUsername());
    }

    public function resetLoginAttempts(AuthenticationEvent $event): void
    {
        $token = $event->getAuthenticationToken();
        if ($token instanceof UsernamePasswordToken === false
            || $token->getProviderKey() !== 'api_get_key'
        ) {
            return;
        }
        $this->userSecurity->resetAttemptsCount($token->getUsername());
    }
}
