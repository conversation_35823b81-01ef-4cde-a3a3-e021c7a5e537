<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Notification;

use Wizacha\Marketplace\Currency\Event\CurrencyRatesUpdateFailed;

class CurrencyNotifier extends Notifier
{
    public function currencyRatesUpdateFailed(CurrencyRatesUpdateFailed $event): void
    {
        $subject = __('currency_updated_failed_notification_subject');
        $body = __('currency_updated_failed_notification_body', [
            '%messageToAdmin%' => $event->getMessageToAdmin(),
        ]);
        $this->sendEmailToAdmin($subject, '@App/mail/currency/currency-updated-failed.html.twig', ['body' => $body]);
    }
}
