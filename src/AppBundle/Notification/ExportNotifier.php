<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Notification;

use Wizacha\Exim\AsyncExportHasFinished;
use Wizacha\Marketplace\User\UserType;

class ExportNotifier extends Notifier
{
    public function asyncExportHasFinished(AsyncExportHasFinished $event)
    {
        $user = $this->userRepository->get($event->getUserId());

        $this->sendEmailToAddress(
            $user->getEmail(),
            __('csv_export_complete'),
            '@App/mail/export/export-finished.html.twig',
            [
                'url' => $event->getUrl(),
            ]
        );
    }
}
