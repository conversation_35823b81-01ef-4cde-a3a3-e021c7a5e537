<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Notification;

use Psr\Log\LoggerInterface;
use <PERSON>ymfony\Component\Templating\EngineInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Wizacha\Bridge\Swiftmailer\Mailer;
use Wizacha\Marketplace\AdminCompany;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Entities\Declination;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\MailingList\MailLogService;
use Wizacha\Marketplace\PIM\Product\Event\ProductApproved;
use Wizacha\Marketplace\PIM\Product\Event\ProductChangesRequested;
use Wizacha\Marketplace\PIM\Product\Event\ProductRejected;
use Wizacha\Marketplace\PIM\Product\Event\ProductStockThresholdReached;
use Wizacha\Marketplace\PIM\Product\ProductService;
use Wizacha\Marketplace\User\UserRepository;
use Wizacha\Product;

class ProductNotifier extends Notifier
{
    /**
     * @var ProductService
     */
    private $productService;

    public function __construct(
        Mailer $mailer,
        EngineInterface $templating,
        LoggerInterface $logger,
        AdminCompany $adminCompany,
        UserRepository $userRepository,
        ProductService $productService,
        UrlGeneratorInterface $router,
        MailLogService $mailLogService
    ) {
        $this->productService = $productService;

        parent::__construct($mailer, $templating, $logger, $adminCompany, $userRepository, $router, $mailLogService);
    }

    public function productApproved(ProductApproved $event)
    {
        if ($event->getCompanyId() == 0) {
            return;
        }

        $products = $this->getTemplateListProducts($event->getProducts());
        $company = new Company($event->getCompanyId());

        $this->sendEmailToCompany(
            $company,
            __(
                $company->isPrivateIndividual() ? 'w_c2c_products_approval_status_changed' : 'products_approval_status_approved_subject',
                [
                    '[company_name]' => $company->getName(),
                    '[products_count]' =>  \count($event->getProducts()),
                    '[status]' => __('approved')
                ],
                $company->getLocale()->__toString()
            ),
            $company->isPrivateIndividual() ? '@App/mail/product/c2c_approved.html.twig' : '@App/mail/product/approved.html.twig',
            [
                'company_id' => $event->getCompanyId(),
                'products' => $products,
                'reason' => $event->getReason(),
            ]
        );
    }

    public function productRejected(ProductRejected $event)
    {
        if ($event->getCompanyId() == 0) {
            return;
        }

        $products = $this->getTemplateListProducts($event->getProducts());
        $company = new Company($event->getCompanyId());

        $this->sendEmailToCompany(
            $company,
            __(
                'products_approval_status_disapproved_subject',
                [
                    '[company_name]' => $company->getName(),
                    '[products_count]' =>  \count($event->getProducts()),
                    '[status]' => __('disapproved')
                ],
                $company->getLocale()->__toString()
            ),
            '@App/mail/product/rejected.html.twig',
            [
                'products' => $products,
                'reason' => $event->getReason(),
            ]
        );
    }

    public function productChangesRequested(ProductChangesRequested $event)
    {
        if ($event->getCompanyId() == 0) {
            return;
        }

        $products = $this->getTemplateListProducts($event->getProducts());
        $company = new Company($event->getCompanyId());

        $this->sendEmailToCompany(
            $company,
            __(
                'products_approval_status_standby_subject',
                [
                    '[company_name]' => $company->getName(),
                    '[products_count]' =>  \count($event->getProducts()),
                    '[status]' => __('standby')
                ],
                $company->getLocale()->__toString()
            ),
            '@App/mail/product/changes_requested.html.twig',
            [
                'products' => $products,
                'reason' => $event->getReason(),
            ]
        );
    }

    public function productStockThresholdReached(ProductStockThresholdReached $event)
    {
        $declination = new Declination(new Product($event->getProductId()), $event->getCombination());

        $this->sendEmailToCompany(
            $declination->getCompany(),
            __('low_stock_subj', [
                '[product]' => $declination->getName() . ' #' . $event->getProductId(),
            ]),
            '@App/mail/product/product-low-stock.html.twig',
            [
                'declination' => $declination,
                'selectedProductOptions' => fn_get_selected_product_options_info(
                    fn_get_product_options_by_combination($event->getCombination())
                ),
                'newAmount' => $event->getNewAmount(),
            ]
        );
    }

    /**
     * List products with details for the twig template
     * @param int[] $productIds
     * @return string[][]
     */
    protected function getTemplateListProducts(array $productIds): array
    {
        $productsList = [];
        $nbProducts = 0;
        foreach ($productIds as $productId) {
            try {
                $productsList[] = $this->productService->get((int) $productId);
                $nbProducts++;
            } catch (NotFound $e) {
                continue;
            }

            if (101 === $nbProducts) {
                break;
            }
        }

        // Order by products name
        usort($productsList, function ($element1, $element2) {
            return $element1->name <=> $element2->name;
        });

        return $productsList;
    }
}
