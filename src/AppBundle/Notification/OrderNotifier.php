<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Notification;

use Psr\Log\LoggerInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Templating\EngineInterface;
use Tygh\Registry;
use Wizacha\AppBundle\Notification\DTO\InternalTransferErrorDTO;
use Wizacha\Bridge\Swiftmailer\Mailer;
use Wizacha\C2C\Code;
use Wizacha\C2C\Order;
use Wizacha\Component\Locale\Locale;
use Wizacha\Marketplace\AdminCompany;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\MailingList\MailLogService;
use Wizacha\Marketplace\Order\Event\OrderDispatchFundsFailed;
use Wizacha\Marketplace\Order\Event\OrderCodeFailed;
use Wizacha\Marketplace\Order\Event\OrderCodeGenerated;
use Wizacha\Marketplace\Order\Event\OrderStatusChanged;
use Wizacha\Marketplace\Order\Event\ShipmentCreated;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\Payment\Event\BankwireFailedToRetrieveOrderEvent;
use Wizacha\Marketplace\Payment\Event\BankwireNotificationFailedStatusEvent;
use Wizacha\Marketplace\Payment\Event\BankwirePaymentEvent;
use Wizacha\Marketplace\Payment\Event\DispatchFundsFailedEvent;
use Wizacha\Marketplace\Payment\Event\HipayTransactionChargedbackEvent;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Price\PriceFormatter;
use Wizacha\Marketplace\Subscription\SubscriptionService;
use Wizacha\Marketplace\Tax\Event\MissingTaxConfigurationEvent;
use Wizacha\Marketplace\User\UserRepository;

class OrderNotifier extends Notifier implements OrderNotifierInterface
{
    /**
     * @var bool $notifyShipmentCreated
     */
    protected $notifyShipmentCreated;

    /** @var bool */
    protected $featureSubscription;

    /** @var null|SubscriptionService */
    protected $subscriptionService;

    /** @var string */
    protected $externalFrontOfficeUrl;

    protected OrderService $orderService;
    protected PriceFormatter $priceFormatter;

    public function __construct(
        Mailer $mailer,
        EngineInterface $templating,
        LoggerInterface $logger,
        AdminCompany $adminCompany,
        UserRepository $userRepository,
        UrlGeneratorInterface $router,
        MailLogService $mailLogService,
        OrderService $orderService
    ) {
        parent::__construct($mailer, $templating, $logger, $adminCompany, $userRepository, $router, $mailLogService);

        $this->orderService = $orderService;
    }

    /** @internal only called by symfony container */
    public function setSubscription(bool $featureSubscription, SubscriptionService $subscriptionService): void
    {
        $this->featureSubscription = $featureSubscription;
        $this->subscriptionService = $subscriptionService;
    }

    /** @internal only call by symfony container */
    public function setExternalFrontOfficeUrl(string $externalFrontOfficeUrl): void
    {
        $this->externalFrontOfficeUrl = "https://" . $externalFrontOfficeUrl;
    }

    public function setPriceFormatter(PriceFormatter $priceFormatter): void
    {
        $this->priceFormatter = $priceFormatter;
    }

    public function orderCodeGenerated(OrderCodeGenerated $event)
    {
        $order = $event->getOrder();
        $user = $this->userRepository->get($order->getUserId());

        $this->sendEmailToCustomer(
            $user,
            __('w_code_notification_subj', array_merge(
                [
                    '[id]' => $order->getId(),
                    '[order_id]' => $order->getId(),
                    '[firstname]' => $user->getFirstname(),
                    '[lastname]' => $user->getLastname()
                ],
                $order->getExtraDataForMailNotificationVariables('w_code_notification_subj')
            )),
            '@App/mail/order/code.html.twig',
            [
                'codes' => $event->getCode()->full(),
                'sellerInfo' => reset(fn_get_users(['company_id' => $order->getCompanyId()], $auth = null)[0]),
                'company' => fn_get_company_data($order->getCompanyId()),
                'orderId' => $order->getId(),
                'user' => $user,
                'order_extra' => $order->getExtraDataForMailNotificationVariables('w_contact_info_seller')
            ]
        );
    }

    public function orderCodeFailed(OrderCodeFailed $event)
    {
        $order = $event->getOrder();
        /** @var Code $code */
        $orderInfo = $this->orderService->overrideLegacyOrder($order->getId());
        $code = $orderInfo[Order::KEY_CODE_IN_ORDER];
        if ($code instanceof Code) {
            $user = $this->userRepository->get($order->getUserId());
            $vendor = new Company($order->getCompanyId());

            $this->sendEmailToAdmin(
                __('w_mail_code_fail_subject', array_merge(
                    [
                        '[order_id]' => $order->getId(),
                        '[firstname]' => $user->getFirstname(),
                        '[lastname]' => $user->getLastname()
                    ],
                    $order->getExtraDataForMailNotificationVariables('w_mail_code_fail_subject')
                )),
                '@App/mail/order/code_failed.html.twig',
                [
                    'logs' => $code->logs(),
                    'correctCode' => $code->expectedCode(),
                    'orderId' => $order->getId(),
                    'userEmail' => $user->getEmail(),
                    'vendorEmail' => $vendor->getEmail(),
                    'user' => $user,
                    'order_extra' => $order->getExtraDataForMailNotificationVariables('w_mail_code_fail_body')
                ]
            );
        }
    }

    public function orderStatusChanged(OrderStatusChanged $event)
    {
        $order_info = $event->getOrderInfo();
        $company = new Company($order_info['company_id']);
        $order_statuses = fn_get_statuses(STATUSES_ORDER, array(), true, false, (string) GlobalState::interfaceLocale(), $order_info['company_id']);
        list($shipments) = fn_get_shipments_info(array('order_id' => $order_info['order_id'], 'advanced_info' => true));
        $use_shipments = !fn_one_full_shipped($shipments);
        $payment_method = fn_get_payment_data((!empty($order_info['payment_method']['payment_id']) ? $order_info['payment_method']['payment_id'] : 0), $order_info['order_id'], $order_info['lang_code']);
        $status_settings = $order_statuses[$order_info['status']]['params'];
        $profile_fields = fn_get_profile_fields('I', '', $order_info['lang_code']);
        $take_surcharge_from_vendor = fn_take_payment_surcharge_from_vendor();

        // Subscription renew case
        if (true === $this->featureSubscription
            && $this->subscriptionService instanceof SubscriptionService
            && \is_string($order_info['subscription_id'])
        ) {
            return $this->subscriptionRenew(
                $event,
                $order_info,
                $shipments,
                $use_shipments,
                $payment_method,
                $status_settings,
                $profile_fields,
                $take_surcharge_from_vendor,
                $company
            );
        }

        $orderInfoOrderId = $order_info['order_id'];
        $order = $this->orderService->getOrder((int) $orderInfoOrderId);
        $user = $this->userRepository->get($order_info['user_id']);
        $userLocale = $user->getLocale();
        $subject = __(
            'order_email_subject_' . $order_info['status'],
            array_merge(
                [
                    '[order_id]' => $orderInfoOrderId,
                    '[firstname]' => $user->getFirstname(),
                    '[lastname]' => $user->getLastname(),
                    '[company_name]' => $company->getName()
                ],
                $order->getExtraDataForMailNotificationVariables('order_email_subject_' . $order_info['status'])
            ),
            (string) $userLocale
        );
        $showAsInvoice = $this->orderService->getOrder((int) $orderInfoOrderId)->shouldShowAsInvoice();

        if ($event->getNotifyCustomer()) {
            $this->sendEmailToAddress(
                $order_info['email'],
                $subject,
                $company->isPrivateIndividual() ? '@App/mail/order/c2c-status-changed.html.twig' : '@App/mail/order/status-changed.html.twig',
                [
                    'order_info' => $order_info,
                    'orderId' => $orderInfoOrderId,
                    'shipments' => $shipments,
                    'use_shipments' => $use_shipments,
                    'payment_method' => $payment_method,
                    'status_settings' => $status_settings,
                    'profile_fields' => $profile_fields,
                    'take_surcharge_from_vendor' => $take_surcharge_from_vendor,
                    'user' => $user,
                    'company_name' => $company->getName(),
                    'show_as_invoice' => $showAsInvoice,
                    'order_extra' => $order->getExtraDataForMailNotificationVariables('order_email_header_' . $order_info['status']),
                    'display_order_summary' => Registry::get('settings.General.display_order_summary_in_emails') === 'Y',
                ],
                $userLocale
            );
        }

        // The subject is now a function, which is called when sending mail once the recipient's language is set correctly
        $subject = function (Locale $locale = null) use ($order, $order_info, $user, $company) {
            if ($order_info['adjustable'] && $order_info['status'] === OrderStatus::STANDBY_VENDOR) {
                return __(
                    'order_vendor_email_subject_adjusted_' . $order_info['status'],
                    array_merge(
                        [
                            '[order_id]' => $order_info['order_id'],
                            '[firstname]' => $user->getFirstname(),
                            '[lastname]' => $user->getLastname(),
                            '[company_name]' => $company->getName(),
                            '[email]' => $user->getEmail(),
                        ],
                        $order->getExtraDataForMailNotificationVariables('order_vendor_email_subject_adjusted_' . $order_info['status'])
                    ),
                    (string) $locale
                );
            }

            return __(
                'order_vendor_email_subject_' . $order_info['status'],
                array_merge(
                    [
                        '[order_id]' => $order_info['order_id'],
                        '[firstname]' => $user->getFirstname(),
                        '[lastname]' => $user->getLastname(),
                        '[company_name]' => $company->getName()
                    ],
                    $order->getExtraDataForMailNotificationVariables('order_vendor_email_subject_' . $order_info['status'])
                ),
                (string) $locale
            );
        };

        if ($event->getNotifyCompany()) {
            $this->sendEmailToCompany(
                $company,
                $subject,
                $company->isPrivateIndividual() ? '@App/mail/order/c2c-company-status-changed.html.twig' : '@App/mail/order/company-status-changed.html.twig',
                [
                    'order_info' => $order_info,
                    'orderId' => $order_info['order_id'],
                    'shipments' => $shipments,
                    'use_shipments' => $use_shipments,
                    'payment_method' => $payment_method,
                    'status_settings' => $status_settings,
                    'profile_fields' => $profile_fields,
                    'take_surcharge_from_vendor' => $take_surcharge_from_vendor,
                    'user' => $user,
                    'company_name' => $company->getName(),
                    'show_as_invoice' => $showAsInvoice,
                    'order_extra' => $order->getExtraDataForMailNotificationVariables('order_vendor_email_header_' . $order_info['status']),
                    'display_order_summary' => Registry::get('settings.General.display_order_summary_in_emails') === 'Y',
                ]
            );
        }

        if ($event->getNotifyAdmin()) {
            $this->sendEmailToAdmin(
                $subject,
                $company->isPrivateIndividual() ? '@App/mail/order/c2c-admin-status-changed.html.twig' : '@App/mail/order/admin-status-changed.html.twig',
                [
                    'order_info' => $order_info,
                    'orderId' => $order_info['order_id'],
                    'shipments' => $shipments,
                    'use_shipments' => $use_shipments,
                    'payment_method' => $payment_method,
                    'status_settings' => $status_settings,
                    'profile_fields' => $profile_fields,
                    'take_surcharge_from_vendor' => $take_surcharge_from_vendor,
                    'user' => $user,
                    'company_name' => $company->getName(),
                    'show_as_invoice' => $showAsInvoice,
                    'order_extra' => $order->getExtraDataForMailNotificationVariables('order_vendor_email_header_' . $order_info['status']),
                    'display_order_summary' => Registry::get('settings.General.display_order_summary_in_emails') === 'Y',
                ]
            );
        }
    }

    public function dispatchFundsFailedEvent(DispatchFundsFailedEvent $event): void
    {
        $order = $event->getOrder();
        $orderId = $order->getId();
        $companyName = $order->getCompany()->getName();
        $commission = fn_format_price_by_currency($event->getCommission()->getConvertedAmount());
        $partVendorTTc = fn_format_price_by_currency($order->getTotal()->subtract($event->getCommission())->getConvertedAmount());

        $subject = __('dispatch_funds_failed_subject', ['%orderId%' => $orderId]);
        $body = __('dispatch_funds_failed_failed_body', [
            '%orderId%' => $orderId,
            '%companyName%' => $companyName,
            '%commissionTTC%' => $commission,
            '%partVendorTTC%' => $partVendorTTc,
        ]);
        $this->sendEmailToAdmin($subject, '@App/mail/order/admin-dispatch-failed.html.twig', ['body' => $body]);
    }

    public function shipmentCreated(ShipmentCreated $event)
    {
        if (false === $this->notifyShipmentCreated) {
            return;
        }

        $isOrderFullyShipped = fn_w_check_all_shipped_order_after_current_shippment(
            $this->orderService->overrideLegacyOrder($event->getShipment()->getOrder()->getId())
        );
        $orderId = $event->getShipment()->getOrder()->getId();
        $user = $this->userRepository->get($event->getShipment()->getOrder()->getUserId());
        $userLocale = $user->getLocale();
        $order = $event->getShipment()->getOrder();

        $this->sendEmailWithSpecifiedFromToAddress(
            $this->adminCompany->getOrdersDepartmentEmail(),
            $event->getShipment()->getOrder()->getEmail(),
            __(
                "new_shipment_was_created",
                array_merge(
                    ['[order_id]' => $orderId],
                    $order->getExtraDataForMailNotificationVariables('new_shipment_was_created')
                ),
                (string) $userLocale
            ),
            '@App/mail/order/shipment_created.html.twig',
            [
                'shipment' => $event->getShipment(),
                'orderId' => $orderId,
                'fullShipment' => $isOrderFullyShipped,
                'user' => [
                    'firstname' => $user->getFirstname(),
                    'lastname' => $user->getLastname(),
                ],
                'order_extra' => $order->getExtraDataForMailNotificationVariables('product_shipped'),
            ],
            $userLocale
        );
    }

    public function bankwireNotificationFailedStatusEvent(BankwireNotificationFailedStatusEvent $event): OrderNotifierInterface
    {
        $this->sendEmailToAdmin(__("bankwire_notification_failed_status_subject"), '@App/mail/order/bankwire-notification-payment-error.twig', [
            'ordersId' => \implode(', ', $event->getOrdersId()),
            'amount' => $event->getAmount(),
            'transactionId' => $event->getTransactionId(),
            'pspName' => $event->getPspName(),
        ]);

        return $this;
    }

    public function bankwireFailedToRetrieveOrderEvent(BankwireFailedToRetrieveOrderEvent $event): OrderNotifierInterface
    {
        $subject = __('bankwire_notification_failed_to_retrieve_orders_subject', [
            '%transactionId%' => $event->getTransactionId(),
            '%token%' => $event->getTokenValue(),
            '%amount%' => $event->getAmount(),
            '%currency%' => $event->getCurrency(),
        ]);
        $body = __('bankwire_notification_failed_to_retrieve_orders_body', [
            '%transactionId%' => $event->getTransactionId(),
            '%token%' => $event->getTokenValue(),
            '%amount%' => $event->getAmount(),
            '%currency%' => $event->getCurrency(),
        ]);
        $this->sendEmailToAdmin($subject, '@App/mail/order/bankwire-notification-failed-to-retrieve-orders.twig', ['body' => $body]);

        return $this;
    }

    public function internalTransferError(InternalTransferErrorDTO $error): OrderNotifierInterface
    {
        $this->sendEmailToAdmin(__("internal_transfer_failed_subject"), '@App/mail/order/lemonway-wallet-transfer-failed.twig', [
            'orderId' => $error->getOrderId(),
            'amount' => $error->getAmount(),
            'sourceTransactionId' => $error->getSourceTransactionId(),
            'internalTransactionId' => $error->getInternalTransactionId(),
            'fromWallet' => $error->getFromWallet(),
            'toWallet' => $error->getToWallet(),
        ]);

        return $this;
    }

    public function hipayTransactionChargedBackEvent(HipayTransactionChargedbackEvent $event): OrderNotifierInterface
    {
        $this->sendEmailToAdmin(
            __("transaction_chargedback_object", [
                "transactionId" => $event->getReference(),
                "parentOrderId" => $event->getOrder()->getId(),
            ]),
            '@App/mail/order/hipay-transaction-chargedback.html.twig',
            [
                'transactionId' => $event->getReference(),
                'parentOrderId' => $event->getOrder()->getId(),
                'childOrders' => $this->getIdListFromSubOrdersArray($event->getOrder()->getSubOrders()),
            ]
        );

        return $this;
    }

    /** @param \Wizacha\Order[] $orders */
    private function getIdListFromSubOrdersArray(array $orders): string
    {
        $orderIds = [];

        foreach ($orders as $order) {
            $orderIds[] = $order->getId();
        }

        return \count($orderIds) > 0 ? "(" . \implode(',', $orderIds) . ")" : "";
    }

    public function setNotifyShipmentCreated(bool $notify)
    {
        $this->notifyShipmentCreated = $notify;
    }

    /**
     * Manage the email Notificatio for subscription renewings
     *
     * @param mixed[] $order_info
     * @param mixed[] $shipments
     * @param mixed[] $payment_method
     * @param mixed[] $status_settings
     * @param mixed[] $profile_fields
     */
    protected function subscriptionRenew(
        OrderStatusChanged $event,
        array $order_info,
        array $shipments,
        bool $use_shipments,
        array $payment_method,
        array $status_settings,
        array $profile_fields,
        bool $take_surcharge_from_vendor,
        Company $company
    ): void {
        $subscription = $this->subscriptionService->get($order_info['subscription_id']);

        $templateVariables = [
            'order_info' => $order_info,
            'orderId' => $order_info['order_id'],
            'shipments' => $shipments,
            'use_shipments' => $use_shipments,
            'payment_method' => $payment_method,
            'status_settings' => $status_settings,
            'profile_fields' => $profile_fields,
            'take_surcharge_from_vendor' => $take_surcharge_from_vendor,
            'subscriptionId' => $subscription->getId(),
            'subscriptionName' => $subscription->getName(),
            'externalFrontOfficeUrl' => $this->externalFrontOfficeUrl,
        ];

        if (true === $event->getNotifyCustomer()) {
            if ($order_info['is_paid'] === "1") {
                $subject = __("subscription_notification_subject_customer_success");
            } else {
                $subject = __("subscription_notification_subject_customer_fail");
            }

            $templateVariables['user'] = $this->userRepository->get($order_info['user_id']);

            $this->sendEmailToAddress(
                $order_info['email'],
                $subject,
                '@App/mail/order/renew-subscription.html.twig',
                $templateVariables,
                \array_key_exists('lang_code', $order_info) ? new Locale($order_info['lang_code']) : null
            );
        }

        if (true === $event->getNotifyCompany()) {
            if ($order_info['is_paid'] === "1") {
                $subject = __("subscription_notification_subject_vendor_success");
            } else {
                $subject = __("subscription_notification_subject_vendor_fail");
            }

            $this->sendEmailToCompany(
                $company,
                $subject,
                '@App/mail/order/company-renew-subscription.html.twig',
                $templateVariables
            );
        }

        return;
    }

    public function bankwirePaymentEvent(BankwirePaymentEvent $event): OrderNotifierInterface
    {
        $order = $event->getOrder();
        $orderId = $order->getId();
        $user = $this->userRepository->get($order->getUserId());
        $paymentDetails = $event->getPaymentDetails();

        $this->sendEmailToCustomer(
            $user,
            __(
                'bankwire_payment_notification_subject',
                [
                    '%orderId%' => $orderId,
                    '%amount%' => $this->priceFormatter->formatFloat($paymentDetails['amount']->getConvertedAmount()),
                    '%$transactionReference%' => $order->getTransactionReference()
                ],
                $user->getLocale()->__toString()
            ),
            '@App/mail/order/bankwire-notification-payment.html.twig',
            ['body' => __(
                'bankwire_payment_notification_body',
                [
                    '%orderId%' => $orderId,
                    '%orderDate%' => $order->getCreatedAt()->format('Y-m-d H:i'),
                    '%amount%' => $this->priceFormatter->formatFloat($paymentDetails['amount']->getConvertedAmount()),
                    '%rib%' => $paymentDetails['IBAN'],
                    '%bic%' => $paymentDetails['BIC'],
                    '%label%' => $paymentDetails['label'],
                    '%social_name%' => $paymentDetails['socialName'],
                    '%address%' => $paymentDetails['address'],
                    '%$transactionReference%' => $order->getTransactionReference()
                ],
                $user->getLocale()->__toString()
            ),
                'user' => $user,
            ]
        );

        return $this;
    }

    public function missingTaxConfigurationEvent(MissingTaxConfigurationEvent $event): OrderNotifierInterface
    {
        $this->sendEmailToAdmin(__("missing_tax_configuration_subject"), '@App/mail/order/missing-tax-configuration.html.twig', [
            'orderId' => $event->getOrderId(),
        ]);

        return $this;
    }
}
