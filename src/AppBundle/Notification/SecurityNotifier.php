<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Notification;

use Wizacha\Marketplace\Security\Event\IpBlockedEvent;

class SecurityNotifier extends Notifier
{
    public function ipBlockedEvent(IpBlockedEvent $event): void
    {
        $this->sendEmailToAdmin(
            __('error_ip_blocked_mail_subject'),
            '@App/mail/security/ip_blocked.html.twig',
            [
                'ipAddress' => $event->getIpAddress(),
                'maxAttempts' => $event->getMaxAttempts(),
                'waitingDuration' => $event->getWaitingDuration(),
                'urlToLogs' => fn_url(
                    'admin.php?dispatch[auth_logs.manage]'
                )
            ]
        );
    }
}
