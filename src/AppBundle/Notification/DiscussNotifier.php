<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Notification;

use Psr\Log\LoggerInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Templating\EngineInterface;
use Wizacha\AppBundle\Service\BFOService;
use Wizacha\Bridge\Swiftmailer\Mailer;
use Wizacha\Discuss\Event\DiscussMessagePosted;
use Wizacha\Marketplace\AdminCompany;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\MailingList\MailLogService;
use Wizacha\Marketplace\User\UserRepository;
use Wizacha\Marketplace\User\UserType;
use Symfony\Component\Routing\RouterInterface;

class DiscussNotifier extends Notifier
{
    /**
     * @var RouterInterface
     */
    protected $router;
    private BFOService $bfoService;

    public function __construct(
        Mailer $mailer,
        EngineInterface $templating,
        LoggerInterface $logger,
        AdminCompany $adminCompany,
        UserRepository $userRepository,
        UrlGeneratorInterface $router,
        MailLogService $mailLogService,
        BFOService $bfoService
    ) {
        parent::__construct($mailer, $templating, $logger, $adminCompany, $userRepository, $router, $mailLogService);
        $this->bfoService = $bfoService;
    }

    public function discussMessagePosted(DiscussMessagePosted $event)
    {
        $user = $event->getUser();
        $area = $user->getUserType() === UserType::VENDOR ? 'V' : 'C';

        if ($area === 'C') {
            $link = fn_url('discuss.list', $area);
            $company = fn_get_user_company_name((int) $event->getMessage()->getAuthor());
            $firstName = $user->getFirstname();
            $lastName = $user->getLastname();
            $email = $user->getEmail();

            $this->sendEmailToCustomer(
                $user,
                __('wizacha_discuss_new_message_subj', [
                    '[firstname]' => $firstName,
                    '[lastname]' => $lastName,
                    '[company_name]' => $company,
                ]),
                '@App/mail/discuss/message-posted.html.twig',
                [
                    'discussUrl' => $link,
                    'homeUrl' => fn_url('', 'C'),
                    'firstname' => $firstName,
                    'lastname' => $lastName,
                    'email' => $email,
                    'user' => $user,
                    'company' => $company,
                ]
            );
        } else {
            $oldBaseUrl = $this->router->getContext()->getBaseUrl();
            $this->router->getContext()->setBaseUrl('/' . container()->getParameter('entrypoint.vendor'));
            $link = $this->router->generate('admin_Discuss_list', [], RouterInterface::ABSOLUTE_URL);
            $this->router->getContext()->setBaseUrl($oldBaseUrl);
            $user_info = fn_get_user_info($event->getMessage()->getAuthor());
            $company = new Company($user->getCompanyId());
            $firstName = $user_info['firstname'];
            $lastName = $user_info['lastname'];
            $email = $user_info['email'];
            $templateParams = [
                'discussUrl' => $link,
                'homeUrl' => fn_url('', 'C'),
                'firstname' => $firstName,
                'lastname' => $lastName,
                'email' => $email,
                'user' => $user,
                'company' => $company,
            ];
            if ($this->bfoService->isBFOEnabled()) {
                $templateParams['homeUrl'] = $this->bfoService->getHomeUrl();
                $templateParams['discussUrl'] = $this->bfoService->getDiscussUrl(
                    $event->getMessage()->getDiscussion()->getId()
                );
            }

            $this->sendEmailToCompany(
                $company,
                __('wizacha_discuss_new_message_subj', [
                    '[firstname]' => $firstName,
                    '[lastname]' => $lastName,
                    '[company_name]' => $company->getName(),
                ]),
                '@App/mail/discuss/message-posted.html.twig',
                $templateParams
            );
        }
    }

    public function setRouter(RouterInterface $router)
    {
        $this->router = $router;
    }
}
