<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Notification;

use Wizacha\Marketplace\CompanyPerson\Event\UBOValidationFailed;
use Wizacha\Marketplace\Entities\Company as CompanyEntity;

class CompanyPersonNotifier extends Notifier
{
    public function UBOValidationFailed(UBOValidationFailed $event): void
    {
        $declarationId = $event->getDeclarationId();
        $subject = __('ubo_validation_failed_subject', ['%declarationId%' => $declarationId]);

        $this->sendEmailToCompany(
            new CompanyEntity($event->getCompanyId()),
            $subject,
            '@App/mail/CompanyPerson/UBO-validation-failed.html.twig',
            [
                'status' => $event->getStatus(),
                'reason' => $event->getReason(),
                'message' => $event->getMessage(),
            ]
        );
    }
}
