<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Notification;

use Wizacha\AppBundle\Notification\DTO\InternalTransferErrorDTO;
use Wizacha\Marketplace\Order\Event\OrderCodeFailed;
use Wizacha\Marketplace\Order\Event\OrderCodeGenerated;
use Wizacha\Marketplace\Order\Event\OrderStatusChanged;
use Wizacha\Marketplace\Order\Event\ShipmentCreated;
use Wizacha\Marketplace\Payment\Event\BankwireFailedToRetrieveOrderEvent;
use Wizacha\Marketplace\Payment\Event\BankwireNotificationFailedStatusEvent;
use Wizacha\Marketplace\Payment\Event\BankwirePaymentEvent;
use Wizacha\Marketplace\Payment\Event\DispatchFundsFailedEvent;
use Wizacha\Marketplace\Payment\Event\HipayTransactionChargedbackEvent;

interface OrderNotifierInterface
{
    public function orderCodeGenerated(OrderCodeGenerated $event);

    public function orderCodeFailed(OrderCodeFailed $event);

    public function orderStatusChanged(OrderStatusChanged $event);

    public function shipmentCreated(ShipmentCreated $event);

    public function bankwireNotificationFailedStatusEvent(BankwireNotificationFailedStatusEvent $event): self;

    public function bankwireFailedToRetrieveOrderEvent(BankwireFailedToRetrieveOrderEvent $event): self;

    public function dispatchFundsFailedEvent(DispatchFundsFailedEvent $event);

    public function internalTransferError(InternalTransferErrorDTO $error): self;

    public function hipayTransactionChargedBackEvent(HipayTransactionChargedbackEvent $event): self;

    public function setNotifyShipmentCreated(bool $notify);

    public function bankwirePaymentEvent(BankwirePaymentEvent $event);
}
