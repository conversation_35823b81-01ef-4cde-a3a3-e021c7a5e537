<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Notification;

use Wizacha\Marketplace\Cms\Event\ContactFormSubmitted;

class ContactNotifier extends Notifier
{
    public function contactFormSubmitted(ContactFormSubmitted $event)
    {
        $this->sendEmailToAddress($event->getReceiver(), $event->getSubject(), '@App/mail/contact/form-submitted.twig', [
            'elements' => $event->getElements(),
        ]);
    }
}
