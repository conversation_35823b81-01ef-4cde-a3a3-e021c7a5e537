<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Notification;

use Psr\Log\LoggerInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Templating\EngineInterface;
use Wizacha\AppBundle\Service\BFOService;
use Wizacha\Bridge\Swiftmailer\Mailer;
use Wizacha\Marketplace\AdminCompany;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\MailingList\MailLogService;
use Wizacha\Marketplace\User\Event\UserAskedToRecoverPassword;
use Wizacha\Marketplace\User\Event\UserProfileActivated;
use Wizacha\Marketplace\User\Event\UserProfileActivationRequested;
use Wizacha\Marketplace\User\Event\UserProfileBlocked;
use Wizacha\Marketplace\User\Event\UserProfileCreated;
use Wizacha\Marketplace\User\Event\UserProfileUpdated;
use Wizacha\Marketplace\User\UserRepository;
use Wizacha\Marketplace\User\UserType;

class UserNotifier extends Notifier
{
    private BFOService $bfoService;

    public function __construct(
        Mailer $mailer,
        EngineInterface $templating,
        LoggerInterface $logger,
        AdminCompany $adminCompany,
        UserRepository $userRepository,
        UrlGeneratorInterface $router,
        MailLogService $mailLogService,
        BFOService $bfoService
    ) {
        parent::__construct($mailer, $templating, $logger, $adminCompany, $userRepository, $router, $mailLogService);
        $this->bfoService = $bfoService;
    }

    public function userAskedToRecoverPassword(UserAskedToRecoverPassword $event)
    {
        $user = $event->getUser();

        if (!empty($event->getRecoverBaseUrl())) {
            $url = $event->getRecoverBaseUrl() . $event->getToken();
        } else {
            $url = fn_url('auth.recover_password?ekey=' . $event->getToken(), $user->getUserType(), 'http');
        }

        $subject = 'recover_password_subj';
        $template = '@App/mail/user/recover_password.html.twig';

        if ($event->isPasswordRecoveryForceChangeActivated() === true) {
            $overrideArea = $user->getUserType() === UserType::ADMIN()->getValue();
            $url = fn_url('auth.password_recovery_force_change?ekey=' . $event->getToken(), $user->getUserType(), 'http', (string) GlobalState::contentLocale(), $overrideArea);
            $subject = 'password_recovery_force_change_mail_subj';
            $template = '@App/mail/user/password_recovery_force_change.html.twig';
        }

        $this->sendEmailToCustomer(
            $user,
            __($subject, [
                '[firstname]' => $user->getFirstname(),
                '[lastname]' => $user->getLastname()
            ], (string) $user->getLocale()),
            $template,
            [
                'url' => $url,
                'user' => $user,
            ]
        );
    }

    public function userProfileActivated(UserProfileActivated $event)
    {
        $user = $event->getUser();

        $this->sendEmailToCustomer(
            $user,
            __('profile_activated', [
                '[firstname]' => $user->getFirstname(),
                '[lastname]' => $user->getLastname()
            ], (string) $user->getLocale()),
            '@App/mail/user/profile_activated.html.twig',
            [
                'user' => $user,
            ]
        );
    }

    public function userProfileCreated(UserProfileCreated $event)
    {
        $user = $event->getUser();
        $templateParams = [ 'user' => $user ];
        if ($this->bfoService->isBFOEnabled()) {
            $templateParams['contactUrl'] = $this->bfoService->getContactPageUrl();
        }
        $this->sendEmailToCustomer(
            $user,
            __('w_new_client_subjet', [
                '[firstname]' => $user->getFirstname(),
                '[lastname]' => $user->getLastname()
            ], (string) $user->getLocale()),
            '@App/mail/user/profile_created.html.twig',
            $templateParams
        );
    }

    public function userProfileUpdated(UserProfileUpdated $event)
    {
        $user = $event->getUser();
        $templateParams = [ 'user' => $user ];
        if ($this->bfoService->isBFOEnabled()) {
            $templateParams['profileUrl'] = $this->bfoService->getProfileUrl();
        }

        $this->sendEmailToCustomer(
            $user,
            __('update_profile_notification', [
                '[firstname]' => $user->getFirstname(),
                '[lastname]' => $user->getLastname()
            ], (string) $user->getLocale()),
            '@App/mail/user/profile_updated.html.twig',
            $templateParams
        );
    }

    public function userProfileActivationRequested(UserProfileActivationRequested $event)
    {
        $user = $event->getUser();

        $this->sendEmailToAdmin(
            __('new_user_profile', [
                '[firstname]' => $user->getFirstname(),
                '[lastname]' => $user->getLastname()
            ], (string) $user->getLocale()),
            '@App/mail/user/profile_activation_requested.html.twig',
            [
                'user' => $user,
            ]
        );
    }

    public function userProfileBlocked(UserProfileBlocked $event)
    {
        $user = $event->getUser();

        $this->sendEmailToAdmin(
            __('error_account_blocked_mail_subject', (string) $user->getLocale()),
            '@App/mail/user/profile_blocked.html.twig',
            [
                'user' => $user,
                'urlToLogs' => fn_url(
                    'admin.php?is_search=Y&login=' . $user->getEmail() . '&dispatch[auth_logs.manage]'
                )
            ]
        );
    }
}
