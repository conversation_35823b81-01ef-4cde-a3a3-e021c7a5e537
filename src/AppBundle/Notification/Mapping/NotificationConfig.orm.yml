Wizacha\AppBundle\Notification\NotificationConfig:
    type: entity
    table: notifications_config
    id:
        id:
            type: integer
            column: id
            generator:
                strategy: AUTO
    fields:
        description:
            type: string
            column: description
            nullable: false
        className:
            type: string
            column: class_name
            nullable: false
        notifier:
            type: string
            column: notifier
            nullable: false
        enabled:
            type: boolean
            column: enabled
            nullable: false
        createdAt:
            type: datetime_immutable
            nullable: false
        updatedAt:
            type: datetime_immutable
            nullable: true
    manyToOne:
        user:
            targetEntity: Wizacha\Marketplace\User\User
            joinColumn:
                name: user_id
                referencedColumnName: user_id
                nullable: true
    lifecycleCallbacks:
        prePersist:
            - defineCreatedAt
        preUpdate:
            - defineUpdatedAt
