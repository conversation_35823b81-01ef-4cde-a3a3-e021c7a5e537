<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Notification;

use Wizacha\Config;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\PIM\Moderation\ProductWasReported;

class ModerationNotifier extends Notifier
{
    public function productWasReported(ProductWasReported $event)
    {
        if (true === $event->isMvp()) {
            $adminUrl = fn_url('', Config::AREA_ADMIN, 'https', (string) GlobalState::interfaceLocale(), true) .
                $this->router->generate('admin_MultiVendorProduct_form', ['id' => $event->getProductId()], $this->router::ABSOLUTE_PATH);
        } else {
            $adminUrl = fn_url('products.update?product_id=' . $event->getProductId(), Config::AREA_ADMIN, 'https', (string) GlobalState::interfaceLocale(), true);
        }

        $this->sendEmailToAdmin(__('report_content_mail_subject'), '@App/mail/moderation/product-reported.html.twig', [
            'name' => $event->getVisitorName(),
            'email' => $event->getVisitorEmail(),
            'message' => $event->getVisitorMessage(),
            'shopUrl' => fn_url('products.view?product_id=' . $event->getProductId(), Config::AREA_CLIENT),
            'adminUrl' => $adminUrl,
        ]);
    }
}
