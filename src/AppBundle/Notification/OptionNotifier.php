<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Notification;

use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\PIM\Option\Event\OptionApproved;
use Wizacha\Marketplace\PIM\Option\Event\OptionRejected;

class OptionNotifier extends Notifier
{
    public function optionApproved(OptionApproved $event)
    {
        $this->sendEmailToCompany(
            new Company($event->getCompanyId()),
            __('w_options_approval_status_changed', ['[status]' => 'approved']),
            '@App/mail/option/approved.html.twig',
            [
                'options' => $event->getOptions(),
                'reason' => $event->getReason(),
            ]
        );
    }

    public function optionRejected(OptionRejected $event)
    {
        $this->sendEmailToCompany(
            new Company($event->getCompanyId()),
            __('w_options_approval_status_changed', ['[status]' => 'disapproved']),
            '@App/mail/option/rejected.html.twig',
            [
                'options' => $event->getOptions(),
                'reason' => $event->getReason(),
            ]
        );
    }
}
