<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Notification;

use Wizacha\Marketplace\Organisation\Event\OrganisationApproved;
use Wizacha\Marketplace\Organisation\Event\OrganisationRegistered;
use Wizacha\Marketplace\Organisation\Event\OrganisationDisapproved;

class OrganisationNotifier extends Notifier
{
    public function organisationRegistered(OrganisationRegistered $event): void
    {
        $this->sendEmailToAdmin(
            __('organisation_registered'),
            '@App/mail/organisation/organisation_registered.html.twig',
            [
                'organisation' => $event->getOrganisation(),
            ]
        );
    }

    public function organisationApproved(OrganisationApproved $event): void
    {
        $this->sendEmailToOrganisation(
            $event->getOrganisation(),
            __('organisation_approved'),
            '@App/mail/organisation/organisation_approved.html.twig',
            [
                'organisation' => $event->getOrganisation(),
            ]
        );
    }

    public function organisationDisapproved(OrganisationDisapproved $event): void
    {
        $this->sendEmailToOrganisation(
            $event->getOrganisation(),
            __('organisation_disapproved'),
            '@App/mail/organisation/organisation_disapproved.html.twig',
            [
                'organisation' => $event->getOrganisation(),
            ]
        );
    }
}
