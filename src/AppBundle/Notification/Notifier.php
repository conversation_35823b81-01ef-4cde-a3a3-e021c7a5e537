<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Notification;

use Psr\Log\LoggerInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Templating\EngineInterface;
use Wizacha\Bridge\Swiftmailer\Mailer;
use Wizacha\Component\Locale\Locale;
use Wizacha\Marketplace\AdminCompany;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\MailingList\MailLogService;
use Wizacha\Marketplace\Organisation\Organisation;
use Wizacha\Marketplace\User\User;
use Wizacha\Marketplace\User\UserRepository;

/**
 * Provides helpers to write notifiers that send emails.
 */
abstract class Notifier
{
    /** @var Mailer */
    private $mailer;

    /** @var EngineInterface */
    protected $templating;

    /** @var AdminCompany */
    protected $adminCompany;

    /** @var UserRepository */
    protected $userRepository;

    /** @var LoggerInterface */
    protected $logger;

    /** @var UrlGeneratorInterface */
    protected $router;

    /** @var MailLogService */
    protected $mailLogService;

    public function __construct(
        Mailer $mailer,
        EngineInterface $templating,
        LoggerInterface $logger,
        AdminCompany $adminCompany,
        UserRepository $userRepository,
        UrlGeneratorInterface $router,
        MailLogService $mailLogService
    ) {
        $this->mailer = $mailer;
        $this->templating = $templating;
        $this->adminCompany = $adminCompany;
        $this->userRepository = $userRepository;
        $this->logger = $logger;
        $this->router = $router;
        $this->mailLogService = $mailLogService;
    }

    protected function sendEmailToCustomer(User $customer, string $subject, string $template, array $templateVariables = [])
    {
        GlobalState::runWithInterface($customer->getLocale(), function () use ($customer, $subject, $template, $templateVariables): void {
            try {
                $message = new \Swift_Message();
                $message
                    ->setSubject($subject)
                    ->setFrom($this->adminCompany->getUsersDepartmentEmail(), $this->adminCompany->getName())
                    ->setTo($customer->getEmail())
                    ->setBody($this->templating->render($template, $templateVariables), 'text/html');
            } catch (\Swift_RfcComplianceException $e) {
                $this->logger->warning('Cannot send email because from/to address is incorrect', ['exception' => $e]);
                return;
            }

            $this->mailer->send($message);
            $this->mailLogService->logger(
                'The mail has been sent successfully to the customer',
                [
                    'message' => $message
                ]
            );
        });
    }

    /**
     * @param Company $company
     * @param string|callable $subject
     * @param string $template
     * @param array $templateVariables
     * @throws NotFound
     */
    protected function sendEmailToCompany(Company $company, $subject, string $template, array $templateVariables = [])
    {
        $locale = $company->getLocale();

        GlobalState::runWithInterface($locale, function () use ($subject, $company, $template, $templateVariables, $locale): void {
            // resolve email subject in the locale closure to have the correct locale for the translation
            if (\is_callable($subject)) {
                $subject = $subject($locale);
            }

            try {
                $message = new \Swift_Message();
                $message
                    ->setSubject($subject)
                    ->setFrom($this->adminCompany->getOrdersDepartmentEmail(), $this->adminCompany->getName())
                    ->setTo($company->getEmail())
                    ->setBody($this->templating->render($template, $templateVariables), 'text/html');
            } catch (\Swift_RfcComplianceException $e) {
                $this->logger->warning('Cannot send email because from/to address is incorrect', ['exception' => $e]);
                return;
            }

            try {
                $this->mailer->send($message);
                $this->logger->info('[check-send-mail-to-company]', [
                    'message' => "A mail sent to company",
                    'company_id' => $company->getId(),
                    'Subject' => $subject,
                    'From' => $this->adminCompany->getOrdersDepartmentEmail() . ' ' . $this->adminCompany->getName(),
                    'TO' => $company->getEmail(),
                    'Template Variables' => $templateVariables,
                    'Template' => $template,
                ]);
            } catch (\Exception $exception) {
                $this->logger->error('[check-send-mail-to-company]', [
                    'message' => 'Failed to send email to company',
                    'company_id' => $company->getId(),
                    'exception' => $exception,
                ]);
            }
        });
    }

    protected function sendEmailToOrganisation(Organisation $organisation, string $subject, string $template, array $templateVariables = []): void
    {
        foreach ($organisation->getAdministrators() as $organisationAdmin) {
            $user = $organisationAdmin->getUser();
            $templateVariables['user'] = $user;
            $this->sendEmailToCustomer($user, $subject, $template, $templateVariables);
        }
    }

    /**
     * @param string|callable $subject
     */
    protected function sendEmailToAdmin($subject, string $template, array $templateVariables = [])
    {
        $locale = new Locale((string) GlobalState::contentLocale());

        GlobalState::runWithInterface(GlobalState::fallbackLocale(), function () use ($subject, $template, $templateVariables, $locale): void {
            // resolve email subject in the locale closure to have the correct locale for the translation
            if (\is_callable($subject)) {
                $subject = $subject($locale);
            }

            try {
                $message = new \Swift_Message();
                $message
                    ->setSubject($subject)
                    ->setFrom($this->adminCompany->getSiteAdministratorEmail(), $this->adminCompany->getName())
                    ->setTo($this->adminCompany->getSiteAdministratorEmail())
                    ->setBody($this->templating->render($template, $templateVariables), 'text/html');
//                $a = $this->templating->render($template, $templateVariables), 'text/html');
            } catch (\Swift_RfcComplianceException $e) {
                $this->logger->warning('Cannot send email because from/to address is incorrect', ['exception' => $e]);
                return;
            }

            $this->mailer->send($message);
        });
    }

    protected function sendEmailToAddress(
        string $address,
        string $subject,
        string $template,
        array $templateVariables = [],
        Locale $locale = null
    ): void {
        GlobalState::runWithInterface(
            $locale ?? GlobalState::fallbackLocale(),
            function () use ($subject, $address, $template, $templateVariables): void {
                try {
                    $message = new \Swift_Message();
                    $message
                        ->setSubject($subject)
                        ->setFrom($this->adminCompany->getSiteAdministratorEmail(), $this->adminCompany->getName())
                        ->setTo($address)
                        ->setBody($this->templating->render($template, $templateVariables), 'text/html');
                } catch (\Swift_RfcComplianceException $e) {
                    $this->logger->warning('Cannot send email because from/to address is incorrect', ['exception' => $e]);
                    return;
                }

                $this->mailer->send($message);
            }
        );
    }

    protected function sendEmailWithSpecifiedFromToAddress(
        string $from,
        string $to,
        string $subject,
        string $template,
        array $templateVariables = [],
        Locale $locale = null
    ): void {
        GlobalState::runWithInterface(
            $locale ?? GlobalState::fallbackLocale(),
            function () use ($subject, $from, $to, $template, $templateVariables): void {
                try {
                    $message = new \Swift_Message();
                    $message
                        ->setSubject($subject)
                        ->setFrom($from, $this->adminCompany->getName())
                        ->setTo($to)
                        ->setBody($this->templating->render($template, $templateVariables), 'text/html');
                } catch (\Swift_RfcComplianceException $e) {
                    $this->logger->warning('Cannot send email because from/to address is incorrect', ['exception' => $e]);
                    return;
                }

                $this->mailer->send($message);
            }
        );
    }

    public function setMailer(Mailer $mailer): self
    {
        $this->mailer = $mailer;

        return $this;
    }
}
