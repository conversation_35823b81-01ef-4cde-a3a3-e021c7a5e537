<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Notification;

use Wizacha\Company;
use Wizacha\Marketplace\Company\Event\C2cCompanyApplied;
use Wizacha\Marketplace\Company\Event\CompanyApplied;
use Wizacha\Marketplace\Company\Event\CompanyDisabled;
use Wizacha\Marketplace\Company\Event\CompanyPending;
use Wizacha\Marketplace\Company\Event\CompanyRejected;
use Wizacha\Marketplace\Entities\Company as CompanyEntity;

class CompanyNotifier extends Notifier
{
    public function companyApplied(CompanyApplied $event)
    {
        $companyData = $event->getCompanyData();

        //for the vendor email, we use the firstname/lastname of the legal representative
        $firstname = $companyData['legal_representative_firstname'];
        $lastname = $companyData['legal_representative_lastname'];

        // Notify admin
        $this->sendEmailToAdmin(__('vendor_approval_pending', [
            '[firstname]' => $firstname,
            '[lastname]' => $lastname,
            '[company_name]' => $companyData['company']
        ]), '@App/mail/company/admin_application_received.html.twig', [
            'companyData' => $companyData,
            'adminFirstName' => $event->getAdminFirstName(),
            'adminLastName' => $event->getAdminLastName(),
            'messageToAdmin' => $event->getMessageToAdmin(),
            'firstname' => $firstname,
            'lastname' => $lastname,
        ]);

        // Notify applier
        $this->sendEmailToCompany(
            new CompanyEntity($companyData['company_id']),
            __('w_vendor_apply_subject', [
                '[company_name]' => $companyData['company'],
                '[firstname]' => $firstname,
                '[lastname]' => $lastname
            ]),
            '@App/mail/company/company_application_received.html.twig',
            [
                'companyData' => $companyData,
            ]
        );
    }

    public function c2cCompanyApplied(C2cCompanyApplied $event)
    {
        $companyData = $event->getCompanyData();

        // Notify admin
        $this->sendEmailToAdmin(__('w_c2c_vendor_approval_pending', [
            '[firstname]' => $event->getFirstName(),
            '[lastname]' => $event->getLastName(),
            '[company_name]' => $companyData['company']
        ]), '@App/mail/company/admin_c2c_application_received.html.twig', [
            'companyData' => $companyData,
            'firstName' => $event->getFirstName(),//avoid bc break with existing templates
            'lastName' => $event->getLastName()
        ]);

        // Notify applier
        $this->sendEmailToCompany(
            new CompanyEntity($companyData['company_id']),
            __('w_vendor_apply_subject', [
                '[firstname]' => $companyData['legal_representative_firstname'],
                '[lastname]' => $companyData['legal_representative_lastname'],
                '[company_name]' => $companyData['company']
            ]),
            '@App/mail/company/c2c_company_application_received.html.twig',
            [
                'hasFiles' => Company::hasFiles($companyData['company_id']),
                'firstname' => $companyData['legal_representative_firstname'],
                'lastname' => $companyData['legal_representative_lastname'],
                'companyData' => $companyData
            ]
        );
    }

    public function companyPending(CompanyPending $event): void
    {
        $companyData = $event->getCompanyData();
        $userData = $event->getUserData();

        $company = new CompanyEntity($companyData['company_id']);
        $c2c = $company->isPrivateIndividual();

        if ($userData['email'] === null || \mb_strlen($userData['email']) === 0) {
            $userData['email'] = $company->getEmail();
        }

        //for the vendor email, we use the firstname/lastname of the legal representative
        $firstname = $companyData['legal_representative_firstname'];
        $lastname = $companyData['legal_representative_lastname'];

        $this->sendEmailToCompany(
            $company,
            __($c2c ? 'w_c2c_new_profile_notification' : 'new_profile_notification', [
                '[firstname]' => $firstname,
                '[lastname]' => $lastname
            ]),
            $c2c ? '@App/mail/company/c2c_company_pending.html.twig' : '@App/mail/company/company_pending.html.twig',
            [
                'companyData' => $companyData,
                'userData' => $userData,
                'firstname' => $firstname,
                'lastname' => $lastname,
            ]
        );
    }

    public function companyRejected(CompanyRejected $event)
    {
        $companyData = $event->getCompanyData();

        //for the vendor email, we use the firstname/lastname of the legal representative
        $firstname = $companyData['legal_representative_firstname'];
        $lastname = $companyData['legal_representative_lastname'];

        $this->sendEmailToCompany(
            new CompanyEntity($companyData['company_id']),
            __('text_company_status_new_to_disable_subj', [
                '[firstname]' => $firstname,
                '[lastname]' => $lastname
            ]),
            '@App/mail/company/company_rejected.html.twig',
            [
                'companyData' => $companyData,
                'reason' => $event->getReason(),
                'firstname' => $firstname,
                'lastname' => $lastname,
            ]
        );
    }

    public function companyDisabled(CompanyDisabled $event)
    {
        $companyData = $event->getCompanyData();

        //for the vendor email, we use the firstname/lastname of the legal representative
        $firstname = $companyData['legal_representative_firstname'];
        $lastname = $companyData['legal_representative_lastname'];
        $companyName = $companyData['company'];

        $this->sendEmailToCompany(
            new CompanyEntity($companyData['company_id']),
            __('text_company_status_disabled_subj', [
                '[firstname]' => $firstname,
                '[lastname]' => $lastname,
                '[company_name]' => $companyName
            ]),
            '@App/mail/company/company_disabled.html.twig',
            [
                'companyData' => $companyData,
                'reason' => $event->getReason(),
                'firstname' => $firstname,
                'lastname' => $lastname,
            ]
        );
    }
}
