<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Notification;

use Psr\Log\LoggerInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Templating\EngineInterface;
use Twig\Environment;
use Twig\Loader\ArrayLoader;
use Tygh\Registry;
use Wizacha\Bridge\Swiftmailer\Mailer;
use Wizacha\Component\Locale\Locale;
use Wizacha\Marketplace\AdminCompany;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\MailingList\MailLogService;
use Wizacha\Marketplace\Order\CreditNote\CreditNote;
use Wizacha\Marketplace\Order\CreditNote\CreditNoteHelper;
use Wizacha\Marketplace\Order\Refund\Enum\OrderRefundStatus;
use Wizacha\Marketplace\Order\Refund\Event\OrderRefundedEvent;
use Wizacha\Marketplace\Order\Refund\Service\RefundService;
use Wizacha\Marketplace\User\UserRepository;

class RefundNotifier extends Notifier
{
    /** @var string */
    protected $currencySign;

    /** @var string */
    protected $creditNoteTemplateUrl;

    /** @var CreditNoteHelper */
    protected $creditNoteHelper;

    private const CREDITNOTE = 'creditNote';

    private RefundService $refundService;

    public function __construct(
        Mailer $mailer,
        EngineInterface $templating,
        LoggerInterface $logger,
        AdminCompany $adminCompany,
        UserRepository $userRepository,
        UrlGeneratorInterface $router,
        MailLogService $mailLogService,
        RefundService $refundService
    ) {
        parent::__construct($mailer, $templating, $logger, $adminCompany, $userRepository, $router, $mailLogService);
        $this->refundService = $refundService;
    }


    public function orderRefundedEvent(OrderRefundedEvent $event): void
    {
        $order = $event->getOrder();
        // creditNote contains the order summary
        $creditNote = Registry::get('settings.General.display_order_summary_in_emails') === 'Y'
            ? $event->getCreditNote()
            : '';
        $company = new Company($order->getCompanyId());
        $companyLocale = $company->getLocale();
        $user = $this->userRepository->get($order->getUserId());
        $orderBalance = $order->getBalanceTotal()->getConvertedAmount();
        if ($order->getRefundStatus()->equals(OrderRefundStatus::COMPLETE_REFUND()) === true
            || $order->getRefundStatus()->equals(OrderRefundStatus::PARTIAL_REFUND()) === true
        ) {
            $orderBalance = $order->getTotal()->getConvertedAmount() - $this->refundService->getOrderTotalAmountRefunded($order)->getConvertedAmount();
        }
        $emailVariables = [
            'orderId' => $order->getId(),
            'orderBalance' => $orderBalance,
            'currency' => $this->currencySign,
            'user' => [
                'firstname' => $user->getFirstname(),
                'lastname' => $user->getLastname(),
            ],
            'company' => [
                'company_name' => $company->getName()
            ]
        ];

        $this->sendEmailToAdmin(
            __(
                'email_notification_refund_admin_subject',
                array_merge(
                    [
                        '[orderId]' => $order->getId(),
                        '[firstname]' => $user->getFirstname(),
                        '[lastname]' => $user->getLastname()
                    ],
                    $order->getExtraDataForMailNotificationVariables('email_notification_refund_admin_subject')
                )
            ),
            '@App/mail/refund/admin-notification.twig',
            array_merge(
                $emailVariables,
                [
                    static::CREDITNOTE => $creditNote instanceof CreditNote ? $this->getCreditNoteContent($creditNote) : '',
                    'order_extra' => $order->getExtraDataForMailNotificationVariables('email_notification_refund_admin_body')
                ],
            )
        );

        $this->sendEmailToCompany(
            $company,
            __(
                'email_notification_refund_company_subject',
                array_merge(
                    [
                        '[orderId]' => $order->getId(),
                        '[firstname]' => $user->getFirstname(),
                        '[lastname]' => $user->getLastname(),
                    ],
                    $order->getExtraDataForMailNotificationVariables('email_notification_refund_company_subject')
                ),
                (string) $companyLocale
            ),
            '@App/mail/refund/company-notification.twig',
            array_merge(
                $emailVariables,
                [
                    static::CREDITNOTE => $creditNote instanceof CreditNote ? $this->getCreditNoteContent($creditNote, $companyLocale) : '',
                    'order_extra' => $order->getExtraDataForMailNotificationVariables('email_notification_refund_company_body')
                ]
            )
        );

        $this->sendEmailToCustomer(
            $user,
            __(
                'email_notification_refund_customer_subject',
                array_merge(
                    [
                        '[firstname]' => $user->getFirstname(),
                        '[lastname]' => $user->getLastname()
                    ],
                    $order->getExtraDataForMailNotificationVariables('email_notification_refund_company_subject')
                ),
                (string) $user->getLocale()
            ),
            '@App/mail/refund/customer-notification.twig',
            array_merge(
                $emailVariables,
                [
                    static::CREDITNOTE => $creditNote instanceof CreditNote ? $this->getCreditNoteContent($creditNote, $user->getLocale()) : '',
                    'order_extra' => $order->getExtraDataForMailNotificationVariables('email_notification_refund_customer_body')
                ]
            )
        );
    }

    public function setCurrencySign(string $currencySign): self
    {
        $this->currencySign = $currencySign;

        return $this;
    }

    public function setCreditNoteTemplateUrl(string $templateUrl): self
    {
        $this->creditNoteTemplateUrl = $templateUrl;

        return $this;
    }

    public function setCreditNoteHelper(CreditNoteHelper $creditNoteHelper): self
    {
        $this->creditNoteHelper = $creditNoteHelper;

        return $this;
    }

    protected function getCreditNoteContent(CreditNote $creditNote, Locale $locale = null): string
    {
        return GlobalState::runWithInterface($locale ?? GlobalState::contentLocale(), function () use ($creditNote) {
            $content = null;
            $twig = $this->creditNoteHelper->retrieveRemoteTemplate($this->creditNoteTemplateUrl);
            if (\is_string($twig) && \strlen($twig) > 0) {
                $twigSandbox = new Environment(new ArrayLoader());
                $twigTemplate = $twigSandbox->createTemplate($twig);

                $content = $twigTemplate->render([static::CREDITNOTE => $creditNote]);
            }

            if ($content === null || \strlen($content) === 0) {
                $content = $this->templating->render('@App/common/order/credit-note.html.twig', [
                    static::CREDITNOTE => $creditNote,
                    'currency' => $this->currencySign,
                ]);
            }

            return $content;
        });
    }
}
