<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Notification;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;

class NotificationConfigRepository extends ServiceEntityRepository
{
    /**
     * @param NotificationConfig[]
     *
     * @return NotificationConfig[]
     */
    public function batchSave(array $notifications): array
    {
        foreach ($notifications as $notification) {
            $this->getEntityManager()->persist($notification);
        }

        $this->getEntityManager()->flush();

        return $notifications;
    }
}
