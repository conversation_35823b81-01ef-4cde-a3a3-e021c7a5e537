<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Notification;

use Psr\Log\LoggerInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Templating\EngineInterface;
use Wizacha\Bridge\Swiftmailer\Mailer;
use Wizacha\Component\Locale\Locale;
use Wizacha\Marketplace\AdminCompany;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\MailingList\MailLogService;
use Wizacha\Marketplace\Order\AfterSales\Event\AfterSalesServiceRequested;
use Wizacha\Marketplace\Order\AfterSales\Event\LitigationCreated;
use Wizacha\Marketplace\Order\OrderReturn\Event\RmaDeclined;
use Wizacha\Marketplace\Order\OrderReturn\Event\RmaReceived;
use Wizacha\Marketplace\Order\OrderReturn\Event\RmaRequested;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\User\UserRepository;

class RmaNotifier extends Notifier
{
    /**
     * @var OrderService
     */
    private $orderService;

    public function __construct(
        Mailer $mailer,
        EngineInterface $templating,
        LoggerInterface $logger,
        AdminCompany $adminCompany,
        UserRepository $userRepository,
        OrderService $orderService,
        UrlGeneratorInterface $router,
        MailLogService $mailLogService
    ) {
        parent::__construct($mailer, $templating, $logger, $adminCompany, $userRepository, $router, $mailLogService);
        $this->orderService = $orderService;
    }

    public function rmaRequested(RmaRequested $event)
    {
        $returnInfo = fn_get_return_info($event->getRmaId());
        $orderInfo = $this->orderService->overrideLegacyOrder((int) $returnInfo['order_id']);

        if (\is_array($orderInfo)) {
            $returnInfo['extra'] = \unserialize($returnInfo['extra'] ?? '');

            $user = $this->userRepository->get($returnInfo['user_id']);
            $rmaAddress = fn_w_get_rma_address($orderInfo['company_id']);
            $reasons = fn_get_rma_properties(RMA_REASON);
            $company = new Company($orderInfo['company_id']);
            $order = $this->orderService->getOrder((int) $returnInfo['order_id']);

            if ($event->getNotifyCustomer()) {
                $this->sendEmailToAddress(
                    $orderInfo['email'],
                    __(
                        'w_rma_return_requested_subj',
                        array_merge(
                            [
                                '[firstname]' => $user->getFirstname(),
                                '[lastname]' => $user->getLastname(),
                                '[company_name]' => $company->getName(),
                                '[orderId]' => $orderInfo['order_id']
                            ],
                            $order->getExtraDataForMailNotificationVariables('w_rma_return_requested_subj')
                        )
                    ),
                    '@App/mail/rma/rma-requested.html.twig',
                    [
                        'rma_address' => $rmaAddress,
                        'return_info' => $returnInfo,
                        'order_info' => $orderInfo,
                        'reasons' => $reasons,
                        'user' => [
                            'firstname' => $user->getFirstname(),
                            'lastname' => $user->getLastname(),
                        ],
                        'company_name' => $company->getName(),
                        'order_extra' => $order->getExtraDataForMailNotificationVariables('w_rma_request')
                    ]
                );
            }

            if ($event->getNotifyCompany()) {
                $company = new Company($orderInfo['company_id']);
                $this->sendEmailToCompany(
                    $company,
                    __(
                        'w_rma_return_requested_subj',
                        array_merge(
                            [
                                '[firstname]' => $user->getFirstname(),
                                '[lastname]' => $user->getLastname(),
                                '[company_name]' => $company->getName(),
                                '[orderId]' => $orderInfo['order_id']
                            ],
                            $order->getExtraDataForMailNotificationVariables('w_rma_return_requested_subj')
                        )
                    ),
                    '@App/mail/rma/company-rma-requested.html.twig',
                    [
                        'rma_address' => $rmaAddress,
                        'return_info' => $returnInfo,
                        'order_info' => $orderInfo,
                        'reasons' => $reasons,
                        'user' => $user,
                        'order_extra' => $order->getExtraDataForMailNotificationVariables('w_rma_request_vendor')
                    ]
                );
            }
        }
    }

    public function rmaCompleted(RmaReceived $event)
    {
        if ($event->getNotifyCustomer()) {
            $returnInfo = fn_get_return_info($event->getRmaId());
            $returnInfo['extra'] = unserialize($returnInfo['extra'] ?? '');
            $orderInfo = $this->orderService->overrideLegacyOrder((int) $returnInfo['order_id']);

            $user = $this->userRepository->get($returnInfo['user_id']);
            $order = $this->orderService->getOrder((int) $returnInfo['order_id']);

            $rmaAddress = fn_w_get_rma_address($orderInfo['company_id']);
            $reasons = fn_get_rma_properties(RMA_REASON);
            $company = new Company($orderInfo['company_id']);

            $this->sendEmailToAddress(
                $orderInfo['email'],
                __(
                    'w_rma_refund_subject',
                    array_merge(
                        [
                            '[firstname]' => $user->getFirstname(),
                            '[lastname]' => $user->getLastname(),
                            '[company_name]' => $company->getName(),
                            '[orderId]' => $orderInfo['order_id']
                        ],
                        $order->getExtraDataForMailNotificationVariables('w_rma_return_requested_subj')
                    )
                ),
                '@App/mail/rma/rma-completed.html.twig',
                [
                    'rma_address' => $rmaAddress,
                    'return_info' => $returnInfo,
                    'order_info' => $orderInfo,
                    'reasons' => $reasons,
                    'user' => $user,
                    'company_name' => $company->getName(),
                    'order_extra' => $order->getExtraDataForMailNotificationVariables('w_rma_return_received'),
                ],
                isset($orderInfo['lang_code']) ? new Locale($orderInfo['lang_code']) : null
            );
        }
    }

    public function rmaReceived(RmaReceived $event)
    {
        if ($event->getNotifyCustomer()) {
            $returnInfo = fn_get_return_info($event->getRmaId());
            $returnInfo['extra'] = unserialize($returnInfo['extra'] ?? '');
            $orderInfo = $this->orderService->overrideLegacyOrder((int) $returnInfo['order_id']);

            $user = $this->userRepository->get($returnInfo['user_id']);
            $order = $this->orderService->getOrder((int) $returnInfo['order_id']);

            $rmaAddress = fn_w_get_rma_address($orderInfo['company_id']);
            $reasons = fn_get_rma_properties(RMA_REASON);
            $company = new Company($orderInfo['company_id']);

            $this->sendEmailToAddress(
                $orderInfo['email'],
                __(
                    'w_rma_return_requested_subj',
                    array_merge(
                        [
                            '[firstname]' => $user->getFirstname(),
                            '[lastname]' => $user->getLastname(),
                            '[company_name]' => $company->getName(),
                            '[orderId]' => $orderInfo['order_id']
                        ],
                        $order->getExtraDataForMailNotificationVariables('w_rma_return_requested_subj')
                    )
                ),
                '@App/mail/rma/rma-received.html.twig',
                [
                    'rma_address' => $rmaAddress,
                    'return_info' => $returnInfo,
                    'order_info' => $orderInfo,
                    'reasons' => $reasons,
                    'user' => [
                        'firstname' => $user->getFirstname(),
                        'lastname' => $user->getLastname(),
                    ],
                    'return_status' => fn_get_status_data($returnInfo['status'], STATUSES_RETURN, $returnInfo['return_id'], $orderInfo['lang_code']),
                    'order_extra' => $order->getExtraDataForMailNotificationVariables('w_rma_refound_products')
                ],
                isset($orderInfo['lang_code']) ? new Locale($orderInfo['lang_code']) : null
            );
        }
    }

    public function rmaDeclined(RmaDeclined $event)
    {
        if ($event->getNotifyCustomer()) {
            $returnInfo = fn_get_return_info($event->getRmaId());
            $returnInfo['extra'] = unserialize($returnInfo['extra'] ?? '');
            $orderInfo = $this->orderService->overrideLegacyOrder((int) $returnInfo['order_id']);

            $user = $this->userRepository->get($returnInfo['user_id']);
            $order = $this->orderService->getOrder((int) $returnInfo['order_id']);

            $rmaAddress = fn_w_get_rma_address($orderInfo['company_id']);
            $reasons = fn_get_rma_properties(RMA_REASON);
            $company = new Company($orderInfo['company_id']);

            $this->sendEmailToAddress(
                $orderInfo['email'],
                __(
                    'w_rma_return_requested_subj',
                    array_merge(
                        [
                            '[firstname]' => $user->getFirstname(),
                            '[lastname]' => $user->getLastname(),
                            '[company_name]' => $company->getName(),
                            '[orderId]' => $orderInfo['order_id']
                        ],
                        $order->getExtraDataForMailNotificationVariables('w_rma_return_requested_subj')
                    )
                ),
                '@App/mail/rma/rma-declined.html.twig',
                [
                    'rma_address' => $rmaAddress,
                    'return_info' => $returnInfo,
                    'order_info' => $orderInfo,
                    'reasons' => $reasons,
                    'return_status' => fn_get_status_data(
                        $returnInfo['status'],
                        STATUSES_RETURN,
                        $returnInfo['return_id'],
                        $orderInfo['lang_code']
                    ),
                    'user' => [
                        'firstname' => $user->getFirstname(),
                        'lastname' => $user->getLastname(),
                    ],
                    'order_extra' => $order->getExtraDataForMailNotificationVariables('w_rma_refound_products')
                ],
                isset($orderInfo['lang_code']) ? new Locale($orderInfo['lang_code']) : null
            );
        }
    }

    public function litigationCreated(LitigationCreated $event)
    {
        $order = $this->orderService->getOrder($event->getOrderId());
        $user = $this->userRepository->get($order->getUserId());

        $items = [];
        $ids = array_flip($event->getDeclinations());
        foreach ($order->getItems() as $item) {
            if (isset($ids[$item->getDeclinationId()])) {
                $items[] = $item;
            }
        }

        $this->sendEmailToCustomer(
            $user,
            __('w_litigation_created_customer', [
                '[firstname]' => $user->getFirstname(),
                '[lastname]' => $user->getLastname(),
            ]),
            '@App/mail/rma/litigation-created.html.twig',
            [
                'order' => $order,
                'firstname' => $user->getFirstname(),
                'lastname' => $user->getLastname(),
                'order_extra' => $order->getExtraDataForMailNotificationVariables('w_litigation_email_content_to_buyer')
            ]
        );

        $company = new Company($order->getCompanyId());
        $this->sendEmailToCompany(
            $company,
            __('w_litigation_created_company', [
                '[firstname]' => $user->getFirstname(),
                '[lastname]' => $user->getLastname(),
            ]),
            '@App/mail/rma/company-litigation-created.html.twig',
            [
                'order' => $order,
                'user' => $user,
                'items' => $items,
                'comment' => $event->getComment(),
                'firstname' => $user->getFirstname(),
                'lastname' => $user->getLastname(),
                'order_extra' => $order->getExtraDataForMailNotificationVariables('w_SAV_email_content')
            ]
        );
    }

    public function afterSalesServiceRequested(AfterSalesServiceRequested $event)
    {
        $order = $this->orderService->getOrder($event->getOrderId());
        $user = $this->userRepository->get($order->getUserId());

        $items = [];
        $ids = array_flip($event->getDeclinations());
        foreach ($order->getItems() as $item) {
            if (isset($ids[$item->getDeclinationId()])) {
                $items[] = $item;
            }
        }

        $company = new Company($order->getCompanyId());
        $this->sendEmailToCompany(
            $company,
            __('w_after_sales_service_requested', [
                '[firstname]' => $user->getFirstname(),
                '[lastname]' => $user->getLastname()
            ]),
            '@App/mail/rma/company-after-sales-service-requested.html.twig',
            [
                'order' => $order,
                'user' => $user,
                'items' => $items,
                'comment' => $event->getComment(),
                'order_extra' => $order->getExtraDataForMailNotificationVariables('w_SAV_email_content')
            ]
        );
    }
}
