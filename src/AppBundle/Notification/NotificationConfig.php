<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Notification;

use Wizacha\AppBundle\Controller\DomainUserTrait;
use Wizacha\Marketplace\User\User;

class NotificationConfig
{
    use DomainUserTrait;

    private int $id;

    private string $className;

    private string $description;

    private string $notifier;

    private bool $enabled;

    private \DateTimeImmutable $createdAt;

    private ?\DateTimeImmutable $updatedAt;

    private ?User $user = null;

    public function __construct(
        string $className,
        string $description,
        string $notifier,
        bool $enabled
    ) {
        $this
            ->setClassName($className)
            ->setDescription($description)
            ->setNotifier($notifier)
            ->setEnabled($enabled);
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getClassName(): string
    {
        return $this->className;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function getNotifier(): string
    {
        return $this->notifier;
    }

    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    public function getCreatedAt(): \DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function getUpdatedAt(): ?\DateTimeImmutable
    {
        return $this->updatedAt;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setClassName(string $className): self
    {
        $this->className = $className;

        return $this;
    }

    public function setDescription(string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function setNotifier(string $notifier): self
    {
        $this->notifier = $notifier;

        return $this;
    }

    public function setEnabled(bool $enabled): self
    {
        $this->enabled = $enabled;

        return $this;
    }

    public function defineCreatedAt(): self
    {
        $this->createdAt = new \DateTimeImmutable();

        return $this;
    }

    public function defineUpdatedAt(): self
    {
        $this->updatedAt = new \DateTimeImmutable();

        return $this;
    }

    public function setUser(?User $user = null): self
    {
        $this->user = $user;

        return $this;
    }
}
