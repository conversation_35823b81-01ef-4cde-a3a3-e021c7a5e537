<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Notification\DTO;

class InternalTransferErrorDTO
{
    /** @var int */
    private $orderId;

    /** @var float */
    private $amount;

    /** @var string */
    private $sourceTransactionId;

    /** @var string|null */
    private $internalTransactionId;

    /** @var string */
    private $fromWallet;

    /** @var string */
    private $toWallet;

    public function __construct(
        int $orderId,
        float $amount,
        string $sourceTransactionId,
        ?string $internalTransactionId,
        string $fromWallet,
        string $toWallet
    ) {
        $this->orderId = $orderId;
        $this->amount = $amount;
        $this->sourceTransactionId = $sourceTransactionId;
        $this->internalTransactionId = $internalTransactionId;
        $this->fromWallet = $fromWallet;
        $this->toWallet = $toWallet;
    }

    public function getOrderId(): int
    {
        return $this->orderId;
    }

    public function getAmount(): float
    {
        return $this->amount;
    }

    public function getSourceTransactionId(): string
    {
        return $this->sourceTransactionId;
    }

    public function getInternalTransactionId(): ?string
    {
        return $this->internalTransactionId;
    }

    public function getFromWallet(): string
    {
        return $this->fromWallet;
    }

    public function getToWallet(): string
    {
        return $this->toWallet;
    }
}
