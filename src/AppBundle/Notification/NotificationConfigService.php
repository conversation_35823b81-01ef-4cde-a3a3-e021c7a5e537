<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Notification;

class NotificationConfigService
{
    protected NotificationConfigRepository $notificationConfigRepository;

    public function __construct(
        NotificationConfigRepository $notificationConfigRepository
    ) {
        $this->notificationConfigRepository = $notificationConfigRepository;
    }

    /**
     * @param NotificationConfig[]
     *
     * @return NotificationConfig[]
     */
    public function batchSave(array $notifications): array
    {
        return $this->notificationConfigRepository->batchSave($notifications);
    }

    /**
     * @param mixed[] $filters
     * @param mixed[] $order
     *
     * @return NotificationConfig[]
     */
    public function findBy(array $filters, array $order = null): ?array
    {
        return $this->notificationConfigRepository->findBy($filters, $order);
    }
}
