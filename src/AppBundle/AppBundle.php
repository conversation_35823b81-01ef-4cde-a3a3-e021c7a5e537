<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle;

use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\HttpKernel\Bundle\Bundle;
use Wizacha\AppBundle\DependencyInjection\Compiler\AmqpsPass;
use Wizacha\AppBundle\DependencyInjection\Compiler\TranslatorPass;

class AppBundle extends Bundle
{
    public function build(ContainerBuilder $container)
    {
        $container->addCompilerPass(new TranslatorPass());
        $container->addCompilerPass(new AmqpsPass());
    }
}
