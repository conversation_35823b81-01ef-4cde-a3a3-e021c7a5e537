<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller;

use HiPay\Fullservice\Enum\Transaction\TransactionStatus as HiPayTransactionStatus;
use HiPay\Fullservice\Gateway\Mapper\TransactionMapper;
use MangoPay\EventType;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\OptionsResolver\Exception\UndefinedOptionsException;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Core\Concurrent\BlockingMutex;
use Wizacha\Core\Concurrent\MutexException;
use Wizacha\Core\Concurrent\MutexService;
use Wizacha\Marketplace\CreditCard\CreditCard;
use Wizacha\Marketplace\CreditCard\Service\CreditCardService;
use Wizacha\Marketplace\Order\Action\Confirm;
use Wizacha\Marketplace\Order\Action\MarkAsPaid;
use Wizacha\Marketplace\Order\Exception\OrderNotFound;
use Wizacha\Marketplace\Order\Order as MarkeplaceOrder;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\Order\Tracer\Tracer;
use Wizacha\Marketplace\Order\Workflow\WorkflowName;
use Wizacha\Marketplace\Payment\Event\BankwireFailedToRetrieveOrderEvent;
use Wizacha\Marketplace\Payment\Event\HipayPaymentCallbackEvent;
use Wizacha\Marketplace\Payment\Event\HipayTransactionChargedbackEvent;
use Wizacha\Marketplace\Payment\Event\LemonwayPaymentCallbackEvent;
use Wizacha\Marketplace\Payment\Event\MangopayPaymentCallbackEvent;
use Wizacha\Marketplace\Payment\Event\SmoneyPaymentCallbackEvent;
use Wizacha\Marketplace\Payment\Event\StripeFailedPaymentCallbackEvent;
use Wizacha\Marketplace\Payment\Event\StripePaymentCallbackEvent;
use Wizacha\Marketplace\Payment\GenericTransactionDetails;
use Wizacha\Marketplace\Payment\LemonWay\LemonWayApi;
use Wizacha\Marketplace\Payment\LemonWay\LemonWayTokenRetriever;
use Wizacha\Marketplace\Payment\LemonWay\LemonWayTransactionSyncService;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\PaymentType;
use Wizacha\Marketplace\Payment\Processor\HiPay;
use Wizacha\Marketplace\Payment\Processor\LemonWay;
use Wizacha\Marketplace\Payment\Stripe\StripeCardException;
use Wizacha\Marketplace\Payment\Stripe\StripeSepaDirectProcessor;
use Wizacha\Marketplace\Transaction\Exception\TransactionNotFound;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionStatus;
use Wizacha\Marketplace\Transaction\TransactionType;
use Wizacha\Money\Money;
use Wizacha\Order;
use Wizacha\OrderStatus as LegacyOrderStatus;

class PaymentNotificationController extends Controller
{
    protected const HIPAY_REFUND_STATUS = [
        HiPayTransactionStatus::REFUND_REQUESTED,
        HiPayTransactionStatus::REFUNDED,
        HiPayTransactionStatus::PARTIALLY_REFUNDED
    ];

    protected LoggerInterface $logger;
    protected OrderService $orderService;
    protected EventDispatcherInterface $eventDispatcher;
    protected TransactionService $transactionService;
    protected CreditCardService $creditCardService;
    protected MutexService $mutexService;
    protected Tracer $tracer;
    protected string $hipayChargebackTestEnv;

    public function __construct(
        LoggerInterface $logger,
        OrderService $orderService,
        CreditCardService $creditCardService,
        EventDispatcherInterface $eventDispatcher,
        TransactionService $transactionService,
        MutexService $mutexService,
        Tracer $tracer,
        string $hipayChargebackTestEnv
    ) {
        $this->orderService = $orderService;
        $this->logger = $logger;
        $this->eventDispatcher = $eventDispatcher;
        $this->transactionService = $transactionService;
        $this->creditCardService = $creditCardService;
        $this->mutexService = $mutexService;
        $this->tracer = $tracer;
        $this->hipayChargebackTestEnv = $hipayChargebackTestEnv;
    }

    /**
     * Est appelé par les serveurs de Mangopay quand un virement bancaire est créé de leur coté
     */
    public function mangopayBankwireAction(Request $request)
    {
        $mangopay = $this->get('marketplace.payment.mangopay');
        if (!$mangopay->isConfigured()) {
            $this->logger->error('We received a hook from Mangopay, but it\'s not configured on this platform', [
                'request' => $request->query->all(),
            ]);

            return new Response('', Response::HTTP_NOT_IMPLEMENTED);
        }

        $eventType = $request->query->get('EventType');

        /**
         * @see https://docs.mangopay.com/endpoints/v2.01/hooks#e246_the-hook-object
         * Note that in the URL parameters, RessourceId has two "s", and not one as in the API objects - this is a mistake and will be corrected in a future API version
         */
        $resourceId = $request->query->get('ResourceId', $request->query->get('RessourceId'));

        if ($eventType === null || $resourceId === null) {
            return new Response('', Response::HTTP_BAD_REQUEST);
        }

        if (false === \in_array($eventType, [EventType::PayinNormalSucceeded, EventType::PayinNormalFailed])) {
            return new Response('', Response::HTTP_NOT_IMPLEMENTED);
        }

        $orderId = $mangopay->getTransactionOrderId($resourceId);
        if ($orderId === null) {
            $this->logger->error('Mangopay hook: cannot find order ID in Mangopay tags', ['id' => $resourceId]);

            return new Response('', Response::HTTP_BAD_REQUEST);
        }

        $this->eventDispatcher->dispatch(
            new MangopayPaymentCallbackEvent(
                new Order($orderId),
                $resourceId,
                TransactionType::BANK_WIRE()
            )
        );

        if (false === $mangopay->isTransactionSuccessful($resourceId)) {
            $this->logger->error('Mangopay hook: transaction is not successful', ['id' => $resourceId]);

            return new Response('', Response::HTTP_BAD_REQUEST);
        }

        $transaction = $this->transactionService->getOneBy([
            'transactionReference' => $resourceId,
            'processorName' => PaymentProcessorName::MANGOPAY()
        ]);

        if (\array_key_exists('mangopay_bankwire', $transaction->getProcessorInformations())) {
             // Avancement du workflow
            $orders = $this->orderService->getChildOrders($orderId);
            $markAsPaid = $this->get('marketplace.order.action.mark_as_paid');
            $confirm = $this->get('marketplace.order.action.confirm');

            foreach ($orders as $order) {
                if ($markAsPaid->isAllowed($order)) {
                    $markAsPaid->execute($order);
                }

                if ($confirm->isAllowed($order)) {
                    $confirm->execute($order);
                }
            }

            // Change the status only if: successful, match an order, is a bankwire transaction
            fn_change_order_status($orderId, OrderStatus::STANDBY_VENDOR);
        }

        // 200 even if the transaction is not a bank wire, to ignore the event
        return new Response('', Response::HTTP_OK);
    }

    /**
     * Est appelé par - le client quand il revient de la page de paiement Lemonway
     *                - le serveur Lemonway en même temps qu'il redirige l'utilisateur, après le paiement
     */
    public function lemonwayCardAction(Request $request)
    {
        // If the request_method is POST, this is a server callback, if it's GET, this is the client. In both cases, we have $_REQUEST['response_wkToken']

        $wkToken = $request->get('response_wkToken');

        if (!$wkToken) {
            $this->logger->error('Lemonway: received payment hook without wkToken', [
                'request' => $request->query->all(),
            ]);

            return $this->redirectToRoute('home');
        }

        \define('PAYMENT_NOTIFICATION', true);

        /** @var LemonWay */
        $lemonway = $this->get('marketplace.payment.lemonway');
        /** @var LemonWayTransactionSyncService */
        $lemonwayTransactionSyncService = $this->get(LemonWayTransactionSyncService::class);

        try {
            $mutex = $this->databaseMutex('lemonway_card_' . $wkToken);
        } catch (MutexException $e) {
            // Cannot lock, log it and try without mutex
            $this->logger->error($e->getMessage(), ['exception' => $e]);
        }

        $orderId = $lemonway->getOrderId($wkToken);

        // Event dispatched to trigger capture if needed
        $transactionDetails = $lemonway->getTransactionDetailsByToken($wkToken);
        $this->eventDispatcher->dispatch(
            new LemonwayPaymentCallbackEvent(
                new Order($orderId),
                $transactionDetails->getTransactionId(),
                TransactionType::CREDITCARD()
            )
        );

        $orders = $this->orderService->getChildOrders($orderId);

        $ordersSynchronizedTransactions = \array_map(
            fn(MarkeplaceOrder $order) => $lemonwayTransactionSyncService->syncTransactionStatusByToken(
                TransactionType::CREDITCARD(),
                $order->getId(),
                $wkToken
            ),
            $orders
        );

        $isTransactionSuccessful = $this->transactionService->isTransactionSuccessful(
            $ordersSynchronizedTransactions
        );

        if (true === $isTransactionSuccessful
            && false === $this->orderService->isPaid($orderId)
            && false === $this->orderService->isBillingFailed($orderId)
        ) {
            /**
             * Avancement du workflow
             */
            /** @var MarkAsPaid */
            $markAsPaid = $this->get('marketplace.order.action.mark_as_paid');
            /** @var Confirm */
            $confirm = $this->get('marketplace.order.action.confirm');
            foreach ($orders as $order) {
                if ($markAsPaid->isAllowed($order)) {
                    $markAsPaid->execute($order);
                }

                if ($confirm->isAllowed($order)) {
                    $confirm->execute($order);
                }
            }

            // We tag the order as "paid" only if it wasn't yet
            fn_change_order_status($orderId, OrderStatus::STANDBY_VENDOR);
        } elseif (false === $isTransactionSuccessful
            && false === $lemonway->isTransactionDetailsCancelled($transactionDetails)
        ) {
            /**
             * Avancement du workflow
             */
            $markPaymentAsRefused = $this->get('marketplace.order.action.mark_payment_as_refused');
            foreach ($orders as $order) {
                if ($markPaymentAsRefused->isAllowed($order)) {
                    $markPaymentAsRefused->execute($order);
                }
            }

            // Cancelled orders must remain incomplete, to trash them later and avoid useless mails
            fn_change_order_status($orderId, OrderStatus::BILLING_FAILED);
        }

        unset($mutex);

        return $this->postProcessResponse(
            $request,
            fn_order_placement_routines('route', $orderId, ['C' => false, 'A' => false, 'V' => false])
        );
    }

    public function lemonwayBankwireAction(Request $request): Response
    {
        \define('PAYMENT_NOTIFICATION', true);

        $lemonway = $this->get('marketplace.payment.lemonway');

        if (false === $lemonway->isConfigured()) {
            $this->logger->error('We received a hook from Lemonway, but it\'s not configured on this platform', [
                'request' => $request->request->all(),
            ]);

            return new Response('', Response::HTTP_NOT_IMPLEMENTED);
        }

        // Resolves Request parameters
        $resolver = (new OptionsResolver())
            ->setDefined([
                'Amount',
                'ExtId',
                'IntId',
                'NotifDate',
                'Payment_method',
            ])
            ->setRequired([
                'IdTransaction',
                'NotifCategory',
                'Status',
            ])
            ->setAllowedValues('NotifCategory', LemonWayApi::NOTIFICATION_CATEGORY_BANKWIRE);

        try {
            $notification = $resolver->resolve($request->request->all());
        } catch (UndefinedOptionsException $e) {
            $this->logger->error('[Lemonway] bankwire hook received bad request', [
                'request' => $request->request->all(),
                'exception' => $e,
            ]);

            return new BadRequestJsonResponse($e->getMessage());
        }

        $transactionDetails = $lemonway->getTransactionDetails($notification['IdTransaction']);

        // Retrieve Orders
        try {
            $mainOrderId = $lemonway
                ->getOrderTokenService()
                ->getOrderId(
                    LemonWayTokenRetriever::fromBankWireLabel($transactionDetails->getComment())
                )
            ;

            $orders = $this->orderService->getChildOrders($mainOrderId);

            if ($this->orderService->isParentOrder($mainOrderId) === false) {
                $totalAmount = $this->orderService->getOrder($mainOrderId)->getTotal();
            } else {
                $totalAmount = Money::fromVariable(0);
                foreach ($orders as $order) {
                    $totalAmount = $totalAmount->add($order->getTotal());
                }
            }
        } catch (\Throwable $e) {
            $this->logger->error("[Lemonway] Unable to retrieve order from bankwire transaction", [
                'transactionId' => $notification['IdTransaction'],
                'comment' => $transactionDetails->getComment(),
                'exception' => $e,
            ]);

            // Trigger email notification
            $this->eventDispatcher->dispatch(
                new BankwireFailedToRetrieveOrderEvent(
                    $transactionDetails->getComment(),
                    $transactionDetails->getAmount(),
                    $transactionDetails->getTransactionId(),
                    $lemonway->getConfig()->getCurrencyCode()
                ),
                BankwireFailedToRetrieveOrderEvent::class
            );

            return new BadRequestJsonResponse("Unable to retrieve order from transaction, id: {$notification['IdTransaction']}, comment: {$transactionDetails->getComment()}.");
        }

        if ($totalAmount->equals(Money::fromVariable($transactionDetails->getAmount())) === false) {
            // Trigger email notification
            $this->eventDispatcher->dispatch(
                new BankwireFailedToRetrieveOrderEvent(
                    $transactionDetails->getComment(),
                    $transactionDetails->getAmount(),
                    $transactionDetails->getTransactionId(),
                    $lemonway->getConfig()->getCurrencyCode()
                ),
                BankwireFailedToRetrieveOrderEvent::class
            );

            return new BadRequestJsonResponse("Wrong transaction amount, id: {$notification['IdTransaction']}, comment: {$transactionDetails->getComment()}.");
        }

        // Triggers additional transfer in LemonWayEventSubscriber
        $this->eventDispatcher->dispatch(
            new LemonwayPaymentCallbackEvent(
                new Order($mainOrderId),
                $transactionDetails->getTransactionId(),
                TransactionType::BANK_WIRE()
            )
        );

        // Marks payment as validates / fail or do nothing
        if (true === $transactionDetails->isSuccessFul()
            && false === $this->orderService->isPaid($mainOrderId)
            && false === $this->orderService->isBillingFailed($mainOrderId)
        ) {
            $this->orderService->markOrdersWithAction(
                $mainOrderId,
                $this->get('marketplace.order.action.mark_as_paid'),
                OrderStatus::STANDBY_VENDOR(),
                $orders
            );
        } else {
            if (false === $transactionDetails->isSuccessFul()) {
                $this->orderService->markOrdersWithAction(
                    $mainOrderId,
                    $this->get('marketplace.order.action.mark_payment_as_refused'),
                    OrderStatus::BILLING_FAILED(),
                    $orders
                );
            }

            // Trigger email notification
            $this->eventDispatcher->dispatch(
                new BankwireFailedToRetrieveOrderEvent(
                    $transactionDetails->getComment(),
                    $transactionDetails->getAmount(),
                    $transactionDetails->getTransactionId(),
                    $lemonway->getConfig()->getCurrencyCode()
                ),
                BankwireFailedToRetrieveOrderEvent::class
            );
        }

        return $this->postProcessResponse(
            $request,
            fn_order_placement_routines('route', $mainOrderId, ['C' => false, 'A' => false, 'V' => false])
        );
    }

    /**
     * Est appelé par le client quand il revient de la page de paiement Mangopay
     */
    public function mangopayCardAction(Request $request)
    {
        /*
         * MangoPay uses transactionId when the user's browser is redirected,
         * but they use RessourceId when they call a hook (e.g. the payment didn't succeed after a while).
         * And we anticipate the typo fix.
         *
         * @see https://docs.mangopay.com/endpoints/v2.01/hooks#e246_the-hook-object
         * Note that in the URL parameters, RessourceId has two "s", and not one as in the API objects - this is a mistake and will be corrected in a future API version
         */
        $transactionId = $request->get('transactionId')
            ?? $request->get('RessourceId')
            ?? $request->get('ResourceId')
        ;

        if (!$transactionId) {
            $this->logger->error('Mangopay: received payment hook without transactionId', [
                'request' => $request->query->all(),
            ]);

            return $this->redirectToRoute('home');
        }

        \define('PAYMENT_NOTIFICATION', true);

        $mangopay = $this->get('marketplace.payment.mangopay');
        if ($orderId = $mangopay->getTransactionOrderId($transactionId)) {
            $this->eventDispatcher->dispatch(
                new MangopayPaymentCallbackEvent(
                    new Order($orderId),
                    $transactionId,
                    TransactionType::CREDITCARD()
                )
            );

            $orders = $this->orderService->getChildOrders($orderId);
            if (true === $mangopay->isTransactionSuccessful($transactionId)) {
                /**
                 * Avancement du workflow
                 */
                $markAsPaid = $this->get('marketplace.order.action.mark_as_paid');
                $confirm = $this->get('marketplace.order.action.confirm');
                foreach ($orders as $order) {
                    if ($markAsPaid->isAllowed($order)) {
                        $markAsPaid->execute($order);
                    }

                    if ($confirm->isAllowed($order)) {
                        $confirm->execute($order);
                    }
                }

                fn_change_order_status($orderId, OrderStatus::STANDBY_VENDOR);
            } elseif (false === $mangopay->isTransactionCancelled($transactionId)) {
                /**
                 * Avancement du workflow
                 */
                $markPaymentAsRefused = $this->get('marketplace.order.action.mark_payment_as_refused');
                foreach ($orders as $order) {
                    if ($markPaymentAsRefused->isAllowed($order)) {
                        $markPaymentAsRefused->execute($order);
                    }
                }

                fn_change_order_status($orderId, OrderStatus::BILLING_FAILED);
            }
        } else {
            $this->logger->error(
                'Mangopay card error on payment! Cannot find the orderId in the transaction data',
                [
                    'transactionId' => $transactionId,
                ]
            );
        }

        return $this->postProcessResponse(
            $request,
            fn_order_placement_routines('route', $orderId, ['C' => false, 'A' => false, 'V' => false])
        );
    }

    /**
     * Est appelé par le client quand il revient de la page de paiement HiPay
     * OU par le serveur HiPay pour le webhook
     */
    public function hipayCardAction(Request $request)
    {
        $hipay = $this->get('marketplace.payment.hipay');
        $transactionId = null;

        // We manage requests GET and POST
        switch ($request->getMethod()) {
            case 'GET':
                $transactionId = $request->query->get('reference');
                if (true === $this->isRenewCreditCardRequest($request)) {
                    // On est dans le cas d'un renouvellement de credit card, on bypass la suite du script
                    fn_set_notification('N', __('subscription_renew_credit_card'), __('renew_credit_card_success'));

                    return $this->redirect(fn_url('?dispatch=subscriptions.manage'));
                }

                break;

            case 'POST':
                $transactionId = $request->request->get('transaction_reference');
                if (true === $this->isRenewCreditCardRequest($request)) {
                    // On est dans le cas d'un register de credit card
                    if (false === $this->creditCardService->replace($request->request->all(), HiPay::class) instanceof CreditCard) {
                        $this->logger->error('HiPay: Unable to register a new credit card.', [
                            'request' => $request->request->all(),
                        ]);
                    }

                    return new Response('');
                }

                break;
        }

        if ($transactionId === null) {
            $this->logger->error('Hipay: received payment hook without transactionId', [
                'request' => $request->query->all(),
            ]);

            return new Response('', Response::HTTP_BAD_REQUEST);
        }

        // Patch to allow testing of chargeback since it's not implemented on hipay's side.
        if (\filter_var($this->hipayChargebackTestEnv, FILTER_VALIDATE_BOOLEAN) === true
            && $request->request->get('status') === HiPayTransactionStatus::CHARGED_BACK
        ) {
            $transactionMapper = new TransactionMapper($request->request->all());
            $hipayTransaction =  $transactionMapper->getModelObjectMapped();

            $orderId = $request->request->get("order[id]");

            $transaction = new GenericTransactionDetails(
                $hipayTransaction->getTransactionReference(),
                $orderId,
                $hipayTransaction->getStatus(),
                $hipayTransaction
            );
        } else {
            $transaction = $hipay->getTransactionDetails($transactionId);
            $orderId = $transaction->getOrderId();

            // On récupere l'order_id de remboursement et on vérifie sa existance
            $operation = $request->request->get('operation') ?? null;
            if ($operation !== null && \array_key_exists('id', $operation) == true) {
                try {
                    $orderRefundedId = $hipay->decodeOrderId($operation['id']);
                    $order = $this->orderService->getOrder($orderRefundedId);
                    $orderId = $orderRefundedId;
                } catch (OrderNotFound $e) {
                }
            }

            try {
                $mutex = $this->databaseMutex('hipay_card_' . $orderId);
            } catch (MutexException $e) {
                // Cannot lock, log it and try without mutex
                $this->logger->error($e->getMessage(), ['exception' => $e]);
            }

            // Request treatment
            \define('PAYMENT_NOTIFICATION', true);
        }

        if (HiPayTransactionStatus::CHARGED_BACK === $transaction->getStatus()) {
            $order = new Order($orderId);
            $this->eventDispatcher->dispatch(
                new HipayTransactionChargedbackEvent(
                    $order,
                    $transactionId,
                    TransactionType::CREDITCARD()
                )
            );

            // Can't trace if order doesn't exist
            if (\filter_var($this->hipayChargebackTestEnv, FILTER_VALIDATE_BOOLEAN) === false) {
                $this->tracer->trace(\intval($orderId), "Chargeback email sent");
            }

            return new Response('Hipay: An Email has been sent to the operator for the chargeback.', Response::HTTP_OK);
        }

        if (\in_array($transaction->getStatus(), static::HIPAY_REFUND_STATUS) === true) {
            $this->eventDispatcher->dispatch(
                new HipayPaymentCallbackEvent(
                    new Order($orderId),
                    $transactionId,
                    TransactionType::REFUND()
                )
            );
        } else {
            $this->eventDispatcher->dispatch(
                new HipayPaymentCallbackEvent(
                    new Order($orderId),
                    $transactionId,
                    TransactionType::CREDITCARD()
                )
            );
        }

        if ($hipay->checkingOrderPayment($transaction->getId())) {
            $orders = $this->orderService->getChildOrders($orderId);
            $this->orderService->removeFromGarbage($orders);

            $childMarkedAsPaid = false;
            $markAsPaid = $this->get('marketplace.order.action.mark_as_paid');
            $confirm = $this->get('marketplace.order.action.confirm');

            // We mark as paid the childrens order if they weren't yet
            foreach ($orders as $order) {
                if ($markAsPaid->isAllowed($order)) {
                    $markAsPaid->execute($order);
                    $childMarkedAsPaid = true;
                }
                if ($confirm->isAllowed($order)) {
                    $confirm->execute($order);
                }
            }

            // Update legacy status, only if we has marked as pay a child order
            if ($childMarkedAsPaid) {
                fn_change_order_status($orderId, LegacyOrderStatus::STANDBY_VENDOR);
            }
        }

        unset($mutex);

        // Generate Response
        return $this->postProcessResponse(
            $request,
            fn_order_placement_routines('route', $orderId, ['C' => false, 'A' => false, 'V' => false])
        );
    }

    /**
     * Est appelé par - le client quand il revient de la page de paiement SMoney
     *                - le serveur SMoney en même temps qu'il redirige l'utilisateur, après le paiement
     */
    public function smoneyCardAction(Request $request): Response
    {
        // If the request_method is POST, this is a server callback, if it's GET, this is the client.
        // In the first case we have 'id' and in GET 'Id' ...
        // In the case of a POST request, we have to check if type===1.
        // If not, throw a not found because SMoney try to use CB callback for other things

        if ($request->isMethod('POST') && $request->get('type') != 1) {
            $this->createNotFoundException();
        }


        $orderId = $this->get('marketplace.payment.smoney')->getOrderId($request);
        if (null === $orderId) {
            $this->logger->error('SMoney: received payment hook without order Id', [
                'requestGet' => $request->query->all(),
                'requestPost' => $request->request->all(),
            ]);

            return $this->redirectToRoute('home');
        }

        \define('PAYMENT_NOTIFICATION', true);

        $smoney = $this->get('marketplace.payment.smoney');

        try {
            $mutex = $this->databaseMutex('smoney_card_' . $orderId);
        } catch (MutexException $e) {
            // Cannot lock, log it and try without mutex
            $this->logger->error($e->getMessage(), ['exception' => $e]);
        }

        // Event dispatched to trigger capture if needed
        $this->eventDispatcher->dispatch(
            new SmoneyPaymentCallbackEvent(
                new Order($orderId),
                $orderId,
                TransactionType::CREDITCARD()
            )
        );

        try {
            $this->orderService->getAnyOrder($orderId);
        } catch (OrderNotFound $exception) {
            unset($mutex);

            return $this->postProcessResponse(
                $request,
                fn_order_placement_routines('route', null, ['C' => false, 'A' => false, 'V' => false])
            );
        }

        $isTransactionSuccessful = $smoney->isTransactionSuccessful((string) $orderId);
        if (true === $isTransactionSuccessful && false === $this->orderService->isPaid($orderId)) {
            /**
             * Avancement du workflow
             */
            $orders = $this->orderService->getChildOrders($orderId);
            $markAsPaid = $this->get('marketplace.order.action.mark_as_paid');
            $confirm = $this->get('marketplace.order.action.confirm');
            foreach ($orders as $order) {
                if ($markAsPaid->isAllowed($order)) {
                    $markAsPaid->execute($order);
                }

                if ($confirm->isAllowed($order)) {
                    $confirm->execute($order);
                }
            }

            // We tag the order as "paid" only if it wasn't yet
            fn_change_order_status($orderId, OrderStatus::STANDBY_VENDOR);
        } elseif (false === $isTransactionSuccessful) {
            /**
             * Avancement du workflow
             */
            $orders = $this->orderService->getChildOrders($orderId);
            $markPaymentAsRefused = $this->get('marketplace.order.action.mark_payment_as_refused');
            foreach ($orders as $order) {
                if ($markPaymentAsRefused->isAllowed($order)) {
                    $markPaymentAsRefused->execute($order);
                }
            }

            fn_change_order_status($orderId, OrderStatus::BILLING_FAILED);
        }

        unset($mutex);

        return $this->postProcessResponse(
            $request,
            fn_order_placement_routines('route', $orderId, ['C' => false, 'A' => false, 'V' => false])
        );
    }

    /**
     * Est appelé par le client, quand il a fait le virement bancaire du montant indiqué aux coordonées indiquées,
     * il clique sur le bouton embarqué dans le HTML, qui le renvoit ici.
     * Ici on passe simplement la commande en attente de paiement.
     */
    public function markBankTransferIsDoneAction(Request $request)
    {
        $orderId = $request->query->getInt('order_id');

        if (!$orderId) {
            throw $this->createNotFoundException();
        }

        \define('PAYMENT_NOTIFICATION', true); // permet de bypasser des droits et de recup nimporte quelle commande
        try {
            $orderInfo = $this->orderService->getAnyOrder($orderId);
        } catch (OrderNotFound $exception) {
            throw $this->createNotFoundException();
        }

        if (false === $orderInfo->getPayment()->getType()->equals(PaymentType::BANK_TRANSFER())) {
            throw $this->createNotFoundException();
        }

        $orders = $this->orderService->getChildOrders($orderId);
        $confirm = $this->get('marketplace.order.action.confirm');
        foreach ($orders as $order) {
            if ($confirm->isAllowed($order)) {
                $confirm->execute($order);
            }

            $this->get('marketplace.order.action.redirect_to_payment_processor')->execute($order);
        }

        if ($order->getStatus()->equals(OrderStatus::INCOMPLETED())) {
            fn_change_order_status($orderId, LegacyOrderStatus::STANDBY_BILLING);
        }

        return $this->postProcessResponse(
            $request,
            fn_order_placement_routines('route', $orderId, ['C' => false, 'A' => false, 'V' => false])
        );
    }

    /** This controller handles client browser redirection from Stripe, and Stripe webhooks. */
    public function stripeCardAction(Request $request): Response
    {
        \define('PAYMENT_NOTIFICATION', true);

        $fromWebHook = false;

        // From browser redirection, we should have at least a query parameter [orderId] and POST [stripeToken]
        $orderId = $request->query->getInt('orderId');
        $token = $request->request->get('stripeToken');

        // Or a webhook object and event type from Stripe webhooks
        $webHookObject = $request->request->get('object');
        $webHookType = $request->request->get('type');
        $object = $request->request->get('data')['object'];

        if ($webHookObject === 'event' && $webHookType !== null) {
            $fromWebHook = true;
            if ('payment_intent.succeeded' === $webHookType) {
                $orderId = (int) $object['metadata']['orderId'] ?? 0;
                $token = $object['id'] ?? null;
            }
            if ('charge.succeeded' === $webHookType) {
                $orderId = (int) $object['metadata']['orderId'] ?? 0;
                $token = $object['payment_intent'] ?? null;
            }
            if ('payment_intent.payment_failed' === $webHookType) {
                $orderId = (int) $object['metadata']['orderId'] ?? 0;
                $token = $object['id'] ?? null;
                $this->eventDispatcher->dispatch(
                    new StripeFailedPaymentCallbackEvent(
                        new Order($orderId),
                        $token,
                        TransactionType::CREDITCARD()
                    )
                );
                return new Response('KO - dispatch failed status for this order', Response::HTTP_OK);
            }
        }

        if ($orderId === 0 || $token === null) {
            $error = 'Stripe: received callback without required parameters or unsupported webhook';

            $this->logger->warning($error, [
                'requestGet' => $request->query->all(),
                'requestPost' => $request->request->all(),
            ]);

            if ($fromWebHook === false) {
                return $this->redirectToRoute('home');
            } else {
                // We return responses with 2XX to prevent Stripe to disable the webhook after N failures.
                return new JsonResponse(['error' => $error], Response::HTTP_OK);
            }
        }

        if ('sepa_debit' === $object['payment_method_details']['type']) {
            $this->logger->info('Stripe CARD hook: called after sepa_debit payment : do nothing.');
            return new Response('KO - not a card payment charge', Response::HTTP_OK);
        }

        try {
            $mutex = $this->databaseMutex('stripe_card_' . $token);
        } catch (MutexException $e) {
            // Cannot lock, log it and try without mutex
            $this->logger->error($e->getMessage(), ['exception' => $e]);
        }

        $this->eventDispatcher->dispatch(
            new StripePaymentCallbackEvent(
                new Order($orderId),
                $token,
                TransactionType::CREDITCARD()
            )
        );

        // Process payment only if order is not already paid
        if (false === $this->orderService->isPaid($orderId)) {
            $stripe = $this->get('marketplace.payment.stripe');

            try {
                $stripe->finalizeCardTransaction($orderId, $token);
            } catch (\Exception $exception) {
                throw new StripeCardException('Unable to finalize the stripe transaction for the order ' . $orderId);
            }
        }

        unset($mutex);

        if ($fromWebHook === false) {
            return $this->postProcessResponse(
                $request,
                fn_order_placement_routines('route', $orderId, ['C' => false, 'A' => false, 'V' => false]),
                false
            );
        } else {
            return new Response('', Response::HTTP_NO_CONTENT);
        }
    }

    /**
     * Controlleur de callback du moyen de paiement HTML qui sert a récupérer l'IBAN et a faire signer le mandat
     * Cette action est appelée directement par l'utilisateur, ce n'est pas un controlleur de callback appelé par Stripe
     *
     * Permet d'initialiser le paiement la première fois, quand il va signer le mandat de prélèvement.
     * Lors des paiements futurs, il va passer en NoPaymentResponse via la méthode StripeSepaDirectProcessor::startPayment
     */
    public function stripeSepaInitAction(Request $request): Response
    {
        \define('PAYMENT_NOTIFICATION', true);

        $orderId = $request->query->getInt('orderId');

        if (!$orderId) {
            $this->logger->error('Stripe SEPA init: received payment callback without orderId', [
                'requestGet' => $request->query->all(),
                'requestPost' => $request->request->all(),
            ]);

            return $this->redirectToRoute('home');
        }

        $token = $request->request->get('stripeToken');

        if (!$token) {
            $this->logger->error('Stripe SEPA init: received payment callback without stripeToken', [
                'requestGet' => $request->query->all(),
                'requestPost' => $request->request->all(),
            ]);

            return $this->redirectToRoute('home');
        }

        $idempotencyKey = $request->request->get('idempotencyKey');

        $orders = $this->orderService->getChildOrders($orderId);

        $stripe = $this->get('marketplace.payment.stripe');

        $stripe->createCustomer($orders[0]->getUser(), $token)->id;

        $markPaymentDefermentAsAuthorized = $this->get('marketplace.order.action.mark_payment_deferment_as_authorized');
        $confirm = $this->get('marketplace.order.action.confirm');
        $commitTo = $this->container->get('marketplace.order.action.commit_to');
        $isOrderSepaDirect = false;

        foreach ($orders as $order) {
            if ($markPaymentDefermentAsAuthorized->isAllowed($order)) {
                $markPaymentDefermentAsAuthorized->execute($order);
            }

            if ($confirm->isAllowed($order)) {
                $confirm->execute($order);
            }

            if ($order->getPayment()->getProcessorName()->equals(PaymentProcessorName::STRIPE())
                && $order->getPayment()->getType()->equals(PaymentType::SEPA_DIRECT())
                && $commitTo->isAllowed($order)
            ) {
                $commitTo->execute(
                    $order,
                    new \DateTimeImmutable(StripeSepaDirectProcessor::DUE_DATE),
                    StripeSepaDirectProcessor::generateCommitmentNumber()
                );

                fn_change_order_status($order->getId(), (string) $order->deduceLegacyStatus(), (string) OrderStatus::INCOMPLETED());

                $isOrderSepaDirect = true;
            }
        }

        if (true === $isOrderSepaDirect) {
            try {
                $stripe->createSepaTransaction($orderId, $idempotencyKey);
            } catch (\Throwable $e) {
                $this->logger->error('[Stripe SEPA Direct] Exception in SEPA init callback: ' . $e->getMessage(), [
                    'exception' => $e,
                    'data' => [
                        'orderId' => $orderId,
                    ],
                ]);
            }
        }

        // une commande en paiement par échance ne peut pas échouer => on force la réponse en succès
        // car les fonctions legacy de CSCart vont considérer cette dernière comme étant en erreur car
        // elle n'a pas réellement été payée
        $response = fn_order_placement_routines('route', $orderId, ['C' => false, 'A' => false, 'V' => false]);
        $response->headers->add([
            'X-TransactionSuccessful' => 1,
            'X-OrderId' => $orderId,
        ]);

        return $this->postProcessResponse($request, $response, false);
    }

    /**
     * Controlleur de callback appelé par Stripe quand on a un OK ou KO sur l'opération SEPA
     * N'est pas appelé par un utilisateur mais par un serveur
     *
     * Stripe nous notifie de quand le prélèvement a été effectué afin que l'on puisse changer le statut de la commande
     *
     * Note: there is a lot of error handling returning HTTP 200, probably because otherwise Stripe
     * will retry the request, which is unnecessary for functional errors (i.e. same data -> same error).
     */
    public function stripeSepaAction(Request $request)
    {
        $hookType = $request->request->get('type');

        if ($hookType !== 'charge.failed' && $hookType !== 'charge.succeeded') {
            return new Response('Ignored', Response::HTTP_OK);
        }

        $data = $request->request->get('data');
        $token = $data['object']['id'];

        if (empty($token)) {
            return new Response('Wrong payload', Response::HTTP_BAD_REQUEST);
        }

        if ('card' === $data['object']['payment_method_details']['type']) {
            $this->logger->info('Stripe SEPA hook: called after card payment : do nothing.');
            return new Response('KO - not a sepa charge', Response::HTTP_OK);
        }

        $charge = $this->get('marketplace.payment.stripe')->getCharge($token);

        if (empty($charge->metadata['orderId'])) {
            $this->logger->error('Stripe SEPA hook: charge does not have orderId metadata.');

            return new Response('KO - no order ID', Response::HTTP_OK);
        }

        $orderId = (int) $charge->metadata['orderId'];

        try {
            $transaction = $this->transactionService->getOneBy([
                'processorName' => PaymentProcessorName::STRIPE(),
                'transactionReference' => $token,
            ]);
        } catch (TransactionNotFound $e) {
            $this->logger->error('Stripe SEPA Hook: Could not retrieve transaction', [
                'transaction_id' => $token,
                'exception' => $e,
            ]);

            return new Response('Transaction not found', Response::HTTP_OK);
        }

        // Ignore non SEPA orders ( CB, ... )
        $order = new Order($orderId);
        if (\array_key_exists('stripe_sepa', $transaction->getProcessorInformations()) === false) {
            return new Response('KO - not SEPA transaction', Response::HTTP_OK);
        }

        // Double check that the guessed order is the right order
        if ($transaction->getTransactionReference() !== $charge->id) {
            // It should *never* happen - until it does.
            $this->logger->error('Stripe SEPA hook: Guessed order is not the right order. Hook was ignored.');

            return new Response('KO - Order mismatch', Response::HTTP_OK);
        }

        $orders = $this->orderService->getChildOrders($orderId);

        \define('PAYMENT_NOTIFICATION', true);

        $this->eventDispatcher->dispatch(
            new StripePaymentCallbackEvent(
                new Order($orderId),
                $data['object']['id'],
                TransactionType::DIRECT_DEBIT()
            )
        );

        $markAsPaid = $this->get('marketplace.order.action.mark_as_paid');
        $markAsRefused = $this->get('marketplace.order.action.mark_payment_as_refused');

        //if charge failed update payment info
        if ($hookType === 'charge.failed') {
            if (true === $order->getPaymentType()->equals(PaymentType::PAYMENT_DEFERMENT())) {
                $this->tracer->trace($order->getId(), 'MarkPaymentAsRefused');

                //make transaction status to Failed
                $transaction->setStatus(TransactionStatus::FAILED());
                $this->transactionService->save($transaction);
                $this->logger->error('Transaction:', ['transaction' => (array) $transaction]);
            } else {
                foreach ($orders as $order) {
                    if ($markAsRefused->isAllowed($order)) {
                        $markAsRefused->execute($order);
                    }
                }

                fn_change_order_status($orderId, OrderStatus::BILLING_FAILED);
            }

            return new Response('OK', Response::HTTP_OK);
        }
        // Avancement du workflow
        if ($hookType === 'charge.succeeded' && $charge->paid && $charge->captured) {
            // Payment: OK
            $allowed = false;
            foreach ($orders as $order) {
                if ($markAsPaid->isAllowed($order)) {
                    $markAsPaid->execute($order);
                    $allowed = true;
                }
            }

            $allowedWorkflows = [
                WorkflowName::PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WAIT_PAYMENT_DEFERMENT_WITHDRAWAL_PERIOD_FUNDS_DISPATCH()->getValue(),
                WorkflowName::PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_WAIT_PAYMENT_DEFERMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD_FUNDS_DISPATCH()->getValue(),
                WorkflowName::PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_WAIT_PAYMENT_DEFERMENT_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD_FUNDS_DISPATCH()->getValue(),
                WorkflowName::PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_VENDOR_VALIDATION_ORDER_PREPARATION_WAIT_PAYMENT_DEFERMENT_WITHDRAWAL_PERIOD_FUNDS_DISPATCH()->getValue(),
            ];
            $isDefermentPayment = \in_array($order->getWorkflowName()->getValue(), $allowedWorkflows, true);

            if ($isDefermentPayment === true && $allowed === true) {
                $forceNotification = ['C' => false, 'A' => false, 'V' => false];
                fn_change_order_status($orderId, OrderStatus::STANDBY_VENDOR, '', $forceNotification);
            }
        } elseif ($hookType === 'charge.succeeded' && ($charge->paid === false || $charge->captured === false)) {
            // Payment: KO
            foreach ($orders as $order) {
                if ($markAsRefused->isAllowed($order)) {
                    $markAsRefused->execute($order);
                }
            }

            fn_change_order_status($orderId, OrderStatus::BILLING_FAILED);
        }

        return new Response('OK', Response::HTTP_OK);
    }

    public function setEventDispatcher(EventDispatcherInterface $eventDispatcher): self
    {
        $this->eventDispatcher = $eventDispatcher;

        return $this;
    }

    /**
     * @param bool $haveServerCallback If true and the request method is POST: we don't return a pretty response but just a blank page because the caller is a hook
     *                                 You might want to disable this behavior if the client callback is a POST request (Stripe)
     */
    private function postProcessResponse(Request $originalRequest, Response $response, bool $haveServerCallback = true): Response
    {
        // If POST, this is a server callback. We just return a simple 200
        if ($haveServerCallback && $originalRequest->isMethod('POST')) {
            return new Response('OK', 200);
        }

        // Redirect API customers to the URL of their original frontend
        if ($redirectUrl = $originalRequest->query->get('payment_redirect_url')) {
            $redirectUrl = base64_decode($redirectUrl);
            if ($redirectUrl && filter_var($redirectUrl, FILTER_VALIDATE_URL)) {
                $transactionSuccessful = $response->headers->get('X-TransactionSuccessful');

                // add success=1 or success=0 at the end of the url
                $params = [
                    'success' => (int) $transactionSuccessful,
                ];

                if ($originalRequest->query->has('renew_credit_card')) {
                    $params['renew_credit_card'] = 1;
                }

                $redirectUrl .= (\parse_url($redirectUrl, PHP_URL_QUERY) ? '&' : '?') . \http_build_query($params);

                if ($transactionSuccessful && $order_id = $response->headers->get('X-OrderId')) {
                    // Get sub orders to give all ids rather than parent id (parent order id is an internal concept)
                    $child = db_get_fields("SELECT order_id FROM cscart_orders WHERE parent_order_id = ?i", $order_id);
                    if (!empty($child)) {
                        $redirectUrl .= '&orderIds=' . implode(',', $child);
                    } else {
                        $redirectUrl .= '&orderIds=' . $order_id;
                    }
                }

                return new RedirectResponse($redirectUrl);
            }
        }

        // Don't expose internal headers
        if ($response->headers->has('X-TransactionSuccessful')) {
            $response->headers->remove('X-TransactionSuccessful');
        }
        if ($response->headers->has('X-OrderId')) {
            $response->headers->remove('X-OrderId');
        }

        return $response;
    }

    protected function databaseMutex(string $token, int $iterations = 5): BlockingMutex
    {
        $mutex = null;

        for ($i = 0; $i < $iterations; ++$i) {
            try {
                // Lock a mutex to prevent race conditions between PSP payment POST call and client GET call
                $mutex = $this->mutexService->createBlockingMutex(
                    'payment_notification',
                    $token
                );
                break;
            } catch (MutexException $e) {
                if ($i === $iterations - 1) {
                    throw $e;
                }
            }
        }

        if ($mutex === null) {
            throw new MutexException('Could not lock the database');
        }

        return $mutex;
    }

    protected function isRenewCreditCardRequest(Request $request): bool
    {
        if ($request->getMethod() === 'GET'
            && $request->query->has('renew_credit_card') === true
            && $request->query->get('renew_credit_card') === "1"
        ) {
            return true;
        }

        $customData = [];

        if ($request->request->has('custom_data') === true) {
            $customData = $request->request->get('custom_data');
        }

        if ($request->query->has('custom_data') === true) {
            $customData = $request->query->get('custom_data');
        }

        if (\is_array($customData) === false && \strlen($customData) > 0) {
            $customData = \json_decode($customData, true);
        }

        if (\count($customData) > 0
            && \array_key_exists('renewCreditCard', $customData) === true
            && $customData['renewCreditCard'] === "1"
        ) {
            return true;
        }

        return false;
    }
}
