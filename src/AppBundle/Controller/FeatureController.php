<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Wizacha\Marketplace\GlobalState\GlobalState;

class FeatureController extends BaseController
{
    public function formAction(Request $request)
    {
        $session = $this->container->get('session');
        $product_form = $session->get('create_product_form', []);
        $current_values = \array_key_exists('features', $product_form) ? $product_form['features'] : [];

        if ($request->get('product_id')) {
            $product_data = fn_get_product_data($request->get('product_id'), $_SESSION['auth'], (string) GlobalState::interfaceLocale(), '', true, true, true, false, false, true, false, true);
            $fv = $product_data['product_features'] ?? [];
            $fill_current_values = function ($feature) use (&$current_values, &$fill_current_values) {
                switch ($feature['feature_type']) {
                    case 'E':
                        $current_values['add_new_variant'][$feature['feature_id']]['variant'] = $feature['variants'][$feature['variant_id']]['variant'];
                        break;
                    case 'S':
                    case 'N':
                        $current_values['product_features'][$feature['feature_id']] = $feature['variant_id'];
                        break;
                    case 'M':
                        $current_values['product_features'][$feature['feature_id']] = array_combine(
                            array_keys($feature['variants']),
                            array_fill(0, \count($feature['variants']), true)
                        );
                        break;
                    case 'O':
                    case 'T':
                        $current_values['product_features'][$feature['feature_id']] = $feature['value'];
                        break;
                    case 'G':
                        array_map($fill_current_values, (array) $feature['subfeatures']);
                        break;
                }
            };
            array_map($fill_current_values, $fv);
        }

        if (!empty($request->get('category_id'))) {
            $category_id = filter_var($request->get('category_id'), FILTER_SANITIZE_NUMBER_INT);
            $features = $this->container->get('marketplace.pim.attribute_service')->getAttributesFromCategory($category_id, true);

            $product_features = $this->setAllVariantsAsJSTable($features);

            $this->smarty()->assign('product_features_currents', $current_values, true);
            $this->smarty()->assign('product_features', $product_features, true);
            $this->smarty()->assign('input_name_prefix', $request->get('input_name_prefix', '', true));

            return new Response(
                $this->smarty()->fetch('views/features/form.tpl', uniqid())
            );
        }

        return new Response('');
    }

    /**
     * @param array $product_features
     * @return array
     */
    public function setAllVariantsAsJSTable($product_features = [])
    {
        foreach ($product_features as &$f) {
            $f['variants_js_table'] = json_encode(
                empty($f['variants']) ? [] : array_column($f['variants'], 'variant'),
                JSON_HEX_APOS + JSON_HEX_QUOT + JSON_HEX_AMP
            );
            // Groups
            if (isset($f['subfeatures'])) {
                $f['subfeatures'] = $this->setAllVariantsAsJSTable($f['subfeatures']);
            }
        }
        return $product_features;
    }
}
