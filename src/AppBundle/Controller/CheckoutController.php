<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Wizacha\AgeVerification;
use Wizacha\Marketplace\Address\Address;
use Wizacha\Marketplace\Basket\Exception\CannotCheckoutEmptyBasket;
use Wizacha\Marketplace\Basket\Exception\CustomerIsTooYoung;
use Wizacha\Marketplace\Basket\Exception\MissingPaymentException;
use Wizacha\Marketplace\Basket\Exception\ProductNotBelongsToUserDivision;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Payment\Exception\SepaMandateNotCreatedException;
use Wizacha\Marketplace\Payment\Exception\SepaMandateNotSignedException;
use Wizacha\Marketplace\Payment\Response\HtmlPaymentResponse;
use Wizacha\Marketplace\Payment\Response\NoPaymentResponse;
use Wizacha\Marketplace\Payment\Response\RedirectPaymentResponse;
use Wizacha\Marketplace\Payment\Stripe\BankAccountIsMissing;
use Wizacha\Marketplace\ReadModel\Basket;
use Wizacha\Marketplace\User\Exception\EmailAlreadyUsed;
use Wizacha\Marketplace\User\Exception\InvalidProfileFields;

class CheckoutController extends BaseController
{
    protected OrderService $orderService;

    public function __construct(OrderService $orderService)
    {
        $this->orderService = $orderService;
    }

    /**
     * Step 1: the user must login.
     */
    public function loginAction(): Response
    {
        // Redirection sur le checkout legacy si le nouveau checkout n'est pas activé
        if (!$this->getParameter('use_new_checkout')) {
            return $this->redirect(fn_url('checkout.checkout'));
        }

        // Récupération du panier avant de vérifier la connexion de l'utilisateur...
        $basket = $this->getBasket();
        if (!$basket) {
            return $this->redirectToRoute('basket_view');
        }

        // ...de pouvoir générer une adresse de redirection
        $redirectRoute = $basket->isPickupPointsShipping() ? 'checkout_pickup_points' : 'checkout_addresses';

        $user = $this->getUser();
        if ($user) {
            // Already logged in: skip to next step
            return $this->redirectToRoute($redirectRoute);
        }

        // Redirect to the legacy checkout if it's not implemented in the current theme
        if (!$this->container->get('templating')->exists('@App/frontend/checkout/login.html.twig')) {
            return $this->redirect('/checkout');
        }

        return $this->render('@App/frontend/checkout/login.html.twig', [
            'basket' => $basket,
            'redirectUrl' => $redirectRoute,
        ]);
    }

    /**
     * Step 2: the user must fill their addresses.
     */
    public function addressesAction(): Response
    {
        // Redirection sur le checkout legacy si le nouveau checkout n'est pas activé
        if (!$this->getParameter('use_new_checkout')) {
            return $this->redirect(fn_url('checkout.checkout'));
        }

        $user = $this->getUser();
        if (!$user) {
            return $this->redirectToRoute('checkout_login');
        }
        $basket = $this->getBasket();
        if (!$basket) {
            return $this->redirectToRoute('basket_view');
        }
        $cart = $basket->getCscartData($user->getId());

        $profileFields = fn_get_profile_fields('O');
        $profileData = fn_get_user_info($user->getId(), true, $cart['profile_id']);

        return $this->render('@App/frontend/checkout/addresses.html.twig', [
            'basket' => $basket,
            'profileFields' => $profileFields,
            'profileData' => $profileData, // Todo: remove this field
            'profile' => $profileData,
            'shipToAnother' => fn_check_shipping_billing($profileData, $profileFields),
            'countries' => fn_get_simple_countries(true, (string) GlobalState::interfaceLocale()),
        ]);
    }

    /**
     * Step 2': the user must select a pickup point.
     */
    public function pickupPointsAction(): Response
    {
        // Redirection sur le checkout legacy si le nouveau checkout n'est pas activé
        if (!$this->getParameter('use_new_checkout')) {
            return $this->redirect(fn_url('checkout.checkout'));
        }

        // Récupération du panier avant de vérifier la connexion de l'utilisateur...
        $basket = $this->getBasket();
        if (!$basket) {
            return $this->redirectToRoute('basket_view');
        }

        // ...afin de pouvoir mettre à jours les modes de livraison automatiquement.
        $this->container->get('marketplace.basket.domain_service')->selectShippingForAllGroups(
            $basket->getId(),
            $basket->getPickupPointsShippingId()
        );

        // Il faut recharger le panier suite à la modification
        $basket = $this->getBasket();
        if (!$basket) {
            return $this->redirectToRoute('basket_view');
        }

        $user = $this->getUser();
        if (!$user) {
            return $this->redirectToRoute('checkout_login');
        }

        $cart = $basket->getCscartData($user->getId());

        $profileData = fn_get_user_info($user->getId(), true, $cart['profile_id']);

        return $this->render('@App/frontend/checkout/pickup-points.html.twig', [
            'basket' => $basket,
            'profile' => $profileData,
            'totalWeightKg' => $cart['total_weight'],
        ]);
    }

    /**
     * Step 3: the user must select a payment method.
     */
    public function paymentAction(): Response
    {
        // Redirection sur le checkout legacy si le nouveau checkout n'est pas activé
        if (!$this->getParameter('use_new_checkout')) {
            return $this->redirect(fn_url('checkout.checkout'));
        }

        $user = $this->getUser();
        if (!$user) {
            return $this->redirectToRoute('checkout_login');
        }
        $basket = $this->getBasket();
        if (!$basket) {
            return $this->redirectToRoute('basket_view');
        }
        $cart = $basket->getCscartData($user->getId());

        if (!AgeVerification::check($cart['user_data'], $cart['age_verification'])) {
            fn_set_notification('E', __('error'), __('w_age_verification_too_young', [
                '[age]' => $cart['age_verification'],
            ]));
            return $this->redirectToRoute('basket_view');
        }

        $profileFields = fn_get_profile_fields('O');

        // Type d'adresse à vérifier
        $sections = ['E', 'C', 'B'];

        // Si on est pas en mode de livraison relais colis, on ajoute la vérification de l'adresse de "Shipping".
        // Car sinon, il est normal que l'adresse de shipping ne soit pas vérifiée : elle est fake, elle
        // correspond à celle du relais colis et on se fiche de la validité de l'adresse de livraison réelle
        // de l'utilisateur.
        if (!$basket->isPickupPointsShipping()) {
            $sections[] = 'S';
        }

        foreach ($sections as $section) {
            if (!fn_check_profile_fields_population($cart['user_data'], $section, $profileFields)) {
                fn_set_notification('W', __('notice'), __('text_fill_the_mandatory_fields'));

                return $this->redirectToRoute('checkout_addresses');
            }
        }

        if (\floatval($cart['total']) == 0 || !isset($cart['payment_id'])) {
            $cart['payment_id'] = 0;
        }

        $companyTerms = [];
        foreach ($basket->getReadModel()->getGroups() as $companyGroup) {
            if (empty(trim(strip_tags($companyGroup->getCompanyTerms())))) {
                continue;
            }

            $companyTerms[] = [
                'link' => $this->generateUrl('company_terms', ['company' => $companyGroup->getCompanySlug()]),
                'name' => $companyGroup->getCompanyName(),
            ];
        }

        $auth = $this->get('session')->get('auth');

        $paymentMethods = fn_prepare_checkout_payment_methods($cart, $auth, null, $basket->hasAdjustableProducts());

        if (empty($paymentMethods)) {
            fn_set_notification('W', __('notice'), __('cannot_proccess_checkout_without_payment_methods'));

            return $this->redirectToRoute('basket_view');
        }

        return $this->render('@App/frontend/checkout/payment.html.twig', [
            'basket' => $basket,
            'profileData' => $cart['user_data'],
            'paymentMethods' => $paymentMethods,
            'paymentNeeded' => $cart['payment_id'] === 0,
            'companyTerms' => $companyTerms,
        ]);
    }

    /**
     * Handle data from step 2
     */
    public function updateAddressesAction(Request $request): Response
    {
        // Récupération de l'utilisateur
        $user = $this->getUser();
        if (!$user) {
            return $this->redirectToRoute('checkout_login');
        }

        // Récupération du basket
        $basket = $this->getBasket();
        if (!$basket) {
            return $this->redirectToRoute('basket_view');
        }
        $cart = $basket->getCscartData($user->getId());

        // Récupération des informations du form
        $userData = $request->request->get('user_data');
        $sameBillingAndShipping = !$request->request->get('ship_to_another', false);

        // Décision de la notification
        $notification = isset($userData['email']) && $userData['email'] != $user->getEmail();

        try {
            $this->get('marketplace.user.user_service')->updateUserProfile(
                $user->getId(),
                $userData,
                $sameBillingAndShipping,
                $notification
            );
        } catch (EmailAlreadyUsed $e) {
            fn_set_notification('E', __('error'), __('error_user_exists'));

            return $this->redirectToRoute('checkout_addresses');
        }

        return $this->redirectToRoute('checkout_payment');
    }

    /**
     * Handle data from step 2' : pickup points
     */
    public function updatePickupPointsAction(Request $request): Response
    {
        // Récupération de l'utilisateur
        $user = $this->getUser();
        if (!$user) {
            return $this->redirectToRoute('checkout_login');
        }

        $user = $this->get('marketplace.user.user_repository')->get($user->getId());

        // Récupération du basket
        $basket = $this->getBasket();
        if (!$basket) {
            return $this->redirectToRoute('basket_view');
        }


        // Il est obligatoire de sélectionner un point relais
        if (empty($idChronoRelais = $request->request->get('chrono_point_relais', ''))) {
            fn_set_notification('E', __('error'), __('chronopost_point_relais_mandatory'));

            return $this->redirectToRoute('checkout_pickup_points');
        }

        // Récupération des informations du point relais via l'API de Chronopost
        $relais = $this->get('marketplace.chronopost.client')->getPointRelais(
            $request->request->get('chrono_point_relais'),
            'FR' // Paramètre en dur, que l'on rendra dynamique si nécessaire et lorsque cela sera spécifié.
        );

        // Récupération des informations du form
        $userData = $request->request->get('user_data');

        // Création de l'adresse de livraison en fonction de l'adresse du relais colis.
        // Concernant le nom et le prénom, on utilise ceux de l'adresse de facturation
        // qui est la seule que le client vient de valider (sur ce même écran).
        $shippingAddress = new Address(
            $userData['b_title'],
            $userData['b_firstname'],
            $userData['b_lastname'],
            $relais->nom,
            $relais->adresse1,
            $relais->adresse2,
            $relais->codePostal,
            $relais->localite,
            '',
            $relais->codePays
        );
        $this->get('marketplace.basket.domain_service')->setShippingAddress($user->getBasketId(), $shippingAddress);

        // Décision de la notification
        $notification = isset($userData['email']) && $userData['email'] != $user->getEmail();

        // Comme ce mode gère une adresse de livraison "temporaire",
        // on ne s'occupe pas de l'adresse de livraison du client.
        // SAUF SI c'est la premiere commande du mec et qu'il n'a pas d'adresse en DB => on la save en billing / shipping pour fluidifier le tunel d'achat
        $sameBillingAndShipping = empty($user->getShippingAddress()->getFieldValue('address'));

        // Sauvegarde des informations de l'utilisateur
        try {
            $this->get('marketplace.user.user_service')->updateUserProfile(
                $user->getUserId(),
                $userData,
                $sameBillingAndShipping,
                $notification
            );
        } catch (EmailAlreadyUsed $e) {
            fn_set_notification('E', __('error'), __('error_user_exists'));

            return $this->redirectToRoute('checkout_pickup_points');
        }

        return $this->redirectToRoute('checkout_payment');
    }

    /**
     * Handle data from step 3 (payment)
     * This route is used even if use_new_checkout is false
     */
    public function updateSelectedPaymentAction(Request $request): Response
    {
        $user = $this->getUser();
        if (!$user) {
            return $this->redirectToRoute('checkout_login');
        }
        $basket = $this->getBasket();
        $basketService = $this->get('marketplace.basket.domain_service');
        if (!$basket || !$basketService->checkIntegrity($basket->getId())) {
            return $this->redirectToRoute('basket_view');
        }

        $cart = $basket->getCscartData($user->getId());
        // Prevent unauthorized access
        if (empty($cart['user_data']['email'])) {
            return $this->redirectToRoute('checkout_login');
        }

        $mustAcceptTerms = $this->getParameter('feature.checkout.accept_terms_and_conditions');
        if ($mustAcceptTerms && !$request->request->get('accept_terms', false)) {
            fn_set_notification('E', __('error'), __('checkout_terms_n_conditions_alert'));
            return $this->redirectToRoute('checkout_payment');
        }

        // Payment is not mandatory if the basket has a total of 0
        $paymentId = $request->request->get('payment_id', 0);

        $checkout = $this->get('marketplace.basket.checkout');

        try {
            $cssUrl = null;
            if (!\is_null($this->getParameter('hipay.css'))) {
                $cssUrl = fn_url("", AREA, "https") . $this->getParameter('hipay.css');
            }

            $result = $checkout->checkout($basket, $paymentId, $user->getId(), null, $cssUrl);
        } catch (MissingPaymentException $e) {
            fn_set_notification('E', __('error'), __('missing_payment'));

            return $this->redirectToRoute('checkout_payment');
        } catch (CannotCheckoutEmptyBasket $e) {
            fn_set_notification('W', __('cart_is_empty'), __('cannot_proccess_checkout'));

            return $this->redirectToRoute('basket_view');
        } catch (CustomerIsTooYoung $e) {
            fn_set_notification('E', __('error'), __('w_age_verification_too_young', [
                '[age]' => $e->getRequiredAge(),
            ]));

            return $this->redirectToRoute('basket_view');
        } catch (InvalidProfileFields $e) {
            fn_set_notification('W', __('notice'), __('text_fill_the_mandatory_fields'));

            return $this->redirectToRoute('checkout_addresses');
        } catch (BankAccountIsMissing | SepaMandateNotCreatedException | SepaMandateNotSignedException $exception) {
            fn_set_notification('E', __('error'), $exception->getMessage());

            return $this->redirectToRoute('checkout_addresses');
        } catch (ProductNotBelongsToUserDivision $e) {
            fn_set_notification('E', __('error'), __('division_product_unavailable'));

            return $this->redirectToRoute('checkout_payment');
        }

        $paymentResponse = $result->getPaymentResponse();
        switch (true) {
            case $paymentResponse instanceof NoPaymentResponse:
                // No payment: the order is placed in standby billing status

                // Service does not detach basket with no payment response, because it's done in fn_order_placement_routines
                // We are in a rare case which does not use fn_order_placement_routines. We do the clean here, by hand
                $this->get('session')->set('basketId', null);
                $this->getUser()->setBasketId('');

                // Redirect to order complete
                $orderId = $result->getParentOrderId();

                return $this->redirectToRoute('checkout_complete', ['orderId' => $orderId]);
            case $paymentResponse instanceof RedirectPaymentResponse:
                return $this->redirect($paymentResponse->getRedirectUrl());
            case $paymentResponse instanceof HtmlPaymentResponse:
                return $this->render('@App/frontend/checkout/payment_html_content.html.twig', [
                    'html' => $paymentResponse->getHtml(),
                ]);
            default:
                throw new \Exception('Unrecognized payment response');
        }
    }

    public function completeAction(int $orderId): Response
    {
        if ($orderId <= 0) {
            throw new BadRequestHttpException('orderId must be strictly positive');
        }

        $session = $this->container->get('session');

        $user = $this->getUser();
        $isAllowed = false;
        if (!$user) {
            $auth = $session->get('auth');
            if (empty($auth['order_ids'])) {
                return $this->redirectToRoute('checkout_login');
            } else {
                $isAllowed = \in_array($orderId, $auth['order_ids']);
            }
        } else {
            $isAllowed = db_get_field("SELECT EXISTS (SELECT user_id FROM ?:orders WHERE user_id = ?i AND order_id = ?i)", $user->getId(), $orderId);
        }

        if (!$isAllowed) {
            return $this->redirectToRoute('checkout_login');
        }

        $orderInfo = $this->orderService->overrideLegacyOrder($orderId);

        if (!empty($orderInfo['is_parent_order']) && $orderInfo['is_parent_order'] == 'Y') {
            $orderInfo['child_ids'] = implode(',', db_get_fields("SELECT order_id FROM ?:orders WHERE parent_order_id = ?i", $orderId));
        }

        fn_add_breadcrumb(__('landing_header'));

        //Remove 'NOTICE' notifications
        $notifications = $session->get('notifications');
        if (\is_array($notifications)) {
            $session->set('notifications', array_filter($notifications, function ($n) {
                return $n['type'] != 'N';
            }));
        } else {
            $session->set('notifications', []);
        }

        return $this->render('@App/frontend/checkout/complete.html.twig', [
            'order_info' => $orderInfo,
        ]);
    }

    /**
     * Generates the Google Analytics script to push transaction data.
     * No route leads here, this is made to be rendered inside another Twig template.
     */
    public function orderCompleteAnalyticsAction(array $orderInfo): Response
    {
        if (!$this->container->getParameter('feature.enable_ecommerce_analytics')) {
            return new Response();
        }

        try {
            $orderId = \intval($orderInfo['order_id']);

            if (!empty($orderInfo['is_parent_order']) && $orderInfo['is_parent_order'] == 'Y') {
                $orders = $this->orderService->getOrdersFromParentOrder($orderId);
            } else {
                $orders = [$this->orderService->getOrder($orderId)];
            }

            $transactions = [];
            $items = [];

            $productService = $this->container->get('marketplace.product.productservice');

            foreach ($orders as $order) {
                $transactions[] = [
                    'id' => $order->getId(),
                    'affiliation' => fn_get_company_name($order->getCompanyId()),
                    'revenue' => $order->getTotal()->getConvertedAmount(),
                    'shipping' => $order->getShippingCost()->getConvertedAmount(),
                    'currency' => $this->getParameter('currency.code'),
                ];

                $orderItems = $order->getItems();
                foreach ($orderItems as $orderItem) {
                    $item = [
                        'id' => $order->getId(),
                        'name' => $orderItem->getProductName(),
                        'sku' => $orderItem->getProductCode(),
                        'price' => $orderItem->getPrice()->getConvertedAmount(),
                        'quantity' => $orderItem->getAmount(),
                        'currency' => $this->getParameter('currency.code'),
                    ];

                    $product = $productService->getProductByDeclinationId($orderItem->getDeclinationId());
                    if ($product) {
                        $category = end($product->getCategoryPath());
                        if ($category) {
                            $item['category'] = $category->getName();
                        }
                    }

                    $items[] = $item;
                }
            }

            return $this->render('@App/frontend/checkout/complete-analytics.html.twig', [
                'transactions' => $transactions,
                'items' => $items,
            ]);
        } catch (\Exception $e) {
            // this is a non-critical part of the website, we don't want to display an error to the customer for this
            $this->container->get('logger')->error('failed to generate Google Analytics Ecommerce data', ['exception' => $e]);

            return new Response();
        }
    }

    private function getBasket(): ?Basket
    {
        $basketId = $this->get('session')->get('basketId');
        return $this->get('marketplace.basket.repository.read_model')->find($basketId);
    }
}
