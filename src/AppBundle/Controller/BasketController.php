<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller;

use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Form;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Wizacha\Marketplace\Basket\Exception\CannotAddNonTransactionalProduct;
use Wizacha\Bridge\Broadway\Exceptions\InvalidCommandException;
use Wizacha\Marketplace\Entities\Declination;
use Wizacha\Marketplace\ReadModel\Basket;
use Wizacha\Marketplace\Basket\BasketProjector;
use Wizacha\Marketplace\Basket\Exception\BasketNotFound;

class BasketController extends BaseController
{
    /**
     * @return Response
     */
    public function compactAction()
    {
        return $this->render('@App/frontend/views/basket/compact.html.twig', [
            'basket' => $this->getBasketData(),
        ]);
    }

    /**
     * @return Response
     */

    public function viewAction()
    {
        return $this->render('@App/frontend/views/basket/view.html.twig', [
            'basket' => $this->getBasketData(),
        ]);
    }

    /**
     * Add a product to the basket.
     */
    public function addProductAction(Request $request)
    {
        $quantity = $request->request->getInt('quantity');
        $redirectUrl = $request->request->get('redirect_url', $this->generateUrl('home'));
        $productId = $request->request->get('product_id');

        if (empty($productId)) {
            throw $this->createNotFoundException();
        }

        try {
            $basketId = $this->getOrCreateBasketId();
            $declination = Declination::fromId($productId);
            $addedQuantity = $this->container->get('marketplace.basket.domain_service')->addProductToBasket(
                $basketId,
                $declination,
                $quantity
            );
        } catch (BasketNotFound $e) {
            fn_set_notification('E', __('error'), $e->getMessage());

            return $this->redirect($redirectUrl);
        } catch (CannotAddNonTransactionalProduct $e) {
            fn_set_notification('E', __('error'), __('cannot_buy_non_transactional_product'));

            return $this->redirect($redirectUrl);
        }

        if ($addedQuantity == 0) {
            fn_set_notification('W', __('error'), __('out_of_stock_product_in_cart'));

            return $this->redirect($this->generateUrl('basket_view'));
        }

        $warning = null;
        if ($addedQuantity < $quantity) {
            $warning = __('w_text_cart_amount_corrected');
        }

        $declinationData = BasketProjector::getDeclinationData($declination->getId());
        $title = __($quantity > 1 ? 'products_added_to_cart' : 'product_added_to_cart');
        $modal = $this->renderView('@App/frontend/views/basket/commons/add_product_notification.html.twig', [
            'title' => $title,
            'product' => [
                'quantity' => $addedQuantity,
                'declination' => $declinationData,
                'subtotal' => $addedQuantity * $declinationData['reduced_price'],
            ],
            'warning' => $warning,
        ]);
        fn_set_notification('I', $title, $modal);

        return $this->redirect($redirectUrl);
    }

    public function selectShippingAction(Request $request)
    {
        $basketId = $this->container->get('session')->get('basketId');
        try {
            $this->container->get('marketplace.basket.domain_service')->selectShippingForGroup(
                $basketId,
                $request->request->get('group_id'),
                $request->request->get('shipping_id')
            );
        } catch (InvalidCommandException $e) {
            fn_set_notification('E', __('error'), $e->getMessage());
        }
        $basketRepository = $this->container->get('marketplace.basket.repository.read_model');
        /** @var Basket $basket */
        $basket = $basketRepository->find($basketId);
        $this->smarty()->assign('basket', $basket ? $basket->getData() : null, true);
        return new Response($this->smarty()->fetch('views/basket/commons/full_view.tpl'));
    }

    public function shippingsSummaryAction()
    {
        return $this->render('@App/frontend/views/basket/shippings_summary.html.twig', [
            'basket' => $this->getBasketData(),
        ]);
    }

    public function modifyProductQuantityAction(Request $request)
    {
        $basketId = $this->getOrCreateBasketId();
        try {
            $declination = Declination::fromId($request->request->get('product_id'));
            $quantity = $request->request->get('quantity');

            $this->container->get('marketplace.basket.domain_service')->modifyProductQuantityInBasket(
                $basketId,
                $declination,
                $quantity
            );
        } catch (InvalidCommandException $e) {
            fn_set_notification('E', 'Erreur', $e->getMessage());
        }
        $basketRepository = $this->container->get('marketplace.basket.repository.read_model');
        /** @var Basket $basket */
        $basket = $basketRepository->find($basketId);
        $this->smarty()->assign('basket', $basket->getData(), true);
        return new Response(
            $this->smarty()->fetch('views/basket/commons/full_view.tpl')
        );
    }

    private function getOrCreateBasketId()
    {
        $session = $this->container->get('session');
        $basketId = $session->get('basketId');

        if (!$basketId) {
            $basketId = $this->container->get('marketplace.basket.domain_service')->generateNewBasket();
            $session->set('basketId', $basketId);
        }
        return $basketId;
    }

    public function addCartPromotionAction(Request $request)
    {
        $formFactory = $this->container->get('form.factory');

        /** @var Form $form */
        $form = $formFactory->createNamedBuilder(null)
            ->setAction($this->generateUrl('basket_add_promotion'))
            ->add('coupon_code', TextType::class, ['attr' => ['placeholder' => 'promo_code']])
            ->add(
                'ok',
                SubmitType::class,
                ['attr' => ['value' => 'sign_in']]
            )
            ->getForm();
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $session = $this->container->get('session');
            $basketId = $session->get('basketId');
            if ($basketId) {
                $coupon = $form->getData()['coupon_code'];
                $basketService = $this->container->get('marketplace.basket.domain_service');

                if (!$basketService->addCoupon($basketId, $coupon, $this->getUser())) {
                    fn_set_notification('E', __('error'), __('coupon_doesnt_exists'));
                }

                return $this->redirect($this->generateUrl('basket_view'));
            }
        }

        $basket =  $request->attributes->get('basket');
        if (!$basket) {
            // Retrocompatibility with Smarty inclusion
            $basket = $this->smarty()->getTemplateVars('basket');
        }

        return $this->render('@App/frontend/views/basket/coupon_code.html.twig', [
            'form_coupon_code' => $form->createView(),
            'basket' => $basket,
        ]);
    }

    public function removeCartPromotionAction(Request $request)
    {
        $coupon = $request->get('coupon');
        $basketId = $request->hasSession() ? $request->getSession()->get('basketId') : null;

        if ($coupon !== null && $basketId !== null) {
            $this->container->get('marketplace.basket.domain_service')->removeCoupon($basketId, $coupon);
        }

        return $this->redirectToRoute('basket_view');
    }

    /**
     * Returns the basket data for the current user.
     *
     * @param bool $useNewModel
     * @return array|null
     */
    private function getBasketData(bool $useNewModel = false)
    {
        $userService = $this->container->get('marketplace.user_service');
        $basket = $userService->getUserBasket($useNewModel);

        return $basket ? $basket->getData() : null;
    }
}
