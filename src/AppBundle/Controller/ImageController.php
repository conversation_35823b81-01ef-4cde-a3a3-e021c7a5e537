<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class ImageController extends BaseController
{
    /**
     * @deprecated Use Twig helpers.
     */
    public function displayAction(Request $request)
    {
        $cache_id_keys = [
            $request->get('image_id', 0),
            $request->get('size_x', 0),
            $request->get('size_y', 0),
        ];
        $cache_id = implode('_', $cache_id_keys);
        if (!$this->smarty()->isCached('views/images/display.tpl', $cache_id)) {
            $image_path = fn_generate_thumbnail(
                fn_get_image($request->get('image_id', 0), 'detailed')['relative_path'],
                $request->get('size_x', 0),
                $request->get('size_y', 0),
                false
            );
            $this->smarty()->assign('image_url', $image_path);
        }

        return new Response(
            $this->smarty()->fetch('views/images/display.tpl', $cache_id)
        );
    }

    /**
     * Calculate image real URL using \Wizacha\ImageManager\GetFullPath as parameter 'path' in Request
     *
     * @deprecated Use Twig helpers.
     */
    public function retrieveUrlAction(Request $request)
    {
        if ($request->get('image_id')) {
            $path = fn_get_image($request->get('image_id', 0), 'detailed')['relative_path'];
        } else {
            $path = $request->attributes->get('path');
        }
        return new Response(
            container()->get("Wizacha\Storage\ImagesStorageService")->getUrl($path)
        );
    }
}
