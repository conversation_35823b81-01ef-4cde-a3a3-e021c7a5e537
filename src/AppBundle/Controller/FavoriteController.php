<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Wizacha\Bridge\Broadway\Exceptions\InvalidCommandException;
use Wizacha\Marketplace\Entities\Declination;
use Wizacha\Marketplace\Favorite\FavoriteAlreadyExists;
use Wizacha\Marketplace\Favorite\FavoriteService;
use Wizacha\Marketplace\Favorite\UserDeclinationFavorite;

class FavoriteController extends BaseController
{
    public function addFavoriteAction(Request $request)
    {
        if (!$this->getUser()) {
            return $this->redirect('/login');
        }

        // check for csrf
        $submittedToken = $request->get('csrf_token');
        if (!$this->isCsrfTokenValid('add_favorite', $submittedToken)) {
            fn_set_notification('E', __('error'), __('invalid_csrf_token'));
            throw $this->createNotFoundException();
        }
        $declinationId = $request->get('declination_id');

        if ($declinationId) {
            try {
                /** @var FavoriteService $favoriteService */
                $this->get('marketplace.favorite.favorite_service')
                    ->addDeclinationToUserFavorites($this->getUser(), Declination::fromId($declinationId));
            } catch (FavoriteAlreadyExists $e) {
                fn_set_notification('N', __('notice'), __('favorite_already_exist'));
            }
        }

        $referer = $request->headers->get('referer');

        return $this->redirect($referer ?: "/");
    }

    public function addToBasketAction(Request $request)
    {
        $basketId = $this->getOrCreateBasketId();

        foreach ($request->request->get('quantity', []) as $declinationId => $quantity) {
            try {
                $declination = Declination::fromId($declinationId);
                $addedQuantity = $this->container->get('marketplace.basket.domain_service')->addProductToBasket(
                    $basketId,
                    $declination,
                    $quantity
                );
            } catch (InvalidCommandException $e) {
                fn_set_notification('E', __('error'), $e->getMessage());
                return $this->redirectToRoute('favorites_list');
            }

            if (!$addedQuantity) {
                fn_set_notification('E', __('error'), __('out_of_stock_product_in_cart'));
            } elseif ($addedQuantity < $quantity) {
                fn_set_notification('W', __('warning'), __('w_text_cart_amount_corrected'));
            }
        }

        return $this->redirect($this->generateUrl('basket_view'));
    }


    public function listFavoriteAction()
    {
        if (!$this->getUser()) {
            return $this->redirect('/login');
        }


        $favorites = $this->get('marketplace.favorite.favorite_service')->getFavoritesByUser($this->getUser());

        $sortFavorites = [];
        foreach ($favorites as $favorite) {
            if ($favorite->hasReadmodel()) {
                $readmodel = $favorite->getProductReadmodel();
                $sortFavorites[reset($readmodel->getCategoryPath())->getName()][] = $favorite;
            }
        }

        //Pour chaque catégorie, trier les elements à l'interieur de la categorie
        $sortFavorites = array_map(
            function ($favorites) {
                uasort(
                    $favorites,
                    function (UserDeclinationFavorite $favorite1, UserDeclinationFavorite $favorite2) {
                        return strcmp(
                            $favorite1->getProductReadmodel()->getName(),
                            $favorite2->getProductReadmodel()->getName()
                        );
                    }
                );
                return $favorites;
            },
            $sortFavorites
        );

        return $this->render('@App/frontend/favorites/view.html.twig', [
            'groupedFavorites' =>  $sortFavorites,
        ]);
    }

    public function removeFavoriteAction(Request $request)
    {
        if (!$this->getUser()) {
            return $this->redirect('/login');
        }

        // check for csrf
        $submittedToken = $request->get('csrf_token');
        if (!$this->isCsrfTokenValid('remove_favorite', $submittedToken)) {
            fn_set_notification('E', __('error'), __('invalid_csrf_token'));
            throw $this->createNotFoundException();
        }
        $declinationId = $request->get('declination_id');

        if ($declinationId) {
            /** @var FavoriteService $favoriteService */
            $this->get('marketplace.favorite.favorite_service')->removeDeclinationFromUserFavorites($this->getUser(), Declination::fromId($declinationId));
            fn_set_notification('S', __('success'), __('favorite_deleted'));
        }

        $referer = $request->headers->get('referer');

        return $this->redirect($referer ?: "/");
    }

    private function getOrCreateBasketId()
    {
        $session = $this->container->get('session');
        $basketId = $session->get('basketId');

        if (!$basketId) {
            $basketId = $this->container->get('marketplace.basket.domain_service')->generateNewBasket();
            $session->set('basketId', $basketId);
        }
        return $basketId;
    }
}
