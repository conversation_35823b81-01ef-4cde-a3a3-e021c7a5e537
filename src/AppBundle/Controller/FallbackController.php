<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Templating\EngineInterface;

class FallbackController
{
    /**
     * @var EngineInterface
     */
    private $templateEngine;

    public function __construct(EngineInterface $templateEngine)
    {
        $this->templateEngine = $templateEngine;
    }

    public function displayAction(Request $request)
    {
        return new Response(
            $this->templateEngine->render('@App/frontend/views/fallback/display.html.twig', [
                'content' => $request->attributes->get('content'),
                'scripts' => $request->attributes->get('scripts'),
                'styles' => $request->attributes->get('styles'),
            ]),
            $request->attributes->get('status', 200)
        );
    }
}
