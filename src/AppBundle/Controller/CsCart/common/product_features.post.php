<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

use Tygh\Registry;
use Wizacha\Marketplace\GlobalState\GlobalState;

if (!defined('BOOTSTRAP')) {
    die('Access denied');
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    return;
}

if ($mode == 'get_feature_variants_list') {
    if (empty($_REQUEST['feature_id'])) {
        exit;
    }

    $pattern = !empty($_REQUEST['pattern']) ? $_REQUEST['pattern'] : '';
    $start = !empty($_REQUEST['start']) ? $_REQUEST['start'] : 0;
    $limit = (!empty($_REQUEST['limit']) ? $_REQUEST['limit'] : 10) + 1;
    $sorting = db_quote("?:product_feature_variants.position, ?:product_feature_variant_descriptions.variant");

    $join = db_quote(" LEFT JOIN ?:product_feature_variant_descriptions ON ?:product_feature_variant_descriptions.variant_id = ?:product_feature_variants.variant_id AND ?:product_feature_variant_descriptions.lang_code = ?s", (string) GlobalState::contentLocale());
    $condition = db_quote(" AND ?:product_feature_variants.feature_id = ?i", $_REQUEST['feature_id']);

    $objects = db_get_hash_array("SELECT SQL_CALC_FOUND_ROWS ?:product_feature_variants.variant_id AS value, ?:product_feature_variant_descriptions.variant AS name FROM ?:product_feature_variants $join WHERE 1 $condition AND ?:product_feature_variant_descriptions.variant LIKE ?l ORDER BY ?p LIMIT ?i, ?i", 'value', '%' . $pattern . '%', $sorting, $start, $limit);

    if (defined('AJAX_REQUEST') && sizeof($objects) < $limit) {
        Registry::get('ajax')->assign('completed', true);
    } else {
        array_pop($objects);
    }

    if (empty($_REQUEST['enter_other']) || $_REQUEST['enter_other'] != 'N') {
        $total = db_get_found_rows();
        $featureType = db_get_field('SELECT feature_type FROM cscart_product_features WHERE feature_id = ?i', $_REQUEST['feature_id']);
        // Display "Enter other" entry in dropdowns for vendors only on brand field
        if (
            ( $featureType === \Wizacha\Marketplace\PIM\Attribute\AttributeType::LIST_BRAND
                || \Wizacha\Company::runtimeID(AREA, $_SESSION, \Wizacha\Registry::defaultInstance()) == 0
            )
            && $start + $limit >= $total + 1
        ) {
            $objects[] = array('value' => 'disable_select', 'name' => '-' . __('enter_other') . '-');
        }
    }

    if (!$start) {
        array_unshift($objects, array('value' => '', 'name' => '-' . __('none') . '-'));
    }

    Registry::get('view')->assign('objects', $objects);

    Registry::get('view')->assign('id', $_REQUEST['result_ids']);
    Registry::get('view')->display('common/ajax_select_object.tpl');
    exit;

}
