<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON>ey <PERSON>, <PERSON>ya <PERSON>halnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\Registry;
use Wizacha\Product;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

//
// Delete image
//
if ($mode == 'delete_image') {
    if (!empty($auth['user_id'])) {
        $wasDeleted = fn_delete_image($_REQUEST['image_id'], $_REQUEST['pair_id'], $_REQUEST['object_type']);
        if ($wasDeleted === true && \array_key_exists('product_id', $_REQUEST) === true) {
            $event = Product::EVENT_UPDATE;
            \Wizacha\Events\Config::dispatch(
                $event,
                (new \Wizacha\Events\IterableEvent)->setElement($_REQUEST['product_id'])
            );
        }

        if (defined('AJAX_REQUEST')) {
            Registry::get('ajax')->assign('deleted', true);
            Registry::get('ajax')->assign('pairId', $_REQUEST['pair_id']);
        } elseif (!empty($_SERVER['HTTP_REFERER'])) {
            return array(CONTROLLER_STATUS_REDIRECT, $_SERVER['HTTP_REFERER']);
        }
    }
    exit;

//
// Delete image pair
//
} elseif ($mode == 'delete_image_pair') {
    if (AREA == 'A' && !empty($auth['user_id'])) {
        fn_delete_image_pair($_REQUEST['pair_id'], $_REQUEST['object_type']);
        if (defined('AJAX_REQUEST')) {
            Registry::get('ajax')->assign('deleted', true);
        }
    }
    exit;

} elseif ($mode == 'custom_image') {
    if (empty($_REQUEST['image'])) {
        exit();
    }

    $type = empty($_REQUEST['type']) ? 'T' : $_REQUEST['type'];

    $image_path = 'sess_data/' . fn_basename($_REQUEST['image']);

    $customFilesStorageService = container()->get('Wizacha\Storage\CustomFilesStorageService');

    if ($customFilesStorageService->isExist($image_path)) {
        $real_path = $customFilesStorageService->getAbsolutePath($image_path);
        list(, , $image_type, $tmp_path) = fn_get_image_size($real_path);

        if ($type == 'T') {
            $thumb_path = $image_path . '_thumb';

            if (!$customFilesStorageService->isExist($thumb_path)) {
                // Output a thumbnail image
                list($cont, $format) = fn_resize_image($tmp_path, Registry::get('settings.Thumbnails.product_lists_thumbnail_width'), Registry::get('settings.Thumbnails.product_lists_thumbnail_height'), Registry::get('settings.Thumbnails.thumbnail_background_color'));

                if (!empty($cont)) {
                    $customFilesStorageService->put($thumb_path, array(
                        'contents' => $cont
                    ));
                }
            }

            $real_path = $customFilesStorageService->getAbsolutePath($thumb_path);
        }

        header('Content-type: ' . $image_type);
        fn_echo(fn_get_contents($real_path));

        exit();
    }

    // Not image file. Display spacer instead.
    header('Content-type: image/gif');
    readfile(fn_get_theme_path('[themes]/[theme]') . '/media/images/spacer.gif');

    exit();

} elseif ($mode == 'thumbnail') {
    /**
     * @deprecated Use the `image` Symfony route instead
     */

    if ($_REQUEST['w'] || $_REQUEST['h']) {
        $img = fn_generate_thumbnail($_REQUEST['image_path'], $_REQUEST['w'], $_REQUEST['h']);
    } else {
        $img = container()->get('Wizacha\Storage\ImagesStorageService')->getUrl($_REQUEST['image_path']);
    }
    return [CONTROLLER_STATUS_REDIRECT, $img, true];
}
