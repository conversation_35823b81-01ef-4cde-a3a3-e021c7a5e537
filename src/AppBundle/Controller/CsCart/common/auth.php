<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON>ey <PERSON>, Ilya M<PERSON> Shalnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Monolog\Logger;
use Tygh\Languages\Languages;
use Tygh\Registry;
use Tygh\Session;
use Wizacha\AppBundle\Security\Exception\OrganisationUserIsNotApprovedException;
use Wizacha\AppBundle\Security\Exception\VendorHasNoCompanyException;
use Wizacha\AppBundle\Security\User\AuthorizationChecker;
use Wizacha\AppBundle\Security\User\Throttling\AuthenticatorThrottlingService;
use Wizacha\Component\AuthLog\AuthLogRepository;
use Wizacha\Component\AuthLog\DestinationType;
use Wizacha\Component\AuthLog\SourceType;
use Wizacha\Component\AuthLog\StatusType;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\User\Exception\PasswordDontMatch;
use Wizacha\Marketplace\User\Exception\PasswordExist;
use Wizacha\Marketplace\User\Exception\PasswordFormatNotValid;
use Wizacha\Marketplace\User\User;
use Symfony\Component\HttpFoundation\Request;
use Wizacha\Marketplace\User\UserSecurity;
use Wizacha\Marketplace\User\UserService;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

// AuthLog
$authLogRepository = container()->get('marketplace.authlog.repository');
$userSecurity = container()->get('marketplace.user.user_security');
$userService = container()->get(UserService::class);
$authenticatorThrottling = container()->get('app.authenticator_throttling');
$captcha = container()->get('app.captcha');
$captchaFeatureflag = (bool) container()->getParameter('feature.security.captcha');
$googleRecaptchaKey = container()->getParameter('recaptcha.public_key');
$locale = container()->getParameter('locale');
$request = Request::createFromGlobals();

if ($_SERVER['REQUEST_METHOD'] == 'POST') {

    //
    // Login mode
    //
    if ($mode == 'login') {

        $redirect_url = '';

        list($status, $user_data, $user_login, $password, $salt) = fn_auth_routines($request, $_REQUEST['user_login']);

        if (!empty($_REQUEST['redirect_url'])) {
            $redirect_url = $_REQUEST['redirect_url'];
        } else {
            $redirect_url = fn_url('auth.login_form' . (!empty($_REQUEST['return_url']) ? '?return_url=' . $_REQUEST['return_url'] : ''));
        }

        if ($status === false) {
            fn_save_post_data('user_login');

            return array(CONTROLLER_STATUS_REDIRECT, $redirect_url);
        }

        $passwordOK = !empty($user_data) && !empty($password) && fn_verify_password($password, $salt, $user_data['password']);

        // AuthLog
        if (empty($user_data)){
            // Login failed = we save the try for throttling
            $authenticatorThrottling->saveAttempt($user_login);

            $authLogRepository->save(
                $user_login,
                StatusType::UNKNOWN_LOGIN(),
                SourceType::BASIC(),
                DestinationType::mapLegacy(ACCOUNT_TYPE)
            );
        }elseif (!$passwordOK) {
            // Login failed = we save the try for throttling
            $authenticatorThrottling->saveAttempt($user_login);

            if (true === $userSecurity->isBlockAccountFeatureActivated()) {
                $remainingTry = $userSecurity->registerFailedLoginAttempt($user_login);
            }

            $authLogRepository->save(
                $user_login,
                StatusType::WRONG_PASSWORD(),
                SourceType::BASIC(),
                DestinationType::mapLegacy(ACCOUNT_TYPE)
            );
        }

        //
        // Success login
        //
        if ($passwordOK) {
            // Captcha Feature flag activated
            if (true === \filter_var($captchaFeatureflag, FILTER_VALIDATE_BOOLEAN)) {
                // Captcha success
                if (true === $captcha->isHuman($_REQUEST)) {
                    login($user_data, $request, $authLogRepository, $userSecurity, $authenticatorThrottling, $user_login, $redirect_url, $password);
                // Captcha failed
                } else {
                    fn_set_notification('W', __('warning'), __('error_captcha_required'));
                }
            // Captcha Feature flag NOT activated
            } else {
                login($user_data, $request, $authLogRepository, $userSecurity, $authenticatorThrottling, $user_login, $redirect_url, $password);
            }
        } else {
        //
        // Login incorrect
        //
            // Log user failed login
            container()->get('logger')->notice('Failed login for {user}', [
                'user' => $user_login,
            ]);

            $auth = array();

            if (null !== $remainingTry && true === $userSecurity->isBlockAccountFeatureActivated()) {
                fn_set_notification(
                    'E',
                    __('error'),
                    __('error_account_remaining_try', ['remaining_try' => $remainingTry])
                );
            } else {
                fn_set_notification('E', __('error'), __('error_incorrect_login'));
            }

            fn_save_post_data('user_login');

            return fn_redirect($redirect_url);
        }

        unset($_SESSION['edit_step']);
    }

    /** Recover password mode **/
    if ($mode == 'recover_password') {
        $user = container()->get('marketplace.user.user_repository')->findOneByEmail($_REQUEST['user_email']);

        if (false === empty($_REQUEST['user_email'])
            && $user instanceof User
            && (ACCOUNT_TYPE === 'vendor' && $user->getUserType() === 'V'
                || ACCOUNT_TYPE === 'admin' && $user->getUserType() === 'A'
            )
        ) {
            container()->get('marketplace.user.user_service')->recoverPassword($_REQUEST['user_email']);
        }
        fn_set_notification('N', __('information'), __('text_password_recovery_instructions_sent'));
    }

    //
    // Change expired password
    //
    if ($mode == 'password_change') {
        // Fix security breach (Admin account creation with POST on auth.password_change when logged out)
        if (empty($auth['user_id'])) {
            return [CONTROLLER_STATUS_NO_PAGE];
        }

        fn_restore_processed_user_password($_REQUEST['user_data'], $_POST['user_data']);

        if (fn_update_user($auth['user_id'], $_REQUEST['user_data'], $auth, false, true)) {
            $redirect_url = !empty($_REQUEST['return_url']) ? $_REQUEST['return_url'] : '';
        } else {
            $redirect_url = 'auth.password_change';
            if (!empty($_REQUEST['return_url'])) {
                $redirect_url .= '?return_url=' . urlencode($_REQUEST['return_url']);
            }
        }
    }

    if ($mode === 'password_recovery_force_change') {
        $userId = null;

        try {
            $userId = $userService->passwordRecoveryForceChange($_REQUEST['ekey'], $_REQUEST['password'], $_REQUEST['confirm_password']);
        } catch (NotFound $exception) {
            fn_set_notification('E', __('error'), __('text_ekey_not_valid'));

            return array(CONTROLLER_STATUS_OK, "auth.recover_password");
        } catch (PasswordExist $exception) {
            fn_set_notification(
                'E',
                __('error'),
                __('error_password_difference_limit', ['%limit%' => container()->getParameter('previous_passwords_difference_limit')]),
                '',
                'error_password_difference_limit'
            );

            return array(CONTROLLER_STATUS_OK, "auth.password_recovery_force_change&ekey=" . $_REQUEST['ekey']);
        } catch (PasswordDontMatch $exception) {
            fn_set_notification('E', __('error'), __('error_passwords_dont_match'));

            return array(CONTROLLER_STATUS_OK, "auth.password_recovery_force_change&ekey=" . $_REQUEST['ekey']);
        } catch (PasswordFormatNotValid $exception) {
            fn_set_notification(
                'E',
                __('error'),
                __('error_password_format_not_valid'),
                '',
                'password_format_invalid'
            );

            return array(CONTROLLER_STATUS_OK, "auth.password_recovery_force_change&ekey=" . $_REQUEST['ekey']);
        }

        if (\is_null($userId) === false && \mb_strlen($userId) > 0) {
            fn_set_notification('N', __('notice'), __('success_password_recovery_force_change'));

            return array(CONTROLLER_STATUS_OK, "profiles.update?user_id=$userId");
        }

        return array(CONTROLLER_STATUS_OK, fn_url());
    }

    return array(CONTROLLER_STATUS_OK, !empty($redirect_url)? $redirect_url : fn_url());
}

//
// Perform user log out
//
if ($mode == 'logout') {

    // Regenerate session_id for security reasons

    fn_save_cart_content($_SESSION['cart'], $auth['user_id']);

    Session::regenerateId();
    fn_init_user();
    $auth = $_SESSION['auth'];

    if (!empty($auth['user_id'])) {
        // Log user logout
        fn_log_event('users', 'session', array(
            'user_id' => $auth['user_id'],
            'time' => TIME - $auth['this_login'],
            'timeout' => false,
            'company_id' => fn_get_company_id('users', 'user_id', $auth['user_id']),
        ));
    }

    unset($_SESSION['auth']);
    fn_clear_cart($_SESSION['cart'], false, true);

    fn_delete_session_data(AREA . '_user_id', AREA . '_password');

    //Hotfix : clear all data
    $_SESSION = [];

    fn_login_user(); // need to fill $_SESSION['auth'] array for anonymous user

    // check if password expired notification must be displayed
    if (\array_key_exists('passwordExpiryTimeLeft', $_REQUEST) === true) {
        fn_set_notification('E',
            __('error'),
            __('error_current_password_expired',
                [
                    '[passwordRenewalTimeLimit]' => $_REQUEST['passwordExpiryTimeLeft']
                ]
            )
        );
    }

    return array(CONTROLLER_STATUS_OK, fn_url());
}

if ($mode === 'password_recovery_force_change') {
    if (defined('AJAX_REQUEST') && empty($auth)) {
        exit;
    }

    if (!empty($auth['user_id'])) {
        return array(CONTROLLER_STATUS_REDIRECT, fn_url());
    }

    if (\array_key_exists('ekey', $_REQUEST) === false) {
        fn_set_notification('E', __('error'), __('text_ekey_not_valid'));

        return array(CONTROLLER_STATUS_OK, "auth.recover_password");
    }

    if (\array_key_exists('ekey', $_REQUEST) === true) {
        $u_id = $userService->getUserIdFromEKey($_REQUEST['ekey']);
        if (\is_string($u_id) === false || \mb_strlen($u_id) === 0) {
            fn_set_notification('E', __('error'), __('text_ekey_not_valid'));

            return array(CONTROLLER_STATUS_OK, "auth.recover_password");
        }
    }

    if (AREA === 'C') {
        return [CONTROLLER_STATUS_REDIRECT, container()->get('router')->generate('recover_password')];
    }

    Registry::get('view')->assign('locale', $locale);
    Registry::get('view')->assign('view_mode', 'simple');
    Registry::get('view')->assign('ekey', $_REQUEST['ekey']);
}

//
// Recover password mode
//
if ($mode == 'recover_password') {
    $isPasswordRecoveryForceChangeActivated = container()->getParameter('feature.password_recovery_force_change');
    // Cleanup expired keys
    $userService->removeExpiredEKeys(); // FIXME: should be moved to another place

    if (!empty($_REQUEST['ekey'])) {
        if ($isPasswordRecoveryForceChangeActivated === true) {
            return array(CONTROLLER_STATUS_NO_PAGE, fn_url());
        }

        $u_id = $userService->getUserIdFromEKey($_REQUEST['ekey']);
        if (!empty($u_id)) {

            // Delete this key
            $userService->removeByEKey($_REQUEST['ekey']);

            $user_status = fn_login_user($u_id);
            $_SESSION['auth']['w_recover_password'] = true;

            if ($user_status == LOGIN_STATUS_OK) {
                fn_set_notification('N', __('notice'), __('text_change_password'));

                return array(CONTROLLER_STATUS_OK, "profiles.update?user_id=$u_id");
            } else {
                fn_set_notification('E', __('error'), __('error_login_not_exists'));

                return array(CONTROLLER_STATUS_OK, fn_url());
            }
        } else {
            fn_set_notification('E', __('error'), __('text_ekey_not_valid'));

            return array(CONTROLLER_STATUS_OK, "auth.recover_password");
        }
    }

    if (AREA === 'C') {
        // Frontend users use Symfony action when no ekey is set
        return [CONTROLLER_STATUS_REDIRECT, container()->get('router')->generate('recover_password')];
    }

    if (AREA != 'A') {
        fn_add_breadcrumb(__('recover_password'));
    }

    Registry::get('view')->assign('isPasswordRecoveryForceChangeActivated', $isPasswordRecoveryForceChangeActivated);
    Registry::get('view')->assign('view_mode', 'simple');
}

//
// Display login form in the mainbox
//
if ($mode == 'login_form') {
    if (defined('AJAX_REQUEST') && empty($auth)) {
        exit;
    }

    if (!empty($auth['user_id'])) {
        return array(CONTROLLER_STATUS_REDIRECT, fn_url());
    }

    $stored_user_login = fn_restore_post_data('user_login');
    if (!empty($stored_user_login)) {
        Registry::get('view')->assign('stored_user_login', $stored_user_login);
    }

    if (AREA != 'A') {
        fn_add_breadcrumb(__('sign_in'));
    }

    try {
        $authorizeUrl = (string) container()->get('marketplace.oauth.admin_provider')->getAuthorizeUrl();
        Registry::get('view')->assign('sso_url', $authorizeUrl);
    } catch (\Throwable $e) {
        // pass
    }

    $canLoginWithForm = true;
    if (\strlen($authorizeUrl) > 0) {
        $canLoginWithForm = (bool) container()->getParameter('feature.sso_connection_only_bo') === false;
    }

    Registry::get('view')->assign('locale', $locale);

    Registry::get('view')->assign('canLoginWithForm', $canLoginWithForm);

    Registry::get('view')->assign('captcha', $captchaFeatureflag);

    Registry::get('view')->assign('recaptchaKey', $googleRecaptchaKey);

    Registry::get('view')->assign('view_mode', 'simple');

} elseif ($mode == 'password_change' && AREA == 'A') {
    if (defined('AJAX_REQUEST') && empty($auth)) {
        exit;
    }

    if (empty($auth['user_id'])) {
        return array(CONTROLLER_STATUS_REDIRECT, fn_url());
    }

    $profile_id = 0;
    $user_data = fn_get_user_info($auth['user_id'], true, $profile_id);

    Registry::get('view')->assign('user_data', $user_data);
    Registry::get('view')->assign('view_mode', 'simple');

} elseif ($mode == 'change_login') {
    $auth = $_SESSION['auth'];

    if (!empty($auth['user_id'])) {
        // Log user logout
        fn_log_event('users', 'session', array(
            'user_id' => $auth['user_id'],
            'time' => TIME - $auth['this_login'],
            'timeout' => false,
            'company_id' => fn_get_company_id('users', 'user_id', $auth['user_id']),
        ));
    }

    unset($_SESSION['auth'], $_SESSION['cart']['user_data']);

    fn_delete_session_data(AREA . '_user_id', AREA . '_password');

    return array(CONTROLLER_STATUS_OK, 'checkout.checkout');
} elseif ($mode == 'support') {
    // redirect if already logged in with right user type
    $userType = (defined('ACCOUNT_TYPE') && ACCOUNT_TYPE === 'vendor') ? 'V' : AREA;
    if (($auth['user_type'] ?? '') === $userType && ($auth['user_id'] ?? 0) != 0) {
        return [CONTROLLER_STATUS_REDIRECT, fn_url()];
    }

    $client = container()->get('app.google_auth_support_client');

    if (isset($_GET['code'])) {
        $client->fetchAccessTokenWithAuthCode($_GET['code']);

        if ($client->getAccessToken()) {
            $userInfo = $client->verifyIdToken();
            $userEmail = $userInfo['email'];
            $domains = ['wizacha.com', 'wizaplace.com'];
            $emailParts = explode('@', $userEmail);
            $isMemberSupportLogin = false;
            $isUnknownGroup = false;

            if (true === $userInfo['email_verified'] &&
                true === \array_key_exists(0, $emailParts) &&
                true === \array_key_exists(1, $emailParts) &&
                true === in_array(\strtolower($userInfo['hd']), $domains, true) &&
                true === in_array(\strtolower($emailParts[1]), $domains, true) &&
                0 < \strlen($userEmail)
            ) {
                if (defined('ACCOUNT_TYPE') && ACCOUNT_TYPE === 'vendor') {
                    $userId = $_GET['userId'] ?: db_get_field("SELECT user_id FROM cscart_users WHERE status = 'A' AND user_type = 'V' AND company_id <> 0 ORDER BY user_id ASC LIMIT 1");
                } else {
                    $userId = $_GET['userId'] ?: db_get_field("SELECT user_id FROM cscart_users WHERE status = 'A' AND user_type = 'A' AND company_id = 0 ORDER BY user_id ASC LIMIT 1");
                }

                $googleServiceDirectory = new \Google_Service_Directory($client);

                foreach (explode(',', container()->getParameter('support_login.groups')) as $group) {
                    if (0 === \strlen($group)) {
                        continue;
                    }

                    try {
                        $hasMember = $googleServiceDirectory->members->hasMember($group, $userEmail);
                    } catch (\Google_Service_Exception $exception) {
                        /** @var Logger */
                        $logger = container()->get('logger');
                        $logger->error(
                            'GoogleServiceDirectory',
                            ['exception' => $exception]
                        );

                        $isUnknownGroup = true;
                        continue;
                    }

                    if ($hasMember->isMember) {
                        $isUnknownGroup = false;
                        $isMemberSupportLogin = true;
                        break;
                    }
                }
            }

            if (true === $isMemberSupportLogin && false === $isUnknownGroup) {
                Session::regenerateId();
                fn_login_user($userId);
                fn_set_notification('N', __('notice'), __('successful_login'));

                // AuthLog
                $authLogRepository->save(
                    $userEmail,
                    StatusType::SUCCESS(),
                    SourceType::SSO(),
                    DestinationType::mapLegacy(ACCOUNT_TYPE)
                );
            } else {
                if (false === $isMemberSupportLogin && false === $isUnknownGroup) {
                    fn_set_notification('E', __('error'), __('error_area_access_denied'));
                } elseif (false === $isMemberSupportLogin && true === $isUnknownGroup) {
                    fn_set_notification('E', __('error'), __('error_area_access_denied_unknown_group'));
                }

                // AuthLog
                $authLogRepository->save(
                    $userEmail,
                    StatusType::ACCESS_DENIED(),
                    SourceType::SSO(),
                    DestinationType::mapLegacy(ACCOUNT_TYPE)
                );
            }

            return [CONTROLLER_STATUS_REDIRECT, fn_url()];
        }
    } else {
        $client->setHostedDomain('wizaplace.com');
        $client->setState(fn_url('auth.support?userId='.$_GET['userId'])); // Use state parameter to forward origin
        $client->addScope([
            \Google_Service_Oauth2::USERINFO_EMAIL,
            \Google_Service_Directory::ADMIN_DIRECTORY_GROUP_MEMBER_READONLY,
        ]);

        return [CONTROLLER_STATUS_REDIRECT, $client->createAuthUrl(), true];
    }

    return [CONTROLLER_STATUS_REDIRECT, fn_url()];
}

function login(
    array $user_data,
    Request $request,
    AuthLogRepository $authLogRepository,
    UserSecurity $userSecurity,
    AuthenticatorThrottlingService $authenticatorThrottling,
    string $user_login,
    string $redirect_url,
    string $password
) {
    // List of available languages
    $languages = Languages::getAvailable();
    if (\array_key_exists($user_data['lang_code'], $languages) === true) {
        $lang_code = $user_data['lang_code'];
    } else {
        $lang_code = $request->getDefaultLocale();
    }

    // Language of BO
    fn_set_session_data('cart_language' . AREA, $lang_code, COOKIE_ALIVE_TIME);

    // Language of content
    fn_set_session_data('descr_sl', $lang_code, COOKIE_ALIVE_TIME);

    // si l'utilisateur est rattaché à une organisation, on vérifie que cette dernière est bien active
    $user = container()->get('marketplace.user.user_service')->get($user_data['user_id']);

    try {
        $checker = new AuthorizationChecker();
        $checker->check($user);
    } catch (VendorHasNoCompanyException $e) {
        fn_set_notification('E', __('error'), __('error_vendor_access_denied'));
        fn_save_post_data('user_login');

        // AuthLog
        $authLogRepository->save(
            $user_login,
            StatusType::ACCESS_DENIED(),
            SourceType::BASIC(),
            DestinationType::mapLegacy(ACCOUNT_TYPE)
        );

        return fn_redirect($redirect_url);
    } catch (OrganisationUserIsNotApprovedException $e) {
        $auth = array();
        fn_set_notification('E', __('error'), __('error_login_organisation_not_approved'));
        fn_save_post_data('user_login');

        // AuthLog
        $authLogRepository->save(
            $user_login,
            StatusType::PENDING_ACCOUNT(),
            SourceType::BASIC(),
            DestinationType::mapLegacy(ACCOUNT_TYPE)
        );

        return fn_redirect($redirect_url);
    }

    // Regenerate session_id for security reasons
    Session::regenerateId();

    //
    // If customer placed orders before login, assign these orders to this account
    //
    if (!empty($auth['order_ids'])) {
        foreach ($auth['order_ids'] as $k => $v) {
            db_query("UPDATE ?:orders SET ?u WHERE order_id = ?i", array('user_id' => $user_data['user_id']), $v);
        }
    }

    fn_login_user($user_data['user_id']);
    $authenticatorThrottling->resetAttempts($user_login);

    // check if logged user password is expired or not
    if (\array_key_exists('password_change_timestamp', $_SESSION['auth']) === true) {

        $passwordExpiryTimeLeft = container()->get(UserService::class)->calculatePasswordExpiryTimeLeft($_SESSION['auth']['password_change_timestamp']);

        if ($passwordExpiryTimeLeft !== null && $passwordExpiryTimeLeft <= UserService::PASSWORD_RENEWAL_TIME_NOTIFY_USER) {
            fn_set_notification('W',
                __('notice'),
                __('warning_password_expiry_time_left',
                    [
                        '[passwordExpiryTimeLeft]' => $passwordExpiryTimeLeft
                    ]
                )
            );
        }
    }

    // Set system notifications
    if (AREA == 'A') {
        // If username equals to the password
        if (!(defined('DEVELOPMENT') && DEVELOPMENT) && fn_compare_login_password($user_data, $password)) {

            $lang_var = 'warning_insecure_password';
            if (Registry::get('settings.General.use_email_as_login') == 'Y') {
                $lang_var = 'warning_insecure_password_email';
            }

            fn_set_notification('E', __('warning', [], $lang_code), __($lang_var, array(
                '[link]' => fn_url('profiles.update')
            ), $lang_code), 'S', 'insecure_password');
        }
        if (empty($user_data['company_id']) && !empty($user_data['user_id'])) {
            if ($user_data['company_id'] == 0) {

                $count = db_get_field("SELECT COUNT(*) FROM ?:companies WHERE status IN ('N', 'P')");

                if ($count > 0) {
                    fn_set_notification('W', __('notice', [], $lang_code), __('text_not_approved_vendors', array(
                        '[link]' => fn_url('companies.manage?status[]=N&status[]=P')
                    ), $lang_code), 'K');
                }
            }
            if ($auth['company_id'] == 0 && fn_check_permissions('premoderation', 'products_approval', 'admin')) {
                $count = db_get_field('SELECT COUNT(*) FROM ?:products WHERE approved = ?s', 'P');

                if ($count > 0) {
                    fn_set_notification('W', __('notice', [], $lang_code), __('text_not_approved_products', array(
                        '[link]' => fn_url('premoderation.products_approval?approval_status=P')
                    ), $lang_code), 'K');
                }
            }
            if ($auth['company_id'] == 0 && fn_check_permissions('premoderation', 'features_approval', 'admin')) {
                $count = db_get_field("SELECT COUNT(*) FROM ?:product_features WHERE status = 'H' LIMIT 1");

                if ($count > 0) {
                    fn_set_notification('W', __('notice', [], $lang_code), __('w_not_approved_features', array(
                        '[link]' => fn_url('premoderation.features_approval')
                    ), $lang_code), 'K');
                }
            }
            if ($auth['company_id'] == 0 && fn_check_permissions('premoderation', 'options_approval', 'admin')) {
                $approval_needed = \Tygh\Database::getField(
                    'SELECT 1 FROM ?:product_options WHERE 1 ?p',
                    W_SUBIMTTED_OPTION_CONDITION
                );

                if ($approval_needed) {
                    fn_set_notification(
                        'W',
                        __('notice', [], $lang_code),
                        __('w_text_not_approved_option',
                            ['[link]' => fn_url('premoderation.options_approval')],
                            $lang_code
                        ),
                        'K'
                    );
                }
            }
        }
    }

    if (!empty($_REQUEST['remember_me'])) {
        fn_set_session_data(AREA . '_user_id', $user_data['user_id'], COOKIE_ALIVE_TIME);
        fn_set_session_data(AREA . '_password', $user_data['password'], COOKIE_ALIVE_TIME);
    }

    // Set last login time
    db_query("UPDATE ?:users SET ?u WHERE user_id = ?i", array('last_login' => TIME), $user_data['user_id']);

    $_SESSION['auth']['this_login'] = TIME;
    $_SESSION['auth']['ip'] = $_SERVER['REMOTE_ADDR'];

    // Log user successful login
    fn_log_event('users', 'session', array(
        'user_id' => $user_data['user_id'],
        'company_id' => fn_get_company_id('users', 'user_id', $user_data['user_id']),
    ));

    // AuthLog
    $authLogRepository->save(
        $user_login,
        StatusType::SUCCESS(),
        SourceType::BASIC(),
        DestinationType::mapLegacy(ACCOUNT_TYPE)
    );

    $userSecurity->resetAttemptsCount($user_login);

    $redirect_to_checkout = false;
    if (!empty($_REQUEST['return_url'])) {
        $return_url = $_REQUEST['return_url'];
        if (strpos($return_url, 'checkout.checkout') !== false) {
            $redirect_to_checkout = true;
        }
    }

    unset($_REQUEST['redirect_url']);

    if (AREA == 'C') {
        fn_set_notification('N', __('notice', [], $lang_code), __('successful_login', [], $lang_code));
    }

    if (AREA == 'A' && Registry::get('runtime.unsupported_browser')) {
        return array(CONTROLLER_STATUS_REDIRECT, fn_url());
    }

    //Basket merge
    $user = new \Wizacha\User($user_data['user_id']);
    return container()->get('marketplace.user.domain_service')->setUserBasket($user, $redirect_to_checkout);
}
