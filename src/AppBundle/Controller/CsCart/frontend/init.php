<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON>nev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\BlockManager\Location;
use Tygh\Registry;
use Tygh\Session;
use Wizacha\Marketplace\GlobalState\GlobalState;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

if (!empty($_REQUEST['skey'])) {
    $session_data = fn_get_storage_data('session_' . $_REQUEST['skey'] . '_data');
    fn_set_storage_data('session_' . $_REQUEST['skey'] . '_data', '');

    if (!empty($session_data)) {
        $_SESSION = unserialize($session_data);
    }

    return array(CONTROLLER_STATUS_REDIRECT, fn_query_remove(REAL_URL, 'skey'));
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    return;
}

if (empty($_REQUEST['product_id']) && empty($_REQUEST['category_id'])) {
    unset($_SESSION['current_category_id']);
}

$dynamic_object = array();
if (!empty($_REQUEST['dynamic_object'])) {
    $dynamic_object = $_REQUEST['dynamic_object'];
}
Registry::get('view')->assign('location_data', Location::instance()->get($_REQUEST['dispatch'], $dynamic_object, (string) GlobalState::interfaceLocale()));

// Init cart if not set
if (empty($_SESSION['cart'])) {
    fn_clear_cart($_SESSION['cart']);
}

if (!empty($_SESSION['continue_url'])) {
    $_SESSION['continue_url'] = fn_url_remove_service_params($_SESSION['continue_url']);
}
