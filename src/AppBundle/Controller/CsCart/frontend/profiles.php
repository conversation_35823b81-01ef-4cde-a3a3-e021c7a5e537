<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON>ey <PERSON>, Ilya <PERSON> Shalnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Symfony\Component\HttpFoundation\Response;
use Tygh\Registry;
use Tygh\Session;
use Tygh\SmartyEngine\Core;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\User\UserSerializer;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

if ($_SERVER['REQUEST_METHOD'] == 'POST') {

    //
    // Create/Update user
    //
    if ($mode == 'update') {
        fn_restore_processed_user_password($_REQUEST['user_data'], $_POST['user_data']);

        $is_update = !empty($auth['user_id']);

        if (!$is_update) {
            $is_valid_user_data = true;

            if (!container()->get('app.captcha')->isHuman($_REQUEST)) {
                fn_set_notification('W', __('warning'), __('error_captcha_required'));
                $is_valid_user_data = false;
            }

            $shouldAcceptTerms = container()->getParameter('feature.create_account.must_accept_terms');
            if ($shouldAcceptTerms && empty($_REQUEST['terms_approved'])) {
                fn_set_notification('E', __('error'), __('register_must_accept_terms'));
                $is_valid_user_data = false;
            }

            if (empty($_REQUEST['user_data']['email'])) {
                fn_set_notification('W', __('warning'), __('error_validator_required', array('[field]' => __('email'))));
                $is_valid_user_data = false;

            } elseif (!fn_validate_email($_REQUEST['user_data']['email'])) {
                fn_set_notification('W', __('error'), __('text_not_valid_email', array('[email]' => $_REQUEST['user_data']['email'])));
                $is_valid_user_data = false;
            }

            if (!$is_valid_user_data) {
                $loginUrl = container()->get('router')->generate('login');
                return array(CONTROLLER_STATUS_REDIRECT, $loginUrl, true);
            }
        }

        if (\array_key_exists('birthday', $_REQUEST['user_data']) === true
            && \strlen($_REQUEST['user_data']['birthday']) > 0
        ) {
            if (\preg_match('/(\d{2})\/(\d{2})\/(\d{4})$/', $_REQUEST['user_data']['birthday']) === 0) {
                fn_set_notification('E', __('error'), __('error_validator_birthday'));

                return array(CONTROLLER_STATUS_OK, "profiles.update");
            }

            // Changer la date format de d/m/y -> Y-m-d
            $_REQUEST['user_data']['birthday'] =
                DateTime::createFromFormat('d/m/Y', $_REQUEST['user_data']['birthday'])
                ->format(UserSerializer::BIRTHDAY_FORMAT);
        }

        $res = fn_update_user($auth['user_id'], $_REQUEST['user_data'], $auth, !empty($_REQUEST['ship_to_another']), true);

        if ($res) {
            list($user_id, $profile_id) = $res;

            $user = new \Wizacha\User($user_id);
            if (isset($_REQUEST['user_data']['additional_datas']['pseudo'])) {
                $user->setPseudo($_REQUEST['user_data']['additional_datas']['pseudo']);
            }
            if (isset($_REQUEST['user_data']['additional_datas']['settings_notifications'])) {
                $user->setNotificationsSetting($_REQUEST['user_data']['additional_datas']['settings_notifications']);
            }

            // Cleanup user info stored in cart
            if (!empty($_SESSION['cart']) && !empty($_SESSION['cart']['user_data'])) {
                unset($_SESSION['cart']['user_data']);
            }

            // Delete anonymous authentication
            if ($cu_id = fn_get_session_data('cu_id') && !empty($auth['user_id'])) {
                fn_delete_session_data('cu_id');
            }

            Session::regenerateId();
            if (!$is_update) {
                container()->get('marketplace.user.domain_service')->setUserBasket($user);
            }

            if (!empty($_REQUEST['return_url'])) {
                return array(CONTROLLER_STATUS_OK, $_REQUEST['return_url']);
            }

        } else {
            fn_save_post_data('user_data');
            fn_delete_notification('changes_saved');
        }

        $redirectUrl = container()->get('router')->generate('login');
        return [CONTROLLER_STATUS_REDIRECT, $redirectUrl, true];
    }
}

if ($mode == 'update') {

    if (empty($auth['user_id'])) {
        return array(CONTROLLER_STATUS_REDIRECT, "auth.login_form?return_url=".urlencode(Registry::get('config.current_url')));
    }

    $profile_id = empty($_REQUEST['profile_id']) ? 0 : $_REQUEST['profile_id'];
    fn_add_breadcrumb(__('editing_profile'));

    if (!empty($_REQUEST['profile']) && $_REQUEST['profile'] == 'new') {
        $user_data = fn_get_user_info($auth['user_id'], false);
    } else {
        $user_data = fn_get_user_info($auth['user_id'], true, $profile_id);
    }

    if (empty($user_data)) {
        return array(CONTROLLER_STATUS_NO_PAGE);
    }

    $restored_user_data = fn_restore_post_data('user_data');
    if ($restored_user_data) {
        $user_data = fn_array_merge($user_data, $restored_user_data);
    }

    $user = new \Wizacha\User($auth['user_id']);
    $user_data['additional_datas'] = [
        'pseudo'                 => $user->getPseudo(),
        'settings_notifications' => $user->getNotificationsSetting(),
    ];

    Registry::set('navigation.tabs.general', array (
        'title' => __('general'),
        'js' => true
    ));

    $profile_fields = fn_get_profile_fields();

    Registry::get('view')->assign('profile_fields', $profile_fields);
    Registry::get('view')->assign('can_edit_pseudo', !$user->isVendor());
    Registry::get('view')->assign('user_data', $user_data);
    Registry::get('view')->assign('ship_to_another', fn_check_shipping_billing($user_data, $profile_fields));
    Registry::get('view')->assign('countries', fn_get_simple_countries(true, (string) GlobalState::interfaceLocale()));
    Registry::get('view')->assign('states', fn_get_all_states());
    if (Registry::get('settings.General.user_multiple_profiles') == 'Y') {
        Registry::get('view')->assign('user_profiles', fn_get_user_profiles($auth['user_id']));
    }

    /** @var Core $smarty */
    $smarty = Registry::get('view');
    $templating = container()->get('templating');
    if ($action == 'address' && $templating->exists('@App/frontend/views/profile/update_address.html.twig')) {
        $html = $templating->render('@App/frontend/views/profile/update_address.html.twig', [
            'profile' => $user_data,
            'auth' => $auth,
            'shipToAnother' => fn_check_shipping_billing($user_data, $profile_fields),
        ]);
        return [CONTROLLER_STATUS_OK, null, null, new Response($html)];
    }
    if ($action == 'newsletters' && $templating->exists('@App/frontend/views/profile/update_newsletters.html.twig')) {
        $html = $templating->render('@App/frontend/views/profile/update_newsletters.html.twig', [
            'profile' => $user_data,
            'auth' => $auth,
        ]);
        return [CONTROLLER_STATUS_OK, null, null, new Response($html)];
    }
    if ($templating->exists('@App/frontend/views/profile/update.html.twig')) {
        $html = $templating->render('@App/frontend/views/profile/update.html.twig', [
            'profile' => $user_data,
            'canEditPseudo' => !$user->isVendor(),
            'auth' => $auth,
            'user' => \Wizacha\Registry::defaultInstance()
                ->container
                ->get('marketplace.user.user_service')
                ->get((int) $auth['user_id']),
        ]);
        return [CONTROLLER_STATUS_OK, null, null, new Response($html)];
    }

// Delete profile
} elseif ($mode == 'delete_profile') {

    fn_delete_user_profile($auth['user_id'], $_REQUEST['profile_id']);

    return array(CONTROLLER_STATUS_OK, "profiles.update");

}
