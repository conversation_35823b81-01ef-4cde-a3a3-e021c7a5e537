<?php
/**
 * This controller is used in development as a fallback to Algolia.
 */

use Symfony\Component\HttpFoundation\JsonResponse;
use Tygh\Registry;
use Wizacha\Marketplace\Entities\Declination;
use Wizacha\Marketplace\Price\PriceFields;
use Wizacha\Registry as WizachaRegistry;
use Wizacha\Search\Index\AbstractIndex;
use Wizacha\Search\Record\Product;
use Wizacha\Search\Record\Record;

if (!defined('BOOTSTRAP')) {
    die('Access denied');
}

if ('ajax' == $mode) {
    $q = $_REQUEST['q'];
    $index = $_REQUEST['index'];
    $args = $_REQUEST['args'];

    $response = getResponseByIndex($q, $index, $args);

    echo json_encode($response);
    exit;
} elseif ('batch' == $mode) {
    $q = $_REQUEST['q'];
    $indexes = $_REQUEST['indexes'];

    $response = [
        'results' => []
    ];

    foreach ($indexes as $index) {
        $response['results'][] = getResponseByIndex($q, $index['index'], $index['params']);
    }

    echo json_encode($response);
    exit;
} elseif ('facets' == $mode) {
    $start_time = microtime(true);
    $args       = $_REQUEST['args'] ? : [];
    $params     = [
        'page' => $_REQUEST['page'] + 1,
    ];

    if (isset($args['tagFilters']) && $args['tagFilters'][0] == Product::NAMESPACE_VENDOR) {
        $params['company_id'] = substr($args['tagFilters'], 1);
    }
    if (!isset($args['facetFilters'])) {
        $args['facetFilters'] = '';
    }
    if (!isset($args['facets'])) {
        $args['facets'] = '';
    }

    // Override cscart settings to be in sync with sf container
    $showOutOfStockProducts = container()->getParameter("feature.catalog.show_out_of_stock_products");
    Registry::set("settings.General.show_out_of_stock_products", $showOutOfStockProducts ? "Y" : "N");

    list($products, $search) = fn_get_products($params, $args['hitsPerPage'] ? : Registry::get('settings.Appearance.products_per_page'));
    $productService = container()->get('marketplace.product.productservice');

    $productRecordFactory = container()->get('marketplace.search.product_record_factory');

    //Format products as Records
    $hits = [];
    foreach ($products as $product) {
        $productId = $product['product_id'];

        $promotionService = container()->get('marketplace.promotion.promotionservice');

        $declination = new Declination(new \Wizacha\Product($productId));
        $price = $promotionService->getProductFinalPrice($declination);
        $price = $price->get(PriceFields::BASE_PRICE())->getAmount() / 100;

        $record = $productRecordFactory->create($productService->getProduct($productId));

        $hits[] = $record->data();
    }

    //Special facets
    $specialFacets = [
        Product::KEY_PRICE       => 1,
        Product::KEY_VENDOR_TYPE => 1,
        Product::KEY_VENDOR      => 1,
        Product::KEY_CONDITION   => 1,
    ];
    //Retrieve facets from current results
    $facets = [];
    foreach ($hits as $hit) {
        foreach ($hit as $property => $value) {
            if (isset($specialFacets[$property])) {
                $facets[$property]         = $facets[$property] ? : [];
                if (is_array($value)) {
                    foreach ($value as $subValue) {
                        $facets[$property][$subValue] = $facets[$property][$subValue] + 1 ? : 1;
                    }
                } else {
                    $facets[$property][$value] = $facets[$property][$value] + 1 ? : 1;
                }
            } elseif (explode(Record::NAMESPACE_SEPARATOR, $property)[0] == Record::NAMESPACE_FACET) {
                $facets[$property] = $facets[$property] ? : [];
                if (is_array($value)) {
                    foreach ($value as $variant) {
                        $facets[$property][$variant] = intval($search['total_items']*0.6);
                    }
                } else {
                    $facets[$property][$value] = $search['total_items']*0.6;
                }
            }
        }
    }
    //Retrieve price statistics
    $prices = array_column($hits, Product::KEY_PRICE);
    $facetStats = [
        Product::KEY_PRICE => [
            'min' => empty($prices) ? 0 : min($prices),
            'max' => empty($prices) ? 0 : max($prices),
        ]
    ];

    $response = new JsonResponse([
        'hits'              => $hits,
        'index'             => $_REQUEST['index'],
        'refinements'       => $_REQUEST['refinements'],
        'query'             => $_REQUEST['q'],
        'nbHits'            => $search['total_items'],
        'page'              => $search['page'] - 1,
        'nbPages'           => ceil($search['total_items'] / $search['items_per_page']),
        'hitsPerPage'       => $search['items_per_page'],
        'processingTimeMS'  => ceil((microtime(true) - $start_time) * 1000),
        'disjunctiveFacets' => $facets,
        'facetStats'        => $facetStats,
        'params'            => http_build_query($args),
    ]);

    return [CONTROLLER_STATUS_OK, null, false, $response];
}

/*STRICTLY LOCAL FUNCTIONS*/
function getResponseByIndex($q, $index, $args)
{
    $config = \Wizacha\Registry::defaultInstance()->get(['config', \Wizacha\Search\Config::CFG_SEARCH_ENGINE]);
    $page = isset($args['page']) ? $args['page'] : 0;
    $hits_per_page = isset($args['hitsPerPage']) ? $args['hitsPerPage'] : 2;
    $hit_start = $page * $hits_per_page;

    $nb_hits = 0;

    $highlight_results = '_highlightResult';
    $results    = [];
    $hits       = [];
    $class      = '';
    $query      = '%'.$q.'%';
    switch ($index) {
        case $config[\Wizacha\Search\Config::INDEX_PRODUCTS]:
            $class = '\Wizacha\Search\Record\Product';
            $results = \Tygh\Database::query(
                "SELECT SQL_CALC_FOUND_ROWS d.product_id as id FROM ?:product_descriptions as d
                LEFT JOIN ?:products as p ON d.product_id = p.product_id
                WHERE product LIKE ?l
                OR full_description LIKE ?l
                AND p.status = 'A'
                LIMIT ?i,?i",
                $query,
                $query,
                $hit_start,
                $hits_per_page
            );
            $nb_hits = db_get_found_rows();
            break;
        case $config[\Wizacha\Search\Config::INDEX_CATEGORIES]:
            $class = '\Wizacha\Search\Record\Category';
            $results = \Tygh\Database::query(
                "SELECT d.category_id as id FROM ?:category_descriptions as d
                LEFT JOIN ?:categories as c ON d.category_id = c.category_id
                WHERE category LIKE ?l
                OR d.meta_description LIKE ?l
                AND c.status = 'A'
                LIMIT ?i,?i",
                $query,
                $query,
                $hit_start,
                $hits_per_page
            );
            break;

        case $config[\Wizacha\Search\Config::INDEX_GEOCODING]:
            for($i = 0; $i< 10; ++$i) {
                $hits[] = [
                    'name'    => "$q $i",
                    'postal'  => array_fill(0, $i + 1, "0000$i"),
                    '_geoloc' => [
                        'lat' => $i,
                        'lng' => $i,
                    ],
                ];
            }
            break;
    }

    $to_be_higlighted = ['name', 'path'];
    if (class_exists($class) && isset(class_implements($class)[\Wizacha\Search\Record\Record::class])) {
        foreach ($results as $result) {
            $record = $class::fromId($result['id']);
            if ($record->isDeletable()) {
                continue;
            }
            $datas = $record->data();
            foreach ($to_be_higlighted as $mark) {
                if (isset($datas[$mark])) {
                    $datas[$highlight_results][$mark]['value'] = str_ireplace($q, '<em>'.$q.'</em>', $datas[$mark]);
                }
            }
            $hits[] = $datas;
        }
    }

    return [
        "hits" => $hits,
        "index" => $index,
        "query" => $q,
        "nbHits" => $nb_hits,
        "nbPages" => ceil($nb_hits/$hits_per_page),
        "hitsPerPage" => $hits_per_page
    ];
}
