<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

use Symfony\Component\HttpFoundation\Response;
use Tygh\Registry;
use Tygh\SmartyEngine\Core;
use Wizacha\Discuss\Exception\CompanyHasNoAdministrator;
use Wizacha\Discuss\Exception\SenderIsAlsoRecipient;
use Wizacha\Marketplace\Exception\NotFound;

if (!defined('BOOTSTRAP')) {
    die('Access denied');
}

if (empty($auth['user_id'])) {
    return array(CONTROLLER_STATUS_REDIRECT, "auth.login_form?return_url=".urlencode(Registry::get('config.current_url')));
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if ($mode == 'update') {
        if (!isset($_REQUEST['discussion_id'])
            || !isset($_REQUEST['content'])
            || !(isset($_REQUEST['product_id']) || isset($_REQUEST['company_id']))
        ) {
            return array(CONTROLLER_STATUS_DENIED);
        }

        $discussService = container()->get('app.discuss_service');
        $discuss = $discussService->getDiscussClient();
        $discussion_id = filter_var($_REQUEST["discussion_id"], FILTER_SANITIZE_NUMBER_INT);
        $discussion = null;

        if (!$discussion_id) {
            try {
                if (isset($_REQUEST["product_id"])) {
                    $product_id = filter_var($_REQUEST["product_id"], FILTER_SANITIZE_NUMBER_INT);

                    if (!$product_id) {
                        return array(CONTROLLER_STATUS_DENIED);
                    }

                    $discussion = $discussService->createDiscussionWithProductSeller($auth['user_id'], $product_id);
                } else {
                    $companyId = filter_var($_REQUEST["company_id"], FILTER_SANITIZE_NUMBER_INT);

                    if (!$companyId) {
                        return array(CONTROLLER_STATUS_DENIED);
                    }

                    $discussion = $discussService->createDiscussionWithCompany($auth['user_id'], $companyId);
                }
            } catch (SenderIsAlsoRecipient $exception) {
                fn_set_notification('E', __('error'), __('discuss_initiator_also_recipient'));
            } catch (CompanyHasNoAdministrator $exception) {
                fn_set_notification('E', __('error'), __('discuss_no_user_in_company'));
            } catch (NotFound $exception) {
                // ignore it, $discussion is null => show 403 error
            }

            if ($discussion !== null) {
                $discuss->getDiscussionRepository()->save($discussion);
            }
        } else {
            $discussion = $discuss->getDiscussionRepository()->get($discussion_id);
        }

        if ($discussion === null || !in_array($auth['user_id'], [$discussion->getInitiator(), $discussion->getRecipient()])) {
            return array(CONTROLLER_STATUS_DENIED);
        }

        $m = $discuss->getMessageRepository()->create();
        $m->setSendDate(new DateTime());
        $m->setDiscussion($discussion);
        $m->setAuthor($auth['user_id']);
        $m->setContent(nl2br($_REQUEST['content']));
        $discuss->getMessageRepository()->save($m);

        return array(CONTROLLER_STATUS_REDIRECT, "discuss.view?discussion_id=" . $discussion->getId());
    }
}

fn_add_breadcrumb(__('discuss_breadcrumbs'));

if ($mode == 'list') {

    $items_per_page = isset($_REQUEST['items_per_page']) ?
        filter_var($_REQUEST['items_per_page'], FILTER_SANITIZE_NUMBER_INT)
        : 10
    ;

    $page = isset($_REQUEST['page']) ?
        filter_var($_REQUEST['page'], FILTER_SANITIZE_NUMBER_INT)
        : 1
    ;

    $discuss = container()->get('app.discuss_service')->getDiscussClient();

    $discussions = $discuss->getDiscussionRepository()->getByUser($auth['user_id'], $items_per_page, $page - 1);

    $interlocutors = [];
    $last_messages = [];

    foreach ($discussions as $discussion) {
        if ($discussion->getInitiator() != $auth['user_id']) {
            $interlocutor = $discussion->getInitiator();
        } else {
            $interlocutor= $discussion->getRecipient();
        }

        $last_messages[$discussion->getId()] = $discuss
            ->getMessageRepository()
            ->getLastOfDiscussion($discussion->getId());
        $interlocutors[$discussion->getId()] = (new \Wizacha\User($interlocutor))->getPseudo()?:$interlocutor;
    }

    $total_items = count($discussions);

    $search = [
                'total_items' => $total_items,
                'page' => $page,
                'items_per_page' => $items_per_page,
                'total_pages' => ceil($total_items/$items_per_page)
              ];

    \Wizacha\Registry::defaultInstance()->get(['view'])->assign('last_messages', $last_messages);
    \Wizacha\Registry::defaultInstance()->get(['view'])->assign('search', $search);
    \Wizacha\Registry::defaultInstance()->get(['view'])->assign('interlocutors', $interlocutors);
    \Wizacha\Registry::defaultInstance()->get(['view'])->assign('discussions', $discussions);

    $templating = container()->get('templating');
    if ($templating->exists('@App/frontend/views/profile/c2c/message_list.html.twig')) {
        $html = $templating->render('@App/frontend/views/profile/c2c/message_list.html.twig', [
            'last_messages' => $last_messages,
            'search' => $search,
            'interlocutors' => $interlocutors,
            'discussions' => $discussions,
        ]);
        return [CONTROLLER_STATUS_OK, null, null, new Response($html)];
    }

} elseif ($mode == 'hide') {
    if (!isset($_REQUEST['discussion_id'])) {
        return [CONTROLLER_STATUS_NO_PAGE];
    }

    $discuss = container()->get('app.discuss_service')->getDiscussClient();
    $discussion = $discuss->getDiscussionRepository()->getIfUser(
        filter_var($_REQUEST["discussion_id"], FILTER_SANITIZE_NUMBER_INT),
        $auth['user_id']
    );

    if (null == $discussion) {
        return [CONTROLLER_STATUS_NO_PAGE];
    }

    $discussion->hideDiscussion($auth['user_id']);
    $discuss->getDiscussionRepository()->save($discussion);

    return array(CONTROLLER_STATUS_REDIRECT, "discuss.list");
} elseif ($mode == 'view') {

    if (!isset($_REQUEST['discussion_id'])) {
        return [CONTROLLER_STATUS_NO_PAGE];
    }

    $discussService = container()->get('app.discuss_service');
    $discuss = $discussService->getDiscussClient();

    $discussion_id = filter_var($_REQUEST["discussion_id"], FILTER_SANITIZE_NUMBER_INT);
    $discuss_product_id = $product_id = isset($_REQUEST["product_id"]) ? filter_var($_REQUEST["product_id"], FILTER_SANITIZE_NUMBER_INT) : 0;
    $companyId = isset($_REQUEST["company_id"]) ? filter_var($_REQUEST["company_id"], FILTER_SANITIZE_NUMBER_INT) : null;

    if ($discussion_id == 0) {
        try {
            if ($product_id) {
                $discussion = $discussService->createDiscussionWithProductSeller($auth['user_id'], $product_id);
            } else {
                if ($companyId) {
                    $discussion = $discussService->createDiscussionWithCompany($auth['user_id'], $companyId);
                }
            }
        } catch (SenderIsAlsoRecipient $exception) {
            fn_set_notification('E', __('error'), __('discuss_initiator_also_recipient'));
        } catch (CompanyHasNoAdministrator $exception) {
            fn_set_notification('E', __('error'), __('discuss_no_user_in_company'));
        } catch (NotFound $exception) {
            // ignore it, $discussion is null => show 403 error
        }

        if (!isset($discussion)) {
            return array(CONTROLLER_STATUS_DENIED);
        }
    } else {
        $discussion = $discuss->getDiscussionRepository()->getIfUser(
            $discussion_id,
            $auth['user_id']
        );

        if (null == $discussion) {
            return [CONTROLLER_STATUS_NO_PAGE];
        }
        $discuss_product_id = $discussion->getMetaData('product_id');
    }

    $messages = $discuss->getMessageRepository()->getByDiscussion($_REQUEST['discussion_id']);

    foreach ($messages as $message) {
        if ($message->getAuthor() != $auth['user_id']) {
            $message->setAsRead();
            $discuss->getMessageRepository()->save($message);
        }
    }

    \Wizacha\Registry::defaultInstance()->get(['view'])->assign('company_id', $companyId);
    \Wizacha\Registry::defaultInstance()->get(['view'])->assign('product_id', $product_id);
    \Wizacha\Registry::defaultInstance()->get(['view'])->assign('discussion', $discussion);
    \Wizacha\Registry::defaultInstance()->get(['view'])->assign('messages', $messages);
    if ($discuss_product_id) {
        \Wizacha\Registry::defaultInstance()->get(['view'])->assign('product_url',
            fn_url('products.view&product_id=' . $discuss_product_id));
    }

    /** @var Core $smarty */
    $smarty = Registry::get('view');
    $templating = container()->get('templating');

    if ($templating->exists('@App/frontend/views/profile/c2c/message.html.twig')) {
        $html = $templating->render('@App/frontend/views/profile/c2c/message.html.twig', [
            'product_id' => $product_id,
            'company_id' => $companyId,
            'discussion' => $discussion,
            'product_url' => $discuss_product_id ? fn_url('products.view&product_id='.$discuss_product_id) : null,
            'messages' => $messages,
        ]);
        return [CONTROLLER_STATUS_OK, null, null, new Response($html)];
    }
}
