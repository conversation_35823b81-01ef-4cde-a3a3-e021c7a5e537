<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON> Shalnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Symfony\Component\HttpFoundation\Response;
use Tygh\Registry;
use Tygh\SmartyEngine\Core;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Order\OrderStatus;
use function Wizacha\Marketplace\Order\is_order_status_equal_to;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

if (!empty($_REQUEST['order_id']) && $mode != 'search') {
    // If user is not logged in and trying to see the order, redirect him to login form
    if (empty($auth['user_id']) && empty($auth['order_ids'])) {
        return array(CONTROLLER_STATUS_REDIRECT, "auth.login_form?return_url=" . urlencode(Registry::get('config.current_url')));
    }

    $orders_company_condition = '';

    if (!empty($auth['user_id'])) {
        $allowed_id = db_get_field("SELECT user_id FROM ?:orders WHERE user_id = ?i AND order_id = ?i $orders_company_condition", $auth['user_id'], $_REQUEST['order_id']);

    } elseif (!empty($auth['order_ids'])) {
        $allowed_id = in_array($_REQUEST['order_id'], $auth['order_ids']);
    }

    // Check order status (incompleted order)
    if (!empty($allowed_id)) {
        $status = db_get_field("SELECT status FROM ?:orders WHERE order_id = ?i $orders_company_condition", $_REQUEST['order_id']);
        if ($status == STATUS_INCOMPLETED_ORDER) {
            $allowed_id = 0;
        }
    }

    if (empty($allowed_id)) { // Access denied
        return array(CONTROLLER_STATUS_DENIED);
    }
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    return array(CONTROLLER_STATUS_OK, "orders.details?order_id=$_REQUEST[order_id]");
}

fn_add_breadcrumb(__('orders'), $mode == 'search' ? '' : "orders.search");

//
// Show invoice
//
if ($mode == 'invoice') {
    fn_add_breadcrumb(__('order') . ' #' . $_REQUEST['order_id'], "orders.details?order_id=$_REQUEST[order_id]");
    fn_add_breadcrumb(__('invoice'));

    $order_info = container()
        ->get('marketplace.order.order_service')
        ->overrideLegacyOrder($_REQUEST['order_id']);

    Registry::get('view')->assign('order_info', $order_info);

//
// Show invoice on separate page
//
} elseif ($mode == 'print_invoice') {

    if (!empty($_REQUEST['order_id'])) {
        return fn_print_order_invoices($_REQUEST['order_id'], !empty($_REQUEST['format']) && $_REQUEST['format'] == 'pdf');
    }
    exit;

//
// Track orders by ekey
//
} elseif ($mode == 'track') {
    if (!empty($_REQUEST['ekey'])) {
        $email = db_get_field("SELECT object_string FROM ?:ekeys WHERE object_type = 'T' AND ekey = ?s AND ttl > ?i", $_REQUEST['ekey'], TIME);

        // Cleanup keys
        db_query("DELETE FROM ?:ekeys WHERE object_type = 'T' AND ttl < ?i", TIME);

        if (empty($email)) {
            return array(CONTROLLER_STATUS_DENIED);
        }

        $auth['order_ids'] = db_get_fields("SELECT order_id FROM ?:orders WHERE email = ?s", $email);

        if (!empty($_REQUEST['o_id']) && in_array($_REQUEST['o_id'], $auth['order_ids'])) {
            return array(CONTROLLER_STATUS_REDIRECT, "orders.details?order_id=$_REQUEST[o_id]");
        } else {
            return array(CONTROLLER_STATUS_REDIRECT, "orders.search");
        }
    } else {
        return array(CONTROLLER_STATUS_DENIED);
    }

    exit;

//
// Request for order tracking
//
} elseif ($mode == 'track_request') {
    $condition = fn_get_company_condition('?:orders.company_id');

    if (!empty($auth['user_id'])) {

        $allowed_id = db_get_field(
            'SELECT user_id '
            . 'FROM ?:orders '
            . 'WHERE user_id = ?i AND order_id = ?i AND is_parent_order != ?s' . $condition,
            $auth['user_id'], $_REQUEST['track_data'], 'Y'
        );

        if (!empty($allowed_id)) {
            Registry::get('ajax')->assign('force_redirection', fn_url('orders.details?order_id=' . $_REQUEST['track_data']));
            exit;
        } else {
            fn_set_notification('E', __('error'), __('warning_track_orders_not_allowed'));
        }
    } else {
        $email = '';

        if (!empty($_REQUEST['track_data'])) {
            $o_id = 0;
            // If track by email
            if (strpos($_REQUEST['track_data'], '@') !== false) {
                $order_info = db_get_row("SELECT order_id, email, company_id, lang_code FROM ?:orders WHERE email = ?s $condition ORDER BY timestamp DESC LIMIT 1", $_REQUEST['track_data']);
            // Assume that this is order number
            } else {
                $order_info = db_get_row("SELECT order_id, email, company_id, lang_code FROM ?:orders WHERE order_id = ?i $condition", $_REQUEST['track_data']);
            }
        }

        if (!empty($order_info['email'])) {
            // Create access key
            $ekey_data = array (
                'object_string' => $order_info['email'],
                'object_type' => 'T',
                'ekey' => md5(uniqid(rand())),
                'ttl' => strtotime("+1 hour"), // FIXME!!! hardcoded
            );

            db_query("REPLACE INTO ?:ekeys ?e", $ekey_data);

            $company_id = fn_get_company_id('orders', 'order_id', $order_info['order_id']);
        } else {
            fn_set_notification('E', __('error'), __('warning_track_orders_not_found'));
        }
    }

    return array(CONTROLLER_STATUS_OK, $_REQUEST['return_url']);
//
// Show order details
//
} elseif ($mode == 'details') {

    fn_add_breadcrumb(__('order_info'));

    $order_info = container()
        ->get('marketplace.order.order_service')
        ->overrideLegacyOrder($_REQUEST['order_id']);

    if (empty($order_info)) {
        return array(CONTROLLER_STATUS_NO_PAGE);
    }

    if ($order_info['is_parent_order'] == 'Y') {
        $child_ids = db_get_fields("SELECT order_id FROM ?:orders WHERE parent_order_id = ?i", $_REQUEST['order_id']);
        return array(CONTROLLER_STATUS_REDIRECT, "orders.search?period=A&order_id=" . implode(',', $child_ids));
    }

    Registry::get('view')->assign('take_surcharge_from_vendor', fn_take_payment_surcharge_from_vendor());
    $statuses = fn_get_statuses(STATUSES_ORDER, array(), true);

    $navigation_tabs = array(
        'general' => array(
            'title' => __('general'),
            'js' => true,
            'href' => 'orders.details?order_id=' . $_REQUEST['order_id'] . '&selected_section=general'
        ),
    );

    list($shipments) = fn_get_shipments_info(array('order_id' => $order_info['order_id'], 'advanced_info' => true));
    $use_shipments = !fn_one_full_shipped($shipments);

    if (Registry::get('settings.General.use_shipments') == 'Y' || $use_shipments) {
        $navigation_tabs['shipment_info'] = array(
            'title' => __('shipment_info'),
            'js' => true,
            'href' => 'orders.details?order_id=' . $_REQUEST['order_id'] . '&selected_section=shipment_info'
        );
        $use_shipments = true;
    }

    $hide_invoice = \Wizacha\Company::isPrivateIndividual(fn_get_company_data($order_info['company_id'])) ||
                    !is_order_status_equal_to($order_info['order_id'], OrderStatus::PROCESSED, OrderStatus::COMPLETED);

    Registry::get('view')->assign('hide_invoice', $hide_invoice);
    Registry::get('view')->assign('shipments', $shipments);
    Registry::get('view')->assign('use_shipments', $use_shipments);

    $shipping_id = $order_info['shipping_ids']?:reset($order_info['shipping'])['shipping_id'];
    Registry::get('view')->assign('order_require_code', \Wizacha\Shipping::requiredCode($shipping_id));

    Registry::set('navigation.tabs', $navigation_tabs);
    Registry::get('view')->assign('order_info', $order_info);
    Registry::get('view')->assign('status_settings', $statuses[$order_info['status']]['params']);

    if (!empty($_REQUEST['selected_section'])) {
        Registry::get('view')->assign('selected_section', $_REQUEST['selected_section']);
    }

    if (!empty($_REQUEST['active_tab'])) {
        Registry::get('view')->assign('active_tab', $_REQUEST['active_tab']);
    }

    /** @var Core $smarty */
    $smarty = Registry::get('view');
    $templating = container()->get('templating');
    if ($templating->exists('@App/frontend/views/profile/order_detail.html.twig')) {
        $params = [];
        if (!empty($auth['user_id'])) {
            $params['user_id'] = $auth['user_id'];
        } else {
            return array(CONTROLLER_STATUS_REDIRECT, "auth.login_form?return_url=" . urlencode(Registry::get('config.current_url')));
        }
        list($orders) = fn_get_orders($params);
        foreach ($orders as &$order) {
            $order['company_name'] = fn_get_company_name($order['company_id']);
        }
        list($shipment) = fn_get_shipments_info([
            'order_id' => $order_info['order_id'],
            'advanced_info' => true,
        ]);
        $html = $templating->render('@App/frontend/views/profile/order_detail.html.twig', [
            'orders' => $orders,
            'order' => $order_info,
            'shipment' => $shipment,
        ]);
        return [CONTROLLER_STATUS_OK, null, null, new Response($html)];
    }

//
// Search orders
//
} elseif ($mode == 'search') {

    $params = $_REQUEST;
    if (!empty($auth['user_id'])) {
        $params['user_id'] = $auth['user_id'];

    } elseif (!empty($auth['order_ids'])) {
        if (empty($params['order_id'])) {
            $params['order_id'] = $auth['order_ids'];
        } else {
            $ord_ids = is_array($params['order_id']) ? $params['order_id'] : explode(',', $params['order_id']);
            $params['order_id'] = array_intersect($ord_ids, $auth['order_ids']);
        }

    } else {
        return array(CONTROLLER_STATUS_REDIRECT, "auth.login_form?return_url=" . urlencode(Registry::get('config.current_url')));
    }

    list($orders, $search) = fn_get_orders($params, Registry::get('settings.Appearance.orders_per_page'));

    Registry::get('view')->assign('orders', $orders);
    Registry::get('view')->assign('search', $search);

    /** @var Core $smarty */
    $smarty = Registry::get('view');
    $templating = container()->get('templating');
    if ($templating->exists('@App/frontend/views/profile/orders.html.twig')) {
        list($orders) = fn_get_orders($params);
        foreach ($orders as &$order) {
            $order['company_name'] = fn_get_company_name($order['company_id']);
        }
        $html = $templating->render('@App/frontend/views/profile/orders.html.twig', [
            'orders' => $orders,
        ]);
        return [CONTROLLER_STATUS_OK, null, null, new Response($html)];
    }

//
// Reorder order
//
} elseif ($mode == 'reorder') {

    fn_reorder($_REQUEST['order_id'], $_SESSION['cart'], $auth);

    return array(CONTROLLER_STATUS_REDIRECT, "checkout.cart");

} elseif ($mode == 'downloads') {

    if (empty($auth['user_id']) && empty($auth['order_ids'])) {
        return array(CONTROLLER_STATUS_REDIRECT, fn_url());
    }

    fn_add_breadcrumb(__('downloads'));

    $params = $_REQUEST;
    $params['user_id'] = $auth['user_id'];
    $params['order_ids'] = !empty($auth['order_ids']) ? $auth['order_ids'] : array();

    list($products, $search) = fn_get_user_edp($params, Registry::get('settings.Appearance.elements_per_page'));

    Registry::get('view')->assign('products', $products);
    Registry::get('view')->assign('search', $search);

} elseif ($mode == 'order_downloads') {

    if (empty($auth['user_id']) && empty($auth['order_ids'])) {
        return array(CONTROLLER_STATUS_REDIRECT, fn_url());
    }

    if (!empty($_REQUEST['order_id'])) {
        if (empty($auth['user_id']) && !in_array($_REQUEST['order_id'], $auth['order_ids'])) {
            return array(CONTROLLER_STATUS_DENIED);
        }
        $orders_company_condition = '';

        $order = db_get_row("SELECT user_id, order_id FROM ?:orders WHERE ?:orders.order_id = ?i AND is_parent_order != 'Y' $orders_company_condition", $_REQUEST['order_id']);

        if (empty($order)) {
            return array(CONTROLLER_STATUS_NO_PAGE);
        }

        fn_add_breadcrumb(__('order') . ' #' . $_REQUEST['order_id'], "orders.details?order_id=" . $_REQUEST['order_id']);
        fn_add_breadcrumb(__('downloads'));

        $params = array(
            'user_id' => $order['user_id'],
            'order_id' => $order['order_id']
        );
        list($products) = fn_get_user_edp($params);

        Registry::get('view')->assign('products', $products);
    } else {
        return array(CONTROLLER_STATUS_NO_PAGE);
    }

} elseif ($mode == 'get_file') {

    if (empty($_REQUEST['file_id']) || (empty($_REQUEST['ekey']) && empty($_REQUEST['preview']))) {
        return array(CONTROLLER_STATUS_NO_PAGE);
    }

    $response = fn_get_product_file($_REQUEST['file_id'], !empty($_REQUEST['preview']), $_REQUEST['ekey']);
    if ($response == false) {
        return array(CONTROLLER_STATUS_DENIED);
    } else {
        return $response;
    }

//
// Display list of files for downloadable product
//
} elseif ($mode == 'download') {
    if (!empty($_REQUEST['ekey'])) {

        $ekey_info = fn_get_product_edp_info($_REQUEST['product_id'], $_REQUEST['ekey']);

        if (empty($ekey_info)) {
            return array(CONTROLLER_STATUS_DENIED);
        }

        $product = array(
            'ekey' => $_REQUEST['ekey'],
            'product_id' => $ekey_info['product_id'],
        );

        if (!empty($product['product_id'])) {
            $product['product'] = db_get_field("SELECT product FROM ?:product_descriptions WHERE product_id = ?i AND lang_code = ?s", $product['product_id'], (string) GlobalState::interfaceLocale());
            $params = array (
                'product_id' => $product['product_id'],
                'order_id' => $ekey_info['order_id']
            );
            $product['files'] = fn_get_product_files($params);
        }
    }

    if (!empty($auth['user_id'])) {
        fn_add_breadcrumb(__('downloads'), "profiles.downloads");
    }

    fn_add_breadcrumb($product['product'], "products.view?product_id=$product[product_id]");
    fn_add_breadcrumb(__('download'));

    if (!empty($product['files'])) {
        Registry::get('view')->assign('product', $product);
    } else {
        return array(CONTROLLER_STATUS_DENIED);
    }

} elseif ($mode == 'get_custom_file') {
    $filename = !empty($_REQUEST['filename']) ? $_REQUEST['filename'] : '';

    if (!empty($_REQUEST['file'])) {
        if (!empty($_REQUEST['order_id'])) {
            $file_path = 'order_data/' . $_REQUEST['order_id'] . '/' . fn_basename($_REQUEST['file']);
        } else {
            $file_path = 'sess_data/' . fn_basename($_REQUEST['file']);
        }

        $customFilesStorageService = container()->get('Wizacha\Storage\CustomFilesStorageService');

        if ($customFilesStorageService->isExist($file_path)) {
            return $customFilesStorageService->get($file_path, $filename);
        }
    }
}

function fn_reorder($order_id, &$cart, &$auth)
{
    $order_info = container()
        ->get('marketplace.order.order_service')
        ->overrideLegacyOrder($order_id, false, false, false, true);
    unset($_SESSION['shipping_hash']);
    unset($_SESSION['edit_step']);

    foreach ($order_info['products'] as $k => $item) {
        // refresh company id
        $company_id = db_get_field("SELECT company_id FROM ?:products WHERE product_id = ?i", $item['product_id']);
        $order_info['products'][$k]['company_id'] = $company_id;

        unset($order_info['products'][$k]['extra']['ekey_info']);
        $order_info['products'][$k]['product_options'] = empty($order_info['products'][$k]['extra']['product_options']) ? array() : $order_info['products'][$k]['extra']['product_options'];
        $order_info['products'][$k]['main_pair'] = fn_get_cart_product_icon($item['product_id'], $order_info['products'][$k]);
    }

    if (!empty($cart) && !empty($cart['products'])) {
        $cart['products'] = fn_array_merge($cart['products'], $order_info['products']);
    } else {
        $cart['products'] = $order_info['products'];
    }

    foreach ($cart['products'] as $k => $v) {
        $_is_edp = db_get_field("SELECT is_edp FROM ?:products WHERE product_id = ?i", $v['product_id']);
        if ($amount = fn_check_amount_in_stock($v['product_id'], $v['amount'], $v['product_options'], $k, $_is_edp, 0, $cart)) {
            $cart['products'][$k]['amount'] = $amount;

            // Change the path of custom files
            if (!empty($v['extra']['custom_files'])) {
                foreach ($v['extra']['custom_files'] as $option_id => $_data) {
                    if (!empty($_data)) {
                        foreach ($_data as $file_id => $file) {
                            $cart['products'][$k]['extra']['custom_files'][$option_id][$file_id]['path'] = 'sess_data/' . fn_basename($file['path']);
                        }
                    }
                }
            }
        } else {
            unset($cart['products'][$k]);
        }
    }

    // Restore custom files for editing
    $dir_path = 'order_data/' . $order_id;

    $customFilesStorageService = container()->get('Wizacha\Storage\CustomFilesStorageService');

    if ($customFilesStorageService->isExist($dir_path)) {
        $customFilesStorageService->copy($dir_path, 'sess_data');
    }

    // Redirect customer to step three after reordering
    $cart['payment_updated'] = true;

    fn_save_cart_content($cart, $auth['user_id']);
}

if ('search' == $mode) {
    $orders = Tygh\Registry::get('view')->getTemplateVars('orders');
    $w_orders = array_reduce(
        $orders,
        function(&$result, $order) {
            if (is_order_status_equal_to($order['order_id'],
                OrderStatus::BILLING_FAILED,
                OrderStatus::CANCELED,
                OrderStatus::COMPLETED,
                OrderStatus::PROCESSED,
                OrderStatus::REFUNDED,
                OrderStatus::UNPAID
            )) {
                $result[__('w_ended_orders')][] = $order;
            } else {
                $result[__('w_processing_orders')][] = $order;
            }

            return $result;
        },
        [__('w_processing_orders') => [],__('w_ended_orders') => []]
    );
    Tygh\Registry::get('view')->assign('w_orders', $w_orders);
}elseif ('details' == $mode) {
    $order_info = Tygh\Registry::get('view')->getTemplateVars('order_info');

    $params['user_id'] = $auth['user_id'];
    $params['order_ids'] = $order_info['order_id'];

    list($products, $search) = fn_get_user_edp($params);
    Tygh\Registry::get('view')->assign('edp_products', $products);
}

