<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */
declare(strict_types=1);

use Tygh\Registry;
use Wizacha\Marketplace\Country\CountryService;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\CreditCard\CreditCard;
use Wizacha\Marketplace\CreditCard\Repository\CreditCardRepository;
use Wizacha\Marketplace\CreditCard\Service\CreditCardService;
use Wizacha\Marketplace\Subscription\Event\SubscriptionAutoRenewEvent;
use Wizacha\Marketplace\Subscription\Event\SubscriptionStatusEvent;
use Wizacha\Marketplace\Subscription\Log\SubscriptionEventType;
use Wizacha\Marketplace\Subscription\Subscription;
use Wizacha\Marketplace\Subscription\SubscriptionFrequency;
use Wizacha\Marketplace\Subscription\Log\SubscriptionActionTraceRepository;
use Wizacha\Marketplace\Subscription\SubscriptionRepository;
use Wizacha\Marketplace\Subscription\SubscriptionService;
use Wizacha\Marketplace\Subscription\SubscriptionSortBy;
use Wizacha\Marketplace\Subscription\SubscriptionStatus;
use Wizacha\Marketplace\User\UserType;

if (defined('BOOTSTRAP') === false) {
    die('Access denied.');
}

$params = $_REQUEST;
$subscriptionRepository = container()->get(SubscriptionRepository::class);
$subscriptionService = container()->get(SubscriptionService::class);
$userService = container()->get('marketplace.user.user_service');
$creditCardRepository = container()->get(CreditCardRepository::class);
$creditCardService = container()->get(CreditCardService::class);
$paymentService = container()->get('marketplace.payment.payment_service');
$subscriptionActionTraceRepository = container()->get(SubscriptionActionTraceRepository::class);
$eventDispatcher = container()->get('event_dispatcher');
$orderService = container()->get('marketplace.order.order_service');

// API auth info
$userId = $_SESSION['auth']['user_id'];
$userData = fn_get_user_info($userId, false);
Registry::get('view')->assign('user_api_key', $userData['api_key']);
Registry::get('view')->assign('user_email', $userData['email']);
$subscriptionId = \array_key_exists('subscription_id', $_REQUEST) ? $_REQUEST['subscription_id'] : null;
Registry::get('view')->assign('subscription_id', $subscriptionId);

$statusValues = SubscriptionStatus::values();
Registry::get('view')->assign('status', $statusValues);

$companyId = Registry::get('runtime.company_id');
Registry::get('view')->assign('companyId', $companyId);


/**
 * @var array $auth
 */
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($mode === 'renew') {
        $subscriptionId = $request->request->has('subscriptionId') ? $request->request->get('subscriptionId') : null;
        if (\is_string($subscriptionId) && \strlen($subscriptionId) > 0) {

            $subscription = $subscriptionService->get($request->request->get('subscriptionId'));

            if ($subscription instanceof Subscription === true) {
                try {
                    $subscriptionService->renew($subscription, true);
                    fn_set_notification('N', __('notice'), __('subscription_renewed'));
                } catch (\Exception $exception) {
                    fn_set_notification('E', __('error'), __('subscription_renew_error'));

                    return [CONTROLLER_STATUS_OK, 'subscriptions.update&subscription_id=' . $subscriptionId];
                }

                return [CONTROLLER_STATUS_OK, 'subscriptions.update&subscription_id=' . $subscriptionId];
            } else {
                fn_set_notification('E', __('error'), __('subscription_not_found'));

                return [CONTROLLER_STATUS_OK, 'subscriptions.manage'];
            }
        }
    }

    $subId = $_POST['subscriptionId'];

    if ($mode === 'update') {
        $status = $request->request->get('status');
        $subscription = $subscriptionRepository->findOneById($subId);
        $loggedUser = $userService->get($auth['user_id']);

        if ($request->request->has('status')) {
            $newSubscriptionStatus = new SubscriptionStatus($status);
            if ($companyId > 0
                && SubscriptionStatus::isValid($status)
                && SubscriptionStatus::SUSPENDED()->equals($newSubscriptionStatus)
            ) {
                fn_set_notification('E', __('error'), __('subscription_update_status_error'));

                return [CONTROLLER_STATUS_OK, 'subscriptions.update&subscription_id='.$subId];
            }

            $subscriptionService->updateSubscriptionStatus($subscription, $newSubscriptionStatus);
        }

        $oldValueOfAutoRenew = $subscription->isAutoRenew();
        $newValueOfAutoRenew = $request->request->has('autorenew');
        $subscription->setIsAutoRenew($newValueOfAutoRenew);

        if ($request->request->has('quantity')
            && (
                SubscriptionStatus::ACTIVE()->equals($subscription->getStatus())
                || SubscriptionStatus::WAITING_RENEW()->equals($subscription->getStatus())
            )
        ) {
            $quantity = (int) $request->request->get('quantity');
            if ($quantity <= 0) {
                fn_set_notification('E', __('error'), __('subscription_update_quantity_error'));

                return [CONTROLLER_STATUS_OK, 'subscriptions.update&subscription_id='.$subId];
            }

            $subscription = $subscriptionService->updateQuantity($subscription, $quantity);
        }

        if ($request->request->has('nextPaymentAt')) {
            $nextPaymentAt = $request->request->get('nextPaymentAt');

            if (\in_array($subscription->getStatus(), [
                SubscriptionStatus::ACTIVE(),
                SubscriptionStatus::DEFAULTED(),
                SubscriptionStatus::WAITING_RENEW(),
            ])) {
                $errors = $subscriptionService->checkNextPaymentAt($subscription, $nextPaymentAt, 'd/m/Y');

                if (\count($errors) > 0) {
                    fn_set_notification('E', __('error'), __('subscription_error_next_payment_at'));

                    return [CONTROLLER_STATUS_OK, 'subscriptions.update&subscription_id='.$subId];
                } else {
                    $subscription->setNextPaymentAt(\DateTimeImmutable::createFromFormat('d/m/Y', $nextPaymentAt));
                }
            } elseif (\DateTimeImmutable::createFromFormat(
                    'd/m/Y',
                    $nextPaymentAt
                )->format('Y-m-d') !== $subscription->getNextPaymentAt()->format('Y-m-d')
            ) {
                fn_set_notification('E', __('error'), __('subscription_error_next_payment_at_not_active'));
            }
        }

        if ($request->request->has('commitmentEndAt')) {
            $commitmentEndAt = $request->request->get('commitmentEndAt');

            if (\in_array($subscription->getStatus(), [
                    SubscriptionStatus::ACTIVE(),
                    SubscriptionStatus::DEFAULTED(),
                    SubscriptionStatus::WAITING_RENEW(),
                ])
            ) {
                $errors = $subscriptionService->checkCommitmentEndAt($subscription, $commitmentEndAt, 'd/m/Y');

                if (\count($errors) > 0) {
                    fn_set_notification('E', __('error'), __('subscription_error_commitment_end_at'));

                    return [CONTROLLER_STATUS_OK, 'subscriptions.update&subscription_id=' . $subId];
                } else {
                    $subscription->setCommitmentEndAt(\DateTimeImmutable::createFromFormat('d/m/Y', $commitmentEndAt));
                }
            } elseif (\DateTimeImmutable::createFromFormat(
                    'd/m/Y',
                    $commitmentEndAt
                )->format('Y-m-d') !== $subscription->getCommitmentEndAt()->format('Y-m-d')
            ) {
                fn_set_notification('E', __('error'), __('subscription_error_commitment_end_at_not_active'));
            }
        }

        $subscriptionRepository->save($subscription);

        if ($oldValueOfAutoRenew !== $newValueOfAutoRenew) {
            $eventDispatcher->dispatch(
                new SubscriptionAutoRenewEvent($subscription,
                    $loggedUser,
                    $oldValueOfAutoRenew,
                    $newValueOfAutoRenew
                ),
                SubscriptionEventType::AUTO_RENEW_UPDATED()->getValue()
            );
        }

        return [CONTROLLER_STATUS_OK, 'subscriptions.update&subscription_id='.$subId];

    } elseif ($mode === 'renew_credit_card') {
        $subscription = $subscriptionRepository->findOneById($subId);

        try {
            $pspRenew = $creditCardService->renew(
                $subscription->getUser(),
                (int) $request->request->get('paymentMethod'),
                fn_url('subscriptions.update&subscription_id='.$subId)
            );
        } catch (\Throwable $exception) {
            container()->get('logger')->error('Back office renew credit card.', [
                'exception' => $exception,
                'data' => [
                    'subscriptionId' => $subId,
                    'paymentId' => $request->request->get('paymentMethod'),
                    'subscription' => $subscription->jsonSerialize(),
                ]
            ]);

            fn_set_notification('E', __('error'), __('error_renew_credit_card'));

            return [CONTROLLER_STATUS_OK, 'subscriptions.update&subscription_id='.$subId];
        }

        header("Location: " . $pspRenew);
        exit();
    }
}

if ($mode === 'manage') {
    // Pagination
    $params['items_per_page'] = $params['items_per_page'] ?? Registry::get('settings.Appearance.admin_pages_per_page');
    $params['page'] = max(intval($params['page']), 1);
    $start = $params['page'] > 0 ? ($params['page'] - 1) * $params['items_per_page'] : 0;
    $company = null;
    $params['sort_order'] = $params['sort_order'] ?? 'DESC';

    if ($companyId > 0) {
        $company = container()->get(CompanyService::class)->get($companyId);
    }

    if (\is_null($company)) {
        [$items, $totalCount] = $subscriptionRepository->findByFilters((int) $params['items_per_page'], (int) $start, $params, $params['sort_by'], $params['sort_order']);
    } else {
        [$items, $totalCount] = $subscriptionRepository->findByCompany($company, (int) $params['items_per_page'], (int) $start, $params, $params['sort_by'], $params['sort_order']);
    }

    $params['total_items'] = $totalCount;

    $users = [];
    $subscriptions = [];

    foreach ($items as $subscription) {
        $expose = $subscription->expose();
        $users[$expose['userId']] = $expose['userId'];
        $expose['frequency'] = SubscriptionFrequency::search($expose['paymentFrequency']);
        $expose['lastOrderId'] = $subscriptionRepository->getLastOrderFromSubscriptionId($subscription->getId())['order_id'];
        if ($expose['lastOrderId'] === null) {
            $expose['lastOrderId'] = $subscriptionRepository->getFirstOrderFromSubscriptionId($subscription->getId())['order_id'];
        }
        $subscriptions[] = $expose;
    }

    foreach ($users as $userId => $user) {
        $users[$userId] = $userService->get($userId)->getFullName();
    }

    Registry::get('view')->assign('subscriptions', $subscriptions);
    Registry::get('view')->assign('userFullname', $users);
    Registry::get('view')->assign('search', $params);
    Registry::get('view')->assign('sort_by', SubscriptionSortBy::values());
    Registry::get('view')->assign('sort_order', ['desc', 'asc']);
} elseif ($mode === 'update') {
    $user = $userService->get($userId);
    Registry::get('view')->assign('isAdmin', $user->getUserType() === UserType::ADMIN && !Registry::get('runtime.company_id'));

    // [Section general]
    // Get current subscription data
    $subscription = $subscriptionRepository->findOneById($subscriptionId);
    Registry::get('view')->assign('subscriptionData', $subscription->expose());
    Registry::get('view')->assign('statusActive', SubscriptionStatus::ACTIVE()->getValue());
    Registry::get('view')->assign('isRenewable', \in_array($subscription->getStatus(), [
            SubscriptionStatus::ACTIVE()->getValue(),
            SubscriptionStatus::DEFAULTED()->getValue(),
            SubscriptionStatus::WAITING_RENEW()->getValue(),
        ])
    );
    Registry::get('view')->assign('nextPaymentAt', $subscription->getNextPaymentAt()->format('d/m/Y'));
    Registry::get('view')->assign('commitmentEndAt', $subscription->getCommitmentEndAt()->format('d/m/Y'));

    $user = $subscription->getUser();
    $orders = $subscriptionRepository->getSubsequentOrdersFromSubscriptionId($subscriptionId)->getResults();
    array_unshift($orders, $subscriptionRepository->getFirstOrderFromSubscriptionId($subscriptionId));
    $lastOrder = end($orders);
    $lastPaymentStatus = $lastOrder['is_paid'];
    Registry::get('view')->assign('lastPaymentStatus', $lastPaymentStatus);
    Registry::get('view')->assign('lastPaymentDate', $lastOrder['timestamp']);

    $orderItemsTaxes = [];

    foreach ($subscription->getOrderItems() as $item) {
        foreach ($item->getTaxItems() as $tax) {
            $orderItemsTaxes[$item->getId()][$tax->getId()] = $tax->jsonSerialize();
        }
    }

    Registry::get('view')->assign('orderItemsTaxes', $orderItemsTaxes);

    $companyName = container()->get(CompanyService::class)->get($subscription->getCompanyId())->getName();
    Registry::get('view')->assign('companyName', $companyName);

    $countryCode = $userService
        ->get($user->getUserId())
        ->getBillingAddress()
        ->getFieldValue('country')
    ;

    $country = container()->get(CountryService::class)->getCountryName($countryCode, $user->getLangCode());

    if ($companyId > 0) {
        unset($statusValues['SUSPENDED']);
        Registry::get('view')->assign('status', $statusValues);
    }

    $userProfile = [
        'id' => $user->getUserId(),
        'fullName' => $user->getFullName(),
        'email' => $user->getEmail(),
        'country' => $country,
        'birthday' => $user->getBirthday(),
        'phone' => $user->getPhone(),
        'langCode' => $user->getLocale(),
        'registeredOn' => $user->getTimestamp(),
    ];

    Registry::get('view')->assign('userProfile', $userProfile);

    // [Section payment history]
    $ordersId = [];

    foreach ($orders as $order) {
        $ordersId[] = $order['order_id'];
    }

    $order_info = $orderService->overrideLegacyOrder($subscription->getFirstOrderId());
    Registry::get('view')->assign('order_info', $order_info);

    if (\count($orders) === 0 || \in_array($subscription->getFirstOrderId(), $ordersId) === false) {
        array_unshift($orders, fn_api_order_info($order_info));
    }

    Registry::get('view')->assign('orders', $orders);

    // [Section payment method]
    if ($subscription->getCreditCard() instanceof CreditCard) {
        Registry::get('view')->assign(
            'creditCard',
            $subscription->getCreditCard()->jsonSerialize()
        );
    }

    $subscriptionActionTraces = [];
    foreach ($subscriptionActionTraceRepository->getLogsFromSubscriptionId($subscriptionId) as $actionsTrace) {
        $subscriptionActionTraces[] = $actionsTrace->jsonSerialize();
    }

    Registry::get('view')->assign(
        'subscriptionActionTraces',
        $subscriptionActionTraces
    );

    $activePayments = $paymentService->getActivePayments();
    $payments = [];

    foreach ($activePayments as $payment) {
        $payments[] = $payment->jsonSerialize();
    }

    Registry::get('view')->assign('payments', $payments);

    // [Section other subscriptions]
    $userSubscriptions = [];
    $activity_data = ['total_subscriptions_count' => 0, 'total_subscriptions_count_active' => 0];
    /** @var \Wizacha\Marketplace\Subscription\Subscription $userSubscription */
    foreach ($subscriptionRepository->findByUser($user)[0] as $userSubscription) {
        $activity_data['total_subscriptions_count']++;
        if (SubscriptionStatus::ACTIVE()->equals($userSubscription->getStatus())
            || SubscriptionStatus::WAITING_RENEW()->equals($userSubscription->getStatus())
        ) {
            $activity_data['total_subscriptions_count_active']++;
        }
        if ($userSubscription->getId() !== $subscriptionId) {
            if (($companyId > 0 && $userSubscription->getCompanyId() === $companyId) || $companyId === 0) {
                $userSubscriptions[] = $userSubscription->expose();
            }
        }
    }
    Registry::get('view')->assign('userSubscriptions', $userSubscriptions);

    [$total_orders] = fn_get_orders(['user_id' => [$user->getUserId()]]);
    $activity_data['total_orders_count'] = \count($total_orders);

    Registry::get('view')->assign('activity_data', $activity_data);

    $isAdmin = container()->get('marketplace.user.user_service')->get($auth['user_id'])->getUserType() === UserType::ADMIN && !Registry::get('runtime.company_id');

    Registry::get('view')->assign('hide_email',
        true === \filter_var(container()->getParameter('feature.hide_client_email_from_vendor'), FILTER_VALIDATE_BOOLEAN)
        && false === $isAdmin
    );

    // [Page sections]
    $tabs = [
        'general' => [
            'title' => __('general'),
            'js' => true,
        ],
        'paymentHistory' => [
            'title' => __('orders'),
            'js' => true,
        ],
        'paymentMethod' => [
            'title' => __('subscription_payment_method'),
            'js' => true,
        ],
        'changeHistory' => [
            'title' => __('subscription_change_history'),
            'js' => true
        ]

    ];

    Registry::set('navigation.tabs', $tabs);
}

