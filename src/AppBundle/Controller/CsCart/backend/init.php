<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON>nev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\BackendMenu;
use Tygh\Navigation\Breadcrumbs;
use Tygh\Registry;
use Wizacha\Marketplace\GlobalState\GlobalState;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

Registry::get('view')->assign('descr_sl', (string) GlobalState::contentLocale());

if (!empty($auth['user_id']) && $auth['area'] != AREA) {
    $auth = array();

    return array(CONTROLLER_STATUS_REDIRECT, fn_url());
}

if (empty($auth['user_id']) && !fn_check_permissions(Registry::get('runtime.controller'), Registry::get('runtime.mode'), 'trusted_controllers')) {
    if (Registry::get('runtime.controller') != 'index') {
        fn_set_notification('E', __('access_denied'), __('error_not_logged'));

        if (defined('AJAX_REQUEST')) {
            Registry::get('ajax')->assign('force_redirection', fn_url('auth.login_form?return_url=' . urlencode(Registry::get('config.current_url'))));
            exit;
        }
    }

    return array(CONTROLLER_STATUS_REDIRECT, "auth.login_form?return_url=" . urlencode(Registry::get('config.current_url')));
} elseif (!empty($auth['user_id']) && !fn_check_user_type_access_rules($auth)) {
    fn_set_notification('E', __('error'), __('error_area_access_denied'));

    return array(CONTROLLER_STATUS_DENIED);
}

// Skip menu generation for POST requests, except those on 'bundle' controller
if ($_SERVER['REQUEST_METHOD'] == 'POST' && $controller != 'bundle') {
    return;
}

list($static, $actions, $selected_items) = BackendMenu::instance(
    Registry::get('runtime.controller'),
    Registry::get('runtime.mode'),
    Registry::get('runtime.action')
)->generate($_REQUEST);

Registry::set('navigation', array(
    'static' => $static,
    'dynamic' => array('actions' => $actions),
    'selected_tab' => $selected_items['section'],
    'subsection' => $selected_items['item']
));

// update request history
// save only current and previous page requests in history
if (!defined('AJAX_REQUEST')) {
    $current_dispatch = Registry::get('runtime.controller') . '.' . Registry::get('runtime.mode');
    if (!empty($_SESSION['request_history']['current']['dispatch'])) {
        $hist_dispatch = !empty($_SESSION['request_history']['current']['dispatch']) ? $_SESSION['request_history']['current']['dispatch'] : '';
        if ($hist_dispatch != $current_dispatch) {
            // replace previously saved reuest if new page is opened
            $_SESSION['request_history']['prev'] = $_SESSION['request_history']['current'];
        }
    }
    $_SESSION['request_history']['current'] = array (
        'dispatch' => $current_dispatch,
        'params' => $_REQUEST
    );
}

// generate breadcrumbs
$prev_request = !empty($_SESSION['request_history']['prev']['params']) ? $_SESSION['request_history']['prev']['params'] : array();
$breadcrumbs = Breadcrumbs::instance(Registry::get('runtime.controller'), Registry::get('runtime.mode'), AREA, $_REQUEST, $prev_request)->getLinks();
Registry::get('view')->assign('breadcrumbs', $breadcrumbs);
