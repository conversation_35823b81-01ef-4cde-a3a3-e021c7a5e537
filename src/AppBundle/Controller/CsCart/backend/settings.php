<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON>halnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\Registry;
use Tygh\Settings;
use Wizacha\Marketplace\GlobalState\GlobalState;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

$section_id = empty($_REQUEST['section_id']) ? 'General' : $_REQUEST['section_id'];
// Convert section name to section_id
$section = Settings::instance()->getSectionByName($section_id);
if (isset($section['section_id'])) {
    $section_id = $section['section_id'];
} else {
    return array(CONTROLLER_STATUS_NO_PAGE);
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {

    fn_trusted_vars('update');
    fn_trusted_vars('sections');
    fn_trusted_vars('settings');
    $_suffix = '';

    if ($mode == 'sections') {
        foreach ($_REQUEST['sections'] as $id => $section) {
            [$object_id, $object_type] = explode('_', $id);

            // Update section
            Settings::instance()->updateSection([
                'section_id' => $object_id,
                'position' => $section['position'],
            ]);

            // And its description
            Settings::instance()->updateDescription([
                'object_id' => $object_id,
                'object_type' => $object_type,
                'value' => $section['title'],
                'lang_code' => (string) GlobalState::contentLocale(),
            ]);
        }

        return [CONTROLLER_STATUS_OK, 'settings.sections'];
    }

    if ($mode == 'descriptions') {
        foreach ($_REQUEST['settings'] as $id => $setting) {

            // Update setting
            Settings::instance()->update(
                [
                    'object_id' => $id,
                    'position' => $setting['position'],
                ], // setting
                null, // variant
                [
                    [
                        'object_id' => $id,
                        'object_type' => Settings::SETTING_DESCRIPTION,
                        'value' => $setting['description'],
                        'lang_code' => (string) GlobalState::contentLocale(),
                    ],
                ] // description
            );
        }

        return [CONTROLLER_STATUS_OK, 'settings.descriptions&section_id=' . Settings::instance()->getSectionTextId($section_id)];
    }

    if ($mode == 'update') {
        if (isset($_REQUEST['update']) && is_array($_REQUEST['update'])) {
            foreach ($_REQUEST['update'] as $k => $v) {
                Settings::instance()->updateValueById($k, $v);

                if (!empty($_REQUEST['update_all_vendors'][$k])) {
                    Settings::instance()->resetAllVendorsSettings($k);
                }
            }
        }
        $_suffix = ".manage";

    }

    return array(CONTROLLER_STATUS_OK, "settings{$_suffix}?section_id=" . Settings::instance()->getSectionTextId($section_id));
}

//
// OUPUT routines
//
if ($mode == 'manage') {
    $subsections = Settings::instance()->getSectionTabs($section_id, (string) GlobalState::interfaceLocale());

    $options = Settings::instance()->getList($section_id);
    $optionsPlaceholder = Settings::instance()->getList($section_id, null, null, null, (string) GlobalState::interfaceLocale());

    // [Page sections]
    if (!empty($subsections)) {
        Registry::set('navigation.tabs.main', array (
            'title' => __('main'),
            'js' => true
        ));
        foreach ($subsections as $k => $v) {
            Registry::set('navigation.tabs.' . $k, array (
                'title' => $v['description'],
                'js' => true
            ));
        }
    }
    // [/Page sections]

    // Set navigation menu
    $sections = Registry::get('navigation.static.top.settings.items');

    Registry::set('navigation.dynamic.sections', $sections);
    Registry::set('navigation.dynamic.active_section', Settings::instance()->getSectionTextId($section_id));

    Registry::get('view')->assign('options', $options);
    Registry::get('view')->assign('optionsPlaceholder', $optionsPlaceholder);
    Registry::get('view')->assign('subsections', $subsections);
    Registry::get('view')->assign('section_id', Settings::instance()->getSectionTextId($section_id));
    Registry::get('view')->assign('settings_title', Settings::instance()->getSectionName($section_id));
}

if ($mode == 'descriptions') {
    $list = Settings::instance()->getList($section_id);
    $listPlaceholder = Settings::instance()->getList($section_id, null, null, null, (string) GlobalState::interfaceLocale());
    Registry::get('view')->assign('settings_list', $list);
    Registry::get('view')->assign('settingsListPlaceholder', $listPlaceholder);
    Registry::get('view')->assign('section_id', Settings::instance()->getSectionTextId($section_id));
    Registry::get('view')->assign('settings_title', Settings::instance()->getSectionName($section_id));
}

if ($mode == 'flags') {
    $parameterBag = container()->getParameterBag();
    $features = [];

    foreach ($parameterBag->all() as $name => $value) {
        if (preg_match('/^feature\./i', $name)) {
            $features[$name] = $value;
        }
    }
    ksort($features);

    Registry::get('view')->assign('feature_flags', $features);
}

if ($mode == 'sections') {
    // get settings sections for content locale
    $sections = Settings::instance()->getCoreSections((string) GlobalState::contentLocale());
    Registry::get('view')->assign('sections', $sections);
}
