<?php

use Symfony\Component\Security\Csrf\CsrfToken;
use Wizacha\Discuss\Entity\Discussion\Status;
use Wizacha\FeatureFlag\FeatureFlagService;
use Wizacha\Marketplace\Company\CompanyRepository;
use Wizacha\Marketplace\MessageAttachment\MessageAttachmentService;
use Wizacha\Marketplace\User\UserService;
use Wizacha\Registry;
use Wizacha\User;

if (!defined('BOOTSTRAP')) {
    die('Access denied');
}

$discussionClient = container()->get('app.discuss_service')->getDiscussClient();
$isDiscussionsAllowedForAdmin = container()->get(FeatureFlagService::class)->get('feature.discussions_allowed_for_admin');
$userService = container()->get(UserService::class);
$companyRepository = container()->get(CompanyRepository::class);

if ($mode == 'list') {
    $userId = $_REQUEST['user_id'] ? intval($_REQUEST['user_id']) : null;

    $companyId = null;
    if (Registry::defaultInstance()->get(['runtime', 'company_id']) > 0) {
        $companyId = Registry::defaultInstance()->get(['runtime', 'company_id']);
    } elseif (\array_key_exists('company_id', $_REQUEST) === true) {
        $companyId = \intval($_REQUEST['company_id']);
    }

    if ($companyId > 0) {
        $userId = \reset(fn_get_users(['company_id' => $companyId], $auth)[0])['user_id'];
    }

    $status = $_REQUEST['status'] ?? new Status(Status::DISPLAYED);
    $items_per_page = $_REQUEST['items_per_page'] ?? 10;
    $page = $_REQUEST['page'] ?? 1;

    if ($companyId > 0) {
        $discussions = $discussionClient->getDiscussionRepository()->getByCompanyIdOrUserId($companyId, $userId);
    } else {
        $discussions = $discussionClient->getDiscussionRepository()->getAllOrderedByMessageSendDate($userId, $status, $items_per_page, $page -1);
    }

    $message_repo = $discussionClient->getMessageRepository();

    $interlocutors = [];
    $last_messages = [];
    $discussionsData = [];

    foreach ($discussions as $discussion) {
        $companyId = $discussion->getMetaData('company_id');
        $initiator = $discussion->getInitiator();
        $initiatorUser = new User($initiator);
        $interlocutors[$initiator] = $initiatorUser->getPseudo() ?: ($initiatorUser->getFullname() ?: __("user_unknown"));

        $recipient = $discussion->getRecipient();
        $recipientUser = new User($recipient);
        $interlocutors[$recipient] = $recipientUser->getPseudo() ?: ($recipientUser->getFullname() ?: __("user_unknown"));

        $lastMessage = $message_repo->getLastOfDiscussion($discussion->getId());
        $lastMessageComment = (null !== $lastMessage) ? $lastMessage->getContent() : '';
        $lastMessageDate = (null !== $lastMessage) ? $lastMessage->getSendDate() : null;

        $discussionsData[$discussion->getId()] = [
            'companyId' => $companyId,
            'initiator' => $initiator,
            'recipient' => $recipient,
            'lastMessageDate' => $lastMessageDate,
            'lastMessageComment' => $lastMessageComment,
            'title' => $discussion->getMetaData('title'),
        ];
    }

    // For pagination block
    $total_items = $discussions->count();
    $search = [
        'total_items' => $total_items,
        'page' => $page,
        'items_per_page' => $items_per_page,
        'total_pages' => ceil($total_items/$items_per_page),
        'user_id' => $userId,
        'company_id' => $companyId,
    ];

    $view = Wizacha\Registry::defaultInstance()->get(['view']);
    $view->assign('search', $search);
    $view->assign('interlocutors', $interlocutors);
    $view->assign('discussions', $discussionsData);
    $view->assign('csrfToken', container()->get('security.csrf.token_manager')->getToken('discussHideLink')->getValue());
    $view->assign('isDiscussionsAllowedForAdmin', $isDiscussionsAllowedForAdmin);
} elseif ($mode == 'view') {
    if (\array_key_exists('discussion_id', $_REQUEST) === false) {
        return [CONTROLLER_STATUS_NO_PAGE];
    }

    $discussion = $discussionClient->getDiscussionRepository()->get($_REQUEST['discussion_id']);
    $companyId = $discussion->getMetaData('company_id');

    if ($companyId === null || $companyId === 0) {
        $recipientCompanyId = $userService->get($discussion->getRecipient())->getCompanyId();
        $companyId = $recipientCompanyId > 0 ? $recipientCompanyId : $userService->get($discussion->getInitiator())->getCompanyId();
    }

    if ($_SERVER['REQUEST_METHOD'] == 'POST' && $isDiscussionsAllowedForAdmin === true) {
        if (container()->get('security.csrf.token_manager')->isTokenValid(new CsrfToken('discussPostMessage', $_REQUEST['token'])) === false) {
            fn_set_notification('E', __('error'), __('invalid_csrf_token'));
            return [CONTROLLER_STATUS_NO_PAGE];
        }

        $message = $discussionClient->getMessageRepository()->create();
        $message->setSendDate(new \DateTime());
        $message->setDiscussion($discussion);
        $message->setAuthor($companyRepository->getFirstAdmin($companyId)['user_id']);
        $message->setContent(nl2br($_REQUEST['content']));
        $discussionClient->getMessageRepository()->save($message);

        $files = $request->files->get('files');
        if (\is_array($files) === true && \count($files) > 0) {
            container()->get('marketplace.message_attachment.message_attachment_service')->create($files, $message->getId());
        }

        if (\array_key_exists('formInOrderDetails', $_REQUEST) === true
            && (bool) $_REQUEST['formInOrderDetails'] === true
        ) {
            return [
                CONTROLLER_STATUS_OK,
                'orders.details&order_id= ' . (int) $discussion->getMetaData('order_id'). '&selected_section=discussion'
            ];
        }

        return [CONTROLLER_STATUS_OK, 'discuss.view&discussion_id=' . $discussion->getId()];
    }

    $messages = $discussionClient->getMessageRepository()->getByDiscussion($discussion->getId());
    $interlocutors = [];
    $isCompany = [];
    foreach ($messages as $message) {
        $author = new User($message->getAuthor());
        $interlocutors[$message->getAuthor()] = $author->getFullname() ?: $message->getAuthor();
        $isCompany[$message->getId()] = (int) $message->getDiscussion()->getMetaData('company_id') ===  $author->getCompanyId();
    }

    $view = Wizacha\Registry::defaultInstance()->get(['view']);
    $view->assign('discussion', $discussion);
    $view->assign('discussionId', $discussion->getId());
    $view->assign('interlocutors', $interlocutors);
    $view->assign('isCompany', $isCompany);
    $view->assign('messages', $messages);
    $view->assign('csrfToken', container()->get('security.csrf.token_manager')->getToken('discussPostMessage'));
    $view->assign('validateExtension', MessageAttachmentService::VALIDATE_ATTACHMENT_MESSAGE_EXTENSIONS);
    $view->assign('isDiscussionsAllowedForAdmin', $isDiscussionsAllowedForAdmin);
    $view->assign('companyName', $companyRepository->getCompanyNameById($companyId?:0));
    $view->assign('formInOrderDetails', false);
} elseif ($mode == 'delete') {
    if (\array_key_exists('discussion_id', $_REQUEST) === false) {
        return [CONTROLLER_STATUS_NO_PAGE];
    }

    if (container()->get('security.csrf.token_manager')->isTokenValid(new CsrfToken('discussHideLink', $_REQUEST['token'])) === false) {
        fn_set_notification('E', __('error'), __('invalid_csrf_token'));
        return [CONTROLLER_STATUS_NO_PAGE];
    }

    $userId = $_SESSION['auth'][0]['user_id'];

    $discussion = $discussionClient->getDiscussionRepository()->get($_REQUEST['discussion_id']);

    $discussion->hideDiscussion($discussion->getInitiator());
    $discussion->hideDiscussion($discussion->getRecipient());
    $discussionClient->getDiscussionRepository()->save($discussion);

    fn_set_notification('N', __('notice'), __('discussion_deleted'));

    return [CONTROLLER_STATUS_REDIRECT, 'discuss.list'];
}
