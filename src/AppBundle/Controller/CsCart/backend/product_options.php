<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON> Shalnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\Registry;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Option;
use Wizacha\Events\IterableEvent;
use Wizacha\Events\Config;
use Wizacha\Marketplace\PIM\Option\OptionRepository;
use Wizacha\Marketplace\PIM\Option\CannotDeleteOptionVariantException;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

fn_define('KEEP_UPLOADED_FILES', true);
if (isset($_REQUEST['product_id']) && !fn_company_products_check($_REQUEST['product_id'])) {
    return [CONTROLLER_STATUS_DENIED];
}
if ($_SERVER['REQUEST_METHOD'] == 'POST') {

    if ($mode == 'exceptions') {
        //Intercept "save and close" button and return the right url
        \Wizacha\Misc::redirectAfterSave('product_options', 'product_options.inventory&product_id=' . $_REQUEST['product_id'], $_REQUEST);

        //Check new variants
        $new_variants = array_filter($_REQUEST['w_edit_variant']?:[]);
        if(!fn_w_company_options_check(array_keys($new_variants))){
            return CONTROLLER_STATUS_DENIED;
        }
        foreach ($new_variants as $option_id => $variant_name) {
            list($variant_id) = fn_w_add_option_variants_by_name($option_id,[$variant_name]);
            if($variant_id) {
                $_REQUEST['w_add_variant'][$option_id][$variant_id] = 1;
            }
        }

        $product_id = $_REQUEST['product_id']?:0;
        $exceptions = array_map('array_filter', $_REQUEST['w_add_variant']?:[]);
        fn_w_update_product_options_authorization($product_id, $exceptions);
        \Wizacha\Events\Config::dispatch(
            \Wizacha\Product::EVENT_UPDATE,
            (new \Wizacha\Events\IterableEvent)->setElement($product_id)
        );
        return array(CONTROLLER_STATUS_REDIRECT, "product_options.exceptions&product_id=" . $_REQUEST['product_id']);
    }

    if ($mode == 'update') {
        fn_trusted_vars('option_data', 'regexp');

        $option_data = array();
        $old_categories_path = '';
        $new_categories_path = $_REQUEST['option_data']['w_categories_path'];
        if (!empty($_REQUEST['option_id'])) {
            $option_data = db_get_row("SELECT * FROM ?:product_options WHERE option_id = ?i", $_REQUEST['option_id']);
            $old_categories_path = $option_data['w_categories_path'];
        }

        if ($old_categories_path != $new_categories_path) {
            $new_categories_path = explode(',', $new_categories_path);
            $old_categories_path = explode(',', $option_data['w_categories_path']);
            $real_categories_path = $new_categories_path;

            $_POST['option_data']['w_categories_path'] = trim(implode(',', $real_categories_path), ',');
        }
    }

    if ($mode == 'vendor_submit') {
        $variants = array_unique(array_filter($_REQUEST['option_data']['variants']?:[]));
        if(!empty($_REQUEST['option_data']['option_name']) && !empty($variants)) {
            $variants = array_map(
                function($name){
                    return ['variant_name' => $name];
                },
                $variants
            );
            $option_data = [
                'option_name'   => $_REQUEST['option_data']['option_name'],
                'variants'      => $variants,
                'w_product_id'  => $_REQUEST['product_id'],
            ];
            fn_update_product_option($option_data);
        }
        return [CONTROLLER_STATUS_REDIRECT, "product_options.exceptions&product_id=" . $_REQUEST['product_id']];
    }

    $suffix = '';
    if ($mode == 'add_exceptions') {
        foreach ($_REQUEST['add_options_combination'] as $k => $v) {

            $exist = fn_check_combination($v, $_REQUEST['product_id']);
            $_data = array(
                'product_id' => $_REQUEST['product_id'],
                'option_id' => \key($v),
                'variant_id' => \current($v),
            );

            if (!$exist) {
                db_query("INSERT INTO ?:product_options_exceptions ?e", $_data);
            } else {
                fn_set_notification('W', __('warning'), __('exception_exist'));
            }
        }

        fn_update_exceptions($_REQUEST['product_id']);

        $suffix = ".exceptions?product_id=$_REQUEST[product_id]";
    }

    if ($mode == 'add_combinations') {
        if (is_array($_REQUEST['add_inventory'])) {
            foreach ($_REQUEST['add_inventory'] as $k => $v) {
                $combination_hash = fn_generate_cart_id($_REQUEST['product_id'], array('product_options' => $_REQUEST['add_options_combination'][$k]));

                $combination = fn_get_options_combination($_REQUEST['add_options_combination'][$k]);

                $product_code = fn_get_product_code($_REQUEST['product_id'], $_REQUEST['add_options_combination'][$k]);

                $_data = array(
                    'product_id' => $_REQUEST['product_id'],
                    'combination_hash' => $combination_hash,
                    'combination' => $combination,
                    'product_code' => !empty($product_code) ? $product_code : '',

                );

                $_data = fn_array_merge($v, $_data);

                db_query("REPLACE INTO ?:product_options_inventory ?e", $_data);
            }
        }

        $suffix = ".inventory?product_id=$_REQUEST[product_id]";
    }

    if ($mode == 'update_combinations') {
        $currentUser = fn_get_user_info($auth['user_id']);
        $combinations_hashes = \array_key_exists('combination_hashes',$_REQUEST) === true
            && \is_array($_REQUEST['combination_hashes']) === true ? $_REQUEST['combination_hashes'] : [];
        $declinationDisplayType = $currentUser['declination_display_type'] ?: 'board';
        $inventory = db_get_hash_array("SELECT * FROM ?:product_options_inventory WHERE product_id = ?i", 'combination_hash', $_REQUEST['product_id']);
        $globalSave = $_REQUEST['global_save'] === "true" ? true : false;
        $infiniteStock = $_REQUEST['infinite_stock'] ?? "0";

        if (\array_key_exists('amount', $_REQUEST) === true && \intval($_REQUEST['amount']) < 0) {
            fn_set_notification('E', __('error'), __('exceptions_invalid_quantity'));
        }

        // Updating images (new id)
        fn_attach_image_pairs('declinations', 'declinations', 0, (string) GlobalState::interfaceLocale(), array());
        // Updating images, old id with possible collision on combination_hash.
//        db_query("INSERT INTO `cscart_images_links` (object_id, object_type, detailed_id, `type`, `position`)
//                      SELECT combination_hash, 'product_option', detailed_id, `type`, cil.position
//                        FROM `cscart_images_links` AS cil
//                        INNER JOIN `cscart_product_options_inventory` as poi
//		                  ON object_type='declinations' AND object_id=CONCAT(poi.product_id, '_', poi.combination)
//		                WHERE poi.product_id = ?i
//		              ON DUPLICATE KEY UPDATE detailed_id=cil.detailed_id, `type`=cil.`type`, `position`=cil.position
//		                   ;",$_REQUEST['product_id']
//        );

        //Board declination management
        if ($declinationDisplayType === 'board') {
            foreach ($_REQUEST['inventory'] as $k => $v) {
                if ($infiniteStock === "1") {
                    $v['infinite_stock'] = 1;
                }

                if ($v['amount'] < 0) {
                    fn_set_notification('E', __('error'), __('exceptions_invalid_quantity'));
                }

                if (\array_key_exists('amount', $_REQUEST) === true && \mb_strlen($_REQUEST['amount']) > 0) {
                    $v['amount'] = $_REQUEST['amount'];
                }

                db_query("UPDATE ?:product_options_inventory SET ?u WHERE product_id = ?s AND combination = ?s", $v, $v['product_id'], $v['combination']);
            }
        }

        //List declination management
        if ($declinationDisplayType === 'list') {
            if ($globalSave === true && \count($combinations_hashes) === 0) {
                //global saving for every combination
                db_query("UPDATE ?:product_options_inventory SET infinite_stock = ?s WHERE product_id = ?i", $infiniteStock, $_REQUEST['product_id']);

                if (\array_key_exists('amount', $_REQUEST) === true && \mb_strlen($_REQUEST['amount']) > 0) {
                    db_query("UPDATE ?:product_options_inventory SET amount = ?s WHERE product_id = ?i", $_REQUEST['amount'], $_REQUEST['product_id']);
                }
            } elseif ($globalSave === true && \count($combinations_hashes) > 0) {
                //global saving with some combinations checked
                foreach ($combinations_hashes as $combination_hash) {
                    $combination = $_REQUEST['inventory'][$combination_hash];
                    $combination['infinite_stock'] = $infiniteStock;

                    if (\array_key_exists('amount', $_REQUEST) === true && \mb_strlen($_REQUEST['amount']) > 0) {
                        $combination['amount'] = $_REQUEST['amount'];
                    }
                    db_query("UPDATE ?:product_options_inventory SET ?u WHERE product_id = ?s AND combination = ?s", $combination, $combination['product_id'], $combination['combination']);
                }
            } else {
                //Saving only combinations checked
                foreach ($combinations_hashes as $combination_hash) {
                    $combination = $_REQUEST['inventory'][$combination_hash];
                    if (\array_key_exists('infinite_stock', $combination) === false) {
                        $combination['infinite_stock'] = "0";
                    }
                    if ($combination['amount'] < 0) {
                        fn_set_notification('E', __('error'), __('exceptions_invalid_quantity'));
                    }else{
                        db_query("UPDATE ?:product_options_inventory SET ?u WHERE product_id = ?s AND combination = ?s", $combination, $combination['product_id'], $combination['combination']);
                    }
                }
            }
        }

        // Do not update prices if the price value itself is not really updated, to avoid removing configuration
        $updatePrices = (\array_key_exists('user_price_input', $_REQUEST) === true && $_REQUEST['user_price_input'] !== '')
            || \array_key_exists('product-price-tiers-display', $_REQUEST) === true
            || $_REQUEST['prices'][0] !== '';

        if ($updatePrices === true && \count($_REQUEST['inventory']) > 0) {
            //Price management
            if ((\array_key_exists('lowerLimits', $_REQUEST) === false
                || \array_key_exists('prices', $_REQUEST) === false)
                && container()->getParameter('feature.tier_pricing') === true
            ) {
                fn_set_notification('E', __('error'), __('product_price_tiers_settings_error_detailed'));

                return [CONTROLLER_STATUS_REDIRECT, true === \array_key_exists('product_id', $_REQUEST) ? "products.update?product_id=" . $_REQUEST['product_id'] : "products.add"];
            }

            // Price Tiers management
            $priceTierInventory = [];

            if ($declinationDisplayType === 'board' || \count($combinations_hashes) === 0) {
                foreach ($_REQUEST['inventory'] as $key => $singleCombinationInfos) {
                    \array_push($priceTierInventory, [
                        'product_id' => $_REQUEST['product_id'],
                        'combination' => $singleCombinationInfos['combination'],
                        'priceTiers' => [],
                        'combination_key' => $key,
                    ]);
                }
            } elseif ($declinationDisplayType === 'list') {
                foreach ($combinations_hashes as $combination_hash) {
                    if (\count($_REQUEST['inventory']) === 1) {
                        $singleCombinationInfos = $_REQUEST['inventory'][$combination_hash];
                    }
                    \array_push($priceTierInventory, [
                        'product_id' => $_REQUEST['product_id'],
                        'combination' => $_REQUEST['inventory'][$combination_hash]['combination'],
                        'priceTiers' => [],
                        'combination_key' => $combination_hash,
                    ]);
                }
            }

            // Add priceTiers to priceTierInventory
            $userPriceInput = $_REQUEST['user_price_input'];

            for ($i = 0; $i < count($priceTierInventory); $i++) {
                $current_combination = $priceTierInventory[$i]['combination_key'];
                // Get price from overall declinations price field
                if (\is_string($userPriceInput) && \strlen($userPriceInput) > 0) {
                    \array_push($priceTierInventory[$i]['priceTiers'], [
                        'lowerLimit' => 0,
                        'price' => (float) $userPriceInput,
                    ]);
                // Get price from the price field of the single declinations
                } elseif (\is_array($_REQUEST['lowerLimits']) === false
                    && (\is_string($userPriceInput)
                    || \strlen($userPriceInput) > 0) === false
                ) {
                    \array_push($priceTierInventory[$i]['priceTiers'], [
                        'lowerLimit' => 0,
                        'price' => (float) $singleCombinationInfos['w_price'],
                    ]);
                // Get price from the different price tiers set
                } elseif ((\is_array($_REQUEST['lowerLimits']) === true || \is_array($_REQUEST['prices']) === true)
                    && \mb_strlen($_REQUEST['prices'][0]) > 0
                    && \mb_strlen($_REQUEST['lowerLimits'][0]) > 0
                ) {
                    for ($y = 0; $y < \count($_REQUEST['lowerLimits']); $y++) {
                        if ($_REQUEST['lowerLimits'][$y] !== "" && $_REQUEST['prices'][$y] !== "") {
                            \array_push($priceTierInventory[$i]['priceTiers'], [
                                'lowerLimit' => (int) $_REQUEST['lowerLimits'][$y],
                                'price' => (float) $_REQUEST['prices'][$y],
                            ]);
                        }
                    }
                }  elseif ( $_REQUEST['inventory'][$current_combination]['w_price']
                    !== $_REQUEST['inventory'][$current_combination]['w_price_old']
                ) {
                    $w_price = $_REQUEST['inventory'][$priceTierInventory[$i]['combination_key']]['w_price'];
                    \array_push($priceTierInventory[$i]['priceTiers'], [
                        'lowerLimit' => 0,
                        'price' => (float) $w_price,
                    ]);
                }
            }

            container()->get('marketplace.price_tier.price_tier_service')->savePriceTiers($_REQUEST['product_id'], $priceTierInventory);
        }

        $suffix = ".inventory?product_id=$_REQUEST[product_id]";
        \Wizacha\Events\Config::dispatch(
            \Wizacha\Product::EVENT_UPDATE,
            (new \Wizacha\Events\IterableEvent)->setElement((int)$_REQUEST['product_id'])
        );
    }

    if ($mode == 'm_delete_combinations') {
        foreach ($_REQUEST['combination_hashes'] as $v) {
            fn_delete_image_pairs($v, 'product_option');
            db_query("DELETE FROM ?:product_options_inventory WHERE combination_hash = ?i", $v);
        }

        $suffix = ".inventory?product_id=$_REQUEST[product_id]";
    }


    if ($mode == 'update') {
        fn_trusted_vars('option_data', 'regexp');

        $option_data = array();

        if (!empty($_REQUEST['option_id'])) {
            $condition = fn_get_company_condition('?:product_options.company_id');
            $option_data = db_get_row("SELECT * FROM ?:product_options WHERE option_id = ?i $condition", $_REQUEST['option_id']);
            if (empty($option_data)) {
                fn_set_notification('W', __('warning'), __('access_denied'));

                return array(CONTROLLER_STATUS_REDIRECT, "product_options.manage");
            }
        }

        $_REQUEST['option_data']['option_type'] = 'S'; //Liste de choix à sélection multiple.
        $_REQUEST['option_data'] = array_merge($option_data, $_REQUEST['option_data']);
        fn_set_company_id($_REQUEST['option_data']);

        try {
            $option_id = fn_update_product_option($_REQUEST['option_data'], $_REQUEST['option_id'], (string) GlobalState::contentLocale());
        } catch (CannotDeleteOptionVariantException $exception) {
            fn_set_notification('E', __('error'), __('w_product_exist_for_variant'));

            return [CONTROLLER_STATUS_DENIED];
        }

        if (!empty($_REQUEST['object']) && $_REQUEST['object'] == 'product') { // FIXME (when assigning page and current url will be removed from ajax)

            return array(CONTROLLER_STATUS_OK, "$_SERVER[HTTP_REFERER]&selected_section=options");
        }

        $suffix = ".manage";
    }

    if ($mode == "update_combinations") {
        //Display notification if product is not salable
        if (!empty($_REQUEST['inventory'])) {
            //get full inventory to avoid bad notification if only one combination is submitted (manual edition)
            list($inventory) = \Wizacha\Product::getInventory(['product_id'=>$_REQUEST['product_id']]);

            $valid_inventory = array_filter(
                $inventory,
                function ($item) {
                    $amount = $item['infinite_stock'] === '1' ? $item['infinite_stock'] : $item['amount'];
                    return ($item['w_price'] * $amount);
                }
            );
            if (empty($valid_inventory)) {
                fn_set_notification('E', __('error'), __('w_prices_inventory_error'));
            }
        }

        // Intercept redirect from "save and close" button and give the correct URL.
        \Wizacha\Misc::redirectAfterSave('product_options', 'products.update&product_id=' . $_REQUEST['product_id'], $_REQUEST);

        $hasCustomPositions = db_get_field('SELECT COUNT(*) FROM ?:product_options_inventory WHERE product_id = ?i AND position > 1', $_REQUEST['product_id']) != 0;
        if (!$hasCustomPositions) {
            //Affect 0 as position if is the best price, else 1
            db_query("
                update ?:product_options_inventory set position = if
                (
                    combination_hash in
                    (
                        select * from (
                            select b.combination_hash from ?:product_options_inventory as b where b.product_id = ?i and b.w_price =
                            (
                                select min(c.w_price) from ?:product_options_inventory as c where c.product_id = ?i and c.w_price > 0
                            )
                        ) as tmp1
                    ) , 0 , 1
                )
                where product_id = ?i",
                $_REQUEST["product_id"], $_REQUEST["product_id"], $_REQUEST["product_id"]
            );
        }

        //Reset product base price
        db_query("UPDATE ?:product_prices SET price=0 WHERE product_id=?i",$_REQUEST["product_id"]);
    }

    if ($mode === 'display_items_in_search_filters' || $mode === 'remove_items_in_search_filters') {
        $display_on_faceting = 1;
        if ($mode === 'remove_items_in_search_filters') {
            $display_on_faceting = 0;
        }

        if (\array_key_exists('selected_options_ids', $_REQUEST) === true && \strlen($_REQUEST['selected_options_ids']) > 0) {
            db_query("UPDATE ?:product_options SET display_on_faceting = ?d WHERE option_id IN (?n) ",
                     $display_on_faceting,
                     \explode(',', $_REQUEST['selected_options_ids'])
            );

            foreach (\explode(',', $_REQUEST['selected_options_ids']) as $optionId) {
                Config::dispatch(
                    Option::EVENT_UPDATE,
                    (new IterableEvent)->setElement($optionId)
                );
            }
            fn_set_notification('N', __('notice'), __('change_saved'));
        }

        return array(CONTROLLER_STATUS_REDIRECT, "product_options.manage");
    }

    if ($mode === "change_declination_type") {
        $userInfo = fn_get_user_info($auth['user_id']);
        $declinationDisplayType = $userInfo['declination_display_type'] ?: 'board';
        if ($declinationDisplayType === 'list') {
            db_query("UPDATE ?:users SET declination_display_type='board' WHERE user_id=?i", $userInfo['user_id']);
        } elseif ($declinationDisplayType === 'board') {
            db_query("UPDATE ?:users SET declination_display_type='list' WHERE user_id=?i", $userInfo['user_id']);
        }

        $suffix = ".inventory?product_id=$_REQUEST[product_id]";
    }

    return array(CONTROLLER_STATUS_OK, "product_options$suffix&sort_by=$_REQUEST[sort_col]&sort_order=$_REQUEST[sort_order]");
}

/**
 * Set $_REQUEST['items_per_page'] to the exact number of items, only if combination are displayed in table mode (more than 1 option)
 */
if ($mode == 'inventory') {
    $options = fn_get_product_options($_REQUEST['product_id'], (string) GlobalState::contentLocale(), true, true, true);
    //Filter the array to have payment frequency and commitment_period as the firsts items
    $optionsToSort = [];
    foreach ($options as $option_id => $option) {
        if ($option['code'] !== 'payment_frequency' && $option['code'] !== 'commitment_period') {
            $optionsToSort[$option_id] = $option;
            unset($options[$option_id]);
        }
    }
    $options = \array_replace_recursive($options, $optionsToSort);
    if (!empty($options)) {
        $_REQUEST["items_per_page"] = fn_count_combinations($options);
    }
} elseif ($mode == 'delete') {
    /** @var OptionRepository $optionRepository */
    $optionRepository = container()->get(OptionRepository::class);
    $optionId = \intval($_REQUEST['option_id']);

    if ($optionRepository->isOptionIdLinkedToCategories($optionId)) {
        fn_set_notification('E', __('error'), __('w_category_exist_for_option') . $category_data['category']);

        return [CONTROLLER_STATUS_DENIED];
    }

    if ($optionRepository->isOptionIdLinkedToProducts($optionId)) {
        fn_set_notification('E', __('error'), __('w_product_exist_for_option'));

        return [CONTROLLER_STATUS_DENIED];
    }
}

//
// Product options combination inventory tracking
//
if ($mode == 'inventory') {

    list($inventory, $search, $product_options, $product_inventory) = fn_get_product_options_inventory($_REQUEST, Registry::get('settings.Appearance.admin_elements_per_page'));

    //Sorting inventory
    $amount = [];
    foreach ($inventory as $key => $row) {
        $amount[$key] = $row['amount'];
    }
    \array_multisort($amount, SORT_DESC, $inventory);

    Registry::get('view')->assign('product_inventory', $product_inventory);
    Registry::get('view')->assign('product_options', $product_options);
    Registry::get('view')->assign('inventory', $inventory);
    Registry::get('view')->assign('search', $search);
    Registry::get('view')->assign('is_transactional', (new \Wizacha\Product($_REQUEST['product_id']))->getCategory()->isTransactional());

//
// Options list
//
} elseif ($mode == 'manage') {
    $params = $_REQUEST;

    $showOnlySystemOptions = (int) $_REQUEST['show_only_system_options'] === 1;

    if ($showOnlySystemOptions) {
        [$product_options, $search] = fn_get_system_options($params, Registry::get('settings.Appearance.admin_elements_per_page'), (string) GlobalState::contentLocale());
    } else {
        [$product_options, $search] = fn_get_product_global_options($params, Registry::get('settings.Appearance.admin_elements_per_page'), (string) GlobalState::contentLocale());
    }

    Registry::get('view')->assign('product_options', $product_options);
    Registry::get('view')->assign('search', $search);
    Registry::get('view')->assign('object', 'global');
    Registry::get('view')->assign('show_only_system_options', $showOnlySystemOptions);

    if (empty($product_options) && defined('AJAX_REQUEST')) {
        $ajax->assign('force_redirection', fn_url('product_options.manage'));
    }

//
// Update option
//
} elseif ($mode == 'update') {

    $product_id = !empty($_REQUEST['product_id']) ? $_REQUEST['product_id'] : 0;
    $o_data = fn_get_product_option_data($_REQUEST['option_id'], $product_id);
    $oDataPlaceholder = fn_get_product_option_data($_REQUEST['option_id'], $product_id, (string) GlobalState::interfaceLocale());

    if (isset($_REQUEST['object'])) {
        Registry::get('view')->assign('object', $_REQUEST['object']);
    }
    Registry::get('view')->assign('option_data', $o_data);
    Registry::get('view')->assign('optionDataPlaceholder', $oDataPlaceholder);
    Registry::get('view')->assign('option_id', $_REQUEST['option_id']);

//
// Delete option
//
} elseif ($mode == 'delete') {
    if (!empty($_REQUEST['option_id']) && fn_check_company_id('product_options', 'option_id', $_REQUEST['option_id']) || (!empty($_REQUEST['product_id']) && fn_check_company_id('products', 'product_id', $_REQUEST['product_id']))) {

        $p_id = db_get_field("SELECT product_id FROM ?:product_options WHERE option_id = ?i", $_REQUEST['option_id']);

        if (!empty($_REQUEST['product_id']) && empty($p_id)) { // we're deleting global option from the product
            db_query("DELETE FROM ?:product_global_option_links WHERE product_id = ?i AND option_id = ?i", $_REQUEST['product_id'], $_REQUEST['option_id']);

        } else {
            fn_delete_product_option($_REQUEST['option_id']);
        }

        if (empty($_REQUEST['product_id']) && empty($p_id)) { // we're deleting global option itself
            db_query("DELETE FROM ?:product_global_option_links WHERE option_id = ?i", $_REQUEST['option_id']);
        }
    }
    if (!empty($_REQUEST['product_id'])) {
        $_options = fn_get_product_options($_REQUEST['product_id']);
        if (empty($_options)) {
            Registry::get('view')->display('views/product_options/manage.tpl');
        }
        exit();
    }

    return array(CONTROLLER_STATUS_REDIRECT, "product_options.manage");

} elseif ($mode == 'rebuild_combinations') {
    fn_rebuild_product_options_inventory($_REQUEST['product_id']);

    return array(CONTROLLER_STATUS_OK, "product_options.inventory?product_id=$_REQUEST[product_id]");

} elseif ($mode == 'delete_combination') {
    if (!empty($_REQUEST['combination_hash'])) {
        fn_delete_product_combination($_REQUEST['combination_hash']);
    }

    return array(CONTROLLER_STATUS_REDIRECT, "product_options.inventory?product_id=$_REQUEST[product_id]");
}

//
// Product options exceptions
//
if ($mode == 'exceptions') {

    $exceptions = \Wizacha\Product::getExceptions($_REQUEST['product_id']);
    $product_options = fn_get_product_options($_REQUEST['product_id'], (string) GlobalState::contentLocale(), true);
    $product_data = fn_get_product_data($_REQUEST['product_id'], $auth, (string) GlobalState::contentLocale(), '', true, true, true, true);

    Registry::get('view')->assign('product_options', $product_options);
    Registry::get('view')->assign('exceptions', $exceptions);
    Registry::get('view')->assign('product_data', $product_data);
}

if (!empty($_REQUEST['product_id'])) {
    Registry::get('view')->assign('product_id', $_REQUEST['product_id']);
}

if ($mode == "inventory") {
    $userInfo = fn_get_user_info($auth['user_id']);
    $product_options = Registry::get('view')->getTemplateVars('product_options');
    Registry::get('view')->assign('declinationDisplayType', $userInfo['declination_display_type'] ?: 'board');
    $declinationDisplayType = Registry::get('view')->getTemplateVars('declinationDisplayType');
    if (empty($product_options)) {
        return array(CONTROLLER_STATUS_OK, "products.update?product_id=$_REQUEST[product_id]");
    }
    $inventory = Registry::get('view')->getTemplateVars('inventory');
    $product_id = $_REQUEST["product_id"];

    $exceptions = \Wizacha\Product::getExceptions($product_id);
    $product_options = \Wizacha\Option::filterExceptions($product_options, $exceptions);

    $nb_combinations = fn_count_combinations($product_options);

    if ($nb_combinations) {
        //re-assign product_options to view
        Registry::get('view')->assign('product_options', $product_options);
    } else {
        fn_set_notification('E', __('error'), __('w_all_option_forbidden'));
        Registry::get('view')->assign('product_options', []);
    }
    Registry::get('view')->assign('disable_add_button', true);


} elseif ($mode == 'exceptions') {
    $exceptions = Registry::get('view')->getTemplateVars('exceptions');
    $main_category = fn_w_get_product_main_category($_REQUEST['product_id']);
    $category_options = fn_w_get_category_options($main_category);

    foreach ($exceptions as $option_id => $option) {
        foreach ($option as $variant_id) {
            $category_options[$option_id]['variants'][$variant_id]['allowed'] = true;
        }
    }

    //Removed variants without id
    foreach ($category_options as &$option) {
        $option['variants'] = array_filter(
            $option['variants'],
            function ($variant) {
                return $variant['variant_id'];
            }
        );
    }

    Registry::get('view')->assign('product_options', $category_options);
    Registry::get('view')->assign('w_exceptions', 1);
}

// count combinations from options variants
function fn_count_combinations(array $options): int
{
    // filter out options without variants
    $available_options = array_filter(
        $options,
        function (array $opt): bool {
            return is_array($opt['variants']);
        }
    );

    // count variants per options
    $variants_qty = array_map(
        function (array $opt): int {
            return count($opt['variants']);
        },
        $available_options
    );

    return array_product($variants_qty);
}

function fn_get_product_options_inventory($params, $items_per_page = 0, $lang_code = null)
{
    return \Wizacha\Product::getInventory($params, $items_per_page, $lang_code ?? (string) GlobalState::contentLocale());
}

/**
 * @param $combination_code : option code in style 3_2.6_8
 * @param $inventory
 * @return mixed inventory item
 */
function fn_get_combination_from_code($combination_code, $inventory)
{
    $combination_table = explode('.', $combination_code);
    $options = [];
    array_walk($combination_table, function ($value, $key) use (&$options) {
        $option = explode('_', $value);
        $options[$option[0]] = $option[1];
    });

    /**
     * 1. Filter the inventory to get current combination.
     * 2. Retrieve array, so array_shift it to get first (and only) item
     * NOTE : array_shift take a reference to an array, so the 2 steps must be separated
     */

    $filtered = array_filter($inventory,
        function ($item) use ($options) {
            foreach ($options as $key => $value) {
                if ($item['combination'][$key] !== $value) {
                    return false;
                }
            }
            return true;
        }
    );

    return array_shift($filtered);

}
