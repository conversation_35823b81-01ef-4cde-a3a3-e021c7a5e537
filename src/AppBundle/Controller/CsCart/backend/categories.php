<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON> Shalnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\Ajax;
use Tygh\Registry;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

$_REQUEST['category_id'] = empty($_REQUEST['category_id']) ? 0 : $_REQUEST['category_id'];

// Return picker categories as JSON
if ($mode == 'picker_json') {
    list($categoryTree,) = fn_get_categories([
        'simple' => false,
        'add_root' => !empty($_REQUEST['root']) ? $_REQUEST['root'] : '',
        'b_id' => !empty($_REQUEST['b_id']) ? $_REQUEST['b_id'] : '',
        'except_id' => $except_id,
        'company_ids' => !empty($_REQUEST['company_ids']) ? $_REQUEST['company_ids'] : '',
        'save_view_results' => !empty($_REQUEST['save_view_results']) ? $_REQUEST['save_view_results'] : '',
    ], (string) GlobalState::contentLocale());

    return new JsonResponse(array_values($categoryTree));
}

// Return categories as JSON
if ($mode === 'picker_json_get_categories_by_ids') {
    $categoriesData = [];
    if (\array_key_exists('categories_ids', $_REQUEST) === true) {
        list($categoriesData,) = fn_get_categories([
            'simple' => false,
            'group_by_level' => false,
            'item_ids' => $_REQUEST['categories_ids'],
        ], (string) GlobalState::contentLocale());
    }

    return new JsonResponse(\array_values($categoriesData));
}

// Return categories children ids as JSON
if ($mode === 'picker_json_get_categories_children_ids') {
    $ids = [];
    $categoryService = container()->get('marketplace.catalog.category_service');
    foreach ($categoryService->getCategories([], true) as $category) {
        $ids[$category->getId()] = $categoryService->getCategoryAllChildrenIds($category->getId());
    }

    return new JsonResponse($ids);
}

if ($mode == 'variant_picker_json') {
    $options = fn_w_get_category_options($_REQUEST['category_id'], false, true);

    return new JsonResponse($options);
}


if ($_SERVER['REQUEST_METHOD'] == 'POST') {

    // Define trusted variables that shouldn't be stripped
    fn_trusted_vars (
        'category_data',
        'categories_data'
    );

    //
    // Create/update category
    //
    if ($mode == 'update') {

        $category_id = fn_update_category($_REQUEST['category_data'], $_REQUEST['category_id'], (string) GlobalState::contentLocale());

        if (!empty($category_id)) {
            fn_attach_image_pairs('category_main', 'category', $category_id, (string) GlobalState::contentLocale());

            $suffix = ".update?category_id=$category_id" . (!empty($_REQUEST['category_data']['block_id']) ? "&selected_block_id=" . $_REQUEST['category_data']['block_id'] : "");
        } else {
            $suffix = '.manage';
        }
    }

    //
    // Processing mulitple addition of new category elements
    //
    if ($mode == 'm_add') {
        if (!fn_is_empty($_REQUEST['categories_data'])) {
            $is_added = false;
            foreach ($_REQUEST['categories_data'] as $k => $v) {
                if (!empty($v['category'])) {  // Checking for required fields for new category
                    if (fn_update_category($v)) {
                        $is_added = true;
                    }
                }
            }

            if ($is_added) {
                fn_set_notification('N', __('notice'), __('categories_have_been_added'));
            }
        }


        $suffix = ".manage";
    }

    //
    // Processing multiple updating of category elements
    //
    if ($mode == 'm_update') {

        // Update multiple categories data
        if (is_array($_REQUEST['categories_data'])) {
            fn_attach_image_pairs('category_main', 'category', 0, (string) GlobalState::contentLocale());

            foreach ($_REQUEST['categories_data'] as $k => $v) {
                fn_update_category($v, $k, (string) GlobalState::contentLocale());
            }
        }

        $suffix = ".manage";
    }

    //
    // Processing deleting of multiple category elements
    //
    if ($mode == 'm_delete') {

        if (isset($_REQUEST['category_ids'])) {
            foreach ($_REQUEST['category_ids'] as $v) {
                fn_delete_category($v);
            }
        }

        unset($_SESSION['category_ids']);

        fn_set_notification('N', __('notice'), __('text_categories_have_been_deleted'));
        $suffix = ".manage";
    }


    //
    // Store selected fields for using in 'm_update' mode
    //
    if ($mode == 'store_selection') {

        if (!empty($_REQUEST['category_ids'])) {
            $_SESSION['category_ids'] = $_REQUEST['category_ids'];
            $_SESSION['selected_fields'] = $_REQUEST['selected_fields'];

            $suffix = ".m_update";
        } else {
            $suffix = ".manage";
        }
    }

    return array(CONTROLLER_STATUS_OK, "categories$suffix");
}

//
// 'Add new category' page
//
if ($mode == 'add') {

    // [Page sections]
    $tabs = array (
        'detailed' => array (
            'title' => __('general'),
            'js' => true
        )
    );

    $tabs['addons'] = array (
        'title' => __('addons'),
        'js' => true
    );

    Registry::set('navigation.tabs', $tabs);
    // [/Page sections]

    $category_data['transaction_modes'] = container()->get('marketplace.transaction_mode.service')->getAvailablesModesAsArray();

    if (!empty($_REQUEST['parent_id'])) {
        $category_data['parent_id'] = $_REQUEST['parent_id'];
    }
    Registry::get('view')->assign('category_data', $category_data);

//
// 'Multiple categories addition' page
//
} elseif ($mode == 'm_add') {

//
// 'category update' page
//
} elseif ($mode == 'update') {

    $category_id = $_REQUEST['category_id'];
    // Get current category data
    $category_data = fn_get_category_data($category_id, (string) GlobalState::contentLocale());
    $categoriesDataPlaceholder = fn_get_category_data($category_id, (string) GlobalState::interfaceLocale());

    if (empty($category_data)) {
        return array(CONTROLLER_STATUS_NO_PAGE);
    }

    $category_data['transaction_modes'] = container()->get('marketplace.transaction_mode.service')->getAvailablesModesAsArray();

    // [Page sections]
    $tabs = array (
        'detailed' => array (
            'title' => __('general'),
            'js' => true
        )
    );

    $tabs['addons'] = array (
        'title' => __('addons'),
        'js' => true
    );


    Registry::set('navigation.tabs', $tabs);
    // [/Page sections]
    Registry::get('view')->assign('category_data', $category_data);
    Registry::get('view')->assign('categoryDataPlaceholder', $categoriesDataPlaceholder);

    $params = array (
        'active_category_id' => $category_id,
    );
    $category_count = db_get_field("SELECT COUNT(*) FROM ?:categories");
    if ($category_count > CATEGORY_THRESHOLD) {
        $params['current_category_id'] = $category_id;
        $params['visible'] = true;
    }
    list($categories_tree, ) = fn_get_categories($params, (string) GlobalState::contentLocale());
    Registry::get('view')->assign('categories_tree', $categories_tree);
//
// 'Mulitple categories updating' page
//
} elseif ($mode == 'm_update') {

    $category_ids = $_SESSION['category_ids'];
    $selected_fields = $_SESSION['selected_fields'];

    if (empty($category_ids) || empty($selected_fields) || empty($selected_fields['object']) || $selected_fields['object'] != 'category') {
        return array(CONTROLLER_STATUS_REDIRECT, "categories.manage");
    }

    $field_groups = array (
        'A' => array (
            'category' => 'categories_data',
            'page_title' => 'categories_data',
            'position' => 'categories_data',
        ),

        'C' => array ( // textareas
            'description' => 'categories_data',
            'meta_keywords' => 'categories_data',
            'meta_description' => 'categories_data',
        ),
    );

    $get_main_pair = false;

    $fields2update = $selected_fields['data'];

    $data_search_fields = implode($fields2update, ', ');

    if (!empty($data_search_fields)) {
        $data_search_fields = ', ' . $data_search_fields;
    }

    if (!empty($selected_fields['images'])) {
        foreach ($selected_fields['images'] as $value) {
            $fields2update[] = $value;
            if ($value == 'image_pair') {
                $get_main_pair = true;
            }
        }
    }

    $filled_groups = array();
    $field_names = array();
    foreach ($fields2update as $field) {
        if ($field == 'timestamp') {
            $desc = 'creation_date';
        } else {
            $desc = $field;
        }
        if ($field == 'category_id') {
            continue;
        }

        if (!empty($field_groups['A'][$field])) {
            $filled_groups['A'][$field] = __($desc);
            continue;
        } elseif (!empty($field_groups['B'][$field])) {
            $filled_groups['B'][$field] = __($desc);
            continue;
        } elseif (!empty($field_groups['C'][$field])) {
            $filled_groups['C'][$field] = __($desc);
            continue;
        }

        $field_names[$field] = __($desc);
    }

    ksort($filled_groups, SORT_STRING);

    $categories_data = array();
    foreach ($category_ids as $value) {
        $categories_data[$value] = fn_get_category_data($value, (string) GlobalState::contentLocale(), '?:categories.category_id' . $data_search_fields, $get_main_pair);
    }



    Registry::get('view')->assign('field_groups', $field_groups);
    Registry::get('view')->assign('filled_groups', $filled_groups);

    Registry::get('view')->assign('fields2update', $fields2update);
    Registry::get('view')->assign('field_names', $field_names);

    Registry::get('view')->assign('categories_data', $categories_data);
}
//
// Delete category
//
elseif ($mode == 'delete') {

    if (!empty($_REQUEST['category_id'])) {
        if (fn_delete_category($_REQUEST['category_id'])) {
            fn_set_notification('N', __('notice'), __('text_category_has_been_deleted'));
        } else {
            fn_set_notification('E', __('error'), __('w_cant_delete_category'));
        }
    }

    return array(CONTROLLER_STATUS_REDIRECT, "categories.manage");

//
// 'Management' page
//
} elseif ($mode == 'manage' || $mode == 'picker') {

    if ($mode == 'manage') {
        unset($_SESSION['category_ids']);
        unset($_SESSION['selected_fields']);
        Registry::get('view')->assign('categories_stats', fn_get_categories_stats());
    }

    $category_count = db_get_field("SELECT COUNT(*) FROM ?:categories");
    $category_id = empty($_REQUEST['category_id']) ? 0 : $_REQUEST['category_id'];
    $except_id = 0;
    if (!empty($_REQUEST['except_id'])) {
        $except_id = $_REQUEST['except_id'];
        Registry::get('view')->assign('except_id', $_REQUEST['except_id']);
    }
    if (!isset($_REQUEST['search'])) {
        if ($category_count < CATEGORY_THRESHOLD) {
            $params = array(
                'simple' => false,
                'add_root' => !empty($_REQUEST['root']) ? $_REQUEST['root'] : '',
                'b_id' => !empty($_REQUEST['b_id']) ? $_REQUEST['b_id'] : '',
                'except_id' => $except_id,
                'company_ids' => !empty($_REQUEST['company_ids']) ? $_REQUEST['company_ids'] : '',
                'save_view_results' => !empty($_REQUEST['save_view_results']) ? $_REQUEST['save_view_results'] : ''
            );
            list($categories_tree,) = fn_get_categories($params, (string) GlobalState::contentLocale());
            Registry::get('view')->assign('show_all', true);
        } else {
            $params = array(
                'category_id' => $category_id,
                'current_category_id' => $category_id,
                'visible' => true,
                'simple' => false,
                'add_root' => !empty($_REQUEST['root']) ? $_REQUEST['root'] : '',
                'b_id' => !empty($_REQUEST['b_id']) ? $_REQUEST['b_id'] : '',
                'except_id' => $except_id,
                'company_ids' => !empty($_REQUEST['company_ids']) ? $_REQUEST['company_ids'] : '',
                'save_view_results' => !empty($_REQUEST['save_view_results']) ? $_REQUEST['save_view_results'] : ''
            );
            list($categories_tree,) = fn_get_categories($params, (string) GlobalState::contentLocale());
        }

        //add transaction_mode info
        $applyIsTransactional = function (&$category) use (&$applyIsTransactional) {
            $categoryObj = new \Wizacha\Category($category['category_id']);
            if ($categoryObj->getId() != 0) {
                $category['is_transactional'] = $categoryObj->isTransactional();
                $category['transaction_mode'] = $categoryObj->getTransactionMode()->getValue();
                $category['is_transaction_mode_overridable'] = $categoryObj->isTransactionModeOverridable();
            } else {
                $category['is_transactional'] = true;
                $category['transaction_mode'] = container()->get('marketplace.transaction_mode.service')->getDefaultMode()->getValue();
                $category['is_transaction_mode_overridable'] = false;
            }

            if (!empty($category['subcategories'])) {
                array_walk($category['subcategories'], $applyIsTransactional);
            }
        };
        array_walk($categories_tree, $applyIsTransactional);

        Registry::get('view')->assign('categories_tree', $categories_tree);
    }
    if ($category_count < CATEGORY_SHOW_ALL) {
        Registry::get('view')->assign('expand_all', true);
    }
    if (defined('AJAX_REQUEST')) {
        if (!empty($_REQUEST['random'])) {
            Registry::get('view')->assign('random', $_REQUEST['random']);
        }
        Registry::get('view')->assign('category_id', $category_id);
    }
}

//
// Categories picker
//
if ($mode == 'picker') {
    if (!empty($_REQUEST['w_leaves_only'])) {
        Registry::get('view')->assign('w_leaves_only', $_REQUEST['w_leaves_only']);
    }
    if (!empty($_REQUEST['w_select_manager'])) {
        Registry::get('view')->assign('w_select_manager', $_REQUEST['w_select_manager']);
    }

    $content = Registry::get('view')->fetch('pickers/categories/picker_contents.tpl');

    if (defined('AJAX_REQUEST') === true) {
        return Registry::get('ajax')->render($content);
    } else {
        return new Response($content, Response::HTTP_OK);
    }
}

/**
 * Return json according to the field "transaction_mode_overridable" of the category
 */
if ($mode === 'transaction_mode_overridable') {

    $categoryId = array_key_exists('category_id', $_GET) && is_numeric($_GET['category_id']) ? (int)$_GET['category_id'] : null;

    if ($categoryId === null || $categoryId <= 0) {
        return;
    }

    $categoryDatas = fn_get_category_data($categoryId, null, 'transaction_mode_overridable');

    if (is_array($categoryDatas) && array_key_exists('transaction_mode_overridable', $categoryDatas)) {
        return new JsonResponse([
            'transaction_mode_overridable' => $categoryDatas['transaction_mode_overridable'] === 'Y',
        ]);
    }
}
