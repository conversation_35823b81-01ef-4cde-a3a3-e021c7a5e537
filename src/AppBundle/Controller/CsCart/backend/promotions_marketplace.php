<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */
declare(strict_types=1);

use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\OptionsResolver\Options;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Serializer\Serializer;
use Tygh\Registry;
use Wizacha\Marketplace\Promotion\MarketplacePromotion;

if (defined('BOOTSTRAP') === false) {
    die('Access denied.');
}

$params = $_REQUEST;
$promotionService = container()->get('marketplace.promotion.promotionservice');
$serializer = container()->get('serializer');

// API auth info
$userId = $_SESSION['auth']['user_id'];
$userData = fn_get_user_info($userId, false);
Registry::get('view')->assign('user_api_key', $userData['api_key']);
Registry::get('view')->assign('user_email', $userData['email']);

$promotionId = array_key_exists('promotion_id', $_REQUEST) ? $_REQUEST['promotion_id'] : null;
Registry::get('view')->assign('promotion_id', $promotionId);
Registry::get('view')->assign('groups', container()->get('marketplace.user_group.service')->getGroupsToArray());
Registry::get('view')->assign('featureUserGroupEnabled', container()->getParameter('feature.user_groups_enabled'));
Registry::get('view')->assign('featureOptionalTargetMarketplacePromotion', container()->getParameter('feature.marketplace_discount_optional_bonuses'));
$duplicate = array_key_exists('duplicate', $_REQUEST) ? true : false;
Registry::get('view')->assign('duplicate', $duplicate);

if ($mode === 'manage') {
    // Pagination
    $params['items_per_page'] = $params['items_per_page'] ?? Registry::get('settings.Appearance.admin_pages_per_page');
    $params['page'] = max(intval($params['page']), 1);
    $start = $params['page'] > 0 ? ($params['page'] - 1) * $params['items_per_page'] : 0;

    // Normalize active filter
    if (array_key_exists('active', $params)) {
        if ($params['active'] === "") {
            unset($params['active']);
        } else {
            $params['active'] = intval($params['active']);
        }
    }

    if (array_key_exists('isValid', $params)) {
        if ($params['isValid'] === "") {
            unset($params['isValid']);
        } else {
            $params['isValid'] = intval($params['isValid']);
        }
    }

    // Normalize coupon filter
    if (array_key_exists('coupon', $params) && $params['coupon'] === "") {
        unset($params['coupon']);
    }

    [$promotions, $totalCount] = $promotionService->listMarketplacePromotionsPaginated((int) $start, (int) $params['items_per_page'], $params);

    $items = $serializer->normalize($promotions);
    $params['total_items'] = $totalCount;

    // feed the tpl
    Registry::get('view')->assign('items', $items);
    Registry::get('view')->assign('search', $params);
    Registry::get('view')->assign('editUrl', fn_url('promotions_marketplace.update?promotion_id='));
    Registry::get('view')->assign('duplicateUrl', fn_url('promotions_marketplace.add?duplicate=true&promotion_id='));
}
