<?php
/**
 *  <AUTHOR> DevTeam <<EMAIL>>
 *  @copyright   Copyright (c) Wizacha
 *  @license     Proprietary
 */

use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Tygh\Registry;
use Wizacha\AppBundle\Controller\Api\Division\Dto\ApiDivisionSettingsDto;
use Wizacha\Marketplace\Division\CompanyDivisionSettings;
use Wizacha\Marketplace\Division\Exception\InvalidDivisionCodeException;
use Wizacha\Marketplace\Division\Service\DivisionService;
use Wizacha\Marketplace\GlobalState\GlobalState;

if (!defined('BOOTSTRAP')) {
    die('Access denied');
}

$params = $_REQUEST;

$container = container();
$divisionService = $container->get('marketplace.division.service');
$divisionSettingsService = $container->get('marketplace.divisions_settings.service');
$productService = $container->get('marketplace.pim.product.service');

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if ($mode === 'update'
        && true === $container->getParameter('feature.available_offers')
        && \array_key_exists('division-loaded', $_REQUEST)
        && 'true' === $_REQUEST['division-loaded']
    ) {
        // Manage inherit-divisions checkbox
        if (\array_key_exists('included', $_REQUEST)
            && \is_array($_REQUEST['included'])
        ) {
            $includedDivisions = $_REQUEST['included'];
        } elseif (\array_key_exists('inherit-divisions', $_REQUEST)
            && 'on' === $_REQUEST['inherit-divisions']
        ) {
            $includedDivisions = [DivisionService::ROOT_CODE];
        } else {
            $includedDivisions = [];
        }

        $payload = [
            'included' => $includedDivisions,
            'excluded' => \is_array($_REQUEST['excluded']) ? $_REQUEST['excluded'] : []
        ];

        try {
            $divisionSettingsService->updateMarketplaceDivisionSettings(
                new ApiDivisionSettingsDto($payload['included'], $payload['excluded'])
            );
        } catch (InvalidDivisionCodeException $exception) {
            throw new BadRequestHttpException($exception->getMessage(), $exception);
        }
    }

    return array(CONTROLLER_STATUS_OK, "available_offers.update");
}

if ($mode == 'update') {
    // Get all divisions
    $selectableDivisions = $divisionService->getMarketplaceDivisions(
        GlobalState::interfaceLocale(),
        false
    );

    $divisionsSideBar = $selectableDivisions;
    $marketplaceDivisionSettings = $divisionSettingsService->getMarketplaceDivisionSettings();

    foreach ($selectableDivisions as &$division) {
        $division->included = \in_array(
            $division->code,
            $marketplaceDivisionSettings->getIncludedDivisions()->toArray()
        );
        $division->excluded = \in_array(
            $division->code,
            $marketplaceDivisionSettings->getExcludedDivisions()->toArray()
        );
    }

    Registry::get('view')->assign('divisions', $selectableDivisions);
    Registry::get('view')->assign('divisionsSideBar', $divisionsSideBar);
    Registry::get('view')->assign('warningMsgKey', 'division_admin_edit_warning');
} elseif ($mode == 'ajax_load') {
    if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] !== "XMLHttpRequest") {
        return array(CONTROLLER_STATUS_NO_PAGE);
    }

    // Get marketplace enabled divisions
    $selectableDivisions = $divisionService->getMarketplaceDivisions(
        GlobalState::interfaceLocale(),
        false,
        true
    );

    // Get all divisions
    $allDivisions = $divisionService->getMarketplaceDivisions(
        GlobalState::interfaceLocale(),
        false,
        null
    );

    $includedDivisions = [];
    $excludedDivisions = [];

    if (false === \is_null($params['company'])) {
        if ($params['company'] === "0") {
            // new company case
            $includedDivisions = [CompanyDivisionSettings::DEFAULT_DIVISION];
            $excludedDivisions = [];
        } else {
            // update case
            $companyDivisionSettings = $divisionSettingsService->getCompanyDivisionSettings((int)$params['company']);

            $includedDivisions = $companyDivisionSettings->getIncludedDivisions()->toArray();
            $excludedDivisions = $companyDivisionSettings->getExcludedDivisions()->toArray();
        }
    } elseif (false === \is_null($params['product'])) {
        $productDivisionSettings = $divisionSettingsService->getProductDivisionSettings((int) $params['product']);
        $product = $productService->get($params['product']);

        // Get company enabled divisions
        $selectableDivisions = $divisionService->getCompanyDivisions(
            $product->getCompanyId(),
            GlobalState::interfaceLocale(),
            false,
            true
        );

        $includedDivisions = $productDivisionSettings->getIncludedDivisions()->toArray();
        $excludedDivisions = $productDivisionSettings->getExcludedDivisions()->toArray();
    }

    // Add The 'ALL' divisions if it not already in selectable Divisions
    if (false ===
        \in_array(
            'ALL',
            $divisionSettingsService->getMarketplaceDivisionSettings()->getIncludedDivisions()->toArray()
        )
         && \count(array_filter($selectableDivisions, function($division) { return 'ALL' === $division->code; })) === 0
    ) {
        $parentDivision = new stdClass();
        $parentDivision->code = 'ALL';
        $parentDivision->name = __('all_divisions');
        $parentDivision->included = false;
        $parentDivision->excluded = false;

        $selectableDivisions[] = $parentDivision;
    }

    // Add outscope divisions to selectable divisions
    $configDivisionsCodes = array_merge($includedDivisions, $excludedDivisions);
    $configDivisions = array_filter(
        $allDivisions,
        function (\stdClass $division) use ($configDivisionsCodes): bool {
            return \in_array($division->code, $configDivisionsCodes);
        }
    );

    foreach ($configDivisions as $configDivision) {
        $isFound = false;
        foreach ($selectableDivisions as $selectableDivision) {
            if ($selectableDivision->code === $configDivision->code) {
                $isFound = true;
            }
        }

        if (false === $isFound) {
            $selectableDivisions[] = $configDivision;
        }
    }


    // Set included and excluded flags
    foreach ($selectableDivisions as &$division) {
        $division->included = \in_array($division->code, $includedDivisions);
        $division->excluded = \in_array($division->code, $excludedDivisions);
    }

    $companyUpdate = false === \is_null($params['company']) && \is_null($params['product']);

    $adminUpdate = (
        \is_null($params['product'])
        && Registry::get('runtime.company_id') === 0
    );

    // Warning message
    $warningMsgKey =
        true === $companyUpdate
            ? 'division_vendor_edit_warning'
            : null;

    return $container->get('templating')->renderResponse('@App/backend/available_offers/divisions.html.twig', [
        'ajax_load' => true,
        'divisions' => $selectableDivisions,
        'companyUpdate' => $companyUpdate,
        'adminUpdateDivisionBlacklist' => $adminUpdate,
        'warningMsgKey' => $warningMsgKey,
    ]);
}
