<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON>halnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\Registry;
use Wizacha\Marketplace\GlobalState\GlobalState;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

//
// Currently defined sections:
// A - menu
// ----------------------

//
// Defaults
//
$section = empty($_REQUEST['section']) ? 'N' : $_REQUEST['section'];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $schema = fn_get_schema('static_data', 'schema');
    $section_data = $schema[$section];

    $redirect_url = "static_data.manage?section=$section";

    if (!empty($section_data['owner_object'])) {
        $param = $section_data['owner_object'];
        if (!empty($_REQUEST[$param['key']])) {
            $redirect_url .= "&" . $param['key'] . "=" . $_REQUEST[$param['key']];
        }
    }

    if ($mode == 'update') {
        fn_update_static_data($_REQUEST['static_data'], $_REQUEST['param_id'], $section);
    }

    if ($mode == 'm_update') {
        if (!empty($_REQUEST['static_data'])) {
            foreach ($_REQUEST['static_data'] as $k => $v) {
                $localizations = db_get_field("SELECT localization FROM ?:static_data WHERE section = ?s AND param_id = ?i", $section, $k);
                $v['localization'] = fn_explode_localizations($localizations);
                fn_update_static_data($v, $k, $section);
            }
        }
    }

    if ($mode == 'm_delete') {
        if (!empty($_REQUEST['static_data_ids'])) {
            foreach ($_REQUEST['static_data_ids'] as $k => $v) {
                fn_delete_static_data($v);
            }
        }
    }

    return array(CONTROLLER_STATUS_OK, $redirect_url);
}

//
// Delete
//
if ($mode == 'delete') {
    fn_delete_static_data($_REQUEST['param_id']);

    return array(CONTROLLER_STATUS_OK, fn_url('static_data.manage?section=' . $_REQUEST['section'] . '&menu_id=' . $_REQUEST['menu_id']));

} elseif ($mode == 'update') {

    $schema = fn_get_schema('static_data', 'schema');
    $section_data = $schema[$section];
    Registry::get('view')->assign('section_data', $section_data);

    $static_data = db_get_row("SELECT sd.*, ?:static_data_descriptions.descr FROM ?:static_data AS sd LEFT JOIN ?:static_data_descriptions ON sd.param_id = ?:static_data_descriptions.param_id AND ?:static_data_descriptions.lang_code = ?s WHERE sd.section = ?s AND sd.param_id = ?i", (string) GlobalState::contentLocale(), $_REQUEST['section'], $_REQUEST['param_id']);

    if (!empty($section_data['icon'])) {
        $static_data['icon'] = fn_get_image_pairs($static_data['param_id'], $section_data['icon']['type'], 'M', true, true, (string) GlobalState::contentLocale());
    }

    if (!empty($section_data['multi_level'])) {
        $params = array(
            'section' => $_REQUEST['section'],
            'generate_levels' => true,
            'get_params' => true,
            'multi_level' => true,
            'plain' => true,
        );
        Registry::get('view')->assign('parent_items', fn_get_static_data($params));
    }

    if (!empty($section_data['owner_object']['check_owner_function']) && function_exists($section_data['owner_object']['check_owner_function'])) {
        if ($section_data['owner_object']['check_owner_function']($_REQUEST[$section_data['owner_object']['key']]) == false) {
            return array(CONTROLLER_STATUS_NO_PAGE);
        }
    }

    Registry::get('view')->assign('static_data', $static_data);

    Registry::get('view')->assign('section', $section);

} elseif ($mode == 'manage') {

    $schema = fn_get_schema('static_data', 'schema');
    $section_data = $schema[$section];
    Registry::get('view')->assign('section_data', $section_data);

    $params = array(
        'section' => $_REQUEST['section'],
        'multi_level' => !empty($section_data['multi_level']),
        'generate_levels' => !empty($section_data['multi_level']),
        'icon_name' => !empty($section_data['icon']) ? $section_data['icon']['type'] : '',
        'get_params' => true
    );
    $static_data = fn_get_static_data($params);

    if (!empty($section_data['multi_level'])) {
        $params = array(
            'section' => $_REQUEST['section'],
            'generate_levels' => true,
            'get_params' => true,
            'multi_level' => true,
            'plain' => true,
        );
        Registry::get('view')->assign('parent_items', fn_get_static_data($params));
    }

    if (!empty($section_data['owner_object']['name_function']) && function_exists($section_data['owner_object']['name_function'])) {
        Registry::get('view')->assign('owner_object_name', $section_data['owner_object']['name_function']($_REQUEST[$section_data['owner_object']['key']]));
    }

    if (!empty($section_data['owner_object']['check_owner_function']) && function_exists($section_data['owner_object']['check_owner_function'])) {
        if ($section_data['owner_object']['check_owner_function']($_REQUEST[$section_data['owner_object']['key']]) == false) {
            return array(CONTROLLER_STATUS_NO_PAGE);
        }
    }

    Registry::get('view')->assign('static_data', $static_data);
    Registry::get('view')->assign('section', $section);
}
