<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON>halnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\Registry;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Order\OrderDataType;
use Wizacha\Marketplace\Order\OrderStatus;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

// Generate dashboard
if ($mode == 'index') {

    $time_from = !empty($_REQUEST['time_from']) ? $_REQUEST['time_from'] : strtotime('-30 day');
    $time_to = !empty($_REQUEST['time_to']) ? $_REQUEST['time_to'] : strtotime('now');
    $time_difference = $time_to - $time_from;
    $is_day = ($time_to - $time_from) <= SECONDS_IN_DAY ? true : false;
    $workflowTranslation = container()->getParameter('feature.activate_workflow_translation');
    $general_stats = array();
    $orderService = container()->get('marketplace.order.order_service');

    /* Order */
    $orders_stat = [];

    if (fn_check_view_permissions('orders.manage', 'GET')
        || fn_check_view_permissions('sales_reports.view', 'GET')
        || fn_check_view_permissions('taxes.manage', 'GET')
    ) {
        $legacyStatusesToTrim = [
            OrderStatus::INCOMPLETED => "",
            OrderStatus::CANCELED => "",
            OrderStatus::BILLING_FAILED => "",
        ];

        $toDisplayOrderStatuses = [];

        if ($workflowTranslation === true) {
            foreach (
                \array_diff_key(
                    $orderService->getWorkflowStatuses(),
                    [
                        'workflow_credit-card-payment_pending-bank-validation_failed' => "",
                    ]
                )
            as $key => $value ) {
                $toDisplayOrderStatuses[] = $key;
            }
        } else {
            foreach (fn_get_statuses(STATUSES_ORDER, [], false, true) as $singleStatus) {
                if (\array_key_exists($singleStatus['status'], $legacyStatusesToTrim) === false) {
                    $toDisplayOrderStatuses[] = $singleStatus['status'];
                }
            }
        }

        $params = [
            'period' => 'C',
            'time_from' => $time_from,
            'time_to' => $time_to,
            'status' => $toDisplayOrderStatuses,
            'inclusive_status_list' => true,
        ];

        list($orders_stat['orders'], $search_params, $orders_stat['orders_total']) = fn_get_orders($params, 0, true);

        $time_difference = $time_to - $time_from;
        $params = [
            'period' => 'C',
            'time_from' => $time_from - $time_difference,
            'time_to' => $time_to - $time_difference,
            'status' => $toDisplayOrderStatuses,
            'inclusive_status_list' => true,
        ];

        list($orders_stat['prev_orders'], $search_params, $orders_stat['prev_orders_total']) = fn_get_orders($params, 0, true);

        $orders_stat['diff']['orders_count'] = count($orders_stat['orders']) - count($orders_stat['prev_orders']);
        $orders_stat['diff']['sales'] = fn_calculate_differences($orders_stat['orders_total']['gross_total'], $orders_stat['prev_orders_total']['gross_total']);
    }

    if (!fn_check_view_permissions('orders.manage', 'GET')) {
        $orders_stat['orders'] = array();
        $orders_stat['prev_orders'] = array();
    }

    if (!fn_check_view_permissions('sales_reports.view', 'GET')) {
        $orders_stat['orders_total'] = array();
        $orders_stat['prev_orders_total'] = array();
    }
    /* /Orders */

    /* Order statuses */
    $order_statuses = [];

    if (fn_check_view_permissions('orders.manage', 'GET')) {
        if ($workflowTranslation === true) {
            $order_statuses = \array_diff_key(
                $orderService->getWorkflowStatuses(),
                [
                    'workflow_credit-card-payment_pending-bank-validation_failed' => "",
                ]
            );
        } else {
            $order_statuses = \array_diff_key(fn_get_statuses(STATUSES_ORDER, [], false, true), $legacyStatusesToTrim);
        }
    }
    /* /Order statuses */

    /* Recent activity block */
    $logs = array();
    /* /Recent activity block */

    /* Order by statuses */
    $order_by_statuses = array();

    if (fn_check_view_permissions('orders.manage', 'GET')) {
        $company_condition = fn_get_company_condition('?:orders.company_id');

        $order_by_statuses = db_get_array(
                                    "SELECT "
                                        . "?:status_descriptions.description as status_name, "
                                        . "?:orders.status, "
                                        . "?:orders.workflow_status, "
                                        . "?:orders.workflow_current_step_name, "
                                        . "?:orders.workflow_current_module_name, "
                                        . "?:orders.canceled, "
                                        . "?:orders.refunded, "
                                        . "COUNT(*) as count, "
                                        . "SUM(?:orders.total) as total, "
                                        . "SUM(?:orders.shipping_cost) as shipping "
                                    . "FROM ?:orders "
                                    . "INNER JOIN ?:status_descriptions "
                                        . "ON ?:status_descriptions.status = ?:orders.status "
                                    . "WHERE ?:status_descriptions.type = ?s "
                                        . "AND ?:orders.timestamp > ?i "
                                        . "AND ?:orders.timestamp < ?i "
                                        . "AND ?:status_descriptions.lang_code = ?s "
                                        . "?p "
                                    . "GROUP BY ?:orders.status ",
                                    'O', $time_from, $time_to, (string) GlobalState::interfaceLocale(), $company_condition);

        $refunded = 0;

        foreach ($order_by_statuses as $key => &$order) {
            if (\array_key_exists($order['status'], $legacyStatusesToTrim) === true) {
                unset($order_by_statuses[$key]);
                continue;
            }

            $order['workflow_url'] = "workflow_{$order['workflow_current_module_name']}_{$order['workflow_current_step_name']}_{$order['workflow_status']}";
            $order['workflow_step'] = str_replace('-', '_', $order['workflow_url']);

            if ($order['refunded']) {
                $order['workflow_url'] = 'workflow_refunded';
                $order['workflow_step'] = $order['workflow_url'];
                $refunded++;
            }
        }

        if ($workflowTranslation) {
            if ($refunded === 0) {
                unset($order_statuses['workflow_refunded']);
            }
        }
    }
    /* /Order by statuses */

    /* Statistics */
    $graphs = fn_dashboard_get_graphs_data($time_from, $time_to, $is_day);
    /* /Statistics */

    Registry::get('view')->assign('workflowTranslation', $workflowTranslation);

    Registry::get('view')->assign('general_stats', $general_stats);
    Registry::get('view')->assign('orders_stat', $orders_stat);
    Registry::get('view')->assign('logs', $logs);

    Registry::get('view')->assign('allowed_status', $toDisplayOrderStatuses);
    Registry::get('view')->assign('order_statuses', $order_statuses);

    Registry::get('view')->assign('graphs', $graphs);
    Registry::get('view')->assign('is_day', $is_day);

    Registry::get('view')->assign('time_from', $time_from);
    Registry::get('view')->assign('time_to', $time_to);

    Registry::get('view')->assign('order_by_statuses', $order_by_statuses);
}

function fn_get_orders_taxes_subtotal($orders, $params)
{
    $subtotal = 0;

    if (!empty($orders)) {
        $_oids = array();
        foreach ($orders as $order) {
            if (in_array($order['status'], $params['paid_statuses'])) {
                $oids[] = $order['order_id'];
            }
        }

        if (empty($oids)) {
            return $subtotal;
        }

        $taxes = db_get_fields(
            'SELECT data FROM ?:order_data WHERE order_id IN (?a) AND type = ?s',
            $oids,
            OrderDataType::TAX_INFO()->getValue()
        );

        if (!empty($taxes)) {
            foreach ($taxes as $tax) {
                $tax = unserialize($tax);
                foreach ($tax as $id => $tax_data) {
                    $subtotal += !empty($tax_data['tax_subtotal']) ? $tax_data['tax_subtotal'] : 0;
                }
            }
        }
    }

    return $subtotal;
}

function fn_calculate_differences($new_value, $old_value): string
{
    return $old_value > 0
        ? number_format(
            (($new_value - $old_value) / $old_value) * 100,
            2
        )
        : '&infin;'
    ;
}

function fn_dashboard_get_graphs_data($time_from, $time_to, $is_day)
{
    $company_condition = fn_get_company_condition('?:orders.company_id');

    $graphs = array();
    $graph_tabs = array();

    $time_to = mktime(23, 59, 59, date("n", $time_to), date("j", $time_to), date("Y", $time_to));

    if (fn_check_view_permissions("sales_reports.view", "GET")) {
        $graphs['dashboard_statistics_sales_chart'] = array();
        $paid_statuses = \Wizacha\OrderStatus::getPaidStatuses();

        for ($i = $time_from; $i <= $time_to; $i = $i + ($is_day ? 60*60 : SECONDS_IN_DAY)) {
            $date = !$is_day ? date("Y, (n-1), j", $i) : date("H", $i);
            if (empty($graphs['dashboard_statistics_sales_chart'][$date])) {
                $graphs['dashboard_statistics_sales_chart'][$date] = array(
                    'cur' => 0,
                    'prev' => 0,
                );
            }
        }

        $sales = db_get_array("SELECT "
                                . "?:orders.timestamp, "
                                . "?:orders.total "
                            . "FROM ?:orders "
                            . "WHERE ?:orders.timestamp BETWEEN ?i AND ?i "
                                . "AND ?:orders.status IN (?a) "
                                . "?p ",
                            $time_from, $time_to, $paid_statuses, $company_condition);
        foreach ($sales as $sale) {
            $date = !$is_day ? date("Y, (n-1), j", $sale['timestamp']) : date("H", $sale['timestamp']);
            $graphs['dashboard_statistics_sales_chart'][$date]['cur'] += $sale['total'];
        }

        $sales_prev = db_get_array("SELECT "
                                    . "?:orders.timestamp, "
                                    . "?:orders.total "
                                . "FROM ?:orders "
                                . "WHERE ?:orders.timestamp BETWEEN ?i AND ?i "
                                    . "AND ?:orders.status IN (?a) "
                                    . "?p ",
                                $time_from - ($time_to - $time_from), $time_from, $paid_statuses, $company_condition);
        foreach ($sales_prev as $sale) {
            $date = $sale['timestamp'] + ($time_to - $time_from);
            $date = !$is_day ? date("Y, (n-1), j", $date) : date("H", $date);
            $graphs['dashboard_statistics_sales_chart'][$date]['prev'] += $sale['total'];
        }

        $graph_tabs['sales_chart'] = array (
            'title' => __('sales'),
            'js' => true
        );
    }

    Registry::set('navigation.tabs', $graph_tabs);

    return $graphs;
}
