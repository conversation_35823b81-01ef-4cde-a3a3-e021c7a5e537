<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON>halnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Tygh\Registry;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

// Code utilisé par `admin.php?dispatch=tools.update_status&...`
// log_unused_code(__FILE__, __LINE__);

fn_define('DB_MAX_ROW_SIZE', 10000);
fn_define('DB_ROWS_PER_PASS', 140);

if ($mode == 'cleanup_history') {
    $_SESSION['last_edited_items'] = array();
    fn_save_user_additional_data('L', '');
    Registry::get('view')->assign('last_edited_items', '');

    return new Response(
        Registry::get('view')->fetch('common/last_viewed_items.tpl')
    );

} elseif ($mode == 'update_status') {

    fn_tools_update_status($_REQUEST);

    if (empty($_REQUEST['redirect_url'])) {
        return new JsonResponse();
    }
} elseif ($mode == 'update_position') {

    if (preg_match("/^[a-z_]+$/", $_REQUEST['table'])) {
        $table_name = $_REQUEST['table'];
    } else {
        return new JsonResponse();
    }

    $id_name = $_REQUEST['id_name'];
    $ids = explode(',', $_REQUEST['ids']);
    $positions = explode(',', $_REQUEST['positions']);

    foreach ($ids as $k => $id) {
        db_query("UPDATE ?:$table_name SET position = ?i WHERE ?w", $positions[$k], array($id_name => $id));
    }

    fn_set_notification('N', __('notice'), __('positions_updated'));

    return new JsonResponse();
}
