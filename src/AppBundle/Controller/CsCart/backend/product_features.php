<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON>nev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\Registry;
use Wizacha\Marketplace\GlobalState\GlobalState;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

fn_define('KEEP_UPLOADED_FILES', true);
fn_define('NEW_FEATURE_GROUP_ID', 'OG');

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    fn_trusted_vars ('feature_data');

    // Update features
    if ($mode == 'update') {
        $featureId = fn_update_product_feature($_REQUEST['feature_data'], $_REQUEST['feature_id'], (string) GlobalState::contentLocale());

        if ($featureId > 0) {
            fn_set_notification('N', __('notice'), __('text_if_attribute_has_been_updated'));
        }
    }
    return array(CONTROLLER_STATUS_OK, "product_features.manage");
}

if ($mode == 'update') {

    Registry::get('view')->assign('feature', fn_get_product_feature_data($_REQUEST['feature_id'], false, false, (string) GlobalState::contentLocale()));
    Registry::get('view')->assign('featurePlaceholder', fn_get_product_feature_data($_REQUEST['feature_id'], false, false, (string) GlobalState::interfaceLocale()));
    list($group_features) = fn_get_product_features(array('feature_types' => 'G'), 0, (string) GlobalState::contentLocale());
    Registry::get('view')->assign('group_features', $group_features);
    Registry::get('view')->assign('attributes_in_groups_can_override_categories', container()->getParameter('feature.attributes_in_groups_can_override_categories'));

} elseif ($mode == 'delete') {

    if (!empty($_REQUEST['feature_id'])) {
        if (!array_key_exists('superadmin', $_GET) &&
            in_array($_REQUEST['feature_id'], container()->getParameter('readonly_attributes'))) {

            fn_set_notification('E', __('error'), __('cannot_update_delete_protected_attribute'));
        } else {
            $featureIsDeleted = fn_delete_feature($_REQUEST['feature_id']);
            if ($featureIsDeleted === true) {
                fn_set_notification('N', __('notice'), __('text_if_attribute_has_been_updated'));
            }
        }
    }

    return array(CONTROLLER_STATUS_OK);

} elseif ($mode == 'manage') {

    $params = $_REQUEST;
    $params['exclude_group'] = true;
    $params['get_descriptions'] = true;
    list($features, $search, $has_ungroupped) = fn_get_product_features($params, Registry::get('settings.Appearance.admin_elements_per_page'), (string) GlobalState::contentLocale());

    Registry::get('view')->assign('features', $features);
    Registry::get('view')->assign('search', $search);
    Registry::get('view')->assign('has_ungroupped', $has_ungroupped);

    if (empty($features) && defined('AJAX_REQUEST')) {
        Registry::get('ajax')->assign('force_redirection', fn_url('product_features.manage'));
    }

    list($group_features) = fn_get_product_features(array('feature_types' => 'G'), 0, (string) GlobalState::contentLocale());
    Registry::get('view')->assign('group_features', $group_features);
    Registry::get('view')->assign('attributes_in_groups_can_override_categories', container()->getParameter('feature.attributes_in_groups_can_override_categories'));
    Registry::get('view')->assign('total_features', intval(db_get_field("SELECT COUNT(*) FROM ?:product_features WHERE is_searchable = 'Y' AND status = 'A'")));

} elseif ($mode == 'get_variants') {

    $params = $_REQUEST;
    $params['get_images'] = true;
    [$variants, $search] = fn_get_product_feature_variants($params, Registry::get('settings.Appearance.admin_elements_per_page'), (string) GlobalState::contentLocale());
    [$variantPlaceholder, $search] = fn_get_product_feature_variants($params, Registry::get('settings.Appearance.admin_elements_per_page'), (string) GlobalState::interfaceLocale());

    Registry::get('view')->assign('feature_variants', $variants);
    Registry::get('view')->assign('featureVariantsPlaceholder', $variantPlaceholder);
    Registry::get('view')->assign('search', $search);
    Registry::get('view')->assign('feature_type', $_REQUEST['feature_type']);
    Registry::get('view')->assign('id', $_REQUEST['feature_id']);
    Registry::get('view')->display('views/product_features/components/variants_list.tpl');
    exit;
} elseif ($mode == 'update_status') {

    if (!array_key_exists('superadmin', $_GET) &&
        in_array($_REQUEST['feature_id'], container()->getParameter('readonly_attributes'))) {

        fn_set_notification('E', __('error'), __('cannot_update_delete_protected_attribute'));
        exit;
    }

    fn_tools_update_status($_REQUEST);

    if (!empty($_REQUEST['status']) && $_REQUEST['status'] == 'D') {
        $filter_ids = db_get_fields("SELECT filter_id FROM ?:product_filters WHERE feature_id = ?i AND status = 'A'", $_REQUEST['id']);
        if (!empty($filter_ids)) {
            db_query("UPDATE ?:product_filters SET status = 'D' WHERE filter_id IN (?n)", $filter_ids);
            $filter_names_array = db_get_fields("SELECT filter FROM ?:product_filter_descriptions WHERE filter_id IN (?n) AND lang_code = ?s", $filter_ids, (string) GlobalState::contentLocale());

            fn_set_notification('W', __('warning'), __('text_product_filters_were_disabled', array(
                '[url]' => fn_url('product_filters.manage'),
                '[filters_list]' => implode(', ', $filter_names_array)
            )));
        }
    }

    exit;
}
