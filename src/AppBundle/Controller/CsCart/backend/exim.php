<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, Ilya M<PERSON> Shalnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Psr\Log\LoggerInterface;
use Tygh\Languages\Languages;
use Tygh\Registry;
use Wizacha\Component\Import\EximJob;
use Wizacha\Component\Import\EximJobRepository;
use Wizacha\Component\Import\EximJobService;
use Wizacha\Component\Import\JobStatus;
use Wizacha\Component\Import\JobType;
use Wizacha\Cscart\Exim;
use Wizacha\Exim\AsyncExportHasFinished;
use Wizacha\Exim\Exception\InvalidFormatDateTimeException;
use Wizacha\Exim\Misc;
use Wizacha\Exim\Orders\DateRangeSqlQueryBuilder;
use Wizacha\Marketplace\Accounting\Period;
use Wizacha\Marketplace\Division\Service\DivisionService;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

define('SQL_AND', ' AND ');
define('DB_LIMIT_SELECT_ROW', 128);

// Set line endings autodetection
ini_set('auto_detect_line_endings', true);

if (empty($_SESSION['export_ranges'])) {
    $_SESSION['export_ranges'] = array();
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $suffix = '';

    $layout_data = !empty($_REQUEST['layout_data']) ? $_REQUEST['layout_data'] : array();
    //
    // Select layout
    //
    if ($mode == 'set_layout') {
        db_query("UPDATE ?:exim_layouts SET active = 'N' WHERE pattern_id = ?s", $layout_data['pattern_id']);
        db_query("UPDATE ?:exim_layouts SET active = 'Y' WHERE layout_id = ?i", $layout_data['layout_id']);

        return array(CONTROLLER_STATUS_OK, "exim.export?section=$_REQUEST[section]&pattern_id=$layout_data[pattern_id]");
    }

    //
    // Store layout
    //
    if ($mode == 'store_layout') {

        if (!empty($layout_data['cols'])) {
            $layout_data['cols'] = implode(',', $layout_data['cols']);

            // Update current layout
            if ($action == 'save_as') {

                // On export, the filename can't have a /, and the layout name is used has prefix for the filename.
                // So we replace the /.
                $layout_data['name'] = str_replace('/', '-', $layout_data['name']);

                unset($layout_data['layout_id']);
                if (!empty($layout_data['name'])) {
                    $layout_data['active'] = 'Y';
                    db_query("UPDATE ?:exim_layouts SET active = 'N' WHERE pattern_id = ?s", $layout_data['pattern_id']);
                    db_query("INSERT INTO ?:exim_layouts ?e", $layout_data);

                    return array(CONTROLLER_STATUS_OK, "exim.export?section=$_REQUEST[section]&pattern_id=$layout_data[pattern_id]");
                }
            } else {
                if (!empty($layout_data['layout_id'])) {
                    unset($layout_data['name']);
                    db_query("UPDATE ?:exim_layouts SET ?u WHERE layout_id = ?i", $layout_data, $layout_data['layout_id']);
                }
            }
        }

        return array(CONTROLLER_STATUS_OK, "exim.export?section=$_REQUEST[section]&pattern_id=$layout_data[pattern_id]");
    }

    //
    // Delete layout
    //
    if ($mode == 'delete_layout') {
        db_query("DELETE FROM ?:exim_layouts WHERE layout_id = ?i", $layout_data['layout_id']);

        return array(CONTROLLER_STATUS_OK, "exim.export?section=$_REQUEST[section]&pattern_id=$layout_data[pattern_id]");
    }

    //
    // Perform export
    //
    if ($mode == 'export') {
        $_suffix = '';
        if (!empty($layout_data['cols'])) {
            $pattern = fn_get_pattern_definition($layout_data['pattern_id'], 'export');

            if (empty($pattern)) {
                fn_set_notification('E', __('error'), __('error_exim_pattern_not_found'));
                exit;
            }

            if (!empty($_SESSION['export_ranges'][$pattern['section']])) {
                if (empty($pattern['condition']['conditions'])) {
                    $pattern['condition']['conditions'] = array();
                }

                $pattern['condition']['conditions'] = fn_array_merge($pattern['condition']['conditions'], $_SESSION['export_ranges'][$pattern['section']]['data']);
            }

            $emailMode = $_REQUEST['export_options']['output'] === 'E';

            if (true === isset($_REQUEST['export_options']['filename'])) {
                // Remove '/' to prevent acces to subdirectory
                $_REQUEST['export_options']['filename'] = str_replace(
                    '/',
                    '-',
                    $_REQUEST['export_options']['filename']
                );
            }

            if ($pattern['section'] === 'accounting' && isset($_REQUEST['export_options']['filename']) === false) {
                if ($_REQUEST['date_from'] === ''
                    || $_REQUEST['date_to'] === ''
                ) {
                    $_REQUEST['export_options']['filename'] = 'Accounting_export_'.date('d-m-Y').'_from_'.date('d-m-Y', strtotime('first day of january this year')).'_to_'.date('d-m-Y', strtotime('-1 day', Period::getPeriod(time(), 1)['end'])).'.csv';
                } else {
                    $_REQUEST['export_options']['filename'] = 'Accounting_export_'.date('d-m-Y').'_from_'.str_replace('/', '-', $_REQUEST['date_from']).'_to_'.str_replace('/', '-', $_REQUEST['date_to']).'.csv';
                }

                // make travel date_range to the worker payload
                $_REQUEST['export_options']['date_range'] = [
                    'from' => $_REQUEST['date_from'],
                    'to' => $_REQUEST['date_to'],
                ];
            }

            if ($pattern['section'] === 'orders') {
                // we must preserve the date range from
                // the view date range component selector and use it in DateRangeSqlQueryBuilder
                // to manage the async (email)
                // and sync mode (download) in the fn_export_build_conditions function
                $_REQUEST['export_options']['date_range'] = [
                    'time_from' => $_REQUEST['time_from'],
                    'time_to' => $_REQUEST['time_to'],
                ];
            }

            // make travel user infos to the worker payload
            $_REQUEST['export_options']['user_id'] = $_SESSION['auth']['user_id'];
            $_REQUEST['export_options']['company_id'] = Registry::get('runtime.company_id');
            $_REQUEST['export_options']['email_mode'] = $emailMode;

            //Encode une chaîne en URL, selon la RFC 3986
            $filename = rawurlencode($_REQUEST['export_options']['filename']);
            $url = fn_url("exim.get_file?filename=" . $filename, 'A', 'current');

            if($emailMode){
                 $_REQUEST['export_options']['url'] = $url;
            }

            $companyId = Registry::get('runtime.company_id');
            $jobService = container()->get('marketplace.import.job_service');
            $pendingJobIds = $jobService->getPendingExportJobIds($companyId);

            // Check if there is not already pending async job for current company (if logged as vendor)
            if (
                true === $emailMode
                && true === $jobService->hasPendingJobIds($companyId, $pendingJobIds)
            ) {
                fn_set_notification('E', __('error'), __('exim_error_export_already_exist'));

                return false;
            }

            if (
                true === fn_export(
                    $pattern,
                    $layout_data['cols'],
                    $_REQUEST['export_options'],
                    $emailMode # async for emailing results
                )
            ) {

                if ($emailMode) {
                    fn_set_notification('N', __('notice'), __('csv_export_complete_notification'));

                    exit; // let's the worker do the job.
                }else{
                    // Notification only if we don't send a redirection
                    fn_set_notification('N', __('notice'), __('text_exim_data_exported'));
                }

                if (defined('AJAX_REQUEST') && !empty($url)) {
                    Registry::get('ajax')->assign('force_redirection', $url);

                    exit;
                } else {
                    // Notification only if we don't send a redirection
                    fn_set_notification('N', __('notice'), __('text_exim_data_exported'));
                }

                $url = empty($url) ? fn_url('exim.export?section=' . $_REQUEST['section']) : $url;

                return array(CONTROLLER_STATUS_OK, $url);
            } else {
                fn_set_notification('E', __('error'), __('error_exim_no_data_exported'));
            }
        } else {
            fn_set_notification('E', __('error'), __('error_exim_fields_not_selected'));
        }

        exit;
    }

    //
    // Perform import
    //
    if ($mode == 'import') {

        if (empty($_REQUEST['pattern_id'])) {
            fn_set_notification('E', __('error'), __('error_exim_pattern_not_found'));
        } else {
            $pattern = fn_get_pattern_definition($_REQUEST['pattern_id'], 'import');
            $jobType = Misc::patternJobTypeMapping($_REQUEST['pattern_id']);

            switch($_REQUEST['import_options']['delimiter']){
                case 'C':
                    $delimiter = ',';
                    break;
                case 'T':
                    $delimiter = "\t";
                    break;
                case 'P':
                    $delimiter = "|";
                    break;
                default:
                    $delimiter = ';';
            }

            try {
                $userId = \Wizacha\Registry::defaultInstance()->get(['runtime', 'user_id']) ?? $auth['user_id'];
                $user = container()->get('marketplace.user.user_service')->get($userId);
            } catch (\Exception $e) {
                $user = null;
            }

            try {
                if (!empty($pattern['pre_import'])) {
                    foreach ($pattern['pre_import'] as $schema) {
                        $args = Exim::fn_exim_get_values($schema['args'], [], [], [], [
                            'company_id' => \Wizacha\Registry::defaultInstance()->get(['runtime', 'company_id']),
                            'user'       => $user,
                        ], '');
                        call_user_func_array($schema['function'], $args);
                    }
                }

                $importHandler = container()->get('marketplace.import.import_factory')->getHandlerFromLegacy(
                    $request,
                    $jobType,
                    $auth['user_id'],
                    $_SESSION['settings']['company_id']['value'] ?? $auth['company_id'],
                    $pattern,
                    $delimiter
                );

                switch ($jobType) {
                    case JobType::PRODUCTS():
                        container()->get('marketplace.import.import_service')->catalog($importHandler);
                        break;
                    case JobType::PRODUCT_PRICES():
                        container()->get('marketplace.import.import_service')->importInventory($importHandler, 'prices');
                        break;
                    case JobType::PRODUCT_QUANTITIES():
                        container()->get('marketplace.import.import_service')->importInventory($importHandler, 'quantities');
                        break;
                    case JobType::TRANSLATIONS():
                        container()->get('marketplace.import.import_service')->translations($importHandler);
                        break;
                    default:
                        container()->get('marketplace.import.import_service')->entities($importHandler);
                        break;
                }

                if (!empty($pattern['post_import'])) {
                    foreach ($pattern['post_import'] as $schema) {
                        $args = Exim::fn_exim_get_values($schema['args'], [], [], [], [
                            'company_id' => \Wizacha\Registry::defaultInstance()->get(['runtime', 'company_id']),
                        ], '');
                        call_user_func_array($schema['function'], $args);
                    }
                }

                fn_set_notification('W', __('important'), __('text_exim_data_imported', [
                    '[total]'   => $importHandler->getJob()->getNbLines(),
                ]));
            }  catch (\Throwable $e) {

                if(isset($importHandler) && $importHandler->getJob()->getId()) {
                    EximJobService::error(
                        $e->getMessage(),
                        $importHandler->getJob()->getId(),
                        $importHandler->getJob()->getNbLines() + 1 // Its a fake last line to set status
                    );
                    $jobService = container()->get('marketplace.import.job_service');
                    $job = $jobService->get($importHandler->getJob()->getId());
                    $job->setStatus(JobStatus::ERROR());
                    $job->finish();
                    $jobService->save($job);
                }

                fn_set_notification('E', __('error'), $e->getMessage());
            }

            // On refresh les infos dans Algolia
            $searchProductIndex = container()->get('marketplace.search.product_index');
        }

        if (defined('API')) {
            return array(CONTROLLER_STATUS_OK);
        }
        return array(CONTROLLER_STATUS_OK, "exim.import?section=$_REQUEST[section]&pattern_id=$_REQUEST[pattern_id]");
    }

    exit;
}

if ($mode == 'export') {

    if ($_REQUEST['section'] === "divisions") {
        $now = (new \DateTime())->format("Y_m_d_H_i_s");
        $filename = "available_offers_divisions-${now}.csv";

        $options = [
            'delimiter' => "S",
            'filename' => $filename,
        ];

        $runtimeCompanyId = Registry::get('runtime.company_id');
        $companyId = ($runtimeCompanyId > 0) ? $runtimeCompanyId : null;

        /** @var DivisionService $divisionService */
        $divisionService = container()->get('marketplace.division.service');

        $data = $divisionService->getDivisionsCsv($companyId);

        if (empty($data)) {
            fn_set_notification('E', __('error'), __('error_exim_no_data_exported'));
            $url = fn_url("", 'A', 'current');
            return array(CONTROLLER_STATUS_OK, $url);
        }

        $fileDescriptor = Exim::getCsvOutputFileDescriptor($filename);
        fn_put_csv($fileDescriptor, $data, $options, '"');
        \fclose($fileDescriptor);

        $prefix = Registry::get('runtime.company_id') ? Registry::get('runtime.company_id').'/' : '';
        container()->get('Wizacha\Storage\CsvStorageService')
            ->put(
                $prefix.$filename,
                [
                    'file' => Registry::get('config.dir.exim') . $filename,
                    'overwrite' => true,
                    'keep_origins' => false,
                ]
            )
        ;

        $url = fn_url("exim.get_file?filename=${filename}", 'A', 'current');
        return array(CONTROLLER_STATUS_OK, $url);
    }

    if (empty($_REQUEST['section'])) {
        $_REQUEST['section'] = 'products';
    }

    [$sections, $patterns] = fn_get_patterns($_REQUEST['section'], 'export');

    if (empty($sections) && empty($patterns) || (isset($_REQUEST['section']) && empty($sections[$_REQUEST['section']]))) {
        return array(CONTROLLER_STATUS_DENIED);
    }

    if (!array_key_exists('superadmin', $_GET)) {
        $exportSections = [
            'categories',
            'orders',
            'products',
            'features',
            'accounting',
            'companies',
            'users',
            'related_products',
            'product_attributes',
        ];

        if (container()->getParameter('feature.multi_vendor_product') === true) {
            $exportSections[] = 'multi_vendor_products';
        }

        if (container()->getParameter('feature.marketplace_discounts') === true) {
            $exportSections[] = 'marketplace_discounts';
        }

        $sections = array_intersect_key($sections, array_flip($exportSections));
    }

    $pattern_id = (empty($_REQUEST['pattern_id']) || empty($patterns[$_REQUEST['pattern_id']])) ? key($patterns) : $_REQUEST['pattern_id'];

    // Tabs
    foreach ($patterns as $p_id => $p) {
        Registry::set('navigation.tabs.' . $p_id, array (
            'title' => $p['name'],
            'href' => "exim.export?pattern_id=" . $p_id . '&section=' . $_REQUEST['section'],
            'ajax' => true
        ));
    }

    if (!empty($_SESSION['export_ranges'][$_REQUEST['section']])) {
        $key = key($_SESSION['export_ranges'][$_REQUEST['section']]['data']);
        if (!empty($key)) {
            Registry::get('view')->assign('export_range', count($_SESSION['export_ranges'][$patterns[$pattern_id]['section']]['data'][$key]));
            Registry::get('view')->assign('active_tab', $_SESSION['export_ranges'][$_REQUEST['section']]['pattern_id']);
        }
    }

    // Get available layouts
    $layouts = db_get_array("SELECT * FROM ?:exim_layouts WHERE pattern_id = ?s", $pattern_id);

    // Extract columns information
    foreach ($layouts as $k => $v) {
        $layouts[$k]['cols'] = explode(',', $v['cols']);
    }

    // Export languages
    foreach (Languages::getAll() as $lang_code => $lang_data) {
        $export_langs[$lang_code] = $lang_data['name'];
    }

    Registry::set('navigation.dynamic.sections', $sections);
    Registry::set('navigation.dynamic.active_section', $_REQUEST['section']);

    Registry::get('view')->assign('pattern', $patterns[$pattern_id]);
    Registry::get('view')->assign('layouts', $layouts);

    Registry::get('view')->assign('export_langs', $export_langs);

    // Only for marketplace admin, vendor permission is not set for this url
    Registry::get('view')->assign('help_page_url', container()->getParameter('url_wizaplace_documentation'));

}
elseif ($mode == 'import') {

    if (empty($_REQUEST['section'])) {
        $_REQUEST['section'] = Registry::get('runtime.company_id') ? 'products' : 'categories';
    }

    [$sections, $patterns] = fn_get_patterns($_REQUEST['section'], 'import');

    if (empty($sections) && empty($patterns) || (isset($_REQUEST['section']) && empty($sections[$_REQUEST['section']]))) {
        return array(CONTROLLER_STATUS_DENIED);
    }

    if (!array_key_exists('superadmin', $_GET)) {
        $importSections = [
            'categories',
            'products',
            'related_products',
            'features',
            'product_attributes'
        ];

        if (container()->getParameter('feature.available_offers') === true) {
            $importSections[] = 'available_offers_divisions';
        }

        if (container()->getParameter('feature.multi_vendor_product') === true) {
            $importSections[] = 'multi_vendor_products';
        }

        $sections = array_intersect_key($sections, array_flip($importSections));
    }

    $pattern_id = empty($_REQUEST['pattern_id']) ? key($patterns) : $_REQUEST['pattern_id'];

    // Tabs
    if(count($patterns) > 1) {
        foreach ($patterns as $p_id => $p) {
            Registry::set('navigation.tabs.' . $p_id, array(
                'title' => $p['name'],
                'href' => "exim.import?pattern_id=" . $p_id . '&section=' . $_REQUEST['section'],
                'ajax' => true
            ));
        }
    }

    // Show help csv buttons
    $patterns[$pattern_id]['show_help_structure'] = $patterns[$pattern_id]['show_help_structure'] ?? true;

    Registry::set('navigation.dynamic.sections', $sections);
    Registry::set('navigation.dynamic.active_section', $_REQUEST['section']);

    unset($patterns[$pattern_id]['options']['lang_code']);
    Registry::get('view')->assign('pattern', $patterns[$pattern_id]);
    Registry::get('view')->assign('sections', $sections);

    // Only for marketplace admin, vendor permission is not set for this url
    Registry::get('view')->assign('help_page_url', container()->getParameter('url_wizaplace_documentation'));

}
elseif ($mode == 'get_file' && !empty($_REQUEST['filename'])) {
    $file = fn_basename($_REQUEST['filename']);
    $prefix = '';

    // Sur la ligne au dessus, on ne prend que la derniere partie de filename, ça permet d'eviter une tentative de remontée
    // dans le storage et de tenter d'aller voir les fichiers des autres
    if (Registry::get('runtime.company_id')) {
        $prefix = Registry::get('runtime.company_id').'/';
    // Pour l'admin, on l'autorise a acceder aux fichiers des vendeurs.
    } elseif (strpos($_REQUEST['filename'], '/') !== false && preg_match('/^[0-9]\/[^\/]+$/', $_REQUEST['filename'])) {
        $file = $_REQUEST['filename'];
    }

    return container()->get('Wizacha\Storage\CsvStorageService')->get(
        $prefix.$file
    );

}
elseif ($mode == 'delete_file' && !empty($_REQUEST['filename'])) {
    $file = fn_basename($_REQUEST['filename']);
    $prefix = '';

    if (Registry::get('runtime.company_id')) {
        $prefix = Registry::get('runtime.company_id').'/';
    } elseif (strpos($_REQUEST['filename'], '/') !== false && preg_match('/^[0-9]\/[^\/]+$/', $_REQUEST['filename'])) {
        $file = $_REQUEST['filename'];
    }

    container()->get('Wizacha\Storage\CsvStorageService')->delete($prefix.$file);

    return array(CONTROLLER_STATUS_REDIRECT);

}
elseif ($mode == 'delete_range') {
    unset($_SESSION['export_ranges'][$_REQUEST['section']]);

    return array(CONTROLLER_STATUS_REDIRECT, "exim.export?section=$_REQUEST[section]&pattern_id=$_REQUEST[pattern_id]");

}
elseif ($mode == 'select_range') {
    $_SESSION['export_ranges'][$_REQUEST['section']] = array (
        'pattern_id' => $_REQUEST['pattern_id'],
        'data' => array(),
    );
    $pattern = fn_get_pattern_definition($_REQUEST['pattern_id']);

    return array(CONTROLLER_STATUS_REDIRECT, $pattern['range_options']['selector_url']);

}

// --------- ExIm core functions ------------------
//
// Export data using pattern
// Parameters:
// @pattern - import/export pattern
// @export_fields - export defined fields only
// @options - export options
// FIXME: add export conditions

function fn_export($pattern, $export_fields, $options, bool $asynchronous = false)
{
    if ($asynchronous
        && container()
            ->get('marketplace.async_dispatcher')
            ->delayExec(__METHOD__, func_get_args())
    ) {
        return true;
    }

    if (true === \defined('WORKER')
        && true === WORKER
    ) {
        set_time_limit(60 * 60 * 6); # 6 hours ...
        ini_set('memory_limit', '3G');
    }

    if (empty($pattern) || empty($export_fields)) {
        return false;
    }

    define('CSV_EXPORT', true);

    // Exim job
    $eximJob = new EximJob(
        Misc::patternJobTypeMapping($pattern['section']),
        (int) $options['user_id'],
        false
    );
    $eximJob->setCompanyId(Registry::get('runtime.company_id'));
    /** @var EximJobRepository */
    $eximJobRepository = container()->get('marketplace.import.job_repository');

    // Prevent user to put csv file in subdirectory
    $options['filename'] = fn_basename($options['filename']);
    $csvOutputPath = Exim::getCsvOutputFilePath($options['filename']);
    $fileDescriptor = Exim::getCsvOutputFileDescriptor($options['filename']);

    if (false === \flock($fileDescriptor, LOCK_EX | LOCK_NB)) {
        // Current export already in progress

        /** @var LoggerInterface */
        $logger = container()->get('logger');
        $logger->error(
            'Exim export error: job already processed',
            ['csvOutputPath' => $csvOutputPath]
        );

        return false;
    }

    // Languages
    if (!empty($options['lang_code'])) {
        $multi_lang = $options['lang_code'];
        $count_langs = count($multi_lang);
    } else {
        $multi_lang = array(DEFAULT_LANGUAGE);
        $count_langs = 1;
        $options['lang_code'] = $multi_lang;
    }

    $can_continue = true;

    if (!empty($pattern['export_pre_moderation'])) {
        $data_export_pre_moderation = array(
            'pattern' => &$pattern,
            'export_fields' => &$export_fields,
            'options' => &$options,
            'can_continue' => &$can_continue,
        );

        fn_exim_processing('export', $pattern['export_pre_moderation'], $options, $data_export_pre_moderation);
    }

    if (!$can_continue) {
        return false;
    }

    if (!empty($pattern['pre_processing'])) {
        fn_exim_processing('export', $pattern['pre_processing'], $options);
    }

    $data_exported = false;

    // export_custom_function property allows retrieving formatted data from a callback function,
    // in place of the query automatically build from the schema definition which has some limitations.
    if (false === \is_null($pattern['export_custom_function'])) {
        $data_exported = true;

        // Call the specific function to retrieve data
        $data = call_user_func($pattern['export_custom_function'], array_values($export_fields), $options);

        $nbLines = \count($data);

        if (0 === $nbLines){
            fn_set_notification('E', __('error'), __('error_exim_no_data_exported'));

            return false;
        }

        $eximJob->setStartedAt(new \DateTime());
        $eximJob->setNbLines($nbLines);
        $eximJob->setNbImported($nbLines);
        $eximJobRepository->save($eximJob);

        // Put data
        $enclosure = (isset($pattern['enclosure'])) ? $pattern['enclosure'] : '"';

        fn_put_csv($fileDescriptor, $data, $options, $enclosure);
    } else {
        $progress = 0;

        if (isset($options['fields_names'])) {
            if ($options['fields_names']) {
                $fields_names = $export_fields;
                $export_fields = array_keys($export_fields);
            }
        }

        $primary_key = array();
        $_primary_key = $pattern['key'];
        foreach ($_primary_key as $key) {
            $primary_key[$key] = $key;
        }
        array_walk($primary_key, 'fn_attach_value_helper', $pattern['table'].'.');

        $table_fields = $primary_key;
        $processes = array();

        // Build list of fields that should be retrieved from the database
        fn_export_build_retrieved_fields($processes, $table_fields, $pattern, $export_fields);
        $table_fields_without_aliases = array_map(function(string $field): string {
            [$field] = explode(' as ', $field);

            return $field;
        }, $table_fields);
        $group_fields = array_intersect($pattern['group_by'] ?? [], $table_fields_without_aliases);

        if (empty($pattern['export_fields']['multilang'])) {
            $multi_lang = array(DEFAULT_LANGUAGE);
            $count_langs = 1;
            $options['lang_code'] = $multi_lang;
        }

        // Build the list of joins
        $joins = fn_export_build_joins($pattern, $options, $primary_key, $multi_lang);

        // Add retrieve conditions
        try {
            $conditions = fn_export_build_conditions($pattern, $options);
            if ($pattern['pattern_id'] === 'orders') {
                $statutCondition = \Wizacha\Order::statusCondition(
                    Registry::get('runtime.company_id'),
                    'A',
                    0,
                    '',
                    false
                );
                if ($statutCondition !== '') {
                    $conditions[] = $statutCondition;
                }
            }

        } catch (InvalidFormatDateTimeException $exception) {
            fn_set_notification('E', __('error'), __('error_exim_invalid_format_datetime', ['[errorType]' => $exception->errorTypeToString()]));

            return false;
        }

        if (!empty($pattern['pre_export_process'])) {
            $pre_export_process_data = array(
                'pattern' => &$pattern,
                'export_fields' => &$export_fields,
                'options' => &$options,
                'conditions' => &$conditions,
                'joins' => &$joins,
                'table_fields' => &$table_fields,
                'processes' => &$processes
            );
            fn_exim_processing('export', $pattern['pre_export_process'], $options, $pre_export_process_data);
        }

        $sorting = $group_by = '';

        if (count($group_fields) > 0) {
            $group_by = ' GROUP BY ' . implode(', ', $group_fields);
        }

        $order_by = $pattern['order_by'] ?? [];

        if (is_string($order_by) && !empty($order_by)) {
            $sorting = ' ORDER BY ' . $pattern['order_by'];
        } else if (is_array($order_by)) {
            $order_by = array_intersect($order_by, $table_fields_without_aliases);

            if (count($order_by) > 0) {
                $sorting = ' ORDER BY ' . implode(', ', $order_by);
            }
        }

        // Build main query
        $query =
            "SELECT " . implode(', ', $table_fields) . "
        FROM ?:" . $pattern['table'] . " as " . $pattern['table'] .' '
            . implode(' ', $joins)
            . (!empty($conditions) ? ' WHERE ' . implode(SQL_AND, $conditions) : '')
            . $group_by
            . $sorting
        ;

        $manual_multilang = true;

        foreach ($pattern['export_fields']['multilang'] as $key => $value) {
            if (array_search('languages', $value, true)) {
                if (!isset($value['linked']) || $value['linked'] === true) {
                    $manual_multilang = false;
                }

                break;
            }
        }

        //Hotfix : If no products exist, we can export an example.
        //TODO : Create a specific pattern.

        if (isset($options['example'])) {
            $query = "SELECT 1";
        }

        $pdoIterator = new \Wizacha\Core\Iterator\PdoIterator(Tygh\Database::prepare($query), PDO::FETCH_ASSOC);
        $chunkedIterator = \iter\chunk($pdoIterator, DB_LIMIT_SELECT_ROW);

        $nbLines = $pdoIterator->count();
        if(!$nbLines){
            fn_set_notification('E', __('error'), __('error_exim_no_data_exported'));

            return false;
        }

        fn_set_progress('parts', $nbLines);
        fn_set_progress('step_scale', 1);

        // EximJob
        $eximJob->setNbLines($nbLines);
        $eximJob->setStartedAt(new \DateTime());
        $eximJobRepository->save($eximJob);

        $outputStarted = false;
        foreach ($chunkedIterator as $data) {
            $data_exported = true;

            if ($manual_multilang) {
                $data_lang = $data;
                $data = array();

                foreach ($data_lang as $data_key => $data_value) {
                    $data[] = array_combine($multi_lang, array_fill(0, $count_langs, $data_value));
                }

            } else {

                $data_lang = array_chunk($data, $count_langs);
                $data = array();

                foreach ($data_lang as $data_key => $data_value) {
                    // Sort
                    foreach ($multi_lang as $lang_code) {
                        foreach ($data_value as $v) {
                            if (strtolower($lang_code) == strtolower($v['Language'])) {
                                // Ce champs est insensible à la casse dans la BDD
                                // On fait la comparaison en minuscule coté php pour être sur
                                // de ne pas avoir de blagues.
                                $v['Language'] = strtolower($v['Language']);
                                $data[$data_key][$lang_code] = $v;
                            }
                        }
                    }
                }
            }

            $result = array();
            foreach ($data as $k => $v) {
                fn_set_progress('echo', __('exporting_data') . ':&nbsp;<b>' . ($progress)  .'</b>');
                fn_export_fill_fields($result[$k], $v, $processes, $pattern, $options);
            }

            if (isset($options['filename'])) {
                $options['filename'] = basename($options['filename']);
            }
            if (isset($options['example'])) {
                if ('products' == $pattern['pattern_id']) {
                    $result = fn_w_set_data_template_csv_products();
                } elseif ('product_combinations' == $pattern['pattern_id']) {
                    $result = fn_w_set_data_template_csv_product_combination();
                }
            }
            if ($pattern['pattern_id'] == 'products') {
                \Wizacha\Exim\Product::exportResultPost($export_fields);
            }

            $_result = array();

            foreach ($result as $k => $v) {
                foreach ($v as $lang_code => $row) {
                    if (isset($row['Language'])
                        && $row['Language'] !== $lang_code
                    ) {
                        continue;
                    }

                    $_data = array();
                    foreach ($export_fields as $field) {
                        if (isset($fields_names[$field])) {
                            $_data[$fields_names[$field]] = $row[$field];
                        } else {
                            $_data[$field] = $row[$field] ?? '';
                        }
                    }

                    $progress ++;

                    // Si toutes les valeurs des champs sont vides, alors on zappe la ligne au lieu de faire un trou dans le CSV
                    if (empty(array_filter($_data, function ($item) { return $item !== ''; }))) {
                        continue;
                    }

                    $_result[] = $_data;
                }
            }

            // Put data
            $enclosure = (isset($pattern['enclosure'])) ? $pattern['enclosure'] : '"';
            fn_echo(' .');

            // Update EximJob progress
            $eximJob->setNbImported($progress);
            $eximJobRepository->save($eximJob);


            if (isset($pattern['func_save_content_to_file']) && is_callable($pattern['func_save_content_to_file'])) {
                call_user_func($pattern['func_save_content_to_file'], $_result, $options, $enclosure);
            } else {
                fn_put_csv($fileDescriptor, $_result, $options, $enclosure, $outputStarted);
                $outputStarted = true;
            }
        }
    }

    if (!empty($pattern['post_processing'])) {
        fn_set_progress('echo', __('processing'), false);

        if (file_exists($csvOutputPath)) {

            $data_exported = fn_exim_processing('export', $pattern['post_processing'], $options);
        }
    }

    if (\file_exists($csvOutputPath)) {
        \fclose($fileDescriptor);

        $prefix = Registry::get('runtime.company_id') ? Registry::get('runtime.company_id').'/' : '';

        $filePath = implode(
            '/',
            array_filter(
                [
                    Registry::get('runtime.company_id'),
                    $options['filename']
                ]
            )
        );

        container()->get('Wizacha\Storage\CsvStorageService')->put(
            $filePath,
            [
                'file' => $csvOutputPath,
                'overwrite' => true,
                'keep_origins' => false,
            ]
        );

        // EximJob
        $eximJob->setFile($prefix . $options['filename']);
        $eximJob->setStatus(JobStatus::SUCCESS());
        $eximJob->finish();
        $eximJobRepository->save($eximJob);

        // Notify by email
        if ($options['email_mode']) {
            container()->get('event_dispatcher')->dispatch(
                new AsyncExportHasFinished(
                    $options['url'],
                    $eximJob->getUserId()
                ),
                AsyncExportHasFinished::class
            );
        }
    }

    return $data_exported;
}

//
// Put data to csv file
// Parameters:
// @data - export data
// @options - options

function fn_put_csv(&$fd, &$data, &$options, $enclosure, $outputStarted = false)
{
    if (empty($data)) {
        if ($outputStarted === false) {
            fn_set_notification('E', __('error'), __('error_exim_no_data_exported'));
            exit;
        } else {
            return;
        }
    }

    if ($options['delimiter'] === 'C') {
        $delimiter = ',';
    } elseif ($options['delimiter'] === 'T') {
        $delimiter = "\t";
    } elseif ($options['delimiter'] === 'P') {
        $delimiter = '|';
    } else {
        $delimiter = ';';
    }

    if ($outputStarted === false) {
        // write BOM to the beginning of the file
        fwrite($fd, "\xEF\xBB\xBF", 3);
        // write CSV headers
        fputcsv($fd, array_keys(reset($data)), $delimiter, $enclosure);
    }

    foreach ($data as $k => $v) {
        foreach ($v as &$column) {
            if (preg_match("/[=+@_\-\"]/", $column[0])) {
                $column = "\t" . $column;
            }
        }
        unset($column);

        fputcsv($fd, $v, $delimiter, $enclosure);
    }

    return true;
}

//
// Helper function: attaches prefix to value
//
function fn_attach_value_helper(&$value, $key, $attachment)
{
    $value = $attachment . $value;

    return true;
}


// -------------- ExIm utility functions ---------------------

/**
 * Export image (moves to selected directory on filesystem)
 *
 * @param int $image_id ID of the image
 * @param string $object object to export image for (product, category, etc...)
 * @param string $backup_path path to export image
 * @return string path to the exported image
 */
function fn_export_image($image_id, $object, $backup_path = '')
{
    $images_path = !empty($backup_path) ? $backup_path : Registry::get('config.dir.exim') . 'backup/images/' . $object . '/';

    // if backup dir does not exist then try to create it
    fn_mkdir($images_path);

    $image_data = db_get_row("SELECT image_id, image_path FROM ?:images WHERE image_id = ?i", $image_id);
    if (empty($image_data)) {
        return '';
    }

    $alt_data = db_get_hash_single_array("SELECT lang_code, description FROM ?:common_descriptions WHERE ?:common_descriptions.object_id = ?i AND ?:common_descriptions.object_holder = 'images'", array('lang_code', 'description'), $image_id);
    $alt_text = '{';
    if (!empty($alt_data)) {
        foreach ($alt_data as $lang_code => $text) {
            $alt_text .= '[' . $lang_code . ']:' . $text . ';';
        }
    }
    $alt_text .= '}';

    $path = rtrim($images_path, '/') . '/' . fn_basename($image_data['image_path']);

    container()->get('Wizacha\Storage\ImagesStorageService')->export($object . '/' . floor($image_id / MAX_FILES_IN_DIR) . '/' . $image_data['image_path'], $path);

    return $path . (!empty($alt_data) ? '#' . $alt_text : '');
}

//
// Converts timestamp to human-readable date
//

function fn_timestamp_to_date($timestamp)
{
    return date('d M Y H:i:s', intval($timestamp));
}

//
// Converts human-readable date to timestamp
//

function fn_date_to_timestamp($date)
{
    return strtotime($date);
}

//
// Converts timestamp to human-readable date with specific format
//
function fn_convert_timestamp_to_date_with_specific_format($timestamp): ?string
{
    $date = \DateTime::createFromFormat('U', intval($timestamp));

    if ($date === false) {
        return null;
    }

    return $date->format("d/m/Y");
}

//
// Get absolute url to the image
// Parameters:
// @image_id - Id of image
// @object_type - type of image object

function fn_exim_get_image_url($product_id, $object_type, $pair_type, $get_icon, $get_detailed, $lang_code)
{
    $image_pair = fn_get_image_pairs($product_id, $object_type, $pair_type, true, true, $lang_code);
    $image_data = fn_image_to_display($image_pair, Registry::get('settings.Thumbnails.product_details_thumbnail_width'), Registry::get('settings.Thumbnails.product_details_thumbnail_height'));

    return !empty($image_data) ? $image_data['image_path'] : '';
}

//
// Get absolute url to the detailed image
// Parameters:
// @image_id - Id of image
// @object_type - type of image object

function fn_exim_get_detailed_image_url($product_id, $object_type, $pair_type, $lang_code)
{
    $image_pair = fn_get_image_pairs($product_id, $object_type, $pair_type, false, true, $lang_code);

    return !empty($image_pair['detailed']['image_path']) ? $image_pair['detailed']['image_path'] : '';
}

//
// Get pattern definition by its id
// Parameters:
// @pattern_id - pattern ID
function fn_get_pattern_definition($pattern_id, $get_for = '')
{
    return \Wizacha\Cscart\Exim::get_pattern_definition($pattern_id, $get_for);
}

/**
 * Gets all available patterns for the section
 *
 * @param string $section section to get patterns for
 * @param string $get_for get import or export patterns
 * @return array
 */
function fn_get_patterns($section, $get_for)
{
    // Get core patterns
    $files = fn_get_dir_contents(Registry::get('config.dir.schemas') . 'exim', false, true, '.php');

    foreach (Registry::get('addons') as $addon_name => $addon_data) {
        if ($addon_data['status'] != 'A') {
            continue;
        }

        $schema_dir = Registry::get('config.dir.addons') . $addon_name . '/schemas/exim';
        if (is_dir($schema_dir)) {
            $_files = fn_get_dir_contents($schema_dir, false, true, '.php');
            foreach ($_files as $key => $filename) {
                if (strpos($filename, '.post.php') !== false) {
                    unset($_files[$key]);
                }
            }

            if (!empty($_files)) {
                $files = fn_array_merge($files, $_files, false);
            }
        }
    }

    $patterns = array();
    $sections = array();

    foreach ($files as $schema_file) {
        if (strpos($schema_file, '.functions.') !== false) { // skip functions schema definition
            continue;
        }

        $pattern_id = str_replace('.php', '', $schema_file);
        $pattern = fn_get_pattern_definition($pattern_id, $get_for);

        if (empty($pattern)) {
            continue;
        }

        $sections[$pattern['section']] = array (
            'title' => __($pattern['section']),
            'href' => 'exim.' . Registry::get('runtime.mode') . '?section=' . $pattern['section'],
        );
        if ($pattern['section'] == $section) {
            $patterns[$pattern_id] = $pattern;
        }
    }

    if (Registry::get('runtime.company_id')) {
        $schema = fn_get_permissions_schema('vendor');

        // Check if the selected section is available
        if (isset($schema[$get_for]['sections'][$section]['permission']) && !$schema[$get_for]['sections'][$section]['permission']) {
            return array('', '');
        }

        if (!empty($schema[$get_for]['sections'])) {
            foreach ($schema[$get_for]['sections'] as $section_id => $data) {
                if (isset($data['permission']) && !$data['permission']) {
                    unset($sections[$section_id]);
                }
            }
        }

        if (!empty($schema[$get_for]['patterns'])) {
            foreach ($schema[$get_for]['patterns'] as $pattern_id => $data) {
                if (isset($data['permission']) && !$data['permission']) {
                    unset($patterns[$pattern_id]);
                }
            }
        }
    }

    ksort($sections, SORT_STRING);
    uasort($patterns, 'fn_sort_patterns');

    return array($sections, $patterns);
}

/**
 * Patterns sort function
 *
 * @param array $a scheme array
 * @param array $b scheme array
 * @return int
 */
function fn_sort_patterns($a, $b)
{
    $s1 = isset($a['order']) ? $a['order'] : $a['pattern_id'];
    $s2 = isset($b['order']) ? $b['order'] : $b['pattern_id'];
    if ($s1 == $s2) {
        return 0;
    }

    return ($s1 < $s2) ? -1 : 1;
}

/**
 * Gets product url
 *
 * @param $product_id
 * @param string $lang_code
 * @return bool
 */
function fn_exim_get_product_url($product_id, $lang_code = '')
{
    $url = fn_url('products.view?product_id=' . $product_id, 'C', 'http', $lang_code);

    return $url;
}

/**
 * Convert price to it representation with selected decimal separator
 *
 * @param float $price Price
 * @param string $decimals_separator
 * @return string Converted price
 */
function fn_exim_export_price($price, $decimals_separator)
{
    if ($decimals_separator == '.') {
        return $price;
    }

    return str_replace('.', $decimals_separator, $price);
}

function fn_exim_processing($type_processing, $processing, $options, $vars = array())
{
    return \Wizacha\Cscart\Exim::fn_exim_processing($type_processing, $processing, $options, $vars);
}

function fn_export_build_retrieved_fields(&$processes, &$table_fields, &$pattern, $export_fields)
{
    $_pattern['export_fields']['main'] = $_pattern['export_fields']['multilang'] = array();
    $processes['main'] = $processes['multilang'] = array();

    foreach ($pattern['export_fields'] as $field => $data) {
        if (!in_array($field, $export_fields)) {
            continue;
        }

        // Do no link this field
        if (isset($data['linked']) && $data['linked'] == false) {
            // do something?
        }
        // Primary object table
        elseif (empty($data['table']) || $data['table'] == $pattern['table']) {
            $table_fields[] = $pattern['table'] . '.' . (!empty($data['db_field']) ? $data['db_field'] . ' as "' .$field. '"' : $field);
        // Linked object tables
        } else {
            $table_fields[] = $data['table'] . '.' . (!empty($data['db_field']) ? $data['db_field'] . ' as "' .$field. '"' : $field);
        }

        $type_data = (array_key_exists('multilang', $data)) ? 'multilang' : 'main';

        $_pattern['export_fields'][$type_data][$field] = $data;

        if (!empty($data['process_get'])) {
            $processes[$type_data][$field]['function'] = array_shift($data['process_get']);
            $processes[$type_data][$field]['args'] = $data['process_get'];
        }

    }

    $pattern['export_fields'] = $_pattern['export_fields'];

    return true;
}

function fn_export_build_joins($pattern, $options, $primary_key, $langs)
{
    $joins = array();
    if (!empty($pattern['references'])) {
        foreach ($pattern['references'] as $table => $data) {
            $ref = array();
            $vars = array(
                'key' => $primary_key,
                'lang_code' => $langs,
            );
            $values = fn_exim_get_values($data['reference_fields'], $pattern, $options, $vars);

            foreach ($data['reference_fields'] as $k => $v) {
                $_val = $values[$k];
                if ($table === 'seo_names' && $k === 'object_id' && $_val === 'products.product_id') {
                    $_val = "CONVERT($_val USING utf8)";
                } elseif ($table === 'images_links' && $k === 'object_id' && $_val === 'product_feature_variants.variant_id') {
                    $_val = "CONVERT($_val USING utf8)";
                }

                if (is_array($_val)) {
                    $ref[] = "$table.$k IN (" . implode(", ", $_val) . ")";
                } else {
                    $ref[] = "$table.$k = $_val";
                }
            }

            $joins[] = $data['join_type'] . ' JOIN ?:' . $table . " as $table ON " . implode(SQL_AND, $ref);
        }
    }

    return $joins;
}

function fn_export_build_conditions($pattern, $options)
{
    $conditions = array();

    if (!empty($pattern['condition'])) {
        $_cond = array();

        if (!empty($pattern['condition']['conditions'])) {
            $values = fn_exim_get_values($pattern['condition']['conditions'], $pattern, $options);
            foreach ($pattern['condition']['conditions'] as $field => $value) {

                $_val = $values[$field];

                if (strpos($field, '&') !== false) {
                    $_field = substr($field, 1);
                } else {
                    $_field = $pattern['table'] . '.' .$field;
                }

                if (is_array($_val)) {
                    $_val = implode(",", $_val);
                    $_cond[] = "$_field IN ($_val)";

                } elseif (strpos($_val, '!') === 1) {
                    $_val = str_replace('!', '', $_val);
                    $_cond[] = "$_field != $_val";

                } else {
                    $_cond[] = "$_field = $_val";
                }
            }
        }

        if (!empty($pattern['condition']['use_company_condition'])) {

            if ($pattern['table'] === 'order_details') {
                $company_condition = fn_get_company_condition('orders.company_id', false);
            } else if ($pattern['table'] === 'doctrine_related_products') {
                $company_condition = fn_get_company_condition('products.company_id', false);
            } else {
                $company_condition = fn_get_company_condition($pattern['table'] . '.company_id', false);
            }

            if (!empty($company_condition)) {
                $_cond[] = $company_condition;
            }
        }

        if (null !== $options['date_range']['time_from'] && null !== $options['date_range']['time_to']) {
            // used to export orders with a date range
            $dateRangeSqlQueryBuilder = new DateRangeSqlQueryBuilder(
                $options['date_range']['time_from'],
                $options['date_range']['time_to'],
                $pattern['section'],
                'd/m/Y'
            );

            $builtQuery = $dateRangeSqlQueryBuilder->buildRangeQuery();
            // we add the query built to existing conditions at the end if not empty
            if ('' !== $builtQuery) {
                $_cond[] = $builtQuery;
            }
        }

        if ($pattern['section'] === 'accounting') {
            if (isset($pattern['condition']['use_date_range_condition'])) {
                $date_range_condition = fn_get_date_range_condition(
                    $options['date_range']['from'],
                    $options['date_range']['to'],
                    $pattern['table'] . '.w_last_status_change',
                    false
                );
                $_cond[] = $date_range_condition;
            }
        }

        if (!empty($_cond)) {
            $conditions[] = implode(SQL_AND, $_cond);
        }
    }

    return $conditions;
}

function fn_get_date_range_condition($from, $to, $db_field = 'w_last_status_change', $add_and = true)
{
    $periodEnd = date('Y-m-d H:i:s', Period::getPeriod(time(), 1)['end']);

    if ($from !== '' && $to !== '') {
        $dateFrom = date_create_from_format('d/m/Y|', $from)->format('Y-m-d H:i:s');
        $day = date_interval_create_from_date_string('1 day');
        $dateTo = date_create_from_format('d/m/Y|', $to)->add($day)->format('Y-m-d H:i:s');
        ($dateTo < $periodEnd) ?: $dateTo = $periodEnd;
    } else {
        $dateFrom = date('Y-m-d H:i:s', strtotime('first day of january this year'));
        $dateTo = $periodEnd;
    }

    $cond = $add_and ? ' AND' : '';
    $cond .= " $db_field BETWEEN '$dateFrom' AND '$dateTo'";

    return $cond;
}

function fn_exim_get_values($values, $pattern, $options, $vars = array(), $data = array(), $quote = "'")
{
    return \Wizacha\Cscart\Exim::fn_exim_get_values($values, $pattern, $options, $vars, $data, $quote);
}

function fn_export_fill_fields(&$result, $data, $processes, $pattern, $options)
{
    $multi_lang = array_keys($data);
    $main_lang = reset($multi_lang);

    // Filter
    $export_fields_all =  array_merge($pattern['export_fields']['main'], $pattern['export_fields']['multilang']);
    $result[$main_lang] = fn_array_key_intersect($data[$main_lang], $export_fields_all);
    foreach (array_diff($multi_lang, array($main_lang)) as $lang_code) {
        $result[$lang_code] = fn_array_key_intersect($data[$lang_code], $pattern['export_fields']['multilang']);
    }

    foreach ($processes['main'] as $field => $process_data) {
        $vars = array(
            'key' => array($data[$main_lang][reset($pattern['key'])]),
            'field' => $field,
            'lang_code' => $main_lang
        );

        $args = fn_exim_get_values($process_data['args'], $pattern, $options, $vars, $data[$main_lang], '');

        if (!empty($process_data['function'])) {
            $result[$main_lang][$field] = call_user_func_array($process_data['function'], $args);
        } else {
            $result[$main_lang][$field] = array_shift($args);
        }
    }

    foreach ($processes['multilang'] as $field => $process_data) {

        foreach ($multi_lang as $lang_code) {
            $vars = array(
                'key' => array($data[$lang_code][reset($pattern['key'])]),
                'field' => $field,
                'lang_code' => $lang_code,
            );

            $args = fn_exim_get_values($process_data['args'], $pattern, $options, $vars, $data[$lang_code], '');
            //$args[$index_lang] = $lang_code;

            if (!empty($process_data['function'])) {
                $result[$lang_code][$field] = call_user_func_array($process_data['function'], $args);
            } else {
                $result[$lang_code][$field] = array_shift($args);
            }
        }
    }
}


/**
 * Import company
 *
 * @param string $object_type Type of object ('currencies', 'pages', etc)
 * @param integer $object_id Product ID
 * @param string $company_name Company name
 * @return integer $company_id Company identifier.
 */

function fn_exim_set_company($object_type, $object_key, $object_id, $company_name)
{
    if (empty($company_name) || empty($object_id) || empty($object_type)) {
        return false;
    }

    if (Registry::get('runtime.company_id')) {
        $company_id = Registry::get('runtime.company_id');
    } else {
        $company_id = fn_get_company_id_by_name($company_name);

        if (!$company_id) {
            $company_data = array('company' => $company_name, 'email' => '');
            $company_id = fn_update_company($company_data, 0);
        }
    }

    db_query("UPDATE ?:$object_type SET company_id = ?s WHERE $object_key = ?i", $company_id, $object_id);

    return $company_id;
}
