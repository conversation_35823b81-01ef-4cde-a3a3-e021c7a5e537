<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, Ilya M<PERSON> Shalnev    *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use AlgoliaSearch\AlgoliaException;
use Symfony\Component\Security\Csrf\CsrfToken;
use Tygh\Registry;
use Wizacha\AppBundle\Controller\Api\RelatedProduct\RelatedProductDto;
use Wizacha\Component\Import\EximJobLog;
use Wizacha\Component\Import\EximJobLogRepository;
use Wizacha\Async\AbstractAsyncQueue;
use Wizacha\Async\Exception\QueueException;
use Wizacha\Async\QueueServiceFactory;
use Wizacha\Marketplace\Division\Service\DivisionService;
use Wizacha\Marketplace\Exception\FileNotFoundException;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\PIM\Product\ProductVisibilityReportService;
use Wizacha\Marketplace\PIM\Product\Template\HiddenField;
use Wizacha\Marketplace\PIM\Product\Template\ProductInfoFormType;
use Wizacha\Marketplace\PIM\Product\Template\ProductPriceType;
use Wizacha\Marketplace\PIM\Product\Template\TemplateService;
use Wizacha\Marketplace\RelatedProduct\RelatedProductsType;
use Wizacha\Product;
use Wizacha\Search\Engine\AlgoliaSyncService;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

$_REQUEST['product_id'] = empty($_REQUEST['product_id']) ? 0 : (int) $_REQUEST['product_id'];

if (\array_key_exists('product_id', $_REQUEST) === true
    && fn_company_products_check($_REQUEST['product_id']) === false
) {
    return array(CONTROLLER_STATUS_DENIED);
}

$selectedProductIds = [];
$nbSelectedProductIds = 0;
if (\array_key_exists('selected_product_ids', $_REQUEST) === true && \strlen($_REQUEST['selected_product_ids']) > 0) {
    $selectedProductIds = \explode(',', $_REQUEST['selected_product_ids']);
    if (fn_company_products_check($selectedProductIds) === false) {
        return array(CONTROLLER_STATUS_DENIED);
    }
    $nbSelectedProductIds = \count($selectedProductIds);
}

$container = container();
$saveProductForm = $container->get('form.factory')->create(ProductInfoFormType::class);
$divisionSettingsService = $container->get('marketplace.divisions_settings.service');
$divisionService = $container->get('marketplace.division.service');
$relatedProductsService = $container->get('marketplace.related_product.related_product_service');
$priceTiersService = $container->get('marketplace.price_tier.price_tier_service');

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $suffix = '';

    // Define trusted variables that shouldn't be stripped
    fn_trusted_vars (
        'product_data',
        'override_products_data',
        'product_files_descriptions',
        'add_product_files_descriptions',
        'products_data',
        'product_file'
    );

    //
    // Create/update product
    //
    if ($mode == 'update') {
        $saveProductForm->handleRequest();
        $_REQUEST['product_data'] = array_merge($_REQUEST['product_data'], $saveProductForm->getNormData());
        Registry::get('view')->assign('saveProductForm', $saveProductForm->createView());

        if (!empty($_REQUEST['product_data']['product'])) {

            // Allow vendor to add variants only for brand
            if (
                isset($_REQUEST['product_data']['add_new_variant'])
                && \Wizacha\Company::runtimeID(AREA, $_SESSION, \Wizacha\Registry::defaultInstance()) !== 0
            ) {
                $brandId = fn_w_get_brand_id([$_REQUEST['product_data']['category_ids']]);
                if ($brandId) {
                    $_REQUEST['product_data']['add_new_variant'] = array_intersect_key(
                        $_REQUEST['product_data']['add_new_variant'],
                        array_flip([$brandId])
                    );
                } else {
                    $_REQUEST['product_data']['add_new_variant'] = [];
                }
            }
            fn_companies_filter_company_product_categories($_REQUEST, $_REQUEST['product_data']);
            if (empty($_REQUEST['product_data']['category_ids'])) {
                fn_set_notification('E', __('error'), __('category_is_empty'));

                return array(CONTROLLER_STATUS_REDIRECT, !empty($_REQUEST['product_id']) ? "products.update?product_id=" . $_REQUEST['product_id'] : "products.add");
            } else {
                $_REQUEST['product_data']['category_ids'] = explode(',', $_REQUEST['product_data']['category_ids']);
            }

            //Set the products previous template hidden fields to null
            $_REQUEST = fn_set_previous_template_hidden_fields_to_null($_REQUEST);

            //Set the products current template hidden fields to their default values
            $_REQUEST = fn_set_new_template_hidden_fields_to_default($_REQUEST);

            // Format divisions data
            if (true === $container->getParameter('feature.available_offers')
                && \array_key_exists('division-loaded', $_REQUEST)
                && "true" === $_REQUEST['division-loaded']
            ) {
                // Manage inherit-divisions checkbox
                if (\array_key_exists('included', $_REQUEST)
                    && \is_array($_REQUEST['included'])
                ) {
                    $includedDivisions = $_REQUEST['included'];
                } elseif (\array_key_exists('inherit-divisions', $_REQUEST)
                    && 'on' === $_REQUEST['inherit-divisions']
                ) {
                    $includedDivisions = [DivisionService::ROOT_CODE];
                } else {
                    $includedDivisions = [];
                }

                $_REQUEST['product_data']['divisions'] = [
                    'included' => $includedDivisions,
                    'excluded' => \is_array($_REQUEST['excluded']) ? $_REQUEST['excluded'] : [],
                ];
            }

            // Format tax
            if (true === \array_key_exists('tax_ids', $_REQUEST['product_data'])) {
                $_REQUEST['product_data']['tax_ids'] = [$_REQUEST['product_data']['tax_ids']];
            }

            $product_id = (int) fn_update_product($_REQUEST['product_data'], $_REQUEST['product_id'], (string) GlobalState::contentLocale());

            $productService = $container->get('marketplace.pim.product.service');
            $attachmentsUpdated = $productService->addAttachments((int) $product_id, 'add_product_attachments');
            $attachmentsUpdated = $attachmentsUpdated || $productService->updateAttachments((int) $product_id, 'product_attachments');

            if ($attachmentsUpdated) {
                $container->get('event_dispatcher')->dispatch(
                    \Wizacha\Events\IterableEvent::fromElement($product_id),
                    \Wizacha\Product::EVENT_UPDATE
                );
            }

            if ($product_id) {
                $product_obj = new \Wizacha\Product($product_id);
                if ($_REQUEST['product_data']['geoloc_lat'] !== null
                    && $_REQUEST['product_data']['geoloc_lng'] !== null
                    && $_REQUEST['product_data']['geoloc_label'] !== null
                    && $_REQUEST['product_data']['geoloc_postal'] !== null
                ) {
                    $product_obj->setGeoloc(
                        $_REQUEST['product_data']['geoloc_lat'],
                        $_REQUEST['product_data']['geoloc_lng'],
                        $_REQUEST['product_data']['geoloc_label'],
                        $_REQUEST['product_data']['geoloc_postal']
                    );
                } elseif ($_REQUEST['product_data']['geoloc_lat'] !== null && $_REQUEST['product_data']['geoloc_lng'] !== null) {
                    $product_obj->setGeoloc(
                        $_REQUEST['product_data']['geoloc_lat'],
                        $_REQUEST['product_data']['geoloc_lng'],
                        null,
                        null
                    );
                } else {
                    $product_obj->setGeoloc(
                        null,
                        null,
                        null,
                        null
                    );
                }

                if (isset($_REQUEST['product_data']['free_features'])
                    && is_array($_REQUEST['product_data']['free_features'])
                ) {
                    $free_features = [];
                    foreach ($_REQUEST['product_data']['free_features'] as $data) {
                        if (!empty($data['name']) && isset($data['value'])) {
                            if (!isset($free_features[$data['name']])) {
                                $free_features[$data['name']] = [];
                            }
                            $free_features[$data['name']][] = $data['value'];
                        }
                    }

                    if ($product_obj->getFreeFeatures() != $free_features) {
                        $product_obj->setFreeFeatures($free_features);
                        \Wizacha\Events\Config::dispatch(
                            \Wizacha\Product::EVENT_UPDATE,
                            (new \Wizacha\Events\IterableEvent)->setElement($product_id)
                        );
                    }
                }
            }
        }

        $suffix = ".update?product_id=$product_id" . (!empty($_REQUEST['product_data']['block_id']) ? "&selected_block_id=" . $_REQUEST['product_data']['block_id'] : "");
    }

    //
    // Processing mulitple addition of new product elements
    //
    if ($mode == 'm_add') {

        if (is_array($_REQUEST['products_data'])) {
            $p_ids = array();
            foreach ($_REQUEST['products_data'] as $k => $v) {
                if (!empty($v['product']) && !empty($v['main_category'])) {  // Checking for required fields for new product
                    fn_companies_filter_company_product_categories($_REQUEST, $v);
                    $p_id = fn_update_product($v);
                    if (!empty($p_id)) {
                        $p_ids[] = $p_id;
                    }
                }
            }

            if (!empty($p_ids)) {
                fn_set_notification('N', __('notice'), __('text_products_added'));
            }
        }
        $suffix = ".manage" . (empty($p_ids) ? "" : "?pid[]=" . implode('&pid[]=', $p_ids));
    }

    //
    // Processing multiple updating of product elements
    //
    if ($mode == 'm_update') {
        // Update multiple products data
        if (!empty($_REQUEST['products_data'])) {

            if (!fn_company_products_check(array_keys($_REQUEST['products_data']))) {
                return array(CONTROLLER_STATUS_DENIED);
            }

            // Update images
            fn_attach_image_pairs('product_main', 'product', 0, (string) GlobalState::contentLocale());

            foreach ($_REQUEST['products_data'] as $k => $v) {
                if (!empty($v['product'])) { // Checking for required fields for new product

                    if (Registry::get('runtime.company_id')) {
                        unset($v['company_id']);
                    }

                    fn_companies_filter_company_product_categories($_REQUEST, $v);

                    if (!empty($v['category_ids'])) {
                        $v['category_ids'] = explode(',', $v['category_ids']);
                    }

                    fn_update_product($v, $k, (string) GlobalState::contentLocale());

                    // Updating products position in category
                    if (isset($v['position']) && !empty($_REQUEST['category_id'])) {
                        db_query("UPDATE ?:products_categories SET position = ?i WHERE category_id = ?i AND product_id = ?i", $v['position'], $_REQUEST['category_id'], $k);
                    }
                }
            }
        }
        $suffix = ".manage";
    }

    //
    // Override multiple products with the one value
    //
    if ($mode == 'm_override') {
        // Update multiple products data
        if (!empty($_SESSION['product_ids'])) {

            if (!fn_company_products_check($_SESSION['product_ids'])) {
                return array(CONTROLLER_STATUS_DENIED);
            }

            $product_data = !empty($_REQUEST['override_products_data']) ? $_REQUEST['override_products_data'] : array();
            if (isset($product_data['avail_since'])) {
                $product_data['avail_since'] = fn_parse_date($product_data['avail_since']);
            }
            if (isset($product_data['timestamp'])) {
                $product_data['timestamp'] = fn_parse_date($product_data['timestamp']);
            }

            if (Registry::get('runtime.company_id')) {
                unset($product_data['company_id']);
            }

            fn_define('KEEP_UPLOADED_FILES', true);

            fn_companies_filter_company_product_categories($_REQUEST, $product_data);
            if (!empty($product_data['category_ids'])) {
                $product_data['category_ids'] = explode(',', $product_data['category_ids']);
            }

            foreach ($_SESSION['product_ids'] as $_o => $p_id) {
                // Update product
                fn_update_product($product_data, $p_id, (string) GlobalState::contentLocale());
            }
        }
    }
    //
    // Processing deleting of multiple product elements
    //
    if ($mode == 'm_delete') {
        if ($nbSelectedProductIds > 0) {
            $asynchronous = $nbSelectedProductIds > 1;
            foreach ($selectedProductIds as $v) {
                try {
                    fn_delete_product($v, $asynchronous);
                } catch (Forbidden $e) {
                    fn_set_notification('W', __('warning'), __('access_denied'));
                }
            }
        }
        unset($_SESSION['product_ids']);
        fn_set_notification('N', __('notice'), __('text_products_have_been_deleted'));
        $suffix = ".manage";
    }

    //
    // Processing clonning of multiple product elements
    //
    if ($mode == 'm_clone') {
        $p_ids = array();
        if ($nbSelectedProductIds > 0) {
            foreach ($selectedProductIds as $v) {
                $pdata = fn_clone_product($v);
                $p_ids[] = $pdata['product_id'];
            }

            fn_set_notification('N', __('notice'), __('text_products_cloned'));
        }
        $suffix = ".manage?pid[]=" . implode('&pid[]=', $p_ids);
        unset($_REQUEST['redirect_url'], $_REQUEST['page']); // force redirection
    }

    //
    // Storing selected fields for using in m_update mode
    //
    if ($mode == 'store_selection') {

        if ($nbSelectedProductIds > 0) {
            $_SESSION['product_ids'] = $selectedProductIds;
            $_SESSION['selected_fields'] = $_REQUEST['selected_fields'];

            unset($_REQUEST['redirect_url']);

            $suffix = ".m_update";
        } else {
            $suffix = ".manage";
        }
    }

    //
    // Add edp files to the product
    //
    if ($mode == 'update_file') {
        if (!empty($_REQUEST['product_file'])) {

            if (empty($_REQUEST['product_file']['folder_id'])) {
                $_REQUEST['product_file']['folder_id'] = null;
            }
            $file_id = fn_update_product_file($_REQUEST['product_file'], $_REQUEST['file_id'], (string) GlobalState::contentLocale());
        }

        $suffix = ".update?product_id=$_REQUEST[product_id]";
    }

    //
    // Add edp folder to the product
    //
    if ($mode == 'update_folder') {

        if (!empty($_REQUEST['product_file_folder'])) {
            $folder_id = fn_update_product_file_folder($_REQUEST['product_file_folder'], $_REQUEST['folder_id'], (string) GlobalState::contentLocale());
        }

        $suffix = ".update?product_id=$_REQUEST[product_id]";
    }

    if (($mode == 'export_range') && $nbSelectedProductIds > 0) {
        if (\array_key_exists('export_ranges', $_SESSION) === true && \count($_SESSION['export_ranges']) === 0) {
            $_SESSION['export_ranges'] = array();
        }

        if (\array_key_exists('export_ranges', $_SESSION) === true
            && \array_key_exists('products', $_SESSION['export_ranges']) === true
            && \count($_SESSION['export_ranges']['products']) === 0
        ) {
            $_SESSION['export_ranges']['products'] = array('pattern_id' => 'products');
        }

        $_SESSION['export_ranges']['products']['data'] = array('product_id' => $selectedProductIds);

        unset($_REQUEST['redirect_url']);

        return array(CONTROLLER_STATUS_REDIRECT, "exim.export?section=products&pattern_id=" . $_SESSION['export_ranges']['products']['pattern_id']);
    }

    if ($mode == 'product_templates_infos' && !empty($_REQUEST['template'])) {
        $template = $container->get(TemplateService::class)->getTemplate($_REQUEST['template']);
        if ($template) {
            $hidden = array_map(function (HiddenField $field) {
                return $field->expose();
            }, $template->getHiddenFields());

            echo json_encode($hidden); die;
        }

        return array(CONTROLLER_STATUS_NO_PAGE);
    }

    if ($mode == 'related_products_add') {
        $fromProductId = $_REQUEST['product_id'];
        $relatedProductIds = \array_key_exists('related_product_id', $_REQUEST) === true ? $_REQUEST['related_product_id'] : [] ;

        $productService = $container->get('marketplace.pim.product.service');
        $fromProduct = $productService->get($fromProductId);

        foreach ($relatedProductIds as $relatedProductId) {
            $toProduct = $productService->get($relatedProductId);

            $dto = new RelatedProductDto(
                $fromProduct,
                $toProduct,
                $_REQUEST['type'],
                $_REQUEST['description_' . $_REQUEST['type']],
                '',
            );

            try {
                $relatedProduct = $relatedProductsService->findBy([
                    'fromProduct' => $fromProduct->getId(),
                    'toProduct' => $toProduct->getId(),
                    'type' => $dto->type,
                ]);

                $user = Registry::get('user_info');

                if (\count($relatedProduct) > 0) {
                    $relatedProductsService->updateRelatedProduct($dto, $user['user_id']);
                } else {
                    $relatedProductsService->addRelatedProduct($dto, $user['user_id']);
                }
            } catch (\Throwable $e) {
                fn_set_notification('W', __('error'), __('related_product_add_errors'));
            }
        }

        return [CONTROLLER_STATUS_REDIRECT, "products.update?product_id=" . $fromProductId . "&selected_section=related_products"];
    }

    if ($mode == 'related_products_delete') {
        $fromProductId = $_REQUEST['product_id'];
        $relatedProductIds = \array_key_exists('related_ids', $_REQUEST) === true ? $_REQUEST['related_ids'] : [] ;

        try {
            foreach ($relatedProductIds as $id) {
                $relatedProductsService->deleteRelatedProductById((int) $id);
            }
        } catch (\Throwable $e) {
            fn_set_notification('W', __('error'), __('related_product_delete_errors'));
        }

        return [CONTROLLER_STATUS_REDIRECT, "products.update?product_id=" . $fromProductId . "&selected_section=related_products"];
    }

    return array(CONTROLLER_STATUS_OK, "products$suffix");
}

//
// 'Management' page
//
if ($mode == 'manage') {
    unset($_SESSION['product_ids']);
    unset($_SESSION['selected_fields']);

    $params = $_REQUEST;
    $params['only_short_fields'] = true;
    $params['extend'][] = 'companies';
    $params['moderation'] = 'include';

    list($products, $search) = fn_get_products($params, Registry::get('settings.Appearance.admin_products_per_page'), (string) GlobalState::contentLocale());
    fn_gather_additional_products_data($products, array('get_icon' => true, 'get_detailed' => true, 'get_options' => false, 'get_discounts' => false));

    Registry::get('view')->assign('products', $products);
    Registry::get('view')->assign('search', $search);

    if (!empty($_REQUEST['redirect_if_one']) && $search['total_items'] == 1) {
        return array(CONTROLLER_STATUS_REDIRECT, "products.update?product_id={$products[0]['product_id']}");
    }

    $selected_fields = fn_get_product_fields();

    Registry::get('view')->assign('selected_fields', $selected_fields);
    $filter_params = array(
        'get_variants' => true,
        'short' => true
    );

    $feature_params = array(
        'plain' => true,
        'variants' => true,
        'exclude_group' => true,
        'exclude_filters' => true
    );
    list($features) = fn_get_product_features($feature_params);
    Registry::get('view')->assign('feature_items', $features);

    $feature_available_offers = $container->getParameter('feature.available_offers');
    Registry::get('view')->assign('feature_available_offers', $feature_available_offers);

    if ($feature_available_offers) {
        $divisionService = $container->get("marketplace.division.service");

        if (Registry::get('runtime.company_id') > 0) {
            // Company enabled divisions
            $divisions = $divisionService->getCompanyDivisions(
                Registry::get('runtime.company_id'),
                GlobalState::interfaceLocale(),
                false,
                true
            );
        } else {
            // Marketplace enabled divisions
            $divisions = $divisionService->getMarketplaceDivisions(
                GlobalState::interfaceLocale(),
                false,
                true
            );
        }

        Registry::get('view')->assign('divisions', $divisions);
    }

    // Get the readmodel queue number of messages
    $queue = container()
        ->get(QueueServiceFactory::class)
        ->get();

    $nbQueueMessages = 0;

    if ($queue instanceof AbstractAsyncQueue) {
        try {
            $nbQueueMessages = $queue->getNbOfQueueMessages('readmodel');
        } catch (QueueException $e) {
            // If something fails, prefer to avoid throwing an exception.
            // The aim is just to display an informative message.
            $logger = container()->get('logger');
            $logger->warning($e->getMessage());
        }
    }

    Registry::get('view')->assign('cpt_sync_products', $nbQueueMessages);

    Registry::get('view')->assign('templatesList', $container->get(TemplateService::class)->getTemplates());

    unset($features);
}
//
// 'Add new product' page
//
if ($mode == 'add') {
    Registry::get('view')->assign('saveProductForm', $saveProductForm->createView());
    Registry::get('view')->assign('featureSubscription', $container->getParameter('feature.subscription'));
    Registry::get('view')->assign('taxes', fn_get_taxes());

    // [Page sections]
    $tabs = [
        'detailed' => [
            'title' => __('general'),
            'js' => true,
        ],
        'images' => [
            'title' => __('images'),
            'js' => true,
        ],
        'qty_discounts' => [
            'title' => __('qty_discounts'),
            'js' => true,
        ],
        'addons' => [
            'title' => __('addons'),
            'js' => true,
        ],
    ];

    Registry::set('navigation.tabs', $tabs);
    // [/Page sections]

    Registry::get('view')->assign('available_offers_count', 0);
    Registry::get('view')->assign('transaction_modes', $container->get('marketplace.transaction_mode.service')->getAvailablesModesAsArray());

    Registry::get('view')->assign('is_up_to_date',  true);
//
// 'Multiple products addition' page
//
} elseif ($mode == 'm_add') {

//
// 'product update' page
//
} elseif ($mode == 'update') {
    $selected_section = (empty($_REQUEST['selected_section']) ? 'detailed' : $_REQUEST['selected_section']);

    // Get current product data
    $product_data = fn_get_product_data($_REQUEST['product_id'], $auth, (string) GlobalState::contentLocale(), '', true, true, true, true, false, true, false);
    $productDataPlaceholder = fn_get_product_data($_REQUEST['product_id'], $auth, (string) GlobalState::interfaceLocale(), '', true, true, true, true, false, true, false);

    if (empty($product_data)) {
        return array(CONTROLLER_STATUS_NO_PAGE);
    }

    $productId = $product_data['product_id'];

    // Readmodel / Algolia Sync
    /** @var AlgoliaSyncService */
    $algoliaSyncService = container()->get(AlgoliaSyncService::class);

    $isDebug = isset($_REQUEST['debug_workflow']) || isset($_SESSION['debug_workflow']);
    if ($isDebug) {
        /** @var EximJobLogRepository */
        $eximJobLogRepository = container()->get('marketplace.import.log_repository');
        /** @var EximJobLog[] */
        $eximJobLogs = $eximJobLogRepository->getByProductId(
            $product_data['product_id']
        );
        Registry::get('view')->assign(
            'eximJobLogs',
            $eximJobLogs
        );
        Registry::get('view')->assign('isDebug', $isDebug);

        try {
            $recordDiffGenerator = $algoliaSyncService->getRecordDiffGenerator(
                GlobalState::interfaceLocale(),
                $productId
            );

            $diff = \iterator_to_array($recordDiffGenerator);
            if (\count($diff)) {
                Registry::get('view')->assign('algoliaError', 'Out of sync Algolia record');
            }
            Registry::get('view')->assign(
                'algoliaDiff',
                $diff
            );
        } catch (AlgoliaException $exception) {
            Registry::get('view')->assign('algoliaError', $exception->getMessage());
        };
    }

    $product_obj = new \Wizacha\Product($product_data['product_id']);
    list(
        $product_data['geoloc_lat'],
        $product_data['geoloc_lng'],
        $product_data['geoloc_label'],
        $product_data['geoloc_postal']
        ) = $product_obj->getGeoloc();

    $product_data['free_features'] = [];
    foreach ($product_obj->getFreeFeatures() as $name => $values) {
        foreach ($values as $value) {
            $product_data['free_features'][] = ['name' => $name, 'value' => $value];
        }
    }

    if (\count($product_data['tax_ids']) === 1) {
        $product_data['tax_ids'] = $product_data['tax_ids'][\array_key_first($product_data['tax_ids'])];
    }

    $taxes = fn_get_taxes();
    arsort($product_data['category_ids']);

    // reload form (refresh categories list if vendor was changed)
    if (defined('AJAX_REQUEST') && !empty($_REQUEST['reload_form'])) {
        $company_id = isset($_REQUEST['product_data']['company_id']) ? $_REQUEST['product_data']['company_id'] : 0;
        $company_data = fn_get_company_data($company_id);
        if (!empty($company_data['categories'])) {
            $params = array (
                'simple' => false,
                'company_ids' => $company_id,
            );
            list($cat_ids, ) = fn_get_categories($params);
            $cat_ids = array_keys($cat_ids);
            //Assign available category ids to be displayed after admin changes product owner.
            $product_data['category_ids'] = array_intersect($product_data['category_ids'], $cat_ids);
        }
        //Assign received company_id to product data for the correct company categories to be displayed in the picker.
        $product_data['company_id'] = $company_id;
        Registry::get('view')->assign('product_data', $product_data);
        Registry::get('view')->assign('productDataPlaceholder', $productDataPlaceholder);
        $saveProductForm->setData($product_data);
        Registry::get('view')->assign('saveProductForm', $saveProductForm->createView());
        Registry::get('view')->display('views/products/update.tpl');
        exit;
    }

    /** @var ProductVisibilityReportService */
    $productVisibilityReportService = $container->get('marketplace.product.product_visibility_report_service');
    $productVisibilityReport = $productVisibilityReportService->getVisibilityReportFromCsCartData(
        $product_data,
        Registry::get('user_info')
    );
    if ($productVisibilityReport->isProductVisible() === false) {
        Registry::get('view')->assign('productVisibilityReport', $productVisibilityReport);
    }

    Registry::get('view')->assign('category_obj', new \Wizacha\Category($product_data['main_category']));
    Registry::get('view')->assign('product_data', $product_data);
    Registry::get('view')->assign('productDataPlaceholder', $productDataPlaceholder);

    $productPriceTiers = $priceTiersService->getPriceTiersOfProductOrCombination($product_data['product_id'], null);

    $product_data[ProductPriceType::PRICE_TIERS_FIRST_INTERVAL_QUANTITY_MAX_LIMIT] = 0;

    if (\count($productPriceTiers) > 1) {
        $product_data[ProductPriceType::PRICE_TIERS_FIRST_INTERVAL_QUANTITY_MAX_LIMIT] = $productPriceTiers[1]->getLowerLimit();
    }

    $saveProductForm->setData($product_data);
    Registry::get('view')->assign('saveProductForm', $saveProductForm->createView());
    Registry::get('view')->assign('featureSubscription', $container->getParameter('feature.subscription'));
    Registry::get('view')->assign('taxes', $taxes);

    $product_options = fn_get_product_options($_REQUEST['product_id'], (string) GlobalState::contentLocale());
    $productOptionsPlaceholder = fn_get_product_options($_REQUEST['product_id'], (string) GlobalState::interfaceLocale());
    if (!empty($product_options)) {
        $has_inventory = false;
        foreach ($product_options as $p) {
            if ($p['inventory'] == 'Y') {
                $has_inventory = true;
                break;
            }
        }
        Registry::get('view')->assign('has_inventory', $has_inventory);
    }
    Registry::get('view')->assign('product_options', $product_options);
    Registry::get('view')->assign('productOptionsPlaceholder', $productOptionsPlaceholder);
    list($global_options) = fn_get_product_global_options();
    Registry::get('view')->assign('global_options', $global_options);
    Registry::get('view')->assign('hasDeclinations', $product_obj->hasDeclinations());

    // If the product is electronically distributed, get the assigned files
    list($product_files) = fn_get_product_files(array('product_id' => $_REQUEST['product_id']));
    list($product_file_folders) = fn_get_product_file_folders( array('product_id' => $_REQUEST['product_id']) );
    $files_tree = fn_build_files_tree($product_file_folders, $product_files);

    // get attachments
    $productService = $container->get('marketplace.pim.product.service');
    $attachments = $productService->getAttachments((int) $_REQUEST['product_id']);

    Registry::get('view')->assign('product_file_folders', $product_file_folders);
    Registry::get('view')->assign('product_files', $product_files);
    Registry::get('view')->assign('files_tree', $files_tree);
    Registry::get('view')->assign('attachments', $attachments);

    Registry::get('view')->assign('expand_all', true);

    // [Page sections]
    $tabs = array (
        'detailed' => array (
            'title' => __('general'),
            'js' => true
        ),
        'free_features' => [
            'title' => __('free_features'),
            'js' => true,
        ],
        'images' => array (
            'title' => __('images'),
            'js' => true
        ),
        'options' => array (
            'title' => __('options'),
            'js' => true
        ),
        'qty_discounts' => array (
            'title' => __('qty_discounts'),
            'js' => true
        ),
        'files' => array (
            'title' => __('files'),
            'js' => true
        ),
        'related_products' => [
            'title' => __('related_products'),
            'js' => true,
        ],
    );

    if ($container->getParameter('feature.enable_product_attachments')) {
        $tabs['attachments'] = [
            'title' => __('attachments'),
            'js' => true
        ];
    }

    if ($container->getParameter('feature.available_offers')) {
        $marketplaceDivisionSettings = $divisionSettingsService->getMarketplaceDivisionSettings();
        $companyDivisionSettings = $divisionSettingsService
            ->getCompanyDivisionSettings((int) $product_data['company_id'])
        ;

        $companyAvailableDivisions = $companyDivisionSettings->getIncludedDivisions()->toArray();
        $marketplaceAvailableDivisions = $marketplaceDivisionSettings->getIncludedDivisions()->toArray();

        Registry::get('view')->assign([
            'availableDivisions' => $companyDivisionSettings->getIncludedDivisions()->toArray(),
            'unavailableDivisions' => $companyDivisionSettings->getExcludedDivisions()->toArray(),
            'marketplaceDivisions' => [
                'available' => $marketplaceAvailableDivisions,
                'unavailable' => $marketplaceDivisionSettings->getExcludedDivisions()->toArray(),
            ],
        ]);
        $tabs['available_offers'] = [
            'title' => __('available_offers_product'),
            'href' => 'available_offers.ajax_load&product='.$_REQUEST['product_id'], // for ajax load
            'js' => true
        ];

        $productDivisionSettings = $divisionSettingsService->getProductDivisionSettings($product_data['product_id']);
        $productAvailableDivisions = $productDivisionSettings->getIncludedDivisions()->toArray();

        $warningMsg = \count($productAvailableDivisions) === 0 ? __('no_division_for_product') : null;
        Registry::get('view')->assign('warningMsg', $warningMsg);

        if (\count($marketplaceAvailableDivisions) === 0) {
            $noDivisionEnteredMsg = __('no_division_for_product_in_marketplace');
        } elseif (\count($companyAvailableDivisions) === 0) {
            $noDivisionEnteredMsg = __('no_division_for_vendor_in_product');
        } else {
            $noDivisionEnteredMsg = null;
        }

        Registry::get('view')->assign('noDivisionEnteredMsg', $noDivisionEnteredMsg);
    }

    $tabs['addons'] = array (
        'title' => __('addons'),
        'js' => true
    );

    // If we have some additional product fields, lets add a tab for them
    if (!empty($product_data['product_features'])) {
        $tabs['features'] = array(
            'title' => __('features'),
            'js' => true
        );
    }

    if (Registry::get('settings.Shippings.disable_shipping') !== 'Y') {
        $tabs['shippings'] = [
            'class' => 'transactional-shown',
            'title' => __('shipping_properties'),
            'js' => true,
        ];
    }

    Registry::set('navigation.tabs', $tabs);
    // [/Page sections]

    $mvp = $container->getParameter('feature.multi_vendor_product') ? $productService->get($_REQUEST['product_id'])->getMultiVendorProduct() : null;
    Registry::get('view')->assign('linked_mvp', $mvp);
    Registry::get('view')->assign('transaction_modes', $container->get('marketplace.transaction_mode.service')->getAvailablesModesAsArray());
    Tygh\Registry::get('view')->assign('w_load', true);

    // Product preview
    $productId = $_REQUEST['product_id'];
    if ($isProductValidInFront = \Wizacha\Product::frontIds([$productId])->valid()) {
        $bfoUrl = $container->getParameter('config.bfo_url');
        if ($container->getParameter('feature.enable_bfo')
            && \is_null($bfoUrl) === false
            && $bfoUrl !== '') {
            $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
            $slug = fn_seo_get_name('p', $productId, '', null, $lang_code);
            // mimic a 'BFO' product url: /p{productId}-{productSlug}
            Registry::get('view')->assign('preview_uri', $bfoUrl.'/p'.$productId.'-'.$slug);
        } else {
            $remoteFrontUrl = $container->getParameter('feature.disable_front_office') ? $container->getParameter('external_front_office_url') : null;
            if ($remoteFrontUrl) {
                Registry::get('view')->assign('preview_uri', (string)\GuzzleHttp\Psr7\Uri::composeComponents(
                    'http',
                    $remoteFrontUrl,
                    '/product/'.$productId,
                    '',
                    ''
                ));
            } else {
                Registry::get('view')->assign('preview_uri', fn_url('products.view?product_id='.$productId, 'C'));
            }
        }
    }

    if (\count($container->get('marketplace.price_tier.price_tier_repository')->findByProductId($product_data['product_id'])) === 0) {
        $container->get('marketplace.price_tier.price_tier_service')->savePriceTiers(
            $product_data['product_id'],
            [
                [
                    'product_id' => $product_data['product_id'],
                    'combination' => null,
                    'priceTiers' => [
                        [
                            'lowerLimit' => 0,
                            'price' => (float) $product_data['price'],
                        ],
                    ],
                ],
            ]
        );
    };

    Registry::get('view')->assign('is_product_is_valid', $isProductValidInFront);

    // START Related Product variables part
    $availableTypes = $relatedProductsService->getAvailableTypes();
    Registry::get('view')->assign('available_types', $availableTypes);

    $relatedProducts = $relatedProductsService->findByFromProductIdForBO($product_data['product_id']);
    Registry::get('view')->assign('related_products', $relatedProducts);

    $relatedProductTypePrefix = RelatedProductsType::RELATED_PRODUCT_TYPE_PREFIX;
    Registry::get('view')->assign('related_product_type_prefix', $relatedProductTypePrefix);

    Registry::get('view')->assign('from_company_id', $product_data['company_id']);

    $user = Registry::get('user_info');
    Registry::get('view')->assign('user_type',  $user['user_type']);

    $runtimeCompanyId = Registry::get('runtime.company_id');
    Registry::get('view')->assign('runtime_company_id', $runtimeCompanyId);
    // END Related Product variables part

    $catalogProductService = $container->get('marketplace.product.productservice');
    // Check if the catalog is up-to-date and if not, return the last update
    $productUpToDateMessage = $catalogProductService->getIsUpToDateAndLastUpdate($product_data['product_id']);

    Registry::get('view')->assign('is_up_to_date',  $productUpToDateMessage->isUpToDate);
    Registry::get('view')->assign('message', $productUpToDateMessage->message);
    Registry::get('view')->assign('message_data', $productUpToDateMessage->messageData);

//
// 'Mulitple products updating' page
//
} elseif ($mode == 'm_update') {

    if (empty($_SESSION['product_ids']) || empty($_SESSION['selected_fields']) || empty($_SESSION['selected_fields']['object']) || $_SESSION['selected_fields']['object'] != 'product') {
        return array(CONTROLLER_STATUS_REDIRECT, "products.manage");
    }

    $product_ids = $_SESSION['product_ids'];

    if (!fn_company_products_check($product_ids)) {
        return array(CONTROLLER_STATUS_DENIED);
    }

    $selected_fields = $_SESSION['selected_fields'];

    $field_groups = array (
        'A' => array ( // inputs
            'product' => 'products_data',
            'product_code' => 'products_data',
            'page_title' => 'products_data',
        ),

        'B' => array ( // short inputs
            'price' => 'products_data',
            'list_price' => 'products_data',
            'amount' => 'products_data',
            'min_qty' => 'products_data',
            'max_qty' => 'products_data',
            'weight' => 'products_data',
            'shipping_freight' => 'products_data',
            'box_height' => 'products_data',
            'box_length' => 'products_data',
            'box_width' => 'products_data',
            'min_items_in_box' => 'products_data',
            'max_items_in_box' => 'products_data',
            'qty_step' => 'products_data',
            'list_qty_count' => 'products_data',
        ),

        'C' => array ( // checkboxes
            'is_edp' => 'products_data',
            'edp_shipping' => 'products_data',
            'free_shipping' => 'products_data',
        ),

        'D' => array ( // textareas
            'short_description' => 'products_data',
            'full_description' => 'products_data',
            'meta_keywords' => 'products_data',
            'meta_description' => 'products_data',
            'search_words' => 'products_data',
        ),
        'T' => array( // dates
            'timestamp' => 'products_data',
            'avail_since' => 'products_data',
        ),
        'S' => array ( // selectboxes
            'out_of_stock_actions' => array (
                'name' => 'products_data',
                'variants' => array (
                    'N' => 'none',
                    'B' => 'buy_in_advance',
                    'S' => 'sign_up_for_notification'
                ),
            ),
            'status' => array (
                'name' => 'products_data',
                'variants' => array (
                    'A' => 'active',
                    'D' => 'disabled',
                    'H' => 'hidden'
                ),
            ),
            'tracking' => array (
                'name' => 'products_data',
                'variants' => array (
                    'O' => 'track_with_options',
                    'B' => 'track_without_options',
                    'D' => 'dont_track'
                ),
            ),
            'zero_price_action' => array (
                'name' => 'products_data',
                'variants' => array (
                    'R' => 'zpa_refuse',
                    'P' => 'zpa_permit',
                    'A' => 'zpa_ask_price'
                ),
            ),
        ),
        'E' => array ( // categories
            'categories' => 'products_data'
        ),
        'W' => array( // Product details layout
            'details_layout' => 'products_data'
        )
    );

    $field_groups['L'] = array( // miltiple selectbox (localization)
        'localization' => array(
            'name' => 'localization'
        ),
    );

    $data = array_keys($selected_fields['data']);
    $get_main_pair = false;
    $get_taxes = false;
    $get_features = false;

    $fields2update = $data;

    // Process fields that are not in products or product_descriptions tables
    if (!empty($selected_fields['categories']) && $selected_fields['categories'] == 'Y') {
        $fields2update[] = 'categories';
    }
    if (!empty($selected_fields['main_pair']) && $selected_fields['main_pair'] == 'Y') {
        $get_main_pair = true;
        $fields2update[] = 'main_pair';
    }
    if (!empty($selected_fields['data']['taxes']) && $selected_fields['data']['taxes'] == 'Y') {
        Registry::get('view')->assign('taxes', fn_get_taxes());
        $fields2update[] = 'taxes';
        $get_taxes = true;
    }
    if (!empty($selected_fields['data']['features']) && $selected_fields['data']['features'] == 'Y') {
        $fields2update[] = 'features';
        $get_features = true;

        // get features for categories of selected products only
        $id_paths = db_get_fields("SELECT ?:categories.id_path FROM ?:products_categories LEFT JOIN ?:categories ON ?:categories.category_id = ?:products_categories.category_id WHERE product_id IN (?n)", $product_ids);

        $_params = array(
            'variants' => true,
            'category_ids' => array_unique(explode('/', implode('/', $id_paths)))
        );

        list($all_product_features) = fn_get_product_features($_params, 0, (string) GlobalState::contentLocale());

        Registry::get('view')->assign('all_product_features', $all_product_features);
    }

    foreach ($product_ids as $value) {
        $products_data[$value] = fn_get_product_data($value, $auth, (string) GlobalState::contentLocale(), '?:products.*, ?:product_descriptions.*', false, $get_main_pair, $get_taxes, false, false, $get_features, true);
    }

    $filled_groups = array();
    $field_names = array();

    foreach ($fields2update as $k => $field) {
        if ($field == 'main_pair') {
            $desc = 'image_pair';
        } elseif ($field == 'tracking') {
            $desc = 'inventory';
        } elseif ($field == 'edp_shipping') {
            $desc = 'downloadable_shipping';
        } elseif ($field == 'is_edp') {
            $desc = 'downloadable';
        } elseif ($field == 'timestamp') {
            $desc = 'creation_date';
        } elseif ($field == 'categories') {
            $desc = 'categories';
        } elseif ($field == 'status') {
            $desc = 'status';
        } elseif ($field == 'avail_since') {
            $desc = 'available_since';
        } elseif ($field == 'min_qty') {
            $desc = 'min_order_qty';
        } elseif ($field == 'max_qty') {
            $desc = 'max_order_qty';
        } elseif ($field == 'qty_step') {
            $desc = 'quantity_step';
        } elseif ($field == 'list_qty_count') {
            $desc = 'list_quantity_count';
        } elseif ($field == 'details_layout') {
            $desc = 'product_details_layout';
        } elseif ($field == 'max_items_in_box') {
            $desc = 'maximum_items_in_box';
        } elseif ($field == 'min_items_in_box') {
            $desc = 'minimum_items_in_box';
        } elseif ($field == 'amount') {
            $desc = 'quantity';
        } else {
            $desc = $field;
        }

        if (!empty($field_groups['A'][$field])) {
            $filled_groups['A'][$field] = __($desc);
            continue;
        } elseif (!empty($field_groups['B'][$field])) {
            $filled_groups['B'][$field] = __($desc);
            continue;
        } elseif (!empty($field_groups['C'][$field])) {
            $filled_groups['C'][$field] = __($desc);
            continue;
        } elseif (!empty($field_groups['D'][$field])) {
            $filled_groups['D'][$field] = __($desc);
            continue;
        } elseif (!empty($field_groups['S'][$field])) {
            $filled_groups['S'][$field] = __($desc);
            continue;
        } elseif (!empty($field_groups['T'][$field])) {
            $filled_groups['T'][$field] = __($desc);
            continue;
        } elseif (!empty($field_groups['E'][$field])) {
            $filled_groups['E'][$field] = __($desc);
            continue;
        } elseif (!empty($field_groups['L'][$field])) {
            $filled_groups['L'][$field] = __($desc);
            continue;
        } elseif (!empty($field_groups['W'][$field])) {
            $filled_groups['W'][$field] = __($desc);
            continue;
        }

        $field_names[$field] = __($desc);
    }


    ksort($filled_groups, SORT_STRING);

    Registry::get('view')->assign('field_groups', $field_groups);
    Registry::get('view')->assign('filled_groups', $filled_groups);

    Registry::get('view')->assign('field_names', $field_names);
    Registry::get('view')->assign('products_data', $products_data);

//
// Delete product
//
} elseif ($mode == 'delete') {

    if (!empty($_REQUEST['product_id'])) {
        try {
            $result = fn_delete_product($_REQUEST['product_id']);
            if ($result) {
                fn_set_notification('N', __('notice'), __('text_product_has_been_deleted'));
            } else {
                return array(CONTROLLER_STATUS_REDIRECT, "products.update?product_id=$_REQUEST[product_id]");
            }
        } catch (Forbidden $e) {
            fn_set_notification('W', __('warning'), __('access_denied'));
            return array(CONTROLLER_STATUS_REDIRECT, "products.update?product_id=$_REQUEST[product_id]");
        }
    }

    return array(CONTROLLER_STATUS_REDIRECT, "products.manage");

} elseif ($mode == 'get_file') {

    $response = fn_get_product_file($_REQUEST['file_id'], !empty($_REQUEST['file_type']));
    if ($response == false) {
        return array(CONTROLLER_STATUS_DENIED);
    } else {
        return $response;
    }

} elseif ($mode == 'clone') {
    if (!empty($_REQUEST['product_id'])) {
        $pid = $_REQUEST['product_id'];
        $pdata = fn_clone_product($pid);
        if (!empty($pdata['product_id'])) {
            $pid = $pdata['product_id'];
            fn_set_notification('N', __('notice'), __('text_product_cloned', ['[product]' => $pdata['orig_name']]));
        }

        return array(CONTROLLER_STATUS_REDIRECT, "products.update?product_id=$pid");
    }
} elseif ($mode == 'update_file') {

    if (!empty($_REQUEST['product_id'])) {

        if (!empty($_REQUEST['file_id'])) {
            $params = array (
                'product_id' => $_REQUEST['product_id'],
                'file_ids' => $_REQUEST['file_id']
            );

            list($product_files) = fn_get_product_files($params);
            $product_file = reset($product_files);
            $product_file['company_id'] = db_get_field('SELECT company_id FROM ?:products WHERE product_id = ?i', $_REQUEST['product_id']);

            Registry::get('view')->assign('product_file', $product_file);
        }

        list($product_file_folders) = fn_get_product_file_folders(array('product_id' => $_REQUEST['product_id']));
        Registry::get('view')->assign('product_file_folders', $product_file_folders);

        Registry::get('view')->assign('product_id', $_REQUEST['product_id']);
    }

} elseif ($mode == 'update_folder') {

    if (!empty($_REQUEST['product_id'])) {

        if (!empty($_REQUEST['folder_id'])) {
            $params = array (
                'product_id' => $_REQUEST['product_id'],
                'folder_ids' => $_REQUEST['folder_id']
            );

            list($product_file_folders) = fn_get_product_file_folders($params);
            $product_file_folder = reset($product_file_folders);
            $product_file_folder['company_id'] = db_get_field('SELECT company_id FROM ?:products WHERE product_id = ?i', $_REQUEST['product_id']);

            Registry::get('view')->assign('product_file_folder', $product_file_folder);
        }

        Registry::get('view')->assign('product_id', $_REQUEST['product_id']);
    }

} elseif ($mode == 'delete_file') {

    if (!empty($_REQUEST['file_id']) && !empty($_REQUEST['product_id'])) {

        if (fn_delete_product_files($_REQUEST['file_id']) == false) {
            return array(CONTROLLER_STATUS_DENIED);
        }

        list($_files) = fn_get_product_files(array('product_id' => $_REQUEST['product_id']));
        list($_folder) = fn_get_product_file_folders(array('product_id' => $_REQUEST['product_id']));

        if (empty($_files) && empty($_folder)) {
            Registry::get('view')->assign('product_id', $_REQUEST['product_id']);
        }
    }

    return array(CONTROLLER_STATUS_OK, fn_url('products.update?product_id=' . $_REQUEST['product_id'] . '&selected_section=files'));

} elseif ($mode == 'delete_folder') {

    if (!empty($_REQUEST['folder_id']) && !empty($_REQUEST['product_id'])) {

        if (fn_delete_product_file_folders($_REQUEST['folder_id'], $_REQUEST['product_id']) == false) {
            return array(CONTROLLER_STATUS_DENIED);
        }

        list($product_files) = fn_get_product_files(array('product_id' => $_REQUEST['product_id']));
        list($product_file_folders) = fn_get_product_file_folders( array('product_id' => $_REQUEST['product_id']) );
        $files_tree = fn_build_files_tree($product_file_folders, $product_files);

        Registry::get('view')->assign('product_file_folders', $product_file_folders);
        Registry::get('view')->assign('product_files', $product_files);
        Registry::get('view')->assign('files_tree', $files_tree);

        Registry::get('view')->assign('product_id', $_REQUEST['product_id']);
    }

    return array(CONTROLLER_STATUS_OK, fn_url('products.update?product_id=' . $_REQUEST['product_id'] . '&selected_section=files'));
} elseif ($mode == 'get_attachment') {
    $productService = $container->get('marketplace.pim.product.service');
    $attachment = $productService->getAttachment($_REQUEST['id']);

    // If the current user is a vendor, and this is not the same company than the product: deny access
    if (Registry::get('runtime.company_id') && $attachment->getProduct()->getCompanyId() != Registry::get('runtime.company_id')) {
        return [CONTROLLER_STATUS_DENIED];
    }

    try {
        return $attachment->getFile();
    } catch (FileNotFoundException $exception) {
        fn_set_notification('E', __('error'), $exception->getMessage());
        return [CONTROLLER_STATUS_OK, 'products.update?product_id='.$_REQUEST['product_id'].'&selected_section=attachments'];
    }
} elseif ($mode == 'delete_attachment') {
    $productService = $container->get('marketplace.pim.product.service');
    $attachment = $productService->getAttachment($_REQUEST['id']);

    // If the current user is a vendor, and this is not the same company than the product: deny access
    if (Registry::get('runtime.company_id') && $attachment->getProduct()->getCompanyId() != Registry::get('runtime.company_id')) {
        return [CONTROLLER_STATUS_DENIED];
    }

     // CSRF
    if (!$container->get('security.csrf.token_manager')->isTokenValid(new CsrfToken('bo_delete_attachment', $_REQUEST['token'] ?? ''))) {
        return [CONTROLLER_STATUS_DENIED];
    }

    $productService->removeAttachment($attachment);

    // http://wizaplace.loc/admin.php?dispatch=products.update&product_id=1&page=1&selected_section=attachments
    return [CONTROLLER_STATUS_OK, 'products.update?product_id='.$_REQUEST['product_id'].'&selected_section=attachments'];
//
// Processing deletion of all products of a vendor
//
} elseif ($mode == 'vendor_delete_all') {
    if (!Registry::get('runtime.company_id')) {
        fn_set_notification('E', __('error'), 'missing company_id parameter');
        return [CONTROLLER_STATUS_DENIED];
    }
    \Wizacha\Product::deleteFromCompany(Registry::get('runtime.company_id'));
    fn_set_notification('N', __('notice'), __('text_products_deletion_scheduled'));
    return [CONTROLLER_STATUS_REDIRECT, "products.manage"];
} elseif ($mode == 'create_mvp'){
    try {
        if (fn_can_create_mvp_from_product($_REQUEST['product_id'])) {
            $container->get('marketplace.multi_vendor_product.service')->createFromProduct($_REQUEST['product_id']);
        }
    } catch (\Wizacha\Marketplace\Exception\NotFound $e) {
        fn_set_notification('E', __('error'), __('error_creating_mvp_from_product'));
    }

    return array(CONTROLLER_STATUS_OK, fn_url('products.update?product_id='.$_REQUEST['product_id']));
}
else if ($mode == 'related_products_add' ) {
    $type = \array_key_exists('type', $_REQUEST) === true ? $_REQUEST['type'] : null;
    $productId = \array_key_exists('product_id', $_REQUEST) === true ? $_REQUEST['product_id'] : null;
    $fromCompanyId = \array_key_exists('from_company_id', $_REQUEST) === true ? $_REQUEST['from_company_id'] : null;

    Registry::get('view')->assign('type', $type);
    Registry::get('view')->assign('product_id', $productId);
    Registry::get('view')->assign('from_company_id', $fromCompanyId);
}

if ($mode == 'related_products_picker') {
    // The following params allow to search on name, ean code and supplier reference
    // with an OR condition and LIKE comparison
    $params = [
        //param to specify an OR, for search on name and ean code
        'compact' => 'Y',
        //param to specify a LIKE for the product name
        'match' => 'any',
        //combination of params pname and q to search on product name
        'pname' => 'Y',
        'q' => $_REQUEST['search_text'],
        //param for ean code search value
        'pcode' => $_REQUEST['search_text'],
        //param to specify an OR for supplier reference
        'supplier_ref_compact' => 'Y',
        //param to specify a LIKE for supplier reference
        'supplier_ref_match' => 'any',
        //param for supplier reference search value
        'supplier_ref' => $_REQUEST['search_text'],
        //sorting on product name
        'sort_by' => 'product',
    ];

    list($products) = fn_get_products($params, 10);

    $productService = $container->get('marketplace.pim.product.service');

    $result = [];
    foreach ($products as $product) {
        if ($_REQUEST['product_id'] == $product['product_id']) {
            continue;
        }

        $p = new Product($product['product_id'], true);

        $result[] = [
            'product_id' => $p->getId(),
            'product_name' => $p->getName(),
            'product_code' => $p->getProductCode(),
            'product_supplier_ref' => $p->getSupplierRef(),
            'product_company_id' => $p->getCompany()->getId(),
            'product_company_name' => $p->getCompany()->getName(),
            'product_image' => $p->getMainImage() ? $p->getMainImage()->getId() : null,
        ];
    }

    Registry::get('view')->assign('search_products', $result);
    Registry::get('view')->assign('type', $_REQUEST['type']);
    Registry::get('view')->assign('from_company_id', $_REQUEST['from_company_id']);

    $user = Registry::get('user_info');
    Registry::get('view')->assign('user_type',  $user['user_type']);

    $runtimeCompanyId = Registry::get('runtime.company_id');
    Registry::get('view')->assign('runtime_company_id', $runtimeCompanyId);

    Registry::get('view')->display('views/products/components/products_related_search_list.tpl');

    //There is no associated template, we must exit to avoid 404 result from exit.php controller script
    //(app/addons/wizacha_backend_design/controllers/backend/exit.php)
    exit;
}
