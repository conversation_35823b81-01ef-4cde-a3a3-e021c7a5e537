<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright	Copyright (c) Wizacha
 * @license		Proprietary
 */

use Tygh\Registry;

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    return array(CONTROLLER_STATUS_OK);
}

/**
 * $status is given to ajax and he refresh the status on the view
 */

if ($mode == 'update_status' && $_REQUEST['table'] == 'companies') {
    $company_id = Registry::get('runtime.company_id');
    $shipping_id = $_REQUEST['id'];
    $status = $_REQUEST['status'];

    switch($status) {
        case 'A':
            $result = \Wizacha\Company::addShipping($company_id, $shipping_id);
            break;
        case 'D':
            $result = \Wizacha\Company::removeShipping($company_id, $shipping_id);
            break;
        default:
            $result = false;
            break;
    }

    \Wizacha\Product::checkAllProductsShippings($company_id);

    if ($result) {
        fn_set_notification('N', __('notice'), __('status_changed'));
        \Wizacha\Product::checkAllProductsShippings($company_id);
        Registry::get('ajax')->assign('return_status', $status);
    } else {
        fn_set_notification('E', __('error'), __('error_status_not_changed'));
    }

}
