<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, Ilya M<PERSON> Shalnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Tygh\Registry;
use Tygh\Settings;
use Wizacha\AppBundle\Security\User\ApiSecurityUser;
use Wizacha\AppBundle\Security\User\SecurityUser;
use Wizacha\Cscart\AppearanceType;
use Wizacha\FeatureFlag\FeatureFlagService;
use Wizacha\Marketplace\Company\CompanyRepository;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Discuss\Entity\DiscussionInterface;
use Wizacha\Marketplace\MessageAttachment\MessageAttachmentService;
use Wizacha\Marketplace\Order\AmountsCalculator\Entity\OrderAmountsCommission;
use Wizacha\Marketplace\Order\AmountsCalculator\Service\OrderAmountsCalculator;
use Wizacha\Marketplace\Exception\FileNotFoundException;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Order\Action\Accept;
use Wizacha\Marketplace\Order\Action\ActionName;
use Wizacha\Marketplace\Order\Action\Cancel;
use Wizacha\Marketplace\Order\Action\DeclareInvoiceNumberGeneratedElsewhere;
use Wizacha\Marketplace\Order\Action\DispatchFunds;
use Wizacha\Marketplace\Order\Action\MarkAsPaid;
use Wizacha\Marketplace\Order\Action\MarkAsShipped;
use Wizacha\Marketplace\Order\Action\ProvideInvoiceNumber;
use Wizacha\Marketplace\Order\Action\Refuse;
use Wizacha\Marketplace\Order\Adjustment\OrderAdjustment;
use Wizacha\Marketplace\Order\Exception\AdjustmentException;
use Wizacha\Marketplace\Order\Exception\OrderNotFound;
use Wizacha\Marketplace\Order\OrderAttachment\Entity\OrderAttachment;
use Wizacha\Marketplace\Order\OrderAttachment\Exception\UploadFailException;
use Wizacha\Marketplace\Order\OrderAttachment\Service\OrderAttachmentService;
use Wizacha\Marketplace\Order\OrderDataType;
use Wizacha\Marketplace\Order\OrderReturn\OrderReturn;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\OrderStatus as MarketplaceOrderStatus;
use Wizacha\Marketplace\Order\Refund\Entity\Refund;
use Wizacha\Marketplace\Order\Refund\Enum\OrderRefundStatus;
use Wizacha\Marketplace\Order\Refund\Enum\RefundStatus;
use Wizacha\Marketplace\Order\Refund\Exception\RefundNotFoundException;
use Wizacha\Marketplace\Order\Refund\Service\RefundNotificationService;
use Wizacha\Marketplace\Order\Refund\Service\RefundService;
use Wizacha\Marketplace\Order\Workflow\ModuleName;
use Wizacha\Marketplace\Payment\PaymentType;
use Wizacha\Marketplace\Price\DetailedPriceFormatter;
use Wizacha\Marketplace\Subscription\SubscriptionRepository;
use Wizacha\Marketplace\Subscription\Subscription;
use Wizacha\Marketplace\Subscription\SubscriptionLinkStatus;
use Wizacha\Marketplace\Subscription\SubscriptionService;
use Wizacha\Marketplace\Subscription\SubscriptionStatus;
use Wizacha\Marketplace\Transaction\Exception\TransactionNotFound;
use Wizacha\Marketplace\Transaction\Transaction;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionStatus;
use Wizacha\Marketplace\Transaction\TransactionType;
use Wizacha\Marketplace\User\UserType;
use Wizacha\Money\Money;
use Wizacha\Order;
use Wizacha\OrderStatus;
use Wizacha\Shipping;
use Wizacha\User;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

fn_define('GOOGLE_ORDER_DATA', 'O');

const REFUND_KEY = 'refund';

$container = container();
/** @var OrderService */
$orderService = $container->get('marketplace.order.order_service');
/** @var RefundService */
$refundService = $container->get('marketplace.order.refund.refund_service');
/** @var RefundNotificationService */
$refundNotificationService = $container->get('marketplace.order.refund.refund_notification_service');
/** @var TransactionService */
$transactionService = $container->get('marketplace.transaction.transaction_service');
/** @var OrderAttachmentService */
$attachmentsService = $container->get(OrderAttachmentService::class);
/** @var OrderAmountsCalculator */
$orderAmountsCalculator = $container->get('app.order.amount.calculator');
/** @var CompanyService */
$companyService = $container->get('marketplace.company_service');

$discussionService = $container->get('app.discuss_service');
$discuss = $discussionService->getDiscussClient();
$companyRepository = container()->get(CompanyRepository::class);
$isDiscussionsAllowedForAdmin = container()->get(FeatureFlagService::class)->get('feature.discussions_allowed_for_admin');

/**
 * @var array $auth
 * @var string $mode
 */
if ($_SERVER['REQUEST_METHOD'] == 'POST') {

    $user = $container->get('marketplace.user.user_service')->get($auth['user_id']);

    $suffix = '';
    if ('update_details' == $mode) {
        if (Registry::get('runtime.company_id')) {
            $invoice_number = db_get_field("SELECT w_invoice_number FROM ?:orders WHERE order_id = ?i", $_REQUEST['order_id']);
            if ($invoice_number) {
                $_REQUEST['update_order']['w_invoice_number'] = $_POST['update_order']['w_invoice_number'] = $invoice_number;
            }
        }

        // EXTRA DATA
        if (\count($_REQUEST['extra_order_data']) > 0) {
            // old data except deleted one
            $oldExtraData = $_REQUEST['extra_order_data']['extra'] ?? [];

            if (\array_key_exists('extra_to_add', $_REQUEST['extra_order_data'])
                && \count($_REQUEST['extra_order_data']['extra_to_add']) > 0
            ) {
                // Remove empty field
                $addedExtra = \array_filter($_REQUEST['extra_order_data']['extra_to_add'], static function (array $extra): bool {
                    return $extra['name'] !== '' && $extra['value'] !== '';
                });

                $addedExtra = \array_combine(
                    \array_column($addedExtra, 'name'),
                    \array_column($addedExtra, 'value')
                );

                // Ignore existing key
                $intersectKeysExtra = \array_keys(\array_intersect_key($oldExtraData, $addedExtra));
                $addedExtra = \array_diff_key($addedExtra, \array_flip($intersectKeysExtra));

                $extraOrder = $oldExtraData + $addedExtra;
            }

            \array_walk(
                $extraOrder,
                static function(&$extraValue): void {
                    if (\is_numeric($extraValue) === true
                        && \strpos($extraValue, '.') === false
                        && \strpos($extraValue, ',') === false
                    ) {
                        $extraValue = \intval($extraValue);
                    }
                }
            );

            $orderService->setExtraData($_REQUEST['order_id'], \json_encode($extraOrder));
        }

        fn_trusted_vars('update_order');

        // Update customer's email if its changed in customer's account
        if (!empty($_REQUEST['update_customer_details']) && $_REQUEST['update_customer_details'] == 'Y') {
            $u_id = db_get_field("SELECT user_id FROM ?:orders WHERE order_id = ?i", $_REQUEST['order_id']);
            $current_email = db_get_field("SELECT email FROM ?:users WHERE user_id = ?i", $u_id);
            db_query("UPDATE ?:orders SET email = ?s WHERE order_id = ?i", $current_email, $_REQUEST['order_id']);
        }

        // Log order update
        fn_log_event('orders', 'update', [
            'order_id' => $_REQUEST['order_id'],
            'company_id' => fn_get_company_id('orders', 'order_id', $_REQUEST['order_id']),
        ]);

        $update_order = $_REQUEST['update_order'];
        unset($update_order['notes']); // cannot edit notes
        $update_order['do_not_create_invoice'] = false;

        // update invoice date
        $update_order['invoice_date'] = (new DateTime())->format('Y-m-d H:i:s');

        if (isset($_REQUEST['update_order']['do_not_create_invoice'])) {
            $update_order['do_not_create_invoice'] = true;
        }

        if ($companyService->isInvoicingDisabled((int) fn_get_company_id('orders', 'order_id', $_REQUEST['order_id'])) === true
            && ($orderService->getStatus($_REQUEST['order_id'])->getValue() === OrderStatus::STANDBY_BILLING || $orderService->getStatus($_REQUEST['order_id'])->getValue() === OrderStatus::PROCESSING_SHIPPING)
        ) {
            $update_order['do_not_create_invoice'] = true;
            $update_order['w_invoice_number'] = null;
        }

        db_query('UPDATE ?:orders SET ?u WHERE order_id = ?i', $update_order, $_REQUEST['order_id']);

        //Update shipping info
        if (!empty($_REQUEST['update_shipping'])) {
            foreach ($_REQUEST['update_shipping'] as $group_key => $shipment) {
                $shipment['shipment_data']['order_id'] = $_REQUEST['order_id'];
                $shipment_id = isset($shipment['shipment_id']) ? $shipment['shipment_id'] : 0;
                fn_update_shipment($shipment['shipment_data'], $shipment_id, $group_key, true);
            }
        }

        // If the Lemonway cron (our side) fails to match an order with a transfer, the order is flagged with 'lemonway_bank_transfer_require_check'
        // An input is shown only with this flag, this piece of code handle its value (the lemonway transaction id)
        // It triggers the withdrawal of the commission, and the order status is changed to paid
        if (AREA === 'A' && !empty($_REQUEST['update_order']['lemonway_transaction_id'])) {
            $lemonway = $container->get('marketplace.payment.lemonway');
            if (!$lemonway->linkTransferIdToOrder($_REQUEST['update_order']['lemonway_transaction_id'], new Order($_REQUEST['order_id']))) {
                fn_set_notification('E', __('error'), __('lemonway_manual_check_failed'));
            }
        }

        $order_info = $orderService->overrideLegacyOrder(
            $_REQUEST['order_id'],
            false,
            true,
            false,
            true
        );
        fn_order_notification($order_info, array(), fn_get_notification_rules($_REQUEST));

        if (!empty($_REQUEST['prolongate_data']) && is_array($_REQUEST['prolongate_data'])) {
            foreach ($_REQUEST['prolongate_data'] as $ekey => $v) {
                $newttl = fn_parse_date($v, true);
                db_query('UPDATE ?:product_file_ekeys SET ?u WHERE ekey = ?s', array('ttl' => $newttl), $ekey);
            }
        }

        if (!empty($_REQUEST['activate_files'])) {
            $edp_data = fn_generate_ekeys_for_edp(array(), $order_info, $_REQUEST['activate_files']);
        }

        // Update file downloads section
        if (!empty($_REQUEST['edp_downloads'])) {
            foreach ($_REQUEST['edp_downloads'] as $ekey => $v) {
                foreach ($v as $file_id => $downloads) {
                    $max_downloads = db_get_field("SELECT max_downloads FROM ?:product_files WHERE file_id = ?i", $file_id);
                    if (!empty($max_downloads)) {
                        db_query('UPDATE ?:product_file_ekeys SET ?u WHERE ekey = ?s', array('downloads' => $max_downloads - $downloads), $ekey);
                    }
                }
            }
        }

        /**
         * Pièces jointes
         */
        $attachmentData = $_POST['attachment_data'];

        if (\is_array($attachmentData) === true && \count($attachmentData) > 0) {
            foreach ($attachmentData as $attachmentId => $newAttachment) {
                /** @var OrderAttachment $orderAttachment */
                $orderAttachment = $attachmentsService->get(
                    (int) $_POST['order_id'],
                    $attachmentId,
                    SecurityUser::fromUser($user)
                );
                if (($orderAttachment->getName() !== $newAttachment['name'] && $newAttachment['name'] !== '')
                    || $orderAttachment->getType()->getValue() !== $newAttachment['type']
                ) {
                    if ($newAttachment['name'] === '') {
                        unset($newAttachment['name']);
                    }

                    $attachmentsService->update(
                        $newAttachment,
                        (int) $_POST['order_id'],
                        $attachmentId,
                        SecurityUser::fromUser($user)
                    );
                }
            }
        }

        // Add new attachments file
        if ((\is_array($_FILES['file_attachment']) === true
            && $_FILES['file_attachment']['name']['new_attachment'] !== '')
            || $_POST['file_attachment']['new_attachment'] !== ''
        ) {
            foreach ($_FILES as $key => $file) {
                if ($key === 'file_attachment') {
                    $payload = [
                        'name' => $_POST['attachment_new_name'],
                        'type' => $_POST['attachment_new_type'],
                    ];

                    if ($_POST['type_attachment']['new_attachment'] === 'url') {
                        $payload['url'] = $_POST['file_attachment']['new_attachment'];
                    } else {
                        $payload['file'] = new UploadedFile(
                            $file['tmp_name']['new_attachment'],
                            $file['name']['new_attachment'],
                            $file['type']['new_attachment'],
                            $file['size']['new_attachment'],
                            $file['error']['new_attachment']
                        );
                    }

                    try {
                        $attachmentsService->create(
                            $payload,
                            (int)$_POST['order_id'],
                            SecurityUser::fromUser($user)
                        );
                    } catch (OrderNotFound $exception) {
                        fn_set_notification(
                            'E',
                            __('error'),
                            __('order_attachments.exception.order_not_found')
                        );
                    } catch (BadRequestHttpException $exception) {
                        fn_set_notification('E', __('error'), __('w_invalid_parameters'));
                    } catch (UploadFailException $exception) {
                        fn_set_notification('E', __('error'), __('order_attachments.exception.upload_fail'));
                    } catch (Exception $exception) {
                        fn_set_notification('E', __('error'), $exception->getMessage());
                    }
                }
            }
        }

        if ($order_info['status'] !== OrderStatus::COMPLETED) {
            OrderStatus::automaticUpdate($_REQUEST['order_id']);
        }

        /**
         * Avancement du workflow
         */
        $order = $container->get('marketplace.order.order_service')->getOrder($_REQUEST['order_id']);

        $hasNewInvoiceNumber = isset($update_order['w_invoice_number']) && strlen($update_order['w_invoice_number']) > 0;
        $hasNewDoNotCreateInvoiceFlag = isset($update_order['do_not_create_invoice']) && (bool) $update_order['do_not_create_invoice'];

        // La vérification de la présence du numéro de facture est temporaire,
        // tant que le workflow n'a pas réellement la main : le feature flag
        // permettant de passer outre les contraintes des actions, permettrait
        // dans ce cas précis d'écrire un numéro de facture même s'il y en a
        // déjà un.
        if ($hasNewInvoiceNumber) {
            $provideInvoiceNumber = $container->get('marketplace.order.action.provide_invoice_number');
            if ($provideInvoiceNumber->isAllowed($order)) {
                $provideInvoiceNumber->execute($order, (string) $update_order['w_invoice_number']);
            }
        } else if ($hasNewDoNotCreateInvoiceFlag) {
            $declareInvoiceNumberGeneratedElsewhere = $container->get('marketplace.order.action.declare_invoice_number_generated_elsewhere');
            if ($declareInvoiceNumberGeneratedElsewhere->isAllowed($order)) {
                $declareInvoiceNumberGeneratedElsewhere->execute($order);
            }
        }

        /**
         * Avancement du workflow
         */
        if ($order->areAllShipmentsSent()) {
            $markAsShipped = $container->get('marketplace.order.action.mark_as_shipped');
            if ($markAsShipped->isAllowed($order)) {
                $markAsShipped->execute($order);
            }
        }

        /**
         * Ajustement des prix
         */
        if (isset($_POST['adjustedPrice']) && is_array($_POST['adjustedPrice']) && isset($_POST['order_id'])) {
            $order = $orderService->getOrder((int) $_POST['order_id']);

            foreach ($_POST['adjustedPrice'] as $itemId => $newPrice) {
                try {
                    $container->get('marketplace.order.adjustment_service')->adjust($order, $itemId, $newPrice, $user);
                } catch (AdjustmentException $e) {
                    fn_set_notification('W', __('warning'), $e->getMessage());
                }
            }
        }

        $suffix = ".details?order_id=$_REQUEST[order_id]";
    }

    $selectedOrderIds = [];
    $nbSelectedOrderIds = 0;
    if (\array_key_exists('selected_order_ids', $_REQUEST) === true) {
        $selectedOrderIds = \explode(',', $_REQUEST['selected_order_ids']);
        $nbSelectedOrderIds = \count($selectedOrderIds);
    }

    if ($nbSelectedOrderIds > 0) {
        if ($mode == 'bulk_print') {

            return fn_print_order_invoices($selectedOrderIds, Registry::get('runtime.dispatch_extra') == 'pdf');
        }

        if ($mode == 'packing_slip') {

            fn_print_order_packing_slips($selectedOrderIds, Registry::get('runtime.dispatch_extra') == 'pdf');
            exit;
        }

        if ($mode == 'remove_cc_info') {

            fn_set_progress('parts', $nbSelectedOrderIds);

            foreach ($selectedOrderIds as $v) {
                $payment_info = db_get_field(
                    "SELECT data FROM ?:order_data WHERE order_id = ?i AND type = ?s",
                    $v,
                    OrderDataType::PAYMENT_INFO()->getValue()
                );
                fn_cleanup_payment_info($v, $payment_info);
            }

            fn_set_notification('N', __('notice'), __('done'));

            if ($nbSelectedOrderIds !== 1) {
                exit;
            }

            $o_id = array_pop($selectedOrderIds);
            $suffix = ".details?order_id=$o_id";
        }

        if ($mode == 'export_range') {
            if (\array_key_exists('export_ranges', $_SESSION) === true
                && \count($_SESSION['export_ranges']) === 0
            ) {
                $_SESSION['export_ranges'] = array();
            }


            if (\array_key_exists('export_ranges', $_SESSION) === true
                && \array_key_exists('orders', $_SESSION['export_ranges']) === true
                && \count($_SESSION['export_ranges']['orders']) === 0
            ) {
                $_SESSION['export_ranges']['orders'] = array('pattern_id' => 'orders');
            }

            $_SESSION['export_ranges']['orders']['data'] = array('order_id' => $selectedOrderIds);

            unset($_REQUEST['redirect_url']);

            return array(CONTROLLER_STATUS_REDIRECT, "exim.export?section=orders&pattern_id=" . $_SESSION['export_ranges']['orders']['pattern_id']);
        }

        if ($mode == 'products_range') {
            unset($_REQUEST['redirect_url'], $_REQUEST['page']); // force redirection

            return array(CONTROLLER_STATUS_REDIRECT, "products.manage?order_ids=" . implode(',', $selectedOrderIds));
        }
    }

    if ($mode == 'google') {
        $google_request_sent = false;
        $order_info = $orderService->overrideLegacyOrder(
            $_REQUEST['order_id'],
            false,
            true,
            false,
            true
        );
        $processor_data = fn_get_payment_method_data($order_info['payment_id']);
        $base_url = "https://" . (($processor_data['processor_params']['test'] == 'N') ? 'checkout.google.com' : 'sandbox.google.com/checkout') . '/cws/v2/Merchant/' . $processor_data['processor_params']['merchant_id'];
        $request_url = $base_url . '/request';
        $schema_url = 'http://checkout.google.com/schema/2';
        $google_data = !empty($_REQUEST['google_data']) ? $_REQUEST['google_data'] : array();

        $post = array();
        // XML request to mark order delivered
        if ($action == 'deliver') {
            $ship_info = reset($order_info['shipping']);

            $post = array();
            $post[] = "<deliver-order xmlns='" . $schema_url . "' google-order-number='" . $order_info['payment_info']['transaction_id'] . "'>";
            $post[] = '<tracking-data>';
            $post[] = '<carrier>' . (!empty($ship_info['carrier']) ? $ship_info['carrier'] : 'Other') . '</carrier>';
            $post[] = '<tracking-number>' . (!empty($ship_info['tracking_number']) ? $ship_info['tracking_number'] : '') . '</tracking-number>';
            $post[] = '</tracking-data>';
            $post[] = '<send-email>false</send-email>';
            $post[] = '</deliver-order>';

        // XML request to cancel the order
        } elseif ($action == 'add_tracking_data') {
            //$ship_info = reset($order_info['shipping']);

            foreach ($order_info['shipping'] as $ship_info) {
                if (!empty($ship_info['carrier']) && !empty($ship_info['tracking_number'])) {
                    $post = array();
                    $post[] = "<add-tracking-data xmlns='" . $schema_url . "' google-order-number='" . $order_info['payment_info']['transaction_id'] . "'>";
                    $post[] = '<tracking-data>';
                    $post[] = '<carrier>' . $ship_info['carrier'] . '</carrier>';
                    $post[] = '<tracking-number>' . $ship_info['tracking_number'] . '</tracking-number>';
                    $post[] = '</tracking-data>';
                    $post[] = '</add-tracking-data>';
                    fn_google_send_order_command($post, $processor_data, $request_url, $action, $_REQUEST['order_id']);
                }
            }
            $google_request_sent = true;

        // XML request to send a message to the customer
        } elseif ($action == 'send_message') {
            $post[] = "<send-buyer-message xmlns='" . $schema_url . "' google-order-number='" . $order_info['payment_info']['transaction_id'] . "'>";
            $post[] = '<message>' . $google_data['message'] . '</message>';
            $post[] = '<send-email>true</send-email>';
            $post[] = '</send-buyer-message>';

        // XML request to refund the order
        } elseif ($action == 'charge') {
            $post[] = "<charge-order xmlns='" . $schema_url . "' google-order-number='" . $order_info['payment_info']['transaction_id'] . "'>";
            $post[] = '<amount currency="' . $processor_data['processor_params']['currency'] . '">' . $google_data['charge_amount'] . '</amount>';
            $post[] = '</charge-order>';

        // XML request to refund the order
        } elseif ($action == REFUND_KEY) {
            $post[] = "<refund-order xmlns='" . $schema_url . "' google-order-number='" . $order_info['payment_info']['transaction_id'] . "'>";
            $post[] = '<amount currency="' . $processor_data['processor_params']['currency'] . '">' . $google_data['refund_amount'] . '</amount>';
            $post[] = '<reason>' . $google_data['refund_reason'] . '</reason>';
            $post[] = '<comment>' . $google_data['refund_comment'] . '</comment>';
            $post[] = '</refund-order>';

        // XML request to cancel the order
        } elseif ($action == 'cancel') {
            $post[] = "<cancel-order xmlns='" . $schema_url . "' google-order-number='" . $order_info['payment_info']['transaction_id'] . "'>";
            $post[] = '<reason>' . $google_data['cancel_reason'] . '</reason>';
            $post[] = '<comment>' . $google_data['cancel_comment'] . '</comment>';
            $post[] = '</cancel-order>';

        // XML request to archive the order
        } elseif ($action == 'archive') {
            $post[] = "<archive-order xmlns='" . $schema_url . "' google-order-number='" . $order_info['payment_info']['transaction_id'] . "' />";

        }

        if (!$google_request_sent) {
            fn_google_send_order_command($post, $processor_data, $request_url, $action, $_REQUEST['order_id']);
        }

        $suffix = '.details?order_id=' . $_REQUEST['order_id'];
    }

    if ($mode === 'partial_refund') {
        $itemIds = $_POST['item_ids'] ?? [];
        $productsDetails = $_POST['products'] ?? [];
        $items = [];

        $order = $orderService->getOrder((int) $_GET['order_id']);

        $invoicingDisabled = $companyService->isInvoicingDisabled($order->getCompanyId());

        //if do_not_create_credit is checked or invoicing is disabled for the company we can't have a creditNoteReference
        if ((\array_key_exists('do_not_create_credit', $_POST) === true
            && $_POST['do_not_create_credit'] === 'on')
            || $invoicingDisabled === true
        ) {
            $creditNoteReference = null;
        } else {
            $creditNoteReference = $_POST['credit_note_number'];
        }

        if (\is_array($itemIds)) {
            foreach ($productsDetails as $itemId => $product) {
                if ((int) $product['amount'] > 0 && \in_array($itemId, $itemIds)) {
                    $items[] = ['itemId' => $itemId, 'quantity' => \intval($product['amount'])];
                }
            }

            try {
                $refund = $refundService->createPartialRefund(
                    $order,
                    null,
                    $items,
                    $_REQUEST['shipping'] === 'on' ? true : false,
                    $creditNoteReference
                );
            } catch (\Exception $exception) {
                fn_set_notification('E', __('error'), $exception->getMessage());

                return array(CONTROLLER_STATUS_OK, 'orders.partial_refund?order_id=' . $_GET['order_id']);
            }
        }

        try {
            $refund = $refundService->executeRefund($refund, $order);
            $refundNotificationService->notifyRefund($refund, $order);
        } catch (\Exception $exception) {
            fn_set_notification('E', __('error'), $exception->getMessage());
        }

        $suffix = '.partial_refund_confirm?order_id=' . $_GET['order_id'] . '&refund_id=' . $refund->getId();
    }

    if ($mode === 'refund_mark_as_paid') {
        try {
            $refund = $refundService->get((int) $_POST['refund_id']);
            $order = $orderService->getOrder($refund->getOrderId());

            if (\in_array($refund->getStatus(), [RefundStatus::CREATED(), RefundStatus::FAILED()])) {
                $refundService->markRefundAsPaid($refund);
                $refundNotificationService->notifyRefund($refund, $order);
            }
        } catch (RefundNotFoundException $exception) {
            fn_set_notification('E', __('error'), __('refund_does_not_exist'));
        } catch (TransactionNotFound $exception) {
            fn_set_notification('E', __('error'), __('refund.error.original_transaction_not_found'));
        }

        $suffix = '.details?order_id='. $_POST['order_id'] .'&selected_section=returns_and_refunds';
    }

    if ($mode === 'delete_attachment') {
        $attachmentsService->delete(
            (int) $_POST['order_id'],
            (string) $_POST['attachment_id'],
            SecurityUser::fromUser($user)
        );

        $suffix = '.details?order_id=' . $_POST['order_id'] . '&selected_section=order_attachments';
    }

    return array(CONTROLLER_STATUS_OK, "orders" . $suffix);
}

if (Registry::get('runtime.company_id')) {
    Registry::get('view')->assign('w_restrain_vendor', true);
}

if ('update_status' == $mode) {
    if (Registry::get('runtime.company_id')) {
        $schema = fn_get_permissions_schema('change_status');
        $old_status = db_get_field("SELECT status FROM ?:orders WHERE order_id = ?i", $_REQUEST['id']);
        if (!isset($schema['orders'][$old_status][$_REQUEST['status']])) {
            fn_set_notification('E', __('error'), __('w_change_status_order_not_allowed'));
            return array(CONTROLLER_STATUS_REDIRECT, $_REQUEST['return_url']);
        }

        $order_statuses = fn_get_statuses(STATUSES_ORDER, array(), true, false);
        $status_params = $order_statuses[$_REQUEST['status']]['params'];

        $_GET['notify_user'] = $_REQUEST['notify_user'] = (!empty($status_params['notify']) && $status_params['notify'] == 'Y' ? true : false);
        $_GET['notify_department'] = $_REQUEST['notify_department'] = (!empty($status_params['notify_department']) && $status_params['notify_department'] == 'Y' ? true : false);
        $_GET['notify_vendor'] = $_REQUEST['notify_vendor'] = (!empty($status_params['notify_vendor']) && $status_params['notify_vendor'] == 'Y' ? true : false);
    }
}

if ('manage' == $mode && !$container->getParameter('feature.activate_workflow_translation')) {
    $statuses = Order::getStatuses(Registry::get('runtime.company_id'));

    if (!empty($_REQUEST['status'])) {
        // Filter out statuses which do not exist
        $validStatuses = array_keys($statuses);
        if (!Registry::get('runtime.company_id')) {
            // Admins can see 'N' orders if they want
            $validStatuses[] = \Wizacha\Marketplace\Order\OrderStatus::INCOMPLETED;
        }

        $_REQUEST['status'] = array_intersect((array) $_REQUEST['status'], $validStatuses);
    }
}

$params = $_REQUEST;
$subscriptionService = container()->get(SubscriptionService::class);

if ($mode == 'print_invoice') {
    if (!empty($_REQUEST['order_id'])) {
        return fn_print_order_invoices($_REQUEST['order_id'], !empty($_REQUEST['format']) && $_REQUEST['format'] == 'pdf');
    }
    exit;

} elseif ($mode == 'print_credit_note') {
    if (!empty($_REQUEST['order_id'])) {
        return fn_print_order_credit_note(
            (int) $_GET['order_id'],
            (int) $_GET['refund_id'],
            ($_GET['format'] ?? null) === 'pdf'
        );
    }
    exit;

} elseif ($mode == 'print_packing_slip') {
    if (!empty($_REQUEST['order_id'])) {
        fn_print_order_packing_slips($_REQUEST['order_id'], !empty($_REQUEST['format']) && $_REQUEST['format'] == 'pdf');
    }
    exit;

} elseif ($mode == 'details') {
    $_REQUEST['order_id'] = empty($_REQUEST['order_id']) ? 0 : $_REQUEST['order_id'];

    $orderReturnService = $container->get('marketplace.order_return.service');
    $creditNoteService = $container->get('marketplace.order.credit_note.credit_note_service');
    $subscriptionRepository = container()->get(SubscriptionRepository::class);
    $userService = $container->get('marketplace.user.user_service');
    $user = $userService->get($auth['user_id']);

    $order_info = $orderService->overrideLegacyOrder(
        $_REQUEST['order_id'],
        false,
        true,
        true,
        true
    );
    fn_check_first_order($order_info);

    if (empty($order_info)) {
        return [CONTROLLER_STATUS_NO_PAGE];
    }

    if (isset($order_info['is_parent_order']) && $order_info['is_parent_order'] == 'Y') {
        $children_order_ids = db_get_fields('SELECT order_id FROM ?:orders WHERE parent_order_id = ?i', $order_info['order_id']);
        return [CONTROLLER_STATUS_REDIRECT, 'orders.manage?order_id=' . implode(',', $children_order_ids)];
    }

    if (isset($order_info['need_shipping']) && $order_info['need_shipping']) {
        $shippings = fn_get_available_shippings($order_info['company_id'] ?? null);
        Registry::get('view')->assign('shippings', $shippings);
    }

    Registry::get('view')->assign('order_extras', \json_decode($order_info['extra'], true));
    Registry::get('view')->assign(
        'canDeleteExtra',
        (Registry::get('runtime.company_id') === 0 ) ? 'Y' : 'N'
    );

    Registry::set('navigation.tabs', [
        'general' => [
            'title' => __('general'),
            'js' => true
        ],
        'addons' => [
            'title' => __('addons'),
            'js' => true
        ],
        'order_attachments' => [
            'title' => __('order_attachments.attachments'),
            'js' => true,
        ],
    ]);

    if ($order_info['status'] !== OrderStatus::COMPLETED) {
        OrderStatus::automaticUpdate((int) $order_info['order_id']);
    }
    $order = $orderService->getOrder((int) $order_info['order_id']);

    // Cette page est déjà securisé
    $orderAttachments = $attachmentsService->list(
        (int) $order_info['order_id'],
        []
    );

    // add try catch to get files
    $resultOrderAttachment = [];

    if (\count($orderAttachments) > 0) {
        $orderAttachmentsStorageService = container()->get('Wizacha\Storage\OrderAttachmentsStorageService');
        foreach ($orderAttachments as $orderAttachment) {
            try {
                $resultOrderAttachment[] = [
                    'id' => $orderAttachment->getId(),
                    'orderId' => $orderAttachment->getOrderId(),
                    'name' => $orderAttachment->getName(),
                    'createdAt' => $orderAttachment->getCreatedAt(),
                    'updatedAt' => $orderAttachment->getUpdatedAt(),
                    'url' => $orderAttachmentsStorageService
                        ->getUrl(
                            $orderAttachment->getOrderId() . '/'
                            . $orderAttachment->getId() . '/'
                            . $orderAttachment->getFilename()
                        ),
                    'urlDownload' => $orderAttachmentsStorageService
                        ->getUrl(
                            $orderAttachment->getOrderId() . '/'
                            . $orderAttachment->getId() . '/'
                            . $orderAttachment->getFilename(),
                            '',
                            'attachment'
                        ),
                    'type' => $orderAttachment->getType(),
                    'createdBy' => $userService->get($orderAttachment->getCreatedBy())->getEmail(),
                    'updatedBy' => $userService->get($orderAttachment->getUpdatedBy())->getEmail(),
                ];
            } catch (FileNotFoundException $e) {
                fn_set_notification('E', __('error'), __('storage_file_not_exist', ['file_name' => $orderAttachment->getName()]));
                $container->get('logger')->error(
                    'File does not exist',
                    ['message' => $e->getMessage()]
                );
                continue;
            }
        }
    }

    Registry::get('view')->assign('order_attachments', $resultOrderAttachment);

    Registry::get('view')->assign('isAutomaticBillingNumberEnabled', $container->getParameter('feature.activate_billing_number_auto_generation') && $order->getCompany()->hasAutomaticBillingNumber());
    Registry::get('view')->assign('invoiceNumber', $order_info['w_invoice_number']);
    Registry::get('view')->assign('take_surcharge_from_vendor', fn_take_payment_surcharge_from_vendor());

    $invoicingDisabled = $companyService->isInvoicingDisabled($order_info['company_id']);
    Registry::get('view')->assign('invoicingDisabled', $invoicingDisabled);

    $google_info = db_get_field(
        "SELECT data FROM ?:order_data WHERE order_id = ?i AND type = ?s",
        $_REQUEST['order_id'],
        OrderDataType::GOOGLE_CHECKOUT_INFO()->getValue()
    );

    if (!empty($google_info)) {
        Registry::set('navigation.tabs.google', array (
            'title' => __('google_info'),
            'js' => true
        ));

        $google_info = unserialize($google_info);
        $google_actions = [];

        if (($google_info['financial_state'] == "CHARGED" || $google_info['financial_state'] == "REFUNDED") && $google_info['refunded_amount'] != $order_info['total']) {
            $google_actions[REFUND_KEY] = true;
        }

        if (((!empty($google_info['refunded_amount']) && $google_info['refunded_amount'] == $google_info['charged_amount']) || (!empty($google_info['chargeback_amount']) && $google_info['chargeback_amount'] == $google_info['charged_amount'])) && !($google_info['financial_state'] == "CANCELLED" || $google_info['financial_state'] == "CANCELLED_BY_GOOGLE")) {
            $google_actions['cancel'] = true;
        }

        if ($google_info['charged_amount'] < $order_info['total']) {
            $google_actions['charge'] = true;
        }

        $_SESSION['google_info'] = $google_info;

        Registry::get('view')->assign('google_actions', $google_actions);
        Registry::get('view')->assign('google_info', $google_info);
    }

    foreach ($order_info['products'] as $v) {
        if (!empty($v['extra']['is_edp']) && $v['extra']['is_edp'] == 'Y') {
            Registry::set('navigation.tabs.downloads', array (
                'title' => __('downloads'),
                'js' => true
            ));
            Registry::get('view')->assign('downloads_exist', true);
            break;
        }
    }

    $transactions = $transactionService->findByOrderId((int) $params['order_id']);
    if (count($transactions) > 0) {
        Registry::set('navigation.tabs.transactions', array (
            'title' => __('transactions.table_header.transactions'),
            'js' => true
        ));

        $transactionTotal = $order->getBalanceTotalOnlyForRefundDetail();

        $transactions =  array_filter(
            $transactions,
            function (Transaction $transaction): bool {
                return false === in_array(
                    $transaction->getType(),
                    TransactionType::getDispatchFundsTransferTypes()
                );
            }
        );

        $transactions = array_map(
            static function (Transaction $transaction) use ($refundService): array {

                $transactionArray = $transaction->expose();
                $transactionRefundId = $transaction->getRefundId();

                if ($transactionRefundId !== null) {
                    $transactionArray['isRefundedAfterWithdrawalPeriod'] =
                        $refundService
                            ->get($transactionRefundId)
                            ->isRefundedAfterWithdrawalPeriod();
                }

                return  $transactionArray;
            }, $transactions);

        $transfersVendors = $transactionService->findByOrderId((int) $params['order_id'], TransactionType::DISPATCH_FUNDS_TRANSFER_VENDOR());
        $transfersCommission = $transactionService->findByOrderId((int) $params['order_id'], TransactionType::DISPATCH_FUNDS_TRANSFER_COMMISSION());

        $transfers = array_map(function (Transaction $transaction): array {
            return  $transaction->expose();
        }, array_merge($transfersVendors, $transfersCommission));

        Registry::get('view')->assign('transactionTotal', $transactionTotal->getConvertedAmount());
        Registry::get('view')->assign('transactions', $transactions);
        Registry::get('view')->assign('transfers', $transfers);
    }

    if (!empty($order_info['promotions'])) {
        Registry::set('navigation.tabs.promotions', array (
            'title' => __('promotions'),
            'js' => true
        ));
    }

    $order_info['marketplace_discount_coupon'] = current(array_filter($order_info['promotions'], function (array $promotion) use($order_info): bool {
        return $promotion['id'] === $order_info['marketplace_discount_id'];
    }))['name'];

    [$shipments] = fn_get_shipments_info(array('order_id' => $params['order_id'], 'advanced_info' => true));
    $use_shipments = !fn_one_full_shipped($shipments);

    // Check for the shipment access
    // If current edition is FREE, we still need to check shipments accessibility (need to display promotion link)
    if (Settings::instance()->getValue('use_shipments', '', $order_info['company_id']) == 'Y') {
        $use_shipments = true;
    } else {
        Registry::get('view')->assign('shipments', $shipments);
    }

    $isPaidByCard = $order->getPayment()->isOfType(PaymentType::CREDIT_CARD()) === true;

    $customer = $userService->get($order_info['user_id']);

    $userProfile = [
        'birthday' => $customer->getBirthday(),
        'registeredOn' => $customer->getTimestamp(),
    ];

    //get image pair
    foreach ($order_info['products'] as &$product) {
        if (\count($product['product_options']) > 0) {
            $productOption = $product['product_options'];
            $objectId = $product['product_id'] . "_" . $productOption[0]['option_id'] . "_" . $productOption[0]['value'];
            $product['main_pair'] = fn_get_image_pairs($objectId, 'declinations', 'M', true, true, $lang_code);
        } else {
            $product['main_pair'] = fn_get_image_pairs($product['product_id'], 'product', 'M', true, true, $lang_code);
        }

        if (\count($product['main_pair']) === 0) {
            $product['main_pair'] = fn_get_image_pairs($product['product_id'], 'product', 'M', true, true, $lang_code);
        }
    }

    Registry::get('view')->assign('userProfile', $userProfile);
    Registry::get('view')->assign('use_shipments', $use_shipments);
    Registry::get('view')->assign('order_info', $order_info);
    Registry::get('view')->assign('order_amounts', $order->getOrderAmounts());
    Registry::get('view')->assign('isPaidByCard', $isPaidByCard);
    Registry::get('view')->assign('showParentOrderId', $user->isMarketplaceAdministrator());

    // Chargement des actions du workflow
    /** @var MarkAsPaid */
    $markAsPaid = $container->get('marketplace.order.action.mark_as_paid');
    /** @var Accept */
    $accept = $container->get('marketplace.order.action.accept');
    /** @var Refuse */
    $refuse = $container->get('marketplace.order.action.refuse');
    /** @var DeclareInvoiceNumberGeneratedElsewhere */
    $declareInvoiceNumberGeneratedElsewhere = $container->get('marketplace.order.action.declare_invoice_number_generated_elsewhere');
    /** @var ProvideInvoiceNumber */
    $provideInvoiceNumber = $container->get('marketplace.order.action.provide_invoice_number');
    /** @var MarkAsShipped */
    $markAsShipped = $container->get('marketplace.order.action.mark_as_shipped');
    /** @var Cancel */
    $cancel = $container->get('marketplace.order.action.cancel');
    /** @var bool */
    $workflowTranslation = $container->getParameter('feature.activate_workflow_translation');

    Registry::get('view')->assign('show_as_invoice', $order->shouldShowAsInvoice());
    Registry::get('view')->assign('order', $order);
    Registry::get('view')->assign('status_settings', fn_get_status_params($order_info['status']));
    Registry::get('view')->assign('workflowTranslation', $workflowTranslation);

    $isAdmin = $user->getUserType() === UserType::ADMIN && !Registry::get('runtime.company_id');

    $hasMissingShipment = (
        $order->hasDelivery()
        && 0 === \count($order_info['shipment_ids'])
    );
    $canPrepareOrder = (
        $declareInvoiceNumberGeneratedElsewhere->isAllowed($order)
        || $provideInvoiceNumber->isAllowed($order)
        || $markAsShipped->isAllowed($order)
        || $hasMissingShipment
    );

    // Génération des droits d'action sur le workflow
    Registry::get('view')->assign('canAccept', $accept->isAllowed($order));
    Registry::get('view')->assign('canRefuse', $refuse->isAllowed($order));
    Registry::get('view')->assign('canMarkAsPaid', $markAsPaid->isAllowed($order) && $isAdmin);
    Registry::get('view')->assign('canPrepareOrder', $canPrepareOrder);

    Registry::get('view')->assign('canRefund', $refundService->isManuallyRefundable($order, $isAdmin));

    $canCancelOrder = $cancel->isAllowed($order) && $isAdmin;

    if ($order->getStatus()->equals(MarketplaceOrderStatus::PROCESSED())) {
        $canCancelOrder = false;
    }

    Registry::get('view')->assign('canCancelOrder', $canCancelOrder);

    // Génération d'un droit ne dépendant pas d'une action simple,
    // car pour terminer une commande, il faut pouvoir faire plusieurs
    // actions différentes à la suite.
    // Le controller de cette action spécifique se trouve d'ailleurs à
    // la fin de ce fichier.
    $unfolding = $container
        ->get('marketplace.order.workflow_service')
        ->getWorkflowUnfoldingFor($order);

    $canMarkAsCompleted = $order->getWorkflowProgress()->isProcessing() &&
        $unfolding->hasCompleted(ModuleName::ORDER_PREPARATION()) &&
        $isAdmin;

    // Le dispatch des fonds est "failed" seulement si il y a une action de dispatch echoué et qu'il n'y a
    // aucune action de dispatch réussie. C'est pour ça qu'on a un break pour sortir de la boucle dans le cas
    // du succes mais pas dans le cas de l'echec.
    $hasDispatchFundsFailed = false;
    foreach (container()->get('marketplace.order.tracer')->list($order->getId()) as $trace) {
        if ($trace->getAction() === "DispatchFundsFailed") {
            $hasDispatchFundsFailed = true;
        }
        if ($trace->getAction() === 'DispatchFundsSucceeded') {
            $hasDispatchFundsFailed = false;
            break;
        }
    }
    if ($hasDispatchFundsFailed) {
        $canMarkAsCompleted = $canMarkAsFinished = true;
    }

    Registry::get('view')->assign('canMarkAsCompleted', $canMarkAsCompleted);
    Registry::get('view')->assign('canMarkAsFinished', $canMarkAsFinished);

    // Delete order_id from new_orders table
    db_query("DELETE FROM ?:new_orders WHERE order_id = ?i AND user_id = ?i", $_REQUEST['order_id'], $auth['user_id']);

    // Check if customer's email is changed
    if (!empty($order_info['user_id'])) {
        $current_email = db_get_field("SELECT email FROM ?:users WHERE user_id = ?i", $order_info['user_id']);
        if (!empty($current_email) && $current_email != $order_info['email']) {
            Registry::get('view')->assign('email_changed', true);
        }
    }

    // Type de mode de livraison
    $currentShippingId = $order_info['shipping'][0]['shipping_id'] ?? null;
    Registry::get('view')->assign('w_shipping_required_code', is_null($currentShippingId) ? false : Shipping::requiredCode($currentShippingId));
    Registry::get('view')->assign('w_shipping_is_hand_delivery', is_null($currentShippingId) ? false : Shipping::isHandDelivery($currentShippingId));
    Registry::get('view')->assign('w_shipping_is_mondialrelay', is_null($currentShippingId) ? false : Shipping::isMondialRelay($currentShippingId));

    $shippingType = is_null($currentShippingId) ? null : ($order_info['shipping'][0]['w_delivery_type'] ?? null);
    Registry::get('view')->assign('w_shipping_is_chronopost', is_null($shippingType) ? false : Shipping::isChronopost($shippingType));

    if ($workflowTranslation) {
        Registry::get('view')->assign('workflow_status', $order->deduceWorkflowTranslationKey());
    }

    Registry::get('view')->assign('show_order_adjustments', false);
    if ($container->getParameter('feature.order_adjustment')) {
        $adjustments = $container->get('marketplace.order.adjustment_service')->findByOrderId((int) $order_info['order_id']);

        if (count($adjustments) > 0) {
            $products = $order_info['products'];
            Registry::get('view')->assign('show_order_adjustments', true);
            Registry::get('view')->assign('order_adjustments', array_map(function (OrderAdjustment $adjustment) use ($products): array {
                return [
                    'itemId' => $adjustment->getItemId(),
                    'createdAt' => $adjustment->getCreatedAt()->format(DateTime::RFC3339),
                    'product' => isset($products[$adjustment->getItemId()]) ? $products[$adjustment->getItemId()]['product'] : null,
                    'oldPrice' => $adjustment->getOldPriceWithoutTaxes(),
                    'newPrice' => $adjustment->getNewPriceWithoutTaxes(),
                    'oldTotal' => $adjustment->getOldTotalIncludingTaxes(),
                    'newTotal' => $adjustment->getNewTotalIncludingTaxes(),
                    'user' => $adjustment->getUser() ? $adjustment->getUser()->getFullName() : null,
                ];
            }, $adjustments));

            Registry::set('navigation.tabs.adjustments', [
                'title' => __('Adjustments'),
                'js' => true
            ]);
        }
    }

    $refunds = $refundService->getByOrderId($order->getId());
    $orderReturns = $orderReturnService->getReturnsByOrder($order);

    // Accounting Documents - only delivered orders has emitted invoices and/or credit notes
    if (true === $order->hasAccountingDocument()) {
        Registry::set('navigation.tabs.accounting_documents', array (
            'title' => __('accounting_documents'),
            'js' => true
        ));

        $currencySign = $container->getParameter('currency.sign');
        Registry::get('view')->assign('currency_sign', $currencySign);

        if (true === $order->shouldShowAsInvoice()) {
            $documents[] = [
                'type' => 'invoice',
                'order_id' => $order->getId(),
                'document_id' => $order_info['w_invoice_number'],
                'date' => $order->getInvoiceDate() ?: $order->getIssueDate(),
                'amount' => $order->getTotal(),
            ];
        }

        foreach ($refunds as $refund) {
            if ($refund->getCreditNoteReference() !== null) {
                $documents[] = [
                    'type' => 'credit_note',
                    'order_id' => $order->getId(),
                    'refund_id' => $refund->getId(),
                    'document_id' => $refund->getCreditNoteReference() ?? $refund->getId(),
                    'date' => $refund->getCreatedAt(),
                    'amount' => $refund->getAmount(),
                ];
            }
        }

        Registry::get('view')->assign('accounting_documents', $documents);
    }

    Registry::get('view')->assign('feature_order_adjustment', $container->getParameter('feature.order_adjustment'));
    Registry::get('view')->assign('hide_email',
        true === \filter_var($container->getParameter('feature.hide_client_email_from_vendor'), FILTER_VALIDATE_BOOLEAN)
        && false === $isAdmin
    );

    $refundStatusValue = $order->getRefundStatus()->getValue();

    if (RefundStatus::PAID()->getValue() === (int) $order_info['doctrine_order_refund_status']) {
        if ($transactionTotal->isZero() === true) {
            // Si le solde égale à zéro => Remboursée intégralement (2)
            $refundStatusValue = OrderRefundStatus::COMPLETE_REFUND()->getValue();
        } else {
            // Si le solde de la commande supérieur a zéro => Remboursée partiellement (3)
            $refundStatusValue = OrderRefundStatus::PARTIAL_REFUND()->getValue();
        }
    }

    Registry::get('view')->assign('refundStatusValue', $refundStatusValue);
    Registry::get('view')->assign('showFlagRemStatus', $showFlagRemStatus);
    Registry::get('view')->assign('order_refund_status_created',(string) RefundStatus::CREATED()->getValue());
    Registry::get('view')->assign('order_refund_status_failed', (string) RefundStatus::FAILED()->getValue());
    Registry::get('view')->assign('order_refund_status_paid', (string) RefundStatus::PAID()->getValue());

    // Returns & Refunds tab
    if (count($refunds) > 0 || count($orderReturns) > 0) {
        Registry::set('navigation.tabs.returns_and_refunds', array (
            'title' => __('returns_and_refunds'),
            'js' => true
        ));

        $currencyCode = $container->getParameter('currency.code');
        $priceFormatter = new DetailedPriceFormatter($currencyCode);
        $statuses = fn_get_statuses(STATUSES_RETURN);
        $orderReturnsData = array_map(
            function (OrderReturn $orderReturn) use ($order, $priceFormatter, $statuses): array {
                return [
                    'id' => $orderReturn->getId(),
                    'status' => $statuses[$orderReturn->getStatus()]['description'],
                    'date' => $orderReturn->getCreatedAt(),
                    'amount' => $priceFormatter->formatFloat($orderReturn->getItemsTotal()->getConvertedAmount()),
                ];
            },
            $orderReturns
        );

        $displayableRefunds = array_filter($refunds, function (Refund $refund): bool {
            return $refund->isLegacy() === false;
        });

        Registry::get('view')->assign('returns_and_refunds', true);
        Registry::get('view')->assign('returns', $orderReturnsData);
        Registry::get('view')->assign('refunds', $displayableRefunds);
        Registry::get('view')->assign('canMarkRefundAsPaid', $isAdmin);
        Registry::get('view')->assign('refundCreated', RefundStatus::CREATED());
        Registry::get('view')->assign('refundFailed', RefundStatus::FAILED());
    }

    Registry::set('navigation.tabs.extras', array (
        'title' => __('extra_data'),
        'js' => true
    ));

    $discussion = $discuss->getDiscussionRepository()->getByOrderId($order->getId());

    $canVendorShowDiscussion = $user->getUserType() === UserType::VENDOR;
    $canAdminShowDiscussion = $user->getUserType() === UserType::ADMIN && $discussion instanceof DiscussionInterface === true;

    Registry::get('view')->assign('canVendorShowDiscussion', $canVendorShowDiscussion);
    Registry::get('view')->assign('canAdminShowDiscussion', $canAdminShowDiscussion);

    if ($canVendorShowDiscussion === true || $canAdminShowDiscussion === true) {
        Registry::set('navigation.tabs.discussion', [
            'title' => __('discuss_menu'),
            'js' => true
        ]);

        if ($discussion !== null) {
            $messages = $discuss->getMessageRepository()->getByDiscussion($discussion->getId());
            $interlocutors = [];
            $isCompany = [];
            foreach ($messages as $message) {
                $author = new User($message->getAuthor());
                $interlocutors[$message->getAuthor()] = $author->getFullname() ?: $message->getAuthor();
                $isCompany[$message->getId()] = (int)$message->getDiscussion()->getMetaData('company_id') === $author->getCompanyId();
            }

            Registry::get('view')->assign('productId', $discussion->getMetaData('product_id'));
            Registry::get('view')->assign('title', $discussion->getMetaData('title'));
            Registry::get('view')->assign('interlocutors', $interlocutors);
            Registry::get('view')->assign('isCompany', $isCompany);
            Registry::get('view')->assign('messages', $messages);
            Registry::get('view')->assign(
                'csrfToken',
                container()->get('security.csrf.token_manager')->getToken(
                    'discussPostMessage'
                )
            );            Registry::get('view')->assign('orderId', $discussion->getMetaData('product_id'));

            Registry::get('view')->assign(
                'validateExtension',
                MessageAttachmentService::VALIDATE_ATTACHMENT_MESSAGE_EXTENSIONS
            );

            Registry::get('view')->assign('discussion', $discussion);
            Registry::get('view')->assign('discussionId', $discussion->getId());
            Registry::get('view')->assign('isDiscussionsAllowedForAdmin', $isDiscussionsAllowedForAdmin);
            Registry::get('view')->assign('companyName', $companyRepository->getCompanyNameById((int) $discussion->getMetaData('company_id')));
        }

        Registry::get('view')->assign('orderId', $_REQUEST['order_id']);
    }

    // Affichage optionnel des informations du workflow de commande,
    // uniquement si le paramètre 'debug_workflow' est spécifié dans l'url.
    if (isset($_REQUEST['debug_workflow']) || isset($_SESSION['debug_workflow'])) {
        $_SESSION['debug_workflow'] = true;
        $order = $orderService->getOrder($_REQUEST['order_id']);

        $actions = [
            ActionName::CONFIRM()->getValue() => 'Marquer comme confirmé',
            ActionName::MARKASSHIPPED()->getValue() => 'Marquer comme expédié',
            ActionName::MARKASDELIVERED()->getValue() => 'Marquer comme livré',
            ActionName::ENDWITHDRAWALPERIOD()->getValue() => 'Finir le délai de rétractation',
            ActionName::MARKASFINISHED()->getValue() => 'Terminer la commande manuellement',
            ActionName::DISPATCHFUNDS()->getValue() => 'Répartir les fonds',
        ];

        $allowedActions = \array_filter(
            $actions,
            fn(string $action) => container()->get("marketplace.order.action.$action")->isAllowed($order),
            ARRAY_FILTER_USE_KEY
        );

        Registry::get('view')->assign('workflow_allowed_actions', $allowedActions);
        Registry::get('view')->assign('basket_id', $order->getBasketId());
        Registry::get('view')->assign('workflow_name', $order->getWorkflowName());
        Registry::get('view')->assign('workflow_progress', $order->getWorkflowProgress());
        Registry::get('view')->assign('workflow_status', $order->deduceWorkflowTranslationKey());
        Registry::get('view')->assign('workflow_legacy_status', $order->deduceLegacyStatus());
        Registry::get('view')->assign('workflow_traces', $container->get('marketplace.order.tracer')->list($order->getId()));
        Registry::get('view')->assign('showCommissionData', false);

        if (Registry::get('runtime.company_id') == 0) {
            Registry::get('view')->assign('showCommissionData', true);
            $commissionService = $container->get('marketplace.commission.commission_service');

            Registry::get('view')->assign('commissionMarketplace', $commissionService->getMarketplaceCommission($order)->getConvertedAmount());

            $orderCommission = $commissionService->getOrderAmountsCommissionByOrderId($order->getId());
            $orderCommission = reset($orderCommission);

            if ($orderCommission instanceof OrderAmountsCommission) {
                Registry::get('view')->assign('commissionOrigin', 'marchand');
                Registry::get('view')->assign('commissionPercent', $orderCommission->getPercentAmount());
                Registry::get('view')->assign('commissionFixed', $orderCommission->getFixAmount());
                Registry::get('view')->assign('commissionMaximum', $orderCommission->getMaximumAmount());
            } else {
                Registry::get('view')->assign('commissionOrigin', 'marketplace');
                $defaultCommission = $commissionService->getDefaultCommission();
                Registry::get('view')->assign('commissionPercent', $defaultCommission->getPercentAmount());
                Registry::get('view')->assign('commissionFixed', $defaultCommission->getFixAmount());
                Registry::get('view')->assign('commissionMaximum', $defaultCommission->getMaximumAmount());
            }

            $categoryCommissions = $commissionService->getCategoriesCommissionsByOrder($order);
            if (\count($categoryCommissions) > 0) {
                Registry::get('view')->assign('categoriesCommissions', $categoryCommissions);
            }
        }
    }
    if ($container->getParameter('feature.subscription') === true) {
        // activity data (left panel)
        $activity_data = ['total_subscriptions_count' => 0, 'total_subscriptions_count_active' => 0];
        foreach ($subscriptionRepository->findByUser($order->getUser())[0] as $userSubscription) {
            $activity_data['total_subscriptions_count']++;
            if ($userSubscription->getStatus()->getValue() === SubscriptionStatus::ACTIVE()->getValue()) {
                $activity_data['total_subscriptions_count_active']++;
            }
        }
    }

    [$total_orders] = fn_get_orders(['user_id' => [$order->getUserId()]]);
    $activity_data['total_orders_count'] = \count($total_orders);

    Registry::get('view')->assign('activity_data', $activity_data);
} elseif ($mode == 'picker') {
    $_REQUEST['skip_view'] = 'Y';

    [$orders, $search] = fn_get_orders($_REQUEST, Registry::get('settings.Appearance.admin_orders_per_page'));
    Registry::get('view')->assign('orders', $orders);
    Registry::get('view')->assign('search', $search);

    Registry::get('view')->display('pickers/orders/picker_contents.tpl');
    exit;

} elseif ($mode == 'update_status') {
    $container->get('logger')->warning(
        'The order.update_status action should not be used',
        $_REQUEST
    );

    /**
     * Avancement du workflow
     */
    $order = $orderService->getOrder($_REQUEST['id']);

    // Paiement manuel (ou à la sauvage par l'admin)
    if ($_REQUEST['status'] == OrderStatus::STANDBY_VENDOR) {
        $markAsPaid = $container->get('marketplace.order.action.mark_as_paid');
        if ($markAsPaid->isAllowed($order)) {
            $markAsPaid->execute($order);
        }
    }

    if ($_REQUEST['status'] == OrderStatus::PROCESSING_SHIPPING) {
        // Le vendeur a cliqué sur "Valider"
        $accept = $container->get('marketplace.order.action.accept');
        if ($accept->isAllowed($order)) {
            $accept->execute($order);
        }
    } else if ($_REQUEST['status'] == OrderStatus::VENDOR_DECLINED) {
        // Le vendeur a refusé la commande
        $refuse = $container->get('marketplace.order.action.refuse');
        if ($refuse->isAllowed($order)) {
            $refuse->execute($order);
            $subscriptionService->updateStatus([$order], SubscriptionStatus::DISABLED());
        }
    }

    if ($_REQUEST['status'] == OrderStatus::CANCEL) {
        // Le vendeur annule la commande
        $cancel = $container->get('marketplace.order.action.cancel');
        if ($cancel->isAllowed($order)) {
            $cancel->execute($order);
            $subscriptionService->updateStatus([$order], SubscriptionStatus::DISABLED());
        }
    }

    // use the 3 checkboxes if the status in changed with the selector, not the button to accept / decline
    $notification_rules = empty($_REQUEST['isVendorBtn']) ? fn_get_notification_rules($_REQUEST) : [];

    if ($_REQUEST['force_status']) {
        $container->get('logger')->warning(
            sprintf('Order #%s had its status forced', $order->getId()),
            [
                'order_id' => $order->getId(),
                'status' => (string) $order->getStatus(),
                'newStatus' => $_REQUEST['status']
            ]
        );
        $container->get('marketplace.order.tracer')->trace(
            $order->getId(),
            sprintf('Status forced to %s', $_REQUEST['status'])
        );
    }

    $order_info = fn_get_order_short_info($_REQUEST['id']);
    $old_status = $order_info['status'];
    if (fn_change_order_status($_REQUEST['id'], $_REQUEST['status'], '', $notification_rules)) {
        $order_info = fn_get_order_short_info($_REQUEST['id']);
        fn_check_first_order($order_info);
        $new_status = $order_info['status'];
        if ($_REQUEST['status'] != $new_status) {
            Registry::get('ajax')->assign('return_status', $new_status);
            Registry::get('ajax')->assign('color', fn_get_status_param_value($new_status, 'color'));

            fn_set_notification('W', __('warning'), __('status_changed'));
        } else {
            fn_set_notification('N', __('notice'), __('status_changed'));
        }
    } else {
        fn_set_notification('E', __('error'), __('error_status_not_changed'));
        Registry::get('ajax')->assign('return_status', $old_status);
        Registry::get('ajax')->assign('color', fn_get_status_param_value($old_status, 'color'));
    }

    if (empty($_REQUEST['return_url'])) {
        exit;
    } else {
        return array(CONTROLLER_STATUS_REDIRECT, $_REQUEST['return_url']);
    }

} elseif ($mode === REFUND_KEY) {
    // L'admin de la MP rembourse la commande
    $order = $orderService->getOrder((int) $_GET['order_id']);

    try {
        $refund = $refundService->createCompleteRefund($order);
        $refund = $refundService->executeRefund($refund, $order);
        $subscriptionService->updateStatus([$order], SubscriptionStatus::DISABLED());
        $refundNotificationService->notifyRefund($refund, $order);
    } catch (\Exception $exception) {
        fn_set_notification('E', __('error'), $exception->getMessage());
    }

    return [CONTROLLER_STATUS_REDIRECT, "orders.details?order_id=$_GET[order_id]"];

} elseif ($mode == 'update_decline_reason') {

    $condition = '';
    if ($company_id = Registry::get('runtime.company_id')) {
        $condition = db_quote(' AND company_id = ?i', $company_id);
    }
    db_query("UPDATE cscart_orders SET decline_reason = ?s WHERE order_id = ?i".$condition, $_REQUEST['decline_reason'], $_REQUEST['order_id']);

    exit;

} elseif ($mode == 'manage') {
    if (!empty($params['status']) && $params['status'] == STATUS_INCOMPLETED_ORDER) {
        $params['include_incompleted'] = true;
    }

    $params['company_name'] = true;
    $params['refund_status_label'] = true;

    [$orders, $search, $totals] = fn_get_orders($params, Registry::get('settings.Appearance.admin_orders_per_page'), true);

    $updatedOrders = [];

    foreach ($orders as $order) {
        $subscription = $subscriptionService->getByFirstOrderId($order['order_id'])[0] ?? null;
        if ($subscription instanceof Subscription) {
            $order['subscription_id'] = $subscription->getId();
            $order['is_subscription_initiator'] = true;
        }
        $updatedOrders[] = $order;
    }

    $orders = $updatedOrders;

    if (!empty($params['include_incompleted']) || !empty($search['include_incompleted'])) {
        Registry::get('view')->assign('incompleted_view', true);
    }

    if (!empty($_REQUEST['redirect_if_one']) && count($orders) == 1) {
        return array(CONTROLLER_STATUS_REDIRECT, "orders.details?order_id={$orders[0]['order_id']}");
    }

    $shippings = fn_get_shippings(true, (string) GlobalState::interfaceLocale());
    if (Registry::get('runtime.company_id')) {
        $company_shippings = fn_get_companies_shipping_ids(Registry::get('runtime.company_id'));

        foreach ($shippings as $shipping_id => $shipping) {
            if (!in_array($shipping_id, $company_shippings)) {
                unset($shippings[$shipping_id]);
            }
        }
    }

    $remove_cc = db_get_field("SELECT COUNT(*) FROM ?:status_data WHERE type = 'O' AND param = 'remove_cc_info' AND value = 'N'");
    $remove_cc = $remove_cc > 0 ? true : false;
    $workflowTranslation = $container->getParameter('feature.activate_workflow_translation');
    Registry::get('view')->assign('remove_cc', $remove_cc);
    Registry::get('view')->assign('workflowTranslation', $workflowTranslation);

    if ($workflowTranslation) {
        Registry::get('view')->assign('order_status_descr', $orderService->getWorkflowStatuses());
    }

    Registry::get('view')->assign('canRefund', true);
    Registry::get('view')->assign('order_refund_status_created',(string) RefundStatus::CREATED()->getValue());
    Registry::get('view')->assign('order_refund_status_failed', (string) RefundStatus::FAILED()->getValue());
    Registry::get('view')->assign('orders', $orders);
    Registry::get('view')->assign('search', $search);

    Registry::get('view')->assign('totals', $totals);
    Registry::get('view')->assign('display_totals', fn_display_order_totals($orders));
    Registry::get('view')->assign('shippings', $shippings);

    $payments = fn_get_simple_payment_methods(false);
    Registry::get('view')->assign('payments', $payments);
    Registry::get('view')->assign('orderRefundStatuses', OrderRefundStatus::toArray());
    Registry::get('view')->assign('featureSubscription', $container->getParameter('feature.subscription'));
    Registry::get('view')->assign('subscriptionLinkStatuses', SubscriptionLinkStatus::toArray());

} elseif ($mode == 'google') {
    // In this action we loop the script until google data is changed
    if ($action == 'wait_response') {
        $current_time = TIME;
        echo "Waiting for a Google response. Please be patient.";
        fn_flush();
        do {
            echo ' .';
            $google_info_new = db_get_field(
                "SELECT data FROM ?:order_data WHERE order_id = ?i AND type = ?s",
                $_REQUEST['order_id'],
                OrderDataType::GOOGLE_CHECKOUT_INFO()->getValue()
            );
            if ($google_info_new != $_SESSION['google_info']) {
                unset($_SESSION['google_info']);

                return array(CONTROLLER_STATUS_REDIRECT, "orders.details?order_id=$_REQUEST[order_id]");
            }
            sleep(1);
        } while (time() - TIME < 59);

        return array(CONTROLLER_STATUS_REDIRECT, "orders.google.wait_response?order_id=$_REQUEST[order_id]");
    }

} elseif ($mode == 'get_custom_file') {
    if (!empty($_REQUEST['file']) && !empty($_REQUEST['order_id'])) {
        $file_path = 'order_data/' . $_REQUEST['order_id'] . '/' . $_REQUEST['file'];

        $customFilesStorageService = container()->get('Wizacha\Storage\CustomFilesStorageService');
        if ($customFilesStorageService->isExist($file_path)) {

            $filename = !empty($_REQUEST['filename']) ? $_REQUEST['filename'] : '';
            return $customFilesStorageService->get($file_path, $filename);
        }
    }
} elseif ($mode == 'workflow_action') {
    /**
     * Ce mode est utilisé uniquement en mode de debug du workflow pour
     * faire avancer certaines étapes plus rapidement
     *
     * Dans notre cas on a juste les actions:
     *  - marquer comme livré
     *  - finir le délai de rétractation
     *  - répartir les fonds
     *
     * Ce sont des actions qui sont faites en temps normal en fonction du
     * temps qui passe, hors pour les tests on a besoin de les faire avancer
     * plus rapidement
     */
    $action = $container->get('marketplace.order.action.'.$_REQUEST['action']);
    $order = $orderService->getOrder($_REQUEST['order_id']);
    $confirm = $container->get('marketplace.order.action.confirm');

    if ($action->isAllowed($order)) {
        $currentModule = $order->getWorkflowProgress()->getModuleName();

        // Execute action except for the DispatchFunds because we may want to execute it several times
        if ($action instanceof DispatchFunds === false) {
            $action->execute($order);
        }

        if ($action instanceof DispatchFunds) {
            fn_change_order_status($order->getId(), OrderStatus::COMPLETED);
        } else if ($action instanceof MarkAsPaid) {
            if ($confirm->isAllowed($order)) {
                $confirm->execute($order);
            }

            foreach ($transactionService->findByOrderId($order->getId()) ?? [] as $transaction) {
                if ($transactionService->assertTransactionCanBeMarkAsPaid($transaction)) {
                    $transactionService->updateTransactionStatus($transaction, TransactionStatus::SUCCESS());
                }
            }

            if ($currentModule->equals(ModuleName::WAIT_PAYMENT_DEFERMENT())) {
                // en paiement différé l'action de payer est la dernière étape du workflow
                if (fn_get_order_info($order->getId())['isPaymentRefused'] === true) {
                    $dispatchFunds = $container->get('marketplace.order.action.dispatch_funds');
                    if ($dispatchFunds->isAllowed($order) === true) {
                        $dispatchFunds->execute($order);
                    }
                }
                fn_change_order_status($order->getId(), OrderStatus::COMPLETED);
            } else {
                // En cas normal, on passe en validation vendeur
                fn_change_order_status($order->getId(), OrderStatus::STANDBY_VENDOR);

                if ($order->getPayment()->getProcessorName() !== null
                    && $order->getPayment()->getProcessorName()->getValue() === 'mangopay'
                    && $order->getPayment()->getType()->getValue() === 'bank-transfer'
                ) {
                    $transactionService = container()->get('marketplace.transaction.transaction_service');
                    $transactions = $transactionService->findBy(['orderId' => $order->getId()]);

                    if (\count($transactions) !== 0) {
                        $transactionService->save(
                            $transactions[0]->setStatus(TransactionStatus::SUCCESS())
                        );
                    }
                }
            }
        } else if ($action instanceof MarkAsShipped) {
            fn_change_order_status($order->getId(), OrderStatus::PROCESSED);
        } else if ($action instanceof Cancel) {
            fn_change_order_status($order->getId(), OrderStatus::CANCEL);
            $subscriptionService->updateStatus([$order], SubscriptionStatus::DISABLED());
            foreach ($order->getItems() as $item) {
                $extras = (array) unserialize($item->getExtra());
                fn_update_product_amount($item->getProductId(), $item->getAmount(), $extras['product_options'] ?? [], '+');
            }
        } else if ($action instanceof Accept) {
            fn_change_order_status($order->getId(), OrderStatus::PROCESSING_SHIPPING);
        } else if ($action instanceof Refuse) {
            fn_change_order_status($order->getId(), OrderStatus::VENDOR_DECLINED);
            $subscriptionService->updateStatus([$order], SubscriptionStatus::DISABLED());
        }
    }

    return array(CONTROLLER_STATUS_REDIRECT, 'orders.details?order_id='.$_REQUEST['order_id']);
} elseif ($mode == 'complete') {
    $order = $orderService->getOrder($_REQUEST['order_id']);

    $markAsDelivered = $container->get('marketplace.order.action.mark_as_delivered');
    $markAsPaid = $container->get('marketplace.order.action.mark_as_paid');
    $endWithdrawalPeriod = $container->get('marketplace.order.action.end_withdrawal_period');

    if ($markAsDelivered->isAllowed($order)) {
        $markAsDelivered->execute($order);
    }
    if ($markAsPaid->isAllowed($order)) { // nécessaire pour le paiement à échéance
        $markAsPaid->execute($order);
    }
    if ($endWithdrawalPeriod->isAllowed($order)) {
        $endWithdrawalPeriod->execute($order);
    }

    fn_change_order_status($order->getId(), OrderStatus::COMPLETED);

    return array(CONTROLLER_STATUS_REDIRECT, 'orders.details?order_id='.$_REQUEST['order_id']);

} elseif ($mode === 'partial_refund') {
    $orderId = (int) $_GET['order_id'];
    $order = $orderService->getOrder($orderId);
    $orderInfo = $orderService
        ->overrideLegacyOrder($orderId, false, true, true, true);
    $remainingProducts = $orderInfo['products'];
    if ($order->isDiscounted() === true) {
        $orderInfo['shipping_cost'] = $order->getShippingCostAfterPromotion()->getConvertedAmount();
    }
    $refunds = $refundService->getByOrderId($orderId);
    $shippingsRefunded = false;

    foreach ($refunds as $refund) {
        if ($refund->hasShipping()) {
            $shippingsRefunded = true;
        }

        foreach ($refund->getItems() as $item) {
            $itemId = $item->getItemId();

            if (\array_key_exists($itemId, $remainingProducts)) {
                $baseTax = Money::fromVariable($remainingProducts[$itemId]['tax_value'])
                    ->divide($remainingProducts[$itemId]['amount'])
                ;
                $remainingProducts[$itemId]['amount'] = (int) $remainingProducts[$itemId]['amount'] - $item->getQuantity();
                $remainingProducts[$itemId]['subtotal'] = Money::fromVariable($remainingProducts[$itemId]['base_price'])
                    ->multiply($remainingProducts[$itemId]['amount'])
                    ->getConvertedAmount()
                ;
                $remainingProducts[$itemId]['tax_value'] = $baseTax
                    ->multiply($remainingProducts[$itemId]['amount'])
                    ->getConvertedAmount()
                ;
            }
        }
    }

    $orderInfo['shippingCostDiscount'] = $order->getShippingCostDiscount();
    $remainingProducts = $orderService->applyDiscountOnItems($orderInfo, $remainingProducts);

    $referenceGenerator = $container->get('marketplace.order.credit_note_reference_generator');
    if ($referenceGenerator->canGenerate($order) === true) {
        $autoGeneratedReference = $referenceGenerator->generate($order);
    }

    // Shipping info
    $shippingInfo['originalShippingCost'] = $order->getOriginalShippingCost()->getConvertedAmount();
    $shippingInfo['shippingCostDiscount'] = $order->getShippingCostDiscount()->getConvertedAmount();

    $invoicingDisabled = $companyService->isInvoicingDisabled($orderInfo['company_id']);

    $templating = $container->get('templating');
    $html = $templating->render('@App/backend/refund/partial_refund.html.twig', [
        'order_info' => $orderInfo,
        'autoGeneratedReference' => $autoGeneratedReference ?? null,
        'remainingProducts' => $remainingProducts,
        'shippingsRefunded' => $shippingsRefunded,
        'isDiscounted' => $order->isDiscounted(),
        'currencySign' => $container->getParameter('currency.sign'),
        'isTaxIncludedInPrice' => $order->getItems()[0]->isTaxIncludedInPrice(),
        'shippingInfo' => $shippingInfo,
        'invoicingDisabled' => $invoicingDisabled
     ]);

    Registry::get('view')->assign('html', $html);
    Registry::get('view')->assign('order_info', $orderInfo);

} elseif ($mode === 'partial_refund_confirm') {
    $orderId = (int) $_GET['order_id'];
    $refundId = (int) $_GET['refund_id'];

    $orderInfo = $orderService
        ->overrideLegacyOrder($orderId, false, true, true, true);
    $productsInfo = [];

    $order = $orderService->getOrder($orderId);
    $orderItems = [];
    foreach ($order->getItems() as $orderItem) {
        $orderItems[$orderItem->getItemId()] = $orderItem;
        $productsInfo[$orderItem->getItemId()] = $orderInfo['products'][$orderItem->getItemId()];
    }

    $refund = $refundService->get($refundId);
    $creditNote = $container
        ->get('marketplace.order.credit_note.credit_note_service')
        ->buildCreditNote($refund, $order)
    ;

    $templating = $container->get('templating');
    $html = $templating->render('@App/backend/refund/partial_refund_confirm.html.twig', [
        'order' => $order,
        'orderItems' => $orderItems,
        'creditNote' => $creditNote,
        'products' => $productsInfo,
        'refund' => $refund,
    ]);

    Registry::get('view')->assign('html', $html);
    Registry::get('view')->assign('order_info', $orderInfo);
} elseif ($mode === 'create_discussion') {
    $orderId = (int) $_GET['order_id'];
    $order = $orderService->getOrder($orderId);

    if ($auth['user_type'] !== UserType::VENDOR) {
        return [CONTROLLER_STATUS_DENIED];
    }

    $user = $container->get('marketplace.user.user_service')->get($auth['user_id']);

    $vendor = new ApiSecurityUser(
        $user->getUserId(),
        $user->getEmail(),
        $user->getApiKey(),
        null,
        ['ROLE_VENDOR'],
        $user->getCompanyId()
    );

    $discussion = $discussionService->createDiscussionFromOrderWithCustomer($vendor, $orderId, $order->getUserId());

    $discuss->getDiscussionRepository()->save($discussion);

    fn_set_notification('N', __('notice'), __('new_discussion_created'));

    return [
        CONTROLLER_STATUS_REDIRECT,
        'orders.details&order_id=' . $orderId . '&selected_section=discussion'
    ];

}
