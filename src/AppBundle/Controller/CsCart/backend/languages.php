<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON> Shalnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Symfony\Component\Yaml\Exception\ParseException;
use Tygh\Languages\Languages;
use Tygh\Languages\Values as LanguageValues;
use Tygh\Registry;
use Wizacha\Marketplace\GlobalState\GlobalState;

const LANG_CODE = 'lang_code';

if (!defined('BOOTSTRAP')) { die('Access denied'); }

if ($_SERVER['REQUEST_METHOD'] == 'POST') {

    fn_trusted_vars("lang_data", "new_lang_data");
    $suffix = 'manage';

    //
    // Update language variables
    //
    if ($mode == 'm_update_variables') {
        if (is_array($_REQUEST['lang_data'])) {
            LanguageValues::updateLangVar($_REQUEST['lang_data']);
        }

        $suffix = 'translations';
    }

    //
    // Delete language variables
    //
    if ($mode == 'm_delete_variables') {
        if (!empty($_REQUEST['names'])) {
            LanguageValues::deleteVariables($_REQUEST['names']);
        }

        $suffix = 'translations';
    }

    //
    // Add new language variable
    //
    if ($mode == 'update_variables') {
        if (!empty($_REQUEST['new_lang_data'])) {
            $params = array('clear' => false);
            foreach (Languages::getAll() as $lc => $_v) {
                LanguageValues::updateLangVar($_REQUEST['new_lang_data'], $lc, $params);
            }
        }

        $suffix = 'translations';
    }

     //
    // Delete languages
    //
    if ($mode == 'm_delete') {

        if (!empty($_REQUEST['lang_ids'])) {
            Languages::deleteLanguages($_REQUEST['lang_ids']);
        }
    }

    //
    // Update languages
    //
    if ($mode == 'm_update') {

        if (!Registry::get('runtime.company_id')) {
            if (!empty($_REQUEST['update_language'])) {
                foreach ($_REQUEST['update_language'] as $lang_id => $data) {
                    Languages::update($data, $lang_id);
                }
            }

            Languages::saveLanguagesIntegrity();
        }
    }

    //
    // Create/update language
    //
    if ($mode == 'update') {

        $lc = false;
        if (!Registry::get('runtime.company_id')) {

            $lang_code = $_REQUEST['language_data']['lang_code'];

            if ($lang_code = \Locale::acceptFromHttp($lang_code)) {
                $lc = Languages::update($_REQUEST['language_data'], $_REQUEST['lang_id']);
            } else {
                fn_set_notification('E', __('error'), __('lang_code_not_valid'));
            }

            if ($lc !== false) {
                Languages::saveLanguagesIntegrity();
                LanguageValues::saveLanguageValues((string) $_REQUEST['language_data'][LANG_CODE]);
            }
        }

        if ($lc == false) {
            fn_delete_notification('changes_saved');
        }
    }

    $q = (empty($_REQUEST['q'])) ? '' : $_REQUEST['q'];

    return array(CONTROLLER_STATUS_OK, "languages.$suffix?q=$q");
}

//
// Get language variables values
//
if ($mode == 'manage') {

    $sections = array(
        'translations' => array(
            'title' => __('translations'),
            'href' => fn_url('languages.translations'),
        ),
        'manage_languages' => array(
            'title' => __('manage_languages'),
            'href' => fn_url('languages.manage'),
        ),
    );

    Registry::set('navigation.dynamic.sections', $sections);
    Registry::set('navigation.dynamic.active_section', 'manage_languages');

    Registry::set('navigation.tabs', array (
        'languages' => array (
            'title' => __('installed_languages'),
            'js' => true
        ),
    ));

    $view = Registry::get('view');

    $languages = Languages::getAll(true);
    $view->assign('langs', $languages);
    $view->assign('countries', fn_get_simple_countries(false, (string) GlobalState::interfaceLocale()));

} elseif ($mode == 'translations') {

    [$lang_data, $search] = container()->get('app.translation')->searchTranslations((string) GlobalState::contentLocale(), $_REQUEST['page'] ?? 1, $_REQUEST['items_per_page'] ?? null, $_REQUEST['q'] ?? null);

    Registry::get('view')->assign('lang_data', $lang_data);
    Registry::get('view')->assign('search', $search);

} elseif ($mode == 'delete_variable') {

    LanguageValues::deleteVariables($_REQUEST['name']);

    return array(CONTROLLER_STATUS_REDIRECT);

} elseif ($mode == 'update') {
    $lang_data = db_get_row("SELECT ?:languages.* FROM ?:languages WHERE lang_id = ?i",  $_REQUEST['lang_id']);

    Registry::get('view')->assign('lang_data', $lang_data);
    Registry::get('view')->assign('countries', fn_get_simple_countries(false, (string) GlobalState::contentLocale()));

} elseif ($mode == 'update_status') {
    fn_tools_update_status($_REQUEST);
    Languages::saveLanguagesIntegrity();
    exit;

} elseif ($mode == 'clone_language') {
    $lang_id = $_REQUEST['lang_id'];
    $lang_data = Languages::get(array('lang_id' => $lang_id), 'lang_id');

    if (!empty($lang_data) && !empty($_REQUEST[LANG_CODE])) {
        $language = $lang_data[$lang_id];

        $new_language = array(
            LANG_CODE => $_REQUEST[LANG_CODE],
            'name' => $language['name'] . '_clone',
            'country_code' => $language['country_code'],
            'from_lang_code' => $language[LANG_CODE],
            'status' => 'D', // Disable cloned language
        );

        $lc = Languages::update($new_language, 0);

        if ($lc !== false) {
            Languages::saveLanguagesIntegrity();
        }
    }

    return array(CONTROLLER_STATUS_REDIRECT, "languages.manage");

} elseif ($mode == 'delete_language') {

    if (array_key_exists('lang_id', $_REQUEST)) {
        Languages::deleteLanguages($_REQUEST['lang_id']);
    }

    return array(CONTROLLER_STATUS_REDIRECT, "languages.manage?selected_section=languages");
} elseif ($mode === 'create_profile_fields') {
    if (array_key_exists('lang_id', $_REQUEST)) {
        $langData = Languages::get([
            'lang_id' => $_REQUEST['lang_id']
        ], 'lang_id');

        if (false === array_key_exists($_REQUEST['lang_id'], $langData)) {
            return [CONTROLLER_STATUS_NO_PAGE];
        }

        try {
            Languages::createTranslations($langData[$_REQUEST['lang_id']][LANG_CODE]);
            fn_set_notification('N', __('successful'), __('create_language_translation_success'));
        } catch (ParseException $exception) {
            fn_set_notification('E', __('error'), __('missing_profile_fields_file'));
        }
    }

    return [CONTROLLER_STATUS_REDIRECT, "languages.manage?selected_section=languages"];
}
