<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

use Doctrine\ORM\QueryBuilder;
use Monolog\Logger;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\Security\Core\Exception\InvalidArgumentException;
use Tygh\Registry;
use Wizacha\Company;
use Wizacha\Component\Import\EximJob;
use Wizacha\Component\Import\EximJobLog;
use Wizacha\Component\Import\EximJobService;
use Wizacha\Component\Import\JobStatus;
use Wizacha\Component\Import\JobType;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\User\User;
use Wizacha\Marketplace\User\UserRepository;

if (!defined('BOOTSTRAP')) {
    die('Access denied');
}

$params = $_REQUEST;

/** @var EximJobService $eximJobService */
$eximJobService = container()->get('marketplace.import.job_service');

/** @var UserRepository */
$userRepository = container()->get('marketplace.user.user_repository');

$companyId = Company::runtimeID(AREA, $_SESSION, \Wizacha\Registry::defaultInstance());

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if ($mode == 'delete' && isset($params['job'])) {
        foreach ($_REQUEST['job'] as $k => $id) {
            try {
                $job = $eximJobService->get($id);
            } catch (NotFound $e) {
                return array(CONTROLLER_STATUS_NO_PAGE);
            }

            // Delete if user is Admin or job belongs to company
            if ($companyId === 0 || $companyId === $job->getCompanyId()) {
                $eximJobService->delete($job);
            }
        }

        fn_set_notification('N', __('notice'), __('text_exim_jobs_have_been_deleted'));
    }



    return array(CONTROLLER_STATUS_OK, "exim_jobs.manage");
}

if ($mode == 'manage') {
    $section = $_REQUEST['section'] ?? 'import';

    switch ($section) {
        case 'import':
            $title = 'exim_jobs_import_title';
            break;
        case 'export':
            $title = 'exim_jobs_export_title';
            break;
        default:
            throw new InvalidArgumentException('This section does not exist');
    }

    $params['items_per_page'] = $params['items_per_page'] ?? Registry::get('settings.Appearance.admin_pages_per_page');
    $params['page'] = intval($params['page']) > 0 ? intval($params['page']) :  1;

    $start = $params['page'] > 0 ? ($params['page'] - 1) * $params['items_per_page'] : 0;

    /**
     * Get paginate job with total items
     * Set company, type, status filter
     */
    $paginate = $eximJobService->getJobsPaginate(
        (int) $start,
        (int) $params['items_per_page'],
        function (QueryBuilder $query) use ($params, $companyId, $section): void {
            $query->addSelect('user.email');
            $query->leftJoin(User::class, 'user', 'WITH', 'job.userId = user.userId');

            if (\strlen($params['period']) > 0 && $params['period'] !== 'A') {
                [$params['time_from'], $params['time_to']] = fn_create_periods($params);

                $query->andWhere('job.createdAt >= :from AND job.createdAt <= :to');
                $query->setParameters([
                    'from' => date('Y-m-d H:i:s', $params['time_from']),
                    'to' => date('Y-m-d H:i:s', $params['time_to']),
                ]);
            }

            $query->andWhere('job.isImport = :isImport');
            $query->setParameter('isImport', $section === "export" ? 0 : 1);

            if ($companyId > 0) {
                $query->andWhere('job.companyId = :companyId');
                $query->setParameter('companyId', $companyId);
            }

            if (isset($params['job_user']) && is_numeric($params['job_user'])) {
                $query->andWhere('job.userId = :userId');
                $query->setParameter('userId', (int) $params['job_user']);
            }

            try {
                $type = new JobType($params['job_type']);
                $query->andWhere('job.type = :type');
                $query->setParameter('type', $type->getValue());
            } catch (Exception $e) {
            }

            try {
                $status = new JobStatus($params['job_status']);
                $query->andWhere('job.status = :status');
                $query->setParameter('status', $status->getValue());
            } catch (Exception $e) {
            }
        }
    );

    $results = $paginate->getQuery()->getResult();

    /**
     * Mapping additionnal data to jobs
     */
    $jobs = \array_map(
        function ($data) use ($eximJobService) {
            $data['email'] ??= __('deleted');
            /** @var EximJob */
            $eximJob = array_shift($data);
            $durationInfo = $eximJobService
                ->getProcessDurationInfo(
                    $eximJob
                )
            ;

            $processed = $eximJobService
                ->getCountProcessedLines(
                    $eximJob
                )
            ;

            return \array_merge(
                $eximJob->expose(),
                $data,
                $durationInfo,
                ['nbImported' => $processed]
            );
        },
        $results
    );

    $params['total_items'] = $paginate->count();

    Registry::get('view')->assign('companyId', $companyId);
    Registry::get('view')->assign('jobs', $jobs);
    Registry::get('view')->assign('search', $params);
    Registry::get('view')->assign('job_types', JobType::values());
    Registry::get('view')->assign('job_status', JobStatus::values());
    Registry::get('view')->assign(
        'job_users',
        array_map(
            function (User $user) {
                return [
                    'userId' => $user->getUserId(),
                    'email' => $user->getEmail()
                ];
            },
            $userRepository->findWithEximJob()
        )
    );
    Registry::get('view')->assign('title', $title);
}


if ($mode == 'detail') {
    $id = empty($params['id']) ? 0 : $params['id'];
    $params['items_per_page'] = $params['items_per_page'] ?? Registry::get('settings.Appearance.admin_pages_per_page') * 2;
    $params['page'] = intval($params['page']) > 0 ? intval($params['page']) :  1;
    $start = $params['page'] > 0 ? ($params['page'] - 1) * $params['items_per_page'] : 0;

    try {
        $job = $eximJobService->get($id);
    } catch (NotFound $e) {
        return array(CONTROLLER_STATUS_NO_PAGE);
    }

    if ($companyId > 0 && $companyId !== $job->getCompanyId()) {
        return array(CONTROLLER_STATUS_DENIED);
    }

    $paginate = $eximJobService->getLogsPaginate(
        (int) $start,
        (int) $params['items_per_page'],
        (string) $id,
        (int) $params['code']
    );

    $params['total_items'] = $paginate->count();

    try {
        $userData = $userRepository->get($job->getUserId())->expose();
    } catch (NotFound $exception) {
        $userData = [
            'email' => __('deleted')
        ];
    }

    $durationInfo = $eximJobService
        ->getProcessDurationInfo(
            $job
        )
    ;

    Registry::get('view')->assign('percent', $job->getPercent());
    Registry::get('view')->assign('job', $job->expose());
    Registry::get('view')->assign('processedLines', $eximJobService->getCountProcessedLines($job));
    Registry::get('view')->assign('duration', $durationInfo);
    Registry::get('view')->assign('logs', array_map(function (EximJobLog $log) {
        return $log->expose();
    }, $paginate->getQuery()->getResult()));
    Registry::get('view')->assign('file', $job->getFile());
    Registry::get('view')->assign('user', $userData);
    Registry::get('view')->assign('search', $params);
    Registry::get('view')->assign('code', [
        'WARNING' => Logger::WARNING,
        'ERROR' => Logger::ERROR,
    ]);
}

if ($mode == 'file' && isset($params['id'])) {
    try {
        $job = $eximJobService->get($params['id']);
    } catch (NotFound $e) {
        return array(CONTROLLER_STATUS_NO_PAGE);
    }

    // Get file if user is Admin or job belongs to company
    if ($companyId === 0 || $companyId === $job->getCompanyId()) {
        $eximJobService->getFile($job->getFile())->send();
    } else {
        return array(CONTROLLER_STATUS_DENIED);
    }
}


if ($mode == 'report' && isset($params['id'])) {
    try {
        /** @var EximJob $job */
        $job = $eximJobService->get($params['id']);
    } catch (NotFound $e) {
        return array(CONTROLLER_STATUS_NO_PAGE);
    }

    // Get file if user is Admin or job belongs to company
    if ($companyId === 0 || $companyId === $job->getCompanyId()) {
        $file = $eximJobService->getReport($job);

        if (is_string($file) === false) {
            return [CONTROLLER_STATUS_NO_PAGE];
        }

        $date = (new \DateTime())->format('Y-m-d');
        $response = new BinaryFileResponse($file);
        $response->setContentDisposition(ResponseHeaderBag::DISPOSITION_ATTACHMENT, "report-$date.csv");

        return $response;
    } else {
        return array(CONTROLLER_STATUS_DENIED);
    }
}

if ($mode == 'cancel' && \array_key_exists('job_id', $params) === true) {
    try {
        $job = $eximJobService->get($params['job_id']);
    } catch (NotFound $e) {
        return array(CONTROLLER_STATUS_NO_PAGE);
    }

    $eximJobService->cancel($job);
    fn_set_notification('N', __('notice'), __('text_exim_jobs_have_been_canceled'));

    return array(CONTROLLER_STATUS_OK, 'exim_jobs.detail&id=' . $params['job_id']);
}
