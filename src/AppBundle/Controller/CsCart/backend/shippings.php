<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON>halnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\Registry;
use Tygh\Settings;
use Tygh\Shippings\Shippings;
use Wizacha\Marketplace\GlobalState\GlobalState;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

$_REQUEST['shipping_id'] = empty($_REQUEST['shipping_id']) ? 0 : $_REQUEST['shipping_id'];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (!Registry::get('runtime.company_id')) {
        fn_trusted_vars (
            'shipping_data'
        );
    }

    $user = Registry::get('user_info', null);

    /*
     * Only administrator can create a shipping method
     */
    if ($mode == 'update' && $user['user_type'] != 'A' && empty($_REQUEST['shipping_id'])) {
        return array(CONTROLLER_STATUS_DENIED);
    }

    /*
     * Save rates for vendor
     */
    if ($mode == 'update' && !empty($_REQUEST['shipping_id']) && Registry::get('runtime.company_id')) {
        $shipping_data = $_REQUEST['shipping_data'];
        $companyId = Registry::get('runtime.company_id');

        $carriagePaidThreshold = null;
        if (true ===  \array_key_exists('carriage_paid_threshold', $shipping_data)
        && '' !== $shipping_data['carriage_paid_threshold']
        ) {
            // We check if the free shipping threshold is a number above 0
            if (false === is_numeric($shipping_data['carriage_paid_threshold'])
                || $shipping_data['carriage_paid_threshold'] <= 0
            ) {
                fn_set_notification('E', __('error'), __('api_carriage_paid_threshold_malformed'));
            } else {
                $carriagePaidThreshold = $shipping_data['carriage_paid_threshold'];
            }
        }

        fn_wizacha_shippings_w_update_rate(
            'w_company_shipping_rates',
            $_REQUEST['shipping_id'],
            $shipping_data['rates'],
            'company_id',
            $companyId,
            $companyId,
            $carriagePaidThreshold
        );

        $_extra = empty($_REQUEST['destination_id']) ? '' : '&destination_id=' . $_REQUEST['destination_id'];
        $suffix = '.update?shipping_id=' . $_REQUEST['shipping_id'] . $_extra;
        return array(CONTROLLER_STATUS_REDIRECT, "shippings$suffix");
    }

    $suffix = '';

    //
    // Update shipping method
    //
    if ($mode == 'update') {
        if ((!empty($_REQUEST['shipping_id']) && fn_check_company_id('shippings', 'shipping_id', $_REQUEST['shipping_id'])) || empty($_REQUEST['shipping_id'])) {
            fn_set_company_id($_REQUEST['shipping_data']);
            $_REQUEST['shipping_id'] = fn_update_shipping($_REQUEST['shipping_data'], $_REQUEST['shipping_id']);

        }

        $_extra = empty($_REQUEST['destination_id']) ? '' : '&destination_id=' . $_REQUEST['destination_id'];
        $suffix = '.update?shipping_id=' . $_REQUEST['shipping_id'] . $_extra;
    }

    // Delete selected rates
    if ($mode == 'delete_rate_values') {
        if (fn_check_company_id('shippings', 'shipping_id', $_REQUEST['shipping_id'])) {
            foreach ($_REQUEST['delete_rate_data'] as $destination_id => $rates) {
                fn_delete_rate_values($rates, $_REQUEST['shipping_id'], $destination_id);
            }
        }

        $suffix = ".update?shipping_id=$_REQUEST[shipping_id]";
    }

    //
    // Update shipping methods
    //
    if ($mode == 'm_update') {

        if (!empty($_REQUEST['shipping_data']) && is_array($_REQUEST['shipping_data'])) {
            foreach ($_REQUEST['shipping_data'] as $k => $v) {
                if (empty($v)) {
                    continue;
                }

                if (fn_check_company_id('shippings', 'shipping_id', $k)) {
                    fn_update_shipping($v, $k);
                }
            }
        }

        $suffix .= '.manage';
    }

    //
    // Delete shipping methods
    //
    //TODO make security check for company_id
    if ($mode == 'm_delete') {

        if (!empty($_REQUEST['shipping_ids'])) {
            foreach ($_REQUEST['shipping_ids'] as $id) {
                if (fn_check_company_id('shippings', 'shipping_id', $id)) {
                    fn_delete_shipping($id);
                }
            }
        }

        $suffix = '.manage';
    }

    return array(CONTROLLER_STATUS_OK, "shippings$suffix");
}

if ($mode == 'configure') {

    static $templates = array();
    static $addons_path = array();

    $shipping_id = !empty($_REQUEST['shipping_id']) ? $_REQUEST['shipping_id'] : 0;

    if (Registry::get('runtime.company_id')) {
        $shipping = db_get_row("SELECT company_id, service_params FROM ?:shippings WHERE shipping_id = ?i", $shipping_id);
        if ($shipping['company_id'] != Registry::get('runtime.company_id')) {
            exit;
        }
    }

    if (empty($templates)) {
        $templates = fn_get_dir_contents(fn_get_theme_path('[themes]/[theme]') . '/templates/views/shippings/components/services/', false, true, '.tpl');

        // Get addons templates as well
        $path = fn_get_theme_path('[themes]/[theme]') . '/templates/addons/[addon]/views/shippings/components/services/';

        $addons = Registry::get('addons');
        foreach ($addons as $addon_id => $addon) {
            $addon_path = str_replace('[addon]', $addon_id, $path);
            $addon_templates = fn_get_dir_contents($addon_path, false, true, '.tpl');

            if (!empty($addon_templates)) {
                $templates = array_merge($templates, $addon_templates);

                foreach ($addon_templates as $template) {
                    $addons_path[basename($template, '.tpl')] = str_replace('[addon]', $addon_id, 'addons/[addon]/views/shippings/components/services/');
                }
            }
        }
    }

    $module = !empty($_REQUEST['module']) ? $_REQUEST['module'] : '';

    if ($module && !in_array("$module.tpl", $templates)) {
        exit;
    }

    if (isset($shipping['service_params'])) {
        $shipping['service_params'] = unserialize($shipping['service_params']);
        if (empty($shipping['service_params'])) {
            $shipping['service_params'] = array();
        }
    } else {
        $shipping['service_params'] = fn_get_shipping_params($shipping_id);
    }
    Registry::get('view')->assign('shipping', $shipping);
    Registry::get('view')->assign('service_template', $module);
    Registry::get('view')->assign('addons_path', $addons_path);

    $code = !empty($_REQUEST['code']) ? $_REQUEST['code'] : '';
    Registry::get('view')->assign('code', $code);
// Add new shipping method
} elseif ($mode == 'add') {

    $rate_data = array(
        'rate_value' => array(
            'C' => array(),
            'W' => array(),
            'I' => array(),
        )
    );

    Registry::get('view')->assign('shipping_settings', Settings::instance()->getValues('Shippings'));
    Registry::get('view')->assign('rate_data', $rate_data);
    Registry::get('view')->assign('taxes', fn_get_taxes());

// Collect shipping methods data
} elseif ($mode == 'update') {
    $shipping = fn_get_shipping_info($_REQUEST['shipping_id']);
    $shippingPlaceholder = fn_get_shipping_info($_REQUEST['shipping_id'], (string) GlobalState::interfaceLocale());

    if (empty($shipping)) {
        return array(CONTROLLER_STATUS_NO_PAGE);
    }

    if ($shipping['rate_calculation'] == 'M') {
        $rates_defined = db_get_hash_array("SELECT destination_id, IF(rate_value = '', 0, 1) as defined FROM ?:shipping_rates WHERE shipping_id = ?i", 'destination_id', $_REQUEST['shipping_id']);
        foreach ($shipping['rates'] as $rate_key => $rate) {
            if (!empty($rates_defined[$rate['destination_id']]['defined'])) {
                $shipping['rates'][$rate_key]['rate_defined'] = true;
            }
        }
    }

    $shipping['type'] = 'company';
    Registry::get('view')->assign('shipping', $shipping);
    Registry::get('view')->assign('shippingPlaceholder', $shippingPlaceholder);

    $tabs = array (
        'general' => array (
            'title' => __('general'),
            'js' => true
        ),
        'configure' => array (
            'title' => __('configure'),
            'ajax' => true,
        ),
        'shipping_charges' => array (
            'title' => __('shipping_charges'),
            'js' => true
        ),
    );

    $shipping_settings = Settings::instance()->getValues('Shippings');
    $tabs['configure']['hidden'] = 'Y';

    if (Registry::get('runtime.company_id') && Registry::get('runtime.company_id') != $shipping['company_id']) {
        unset($tabs['configure']);
        Registry::get('view')->assign('hide_for_vendor', true);
    }

    Registry::set('navigation.tabs', $tabs);

    Registry::get('view')->assign('taxes', fn_get_taxes());

// Show all shipping methods
} elseif ($mode == 'manage') {

    $company_id = Registry::ifGet('runtime.company_id', null);
    Registry::get('view')->assign('shippings', fn_get_available_shippings($company_id));


// Delete shipping method
} elseif ($mode == 'delete') {

    if (!empty($_REQUEST['shipping_id']) && fn_check_company_id('shippings', 'shipping_id', $_REQUEST['shipping_id'])) {
        fn_delete_shipping($_REQUEST['shipping_id']);
    }

    return array(CONTROLLER_STATUS_REDIRECT, "shippings.manage");

// Delete selected rate
} elseif ($mode == 'delete_rate_value') {

    if (fn_check_company_id('shippings', 'shipping_id', $_REQUEST['shipping_id'])) {
        fn_delete_rate_values(array($_REQUEST['rate_type'] => array($_REQUEST['amount'] => 'Y')), $_REQUEST['shipping_id'], $_REQUEST['destination_id']);
    }

    return array(CONTROLLER_STATUS_REDIRECT, "shippings.update?shipping_id=$_REQUEST[shipping_id]&destination_id=$_REQUEST[destination_id]&selected_section=shipping_charges");
}

if ($mode == 'update') {
    if (Registry::get('runtime.company_id')) {
        Registry::get('view')->assign('w_is_vendor', true);
    } else {
        Registry::get('view')->assign('w_is_admin', true);
    }


    Registry::get('view')->assign('w_simple_rate', true);
}

if ($mode == 'manage' && Registry::get('runtime.company_id')) {
    Registry::get('view')->assign('w_is_vendor', true);
}

function fn_delete_rate_values($delete_rate_data, $shipping_id, $destination_id)
{
    $rate_values = db_get_field("SELECT rate_value FROM ?:shipping_rates WHERE shipping_id = ?i AND destination_id = ?i", $shipping_id, $destination_id);

    if (!empty($rate_values)) {
        $rate_values = unserialize($rate_values);
    }

    foreach ((array) $rate_values as $rate_type => $rd) {
        foreach ((array) $rd as $amount => $data) {
            if (isset($delete_rate_data[$rate_type][$amount]) && $delete_rate_data[$rate_type][$amount] == 'Y') {
                unset($rate_values[$rate_type][$amount]);
            }
        }
    }

    if (is_array($rate_values)) {
        foreach ($rate_values as $k => $v) {
            if ((count($v)==1) && (floatval($v[0]['value'])==0)) {
                unset($rate_values[$k]);
                continue;
            }
        }
    }

    if (fn_is_empty($rate_values)) {
            db_query("DELETE FROM ?:shipping_rates WHERE shipping_id = ?i AND destination_id = ?i", $shipping_id, $destination_id);
    } else {
        db_query("UPDATE ?:shipping_rates SET ?u WHERE shipping_id = ?i AND destination_id = ?i", array('rate_value' => serialize($rate_values)), $shipping_id, $destination_id);
    }
}
