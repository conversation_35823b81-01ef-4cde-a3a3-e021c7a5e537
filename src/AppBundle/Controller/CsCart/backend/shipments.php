<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, Ilya <PERSON> Shalnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\Pdf;
use Tygh\Registry;
use Wizacha\Component\Chronopost\Client;
use Wizacha\Component\Chronopost\Exceptions;
use Wizacha\Component\MondialRelay\Exception\ApiException;
use Wizacha\Component\MondialRelay\Model\Contact;
use Wizacha\Marketplace\GlobalState\GlobalState;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $suffix = '.manage';

    if ('add' == $mode) {
        $shipmentFormData = $_REQUEST['shipment_data'];
        $emailNotification = true;

        // Récupération des informations de la commande
        $orderInfo = container()
            ->get('marketplace.order.order_service')
            ->overrideLegacyOrder($shipmentFormData['order_id']);

        $orderId = $orderInfo['order_id'];

        // Récupération du mode de livraison
        $shippingMode = $orderInfo['shipping'][0]['w_delivery_type'] ?? false;
        $shippingId = $orderInfo['shipping'][0]['shipping_id'] ?? null;

        $container = container();

        //---------------------------------------------------------------
        // Gestion des cas particuliers nécessitant un code de validation
        //---------------------------------------------------------------
        if (!is_null($shippingId) && \Wizacha\Shipping::requiredCode($shippingId)) {
            if (!$result = $orderInfo[\Wizacha\C2C\Order::KEY_CODE_IN_ORDER]->validate(trim($shipmentFormData['try_code']))) {
                fn_set_notification('E', __('error'), __('w_failed_shipment_code_registration'), '', '');

                return [CONTROLLER_STATUS_REDIRECT, fn_url("orders.details?order_id=".$orderId)];
            }

            // Notification de réussite
            fn_set_notification('N', __('notice'), __('w_successful_shipment_code_registration'), '', '');

            // Sauvegarde du code (je ne sais pas trop à quoi ça sert..)
            \Wizacha\C2C\Order::saveCode($orderInfo);

            // Sauvegarde de la date de la validation dans le numéro de tracking
            $_REQUEST['shipment_data']['tracking_number'] = time();
        }

        //------------------------------------------------------
        // Gestion des cas particuliers de remise en main propre
        //------------------------------------------------------
        if (!is_null($shippingId) && \Wizacha\Shipping::isHandDelivery($shippingId)) {
            // Désactivation de l'email envoyé au client pour l'informer de la remise de sa commande au transporteur
            $emailNotification = false;
        }

        //--------------------------------------------------------------------
        // Gestion des cas particuliers devant faire appel à une API
        // pour la gestion des étiquettes (ex: Chrono 13, Chrono Relais, etc.)
        //--------------------------------------------------------------------

        // Si Chronopost
        if ($shippingMode && \Wizacha\Shipping::isChronopost($shippingMode)) {
            if (!$container->getParameter('feature.carrier.chronopost.chrono13') && !$container->getParameter('feature.carrier.chronopost.chronorelais')) {
                fn_set_notification('E', __('error'), __('an_error_occured'), '', '');
                container()->get('logger')->error('Having a shipment using Chronopost whereas Chronopost is disabled by feature flag.');

                return [CONTROLLER_STATUS_REDIRECT, fn_url()];
            }

            // Récupération des champs nécessaires
            $invoiceNumber = trim($orderInfo['w_invoice_number']);

            if (strlen($invoiceNumber) > 40) {
                fn_set_notification('E', __('error'), __('w_invoice_number_error'), '', '');

                return [CONTROLLER_STATUS_REDIRECT, fn_url("orders.details?order_id=".$orderId)];
            }

            // Récupération du poids global des produits du shipment
            $weight = 0.;
            foreach ($shipmentFormData['products'] as $key => $amount) {
                $weight += (float) $orderInfo['products'][$key]['weight'] * (int) $amount;
            }

            // Récupération de l'adresse d'expédition : celle de la companie
            $companyInfo = fn_get_company_data($orderInfo['company_id']);

            $fullName = $orderInfo['s_firstname'].' '.$orderInfo['s_lastname'];

            // Création du client SOAP
            $soapClient = new \SoapClient('https://ws.chronopost.fr/shipping-cxf/ShippingServiceWS?wsdl');

            // Génération des données
            $shipper = [
                'shipperCivility' => 'M', // Obligatoire, 'Mr' par défaut...
                'shipperName' => $companyInfo['company'],
                'shipperAdress1' => $companyInfo['address'],
                'shipperZipCode' => $companyInfo['zipcode'],
                'shipperCity' => $companyInfo['city'],
                'shipperCountry' => strtoupper($companyInfo['country']),
                'shipperEmail' => $companyInfo['email'],
                'shipperPhone' => $companyInfo['phone'],
            ];

            $zipCode = trim(
                str_replace(
                    mb_strtolower($orderInfo['s_city']),
                    "",
                    mb_strtolower($orderInfo['s_zipcode'])
                )
            );
            $recipient = [
                'recipientName' => empty($orderInfo['s_company']) ? $fullName : $orderInfo['s_company'], // Si une company est présente, elle passe devant
                'recipientName2' => empty($orderInfo['s_company']) ? '' : $fullName, // Si une company est présente, le nom passe en 2eme
                'recipientAdress1' => $orderInfo['s_address'],
                'recipientAdress2' => $orderInfo['s_address_2'],
                'recipientZipCode' => $zipCode,
                'recipientCity' => $orderInfo['s_city'],
                'recipientCountry' => strtoupper($orderInfo['s_country']),
                'recipientContactName' => $orderInfo['s_firstname'].' '.$orderInfo['s_lastname'],
                'recipientEmail' => $orderInfo['email'],
                'recipientPhone' => $orderInfo['s_phone'],
            ];

            $zipCode = trim(
                str_replace(
                    mb_strtolower($orderInfo['b_city']),
                    "",
                    mb_strtolower($orderInfo['b_zipcode'])
                )
            );
            $customer = [
                'customerCivility' => 'M', // Obligatoire, 'Mr' par défaut...
                'customerName' => $orderInfo['b_firstname'],
                'customerName2' => $orderInfo['b_lastname'],
                'customerAdress1' => $orderInfo['b_address'],
                'customerAdress2' => $orderInfo['b_address_2'],
                'customerZipCode' => $zipCode,
                'customerCity' => $orderInfo['b_city'],
                'customerCountry' => strtoupper($orderInfo['b_country']),
                'customerContactName' => $fullName,
                'customerEmail' => $orderInfo['email'],
                'customerPhone' => $orderInfo['b_phone'],
            ];

            /** @var Client */
            $chronopostClient = $container->get('marketplace.chronopost.client');
            // Appel à l'API Chronopost de création de shipment
            try {
                [$reservationNumber, $skybillNumber] = $chronopostClient->createShipment(
                    $shipper,
                    $recipient,
                    $customer,
                    $orderId,
                    $invoiceNumber,
                    $weight,
                    \Wizacha\Shipping::isChrono13($shippingId) ? '01' : '86' // 86 : Chrono Relais
                );
            } catch (Exceptions\MalformedAddress $exception) {
                // Si une adresse est mal formatée
                fn_set_notification('E', __('error'), __('error_chronopost_address'), '', '');
                container()->get('logger')->warning('Error while creating Chronopost shipment by API : invalid address', [
                    'message' => $exception->getMessage(),
                    'soapData' => $shipper + $recipient + $customer,
                ]);

                return [CONTROLLER_STATUS_REDIRECT, fn_url("orders.details?order_id=".$orderId)];
            } catch (\Exception $exception) {
                // S'il y a une autre erreur
                fn_set_notification('E', __('error'), __('an_error_occured'), '', '');
                container()->get('logger')->warning('Error while creating Chronopost shipment by API', [
                    'soapData' => $shipper + $recipient + $customer,
                ]);

                return [CONTROLLER_STATUS_REDIRECT, fn_url("orders.details?order_id=".$orderId)];
            }

            // Mise à jour du numéro de tracking, à l'ancienne...
            $_REQUEST['shipment_data']['tracking_number'] = $reservationNumber;

            // ...et du nouveau numéro interne de Chronopost
            $_REQUEST['shipment_data']['chronopost_skybill_number'] = $skybillNumber;
        }

        // --- MondialRelay ---
        //
        if ($shippingId && \Wizacha\Shipping::isMondialRelay($shippingId)) {
            if (!$container->getParameter('feature.carrier.mondial_relay')) {
                fn_set_notification('E', __('error'), __('an_error_occured'), '', '');
                container()->get('logger')->error(sprintf('Shipment %s uses MondialRelay but MondialRelay is disabled by feature flag.', $shippingId));

                return [CONTROLLER_STATUS_REDIRECT, fn_url()];
            }

            // Shipper info
            $companyInfo = fn_get_company_data($orderInfo['company_id']);

            // The weight is in kg in the backend.
            // MR API expects g.
            $weight = 0.;
            foreach ($shipmentFormData['products'] as $key => $amount) {
                $weight += (float) $orderInfo['products'][$key]['weight'] * (int) $amount * 1000;
            }

            $fullName = $orderInfo['s_firstname'].' '.$orderInfo['s_lastname'];

            $shipper = (new Contact())
                ->setName($companyInfo['company'])
                ->setAddress($companyInfo['address'])
                ->setZipCode($companyInfo['zipcode'])
                ->setCity($companyInfo['city'])
                ->setCountry(strtoupper($companyInfo['country']))
                ->setEmail($companyInfo['email'])
                ->setPhone($companyInfo['phone'])
            ;

            $recipient = (new Contact())
                ->setName($fullName)
                ->setAddress($orderInfo['s_address'])
                ->setAddress2($orderInfo['s_address_2'])
                ->setZipCode($orderInfo['s_zipcode'])
                ->setCity($orderInfo['s_city'])
                ->setCountry(strtoupper($orderInfo['s_country']))
                ->setEmail($orderInfo['email'])
                ->setPhone($orderInfo['s_phone'])
            ;

            if (empty($recipient->getAddress())) {
                $recipient->setAddress($recipient->getAddress2());
                $recipient->setAddress2(null);
            }

            $customerId = $orderInfo['user_id'];
            $pickupPointId = $orderInfo['s_pickup_point_id'];

            try {
                $result = $container->get('marketplace.mondial_relay.client')->createShipment($shipper, $recipient, $orderId, $customerId, $weight, $pickupPointId);
                $_REQUEST['shipment_data']['tracking_number'] = $result->getTrackingNumber();
                $_REQUEST['shipment_data']['label_url'] = $result->getLabelUrl();
            } catch(ApiException $e) {
                fn_set_notification('E', __('error'), __(sprintf('mondial_relay.error.%s', $e->getCode())), '', '');
                container()->get('logger')->warning('Error while creating MondialRelay shipment via API', [
                    'soapData' => [
                        'shipper' => $shipper,
                        'recipient' => $recipient,
                        'orderId' => $orderId,
                        'customerId' => $customerId,
                        'weight' => $weight,
                        'pickupPointId' => $pickupPointId,
                    ],
                    'message' => $e->getMessage(),
                    'exception' => $e,
                ]);

                return [CONTROLLER_STATUS_REDIRECT, fn_url("orders.details?order_id=".$orderId)];
            } catch (\Exception $e) {
                fn_set_notification('E', __('error'), __('an_error_occured'), '', '');
                container()->get('logger')->warning('Error while creating MondialRelay shipment via API', [
                    'soapData' => [
                        'shipper' => $shipper,
                        'recipient' => $recipient,
                        'orderId' => $orderId,
                        'customerId' => $customerId,
                        'weight' => $weight,
                        'pickupPointId' => $pickupPointId,
                    ],
                    'message' => $e->getMessage(),
                    'exception' => $e,
                ]);

                return [CONTROLLER_STATUS_REDIRECT, fn_url("orders.details?order_id=".$orderId)];
            }
        }

        // Vérification que le numéro de tracking est toujours présent
        if (empty($_REQUEST['shipment_data']['tracking_number'])) {
            fn_set_notification('E', __('error'), __('w_tracking_number_required'));
            $_REQUEST['shipment_data'] = $_POST['shipment_data'] = null;
        }

        $_REQUEST['shipment_data']['order_status'] = $_POST['shipment_data']['order_status'] = '';
        $_REQUEST['notify_user'] = $_POST['notify_user'] = 'Y';
        $_REQUEST['shipment_data']['shipping_id'] = $_POST['shipment_data']['shipping_id'] = $shippingId;

        if (!empty($_REQUEST['shipment_data'])) {
            $force_notification =  $emailNotification ? fn_get_notification_rules($_REQUEST) : [];
            fn_update_shipment($_REQUEST['shipment_data'], 0, 0, false, $force_notification);

            $suffix = '.details?order_id='.$orderId;
        }

        //-----------------------------------
        // Evolution du statut de la commande
        //-----------------------------------
        \Wizacha\OrderStatus::automaticUpdate($orderId);

        /**
         * Avancement du workflow
         */
        $order = $container->get('marketplace.order.order_service')->getOrder($orderId);
        if ($order->areAllShipmentsSent()) {
            $markAsShipped = $container->get('marketplace.order.action.mark_as_shipped');
            if ($markAsShipped->isAllowed($order)) {
                $markAsShipped->execute($order);
            }
        }
    }

    if ($mode == 'packing_slip' && !empty($_REQUEST['shipment_ids'])) {

        fn_print_shipment_packing_slips($_REQUEST['shipment_ids'], Registry::get('runtime.dispatch_extra') == 'pdf');
        exit;

    }

    if ($mode == 'm_delete' && !empty($_REQUEST['shipment_ids'])) {
        fn_delete_shipments($_REQUEST['shipment_ids']);

        if (!empty($_REQUEST['redirect_url'])) {
            return array(CONTROLLER_STATUS_REDIRECT, $_REQUEST['redirect_url']);
        }
    }

    return array(CONTROLLER_STATUS_OK, 'orders' . $suffix);
}

$params = $_REQUEST;

if ($mode == 'details') {
    if (empty($params['order_id']) && empty($params['shipment_id'])) {
        return array(CONTROLLER_STATUS_NO_PAGE);
    }

    if (!empty($params['shipment_id'])) {
        $params['order_id'] = db_get_field('SELECT ?:shipment_items.order_id FROM ?:shipment_items WHERE ?:shipment_items.shipment_id = ?i', $params['shipment_id']);
    }

    $shippings = db_get_array("SELECT a.shipping_id, a.min_weight, a.max_weight, a.position, a.status, b.shipping, b.delivery_time FROM ?:shippings as a LEFT JOIN ?:shipping_descriptions as b ON a.shipping_id = b.shipping_id AND b.lang_code = ?s WHERE a.status = ?s ORDER BY a.position", (string) GlobalState::contentLocale(), 'A');

    $orderInfo = container()
        ->get('marketplace.order.order_service')
        ->overrideLegacyOrder($params['order_id'], false, true, true);

    if (empty($orderInfo)) {
        return array(CONTROLLER_STATUS_NO_PAGE);
    }
    if (!empty($params['shipment_id'])) {
        $params['advanced_info'] = true;

        list($shipment, $search) = fn_get_shipments_info($params);

        if (!empty($shipment)) {
            $shipment = array_pop($shipment);

            foreach ($orderInfo['products'] as $item_id => $item) {
                if (isset($shipment['products'][$item_id])) {
                    $orderInfo['products'][$item_id]['amount'] = $shipment['products'][$item_id];
                } else {
                    $orderInfo['products'][$item_id]['amount'] = 0;
                }
            }
        } else {
            $shipment = array();
        }

        Registry::get('view')->assign('shipment', $shipment);
    }

    Registry::get('view')->assign('shippings', $shippings);
    Registry::get('view')->assign('order_info', $orderInfo);

} elseif ($mode == 'manage') {
    [$shipments, $search] = fn_get_shipments_info($params, Registry::get('settings.Appearance.admin_elements_per_page'));

    /**
     * No more mixed content since 01/2021
     * with Chrome 88
     * (see https://blog.chromium.org/2020/02/protecting-users-from-insecure.html)
     */
    foreach ($shipments as &$shipment) {
        $shipment['label_url'] = \preg_replace(
            '/^(?<head>(?<scheme>https?:)?\/\/?)/',
            '//',
            $shipment['label_url']
        );
    }

    Registry::get('view')->assign('shipments', $shipments);
    Registry::get('view')->assign('search', $search);

    // Récupération des infos de la commande
    $orderInfo = container()
        ->get('marketplace.order.order_service')
        ->overrideLegacyOrder($_REQUEST['order_id']);

    Tygh\Registry::get('view')->assign('w_s_zipcode', $orderInfo['s_zipcode']);

    // Récupération du type de mode de livraison
    $shippingType = $orderInfo['shipping'][0]['w_delivery_type'];

    // Passage du type mode de livraison au template
    Tygh\Registry::get('view')->assign('w_shipping_is_chronopost', \Wizacha\Shipping::isChronopost($shippingType));
    Tygh\Registry::get('view')->assign('w_shipping_is_mondialrelay', \Wizacha\Shipping::isMondialRelay($orderInfo['shipping'][0]['shipping_id']));
} elseif ($mode == 'packing_slip' && !empty($_REQUEST['shipment_ids'])) {

    fn_print_shipment_packing_slips($_REQUEST['shipment_ids'], !empty($_REQUEST['format']) && $_REQUEST['format'] == 'pdf');
    exit;

} elseif ($mode == 'delete' && !empty($_REQUEST['shipment_ids']) && is_array($_REQUEST['shipment_ids'])) {
    // Initialisation du tableau d'id des shipments à supprimer réellement (peut dépendre des fail de l'API)
    $deletedShipmentIds = [];

    // Récupération du container
    $container = container();

    // Parcours de tous les shipments
    foreach ($_REQUEST['shipment_ids'] as $shipmentId) {
        // Récupération des informations du shipment
        $shipmentInfo = fn_get_shipments_info([
            'shipment_id' => $shipmentId,
            'advanced_info' => true,
        ]);

        // Récupération du type de mode de livraison
        $shippingMode = $shipmentInfo[0][0]['w_delivery_type'] ?? false;

        // Suppression des shipment Chronopost
        if ($shippingMode && \Wizacha\Shipping::isChronopost($shippingMode)) {
            if (!$container->getParameter('feature.carrier.chronopost.chrono13') && !$container->getParameter('feature.carrier.chronopost.chronorelais')) {
                $container->get('logger')->error('Trying to delete shipment using Chronopost whereas Chronopost is disabled by feature flag.');
                fn_set_notification('E', __('error'), __('an_error_occured'), '', '');

                return [CONTROLLER_STATUS_REDIRECT, fn_url()];
            }

            // Récupération du skybill number
            $skybillNumber = $shipmentInfo[0][0]['chronopost_skybill_number'];

            // S'il manque le skybill number
            if (empty($skybillNumber)) {
                $container->get('logger')->warning('Trying to delete shipment using Chronopost without Skybill number.', [
                    'shipment_info' => $shipmentInfo,
                ]);

                continue;
            }

            /** @var Client */
            $chronopostClient = $container->get('marketplace.chronopost.client');
            // Suppression du shipment via l'API de Chronopost
            try {
                $chronopostClient->deleteShipment($skybillNumber);
            } catch (Exceptions\ParcelAlreadyShipped $exception) {
                // Si le colis ne peut être pas annulé car il a été pris en charge par Chronopost
                fn_set_notification('E', __('error'), __('error_chronopost_delete_shipment_already_shipped'), '', '');
                $container->get('logger')->warning($exception->getMessage(), [
                    'message' => $exception->getMessage(),
                    'shipment_info' => $shipmentInfo,
                ]);
            } catch (\Exception $exception) {
                // S'il y a une autre erreur
                fn_set_notification('E', __('error'), __('an_error_occured'), '', '');
                container()->get('logger')->warning('Error while deleting Chronopost shipment by API', [
                    'message' => $exception->getMessage(),
                    'shipment_info' => $shipmentInfo,
                ]);
            }
        }

        $deletedShipmentIds[] = $shipmentId;
    }

    if (!empty($deletedShipmentIds)) {
        fn_delete_shipments(implode(',', $deletedShipmentIds));
    }

    return array(CONTROLLER_STATUS_OK, 'shipments.manage');
}

function fn_get_packing_info($shipment_id)
{
    $params['advanced_info'] = true;
    $params['shipment_id'] = $shipment_id;

    list($shipment, $search) = fn_get_shipments_info($params);

    if (!empty($shipment)) {
        $shipment = array_pop($shipment);

        $order_info = container()
            ->get('marketplace.order.order_service')
            ->overrideLegacyOrder($shipment['order_id'], false, true, true);

        $shippings = db_get_array("SELECT a.shipping_id, a.min_weight, a.max_weight, a.position, a.status, b.shipping, b.delivery_time FROM ?:shippings as a LEFT JOIN ?:shipping_descriptions as b ON a.shipping_id = b.shipping_id AND b.lang_code = ?s ORDER BY a.position", (string) GlobalState::contentLocale());

        $_products = db_get_array("SELECT item_id, SUM(amount) AS amount FROM ?:shipment_items WHERE order_id = ?i GROUP BY item_id", $shipment['order_id']);
        $shipped_products = array();

        if (!empty($_products)) {
            foreach ($_products as $_product) {
                $shipped_products[$_product['item_id']] = $_product['amount'];
            }
        }

        foreach ($order_info['products'] as $k => $oi) {
            if (isset($shipped_products[$k])) {
                $order_info['products'][$k]['shipment_amount'] = $oi['amount'] - $shipped_products[$k];
            } else {
                $order_info['products'][$k]['shipment_amount'] = $order_info['products'][$k]['amount'];
            }

            if (isset($shipment['products'][$k])) {
                $order_info['products'][$k]['amount'] = $shipment['products'][$k];
            } else {
                $order_info['products'][$k]['amount'] = 0;
            }
        }
    } else {
        $shipment = $order_info = array();
    }

    return array($shipment, $order_info);
}

function fn_print_shipment_packing_slips($shipment_ids, $pdf = false, $lang_code = null)
{
    $lang_code = $lang_code ?? (string) GlobalState::interfaceLocale();
    $view = Registry::get('view');
    $html = [];

    foreach ($shipment_ids as $shipment_id) {
        list($shipment, $order_info) = fn_get_packing_info($shipment_id);
        if (empty($shipment)) {
            continue;
        }

        $view->assign('order_info', $order_info);
        $view->assign('shipment', $shipment);

        if ($pdf == true) {
            $html[] = $view->displayMail('orders/print_packing_slip.tpl', false, 'A', $order_info['company_id'], $lang_code);
        } else {
            $view->displayMail('orders/print_packing_slip.tpl', true, 'A', $order_info['company_id'], $lang_code);
            if ($shipment_id != end($shipment_ids)) {
                echo("<div style='page-break-before: always;'>&nbsp;</div>");
            }
        }
    }

    if ($pdf) {
        if (is_array($html)) {
            $html = implode("<div style='page-break-before: always;'>&nbsp;</div>", $html);
        }
        header('Content-disposition: attachment; filename="' . __('shipments') . '-' . implode('-', $shipment_ids) . '.pdf"');
        header('Content-type: application/pdf');
        exit(container()->get('knp_snappy.pdf')->getOutputFromHtml($html));
    }

    return true;
}
