<?php

use Tygh\Registry;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

// API auth info
$userId = $_SESSION['auth']['user_id'];
$userData = fn_get_user_info($userId, false);
Registry::get('view')->assign('user_api_key', $userData['api_key']);
Registry::get('view')->assign('user_email', $userData['email']);

$promotionId = !empty($_REQUEST['promotion_id']) ? $_REQUEST['promotion_id'] : null;
Registry::get('view')->assign('promotion_id', $promotionId);

$zone = !empty($_REQUEST['zone']) ? $_REQUEST['zone'] : 'catalog';
Registry::get('view')->assign('zone', $zone);
$companyId = Registry::get('runtime.company_id') === 0 ? null : Registry::get('runtime.company_id');
Registry::get('view')->assign('companyId', $companyId);
Registry::get('view')->assign('groups', container()->get('marketplace.user_group.service')->getGroupsToArray());
Registry::get('view')->assign('featureUserGroupEnabled', container()->getParameter('feature.user_groups_enabled'));

// Marketplace mode de taxe : TTC ou HT
$taxMode = fn_is_prices_includes_tax() ? 'TTC' : 'HT';
Registry::get('view')->assign('taxMode', $taxMode);
