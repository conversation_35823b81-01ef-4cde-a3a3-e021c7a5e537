<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

use Symfony\Component\HttpFoundation\RedirectResponse;
use Wizacha\Registry;

// log_unused_code(__FILE__, __LINE__);

if (!defined('BOOTSTRAP')) {
    die('Access denied');
}

/** @var string $mode The Symfony controller */
/** @var string $action The Symfony controller action */

$sfController = ucfirst($mode);
$sfAction = $action;

$container = Registry::defaultInstance()->container;
$router = $container->get('router');
$request = $container->get('request_stack')->getCurrentRequest();
$query = $request->query->all();
unset($query['dispatch']);

/**
 * Ce fichier sert uniquement pour la redirection permanente des anciennes
 * routes du BO en dispatch=bundle.<controller>.<action> vers la roure symfony
 */
return new RedirectResponse($router->generate("admin_{$sfController}_{$sfAction}", $query), 301);
