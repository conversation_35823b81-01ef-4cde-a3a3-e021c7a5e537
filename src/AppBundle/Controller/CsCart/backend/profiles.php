<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON>ey <PERSON>, Ilya M. Shalnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Symfony\Component\Security\Csrf\CsrfToken;
use Tygh\Api;
use Tygh\Registry;
use Wizacha\Marketplace\Country\CountryService;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\User\AddressStatus;
use Wizacha\Marketplace\User\UserSerializer;
use Wizacha\Marketplace\User\UserType;
use Wizacha\Status;

$addressBookService = container()->get('marketplace.user.address_service');
$userService = container()->get('marketplace.user.user_service');
$regexp_guid = '#'.container()->getParameter('regexp_guid').'#';

if (!defined('BOOTSTRAP')) { die('Access denied'); }

if ($_SERVER['REQUEST_METHOD'] == 'POST') {

    $selectedUsersIds = [];
    $nbSelectedUsersIds = 0;
    if (\array_key_exists('selected_profile_ids', $_REQUEST) === true) {
        $selectedUsersIds = \explode(',', $_REQUEST['selected_profile_ids']);
        $nbSelectedUsersIds = \count($selectedUsersIds);
    }

    if ($mode === 'm_delete') {
            foreach ($selectedUsersIds as $v) {
                fn_delete_user($v);
            }

        return array(CONTROLLER_STATUS_OK, "profiles.manage" . (isset($_REQUEST['user_type']) ? "?user_type=" . $_REQUEST['user_type'] : '' ));
    }

    if (($mode == 'export_range') && $nbSelectedUsersIds > 0) {
        if (\array_key_exists('export_ranges', $_SESSION) === true
            && \count($_SESSION['export_ranges']) === 0
        ) {
            $_SESSION['export_ranges'] = array();
        }

        if (\array_key_exists('export_ranges', $_SESSION) === true
            && \array_key_exists('users', $_SESSION['export_ranges']) === true
            && \count($_SESSION['export_ranges']['users']) === 0
        ) {
            $_SESSION['export_ranges']['users'] = array('pattern_id' => 'users');
        }

        $_SESSION['export_ranges']['users']['data'] = array('user_id' => $selectedUsersIds);

        unset($_REQUEST['redirect_url']);

        return array(CONTROLLER_STATUS_REDIRECT, "exim.export?section=users&pattern_id=" . $_SESSION['export_ranges']['users']['pattern_id']);
    }

    //
    // Create/Update user
    //
    if ($mode == 'update' || $mode == 'add') {

        // We check the CSFR token
        if (false === container()->get('security.csrf.token_manager')->isTokenValid(
            new CsrfToken('profile_update', $_REQUEST['csrf_token'] ?? ''))) {

            fn_set_notification('E', __('error'), __('invalid_csrf_token'));
            return [CONTROLLER_STATUS_DENIED];
        }

        fn_restore_processed_user_password($_REQUEST['user_data'], $_POST['user_data']);

        $_auth = NULL;

        $profile_id = !empty($_REQUEST['profile_id']) ? $_REQUEST['profile_id'] : 0;
        $_uid = !empty($profile_id) ? db_get_field("SELECT user_id FROM ?:user_profiles WHERE profile_id = ?i", $profile_id) : $auth['user_id'];
        $user_id = empty($_REQUEST['user_id']) ? (($mode == 'add') ? '' : $_uid) : $_REQUEST['user_id'];

        /** @var string $statusFrom */
        $queryResults = db_get_row("SELECT status, is_locked FROM ?:users WHERE user_id = ?i", $user_id);
        $statusFrom = $queryResults['status'];
        $isLocked = $queryResults['is_locked'];

        $mode = empty($_REQUEST['user_id']) ? 'add' : 'update';
        // TODO: FIXME user_type
        if (Registry::get('runtime.company_id') && $user_id != $auth['user_id']) {
            $_REQUEST['user_data']['user_type'] = !empty($_REQUEST['user_type']) ? $_REQUEST['user_type'] : 'C';
        }

        // Restricted admin cannot change its user type
        if (fn_is_restricted_admin($_REQUEST) && $user_id == $auth['user_id']) {
            $_REQUEST['user_type'] = '';
            $_REQUEST['user_data']['user_type'] = $auth['user_type'];
        }

        // Only admin type user can add admin type to user
        if (false === empty($_REQUEST['user_type'])
            && $_REQUEST['user_data']['user_type'] === UserType::ADMIN
            && $auth['user_type'] !== UserType::ADMIN
        ) {
            throw new SecurityException('A non admin user try to change user type');
        }

        /**
         * Only admin can set the api key.
         */
        if (!empty($_REQUEST['user_data']['api_key']) && (empty($_REQUEST['user_api_status']) || $_REQUEST['user_api_status'] == 'N')) {
            unset($_REQUEST['user_data']['api_key']);
        }

        $user_data = $_REQUEST['user_data'];
        if (strlen($user_data['birthday']) !== 0) {
            if (Registry::get('settings.Appearance.calendar_date_format') === 'month_first') {
                if (($user_data['birthday'] = \DateTime::createFromFormat('m/d/Y', $user_data['birthday'])) instanceof DateTime) {
                    if ($user_data['birthday']->diff(new \DateTime(), true)->y > 150) {
                        fn_set_notification('E', __('error'), __('error_validator_birthday'));
                        $user_data['birthday'] = null;
                    } else {
                        $user_data['birthday'] = $user_data['birthday']->format(UserSerializer::BIRTHDAY_FORMAT);
                    }
                } else {
                    fn_set_notification('E', __('error'), __('error_validator_birthday'));
                    $user_data['birthday'] = null;
                }
            } else {
                if (($user_data['birthday'] = \DateTime::createFromFormat('d/m/Y', $user_data['birthday'])) instanceof DateTime) {
                    if ($user_data['birthday']->diff(new \DateTime(), true)->y > 150) {
                        fn_set_notification('E', __('error'), __('error_validator_birthday'));
                        $user_data['birthday'] = null;
                    } else {
                        $user_data['birthday'] = $user_data['birthday']->format(UserSerializer::BIRTHDAY_FORMAT);
                    }
                } else {
                    fn_set_notification('E', __('error'), __('error_validator_birthday'));
                    $user_data['birthday'] = null;
                }
            }
        }

        if (\array_key_exists('is_professional', $user_data)) {
            $user_data['is_professional'] = 'Y' === $user_data['is_professional'];
        }

        //Edit extra fields
        $user_data['add_extra'] = array_filter($user_data['add_extra'] ?? [], function ($extra_field) {
            return isset($extra_field['name'], $extra_field['value']) && $extra_field['name'] !== '' && $extra_field['value'] !== '';
        });

        if (\array_key_exists('add_extra', $user_data) === true) {
            $existingExtra = $_REQUEST['user_data']['extra'] ?? [];

            $extra_names = \array_column($user_data['add_extra'], 'name');
            $extra_values = \array_column($user_data['add_extra'], 'value');
            $extra_new = \array_combine($extra_names, $extra_values);

            $user_data['extra'] = $existingExtra + $extra_new;
            \array_walk(
                $user_data['extra'],
                static function(&$extraValue): void {
                    if (\is_numeric($extraValue) === true
                        && \strpos($extraValue, '.') === false
                        && \strpos($extraValue, ',') === false
                    ) {
                        $extraValue = \intval($extraValue);
                    }
                }
            );
        }

        if (\array_key_exists('extra_force_delete', $user_data) === true) {
            unset($user_data['extra_force_delete']);
        }

        if (\array_key_exists('extra', $user_data) === true) {
            $user_data['extra'] = \json_encode($user_data['extra']);
        }

        if ($mode === 'update') {
            // Status update
            if ($isLocked === '1' && $statusFrom === 'D') {
                $user_data['status'] = $statusFrom;
                fn_set_notification('E', __('error'), __('account_is_locked_status_cannot_change'));
            } else if(\is_numeric($user_data['company_id']) === true) {
                if (fn_can_update_user_status(
                        new Company($user_data['company_id']),
                        $statusFrom,
                        $user_data['status'])
                ) {
                    fn_update_user_status_and_notify(
                        $user_data['status'],
                        $statusFrom,
                        $_REQUEST['user_id'],
                        $user_data['email'],
                        $_REQUEST
                    );
                } else {
                    $user_data['status'] = $statusFrom;
                    fn_set_notification('E', __('error'), __('single_admin_cannot_be_deleted'));
                }
            }

            $user = container()->get('marketplace.user.user_service')->get($user_id);
            $userLogged = container()->get('marketplace.user.user_service')->get($user_id);
            if ($userLogged->canEditUser($user) === true) {
                $res = fn_update_user($user_id, $user_data, $_auth, !empty($_REQUEST['ship_to_another']), !empty($_REQUEST['notify_customer']));
            } else {
                $res = false;
                fn_set_notification('E', __('error'), __('edit_admin_forbidden'));
            }
        } else {
            $companyData = fn_get_company_data($user_data['company_id']);
            $user_data['company'] = $companyData['company'];
            $res = fn_update_user($user_id, $user_data, $_auth, !empty($_REQUEST['ship_to_another']), !empty($_REQUEST['notify_customer']));
        }

        if ($res) {
            [$user_id, $profile_id] = $res;

            if (!empty($_REQUEST['return_url'])) {
                return array(CONTROLLER_STATUS_OK, $_REQUEST['return_url']);
            }
        } else {
            fn_save_post_data('user_data');
            fn_delete_notification('changes_saved');
        }

        $redirect_params = array(
            'user_id' => $user_id
        );

        if (Registry::get('settings.General.user_multiple_profiles') == 'Y') {
            $redirect_params['profile_id'] = $profile_id;
        }

        if (!empty($_REQUEST['user_type'])) {
            $redirect_params['user_type'] = $_REQUEST['user_type'];
        }

        if (empty($redirect_params['user_type']) && !empty($_REQUEST['user_data']['user_type'])) {
            $redirect_params['user_type'] = $_REQUEST['user_data']['user_type'];
        }

        if (!empty($_REQUEST['return_url'])) {
            $redirect_params['return_url'] = urlencode($_REQUEST['return_url']);
        }

        if (ACCOUNT_TYPE == 'vendor') {
            $redirect_url = 'profiles.update';
            unset($redirect_params['user_id']);
        } else {
            $redirect_url = 'profiles.' . (!empty($user_id) ? "update" : "add");
        }

        unset($redirect_params['user_type']);

        return array(CONTROLLER_STATUS_OK, $redirect_url . '?' . http_build_query($redirect_params));
    }

    if ($mode === 'address_book_update') {
        $addressId = \array_key_exists('address_id', $_REQUEST) === true ? $_REQUEST['address_id'] : '0';
        $user_id = \array_key_exists('user_id', $_REQUEST) === true ? $_REQUEST['user_id'] : 0 ;

        if (\strlen($_REQUEST['address_data']['country']) > 0 && container()->get(CountryService::class)->isValidCountryCode($_REQUEST['address_data']['country']) === false) {
            fn_set_notification('E', __('error'), __('error_country'));
        } else {
            $addressBookService->save($user_id, $_REQUEST['address_data'], \array_key_exists('notify_address_user', $_REQUEST), $addressId);
        }

        return array(CONTROLLER_STATUS_OK, "profiles.update?user_id=" . $user_id . "&selected_section=address_book");
    }
}
if ($mode == 'manage') {

    if (
        Registry::get('runtime.company_id')
        && !empty($_REQUEST['user_type'])
        && (
            $_REQUEST['user_type'] == 'P'
            || (
                $_REQUEST['user_type'] == 'A'
                && !fn_check_permission_manage_profiles('A')
            )
        )
    ) {
        return array(CONTROLLER_STATUS_DENIED);
    }

    [$users, $search] = fn_get_users($_REQUEST, $auth, Registry::get('settings.Appearance.admin_elements_per_page'));

    Registry::get('view')->assign('users', $users);
    Registry::get('view')->assign('search', $search);

    if (!empty($search['user_type'])) {
        Registry::get('view')->assign('user_type_description', fn_get_user_type_description($search['user_type']));
    }

    $user_types = fn_get_user_types();
    if (Registry::get('runtime.company_id')) {
        unset($user_types['C']);
    }
    if (fn_is_restricted_admin(array('user_type' => 'V'))) {
        unset($user_types['V']);
    }

    if (container()->getParameter('feature.approve_user_by_admin')) {
        $items_status = [
            \Wizacha\Status::ENABLED => __('w_allow'),
            \Wizacha\Status::DISABLED => __('disapproved'),
            \Wizacha\Status::PENDING => __('pending'),
        ];
    } else {
        $items_status = [
            \Wizacha\Status::ENABLED => __('enable'),
            \Wizacha\Status::DISABLED => __('disable'),
        ];
    }
    $userLogged = $userService->get($auth['user_id']);
    Registry::get('view')->assign('isAdminSupport', $userLogged->isMarketplaceSupportAdministrator());
    Registry::get('view')->assign('items_status', $items_status);
    Registry::get('view')->assign('user_types', $user_types);
    Registry::get('view')->assign('countries', fn_get_simple_countries(true, (string) GlobalState::interfaceLocale()));
    Registry::get('view')->assign('states', fn_get_all_states());

} elseif ($mode == 'picker') {
    $params = $_REQUEST;
    $params['exclude_user_types'] = array('A', 'V');
    $params['skip_view'] = 'Y';

    [$users, $search] = fn_get_users($params, $auth, Registry::get('settings.Appearance.admin_elements_per_page'));
    Registry::get('view')->assign('users', $users);
    Registry::get('view')->assign('search', $search);

    Registry::get('view')->assign('countries', fn_get_simple_countries(true, (string)GlobalState::interfaceLocale()));
    Registry::get('view')->assign('states', fn_get_all_states());

    Registry::get('view')->display('pickers/users/picker_contents.tpl');
    exit;

} elseif ($mode == 'delete') {

    fn_delete_user($_REQUEST['user_id']);

    return array(CONTROLLER_STATUS_REDIRECT);

} elseif ($mode == 'update_status') {

    $condition = fn_get_company_condition('?:users.company_id');
    $user_data = db_get_row("SELECT * FROM ?:users WHERE user_id = ?i $condition", $_REQUEST['id']);

    if (\is_array($user_data) === true) {
        if ($user_data['is_locked'] === '1' && $user_data['status'] === 'D') {
            fn_set_notification('E', __('error'), __('account_is_locked_status_cannot_change'));
            Registry::get('ajax')->assign('return_status', $user_data['status']);
        } else {
            if (\is_numeric($user_data['company_id']) === true
                && fn_can_update_user_status(
                new Company($user_data['company_id']),
                $user_data['status'],
                $_REQUEST['status'])
            ) {
                fn_update_user_status_and_notify(
                    $_REQUEST['status'],
                    $user_data['status'],
                    $_REQUEST['id'],
                    $user_data['email'],
                    $_REQUEST
                );
            } else {
                fn_set_notification('E', __('error'), __('single_admin_cannot_be_deleted'));
                Registry::get('ajax')->assign('return_status', $user_data['status']);
            }
        }
    }
    exit;
} elseif ($mode == 'update' || $mode == 'add') {
        $user_type = fn_get_request_user_type($_REQUEST);

        $canRenewApiKey = false;
        $params = array();
        if (!empty($_REQUEST['user_id'])) {
            $params[] = "user_id=" . $_REQUEST['user_id'];
        }
        if (\array_key_exists('address_book_id', $_REQUEST) === true && \preg_match($regexp_guid, $_REQUEST['address_book_id']) && \strlen($_REQUEST['address_book_id']) === 36) {
            $params[] = "address_book_id=" . $_REQUEST['address_book_id'];
        }
    if ($mode == 'add') {
        if (Registry::get('runtime.company_id')) {
            if (empty($_REQUEST['user_type'])) {
                return array(CONTROLLER_STATUS_REDIRECT, 'profiles.add?user_type=' . fn_get_request_user_type($_REQUEST));
            } elseif ($_REQUEST['user_type'] == 'C') {
                return array(CONTROLLER_STATUS_DENIED);
            } elseif ($_REQUEST['user_type'] == 'A') {
                $_GET['user_type'] = 'V';

                return array(CONTROLLER_STATUS_REDIRECT, 'profiles.add?' . http_build_query($_GET));
            }
        }
    } else {
        if (Registry::get('runtime.company_id') && !empty($_REQUEST['user_id']) && $_REQUEST['user_id'] != $auth['user_id']) {
            if (empty($_REQUEST['user_type'])) {
                $_GET['user_type'] = fn_get_request_user_type($_REQUEST);

                return array(CONTROLLER_STATUS_REDIRECT, 'profiles.update?' . http_build_query($_GET));
            } elseif ($_REQUEST['user_type'] == 'A') {
                $_GET['user_type'] = 'V';

                return array(CONTROLLER_STATUS_REDIRECT, 'profiles.update?' . http_build_query($_GET));
            }
        }
    }

    if (
        Registry::get('runtime.company_id')
        && !empty($_REQUEST['user_type'])
        && (
            $_REQUEST['user_type'] == 'P'
            || (
                $_REQUEST['user_type'] == 'A'
                && !fn_check_permission_manage_profiles('A')
            )
        )
    ) {
        return array(CONTROLLER_STATUS_DENIED);
    }

    if (!empty($_REQUEST['user_id']) && !empty($_REQUEST['user_type'])) {
        if ($_REQUEST['user_id'] == $auth['user_id'] && defined('RESTRICTED_ADMIN') && !in_array($_REQUEST['user_type'], array('A', ''))) {
            return array(CONTROLLER_STATUS_REDIRECT, "profiles.update?user_id=" . $_REQUEST['user_id']);
        }
    }

    if (fn_is_restricted_admin($_REQUEST) == true) {
        return array(CONTROLLER_STATUS_DENIED);
    }

    // copy to add below this line
    $profile_id = !empty($_REQUEST['profile_id']) ? $_REQUEST['profile_id'] : 0;
    $_uid = !empty($profile_id) ? db_get_field("SELECT user_id FROM ?:user_profiles WHERE profile_id = ?i", $profile_id) : $auth['user_id'];
    $user_id = empty($_REQUEST['user_id']) ? (($mode == 'add') ? '' : $_uid) : $_REQUEST['user_id'];

    if (!empty($_REQUEST['profile']) && $_REQUEST['profile'] == 'new') {
        $user_data = fn_get_user_info($user_id, false);
    } else {
        $user_data = fn_get_user_info($user_id, true, $profile_id);
    }

    $saved_user_data = fn_restore_post_data('user_data');
    if (!empty($saved_user_data)) {
        $user_data = fn_array_merge($user_data, $saved_user_data);
    }

    if ($mode == 'update') {
        if (empty($user_data)) {
            return array(CONTROLLER_STATUS_NO_PAGE);
        }
    }

    $user_data['user_id'] = empty($user_data['user_id']) ? (!empty($user_id) ? $user_id : 0) : $user_data['user_id'];
    $user_data['is_professional'] = '1' === $user_data['is_professional'] ? 'Y' : 'N';
    $user_type = array_key_exists('user_type', $user_data) === true ? $user_data['user_type'] : $_REQUEST['user_type'];
    $user_data['user_type'] = array_key_exists('user_type', $user_data) === true  ? $user_data['user_type'] : 'C' ;

    $auth['is_root'] = isset($auth['is_root']) ? $auth['is_root'] : '';

    $navigation = array(
        'general' => array(
            'title' => __('general'),
            'js' => true
        ),
        "address_book" => array (
            'title' => __('address_book'),
            'js' => true
        ),
        'addons' => array (
            'title' => __('addons'),
            'js' => true
        ),
        'extra_fields' => array (
            'title' => __('extra_info'),
            'js' => true
        )
    );

    if ($user_id) {
        $user = $userService->get($user_id);
        $showOrganisationTab = $user->belongsToAnOrganisation() && $user->getOrganisation()->isAdministrator($user);
        $userLogged = $userService->get($auth['user_id']);

        if ($auth['user_id'] === $user_data['user_id']) {
            $canRenewApiKey = true;
        }

        Registry::get('view')->assign('showOrganisationTab', $showOrganisationTab);
        Registry::get('view')->assign('canEditAccount', $userLogged->canEditUser($user));
        Registry::get('view')->assign('notify_user_update', container()->getParameter('feature.notify_user_update') === true);
        Registry::get('view')->assign('mode', $mode);
        Registry::get('view')->assign('address_book_data', $userService->get($user_id)->getAddressBook());

        if ($showOrganisationTab) {
            $navigation['organisation'] = [
                'title' => __('organisation'),
                'js' => true,
            ];

            $identityCard = container()->get('router')->generate('admin_organisation_admin_identity_card',
                ['userId' => $user->getUserId()]);
            $proofOfAppointment = container()->get('router')->generate('admin_organisation_admin_proof_of_appointment',
                ['userId' => $user->getUserId()]);

            Registry::get('view')->assign('user', $user);
            Registry::get('view')->assign('identityCard', $identityCard);
            Registry::get('view')->assign('proofOfAppointment', $proofOfAppointment);
        }
    }

    if (empty($user_data['api_key'])) {
        Registry::get('view')->assign('new_api_key', Api::generateKey());
    }

    /**
     * Only admin can set the api key.
     * Wizacha : all vendors can set api key
     */
    if (fn_check_user_type_admin_area($user_data)
        && !empty($user_data['user_id'])
        && ($user_id === $auth['user_id']
            || $userLogged->canEditUser($user) === true)
    ) {
        $navigation['api'] = array (
            'title' => __('api_access'),
            'js' => true
        );

        Registry::get('view')->assign('show_api_tab', true);

    }

    Registry::set('navigation.tabs', $navigation);
    if ($user_data['birthday'] instanceof DateTime === true) {
        if(Registry::get('settings.Appearance.calendar_date_format') == 'month_first'){
            $user_data['birthday'] = $user_data['birthday']->format('m/d/Y');
        }else{
            $user_data['birthday'] = $user_data['birthday']->format('d/m/Y');
        }
    }
    if (\array_key_exists('extra', $user_data) === true) {
        $user_data['extra'] = \json_decode($user_data['extra'], true);
    }
    $profile_fields = fn_get_profile_fields($user_type);

    if(\array_key_exists('address_book_id', $_REQUEST) && \strlen($_REQUEST["address_book_id"]) > 0) {
        Registry::get('view')->assign('address_book_id', $_REQUEST["address_book_id"]);
    }
    Registry::get('view')->assign('user_type', $user_type);
    Registry::get('view')->assign('profile_fields', $profile_fields);
    Registry::get('view')->assign('mode', $mode);
    Registry::get('view')->assign('user_data', $user_data);
    Registry::get('view')->assign('canRenewApiKey', $canRenewApiKey);
    Registry::get('view')->assign('ship_to_another', fn_check_shipping_billing($user_data, $profile_fields));
    if (Registry::get('settings.General.user_multiple_profiles') == 'Y' && !empty($user_id)) {
        Registry::get('view')->assign('user_profiles', fn_get_user_profiles($user_id));
    }
    Registry::get('view')->assign('nationalities', container()->get(CountryService::class)->getAll((string) GlobalState::interfaceLocale(), Status::ENABLED()));
    Registry::get('view')->assign('countries', fn_get_simple_countries(true, (string)GlobalState::interfaceLocale()));
    Registry::get('view')->assign('states', fn_get_all_states());

} elseif ($mode == 'delete_profile') {

    if (fn_is_restricted_admin($_REQUEST)) {
        return array(CONTROLLER_STATUS_DENIED);
    }

    $user_id = empty($_REQUEST['user_id']) ? $auth['user_id'] : $_REQUEST['user_id'];

    fn_delete_user_profile($user_id, $_REQUEST['profile_id']);

    return array(CONTROLLER_STATUS_OK, "profiles.update?user_id=" . $user_id);

} elseif ($mode == 'address_book_update' ) {
    $addressId = \array_key_exists('address_id', $_REQUEST) === true ? $_REQUEST['address_id'] : null;
    $userId = \array_key_exists('user_id', $_REQUEST) === true ? $_REQUEST['user_id'] : null;

    if (\preg_match($regexp_guid, $addressId) && \strlen($addressId) === 36) {

        $user = $userService->get($userId);
        $billingAddressId = $user->getBillingAddress()->getFieldValue('address_id');
        $shippingAddressId = $user->getShippingAddress()->getFieldValue('address_id');

        if ($shippingAddressId === $addressId && $billingAddressId === $addressId) {
            $principalAddress = AddressStatus::BILLING_SHIPPING()->getValue();
        } elseif ($shippingAddressId === $addressId) {
            $principalAddress = AddressStatus::SHIPPING()->getValue();
        } elseif ($billingAddressId === $addressId) {
            $principalAddress = AddressStatus::BILLING()->getValue();
        } else {
            $principalAddress = AddressStatus::NON()->getValue();
        }

        Registry::get('view')->assign('address', $addressBookService->get($addressId));
        Registry::get('view')->assign('principalAddress', $principalAddress);
    }

    Registry::get('view')->assign('user_id', $userId);
    Registry::get('view')->assign('availableOffers', container()->getParameter('feature.available_offers'));
    Registry::get('view')->assign('countries', fn_get_simple_countries(true, (string) GlobalState::interfaceLocale()));

} elseif ($mode === 'b_address_book_update') {
    $user_id = empty($_REQUEST['user_id']) ? $auth['user_id'] : $_REQUEST['user_id'];

    $addressBookService->save($user_id, $_REQUEST['user_data'], false, '0', AddressStatus::BILLING());

    return array(CONTROLLER_STATUS_OK, "profiles.update?user_id=" . $user_id);

} elseif ($mode === 's_address_book_update') {
    $user_id = empty($_REQUEST['user_id']) ? $auth['user_id'] : $_REQUEST['user_id'];

    $addressBookService->save($user_id, $_REQUEST['user_data'], false, '0', AddressStatus::SHIPPING());

    return array(CONTROLLER_STATUS_OK, "profiles.update?user_id=" . $user_id);

} elseif ($mode === 'address_book_delete') {

    $userId = \array_key_exists('user_id', $_REQUEST) === true ? $_REQUEST['user_id'] : 0 ;
    if (\array_key_exists('address_ids', $_REQUEST) === true) {

        foreach ($_REQUEST['address_ids'] as $id) {
            $addressBookService->delete($userId, $id);
        }
    }
    fn_set_notification('N', __('notice'), __('text_address_book_have_been_deleted'));

    return array(CONTROLLER_STATUS_OK, "profiles.update?user_id=" . $userId . "&selected_section=address_book");
} elseif ($mode === 'reNewApiKey') {
    $user_id = $_REQUEST['user_id'];
    if ($user_id === $auth['user_id']) {
        $userService->createApiKey((int) $user_id);
    } else {
        fn_set_notification('E', __('error'), __('invalid_renew_token'));
    }

    return array(CONTROLLER_STATUS_OK, "profiles.update?user_id=" . $user_id);

}
