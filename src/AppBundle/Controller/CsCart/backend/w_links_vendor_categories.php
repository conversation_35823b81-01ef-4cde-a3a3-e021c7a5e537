<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

if (!defined('BOOTSTRAP')) {
    die('Access denied');
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if ('update' == $mode) {
        fn_trusted_vars('links_data');
        fn_w_update_links_vendor_categories($_REQUEST['links_data']);
    }
    return;
}

if ('update' == $mode) {
    $item = $_REQUEST['page']?:1;
    $items_per_page = $_REQUEST['items_per_page']?:10;

    $links = \Wizacha\Exim\VendorCategory::getLinks(
        \Tygh\Registry::get('runtime.company_id'),
        $items_per_page*($item-1),
        $items_per_page
    );

    $search = [
        'item' => $item,
        'total_items' => \Wizacha\Exim\VendorCategory::countLinks(\Tygh\Registry::get('runtime.company_id')),
        'items_per_page' => $items_per_page,
        'page' => $item,
    ];

    \Tygh\Registry::get('view')->assign('search', $search);
    \Tygh\Registry::get('view')->assign('links', $links);
} elseif ('delete' == $mode) {
    \Wizacha\Exim\VendorCategory::delete(
        \Tygh\Registry::get('runtime.company_id'),
        $_REQUEST['w_link_id']
    );
}
