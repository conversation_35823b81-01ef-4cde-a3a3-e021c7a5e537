<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

if (!defined('BOOTSTRAP')
    || false === container()->get('marketplace.user.user_service')->get($auth['user_id'])->isMarketplaceSupportAdministrator()
) {
    (
    new Response('Forbidden', 403)
    )
        ->send();
}

if ($mode == 'manage') {
    (new JsonResponse(
        container()
            ->getParameterBag()
            ->all()
    ))
        ->send();
}
