<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON>ey <PERSON>, <PERSON>ya <PERSON>halnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\Http;
use Tygh\Registry;
use Wizacha\Marketplace\GlobalState\GlobalState;
use \Wizacha\Marketplace\Payment\PaymentService;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

fn_trusted_vars("processor_params", "payment_data");

if ($_SERVER['REQUEST_METHOD'] == 'POST') {

    //
    // Update payment method
    //
    if ($mode == 'update') {

        $certificate_file = fn_filter_uploaded_data('payment_certificate');
        $certificates_dir = Registry::get('config.dir.certificates');
        $file = array();

        if ($certificate_file) {

            if (!empty($_REQUEST['payment_data']['processor_params']['certificate_filename'])) {
                fn_rm($certificates_dir . $_REQUEST['payment_data']['processor_params']['certificate_filename']);
            }

            $file = reset($certificate_file);
            $filename = $_REQUEST['payment_id'] . '/' . $file['name'];

            fn_mkdir($certificates_dir . $_REQUEST['payment_id']);
            fn_copy($file['path'], $certificates_dir . $filename);
            $_REQUEST['payment_data']['processor_params']['certificate_filename'] = $filename;
        }

        if ($_REQUEST['payment_id']) {
            $old_params = fn_get_processor_data($_REQUEST['payment_id']);

            // Cannot change processor. Override potential input
            $_REQUEST['payment_data']['processor_id'] = $old_params['processor_id'];

            if (empty($_REQUEST['payment_data']['processor_params']['certificate_filename'])) {
                $_REQUEST['payment_data']['processor_params']['certificate_filename'] = $old_params['processor_params']['certificate_filename'];
            }

            if ($_REQUEST['payment_data']['processor_params']['certificate_filename'] != $old_params['processor_params']['certificate_filename']) {
                if ($old_params['processor_params']['certificate_filename']) {
                    fn_rm($certificates_dir . $old_params['processor_params']['certificate_filename']);
                }
            }

            if (!file_exists($certificates_dir . $_REQUEST['payment_data']['processor_params']['certificate_filename'])) {
                $_REQUEST['payment_data']['processor_params']['certificate_filename'] = '';
            }
        }

        $payment_id = fn_update_payment($_REQUEST['payment_data'], $_REQUEST['payment_id']);

        if (!$_REQUEST['payment_id'] && $file) {
            fn_mkdir($certificates_dir . $payment_id);
            $filename = $payment_id . '/' . $file['name'];
            fn_copy($certificates_dir . $_REQUEST['payment_data']['processor_params']['certificate_filename'], $certificates_dir . $filename);
            $_REQUEST['payment_data']['processor_params']['certificate_filename'] = $filename;

            fn_rm($certificates_dir . $_REQUEST['payment_id']);
            fn_update_payment($_REQUEST['payment_data'], $payment_id);
        }
    }

    return array(CONTROLLER_STATUS_OK, "payments.manage");
}

if ($mode == 'delete_certificate') {
    if (!empty($_REQUEST['payment_id'])) {
        $payment_data = fn_get_payment_method_data($_REQUEST['payment_id']);

        if ($payment_data['processor_params']['certificate_filename']) {
            fn_rm(Registry::get('config.dir.certificates') . $payment_data['processor_params']['certificate_filename']);
            $payment_data['processor_params']['certificate_filename'] = '';

            fn_update_payment($payment_data, $_REQUEST['payment_id']);
        }
    }

    return array(CONTROLLER_STATUS_REDIRECT, 'payments.processor?payment_id=' . $_REQUEST['payment_id']);
}

// If any method is selected - show it's settings
if ($mode == 'processor') {
    $processor_data = fn_get_processor_data($_REQUEST['payment_id']);

    // We're selecting new processor
    if (!empty($_REQUEST['processor_id']) && (empty($processor_data['processor_id']) || $processor_data['processor_id'] != $_REQUEST['processor_id'])) {
        $processor_data = db_get_row("SELECT * FROM ?:payment_processors WHERE processor_id = ?i", $_REQUEST['processor_id']);
        $processor_data['processor_params'] = array();
        $processor_data['currencies'] = (!empty($processor_data['currencies'])) ? explode(',', $processor_data['currencies']) : array();
    }

    if (!empty($processor_data) && $processor_data['callback'] == "Y") {
        Registry::get('view')->assign('curl_info', Http::getCurlInfo($processor_data['processor']));
    }

    if (!empty($processor_data['processor_params']['certificate_filename'])) {
        $processor_data['processor_params']['certificate_filename'] = preg_replace('#^\d+/#', '', $processor_data['processor_params']['certificate_filename']);
    }

    Registry::get('view')->assign('processor_template', $processor_data['admin_template']);
    Registry::get('view')->assign('processor_params', $processor_data['processor_params']);
    Registry::get('view')->assign('processor_name', $processor_data['processor']);
    Registry::get('view')->assign('callback', $processor_data['callback']);
    Registry::get('view')->assign('payment_id', $_REQUEST['payment_id']);

// Show methods list
} elseif ($mode == 'manage') {

    $payments = fn_get_payments((string) GlobalState::contentLocale());

    Registry::get('view')->assign('payments', $payments);
    Registry::get('view')->assign('payment_processors', fn_get_payment_processors());
    Registry::get('view')->assign('order_acceptation_delay', PaymentService::getAcceptationDelay());

} elseif ($mode == 'update') {
    $payment = fn_get_payment_method_data($_REQUEST['payment_id'], (string) GlobalState::contentLocale());
    $paymentPlaceholder = fn_get_payment_method_data($_REQUEST['payment_id'], (string) GlobalState::interfaceLocale(), true);
    $payment['icon'] = fn_get_image_pairs($payment['payment_id'], 'payment', 'M', true, true, (string) GlobalState::contentLocale());

    Registry::get('view')->assign('payment', $payment);
    Registry::get('view')->assign('paymentPlaceholder', $paymentPlaceholder);
    Registry::get('view')->assign('payment_processors', fn_get_payment_processors());
    Registry::get('view')->assign('taxes', fn_get_taxes());
    Registry::get('view')->assign('order_acceptation_delay', PaymentService::getAcceptationDelay());

    if (Registry::get('runtime.company_id') && Registry::get('runtime.company_id') != $payment['company_id']) {
        Registry::get('view')->assign('hide_for_vendor', true);
    }

} elseif ($mode == 'delete') {
    if (!empty($_REQUEST['payment_id'])) {
        $result = fn_delete_payment($_REQUEST['payment_id']);

        if ($result) {
            fn_set_notification('N', __('notice'), __('text_payment_have_been_deleted'));
        } else {
            fn_set_notification('W', __('warning'), __('text_payment_have_not_been_deleted'));
        }

        $count = db_get_field("SELECT COUNT(*) FROM ?:payments");
        if (empty($count)) {
            Registry::get('view')->display('views/payments/manage.tpl');
        }

        fn_rm(Registry::get('config.dir.certificates') . $_REQUEST['payment_id']);
    }
    exit;
}

function fn_get_payment_processors($lang_code = null)
{
    return db_get_hash_array("SELECT a.processor_id, a.processor, a.type, b.value as description FROM ?:payment_processors as a LEFT JOIN ?:language_values as b ON b.name = CONCAT('processor_description_', REPLACE(a.processor_script, '.php', '')) AND lang_code = ?s ORDER BY processor", 'processor_id', $lang_code ?? (string) GlobalState::interfaceLocale());
}
