<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, Ilya M<PERSON> Shalnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use MangoPay\Libraries\ResponseException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Tygh\Registry;
use Wizacha\Company;
use Wizacha\Events\Config;
use Wizacha\Events\IterableEvent;
use Wizacha\Marketplace\Commission\Commission;
use Wizacha\Marketplace\Company\CompanyEvents;
use Wizacha\Marketplace\Company\Event\CompanyApplied;
use Wizacha\Marketplace\Company\Event\CompanyLegalDocumentsUpdated;
use Wizacha\Marketplace\Division\Service\DivisionService;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Payment\LemonWay\LemonWayConfig;
use Wizacha\Marketplace\Payment\Exception\PayoutException;
use Wizacha\Marketplace\Payment\Processor\Exception\NoValidProcessorException;
use Wizacha\Marketplace\User\Password;
use Wizacha\Money\Money;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

const COMPANIES_MANAGER_LINK = 'companies.manage';
$container = container();
$divisionService = $container->get('marketplace.division.service');
$divisionSettingsService = $container->get('marketplace.divisions_settings.service');
$payoutService = $container->get('marketplace.payment.payout_service');
$companyService = $container->get('marketplace.company_service');
$invoicingSettingsService = $container->get('marketplace.invoicing_settings_service');

if ($_SERVER['REQUEST_METHOD'] == 'POST') {

    //
    // Before adding a Company, check SIRET and legal_status
    //
    if (!Tygh\Registry::get('runtime.company_id') && ($mode == 'update' || $mode == 'add')) {
        $error = false;

        if (empty($_REQUEST['company_id']) || Wizacha\Company::isProfessional(fn_get_company_data($_REQUEST['company_id']))) {
            if (!preg_match('/^[a-zA-Z]/', $_REQUEST['company_data']['w_legal_status'])) {
                $error = true;
                fn_set_notification('E', __('error'), __('error_w_legal_status_not_valid'));
            }
        }

        if ($error) {
            $suffix = ".$mode";
            fn_save_post_data('company_data', 'update'); // company data and settings

            if (isset($_REQUEST['company_id'])) {
                $suffix .= "?company_id=".$_REQUEST['company_id'];
            }

            return array(CONTROLLER_STATUS_REDIRECT, "companies$suffix");
        }
    }

    $suffix = '';

    // Define trusted variables that shouldn't be stripped
    fn_trusted_vars(
        'company_data'
    );

    foreach (['latitude', 'longitude'] as $coordinates) {
        if (isset($_REQUEST['company_data'][$coordinates]) && $_REQUEST['company_data'][$coordinates] === "") {
            $_REQUEST['company_data']['longitude'] = null;
            $_REQUEST['company_data']['latitude'] = null;
            break;
        }
    }

    if (!empty($_REQUEST['company_data']['extra'])) {
        $originalInput = $_REQUEST['company_data']['extra'];
        $_REQUEST['company_data']['extra'] = [];
        foreach ($originalInput as $k => $v) {
            $kk = urldecode($k);
            $vv = html_entity_decode($v);

            $_REQUEST['company_data']['extra'][$kk] = $vv;
        }
    }

    if (!empty($_REQUEST['company_data']['add_extra'])) {
        foreach ($_REQUEST['company_data']['add_extra'] as &$v) {
            $v['name'] = urldecode($v['name']);
            $v['value'] = html_entity_decode($v['value']);
        }
    }

    // Format divisions data
    if (true === $container->getParameter('feature.available_offers')
        && \array_key_exists('division-loaded', $_REQUEST)
        && "true" === $_REQUEST['division-loaded']
    ) {
        // Manage inherit-divisions checkbox
        if (\array_key_exists('included', $_REQUEST)
            && \is_array($_REQUEST['included'])
        ) {
            $includedDivisions = $_REQUEST['included'];
        } elseif (\array_key_exists('inherit-divisions', $_REQUEST)
            && 'on' === $_REQUEST['inherit-divisions']
        ) {
            $includedDivisions = [DivisionService::ROOT_CODE];
        } else {
            $includedDivisions = [];
        }

        $_REQUEST['company_data']['divisions'] = [
            'included' => $includedDivisions,
            'excluded' => \is_array($_REQUEST['excluded']) ? $_REQUEST['excluded'] : [],
        ];
    }

    //
    // Processing additon of new company
    //
    if ($mode == 'add') {

        $suffix = '.add';

        if (!empty($_REQUEST['company_data']['company'])) {  // Checking for required fields for new company

            if (Registry::get('runtime.simple_ultimate')) {
                Registry::set('runtime.simple_ultimate', false);
            }

            if (!empty($_REQUEST['company_data']['admin_username']) && db_get_field("SELECT COUNT(*) FROM ?:users WHERE user_login = ?s", $_REQUEST['company_data']['admin_username']) > 0) {
                fn_set_notification('E', __('error'), __('error_admin_not_created_name_already_used'));
                fn_save_post_data('company_data', 'update'); // company data and settings
                $suffix = '.add';
            } else {
                $_REQUEST['company_data']['invoicing_disabled'] = \array_key_exists('invoicing_disabled', $_REQUEST['company_data']) === true;
                //if MP invoicing is disabled company invoicing must be disabled
                if ($invoicingSettingsService->getMarketplaceInvoicingDisplayed() === true) {
                    $_REQUEST['company_data']['invoicing_disabled'] = true;
                }

                // Adding company record
                $company_id = fn_update_company($_REQUEST['company_data']);

                if (!empty($company_id)) {
                    $suffix = ".update?company_id=$company_id";

                    // Save images
                    fn_attach_image_pairs('company_main', 'company', $company_id, (string) GlobalState::contentLocale());

                    if (db_get_field("SELECT COUNT(*) FROM ?:users WHERE email = ?s", $_REQUEST['company_data']['email']) > 0) {
                        fn_set_notification('E', __('error'), __('error_admin_not_created_email_already_used'));
                    } else {

                        // Add company's administrator
                        if (fn_is_restricted_admin($_REQUEST) == true) {
                            return array(CONTROLLER_STATUS_DENIED);
                        }

                        $user_data['fields'] = [];
                        if (isset($_REQUEST['user_data']['fields']) && is_array($_REQUEST['user_data']['fields'])) {
                            $user_data['fields'] = $_REQUEST['user_data']['fields'];
                        }

                        if (!empty($_REQUEST['company_data']['admin_username'])) {
                            $user_data['user_login'] = $_REQUEST['company_data']['admin_username'];
                        } else {
                            $user_data['user_login'] = $_REQUEST['company_data']['email'];
                        }

                        $user_data['user_type'] = 'V';
                        $user_data['password1'] = Password::generatePassword();
                        $user_data['password2'] = $user_data['password1'];
                        $user_data['status'] = $_REQUEST['company_data']['status'];
                        $user_data['company_id'] = $company_id;
                        $user_data['email'] = $_REQUEST['company_data']['email'];
                        $user_data['company'] = $_REQUEST['company_data']['company'];
                        $user_data['last_login'] = 0;
                        $user_data['lang_code'] = $_REQUEST['company_data']['lang_code'];
                        $user_data['password_change_timestamp'] = 0;
                        $user_data['is_root'] = 'N';

                        // Copy vendor admin billing and shipping addresses from the company's credentials
                        $user_data['firstname'] = $user_data['b_firstname'] = $user_data['s_firstname'] = $_REQUEST['company_data']['legal_representative_firstname'] ?? '';
                        $user_data['lastname'] = $user_data['b_lastname'] = $user_data['s_lastname'] = $_REQUEST['company_data']['legal_representative_lastname'] ?? '';

                        // Create new user, avoiding switching to the vendor admin's session ($null as the 3rd argument)
                        list($added_user_id, $null) = fn_update_user(
                            0,
                            $user_data,
                            $null,
                            false,
                            array_key_exists('notify_admin', $_REQUEST),
                            true
                        );

                        if ($added_user_id) {
                            fn_set_request_user_id($company_id, $added_user_id);

                            $msg = __('new_administrator_account_created') . '<a href="' . fn_url('profiles.update?user_id=' . $added_user_id) . '">' . __('you_can_edit_account_details') . '</a>';
                            fn_set_notification('N', __('notice'), $msg, 'K');
                        }

                        if ($_REQUEST['company_data']['status'] === Company::STATUS_ENABLED) {
                            Config::dispatch(
                                CompanyEvents::SEND_TO_PSP,
                                IterableEvent::fromElement($company_id)
                            );
                        }

                        // Notifications mail
                        $event = new CompanyApplied(
                            fn_get_company_data($company_id),
                            $_REQUEST['company_data']['legal_representative_firstname'],
                            $_REQUEST['company_data']['legal_representative_lastname'],
                            ''
                        );
                        $container->get('event_dispatcher')->dispatch($event, CompanyApplied::class);
                    }
                } else {
                    fn_save_post_data('company_data', 'update');
                }
            }
        }
    }

    //
    // Processing updating of company element
    //
    if ($mode == 'update') {
        $company_id = (int) $_REQUEST['company_id'];

        if (!empty($_REQUEST['company_data']['company'])) {
            if (!empty($_REQUEST['company_id']) && Registry::get('runtime.company_id') && Registry::get('runtime.company_id') != $_REQUEST['company_id']) {
                fn_company_access_denied_notification();
                fn_save_post_data('company_data', 'update');
            } else {
                // Manage automatic billing number settings
                list($orders) = fn_get_orders(['company_id' => $_REQUEST['company_id']]);
                $hasOrders = count($orders) > 0;

                $hasAutomaticBillingNumber = (bool) ($_REQUEST['has_automatic_billing_number'] ?? false);
                $hasAutomaticRmaNumber = (bool) ($_REQUEST['has_automatic_rma_number'] ?? false);

                // s'il n'y a pas de commande, on autorise le changement de numéro initial de facture
                if (!$hasOrders) {
                    $_REQUEST['company_data']['initial_billing_number'] = $hasAutomaticBillingNumber ? $_REQUEST['company_data']['initial_billing_number'] : null;
                    $_REQUEST['company_data']['initial_rma_number'] = $hasAutomaticRmaNumber ? $_REQUEST['company_data']['initial_rma_number'] : null;
                }

                // s'il y a des commandes passées, on autorise uniquement la désactivation de la génération automatique
                if ($hasOrders) {
                    if (!$hasAutomaticBillingNumber) {
                        $_REQUEST['company_data']['initial_billing_number'] = null;
                    }

                    if (!$hasAutomaticRmaNumber) {
                        $_REQUEST['company_data']['initial_rma_number'] = null;
                    }
                }

                $vendorCanEditInvoicing = $companyService->canEditInvoicingDisplayed($company_id, ACCOUNT_TYPE === 'admin')  === true
                    && $invoicingSettingsService->getMarketplaceInvoicingDisplayed() === false;

                if (ACCOUNT_TYPE === 'admin' || $vendorCanEditInvoicing === true) {
                    $_REQUEST['company_data']['invoicing_disabled'] = \array_key_exists('invoicing_disabled', $_REQUEST['company_data']) === true;
                }

                // Updating company record
                try {
                    fn_update_company($_REQUEST['company_data'], $_REQUEST['company_id'], (string) GlobalState::contentLocale());
                } catch (ResponseException $exception) {
                    fn_set_notification(
                        'E',
                        __('error'),
                        implode(
                            '<br/>',
                            [
                                'MangoPay:',
                                $exception->GetErrorDetails(),
                            ]
                        )
                    );
                }

                // If the commissions fields are not send, we don't need to do anything about them
                if (true === array_key_exists('commission_percent', $_REQUEST['company_data'])) {

                    // Save company commissions
                    $commissionService = $container->get('marketplace.commission.commission_service');
                    $companyCommission = $commissionService->getCompanyCommission($_REQUEST['company_id']);

                    if (false === $companyCommission instanceof Commission) {
                        $companyCommission = new Commission('', $_REQUEST['company_id'], null,0, 0, null);
                    }

                    if ($_REQUEST['company_data']['commission_percent'] === ''
                        && $_REQUEST['company_data']['commission_fixed'] === ''
                        && $_REQUEST['company_data']['commission_maximum'] === ''
                    ) {
                        if (\strlen($companyCommission->getId()) > 0) {
                            $commissionService->deleteCommission($companyCommission->getId());
                        }
                    } else {
                        $companyCommission
                            ->setPercentAmount((float)$_REQUEST['company_data']['commission_percent'])
                            ->setFixAmount((float)$_REQUEST['company_data']['commission_fixed'])
                            ->setMaximumAmount(
                                $_REQUEST['company_data']['commission_maximum'] !== ""
                                    ? (float)$_REQUEST['company_data']['commission_maximum']
                                    : null
                            )
                        ;
                        $commissionService->saveCommission($companyCommission);
                    }

                    $commissionDataList = $_REQUEST['company_data']['commission_data'] ?? [];
                    $commissionCategoriesIds = array_column($commissionDataList, 'category_id');

                    // If a category appears more than once, show an error
                    if (count($commissionCategoriesIds) !== count(array_unique($commissionCategoriesIds))) {
                        fn_set_notification('E', __('error'), __('commission_category_duplicate'));
                    } else {
                        foreach ($commissionDataList as $commissionData) {
                            if (!empty($commissionData['id'])) {
                                try {
                                    $commission = $commissionService->getCommissionById($commissionData['id']);
                                } catch (NotFound $e) {
                                    $container->get('logger')->warning($e->getMessage());
                                    break;
                                }
                                $commission->setCategoryId((int) $commissionData['category_id']);
                                $commission->setPercentAmount((float) $commissionData['percent_amount']);
                                $commission->setFixAmount((float) $commissionData['fix_amount']);
                                $commission->setMaximumAmount(
                                    $commissionData['maximum_amount'] !== "0"
                                    && $commissionData['maximum_amount'] !== ""
                                        ? (float) $commissionData['maximum_amount']
                                        : null
                                );
                                $commissionService->saveCommission($commission);
                            } elseif (!empty($commissionData['category_id'])) {
                                $commission = new Commission(
                                    '',
                                    $_REQUEST['company_id'],
                                    \intval($commissionData['category_id']),
                                    \floatval($commissionData['percent_amount']),
                                    \floatval($commissionData['fix_amount']),
                                    $commissionData['maximum_amount'] ? \floatval($commissionData['maximum_amount']) : null
                                );
                                $commissionService->saveCommission($commission);
                            }
                        }
                    }
                }

                // Save images
                fn_attach_image_pairs('company_main', 'company', $_REQUEST['company_id'], (string) GlobalState::contentLocale());
            }
        }

        $suffix = ".update?company_id=$company_id";
    }

    // Upload legal documents
    if (($mode == 'update' || $mode == 'add') && $company_id && !Registry::get('runtime.company_id')) {
        $files = fn_filter_uploaded_data('company_legal_documents');

        foreach ($files as $key => $file) {
            if (!fn_w_check_apply_for_vendor_files($file)) {
                fn_set_notification(
                    'E',
                    __('error'),
                    __(
                        'text_forbidden_uploaded_file_extension',
                        [
                            '[ext]'=>strtolower(fn_get_file_ext($file['name'])),
                            '[exts]'=>implode(',', fn_w_get_vendor_files_allowed_extensions())
                        ]
                    )
                );

                continue;
            }

            $filename = $key.'_'.$company_id.'_'.basename($file['name']);

            $vendorSubscriptionStorageService = container()->get('Wizacha\Storage\VendorSubscriptionStorageService');

            // Remove old file of this type before uploading the new one
            foreach ($vendorSubscriptionStorageService->getList($company_id . '/') as $existingFile) {
                if (strpos($existingFile, $key) === 0) {
                    $vendorSubscriptionStorageService->delete($company_id . '/'.$existingFile);
                }
            }

            $vendorSubscriptionStorageService->put($company_id.'/'.$filename, ['file' => $file['path']]);
        }

        if (!empty($files)) {
            $container->get('event_dispatcher')->dispatch(
                new CompanyLegalDocumentsUpdated($company_id, array_keys($files)),
                \Wizacha\Marketplace\Company\CompanyEvents::LEGAL_DOCUMENTS_UPDATED
            );
        }
    }

    $selectedCompaniesIds = [];
    $nbSelectedCompaniesIds = 0;
    if (\array_key_exists('selected_companies_ids', $_REQUEST) === true) {
        $selectedCompaniesIds = \explode(',', $_REQUEST['selected_companies_ids']);
        $nbSelectedCompaniesIds = \count($selectedCompaniesIds);
    }

    if ($mode == 'm_delete') {
        if ($nbSelectedCompaniesIds > 0) {
            foreach ($selectedCompaniesIds as $companyId) {
                fn_delete_company($companyId);
            }
        }

        return array(CONTROLLER_STATUS_OK, COMPANIES_MANAGER_LINK);
    }

    if ($mode == 'm_activate' || $mode == 'm_disable') {
        if ($mode == 'm_activate') {
            $status = 'A';
            $reason = !empty($_REQUEST['action_reason_activate']) ? $_REQUEST['action_reason_activate'] : '';
            $msg = __('text_companies_activated');
        } else {
            $status = 'D';
            $reason = !empty($_REQUEST['action_reason_disable']) ? $_REQUEST['action_reason_disable'] : '';
            $msg = __('text_companies_disabled');
        }

        $notification = !empty($_REQUEST['action_notification']) && $_REQUEST['action_notification'] == 'Y';

        $result = array();
        foreach ($selectedCompaniesIds as $company_id) {
            $status_from = '';
            $res = fn_companies_change_status($company_id, $status, $reason, $status_from, false, $notification);
            if ($res) {
                $result[] = $company_id;
            }
        }

        if ($result) {
            fn_set_notification('N', __('notice'), $msg);
        } else {
            fn_set_notification('E', __('error'), __('error_status_not_changed'), 'I');
        }

        return array(CONTROLLER_STATUS_REDIRECT, COMPANIES_MANAGER_LINK);
    }

    return array(CONTROLLER_STATUS_OK, "companies$suffix");
}

if ($mode == 'manage') {

    list($companies, $search) = fn_get_companies($_REQUEST, $auth, Registry::get('settings.Appearance.admin_elements_per_page'));

    Registry::get('view')->assign('companies', $companies);
    Registry::get('view')->assign('search', $search);

    Registry::get('view')->assign('countries', fn_get_simple_countries(true, (string) GlobalState::interfaceLocale()));
    Registry::get('view')->assign('states', fn_get_all_states());

} elseif ($mode == 'delete') {

    fn_delete_company($_REQUEST['company_id']);

    return array(CONTROLLER_STATUS_REDIRECT);

} elseif ($mode == 'update' || $mode == 'add') {
    if (Registry::get('runtime.company_id')) {
        Registry::get('view')->assign('hide_admin_info', true);
    }

    Registry::get('view')->assign('googlemaps_api_key', $container->getParameter('googlemaps.api_key'));

    $company_id = !empty($_REQUEST['company_id']) ? $_REQUEST['company_id'] : 0;
    $company_data = !empty($company_id) ? fn_get_company_data($company_id) : array();
    $companyDataPlaceholder = !empty($company_id) ? fn_get_company_data($company_id, (string) GlobalState::interfaceLocale()) : array();

    if ($mode == 'update' && empty($company_data)) {
        return array(CONTROLLER_STATUS_NO_PAGE);
    }

    Registry::get('view')->assign('logo_types', fn_get_logo_types(true));

    $restored_company_data = fn_restore_post_data('company_data');
    if (!empty($restored_company_data)) {
        if (!empty($restored_company_data['shippings'])) {
            $restored_company_data['shippings'] = implode(',', $restored_company_data['shippings']);
        }
        $company_data = fn_array_merge($company_data, $restored_company_data);
    }

    Registry::get('view')->assign('company_data', $company_data);
    Registry::get('view')->assign('companyDataPlaceholder', $companyDataPlaceholder);
    Registry::get('view')->assign('countries', fn_get_simple_countries(true, (string) GlobalState::interfaceLocale()));
    Registry::get('view')->assign('states', fn_get_all_states());

    $profile_fields = fn_get_profile_fields('A', array(), (string) GlobalState::interfaceLocale(), array('get_custom' => true, 'get_profile_required' => true));
    Registry::get('view')->assign('profile_fields', $profile_fields);

    if ($mode === 'update'
        && Registry::get('runtime.company_id') === 0
    ) {
        $payoutBalance = $payoutService->getPayoutBalance($company_id) ?? 0;
        Registry::get('view')->assign(
            'balance',
            (new Money($payoutBalance))->getConvertedAmount()
        );

        try {
            $showPayoutButton = $payoutService->getFirstValidProcessor()->canDoPayoutCompany();
        } catch (NoValidProcessorException $e) {
            $showPayoutButton = false;
        }

        Registry::get('view')->assign('showPayoutButton', $showPayoutButton);
    }

    // [Page sections]
    $tabs['detailed'] = array (
        'title' => __('general'),
        'js' => true
    );
    $tabs['terms'] = array (
        'title' => __(Registry::get('runtime.company_id') ? 'w_terms_and_conditions' : 'w_terms_and_conditions_short'),
        'js' => true
    );
    $tabs['description'] = array (
        'title' => __('description'),
        'js' => true
    );
    $tabs['extra_fields'] = array (
        'title' => __('extra_fields'),
        'js' => true
    );

    if ($container->getParameter('feature.available_offers')) {
        $tabs['available_offers'] = [
            'title' => __('available_offers_allowed_divisions'),
            'href' => 'available_offers.ajax_load&company='.$company_id, // for ajax load
            'js' => true
        ];

        $marketplaceDivisionSettings = $divisionSettingsService->getMarketplaceDivisionSettings();
        $marketplaceAvailableDivision = $marketplaceDivisionSettings->getIncludedDivisions()->toArray();

        $companyDivisionSettings = $divisionSettingsService->getCompanyDivisionSettings($company_id);
        $companyAvailableDivision = $companyDivisionSettings->getIncludedDivisions()->toArray();

        // DIVISIONS DISPONIBLES
        Registry::get('view')->assign(
            'availableDivisions',
            $marketplaceAvailableDivision
        );

        // DIVISIONS INDISPONIBLES
        Registry::get('view')->assign(
            'unavailableDivisions',
            $marketplaceDivisionSettings->getExcludedDivisions()->toArray()
        );

        $warningMsg = \count($companyAvailableDivision) === 0 ? __('no_division_for_vendor') : null;
        Registry::get('view')->assign('warningMsg', $warningMsg);

        $noDivisionEnteredMsg = \count($marketplaceAvailableDivision) === 0 ? __('no_division_in_marketplace') : null;
        Registry::get('view')->assign('noDivisionEnteredMsg', $noDivisionEnteredMsg);
    }

    Registry::get('view')->assign('isMarketplaceInvoicingDisplayed', $invoicingSettingsService->getMarketplaceInvoicingDisplayed());
    Registry::get('view')->assign('canEditInvoice', $companyService->canEditInvoicingDisplayed($company_id, ACCOUNT_TYPE === 'admin'));
    Registry::get('view')->assign('isAdmin', ACCOUNT_TYPE === 'admin');

    $isBillingNumberAutoGenerationActivated = $container->getParameter('feature.activate_billing_number_auto_generation');
    Registry::get('view')->assign('isBillingNumberAutoGenerationActivated', $isBillingNumberAutoGenerationActivated);

    // always false if mode = 'add'
    $hasOrders = false;

    // check count orders if mode ='update'
    if ($mode == 'update') {
        list($orders) = fn_get_orders(['company_id' => $_REQUEST['company_id']]);

        $hasOrders = count($orders) > 0;
    }

    Registry::get('view')->assign('hasOrders', $hasOrders);

    // On peut activer la génération automatique uniquement s'il n'y a pas de commande pour le vendeur
    // On peut désactiver la génération à partir du moment où le numéro a été renseigné, peu importe le statut
    Registry::get('view')->assign(
        'canActivateAutomaticBilling',
        !$hasOrders || $company_data['initial_billing_number'] !== null
    );

    // Le numéro initial de facturation peut-être changé tant qu'il n'y a pas eu de commande
    // et qu'un numéro a été saisi
    Registry::get('view')->assign(
        'canChangeInitialBillingNumber',
        !$hasOrders && $company_data['initial_billing_number'] !== null
    );

    Registry::get('view')->assign(
        'initialBillingNumberValue',
        $company_data['initial_billing_number'] !== null ? $company_data['initial_billing_number'] : 1
    );

    // On peut activer la génération automatique uniquement s'il n'y a pas de commande pour le vendeur
    // On peut désactiver la génération à partir du moment où le numéro a été renseigné, peu importe le statut
    Registry::get('view')->assign(
        'canActivateAutomaticRma',
        !$hasOrders || $company_data['initial_rma_number'] !== null
    );

    // Le numéro initial d'avoir peut-être changé tant qu'il n'y a pas eu de commande
    // et qu'un numéro a été saisi
    Registry::get('view')->assign(
        'canChangeInitialRmaNumber',
        !$hasOrders && $company_data['initial_rma_number'] !== null
    );

    Registry::get('view')->assign(
        'initialRmaNumberValue',
        $company_data['initial_rma_number'] !== null ? $company_data['initial_rma_number'] : 1
    );

    $tabs['advanced_settings'] = array(
        'title' => __('advanced_settings'),
        'js' => true
    );

    Registry::set('navigation.tabs', $tabs);

    Registry::get('view')->assign('items_status', [
        \Wizacha\Company::STATUS_NEW => __('new'),
        \Wizacha\Company::STATUS_PENDING => __('pending'),
        \Wizacha\Company::STATUS_ENABLED => __('active'),
        \Wizacha\Company::STATUS_DISABLED => __('disabled'),
    ]);

    if (!Registry::get('runtime.company_id')) {
        $legalDocuments = [
            'extra' => [],
        ];
        $legalDocumentsCount = 0;

        if ($company_id) {
            $files = (array) container()->get('Wizacha\Storage\VendorSubscriptionStorageService')->getList($company_id . '/');
            foreach ($files as $file) {
                $matches = [];
                if (preg_match('/w_([^0-9]+)_/', $file, $matches)) {
                    $legalDocuments[$matches[1]] = $file;
                    $legalDocumentsCount++;
                } elseif (preg_match('/^extra_([^0-9]+)_/', $file, $matches)) {
                    $legalDocuments['extra'][$matches[1]] = $file;
                    $legalDocumentsCount++;
                }
            }

            Registry::get('view')->assign('legalDocuments', $legalDocuments);
        }

        $tabs['legal_documents'] = array (
            'title' => __('legal_documents').' ('.$legalDocumentsCount.')',
            'js' => true
        );

        $commissionService = $container->get('marketplace.commission.commission_service');

        if ($mode === 'add') {
            $commissions = $commissionService->getDefaultCategoryCommissions();
        } else {
            $commissions = $commissionService->getCategoryCommissionsByCompany(\intval($_REQUEST['company_id']));
        }

        $commissions = array_map(function($commission) {
            /** @var \Wizacha\Marketplace\Commission\Commission $commission */
            return [
                'id' => $commission->getId(),
                'category_id' => $commission->getCategoryId(),
                'percent_amount' => $commission->getPercentAmount(),
                'fix_amount' => $commission->getFixAmount(),
                'maximum_amount' => $commission->getMaximumAmount(),
            ];
        }, $commissions);
        Registry::get('view')->assign('commissions', $commissions);

        if ($mode === 'add') {
            $commissionCompany = $commissionService->getDefaultCommission();
        } else {
            $commissionCompany = $commissionService->getCompanyCommission(\intval($_REQUEST['company_id']));
        }

        if ($commissionCompany instanceof Commission) {
            $commissionCompany = [
                'percent_amount' => $commissionCompany->getPercentAmount(),
                'fix_amount' => $commissionCompany->getFixAmount(),
                'maximum_amount' => $commissionCompany->getMaximumAmount(),
            ];
            Registry::get('view')->assign('commission_company', $commissionCompany);
        }

        $tabs['commissions'] = array (
            'title' => __('commissions'),
            'js' => true
        );

        $shippings = db_get_hash_array("SELECT a.shipping_id, a.status, b.shipping FROM ?:shippings as a LEFT JOIN ?:shipping_descriptions as b ON a.shipping_id = b.shipping_id AND b.lang_code = ?s WHERE a.company_id = 0 AND a.status = 'A' ORDER BY a.position", 'shipping_id', (string) GlobalState::contentLocale());
        Registry::get('view')->assign('shippings', $shippings);

        $tabs['shipping_methods'] = array (
            'title' => __('shipping_methods'),
            'js' => true
        );

        $tabs['addons'] = array (
            'title' => __('addons'),
            'js' => true
        );

        // Activate debug infos
        Registry::get('view')->assign(
            'lemonway_id',
            LemonWayConfig::PREFIX_VENDOR . $company_id
        );
        Registry::get('view')->assign('is_debug', isset($_REQUEST['debug_workflow']) || isset($_SESSION['debug_workflow']));

        Registry::set('navigation.tabs', $tabs);
    }
} elseif ($mode == 'update_status') {

    $notification = !empty($_REQUEST['notify_user']) && $_REQUEST['notify_user'] == 'Y';
    $changeStatus = true;

    if (true === $container->get('marketplace.payment.hipay')->isConfigured()) {
        $company_data = fn_get_company_data($_REQUEST['id']);

        /*
         * Si iban et bic est vide on rencontre deux cas :
         * 1) Lors de la création d'un marchant par le BO
         *    iban et bic sont égaux à chaine vide
         * 2) Lors de la création d'un marchant par le front
         *    iban et bic sont null
         *
         * Si l'iban ou le bic est null,
         * on les forces à chaine vide pour être dans le même cas que dans la création par BO.
         */
        if (($company_data['iban'] ?? '') === '' || ($company_data['bic'] ?? '') === '') {
            fn_set_notification('E', __('error'), __('iban_or_bic_empty'));

            $changeStatus = false;
         }

        if (false === $container->get('marketplace.payment.hipay')->getWalletApi()->isEmailAvailable($company_data['email'])) {
            fn_set_notification('E', __('error'), __('error_hipay_not_created_email_already_used'));

            $changeStatus = false;
        }
    }

    if ($changeStatus && fn_companies_change_status($_REQUEST['id'], $_REQUEST['status'], '', $status_from, false, $notification) && fn_notification_exists('type', 'E') === false) {
        Config::dispatch(
            CompanyEvents::UPDATED,
            IterableEvent::fromElement((int) $_REQUEST['id'])
        );

        fn_set_notification('N', __('notice'), __('status_changed'));

        return new JsonResponse();
    } else {
        if (fn_notification_exists('type', 'E') === false) {
            fn_set_notification('E', __('error'), __('error_status_not_changed'));
        }

        if (defined('AJAX_REQUEST') === true) {
            Registry::get('ajax')->assign('return_status', $status_from);
        } else {
            return array(CONTROLLER_STATUS_OK, COMPANIES_MANAGER_LINK);
        }
    }

    exit;

} elseif ($mode == 'picker') {
    list($companies, $search) = fn_get_companies($_REQUEST, $auth, Registry::get('settings.Appearance.admin_elements_per_page'));

    Registry::get('view')->assign('companies', $companies);
    Registry::get('view')->assign('search', $search);

    Registry::get('view')->assign('countries', fn_get_simple_countries(true, (string) GlobalState::interfaceLocale()));
    Registry::get('view')->assign('states', fn_get_all_states());

    Registry::get('view')->display('pickers/companies/picker_contents.tpl');
    exit;
}

if ($mode === 'trigger_payout') {
    $company_id = \array_key_exists('company_id', $_REQUEST) === true && \strlen($_REQUEST['company_id']) > 0 ? $_REQUEST['company_id'] : 0;
    $payoutBalance = $payoutService->getPayoutBalance($company_id) ?? 0;
    $payoutBalance = (new Money($payoutBalance))->getConvertedAmount();

    try {
        $payoutService->doPayoutCompany($company_id);
        fn_set_notification('N', __('notice'), __('company_trigger_payment_success', ['[balance]'=>$payoutBalance]));
    } catch (PayoutException $exception) {
        fn_set_notification('E', __('error'), __('company_trigger_payment_error'));
    }
}

if ($mode == 'merge') {

    if (!isset($_SESSION['auth']['is_root']) || $_SESSION['auth']['is_root'] != 'Y' || Registry::get('runtime.company_id')) {
        return array(CONTROLLER_STATUS_DENIED);
    }

    if (empty($_REQUEST['company_id'])) {
        return array(CONTROLLER_STATUS_NO_PAGE);
    }

    $company_id = $_REQUEST['company_id'];
    unset ($_REQUEST['company_id']);
    $company_data = !empty($company_id) ? fn_get_company_data($company_id) : array();

    if (empty($company_data)) {
        return array(CONTROLLER_STATUS_NO_PAGE);
    }

    $_REQUEST['exclude_company_id'] = $company_id;

    list($companies, $search) = fn_get_companies($_REQUEST, $auth, Registry::get('settings.Appearance.admin_elements_per_page'));

    Registry::get('view')->assign('company_id', $company_id);
    Registry::get('view')->assign('company_name', $company_data['company']);
    Registry::get('view')->assign('companies', $companies);
    Registry::get('view')->assign('search', $search);
    Registry::get('view')->assign('countries', fn_get_simple_countries(true, (string) GlobalState::interfaceLocale()));
    Registry::get('view')->assign('states', fn_get_all_states());

} elseif ($mode == 'delete_commission') {

    $container->get('marketplace.commission.commission_repository')->delete($_REQUEST['id']);
}
if ('add' == $mode) {
    Tygh\Registry::get('view')->assign('w_shippings_load', true);
}
