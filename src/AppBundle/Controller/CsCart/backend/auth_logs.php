<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

use Tygh\Registry;
use Wizacha\Component\AuthLog\AuthLog;
use Wizacha\Component\AuthLog\AuthLogRepository;
use Wizacha\Component\AuthLog\DestinationType;
use Wizacha\Component\AuthLog\SortByType;
use Wizacha\Component\AuthLog\SourceType;
use Wizacha\Component\AuthLog\StatusType;

if (!defined('BOOTSTRAP')) {
    die('Access denied');
}

$params = $_REQUEST;
$authLogService = container()->get('marketplace.authlog.service');

if ($mode == 'manage') {
    $params['items_per_page'] = $params['items_per_page'] ?? Registry::get('settings.Appearance.admin_pages_per_page');
    $params['page'] = intval($params['page']) > 0 ? intval($params['page']) : 1;
    $start = $params['page'] > 0 ? ($params['page'] - 1) * $params['items_per_page'] : 0;

    // multiple logins search
    preg_match_all('/([^\s|,]+)/', $params['login'], $logins);

    // parse date
    $params['period'] = $params['period'] ?: [];
    $normalizeDate = function ($date) {
        $datetime = DateTime::createFromFormat('d/m/Y H:i:s', sprintf('%s 00:00:00', $date));

        return ($datetime instanceof DateTime) ? $datetime->format(\DateTime::RFC3339) : null;
    };
    $period = array_map($normalizeDate, $params['period']);

    $default = array(
        'login' => $logins[0] ?: null,
        'status' => $params['status'] ?: null,
        'source' => $params['source'] ?: null,
        'destination' => $params['destination'] ?: null,
        'period' => $period ?: [],
        'sort_by' => $params['sort_by'] ?: SortByType::ID()->getValue(),
        'sort_order' => $params['sort_order'] ?: 'desc',
    );

    // save to file button
    $formats = ['csv'];
    $callback = function ($format) use ($params) {
        return http_build_query(
            array_merge($params, ['save' => $format])
        );
    };
    $formatUrls = array_map($callback, $formats);

    if (in_array($params['save'], $formats)) {
        // save to file
        return $authLogService->exportCSV(array_merge($default));
    }

    $paginate = array(
        'start' => $start,
        'limit' => $params['items_per_page'],
    );
    [$authLogs, $total] = $authLogService->search(array_merge($default, $paginate));

    $items = array_map(
        function (AuthLog $authLog) {
            return $authLog->expose();
        },
        $authLogs
    );
    $params['total_items'] = $total;

    // feed the tpl
    Registry::get('view')->assign('extra', 'autocomplete="off"'); // disable annoying autocomplete on date pickers
    Registry::get('view')->assign('status', StatusType::values());
    Registry::get('view')->assign('source', SourceType::values());
    Registry::get('view')->assign('destination', DestinationType::values());
    Registry::get('view')->assign('sort_by', SortByType::values());
    Registry::get('view')->assign('sort_order', ['desc', 'asc']);
    Registry::get('view')->assign('items', $items);
    Registry::get('view')->assign('search', $params);
    Registry::get('view')->assign('formats', $formats);
    Registry::get('view')->assign('formatUrls', $formatUrls);
    Registry::get('view')->assign(
        'csv_info',
        [
            'total' => min($total, AuthLogRepository::MAX),
            'max' => AuthLogRepository::MAX,
        ]
    );
}
