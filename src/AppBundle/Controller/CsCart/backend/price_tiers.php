<?php
/**
 *  <AUTHOR> DevTeam <<EMAIL>>
 *  @copyright   Copyright (c) Wizacha
 *  @license     Proprietary
 */
declare(strict_types=1);

use Tygh\Registry;
use Wizacha\Misc;

if (false === defined('BOOTSTRAP')) {
    die('Access denied');
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($mode === 'update') {
        Misc::redirectAfterSave('price_tiers', 'products.update&product_id=' . $_REQUEST['product_id'], $_REQUEST);

        $inventory = [
            [
                'combination' => "",
                'priceTiers' => [],
            ]
        ];

        for ($i = 0; $i < count($_REQUEST['lowerLimits']); $i++) {
            if ($_REQUEST['lowerLimits'][$i] !== "" && $_REQUEST['prices'][$i] !== "") {
                \array_push($inventory[0]['priceTiers'], [
                    'lowerLimit' => (int) $_REQUEST['lowerLimits'][$i],
                    'price' => (float) $_REQUEST['prices'][$i],
                ]);
            }
        }

        $productId = (int) $_REQUEST['product_id'];
        container()->get('marketplace.price_tier.price_tier_service')->savePriceTiers($productId, $inventory);

        return array(CONTROLLER_STATUS_OK, "price_tiers.update&product_id=$productId");
    }
}

if ($mode === 'update') {
    $productId = (int) $_REQUEST['product_id'];
    $productPriceTiers = container()
        ->get('marketplace.price_tier.price_tier_service')
        ->getPriceTiersOfProductOrCombination($productId, null);

    Registry::get('view')->assign('product_id', $productId);
    Registry::get('view')->assign('price_tiers', $productPriceTiers);
}
