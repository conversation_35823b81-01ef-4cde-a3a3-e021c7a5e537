<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */


use Tygh\Ajax;
use Tygh\Registry;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

if ($_SERVER['REQUEST_METHOD'] == 'POST') {

    $user = Registry::get('user_info', null);

    /*
     * Save rates for products
     */
    if ($mode == 'update' && !empty($_REQUEST['shipping_id']) && !empty($_REQUEST['product_id'])) {
        $shipping_id = $_REQUEST['shipping_id'];
        $company_id = Registry::get('runtime.company_id');
        $shipping_data = $_REQUEST['shipping_data'];

        \Wizacha\Misc::redirectAfterSave('w_product_rate', 'products.update&product_id='.$_REQUEST['product_id'], $_REQUEST);

        fn_wizacha_shippings_w_update_rate('w_product_shipping_rates', $_REQUEST['shipping_id'], $shipping_data['rates'], 'product_id', $_REQUEST['product_id'], \Tygh\Registry::get('runtime.company_id'));
        if (\Wizacha\Product::hasChanged($_REQUEST['product_id'])) {
            \Wizacha\Events\Config::dispatch(
                \Wizacha\Product::EVENT_UPDATE,
                (new \Wizacha\Events\IterableEvent)->setElement($_REQUEST['product_id'])
            );
        }

        $_extra = empty($_REQUEST['destination_id']) ? '' : '&destination_id=' . $_REQUEST['destination_id'];
        $suffix = '.update?shipping_id=' . $_REQUEST['shipping_id'] .'&product_id='.$_REQUEST['product_id']. $_extra;
        return array(CONTROLLER_STATUS_OK, "w_product_rate$suffix");
    }
    return array(CONTROLLER_STATUS_OK);
}




if ($mode == 'update_status') {
    $company_id = \Wizacha\Registry::defaultInstance()->get(['runtime', 'company_id']);
    $product_id = $_REQUEST['id'];
    $product_data = fn_get_product_data($product_id, $auth);
    $shipping_id = $_REQUEST['shipping_id'];
    $status = $_REQUEST['status'];

    if ($company_id && $company_id != $product_data['company_id']) {
        return array(CONTROLLER_STATUS_DENIED);
    }

    $result = \Wizacha\Shipping::changeStatusOnProduct($shipping_id, $product_id, $status);

    if ($result) {
        fn_set_notification('N', __('notice'), __('status_changed'));
        $ajax = Registry::get('ajax');
        if ($ajax instanceof Ajax) {
            $ajax->assign('return_status', $status);
        }
        \Wizacha\Product::checkStatusAfterChangeShippings($_REQUEST['id']);
    } else {
        fn_set_notification('E', __('error'), __('error_status_not_changed'));
    }
} elseif ($mode == 'update') {

    $product_id = $_REQUEST['product_id'];
    $product_data = fn_get_product_data($product_id, $auth);

    $shipping_id = $_REQUEST['shipping_id'];
    $company_id = Registry::get('runtime.company_id');

    if ($company_id && $product_data['company_id'] != $company_id) {
        return array(CONTROLLER_STATUS_DENIED);
    }

    Registry::get('view')->assign('product', $product_data);
    Registry::get('view')->assign('shipping', $product_data['shippings'][$shipping_id]);
    Registry::get('view')->assign('w_simple_rate', true);
} elseif ($mode == 'manage') {
    $suffix = ".update&product_id=".$_REQUEST['product_id'];
    return array(CONTROLLER_STATUS_REDIRECT,"products$suffix");
} elseif ($mode == 'delete') {
    if( !empty($_REQUEST['shipping_id']) && !empty($_REQUEST['product_id'])) {
        fn_w_delete_rates_for_product($_REQUEST['product_id'], $_REQUEST['shipping_id']);
    }
    return array(CONTROLLER_STATUS_REDIRECT, 'products.update?shipping_id=' . $_REQUEST['shipping_id'] .'&product_id='.$_REQUEST['product_id']);
}
