<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON>halnev    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

use Tygh\Registry;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Tax\Enum\TaxParameterEnum;

if (!defined('BOOTSTRAP')) { die('Access denied'); }

$_REQUEST['tax_id'] = empty($_REQUEST['tax_id']) ? 0 : $_REQUEST['tax_id'];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {

    $suffix = '.manage';

    //
    // Update taxes
    //
    if ($mode == 'm_update') {

        // Update taxes data
        if (!empty($_REQUEST['tax_data'])) {
            foreach ($_REQUEST['tax_data'] as $k => $v) {

                db_query("UPDATE ?:taxes SET ?u WHERE tax_id = ?i", $v, $k);
                db_query("UPDATE ?:tax_descriptions SET ?u WHERE tax_id = ?i AND lang_code = ?s", $v, $k, (string) GlobalState::contentLocale());
            }
        }
    }

    //
    // Delete taxes
    //
    if ($mode == 'm_delete') {
        // Delete selected taxes
        if (!empty($_REQUEST['tax_ids'])) {
            fn_delete_taxes($_REQUEST['tax_ids']);
        }
    }

    //
    // Update selected tax data
    //
    if ($mode == 'update') {
        $tax_id = fn_update_tax($_REQUEST['tax_data'], $_REQUEST['tax_id'], (string) GlobalState::contentLocale());
        $suffix = ".update?tax_id=$tax_id";
    }

    if ($mode == 'apply_selected_taxes') {
        if (!empty($_REQUEST['tax_ids'])) {

            $tax_names = fn_get_tax_name($_REQUEST['tax_ids']);

            foreach ($_REQUEST['tax_ids'] as $v) {
                db_query("UPDATE ?:products SET tax_ids = ?p", fn_add_to_set('?:products.tax_ids', $v));

                fn_set_notification('N', __('notice'), __('text_tax_applied', array(
                    '[tax]' => $tax_names[$v]
                )));
            }
        }

        $suffix = '.manage';
    }

    if ($mode == 'unset_selected_taxes') {
        if (!empty($_REQUEST['tax_ids'])) {

            $tax_names = fn_get_tax_name($_REQUEST['tax_ids']);

            foreach ($_REQUEST['tax_ids'] as $v) {
                db_query("UPDATE ?:products SET tax_ids = ?p", fn_remove_from_set('?:products.tax_ids', $v));

                fn_set_notification('N', __('notice'), __('text_tax_unset', array(
                    '[tax]' => $tax_names[$v]
                )));
            }
        }
    }
    return array(CONTROLLER_STATUS_OK, "taxes$suffix");
}

// ---------------------- GET routines ---------------------------------------
$regNumber = \Tygh\Settings::instance()->getSettingDataByName('w_full_tva_register_number')['value'];

// Edit tax rates
if ($mode == 'update') {
    $tax = fn_get_tax($_REQUEST['tax_id'], (string) GlobalState::contentLocale());
    $taxPlaceholder = fn_get_tax($_REQUEST['tax_id'], (string) GlobalState::interfaceLocale());
    if (empty($tax)) {
        return array(CONTROLLER_STATUS_NO_PAGE);
    }

    $destinations = fn_get_destinations();

    Registry::get('view')->assign('tax', $tax);
    Registry::get('view')->assign('taxRegNumber', $regNumber);
    Registry::get('view')->assign('taxPlaceholder', $taxPlaceholder);
    Registry::get('view')->assign('rates',  db_get_hash_array("SELECT * FROM ?:tax_rates WHERE tax_id = ?i", 'destination_id', $_REQUEST['tax_id']));
    Registry::get('view')->assign('destinations', \reset($destinations));

// Add tax
} elseif ($mode == 'add') {
    Registry::get('view')->assign('destinations', fn_get_destinations());

// Edit taxes
} elseif ($mode == 'manage') {
    $taxes = fn_get_taxes((string) GlobalState::contentLocale());
    $pagination = [
        'page' => $_REQUEST['page'] ?? 1,
        'items_per_page' => $_REQUEST['items_per_page'] ?? Registry::get('settings.Appearance.admin_elements_per_page'),
        'total_items' => \count($taxes),
    ];

    $tabs['tax_list'] = array (
        'title' =>  __('general'),
        'js' => true,
    );

    // Shipping taxes
    $configTaxMode = container()->getParameter('config.tax_mode');
    if (TaxParameterEnum::NATIONAL()->getValue() === $configTaxMode
        || TaxParameterEnum::EU_B2B()->getValue() === $configTaxMode
    ) {
        $internationalTaxService = container()->get('marketplace.international_tax.shipping');
        $internationalTaxes = $internationalTaxService->get($_REQUEST);

        $countries = $internationalTaxService->getCountriesWithoutTax();
        $paginationParams = $pagination;
        $paginationParams['total_items'] = $internationalTaxService->count();

        Registry::get('view')->assign('countries', $countries);
        Registry::get('view')->assign('shippingTaxes', $internationalTaxes);
        Registry::get('view')->assign('search_shipping_taxes', $paginationParams);

        $tabs['shipping_tab'] = array (
            'title' => __('taxes_manage_tab_shippings'),
            'js' => true
        );
    }

    Registry::set('navigation.tabs', $tabs);
    Registry::get('view')->assign('taxRegNumber', $regNumber);
    Registry::get('view')->assign('search_taxes', $pagination);
    Registry::get('view')->assign('taxes', $taxes);
    Registry::get('view')->assign('paginated_taxes', fn_get_taxes((string) GlobalState::contentLocale(), $pagination));
    Registry::get('view')->assign('prices_include_taxes', fn_is_prices_includes_tax() === true ? "A" : "D");

} elseif ($mode == 'delete') {
    if (!empty($_REQUEST['tax_id'])) {
        fn_delete_taxes($_REQUEST['tax_id']);
    }

    return array(CONTROLLER_STATUS_REDIRECT, "taxes.manage");
} elseif ($mode == 'update_status') {
    if (\array_key_exists('status', $_REQUEST) === true && ($_REQUEST['status'] === "A" || $_REQUEST['status'] === "D")) {
        fn_set_prices_includes_tax($_REQUEST['status'] === "A");
    }
}

