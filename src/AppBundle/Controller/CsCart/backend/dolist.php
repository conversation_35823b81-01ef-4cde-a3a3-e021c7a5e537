<?php
/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

use Wizacha\Component\Dolist\DolistTemplateType;
use Tygh\Registry;
use Wizacha\Component\Dolist\Entities\DolistTemplate;

if (!defined('BOOTSTRAP')) {
    die('Access denied');
}

$params = $_REQUEST;
$service = container()->get('marketplace.dolist_template_service');

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if($mode == 'update') {
        if (isset($params['templates']) && is_array($params['templates'])) {
            foreach ($params['templates'] as $template) {
                try {
                    $service->save(new DolistTemplateType($template['templateType']), (int) $template['templateId']);
                } catch (\Exception $exception) {
                    fn_set_notification('E', __('error'), $exception->getMessage());
                }
            }
        }
    }

    return array(CONTROLLER_STATUS_OK, "dolist.manage");
}

if ($mode == 'manage') {

    Registry::get('view')->assign('templates', array_map(function (DolistTemplate $template) {
        return [
            'templateType' => $template->getTemplateType()->getValue(),
            'templateLabelType' => $template->getTemplateType()->getKey(),
            'templateId' => $template->getTemplateId(),
        ];
    }, $service->find()));
}

