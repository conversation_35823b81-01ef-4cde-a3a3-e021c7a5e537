<?php
/***************************************************************************
*                                                                          *
*   (c) 2004 <PERSON>, <PERSON><PERSON>, <PERSON>ya <PERSON>ne<PERSON>    *
*                                                                          *
* This  is  commercial  software,  only  users  who have purchased a valid *
* license  and  accept  to the terms of the  License Agreement can install *
* and use this program.                                                    *
*                                                                          *
****************************************************************************
* PLEASE READ THE FULL TEXT  OF THE SOFTWARE  LICENSE   AGREEMENT  IN  THE *
* "copyright.txt" FILE PROVIDED WITH THIS DISTRIBUTION PACKAGE.            *
****************************************************************************/

if (!defined('BOOTSTRAP')) { die('Access denied'); }

if ($mode == 'export') {
    // TODO: move me to Symfony!
    $recipients = container()->get('marketplace.mailing_list.mailing_list_service')->getSubscribers();

    $content = implode(
        PHP_EOL,
        array_map(
            function ($recipient) {
                return $recipient->getEmail();
            },
            $recipients
        )
    );

    $response = new \Symfony\Component\HttpFoundation\Response($content, 200);
    $response->headers->set('Content-type', 'text/csv');
    $response->headers->set('Content-Disposition', 'attachment; filename="export_mailing.csv";');
    return [CONTROLLER_STATUS_OK, null, null, $response];
}
