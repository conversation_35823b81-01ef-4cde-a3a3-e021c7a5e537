<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller;

use Wizacha\Component\OAuth\Token;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\RedirectResponse;

/**
 * Controlleur de test pour vérifier le bon fonctionnement d'un provider oauth
 *
 * Accessible uniquement en env de dev
 */
class OAuthController extends BaseController
{
    public function testAction(Request $request): Response
    {
        if ($this->getParameter('kernel.environment') !== 'dev') {
            throw new \RuntimeException();
        }

        $provider = $this->get('marketplace.oauth.provider');

        if ($request->query->has('code')) {
            $user = $provider->authorize(new Token($request->query->get('code')));
            // phpcs:ignore
            dump($user);

            return new Response();
        }

        return new RedirectResponse(
            (string) $provider->getAuthorizeUrl()
        );
    }
}
