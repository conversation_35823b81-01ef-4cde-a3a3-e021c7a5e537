<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Wizacha\Marketplace\Cms\Event\ContactFormSubmitted;

class ContactController extends BaseController
{
    public function sendAction(Request $request)
    {
        $sender = $request->get('sender');
        $content = $request->get('content');
        $submittedToken = $request->get('csrf_token');

        if (!$sender || !$content) {
            fn_set_notification('E', __('error'), __('send_contact_missing_field'));
        } elseif (! $this->isCsrfTokenValid('contact_form_token', $submittedToken)) {
            fn_set_notification('E', __('error'), __('invalid_csrf_token'));
        } else {
            $eventDispatcher = $this->get('event_dispatcher');
            $adminCompany = $this->get('marketplace.admin_company');

            //Reconstruction du formulaire comme s'il venait d'une page CMS pour utiliser le même système de notification
            $event = new ContactFormSubmitted(
                __('subject_contact_by_client'),
                $adminCompany->getSiteAdministratorEmail(),
                [
                    [
                        'element_type' => FORM_EMAIL,
                        'value' => $sender,
                        'description' => __('sender')
                    ],
                    [
                        'element_type' => FORM_TEXTAREA,
                        'value' => $content,
                        'description' => __('body')
                    ]
                ]
            );
            $eventDispatcher->dispatch($event, ContactFormSubmitted::class);
        }

        return $this->redirect($request->headers->get('referer') ?: $this->generateUrl('home'));
    }
}
