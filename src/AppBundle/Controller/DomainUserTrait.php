<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller;

use Wizacha\Marketplace\User\UserService;
use Wizacha\Marketplace\User\User;

trait DomainUserTrait
{
    private UserService $userService;

    /**
     * Récupération de la classe métier de l'utilisateur,
     * et non la classe de sécurité qui est accessible par défaut.
     */
    private function getDomainUser(int $userId): User
    {
        return $this->userService->get($userId);
    }

    /** @required  */
    public function setUserService(UserService $userService): void
    {
        $this->userService = $userService;
    }
}
