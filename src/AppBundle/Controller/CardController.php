<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Wizacha\Marketplace\Payment\Event\StripeSetupCardCallbackEvent;

class CardController extends AbstractController
{
    private EventDispatcherInterface $eventDispatcher;

    public function __construct(EventDispatcherInterface $eventDispatcher)
    {
        $this->eventDispatcher = $eventDispatcher;
    }

    public function stripeUpdateAction(Request $request): Response
    {
        return $this->render('@App/frontend/checkout/stripe_update_credit_card.html.twig', [
            'stripePublicKey' => $request->get('stripePublicKey'),
            'checkoutSessionId' => $request->get('checkoutSessionId'),
        ]);
    }

    public function stripeUpdateCallbackAction(Request $request): Response
    {
        if (false === $request->query->has('session_id')
            && false === $request->query->has('redirect_url')
        ) {
            return new Response('', Response::HTTP_BAD_REQUEST);
        }

        if ('1' !== $request->query->get('canceled', 0)) {
            $this->eventDispatcher->dispatch(
                new StripeSetupCardCallbackEvent($request->query->get('session_id'), $request->query->get('customer_id', null))
            );
        }

        return $this->redirect(base64_decode($request->query->get('redirect_url')));
    }
}
