<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Backend;

use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Tygh\Registry;
use Wizacha\AppBundle\Notification\NotificationConfig;
use Wizacha\AppBundle\Notification\NotificationConfigService;
use Wizacha\Component\Notification\NotificationDispatcher;
use Wizacha\Component\Notification\NotificationEvent;
use Wizacha\Component\Notification\TestNotificationRequested;

class NotificationController extends BackendController
{
    public NotificationConfigService $notificationConfigService;

    public function __construct(
        NotificationConfigService $notificationConfigService
    ) {
        $this->notificationConfigService = $notificationConfigService;
    }

    public function listAction(Request $request): Response
    {
        $this->mustBeAdmin();

        $notificationsNotSynchronised = 0;
        if ($request->isMethod('GET')) {
            $notificationsNotSynchronised = $this->checkAllNotificationsConfigAreInDB($request->get('synchronize'));

            $notifications = $this->getAllNotificationsConfig(['description' => 'ASC']);
        } else {
            $notifications = $this->saveNotificationsConfig($request);
        }

        Registry::get('view')->assign('buttons', $this->renderView('@App/backend/notification/save.buttons.html.twig'));

        return $this->render('@App/backend/notification/list.html.twig', [
            // Title page
            'navigation.subsection' => 'emails_notifications',
            // Data
            'events' => $this->exposeNotificationsConfig($notifications),
            'warning' => $notificationsNotSynchronised > 0,
        ]);
    }

    public function triggerAction(Request $request)
    {
        if (!$this->getUser()->isAdmin()) {
            throw $this->createAccessDeniedException();
        }

        $eventName = $request->query->get('event');
        if (!is_subclass_of($eventName, NotificationEvent::class, true)) {
            // We only allow to trigger those specific events (it's safer if we limit what can be done)
            throw $this->createNotFoundException();
        }

        // Create the form
        $formBuilder = $this->createFormBuilder();
        $eventName::buildDebugForm($formBuilder);
        $formBuilder->add('submit', SubmitType::class, [
            'attr' => ['class' => 'btn btn-primary'],
        ]);
        $form = $formBuilder->getForm();

        $result = null;

        if ($request->isMethod('GET')) {
            $notification = $this->get('app.notification_dispatcher')->getNotification($eventName);

            // The notification is considered enabled by default
            $result['enabled'] = $notification instanceof NotificationConfig
                    ? $notification->isEnabled()
                    : true;
        }

        if ($request->isMethod('POST')) {
            $form->handleRequest($request);
            if ($form->isValid()) {
                $event = $eventName::createFromForm($form);
                $result = $this->get('app.notification_dispatcher')->dispatchDebug($event);
            }
        }

        return $this->render('@App/backend/notification/view.html.twig', [
            'form' => $form->createView(),
            'resultMail' => $result['mail'] ?? null,
            'title' => $eventName::getDescription(),
            'enabled' => $result['enabled'] ?? false,
        ]);
    }

    public function checkAllNotificationsConfigAreInDB(?string $synchronize): int
    {
        $events = NotificationDispatcher::getMapping();

        $notifications = array_map(
            function (NotificationConfig $notification) {
                return $notification->getClassName();
            },
            $this->getAllNotificationsConfig()
        );

        foreach ($events as $eventClass => $eventNotifier) {
            if (TestNotificationRequested::class === $eventClass) {
                unset($events[$eventClass]);
            }

            if (true === \in_array($eventClass, $notifications)) {
                unset($events[$eventClass]);
            }
        }

        if ($synchronize === '1') {
            try {
                // Synchronize missing notifications in DB, enabled by default
                foreach ($events as $eventClass => $eventNotifier) {
                    $notification = new NotificationConfig(
                        $eventClass,
                        $eventClass::getDescription() ?? $eventClass,
                        $eventNotifier,
                        true
                    );

                    $notification->setUser($this->getDomainUser());

                    $this->notificationConfigService
                        ->batchSave([$notification]);
                }

                return 0;
            } catch (\Throwable $e) {
                fn_set_notification('W', __('error'), __('notifications_config_update_error'));
            }
        }

        return \count($events);
    }

    /** @return NotificationConfig[] */
    public function saveNotificationsConfig(Request $request): array
    {
        $formEnabledNotifications = $request->request->get('notifications');

        $allNotifications = $this->getAllNotificationsConfig(['description' => 'ASC']);

        foreach ($allNotifications as $notification) {
            $notification->setEnabled(
                TestNotificationRequested::class === $notification->getClassName()
                || \array_key_exists($notification->getId(), $formEnabledNotifications)
            );
            $notification->setUser($this->getDomainUser());
        }

        try {
            $this->notificationConfigService->batchSave($allNotifications);
        } catch (\Throwable $e) {
            fn_set_notification('W', __('error'), __('notifications_config_update_error'));
        }

        return $allNotifications;
    }

    /**
     * @param NotificationConfig[]
     *
     * @return mixed[]
     */
    private function exposeNotificationsConfig(array $notifications): array
    {
        return array_filter(
            array_map(
                function (NotificationConfig $notification) {
                    return [
                        'id' => $notification->getId(),
                        'class' => $notification->getClassName(),
                        'description' => $notification->getDescription(),
                        'checked' => $notification->isEnabled() ? 'checked' : '',
                    ];
                },
                $notifications
            ),
            function (array $notification) {
                // We don't want this notification to be visible in the BO list
                return TestNotificationRequested::class !== $notification['class'];
            }
        );
    }

    /**
     * @param mixed[]
     *
     * @return NotificationConfig[]
     */
    private function getAllNotificationsConfig(array $order = []): array
    {
        return $this->notificationConfigService
            ->findBy(
                [],
                $order
            );
    }
}
