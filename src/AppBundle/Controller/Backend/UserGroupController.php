<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Backend;

use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Tygh\Registry;
use Wizacha\Component\Import\EximJobService;
use Wizacha\Component\Import\JobType;
use Wizacha\Component\Import\Uploader\UploaderInterface;
use Wizacha\FeatureFlag\FeatureFlaggableInterface;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Group\UserGroupService;

class UserGroupController extends BackendController implements FeatureFlaggableInterface
{
    private UserGroupService $userGroupService;
    private UploaderInterface $uploader;
    private EximJobService $jobService;

    public function __construct(
        UserGroupService $userGroupService,
        UploaderInterface $uploader,
        EximJobService $jobService
    ) {
        $this->userGroupService = $userGroupService;
        $this->uploader = $uploader;
        $this->jobService = $jobService;
    }

    public function listAction(Request $request): Response
    {
        $currentPage = $request->query->getInt('page', 1);
        $resultPerPage = $request->query->get('items_per_page', 10);
        $groups = $this->userGroupService->list($currentPage, $resultPerPage);

        Registry::get('view')->assign('page_title', __('user_groups_page_title'));
        Registry::get('view')->assign('buttons', $this->renderView('@App/backend/group/save.buttons.html.twig'));

        return $this->render('@App/backend/group/list.html.twig', [
            'currentPage' => $currentPage,
            'lastPage' => ceil($groups->count() / $groups->getQuery()->getMaxResults()),
            'total' => $groups->count(),
            'items_per_page' => $resultPerPage,
            'groups' => $groups,
            'localeSelector' => true,
        ], null);
    }

    public function updateAction(Request $request): Response
    {
        try {
            $this->userGroupService->saveAll($request->request->get('ids'));
            fn_set_notification('N', __('notice'), __('user_groups_updated'));
        } catch (\Exception $exception) {
            fn_set_notification('E', __('error'), __('user_groups_cant_be_updated'));
        }

        return $this->redirectToRoute('user_groups_list');
    }

    public function importUsersAction(Request $request): Response
    {
        try {
            $this->uploader->check($request);
        } catch (\Throwable $exception) {
            fn_set_notification('E', __('error'), __($exception->getMessage()));
        }

        $groupId = $request->request->get("groupId");
        $uploadedFile = $this->uploader->upload($request, 'group_users');
        $data = $this->getDataFromCsvFile($uploadedFile);
        $countLines = \count($data);

        if ($countLines === 0) {
            fn_set_notification('E', __('error'), __('user_groups_file_without_lines'));

            return $this->redirectToRoute('user_groups_list');
        }

        if ($countLines === 1) {
            $this->userGroupService->deleteAllUsers($groupId);
            fn_set_notification('N', __('notice'), __('user_groups_users_deleted'));

            return $this->redirectToRoute('user_groups_list');
        }

        $job = $this->jobService->create(
            JobType::GROUP_USERS(),
            $this->getUser()->getId(),
            $this->getUser()->getCompanyId(),
            $uploadedFile->getClientOriginalName(),
            $countLines - 1
        );

        try {
            $this->userGroupService->importUsers($data, $groupId, $job);
            fn_set_notification('N', __('notice'), __('user_groups_users_imported'));
        } catch (BadRequestHttpException | \LogicException $exception) {
            fn_set_notification('E', __('error'), __($exception->getMessage()));
        }

        return $this->redirectToRoute('user_groups_list');
    }

    public function exportUsersAction(string $groupId): Response
    {
        try {
            $data = $this->userGroupService->exportUsersData($groupId);
            $fp = fopen('php://output', 'w');
            fputcsv($fp, \array_keys($data[0]));

            foreach ($data as $fields) {
                fputcsv($fp, $fields);
            }

            fclose($fp);

            $response = new Response();
            $response->headers->set('Content-Type', 'text/csv');
            $response->headers->set('Content-Disposition', 'attachment; filename="users.csv"');

            fn_set_notification('N', __('notice'), __('user_groups_users_exported'));

            return $response;
        } catch (NotFound $exception) {
            fn_set_notification('E', __('error'), __($exception->getMessage()));
        }

        return $this->redirectToRoute('user_groups_list');
    }

    public function getFeatureFlag(): string
    {
        return 'feature.user_groups_enabled';
    }

    /** @return string[] */
    protected function getDataFromCsvFile(UploadedFile $uploadedFile): array
    {
        $handler = fopen($uploadedFile->getPathname(), 'r');
        $row = 1;
        $rows = [];

        while (($data = fgetcsv($handler, 1000, ";")) !== false) {
            if (\mb_strlen($data[0]) > 0) {
                $rows[] = $data;
            }
            $row++;
        }
        fclose($handler);

        return $rows;
    }
}
