<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Backend;

use GuzzleHttp\Exception\ClientException;
use Guz<PERSON>Http\Exception\ConnectException;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\RouterInterface;
use Tygh\Registry;
use Wizacha\AppBundle\Form\Type\WebhookType;
use Wizacha\Component\Http\YavinClientException;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Webhooks\Service\WebhooksService;
use Wizacha\Marketplace\Webhooks\WebhookData;

class NotificationsController extends BackendController
{
    private const NOTIFICATION_DELETED_TRANS_KEY = 'webhooks_notifications_deleted';
    private const NOTIFICATION_CREATED_TRANS_KEY = 'webhooks_notifications_created';
    private const NOTIFICATION_UPDATED_TRANS_KEY = 'webhooks_notifications_updated';
    private const NOTIFICATION_ERROR_TRANS_KEY = 'webhooks_notifications_service_error';
    private const NOTIFICATION_FORBIDDEN_TRANS_KEY = 'webhooks_notifications_service_forbidden';
    private const NOTIFICATION_UNEXPECTED_ERROR_TRANS_KEY = 'webhooks_notifications_service_unexpected_error';

    private WebhooksService $webhooksService;
    private CompanyService $companyService;
    private LoggerInterface $logger;
    private RouterInterface $router;

    public function __construct(
        WebhooksService $webhooksService,
        CompanyService $companyService,
        LoggerInterface $logger,
        RouterInterface $router
    ) {
        $this->webhooksService = $webhooksService;
        $this->companyService = $companyService;
        $this->logger = $logger;
        $this->router = $router;
    }

    public function manageAction(): Response
    {
        try {
            $webhooks = $this->webhooksService->getWebhooks();
        } catch (YavinClientException $exception) {
            return $this->displayErrorMessage($exception);
        }

        $companies = [];

        foreach ($webhooks as $webhook) {
            $webhookCompanyId = $webhook->getCompanyId();

            if (\is_int($webhookCompanyId) === true) {
                try {
                    $companies[$webhookCompanyId] = $this->companyService->get($webhookCompanyId)->getName();
                } catch (\Exception $exception) {
                    $this->logger->warning($exception->getMessage());
                }
            }
        }

        return $this->render(
            '@App/backend/notifications/manage.html.twig',
            [
                'webhooks' => $webhooks,
                'companies' => $companies,
                'switchCompanyId' => Registry::get('runtime.company_id')
            ]
        );
    }

    public function viewAction(Request $request): Response
    {
        Registry::get('view')->assign('buttons', $this->renderView('@App/backend/notifications/save_buttons.html.twig'));

        $webhookId = $request->query->get('id');

        try {
            if (\is_null($webhookId) === true) {
                return $this->render(
                    '@App/backend/notifications/view.html.twig',
                    [
                        'form' => $this->createForm(
                            WebhookType::class,
                            null,
                            ['allEventReferences' => $this->webhooksService->getWebhooksEventReferences()]
                        )
                            ->createView(),
                    ]
                );
            }

            return $this->render(
                '@App/backend/notifications/view.html.twig',
                [
                    'form' => $this->createForm(
                        WebhookType::class,
                        $this->webhooksService->getWebhook($webhookId),
                        ['allEventReferences' => $this->webhooksService->getWebhooksEventReferences()]
                    )
                        ->createView(),
                    'webhook' => $this->webhooksService->getWebhook($request->query->get('id')),
                ]
            );
        } catch (YavinClientException $exception) {
            return $this->displayErrorMessage($exception);
        }
    }

    public function saveAction(Request $request): RedirectResponse
    {
        $eventReferences = [];
        $webhookId = $request->request->get('id');
        $url = $request->request->get('webhook')['url'];


        $switchCompanyId = Registry::get('runtime.company_id');
        if ($this->getUser()->getCompanyId() === 0) {
            $companyId = $switchCompanyId > 0 ? $switchCompanyId : null;
        } else {
            $companyId = $this->getUser()->getCompanyId();
        }

        foreach ($request->request->get('event_names') as $eventName) {
            $eventReferences[] = $eventName;
        }

        try {
            if (\is_string($webhookId) === true) {
                $this->update($webhookId, $url, $eventReferences, $companyId);
            } else {
                $webhookId = $this->create(
                    $url,
                    $eventReferences,
                    $companyId
                );
            }
        } catch (YavinClientException $exception) {
            return $this->displayErrorMessage($exception);
        }

        if ($request->request->get('return_to_list') === 'Y') {
            $redirectURL = $this->router->generate('admin_notifications_manage');
        } else {
            $redirectURL = $this->router->generate('admin_notifications_view', ['id' => $webhookId]);
        }

        return $this->redirect($redirectURL);
    }

    public function deleteAction(Request $request): RedirectResponse
    {
        try {
            $this->webhooksService->deleteWebhook($request->query->get('id'));

            fn_set_notification('N', __('notice'), __(self::NOTIFICATION_DELETED_TRANS_KEY));
        } catch (YavinClientException $exception) {
            return $this->displayErrorMessage($exception);
        }

        return $this->redirect($this->router->generate('admin_notifications_manage'));
    }

    /** @return string|void */
    protected function create(string $url, array $eventReferences, ?int $companyId)
    {
        $webhookId = $this->webhooksService->postWebhook(
            new WebhookData(
                $url,
                $companyId,
                null,
                $eventReferences
            )
        )->getId();

        fn_set_notification('N', __('notice'), __(self::NOTIFICATION_CREATED_TRANS_KEY));

        return $webhookId;
    }

    protected function update(string $id, string $url, array $eventReferences, ?int $companyId): void
    {
        $this->webhooksService->patchWebhook(
            $id,
            new WebhookData(
                $url,
                $companyId,
                null,
                $eventReferences
            )
        );

        fn_set_notification('N', __('notice'), __(self::NOTIFICATION_UPDATED_TRANS_KEY));
    }

    protected function displayErrorMessage(YavinClientException $exception): RedirectResponse
    {
        if ($exception->getPrevious() instanceof ClientException) {
            fn_set_notification('E', __('error'), __(self::NOTIFICATION_FORBIDDEN_TRANS_KEY));
        } elseif ($exception->getPrevious() instanceof ConnectException) {
            fn_set_notification('E', __('error'), __(self::NOTIFICATION_ERROR_TRANS_KEY));
        } else {
            fn_set_notification('E', __('error'), __(self::NOTIFICATION_UNEXPECTED_ERROR_TRANS_KEY));
        }

        return $this->redirect(fn_url('index'));
    }
}
