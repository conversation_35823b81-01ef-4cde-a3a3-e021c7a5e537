<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Backend;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class CsvfileController extends BackendController
{
    public function listAction()
    {
        $objects = container()->get("Wizacha\Storage\CsvStorageService")->listContents();

        return $this->render('AppBundle:backend/csvfile:list.html.twig', ['objects' => $objects]);
    }

    public function getAction(Request $request)
    {
        if ($request->query->get('file') === false) {
            return  new Response('', 404);
        }

        return container()->get("Wizacha\Storage\CsvStorageService")->get($request->query->get('file'));
    }
}
