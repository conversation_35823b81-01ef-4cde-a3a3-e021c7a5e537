<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Backend;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Wizacha\Marketplace\Commission\CommissionService;
use Wizacha\Marketplace\Exception\NotFound;
use Symfony\Component\HttpFoundation\Response;
use Wizacha\Marketplace\Commission\Exception\CommissionAlreadyExists;
use Wizacha\Marketplace\Commission\Exception\CommissionByCategoryException;
use Wizacha\Marketplace\Commission\Exception\CommissionNotFound;
use Wizacha\Money\Money;

class CommissionController extends BackendController
{
    /** @var CommissionService */
    protected $commissionService;

    public function __construct(CommissionService $commissionService)
    {
        $this->commissionService = $commissionService;
    }

    public function manageAction(): Response
    {
        if ($this->getUser()->isAdmin() === false) {
            throw $this->createAccessDeniedException();
        }

        $defaultCommission = $this->commissionService->getDefaultCommission();
        $commissionsByCategory = $this->commissionService->getAllCommissionsByCategory();

        return $this->render('@App/backend/commission/manage.html.twig', [
            'fixed' => $defaultCommission->getFixAmount(),
            'percent' => $defaultCommission->getPercentAmount(),
            'maximum' => $defaultCommission->getMaximumAmount(),
            'commissionsByCategory' => $commissionsByCategory,
            'currency' => container()->getParameter('currency.sign'),
        ]);
    }

    public function saveAction(Request $request): RedirectResponse
    {
        if ($this->getUser()->isAdmin() === false) {
            throw $this->createAccessDeniedException();
        }

        if ($this->isCsrfTokenValid('bo_default_commission', $request->request->get('csrf_token')) === false) {
            fn_set_notification('E', __('error'), __('invalid_csrf_token'));

            return $this->redirectToRoute('admin_Commission_manage');
        }

        if (\strlen($request->request->get('maximum')) > 0 && (float) $request->request->get('maximum') <= 0) {
            fn_set_notification('E', __('error'), __('maximum_commission_value'));

            return $this->redirectToRoute('admin_Commission_manage');
        }

        // Old system
        $fixed = Money::fromVariable((float) $request->request->get('fixed'));
        $percent = (float) $request->request->get('percent');
        $maximum = (float) $request->request->get('maximum');

        $this->commissionService->setDefaultCommissionFixed($fixed);
        $this->commissionService->setDefaultCommissionPercent($percent);
        $this->commissionService->setDefaultCommissionMaximum($maximum);

        // New system
        $maximum = $request->request->get('maximum', null);
        $defaultCommission = $this->commissionService->getDefaultCommission();

        $defaultCommission
            ->setFixAmount((float) $request->request->get('fixed', 0))
            ->setPercentAmount((float) $request->request->get('percent', 0))
            ->setMaximumAmount(\strlen($maximum) > 0 ? (float) $maximum : null)
        ;
        $this->commissionService->saveCommission($defaultCommission);

        if ($request->request->has('apply_to_all')) {
            $this->commissionService->applyCommissionToAllCompanies($fixed, $percent);
            $this->commissionService->deleteAllCompanyCommissions();
            $this->commissionService->applyToAllCompanies($defaultCommission);
        }

        $commissionsByCategory = $request->request->get('commission_data');

        foreach ($commissionsByCategory as $commissionByCategory) {
            $commissionId = '';

            if (\is_string($commissionByCategory['id']) && \strlen($commissionByCategory['id']) > 0) {
                try {
                    $commissionId = $this->commissionService->updateCommissionByCategory($commissionByCategory);
                } catch (CommissionByCategoryException $exception) {
                    fn_set_notification('E', __('error'), __('category_not_found'));
                } catch (CommissionNotFound $exception) {
                    fn_set_notification('E', __('error'), __('commission_does_not_exist'));
                }
            } elseif (\intval($commissionByCategory['category_id']) !== 0) {
                try {
                    $commissionId = $this->commissionService->addCommissionByCategory($commissionByCategory);
                } catch (CommissionByCategoryException $exception) {
                    fn_set_notification('E', __('error'), __('category_not_found'));
                } catch (CommissionAlreadyExists $exception) {
                    fn_set_notification('E', __('error'), __('commission_category_duplicate'));
                }
            }

            if (\strlen($commissionId) > 0) {
                if ($request->request->has('apply_to_all')) {
                    $commissionAdded = $this->commissionService->getCommissionById($commissionId);
                    $this->commissionService->applyToAllCompanies($commissionAdded);
                }
            }
        }

        fn_set_notification('N', __('notice'), __('text_changes_saved'));

        return $this->redirectToRoute('admin_Commission_manage');
    }

    public function deleteAction(Request $request): Response
    {
        if ($this->getUser()->isAdmin() === false) {
            throw $this->createAccessDeniedException();
        }

        try {
            $this->commissionService->deleteCommission($request->get('commissionId'));
            fn_set_notification('N', __('notice'), __('commission_deleted'));
        } catch (NotFound $exception) {
            fn_set_notification('E', __('error'), __('commission_does_not_exist'));
        }

        if ($request->isXmlHttpRequest()) {
            return new JsonResponse($_SESSION);
        }

        return $this->redirectToRoute('admin_Commission_manage');
    }
}
