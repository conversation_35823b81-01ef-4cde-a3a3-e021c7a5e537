<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Backend;

use Symfony\Component\HttpFoundation\Response;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\Metrics\SandboxMetrics;

class SandboxDiskUsageController extends BackendController
{
    private SandboxMetrics $sandboxMetrics;
    private bool $isSandbox;

    public function __construct(SandboxMetrics $sandboxMetrics, bool $isSandbox)
    {
        $this->sandboxMetrics = $sandboxMetrics;
        $this->isSandbox = $isSandbox;
    }

    public function getAction(): Response
    {
        if ($this->isSandbox === false) {
            throw new Forbidden('Only for sandbox');
        };

        $dbSize = $this->sandboxMetrics->getDatabaseSize();
        $diskLimit = $this->sandboxMetrics->getDatabaseLimit();
        $percentUsage = \round((\floatval($dbSize) / $diskLimit) * 100.0);

        return $this->render('@App/backend/metrics/sandbox_disk_usage.html.twig', [
            'dbSize' => $dbSize,
            'percentUsage' => $percentUsage,
            'diskLimit' => $diskLimit
        ]);
    }
}
