<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Backend;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Wizacha\AppBundle\Controller\BaseController;

class AttributeController extends BaseController
{
    public function searchBrandAction(Request $request)
    {
        $query = $request->query->get('query');
        $brands = $this->get('marketplace.pim.attribute_service')->searchBrand($query);
        $results = [
            'data' => [],
        ];

        foreach ($brands as $brand) {
            $results['data'][$brand['variant_id']] = $brand['variant'];
        }

        return new JsonResponse($results);
    }
}
