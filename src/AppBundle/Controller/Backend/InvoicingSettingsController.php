<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Backend;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Wizacha\Marketplace\InvoicingSettings\InvoicingSettingsService;

class InvoicingSettingsController extends BackendController
{
    private InvoicingSettingsService $invoicingSettingsService;

    public function __construct(InvoicingSettingsService $invoicingSettingsService)
    {
        $this->invoicingSettingsService = $invoicingSettingsService;
    }

    public function indexAction(Request $request): Response
    {
        $this->mustBeAdmin();

        $marketplaceInvoicingDisplayed = $this->invoicingSettingsService->getMarketplaceInvoicingDisplayed();
        if ($request->isMethod('POST')  === true) {
            $newMarketplaceInvoicingDisplayed = $request->get('invoicing_settings') === 'on';
            if ($marketplaceInvoicingDisplayed !== $newMarketplaceInvoicingDisplayed) {
                $this->invoicingSettingsService->updateMarketplaceInvoicingDisplayed($newMarketplaceInvoicingDisplayed);
                $marketplaceInvoicingDisplayed = $newMarketplaceInvoicingDisplayed;
            }
        }

        return $this->render('@App/backend/invoicing_settings/index.html.twig', ['marketplaceInvoicingDisplayed' => $marketplaceInvoicingDisplayed]);
    }
}
