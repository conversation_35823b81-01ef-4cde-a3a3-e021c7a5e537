<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Backend;

use Symfony\Component\HttpFoundation\Response;

final class MailController extends BackendController
{
    public function listVariablesAction(): Response
    {
        $notifierParseService = $this->container->get('app.notifier_parser');

        return $this->render(
            '@App/backend/templates/list.html.twig',
            [
                'list' => $notifierParseService->parseNotifierClasses(),
            ]
        );
    }
}
