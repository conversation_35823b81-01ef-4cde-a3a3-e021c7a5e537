<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Backend;

use Symfony\Component\Intl\Countries;
use Tygh\Registry;
use Wizacha\FeatureFlag\FeatureFlaggableInterface;
use Wizacha\Marketplace\Exception\Forbidden;
use Symfony\Component\HttpFoundation\Response;

class OrganisationController extends BackendController implements FeatureFlaggableInterface
{
    public function listAction(): Response
    {
        return $this->render('@App/backend/organisation/organisations.html.twig', [
            'apiKey' => $this->getApikey(),
        ]);
    }

    public function organisationAction(string $organisationId): Response
    {
        Registry::get('view')->assign('buttons', $this->renderView('@App/backend/organisation/save_buttons.html.twig'));

        return $this->render('@App/backend/organisation/organisation.html.twig', [
            'organisationId' => $organisationId,
            'apiKey' => $this->getApikey(),
            'countries' => Countries::getNames(),
        ]);
    }

    public function identityCardAction(int $userId): Response
    {
        $user = $this->get('marketplace.user.user_service')->get($userId);

        if (!$user->belongsToAnOrganisation()) {
            throw new Forbidden();
        }

        $admin = $user->getOrganisation()->getAdministrator($user);

        return container()->get("Wizacha\Storage\OrganisationStorageService")->get('identity_card/' . $admin->getIdentityCard());
    }

    public function proofOfAppointmentAction(int $userId): Response
    {
        $user = $this->get('marketplace.user.user_service')->get($userId);

        if (!$user->belongsToAnOrganisation()) {
            throw new Forbidden();
        }

        $admin = $user->getOrganisation()->getAdministrator($user);

        return container()->get("Wizacha\Storage\OrganisationStorageService")->get('proof_of_appointment/' . $admin->getProofOfAppointment());
    }

    private function getApikey(): string
    {
        $user = $this->get('marketplace.user.user_service')->get($this->getUser()->getId());

        return $user->getApiKey();
    }

    public function getFeatureFlag(): string
    {
        return 'feature.organisations_enabled';
    }
}
