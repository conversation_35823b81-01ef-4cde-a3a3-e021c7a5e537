<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Backend;

use Stripe\Error\Base;
use Stripe\Person;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Registry;
use Wizacha\Marketplace\Payment\Stripe\StripeConfig;

class StripeController extends BackendController
{
    public function stripeTosAcceptanceAction(Request $request): Response
    {
        $companyId = Registry::defaultInstance()->get(['runtime', 'company_id']);
        $company = $this->get(CompanyService::class)->get($companyId);

        $country = $company->getCountry();
        if (empty($company->getIBAN()) || empty($company->getBIC())) {
            fn_set_notification('E', __('error'), __('stripe_no_iban'));

            return $this->redirect($request->getBaseUrl());
        }

        $addressLines = explode("\n", $company->getAddress(), 2);
        $addressLine1 = trim($addressLines[0]);
        $addressLine2 = !empty($addressLines[1]) ? str_replace(["\r", "\n"], ' ', trim($addressLines[1])) : '';

        $userRepository = $this->get('marketplace.user.user_repository');
        $companyLegacy = new Company(Registry::defaultInstance()->get(['runtime', 'company_id']));

        // If we are logged in as MP admin - forcing vendor registration/update
        // then we retrieve the first company administrator - otherwise we use the current user
        if ($this->getUser()->getCompanyId() === 0) {
            $user = $userRepository->get($companyLegacy->getFirstAdmin()->getId());
        } else {
            $user = $userRepository->get($this->getUser()->getId());
        }

        $person = null;
        if (\is_string($companyLegacy->getStripeId())) {
            try {
                $account = $this->get('marketplace.payment.stripe')->getCustomAccount($companyLegacy->getStripeId());
                $person = $this->get('marketplace.payment.stripe')->getCustomPerson($account);
            } catch (Base $e) {
                fn_set_notification('E', __('error'), $e->getMessage());
                $this->get('logger')->error('Stripe error: ' . $e->getMessage(), ['exception' => $e]);

                return $this->redirect($request->getBaseUrl());
            }
        }

        $files = [];
        if (false === $person instanceof Person || $person->verification->status !== 'verified') {
            $vendorSubscriptionStorage = container()->get('Wizacha\Storage\VendorSubscriptionStorageService');
            foreach ($vendorSubscriptionStorage->getList($companyId . '/') as $existingFile) {
                if (1 !== preg_match('/^(w_ID_card_|w_KBIS_)/', $existingFile, $matches)) {
                    continue;
                }

                $files[$matches[1]]['filename'] = $existingFile;
                $file = $vendorSubscriptionStorage->readStream($companyId . '/' . $existingFile);
                $files[$matches[1]]['content'] = base64_encode(stream_get_contents($file));
                fclose($file);
            }
        }

        return $this->render('@App/backend/stripe/tos-acceptance.html.twig', [
            'method' => $companyLegacy->getStripeId() !== null ?  'PATCH' :  'POST',
            'user' => $user,
            'personVerified' => isset($person) && $person->verification->status === 'verified',
            'company' => $company,
            'country' => $country,
            'addressLine1' => $addressLine1,
            'addressLine2' => $addressLine2,
            'files' => $files,
            'stripePublicKey' => $this->getParameter('stripe.public_token'),
            'stripeApiVersion' => $this->get(StripeConfig::class)->getStripeApiVersion(),
        ]);
    }

    public function handleStripeTosAcceptanceAction(Request $request): Response
    {
        $accountToken = $request->request->get('account_token');
        $personToken = $request->request->get('person_token');
        $company = new Company(Registry::defaultInstance()->get(['runtime', 'company_id']));

        try {
            if ($request->isMethod('POST')) {
                $this->get('marketplace.payment.stripe')->createCustomAccount($company, $accountToken, $personToken);
            } else {
                $this->get('marketplace.payment.stripe')->updateCustomAccount($company, $accountToken, $personToken);
            }
            fn_set_notification('N', __('notice'), __('text_changes_saved'));
        } catch (\Exception $e) {
            fn_set_notification('E', __('error'), __('stripe_create_custom_account_fail') . $e->getMessage());
            $this->get('logger')->error('Stripe account creation failure: ' . $e->getMessage(), ['exception' => $e]);
        }

        return $this->redirect($request->getBaseUrl());
    }
}
