<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Backend;

use Symfony\Component\HttpFoundation\Request;
use Wizacha\Discuss\Entity\Discussion\Status;
use Wizacha\Discuss\Entity\DiscussionInterface;
use Wizacha\Marketplace\MessageAttachment\MessageAttachmentService;
use Wizacha\User;
use Wizacha\Marketplace\User\User as Client;

class DiscussController extends BackendController
{
    /** @var MessageAttachmentService */
    private $attachmentService;

    public function __construct(MessageAttachmentService $attachmentService)
    {
        $this->attachmentService = $attachmentService;
    }

    public function listAction()
    {
        $user = $this->getUser();
        if (!$user->isProfessionalVendor()) {
            throw $this->createAccessDeniedException();
        }

        $discussService = $this->get('app.discuss_service');
        $discussionRepo = $discussService->getDiscussClient()->getDiscussionRepository();
        $userId = $user->getId();
        $messagesRepo = $discussService->getDiscussClient()->getMessageRepository();

        $discussions = $discussionRepo
            ->getByCompanyId($user->getCompanyId())
        ;

        $interlocutors = [];
        $lastMessages = [];

        foreach ($discussions as $discussion) {
            if ($discussion->getInitiator() != $userId) {
                $user = $this->getDoctrine()->getManager()->find(Client::class, $discussion->getInitiator());
                $fullName = $user ? $user->getFullName() : __("user_unknown");
            } elseif (\is_string($discussion->getMetaData('customer_id')) === true) {
                $user = $this->getDoctrine()->getManager()->find(Client::class, $discussion->getMetaData('customer_id'));
                $fullName = $user ? $user->getFullName() : __("user_unknown");
            } else {
                $fullName = (new User($discussion->getRecipient()))->getPseudo();
            }
            $interlocutors[$discussion->getId()] = $fullName ?? __("user_unknown");
            $lastMessages[$discussion->getId()] = $messagesRepo->getLastOfDiscussion($discussion->getId());
        }

        return $this->render('@App/backend/discuss/list.html.twig', [
            'lastMessages' => $lastMessages,
            'interlocutors' => $interlocutors,
            'discussions' => $discussions,
            'csrfToken' => $this->get('security.csrf.token_manager')->getToken('discussHideLink')->getValue(),
        ]);
    }

    public function viewAction(int $discussionId)
    {
        $user = $this->getUser();
        if (!$user->isProfessionalVendor()) {
            throw $this->createAccessDeniedException();
        }

        if (!$discussionId) {
            throw $this->createNotFoundException();
        }

        $discussService = $this->get('app.discuss_service');
        $discussionClient = $discussService->getDiscussClient();

        $discussion = $discussionClient->getDiscussionRepository()->get($discussionId);

        $canReadMessage = false;
        if ((int) $discussion->getMetaData('company_id') === $user->getCompanyId()
            || \in_array($user->getId(), $discussion->getUsers()) === true
        ) {
            $canReadMessage = true;
        }

        if ($canReadMessage === false) {
            throw $this->createNotFoundException();
        }

        $messages = $discussionClient->getMessageRepository()->getByDiscussion($discussion->getId());
        $interlocutors = [];
        $isCompany = [];
        foreach ($messages as $message) {
            if ($message->getAuthor() != $user->getId()) {
                $message->setAsRead();
                $discussionClient->getMessageRepository()->save($message);
            }
            $interlocutors[$message->getAuthor()] = (new User($message->getAuthor()))->getFullname() ?: $message->getAuthor();
            $author = new User($message->getAuthor());
            $isCompany[$message->getId()] = (int) $message->getDiscussion()->getMetaData('company_id') ===  $author->getCompanyId();
        }

        return $this->render('@App/backend/discuss/view.html.twig', [
            'userId' => $user->getId(),
            'discussionId' => $discussionId,
            'messages' => $messages,
            'interlocutors' => $interlocutors,
            'isCompany' => $isCompany,
            'productId' => $discussion->getMetaData('product_id'),
            'title' => $discussion->getMetaData('title'),
            'csrfToken' => $this->get('security.csrf.token_manager')->getToken('discussPostMessage'),
            'validateExtension' => MessageAttachmentService::VALIDATE_ATTACHMENT_MESSAGE_EXTENSIONS,
            'formInOrderDetails' => false
        ]);
    }

    public function postAction(Request $request)
    {
        $user = $this->getUser();
        if (!$user->isProfessionalVendor()) {
            throw $this->createAccessDeniedException();
        }

        if (!$request->isMethod('POST')) {
            return $this->redirectToRoute('admin_Discuss_list');
        }

        $discussionId = $request->request->getInt('discussionId');

        if (!$discussionId) {
            throw $this->createNotFoundException();
        }

        $discussService = $this->get('app.discuss_service');
        $discussionClient = $discussService->getDiscussClient();

        $discussion = $discussionClient->getDiscussionRepository()->get($discussionId);

        $canPostMessage = false;
        if ((int) $discussion->getMetaData('company_id') === $user->getCompanyId()
            || \in_array($user->getId(), $discussion->getUsers()) === true
        ) {
            $canPostMessage = true;
        }

        if ($canPostMessage === false) {
            throw $this->createNotFoundException();
        }

        if (!$this->isCsrfTokenValid('discussPostMessage', $request->request->get('token'))) {
            fn_set_notification('E', __('error'), __('invalid_csrf_token'));
            throw $this->createNotFoundException();
        }

        $message = $discussionClient->getMessageRepository()->create();
        $message->setSendDate(new \DateTime());
        $message->setDiscussion($discussion);
        $message->setAuthor($user->getId());
        $message->setContent(nl2br($request->request->get('content')));
        $discussionClient->getMessageRepository()->save($message);

        $files = $request->files->get('files');
        if (\is_array($files) === true && \count($files) > 0) {
            $this->attachmentService->create($files, $message->getId());
        }

        if ($request->request->has('formInOrderDetails')
            && (bool) $request->request->get('formInOrderDetails') === true
        ) {
            return $this->redirect(
                fn_url(
                    'orders.details&order_id=' .
                    $discussion->getMetaData('order_id') .
                    '&selected_section=discussion'
                )
            );
        }

        return $this->redirectToRoute('admin_Discuss_view', ['discussionId' => $discussionId]);
    }

    public function hideAction(int $discussionId, Request $request)
    {
        $user = $this->getUser();
        if (!$user->isProfessionalVendor()) {
            throw $this->createAccessDeniedException();
        }

        if (!$discussionId) {
            throw $this->createNotFoundException();
        }

        $discussService = $this->get('app.discuss_service');
        $discussionClient = $discussService->getDiscussClient();

        $discussion = $discussionClient->getDiscussionRepository()->get($discussionId);

        if ($discussion instanceof DiscussionInterface === false
            || \intval($discussion->getMetaData('company_id')) !== $user->getCompanyId()
        ) {
            throw $this->createNotFoundException();
        }

        if (!$this->isCsrfTokenValid('discussHideLink', $request->query->get('token'))) {
            fn_set_notification('E', __('error'), __('invalid_csrf_token'));
            throw $this->createNotFoundException();
        }

        $discussion->hideDiscussion();
        $discussionClient->getDiscussionRepository()->save($discussion);

        return $this->redirectToRoute('admin_Discuss_list');
    }
}
