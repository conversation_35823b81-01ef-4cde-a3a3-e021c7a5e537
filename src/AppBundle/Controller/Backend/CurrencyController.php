<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Backend;

use Symfony\Component\HttpFoundation\JsonResponse;
use Tygh\Registry;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;
use Wizacha\Marketplace\Currency\CurrencyService;
use Wizacha\Marketplace\Currency\Exception\CurrencyNotFound;

class CurrencyController extends BackendController
{
    /** @var CurrencyService */
    private $currencyService;

    public function __construct(CurrencyService $currencyService)
    {
        $this->currencyService = $currencyService;
    }

    /**
     * @param Request $request
     * @return Response
     */
    public function listAction(Request $request): Response
    {
        if ($this->getUser() === null) {
            return $this->redirect(fn_url("auth.login_form?return_url=" . urlencode($request->getRequestUri())));
        }

        $this->mustBeAdmin();

        // access only if feature flag is active
        if ($this->currencyService->isFeatureCurrencyAdvancedActivated() === false
            || $this->isVendorBoInterface() === true
        ) {
            Registry::get('view')->assign('page_title', __('access_denied'));

            return $this->render('@App/backend/exception/exception.html.twig', [
                'exception_status' => Response::HTTP_FORBIDDEN,
                'localeSelector' => true,
            ], null)->setStatusCode(Response::HTTP_FORBIDDEN);
        }

        $currencies = $this->currencyService->list();
        $mainCurrency = $this->currencyService->getDefaultCurrency();
        Registry::get('view')->assign('page_title', __('currencies_management'));
        return $this->render('@App/backend/currency/list.html.twig', [
            'currencies' => $currencies,
            'mainCurrency' => $mainCurrency
        ]);
    }

    public function updateStatusAction(Request $request, string $code, bool $status): Response
    {
        $this->mustBeAdmin();

        $changeStatusConditions = true;
        $response = [];

        if ($this->currencyService->isFeatureCurrencyAdvancedActivated() === false
            || $this->isVendorBoInterface() === true
        ) {
            fn_set_notification('E', __('error'), __('access_denied'));
            $changeStatusConditions = false;
            $response['return_status'] = $status === true ? 'd' : 'a';
        }

        try {
            $currency = $this->currencyService->getCurrency($code);
        } catch (CurrencyNotFound $exception) {
            fn_set_notification('E', __('error'), __('currency_not_found'));
            $changeStatusConditions = false;
            $response['return_status'] = $status === true ? 'd' : 'a';
        }

        if ($changeStatusConditions === true) {
            $fromStatus = $currency->isEnabled() === true ? 'a' : 'd';
            $currency->setEnabled($status);

            try {
                $this->currencyService->save($currency);
                fn_set_notification('N', __('notice'), __('status_changed'));
            } catch (\Exception $exception) {
                fn_set_notification('E', __('error'), __('error_status_not_changed'));
                $response['return_status'] = $fromStatus;
            }
        }

        if ($request->isXmlHttpRequest() === true) {
            $response['notifications'] = $_SESSION['notifications'];
            return new JsonResponse($response);
        }

        return $this->redirectToRoute('currency_management');
    }
}
