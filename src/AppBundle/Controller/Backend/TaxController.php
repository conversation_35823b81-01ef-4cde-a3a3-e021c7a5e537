<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Backend;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Wizacha\Marketplace\Tax\Exception\TaxNotFoundException;
use Wizacha\Marketplace\Tax\InternationalTaxService;

class TaxController extends BackendController
{
    private InternationalTaxService $internationalTaxService;

    public function __construct(InternationalTaxService $internationalTaxService)
    {
        $this->internationalTaxService = $internationalTaxService;
    }

    public function updateCommissionTaxAction(Request $request): JsonResponse
    {
        if ($this->getUser()->isAdmin() === false) {
            throw $this->createAccessDeniedException();
        }

        try {
            $this->internationalTaxService->setCommissionTax($request->get('taxId'));
            return new JsonResponse(['success' => true, 'message' => __('bo_tax_manage.commissions_updated')]);
        } catch (TaxNotFoundException $e) {
            return new JsonResponse(
                ['success' => false, 'message' => __('taxes_manage_not_found')],
                404
            );
        }
    }

    public function updateShippingTaxAction(Request $request): JsonResponse
    {
        if ($this->getUser()->isAdmin() === false) {
            throw $this->createAccessDeniedException();
        }

        $taxId = $request->get('taxId');
        $countryCode = $request->get('countryCode');

        if ($countryCode !== null && $taxId !== null) {
            $this->internationalTaxService->updateShippingTax(
                (int) $taxId,
                $countryCode
            );

            return new JsonResponse(['success' => true, 'message' => __('taxes_manage_shipping_updated')]);
        } else {
            return new JsonResponse(
                ['success' => false, 'message' => __('taxes_manage_shipping_updated_error')],
                400
            );
        }
    }

    public function deleteShippingTaxAction(string $countryCode): JsonResponse
    {
        if ($this->getUser()->isAdmin() === false) {
            throw $this->createAccessDeniedException();
        }

        try {
            $this->internationalTaxService->deleteShippingTax($countryCode);
            return new JsonResponse(['success' => true, 'message' => __('taxes_manage_shipping_deleted')]);
        } catch (TaxNotFoundException $e) {
            return new JsonResponse(
                ['success' => false, 'message' => __('taxes_manage_not_found')],
                404
            );
        }
    }
}
