<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Backend;

use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Form;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Wizacha\Company;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\FinancialFlowsHistory\FinancialFlowsHistoryService;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\Refund\Repository\RefundRepository;
use Wizacha\Marketplace\Transaction\Form\CommissionTransactionType;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionStatus;
use Wizacha\Marketplace\Transaction\TransactionType;
use Wizacha\Registry;

class FinancialFlowsHistoryController extends BackendController
{
    private TransactionService $transactionService;
    private string $currencyCode;
    private OrderService $orderService;
    private RefundRepository $refundRepository;
    private FinancialFlowsHistoryService $financialFlowsHistoryService;

    // Sets the number of items to display on a single pag
    private const MAX_ITEM_PER_PAGE = 10;

    public function __construct(
        TransactionService $transactionService,
        string $currencyCode,
        OrderService $orderService,
        RefundRepository $refundRepository,
        FinancialFlowsHistoryService $financialFlowsHistoryService
    ) {
        $this->transactionService = $transactionService;
        $this->currencyCode = $currencyCode;
        $this->orderService = $orderService;
        $this->refundRepository = $refundRepository;
        $this->financialFlowsHistoryService = $financialFlowsHistoryService;
    }

    public function listAction(Request $request): Response
    {
        // Create Form
        $form = $this->getFilterForm();

        $filters = [];

        // Pagination page
        $page = 1;

        // Number of item per page
        $resultPerPage = self::MAX_ITEM_PER_PAGE;

        if ($request->isMethod('POST')  === true) {
            $form->handleRequest($request);
            $formsData = $form->getData();
            $filters = $this->getFilterData($formsData);
            $page = $formsData['page'];
            $resultPerPage = $formsData['items_per_page'];
        } else {
            /**
             * Method [GET]
             */
            // Create a date filter that defaults to today
            $filters['period_start'] = $filters['period_end'] = date("Y-m-d");

            $companyId = Company::runtimeID(AREA, $_SESSION, Registry::defaultInstance());
            if ($companyId > 0) {
                $filters['companies'] = $companyId;
            }
        }

        if (\array_key_exists('period_start', $filters) === true) {
            $filters['period_start'] .= ' 00:00:00';
        }

        if (\array_key_exists('period_end', $filters) === true) {
            $filters['period_end'] .= ' 23:59:59';
        }

        // Btn export clicked
        if ($form->get('export')->isClicked() === true) {
            // Find transactions without pagination
            $transactions = $this->transactionService->findExternalTransferTransactions($filters);

            return $this->doExport($transactions->getQuery()->getResult());
        }

        // Find transactions
        $transactions = $this->transactionService->findExternalTransferTransactions(
            $filters,
            (int) $page,
            (int) $resultPerPage
        );

        // Return to page 1 if current page exceed number of result
        if (\ceil($transactions->count() / $resultPerPage) < $page) {
            $page = 1;
            $transactions = $this->transactionService->findExternalTransferTransactions(
                $filters,
                (int) $page,
                (int) $resultPerPage
            );
        }

        return $this->render('@App/backend/transaction/transaction_view.html.twig', [
            'form' => $form->createView(),
            'transactions' => $transactions,
            'total' => $transactions->count(),
            'lastPage' => \ceil($transactions->count() / $transactions->getQuery()->getMaxResults()),
            'currentPage' => $page,
            'items_per_page' => $resultPerPage,
            'filters' => $filters
        ]);
    }

    public function commissionListAction(Request $request): Response
    {
        $form = $this->createForm(CommissionTransactionType::class);
        $form->handleRequest($request);
        if ($form->isSubmitted() === true && $form->isValid() === true) {
            $formsData = $form->getData();
            $filters = $this->getFilterData($formsData);
            $commissions = $this->financialFlowsHistoryService->getCommissionsByFilters($filters);

            return $this->financialFlowsHistoryService->doCommissionExport($commissions);
        }

        return $this->render('@App/backend/transaction/transaction_view.html.twig', [
            'form' => $form->createView(),
            'transactions' => $commissions ?? [],
            'navigation.subsection' => 'commission_monitoring',
            'filters' => []
        ]);
    }

    private function getFilterForm(): Form
    {
        $form = $this->createFormBuilder();

        $companyId = Company::runtimeID(AREA, $_SESSION, Registry::defaultInstance());
        $companyData = fn_get_company_data($companyId);

        if (\is_array($companyData) === false) {
            $isCompanyDisabled = false;
            $companies = null;
            $statement = $this->get(CompanyService::class)->getAllVendorsExceptNew();
            while ($company = $statement->fetch()) {
                $companies[$company['company']] = $company['company_id'];
            }
        } else {
            $isCompanyDisabled = true;
            $companies[$companyData['company']] = $companyData['company_id'];
        }

        $form->add(
            'companies',
            ChoiceType::class,
            [
                'choices' => $companies,
                'multiple' => true,
                'required' => $isCompanyDisabled
            ]
        );

        $form->add('period_start', HiddenType::class);
        $form->add('period_end', HiddenType::class);
        $form->add(
            'page',
            HiddenType::class,
            [
                'data' => 1,
            ]
        );

        $form->add(
            'items_per_page',
            HiddenType::class,
            [
                'data' => self::MAX_ITEM_PER_PAGE,
            ]
        );

        $transactionTypesKeys = TransactionType::toArray();

        $transactionTypesValues = \array_map(
            static function (string $transactionType): string {
                return \mb_strtoupper(__('transaction_' . \strtolower($transactionType)));
            },
            $transactionTypesKeys
        );

        $transactionTypes =  \array_combine($transactionTypesValues, $transactionTypesKeys);

        $form->add(
            'type',
            ChoiceType::class,
            [
                'choices' => $transactionTypes,
                'multiple' => true,
                'required' => false,
                'choice_translation_domain' => false,
            ]
        );

        $form->add(
            'status',
            ChoiceType::class,
            [
                'required' => true,
                'choices' => TransactionStatus::toArray(),
                'multiple' => true,
                'choice_translation_domain' => false,
            ]
        );

        // Btn search
        $form->add(
            'search',
            SubmitType::class,
            [
                'label' => 'w_search_button',
                'attr' => ['class' => 'btn btn-primary btn-export'],
            ]
        );

        // Btn export
        $form->add(
            'export',
            SubmitType::class,
            [
                'label' => 'export',
                'attr' => ['class' => 'btn btn-primary btn-export'],
            ]
        );

        return $form->getForm();
    }

    private function getFilterData($formsData): array
    {
        $filters['period_start'] = $formsData['period_start'];
        $filters['period_end'] = $formsData['period_end'];

        if (\count($formsData['companies']) > 0) {
            $filters['companies'] = \array_map(
                function ($value) {
                    return (int) $value;
                },
                $formsData['companies']
            );
        }

        if (\is_array($formsData['type']) === true && \count($formsData['type']) > 0) {
            $filters['type'] = $formsData['type'];
        } else {
            $filters['type'] = TransactionType::toArray();
        }

        if (\is_array($formsData['status']) === true && \count($formsData['status']) > 0) {
            $filters['status'] = $formsData['status'];
        }

        return $filters;
    }

    private function doExport(array $transactions): Response
    {
        $header = [
            __('table_transaction_header_date'),
            __('table_transaction_header_type'),
            __('table_transaction_header_origin'),
            __('table_transaction_header_destination'),
            __('table_transaction_header_transaction_id'),
            __('table_transaction_header_amount'),
            __('table_transaction_header_currency'),
            __('table_transaction_header_status'),
            __('table_transaction_header_order_id'),
            __('table_transaction_header_additional_information'),
        ];

        $fp = fopen('php://output', 'w');
        fputcsv($fp, $header, ';');
        foreach ($transactions as $transaction) {
            $transactionData = $transaction->exposeTransactionHistory();
            fputcsv(
                $fp,
                [
                    $transactionData['transactionDate'],
                    \mb_strtoupper(__('transaction_' . \mb_strtolower($transactionData['type']), 'en')), // type always displayed in english
                    $transactionData['origin'],
                    $transactionData['destination'],
                    $transactionData['transactionReference'],
                    $transactionData['amount'],
                    $transactionData['currency'] !== null && \mb_strlen($transactionData['currency']) > 0
                        ? $transactionData['currency']
                        : $this->currencyCode,
                    $transactionData['status'],
                    $transactionData['order_id'],
                    $transactionData['processorInformation']['additional_info'],
                ],
                ';'
            );
        }

        fclose($fp);

        $response = new Response();
        $response->headers->set('Content-Type', 'text/csv');
        $response->headers->set('Content-Disposition', 'attachment; filename="transactions.csv"');

        return $response;
    }
}
