<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Backend;

use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Wizacha\Marketplace\CompanyPerson\CompanyPerson;
use Wizacha\Marketplace\CompanyPerson\CompanyPersonService;
use Wizacha\Marketplace\CompanyPerson\CompanyPersonTrait;
use Wizacha\Marketplace\CompanyPerson\CompanyPersonType;
use Wizacha\Marketplace\CompanyPerson\CompanyPersonUBO;
use Wizacha\Marketplace\CompanyPerson\Event\UBOSubmitted;
use Wizacha\Marketplace\CompanyPerson\Form\CompanyPersonUBOType;
use Tygh\Registry;
use Wizacha\Marketplace\Exception\CompanyNotFound;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Payment\Processor\MangoPay;
use Wizacha\Marketplace\PSP\UboMangopay\UboMangopayService;
use Wizacha\Registry as WizRegistry;
use Wizacha\Marketplace\Company\CompanyService;

class MangopayController extends BackendController
{
    use CompanyPersonTrait;

    private MangoPay $mangoPay;
    protected CompanyPersonService $companyPersonService;
    private LoggerInterface $logger;
    private UboMangopayService $uboMangopayService;

    public function __construct(
        MangoPay $mangoPay,
        CompanyPersonService $companyPersonService,
        LoggerInterface $logger,
        UboMangopayService $uboMangopayService
    ) {
        $this->mangoPay = $mangoPay;
        $this->companyPersonService = $companyPersonService;
        $this->logger = $logger;
        $this->uboMangopayService = $uboMangopayService;
    }

    public function mangoPayInscriptionAction(Request $request): Response
    {
        if ($this->getUser() === null) {
            return $this->redirect(fn_url("auth.login_form?return_url=" . urlencode($request->getRequestUri())));
        }

        $companyId = WizRegistry::defaultInstance()->get(['runtime', 'company_id']);

        if ($companyId === 0) {
            Registry::get('view')->assign('page_title', __('access_denied'));

            return $this->render('@App/backend/exception/exception.html.twig', [
                'exception_status' => Response::HTTP_FORBIDDEN,
            ])->setStatusCode(Response::HTTP_FORBIDDEN);
        }

        try {
            $company = $this->get(CompanyService::class)->get($companyId);
        } catch (CompanyNotFound $ex) {
            return $this->render('@App/backend/exception/exception.html.twig', [
                'exception_status' => Response::HTTP_NOT_FOUND,
            ])->setStatusCode(Response::HTTP_NOT_FOUND);
        }

        if ($this->mangoPay->isConfigured() === false
            || ($this->getUser()->isAdmin() === false
            && $this->getUser()->isVendor() === false)
            || $company->isPrivateIndividual() === true
        ) {
            Registry::get('view')->assign('page_title', __('access_denied'));

            return $this->render('@App/backend/exception/exception.html.twig', [
                'exception_status' => Response::HTTP_FORBIDDEN,
            ])->setStatusCode(Response::HTTP_FORBIDDEN);
        }

        $isUBOSubmitted = $this->uboMangopayService->isUBOSubmitted($companyId);
        $mangoPayDeclarationId = null;
        $companyPersonUBO = new CompanyPersonUBO();

        $companyPersonList = $this->companyPersonService->getByCompanyId($companyId);

        if (\count($companyPersonList) > 0) {
            $canSubmitToMangoPay = true;
            foreach ($companyPersonList as $companyPerson) {
                $companyPersonUBO->addCompanyPerson($companyPerson);
            }
            $mangoPayDeclarationId = $this->uboMangopayService->getDeclarationId($companyPersonList[0]);
        } else {
            $canSubmitToMangoPay = false;

            $companyPerson = new CompanyPerson();
            $companyPerson->setFirstName($company->getLegalRepresentativeFirstName());
            $companyPerson->setLastName($company->getLegalRepresentativeLastName());

            $companyPersonUBO->addCompanyPerson($companyPerson);
        }

        $formIsDisabled = $this->getUser()->isAdmin() === true || $isUBOSubmitted === true;
        $options = [
            'formIsDisabled' => $formIsDisabled,
            'timezone' => 'UTC'
        ];

        if ($this->getUser()->isVendor() === true && $isUBOSubmitted === false) {
            Registry::get('view')->assign('buttons', $this->renderView('@App/backend/mangopay/save_buttons.html.twig', [
                'canSubmitToMangoPay' => $canSubmitToMangoPay,
                'companyId' => $companyId,
            ]));
        }

        $form = $this->createForm(CompanyPersonUBOType::class, $companyPersonUBO, $options);

        $form->handleRequest($request);
        if ($form->isSubmitted() === true && $form->isValid() === true) {
            $companyPersonUBO = $form->getData();
            foreach ($companyPersonUBO->getCompanyPersons() as $companyPerson) {
                $companyPerson->setType(CompanyPersonType::OWNER());
                $companyPerson->setCompanyId($companyId);

                try {
                    $this->validateOwnershipPercentage($companyId, $companyPerson);
                } catch (BadRequestHttpException $e) {
                    fn_set_notification('E', __('error'), $e->getMessage());

                    return $this->redirectToRoute('company_mangopay_inscription');
                }

                $this->companyPersonService->save($companyPerson);
            }

            fn_set_notification('N', __('successful'), __('text_changes_saved'));
            return $this->redirect($request->getUri());
        }

        return $this->render('@App/backend/mangopay/inscription.html.twig', [
            'form' => $form->createView(),
            'mangoPayDeclarationId' => $isUBOSubmitted === true ? $mangoPayDeclarationId : null,
            'formIsDisabled' => $formIsDisabled,
            'companyPersonUBO' => $companyPersonUBO,
            'navigation.subsection' => 'mangopay_inscription',
        ]);
    }

    public function deleteMangoPayInscriptionAction(int $id): Response
    {
        try {
            $this->companyPersonService->delete($id);
            fn_set_notification('N', __('successful'), __('ubo_deleted'));
        } catch (NotFound $e) {
            fn_set_notification('E', __('error'), $e->getMessage());
        }

        return $this->redirectToRoute('company_mangopay_inscription');
    }

    public function submitMangoPayUBOAction(int $companyId): Response
    {
        try {
            $this->mangoPay->submitUBO(new UBOSubmitted($companyId));
            fn_set_notification('N', __('successful'), __('submit_ubo_response'));
        } catch (\Exception $e) {
            $this->logger->error(
                '[Mangopay][UBO]: Cannot submit UboDeclaration',
                [
                    'exception' => $e,
                    'userId' => $companyId,
                ]
            );

            fn_set_notification('E', __('error'), $e->getMessage());
        }

        return $this->redirectToRoute('company_mangopay_inscription');
    }
}
