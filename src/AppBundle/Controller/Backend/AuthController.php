<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Backend;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Tygh\Session;
use Wizacha\AppBundle\Security\Exception\VendorHasNoCompanyException;
use Wizacha\AppBundle\Security\User\AuthorizationChecker;
use Wizacha\Component\AuthLog\AuthLogRepository;
use Wizacha\Component\AuthLog\DestinationType;
use Wizacha\Component\AuthLog\SourceType;
use Wizacha\Component\AuthLog\StatusType;
use Wizacha\Component\OAuth\Provider;
use Wizacha\Component\OAuth\Token;
use Wizacha\MarketPlace\User\User;

class AuthController extends BackendController
{
    private $authLogRepository;

    /** @var Provider */
    protected $provider;

    public function __construct(AuthLogRepository $authLogRepository, Provider $provider)
    {
        $this->authLogRepository = $authLogRepository;
        $this->provider = $provider;
    }

    public function oauthAuthorizeAction(Request $request): Response
    {
        if (!$request->query->has('code')) {
            return fn_redirect('auth.login_form');
        }

        $user = $this->provider->authorize(new Token($request->query->get('code')));

        try {
            $checker = new AuthorizationChecker();
            $checker->check($user);
        } catch (VendorHasNoCompanyException $e) {
            fn_set_notification('E', __('error'), __('error_vendor_access_denied'));

            $this->authLogRepository->save(
                $user->getEmail(),
                StatusType::ACCESS_DENIED(),
                SourceType::SSO(),
                DestinationType::VENDOR_BACKOFFICE()
            );

            return $this->redirect(fn_url('auth.login_form'));
        }

        if ($user->isPending() === true) {
            fn_set_notification('E', __('error'), __('error_account_pending'));

            $this->authLogRepository->save(
                $user->getEmail(),
                StatusType::ACCESS_DENIED(),
                SourceType::SSO(),
                DestinationType::VENDOR_BACKOFFICE()
            );

            return $this->redirect(fn_url('auth.login_form'));
        }

        if ($user->isEnabled() === false) {
            fn_set_notification('E', __('error'), __('error_account_disabled'));

            $this->authLogRepository->save(
                $user->getEmail(),
                StatusType::ACCESS_DENIED(),
                SourceType::SSO(),
                DestinationType::VENDOR_BACKOFFICE()
            );

            return $this->redirect(fn_url('auth.login_form'));
        }

        if ($user->getUserType() === 'V') {
            return $this->authorizeVendor($user);
        }

        // AuthLogs
        $this->authLogRepository->save(
            $user->getEmail(),
            StatusType::SUCCESS(),
            SourceType::SSO(),
            DestinationType::ADMINISTRATOR_BACKOFFICE()
        );

        fn_login_user($user->getUserId());

        return $this->redirect(fn_url('admin.php'));
    }

    private function authorizeVendor(User $user): Response
    {
        session_destroy();
        Session::setName('vendor');
        Session::start();
        fn_login_user($user->getUserId());

        // AuthLogs
        $this->authLogRepository->save(
            $user->getEmail(),
            StatusType::SUCCESS(),
            SourceType::SSO(),
            DestinationType::VENDOR_BACKOFFICE()
        );

        return $this->redirect(fn_url('vendor.php'));
    }
}
