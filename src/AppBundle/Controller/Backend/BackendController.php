<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Backend;

use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Tygh\BackendMenu;
use Tygh\Navigation\Breadcrumbs;
use Tygh\Registry;
use Wizacha\AppBundle\Controller\BaseController;
use Wizacha\Marketplace\GlobalState\GlobalState;

class BackendController extends BaseController
{
    /**
     * Use this shortcut when you want to render a template inside the
     * backoffice layout
     */
    protected function render(string $view, array $parameters = array(), Response $response = null): Response
    {
        $response = parent::render($view, $parameters, $response);
        $view = Registry::get('view');

        // Langue de la vue
        $view->assign('descr_sl', (string) GlobalState::contentLocale());

        // Génération de la navigation du BO
        list($static, $actions, $selectedItems) = BackendMenu::instance(
            Registry::get('runtime.controller'),
            Registry::get('runtime.mode'),
            Registry::get('runtime.action')
        )->generate($_REQUEST);

        Registry::set('navigation', array(
            'static' => $static,
            'dynamic' => array('actions' => $actions),
            'selected_tab' => $selectedItems['section'],
            'subsection' => $selectedItems['item'],
        ));

        // generate breadcrumbs
        $prevRequest = !empty($_SESSION['request_history']['prev']['params']) ? $_SESSION['request_history']['prev']['params'] : array();
        $breadcrumbs = Breadcrumbs::instance(Registry::get('runtime.controller'), Registry::get('runtime.mode'), AREA, $_REQUEST, $prevRequest)->getLinks();
        $view->assign('breadcrumbs', $breadcrumbs);

        $view->assign('content_tpl', 'views/bundle/layout.tpl');
        $view->assign('content', $response->getContent());

        if (\array_key_exists('navigation.subsection', $parameters) === true) {
            $view->assign('title', $parameters['navigation.subsection']);
        } else {
            $view->assign('title', Registry::get('navigation.subsection'));
        }

        if (\array_key_exists('localeSelector', $parameters)
            && $parameters['localeSelector'] === true
        ) {
            $view->assign('no_sidebar', false);
            $view->assign('select_languages', array_keys(fn_get_languages()));
        }

        if (\array_key_exists('sidebarView', $parameters)
            && \is_string($parameters['sidebarView']) === true
        ) {
            $view->assign('no_sidebar', false);
            $view->assign('sidebar', parent::render($parameters['sidebarView'], $parameters)->getContent());
        }

        $content = $view->fetch('index.tpl');

        return new Response($content);
    }

    /**
     * With symfony security, when throwing Symfony\Component\Security\Core\Exception\AccessDeniedException, the framework
     * will try to make some authentication based on firewall and throw a 401 instead
     */
    protected function createAccessDeniedException(string $message = 'Access Denied.', \Throwable $previous = null): AccessDeniedException
    {
        return new AccessDeniedException($message, $previous);
    }

    /**
     * Check that the logged in user must be an administrator.
     */
    protected function mustBeAdmin()
    {
        if (!$this->getUser()->isAdmin()) {
            fn_set_notification('E', __('error'), __('admin_only'));
            throw $this->createAccessDeniedException('Must be admin');
        }
    }

    /**
     * Check that the logged in user must be a vendor.
     */
    protected function mustBeVendor()
    {
        if (!$this->getUser()->isVendor()) {
            fn_set_notification('E', __('error'), __('vendor_only'));
            throw $this->createAccessDeniedException('Must be vendor');
        }
    }

    protected function isVendorBoInterface(): bool
    {
        return Registry::get('runtime.company_id') !== 0 ? true : false;
    }
}
