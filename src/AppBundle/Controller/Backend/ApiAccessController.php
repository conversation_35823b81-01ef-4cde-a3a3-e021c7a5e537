<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Backend;

class ApiAccessController extends BackendController
{
    public function listTokensAction()
    {
        return $this->render('@App/backend/api/tokens.html.twig', [
            'applicationToken' => $this->getParameter('api_application_token'),
            'applicationVersion' => $this->getParameter('marketplace.version')
        ]);
    }
}
