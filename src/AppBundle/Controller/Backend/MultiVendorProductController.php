<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Backend;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Tygh\Registry;
use Wizacha\FeatureFlag\FeatureFlaggableInterface;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Exception\IntegrityConstraintViolation;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Image\Exception\BadImage;
use Wizacha\Marketplace\Image\Image;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProductService;
use Wizacha\Marketplace\PIM\MultiVendorProduct\Status\Status;
use Wizacha\Marketplace\PIM\Product\Template\TemplateService;

/**
 * Class MultiVendorProductController
 * @package Wizacha\AppBundle\Controller\Backend
 * This controller is called by CS-Cart in AppBundle/Controller/CsCart/backend/bundle.php
 */
class MultiVendorProductController extends BackendController implements FeatureFlaggableInterface
{
    private CompanyService $companyService;

    public function __construct(CompanyService $companyService)
    {
        $this->companyService = $companyService;
    }

    public function listAction(Request $request)
    {
        $this->mustBeAdmin();
        $currentPage = $request->query->getInt('page', 1);
        $mvpService = $this->getMultiVendorProductService();

        $resultPerPage = $request->query->get('items_per_page', 50);

        $mvps = $request->query->has('search') && $request->query->get('search') !== ''
            ? $mvpService->search($request->query->get('search'), $currentPage, $resultPerPage)
            : $mvpService->list($currentPage, $resultPerPage)
        ;

        Registry::get('view')->assign('buttons', $this->renderView('@App/backend/mvp/list.buttons.html.twig'));

        return $this->render('@App/backend/mvp/list.html.twig', [
            'multiVendorProducts' => $mvps,
            'currentSearch' => $request->query->get('search'),
            'statuses' => $this->getStatuses(),
            'currentPage' => $currentPage,
            'lastPage' => ceil($mvps->count() / $mvps->getQuery()->getMaxResults()),
            'total' => $mvps->count(),
            'items_per_page' => $resultPerPage,
            'localeSelector' => true,
            'sidebarView' => '@App/backend/mvp/sidebar.list.html.twig',
        ], null);
    }

    public function syncAllFromProductAction(Request $request)
    {
        $this->mustBeAdmin();
        if (!$this->getParameter('feature.allow_mvp_sync_from_products')) {
            return $this->redirectToRoute('admin_MultiVendorProduct_list');
        }

        $this->csrfTokenMustBeValid('sync_all_mvp', $request->query->get('csrf'));
        $this->getMultiVendorProductService()->updateAllFromFirstProduct();
        fn_set_notification('N', '', __('processing_sync_all_mvp_from_product'));
        $this->addFlash('info', 'envoit');

        return $this->redirectToRoute('admin_MultiVendorProduct_list');
    }

    public function productSyncFromCatalogAction()
    {
        $this->mustBeVendor();

        if (!$this->getParameter('feature.pim_sync_product_from_mvp')) {
            throw $this->createAccessDeniedException();
        }

        $companyId = Registry::get('runtime.company_id');
        $rules = $this->get('marketplace.multi_vendor_product.product_synchronization.rules_service')->get($companyId);
        $categories = $this->get('marketplace.pim.category_service')->getCategories();

        // included categories
        $includedCategories = $this->formatCategories($categories, $rules->getIncludedCategories());

        // included brands
        $includedBrands = $this->formatBrands($rules->getIncludedBrands());

        // included mvps
        $includedProducts = $this->formatMvps($rules->getIncludedProducts());

        // excluded categories
        $excludedCategories = $this->formatCategories($categories, $rules->getExcludedCategories());

        // excluded brands
        $excludedBrands = $this->formatBrands($rules->getExcludedBrands());

        // excluded mvps
        $excludedProducts = $this->formatMvps($rules->getExcludedProducts());

        // API auth info
        $userId = $_SESSION['auth']['user_id'];
        $userData = fn_get_user_info($userId, false);

        return $this->render('@App/backend/mvp/synchronisation/form.html.twig', [
            "includedCategories" => $includedCategories,
            "includedBrands" => $includedBrands,
            "includedProducts" => $includedProducts,
            "excludedCategories" => $excludedCategories,
            "excludedBrands" => $excludedBrands,
            "excludedProducts" => $excludedProducts,
            "userApiKey" => $userData['api_key'],
        ]);
    }

    public function saveSyncRulesAction(Request $request)
    {
        $authorizationChecker = $this->get('security.authorization_checker');
        /** @var ApiSecurityUser $loggedInUser */
        $loggedInUser = $this->get('security.token_storage')->getToken()->getUser();

        // An vendor can access all users
        $this->denyAccessUnlessGranted('ROLE_VENDOR', null, 'Must be vendor');

        $data = json_decode($request->getContent(), true);

        if (!\is_array($data)) {
            throw new BadRequestHttpException('Request content must be a JSON');
        }

        $companyId = $loggedInUser->getCompanyId();
        $rules = $this->get('marketplace.multi_vendor_product.product_synchronization.rules_service')->get($companyId);
        $rules->setIncludedCategories($data['includedCategories']);
        $rules->setExcludedCategories($data['excludedCategories']);
        $rules->setIncludedBrands($data['includedBrands']);
        $rules->setExcludedBrands($data['excludedBrands']);
        $rules->setIncludedProducts($data['includedProducts']);
        $rules->setExcludedProducts($data['excludedProducts']);

        $this->get('marketplace.multi_vendor_product.product_synchronization.rules_service')->save($rules);

        return new Response('', Response::HTTP_OK);
    }

    public function removeLinkAction(Request $request)
    {
        $this->mustBeAdmin();
        $this->csrfTokenMustBeValid('remove_mvp_link', $request->query->get('csrf'));

        $mvpId = $request->query->get('mvp_id', null);
        if (\is_null($mvpId)) {
            throw new BadRequestHttpException('Missing mvp_id in query');
        }

        $productId = $request->query->get('product_id', null);
        if (\is_null($mvpId)) {
            throw new BadRequestHttpException('Missing product_id in query');
        }

        $mvpService = $this->getMultiVendorProductService();
        if ($mvpService->detach($mvpId, $productId) !== true) {
            throw NotFound::fromId('Link', $productId . ' / ' . $mvpId);
        }

        fn_set_notification('N', __('notice'), __('text_product_has_been_deleted'));

        return $this->redirectToRoute('admin_MultiVendorProduct_form', ['id' => $mvpId]);
    }

    public function deleteAction(Request $request)
    {
        $this->mustBeAdmin();
        $this->csrfTokenMustBeValid('delete_mvp', $request->query->get('csrf'));

        $id = $request->query->get('id', null);
        if (\is_null($id)) {
            throw new BadRequestHttpException('Missing id in query');
        }

        $mvpService = $this->getMultiVendorProductService();

        try {
            $mvpService->delete($id);
            fn_set_notification('N', __('notice'), __('text_product_has_been_deleted'));
        } catch (NotFound $e) {
            fn_set_notification('W', __('warning'), __('object_not_found', array('[object]' => __('multi_vendor_product'))));
        }

        return $this->redirectToRoute('admin_MultiVendorProduct_list');
    }

    public function batchDeleteAction(Request $request): Response
    {
        $this->mustBeAdmin();
        $this->csrfTokenMustBeValid('delete_mvp', $request->request->get('csrf'));

        $mvpService = $this->getMultiVendorProductService();
        $selectedMvpProducts = $request->request->get('selected_mvp_products_ids', '');

        $mvpIds = [];
        if ($selectedMvpProducts !== '') {
            $mvpIds = \explode(',', $selectedMvpProducts);
        }
        foreach ($mvpIds as $mvpId) {
            try {
                $mvpService->delete($mvpId);
            } catch (NotFound $e) {
                // already removed in another tab I guess
            }
        }

        fn_set_notification('N', __('notice'), __('text_product_has_been_deleted'));

        return $this->redirectToRoute('admin_MultiVendorProduct_list');
    }

    public function formAction(Request $request): Response
    {
        $this->mustBeAdmin();

        // Default values
        $productData = [
            'status' => MultiVendorProductService::DEFAULT_STATUS,
            'productTemplateType' => null,
            'video' => [
                'id' => null,
            ],
        ];

        if ($id = $request->query->get('id', null)) {
            try {
                if ($mvp = $this->getMultiVendorProductService()->get($id)) {
                    // Actual values from given ID
                    $productData = $mvp->expose($this->getMultiVendorProductService());
                    $productDataPLaceholder = $mvp->exposeTranslatedData($this->getMultiVendorProductService());
                }
            } catch (NotFound $notFound) {
                Registry::get('view')->assign('page_title', __('page_not_found'));

                return $this->render('@App/backend/exception/exception.html.twig', [
                    'exception_status' => Response::HTTP_NOT_FOUND,
                    'localeSelector' => true,
                ], null)->setStatusCode(Response::HTTP_NOT_FOUND);
            }
        }

        // Gestion des images
        $images = [];
        if ($mvp instanceof MultiVendorProduct) {
            foreach ($this->getMultiVendorProductService()->getValidImageIds($mvp->getImageIds()) as $imageId) {
                $images[] = new Image($imageId, $this->getMultiVendorProductService()->getImageAltText($imageId, (string) GlobalState::contentLocale()));
            }
        }

        // Free attributes
        $freeAttributes = [];
        if (!empty($id)) {
            foreach ($this->getMultiVendorProductService()->getFreeLegacyAttributes($id) as $name => $values) {
                foreach ($values as $value) {
                    $freeAttributes[] = ['name' => $name, 'value' => $value];
                }
            }
        }

        $productsIds = [(string) $id];
        if ($mvp instanceof MultiVendorProduct === true) {
            foreach ($mvp->getProductIds() as $productId) {
                $productsIds[] = (string) $productId;
            }

            $links = [];
            foreach ($mvp->getLinks() as $link) {
                $links[] = [
                    "productId"   => $link->getProduct()->getId(),
                    "productName" => $link->getProduct()->getName(),
                    "productCode" => $link->getProduct()->getCode(),
                    "productStatus" => $link->getProduct()->getStatus(),
                    "supplierRef" => $link->getProduct()->getSupplierRef(),
                    "companyId"   => $link->getProduct()->getCompanyId(),
                    "companyName" => $this->companyService->get($link->getProduct()->getCompanyId())->getName()
                ];
            }
        }
        Registry::get('view')->assign('buttons', $this->renderView('@App/backend/mvp/save_buttons.html.twig'));

        return $this->render('@App/backend/mvp/form.html.twig', [
            'statuses' => $this->getStatuses(),
            'multiVendorProduct' => $productData,
            'multiVendorProductPlaceholder' => $productDataPLaceholder,
            'images' => $images,
            'attributes' => $id ? $this->getMultiVendorProductService()->getDetailedLegacyAttributes($id, false, false) : [],
            'freeAttributes' => $freeAttributes,
            'links' => $links,
            'posts' => $this->get('marketplace.review.product.service')->getReviewsForModeration($productsIds),
            'templates' => $this->get(TemplateService::class)->getTemplates(),
            'selectedSection' => $request->query->get('selectedSection'),
            'localeSelector' => true,
            'navigation.subsection' => $productData['name'],
        ], null);
    }

    public function saveAction(Request $request)
    {
        $this->mustBeAdmin();

        $mvp = $this->save($request);
        $router = $this->get('router');
        if ($request->request->get('return_to_list') === 'Y') {
            $redirectURL = $router->generate('admin_MultiVendorProduct_list');
        } else {
            $redirectURL = $mvp ? $router->generate('admin_MultiVendorProduct_form', ['id' => $mvp->getId()]) : $router->generate('admin_MultiVendorProduct_form');
        }

        return $this->redirect($redirectURL);
    }

    public function searchAction(Request $request)
    {
        $query = $request->query->get('query');
        $mvps = $this->getMultiVendorProductService()->search($query);
        $results = [
            'data' => [],
        ];

        foreach ($mvps as $mvp) {
            $results['data'][$mvp->getId()] = $mvp->getName();
        }

        return new JsonResponse($results);
    }

    public function approveReviewAction(Request $request)
    {
        $this->mustBeAdmin();

        $mvpId = $request->query->get('mvpId', null);
        $reviewId = $request->query->get('reviewId', null);

        if (\is_null($mvpId)) {
            throw IntegrityConstraintViolation::isMissing('mvpId');
        }

        if (\is_null($reviewId)) {
            throw IntegrityConstraintViolation::isMissing('reviewId');
        }

        // Vérification que la review demandée appartient bien au produit
        $this->container->get('marketplace.review.product.service')->getReview($mvpId, $reviewId);

        // Approbation de la review
        $this->container->get('marketplace.review.product.service')->approveReview($reviewId);

        // Notification
        fn_set_notification('N', __('notice'), __('text_changes_saved'));

        return $this->redirectToRoute('admin_MultiVendorProduct_form', ['id' => $mvpId]);
    }

    public function declineReviewAction(Request $request)
    {
        $this->mustBeAdmin();

        $mvpId = $request->query->get('mvpId', null);
        $reviewId = $request->query->get('reviewId', null);

        if (\is_null($mvpId)) {
            throw IntegrityConstraintViolation::isMissing('mvpId');
        }

        if (\is_null($reviewId)) {
            throw IntegrityConstraintViolation::isMissing('reviewId');
        }

        // Vérification que la review demandée appartient bien au produit
        $this->container->get('marketplace.review.product.service')->getReview($mvpId, $reviewId);

        // Approbation de la review
        $this->container->get('marketplace.review.product.service')->declineReview($reviewId);

        // Notification
        fn_set_notification('N', __('notice'), __('text_changes_saved'));

        return $this->redirectToRoute('admin_MultiVendorProduct_form', ['id' => $mvpId]);
    }

    protected function getMultiVendorProductService(): MultiVendorProductService
    {
        return $this->container->get('marketplace.multi_vendor_product.service');
    }

    protected function getStatuses(): array
    {
        return [
            Status::ENABLED => __('enabled'),
            Status::DISABLED => __('disabled'),
            Status::HIDDEN => __('hidden'),
        ];
    }

    protected function save(Request $request): ?MultiVendorProduct
    {
        $mvpService = $this->getMultiVendorProductService();

        $id = $request->request->get('id', null);
        try {
            $mvp = $id ? $mvpService->get($id) : null;
        } catch (NotFound $e) {
            fn_set_notification('W', __('warning'), __('object_not_found', array('[object]' => __('multi_vendor_product'))));

            return null;
        }

        // Nettoyage des données
        $data = array_filter($request->request->all()); // We remove empty fields

        // Extract attributes
        $attributesValues = $data['product_data']['product_features'] ?? [];
        $newVariants = $data['product_data']['add_new_variant'] ?? [];
        $freeAttributesValues = $data['product_data']['free_features'] ?? [];
        unset($data['product_data']);

        // Gestion de la catégorie
        if (isset($data['category'])) {
            // Loading the Category entity from the given ID
            try {
                $data['category'] = $this->container->get('marketplace.pim.category_service')->get((int) $data['category']);
            } catch (NotFound $e) {
                fn_set_notification('W', __('warning'), __('object_not_found', array('[object]' => __('category'))));

                return $mvp;
            }
        }

        // Récupération des ids d'image du mvp courant
        $currentImageIds = !\is_null($mvp) ? $mvp->getImageIds() : [];

        // Récupération des ids des images à supprimer
        $toDeleteImageIds = array_keys($request->request->get('removeImage', []));

        // Suppression physique des images
        foreach ($toDeleteImageIds as $toDeleteImageId) {
            $mvpService->removeImage($id, $toDeleteImageId);
        }

        // Suppression video
        if ($mvp && $mvp->getVideo() && $data['video'] != $mvp->getVideo()) {
            try {
                $this->getMultiVendorProductService()->deleteVideo($mvp->getId());
            } catch (\Exception $exception) {
                $data['video'] = $mvp->getVideo();
                fn_set_notification('W', __('warning'), __('unable_delete_video'));
            }
        }

        // Mise à jour des ids des images à sauvegarder
        $data['imageIds'] = array_diff($currentImageIds, $toDeleteImageIds);

        // Sanitize mvp full description
        if (true === \array_key_exists('description', $data) && $data['description'] !== null) {
            $purifierService = $this->container->get('purifier.default');
            $data['description'] = \trim($purifierService->purify($data['description']));
        }
        $data['description'] ??= '';

        // Sanitize mvp short description
        if (true === \array_key_exists('shortDescription', $data) && $data['shortDescription'] !== null) {
            $purifierService = $this->container->get('purifier.default');
            $data['shortDescription'] = \trim($purifierService->purify($data['shortDescription']));
        }
        $data['shortDescription'] ??= '';

        // Sauvegarde du MVP
        try {
            $mvp = $mvpService->save($data, $mvp);
        } catch (IntegrityConstraintViolation $e) {
            fn_set_notification('W', __('warning'), __('missing_required_field'));

            return $mvp;
        }

        // Sauvegarde de la nouvelle image
        if ($file = $request->files->get('newImage')) {
            try {
                $mvp = $mvpService->addImage($mvp->getId(), $file, $request->request->get('newImageAlt'));
            } catch (BadImage $e) {
                fn_set_notification('W', __('warning'), __('invalid_image'));
            }
        }

        // Update alternative text for current images
        foreach ($data['imageIds'] as $imageId) {
            $hasAlt = $mvpService->getImageAltText(
                $imageId,
                (string) GlobalState::contentLocale()
            );
            $altText = $request->request->get('alt_' . $imageId) ?? '';

            if (\is_null($hasAlt)) {
                $mvpService->setImageAltText(
                    $imageId,
                    $altText,
                    (string) GlobalState::contentLocale()
                );
            } else {
                $mvpService->updateImageAltText(
                    $imageId,
                    $altText,
                    (string) GlobalState::contentLocale()
                );
            }
        }

        // Sauvegarde des valeurs d'attributs
        if (!empty($attributesValues)) {
            $this->getMultiVendorProductService()->setLegacyAttributesValues($mvp->getId(), $attributesValues, $newVariants);
        }

        // Sauvegarde des valeurs d'attributs libres
        if (!empty($freeAttributesValues)) {
            $freeAttributes = [];
            foreach ($freeAttributesValues as $value) {
                if (!empty($value['name']) && isset($value['value'])) {
                    if (!isset($freeAttributes[$value['name']])) {
                        $freeAttributes[$value['name']] = [];
                    }
                    $freeAttributes[$value['name']][] = $value['value'];
                }
            }

            $this->getMultiVendorProductService()->setFreeLegacyAttributes($mvp->getId(), $freeAttributes);
        }

        // Attach new products
        $productsToAttach = (array) $request->request->get('product_ids_to_attach', []);
        foreach ($productsToAttach as $productId) {
            $mvpService->attach($mvp->getId(), $productId);
        }

        // Remove selected products
        // We detach products only if the user clicked on the Remove button, and not the Save button
        if ($request->request->get('allow_detach') === 'Y') {
            $productsToDetach = (array) $request->request->get('product_ids_to_detach', []);
            foreach ($productsToDetach as $productId) {
                $mvpService->detach($mvp->getId(), $productId);
            }
        }

        fn_set_notification('N', __('notice'), __('text_changes_saved'));

        return $mvp;
    }

    protected function csrfTokenMustBeValid($id, $token)
    {
        if (!$this->isCsrfTokenValid($id, $token)) {
            fn_set_notification('E', __('error'), __('invalid_csrf_token'));
            throw $this->createAccessDeniedException('Invalid CSRF token');
        }
    }

    private function formatCategories(array $categories, array $selectedCategoryIds)
    {
        return array_values(array_map(function ($category) use ($selectedCategoryIds) {
            return $this->formatCategory($category, $selectedCategoryIds);
        }, $categories));
    }

    private function formatBrands($selectedBrandIds)
    {
        $attributeService = $this->get('marketplace.pim.attribute_service');

        return array_filter(array_map(function ($brandId) use ($attributeService) {
            $variant = $attributeService->getAttributeVariant($brandId);
            if (!$variant) {
                return null;
            }

            return [
                'id' => $variant->getId(),
                'name' => $variant->getName(),
            ];
        }, $selectedBrandIds));
    }

    private function formatMvps($selectedMvpIds)
    {
        return array_filter(array_map(function ($mvpId) {
            try {
                $mvp = $this->getMultiVendorProductService()->get($mvpId);
            } catch (NotFound $e) {
                return null;
            }

            return [
                'id' => $mvpId,
                'name' => $mvp->getName(),
            ];
        }, $selectedMvpIds));
    }

    /**
     * Transforme le retour de la fonction fn_get_categories() par le tableau requis par Vue.js
     */
    private function formatCategory(array $category, array $selectedCategoryIds)
    {
        $return = [
            'id' => (int) $category['category_id'],
            'name' => $category['category'],
            'isSelected' => \in_array($category['category_id'], $selectedCategoryIds),
        ];

        if (isset($category['subcategories'])) {
            $return['subcategories'] = array_map(function ($category) use ($selectedCategoryIds) {
                return $this->formatCategory($category, $selectedCategoryIds);
            }, $category['subcategories']);
        }

        return $return;
    }

    public function getFeatureFlag(): string
    {
        return 'feature.multi_vendor_product';
    }
}
