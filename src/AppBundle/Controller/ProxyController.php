<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller;

use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Wizacha\Component\Http\Authorization\UserApiKeyAuthorization;
use Wizacha\Component\Http\Client\GuzzleYavinClient;
use Wizacha\Component\Http\Factory\ProxyRequestFactory;
use Wizacha\Component\Http\Option\Common\HeaderOption;
use Wizacha\Component\Http\Request\YavinRequest;
use Wizacha\Marketplace\User\UserService;

class ProxyController
{
    public const SESSION_UNEXPECTED_MESSAGE = 'An error occurred when trying to send request. Error in the user session (user is not logged in or session is invalid). See log for more information.';

    private LoggerInterface $logger;

    private UserService $userService;

    private GuzzleYavinClient $yavinClient;

    private ProxyRequestFactory $proxyRequestFactory;

    public function __construct(
        LoggerInterface $logger,
        UserService $userService,
        GuzzleYavinClient $yavinClient,
        ProxyRequestFactory $proxyRequestFactory
    ) {
        $this->logger = $logger;
        $this->userService = $userService;
        $this->yavinClient = $yavinClient;
        $this->proxyRequestFactory = $proxyRequestFactory;
    }

    public function sendRequestAction(string $apiUri, Request $httpRequest): Response
    {
        $httpResponse = new Response();

        $session = $httpRequest->getSession();

        // Error in the user session (user is not logged in or session is invalid).
        if (null === $session->get('auth')['user_id'] || false === isset($session->get('auth')['user_id'])) {
            $httpResponse->setContent(self::SESSION_UNEXPECTED_MESSAGE);
            $httpResponse->setStatusCode(Response::HTTP_BAD_REQUEST);

            $this->logger->error(
                self::SESSION_UNEXPECTED_MESSAGE,
                [
                    'method' => $httpRequest->getMethod(),
                    'uri' => $httpRequest->getUri(),
                    'error' => "Error in the user session (user is not logged in or session is invalid).",
                    'file' => \get_class()
                ]
            );

            return $httpResponse;
        }

        try {
            $currentUser = $this->userService->get($session->get('auth')['user_id']);

            $yavinRequest = new YavinRequest(
                $this->proxyRequestFactory->create($apiUri, $httpRequest),
                $httpRequest->getSchemeAndHttpHost(),
                new HeaderOption(new UserApiKeyAuthorization($currentUser))
            );

            $yavinResponse = $this->yavinClient->send($yavinRequest);

            $httpResponse->setContent($yavinResponse->getBody()->getContents());
            $httpResponse->setStatusCode($yavinResponse->getStatusCode());
            $httpResponse->headers->add($yavinResponse->getHeaders());
        } catch (\Throwable $e) {
            $this->logger->error(
                $e->getMessage(),
                [
                    'method' => $httpRequest->getMethod(),
                    'uri' => $httpRequest->getUri(),
                    'trace' => $e->getTraceAsString(),
                    'error' => $e->getPrevious()
                ]
            );

            $httpResponse = $this->buildHttpResponseOnError($e, $httpResponse);
        }

        return $httpResponse;
    }

    private function buildHttpResponseOnError(\Exception $exception, Response $response): Response
    {
        $message = $exception->getMessage();

        if (null !== $exception->getPrevious()) {
            $previousMessage = $exception->getPrevious()->getMessage();
            $message = $message . ": " . $previousMessage;
        }

        $response->setContent($message);
        $response->setStatusCode(Response::HTTP_BAD_REQUEST);

        return $response;
    }
}
