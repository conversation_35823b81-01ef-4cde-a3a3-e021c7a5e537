<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;

trait StreamedResponseTrait
{
    /**
     * Request an url with a set of headers, return the response wrapped in a StreamedResponse
     *
     * @param string $url
     * @param array $headers
     * @return Response
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function createStreamedResponseFromUrl(string $url, array $headers): Response
    {
        // get Response
        $response = (new Client(
            [
                'decode_content' => false,
                'stream' => true,
            ]
        ))
            ->request(
                'GET',
                $url,
                [ 'headers' => $headers ]
            )
        ;

        // create StreamedResponse from Response
        $stream = $response->getBody();
        $streamedResponse = new StreamedResponse(
            function () use ($stream) {
                while (false === $stream->eof()) {
                    echo $stream->read(8192);
                    flush();
                }
            }
        );

        // copy cache- and content-related headers from Response to StreamedResponse
        $copiedHeaders = [
            'Last-Modified',
            'ETag',
            'Cache-Control',
            'Content-Encoding',
            'Content-Type',
            'Content-Length'
        ];
        foreach ($copiedHeaders as $header) {
            $streamedResponse->headers->set($header, $response->getHeader($header));
        }
        $streamedResponse->headers->set('Content-Location', $url);

        return $streamedResponse;
    }
}
