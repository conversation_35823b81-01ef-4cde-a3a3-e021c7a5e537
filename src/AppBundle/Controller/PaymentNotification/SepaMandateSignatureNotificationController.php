<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\PaymentNotification;

use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\OptionsResolver\Exception\UndefinedOptionsException;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Wizacha\Marketplace\Payment\MandateService;
use Wizacha\Marketplace\Payment\MandateStatus;
use Wizacha\Marketplace\Payment\Processor\LemonWay;
use Wizacha\Marketplace\Payment\UserMandate;
use Wizacha\Marketplace\User\UserRepository;

class SepaMandateSignatureNotificationController extends Controller
{
    protected LoggerInterface $logger;

    protected LemonWay $lemonWay;

    protected MandateService $mandateService;

    protected UserRepository $userRepository;

    public function __construct(
        LemonWay $lemonWay,
        MandateService $mandateService,
        LoggerInterface $logger,
        UserRepository $userRepository
    ) {
        $this->lemonWay = $lemonWay;
        $this->logger = $logger;
        $this->mandateService = $mandateService;
        $this->userRepository = $userRepository;
    }

    public function lemonwaySignDocumentAction(Request $request): Response
    {
        \define('PAYMENT_NOTIFICATION', true);

        if ($this->isConfigured($request) === false) {
            return new Response(null, Response::HTTP_NO_CONTENT);
        }

        // Resolves Request parameters
        $notification = $this->optionsResolver($request);

        if (\count($notification) === 0 || \array_key_exists('signingtoken', $notification) === false) {
            return new Response(null, Response::HTTP_NO_CONTENT);
        }

        $signingToken = $notification['signingtoken'];

        $userInfo = $this->mandateService->findOneUserInfoBy(['lemonwayElectronicSignature' => $signingToken]);

        if ($userInfo === null) {
            $this->logger->error(
                'None user informations retrived for the signingtoken :' . $signingToken,
                [
                    'request' => $request->request->all(),
                ]
            );

            return new Response('', Response::HTTP_BAD_REQUEST);
        }

        $wallet = $this->lemonWay->getTechnicalUserWallet($this->userRepository->get($userInfo->getUserId()), true);

        $userMandate = $this->mandateService->findOneMandateBy(
            [
                'agreementId' => $userInfo->getLemonwaySepaAgreement()
            ]
        );

        if ($userMandate instanceof UserMandate === false) {
            $this->logger->error(
                'User mandate not found: ' . $userMandate->getId(),
                [
                    'request' => $request->request->all(),
                ]
            );

            return new Response('', Response::HTTP_BAD_REQUEST);
        }

        if ($this->lemonWay->sepaMandateIsEnabled($wallet, $userMandate) === true) {
            $userMandate->setStatus(MandateStatus::ENABLED());
        } else {
            $userMandate->setStatus(MandateStatus::DISABLED());
        }

        $this->mandateService->saveMandate($userMandate);

        return new Response('', Response::HTTP_OK);
    }

    protected function isConfigured(Request $request): bool
    {
        if ($this->lemonWay->isConfigured() === false) {
            $this->logger->error('We received a hook from Lemonway, but it\'s not configured on this platform', [
                'request' => $request->request->all(),
            ]);

            return false;
        }

        return true;
    }

    protected function optionsResolver(Request $request): array
    {
        // Resolves Request parameters
        $resolver = (new OptionsResolver())
            ->setDefined([
                'signingtoken',
            ])
            ->setRequired([
                'signingtoken',
            ]);

        try {
            return $resolver->resolve($request->request->all());
        } catch (UndefinedOptionsException $e) {
            $this->logger->error('[Lemonway] Signing sepa mandate hook received bad request', [
                'request' => $request->request->all(),
                'exception' => $e,
            ]);

            return [];
        }
    }
}
