<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\PaymentNotification;

use HiPay\Fullservice\Enum\Transaction\TransactionStatus as HiPayTransactionStatus;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Response;
use Psr\Log\LoggerInterface;
use Wizacha\Marketplace\Order\Action\MarkAsPaid;
use Wizacha\Marketplace\Order\Action\MarkPaymentAsRefused;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\Order\Workflow\WorkflowName;
use Wizacha\Marketplace\Payment\Event\HipayPaymentCallbackEvent;
use Wizacha\Order;
use Wizacha\Marketplace\Order\Exception\OrderNotFound;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Payment\Processor\HiPay;
use Wizacha\Marketplace\Transaction\Exception\TransactionNotFound;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionType;

class HipaySepaNotificationController extends Controller
{
    /** @var LoggerInterface */
    protected $logger;

    /** @var Hipay */
    protected $processor;

    /** @var TransactionService */
    protected $transactionService;

    /** @var OrderService */
    protected $orderService;

    /** @var EventDispatcherInterface */
    protected $eventDispatcher;

    /** @var MarkPaymentAsRefused */
    protected $markAsPaidAction;

    /** @var MarkAsPaid */
    protected $markPaymentAsRefusedAction;

    public function __construct(
        LoggerInterface $logger,
        HiPay $processor,
        TransactionService $transactionService,
        OrderService $orderService,
        EventDispatcherInterface $eventDispatcher,
        MarkPaymentAsRefused $markPaymentAsRefused,
        MarkAsPaid $markAsPaid
    ) {
        $this->logger = $logger;
        $this->processor = $processor;
        $this->transactionService = $transactionService;
        $this->orderService = $orderService;
        $this->eventDispatcher = $eventDispatcher;
        $this->markPaymentAsRefusedAction = $markPaymentAsRefused;
        $this->markAsPaidAction = $markAsPaid;
    }

    /**
     * Callback controller called by Hipay used to receive response from Hipay during sepa transaction process.
     * Sepa transaction initialized in createSepaTransaction method in Hipay class.
     * Security note : we not have access to BO of client to get passphrase for check of integrity
     * http header named x-allopass-signature.
     * Return note : We return always HTTP OK response to Hipay to tell him ok we received your message/request/hook...
     */
    public function transactionAction(Request $request): Response
    {
        $referenceId = $request->request->get('transaction_reference');

        if (null === $referenceId) {
            $this->logError('Missing or wrong request');

            return new Response(null, Response::HTTP_NO_CONTENT);
        }

        // Recall Hipay to get transaction info for $referenceId
        try {
            $transactionDetail = $this->processor->getTransactionDetails($referenceId);
            $transaction = $this->transactionService->getOneBy(
                [
                    'processorName' => $this->processor->getName(),
                    'transactionReference' => $referenceId,
                ]
            );
        } catch (TransactionNotFound $exception) {
            $this->logError('Transaction not found : ' . $referenceId, $exception);

            return new Response(null, Response::HTTP_NO_CONTENT);
        }

        // Ignore non SEPA orders ( CB, ... )
        if (false === \array_key_exists('hipay_sepa', $transaction->getProcessorInformations())) {
            $this->logError('Transaction not found : ' . $referenceId);

            return new Response(null, Response::HTTP_NO_CONTENT);
        }

        $orderId = $transactionDetail->getOrderId();

        try {
            \define('PAYMENT_NOTIFICATION', true);
            $orders = $this->orderService->getChildOrders($orderId);
        } catch (OrderNotFound $exception) {
            $this->logError('Order not found : ' . $referenceId, $exception);

            return new Response(null, Response::HTTP_NO_CONTENT);
        }

        $this->eventDispatcher->dispatch(
            new HiPayPaymentCallbackEvent(
                new Order($orderId),
                $referenceId,
                TransactionType::DIRECT_DEBIT()
            )
        );

        // Avancement du workflow
        if ($transactionDetail->getStatus() === HiPayTransactionStatus::CAPTURED) {
            $this->markOrderAsPaid($orderId, $orders);
        } elseif ($transactionDetail->getStatus() === HiPayTransactionStatus::REFUSED) {
            $this->markOrderAsRefused($orderId, $orders);
        } else {
            $this->logError('Not managed status : ' . $transactionDetail->getStatus());
        }

        return new Response(null, Response::HTTP_NO_CONTENT);
    }

    /** @param Order[] $orders */
    protected function markOrderAsPaid(int $orderId, array $orders): void
    {
        $allowed = false;
        foreach ($orders as $order) {
            if (true === $this->markAsPaidAction->isAllowed($order)) {
                $this->markAsPaidAction->execute($order);
                $allowed = true;
            }
        }

        $allowedWorkflows = [
            WorkflowName::PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WAIT_PAYMENT_DEFERMENT_WITHDRAWAL_PERIOD_FUNDS_DISPATCH()->getValue(),
            WorkflowName::PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_WAIT_PAYMENT_DEFERMENT_VENDOR_VALIDATION_ORDER_PREPARATION_DELIVERY_WITHDRAWAL_PERIOD_FUNDS_DISPATCH()->getValue(),
            WorkflowName::PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_WAIT_PAYMENT_DEFERMENT_VENDOR_VALIDATION_ORDER_PREPARATION_WITHDRAWAL_PERIOD_FUNDS_DISPATCH()->getValue(),
            WorkflowName::PAYMENT_DEFERMENT_AUTHORIZATION_ORDER_CONFIRMATION_ORDER_COMMITMENT_VENDOR_VALIDATION_ORDER_PREPARATION_WAIT_PAYMENT_DEFERMENT_WITHDRAWAL_PERIOD_FUNDS_DISPATCH()->getValue(),
        ];
        $isDefermentPayment = \in_array($order->getWorkflowName()->getValue(), $allowedWorkflows, true);

        // changement du statut legacy si le workflow est ok afin de lancer le dispatch des fonds
        if ($isDefermentPayment === true && $allowed === true) {
            fn_change_order_status($orderId, OrderStatus::COMPLETED);
        }
    }

    /** @param Order[] $orders */
    protected function markOrderAsRefused(int $orderId, array $orders): void
    {
        foreach ($orders as $order) {
            if (true === $this->markPaymentAsRefusedAction->isAllowed($order)) {
                $this->markPaymentAsRefusedAction->execute($order);
            }
        }

        fn_change_order_status($orderId, OrderStatus::BILLING_FAILED);
    }

    private function logError(string $message, \Exception $exception = null): void
    {
        $this->logger->error(
            'Hipay Sepa Notification Controller ',
            [
                'message' =>  $message,
                'exception' => $exception
            ]
        );
    }
}
