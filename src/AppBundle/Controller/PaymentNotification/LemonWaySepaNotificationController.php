<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\PaymentNotification;

use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Wizacha\Marketplace\Order\Action\MarkAsPaid;
use Wizacha\Marketplace\Order\Action\MarkPaymentAsRefused;
use Wizacha\Marketplace\Order\Exception\OrderNotFound;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\Payment\Event\LemonwayPaymentCallbackEvent;
use Wizacha\Marketplace\Order\Tracer\Tracer;
use Wizacha\Marketplace\Payment\LemonWay\LemonWayApi;
use Wizacha\Marketplace\Payment\LemonWay\LemonWayTransactionDetails;
use Wizacha\Marketplace\Payment\PaymentType;
use Wizacha\Marketplace\Payment\Processor\LemonWay;
use Wizacha\Marketplace\Payment\TransactionDetails;
use Wizacha\Marketplace\Transaction\Exception\TransactionNotFound;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionType;
use Wizacha\Money\Money;
use Wizacha\Order;

class LemonWaySepaNotificationController extends AbstractController
{
    protected LoggerInterface $logger;
    protected LemonWay $lemonWay;
    protected OrderService $orderService;
    protected TransactionService $transactionService;
    protected EventDispatcherInterface $eventDispatcher;
    protected MarkAsPaid $markAsPaidAction;
    protected MarkPaymentAsRefused $markPaymentAsRefusedAction;

    private Tracer $orderDebugTracer;

    public function __construct(
        LemonWay $lemonWay,
        OrderService $orderService,
        TransactionService $transactionService,
        EventDispatcherInterface $eventDispatcher,
        MarkAsPaid $markAsPaid,
        MarkPaymentAsRefused $markPaymentAsRefused,
        LoggerInterface $logger,
        Tracer $orderDebugTracer
    ) {
        $this->lemonWay = $lemonWay;
        $this->orderService = $orderService;
        $this->transactionService = $transactionService;
        $this->logger = $logger;
        $this->eventDispatcher = $eventDispatcher;
        $this->markAsPaidAction = $markAsPaid;
        $this->markPaymentAsRefusedAction = $markPaymentAsRefused;
        $this->orderDebugTracer = $orderDebugTracer;
    }

    public function lemonwaySepaTransactionAction(Request $request): Response
    {
        \define('PAYMENT_NOTIFICATION', true);

        if ($this->lemonWay->isConfigured() === false) {
            return $this->generateBadResponse("We received a hook from Lemonway, but it's not configured on this platform");
        }

        $referenceId = $request->request->get('IdTransaction');

        if ($referenceId === null) {
            return $this->generateBadResponse('Received bad request');
        }

        $transactionDetails = $this->lemonWay->getSepaTransactionDetails($referenceId);

        if ($transactionDetails === null) {
            return $this->generateBadResponse('Transaction not found : ' . $referenceId);
        }

        try {
            $this->transactionService->retrieveTransaction(
                $this->lemonWay->getName(),
                TransactionType::DIRECT_DEBIT(),
                $transactionDetails->getOrderId()
            );
        } catch (TransactionNotFound $e) {
            $this->logger->error('[Lemonway] SEPA Transaction hook : Is not SEPA transaction : ' . $referenceId);

            return new Response(null, Response::HTTP_NO_CONTENT);
        }

        if ($this->isValidTotalAmount($transactionDetails) === false) {
            return $this->generateBadResponse('Cannot validate total amount');
        }

        $orderId = $transactionDetails->getOrderId();
        $order = new Order($orderId);
        $this->eventDispatcher->dispatch(
            new LemonwayPaymentCallbackEvent(
                $order,
                $referenceId,
                TransactionType::DIRECT_DEBIT()
            )
        );

        // Avancement du workflow
        if ($transactionDetails->getStatus() === LemonWayApi::STATUS_SUCCESS) {
            $forceNotification = ['C' => false, 'A' => false, 'V' => false];
            $this->orderService->markOrdersWithAction($orderId, $this->markAsPaidAction, OrderStatus::STANDBY_VENDOR(), null, $forceNotification);
        } elseif ($transactionDetails->getStatus() === LemonWayApi::STATUS_ERROR) {
            // Do not refuse order if deferment
            if (true === $order->getPaymentType()->equals(PaymentType::PAYMENT_DEFERMENT())) {
                $this->orderDebugTracer->trace($orderId, 'MarkPaymentAsRefused');
            } else {
                $this->orderService
                    ->markOrdersWithAction($orderId, $this->markPaymentAsRefusedAction, OrderStatus::BILLING_FAILED());
            }
        } else {
            return $this->generateBadResponse('Not managed status : ' . $transactionDetails->getStatus());
        }

        return new Response('', Response::HTTP_OK);
    }

    protected function isValidTotalAmount(LemonWayTransactionDetails $transactionDetails): bool
    {
        $orderId = $transactionDetails->getOrderId();
        try {
            $totalAmount = Money::fromVariable(0)
                ->add($this->orderService->getAnyOrder($orderId)->getCustomerTotal())
                ->reducePrecisionToCents()
            ;
        } catch (OrderNotFound $exception) {
            $this->logger->error('[Lemonway] SEPA Transaction hook : Order not found.', [
                'exception' => $exception,
                'transactionDetails' => $transactionDetails
            ]);

            return false;
        }
        if ($totalAmount->equals(Money::fromVariable($transactionDetails->getAmount())) === false) {
            $this->logger->error("[Lemonway] SEPA Transaction hook : Wrong transaction amount, id: {$transactionDetails->getId()}, comment: {$transactionDetails->getComment()}.");

            return false;
        }
        return true;
    }

    protected function generateBadResponse(string $message): JsonResponse
    {
        return new JsonResponse(
            [
                'message' => '[Lemonway] SEPA Transaction hook : ' . $message,
            ],
            Response::HTTP_BAD_REQUEST
        );
    }
}
