<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Wizacha\User;
use Wizacha\Marketplace\User\User as DomainUser;

/**
 * Base controller class for using legacy services.
 */
abstract class BaseController extends Controller
{
    /**
     * @var \Smarty
     */
    protected $smarty;

    public function smarty(): \Smarty
    {
        if (!$this->smarty) {
            $this->smarty = $this->get('templating.smarty');
        }

        return $this->smarty;
    }

    /**
     * Returns the CS Cart user.
     */
    public function getUser(): ?User
    {
        $session = $this->container->get('session');
        $userId = $session->get('auth')['user_id'];
        return $userId ? new User($userId) : null;
    }

    /**
     * Retourne l'objet User de la marketplace et non celui de la couche
     * sécurité (qui contient celui de CSCart).
     */
    protected function getDomainUser(): ?DomainUser
    {
        $user = $this->getUser();

        if (\is_null($user)) {
            return null;
        }

        return $this
            ->get('marketplace.user.user_service')
            ->get($user->getId());
    }
}
