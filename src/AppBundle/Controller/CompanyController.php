<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Tygh\Registry;
use Wizacha\Company;
use Wizacha\Marketplace\Company\Event\CompanyApplied;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Company\CompanyType;

class CompanyController extends BaseController
{
    public function termsAction($company)
    {
        $company = $this->get('marketplace.catalog.company_service')->getCompanyBySlug((string) $company);

        return $this->render('@App/frontend/cms/terms.html.twig', [
            'company' => $company,
        ]);
    }

    /**
     * Création d'une nouvelle boutique pro (et de son compte "vendor pro" associé
     */
    public function createAction(Request $request): Response
    {
        //-----------------------------------
        // Vérifications globales de sécurité
        //-----------------------------------

        // Les admin logués ne peuvent pas créer de boutique dans le front
        if ($this->getUser() && $this->getUser()->isAdmin()) {
            fn_set_notification('E', __('error'), __('error_admin_cannot_create_b2c_vendor'));

            return $this->redirectToRoute('home');
        }

        // Les vendeurs pro logués ne peuvent pas créer de nouvelle boutique dans le front
        if ($this->getUser() && $this->getUser()->isProfessionalVendor()) {
            fn_set_notification('E', __('error'), __('error_vendor_cannot_create_b2c_vendor'));

            return $this->redirectToRoute('home');
        }

        //---------------------
        // Paramétrage statique
        //---------------------

        // Les conditions doivent-elles être validées ?
        $mustHaveValidatedTerms = $this->getParameter('feature.create_account.must_accept_terms');

        //------------------------------------------
        // Gestion de l'enregistrement du formulaire
        //------------------------------------------
        if ($request->getMethod() === Request::METHOD_POST) {
            do {
                // Récupération des paramètres de POST (à faire impérativement avant le premier break)
                $company = $request->request->all()['company_data'];
                $user    = $request->request->all()['user_data'];

                // Validation des terms
                if ($mustHaveValidatedTerms && \is_null($request->request->get('terms_approved'))) {
                    fn_set_notification('E', __('error'), __('register_must_accept_terms'));

                    break;
                }

                // Validation des champs obligatoires
                if (!Company::checkApplyForVendorAdditionalFields($company, $_FILES)) {
                    fn_set_notification('E', __('error'), __('text_fill_the_mandatory_fields'));

                    break;
                }

                // Validation du type et de l'extension des fichiers à uploader
                foreach ($_FILES as $key => $file) {
                    if (!fn_w_check_apply_for_vendor_files($file)) {
                        fn_set_notification('E', __('error'), __('text_forbidden_uploaded_file_extension', [
                            '[ext]' => strtolower(fn_get_file_ext($file['name'])),
                            '[exts]' => implode(',', fn_w_get_vendor_files_allowed_extensions())
                        ]));

                        break 2;
                    }
                }

                // Initialisation des données à sauvegarder
                $infoToSave = [
                    'timestamp'                      => TIME,
                    'w_company_type'                 => (string) CompanyType::PROFESSIONAL(),
                    'status'                         => 'N',
                    'request_account_data'           => serialize([
                        'company'         => $user['company'],
                        'admin_firstname' => $company['admin_firstname'],
                        'admin_lastname'  => $company['admin_lastname'],
                    ]),
                    'legal_representative_firstname' => $company['admin_firstname'],
                    'legal_representative_lastname'  => $company['admin_lastname'],
                ] + $company;

                // Sauvegarde en base
                $savedCompanyId = fn_update_company($infoToSave, 0, $company['country']);
                if (!$savedCompanyId) {
                    // S'il y a une notification : c'est une erreur 'normale', une popup va apparaitre et ce n'est pas nécessaire de logguer
                    // Si il n'y a pas de notification, c'est une erreur système (BDD, etc.) : il faut donc logguer et avertir l'utilisateur
                    if (empty($_SESSION['notifications'])) {
                        $this->get('logger')->error('Error saving company registration form : ', $infoToSave);
                        fn_set_notification('E', __('error'), __('text_error_adding_request'));
                    }

                    break;
                }

                // Sauvegarde des fichiers uploadés
                fn_w_put_files_for_vendor_post($savedCompanyId);

                // Notifications mail
                $companyData = fn_get_company_data($savedCompanyId);
                $adminFirstName = $company['admin_firstname'] ?? '';
                $adminLastName = $company['admin_lastname'] ?? '';
                $messageToAdmin = $company['message'] ?? '';
                $event = new CompanyApplied($companyData, $adminFirstName, $adminLastName, $messageToAdmin);
                $this->get('event_dispatcher')->dispatch($event, CompanyApplied::class);

                // Notification de succès
                fn_set_notification('N', __('information'), __('text_successful_request'));

                // Redirection sur la home
                return $this->redirectToRoute('home');
            } while (false);
            // Tricky trick pour simplifier la sortie du if,
            // car segmenter le code en fonctions pour des raison d'architecture
            // et non de logique métier me parait encore moins propre :p
        } else {
            //----------------------------------
            // Gestion de l'affichage de la page
            //----------------------------------

            // Initialisation de la compagnie
            $company = [];

            // Initialisation du user
            $user = ['b_company' => ''];
        }

        //-------------------------------------------
        // Affichage général GET / POST (avec erreur)
        //-------------------------------------------
        return $this->render('@App/frontend/views/company/registration_form.twig', [
            'company'                => $company,
            'user'                   => $user,
            'countries'              => fn_get_simple_countries(true, (string) GlobalState::interfaceLocale()),
            'default_country'        => Registry::get('settings.General.default_country'),
            'mandatory_terms'        => $mustHaveValidatedTerms,
            'terms_label'            => $this->get('translator')->trans('register_company_accept_terms', [
                '[usageUrl]' => fn_url('pages.view?page_id=12'),
                '[saleUrl]'  => fn_url('pages.view?page_id=8'),
            ]),
        ]);
    }
}
