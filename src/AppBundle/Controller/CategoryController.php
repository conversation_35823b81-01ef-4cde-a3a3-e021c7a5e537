<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Tygh\Database;
use Wizacha\Cache\Handler;
use Wizacha\Category;
use Wizacha\Marketplace\Smarty\Config;
use Wizacha\Registry;

class CategoryController extends BaseController
{
    /**
     * @return Response
     */
    public function sitemapAction()
    {
        $cache_id = date('Ymd');

        if (!$this->smarty()->isCached('views/categories/sitemap.tpl', $cache_id)) {
            $leaf_iterator = new \Wizacha\Core\Iterator\PdoColumnIterator(
                Database::query(
                    "SELECT category_id FROM ?:categories as c WHERE (SELECT COUNT(*) FROM ?:categories as c2 WHERE parent_id=c.category_id)=0 AND c.status = 'A';"
                )
            );
            $urls = [];
            foreach ($leaf_iterator as $category) {
                $urls[] = Category::frontUrl($category);
            }
            $this->smarty()->assign('urls', $urls);
            $this->smarty()->assign('lastmod', date('Y-m-d'));
        }

        $response = new Response(
            $this->smarty()->fetch('views/categories/sitemap.tpl', $cache_id)
        );
        $response->headers->set('Content-Type', 'xml');

        return $response;
    }

    /**
     * @return Response
     */
    public function dropdownAction()
    {
        $registry = Registry::defaultInstance();
        $cache_keys = [$registry->cache()->getHandlerId(Handler::CATEGORIES_MENU)];
        $cache_id   = Config::cacheId($registry->cache(), $cache_keys);

        if (!$this->smarty()->isCached('views/categories/dropdown.tpl', $cache_id)) {
            $this->smarty()->assign('categories', Category::getTree());
        }

        return new Response(
            $this->smarty()->fetch('views/categories/dropdown.tpl', $cache_id)
        );
    }

    /**
     * Return subcategories informations
     * @param Request $request
     * @return JsonResponse
     */
    public function subCategoriesPickerAction(Request $request)
    {
        $category_id = (int) $request->get('category_id', 0);
        $categories  = (new Category($category_id))->getSubCategoriesVendor();
        $data        = [];
        foreach ($categories as $category) {
            $data[] = $this->categoryData($category);
        }

        return new JsonResponse(['data' => $data]);
    }

    /**
     * Return informations of a specific category
     * @param Request $request
     * @return JsonResponse
     */
    public function categoryInfoAction(Request $request)
    {
        $category_id = (int) $request->get('category_id', 0);
        $category = new Category($category_id);
        if ($category->exists()) {
            return new JsonResponse($this->categoryData($category));
        } else {
            return new JsonResponse([], 404);
        }
    }

    /**
     * Format category info for Json response
     * @param Category $category
     * @return array
     */
    public function categoryData(Category $category)
    {
        return [
            'category_id'      => $category->getId(),
            'name'             => $category->getName(),
            'is_leaf'          => $category->isLeaf(),
            'is_transactional' => $category->isTransactional(),
            'status'           => (string) $category->getStatus(),
            'is_garbage'       => Category::isMessCategory($category->getId())
        ];
    }
}
