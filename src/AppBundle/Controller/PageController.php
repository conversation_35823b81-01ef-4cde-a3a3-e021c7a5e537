<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Wizacha\Marketplace\Cms\PageType;
use Wizacha\Marketplace\Entities\Page;
use Wizacha\Status;

class PageController extends BaseController
{
    public function viewAction($pageId)
    {
        $page = new Page($pageId);

        $status = $page->getStatus();
        if (!$status || $status == Status::DISABLED) {
            throw $this->createNotFoundException();
        }


        // Special case, for BC ( /vendeur-particulier.html )
        // Before refactoring, this page rendered a login form which redirected to product creation
        // Now, we directly redirect to product creation
        if ($page->getWType() == PageType::CUSTOMER_APPLY) {
            return $this->redirectToRoute('product_new');
        }

        $breadcrumbs = [
            [
                'name' => __('homepage'),
                'link' => $this->generateUrl('home'),
            ],
        ];

        $parentIds = explode('/', $page->getIdPath());
        foreach ($parentIds as $id) {
            $_page = new Page($id);
            $breadcrumbs[] = [
                'name' => $_page->getTitle(),
                'link' => $_page->getUrl(),
            ];
        }

        return $this->render('@App/frontend/cms/page.html.twig', [
            'breadcrumbs' => $breadcrumbs,
            'page' => $page,
        ]);
    }

    public function handleFormAction(Request $request)
    {
        $pageId = $request->request->getInt('page_id');

        if (!$pageId) {
            return $this->redirectToRoute('home');
        }

        if (!$this->getUser()) {
            $recaptcha = $this->get('app.captcha');
            if (!$recaptcha->isHuman($request->request->all())) {
                fn_set_notification('W', __('warning'), __('error_captcha_required'));

                return $this->redirect(fn_url('pages.view&page_id=' . $pageId));
            }
        }

        if (fn_send_form($pageId, (array) $request->request->get('form_values', []))) {
            return $this->redirect(fn_url('pages.view&page_id=' . $pageId . '&sent=Y'));
        }

        return $this->redirect(fn_url('pages.view&page_id=' . $pageId));
    }
}
