<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Wizacha\Category;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Search\Record\Product;

class IndexController extends Controller
{
    public function indexAction(Request $request)
    {
        $filters  = [];
        $tagId  = '';
        $breadcrumbs = [];
        $vendorDescription = '';
        $category = null;

        if (!empty($companyId = $request->attributes->get('companyId'))) {
            if (!\Wizacha\Company::frontIds([$companyId])->valid()) {
                throw new NotFoundHttpException();
            }
            $tagId = Product::NAMESPACE_VENDOR . $companyId;
            $company = new Company($companyId);
            $breadcrumbs = [
                [
                    'name' => __('homepage'),
                    'link' => fn_url('')
                ],
                [
                    'name' => __('vendor') . " " . $company->getName(),
                    'link' => $company->getUrl()
                ]
            ];
            $vendorDescription = $company->getDescription();
        }

        if (!empty($request->attributes->get('categoryId'))) {
            $category = new Category($request->attributes->get('categoryId'));
            if (!$category->isVisibleForPublic()) {
                throw new NotFoundHttpException();
            }
            $productIndex = $this->container->get('marketplace.search.product_index');
            $filters = $productIndex->getFacetsFromCategory($category);
            if (!( isset($filters['main_category'], $filters['category_3'])
                && $filters['main_category'] == $filters['category_3']                )
            ) {
                unset($filters['main_category']);
            }
            unset($filters['category_3']);
        }

        if (!empty($request->attributes->get('variantId'))) {
            if (!(($variant_data = fn_get_product_feature_variant($request->attributes->get('variantId')))
                && $variant_data['feature_id'] == fn_w_get_brand_id()                )
            ) {
                throw new NotFoundHttpException();
            }
            $filters = [ Product::getBrandFacetName() => $variant_data['variant'] ];
        }

        return $this->render('@App/frontend/views/index/index.html.twig', [
            'search' => [
                'q' => $request->attributes->get('q'),
            ],
            'filters' => $filters,
            'tag_id' => $tagId,
            'breadcrumbs' => $breadcrumbs,
            'category' => $category,
            'vendor_description' => $vendorDescription,
            'seo_data' => isset($category) ? $category->getSeoData() : null,
        ]);
    }
}
