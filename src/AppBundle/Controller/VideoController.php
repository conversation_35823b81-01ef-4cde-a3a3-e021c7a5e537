<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\ServiceUnavailableHttpException;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;

class VideoController extends Controller
{
    public function videoUploadFormAction()
    {
        $form = $this->get('marketplace.pim.video_storage')->generateUploadFormData();
        return $this->render('@App/backend/product/video_upload_form.html.twig', [
            'form' => $form,
        ]);
    }

    public function startTranscodeJobsAction(Request $request): JsonResponse
    {
        $s3Key = $request->get('key');
        if (!$s3Key) {
            throw new BadRequestHttpException('\'key\' is undefined');
        }

        $videoService = $this->get('marketplace.pim.video_service');
        $videoStorage = $this->get('marketplace.pim.video_storage');

        try {
            $video = $videoService->importFromS3($s3Key);

            return new JsonResponse([
                'id'        => $video->getId(),
                'thumb'     => $videoStorage->generatePublicLinks($video)['thumb']
            ]);
        } catch (\Exception $e) {
            container()->get('logger')->error('S3 import error', ['exception' => $e]);
            throw new ServiceUnavailableHttpException('The video processing failed');
        }
    }

    public function startCopyUrlAction(Request $request): JsonResponse
    {
        $url = $request->get('file');
        $videoService = $this->get('marketplace.pim.video_service');

        if (\is_null($url)) {
            return BadRequestJsonResponse::missingFields('file');
        }

        try {
            $videoId = $videoService->startUploadFromUrl(0, $url);
            $video = $videoService->getFromStorage($videoId);

            return new JsonResponse([
                'id'        => $video->getId(),
                'thumb'     => $video->getThumbLink()
            ]);
        } catch (\Exception $e) {
            throw new ServiceUnavailableHttpException('The video processing failed');
        }
    }
}
