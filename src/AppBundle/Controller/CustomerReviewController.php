<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class CustomerReviewController extends BaseController
{
    /**
     * @var Request $request
     * @return Response
     */
    public function retrieveProductPostsAction(Request $request)
    {
        $productId = $request->attributes->get('product_id');

        $thread = $this->get('marketplace.review.product.service')->getProductThread($productId);
        $posts = $this->get('marketplace.review.product.service')->getReviews($productId);

        $this->smarty()->assign('thread', $thread, true);
        $this->smarty()->assign('posts', $posts, true);

        return new Response(
            $this->smarty()->fetch('views/customerreview/view.tpl')
        );
    }

    /**
     * @var Request $request
     * @return Response
     */
    public function retrieveNumberRatingsAction(Request $request)
    {
        $productId = $request->attributes->get('product_id');
        $nbPosts = $this->get('marketplace.review.product.service')->getRatingCount($productId);

        if (true == $request->attributes->get('return_empty_for_zero') && $nbPosts == 0) {
            return new Response("");
        }

        return new Response($nbPosts);
    }
}
