<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Request;
use Wizacha\Marketplace\GlobalState\GlobalState;

class SupportController extends Controller
{
    public function loginAction(Request $request)
    {
        $userId = $request->query->has('userId') ? $request->query->getInt('userId') : null;

        return $this->redirect(fn_url('auth.support?userId=' . $userId, 'A', 'current', (string) GlobalState::interfaceLocale(), true));
    }

    public function loginVendorAction(Request $request)
    {
        $userId = $request->query->has('userId') ? $request->query->getInt('userId') : null;

        return $this->redirect(fn_url('auth.support?userId=' . $userId, 'V', 'current', (string) GlobalState::interfaceLocale(), true));
    }

    public function loginFrontAction(Request $request)
    {
        $userId = $request->query->has('userId') ? $request->query->getInt('userId') : null;

        return $this->redirect(fn_url('auth.support?userId=' . $userId, 'C', 'current', (string) GlobalState::interfaceLocale(), true));
    }
}
