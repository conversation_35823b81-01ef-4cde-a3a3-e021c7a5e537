<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller;

use Symfony\Component\HttpFoundation\Response;
use Wizacha\Marketplace\Smarty\Config;
use Wizacha\Registry;

class FooterController extends BaseController
{
    public function mainAction()
    {
        return $this->renderFooter('views/footer/main.tpl');
    }

    public function renderFooter($template)
    {
        $tables = [
            'static_data_descriptions',
            'static_data',
            'menus_descriptions',
        ];
        $cache_keys = array_map([Registry::defaultInstance()->cache(), 'getHandlerId'], $tables);
        $cache_id   = Config::cacheId(Registry::defaultInstance()->cache(), $cache_keys);

        //Retrieve menus only if not yet cached
        if (!$this->smarty()->isCached($template, $cache_id)) {
            $menus = ['infos', 'customers', 'vendors'];
            $this->smarty()->assign('menus', \Wizacha\Misc::getMenus($menus));
        }

        return new Response(
            $this->smarty()->fetch($template, $cache_id)
        );
    }
}
