<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

/**
 * Documentation de l'API.
 */
class DocumentationController extends Controller
{
    /**
     * @var Filesystem
     */
    private $filesystem;

    /**
     * DocumentationController constructor.
     */
    public function __construct(Filesystem $filesystem)
    {
        $this->filesystem = $filesystem;
    }

    public function indexAction(): Response
    {
        return $this->getFileResponse('swagger.html');
    }

    /**
     * Retourne le schema YAML ou JSON qui permet de configurer Swagger.
     *
     * @param Request $request
     * @return Response
     */
    public function schemaAction(Request $request): Response
    {
        return $this->getFileResponse('swagger.' . $request->getRequestFormat());
    }

    /**
     * Return a Response with a body set to the content of a file
     *
     * @param string $filename
     * @return Response
     */
    private function getFileResponse(string $filename)
    {
        $pathname = $this->get('kernel')->getProjectDir() . '/docs/api/' . $filename;

        if (false === $this->filesystem->exists($pathname)) {
            throw $this->createNotFoundException("Not Found");
        }

        return new Response(file_get_contents($pathname));
    }
}
