<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Component\Import\EximJobService;
use Wizacha\Component\Import\ImportService;
use Wizacha\Component\Import\JobStatus;
use Wizacha\Component\Import\JobType;
use Wizacha\Component\Import\ImportFactory;
use Wizacha\Component\Import\ImportHandler;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\Exception\ImportAlreadyExistsException;
use Wizacha\Registry;
use Wizacha\Cscart\Exim;

class ImportController extends Controller
{
    /**
     * @var ImportService
     */
    private $importService;
    /**
     * @var ImportFactory
     */
    private $importFactory;
    /**
     * @var EximJobService
     */
    private $jobService;

    public function __construct(
        ImportService $importService,
        ImportFactory $importFactory,
        EximJobService $jobService
    ) {
        $this->importService = $importService;
        $this->importFactory = $importFactory;
        $this->jobService = $jobService;
    }

    public function productsAction(Request $request): JsonResponse
    {
        if ($this->isGranted('ROLE_ADMIN') === false
            && $this->isGranted('ROLE_VENDOR') === false
        ) {
            throw new Forbidden('You must be authenticated as an admin or a vendor');
        }

        // Set companyId which is used by the importer
        Registry::defaultInstance()->set(
            ['runtime', 'company_id'],
            $this->getUser()->getCompanyId()
        );

        try {
            $pattern = fn_get_schema('exim', 'products');

            $user = $this->getUser();
            $pendingJobIds = $this->jobService->getPendingImportJobIds($user->getCompanyId());
            if (true === $this->jobService->hasPendingJobIds($user->getCompanyId(), $pendingJobIds)) {
                throw new ImportAlreadyExistsException($pendingJobIds);
            }

            $importHandler = $this->importFactory->getHandler(
                $request,
                JobType::PRODUCTS(),
                $this->getUser(),
                $pattern
            );
            $this->importService->catalog($importHandler);
        } catch (\Throwable $exception) {
            return $this->abortJob($exception, $importHandler);
        }

        return $this->getSuccessJsonResponse($importHandler->getJob()->getId(), $pendingJobIds);
    }

    public function categoriesAction(Request $request): JsonResponse
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            throw new Forbidden('Only admin can import categories');
        }

        return $this->execute($request, 'categories', JobType::CATEGORIES());
    }

    public function attributesAction(Request $request): JsonResponse
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            throw new Forbidden('Only admin can import attributes');
        }

        // Force Admin AREA to keep compatibility with legacy code
        \Wizacha\Registry::defaultInstance()->set([\Wizacha\Config::REG_AREA], 'A');

        return $this->execute($request, 'features', JobType::ATTRIBUTES());
    }

    public function variantsAction(Request $request): JsonResponse
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            throw new Forbidden('Only admin can import variants');
        }

        return $this->execute($request, 'feature_variants', JobType::VARIANTS());
    }

    public function linksCategoriesAction(Request $request): JsonResponse
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            throw new Forbidden('Only admin can import links categories');
        }

        // Set companyId which is used by the importer
        Registry::defaultInstance()->set(
            ['runtime', 'company_id'],
            $this->getUser()->getCompanyId()
        );

        return $this->execute($request, 'link_categories', JobType::LINK_CATEGORIES());
    }

    public function divisionsAction(Request $request): JsonResponse
    {
        if (!$this->isGranted('ROLE_VENDOR') && !$this->isGranted('ROLE_ADMIN')) {
            throw new Forbidden('Only admin or vendor can import divisions');
        }

        // Set companyId which is used by the importer
        Registry::defaultInstance()->set(
            ['runtime', 'company_id'],
            $this->getUser()->getCompanyId()
        );

        // Set userId which is used by the importer
        Registry::defaultInstance()->set(
            ['runtime', 'user_id'],
            $this->getUser()->getId()
        );

        return $this->execute($request, 'available_offers_divisions', JobType::AVAILABLE_OFFERS());
    }

    public function multiVendorProductsAction(Request $request): JsonResponse
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            throw new Forbidden('Only admin can import multi vendor products');
        }

        // Set userId which is used by the importer
        Registry::defaultInstance()->set(
            ['runtime', 'user_id'],
            $this->getUser()->getId()
        );

        return $this->execute($request, 'multi_vendor_products', JobType::MULTI_VENDOR_PRODUCTS());
    }

    public function relatedProductsAction(Request $request): JsonResponse
    {
        if (false === $this->isGranted('ROLE_VENDOR')
            && false === $this->isGranted('ROLE_ADMIN')
        ) {
            throw new Forbidden('Only admin or vendor can import related products');
        }

        // Set user_info which is used by the importer
        Registry::defaultInstance()->set(
            ['user_info'],
            fn_get_user_short_info($this->getUser()->getId())
        );

        // Set company_id which is used by the importer
        Registry::defaultInstance()->set(
            ['runtime', 'company_id'],
            $this->getUser()->getCompanyId() ?? 0
        );

        return $this->execute($request, 'related_products', JobType::RELATED_PRODUCTS());
    }

    public function productPricesAction(Request $request): JsonResponse
    {
        return $this->importInventories($request, 'inventory_prices', JobType::PRODUCT_PRICES());
    }

    public function productQuantitiesAction(Request $request): JsonResponse
    {
        return $this->importInventories($request, 'inventory_quantities', JobType::PRODUCT_QUANTITIES());
    }

    private function importInventories(Request $request, string $pattern_id, JobType $jobType): JsonResponse
    {
        // Set companyId which is used by the importer
        Registry::defaultInstance()->set(
            ['runtime', 'company_id'],
            $this->getUser()->getCompanyId() ?? 0
        );

        try {
            $pattern = Exim::get_pattern_definition($pattern_id, 'import');

            $user = $this->getUser();
            $pendingJobIds = $this->jobService->getPendingImportJobIds($user->getCompanyId());
            if (true === $this->jobService->hasPendingJobIds($user->getCompanyId(), $pendingJobIds)) {
                throw new ImportAlreadyExistsException($pendingJobIds);
            }

            $importHandler = $this->importFactory->getHandler(
                $request,
                $jobType,
                $this->getUser(),
                $pattern
            );
            if ($jobType == JobType::PRODUCT_QUANTITIES()) {
                $this->importService->importInventory($importHandler, 'quantities');
            } else {
                $this->importService->importInventory($importHandler, 'prices');
            }
        } catch (\Throwable $exception) {
            return $this->abortJob($exception, $importHandler);
        }

        return $this->getSuccessJsonResponse($importHandler->getJob()->getId(), $pendingJobIds);
    }

    public function productAttributesAction(Request $request): JsonResponse
    {
        if (false === $this->isGranted('ROLE_VENDOR')
            && false === $this->isGranted('ROLE_ADMIN')
        ) {
            throw new Forbidden('Only admin or vendor can import product attributes');
        }

        // Set company_id which is used by the importer
        Registry::defaultInstance()->set(
            ['runtime', 'company_id'],
            $this->getUser()->getCompanyId() ?? 0
        );

        return $this->execute($request, 'product_attributes', JobType::PRODUCT_ATTRIBUTES());
    }

    private function execute(Request $request, string $pattern, JobType $jobType): JsonResponse
    {
        $importHandler = null;

        try {
            $pattern = \Wizacha\Cscart\Exim::get_pattern_definition($pattern, 'import');

            $user = $this->getUser();
            $pendingJobIds = $this->jobService->getPendingImportJobIds($user->getCompanyId());
            if (true === $this->jobService->hasPendingJobIds($user->getCompanyId(), $pendingJobIds)) {
                throw new ImportAlreadyExistsException($pendingJobIds);
            }

            $importHandler = $this->importFactory->getHandler(
                $request,
                $jobType,
                $this->getUser(),
                $pattern
            );
            $this->importService->entities($importHandler);
        } catch (\Throwable $exception) {
            return $this->abortJob($exception, $importHandler);
        }

        return $this->getSuccessJsonResponse($importHandler->getJob()->getId(), $pendingJobIds);
    }

    private function abortJob(
        \Throwable $exception,
        ?ImportHandler $importHandler
    ): BadRequestJsonResponse {
        if ($importHandler instanceof ImportHandler
            && $importHandler->getJob()->getId()
        ) {
            EximJobService::error(
                $exception->getMessage(),
                $importHandler->getJob()->getId(),
                $importHandler->getJob()->getNbLines() + 1 // Its a fake last line to set status
            );

            /** no more pending if aborted */
            $job = $this->jobService->get($importHandler->getJob()->getId());
            $job->setStatus(JobStatus::ERROR());
            $job->finish();
            $this->jobService->save($job);
        }

        return new BadRequestJsonResponse(
            $exception->getMessage(),
            [],
            ($exception instanceof ImportAlreadyExistsException) ? ['pendingJobIds' => $exception->getPendingJobIds()] : []
        );
    }

    /**
     * @param string $jobId
     * @param string[] $pendingJobIds
     * @return JsonResponse
     */
    private function getSuccessJsonResponse(string $jobId, array $pendingJobIds): JsonResponse
    {
        return new JsonResponse([
            'jobId' => $jobId,
            'pendingJobIds' => $pendingJobIds
        ]);
    }
}
