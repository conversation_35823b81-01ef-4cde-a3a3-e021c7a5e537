<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Bridge\Symfony\Response\InternalServerErrorJsonResponse;
use Wizacha\Bridge\Symfony\Response\NotFoundJsonResponse;
use Wizacha\Component\MondialRelay\Exception\ApiException;
use Wizacha\Component\MondialRelay\Model\Search;
use Wizacha\Marketplace\Order\Exception\OrderNotFound;
use Wizacha\Marketplace\Order\OrderStatus;

class MondialRelayController extends Controller
{
    public function listPickupPointsAction(Request $request): JsonResponse
    {
        if (empty($this->container->getParameter('mondial_relay.api.userid')) || empty($this->container->getParameter('mondial_relay.api.password'))) {
            return new InternalServerErrorJsonResponse('Missing Mondial Relay API credentials');
        }

        if (!$zipCode = $request->query->get('zipCode')) {
            return BadRequestJsonResponse::missingField('zipCode');
        }

        $search = (new Search())->setZipCode($zipCode);

        if ($request->query->has('country')) {
            $search->setWeight($request->query->get('country'));
        }

        if ($request->query->has('weight')) {
            $search->setWeight($request->query->getInt('weight'));
        }

        if ($request->query->has('distance')) {
            $search->setDistance($request->query->getInt('distance'));
        }

        if ($request->query->has('limit')) {
            $search->setLimit($request->query->getInt('limit'));
        }

        try {
            $pickupPoints = $this->container->get('marketplace.mondial_relay.client')->listPickupPoints($search);
        } catch (ApiException $exception) {
            return new NotFoundJsonResponse($exception->getMessage());
        }

        return new JsonResponse($pickupPoints);
    }

    public function getPickupPointAction(string $pickupPointId): JsonResponse
    {
        try {
            $pickupPoint = $this->container->get('marketplace.mondial_relay.client')->getPickupPoint($pickupPointId);
        } catch (ApiException $exception) {
            return new NotFoundJsonResponse($exception->getMessage());
        }

        return new JsonResponse($pickupPoint);
    }

    public function getBrandCodeAction(): JsonResponse
    {
        return new JsonResponse(['brandCode' => $this->container->getParameter('mondial_relay.api.userid')]);
    }
}
