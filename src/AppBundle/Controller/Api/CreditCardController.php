<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Wizacha\AppBundle\Service\WhitelistDomainsService;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Marketplace\CreditCard\CreditCard;
use Wizacha\Marketplace\CreditCard\Repository\CreditCardRepository;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Payment\PaymentProcessorIdentifier;
use Wizacha\Marketplace\Payment\PaymentService;
use Wizacha\Marketplace\Traits\AssertCanAccessUserAccountTrait;
use Wizacha\Marketplace\Traits\ContraintViolationTrait;
use Wizacha\Marketplace\Traits\PaginationValidatorTrait;
use Wizacha\Marketplace\User\UserRepository;

class CreditCardController extends Controller
{
    use AssertCanAccessUserAccountTrait;
    use PaginationValidatorTrait;
    use ContraintViolationTrait;

    /** @var CreditCardRepository */
    protected $creditCardRepository;

    /** @var UserRepository */
    protected $userRepository;

    /** @var ValidatorInterface */
    protected $validator;

    /** @var PaymentProcessorIdentifier[] */
    protected $allowedProcessors = [];

    /** @var PaymentService */
    protected $paymentService;

    /** @var WhitelistDomainsService */
    private $whitelistDomainsService;

    public function __construct(
        CreditCardRepository $creditCardRepository,
        UserRepository $userRepository,
        ValidatorInterface $validator,
        PaymentService $paymentService,
        WhitelistDomainsService $whitelistDomainsService
    ) {
        $this->creditCardRepository = $creditCardRepository;
        $this->userRepository = $userRepository;
        $this->validator = $validator;
        $this->allowedProcessors = [
            PaymentProcessorIdentifier::HIPAY_CARD()->getValue(),
            PaymentProcessorIdentifier::STRIPE_CARD()->getValue(),
        ];
        $this->paymentService = $paymentService;
        $this->whitelistDomainsService = $whitelistDomainsService;
    }

    public function listAction(Request $request, int $userId): JsonResponse
    {
        $this->assertCanAccessUserAccount($userId);

        $violationsPagination = $this->paginationValidator($request);

        if (\count($violationsPagination) > 0) {
            return $this->badRequest($violationsPagination);
        }

        $pagination = $this->paginationResolver($request);

        $user = $this->userRepository->get($userId);

        return new JsonResponse([
            "limit" => $pagination['limit'],
            "offset" => $pagination['offset'],
            "total" => \count($this->creditCardRepository->findByUser($user)),
            "items" => $this->creditCardRepository->findByUser(
                $user,
                $pagination['limit'],
                $pagination['offset']
            ),
        ]);
    }

    public function getAction(int $userId, string $cardId): JsonResponse
    {
        $this->assertCanAccessUserAccount($userId);

        $creditCard = $this->creditCardRepository->findOneById(
            $cardId,
            $this->userRepository->get($userId)
        );

        if (false === $creditCard instanceof CreditCard) {
            throw NotFound::fromId('CreditCard', $cardId);
        }

        return new JsonResponse($creditCard);
    }

    public function registrationAction(Request $request, int $userId): JsonResponse
    {
        $user = $this->assertCanAccessUserAccount($userId);
        $paymentId = $request->query->getInt('paymentId', 0);
        $redirectUrl = $request->query->get('redirectUrl', null);
        $cssUrl = $request->query->get('cssUrl', null);

        if (0 === $paymentId) {
            return BadRequestJsonResponse::missingField('paymentId');
        }

        if (false === \is_string($redirectUrl) || false === \filter_var($redirectUrl, FILTER_VALIDATE_URL)) {
            return BadRequestJsonResponse::invalidField('redirectUrl', 'redirectUrl must be an URI.');
        }

        if ($this->whitelistDomainsService->isWhitedDomainHost($redirectUrl) === false) {
            return BadRequestJsonResponse::invalidField('redirectUrl', 'Domain is not whitelisted');
        }

        if (\is_string($cssUrl) && false === \filter_var($cssUrl, FILTER_VALIDATE_URL)) {
            return BadRequestJsonResponse::invalidField('cssUrl', 'cssUrl must be an URI.');
        }

        $paymentMethodData = fn_get_payment_method_data($paymentId);

        if (false === \in_array((int) $paymentMethodData['processor_id'], $this->allowedProcessors, true)) {
            return new JsonResponse([
                'errors' => [
                    'message' => "This 'paymentId' is not implemented yet."
                ]
            ], JsonResponse::HTTP_NOT_IMPLEMENTED);
        }

        return new JsonResponse([
            'url' => $this
                ->paymentService
                ->getPaymentProcessor($paymentMethodData)
                ->renewCreditCard($user, $redirectUrl, $cssUrl)
                ->getRedirectUrl(),
        ]);
    }
}
