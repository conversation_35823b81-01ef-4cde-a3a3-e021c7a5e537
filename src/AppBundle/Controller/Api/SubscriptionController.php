<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Wizacha\AppBundle\Controller\DomainUserTrait;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Subscription\Event\SubscriptionAutoRenewEvent;
use Wizacha\Marketplace\Subscription\Event\SubscriptionStatusEvent;
use Wizacha\Marketplace\Subscription\Log\SubscriptionActionTrace;
use Wizacha\Marketplace\Subscription\Log\SubscriptionActionTraceRepository;
use Wizacha\Marketplace\Subscription\Log\SubscriptionActionTraceService;
use Wizacha\Marketplace\Subscription\Log\SubscriptionEventType;
use Wizacha\Marketplace\Subscription\Subscription;
use Wizacha\Marketplace\Subscription\SubscriptionRepository;
use Wizacha\Marketplace\Subscription\SubscriptionService;
use Wizacha\Marketplace\Subscription\SubscriptionStatus;
use Wizacha\Marketplace\Traits\ContraintViolationTrait;
use Wizacha\Marketplace\Traits\PaginationValidatorTrait;
use Wizacha\Marketplace\Traits\SubscriptionFilterValidatorTrait;

class SubscriptionController extends Controller
{
    use DomainUserTrait;
    use PaginationValidatorTrait;
    use ContraintViolationTrait;
    use SubscriptionFilterValidatorTrait;

    protected ValidatorInterface $validator;
    protected SubscriptionService $subscriptionService;
    protected SubscriptionRepository $subscriptionRepository;
    protected AuthorizationCheckerInterface $authorizationChecker;
    protected SubscriptionActionTraceRepository $subscriptionLogRepository;
    protected SubscriptionActionTraceService $subscriptionLogService;
    protected EventDispatcherInterface $eventDispatcher;

    public function __construct(
        ValidatorInterface $validator,
        SubscriptionService $subscriptionService,
        SubscriptionRepository $subscriptionRepository,
        AuthorizationCheckerInterface $authorizationChecker,
        SubscriptionActionTraceRepository $subscriptionActionTraceRepository,
        SubscriptionActionTraceService $subscriptionLogService,
        EventDispatcherInterface $eventDispatcher
    ) {
        $this->validator = $validator;
        $this->subscriptionService = $subscriptionService;
        $this->subscriptionRepository = $subscriptionRepository;
        $this->authorizationChecker = $authorizationChecker;
        $this->subscriptionActionTraceRepository = $subscriptionActionTraceRepository;
        $this->subscriptionLogService = $subscriptionLogService;
        $this->eventDispatcher = $eventDispatcher;
    }

    public function listAction(Request $request): JsonResponse
    {
        $violationsPagination = $this->paginationValidator($request);

        if (\count($violationsPagination) > 0) {
            return $this->badRequest($violationsPagination);
        }

        $pagination = $this->paginationResolver($request);
        $violationsFilters = $this->subscriptionFilterValidator($request);

        if (\count($violationsFilters) > 0) {
            return $this->badRequest($violationsFilters);
        }

        if (false === $this->authorizationChecker->isGranted('ROLE_ADMIN')) {
            throw new Forbidden();
        }

        [1 => $totalCount] = $this->subscriptionRepository->findByFilters();
        [$items] = $this->subscriptionRepository->findByFilters(
            $pagination['limit'],
            $pagination['offset'],
            $request->query->all()
        );

        return new JsonResponse([
            "limit" => $pagination['limit'],
            "offset" => $pagination['offset'],
            "total" => $totalCount,
            "items" => $items,
        ]);
    }

    public function getAction(string $subscriptionId): JsonResponse
    {
        $subscription = $this->subscriptionService->get($subscriptionId);

        $this->assertCanAccessSubscription($subscription);

        if (false === $subscription instanceof Subscription) {
            throw NotFound::fromId("Subscription", $subscriptionId);
        }

        return new JsonResponse($subscription->expose());
    }

    public function logsAction(Request $request, string $subscriptionId): JsonResponse
    {
        $subscription = $this->subscriptionService->get($subscriptionId);

        if ($subscription instanceof Subscription === false) {
            throw NotFound::fromId("Subscription", $subscriptionId);
        }

        $this->assertCanAccessSubscription($subscription);

        $violationsPagination = $this->paginationValidator($request);
        if (\count($violationsPagination) > 0) {
            return $this->badRequest($violationsPagination);
        }

        $pagination = $this->paginationResolver($request);
        $paginationLimit = $pagination['limit'];
        $limit = $request->query->getInt('limit', $paginationLimit);
        if (\is_numeric($limit) === false || $limit > $paginationLimit || $limit < 1) {
            $limit = $paginationLimit;
        }

        $paginationOffset = $pagination['offset'];
        $offset = $request->query->getInt('offset', $paginationOffset);
        if (\is_numeric($offset) === false || $offset > $paginationOffset || $offset < 1) {
            $offset = $paginationOffset;
        }

        $items = $this->subscriptionActionTraceRepository->getLogsFromSubscriptionId(
            $subscriptionId,
            $limit,
            $offset
        );

        $total = $this->subscriptionActionTraceRepository->countLogsFromSubscriptionId($subscriptionId);

        $items =  \array_map(static function (SubscriptionActionTrace $subscriptionActionTrace): array {
            $subscription = $subscriptionActionTrace->jsonSerialize();
            unset($subscription['user']);
            return $subscription;
        }, $items);

        return new JsonResponse([
            'limit' => $pagination['limit'],
            'offset' => $pagination['offset'],
            'total' => $total,
            'items' => $items,
        ]);
    }

    public function patchAction(Request $request, string $subscriptionId): JsonResponse
    {
        $subscription = $this->subscriptionService->get($subscriptionId);

        if (false === $subscription instanceof Subscription) {
            throw NotFound::fromId('Subscription', $subscriptionId);
        }

        $this->assertCanAccessSubscription($subscription);

        $errors = [];
        $newSubscriptionStatus = null;

        if (true === $request->request->has('status')
            && (true === $this->authorizationChecker->isGranted('ROLE_VENDOR')
            || true === $this->authorizationChecker->isGranted('ROLE_ADMIN'))
        ) {
            try {
                $newSubscriptionStatus = new SubscriptionStatus($request->request->get('status'));

                if ($this->authorizationChecker->isGranted('ROLE_VENDOR')
                    && SubscriptionStatus::SUSPENDED()->equals($newSubscriptionStatus)
                ) {
                    $errors[] = ['message' => 'Unallowed status "' . SubscriptionStatus::SUSPENDED() . '".'];
                } else {
                    $this->subscriptionService->updateSubscriptionStatus($subscription, $newSubscriptionStatus);
                }
            } catch (\UnexpectedValueException $exception) {
                $errors[] = ['message' => 'Unrecognized status "' . $request->request->get('status') . '".'];
            }
        }

        if (true === $request->request->has('quantity')) {
            $quantityErrors = $this->subscriptionService->checkQuantity($request, $subscription);

            if (\count($quantityErrors) > 0) {
                $errors = \array_merge($errors, $quantityErrors);
            } else {
                $subscription = $this->subscriptionService->updateQuantity(
                    $subscription,
                    $request->request->get('quantity')
                );
            }
        }

        $oldValueOfAutoRenew = $subscription->isAutoRenew();
        $newValueOfAutoRenew = null;
        if (true === $request->request->has('isAutorenew')) {
            $isAutorenew = $request->request->get('isAutorenew');

            if (\in_array($isAutorenew, ['true', '1', true, 1], true)) {
                $subscription->setIsAutoRenew(true);
                $newValueOfAutoRenew = true;
            } elseif (\in_array($isAutorenew, ['false', '0', false, 0], true)) {
                $subscription->setIsAutoRenew(false);
                $newValueOfAutoRenew = false;
            } else {
                $errors[] = ['message' => 'isAutorenew must be a boolean.'];
            }
        }

        if (true === $request->request->has('commitmentEndAt')
            && (true === $this->authorizationChecker->isGranted('ROLE_VENDOR')
            || true === $this->authorizationChecker->isGranted('ROLE_ADMIN'))
        ) {
            $commitmentEndAtString = (string) $request->request->get('commitmentEndAt');
            $commitmentEndAtErrors = $this->subscriptionService->checkCommitmentEndAt($subscription, $commitmentEndAtString, 'Y-m-d');

            if (\count($commitmentEndAtErrors) > 0) {
                $errors = \array_merge($errors, $commitmentEndAtErrors);
            } else {
                $subscription->setCommitmentEndAt(\DateTimeImmutable::createFromFormat('Y-m-d', $commitmentEndAtString));
            }
        }

        if (true === $request->request->has('nextPaymentAt')) {
            if (false === $this->authorizationChecker->isGranted('ROLE_ADMIN')
                && false === $this->authorizationChecker->isGranted('ROLE_VENDOR')
            ) {
                return new JsonResponse(
                    [
                        'errors' => ['message' => 'You are not allowed to update property nextPaymentAt.']
                    ],
                    Response::HTTP_FORBIDDEN
                );
            }

            $nextPaymentAtString = (string) $request->request->get('nextPaymentAt');
            $nextPaymentAtErrors = $this->subscriptionService->checkNextPaymentAt(
                $subscription,
                $nextPaymentAtString,
                'Y-m-d'
            );

            if (\count($nextPaymentAtErrors) > 0) {
                $errors = \array_merge($errors, $nextPaymentAtErrors);
            } else {
                $subscription->setNextPaymentAt(\DateTimeImmutable::createFromFormat('Y-m-d', $nextPaymentAtString));
            }
        }

        if (\count($errors) > 0) {
            return new JsonResponse(['errors' => $errors], Response::HTTP_BAD_REQUEST);
        }

        $this->subscriptionRepository->save($subscription);

        if (\is_null($newValueOfAutoRenew) === false
            && $oldValueOfAutoRenew !== $newValueOfAutoRenew
        ) {
            $this->eventDispatcher->dispatch(
                new SubscriptionAutoRenewEvent(
                    $subscription,
                    $this->getDomainUser($this->getUser()->getId()),
                    $oldValueOfAutoRenew,
                    $newValueOfAutoRenew
                ),
                SubscriptionEventType::AUTO_RENEW_UPDATED()->getValue()
            );
        }

        return new JsonResponse($subscription->expose());
    }

    public function getTaxesAction(Request $request, string $subscriptionId): JsonResponse
    {
        $violationsPagination = $this->paginationValidator($request);

        if (\count($violationsPagination) > 0) {
            return $this->badRequest($violationsPagination);
        }

        $pagination = $this->paginationResolver($request);
        $subscription = $this->subscriptionService->get($subscriptionId);

        if (false === $subscription instanceof Subscription) {
            throw NotFound::fromId("Subscription", $subscriptionId);
        }

        $this->assertCanAccessSubscription($subscription);

        return new JsonResponse([
            "limit" => $pagination['limit'],
            "offset" => $pagination['offset'],
            "total" => \count($this->subscriptionRepository->getTaxesFromSubscriptionId($subscriptionId)),
            "items" => $this->subscriptionRepository->getTaxesFromSubscriptionId(
                $subscriptionId,
                $pagination['limit'],
                $pagination['offset']
            ),
        ]);
    }

    public function getOrdersAction(Request $request, string $subscriptionId): JsonResponse
    {
        $violationsPagination = $this->paginationValidator($request);

        if (\count($violationsPagination) > 0) {
            return $this->badRequest($violationsPagination);
        }

        $pagination = $this->paginationResolver($request);
        $subscription = $this->subscriptionService->get($subscriptionId);

        if (false === $subscription instanceof Subscription) {
            throw NotFound::fromId("Subscription", $subscriptionId);
        }

        $this->assertCanAccessSubscription($subscription);

        $orders = $this->subscriptionRepository->getSubsequentOrdersFromSubscriptionId(
            $subscriptionId,
            $pagination['limit'],
            $pagination['offset']
        );

        return new JsonResponse($orders->expose());
    }

    public function getItemsAction(Request $request, string $subscriptionId): JsonResponse
    {
        $violationsPagination = $this->paginationValidator($request);

        if (\count($violationsPagination) > 0) {
            return $this->badRequest($violationsPagination);
        }

        $pagination = $this->paginationResolver($request);
        $subscription = $this->subscriptionService->get($subscriptionId);

        if (false === $subscription instanceof Subscription) {
            throw NotFound::fromId("Subscription", $subscriptionId);
        }

        $this->assertCanAccessSubscription($subscription);

        return new JsonResponse([
            "limit" => $pagination['limit'],
            "offset" => $pagination['offset'],
            "total" => \count($this->subscriptionRepository->getItemsFromSubscriptionId($subscriptionId)),
            "items" => $this->subscriptionRepository->getItemsFromSubscriptionId(
                $subscriptionId,
                $pagination['limit'],
                $pagination['offset']
            ),
        ]);
    }

    protected function assertCompanyCanAccessSubscription(int $targetCompanyId, string $subscriptionId): void
    {
        $loggedUser = $this->getDomainUser($this->getUser()->getId());

        // An admin can access all users
        if ($this->authorizationChecker->isGranted('ROLE_ADMIN')) {
            return;
        }

        // A non-admin can only access its own account
        if ($loggedUser->getCompanyId() === $targetCompanyId) {
            return;
        }

        // 404 instead of 403 to avoid leaking information
        throw NotFound::fromId('Subscription', $subscriptionId);
    }

    protected function assertUserCanAccessSubscription(int $targetUserId, string $subscriptionId): void
    {
        $loggedUser = $this->getDomainUser($this->getUser()->getId());

        // An admin can access all users
        if ($this->authorizationChecker->isGranted('ROLE_ADMIN')) {
            return;
        }

        // A non-admin can only access its own account
        if ($loggedUser->getUserId() === $targetUserId) {
            return;
        }

        $targetUser = $this->getDomainUser($targetUserId);

        if ($loggedUser->belongsToAnOrganisation()
            && $loggedUser->getOrganisation()->isAdministrator($loggedUser)
            && $targetUser->belongsToOrganisation($loggedUser->getOrganisation())
        ) {
            return;
        }

        // 404 instead of 403 to avoid leaking information
        throw NotFound::fromId('Subscription', $subscriptionId);
    }

    protected function assertCanAccessSubscription(Subscription $subscription): self
    {
        if ($this->authorizationChecker->isGranted('ROLE_VENDOR')) {
            $this->assertCompanyCanAccessSubscription($subscription->getCompanyId(), $subscription->getId());
        } elseif ($this->authorizationChecker->isGranted('ROLE_USER')) {
            $this->assertUserCanAccessSubscription($subscription->getUser()->getUserId(), $subscription->getId());
        }

        return $this;
    }
}
