<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api;

use Tygh\Api\Response;

class Shippings extends \Tygh\Api\Entities\Shippings
{
    public function index($id = 0, $params = [])
    {
        $return = [];
        if ($this->getParentName() == 'products') {
            $auth = [];
            $parent_product = $this->getParentData();
            $product_info = fn_get_product_data($parent_product['product_id'], $auth);
            $return['data'] = $product_info['shippings'];
            array_walk(
                $return['data'],
                function (&$shipping) use ($product_info) {
                    $shipping['product_id'] = $product_info['product_id'];
                }
            );
        } else {
            $return['data'] = fn_w_get_shipping_info_for_company(\Tygh\Registry::get('runtime.company_id'), $id);
        }

        if ($id) {
            $return['data'] = $return['data'][$id];
        }

        if (empty($return['data'])) {
            $return['status'] = Response::STATUS_NOT_FOUND;
        } else {
            if ($id) {
                $return['data'] = array_merge(fn_get_shipping_info($id), $return['data']);

                // To avoid an additional request, we get the carriage_paid_threshold with the rates
                // then we move it from the lowest array level to the top, to expose it properly with the API
                if (true === \array_key_exists('carriage_paid_threshold', $return['data']['rates'][0])) {
                    $return['data']['carriage_paid_threshold'] = $return['data']['rates'][0]['carriage_paid_threshold'];
                    unset($return['data']['rates'][0]['carriage_paid_threshold']);
                }

                $data = [&$return['data']];
            } else {
                $data = &$return['data'];
            }

            array_walk(
                $data,
                function (&$_data) {
                    $_data = fn_w_object_filter($_data, 'shipping', 'api', 'display_objects');
                    fn_w_fields_cast($_data);
                    $_data = fn_w_convert_fields($_data, 'shipping', 'send', 'convert_fields_api');
                }
            );
            $return['status'] = Response::STATUS_OK;
        }

        return $return;
    }

    public function update($id, $params)
    {
        $params = fn_w_convert_fields($params, 'shipping', 'get', 'convert_fields_api');
        $status = Response::STATUS_BAD_REQUEST;

        $toReturn = [];

        if (\Tygh\Registry::get('runtime.company_id') === 0) {
            return [
                "status" => Response::STATUS_FORBIDDEN,
                "data" => ['message' => __("must_be_vendor_to_see_this_data")],
            ];
        }

        if (isset($params['rates']) && $params['rates'] === false) {
            $data['message'] =  __('api_rates_malformed');
        } elseif (true === \array_key_exists('carriage_paid_threshold', $params)
            && null !== $params['carriage_paid_threshold']
            && (false === is_numeric($params['carriage_paid_threshold']) || $params['carriage_paid_threshold'] <= 0)
        ) {
            // carriage_paid_threshold must be a decimal above 0 or null
            $data['message'] =  __('api_carriage_paid_threshold_malformed');
        } elseif (isset($params['status']) && !\in_array($params['status'], ['A', 'D'])) {
            $data['message'] =  __('api_status_unauthorized');
        } else {
            if ($this->getParentName() == 'products') {
                $auth = [];
                $parent_product = $this->getParentData();
                $product_info = fn_get_product_data($parent_product['product_id'], $auth);
                $toReturn = $this->updateProduct($id, $params, $product_info);
            } else {
                if (\Tygh\Registry::get('runtime.company_id') === 0) { // if user is admin.
                    return [
                        'status' => Response::STATUS_FORBIDDEN,
                        'data' => [
                            'message' => __("must_be_vendor_to_change_this_data"),
                        ],
                    ];
                }

                return $this->updateCompany($id, $params);
            }
        }

        if ($toReturn === []) {
            $toReturn = [
                'status' => $status,
                'data' => $data ?? [],
            ];
        }

        return $toReturn;
    }

    protected function updateCompany($id, $params)
    {
        if (isset($params['rates']) && empty($params['rates'])) {
            $data['message'] =  __('api_rates_malformed');
            return [
                'status' => Response::STATUS_BAD_REQUEST,
                'data' => $data,
            ];
        }

        // If we have carriage paid threshold but don't have rates, we need to check if the shipping already exists
        // in cscart_w_company_shipping_rates, if it's doesn't, we need to initialize the rates
        if (false === \array_key_exists('rates', $params)
            && true === \array_key_exists('carriage_paid_threshold', $params)
        ) {
            $row = db_get_row(
                "SELECT w_rate_id FROM ?:w_company_shipping_rates
                    WHERE company_id = ?i AND shipping_id = ?i",
                \Tygh\Registry::get('runtime.company_id'),
                $id
            );

            if (0 === \count($row)) {
                $params['rates'] = fn_w_complexify_rates(
                    [['amount' => 0, 'value' => 0], ['amount' => 0, 'value' => 0]]
                );
            }
        }

        if (isset($params['rates'])) {
            fn_wizacha_shippings_w_update_rate(
                'w_company_shipping_rates',
                $id,
                $params['rates'],
                'company_id',
                \Tygh\Registry::get('runtime.company_id'),
                \Tygh\Registry::get('runtime.company_id'),
                $params['carriage_paid_threshold']
            );
        } elseif (true === \array_key_exists('carriage_paid_threshold', $params)) {
            db_query(
                "UPDATE ?:w_company_shipping_rates SET ?u
                    WHERE company_id = ?i AND shipping_id = ?i",
                ['carriage_paid_threshold' => $params['carriage_paid_threshold']],
                \Tygh\Registry::get('runtime.company_id'),
                $id
            );
        }

        if (isset($params['status'])) {
            $result = db_get_row(
                "SELECT shippings FROM ?:companies WHERE company_id = ?i",
                \Tygh\Registry::get('runtime.company_id')
            );
            $shippings = explode(',', $result['shippings']);

            if (\in_array($id, $shippings)) {
                if ($params['status'] == 'D') {
                    $key = array_search($id, $shippings);
                    unset($shippings[$key]);
                }
                $shippings_req = implode(',', $shippings);
            } else {
                if ($params['status'] == 'A') {
                    if (empty($result['shippings'])) {
                        $shippings_req = "$id";
                    } else {
                        $shippings[] = $id;
                        $shippings_req = implode(',', $shippings);
                    }
                } else {
                    $shippings_req = implode(',', $shippings);
                }
            }
            db_query(
                "UPDATE ?:companies SET shippings = ?s WHERE company_id = ?i",
                $shippings_req,
                \Tygh\Registry::get('runtime.company_id')
            );
            \Wizacha\Product::checkAllProductsShippings(\Tygh\Registry::get('runtime.company_id'));
        }

        $status = Response::STATUS_OK;
        $data['shipping_id'] = $id;

        return [
            'status' => $status,
            'data' => $data,
        ];
    }

    protected function updateProduct($shipping_id, $params, $product_data)
    {
        if (isset($params['rates'])) {
            if (empty($params['rates'])) {
                fn_w_delete_rates_for_product($product_data['product_id'], $shipping_id);
            } else {
                fn_wizacha_shippings_w_update_rate(
                    'w_product_shipping_rates',
                    $shipping_id,
                    $params['rates'],
                    'product_id',
                    $product_data['product_id'],
                    \Tygh\Registry::get('runtime.company_id')
                );
            }
        }

        if ($params['status']) {
            $disable_shippings = explode(',', $product_data['w_disable_shippings']);

            if (\in_array($shipping_id, $disable_shippings)) {
                if ($params['status'] == 'A') {
                    $key = array_search($shipping_id, $disable_shippings);
                    unset($disable_shippings[$key]);
                    $shippings_req = implode(',', $disable_shippings);
                }
            } else {
                if ($params['status'] == 'D') {
                    if (empty($product_data['w_disable_shippings'])) {
                        $shippings_req = "$shipping_id";
                    } else {
                        $disable_shippings[] = $shipping_id;
                        $shippings_req = implode(',', $disable_shippings);
                    }
                }
            }

            if (isset($shippings_req)) {
                db_query(
                    "UPDATE ?:products SET w_disable_shippings = ?s WHERE product_id = ?i",
                    $shippings_req,
                    $product_data['product_id']
                );
            }
            \Wizacha\Product::checkStatusAfterChangeShippings($product_data['product_id']);
        }

        $data['shipping_id'] = $shipping_id;
        $data['product_id'] = $product_data['product_id'];

        if (\Wizacha\Product::hasChanged($product_data['product_id'])) {
            \Wizacha\Events\Config::dispatch(
                \Wizacha\Product::EVENT_UPDATE,
                (new \Wizacha\Events\IterableEvent())->setElement($product_data['product_id'])
            );
        }

        return [
            'status' => Response::STATUS_OK,
            'data' => $data,
        ];
    }

    public function childEntities()
    {
        return [
            'products',
        ];
    }

    public function privileges()
    {
        return [
            'update' => true,
            'index'  => true,
        ];
    }
}
