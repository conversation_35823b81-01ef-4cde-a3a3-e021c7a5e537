<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Marketplace\Exception\IntegrityConstraintViolation;
use Wizacha\Marketplace\PIM\Attribute\Attribute;
use Wizacha\Marketplace\PIM\Attribute\AttributeService;

class AttributeController extends Controller
{
    /** @var AttributeService */
    protected $attributeService;

    public function __construct(AttributeService $attributeService)
    {
        $this->attributeService = $attributeService;
    }

    public function listAction(): JsonResponse
    {
        $resources = $this->attributeService->list();

        $exposedRessources = array_map(function (Attribute $attribute) {
            return $attribute->expose();
        }, $resources);

        return new JsonResponse($exposedRessources);
    }

    public function readAction(int $id): JsonResponse
    {
        $resource = $this->attributeService->get($id);

        return new JsonResponse($resource->expose());
    }

    public function createAction(Request $request): JsonResponse
    {
        // Récupération des données
        $data = $this->getJsonContent($request);

        // Sauvegarde
        try {
            $resource = $this->attributeService->save($data);
        } catch (IntegrityConstraintViolation $e) {
            return new BadRequestJsonResponse($e->getMessage(), $e->getErrors());
        }

        // Retourne le bon code HTTP et le contenu de la ressource nouvellement créée
        return new JsonResponse($resource->expose(), Response::HTTP_CREATED);
    }

    public function updateAction(int $id, Request $request): JsonResponse
    {
        // Récupération des données
        $data = $this->getJsonContent($request);

        // Vérification de la présence de la ressource
        $resource = $this->attributeService->get($id);

        $data = $this->mergeDataWithCurrentValues($data, $resource);

        // Sauvegarde
        try {
            $resource = $this->attributeService->save($data, $resource);
        } catch (IntegrityConstraintViolation $e) {
            return new BadRequestJsonResponse($e->getMessage(), $e->getErrors());
        }

        // Retourne le bon code HTTP et le contenu de la ressource modifiée
        return new JsonResponse($resource->expose());
    }

    public function deleteAction(int $id): Response
    {
        $this->attributeService->delete($id);

        return new Response('', Response::HTTP_NO_CONTENT);
    }

    public function createVariantAction(Request $request, int $attributeId): JsonResponse
    {
        // Récupération des données
        $data = $this->getJsonContent($request);

        // Récupération de l'attribut
        $data['attributeId'] = $attributeId;

        // Sauvegarde
        try {
            $resource = $this->attributeService->saveVariant($data);
        } catch (IntegrityConstraintViolation $e) {
            return new BadRequestJsonResponse($e->getMessage(), $e->getErrors());
        }

        // Retourne le bon code HTTP et le contenu de la ressource nouvellement créée
        return new JsonResponse($resource->expose(), Response::HTTP_CREATED);
    }

    public function updateVariantAction(Request $request, int $attributeId, int $id): JsonResponse
    {
        // Récupération des données
        $data = $this->getJsonContent($request);

        // Vérification de la présence de l'attribut
        $this->attributeService->get($attributeId);

        // Vérification de la présence de la ressource
        $resource = $this->attributeService->getVariant($id);

        // Sauvegarde
        try {
            $resource = $this->attributeService->saveVariant($data, $resource);
        } catch (IntegrityConstraintViolation $e) {
            return new BadRequestJsonResponse($e->getMessage(), $e->getErrors());
        }

        // Retourne le bon code HTTP et le contenu de la ressource mise à jour
        return new JsonResponse($resource->expose());
    }

    public function deleteVariantAction(int $id): Response
    {
        $this->attributeService->deleteVariant($id);

        return new Response('', Response::HTTP_NO_CONTENT);
    }

    protected function getJsonContent(Request $request): array
    {
        $data = json_decode($request->getContent(), true);
        if (!\is_array($data)) {
            throw new BadRequestHttpException('Content has to be valid JSON');
        }

        return $data;
    }

    protected function mergeDataWithCurrentValues(array $data, Attribute $attribute): array
    {
        // "name" is required on update, so we don't take it from existing resource
        $validFields = [
            "categoryIds",
            "code",
            "description",
            "id",
            "isASearchFilter",
            "isDisplayedOnProduct",
            "isIndexedForTextSearch",
            "isOnlyEditableByAdmin",
            "isRequired",
            "isUsedForRecommendations",
            "parentId",
            "position",
            "status",
        ];

        $defaultData = $attribute->expose();
        $defaultData['parentId'] = $defaultData['parent'] ? $defaultData['parent']['id'] : '';
        $defaultData = array_intersect_key($defaultData, array_flip($validFields));

        return array_merge($defaultData, $data);
    }
}
