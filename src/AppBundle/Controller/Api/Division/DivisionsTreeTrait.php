<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\Division;

use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\OptionsResolver\Options;
use Symfony\Component\OptionsResolver\OptionsResolver;

trait DivisionsTreeTrait
{
    /**
     * Resolve parameters query parameters for a divisions tree
     *
     * @param mixed[] $parameters parameters to resolve
     * @return mixed[]
     */
    public function resolveOptions(array $parameters): array
    {
        return (new OptionsResolver())
            ->setDefined(['isEnabled', 'rootCode'])
            ->setAllowedTypes('isEnabled', 'string')
            ->setAllowedTypes('rootCode', 'string')
            ->setNormalizer('isEnabled', function (Options $options, $value): bool {
                return $value === 'true';
            })
            ->setNormalizer('rootCode', function (Options $options, $value): string {
                return \mb_strtoupper($value);
            })
            ->resolve($parameters)
        ;
    }

    public function isAvailableOffersEnabled(): bool
    {
        return \filter_var($this->container->getParameter('feature.available_offers'), FILTER_VALIDATE_BOOLEAN);
    }

    public function notImplementedResponse(): Response
    {
        return new Response(null, Response::HTTP_NOT_IMPLEMENTED);
    }

    public function setDivisionCache(Response $response): self
    {
        if ($this->getParameter('feature.cache_http')) {
            $response->setVary('Accept-Language');
            $response->setSharedMaxAge($this->getParameter('cache_api_divisions'));
        }

        return $this;
    }
}
