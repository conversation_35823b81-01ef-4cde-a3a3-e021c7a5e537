<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\Division;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Wizacha\AppBundle\Controller\Api\Division\Dto\ApiDivisionSettingsDto;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\FeatureFlag\FeatureFlaggableInterface;
use Wizacha\Marketplace\Division\Exception\InvalidDivisionCodeException;
use Wizacha\Marketplace\Division\Service\DivisionService;
use Wizacha\Marketplace\Division\Service\DivisionSettingsService;
use Wizacha\Marketplace\GlobalState\GlobalState;

class DivisionController extends Controller implements FeatureFlaggableInterface
{
    use DivisionsTreeTrait;

    /** @var DivisionSettingsService */
    private $divisionSettingsService;

    /** @var DivisionService */
    private $divisionService;

    public function __construct(
        DivisionSettingsService $divisionSettingsService,
        DivisionService $divisionService
    ) {
        $this->divisionSettingsService = $divisionSettingsService;
        $this->divisionService = $divisionService;
    }

    public function getAction(): Response
    {
        $marketplaceDivisionSettings = $this->divisionSettingsService->getMarketplaceDivisionSettings();

        return $this->json(
            (new ApiDivisionSettingsDto(
                $marketplaceDivisionSettings->getIncludedDivisions()->toArray(),
                $marketplaceDivisionSettings->getExcludedDivisions()->toArray()
            ))->toArray()
        );
    }

    public function getTreeAction(Request $request): Response
    {
        try {
            $options = $this->resolveOptions($request->query->all());
        } catch (\InvalidArgumentException $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        $divisions = $this->divisionService->getMarketplaceDivisions(
            (string) GlobalState::contentLocale(),
            true,
            $options['isEnabled'],
            $options['rootCode']
        );

        $response = new JsonResponse();

        $this->setDivisionCache($response);

        $response->setData($divisions);
        $response->setStatusCode(
            \count($divisions) > 0
                ? Response::HTTP_OK
                : Response::HTTP_NOT_FOUND
        );

        return $response;
    }

    public function putAction(Request $request): Response
    {
        try {
            $payload = (new OptionsResolver())
                ->setRequired(['included', 'excluded'])
                ->setAllowedTypes('included', 'array')
                ->setAllowedTypes('excluded', 'array')
                ->resolve($request->request->all())
            ;
        } catch (\InvalidArgumentException $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        try {
            $this->divisionSettingsService->updateMarketplaceDivisionSettings(
                new ApiDivisionSettingsDto($payload['included'], $payload['excluded'])
            );
        } catch (InvalidDivisionCodeException $exception) {
            throw new BadRequestHttpException($exception->getMessage(), $exception);
        }

        return $this->json(null, Response::HTTP_NO_CONTENT);
    }

    public function getFeatureFlag(): string
    {
        return 'feature.enable_divisions';
    }
}
