<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api\Division\Dto;

final class ApiDivisionSettingsDto
{
    /** @var string[] */
    private $excluded = [];

    /** @var string[] */
    private $included = [];

    /**
     * DivisionListDto constructor.
     * @param string[] $includedDivisions
     * @param string[] $excludedDivisions
     */
    public function __construct(array $includedDivisions, array $excludedDivisions)
    {
        $this->included = $includedDivisions;
        $this->excluded = $excludedDivisions;
    }

    /** @return string[] */
    public function getIncluded(): array
    {
        return $this->included;
    }

    /** @return string[] */
    public function getExcluded(): array
    {
        return $this->excluded;
    }

    /** @return string[][] */
    public function toArray(): array
    {
        return [
            'included' => $this->included,
            'excluded' => $this->excluded,
        ];
    }
}
