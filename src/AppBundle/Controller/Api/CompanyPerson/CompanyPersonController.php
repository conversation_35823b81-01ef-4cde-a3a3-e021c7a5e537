<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api\CompanyPerson;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Wizacha\Bridge\Symfony\Response\AccessDeniedJsonResponse;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Bridge\Symfony\Response\NotFoundJsonResponse;
use Wizacha\Bridge\Symfony\Response\UnauthorizedJsonResponse;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\CompanyPerson\CompanyPerson;
use Wizacha\Marketplace\CompanyPerson\CompanyPersonEvents;
use Wizacha\Marketplace\CompanyPerson\CompanyPersonService;
use Wizacha\Marketplace\CompanyPerson\CompanyPersonTrait;
use Wizacha\Marketplace\CompanyPerson\Event\UBOSubmitted;
use Wizacha\Marketplace\CompanyPerson\Exception\CompanyPersonNotFound;
use Wizacha\Marketplace\Exception\CompanyNotFound;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\Exception\IntegrityConstraintViolation;
use Wizacha\Marketplace\Exception\Unauthorized;
use Wizacha\Marketplace\Payment\Processor\MangoPay;
use Wizacha\Marketplace\PSP\UboMangopay\UboMangopayService;

class CompanyPersonController extends AbstractController
{
    use CompanyPersonTrait;

    protected CompanyPersonService $companyPersonService;
    protected CompanyService $companyService;
    protected UboMangopayService $uboMangopayService;
    private EventDispatcherInterface $eventDispatcher;
    private MangoPay $mangoPay;

    public function __construct(
        CompanyPersonService $companyPersonService,
        CompanyService $companyService,
        UboMangopayService $uboMangopayService,
        EventDispatcherInterface $eventDispatcher,
        MangoPay $mangoPay
    ) {
        $this->companyPersonService = $companyPersonService;
        $this->companyService = $companyService;
        $this->uboMangopayService = $uboMangopayService;
        $this->eventDispatcher = $eventDispatcher;
        $this->mangoPay = $mangoPay;
    }

    public function addCompanyPersonAction(int $companyId, Request $request): JsonResponse
    {
        try {
            $company = $this->companyService->get($companyId);
        } catch (CompanyNotFound $e) {
            return new NotFoundJsonResponse($e->getMessage());
        }

        try {
            $this->assertCanAccessCompanyPerson($company);
        } catch (Unauthorized $e) {
            return new AccessDeniedJsonResponse($e->getMessage());
        } catch (Forbidden $e) {
            return new UnauthorizedJsonResponse($e->getMessage());
        }

        $companyPersonList = $this->companyPersonService->getByCompanyId($companyId);
        if (\count($companyPersonList) >= 4) {
            return new BadRequestJsonResponse('Cannot create more than 4 persons for one vendor.');
        }

        $companyPerson = new CompanyPerson();

        try {
            $companyPerson = $this->validateForm($companyPerson, $request);
        } catch (IntegrityConstraintViolation $e) {
            return new BadRequestJsonResponse($e->getMessage(), $e->getErrors());
        }

        $companyPerson->setCompanyId($companyId);

        try {
            $this->validateOwnershipPercentage($companyId, $companyPerson);
        } catch (BadRequestHttpException $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        $companyPerson = $this->companyPersonService->save($companyPerson);

        return new JsonResponse($companyPerson->expose(), Response::HTTP_CREATED);
    }

    public function updateCompanyPersonAction(int $companyId, int $personId, Request $request): JsonResponse
    {
        try {
            $company = $this->companyService->get($companyId);
        } catch (CompanyNotFound $e) {
            return new NotFoundJsonResponse($e->getMessage());
        }

        try {
            $this->assertCanAccessCompanyPerson($company);
        } catch (Unauthorized $e) {
            return new AccessDeniedJsonResponse($e->getMessage());
        } catch (Forbidden $e) {
            return new UnauthorizedJsonResponse($e->getMessage());
        }

        try {
            $companyPerson = $this->companyPersonService->get($personId);
        } catch (CompanyPersonNotFound $e) {
            return new NotFoundJsonResponse($e->getMessage());
        }

        try {
            $companyPerson = $this->validateForm($companyPerson, $request);
        } catch (IntegrityConstraintViolation $e) {
            return new BadRequestJsonResponse($e->getMessage(), $e->getErrors());
        }

        try {
            $this->validateOwnershipPercentage($companyId, $companyPerson);
        } catch (BadRequestHttpException $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        $companyPerson = $this->companyPersonService->save($companyPerson);

        return new JsonResponse($companyPerson->expose(), Response::HTTP_OK);
    }

    public function getCompanyPersonListAction(int $companyId): JsonResponse
    {
        try {
            $company = $this->companyService->get($companyId);
        } catch (CompanyNotFound $e) {
            return new NotFoundJsonResponse($e->getMessage());
        }

        try {
            $this->assertCanGetCompanyPerson($company);
        } catch (Unauthorized $e) {
            return new AccessDeniedJsonResponse($e->getMessage());
        } catch (Forbidden $e) {
            return new UnauthorizedJsonResponse($e->getMessage());
        }

        $companyPersonList = $this->companyPersonService->getByCompanyId($companyId);

        $exposedCompanyPersonList = array_map(function (CompanyPerson $companyPerson) {
            return $companyPerson->expose();
        }, $companyPersonList);

        return new JsonResponse($exposedCompanyPersonList, Response::HTTP_OK);
    }

    public function deleteCompanyPersonAction(int $companyId, int $personId): JsonResponse
    {
        try {
            $company = $this->companyService->get($companyId);
        } catch (CompanyNotFound $e) {
            return new AccessDeniedJsonResponse($e->getMessage());
        }

        try {
            $this->assertCanAccessCompanyPerson($company);
        } catch (Unauthorized $e) {
            return new AccessDeniedJsonResponse($e->getMessage());
        } catch (Forbidden $e) {
            return new UnauthorizedJsonResponse($e->getMessage());
        }

        try {
            $this->companyPersonService->delete($personId);
        } catch (CompanyPersonNotFound $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        return new JsonResponse('', Response::HTTP_NO_CONTENT);
    }

    public function submitUBOAction(int $companyId): JsonResponse
    {
        try {
            $company = $this->companyService->get($companyId);
        } catch (Unauthorized $e) {
            return new AccessDeniedJsonResponse('CompanyId not found');
        }

        try {
            $this->assertCanAccessCompanyPerson($company);
        } catch (Unauthorized $e) {
            return new AccessDeniedJsonResponse($e->getMessage());
        } catch (Forbidden $e) {
            return new UnauthorizedJsonResponse($e->getMessage());
        }

        try {
            $this->eventDispatcher->dispatch(
                new UBOSubmitted($company->getId()),
                CompanyPersonEvents::SUBMITTED
            );
        } catch (CompanyPersonNotFound $e) {
            return new NotFoundJsonResponse($e->getMessage());
        } catch (\InvalidArgumentException $e) {
            return new BadRequestJsonResponse($e->getMessage());
        } catch (\Exception $e) {
            return new JsonResponse($e->getMessage(), $e->getCode());
        }

        return new JsonResponse('UBO submission successfully completed, validation in progress', Response::HTTP_OK);
    }
}
