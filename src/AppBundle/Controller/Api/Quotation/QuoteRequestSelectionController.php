<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\Quotation;

use http\Exception\InvalidArgumentException;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Wizacha\AppBundle\Security\User\ApiSecurityUser;
use Wizacha\FeatureFlag\FeatureFlaggableInterface;
use Wizacha\Marketplace\Quotation\Entity\QuoteRequestSelection;
use Wizacha\Marketplace\Quotation\QuoteRequestService;

class QuoteRequestSelectionController extends Controller implements FeatureFlaggableInterface
{
    private QuoteRequestService $quoteRequestService;

    public function __construct(QuoteRequestService $quoteRequestService)
    {
        $this->quoteRequestService = $quoteRequestService;
    }

    public function getFeatureFlag(): string
    {
        return 'feature.quote_requests';
    }

    public function listAction(Request $request): JsonResponse
    {
        try {
            // Closure to validate input date format
            $dateValidationClosure = function ($value) {
                if ($value === null) {
                    return true;
                }

                if (true === \array_key_exists('from', $value)) {
                    $from = \DateTime::createFromFormat(\DateTime::RFC3339, $value['from']);
                    if ($from === false) {
                        throw new \InvalidArgumentException('Date should be in RFC3339 date format.');
                    }
                }
                if (true === \array_key_exists('to', $value)) {
                    $from = \DateTime::createFromFormat(\DateTime::RFC3339, $value['to']);
                    if ($from === false) {
                        throw new \InvalidArgumentException('Date should be in RFC3339 date format.');
                    }
                }
                return true;
            };

            $payload = (new OptionsResolver())
                ->setDefaults([
                    'limit' => 100,
                    'offset' => 0,
                    'ids' => null,
                    'userIds' => null,
                    'active' => null,
                    'declinationIds' => null,
                    'quoteRequestIds' => null,
                    'creationPeriod' => null,
                    'updatedPeriod' => null,
                ])
                ->setAllowedTypes('ids', ['null', 'string[]'])
                ->setAllowedTypes('userIds', ['null', 'string[]'])
                ->setAllowedTypes('declinationIds', ['null', 'string[]'])
                ->setAllowedTypes('quoteRequestIds', ['null', 'string[]'])
                ->setAllowedTypes('creationPeriod', ['null', 'string[]'])
                ->setAllowedTypes('updatedPeriod', ['null', 'string[]'])
                ->setAllowedValues('creationPeriod', $dateValidationClosure)
                ->setAllowedValues('updatedPeriod', $dateValidationClosure)
                ->setAllowedValues('limit', fn ($value) => \is_numeric($value))
                ->setAllowedValues('offset', fn ($value) => \is_numeric($value))
                ->setAllowedValues('active', fn ($value) => \in_array($value, [null, "0", "1", "false", "true"]))
                ->resolve($request->query->all());
        } catch (\InvalidArgumentException $e) {
            throw new BadRequestHttpException($e->getMessage());
        }

        // All GET parameters are string, so we need to convert them
        $payload['limit'] = (int) $payload['limit'];
        $payload['offset'] = (int) $payload['offset'];

        if ($payload['active'] !== null) {
            $payload['active'] = \filter_var($payload['active'], FILTER_VALIDATE_BOOLEAN);
        }

        /** @var ApiSecurityUser $user */
        $user = $this->getUser();
        $this->quoteRequestService->getOrCreateActiveSelection($user->getId(), $user->getRoles());
        $quoteSelections = $this->quoteRequestService->filter($user->getId(), $payload, $user->getRoles());

        $items = \array_map(fn($quoteSelection) => $quoteSelection->expose(), $quoteSelections);
        return new JsonResponse([
            'limit' => $payload['limit'],
            'offset' => $payload['offset'],
            'total' => (int) $this->quoteRequestService->count($user->getId(), $payload, $user->getRoles()),
            'items' => $items
        ]);
    }

    public function addDeclinationsAction(Request $request, int $quoteRequestSelectionId = null): JsonResponse
    {
        if ($quoteRequestSelectionId === null) {
            $quoteRequestSelectionId = $this->quoteRequestService->getOrCreateActiveSelection(
                $this->getUser()->getId(),
                $this->getUser()->getRoles()
            );
        }

        $quoteRequestSelection = $this->checkUpdatePayload($request, $quoteRequestSelectionId);
        $declinations = $request->request->get('declinations');

        foreach ($declinations as $declination) {
            try {
                $this->checkDeclinationData($declination);
            } catch (\InvalidArgumentException $e) {
                throw new BadRequestHttpException($e->getMessage());
            }
        }

        $addedDeclinations = [];
        foreach ($declinations as $declination) {
            $addedDeclinations[] = $this->quoteRequestService->addDeclination(
                $quoteRequestSelection,
                $declination['declinationId'],
                $declination['quantity']
            );
        }
        $this->quoteRequestService->updateDate($quoteRequestSelection);

        return new JsonResponse(['declinations' => $addedDeclinations], Response::HTTP_CREATED);
    }

    public function updateDeclinationsAction(Request $request, int $quoteRequestSelectionId = null): JsonResponse
    {
        if ($quoteRequestSelectionId === null) {
            $quoteRequestSelectionId = $this->quoteRequestService->getOrCreateActiveSelection(
                $this->getUser()->getId(),
                $this->getUser()->getRoles()
            );
        }

        $quoteRequestSelection = $this->checkUpdatePayload($request, $quoteRequestSelectionId);
        $declinations = $request->request->get('declinations');

        foreach ($declinations as $declination) {
            try {
                $this->checkDeclinationData($declination);
            } catch (\InvalidArgumentException $e) {
                throw new BadRequestHttpException($e->getMessage());
            }

            $this->checkDeclinationInSelection($quoteRequestSelectionId, $declination['declinationId']);
        }

        $updatedDeclinations = [];
        foreach ($declinations as $declination) {
            $updatedDeclinations[] = $this->quoteRequestService->updateDeclination(
                $quoteRequestSelection,
                $declination['declinationId'],
                $declination['quantity']
            );
        }
        $this->quoteRequestService->updateDate($quoteRequestSelection);

        return new JsonResponse(['declinations' => $updatedDeclinations], Response::HTTP_OK);
    }

    public function removeDeclinationsAction(Request $request, int $quoteRequestSelectionId = null): JsonResponse
    {
        if ($quoteRequestSelectionId === null) {
            $quoteRequestSelectionId = $this->quoteRequestService->getOrCreateActiveSelection(
                $this->getUser()->getId(),
                $this->getUser()->getRoles()
            );
        }

        $quoteRequestSelection = $this->checkUpdatePayload($request, $quoteRequestSelectionId);
        $declinations = $request->request->get('declinations');

        foreach ($declinations as $declination) {
            if (false === \is_array($declination)) {
                throw new \InvalidArgumentException('Invalid format');
            }

            try {
                (new OptionsResolver())
                    ->setRequired([
                        'declinationId'
                    ])
                    ->setAllowedTypes('declinationId', 'string')
                    ->resolve($declination);
            } catch (\InvalidArgumentException $e) {
                throw new BadRequestHttpException($e->getMessage());
            }

            $this->checkDeclinationInSelection($quoteRequestSelectionId, $declination['declinationId']);
        }

        foreach ($declinations as $declination) {
            $this->quoteRequestService->removeDeclination(
                $quoteRequestSelection,
                $declination['declinationId']
            );
        }
        $this->quoteRequestService->updateDate($quoteRequestSelection);

        return new JsonResponse($declinations, Response::HTTP_OK);
    }

    private function checkUpdatePayload(Request $request, ?int $quoteRequestSelectionId): QuoteRequestSelection
    {
        if (null === $quoteRequestSelectionId) {
            throw new NotFoundHttpException('Quote request selection not found');
        }

        /** @var ApiSecurityUser $user */
        $user = $this->getUser();

        if (false === $request->request->has('declinations')
            || \count($request->request->get('declinations')) === 0
        ) {
            throw new BadRequestHttpException('Invalid format');
        }

        $quoteRequestSelection = $this->quoteRequestService
            ->get($quoteRequestSelectionId, $user->getId(), $user->getRoles());
        if (null === $quoteRequestSelection) {
            throw new NotFoundHttpException('Quote request selection not found');
        }

        return $quoteRequestSelection;
    }

    private function checkDeclinationInSelection(int $quoteRequestSelectionId, string $declinationId): void
    {
        if (false === $this->quoteRequestService->isDeclinationInSelection($quoteRequestSelectionId, $declinationId)) {
            throw new NotFoundHttpException('Declination ' . $declinationId . ' not found in selection');
        }
    }

    /** @param mixed[] $declinations */
    private function checkDeclinationData(array $declination): void
    {
        if (false === \is_array($declination)) {
            throw new \InvalidArgumentException('declinations must be an array');
        }

        (new OptionsResolver())
            ->setRequired([
                'declinationId',
                'quantity'
            ])
            ->setAllowedTypes('declinationId', 'string')
            ->setAllowedTypes('quantity', 'int')
            ->setAllowedValues('quantity', fn ($value) => $value > 0)
            ->resolve($declination);

        $this->quoteRequestService->checkIfDeclinationCanBeAdded($declination['declinationId']);
    }
}
