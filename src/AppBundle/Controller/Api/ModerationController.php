<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Response;
use Wizacha\Marketplace\PIM\Moderation\ModerationService;
use Wizacha\Marketplace\PIM\Product\ProductService;
use Wizacha\Premoderation;
use Wizacha\Premoderation\ProductModeration\Exception\ProductAlreadyInModerationException;
use Wizacha\Premoderation\ProductModeration\Exception\ProductNotFoundException;

class ModerationController extends Controller
{
    /**
     * @var ProductService
     */
    private $productService;
    /**
     * @var ModerationService
     */
    private $moderationService;

    public function __construct(ProductService $productService, ModerationService $moderationService)
    {
        $this->productService = $productService;
        $this->moderationService = $moderationService;
    }

    public function updateProductAction(int $productId, string $moderationAction): Response
    {
        // Mise à jour de la modération
        try {
            $this->moderationService->prepareModerateProduct(
                [$productId],
                Premoderation::getStatusByAction($moderationAction)
            );
        } catch (ProductAlreadyInModerationException $e) {
            return new Response($e->getMessage(), Response::HTTP_CONFLICT);
        } catch (ProductNotFoundException $e) {
            return new Response($e->getMessage(), Response::HTTP_NOT_FOUND);
        }

        return new Response('', Response::HTTP_NO_CONTENT);
    }

    public function updatePendingProductsFromCompany(int $companyId, string $moderationAction): Response
    {
        try {
            $this->moderationService->setCompanyPendingProductsStatus(
                $companyId,
                Premoderation::getStatusByAction($moderationAction)
            );
        } catch (ProductNotFoundException $e) {
            return new Response($e->getMessage(), Response::HTTP_NOT_FOUND);
        }

        return new Response('', Response::HTTP_NO_CONTENT);
    }
}
