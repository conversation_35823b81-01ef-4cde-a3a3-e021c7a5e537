<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Broadway\ReadModel\RepositoryInterface as ReadModelRepository;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Router;
use Wizacha\AppBundle\Controller\Api\Error\ApiErrorException;
use Wizacha\Marketplace\Exception\ErrorCode;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\Exception\MarketplaceExceptionInterface;
use Wizacha\Marketplace\Order\Exception\OrderNotFound;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\Refund\Enum\OrderRefundStatus;
use Wizacha\Marketplace\Order\Refund\Exception\RefundNotFoundException;
use Wizacha\Marketplace\Order\Refund\Service\RefundNotificationService;
use Wizacha\Marketplace\Order\Refund\Service\RefundService;
use Wizacha\Marketplace\Order\Refund\Utils\RefundAmounts;
use Wizacha\Marketplace\Price\PriceFields;
use Wizacha\Marketplace\ReadModel\Basket;
use Wizacha\Money\Money;

class RefundController extends Controller
{
    /** @var Router */
    protected $router;

    /** @var LoggerInterface */
    protected $logger;

    /** @var RefundService */
    protected $refundService;

    /** @var RefundNotificationService */
    protected $refundNotificationService;

    /** @var OrderService */
    protected $orderService;

    /** @var readModelRepository */
    protected $readModelRepository;

    private CompanyService $companyService;

    public function __construct(
        Router $router,
        LoggerInterface $logger,
        RefundService $refundService,
        RefundNotificationService $refundNotificationService,
        OrderService $orderService,
        ReadModelRepository $readModelRepository,
        CompanyService $companyService
    ) {
        $this->router = $router;
        $this->logger = $logger;
        $this->refundService = $refundService;
        $this->orderService = $orderService;
        $this->refundNotificationService = $refundNotificationService;
        $this->readModelRepository = $readModelRepository;
        $this->companyService = $companyService;
    }

    public function getOrderRefundAction(int $orderId): Response
    {
        $order = $this->orderService->getOrder($orderId);

        $this->assertRefundPermission($order);

        $refunds = $this->refundService->getByOrderId($order->getId());

        foreach ($refunds as $refund) {
            $refundAmounts = new RefundAmounts($refund, $order);
            $refund->setRefundAmounts($refundAmounts);
        }

        return new JsonResponse($refunds);
    }

    public function getOrderRefundByIdAction(int $orderId, int $refundId): Response
    {
        $order = $this->orderService->getOrder($orderId);

        $this->assertRefundPermission($order);

        try {
            $refund = $this->refundService->getOneBy(['orderId' => $orderId, 'id' => $refundId]);
        } catch (RefundNotFoundException $exception) {
            throw new ApiErrorException($exception);
        }

        $refundAmounts = new RefundAmounts($refund, $order);
        $refund->setRefundAmounts($refundAmounts);

        return new JsonResponse($refund);
    }

    public function postOrderRefundAction(Request $request, int $orderId): Response
    {
        if (($this->refundService->getVendorRefundAbility() === false
            && $this->isGranted('ROLE_VENDOR') === true)
            && $this->isGranted('ROLE_ADMIN') === false
        ) {
            throw new Forbidden('You are not allowed to create refunds');
        }

        $order = $this->orderService->getOrder($orderId);

        if ($this->isGranted('ROLE_VENDOR') === true
            && $order->getCompanyId() !== $this->getUser()->getCompanyId()
        ) {
            throw new OrderNotFound($order->getId());
        }

        if ($order->getRefundStatus()->equals(OrderRefundStatus::COMPLETE_REFUND()) === true) {
            throw new Forbidden(__('order_already_refunded'));
        }

        if ($this->refundService->isManuallyRefundable($order, $this->isGranted('ROLE_ADMIN')) === false) {
            throw new Forbidden('You are not allowed to create refunds');
        }

        $creditNoteReference = $request->request->get('creditNoteReference', null);

        $hasError = false;

        if ($creditNoteReference !== null
            && $this->companyService->isInvoicingDisabled($order->getCompanyId()) === true
        ) {
            $hasError = true;
        }

        if ($request->request->has('doNotCreateCreditNote') === true
            && $request->request->get('doNotCreateCreditNote') === false
            && $this->companyService->isInvoicingDisabled($order->getCompanyId()) === true
        ) {
            $hasError = true;
        }

        if ($hasError === true) {
            return new JsonResponse([
                'error' => [
                    'code' => ErrorCode::INVALID_REFUND_REQUEST(),
                    'message' => __('refund.refund_cannot_be_made'),
                    'context' => []
                ],
            ], Response::HTTP_BAD_REQUEST);
        }

        if ($request->request->has('doNotCreateCreditNote') === true
            && $request->request->get('doNotCreateCreditNote') === true
        ) {
            $creditNoteReference = null;
        }

        $refund = null;

        try {
            $hasShipping = $request->request->get('shipping', ['hasShipping' => false]);

            if (true === \array_key_exists('hasShipping', $hasShipping)
                && true === $hasShipping['hasShipping']
            ) {
                $this->refundService->assertCanRefundShipping($order);
            }

            if ($request->request->get('isPartial', false) === false) {
                $refund = $this->refundService->createCompleteRefund($order, $request->request->get('message', null));
                if (\is_string($creditNoteReference) === true) {
                    $refund->setCreditNoteReference($creditNoteReference);
                }
            } else {
                $refund = $this->refundService->createPartialRefund(
                    $order,
                    $request->request->get('message'),
                    $request->request->get('items'),
                    $request->request->get('shipping', [])['hasShipping'] ?? true,
                    $creditNoteReference
                );
            }

            $refund = $this->refundService->executeRefund($refund, $order);
            $this->refundNotificationService->notifyRefund($refund, $order);
        } catch (MarketplaceExceptionInterface $e) {
            throw new ApiErrorException($e);
        } catch (\Exception $e) {
            // We want to return the created Refund with a FAILED status, not throw the exception.
            $this->logger->warning('API: error while executing refund', ['exception' => $e]);

            if ($refund === null) {
                return new JsonResponse($e, Response::HTTP_INTERNAL_SERVER_ERROR);
            }
        }

        $refundAmounts = new RefundAmounts($refund, $order);
        $refund->setRefundAmounts($refundAmounts);

        return new JsonResponse($refund, Response::HTTP_CREATED);
    }

    protected function assertRefundPermission(Order $order): bool
    {
        $route = $this->router->getContext()->getPathInfo();

        if (\preg_match("~user~", $route) > 0
            && ($this->isGranted('ROLE_ADMIN') || $this->isGranted('ROLE_VENDOR'))
        ) {
            throw new OrderNotFound($order->getId());
        }

        if ($this->isGranted('ROLE_VENDOR') === true
            && $order->getCompanyId() !== $this->getUser()->getCompanyId()
        ) {
            throw new OrderNotFound($order->getId());
        }

        return true;
    }
}
