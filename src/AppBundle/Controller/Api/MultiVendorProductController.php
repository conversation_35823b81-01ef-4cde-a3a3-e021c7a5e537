<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Wizacha\AppBundle\Validator\UrlValidatorInterface;
use Wizacha\AppBundle\Validator\VideoValidator;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\FeatureFlag\FeatureFlaggableInterface;
use Wizacha\Marketplace\Exception\AlreadyExists;
use Wizacha\Marketplace\Exception\IntegrityConstraintViolation;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Image\Exception\BadImage;
use Wizacha\Marketplace\PIM\Attribute\Exception\AttributeCannotHaveMultipleValues;
use Wizacha\Marketplace\PIM\MultiVendorProduct\Link;
use Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProductService;
use Wizacha\Marketplace\PIM\Video\VideoService;

class MultiVendorProductController extends Controller implements FeatureFlaggableInterface
{
    /**
     * @var MultiVendorProductService
     */
    private $multiVendorProductService;

    private UrlValidatorInterface $urlValidator;
    /**
     * @var VideoValidator
     */
    private $videoValidator;

    /** @var videoService */
    private $videoService;

    public function __construct(
        MultiVendorProductService $multiVendorProductService,
        UrlValidatorInterface $urlValidator,
        VideoValidator $videoValidator,
        videoService $videoService
    ) {
        $this->multiVendorProductService = $multiVendorProductService;
        $this->urlValidator = $urlValidator;
        $this->videoValidator = $videoValidator;
        $this->videoService = $videoService;
    }

    public function listAction(Request $request): JsonResponse
    {
        $page = $request->query->getInt('page', 1);
        $limit = $request->query->getInt('resultsPerPage', 50);

        $request->query->remove('page');
        $request->query->remove('resultsPerPage');

        $resolver = (new OptionsResolver())
            ->setDefaults([
                'id' => [],
                'code' => [],
                'supplierReference' => [],
                'categoryId' => [],
                'updatedAfter' => null,
                'updatedBefore' => null,
            ])
            ->setAllowedTypes('id', ['null', 'string[]'])
            ->setAllowedTypes('code', ['null', 'string[]'])
            ->setAllowedTypes('supplierReference', ['null', 'string[]'])
            ->setAllowedTypes('categoryId', ['null', 'string[]'])
            ->setAllowedTypes('updatedAfter', ['null', 'string'])
            ->setAllowedTypes('updatedBefore', ['null', 'string'])
            ;

        try {
            $filters = $resolver->resolve($request->query->all());
        } catch (\Exception $exception) {
            throw new BadRequestHttpException($exception->getMessage(), $exception->getPrevious());
        }
        if (\array_key_exists('categoryId', $filters)) {
            $filters['category'] = $filters['categoryId'];
            unset($filters['categoryId']);
        }
        try {
            $resources = $this->multiVendorProductService->list($page, $limit, array_filter($filters));
        } catch (\InvalidArgumentException $exception) {
            throw new BadRequestHttpException($exception->getMessage(), $exception->getPrevious());
        }

        $exposedResources = [];
        foreach ($resources->getIterator() as $resource) {
            $exposedResources[] = $resource->expose($this->multiVendorProductService);
        }

        $hal = [
            "total" => $resources->count(),
            "count" => \count($exposedResources),
            "_embedded" => [
                "multiVendorProducts" => $exposedResources,
            ],
        ];

        return new JsonResponse($hal);
    }

    public function readAction(string $id): JsonResponse
    {
        $resource = $this->multiVendorProductService->get($id);

        return new JsonResponse($resource->expose($this->multiVendorProductService));
    }

    public function createAction(Request $request): JsonResponse
    {
        // Récupération des données
        $data = $request->request->all();

        // Sauvegarde
        try {
            $resource = $this->multiVendorProductService->save($data);
        } catch (IntegrityConstraintViolation $e) {
            return new BadRequestJsonResponse($e->getMessage(), $e->getErrors());
        } catch (AttributeCannotHaveMultipleValues $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        // Retourne le bon code HTTP et le contenu de la ressource nouvellement créée
        return new JsonResponse($resource->expose($this->multiVendorProductService), Response::HTTP_CREATED);
    }

    public function updateAction(string $id, Request $request): JsonResponse
    {
        // Récupération des données
        $data = $request->request->all();

        // Vérification de la présence de la ressource
        $resource = $this->multiVendorProductService->get($id);

        // Sauvegarde
        try {
            $resource = $this->multiVendorProductService->save($data, $resource);
        } catch (IntegrityConstraintViolation $e) {
            return new BadRequestJsonResponse($e->getMessage(), $e->getErrors());
        } catch (AttributeCannotHaveMultipleValues $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        // Retourne le bon code HTTP et le contenu de la ressource nouvellement créée
        return new JsonResponse($resource->expose($this->multiVendorProductService));
    }

    /**
     * PATCH a multi-vendor product
     *
     * @param string $id
     * @param Request $request
     * @return JsonResponse
     * @throws \Wizacha\Marketplace\Exception\NotFound
     */
    public function patchAction(string $id, Request $request): JsonResponse
    {
        $data = $request->request->all();

        $resource = $this->multiVendorProductService->get($id);

        try {
            $resource = $this->multiVendorProductService->patch($data, $resource);
        } catch (IntegrityConstraintViolation $e) {
            return new BadRequestJsonResponse($e->getMessage(), $e->getErrors());
        } catch (AttributeCannotHaveMultipleValues $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        return new JsonResponse($resource->expose($this->multiVendorProductService));
    }

    public function deleteAction(string $id): Response
    {
        $this->multiVendorProductService->delete($id);

        return new Response('', Response::HTTP_NO_CONTENT);
    }

    public function attachProductAction(string $id, Request $request): JsonResponse
    {
        // Vérification de la présence de la ressource
        $multiVendorProduct = $this->multiVendorProductService->get($id);

        // Récupération des données
        $data = $request->request->all();

        if (!isset($data['productId'])) {
            return new BadRequestJsonResponse("You must specify a product id to attach it to a MVP");
        }

        try {
            $resource = $this->multiVendorProductService->attach($multiVendorProduct->getId(), (int) $data['productId']);
        } catch (IntegrityConstraintViolation $e) {
            return new BadRequestJsonResponse($e->getMessage(), $e->getErrors());
        } catch (AlreadyExists $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        // Retourne le bon code HTTP et le contenu de la ressource nouvellement créée
        return new JsonResponse($resource->expose(), Response::HTTP_CREATED);
    }

    public function detachProductAction(string $id, int $productId): Response
    {
        if ($this->multiVendorProductService->detach($id, $productId) !== true) {
            throw NotFound::fromId('Link', $productId . ' / ' . $id);
        }

        return new Response('', Response::HTTP_NO_CONTENT);
    }

    public function listLinksAction(string $id): JsonResponse
    {
        // Récupération du MVP
        $multiVendorProduct = $this->multiVendorProductService->get($id);

        $resources = $multiVendorProduct->getLinks()->getValues();

        $exposedResources = array_map(function (Link $resource) {
            return $resource->expose();
        }, $resources);

        return new JsonResponse($exposedResources);
    }

    public function addImageAction(string $id, Request $request): JsonResponse
    {
        if (!$file = $request->files->get('file')) {
            return BadRequestJsonResponse::missingFields('file');
        }

        $altText = $request->request->get('altText');
        if ($altText === null || \strlen($altText) > 255) {
            $altText = '';
        }

        try {
            $resource = $this->multiVendorProductService->addImage($id, $file, $altText);
        } catch (BadImage $e) {
            return BadRequestJsonResponse::invalidField('file', $e->getMessage());
        }

        // Retourne le bon code HTTP et le contenu de la ressource modifiée
        return new JsonResponse($resource->expose($this->multiVendorProductService));
    }

    public function deleteImageAction(string $id, int $imageId): Response
    {
        $this->multiVendorProductService->removeImage($id, $imageId);

        $this->get('doctrine.orm.entity_manager')->flush();

        return new Response('', 204);
    }

    public function addVideoAction(Request $request, string $id): JsonResponse
    {
        if (!$request->request->has('file') && !$request->files->get('file')) {
            return BadRequestJsonResponse::missingFields('file');
        }

        try {
            if ($request->request->get('file') && $this->urlValidator->isUrlValid($request->request->get('file'))) {
                $videoUrl = $request->request->get('file');
                $this->videoValidator->assertIsUrlValid($videoUrl);
            } else {
                $file = $request->files->get('file');
                $this->videoValidator->assertIsValid($file);
                $videoUrl = $file->getPathname();
            }
            $videoId = $this->multiVendorProductService->addVideo($id, $videoUrl);
            $video = $this->multiVendorProductService->getVideo($videoId);
        } catch (\Exception $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        return new JsonResponse($video->expose(), Response::HTTP_CREATED);
    }

    public function deleteVideoAction(string $id): Response
    {
        try {
            $this->multiVendorProductService->deleteVideo($id);
        } catch (\Exception $e) {
            return new BadRequestJsonResponse('Unable to delete video');
        }

        return new Response('', Response::HTTP_NO_CONTENT);
    }

    public function getFeatureFlag(): string
    {
        return 'feature.multi_vendor_product';
    }
}
