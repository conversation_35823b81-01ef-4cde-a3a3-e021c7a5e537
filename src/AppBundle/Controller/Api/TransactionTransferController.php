<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\OptionsResolver\Exception\ExceptionInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionType;

class TransactionTransferController extends AbstractController
{
    private TransactionService $transactionService;

    // Sets the number of items to display on a single page
    private const MAX_ITEM_PER_PAGE = 10;

    public function __construct(
        TransactionService $transactionService
    ) {
        $this->transactionService = $transactionService;
    }

    public function reportTransactionListAction(Request $request): JsonResponse
    {
        try {
            $data = (new OptionsResolver())
                ->setDefaults([
                    'page' => '1',
                    'resultPerPage' => (string) self::MAX_ITEM_PER_PAGE,
                    'period' => [],
                    'status' => null,
                    'type' => null,
                    'company' => null
                ])
                ->setAllowedTypes('page', 'string')
                ->setAllowedTypes('resultPerPage', 'string')
                ->setAllowedTypes('status', ['string[]', 'null'])
                ->setAllowedTypes('type', ['string[]', 'null'])
                ->setAllowedTypes('company', ['string[]' , 'null'])
                ->setAllowedTypes('period', ['string[]'])
                ->resolve($request->query->all());
        } catch (ExceptionInterface $exception) {
            return new BadRequestJsonResponse($exception->getMessage());
        }

        /**
         * The seller can only see their transactions
         * => Overwrite filter value
         */
        if ($this->isGranted('ROLE_VENDOR') === true) {
            $data['company'] = $this->getUser()->getId();
        }

        $filters = [];

        $filters['companies'] = $data['company'];
        $filters['status'] = $data['status'];
        $filters['type'] = $data['type'];
        if (\is_null($filters['type']) === true) {
            $filters['type'] = TransactionType::getTransactionFilter();
        }

        try {
            $filters['period_start'] = $this->getFilterPeriod($data, 'from');
            $filters['period_end'] = $this->getFilterPeriod($data, 'to');
        } catch (\InvalidArgumentException $exception) {
            return new BadRequestJsonResponse($exception->getMessage());
        }

        $page = (int) $data['page'];
        $resultPerPage = (int) $data['resultPerPage'];

        $transactions = $this->transactionService->findExternalTransferTransactions(
            $filters,
            $page,
            $resultPerPage
        );

        $exposedTransactions = [];
        foreach ($transactions->getIterator() as $i => $transaction) {
            $exposedTransactions[] = $transaction->exposeTransactionHistory((string) \DateTime::RFC3339);
        }

        $nbResults = \count($this->transactionService->findExternalTransferTransactions($filters));
        return new JsonResponse(
            [
                [
                    'results' => $exposedTransactions
                ],
                [
                    'page' => $page,
                    'nbResults' => $nbResults,
                    'nbPages' => \ceil($nbResults / $resultPerPage),
                    'resultsPerPage' => $resultPerPage,
                ]
            ]
        );
    }

    private function getDateFromParameters(string $dateValue, string $fieldName): \DateTime
    {
        $date = \DateTime::createFromFormat(\DateTime::RFC3339, $dateValue);
        if ($date instanceof \DateTime === false) {
            throw new \InvalidArgumentException(
                \sprintf(
                    'Invalid date format for parameter %s.',
                    $fieldName
                )
            );
        }

        return $date;
    }

    private function getFilterPeriod(array $data, string $fieldName): ?\DateTime
    {
        $period = $data['period'];

        if (\array_key_exists($fieldName, $period) === true) {
            return $this->getDateFromParameters($period[$fieldName], $fieldName);
        }

        return null;
    }
}
