<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\RelatedProduct;

use Doctrine\DBAL\Exception\UniqueConstraintViolationException;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\ConflictHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Wizacha\AppBundle\Security\User\ApiSecurityUser;
use Wizacha\Bridge\Symfony\Response\NotFoundJsonResponse;
use Wizacha\Marketplace\Exception\ProductNotFound;
use Wizacha\Marketplace\PIM\Product\ProductService;
use Wizacha\Marketplace\RelatedProduct\Exception\InvalidRelatedProductException;
use Wizacha\Marketplace\RelatedProduct\Exception\NotFoundRelatedProductException;
use Wizacha\Marketplace\RelatedProduct\RelatedProductService;

class RelatedProductController extends Controller
{
    private RelatedProductService $relatedProductService;

    private ProductService $productService;

    public function __construct(
        RelatedProductService $relatedProductService,
        ProductService $productService
    ) {
        $this->relatedProductService = $relatedProductService;
        $this->productService = $productService;
    }

    public function createRelatedProductAction(Request $request, int $productId): JsonResponse
    {
        try {
            $payload = (new OptionsResolver())
                ->setRequired([
                    'productId',
                    'type',
                ])
                ->setDefaults([
                    'description' => null,
                    'extra' => null,
                ])
                ->setAllowedTypes('productId', 'int')
                ->setAllowedTypes('type', 'string')
                ->setAllowedTypes('description', ['string', 'null'])
                ->setAllowedTypes('extra', ['string', 'null'])
                ->resolve($request->request->all())
            ;
        } catch (\InvalidArgumentException $e) {
            throw new BadRequestHttpException($e->getMessage());
        }

        try {
            $fromProduct = $this->productService->get($productId);
        } catch (ProductNotFound $e) {
            return new NotFoundJsonResponse($e->getMessage());
        }

        /** @var ApiSecurityUser $user */
        $user = $this->getUser();

        if ($fromProduct->getCompanyId() !== $user->getCompanyId()
            && \in_array('ROLE_ADMIN', $user->getRoles()) === false
        ) {
            $this->denyAccessUnlessGranted('ROLE_ADMIN');
        }

        try {
            $toProduct = $this->productService->get($payload['productId']);
        } catch (ProductNotFound $e) {
            return new NotFoundJsonResponse($e->getMessage());
        }

        $dto = new RelatedProductDto(
            $fromProduct,
            $toProduct,
            $payload['type'],
            $payload['description'],
            $payload['extra']
        );

        try {
            $relatedProduct = $this->relatedProductService->addRelatedProduct(
                $dto,
                (null != $this->getUser()) ? $this->getUser()->getId() : null
            );
        } catch (InvalidRelatedProductException $e) {
            throw new BadRequestHttpException($e->getMessage());
        } catch (UniqueConstraintViolationException $e) {
            throw new ConflictHttpException('The related product already exists.', $e);
        } catch (\Throwable $e) {
            throw new BadRequestHttpException($e->getMessage());
        }

        return new JsonResponse(
            $relatedProduct->expose()
        );
    }

    public function deleteRelatedProductAction(Request $request, int $productId): JsonResponse
    {
        try {
            $product = $this->productService->get($productId);
        } catch (ProductNotFound $e) {
            throw new BadRequestHttpException("Product '$productId' not found.");
        }

        /** @var ApiSecurityUser $user */
        $user = $this->getUser();

        if ($product->getCompanyId() !== $user->getCompanyId()
            && \in_array('ROLE_ADMIN', $user->getRoles()) === false
        ) {
            $this->denyAccessUnlessGranted('ROLE_ADMIN');
        }

        $queryParams = $request->query->all();

        $relatedProductId = 0;

        if (\array_key_exists('productId', $queryParams)) {
            $relatedProductId = $request->query->get('productId');

            try {
                $this->productService->get((int) $relatedProductId);
            } catch (ProductNotFound $e) {
                throw new BadRequestHttpException("Product '$relatedProductId' not found.");
            }
        }

        $type = $request->query->get('type');

        if (\array_key_exists('type', $queryParams)) {
            if ('' === $type
                || false === $this->relatedProductService->isValidType($type)
            ) {
                throw new BadRequestHttpException(
                    sprintf(
                        "The type '%s' is not available in the configuration. The available types are %s.",
                        $type,
                        $this->relatedProductService->getAvailableTypesToString()
                    )
                );
            }
        }

        try {
            $this->relatedProductService->deleteRelatedProduct(
                $productId,
                (int) $relatedProductId,
                $type
            );
        } catch (InvalidRelatedProductException $e) {
            throw new BadRequestHttpException($e->getMessage());
        } catch (NotFoundRelatedProductException $e) {
            throw new NotFoundHttpException($e->getMessage());
        } catch (\Throwable $e) {
            throw new BadRequestHttpException($e->getMessage());
        }

        return new JsonResponse(null, JsonResponse::HTTP_NO_CONTENT);
    }
}
