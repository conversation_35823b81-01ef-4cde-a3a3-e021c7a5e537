<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\RelatedProduct;

use Wizacha\Marketplace\PIM\Product\Product;

final class RelatedProductDto
{
    public Product $fromProduct;
    public Product $toProduct;
    public string $type;
    public ?string $description;
    public ?string $extra;

    public function __construct(
        Product $fromProduct,
        Product $toProduct,
        string $type,
        ?string $description,
        ?string $extra
    ) {
        $this->fromProduct = $fromProduct;
        $this->toProduct = $toProduct;
        $this->type = $type;
        $this->description = $description;
        $this->extra = $extra;
    }
}
