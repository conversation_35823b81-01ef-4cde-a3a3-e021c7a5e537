<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api;

use Tygh\Api\Response;

class Categories extends \Tygh\Api\Entities\Categories
{
    public function index($id = 0, $params = array())
    {
        /** @var $lang_code TYPE_NAME */
        $lang_code = $this->safeGet($params, 'lang_code', DEFAULT_LANGUAGE);
        if (!empty($params['category_ids'])) {
            if (\is_array($params['category_ids'])) {
                $params['item_ids'] = implode(',', $params['category_ids']);
            }
            unset($params['category_ids']);
        }
        if (!$id) {
            $return = parent::index($id, $params);
            $data = &$return['data'];
            array_walk(
                $data,
                function (&$value) {
                    $value = array_intersect_key(
                        $value,
                        array_flip(['category_id', 'parent_id','status', 'category'])
                    );
                }
            );
        } else {
            $c = new \Wizacha\Category($id);
            if ($c->isStored()) {
                $return['status'] = Response::STATUS_OK;
                $return['data'] = [
                    'category'      => $c->getName(),
                    'category_id'   => $c->getId(),
                    'status'        => $c->getStatus()->getValue(),
                    'parent_id'     => $c->getParentCategory()->getId(),
                    'ageLimit'     => $c->getAgeLimit(),
                    'is_fillable'   => $c->isApiFillable(),
                ];
                $return['data']['features'] = fn_get_product_features(['category_ids' => $return['data']['category_id'], 'statuses' => ['A']])[0];
                array_walk(
                    $return['data']['features'],
                    function (&$feature) {
                        $feature = array_intersect_key($feature, array_flip(['feature_id', 'description']));
                        $feature['variants'] = fn_get_product_feature_variants(['feature_id' => $feature['feature_id']])[0];
                        array_walk(
                            $feature['variants'],
                            function (&$variant) {
                                $variant = array_intersect_key($variant, array_flip(['variant_id', 'variant']));
                            }
                        );
                    }
                );
                $return['data']['declinations'] = fn_w_get_category_options($return['data']['category_id']);
            } else {
                $return = [
                    'status'    => Response::STATUS_INTERNAL_SERVER_ERROR,
                    'data'      => [],
                ];
            }
        }
        fn_w_fields_cast($return['data']);
        return array(
            'status' => $return['status'],
            'data' => $return['data']
        );
    }

    public function privileges()
    {
        return array(
            'index'  => true
        );
    }
}
