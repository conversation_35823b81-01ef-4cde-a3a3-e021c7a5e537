<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api;

use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ServerException;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Wizacha\AppBundle\Controller\Api\Exception\RateLimitException;
use Wizacha\AppBundle\Controller\StreamedResponseTrait;
use Wizacha\FeatureFlag\FeatureFlagService;
use Wizacha\ImageManager;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\Exception\NotFound;

class ImageController extends Controller
{
    use StreamedResponseTrait;

    private ImageManager $imageManager;
    private FeatureFlagService $featureFlagService;
    private SessionInterface $session;

    public function __construct(
        ImageManager $imageManager,
        FeatureFlagService $featureFlagService,
        SessionInterface $session
    ) {
        $this->imageManager = $imageManager;
        $this->featureFlagService = $featureFlagService;
        $this->session = $session;
    }

    /**
     * Returns an image.
     *
     * Optional query parameters: w and h for width and height.
     *
     * If width is given and height is null, the image will be resized to the given width, keeping its ratio
     * If height is given and width is null, the image will be resized to the given height, keeping its ratio
     * If width and height are given, the ratio is kept and a white background is added
     *
     * ! This specific endpoint bypass the legacy stack in init.php, see fn.init.php fn_init_stack_bypass()
     */
    public function getAction(int $id, Request $request): Response
    {
        // Resize feature check
        $isImageResizeAllowed = $this->featureFlagService->get('feature.enable_image_resizing');

        $width = $request->query->getInt('w') ?: null;
        $height = $request->query->getInt('h') ?: null;

        // resize need to be allowed in backend
        $userId = $this->session->get('auth')['user_id'];

        if ((null !== $width || null !== $height)
            && (0 === $userId || null === $userId)
            && false === \filter_var($isImageResizeAllowed, FILTER_VALIDATE_BOOLEAN)
        ) {
            throw new Forbidden(
                __('image_resizing_forbidden')
            );
        }

        try {
            $url = $this->imageManager->getInternalUrl(
                $id,
                $width,
                $height,
                $request instanceof Request ? $request->getScheme() : 'https'
            );
        } catch (NotFound $e) {
            return new Response('Image not found', Response::HTTP_NOT_FOUND);
        }

        $headers = array_map(
            function ($headerKey) use ($request) {
                return $request->headers->get($headerKey);
            },
            [
                'accept-language',
                'accept-encoding',
                'accept',
                'user-agent',
                'x-request-id'
            ]
        );

        try {
            return $this->createStreamedResponseFromUrl($url, $headers);
        } catch (ClientException $exception) {
            return new Response(
                'Image not found',
                Response::HTTP_NOT_FOUND
            );
        } catch (ServerException $exception) {
            if ($exception->getCode() ===  Response::HTTP_SERVICE_UNAVAILABLE) {
                throw new RateLimitException($exception->getMessage(), 0, $exception);
            }

            throw $exception;
        }
    }
}
