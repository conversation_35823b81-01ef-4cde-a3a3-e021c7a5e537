<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Serializer\SerializerInterface;
use Wizacha\Component\AuthLog\AuthLog;
use Wizacha\Component\AuthLog\AuthLogRepository;
use Wizacha\Component\AuthLog\AuthLogService;
use Wizacha\Component\AuthLog\SortByType;

class AuthLogController
{
    /** @var AuthLogService */
    private $authLogService;

    /** @var serializer */
    private $serializer;

    public function __construct(AuthLogService $authLogService, SerializerInterface $serializer)
    {
        $this->authLogService = $authLogService;
        $this->serializer = $serializer;
    }

    public function getAction(Request $request, int $id): JsonResponse
    {
        return new JsonResponse(
            $this->authLogService->get($id)->expose()
        );
    }

    public function listAction(Request $request, int $page, string $format): Response
    {
        try {
            $options = $this->resolve($request, $page);
        } catch (ExceptionInterface $e) {
            throw new BadRequestHttpException($e->getMessage(), $e);
        }

        [$authLogs, $total] = $this->authLogService->search($options);

        $exposedAuthLogs = array_map(
            function (AuthLog $authLog): array {
                return $authLog->expose();
            },
            $authLogs
        );

        switch ($format) {
            case 'json':
                return new JsonResponse([
                    'total' => (int) $total,
                    'count' => (int) \count($authLogs),
                    'start' => (int) $options['start'],
                    'limit' => (int) $options['limit'],
                    'authLogs' => $exposedAuthLogs,
                ]);
            case 'csv':
                $response = new Response(
                    $this->serializer->serialize($exposedAuthLogs, 'csv')
                );
                $response->headers->set('Content-Type', 'text/csv; charset=utf-8', 'application/force-download');

                return $response;
        }
    }

    public function resolve(Request $request, int $page): array
    {
        $resolver = new OptionsResolver();
        $this->configureOptions($resolver);

        // page number to start offset
        $request->query->set(
            'start',
            ($page - 1) * ($request->query->get('limit') ?: AuthLogRepository::LIMIT)
        );

        // convert numeric string to proper ints
        $options = $resolver->resolve(
            $this->normalizeIntegers(
                $request->query->all(),
                ['start', 'limit']
            )
        );

        $periodResolver = new OptionsResolver();
        $periodResolver->setDefaults([
            'from' => null,
            'to' => null,
        ]);

        $allowedDateTime = function ($value) {
            return \is_null($value) ? true : \DateTime::createFromFormat(\DateTime::RFC3339, $value);
        };
        $periodResolver->setAllowedValues('from', $allowedDateTime);
        $periodResolver->setAllowedValues('to', $allowedDateTime);

        $period = $periodResolver->resolve($options['period']);

        return array_merge(
            $options,
            ['period' => $period]
        );
    }

    private function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'start' => AuthLogRepository::START,
            'limit' => AuthLogRepository::LIMIT,
            'login' => null,
            'status' => null,
            'source' => null,
            'destination' => null,
            'period' => [],
            'sort_by' => SortByType::ID()->getValue(),
            'sort_order' => 'desc',
        ]);

        $resolver
            ->setAllowedTypes('start', 'int')
            ->setAllowedTypes('limit', 'int')
            ->setAllowedTypes('login', ['string[]', 'string', 'null'])
            ->setAllowedTypes('status', ['string[]', 'string', 'null'])
            ->setAllowedTypes('source', ['string[]', 'string', 'null'])
            ->setAllowedTypes('destination', ['string[]', 'string', 'null'])
            ->setAllowedTypes('period', ['string[]', 'null'])
            ->setAllowedValues('sort_by', SortByType::toArray())
            ->setAllowedValues('sort_order', ['asc', 'desc']);
    }

    private function normalizeIntegers(array $parameters, array $keys): array
    {
        foreach ($keys as $key) {
            if (isset($parameters[$key])) {
                $value = $parameters[$key];
                $parameters[$key] = is_numeric($value) ? (int) $value : $value;
            }
        }

        return $parameters;
    }
}
