<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Tygh\Storage;
use Wizacha\AppBundle\Controller\Api\Exception\MissingFieldsHttpException;
use Wizacha\AppBundle\Security\User\ApiSecurityUser;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Bridge\Symfony\Response\NotFoundJsonResponse;
use Wizacha\Bridge\Symfony\Response\UnauthorizedJsonResponse;
use Wizacha\Discuss\DiscussService;
use Wizacha\Discuss\Entity\Discussion;
use Wizacha\Discuss\Entity\DiscussionInterface;
use Wizacha\Discuss\Entity\Message;
use Wizacha\Discuss\Entity\MessageInterface;
use Wizacha\Marketplace\Entities\Declination;
use Wizacha\Marketplace\Exception\DiscussionNotFound;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Exception\Unauthorized;
use Wizacha\Marketplace\MessageAttachment\MessageAttachmentService;
use Wizacha\Marketplace\Order\OrderAttachment\Exception\DownloadFailException;
use Wizacha\Marketplace\User\Exception\InvalidFieldException;
use Wizacha\Purifier\Purifier;
use Wizacha\Storage\StorageService;
use Wizacha\User;

class DiscussionController extends Controller
{
    private MessageAttachmentService $attachmentService;
    private Purifier $purifierService;
    private DiscussService $discussService;
    private StorageService $messageAttachmentsStorageService;

    public function __construct(
        MessageAttachmentService $attachmentService,
        Purifier $purifier,
        StorageService $messageAttachmentsStorageService,
        DiscussService $discussService
    ) {
        $this->attachmentService = $attachmentService;
        $this->purifierService = $purifier;
        $this->messageAttachmentsStorageService = $messageAttachmentsStorageService;
        $this->discussService = $discussService;
    }

    public function listAction()
    {
        $user = $this->getUser();
        $client = $this->get('app.discuss_service')->getDiscussClient();
        if ($user->getCompanyId() !== 0) {
            $discussions = $client->getDiscussionRepository()->getByCompanyIdOrUserId((int) $user->getCompanyId(), $user->getId());
        } else {
            $discussions = $client->getDiscussionRepository()->getByUser($user->getId());
        }

        return new JsonResponse(array_map(function (Discussion $discussion) {
            return $this->serializeDiscussion($discussion);
        }, iterator_to_array($discussions)));
    }

    public function getAction(int $id)
    {
        $user = $this->getUser();
        $client = $this->get('app.discuss_service')->getDiscussClient();

        $discussion = $client->getDiscussionRepository()->get($id);

        $this->assertVendorAccessDiscussion($discussion, $user);

        if ($discussion !== null
            && ((int) $discussion->getMetaData('company_id') === $user->getCompanyId()
            || \in_array($user->getId(), $discussion->getUsers()) === true
            || \in_array('ROLE_ADMIN', $user->getRoles()) === true)
        ) {
            return new JsonResponse($this->serializeDiscussion($discussion));
        }

        throw new DiscussionNotFound($id);
    }

    public function createAction(Request $request)
    {
        $params = $request->request->all();

        // Parameters combination authorized are : One parameter of (productId, declinationId, companyId, userId), (userId, orderId) or (companyId, orderId).
        if (\count($params) > 2 || (\count($params) > 1 && \array_key_exists('orderId', $params) === false)) {
            return new BadRequestJsonResponse('Only one parameter of (productId, declinationId, companyId, userId), (userId, orderId) or (companyId, orderId) is authorized.');
        }

        if (\count($params) === 0) {
            return BadRequestJsonResponse::missingField('productId, declinationId, companyId or userId');
        }

        if (\array_key_exists('orderId', $params) === true
            && \array_key_exists('companyId', $params) === false
            && \array_key_exists('userId', $params) === false
        ) {
            return new BadRequestJsonResponse('OrderId must be associated with a companyId or a userId.');
        }

        $user = $this->getUser();

        try {
            $discussion = $this->discussService->createDiscussionFilteredByParams($params, $user);
        } catch (NotFound $e) {
            return new NotFoundJsonResponse($e->getMessage());
        } catch (\InvalidArgumentException $e) {
            return new BadRequestJsonResponse($e->getMessage());
        } catch (Unauthorized $e) {
            return new UnauthorizedJsonResponse($e->getMessage());
        } catch (MissingFieldsHttpException $e) {
            return BadRequestJsonResponse::missingField((string) $e->fields);
        } catch (InvalidFieldException $e) {
            return BadRequestJsonResponse::invalidField($e->getInvalidField(), $e->getMessage());
        }

        $this->discussService->getDiscussClient()->getDiscussionRepository()->save($discussion);

        return new JsonResponse($this->serializeDiscussion($discussion), Response::HTTP_CREATED);
    }

    public function getMessagesAction(int $id, Request $request)
    {
        $user = $this->getUser();
        $client = $this->get('app.discuss_service')->getDiscussClient();

        $discussion = $client->getDiscussionRepository()->get($id);

        $markMessagesAsRead = (new OptionsResolver())
            ->setDefined('markMessagesAsRead')
            ->setAllowedTypes('markMessagesAsRead', 'string')
            ->setDefault('markMessagesAsRead', 'true')
            ->setNormalizer('markMessagesAsRead', function ($request, $value): bool {
                return $value !== 'false';
            })
            ->resolve($request->query->all());

        $markMessagesAsRead = $markMessagesAsRead['markMessagesAsRead'];

        $this->assertVendorAccessDiscussion($discussion, $user);

        $canGetMessages = false;
        if ($discussion !== null
            && ((int) $discussion->getMetaData('company_id') === $user->getCompanyId()
            || \in_array($user->getId(), $discussion->getUsers()) === true
            || \in_array('ROLE_ADMIN', $user->getRoles()) === true)
        ) {
            $canGetMessages = true;
        }

        if ($canGetMessages === false) {
            throw new DiscussionNotFound($id);
        }

        $messages = $client->getMessageRepository()->getByDiscussion($discussion->getId());

        /** @var MessageInterface $message */
        foreach ($messages as $message) {
            if ($message->getAuthor() != $user->getId() && $markMessagesAsRead === true) {
                $message->setAsRead();
                $client->getMessageRepository()->save($message);
            }
        }

        return new JsonResponse(array_map(function (Message $message) {
            return $this->serializeMessage($message);
        }, iterator_to_array($messages)));
    }

    public function postMessageAction(int $id, Request $request)
    {
        $user = $this->getUser();
        $client = $this->get('app.discuss_service')->getDiscussClient();

        $discussion = $client->getDiscussionRepository()->get($id);

        $this->assertVendorAccessDiscussion($discussion, $user);

        $canPostMessage = false;
        if ($discussion !== null
            && ((int) $discussion->getMetaData('company_id') === $user->getCompanyId()
            || \in_array($user->getId(), $discussion->getUsers()) === true)
        ) {
            $canPostMessage = true;
        }

        if ($canPostMessage === false) {
            throw new DiscussionNotFound($id);
        }

        $content = $request->request->get('content');
        if (null === $content) {
            return BadRequestJsonResponse::missingField('content');
        }

        $attachments = $request->files->get('attachments');
        if (\is_array($attachments) === true) {
            if ($this->attachmentService->isValidateFilesExtension($attachments) === false) {
                return BadRequestJsonResponse::invalidField('attachments', 'Invalid file format.');
            }
            if ($this->attachmentService->isFilesSizeValid($attachments) === false) {
                return BadRequestJsonResponse::invalidField(
                    'attachments',
                    "The file can't be uploaded because it exceeds the maximum size."
                );
            }
        }

        $message = $client->getMessageRepository()->create();
        $message->setSendDate(new \DateTime());
        $message->setDiscussion($discussion);
        $message->setAuthor($user->getId());
        $message->setContent(nl2br(\trim($this->purifierService->purify($content))));
        $client->getMessageRepository()->save($message);

        if (\is_array($attachments) === true) {
            $this->attachmentService->create($attachments, $message->getId());
        }

        return new JsonResponse($this->serializeMessage($message), Response::HTTP_CREATED);
    }

    private function serializeDiscussion(DiscussionInterface $discussion): array
    {
        $user = $this->getUser();
        $client = $this->get('app.discuss_service')->getDiscussClient();

        $recipientId = $discussion->getRecipient();
        $recipient = new User($recipientId);

        return [
            'id' => $discussion->getId(),
            'recipient' => !empty($recipient->getPseudo()) ? $recipient->getPseudo() : $recipient->getFullname(),
            'recipientId' => $recipientId,
            'productId' =>  $discussion->getMetaData('product_id') ? (int) $discussion->getMetaData('product_id') : null,
            'companyId' => (int) $discussion->getMetaData('company_id'),
            'orderId' => $discussion->getMetaData('order_id'),
            'title' => $discussion->getMetaData('title'),
            'unreadCount' => $client->getMessageRepository()->getUnreadCount(
                $user->getId(),
                $discussion->getId(),
                $user->getCompanyId()
            ),
        ];
    }

    private function serializeMessage(MessageInterface $message): array
    {
        $authorId = $message->getAuthor();
        $author = new User($authorId);

        return [
            'content' => $message->getContent(),
            'author' => !empty($author->getPseudo()) ? $author->getPseudo() : $author->getFullname(),
            'authorId' => $authorId,
            'date' => $message->getSendDate()->format(\DateTime::RFC3339),
            'attachments' => $this->attachmentService->getAttachmentsByMessage($message->getId()),
            'isCompany' => (int) $message->getDiscussion()->getMetaData('company_id') ===  $author->getCompanyId()
        ];
    }

    public function downloadMessageAttachmentAction(string $attachmentId): Response
    {
        $user = $this->getUser();
        $client = $this->get('app.discuss_service')->getDiscussClient();

        $messageAttachment = $this->attachmentService->get($attachmentId);

        $discussion = $client->getMessageRepository()->get($messageAttachment->getMessageId())->getDiscussion();

        $canGetFile = (
            $discussion !== null
            && (
                (int) $discussion->getMetaData('company_id') === $user->getCompanyId()
                || \in_array($user->getId(), $discussion->getUsers()) === true
            )
        );

        if ($canGetFile === false) {
            throw new Forbidden("User is not authorized to download File");
        }

        try {
            return $this->messageAttachmentsStorageService
                ->get(
                    $messageAttachment->getMessageId() . '/'
                    . $messageAttachment->getId() . '/'
                    . $messageAttachment->getName()
                )
                ;
        } catch (FileException $exception) {
            throw new DownloadFailException();
        }
    }

    private function assertVendorAccessDiscussion(?Discussion $discussion, ApiSecurityUser $user): void
    {
        //for vendors if $discussion->getMetaData('company_id') have value, it must be equal to $user->getCompanyId()
        if ($discussion !== null
            && \is_null($discussion->getMetaData('company_id')) === false
            && $user->getCompanyId() !== 0
            && (int) $discussion->getMetaData('company_id') !== $user->getCompanyId()
            && ($discussion->getRecipient() === $user->getId()
            || \is_null($discussion->getMetaData('customer_id')) === false)
        ) {
            throw new DiscussionNotFound($discussion->getId());
        }
    }
}
