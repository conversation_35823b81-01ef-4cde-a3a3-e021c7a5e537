<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;
use Twig\Environment;
use Twig\Loader\ArrayLoader;
use Wizacha\AppBundle\Constant\ResponseContentType;
use Wizacha\Component\PdfGenerator\PdfGenerator;
use Wizacha\Component\PdfGenerator\PdfOptions;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Order\CreditNote\CreditNoteHelper;
use Wizacha\Marketplace\Order\CreditNote\CreditNoteService;
use Wizacha\Marketplace\Order\Exception\OrderNotFound;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\Refund\Exception\RefundNotFoundException;
use Wizacha\Marketplace\Order\Refund\Service\RefundService;

class CreditNoteController extends Controller
{
    /** @var RouterInterface */
    protected $router;

    /** @var LoggerInterface */
    protected $logger;

    /** @var RefundService */
    protected $refundService;

    /** @var OrderService */
    protected $orderService;

    /** @var CreditNoteService */
    protected $creditNoteService;

    /** @var CreditNoteHelper */
    protected $creditNoteHelper;

    /** @var PdfGenerator */
    protected $pdfGenerator;

    /** @var string */
    protected $currency;

    public function __construct(
        RouterInterface $router,
        RefundService $refundService,
        OrderService $orderService,
        CreditNoteService $creditNoteService,
        CreditNoteHelper $creditNoteHelper,
        PdfGenerator $pdfGenerator
    ) {
        $this->router = $router;
        $this->refundService = $refundService;
        $this->orderService = $orderService;
        $this->creditNoteService = $creditNoteService;
        $this->pdfGenerator = $pdfGenerator;
        $this->creditNoteHelper = $creditNoteHelper;
    }

    public function getCreditNoteAction(int $orderId, int $refundId): Response
    {
        $order = $this->orderService->getOrder($orderId);

        $this->assertCreditNotePermission($order);

        try {
            $refund = $this->refundService->get($refundId);
        } catch (RefundNotFoundException $e) {
            throw new NotFoundHttpException($e->getMessage(), $e);
        }

        $creditNote = $this->creditNoteService->buildCreditNote($refund, $order);

        $content = null;
        $twig = $this->creditNoteHelper->retrieveRemoteTemplate($this->getParameter('marketplace.rma.template_url'));
        if (\is_string($twig) && \strlen($twig) > 0) {
            $twigSandbox = new Environment(new ArrayLoader());
            $twigTemplate = $twigSandbox->createTemplate($twig);

            $content = $twigTemplate->render(['creditNote' => $creditNote]);
        }

        if ($content === null || \strlen($content) === 0) {
            $content = $this->renderView('@App/common/order/credit-note.html.twig', [
                'creditNote' => $creditNote,
                'currency' => $this->currency,
            ]);
        }

        $header = $this->creditNoteHelper->retrieveRemoteTemplate(
            $this->getParameter('marketplace.rma.header.template_url')
        );
        $footer = $this->creditNoteHelper->retrieveRemoteTemplate(
            $this->getParameter('marketplace.rma.footer.template_url')
        );
        $pdfOptions = [
            PdfOptions::ENCODING()->getValue() => 'UTF-8',
            PdfOptions::HEADER_HTML()->getValue() => $header,
            PdfOptions::FOOTER_HTML()->getValue() => $footer,
            PdfOptions::MARGIN_BOTTOM()->getValue() => $this->getParameter('marketplace.rma.options.margin.top'),
            PdfOptions::MARGIN_BOTTOM()->getValue() => $this->getParameter('marketplace.rma.options.margin.bottom'),
        ];

        $pdfContent = $this->pdfGenerator->convertHtmlToPdf($content, $pdfOptions);

        return new Response($pdfContent, Response::HTTP_OK, [
            'Content-Disposition' => 'attachment; filename="' . $this->creditNoteHelper->buildCreditNoteFilename(
                $order->getId(),
                $refund->getId()
            ) . '"',
            ResponseContentType::CONTENT_TYPE()->getValue() => ResponseContentType::PDF()->getValue(),
        ]);
    }

    public function getOrderCreditNotesAction(int $orderId): Response
    {
        $order = $this->orderService->getOrder($orderId);

        $this->assertCreditNotePermission($order);

        $creditNotes = [];
        $refunds = $this->refundService->getByOrderId($order->getId());

        foreach ($refunds as $refund) {
            $creditNotes[] = [
                'refundId' => $refund->getId(),
                'filename' => $this->creditNoteHelper->buildCreditNoteFilename($order->getId(), $refund->getId()),
                'url' => $this->router->generate(
                    'api_orders_get_credit_note',
                    ['orderId' => $order->getId(), 'refundId' => $refund->getId()],
                    UrlGeneratorInterface::ABSOLUTE_URL
                ),
            ];
        }

        return new JsonResponse($creditNotes);
    }

    protected function assertCreditNotePermission(Order $order): bool
    {
        $route = $this->router->getContext()->getPathInfo();

        if (\preg_match("~user~", $route) > 0
            && ($this->isGranted('ROLE_ADMIN') || $this->isGranted('ROLE_VENDOR'))
        ) {
            throw new OrderNotFound($order->getId());
        }

        if ($this->isGranted('ROLE_VENDOR') === true
            && $order->getCompanyId() !== $this->getUser()->getCompanyId()
        ) {
            throw new OrderNotFound($order->getId());
        }

        if ($this->creditNoteService->isAvailable($order) === false) {
            throw new NotFoundHttpException('No credit note available for this order');
        }

        return true;
    }

    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;

        return $this;
    }
}
