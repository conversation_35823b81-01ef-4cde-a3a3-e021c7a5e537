<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Wizacha\Marketplace\Cms\Banner;

class BannerController extends Controller
{
    public function getAction(Request $request)
    {
        $device = $request->query->get('device');
        $includeMobile = $device === null || $device === 'mobile';
        $includeDesktop = $device === null || $device === 'desktop';
        $banners = $this->get('marketplace.banner.banner_service')->getHomepageActiveBanners($includeMobile, $includeDesktop);

        $response = new JsonResponse(array_map([$this, 'serialize'], $banners));

        if ($this->getParameter('feature.cache_http')) {
            $response->setVary('Accept-Language');
            $response->setSharedMaxAge($this->getParameter('cache_api_banners_get'));
        }

        return $response;
    }

    public function getCategoryAction(Request $request, int $id)
    {
        $device = $request->query->get('device');
        $includeMobile = $device === null || $device === 'mobile';
        $includeDesktop = $device === null || $device === 'desktop';
        $banners = $this->get('marketplace.banner.banner_service')->getCategoriesActiveBanners($id, $includeMobile, $includeDesktop);

        $response = new JsonResponse(array_map([$this, 'serialize'], $banners));

        if ($this->getParameter('feature.cache_http')) {
            $response->setVary('Accept-Language');
            $response->setSharedMaxAge($this->getParameter('cache_api_banners_get_category'));
        }

        return $response;
    }

    private function serialize(Banner $banner): array
    {
        return [
            'name' => $banner->getName(),
            'link' => $banner->getLink(),
            'shouldOpenInNewWindow' => $banner->shouldOpenInNewWindow(),
            'image' => $banner->getImage() ? [
                'id' => $banner->getImage()->getId(),
                'alt' => $banner->getAltImage(),
            ] : null,
        ];
    }
}
