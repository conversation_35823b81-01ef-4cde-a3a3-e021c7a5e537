<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Wizacha\Bridge\Symfony\Response\AccessDeniedJsonResponse;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Bridge\Symfony\Response\UnauthorizedJsonResponse;
use Wizacha\Marketplace\Exception\IntegrityConstraintViolation;
use Wizacha\Marketplace\Exception\Unauthorized;
use Wizacha\Marketplace\Review\CompanyReviewService;
use Wizacha\Marketplace\Review\Review;

class ReviewController extends Controller
{
    /**
     * @var CompanyReviewService
     */
    private $reviewService;

    public function __construct(CompanyReviewService $reviewService)
    {
        $this->reviewService = $reviewService;
    }

    public function createFromCompanyAction(int $companyId, Request $request): JsonResponse
    {
        // Récupération des données
        $data = $this->getJsonContent($request);

        // Récupération du user courant
        if (!$user = $this->getUser()) {
            return new AccessDeniedJsonResponse();
        }

        // Vérification de la présence des paramètres obligatoires
        foreach (['rating', 'message'] as $field) {
            if (!isset($data[$field])) {
                return new BadRequestJsonResponse("Invalid payload : {$field} is missing.");
            }
        }

        // Sauvegarde
        try {
            $resource = $this->reviewService->reviewCompany($user->getId(), $companyId, $data['rating'], $data['message']);
        } catch (IntegrityConstraintViolation $e) {
            return new BadRequestJsonResponse($e->getMessage(), $e->getErrors());
        } catch (Unauthorized $e) {
            return new AccessDeniedJsonResponse($e->getMessage());
        }

        // Retourne le bon code HTTP et le contenu de la ressource nouvellement créée
        return new JsonResponse($resource->expose(), Response::HTTP_CREATED);
    }

    public function listFromCompanyAction(int $companyId, Request $request): JsonResponse
    {
        $resources = $this->reviewService->listCompanyReviews($companyId);

        $exposedResources = array_map(function (Review $resource) {
            return $resource->expose();
        }, $resources);

        $content = [
            'averageRating' => $this->reviewService->getAverageRating($companyId),
            '_embedded' => $exposedResources,
        ];

        return new JsonResponse($content, Response::HTTP_OK);
    }

    public function getUserAuthorizationToReviewCompany(int $companyId): Response
    {
        // Récupération du user courant
        if (!$user = $this->getUser()) {
            return new UnauthorizedJsonResponse();
        }

        $userService = $this->container->get('marketplace.user_service');
        $userId = $userService->getCurrentUserId();

        try {
            $this->reviewService->assertUserIsAuthorizedToReviewCompany($userId, $companyId);
        } catch (Unauthorized $exception) {
            return new UnauthorizedJsonResponse();
        }

        return new Response('', Response::HTTP_NO_CONTENT);
    }

    protected function getJsonContent(Request $request): array
    {
        $data = json_decode($request->getContent(), true);
        if (!\is_array($data)) {
            throw new BadRequestHttpException('Content has to be valid JSON');
        }

        return $data;
    }
}
