<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;

class StatisticsController extends Controller
{
    /**
     * Cette route retourne des statistiques qui visent à être utilisées dans notre dashboard interne.
     */
    public function getAction(): JsonResponse
    {
        $db = $this->get('database_connection');

        $userCount = (int) $db->fetchColumn('SELECT COUNT(user_id) FROM cscart_users');
        $userCountToday = (int) $db->fetchColumn('SELECT COUNT(user_id) FROM cscart_users WHERE DATE(FROM_UNIXTIME(timestamp)) = CURDATE()');

        $orderCount = (int) $db->fetchColumn('SELECT COUNT(order_id) FROM cscart_orders WHERE is_parent_order="N" AND status != "N"');
        $orderCountToday = (int) $db->fetchColumn('SELECT COUNT(order_id) FROM cscart_orders WHERE is_parent_order="N" AND status != "N" AND DATE(FROM_UNIXTIME(timestamp)) = CURDATE()');

        $companyCount = (int) $db->fetchColumn('SELECT COUNT(company_id) FROM cscart_companies');
        $companyCountToday = (int) $db->fetchColumn('SELECT COUNT(company_id) FROM cscart_companies WHERE DATE(FROM_UNIXTIME(timestamp)) = CURDATE()');

        return new JsonResponse([
            'userCount' => $userCount,
            'userCountToday' => $userCountToday,
            'orderCount' => $orderCount,
            'orderCountToday' => $orderCountToday,
            'companyCount' => $companyCount,
            'companyCountToday' => $companyCountToday,
        ]);
    }
}
