<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api;

use Tygh\Api\Response;
use Tygh\Database;
use Tygh\Registry;
use Wizacha\Category;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\PIM\Product\Attachment;
use Wizacha\Marketplace\PIM\Product\Template\Template;
use Wizacha\Marketplace\PIM\Product\Template\TemplateService;
use Wizacha\Marketplace\PIM\Video\Video;
use Wizacha\Marketplace\PriceTier\Exception\PriceTierException;
use Wizacha\Marketplace\PriceTier\PriceTier;
use Wizacha\Marketplace\Quotation\QuoteRequestService;
use Wizacha\Marketplace\RelatedProduct\RelatedProduct;
use Wizacha\Marketplace\RelatedProduct\RelatedProductService;
use Wizacha\Marketplace\Subscription\SubscriptionService;
use Wizacha\Marketplace\User\UserType;
use Wizacha\Status;

class Products extends \Tygh\Api\Entities\Products
{
    // Chunk size for CSV import
    public const CHUNK_SIZE = 1000;
    private const PRODUCT_CODE = "product_code";
    private const PRODUCT_TEMPLATE_TYPE = "product_template_type";

    public function index($id = 0, $params = array())
    {
        $params = fn_w_object_filter($params, 'params', 'api', 'filter_params');

        if (empty($params['items_per_page'])) {
            $params['items_per_page'] = 100;
        }

        $params['get_options'] = true;

        // Get short AND full description
        $params['extend'][] = 'short_and_full_description';

        $return = parent::index($id, $params);

        if ($return['status'] === 400 || $return['status'] === 404) {
            return $return;
        }

        if ($id) {
            $return['data']['inventory'] = fn_w_get_product_inventory($id);

            $data = [&$return['data']];
        } else {
            $data = &$return['data']['products'];
        }

        array_walk(
            $data,
            function (array &$_data) use ($id) {
                if ($_data['tracking'] == 'B') {
                    $_data['inventory'][] = [
                        'combination' => [],
                        'amount' => $_data['amount'],
                        'w_price' => $_data['price'],
                        'crossed_out_price' => $_data['crossed_out_price'],
                    ];
                } else {
                    $_data['inventory'] = fn_w_get_product_inventory($_data['product_id']);
                }

                $_data = fn_w_object_filter($_data, 'product', 'api', 'display_objects');
                if ($_data['product_features']) {
                    $_data['product_features'] = self::prepareFeatureGet($_data);
                }

                fn_w_fields_cast($_data);

                // Transform the tax_ids in array
                if (\array_key_exists('tax_ids', $_data) && \is_string($_data['tax_ids'])) {
                    $_data['tax_ids'] = !empty($_data['tax_ids']) ? explode(',', $_data['tax_ids']) : [];
                }

                $_data = fn_w_convert_fields($_data, 'product', 'send', 'convert_fields_api');
                if ($_data['product_id']) {
                    $product = new \Wizacha\Product($_data['product_id']);

                    // Récupération des free attributes
                    $_data[\Wizacha\Product::FIELD_FREE_FEATURES] = $product->getFreeFeatures();

                    // Récupération de la géolocalisation
                    list($geoloc['latitude'], $geoloc['longitude'], $geoloc['label'], $geoloc['zipcode']) = $product->getGeoloc();
                    $_data[\Wizacha\Product::FIELD_GEOLOCATION] = $geoloc;

                    // On ajoute les divisions
                    $_data['divisions'] = container()->get('marketplace.division.products.service')->getAllDivisionsCode($_data['product_id']);

                    // We check first if the 'inventory' key has already been created. If not, it's not meant to be.
                    // If the declination is the single product combination (= {productId}_0) , its pricetiers are the product price tiers
                    if (\is_array($_data['inventory'])
                        && (\is_null($id) || \count($_data['inventory'][0]['combination'] ?? []) === 0)
                    ) {
                        $priceTiers = container()->get('marketplace.price_tier.price_tier_repository')->findByProductIdWithoutInventory($_data['product_id']);

                        foreach ($priceTiers as &$priceTier) {
                            $_data['inventory'][0]['priceTiers'][] = $priceTier instanceof PriceTier ? $priceTier->expose(true) : null;
                        }
                    }
                }

                if (\array_key_exists('video', $_data) && $_data['video'] instanceof Video) {
                    $_data['video'] = $_data['video']->getLinks()['path'];
                } else {
                    $_data['video'] = null;
                }

                $_data['attachments'] = array_map(
                    function (Attachment $attachment) {
                        return $attachment->expose();
                    },
                    container()->get('marketplace.pim.product.service')->getAttachments((int) $_data['product_id'])
                );

                // map Seo Information
                $isAdmin = $this->auth['user_type'] === UserType::ADMIN()->getValue();
                $this->mapDataIfAdmin($_data, 'seo_name', 'slug', $isAdmin)
                    ->mapDataIfAdmin($_data, 'page_title', 'seoTitle', $isAdmin)
                    ->mapDataIfAdmin($_data, 'meta_description', 'seoDescription', $isAdmin)
                    ->mapDataIfAdmin($_data, 'meta_keywords', 'seoKeywords', $isAdmin);

                $_data['related'] = array_map(
                    function (RelatedProduct $relatedProduct) {
                        return $relatedProduct->expose();
                    },
                    container()
                        ->get(RelatedProductService::class)
                        ->findByFromProductId((int) $_data['product_id'])
                );

                if (true === container()->getParameter('feature.quote_requests')) {
                    $_data['quote_requests_min_quantity'] = $product->getQuoteRequestMinQuantity();
                    $_data['is_exclusive_to_quote_requests'] = $product->isQuoteExclusive();
                }
            }
        );

        if (isset($return['data']['params'])) {
            $return['data']['params'] = fn_w_object_filter($return['data']['params'], 'params', 'api', 'filter_params');
        }

        return $return;
    }

    public function create($params)
    {
        self::prepareAmountAndPrice($params);
        $params = fn_w_convert_fields($params, 'product', 'get', 'convert_fields_api');
        $params = fn_w_object_filter($params, 'product', 'api');
        $params['discussion_type'] = 'B';

        try {
            $companyId = $this->getCompanyId($params);
        } catch (\InvalidArgumentException $exception) {
            return [
                'status' => Response::STATUS_BAD_REQUEST,
                'data' => ['message' => $exception->getMessage()],
            ];
        }

        // Check type of product_code : must be a string
        if (\is_string($params[static::PRODUCT_CODE]) === false) {
            return [
                'status' => Response::STATUS_BAD_REQUEST,
                'data' => ['message' => 'product_code should  be string']
            ];
        }

        //  short_description, supplier_ref & product_template_type cannot be null
        $fields = array( 'short_description', 'w_supplier_ref', static::PRODUCT_TEMPLATE_TYPE);
        foreach ($fields as $field) {
            if (\array_key_exists($field, $params) === true && \is_null($params[$field]) === true) {
                return [
                    'status' => Response::STATUS_BAD_REQUEST,
                    'data' => ['message' => ltrim($field, 'w_') . ' should not be null']
                ];
            }
        }

        //Premoderation
        $company_data = Registry::get('runtime.company_data');
        $products_prior_approval = Registry::get('addons.vendor_data_premoderation.products_prior_approval');
        if ($products_prior_approval == 'all'
            || $products_prior_approval == 'custom'
            && $company_data['pre_moderation'] == 'Y'
        ) {
            $isEnableAutoValidateProduct = container()->getParameter('feature.config_enable_auto_validate_product');
            $approved = ($this->auth['user_type'] === UserType::ADMIN || $isEnableAutoValidateProduct === true) ? 'Y' : 'P';
            $params['approved'] = $approved;
        }

        // Check product code to prevent HTTP 500 on create
        if (\array_key_exists(static::PRODUCT_CODE, $params)) {
            $existingProduct = $this->checkExistingProduct($params[static::PRODUCT_CODE], $companyId);
            if (\is_int($existingProduct)) {
                return [
                    'status' => Response::STATUS_CONFLICT,
                    'data' => ['message' => "Product with same product_code already exists (product_id = $existingProduct)"]
                ];
            }
        }

        $main_category = \Wizacha\Product::getMainCategoryFromData($params);
        if (\is_null($main_category)) {
            fn_set_notification('E', __('error'), __('category_is_empty'));
            return [
                'status' => Response::STATUS_BAD_REQUEST,
                'data' => ['message' => 'Category missing'],
            ];
        } elseif (!$main_category || !(new Category($main_category))->isApiFillable()) {
            fn_set_notification('E', __('error'), __('w_api_invalid_category'));
            return [
                'status' => Response::STATUS_BAD_REQUEST,
                'data' => ['message' => 'Invalid category'],
            ];
        }

        // Vérification de la geolocalisation
        $geolocation = $params[\Wizacha\Product::FIELD_GEOLOCATION] ?? [];
        if (!empty($geolocation)) {
            if (!isset($geolocation['latitude']) || !isset($geolocation['longitude'])) {
                fn_set_notification('E', __('error'), __('w_api_invalid_geolocation'));
                return [
                    'status' => Response::STATUS_BAD_REQUEST,
                    'data' => ['message' => 'Invalid geolocation : latitude and longitude are mandatory']
                ];
            }
        }

        $this->checkSupplierRefInMvpRules($params);

        if (\array_key_exists(static::PRODUCT_TEMPLATE_TYPE, $params) === false) {
            $params[static::PRODUCT_TEMPLATE_TYPE] = "";
        }

        if (\array_key_exists('infinite_stock', $params) && \is_null($params['infinite_stock'])) {
            unset($params['infinite_stock']);
        } elseif (\array_key_exists('infinite_stock', $params) && $params['infinite_stock'] === true) {
            $params['infinite_stock'] = 'Y';
        }

        /** @var TemplateService */
        $templateService = container()->get(TemplateService::class);
        $template = $templateService->getTemplate($params[static::PRODUCT_TEMPLATE_TYPE]);
        $environmentTemplates = $templateService->getTemplates();

        if (\is_null($template)) {
            if ($params[static::PRODUCT_TEMPLATE_TYPE] === "") {
                //Set the environment's default template to the product
                $params[static::PRODUCT_TEMPLATE_TYPE] = array_keys($environmentTemplates)[0];
            } else {
                container()->get('logger')->error('exim_product_not_match_template', ['%' => $params[static::PRODUCT_TEMPLATE_TYPE]]);

                return [
                    'status' => Response::STATUS_BAD_REQUEST,
                    'data' => ['message' => __('exim_product_not_match_template', ['%' => $params[static::PRODUCT_TEMPLATE_TYPE]])]
                ];
            }
        } else {
            // Check product fields regarding templates configuration
            $invalidHiddenFields = $templateService->getInvalidHiddenFields($template, $params);
            if (0 < \count($invalidHiddenFields)) {
                container()->get('logger')->error(
                    'exim_field_value_not_match_template',
                    [
                        'invalidHiddenFields' => $invalidHiddenFields
                    ]
                );

                return [
                    'status' => Response::STATUS_BAD_REQUEST,
                    'data' => [
                        'message' => __('exim_field_value_not_match_template'),
                        'invalidHiddenFields' => $invalidHiddenFields
                    ]
                ];
            }
        }

        if (\array_key_exists('max_price_adjustment', $params)) {
            $maxPriceAdjustment = $params['max_price_adjustment'];

            if ((\is_int($maxPriceAdjustment) && $maxPriceAdjustment >= 0 && $maxPriceAdjustment <= 100)
                || $maxPriceAdjustment === null
            ) {
                // value is valid
            } else {
                return [
                    'status' => Response::STATUS_BAD_REQUEST,
                    'data' => ['message' => "'max_price_adjustment' must be an integer between 0 and 100 (or null)"],
                ];
            }
        }

         // Set the default template values to the product
        foreach ($templateService->getTemplate($params['product_template_type'])->getHiddenFields() as $importField) {
            $value = $importField->getValue(Template::class);

            if ($importField->getName(Template::class) === 'infinite_stock') {
                $params[$importField->getName(Template::class)] = 'Y' === $value;
            } else {
                $params[$importField->getName(Template::class)] = $value;
            }
        }

        // Set value for infinite_stock
        if (\array_key_exists('infinite_stock', $params) && \is_bool($params['infinite_stock']) === false) {
            switch ($params['infinite_stock']) {
                case 'Y':
                    $params['infinite_stock'] = true;
                    break;
                case 'N':
                    $params['infinite_stock'] = false;
                    break;
            }
        }

        try {
            $this->assertProductParamsAreValid($params, true);
        } catch (\InvalidArgumentException $e) {
            return [
                'status' => Response::STATUS_BAD_REQUEST,
                'data' => ['message' => $e->getMessage(), json_encode($params)],
            ];
        }

        $params = $this->removeDivisionsFromProductData($params);
        // map Seo Information from Product Data
        $isAdmin = $this->auth['user_type'] === UserType::ADMIN()->getValue();
        $this->mapDataIfAdmin($params, 'slug', 'seo_name', $isAdmin)
            ->mapDataIfAdmin($params, 'seoTitle', 'page_title', $isAdmin)
            ->mapDataIfAdmin($params, 'seoDescription', 'meta_description', $isAdmin)
            ->mapDataIfAdmin($params, 'seoKeywords', 'meta_keywords', $isAdmin);

        $return = parent::create($params);
        $messageError = [];
        if ($return['status'] == Response::STATUS_CREATED) {
            $id = $return['data']['product_id'];
            if (!empty($params['product_features'])) {
                self::prepareFeature($params);
                fn_update_product_features_value($id, $params['product_features'], [], (string) GlobalState::contentLocale());
            }

            try {
                static::updateOptionsAndInventory($id, $params);
            } catch (\Throwable $exception) {
                return [
                    'status' => Response::STATUS_BAD_REQUEST,
                    'data' => ['message' => $exception->getMessage()],
                ];
            }

            try {
                $urlValidator = \Wizacha\Registry::defaultInstance()->container->get('app.validator.url_validator');
                if (!empty($params['video']) && $urlValidator->isUrlValid($params['video'])) {
                    container()->get('marketplace.pim.video_service')->startUploadFromUrl($id, $params['video']);
                }
            } catch (\Exception $e) {
                container()->get('logger')->error($e->getMessage(), [
                    'data' => $params,
                    'exception' => $e,
                ]);
                $messageError[] = __('video_not_found');
            }

            try {
                if (isset($params['attachments'])) {
                    container()->get('marketplace.pim.product.service')->updateAttachmentsFromUrl((int) $id, $params['attachments']);
                }
            } catch (\Exception $e) {
                container()->get('logger')->error($e->getMessage(), [
                    'data' => $params,
                    'exception' => $e,
                ]);
                $messageError[] = __('url_attachement_not_found');
            }
        }

        fn_w_fields_cast($return['data']);
        if (\count($messageError) > 0) {
            return [
                'status' => Response::STATUS_CREATED,
                'data' => [
                    'product_id' => $id,
                    'message' => join('. ', $messageError),
                ],
            ];
        }

        if ($messages = $this->checkDeprecatedFields($params)) {
            $return['data']['message'] = join('. ', $messages);
        }

        return $return;
    }

    private function handleAPIImage(array $image_infos)
    {
        if (!isset($image_infos['image_data'])
            || !isset($image_infos['image_name'])
            || !isset($image_infos['image_type'])
        ) {
            return false;
        }

        $allowed_extensions = array_flip(['jpeg', 'jpg', 'gif', 'png']);

        $allowed_types = array_flip(['image/jpeg', 'image/gif', 'image/png']);

        $filename = basename($image_infos['image_name']);
        $ext = strtolower(fn_get_file_ext($filename));

        if (!isset($allowed_extensions[$ext])
            || !isset($allowed_types[$image_infos['image_type']])
        ) {
            return false;
        }

        $tmp = fn_create_temp_file();
        if (!$tmp) {
            return false;
        }

        if (!fn_put_contents($tmp, base64_decode($image_infos['image_data']))) {
            return false;
        }

        //Is it really a valid image ?
        if (!getimagesize($tmp)) {
            return false;
        }

        //Is mime type correct ?
        $content_type = strtolower(fn_get_mime_content_type($tmp));
        if (!isset($allowed_types[$content_type])
            || $content_type !== $image_infos['image_type']
        ) {
            return false;
        }

        $targetDir = Registry::get('config.dir.files') . 'api_uploads/';
        if (!is_dir($targetDir)) {
            if (!mkdir($targetDir, 0777, true)) {
                return false;
            }
            if (!chmod($targetDir, 0777)) {
                return false;
            }
        }

        $full_path = $targetDir . self::getRandString(8) . '_' . $filename;
        if (!rename($tmp, $full_path)) {
            return false;
        }

        // will purge files after request
        \Wizacha\Misc::addFileToDeleteInCleanup($full_path, \Wizacha\Registry::defaultInstance());

        return $full_path;
    }

    public function getRandString($length)
    {
        return substr(str_shuffle('abcdefghijklmnopqrstuvwxyz0123456789'), 0, $length);
    }

    public function prepareImages(&$params, $product_id = 0)
    {
        // Main Image
        if (isset($params['main_pair'])) {
            if (isset($params['main_pair']['detailed']['image_data'])) {
                $path = self::handleAPIImage($params['main_pair']['detailed']);
                if (!$path) {
                    unset($params['main_pair']);
                } else {
                    $_REQUEST['file_product_main_image_detailed'][] = $path;
                    $_REQUEST['type_product_main_image_detailed'][] = 'server';
                }
            }

            // Check the old one
            if ($product_id != 0) {
                $old_main_image = fn_get_image_pairs($product_id, 'product', 'M', true, true, DEFAULT_LANGUAGE);
                if (!empty($old_main_image)) {
                    if ($old_main_image['detailed']['image_path'] === $params['main_pair']['detailed']['image_path']) {
                        // Same image path so we do nothing
                        unset($params['main_pair']);
                    } else {
                        // We delete the old one before add the new one
                        fn_delete_image_pair($old_main_image['pair_id']);
                    }
                }
            }
        }

        // Image Pairs
        if (\is_array($params['image_pairs'])) {
            foreach ($params['image_pairs'] as $pair_id => $pair) {
                if (isset($pair['detailed']['image_data'])) {
                    $path = self::handleAPIImage($pair['detailed']);
                    if (!$path) {
                        unset($params['image_pairs'][$pair_id]);
                    } else {
                        $_REQUEST['file_product_add_additional_image_detailed'][] = $path;
                        $_REQUEST['type_product_add_additional_image_detailed'][] = 'server';
                    }
                }
            }

            // Get old images pairs
            $old_additional_images = [];
            if ($product_id != 0) {
                $old_additional_images = fn_get_image_pairs($product_id, 'product', 'A', true, true, DEFAULT_LANGUAGE);
            }

            // We search if this image path already exist in the old images pairs
            foreach ($params['image_pairs'] as $pair_id => $pair) {
                foreach ($old_additional_images as $old_pair_id => $old_pair) {
                    if ($old_pair['detailed']['image_path'] === $pair['detailed']['image_path']) {
                        // Found, so this image won't be deleted
                        unset($old_additional_images[$old_pair_id]);
                        // And won't be added
                        unset($params['image_pairs'][$pair_id]);
                    }
                }
            }

            // We delete the old images pairs that we don't use anymore
            foreach ($old_additional_images as $pair) {
                fn_delete_image_pair($pair['pair_id']);
            }
        }

        parent::prepareImages($params, $product_id);
    }

    public function update($id, $params)
    {
        if (!fn_company_products_check($id)) {
            return ['status' => Response::STATUS_NOT_FOUND];
        }

        $messageError = [];

        $main_category = \Wizacha\Product::getMainCategoryFromData($params);
        if (!\is_null($main_category)) {
            if (!$main_category || !(new Category($main_category))->isApiFillable()) {
                fn_set_notification('E', __('error'), __('w_api_invalid_category'));
                return [
                    'status' => Response::STATUS_BAD_REQUEST,
                    'data'   => ['message' => 'Invalid category']
                ];
            }
        }

        $this->checkSupplierRefInMvpRules($params);

        self::prepareAmountAndPrice($params);
        $params = fn_w_convert_fields($params, 'product', 'get', 'convert_fields_api');
        $params = fn_w_object_filter($params, 'product', 'api');

        // Check product code to prevent HTTP 500 on update
        if (\array_key_exists(static::PRODUCT_CODE, $params)) {
            $existingProduct = $this->checkExistingProduct($params[static::PRODUCT_CODE]);
            if (\is_int($existingProduct) && (int) $id !== $existingProduct) {
                return [
                    'status' => Response::STATUS_CONFLICT,
                    'data' => ['message' => "Product with same product_code already exists (product_id = $existingProduct)"]
                ];
            }
        }

        if (\array_key_exists('video', $params) === true) {
            $videoService = container()->get('marketplace.pim.video_service');
            $urlValidator = container()->get('app.validator.url_validator');

            if (\mb_strlen($params['video']) === 0) {
                $existingVideo = $videoService->findOneByProductId($id);
                if ($existingVideo !== null) {
                    $videoService->delete($existingVideo->getId());
                }
            } elseif ($urlValidator->isUrlValid($params['video']) === true) {
                try {
                    $params['video'] = $videoService->startUploadFromUrl($id, $params['video']);
                } catch (\Exception $e) {
                    unset($params['video']);
                    $messageError[] = __('video_not_found');

                    container()->get('logger')->error($e->getMessage(), [
                        'data' => $params,
                        'exception' => $e,
                    ]);
                }
            } else {
                unset($params['video']);
                $messageError[] = __('text_invalid_url');
            }
        }

        $templateService = container()->get(TemplateService::class);
        $oldTemplate = $templateService
            ->getTemplate(container()
                ->get('marketplace.pim.product.service')
                ->get($id)
                ->getProductTemplateType());

        $template = $this->assignTemplateToProduct($params, $oldTemplate);

        if ($template->getId() !== $oldTemplate->getId()) {
            $params = array_merge($params, $templateService->resetProductOldTemplateHiddenFields($oldTemplate, $params));
        }

        if (\is_null($template)) {
            container()->get('logger')->error('exim_product_not_match_template', ['%' => $params[static::PRODUCT_TEMPLATE_TYPE]]);

            return [
                'status' => Response::STATUS_BAD_REQUEST,
                'data' => ['message' => __('exim_product_not_match_template', ['%' => $params[static::PRODUCT_TEMPLATE_TYPE]])]
            ];
        } else {
            if ($templateService->validateHiddenFields($template, $params) === false) {
                container()->get('logger')->error('exim_field_value_not_match_template');

                return [
                    'status' => Response::STATUS_BAD_REQUEST,
                    'data' => ['message' => __('exim_field_value_not_match_template')]
                ];
            }

            foreach ($templateService->getTemplate($template->getId())->getHiddenFields() as $importField) {
                $params[$importField->getName(Template::class)] = $importField->getValue(Template::class);
            }

            if (\array_key_exists('infinite_stock', $params) === true) {
                $params['infinite_stock'] = 'Y' === $params['infinite_stock'];
            }

            $params[static::PRODUCT_TEMPLATE_TYPE] = $template->getId();
        }

        try {
            $this->assertProductParamsAreValid($params);
        } catch (\InvalidArgumentException $e) {
            return [
                'status' => Response::STATUS_BAD_REQUEST,
                'data' => ['message' => $e->getMessage()],
            ];
        }

        if (\array_key_exists('max_price_adjustment', $params)) {
            $maxPriceAdjustment = $params['max_price_adjustment'];

            if ((\is_int($maxPriceAdjustment) && $maxPriceAdjustment >= 0 && $maxPriceAdjustment <= 100)
                || $maxPriceAdjustment === null
            ) {
                // value is valid
            } else {
                return [
                    'status' => Response::STATUS_BAD_REQUEST,
                    'data' => ['message' => "'max_price_adjustment' must be an integer between 0 and 100 (or null)"],
                ];
            }
        }

        $params = $this->removeDivisionsFromProductData($params);
        // map Seo Information from Product Data
        $isAdmin = $this->auth['user_type'] === UserType::ADMIN()->getValue();
        $this->mapDataIfAdmin($params, 'slug', 'seo_name', $isAdmin)
            ->mapDataIfAdmin($params, 'seoTitle', 'page_title', $isAdmin)
            ->mapDataIfAdmin($params, 'seoDescription', 'meta_description', $isAdmin)
            ->mapDataIfAdmin($params, 'seoKeywords', 'meta_keywords', $isAdmin);

        try {
            $params['updateProductByApi'] = true;
            $return = parent::update($id, $params);
        } catch (\InvalidArgumentException $exception) {
            return [
                'status' => Response::STATUS_BAD_REQUEST,
                'data' => ['message' => __($exception->getMessage())],
            ];
        }

        try {
            static::updateOptionsAndInventory($id, $params);
        } catch (\Throwable $exception) {
            return [
                'status' => Response::STATUS_BAD_REQUEST,
                'data' => ['message' => $exception->getMessage()],
            ];
        }


        if ($return['status'] == Response::STATUS_OK
            && $messages = $this->checkDeprecatedFields($params)
        ) {
            $messageError[] = \array_merge($messageError, $messages);
        }

        if (\count($messageError) > 0) {
            $return['data']['message'] = join('. ', $messageError);
        }

        $isEnableAutoValidateProduct = container()->getParameter('feature.config_enable_auto_validate_product');
        if ($isEnableAutoValidateProduct === true) {
            $moderationService = container()->get('marketplace.moderation.moderation_service');
            $moderationService->setProductsStatus(
                [$id],
                \Wizacha\Premoderation::STATUS_APPROVED
            );
        }

        if (0 === $params['quote_requests_min_quantity']) {
            container()->get(QuoteRequestService::class)->removeDeclinationFromSelections($id);
        }

        return $return;
    }

    private function assignTemplateToProduct(array $params, Template $oldTemplate): Template
    {
        $templateService = container()->get(TemplateService::class);

        if ($params[static::PRODUCT_TEMPLATE_TYPE] === '' || \is_null($params[static::PRODUCT_TEMPLATE_TYPE])) {
            return $templateService->getTemplate($oldTemplate->getId());
        }

        return $templateService->getTemplate($params[static::PRODUCT_TEMPLATE_TYPE]);
    }

    public function prepareAmountAndPrice(&$params)
    {
        if (\is_array($params['inventory'])) {
            array_walk(
                $params['inventory'],
                function ($_data) use (&$params) {
                    if (empty($_data['combination'])) {
                        $params['amount'] = $_data['amount'];
                        $params['price'] = $_data['price'];
                    }
                }
            );
        }
    }
    public function prepareFeature(&$params)
    {
        // Inspired by Api\Products::preparaFeature()
        if (!empty($params['product_features'])) {
            $features = $params['product_features'];
            $params['product_features'] = array();

            foreach ($features as $feature_id => &$feature) {
                if (!empty($feature['feature_type'])) {
                    if (strpos('CSNE', $feature['feature_type']) !== false) {
                        $params['product_features'][$feature_id] = fn_add_feature_variant(
                            $feature_id,
                            ['variant' => $feature['variants']]
                        );
                    } elseif (strpos('OD', $feature['feature_type']) !== false) {
                        $params['product_features'][$feature_id] = $feature['variants'];
                    } elseif (strpos('M', $feature['feature_type']) !== false) {
                        foreach ($feature['variants'] as $variant) {
                            $params['product_features'][$feature_id][] = fn_add_feature_variant(
                                $feature_id,
                                ['variant' => $variant]
                            );
                        }
                    } else { // T
                        $params['product_features'][$feature_id] = $feature['variants'];
                    }
                } else {
                    $params['product_features'][$feature_id] = $feature;
                }
            }
        }
    }

    public function prepareFeatureGet($params)
    {
        if (!empty($params['product_features'])) {
            return array_map(
                function ($feature) {
                    $return = [
                        'feature_id' => $feature['feature_id'],
                        'feature_type' => $feature['feature_type'],
                        'description' => $feature['description'],
                    ];
                    if (strpos('SNE', $feature['feature_type']) !== false) {
                        $return['variants'] = $feature['variants'][$feature['variant_id']]['variant'];
                    } elseif (strpos('OD', $feature['feature_type']) !== false) {
                        $return['variants'] = $feature['value_int'];
                    } elseif (strpos('M', $feature['feature_type']) !== false) {
                        foreach ($feature['variants'] as $variant) {
                            $return['variants'][] = $variant['variant'];
                        }
                    } else { // CT
                        $return['variants'] = $feature['value'];
                    }
                    return $return;
                },
                $params['product_features']
            );
        }
    }

    public function updateOptionsAndInventory($id, $params)
    {
        if (isset($params['allowed_options_variants'])) {
            self::prepareVariants($params['allowed_options_variants']);
            fn_w_update_product_options_authorization($id, (array) $params['allowed_options_variants']);
        }

        $inventory = (\is_array($params['inventory']) && \count($params['inventory']) > 0) ? $params['inventory'] : [];

        foreach ($inventory as &$product) {
            // Format $product['combination']
            $combination = [];
            $product['combination'] = $product['combination'] ?: [];
            ksort($product['combination']);
            foreach ($product['combination'] as $option => $variant) {
                $combination[] = $option . "_" . $variant;
            }
            $product['combination'] = implode('_', $combination);

            //Update Options inventory
            db_query(
                'UPDATE ?:product_options_inventory
                SET amount=?s, w_price=?s, product_code=?s, crossed_out_price=?s, affiliate_link=?s, infinite_stock=?d, supplier_reference=?s
                WHERE product_id=?i AND combination=?s',
                $product['amount'],
                $product['w_price'],
                $product[static::PRODUCT_CODE],
                $product['crossed_out_price'],
                $product['affiliate_link'],
                (isset($product['infinite_stock']) && $product['infinite_stock']) ? 1 : 0,
                $product['supplier_reference'],
                $id,
                $product['combination']
            );
        }

        fn_w_set_product_tracking($id);

        $priceTierService = container()->get('marketplace.price_tier.price_tier_service');

        if (\is_array($params['allowed_options_variants'])) {
            $params['allowed_options_variants'] = container()
                ->get('marketplace.price_tier.price_tier_service')
                ->filterAllowedOptions($id, $params['allowed_options_variants']);

            if (\count($params['allowed_options_variants']) > 0) {
                ksort($params['allowed_options_variants']);
                if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                    $inventory = $priceTierService->createEmptyMissingCombinations($params['allowed_options_variants'], $inventory);
                } elseif ($_SERVER['REQUEST_METHOD'] === 'PUT') {
                    $inventory = $priceTierService->createExistingCombinations($params['allowed_options_variants'], $inventory, container()->get('marketplace.pim.product.service')->get($id));
                }
            }
        }

        if (\array_key_exists('price', $params) && (\count($inventory) === 0 || $this->isEmptyCombinationInInventory($inventory) === false)) {
            $inventory[] = [
                'combination' => '',
                'w_price' => $params['price'],
            ];
        }

        /* @var PriceTierService $priceTierService */
        $errors = $priceTierService->savePriceTiers($id, $inventory);

        if (\is_array($errors) === true && \count($errors) > 0) {
            throw new PriceTierException($errors[0]->getMessage());
        }
    }

    protected function isEmptyCombinationInInventory(array $inventory): bool
    {
        foreach ($inventory as $singleItem) {
            if (\array_key_exists('combination', $singleItem) === false || \strlen($singleItem['combination']) === 0) {
                return true;
            }
        }

        return false;
    }

    public function prepareVariants(&$exception)
    {
        if (empty($exception)) {
            return;
        }

        $exception = array_reduce(
            $exception,
            function ($r, $option) {
                $r[$option['option_id']] = array_reduce(
                    $option['variants'],
                    function ($r, $variant) {
                        $r[$variant] = 1;
                        return $r;
                    }
                );
                return $r;
            }
        );
    }

    /**
     * @param array $p_lst_products list of product codes to store in temporary table in database
     * @param integer $company_id
     * @param string $p_table_name temporary table name
     * @return string temporary table name (set if parameter is not present, or identical value is returned)
     */
    protected function storeProductsIdsInTempTable(array $p_lst_products, $p_company_id, $p_table_name = '')
    {
        if (empty($p_table_name)) {
            $p_table_name = uniqid('wiz_tmp_products_');
            $query = 'CREATE TEMPORARY TABLE ?p ( product_id BIGINT UNSIGNED, INDEX (product_id));';
            Database::query($query, $p_table_name);
        }

        // We need to get the products ids from the table :products
        // to store them in the temp table
        $query = 'INSERT INTO ?p
                    SELECT product_id FROM ?:products WHERE product_code IN (?a)
                    AND company_id = ?i';

        Database::query($query, $p_table_name, $p_lst_products, $p_company_id);

        return $p_table_name;
    }

    /**
     * @param string $p_table_name - name of temporary table to read products ids from
     * @param integer $company_id
     * @param string $p_status - status to update products to
     * @return null|\PdoStatementIterator Iterator on updated products ids
     */
    protected function updateProductsStatusFromTempTable($p_table_name, $p_company_id, $p_status)
    {
        $ret = null;
        $data = ['status' => $p_status];

        // Update status of products not in CSV File - their id is not in temp table
        $query = 'UPDATE ?:products SET ?u
                        WHERE company_id = ?i
                            AND product_id NOT IN (
                                SELECT product_id FROM ?p
                                );';
        // If AFFECTED_ROWS > 0, we need to return an iterator on the products ids to throw an EVENT_UPDATE
        if (Database::query($query, $data, $p_company_id, $p_table_name)->rowCount() > 0) {
            $query = 'SELECT product_id FROM ?:products
                        WHERE company_id = ?i
                            AND product_id NOT IN (
                                SELECT product_id FROM ?p
                                );';
            $ret = new \Wizacha\Core\Iterator\PdoColumnIterator(Database::prepare($query, $p_company_id, $p_table_name));
        }
        return $ret;
    }

    /**
     * @param \Wizacha\Core\Iterator\PdoColumnIterator $it - Iterator to send to UpdateEvent dispatcher
     */
    protected function sendUpdateEvent(\Wizacha\Core\Iterator\PdoColumnIterator $it)
    {
        \Wizacha\Events\Config::dispatch(
            \Wizacha\Product::EVENT_UPDATE,
            (new \Wizacha\Events\IterableEvent())->setIterator($it)
        );
    }

    public function privileges()
    {
        return array(
            'create' => true,
            'update' => true,
            'index'  => true,
            'delete' => true,
        );
    }

    public static function prepareRatesSend($rates)
    {
        return [
            ['amount' => 0, 'value' => $rates['rate_value']['I'][0]['value']],
            ['amount' => 1, 'value' => $rates['rate_value']['I'][1]['value']],
        ];
    }

    /**
     * @param array $params
     * @return string[]  A list of warning messages
     */
    public function checkDeprecatedFields(array $params)
    {
        $messages          = [];
        $deprecated_fields = [
            'category_ids',
        ];
        foreach ($deprecated_fields as $field) {
            if (\array_key_exists($field, $params)) {
                $messages[] = __('w_api_deprecated_field', ['[field]' => $field]);
            }
        }
        return $messages;
    }

    /** @var mixed[] $params */
    protected function checkSupplierRefInMvpRules(array $params)
    {
        $mvpRules = container()->getParameter('feature.multi_vendor_product.rules');

        if ('' !== $mvpRules && false === fn_check_supplier_ref_mvp_rules($params, \explode(',', $mvpRules))) {
            return [
                'status' => Response::STATUS_BAD_REQUEST,
                'data' => ['message' => 'Supplier reference is empty.'],
            ];
        }
    }

    /**
     * Factorisation of validations for create and update product
     */
    private function assertProductParamsAreValid(array $params, bool $isCreating = false): void
    {
        if (\array_key_exists('status', $params) && Status::isValid($params['status']) === false) {
            throw new \InvalidArgumentException('Status ' . $params['status'] . ' invalid, accepted values are A, D or H.');
        }

        if (container()->get(SubscriptionService::class)->hasFeatureFlag()) {
            if (\array_key_exists('is_subscription', $params) && \is_bool($params['is_subscription']) === false) {
                throw new \InvalidArgumentException('"is_subscription" must be a boolean.');
            }

            if (\array_key_exists('is_renewable', $params) && \is_bool($params['is_renewable']) === false) {
                throw new \InvalidArgumentException('"is_renewable" must be a boolean.');
            }
        } else {
            if (\array_key_exists('is_subscription', $params)) {
                throw new \InvalidArgumentException('Use of a forbidden key : "is_subscription"');
            }

            if (\array_key_exists('is_renewable', $params)) {
                throw new \InvalidArgumentException('Use of a forbidden key : "is_renewable"');
            }
        }

        if (\array_key_exists('infinite_stock', $params) && \is_bool($params['infinite_stock']) === false) {
            throw new \InvalidArgumentException('"infinite_stock" must be a boolean.');
        }

        if (true === $isCreating) {
            if (false === \array_key_exists('tax_ids', $params)) {
                throw new \InvalidArgumentException('Missing parameter "tax_ids"');
            }

            if (false === \is_array($params['tax_ids'])) {
                throw new \InvalidArgumentException('"tax_ids" must be array');
            }
        }

        if (true === \array_key_exists('tax_ids', $params) && 1 !== \count($params['tax_ids'])) {
            throw new \InvalidArgumentException('One and only one tax rate must be filled per product.');
        }
    }

    /** Check product code to prevent HTTP 500 on create or update */
    protected function checkExistingProduct(string $productCode, int $companyId = null): ?int
    {
        if (false === \is_int($companyId)) {
            $productId = Database::getField(
                "SELECT product_id FROM ?:products WHERE product_code = ?s",
                $productCode
            );
        } else {
            $productId = Database::getField(
                "SELECT product_id FROM ?:products WHERE product_code = ?s AND company_id = ?i",
                $productCode,
                $companyId
            );
        }

        if (false === is_numeric($productId)) {
            return null;
        }

        return (int) $productId;
    }

    protected function getCompanyId(array &$params): int
    {
        if ($this->auth['user_type'] === UserType::ADMIN) {
            if (false === \array_key_exists('company_id', $params)) {
                // company_id is required when creating a product as an admin
                throw new \InvalidArgumentException("Missing parameter company_id");
            }
            $companyId = $params['company_id'];
        } else {
            // Only admins can set company_id
            unset($params['company_id']);
            $companyId = Registry::get('runtime.company_id');
        }

        return (int) $companyId;
    }

    /**
     * Remove divisions from product creation or update
     *
     * @return mixed[] $params
     */
    protected function removeDivisionsFromProductData(array $params): array
    {
        // We don't want division on product creation or update
        if (true === \array_key_exists('divisions', $params)) {
            unset($params['divisions']);
        }

        return $params;
    }

    public function mapDataIfAdmin(array &$data, string $fromKey, string $toKey, bool $isAdmin): self
    {
        if (\array_key_exists($fromKey, $data) === true) {
            if ($isAdmin === true) {
                $data[$toKey] = $data[$fromKey];
            }
            unset($data[$fromKey]);
        }

        return $this;
    }
}
