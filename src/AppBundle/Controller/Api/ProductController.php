<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api;

use Doctrine\DBAL\Connection;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\EventDispatcher\LegacyEventDispatcherProxy;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Wizacha\AppBundle\Controller\Api\Division\DivisionsTreeTrait;
use Wizacha\AppBundle\Controller\Api\Division\Dto\ApiDivisionSettingsDto;
use Wizacha\AppBundle\Controller\DomainUserTrait;
use Wizacha\AppBundle\Security\User\ApiSecurityUser;
use Wizacha\Bridge\Symfony\Response\AccessDeniedJsonResponse;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Events\IterableEvent;
use Wizacha\Marketplace\Division\Exception\InvalidDivisionCodeException;
use Wizacha\Marketplace\Division\Service\DivisionProductsService;
use Wizacha\Marketplace\Division\Service\DivisionService;
use Wizacha\Marketplace\Division\Service\DivisionSettingsService;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\PIM\Product\Attachment;
use Wizacha\Marketplace\PIM\Product\ProductService;
use Wizacha\Marketplace\PIM\Stock\StockService;
use Wizacha\Marketplace\PIM\Video\Video;
use Wizacha\Product;

class ProductController extends Controller
{
    use DomainUserTrait;
    use DivisionsTreeTrait;

    private const KEY_NAME = 'stock';
    private const COMPANY_IDS = 'company_ids';

    private Connection $db;
    private string $csCartTablesPrefix;
    private DivisionService $divisionService;
    private DivisionProductsService $divisionProductsService;
    private ProductService $productService;
    private StockService $stockService;
    protected EventDispatcherInterface $eventDispatcher;
    private DivisionSettingsService $divisionSettingsService;
    private LoggerInterface $logger;

    public function __construct(
        Connection $db,
        string $csCartTablesPrefix,
        DivisionService $divisionService,
        DivisionProductsService $divisionProductsService,
        ProductService $productService,
        StockService $stockService,
        EventDispatcherInterface $eventDispatcher,
        DivisionSettingsService $divisionSettingsService,
        LoggerInterface $logger
    ) {
        $this->db = $db;
        $this->csCartTablesPrefix = $csCartTablesPrefix;
        $this->divisionService = $divisionService;
        $this->divisionProductsService = $divisionProductsService;
        $this->productService = $productService;
        $this->stockService = $stockService;
        $this->divisionSettingsService = $divisionSettingsService;
        $this->eventDispatcher = LegacyEventDispatcherProxy::decorate($eventDispatcher);
        $this->logger = $logger;
    }

    public function productNameAction(Request $request)
    {
        $products = $request->get('products_ids');
        $promotionId = $request->get('promotion_id');

        if (!empty($promotionId)) {
            $promotionType = $request->get('promotion_picker') ?? 'bonus';
            $products = $this->getProductsFromPromotionId($promotionId, $promotionType);
        }

        if (!\is_array($products) || empty($products)) {
            return BadRequestJsonResponse::invalidField('products_ids', 'products_ids must be an array of product ids');
        }

        /** @var ApiSecurityUser $user */
        $user = $this->getUser();
        $companyId = $user->getCompanyId();

        $sql = 'SELECT p.product_id, pd.product, company.company
                FROM ' . $this->csCartTablesPrefix . 'products AS p
                INNER JOIN ' . $this->csCartTablesPrefix . 'product_descriptions AS pd
                    ON pd.product_id = p.product_id AND pd.lang_code = ?
                INNER JOIN ' . $this->csCartTablesPrefix . 'companies AS company
                    ON company.company_id = p.company_id
                WHERE p.product_id IN (?)';

        $params = [(string) GlobalState::contentLocale(), $products];
        $paramsTypes = [\PDO::PARAM_STR, Connection::PARAM_INT_ARRAY];
        if ($companyId) {
            $sql .= ' AND p.company_id = ?';
            $params[] = $companyId;
            $paramsTypes[] = \PDO::PARAM_INT;
        }

        $productsData = $this->db->executeQuery($sql, $params, $paramsTypes)->fetchAll(\PDO::FETCH_ASSOC);

        return new JsonResponse($productsData);
    }

    public function addAttachmentsAction(int $productId, Request $request): JsonResponse
    {
        $entityManager = $this->container->get('doctrine.orm.entity_manager');

        $productService = $this->container->get('marketplace.pim.product.service');
        $product = $productService->get($productId);

        $user = $this->getUser();

        if ($product->getCompanyId() !== $user->getCompanyId()) {
            $this->denyAccessUnlessGranted('ROLE_ADMIN');
        }

        $attachmentsFiles = $request->files->get('attachments') ?? [];
        if (false === \is_array($attachmentsFiles)) {
            $attachmentsFiles = [$attachmentsFiles];
        }

        $attachmentsUrls = $request->request->get('attachments') ?? [];
        if (false === \is_array($attachmentsUrls)) {
            $attachmentsUrls = [$attachmentsUrls];
        }

        if (\count($attachmentsFiles) === 0 && \count($attachmentsUrls) === 0) {
            return BadRequestJsonResponse::missingField('attachments');
        }


        $attachments = [];

        /** @var UploadedFile $file */
        foreach ($attachmentsFiles as $file) {
            $attachment = $productService->createAttachment(
                $product,
                $file->getPathname(),
                $file->getClientOriginalName()
            );

            $entityManager->persist($attachment);

            $attachments[] = $attachment;
        }

        $invalidUrls = [];
        $attachmentsUrlsWithFile = [];
        foreach ($attachmentsUrls as $url) {
            $file = fn_get_url_data($url);

            if ($file === false) {
                $invalidUrls[] = $url;
                continue;
            }
            $attachmentsUrlsWithFile[] = [
                'url' => $url,
                'file' => $file,
            ];
        }

        if (0 !== \count($invalidUrls)) {
            return new BadRequestJsonResponse(
                __('cant_upload_file_not_fqdn_domain'),
                ['urls' => $invalidUrls],
            );
        }

        foreach ($attachmentsUrlsWithFile as $urlWithFile) {
            $attachment = $productService->createAttachment(
                $product,
                $urlWithFile['file']['path'],
                $urlWithFile['file']['name'],
                null,
                $urlWithFile['url']
            );

            $entityManager->persist($attachment);

            $attachments[] = $attachment;
        }

        $entityManager->flush($attachments);

        $this->container->get('event_dispatcher')->dispatch(
            IterableEvent::fromElement($productId),
            Product::EVENT_UPDATE
        );

        return new JsonResponse(array_map(function (Attachment $attachment): string {
            return $attachment->getId();
        }, $attachments), Response::HTTP_CREATED);
    }

    public function deleteAttachmentAction(int $productId, string $attachmentId)
    {
        $productService = $this->get('marketplace.pim.product.service');

        $product = $productService->get($productId);

        $user = $this->getUser();

        if ($product->getCompanyId() !== $user->getCompanyId()) {
            $this->denyAccessUnlessGranted('ROLE_ADMIN');
        }

        $attachment = $productService->getAttachment($attachmentId);

        if ($product != $attachment->getProduct()) {
            return new AccessDeniedJsonResponse(sprintf('Attachment #%s does not belong to product #%d', $attachmentId, $productId));
        }

        $productService->removeAttachment($attachment);

        return new Response('', Response::HTTP_NO_CONTENT);
    }

    public function getDivisionsAction(int $productId): Response
    {
        if (false === $this->isAvailableOffersEnabled()) {
            return $this->notImplementedResponse();
        }

        $this->assertCanAccessProduct($productId);

        $productDivisionSettings = $this->divisionSettingsService->getProductDivisionSettings($productId);

        return $this->json(
            (new ApiDivisionSettingsDto(
                $productDivisionSettings->getIncludedDivisions()->toArray(),
                $productDivisionSettings->getExcludedDivisions()->toArray()
            ))->toArray()
        );
    }

    public function getDivisionsTreeAction(Request $request, int $productId): Response
    {
        if (false === $this->isAvailableOffersEnabled()) {
            return $this->notImplementedResponse();
        }

        try {
            $options = $this->resolveOptions($request->query->all());
        } catch (\InvalidArgumentException $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        $this->assertCanAccessProduct($productId);

        $divisions = $this->divisionService->getProductDivisions(
            $productId,
            (string) GlobalState::contentLocale(),
            true,
            $options['isEnabled'],
            $options['rootCode']
        );

        $response = new JsonResponse();

        $this->setDivisionCache($response);

        return $response->setData($divisions);
    }

    public function putDivisionsAction(int $productId, Request $request): Response
    {
        if (false === $this->isAvailableOffersEnabled()) {
            return $this->notImplementedResponse();
        }

        $this->assertCanAccessProduct($productId);

        try {
            $payload = (new OptionsResolver())
                ->setRequired(['included', 'excluded',])
                ->setAllowedTypes('included', 'array')
                ->setAllowedTypes('excluded', 'array')
                ->resolve($request->request->all())
            ;
        } catch (\InvalidArgumentException $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        try {
            $this->divisionSettingsService->updateProductDivisionSettings(
                $productId,
                new ApiDivisionSettingsDto($payload['included'], $payload['excluded'])
            );
        } catch (InvalidDivisionCodeException $exception) {
            throw new BadRequestHttpException($exception->getMessage(), $exception);
        }

        return $this->json(null, Response::HTTP_NO_CONTENT);
    }

    private function exposeDivisionProducts(array &$divisionsProduct, array $divisions, bool $hasCountryCode)
    {
        foreach ($divisions as $division) {
            if (!$hasCountryCode) {
                if (\is_null($division->disabledBy) && $division->isEnabled) {
                    $divisionsProduct[] = $division->code;
                }
            } else {
                if (\is_null($division->disabledBy) && $division->isEnabled && !\is_null($division->productId)) {
                    $divisionsProduct[] = $division->code;
                }
            }

            if (isset($division->children)) {
                $this->exposeDivisionProducts($divisionsProduct, $division->children, $hasCountryCode);
            }
        }
    }

    private function getProductsFromPromotionId(string $promotionId, string $type): array
    {
        $promotion = $this->get('marketplace.promotion.promotionservice')->get($promotionId);

        if ('bonus' === $type) {
            return $promotion->getTarget()->getProductIds();
        } elseif ('rules' === $type) {
            $matches = [];
            $rule = $promotion->getRules();
            preg_match('/(products intersects|product in) \[([0-9,]*)\]/', $rule, $matches);

            return explode(',', $matches[2]);
        }

        return [];
    }

    public function addVideoAction(Request $request, int $productId): JsonResponse
    {
        if (!$request->request->has('url')) {
            return new BadRequestJsonResponse('url param not found');
        }

        $this->assertCanAccessProduct($productId);

        $videoUrl = $request->request->get('url');
        $videoService = container()->get('marketplace.pim.video_service');
        $videoValidator = container()->get('app.validator.video_validator');

        try {
            $videoValidator->assertIsUrlValid($videoUrl);
            $id = $videoService->startUploadFromUrl($productId, $videoUrl);
        } catch (BadRequestHttpException $e) {
            $this->logger->notice($e->getMessage(), [
                'data' => $videoUrl,
                'exception' => $e,
            ]);

            return new JsonResponse([
                'product_id' => $productId,
                'message' => $e->getMessage(),
            ], Response::HTTP_BAD_REQUEST);
        }

        return new JsonResponse([
            'id' => $id,
        ], Response::HTTP_CREATED);
    }

    public function removeVideoAction(int $productId): Response
    {
        $this->assertCanAccessProduct($productId);

        $videoService = container()->get('marketplace.pim.video_service');

        /* @var Video|null $video */
        $video = container()->get('marketplace.pim.video_repository')->findOneByProductId($productId);

        if ($video instanceof Video === true) {
            $videoService->delete($video->getId());

            return new Response(null, Response::HTTP_NO_CONTENT);
        }

        return new Response(null, Response::HTTP_NOT_FOUND);
    }

    public function updateStockAction(string $ean, Request $request): Response
    {
        $data = $this->getJsonContent($request);

        try {
            $this->validatePayload($data);
        } catch (\InvalidArgumentException $e) {
            $this->get("logger")->warning($e->getMessage(), [
                'exception' => $e,
            ]);

            return new BadRequestJsonResponse($e->getMessage());
        }

        $companyIds = [];
        if (\array_key_exists(static::COMPANY_IDS, $data) === true) {
            //Only admin can use company_ids
            $authorizationChecker = $this->get('security.authorization_checker');
            if ($authorizationChecker->isGranted('ROLE_ADMIN') === false) {
                throw new Forbidden('You are not allowed to use company_ids key.');
            }

            $companyIds = $data[static::COMPANY_IDS];
        }

        $declinations = $this->productService->getDeclinationsFromProductCode($ean, $companyIds);

        $updatedProductIds = [];
        $hasValidProduct = false;
        foreach ($declinations as $declination) {
            // Plusieurs vendeurs peuvent avoir le même EAN. Même si la première déclinaisons n'est pas updatable, une
            // des suivantes peut l'être.
            try {
                $this->assertCanAccessProduct($declination->getProductId());
            } catch (NotFound $e) {
                continue;
            }
            $hasValidProduct = true;

            if ($this->stockService->setDeclinationStock($declination, $data[static::KEY_NAME])) {
                $updatedProductIds[] = $declination->getProductId();
            }
        }

        if ($hasValidProduct === false) {
            throw NotFound::fromId('EAN', $ean);
        }

        $this->eventDispatcher->dispatch(
            IterableEvent::fromArray(array_unique($updatedProductIds)),
            Product::EVENT_UPDATE
        );

        return new JsonResponse(\count($updatedProductIds) . " entities updated.");
    }

    private function assertCanAccessProduct(int $productId)
    {
        $product = $this->productService->get($productId);

        $authorizationChecker = $this->get('security.authorization_checker');
        /** @var ApiSecurityUser $securityUser */
        $loggedInUser = $this->getDomainUser($this->getUser()->getId());

        // An admin can access all users
        if ($authorizationChecker->isGranted('ROLE_ADMIN')) {
            return;
        }

        // A non-admin can only access its own account
        if ($product->getCompanyId() === $loggedInUser->getCompanyId()) {
            return;
        }

        // 404 instead of 403 to avoid leaking information
        throw NotFound::fromId('Product', $productId);
    }

    protected function validatePayload(array $payload): self
    {
        if (false === \array_key_exists(static::KEY_NAME, $payload)) {
            throw new \InvalidArgumentException("Invalid payload : missing " . static::KEY_NAME . " key.");
        }

        if (false === \is_integer($payload[static::KEY_NAME])) {
            throw new \InvalidArgumentException("Invalid payload : " . static::KEY_NAME . " must be an integer.");
        }

        if (true === \array_key_exists(static::COMPANY_IDS, $payload) && false === \is_array($payload[static::COMPANY_IDS])) {
            throw new \InvalidArgumentException("Invalid payload : " . static::COMPANY_IDS . " must be an array.");
        }

        return $this;
    }

    protected function getJsonContent(Request $request): array
    {
        $data = json_decode($request->getContent(), true);
        if (false === \is_array($data)) {
            throw new BadRequestHttpException('Content has to be valid JSON. ' . json_last_error_msg());
        }

        return $data;
    }
}
