<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Tygh\Languages\Languages;
use Wizacha\Component\Language\LanguageNormalizer;

class LanguageController extends Controller
{
    public function listAction(LanguageNormalizer $languageNormalizer): JsonResponse
    {
        return new JsonResponse(
            array_values(
                array_map([$languageNormalizer, 'normalize'], Languages::get())
            )
        );
    }
}
