<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=0);

namespace Wizacha\AppBundle\Controller\Api\Promotion;

use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Serializer\Serializer;
use Wizacha\Component\DateRange\DateRange;
use Wizacha\Component\DateRange\DateRangeComparator;
use Wizacha\Marketplace\Promotion\Promotion;
use Wizacha\Marketplace\Promotion\PromotionService;

/** This class check fields validity before saving the marketplace promotion */
class MarketplacePromotionValidator
{
    private PromotionService $promotionService;
    private Serializer $serializer;
    private RulesUserGroupValidator $usersGroupsValidator;

    public function __construct(PromotionService $promotionService, Serializer $serializer, RulesUserGroupValidator $usersGroupsValidator)
    {
        $this->promotionService = $promotionService;
        $this->serializer = $serializer;
        $this->usersGroupsValidator = $usersGroupsValidator;
    }

    public function assertCouponIsUniqueOverItsPeriod(Promotion $newPromotion): self
    {
        $newPromotionDateRange = new DateRange($newPromotion->getStartTime(), $newPromotion->getEndTime());
        foreach ($this->promotionService->findMarketplacePromotionByCoupon($newPromotion->getCoupon()) as $existingPromotion) {
            if (DateRangeComparator::isOverlap(
                $newPromotionDateRange,
                new DateRange($existingPromotion->getStartTime(), $existingPromotion->getEndTime())
            )
            ) {
                throw new BadRequestHttpException(__('marketplace_promotion_already_exist'));
            }
        }

        return $this;
    }

    public function assertStartDateBeforeToday(Promotion $promotion): self
    {
        $today = new \DateTime('now');
        $today->setTime(0, 0, 0);
        if ($promotion->getStartTime() < $today) {
            throw new BadRequestHttpException(__('start_past_date_validation_error'));
        }

        return $this;
    }

    public function assertRules(Promotion $normalizedPromotion): self
    {
        $normalizedPromotion = $this->serializer->normalize($normalizedPromotion);

        if (\is_null($normalizedPromotion['rule']) === false) {
            $ruleType = new BasketRuleType($normalizedPromotion['rule']['type']);

            if ($ruleType->equals(BasketRuleType::AND())) {
                $ruleItemAmount = 0;
                $ruleItemProductList = 0;
                $ruleItemMaxUsage = 0;
                $ruleItemMaxUsageUser = 0;

                foreach ($normalizedPromotion['rule']['items'] as $item) {
                    $itemType = new BasketRuleType($item['type']);
                    if ($itemType->equals(BasketRuleType::BASKET_PRICE_SUPERIOR_TO())
                        || $itemType->equals(BasketRuleType::BASKET_PRICE_INFERIOR_TO())
                        || $itemType->equals(BasketRuleType::BASKET_PRICE_SUPERIOR_OR_EQUAL_TO())
                        || $itemType->equals(BasketRuleType::BASKET_PRICE_INFERIOR_OR_EQUAL_TO())
                    ) {
                        $ruleItemAmount++;
                    }

                    if ($itemType->equals(BasketRuleType::BASKET_HAS_PRODUCT_IN_LIST())) {
                        $ruleItemProductList++;
                    }

                    if ($itemType->equals(BasketRuleType::MAX_USAGE_COUNT())) {
                        $ruleItemMaxUsage++;
                    }

                    if ($itemType->equals(BasketRuleType::MAX_USAGE_COUNT_PER_USER())) {
                        $ruleItemMaxUsageUser++;
                    }
                }

                if ($ruleItemAmount > 1) {
                    throw new BadRequestHttpException('Rule on basket amount can be used only once.');
                }
                if ($ruleItemProductList > 1) {
                    throw new BadRequestHttpException('Rule "basket_has_product_in_list" can be used only once.');
                }
                if ($ruleItemMaxUsage > 1) {
                    throw new BadRequestHttpException('Rule "max_usage_count" can be used only once.');
                }
                if ($ruleItemMaxUsageUser > 1) {
                    throw new BadRequestHttpException('Rule "max_usage_count_per_user" can be used only once.');
                }
            } elseif ($ruleType->equals(BasketRuleType::BASKET_HAS_GROUP_IN_LIST())) {
                $this->usersGroupsValidator->assertRulesUsersGroupsIdsExists($normalizedPromotion['rule']['groups_ids']);
            }
        }

        return $this;
    }
}
