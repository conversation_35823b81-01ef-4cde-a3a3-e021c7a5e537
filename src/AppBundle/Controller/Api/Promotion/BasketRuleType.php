<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\Promotion;

use MyCLabs\Enum\Enum;

/**
 * @method static BasketRuleType AND()
 * @method static BasketRuleType OR()
 * @method static BasketRuleType BASKET_HAS_PRODUCT_IN_LIST()
 * @method static BasketRuleType MAX_USAGE_COUNT()
 * @method static BasketRuleType MAX_USAGE_COUNT_PER_USER()
 * @method static BasketRuleType BASKET_PRICE_SUPERIOR_TO()
 * @method static BasketRuleType BASKET_PRICE_INFERIOR_TO()
 * @method static BasketRuleType BASKET_PRICE_SUPERIOR_OR_EQUAL_TO()
 * @method static BasketRuleType BASKET_PRICE_INFERIOR_OR_EQUAL_TO()
 * @method static BasketRuleType BASKET_QUANTITY_SUPERIOR_TO()
 * @method static BasketRuleType BASKET_QUANTITY_INFERIOR_TO()
 * @method static BasketRuleType BASKET_QUANTITY_SUPERIOR_OR_EQUAL_TO()
 * @method static BasketRuleType BASKET_QUANTITY_INFERIOR_OR_EQUAL_TO()
 * @method static BasketRuleType BASKET_HAS_GROUP_IN_LIST()
 */
final class BasketRuleType extends Enum
{
    private const AND = 'and';
    private const OR = 'or';
    private const BASKET_HAS_PRODUCT_IN_LIST = 'basket_has_product_in_list';
    private const MAX_USAGE_COUNT = 'max_usage_count';
    private const MAX_USAGE_COUNT_PER_USER = 'max_usage_count_per_user';
    private const BASKET_PRICE_SUPERIOR_TO = 'basket_price_superior_to';
    private const BASKET_PRICE_INFERIOR_TO = 'basket_price_inferior_to';
    private const BASKET_PRICE_SUPERIOR_OR_EQUAL_TO = 'basket_price_superior_or_equal_to';
    private const BASKET_PRICE_INFERIOR_OR_EQUAL_TO = 'basket_price_inferior_or_equal_to';
    private const BASKET_QUANTITY_SUPERIOR_TO = 'basket_quantity_superior_to';
    private const BASKET_QUANTITY_INFERIOR_TO = 'basket_quantity_inferior_to';
    private const BASKET_QUANTITY_SUPERIOR_OR_EQUAL_TO = 'basket_quantity_superior_or_equal_to';
    private const BASKET_QUANTITY_INFERIOR_OR_EQUAL_TO = 'basket_quantity_inferior_or_equal_to';
    private const BASKET_HAS_GROUP_IN_LIST = 'basket_has_users_groups';
}
