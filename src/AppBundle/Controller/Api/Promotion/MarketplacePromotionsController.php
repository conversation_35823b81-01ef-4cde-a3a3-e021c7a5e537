<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\Promotion;

use Hoa\Compiler\Exception\UnexpectedToken;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\ConflictHttpException;
use Symfony\Component\OptionsResolver\Exception\UndefinedOptionsException;
use Symfony\Component\OptionsResolver\Options;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Serializer\Encoder\JsonEncoder;
use Symfony\Component\Serializer\Exception\NotEncodableValueException;
use Symfony\Component\Serializer\Serializer;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Bridge\Symfony\Response\InternalServerErrorJsonResponse;
use Wizacha\FeatureFlag\FeatureFlaggableInterface;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\Promotion\BonusTarget\BonusTarget;
use Wizacha\Marketplace\Promotion\BonusTarget\BonusTargetProductCategoryInBasket;
use Wizacha\Marketplace\Promotion\BonusTarget\BonusTargetProductInBasket;
use Wizacha\Marketplace\Promotion\MarketplacePromotion;
use Wizacha\Marketplace\Promotion\Promotion;
use Wizacha\Marketplace\Promotion\PromotionService;
use Wizacha\Marketplace\Promotion\PromotionType;

final class MarketplacePromotionsController extends Controller implements FeatureFlaggableInterface
{
    public const LIST_OFFSET_DEFAULT = 0;
    public const LIST_LIMIT_DEFAULT = 10;
    public const LIST_LIMIT_MAX = 100;

    /** @var PromotionService */
    private $promotionService;

    /** @var Serializer */
    private $serializer;

    /** @var MarketplacePromotionValidator */
    private $marketplacePromotionValidator;

    private bool $marketplaceDiscountOptionalBonuses;

    public function __construct(
        PromotionService $promotionService,
        Serializer $serializer,
        MarketplacePromotionValidator $marketplacePromotionValidator,
        bool $marketplaceDiscountOptionalBonuses
    ) {
        $this->promotionService = $promotionService;
        $this->serializer = $serializer;
        $this->marketplacePromotionValidator = $marketplacePromotionValidator;
        $this->marketplaceDiscountOptionalBonuses = $marketplaceDiscountOptionalBonuses;
    }

    public function getAction(string $promotionId): JsonResponse
    {
        $promotions = $this->promotionService->get($promotionId);

        return new JsonResponse($this->serializer->normalize($promotions));
    }

    public function upsertAction(Request $request, string $promotionId = null): JsonResponse
    {
        try {
            $bodyData = $this->serializer->decode($request->getContent(), JsonEncoder::FORMAT);
        } catch (NotEncodableValueException $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        $target = $bodyData['target']['type'];
        if (false === $this->marketplaceDiscountOptionalBonuses
            && (BonusTarget::fromString($target) instanceof BonusTargetProductInBasket === true
            || BonusTarget::fromString($target) instanceof BonusTargetProductCategoryInBasket === true)
        ) {
            throw new Forbidden('promotion target is not allowed');
        }

        try {
            (new OptionsResolver())
                ->setDefined([
                    'active',
                    'coupon',
                    'discounts',
                    'name',
                    'period',
                    'rule',
                    'target',
                    'isValid',
                ])
                ->resolve($bodyData);
        } catch (UndefinedOptionsException $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        $companyId = $this->getUser()->getCompanyId();
        $existingPromotion = $promotionId !== null ? $this->promotionService->get($promotionId) : null;

        if ($existingPromotion !== null && $existingPromotion->getType()->equals(PromotionType::MARKETPLACE()) === false) {
            throw new ConflictHttpException('cannot change promotion\'s type');
        }

        /** @var Promotion $promotion */
        $promotion = $this->serializer->denormalize($bodyData, MarketplacePromotion::class, JsonEncoder::FORMAT, [
            BasketPromotionNormalizer::CONTEXT_KEY_COMPANY_ID => $companyId,
            BasketPromotionNormalizer::CONTEXT_KEY_EXISTING_PROMOTION => $existingPromotion,
        ]);

        try {
            $this->marketplacePromotionValidator->assertRules($promotion);
        } catch (UnexpectedToken $exception) {
            $errorMessage = 'Failed to update promotion marketplace.';
            if (\is_null($existingPromotion) === true) {
                $errorMessage = 'Failed to create promotion marketplace.';
            }
            $this->get('logger')->error($errorMessage, [
                'exception' => $exception,
                'data' => $bodyData,
                'companyId' => $companyId,
                'promotionId' => $promotionId
            ]);

            return new InternalServerErrorJsonResponse($errorMessage);
        }

        // Only for insert case, thoses fields cannot be changed at update
        if (\is_null($existingPromotion) === true) {
            $this->marketplacePromotionValidator->assertCouponIsUniqueOverItsPeriod($promotion);
            $this->marketplacePromotionValidator->assertStartDateBeforeToday($promotion);
        }

        $this->promotionService->save($promotion);

        $responseData = $this->serializer->normalize($promotion);

        return new JsonResponse($responseData);
    }

    public function listAction(Request $request): JsonResponse
    {
        try {
            $options = $this->resolveListOptions($request);
        } catch (\Throwable $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        [$promotions, $totalCount] = $this->promotionService->listMarketplacePromotionsPaginated($options['offset'], $options['limit'], $options);

        return new JsonResponse([
            'offset' => $options['offset'],
            'limit' => $options['limit'],
            'total' => $totalCount,
            'items' => $this->serializer->normalize($promotions),
        ]);
    }

    protected function resolveListOptions(Request $request): array
    {
        $normalizerInteger = function (Options $options, $value): int {
            if ($value !== '0' && $value !== '1') {
                throw new BadRequestHttpException('The value of parameter "active" must be "0" or "1"');
            }

            return (int) $value;
        };

        $resolver = (new OptionsResolver())
            ->setDefaults([
                'offset' => static::LIST_OFFSET_DEFAULT,
                'limit' => static::LIST_LIMIT_DEFAULT,
            ])
            ->setDefined(['active', 'coupon', 'isValid'])
            ->setAllowedTypes('active', 'string')
            ->setAllowedTypes('coupon', 'string')
            ->setAllowedTypes('isValid', 'string')
            ->setNormalizer('active', $normalizerInteger)
            ->setNormalizer('isValid', $normalizerInteger)
            ->setNormalizer('offset', function (Options $options, $value) {
                return max((int) $value, 0);
            })
            ->setNormalizer('limit', function (Options $options, $value) {
                return min((int) $value, static::LIST_LIMIT_MAX);
            });

        return $resolver->resolve($request->query->all());
    }

    public function getFeatureFlag(): string
    {
        return 'feature.marketplace_discounts';
    }
}
