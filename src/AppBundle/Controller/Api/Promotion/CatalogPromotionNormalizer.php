<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\Promotion;

use Hoa\Ruler\Model\Bag\Context;
use Hoa\Ruler\Model\Bag\RulerArray;
use Hoa\Ruler\Model\Bag\Scalar;
use Hoa\Ruler\Model\Operator;
use RulerZ\Parser\Parser;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Wizacha\Marketplace\Promotion\BasketPromotion;
use Wizacha\Marketplace\Promotion\Bonus\Bonus;
use Wizacha\Marketplace\Promotion\Bonus\FixedBonus;
use Wizacha\Marketplace\Promotion\Bonus\PercentageBonus;
use Wizacha\Marketplace\Promotion\BonusTarget\BonusTargetProduct;
use Wizacha\Marketplace\Promotion\CouponBasketInterface;
use Wizacha\Marketplace\Promotion\MarketplacePromotion;
use Wizacha\Marketplace\Promotion\Promotion;
use Wizacha\Marketplace\Promotion\PromotionInterface;
use Wizacha\Marketplace\Promotion\Rule\Exception\InvalidRules;
use Wizacha\Money\Money;

final class CatalogPromotionNormalizer implements NormalizerInterface, DenormalizerInterface
{
    public const CONTEXT_KEY_EXISTING_PROMOTION = 'existingPromotion';
    public const CONTEXT_KEY_COMPANY_ID = 'company_id';
    /**
     * @var Parser
     */
    private $rulerzParser;

    public function __construct(Parser $rulerzParser)
    {
        $this->rulerzParser = $rulerzParser;
    }

    /**
     * @inheritdoc
     */
    public function normalize($promotion, $format = null, array $context = array())
    {
        if (!$this->supportsNormalization($promotion, $format)) {
            throw new \InvalidArgumentException();
        }

        $rule = null;
        if (trim($promotion->getRules()) !== '') {
            $parsed = $this->rulerzParser->parse($promotion->getRules());
            $rule = self::normalizeRule($parsed->getExpression());
        }

        return [
            'promotion_id' => $promotion->getId(),
            'company_id' => $promotion->getCompanyId(),
            'name' => $promotion->getName(),
            'active' => $promotion->isActive(),
            'rule' => $rule,
            'ruleString' => $promotion->getRules(),
            'period' => [
                'from' => $promotion->getStartTime()->format(\DateTime::RFC3339),
                'to' => $promotion->getEndTime()->format(\DateTime::RFC3339),
            ],
            'discounts' => array_map(static function (Bonus $bonus): array {
                if ($bonus instanceof PercentageBonus) {
                    return [
                        'type' => PercentageBonus::ID,
                        'percentage' => $bonus->getReduction(),
                    ];
                }

                if ($bonus instanceof FixedBonus) {
                    return [
                        'type' => FixedBonus::ID,
                        'value' => $bonus->getReduction()->getConvertedAmount(),
                    ];
                }

                throw new \Exception('Unsupported discount type');
            }, $promotion->getBonuses()->toArray()),
        ];
    }

    /**
     * @inheritdoc
     */
    public function supportsNormalization($data, $format = null): bool
    {
        return $data instanceof Promotion && false === $data instanceof CouponBasketInterface;
    }

    /**
     * @inheritdoc
     */
    public function denormalize($data, $class, $format = null, array $context = array()): Promotion
    {
        $id = null;
        if (isset($data['promotion_id'])) {
            $id = (string) $data['promotion_id'];
        }

        $companyId = null;
        if (isset($data['company_id'])) {
            $companyId = (int) $data['company_id'];
        } elseif (isset($context[self::CONTEXT_KEY_COMPANY_ID])) {
            $companyId = (int) $context[self::CONTEXT_KEY_COMPANY_ID];
        }

        if (isset($context[self::CONTEXT_KEY_EXISTING_PROMOTION])) {
            /** @var Promotion $promotion */
            $promotion = $context[self::CONTEXT_KEY_EXISTING_PROMOTION];

            if ($id !== null && $id !== $promotion->getId()) {
                throw new \Exception('cannot update promotion\'s id');
            }
            if ($companyId !== null && $companyId !== $promotion->getCompanyId()) {
                throw new \Exception('cannot update promotion\'s company_id');
            }
        } else {
            $promotion = new Promotion($id, $companyId, '', [], new BonusTargetProduct());
        }

        if (isset($data['rule'])) {
            $rulesString = self::denormalizeRule($data['rule']);
            $promotion->setRules($rulesString);
        }

        if (isset($data['name'])) {
            $promotion->setName((string) $data['name']);
        }

        if (isset($data['active'])) {
            $promotion->setActive((bool) $data['active']);
        }

        if (isset($data['period']['from'])) {
            $from = \DateTime::createFromFormat(\DateTime::RFC3339, $data['period']['from']);
            if ($from !== false) {
                $promotion->setActivationPeriod($from, $promotion->getEndTime());
            } else {
                throw new \Exception('Unsupported date format');
            }
        }

        if (isset($data['period']['to'])) {
            $to = \DateTime::createFromFormat(\DateTime::RFC3339, $data['period']['to']);
            if ($to !== false) {
                $promotion->setActivationPeriod($promotion->getStartTime(), $to);
            } else {
                throw new \Exception('Unsupported date format');
            }
        }

        if (isset($data['discounts'])) {
            $promotion->setBonuses(array_map(static function (array $discountData): Bonus {
                switch ($discountData['type']) {
                    case PercentageBonus::ID:
                        return new PercentageBonus($discountData['percentage']);
                    case FixedBonus::ID:
                        return new FixedBonus(Money::fromVariable($discountData['value']));
                    default:
                        throw new \Exception('Unsupported discount type ' . $discountData['type']);
                }
            }, $data['discounts']));
        }

        return $promotion;
    }

    /**
     * @inheritdoc
     */
    public function supportsDenormalization($data, $type, $format = null): bool
    {
        return Promotion::class === $type;
    }

    private static function normalizeRule(Operator $expression): array
    {
        switch ($expression->getName()) {
            case 'and':
                return [
                    'type' => CatalogRuleType::AND()->getValue(),
                    'items' => self::flattenComposites(
                        CatalogRuleType::AND(),
                        array_map([self::class, 'normalizeRule'], $expression->getArguments())
                    ),
                ];
            case 'or':
                return [
                    'type' => CatalogRuleType::OR()->getValue(),
                    'items' => self::flattenComposites(
                        CatalogRuleType::OR(),
                        array_map([self::class, 'normalizeRule'], $expression->getArguments())
                    ),
                ];
            case '>':
                $target = $expression->getArguments()[0];
                if (!($target instanceof Context) || $target->getId() !== 'price') {
                    throw new \Exception('Invalid rule');
                }

                $value = $expression->getArguments()[1];
                if (!($value instanceof Scalar)) {
                    throw new \Exception('Invalid rule');
                }

                return [
                    'type' => CatalogRuleType::PRODUCT_PRICE_SUPERIOR_TO()->getValue(),
                    'value' => $value->getValue(),
                ];
            case '<':
                $target = $expression->getArguments()[0];
                if (!($target instanceof Context) || $target->getId() !== 'price') {
                    throw new \Exception('Invalid rule');
                }

                $value = $expression->getArguments()[1];
                if (!($value instanceof Scalar)) {
                    throw new \Exception('Invalid rule');
                }

                return [
                    'type' => CatalogRuleType::PRODUCT_PRICE_INFERIOR_TO()->getValue(),
                    'value' => $value->getValue(),
                ];
            case 'in':
                $target = $expression->getArguments()[0];
                if (!($target instanceof Context) || $target->getId() !== 'product') {
                    throw new \Exception('Invalid rule');
                }

                $values = $expression->getArguments()[1];
                if (!($values instanceof RulerArray)) {
                    throw new \Exception('Invalid rule');
                }

                return [
                    'type' => CatalogRuleType::PRODUCT_IN_LIST()->getValue(),
                    'products_ids' => array_map(static function (Scalar $value) {
                        return $value->getValue();
                    }, $values->getArray()),
                ];
            case 'intersects':
                $target = $expression->getArguments()[0];
                if (!($target instanceof Context) || $target->getId() !== 'categories') {
                    throw new \Exception('Invalid rule');
                }

                $values = $expression->getArguments()[1];
                if (!($values instanceof RulerArray)) {
                    throw new \Exception('Invalid rule');
                }

                return [
                    'type' => CatalogRuleType::PRODUCT_IN_CATEGORY_LIST()->getValue(),
                    'categories_ids' => array_map(static function (Scalar $value) {
                        return $value->getValue();
                    }, $values->getArray()),
                ];
            default:
                throw new \Exception('Unsupported operator ' . $expression->getName());
        }
    }

    /**
     * Move up one level all items of the given rule type.
     * For example if you have an "OR" rule A as an item of an "OR" rule B, then A will be replaced by its own items.
     */
    private static function flattenComposites(CatalogRuleType $ruleType, array $items): array
    {
        return array_reduce($items, static function (array $flattenedItems, array $item) use ($ruleType): array {
            if ($item['type'] === $ruleType->getValue()) {
                $flattenedItems = array_merge($flattenedItems, $item['items']);
            } else {
                $flattenedItems[] = $item;
            }

            return $flattenedItems;
        }, []);
    }

    private static function denormalizeRule(array $ruleData): string
    {
        $ruleType = new CatalogRuleType($ruleData['type']);

        switch (true) {
            case $ruleType->equals(CatalogRuleType::AND()):
                $operand = 'and';

                return self::denormalizeCompositeRule($operand, $ruleData['items']);
            case $ruleType->equals(CatalogRuleType::OR()):
                $operand = 'or';

                return self::denormalizeCompositeRule($operand, $ruleData['items']);
            case $ruleType->equals(CatalogRuleType::PRODUCT_IN_CATEGORY_LIST()):
                if (empty($ruleData['categories_ids'])) {
                    throw new InvalidRules('missing categories\' ids');
                }

                return '(categories intersects [' . join(',', $ruleData['categories_ids']) . '])';
            case $ruleType->equals(CatalogRuleType::PRODUCT_IN_LIST()):
                if (empty($ruleData['products_ids'])) {
                    throw new InvalidRules('missing products\' ids');
                }

                return '(product in [' . join(',', $ruleData['products_ids']) . '])';
            case $ruleType->equals(CatalogRuleType::PRODUCT_PRICE_INFERIOR_TO()):
                return '(price < ' . $ruleData['value'] . ')';
            case $ruleType->equals(CatalogRuleType::PRODUCT_PRICE_SUPERIOR_TO()):
                return '(price > ' . $ruleData['value'] . ')';
            default:
                throw new InvalidRules('unsupported rule type ' . $ruleType->getValue());
        }
    }

    private static function denormalizeCompositeRule(string $operand, array $itemsData): string
    {
        $itemsRulesStrings = array_map([self::class, 'denormalizeRule'], $itemsData);

        return '(' . join(' ' . $operand . ' ', $itemsRulesStrings) . ')';
    }
}
