<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\Promotion;

use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Serializer\Serializer;
use Wizacha\Marketplace\Promotion\Promotion;

class BasketPromotionValidator
{
    private Serializer $serializer;
    private RulesUserGroupValidator $usersGroupsValidator;

    public function __construct(Serializer $serializer, RulesUserGroupValidator $usersGroupsValidator)
    {
        $this->serializer = $serializer;
        $this->usersGroupsValidator = $usersGroupsValidator;
    }

    public function assertRules(Promotion $promotion): void
    {
        $normalizedPromotion = $this->serializer->normalize($promotion);

        if (\array_key_exists('rule', $normalizedPromotion) && \is_array($normalizedPromotion['rule'])) {
            $ruleType = new BasketRuleType($normalizedPromotion['rule']['type']);

            if ($ruleType->equals(BasketRuleType::BASKET_HAS_GROUP_IN_LIST())) {
                $this->usersGroupsValidator->assertRulesUsersGroupsIdsExists($normalizedPromotion['rule']['groups_ids']);
            }
        }
    }
}
