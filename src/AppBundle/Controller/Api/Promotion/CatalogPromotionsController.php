<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\Promotion;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\ConflictHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Serializer\Encoder\JsonEncoder;
use Symfony\Component\Serializer\Serializer;
use Wizacha\Marketplace\Promotion\Promotion;
use Wizacha\Marketplace\Promotion\PromotionService;
use Wizacha\Marketplace\Promotion\PromotionType;

final class CatalogPromotionsController extends Controller
{
    /**
     * @var PromotionService
     */
    private $promotionService;

    /**
     * @var Serializer
     */
    private $serializer;

    public function __construct(PromotionService $promotionService, Serializer $serializer)
    {
        $this->promotionService = $promotionService;
        $this->serializer = $serializer;
    }

    public function upsertAction(Request $request, ?string $promotionId = null): JsonResponse
    {
        $companyId = $this->getUser()->getCompanyId();

        $existingPromotion = $promotionId !== null ? $this->promotionService->get($promotionId) : null;
        if ($existingPromotion !== null && !$existingPromotion->getType()->equals(PromotionType::CATALOG())) {
            throw new ConflictHttpException('cannot change promotion\'s type');
        }

        /** @var Promotion $promotion */
        $promotion = $this->serializer->deserialize($request->getContent(), Promotion::class, JsonEncoder::FORMAT, [
            CatalogPromotionNormalizer::CONTEXT_KEY_COMPANY_ID => $companyId,
            CatalogPromotionNormalizer::CONTEXT_KEY_EXISTING_PROMOTION => $existingPromotion,
        ]);

        $this->promotionService->save($promotion);

        $responseData = $this->serializer->normalize($promotion);

        return new JsonResponse($responseData);
    }

    public function getAction(string $promotionId): JsonResponse
    {
        $promotion = $this->promotionService->get($promotionId);

        if ($this->promotionService->get($promotionId)->getCompanyId() !== $this->getUser()->getCompanyId() || !$promotion->getType()->equals(PromotionType::CATALOG())) {
            throw new NotFoundHttpException();
        }

        $responseData = $this->serializer->normalize($promotion);

        return new JsonResponse($responseData);
    }

    public function listForCompanyAction(): JsonResponse
    {
        $companyId = $this->getUser()->getCompanyId();
        $promotions = $this->promotionService->findByCompany($companyId, PromotionType::CATALOG());

        $responseData = $this->serializer->normalize([
            'promotions' => $promotions,
        ]);

        return new JsonResponse($responseData);
    }

    public function deleteAction(string $promotionId): Response
    {
        $promotion = $this->promotionService->get($promotionId);
        if ($promotion->getCompanyId() !== $this->getUser()->getCompanyId() || !$promotion->getType()->equals(PromotionType::CATALOG())) {
            throw new NotFoundHttpException();
        }

        $this->promotionService->delete($promotionId);

        return new Response(null, Response::HTTP_NO_CONTENT);
    }
}
