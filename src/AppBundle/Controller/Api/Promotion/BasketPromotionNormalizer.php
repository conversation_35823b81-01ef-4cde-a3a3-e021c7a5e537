<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\Promotion;

use Hoa\Ruler\Model\Bag\Context;
use Hoa\Ruler\Model\Bag\RulerArray;
use Hoa\Ruler\Model\Bag\Scalar;
use Hoa\Ruler\Model\Operator;
use RulerZ\Parser\Parser;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Wizacha\Marketplace\Order\AmountsCalculator\Service\OrderAmountsCalculator;
use Wizacha\Marketplace\Promotion\BasketPromotion;
use Wizacha\Marketplace\Promotion\Bonus\Bonus;
use Wizacha\Marketplace\Promotion\Bonus\FixedBonus;
use Wizacha\Marketplace\Promotion\Bonus\PercentageBonus;
use Wizacha\Marketplace\Promotion\BonusTarget\BonusTarget;
use Wizacha\Marketplace\Promotion\BonusTarget\BonusTargetProductCategoryInBasket;
use Wizacha\Marketplace\Promotion\BonusTarget\BonusTargetProductInBasket;
use Wizacha\Marketplace\Promotion\MarketplacePromotion;
use Wizacha\Marketplace\Promotion\Promotion;
use Wizacha\Marketplace\Promotion\PromotionInterface;
use Wizacha\Marketplace\Promotion\Rule\Exception\InvalidRules;
use Wizacha\Money\Money;

final class BasketPromotionNormalizer implements NormalizerInterface, DenormalizerInterface
{
    public const CONTEXT_KEY_EXISTING_PROMOTION = 'existingPromotion';
    public const CONTEXT_KEY_COMPANY_ID = 'company_id';
    /**
     * @var Parser
     */
    private $rulerzParser;

    public function __construct(Parser $rulerzParser)
    {
        $this->rulerzParser = $rulerzParser;
    }

    /**
     * @inheritdoc
     */
    public function normalize($promotion, $format = null, array $context = array())
    {
        if (!$this->supportsNormalization($promotion, $format)) {
            throw new \InvalidArgumentException();
        }

        $rule = null;
        if (trim($promotion->getRules()) !== '') {
            $parsed = $this->rulerzParser->parse($promotion->getRules());
            $rule = self::normalizeRule($parsed->getExpression());
        }

        $data = [
            'promotion_id' => $promotion->getId(),
            'name' => $promotion->getName(),
            'active' => $promotion->isActive(),
            'isValid' => $promotion->isValid(),
            'rule' => $rule,
            'ruleString' => $promotion->getRules(),
            'period' => [
                'from' => $promotion->getStartTime()->format(\DateTime::RFC3339),
                'to' => $promotion->getEndTime()->format(\DateTime::RFC3339),
            ],
            'discounts' => array_map(static function (Bonus $bonus): array {
                if ($bonus instanceof PercentageBonus) {
                    return [
                        'type' => PercentageBonus::ID,
                        'percentage' => $bonus->getReduction(),
                        'maxAmount' => ($bonus->getMaxAmount() instanceof Money === true) ? $bonus->getMaxAmount()->getConvertedAmount() : null
                    ];
                }

                if ($bonus instanceof FixedBonus) {
                    return [
                        'type' => FixedBonus::ID,
                        'value' => $bonus->getReduction()->getConvertedAmount(),
                        'maxAmount' => ($bonus->getMaxAmount() instanceof Money === true) ? $bonus->getMaxAmount()->getConvertedAmount() : null
                    ];
                }

                throw new \Exception('Unsupported discount type');
            }, $promotion->getBonuses()->toArray()),
            'target' => [
                'type' => (string) $promotion->getTarget(),
            ],
            'coupon' => $promotion->getCoupon(),
        ];

        if ($promotion instanceof MarketplacePromotion === false) {
            $data['company_id'] = $promotion->getCompanyId();
        }

        return $data;
    }

    /**
     * @inheritdoc
     */
    public function supportsNormalization($data, $format = null): bool
    {
        return $data instanceof PromotionInterface;
    }

    /**
     * @inheritdoc
     */
    public function denormalize($data, $class, $format = null, array $context = array()): Promotion
    {
        $id = null;
        if (isset($data['promotion_id'])) {
            $id = (string) $data['promotion_id'];
        }

        $companyId = null;
        if (isset($data['company_id'])) {
            $companyId = (int) $data['company_id'];
        } elseif (isset($context[static::CONTEXT_KEY_COMPANY_ID])) {
            $companyId = (int) $context[static::CONTEXT_KEY_COMPANY_ID];
        }

        if ($class === MarketplacePromotion::class) {
            $companyId = null;
        }

        // BonusTarget::fromString peut renseigner des ids de target si le "type" est renseigné de la même facon que
        // lorsqu'on récuperer la target dans un GET promotions. Il est donc acceptable que le champs products_ids ou
        // categories_ids ne soient pas renseignées si le "type" contient des ids
        $target = isset($data['target']['type']) ? BonusTarget::fromString($data['target']['type']) : null;

        if ($target instanceof BonusTargetProductInBasket) {
            if (isset($data['target']['products_ids'])) {
                $target->setProductIds(...$data['target']['products_ids']);
            } elseif (\count($target->getProductIds()) === 0) {
                throw new BadRequestHttpException("Target : products_ids must be set");
            }
        }

        if ($target instanceof BonusTargetProductCategoryInBasket) {
            if (isset($data['target']['categories_ids'])) {
                $target->setProductCategoryIds(...$data['target']['categories_ids']);
            } elseif (\count($target->getProductCategoryIds()) === 0) {
                throw new BadRequestHttpException("Target : categories_ids must be set");
            }
        }

        if (isset($context[static::CONTEXT_KEY_EXISTING_PROMOTION])) {
            $promotion = $context[self::CONTEXT_KEY_EXISTING_PROMOTION];

            if ($id !== null && $id !== $promotion->getId()) {
                throw new \Exception('cannot update promotion\'s id');
            }

            if ($companyId !== null && $companyId !== $promotion->getCompanyId() && $class === BasketPromotion::class) {
                throw new \Exception('cannot update promotion\'s company_id');
            }

            $normalizedExistingPromotion = $this->normalize($promotion);
            $this->assertDiscountsCanBeUpdated($data, $normalizedExistingPromotion, $class);
            $this->assertTargetCanBeUpdated($data, $normalizedExistingPromotion, $class);
            $this->assertPeriodCanBeUpdated($data, $normalizedExistingPromotion, $class);
            $this->assertCouponCanBeUpdated($data, $normalizedExistingPromotion, $class);
            $this->assertRuleTypeCanBeUpdated($data, $class);

            if ($target !== null) {
                $promotion->setTarget($target);
            }
        } else {
            if ($target === null) {
                throw new BadRequestHttpException('missing promotion target');
            }

            if ($class === BasketPromotion::class) {
                $promotion = new BasketPromotion($id, $companyId, '', [], $target);
            } else {
                try {
                    $promotion = new MarketplacePromotion($data['coupon'], $id, '', [], $target, $companyId);
                } catch (\Throwable $e) {
                    throw new BadRequestHttpException('Field coupon is required.');
                }
            }

            $this->assertName($data, $class);

            $this->assertPeriod($data, $class);
        }

        if ($this->isBasketPromotionCreate($context, $class) === true || $this->isBasketPromotionUpdate($context, $class) === true || $this->isMarketplacePromotionCreate($context, $class)) {
            if (isset($data['coupon'])) {
                $promotion->setCoupon($data['coupon']);
            }

            if (isset($data['period']['from'])) {
                $from = \DateTime::createFromFormat(\DateTime::RFC3339, $data['period']['from']);
                if ($from === false) {
                    throw new InvalidRules('Unsupported date format');
                }

                try {
                    $promotion->setActivationPeriod($from, $promotion->getEndTime());
                } catch (\Throwable $e) {
                    throw new BadRequestHttpException($e->getMessage());
                }
            }

            if (isset($data['period']['to'])) {
                $to = \DateTime::createFromFormat(\DateTime::RFC3339, $data['period']['to']);
                if ($to === false) {
                    throw new InvalidRules('Unsupported date format');
                }

                try {
                    $promotion->setActivationPeriod($promotion->getStartTime(), $to);
                } catch (\Throwable $e) {
                    throw new BadRequestHttpException($e->getMessage());
                }
            }

            if (isset($data['discounts'])) {
                if ($promotion instanceof MarketplacePromotion && \count($data['discounts']) > 1) {
                    throw new BadRequestHttpException('Multiple discounts unauthorized, please choose between "Fixed" and "Percentage".');
                }

                $promotion->setBonuses(array_map(static function (array $discountData): Bonus {
                    $maxAmount = null;
                    if ($discountData['maxAmount'] !== null && \is_numeric($discountData['maxAmount']) === true) {
                        $maxAmount = Money::fromVariable($discountData['maxAmount']);
                    }
                    switch ($discountData['type']) {
                        case PercentageBonus::ID:
                            return new PercentageBonus($discountData['percentage'], $maxAmount);
                        case FixedBonus::ID:
                            return new FixedBonus(Money::fromVariable($discountData['value']), $maxAmount);
                        default:
                            throw new \Exception('Unsupported discount type ' . $discountData['type']);
                    }
                }, $data['discounts']));
            }
        }

        if (isset($data['rule'])) {
            $rulesString = \is_array($data['rule']) ? self::denormalizeRule($data['rule']) : $data['rule'];

            $promotion->setRules($rulesString);
        }

        if (isset($data['active'])) {
            $promotion->setActive((bool) $data['active']);
        }

        if (isset($data['name'])) {
            $promotion->setName((string) $data['name']);
        }

        return $promotion;
    }

    /** Throw an exception if we try to update a marketplace promotion with an unauthorized rule */
    public function assertRuleTypeCanBeUpdated(array $data, string $class): self
    {
        if (\is_array($data['rule']) && \array_key_exists('rule', $data) && $class === MarketplacePromotion::class) {
            $ruleValue = [
                'and',
                'or',
                'basket_has_product_in_list',
                'basket_price_superior_to',
                'basket_price_inferior_to',
                'basket_price_superior_or_equal_to',
                'basket_price_inferior_or_equal_to',
                'basket_quantity_superior_to',
                'basket_quantity_inferior_to',
                'basket_quantity_superior_or_equal_to',
                'basket_quantity_inferior_or_equal_to',

            ];
            foreach ($data['rule'] as $type) {
                if (\in_array($type, $ruleValue)) {
                    throw new BadRequestHttpException('Rule type cannot be updated.');
                }
            }
        }

        return $this;
    }

    public function assertDiscountsCanBeUpdated(array $promotion, array $normalizedExistingPromotion, string $class): self
    {
        if (\is_array($normalizedExistingPromotion['discounts'][0])) {
            $normalizedExistingPromotionDiscount = $normalizedExistingPromotion['discounts'][0];
        } else {
            $normalizedExistingPromotionDiscount = $normalizedExistingPromotion['discounts'];
        }

        if (\is_array($promotion['discounts'])
            && array_diff_assoc($promotion['discounts'][0], $normalizedExistingPromotionDiscount)
            && $class === MarketplacePromotion::class
        ) {
            throw new BadRequestHttpException('Cannot update marketplace promotion discounts.');
        }

        return $this;
    }

    public function assertTargetCanBeUpdated(array $promotion, array $normalizedExistingPromotion, string $class): self
    {
        if (isset($promotion['target'])
            && $promotion['target']['type'] !== $normalizedExistingPromotion['target']['type']
            && $promotion['target']['type'] !== OrderAmountsCalculator::PROMOTION_PRODUCT_TARGET
            && $promotion['target']['type'] !== OrderAmountsCalculator::PROMOTION_PRODUCT_CATEGORY_TARGET
            && $class === MarketplacePromotion::class
        ) {
            throw new BadRequestHttpException('Cannot update marketplace promotion target.');
        }

        return $this;
    }

    public function assertPeriodCanBeUpdated(array $promotion, array $normalizedExistingPromotion, string $class): self
    {
        if (isset($promotion['period'])
            && array_diff_assoc($promotion['period'], $normalizedExistingPromotion['period'])
            && $class === MarketplacePromotion::class
        ) {
            throw new BadRequestHttpException('Cannot update marketplace promotion period.');
        }

        return $this;
    }

    public function assertCouponCanBeUpdated(array $promotion, array $normalizedExistingPromotion, string $class): self
    {
        if (isset($promotion['coupon'])
            && $promotion['coupon'] !== $normalizedExistingPromotion['coupon']
            && $class === MarketplacePromotion::class
        ) {
            throw new BadRequestHttpException('Cannot update marketplace promotion coupon.');
        }

        return $this;
    }

    /** Check if name is a required field for a marketplace promotion */
    public function assertName(array $data, string $class): self
    {
        if (\array_key_exists('name', $data) === false && $class === MarketplacePromotion::class) {
            throw new BadRequestHttpException('Field name is required.');
        }

        return $this;
    }

    /** Check if period is a required field for a marketplace promotion */
    public function assertPeriod(array $data, string $class): self
    {
        if (\array_key_exists('period', $data) === false && $class === MarketplacePromotion::class) {
            throw new BadRequestHttpException('Field period is required.');
        }

        return $this;
    }

    public function isBasketPromotionCreate(array $context, string $class): bool
    {
        if (\is_null($context[static::CONTEXT_KEY_EXISTING_PROMOTION]) && $class !== MarketplacePromotion::class) {
            return true;
        }

        return false;
    }

    public function isBasketPromotionUpdate(array $context, string $class): bool
    {
        if ($context[static::CONTEXT_KEY_EXISTING_PROMOTION] && $class !== MarketplacePromotion::class) {
            return true;
        }

        return false;
    }

    public function isMarketplacePromotionCreate(array $context, string $class): bool
    {
        if (\is_null($context[static::CONTEXT_KEY_EXISTING_PROMOTION]) && $class === MarketplacePromotion::class) {
            return true;
        }

        return false;
    }

    /**
     * @inheritdoc
     */
    public function supportsDenormalization($data, $type, $format = null): bool
    {
        return $type === BasketPromotion::class || $type === MarketplacePromotion::class;
    }

    private static function normalizeRule(Operator $expression): array
    {
        switch ($expression->getName()) {
            case 'and':
                return [
                    'type' => BasketRuleType::AND()->getValue(),
                    'items' => self::flattenComposites(
                        BasketRuleType::AND(),
                        array_map([self::class, 'normalizeRule'], $expression->getArguments())
                    ),
                ];
            case 'or':
                return [
                    'type' => BasketRuleType::OR()->getValue(),
                    'items' => self::flattenComposites(
                        BasketRuleType::OR(),
                        array_map([self::class, 'normalizeRule'], $expression->getArguments())
                    ),
                ];
            case '>':
                $target = $expression->getArguments()[0];
                if ($target instanceof Context === false) {
                    throw new \Exception('Invalid rule');
                }

                $value = $expression->getArguments()[1];
                if ($value instanceof Scalar === false) {
                    throw new \Exception('Invalid rule');
                }

                if ($target->getId() === 'totalAmount') {
                    $type = BasketRuleType::BASKET_PRICE_SUPERIOR_TO();
                } elseif ($target->getId() === 'totalQuantity') {
                    $type = BasketRuleType::BASKET_QUANTITY_SUPERIOR_TO();
                } else {
                    throw new \Exception('Invalid rule');
                }

                return [
                    'type' => $type,
                    'value' => $value->getValue(),
                ];
            case '<':
                $target = $expression->getArguments()[0];
                switch ($target->getId()) {
                    case 'totalAmount':
                        $type = BasketRuleType::BASKET_PRICE_INFERIOR_TO();
                        break;
                    case 'totalQuantity':
                        $type = BasketRuleType::BASKET_QUANTITY_INFERIOR_TO();
                        break;
                    case 'usageCount':
                        $type = BasketRuleType::MAX_USAGE_COUNT();
                        break;
                    case 'userUsageCount':
                        $type = BasketRuleType::MAX_USAGE_COUNT_PER_USER();
                        break;
                    default:
                        throw new \Exception('Invalid rule');
                }

                $value = $expression->getArguments()[1];
                if ($value instanceof Scalar === false) {
                    throw new \Exception('Invalid rule');
                }

                return [
                    'type' => $type->getValue(),
                    'value' => $value->getValue(),
                ];
            case 'intersects':
                $target = $expression->getArguments()[0];
                if ($target instanceof Context === false || ($target->getId() !== 'products' && $target->getId() !== 'groups')) {
                    throw new \Exception('Invalid rule');
                }

                $values = $expression->getArguments()[1];
                if ($values instanceof RulerArray === false) {
                    throw new \Exception('Invalid rule');
                }
                if ($target->getId() === 'products') {
                    return [
                        'type' => BasketRuleType::BASKET_HAS_PRODUCT_IN_LIST()->getValue(),
                        'products_ids' => array_map(static function (Scalar $value) {
                            return $value->getValue();
                        }, $values->getArray()),
                    ];
                }
                if ($target->getId() === 'groups') {
                    return [
                        'type' => BasketRuleType::BASKET_HAS_GROUP_IN_LIST()->getValue(),
                        'groups_ids' => array_map(static function (Scalar $value) {
                            return $value->getValue();
                        }, $values->getArray()),
                    ];
                }
            case '>=':
                $target = $expression->getArguments()[0];
                if ($target instanceof Context === false) {
                    throw new \Exception('Invalid rule');
                }

                $value = $expression->getArguments()[1];
                if ($value instanceof Scalar === false) {
                    throw new \Exception('Invalid rule');
                }

                if ($target->getId() === 'totalAmount') {
                    $type = BasketRuleType::BASKET_PRICE_SUPERIOR_OR_EQUAL_TO();
                } elseif ($target->getId() === 'totalQuantity') {
                    $type = BasketRuleType::BASKET_QUANTITY_SUPERIOR_OR_EQUAL_TO();
                } else {
                    throw new \Exception('Invalid rule');
                }

                return [
                    'type' => $type,
                    'value' => $value->getValue(),
                ];
            case '<=':
                $target = $expression->getArguments()[0];
                if ($target instanceof Context === false) {
                    throw new \Exception('Invalid rule');
                }

                $value = $expression->getArguments()[1];
                if ($value instanceof Scalar === false) {
                    throw new \Exception('Invalid rule');
                }

                if ($target->getId() === 'totalAmount') {
                    $type = BasketRuleType::BASKET_PRICE_INFERIOR_OR_EQUAL_TO();
                } elseif ($target->getId() === 'totalQuantity') {
                    $type = BasketRuleType::BASKET_QUANTITY_INFERIOR_OR_EQUAL_TO();
                } else {
                    throw new \Exception('Invalid rule');
                }

                return [
                    'type' => $type,
                    'value' => $value->getValue(),
                ];
            default:
                throw new \Exception('Unsupported operator ' . $expression->getName());
        }
    }

    /**
     * Move up one level all items of the given rule type.
     * For example if you have an "OR" rule A as an item of an "OR" rule B, then A will be replaced by its own items.
     */
    private static function flattenComposites(BasketRuleType $ruleType, array $items): array
    {
        return array_reduce($items, static function (array $flattenedItems, array $item) use ($ruleType): array {
            if ($item['type'] === $ruleType->getValue()) {
                $flattenedItems = array_merge($flattenedItems, $item['items']);
            } else {
                $flattenedItems[] = $item;
            }

            return $flattenedItems;
        }, []);
    }

    private static function denormalizeRule(array $ruleData): string
    {
        $ruleType = new BasketRuleType($ruleData['type']);

        switch (true) {
            case $ruleType->equals(BasketRuleType::AND()):
                $operand = 'and';

                return self::denormalizeCompositeRule($operand, $ruleData['items']);
            case $ruleType->equals(BasketRuleType::OR()):
                $operand = 'or';

                return self::denormalizeCompositeRule($operand, $ruleData['items']);
            case $ruleType->equals(BasketRuleType::BASKET_HAS_PRODUCT_IN_LIST()):
                if (empty($ruleData['products_ids'])) {
                    throw new InvalidRules('missing products\' ids');
                }

                return '(products intersects [' . join(',', $ruleData['products_ids']) . '])';
            case $ruleType->equals(BasketRuleType::BASKET_HAS_GROUP_IN_LIST()):
                if (\array_key_exists('groups_ids', $ruleData) === false
                    || \is_array($ruleData['groups_ids']) === false
                    || \count($ruleData['groups_ids']) === 0
                ) {
                    throw new InvalidRules('missing groups\' ids');
                }

                return '(groups intersects [' . sprintf('"%s"', join('","', $ruleData['groups_ids'])) . '])';
            case $ruleType->equals(BasketRuleType::BASKET_PRICE_INFERIOR_TO()):
                return '(totalAmount < ' . $ruleData['value'] . ')';
            case $ruleType->equals(BasketRuleType::BASKET_PRICE_SUPERIOR_TO()):
                return '(totalAmount > ' . $ruleData['value'] . ')';
            case $ruleType->equals(BasketRuleType::BASKET_PRICE_SUPERIOR_OR_EQUAL_TO()):
                return '(totalAmount >= ' . $ruleData['value'] . ')';
            case $ruleType->equals(BasketRuleType::BASKET_PRICE_INFERIOR_OR_EQUAL_TO()):
                return '(totalAmount <= ' . $ruleData['value'] . ')';
            case $ruleType->equals(BasketRuleType::BASKET_QUANTITY_INFERIOR_TO()):
                return '(totalQuantity < ' . $ruleData['value'] . ')';
            case $ruleType->equals(BasketRuleType::BASKET_QUANTITY_SUPERIOR_TO()):
                return '(totalQuantity > ' . $ruleData['value'] . ')';
            case $ruleType->equals(BasketRuleType::BASKET_QUANTITY_SUPERIOR_OR_EQUAL_TO()):
                return '(totalQuantity >= ' . $ruleData['value'] . ')';
            case $ruleType->equals(BasketRuleType::BASKET_QUANTITY_INFERIOR_OR_EQUAL_TO()):
                return '(totalQuantity <= ' . $ruleData['value'] . ')';
            case $ruleType->equals(BasketRuleType::MAX_USAGE_COUNT()):
                return '(usageCount < ' . $ruleData['value'] . ')';
            case $ruleType->equals(BasketRuleType::MAX_USAGE_COUNT_PER_USER()):
                return '(userUsageCount < ' . $ruleData['value'] . ')';
            default:
                throw new InvalidRules('unsupported rule type ' . $ruleType->getValue());
        }
    }

    private static function denormalizeCompositeRule(string $operand, array $itemsData): string
    {
        $itemsRulesStrings = array_map([self::class, 'denormalizeRule'], $itemsData);

        return '(' . join(' ' . $operand . ' ', $itemsRulesStrings) . ')';
    }
}
