<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\Promotion;

use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Wizacha\Marketplace\Group\UserGroupService;

class RulesUserGroupValidator
{
    private UserGroupService $userGroupService;

    public function __construct(UserGroupService $userGroupService)
    {
        $this->userGroupService = $userGroupService;
    }

    /**
     * @param string[] $groupsIds [uuid1,uuid2,...]
     */
    public function assertRulesUsersGroupsIdsExists(array $groupsIds): void
    {
        if (\count($groupsIds) === 0) {
            throw new BadRequestHttpException('Rule "' . BasketRuleType::BASKET_HAS_GROUP_IN_LIST()->getValue() . '" is empty.');
        }

        $userGroups = $this->userGroupService->getByIds($groupsIds);

        if (\count($userGroups) !== \count($groupsIds)) {
            throw new BadRequestHttpException('Rule "' . BasketRuleType::BASKET_HAS_GROUP_IN_LIST()->getValue() . '" contains invalid groups_ids values.');
        }
    }
}
