<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\Exception;

use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class MissingFieldsHttpException extends BadRequestHttpException
{
    /**
     * @var string[]
     */
    public $fields;

    public function __construct(string ...$fields)
    {
        parent::__construct('Missing required fields');

        $this->fields = $fields;
    }
}
