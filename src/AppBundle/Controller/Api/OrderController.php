<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Wizacha\AppBundle\Controller\Api\Error\ApiErrorException;
use Wizacha\AppBundle\Controller\Api\Pagination\PaginationHttpHeaders;
use Wizacha\AppBundle\Controller\DomainUserTrait;
use Wizacha\AppBundle\Security\User\ApiSecurityUser;
use Wizacha\AppBundle\Service\TreatmentArrayService;
use Wizacha\Bridge\Symfony\Response\AccessDeniedJsonResponse;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Bridge\Symfony\Response\NotFoundJsonResponse;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\Exception\InvoiceNotFound;
use Wizacha\Marketplace\Exception\MarketplaceExceptionInterface;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Order\Action\MarkAsDelivered;
use Wizacha\Marketplace\Order\Exception\OrderNotFound;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderAttachment\Enum\DownloadAttachmentApiRouteKey;
use Wizacha\Marketplace\Order\OrderAttachment\Service\OrderAttachmentService;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\Organisation\Administrator;
use Wizacha\Marketplace\Subscription\SubscriptionRepository;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionStatus;
use Wizacha\Marketplace\User\WizaplaceUserInterface;
use Wizacha\Search\SortingOrder;

use function Wizacha\Marketplace\Order\is_order_status_equal_to;

class OrderController extends Controller
{
    use DomainUserTrait;

    private const ORDER_SERVICE = 'marketplace.order.order_service';

    protected const LIMIT = 100;
    protected const START = 0;

    /** @var SubscriptionRepository */
    protected $subscriptionRepository;

    /** @var int */
    protected $paymentDeadline;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /** @var TransactionService  */
    private $transactionService;

    private OrderService $orderService;

    private OrderAttachmentService $orderAttachmentService;

    /** @var MarkAsDelivered */
    private $markAsDelivredSerice;

    public function __construct(
        SubscriptionRepository $subscriptionRepository,
        ?string $featurePaymentDeadline,
        LoggerInterface $logger,
        TransactionService $transactionService,
        OrderService $orderService,
        MarkAsDelivered $markAsDelivered,
        OrderAttachmentService $orderAttachmentService
    ) {
        $this->subscriptionRepository = $subscriptionRepository;
        $this->logger = $logger;
        $this->transactionService = $transactionService;
        $this->orderService = $orderService;
        $this->markAsDelivredSerice = $markAsDelivered;
        $this->orderAttachmentService = $orderAttachmentService;

        if (\is_numeric($featurePaymentDeadline) === true) {
            $this->paymentDeadline = (int) $featurePaymentDeadline;
        } else {
            $this->paymentDeadline = 0;
        }
    }

    public function listAction(Request $request): JsonResponse
    {
        $start = $request->query->getInt('start', self::START);
        if ($start < 0) {
            $start = self::START;
        }

        $limit = $request->query->getInt('limit', self::LIMIT);
        if ($limit < 0) {
            $limit = self::LIMIT;
        }

        $nbTotalOrders = $this->get(static::ORDER_SERVICE)->getCountUserOrders(
            $this->getUser()->getId(),
            $request->query->has('transaction_reference') ? $request->query->get('transaction_reference') : ''
        );

        $orders = $this->get(static::ORDER_SERVICE)->getUserOrders(
            $this->getUser()->getId(),
            $request->query->has('transaction_reference') ? $request->query->get('transaction_reference') : '',
            SortingOrder::parseSort($request->query->get('sort', 'id:desc')),
            $start,
            (0 == $limit) ? $nbTotalOrders : $limit
        );

        $exposedOrders = array_map(static function (Order $order) {
            return $order->expose();
        }, array_filter($orders, static function (Order $order) {
            return !is_order_status_equal_to($order->getId(), OrderStatus::INCOMPLETED);
        }));

        return new JsonResponse(
            $exposedOrders,
            200,
            [
                PaginationHttpHeaders::LIMIT => $limit,
                PaginationHttpHeaders::OFFSET => $start,
                PaginationHttpHeaders::TOTAL => $nbTotalOrders,
            ]
        );
    }

    public function getAction(int $id): JsonResponse
    {
        $orderService = $this->get(static::ORDER_SERVICE);

        // Get order's details
        $order = $orderService->getOrderAndCheckStatus($id);

        // Check if user is the order owner ?
        if ($order->getUserId() !== $this->getUser()->getId()) {
            // 404 instead of 403 to avoid leaking information
            throw new OrderNotFound($id);
        }

        $result = $order->expose();

         // Order attachments
        $orderAttachment = $this->orderAttachmentService->list($id, []);
        $result['attachments'] = $this->orderAttachmentService->summaryAttachment($orderAttachment, DownloadAttachmentApiRouteKey::USER_ROUTE_DOWNLOAD());

        return new JsonResponse($result);
    }

    public function getShipmentsAction(int $id): Response
    {
        $orderService = $this->get(static::ORDER_SERVICE);
        $order = $orderService->getOrderAndCheckStatus($id);

        // Check if user is the order owner.
        if ($order->getUserId() !== $this->getUser()->getId()) {
            // 404 instead of 403 to avoid leaking information.
            throw new OrderNotFound($id);
        }

        $shipments = $orderService->getOrderShipments($id);

        return new JsonResponse($shipments);
    }

    public function getShipmentsByOrderIdAction(int $id): Response
    {
        $order = $this->orderService->getOrder($id);

        try {
            $this->assertCanAccessOrder($order);
        } catch (Forbidden $exception) {
            return new AccessDeniedJsonResponse('You must have Admin Role or be the vendor of this order');
        }

        $shipments = $this->orderService->getOrderShipments($id);

        return new JsonResponse($shipments);
    }

    /**
     * @param int $id
     * @param bool $adminRoute Permet de déterminer la route empruntée afin que l'admin n'ai pas accès à la route /user
     *
     * @throws OrderNotFound
     * @throws NotFound
     *
     * @return Response le pdf est envoyé dans la response
     */
    private function getPDFInvoice(int $id, bool $adminRoute, $onlyInvoice = false): Response
    {
        $order = $this->get(static::ORDER_SERVICE)->getOrder($id);

        // If we only want invoices, throw an exception if order is not treated as one
        if (true === $onlyInvoice && false === $order->shouldShowAsInvoice()) {
            throw new InvoiceNotFound($id);
        }

        list($vendors,) = fn_get_users(['company_id' => $order->getCompanyId()], $auth = []);

        $allowedUserIds = array_map(function (array $vendorData): int {
            // the administrators of the company who sold the order
            return (int) $vendorData['user_id'];
        }, $vendors);
        if ($this->isGranted('ROLE_ADMIN') && true === $adminRoute) {
            // The admin of the marketplace.
            $allowedUserIds[] = $this->getUser()->getId();
        } else {
            // The customer who placed the order.
            $allowedUserIds[] = $order->getUserId();
        }

        $user = $this->getDomainUser($order->getUserId());

        if ($user->belongsToAnOrganisation()) {
            $allowedUserIds = array_merge(
                $allowedUserIds,
                array_map(function (Administrator $administrator) {
                    return (int) $administrator->getUser()->getUserId();
                }, $user->getOrganisation()->getAdministrators())
            );
        }

        if (!\in_array($this->getUser()->getId(), $allowedUserIds, true) || is_order_status_equal_to($id, OrderStatus::INCOMPLETED)) {
            // 404 instead of 403 to avoid leaking information
            throw new OrderNotFound($id);
        }

        return fn_print_order_invoices([$id], true, 'C');
    }

    /**
     * @param int $id
     *
     * @throws OrderNotFound
     * @throws NotFound
     *
     * @return Response le pdf est envoyé dans la response
     */
    public function getUserPDFInvoiceAction(Request $request, int $id): Response
    {
        if (true === $request->query->has('renderInvoiceOnly')) {
            $onlyInvoice = \filter_var($request->query->get('renderInvoiceOnly'), FILTER_VALIDATE_BOOLEAN);
        }

        return $this->getPDFInvoice($id, false, $onlyInvoice);
    }

    /**
     * @param int $id
     *
     * @throws OrderNotFound
     * @throws NotFound
     *
     * @return Response le pdf est envoyé dans la response
     */
    public function getAdminPDFInvoiceAction(Request $request, int $id): Response
    {
        if (true === $request->query->has('renderInvoiceOnly')) {
            $onlyInvoice = \filter_var($request->query->get('renderInvoiceOnly'), FILTER_VALIDATE_BOOLEAN);
        }

        return $this->getPDFInvoice($id, true, $onlyInvoice);
    }

    /**
     * Fonction servant à pouvoir afficher le workflow complet d'une commande,
     * à des fins de débugage, pour l'instant.
     * Mais cette fonction donne aussi un apperçu de ce qui sera fait pour
     * transmettre les informations complètes d'un workflow à l'affichage
     * des fronts.
     */
    public function getProgressAction(int $id)
    {
        $order = $this->container->get(static::ORDER_SERVICE)->getOrder($id);
        $workflowUnfolding = $this->container->get('marketplace.order.workflow_service')->getWorkflowUnfoldingFor($order);

        if ($workflowUnfolding->getOrder()->getUserId() !== $this->getUser()->getId()) {
            // 404 instead of 403 to avoid leaking information
            throw new OrderNotFound($id);
        }

        return new JsonResponse($workflowUnfolding->expose());
    }

    public function commitToAction(Request $request, int $id): JsonResponse
    {
        $order = $this->container->get(static::ORDER_SERVICE)->getOrder($id);

        if ($order->getUserId() !== $this->getUser()->getId()  && $this->isGranted('ROLE_ADMIN') === false) {
            // 404 instead of 403 to avoid leaking information
            throw new OrderNotFound($id);
        }

        if ($this->paymentDeadline > 0) {
            return new BadRequestJsonResponse('Order can\'t be committed');
        }

        try {
            $resolver = new OptionsResolver();
            $resolver->setRequired(['date', 'number']);
            $resolver->setAllowedTypes('number', 'string');
            $resolver->setAllowedTypes('date', 'string');
            $resolver->setAllowedValues('date', function (string $value): bool {
                try {
                    new \DateTimeImmutable($value);

                    return true;
                } catch (\Throwable $e) {
                    return false;
                }
            });
            $data = $resolver->resolve($request->request->all());
        } catch (\Throwable $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        $commitTo = $this->container->get('marketplace.order.action.commit_to');

        if (!$commitTo->isAllowed($order)) {
            return new BadRequestJsonResponse('Order can\'t be committed');
        }

        $commitTo->execute($order, new \DateTimeImmutable($data['date']), $data['number']);

        $user = fn_get_user_info($order->getUserId());
        $this->logger->info("le commitement de l'order " . $id . " a été faite par l’utilisateur : " . $user['lastname'] . " " . $user['firstname']);

        return new JsonResponse(null, JsonResponse::HTTP_NO_CONTENT);
    }

    public function getPaymentAction(int $id)
    {
        if (!$this->isGranted('ROLE_ADMIN')) {
            throw new Forbidden('Only admin can access to payment information');
        }

        $order = $this->container->get(static::ORDER_SERVICE)->getOrder($id);

        return new JsonResponse($order->getPayment()->expose());
    }

    public function getSubscriptionsAction(int $id): JsonResponse
    {
        $order = $this->container->get(static::ORDER_SERVICE)->getOrder($id);

        $this->assertCanAccessOrder($order);

        return new JsonResponse($this->subscriptionRepository->findByFirstOrderIds([$id]));
    }

    public function getUserSubscriptionsAction(int $id): JsonResponse
    {
        $order = $this->container->get(static::ORDER_SERVICE)->getOrder($id);

        $this->assertUserCanAccessOrder($order->getUserId(), $order->getId());

        return new JsonResponse($this->subscriptionRepository->findByFirstOrderIds([$id]));
    }

    protected function assertCompanyCanAccessOrder(int $targetCompanyId, int $orderId): void
    {
        $loggedUser = $this->getDomainUser($this->getUser()->getId());

        // An admin can access all users
        if ($this->isGranted('ROLE_ADMIN')) {
            return;
        }

        // A non-admin can only access its own account
        if ($loggedUser->getCompanyId() === $targetCompanyId) {
            return;
        }

        // 404 instead of 403 to avoid leaking information
        throw NotFound::fromId('Order', $orderId);
    }

    protected function assertUserCanAccessOrder(int $targetUserId, int $orderId): void
    {
        $loggedUser = $this->getDomainUser($this->getUser()->getId());

        // An admin can access all users
        if ($this->isGranted('ROLE_ADMIN')) {
            return;
        } elseif ($this->isGranted('ROLE_VENDOR')) {
            throw new Forbidden();
        }

        // A non-admin can only access its own account
        if ($loggedUser->getUserId() === $targetUserId) {
            return;
        }

        // 404 instead of 403 to avoid leaking information
        throw NotFound::fromId('Order', $orderId);
    }

    protected function assertCanAccessOrder(Order $order): self
    {
        if ($this->isGranted('ROLE_ADMIN')) {
            return $this;
        } elseif ($this->isGranted('ROLE_VENDOR')) {
            $this->assertCompanyCanAccessOrder($order->getCompanyId(), $order->getId());

            return $this;
        } elseif ($this->isGranted('ROLE_USER')) {
            throw new Forbidden();
        }

        throw new \RuntimeException("Unknown role.");
    }

    private function assertMarkOrderAsDeliveredPermission(Order $order): bool
    {
        if ($this->isGranted('ROLE_ADMIN') === true
            || $this->getUser()->getId() === $order->getUserId()
            || ($this->isGranted('ROLE_VENDOR') === true
            && $this->getUser()->getCompanyId() === $order->getCompanyId())
        ) {
            return true;
        }

        return false;
    }

    public function assertCanUserAddExtra(WizaplaceUserInterface $user, Order $order): bool
    {
        if ($order->getUserId() !== $user->getId()) {
            // Throw 404 to avoid leaking information
            throw new OrderNotFound($order->getId());
        }

        return true;
    }

    public function postCancelAction(Request $request, int $orderId): Response
    {
        if ($this->isGranted('ROLE_ADMIN') === false) {
            throw new Forbidden('You are not allowed to perform this action');
        }

        $message = $request->request->get('message');

        try {
            $this
                ->container
                ->get('marketplace.order.order_service')
                ->cancelOrder($orderId, $message)
            ;
        } catch (MarketplaceExceptionInterface $e) {
            throw new ApiErrorException($e);
        }

        return new Response();
    }

    public function putMarkAsPaidAction(int $orderId): JsonResponse
    {
        if ($this->isGranted('ROLE_ADMIN') === false) {
            return new JsonResponse(['message' => 'You are not allowed to perform this action'], Response::HTTP_UNAUTHORIZED);
        }
        $order = $this->get(static::ORDER_SERVICE)->getOrder($orderId);
        $markAsPaid = $this->get('marketplace.order.action.mark_as_paid');
        if ($markAsPaid->isAllowed($order) === true
            && $order->isPaid() === false
            && $order->getStatus()->equals(OrderStatus::STANDBY_BILLING()) === true
        ) {
            $markAsPaid->execute($order);
            $this->get(static::ORDER_SERVICE)->overrideOrderStatus($order);

            foreach ($this->transactionService->findByOrderId($orderId) ?? [] as $transaction) {
                if ($this->transactionService->assertTransactionCanBeMarkAsPaid($transaction) === true) {
                    $this->transactionService->updateTransactionStatus($transaction, TransactionStatus::SUCCESS());
                }
            }

            return new JsonResponse(['message' => 'The order has been paid']);
        }

        return new JsonResponse(['message' => 'The order cannot be marked as paid or has already been paid']);
    }

    public function setInvoiceNumberAction(Request $request, int $orderId): JsonResponse
    {
        try {
            $order = $this->orderService->getOrder($orderId);
        } catch (NotFound $exception) {
            return new NotFoundJsonResponse($exception->getMessage());
        }

        try {
            $this->assertCanAccessOrder($order);
        } catch (NotFound | Forbidden $exception) {
            return new AccessDeniedJsonResponse();
        }

        if ($request->request->has('invoice_number') === false) {
            return BadRequestJsonResponse::missingField('invoice_number');
        }

        if ($order->isAcceptedByVendor() !== true) {
            return new BadRequestJsonResponse('order must be approved.');
        }

        if ($order->doNotCreateInvoiceNumber() === true) {
            return new BadRequestJsonResponse('you cannot set invoice_number if do_not_create_invoice_number is true.');
        }

        $this->orderService->setInvoiceNumber($orderId, $request->request->get('invoice_number'));

        return new JsonResponse('', Response::HTTP_OK);
    }

    public function patchExtraAction(Request $request, int $orderId): JsonResponse
    {
        try {
            $order = $this->orderService->getOrder($orderId);
        } catch (NotFound $exception) {
            return new NotFoundJsonResponse($exception->getMessage());
        }

        try {
            $this->assertCanAccessOrder($order);
        } catch (NotFound | Forbidden $exception) {
            return new AccessDeniedJsonResponse();
        }

        if ($request->request->has('extra') === false) {
            return BadRequestJsonResponse::missingField('extra');
        }

        if (\is_array($request->request->get('extra')) === false) {
            return BadRequestJsonResponse::invalidField('extra', 'extra must be an array.');
        }

        $newExtra = $request->request->get('extra');

        $newExtra = TreatmentArrayService::removingEmptyField($newExtra);

        $oldExtra = $order->getOrderExtra();
        if ($newExtra != $oldExtra) {
            $diffKeysExtra = \array_diff_key($oldExtra, $newExtra);
            \array_walk(
                $diffKeysExtra,
                function ($value, $key) use (&$newExtra, $oldExtra) {
                    $newExtra[$key] = $oldExtra[$key];
                }
            );
        }
        $this->orderService->setExtraData($orderId, \json_encode($newExtra));

        return new JsonResponse('', Response::HTTP_OK);
    }

    public function postExtraAction(Request $request, int $orderId): JsonResponse
    {
        /** @var ApiSecurityUser $user */
        $user = $this->getUser();

        $order = $this->orderService->getOrder($orderId);

        try {
            $this->assertCanUserAddExtra($user, $order);
        } catch (OrderNotFound $e) {
            return new AccessDeniedJsonResponse();
        }

        try {
            $payload = (new OptionsResolver())
                ->setRequired('extra')
                ->setAllowedTypes('extra', 'array')
                ->resolve($request->request->all())
            ;
        } catch (\InvalidArgumentException $e) {
            return new BadRequestJsonResponse('Invalid request: extra must be an array.');
        }

        $newExtra = $payload['extra'];
        $newExtra = TreatmentArrayService::removingEmptyField($newExtra);
        $oldExtra = $order->getOrderExtra();

        /**
         * Dans le cas où la clé de valeur existe déjà, la requête n’est pas prise en compte.
         * La valeur existante est conservée
         */
        if (\count(\array_intersect_key($newExtra, $oldExtra)) > 0) {
            return $this->json('Extra key data already exist.', Response::HTTP_CONFLICT);
        }

        $this->orderService->setExtraData($orderId, \json_encode(\array_merge($oldExtra, $newExtra)));

        return $this->json('Extra data are updated.', Response::HTTP_OK);
    }

    public function putMarkAsdDeliveredAction(int $orderId): JsonResponse
    {
        try {
            $order = $this->orderService->getOrder($orderId);
        } catch (NotFound $exception) {
            return new NotFoundJsonResponse($exception->getMessage());
        }

        try {
            $this->assertCanAccessOrder($order);
        } catch (NotFound | Forbidden $exception) {
            return new AccessDeniedJsonResponse();
        }

        if ($order->hasDelivery() === true) {
            return new JsonResponse(['message' => 'The order has already been delivered']);
        }

        if ($this->markAsDelivredSerice->isAllowed($order) === true) {
            $this->markAsDelivredSerice->execute($order);
        } else {
            return new JsonResponse(['message' => 'The order cannot be marked as delivered']);
        }

        return new JsonResponse(['message' => 'The order has been delivered']);
    }

    public function postDispatchFundsAction(int $orderId): JsonResponse
    {
        try {
            $order = $this->orderService->getOrder($orderId);
        } catch (NotFound $exception) {
            return new NotFoundJsonResponse($exception->getMessage());
        }

        if ($this->getUser() === null) {
            return new JsonResponse(['message' => 'Unauthorized access'], Response::HTTP_UNAUTHORIZED);
        }

        if ($this->isGranted('ROLE_ADMIN') === false) {
            return new JsonResponse(['message' => 'Insufficient permission (The logged user is not a marketplace admin)'], Response::HTTP_FORBIDDEN);
        }

        $endWithdrawalPeriod = container()->get('marketplace.order.action.end_withdrawal_period');
        $dispatchFunds = container()->get('marketplace.order.action.dispatch_funds');

        if ($endWithdrawalPeriod->isAllowed($order) === true) {
            $endWithdrawalPeriod->execute($order);
        }

        if ($dispatchFunds->isAllowed($order) === true) {
            $dispatchFunds->execute($order);
            fn_change_order_status($order->getId(), OrderStatus::COMPLETED);

            return new JsonResponse(['message' => 'The Dispatch has been processed.'], Response::HTTP_OK);
        }

        return new BadRequestJsonResponse('The Dispatch could not be processed.');
    }

    public function getOrderChildAction(int $orderId): JsonResponse
    {
        if ($this->isGranted('ROLE_ADMIN') === false) {
            throw new Forbidden('You must have Admin Role.');
        }

        try {
            $this->orderService->getOrderDataById($orderId);
        } catch (OrderNotFound $exception) {
            return new NotFoundJsonResponse($exception->getMessage());
        }

        try {
            $children = $this->orderService->getOrdersFromParentOrder($orderId);
            $children = array_map(function ($order) {
                return [
                    'order_id' => $order->getId(),
                    'status' => $order->getStatus()
                ];
            }, $children);
        } catch (OrderNotFound $exception) {
            $children = [];
        }

        return new JsonResponse($children, Response::HTTP_OK);
    }
}
