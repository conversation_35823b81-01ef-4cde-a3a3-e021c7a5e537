<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\Process\Exception\ProcessFailedException;
use Symfony\Component\Process\Process;

class SystemController extends Controller
{
    /**
     * Mise en place des données de la marketplace pour que le SDK puisse consommer l'API,
     * d'une manière consistante.
     *
     * Actions réalisées (aussi visible dans le Makefile):
     * - reset de la DB,
     * - application des migrations SQL,
     * - chargement des fixtures pour le SDK,
     * - copie de la base dans celle de test.
     */
    public function reloadDataForSDKAction(Request $request): Response
    {
        if (!$this->isSafeEnvironment($request)) {
            throw new AccessDeniedHttpException();
        }

        try {
            $output = $this->executeSystemCommand('make reload-data');
        } catch (\throwable $e) {
            return new Response($e->getMessage(), 500);
        }

        return new Response($output);
    }

    /**
     * Lance une commande système en :
     * - se plaçant dans le répertoire racine du projet Symfony,
     * - échappant correctement la commande.
     */
    protected function executeSystemCommand(string $systemCommand): string
    {
        $systemCommand = escapeshellcmd($systemCommand);

        $process = new Process($systemCommand, $this->get('kernel')->getProjectDir());
        $process->setTimeout(180);
        $process->run();

        if (!$process->isSuccessful()) {
            throw new ProcessFailedException($process);
        }

        return $process->getOutput();
    }

    /**
     * Vérification que l'environnement est sûre afin d'exécuter des commandes systemes :
     * - l'environnement de Symfony doit être 'dev' ou 'test',
     * - le host doit être reconnu.
     */
    protected function isSafeEnvironment(Request $request)
    {
        if (!\in_array($this->get('kernel')->getEnvironment(), ['test'], true)) {
            return false;
        }

        if (!\in_array($request->getHost(), ['wizaplace.test', 'localhost'], true)) {
            return false;
        }

        return true;
    }
}
