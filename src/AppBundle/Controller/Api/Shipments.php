<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api;

use Tygh\Api\Response;

class Shipments extends \Tygh\Api\Entities\Shipments
{
    public function index($id = 0, $params = array())
    {
        $return = parent::index($id, $params);

        if ($id) {
            $data = [&$return['data']];
        } else {
            $data = &$return['data'];
        }
        array_walk(
            $data,
            function (&$_data) {
                $_data = fn_w_object_filter($_data, 'shipment', 'api', 'display_objects');
                fn_w_fields_cast($_data);
                $_data = fn_w_convert_fields($_data, 'shipment', 'send', 'convert_fields_api');
            }
        );

        return $return;
    }

    public function create($params)
    {
        $data = array();
        $valid_params = true;
        $status = Response::STATUS_BAD_REQUEST;
        $params = fn_w_object_filter($params, 'shipment', 'api');
        $orderService = container()->get('marketplace.order.order_service');

        if (empty($params['order_id'])) {
            $data['message'] = __('api_need_order_id');
            $valid_params = false;
        } elseif (empty($params['tracking_number'])) {
            $data['message'] = __('api_need_tracking_number');
            $valid_params = false;
        } elseif (empty($params['products'])) {
            return [
                'status' => Response::STATUS_BAD_REQUEST,
                'data'   => ['message' => __('api_need_order_products')]
            ];
        }

        if ($valid_params) {
            $order_info = $orderService->overrideLegacyOrder($params['order_id']);
            $params['shipping_id'] = $order_info['shipping'][0]['shipping_id'];
            $shipment_id = fn_update_shipment($params, 0, 0, false, ['C' => true]);

            if ($shipment_id) {
                \Wizacha\OrderStatus::automaticUpdate($params['order_id']);

                /**
                 * Avancement du workflow
                 */
                $order = $orderService->getOrder($params['order_id']);
                if ($order->areAllShipmentsSent()) {
                    $markAsShipped = container()->get('marketplace.order.action.mark_as_shipped');
                    if ($markAsShipped->isAllowed($order)) {
                        $markAsShipped->execute($order);
                    }
                }

                $status = Response::STATUS_CREATED;
                $data = ['shipment_id' => $shipment_id];
            }
        }

        return array(
            'status' => $status,
            'data' => $data
        );
    }

    public function privileges()
    {
        return array(
            'create' => true,
            'index'  => true,
        );
    }
}
