<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\Error;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Wizacha\Marketplace\Exception\ErrorCode;

class ErrorResponse extends JsonResponse
{
    public function __construct(
        ErrorCode $errorCode,
        string $errorMessage,
        array $context = [],
        int $httpStatusCode = Response::HTTP_BAD_REQUEST
    ) {
        parent::__construct([
            'error' => [
                'code' => $errorCode,
                'message' => $errorMessage,
                'context' => $context,
            ],
        ], $httpStatusCode);
    }
}
