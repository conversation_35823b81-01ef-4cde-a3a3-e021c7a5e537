<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\Error;

use Wizacha\Marketplace\Exception\ErrorCode;
use Wizacha\Marketplace\Exception\MarketplaceExceptionInterface;

class ApiErrorException extends \Exception implements ApiErrorResponseProvider
{
    protected MarketplaceExceptionInterface $mpException;

    public function __construct(MarketplaceExceptionInterface $exception)
    {
        $this->mpException = $exception;
    }

    public function toApiErrorResponse(): ErrorResponse
    {
        return new ErrorResponse(
            $this->mpException->getErrorCode(),
            $this->mpException->getMessage(),
            $this->mpException->getContext()
        );
    }
}
