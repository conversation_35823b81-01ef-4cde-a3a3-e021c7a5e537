<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Wizacha\AppBundle\Security\Exception\NoOAuthUserAlreadyExist;
use Wizacha\AppBundle\Security\Exception\UserDisabledException;
use Wizacha\AppBundle\Security\Exception\UserInvalidTokenException;
use Wizacha\AppBundle\Security\Exception\UserPendingException;
use Wizacha\Bridge\Symfony\Response\AccessDeniedJsonResponse;
use Wizacha\Component\OAuth\Token;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Exception\Unauthorized;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Bridge\Symfony\Response\NotFoundJsonResponse;
use Wizacha\Bridge\Symfony\Response\UnauthorizedJsonResponse;
use Symfony\Component\OptionsResolver\Exception\ExceptionInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;

class OAuthController extends Controller
{
    public function authorizeUrlAction(): Response
    {
        try {
            return new JsonResponse([
                'url' => (string) $this->get('marketplace.oauth.provider')->getAuthorizeUrl(),
            ]);
        } catch (\UnexpectedValueException $exception) {
            return new BadRequestJsonResponse($exception->getMessage());
        }
    }

    public function adminAuthorizeUrlAction(): Response
    {
        try {
            return new JsonResponse(
                [
                    'url' => (string) $this->get('marketplace.oauth.admin_provider')->getAuthorizeUrl(),
                ]
            );
        } catch (\UnexpectedValueException $exception) {
            return new BadRequestJsonResponse($exception->getMessage());
        }
    }

    public function tokenAction(Request $request): Response
    {
        //seulement un utilisateur anonyme a le droit de faire cet appel
        if ($this->isGranted('ROLE_USER')) {
            throw $this->createAccessDeniedException('Only unauthenticated user can authenticate via oauth');
        }

        $resolver = new OptionsResolver();
        $resolver->setRequired(['token']);
        $resolver->setAllowedTypes('token', 'string');

        try {
            $data = $resolver->resolve($request->request->all());
        } catch (ExceptionInterface $e) {
            return new BadRequestJsonResponse($e->getMessage(), []);
        }

        try {
            $user = $this->get('marketplace.oauth.provider')->authorize(new Token(
                $data['token']
            ));
        } catch (NoOAuthUserAlreadyExist $e) {
            return new JsonResponse([
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
            ], Response::HTTP_BAD_REQUEST);
        } catch (\UnexpectedValueException $exception) {
            return new BadRequestJsonResponse($exception->getMessage());
        } catch (\Throwable $e) {
            container()->get('logger')->error('OAuth authorization error', ['exception' => $e]);

            return new JsonResponse([
                'message' => 'Invalid token : ' . $e->getMessage(),
                'code' => UserInvalidTokenException::CODE,
            ], Response::HTTP_UNAUTHORIZED);
        }

        if ($user->isPending() === true) {
            return new JsonResponse([
                'message' => __(UserPendingException::MESSAGE_KEY),
                'code' => UserPendingException::CODE,
            ], Response::HTTP_UNAUTHORIZED);
        }

        if ($user->isEnabled() === false) {
            return new JsonResponse([
                'message' => __(UserDisabledException::MESSAGE_KEY),
                'code' => UserDisabledException::CODE,
            ], Response::HTTP_UNAUTHORIZED);
        }

        return new JsonResponse([
            'id' => $user->getUserId(),
            'apiKey' => $user->getApiKey(),
        ]);
    }

    public function logoutAction(): Response
    {
        try {
            if ($this->getUser() === null) {
                return new UnauthorizedJsonResponse();
            }
            return new JsonResponse(
                [
                    'logout_redirect_uri' => (string) $this->get('marketplace.oauth.provider')->logout(
                        new Token($this->getUser()->getPassword())
                    ),
                ]
            );
        } catch (NoOAuthUserAlreadyExist $e) {
            return new NotFoundJsonResponse('A non-oauth user already exists');
        } catch (\Throwable $e) {
            container()->get('logger')->error('OAuth loggout error', ['exception' => $e]);

            return new UnauthorizedJsonResponse('Loggout failed : ' . $e->getMessage());
        }
    }
}
