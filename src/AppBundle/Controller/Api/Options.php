<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api;

use Tygh\Api\Response;

class Options extends \Tygh\Api\AEntity
{

    /**
     * Handles REST GET request. Must return Api_Response with list of entities
     * or one entity data if id specified
     *
     * @param  mixed $id
     * @param  array $params
     * @return Response
     */
    public function index($id = '', $params = [])
    {
        if (is_numeric($id)) {
            $option_data = fn_get_product_option_data($id, 0);
            list($option_data) = $this->polish([$option_data]);
            return [
                'status' => empty($option_data) ? Response::STATUS_NOT_FOUND : Response::STATUS_OK,
                'data'   => $option_data,
            ];
        };

        $options = null;
        switch ($this->getParentName()) {
            case 'products':
                $product_data    = $this->getParentData();
                $product_options = fn_get_product_options([$product_data['product_id']]);
                $result          = $this->polish(reset($product_options));
                break;
            case 'categories':
                $category_data = $this->getParentData();
                $result        = $this->polish(
                    fn_w_get_category_options($category_data['category_id'])
                );
                break;
        }
        return [
            'status' => \is_null($result) ? Response::STATUS_NOT_FOUND : Response::STATUS_OK,
            'data'   => $result,
        ];
    }

    /**
     * Polish option data to output
     * @param array $options
     * @return array
     */
    private function polish(array $options)
    {
        $options = array_filter($options);
        foreach ($options as &$option) {
            $pending = $option['status'] != 'A';
            $displayOnFaceting = $option['display_on_faceting'];
            $option  = fn_w_object_filter($option, 'options', 'api', 'display_objects');
            fn_w_fields_cast($option);
            $option['pending'] = $pending;
            $option['display_on_faceting'] = (bool) $displayOnFaceting ;
        }

        return $options;
    }

    /**
     * Handles REST POST request. Must create resource and return Api_Response
     * with STATUS_CREATED on success.
     *
     * @param  array $params POST data
     * @return Response
     */
    public function create($params)
    {
        $bad_request = ['status' => Response::STATUS_BAD_REQUEST];
        if ($this->getParentName() != 'products') {
            return $bad_request;
        }
        $product_data = $this->getParentData();
        $variants     = array_map(
            function ($variant_name) {
                return ['variant_name' => $variant_name];
            },
            $params['variants']
        );
        $option_data  = [
            'option_name'  => $params['option_name'],
            'variants'     => $variants,
            'w_product_id' => $product_data['product_id'],
        ];
        $option_id    = fn_update_product_option($option_data);
        if (!$option_id) {
            return $bad_request;
        }
        return [
            'status' => Response::STATUS_CREATED,
            'data'   => ['option_id' => (int) $option_id],
        ];
    }

    /**
     * Handles REST PUT request. Must update resource and return Api_Response
     * with STATUS_OK on success.
     *
     * @param  int $id
     * @param  array $params POST data
     * @return Response
     */
    public function update($id, $params)
    {
        if (!fn_w_company_options_check([$id])) {
            return ['status' => Response::STATUS_FORBIDDEN];
        }
        if (!isset($params['add_new_variants']) || !\is_array($params['add_new_variants'])) {
            return ['status' => Response::STATUS_BAD_REQUEST];
        }

        if (true === \is_array(fn_get_system_option($id))) {
            return ['status' => Response::STATUS_FORBIDDEN];
        }

        fn_w_add_option_variants_by_name($id, $params['add_new_variants']);
        return ['status' => Response::STATUS_OK];
    }

    /**
     * Handles REST DELETE request. Must create resource and return Api_Response
     * with STATUS_NO_CONTENT on success.
     *
     * @param  int $id
     * @return Response
     */
    public function delete($id)
    {
        return ['status' => Response::STATUS_FORBIDDEN];
    }

    /**
     * Returns list of privileges wat can be enabled for user
     *
     * @return array List of entyties
     */
    public function privileges()
    {
        return [
            'index'  => true,
            'create' => true,
            'update' => true,
        ];
    }
}
