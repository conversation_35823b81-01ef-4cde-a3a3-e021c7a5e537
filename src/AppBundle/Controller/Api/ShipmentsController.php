<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Wizacha\Bridge\Symfony\Response\AccessDeniedJsonResponse;
use Wizacha\Bridge\Symfony\Response\NotFoundJsonResponse;
use Wizacha\Marketplace\Basket\Exception\ShipmentNotFound;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Order\Action\MarkAsDelivered;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\ShipmentService;

class ShipmentsController extends Controller
{
    /** @var OrderService */
    private $orderService;

    /** @var MarkAsDelivered */
    private $markAsDelivered;

    /** @var ShipmentService */
    private $shipmentService;

    public function __construct(
        OrderService $orderService,
        MarkAsDelivered $markAsDelivered,
        ShipmentService $shipmentService
    ) {
        $this->orderService = $orderService;
        $this->markAsDelivered = $markAsDelivered;
        $this->shipmentService = $shipmentService;
    }

    public function putMarkShipmentAsDeliveredAction(int $shipmentId): JsonResponse
    {
        try {
            $shipment = $this->orderService->getShipment($shipmentId);
        } catch (NotFound $e) {
            return new NotFoundJsonResponse($e->getMessage());
        }
        $order = $shipment->getOrder();

        if ($this->assertMarkShipmentsAsDeliveredPermission($order) === false) {
            return new AccessDeniedJsonResponse();
        }

        if ($shipment->getDeliveredDate() !== null) {
            return new JsonResponse(['message' => 'This shipment has already been delivered']);
        }

        $this->shipmentService->updateShipmentDeliveryDate($shipmentId);

        /**
         * Lorsque l’ensemble des shipments d’une commande ont été marqués comme livrés, alors la commande avance
         * à l'étape suivante du workflow de commande indépendamment de l'écoulement de la delivery_period
         *
         */
        $orderId = $order->getId();
        if ($this->orderService->areAllShipmentsDelivered($orderId) === true
            && $this->markAsDelivered->isAllowed($order) === true
        ) {
            $this->markAsDelivered->execute($order);
        }

        return new JsonResponse(['message' => 'The shipment has been delivered']);
    }

    private function assertMarkShipmentsAsDeliveredPermission(Order $order): bool
    {
        if ($this->isGranted('ROLE_ADMIN') === true
            || $this->getUser()->getId() === $order->getUserId()
            || ($this->isGranted('ROLE_VENDOR') === true
            && $this->getUser()->getCompanyId() === $order->getCompanyId())
        ) {
            return true;
        }

        return false;
    }
}
