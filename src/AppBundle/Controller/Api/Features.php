<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api;

use Tygh\Api\Response;
use Wizacha\Category;
use Wizacha\Marketplace\GlobalState\GlobalState;

class Features extends \Tygh\Api\Entities\Features
{
    public function index($id = 0, $params = array())
    {
        //Impossible to retrieve ALL features
        $parentName = $this->getParentName();
        if (!$id && !\in_array($parentName, ['categories', 'products'])) {
            return ['status' => Response::STATUS_BAD_REQUEST];
        }

        //Filter input params
        $params = [
            'exclude_group' => true,
            'variants'      => true,
            'all_variants'  => true,
        ];
        if ($id) {
            //For consistency of results, we do not use $id directly
            $params['feature_id'] = $id;
        }
        if ($parentName == 'categories') {
            $category = new Category($this->getParentData()['category_id']);
            $params['category_ids'] = $category->getPathIds();
        }
        $return = parent::index('', $params);

        if ($id) {
            if (empty($return['data'])) {
                return ['status' => Response::STATUS_NOT_FOUND];
            }
            $return['data'] = array_shift($return['data']);
            $data[] = &$return['data'];
        } else {
            $data = &$return['data'];
        }

        if ('products' == $parentName) {
            foreach ($data as &$_data) {
                //Filter only selected values (for features using variants)
                if (isset($_data['variants'])) {
                    $_data['variants'] = array_filter(
                        $_data['variants'],
                        function ($v) {
                            return isset($v['selected']);
                        }
                    );
                } else {
                    //Unify output value for others
                    if ($_data['feature_type'] == 'O') {
                        $_data['value_str'] = $_data['value_int'];
                    } elseif ($_data['feature_type'] == 'D') {
                        $_data['value_str'] = \intval($_data['value_int']);
                    } else {
                        $_data['value_str'] = $_data['value'];
                    }
                }
            }
        }

        array_walk(
            $data,
            function (&$_data) {
                fn_w_fields_cast($_data);
                $_data = fn_w_object_filter($_data, 'features', 'api', 'display_objects');
                $_data = fn_w_convert_fields($_data, 'features', 'send', 'convert_fields_api');
            }
        );

        return $return;
    }

    public function create($params)
    {
        return [
            'status' => Response::STATUS_FORBIDDEN,
            'data' => []
        ];
    }

    public function update($id, $params)
    {
        $status = Response::STATUS_BAD_REQUEST;
        $data = [];

        if (!empty($params['add_new_variants'])) {
            if ($id != fn_w_get_brand_id()) {
                return [
                    'status' => Response::STATUS_FORBIDDEN,
                    'data' => []
                ];
            }
            list($existing_variants) = fn_get_product_feature_variants(array('feature_id' => $id));
            $existing_variant_names = array_column($existing_variants, 'variant');

            $new_variants = array_unique(array_diff(
                $params['add_new_variants'],
                $existing_variant_names
            ));

            foreach ($new_variants as $variant) {
                fn_add_feature_variant($id, ['variant' => $variant]);
            }

            $status = Response::STATUS_OK;
            $data = ['feature_id' => $id];
        }

        if ('products' == $this->getParentName()) {
            if (\array_key_exists('value_str', $params)) {
                $feature_value = $params['value_str'];
            } elseif (\array_key_exists('variant_id', $params)) {
                $feature_value = $params['variant_id'];
            } elseif (\array_key_exists('variant_ids', $params) && \is_array($params['variant_ids'])) {
                $feature_value = $params['variant_ids'];
            } else {
                return ['status' => $status, 'data' => $data,];
            }

            fn_update_product_features_value(
                $this->getParentData()['product_id'],
                [$id => $feature_value],
                [],
                (string) GlobalState::contentLocale()
            );

            container()->get('event_dispatcher')->dispatch(
                \Wizacha\Events\IterableEvent::fromElement($this->getParentData()['product_id']),
                \Wizacha\Product::EVENT_UPDATE
            );

            //Because function above return always true, and use some notifications...
            $n = fn_get_notifications();
            if (empty($n)) {
                $status = Response::STATUS_OK;
                $data = ['feature_id' => $id];
            }
        }

        fn_w_fields_cast($data);

        return [
            'status' => $status,
            'data'  => $data,
        ];
    }

    public function delete($id = 0, $params = array())
    {
        return [
            'status' => Response::STATUS_FORBIDDEN,
            'data' => []
        ];
    }
}
