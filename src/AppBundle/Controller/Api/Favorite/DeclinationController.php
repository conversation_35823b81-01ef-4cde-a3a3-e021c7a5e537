<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api\Favorite;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Wizacha\Marketplace\Entities\Declination;
use Wizacha\Marketplace\Favorite\UserDeclinationFavorite;
use Wizacha\Product;
use Wizacha\User;

class DeclinationController extends Controller
{
    public function listAllAction()
    {
        $favorites = $this->get('marketplace.favorite.favorite_service')->getFavoritesByUser($this->getUser());

        // Supprime les favoris qui n'ont pas de readmodel puisqu'on en a besoin
        // pour exposer les données
        // S'il y en a pas c'est que la déclinaison est inactive
        $withReadmodelFavorites = array_filter(
            $favorites,
            function (UserDeclinationFavorite $favorite): bool {
                return $favorite->hasReadmodel();
            }
        );
        // Expose les déclinaisons qui proviennent du readmodel
        $favorites = array_map(
            function (UserDeclinationFavorite $favorite) {
                return $favorite
                    ->getProductReadmodel()
                    ->getDeclination($favorite->getDeclinationId())
                    ->expose();
            },
            $withReadmodelFavorites
        );
        $favorites = \array_values($favorites);

        return new JsonResponse([
            'total' => \count($favorites),
            'count' => \count($favorites),
            '_embedded' => [
                'favorites' => $favorites,
            ],
        ]);
    }

    public function listAllIdsAction()
    {
        $favorites = $this->get('marketplace.favorite.favorite_service')->getFavoritesByUser($this->getUser());

        // Supprime les favoris qui n'ont pas de readmodel
        // S'il y en a pas c'est que la déclinaison est inactive
        $favorites = array_filter(
            $favorites,
            function (UserDeclinationFavorite $favorite): bool {
                return $favorite->hasReadmodel();
            }
        );

        $favorites = array_map(
            function (UserDeclinationFavorite $favorite) {
                return $favorite->getDeclinationId();
            },
            $favorites
        );
        $favorites = \array_values($favorites);

        return new JsonResponse([
            'total' => \count($favorites),
            'count' => \count($favorites),
            '_embedded' => [
                'favorites' => $favorites,
            ],
        ]);
    }

    public function addToFavoriteAction($id)
    {
        if (!$id) {
            throw new BadRequestHttpException('Declination id is invalid');
        }

        $declination = Declination::fromId($id);

        // If $id is a product id and the first declination is not active, we try to find an active declination
        if (is_numeric($id) && !$declination->isActive()) {
            $product = new Product($declination->getProductId());
            foreach ($product->getDeclinations() as $declination) {
                if ($declination->isActive()) {
                    break;
                }
            }
        }

        $this->get('marketplace.favorite.favorite_service')->addDeclinationToUserFavorites(
            $this->getUser(),
            $declination
        );

        return new JsonResponse('', Response::HTTP_CREATED);
    }

    public function removeFromFavoriteAction($id)
    {
        if (!$id) {
            throw new BadRequestHttpException('Declination id is invalid');
        }

        $this->get('marketplace.favorite.favorite_service')->removeDeclinationFromUserFavorites(
            $this->getUser(),
            Declination::fromId($id)
        );

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }

    /**
     * Favorite service expect a Wizacha\User, not a ApiSecurityUser
     */
    protected function getUser(): User
    {
        $user = parent::getUser();

        return new User($user->getId());
    }
}
