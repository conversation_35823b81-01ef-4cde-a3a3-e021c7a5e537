<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Translation\Dumper\XliffFileDumper;
use Wizacha\AppBundle\Service\TranslationService;

class TranslationController extends Controller
{
    /** @var TranslationService */
    private $translationService;

    public function __construct(TranslationService $translationService)
    {
        $this->translationService = $translationService;
    }

    public function getFrontTranslationsAction(Request $request, XliffFileDumper $dumper, string $locale): Response
    {
        // HTTP caching
        $response = new Response();
        $response->setLastModified($this->translationService->getLastUpdatedAt($locale));
        $response->setPublic();
        if ($response->isNotModified($request)) {
            // Client has the result in cache, so we return an empty response with status 304 Not Modified
            return $response;
        }

        $locale = self::getPrimaryLanguage($locale);

        $catalog = $this->translationService->loadFrontTranslations($locale);

        $response->setContent($dumper->formatCatalogue($catalog, 'messages'));
        $response->headers->set('Content-Type', 'application/x-xliff+xml');

        return $response;
    }

    public function setFrontTranslationsAction(Request $request, string $locale): Response
    {
        $locale = self::getPrimaryLanguage($locale);

        $catalog = $this->translationService->loadXliffString($request->getContent(), $locale);

        $this->translationService->setFrontTranslations($catalog, $locale);

        return new Response('', Response::HTTP_NO_CONTENT);
    }

    private static function getPrimaryLanguage(string $locale): string
    {
        $primaryLanguage = \locale_get_primary_language($locale);
        if (\is_null($primaryLanguage)) {
            throw new BadRequestHttpException("Invalid locale '{$locale}'");
        }

        return $primaryLanguage;
    }
}
