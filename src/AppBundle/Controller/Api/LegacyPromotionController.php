<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Wizacha\AppBundle\Security\User\ApiSecurityUser;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Promotion\Promotion;
use Wizacha\Marketplace\Promotion\PromotionSerializer;
use Wizacha\Marketplace\Promotion\PromotionService;
use Wizacha\Marketplace\Promotion\PromotionType;
use Wizacha\Marketplace\Promotion\Rule\Exception\InvalidRules;

/**
 * @deprecated this API was meant for internal usage in the BO only
 * @internal
 */
class LegacyPromotionController extends Controller
{
    /**
     * @var PromotionSerializer
     */
    private $serializer;

    /**
     * @var PromotionService
     */
    private $promotionService;

    public function __construct(PromotionSerializer $serializer, PromotionService $promotionService)
    {
        $this->serializer = $serializer;
        $this->promotionService = $promotionService;
    }

    public function getPromotionsAction(Request $request)
    {
        $type = $request->get('type');
        if ($type !== null) {
            if (PromotionType::isValid($type)) {
                $type = new PromotionType($type);
            } else {
                throw new BadRequestHttpException('Field "type" must be either "catalog", "basket" or null');
            }
        }

        $companyId = $this->getCompanyId($request);
        $promotions = $this->promotionService->findByCompany($companyId, $type);

        $response = array_map([$this->serializer, 'serialize'], $promotions);

        return new JsonResponse($response);
    }

    public function getPromotionAction($id, Request $request)
    {
        $promotion = $this->promotionService->get($id);
        $this->assertPromotionIsAccessibleByUser($promotion, $request);

        return new JsonResponse($this->serializer->serialize($promotion));
    }

    public function postPromotionAction(Request $request)
    {
        $data = json_decode($request->getContent(), true);
        // Prevent from providing an ID
        $data['id'] = null;
        $data['companyId'] = $this->getCompanyId($request);
        unset($data['id']);

        $promotion = $this->serializer->deserialize($data);
        try {
            $this->promotionService->save($promotion);
        } catch (InvalidRules $e) {
            return BadRequestJsonResponse::invalidField('rules', 'Invalid rules');
        }

        return new JsonResponse($this->serializer->serialize($promotion), Response::HTTP_CREATED);
    }

    public function putPromotionAction($id, Request $request)
    {
        $promotion = $this->promotionService->get($id);
        $this->assertPromotionIsAccessibleByUser($promotion, $request);

        $data = json_decode($request->getContent(), true);
        // Prevent from modifying these values
        $data['id'] = $id;
        $data['companyId'] = $promotion->getCompanyId();
        $data['type'] = $promotion->getType()->getValue();

        try {
            $this->promotionService->saveFromSerializedData($promotion, $data);
        } catch (InvalidRules $e) {
            return BadRequestJsonResponse::invalidField('rules', 'Invalid rules');
        }

        return new JsonResponse($this->serializer->serialize($promotion));
    }

    public function deletePromotionAction($id, Request $request)
    {
        try {
            $promotion = $this->promotionService->get($id);
        } catch (NotFound $e) {
            // DELETE should return 204 if there is no promotion to delete
            return new Response('', 204);
        }
        $this->assertPromotionIsAccessibleByUser($promotion, $request);

        $this->promotionService->delete($id);

        return new Response('', 204);
    }

    private function assertPromotionIsAccessibleByUser(Promotion $promotion, Request $request)
    {
        /** @var ApiSecurityUser $user */
        $companyId = $this->getCompanyId($request);

        if ($companyId && $promotion->getCompanyId() !== $companyId) {
            throw $this->createNotFoundException();
        }
    }

    /**
     * Admin can choose the companyId he use with the 'companyId' param in the request.
     */
    private function getCompanyId(Request $request): ?int
    {
        /** @var ApiSecurityUser $user */
        $user = $this->getUser();

        if ($user->getCompanyId()) {
            return $user->getCompanyId();
        }
        $companyId = $request->get('companyId', null);

        return $companyId === null ? null : (int) $companyId;
    }
}
