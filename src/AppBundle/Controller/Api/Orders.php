<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Tygh\Api\Response;
use Tygh\Registry;
use Wizacha\Marketplace\Subscription\SubscriptionService;
use Wizacha\Marketplace\Subscription\SubscriptionStatus;
use Wizacha\Marketplace\User\UserType;
use Wizacha\OrderStatus;

use function Wizacha\Marketplace\Order\is_order_status_equal_to;

class Orders extends \Tygh\Api\Entities\Orders
{
    public const COMPANY_ID_KEY = 'company_id';

    public function index($id = 0, $params = array())
    {
        try {
            $return = parent::index($id, $params);
        } catch (\InvalidArgumentException $e) {
            throw new BadRequestHttpException($e->getMessage());
        }

        if ($id) {
            $data = [&$return['data']];
        } else {
            $data = &$return['data'];
            // Remove do_not_create_invoice
            array_walk(
                $data,
                function (&$_data) {
                    unset($_data['do_not_create_invoice']);
                }
            );
        }

        // Add some No-legacy data
        array_walk(
            $data,
            function (&$_data) {
                $_data = fn_api_order_info($_data, $this->auth['user_type'] === UserType::ADMIN);
            }
        );

        return $return;
    }

    public function update($id, $params)
    {
        $container = container();
        $orderService =  $container->get('marketplace.order.order_service');

        $order = $orderService->overrideLegacyOrder($id);
        $needShipping = $order['need_shipping'];
        if ($order[static::COMPANY_ID_KEY] === null) {
            return [
                'status' => Response::STATUS_NOT_FOUND,
                'data' => [],
            ];
        }
        Registry::set('runtime.company_id', $order[static::COMPANY_ID_KEY]);

        if (!is_order_status_equal_to($id, \Wizacha\OrderStatus::STANDBY_VENDOR, \Wizacha\OrderStatus::STANDBY_SUPPLYING, \Wizacha\OrderStatus::PROCESSING_SHIPPING)) {
            return [
                'status' => Response::STATUS_BAD_REQUEST,
                'data' => [
                    'message' => 'order status does not allow this action'
                ],
            ];
        }

        $params = fn_w_convert_fields($params, 'order', 'get', 'convert_fields_api');
        $params = fn_w_object_filter($params, 'order', 'api');

        if (isset($params['approved'])) {
            if (!\is_bool($params['approved'])) {
                return [
                    'status' => Response::STATUS_BAD_REQUEST,
                    'data' => [
                        'message' => 'approved must be a boolean'
                    ],
                ];
            }
            if ($params['approved']) {
                $params['status'] = \Wizacha\OrderStatus::PROCESSING_SHIPPING;
            } else {
                $params['status'] = \Wizacha\OrderStatus::VENDOR_DECLINED;

                if (!empty($params['decline_reason'])) {
                    db_query("UPDATE ?:orders SET decline_reason = ?s WHERE order_id = ?i", $params['decline_reason'], $id);
                }
            }

            $schema = fn_get_permissions_schema('change_status');

            if (!isset($schema['orders'][$order['status']][$params['status']])) {
                return [
                    'status' => Response::STATUS_BAD_REQUEST,
                    'data' => [
                        'message' => 'order status does not allow this action'
                    ],
                ];
            }
        }
        $order = $orderService->getOrder($id);

        //We cannot have both params (do_not_create_invoice and w_invoice_number) in the request body
        if (\array_key_exists('do_not_create_invoice', $params) === true) {
            if (\is_bool($params['do_not_create_invoice']) === false) {
                return [
                    'status' => Response::STATUS_BAD_REQUEST,
                    'data' => ['do_not_create_invoice must be a boolean.'],
                ];
            } elseif ($params['do_not_create_invoice'] === true
                && \array_key_exists('w_invoice_number', $params) === true
            ) {
                return [
                    'status' => Response::STATUS_BAD_REQUEST,
                    'data' => ['do_not_create_invoice can\'t be true with an invoice_number.'],
                ];
            } elseif ($params['do_not_create_invoice'] === false
                && \array_key_exists('w_invoice_number', $params) === true
                && \strlen($params['w_invoice_number']) == 0
            ) {
                return [
                    'status' => Response::STATUS_BAD_REQUEST,
                    'data' => ['Invoice_number must be provided.'],
                ];
            }
        }

        /**
         * La génération automatique du numéro de facture nécessite que la feature soit activée au niveau de la marketplace
         * et que la fonctionnalité soit active pour le vendeur
         */
        $isAutomaticBillingFeatureEnabled
            = $container->getParameter('feature.activate_billing_number_auto_generation') === true
            && $order->getCompany()->hasAutomaticBillingNumber() === true;

        $generateAutomaticBillingNumber
            = \array_key_exists('create_automatic_billing_number', $params) === true
            && ((bool) $params['create_automatic_billing_number']) === true;

        $hasNewInvoiceNumber
            = \array_key_exists('w_invoice_number', $params) === true
            && $params['w_invoice_number'] !== '';

        $companyService = $container->get('marketplace.company_service');

        $hasNewDoNotCreateInvoiceFlag = false;
        //to get the real value of do_not_create_invoice if it exist in $param
        $doNotCreateInvoice = null;

        if (\array_key_exists('do_not_create_invoice', $params) === true) {
            $hasNewDoNotCreateInvoiceFlag = $doNotCreateInvoice = (bool) $params['do_not_create_invoice'] === true;
        }

        /**
         *  Dans le cas ou 'invoicing_disabled' est true pour une company
         *  On ne peut pas avoir $doNotCreateInvoice === false (s'il est présent dans requête sa valeur doit être false)
         *  On ne peut pas aussi avoir un numéro de facture
        */
        if (($hasNewInvoiceNumber === true || $doNotCreateInvoice === false) && $companyService->isInvoicingDisabled($order->getCompanyId()) === true) {
            return [
                'status' => Response::STATUS_BAD_REQUEST,
                'data' => [],
            ];
        }

        /**
         * Si la génération automatique du numéro de facture est actif, on empêche la saisie d'un numéro de facture manuellement
         * La génération automatique du numéro de facture nécessite que la feature soit activée au niveau de la marketplace
         */
        if ($generateAutomaticBillingNumber === true
            && ($isAutomaticBillingFeatureEnabled === false || $hasNewInvoiceNumber === true)
        ) {
            return [
                'status' => Response::STATUS_BAD_REQUEST,
                'data' => [],
            ];
        }

        //Dans le cas où on souhaite avoir un numéro de facture(manuellement ou automatiquement) et ne pas génerer un numéro de facture à la fois
        if (($hasNewInvoiceNumber === true || $generateAutomaticBillingNumber === true) && $hasNewDoNotCreateInvoiceFlag === true) {
            return [
                'status' => Response::STATUS_BAD_REQUEST,
                'data' => [],
            ];
        }

        $generatedBillingNumber = null;
        if ($generateAutomaticBillingNumber === true) {
            $generatedBillingNumber = $container->get('marketplace.order.billing_number_generator')->generate($id);
        }

        if ($hasNewInvoiceNumber === true) {
            db_query("UPDATE ?:orders SET w_invoice_number = ?s, do_not_create_invoice = 0, invoice_date = NOW() WHERE order_id = ?i", $params['w_invoice_number'], $id);
        } elseif ($generateAutomaticBillingNumber === true) {
            db_query("UPDATE ?:orders SET w_invoice_number = ?s, do_not_create_invoice = 0, invoice_date = NOW() WHERE order_id = ?i", $generatedBillingNumber, $id);
        } elseif ($hasNewDoNotCreateInvoiceFlag === true) {
            db_query("UPDATE ?:orders SET do_not_create_invoice = 1, w_invoice_number = null WHERE order_id = ?i", $id);
        } elseif ($doNotCreateInvoice === false) {
            db_query("UPDATE ?:orders SET do_not_create_invoice = 0 WHERE order_id = ?i", $id);
        }

        // Refresh object order after update
        $order = $container->get('marketplace.order.order_service')->getOrder($id);

        /**
         * Avancement du workflow
         */
        if (isset($params['approved']) && \is_bool($params['approved'])) {
            if ($params['approved']) {
                $validate = $container->get('marketplace.order.action.accept');
                if ($validate->isAllowed($order)) {
                    $validate->execute($order);
                }
            } else {
                $refuse = $container->get('marketplace.order.action.refuse');
                if ($refuse->isAllowed($order)) {
                    $refuse->execute($order);
                    $container->get(SubscriptionService::class)->updateStatus([$order], SubscriptionStatus::DISABLED());
                }
            }
        }

        // La vérification de la présence du numéro de facture est temporaire,
        // tant que le workflow n'a pas réellement la main : le feature flag
        // permettant de passer outre les contraintes des actions, permettrait
        // dans ce cas précis d'écrire un numéro de facture même s'il y en a
        // déjà un.
        if (!$order->hasInvoiceNumber()) {
            if ($hasNewInvoiceNumber) {
                $provideInvoiceNumber = $container->get('marketplace.order.action.provide_invoice_number');
                if ($provideInvoiceNumber->isAllowed($order)) {
                    $provideInvoiceNumber->execute($order, (string) $params['w_invoice_number']);
                }
            } elseif ($generateAutomaticBillingNumber) {
                $provideInvoiceNumber = $container->get('marketplace.order.action.provide_invoice_number');
                if ($provideInvoiceNumber->isAllowed($order)) {
                    $provideInvoiceNumber->execute($order, (string) $generatedBillingNumber);
                }
            } elseif ($hasNewDoNotCreateInvoiceFlag) {
                $declareInvoiceNumberGeneratedElsewhere = $container->get('marketplace.order.action.declare_invoice_number_generated_elsewhere');
                if ($declareInvoiceNumberGeneratedElsewhere->isAllowed($order)) {
                    $declareInvoiceNumberGeneratedElsewhere->execute($order);
                }
            }
        }

        //Si on est dans le cas d'un produit de type service, on avance le status au stade traité
        if ($needShipping === false
            && ($hasNewInvoiceNumber
            || $generateAutomaticBillingNumber
            || $hasNewDoNotCreateInvoiceFlag
            || $isAutomaticBillingFeatureEnabled)
        ) {
            $markAsShipped = $container->get('marketplace.order.action.mark_as_shipped');
            if ($markAsShipped->isAllowed($order) === true) {
                $markAsShipped->execute($order);
                $params['status'] = OrderStatus::PROCESSED;
            }
        }

        if ($params['status']) {
            fn_change_order_status($id, $params['status']);
        }

        return [
            'status' => Response::STATUS_OK,
            'data' => ['order_id' => $id],
        ];
    }

    public function privileges()
    {
        return array(
            'update' => true,
            'index'  => true,
        );
    }
}
