<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\FeatureFlag\FeatureFlaggableInterface;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Group\Exception\NotEmptyGroupException;
use Wizacha\Marketplace\Group\Group;
use Wizacha\Marketplace\Group\UserGroupService;

class GroupController extends AbstractController implements FeatureFlaggableInterface
{
    private const OFFSET = 0;
    private const LIMIT = 10;

    private UserGroupService $userGroupService;

    public function __construct(UserGroupService $userGroupService)
    {
        $this->userGroupService = $userGroupService;
    }

    public function listAction(Request $request): JsonResponse
    {
        $this->canAccessUserGroups();

        $offset = $request->query->getInt('offset', static::OFFSET);
        $limit = $request->query->getInt('limit', static::LIMIT);

        if ($offset < 0) {
            return new BadRequestJsonResponse(
                sprintf(
                    'Offset must be a positive integer or zero, %d given',
                    $offset
                )
            );
        }

        if ($limit < 1) {
            return new BadRequestJsonResponse(
                sprintf(
                    'Limit must be a positive integer, %d given',
                    $limit
                )
            );
        }

        // add +1 to offset to use it like page
        $paginator = $this->userGroupService->list($offset + 1, $limit);
        $groups = $paginator->getQuery()->getResult();
        $items = $this->expose($groups);

        $resultJson = [
            'offset' => $offset,
            'limit' => $limit,
            'total' => $paginator->count(),
            'items' => $items
        ];

        return new JsonResponse($resultJson, Response::HTTP_OK);
    }

    public function createAction(Request $request): JsonResponse
    {
        $this->canAccessUserGroups();

        $name = $request->request->get('name');

        if (\is_string($name) === false || \mb_strlen(\trim($name)) === 0) {
            return BadRequestJsonResponse::invalidField('name', 'name must be string and not empty');
        }

        $group = new Group(\trim($name));
        try {
            $this->userGroupService->save($group);
        } catch (\Exception $exception) {
            return new JsonResponse(['message' => 'The group name is already used'], Response::HTTP_CONFLICT);
        }

        return new JsonResponse(['groupId' => $group->getId(), 'name' => $group->getName()], Response::HTTP_CREATED);
    }

    public function updateAction(string $groupId, Request $request): JsonResponse
    {
        $this->canAccessUserGroups();

        $data = \json_decode($request->getContent(), true);

        if (\array_key_exists('name', $data) === false
            || \is_string($data['name']) === false
            ||  \mb_strlen(\trim($data['name'])) === 0
        ) {
            return BadRequestJsonResponse::invalidField('name', 'name must be string and not empty');
        }

        try {
            $group = $this->userGroupService->get($groupId);
        } catch (NotFound $exception) {
            return new JsonResponse(['message' => $exception->getMessage()], Response::HTTP_NOT_FOUND);
        }

        try {
            $group->setName(\trim($data['name']));
            $this->userGroupService->save($group);
        } catch (\Exception $exception) {
            return new JsonResponse(['message' => 'The group name is already used'], Response::HTTP_CONFLICT);
        }

        return new JsonResponse(['groupId' => $group->getId(), 'name' => $group->getName()], Response::HTTP_OK);
    }

    public function addUsersAction(string $groupId, Request $request): JsonResponse
    {
        $this->canAccessUserGroups();

        if ($request->request->has('usersIds') === false) {
            return new JsonResponse(['message' => 'usersIds is required'], Response::HTTP_BAD_REQUEST);
        }

        $usersIds = $request->request->get('usersIds');

        if (\is_array($usersIds) === false) {
            return new JsonResponse(['message' => 'usersIds must be array'], Response::HTTP_BAD_REQUEST);
        }

        try {
            $validIds = $this->userGroupService->addUsers($groupId, $usersIds);

            return new JsonResponse($validIds, Response::HTTP_CREATED);
        } catch (NotEmptyGroupException $exception) {
            return new JsonResponse(['message' => 'Group must be empty'], Response::HTTP_CONFLICT);
        } catch (NotFound $exception) {
            return new JsonResponse(['message' => $exception->getMessage()], Response::HTTP_NOT_FOUND);
        }
    }

    public function addUserAction(string $groupId, int $userId): JsonResponse
    {
        $this->canAccessUserGroups();

        try {
            $this->userGroupService->addUser($groupId, $userId);

            return new JsonResponse(['userId' => $userId], Response::HTTP_CREATED);
        } catch (NotFound $exception) {
            return new JsonResponse(['message' => $exception->getMessage()], Response::HTTP_NOT_FOUND);
        }
    }

    public function deleteUserAction(string $groupId, int $userId): JsonResponse
    {
        $this->canAccessUserGroups();

        try {
            $this->userGroupService->deleteUser($groupId, $userId);

            return new JsonResponse(null, Response::HTTP_NO_CONTENT);
        } catch (NotFound $exception) {
            return new JsonResponse(['message' => $exception->getMessage()], Response::HTTP_NOT_FOUND);
        }
    }

    public function listUsersAction(string $groupId, Request $request): JsonResponse
    {
        $this->canAccessUserGroups();

        $offset = $request->query->getInt('offset', static::OFFSET);
        $limit = $request->query->getInt('limit', static::LIMIT);

        if ($offset < 0) {
            return new BadRequestJsonResponse(
                sprintf(
                    'Offset must be a positive integer or zero, %d given',
                    $offset
                )
            );
        }

        if ($limit < 1) {
            return new BadRequestJsonResponse(
                sprintf(
                    'Limit must be a positive integer, %d given',
                    $limit
                )
            );
        }

        $paginator = $this->userGroupService->listUsers($groupId, $offset, $limit);

        $resultJson = [
            'offset' => $paginator->getOffset(),
            'limit' => $paginator->getLimit(),
            'total' => $paginator->getTotal(),
            'items' => $paginator->getResults()
        ];

        return new JsonResponse($resultJson, Response::HTTP_OK);
    }

    public function deleteUsersAction(string $groupId, Request $request): JsonResponse
    {
        $this->canAccessUserGroups();

        try {
            $group = $this->userGroupService->get($groupId);
            $group->deleteUsers();
            $this->userGroupService->save($group);

            return new JsonResponse(null, Response::HTTP_NO_CONTENT);
        } catch (NotFound $exception) {
            return new JsonResponse(['message' => $exception->getMessage()], Response::HTTP_NOT_FOUND);
        }
    }

    private function expose($groups): array
    {
        $items = [];
        foreach ($groups as $group) {
            $items[] = ['groupId' => $group->getId() , 'name' => $group->getName()];
        }

        return $items;
    }

    public function canAccessUserGroups(): void
    {
        if ($this->isGranted('ROLE_ADMIN') === false) {
            throw new Forbidden('You must be an admin');
        }
    }

    public function getFeatureFlag(): string
    {
        return 'feature.user_groups_enabled';
    }
}
