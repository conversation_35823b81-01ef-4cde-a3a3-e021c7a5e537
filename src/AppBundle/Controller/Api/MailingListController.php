<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\ConflictHttpException;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Marketplace\MailingList\Exception\AlreadyRegisteredException;
use Wizacha\Marketplace\MailingList\MailingList;

class MailingListController extends Controller
{
    public function listAction()
    {
        $response = array_map(function (MailingList $mailingList) {
            return [
                'id' => $mailingList->getId(),
                'name' => $mailingList->getName(),
            ];
        }, $this->get('marketplace.mailing_list.mailing_list_service')->getMailingLists());

        return new JsonResponse($response);
    }

    public function postSubscriptionAction(int $id, $email)
    {
        try {
            $this->get('marketplace.mailing_list.mailing_list_service')->subscribe($email, $id);
        } catch (AlreadyRegisteredException $e) {
            throw new ConflictHttpException('This user already subscribed to this mailing list.');
        } catch (\InvalidArgumentException $e) {
            return BadRequestJsonResponse::invalidField('email', 'You must provide a valid email address.');
        }

        return new JsonResponse('', Response::HTTP_CREATED);
    }

    public function deleteSubscriptionAction(int $id, $email)
    {
        $this->get('marketplace.mailing_list.mailing_list_service')->unsubscribe($email, $id);

        return new JsonResponse('', Response::HTTP_NO_CONTENT);
    }

    /**
     * Needs authentication
     */
    public function getSubscriptionAction(int $id)
    {
        $service = $this->get('marketplace.mailing_list.mailing_list_service');
        $maillingList = $service->getMailingList($id);

        return new JsonResponse([
            'isSubscribedTo' => $service->getSubscriptions($this->getUser()->getUsername())->isSubscribedTo($maillingList),
        ]);
    }
}
