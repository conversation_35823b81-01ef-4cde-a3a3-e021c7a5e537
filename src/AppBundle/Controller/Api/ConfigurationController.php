<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;

class ConfigurationController extends Controller
{
    public function getConfigurationAction()
    {
        $configuration = $this->get("app.configuration_service")->readConfiguration();

        return new JsonResponse($configuration);
    }

    public function postConfigurationAction(Request $request)
    {
        $sentData = $request->request->all();
        $files = array_map(
            function (UploadedFile $file) {
                return [
                    "file" => $file->getRealPath(),
                    "name" => $file->getClientOriginalName(),
                    'mimeType' => $file->getClientMimeType() === 'image/svg+xml' // workaround for svg files (because guesser doesn't work well with them)
                        ? $file->getClientMimeType()
                        : $file->getMimeType(),
                ];
            },
            $request->files->all()
        );
        $newConfiguration = $this->get("app.configuration_service")->saveConfiguration($sentData, $files, false);

        return new JsonResponse($newConfiguration);
    }
}
