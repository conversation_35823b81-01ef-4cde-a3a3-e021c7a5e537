<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api\Prediggo;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\HttpFoundation\StreamedResponse;

class PrediggoController extends Controller
{
    public function productsAction()
    {
        return $this->streamFile('products.xml');
    }

    public function usersAction()
    {
        return $this->streamFile('users.xml');
    }

    public function transactionsAction()
    {
        return $this->streamFile('transactions.xml');
    }

    public function cmsItemsAction()
    {
        return $this->streamFile('CMSItems.xml');
    }

    public function hierarchyAction()
    {
        return $this->streamFile('Hierarchy.csv', 'text/csv');
    }

    public function attributesTranslationsAction()
    {
        return $this->streamFile('AttributesTranslations.csv', 'text/csv');
    }

    public function valuesTranslationsAction()
    {
        return $this->streamFile('ValuesTranslations.csv', 'text/csv');
    }

    public function integrityAction()
    {
        return $this->streamFile('integrity.txt', 'text/plain');
    }

    private function streamFile(string $file, string $mimeType = 'text/xml'): Response
    {
        $storage = container()->get("Wizacha\Storage\PrediggoStorageService");
        $stream = $storage->readStream($file);

        if ($stream === false) {
            $this->get('logger')->error('Prediggo: cannot open ' . $file);

            return new Response('', 500);
        }

        $response = new StreamedResponse(function () use ($stream) {
            while (!feof($stream)) {
                echo fread($stream, 8192);
                ob_flush();
                flush();
            }
            fclose($stream);
        });
        $response->headers->set('Content-Type', $mimeType);
        $response->headers->set('Content-Disposition', $response->headers->makeDisposition(
            ResponseHeaderBag::DISPOSITION_ATTACHMENT,
            $file
        ));

        return $response;
    }
}
