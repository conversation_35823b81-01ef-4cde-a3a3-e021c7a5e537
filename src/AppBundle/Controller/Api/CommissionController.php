<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\ConflictHttpException;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Marketplace\Commission\Commission;
use Wizacha\Marketplace\Commission\CommissionRepository;
use Wizacha\Marketplace\Commission\CommissionService;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\PIM\Category\CategoryService;

class CommissionController extends Controller
{
    /** @var CommissionService */
    protected $commissionService;

    /** @var CategoryService */
    protected $categoryService;

    /** @var CompanyService */
    protected $companyService;

    /** @var CommissionRepository */
    protected $commissionRepository;

    public function __construct(
        CommissionService $commissionService,
        CategoryService $categoryService,
        CompanyService $companyService,
        CommissionRepository $commissionRepository
    ) {
        $this->commissionService = $commissionService;
        $this->categoryService = $categoryService;
        $this->companyService = $companyService;
        $this->commissionRepository = $commissionRepository;
    }

    public function addMarketplaceCommissionAction(Request $request): JsonResponse
    {
        $commissionOptions = $request->request->all();
        $missingFields = $this->checkFields($commissionOptions);

        if (0 < \count($missingFields)) {
            return BadRequestJsonResponse::missingFields(...$missingFields);
        }

        if (0 < \strlen($this->commissionService->getDefaultCommission()->getId())) {
            throw new ConflictHttpException('Marketplace commission already exists.');
        }

        if ($this->hasMaximumCommissionError($commissionOptions) === true) {
            return BadRequestJsonResponse::invalidField('maximum', 'Commission’s maximum must be superior to 0 or null.');
        }

        $commissionId = $this->commissionService->saveCommission(
            new Commission(
                '',
                null,
                null,
                \floatval($commissionOptions['percent']),
                \floatval($commissionOptions['fixed']),
                $commissionOptions['maximum'] ? \floatval($commissionOptions['maximum']) : null
            )
        );

        return new JsonResponse(
            [
                'message' => 'Marketplace commission created successfully.',
                'id' => $commissionId,
            ]
        );
    }

    public function addCategoryCommissionAction(Request $request, int $categoryId): JsonResponse
    {
        $commissionOptions = $request->request->all();
        $missingFields = $this->checkFields($commissionOptions);

        if (0 < \count($missingFields)) {
            return BadRequestJsonResponse::missingFields(...$missingFields);
        }

        if ($this->hasMaximumCommissionError($commissionOptions) === true) {
            return BadRequestJsonResponse::invalidField('maximum', 'Commission’s maximum must be superior to 0 or null.');
        }

        // Throws a 404 if the category doesn't exist
        $this->categoryService->get($categoryId);

        $companyId = $commissionOptions['company'] ? \intval($commissionOptions['company']) : null;

        if (\is_int($companyId)) {
            // Throws a 404 if the company doesn't exist
            $this->companyService->get($companyId);
        }

        if ($this->commissionService->getCommissionByCategoryAndCompany($categoryId, $companyId) instanceof Commission) {
            throw new ConflictHttpException('This category commission already exists.');
        }

        $commissionId = $this->commissionService->saveCommission(
            new Commission(
                '',
                $companyId,
                $categoryId,
                \floatval($commissionOptions['percent']),
                \floatval($commissionOptions['fixed']),
                $commissionOptions['maximum'] ? \floatval($commissionOptions['maximum']) : null
            )
        );

        return new JsonResponse(
            [
                'message' => 'Category commission created successfully.',
                'id' => $commissionId,
            ]
        );
    }

    public function addCompanyCommissionAction(Request $request, int $companyId): JsonResponse
    {
        $commissionOptions = $request->request->all();
        $missingFields = $this->checkFields($commissionOptions);

        if (0 < \count($missingFields)) {
            return BadRequestJsonResponse::missingFields(...$missingFields);
        }

        if ($this->hasMaximumCommissionError($commissionOptions) === true) {
            return BadRequestJsonResponse::invalidField('maximum', 'Commission’s maximum must be superior to 0 or null.');
        }

        // Throws a 404 if the company doesn't exist
        $this->companyService->get($companyId);

        $categoryId = $commissionOptions['category'] ? \intval($commissionOptions['category']) : null;

        if (\is_int($categoryId)) {
            // Throws a 404 if the category doesn't exist
            $this->categoryService->get($categoryId);
        }

        if ($this->commissionService->getCommissionByCategoryAndCompany($categoryId, $companyId) instanceof Commission) {
            throw new ConflictHttpException('This company commission already exists.');
        }

        $commissionId = $this->commissionService->saveCommission(
            new Commission(
                '',
                $companyId,
                $categoryId,
                \floatval($commissionOptions['percent']),
                \floatval($commissionOptions['fixed']),
                $commissionOptions['maximum'] ? \floatval($commissionOptions['maximum']) : null
            )
        );

        return new JsonResponse(
            [
                'message' => 'Company commission created successfully.',
                'id' => $commissionId,
            ]
        );
    }

    public function updateMarketplaceCommissionAction(Request $request, string $commissionId): JsonResponse
    {
        $commission = $this->commissionService->getCommissionById($commissionId);

        $commissionOptions = $request->request->all();
        $missingFields = $this->checkFields($commissionOptions);

        if (0 < \count($missingFields)) {
            return BadRequestJsonResponse::missingFields(...$missingFields);
        }

        if ($this->hasMaximumCommissionError($commissionOptions) === true) {
            return BadRequestJsonResponse::invalidField('maximum', 'Commission’s maximum must be superior to 0 or null.');
        }

        $commission
            ->setPercentAmount(\floatval($commissionOptions['percent']))
            ->setFixAmount(\floatval($commissionOptions['fixed']))
            ->setMaximumAmount($commissionOptions['maximum'] ? \floatval($commissionOptions['maximum']) : null)
        ;

        $this->commissionService->saveCommission($commission);

        return new JsonResponse(['message' => 'Marketplace commission updated successfully.']);
    }

    public function updateCategoryCommissionAction(Request $request, int $categoryId, string $commissionId): JsonResponse
    {
        $commission = $this->commissionService->getCommissionById($commissionId);

        $commissionOptions = $request->request->all();
        $missingFields = $this->checkFields($commissionOptions);

        if (\count($missingFields) > 0) {
            return BadRequestJsonResponse::missingFields(...$missingFields);
        }

        if ($this->hasMaximumCommissionError($commissionOptions) === true) {
            return BadRequestJsonResponse::invalidField('maximum', 'Commission’s maximum must be superior to 0 or null.');
        }

        // Throws a 404 if the category doesn't exist
        $this->categoryService->get($categoryId);

        $companyId = $commissionOptions['company'] ? \intval($commissionOptions['company']) : null;

        if (\is_int($companyId)) {
            // Throws a 404 if the company doesn't exist
            $this->companyService->get($companyId);
        }

        if ($commission->getCompanyId() !== $companyId || $commission->getCategoryId() !== $categoryId) {
            return BadRequestJsonResponse::invalidField('company or category', '');
        }

        $commission
            ->setPercentAmount(\floatval($commissionOptions['percent']))
            ->setFixAmount(\floatval($commissionOptions['fixed']))
            ->setMaximumAmount($commissionOptions['maximum'] ? \floatval($commissionOptions['maximum']) : null)
        ;

        $this->commissionService->saveCommission($commission);

        return new JsonResponse(['message' => 'Category commission updated successfully.']);
    }

    public function updateCompanyCommissionAction(Request $request, int $companyId, string $commissionId): JsonResponse
    {
        $commissionOptions = $request->request->all();
        $missingFields = $this->checkFields($commissionOptions);

        if (0 < \count($missingFields)) {
            return BadRequestJsonResponse::missingFields(...$missingFields);
        }

        if ($this->hasMaximumCommissionError($commissionOptions) === true) {
            return BadRequestJsonResponse::invalidField('maximum', 'Commission’s maximum must be superior to 0 or null.');
        }

        // Throws a 404 if the company doesn't exist
        $this->companyService->get($companyId);

        $categoryId = $commissionOptions['category'] ? \intval($commissionOptions['category']) : null;

        if (\is_int($categoryId)) {
            // Throws a 404 if the category doesn't exist
            $this->categoryService->get($categoryId);
        }

        $commission = $this->commissionService->getCommissionById($commissionId);

        if ($commission->getCompanyId() !== $companyId || $commission->getCategoryId() !== $categoryId) {
            return BadRequestJsonResponse::invalidField('company or category', '');
        }

        $commission
            ->setPercentAmount(\floatval($commissionOptions['percent']))
            ->setFixAmount(\floatval($commissionOptions['fixed']))
            ->setMaximumAmount($commissionOptions['maximum'] ? \floatval($commissionOptions['maximum']) : null)
        ;

        $this->commissionService->saveCommission($commission);

        return new JsonResponse(['message' => 'Company commission updated successfully.']);
    }

    public function getCommissionsAction(Request $request): JsonResponse
    {
        if (\is_string($request->query->get('type')) && $request->query->get('type') === 'marketplace') {
            $listCommissions = $this->commissionRepository->findAllMarketplaceCommissions();
        } else {
            $listCommissions = $this->commissionRepository->findAllCommissions();
        }

        return new JsonResponse($this->exposeCommissions($listCommissions));
    }

    public function getMarketplaceCommissionAction(): JsonResponse
    {
        $defaultCommission = $this->commissionService->getDefaultCommission();
        $returnCommission = (0 < \strlen($defaultCommission->getId())) ? $defaultCommission->expose() : [];

        return new JsonResponse($returnCommission);
    }

    public function getCommissionAction(string $commissionId): JsonResponse
    {
        $commission = $this->commissionService->getCommissionById($commissionId);

        return new JsonResponse($commission->expose());
    }

    public function getCategoryCommissionsAction(int $categoryId): JsonResponse
    {
        // Throws a 404 if the category doesn't exist
        $this->categoryService->get($categoryId);

        $listCommissions = $this->commissionRepository->findCommissionsByCategory($categoryId);

        return new JsonResponse($this->exposeCommissions($listCommissions));
    }

    public function getCompanyCommissionsAction(int $companyId): JsonResponse
    {
        // Throws a 404 if the category doesn't exist
        $this->companyService->get($companyId);

        $listCommissions = $this->commissionRepository->findByCompany($companyId);

        return new JsonResponse($this->exposeCommissions($listCommissions));
    }

    public function deleteCommissionAction(string $commissionId): JsonResponse
    {
        $this->commissionService->getCommissionById($commissionId);
        $this->commissionRepository->delete($commissionId);

        return new JsonResponse(['message' => 'Commission deleted successfully.']);
    }

    /**
     * @param mixed[] $fields
     *
     * @return string[]
     */
    private function checkFields(array $fields): array
    {
        $missingFields = [];

        if (false === \is_float($fields['fixed']) && false === \is_int($fields['fixed'])) {
            $missingFields[] = 'fixed';
        }

        if (false === \is_float($fields['percent']) && false === \is_int($fields['percent'])) {
            $missingFields[] = 'percent';
        }

        return $missingFields;
    }

    /**
     * @param Commission[] $commissions
     *
     * @return Commission[]
     */
    private function exposeCommissions(array $commissions): array
    {
        $returnCommissions = [];

        /** @var Commission $commission */
        foreach ($commissions as $commission) {
            $returnCommissions[] = $commission->expose();
        }

        return $returnCommissions;
    }

    /**
     * @param array $commissionOptions
     * @return bool
     */
    private function hasMaximumCommissionError(array $commissionOptions): bool
    {
        return (\array_key_exists('maximum', $commissionOptions) === true  &&  $commissionOptions['maximum'] <= 0 && $commissionOptions['maximum'] !== null);
    }
}
