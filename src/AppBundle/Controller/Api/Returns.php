<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api;

use Tygh\Api\Response;

class Returns extends \Tygh\Api\AEntity
{
    public function index($id = 0, $params = array())
    {
        if ($id) {
            $params['return_id'] = $id;
        }

        list($returns, ) = fn_get_rma_returns($params);

        array_walk(
            $returns,
            function (&$_data) {
                $_data = fn_w_object_filter($_data, 'returns', 'api', 'display_objects');
                $_data = fn_w_convert_fields($_data, 'returns', 'send', 'convert_fields_api');
            }
        );

        $status = Response::STATUS_OK;

        if ($id) {
            if (\count($returns) == 1) {
                list($returns) = $returns;
            } else {
                $status = Response::STATUS_NOT_FOUND;
                $returns = null;
            }
        }

        return array(
            'status' => $status,
            'data' => $returns,
        );
    }

    public function update($id = 0, $params = array())
    {
        if (!$id) {
            return [
                'status' => Response::STATUS_NOT_FOUND,
                'data' => []
            ];
        }

        $errors = fn_w_rma_check_rma_infos($id, 'A');
        if ($errors) {
            return [
                'status' => Response::STATUS_BAD_REQUEST,
                'data' => [$errors]
            ];
        }

        $container = container();

        $orderReturn = $container->get('marketplace.order_return.service')->getReturn($id);
        $order = $container->get('marketplace.order.order_service')->getOrder($orderReturn->getOrderId());

        $isAutomaticRmaEnabled = $container->getParameter('feature.activate_billing_number_auto_generation')
            && $order->getCompany()->hasAutomaticRmaNumber();

        if ($isAutomaticRmaEnabled && !empty($params['number'])) {
            return [
                'status' => Response::STATUS_BAD_REQUEST,
                'data' => [
                    'error' => 'Automatic RMA number generation is enabled. You cannot set your own RMA number.',
                ],
            ];
        }

        $number = $isAutomaticRmaEnabled
            ? $container->get('marketplace.order.rma_number_generator')->generate($id)
            : $params['number'];

        fn_w_rma_update_status($id, 'A', $number, ['A' => false, 'C' => true, 'V' => true]);

        return [
            'status' => Response::STATUS_OK,
            'data' => ['return_id' => $id],
        ];
    }

    public function delete($id = 0, $params = array())
    {
        return [
            'status' => Response::STATUS_FORBIDDEN,
            'data' => []
        ];
    }

    public function create($id = 0, $params = array())
    {
        return [
            'status' => Response::STATUS_FORBIDDEN,
            'data' => []
        ];
    }

    public function privileges()
    {
        return array(
            'index'  => true,
            'update' => true,
        );
    }
}
