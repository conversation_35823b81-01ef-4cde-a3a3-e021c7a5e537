<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\Catalog;

use http\Exception\InvalidArgumentException;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Wizacha\Marketplace\Catalog\Category\Category;

class CategoryController extends Controller
{
    /**
     * This specific endpoint bypass the legacy stack in init.php,
     * @param Request $request
     * @return JsonResponse
     * @see fn_init_stack_bypass in fn.init.php
     */
    public function listAction(Request $request): JsonResponse
    {
        try {
            $parameters = (new OptionsResolver())
                ->setDefined('id')
                ->setNormalizer('id', function (OptionsResolver $options, $parameters) {
                    return \array_map('intval', \is_string($parameters) ? [$parameters] : $parameters);
                })
                ->setAllowedTypes('id', ['string', 'array', 'null'])
                ->resolve($request->query->all());
        } catch (\InvalidArgumentException $e) {
            throw new BadRequestHttpException();
        }

        $categories = $this->get('marketplace.catalog.category_service')->getCategories($parameters['id']);

        if (\count($categories) === 0) {
            throw new NotFoundHttpException('Aucune categorie trouvée');
        }

        $response = new JsonResponse(array_map(static function (Category $category): array {
            return $category->expose();
        }, $categories));

        if ($this->getParameter('feature.cache_http')) {
            $response->setVary('Accept-Language');
            $response->setSharedMaxAge($this->getParameter('cache_api_catalog_category_list'));
        }

        return $response;
    }

    /**
     * This specific endpoint bypass the legacy stack in init.php,
     * @see fn_init_stack_bypass in fn.init.php
     */
    public function getAction(int $id)
    {
        $category = $this->get('marketplace.catalog.category_service')->getCategory($id);

        return new JsonResponse($category->expose());
    }

    /**
     * This specific endpoint bypass the legacy stack in init.php,
     * @see fn_init_stack_bypass in fn.init.php
     */
    public function treeAction()
    {
        $tree = $this->get('marketplace.catalog.category_service')->getTree();

        $response = new JsonResponse(array_map([$this, 'serializeTree'], array_values($tree)));

        if ($this->getParameter('feature.cache_http')) {
            $response->setVary('Accept-Language');
            $response->setSharedMaxAge($this->getParameter('cache_api_catalog_category_tree'));
        }

        return $response;
    }

    private function serializeTree(array $categoryTreeBranch): array
    {
        if (empty($categoryTreeBranch)) {
            return [];
        }

        return [
            'category' => $categoryTreeBranch['category']->expose(),
            'children' => array_map([$this, 'serializeTree'], array_values($categoryTreeBranch['children'])),
        ];
    }
}
