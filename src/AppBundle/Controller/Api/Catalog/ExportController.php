<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api\Catalog;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Wizacha\Marketplace\Catalog\ExporterService;
use Wizacha\Marketplace\Exception\NotFound;

class ExportController extends Controller
{
    /**
     * @var ExporterService
     */
    private $exporterService;

    public function __construct(ExporterService $exporterService)
    {
        $this->exporterService = $exporterService;
    }

    /**
     * @throws NotFound
     *
     * This specific endpoint bypass the legacy stack in init.php,
     * @see fn_init_stack_bypass in fn.init.php
     */
    public function getFileAction(Request $request, int $page = 1): JsonResponse
    {
        $language = $this->getParameter('locale');
        if ($request->headers->has('Accept-Language')) {
            $acceptLanguage     = strtolower($request->headers->get('Accept-Language'));
            $availableLanguages = \Tygh\Languages\Languages::getAvailable();

            if (\array_key_exists($acceptLanguage, $availableLanguages)) {
                $language = $acceptLanguage;
            }
        }

        $pagination = $this->exporterService->getPageMax($language);

        // $page should be in ]0;$pagination]
        if ($page <= 0 || $page > $pagination) {
            throw new NotFound("The file ${page} does not exist");
        }

        $result = json_decode($this->exporterService->getPageContent($page, $language), true);
        if (JSON_ERROR_NONE !== json_last_error()) {
            throw new \Exception(
                'Unable to decode JSON data: ' . json_last_error_msg()
            );
        }

        $response = new JsonResponse();
        $response->setData([
            'result' => $result,
            'pagination' => [
                'page' => $page,
                'nbPages' => $pagination,
            ],
        ]);

        return $response;
    }
}
