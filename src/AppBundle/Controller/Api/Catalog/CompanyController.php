<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\Catalog;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Marketplace\Catalog\Company\Company;
use Wizacha\Marketplace\Catalog\Company\CompanyService;
use Wizacha\Marketplace\Review\CompanyReviewService;

class CompanyController extends Controller
{
    private CompanyReviewService $companyReviewService;
    private CompanyService $catalogCompanyService;

    public function __construct(
        CompanyReviewService $companyReviewService,
        CompanyService $catalogCompanyService
    ) {
        $this->companyReviewService = $companyReviewService;
        $this->catalogCompanyService = $catalogCompanyService;
    }

    /**
     * This specific endpoint bypass the legacy stack in init.php,
     * @see fn_init_stack_bypass in fn.init.php
     */
    public function listAction(Request $request): JsonResponse
    {
        $filter['extra'] = $request->query->get('extra', []);

        if (\is_array($filter['extra']) === false) {
            return BadRequestJsonResponse::invalidField('extra', 'Invalid request: extra must be an array.');
        }

        $exposedResources = \array_map(function (Company $company) {
            return $company->expose($this->companyReviewService);
        }, $this->catalogCompanyService->getCompanies($filter));

        return $this->json($exposedResources);
    }

    /**
     * This specific endpoint bypass the legacy stack in init.php,
     * @see fn_init_stack_bypass in fn.init.php
     */
    public function getAction(int $id)
    {
        $company = $this->get('marketplace.catalog.company_service')->getCompany($id);

        return new JsonResponse($company->exposeFull($this->get('marketplace.review.company.service')));
    }
}
