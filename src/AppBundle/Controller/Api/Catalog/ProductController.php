<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api\Catalog;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Wizacha\AppBundle\Controller\Api\Exception\MissingFieldsHttpException;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Bridge\Symfony\Response\NotFoundJsonResponse;
use Wizacha\Marketplace\Catalog\Product\ProductService as CatalogProductService;
use Wizacha\Marketplace\Exception\FileNotFoundException;
use Wizacha\Marketplace\Exception\InvalidDeclinationException;
use Wizacha\Marketplace\Exception\ProductNotFound;
use Wizacha\Marketplace\PIM\Product\ProductService as PimProductService;

class ProductController extends Controller
{
    /** @var bool */
    private $showOutOfStockProducts;

    /** @var CatalogProductService */
    private $catalogProductService;

    /** @var PimProductService */
    private $pimProductService;

    public function __construct(
        bool $showOutOfStockProducts,
        CatalogProductService $catalogProductService,
        PimProductService $pimProductService
    ) {
        $this->showOutOfStockProducts = $showOutOfStockProducts;
        $this->catalogProductService = $catalogProductService;
        $this->pimProductService = $pimProductService;
    }

    /**
     * This specific endpoint bypass the legacy stack in init.php,
     * @see fn_init_stack_bypass in fn.init.php
     */
    public function getIdAction(Request $request, string $id)
    {
        $product = $this->catalogProductService->getProduct($id);
        if (!$product) {
            throw new ProductNotFound($id);
        }

        // If the product is linked to an MVP, redirect to it
        $mvpId = $product->getMultiVendorProductId();
        if ($mvpId !== null) {
            $params = $request->query->all();
            $params['id'] = $mvpId;

            return $this->redirectToRoute('api_catalog_products_id', $params);
        }

        $response = new JsonResponse();

        if ($this->getParameter('feature.cache_http') === true) {
            $response->setPublic();
            $response->setVary('Accept-Language');
            $response->setSharedMaxAge(
                $this->getParameter('cache_api_catalog_products_id')
            );
        }
        $data = $this->catalogProductService->exposeFull($product, []);

        if ($product->isMultiVendorProduct() === false) {
            $data['isUpToDate'] = $this->catalogProductService->isUpToDate($id);
        } else {
            $data['isUpToDate'] = null;
        }

        $response->setData($data);

        return $response;
    }

    /**
     * This specific endpoint bypass the legacy stack in init.php,
     * @see fn_init_stack_bypass in fn.init.php
     */
    public function getAction(Request $request)
    {
        $allowMvp = \boolval($request->get('allowMvp', '1'));

        if (\is_null($request->get('id'))
            && \is_null($request->get('code'))
            && \is_null($request->get('supplierRef'))
            && \is_null($request->get('mvpId'))
        ) {
            return new JsonResponse(
                ['message' => __("api_must_provide_filter") . 'id/code/supplierRef/mvpId'],
                Response::HTTP_BAD_REQUEST
            );
        }

        try {
            $productIds = $this->catalogProductService->getProductsIdByFilters(
                $request->get('id'),
                $request->get('code'),
                $request->get('supplierRef'),
                $request->get('mvpId')
            );
        } catch (\UnexpectedValueException $e) {
            return new JsonResponse(['error' => $e->getMessage()], Response::HTTP_BAD_REQUEST);
        }

        if (\count($productIds) > 0) {
            $productIds = $this->catalogProductService->getSearcheableProductIdInFront($productIds, [], $allowMvp)->toArray();
        }

        $products = $this->catalogProductService->getProductsByIds($productIds);
        $features = fn_get_product_features_list(['product_id' => $productIds]);

        $outData = [];
        foreach ($products as $product) {
            if ($request->query->has('companyId')
                && !$product->hasCompany($request->query->getInt('companyId'))
            ) {
                continue;
            }

            if ($product !== null) {
                if ($this->showOutOfStockProducts === true || $product->isAvailable() === true) {
                    $outData[] = $this->catalogProductService->expose($product, $features);
                }
            }
        }

        return new JsonResponse($outData);
    }

    /**
     * This specific endpoint bypass the legacy stack in init.php,
     * @see fn_init_stack_bypass in fn.init.php
     */
    public function getAttachmentAction(string $id)
    {
        $attachment = $this->pimProductService->getAttachment($id);

        try {
            return $attachment->getFile();
        } catch (FileNotFoundException $exception) {
            return new NotFoundJsonResponse($exception->getMessage());
        }
    }

    /**
     * Get Declination by Id
     *
     * This specific endpoint bypass the legacy stack in init.php,
     * @see fn_init_stack_bypass in fn.init.php
     */
    public function getDeclinationAction(string $declinationId)
    {
        try {
            $response = new JsonResponse(
                $this->catalogProductService->getDeclinationById($declinationId),
                Response::HTTP_OK
            );
        } catch (InvalidDeclinationException $exception) {
            return new BadRequestJsonResponse(
                $exception->getMessage(),
                $exception->getContext()
            );
        }

        if ($this->getParameter('feature.cache_http') === true) {
            $response->setSharedMaxAge($this->getParameter('cache_api_declination'));
        }

        return $response;
    }

    public function reportAction($id, Request $request)
    {
        $name = (string) $request->request->get('name');
        $email = filter_var((string) $request->request->get('email'), FILTER_VALIDATE_EMAIL);
        $message = (string) $request->request->get('message');

        if (!$name) {
            throw new MissingFieldsHttpException('name');
        }

        if (!$email) {
            throw new MissingFieldsHttpException('email');
        }

        if (!$message) {
            throw new MissingFieldsHttpException('message');
        }

        $this->catalogProductService->reportProduct($id, $name, $email, $message);

        return new JsonResponse('', Response::HTTP_NO_CONTENT);
    }
}
