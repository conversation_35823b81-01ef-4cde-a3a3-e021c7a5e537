<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api\Catalog;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Marketplace\Catalog\Exceptions\UserCannotReviewProduct;
use Wizacha\Marketplace\Entities\ThreadPost;
use Wizacha\Marketplace\Exception\ProductNotFound;
use Wizacha\Marketplace\ReadModel\Product;
use Wizacha\Marketplace\User\User;

class CustomerReviewController extends Controller
{
    /**
     * This specific endpoint bypass the legacy stack in init.php,
     * @see fn_init_stack_bypass in fn.init.php
     */
    public function getReviewsAction($productId)
    {
        $productReviewService = $this->get('marketplace.review.product.service');

        $reviews = $productReviewService->getReviews($productId);

        $response = new JsonResponse(array_map(function (ThreadPost $review) {
            return [
                'author' => $review->getName(),
                'message' => $review->getMessage(),
                'postedAt' => $review->getDatetime()->format(\DateTime::RFC3339),
                'rating' => $review->getRatingValue(),
            ];
        }, $reviews), 200);

        if ($this->getParameter('feature.cache_http')) {
            $response->setVary('Accept-Language');
            $response->setSharedMaxAge($this->getParameter('cache_api_catalog_product_reviews'));
        }

        return $response;
    }

    /**
     * This specific endpoint bypass the legacy stack in init.php,
     * @see fn_init_stack_bypass in fn.init.php
     */
    public function canAddReviewAction($productId)
    {
        $product = $this->get('marketplace.product.repository.read_model')->find($productId);

        if ($product instanceof Product === false) {
            throw new ProductNotFound($productId);
        }

        if ($this->getUser() === null) {
            return new JsonResponse('', Response::HTTP_UNAUTHORIZED);
        }

        $user = $this->get('marketplace.user.user_service')->get($this->getUser()->getId());

        if (false === $user instanceof User) {
            throw $this->createAccessDeniedException();
        }

        if ($product->isMultiVendorProduct()) {
            $hasUserOrderedProduct = $this->get('marketplace.order.order_service')->hasUserOrderedMultiVendorProduct($user->getUserId(), $product->getProductId());
        } else {
            $hasUserOrderedProduct = $this->get('marketplace.order.order_service')->hasUserOrderedProduct($user->getUserId(), $product->getProductId());
        }

        return new JsonResponse('', $hasUserOrderedProduct ? Response::HTTP_NO_CONTENT : Response::HTTP_UNAUTHORIZED);
    }

    public function addReviewAction($productId, Request $request)
    {
        $product = $this->get('marketplace.product.repository.read_model')->find($productId);

        if (!$product instanceof Product) {
            throw new ProductNotFound($productId);
        }

        $user = $this->get('marketplace.user.user_service')->get($this->getUser()->getId());

        if (!$user instanceof User) {
            throw $this->createAccessDeniedException();
        }

        if ($product->isMultiVendorProduct()) {
            $hasUserOrderedProduct = $this->get('marketplace.order.order_service')->hasUserOrderedMultiVendorProduct($user->getUserId(), $product->getProductId());
        } else {
            $hasUserOrderedProduct = $this->get('marketplace.order.order_service')->hasUserOrderedProduct($user->getUserId(), $product->getProductId());
        }
        if (!$hasUserOrderedProduct) {
            throw new UserCannotReviewProduct();
        }

        $author = $request->request->get('author');
        $rating = $request->request->get('rating');
        $message = $request->request->get('message');

        $missingFields = [];
        if ($author === null) {
            $missingFields[] = 'author';
        }
        if ($rating === null && $message === null) {
            $missingFields[] = 'rating';
            $missingFields[] = 'message';
        }
        if (!empty($missingFields)) {
            return BadRequestJsonResponse::missingFields(...$missingFields);
        }

        $this
            ->get('marketplace.review.product.service')
            ->addReview($product, $user, $author, $message, $rating);

        return new JsonResponse('', Response::HTTP_NO_CONTENT);
    }
}
