<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\Catalog;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use Wizacha\Marketplace\Catalog\Attribute;
use Wizacha\Marketplace\Catalog\DetailedAttributeVariant;

class AttributeController extends Controller
{
    /**
     * This specific endpoint bypass the legacy stack in init.php,
     * @see fn_init_stack_bypass in fn.init.php
     */
    public function listAction(Request $request)
    {
        $params = $request->query->all();
        $attributes = $this->get('marketplace.catalog.attribute_service')->getAttributes($params);

        return new JsonResponse(array_map([$this, 'serialize'], $attributes));
    }

    /**
     * This specific endpoint bypass the legacy stack in init.php,
     * @see fn_init_stack_bypass in fn.init.php
     */
    public function getAction(int $id)
    {
        $attribute = $this->get('marketplace.catalog.attribute_service')->getAttribute($id);

        return new JsonResponse($this->serialize($attribute));
    }

    /**
     * This specific endpoint bypass the legacy stack in init.php,
     * @see fn_init_stack_bypass in fn.init.php
     */
    public function getVariantAction(int $id)
    {
        $variant = $this->get('marketplace.catalog.attribute_service')->getVariant($id);

        return new JsonResponse($variant->expose());
    }

    /**
     * This specific endpoint bypass the legacy stack in init.php,
     * @see fn_init_stack_bypass in fn.init.php
     */
    public function getVariantsAction(int $id)
    {
        $variants = $this->get('marketplace.catalog.attribute_service')->getVariants($id);

        return new JsonResponse(
            array_map(
                function (DetailedAttributeVariant $variant) {
                    return $variant->expose();
                },
                $variants
            )
        );
    }

    private function serialize(Attribute $attribute)
    {
        return [
            'id' => $attribute->getId(),
            'code' => $attribute->getCode(),
            'name' => $attribute->getName(),
            'position' => $attribute->getPosition(),
            'parentId' => $attribute->getParentId(),
            'type' => $attribute->getType()->getKey(),
        ];
    }
}
