<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\Catalog;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Marketplace\Catalog\AttributeValue;
use Wizacha\Marketplace\Catalog\Category\CategorySummary;
use Wizacha\Marketplace\Catalog\Company\CompanySummary;
use Wizacha\Marketplace\Catalog\Declination\DeclinationImages;
use Wizacha\Marketplace\Catalog\Product\ProductCondition;
use Wizacha\Marketplace\Catalog\Product\ProductSummary;
use Wizacha\Marketplace\Image\Image;
use Wizacha\Search\GeoFilter;
use Wizacha\Search\Index\AbstractIndex;
use Wizacha\Search\Pagination;
use Wizacha\Search\Product\Sorting;

class ProductSearchController extends Controller
{
    public function searchAction(Request $request)
    {
        $productIndex = $this->get('marketplace.search.product_index');

        $query = (string) $request->query->get('query', '');
        $filters = (array) $request->query->get('filters', []);
        $sorting = null;
        foreach ((array) $request->query->get('sorting', []) as $sortCriteria => $sortDirection) {
            if (!\is_string($sortCriteria)) {
                return BadRequestJsonResponse::invalidField('sorting', 'Sort criteria must be a string');
            }

            $sorting = Sorting::fromString($sortCriteria, $sortDirection);
            break; // for now accept only the first sorting
        }
        $page = $request->query->getInt('page', 1);
        $resultsPerPage = $request->query->getInt('resultsPerPage', Pagination::DEFAULT_RESULTS_PER_PAGE);
        $pagination = new Pagination($page, $resultsPerPage);
        $extra = (array) $request->query->get('extra', []);
        $geoFilter = GeoFilter::fromHttpRequest($request);

        $results = $productIndex->search($query, $pagination, $filters, $sorting, $geoFilter, $extra);

        $resultsPagination = $results->getPagination();
        $response =  new JsonResponse([
            // Use array_values to ensure it's a JSON array, not object
            'results' => $this->serializeProducts($results->getProducts()),
            'pagination' => [
                'page' => $resultsPagination->getPage(),
                'nbResults' => $resultsPagination->getTotalResultCount(),
                'nbPages' => $resultsPagination->getNbPages(),
                'resultsPerPage' => $resultsPagination->getResultsPerPage(),
            ],
            'facets' => $results->getFacets(),
        ]);

        // We cache only basic and simple search requests
        if ($this->getParameter('feature.cache_http') && $query === '' && \count($filters) <= 1 && empty($extra) && $page === 1 && $geoFilter === null) {
            $response->setVary('Accept-Language');
            $response->setSharedMaxAge($this->getParameter('cache_api_search_products'));
        }

        return $response;
    }

    public function autocompleteAction(Request $request)
    {
        $filters = (array) $request->query->get('filters', []);
        $query = (string) $request->query->get('query', '');
        $results = [];

        if (\strlen($query) >= AbstractIndex::AUTOCOMPLETE_MIN_LENGTH) {
            $productIndex = $this->get('marketplace.search.product_index');
            $results = $productIndex->autocomplete($query, $filters);
        }

        // Use array_values to ensure it's a JSON array, not object
        return new JsonResponse(array_values($results));
    }

    /**
     * @param ProductSummary[] $products
     */
    private function serializeProducts(array $products): array
    {
        return array_map(function (ProductSummary $product) {
            $products = [
                'productId' => $product->getProductId(),
                'name' => $product->getName(),
                'code' => $product->getCode(),
                'productTemplateType' => $product->getProductTemplateType(),
                'slug' => $product->getSlug(),
                'minimumPrice' => $product->getMinimumPrice()->getConvertedAmount(),
                'maxPriceAdjustment' => $product->getMaxPriceAdjustment(),
                'crossedOutPrice' => $product->getCrossedOutPrice() ? $product->getCrossedOutPrice()->getConvertedAmount() : null,
                'isAvailable' => $product->isAvailable(),
                'createdAt' => $product->getCreatedAt()->getTimestamp(),
                'updatedAt' => $product->getUpdatedAt()->getTimestamp(),
                'transactionMode' => $product->getTransactionMode()->getValue(),
                'conditions' => array_map(function (ProductCondition $condition) {
                    return $condition->getValue();
                }, $product->getConditions()),
                'declinationCount' => $product->getDeclinationCount(),
                'affiliateLink' => $product->getAffiliateLink(),
                'mainImage' => $product->getMainImage() ? $product->getMainImage()->toArray() : null,
                'images' => array_map(function (array $image) {
                    return [
                        'id' => $image['id'],
                        'altText' => $image['altText']
                    ];
                }, $product->getImages()),
                'declinationsImages' => array_map(
                    function (DeclinationImages $declinationImage) {
                        return $declinationImage->toArray();
                    },
                    $product->getDeclinationsImages()
                ),
                'url' => $product->getUrl(),
                'subtitle' => $product->getSubtitle(),
                'shortDescription' => $product->getShortDescription(true),
                'averageRating' => (int) round($product->getAverageRating()),
                'averageRatingFloat' => $product->getAverageRating(),
                'attributes' => array_map(function (AttributeValue $attribute) {
                    return $attribute->expose();
                }, $product->getAttributes()),
                'categoryPath' => array_map(function (CategorySummary $category) {
                    return $category->expose();
                }, $product->getCategoryPath()),
                'companies' => array_map(function (CompanySummary $companySummary) {
                    return $companySummary->expose();
                }, $product->getCompanies()),
                'geolocation' => $product->getGeolocation() ? $product->getGeolocation()->expose() : null,
                'geolocations' => $product->getGeolocations() ?: null,
                'mainDeclination' => $product->getMainDeclinationId() === null ? null : [
                    'id' => $product->getMainDeclinationId(),
                ],
                'offers' => $product->getOffers(),
                'isSubscription' => $product->isSubscription(),
                'isRenewable' => $product->isRenewable(),
            ];
            if (true === $this->getParameter('feature.quote_requests')) {
                $products['quoteRequestsMinQuantity'] = $product->getQuoteRequestsMinQuantity();
                $products['isExclusiveToQuoteRequests'] = $product->isExclusiveToQuoteRequests();
            }

            return $products;
        }, $products);
    }
}
