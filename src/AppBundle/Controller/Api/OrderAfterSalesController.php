<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Wizacha\AppBundle\Controller\Api\Exception\MissingFieldsHttpException;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Order\Exception\OrderIsNotComplete;

class OrderAfterSalesController extends Controller
{
    public function createAction(int $orderId, Request $request)
    {
        $order = $this->container->get('marketplace.order.order_service')->getOrder($orderId);
        if ($this->getUser()->getId() != $order->getUserId()) {
            return new JsonResponse('', Response::HTTP_NOT_FOUND);
        }

        $comments = (string) $request->request->get('comments');
        if (empty($comments)) {
            throw new MissingFieldsHttpException('comments');
        }

        $items = (array) $request->request->get('items');
        if (empty($items)) {
            throw new MissingFieldsHttpException('items');
        }

        $items = array_flip($items);

        $selectedItems = [];
        foreach ($order->getItems() as $item) {
            if (isset($items[$item->getDeclinationId()])) {
                $selectedItems[] = $item;
            }
        }

        if (empty($selectedItems)) {
            return BadRequestJsonResponse::invalidField('items', 'Declinations not found');
        }

        $company = new Company($order->getCompanyId());

        try {
            if ($company->isPrivateIndividual()) {
                $this->get('marketplace.order.after_sales_service')->createLitigation($order, $selectedItems, $comments);
            } else {
                $this->get('marketplace.order.after_sales_service')->createAfterSalesServiceRequest($order, $selectedItems, $comments);
            }
        } catch (OrderIsNotComplete $exception) {
            return BadRequestJsonResponse::invalidField('orderId', 'The order must be in COMPLETED status');
        }

        return new JsonResponse('', Response::HTTP_CREATED);
    }
}
