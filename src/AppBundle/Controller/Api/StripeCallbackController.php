<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Wizacha\Marketplace\Payment\Handler\CallbackHandlerInterface;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\Processor\ProcessorCallbackRegistry;

class StripeCallbackController
{
    /** @var ProcessorCallbackRegistry */
    protected $registry;

    public function __construct(ProcessorCallbackRegistry $registry)
    {
        $this->registry = $registry;
    }

    public function __invoke(Request $request): Response
    {
        $payload = $request->request->all();

        if (\array_key_exists('type', $payload) === true && \array_key_exists('object', $payload) === true && $payload['object'] === 'event') {
            $handler = $this->registry->getHandler($payload['type'], PaymentProcessorName::STRIPE());

            if ($handler instanceof CallbackHandlerInterface) {
                $handler->process($payload['data']);
            }
        }

        return new Response('OK', Response::HTTP_OK);
    }
}
