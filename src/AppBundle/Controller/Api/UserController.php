<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\DBAL\Exception\UniqueConstraintViolationException;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\ConflictHttpException;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Security\Core\Encoder\PasswordEncoderInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Tygh\Languages\Languages;
use Wizacha\AppBundle\Controller\Api\Exception\MissingFieldsHttpException;
use Wizacha\AppBundle\Service\WhitelistDomainsService;
use Wizacha\Bridge\Symfony\Response\AccessDeniedJsonResponse;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Bridge\Symfony\Response\NotFoundJsonResponse;
use Wizacha\Marketplace\Basket\BasketService;
use Wizacha\Marketplace\Company\Company;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Country\Exception\InvalidNationalitiesException;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Exception\Unauthorized;
use Wizacha\Marketplace\Traits\AssertCanAccessUserAccountTrait;
use Wizacha\Marketplace\Traits\ContraintViolationTrait;
use Wizacha\Marketplace\Traits\PaginationValidatorTrait;
use Wizacha\Marketplace\Traits\SubscriptionFilterValidatorTrait;
use Wizacha\Marketplace\Traits\UsersAPIFiltersResolver;
use Wizacha\Marketplace\User\AddressBookService;
use Wizacha\Marketplace\User\AddressStatus;
use Wizacha\Marketplace\Country\CountryService;
use Wizacha\Marketplace\User\Event\Yavin\UserUpdated;
use Wizacha\Marketplace\User\Exception\InvalidFieldException;
use Wizacha\Marketplace\User\Exception\PasswordExist;
use Wizacha\Marketplace\User\Exception\PasswordFormatNotValid;
use Wizacha\Marketplace\User\Exception\UserAlreadyExists;
use Wizacha\Marketplace\User\User;
use Wizacha\Marketplace\User\UserFilters;
use Wizacha\Marketplace\User\UserSerializer;
use Wizacha\Marketplace\User\UserService;
use Wizacha\Marketplace\User\UserTitle;
use Wizacha\Marketplace\Traits\UserAddressValidator;
use Wizacha\Marketplace\User\UserType;
use Wizacha\Marketplace\User\Event\UserProfileUpdated;

class UserController extends Controller
{
    use AssertCanAccessUserAccountTrait;
    use PaginationValidatorTrait;
    use SubscriptionFilterValidatorTrait;
    use ContraintViolationTrait;
    use UserAddressValidator;
    use UsersAPIFiltersResolver;

    private UserService $userService;
    private UserSerializer $serializer;
    private BasketService $basketService;
    private ValidatorInterface $validator;
    private AddressBookService $addressBookService;
    private CountryService $countryService;
    private WhitelistDomainsService $whitelistDomainsService;
    private EventDispatcherInterface $eventDispatcher;
    private CompanyService $companyService;
    private PasswordEncoderInterface $passwordEncoder;
    private bool $notifyUserUpdate;

    public function __construct(
        EventDispatcherInterface $eventDispatcher,
        UserService $userService,
        UserSerializer $serializer,
        BasketService $basketService,
        ValidatorInterface $validator,
        AddressBookService $addressBookService,
        CountryService $countryService,
        WhitelistDomainsService $whitelistDomainsService,
        CompanyService $companyService,
        PasswordEncoderInterface $passwordEncoder,
        bool $notifyUserUpdate
    ) {
        $this->eventDispatcher = $eventDispatcher;
        $this->userService = $userService;
        $this->serializer = $serializer;
        $this->basketService = $basketService;
        $this->validator = $validator;
        $this->addressBookService = $addressBookService;
        $this->countryService = $countryService;
        $this->whitelistDomainsService = $whitelistDomainsService;
        $this->companyService = $companyService;
        $this->passwordEncoder = $passwordEncoder;
        $this->notifyUserUpdate = $notifyUserUpdate;
    }

    public function authenticateAction(): JsonResponse
    {
        $user = $this->getUser();

        $apiKey = $user->getApiKey();

        //if invalid apiKey
        if (\is_string($apiKey) === false || \strlen(trim($apiKey)) === 0) {
            $apiKey = $this->userService->createApiKey($user->getId());
        }

        return new JsonResponse([
            'id' => $user->getId(),
            'apiKey' => $apiKey,
        ]);
    }

    /**
     * List all users.
     *
     * Route only accessible to admins.
     */
    public function listAction(Request $request): JsonResponse
    {
        try {
            $options = $this->resolveOptions($request->query->all());
        } catch (\InvalidArgumentException $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        $paginationResolver = $this->UserPaginationResolver($request);

        try {
            [$users, $totalUsers] = $this->userService->findUsersPaginatedWithFilters(new UserFilters($options), $paginationResolver['page'], $paginationResolver['elements']);
        } catch (\UnexpectedValueException $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        return new JsonResponse([
            'results' => array_map(function (User $user) {
                return $this->serializer->serialize($user);
            }, $users),
            'page' => $paginationResolver['page'],
            'elements' => $paginationResolver['elements'],
            'totalElements' => $totalUsers,
        ]);
    }

    public function getAction(int $userId): JsonResponse
    {
        $this->assertCanAccessUserAccount($userId);

        $user = $this->userService->get($userId);

        return new JsonResponse($this->serializeFull($user));
    }

    public function updateAction(int $userId, Request $request): Response
    {
        $this->assertCanAccessToEditUserAccount($userId);

        $user = $this->userService->get($userId);

        $this->assertRequestHasFields($request, [
            'email',
            'firstName',
            'lastName',
        ]);

        try {
            $user->setEmail($request->request->get('email'));
        } catch (\InvalidArgumentException $exception) {
            return BadRequestJsonResponse::invalidField('email', 'invalid');
        }

        if ($request->request->has('extra') === true && \is_array($request->request->get('extra')) === false) {
            return BadRequestJsonResponse::invalidField('extra', 'extra must be an array.');
        }

        $user->setFirstname($request->request->get('firstName'));
        $user->setLastname($request->request->get('lastName'));

        if ($request->request->has('title')) {
            $title = $request->request->get('title');
            if (\is_string($title)) {
                try {
                    $user->setTitle(new UserTitle($request->request->get('title')));
                } catch (\UnexpectedValueException $e) {
                    return BadRequestJsonResponse::invalidField('title', 'The title "' . $request->request->get('title') . '" is invalid.');
                }
            } else {
                if ($user->getTitle() instanceof UserTitle) {
                    $title = new UserTitle($user->getTitle()->getValue());
                }
                $user->setTitle($title);
            }
        }

        if ($request->request->has('birthday') === true) {
            $birthday = $request->request->get('birthday');
            if ($birthday === null) {
                $user->setBirthday($birthday);
            } else {
                $datetime = \DateTime::createFromFormat(UserSerializer::BIRTHDAY_FORMAT, $birthday);
                if ($datetime === false) {
                    return BadRequestJsonResponse::invalidField('birthday', 'The birthday "' . $birthday . '" date format is invalid.');
                }
                $user->setBirthday($datetime);
            }
        }

        try {
            $user = $this->updateNationalities($request, $user);
        } catch (InvalidNationalitiesException $exception) {
            return BadRequestJsonResponse::invalidField('nationalities', $exception->getMessage());
        }

        if ($request->request->has('loyaltyIdentifier')) {
            $user->setLoyaltyIdentifier($request->request->get('loyaltyIdentifier'));
        }

        if ($request->request->has('currencyCode')) {
            try {
                $user->setCurrencyCode($request->request->get('currencyCode'));
            } catch (\Throwable $e) {
                return BadRequestJsonResponse::invalidField('currencyCode', 'The currency code "' . $request->request->get('currencyCode') . '" is invalid, it must be ISO 4217.');
            }
        }

        if (true === $request->request->has('phone')) {
            $user->setPhone($request->request->get('phone'));
        }

        if ($request->request->has('extra') === true) {
            $user->setExtra($request->request->get('extra'));
        }

        try {
            $this->userService->hydrateUserFromApiCall($user, $request);
        } catch (InvalidFieldException $e) {
            return BadRequestJsonResponse::invalidField($e->getInvalidField(), $e->getMessage());
        }

        try {
            fn_update_user_profile($user->getUserId(), $this->userService->convertDataForSql($user, $request->request->all()));
            $this->userService->save($user);
        } catch (UniqueConstraintViolationException $e) {
            throw new ConflictHttpException('This email is already used.', $e);
        }

        $this->eventDispatcher->dispatch(new UserUpdated($user));

        if ($this->notifyUserUpdate === true) {
            $this->eventDispatcher->dispatch(new UserProfileUpdated($user), UserProfileUpdated::class);
        }

        return new JsonResponse($this->serializer->serialize($user));
    }

    public function patchAction(int $userId, Request $request): JsonResponse
    {
        $this->assertCanAccessToEditUserAccount($userId);

        $user = $this->userService->get($userId);

        try {
            $resolver = new OptionsResolver();
            $data = $resolver
                ->setDefined(['currencyCode', 'phone', 'externalIdentifier', 'isProfessional', 'intraEuropeanCommunityVAT', 'company', 'jobTitle', 'comment', 'legalIdentifier', 'loyaltyIdentifier', 'nationalities', 'extra'])
                ->setAllowedTypes('currencyCode', ['string', 'null'])
                ->setAllowedTypes('phone', ['string', 'null'])
                ->setAllowedTypes('externalIdentifier', ['string', 'null'])
                ->setAllowedTypes('intraEuropeanCommunityVAT', ['string', 'null'])
                ->setAllowedTypes('company', ['string', 'null'])
                ->setAllowedTypes('jobTitle', ['string', 'null'])
                ->setAllowedTypes('comment', ['string', 'null'])
                ->setAllowedTypes('legalIdentifier', ['string', 'null'])
                ->setAllowedTypes('loyaltyIdentifier', ['string', 'null'])
                ->setAllowedTypes('extra', ['array'])
                ->resolve($request->request->all());
        } catch (\Throwable $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        if (\array_key_exists('nationalities', $data) === true) {
            $nationalities = $request->request->get('nationalities');

            if (\is_array($data['nationalities']) === false) {
                return new BadRequestJsonResponse('The nationalities [' . \implode(', ', $nationalities) . '] format is invalid');
            }

            if (\count($data['nationalities']) > 3) {
                return new BadRequestJsonResponse('The nationalities [' . \implode(', ', $nationalities) . '] is invalid, max 3 nationalities .');
            }
        }

        if (\array_key_exists('extra', $data) === true) {
            $newExtra = $data['extra'];
            $oldExtra = $user->getExtra();
            if ($newExtra != $oldExtra) {
                $diffKeysExtra = \array_diff_key($oldExtra, $newExtra);
                \array_walk(
                    $diffKeysExtra,
                    function ($value, $key) use (&$newExtra, $oldExtra) {
                        $newExtra[$key] = $oldExtra[$key];
                    }
                );
            }
            $data['extra'] = $newExtra;
        }

        try {
            $data = $this->userService->convertDataForSql($user, $data);
            fn_update_user_profile($user->getUserId(), $data);
            $this->userService->patch($user, $data);

            $this->eventDispatcher->dispatch(new UserUpdated($user));

            if ($this->notifyUserUpdate === true) {
                $this->eventDispatcher->dispatch(new UserProfileUpdated($user), UserProfileUpdated::class);
            }

            return new JsonResponse($this->serializer->serialize($user));
        } catch (\Throwable $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }
    }

    /**
     * Register a new user
     * @throws NotFound
     * @throws \Wizacha\Marketplace\User\Exception\CannotCreateUser
     */
    public function registerAction(Request $request): JsonResponse
    {
        $this->assertRequestHasFields($request, [
            'email',
            'password',
        ]);

        if (!filter_var($request->request->get('email'), FILTER_VALIDATE_EMAIL)) {
            return new JsonResponse('An email is required in request body', Response::HTTP_BAD_REQUEST);
        }

        if ($request->request->has('extra') === true && \is_array($request->request->get('extra')) === false) {
            return BadRequestJsonResponse::invalidField('extra', 'extra must be an array.');
        }

        $langCode = $request->getDefaultLocale();
        if ($request->request->has('lang') === true) {
            // List of available languages
            $languages = Languages::getAvailable();

            if (\array_key_exists($request->request->get('lang'), $languages) === true) {
                $langCode = $request->request->get('lang');
            }
        }

        try {
            $userId = $this->get('marketplace.user.user_service')->createUser(
                $request->request->get('email'),
                $request->request->get('password'),
                $request->request->get('firstName'),
                $request->request->get('lastName'),
                null,
                true,
                $langCode
            );

            $user = $this->userService->get($userId);

            if ($request->request->has('title')) {
                try {
                    $user->setTitle(new UserTitle($request->request->get('title')));
                } catch (\UnexpectedValueException $e) {
                    return BadRequestJsonResponse::invalidField('title', 'The title "' . $request->request->get('title') . '" is invalid.');
                }
            }

            if ($request->request->has('birthday')) {
                $datetime = \DateTime::createFromFormat(UserSerializer::BIRTHDAY_FORMAT, $request->request->get('birthday'));

                if ($datetime === false) {
                    return BadRequestJsonResponse::invalidField('birthday', 'The birthday "' . $request->request->get('birthday') . '" is invalid.');
                }

                $user->setBirthday($datetime);
            }

            try {
                $user = $this->updateNationalities($request, $user);
            } catch (InvalidNationalitiesException $exception) {
                return BadRequestJsonResponse::invalidField('nationalities', $exception->getMessage());
            }

            if ($request->request->has('loyaltyIdentifier')) {
                $user->setLoyaltyIdentifier($request->request->get('loyaltyIdentifier'));
            }

            if ($request->request->has('companyId') && null !== $request->request->get('companyId')) {
                $company = $this->get(CompanyService::class)->get($request->request->get('companyId'));
                if (!($company instanceof Company)) {
                    return BadRequestJsonResponse::invalidField('companyId', 'The ID "' . $request->request->get('companyId') . '" does not match any company');
                }

                $user->setCompanyId($company->getId());
                $user->setCompany($company->getName());
                $user->setStatus('D');//if the user has defined its company, it is deactivated by default
            }

            if (true === $request->request->has('phone')) {
                $user->setPhone($request->request->get('phone'));
            }

            if ($request->request->has('extra') === true) {
                $user->setExtra($request->request->get('extra'));
            }

            try {
                $this->userService->hydrateUserFromApiCall($user, $request);
            } catch (InvalidFieldException $e) {
                return BadRequestJsonResponse::invalidField($e->getInvalidField(), $e->getMessage());
            }

            fn_update_user_profile($user->getUserId(), $this->userService->convertDataForSql($user, $request->request->all()));
            $this->userService->save($user);

            if ($request->request->has('billing') && $request->request->has('shipping')) {
                $this->get('marketplace.user.user_service')->updateAddresses(
                    $userId,
                    (array) $request->request->get('billing'),
                    (array) $request->request->get('shipping')
                );
            }

            return new JsonResponse(['id' => $userId], Response::HTTP_CREATED);
        } catch (UserAlreadyExists $e) {
            return new JsonResponse('The user already exists.', Response::HTTP_CONFLICT);
        } catch (PasswordFormatNotValid $exception) {
            return new JsonResponse(
                [
                    "code" => 'error_password_format_not_valid',
                    "message" => __('error_password_format_not_valid')
                ],
                Response::HTTP_BAD_REQUEST
            );
        }
    }

    public function recoverPasswordAction(Request $request): JsonResponse
    {
        $email = $request->request->get('email');
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return new JsonResponse('An email is required in request body', Response::HTTP_BAD_REQUEST);
        }

        $recoverBaseUrl = $request->request->get('recoverBaseUrl', null);
        if (!empty($recoverBaseUrl) && !filter_var($recoverBaseUrl, FILTER_VALIDATE_URL)) {
            return new JsonResponse('Invalid URL given for recoverBaseUrl', Response::HTTP_BAD_REQUEST);
        }

        if (\strlen($recoverBaseUrl) > 0 && $this->whitelistDomainsService->isWhitedDomainHost($recoverBaseUrl) === false) {
            return new JsonResponse('Invalid URL given for recoverBaseUrl, Domain is not whitelisted', Response::HTTP_BAD_REQUEST);
        }

        $this->userService->recoverPassword($email, $recoverBaseUrl, $this->notifyUserUpdate);

        return new JsonResponse('', Response::HTTP_NO_CONTENT);
    }

    public function changePasswordAction(int $userId, Request $request): JsonResponse
    {
        $user = $this->assertCanAccessToEditUserAccount($userId);
        $targetUser = $this->userService->get($userId); // Check user exists (throws NotFound)
        $requiredFields = [
            'password',
        ];
        $isOldPasswordRequired = $this->getParameter('feature.security.request_old_password_on_change');

        if ($isOldPasswordRequired === true && $user->isMarketplaceAdministrator() !== true) {
            $requiredFields[] = 'currentPassword';
        }
        $this->assertRequestHasFields($request, $requiredFields);

        if ($request->request->has('currentPassword') === true) {
            $isPasswordValid = $this->passwordEncoder->isPasswordValid(
                $targetUser->getPassword(),
                $request->request->get('currentPassword'),
                $targetUser->getSalt()
            );

            if ($isPasswordValid === false) {
                return BadRequestJsonResponse::invalidField(
                    'currentPassword',
                    'Invalid password'
                );
            }
        }

        try {
            $this->get('marketplace.user.user_service')->changePassword($userId, $request->request->get('password'), $this->notifyUserUpdate);
        } catch (PasswordFormatNotValid $exception) {
            return new JsonResponse(
                [
                    "code" => 'error_password_format_not_valid',
                    "message" => __('error_password_format_not_valid')
                ],
                Response::HTTP_BAD_REQUEST
            );
        } catch (PasswordExist $exception) {
            return new JsonResponse(
                [
                    "code" => 'error_password_format_not_valid',
                    "message" => $exception->getMessage()
                ],
                Response::HTTP_BAD_REQUEST
            );
        }

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }

    public function changePasswordWithTokenAction(Request $request): JsonResponse
    {
        $this->assertRequestHasFields($request, [
            'password',
            'token',
        ]);

        try {
            $passwordChanged = $this->get('marketplace.user.user_service')->changePasswordWithRecoveryToken(
                $request->request->get('token'),
                $request->request->get('password'),
                $this->notifyUserUpdate
            );
        } catch (PasswordFormatNotValid $exception) {
            return new JsonResponse(
                [
                    "code" => 'error_password_format_not_valid',
                    "message" => __('error_password_format_not_valid')
                ],
                Response::HTTP_BAD_REQUEST
            );
        } catch (PasswordExist $exception) {
            return new JsonResponse(
                [
                    "code" => 'error_password_format_not_valid',
                    "message" => $exception->getMessage()
                ],
                Response::HTTP_BAD_REQUEST
            );
        }

        return new JsonResponse(null, $passwordChanged ? Response::HTTP_NO_CONTENT : Response::HTTP_NOT_FOUND);
    }

    public function updateAddressesAction(int $userId, Request $request): JsonResponse
    {
        $this->assertCanAccessToEditUserAccount($userId);

        $user = $this->userService->get($userId); // Check user exists (throws NotFound)
        $this->assertRequestHasFields($request, [
            'billing',
            'shipping',
        ]);

        if (\array_key_exists('address_id', (array) $request->request->get('billing')) === true) {
            $addressId = $request->request->get('billing')['address_id'];

            try {
                $addressBook = $this->addressBookService->get($addressId);
            } catch (NotFound $e) {
                return BadRequestJsonResponse::invalidField('address_id', 'Address Id not found.');
            }

            if ($user->getAddressBook()->contains($addressBook) === true) {
                $userBillingAddress = $addressBook->jsonSerialize();
                $userBillingAddress['address_id'] = $userBillingAddress['id'];
                unset($userBillingAddress['id']);
            } else {
                return BadRequestJsonResponse::invalidField('address_id', 'Address Id does not belong to user.');
            }
        } elseif ($user->getBillingAddress()->getFieldValue('address_id') === '') {
            $userBillingAddress = (array) $request->request->get('billing');
        } else {
            return new JsonResponse('Address cannot be modified.', Response::HTTP_BAD_REQUEST);
        }

        if (\array_key_exists('address_id', (array) $request->request->get('shipping')) === true) {
            $addressId = $request->request->get('shipping')['address_id'];

            try {
                $addressBook = $this->addressBookService->get($addressId);
            } catch (NotFound $e) {
                return BadRequestJsonResponse::invalidField('address_id', 'Address Id not found.');
            }

            if ($user->getAddressBook()->contains($addressBook) === true) {
                $userShippingAddress = $addressBook->jsonSerialize();
                $userShippingAddress['address_id'] = $userShippingAddress['id'];
                unset($userShippingAddress['id']);
            } else {
                return BadRequestJsonResponse::invalidField('address_id', 'Address Id does not belong to user.');
            }
        } elseif ($user->getShippingAddress()->getFieldValue('address_id') === '') {
            $userShippingAddress = (array) $request->request->get('shipping');
        } else {
            return new JsonResponse('Address cannot be modified.', Response::HTTP_BAD_REQUEST);
        }

        $this->get('marketplace.user.user_service')->updateAddresses(
            $userId,
            $userBillingAddress,
            $userShippingAddress
        );

        return new JsonResponse('', Response::HTTP_OK);
    }

    public function getBasketAction(int $userId): JsonResponse
    {
        $this->assertCanAccessUserAccount($userId);

        $user = $this->userService->get($userId);

        return new JsonResponse([
            'id' => $user->getBasketId(),
        ]);
    }

    public function postBasketAction(int $userId, Request $request): JsonResponse
    {
        $this->assertCanAccessUserAccount($userId);

        $basketId = (string) $request->request->get('id');
        if (empty($basketId)) {
            $basketId = null;
        } else {
            $basketId = $this->basketService->getReadmodel($basketId)->getId();
        }

        $user = $this->userService->get($userId);
        $user->setBasketId($basketId);
        $this->userService->save($user);

        return new JsonResponse('', Response::HTTP_NO_CONTENT);
    }

    public function deleteBasketAction(int $userId): Response
    {
        $this->assertCanAccessToEditUserAccount($userId);

        $user = $this->userService->get($userId);
        $basketId = $user->getBasketId();

        if (\is_string($basketId) && $this->basketService->assertExists($basketId)) {
            $this->basketService->deleteAllProductsInBasket($basketId);

            $user->setBasketId(null);
            $this->userService->save($user);
        }

        return new Response('', Response::HTTP_NO_CONTENT);
    }

    public function enableAction(int $userId): JsonResponse
    {
        $this->assertCanAccessToEditUserAccount($userId);

        if ($this->userService->get($userId)->isLocked()) {
            return new JsonResponse(
                'Can\'t change status, this user is locked',
                Response::HTTP_UNAVAILABLE_FOR_LEGAL_REASONS
            );
        }

        if ($this->getUser()->getId() === $userId) {
            return new AccessDeniedJsonResponse('The user can\'t enable itself');
        }

        $this->userService->enable($userId);

        return new JsonResponse('', Response::HTTP_NO_CONTENT);
    }

    public function disableAction(int $userId): JsonResponse
    {
        $this->assertCanAccessToEditUserAccount($userId);

        if ($this->userService->get($userId)->isLocked()) {
            return new JsonResponse(
                'Can\'t change status, this user is locked',
                Response::HTTP_UNAVAILABLE_FOR_LEGAL_REASONS
            );
        }

        if ($this->getUser()->getId() === $userId) {
            return new AccessDeniedJsonResponse('The user can\'t disable itself');
        }

        $this->userService->disable($userId);

        return new JsonResponse('', Response::HTTP_NO_CONTENT);
    }

    public function getOrganisationAction(int $userId): JsonResponse
    {
        $this->assertCanAccessUserAccount($userId);

        return new JsonResponse($this->userService->get($userId)->getOrganisation()->expose());
    }

    public function getSubscriptionsAction(Request $request, int $userId): JsonResponse
    {
        $this->assertCanAccessUserAccount($userId);

        $violationsPagination = $this->paginationValidator($request);

        if (\count($violationsPagination) > 0) {
            return $this->badRequest($violationsPagination);
        }

        $pagination = $this->paginationResolver($request);
        $violationsFilters = $this->subscriptionFilterValidator($request);

        if (\count($violationsFilters) > 0) {
            return $this->badRequest($violationsFilters);
        }

        $user = $this->userService->get($userId);

        [1 => $totalCount] = $this->userService->getSubscriptions($user);
        [$items] = $this->userService->getSubscriptions(
            $user,
            $pagination['limit'],
            $pagination['offset'],
            $request->query->all()
        );

        return new JsonResponse([
            "limit" => $pagination['limit'],
            "offset" => $pagination['offset'],
            "total" => $totalCount,
            "items" => $items,
        ]);
    }

    public function ReplaceAddressBooksAction(Request $request, int $userId, string $addressId): JsonResponse
    {
        try {
            $this->assertCanAccessUserAddressBook($userId);
        } catch (Unauthorized $e) {
            return new AccessDeniedJsonResponse();
        }

        try {
            $this->addressBookService->get($addressId);
        } catch (NotFound $e) {
            return new JsonResponse(["message" => $e->getMessage()], Response::HTTP_NOT_FOUND);
        }

        try {
            $this->assertUserHaveAddressBook($userId, $addressId);
        } catch (NotFound $e) {
            return new AccessDeniedJsonResponse($e->getMessage());
        }

        try {
            $violationsUserAddress = $this->UserAddressFilterValidator($request);

            if (\count($violationsUserAddress) > 0) {
                return $this->invalidFields($violationsUserAddress);
            }

            $user = $this->userService->get($userId);
            $addressData = $request->request->all();
            if ($user->getBillingAddress()->getFieldValue('address_id') === $addressId && $user->getShippingAddress()->getFieldValue('address_id') === $addressId) {
                $addressData['principal_address'] = AddressStatus::BILLING_SHIPPING()->getValue();
            } elseif ($user->getBillingAddress()->getFieldValue('address_id') === $addressId) {
                $addressData['principal_address'] = AddressStatus::BILLING()->getValue();
            } elseif ($user->getShippingAddress()->getFieldValue('address_id') === $addressId) {
                $addressData['principal_address'] = AddressStatus::SHIPPING()->getValue();
            }
            $this->addressBookService->save($userId, $addressData, false, $addressId);

            return new JsonResponse('OK', Response::HTTP_OK);
        } catch (\Exception $e) {
            return new JsonResponse(["message" => $e->getMessage()], Response::HTTP_BAD_REQUEST);
        }
    }

    /**  List all address for an user. */
    public function ListAddressBooksAction(Request $request, int $userId): JsonResponse
    {
        try {
            $this->assertCanAccessUserAddressBook($userId);
        } catch (Unauthorized $e) {
            return new AccessDeniedJsonResponse();
        }

        $violationsPagination = $this->paginationValidator($request);

        if (\count($violationsPagination) > 0) {
            return $this->badRequest($violationsPagination);
        }

        if ($request->query->has('limit') === false) {
            $request->query->add(['limit' => 20]);
        }

        $pagination = $this->paginationResolver($request);

        $user = $this->userService->get($userId);

        [$items, $totalCount] = $this->userService->findAddressBook(
            $user,
            $pagination['limit'],
            $pagination['offset']
        );

        return new JsonResponse([
            "limit" => $pagination['limit'],
            "offset" => $pagination['offset'],
            "total" => $totalCount,
            "items" => $items,
        ]);
    }

    public function removeAddressBookAction(int $userId, string $addressId): JsonResponse
    {
        try {
            $this->assertCanAccessUserAddressBook($userId);
        } catch (Unauthorized $e) {
            return new AccessDeniedJsonResponse();
        }

        try {
            $this->addressBookService->get($addressId);
        } catch (NotFound $e) {
            return new JsonResponse(["message" => $e->getMessage()], Response::HTTP_NOT_FOUND);
        }

        try {
            $this->assertUserHaveAddressBook($userId, $addressId);
        } catch (NotFound $e) {
            return new AccessDeniedJsonResponse($e->getMessage());
        }

        try {
            $this->addressBookService->delete($userId, $addressId);

            return new JsonResponse('', Response::HTTP_NO_CONTENT);
        } catch (NotFound $e) {
            return new JsonResponse(["message" => $e->getMessage()], Response::HTTP_NOT_FOUND);
        }
    }

    public function postAddressInAddressBookAction(int $userId, Request $request): JsonResponse
    {
        try {
            $this->assertCanAccessUserAddressBook($userId);
        } catch (Unauthorized $e) {
            return new AccessDeniedJsonResponse();
        }

        $user = $this->userService->get($userId);

        if (\count($user->getAddressBook()) === 20) {
            return new BadRequestJsonResponse('Maximum number of addresses reached.');
        }

        $violations = $this->userAddressBookCreatedFilterValidator($request);

        if (\count($violations) > 0) {
            return $this->invalidFields($violations);
        }

        $addressData = $request->request->all();

        if ($request->request->has('country') === true && $this->countryService->isValidCountryCode($request->request->get('country')) === false) {
            return BadRequestJsonResponse::invalidField('country', 'Invalid country Code');
        }

        if ($request->request->has('fromUserAddress') === true) {
            if ($addressData['fromUserAddress'] !== 'billing' && $addressData['fromUserAddress'] !== 'shipping') {
                return BadRequestJsonResponse::invalidField('fromUserAddress', 'fromUserAddress must be billing or shipping.');
            }

            $addressFunctionName = 'get' . ucfirst($addressData['fromUserAddress']) . 'Address';
            $address = $user->$addressFunctionName();

            if ($address->getFieldValue('address_id') !== "") {
                return new JsonResponse('User address already in Address Book.', Response::HTTP_BAD_REQUEST);
            }

            $status = strtoupper($addressData['fromUserAddress']);
            $addressData = $address->getAllFields();
            $addressData['principal_address'] = AddressStatus::$status();
        }

        $addressId = $this->addressBookService->save($userId, $addressData, false, '0');

        return new JsonResponse($addressId, Response::HTTP_CREATED);
    }

    public function deleteExtraAction(int $userId, Request $request): JsonResponse
    {
        $this->assertCanAccessToEditUserAccount($userId);

        $user = $this->userService->get($userId);

        try {
            $resolver = new OptionsResolver();
            $data = $resolver
                ->setRequired(['extra'])
                ->setAllowedTypes('extra', ['array'])
                ->resolve($request->request->all());
        } catch (\Throwable $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }
        $extra = $user->getExtra();
        foreach ($request->request->get('extra') as $field) {
            unset($extra[$field]);
        }
        $data['extra'] = $extra;

        $data = $this->userService->convertDataForSql($user, $data);
        fn_update_user_profile($user->getUserId(), $data);
        $this->userService->patch($user, $data);

        return new JsonResponse('', Response::HTTP_NO_CONTENT);
    }

    public function affiliateUserToCompanyAction(Request $request): JsonResponse
    {
        $this->assertRequestHasFields($request, [
            'email',
            'companyId'
        ]);

        $data = [
            'companyId' => $request->request->get('companyId')
        ];

        try {
            if ($request->request->get('companyId') === null) { // disaffiliate user
                $user = $this->assertCanAccessToDisaffiliateUser($request->request->get('email'));
                $data['userType'] =  UserType::CLIENT()->getValue();
            } else { // affiliate user
                $user = $this->assertCanAccessToAffiliateUser($request->request->get('email'), \intval($request->request->get('companyId')));
                $data['userType'] =  UserType::VENDOR()->getValue();
            }
        } catch (Forbidden $exception) {
            return new AccessDeniedJsonResponse($exception->getMessage());
        } catch (NotFound $e) {
            return new NotFoundJsonResponse($e->getMessage());
        }

        $this->userService->patch($user, $data);

        unset($data['userType']);
        $data['email'] = $request->request->get('email');

        return new JsonResponse($data, Response::HTTP_OK);
    }

    public function revokeAction(): JsonResponse
    {
        $user = $this->getUser();
        $this->userService->createApiKey($user->getId());

        return new JsonResponse(__('new_api_token_generated'), Response::HTTP_OK);
    }

    private function assertRequestHasFields(Request $request, array $fields)
    {
        $missingFields = array_filter($fields, function (string $field) use ($request) {
            return !$request->request->has($field);
        });
        if (!empty($missingFields)) {
            throw new MissingFieldsHttpException(...$missingFields);
        }
    }

    private function updateNationalities(Request $request, User $user): User
    {
        if ($request->request->has('nationalities') === true) {
            $nationalities = $request->request->get('nationalities');

            if (\is_array($nationalities) === false) {
                throw new InvalidNationalitiesException('The nationalities [' . \implode(', ', $nationalities) . '] format is invalid');
            }

            if (\count($nationalities) > 3) {
                throw new InvalidNationalitiesException('The nationalities [' . \implode(', ', $nationalities) . '] is invalid, max 3 nationalities .');
            }

            $nationalities = $this->countryService->assertValidArrayCodeA3($nationalities);

            $newNationalities = new ArrayCollection($nationalities);

            $user->setNationalities($newNationalities);
        }

        return $user;
    }

    /** @return string[] */
    private function serializeFull(User $user): array
    {
        $userSerializer = $this->serializer->serialize($user);
        if ($this->assertLoggedUserIsTarget($user->getUserId()) === true) {
            $userSerializer['apiKeyUpdatedAt'] = \is_null($user->getApiKeyUpdatedAt()) === false ? $user->getApiKeyUpdatedAt()->format(\DateTime::RFC3339) : '';
        }

        return $userSerializer;
    }
}
