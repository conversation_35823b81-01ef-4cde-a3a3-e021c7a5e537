<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api;

use Tygh\Api\Response;

class Taxes extends \Tygh\Api\Entities\Taxes
{
    public function index($id = 0, $params = array())
    {
        if ($id) {
            return [
                'status' => Response::STATUS_FORBIDDEN,
                'data' => []
            ];
        }

        $return = parent::index();
        $return['data'] = array_filter(
            $return['data'],
            function ($tax) {
                return $tax['status'] == 'A';
            }
        );
        array_walk(
            $return['data'],
            function (&$_data) {
                $_data = fn_w_object_filter($_data, 'tax', 'api', 'display_objects');
                fn_w_fields_cast($_data);

                $_data['id'] = $_data['tax_id'];
                $_data['code'] = $_data['regnumber'];
                unset($_data['regnumber']);

                $_data['is_enabled'] = $_data['status'] === 'A';
                unset($_data['status']);

                $_data['name'] = $_data['tax'];

                $_data['rate'] = $_data['rate_value'];
                unset($_data['rate_value']);

                // Change order of keys
                $order = ['id', 'tax_id', 'priority', 'code', 'is_enabled', 'tax', 'name', 'rate'];
                $_data = array_merge(array_flip($order), $_data);
            }
        );
        return $return;
    }

    public function privileges()
    {
        return array(
            'index'  => true,
        );
    }
}
