<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\Order;

use Wizacha\AppBundle\Controller\Api\Error\ApiErrorResponseProvider;
use Wizacha\Marketplace\Exception\ErrorCode;
use Wizacha\AppBundle\Controller\Api\Error\ErrorResponse;

final class InvalidHandDeliveryCode extends \Exception implements ApiErrorResponseProvider
{
    public function toApiErrorResponse(): ErrorResponse
    {
        return new ErrorResponse(
            ErrorCode::INVALID_HAND_DELIVERY_CODE(),
            'Invalid hand-delivery code'
        );
    }
}
