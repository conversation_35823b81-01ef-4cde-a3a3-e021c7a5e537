<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\Order;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Wizacha\Marketplace\Order\Action\MarkAsDelivered;
use Wizacha\Marketplace\Order\Action\MarkAsShipped;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\User\User;
use Wizacha\OrderStatus;

final class HandDeliveryController extends Controller
{
    /**
     * @var OrderService
     */
    private $orderService;
    /**
     * @var MarkAsShipped
     */
    private $markAsShippedAction;
    /**
     * @var MarkAsDelivered
     */
    private $markAsDeliveredAction;

    public function __construct(OrderService $orderService, MarkAsShipped $markAsShippedAction, MarkAsDelivered $markAsDeliveredAction)
    {
        $this->orderService = $orderService;
        $this->markAsShippedAction = $markAsShippedAction;
        $this->markAsDeliveredAction = $markAsDeliveredAction;
    }

    public function reportHandDeliveryAction(Request $request, int $orderId): Response
    {
        $order = $this->orderService->getOrder($orderId);

        /** @var User $user */
        $user = $this->getUser();
        if ($user->getCompanyId() !== $order->getCompanyId()) {
            throw new AccessDeniedHttpException();
        }

        if (!$order->isHandDelivered()) {
            throw new BadRequestHttpException('order is not hand-delivered');
        }

        $handDeliveryCode = $order->getHandDeliveryCode();
        if ($handDeliveryCode !== null) {
            if (!$handDeliveryCode->validate($request->get('code', ''))) {
                throw new InvalidHandDeliveryCode();
            }
        }
        fn_update_shipment(
            [
                'order_id' => $orderId,
                'shipping_id' => $order->getShippingId(),
                'tracking_number' => uniqid(),
            ],
            0,
            0,
            true,
            [],
            true
        );

        if ($order->getCompany()->isPrivateIndividual()) { // C2C
            if ($this->markAsDeliveredAction->isAllowed($order)) {
                $this->markAsDeliveredAction->execute($order);
            }
        } else { // B2C
            if ($this->markAsShippedAction->isAllowed($order)) {
                $this->markAsShippedAction->execute($order);
            }
        }

        OrderStatus::automaticUpdate($orderId);

        return new Response(null, Response::HTTP_CREATED);
    }

    public function getHandDeliveryCodeListAction(int $orderId): Response
    {
        $order = $this->orderService->getOrder($orderId);

        /** @var User $user */
        $user = $this->getUser();
        if ($user->getCompanyId() !== $order->getCompanyId() && !$this->isGranted('ROLE_ADMIN')) {
            throw new AccessDeniedHttpException();
        }

        if (!$order->isHandDelivered()) {
            throw new BadRequestHttpException('order is not hand-delivered');
        }

        $handDeliveryCode = $order->getHandDeliveryCode();
        if ($handDeliveryCode === null) {
            throw new BadRequestHttpException('order has not hand-delivered code');
        }

        return new JsonResponse($handDeliveryCode->partial());
    }
}
