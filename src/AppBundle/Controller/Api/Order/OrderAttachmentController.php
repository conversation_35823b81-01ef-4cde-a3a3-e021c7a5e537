<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\Order;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Wizacha\AppBundle\Security\User\ApiSecurityUser;
use Wizacha\Bridge\Symfony\Response\AccessDeniedJsonResponse;
use Wizacha\Bridge\Symfony\Response\InternalServerErrorJsonResponse;
use Wizacha\Bridge\Symfony\Response\NotFoundJsonResponse;
use Wizacha\Marketplace\Order\Exception\OrderNotFound;
use Wizacha\Marketplace\Order\OrderAttachment\Enum\DownloadAttachmentApiRouteKey;
use Wizacha\Marketplace\Order\OrderAttachment\Exception\DownloadFailException;
use Wizacha\Marketplace\Order\OrderAttachment\Service\OrderAttachmentService;
use Wizacha\Marketplace\Traits\ContraintViolationTrait;
use Wizacha\Marketplace\Traits\PaginationValidatorTrait;
use Wizacha\Storage\StorageService;

class OrderAttachmentController extends Controller
{
    use PaginationValidatorTrait;
    use ContraintViolationTrait;

    /** @var OrderAttachmentService  */
    private $attachmentService;

    /** @var ValidatorInterface */
    private $validator;

    private StorageService $orderAttachementsStorageService;

    public function __construct(OrderAttachmentService $attachmentService, ValidatorInterface $validator, StorageService $orderAttachementsStorageService)
    {
        $this->attachmentService = $attachmentService;
        $this->validator = $validator;
        $this->orderAttachementsStorageService = $orderAttachementsStorageService;
    }

    public function postOrderAttachmentAction(int $orderId, Request $request): JsonResponse
    {
        /** @var ApiSecurityUser $user */
        $user = $this->getUser();

        $orderAttachment = $this->attachmentService->create(
            \array_merge($request->request->all(), $request->files->all()),
            $orderId,
            $user
        );

        return new JsonResponse($orderAttachment, Response::HTTP_CREATED);
    }

    public function getOrderAttachmentAction(int $orderId, string $attachmentId): JsonResponse
    {
        /** @var ApiSecurityUser $user */
        $user = $this->getUser();

        // Only vendors and administrators can retrieve attachments
        $this->attachmentService->assertCanAttachFile($user, $orderId);

        $orderAttachment = $this->attachmentService->get($orderId, $attachmentId);

        return new JsonResponse($orderAttachment, Response::HTTP_OK);
    }

    public function getOrderAttachmentByUserAction(int $orderId, string $attachmentId): JsonResponse
    {
        /** @var ApiSecurityUser $user */
        $user = $this->getUser();

        // only the owner of the order can retrieve attachments
        try {
            $this->attachmentService->assertCanUserAccessAttachFile($user, $orderId);
        } catch (OrderNotFound $e) {
            return new AccessDeniedJsonResponse($e->getMessage());
        }

        try {
            $orderAttachment = $this->attachmentService->get($orderId, $attachmentId);
        } catch (NotFoundHttpException $e) {
            return new NotFoundJsonResponse($e->getMessage());
        }

        $orderAttachment = $this->attachmentService->summaryAttachment([$orderAttachment], DownloadAttachmentApiRouteKey::USER_ROUTE_DOWNLOAD());

        return new JsonResponse($orderAttachment[0], Response::HTTP_OK);
    }

    public function listOrderAttachmentsAction(int $orderId, Request $request): JsonResponse
    {
        $violationsPagination = $this->paginationValidator($request);

        if (\count($violationsPagination) > 0) {
            return $this->badRequest($violationsPagination);
        }

        $pagination = $this->paginationResolver($request);

        /** @var ApiSecurityUser $user */
        $user = $this->getUser();

        $this->attachmentService->assertCanAttachFile($user, $orderId);

        $orderAttachments = $this->attachmentService->list(
            $orderId,
            \array_merge($request->query->all(), $pagination)
        );

        return new JsonResponse(
            [
                'limit' => $pagination['limit'],
                'offset' => $pagination['offset'],
                'total' => \count($orderAttachments),
                'items' => $orderAttachments,
            ],
            Response::HTTP_OK
        );
    }

    public function downloadOrderAttachmentByUserAction(int $orderId, string $attachmentId): Response
    {
        /** @var ApiSecurityUser $user */
        $user = $this->getUser();

        // only the owner of the order can retrieve attachments
        try {
            $this->attachmentService->assertCanUserAccessAttachFile($user, $orderId);
        } catch (OrderNotFound $e) {
            return new AccessDeniedJsonResponse($e->getMessage());
        }

        try {
            return $this->downloadAttachment($orderId, $attachmentId);
        } catch (NotFoundHttpException $e) {
            return new NotFoundJsonResponse($e->getMessage());
        } catch (DownloadFailException $e) {
            return new InternalServerErrorJsonResponse($e->getMessage());
        }
    }

    public function downloadOrderAttachmentAction(int $orderId, string $attachmentId): Response
    {
        /** @var ApiSecurityUser $user */
        $user = $this->getUser();

        // Only vendors and administrators can retrieve attachments
        $this->attachmentService->assertCanAttachFile($user, $orderId);

        return $this->downloadAttachment($orderId, $attachmentId);
    }

    public function deleteOrderAttachmentAction(int $orderId, string $attachmentId): JsonResponse
    {
        /** @var ApiSecurityUser $user */
        $user = $this->getUser();

        $this->attachmentService->delete($orderId, $attachmentId, $user);

        return new JsonResponse('', Response::HTTP_NO_CONTENT);
    }

    public function updateOrderAttachmentAction(int $orderId, string $attachmentId, Request $request): JsonResponse
    {
        /** @var ApiSecurityUser $user */
        $user = $this->getUser();

        $orderAttachment = $this->attachmentService->update(
            $request->request->all(),
            $orderId,
            $attachmentId,
            $user
        );

        return new JsonResponse($orderAttachment, Response::HTTP_CREATED);
    }

    /**
     * @param int $orderId
     * @param string $attachmentId
     *
     * @return Response
     * @throws DownloadFailException
     */
    protected function downloadAttachment(int $orderId, string $attachmentId): Response
    {
        $orderAttachment = $this->attachmentService->get($orderId, $attachmentId);

        try {
            return $this->orderAttachementsStorageService
                ->get(
                    $orderAttachment->getOrderId() . '/'
                    . $orderAttachment->getId() . '/'
                    . $orderAttachment->getFilename()
                )
                ;
        } catch (FileException $exception) {
            throw new DownloadFailException();
        }
    }
}
