<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\Order;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Wizacha\Marketplace\Order\OrderAction\Entity\OrderAction;
use Wizacha\Marketplace\Order\OrderAction\Exception\OrderActionNotFound;
use Wizacha\Marketplace\Order\OrderAction\Service\OrderActionService;

class OrderActionController extends AbstractController
{
    private OrderActionService $orderActionService;

    public function __construct(
        OrderActionService $orderActionService
    ) {
        $this->orderActionService = $orderActionService;
    }

    public function listOrderActionsAction(int $orderId): JsonResponse
    {
        $orderActions = $this->orderActionService->getByOrderId($orderId);
        $exposedOrderActions = \array_map(static function (OrderAction $orderAction) {
                return $orderAction->expose();
        }, $orderActions);

        return new JsonResponse($exposedOrderActions, Response::HTTP_OK);
    }
}
