<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\Order;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Component\MondialRelay\Client;
use Wizacha\Component\MondialRelay\Model\Contact;
use Wizacha\Marketplace\Order\OrderItem;
use Wizacha\Marketplace\Order\OrderService;

class MondialRelayController extends Controller
{
    private const MONDIAL_RELAY_ID = 17;

    /**
     * @var OrderService
     */
    private $orderService;

    /**
     * @var Client
     */
    private $mondialRelay;

    public function __construct(OrderService $orderService, Client $mondialRelay)
    {
        $this->orderService = $orderService;
        $this->mondialRelay = $mondialRelay;
    }

    public function generateLabelAction(Request $request, int $orderId): Response
    {
        $order = $this->orderService->getOrder($orderId);

        if ($order->getShippingId() !== static::MONDIAL_RELAY_ID) {
            throw new BadRequestHttpException("This order is not shipped by Mondial Relay");
        }

        /** @var User $user */
        $user = $this->getUser();
        if ($user->getCompanyId() !== $order->getCompanyId() && !$this->isGranted('ROLE_ADMIN')) {
            throw new AccessDeniedHttpException();
        }

        $data = $this->getJsonContent($request);

        $productService = $this->get('marketplace.pim.product.service');

        // The weight is in kg in the backend.
        // MR API expects g.
        $weight = 0.;
        foreach ($data['products'] as $itemId => $amount) {
            $item = $this->getItem($order->getItems(), $itemId);
            $product = $productService->get($item->getProductId());
            $weight += (float) $product->getWeight() * (int) $amount * 1000;
        }

        $company = $order->getCompany();

        $shipper = (new Contact())
            ->setName($company->getName())
            ->setAddress($company->getAddress())
            ->setZipCode($company->getZipcode())
            ->setCity($company->getCity())
            ->setCountry(strtoupper($company->getCountry()))
            ->setEmail($company->getEmail())
            ->setPhone($company->getPhoneNumber())
        ;

        $shippingAddress = $order->getShippingAddress();

        $fullName = $shippingAddress->getFirstname() . ' ' . $shippingAddress->getLastname();

        $recipient = (new Contact())
            ->setTitle($shippingAddress->getTitle()->__toString())
            ->setName($shippingAddress->getCompany() ?? $fullName)
            ->setAddress($shippingAddress->getAddress())
            ->setAddress2($shippingAddress->getAddress2())
            ->setZipCode($shippingAddress->getZipcode())
            ->setCity($shippingAddress->getCity())
            ->setCountry(strtoupper($shippingAddress->getCountry()))
            ->setEmail($order->getEmail())
            //->setPhone($orderInfo['s_phone'])
        ;

        $result = $this->mondialRelay->createShipment($shipper, $recipient, $orderId, $order->getUser()->getUserId(), \intval($weight), $shippingAddress->getPickupPointId());

        return new JsonResponse($result);
    }

    private function getJsonContent(Request $request): array
    {
        $data = json_decode($request->getContent(), true);
        if (!\is_array($data)) {
            throw new BadRequestHttpException('Content has to be valid JSON');
        }

        return $data;
    }

    /**
     * @param  OrderItem[]  $items
     * @param  int $itemId
     *
     * @return OrderItem
     */
    private function getItem(array $items, $itemId): OrderItem
    {
        foreach ($items as $item) {
            if ($item->getItemId() == $itemId) {
                return $item;
            }
        }

        throw new BadRequestHttpException('Invalid item id.');
    }
}
