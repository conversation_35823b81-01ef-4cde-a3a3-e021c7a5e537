<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Wizacha\AppBundle\Controller\DomainUserTrait;
use Wizacha\Bridge\Symfony\Response\NotFoundJsonResponse;
use Wizacha\Marketplace\Order\Exception\OrderNotFound;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Transaction\Transaction;
use Wizacha\Marketplace\Transaction\TransactionService;

class OrderTransactionsController extends Controller
{
    use DomainUserTrait;

    /** @var TransactionService */
    private $transactionService;

    /** @var OrderService */
    private $orderService;

    public function __construct(TransactionService $transactionService, OrderService $orderService)
    {
        $this->transactionService = $transactionService;
        $this->orderService = $orderService;
    }

    public function listByOrderAction(int $orderId)
    {
        try {
            $order = $this->orderService->getOrderAndCheckStatus($orderId);
        } catch (OrderNotFound $e) {
            return new NotFoundJsonResponse($e->getMessage());
        }

        // Check if user is the order owner or marketplace administrator
        $user = $this->getDomainUser($this->getUser()->getId());
        if ($user->isMarketplaceAdministrator() === false
            && $order->getCompanyId() !== $user->getCompanyId()
        ) {
            // 404 instead of 403 to avoid leaking information
            return new NotFoundJsonResponse();
        }

        return new JsonResponse(array_map(
            function (Transaction $transaction): array {
                return $transaction->expose();
            },
            $this->transactionService->findByOrderId($orderId)
        ));
    }
}
