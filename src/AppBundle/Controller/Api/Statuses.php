<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api;

use Tygh\Api\Response;

class Statuses extends \Tygh\Api\Entities\Statuses
{
    public function index($id = 0, $params = array())
    {
        if ($id) {
            return [
                'status' => Response::STATUS_FORBIDDEN,
                'data' => []
            ];
        }

        $return = parent::index(0, $params);

        array_walk(
            $return['data'],
            function (&$_data) {
                $_data = fn_w_object_filter($_data, 'statuses', 'api', 'display_objects');
            }
        );

        return $return;
    }

    public function privileges()
    {
        return array(
            'index'  => true,
        );
    }
}
