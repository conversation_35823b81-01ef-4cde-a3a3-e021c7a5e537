<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use MangoPay\Libraries\ResponseException;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Wizacha\AppBundle\Controller\Api\Division\DivisionsTreeTrait;
use Wizacha\AppBundle\Controller\Api\Division\Dto\ApiDivisionSettingsDto;
use Wizacha\AppBundle\Controller\DomainUserTrait;
use Wizacha\AppBundle\Security\User\ApiSecurityUser;
use Wizacha\Bridge\CsCart\ErrorNotification;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Events\Config;
use Wizacha\Events\IterableEvent;
use Wizacha\Marketplace\Company\CompanyEvents;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Company\CompanyStatus;
use Wizacha\Marketplace\Division\Exception\InvalidDivisionCodeException;
use Wizacha\Marketplace\Division\Service\DivisionBlacklistsService;
use Wizacha\Marketplace\Division\Service\DivisionService;
use Wizacha\Marketplace\Division\Service\DivisionSettingsService;
use Wizacha\Marketplace\Exception\AlreadyExists;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\Exception\IntegrityConstraintViolation;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Image\Exception\BadImage;
use Wizacha\Marketplace\Payment\Exception\PayoutException;
use Wizacha\Marketplace\Payment\Processor\MangoPay;
use Wizacha\Marketplace\Payment\Processor\PayoutService;
use Wizacha\Marketplace\Subscription\SubscriptionRepository;
use Wizacha\Marketplace\Traits\ContraintViolationTrait;
use Wizacha\Marketplace\Traits\PaginationValidatorTrait;
use Wizacha\Marketplace\Traits\SubscriptionFilterValidatorTrait;
use Wizacha\Money\Money;

class CompanyController extends Controller
{
    use DomainUserTrait;
    use PaginationValidatorTrait;
    use SubscriptionFilterValidatorTrait;
    use ContraintViolationTrait;
    use DivisionsTreeTrait;

    /** @var CompanyService */
    private $companyService;

    /** @var DivisionBlacklistsService */
    private $divisionBlacklistsService;

    /** @var DivisionService */
    private $divisionService;

    /** @var SubscriptionRepository */
    private $subscriptionRepository;

    /** @var ValidatorInterface */
    private $validator;

    /** @var DivisionSettingsService */
    private $divisionSettingsService;

    private MangoPay $mangoPay;

    private PayoutService $payoutService;

    public function __construct(
        CompanyService $companyService,
        DivisionBlacklistsService $divisionBlacklistsService,
        DivisionService $divisionService,
        SubscriptionRepository $subscriptionRepository,
        ValidatorInterface $validator,
        DivisionSettingsService $divisionSettingsService,
        MangoPay $mangoPay,
        PayoutService $payoutService
    ) {
        $this->companyService = $companyService;
        $this->divisionBlacklistsService = $divisionBlacklistsService;
        $this->divisionService = $divisionService;
        $this->subscriptionRepository = $subscriptionRepository;
        $this->validator = $validator;
        $this->divisionSettingsService = $divisionSettingsService;
        $this->mangoPay = $mangoPay;
        $this->payoutService = $payoutService;
    }

    public function registerAction(Request $request): JsonResponse
    {
        $companyData = $this->getJsonContent($request);

        /** @var ApiSecurityUser $user */
        $user = $this->getUser();

        try {
            $company = $this->companyService->register($user ? $user->getId() : null, $companyData);
        } catch (\InvalidArgumentException | ErrorNotification | IntegrityConstraintViolation $e) {
            return new JsonResponse([
                'message' => "Failed to create company: {$e->getMessage()}",
            ], Response::HTTP_BAD_REQUEST);
        }

        return new JsonResponse($company->expose(), Response::HTTP_CREATED);
    }

    public function registerC2CAction(Request $request): JsonResponse
    {
        /** @var ApiSecurityUser $user */
        $user = $this->getUser();
        try {
            $company = $this->companyService->registerC2C(
                $user->getId(),
                $this->getJsonContent($request)
            );
        } catch (ErrorNotification | \InvalidArgumentException $e) {
            return new JsonResponse([
                'error' => $e->getMessage(),
            ], 400);
        }

        return new JsonResponse($company->expose(), Response::HTTP_CREATED);
    }

    public function updateAction(int $companyId, Request $request): JsonResponse
    {
        $companyData = $this->getJsonContent($request);

        if ($this->mangoPay->isConfigured() === true
            && $companyData['status'] === CompanyStatus::PENDING()->getKey()
        ) {
            $siretNumber = \array_key_exists('siretNumber', $companyData) === true
                ? $companyData['siretNumber']
                : $this->companyService->get($companyId)->getSiretNumber();

            if ($this->companyService->isSiretNumberValid($siretNumber) === false) {
                return new BadRequestJsonResponse(__('error_siret_number'));
            }
        }

        try {
            $this->companyService->update($companyId, $this->getUser()->getId(), $companyData);
        } catch (\InvalidArgumentException | ErrorNotification | IntegrityConstraintViolation $exception) {
            return new JsonResponse(
                [
                    'message' => "Failed to update company: {$exception->getMessage()}",
                ],
                Response::HTTP_BAD_REQUEST
            );
        } catch (ResponseException $exception) {
            return new JsonResponse(
                [
                    'message' => "Failed to update Mangopay company data",
                    'mangopayError' => $exception->GetErrorDetails(),
                ],
                $exception->getCode()
            );
        }


        $company = $this->companyService->get($companyId);

        return new JsonResponse($company->expose(), Response::HTTP_OK);
    }

    public function patchAction(int $companyId, Request $request): Response
    {
        if ($this->isGranted('ROLE_ADMIN') === false) {
            return new Response('', Response::HTTP_UNAUTHORIZED);
        }

        $companyData = $this->getJsonContent($request);

        if (\array_key_exists('status', $companyData) && \is_string($companyData['status'])) {
            try {
                $status = CompanyStatus::createFromApiValue($companyData['status']);
            } catch (\UnexpectedValueException $e) {
                return new JsonResponse(
                    ['message' => 'Invalid status parameter. It must be either ' . implode(',', CompanyStatus::keys())],
                    Response::HTTP_BAD_REQUEST
                );
            }

            if ($this->mangoPay->isConfigured() === true
                && $status->getValue() === CompanyStatus::PENDING()->getValue()
                && $this->companyService->isSiretNumberValid($this->companyService->
                get($companyId)->getSiretNumber()) === false
            ) {
                return new BadRequestJsonResponse(__('error_siret_number'));
            }

            $this->companyService->updateStatus($companyId, $status);
            Config::dispatch(
                CompanyEvents::UPDATED,
                IterableEvent::fromElement($companyId)
            );
        }

        $company = $this->companyService->get($companyId);

        return new JsonResponse($company->expose(), Response::HTTP_OK);
    }

    public function uploadRegistrationFilesAction(Request $request, int $companyId)
    {
        /** @var ApiSecurityUser $user */
        $user = $this->getUser();

        if ($request->files->has("division_of_powers_ids") === true) {
            $request->files->set("division_of_powers_id_cards", $request->files->get("division_of_powers_ids"));
            $request->files->remove("division_of_powers_ids");
        }

        $result = $this->companyService->saveRegistrationFiles($user ? $user->getId() : null, $companyId, $request->files->all(), $this->isGranted('ROLE_ADMIN') === true);

        return new JsonResponse($result, Response::HTTP_OK);
    }

    public function getRegistrationFilesListAction(Request $request, int $companyId): JsonResponse
    {
        /** @var ApiSecurityUser $user */
        $user = $this->getUser();

        $result = $this->companyService->getRegistrationFilesList($companyId, $user ? $user->getId() : null);

        return new JsonResponse($result, Response::HTTP_OK);
    }

    public function getRegistrationFileAction(Request $request, int $companyId, string $filename): Response
    {
        /** @var ApiSecurityUser $user */
        $user = $this->getUser();

        if ($filename === "division_of_powers_ids") {
            $filename = "division_of_powers_id_cards";
        }

        return $this->companyService->getFile($companyId, $filename, $user ? $user->getId() : null);
    }

    public function updateRegistrationFileAction(Request $request, int $companyId, string $filename): Response
    {
        /** @var ApiSecurityUser $user */
        $user = $this->getUser();

        if ($filename === "division_of_powers_ids") {
            $filename = "division_of_powers_id_cards";
            $request->files->set("division_of_powers_id_cards", $request->files->get("division_of_powers_ids"));
            $request->files->remove("division_of_powers_ids");
        }

        $this->companyService->checkUserAccessToCompanyFiles($user ? $user->getId() : null, $companyId);

        if (\array_key_exists('CONTENT_LENGTH', $_SERVER) === true
            && ($_SERVER['CONTENT_LENGTH'] > fn_return_bytes(ini_get('upload_max_filesize'))
            || $_SERVER['CONTENT_LENGTH'] > fn_return_bytes(ini_get('post_max_size')))
        ) {
            $maxSize = fn_return_bytes(ini_get('upload_max_filesize')) < fn_return_bytes(ini_get('post_max_size'))
                ? ini_get('upload_max_filesize')
                : ini_get('post_max_size');

            return new BadRequestJsonResponse(\sprintf('It is not possible to load files larger than %s', $maxSize));
        }

        $result = [
            'code'    => Response::HTTP_OK,
            'message' => [
                'success' => true,
            ],
        ];

        if (!$request->files->has($filename) || !($request->files->get($filename) instanceof UploadedFile)) {
            $result['code'] = Response::HTTP_BAD_REQUEST;
            $result['message'] = [
                'success' => false,
                'error'   => sprintf("Unable to find %s in the uploaded files", $filename),
            ];
        } else {
            try {
                $this->companyService->updateFile($companyId, $filename, $request->files->get($filename));
            } catch (\Throwable $e) {
                $this->get('logger')->error('Failed to update company registration file. ' . $e->getMessage(), [
                    'exception' => $e,
                ]);

                $result['code'] = Response::HTTP_BAD_REQUEST;
                $result['message'] = [
                    'success' => false,
                    'error'   => "Failed to update company registration file.",
                ];
            }
        }

        return new JsonResponse($result['message'], $result['code']);
    }

    public function deleteRegistrationFileAction(Request $request, int $companyId, string $filename): Response
    {
        /** @var ApiSecurityUser $user */
        $user = $this->getUser();

        $this->assertUserExists();

        if ($filename === "division_of_powers_ids") {
            $filename = "division_of_powers_id_cards";
        }

        $this->companyService->checkUserAccessToCompanyFiles($user ? $user->getId() : null, $companyId);

        try {
            $this->companyService->deleteFile($companyId, $filename);
        } catch (\Throwable $e) {
            $this->get('logger')->error('Failed to delete company registration file. ' . $e->getMessage(), [
                'exception' => $e,
            ]);

            return new BadRequestJsonResponse('Failed to delete company registration file.');
        }

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }

    private function getJsonContent(Request $request): array
    {
        $data = json_decode($request->getContent(), true);
        if (!\is_array($data)) {
            throw new BadRequestHttpException('Content has to be valid JSON');
        }

        return $data;
    }

    public function getAction(int $companyId): Response
    {
        $this->assertCanAccessCompany($companyId);

        $company = $this->companyService->get($companyId);

        return new JsonResponse($company->expose(), Response::HTTP_OK);
    }

    public function addCompanyImageAction(Request $request, int $companyId)
    {
        $this->assertUserExists();
        $this->assertCanAccessCompany($companyId);

        if (!$file = $request->files->get('file')) {
            return BadRequestJsonResponse::missingFields('file');
        }

        try {
            $result = $this->companyService->addCompanyImage($companyId, $file);
        } catch (BadImage $e) {
            return BadRequestJsonResponse::invalidField('file', $e->getMessage());
        } catch (AlreadyExists $e) {
            return new BadRequestJsonResponse('Image of company already exists');
        }

        // Return a bool true and HTTP Response
        return new JsonResponse($result, Response::HTTP_OK);
    }

    public function deleteCompanyImageAction(int $companyId, int $imageId): Response
    {
        $this->assertUserExists();
        $this->assertCanAccessCompany($companyId);

        try {
            $this->companyService->deleteCompanyImage($companyId);
        } catch (\Exception $e) {
            return new JsonResponse(
                [
                    'success' => false,
                    'message' => 'Image not found',
                ],
                Response::HTTP_NOT_FOUND
            );
        }

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }

    public function getCompanyImageAction(int $companyId): Response
    {
        $this->assertUserExists();
        $this->assertCanAccessCompany($companyId);

        $result = $this->companyService->getCompanyImageId($companyId);

        return new JsonResponse($result, Response::HTTP_OK);
    }

    public function getDivisionsAction(int $companyId): Response
    {
        if (false === $this->isAvailableOffersEnabled()) {
            return $this->notImplementedResponse();
        }

        $this->assertCanAccessCompany($companyId);

        $companyDivisionSettings = $this->divisionSettingsService->getCompanyDivisionSettings($companyId);

        return $this->json(
            (new ApiDivisionSettingsDto(
                $companyDivisionSettings->getIncludedDivisions()->toArray(),
                $companyDivisionSettings->getExcludedDivisions()->toArray()
            ))->toArray()
        );
    }

    public function getDivisionsTreeAction(Request $request, int $companyId): Response
    {
        if (false === $this->isAvailableOffersEnabled()) {
            return $this->notImplementedResponse();
        }

        try {
            $options = $this->resolveOptions($request->query->all());
        } catch (\InvalidArgumentException $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        $this->assertCanAccessCompany($companyId);

        $divisions = $this->divisionService->getCompanyDivisions(
            $companyId,
            (string) GlobalState::contentLocale(),
            true,
            $options['isEnabled'],
            $options['rootCode']
        );

        $response = new JsonResponse();

        $this->setDivisionCache($response);

        return $response->setData($divisions);
    }

    public function putDivisionsAction(int $companyId, Request $request): Response
    {
        if (false === $this->isAvailableOffersEnabled()) {
            return $this->notImplementedResponse();
        }

        $this->assertCanAccessCompany($companyId);

        try {
            $payload = (new OptionsResolver())
                ->setRequired(['included', 'excluded',])
                ->setAllowedTypes('included', 'array')
                ->setAllowedTypes('excluded', 'array')
                ->resolve($request->request->all())
            ;
        } catch (\InvalidArgumentException $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        try {
            $this->divisionSettingsService->updateCompanyDivisionSettings(
                $companyId,
                new ApiDivisionSettingsDto($payload['included'], $payload['excluded'])
            );
        } catch (InvalidDivisionCodeException $exception) {
            throw new BadRequestHttpException($exception->getMessage(), $exception);
        }

        return $this->json(null, Response::HTTP_NO_CONTENT);
    }

    public function getSubscriptionsAction(Request $request, int $companyId): JsonResponse
    {
        $this->assertCanAccessCompany($companyId);

        $violationsPagination = $this->paginationValidator($request);

        if (\count($violationsPagination) > 0) {
            return $this->badRequest($violationsPagination);
        }

        $pagination = $this->paginationResolver($request);
        $violationsFilters = $this->subscriptionFilterValidator($request);

        if (\count($violationsFilters) > 0) {
            return $this->badRequest($violationsFilters);
        }

        $company = $this->companyService->get($companyId);

        [1 => $totalCount] = $this->subscriptionRepository->findByCompany($company);
        [$items] = $this->subscriptionRepository->findByCompany(
            $company,
            $pagination['limit'],
            $pagination['offset'],
            $request->query->all()
        );

        return new JsonResponse([
            "limit" => $pagination['limit'],
            "offset" => $pagination['offset'],
            "total" => $totalCount,
            "items" => $items,
        ]);
    }

    public function getCompanyBalanceAction(int $companyId): JsonResponse
    {
        if ($this->isGranted('ROLE_ADMIN') === false) {
            return $this->json(['message' => __('company_trigger_payment_only_admin_api')], Response::HTTP_UNAUTHORIZED);
        }

        try {
            $this->companyService->get($companyId);
        } catch (\Exception $e) {
            return $this->json(['message' => $e->getMessage()], Response::HTTP_NOT_FOUND);
        }

        $payoutBalance = $this->payoutService->getPayoutBalance($companyId) ?? 0;

        $message = __(
            'company_trigger_payment_wallet_balance_api',
            [
                '[balance]' => (new Money($payoutBalance))->getConvertedAmount()
            ]
        );

        return $this->json(['message' => $message], Response::HTTP_OK);
    }

    public function companyPayoutAction(int $companyId): JsonResponse
    {
        if ($this->isGranted('ROLE_ADMIN') === false) {
            return $this->json(['message' => __('company_trigger_payment_only_admin_api')], Response::HTTP_UNAUTHORIZED);
        }

        try {
            $this->companyService->get($companyId);
        } catch (\Exception $e) {
            return $this->json(['message' => $e->getMessage()], Response::HTTP_NOT_FOUND);
        }

        try {
            $this->payoutService->doPayoutCompany($companyId);
        } catch (\Exception $exception) {
            return $this->json(['message' => $exception->getMessage()], Response::HTTP_BAD_REQUEST);
        }

        return $this->json(['message' => __('company_trigger_payment_success_api')], Response::HTTP_OK);
    }

    private function assertCanAccessCompany(int $targetCompanyId)
    {
        $authorizationChecker = $this->get('security.authorization_checker');
        /** @var ApiSecurityUser $securityUser */
        $loggedInUser = $this->getDomainUser($this->getUser()->getId());

        // An admin can access all users
        if ($authorizationChecker->isGranted('ROLE_ADMIN')) {
            return;
        }

        // A non-admin can only access its own account
        if ($loggedInUser->getCompanyId() === $targetCompanyId) {
            return;
        }

        // 404 instead of 403 to avoid leaking information
        throw NotFound::fromId('Company', $targetCompanyId);
    }

    private function assertUserExists()
    {
        /** @var ApiSecurityUser $user */
        $user = $this->getUser();

        if (\is_null($user)) {
            throw new Forbidden();
        }

        return;
    }
}
