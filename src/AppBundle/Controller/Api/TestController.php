<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;

class TestController extends Controller
{

    /**
     * Can only be called in test.
     * Simulate a HTTP query with long response (created for timeout in import)
     */
    public function delayAction($delay, Request $request): Response
    {
        if (!$this->isSafeEnvironment($request)) {
            throw new AccessDeniedHttpException();
        }

        sleep($delay);

        return new Response();
    }

    /**
     * Vérification que l'environnement est sûre afin d'exécuter des commandes systemes :
     * - l'environnement de Symfony doit être 'dev' ou 'test',
     * - le host doit être reconnu.
     */
    protected function isSafeEnvironment(Request $request)
    {
        if (!\in_array($this->get('kernel')->getEnvironment(), ['test'], true)) {
            return false;
        }

        if (!\in_array($request->getHost(), ['wizaplace.test', 'localhost'], true)) {
            return false;
        }

        return true;
    }
}
