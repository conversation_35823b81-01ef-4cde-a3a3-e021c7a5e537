<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Bridge\Symfony\Response\NotFoundJsonResponse;
use Wizacha\Marketplace\Currency\Currency;
use Wizacha\Marketplace\Currency\CurrencyCountries;
use Wizacha\Marketplace\Currency\CurrencyService;
use Wizacha\Marketplace\Currency\Exception\CurrencyNotFound;
use Wizacha\Marketplace\Currency\ExchangeRate;

class CurrencyController extends Controller
{
    /** @var CurrencyService */
    private $currencyService;

    public function __construct(CurrencyService $currencyService)
    {
        $this->currencyService = $currencyService;
    }

    public function listAction(Request $request): JsonResponse
    {
        try {
            return new JsonResponse(array_map(
                function (Currency $currency): array {
                    return $currency->expose();
                },
                $this->currencyService->list($request->query->all())
            ));
        } catch (\InvalidArgumentException $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }
    }

    public function updateAction(Request $request, string $currencyCode): JsonResponse
    {
        try {
            $data = (new OptionsResolver())
                ->setDefined('enabled')
                ->setDefined('exchangeRate')
                ->setAllowedTypes('enabled', 'boolean')
                ->setAllowedTypes('exchangeRate', ['float', 'int', 'null'])
                ->setNormalizer('exchangeRate', function (OptionsResolver $options, $value) {
                    return \is_null($value) ? null : ExchangeRate::fromVariable($value);
                })
                ->resolve($request->request->all());
        } catch (\InvalidArgumentException $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        // update updatedAt if only exchangeRate has updated
        if ($request->request->has('exchangeRate') === true) {
            $data['updatedAt'] = new \DateTime();
        }

        try {
            return new JsonResponse(
                $this
                    ->currencyService
                    ->update($currencyCode, $data)
                    ->expose()
            );
        } catch (CurrencyNotFound $e) {
            return new NotFoundJsonResponse($e->getMessage());
        }
    }

    public function getCurrencyCountriesAction(string $currencyCode): JsonResponse
    {
        $this->currencyService->getCurrencyCode($currencyCode);

        return new JsonResponse(array_map(
            function (CurrencyCountries $currencyCountries): array {
                return $currencyCountries->exposeCode();
            },
            $this->currencyService->getCountryCodes($currencyCode)
        ));
    }

    public function deleteCurrencyCountryAction(string $currencyCode, string $countryCode): Response
    {
        try {
            $this->currencyService->deleteCountryCode($currencyCode, $countryCode);
        } catch (NotFoundHttpException $e) {
            return new NotFoundJsonResponse($e->getMessage());
        }

        return new Response(null, Response::HTTP_NO_CONTENT);
    }

    public function createCurrencyCountryAction(string $currencyCode, Request $request): JsonResponse
    {
        $this->currencyService->getCurrencyCode($currencyCode);
        $countryCode = $request->request->get('countryCode');
        $currencyCountry = $this->currencyService->addCountryCode($currencyCode, $countryCode);

        return new JsonResponse([
            'code' => $currencyCountry->getCountryCode(),
        ]);
    }

    public function getCurrencyAction(string $currencyCode): JsonResponse
    {
        try {
            $currency = $this
                ->currencyService
                ->getCurrencyRepository()
                ->get($currencyCode);
        } catch (CurrencyNotFound $e) {
            return new NotFoundJsonResponse($e->getMessage());
        }

        return new JsonResponse($currency->expose());
    }
}
