<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\Payment;

use HiPay\Fullservice\Exception\ApiErrorException;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Wizacha\Marketplace\Order\DirectDebitService;
use Wizacha\Marketplace\Order\Exception\ActionNotAllowed;
use Wizacha\Marketplace\Order\Exception\FieldsValidationException;
use Wizacha\Marketplace\Order\Exception\UnknownPaymentProcessor;
use Wizacha\Marketplace\Payment\LemonWay\Exception\LemonWayWalletRequestFailed;
use Wizacha\Marketplace\Payment\Stripe\BankAccountIsMissing;
use Wizacha\Marketplace\Traits\ContraintViolationTrait;

class DirectDebitPaymentController extends Controller
{
    use ContraintViolationTrait;

    /** @var DirectDebitService */
    protected $directDebitService;

    public function __construct(DirectDebitService $directDebitService)
    {
        $this->directDebitService = $directDebitService;
    }

    public function createMandateAction(Request $request): JsonResponse
    {
        $requestData = $request->request->all();

        try {
            $responseData = $this->directDebitService->createMandate($requestData);

            if (\array_key_exists('signDocumentUrl', $responseData) === true) {
                $reponse = new JsonResponse(['signDocumentUrl' => $responseData['signDocumentUrl']], Response::HTTP_CREATED);
            } else {
                $reponse = new JsonResponse('', Response::HTTP_CREATED);
            }
        } catch (FieldsValidationException | ApiErrorException | LemonWayWalletRequestFailed $e) {
            $reponse = new JsonResponse(['error' => $e->getMessage()], Response::HTTP_BAD_REQUEST);
        } catch (UnknownPaymentProcessor $e) {
            $reponse = new JsonResponse(['error' => $e->getMessage()], Response::HTTP_NOT_FOUND);
        } catch (ActionNotAllowed $e) {
            $reponse = new JsonResponse(['error' => $e->getMessage()], Response::HTTP_BAD_REQUEST);
        }

        return $reponse;
    }

    public function getMandatesAction(): JsonResponse
    {
        try {
            $agreementData = $this->directDebitService->getMandates();
        } catch (BankAccountIsMissing $e) {
            $agreementData = [];
        }

        return new JsonResponse($agreementData);
    }
}
