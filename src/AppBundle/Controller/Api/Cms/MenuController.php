<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\Cms;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Wizacha\Marketplace\Cms\Menu;
use Wizacha\Marketplace\Cms\MenuItem;

class MenuController extends Controller
{
    public function listAction()
    {
        $menus = $this->get('marketplace.cms.menu_service')->getActiveMenus();

        $response = new JsonResponse(array_map([$this, 'serialize'], $menus));

        if ($this->getParameter('feature.cache_http')) {
            $response->setVary('Accept-Language');
            $response->setSharedMaxAge($this->getParameter('cache_api_cms_menu_list'));
        }

        return $response;
    }

    private function serialize(Menu $menu)
    {
        $menuItems = [];
        foreach ($menu->getItems() as $menuItem) {
            if ($menuItem->isActive()) {
                $menuItems[] = $this->serializeItem($menuItem);
            }
        }

        return [
            'id' => $menu->getId(),
            'name' => $menu->getName(),
            'items' => $menuItems,
        ];
    }

    private function serializeItem(MenuItem $item)
    {
        return [
            'name' => $item->getName(),
            'position' => $item->getPosition(),
            'url' => $item->getUrl(),
            'targetBlank' => $item->isTargetBlank(),
            'children' => array_map([$this, 'serializeItem'], $item->getItems()),
        ];
    }
}
