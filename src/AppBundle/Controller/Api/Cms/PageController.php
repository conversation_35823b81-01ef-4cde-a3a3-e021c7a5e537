<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\Cms;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Wizacha\Marketplace\Cms\Page;

class PageController extends Controller
{
    public function viewAction($pageId): JsonResponse
    {
        $pageService = $this->container->get('marketplace.cms.page_service');
        $page = $pageService->getPage((int) $pageId);

        return new JsonResponse($this->serialize($page));
    }

    private function serialize(Page $page): array
    {
        $serializedPage = [
            'id'                => $page->getId(),
            'title'             => $page->getTitle(),
            'content'           => $this->get('html_service')->transformImagesToAbsoluteUrl($page->getContent()),
            'metaTitle'         => $page->getSeoData()->getTitle(),
            'metaDescription'   => $page->getSeoData()->getDescription(),
            'metaKeywords'      => $page->getSeoData()->getKeywords(),
            'slug'              => $page->getSeoData()->getSlug(),
        ];

        return $serializedPage;
    }
}
