<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api\Organisation;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Wizacha\AppBundle\Controller\Api\Orders;
use Wizacha\AppBundle\Controller\Api\Pagination\PaginationHttpHeaders;
use Wizacha\Bridge\Symfony\Response\AccessDeniedJsonResponse;
use Wizacha\Bridge\Symfony\Response\NotFoundJsonResponse;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Order\OrderAttachment\Enum\DownloadAttachmentApiRouteKey;
use Wizacha\Marketplace\Order\OrderAttachment\Exception\DownloadFailException;
use Wizacha\Marketplace\Order\OrderAttachment\Service\OrderAttachmentService;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Organisation\OrganisationOrderService;
use Wizacha\Marketplace\Organisation\OrganisationService;
use Wizacha\Marketplace\User\User;
use Wizacha\Storage\StorageService;

class OrganisationOrderController extends Controller
{
    private OrderAttachmentService $attachmentService;
    private OrganisationOrderService $organisationOrdersService;
    private OrderService $orderService;
    private OrganisationService $organisationService;
    private StorageService $orderAttachmentsStorageService;

    public function __construct(
        OrderAttachmentService $attachmentService,
        OrganisationOrderService $organisationOrdersService,
        OrderService $orderService,
        OrganisationService $organisationService,
        StorageService $orderAttachmentsStorageService
    ) {
        $this->attachmentService = $attachmentService;
        $this->organisationOrdersService = $organisationOrdersService;
        $this->orderService = $orderService;
        $this->organisationService = $organisationService;
        $this->orderAttachmentsStorageService = $orderAttachmentsStorageService;
    }

    protected const START = 0;
    protected const LIMIT = 100;

    public function listAction(Request $request, string $organisationId): JsonResponse
    {
        $start = $request->get('start', self::START);
        if (!is_numeric($start) || $start < 0) {
            $start = self::START;
        }

        $limit = $request->get('limit', self::LIMIT);
        if (!is_numeric($limit) || $limit < 0) {
            $limit = self::LIMIT;
        }

        $user = $this->getDomainUser($this->getUser()->getId());
        try {
            $organisation = $this
                ->container
                ->get('marketplace.organisation.service')
                ->get($organisationId);
        } catch (NotFound $e) {
            return new NotFoundJsonResponse("The organisation doesn't exist");
        }

        if (!$organisation->isAdministrator($user) && !$user->isMarketplaceAdministrator()) {
            return new AccessDeniedJsonResponse("You're neither a marketplace administrator nor an organisation's administrator");
        }

        $filter = [];
        if ($request->query->has('transaction_reference') === true) {
            $filter = [
                'transaction_reference' => $request->query->get('transaction_reference'),
            ];
        }

        $nbTotalOrders = $this
            ->organisationService
            ->getCountOrderIdsByFilter($organisationId, $filter);

        $ordersIds = [];
        if ($nbTotalOrders > 0) {
            $ordersIds['organisation_orders'] = $this
            ->organisationService
            ->getOrderIdsByFilter(
                $organisationId,
                $filter,
                $start,
                (0 == $limit) ? $nbTotalOrders : $limit
            );
        }

        $params = $ordersIds;
        // Set the items_per_page in the params, it will be use by the Orders controller
        $params['items_per_page'] = $limit;

        //Instance Orders legacy class from  Wizacha\AppBundle\Controller\Api
        //And execute index function to have same results like GET /orders path
        $orderController = new Orders();
        $orders = $orderController->index(0, $params);

        return new JsonResponse(
            [
                'total' => $nbTotalOrders,
                'count' => \count($orders['data']),
                'start' => $start,
                'limit' => $limit ,
                '_embedded' => [
                    'orders' => $orders['data'],
                ],
            ],
            $orders['status'],
            [
                PaginationHttpHeaders::LIMIT => $limit,
                PaginationHttpHeaders::OFFSET => $start,
                PaginationHttpHeaders::TOTAL => $nbTotalOrders,
            ]
        );
    }

    /**
     * @param int $orderId
     *
     * @return JsonResponse
     * @throws \Wizacha\Marketplace\Order\Exception\OrderNotFound
     */
    public function getAction(int $orderId): JsonResponse
    {
        try {
            $this->assertCanAccessOrder($orderId);
        } catch (Forbidden $e) {
            return new AccessDeniedJsonResponse($e->getMessage());
        } catch (NotFound $e) {
            return new NotFoundJsonResponse($e->getMessage());
        }

        // Get order details
        $result = $this->orderService->getOrderAndCheckStatus($orderId)->expose();

        // Order attachments
        $orderAttachment = $this->attachmentService->list($orderId, []);
        $result['attachments'] = $this->attachmentService->summaryAttachment($orderAttachment, DownloadAttachmentApiRouteKey::ORGANISATION_ROUTE_DOWNLOAD());

        return new JsonResponse($result);
    }

    public function downloadOrderAttachmentByUserAction(int $orderId, string $attachmentId): Response
    {
        try {
            $this->assertCanAccessOrder($orderId);
        } catch (Forbidden | NotFound $e) {
            return new AccessDeniedJsonResponse($e->getMessage());
        }

        try {
            $orderAttachment = $this->attachmentService->get($orderId, $attachmentId);
        } catch (NotFoundHttpException $e) {
            return new NotFoundJsonResponse($e->getMessage());
        }

        // a optimiser
        try {
            return $this->orderAttachmentsStorageService
                ->get(
                    $orderAttachment->getOrderId() . '/'
                    . $orderAttachment->getId() . '/'
                    . $orderAttachment->getFilename()
                )
                ;
        } catch (FileException $exception) {
            throw new DownloadFailException();
        }
    }

    public function getOrganisationOrderAttachmentByUserAction(int $orderId, string $attachmentId): JsonResponse
    {
        try {
            $this->assertCanAccessOrder($orderId);
        } catch (Forbidden | NotFound $e) {
            return new AccessDeniedJsonResponse($e->getMessage());
        }

        // Order attachments
        try {
            $orderAttachment = $this->attachmentService->get($orderId, $attachmentId);
        } catch (NotFoundHttpException $e) {
            return new NotFoundJsonResponse($e->getMessage());
        }

        $orderAttachment = $this->attachmentService->summaryAttachment([$orderAttachment], DownloadAttachmentApiRouteKey::ORGANISATION_ROUTE_DOWNLOAD());

        return new JsonResponse($orderAttachment[0], Response::HTTP_OK);
    }

    public function assertCanAccessOrder(int $orderId): void
    {
        $user = $this->getDomainUser($this->getUser()->getId());
        $organisation = $user->getOrganisation();

        // Check if user is the organisation admin
        if ($organisation->isAdministrator($user) === false && $user->isMarketplaceAdministrator() === false) {
            throw new Forbidden("You're neither a marketplace administrator nor an organisation's administrator");
        }

        // Get the Organisation order
        $organisationOrder = $this->organisationOrdersService->get($orderId);

        // Check if the order belong to this organisation
        if ($organisationOrder->getOrganisation() !== $user->getOrganisation()) {
            throw new NotFound("The order doesn't exist");
        }
    }

    /**
     * Récupération de la classe métier de l'utilisateur,
     * et non la classe de sécurité qui est accessible par défaut.
     */
    protected function getDomainUser(int $userId): User
    {
        return $this
            ->container
            ->get('marketplace.user.user_service')
            ->get($userId);
    }
}
