<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api\Organisation;

use Symfony\Component\HttpFoundation\Response;
use Wizacha\AppBundle\Controller\DomainUserTrait;
use Wizacha\Marketplace\Organisation\OrganisationBasket;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Bridge\Symfony\Response\AccessDeniedJsonResponse;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;

class OrganisationBasketController extends Controller
{
    use DomainUserTrait;

    public function listAction(Request $request, string $organisationId): JsonResponse
    {
        $user = $this->getDomainUser($this->getUser()->getId());
        $organisation = $this
            ->container
            ->get('marketplace.organisation.service')
            ->get($organisationId);

        if (!$user->belongsToOrganisation($organisation) && !$user->isMarketplaceAdministrator()) {
            return new AccessDeniedJsonResponse('You don\'t belong to this organisation');
        }

        $baskets = array_map(function (OrganisationBasket $basket): array {
            return $basket->expose();
        }, $organisation->getOrganisationBaskets());

        return new JsonResponse([
            'total' => \count($baskets),
            'count' => \count($baskets),
            '_embedded' => [
                'baskets' => $baskets,
            ],
        ]);
    }

    public function createAction(Request $request, string $organisationId): JsonResponse
    {
        $user = $this->getDomainUser($this->getUser()->getId());
        $organisation = $this
            ->container
            ->get('marketplace.organisation.service')
            ->get($organisationId);

        // un administrateur de marketplace ne peut pas créer de panier pour une
        // orga puisqu'il n'appartient pas à celle ci
        if (!$user->belongsToOrganisation($organisation)) {
            return new AccessDeniedJsonResponse('You don\'t belong to this organisation');
        }

        if (!$request->request->has('name')) {
            return new BadRequestJsonResponse('Missing name field', []);
        }

        $basketId = $this
            ->container
            ->get('marketplace.organisation.basket_service')
            ->createOrganisationBasket($user, $request->request->get('name'));
        $basket = $this
            ->container
            ->get('marketplace.organisation.basket_service')
            ->get($basketId);

        return new JsonResponse($basket->expose(), JsonResponse::HTTP_CREATED);
    }

    public function lockAction(Request $request, string $organisationId, string $basketId): JsonResponse
    {
        $user = $this->getDomainUser($this->getUser()->getId());
        $organisation = $this
            ->container
            ->get('marketplace.organisation.service')
            ->get($organisationId);

        if (!$user->belongsToOrganisation($organisation) && !$user->isMarketplaceAdministrator()) {
            return new AccessDeniedJsonResponse('You don\'t belong to this organisation');
        }

        $service = $this->container->get('marketplace.organisation.basket_service');
        $basket = $service->get($basketId);

        if (!$organisation->ownsBasket($basketId)) {
            return new AccessDeniedJsonResponse('You don\'t own this basket');
        }

        $service->lock($basketId);

        return new JsonResponse($basket->expose(), JsonResponse::HTTP_OK);
    }

    public function validateAction(Request $request, string $organisationId, string $basketId): JsonResponse
    {
        $user = $this->getDomainUser($this->getUser()->getId());
        $organisation = $this
            ->container
            ->get('marketplace.organisation.service')
            ->get($organisationId);

        if (!$user->belongsToOrganisation($organisation) && !$user->isMarketplaceAdministrator()) {
            return new AccessDeniedJsonResponse('You don\'t belong to this organisation');
        }

        $service = $this->container->get('marketplace.organisation.basket_service');
        $basket = $service->get($basketId);

        if (!$organisation->ownsBasket($basketId)) {
            return new AccessDeniedJsonResponse('You don\'t own this basket');
        }

        $service->validate($basketId);

        return new JsonResponse($basket->expose(), JsonResponse::HTTP_OK);
    }

    public function orderAction(Request $request, string $organisationId, string $basketId): Response
    {
        $user = $this->getDomainUser($this->getUser()->getId());
        $organisation = $this
            ->container
            ->get('marketplace.organisation.service')
            ->get($organisationId);

        if (!$user->belongsToOrganisation($organisation)) {
            return new AccessDeniedJsonResponse('You don\'t belong to this organisation');
        }

        $service = $this->container->get('marketplace.organisation.basket_service');
        $basket = $service->get($basketId);

        if ($basket->getUser()->getUserId() !== $user->getUserId()) {
            return new AccessDeniedJsonResponse('You don\'t own this basket');
        }

        //The basket must be validated by the marketplace admin
        if (!$basket->isAccepted()) {
            return new AccessDeniedJsonResponse('The basket has not been validated');
        }

        //Call orderAction from BasketController to create order(s)
        $response = $this->forward('AppBundle:Api\Basket:order', [
            'id' => $basketId,
            'request' => $request,
        ]);

        if (JsonResponse::HTTP_OK !== $response->getStatusCode()) {
            return $response;
        }

        //Create the OrganisationOrder
        $organisationOrderService = $this->container->get('marketplace.organisation.order_service');
        $dataOrder = json_decode($response->getContent(), true);

        foreach ($dataOrder['orders'] as $order) {
            $organisationOrderService->create($organisation, $order['id']);
        }

        $service->checkout($basketId);

        return $response;
    }

    public function hideAction(Request $request, string $organisationId, string $basketId): JsonResponse
    {
        $user = $this->getDomainUser($this->getUser()->getId());
        $organisation = $this
            ->container
            ->get('marketplace.organisation.service')
            ->get($organisationId);

        $basketService = $this->container->get('marketplace.organisation.basket_service');
        $basket = $basketService->get($basketId);

        // User rights
        if (!$basket->isOwnedBy($user)
            && !$organisation->isAdministrator($user)
            && !$user->isMarketplaceAdministrator()
        ) {
            return new AccessDeniedJsonResponse();
        }

        $basketService->hide($basketId);

        return new JsonResponse($basket->expose(), JsonResponse::HTTP_OK);
    }
}
