<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api\Organisation;

use Doctrine\ORM\ORMException;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\OptionsResolver\Exception\ExceptionInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Wizacha\Bridge\Symfony\Response\AccessDeniedJsonResponse;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Bridge\Symfony\Response\MethodNotAllowedJsonResponse;
use Wizacha\FeatureFlag\FeatureFlaggableInterface;
use Wizacha\Marketplace\Organisation\Exception\OrganisationAlreadyApproved;
use Wizacha\Marketplace\Organisation\Exception\OrganisationAlreadyDisapproved;
use Wizacha\Marketplace\Organisation\Exception\OrganisationNotDeletable;
use Wizacha\Marketplace\Organisation\Organisation;
use Wizacha\Marketplace\Organisation\UserGroup;
use Wizacha\Marketplace\User\Exception\CannotCreateUser;
use Wizacha\Marketplace\User\Exception\PasswordFormatNotValid;
use Wizacha\Marketplace\User\Exception\UserAlreadyExists;
use Wizacha\Marketplace\User\User;
use Wizacha\Marketplace\User\UserStatus;
use Wizacha\Marketplace\User\UserTitle;

class OrganisationController extends Controller implements FeatureFlaggableInterface
{
    public function registrationAction(Request $request): JsonResponse
    {
        //seulement un utilisateur anonyme a le droit de demander un enregistrement, ou un administrateur de la MP
        if (!$this->isGranted('ROLE_ADMIN') && $this->isGranted('ROLE_USER')) {
            throw $this->createAccessDeniedException("Only unauthenticated user or administrator could request an organisation's registration.");
        }

        $resolver = new OptionsResolver();
        $resolver->setRequired(['name', 'administrator', 'address', 'shippingAddress', 'siret', 'vatNumber', 'businessName', 'businessUnitName', 'businessUnitCode']);
        $resolver->setAllowedTypes('name', 'string');
        $resolver->setAllowedTypes('businessName', 'string');
        $resolver->setAllowedTypes('businessUnitName', 'string');
        $resolver->setAllowedTypes('businessUnitCode', 'string');
        $resolver->setAllowedTypes('address', 'array');
        $resolver->setAllowedTypes('shippingAddress', 'array');
        $resolver->setAllowedTypes('administrator', 'array');
        $resolver->setAllowedTypes('siret', 'string');
        $resolver->setAllowedTypes('vatNumber', 'string');

        try {
            $data = $resolver->resolve($request->request->all());
        } catch (ExceptionInterface $e) {
            return new BadRequestJsonResponse($e->getMessage(), []);
        }

        try {
            $address = $this->resolveOrganisationAddress($data['address']);
        } catch (ExceptionInterface $e) {
            return new BadRequestJsonResponse($e->getMessage(), []);
        }

        try {
            $shippingAddress = $this->resolveOrganisationAddress($data['shippingAddress']);
        } catch (ExceptionInterface $e) {
            return new BadRequestJsonResponse($e->getMessage(), []);
        }

        $resolver = new OptionsResolver();
        $resolver->setRequired(['email', 'password', 'firstName', 'lastName', 'title', 'occupation']);
        $resolver->setAllowedTypes('email', 'string');
        $resolver->setAllowedTypes('firstName', 'string');
        $resolver->setAllowedTypes('lastName', 'string');
        $resolver->setAllowedTypes('password', 'string');
        $resolver->setAllowedTypes('title', 'string');
        $resolver->setAllowedTypes('occupation', 'string');
        $resolver->setAllowedValues('title', [(string) UserTitle::MR(), (string) UserTitle::MRS()]);

        try {
            $admin = $resolver->resolve($data['administrator']);
        } catch (ExceptionInterface $e) {
            return new BadRequestJsonResponse($e->getMessage(), []);
        }

        if (!$request->files->has('identityCard')) {
            return new BadRequestJsonResponse('Missing identity card file');
        }

        //check file extension identityCard
        try {
            $identityCard = $request->files->get('identityCard')->openFile();
        } catch (\Exception $exception) {
            return new BadRequestJsonResponse("Could not open the file identityCard, check it or try another one.");
        }

        if (!$request->files->has('proofOfAppointment')) {
            return new BadRequestJsonResponse('Missing proof of appointment file');
        }

        //check file extension proofOfAppointment
        try {
            $proofOfAppointment = $request->files->get('proofOfAppointment')->openFile();
        } catch (\Exception $exception) {
            return new BadRequestJsonResponse("Could not open the file proofOfAppointment, check it or try another one.");
        }

        try {
            $organisation = $this
                ->container
                ->get('marketplace.organisation.service')
                ->register(
                    $data['name'],
                    $admin['email'],
                    $admin['password'],
                    $address,
                    $shippingAddress,
                    $data['siret'],
                    $data['vatNumber'],
                    $admin['occupation'],
                    $identityCard,
                    $proofOfAppointment,
                    $data['businessName'],
                    $data['businessUnitCode'],
                    $data['businessUnitName'],
                    new UserTitle($admin['title']),
                    $admin['firstName'],
                    $admin['lastName'],
                    $data['phone']
                );
        } catch (UserAlreadyExists $e) {
            return new BadRequestJsonResponse('A user with this email already exists : ' . $data['administrator']['email']);
        } catch (CannotCreateUser | PasswordFormatNotValid $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        return new JsonResponse(
            $organisation->expose(),
            JsonResponse::HTTP_CREATED,
            [
                'Location' => $this->generateUrl(
                    'api_organisation_get',
                    ['organisationId' => $organisation->getId()]
                ),
            ]
        );
    }

    public function getAction(string $organisationId): JsonResponse
    {
        $user = $this->getDomainUser($this->getUser()->getId());
        $organisation = $this
            ->container
            ->get('marketplace.organisation.service')
            ->get($organisationId);

        if (!$this->canManipulateOrganisationInformation($user, $organisation)) {
            return new AccessDeniedJsonResponse('You don\'t belong to this organisation');
        }

        return new JsonResponse($organisation->expose());
    }

    public function listAction(): JsonResponse
    {
        $user = $this->getDomainUser($this->getUser()->getId());

        if (!$user->isMarketplaceAdministrator()) {
            return new AccessDeniedJsonResponse('You\'re not a marketplace administrator');
        }

        $organisations = $this
            ->container
            ->get('marketplace.organisation.service')
            ->list();
        $organisations = array_map(function (Organisation $organisation): array {
            return $organisation->expose();
        }, $organisations);

        return new JsonResponse([
            'total' => \count($organisations),
            'count' => \count($organisations),
            '_embedded' => [
                'organisations' => $organisations,
            ],
        ]);
    }

    public function updateAction(Request $request, string $organisationId): JsonResponse
    {
        $service = $this->container->get('marketplace.organisation.service');
        $organisation = $service->get($organisationId);

        $user = $this->getDomainUser($this->getUser()->getId());
        if (!$this->canManipulateOrganisationInformation($user, $organisation)) {
            return new AccessDeniedJsonResponse('You don\'t belong to this organisation');
        }

        $resolver = new OptionsResolver();
        $resolver->setRequired(['name', 'businessName', 'businessUnitName', 'businessUnitCode', 'siret', 'vatNumber']);
        $resolver->setAllowedTypes('name', 'string');
        $resolver->setAllowedTypes('businessName', 'string');
        $resolver->setAllowedTypes('businessUnitName', 'string');
        $resolver->setAllowedTypes('businessUnitCode', 'string');
        $resolver->setAllowedTypes('siret', 'string');
        $resolver->setAllowedTypes('vatNumber', 'string');

        try {
            $data = $resolver->resolve($request->request->all());
        } catch (ExceptionInterface $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        $service->rename($organisation->getId(), $data['name']);
        $service->declareLegalInformation($organisation->getId(), $data['siret'], $data['vatNumber'], $data['businessName']);
        $service->registerBusinessUnit($organisation->getId(), $data['businessUnitCode'], $data['businessUnitName']);

        return new JsonResponse(null, JsonResponse::HTTP_NO_CONTENT);
    }

    public function addNewUserAction(Request $request, string $organisationId): JsonResponse
    {
        $service = $this->container->get('marketplace.organisation.service');
        $organisation = $service->get($organisationId);

        $user = $this->getDomainUser($this->getUser()->getId());

        if (!$this->canManipulateOrganisationInformation($user, $organisation)) {
            return new AccessDeniedJsonResponse('You don\'t belong to this organisation');
        }

        $resolver = new OptionsResolver();

        $resolver->setRequired([
            'groupId',
            'title',
            'firstName',
            'lastName',
            'email',
            'password',
            'status',
        ]);
        $resolver->setDefined([
            'occupation',
            'phone',
            'shippingAddress',
        ]);

        $resolver->setAllowedTypes('groupId', 'string');
        $resolver->setAllowedTypes('email', 'string');
        $resolver->setAllowedTypes('firstName', 'string');
        $resolver->setAllowedTypes('lastName', 'string');
        $resolver->setAllowedTypes('password', 'string');
        $resolver->setAllowedTypes('status', 'string');
        $resolver->setAllowedTypes('title', 'string');
        $resolver->setAllowedValues('status', [UserStatus::ACTIVE, UserStatus::INACTIVE]);
        $resolver->setAllowedValues('title', [(string) UserTitle::MR(), (string) UserTitle::MRS()]);
        $resolver->setAllowedTypes('phone', 'string');
        $resolver->setAllowedTypes('shippingAddress', 'array');

        $resolver->setDefault('status', UserStatus::ACTIVE);

        try {
            $data = $resolver->resolve($request->request->all());
        } catch (ExceptionInterface $e) {
            return new BadRequestJsonResponse($e->getMessage(), []);
        }

        $group = $this->get('marketplace.organisation.user_group_service')->get($data['groupId']);

        if ($group->isAdminGroup()) {
            $message = [];

            if (!isset($data['occupation'])) {
                $message[] = "Missing occupation admin";
            }

            if (!$request->files->has('identityCard')) {
                $message[] = "Missing identity card of admin";
            }

            if (!$request->files->has('proofOfAppointment')) {
                $message[] = "Missing proof of appointment of admin";
            }

            if (!empty($message)) {
                return new BadRequestJsonResponse(implode(" ", $message), []);
            }
        }

        $identityCard = $proofOfAppointment = null;

        if ($request->files->has('identityCard')) {
            $identityCard = $request->files->get('identityCard')->openFile();
        }

        if ($request->files->has('proofOfAppointment')) {
            $proofOfAppointment = $request->files->get('proofOfAppointment')->openFile();
        }

        try {
            $user = $service->addNewUser(
                $organisation,
                $data['groupId'],
                $data['email'],
                $data['password'],
                $data['status'],
                $data['firstName'],
                $data['lastName'],
                new UserTitle($data['title']),
                $data['occupation'] ?? null,
                $identityCard,
                $proofOfAppointment,
                $data['phone'],
                $data['shippingAddress']
            );

            return new JsonResponse($user->expose(), JsonResponse::HTTP_CREATED);
        } catch (UserAlreadyExists $e) {
            return new BadRequestJsonResponse('A user with this email already exists : ' . $data['email']);
        } catch (CannotCreateUser $e) {
            return new BadRequestJsonResponse($e->getMessage());
        } catch (PasswordFormatNotValid $e) {
            return new BadRequestJsonResponse(__('error_password_format_not_valid'));
        }
    }

    public function listUsersAction(string $organisationId): JsonResponse
    {
        $service = $this->container->get('marketplace.organisation.service');
        $organisation = $service->get($organisationId);

        $user = $this->getDomainUser($this->getUser()->getId());

        if (!$this->canManipulateOrganisationInformation($user, $organisation)) {
            return new AccessDeniedJsonResponse('You don\'t belong to this organisation');
        }

        $users = $service->getUsers($organisation);

        return new JsonResponse([
            'total' => \count($users),
            'count' => \count($users),
            '_embedded' => [
                'users' => array_map(function (User $user): array {
                    return $user->expose();
                }, $users),
            ],
        ]);
    }

    public function listGroupsAction(string $organisationId): JsonResponse
    {
        $service = $this->container->get('marketplace.organisation.service');
        $organisation = $service->get($organisationId);

        $user = $this->getDomainUser($this->getUser()->getId());
        if (!$organisation->getAdminGroup()->hasUser($user) && !$user->isMarketplaceAdministrator()) {
            return new AccessDeniedJsonResponse("You don't belong to the administrator group");
        }

        $groups = $service->getUserGroups($organisation);

        return new JsonResponse([
            'total' => \count($groups),
            'count' => \count($groups),
            '_embedded' => [
                'groups' => array_map(function (UserGroup $group): array {
                    return $group->expose();
                }, $groups),
            ],
        ]);
    }

    public function addGroupAction(Request $request, string $organisationId): JsonResponse
    {
        if (!$request->request->has('name') || !$request->request->has('type')) {
            return new BadRequestJsonResponse("Missing parameters");
        }

        $name = $request->request->get('name');
        $type = $request->request->get('type');

        $regexSlug = preg_match("/^[a-z0-9]+(?:-[a-z0-9]+)*$/", $type);
        if ($regexSlug === false || $regexSlug === 0) {
            return new BadRequestJsonResponse("Parameter 'type' must contain only lowercase letters and dash");
        }

        $service = $this->container->get('marketplace.organisation.service');
        $organisation = $service->get($organisationId);


        $user = $this->getDomainUser($this->getUser()->getId());
        // On regarde que l'utilisateur soit bien dans l'orga ou qu'il soit admin de la MP
        if (!$organisation->isAdministrator($user) && !$user->isMarketplaceAdministrator()) {
            return new AccessDeniedJsonResponse("You don't belong to the administrator group");
        }


        // On check si le nom ou le type du group n'existe pas déjà
        foreach ($organisation->getUserGroups() as $group) {
            if (strtolower($name) === strtolower($group->getName())) {
                return new BadRequestJsonResponse("A group with the same name already exist");
            }

            if (strtolower($type) === strtolower($group->getType())) {
                return new BadRequestJsonResponse("A group with the same type already exist");
            }
        }


        try {
            $userGroupService = $this->container->get('marketplace.organisation.user_group_service');
            $userGroup = $userGroupService->create($organisation, $name, $type);

            return new JsonResponse([
                'id' => $userGroup->getId(),
            ], JsonResponse::HTTP_CREATED);
        } catch (ORMException $e) {
            return new JsonResponse("An error was occured when create the group", 500);
        }
    }

    public function updateAddressesAction(Request $request, string $organisationId): JsonResponse
    {
        $service = $this->container->get('marketplace.organisation.service');
        $organisation = $service->get($organisationId);

        $user = $this->getDomainUser($this->getUser()->getId());
        if (!$this->canManipulateOrganisationInformation($user, $organisation)) {
            return new AccessDeniedJsonResponse("You don't belong to this organisation");
        }

        try {
            $address = $this->resolveOrganisationAddress((array) $request->get('address'));
        } catch (ExceptionInterface $e) {
            return new BadRequestJsonResponse($e->getMessage(), []);
        }

        try {
            $shippingAddress = $this->resolveOrganisationAddress((array) $request->get('shippingAddress'));
        } catch (ExceptionInterface $e) {
            return new BadRequestJsonResponse($e->getMessage(), []);
        }

        $service->updateAddresses($organisationId, $address, $shippingAddress);

        return new JsonResponse(null, JsonResponse::HTTP_NO_CONTENT);
    }

    public function approveAction(string $organisationId): JsonResponse
    {
        $user = $this->getDomainUser($this->getUser()->getId());
        if (!$user->isMarketplaceAdministrator()) {
            return new AccessDeniedJsonResponse("You're not a marketplace administrator");
        }

        try {
            $this->container->get('marketplace.organisation.service')->approve($organisationId);
        } catch (OrganisationAlreadyApproved $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        return new JsonResponse(null, JsonResponse::HTTP_NO_CONTENT);
    }

    public function disapproveAction(string $organisationId): JsonResponse
    {
        $user = $this->getDomainUser($this->getUser()->getId());
        if (!$user->isMarketplaceAdministrator()) {
            return new AccessDeniedJsonResponse("You're not a marketplace administrator");
        }

        try {
            $this->container->get('marketplace.organisation.service')->disapprove($organisationId);
        } catch (OrganisationAlreadyDisapproved $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        return new JsonResponse(null, JsonResponse::HTTP_NO_CONTENT);
    }

    public function deleteAction(string $organisationId): JsonResponse
    {
        $user = $this->getDomainUser($this->getUser()->getId());
        if (!$user->isMarketplaceAdministrator()) {
            return new AccessDeniedJsonResponse("You're not a marketplace administrator");
        }

        try {
            $this->container->get('marketplace.organisation.service')->delete($organisationId);
        } catch (OrganisationNotDeletable $e) {
            return new MethodNotAllowedJsonResponse(
                $e->getMessage(),
                ['Allow' => 'GET, PUT']
            );
        }

        return new JsonResponse(null, JsonResponse::HTTP_NO_CONTENT);
    }

    /**
     * Récupération de la classe métier de l'utilisateur,
     * et non la classe de sécurité qui est accessible par défaut.
     */
    protected function getDomainUser(int $userId): User
    {
        return $this
            ->container
            ->get('marketplace.user.user_service')
            ->get($userId);
    }

    protected function resolveOrganisationAddress(array $data): array
    {
        $resolver = new OptionsResolver();
        $resolver->setRequired(['address', 'zipCode', 'city']);
        $resolver->setDefined(['additionalAddress', 'state', 'country']);
        $resolver->setAllowedTypes('address', 'string');
        $resolver->setAllowedTypes('additionalAddress', 'string');
        $resolver->setAllowedTypes('zipCode', 'string');
        $resolver->setAllowedTypes('city', 'string');
        $resolver->setAllowedTypes('state', 'string');
        $resolver->setAllowedTypes('country', 'string');

        return $resolver->resolve($data);
    }

    protected function canManipulateOrganisationInformation(User $user, Organisation $organisation): bool
    {
        if ($user->isMarketplaceAdministrator()) {
            return true;
        }

        if (!$user->belongsToOrganisation($organisation)) {
            return false;
        }

        return $organisation->isAdministrator($user);
    }

    public function getFeatureFlag(): string
    {
        return 'feature.organisations_enabled';
    }
}
