<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api\Organisation;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Wizacha\Bridge\Symfony\Response\AccessDeniedJsonResponse;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Bridge\Symfony\Response\NotFoundJsonResponse;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Organisation\Exception\AdministratorInformationRequired;
use Wizacha\Marketplace\Organisation\Exception\CannotAddUserToGroup;
use Wizacha\Marketplace\Organisation\Exception\CannotRemoveUserFromGroup;
use Wizacha\Marketplace\Organisation\Exception\UserAlreadyAttachedToTheUserGroup;
use Wizacha\Marketplace\Organisation\Exception\UserNotAttachedToUserGroup;
use Wizacha\Marketplace\User\User;

class UserGroupController extends Controller
{
    public function listUsersAction(string $groupId): JsonResponse
    {
        $user = $this->container->get('marketplace.user.user_service')->get($this->getUser()->getId());
        $group = $this->container->get('marketplace.organisation.user_group_service')->get($groupId);

        $organisation = $group->getOrganisation();
        if (!$organisation->isAdministrator($user) && !$user->isMarketplaceAdministrator()) {
            return new AccessDeniedJsonResponse("You don't belong to the administrator group");
        }

        $users = $group->getUsers();

        return new JsonResponse([
            'total' => \count($users),
            'count' => \count($users),
            '_embedded' => [
                'users' => array_map(function (User $user): array {
                    return $user->expose();
                }, $users),
            ],
        ]);
    }

    public function addUserAction(Request $request, string $groupId): JsonResponse
    {
        $userService = $this->container->get('marketplace.user.user_service');
        $apiUser = $userService->get($this->getUser()->getId());

        $userGroupService = $this->container->get('marketplace.organisation.user_group_service');
        $group = $userGroupService->get($groupId);

        $organisation = $group->getOrganisation();
        if (!$organisation->isAdministrator($apiUser) && !$apiUser->isMarketplaceAdministrator()) {
            return new AccessDeniedJsonResponse("You don't belong to the administrator group");
        }

        $userId = $request->request->get('userId');
        if ($userId === null) {
            return new BadRequestJsonResponse('The required "userId" parameter is missing');
        }

        try {
            $user = $userService->get($userId);
            $userGroupService->attachUser($group, $user);
        } catch (NotFound $e) {
            return new BadRequestJsonResponse($e->getMessage());
        } catch (CannotAddUserToGroup $e) {
            return new BadRequestJsonResponse($e->getMessage());
        } catch (UserAlreadyAttachedToTheUserGroup $e) {
            return new BadRequestJsonResponse($e->getMessage());
        } catch (AdministratorInformationRequired $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        return new JsonResponse(null, JsonResponse::HTTP_CREATED);
    }

    public function removeUserAction(Request $request, string $groupId, string $userId): JsonResponse
    {
        $userService = $this->container->get('marketplace.user.user_service');
        $apiUser = $userService->get($this->getUser()->getId());

        $userGroupService = $this->container->get('marketplace.organisation.user_group_service');
        $group = $userGroupService->get($groupId);

        $organisation = $group->getOrganisation();
        if (!$organisation->isAdministrator($apiUser) && !$apiUser->isMarketplaceAdministrator()) {
            return new AccessDeniedJsonResponse("You don't belong to the administrator group");
        }

        try {
            $user = $userService->get($userId);
            $userGroupService->removeUser($group, $user);
        } catch (CannotRemoveUserFromGroup $e) {
            return new BadRequestJsonResponse($e->getMessage());
        } catch (UserNotAttachedToUserGroup $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        return new JsonResponse(null, JsonResponse::HTTP_NO_CONTENT);
    }
}
