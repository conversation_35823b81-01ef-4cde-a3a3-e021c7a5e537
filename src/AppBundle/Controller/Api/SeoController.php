<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Violet\StreamingJsonEncoder\StreamJsonEncoder;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Marketplace\Seo\SeoService;
use Wizacha\Marketplace\Seo\SlugTarget;
use Wizacha\Marketplace\Seo\SlugTargetType;
use Wizacha\Core\Iterator\PdoIterator;

class SeoController extends Controller
{
    private const OFFSET = 0;
    private const LIMIT = 100;

    /**
     * @var SeoService
     */
    private $seoService;

    public const OBJECT_ID = "object_id";
    public const TARGET = "target";
    public const CATEGORY_ID_PATH = "category_id_path";
    public const CATEGORY_PATH = "categoryPath";

    public function __construct(
        SeoService $seoService
    ) {
        $this->seoService = $seoService;
    }

    /**
     * Resolve the given slugs.
     *
     * The response is a JSON object where keys are slugs and values are
     * the (resolved) target of the slug. For example:
     *
     *     GET /api/v1/seo/slugs?slugs[]=iphone&slugs[]=mobile
     *
     *     {
     *         "iphone": {
     *             "type": "product",
     *             "id": 123
     *         },
     *         "mobile": {
     *             "type": "category",
     *             "id": 456
     *         }
     *     }
     */
    public function resolveAction(Request $request)
    {
        $slugs = (array) $request->query->get('slugs');
        if (empty($slugs)) {
            return BadRequestJsonResponse::missingField('slugs');
        }

        $targets = $this->seoService->resolveSlugs($slugs);

        $targets = \array_filter(
            $targets,
            function (SlugTarget $target): bool {
                // Remove deprecated types
                return false === \in_array(
                    $target->getTargetType(),
                    [
                        SlugTargetType::REDIRECT()
                    ]
                );
            }
        );

        return new JsonResponse(
            \array_map(
                function (SlugTarget $target) {
                    return $target->expose();
                },
                $targets
            )
            ?: new \stdClass()
        );
    }

    public function catalogAction(): Response
    {
        // We stream the response because it can get quite big, and we don't want to hold it entirely in memory
        $response = new StreamedResponse();
        $response->headers->set('Content-Type', 'application/json');

        $slugs = $this->seoService->listValidSlugs(0, 0);

        $encoder = new StreamJsonEncoder(
            $this->itemGenerator($slugs)
        );

        $response->setCallback(
            function () use ($encoder): void {
                $encoder->encode();
            }
        );

        return $response;
    }

    public function catalogListAction(Request $request): Response
    {
        $offset = $request->query->getInt('offset', static::OFFSET);
        $limit = $request->query->getInt('limit', static::LIMIT);

        if ($offset < 0) {
            return new BadRequestJsonResponse(
                sprintf(
                    'Offset must be a positive integer or zero, %d given',
                    $offset
                )
            );
        }

        if ($limit < 1 || $limit > static::LIMIT) {
            return new BadRequestJsonResponse(
                sprintf(
                    'Limit must be a positive integer and less than %d, %d given',
                    static::LIMIT,
                    $limit
                )
            );
        }

        $total = $this->seoService->countListValidSlugs();

        if ($offset >= $total) {
            return new BadRequestJsonResponse(
                sprintf(
                    'Offset must be  < total (%d)',
                    $total
                )
            );
        }

        $slugs = $this->seoService->listValidSlugs($offset, $limit);

        $items = \iterator_to_array(
            $this->itemGenerator($slugs)
        );

        $resultJson = [
            'offset' => $offset,
            'enabled' => \count($items),
            'limit' => $limit,
            'total' => $total,
            'items' => $items,
        ];

        return new JsonResponse($resultJson);
    }

    /*
     *  slugs iterator to items generator
     *
     *  @return \Generator|array[]
     */
    private function itemGenerator(
        PdoIterator $slugs
    ): \Generator {
        foreach ($slugs as $slugData) {
            if (false === (bool) $slugData['enabled']) {
                continue;
            }

            $type = new SlugTargetType($slugData['type']);

            $target = new SlugTarget($type, $slugData[static::OBJECT_ID]);
            $result = [
                'slug' => $slugData['name'],
                self::TARGET => $target->expose()
            ];

            if ($type->equals(SlugTargetType::PRODUCT()) === true
                && \strlen($slugData[self::CATEGORY_ID_PATH]) > 0
            ) {
                $idPath = explode('/', $slugData[self::CATEGORY_ID_PATH]);

                foreach ($idPath as $categoryId) {
                    $categorySlug = $this->seoService->getCategorySlug($categoryId);

                    if (\array_key_exists('name', $categorySlug) === true) {
                        $type = SlugTargetType::CATEGORY();
                        $target = new SlugTarget($type, $categoryId);
                        $categoriesSlugData = [
                            'slug' => $categorySlug['name'],
                            self::TARGET => $target->expose(),
                        ];
                        $result[self::CATEGORY_PATH][] = $categoriesSlugData;
                    }
                }
            }

            yield $result;
        }
    }
}
