<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Broadway\Repository\AggregateNotFoundException;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Wizacha\Marketplace\Exception\ErrorCode;
use Wizacha\AppBundle\Controller\Api\Error\ErrorResponse;
use Wizacha\AppBundle\Service\WhitelistDomainsService;
use Wizacha\Bridge\Broadway\Exceptions\InvalidCommandException;
use Wizacha\Bridge\Symfony\Response\AccessDeniedJsonResponse;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Bridge\Symfony\Response\InternalServerErrorJsonResponse;
use Wizacha\Bridge\Symfony\Response\ServiceUnavailableJsonResponse;
use Wizacha\Bridge\Symfony\Response\UnauthorizedJsonResponse;
use Wizacha\Component\Chronopost\Exceptions\SystemError;
use Wizacha\Component\MondialRelay\Exception\ApiException;
use Wizacha\Marketplace\Address\Address;
use Wizacha\Marketplace\Address\Exception\AddressFieldsException;
use Wizacha\Marketplace\Basket\BasketService;
use Wizacha\Marketplace\Basket\Checkout;
use Wizacha\Marketplace\Basket\Exception\BasketNotFound;
use Wizacha\Marketplace\Basket\Exception\CannotAddCommentToMissingProduct;
use Wizacha\Marketplace\Basket\Exception\CannotAddNonTransactionalProduct;
use Wizacha\Marketplace\Basket\Exception\CannotCheckoutEmptyBasket;
use Wizacha\Marketplace\Basket\Exception\CannotMergeSameBasket;
use Wizacha\Marketplace\Basket\Exception\CustomerIsTooYoung;
use Wizacha\Marketplace\Basket\Exception\MissingPaymentException;
use Wizacha\Marketplace\Basket\Exception\ProductNotBelongsToUserDivision;
use Wizacha\Marketplace\Basket\Exception\UnavailablePaymentException;
use Wizacha\Marketplace\Basket\ReadModel\Basket as BasketReadModelBasket;
use Wizacha\Marketplace\Entities\Declination;
use Wizacha\Marketplace\Exception\InvalidDeclinationException;
use Wizacha\Marketplace\Exception\InvalidGroupException;
use Wizacha\Marketplace\Exception\InvalidShippingException;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Order\Exception\MissingShippingIdException;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Payment\Exception\SepaMandateNotCreatedException;
use Wizacha\Marketplace\Payment\Exception\SepaMandateNotSignedException;
use Wizacha\Marketplace\Payment\Payment;
use Wizacha\Marketplace\Payment\Response\HtmlPaymentResponse;
use Wizacha\Marketplace\Payment\Response\NoPaymentResponse;
use Wizacha\Marketplace\Payment\Response\RedirectPaymentResponse;
use Wizacha\Marketplace\Payment\Stripe\BankAccountIsMissing;
use Wizacha\Marketplace\PIM\Stock\StockService;
use Wizacha\Marketplace\ReadModel\Basket as ReadModelBasket;
use Wizacha\Marketplace\Shipping\DeliveryType;
use Wizacha\Marketplace\Traits\AssertCanAccessUserAccountTrait;
use Wizacha\Marketplace\Traits\ContraintViolationTrait;
use Wizacha\Marketplace\Traits\PaginationValidatorTrait;
use Wizacha\Marketplace\User\AddressBookService;
use Wizacha\Marketplace\User\Exception\InvalidFieldException;
use Wizacha\Marketplace\User\Exception\InvalidProfileFields;
use Wizacha\Marketplace\User\UserService;
use Wizacha\Component\Chronopost\Client as ChronopostClient;
use Wizacha\Component\MondialRelay\Client as MondialRelayClient;
use Wizacha\Marketplace\Payment\PaymentService;

class BasketController extends AbstractController
{
    use PaginationValidatorTrait;
    use ContraintViolationTrait;
    use AssertCanAccessUserAccountTrait;

    private StockService $stockService;

    private AddressBookService $addressBookService;

    private Bool $featureAvailableOffers;

    private Checkout $checkoutService;

    private WhitelistDomainsService $whitelistDomainsService;

    private ChronopostClient $chronopostClient;

    private MondialRelayClient $mondialRelayClient;

    private ValidatorInterface $validator;

    private ?bool $orderAdjustmentEnabled;

    private PaymentService $paymentService;

    private ?string $chronopostApiAccountNumber;

    private ?string $chronopostApiPassword;

    private ?string $mondialRelayApiUserId;

    private ?string $mondialRelayApiPassword;

    private BasketService $basketService;

    private LoggerInterface $logger;

    private LoggerInterface $functionalLogger;
    private bool $featureQuoteRequestEnabled;

    public function __construct(
        ValidatorInterface $validator,
        StockService $stockService,
        AddressBookService $addressBookService,
        UserService $userService,
        bool $featureAvailableOffers,
        Checkout $checkoutService,
        WhitelistDomainsService $whitelistDomainsService,
        ChronopostClient $chronopostClient,
        MondialRelayClient $mondialRelayClient,
        ?bool $orderAdjustmentEnabled,
        PaymentService $paymentService,
        ?string $chronopostApiAccountNumber,
        ?string $chronopostApiPassword,
        ?string $mondialRelayApiUserId,
        ?string $mondialRelayApiPassword,
        BasketService $basketService,
        LoggerInterface $logger,
        LoggerInterface $functionalLogger,
        bool $featureQuoteRequestEnabled
    ) {
        $this->validator = $validator;
        $this->stockService = $stockService;
        $this->addressBookService = $addressBookService;
        $this->userService = $userService;
        $this->featureAvailableOffers = $featureAvailableOffers;
        $this->checkoutService = $checkoutService;
        $this->whitelistDomainsService = $whitelistDomainsService;
        $this->chronopostClient = $chronopostClient;
        $this->mondialRelayClient = $mondialRelayClient;
        $this->orderAdjustmentEnabled = $orderAdjustmentEnabled;
        $this->paymentService = $paymentService;
        $this->chronopostApiAccountNumber = $chronopostApiAccountNumber;
        $this->chronopostApiPassword = $chronopostApiPassword;
        $this->mondialRelayApiUserId = $mondialRelayApiUserId;
        $this->mondialRelayApiPassword = $mondialRelayApiPassword;
        $this->basketService = $basketService;
        $this->logger = $logger;
        $this->functionalLogger = $functionalLogger;
        $this->featureQuoteRequestEnabled = $featureQuoteRequestEnabled;
    }

    public static function getDefaultLimit(): int
    {
        return 100;
    }

    public static function getMaxLimit(): int
    {
        return 1000;
    }

    public function createAction()
    {
        return new JsonResponse($this->basketService->generateNewBasket(), 201);
    }

    public function getAction(string $id)
    {
        $this->basketService->checkIntegrity($id);

        $basket = $this->basketService->getReadmodel($id);

        return new JsonResponse($basket->expose());
    }

    public function itemsAction(Request $request, string $id): JsonResponse
    {
        $violationsPagination = $this->paginationValidator($request);

        if (\count($violationsPagination) > 0) {
            return $this->badRequest($violationsPagination);
        }

        $pagination = $this->paginationResolver($request);

        // get basket items from readmodel
        $items = $this->basketService->getBasketItems($id);
        $itemsTotal = \count($items);

        // Displayed items
        $itemsSlice = \array_slice(
            $items,
            $pagination['offset'],
            $pagination['limit']
        );

        return new JsonResponse(
            [
                'basketId' => $id,
                'items' => $itemsSlice,
                'quantitiesTotal' => array_sum(array_column($items, 'quantity')),
                'indicatedPriceTotal' => array_sum(array_column($items, 'total')),
                'offset' => $pagination['offset'],
                'limit' => $pagination['limit'],
                'total' => $itemsTotal,
            ]
        );
    }

    public function addAction(Request $request, string $id)
    {
        $this->basketService->assertExists($id);

        if (!($request->request->get('declinationId') && $request->request->get('quantity'))) {
            return BadRequestJsonResponse::missingFields('declinationId', 'quantity');
        }
        $declinationId = (string) $request->request->get('declinationId');
        $declination = Declination::fromId($declinationId);
        if (!$declination->isActive()) {
            return BadRequestJsonResponse::invalidField('declinationId', "Declination $declinationId not found");
        }

        if (true === $declination->isQuoteExclusive() && true === $this->featureQuoteRequestEnabled) {
            return BadRequestJsonResponse::invalidField(
                'declinationId',
                "Declination $declinationId only available for quotes"
            );
        }

        try {
            $response = $this->getBookedQuantity(
                $request->request->get('declinationId'),
                $request->request->get('quantity'),
                $id,
                'single'
            );
        } catch (InvalidCommandException $e) {
            return new BadRequestJsonResponse($e->getMessage());
        } catch (CannotAddNonTransactionalProduct $e) {
            return new BadRequestJsonResponse('Cannot add non transactional product to a basket');
        }

        return new JsonResponse($response);
    }

    public function bulkAddAction(Request $request, string $id)
    {
        $this->basketService->assertExists($id);

        $declinations = (array) $request->request->get('declinations', []);

        if (empty($declinations) || \count($declinations) > 100) {
            throw new BadRequestHttpException('Declinations count must be between 1 and 100');
        }

        $bookedQuantities = [];
        $response = [];
        foreach ($declinations as $declination) {
            if (empty($declination['declinationId']) || empty((int) $declination['quantity'])) {
                return BadRequestJsonResponse::missingFields('declinationId', 'quantity');
            }

            $declinationId = $declination['declinationId'];
            $declinationObj = Declination::fromId($declinationId);
            if (!$declinationObj->isActive()) {
                return BadRequestJsonResponse::invalidField('declinationId', "Declination $declinationId not found");
            }

            if (true === $declinationObj->isQuoteExclusive() && true === $this->featureQuoteRequestEnabled) {
                return BadRequestJsonResponse::invalidField(
                    'declinationId',
                    "Declination $declinationId only available for quotes"
                );
            }

            if (!isset($bookedQuantities[$declination['declinationId']])) {
                $bookedQuantities[$declination['declinationId']] = 0;
            }

            try {
                $response[] = $this->getBookedQuantity(
                    $declination['declinationId'],
                    $declination['quantity'],
                    $id,
                    'multipe'
                );
            } catch (InvalidCommandException $e) {
                return new BadRequestJsonResponse($e->getMessage());
            } catch (CannotAddNonTransactionalProduct $e) {
                return new BadRequestJsonResponse('Cannot add non transactional product to a basket');
            }
        }

        return new JsonResponse(['declinations' => $response]);
    }

    public function mergeAction($id, Request $request)
    {
        $idMergeFrom = $request->request->get('basketId');
        if (!$idMergeFrom) {
            return BadRequestJsonResponse::missingField('basketId');
        }

        try {
            $this->basketService->mergeBasket($id, $idMergeFrom);
        } catch (CannotMergeSameBasket $exception) {
            throw new BadRequestHttpException($exception->getMessage(), $exception);
        }

        return new Response('', Response::HTTP_NO_CONTENT);
    }

    public function addCouponAction(string $id, string $coupon)
    {
        try {
            $normalizedCoupon = $this->basketService->getNormalizedCoupon($coupon);
        } catch (NotFound $exception) {
            return new ErrorResponse(
                ErrorCode::COUPON_CODE_DOES_NOT_APPLY(),
                $exception->getMessage(),
                [
                    'basket_id' => $id,
                    'coupon' => $coupon
                ],
                Response::HTTP_NOT_FOUND
            );
        }

        $basket = $this->basketService->getReadmodel($id);

        if (\in_array($normalizedCoupon, $basket->getCoupons())) {
            return new ErrorResponse(
                ErrorCode::COUPON_CODE_ALREADY_APPLIED(),
                'coupon code already applied',
                [
                    'basket_id' => $id,
                    'coupon' => $normalizedCoupon,
                ],
                Response::HTTP_CONFLICT
            );
        }

        if (!$this->basketService->addCoupon($id, $normalizedCoupon, $this->getUser())) {
            return new ErrorResponse(
                ErrorCode::COUPON_CODE_DOES_NOT_APPLY(),
                'coupon code does not apply',
                [
                    'basket_id' => $id,
                    'coupon' => $normalizedCoupon,
                ],
                Response::HTTP_NOT_FOUND
            );
        }

        return new Response('', Response::HTTP_CREATED);
    }

    public function removeCouponAction(string $id, string $coupon)
    {
        $this->basketService->assertExists($id);

        try {
            $normalizedCoupon = $this->basketService->getNormalizedCoupon($coupon);
        } catch (NotFound $exception) {
            $this->basketService->removeCoupon($id, $coupon);

            return new ErrorResponse(
                ErrorCode::COUPON_CODE_DOES_NOT_APPLY(),
                $exception->getMessage(),
                [
                    'basket_id' => $id,
                    'coupon' => $coupon
                ],
                Response::HTTP_GONE
            );
        }

        $this->basketService->removeCoupon($id, $normalizedCoupon);

        return new Response('', Response::HTTP_NO_CONTENT);
    }

    public function setCommentsAction(string $id, Request $request)
    {
        $this->basketService->assertExists($id);

        $elements = (array) $request->request->get('comments', []);

        if (empty($elements)) {
            return BadRequestJsonResponse::missingFields('comments');
        }

        foreach ($elements as $element) {
            if (!isset($element['comment']) || $element['comment'] === null) {
                return BadRequestJsonResponse::missingFields('comment');
            }

            if (!empty($element['declinationId'])) {
                try {
                    $this->basketService->setProductComment(
                        $id,
                        Declination::fromId($element['declinationId']),
                        $element['comment']
                    );
                } catch (CannotAddCommentToMissingProduct $exception) {
                    throw new BadRequestHttpException(
                        'This product is not in the basket. Impossible to add a comment.',
                        $exception
                    );
                }
            } else {
                $this->basketService->setComment($id, $element['comment']);
            }
        }

        return new Response('', Response::HTTP_NO_CONTENT);
    }

    public function getPaymentsAction(string $id)
    {
        $this->basketService->assertExists($id);

        $featureOrderAdjustment = (bool) $this->orderAdjustmentEnabled;
        $readmodel = $this->basketService->getReadmodel($id);

        $payments = $this->paymentService->getActivePayments();

        $payments = array_filter(
            $payments,
            function (Payment $payment) use ($featureOrderAdjustment, $readmodel): bool {
                return $featureOrderAdjustment && $readmodel->hasAdjustableProducts()
                    ? $payment->isNotCreditCard()
                    : $payment->isNotCreditCardCapture();
            }
        );

        return new JsonResponse($payments);
    }

    public function setShippingsAction(Request $request, string $basketId)
    {
        $shippingGroups = $request->request->get('shippingGroups');

        if (empty($shippingGroups) || !\is_array($shippingGroups)) {
            return new BadRequestJsonResponse('Missing shippingGroups');
        }

        try {
            foreach ($shippingGroups as $shippingGroupId => $shippingId) {
                $this->basketService->selectShippingForGroup($basketId, $shippingGroupId, $shippingId);
            }
        } catch (AggregateNotFoundException $e) {
            throw new BasketNotFound($basketId);
        } catch (InvalidCommandException | InvalidShippingException | InvalidGroupException $exception) {
            throw new BadRequestHttpException($exception->getMessage(), $exception);
        }

        return new Response(null, Response::HTTP_NO_CONTENT);
    }

    /**
     * Route only accessible to admins.
     *
     * @throws BasketNotFound
     */
    public function customShippingsPriceResetAction(Request $request, string $basketId): JsonResponse
    {
        $this->functionalLogger->notice(
            "[Basket] reset shipping price requested",
            [
                "context" => [
                    'ip' => $request->getClientIp(),
                    'url' => $request->getUri(),
                    'verb' => $request->getRealMethod(),
                    'body' => \json_encode($request->request->all()),
                ]
            ]
        );
        $this->basketService->assertExists($basketId);
        $this->basketService->resetCustomShippingPrice($basketId);
        $this->functionalLogger->notice("[Basket] reset shipping price executed");

        return new JsonResponse(null, Response::HTTP_NO_CONTENT);
    }

    /**
     * Route only accessible to admins.
     *
     * @throws BasketNotFound
     */
    public function customShippingsPriceUpdateAction(Request $request, string $basketId): JsonResponse
    {
        $this->functionalLogger->notice(
            "[Basket] update shipping price requested",
            [
                "context" => [
                    'ip' => $request->getClientIp(),
                    'url' => $request->getUri(),
                    'verb' => $request->getRealMethod(),
                    'body' => \json_encode($request->request->all()),
                ]
            ]
        );
        $this->basketService->assertExists($basketId);

        $shippingGroups = $request->request->get('shippingGroups');

        if (empty($shippingGroups) || !\is_array($shippingGroups)) {
            return new JsonResponse(
                [
                    "code" => 400,
                    "message" => "Missing shippingGroups"
                ]
            );
        }

        $result = $this->validateAndProcessShippingUpdatePrice($basketId, $shippingGroups);
        $this->functionalLogger->notice("[Basket] update shipping price executed", ["result" => $result]);

        return new JsonResponse($result);
    }

    public function changeQuantityAction(Request $request, string $id)
    {
        $this->basketService->assertExists($id);

        if (!($request->request->get('declinationId') && $request->request->get('quantity'))) {
            return BadRequestJsonResponse::missingFields('declinationId', 'quantity');
        }
        $declination = Declination::fromId((string) $request->request->get('declinationId'));
        if (!$declination->isActive()) {
            return BadRequestJsonResponse::invalidField('declinationId', 'Declination not found');
        }
        try {
            $quantity = $this->basketService->modifyProductQuantityInBasket(
                $id,
                $declination,
                $request->request->get('quantity')
            );
        } catch (InvalidDeclinationException $e) {
            return new BadRequestJsonResponse(
                $e->getMessage(),
                $e->getContext()
            );
        } catch (InvalidCommandException $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        return new JsonResponse(['quantity' => $quantity]);
    }

    public function removeAction(Request $request, $id)
    {
        $this->basketService->assertExists($id);

        if (!$request->request->get('declinationId')) {
            return BadRequestJsonResponse::missingField('declinationId');
        }
        $declination = Declination::fromId((string) $request->request->get('declinationId'));
        if (!$declination->isActive()) {
            return new JsonResponse('', 204);
        }
        try {
            $this->basketService->modifyProductQuantityInBasket(
                $id,
                $declination,
                0
            );
        } catch (InvalidCommandException $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        return new JsonResponse('', 204);
    }

    public function bulkRemoveAction(Request $request, $id): Response
    {
        $this->basketService->assertExists($id);
        $declinations = $request->request->get('declinations');

        if (null === $declinations) {
            return BadRequestJsonResponse::missingField('declinations');
        }

        foreach ($declinations as $jsonDeclination) {
            if (\is_array($jsonDeclination)) {
                $jsonDeclination = json_encode($jsonDeclination);
            }

            if ($this->isJson($jsonDeclination) === false) {
                continue;
            }

            $cleanDeclination = json_decode($jsonDeclination);

            if (false === property_exists($cleanDeclination, "declinationId")) {
                return BadRequestJsonResponse::missingField('declinationId');
            }

            $declination = Declination::fromId((string) $cleanDeclination->declinationId);

            if ($declination->isActive()) {
                try {
                    $this->basketService->modifyProductQuantityInBasket(
                        $id,
                        $declination,
                        0
                    );
                } catch (InvalidCommandException $exception) {
                    return new BadRequestJsonResponse($exception->getMessage());
                }
            }
        }

        return new JsonResponse('', 204);
    }

    public function setChronoRelaisPickupPointAction(Request $request, string $basketId): Response
    {
        // Vérification de la présence des crédentials pour l'utilisation de l'API Chronopost
        if (empty($this->chronopostApiAccountNumber) || empty($this->chronopostApiPassword)) {
            return new InternalServerErrorJsonResponse('You must have Chronopost API credentials');
        }

        // Récupération du panier (throw une notfound si nécessaire)
        $basket = $this->basketService->getReadmodel($basketId);
        if (!$basket->isEligibleToPickupPointsShipping()) {
            return new BadRequestJsonResponse('basket is not eligible for pickup point shipping');
        }

        // Récupération des informations postées
        $postedData = [];
        foreach (['pickupPointId', 'title', 'firstName', 'lastName'] as $fieldName) {
            $postedData[$fieldName] = $request->request->get($fieldName);
            if (\is_null($postedData[$fieldName])) {
                return BadRequestJsonResponse::missingField($fieldName);
            }
        }

        try {
            $shippingGroupsIds = $this->getShippingGroupsIds($request);
        } catch (InvalidFieldException $exception) {
            return BadRequestJsonResponse::invalidField('shippingGroupsIds', $exception->getMessage());
        }

        // Récupération du détail du pickup point (FR en dur pour l'instant)
        // Peut lancer une \Wizacha\Component\Chronopost\Exceptions\SystemError
        try {
            $pickupPoint = $this->chronopostClient->getPointRelais(
                $postedData['pickupPointId']
            );
        } catch (SystemError $exception) {
            return new JsonResponse($exception->getMessage(), Response::HTTP_BAD_REQUEST);
        }

        $this->basketService->setPickupPointId(
            $basketId,
            $postedData['pickupPointId'],
            $postedData['title'],
            $postedData['firstName'],
            $postedData['lastName'],
            DeliveryType::CHRONO_RELAIS(),
            $shippingGroupsIds
        );


        $shippingAddress = new Address(
            $postedData['title'],
            $postedData['firstName'],
            $postedData['lastName'],
            $pickupPoint->nom,
            $pickupPoint->adresse1,
            $pickupPoint->adresse2,
            $pickupPoint->codePostal,
            $pickupPoint->localite,
            '',
            $pickupPoint->codePays,
            '',
            '',
            '',
            $postedData['pickupPointId'],
        );

        return new JsonResponse($shippingAddress->expose(), 201);
    }


    public function setMondialRelayPickupPointAction(Request $request, string $basketId): Response
    {
        if (empty($this->mondialRelayApiUserId) || empty($this->mondialRelayApiPassword)) {
            return new InternalServerErrorJsonResponse('Missing Mondial Relay API credentials');
        }

        $basket = $this->basketService->getReadmodel($basketId);
        if (!$basket->isEligibleToPickupPointsShipping()) {
            return new BadRequestJsonResponse('Basket is not eligible for pickup point shipping');
        }

        $postedData = [];
        foreach (['pickupPointId', 'title', 'firstName', 'lastName'] as $fieldName) {
            $postedData[$fieldName] = $request->request->get($fieldName);
            if (\is_null($postedData[$fieldName])) {
                return BadRequestJsonResponse::missingField($fieldName);
            }
        }

        try {
            $shippingGroupsIds = $this->getShippingGroupsIds($request);
        } catch (InvalidFieldException $exception) {
            return BadRequestJsonResponse::invalidField('shippingGroupsIds', $exception->getMessage());
        }

        try {
            $pickupPoint = $this->mondialRelayClient->getPickupPoint(
                $postedData['pickupPointId']
            );
        } catch (ApiException $exception) {
            return new JsonResponse(
                __(sprintf('mondial_relay.error.%s', $exception->getCode())),
                Response::HTTP_BAD_REQUEST
            );
        }

        $this->basketService->setPickupPointId(
            $basketId,
            $postedData['pickupPointId'],
            $postedData['title'],
            $postedData['firstName'],
            $postedData['lastName'],
            DeliveryType::MONDIAL_RELAY(),
            $shippingGroupsIds
        );

        $shippingAddress = new Address(
            $postedData['title'],
            $postedData['firstName'],
            $postedData['lastName'],
            trim($pickupPoint->getAddress()[0] . ' ' . $pickupPoint->getAddress()[1]),
            $pickupPoint->getAddress()[2],
            $pickupPoint->getAddress()[3],
            $pickupPoint->getZipCode(),
            $pickupPoint->getCity(),
            '',
            $pickupPoint->getCountry(),
            '',
            '',
            '',
            $pickupPoint->getId()
        );

        return new JsonResponse($shippingAddress->expose(), 201);
    }

    /**
     * Turn a basket into an order.
     */
    public function orderAction(string $id, Request $request): Response
    {
        $user = $this->getUser();
        $basket = $this->basketService->getById($id);
        if (!$basket) {
            throw new BasketNotFound($id);
        }

        if (false === $this->basketService->checkIntegrity($basket->getId())) {
            return new JsonResponse(
                'The basket content has been updated to reflect the stock and shipping methods available',
                Response::HTTP_CONFLICT
            );
        }

        $cart = $basket->getCscartData($user->getId());
        // Prevent unauthorized access
        if (empty($cart['user_data']['email'])) {
            return new BadRequestJsonResponse('The basket must contain user data for the checkout');
        }

        if (!$request->request->get('acceptTermsAndConditions', false)) {
            return BadRequestJsonResponse::invalidField(
                'acceptTermsAndConditions',
                'The terms and conditions must be accepted'
            );
        }

        $redirectUrl = (string) $request->request->get('redirectUrl');
        if (!$redirectUrl || !filter_var($redirectUrl, FILTER_VALIDATE_URL)) {
            return BadRequestJsonResponse::invalidField('redirectUrl', 'redirectUrl must be an URI');
        }

        if ($this->whitelistDomainsService->isWhitedDomainHost($redirectUrl) === false) {
            return BadRequestJsonResponse::invalidField('redirectUrl', 'Domain is not whitelisted');
        }

        $cssUrl = $request->request->get('css');
        if (isset($cssUrl) && (!filter_var($cssUrl, FILTER_VALIDATE_URL) || parse_url($cssUrl, PHP_URL_SCHEME) !== 'https')) {
            return BadRequestJsonResponse::invalidField('css', 'css must be an HTTPS URI');
        }

        // Payment is not mandatory if the basket has a total of 0
        $paymentId = $request->request->getInt('paymentId', 0);
        $locale = $request->getPreferredLanguage() ?? $request->getDefaultLocale();

        try {
            $result = $this->checkoutService->checkout(
                $basket,
                $paymentId,
                $user->getId(),
                $redirectUrl,
                $cssUrl,
                $cart,
                $locale
            );
        } catch (CannotCheckoutEmptyBasket $e) {
            throw $e; // re-throw, just so it doesn't get caught in the last `catch`
        } catch (CustomerIsTooYoung $e) {
            return new BadRequestJsonResponse(
                'The customer\'s age is below the age limit for some products in the basket'
            );
        } catch (InvalidProfileFields $e) {
            if ($e instanceof AddressFieldsException) {
                $this->logger->notice(
                    "[Basket Checkout] Missing address information",
                    [
                        'exception' => $e,
                        'data' => [
                            'missingFields' => $e->getMissingFields(),
                            'paymentId' => $paymentId,
                            'userId' => $user->getId(),
                            'redirectUrl' => $redirectUrl,
                            'cssUrl' => $cssUrl,
                            ReadModelBasket::class => $basket->getData(),
                            BasketReadModelBasket::class => $basket->getReadModel()->expose(),
                        ],
                    ]
                );
            }

            return BadRequestJsonResponse::missingFields(...$e->getMissingFields());
        } catch (MissingPaymentException $e) {
            return BadRequestJsonResponse::missingField('paymentId');
        } catch (ProductNotBelongsToUserDivision $e) {
            $this->logger->notice($e->getMessage(), ['exception' => $e]);

            return BadRequestJsonResponse::invalidField('division', $e->getMessage());
        } catch (MissingShippingIdException $e) {
            $this->logger->notice($e->getMessage(), ['exception' => $e, 'cart' => $e->getCart()]);

            return new BadRequestJsonResponse('Some shipping selections are missing');
        } catch (BankAccountIsMissing $e) {
            return new BadRequestJsonResponse('The sepa mandate of customer can\'t be found or does not exist');
        } catch (SepaMandateNotCreatedException $sepaMandateNotCreatedException) {
            $this->logger->notice($sepaMandateNotCreatedException->getMessage(), ['exception' => $sepaMandateNotCreatedException]);

            return new BadRequestJsonResponse(__('sepa_mandate_not_found'));
        } catch (SepaMandateNotSignedException $sepaMandateNotSignedException) {
            $this->logger->notice($sepaMandateNotSignedException->getMessage(), ['exception' => $sepaMandateNotSignedException]);

            return new BadRequestJsonResponse(__('sepa_mandate_not_signed'));
        } catch (UnavailablePaymentException $unavailablePaymentException) {
            $this->logger->error($unavailablePaymentException->getMessage(), ['exception' => $unavailablePaymentException]);

            return $unavailablePaymentException->toApiErrorResponse();
        } catch (ApiException $exception) {
            return new JsonResponse(
                __(sprintf('mondial_relay.error.%s', $exception->getCode())),
                Response::HTTP_BAD_REQUEST
            );
        } catch (SystemError $exception) {
            return new JsonResponse($exception->getMessage(), Response::HTTP_BAD_REQUEST);
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage(), ['exception' => $e]);

            return new ServiceUnavailableJsonResponse('Payment unavailable at this moment');
        }

        $responseData = [
            'parentOrderId' => $result->getParentOrderId(),
            'orders' => array_map(
                function (Order $order) {
                    return [
                        'id' => $order->getId(),
                    ];
                },
                $result->getOrders()
            ),
        ];

        $paymentResponse = $result->getPaymentResponse();
        switch (true) {
            case $paymentResponse instanceof NoPaymentResponse:
                // No payment
                $responseData['redirectUrl'] = $redirectUrl;
                break;
            case $paymentResponse instanceof RedirectPaymentResponse:
                $responseData['redirectUrl'] = $paymentResponse->getRedirectUrl();
                break;
            case $paymentResponse instanceof HtmlPaymentResponse:
                $responseData['html'] = $paymentResponse->getHtml();
                $responseData['isPartial'] = $paymentResponse->isPartial();
                break;
            default:
                throw new \Exception('Unrecognized payment response');
        }

        return new JsonResponse($responseData);
    }

    public function chooseShippingAddressAction(string $basketId, Request $request): JsonResponse
    {
        $loggedUser = $this->getUser();
        if ($loggedUser === null) {
            return new UnauthorizedJsonResponse();
        }

        $this->basketService->assertExists($basketId);

        try {
            $this->assertUserHaveBasket($loggedUser->getId(), $basketId);
        } catch (NotFound $e) {
            return new AccessDeniedJsonResponse($e->getMessage());
        }

        if ($request->request->has('addressId') === false) {
            return BadRequestJsonResponse::missingField('addressId');
        }

        $addressId = $request->request->get('addressId');
        try {
            $addressBook = $this->addressBookService->get($addressId);
        } catch (NotFound $e) {
            return BadRequestJsonResponse::invalidField('addressId', 'AddressId not found.');
        }

        try {
            $this->assertUserHaveAddressBook($loggedUser->getId(), $addressId);
        } catch (NotFound $e) {
            return BadRequestJsonResponse::invalidField('addressId', $e->getMessage());
        }

        try {
            $addressBook->assertValid($this->featureAvailableOffers);
        } catch (AddressFieldsException $e) {
            return BadRequestJsonResponse::missingFields(...$e->getMissingFields());
        }

        $shippingAddress = new Address(
            $addressBook->getTitle(),
            $addressBook->getFirstname(),
            $addressBook->getLastName(),
            $addressBook->getCompany(),
            $addressBook->getAddress(),
            $addressBook->getAddress2(),
            $addressBook->getZipCode(),
            $addressBook->getCity(),
            $addressBook->getState(),
            $addressBook->getCountry(),
            $addressBook->getPhone(),
            '',
            $addressBook->getDivision() ?? '',
            '',
            $addressBook->getLabel(),
            $addressBook->getComment()
        );
        $this->basketService->setShippingAddress($basketId, $shippingAddress);

        return new JsonResponse('', Response::HTTP_OK);
    }

    public function chooseBillingAddressAction(string $basketId, Request $request): JsonResponse
    {
        $loggedUser = $this->getUser();
        if ($loggedUser === null) {
            return new UnauthorizedJsonResponse();
        }

        $this->basketService->assertExists($basketId);

        try {
            $this->assertUserHaveBasket($loggedUser->getId(), $basketId);
        } catch (NotFound $e) {
            return new AccessDeniedJsonResponse($e->getMessage());
        }

        if ($request->request->has('addressId') === false) {
            return BadRequestJsonResponse::missingField('addressId');
        }

        $addressId = $request->request->get('addressId');
        try {
            $addressBook = $this->addressBookService->get($addressId);
        } catch (NotFound $e) {
            return BadRequestJsonResponse::invalidField('addressId', 'AddressId not found.');
        }

        try {
            $this->assertUserHaveAddressBook($loggedUser->getId(), $addressId);
        } catch (NotFound $e) {
            return BadRequestJsonResponse::invalidField('addressId', $e->getMessage());
        }

        try {
            $addressBook->assertValid($this->featureAvailableOffers);
        } catch (AddressFieldsException $e) {
            return BadRequestJsonResponse::missingFields(...$e->getMissingFields());
        }

        $billingAddress = new Address(
            $addressBook->getTitle(),
            $addressBook->getFirstname(),
            $addressBook->getLastName(),
            $addressBook->getCompany(),
            $addressBook->getAddress(),
            $addressBook->getAddress2(),
            $addressBook->getZipCode(),
            $addressBook->getCity(),
            $addressBook->getState(),
            $addressBook->getCountry(),
            $addressBook->getPhone(),
            '',
            $addressBook->getDivision() ?? '',
            '',
            $addressBook->getLabel(),
            $addressBook->getComment()
        );
        $this->basketService->setBillingAddress($basketId, $billingAddress);

        return new JsonResponse('', Response::HTTP_OK);
    }

    private function isJson($string): bool
    {
        return \is_string($string) && \is_array(json_decode($string, true)) && (json_last_error() == JSON_ERROR_NONE) ? true : false;
    }

    private function getBookedQuantity($declinationId, $quantity, $basketId, $type): array
    {
        $declination = Declination::fromId($declinationId);

        $realStock = $this->stockService->getRealTimeStock($declination);

        if (($quantity <= $realStock)
            || ($declination->getCombinationCode() !== '0' && $declination->hasInfiniteStock() === true)
            || ($declination->getCombinationCode() === '0' && $declination->getProduct()->hasInfiniteStock() === true )
        ) {
            $quantityAdded = $quantity;
        } else {
            $quantityAdded = $realStock;
        }

        $bookedQuantity = $this->basketService->addProductToBasket(
            $basketId,
            $declination,
            $quantity
        );

        if ($type === 'single') {
            return [
                'quantity' => (int) $bookedQuantity,
                'added' => (int) $quantityAdded,
            ];
        } else {
            return [
                'declinationId' => $declinationId,
                'quantity' => (int) $bookedQuantity,
                'added' => (int) $quantityAdded,
            ];
        }
    }

    private function getShippingGroupsIds(Request $request): ?array
    {
        $shippingGroupsIds = null;
        if ($request->request->has('shippingGroupsIds') === true) {
            $shippingGroupsIds = $request->request->get('shippingGroupsIds');
            if (\is_array($request->request->get('shippingGroupsIds')) === false || $shippingGroupsIds !== array_filter($shippingGroupsIds, 'is_numeric')) {
                throw new InvalidFieldException('shippingGroupsIds', "The shippingGroupsIds mast be an array of integer");
            }
        }

        return $shippingGroupsIds;
    }

    /**
     * @param mixed[] $datas
     *
     * @return mixed[] lists of error or success message
     */
    private function validateAndProcessShippingUpdatePrice(string $basketId, array $datas): array
    {
        $errorMessage = [];
        foreach ($datas as $shippingGroupIndex => $shippingGroup) {
            $shippingGroupId = $shippingGroup['id'] ?? null;

            if (false === \is_int($shippingGroupId)) {
                $errorMessage[] = [
                    "code" => 404,
                    "message" => "Invalid or missing value for field shippingGroups[$shippingGroupIndex].id",
                ];
                continue;
            }

            if (empty($shippingGroup['shippings']) || !\is_array($shippingGroup['shippings'])) {
                $errorMessage[] = [
                    "shippingGroup" => $shippingGroupId,
                    "code" => 400,
                    "message" => "Missing shippings in shippingGroups"
                ];
                continue;
            }

            foreach ($shippingGroup['shippings'] as $shippingIndex => $shipping) {
                $shippingId = $shipping['id'] ?? null;

                if (false === \is_int($shippingId)) {
                    $errorMessage[] = [
                        "shippingGroup" => $shippingGroupId,
                        "code" => 404,
                        "message" => "Invalid or missing value for field shippingGroups[$shippingGroupIndex].shippings.id",
                    ];
                    continue;
                }

                try {
                    $this->basketService->assertValidShippingIdInShippingGroup($basketId, $shippingGroupId, $shippingId);
                } catch (InvalidGroupException $e) {
                    $errorMessage[] = [
                        "shippingGroup" => $shippingGroupId,
                        "code" => 404,
                        "message" => $e->getMessage()
                    ];
                    continue 2;
                } catch (InvalidShippingException $e) {
                    $errorMessage[] = [
                        "shippingGroup" => $shippingGroupId,
                        "shipping" => $shippingId,
                        "code" => 404,
                        "message" => $e->getMessage()
                    ];
                    continue;
                }

                $price = $shipping['price'] ?? null;
                $typeOfPrice = \gettype($price);

                if ($typeOfPrice !== 'NULL' && $typeOfPrice !== 'double' && $typeOfPrice !== 'integer') {
                    $errorMessage[] = [
                        "shippingGroup" => $shippingGroupId,
                        "shipping" => $shippingId,
                        "code" => 404,
                        "message" => "Invalid or missing value",
                    ];
                    continue;
                }

                $this->basketService->updateCustomShippingPrice($basketId, $shippingGroupId, $shippingId, $price);

                $message = $price === null
                    ? "Price of shipping $shippingId for shipping group $shippingGroupId was set with catalog value"
                    : "Price of shipping $shippingId for shipping group $shippingGroupId was updated to $price"
                ;
                $errorMessage[] = [
                    "shippingGroup" => $shippingGroupId,
                    "shipping" => $shippingId,
                    "code" => 200,
                    "message" => $message,
                ];
            }
        }

        return $errorMessage;
    }
}
