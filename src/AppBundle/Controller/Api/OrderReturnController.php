<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\ParameterBag;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Order\OrderReturn\Exception\OrderReturnCantBeEmpty;
use Wizacha\Marketplace\Order\OrderReturn\OrderReturn;
use Wizacha\Marketplace\Order\OrderReturn\OrderReturnReason;
use Wizacha\Marketplace\Order\OrderReturn\ReturnItem;

class OrderReturnController extends Controller
{
    public function getReasonsAction()
    {
        $reasons = $this->container->get('marketplace.order_return.service')->getReasons();
        // Tri des raisons par position
        usort(
            $reasons,
            function (OrderReturnReason $a, OrderReturnReason $b) {
                if ($a->getPosition() < $b->getPosition()) {
                    return -1;
                }

                return 1;
            }
        );

        return new JsonResponse(array_map(
            function (OrderReturnReason $reason) {
                return [
                    'id' => $reason->getId(),
                    'name' => $reason->getName(),
                    'position' => $reason->getPosition(),
                ];
            },
            $reasons
        ));
    }

    public function createAction(int $orderId, Request $request): Response
    {
        $parameters = $request->request;
        $order = $this->container->get('marketplace.order.order_service')->getOrder($orderId);
        if (!$this->getUser() || $this->getUser()->getId() != $order->getUserId()) {
            return new JsonResponse('', Response::HTTP_UNAUTHORIZED);
        }

        $checkResult = $this->checkCreateParams($parameters);
        if ($checkResult) {
            return $checkResult;
        }

        $returnService = $this->container->get('marketplace.order_return.service');
        $orderItems = $order->getItems();

        //Construction des returnItems à partir de l'order et des DeclinationId
        // Reconstruction des orderItems en un tableau plus glamour
        $rebuildOrderItems = [];
        foreach ($orderItems as $orderItem) {
            $rebuildOrderItems[$orderItem->getDeclinationId()] = $orderItem->getItemId();
        }

        $returns = [];

        foreach ($parameters->get('items') as $item) {
            if (!$rebuildOrderItems[$item['declinationId']]) {
                continue;
            }
            $returns[] = new ReturnItem(
                $rebuildOrderItems[$item['declinationId']],
                $item['declinationId'],
                $item['reason'],
                $item['amount']
            );
        }

        try {
            $returnId = $returnService->createOrderReturn(
                $orderId,
                $this->getUser()->getId(),
                $parameters->get('comments', ''),
                $returns
            );
        } catch (OrderReturnCantBeEmpty $e) {
            return BadRequestJsonResponse::invalidField('items', 'Missing items to create a return');
        }

        return new JsonResponse(['returnId' => $returnId], Response::HTTP_CREATED);
    }

    public function getAction(int $returnId): Response
    {
        $orderReturn = $this->container->get('marketplace.order_return.service')->getReturn($returnId);

        $this->assertCanAccessOrder($orderReturn->getOrderId(), $orderReturn->getUserId(), $returnId);

        return new JsonResponse($this->serializeForApiOrderReturn($orderReturn));
    }

    public function getByUserAction(): Response
    {
        $userId = $this->getUser()->getId();
        $orderReturns = $this->container->get('marketplace.order_return.service')->getReturnsByUser($userId);

        return new JsonResponse(array_map([$this, 'serializeForApiOrderReturn'], $orderReturns));
    }

    private function serializeForApiOrderReturn(OrderReturn $return): array
    {
        $order = $this->container->get('marketplace.order.order_service')->getOrder($return->getOrderId());

        //Rebuild les orderItem pour pouvoir retrouver le declinationId, productCode, declinationOptions
        //et customerComment depuis l'itemId
        $rebuildOrderItems = [];
        foreach ($order->getItems() as $orderItem) {
            $rebuildOrderItems[$orderItem->getItemId()] = [
                'declinationId' => $orderItem->getDeclinationId(),
                'productCode' => $orderItem->getProductCode(),
                'declinationOptions' => $orderItem->getDeclinationOptions(),
                'customerComment' => $orderItem->getCustomerComment()
            ];
        }
        $items = $return->getItems();

        return [
            'id' => $return->getId(),
            'orderId' => $return->getOrderId(),
            'userId' => $return->getUserId(),
            'createdAt' => $return->getCreatedAt()->format(\DateTime::RFC3339),
            'comments' => $return->getComment(),
            'status' => $return->getStatus(),
            'items' => array_map(
                function (ReturnItem $item) use ($rebuildOrderItems, $return): array {
                    $supplier_reference = db_get_field("SELECT supplier_reference FROM ?:order_details WHERE item_id = ?d AND  order_id= ?d", $item->getItemId(), $return->getOrderId());
                    return [
                        'declinationId' => $rebuildOrderItems[$item->getItemId()]['declinationId'],
                        'supplierRef' => $supplier_reference,
                        //We keep 'product' for retro-compatibility
                        'product' => $item->getProduct(),
                        'productName' => $item->getProduct(),
                        'productCode' => $rebuildOrderItems[$item->getItemId()]['productCode'],
                        'price' => $item->getPrice(),
                        'amount' => $item->getAmount(),
                        'reason' => $item->getReason(),
                        'options' => $rebuildOrderItems[$item->getItemId()]['declinationOptions'],
                        'customerComment' => $rebuildOrderItems[$item->getItemId()]['customerComment']
                    ];
                },
                $items
            ),
        ];
    }

    /**
     * Retourne la réponse adaptée si un problème survient sur un des champs
     */
    private function checkCreateParams(ParameterBag $parameters): ?Response
    {
        if (!$parameters->has('items')) {
            return BadRequestJsonResponse::missingField('items');
        }
        $items = $parameters->get('items');
        if (!\is_array($items)) {
            return BadRequestJsonResponse::invalidField('items', 'items must be an array');
        }
        foreach ($items as $item) {
            if (!$item['declinationId']) {
                return BadRequestJsonResponse::invalidField('items', 'each item must have a declinationId');
            }
            if (!$item['amount']) {
                return BadRequestJsonResponse::invalidField('items', 'each item must have an amount');
            }
            if (!$item['reason']) {
                return BadRequestJsonResponse::invalidField('items', 'each item must have a reason');
            }
        }

        return null;
    }

    private function assertCanAccessOrder(int $targetOrder, int $targetUserId, int $targetReturnId)
    {
        $authorizationChecker = $this->get('security.authorization_checker');

        // An admin can access all users
        if ($authorizationChecker->isGranted('ROLE_ADMIN')) {
            return;
        }

        if ($authorizationChecker->isGranted('ROLE_VENDOR')) {
            $orderInfo = container()
                ->get('marketplace.order.order_service')
                ->overrideLegacyOrder($targetOrder);

            if ($this->getUser()->getCompanyId() == $orderInfo['company_id']) {
                return;
            }
        } else {
            // A non-admin can only access its own orders
            if ($this->getUser()->getId() === $targetUserId) {
                return;
            }
        }

        // 404 instead of 403 to avoid leaking information
        throw NotFound::fromId('Return', $targetReturnId);
    }
}
