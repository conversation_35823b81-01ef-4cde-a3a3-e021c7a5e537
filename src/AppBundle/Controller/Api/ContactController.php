<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\Constraints;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Wizacha\Bridge\Swiftmailer\Mailer;
use Wizacha\Marketplace\AdminCompany;
use Wizacha\Marketplace\Traits\ContraintViolationTrait;

class ContactController extends Controller
{
    use ContraintViolationTrait;

    /** @var AdminCompany */
    private $adminCompany;

    /** @var Mailer */
    private $mailer;

    /** @var LoggerInterface */
    private $logger;

    /** @var ValidatorInterface */
    private $validator;

    public function __construct(
        AdminCompany $adminCompany,
        Mailer $mailer,
        LoggerInterface $logger,
        ValidatorInterface $validator
    ) {
        $this->adminCompany = $adminCompany;
        $this->mailer = $mailer;
        $this->logger = $logger;
        $this->validator = $validator;
    }

    public function postContactRequestAction(Request $request): Response
    {
        $requestData = $request->request->all();
        $requestFiles = $request->files->all();

        $senderEmailKey = 'senderEmail';
        $subjectKey = 'subject';
        $messageKey = 'message';
        $recipientEmailKey = 'recipientEmail';
        $attachmentsKey = 'attachments';

        $violations = $this->validator->validate($requestData, new Constraints\Collection([
            'fields' => [
                $senderEmailKey => [
                    new Constraints\Type(['type' => 'string', 'message' => "'${senderEmailKey}' must be a string"]),
                    new Constraints\NotBlank(['message' => "'${senderEmailKey}' must not be blank"]),
                    new Constraints\Email(['message' => "'${senderEmailKey}' must be a valid email"])
                ],
                $subjectKey => [
                    new Constraints\Type(['type' => 'string', 'message' => "'${subjectKey}' must be a string"]),
                    new Constraints\NotBlank(['message' => "'${subjectKey}' must not be blank"]),
                ],
                $messageKey => [
                    new Constraints\Type(['type' => 'string', 'message' => "'${messageKey}' must be a string"]),
                    new Constraints\NotBlank(['message' => "'${messageKey}' must not be blank"])
                ],
            ],
            'allowExtraFields' => true,
            'missingFieldsMessage' => "'{{ field }}' must be set",
        ]));

        if (\count($violations) > 0) {
            return $this->badRequest($violations);
        }

        $optionalFieldsViolations = $this->validator->validate($requestData, new Constraints\Collection([
            'fields' => [
                $recipientEmailKey => [new Constraints\Email(['message' => "'$recipientEmailKey' must be a valid email",])],
                $attachmentsKey => [new Constraints\Type(['type' => 'array', 'message' => "'$attachmentsKey' URL must be an array"])],
            ],
            'allowExtraFields' => true,
            'allowMissingFields' => true,
        ]));

        if (\count($optionalFieldsViolations) > 0) {
            return $this->badRequest($optionalFieldsViolations);
        }

        $filesViolations = $this->validator->validate($requestFiles, new Constraints\Collection([
            'fields' => [
                $attachmentsKey => [new Constraints\Type(['type' => 'array', 'message' => "'$attachmentsKey' files must be an array"])],
            ],
            'allowMissingFields' => true,
        ]));

        if (\count($filesViolations) > 0) {
            return $this->badRequest($filesViolations);
        }

        $from = $this->adminCompany->getSiteAdministratorEmail();
        $to = $requestData[$recipientEmailKey] ?? $this->adminCompany->getSiteAdministratorEmail();

        $message = new \Swift_Message();
        $message
            ->setSubject($requestData[$subjectKey])
            ->setFrom($from, $this->adminCompany->getName())
            ->setReplyTo($requestData[$senderEmailKey])
            ->setTo($to)
            ->setBody(nl2br($requestData[$messageKey], false), 'text/html');

        if (\array_key_exists($attachmentsKey, $requestFiles)) {
            /** @var UploadedFile $file */
            foreach ($requestFiles[$attachmentsKey] as $file) {
                $message->attach(
                    new \Swift_Attachment(
                        file_get_contents($file->getPathname()),
                        $file->getClientOriginalName(),
                        $file->getMimeType()
                    )
                );
            }
        }

        if (\array_key_exists($attachmentsKey, $requestData)) {
            foreach ($requestData[$attachmentsKey] as $url) {
                $message->attach(\Swift_Attachment::fromPath($url));
            }
        }

        $this->mailer->send($message);

        return new Response(null, Response::HTTP_CREATED);
    }
}
