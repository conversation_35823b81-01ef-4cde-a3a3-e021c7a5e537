<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Doctrine\ORM\QueryBuilder;
use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\OptionsResolver\Exception\ExceptionInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Wizacha\Bridge\Symfony\Response\AccessDeniedJsonResponse;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Bridge\Symfony\Response\NotFoundJsonResponse;
use Wizacha\Component\Import\EximJob;
use Wizacha\Component\Import\EximJobLog;
use Wizacha\Component\Import\EximJobLogRepository;
use Wizacha\Component\Import\EximJobRepository;
use Wizacha\Component\Import\EximJobService;
use Wizacha\Component\Import\JobStatus;
use Wizacha\Component\Import\JobType;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\User\User;

class JobController extends Controller
{
    /**
     * @var EximJobService
     */
    private $jobService;

    public function __construct(EximJobService $jobService)
    {
        $this->jobService = $jobService;
    }

    public function listAction(Request $request): Response
    {
        try {
            $options = $this->resolve($request);
        } catch (ExceptionInterface $e) {
            throw new BadRequestHttpException($e->getMessage(), $e);
        }

        $paginate = $this->jobService->getJobsPaginate(
            (int) $options['start'],
            (int) $options['limit'],
            function (QueryBuilder $query) use ($options) {
                $this->callbackJob($query, $options);
            }
        );

        $jobs = $paginate->getQuery()->getResult();

        return new JsonResponse([
            'total' => (int) $paginate->count(),
            'count' => (int) \count($jobs),
            'start' => (int) $options['start'],
            'limit' => (int) $options['limit'],
            'jobs' => array_map(function (EximJob $job) {
                return \array_merge(
                    $job->expose(),
                    $this->jobService->getProcessDurationInfo($job)
                );
            }, $jobs)
        ]);
    }

    public function getJobAction(string $jobId, Request $request): Response
    {
        $start = $request->get('start', EximJobRepository::START);
        if (!is_numeric($start) || $start < 0) {
            $start = EximJobLogRepository::START;
        }

        $limit = $request->get('limit', EximJobLogRepository::LIMIT);
        if (!is_numeric($limit) || $limit > EximJobLogRepository::LIMIT || $limit < 1) {
            $limit = EximJobLogRepository::LIMIT;
        }

        /** @var User $user */
        $user = $this->getUser();

        $job = $this->jobService->get($jobId);

        if ($user->getCompanyId() !== $job->getCompanyId() && !$this->isGranted('ROLE_ADMIN')) {
            throw new Forbidden();
        }

        $paginate = $this->jobService->getLogsPaginate((int) $start, (int) $limit, $jobId);

        /** @var EximJobLog[] $logs */
        $logs = $paginate->getQuery()->getResult();

        return new JsonResponse(
            array_merge(
                $job->expose(),
                $this->jobService->getProcessDurationInfo($job),
                [
                    'logs' => [
                        'total' => $paginate->count(),
                        'count' => \count($logs),
                        'start' => (int) $start,
                        'limit' => (int) $limit,
                        '_embedded' => \array_map(
                            function (EximJobLog $log) {
                                return \array_merge(
                                    $log->expose()
                                );
                            },
                            $logs
                        )
                    ]
                ]
            )
        );
    }

    public function getReportAction(string $jobId): Response
    {
        $job = $this->jobService->get($jobId);

        /** @var User $user */
        $user = $this->getUser();

        if ($user->getCompanyId() !== $job->getCompanyId() && !$this->isGranted('ROLE_ADMIN')) {
            throw new Forbidden();
        }

        $file = $this->jobService->getReport($job);

        if (!$file) {
            throw $this->createNotFoundException();
        }

        $date = (new \DateTime())->format('Y-m-d');
        $response = new BinaryFileResponse($file);
        $response->setContentDisposition(ResponseHeaderBag::DISPOSITION_ATTACHMENT, "report-$date.csv");

        return $response;
    }

    public function cancelAction(string $jobId): Response
    {
        try {
            $job = $this->jobService->get($jobId);
        } catch (NotFound $e) {
            return new NotFoundJsonResponse('Job not found.');
        }

        if (JobStatus::PENDING()->equals($job->getStatus()) === false) {
            return new BadRequestJsonResponse('This job is not pending and can’t be cancelled.');
        }

        /** @var User $user */
        $user = $this->getUser();

        if ($user->getCompanyId() !== $job->getCompanyId() && $this->isGranted('ROLE_ADMIN') === false) {
            return new AccessDeniedJsonResponse('Vendors or admins can only cancel their jobs.');
        }

        $this->jobService->cancel($job);

        $response = [
            'message' => 'Job canceled.'
        ];

        return new JsonResponse($response, Response::HTTP_OK);
    }

    public function resolve(Request $request): array
    {
        $resolver = new OptionsResolver();
        $resolver->setDefaults([
            'start' => EximJobRepository::START,
            'limit' => EximJobRepository::LIMIT,
            'userId' => null,
            'type' => null,
            'status' => null,
            'period' => [],
        ]);

        $resolver->setAllowedTypes('start', 'numeric');
        $resolver->setAllowedTypes('limit', 'numeric');
        $resolver->setAllowedTypes('period', ['null', 'string[]']);
        $resolver->setAllowedTypes('type', ['string', 'null']);
        $resolver->setAllowedTypes('status', ['string', 'null']);
        $resolver->setAllowedValues('type', JobType::keys());
        $resolver->setAllowedValues('status', JobStatus::keys());
        $resolver->addAllowedValues('type', null);
        $resolver->addAllowedValues('status', null);

        $options = $resolver->resolve($request->query->all());

        $periodResolver = new OptionsResolver();
        $periodResolver->setDefaults([
            'to' => (new \DateTime())->format(\DateTime::RFC3339),
            'from' => null,
        ]);

        $periodResolver->setAllowedValues('to', function ($value) {
            return \is_null($value) ? true : \DateTime::createFromFormat(\DateTime::RFC3339, $value);
        });

        $periodResolver->setAllowedValues('from', function ($value) {
            return \is_null($value) ? true : \DateTime::createFromFormat(\DateTime::RFC3339, $value);
        });

        $period = $periodResolver->resolve($options['period']);

        return array_merge($options, ['period' => $period]);
    }

    private function callbackJob(QueryBuilder $query, array $options = []): void
    {
        if ($this->isGranted('ROLE_VENDOR')) {
            $query->where('job.companyId = :companyId')
                ->setParameter('companyId', $this->getUser()->getCompanyId());
        }

        if ($options['userId']) {
            $query->andWhere('job.userId = :userId');
            $query->setParameter('userId', $options['userId']);
        }

        if ($options['period']['from']) {
            $from = \DateTime::createFromFormat(\DateTime::RFC3339, $options['period']['from']);
        }

        if ($options['period']['to']) {
            $to = \DateTime::createFromFormat(\DateTime::RFC3339, $options['period']['to']);
        }

        if (isset($from) && isset($to)) {
            $query->where('job.createdAt >= :from AND job.createdAt <= :to');
            $query->setParameters([
                'from' => $from,
                'to' => $to,
            ]);
        }

        if ($options['type']) {
            $type = new JobType(JobType::toArray()[$options['type']]);
            $query->andWhere('job.type = :type');
            $query->setParameter('type', $type->getValue());
        }

        if ($options['status']) {
            $status = new JobStatus(JobStatus::toArray()[$options['status']]);
            $query->andWhere('job.status = :status');
            $query->setParameter('status', $status->getValue());
        }
    }
}
