<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Bridge\Symfony\Response\InternalServerErrorJsonResponse;
use Wizacha\Bridge\Symfony\Response\NotFoundJsonResponse;
use Wizacha\Component\Chronopost\Exceptions\SystemError;

class ChronopostController extends Controller
{
    /**
     * Récupération des points relais en fonction d'une addresse passée en paramètre
     */
    public function listPointsRelaisAction(Request $request): JsonResponse
    {
        // Vérification de la présence des crédentials
        if (empty($this->container->getParameter('chronopost.api.account_number')) || empty($this->container->getParameter('chronopost.api.password'))) {
            return new InternalServerErrorJsonResponse('Missing Chronopost API credentials');
        }

        if (!$address = $request->query->get('address')) {
            return BadRequestJsonResponse::missingField('address');
        }

        if (!$zipcode = $request->query->get('zipcode')) {
            return BadRequestJsonResponse::missingField('zipcode');
        }

        if (!$city = $request->query->get('city')) {
            return BadRequestJsonResponse::missingField('city');
        }

        $country = $request->query->get('country', 'FR');

        if (!$weight = $request->query->get('weight')) {
            return BadRequestJsonResponse::missingField('weight');
        }

        try {
            $pointsRelais = $this->container->get('marketplace.chronopost.client')->listChronoPointsRelais(
                $address,
                $zipcode,
                $city,
                $country,
                $weight
            );
        } catch (SystemError $exception) {
            return new NotFoundJsonResponse($exception->getMessage());
        }

        return new JsonResponse($pointsRelais);
    }
}
