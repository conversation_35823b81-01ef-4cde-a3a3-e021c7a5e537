<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Marketplace\Order\Adjustment\OrderAdjustment;
use Wizacha\Marketplace\Order\Adjustment\OrderAdjustmentService;
use Wizacha\Marketplace\Order\Exception\AdjustmentException;
use Wizacha\Marketplace\Order\OrderService;

/**
 * A voter to test if we have access to adjustments would be cool
 */
class OrderAdjustmentController extends Controller
{
    public function adjustPriceAction(int $orderId, Request $request, OrderService $orderService, OrderAdjustmentService $orderAdjustmentService): Response
    {
        if ((bool) $this->getParameter('feature.order_adjustment') !== true) {
            throw $this->createAccessDeniedException();
        }

        // OrderNotFound is already thrown if not found
        $order = $orderService->getOrder($orderId);

        if ($this->isGranted('ROLE_VENDOR') && $this->getUser()->getCompanyId() !== $order->getCompanyId()) {
            throw $this->createAccessDeniedException();
        }

        try {
            $body = (new OptionsResolver())
                ->setRequired(['itemId', 'newTotalWithoutTaxes'])
                ->setAllowedTypes('itemId', 'int')
                ->setAllowedTypes('newTotalWithoutTaxes', ['int', 'float'])
                ->resolve($request->request->all());
        } catch (\InvalidArgumentException $e) {
            return new BadRequestJsonResponse(__('order_adjustment_bad_params'));
        }

        try {
            $orderAdjustmentService->adjust($order, $body['itemId'], $body['newTotalWithoutTaxes']);
        } catch (AdjustmentException $e) {
            return new BadRequestJsonResponse($e->getMessage());
        }

        return new Response('', Response::HTTP_NO_CONTENT);
    }

    public function getAdjustmentsAction(int $orderId, OrderService $orderService, OrderAdjustmentService $orderAdjustmentService): JsonResponse
    {
        if ((bool) $this->getParameter('feature.order_adjustment') !== true) {
            throw $this->createAccessDeniedException();
        }

        $order = $orderService->getOrder($orderId);

        if ($this->isGranted('ROLE_VENDOR') && $this->getUser()->getCompanyId() !== $order->getCompanyId()) {
            throw $this->createAccessDeniedException('You cannot access this order.');
        }

        return new JsonResponse(array_map(function (OrderAdjustment $adjustment): array {
            return $adjustment->expose();
        }, $orderAdjustmentService->findByOrderId($orderId)));
    }
}
