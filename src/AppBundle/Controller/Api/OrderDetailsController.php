<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\Order\OrderDetailsService;
use Wizacha\Marketplace\Order\OrderService;

class OrderDetailsController extends Controller
{
    /**
     * @var OrderDetailsService
     */
    private $orderDetailsService;

    /**
     * @var OrderService
     */
    private $orderService;

    /** @var AuthorizationCheckerInterface */
    private $authorizationChecker;

    public function __construct(
        OrderDetailsService $orderDetailsService,
        OrderService $orderService,
        AuthorizationCheckerInterface $authorizationChecker
    ) {
        $this->orderDetailsService = $orderDetailsService;
        $this->orderService = $orderService;
        $this->authorizationChecker = $authorizationChecker;
    }

    public function setDetailsAction(int $orderId, Request $request): JsonResponse
    {
        if (false === $request->request->has('details')) {
            return BadRequestJsonResponse::missingFields('details');
        }

        $this->assertCanAccessOrderDetails($orderId);
        $this->orderDetailsService->saveDetails($orderId, $request->request->get('details'));

        return new JsonResponse([
            'message' => sprintf('Staff note successfully added to order %s.', $orderId)
        ], JsonResponse::HTTP_CREATED);
    }

    private function assertCanAccessOrderDetails(int $targetOrderId): void
    {
        // An admin can access all users
        if ($this->authorizationChecker->isGranted('ROLE_ADMIN')) {
            return;
        }

        if ($this->authorizationChecker->isGranted('ROLE_VENDOR')) {
            $order = $this->orderService->overrideLegacyOrder($targetOrderId);
            if ($this->getUser()->getCompanyId() == $order['company_id']) {
                return;
            }
        }

        throw new Forbidden();
    }
}
