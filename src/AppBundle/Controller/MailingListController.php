<?php

namespace Wizacha\AppBundle\Controller;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Validator\Constraints\Email;
use Symfony\Component\Validator\Constraints\NotBlank;
use Wizacha\Marketplace\MailingList\Exception\AlreadyRegisteredException;
use Wizacha\Marketplace\MailingList\MailingList;

class MailingListController extends BaseController
{
    public function formAction()
    {
        $user = $this->getUser();
        if (!$user) {
            return new Response();
        }
        $mailingListService = $this->container->get('marketplace.mailing_list.mailing_list_service');

        return $this->render('@App/frontend/views/mailing-list/form.twig', [
            'subscriptions' => $mailingListService->getSubscriptions($user->getEmail()),
        ]);
    }

    /**
     * Subscribes a logged in user to a specific mailing list.
     */
    public function subscribeAction(Request $request)
    {
        $user = $this->getUser();
        if (!$user) {
            throw new AccessDeniedHttpException();
        }
        $mailingListId = $request->request->get('mailingList');
        if (!$mailingListId) {
            throw new BadRequestHttpException('Missing mailingList');
        }

        $mailingListService = $this->container->get('marketplace.mailing_list.mailing_list_service');
        try {
            $mailingListService->subscribe($user->getEmail(), $mailingListId);
        } catch (AlreadyRegisteredException $e) {
            // It's OK for a user to register twice, nothing specific should happen
        }

        if ($request->request->getBoolean('notify', false)) {
            fn_set_notification('N', __('subscribed_success'), '');
        }

        return new JsonResponse();
    }

    public function unsubscribeAction(Request $request)
    {
        $user = $this->getUser();
        if (!$user) {
            throw new AccessDeniedHttpException();
        }
        $mailingListId = $request->request->get('mailingList');
        if (!$mailingListId) {
            throw new BadRequestHttpException('Missing mailingList');
        }

        $mailingListService = $this->container->get('marketplace.mailing_list.mailing_list_service');
        $mailingListService->unsubscribe($user->getEmail(), $mailingListId);

        if ($request->request->getBoolean('notify', false)) {
            fn_set_notification('N', __('unsubscribed_success'), '');
        }

        return new JsonResponse();
    }

    /**
     * Subscribe an email to the first mailing list found (public form).
     */
    public function subscribeNewsletterAction(Request $request)
    {
        $returnUrl = $request->get('return_url') ?: $this->generateUrl('home');
        $translator = $this->get("translator");

        // Get and validate the email
        $email = $request->request->get('email', '');
        $errors = $this->get("validator")->validate($email, [new Email(), new NotBlank()]);
        if (\count($errors) > 0) {
            fn_set_notification("E", __("error"), $translator->trans("newsletter_subscribe_failed"));
            return new RedirectResponse($returnUrl);
        }

        $service = $this->get("marketplace.mailing_list.mailing_list_service");
        $lists = $service->getMailingLists();

        // In case there are no mailing list we log the email and show a success message
        // The user shouldn't see an error, we can still recover the emails from the logs afterwards
        if (0 === \count($lists)) {
            $this->get("logger")->warning(
                "The email address {email} couldn't be added to newsletter as no mailing list is defined",
                [ 'email' => $email ]
            );
            fn_set_notification("N", __("notice"), $translator->trans("newsletter_subscribe_success"));
            return new RedirectResponse($returnUrl);
        }

        /** @var MailingList $mailingList */
        $mailingList = array_shift($lists);

        try {
            $service->subscribe($email, $mailingList->getId());

            fn_set_notification("N", __("notice"), $translator->trans("newsletter_subscribe_success"));
        } catch (AlreadyRegisteredException $e) {
            fn_set_notification("E", __("error"), $translator->trans("newsletter_subscribe_already_registered"));
        }

        return new RedirectResponse($returnUrl);
    }
}
