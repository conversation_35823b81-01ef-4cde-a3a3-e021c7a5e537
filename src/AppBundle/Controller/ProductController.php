<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller;

use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Form;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Validator\Constraints\Choice;
use Symfony\Component\Validator\Constraints\GreaterThan;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Type;
use Wizacha\AppBundle\Form\Type\FeatureType;
use Wizacha\Category;
use Wizacha\Company;
use Wizacha\Events\IterableEvent;
use Wizacha\Marketplace\Catalog\Product\ProductSummary;
use Wizacha\Marketplace\Entities\Declination;
use Wizacha\Marketplace\Image\Exception\BadImage;
use Wizacha\Product;
use Wizacha\Registry;
use Wizacha\Search\Pagination;
use Wizacha\Shipping;
use Wizacha\Status;
use Wizacha\Marketplace\Shipping\DeliveryType;

class ProductController extends BaseController
{
    public function viewAction($productId)
    {
        $product = $this->get('marketplace.product.productservice')->getProduct($productId);
        if (!$product) {
            throw new NotFoundHttpException();
        }
        $declinations = $this->getDeclinations($product);
        $mainDeclination = reset($declinations);
        $event = IterableEvent::fromElement($productId);
        $this->container->get('event_dispatcher')->dispatch($event, Product::EVENT_VIEWED);

        $breadcrumbs = $mainDeclination['breadcrumbs'];
        array_unshift($breadcrumbs, [
            'link' => $this->generateUrl('home'),
            'name' => $this->container->get('translator')->trans('home'),
        ]);

        return $this->render('@App/frontend/product/view.html.twig', [
            'declinations' => $declinations,
            'product' => $mainDeclination,
            'productReadModel' => $product,
            'breadcrumbs' => $breadcrumbs,
            'seo_data' => $mainDeclination['seo_data'],
        ]);
    }

    public function affiliateLinkAction($declinationId, Request $request)
    {
        $link = Declination::fromId($declinationId)->getAffiliateLink();
        if (!$link) {
            return new Response('Link doesn\'t exist anymore', Response::HTTP_GONE);
        }
        if (strpos($link, '{email}') !== false) {
            if (!$this->getUser()) {
                return $this->redirect(fn_url("auth.login_form?return_url=" . urlencode($request->getRequestUri())));
            }
            $link = str_replace('{email}', urlencode($this->getUser()->getEmail()), $link);
        }
        return $this->redirect($link);
    }

    public function createAction(Request $request)
    {
        if ($this->getUser() && $this->getUser()->isAdmin()) {
            fn_set_notification('E', __('error'), __('error_admin_cannot_create_c2c_product'));
            return $this->redirect(fn_url(''));
        }

        if ($this->getUser() && $this->getUser()->isProfessionalVendor()) {
            return $this->redirect(fn_url('products.add', 'V'));
        }

        if (!$this->isC2CEnabled()) {
            throw new AccessDeniedHttpException();
        }
        $session = $this->container->get('session');
        $form = $this->createProductForm();
        $form->setData($session->get('create_product_form', []));

        return $this->handleForm($form, $request);
    }

    public function updateAction(Request $request)
    {
        if (!$this->isC2CEnabled()) {
            throw new AccessDeniedHttpException();
        }
        if (!$request->get('product_id')) {
            return $this->redirect($this->generateUrl('product_new'));
        }

        if (!$this->getUser()) {
            return $this->redirect(fn_url("auth.login_form?return_url=" . urlencode($request->getRequestUri())));
        }

        $product = new Product($request->get('product_id'));
        if (!$product->canUserUpdate($this->getUser())) {
            return new Response(
                '',
                Response::HTTP_FORBIDDEN
            );
        }

        $form = $this->createProductForm();
        $form->setData($product->fillFormArray());
        return $this->handleForm($form, $request);
    }

    public function handleReportAction(Request $request)
    {
        $productId = $request->request->get('product_id');
        $product = $this->get('marketplace.product.productservice')->getProduct($productId);

        $message = $request->request->get('message');

        if (empty($product) || empty($message)) {
            return $this->redirectToRoute('home');
        }

        if ($user = $this->getUser()) {
            $lastName = $user->getLastname();
            $firstName = $user->getFirstname();
            $email = $user->getEmail();
        } else {
            $recaptcha = $this->get('app.captcha');
            if (!$recaptcha->isHuman($request->request->all())) {
                fn_set_notification('W', __('warning'), __('error_captcha_required'));

                return $this->redirect(fn_url('products.view&product_id=' . $product->getId()));
            }

            $lastName = $request->request->get('name');
            $firstName = $request->request->get('first_name');
            $email = $request->request->get('email');

            if (empty($lastName) || empty($firstName) || empty($email)) {
                fn_set_notification('W', __('warning'), __('text_fill_the_mandatory_fields'));

                return $this->redirect(fn_url('products.view&product_id=' . $product->getId()));
            }
        }

        $this->get('marketplace.product.productservice')->reportProduct($productId, $firstName . ' ' . $lastName, $email, $message);

        fn_set_notification('N', __('notice'), __('w_message_send'));

        return $this->redirect(fn_url('products.view&product_id=' . $product->getId()));
    }

    protected function handleForm(Form $form, $request)
    {
        $session = $this->container->get('session');
        $form->handleRequest($request);

        // Handle image first, even if the rest of the form is not valid
        // The images are not lost and the user can only change the field which was wrong ad re submit
        // Before this, the user must remove the old broken image ad re upload it
        if ($this->validateFormImages($form)) {
            $image_manager = $this->get('image.manager');

            $form_data = $form->getData();
            $form_data['images'] = array_filter($form_data['images'] ?? []);

            foreach ($form_data['images'] as $image_file) {
                try {
                    $form_data['session_images'][] = $image_manager->createFromUploadedFile($image_file);
                } catch (BadImage $e) { // hotfix: handle bad images or not images files
                    $this->container->get('logger')->error('Bad file for product image', ['exception' => $e]);
                }
            }

            unset($form_data['images']);

            if ($form->isSubmitted()
                && $form->isValid()
                && $this->validateFormRequiredFeatures($form)
                && $this->validateFormShipping($form)
            ) {
                if (isset($form_data['attached_file'])) {
                    $form_data['session_attached_file'] = $this->get('marketplace.product.product_file_service')->saveTmpFile($form_data['attached_file']);
                }

                $session->set('create_product_form', $form_data);

                if ($this->getUser() === null) {
                    return $this->redirect($this->generateUrl('login', [
                        'additional_info' => 'pseudo',
                        'return_url' => 'index.php?dispatch=c2c_products.manage',
                    ]));
                }
                Product::createFromSession($session);

                return $this->redirect(fn_url('c2c_products.manage'));
            } else {
                // The form is invalid, but images are valid
                // Remove input image data and set session images
                // The user can fix the form without reupload images
                $form = $this->createProductForm();
                $form->setData($form_data);
            }
        }

        return $this->render('@App/frontend/views/products/create.html.twig', [
            'form' => $form->createView(),
            'categories' => Category::getTree(true), // boolean param used to get hidden categories as well
        ]);
    }

    protected function createProductForm(): Form
    {
        $shippings = Shipping::getByType(Shipping::TYPE_C2C);
        $shipping_handDelivery = [];
        $shipping_standardDelivery = [];

        foreach ($shippings as $shipping) {
            if ($shipping['status'] != Status::ENABLED) {
                continue;
            }

            if ($shipping['w_delivery_type'] == (string) DeliveryType::STANDARD()) {
                $shipping_standardDelivery = $shipping;
            } elseif ($shipping['w_delivery_type'] == (string) DeliveryType::HAND_WITH_CODE()) {
                $shipping_handDelivery = $shipping;
            }
        }

        $constraint_string   = new Type(['type' => 'string']);
        $constraint_integer  = new Type(['type' => 'integer']);
        $constraint_numeric  = new Type(['type' => 'numeric']);
        $constraint_float    = new Type(['type' => 'float']);
        $constraint_notblank = new NotBlank();
        $constraint_positive = new GreaterThan(['value' => 0]);

        $form = $this->createFormBuilder()
            ->add('product_id', HiddenType::class)
            ->add('category_id', HiddenType::class, [
                'required'  => true,
                'constraints'   => [
                    $constraint_notblank,
                    $constraint_numeric,
                    $constraint_positive,
                ],
            ])
            ->add('category_name', TextType::class, [
                'label'     => 'category',
                'required'  => true,
                'constraints'   => [
                    $constraint_string,
                    $constraint_notblank,
                ],
            ])
            ->add('w_condition', ChoiceType::class, [
                'label'     => 'w_product_condition',
                'required'  => true,
                'expanded'  => true,
                'choices'   => [
                    'w_product_condition_' . Product::CONDITION_NEW     => Product::CONDITION_NEW,
                    'w_product_condition_' . Product::CONDITION_USED    => Product::CONDITION_USED,
                ],
                'constraints'   => [
                    $constraint_string,
                    new Choice(['choices' => [Product::CONDITION_NEW, Product::CONDITION_USED]]),
                ],
            ])
            ->add('product', TextType::class, [
                'label'     => 'c2c_your_ad_title',
                'required'  => true,
                'constraints'   => [
                    $constraint_string,
                    $constraint_notblank,
                ],
                'attr'      => [
                    'placeholder' => 'c2c_product_creation_title_placeholder',
                ],
            ])
            ->add('price', NumberType::class, [
                'label'     => 'price',
                'required'  => true,
                'constraints'   => [
                    $constraint_float,
                    $constraint_positive,
                ],
                'attr'      => [
                    'placeholder' => 'c2c_product_creation_price_placeholder',
                ],
            ])
            ->add('amount', IntegerType::class, [
                'label'     => 'quantity',
                'required'  => true,
                'attr'      => [
                    'placeholder'   => 'quantity',
                    'min'           => 1,
                ],
                'constraints'   => [
                    $constraint_integer,
                    $constraint_notblank,
                ],
            ])
            ->add('full_description', TextareaType::class, [
                'label'     => 'c2c_your_ad_description',
                'required'  => true,
                'attr'      => [
                    'rows'      => 3,
                    'placeholder' => 'c2c_product_creation_description_placeholder',
                ],
                'constraints'   => [
                    $constraint_string,
                    $constraint_notblank,
                ],
            ])
            ->add('shipping_standardDelivery', CheckboxType::class, [
                'label'     => $shipping_standardDelivery['shipping'],
                'required'  => false,
                'attr'      => [
                    'class'                 => 'js-change-required js-switch-availability',
                    'data-required-target'  => '#form_shipping_standardDelivery_price',
                    'data-switch-target'    => '#form_shipping_standardDelivery_price',
                ]
            ])
            ->add('shipping_standardDelivery_price', TextType::class, [
                'label' => 'c2c_euro_delivery_cost',
                'required' => false,
                'attr' => [
                    'placeholder'           => 'price',
                    'disabled'              => 'disabled'
                ]
            ])
            ->add('shipping_handDelivery', CheckboxType::class, [
                'label'     => $shipping_handDelivery['shipping'],
                'required'  => false,
                'attr'      => [
                    'class'                 => 'js-change-required js-switch-availability',
                    'data-required-target'  => '#form_shipping_handDelivery_label',
                    'data-switch-target'    => '#form_shipping_handDelivery_label',
                ]
            ])
            ->add('shipping_handDelivery_lat', HiddenType::class)
            ->add('shipping_handDelivery_lng', HiddenType::class)
            ->add('shipping_handDelivery_postal', HiddenType::class)
            ->add('shipping_handDelivery_label', TextType::class, [
                'label' => 'c2c_delivery_place',
                'required' => false,
                'attr'  => [
                    'placeholder'   => 'w_zipcode_indication',
                    'disabled'      => 'disabled',
                ]
            ])
            ->add('session_images', CollectionType::class, [
                'entry_type' => HiddenType::class,
                'allow_add' => true,
                'allow_delete' => true,
                'attr' => [
                    'data-label-entry' => __('session_image'),
                    'data-label-remove' => __('remove_image')
                ]
            ])
            ->add('images', CollectionType::class, [
                'entry_type' => FileType::class,
                'allow_add' => true,
                'allow_delete' => true,
                'attr' => [
                    'data-label-entry' => __('image'),
                    'data-label-add' => __('add_image'),
                    'data-label-remove' => __('remove_image')
                ]
            ])
            ->add('features', FeatureType::class)
            ->add('geoloc_lat', HiddenType::class)
            ->add('geoloc_lng', HiddenType::class)
            ->add('geoloc_postal', HiddenType::class)
            ->add('geoloc_label', TextType::class, [
                'label' => 'c2c_delivery_place',
                'attr'  => [
                    'placeholder'   => 'w_zipcode_indication',
                ]
            ]);

        if ($this->getUser()) {
            $company_id = Company::runtimeID(AREA, $_SESSION, Registry::defaultInstance());
            $company_data = fn_get_company_data($company_id);
            if ($company_data['company']) {
                $form->add(
                    'company',
                    TextType::class,
                    [
                        'label' => 'w_c2c_company_name',
                        'attr' => [
                            'disabled' => 'disabled',
                            'value' => $company_data['company']
                        ]
                    ]
                );
            } else {
                $form->add(
                    'company',
                    TextType::class,
                    [
                        'label' => 'w_c2c_company_name',
                    ]
                );
            }
        }
        return $form->getForm();
    }

    protected function validateFormImages(Form $form)
    {
        $data = $form->getData();

        // check images only if form is submitted
        if (empty($data)) {
            // false but without notification
            return false;
        }

        if (!empty($data['images'])) {
            $data['images'] = array_filter($data['images']);
        }

        if (empty($data['images']) && empty($data['session_images'])) {
            fn_set_notification('E', __('error'), __('error_validator_required', ['[field]' => __('images')]));
            return false;
        }

        return true;
    }

    protected function validateFormRequiredFeatures(Form $form): bool
    {
        $data = $form->getData();
        $category_id = filter_var($data['category_id'], FILTER_SANITIZE_NUMBER_INT);
        $features = $this->container->get('marketplace.pim.attribute_service')->getRequiredAttributesFromCategory($category_id, true);
        foreach ($features as $feature) {
            if (!$data['features']['product_features'][$feature['feature_id']]) {
                fn_set_notification('E', __('error'), __('error_validator_required', ['[field]' => $feature['description']]));
                return false;
            }
        }

        return true;
    }

    protected function validateFormShipping(Form $form): bool
    {
        // At least one shipping method has to be selected
        $data = $form->getData();
        if ((false === $data['shipping_standardDelivery']) && (false === $data['shipping_handDelivery'])) {
            fn_set_notification('E', __('error'), __('error_validator_required', ['[field]' => __('shipping')]));
            return false;
        }
        return true;
    }

    /**
     * @throws NotFoundHttpException
     */
    protected function getDeclinations(\Wizacha\Marketplace\ReadModel\Product $product, bool $removeOutOfStock = false): array
    {
        $declinations = $product->getData() ?? [];

        if (!$declinations) {
            throw new NotFoundHttpException();
        }

        if ($removeOutOfStock) {
            $declinations = array_filter($declinations, function ($item) {
                 return $item['in_stock'];
            });
        }

        // Format prices
        // We can't do that in Twig/Smarty because Mustache templates use this data and Mustache cannot do
        // any formatting (no logic in Mustache templates)
        $indexedDeclinations = [];
        $priceFormatter = $this->get('marketplace.price.formatter');
        foreach ($declinations as $declination) {
            if (!empty($declination['crossed_out_price'])) {
                $declination['formatted_price'] = $priceFormatter->formatFloat((float) $declination['crossed_out_price']);
                $declination['promotions'] = ($declination['crossed_out_price'] != $declination['final_price']);
            } else {
                $declination['formatted_price'] = $priceFormatter->formatFloat((float) $declination['price']);
                $declination['promotions'] = ($declination['price'] != $declination['final_price']);
            }
            $declination['formatted_reduced_price'] = $priceFormatter->formatFloat((float) $declination['final_price']);
            $indexedDeclinations[$declination['objectID']] = $declination;
        }

        return $indexedDeclinations;
    }

    private function isC2CEnabled(): bool
    {
        return (bool) $this->container->getParameter('feature.enable_c2c');
    }

    /**
     * @return ProductSummary[]
     */
    protected function getProductsOfTheSameCompany($productId, int $maxProductCount): array
    {
        if (!is_numeric($productId)) {
            // Skip MVP
            return [];
        }

        $currentProduct = new Product($productId);
        $productSearch = $this->get('marketplace.search.product_index');

        return $productSearch->search('', new Pagination(1, $maxProductCount), [
            'companies' => $currentProduct->getCompanyId(),
        ], null, null, ['noFacets' => true])->getProducts();
    }
}
