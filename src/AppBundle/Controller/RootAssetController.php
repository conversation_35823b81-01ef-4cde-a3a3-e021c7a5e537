<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\Controller;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;

class RootAssetController extends Controller
{
    public function robotsTxtAction()
    {
        $response = new Response();
        $response->headers->set('Content-Type', 'text/plain');
        $template = $this->container->getParameter('feature.sandbox') ? '@App/rootasset/robots_sandbox.txt.twig' : '@App/rootasset/robots.txt.twig';

        return $this->render($template, [], $response);
    }

    public function versionTxtAction()
    {
        $response = new Response($this->container->getParameter('marketplace.version'));
        $response->headers->set('Content-Type', 'text/plain');

        return $response;
    }

    public function sitemapXmlAction(Request $request)
    {
        $storage = container()->get("Wizacha\Storage\SitemapStorageService");

        if ($page = $request->query->getInt('page')) {
            $filename = 'sitemap' . $page . '.xml';
        } else {
            $filename =  'sitemap.xml';
        }

        if (!$storage->isExist($filename)) {
            throw $this->createNotFoundException();
        }

        $stream = $storage->readStream($filename);
        $response = new StreamedResponse(function () use ($stream) {
            while (!feof($stream)) {
                echo fread($stream, 8192);
                flush();
            }
            fclose($stream);
        });
        $response->headers->set('Content-Type', 'text/xml; charset=utf-8');

        return $response;
    }
}
