<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Controller;

use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\FormType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Wizacha\AppBundle\Form\Type\LoginType;

class ProfileController extends BaseController
{
    public function compactAction(Request $request)
    {
        $user = $this->getUser();

        if (!$user) {
            static $id = 0;

            $returnUrl = $request->query->get('return_url') ?: $request->headers->get('referer');
            $redirectUrl = $request->query->get('redirect_url') ?: $request->headers->get('referer');

            return $this->render('@App/frontend/views/profile/connect_form.html.twig', [
                'recaptcha_number' => ++$id,
                'login_form' => $this->getLoginForm($request, $returnUrl, $redirectUrl),
                'return_url' => $returnUrl,
                'redirect_url' => $redirectUrl,
            ]);
        }

        return $this->render('@App/frontend/views/profile/compact.html.twig', [
            "user" => $user,
        ]);
    }

    public function loginAction(Request $request)
    {
        $session = $this->container->get('session');
        if ($session->get('auth')['user_id']) {
            return $this->redirect(fn_url('?dispatch=profiles.update'));
        }

        $returnUrl = $request->query->get('return_url') ?: $request->headers->get('referer');
        $redirectUrl = $request->query->get('redirect_url') ?: $request->headers->get('referer');

        return $this->render('@App/frontend/profile/login.html.twig', [
            'login_form' => $this->getLoginForm($request, $returnUrl, $redirectUrl),
            'register_form' => static::getRegisterForm($request),
        ]);
    }

    public function loginPopupAction(Request $request)
    {
        $session = $this->container->get('session');
        if ($session->get('auth')['user_id']) {
            return new Response();
        }

        $returnUrl = $request->query->get('return_url') ?: $request->headers->get('referer');
        $redirectUrl = $request->query->get('redirect_url') ?: $request->headers->get('referer');

        return $this->render('@App/frontend/profile/login-popup.html.twig', [
            'login_form' => $this->getLoginForm($request, $returnUrl, $redirectUrl),
            'register_form' => static::getRegisterForm($request),
        ]);
    }

    /**
     * @param $request
     * @return \Symfony\Component\Form\FormView
     */
    private function getLoginForm(Request $request, $returnUrl, $redirectUrl)
    {
        $formFactory = $this->container->get('form.factory');

        $data = [
            'return_url' => $returnUrl,
            'redirect_url' => $redirectUrl,
        ];

        $form = $formFactory->createNamedBuilder(null, LoginType::class, $data, [
            "action" => fn_url("auth.login"),
        ])->getForm();
        $form->handleRequest($request);

        return $form->createView();
    }

    /**
     * @param $request
     * @return \Symfony\Component\Form\FormView
     */
    private function getRegisterForm($request)
    {
        $formFactory = $this->container->get('form.factory');

        $userData = $formFactory->createNamedBuilder('user_data')
            ->add('email', EmailType::class, ['label' => 'email'])
            ->add('password1', PasswordType::class, ['label' => 'password']);

        $builder = $formFactory->createNamedBuilder(null)
            ->add(
                'return_url',
                HiddenType::class,
                ['data' => ($request->query->get('return_url') ?: $request->headers->get('referer'))]
            )
            ->add(
                'register',
                SubmitType::class,
                ['attr' => ['value' => 'register']]
            )
            ->add(
                'terms_approved',
                CheckboxType::class,
                ['required' => true]
            )
            ->add($userData, FormType::class);

        if ($request->query->has('additional_info')) {
            switch ($request->query->get('additional_info')) {
                case "pseudo":
                    $userData->add('c2c_vendor_pseudo', TextType::class, ['label' => 'w_c2c_company_name', 'required' => true]);
                    break;
                default:
                    break;
            }
        }

        $form = $builder->getForm();
        $form->handleRequest($request);

        return $form->createView();
    }

    public function recoverPasswordAction(Request $request)
    {
        if ($request->isMethod('POST')) {
            $email = $request->request->get('email');
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                fn_set_notification('E', __('error'), __('empty_email_recovery'));

                return $this->redirectToRoute('recover_password');
            }
            $this->get('marketplace.user.user_service')->recoverPassword($email);
            fn_set_notification('N', __('well_done'), __('text_password_recovery_instructions_sent'));

            return $this->redirectToRoute('home');
        }

        return $this->render('@App/frontend/views/profile/recover-password.html.twig');
    }
}
