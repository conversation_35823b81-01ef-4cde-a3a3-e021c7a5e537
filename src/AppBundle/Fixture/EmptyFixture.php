<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Fixture;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Test\Fixture\Image\ImageLoader;

class EmptyFixture implements Fixture
{
    /**
     * @var Connection
     */
    private $db;

    /** @var ImageLoader */
    private $imageLoader;

    public function __construct(Connection $db, ImageLoader $imageLoader)
    {
        $this->db = $db;
        $this->imageLoader = $imageLoader;
    }

    public function install(InputInterface $input, OutputInterface $output)
    {
        // TODO setup default taxes, etc.

        $this->createProfileFields();
        $this->createPaymentMethods();
    }

    private function createProfileFields(): void
    {
        $this->db->exec('TRUNCATE cscart_profile_field_descriptions');
        $this->db->exec('TRUNCATE cscart_profile_field_values');

        $profileFields = [
            [1, 'Monsieur', 'V', 'fr'],
            [2, 'Madame', 'V', 'fr'],
            [3, 'Madame', 'V', 'fr'],
            [4, 'Monsieur', 'V', 'fr'],
            [6, 'Prénom', 'F', 'fr'],
            [7, 'Nom', 'F', 'fr'],
            [8, 'Société', 'F', 'fr'],
            [9, 'Téléphone', 'F', 'fr'],
            [10, 'Fax', 'F', 'fr'],
            [11, 'Site', 'F', 'fr'],
            [14, 'Prénom', 'F', 'fr'],
            [15, 'Prénom', 'F', 'fr'],
            [16, 'Nom', 'F', 'fr'],
            [17, 'Nom', 'F', 'fr'],
            [18, 'Adresse', 'F', 'fr'],
            [19, 'Adresse', 'F', 'fr'],
            [20, 'Complément adresse', 'F', 'fr'],
            [21, 'Complément d\'adresse', 'F', 'fr'],
            [22, 'Ville', 'F', 'fr'],
            [23, 'Ville', 'F', 'fr'],
            [24, 'State/province', 'F', 'fr'],
            [25, 'State/province', 'F', 'fr'],
            [26, 'Pays', 'F', 'fr'],
            [27, 'Pays', 'F', 'fr'],
            [28, 'Code postal', 'F', 'fr'],
            [29, 'Code postal', 'F', 'fr'],
            [30, 'Téléphone', 'F', 'fr'],
            [31, 'Téléphone', 'F', 'fr'],
            [32, 'E-mail', 'F', 'fr'],
            [33, 'E-mail', 'F', 'fr'],
            [35, 'Nom de l\'adresse (domicile, travail...)', 'F', 'fr'],
            [36, 'Civilité', 'F', 'fr'],
            [37, 'Civilité', 'F', 'fr'],
            [38, 'Civilité', 'F', 'fr'],
            [39, 'Société', 'F', 'fr'],
            [40, 'Société', 'F', 'fr'],
            [41, 'Date de naissance', 'F', 'fr'],
            [42, 'Division', 'F', 'fr'],
            [43, 'Division', 'F', 'fr'],
        ];
        foreach ($profileFields as $profileField) {
            $this->db->insert('cscart_profile_field_descriptions', [
                'object_id' => $profileField[0],
                'description' => $profileField[1],
                'object_type' => $profileField[2],
                'lang_code' => $profileField[3],
            ]);
        }
        $this->db->exec('INSERT INTO cscart_profile_field_values VALUES (1,36,10),(2,36,20),(3,38,2),(4,38,1)');
    }

    private function createPaymentMethods(): void
    {
        $ccImageId = $this->imageLoader->createFromFile(__DIR__ . '/Image/cc.png');

        $this->db->exec('TRUNCATE cscart_payments');
        $this->db->exec('TRUNCATE cscart_payment_descriptions');
        $this->db->exec("INSERT INTO cscart_payments VALUES
                          (1,0,'0',0,'A',0,'',0.000,0.000,'','','tab1',1.000,0.130,0,0,null),
                          (2,0,'0',1,'A',0,'',0.000,0.000,'','','tab1',1.000,0.130,0,0,null),
                          (3,0,'0',2,'A',0,'',0.000,0.000,'','','tab1',1.000,0.130,0,0,null),
                          (4,0,'0',3,'A',1008,'',0.000,0.000,'','','tab1',NULL,NULL,0,0,null);
");
        $this->db->exec("INSERT INTO cscart_payment_descriptions VALUES
                          (1,'CB','Carte Bleue','','','fr'),
                          (2,'Visa','Carte Visa','','','fr'),
                          (3,'MasterCard','Carte MasterCard','','','fr'),
                          (4,'Paiement à échéance','','','','fr')");
        $this->db->exec("INSERT INTO cscart_images_links (`object_id`, `object_type`, `image_id`) VALUES
                          ('1','payment', $ccImageId)");
    }
}
