<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Fixture;

use Faker\Generator;
use Psr\Http\Message\ResponseInterface;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Tygh\Languages\Languages;
use Wizacha\AppBundle\Fixture\Module\CmsFixture;
use Wizacha\AppBundle\Fixture\Module\DivisionFixture;
use Wizacha\AppBundle\Fixture\Module\UserFixture;
use Wizacha\Component\Locale\Locale;
use Wizacha\Marketplace\Address\Address;
use Wizacha\Marketplace\Entities\Declination;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\Order\Adjustment\OrderAdjustment;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderItem;
use Wizacha\Marketplace\Order\OrderMutator;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\Organisation\Organisation;
use Wizacha\Marketplace\PIM\Video\Video;
use Wizacha\Marketplace\RelatedProduct\RelatedProduct;
use Wizacha\Marketplace\RelatedProduct\RelatedProductsType;
use Wizacha\Marketplace\Shipping\DeliveryType;
use Wizacha\Marketplace\Transaction\TransactionStatus;
use Wizacha\Marketplace\Transaction\TransactionType;
use Wizacha\Marketplace\User\UserTitle;
use Wizacha\Marketplace\User\UserType;
use Wizacha\Money\Money;
use Wizacha\Premoderation;
use Wizacha\Product;
use Wizacha\Status;
use Wizacha\Test\Fixture\Banner\CreateBanner;
use Wizacha\Test\Fixture\Category\CategoryLoader;
use Wizacha\Test\Fixture\Category\CreateCategory;
use Wizacha\Test\Fixture\Company\CreateCompany;
use Wizacha\Test\Fixture\MultiVendorProduct\CreateLink;
use Wizacha\Test\Fixture\MultiVendorProduct\CreateMultiVendorProduct;
use Wizacha\Test\Fixture\MultiVendorProduct\MultiVendorProductLoader;
use Wizacha\Test\Fixture\Product\Attribute\CreateAttribute;
use Wizacha\Test\Fixture\Product\CreateProduct;
use Wizacha\Test\Fixture\Product\Option\CreateOption;
use Wizacha\Test\Fixture\Product\ProductLoader;
use Wizacha\Test\Fixture\Promotion\PromotionLoader;
use Wizacha\Test\Fixture\RelatedProduct\CreateRelatedProduct;
use Wizacha\Test\Fixture\Shipping\CreateShipping;
use Wizacha\Test\Fixture\Shipping\LinkShippingWithCompany;
use Wizacha\Test\Fixture\Transaction\CreateTransaction;
use Wizacha\Test\Fixture\Transaction\TransactionLoader;
use Wizacha\Test\Fixture\User\CreateAdmin;
use Wizacha\Test\Fixture\User\CreateUser;
use Wizacha\Test\Fixture\User\CreateVendor;
use Wizacha\Test\Mock\FakeUuidGenerator;

/**
 * Cette fixture contient un jeu de données précis
 * permettant de faire certaines vérifications techniques.
 */
class TechnicalFixture implements Fixture
{
    /**
     * @var ContainerInterface
     */
    private $container;

    /**
     * @var PromotionLoader
     */
    private $promotionLoader;

    /**
     * @var TransactionLoader
     */
    private $transactionLoader;

    /** @var OrderMutator */
    private $orderMutator;

    /** @var MultiVendorProductLoader */
    private $multiVendorProductLoader;

    /**
     * We inject the container because we'll be using a lot of other fixture classes.
     */
    public function __construct(
        ContainerInterface $container,
        PromotionLoader $promotionLoader,
        TransactionLoader $transactionLoader,
        OrderMutator $orderMutator,
        MultiVendorProductLoader $multiVendorProductLoader
    ) {
        $this->container = $container;
        $this->promotionLoader = $promotionLoader;
        $this->transactionLoader = $transactionLoader;
        $this->orderMutator = $orderMutator;
        $this->multiVendorProductLoader = $multiVendorProductLoader;
    }

    public function install(InputInterface $input, OutputInterface $output): void
    {
        $output->writeln('<comment>Installing technical fixtures...</comment>');

        $this->container->set('broadway.uuid.generator', new FakeUuidGenerator($this->container->get(Generator::class)));
        $this->container->get(EmptyFixture::class)->install($input, $output);
        $this->addLanguages();
        $this->container->get(DivisionFixture::class)->install($input, $output);
        $this->container->get(UserFixture::class)->install($input, $output);
        $this->container->get(CmsFixture::class)->install($input, $output);

        $this->installAdminCompany();
        $shippings = $this->installShippings();
        $this->installPromotions();
        $companiesIds = $this->installCompanies($shippings);
        $userIds = $this->installUsers();
        $productIds = $this->installCatalog($companiesIds, $shippings);
        $this->makeOrder($userIds[2], [['id' => $productIds[0]]]);
        $this->makeOrder($userIds[2], [['id' => $productIds[3], 'count' => 2], ['id' => $productIds[1], 'combination' => '6_4_7_6']]);
        $this->makeOrder($userIds[3], [['id' => $productIds[5]]]);
        $this->makeOrder($userIds[2], [['id' => $productIds[0], 'comment' => 'Please, gift wrap this product.']], OrderStatus::COMPLETED(), 'Please deliver at the front desk of my company.');
        $this->makeOrder($userIds[2], [['id' => $productIds[0]]], OrderStatus::STANDBY_VENDOR(), '', ['SUPERPROMO']);

        // création d'une commande avec un paiement à échénance, en attente du numéro d'engagement
        $this->makeOrder($userIds[2], [['id' => $productIds[0]]], null, '', [], 4); // id = 6
        $order = $this->container->get('marketplace.order.order_service')->getOrder(6);
        $this->container->get('marketplace.order.action.mark_payment_deferment_as_authorized')->execute($order);

        //Création de commandes pour une organisation
        $this->makeOrder($userIds[6], [['id' => $productIds[0]]], OrderStatus::COMPLETED());
        $this->makeOrder($userIds[7], [['id' => $productIds[5], 'count' => 2]], OrderStatus::COMPLETED());
        $this->makeOrder($userIds[8], [['id' => $productIds[3]]], OrderStatus::COMPLETED());

        $this->makeOrderAdjustment(1);

        $organisations = $this->container->get('marketplace.organisation.service')->list();
        $this->makeOrganisationOrder($organisations[1], 7);
        $this->makeOrganisationOrder($organisations[1], 8);
        $this->makeOrganisationOrder($organisations[1], 9);

        // Création de commande mondial relay (id = 42)
        $this->makeOrder($userIds[2], [['id' => $productIds[1]]], OrderStatus::STANDBY_VENDOR(), '', ['SUPERPROMO'], 1, 42);

        // Order with status Billing Failed
        $this->makeOrder($userIds[2], [['id' => $productIds[1]]], OrderStatus::BILLING_FAILED());

        $this->installMarketplacePromotion($userIds[2], $productIds[1]);

        $this->installRelatedProduct($productIds[0], $productIds[1]);
        $this->installRelatedProduct($productIds[0], $productIds[2], true);
        $this->installRelatedProduct($productIds[0], $productIds[3]);
        $this->installRelatedProduct($productIds[1], $productIds[2], false, true);
        $this->installRelatedProduct($productIds[1], $productIds[3], true, true);

        $this->installProductReview($userIds[2], $productIds[0], 4, 'Very good product <3');
        $this->installProductReview($userIds[3], $productIds[0], 1, 'I try to forget it !');
        $this->installCompanyReview($userIds[2], $companiesIds[0], 4, 'Very good customer support.');
        $this->installCompanyReview($userIds[3], $companiesIds[0], 5, 'This company is unbelievable !!');
        $this->installDiscussion($userIds[2], $productIds[3]);
        $this->installCmsPages();
        $this->installAttributes();
        $this->installBanners();
    }

    private function addLanguages()
    {
        Languages::update([
            'lang_code' => 'en',
            'name' => 'English',
            'status' => Languages::ACTIVE,
            'country_code' => 'GB',
        ], null);
    }

    private function installShippings(): array
    {
        $shippingLoader = $this->container->get('marketplace.test.shipping_loader');

        $createShipping = new CreateShipping('Lettre prioritaire', false, (string) DeliveryType::STANDARD());
        $createShipping2 = new CreateShipping('Colissmo', false, (string) DeliveryType::STANDARD());
        $createShipping3 = new CreateShipping('Remise en main propre (avec code)', true, (string) DeliveryType::HAND_WITH_CODE());
        $createShipping4 = new CreateShipping('ChronoRelais', false, (string) DeliveryType::CHRONO_RELAIS());
        $createShipping5 = new CreateShipping('MondialRelay', false, (string) DeliveryType::MONDIAL_RELAY());

        $shippings = [
            [
                'id' => $shippingLoader->create($createShipping),
                'name' => $createShipping->name,
            ],
            [
                'id' => $shippingLoader->create($createShipping2),
                'name' => $createShipping2->name,
            ],
            [
                'id' => $shippingLoader->create($createShipping3),
                'name' => $createShipping3->name,
            ],
            [
                'id' => $shippingLoader->create($createShipping4),
                'name' => $createShipping4->name,
            ],
            [
                'id' => $shippingLoader->create($createShipping5),
                'name' => $createShipping5->name,
            ],
        ];

        GlobalState::switchContentTo(new Locale('en'));
        $shippingLoader->update($shippings[0]['id'], $createShipping);
        $shippingLoader->update($shippings[1]['id'], $createShipping2);
        $shippingLoader->update($shippings[2]['id'], $createShipping3);
        $shippingLoader->update($shippings[3]['id'], $createShipping4);
        $shippingLoader->update($shippings[4]['id'], $createShipping5);
        GlobalState::switchContentTo(new Locale('fr'));

        return $shippings;
    }

    private function installCompanies(array $shippings): array
    {
        $companyLoader = $this->container->get('marketplace.test.company_loader');

        // Création de la company principale contenant tout ce qu'il faut pour le tester correctement.
        $companyCommand = new CreateCompany('The World Company Inc.');
        $companyCommand->latitude = 45.778848;
        $companyCommand->longitude = 4.800039;
        $companyCommand->email = '<EMAIL>';
        $companyCommand->c2c = false;
        $companyCommand->iban = '****************************';
        $companyCommand->bic = 'ABNAFRPP';
        $companyCommand->vatNumber = 'FR83404833049';
        $companyCommand->siretNumber = '40483304800023';
        $companyId = $companyLoader->create($companyCommand);

        // Ajout des shippings à la company principale
        $shippingLoader = $this->container->get('marketplace.test.shipping_loader');
        $shippingLoader->linkWithCompany(new LinkShippingWithCompany(
            $shippings[0]['name'],
            $companyId
        ));
        $shippingLoader->linkWithCompany(new LinkShippingWithCompany(
            $shippings[1]['name'],
            $companyId
        ));
        $shippingLoader->linkWithCompany(new LinkShippingWithCompany(
            $shippings[4]['name'],
            $companyId
        ));

        // Création d'une company particulière C2C
        $company = new CreateCompany('C2C company');
        $company->c2c = true;
        $company->iban = '************************';
        $company->bic = 'AECFFR21';
        $company->vatNumber = 'FR83404833050';
        $company->siretNumber = '40483304800024';
        $c2cCompanyId = $companyLoader->create($company);

        // Création d'une company avec shipping ChronoRelais
        $company = new CreateCompany('Chrono company');
        $company->c2c = true;
        $company->iban = '********************';
        $company->bic = 'AFRIFRPP';
        $company->vatNumber = 'FR83404833051';
        $company->siretNumber = '40483304800025';
        $chronoCompanyId = $companyLoader->create($company);
        $shippingLoader->linkWithCompany(new LinkShippingWithCompany(
            $shippings[0]['name'],
            $chronoCompanyId
        ));
        $shippingLoader->linkWithCompany(new LinkShippingWithCompany(
            $shippings[3]['name'],
            $chronoCompanyId
        ));

        return [$companyId, $c2cCompanyId, $chronoCompanyId];
    }

    private function installUsers(): array
    {
        $userLoader  = $this->container->get('marketplace.test.user_loader');
        $userService  = $this->container->get('marketplace.user.user_service');
        $userIds = [];

        // Création d'un admin
        $admin = new CreateAdmin('<EMAIL>');
        $admin->title = UserTitle::MRS();
        $admin->firstname = 'Emmanuelle';
        $admin->lastname = 'Charpentier';
        $admin->password = static::VALID_PASSWORD;
        $admin->fillBillingAddress = true;
        $admin->fillShippingAddress = true;
        $userIds[] = $userLoader->createAdmin($admin);

        // Création d'un vendeur
        $vendor = new CreateVendor('The World Company Inc.');
        $vendor->email = '<EMAIL>';
        $vendor->title = UserTitle::MR();
        $vendor->firstname = 'Noam';
        $vendor->lastname = 'Chomsky';
        $vendor->password = static::VALID_PASSWORD;
        $admin->fillBillingAddress = true;
        $admin->fillShippingAddress = true;
        $userIds[] = $userLoader->createVendor($vendor);

        // Création d'un customer
        $customer = new CreateUser('<EMAIL>');
        $customer->title = UserTitle::MR();
        $customer->fillShippingAddress = true;
        $customer->fillBillingAddress = true;
        $customer->firstname = 'Michael';
        $customer->lastname = 'Jordan';
        $customer->password = static::VALID_PASSWORD;
        $customer->birthday = \DateTime::createFromFormat('U', '*********');
        $userIds[] = $userLoader->createCustomer($customer);

        // Création d'un deuxième customer
        $customer = new CreateUser('<EMAIL>');
        $customer->title = UserTitle::MR();
        $customer->fillShippingAddress = true;
        $customer->fillBillingAddress = true;
        $customer->firstname = 'Dave';
        $customer->lastname = 'Matthews';
        $customer->password = static::VALID_PASSWORD;
        $userIds[] = $userLoader->createCustomer($customer);

        // Création d'un 3eme customer (sans appartenance à une company...)
        $customer = new CreateUser('<EMAIL>');
        $customer->title = UserTitle::MRS();
        $customer->fillShippingAddress = true;
        $customer->fillBillingAddress = true;
        $customer->firstname = 'Jenna';
        $customer->lastname = 'Jameson';
        $customer->password = static::VALID_PASSWORD;
        $userIds[] = $userLoader->createCustomer($customer);

        // Création d'un 4eme customer (avec un "password recovery" en cours)
        $customer = new CreateUser('<EMAIL>');
        $customer->title = UserTitle::MRS();
        $customer->fillShippingAddress = true;
        $customer->fillBillingAddress = true;
        $customer->firstname = 'Abby';
        $customer->lastname = 'Wambach';
        $customer->password = static::VALID_PASSWORD;
        $userIds[] = $userLoader->createCustomer($customer);
        $this->container->get('doctrine.dbal.default_connection')->insert(
            'cscart_ekeys',
            [
                'object_id' => end($userIds),
                'object_type' => 'U',
                'ekey' => md5('fake_secret_token'),
                'ttl' => strtotime("+1 day"),
            ]
        );

        $organisationService = $this->container->get('marketplace.organisation.service');

        // Création d'une organisation en attente de modération
        $pendingOrganisation = $organisationService->register(
            'University of New York',
            '<EMAIL>',
            static::VALID_PASSWORD,
            [
                'address' => '194 Lindale Avenue',
                'zipCode' => '94801',
                'city' => 'Richmond',
                'country' => 'US',
            ],
            [
                'address' => '4917 Snyder Avenue',
                'zipCode' => '28209',
                'city' => 'North Carolina',
                'country' => 'US',
            ],
            '44229377500031',
            'FR99999999999',
            'Technical Director',
            new \SplFileObject('tests/data/misc/logo-URL.jpg'),
            new \SplFileObject('tests/data/misc/logo-URL.jpg'),
            'University of New York',
            'NTW',
            'Network Infrastructure',
            UserTitle::MR(),
            'Mark',
            'Flemming'
        );
        fn_update_user_profile(
            $pendingOrganisation->getAdminGroup()->getUsers()[0]->getUserId(),
            [
                'b_division_code' => 'FR-03',
                's_division_code' => 'FR-01',
            ],
            'update'
        );

        // Création d'une organisation active
        $organisation = $organisationService->register(
            'University of Southern California',
            '<EMAIL>',
            static::VALID_PASSWORD,
            [
                'address' => '42228 Hunter Summit Suite 058',
                'zipCode' => '13736-4550',
                'city' => 'Bettyeburgh',
                'country' => 'FR',
            ],
            [
                'address' => '99410 Dach Views Apt. 994',
                'zipCode' => '00229',
                'city' => 'West Geraldport',
                'country' => 'FR',
            ],
            '80295478500028',
            'FR63802954785',
            'occupation',
            new \SplFileObject('tests/data/misc/logo-URL.jpg'),
            new \SplFileObject('tests/data/misc/logo-URL.jpg'),
            'Southern California',
            'IT',
            'Information Technology',
            UserTitle::MR(),
            'Antonio',
            'Damasio'
        );
        $organisationService->approve($organisation->getId());

        $orgaUserGroup = $this->container->get('marketplace.organisation.user_group_service')->create(
            $organisation,
            'Teachers',
            'teachers'
        );
        $organisationAdminUserId = $organisation->getAdminGroup()->getUsers()[0]->getUserId();
        fn_update_user_profile(
            $organisationAdminUserId,
            [
                'b_division_code' => 'FR-03',
                's_division_code' => 'FR-01',
            ],
            'update'
        );
        $userIds[] = $organisationAdminUserId;

        // Création d'un utilisateur d'une organisation
        $customer = new CreateUser('<EMAIL>');
        $customer->title = UserTitle::MR();
        $customer->fillShippingAddress = true;
        $customer->fillBillingAddress = true;
        $customer->firstname = 'John';
        $customer->lastname = 'Smith';
        $customer->password = static::VALID_PASSWORD;
        $userId = $userLoader->createCustomer($customer);
        $userIds[] = $userId;
        $user = $userService->get($userId);
        $user->setOrganisation($organisation);
        $orgaUserGroup->addUser($user);

        // Création d'un 2 eme utilisateur d'une organisation
        $customer = new CreateUser('<EMAIL>');
        $customer->title = UserTitle::MR();
        $customer->fillShippingAddress = true;
        $customer->fillBillingAddress = true;
        $customer->firstname = 'Patric';
        $customer->lastname = 'Simpsons';
        $customer->password = static::VALID_PASSWORD;
        $userId = $userLoader->createCustomer($customer);
        $userIds[] = $userId;
        $user = $userService->get($userId);
        $user->setOrganisation($organisation);
        $orgaUserGroup->addUser($user);

        return $userIds;
    }

    private function installPromotions(): void
    {
        $this->promotionLoader->createBasketPromotion(Money::fromVariable(1.2), 'SUPERPROMO');
    }

    private function installCatalog(array $companiesIds, array $shippings)
    {
        $attributeLoader = $this->container->get('marketplace.test.attribute_loader');
        $complexAttributes = $attributeLoader->generateComplexAttributesAndRetrieveValues();
        $additionalAttributes = $attributeLoader->generateAdditionalAttributesAndRetrieveValues();
        $freeAttributes = $attributeLoader->getComplexFreeAttributes();

        $catalog = [
            [
                'category'  => [
                    'fr' => new CreateCategory('Informatique'),
                    'en' => new CreateCategory('IT'),
                ],
                'products'  => [
                    [
                        'name'    => [
                            'fr' => 'Z11 Plus Boîtier PC en Acier ATX',
                            'en' => 'Z11 ATX Plus PC Steel case',
                        ],
                        'slug'    => 'test-product-slug',
                        'companyId' => $companiesIds[0],
                        'price' => 67.90,
                        'code' => '978020137962',
                        'supplierRef' => 'INFO-001',
                        'divisions' => ['FR', 'FR-ARA', 'FR-01', 'FR-03', 'FR-69'],
                    ],
                    [
                        'name'  => [
                            'fr' => 'Souris sans fil avec récepteur nano 6 boutons',
                            'en' => '6 buttons wireless mouse with nano receptor',
                        ],
                        'companyId' => $companiesIds[0],
                        'price' => 7.99,
                        'code' => '90204479D2',
                        'supplierRef' => 'INFO-002',
                        'isBrandNew' => false,
                        'maxPriceAdjustment' => 50,
                        'options' => [
                            [
                                'name' => 'color',
                                'variants' => ['white', 'black', 'blue', 'red'],
                            ],
                            [
                                'name' => 'connectivity',
                                'variants' => ['wireless', 'wired'],
                            ],
                        ],
                        'divisions' => ['FR', 'FR-ARA', 'FR-01', 'FR-03', 'FR-69'],
                    ],
                ],
                'children'  => [
                    [
                        'category'  => [
                            'fr' => new CreateCategory('Écrans'),
                            'en' => new CreateCategory('Screens'),
                        ],
                        'products'   => [
                            [
                                'name'  => [
                                    'fr' => 'Ecran PC Full HD Noir',
                                    'en' => 'Full HD Black PC Screen',
                                ],
                                'companyId' => $companiesIds[0],
                                'price' => 143.66,
                                'code' => '0000001',
                                'supplierRef' => 'INFO-ECRAN-001',
                                'videoId' => '414375b2-61cb-4260-b82b-4a2636cb5673',
                                'options' => [
                                    [
                                        'name' => 'size',
                                        'variants' => ['13', '15', '17', '21'],
                                    ],
                                ],
                                'divisions' => ['FR', 'FR-ARA', 'FR-01', 'FR-03', 'FR-69'],
                            ],
                        ],
                    ],
                    [
                        'category' => [
                            'fr' => new CreateCategory('Casques'),
                            'en' => new CreateCategory('Headsets'),
                        ],
                        'products' => [
                            [
                                'name'    => [
                                    'fr' => 'Corsair Gaming VOID Pro RGB Dolby 7.1 Sans fil - Edition Carbon',
                                    'en' => 'Corsair Gaming VOID Pro RGB Dolby 7.1 Wireless - Edition Carbon',
                                ],
                                'slug'    => 'casque-corsair-gaming',
                                'companyId' => $companiesIds[0],
                                'price' => 54.20,
                                'code' => '7531596248951',
                                'supplierRef' => 'CORSAIR-CASQUE-GAMING',
                                'seoTitle' => 'Micro-Casque Corsair Gaming',
                                'seoDescription' => 'Achat Micro-casque Corsair Gaming VOID Pro RGB Dolby 7.1 Wireless - Edition Carbon sur notre MarketPlace. Casque-micro 7.1 sans fil pour gamer.',
                                'divisions' => ['FR', 'FR-ARA', 'FR-01', 'FR-03', 'FR-69'],
                            ],
                            [
                                'name'    => [
                                    'fr' => 'Logitech G430 Casque Gaming pour PC Gaming, PS4, Xbox One with 7.1 Dolby Surround',
                                    'en' => 'Logitech G430 Gaming Headset for PC Gaming, PS4, Xbox One with 7.1 Dolby Surround',
                                ],
                                'slug'    => 'casque-logitech-gaming',
                                'companyId' => $companiesIds[0],
                                'price' => 61.50,
                                'code' => '7531596248952',
                                'supplierRef' => 'LOGITECH-CASQUE-GAMING',
                                'seoTitle' => 'Micro-Casque Logitech Gaming',
                                'divisions' => ['FR', 'FR-ARA', 'FR-01', 'FR-03', 'FR-69'],
                            ],
                            [
                                'name'    => [
                                    'fr' => 'Casque Gaming Razer ManO\'War Sans fil 7.1 Surround (PC/PS4)',
                                    'en' => 'Razer ManO\'War Wireless 7.1 Surround Sound Gaming Headset (PC/PS4)',
                                ],
                                'slug'    => 'casque-razer-gaming',
                                'companyId' => $companiesIds[0],
                                'price' => 73.70,
                                'code' => '7531596248953',
                                'supplierRef' => 'RAZER-CASQUE-GAMING',
                                'seoDescription' => 'Achat Micro-casque Razer ManO\'War 7.1 Wireless - Edition Carbon sur notre MarketPlace. Casque-micro 7.1 sans fil pour gamer.',
                                'divisions' => ['FR', 'FR-ARA', 'FR-01', 'FR-03', 'FR-69'],
                            ],
                        ],
                    ],
                ],
            ],
            [
                'category'  => [
                    'fr' => new CreateCategory('Special category dedicated to specific tests'),
                    'en' => new CreateCategory('Special category dedicated to specific tests en'),
                ],
                'products'   => [
                    [
                        // Produit utilisé pour le test de recherche dans le SDK
                        'name'  => [
                            'fr' => 'Product with shippings',
                            'en' => 'Product with shippings en',
                        ],
                        'companyId' => $companiesIds[0],
                        'price' => 9.90,
                        'code' => '0493020427963',
                        'supplierRef' => 'TEST-SHIPPINGS',
                        'shippings' => [$shippings[0], $shippings[1]],
                        'shortDescription' => "La nouvelle génération de notre tablette Fire phare - désormais plus fine, plus légère, dotée d'une plus longue autonomie et d'un écran amélioré.",
                        'geolocation' => [45.75845, 4.799044, 'Lyon 5e', '69005'],
                        'divisions' => ['FR', 'FR-ARA', 'FR-01', 'FR-03', 'FR-69'],
                    ],
                    [
                        'name'  => [
                            'fr' => 'Product with complex attributes',
                            'en' => 'Product with complex attributes en',
                        ],
                        'companyId' => $companiesIds[0],
                        'price' => 15.,
                        'code' => '32094574920',
                        'supplierRef' => 'TEST-ATTRIBUTES',
                        'attributes' => $complexAttributes,
                        'freeAttributes' => $freeAttributes,
                        'divisions' => ['FR', 'FR-ARA', 'FR-01', 'FR-03', 'FR-69'],
                    ],
                    [
                        'name'  => [
                            'fr' => 'Product with valid geoloc',
                            'en' => 'Product with valid geoloc en',
                        ],
                        'companyId' => $companiesIds[0],
                        'price' => 0.5,
                        'code' => '20230495445',
                        'supplierRef' => 'TEST-GEOLOC',
                        'geoloc' => [
                            45.778848,
                            4.800039,
                            'Wizacha',
                            '69009',
                        ],
                        'divisions' => ['FR', 'FR-ARA', 'FR-01', 'FR-03', 'FR-69'],
                        'attributes' => $additionalAttributes,
                    ],
                    [
                        'name'  => [
                            'fr' => 'Product with attachments',
                            'en' => 'Product with attachments en',
                        ],
                        'companyId' => $companiesIds[0],
                        'price' => 0.5,
                        'code' => '20230495446',
                        'supplierRef' => 'TEST-ATTACHMENT',
                        'attachments' => [
                            [
                                'src' => __DIR__ . '/../../../tests/data/misc/minimal.pdf',
                                'name' => 'Manuel de montage',
                            ],
                        ],
                        'divisions' => ['FR', 'FR-ARA', 'FR-01', 'FR-03', 'FR-69'],
                        'quoteRequestMinQuantity' => 0
                    ],
                    [
                        'name'  => [
                            'fr' => 'Test MVP',
                            'en' => 'MVP Test',
                        ],
                        'code' => '20230495447',
                        'supplierRef' => 'TEST-MVP',
                        'attributes' => $complexAttributes,
                        'freeAttributes' => $freeAttributes,
                        'attached_products' => [
                            [
                                'name'  => [
                                    'fr' => 'Product in MVP 1',
                                    'en' => 'Product in MVP 1',
                                ],
                                'companyId' => $companiesIds[0],
                                'price' => 0.5,
                                'code' => '20230495447', // Même code que le MVP pour l'auto-matching (optionnel)
                                'supplierRef' => 'TEST-MVP-1',
                            ],
                            [
                                'name'  => [
                                    'fr' => 'Product in MVP 2',
                                    'en' => 'Product in MVP 2',
                                ],
                                'companyId' => $companiesIds[1],
                                'price' => 0.5,
                                'code' => '20230495447', // Même code que le MVP pour l'auto-matching (optionnel)
                                'supplierRef' => 'TEST-MVP-2',
                            ],
                        ],
                        'divisions' => ['FR', 'FR-ARA', 'FR-01', 'FR-03', 'FR-69'],
                    ],
                    [
                        'name'  => [
                            'fr' => 'Produit avec ChronoRelais',
                            'en' => 'Product with ChronoRelais',
                        ],
                        'companyId' => $companiesIds[2],
                        'price' => 0.5,
                        'code' => '20231495446',
                        'supplierRef' => 'TEST-CHRONO-RELAIS',
                        'divisions' => ['FR', 'FR-ARA', 'FR-01', 'FR-03', 'FR-69'],
                    ],
                    [
                        'name'  => [
                            'fr' => 'Produit attendant la modération',
                            'en' => 'Product waiting for moderation',
                        ],
                        'companyId' => $companiesIds[0],
                        'price' => 0.5,
                        'code' => '20230495448',
                        'supplierRef' => 'TEST-MODERATION-0',
                        'approved' => Premoderation::STATUS_PENDING
                    ],
                    [
                        'name'  => [
                            'fr' => 'Produit attendant la modération 2',
                            'en' => 'Product waiting for moderation 2',
                        ],
                        'companyId' => $companiesIds[0],
                        'price' => 0.5,
                        'code' => '20230495449',
                        'supplierRef' => 'TEST-MODERATION-1',
                        'approved' => Premoderation::STATUS_PENDING
                    ],
                    [
                        'name'  => [
                            'fr' => 'Produit refusé',
                            'en' => 'Disapproved product',
                        ],
                        'companyId' => $companiesIds[0],
                        'price' => 0.5,
                        'code' => '20230495450',
                        'supplierRef' => 'TEST-MODERATION-2',
                        'approved' => Premoderation::STATUS_DISAPPROVED
                    ],
                    [
                        'name'  => [
                            'fr' => 'Produit attendant la modération 3',
                            'en' => 'Product waiting for moderation 3',
                        ],
                        'companyId' => $companiesIds[1],
                        'price' => 0.5,
                        'code' => '20230495451',
                        'supplierRef' => 'TEST-MODERATION-3',
                        'approved' => Premoderation::STATUS_PENDING
                    ],
                ],
            ],
        ];

        $categoryLoader = $this->container->get('marketplace.test.category_loader');
        $productLoader = $this->container->get('marketplace.test.product_loader');
        $shippingLoader = $this->container->get('marketplace.test.shipping_loader');
        $optionLoader = $this->container->get('marketplace.test.option_loader');

        $productIds = [];

        ($installer = function (array $catalog) use (&$installer, &$productIds, $categoryLoader, $productLoader, $shippingLoader, $optionLoader) {
            /** @var CategoryLoader $categoryLoader */
            /** @var ProductLoader $productLoader */
            foreach ($catalog as $catalogData) {
                $parentId = $categoryLoader->create($catalogData['category']['fr']);
                GlobalState::switchContentTo(new Locale('en'));
                $categoryLoader->update($parentId, $catalogData['category']['en']);
                GlobalState::switchContentTo(new Locale('fr'));
                if (isset($catalogData['products'])) {
                    foreach ($catalogData['products'] as $product) {
                        if ($product['attached_products']) {
                            $newMvp = new CreateMultiVendorProduct($product['name']['fr']);
                            $newMvp->categoryId = $parentId;
                            $newMvp->code = $product['code'];
                            $newMvp->supplierReference = $product['supplierRef'];
                            if ($product['attributes']) {
                                $newMvp->attributes = $product['attributes'];
                            }
                            if ($product['freeAttributes']) {
                                $newMvp->freeAttributes = $product['freeAttributes'];
                            }
                            $mvpId = $this->multiVendorProductLoader->create($newMvp);
                            GlobalState::switchContentTo(new Locale('en'));
                            $newMvp->name = $product['name']['en'];
                            $this
                                ->multiVendorProductLoader
                                ->update($mvpId, $newMvp);
                            GlobalState::switchContentTo(new Locale('fr'));

                            foreach ($product['attached_products'] as $attachedProduct) {
                                $productId = $this->createProduct($attachedProduct, $parentId);
                                $productIds[] = $productId;

                                $newLink = new CreateLink();
                                $newLink->multiVendorProductId = $mvpId;
                                $newLink->productId = $productId;

                                try {
                                    $this->container->get('marketplace.test.multi_vendor_product_link_loader')->create($newLink);
                                } catch (\Exception $e) {
                                    // Dans le cas où l'auto-matching est activé on ne fait rien.
                                    // Ah si, on nettoie Doctrine qui n'est pas encore propre.
                                    // À bientôt 10 ans.
                                    $this->container->get('doctrine')->resetManager();
                                }
                            }
                        } else {
                            $productIds[] = $this->createProduct($product, $parentId);
                        }
                    }
                }
                $installer(array_map(function (array $catalogData) use ($parentId): array {
                    $catalogData['category']['fr']->parentCategoryId = $parentId;
                    $catalogData['category']['en']->parentCategoryId = $parentId;

                    return $catalogData;
                }, $catalogData['children'] ?? []));
            }
        })($catalog);

        return $productIds;
    }

    private function createProduct($product, int $categoryId): int
    {
        $newProduct = new CreateProduct($product['name']['fr']);
        if ($product['slug']) {
            $newProduct->slug = $product['slug'];
        }
        if ($product['companyId']) {
            $newProduct->companyId = $product['companyId'];
        }
        if ($product['attributes']) {
            $newProduct->attributes = $product['attributes'];
        }
        if ($product['freeAttributes']) {
            $newProduct->freeAttributes = $product['freeAttributes'];
        }
        if ($product['shortDescription']) {
            $newProduct->shortDescription = $product['shortDescription'];
        }
        if ($product['geoloc']) {
            $newProduct->geoloc = $product['geoloc'];
        }
        if ($product['price']) {
            $newProduct->price = $product['price'];
        }
        if ($product['main_pair']) {
            $newProduct->imageId = $product['main_pair'];
        }
        if ($product['code']) {
            $newProduct->productCode = $product['code'];
        }
        if ($product['supplierRef']) {
            $newProduct->supplierRef = $product['supplierRef'];
        }
        if (isset($product['isBrandNew']) && $product['isBrandNew'] === false) {
            $newProduct->condition = Product::CONDITION_USED;
        }
        if ($product['videoId']) {
            $this->container->get('marketplace.pim.video_repository')->save(new Video($product['videoId']));
            $newProduct->videoId = $product['videoId'];
        }
        if ($product['geolocation']) {
            $newProduct->geoloc = $product['geolocation'];
        }
        if ($product['options']) {
            $optionLoader = $this->container->get('marketplace.test.option_loader');
            $declinationsOptions = [[]];
            foreach ($product['options'] as $option) {
                $newOption = new CreateOption($option['name']);
                $newOption->categoryId = $categoryId;
                $variants = [];
                $tmp = [];
                foreach ($option['variants'] as $variant) {
                    foreach ($declinationsOptions as $declinationOptions) {
                        $declinationOptions[$newOption->name] = $variant;
                        $tmp[] = $declinationOptions;
                    }

                    $variants[] = $variant;
                }
                $declinationsOptions = $tmp;
                $newOption->variants = $variants;
                $optionLoader->create($newOption);
            }

            foreach ($declinationsOptions as $declinationOptions) {
                $newProduct->addMultiVariantsDeclination($declinationOptions, 10, 15.5);
            }
        }
        if ($product['attachments']) {
            foreach ($product['attachments'] as $attachmentData) {
                $newProduct->addAttachment($attachmentData['src'], $attachmentData['name'] ?? null);
            }
        }

        if ($product['seoTitle']) {
            $newProduct->seoTitle = $product['seoTitle'];
        }

        if ($product['seoDescription']) {
            $newProduct->seoDescription = $product['seoDescription'];
        }

        if ($product['divisions']) {
            $newProduct->divisions = $product['divisions'];
        }

        if (true === \array_key_exists('approved', $product)) {
            $newProduct->approved = $product['approved'];
        }

        if (\array_key_exists('maxPriceAdjustment', $product)) {
            $newProduct->maxPriceAdjustment = $product['maxPriceAdjustment'];
        }

        if (\array_key_exists('quoteRequestMinQuantity', $product)) {
            $newProduct->quoteRequestMinQuantity = $product['quoteRequestMinQuantity'];
        }

        $newProduct->categoryId = $categoryId;
        $productId = $this->container->get('marketplace.test.product_loader')->create($newProduct);
        $newProduct->name = $product['name']['en'];
        $newProduct->locale = 'en';

        // we have to recreate again the local file because it was locally deleted during creation
        if ($product['attachments']) {
            $newProduct->attachments = [];
            foreach ($product['attachments'] as $attachmentData) {
                $newProduct->addAttachment($attachmentData['src'], $attachmentData['name'] ?? null);
            }
        }

        $this
            ->container
            ->get('marketplace.test.product_loader')
            ->update($productId, $newProduct);

        if (!empty($product['shippings'])) {
            $this->container->get('marketplace.test.shipping_loader')->enableShippingOnProduct($productId, array_column($product['shippings'], 'id'));
        }

        return $productId;
    }

    /** @return Order[] */
    private function makeOrder(int $userId, array $products, ?OrderStatus $status = null, string $basketComment = '', array $coupons = [], int $paymentId = 1, int $shippingId = 1): array
    {
        $basketService = $this->container->get('marketplace.basket.domain_service');
        $checkout = $this->container->get('marketplace.basket.checkout');

        $basketId = $basketService->generateNewBasket();

        foreach ($products as $productToAdd) {
            $product = new Product((int) $productToAdd['id']);
            $declination = new Declination($product, $productToAdd['combination'] ?? '');
            $basketService->addProductToBasket($basketId, $declination, $productToAdd['count'] ?? 1);
            if (isset($productToAdd['comment'])) {
                $basketService->setProductComment($basketId, $declination, $productToAdd['comment']);
            }
        }

        $basketService->selectShippingForAllGroups($basketId, $shippingId);

        if ($shippingId === 5) { // Mondial Relay
            $shippingAddress = new Address(
                'mr',
                'mondial',
                'relay',
                'wiz',
                '25 rue de la gare',
                '',
                '69009',
                'Lyon',
                '',
                'FR',
                '',
                '',
                '',
                '00393'
            );
            $basketService->setShippingAddress($basketId, $shippingAddress);
        }

        $basketService->setComment($basketId, $basketComment);

        foreach ($coupons as $coupon) {
            $basketService->addCoupon($basketId, $coupon);
        }

        $result = $checkout->checkout($basketService->getById($basketId), $paymentId, $userId);

        if ($status !== null) {
            foreach ($result->getOrders() as $order) {
                $order = $this->orderMutator->setOrderStatus($order, $status);
            }
        }

        return $result->getOrders();
    }

    private function makeOrderAdjustment(int $orderId): void
    {
        $orderAdjustmentRepository = $this->container->get('marketplace.order.adjustment_repository');
        $orderService = $this->container->get('marketplace.order.order_service');
        $order = $orderService->getOrder($orderId);

        array_map(function (OrderItem $item) use ($order, $orderAdjustmentRepository) {
            $adjustment = new OrderAdjustment(
                $order->getId(),
                (int) $item->getItemId(),
                $order->getUser(),
                $item->getPrice()->getConvertedAmount() + 3,
                $item->getPrice()->getConvertedAmount(),
                $order->getTotal()->getConvertedAmount() + 3,
                $order->getTotal()->getConvertedAmount()
            );

            $orderAdjustmentRepository->save($adjustment);
        }, $order->getItems());
    }

    private function makeOrganisationOrder(Organisation $organisation, int $orderId): void
    {
        $organisationOrderService = $this->container->get('marketplace.organisation.order_service');
        $organisationOrderService->create($organisation, $orderId);
    }

    private function installRelatedProduct(
        int $fromProductId,
        int $toProductId,
        bool $withDescription = false,
        bool $withExtra = false,
        string $type = RelatedProductsType::RECOMMENDED
    ): void {
        $productService = $this->container->get('marketplace.pim.product.service');
        $fromProduct = $productService->get($fromProductId);
        $toProduct = $productService->get($toProductId);

        $newRelatedProduct = new CreateRelatedProduct($fromProduct, $toProduct, $type);
        if (true === $withDescription) {
            $newRelatedProduct->description = 'Description des produits associés de '
                . $newRelatedProduct->fromProduct
                . ' vers '
                . $newRelatedProduct->toProduct
            ;
        }

        if (true === $withExtra) {
            $newRelatedProduct->extra = 'Extra optionnel n°99';
        }

        $this->container->get('marketplace.test.related_product_loader')->create($newRelatedProduct);
    }

    private function installProductReview(int $userId, int $productId, float $rating, string $message): void
    {
        $productReviewService = $this->container->get('marketplace.review.product.service');
        $user = $this->container->get('marketplace.user.user_service')->get($userId);
        $product = $this->container->get('marketplace.product.productservice')->getProduct($productId);

        $productReviewService->addReview($product, $user, $user->getFullName(), $message, \intval($rating), Status::ENABLED);
    }

    private function installCompanyReview(int $userId, int $companyToReviewId, int $rating, string $message): void
    {
        $companyReviewService = $this->container->get('marketplace.review.company.service');
        $companyReviewService->reviewCompany($userId, $companyToReviewId, $rating, $message, Status::ENABLED);
    }

    private function installDiscussion($userId, $productId): int
    {
        $response = $this->doRequest($userId, 'POST', '/discussions', [], ['productId' => $productId]);

        $content = json_decode($response->getBody()->getContents(), true);

        return $content['id'];
    }

    private function installCmsPages(): void
    {
        $pageLoader = $this->container->get('marketplace.test.page_loader');

        $pageLoader->create('Test Cms Page Slug', '');

        $faqHtml = <<<HTML
<blockquote>
<div class="container">
<h5>QU&rsquo;EST-CE QU&rsquo;UNE MARKET PLACE / PLACE DE MARCHE ?</h5>
<span style="font-size: 10pt;">Une Marketplace est un site permettant une mise en relation entre des vendeurs et des acheteurs afin d&rsquo;effectuer une transaction commerciale. Une Marketplace agit comme un tiers de confiance et offre aux utilisateurs (acheteurs et vendeurs) une garantie de paiement via un site s&eacute;curis&eacute;, et l&rsquo;assurance d&rsquo;une transaction r&eacute;alis&eacute;e dans les meilleures conditions.</span>
<h5>QUELS SONT LES AVANTAGES D&rsquo;UNE MARKET POUR LE CLIENT ?</h5>
<span style="font-size: 10pt;">Les avantages sont multiples : &nbsp;Le client b&eacute;n&eacute;ficie d&rsquo;un catalogue de produits plus large et plus vari&eacute; au meilleur prix.</span> <br /><span style="font-size: 10pt;">Le client peut vendre et acheter un article neuf ou d&rsquo;occasion dans diff&eacute;rentes cat&eacute;gories de disciplines sportives, tout en ayant un seul panier, et donc, un seul paiement.</span> <br /><span style="font-size: 10pt;">Le client b&eacute;n&eacute;ficie d&rsquo;un paiement s&eacute;curis&eacute;.</span> <br /><span style="font-size: 10pt;">Le client utilisateur fait partie d&rsquo;un fichier de clients r&eacute;f&eacute;renc&eacute;s pouvant participer &agrave; des jeux concours, et ainsi gagner des objets d&eacute;dicac&eacute;s par de grands champions.</span>
<h5>QUEL EST LE ROLE DE &nbsp;Starterkit™ ?</h5>
<span style="font-size: 10pt;">Starterkit™ est une Marketplace (place de march&eacute;) d&eacute;di&eacute;e aux amateurs de sport, professionnels ou particuliers, pour VENDRE et ACHETER des articles de sport neufs et d&rsquo;occasion.</span> <br /><span style="font-size: 10pt;">Que vous soyez vendeur, acheteur, professionnel ou particulier, nous sommes l&agrave; pour vous aider &agrave; mettre en vente votre article ou votre mat&eacute;riel sportif dans les meilleures dispositions et au prix le plus juste.&nbsp;</span> <br /><span style="font-size: 10pt;">Avec Starterkit™, vous b&eacute;n&eacute;ficiez d&rsquo;un accompagnement d&egrave;s la mise en vente de votre article, et d&rsquo;une visibilit&eacute; accrue sur un site 100% d&eacute;di&eacute; au sport.</span>
<h5>QUELS SONT LES AVANTAGES DE Starterkit™ ?</h5>
<span style="font-size: 10pt;">Avec Starterkit™ vous avez l'assurance d'acheter des produits soigneusement s&eacute;lectionn&eacute;s parmi les meilleurs revendeurs, sp&eacute;cialis&eacute;s dans chaque sport, et de b&eacute;n&eacute;ficier de conseils d&rsquo;experts.</span> <br /><span style="font-size: 10pt;">Vous pouvez vendre et acheter du mat&eacute;riel sportif neuf ou d&rsquo;occasion sur une m&ecirc;me plateforme et n&rsquo;avoir qu&rsquo;un seul panier pour tous ces achats. Vous b&eacute;n&eacute;ficiez des meilleures offres promotionnelles du moment.</span> <br /><span style="font-size: 10pt;">La communication autour de Starterkit™ permet au vendeur d&rsquo;optimiser son catalogue et &agrave; l&rsquo;acheteur de trouver un large choix de produits.</span> <br /><span style="font-size: 10pt;">L'onglet "Que recherchez-vous" vous permet de trouver pr&eacute;cis&eacute;ment et rapidement l&rsquo;article que vous d&eacute;sirez et dont vous souhaitez faire l'acquisition.</span>
<h5>COMMENT SUIVRE L&rsquo;ACTUALIT&Eacute; DE Starterkit™ ?</h5>
<span style="font-size: 10pt;">Nous vous invitons &agrave; nous suivre sur nos pages officielles Facebook et Twitter, ainsi qu&rsquo;&agrave; vous inscrire &agrave; notre Newsletter.</span>
<h5>DISPONIBILIT&Eacute;</h5>
<span style="font-size: 10pt;">Une fois votre commande effectu&eacute;e, nous vous confirmerons sa disponibilit&eacute; sous 48 heures (jours ouvrables), suite &agrave; ce d&eacute;lai, la livraison sera organis&eacute;e par le revendeur.</span>
<h5>REMBOURSEMENT</h5>
<span style="font-size: 10pt;">Suite &agrave; l&rsquo;annulation ou au retour de votre commande, le remboursement se fera par le moyen de paiement utilis&eacute; lors de votre transaction.</span>
<h5>PARTICULIERS, COMMENT METTRE EN VENTE VOS ARTICLES DE SPORT ?</h5>
<span style="font-size: 10pt;">Vous souhaitez mettre en vente un article de sport et vous &ecirc;tes un particulier.</span> <br /><span style="font-size: 10pt;">Rien de plus simple, nous vous invitons &agrave; aller dans la section VENDRE du site et &agrave; compl&eacute;ter le formulaire de vente.</span> <br /><span style="font-size: 10pt;">Celui-ci sera soumis &agrave; notre mod&eacute;ration, puis Starterkit™ vous informera de la publication de votre annonce.</span>
<ul>
<li><span style="font-size: 10pt;">Une fois l&rsquo;annonce valid&eacute;e, le vendeur a 48h pour valider la disponibilit&eacute; du produit.</span></li>
<li><span style="font-size: 10pt;">L&rsquo;acheteur re&ccedil;oit une phrase &laquo; code &raquo; (succession de mots) dont le vendeur aura partiellement connaissance.</span></li>
<li><span style="font-size: 10pt;">La livraison en main propre, 2 possibilit&eacute;s :</span></li>
<li><span style="font-size: 10pt;">L&rsquo;argent est d&eacute;bloqu&eacute; et sera vers&eacute; sur le compte du vendeur sous 15 jours.</span></li>
</ul>
<span style="font-size: 10pt;">Nous attirons votre attention sur le fait que cette solution ne permet pas de retour du produit.</span> <br /><span style="font-size: 10pt;">Starterkit™ se r&eacute;mun&egrave;re par le biais d'une commission per&ccedil;ue sur le prix de vente TTC, hors frais de transport.&nbsp; Cette commission correspond &agrave; 15% + 0,50 &euro; (frais de gestion),pour les prix de vente TTC.</span>
<h5>RESPONSABILITE DU VENDEUR</h5>
<span style="font-size: 10pt;">Au titre de place de march&eacute;, Starterkit™ est interm&eacute;diaire entre le vendeur et l&rsquo;acheteur.</span> <br /><span style="font-size: 10pt;">Le vendeur est, de fait, responsable des informations communiqu&eacute;es sur l&rsquo;article, qui se doivent &ecirc;tre conformes &agrave; la r&eacute;alit&eacute;. Pour &eacute;viter les m&eacute;contentements, il est important que le vendeur communique le maximum d&rsquo;information sur le produit, notamment ses particularit&eacute;s, entre autre ses d&eacute;fauts.</span>
<h5>COMMENT ACCEDER AUX CGV, CGU ET MENTIONS LEGALES ?</h5>
<span style="font-size: 10pt;">Nous accordons une attention particuli&egrave;re aux Conditions G&eacute;n&eacute;rales Vendeurs, aux Conditions G&eacute;n&eacute;rales d&rsquo;Utilisation et &agrave; la protection des donn&eacute;es personnelles. </span> <br /><span style="font-size: 10pt;">Pour en prendre connaissance, merci de cliquer sur le lien suivant : <a href="https://Starterkit™.sandbox.wizaplace.com/conditions-generales-vendeurs.html">CGVendeurs</a>&nbsp;, <a href="https://Starterkit™.sandbox.wizaplace.com/conditions-generales-d-utilisation.html">CGU</a></span>
<h5>MOT DE PASSE OUBLIE ?</h5>
<span style="font-size: 10pt;">Pour r&eacute;initialiser votre mot de passe, cliquez sur &laquo; mot de passe oubli&eacute; &raquo; dans MON COMPTE et indiquez votre email. Vous recevrez en retour, un lien pour r&eacute;initialiser votre nouveau mot de passe</span>.
HTML;
        $pageLoader->create(
            'FAQ',
            $faqHtml,
            'FAQ',
            'Frequently Asked Questions / Foire Aux Questions',
            'faq,support'
        );
    }

    private function installAttributes(): void
    {
        $attributeLoader = $this->container->get('marketplace.test.attribute_loader');

        $createAttribute = new CreateAttribute('color');
        $createAttribute->type = 'M';
        $createAttribute->variants = ['red', 'blue', 'test attribute variant slug'];
        $id = $attributeLoader->create($createAttribute);

        GlobalState::switchContentTo(new Locale('en'));
        $attributeLoader->update($id, new CreateAttribute('color'));
        GlobalState::switchContentTo(new Locale('fr'));
    }

    private function installBanners(): void
    {
        $banners = [
            [
                'url'                   => 'http://url-de-test.com',
                'shouldOpenInNewWindow' => true,
                'displayOnHomepage'     => true,
                'displayOnCategories'   => false,
                'categoriesIds'         => [],
                'device'                => 'mobile',
            ],
            [
                'url'                   => 'http://url-de-test-2.com',
                'shouldOpenInNewWindow' => false,
                'displayOnHomepage'     => true,
                'displayOnCategories'   => true,
                'categoriesIds'         => [3],
                'device'                => 'desktop',
            ],
            [
                'url'                   => 'http://url-de-test-3.com',
                'shouldOpenInNewWindow' => false,
                'displayOnHomepage'     => false,
                'displayOnCategories'   => true,
                'categoriesIds'         => [3],
                'device'                => 'mobile',
            ],
        ];

        $bannerLoader = $this->container->get('marketplace.test.banner_loader');

        $bannerCount = 0;
        foreach ($banners as $banner) {
            $createBanner = new CreateBanner('Banner' . $bannerCount, $banner['url']);
            $createBanner->targetBlank = $banner['shouldOpenInNewWindow'];
            $createBanner->displayOnHomepage = $banner['displayOnHomepage'];
            $createBanner->displayOnCategories = $banner['displayOnCategories'];
            $createBanner->categoriesIds = $banner['categoriesIds'];
            $createBanner->device = $banner['device'];

            $bannerLoader->create($createBanner);
            $bannerCount++;
        }
    }

    private function installAdminCompany(): void
    {
        $settings = [
            'company_name' => 'Example MarketPlace Incorporation',
            'company_users_department' => '<EMAIL>',
            'company_site_administrator' => '<EMAIL>',
            'company_orders_department' => '<EMAIL>',
            'company_support_department' => '<EMAIL>',
        ];
        foreach ($settings as $name => $value) {
            $this->container->get('doctrine.dbal.default_connection')
                ->executeUpdate('UPDATE cscart_settings_objects SET value = ? WHERE name = ?', [$value, $name]);
        }
    }

    private function doRequest(int $userId, string $method, $url, $parameters = [], $content = [], $contentType = 'application/json'): ResponseInterface
    {
        $userRepository = $this->container->get('marketplace.user.user_repository');

        $server['Authorization'] = 'token ' . $userRepository->get($userId)->getApiKey();
        $server['Content-Type'] = $contentType;

        $response = (new \GuzzleHttp\Client())->request($method, 'http://' . $this->container->getParameter('http_host') . '/api/v1' . $url, [
            'headers' => $server,
            'json' => $content,
        ]);

        $this->container->get('doctrine.orm.entity_manager')->clear();

        return $response;
    }

    private function installMarketplacePromotion(int $userId, int $productId)
    {
        $this->promotionLoader->createMarketplaceFixedPromotion(Money::fromVariable(10), 'FIRST');

        $order = $this->makeOrder($userId, [['id' => $productId]], OrderStatus::COMPLETED(), '', ['FIRST'], 1, 42)[0];

        $createTransaction = new CreateTransaction($order->getId());
        $createTransaction->transactionReference = "a123456789";
        $createTransaction->amount = $order->getMarketplaceDiscountTotal();
        $createTransaction->status = TransactionStatus::SUCCESS();
        $createTransaction->type = TransactionType::TRANSFER();
        $this->transactionLoader->createTransaction($createTransaction);
    }
}
