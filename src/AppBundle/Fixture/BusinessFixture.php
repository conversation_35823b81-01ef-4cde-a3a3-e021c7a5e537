<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Fixture;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Wizacha\AppBundle\Fixture\Module\AuthLogFixture;
use Wizacha\AppBundle\Fixture\Module\CmsFixture;
use Wizacha\AppBundle\Fixture\Module\CompanyFixture;
use Wizacha\AppBundle\Fixture\Module\OrderFixture;
use Wizacha\AppBundle\Fixture\Module\PimFixture;

/**
 * Cette fixture contient un jeu de données large et varié
 * permettant de mettre en place une marketplace bien remplie.
 * Elle se base sur les "fixtures techniques" pour ensuite ajouter
 * des données plus métier (business).
 */
class BusinessFixture implements Fixture
{
    /**
     * @var ContainerInterface
     */
    private $container;

    /**
     * We inject the container because we'll be using a lot of other fixture classes.
     */
    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;
    }

    public function install(InputInterface $input, OutputInterface $output)
    {
        $output->writeln('<comment>Installing business fixtures...</comment>');

        $this->get(TechnicalFixture::class)->install($input, $output);
        $this->get(CmsFixture::class)->install($input, $output);
        $this->get(CompanyFixture::class)->install($input, $output);
        $this->get(PimFixture::class)->install($input, $output);
        $this->get(OrderFixture::class)->install($input, $output);
        $this->get(AuthLogFixture::class)->install($input, $output);
    }

    private function get(string $fixtureClass)
    {
        return $this->container->get($fixtureClass);
    }
}
