<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Fixture\Module;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\AppBundle\Fixture\Fixture;
use Wizacha\Marketplace\Basket\BasketService;
use Wizacha\Marketplace\Basket\Checkout;
use Wizacha\Marketplace\Entities\Declination;
use Wizacha\Product;

class OrderFixture implements Fixture
{
    /**
     * @var BasketService
     */
    private $basketService;

    /**
     * @var Checkout
     */
    private $checkout;

    public function __construct(BasketService $basketService, Checkout $checkout)
    {
        $this->basketService = $basketService;
        $this->checkout = $checkout;
    }

    public function install(InputInterface $input, OutputInterface $output)
    {
        $output->writeln('<comment>Installing order fixtures...</comment>');

        $userId = 2; // TODO improve
        $paymentId = 1; // TODO improve
        $productId = 2; // TODO improve

        $basketId = $this->basketService->generateNewBasket();

        $declination = new Declination(new Product($productId), '');
        $this->basketService->addProductToBasket($basketId, $declination, 2);

        $basket = $this->basketService->getById($basketId);
        $this->checkout->checkout($basket, $paymentId, $userId);
    }
}
