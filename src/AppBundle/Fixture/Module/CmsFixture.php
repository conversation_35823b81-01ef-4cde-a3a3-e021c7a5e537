<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Fixture\Module;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\AppBundle\Fixture\Fixture;

class CmsFixture implements Fixture
{
    /**
     * @var Connection
     */
    private $db;

    public function __construct(Connection $db)
    {
        $this->db = $db;
    }

    public function install(InputInterface $input, OutputInterface $output)
    {
        $output->writeln('<comment>Installing CMS fixtures...</comment>');

        // Pages
        $this->db->exec('TRUNCATE cscart_pages');
        $this->db->exec('TRUNCATE cscart_page_descriptions');

        $contactPage = $this->createPage('Contact', '');
        $this->createPage('Contact', '', $contactPage, 'en');
        $legalPage = $this->createPage('Mentions légales', '');
        $this->createPage('Legal mentions', '', $legalPage, 'en');
        $cgvPage = $this->createPage('Conditions Générales de Vente', '');
        $this->createPage('Terms of sales', '', $cgvPage, 'en');
        $returnsPage = $this->createPage('Retours produits', '');
        $this->createPage('RMA', '', $returnsPage, 'en');
        $savPage = $this->createPage('Créer un SAV', '');
        $this->createPage('Create RMA', '', $savPage, 'en');
        $faqPage = $this->createPage('FAQ / Aide', '');
        $this->createPage('FAQ / Help', '', $faqPage, 'en');
        $aboutUsPage = $this->createPage('Qui sommes-nous ?', '');
        $this->createPage('About us', '', $aboutUsPage, 'en');
        $vendorPage = $this->createPage('Proposer un partenariat', '');
        $this->createPage('Propose a partnership', '', $vendorPage, 'en');

        // Menus
        $this->db->exec('TRUNCATE cscart_menus');
        $this->db->exec('TRUNCATE cscart_menus_descriptions');
        $this->db->exec('TRUNCATE cscart_static_data');
        $this->db->exec('TRUNCATE cscart_static_data_descriptions');

        $this->createMenu(
            'Informations',
            [
                $contactPage,
                $faqPage,
                $legalPage,
            ],
            [
                [
                    'parentId' => $contactPage,
                    'id' => $aboutUsPage,
                ],
            ]
        );
        $this->createMenu('Espace client', [
            $returnsPage,
            $savPage,
        ]);
        $this->createMenu('Espace pro', [
            $vendorPage,
            $cgvPage,
        ]);
    }

    private function createPage(string $title, string $htmlContent, int $id = 0, string $locale = 'fr'): int
    {
        $pageId = (int) fn_update_page([
            'parent_id' => 0,
            'page' => $title,
            'description' => $htmlContent,
            'seo_name' => '', // required to generate seo names
        ], $id, $locale);

        if ($pageId == false) {
            throw new \Exception('Error while creating the page');
        }

        return $pageId;
    }

    private function createMenu(string $title, array $pages, array $children = [])
    {
        $menuId = \Tygh\Menu::update([
            'lang_code' => 'fr',
            'name' => $title,
            'status' => 'A',
            'company_id' => null,
        ]);

        if ($menuId == false) {
            throw new \Exception('Error while creating the menu');
        }

        foreach ($pages as $pageId) {
            $this->createMenuItem($menuId, $pageId);
        }
        foreach ($children as $child) {
            $this->createMenuItem($menuId, $child['id'], $child['parentId']);
        }
    }

    private function createMenuItem(int $menuId, $pageId, $parentId = null): void
    {
        $title = fn_get_page_name($pageId, 'fr');

        $data = [
            'lang_code' => 'fr',
            'descr' => $title,
            'param' => 'pages.view&page_id=' . $pageId,
            'param_5' => $menuId,
        ];

        if ($parentId) {
            $data['parent_id'] = $parentId;
        }

        $id = fn_update_static_data($data, 0, 'A');

        if ($id == false) {
            throw new \Exception('Error while creating the menu item');
        }
    }
}
