<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Fixture\Module;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\AppBundle\Command\DeployMigrateDivisionCommand;
use Wizacha\AppBundle\Command\Division\ImportDivisionProductsCommand;
use Wizacha\AppBundle\Fixture\Fixture;

class DivisionFixture implements Fixture
{
    /** @var DeployMigrateDivisionCommand */
    protected $migrateDivisionsCommand;

    /** @var ImportDivisionProductsCommand */
    protected $importProductDivisionsCommand;

    public function __construct(
        DeployMigrateDivisionCommand $deployMigrateDivisionCommand,
        ImportDivisionProductsCommand $importDivisionProductsCommand
    ) {
        $this->migrateDivisionsCommand = $deployMigrateDivisionCommand;
        $this->importProductDivisionsCommand = $importDivisionProductsCommand;
    }

    public function install(InputInterface $input, OutputInterface $output)
    {
        $output->writeln('<comment>Installing division fixtures...</comment>');

        // deploy:command:migrate-divisions
        $this->migrateDivisionsCommand->run($input, $output);
        // products:divisions:init
        $this->importProductDivisionsCommand->run($input, $output);
    }
}
