<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Fixture\Module;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\AppBundle\Fixture\Fixture;
use Wizacha\Test\Fixture\Company\CompanyLoader;
use Wizacha\Test\Fixture\Company\CreateCompany;
use Wizacha\Test\Fixture\Shipping\LinkShippingWithCompany;
use Wizacha\Test\Fixture\Shipping\ShippingLoader;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Component\Locale\Locale;

class CompanyFixture implements Fixture
{
    /**
     * @var CompanyLoader
     */
    private $companyLoader;

    /**
     * @var ShippingLoader
     */
    private $shippingLoader;

    public function __construct(CompanyLoader $companyLoader, ShippingLoader $shippingLoader)
    {
        $this->companyLoader = $companyLoader;
        $this->shippingLoader = $shippingLoader;
    }

    public function install(InputInterface $input, OutputInterface $output)
    {
        $output->writeln('<comment>Installing company fixtures...</comment>');

        $faker = \Faker\Factory::create();

        $createCompany = new CreateCompany('ACME');
        $createCompany->companyDescription = $faker->sentences(3, true);
        $createCompany->commissionPercentage = 5;
        $createCompany->commissionFixed = 0;
        $createCompany->iban = '***********************';
        $createCompany->bic = 'DABAIE2D';
        $createCompany->vatNumber = 'FR83404833048';
        $createCompany->siretNumber = '40483304800022';
        $companyId = $this->companyLoader->create($createCompany);

        GlobalState::switchContentTo(new Locale('en'));
        $this->companyLoader->update($companyId, $createCompany);
        GlobalState::switchContentTo(new Locale('fr'));

        // TODO "TNT Express" est hardcodé, à améliorer
        $command = new LinkShippingWithCompany('TNT Express', $companyId);
        $command->firstRate = 12; // 12 euros
        $this->shippingLoader->linkWithCompany($command);

        $command = new LinkShippingWithCompany('Colissimo', $companyId);
        $command->firstRate = 15; // 15 euros
        $this->shippingLoader->linkWithCompany($command);
    }
}
