<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Fixture\Module;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\AppBundle\Fixture\Fixture;
use Wizacha\Component\AuthLog\AuthLog;
use Wizacha\Component\AuthLog\AuthLogRepository;
use Wizacha\Component\AuthLog\DestinationType;
use Wizacha\Component\AuthLog\SourceType;
use Wizacha\Component\AuthLog\StatusType;

class AuthLogFixture implements Fixture
{
    /**
     * @var AuthLogRepository
     */
    private $authLogRepository;
    /**
     * @var array
     */
    private $keys;
    /**
     * @var int
     */
    private $max;

    public function __construct(AuthLogRepository $authLogRepository)
    {
        $this->authLogRepository = $authLogRepository;

        $this->keys = [
            'logins' => [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>',
            ],
            'statusKeys' => StatusType::keys(),
            'sourceKeys' => SourceType::keys(),
            'destinationKeys' => DestinationType::keys(),
        ];

        $product = function (int $carry, array $item): int {
            return $carry *= \count($item);
        };
        $this->max = array_reduce($this->keys, $product, 1);
    }

    public function install(InputInterface $input, OutputInterface $output, int $quantity = 10): void
    {
        $output->writeln('<comment>Installing authentication log fixtures...</comment>');

        $quantity = $quantity ?: $this->max;

        // let's produce all possible states
        for ($i = 0; $i < $quantity; $i++) {
            $this->authLogRepository->save(...$this->intToAuthLog($i % $this->max));
        }
    }

    // every possibles AuthLog states
    public function intToAuthLog(int $i): array
    {
        if ($i > $this->max) {
            throw new \Exception("$i > {$this->max} (max. value)");
        }

        $data = [];

        foreach ($this->keys as $key => $values) {
            $base = \count($values);
            $mod = $i % $base;
            $i = intdiv($i, $base);
            $data[$key] = $this->keys[$key][$mod];
        }

        return [
            $data['logins'],
            StatusType::{$data['statusKeys']}(),
            SourceType::{$data['sourceKeys']}(),
            DestinationType::{$data['destinationKeys']}(),
        ];
    }
}
