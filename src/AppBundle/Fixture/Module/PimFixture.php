<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Fixture\Module;

use Faker\Generator;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\AppBundle\Fixture\Fixture;
use Wizacha\Marketplace\Catalog\Company\Company;
use Wizacha\Marketplace\Catalog\Company\CompanyService;
use Wizacha\Marketplace\PIM\Attribute\AttributeType;
use Wizacha\Test\Fixture\Category\CategoryLoader;
use Wizacha\Test\Fixture\Category\CreateCategory;
use Wizacha\Test\Fixture\Image\ImageLoader;
use Wizacha\Test\Fixture\Product\Attribute\AttributeLoader;
use Wizacha\Test\Fixture\Product\Attribute\CreateAttribute;
use Wizacha\Test\Fixture\Product\CreateProduct;
use Wizacha\Test\Fixture\Product\ProductLoader;

class PimFixture implements Fixture
{
    /**
     * @var Generator
     */
    private $faker;

    /**
     * @var CategoryLoader
     */
    private $categoryLoader;

    /**
     * @var ProductLoader
     */
    private $productLoader;

    /**
     * @var CompanyService
     */
    private $companyService;

    /**
     * @var AttributeLoader
     */
    private $attributeLoader;

    /**
     * @var ImageLoader
     */
    private $imageLoader;

    public function __construct(
        AttributeLoader $attributeLoader,
        CategoryLoader $categoryLoader,
        ProductLoader $productLoader,
        CompanyService $companyService,
        Generator $faker,
        ImageLoader $imageLoader
    ) {
        $this->attributeLoader = $attributeLoader;
        $this->categoryLoader = $categoryLoader;
        $this->productLoader = $productLoader;
        $this->companyService = $companyService;
        $this->faker = $faker;
        $this->imageLoader = $imageLoader;
    }

    public function install(InputInterface $input, OutputInterface $output)
    {
        $output->writeln('<comment>Installing PIM fixtures...</comment>');

        $company = reset($this->companyService->getCompanies());

        $createAttribute = new CreateAttribute('Couleur');
        $createAttribute->type = AttributeType::CHECKBOX_MULTIPLE;
        $createAttribute->variants = [
            'Noir',
            'Blanc',
            'Rouge',
            'Bleu',
        ];
        $this->attributeLoader->create($createAttribute);

        $createAttribute = new CreateAttribute('Dimensions');
        $createAttribute->type = AttributeType::GROUP;
        $dimensionAttribute = $this->attributeLoader->create($createAttribute);
        $createAttribute = new CreateAttribute('Largeur');
        $createAttribute->groupId = $dimensionAttribute;
        $createAttribute->type = AttributeType::FREE_NUMBER;
        $createAttribute = new CreateAttribute('Hauteur');
        $createAttribute->groupId = $dimensionAttribute;
        $createAttribute->type = AttributeType::FREE_NUMBER;
        $this->attributeLoader->create($createAttribute);

        // Informatique
        $createCategory = new CreateCategory('Informatique');
        $createCategory->description = $this->faker->sentences(3, true);
        $computers = $this->categoryLoader->create($createCategory);
        // Ordinateurs portables
        $createCategory = new CreateCategory('Ordinateurs portables');
        $createCategory->description = $this->faker->sentences(3, true);
        $createCategory->parentCategoryId = $computers;
        $laptops = $this->categoryLoader->create($createCategory);
        // Écrans
        $createCategory = new CreateCategory('Écrans');
        $createCategory->description = $this->faker->sentences(3, true);
        $createCategory->parentCategoryId = $computers;
        $displays = $this->categoryLoader->create($createCategory);
        for ($i = 0; $i < 20; $i++) {
            $this->createRandomProduct($company, [$computers, $laptops, $displays]);
        }

        // Téléphone
        $createCategory = new CreateCategory('Téléphone');
        $createCategory->description = $this->faker->sentences(3, true);
        $telephone = $this->categoryLoader->create($createCategory);

        $this->createIPhone($telephone, $company);
        $this->createGalaxyNote($telephone, $company);
    }

    private function createIPhone(int $categoryId, Company $company)
    {
        $createProduct = new CreateProduct('iPhone 7');
        $createProduct->crossedOutPrice = 799;
        $createProduct->price = 699;
        $createProduct->amount = 50;
        $createProduct->weight = .18;
        $createProduct->categoryId = $categoryId;
        $createProduct->companyId = $company->getId();
        $createProduct->creationDate = $this->faker->dateTimeThisYear;
        $productImageId = $this->imageLoader->createFromFile(__DIR__ . '/../Image/iphone.jpg');
        $createProduct->imageId = $productImageId;
        $createProduct->divisions = ['FR', 'FR-ARA', 'FR-01', 'FR-03', 'FR-69'];
        $this->productLoader->create($createProduct);
    }

    private function createGalaxyNote(int $categoryId, Company $company)
    {
        $createProduct = new CreateProduct('Galaxy Note 5');
        $createProduct->crossedOutPrice = 699;
        $createProduct->price = 629;
        $createProduct->amount = 50;
        $createProduct->weight = .13;
        $createProduct->categoryId = $categoryId;
        $createProduct->companyId = $company->getId();
        $createProduct->creationDate = $this->faker->dateTimeThisYear;
        $productImageId = $this->imageLoader->createFromFile(__DIR__ . '/../Image/samsung-galaxy.png');
        $createProduct->imageId = $productImageId;
        $createProduct->divisions = ['FR', 'FR-ARA', 'FR-01', 'FR-03', 'FR-69'];
        $this->productLoader->create($createProduct);
    }

    private function createRandomProduct(Company $company, array $categoryIds)
    {
        $createProduct = new CreateProduct($this->faker->words(4, true));
        $createProduct->approved = $this->faker->boolean(90);
        $createProduct->price = $this->faker->numberBetween(10, 300);
        $createProduct->crossedOutPrice = $this->faker->numberBetween($createProduct->price + 10, 500);
        $createProduct->amount = $this->faker->numberBetween(2, 50);
        $createProduct->weight = $this->faker->randomFloat(2, 3, 10);
        $createProduct->categoryId = $this->faker->randomElement($categoryIds);
        $createProduct->companyId = $company->getId();
        $createProduct->creationDate = $this->faker->dateTimeThisYear;
        $createProduct->divisions = ['FR', 'FR-ARA', 'FR-01', 'FR-03', 'FR-69'];
        $this->productLoader->create($createProduct);
    }
}
