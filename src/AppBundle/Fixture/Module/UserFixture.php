<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Fixture\Module;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\AppBundle\Fixture\Fixture;
use Wizacha\Test\Fixture\User\CreateAdmin;
use Wizacha\Test\Fixture\User\CreateUser;
use Wizacha\Test\Fixture\User\CreateVendor;
use Wizacha\Test\Fixture\User\UserLoader;

class UserFixture implements Fixture
{
    /** @var UserLoader */
    private $userLoader;

    public function __construct(UserLoader $userLoader)
    {
        $this->userLoader = $userLoader;
    }

    public function install(InputInterface $input, OutputInterface $output)
    {
        $output->writeln('<comment>Installing user fixtures...</comment>');

        $createAdmin = new CreateAdmin('<EMAIL>', 'Administrateur', 'Wizaplace');
        $createAdmin->password = static::VALID_PASSWORD;
        $createAdmin->fillBillingAddress = true;
        $createAdmin->fillShippingAddress = true;
        $this->userLoader->createAdmin($createAdmin);

        $createUser = new CreateUser('<EMAIL>', 'Paul', 'Martin');
        $createUser->password = static::VALID_PASSWORD;
        $createUser->fillBillingAddress = true;
        $createAdmin->fillShippingAddress = true;
        $this->userLoader->createCustomer($createUser);

        $createVendor = new CreateVendor('ACME');
        $createVendor->password = static::VALID_PASSWORD;
        $createVendor->fillBillingAddress = true;
        $createAdmin->fillShippingAddress = true;
        $this->userLoader->createVendor($createVendor);
    }
}
