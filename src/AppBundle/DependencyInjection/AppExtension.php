<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\DependencyInjection;

use Symfony\Component\Config\FileLocator;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Loader\YamlFileLoader;
use Symfony\Component\HttpKernel\DependencyInjection\Extension;

class AppExtension extends Extension
{
    public function load(array $config, ContainerBuilder $container)
    {
        $globalLoader = new YamlFileLoader(
            $container,
            new FileLocator(__DIR__ . '/../../../app/config')
        );
        $loader = new YamlFileLoader(
            $container,
            new FileLocator(__DIR__ . '/../Resources/config')
        );

        $loader->load('config.yml');
        $loader->load('services.yml');
        $globalLoader->load("services.aliases.yml");

        if ($container->hasParameter('hipay.css') === false) {
            $container->setParameter('hipay.css', null);
        }

        $cacheBackend = \defined('CACHE_BACKEND') ? CACHE_BACKEND : 'redis';

        $loader->load("services.cache_$cacheBackend.yml");
    }
}
