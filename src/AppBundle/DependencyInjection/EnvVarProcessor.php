<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\DependencyInjection;

use Symfony\Component\DependencyInjection\EnvVarProcessorInterface;

class EnvVarProcessor implements EnvVarProcessorInterface
{
    public function getEnv($prefix, $name, \Closure $getEnv)
    {
        if ($prefix === 'ucfirst') {
            return ucfirst($getEnv($name));
        }
    }

    public static function getProvidedTypes()
    {
        return [
            'ucfirst' => 'string',
        ];
    }
}
