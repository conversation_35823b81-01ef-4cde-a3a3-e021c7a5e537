<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\DependencyInjection\Compiler;

use Symfony\Component\DependencyInjection\Compiler\CompilerPassInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Reference;

/**
 * Cette passe de compilation est une duplication de la passe de compilation
 * du composant translation de Symfony. On va chercher tous les services qui
 * ont le tag `translation.loader` pour les ajouter à notre `translation.importer`
 * à l'exception du loader cscart, qui est celui qui charge les traductions
 * depuis la DB, et donc on n'en a pas besoin pour importer en DB
 */
class TranslatorPass implements CompilerPassInterface
{
    public function process(ContainerBuilder $container)
    {
        if (!$container->hasDefinition('translator.default') && !$container->hasDefinition('translation.importer')) {
            return;
        }

        $loaders = array();
        $loaderRefs = array();
        foreach ($container->findTaggedServiceIds('translation.loader', true) as $id => $attributes) {
            $loaderRefs[$id] = new Reference($id);
            $loaders[$id][] = $attributes[0]['alias'];
            if (isset($attributes[0]['legacy-alias'])) {
                $loaders[$id][] = $attributes[0]['legacy-alias'];
            }
        }

        $definition = $container->getDefinition('translation.importer');
        foreach ($loaders as $id => $formats) {
            foreach ($formats as $format) {
                // On n'importe pas les traduction qui sont déjà en base
                if ($format != 'cscart') {
                    $definition->addMethodCall('addLoader', array($format, $loaderRefs[$id]));
                }
            }
        }
    }
}
