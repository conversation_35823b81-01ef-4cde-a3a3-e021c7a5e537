<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\DependencyInjection\Compiler;

use Symfony\Component\DependencyInjection\Compiler\CompilerPassInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Wizacha\Marketplace\Messenger\Transport\AmqpTransportFactory;
use Wizacha\Marketplace\Messenger\Transport\Connection;

class AmqpsPass implements CompilerPassInterface
{
    public function process(ContainerBuilder $container)
    {
        if ($container->has('messenger.transport.amqp.factory')) {
            $container->getDefinition('messenger.transport.amqp.factory')->setClass(AmqpTransportFactory::class);
        }

        if ($container->has('Symfony\Component\Messenger\Transport\AmqpExt\Connection')) {
            $container->getDefinition('Symfony\Component\Messenger\Transport\AmqpExt\Connection')->setClass(Connection::class);
        }
    }
}
