<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Marketplace\Promotion\PromotionService;
use Wizacha\Marketplace\Promotion\PromotionType;

/**
 * Must disable marketplace discount that are out of date
 */
class CheckValidityMarketplaceDiscountCommand extends Command implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    /** @var PromotionService  */
    private $promotionService;

    public function __construct(PromotionService $promotionService)
    {
        parent::__construct();

        $this->promotionService = $promotionService;
    }

    protected function configure(): void
    {
        $this
            ->setName('discounts:marketplace:update')
            ->setDescription('Update the status of marketplace discounts based on their start or end date.')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $invalidPromotions = $this->promotionService->findPromotionsWithRightDate(PromotionType::MARKETPLACE(), new \DateTime());
        $validate = 0;
        $invalidate = 0;
        $return = 0;

        foreach ($invalidPromotions as $promotion) {
            try {
                $this->promotionService->validate($promotion->getId());
                $validate++;
            } catch (\Throwable $exception) {
                $return = 1;
                $this->logger->error(
                    'Unable to validate marketplace discount '
                    . $promotion->getId(),
                    ['exception' => $exception]
                );
            }
        }

        $validPromotions = $this->promotionService->findPromotionsWithWrongDate(PromotionType::MARKETPLACE(), new \DateTime());

        foreach ($validPromotions as $promotion) {
            try {
                $this->promotionService->invalidate($promotion->getId());
                $invalidate++;
            } catch (\Throwable $exception) {
                $return = 1;
                $this->logger->error(
                    'Unable to invalidate marketplace discount '
                    . $promotion->getId(),
                    ['exception' => $exception]
                );
            }
        }

        $output->writeln(sprintf('<info>%s marketplace discounts validate!</info>', $validate));
        $output->writeln(sprintf('<info>%s marketplace discounts invalidate!</info>', $invalidate));

        return $return;
    }
}
