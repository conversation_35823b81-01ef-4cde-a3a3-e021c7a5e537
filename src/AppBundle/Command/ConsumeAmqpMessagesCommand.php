<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\ContainerAwareInterface;
use Symfony\Component\DependencyInjection\ContainerAwareTrait;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Wizacha\Async\Exception\WorkerTerminationException;
use Wizacha\Async\Worker;
use Wizacha\Component\Locale\Locale;
use Wizacha\Marketplace\GlobalState\GlobalState;

class ConsumeAmqpMessagesCommand extends Command implements ContainerAwareInterface
{
    use ContainerAwareTrait;

    protected static $defaultName = 'amqp:consume';

    protected const MAX_TTL_OPTION_NAME = 'max-time';
    protected const MAX_MSG_OPTION_NAME = 'max-msg';
    protected const TICK_OPTION_NAME = 'tick';

    protected const DEFAULT_TTL = 60; // seconds
    protected const MAX_TTL = 3600; // seconds
    protected const DEFAULT_MSG = 100; // messages
    protected const MAX_MSG = 2048; // messages

    protected function configure(): void
    {
        $this
            ->setDescription('Start a worker that consumes messages from the specified AMQP queues')
            ->addArgument(
                'queues',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'The queue to process (can be specified multiple times)'
            )
            ->addOption(
                static::MAX_TTL_OPTION_NAME,
                null,
                InputOption::VALUE_REQUIRED,
                \sprintf(
                    'Maximum elapsed time before the worker exits (<= %d)',
                    static::MAX_TTL
                ),
                static::DEFAULT_TTL
            )
            ->addOption(
                static::MAX_MSG_OPTION_NAME,
                null,
                InputOption::VALUE_REQUIRED,
                \sprintf(
                    'Maximum number of messages handled before the worker exits (<= %d)',
                    static::MAX_MSG
                ),
                static::DEFAULT_MSG
            )
            ->addOption(
                static::TICK_OPTION_NAME,
                null,
                InputOption::VALUE_NONE,
                'Poorman feedback',
            );
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        // validate input arguments
        $resolver = new OptionsResolver();
        $resolver
            ->setDefaults([
                static::MAX_TTL_OPTION_NAME => static::DEFAULT_TTL,
                static::MAX_MSG_OPTION_NAME => static::DEFAULT_MSG,
            ])
            ->setAllowedValues(
                static::MAX_TTL_OPTION_NAME,
                function (int $value): bool {
                    return $value <= static::MAX_TTL;
                }
            )
            ->setAllowedValues(
                static::MAX_MSG_OPTION_NAME,
                function (int $value): bool {
                    return $value <= static::MAX_MSG;
                }
            )
        ;

        $options = $resolver->resolve([
            static::MAX_TTL_OPTION_NAME => (int) $input->getOption(static::MAX_TTL_OPTION_NAME),
            static::MAX_MSG_OPTION_NAME => (int) $input->getOption(static::MAX_MSG_OPTION_NAME),
        ]);

        $enableTick = $input->getOption(static::TICK_OPTION_NAME);

        // see: Dispatcher::delayExec()
        \define('WORKER', true);

        // Legacy: to prevent some issues with url generation
        \define('HTTPS', true);

        // Legacy: function needed for attributes import
        require_once __DIR__ . '/../../../app/schemas/exim/feature_variants.functions.php';
        require_once __DIR__ . '/../../../app/schemas/exim/features.functions.php';
        require_once __DIR__ . '/../../../app/schemas/exim/products.functions.php';
        require_once __DIR__ . '/../../../src/AppBundle/Controller/CsCart/backend/exim.php';

        // Legacy: force Locale to french, to make sure the readmodel is generated in french language
        // (it's temporary, as long we don't handle Multiple Language properly)
        GlobalState::switchContentTo(new Locale('fr'));

        $worker = new Worker(
            $this->container->get('wizacha.registry'),
            $this->container->get('doctrine'),
            $this->container->get('monolog.logger.worker'),
            $input->getArgument('queues') ?? [],
            $options[static::MAX_TTL_OPTION_NAME],
            $options[static::MAX_MSG_OPTION_NAME]
        );

        // Intercept signals
        \pcntl_async_signals(true);
        // OS signals to intercept
        $stoppingSignals = [
            SIGTERM,
            SIGHUP,
            SIGINT,
            SIGQUIT,
        ];
        foreach ($stoppingSignals as $signal) {
            \pcntl_signal(
                $signal,
                [$worker, 'signalHandler']
            );
        }

        try {
            $worker->run($enableTick);
        } catch (WorkerTerminationException $e) {
            return;
        }
    }
}
