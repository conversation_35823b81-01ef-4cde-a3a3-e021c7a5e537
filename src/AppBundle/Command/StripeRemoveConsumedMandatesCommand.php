<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Stripe\Source;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Marketplace\Payment\Repository\UserPaymentInfoRepository;
use Wizacha\Marketplace\Payment\Stripe\StripeApi;

class StripeRemoveConsumedMandatesCommand extends Command
{
    private UserPaymentInfoRepository $userPaymentInfoRepository;
    private StripeApi $stripeApi;

    protected function configure()
    {
        $this
            ->setName('stripe:remove-consumed-mandates')
            ->setDescription('Remove consumed mandates (fix WIZ-9643)');
    }

    public function __construct(UserPaymentInfoRepository $userPaymentInfoRepository, StripeApi $stripeApi)
    {
        parent::__construct();

        $this->userPaymentInfoRepository = $userPaymentInfoRepository;
        $this->stripeApi = $stripeApi;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        // get all users with a stripe customer id => customerID + sourceId
        $users = $this->userPaymentInfoRepository->findUserWithStripeMandate();

        // a lot of users can be retrieved, we need a progress bar because the command can be quite long
        $progressBar = new ProgressBar($output, \count($users));
        $progressBar->start();
        foreach ($users as $user) {
            $progressBar->advance();
            try {
                $source = $this->stripeApi->retrieveSource($user->getStripeBankAccountToken());
            } catch (\Stripe\Error\InvalidRequest $e) {
                $output->writeln('Unknown source stored for user ' . $user->getUserId() . '');
                $output->writeln('Error Message :' . $e->getMessage());
                continue;
            }
            if (null !== $source->customer && null === $user->getStripeCustomerId()) {
                $output->writeln('user ' . $user->getUserId() . ' updated in database with stripe given customer_id');
                $this->userPaymentInfoRepository->save($user->setStripeCustomerId($source->customer));
                continue;
            }
            if (Source::STATUS_CONSUMED === $source->status) {
                $output->writeln('Deletion of consumed mandate for user ' . $user->getUserId() . '');
                $this->userPaymentInfoRepository->save($user->setStripeBankAccountToken(null));
                // get all command for this user
                [$orders] = fn_get_orders(['user_id' => [$user->getUserId()], 'include_incompleted' => true]);
                // foreach command if command->isPaid === 0 then log "il va y avoir une erreur" else "le client devra juste re-remplir son IBAN"
                foreach ($orders as $order) {
                    if (null === $order['is_paid']) {
                        $output->writeln('For user ' . $user->getUserId() . ', please check order ' . $order['order_id']);
                    }
                }
            }
        }
        $progressBar->finish();
        $output->writeln('');
    }
}
