<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Cocur\Slugify\Slugify;
use Doctrine\DBAL\Connection;
use Ifsnop\Mysqldump\Mysqldump;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\DumpAnonymizer\Services\OutputS3Manager;
use Wizacha\Janus\RowTransformer\AnonymizerCscartCompaniesTransformer;
use Wizacha\Janus\RowTransformer\AnonymizerCscartOrderDataTransformer;
use Wizacha\Janus\RowTransformer\AnonymizerCscartOrdersTransformer;
use Wizacha\Janus\RowTransformer\AnonymizerCscartUserProfilesTransformer;
use Wizacha\Janus\RowTransformer\AnonymizerCscartUsersTransformer;
use Wizacha\Janus\RowTransformer\AnonymizerDoctrineOrganisationsTransformer;
use Wizacha\Janus\RowTransformer\AnonymizerOrdersActionsTracesTransformer;
use Wizacha\Janus\RowTransformer\RowTransformerException;
use Wizacha\Janus\RowTransformer\RowTransformerInterface;

class DumpAnonymizerCommand extends Command
{
    protected static $defaultName = 'rgpd:anonymize:dump';

    private Connection $connection;
    private OutputS3Manager $outputS3Manager;
    private Slugify $slugify;
    private LoggerInterface $logger;

    private string $platformTechnicalName;

    public function __construct(
        Connection $connection,
        OutputS3Manager $outputS3Manager,
        Slugify $slugify,
        LoggerInterface $logger,
        string $platformTechnicalName
    ) {
        parent::__construct();

        $this->connection = $connection;
        $this->outputS3Manager = $outputS3Manager;
        $this->slugify = $slugify;
        $this->logger = $logger;
        $this->platformTechnicalName = $platformTechnicalName;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('This command output GDPR anonymized dump from the current database.')
            ->addArgument(
                'filepath',
                InputArgument::OPTIONAL,
                'Optionnal output file path, (ex.: - for STDIN)'
            )
        ;
    }

    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ): int {
        set_time_limit(0);
        $io = new SymfonyStyle($input, $output);
        $io->title('Anonymization');

        $filePath = $input->getArgument('filepath');

        if ($filePath === '-') {
            $filePath = 'php://stdout';
        }

        $params = $this->connection->getParams();
        $mysqlDump = new Mysqldump(
            \sprintf(
                'mysql:host=%s;dbname=%s',
                $params['host'],
                $params['dbname']
            ),
            $params['user'],
            $params['password'],
            [
                'compress' => $filePath ? Mysqldump::NONE : Mysqldump::GZIP,
                'add-drop-table' => true,
                'no-data' => [
                    'cscart_views',
                    'doctrine_auth_logs',
                    'doctrine_recipient',
                    'doctrine_mailing_list_recipients',
                ],
            ]
        );

        /** @var RowTransformerInterface[] */
        $transformers = [
            new AnonymizerOrdersActionsTracesTransformer(),
            new AnonymizerCscartOrderDataTransformer(),
            new AnonymizerCscartCompaniesTransformer(),
            new AnonymizerDoctrineOrganisationsTransformer(),
            new AnonymizerCscartUsersTransformer(),
            new AnonymizerCscartUserProfilesTransformer(),
            new AnonymizerCscartOrdersTransformer(),
        ];

        $mysqlDump->setTransformTableRowHook(
            function (string $tableName, array $row) use ($transformers) {
                foreach ($transformers as $transformers) {
                    try {
                        $row = $transformers->transform($tableName, $row);
                    } catch (RowTransformerException $exception) {
                        $this->logger->error(
                            $exception->getMessage(),
                            $exception->getContext()
                        );
                    }
                }

                return $row;
            }
        );

        $tmpFile = tmpfile();

        $inputPath = $filePath ?? \stream_get_meta_data(
            $tmpFile
        )['uri'];
        $outputPath = $this->getOutputPath();

        $io->section('dump');
        $mysqlDump->start($inputPath);
        $io->success($inputPath);

        if ($filePath) {
            return 0;
        }

        $io->section('upload');
        $this->outputS3Manager->uploadFile(
            $inputPath,
            $outputPath
        );
        $io->success($outputPath);

        \fclose($tmpFile);

        return 0;
    }

    private function getOutputPath(): string
    {
        $now = new \DateTimeImmutable(
            'now',
            new \DateTimeZone('UTC')
        );

        $datetime = $now->format('Y-m-d\TH_i_sT');
        $platform = $this->slugify->slugify($this->platformTechnicalName);

        return "$platform-$datetime.sql.gz";
    }
}
