<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Input\StringInput;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\Finder\Finder;
use Symfony\Component\Finder\SplFileInfo;
use Wizacha\AppBundle\Service\ConfigurationService;
use Wizacha\Marketplace\Theme\LessCompiler;

class AssetsInstallCommand extends Command
{
    /** @var LessCompiler */
    private $lessCompiler;

    /** @var ConfigurationService */
    private $configurationService;

    /** @var string */
    private $themeBundle;

    /** @var string */
    private $projectDir;

    /**
     * AssetsInstallCommand constructor.
     */
    public function __construct(
        LessCompiler $lessCompiler,
        ConfigurationService $configurationService,
        string $themeBundle,
        string $projectDir
    ) {
        parent::__construct();
        $this->lessCompiler = $lessCompiler;
        $this->configurationService = $configurationService;
        $this->themeBundle = $themeBundle;
        $this->projectDir = $projectDir;
    }

    protected function configure()
    {
        $this
            ->setName('theme:assets:install')
            ->setDescription('Install assets of the current theme')
            ->addOption(
                'off-line',
                null,
                InputOption::VALUE_NONE,
                'Disable asset generation if the bundle require a DB connection'
            )
        ;
    }



    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $fs = new Filesystem();
        chdir($this->projectDir);

        $dir = "src/$this->themeBundle/Resources/public";
        if (! is_dir($dir)) {
            $output->writeln("<comment>Directory $dir does not exist, falling back to AppBundle assets</comment>");
            $dir = 'src/AppBundle/Resources/public';
        }

        $output->writeln("<info>Installing assets from $dir</info>");

        // Images
        $fs->remove('assets/images');
        $fs->mkdir('assets/images');
        $this->cpr($dir . '/images/*', 'assets/images/');

        // Fonts
        $fs->remove('assets/fonts');
        $fs->mkdir('assets/fonts');
        $this->cpr($dir . '/fonts/*', 'assets/fonts/');


        // Css from less
        $fs->remove('assets/css/main.css');
        $fs->mkdir('assets/css');

        if (!file_exists($dir . '/css/main.less')) {
            throw new \Exception(sprintf('File %s does not exist', $dir . '/css/main.less'));
        }

        $css = $this->lessCompiler->compile($dir . '/css/main.less', [], '/assets/');
        file_put_contents('assets/css/main.css', $css);

        // Js
        $fs->remove('assets/js');
        $timestamp = date('YmdHis');
        $fs->mkdir("assets/js/$timestamp");
        $finder = new Finder();
        $finder->files()->name('*.js')->in(['src/AppBundle/Resources/public/common/js', $dir . '/js']);
        foreach ($finder as $file) {
            /** @var SplFileInfo $file */
            $minifiedJs = \JShrink\Minifier::minify($file->getContents());
            if (strpos($file->getRelativePathname(), '/') !== false) {
                $fs->mkdir("assets/js/$timestamp/" . $file->getRelativePath());
            }
            $filename = preg_replace('/.js$/i', '-min.js', $file->getRelativePathname());
            file_put_contents("assets/js/$timestamp/$filename", $minifiedJs);
        }

        // Bundle assets
        $this->getApplication()->find('assets:install')->run(new StringInput('assets:install assets --symlink --relative --bypass-legacy-init'), $output);

        $output->writeln('<info>Assets successfully installed!</info>');
    }

    private function cpr($src, $dst)
    {
        exec("cp -r $src $dst 2>&1");
    }
}
