<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Marketplace\PIM\Option\SystemOptionsRegistry;

class DeploySystemOptions extends Command implements DeployPostActionsInterface
{
    protected const MODE_CREATE = 'create';
    protected const MODE_ROLLBACK = 'rollback';

    /** @var string $configEnabledSystemOptions */
    protected $configEnabledSystemOptions;

    /** @var SystemOptionsRegistry */
    protected $registry;

    public function __construct(string $configEnabledSystemOptions, SystemOptionsRegistry $registry)
    {
        parent::__construct();

        $this->configEnabledSystemOptions = $configEnabledSystemOptions;
        $this->registry = $registry;
    }

    public function getPriority(): int
    {
        return 100;
    }

    protected function configure(): void
    {
        $this
            ->setName('deploy:command:system-options')
            ->setDescription('Deploy systems options. See help <info>php %command.full_name% --help</info>')
            ->addArgument('code', InputArgument::IS_ARRAY | InputArgument::OPTIONAL, 'Systems options code')
            ->addOption('create', null, InputOption::VALUE_NONE, 'Create systems options.')
            ->addOption('rollback', null, InputOption::VALUE_NONE, 'Delete systems options.')
            ->addOption('get-codes', null, InputOption::VALUE_NONE, 'Delete systems options.')
            ->setHelp(<<<'EOF'
The <info>%command.name%</info> command create or delete systems options according to the given option ("<comment>--create</comment>" or "<comment>--rollback</comment>").

The <info>%command.name%</info> is called when running PostDeploy command, and get values of "<comment>CONFIG_ENABLED_SYSTEM_OPTIONS</comment>"
environment variable as "<comment>code</comment>" argument.

When running the command manually, if argument "<comment>code</comment>" is not provide, environment variable "<comment>CONFIG_ENABLED_SYSTEM_OPTIONS</comment>" values
will be set as argument, otherwise it will set the provided "<comment>code</comment>".

Example running command manually
  <info>php %command.full_name% --help</info>
  <info>php %command.full_name%</info>
  <info>php %command.full_name% payment_frequency commitment_period --create</info>
  <info>php %command.full_name% payment_frequency commitment_period --rollback</info>

Available codes : <info>php %command.full_name% --get-codes</info>
EOF
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if (true === $input->getOption('get-codes')) {
            $output->writeln($this->getAvailableCodes($output));

            return 0;
        }

        if (true === $input->getOption('create') && true === $input->getOption('rollback')) {
            $output->writeln('<error>Using --create and --rollback at the same time is forbidden.</error>');

            return 1;
        }

        $codes = explode(',', $this->configEnabledSystemOptions);
        $mode = static::MODE_CREATE;

        if (true === $input->getOption('create') && 0 < \count($input->getArgument('code'))) {
            $codes = $input->getArgument('code');
        } elseif (true === $input->getOption('rollback') && 0 < \count($input->getArgument('code'))) {
            $codes = $input->getArgument('code');
            $mode = static::MODE_ROLLBACK;
        }

        $error = false;

        foreach ($codes as $code) {
            if ('' === $code) {
                $output->writeln('<info>No system option has been created because no code has been configured.</info>');

                return 1;
            }

            $handler = $this->registry->getHandler($code);

            if (false === \is_null($handler)) {
                if (static::MODE_CREATE === $mode) {
                    $handler->up();
                    $output->writeln('<info>System option "' . $code . '" created successfully.</info>');
                } else {
                    $handler->down();
                    $output->writeln('<info>System option "' . $code . '" deleted successfully.</info>');
                }
            } else {
                $output->writeln('<error>The code "' . $code . '" is not valid. Authorized codes list available running ' . $this->getName() . ' --get-codes </error>');
                $error = true;
            }
        }

        return $error ? 1 : 0;
    }

    protected function getAvailableCodes(OutputInterface $output)
    {
        $table = new Table($output);
        $table->setHeaders([
            'FQCN',
            'Code',
        ]);

        $commandsRows = [];

        foreach ($this->registry->getHandlersCodes() as $handler) {
            $commandsRows[] = [
                \get_class($handler),
                $handler->getSystemOptionCode(),
            ];
        }

        $table->setRows($commandsRows);

        $table->render();
    }
}
