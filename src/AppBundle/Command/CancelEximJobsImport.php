<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Component\Import\EximJobService;
use Wizacha\Component\Import\JobStatus;

class CancelEximJobsImport extends Command
{
    private EximJobService $eximJobService;

    public function __construct(EximJobService $eximJobService)
    {
        parent::__construct();

        $this->eximJobService = $eximJobService;
    }

    protected function configure(): void
    {
        parent::configure();

        $this
            ->setName('exim:jobs:cancel')
            ->setDescription('cancel pending exim jobs.')
            ->addArgument(
                'id',
                null,
                InputOption::VALUE_REQUIRED,
                'The id of the JOB.'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $job = $this->eximJobService->get($input->getArgument('id'));

        if (JobStatus::PENDING()->equals($job->getStatus()) === false) {
            $io->error('Impossible to cancel an import job that is not pending.');

            return 1;
        }

        $this->eximJobService->cancel($job);

        $io->success(sprintf("Job with id %s is canceled successfully.", $input->getArgument('id')));

        return 0;
    }
}
