<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\AppBundle\Command\Traits\DeployPostActionsOnce;
use Wizacha\Marketplace\Payment\PaymentProcessorIdentifier;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Transaction\Transaction;
use Wizacha\Marketplace\Transaction\TransactionStatus;
use Wizacha\Marketplace\Transaction\TransactionType;
use Wizacha\Money\Money;

/**
 * Class MigrateOrderTransactionsCommand
 *
 * Migrate data for the new transaction table:
 *   1. doctrine_order_transactions.processor_informations is now a serialized array
 *   2. Duplicate cscart_order_data encrypted data to doctrine_order_transactions
 *
 * Note that this migration command should be run only once,
 * but it requires Symfony's container for encryption, so it can't run through phinx migration.
 *
 * Also note that existing transaction won't transaction reference as they're mostly not saved yet.
 */
class DeployMigrateOrderTransactionsCommand extends Command implements DeployPostActionsInterface
{
    use DeployPostActionsOnce;

    protected const VERSION_DATE = '2019-11-21 15:01:00';
    protected const VERSION_NAME = 'MigrateOrderTransactions';

    /** @var EntityManagerInterface */
    protected $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        parent::__construct();

        $this->entityManager = $entityManager;
    }

    public function getPriority(): int
    {
        return 101;
    }

    protected function configure(): void
    {
        $this
            ->setName('deploy:command:migrate-transaction')
            ->setDescription('Migrate new transaction data.')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if ($this->hasRun() === true) {
            return 0;
        }

        $this->executeExistingTransactionData($input, $output);
        $this->executeCreateOrderTransactions($input, $output);

        $this->saveExecution();

        return 0;
    }

    protected function executeExistingTransactionData(InputInterface $input, OutputInterface $output): void
    {
        $sqlSelect = 'SELECT id, order_id, processor_informations FROM doctrine_order_transactions';
        $sqlUpdate = 'UPDATE doctrine_order_transactions SET processor_informations = :data WHERE id = :id';

        $statementSelect = $this->getConnection()->prepare($sqlSelect);
        $statementUpdate = $this->getConnection()->prepare($sqlUpdate);

        $output->writeln('<info>Retrieving required data to update existing transactions</info>');
        $statementSelect->execute();
        $output->writeln('<info>Starting to update</info>');

        foreach ($statementSelect->fetchAll(\PDO::FETCH_ASSOC) as $row) {
            $statementUpdate->execute([
                'data' => \serialize([$row['processor_informations']]),
                'id' => $row['id'],
            ]);
        }
    }

    protected function executeCreateOrderTransactions(InputInterface $input, OutputInterface $output): void
    {
        $mapping = $this->getProcessorMapping();

        $output->writeln('<info>Retrieving required data to migration transactions</info>');

        $sql = '
            SELECT
                cod.order_id,
                cod.data,
                co.total,
                cp.processor_id,
                co.is_paid,
                co.is_capture
            FROM cscart_order_data cod
            INNER JOIN cscart_orders co ON cod.order_id = co.order_id
            INNER JOIN cscart_payments cp on co.payment_id = cp.payment_id
            WHERE cod.type = :type
        ';

        $statement = $this->getConnection()->prepare($sql);

        $statement->execute(['type' => 'P']);

        $data = $statement->fetchAll(\PDO::FETCH_ASSOC);
        $count = \count($data);
        $progressBar = new ProgressBar($output, $count);

        $output->writeln('<info>Starting migration of ' . $count . ' transactions</info>');

        if (false === $output->isVerbose()) {
            $progressBar->start();
        }

        foreach ($data as $entry) {
            $status = $entry['is_paid'] ? TransactionStatus::SUCCESS() : TransactionStatus::FAILED();
            $information = \unserialize(fn_decrypt_text($entry['data']));

            if (\is_array($information)
                && \array_key_exists('stripe_charge_captured', $information)
                && $entry['is_capture'] === null
            ) {
                $status = TransactionStatus::PENDING();
            }

            $transaction = new Transaction(
                (int) $entry['order_id'],
                $mapping[$entry['processor_id']][1],
                $status,
                Money::fromVariable($entry['total'])
            );

            $transaction->setProcessorName($mapping[$entry['processor_id']][0]->getValue())
                ->setProcessorInformations(\is_array($information) ? $information : null)
                ->updateTimestamp();
            $this->entityManager->persist($transaction);

            if (false === $output->isVerbose()) {
                $progressBar->advance();
            } else {
                $output->writeln('<info>Created transaction for Order ID #' . $entry['order_id'] . '</info>');
            }
        }

        if (false === $output->isVerbose()) {
            $progressBar->finish();
        } else {
            $output->writeln('Saving transactions in the database');
        }

        $this->entityManager->flush();
    }

    /** @return array [Processor ID => [Processor name, Transaction type]] */
    protected function getProcessorMapping(): array
    {
        return [
            PaymentProcessorIdentifier::MANGOPAY_CARD()->getValue() => [
                PaymentProcessorName::MANGOPAY(), TransactionType::CREDITCARD(),
            ],
            PaymentProcessorIdentifier::LEMONWAY_CARD()->getValue() => [
                PaymentProcessorName::LEMONWAY(), TransactionType::CREDITCARD(),
            ],
            PaymentProcessorIdentifier::LEMONWAY_BANKWIRE()->getValue() => [
                PaymentProcessorName::LEMONWAY(), TransactionType::BANK_WIRE(),
            ],
            PaymentProcessorIdentifier::MANGOPAY_BANKWIRE()->getValue() => [
                PaymentProcessorName::MANGOPAY(), TransactionType::BANK_WIRE(),
            ],
            PaymentProcessorIdentifier::HIPAY_CARD()->getValue() => [
                PaymentProcessorName::HIPAY(), TransactionType::CREDITCARD(),
            ],
            PaymentProcessorIdentifier::SMONEY_CARD()->getValue() => [
                PaymentProcessorName::SMONEY(), TransactionType::CREDITCARD(),
            ],
            PaymentProcessorIdentifier::STRIPE_CARD()->getValue() => [
                PaymentProcessorName::STRIPE(), TransactionType::CREDITCARD(),
            ],
            PaymentProcessorIdentifier::STRIPE_SEPA_DIRECT()->getValue() => [
                PaymentProcessorName::STRIPE(), TransactionType::BANK_WIRE(),
            ],
            PaymentProcessorIdentifier::STRIPE_SEPA_DEFERMENT()->getValue() => [
                PaymentProcessorName::STRIPE(), TransactionType::BANK_WIRE(),
            ],
            PaymentProcessorIdentifier::NO_PAYMENT()->getValue() => [
                PaymentProcessorName::NONE(), TransactionType::CREDITCARD(),
            ],
            PaymentProcessorIdentifier::HIPAY_CAPTURE()->getValue() => [
                PaymentProcessorName::HIPAY(), TransactionType::CREDITCARD(),
            ],
            PaymentProcessorIdentifier::OFFLINE()->getValue() => [
                PaymentProcessorName::NONE(), TransactionType::CREDITCARD(),
            ],
        ];
    }

    protected function getConnection(): Connection
    {
        return $this->entityManager->getConnection();
    }

    protected function getDateTime(): \DateTimeInterface
    {
        return new \DateTimeImmutable(static::VERSION_DATE);
    }

    protected function getVersionName(): string
    {
        return static::VERSION_NAME;
    }
}
