<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Stopwatch\Stopwatch;
use Wizacha\Marketplace\User\Exception\UserAlreadyExists;
use Wizacha\Marketplace\User\UserService;

class CreateUserFromCSVCommand extends Command
{
    /** @var UserService */
    protected $userService;
    /** @var Stopwatch */
    protected $stopWatch;
    /** @var int */
    protected $quantity = 0;
    /** @var ProgressBar */
    protected $progressBar;

    public function __construct(UserService $userService)
    {
        parent::__construct();
        $this->userService = $userService;
        $this->stopWatch = new Stopwatch();
    }

    protected function configure()
    {
        $this->setName('user:create:from-csv')
            ->setDescription('Create users from a CSV File')
            ->addArgument('file', InputArgument::REQUIRED, 'Path to the import CSV, Ex: ./var/input/usrImport.csv')
            ->addArgument('limit', InputArgument::OPTIONAL, 'Number of lines to import', 1000)
            ->addArgument('offset', InputArgument::OPTIONAL, 'First Line to consider', 0);
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $offset = (int) $input->getArgument('offset');
        $limit =  (int) $input->getArgument('limit');

        $io = new SymfonyStyle($input, $output);

        $progressBar = new ProgressBar($output, $limit);
        $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% %elapsed:6s%/%estimated:-6s% %memory:6s%');
        $progressBar->start();

        $now = new \DateTime();
        $path = $input->getArgument('file');
        $io->title('Batch creation of users - Start: [' . $now->format('G:i:s') . ']');

        $this->stopWatch->start('main');

        $handle = fopen($path, "r");
        $lineNumber = 0;
        $writtenLines = 0;

        while ($limit > $writtenLines && ($data = fgetcsv($handle, 0, ';')) !== false) {
            if ($lineNumber >= $offset) {
                try {
                    $this->userService->createUser($data[0], $data[1]);

                    ++$this->quantity;

                    $progressBar->advance();
                } catch (UserAlreadyExists $e) {
                    $io->note(
                        \sprintf(
                            'line: %d error: Account %s already exist',
                            $lineNumber,
                            $data[0]
                        )
                    );
                } catch (\Throwable $throwable) {
                    $io->error(
                        \sprintf(
                            'line: %d account: %s error: %s',
                            $lineNumber,
                            $data[0],
                            $throwable->getMessage()
                        )
                    );
                }

                ++$writtenLines;
            }

            ++$lineNumber;
        }

        $progressBar->finish();
        $event = $this->stopWatch->stop('main');
        $io->success(sprintf("%d user created in %d ms and %d bytes.", $this->quantity, $event->getDuration(), $event->getMemory()));
        $now = new \DateTime();
        $io->text('End of script: [' . $now->format('G:i:s') . ']');

        return 0;
    }
}
