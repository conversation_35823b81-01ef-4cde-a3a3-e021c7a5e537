<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\AppBundle\Command\Traits\DeployPostActionsOnce;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Input\InputArgument;

class DeployOnceCompanyEmailSettingsCommand extends Command implements DeployPostActionsInterface
{
    use DeployPostActionsOnce;

    protected static $defaultName = 'deploy:command:company_email_settings';

    protected const SETTINGS_EMAILS = [
        'company_users_department',
        'company_site_administrator',
        'company_orders_department',
        'company_support_department',
        'company_newsletter_email',
    ];

    /** @var string */
    protected $mail;

    /** @var string */
    protected const VERSION_DATE = '2020-02-10 11:00:00';

    /** @var string */
    protected const VERSION_NAME = 'Reset Setting Email';

    /** @var EntityManagerInterface */
    protected $entityManager;

    public function __construct(EntityManagerInterface $entityManager, string $mailDomain)
    {
        parent::__construct();

        $this->entityManager = $entityManager;
        $this->mail = "contact@" . $mailDomain;
    }

    public function getPriority(): int
    {
        return 100;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Reset Email Setting')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        //check that the command has been executed before
        if ($this->hasRun() === true) {
            $output->writeln("<info>command can be executed only once.</info>");

            return 0;
        }

        foreach (static::SETTINGS_EMAILS as $key => $mail) {
            $query = $this->getConnection()->prepare("SELECT value FROM cscart_settings_objects WHERE name = :mail");
            $query->execute(['mail' => $mail]);
            $row = $query->fetch(\PDO::FETCH_OBJ);

            //check if a value exit
            if (\strlen($row->value) === 0) {
                $statement = $this->getConnection()->prepare("UPDATE cscart_settings_objects SET value = ? WHERE name = ?");
                $statement->execute([$this->mail, $mail]);
            }
        }
        $output->writeln("<info>Company's email has been updated with success.</info>");
        $this->saveExecution();

        return 0;
    }


    protected function getConnection(): Connection
    {
        return $this->entityManager->getConnection();
    }

    protected function getDateTime(): \DateTimeInterface
    {
        return new \DateTimeImmutable(static::VERSION_DATE);
    }

    protected function getVersionName(): string
    {
        return static::VERSION_NAME;
    }
}
