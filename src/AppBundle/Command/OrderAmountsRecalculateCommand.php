<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Marketplace\Order\AmountsCalculator\Service\OrderAmountsCalculator;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Product;
use Wizacha\Status;

class OrderAmountsRecalculateCommand extends Command
{
    protected static $defaultName = 'orders:recalculate';

    private OrderService $orderService;
    private OrderAmountsCalculator $orderAmountCalculator;

    public function __construct(
        OrderService $orderService,
        OrderAmountsCalculator $orderAmountsCalculator
    ) {
        parent::__construct();

        $this->orderService = $orderService;
        $this->orderAmountCalculator = $orderAmountsCalculator;
    }

    protected function configure()
    {
        $this
            ->addArgument(
                'order-id',
                InputArgument::IS_ARRAY,
                'order_id(s)'
            )
            ->setDescription(
                <<<TXT
                Recalculate missing taxes orders.
                TXT
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('Recalculate OrderAmounts');

        if (false === $io->confirm('This is a destructive operation. Do you confirm?', false)) {
            $io->warning('Aborted');

            return 0;
        };

        $orderIds = \array_map(
            'intval',
            $input->getArgument('order-id')
        );

        foreach ($orderIds as $orderId) {
            try {
                $this->process($orderId);

                $io->success(
                    \sprintf(
                        'Order %s updated ...',
                        $orderId
                    )
                );
            } catch (\Exception | \Error $exception) {
                $io->error(
                    \sprintf(
                        <<<TXT
                        Order %s failed :
                        %s
                        TXT,
                        $orderId,
                        $exception->getMessage()
                    )
                );
            }
        }
    }

    private function process(int $orderId)
    {
        $order = $this->orderService->getOrder($orderId);

        foreach ($order->getOrderAmounts()->getAmountItems() as $amountItem) {
            $productId = $amountItem->getOrderItemData()->getProductId();
            $product = new Product($productId);
            $tax = \reset(
                $product->getTaxes(Status::ENABLED())
            );

            $price = $tax->isPriceIncluded()
                ? $amountItem->getUnitPriceInclTaxes()
                : $amountItem->getUnitPriceExclTaxes()
            ;

            $amountItem->hydrate(
                $price,
                $tax->getId(),
                $tax->getRate(),
                $amountItem->getGreenTax(),
                $tax->isPriceIncluded(),
                $amountItem->getQuantity()
            );
        }

        $this->orderAmountCalculator->recalculate($order);
    }
}
