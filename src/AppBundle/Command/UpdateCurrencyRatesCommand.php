<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Marketplace\Currency\CurrencyService;
use Wizacha\Marketplace\Currency\Exception\FixerRequestFailed;
use Wizacha\Marketplace\Currency\ExchangeRate;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Wizacha\Marketplace\Currency\Event\CurrencyRatesUpdateFailed;
use Wizacha\Marketplace\Currency\FixerClientApi;

class UpdateCurrencyRatesCommand extends Command
{
    /** @var CurrencyService */
    protected $currencyService;

    /** @var EventDispatcherInterface */
    protected $eventDispatcher;

    /** @var LoggerInterface */
    protected $logger;

    /** @var FixerClientApi */
    private $fixerClientApi;

    protected static $defaultName = 'currency:rates:update';

    public function __construct(
        CurrencyService $currencyService,
        EventDispatcherInterface $eventDispatcher,
        LoggerInterface $logger,
        FixerClientApi $fixerClientApi
    ) {
        $this->currencyService = $currencyService;
        $this->eventDispatcher = $eventDispatcher;
        $this->logger = $logger;
        $this->fixerClientApi = $fixerClientApi;

        parent::__construct();
    }

    protected function configure(): void
    {
        $this->setDescription('Update currencies rates');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if ($this->currencyService->isFeatureCurrencyAdvancedActivated() === false) {
            return 0;
        }

        $this->setDefaultCurrency();
        try {
            $this->fixerClientApi->assertApiKeyAndUrlValid();
        } catch (\InvalidArgumentException $exception) {
            $this->eventDispatcher->dispatch(
                new CurrencyRatesUpdateFailed($exception->getMessage()),
                CurrencyRatesUpdateFailed::class
            );
            $this->logger->error('Updating currency rates failed', [
                'exception' => $exception,
            ]);

            $output->writeln('<error>' . $exception->getMessage() . '</error>');

            return 1;
        }

        $currencies = $this->currencyService->list();
        $nbCurrencies = 0;
        try {
            $latestCurrency = $this->fixerClientApi->getLatestCurrencyRate($this->currencyService->getDefaultCurrencyCode());
        } catch (FixerRequestFailed $exception) {
            $this->eventDispatcher->dispatch(
                new CurrencyRatesUpdateFailed($exception->getMessage()),
                CurrencyRatesUpdateFailed::class
            );
            $this->logger->error('Updating currency rates failed', [
                'message' => $exception->getMessage(),
                'code' => $exception->getCode(),
                'exception' => $exception,
            ]);

            $output->writeln('<error>' . $exception->getMessage() . '</error>');

            return 1;
        }

        foreach ($currencies as $currency) {
            $data = [];
            if (\array_key_exists($currency->getCode(), $latestCurrency["rates"]) === true
                && \is_null($latestCurrency["rates"][$currency->getCode()]) === false
            ) {
                $data['exchangeRate'] = ExchangeRate::fromVariable($latestCurrency["rates"][$currency->getCode()]);
                $nbCurrencies++;
            } else {
                $data['exchangeRate'] = null;
            }

            $data['updatedAt'] = new \DateTime();
            if ($currency->getCode() === $this->currencyService->getDefaultCurrencyCode()) {
                $data['enabled'] = true;
            }

            $this->currencyService->update($currency->getCode(), $data);
        }

        $output->writeln('<info>Ok (' . $nbCurrencies . ' currencies rates are updated)</info>');

        return 0;
    }

    public function setDefaultCurrency(): void
    {
        $data = [
            'enabled' => true,
            'exchangeRate' => ExchangeRate::fromVariable(1),
            'updatedAt' => new \DateTime()
        ];
        $this->currencyService->update($this->currencyService->getDefaultCurrencyCode(), $data);
    }
}
