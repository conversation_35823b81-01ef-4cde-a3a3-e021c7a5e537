<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\ConfirmationQuestion;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Search\Engine\AlgoliaService;

class ProductRefreshCacheCommand extends Command
{
    /** @var AlgoliaService */
    protected $algoliaService;

    public function __construct(AlgoliaService $algoliaService)
    {
        parent::__construct();

        $this->algoliaService = $algoliaService;
    }

    protected function configure(): void
    {
        $this
            ->setName('products:cache:refresh')
            ->setDescription("Clear and recreate Algolia index, then clear and recreate readmodel.");
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title($this->getDescription());

        if (false === $this->algoliaService->isFeatureEnableUpdateAlgolia()) {
            $io->error("Update blocked by config. Call each command separately with the relevant --force options.");
            return 0;
        }

        if (false === $io->confirm("This is a destructive operation. Do you confirm?")) {
            $io->warning("Cancelled.");

            return 0;
        }

        $commands = [
            'algolia:index:clear',
            'algolia:index:create',
            'algolia:config:push',
            'readmodel:clear',
            'readmodel:create',
        ];

        foreach ($commands as $name) {
            if ($this->getApplication()->find($name)->run($input, $output) > 0) {
                throw new \RuntimeException("An error has occurred with command $name");
            }
        }

        return 0;
    }
}
