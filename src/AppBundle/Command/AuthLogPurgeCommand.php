<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Component\AuthLog\AuthLogRepository;

class AuthLogPurgeCommand extends Command
{
    /** @var int */
    private $logsTTL;

    /** @var AuthLogRepository */
    private $authLogRepo;

    public function __construct(int $logsTTL, AuthLogRepository $authLogRepo)
    {
        $this->logsTTL = $logsTTL;
        $this->authLogRepo = $authLogRepo;

        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setName('purge:authlogs')
            ->setDescription(
                sprintf(
                    'Purge authlog ( > %d month old between %d and %d), editable with clear_cache_auth_delay_month',
                    $this->logsTTL,
                    AuthLogRepository::TTL_MIN,
                    AuthLogRepository::TTL_MAX
                )
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $qty = $this->authLogRepo->purge($this->logsTTL);

        $output->writeln(
            sprintf(
                '<comment>AuthLog: %d record%s deleted</comment>',
                $qty,
                $qty > 1 ? 's' : ''
            )
        );

        return 0;
    }
}
