<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Marketplace\RgpdAnonymizer\RgpdAnonymizerService;
use Wizacha\Marketplace\RgpdAnonymizer\RgpdAnonymizerType;

class RgpdUserAnonymizerCommand extends Command
{
    /** @var RgpdAnonymizerService */
    protected $rgpdAnonymizerService;

    /** @var string  */
    protected static $defaultName = 'rgpd:anonymize-user';

    public function __construct(RgpdAnonymizerService $rgpdAnonymizerService)
    {
        parent::__construct();

        $this->rgpdAnonymizerService = $rgpdAnonymizerService;
    }

    protected function configure(): void
    {
        $this
            ->addArgument('userId', InputArgument::REQUIRED, 'Enter a user id.')
            ->setDescription('Anonymize a user and disable it.')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $userId = $input->getArgument('userId');

        if (false === \ctype_digit($userId)) {
            $output->writeln('The userId parameter is invalid (must be an integer)');

            return 1;
        }

        try {
            $anonymisationInformations = $this->rgpdAnonymizerService
                ->anonymize((int) $userId, RgpdAnonymizerType::USER()->getValue())
                ->expose()
            ;

            foreach ($anonymisationInformations as $information) {
                $output->writeln($information);
            }
        } catch (\Throwable $exception) {
            $output->writeln($exception->getMessage());

            return 1;
        }

        return 0;
    }
}
