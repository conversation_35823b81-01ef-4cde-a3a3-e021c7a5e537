<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Exim\Import\AutomatedFeedsService;

class ImportAutomatedFeedsCommand extends Command
{
    private AutomatedFeedsService $automatedFeedsService;
    private bool $enableAutomatedFeeds;

    public function __construct(AutomatedFeedsService $automatedFeedsService, bool $enableAutomatedFeeds)
    {
        parent::__construct();

        $this->automatedFeedsService = $automatedFeedsService;
        $this->enableAutomatedFeeds = $enableAutomatedFeeds;
    }

    protected function configure()
    {
        $this
            ->setName('csv:import-automated-feeds')
            ->setDescription('Import automated feeds')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Automated feeds');

        if (false === $this->enableAutomatedFeeds) {
            $io->warning('Automated feeds are disabled');

            return 0;
        }

        $results = $this->automatedFeedsService->treatAutomatedFeeds();

        if (null === $results) {
            $io->warning('No feeds to process');

            return 0;
        }

        [$success, $total] = $results;

        $io->{
            $success === $total
            ? 'success'
            : 'error'
        }(
            sprintf(
                '%s/%s feed%s processed',
                $success,
                $total,
                $total > 1 ? 's' : ''
            )
        );

        // error exit code if there is error in some feeds processing
        return $success !== $total;
    }
}
