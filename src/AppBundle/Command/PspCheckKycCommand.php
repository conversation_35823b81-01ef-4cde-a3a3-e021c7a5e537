<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class PspCheckKycCommand extends Command
{
    protected function configure(): void
    {
        $this
            ->setName('psp:check-kyc')
            ->setDescription('Check the KYC of all pending and C2C companies for all PSP')
            ->addOption(
                'no-pending-only',
                null,
                InputOption::VALUE_NONE,
                'Check the KYC of all companies except company with status = new'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if ($input->getOption('no-pending-only') === true) {
            $input = new ArrayInput(['--no-pending-only']);
        } else {
            $input = new ArrayInput([]);
        }

        $this
            ->getApplication()
            ->find('mangopay:check-kyc')
            ->run($input, $output);

        $this
            ->getApplication()
            ->find('smoney:check-kyc')
            ->run($input, $output);

        $this
            ->getApplication()
            ->find('lemonway:check-kyc')
            ->run($input, $output);

        $this
            ->getApplication()
            ->find('hipay:check-kyc')
            ->run(new ArrayInput([]), $output)
        ;

        return 0;
    }
}
