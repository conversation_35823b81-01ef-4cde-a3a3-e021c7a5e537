<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Misc;

class GenerateSitemapCommand extends Command
{
    protected function configure()
    {
        $this
            ->setName('sitemap:generate')
            ->setDescription('Generate sitemap files')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        Misc::updateSitemapFilesDelayed();

        $output->writeln('<info>Sitemap files are building...</info>');
    }
}
