<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command\Moderation;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Marketplace\PIM\Moderation\ModerationService;

class PurgeProductInProgressCommand extends Command
{
    /**
     * @var ModerationService
     */
    protected $moderationService;

    public function __construct(ModerationService $moderationService)
    {
        parent::__construct();

        $this->moderationService = $moderationService;
    }

    protected function configure(): void
    {
        $this
            ->setName('purge:product-moderation-in-progress')
            ->setDescription('Remove all products moderation in progress older than 24h')
            ->addOption(
                'approval',
                null,
                InputOption::VALUE_REQUIRED,
                'true = approve all pending products / false = refuse all pending products'
            )
            ->addOption(
                'reason',
                null,
                InputOption::VALUE_OPTIONAL,
                'Moderation reason'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        // Option de approve or refuse all in_progress moderation
        $approval = $input->getOption('approval');
        // The reason is used in the mail sent to the companies
        $reason = $input->getOption('reason') ?? '';

        if ('true' === $approval) {
            $this->moderationService->processAll($reason, true);
        } elseif ('false' === $approval) {
            $this->moderationService->processAll($reason, false);
        } else {
            throw new \RuntimeException("Approval option must be true or false");
        }

        return 0;
    }
}
