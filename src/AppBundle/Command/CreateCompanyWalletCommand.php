<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\ContainerAwareInterface;
use Symfony\Component\DependencyInjection\ContainerAwareTrait;
use Wizacha\Company;
use Wizacha\Events\IterableEvent;
use Wizacha\Marketplace\Entities\Company as EntityCompany;

class CreateCompanyWalletCommand extends Command implements ContainerAwareInterface
{
    use ContainerAwareTrait;

    protected function configure()
    {
        $this
            ->setName('company:create:wallet')
            ->setDescription('Syncs wallets between marketplace and payment platform')
            ->addOption(
                'no-enabled-only',
                null,
                InputOption::VALUE_NONE,
                'Check the KYC of all companies except company with status = new'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        if ($input->getOption('no-enabled-only') === true) {
            $companies = db_get_fields('SELECT company_id FROM ?:companies WHERE status != ?s', Company::STATUS_NEW);
        } else {
            $companies = db_get_fields('SELECT company_id FROM ?:companies WHERE status = ?s', Company::STATUS_ENABLED);
        }
        $lemonway = $this->container->get('marketplace.payment.lemonway');
        $mangopay = $this->container->get('marketplace.payment.mangopay');
        $hipay = $this->container->get('marketplace.payment.hipay');
        $stripe = $this->container->get('marketplace.payment.stripe');

        foreach ($companies as $companyId) {
            $company = new EntityCompany($companyId);
            if ($lemonway->isConfigured()) {
                try {
                    $lemonway->getCompanyWallet($company);
                } catch (\Exception $e) {
                    $output->writeln('<error>Lemonway: Skip company ' . $companyId . ' : ' . $e->getMessage() . '</error>');
                }
            }

            if ($mangopay->isConfigured()) {
                try {
                    $mangopay->getCompanyUserAndWallet($company);
                } catch (\Exception $e) {
                    $output->writeln('<error>Mangopay: Skip company ' . $companyId . ' : ' . $e->getMessage() . '</error>');
                }
            }

            if ($hipay->isConfigured()) {
                try {
                    $hipay->onCompanyUpdate(IterableEvent::fromElement($companyId));
                } catch (\Exception $e) {
                    $output->writeln('<error>HiPay: Skip company ' . $companyId . ' : ' . $e->getMessage() . '</error>');
                }
            }

            if ($stripe->isConfigured()) {
                try {
                    $stripe->onCompanyUpdate(IterableEvent::fromElement($companyId));
                } catch (\Exception $e) {
                    $output->writeln('<error>Stripe: Skip company ' . $companyId . ' : ' . $e->getMessage() . '</error>');
                }
            }
        }

        $output->writeln('<comment>Ok</comment>');
    }
}
