<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\DependencyInjection\ContainerAwareInterface;
use Symfony\Component\DependencyInjection\ContainerAwareTrait;
use Wizacha\Marketplace\Order\Workflow\WorkflowName;
use Symfony\Component\Filesystem\Filesystem;

/**
 * run this command in var/workflows folder to render graph as PNG:
 * ls *.dot | xargs -I {} sh -c "cat {} | dot -Tpng > {}.png"
 */
class WorkflowToGraphvizCommand extends Command implements ContainerAwareInterface
{
    use Container<PERSON><PERSON>Trait;

    protected function configure()
    {
        $this
            ->setName('debug:workflows')
            ->setDescription('Generate a graph of all possible workflows.')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $io = new SymfonyStyle($input, $output);

        $workflowService = $this->container->get('marketplace.order.workflow_service');
        $workflows = WorkflowName::getClassNames();
        $actions = array_filter($this->container->getServiceIds(), function ($id) {
            return strpos($id, 'marketplace.order.action.') !== false;
        });

        $moduleToAction = [];
        $fs = new Filesystem();
        $basePath = $this->container->getParameter('kernel.project_dir') . '/var/workflows';
        $fs->mkdir($basePath);

        $progressBar = $io->createProgressBar(\count($actions));
        foreach ($actions as $servideId) {
            $progressBar->advance();
            $action = $this->container->get($servideId);
            $actionName = end(explode('\\', \get_class($action)));

            foreach ($action->getAllowedModules() as $moduleName) {
                if (!isset($moduleToAction[$moduleName->getValue()])) {
                    $moduleToAction[$moduleName->getValue()] = [];
                }

                $moduleToAction[$moduleName->getValue()][] = $actionName;
            }
        }
        $progressBar->clear();

        $progressBar = $io->createProgressBar(\count($workflows));
        foreach ($workflows as $className) {
            $progressBar->advance();
            $workflow = $className::load($workflowService);
            $workflowName = $workflow->getName()->getValue();
            $stack = $workflow->getStack();
            $modules = $stack->getModules();
            $workflowToActions = [];
            $moduleToModule = [];
            $lastModule = 0;
            $render = "digraph {\n";
            $render .= "    labelloc=\"t\";";
            $render .= "    label=\"{$workflowName}\";\n";
            $render .= "    completed [shape=circle style=filled width=0.05 label=\"completed\"];\n";

            foreach ($modules as $module) {
                $workflowToActions[$module->getName()->getValue()] = $moduleToAction[$module->getName()->getValue()];
                $moduleToModule[$lastModule] = $module->getName()->getValue();
                $lastModule = $module->getName()->getValue();
                $render .= "    \"{$lastModule}\" [shape=ellipse];\n";
            }

            $moduleToModule[$lastModule] = 'completed';

            foreach ($workflowToActions as $srcModuleName => $actions) {
                $destModuleName = $moduleToModule[$srcModuleName];

                foreach ($actions as $action) {
                    $render .= "    \"{$action}\" [shape=rectangle];\n";
                    $render .= "    \"{$srcModuleName}\" -> \"{$action}\";\n";
                    $render .= "    \"{$action}\" -> \"{$destModuleName}\";\n";
                }
            }

            $render .= "}\n";
            $outputPath = $basePath . '/' . $workflowName . '.dot';

            file_put_contents($outputPath, $render);
        }
        $progressBar->clear();
        $io->success(\dirname($outputPath));
    }

    private function getActionName($action)
    {
        return end(explode('\\', \get_class($action)));
    }
}
