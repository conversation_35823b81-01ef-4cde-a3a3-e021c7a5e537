<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\ContainerAwareInterface;
use Symfony\Component\DependencyInjection\ContainerAwareTrait;

class RefreshNewPromotionsInSearchCommand extends Command implements ContainerAwareInterface
{
    use ContainerAwareTrait;

    protected function configure()
    {
        $this
            ->setName('search:refresh-promotions')
            ->setDescription('Refresh in search products with promotions starting today or ending yesterday')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $productsIds = $this->container->get('marketplace.promotion.promotionservice')->getProductsToRefreshInSearch();
        $this->container->get('marketplace.search.product_index')->update(new \ArrayIterator($productsIds));

        $output->writeln('<info>Ok (' . \count($productsIds) . ' products)</info>');
    }
}
