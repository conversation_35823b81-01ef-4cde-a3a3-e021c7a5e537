<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Component\PurgeResources\PurgeResourceFactory;
use Wizacha\Component\PurgeResources\Report;

/**
 * Purge unused resource type given by arguments
 */
class PurgeResourcesCommand extends Command
{
    /** Allowed resources types to purge */
    protected const ALLOWED_TYPES = ['images'];

    /** Resource type argument key */
    protected const RESOURCES_TYPES_KEY = 'resourcesTypes';

    /** Dry run option key */
    protected const DRY_RUN_KEY = 'dry-run';

    /** Used to print time report */
    protected const TIME_FORMAT = 'G:i:s';

    /** @var PurgeResourceFactory */
    protected $purgeResourceFactory;

    /** @var Report */
    protected $report;

    public function __construct(PurgeResourceFactory $purgeResourceFactory, Report $report)
    {
        parent::__construct('purge:resources');

        $this->purgeResourceFactory = $purgeResourceFactory;
        $this->report = $report;
    }

    protected function configure(): void
    {
        $allowedTypesString = implode('","', static::ALLOWED_TYPES);

        $this
            ->setDescription('Purge unused resources')
            ->addArgument(
                static::RESOURCES_TYPES_KEY,
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Resource types to purge, if empty, all types will be purged',
                static::ALLOWED_TYPES
            )
            ->addOption(static::DRY_RUN_KEY, null, InputOption::VALUE_NONE, 'Run without deleting resources')
            ->setHelp(<<<EOT
The purge resource command can be used to delete unused files in the storage system.

Allowed resources types are "$allowedTypesString".

A file will be deleted if it's not found in DataBase and if its last modified date is older
than nb days defined in 'resources_purge_period.xxxx' parameter. (RESOURCES_PURGE_PERIOD_XXXX) in .env
EOT
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $startTime = new \DateTime();
        $inputResourceTypes = $input->getArgument(static::RESOURCES_TYPES_KEY);

        if (false === $this->verifyArguments($inputResourceTypes)) {
            $io->writeln("Invalid input type. Allowed types are [" . implode(', ', self::ALLOWED_TYPES) . "]");

            return 1;
        }

        $dryRun = true === $input->getOption(static::DRY_RUN_KEY);
        if ($dryRun) {
            $io->note('DRY RUN');
        }

        foreach ($inputResourceTypes as $resourceType) {
            $purgeResource = $this->purgeResourceFactory->get($resourceType);
            $purgeResource->setDryRun($dryRun);

            $io->section(
                "Start Purge "
                . $purgeResource->getType()
                . " at "
                . (new \DateTime())->format(static::TIME_FORMAT)
            );
            $purgeResource->process();
        }

        $io->section('Report');
        $io->writeln("Started at " . $startTime->format(static::TIME_FORMAT));
        $io->writeln("Finished at " . (new \DateTime())->format(static::TIME_FORMAT));
        $io->writeln('Nb files deleted : ' . $this->report->getNbItems());
        $io->writeln(
            'Total files size deleted : '
            . number_format($this->report->getFilesSize() / pow(1024, 2), 3, ',', ' ')
            . ' Mo'
        );

        return 0;
    }

    /**
     * Check if inputTypes are corrects
     */
    protected function verifyArguments(array $inputTypes): bool
    {
        foreach ($inputTypes as $inputType) {
            if (false === \in_array($inputType, self::ALLOWED_TYPES)) {
                return false;
            }
        }

        return true;
    }
}
