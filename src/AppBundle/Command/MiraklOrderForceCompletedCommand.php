<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\Input;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Marketplace\Payment\LemonWay\LemonWayConfig;
use Wizacha\OrderStatus;
use Wizacha\Order;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\Action\MarkAsDelivered;
use Wizacha\Marketplace\Order\Action\EndWithdrawalPeriod;
use Psr\Log\LoggerInterface;

class MiraklOrderForceCompletedCommand extends Command
{
    /** @var \Doctrine\DBAL\Connection */
    protected $connection;

    /** @var SymfonyStyle */
    protected $io;

    /** @var bool */
    protected $noFundsDispatch;

    /** @var \Wizacha\Marketplace\Order\OrderService */
    protected $orderService;

    /** @var \Wizacha\Marketplace\Order\Action\MarkAsDelivered */
    protected $markAsDelivered;

    /** @var \Wizacha\Marketplace\Order\Action\EndWithdrawalPeriod */
    protected $endWithDrawalPeriod;

    /** @var string|null */
    protected $filePath;

    /** @var \Psr\Log\LoggerInterface */
    protected $logger;

    public function __construct(
        LoggerInterface $logger,
        Connection $connection,
        OrderService $orderService,
        MarkAsDelivered $markAsDelivered,
        EndWithdrawalPeriod $endWithDrawalPeriod,
        $name = null
    ) {
        parent::__construct($name);

        $this->logger = $logger;
        $this->connection = $connection;
        $this->orderService = $orderService;
        $this->markAsDelivered = $markAsDelivered;
        $this->endWithDrawalPeriod = $endWithDrawalPeriod;
    }

    protected function configure()
    {
        $this
            ->setName('orders:force-completed')
            ->setDescription('Force an order to the completed status')
            ->addArgument(
                'filepath',
                InputArgument::REQUIRED,
                'Path of the file containing the ids, separated by commas'
            )
            ->addOption(
                'no-funds-dispatch',
                null,
                InputOption::VALUE_NONE,
                'Do not trigger a dispatch of the funds on the PSP'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->io = new SymfonyStyle($input, $output);
        $this->filePath = $input->getArgument('filepath');
        $this->getOptions($input);
        $processedOrderCount = 0;

        foreach ($this->getData() as $row) {
            $result = $this->processOrder((int) $row['order_id']);
            if (0 !== $result) {
                break;
            }
            $processedOrderCount++;
        }

        $this->io->success("$processedOrderCount orders were updated.");

        return 0;
    }

    protected function getData(): \Generator
    {
        $data = file_get_contents($this->filePath);
        if (false === $data) {
            throw new \RuntimeException("Impossible to read the file '{$this->filePath}'.");
        }

        $ids = explode(',', $data);

        foreach ($ids as $id) {
            yield ['order_id' => trim($id)];
        }
    }

    protected function processOrder(int $orderId): int
    {
        $this->io->title("Force order $orderId to completed "
            . ($this->noFundsDispatch ? "without" : "with") . " dispatching funds");

        $order = $this->orderService->getOrder($orderId);

        if ($order->getStatus()->getValue() !== OrderStatus::PROCESSED) {
            $this->io->error("Order is in wrong status: " . $order->getStatus() . '. Stopping processing.');

            return 1;
        }

        if ($this->noFundsDispatch) {
            $this->disableFundsDispatch($orderId);
            $this->io->text("Funds dispatch disabled");
        }

        if ($this->markAsDelivered->isAllowed($order)) {
            $this->markAsDelivered->execute($order);
            $this->io->text("Marked as delivered");
        } else {
            $this->io->warning("WARNING for order #{$orderId}: impossible to mark as delivered");
        }

        if ($this->endWithDrawalPeriod->isAllowed($order)) {
            $this->endWithDrawalPeriod->execute($order);
            $this->io->text("Withdrawal period ended");
        } else {
            $this->io->warning("WARNING for order #{$orderId}: impossible to end withdrawal period");
        }

        fn_change_order_status($order->getId(), OrderStatus::COMPLETED);
        $this->io->success("Order with id #{$order->getId()}: status set to completed");

        return 0;
    }

    /**
     * Empêche le dispatch sur cette commande en indiquant qu'il a déjà été fait
     * Preneur de suggestions pour améliorer ce code...
     * On ne sait pas si le paiement a été fait correctement ni de quelle manière
     */
    protected function disableFundsDispatch(int $orderId): void
    {
        $order = new Order($orderId);
        $order->setPaymentInformation('hipay_transfer_done', true);
        $order->setPaymentInformation('smoney_dispatch_funds_done', true);
        $order->setPaymentInformation('stripe_transfer_done', true);
        $order->setPaymentInformation(LemonWayConfig::LEMONWAY_DISPATCH_FUNDS_DONE, true);
        $order->setPaymentInformation('mangopay_transfer_done', true);
    }

    protected function getOptions(Input $input): void
    {
        $this->noFundsDispatch = $input->getOption('no-funds-dispatch');
    }
}
