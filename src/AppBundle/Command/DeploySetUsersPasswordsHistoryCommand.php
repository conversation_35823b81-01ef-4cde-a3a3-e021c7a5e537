<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Wizacha\AppBundle\Command\Traits\DeployPostActionsOnce;
use Wizacha\Marketplace\User\Event\UserPasswordChanged;
use Wizacha\Marketplace\User\UserService;

class DeploySetUsersPasswordsHistoryCommand extends Command implements DeployPostActionsInterface
{
    use DeployPostActionsOnce;

    private const VERSION_DATE = '2021-10-06 14:25:00';
    private const VERSION_NAME = 'Set users passwords history';
    private EventDispatcherInterface $eventDispatcher;
    private EntityManagerInterface $entityManager;
    private UserService $userService;
    private int $previousPasswordsDifferenceLimit;

    public function __construct(
        EventDispatcherInterface $eventDispatcher,
        EntityManagerInterface $entityManager,
        UserService $userService,
        int $previousPasswordsDifferenceLimit
    ) {
        parent::__construct('deploy:command:users-set-passwords-history');
        $this->eventDispatcher = $eventDispatcher;
        $this->entityManager = $entityManager;
        $this->userService = $userService;
        $this->previousPasswordsDifferenceLimit = $previousPasswordsDifferenceLimit;
    }

    public function getPriority(): int
    {
        return 100;
    }

    protected function configure(): void
    {
        $this->setDescription('Set users passwords history');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if (\is_int($this->previousPasswordsDifferenceLimit) === false
            || $this->previousPasswordsDifferenceLimit <= 0
        ) {
            return 0;
        }

        $io = new SymfonyStyle($input, $output);

        if ($this->hasRun() === true) {
            $io->writeln("<info>Command can be executed only once.</info>");
            return 0;
        }

        $users = $this->getConnection()->fetchAllAssociative('SELECT
                    u.*
                FROM
                    cscart_users u
                LEFT JOIN doctrine_user_passwords_history uph ON
                    u.user_id = uph.user_id
                WHERE uph.user_id is null
                ');

        if (\count($users) > 0) {
            foreach ($users as $user) {
                $this->eventDispatcher->dispatch(
                    new UserPasswordChanged(
                        $this->userService->get(\intval($user['user_id']))
                    ),
                    UserPasswordChanged::class
                );
            }
        }

        $io->success(\count($users) . " Users passwords history saved ...");

        $this->saveExecution();

        return 0;
    }

    protected function getConnection(): Connection
    {
        return $this->entityManager->getConnection();
    }

    protected function getDateTime(): \DateTimeInterface
    {
        return new \DateTimeImmutable(static::VERSION_DATE);
    }

    protected function getVersionName(): string
    {
        return static::VERSION_NAME;
    }
}
