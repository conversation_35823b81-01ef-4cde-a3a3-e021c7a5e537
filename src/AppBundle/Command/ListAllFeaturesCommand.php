<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Formatter\OutputFormatterStyle;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Wizacha\FeatureFlag\FeatureFlagService;

class ListAllFeaturesCommand extends Command
{
    private FeatureFlagService $featureFlagService;

    /**
     * ListAllFeaturesCommand constructor.
     * @param ContainerInterface $containerInterface
     */
    public function __construct(FeatureFlagService $featureFlagService)
    {
        parent::__construct();

        $this->featureFlagService = $featureFlagService;
    }

    protected function configure()
    {
        $this
            ->setName("debug:features")
            ->setDefinition([
                new InputArgument('name', InputArgument::OPTIONAL, 'A feature name'),
            ])
            ->setDescription("List all features from the kernel")
            ->setHelp(<<<'EOF'
The <info>%command.name%</info> displays the features' status:

<info>php %command.full_name%</info>

EOF
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $trueStyle  = new OutputFormatterStyle("green");
        $falseStyle = new OutputFormatterStyle("red");

        $output->getFormatter()->setStyle("true", $trueStyle);
        $output->getFormatter()->setStyle("false", $falseStyle);

        $table = new Table($output);
        $table->setHeaders([
            'Feature name',
            'Status',
        ]);

        $features = $this->featureFlagService->all();

        foreach ($features as $featureName => $featureValue) {
            if (\is_bool($featureValue)) {
                $featureValue = $featureValue ? 'true' : 'false';

                $result = '<' . $featureValue . '>' . $featureValue . '</>';
            } else {
                $result = "--not set--";
            }

            $features[$featureName] = [
                $featureName,
                $result,
            ];
        }

        ksort($features);

        $name = $input->getArgument('name');

        if ($name) {
            if (!isset($features[$name])) {
                $io = new SymfonyStyle($input, $output);
                $io->error(sprintf("Unable to find the feature %s", $name));
            } else {
                $table->setRows([
                    $features[$name],
                ]);
            }
        } else {
            $table->setRows($features);
        }

        $table->render();
    }
}
