<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\ConfirmationQuestion;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Process\Exception\ProcessFailedException;
use Symfony\Component\Process\Process;
use Tygh\Languages\Languages;
use Wizacha\Search\Engine\AlgoliaService;

class DropMarketplaceDatabaseCommand extends Command
{
    protected Connection $connection;
    protected bool $sandbox;

    public function __construct(Connection $connection, bool $sandbox)
    {
        parent::__construct();

        $this->connection = $connection;
        $this->sandbox = $sandbox;
    }

    protected function configure(): void
    {
        $this
            ->setName('database:marketplace:drop')
            ->setDescription("Drop all tables from the marketplace database.")
            ->addOption("dry-run", null, InputOption::VALUE_NONE, "Dry run")
            ->setHelp(implode("\n", [
                "Run this command in a sandbox environment to drop all tables from the database.",
                "See also ./bin/database-marketplace-reset.sh for a complete reset of the database."
            ]))
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $dryRun = $input->getOption("dry-run");

        if (false === $this->sandbox) {
            throw new \RuntimeException("Not a sandbox environment");
        }

        $io->title("Drop all tables");

        if ($dryRun) {
            $io->warning("Dry run");
        }

        if (false === $dryRun && false === $io->confirm("This is a destructive operation. Confirm?")) {
            $io->warning("Aborted");
            return 0;
        }

        $this->connection->exec("SET FOREIGN_KEY_CHECKS = 0");

        $tables = $this->connection->prepare("SHOW TABLES");
        $tables->execute();

        foreach ($tables->fetchAll() as $table) {
            // En fonction du nom de la BDD suivant les env
            // Local => $table = ['tables_of_wizaplace' => 'tableName']
            // Platform => $table = ['tables_of_marketplace-database' => 'tableName']
            // AWS => $table = ['tables_of_marketplace' => 'tableName']
            $table = array_values($table)[0];

            $io->text("Dropping table " . $table);

            if ($dryRun) {
                continue;
            }

            $this->connection->exec("DROP TABLE $table");

            $io->comment("Table dropped");
        }

        $this->connection->exec("SET FOREIGN_KEY_CHECKS = 1");

        $io->success("All tables dropped.");

        $io->text("You should execute the following commands to proceed:");
        $io->listing([
            'vendor/bin/phinx migrate',
            'bin/console products:cache:refresh',
            'bin/console deploy:command:company_email_settings',
            'bin/console redis:flushall',
            'bin/console cache:clear',
        ]);

        return 0;
    }
}
