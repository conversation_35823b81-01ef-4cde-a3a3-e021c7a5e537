<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Marketplace\Order\Action\Action;
use Wizacha\Marketplace\Order\OrderService;

class OrderListActionsCommand extends Command
{
    private OrderService $orderService;
    /** @var Action[]  */
    private iterable $actions;

    /** @param Action[] $actions */
    public function __construct(iterable $actions, OrderService $orderService)
    {
        parent::__construct();

        $this->actions = $actions;
        $this->orderService = $orderService;
    }

    protected function configure(): void
    {
        $this
            ->setName('orders:list-actions')
            ->setDescription('List all action possible (on an Order if provided)')
            ->addArgument('orderId', InputArgument::OPTIONAL, 'The id of the Order')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $orderId = $input->getArgument('orderId');

        if ($orderId !== null) {
            $orders = $this->orderService->getChildOrders($orderId);

            foreach ($orders as $order) {
                $output->writeln(sprintf('<info>Possible action on order id %d </info>', $order->getId()));
                /** @var Action $action */
                foreach ($this->actions as $action) {
                    if ($action->isAllowed($order)) {
                        $output->writeln($action->getActionName()->getValue());
                    }
                }
            }
        } else {
            $output->writeln('<info>Liste of action : </info>');
            foreach ($this->actions as $action) {
                $output->writeln($action->getActionName()->getValue());
            }
        }
    }
}
