<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Exim\CompanyTokenCsv;

class ExportCompanyTokenCommand extends Command
{
    protected static $defaultName = 'company:token:export';
    /** @var CompanyTokenCsv */
    protected $companyTokenCsv;

    public function __construct(CompanyTokenCsv $companyTokenCsv)
    {
        parent::__construct();

        $this->companyTokenCsv = $companyTokenCsv;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('List companies tokens')
            ->setHelp('Output to php://stdout in CSV format')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->companyTokenCsv->drawCsv();

        return 0;
    }
}
