<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\Question;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Marketplace\Payment\LemonWay\LemonWayConfig;
use Wizacha\Order as WizachaOrder;

class DispatchFundsCommand extends Command implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    private OrderService $orderService;

    public function __construct(OrderService $orderService)
    {
        parent::__construct();

        $this->orderService = $orderService;
    }

    protected function configure()
    {
        $this
            ->setName('orders:dispatch-funds')
            ->setDescription('Dispatch funds for completed orders')
            ->addOption(
                'order-id',
                'o',
                (
                    InputOption::VALUE_REQUIRED
                    | InputOption::VALUE_IS_ARRAY
                ),
                'dispatch funds on specific orderIds'
            )
            ->addOption(
                'force',
                'f',
                InputOption::VALUE_NONE,
                <<<DOC
                ⚠ BEWARE: will produce real money transfer (work only with order-id option)
                Faking order status as PROCESSED, reset paymentInformation dispatch info
                DOC
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Orders dispatch funds');

        $orderIds = $input->getOption('order-id')
            ?: $this->getPendingDispatchFundsOrderIds()
        ;

        $total = \count($orderIds);

        $useForce = $input->getOption('force');

        // manual action security
        if ($input->getOption('order-id')) {
            $io->warning('!! YOU SHOULD NEVER RUN THIS COMMAND ON PROD !!');

            $really = (
                'REALLY' === $io->askQuestion(
                    new Question(
                        \sprintf(
                            (
                                'Answer REALLY, if you are REALLY sure to dispatch'
                                . ' %d order%s'
                            ),
                            $total,
                            $total ? 's' : ''
                        )
                    )
                )
            );

            if (false === $really) {
                return 0;
            }
        } elseif (true === $useForce) {
            $logger->error('Never use force without order-id option');

            return 1;
        }

        // cette commande ne devrait pas utiliser la fonction de mise à jour
        // du statut de commande car il s'agit d'un système déprécié, mais
        // il s'agit encore du seul moyen pour lancer la répartition des
        // fonds sur une commande. A terme il faudra appeler directement
        // l'action DispatchFunds sur la commande et ce sera à elle de faire
        // le nécessaire pour la répartition. (actuellement cette action est
        // appelée directement dans les PSP afin de faire avancer le
        // workflow, à terme il faudra donc retirer ces appels des PSP)
        $results = \array_map(
            function (int $orderId) use ($useForce): bool {
                if (true === $useForce) {
                    $this->resetDispatchPaymentInformation($orderId);
                }

                $success = fn_change_order_status(
                    $orderId,
                    OrderStatus::COMPLETED()->getValue(),
                    $useForce ? OrderStatus::PROCESSED()->getValue()  : ''
                );

                $this->logger->{$success ? 'notice' : 'error'}(
                    'Order dispatch funds',
                    ['id' => $orderId]
                );

                return $success;
            },
            $orderIds
        );

        $processed = \count(
            \array_filter(
                $results
            )
        );

        $hasErrors = $processed != $total;

        $info = \sprintf(
            '%s/%s order%s processed!',
            $processed,
            $total,
            $total ? 's' : ''
        );
        $hasErrors == true ? $logger->error($info) : $io->success($info);

        return (int) $hasErrors;
    }

    protected function resetDispatchPaymentInformation(int $orderId): void
    {
        $dispatchKeys = [
            // Dispatch vendor
            LemonWayConfig::LEMONWAY_DISPATCH_FUNDS_DONE,
            LemonWayConfig::LEMONWAY_TRANSFER_VENDOR_DONE,
            'hipay_transfer_vendor_done',
            'stripe_charge_id',
            'stripe_transfer_done',
            // Dispatch commission
            LemonWayConfig::LEMONWAY_TRANSFER_COM_DONE,
            'hipay_transfer_com_done',

        ];

        $order = new WizachaOrder($orderId);

        $paymentInformation = $order->getPaymentInformation();

        foreach ($dispatchKeys as $dispatchKey) {
            if (true === isset($paymentInformation[$dispatchKey])) {
                $order->setPaymentInformation($dispatchKey, false);
            }
        }
    }

    protected function getPendingDispatchFundsOrderIds(): array
    {
        return \array_map(
            function (Order $order): int {
                return $order->getId();
            },
            $this->orderService->getPendingDispatchFundsOrders()
        );
    }
}
