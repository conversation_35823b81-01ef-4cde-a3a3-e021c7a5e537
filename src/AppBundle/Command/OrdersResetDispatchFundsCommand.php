<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\Question;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Marketplace\Order\OrderService;

class OrdersResetDispatchFundsCommand extends Command
{
    protected static $defaultName = 'orders:reset:dispatch-funds';

    /** @var Connection $connection */
    protected $connection;

    /** @var OrderService $orderService */
    protected $orderService;

    public function __construct(
        Connection $connection,
        OrderService $orderService
    ) {
        parent::__construct();

        $this->connection = $connection;
        $this->orderService = $orderService;
    }

    protected function configure()
    {
        $this
            ->setDescription(
                'reset fund dispatch'
            )
            ->addArgument(
                'order_ids',
                InputArgument::IS_ARRAY,
                'Order ids'
            )
            ->addOption(
                'info',
                'i',
                InputOption::VALUE_NONE,
                'extended info'
            )
        ;
    }

    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ) {
        $io = new SymfonyStyle($input, $output);
        $io->title('Orders reset dispatch funds status');

        $orderIds = $input->getArgument('order_ids');

        if ($input->getOption('info')) {
            $io->write(
                \json_encode(
                    \array_map(
                        function (string $id) {
                            return $this->orderService->getOrder(\intval($id))->expose();
                        },
                        $orderIds
                    ),
                    (
                        \JSON_PRETTY_PRINT
                        | \JSON_UNESCAPED_UNICODE
                    )
                )
            );
        }

        $this->connection->beginTransaction();

        $rows = $this->connection->executeUpdate(
            'UPDATE
                cscart_orders
            SET
                funds_dispatched = null
            WHERE
                order_id IN (:orderIds)
            ',
            [
                'orderIds' => \array_map(
                    function (string $id): int {
                        return \intval($id);
                    },
                    $orderIds
                ),
            ],
            ['orderIds' => Connection::PARAM_INT_ARRAY]
        );

        if (0 === $rows) {
            $io->success('Nothing to update');

            return 0;
        }

        $io->warning('!! YOU SHOULD NEVER RUN THIS COMMAND ON PROD !!');

        $answer = $io->askQuestion(
            new Question(
                \sprintf(
                    (
                            'Answer REALLY, if you are REALLY sure to update'
                            . ' %d/%d order%s'
                        ),
                    $rows,
                    \count($orderIds),
                    $rows > 1 ? 's' : ''
                )
            )
        );

        if ('REALLY' === $answer) {
            $this->connection->commit();
            $io->success('Updated');
        } else {
            $this->connection->rollBack();
            $io->note('Aborted');
        }
    }
}
