<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Exim\MiraklShipping\MiraklShippingsCsvProcessor;

class ImportMiraklShippingsCommand extends Command
{
    public const DEFAULT_PATH = 'php://stdin';

    protected static $defaultName = 'import:mirakl:shippings';

    /** @var MiraklShippingsCsvProcessor */
    protected $miraklShippingCsvProcessor;

    public function __construct(MiraklShippingsCsvProcessor $miraklShippingsCsvProcessor)
    {
        parent::__construct();

        $this->miraklShippingCsvProcessor = $miraklShippingsCsvProcessor;
    }

    protected function configure()
    {
        $this
            ->setDescription(
                'Import ETL mirakl shippings configuration csv'
            )
            ->addArgument(
                'path',
                InputArgument::OPTIONAL,
                'optional file path, use standard input (php://stdin) if empty'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $miraklShippingProcessGenerator = $this->miraklShippingCsvProcessor->process(
            $input->getArgument('path') ?? static::DEFAULT_PATH
        );

        foreach ($miraklShippingProcessGenerator as $info) {
            $output->writeln($info);
        }

        return 0;
    }
}
