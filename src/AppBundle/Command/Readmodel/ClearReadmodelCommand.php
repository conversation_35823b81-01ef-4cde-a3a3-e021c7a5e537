<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright Copyright (c) Wizaplace
 * @license   Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command\Readmodel;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Marketplace\ReadModel\ReadModelService;

class ClearReadmodelCommand extends Command
{
    /** @var ReadModelService */
    protected $readModelService;

    public function __construct(ReadModelService $readModelService)
    {
        parent::__construct();

        $this->readModelService = $readModelService;
    }

    protected function configure(): void
    {
        $this
            ->setName('readmodel:clear')
            ->setDescription("Clear the product read models");
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title($this->getDescription());

        if (false === $io->confirm("This is a destructive operation. Do you confirm?")) {
            $io->warning("Cancelled.");

            return 0;
        }

        $this->readModelService->clear();

        $io->success("Readmodel cleared successfully.");

        return 0;
    }
}
