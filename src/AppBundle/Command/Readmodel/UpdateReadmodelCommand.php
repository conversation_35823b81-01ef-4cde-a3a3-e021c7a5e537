<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright Copyright (c) Wizaplace
 * @license   Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command\Readmodel;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Marketplace\ReadModel\ProductProjector;

class UpdateReadmodelCommand extends Command
{
    /** @var ProductProjector */
    protected $productProjector;

    public function __construct(
        ProductProjector $productProjector
    ) {
        parent::__construct();

        $this->productProjector = $productProjector;
    }

    protected function configure(): void
    {
        $this
            ->setName('readmodel:update')
            ->setDescription("Update products in the read model")
            ->addArgument(
                'ids',
                InputArgument::IS_ARRAY,
                'Id of product to update (separate multiple values with a space)'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        foreach ($input->getArgument('ids') as $id) {
            $this->productProjector->rebuildReadmodel($id);

            $io->success(
                "Id: $id, read model updated"
            );
        }

        return 0;
    }
}
