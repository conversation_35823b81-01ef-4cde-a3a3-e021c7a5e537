<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright Copyright (c) Wizaplace
 * @license   Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command\Readmodel;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Statement;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Async\Debouncer\DebouncerService;
use Wizacha\Marketplace\ReadModel\ProductProjector;

class CreateReadmodelCommand extends Command
{
    private Connection $connection;
    private ProductProjector $productProjector;
    private DebouncerService $debouncerService;

    public function __construct(
        Connection $connection,
        ProductProjector $productProjector,
        DebouncerService $debouncerService
    ) {
        parent::__construct();

        $this->connection = $connection;
        $this->productProjector = $productProjector;
        $this->debouncerService = $debouncerService;
    }

    protected function configure(): void
    {
        $this
            ->setName('readmodel:create')
            ->setDescription("Update all the product read models")
            ->addOption('batch', 'b', InputOption::VALUE_REQUIRED, 'Size of the batch', 100)
            ->addOption('offset', 'o', InputOption::VALUE_REQUIRED, 'Offset of the update', 0)
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title($this->getDescription());

        $batchSize = $input->getOption('batch');
        if (!is_numeric($batchSize)) {
            throw new \RuntimeException("Batch option must be numeric");
        }
        $batchSize = (int) $batchSize;

        $offset = $input->getOption('offset');
        if (!is_numeric($offset)) {
            throw new \RuntimeException("Offset option must be numeric");
        }
        $offset = (int) $offset;

        $statement = $this->connection->prepare(
            <<<SQL
            SELECT
                product_id
            FROM
                cscart_products
            SQL
        );
        $statement->execute();

        $progressBar = $io->createProgressBar();
        $iterator = $progressBar->iterate(
            $statement->iterateColumn(),
            $statement->rowCount()
        );

        foreach ($iterator as $id) {
            $this->productProjector->rebuildReadmodel(
                $id,
                false
            );
        }
        $progressBar->finish();
        $progressBar->clear();

        $io->success("All done. The readmodel is updated asynchronously, so it may take a while before the update is complete.");
        $io->note('commit in progress ...');

        return 0;
    }
}
