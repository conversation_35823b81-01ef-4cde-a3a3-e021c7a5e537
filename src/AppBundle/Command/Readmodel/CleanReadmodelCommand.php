<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright Copyright (c) Wizaplace
 * @license   Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command\Readmodel;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Driver\Statement;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Marketplace\ReadModel\ProductProjector;

class CleanReadmodelCommand extends Command
{
    protected static $defaultName = 'readmodel:orphan:clean';

    protected ProductProjector $productProjector;
    protected Connection $connection;
    protected LoggerInterface $logger;

    private SymfonyStyle $io;
    private bool $isDryRun;

    public function __construct(
        ProductProjector $productProjector,
        Connection $connection,
        LoggerInterface $logger
    ) {
        parent::__construct();

        $this->productProjector = $productProjector;
        $this->connection = $connection;
        $this->logger = $logger;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Remove orphan readmodel entries')
            ->addOption(
                'dry-run',
                null,
                InputOption::VALUE_NONE
            )
            ->addOption(
                'force',
                'f',
                InputOption::VALUE_NONE
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->isDryRun = $input->getOption('dry-run');

        $this->io = new SymfonyStyle($input, $output);
        $this->io->title('Clean orphan readmodels');

        if (false === $this->isDryRun
            && false === $input->getOption('force')
            && false === $this->io->confirm("This is a destructive operation. Do you confirm?", false)
        ) {
            $this->io->warning("Cancelled.");

            return 1;
        }

        $orphanCount = \array_sum(
            [
                $this->process(
                    'Missing product',
                    $this->getMissingProductStatement()
                ),
                $this->process(
                    'Missing MVP',
                    $this->getMissingMvpStatement()
                ),
                $this->process(
                    'Removed MVP link',
                    $this->getRemovedMvpLinkStatement()
                )
            ]
        );

        if ($orphanCount > 0) {
            $this->io->warning(
                \sprintf(
                    '%s orphan(s) found',
                    $orphanCount
                )
            );
        } else {
            $this->io->success(
                'No orphan found'
            );
        }

        return 0;
    }

    private function process(string $message, Statement $statement): int
    {

        $this->io->section($message);

        $statement->execute();

        $rowCount = $statement->rowCount();

        if ($rowCount) {
            $this->io->progressStart(
                $rowCount
            );

            while ($id = $statement->fetchColumn()) {
                $this->io->progressAdvance();

                if (false === $this->isDryRun) {
                    $this->productProjector->rebuildReadmodel($id);
                    $this->logger->error(
                        'Orphan readmodel found',
                        [
                            'message' => $message,
                            'id' => $id,
                        ]
                    );
                }
            }

            $this->io->progressFinish();
        }

        return $rowCount;
    }

    private function getMissingProductStatement(): Statement
    {
        return $this->connection->prepare(
            <<<SQL
            SELECT
                pc.id
            FROM
                product_catalog pc
                LEFT JOIN cscart_products cp
                    ON cp.product_id = pc.id
            WHERE
                pc.id REGEXP '^-?[0-9]+$'
                AND cp.product_id IS NULL
            SQL
        );
    }

    private function getMissingMvpStatement(): Statement
    {
        return $this->connection->prepare(<<<SQL
            SELECT
                pc.id
            FROM
                product_catalog pc
            WHERE
                pc.id NOT REGEXP '^-?[0-9]+$'
                AND pc.id NOT IN(
                    SELECT
                        CONVERT(dmvp.id USING utf8)
                    FROM doctrine_multi_vendor_product dmvp
                )
            SQL
        );
    }

    private function getRemovedMvpLinkStatement(): Statement
    {
        return $this->connection->prepare(
            <<<SQL
            SELECT
                pc.id
            FROM
                product_catalog pc
            WHERE
                pc.id IN(
                    SELECT
                        CONVERT(dmvp.id USING utf8)
                    FROM
                        doctrine_multi_vendor_product dmvp
                        LEFT JOIN doctrine_multi_vendor_product_link dmvpl
                            ON dmvp.id = dmvpl.multi_vendor_product_id
                    WHERE
                        dmvpl.multi_vendor_product_id IS NULL
                )
            SQL
        );
    }
}
