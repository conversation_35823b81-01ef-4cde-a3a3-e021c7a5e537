<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright Copyright (c) Wizaplace
 * @license   Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command\Readmodel;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Marketplace\Catalog\Product\ProductService;

class DumpReadmodelCommand extends Command
{
    /** @var ProductService */
    private $productService;

    public function __construct(ProductService $productService)
    {
        parent::__construct();

        $this->productService = $productService;
    }

    protected function configure(): void
    {
        $this
            ->setName('readmodel:dump')
            ->setDescription("Dump the read model of a specific product or MVP")
            ->addArgument("productId", InputArgument::REQUIRED, "Id of the product to dump");
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $productId = $input->getArgument("productId");

        $output->writeln(
            json_encode(
                $this->productService->getProduct($productId),
                JSON_PRESERVE_ZERO_FRACTION | JSON_PRETTY_PRINT
            )
        );

        return 0;
    }
}
