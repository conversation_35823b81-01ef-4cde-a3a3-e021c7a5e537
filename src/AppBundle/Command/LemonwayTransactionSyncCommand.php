<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Marketplace\Payment\LemonWay\Exception\LemonWayException;
use Wizacha\Marketplace\Payment\LemonWay\LemonWayTransactionSyncService;
use Wizacha\Marketplace\Transaction\TransactionType;

class LemonwayTransactionSyncCommand extends Command
{
    public static $defaultName = 'lemonway:transaction:sync';

    private const WIZAPLACE_ORDER_ID = 'orderId';
    private const WIZAPLACE_TRANSACTION_TYPE = 'transactionType';
    private const LEMONWAY_TRANSACTION_REFERENCE = 'transactionReference';

    private LemonWayTransactionSyncService $lemonwayTransactionSyncService;

    public function __construct(LemonWayTransactionSyncService $lemonway)
    {
        $this->lemonwayTransactionSyncService = $lemonway;
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->setDescription(
                'Synchronize Wizaplace Transaction status with Lemonway status'
            )
            ->addArgument(
                static::WIZAPLACE_ORDER_ID,
                InputArgument::REQUIRED,
                'Wizaplace order id'
            )
            ->addArgument(
                static::WIZAPLACE_TRANSACTION_TYPE,
                InputArgument::REQUIRED,
                \sprintf(
                    'Wizapace transaction type (%s)',
                    \implode(', ', TransactionType::keys())
                )
            )
            ->addArgument(
                static::LEMONWAY_TRANSACTION_REFERENCE,
                InputArgument::REQUIRED,
                'Lemonway transaction id'
            )
        ;
    }

    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ): int {
        $io = new SymfonyStyle(
            $input,
            $output
        );

        $orderId = (int) $input->getArgument(static::WIZAPLACE_ORDER_ID);
        $transactionType = new TransactionType(
            $input->getArgument(static::WIZAPLACE_TRANSACTION_TYPE)
        );
        $transactionRef = $input->getArgument(static::LEMONWAY_TRANSACTION_REFERENCE);

        if (false === $this->lemonwayTransactionSyncService->isConfigured()) {
            throw new LemonWayException('Lemonway is not configured');
        }
        $io->title($this->getDescription());

        $transaction = $this->lemonwayTransactionSyncService->syncTransactionStatus(
            $transactionType,
            $orderId,
            $transactionRef
        );

        $io->success('Transaction synced');
        $transaction->expose();

        return 0;
    }
}
