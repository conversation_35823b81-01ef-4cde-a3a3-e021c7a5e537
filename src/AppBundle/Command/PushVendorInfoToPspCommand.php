<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\EventDispatcher\EventDispatcher;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Wizacha\Events\IterableEvent;
use Wizacha\Marketplace\Company\CompanyEvents;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\User\UserService;
use Wizacha\Marketplace\User\UserStatus;
use Wizacha\Marketplace\User\UserType;

class PushVendorInfoToPspCommand extends Command
{
    /** @var UserService */
    private $userService;

    /** @var EventDispatcher */
    private $dispatcher;

    public function __construct(UserService $userService, EventDispatcherInterface $dispatcher)
    {
        $this->userService = $userService;
        $this->dispatcher  = $dispatcher;

        parent::__construct(null);
    }

    protected function configure()
    {
        $this
            ->setName('psp:push:vendor')
            ->setDescription("Push vendor's information to the PSP")
            ->addOption('id', 'id', InputOption::VALUE_REQUIRED | InputOption::VALUE_IS_ARRAY, "Vendor'id to push to the PSP", [])
            ->setHelp(<<<'EOF'
In some case it's usefull to push vendor's information to a PSP.
The <info>%command.name%</info> allow to do that

Example running
  <info>php %command.full_name% --id=1</info>
  <info>php %command.full_name% --id=1 --id=123</info>
EOF
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $ids = $input->getOption("id");

        if (empty($ids)) {
            $output->writeln("<error>The option --id is required</error>");
            exit(1);
        }

        $companyIds = [];
        foreach ($ids as $id) {
            $user = null;
            try {
                $user = $this->userService->get($id);

                if ($user->getUserType() !== UserType::VENDOR()->getValue() && \is_null($user->getCompanyId())) {
                    $output->writeln("<error>The user #{$id} is not a vendor</error>");
                    continue;
                }
                if ($user->getStatus() !== UserStatus::ACTIVE()->getValue()) {
                    $output->writeln("<error>The user #{$id} is activated</error>");
                    continue;
                }

                $companyIds[] = $user->getCompanyId();
            } catch (NotFound $e) {
                $output->writeln("<error>Unable to find the user #{$id}</error>");
                continue;
            }
        }

        if (!empty($companyIds)) {
            $this->dispatcher->dispatch(IterableEvent::fromArray($companyIds), CompanyEvents::SEND_TO_PSP);
        }
    }
}
