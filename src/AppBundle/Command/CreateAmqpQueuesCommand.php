<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use PhpAmqpLib\Wire\AMQPTable;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\ContainerAwareInterface;
use Symfony\Component\DependencyInjection\ContainerAwareTrait;

class CreateAmqpQueuesCommand extends Command implements ContainerAwareInterface
{
    use ContainerAwareTrait;

    protected function configure()
    {
        $this
            ->setName('amqp:create-queues')
            ->setDescription('Create AMQP queues')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if ($this->container->getParameter('queue.type') !== \Wizacha\Async\Config::TYPE_AMQP) {
            $output->writeln('<info>The queue type is not AMQP</info>');

            return 0;
        }

        $amqp = $this->container->get('marketplace.amqp.connection')->channel();
        $queues = $this->container->getParameter('marketplace.queue.config');

        $amqp->exchange_declare('wizaplace', 'direct');

        // This queue should not be consumed. It's only here to delay messages after first fail
        unset($queues['wait_retry']);// unset the queue if created in parameters.default.yml
        $amqp->queue_declare('wait_retry', false, true, false, true, false, new AMQPTable([
            'x-dead-letter-exchange' => 'wizaplace',
            'x-dead-letter-routing-key' => 'retry',
            'x-message-ttl' => 300000, // After 5 min, message goes to 'retry' queue
        ]));
        $amqp->queue_bind('wait_retry', 'wizaplace', 'wait_retry');

        // This queue should not be consumed. It's only here to save failed messages
        unset($queues['dead_letter']);// unset the queue if created in parameters.default.yml
        $amqp->queue_declare('dead_letter', false, true, false, true, false, new AMQPTable([
            'x-message-ttl' => *********, // Clean dead_letter after 1 week
        ]));
        $amqp->queue_bind('dead_letter', 'wizaplace', 'dead_letter');

        // This queue should be consumed to handle messages which fails once. If they fail again, they go to dead_letter
        unset($queues['retry']);// unset the queue if created in parameters.default.yml
        $amqp->queue_declare('retry', false, true, false, true, false, new AMQPTable([
            'x-dead-letter-exchange' => 'wizaplace',
            'x-dead-letter-routing-key' => 'dead_letter',
        ]));
        $amqp->queue_bind('retry', 'wizaplace', 'retry');

        foreach (array_keys($queues) as $queue) {
            $amqp->queue_declare($queue, false, true, false, true, false, new AMQPTable([
                'x-dead-letter-exchange' => 'wizaplace',
                'x-dead-letter-routing-key' => 'wait_retry',
            ]));
            $amqp->queue_bind($queue, 'wizaplace', $queue);
            $output->writeln('Created queue: ' . $queue);
        }

        $amqp->close();

        $output->writeln('<info>Queues created!</info>');

        return 0;
    }
}
