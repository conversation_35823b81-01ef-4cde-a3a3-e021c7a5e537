<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\AppBundle\Command\Traits\DeployPostActionsOnce;
use Wizacha\Marketplace\Basket\Exception\MissingPaymentException;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\Processor\AbstractProcessor;
use Wizacha\Marketplace\Payment\Processor\Exception\NoValidProcessorException;
use Wizacha\Marketplace\Payment\Processor\LemonWay;
use Wizacha\Marketplace\Payment\Processor\MangoPay;
use Wizacha\Marketplace\Payment\Processor\PayoutService;
use Wizacha\Marketplace\Payment\Processor\Stripe;
use Wizacha\Marketplace\Transaction\Transaction;
use Wizacha\Marketplace\Transaction\TransactionRepository;
use Wizacha\Marketplace\Transaction\TransactionType;
use Wizacha\Order as WizachaOrder;

class DeploySetTransactionOriginAndDestinationCommand extends Command
{
    use DeployPostActionsOnce;

    public static $defaultName = 'fix:transactions:set-origin-and-destination';

    private const BULK = 1024;

    private TransactionRepository $transactionRepository;
    private CompanyService $companyService;
    private LemonWay $lemonWay;
    private MangoPay $mangoPay;
    private PayoutService $payoutService;
    private const VERSION_DATE = '2021-08-11 14:25:00';
    private const VERSION_NAME = 'Set transaction origin and destination';

    /** @var iterable|AbstractProcessor[] */
    private iterable $processors;

    public function __construct(
        TransactionRepository $transactionRepository,
        CompanyService $companyService,
        MangoPay $mangoPay,
        LemonWay $lemonWay,
        EntityManagerInterface $entityManager,
        PayoutService $payoutService,
        iterable $processors
    ) {
        parent::__construct();
        $this->transactionRepository = $transactionRepository;
        $this->companyService = $companyService;
        $this->mangoPay = $mangoPay;
        $this->lemonWay = $lemonWay;
        $this->entityManager = $entityManager;
        $this->payoutService = $payoutService;
        $this->processors = $processors;
    }

    public function getPriority(): int
    {
        return 100;
    }

    protected function configure(): void
    {
        $this->setDescription('Set transaction origin and destination for historic transaction');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        //check that the command has been executed before
        if ($this->hasRun() === true) {
            $io->warning('command can be executed only once.');

            return 0;
        }


        try {
            $currentProcessor = $this->payoutService->getFirstValidProcessor();
        } catch (NoValidProcessorException $exception) {
            $io->warning('No PSP configured');

            return 0;
        }

        $io->section('fix Transaction Without Origin Filtered By ProcessorName');
        $transactionsWithoutOriginFixed = $this->fixTransactionsWithoutOrigin($currentProcessor, $io);

        $io->section('fix Transaction Without Destination Filtered By ProcessorName');
        $transactionsWithoutDestinationFixed = $this->fixTransactionWithoutDestination($currentProcessor, $io);

        $io->success(
            \sprintf(
                '%d origin fields and %d destination fields are filled.',
                $transactionsWithoutOriginFixed,
                $transactionsWithoutDestinationFixed
            )
        );
        $this->saveExecution();

        return 0;
    }

    private function fixTransactionsWithoutOrigin(AbstractProcessor $processor, SymfonyStyle $io): int
    {
        $transactionsWithoutOrigin = $this->transactionRepository->findTransactionWithoutOriginFilteredByProcessorName(
            $processor->getName()->getValue()
        );
        $progressBar = $io->createProgressBar();

        $nbFixedTransactions = 0;

        foreach ($transactionsWithoutOrigin as $transactions) {
            foreach ($transactions as $transaction) {
                $origin = $this->getOriginByTransaction($processor, $transaction);

                if ($origin !== '') {
                    $transaction->setOrigin($origin);
                    $this->transactionRepository->persist($transaction);
                    $nbFixedTransactions++;
                }

                // Delete hipay transaction without origin (company's hipay_id === NULL)
                if (PaymentProcessorName::HIPAY()->equals($processor->getName())
                    && TransactionType::VENDOR_WITHDRAWAL()->equals($transaction->getType())  === true
                    && $origin === ''
                ) {
                    $this->transactionRepository->deleteTransaction($transaction);
                    $nbFixedTransactions++;
                }

                if (0 === $nbFixedTransactions % static::BULK) {
                    $this->transactionRepository->commit();
                }

                $progressBar->advance();
            }
        }

        $this->transactionRepository->commit();
        $progressBar->clear();

        return $nbFixedTransactions;
    }

    private function fixTransactionWithoutDestination(AbstractProcessor $processor, SymfonyStyle $io): int
    {
        $transactionsWithoutDestination = $this->transactionRepository->findTransactionWithoutDestinationFilteredByProcessorName(
            $processor->getName()->getValue()
        );

        $nbFixedTransactions = 0;

        $progressBar = $io->createProgressBar();

        foreach ($transactionsWithoutDestination as $transactions) {
            foreach ($transactions as $transaction) {
                $destination = $this->getDestinationByTransaction($processor, $transaction);

                if ($destination !== '') {
                    $transaction->setDestination($destination);
                    $this->transactionRepository->persist($transaction);
                    $nbFixedTransactions++;
                }

                if (0 === $nbFixedTransactions % static::BULK) {
                    $this->transactionRepository->commit();
                }

                $progressBar->advance();
            }
        }

        $this->transactionRepository->commit();
        $progressBar->clear();

        return $nbFixedTransactions;
    }

    private function getOriginByTransaction(AbstractProcessor $processor, Transaction $transaction): string
    {
        $origin = '';
        switch ($processor->getName()->getValue()) {
            case PaymentProcessorName::STRIPE()->getValue():
                $origin = Stripe::ORIGIN_TRANSACTION_PAYMENT;
                break;
            case PaymentProcessorName::SMONEY()->getValue():
                // Shame! Shame! CompanyId can be null...
                $origin = 'payout-company-' . $transaction->getCompanyId();
                break;
            case PaymentProcessorName::MANGOPAY()->getValue():
                if (null !== $transaction->getOrderId()) {
                    if (TransactionType::VENDOR_WITHDRAWAL()->equals($transaction->getType()) === true) {
                        $origin = $this->mangoPay->getUserWalletId($transaction->getOrderId());
                    } elseif (TransactionType::DISPATCH_FUNDS_TRANSFER_VENDOR()->equals($transaction->getType()) === true
                        || TransactionType::DISPATCH_FUNDS_TRANSFER_COMMISSION()->equals($transaction->getType()) === true
                    ) {
                        $origin = $this->mangoPay->getCreditedWalletId($transaction->getOrderId());
                    }
                }

                break;
            case PaymentProcessorName::LEMONWAY()->getValue():
                if (null !== $transaction->getOrderId()) {
                    try {
                        $origin = $this->lemonWay->getUserWalletByOrderId($transaction->getOrderId());
                    } catch (MissingPaymentException $exception) {
                        // We ignore this entry
                    }
                }

                break;
            case PaymentProcessorName::HIPAY()->getValue():
                $origin = $this->companyService->getCompanyHipayId($transaction->getCompanyId());
                break;
            default:
                break;
        }

        return $origin;
    }

    private function getDestinationByTransaction(AbstractProcessor $processor, Transaction $transaction): string
    {
        $destination = '';
        switch ($processor->getName()->getValue()) {
            case PaymentProcessorName::STRIPE()->getValue():
                $destination = $this->companyService->getCompanyStripeId($transaction->getCompanyId());
                break;
            case PaymentProcessorName::SMONEY()->getValue():
            case PaymentProcessorName::HIPAY()->getValue():
                $destination = $this->companyService->get($transaction->getCompanyId())->getName() . ' bank account';
                break;
            case PaymentProcessorName::MANGOPAY()->getValue():
                if (TransactionType::VENDOR_WITHDRAWAL()->equals($transaction->getType()) === true) {
                    $destination = $this->companyService->get($transaction->getCompanyId())->getName() . ' bank account';
                } elseif (TransactionType::DISPATCH_FUNDS_TRANSFER_VENDOR()->equals($transaction->getType()) === true) {
                    $order = new WizachaOrder($transaction->getOrderId());
                    $destination = \count($this->mangoPay->getCompanyUserAndWallet($order->getCompany())) > 1
                        ? $this->mangoPay->getCompanyUserAndWallet($order->getCompany())[1]
                        : '';
                } elseif (TransactionType::DISPATCH_FUNDS_TRANSFER_COMMISSION()->equals($transaction->getType()) === true) {
                    $destination = MangoPay::DESTINATON_COMMISSION;
                }

                break;
            case PaymentProcessorName::LEMONWAY()->getValue():DESTINATON_COMMISSION:
                $destination = $this->lemonWay->getCompanyWallet((new Company($transaction->getCompanyId())))->ID;
                break;
            default:
                $destination = '';
                break;
        }

        return $destination;
    }

    protected function getConnection(): Connection
    {
        return $this->entityManager->getConnection();
    }

    protected function getDateTime(): \DateTimeInterface
    {
        return new \DateTimeImmutable(static::VERSION_DATE);
    }

    protected function getVersionName(): string
    {
        return static::VERSION_NAME;
    }
}
