<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright Copyright (c) Wizaplace
 * @license   Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command\Algolia;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Search\Engine\AlgoliaService;

class ClearAlgoliaIndexCommand extends Command
{
    /** @var AlgoliaService */
    protected $algoliaService;

    public function __construct(AlgoliaService $algoliaService)
    {
        parent::__construct();

        $this->algoliaService = $algoliaService;
    }

    protected function configure(): void
    {
        $this
            ->setName('algolia:index:clear')
            ->setDescription("Clear all index from search engine")
            ->addOption('force', null, InputOption::VALUE_NONE, 'Force update');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title($this->getDescription());

        if (false === $input->getOption('force') && false === $this->algoliaService->isFeatureEnableUpdateAlgolia()) {
            $io->error("Update blocked by config. Use --force option to bypass.");
            return 0;
        }

        if (false === $io->confirm("This is a destructive operation. Do you confirm?")) {
            $io->warning("Cancelled.");
            return 0;
        }

        $this->algoliaService->clearIndex();

        $io->success("All Algolia index have been cleared with success");

        return 0;
    }
}
