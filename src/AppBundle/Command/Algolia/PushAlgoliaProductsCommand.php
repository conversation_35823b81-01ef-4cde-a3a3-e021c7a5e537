<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright Copyright (c) Wizaplace
 * @license   Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command\Algolia;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Statement;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Tygh\Languages\Languages;
use Wizacha\Search\Index\AbstractIndex;

class PushAlgoliaProductsCommand extends Command
{
    private Connection $connection;
    private AbstractIndex $algoliaProductIndex;

    public function __construct(
        Connection $connection
    ) {
        parent::__construct();

        $this->connection = $connection;

        $this->algoliaProductIndex = container()->get('marketplace.search.product_index');
    }

    protected function configure(): void
    {
        $this
            ->setName('algolia:products:push')
            ->setDescription("Synchronously send all products to search engine");
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('Push products to Algolia');

        foreach (Languages::getAll() as $lang) {
            $this->update($io, $lang);
        }

        return 0;
    }

    private function update(SymfonyStyle $io, array $lang)
    {
        $io->section($lang['name']);

        $statement = $this->connection->prepare(
            <<<SQL
            SELECT
                id
            FROM
                product_catalog
            SQL
        );
        $statement->execute();
        $total = $statement->rowCount();

        $progressBar = $io->createProgressBar();
        $this->algoliaProductIndex->update(
            $progressBar->iterate(
                $statement->iterateColumn(),
                $total
            )
        );
        $progressBar->finish();
        $progressBar->clear();

        $io->success("$total product(s) pushed");
    }
}
