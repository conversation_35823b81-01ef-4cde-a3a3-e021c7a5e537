<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright Copyright (c) Wizaplace
 * @license   Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command\Algolia;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Tygh\Languages\Languages;
use Wizacha\Search\Engine\AlgoliaService;

class CreateAlgoliaIndexCommand extends Command
{
    /** @var AlgoliaService */
    protected $algoliaService;

    public function __construct(AlgoliaService $algoliaService)
    {
        parent::__construct();

        $this->algoliaService = $algoliaService;
    }

    protected function configure(): void
    {
        $this
            ->setName('algolia:index:create')
            ->setDescription("Recreate index to search engine")
            ->addOption('force', null, InputOption::VALUE_NONE, 'Force update');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title($this->getDescription());

        if (false === $input->getOption('force') && false === $this->algoliaService->isFeatureEnableUpdateAlgolia()) {
            $io->error("Update blocked by config. Use --force option to bypass.");
            return 0;
        }

        foreach (Languages::getAll() as $lang) {
            $io->section("Index creation for lang " . $lang['lang_code']);
            $this->algoliaService->createIndex($lang['lang_code']);
        }

        $io->success("All Algolia index have been created with success.");

        return 0;
    }
}
