<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command\Algolia;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Search\Index\AbstractIndex;

class UpdateAlgoliaProductsCommand extends Command
{
    private AbstractIndex $productIndex;

    public function __construct(AbstractIndex $productIndex)
    {
        parent::__construct();

        $this->productIndex = $productIndex;
    }

    protected function configure(): void
    {
        $this
            ->setName('algolia:products:update')
            ->setDescription("Update some products in Algolia indices")
            ->addArgument('id', InputArgument::IS_ARRAY, 'ID of the products')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->text("Manual updating of Algolia index");

        $ids = $input->getArgument('id');
        if (\is_string($ids)) {
            $ids = [$ids];
        }

        $this->productIndex->update(new \ArrayIterator($ids));

        $io->success(sprintf("%d products updated successfully.", \count($ids)));

        return 0;
    }
}
