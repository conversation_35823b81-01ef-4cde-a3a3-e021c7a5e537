<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright Copyright (c) Wizaplace
 * @license   Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command\Algolia;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Search\Engine\AlgoliaService;

class PushAlgoliaConfigCommand extends Command
{
    /** @var AlgoliaService */
    protected $algoliaService;

    public function __construct(AlgoliaService $algoliaService)
    {
        parent::__construct();

        $this->algoliaService = $algoliaService;
    }

    protected function configure(): void
    {
        $this
            ->setName('algolia:config:push')
            ->setDescription("Send to search engine all values for indexing and faceting")
            ->addOption('force', null, InputOption::VALUE_NONE, 'Force update');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title($this->getDescription());

        if (false === $input->getOption('force') && false === $this->algoliaService->isFeatureEnableUpdateAlgolia()) {
            $io->error("Update blocked by config. Use --force option to bypass.");
            return 0;
        }

        $this->algoliaService->pushConfig();

        $io->success("All values for indexing and faceting have been pushed to Algolia successfully.");

        return 0;
    }
}
