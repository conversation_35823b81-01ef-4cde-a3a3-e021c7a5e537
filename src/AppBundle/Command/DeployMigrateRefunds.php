<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\AppBundle\Command\Traits\DeployPostActionsOnce;
use Wizacha\Marketplace\Order\Exception\OrderNotFound;
use Wizacha\Marketplace\Order\OrderReturn\OrderReturn;
use Wizacha\Marketplace\Order\OrderReturn\OrderReturnService;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\Refund\Entity\Refund;
use Wizacha\Marketplace\Order\Refund\Entity\RefundItem;
use Wizacha\Marketplace\Order\Refund\Enum\RefundStatus;
use Wizacha\Marketplace\Order\Refund\Service\RefundService;
use Wizacha\Money\Money;

class DeployMigrateRefunds extends Command implements DeployPostActionsInterface
{
    use DeployPostActionsOnce;

    protected const VERSION_DATE = '2019-12-04 15:53:00';
    protected const VERSION_NAME = 'MigrateRefunds';

    protected const BATCH_SIZE = 200;

    /** @var EntityManagerInterface */
    protected $entityManager;

    /** @var OrderReturnService */
    protected $orderReturnService;

    /** @var RefundService */
    protected $refundService;

    /** @var OrderService */
    protected $orderService;

    public function __construct(
        EntityManagerInterface $entityManager,
        OrderReturnService $orderReturnService,
        RefundService $refundService,
        OrderService $orderService
    ) {
        parent::__construct();

        $this->entityManager = $entityManager;
        $this->orderReturnService = $orderReturnService;
        $this->refundService = $refundService;
        $this->orderService = $orderService;
    }

    public function getPriority(): int
    {
        return 103;
    }

    protected function configure(): void
    {
        $this
            ->setName('deploy:command:migrate-refunds')
            ->setDescription('Migration for the new refund system. It will run only once.')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if ($this->hasRun() === true) {
            return 0;
        }

        $this->executeMigrateOrderReturns($output);
        $this->executeInitializeInvoiceDate($output);

        $this->saveExecution();

        return 0;
    }

    protected function executeMigrateOrderReturns(OutputInterface $output): void
    {
        $batch = 1;

        do {
            $data = $this->orderReturnService->getReturns(static::BATCH_SIZE, ($batch - 1 ) * static::BATCH_SIZE);

            if ($data === false) {
                $output->writeln('<error>An error occurred while executing batch #' . $batch . '</error>');
                break;
            }

            $count = \count($data);
            $progressBar = new ProgressBar($output, $count);

            $output->writeln('<info>Starting migration of ' . $count . ' order returns (batch #' . $batch . ')</info>');
            if (false === $output->isVerbose()) {
                $progressBar->start();
            }

            $orders = [];
            /** @var OrderReturn $orderReturn */
            foreach ($data as $orderReturn) {
                if (\array_key_exists($orderReturn->getOrderId(), $orders) === false) {
                    try {
                        $orders[$orderReturn->getOrderId()] = $this->orderService->getOrder($orderReturn->getOrderId());
                    } catch (OrderNotFound $exception) {
                        continue;
                    }
                }

                $amount = $orderReturn->getItemsTotal();

                $refund = new Refund();
                $refund->setOrderId($orderReturn->getOrderId());
                $refund->setStatus(RefundStatus::PAID());
                $refund->setAmount($amount);
                $refund->setShippingAmount(new Money(0));
                $refund->setBasketDiscount(new Money(0));
                $refund->setMarketplaceDiscount(new Money(0));
                $refund->setIsLegacy(true);
                $refund->setIsPartial(true);
                $refund->setHasShipping(false);
                $refund->setCreditNoteReference($orderReturn->getRmaNumber());
                $refund->setMessage($orderReturn->getComment());
                $refund->setCreatedAt($orderReturn->getCreatedAt());
                $refund->setRefundedAfterWithdrawalPeriod(false);

                foreach ($orderReturn->getItems() as $returnItem) {
                    $refundItem = new RefundItem();
                    $refundItem->setItemId((int) $returnItem->getItemId());
                    $refundItem->setAmount(Money::fromVariable($returnItem->getPrice()));
                    $refundItem->setQuantity($returnItem->getAmount());
                    $refundItem->setRefund($refund);
                    $refundItem->setCreatedAt($orderReturn->getCreatedAt());

                    $this->entityManager->persist($refundItem);
                }

                $this->entityManager->persist($refund);
                $progressBar->advance();
            }

            $this->entityManager->flush();
            unset($orders);

            $progressBar->finish();

            ++$batch;
        } while ($count === static::BATCH_SIZE);
    }

    protected function executeInitializeInvoiceDate(OutputInterface $output): void
    {
        $sqlSelect = '
            SELECT order_id, timestamp
            FROM cscart_orders
            WHERE (
                invoice_number_provided = 1
                OR do_not_create_invoice = 1
            )
            AND invoice_date IS NULL
            AND is_garbage = 0
        ';
        $sqlUpdate = 'UPDATE cscart_orders SET invoice_date = :invoiceDate WHERE order_id = :orderId';

        $statementSelect = $this->getConnection()->prepare($sqlSelect);
        $statementUpdate = $this->getConnection()->prepare($sqlUpdate);

        $output->writeln('Starting to migrate empty invoice dates.');

        $statementSelect->execute();
        while ($row = $statementSelect->fetch()) {
            $dateTime = (new \DateTime())->setTimestamp((int) $row['timestamp']);

            $output->writeln('Update order #' . $row['order_id']);

            $statementUpdate->bindValue('orderId', $row['order_id']);
            $statementUpdate->bindValue('invoiceDate', $dateTime->format('Y-m-d H:i:s'));
            $statementUpdate->execute();
        }
    }

    protected function getConnection(): Connection
    {
        return $this->entityManager->getConnection();
    }

    protected function getDateTime(): \DateTimeInterface
    {
        return new \DateTimeImmutable(static::VERSION_DATE);
    }

    protected function getVersionName(): string
    {
        return static::VERSION_NAME;
    }
}
