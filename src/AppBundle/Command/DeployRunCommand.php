<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class DeployRunCommand extends AbstractDeployCommand
{
    protected function configure()
    {
        $this
            ->setName('deploy:run')
            ->setDescription('Execute all commands from deploy:command:*')
            ->setHelp("You can list all commands with bin/console deploy:debug")
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $io = new SymfonyStyle($input, $output);

        foreach ($this->commands as $command) {
            $io->title(
                $command->getName()
            );
            $io->text(
                $command->getDescription()
            );

            $input = new ArrayInput([]);
            $exitCode = $command->run($input, $output);

            if ($exitCode) {
                $io->error('command failed');
            }
        }

        $io->note('All commands from deploy:command:* have been executed!');
    }
}
