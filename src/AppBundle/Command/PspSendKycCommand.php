<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class PspSendKycCommand extends Command
{
    protected function configure(): void
    {
        $this
            ->setName('psp:send-kyc')
            ->setDescription('Send the KYC of all companies except companies with status = new for all PSP')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $arrayInput = new ArrayInput(["--no-pending-only"]);

        $this
            ->getApplication()
            ->find('mangopay:send-kyc')
            ->run($arrayInput, $output);

        $this
            ->getApplication()
            ->find('smoney:send-kyc')
            ->run($arrayInput, $output);

        $this
            ->getApplication()
            ->find('lemonway:send-kyc')
            ->run($arrayInput, $output);

        return 0;
    }
}
