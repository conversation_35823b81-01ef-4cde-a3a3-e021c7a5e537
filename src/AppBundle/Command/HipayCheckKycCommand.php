<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Payment\Processor\HiPay;

class HipayCheckKycCommand extends Command
{
    /** @var HiPay */
    protected $hipay;

    /** @var CompanyService */
    protected $companyService;

    /** @var LoggerInterface */
    private $logger;

    public function __construct(Hipay $hipay, CompanyService $companyService, LoggerInterface $logger)
    {
        $this->hipay = $hipay;
        $this->companyService = $companyService;
        $this->logger = $logger;

        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->setName('hipay:check-kyc')
            ->setDescription('Check the KYC of all pending companies for Hipay')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if (false === $this->hipay->isConfigured()) {
            return 0;
        }

        $companies = $this->companyService->getPendingVendors(true);

        $this->logger->error("[hipay:check-kyc]", [
            "message" => "Total company with pending status :" . $companies->rowCount(),
        ]);

        $companiesError = 0;
        foreach ($companies as $company) {
            try {
                $this->hipay->getWalletApi()->validateKyc(new Company($company['company_id']));
            } catch (\Exception $exception) {
                $this->logger->error(
                    "[Hipay] error company",
                    [
                        'companyId' => $company['company_id'],
                        'exception' => $exception
                    ]
                );
                $companiesError++;
            }
        }

        if ($companiesError > 0) {
            return 1;
        }

        return 0;
    }
}
