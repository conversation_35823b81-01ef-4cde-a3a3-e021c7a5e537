<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command\Division;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Tygh\Database;
use Wizacha\Component\Log\CommandLogService;
use Wizacha\Core\Iterator\PdoColumnIterator;
use Wizacha\Marketplace\Catalog\Company\CompanyService;
use Wizacha\Marketplace\Division\CompanyDivisionSettings;
use Wizacha\Marketplace\Division\DivisionSet;
use Wizacha\Marketplace\Division\ProductDivisionSettings;
use Wizacha\Marketplace\Division\Service\DivisionSettingsService;

class ImportDivisionProductsCommand extends Command
{
    /** @var CompanyService */
    private $companyService;

    /** @var CompanyDivisionSettings */
    private $companyDivisionSettings;

    /** @var ProductDivisionSettings */
    private $productDivisionSettings;

    /** @var DivisionSettingsService */
    private $divisionSettingsService;

    /** @var CommandLogService */
    private $commandLogService;

    public function __construct(
        CompanyService $companyService,
        CompanyDivisionSettings $companyDivisionSettings,
        ProductDivisionSettings $productDivisionSettings,
        DivisionSettingsService $divisionSettingsService,
        CommandLogService $commandLogService
    ) {
        parent::__construct();

        $this->companyService = $companyService;
        $this->companyDivisionSettings = $companyDivisionSettings;
        $this->productDivisionSettings = $productDivisionSettings;
        $this->divisionSettingsService = $divisionSettingsService;
        $this->commandLogService = $commandLogService;
    }

    protected function configure(): void
    {
        $this
            ->setName('products:divisions:init')
            ->setDescription('Install the divisions and subdivisions for products')
            ->addArgument('companies', InputArgument::IS_ARRAY, 'Ids of the companies to init')
            ->addOption('no-products', null, InputOption::VALUE_NONE, 'Do not update product data')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $bypassProductUpdate = $input->getOption('no-products');

        $companiesId = $input->getArgument('companies');
        if (empty($companiesId)) {
            $companiesId = $this->companyService->getCompaniesId();
        }

        $io = new SymfonyStyle($input, $output);
        $io->title(sprintf("Install the divisions for the products of %d companies", \count($companiesId)));

        $this->commandLogService->setOutput($output);
        $this->divisionSettingsService->setLogger($this->commandLogService);

        if (false === $bypassProductUpdate) {
            $this->updateProducts($io, $output, $companiesId);
        }

        // Dispatch MP divisions settings (and trigger cascade updates)
        $this->divisionSettingsService->onAsyncMarketplaceDivisionSettingsUpdate();

        $io->success("Divisions successfully initialized!");

        return 0;
    }

    private function updateProducts(SymfonyStyle $io, OutputInterface $output, array $companiesId)
    {
        $this->companyDivisionSettings->setIncludedDivisions(new DivisionSet(['ALL']));
        $this->productDivisionSettings->setIncludedDivisions(new DivisionSet(['ALL']));

        foreach ($companiesId as $companyId) {
            $io->section(sprintf("Company %d", $companyId));

            $count = (int) Database::getField("SELECT count(product_id) FROM ?:products WHERE company_id = ?i", $companyId);

            $progressBar = new ProgressBar($output, $count);
            $progressBar->start();

            // Update company division settings
            $this->companyDivisionSettings
                ->setCompanyId((int) $companyId)
                ->save()
            ;

            $iterator = new PdoColumnIterator(
                Database::query('SELECT product_id FROM ?:products WHERE company_id = ?i', $companyId)
            );

            foreach ($iterator as $productId) {
                $this->productDivisionSettings
                    ->setProductId((int) $productId)
                    ->save()
                ;
                $progressBar->advance();
            }

            $progressBar->finish();
            $progressBar->clear();

            $io->text(sprintf("%d processed in %d seconds", $iterator->count(), time() - $progressBar->getStartTime()));
        }
    }
}
