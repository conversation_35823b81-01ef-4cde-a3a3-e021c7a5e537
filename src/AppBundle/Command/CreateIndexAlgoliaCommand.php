<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Tygh\Languages\Languages;
use Wizacha\Search\Engine\AlgoliaService;

class CreateIndexAlgoliaCommand extends Command
{
    private AlgoliaService $algoliaService;

    public function __construct(AlgoliaService $algoliaService)
    {
        parent::__construct();

        $this->algoliaService = $algoliaService;
    }

    protected function configure()
    {
        $this
            ->setName('algolia:index')
            ->setDescription("Create index Algolia")
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        try {
            foreach (Languages::getAll() as $lang) {
                $this->algoliaService->createIndex($lang['lang_code']);
            }

            $output->writeln('<info>Index algolia successfully created!</info>');
        } catch (\Throwable $exception) {
            $output->writeln('<info>' . $exception->getMessage() . '</info>');
        }
    }
}
