<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Component\Import\EximJobService;

class ClearEximJobsCommand extends Command
{
    private EximJobService $service;

    public function __construct(EximJobService $service)
    {
        $this->service = $service;

        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setName('clear:exim-jobs')
            ->setDescription('Delete all exim jobs')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title($this->getDescription());

        $count = $this->service->count();

        if (0 === $count) {
            $io->success("No jobs to delete.");

            return 0;
        }

        if (false === $io->confirm(sprintf("This operation will delete %d jobs. Do you confirm the suppression?", $count))) {
            $io->warning("Cancelled.");

            return 1;
        }

        $io->progressStart($count);

        do {
            $paginator = $this->service->getJobsPaginate();
            foreach ($paginator as $job) {
                $this->service->delete($job);
                $io->progressAdvance();
            }
        } while ($paginator->count() > 0);

        $io->progressFinish();
        $io->success(sprintf("Successfully deleted %d jobs.", $count));

        return 0;
    }
}
