<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Marketplace\Company\CompanyStatus;
use Wizacha\Sentinel\AlertManager;
use Wizacha\Marketplace\Commission\CommissionService;

class CommissionsCheckConfigCommand extends Command
{
    protected static $defaultName = 'commissions:check:config';

    private CommissionService $commissionService;
    private AlertManager $alertManager;

    public function __construct(CommissionService $commissionService, AlertManager $alertManager)
    {
        parent::__construct();

        $this->commissionService = $commissionService;
        $this->alertManager = $alertManager;
    }

    protected function configure()
    {
        $this
            ->setDescription('Logs an alert for every company with a maximum commission at 0')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $companyCommissions = $this->commissionService->getAllCompanyCommissions();

        foreach ($companyCommissions as $companyCommission) {
            $companyId = $companyCommission->getCompanyId();
            $companyData = fn_get_company_data($companyId);
            $companyStatus = new CompanyStatus($companyData['status']);

            if (false === $companyStatus->equals(CompanyStatus::ENABLED())) {
                continue;
            }

            if ($companyCommission->getMaximumAmount() === 0.) {
                $this->alertManager->send("Commission with maximum configured of 0", [
                    'companyId' => $companyId
                ], 'commissions');
            }

            if ($companyCommission->getFixAmount() === 0. && $companyCommission->getPercentAmount() === 0.) {
                $this->alertManager->send("Commission with fixed and percent of 0", [
                    'companyId' => $companyId
                ], 'commissions');
            }
        }
    }
}
