<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Marketplace\RgpdAnonymizer\RgpdAnonymizerService;
use Wizacha\Marketplace\RgpdAnonymizer\RgpdAnonymizerType;

class RgpdCompanyAnonymizerCommand extends Command
{
    /** @var RgpdAnonymizerService */
    protected $rgpdAnonymizerService;

    /** @var string  */
    protected static $defaultName = 'rgpd:anonymize-company';

    public function __construct(RgpdAnonymizerService $rgpdAnonymizerService)
    {
        parent::__construct();

        $this->rgpdAnonymizerService = $rgpdAnonymizerService;
    }

    protected function configure(): void
    {
        $this
            ->addArgument('companyId', InputArgument::REQUIRED, 'Enter a company id.')
            ->setDescription('Anonymize a company and disable it.')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $companyId = $input->getArgument('companyId');

        if (false === \ctype_digit($companyId)) {
            $output->writeln('The companyId parameter is invalid (must be an integer)');

            return 1;
        }

        try {
            $anonymizationInformations = $this->rgpdAnonymizerService
                ->anonymize((int) $companyId, RgpdAnonymizerType::COMPANY()->getValue())
                ->expose()
            ;

            foreach ($anonymizationInformations as $information) {
                $output->writeln($information);
            }
        } catch (\Throwable $exception) {
            $output->writeln($exception->getMessage());

            return 1;
        }

        return 0;
    }
}
