<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright Copyright (c) Wizaplace
 * @license   Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class UpdateTranslationOrderStatusCommand extends Command
{
    /** @var Connection */
    protected $connection;

    public function __construct(Connection $connection)
    {
        parent::__construct();

        $this->connection = $connection;
    }

    protected function configure(): void
    {
        $this
            ->setName('translation:update:default_value_order_status')
            ->setDescription('Update Translation Order Status Default Value.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $englishStatus = $this->connection->prepare(
            'SELECT * FROM cscart_status_descriptions WHERE lang_code = :lang_code'
        );
        $englishStatus->execute(['lang_code' => 'en']);
        while ($status = $englishStatus->fetch()) {
            $frenchStatus = $this->connection->prepare(
                'SELECT * FROM cscart_status_descriptions WHERE lang_code = :lang_code AND status = :status AND type = :type'
            );
            $frenchStatus->execute(
                [
                    'lang_code' => 'fr',
                    'status' => $status['status'],
                    'type' => $status['type']
                ]
            );
            $defaultStatus = $frenchStatus->fetch();

            $newValues = [];
            $update = [];

            if (\strlen($status['description']) === 0) {
                $newValues['description'] = $defaultStatus['description'];
                $update[] = ' description = :description ';
            }
            if (\strlen($status['email_subj']) === 0) {
                $newValues['email_subj'] = $defaultStatus['email_subj'];
                $update[] = ' email_subj = :email_subj ';
            }
            if (\strlen($status['email_header']) === 0) {
                $newValues['email_header'] = $defaultStatus['email_header'];
                $update[] = ' email_header = :email_header ';
            }
            if (\strlen($status['vendor_email_subj']) === 0) {
                $newValues['vendor_email_subj'] = $defaultStatus['vendor_email_subj'];
                $update[] = ' vendor_email_subj = :vendor_email_subj ';
            }
            if (\strlen($status['vendor_email_header']) === 0) {
                $newValues['vendor_email_header'] = $defaultStatus['vendor_email_header'];
                $update[] = ' vendor_email_header = :vendor_email_header ';
            }

            if (\count($update) > 0) {
                $toUpdate = implode(",", $update);
                $conditions = ' lang_code = :lang_code AND status = :status AND type = :type ';
                $newValues['lang_code'] = 'en';
                $newValues['status'] = $status['status'];
                $newValues['type'] = $status['type'];
                $update = $this->connection->prepare(
                    'UPDATE cscart_status_descriptions set' . $toUpdate . ' WHERE ' . $conditions
                );
                $update->execute($newValues);
            }
        };
        $output->writeln(sprintf('<comment>Update Has been made</comment>'));

        return 0;
    }
}
