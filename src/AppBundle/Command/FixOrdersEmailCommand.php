<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Marketplace\Order\OrderService;

class FixOrdersEmailCommand extends Command
{
    protected static $defaultName = 'orders:fix-email';

    /** @var OrderService */
    protected $orderService;

    public function __construct(
        OrderService $orderService
    ) {
        parent::__construct();

        $this->orderService = $orderService;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Update all orders without an email with the email from the client profile')
            ->addOption('dry-run', null, InputOption::VALUE_NONE, 'Dry mode won\'t alter the DB.');
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Update orders without email');

        $orders = $this->orderService->getOrdersWithEmptyEmail();
        $dryMode = $input->getOption('dry-run') !== false;

        if ($dryMode === false) {
            foreach ($orders as $order) {
                $this->orderService->setOrderEmail((int) $order['order_id'], $order['user_email']);
            }
        }

        $io->success(sprintf("%d orders updated successfully.", \count($orders)));

        return 0;
    }
}
