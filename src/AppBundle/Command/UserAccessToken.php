<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Marketplace\User\UserService;

class UserAccessToken extends Command
{
    /** @var UserService */
    protected $userService;

    public function __construct(UserService $userService)
    {
        parent::__construct();

        $this->userService = $userService;
    }

    protected function configure(): void
    {
        $this
            ->setName('user:oauth:token')
            ->setDescription('Read or create a user\'s OAuth2 access_token')
            ->addArgument('email', InputArgument::REQUIRED)
            ->addArgument('token', InputArgument::OPTIONAL, 'If provided, the new access token')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $email = $input->getArgument('email');
        $token = $input->getArgument('token') ?? null;

        $user = $this->userService->CreateOrGetOAuthUser($email, $token);

        $output->writeln(sprintf('<comment>OAuth token : %s</comment>', $this->userService->get($user)->getOAuthToken()->getToken()));

        return 0;
    }
}
