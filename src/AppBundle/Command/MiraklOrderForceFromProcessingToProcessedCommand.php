<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Connection;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Input\Input;
use Symfony\Component\Console\Input\InputArgument;
use Wizacha\Marketplace\Order\Action\EndWithdrawalPeriod;
use Wizacha\Marketplace\Order\Action\MarkAsDelivered;
use Wizacha\Marketplace\Order\Action\MarkAsShipped;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\OrderStatus;
use Wizacha\AppBundle\Controller\Api\Shipments;
use Wizacha\Marketplace\Order\Order;

class MiraklOrderForceFromProcessingToProcessedCommand extends MiraklOrderForceCompletedCommand
{
    /**  @var \Wizacha\Marketplace\Order\Action\MarkAsShipped */
    private $markAsShipped;
    /** @var \Wizacha\AppBundle\Controller\Api\Shipments */
    private $shipments;

    public function __construct(
        LoggerInterface $logger,
        Connection $connection,
        OrderService $orderService,
        MarkAsDelivered $markAsDelivered,
        EndWithdrawalPeriod $endWithDrawalPeriod,
        MarkAsShipped $markAsShipped,
        Shipments $shipments,
        $name = null
    ) {
        parent::__construct($logger, $connection, $orderService, $markAsDelivered, $endWithDrawalPeriod, $name);
        $this->markAsShipped = $markAsShipped;
        $this->shipments = $shipments;
    }

    protected function configure()
    {
        $this
            ->setName('orders:force-processed')
            ->setDescription('Force orders to the processed status')
            ->addArgument(
                'filepath',
                InputArgument::REQUIRED,
                'Path of the file containing the ids, separated by commas'
            );
    }

    protected function processOrder(int $orderId): int
    {
        $this->io->title("Force order $orderId to processed.");

        $order = $this->orderService->getOrder($orderId);

        if ($order->getStatus()->getValue() !== OrderStatus::PROCESSING_SHIPPING) {
            $this->io->error("Order is in wrong status: " . $order->getStatus() . '. Stopping processing.');

            return 1;
        }

        $order->declareInvoiceNumberGeneratedElsewhere();

        try {
            $this->shipments->create($this->getShipmentParams($order));
        } catch (\Throwable $exception) {
            /**
             * The shipment is correctly created, but an error occurs. The same issue appears
             * when trying to create it manually from the back-office. But this it is enough
             * to allow to continue the workflow.
             */
            $this->logger->error($exception->getMessage() . ' ' . $exception->getTraceAsString());
        }

        if ($this->markAsShipped->isAllowed($order)) {
            $this->markAsShipped->execute($order);

            $this->io->text("Marked as shipped");
        } else {
            $this->io->warning("WARNING for order #{$orderId}: impossible to mark as shipped");
        }

        fn_change_order_status($order->getId(), OrderStatus::PROCESSED);
        $this->io->success("Order with id #{$order->getId()}: status set to processed");

        return 0;
    }

    private function getShipmentParams(Order $order): array
    {
        $items = $order->getItems();
        $orderItems = [];
        foreach ($items as $item) {
            $orderItems[(int) $item->getItemId()] = (int) $item->getAmount();
        }

        return [
            'order_id' => $order->getId(),
            'tracking_number' => 'Reprise de données depuis Mirakl',
            'comments' => 'Reprise de données depuis Mirakl',
            'label_url' => 'http://www.wizaplace.com',
            'products' => $orderItems
        ];
    }

    protected function getOptions(Input $input): void
    {
        // No options for this command
    }
}
