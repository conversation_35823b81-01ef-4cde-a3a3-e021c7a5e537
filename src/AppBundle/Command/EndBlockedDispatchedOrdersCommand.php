<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Marketplace\Order\Action\DispatchFunds;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\OrderStatus;

class EndBlockedDispatchedOrdersCommand extends Command
{
    /** @var Connection */
    protected $connection;

    /** @var OrderService */
    protected $orderService;

    /** @var DispatchFunds */
    protected $dispatchFunds;

    /** @var int */
    protected $count = 0;

    public function __construct(Connection $connection, OrderService $orderService, DispatchFunds $dispatchFunds)
    {

        $this->orderService = $orderService;
        $this->connection = $connection;
        $this->dispatchFunds = $dispatchFunds;
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->setName('orders:end-blocked-dispatched-orders')
            ->setDescription('Complete orders stuck on awaiting dispatch workflow')
            ->addOption('dry-run', null, InputOption::VALUE_OPTIONAL, 'Dry mode won\'t alter the DB.', false);
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $dryMode = false !== $input->getOption('dry-run');

        $sql = '
            SELECT co.order_id, od.data
            FROM cscart_orders co
            INNER JOIN cscart_order_data od ON od.order_id = co.order_id and od.`type` = :dataType
            LEFT join orders_actions_traces t ON t.order_id = co.order_id AND t.`action` = :endWithrawalAction
            WHERE co.status IN (:typeCompleted, :typeProcessed)
            AND co.workflow_status = :workflowStatus
            AND co.workflow_current_module_name = :workflowModule
            GROUP BY co. order_id
        ';

        $statement = $this->connection->prepare($sql);

        $statement->bindValue(':dataType', 'P');
        $statement->bindValue(':endWithrawalAction', 'EndWithdrawalPeriod');
        // COMPLETED and PROCESSED
        $statement->bindValue(':typeCompleted', 'H');
        $statement->bindValue(':typeProcessed', 'C');
        $statement->bindValue(':workflowStatus', 'processing');
        $statement->bindValue(':workflowModule', 'funds-dispatch');
        $statement->execute();

        while ($row = $statement->fetch()) {
            $orderId = (int) $row['order_id'];
            $paymentData = unserialize(fn_decrypt_text($row['data']));
            if (false === \array_key_exists('stripe_transfer_done', $paymentData)
                || false === filter_var($paymentData['stripe_transfer_done'], FILTER_VALIDATE_BOOLEAN)
            ) {
                $io->warning(sprintf("Order #%s: Stripe dispatch NOT done - but the order matches criteria", $orderId));

                continue;
            }

            $order = $this->orderService->getOrder($orderId);

            if ($this->dispatchFunds->isAllowed($order)) {
                $io->writeln(sprintf("Order #%s: advancing workflow", $orderId));

                if (false === $dryMode) {
                    $this->dispatchFunds->execute($order);
                }
            } else {
                $io->warning(sprintf("Order #%s: workflow advance not allowed", $orderId));

                continue;
            }

            $io->writeln(sprintf("Order #%s: setting legacy status to completed", $orderId));

            if (false === $dryMode) {
                fn_change_order_status($order->getId(), OrderStatus::COMPLETED);
            }

            ++$this->count;
        }

        $io->success(str_repeat('-', 50) . PHP_EOL . sprintf('Done! %d orders have been processed', $this->count));

        return 0;
    }
}
