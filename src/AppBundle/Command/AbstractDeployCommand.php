<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

abstract class AbstractDeployCommand extends Command
{
    /** @var Command[] */
    protected $commands;

    protected function initialize(InputInterface $input, OutputInterface $output)
    {
        /** @var DeployPostActionsInterface[] $commands */
        $commands = [];
        foreach ($this->getApplication()->all("deploy:command") as $command) {
            if ($command instanceof DeployPostActionsInterface) {
                $commands[] = $command;
            }
        }

        usort($commands, function (DeployPostActionsInterface $commandA, DeployPostActionsInterface $commandB) {
            return $commandA->getPriority() <=> $commandB->getPriority();
        });

        $this->commands = $commands;
    }
}
