<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\ContainerAwareInterface;
use Symfony\Component\DependencyInjection\ContainerAwareTrait;
use Wizacha\AppBundle\Fixture\BusinessFixture;
use Wizacha\AppBundle\Fixture\EmptyFixture;
use Wizacha\AppBundle\Fixture\Module\AuthLogFixture;
use Wizacha\AppBundle\Fixture\Module\CmsFixture;
use Wizacha\AppBundle\Fixture\TechnicalFixture;

class LoadFixtureCommand extends Command implements ContainerAwareInterface
{
    use ContainerAwareTrait;

    protected function configure()
    {
        $this
            ->setName('fixture:load')
            ->setDescription('Load fixture')
            ->addArgument('name', InputArgument::OPTIONAL, 'Name of the fixture to load (empty, technical, business, authlog)', 'business')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $name = $input->getArgument('name');
        switch ($name) {
            case 'business':
                $fixture = $this->container->get(BusinessFixture::class);
                break;
            case 'technical':
                $fixture = $this->container->get(TechnicalFixture::class);
                break;
            case 'empty':
                $fixture = $this->container->get(EmptyFixture::class);
                break;
            case 'cms':
                $fixture = $this->container->get(CmsFixture::class);
                break;
            case 'authlog':
                $fixture = $this->container->get(AuthLogFixture::class);
                break;
            default:
                throw new \Exception('Unknown fixture ' . $name);
        }

        $fixture->install($input, $output);

        $output->writeln(sprintf('<comment>Fixtures installed</comment>'));
    }
}
