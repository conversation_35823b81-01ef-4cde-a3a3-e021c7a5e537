<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\AppBundle\Command\Traits\DeployPostActionsOnce;
use Wizacha\Component\Divisions\XmlImporter;

class DeployMigrateDivisionCommand extends Command implements DeployPostActionsInterface
{
    use DeployPostActionsOnce;

    /** @var string */
    protected const VERSION_DATE = '2020-02-26 11:00:00';

    /** @var string */
    protected const VERSION_NAME = 'Import all divisions';

    /** @var string[] */
    protected const LOCALES_TO_IMPORT = ['fr', 'en'];

    /** @var string */
    protected const FALLBACK_LOCALE = 'en';

    /** @var EntityManagerInterface */
    private $entityManager;

    /** @var bool */
    private $featureEnableDivisions;

    /** @var XmlImporter */
    private $xmlImporter;

    public function __construct(
        EntityManagerInterface $entityManager,
        XmlImporter $xmlImporter,
        bool $featureEnableDivisions
    ) {
        parent::__construct('deploy:command:migrate-divisions');

        $this->entityManager = $entityManager;
        $this->featureEnableDivisions = $featureEnableDivisions;
        $this->xmlImporter = $xmlImporter;
        $this->xmlImporter->setLocalesToImport(static::LOCALES_TO_IMPORT);
        $this->xmlImporter->setFallbackLocale(static::FALLBACK_LOCALE);
    }

    protected function configure(): void
    {
        $this->setDescription('Import all divisions');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        if (false === $this->featureEnableDivisions) {
            $io->writeln("<info>Divisions feature is not enabled.</info>");

            return 0;
        }

        if (true === $this->hasRun()) {
            $io->writeln("<info>Command can be executed only once.</info>");

            return 0;
        }

        $this->xmlImporter->process();

        $this->saveExecution();

        return 0;
    }

    public function getPriority(): int
    {
        return 100;
    }

    protected function getConnection(): Connection
    {
        return $this->entityManager->getConnection();
    }

    protected function getDateTime(): \DateTimeInterface
    {
        return new \DateTimeImmutable(static::VERSION_DATE);
    }

    protected function getVersionName(): string
    {
        return static::VERSION_NAME;
    }
}
