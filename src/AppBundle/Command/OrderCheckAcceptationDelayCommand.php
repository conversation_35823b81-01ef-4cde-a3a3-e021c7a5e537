<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\ContainerAwareInterface;
use Symfony\Component\DependencyInjection\ContainerAwareTrait;
use Wizacha\Marketplace\Order\OrderStatus;

class OrderCheckAcceptationDelayCommand extends Command implements ContainerAwareInterface
{
    use ContainerAwareTrait;

    protected function configure()
    {
        $this
            ->setName('orders:check-acceptation-delay')
            ->setDescription('Cancel order in pending vendor validation when the payment is a credit card with authorization')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $orders = $this->container->get('marketplace.order.order_service')->getPendingOrderToBeCanceled();
        $cancel = $this->container->get('marketplace.order.action.cancel');

        $nbCanceledOrders = 0;
        foreach ($orders as $order) {
            if ($cancel->isAllowed($order)) {
                try {
                    $cancel->execute($order);
                    $nbCanceledOrders++;
                    fn_change_order_status($order->getId(), OrderStatus::CANCELED);
                } catch (\Throwable $exception) {
                }
            }
        }

        $output->writeln(sprintf("<info>%s commandes traitées, dont %s ont été annulé.</info>", \count($orders), $nbCanceledOrders));

        return 0;
    }
}
