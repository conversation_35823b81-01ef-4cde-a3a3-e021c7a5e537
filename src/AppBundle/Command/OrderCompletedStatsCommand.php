<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Tygh\Database;
use Wizacha\OrderStatus;

class OrderCompletedStatsCommand extends Command
{
    protected function configure()
    {
        $this
            ->setName('stats:order:completed')
            ->setDescription('Return number of order completed between two dates')
            ->addArgument('start', InputArgument::OPTIONAL, 'Start date in ISO format or timestamp', strtotime('yesterday'))
            ->addArgument('end', InputArgument::OPTIONAL, 'End date in ISO format or timestamp', time())
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $start = $input->getArgument('start');
        if (is_numeric($start)) {
            // Timestamp
            $start = \DateTime::createFromFormat('U', $start);
        } else {
            $start = new \DateTime($start);
        }
        $end = $input->getArgument('end');
        if (is_numeric($end)) {
            // Timestamp
            $end = \DateTime::createFromFormat('U', $end);
        } else {
            $end = new \DateTime($end);
        }

        $resultats = Database::getRow(
            "SELECT SUM(total) as sum, GROUP_CONCAT(order_id) as ids
              FROM ?:orders
              WHERE w_last_status_change > ?s AND w_last_status_change <= ?s
              AND status=?s
              AND is_parent_order= 'N';",
            $start->format("Y-m-d H:i:s"),
            $end->format("Y-m-d H:i:s"),
            OrderStatus::COMPLETED
        );

        $output->writeln('Orders sum : ' . (int) $resultats['sum'] . ' Order ids : ' . $resultats['ids']);
    }
}
