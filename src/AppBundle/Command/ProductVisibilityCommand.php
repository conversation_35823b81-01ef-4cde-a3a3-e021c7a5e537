<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Marketplace\Catalog\Product\ProductService;
use Wizacha\Marketplace\ReadModel\Product;

class ProductVisibilityCommand extends Command
{
    private ProductService $productService;

    public function __construct(ProductService $productService)
    {
        parent::__construct();

        $this->productService = $productService;
    }

    protected function configure()
    {
        $this
            ->setName('products:visibility')
            ->setDescription('Check if a product is searcheable in the front')
            ->addArgument('id', InputArgument::REQUIRED, 'MVP id')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title("Catalog API debugging");

        $productId = $input->getArgument('id');

        $io->text("Input: product #" . $productId);

        if (\is_numeric($productId)) {
            $id = [ $productId ];
            $mvpId = [];
        } else {
            $id = [];
            $mvpId = [ $productId ];
        }

        $io->section("getProductsIdByFilters");
        $productIds = $this->productService->getProductsIdByFilters(
            $id,
            [],
            [],
            $mvpId
        );
        $io->listing($productIds);

        $io->section("getSearcheableProductIdInFront");
        $list = $this->productService->getSearcheableProductIdInFront($productIds, [], true)->toArray();
        $io->listing($list);

        $io->section("getProductsByIds");
        $products = $this->productService->getProductsByIds($productIds);
        $io->listing(array_map(fn (Product $product) => $product->getId(), $products));

        foreach ($products as $product) {
            if ($product !== null) {
                $io->section("Availability of product #" . $product->getId());
                if ($product->isAvailable() === true) {
                    $io->success("Product is available and returned in catalog API");
                } else {
                    $io->error("Product is not available and not returned in catalog API");
                    $io->text(json_encode($product->getDeclinations(), JSON_PRETTY_PRINT));
                }
            }
        }

        return 0;
    }
}
