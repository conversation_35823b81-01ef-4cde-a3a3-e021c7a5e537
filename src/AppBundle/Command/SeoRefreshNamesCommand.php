<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright Copyright (c) Wizaplace
 * @license   Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Marketplace\Seo\SeoService;

class SeoRefreshNamesCommand extends Command
{
    /** @var SeoService */
    protected $seoService;

    public function __construct(SeoService $seoService)
    {
        parent::__construct();

        $this->seoService = $seoService;
    }

    protected function configure(): void
    {
        $this
            ->setName('seo:names:refresh')
            ->setDescription("Regenerate seo names");
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->seoService->refreshNames();
        $output->writeln("<info>SEO regenerated with success.</info>");

        return 0;
    }
}
