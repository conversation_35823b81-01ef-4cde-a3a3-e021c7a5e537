<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\Question;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\Repository\OrderRepository;

class RollbackProcessedOrdersCommand extends Command
{
    /** @var OrderRepository */
    private $orderRepository;

    /** @var OrderService */
    private $orderService;

    /** @var SymfonyStyle */
    private $io;

    protected const DESCRIPTION = 'Rollback shipped orders, by batches of 10 orders.';

    protected static $defaultName = 'orders:rollback-processed-orders';


    public function __construct(OrderRepository $orderRepository, OrderService $orderService)
    {
        parent::__construct();

        $this->orderRepository = $orderRepository;
        $this->orderService = $orderService;
    }

    protected function configure(): void
    {
        $this
            ->setDescription(static::DESCRIPTION)
            ->addOption('dry-run', null, InputOption::VALUE_NONE, 'Dry mode won\'t alter the DB.')
            ->addOption('force', null, InputOption::VALUE_NONE, 'Force mode will rollback all targeted orders in one go.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->io = new SymfonyStyle($input, $output);

        $this->io->title(static::DESCRIPTION);

        $toBeRollbackedOrderIds = $this->orderRepository->getProcessedOrdersToRollbackToPendingPreparation();
        $ordersCount = \count($toBeRollbackedOrderIds);

        $this->io->{
        $ordersCount > 0 ? 'error' : 'success'
        }(
            sprintf(
                'Order count: %d. Order Ids (truncated to first 20 results): %s.',
                $ordersCount,
                $ordersCount > 0 ? \implode(",", \array_slice($toBeRollbackedOrderIds, 0, 20)) : "none"
            )
        );

        if ($input->getOption('dry-run') === true) {
            return 0;
        }

        if ($ordersCount > 0) {
            // FORCE MODE
            if ($input->getOption('force') === true) {
                $response = $this->io->askQuestion(
                    new Question(
                        sprintf(
                            "Answer REALLY, if you REALLY want to rollback these %d orders",
                            $ordersCount
                        )
                    )
                );

                if ('REALLY' === $response) {
                    $this->orderRepository->rollbackProcessedOrdersToPendingPreparation($toBeRollbackedOrderIds);
                    $this->displaySuccess($toBeRollbackedOrderIds);

                    return 0;
                }
            }

            // STANDARD BEHAVIOUR
            $toBeRollbackedOrderIds = \array_slice($toBeRollbackedOrderIds, 0, 10);

            $response = $this->io->askQuestion(
                new Question(
                    sprintf(
                        "Answer REALLY if you want to rollback this batch of %d orders : %s",
                        \count($toBeRollbackedOrderIds),
                        \implode(",", $toBeRollbackedOrderIds)
                    )
                )
            );

            if ('REALLY' === $response) {
                $this->orderRepository->rollbackProcessedOrdersToPendingPreparation($toBeRollbackedOrderIds);
                $this->displaySuccess($toBeRollbackedOrderIds);

                if (\count($this->orderRepository->getProcessedOrdersToRollbackToPendingPreparation()) > 0) {
                    $this->execute($input, $output);
                }
            }
        }

        return 0;
    }

    /** @param int[] $orderIds */
    private function displaySuccess(array $orderIds, bool $detailed = false): void
    {
        $message = \count($orderIds) . " orders have been sucessfully rollbacked";
        $detailAppendix = ": " . \implode(",", $orderIds);
        $message = $detailed === false ? $message : $message . $detailAppendix;

        $this->io->success(\sprintf($message));
    }
}
