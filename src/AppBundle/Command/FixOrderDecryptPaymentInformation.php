<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Driver\Connection;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Marketplace\Order\OrderDataType;

class FixOrderDecryptPaymentInformation extends Command
{
    protected static $defaultName = 'orders:decrypt-payment-information';

    /** @var Connection */
    private $connection;

    public function __construct(
        Connection $connection
    ) {
        parent::__construct();
        $this->connection = $connection;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Decrypt and encrypt orders` payment information with an old crypt_key and the current crypt_key')
            ->addArgument('crypt-key', InputArgument::REQUIRED, 'The old crypt_key')
            ->addOption('dry-run', null, InputOption::VALUE_NONE, 'Dry mode will not alter the DB');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Decrypting orders with the old crypt key...');
        $cryptKey = $input->getArgument('crypt-key');
        $dryMode = $input->getOption('dry-run') !== false;
        $currentCryptKey = container()->getParameter('cscart.crypt_key');

        $cryptService = container()->get('cscart.crypt');
        $numberOrdersUpdated = 0;

        $stmt = $this->connection->prepare(
            "SELECT
                    *
                FROM
                    cscart_order_data
                WHERE
                      type = :order_data_type"
        );

        $this->connection->beginTransaction();
        $stmt->execute(
            ['order_data_type' => OrderDataType::PAYMENT_INFO()->getValue()]
        );
        $progressBar = $io->createProgressBar($stmt->rowCount());

        $stmtUpdate = $this->connection->prepare(
            'UPDATE
                cscart_order_data
            SET
                data = :data
            WHERE
                order_id = :order_id
                AND type = :order_data_type
            '
        );

        while ($order = $stmt->fetch()) {
            $cryptService->setKey($currentCryptKey);
            $data = unserialize(fn_decrypt_text($order['data']));

            if (false === $data) {
                $cryptService->setKey($cryptKey);
                $data = unserialize(fn_decrypt_text($order['data']));

                if (false === $data) {
                    $io->error(\sprintf('Impossible to decrypt the order %d with the current and the old crypt-key.', $order['order_id']));
                    $progressBar->advance();
                    continue;
                }

                $cryptService->setKey($currentCryptKey);
                $newData = fn_encrypt_text(serialize($data));
                $stmtUpdate->execute(
                    [
                        'order_id' => $order['order_id'],
                        'data' => $newData,
                        'order_data_type' => OrderDataType::PAYMENT_INFO()->getValue(),
                    ]
                );

                $numberOrdersUpdated++;
            }

            $progressBar->advance();
        }

        $progressBar->clear();

        if ($dryMode === true) {
            $this->connection->rollBack();
        } else {
            $this->connection->commit();
        }

        $io->success(sprintf('%d order(s) with the old crypt key.', $numberOrdersUpdated));

        return 0;
    }
}
