<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Component\Translation\MessageCatalogue;
use Symfony\Component\Translation\Reader\TranslationReaderInterface;
use Tygh\Languages\Languages;
use Wizacha\AppBundle\Service\TranslationService;

class ImportThemeTranslationsCommand extends Command
{
    /**
     * @var TranslationReaderInterface
     */
    private $loader;
    /**
     * @var TranslationService
     */
    private $translationService;
    /**
     * @var KernelInterface
     */
    private $kernel;

    protected function configure()
    {
        $this
            ->setName('theme:import-translations')
            ->setDescription('Import translations of the current theme')
            ->addOption(
                'force',
                'f',
                InputOption::VALUE_NONE,
                'Overwrite existing translations. This should NOT be used in production environment.'
            )
        ;
    }

    public function __construct(
        TranslationReaderInterface $loader,
        TranslationService $translationService,
        KernelInterface $kernel
    ) {

        parent::__construct();
        $this->loader = $loader;
        $this->translationService = $translationService;
        $this->kernel = $kernel;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $projectDir = $this->kernel->getProjectDir();
        $transPaths = array($projectDir . '/app/Resources/');
        $updateDuplicates = $input->getOption('force');

        foreach ($this->kernel->getBundles() as $bundle) {
            $transPaths[] = $bundle->getPath() . '/Resources/';
            $transPaths[] = sprintf('%s/app/Resources/%s/', $projectDir, $bundle->getName());
        }

        foreach (Languages::getAvailable() as $locale => $language) {
            $count = $this->doImport($locale, $transPaths, $this->loader, $this->translationService, $updateDuplicates);

            if ($count > 0) {
                $output->writeln(sprintf('<comment>%d translations imported for "%s"</comment>', $count, $locale));
            } else {
                $output->writeln(sprintf('<comment>No translations imported for "%s"</comment>', $locale));
            }
        }
    }

    private function doImport(
        string $locale,
        array $transPaths,
        TranslationReaderInterface $loader,
        TranslationService $translationService,
        bool $updateDuplicates
    ) {
        $catalog = new MessageCatalogue($locale);

        foreach ($transPaths as $path) {
            $path = $path . 'translations';
            if (is_dir($path)) {
                $loader->read($path, $catalog);
            }
        }

        // On importe seulement les traductions du domaine "messages"
        $messages = $catalog->all('messages');
        $translationService->setTranslations($messages, $locale, $updateDuplicates);

        return \count($messages);
    }
}
