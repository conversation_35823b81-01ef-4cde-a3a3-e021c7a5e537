<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\AppBundle\Command\Traits\DeployPostActionsOnce;
use Wizacha\Marketplace\Transaction\TransactionRepository;

class DeploySetTransactionCompanyIdAndCurrencyCommand extends Command implements DeployPostActionsInterface
{
    use DeployPostActionsOnce;

    private TransactionRepository $transactionRepository;
    private string $currencyCode;
    private const VERSION_DATE = '2021-07-28 14:25:00';
    private const VERSION_NAME = 'Set transaction companyId and currency';
    private EntityManagerInterface $entityManager;

    public function __construct(
        TransactionRepository $transactionRepository,
        string $currencyCode,
        EntityManagerInterface $entityManager
    ) {
        parent::__construct('deploy:command:transactions-set-companyId-and-currency');
        $this->transactionRepository = $transactionRepository;
        $this->currencyCode = $currencyCode;
        $this->entityManager = $entityManager;
    }

    public function getPriority(): int
    {
        return 100;
    }

    protected function configure(): void
    {
        $this->setDescription('Set transaction companyId and currency for historic transaction');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $rowCount = $this->transactionRepository->fixTransactionWithoutCompanyId(
            $this->currencyCode
        );

        $io->success(
            "$rowCount Transactions updated ..."
        );

        return 0;
    }

    protected function getConnection(): Connection
    {
        return $this->entityManager->getConnection();
    }

    protected function getDateTime(): \DateTimeInterface
    {
        return new \DateTimeImmutable(static::VERSION_DATE);
    }

    protected function getVersionName(): string
    {
        return static::VERSION_NAME;
    }
}
