<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class DeployDebugCommand extends AbstractDeployCommand
{
    protected function configure()
    {
        $this
            ->setName('deploy:debug')
            ->setAliases(['debug:deploy'])
            ->setDescription('List all deploy commands')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $table = new Table($output);
        $table->setHeaders([
            'Priority',
            'Command',
            'Aliases',
            'Description',
        ]);

        $commandsRows = [];
        foreach ($this->commands as $command) {
            $commandsRows[] = [
                $command->getPriority(),
                $command->getName(),
                implode(", ", $command->getAliases()),
                $command->getDescription(),
            ];
        }

        $table->setRows($commandsRows);

        $table->render();
    }
}
