<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Payment\Processor\LemonWay;

class LemonWayCheckKycCommand extends Command implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    /** @var LemonWay */
    private $lemonway;

    /** @var CompanyService */
    private $companyService;

    public function __construct(LemonWay $lemonway, CompanyService $companyService)
    {
        parent::__construct();

        $this->lemonway = $lemonway;
        $this->companyService = $companyService;
    }

    protected function configure(): void
    {
        $this
            ->setName('lemonway:check-kyc')
            ->setDescription('Check the KYC of all pending and C2C companies for LemonWay')
            ->addOption(
                'no-pending-only',
                null,
                InputOption::VALUE_NONE,
                'Check the KYC of all companies except company with status = new'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if (false === $this->lemonway->isConfigured()) {
            return 0;
        }

        if ($input->getOption('no-pending-only') === true) {
            $statement = $this->companyService->getAllVendorsExceptNew();
        } else {
            $statement = $this->companyService->getPendingVendors(true);
        }

        $progressBar = new ProgressBar($output, $statement->rowCount());
        $progressBar->start();

        $return = 0;
        while ($company = $statement->fetch()) {
            try {
                $progressBar->advance();

                $this->lemonway->validateKyc(new Company($company['company_id']));
            } catch (\Exception $exception) {
                $this->logger
                    ->error("[LemonWay] error company", [
                        'companyId' => $company['company_id'],
                        'exception' => $exception,
                    ]);
                $return = 1;
            }
        }

        $progressBar->finish();

        return $return;
    }
}
