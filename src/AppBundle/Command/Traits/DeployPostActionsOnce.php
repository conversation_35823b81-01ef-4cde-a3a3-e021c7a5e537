<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command\Traits;

use Doctrine\DBAL\Connection;

/**
 * Trait DeployPostActionsOnce
 *
 * This trait is an helper letting you create deployment command which will be run only once.
 * It must be used in a DeployPostActions command, and the command must include a connection to the DB.
 * It uses the migration DB table, which is also used by phinx.
 */
trait DeployPostActionsOnce
{
    public function hasRun(): bool
    {
        $this->checkVersionName($this->getVersionName());

        $sql = 'SELECT COUNT(version) FROM migration WHERE migration_name = :name';
        $statement = $this->getConnection()->prepare($sql);

        $statement->execute(['name' => $this->getVersionName()]);

        $result = (int) $statement->fetchColumn(0);

        return $result !== 0;
    }


    public function saveExecution(): void
    {
        $this->checkVersionName($this->getVersionName());

        $sql = 'INSERT INTO migration (version, migration_name, start_time, end_time, breakpoint)
                VALUES (:date, :name, NOW(), NOW(), 0)';
        $statement = $this->getConnection()->prepare($sql);

        $statement->execute([
            'date' => $this->getDateTime()->format('YmdHis'),
            'name' => $this->getVersionName(),
        ]);
    }

    abstract protected function getConnection(): Connection;

    /**
     * IMPORTANT: the is the _unique_ primary key of the `migration` DB table,
     *            so you MUST provide a fixed date and time that is unique among DeployPostActionsOnce classes.
     *            Note however that we ignore it when we check if the migration has already ran.
     */
    abstract protected function getDateTime(): \DateTimeInterface;

    abstract protected function getVersionName(): string;

    protected function checkVersionName(string $versionName): void
    {
        if (preg_match('/[a-z0-9]+/i', $versionName) === 0) {
            throw new \Exception('Invalid version name provided.');
        }
    }
}
