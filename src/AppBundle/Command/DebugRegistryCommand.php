<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Helper\TableSeparator;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class DebugRegistryCommand extends Command
{
    private bool $usePermanentCache;
    private ?\Wizacha\Registry $registry;

    protected function configure()
    {
        $this
            ->setName('debug:registry')
            ->setDescription('Displays the content of the legacy Registry')
            ->addOption('key', null, InputOption::VALUE_REQUIRED, 'Shows cache for a specific key')
            ->addOption('keys', null, InputOption::VALUE_NONE, 'Displays Registry keys')
            ->addOption('permanent', 'p', InputOption::VALUE_NONE, 'Use permanent cache instead of local cache')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $io = new SymfonyStyle($input, $output);
        $io->title("Legacy Registry");

        $this->usePermanentCache = true === $input->getOption('permanent');
        $this->registry = \Wizacha\Registry::defaultInstance();

        if (true === $input->getOption('keys')) {
            $table = new Table($output);
            $table->setHeaders(['Key', 'Type', 'Size']);
            $total = 0;
            foreach ($this->getKeys() as $key) {
                $metadata = $this->getMetadata($key);
                $total += $metadata[2];
                $table->addRow($metadata);
            }
            $table->addRow(new TableSeparator());
            $table->addRow(['Total', '', $total]);
            $table->render();
            return 0;
        }

        if (null !== $input->getOption('key')) {
            $key = $input->getOption('key');
            $output->writeln($this->serialize($this->getValue($key)));
            return 0;
        }

        foreach ($this->getKeys() as $key) {
            $io->section($key);
            $output->writeln($this->serialize($this->getValue($key)));
            $io->newLine();
        }
        return 0;
    }

    private function serialize($value): string
    {
        switch (\gettype($value)) {
            case 'object':
                return 'Object ' . \get_class($value);
                break;
            default:
                return json_encode($value);
        }
    }

    /**
     * @return string[]
     */
    private function getKeys(): array
    {
        if ($this->usePermanentCache) {
            return $this->registry->getAliasKeys();
        }

        return $this->registry->getLocalKeys();
    }

    /**
     * @param string $key
     * @return array|mixed|null
     */
    private function getValue(string $key)
    {
        if ($this->usePermanentCache) {
            return $this->registry->cache()->get($this->registry->getPermanentCacheKey($key))[0];
        }

        return $this->registry->get([$key]);
    }

    /**
     * @param string $key
     * @return array
     */
    private function getMetadata(string $key)
    {
        if ($this->usePermanentCache) {
            $var = $this->getValue($key);
            $memory_usage = memory_get_usage();
            $type = \gettype($var);
            unset($var);
            $usage = $memory_usage - memory_get_usage();
            return [ $key, $type, $usage ];
        }

        $memory_usage = memory_get_usage();
        $type = \gettype($this->registry->get([$key]));
        $this->registry->del([$key]);
        $usage = $memory_usage - memory_get_usage();
        return [ $key, $type, $usage ];
    }
}
