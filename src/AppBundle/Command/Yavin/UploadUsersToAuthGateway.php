<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command\Yavin;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Messenger\Envelope;
use Wizacha\Marketplace\Messenger\BroadcastPublisher;
use Wizacha\Marketplace\User\UserService;

class UploadUsersToAuthGateway extends Command
{
    protected static $defaultName = 'user:init-auth-gateway';
    private UserService $userService;
    private BroadcastPublisher $broadcastPublisher;

    public function __construct(UserService $userService, BroadcastPublisher $broadcastPublisher)
    {
        parent::__construct();
        $this->userService = $userService;
        $this->broadcastPublisher = $broadcastPublisher;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Upload every existing user into the microservice auth-gateway.')
        ;
    }

    /** {@inheritdoc} */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $totalUsers = $this->userService->count();
        $i = $nbUsers = 0;

        while ($nbUsers < $totalUsers) {
            $users = $this->userService->findWithPagination($i, 100);

            foreach ($users as $user) {
                $envelope = $this->broadcastPublisher->publish(
                    'user.created',
                    [
                        'apiKey' => $user->getApiKey(),
                        'userId' => $user->getUserUuid()->toString(),
                        'lastName' => $user->getLastname(),
                        'firstName' => $user->getFirstname(),
                        'email' => $user->getEmail(),
                        'companyId' => $user->getCompanyId(),
                        'type' => $user->getUserType(),
                        'isActivated' => $user->isEnabled(),
                    ],
                    []
                );
                if ($envelope instanceof Envelope) {
                    $io->success("User {$user->getFirstname()} {$user->getLastname()} ({$user->getEmail()}) successfully created.");
                } else {
                    // Already logged in BroadcastPublisher
                    $io->error('Unable to publish message.');
                }
            }

            $this->userService->clearEntityManager();
            $i++;
            $nbUsers += 100;
        }

        return 0;
    }
}
