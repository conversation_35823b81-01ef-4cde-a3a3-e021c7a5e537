<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\Question;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Marketplace\Order\Repository\OrderRepository;

class RemovePromotionsOnIrrelevantOrdersCommand extends Command
{
    /** @var OrderRepository */
    protected $orderRepository;

    protected const DESCRIPTION = 'Remove Promotions On Irrelevant Orders';

    protected static $defaultName = 'orders:remove:irrelevant-promotions';

    public function __construct(OrderRepository $orderRepository)
    {
        parent::__construct();

        $this->orderRepository = $orderRepository;
    }

    protected function configure(): void
    {
        $this->setDescription(static::DESCRIPTION);
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title(static::DESCRIPTION);

        $toBeUpdatedOrderIds = $this->orderRepository->prepareOrdersToUpdate();

        $ordersCount = \count($toBeUpdatedOrderIds);

        // stats
        $io->{
        $ordersCount > 0 ? 'error' : 'success'
        }(
            sprintf(
                'Order count: %d. Order Ids: %s.',
                $ordersCount,
                $ordersCount > 0 ? implode(",", $toBeUpdatedOrderIds) : "none"
            )
        );

        if ($ordersCount > 0) {
            $response = $io->askQuestion(
                new Question(
                    sprintf(
                        "Answer REALLY, if you REALLY want to remove promotions on %d order()s. Order ids: %s",
                        $ordersCount,
                        implode(",", $toBeUpdatedOrderIds)
                    )
                )
            );

            if ('REALLY' === $response) {
                // we commit updates here only if we REALLY want to
                $this->orderRepository->getConnection()->commit();
            } else {
                $this->orderRepository->getConnection()->rollBack();
            }
        }

        return 0;
    }
}
