<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class DeleteOrdersAmountsCommand extends Command
{
    private Connection $connection;

    public function __construct(Connection $connection)
    {
        parent::__construct();
        $this->connection = $connection;
    }

    protected function configure(): void
    {
        $this
            ->setName('orders-amounts:delete')
            ->setDescription('Delete orders amounts')
            ->addArgument(
                'pivotDate',
                InputArgument::REQUIRED,
                'Pivot date, format d-m-Y H:i:s'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $pivotDate = $input->getArgument('pivotDate');
        $pivotDatetime = \DateTime::createFromFormat('d-m-Y H:i', $pivotDate);

        if (false === $pivotDatetime) {
            $output->writeln('Invalid date format, d-m-Y H:i expected');
            return 2;
        }

        $output->writeln('Delete orders amounts after ' . $pivotDate . '...');

        $query = $this->connection->prepare(<<<SQL
            SET foreign_key_checks = 0;

            DELETE FROM doctrine_order_item_data
            WHERE id IN (SELECT order_item_data_id FROM doctrine_amount_item dai
            JOIN cscart_orders o ON dai.order_id = o.order_id WHERE timestamp > :pivotDate);

            DELETE FROM doctrine_shipping_amounts
            WHERE id IN (SELECT shipping_amount_id FROM doctrine_order_amounts doa
            JOIN cscart_orders o ON doa.order_id = o.order_id WHERE timestamp > :pivotDate);

            DELETE FROM doctrine_amount_item
            WHERE order_id IN (SELECT order_id FROM cscart_orders WHERE timestamp > :pivotDate);

            DELETE FROM doctrine_order_amounts
            WHERE order_id IN (SELECT order_id FROM cscart_orders WHERE timestamp > :pivotDate);

            SET foreign_key_checks = 1;
        SQL);

        $query->execute(['pivotDate' => $pivotDatetime->getTimestamp()]);
        $output->writeln('Done');

        return 0;
    }
}
