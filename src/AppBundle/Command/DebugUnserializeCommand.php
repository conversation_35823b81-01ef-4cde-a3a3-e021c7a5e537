<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class DebugUnserializeCommand extends Command
{
    public const DEFAULT_PATH = '-';

    protected static $defaultName = 'debug:unserialize';

    protected function configure()
    {
        $this
            ->setDescription(
                "Unserialize job payload"
            )
            ->addArgument(
                'path',
                InputArgument::OPTIONAL,
                \sprintf(
                    'file path (%s for STDIN)',
                    static::DEFAULT_PATH
                ),
                static::DEFAULT_PATH
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('Unserialized data');

        $path = $input->getArgument('path');

        if ($path === static::DEFAULT_PATH) {
            $path = 'php://stdin';
        }

        $data = \file_get_contents(
            $path
        );

        // phpcs:ignore
        var_dump(
            \unserialize($data)
        );

        return 1;
    }
}
