<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright Copyright (c) Wizaplace
 * @license   Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Core\ErrorProofUnserializer\ErrorProofUnserializer;
use Wizacha\Marketplace\Order\OrderDataType;

class FixCorruptedSerializedDataCommand extends Command
{
    protected static $defaultName = 'fix:corrupted-serialization';

    /** @var Connection */
    protected $connection;

    public function __construct(
        Connection $connection
    ) {
        parent::__construct();

        $this->connection = $connection;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Fix corrupted serialization')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('Fix corrupted serialization');

        $checks = [
            [
                'table' => 'cscart_order_data',
                'columns' => ['data'],
                'filter' => \sprintf(
                    'WHERE `type` IN ("%s","%s","%s")',
                    OrderDataType::GROUP_INFO(),
                    OrderDataType::SHIPPING_INFO(),
                    OrderDataType::TAX_INFO()
                )
            ],
        ];

        foreach ($checks as $check) {
            $this->process($io, ...\array_values($check));
        }

        return 0;
    }

    private function getPrimaryKeys(string $table)
    {
        $statement = $this->connection->prepare(
            <<<SQL
            SHOW INDEX
            FROM $table
            WHERE Key_name = 'PRIMARY'
            SQL
        );
        $statement->execute();

        return \array_column(
            $statement->fetchAllAssociative(),
            'Column_name',
        );
    }

    private function process(
        SymfonyStyle $io,
        string $table,
        array $columns,
        ?string $filter = null
    ) {
        $io->section($table);

        $keys = $this->getPrimaryKeys($table);

        $readStatement = $this->connection->prepare(
            \sprintf(
                <<<SQL
                SELECT %s
                FROM %s
                %s
                SQL,
                \implode(
                    ', ',
                    [
                        ...$columns,
                        ...$keys,
                    ]
                ),
                $table,
                $filter
            )
        );

        $updateStatement = $this->connection->prepare(
            \sprintf(
                <<<SQL
                    UPDATE %s
                    SET %s
                    WHERE %s
                    SQL
                ,
                $table,
                \implode(
                    ', ',
                    \array_map(
                        fn(string $column) => "$column = :$column",
                        $columns
                    )
                ),
                \implode(
                    ' AND ',
                    \array_map(
                        fn(string $key) => "$key = :$key",
                        $keys
                    )
                )
            )
        );

        $readStatement->execute();
        $total = $readStatement->rowCount() * \count($columns);
        $progressBar = $io->createProgressBar($total);

        $errors = 0;

        $this->connection->beginTransaction();

        foreach ($readStatement->iterateAssociative() as $row) {
            $updates = [];

            foreach ($columns as $column) {
                $serialized = $row[$column];
                $unserializer = new ErrorProofUnserializer($serialized);
                $repaired = $unserializer->repairIncorrectLength();

                if ($serialized !== $repaired) {
                    $errors++;
                    $updates[$column] = $repaired;
                }

                $progressBar->advance();
            }

            if (\count($updates) > 0) {
                $updateStatement->execute(
                    \array_merge(
                        $row,
                        $updates
                    )
                );
            }
        }

        $progressBar->finish();
        $io->newLine(2);

        if ($errors > 0) {
            $io->error(
                \sprintf(
                    '%s corrupted / %s serialized string(s)',
                    $errors,
                    $total
                )
            );

            $confirmation = $io->confirm(
                \sprintf(
                    'Do you want to commit repair attempts (%s modifications) ?',
                    $errors
                ),
                false
            );

            if ($confirmation) {
                $this->connection->commit();
                $io->success('Modification applied');
            } else {
                $this->connection->rollBack();
                $io->warning('ABORTED, no modification commited');
            }
        } else {
            $io->success('Everything is "OK"');
        }
    }
}
