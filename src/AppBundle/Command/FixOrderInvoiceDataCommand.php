<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Marketplace\Order\Discount\Exception\EmptyDiscountException;
use Wizacha\Marketplace\Order\Discount\OrderDiscountsCalculator;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\OrderStatus;
use Wizacha\Money\Money;
use Wizacha\Storage\StorageService;

/**
 * This command extracts all orders with a discount, and recalculate its VAT properly.
 * This a quick workaround to an underlying issue with W<PERSON><PERSON>'s invoice VAT calculation.
 *
 * @see https://wizaplace.atlassian.net/browse/WIZ-6953
 */
class FixOrderInvoiceDataCommand extends Command
{
    protected static $defaultName = 'orders:invoice-vat';

    protected OrderService $orderService;

    protected OrderDiscountsCalculator $calculator;

    private StorageService $assetsStorageService;

    public function __construct(
        OrderService $orderService,
        OrderDiscountsCalculator $calculator,
        StorageService $assetsStorageService
    ) {
        parent::__construct();

        $this->orderService = $orderService;
        $this->calculator = $calculator;
        $this->assetsStorageService = $assetsStorageService;
    }


    protected function configure()
    {
        $this
            ->setDescription(
                'Recalculate discount related orders VAT'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $count = $skipped = 0;
        $existingTaxes = [];
        $csvLine = [];

        // Recalculate VAT on every order with a discount
        foreach ($this->orderService->getOrders() as $order) {
            if (\count($order->getPromotionIds() ?? []) === 0
                || \in_array(
                    $order->getStatus(),
                    [OrderStatus::CANCELED(), OrderStatus::VENDOR_DECLINED(), OrderStatus::BILLING_FAILED()]
                )
            ) {
                continue;
            }

            ++$count;
            $output->writeln(sprintf(
                "[%04d] Processing order #%05d (with %d promotions)",
                $count,
                $order->getId(),
                \count($order->getPromotionIds() ?? [])
            ));

            // legacy
            $orderData = $this->orderService->overrideLegacyOrder($order->getId());

            try {
                $invoiceData = $this->calculator->buildInvoiceData($orderData, $this->isTaxIncluded($orderData));
            } catch (EmptyDiscountException $e) {
                $output->writeln(sprintf("[%04d] Empty discount - Skipping", $count));
                ++$skipped;

                continue;
            }

            $csvLine[] = $invoiceData['total'];
            $existingTaxes = array_merge($existingTaxes, array_keys($invoiceData['total']['taxes']));
        }

        // Write the CSV in memory
        $handler = fopen('php://memory', 'w');
        foreach ($csvLine as $key => $line) {
            $line = $this->formatLine($line, $existingTaxes);

            // CSV headers
            if (0 === $key) {
                fputcsv($handler, array_keys($line), ';');
            }

            fputcsv($handler, $line, ';');
        }

        rewind($handler);
        $csvContent = stream_get_contents($handler);
        fclose($handler);

        // Send the CSV in the configured FS
        [, $filename] = $this->assetsStorageService->put(
            'discounted_invoices_vat.csv',
            [
                'contents' => $csvContent,
                'overwrite' => true,
            ]
        );

        $output->writeln('---');
        $output->writeln(sprintf(
            'Done! Processed %d orders with discount - skipped %d due to empty discount',
            $count,
            $skipped
        ));
        $output->writeln('CSV: ' . $this->assetsStorageService->getAbsolutePath($filename));
    }

    /**
     * @param mixed[] $line CSV line to format
     * @param string[] $existingTaxes List of all tax rates that needs to be displayed in output file
     *
     * @return string[] Formatted CSV line
     */
    protected function formatLine(array $line, array $existingTaxes): array
    {
        // Make sure that every line has a key set for every existing tax
        $taxes = array_merge(
            array_map(function () {
                return 0;
            }, array_flip($existingTaxes)),
            $line['taxes']
        );

        unset($line['taxes']);
        $line = array_merge($line, $taxes);

        // Replace array keys with human readable labels
        $line = array_combine(
            array_map(
                function (string $column): string {
                    return $this->getColumnTranslation($column);
                },
                array_keys($line)
            ),
            array_values($line)
        );

        // Replace all Money instances with displayable strings
        $line = array_map(function ($entry): string {
            return $entry instanceof Money ? (string) $entry->getConvertedAmount() : (string) $entry;
        }, $line);

        return $line;
    }

    /** @param mixed[] */
    protected function isTaxIncluded(array $orderInfo): bool
    {
        $isTaxIncludedInPrice = false;
        if (\count($orderInfo['taxes'] ?? []) > 0) {
            $isTaxIncludedInPrice = reset($orderInfo['taxes'])['price_includes_tax'] === 'Y';
        }

        return $isTaxIncludedInPrice;
    }

    protected function getColumnTranslation(string $key): string
    {
        return [
            'orderId' => 'Order #',
            'companyId' => 'Marchand #',
            'subtotalExclTaxes' => 'Sous-total HT',
            'subtotalInclTaxes' => 'Sous-total TTC',
            'shippingCostExclTaxes' => 'Frais de port HT',
            'shippingCostInclTaxes' => 'Frais de port TTC',
            'discount' => 'Réduction TTC',
            'totalTaxes' => 'Total taxes',
            'totalExclTaxes' => 'Total HT',
            'totalInclTaxes' => 'Total TTC',
            '0.0' => '0%',
            '0.2' => 'TVA 20%',
            '0.1' => 'TVA 10%',
            '0.055' => 'TVA 5.5%',
            '0.021' => 'TVA 2.1%',
        ][$key] ?? $key;
    }
}
