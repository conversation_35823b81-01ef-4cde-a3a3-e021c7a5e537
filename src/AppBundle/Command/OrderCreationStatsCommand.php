<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Tygh\Database;

class OrderCreationStatsCommand extends Command
{
    protected function configure()
    {
        $this
            ->setName('stats:order:created')
            ->setDescription('Return number of order created between two dates')
            ->addArgument('start', InputArgument::REQUIRED, 'Start date in ISO format')
            ->addArgument('end', InputArgument::REQUIRED, 'End date in ISO format')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $start = new \DateTime($input->getArgument('start'));
        $end = new \DateTime($input->getArgument('end'));

        $resultats = Database::getRow(
            "SELECT count(*) as count, GROUP_CONCAT(order_id) as ids
              FROM ?:orders
              WHERE timestamp > ?i AND timestamp<= ?i
              AND is_parent_order = 'N';",
            $start->getTimestamp(),
            $end->getTimestamp()
        );
        $output->writeln('Order count : ' . $resultats['count'] . ' Order ids : ' . $resultats['ids']);
    }
}
