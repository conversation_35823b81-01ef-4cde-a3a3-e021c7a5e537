<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Broadway\ReadModel\RepositoryInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Stopwatch\Stopwatch;
use Wizacha\Bridge\Broadway\DatabaseRepository;
use Wizacha\Marketplace\User\UserRepository;

class BasketsDeleteAllCommand extends Command
{
    /** @var DatabaseRepository */
    protected $databaseRepository;
    /** @var UserRepository */
    protected $userRepository;
    /** @var Stopwatch */
    protected $stopWatch;

    public function __construct(RepositoryInterface $databaseRepository, UserRepository $userRepository)
    {
        parent::__construct();
        if ($databaseRepository instanceof DatabaseRepository === false) {
            throw new \InvalidArgumentException(
                'The first argument must be instance of DatabaseRepository, found instance of ' . \get_class($databaseRepository)
            );
        }
        $this->databaseRepository = $databaseRepository;
        $this->userRepository = $userRepository;
        $this->stopWatch = new Stopwatch();
    }

    protected function configure()
    {
        $this->setName('clear:baskets')
            ->setDescription('Delete ALL baskets');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Reset all baskets for all users - Start: [' . (new \DateTime())->format('G:i:s') . ']');
        $this->stopWatch->start('main');

        $numberOfBasketsReset = $this->userRepository->resetAllUsersBaskets();
        $this->databaseRepository->removeAll();

        $event = $this->stopWatch->stop('main');
        $io->success(sprintf("%d user baskets  have been reset in %d ms and %d bytes.", $numberOfBasketsReset, $event->getDuration(), $event->getMemory()));
        $io->text('End of script: [' . (new  \DateTime())->format('G:i:s') . ']');

        return 0;
    }
}
