<?php

/**
 *  <AUTHOR> DevTeam <<EMAIL>>
 *  @copyright   Copyright (c) Wizacha
 *  @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\AppBundle\Service\TranslationService;

/**
 * Cette commande va créer les clés qui seraient manquantes pour toutes les langues dispos dans la MP
 */
class TranslationMissingKeysCommand extends Command
{
    private TranslationService $translationService;

    public function __construct(TranslationService $translationService)
    {
        parent::__construct();

        $this->translationService = $translationService;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this
            ->setName('marketplace:translation:missing-keys')
            ->setDescription('Create missing keys for the translations of all available locales.')
            ->setHelp(<<<'EOF'
The <info>%command.name%</info> create all missing keys for the translations of all available locales.
EOF
            )
        ;
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $io = new SymfonyStyle($input, $output);

        $affectedRows = $this->translationService->createMissingKeys();

        $io->success("Missing translations keys have been created.");
        $io->block(
            \json_encode($affectedRows, JSON_PRETTY_PRINT)
        );
    }
}
