<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Component\AuthLog\AuthLogRepository;

class AuthLogClearCommand extends Command
{
    private AuthLogRepository $repository;

    public function __construct(AuthLogRepository $repository)
    {
        $this->repository = $repository;

        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setName('clear:authlogs')
            ->setDescription('Delete all authlogs')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title($this->getDescription());

        if (false === $io->confirm("This is a destructive operation. Do you confirm?")) {
            $io->warning("Cancelled.");

            return 1;
        }

        $qty = $this->repository->deleteAll();

        $io->success("Records deleted: " . $qty);

        return 0;
    }
}
