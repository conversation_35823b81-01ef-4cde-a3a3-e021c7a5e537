<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class DebugVariablesCommand extends Command
{
    private string $projectDir;

    /**
     * DebugVariablesCommand constructor.
     */
    public function __construct(string $projectDir)
    {
        parent::__construct();
        $this->projectDir = $projectDir;
    }

    protected function configure()
    {
        $this
            ->setName('debug:variables')
            ->setDescription('Displays all build variables used by Phing')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $io = new SymfonyStyle($input, $output);
        $io->title("Build Variables");

        $variables = [];

        $element = new \SimpleXMLElement($this->projectDir . '/build/config.xml', 0, true);
        foreach ($this->properties($element) as $attributes) {
            $variables[(string) $attributes['propertyName']] = $attributes;
        }

        ksort($variables);

        $table = new Table($output);
        $table->setHeaders(['Variable name', 'Description']);
        foreach ($variables as $name => $attributes) {
            $table->addRow([$name, $attributes['promptText']]);
        }
        $table->render();

        return 0;
    }

    private function properties(\SimpleXMLElement $element): \Generator
    {
        foreach ($element->children() as $child) {
            yield from $this->properties($child);
        }
        if ('propertyprompt' === $element->getName()) {
            yield $element->attributes();
        }
    }
}
