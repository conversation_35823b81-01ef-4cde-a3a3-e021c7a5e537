<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright Copyright (c) Wizaplace
 * @license   Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\Question;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Marketplace\Image\ImageLinkType;

class FixDuplicatedMainImageLinksCommand extends Command
{
    protected static $defaultName = 'fix:duplicated-main-image-links';

    /** @var Connection */
    protected $connection;

    public function __construct(
        Connection $connection
    ) {
        parent::__construct();

        $this->connection = $connection;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Fix duplicated main image links')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title(
            $this->getDescription()
        );

        $statement = $this
            ->connection
            ->prepare(
                <<<SQL
                DELETE
                    cil
                FROM
                    cscart_images_links cil
                    JOIN cscart_images_links cil2
                WHERE
                    cil.`type` = :type
                    AND cil.object_type = cil2.object_type
                    AND cil.pair_id < cil2.pair_id
                    AND cil2.`type` = cil.`type`
                    AND cil.object_id = cil2.object_id
                SQL
            )
        ;

        $this->connection->beginTransaction();

        $statement->execute(
            ['type' => ImageLinkType::MAIN()]
        );

        $total = $statement->rowCount();

        if ($total === 0) {
            $io->success(
                'No duplicates'
            );

            return 0;
        }

        $response = $io->askQuestion(
            new Question(
                \sprintf(
                    'Answer REALLY, if you REALLY want to remove %s duplicated links',
                    $total
                )
            )
        );

        if ('REALLY' === $response) {
            $this->connection->commit();

            $io->success(
                \sprintf(
                    '%s duplicates links deleted',
                    $total
                )
            );

            return 0;
        }

        $this->connection->rollBack();

        $io->warning('Aborted');

        return 0;
    }
}
