<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Component\Import\EximJob;
use Wizacha\Component\Import\EximJobService;
use Wizacha\Component\Import\JobStatus;

class ListEximJobsImport extends Command
{
    private EximJobService $eximJobService;

    public function __construct(EximJobService $eximJobService)
    {
        parent::__construct();

        $this->eximJobService = $eximJobService;
    }

    protected function configure(): void
    {
        parent::configure();

        $this
            ->setName('exim:jobs:list')
            ->setDescription('get list of exim jobs.')
            ->addOption(
                'company',
                null,
                InputOption::VALUE_OPTIONAL,
                'The id of the company.'
            )
            ->addOption(
                'status',
                null,
                InputOption::VALUE_OPTIONAL,
                'The status of the job.'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $table = new Table($output);

        $table->setHeaders([
            'jobId',
            'companyId',
            'status',
        ]);

        $companyId = $input->hasOption('company') === true ? \intval($input->getOption('company')) : null;

        try {
            $status = $input->hasOption('status') === true ? new JobStatus($input->getOption('status')) : null;
        } catch (\Exception $e) {
            $io->error('Status is invalid. It must be either ' . implode(',', JobStatus::values()));

            return 1;
        }

        $list = $this->eximJobService->getCompanyJobByStatus($companyId, $status);

        $rows = \array_map(function (EximJob $job) use ($table): array {
            return [
                $job->getId(),
                $job->getCompanyId(),
                $job->getStatus()
            ];
        }, $list);

        $table->setRows($rows);
        $table->render();

        return 0;
    }
}
