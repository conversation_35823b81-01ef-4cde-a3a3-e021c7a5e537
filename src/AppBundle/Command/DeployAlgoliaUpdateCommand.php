<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class DeployAlgoliaUpdateCommand extends Command implements DeployPostActionsInterface
{
    public function getPriority(): int
    {
        return 100;
    }

    protected function configure(): void
    {
        $this
            ->setName('deploy:command:update-algolia')
            ->setDescription('Update Algolia indices')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $command = $this->getApplication()->find('algolia:index:create');
        $command->run(new ArrayInput([]), $output);

        $command = $this->getApplication()->find('algolia:config:push');
        $command->run(new ArrayInput([]), $output);

        return 0;
    }
}
