<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class ClearPaymentDataBeforeProductionCommand extends Command
{
    private Connection $connection;

    public function __construct(Connection $connection)
    {
        parent::__construct();

        $this->connection = $connection;
    }

    protected function configure()
    {
        $this
            ->setName('psp:wallets:reset')
            ->setDescription('Clear sandbox payment data (wallets); DO NOT RUN IN PRODUCTION IF YOU DON\'T KNOW THE CONSEQUENCES!')
            ->addOption('force', 'f', InputOption::VALUE_NONE)
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        if (!$input->getOption('force')) {
            $output->writeln('<error>YOU FOOL! Use the force, if you know what you are doing only!</error>');

            return;
        }

        $this->connection->exec('UPDATE cscart_companies SET mangopay_id=NULL, hipay_id=NULL, stripe_id=NULL');
        $this->connection->exec('TRUNCATE doctrine_user_payment_info');

        $output->writeln('<comment>Ok</comment>');
    }
}
