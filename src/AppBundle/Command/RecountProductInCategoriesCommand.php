<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class RecountProductInCategoriesCommand extends Command
{
    protected function configure()
    {
        $this
            ->setName('categories:recount-products')
            ->setDescription('Recount all products in category')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        \Wizacha\Category::synchronizeCount();

        $output->writeln('Ok');
    }
}
