<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\ContainerAwareInterface;
use Symfony\Component\DependencyInjection\ContainerAwareTrait;
use Symfony\Component\Process\Exception\ProcessFailedException;
use Symfony\Component\Process\Process;

class DeployPostActionsCommand extends Command implements ContainerAwareInterface
{
    use ContainerAwareTrait;

    protected function configure()
    {
        $this
            ->setName('deploy:post-actions')
            ->setDescription('Execute action required after a deployment')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $message = $this->migrate();
        $output->writeln($message);

        $command = $this->getApplication()->find('theme:import-translations');
        $input = new ArrayInput([]);
        $command->run($input, $output);

        if ($this->container->getParameter('queue.type') === \Wizacha\Async\Config::TYPE_AMQP) {
            $command = $this->getApplication()->find('amqp:create-queues');
            $input = new ArrayInput([]);
            $command->run($input, $output);
        }

        $command = $this->getApplication()->find('deploy:run');
        $input = new ArrayInput([]);
        $command->run($input, $output);
    }

    protected function migrate(): string
    {
        $path = $this->container->getParameter('kernel.project_dir') . '/';
        $command = 'vendor/bin/phinx migrate';

        $process = new Process($path . $command);
        $process->run();

        if (!$process->isSuccessful()) {
            throw new ProcessFailedException($process);
        }

        return $process->getOutput();
    }
}
