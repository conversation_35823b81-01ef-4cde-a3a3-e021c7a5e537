<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\DependencyInjection\ContainerAwareInterface;
use Symfony\Component\DependencyInjection\ContainerAwareTrait;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Wizacha\Marketplace\Order\Event\OrderTrashed;
use Wizacha\Marketplace\Order\OrderEvents;
use Wizacha\Marketplace\Order\OrderStatus;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class TrashAbandonedOrdersCommand extends Command implements ContainerAwareInterface
{
    use ContainerAwareTrait;

    protected EventDispatcherInterface $eventDispatcher;

    public function __construct(
        EventDispatcherInterface $eventDispatcher
    ) {
        parent::__construct();
        $this->eventDispatcher = $eventDispatcher;
    }

    protected function configure()
    {
        $this
            ->setName('orders:trash-abandoned')
            ->setDescription('Mark as trashed abandoned orders');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $orders = $this
            ->container
            ->get('marketplace.order.order_service')
            ->getAbandonedOrders();
        $trash = $this
            ->container
            ->get('marketplace.order.action.trash');

        foreach ($orders as $order) {
            $trash->execute($order);
            fn_change_order_status($order->getId(), (string) OrderStatus::INCOMPLETED());
            $this->eventDispatcher->dispatch(
                new OrderTrashed($order->getId()),
                OrderEvents::TRASHED,
            );
        }

        $output->writeln(sprintf("<comment>%s</comment> deleted(s) order(s)", \count($orders)));
    }
}
