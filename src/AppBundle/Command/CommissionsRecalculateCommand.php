<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Driver\Connection;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Marketplace\Commission\Commission;
use Wizacha\Marketplace\Commission\CommissionService;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\OrderStatus;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Console\Input\InputOption;
use Wizacha\Money\Money;

class CommissionsRecalculateCommand extends Command
{
    protected static $defaultName = 'commissions:recalculate';

    /** @var Connection */
    private $connection;

    /** @var CommissionService */
    private $commissionService;

    /** @var OrderService */
    private $orderService;

    public function __construct(
        Connection $connection,
        CommissionService $commissionService,
        OrderService $orderService
    ) {
        parent::__construct();

        $this->connection = $connection;
        $this->commissionService = $commissionService;
        $this->orderService = $orderService;
    }

    protected function configure()
    {
        $this
            ->setDescription(
                'Get all orders with a commission to 0, funds not dispatched, '
                . 'but with a company commission that should not allow it. '
                . 'Or recalculate commissions for specified order Ids'
            )
            ->addOption(
                'order-id',
                'o',
                (
                    InputOption::VALUE_REQUIRED
                    | InputOption::VALUE_IS_ARRAY
                ),
                'Update specific order Id(s)'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $io = new SymfonyStyle($input, $output);

        $this->updateCompanyCommissions($output);

        $orderIds
            = $input->getOption('order-id')
            ?: array_column(
                $this->getOrdersWithZeroCommission($output),
                'orderId'
            )
        ;

        foreach ($orderIds as $orderId) {
            $commissionAmount = $this->processOrder(
                (int) $orderId,
                $io
            );

            $io->success(
                "Order #{$orderId} has been updated " .
                "with a commission of {$commissionAmount->getConvertedAmount()}"
            );
        }
    }

    protected function processOrder(int $orderId): Money
    {
        $order = ($this->orderService->getOrder($orderId));

        return $this->commissionService
            ->saveMarketPlaceCommission($order)
            ->getMarketplaceCommission($order)
        ;
    }

    protected function getOrdersWithZeroCommission(OutputInterface $output): array
    {
        $orders = [];

        // Get all orders not completed or cancelled
        $queryOrders = $this
            ->connection
            ->prepare(
                '
                    SELECT order_id, company_id
                    FROM cscart_orders
                    WHERE status != :completed
                    AND status != :cancelled
                '
            )
        ;
        $queryOrders->execute([
            'completed' => OrderStatus::COMPLETED,
            'cancelled' => OrderStatus::CANCEL,
        ]);
        $output->writeln("<info>{$queryOrders->rowCount()} orders found.</info>");

        while ($rowOrder = $queryOrders->fetch()) {
            $fundsDispatched = false;
            $output->writeln("<info>Order #{$rowOrder['order_id']} for company #{$rowOrder['company_id']}.</info>");

            $queryTraceOrder = $this
                ->connection
                ->prepare(
                    '
                        SELECT action
                        FROM orders_actions_traces
                        WHERE order_id = :order_id
                    '
                )
            ;
            $queryTraceOrder->execute([
                'order_id' => $rowOrder['order_id'],
            ]);

            while ($rowTrace = $queryTraceOrder->fetch()) {
                if ($rowTrace['action'] === 'DispatchFunds') {
                    $fundsDispatched = true;
                    $output->writeln("<info>Funds already dispatched for order #{$rowOrder['order_id']}.</info>");
                }
            }

            if ($fundsDispatched === false) {
                $queryOrderCommission = $this
                    ->connection
                    ->prepare(
                        '
                            SELECT payout_id, commission_amount
                            FROM cscart_vendor_payouts
                            WHERE order_id = :order_id
                            AND company_id = :company_id
                        '
                    )
                ;
                $queryOrderCommission->execute([
                    'order_id' => $rowOrder['order_id'],
                    'company_id' => $rowOrder['company_id'],
                ]);

                $rowPayout = $queryOrderCommission->fetch();

                if (\is_array($rowPayout) === true
                    && \array_key_exists('commission_amount', $rowPayout)
                    && (float) $rowPayout['commission_amount'] === 0.
                ) {
                    $companyCommission = $this->commissionService->getCompanyCommission((int) $rowOrder['company_id']);

                    if ($companyCommission instanceof Commission
                        && (($companyCommission->getPercentAmount() > 0 || $companyCommission->getFixAmount() > 0)
                        && $companyCommission->getMaximumAmount() === null)
                    ) {
                        $output->writeln("<info>Order #{$rowOrder['order_id']} has 0 commission.</info>");
                        $orders[] = ['orderId' => $rowOrder['order_id'], 'companyId' => $rowOrder['company_id']];
                    }
                } else {
                    $output->writeln(
                        "<info>The commission for order #{$rowOrder['order_id']} has a right amount.</info>"
                    );
                }
            }
        }

        return $orders;
    }

    protected function updateCompanyCommissions(OutputInterface $output): void
    {
        $commissions = $this->commissionService->getAllCompanyCommissions();

        foreach ($commissions as $commission) {
            if ($commission->getMaximumAmount() === 0.) {
                $commission->setMaximumAmount(null);
                $this->commissionService->saveCommission($commission);
                $output->writeln(
                    "<info>Maximum commission amount for company #{$commission->getCompanyId()} has been set to null.</info>"
                );
            }
        }
    }
}
