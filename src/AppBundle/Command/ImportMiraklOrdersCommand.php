<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

/** @see https://wizaplace.atlassian.net/browse/WIZ-3880 */
set_time_limit(0);
ini_set('memory_limit', '1G');

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Exim\MiraklOrder\MiraklOrderCsvProcessor;
use Wizacha\Marketplace\Traits\MonitoringTrait;

/**
 * Import mirakl order data from the mirakl order api extracted data
 * see the ETL project extract:mirakl:orders command
 */
class ImportMiraklOrdersCommand extends Command
{
    use MonitoringTrait;

    public const DEFAULT_PATH = 'php://stdin';

    protected static $defaultName = 'import:mirakl:orders';

    /** @var MiraklOrderCsvProcessor */
    protected $csvProcessor;

    public function __construct(MiraklOrderCsvProcessor $csvProcessor)
    {
        parent::__construct();

        $this->csvProcessor = $csvProcessor;
    }

    protected function configure()
    {
        $this
            ->setDescription(
                'Import ETL mirakl orders csv'
            )
            ->addArgument(
                'path',
                InputArgument::OPTIONAL,
                'optional file path, use standard input (php://stdin) if empty'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $path = $input->getArgument('path') ?? static::DEFAULT_PATH;

        $count = 0;
        $start = time();

        foreach ($this->csvProcessor->process($path) as $error) {
            ++$count;
            $output->writeln(
                sprintf(
                    '[%5d] %5ds - %s - RAM: %.2f%%',
                    $count,
                    time() - $start,
                    $error,
                    $this->freeMemoryRatio() * 100
                )
            );
        }

        return 0;
    }
}
