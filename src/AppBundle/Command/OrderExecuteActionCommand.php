<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\ContainerAwareInterface;
use Symfony\Component\DependencyInjection\ContainerAwareTrait;
use Wizacha\Marketplace\Order\OrderService;

class OrderExecuteActionCommand extends Command implements ContainerAwareInterface
{
    use ContainerAwareTrait;

    private OrderService $orderService;

    public function __construct(OrderService $orderService)
    {
        parent::__construct();

        $this->orderService = $orderService;
    }

    protected function configure(): void
    {
        $this
            ->setName('orders:execute-action')
            ->setDescription('Executes an action on an Order')
            ->addArgument('action', InputArgument::REQUIRED, 'The action to execute. This is the last part of its service id (ex: dispatch_funds)')
            ->addArgument('orderId', InputArgument::REQUIRED, 'The id of the Order')
            ->addArgument('optionalArgs', InputArgument::IS_ARRAY | InputArgument::OPTIONAL, 'Optional argument needed by some actions')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $actionId = $input->getArgument('action');
        $orderId = $input->getArgument('orderId');

        $action = $this->container->get(sprintf('marketplace.order.action.%s', $actionId));
        $orders = $this->orderService->getChildOrders($orderId);

        foreach ($orders as $order) {
            if ($action->isAllowed($order)) {
                $action->execute($order, implode(',', $input->getArgument('optionalArgs')));
                $output->writeln(sprintf('<info>Action "%s" successfully executed on order "%s".</info>', $actionId, $order->getId()));
            } else {
                $output->writeln(sprintf('<error>Action "%s" not allowed on order with id "%s".</error>', $actionId, $order->getId()));
            }
        }
    }
}
