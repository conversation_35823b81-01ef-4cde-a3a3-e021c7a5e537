<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Storage\StorageService;

class DeployRemoveAssetsAwsCacheCommand extends Command implements DeployPostActionsInterface
{
    private string $marketplaceVersion;

    private StorageService $staticsStorageService;

    public function __construct(string $marketplaceVersion, StorageService $staticsStorageService)
    {
        parent::__construct();

        $this->marketplaceVersion = $marketplaceVersion;
        $this->staticsStorageService = $staticsStorageService;
    }

    public function getPriority(): int
    {
        return 100;
    }

    protected function configure(): void
    {
        $this
            ->setName('deploy:command:remove-assets-aws-cache')
            ->setDescription('Remove assets cache from AWS')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if ($this->staticsStorageService->getOption("storage") !== 'amazon') {
            return 0;
        }

        $this->staticsStorageService->delete("js/tygh/scripts-" . $this->marketplaceVersion . "-" . AREA . ".js");
        $output->writeln("<info>Javascript cache from AWS cleared successfully.</info>");

        $this->staticsStorageService->deleteDir("design/backend");
        $output->writeln("<info>CSS cache from AWS cleared successfully.</info>");

        return 0;
    }
}
