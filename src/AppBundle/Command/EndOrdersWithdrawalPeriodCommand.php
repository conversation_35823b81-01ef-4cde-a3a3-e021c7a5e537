<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Marketplace\Order\Action\EndWithdrawalPeriod;
use Wizacha\Marketplace\Order\Order;
use Wizacha\Marketplace\Order\OrderService;

class EndOrdersWithdrawalPeriodCommand extends Command
{
    protected static $defaultName = 'orders:end-withdrawal-period';

    /** @var OrderService */
    protected $orderService;

    /** @var EndWithdrawalPeriod */
    protected $endWithdrawalPeriod;

    /** @var int */
    protected $withdrawalPeriod;

    public function __construct(
        int $withdrawalPeriod,
        OrderService $orderService,
        EndWithdrawalPeriod $endWithdrawalPeriod
    ) {
        $this->withdrawalPeriod = $withdrawalPeriod;
        $this->orderService = $orderService;
        $this->endWithdrawalPeriod = $endWithdrawalPeriod;

        // for having $withdrawalPeriod available in configure method
        parent::__construct();
    }

    protected function configure()
    {
        $this
            ->setDescription(
                \sprintf(
                    'End orders withdrawal period after a specific amount of time (> %d d.)',
                    $this->withdrawalPeriod
                )
            )
            ->addOption(
                'dry-run',
                'd',
                InputOption::VALUE_NONE,
                'dry run the command'
            )
        ;
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $dry = $input->getOption('dry-run') ?? true;

        $io = new SymfonyStyle($input, $output);
        $io->title(
            \sprintf(
                'Order end withdrawal period (> %d d.)',
                $this->withdrawalPeriod
            )
        );

        // Récupération des commandes livrées en attente de fin de période de rétractation
        $pendingWithdrawalPeriodEndOrders = $this->orderService->getPendingWithdrawalPeriodEndOrders();

        // Initialisation du compteur
        $endWithdrawalPeriodCount = 0;

        /** @var Order $pendingWithdrawalPeriodEndOrder */
        foreach ($pendingWithdrawalPeriodEndOrders as $order) {
            // Si la période d'attente n'est pas terminée, on ne s'occupe pas
            // de cette commande.
            if (false === $order->withdrawalPeriodIsOver()) {
                continue;
            }

            // Vérification qu'il est possible d'effectuer l'action
            if (true === $this->endWithdrawalPeriod->isAllowed($order)) {
                if (true === $dry) {
                    $io->writeln(
                        \sprintf(
                            (
                                "OrderID %s widthdrawal period ended\n"
                                . "   started: %s\n"
                                . "     ended: %s\n"
                            ),
                            $order->getId(),
                            $order->getWithdrawalStartDate()->format(\DATE_ATOM),
                            $order->getWithdrawalEndDate()->format(\DATE_ATOM)
                        )
                    );
                } else {
                    $this->endWithdrawalPeriod->execute($order);
                }
                $endWithdrawalPeriodCount++;
            }
        }

        $total = \count($pendingWithdrawalPeriodEndOrders);
        $pluriel = $total > 1 ? 's' : '';

        $io->success(
            \sprintf(
                "%s commande%s au status traitée%s, dont %s ont eu leur période de rétractation terminée.",
                $total,
                $pluriel,
                $pluriel,
                $endWithdrawalPeriodCount
            )
        );
    }
}
