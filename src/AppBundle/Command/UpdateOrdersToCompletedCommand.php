<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Bundle\FrameworkBundle\Command\ContainerAwareCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\OrderStatus;

class UpdateOrdersToCompletedCommand extends ContainerAwareCommand
{
    protected function configure()
    {
        $this
            ->setName('orders:update-to-completed')
            ->setDescription('Update orders to completed status')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        OrderStatus::completeProcessed();

        $output->writeln('<info>Orders processed!</info>');
    }
}
