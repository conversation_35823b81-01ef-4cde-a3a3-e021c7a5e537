<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\DependencyInjection\ContainerAwareInterface;
use Symfony\Component\DependencyInjection\ContainerAwareTrait;
use Wizacha\Marketplace\Organisation\Organisation;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\Question;

class DeleteOrganisationsCommand extends Command implements ContainerAwareInterface
{
    use ContainerAwareTrait;

    protected function configure()
    {
        $this
            ->setName('purge:organisations')
            ->setDescription('Delete all invalid organisations');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        if (!$input->isInteractive()) {
            $output->writeln('Non interactive shell, aborting.');

            return;
        }

        $passphrase = 'REALLY';
        $question = new Question(sprintf('Write REALLY if you are REALLY sure of what you are doing: ', $passphrase));

        do {
            $response = $this
                ->getHelperSet()
                ->get('question')
                ->ask($input, $output, $question);
        } while ($response !== $passphrase);

        $dbal = $this->container->get('database_connection');
        $orm = $this->container->get('doctrine.orm.default_entity_manager');
        $organisations = $orm->getRepository(Organisation::class)->findAll();
        $deleted = 0;

        foreach ($organisations as $organisation) {
            if ($organisation->getName() !== '') {
                continue;
            }

            if (\count($organisation->getOrganisationBaskets()) > 0) {
                continue;
            }

            $orm->remove($organisation);
            ++$deleted;
        }

        $dbal->exec('SET foreign_key_checks = 0;');
        $orm->flush();
        $dbal->exec('SET foreign_key_checks = 1;');

        $output->writeln(sprintf("<comment>%s</comment> deleted organisation(s)", $deleted));
    }
}
