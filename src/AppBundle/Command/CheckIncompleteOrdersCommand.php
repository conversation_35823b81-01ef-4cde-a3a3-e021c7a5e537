<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Marketplace\Order\CheckIncompleteOrdersService;

class CheckIncompleteOrdersCommand extends Command
{
    /** @var CheckIncompleteOrdersService */
    private $checkIncompleteOrdersService;

    public function __construct(CheckIncompleteOrdersService $checkIncompleteOrdersService)
    {
        parent::__construct();

        $this->checkIncompleteOrdersService = $checkIncompleteOrdersService;
    }

    protected function configure(): void
    {
        $this
            ->setName('psp:check-incomplete-orders')
            ->setDescription('Check incomplete order for all psp')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->checkIncompleteOrdersService->checkIncompleteOrders($output);

        return 0;
    }
}
