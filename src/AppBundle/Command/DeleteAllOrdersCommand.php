<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class DeleteAllOrdersCommand extends Command
{
    private Connection $connection;

    public function __construct(Connection $connection)
    {
        parent::__construct();

        $this->connection = $connection;
    }

    protected function configure()
    {
        $this
            ->setName('clear:orders')
            ->setDescription('Delete ALL orders');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $io = new SymfonyStyle($input, $output);

        $io->title("Delete all orders");

        if (false === $io->confirm("This is a destructive operation. Do you confirm?", false)) {
            $io->warning("Cancelled.");

            return 1;
        }

        $tablesToDelete = [
            'orders_actions_traces',
            'doctrine_order_transactions',
            'doctrine_organisation_order',
            'cscart_order_data',
            'cscart_order_details',
            'cscart_order_docs',
            'cscart_order_transactions',
            'doctrine_order_token',
            'cscart_new_orders',
            'cscart_order_adjustment',
            'doctrine_order_attachments',
            'doctrine_tax_item',
            'doctrine_subscriptions_has_order_items',
            'doctrine_order_item',
            'doctrine_order_refund_items',
            'cscart_orders',
            'cscart_shipments',
            'doctrine_order_refunds',
            'cscart_shipment_items',
            'doctrine_promotion_usage',
            'cscart_rma_return_products',
            'cscart_rma_returns',
            'cscart_vendor_payouts',
            'doctrine_subscription'
        ];

        foreach ($tablesToDelete as $table) {
            $io->comment("Truncating $table...");
            $this->connection->exec("DELETE FROM $table");
        }

        $io->success("Done.");

        return 0;
    }
}
