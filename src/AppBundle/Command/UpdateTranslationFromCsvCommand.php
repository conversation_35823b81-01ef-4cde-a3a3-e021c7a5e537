<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright Copyright (c) Wizaplace
 * @license   Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class UpdateTranslationFromCsvCommand extends Command
{
    /** @var EntityManagerInterface */
    protected $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        parent::__construct();

        $this->entityManager = $entityManager;
    }

    protected function configure(): void
    {
        $this
            ->setName('translation:update:csv')
            ->setDescription('Update Translation from CSV.')
            ->addArgument('file', InputArgument::REQUIRED, 'Input CSV file')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $handle = @\fopen($input->getArgument('file'), 'r');
        if (false === \is_resource($handle)) {
            throw new \RuntimeException('Unable to open file ' . $input->getArgument('file') . ' .');
        }

        $this->entityManager->getConnection()->beginTransaction();

        $headers = array_map(
            function ($value) {
                return strtolower(trim($value, "\xEF\xBB\xBF"));
            },
            fgetcsv($handle, 0, ';')
        );

        while ($line = fgetcsv($handle, 0, ';')) {
            $data = array_combine($headers, $line);

            $this->entityManager->getConnection()->executeQuery(
                'UPDATE cscart_language_values SET value = :value WHERE name = :name AND lang_code = :lang',
                [
                    ':value' => $data['value'],
                    ':name' => $data['name'],
                    ':lang' => $data['language'],
                ]
            );
        }

        $this->entityManager->getConnection()->commit();

        return 0;
    }
}
