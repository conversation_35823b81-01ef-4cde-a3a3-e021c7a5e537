<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command\Benchmark;

use Doctrine\DBAL\Connection;

class BenchmarkDatabaseCommand extends AbstractBenchmarkCommand
{
    protected static $defaultName = 'debug:benchmark:database';

    private Connection $connection;

    public function __construct(
        Connection $connection
    ) {
        parent::__construct();

        $this->connection = $connection;
    }

    protected function configure()
    {
        parent::configure();

        $this->setDescription('Database benchmark');
    }

    /**
     * Minimal test without trying to read or write data from database
     */
    protected function processIteration()
    {
        $this->connection->executeQuery(
            'SELECT 1'
        )->fetch();
    }

    protected function getConfig()
    {
        return  $this->connection->getParams('dsn');
    }
}
