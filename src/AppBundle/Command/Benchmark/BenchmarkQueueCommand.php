<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command\Benchmark;

use Aws\Sqs\SqsClient;
use Eventio\BBQ;
use Eventio\BBQ\Queue\AbstractQueue;
use PhpAmqpLib\Channel\AMQPChannel;
use Wizacha\Async\Config;
use Wizacha\Async\Exception\UnknownQueueTypeException;
use Wizacha\BBQ\Queue\AmqpQueue;
use Wizacha\BBQ\Queue\SqsQueue;

class BenchmarkQueueCommand extends AbstractBenchmarkCommand
{
    public const DEFAULT_ITERATIONS = 2 ** 8;
    private const DIRECTORY_NOT_IMPLEMENTED = 'Benchmark for directory queue type is not implemented';

    protected static $defaultName = 'debug:benchmark:queue';

    private string $queueType;
    private BBQ $bbq;
    private array $sqsConfig;
    private array $amqpConfig;

    private \ReflectionProperty $clientRP;
    private \ReflectionProperty $queueUrlRP;
    private \ReflectionProperty $channelRP;

    public function __construct(
        string $queueType,
        BBQ $bbq,
        array $sqsConfig,
        array $amqpConfig
    ) {
        $this->queueType = $queueType;
        $this->bbq = $bbq;
        $this->sqsConfig = $sqsConfig;
        $this->amqpConfig = $amqpConfig;

        parent::__construct();
    }

    protected function configure()
    {
        parent::configure();

        $this->setDescription("{$this->queueType} queue benchmark");
    }

    protected function processIteration()
    {
        $this->getQueueInfo();
    }

    private function getQueueInfo(): array
    {
        switch ($this->queueType) {
            case Config::TYPE_SQS:
                return $this->getSqsInfo($this->bbq);
            case Config::TYPE_AMQP:
                return $this->getAmqpInfo($this->bbq);
            case Config::TYPE_DIRECTORY:
                throw new \Exception(
                    static::DIRECTORY_NOT_IMPLEMENTED
                );
            default:
                throw new UnknownQueueTypeException(
                    $this->queueType
                );
        }
    }

    private function getClientRP(): \ReflectionProperty
    {
        return $this->clientRP ?? $this->getAccessibleReflectionProperty(
            SqsQueue::class,
            '_client'
        );
    }

    private function getSqsInfo(BBQ $bbq): array
    {
        return \array_map(
            function (AbstractQueue $queue) {
                /** @var SqsClient */
                $sqsClient = $this->getClientRP()->getValue($queue);
                try {
                    return
                        $sqsClient->getQueueAttributes(
                            [
                                'QueueUrl' => $this->getQueueUrlRP()->getValue($queue),
                                'AttributeNames' => ['ApproximateNumberOfMessages'],
                            ]
                        )->get('Attributes')
                    ;
                } catch (\Exception $e) {
                    return 'unavailable';
                }
            },
            $bbq->getQueues()
        );
    }
    private function getQueueUrlRP(): \ReflectionProperty
    {
        return $this->queueUrlRP ??= $this->getAccessibleReflectionProperty(
            SqsQueue::class,
            '_queueUrl'
        );
    }

    private function getAmqpInfo(BBQ $bbq): array
    {
        return \array_map(
            function (AbstractQueue $queue): array {
                /** @var AMQPChannel */
                $channel = $this->getChannelRP()->getValue($queue);

                return $channel
                    ->queue_declare(
                        $queue->getId(),
                        true
                    )
                ;
            },
            $bbq->getQueues()
        );
    }
    private function getChannelRP(): \ReflectionProperty
    {
        return $this->channelRP ??= $this->getAccessibleReflectionProperty(
            AmqpQueue::class,
            'channel'
        );
    }

    private function getAccessibleReflectionProperty(
        string $class,
        string $property
    ): \ReflectionProperty {
        $reflectionProperty = new \ReflectionProperty($class, $property);
        $reflectionProperty->setAccessible(true);

        return $reflectionProperty;
    }

    protected function getConfig(): array
    {
        switch ($this->queueType) {
            case Config::TYPE_SQS:
                return $this->sqsConfig;
            case Config::TYPE_AMQP:
                return $this->amqpConfig;
            case Config::TYPE_DIRECTORY:
                throw new \Exception(
                    static::DIRECTORY_NOT_IMPLEMENTED
                );
            default:
                throw new UnknownQueueTypeException(
                    $this->queueType
                );
        }
    }
}
