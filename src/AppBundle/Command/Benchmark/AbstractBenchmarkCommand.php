<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command\Benchmark;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Yaml\Yaml;

abstract class AbstractBenchmarkCommand extends Command
{
    public const OPTION_ITERATIONS = 'iterations';
    public const DEFAULT_ITERATIONS = 2 ** 15;

    protected InputInterface $input;
    protected OutputInterface $output;

    protected function configure()
    {
        $this
            ->addOption(
                static::OPTION_ITERATIONS,
                'i',
                InputOption::VALUE_REQUIRED,
                'number of iterations',
                static::DEFAULT_ITERATIONS
            )
        ;
    }

    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ) {
        $this->input = $input;
        $this->output = $output;

        $io = new SymfonyStyle($input, $output);
        $io->title($this->getDescription());

        $output->write($this->dumpConfig());
        $io->newLine(1);

        $iterations = (int) $input->getOption(static::OPTION_ITERATIONS);
        $progressBar = $io->createProgressBar($iterations);

        $total = 0;
        for ($i = 0; $i < $iterations; $i++) {
            $total += $this->chrono();
            $progressBar->advance();
        }
        $progressBar->finish();
        $io->newLine(2);

        $io->success(
            \sprintf(
                '%01.2f ms / %d iterations (~ %01.2f µs/iteration)',
                $total * 10 ** 3, // in ms
                $iterations,
                ($total / $iterations) * 10 ** 6 // in µs
            )
        );
    }

    private function chrono(): float
    {
        $startTime = microtime(true);
        $this->processIteration();

        return microtime(true) - $startTime;
    }

    private function dumpConfig()
    {
        return Yaml::dump(
            $this->getConfig()
        );
    }

    abstract protected function processIteration();

    abstract protected function getConfig();
}
