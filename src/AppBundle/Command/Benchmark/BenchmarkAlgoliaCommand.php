<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command\Benchmark;

use Wizacha\Search\Engine\Algolia;

class BenchmarkAlgoliaCommand extends AbstractBenchmarkCommand
{
    public const DEFAULT_ITERATIONS = 64;

    protected static $defaultName = 'debug:benchmark:algolia';

    private Algolia $algolia;

    public function __construct(
        Algolia $algolia
    ) {
        parent::__construct();

        $this->algolia = $algolia;
    }

    protected function configure()
    {
        parent::configure();

        $this->setDescription('Algolia benchmark');
    }

    /**
     * Make a call to Algolia API isAlive
     */
    protected function processIteration()
    {
        $this->algolia->getClient()->isAlive();
    }

    protected function getConfig(): array
    {
        return \array_filter(
            (array) $this->algolia->getClient()->getContext()
        );
    }
}
