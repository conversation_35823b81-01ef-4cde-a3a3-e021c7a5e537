<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command\Benchmark;

class BenchmarkRedisCommand extends AbstractBenchmarkCommand
{
    private const BENCHMARK_KEY = 'benchmark:key';

    protected static $defaultName = 'debug:benchmark:redis';

    private \Redis $redis;

    public function __construct(
        \Redis $redis
    ) {
        parent::__construct();

        $this->redis = $redis;
    }

    protected function configure()
    {
        parent::configure();

        $this->setDescription('Redis benchmark');
    }

    /**
     * write and read test
     */
    protected function processIteration()
    {
        $this->redis->set(
            static::BENCHMARK_KEY,
            'hello world',
            10
        );

        $this->redis->get(static::BENCHMARK_KEY);
    }

    protected function getConfig()
    {
        return [
            'host' => $this->redis->getHost(),
            'port' => $this->redis->getPort(),
            'isConnected' => $this->redis->isConnected(),
        ];
    }
}
