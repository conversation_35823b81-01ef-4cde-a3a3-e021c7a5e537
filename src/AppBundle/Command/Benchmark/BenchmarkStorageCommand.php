<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command\Benchmark;

use Wizacha\Storage\StorageService;

class BenchmarkStorageCommand extends AbstractBenchmarkCommand
{
    public const DEFAULT_ITERATIONS = 2 ** 8;
    public const BENCHMARK_FILE = 'benchmark.txt';

    protected static $defaultName = 'debug:benchmark:storage';

    protected static StorageService $storage;

    public function __construct(
        StorageService $staticsStorageService
    ) {
        parent::__construct();

        $this->storage = $staticsStorageService;
    }

    protected function configure()
    {
        parent::configure();

        $this->setDescription('Storage benchmark');
    }

    /**
     * write/read test
     */
    protected function processIteration()
    {
        $this->storage->put(
            static::BENCHMARK_FILE,
            [
                'file' => __DIR__ . '/data/' . static::BENCHMARK_FILE,
                'keep_origins' => true,
                'overwrite' => true
            ]
        );
        $this->storage->get(static::BENCHMARK_FILE);
    }

    protected function getConfig()
    {
        return $this->storage->getOptions();
    }
}
