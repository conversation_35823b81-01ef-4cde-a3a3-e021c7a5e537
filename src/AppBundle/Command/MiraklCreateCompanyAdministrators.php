<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Marketplace\User\UserService;
use Wizacha\User;

/**
 * @TODO To remove
 */
class MiraklCreateCompanyAdministrators extends Command
{
    /** @var SymfonyStyle */
    protected $io;
    /** @var \Wizacha\Marketplace\User\UserService  */
    private $userService;
    /**  @var \Doctrine\DBAL\Connection */
    private $connection;
    /** @var string|null */
    private $usersFilePath;
    /** @var string|null */
    private $companiesMappingFilePath;
    /** @var \Doctrine\DBAL\Statement */
    private $updateStatement;

    public function __construct(
        UserService $userService,
        Connection $connection
    ) {
        parent::__construct();
        $this->userService = $userService;
        $this->connection = $connection;
    }

    protected function configure()
    {
        $this
            ->setName('companies:create-admin')
            ->setDescription('Create companies administrators from a csv')
            ->addArgument(
                'usersFile',
                InputArgument::REQUIRED,
                'Path of the file containing the data, csv with semicolon as separator'
            )
            ->addArgument(
                'companiesMapping',
                InputArgument::REQUIRED,
                'Path of the file containing the company mapping, csv with semicolon as separator'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->io            = new SymfonyStyle($input, $output);
        $this->usersFilePath = $input->getArgument('usersFile');
        $this->companiesMappingFilePath = $input->getArgument('companiesMapping');
        $lineCount           = 1;
        $processedLines = 0;
        $erroredLines = [];

        foreach ($this->parseFile() as $input) {
            if ($lineCount === 1) {
                $lineCount++;

                continue;
            }

            try {
                $this->validateInput($input);
                [$miraklCompanyId, $email, $status] = $input;
                $this->checkIfEmailExists($email);
                $companyId = $this->getWizaplaceCompanyId($miraklCompanyId);
                $userId = $this->userService->createUser(
                    $email,
                    bin2hex(random_bytes(8)),
                    null,
                    null,
                    null,
                    false
                );
                $this->updateUser($userId, $companyId, $status);
                $processedLines++;
            } catch (\Exception $exception) {
                $this->io->warning(
                    "Error at line #{$lineCount}. Error was:" . $exception->getMessage()
                );
                $erroredLines[] = $lineCount;
            }

            $lineCount++;
        }


        $endMessage = "$processedLines companies administrators were created. $erroredLines error(s)";
        if ([] !== $erroredLines) {
            $endMessage .= ': ' . implode(',', $erroredLines);
        }
        $this->io->success($endMessage);

        return 0;
    }

    private function updateUser(int $userId, int $companyId, string $status): void
    {
        if (null === $this->updateStatement) {
            $this->updateStatement = $this->connection->prepare(
                'UPDATE cscart_users SET user_type = :userType, company_id = :companyId,'
                    . 'status = :status WHERE user_id = :userId'
            );
        }

        $this->updateStatement->execute([
            'userType' => User::VENDOR_TYPE,
            'companyId' => $companyId,
            'status' => $status,
            'userId' => $userId
        ]);
    }

    private function parseFile(): \Generator
    {
        if (($handle = fopen($this->usersFilePath, 'r')) !== false) {
            while (($data = fgetcsv($handle, 1000, ';')) !== false) {
                yield $data;
            }
            fclose($handle);
        } else {
            throw new \RuntimeException('Impossible to extract data from the file ' . $this->usersFilePath);
        }
    }

    private function validateInput(array &$input): void
    {
        if (\count($input) !== 3) {
            throw new \LogicException('Invalid input qty');
        }

        $input[0] = (int) trim($input[0]);
        if ($input[0] === 0) {
            throw new \LogicException('Invalid Mirakl company id');
        }

        $input[1] = trim($input[1]);

        $input[2] = strtolower(trim($input[2]));
        if ($input[2] === 'oui') {
            $input[2] = 'A';
        } elseif ($input[2] === 'non') {
            $input[2] = 'D';
        } else {
            throw new \LogicException('Invalid admin status');
        }
    }

    /**
     * YES bro! Because if you don't do that, if the email is already used, you will get a nice
     * exception with no message to help you.
     */
    private function checkIfEmailExists(string $email): void
    {
        $result = $this->connection
            ->executeQuery('SELECT user_id FROM cscart_users WHERE email = :email', ['email' => $email])
            ->fetchAll();

        if ([] !== $result) {
            throw new \LogicException("The email $email is already used by user with id {$result[0]['user_id']}");
        }
    }

    private function getWizaplaceCompanyId(int $miraklCompanyId): int
    {
        static $companiesId = [];

        if ([] === $companiesId) {
            if (($handle = fopen($this->companiesMappingFilePath, 'r')) !== false) {
                while (($data = fgetcsv($handle, 1000, ';')) !== false) {
                    $companiesId[$data[0]] = $data[1];
                }
                fclose($handle);
            } else {
                throw new \RuntimeException('Impossible to extract data from the file ' . $this->usersFilePath);
            }
        }

        if (false === \array_key_exists($miraklCompanyId, $companiesId)) {
            throw new \LogicException("Company with Mirakl Id #{$miraklCompanyId} not found in mapping");
        }

        return (int) $companiesId[$miraklCompanyId];
    }
}
