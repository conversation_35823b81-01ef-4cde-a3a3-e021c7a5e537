<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Driver\Connection;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class CatalogJsonExporterCommand extends Command
{
    /**
     * @var Connection
     */
    private $connection;

    public function __construct(Connection $connection)
    {
        parent::__construct();
        $this->connection = $connection;
    }

    protected function configure(): void
    {
        $this
            ->setName('readmodel:export')
            ->setDescription('Export Readmodel to a JSON file')
            ->addArgument('filename', InputArgument::OPTIONAL, 'Filename to write', 'readmodel.json')
            ->addOption('batch', 'b', InputOption::VALUE_REQUIRED, 'Batch size', 1024)
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $filename = $input->getArgument('filename');
        $batchSize = $input->getOption('batch');

        $io = new SymfonyStyle($input, $output);
        $io->title("Export Readmodel to JSON file $filename by batch of $batchSize");

        $stmt = $this->connection->prepare("SELECT count(*) FROM product_catalog");
        $stmt->execute();
        $countRows = (int) $stmt->fetchColumn(0);
        $io->comment("Number of rows to export : $countRows");

        $fh = fopen($filename, 'w');
        if (false === $fh) {
            $io->error("Cannot open file for writing");
            return 1;
        }
        fwrite($fh, "[\n");
        $first = true;

        $progressBar = new ProgressBar($output, $countRows);
        $progressBar->start();

        $stmt = $this->connection->prepare("SELECT id, product_json FROM product_catalog WHERE id > :index ORDER BY id LIMIT $batchSize");

        $index = 0;

        do {
            $stmt->bindValue('index', $index);
            $stmt->execute();

            $index = 0; // stop condition

            while ($result = $stmt->fetch()) {
                if ($first) {
                    $first = false;
                } else {
                    fwrite($fh, ",\n");
                }
                fwrite($fh, $result['product_json']);
                $index = (int) $result['id'];

                $progressBar->advance();
            }
        } while ($index > 0);

        $progressBar->finish();
        $progressBar->clear();

        fwrite($fh, "\n]\n");
        fclose($fh);

        $io->success("Done.");

        return 0;
    }
}
