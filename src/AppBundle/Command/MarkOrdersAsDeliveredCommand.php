<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\ContainerAwareInterface;
use Symfony\Component\DependencyInjection\ContainerAwareTrait;

class MarkOrdersAsDeliveredCommand extends Command implements ContainerAwareInterface
{
    use ContainerAwareTrait;

    protected function configure()
    {
        $this
            ->setName('orders:mark-as-delivered')
            ->setDescription('Mark shipped orders as delivered after a specific amount of time.')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $orderService = $this->container->get('marketplace.order.order_service');
        $markAsDeliveredAction = $this->container->get('marketplace.order.action.mark_as_delivered');

        // Récupération des commandes expédiées et en attente de livraison
        $pendingDeliveryOrders = $orderService->getPendingDeliveryOrders();

        // Initialisation du compteur
        $markAsDeliveredCount = 0;

        foreach ($pendingDeliveryOrders as $pendingDeliveryOrder) {
            // Si la période d'attente n'est pas terminée, on ne s'occupe pas
            // de cette commande.
            if (!$pendingDeliveryOrder->deliveryPeriodIsOver()) {
                continue;
            }

            // Vérification qu'il est possible d'effectuer l'action
            if ($markAsDeliveredAction->isAllowed($pendingDeliveryOrder)) {
                $markAsDeliveredAction->execute($pendingDeliveryOrder);
                $markAsDeliveredCount++;
            }
        }

        $output->writeln(sprintf("<info>%s commandes traitées, dont %s sont passées en 'livrées'.</info>", \count($pendingDeliveryOrders), $markAsDeliveredCount));
    }
}
