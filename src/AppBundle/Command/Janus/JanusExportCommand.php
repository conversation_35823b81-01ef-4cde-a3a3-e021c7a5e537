<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command\Janus;

use Ifsnop\Mysqldump\Mysqldump;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Tygh\Registry;
use Wizacha\Janus\JanusPerimeter;
use Wizacha\Janus\JanusService;
use Wizacha\Janus\RowTransformer\CategoryProductCountResetTransformer;
use Wizacha\Janus\RowTransformer\CompanyPspWalletIdResetTransformer;

class JanusExportCommand extends Command
{
    public const DEFAULT_PATH = 'php://stdout';
    public const DESCRIPTION = 'Exports data according to a given perimeter';

    private ContainerInterface $container;
    private JanusService $janusService;

    public function __construct(JanusService $janusService)
    {
        parent::__construct();

        $this->janusService = $janusService;
        $this->container = container();
    }

    protected function configure()
    {
        $this
            ->setName('janus:export')
            ->setDescription(static::DESCRIPTION)
            ->setHelp(
                'Creates a sql dump file, containing all necessary data according to the given perimeter.' . PHP_EOL
                . 'Meant to be used along with janus:import command.',
            )
            ->addArgument(
                'perimeter',
                InputArgument::REQUIRED,
                'Provided perimeter has to be a member of JanusPerimeter class.'
            )
            ->addOption(
                'path',
                null,
                InputOption::VALUE_REQUIRED,
                'Specifiy the file where data will be output to. If not provided, data will be output to stdout.',
                '-'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->getErrorStyle()->title(static::DESCRIPTION);

        $perimeter = new JanusPerimeter($input->getArgument('perimeter'));
        $path = $input->getOption('path');
        $path = '-' === $path ? static::DEFAULT_PATH : $path;

        $dumper = new Mysqldump(
            'mysql:host=' . Registry::get('config.db_host') . ';dbname=' . Registry::get('config.db_name'),
            Registry::get('config.db_user'),
            Registry::get('config.db_password'),
            [
                'no-data' => $this->janusService->getNoDataTables($perimeter),
            ]
        );

        $dumper->setTableWheres($this->janusService->getTableConditions($perimeter));

        if ($perimeter->getValue() === JanusPerimeter::ADMIN_PERIMETER) {
            // We remove product counts from categories if we dont get products table
            $this->janusService->addRowTransformer(
                new CategoryProductCountResetTransformer()
            );
        }
        $this->janusService->addRowTransformer(
            new CompanyPspWalletIdResetTransformer()
        );

        $dumper->setTransformTableRowHook(
            $this->janusService->getTransformTableRowHook()
        );
        $dumper->start($path);

        $io->getErrorStyle()->success($this->getSuccessMessage($perimeter, $path));

        return 0;
    }

    private function getSuccessMessage(JanusPerimeter $perimeter, ?string $path): string
    {
        return \sprintf(
            'Data has been successfully exported with %s perimeter to %s',
            $perimeter,
            $path
        );
    }
}
