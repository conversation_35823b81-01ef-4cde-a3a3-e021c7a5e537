<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command\Janus;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\Question;
use Symfony\Component\Console\Style\SymfonyStyle;
use Tygh\Registry;
use Wizacha\Janus\JanusService;

class JanusImportCommand extends Command
{
    public const DEFAULT_PATH = '/dev/stdin';
    public const DESCRIPTION = 'Clear tables and import data';

    private JanusService $janusService;

    public function __construct(JanusService $janusService)
    {
        parent::__construct();

        $this->janusService = $janusService;
    }

    protected function configure()
    {
        $this
            ->setName('janus:import')
            ->setDescription(static::DESCRIPTION)
            ->setHelp(<<<EOT
Clears all tables from database and imports provided data.
Meant to be used along with janus:export command.
EOT
            )
            ->addArgument(
                'path',
                InputArgument::REQUIRED,
                'Specifiy the file where data will be get from. If - is provided, data will be get from stdin.'
            )
            ->addOption(
                'y',
                null,
                InputOption::VALUE_NONE,
                'Automatically responds positively to confirmation question'
            )
        ;
    }

    private function isMysqlCliInstalled(): bool
    {
        $testCmd = exec("command -v mysql >/dev/null 2>&1 || exit;", $output, $statusCode);

        return $statusCode === 0;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        if (false === $this->isMysqlCliInstalled()) {
            $io->warning("I require mysql cli but it's not installed.");

            return 1;
        }

        $io->title(static::DESCRIPTION);

        $path = $input->getArgument('path');
        $path = '-' === $path ? static::DEFAULT_PATH : $path;

        $response = $input->getOption('y') === true && $path === static::DEFAULT_PATH
            ? 'REALLY'
            : $io->askQuestion(
                new Question(
                    sprintf('Answer REALLY if you REALLY want to clear database and import provided data')
                )
            );

        if ($response !== 'REALLY') {
            $io->warning('Import has been canceled, database has not been edited');

            return 0;
        }

        $this->janusService->clearTables();

        $cmd = sprintf(
            'MYSQL_PWD=%s mysql -h%s -u%s %s < %s',
            Registry::get('config.db_password'),
            Registry::get('config.db_host'),
            Registry::get('config.db_user'),
            Registry::get('config.db_name'),
            $path
        );

        $commandOutPut = shell_exec($cmd);

        if ($io->isVerbose() === true) {
            $io->text('Executed command: ' . $cmd);
            $io->text('Command output: ' . $commandOutPut);
        }

        $io->success('Database has been cleared and provided data has been imported');

        return 0;
    }
}
