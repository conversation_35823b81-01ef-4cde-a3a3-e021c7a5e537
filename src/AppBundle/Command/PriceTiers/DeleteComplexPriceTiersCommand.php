<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command\PriceTiers;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Marketplace\PriceTier\Repository\PriceTierRepository;

class DeleteComplexPriceTiersCommand extends Command
{
    protected static $defaultName = 'products:price-tiers:reset';

    /** @var PriceTierRepository */
    protected $priceTierRepository;

    public function __construct(PriceTierRepository $priceTierRepository)
    {
        parent::__construct();

        $this->priceTierRepository = $priceTierRepository;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Deletes all price tiers with a lower limit different from 0.')
            ->setHelp('This command is especially useful if an environment goes from a price tiers feature flag set to true to false.')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        try {
            $this->priceTierRepository->deletePriceTiersLowerLimitNot0();
        } catch (\Exception $exception) {
            $output->writeln(
                sprintf(
                    "<error>Delete price tiers: %s</error>\n\n%s",
                    $exception->getMessage(),
                    $exception
                )
            );

            return 1;
        }

        $output->writeln([
            '<info>Price Tiers with a lower limit different from 0 have been successfully deleted.</info>',
        ]);

        return 0;
    }
}
