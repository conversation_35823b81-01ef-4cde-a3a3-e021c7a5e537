<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Marketplace\User\Password;
use Wizacha\Marketplace\User\UserService;

class CreateAdminUserCommand extends Command
{
    /** @var UserService */
    private $userService;

    /** @var string */
    public $isStrictPassword;

    public function __construct(UserService $userService, string $isStrictPassword)
    {
        parent::__construct();

        $this->userService = $userService;
        $this->isStrictPassword = $isStrictPassword;
    }

    protected function configure()
    {
        $this
            ->setName('user:create:admin')
            ->setDescription('Create an admin user')
            ->addArgument('email', InputArgument::REQUIRED)
            ->addArgument('password', InputArgument::REQUIRED)
            ->addOption('api-key', null, InputOption::VALUE_REQUIRED)
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $email = $input->getArgument('email');
        $password = $input->getArgument('password');
        $apiKey = $input->getOption('api-key');

        if (true === \filter_var($this->isStrictPassword, FILTER_VALIDATE_BOOLEAN)
            && false === Password::isPasswordFormatValid($password)
        ) {
            $output->writeln(__('error_password_format_not_valid'));

            return 1;
        }

        $userId = $this->userService->createAdmin($email, $password, $apiKey);

        $output->writeln('<comment>Account created</comment>');
        $output->writeln(sprintf('<comment>API key : %s</comment>', $this->userService->get($userId)->getApiKey()));

        return 0;
    }
}
