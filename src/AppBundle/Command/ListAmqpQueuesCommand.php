<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ListAmqpQueuesCommand extends Command
{
    /** @var array */
    private $queues;

    public function __construct(array $queues)
    {
        parent::__construct();

        \ksort($queues);
        $this->queues = $queues;
    }

    protected function configure(): void
    {
        $this
            ->setName('amqp:list')
            ->setDescription('List AMQP queues')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $table = new Table($output);
        $table->setHeaders(['Queue name', 'Priority']);

        foreach ($this->queues as $key => $values) {
            $table->addRow(
                [$key, $values['priority'] ?? '']
            );
        }

        $table->render();

        return 0;
    }
}
