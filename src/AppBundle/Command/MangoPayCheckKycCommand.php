<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Payment\Processor\MangoPay;

class MangoPayCheckKycCommand extends Command implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    public const COMPANY_ID_KEY = 'company_id';

    /** @var MangoPay */
    protected $mangopay;

    /** @var CompanyService */
    protected $companyService;

    public function __construct(MangoPay $mangopay, CompanyService $companyService)
    {
        parent::__construct();

        $this->mangopay = $mangopay;
        $this->companyService = $companyService;
    }

    protected function configure(): void
    {
        $this
            ->setName('mangopay:check-kyc')
            ->setDescription('Check the KYC of all pending and C2C companies for MangoPay')
            ->addOption(
                'no-pending-only',
                null,
                InputOption::VALUE_NONE,
                'Check the KYC of all companies except company with status = new'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if (false === $this->mangopay->isConfigured()) {
            return 0;
        }

        if ($input->getOption('no-pending-only') === true) {
            $statement = $this->companyService->getAllVendorsExceptNew();
        } else {
            $statement = $this->companyService->getPendingVendors(true);
        }

        $progressBar = new ProgressBar($output, $statement->rowCount());
        $progressBar->start();

        $companiesError = 0;
        while ($company = $statement->fetch()) {
            try {
                $progressBar->advance();

                $this->mangopay->validateKyc((int) $company[static::COMPANY_ID_KEY]);
            } catch (\Exception $exception) {
                $this->logger->error("[MangoPay] error company", [
                    'companyId' => $company[static::COMPANY_ID_KEY],
                    'exception' => $exception
                ]);
                $companiesError++;
            }
        }

        $progressBar->finish();

        if ($companiesError > 0) {
            return 1;
        }

        return 0;
    }
}
