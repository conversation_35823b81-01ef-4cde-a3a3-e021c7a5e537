<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Eventio\BBQ\Job\Payload\StringPayload;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\Question;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Async\Debouncer\DebouncerService;

class AmqpDebouncerBenchmarkCommand extends Command
{
    protected static $defaultName = 'amqp:debouncer:benchmark';

    /** @var DebouncerService */
    protected $debouncerService;

    public function __construct(DebouncerService $debouncerService)
    {
        parent::__construct();

        $this->debouncerService = $debouncerService;
    }

    protected function configure()
    {
        $this
            ->setDescription(
                "Benchmarking (don't run this in production)"
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('Debouncer');
        $io->warning("Don't run benchmark in production, or you will loose jobs held in the debouncer.");
        $response = $io->askQuestion(new Question('Write REALLY if you are REALLY sure of what you are doing'));

        if ('REALLY' !== $response) {
            return 0;
        }

        $qty = 10 ** 4;

        $start = \microtime(true);
        foreach (\range(1, $qty) as $i) {
            $this->debouncerService->bounceJob(
                'benchmark_queue',
                "benchmark:$i",
                new StringPayload("benchmark payload $i"),
                0
            );
        }
        $elapsed = \microtime(true) - $start;

        $io->success(
            \sprintf(
                '%d jobs wrote in %d msec. (%d jobs/sec)',
                $qty,
                $elapsed * 1000,
                $qty / $elapsed
            )
        );

        \sleep(1);

        $start = \microtime(true);
        $expiredGenerator = $this->debouncerService
            ->debouncedJobRepository
            ->getExpired()
        ;

        $extracted = 0;
        foreach ($expiredGenerator as $debounceJob) {
            ++$extracted;
        }
        $elapsed = \microtime(true) - $start;

        $io->success(
            \sprintf(
                '%d jobs extracted in %d msec. (%d jobs/sec)',
                $extracted,
                $elapsed * 1000,
                $extracted / $elapsed
            )
        );

        return 0;
    }
}
