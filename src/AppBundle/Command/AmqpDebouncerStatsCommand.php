<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Async\Debouncer\DebouncerService;

class AmqpDebouncerStatsCommand extends Command
{
    protected static $defaultName = 'amqp:debouncer:stats';

    /** @var DebouncerService */
    protected $debouncerService;

    public function __construct(DebouncerService $debouncerService)
    {
        parent::__construct();

        $this->debouncerService = $debouncerService;
    }

    protected function configure()
    {
        $this
            ->setDescription(
                'Debouncer stats'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Debouncer');

        [$jobs, $bounces] = \array_values(
            $this->debouncerService->getStats()
        );

        $io->note(
            \sprintf(
                '%s job%s holded in debouncer, (%d bounce%s detected)',
                $jobs,
                $jobs > 1 ? 's' : '',
                $bounces,
                $bounces > 1 ? 's' : ''
            )
        );

        return 0;
    }
}
