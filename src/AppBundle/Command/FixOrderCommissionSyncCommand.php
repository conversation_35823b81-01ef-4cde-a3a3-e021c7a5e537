<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\Question;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Marketplace\Transaction\TransactionStatus;
use Wizacha\Marketplace\Transaction\TransactionType;

class FixOrderCommissionSyncCommand extends Command
{
    protected static $defaultName = 'orders:commission:sync';

    private Connection $connection;

    public function __construct(
        Connection $connection
    ) {
        parent::__construct();

        $this->connection = $connection;
    }

    protected function configure()
    {
        $this
            ->setDescription(
                <<<EOT
                Synchronize orders cscart_vendor_payout commission amount with
                TransactionType::DISPATCH_FUNDS_TRANSFER_COMMISSION() transaction amount
                EOT
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title(
            'Fix out of sync cscart_vendor_payout:doctrine_order_transaction commission amounts'
        );

        $this->connection->beginTransaction();

        $updatedRows = $this->process();

        $question = new Question(
            \sprintf(
                'Answer REALLY, if you are REALLY want to update %s out of sync cscart_vendor_payout rows',
                $updatedRows
            )
        );

        if (0 < $updatedRows
            && 'REALLY' === $io->askQuestion($question)
        ) {
            $this->connection->commit();

            $io->success(
                \sprintf(
                    '%s cscart_vendor_payout rows synchronized with dispatch transactions',
                    $updatedRows
                )
            );
        } else {
            $this->connection->rollBack();

            $io->success(
                'Nothing to update'
            );
        }

        return 0;
    }

    protected function process(): int
    {
        $statement = $this->connection->prepare(
            <<<SQL
            UPDATE
                cscart_vendor_payouts cvp
                JOIN doctrine_order_transactions dot
                    ON cvp.order_id = dot.order_id
                    AND dot.type = :transactionType
                    AND dot.status = :transactionStatus
                    AND cvp.commission_amount != dot.amount * .01
            SET
                cvp.commission_amount = dot.amount * .01,
                cvp.commission = dot.amount * .01
            SQL
        );

        $statement->execute(
            [
                'transactionType' => TransactionType::DISPATCH_FUNDS_TRANSFER_COMMISSION(),
                'transactionStatus' => TransactionStatus::SUCCESS(),
            ]
        );

        return $statement->rowCount();
    }
}
