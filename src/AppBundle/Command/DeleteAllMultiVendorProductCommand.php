<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Driver\Connection;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\ConfirmationQuestion;
use Symfony\Component\Console\Style\SymfonyStyle;

class DeleteAllMultiVendorProductCommand extends Command
{
    private Connection $connection;

    public function __construct(Connection $connection)
    {
        parent::__construct();

        $this->connection = $connection;
    }

    protected function configure()
    {
        $this
            ->setName('clear:mvp')
            ->setDescription('Drop all multi-vendor products');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $helper = $this->getHelper('question');
        $question = new ConfirmationQuestion(
            "<question>Etes-vous sûr de vouloir supprimer tous les produits unifiés (y/n)?</question> ",
            false
        );

        if ($helper->ask($input, $output, $question)) {
            // suppression des données en base
            $sql = <<<SQL
SET foreign_key_checks = 0;
DELETE FROM `cscart_product_features_values` WHERE LENGTH(product_id) = 36;
DELETE FROM `cscart_product_metadata` WHERE LENGTH(product_id) = 36;
DELETE FROM `product_catalog` WHERE LENGTH(id) = 36;
DELETE FROM `cscart_seo_names` WHERE LENGTH(object_id) = 36;
DELETE FROM `cscart_discussion_messages`
    WHERE thread_id IN (SELECT thread_id FROM `cscart_discussion` WHERE LENGTH(object_id) = 36);
DELETE FROM `cscart_discussion_posts`
    WHERE thread_id IN (SELECT thread_id FROM `cscart_discussion` WHERE LENGTH(object_id) = 36);
DELETE FROM `cscart_discussion_rating`
    WHERE thread_id IN (SELECT thread_id FROM `cscart_discussion` WHERE LENGTH(object_id) = 36);
DELETE FROM `cscart_discussion` WHERE LENGTH(object_id) = 36;
TRUNCATE TABLE `doctrine_multi_vendor_product`;
TRUNCATE TABLE `doctrine_multi_vendor_product_link`;
TRUNCATE TABLE `doctrine_multi_vendor_product_translations`;
SET foreign_key_checks = 1;
SQL;

            $io = new SymfonyStyle($input, $output);

            $this->connection->exec($sql);
            $io->success('Les données en base ont été correctement supprimées');

            $io->warning('Lancer la commande products:cache:refresh pour mettre à jour les index Algolia');
        }

        return 0;
    }
}
