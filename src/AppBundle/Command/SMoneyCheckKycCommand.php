<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Payment\Processor\SMoney;
use Symfony\Component\Console\Helper\ProgressBar;

class SMoneyCheckKycCommand extends Command implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    /** @var SMoney */
    private $smoney;

    /** @var CompanyService */
    private $companyService;

    public function __construct(SMoney $smoney, CompanyService $companyService)
    {
        parent::__construct();

        $this->smoney = $smoney;
        $this->companyService = $companyService;
    }

    protected function configure()
    {
        $this
            ->setName('smoney:check-kyc')
            ->setDescription('Check the KYC of all pending and C2C companies for SMoney')
            ->addOption(
                'no-pending-only',
                null,
                InputOption::VALUE_NONE,
                'Check the KYC of all companies except company with status = new'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        if (!$this->smoney->isConfigured()) {
            return;
        }

        if ($input->getOption('no-pending-only') === true) {
            $statement = $this->companyService->getAllVendorsExceptNew();
        } else {
            $statement = $this->companyService->getPendingVendors(true);
        }

        $progressBar = new ProgressBar($output, $statement->rowCount());
        $progressBar->start();

        while ($company = $statement->fetch()) {
            try {
                $progressBar->advance();

                $this->smoney->checkKyc((int) $company['company_id']);
                $output->writeln("<success>SMoney company #{$company['company_id']} success</success>");
            } catch (\Exception $exception) {
                $this->logger->error("[SMoney] error company", [
                    'companyId' => $company['company_id'],
                    'exception' => $exception
                ]);
            }
        }
        $progressBar->finish();
    }
}
