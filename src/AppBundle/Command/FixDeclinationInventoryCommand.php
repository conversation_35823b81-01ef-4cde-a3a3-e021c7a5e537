<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright Copyright (c) Wizaplace
 * @license   Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Core\LimitedLogCounter;
use Wizacha\Product;

class FixDeclinationInventoryCommand extends Command
{
    protected static $defaultName = 'fix:products:declinations';

    protected const PRODUCTS_TABLE_NAME = 'cscart_products';
    protected const INVENTORY_TABLE_NAME = 'cscart_product_options_inventory';
    protected const OPTION_VARIANTS_TABLE_NAME = 'cscart_product_option_variants';
    protected const OPTIONS_EXCEPTIONS_TABLE_NAME = 'cscart_product_options_exceptions';

    protected const LOG_LIMIT = 64;

    protected Connection $connection;

    public function __construct(
        Connection $connection
    ) {
        parent::__construct();

        $this->connection = $connection;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Fix declination inventories')
            ->addOption(
                'info',
                'i',
                InputOption::VALUE_NONE,
                'Extended info'
            )
            ->setHelp(
                <<<TXT
                Fix various declination inventories problems:
                    - update incoherent inventory combinations hashes
                    - delete option variants orphan inventories
                    - delete invalid combination inventories
                    - delete option variant orphan exceptions
                    - update incorrect product tracking types
                TXT
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $extendedInfo = $input->getOption('info') ?? true;

        $io = new SymfonyStyle($input, $output);

        $io->title('Declinations inventory');

        // don't autocommit update queries
        $this->connection->beginTransaction();

        $progress = $io->createProgressBar($this->countInventory());

        $total = 0;

        $incoherentHashes = new LimitedLogCounter(static::LOG_LIMIT);
        $orphanInventories = new LimitedLogCounter(static::LOG_LIMIT);
        $incompleteInventories = new LimitedLogCounter(static::LOG_LIMIT);
        $invalidCombinations = new LimitedLogCounter(static::LOG_LIMIT);

        foreach ($this->getInventory() as $row) {
            try {
                $options = $this->extractOptions($row['combination']);
            } catch (\TypeError $e) {
                $this->deleteInventory((int) $row['id'])
                    ? $invalidCombinations->append($row)
                    : null
                ;
            }

            // as we do in fn.cart.php in fn_check_amount_in_stock function
            $expectedCombinationHash = fn_generate_cart_id(
                $row['product_id'],
                [
                    'product_options' => $options,
                ],
                true
            );

            if ($this->isOrphan($options)) {
                $this->deleteInventory((int) $row['id'])
                    ? $orphanInventories->append($row)
                    : null
                ;
            } elseif ($this->isIncomplete((int) $row['product_id'], $options)) {
                $this->deleteInventory((int) $row['id'])
                    ? $incompleteInventories->append($row)
                    : null
                ;
            } elseif ($expectedCombinationHash !== $row['combination_hash']) {
                $params = [
                    'productId' => $row['product_id'],
                    'combination' => $row['combination'],
                    'combinationHash' => $expectedCombinationHash,
                ];

                $this->updateInventory($params)
                    ? $incoherentHashes->append(
                        \array_merge(
                            $params,
                            ['actualHash' => $row['combination_hash']]
                        )
                    )
                    : null
                ;
            }

            ++$total;
            $progress->setProgress($total);
        }

        $progress->finish();
        $progress->clear();

        $orphanExceptions = $this->cleanOrphanExceptions();

        $errors = \array_sum(
            \array_map(
                'count',
                [
                    $orphanExceptions,
                    $orphanInventories,
                    $incompleteInventories,
                    $incoherentHashes,
                    $invalidCombinations,
                ]
            )
        );

        // fix tracking type
        $fixedTracking = $this->fixTrackingType();
        $errors += $fixedTracking;

        if ($errors > 0) {
            $this->report(
                $io,
                'Incoherent inventory combination hash to update',
                $incoherentHashes,
                $extendedInfo
            );
            $this->report(
                $io,
                'Option variants orphan inventory(ies) to delete',
                $orphanInventories,
                $extendedInfo
            );
            $this->report(
                $io,
                'Incomplete inventory(ies) to delete',
                $incompleteInventories,
                $extendedInfo
            );
            $this->report(
                $io,
                'Invalid combination inventory(ies) to delete',
                $invalidCombinations,
                $extendedInfo
            );
            $this->report(
                $io,
                'Option variants orphan exception(s) to delete',
                $orphanExceptions,
                $extendedInfo
            );
            if ($fixedTracking > 0) {
                $io->error(
                    \sprintf(
                        '%s incorrect product tracking type',
                        $fixedTracking
                    )
                );
            }
        } else {
            $io->success('No errors');
        }

        // ask for update authorization
        if ($errors > 0) {
            $isConfirmed =  $io->confirm(
                \sprintf(
                    "Are you sure to commit %d fix(es)",
                    $errors
                ),
                false
            );

            if ($isConfirmed) {
                // we commit hashes updates here only if we REALLY want to
                $this->connection->commit();
                $io->success('COMMIT');
            } else {
                $this->connection->rollBack();
                $io->warning('ROLLBACK');
            }
        }

        return 0;
    }

    private function fixTrackingType(): int
    {
        $productTableName = static::PRODUCTS_TABLE_NAME;
        $optionsExceptionsTableName = static::OPTIONS_EXCEPTIONS_TABLE_NAME;

        $statement = $this->connection->prepare(
            <<<SQL
            UPDATE
                $productTableName p
                LEFT JOIN $optionsExceptionsTableName oe ON
                    p.product_id = oe.product_id
            SET
                p.tracking = :tracking_classical
            WHERE
                p.tracking = :tracking_inventory
                AND oe.product_id IS NULL
            SQL
        );

        $statement->execute(
            [
                'tracking_classical' => Product::TRACKING_TYPE_CLASSICAL,
                'tracking_inventory' => Product::TRACKING_TYPE_INVENTORY,
            ]
        );

        return $statement->rowCount();
    }

    private function getOptionVariant(int $optionId, int $variantId)
    {
        $tableName = static::OPTION_VARIANTS_TABLE_NAME;

        $statement = $this->connection->prepare(
            <<<SQL
            SELECT
                v.*
            FROM
                $tableName v
            WHERE
                v.option_id = :optionId
                AND v.variant_id = :variantId
            SQL
        );

        $statement->execute(
            [
                'optionId' => $optionId,
                'variantId' => $variantId,
            ]
        );

        return $statement->fetchAssociative();
    }

    private function updateInventory(array $params): bool
    {
        $tableName = static::INVENTORY_TABLE_NAME;

        $updateInventoryStatement = $this->connection->prepare(
            <<<SQL
            UPDATE
                $tableName
            SET
                combination_hash = :combinationHash
            WHERE
                product_id = :productId
                AND combination = :combination
            SQL
        );

        return $updateInventoryStatement->execute($params);
    }


    private function countInventory(): int
    {
        $tableName = static::INVENTORY_TABLE_NAME;

        $countInventoryStatement = $this->connection->prepare(
            <<<SQL
            SELECT
                COUNT(*)
            FROM
                $tableName
            SQL
        );
        $countInventoryStatement->execute();

        return (int) $countInventoryStatement->fetchOne();
    }

    /**
     * @return \Generator<array>
     */
    private function getInventory(): \Generator
    {
        $tableName = static::INVENTORY_TABLE_NAME;

        $getInventoryStatement = $this->connection->prepare(
            <<<SQL
            SELECT
                id,
                product_id,
                combination,
                combination_hash
            FROM
                $tableName
            SQL
        );

        $getInventoryStatement->execute();

        foreach ($getInventoryStatement as $row) {
            yield $row;
        }
    }

    // delete variant orphan combination hash
    private function deleteInventory(int $id): bool
    {
        $tableName = static::INVENTORY_TABLE_NAME;

        $deleteInventoryStatement = $this->connection->prepare(
            <<<SQL
            DELETE FROM $tableName
            WHERE id = :id
            SQL
        );

        return $deleteInventoryStatement->execute(
            ['id' => $id]
        );
    }

    private function isOrphan(array $options): bool
    {
        foreach ($options as $optionId => $variantId) {
            if (false === $this->getOptionVariant((int) $optionId, (int) $variantId)) {
                return true;
            }
        }

        return false;
    }

    private function isIncomplete(int $productId, array $options): bool
    {
        $exceptionOptionIds = \array_values(
            \array_unique(
                \array_map(
                    fn($row) => \key($row),
                    \iterator_to_array(
                        $this->getProductExceptions($productId)
                    )
                ),
            )
        );
        $inventoryOptionIds = \array_keys(
            $options
        );
        sort($exceptionOptionIds);
        sort($inventoryOptionIds);

        if ($inventoryOptionIds === $exceptionOptionIds) {
            return false;
        }

        return true;
    }

    private function cleanOrphanExceptions(): LimitedLogCounter
    {
        $table = static::OPTIONS_EXCEPTIONS_TABLE_NAME;

        $deleteStatement = $this->connection->prepare(
            <<<SQL
            DELETE FROM
                $table
            WHERE
                product_id = :product_id
                AND combination = :combination
            SQL
        );

        $orphanExceptions = new LimitedLogCounter(static::LOG_LIMIT);

        foreach ($this->getOptionsExceptions() as $exception) {
            if (false === $this->getOptionVariant((int) $exception['option_id'], (int) $exception['variant_id'])) {
                $deleteStatement->execute($exception)
                    ? $orphanExceptions->append($exception)
                    : null
                ;
            }
        }

        return $orphanExceptions;
    }

    private function getOptionsExceptions(): \Generator
    {
        $table = static::OPTIONS_EXCEPTIONS_TABLE_NAME;

        $getStatement = $this->connection->prepare(
            <<<SQL
            SELECT
                *
            FROM
                $table
            SQL
        );

        $getStatement->execute();

        while ($result = $getStatement->fetch()) {
            yield $result;
        }
    }

    private function getProductExceptions($productId): \Generator
    {
        $table = static::OPTIONS_EXCEPTIONS_TABLE_NAME;

        $getStatement = $this->connection->prepare(
            <<<SQL
            SELECT
                *
            FROM
                $table
            WHERE
                product_id = :productId
            SQL
        );

        $getStatement->execute(
            ['productId' => $productId]
        );

        while ($row = $getStatement->fetch()) {
            yield [$row['option_id'] => $row['variant_id']];
        };
    }

    // Convert combinations string to options:variants array
    private function extractOptions(string $combination): array
    {
        $parts  = \array_chunk(
            \explode('_', $combination),
            2
        );

        return \array_combine(
            \array_column($parts, 0),
            \array_map(
                'intval',
                \array_column($parts, 1)
            )
        );
    }

    private function report(
        SymfonyStyle $io,
        string $section,
        LimitedLogCounter $data,
        bool $extendedInfo = false
    ): void {
        if (0 === $data->count()) {
            return;
        }

        $io->section($section);
        $io->error($data->count() . ' error(s)');

        if (false === $extendedInfo) {
            return;
        }

        $array = $data->getArray();
        $io->table(
            \array_keys($array[0]),
            $array
        );

        if ($data->isFull()) {
            $io->note(
                \sprintf(
                    '... + %s more',
                    $data->more()
                )
            );
        }
    }
}
