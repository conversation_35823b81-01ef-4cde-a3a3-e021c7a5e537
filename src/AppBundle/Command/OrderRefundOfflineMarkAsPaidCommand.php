<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright Copyright (c) Wizaplace
 * @license   Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\Question;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Marketplace\Order\Refund\Service\RefundService;

class OrderRefundOfflineMarkAsPaidCommand extends Command
{
    protected static $defaultName = 'orders:refund:offline-mark-as-paid';

    protected RefundService $refundService;

    public function __construct(
        RefundService $refundService
    ) {
        parent::__construct();

        $this->refundService = $refundService;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Mark as paid refund with missing transaction')
            ->addArgument(
                'ids',
                InputArgument::IS_ARRAY
                | InputArgument::REQUIRED,
                'Order refunds ID list'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('Order Refunds');

        $refundIds = \array_map(
            'intval',
            $input->getArgument('ids')
        );
        $total = \count($refundIds);

        $response = $io->askQuestion(
            new Question(
                \sprintf(
                    'Answer REALLY, if you REALLY want to mark %s refund%s as paid',
                    $total,
                    $total > 1 ? 's' : ''
                )
            )
        );

        $errors = [];

        if ('REALLY' === $response) {
            $progressBar = $io->createProgressBar($total);

            foreach ($refundIds as $refundId) {
                try {
                    $this->process($refundId);
                } catch (\Exception $exception) {
                    $errors[] = [
                        'refundId' => $refundId,
                        'message' => $exception->getMessage()
                    ];
                }
                $progressBar->advance();
            }
            $progressBar->finish();
            $progressBar->clear();
        } else {
            $io->warning("Aborted");

            return 0;
        }

        $processed = $total - \count($errors);
        $hasErrors = $processed < $total;

        $io->{$hasErrors ? 'error' : 'success'}(
            \sprintf(
                '%s/%s refund%s processed',
                $processed,
                $total,
                $total > 1 ? 's' : ''
            )
        );

        if (true === $hasErrors) {
            $io->table(
                \array_keys(
                    current($errors)
                ),
                $errors
            );
        }

        return (int) $hasErrors;
    }

    protected function process(int $refundId)
    {
        $this->refundService->offlineMarkRefundAsPaid(
            $this->refundService->get($refundId)
        );
    }
}
