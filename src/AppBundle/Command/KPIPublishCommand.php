<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Marketplace\KPI\KPIPublishService;

class KPIPublishCommand extends Command
{
    protected static $defaultName = 'kpi:publish';

    private KPIPublishService $KPIPublishService;
    private LoggerInterface $logger;
    private SymfonyStyle $io;

    public function __construct(KPIPublishService $KPIPublishService, LoggerInterface $logger)
    {
        parent::__construct();

        $this->KPIPublishService = $KPIPublishService;
        $this->logger = $logger;
    }

    protected function configure()
    {
        $this
            ->setDescription('Publish the marketplace KPI to the configured webhook')
            ->addOption('url', null, InputOption::VALUE_OPTIONAL, 'The KPI_WEBHOOK_URL')
            ->addOption('dry-run', null, InputOption::VALUE_NONE, 'Dry mode');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->io = new SymfonyStyle($input, $output);

        $this->io->title('Publish KPI');

        $dryMode = $input->getOption('dry-run') !== false;

        $url = $input->getOption('url') ?? $this->KPIPublishService->getConfigKPIWebhookURL();

        $this->KPIPublishService->checkKPIParameters();

        $this->io->section('Generating KPI JSON to send');
        $requestBody = $this->KPIPublishService->generateKPIArray();

        $this->io->section('Sending KPI to Webhook URL ' . $url);

        if ($dryMode === false) {
            $this->KPIPublishService->sendKPIToWebhook($requestBody, $url);
            $this->io->success('KPI successfully published!');
        } else {
            $this->io->success(\json_encode($requestBody));
        }

        return 0;
    }
}
