<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Async\Debouncer\DebouncerService;

class AmqpDebouncerTriggerCommand extends Command
{
    protected static $defaultName = 'amqp:debouncer:trigger';

    /** @var DebouncerService */
    protected $debouncerService;

    public function __construct(DebouncerService $debouncerService)
    {
        parent::__construct();

        $this->debouncerService = $debouncerService;
    }

    protected function configure()
    {
        $this
            ->setDescription(
                'Release held jobs and push them in AMQP queues'
            )
            ->addOption(
                'info',
                '-i',
                InputOption::VALUE_NONE,
                'Extended info'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $extendedInfo = $input->getOption('info') ?? true;

        [$processed, $nbErrors] = $this->debouncerService->processExpired();
        $hasErrors = 0 < $nbErrors;

        if ($extendedInfo) {
            $io->title('Debouncer trigger');

            $io->success("$processed job(s) processed");
            if ($hasErrors) {
                $io->error("$nbErrors error(s)");
            }
        }

        return (int) $hasErrors;
    }
}
