<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Filesystem\Exception\IOExceptionInterface;
use Symfony\Component\Filesystem\Filesystem;
use Symfony\Component\Finder\Finder;

class ClearTemporaryResourcesCommand extends Command
{
    protected function configure(): void
    {
        $this
            ->setName('purge:uploads')
            ->setDescription('Clear all Temporary Resources (var/files/api_uploads)');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $dir = 'var/files/api_uploads';
        $filesystem = new Filesystem();
        $return = 0;

        if ($filesystem->exists($dir) === false) {
            $output->writeln('The ' . $dir . ' directory does not exist.');

            return $return;
        }

        $finder = new Finder();
        $finder->files()->in($dir);
        $output->writeln('List of deleted files');
        $output->writeln('------------------------');
        foreach ($finder as $file) {
            $updateFileTime = filectime("$file");
            /*** if file is 24 hours (86400 seconds) old then delete it ***/
            if (time() - $updateFileTime > 86400) {
                try {
                    $filesystem->remove($file);
                    $output->writeln('File name : ' . $file);
                    $output->writeln('Date update: ' . date('F d Y H:i:s.', $updateFileTime));
                    $output->writeln('------------------------------------------------');
                } catch (IOExceptionInterface $exception) {
                    $output->writeln('An error occurred while remove your file at ' . $exception->getPath());
                    $return = 1;
                }
            }
        }

        return $return;
    }
}
