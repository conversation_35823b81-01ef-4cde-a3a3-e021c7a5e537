<?php

/**
 *  <AUTHOR> DevTeam <<EMAIL>>
 *  @copyright   Copyright (c) Wizacha
 *  @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

/**
 * Interface à implémenter pour que la commande soit exécutée à chaque déploiement
 *
 * /!\ Il faut aussi que la commande soit du type deploy:command:*****
 * @see src/AppBundle/Command/AbstractDeployCommand.php
 */
interface DeployPostActionsInterface
{
    /**
     * Permet de gérer un ordre de priorité sur les commandes à lancer lors d'un déploiement via les commandes
     * deploy:post-actions ou deploy:run
     *
     * Les priorités sont traitées par ordre croissant. Une commande avec une priorité de 0 va être traitée en premier
     *
     * @return int
     */
    public function getPriority(): int;
}
