<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\Bundle\DoctrineBundle\Registry;
use <PERSON>hum<PERSON>a\Uuid\Uuid;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Tygh\Api\Entities\Products;
use Wizacha\Core\Looper;
use Wizacha\Core\Helper;
use Wizacha\Marketplace\PIM\TransactionMode\TransactionMode;
use Wizacha\Marketplace\User\UserType;
use Wizacha\Status;

class BenchmarkFixtureCommand extends Command
{
    protected static $defaultName = 'debug:benchmark:fixtures';

    private Registry $doctrine;

    private Products $tyghProducts;
    private Uuid $uuid;

    private string $version;
    private array $stats;

    public function __construct(
        Registry $doctrine,
        string $version
    ) {
        parent::__construct();

        $this->doctrine = $doctrine;
        $this->version = $version;

        $this->tyghProducts = new Products([]);
        $this->uuid = Uuid::uuid4();
    }

    protected function configure()
    {
        $this
            ->setDescription(
                'Benchmark fixtures'
            )
            ->addOption(
                'companies',
                'c',
                InputOption::VALUE_OPTIONAL,
                'companies qty',
                50
            )
            ->addOption(
                'category-depth',
                'd',
                InputOption::VALUE_OPTIONAL,
                'category tree levels',
                3
            )
            ->addOption(
                'category-leaves',
                'l',
                InputOption::VALUE_OPTIONAL,
                'child category per parent category',
                5
            )
            ->addOption(
                'products',
                'p',
                InputOption::VALUE_OPTIONAL,
                'numbers of products',
                1000
            )
            ->addOption(
                'multi-vendor-products',
                'm',
                InputOption::VALUE_OPTIONAL,
                'numbers of mvp',
                250
            )
            ->addOption(
                'asynchronous',
                'a',
                InputOption::VALUE_NONE,
                'using AMQP/SQS queue if set'
            )
        ;
    }

    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ) {
        $isAsync = $input->getOption('asynchronous');
        \define('SYNC', !$isAsync);

        $companies = (int) $input->getOption('companies');
        $products = (int) $input->getOption('products');
        $mvps = (int) $input->getOption('multi-vendor-products');
        $depth = (int) $input->getOption('category-depth');
        $leaves = (int) $input->getOption('category-leaves');

        $categories = $leaves ** $depth;

        $categoryIds = $this->chrono(
            'categories',
            $categories,
            fn() => \iterator_to_array(
                $this->buildCategoryTree($depth, $leaves),
                false
            )
        );

        $companyIds = $this->chrono(
            'companies',
            $companies,
            fn() =>  \iterator_to_array(
                $this->createCompanies($companies),
                false
            )
        );

        $createProductClosure = function () use (
            $products,
            $categoryIds,
            $companyIds,
            $mvps
        ) {
            $categoryIdLooper = new Looper($categoryIds);
            $companyIdLooper = new Looper($companyIds);
            $mvpIdLooper = new Looper(range(1, $mvps));

            foreach (Helper::xrange($products) as $id) {
                $this->createProduct(
                    $categoryIdLooper->step(),
                    $companyIdLooper->step(),
                    $mvpIdLooper->step(),
                    $id
                );
            }
        };
        $this->chrono(
            'products',
            $products,
            $createProductClosure
        );

        $this->outputStats();
    }

    /**
     * @return int the new created company Id
     */
    private function createCompany(int $id): int
    {
        // fix memory leak
        $this->doctrine->resetManager();

        $name = \sprintf(
            'company:%s:%s',
            $id,
            $this->uuid
        );

        $email = \sprintf(
            '%<EMAIL>',
            \uniqid()
        );

        $companyId = (int) fn_update_company(
            [
                'company' => $name,
                'email' => $email,
                'status' => Status::ENABLED(),
                'shippings' => [1]
            ]
        );

        $auth = [];

        fn_update_user(
            null,
            [
                'user' => $name,
                'company_id' => $companyId,
                'email' => $email,
                'status' => Status::ENABLED(),
                'user_type' => UserType::VENDOR(),
                'api_key' => "vendor_$companyId",
            ],
            $auth,
            false,
            false,
        );

        return $companyId;
    }

    /**
     * @return \Generator<int> company Ids
     */
    private function createCompanies(int $qty): \Generator
    {
        foreach (Helper::xrange($qty) as $id) {
            yield $this->createCompany($id);
        }
    }

    /**
     * @return int the new created product Id
     */
    private function createProduct(
        int $categoryId,
        int $companyId,
        int $mvpId,
        int $id
    ): int {
        // fix memory leak
        $this->doctrine->resetManager();

        $name = \sprintf(
            'product:%s:%s',
            $id,
            $this->uuid
        );

        $productCode = \sprintf(
            'MVP:%s:%s',
            $mvpId,
            $this->uuid
        );

        $response = $this->tyghProducts->create(
            [
                'product' => $name,
                'main_category' => $categoryId,
                'w_supplier_ref' => $productCode,
                'price' => 777,
                'infinite_stock' => false,
                'amount' => 9999999,
                'status' => Status::ENABLED(),
                'tax_ids' => [3],
                'company_id' => $companyId
            ]
        );

        return $response['data']['product_id'];
    }

    /**
     * Recursive method for creating a category tree
     *
     * @return \Generator<int> leaf category id
     */
    private function buildCategoryTree(int $depth, int $leaves, int $parentId = 0): \Generator
    {
        foreach (Helper::xrange($leaves) as $leaf) {
            $id = $this->createCategory($depth, $leaf, $parentId);

            if ($depth > 1) {
                yield from $this->buildCategoryTree(
                    $depth - 1,
                    $leaves,
                    $id
                );
            } else {
                yield $id;
            }
        }
    }

    /**
     * @return int categoryId
     */
    private function createCategory(
        int $depth,
        int $leaf,
        $parentId = 0
    ): int {
        // fix memory leak
        $this->doctrine->resetManager();

        $name = \sprintf(
            'category:%s:%s:%s',
            $depth,
            $leaf,
            $this->uuid
        );

        return (int) fn_update_category(
            [
                'category' => $name,
                'parent_id' => $parentId,
                'transaction_mode' => TransactionMode::TRANSACTIONAL()->getValue(),
            ]
        );
    }

    /**
     *  Poorman's chrono decorator
     *
     * @return mixed
     */
    private function chrono(
        string $key,
        int $qty,
        \Closure $closure
    ) {
        $start = \hrtime(true);
        $data = $closure();
        $elapsed = \hrtime(true) - $start; # in nanoseconds
        $speed = $qty / ($elapsed * 10 ** -9); # in seconds

        $this->stats[$key] = [
            'elapsed' => $elapsed,
            '_elapsed' => 'Total time in nanoseconds.',
            'qty' => $qty,
            'speed' => $speed,
            '_speed' => 'Units processed in a second.',
        ];

        return $data;
    }

    /**
     * Output stats as json
     */
    private function outputStats(): void
    {
        $timeZone =  new \DateTimeZone('UTC');
        $now = new \DateTimeImmutable(
            'now',
            $timeZone
        );

        echo \json_encode(
            \array_merge(
                [
                    'version' => $this->version,
                    'datetime' => $now->format(\DateTimeImmutable::ATOM),
                    'timezone' => $timeZone->getName(),
                ],
                $this->stats
            ),
            JSON_PRETTY_PRINT
            | JSON_UNESCAPED_SLASHES
        ) . PHP_EOL;
    }
}
