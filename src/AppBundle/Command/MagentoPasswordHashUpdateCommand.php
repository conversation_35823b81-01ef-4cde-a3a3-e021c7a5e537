<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Marketplace\Payment\UserPaymentInfoService;
use Wizacha\Marketplace\User\User;
use Wizacha\Marketplace\User\UserService;

/**
 * To be used in the context of an ETL Migration of users from Magento.
 * It requires to have an import file uploaded to the var/imports/ local directory.
 */
class MagentoPasswordHashUpdateCommand extends Command
{
    protected const ARG_IMPORT_KEY = 'import-file';

    protected static $defaultName = 'migration:magento:passwords';

    protected static $counter = 0;

    /** @var UserService */
    protected $userService;

    /** @var EntityManagerInterface */
    protected $entityManager;

    /** @var UserPaymentInfoService */
    protected $userPaymentInfoService;

    public function __construct(
        EntityManagerInterface $entityManager,
        UserService $userService,
        UserPaymentInfoService $userPaymentInfoService
    ) {
        parent::__construct();

        $this->entityManager = $entityManager;
        $this->userService = $userService;
        $this->userPaymentInfoService = $userPaymentInfoService;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Import users Magento hashes and salts')
            ->setHelp('Use generated import file from Wizaplace ETL to update users credentials')
            ->addArgument(
                static::ARG_IMPORT_KEY,
                InputArgument::REQUIRED,
                'Path to the import file containing user hashs.'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $importFile = $input->getArgument(static::ARG_IMPORT_KEY);

        if (is_readable($importFile) === false) {
            $io->error('Import file not found in ' . $importFile);

            return 1;
        }

        $io->writeln('Starting to update users from provided import file: ' . $importFile);

        $handler = fopen($importFile, 'r');

        // drop the header
        fgets($handler);

        while ([$userId, $email, $hash, $salt, $sddAgreementId] = fgetcsv($handler, 0, ';')) {
            $this->processUser($io, $userId, $email, $hash, $salt, $sddAgreementId);
        }

        $this->entityManager->flush();

        fclose($handler);

        $io->writeln('Update finished with ' . static::$counter . ' updated.');

        return 0;
    }

    protected function processUser(
        SymfonyStyle $io,
        string $userId,
        string $email,
        string $hash,
        string $salt,
        string $sddAgreementId
    ): void {
        $user = $this->userService->get($userId);

        if ($user instanceof User === false) {
            $io->error('User not found (#' . $userId . ')');

            return;
        }

        if ($user->getEmail() !== $email) {
            $io->error(
                'Skipping invalid email for user #' . $user->getUserId() . ': ' . $email . '->' . $user->getEmail()
            );

            return;
        }

        ++static::$counter;

        $io->writeln(sprintf(
            '[%05d] Updating user hash #%d - %s',
            static::$counter,
            $user->getUserId(),
            $user->getEmail()
        ));

        $user->setPassword($hash);
        $user->setSalt($salt);

        $this->entityManager->persist($user);

        if (\strlen($sddAgreementId) > 0) {
            $this->userPaymentInfoService->setHipaySepaAgreement($user->getUserId(), (int) $sddAgreementId, false);
        }
    }
}
