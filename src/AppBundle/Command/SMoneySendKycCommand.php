<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Wizacha\Marketplace\Company\CompanyEvents;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Company\Event\CompanyLegalDocumentsUpdated;
use Wizacha\Marketplace\Payment\Processor\SMoney;

class SMoneySendKycCommand extends Command
{
    public const COMPANY_ID_KEY = 'company_id';

    /** @var SMoney */
    protected $smoney;

    /** @var CompanyService */
    protected $companyService;

    /** @var LoggerInterface */
    protected $logger;

    /** @var EventDispatcherInterface */
    protected $eventDispatcher;

    public function __construct(
        SMoney $smoney,
        CompanyService $companyService,
        LoggerInterface $logger,
        EventDispatcherInterface $eventDispatcher
    ) {
        parent::__construct();

        $this->smoney = $smoney;
        $this->companyService = $companyService;
        $this->logger = $logger;
        $this->eventDispatcher = $eventDispatcher;
    }

    protected function configure()
    {
        $this
            ->setName('smoney:send-kyc')
            ->setDescription('Send the KYC of all pending and C2C companies for SMoney')
            ->addOption("companyId", null, InputOption::VALUE_OPTIONAL, "A specific companyId")
            ->addOption(
                'no-pending-only',
                null,
                InputOption::VALUE_NONE,
                'Check the KYC of all companies except company with status = new'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        if (!$this->smoney->isConfigured()) {
            return;
        }

        $companyId = $input->getOption("companyId");

        if (false === \is_null($companyId) && false === is_numeric($companyId)) {
            $output->write("<error>--companyId must be an integer.</error>");

            return 1;
        }

        return $this->process($input, $output, $companyId);
    }

    protected function process(InputInterface $input, OutputInterface $output, int $companyId = null)
    {
        if (false === \is_null($companyId) && true === is_numeric($companyId)) {
            return $this->sendCompanyKyc((int) $companyId, $output);
        }

        if ($input->getOption('no-pending-only') === true) {
            $statement = $this->companyService->getAllVendorsExceptNew();
        } else {
            $statement = $this->companyService->getPendingVendors(true);
        }

        return $this->sendCompaniesKyc($statement, $output);
    }

    protected function sendCompaniesKyc(\PDOStatement $statement, OutputInterface $output): int
    {
        $progressBar = new ProgressBar($output, $statement->rowCount());
        $progressBar->start();

        $companiesErrors = [];
        while ($company = $statement->fetch()) {
            try {
                if ($this->smoney->isCompanyActivated((int) $company[static::COMPANY_ID_KEY])) {
                    continue;
                }

                $this->smoney->getOrCreateProfessionalUser((int) $company[static::COMPANY_ID_KEY]);
                $this->eventDispatcher->dispatch(
                    new CompanyLegalDocumentsUpdated((int) $company[static::COMPANY_ID_KEY], []),
                    CompanyEvents::LEGAL_DOCUMENTS_UPDATED
                );
                $progressBar->advance();
            } catch (\Exception $exception) {
                $companiesErrors[$company[static::COMPANY_ID_KEY]] = $exception->getMessage();
            }
        }

        $progressBar->finish();

        if (\count($companiesErrors) > 0) {
            foreach ($companiesErrors as $companyId => $message) {
                $output->writeln("<error>Smoney error send KYC #{$companyId}: {$message}</error>");
            }

            return 1;
        }

        return 0;
    }

    protected function sendCompanyKyc(int $companyId, OutputInterface $output): int
    {
        try {
            if ($this->smoney->isCompanyActivated($companyId)) {
                return 0;
            }

            $this->smoney->getOrCreateProfessionalUser($companyId);
            $this->eventDispatcher->dispatch(
                new CompanyLegalDocumentsUpdated($companyId, []),
                CompanyEvents::LEGAL_DOCUMENTS_UPDATED
            );
        } catch (\Exception $exception) {
            $output->writeln("<error>Smoney error send KYC #{$companyId}: {$exception->getMessage()}</error>");

            return 1;
        }

        return 0;
    }
}
