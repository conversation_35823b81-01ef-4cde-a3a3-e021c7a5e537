<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\ConfirmationQuestion;
use Wizacha\Marketplace\Date\DateService;
use Wizacha\Marketplace\Subscription\Exception\NoProductToRenewException;
use Wizacha\Marketplace\Subscription\SubscriptionRepository;
use Wizacha\Marketplace\Subscription\SubscriptionService;
use Wizacha\Marketplace\Subscription\SubscriptionStatus;
use Wizacha\Marketplace\User\UserService;

class RenewSubscriptionCommand extends Command
{
    protected SubscriptionService $subscriptionService;
    protected SubscriptionRepository $subscriptionRepository;
    protected DateService $dateService;
    protected UserService $userService;
    protected LoggerInterface $logger;

    public function __construct(
        SubscriptionService $subscriptionService,
        SubscriptionRepository $subscriptionRepository,
        DateService $dateService,
        UserService $userService,
        LoggerInterface $logger
    ) {
        parent::__construct();

        $this->subscriptionService = $subscriptionService;
        $this->subscriptionRepository = $subscriptionRepository;
        $this->dateService = $dateService;
        $this->userService = $userService;
        $this->logger = $logger;
    }

    protected function configure()
    {
        $this
            ->setName('subscription:renew')
            ->setDescription("Renew subscriptions.")
            ->setHelp("Set to FINISHED old subscriptions and renew all subscriptions with status ACTIVE or DEFAULTED.")
            ->addArgument('subscription_id', InputArgument::OPTIONAL, 'id of the subscription to renew')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $subscriptionId = $input->getArgument('subscription_id');

        if ($subscriptionId) {
            $subscription = $this->subscriptionRepository->findOneById($subscriptionId);
            if (null !== $subscription) {
                $helper = $this->getHelper('question');
                $question = new ConfirmationQuestion(
                    "You are going to trigger a payment for a renewal order. Confirm ? (y/N)",
                    false,
                    "/^(y|yes)$/i"
                );

                if ($helper->ask($input, $output, $question)) {
                    try {
                        $this->subscriptionService->renew($subscription, true);
                    } catch (NoProductToRenewException $e) {
                        $this->subscriptionService->updateSubscriptionStatus($subscription, SubscriptionStatus::FINISHED());
                        $output->writeln('<comment>' . $e->getMessage() . '</comment>');
                    } catch (\Exception $exception) {
                        $output->writeln('<comment>' . $exception->getMessage() . '</comment>');
                    }
                }
                return 0;
            }

            $this->logger->error("Subscription not Found, check the subscription_id \n");
            return 1;
        }

        return $this->subscriptionService->updateSubscriptions($output);
    }
}
