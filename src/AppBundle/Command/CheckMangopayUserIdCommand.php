<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Driver\Connection;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Exception\NotFound;
use Wizacha\Marketplace\Payment\Processor\MangoPay;
use Wizacha\Status;

class CheckMangopayUserIdCommand extends Command
{
    /** @var MangoPay */
    private $mangoPay;

    /** @var Connection */
    private $connection;

    public function __construct(MangoPay $mangoPay, Connection $connection)
    {
        parent::__construct();

        $this->mangoPay = $mangoPay;
        $this->connection = $connection;
    }

    protected function configure(): void
    {
        $this
            ->setName('mangopay:check-id')
            ->setDescription('Check if the column mangopay_id from cscart_companies was a good Mangopay Id or not.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if (false === $this->mangoPay->isConfigured()) {
            $output->writeln("<error>Mangopay is not configured.</error>");
        }

        $query = $this
            ->connection
            ->prepare("SELECT company_id FROM cscart_companies WHERE status = :enabled OR status = :pending");
        $query->execute([
            'enabled' => Status::ENABLED,
            'pending' => Status::PENDING,
        ]);

        $errors = [];
        while ($row = $query->fetch(\PDO::FETCH_OBJ)) {
            // phpcs:ignore
            $company = new Company($row->company_id);
            $oldMangopayId = $company->getMangopayId();

            try {
                [0 => $mangopayUserId, 2 => $newUser] = $this->mangoPay->getCompanyUserAndWallet($company);
            } catch (NotFound $exception) {
                // Il n'y a pas d'admin de marchand, il se peut que se soit le compte du support-login
                $output->writeln("<info>Skip company #{$company->getId()}. No admin found.</info>");
                continue;
            }


            if ($newUser) {
                if (\is_string($oldMangopayId) && $oldMangopayId !== $mangopayUserId) {
                    $output->writeln("<info>Existing Mangopay id for company #{$company->getId()} not found. New account has been created.</info>");
                } else {
                    $output->writeln("<info>No account found for company #{$company->getId()}. Account has been created.</info>");
                }
            } else {
                $output->writeln("<info>Existing Mangopay id for company #{$company->getId()} was found. That's OK.</info>");
            }
        }

        $return = 0;
        if (\count($errors) > 0) {
            $output->writeln("<info>All companies has been checked with some errors.</info>");
            foreach ($errors as $error) {
                $output->writeln("<error>$error</error>");
            }

            $return = 1;
        } else {
            $output->writeln("<info>All companies has been checked without error.</info>");
        }

        return $return;
    }
}
