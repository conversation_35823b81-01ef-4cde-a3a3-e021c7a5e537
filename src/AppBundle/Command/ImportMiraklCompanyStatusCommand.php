<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Marketplace\Company\Company;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Company\CompanyStatus;
use Wizacha\Marketplace\User\UserService;
use Wizacha\User;

/**
 * In Mirakl migration context, this command is used to disable
 * companies and their associated users which are disabled in Mirakl.
 */
class ImportMiraklCompanyStatusCommand extends Command
{
    public const DEFAULT_PATH = 'php://stdin';

    protected static $defaultName = 'import:mirakl:company-status';

    protected static $counter = 0;

    /** @var CompanyService */
    protected $companyService;

    /** @var UserService */
    protected $userService;

    public function __construct(CompanyService $companyService, UserService $userService)
    {
        parent::__construct();

        $this->companyService = $companyService;
        $this->userService = $userService;
    }

    protected function configure()
    {
        $this
            ->setDescription(
                'Update companies and vendors status based on Mirakl status'
            )
            ->addArgument(
                'path',
                InputArgument::OPTIONAL,
                'optional file path, use standard input (php://stdin) if empty'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $path = $input->getArgument('path') ?? static::DEFAULT_PATH;

        $io->writeln('Starting to update companies from provided import file: ' . $path);

        $handler = fopen($path, 'r');

        // drop the header
        fgets($handler);

        while ([, $companyId, $status] = fgetcsv($handler, 0, ';')) {
            $this->processCompany($io, (int) $companyId, $status);
        }

        fclose($handler);

        $io->writeln('Update finished with ' . static::$counter . ' updated.');

        return 0;
    }

    protected function processCompany(SymfonyStyle $io, int $companyId, string $status): void
    {
        $company = $this->companyService->get($companyId);

        if (false === $company instanceof Company) {
            $io->error('User not found (#' . $companyId . ')');

            return;
        }

        $status = $this->getWizaplaceStatus($status);
        if (true === CompanyStatus::ENABLED()->equals($status)) {
            $io->writeln(sprintf(
                '[%04d] company %d is enabled. Skipping.',
                static::$counter,
                $company->getId()
            ));

            return;
        }

        ++static::$counter;

        $io->writeln(sprintf(
            '[%04d] Updating company %d and its vendors',
            static::$counter,
            $company->getId()
        ));

        $this->companyService->updateStatus($company->getId(), $status);

        $legacyCompany = $this->companyService->getLegacyEntity($company->getId());
        $users = $legacyCompany->getAdmins();

        if (\count($users) > 0) {
            $this->userService->bulkDisable(array_map(function (User $user): int {
                return $user->getId();
            }, $users));
        }
    }

    protected function getWizaplaceStatus(string $miraklStatus): CompanyStatus
    {
        switch ($miraklStatus) {
            case 'ENABLED':
                return CompanyStatus::ENABLED();
            case 'DISABLED':
                return CompanyStatus::DISABLED();
            default:
                throw new \Exception('Unknown status: ' . $miraklStatus);
        }
    }
}
