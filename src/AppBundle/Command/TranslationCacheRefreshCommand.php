<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\AppBundle\Service\TranslationService;

class TranslationCacheRefreshCommand extends Command
{
    protected static $defaultName = 'translation:cache:refresh';

    /**
     * @var TranslationService
     */
    private $translationService;

    public function __construct(TranslationService $translationService)
    {
        parent::__construct();
        $this->translationService = $translationService;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Refresh the translation cache if needed')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title("Translation Cache Refresh");

        $refreshed = $this->translationService->refreshCache();

        if ($refreshed) {
            $io->success("Cache has been refreshed");
        } else {
            $io->success("Cache did not need refreshing");
        }

        return 0;
    }
}
