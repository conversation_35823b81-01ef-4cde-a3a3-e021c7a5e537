<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use GuzzleHttp\Client;
use Guz<PERSON>Http\Exception\BadResponseException;
use Guz<PERSON>Http\Psr7\ServerRequest;
use GuzzleHttp\RequestOptions;
use Psr\Http\Message\ResponseInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Serializer\Encoder\JsonDecode;
use Symfony\Component\Serializer\Encoder\JsonEncoder;
use Symfony\Component\Serializer\Exception\NotEncodableValueException;

/** This command updates a batch of users from a datasource file */
class UserUpdateCommand extends Command
{
    protected static $defaultName = 'user:update:csv';

    protected const DELIMITER = ';';

    /** @var RouterInterface */
    protected $router;

    /** @var Client */
    protected $httpClient;

    /** @var OutputInterface */
    protected $output;

    public function __construct(RouterInterface $router)
    {
        parent::__construct();

        $this->router = $router;
    }


    /**
     * More options can be added but mapping has to stay up-to-date
     * @see UserUpdateCommand::extractMapping()
     */
    protected function configure(): void
    {
        $this
            ->setDescription('Update existing customer from CSV datasource')
            ->addArgument('filename', InputArgument::REQUIRED, 'Absolute filename and path of datasource (.csv)')
            ->addOption(
                'no-header',
                null,
                InputOption::VALUE_NONE,
                'Specify if datasource file has no header line'
            )
            // These two options are required to authenticate user (mapped by default as A and B columns)
            ->addOption('email', null, InputOption::VALUE_REQUIRED, 'Email column letter', 'A')
            ->addOption('pwd', null, InputOption::VALUE_REQUIRED, 'Password column letter', 'B')
            // Below stands the flexible list of user columns to update (or to ignore if cell values are empty)
            ->addOption('company', null, InputOption::VALUE_REQUIRED, 'Company column letter', 'C')
            ->addOption(
                'external-id',
                null,
                InputOption::VALUE_REQUIRED,
                'External identifier column letter',
                'D'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $filename = $input->getArgument('filename');

        if (\is_null($filename)
            || false === \file_exists($filename)
            || 'csv' !== pathinfo($filename, PATHINFO_EXTENSION)
        ) {
            $output->writeln(sprintf("<error>%s</error>", "Please provide a valid CSV filename path with -f option"));
            return 0;
        }
        $this->httpClient = new Client();
        $this->output = $output;

        // Extract column mapping from first row
        [$emailColumn, $passwordColumn, $mapping] = $this->extractMapping($input);
        $treatFirstRow = $input->getOption('no-header');

        // Open file for reading
        $handle = fopen($filename, 'r');
        if ($handle !== false) {
            $line = $treatFirstRow ? 0 : 1;

            while (($row = fgetcsv($handle, 1000, static::DELIMITER)) !== false) {
                // Skip header line
                if (0 === $line++) {
                    continue;
                }

                [$email, $password, $userProfileData] = $this->prepareUserProfileData(
                    $row,
                    $mapping,
                    $emailColumn,
                    $passwordColumn
                );

                if (\is_null($email) || \is_null($password)) {
                    continue;
                }

                $this->treatUserProfileData($email, $password, $userProfileData);
            }
            fclose($handle);
        } else {
            return 0;
        }

        return 1;
    }

    /** Extract CSV file mapping from command options */
    protected function extractMapping(InputInterface $input): array
    {
        $ord = \ord('A');

        return [
            \ord($input->getOption('email')) - $ord,
            \ord($input->getOption('pwd')) - $ord,
            [
                // The following list can be completed if corresponding options are added to the command
                'company' => \ord($input->getOption('company')) - $ord,
                'externalIdentifier' => \ord($input->getOption('external-id')) - $ord,
            ],
        ];
    }

    protected function prepareUserProfileData(
        array $row,
        array $mapping,
        int $emailColumn,
        int $passwordColumn
    ): array {
        // Format empty cells as NULL (can be either empty strings or NULL depending on datasource origin)
        foreach ($row as &$cellValue) {
            if (\is_string($cellValue) && "" === trim($cellValue)) {
                $cellValue = null;
            }
        }

        $userProfileData = [];
        foreach ($mapping as $name => $index) {
            $userProfileData[$name] = $row[$index];
        }

        return [$row[$emailColumn], $row[$passwordColumn], $userProfileData];
    }

    protected function treatUserProfileData(
        string $email,
        string $password,
        array $userProfileData
    ): void {
        try {
            // Authenticate user
            $userData = $this->authenticateUser($email, $password);

            // Extract user profile data from CSV row
            // Update user profile
            $updatedUserProfile = $this->updateUserProfile(
                $userData['id'],
                $userData['apiKey'],
                $userProfileData
            );

            $this->validateUpdateCompletion($userProfileData, $updatedUserProfile, $email);
        } catch (NotEncodableValueException $exception) {
            $this->output->writeln(
                sprintf(
                    "<error>%s</error>",
                    "Command was unable to decode user data '$email'"
                )
            );
        } catch (BadResponseException $exception) {
            $this->output->writeln(sprintf("<error>%s</error>", "No user matching '$email'"));
        }
    }

    protected function validateUpdateCompletion(array $expectedData, array $userProfileData, string $email): void
    {
        // Check that update is successful
        $failures = 0;
        foreach ($expectedData as $name => $value) {
            if ($userProfileData[$name] != $value) {
                $failures++;
                $this->output->writeln(
                    sprintf(
                        "<error>%s</error>",
                        sprintf(
                            "- Expected '%s' value was '%s', got '%s'",
                            $name,
                            $value,
                            $userProfileData[$name]
                        )
                    )
                );
            }
        }

        if ($failures === 0) {
            $this->output->writeln(sprintf("<info>%s</info>", "User '" . $email . "' was successfully updated"));
        } else {
            $this->output->writeln(
                sprintf(
                    "<error>%s</error>",
                    sprintf("%d error(s) thrown while updating '%s' account", $failures, $email)
                )
            );
        }
    }

    /** Authenticate user with provided email and password */
    protected function authenticateUser(string $email, string $password): array
    {
        $route = $this->router->generate('api_user_authenticate', [], RouterInterface::ABSOLUTE_URL);
        $response = $this->httpClient->get(
            $route,
            [
                'auth' => [
                    $email,
                    $password,
                ],
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
            ]
        );
        $statusCode = $response->getStatusCode();

        if ($statusCode !== 200) {
            throw new BadResponseException(
                sprintf("Client error: `%s %s`, expected %d got %d", 'GET', $route, 200, $statusCode),
                new ServerRequest('GET', $route)
            );
        }

        return $this->jsonDecodeResponse($response);
    }

    /** Update user profile after authentication */
    protected function updateUserProfile(int $userId, string $userApiKey, array $userProfileData): array
    {
        $route = $this->router->generate(
            'api_user_update',
            [
                'userId' => $userId,
            ],
            RouterInterface::ABSOLUTE_URL
        );
        $response = $this->httpClient->patch(
            $route,
            [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'Authorization' => 'token ' . $userApiKey,
                ],
                RequestOptions::JSON => $userProfileData,
            ]
        );
        $statusCode = $response->getStatusCode();

        if ($statusCode !== 200) {
            throw new BadResponseException(
                sprintf("Client error: `%s %s`, expected %d got %d", 'GET', $route, 200, $statusCode),
                new ServerRequest('GET', $route)
            );
        }

        return $this->jsonDecodeResponse($response);
    }

    protected function jsonDecodeResponse(ResponseInterface $response): array
    {
        $jsonDecoder = new JsonDecode();

        return $jsonDecoder->decode(
            $response->getBody()->getContents(),
            JsonEncoder::FORMAT,
            ['json_decode_associative' => true]
        );
    }
}
