<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Marketplace\Entities\Company;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Payment\PayoutFrequency;
use Wizacha\Marketplace\Payment\Processor\AbstractProcessor;
use Wizacha\Marketplace\Payment\Processor\Exception\NoValidProcessorException;
use Wizacha\Marketplace\Payment\Processor\PayoutService;

class PayoutCompaniesCommand extends Command implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    /** @var iterable|AbstractProcessor[] */
    private iterable $processors;

    private string $frequencyPayout;

    private PayoutService $payoutService;

    public function __construct(
        iterable $processors,
        string $frequencyPayout,
        PayoutService $payoutService
    ) {
        parent::__construct();
        $this->frequencyPayout = $frequencyPayout;
        $this->processors = $processors;
        $this->payoutService = $payoutService;
    }

    protected function configure()
    {
        $this
            ->setName('company:payout:trigger')
            ->setDescription('Trigger payout of all companies')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $logger = $this->logger;

        if ($this->canDoPayoutCompany() === false) {
            return 0;
        }

        try {
            $processor = $this->payoutService->getFirstValidProcessor();
        } catch (NoValidProcessorException $exception) {
            $output->writeln('<error>' . $exception->getMessage() . '</error>');

            return 0;
        }

        if ($processor->canDoPayoutCompany() === false) {
            $output->writeln('<error>' . __('cant_payout_company', ["[PSP]" => $processor->getName()->getValue()]) . '</error>');

            return 0;
        }

        if (PaymentProcessorName::HIPAY()->equals($processor->getName()) === true) {
            $companies = db_get_fields('SELECT company_id FROM cscart_companies WHERE hipay_id IS NOT NULL');
        } else {
            $companies = db_get_fields('SELECT company_id FROM cscart_companies');
        }

        foreach ($companies as $companyId) {
            try {
                $processor->assertCanDoPayoutCompany(new Company($companyId));
                $output->writeln(
                    sprintf('<info>' . $processor->getName() . ': Payout company %d executed</info>', $companyId)
                );
            } catch (\Exception $e) {
                $output->writeln('<error>' . $processor->getName() . ': Fail with company ' . $companyId . ' : ' . $e->getMessage() . '</error>');
                $logger->error('Payout ' . $processor->getName() . ': Fail with company ' . $companyId . ' : ' . $e->getMessage(), ['exception' => $e]);
            }

            sleep(1);
        }

        return 0;
    }

    private function canDoPayoutCompany(): bool
    {
        switch ($this->frequencyPayout) {
            case PayoutFrequency::DAILY()->getValue():
                return true;
            case PayoutFrequency::BIMONTHLY()->getValue():
                return \date('d') === '01' || \date('d') === '15';
            case PayoutFrequency::MONTHLY()->getValue():
                return \date('d') === '01';
            case PayoutFrequency::MANUAL()->getValue():
                return false;
            case PayoutFrequency::WEEKLY()->getValue():
                return \date('D') === 'Mon';
        }

        return false;
    }
}
