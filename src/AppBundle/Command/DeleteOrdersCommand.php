<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\Question;

class DeleteOrdersCommand extends Command
{
    /** @var Connection */
    private $connection;

    public function __construct(Connection $connection)
    {
        parent::__construct();
        $this->connection = $connection;
    }

    protected function configure(): void
    {
        $this
            ->setName('orders:delete')
            ->setDescription('Delete some orders')
            ->addArgument(
                'ids',
                InputArgument::IS_ARRAY | InputArgument::REQUIRED,
                'order_id(s) list space separated'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if (false === $input->isInteractive()) {
            $output->writeln('<error>Non interactive shell! Aborting.</error>');

            return 1;
        }

        $ids = $input->getArgument('ids');

        foreach ($ids as $orderId) {
            if (\is_numeric($orderId) === false) {
                $output->writeln("<error>Invalid orderId(s)</error>");

                return 1;
            }
        }

        $passphrase = 'REALLY';
        $question = new Question(sprintf('Write %s if you are REALLY sure of what you are doing: ', $passphrase));

        do {
            $response = $this
                ->getHelperSet()
                ->get('question')
                ->ask($input, $output, $question);
        } while ($response !== $passphrase);

        $tablesToDelete = [
            'orders_actions_traces',
            'doctrine_order_transactions',
            'doctrine_organisation_order',
            'cscart_order_data',
            'cscart_order_details',
            'cscart_order_docs',
            'cscart_orders',
        ];

        $in  = str_repeat('?,', \count($ids) - 1) . '?';
        foreach ($tablesToDelete as $table) {
            $output->writeln("Remove orders from $table...");
            $stmt =  $this->connection->prepare("DELETE FROM `$table` WHERE `order_id` IN ($in)");
            $stmt->execute($ids);
        }

        $output->writeln('Done');

        return 0;
    }
}
