<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Marketplace\Order\OrderService;

class OrderCancelRollbackCommand extends Command
{
    /** @var Connection */
    private $connection;

    /** @var OrderService */
    private $orderService;

    public function __construct(Connection $connection, OrderService $orderService)
    {
        parent::__construct();

        $this->connection = $connection;
        $this->orderService = $orderService;
    }

    protected function configure(): void
    {
        $this
            ->setName('orders:cancel:rollback')
            ->setDescription('Rollback the cancel flag and the legacy status on an Order')
            ->addArgument('orderId', InputArgument::REQUIRED, 'The id of the Order');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $orderId = (int) $input->getArgument('orderId');

        // Set cancel flag to false
        $this->connection->update(
            'cscart_orders',
            ['canceled' => false],
            ['order_id' => $orderId]
        );

        // Set the deduce legacy Status
        $this->orderService->overrideOrderStatus(
            $this->orderService->getOrder($orderId)
        );

        // Finish output
        $updatedOrder = $this->orderService->getOrder($orderId);
        $currentProgress = $updatedOrder->getWorkflowProgress();

        $output->writeln('Order ' . $orderId . ' updated');
        $output->writeln('------------------------');
        $output->writeln('isCanceled: ' . (true === $updatedOrder->isCanceled() ? 'true' : 'false'));
        $output->writeln('Legacy status: ' . $updatedOrder->getStatus()->getKey());
        $output->writeln('current Module: ' . $currentProgress->getModuleName()->getValue());
        $output->writeln('current Step: ' . $currentProgress->getStepName()->getValue());
        $output->writeln('current Status: ' . $currentProgress->getStatus());

        return 0;
    }
}
