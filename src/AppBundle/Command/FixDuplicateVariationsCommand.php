<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright Copyright (c) Wizaplace
 * @license   Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Driver\Statement;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\Question;
use Symfony\Component\Console\Style\SymfonyStyle;

class FixDuplicateVariationsCommand extends Command
{
    protected static $defaultName = 'fix:duplicated-variations';

    /** @var Connection */
    protected $connection;

    public function __construct(
        Connection $connection
    ) {
        parent::__construct();

        $this->connection = $connection;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Fix duplicated variations')
            ->addOption(
                'info',
                'i',
                InputOption::VALUE_NONE,
                'Extended info'
            )
            ->addOption(
                'dry-run',
                'd',
                InputOption::VALUE_NONE,
                'Check only for duplicated variant (no commit to db)'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $extendedInfo = $input->getOption('info') ?? true;
        $dryRun = $input->getOption('dry-run') ?? true;

        $io = new SymfonyStyle($input, $output);

        $io->title('Fix duplicated variants');

        // ALL duplicated variant name
        $duplicateVariantStatement = $this->getDuplicatedVariantStatement();
        $total = $duplicateVariantStatement->rowCount();
        $progressBar = $io->createProgressBar($total);

        $this->connection->beginTransaction();

        $duplicates = [];

        while ($variant = $duplicateVariantStatement->fetch()) {
            // Get the variant Id and option Id for duplicated variant name.
            $combinations = [];
            $variantIds = [];
            $variants = $this->getVariantByNameAndOptionId($variant);

            foreach ($variants as $variantData) {
                $combinations[] = $this->generateCombination($variantData['option_id'], $variantData['variant_id']);
                $variantIds[] = $variantData['variant_id'];
            }

            $productIdsWithDoubleOptionInventory = $this->getProductIdsWithDoubleOptionInventory($combinations);

            $this->removeOptionsDoubleInventoryForProduct($productIdsWithDoubleOptionInventory, $combinations);
            $this->updateOptionsInventory($combinations);

            $products = $this->getProductWillUpdateByCombination($combinations[0]);
            $productId = null;
            foreach ($products as $product) {
                if ($productId !== (int) $product['product_id']) {
                    $productId = (int) $product['product_id'];
                    $this->relinkProductOptionsExceptions($productId);
                }
            }
            $this->removeOptionVariants($variantIds);

            $duplicates[] = [
                'variant' => \json_encode($variant, JSON_PRETTY_PRINT),
                'combinations' => implode(', ', $combinations),
            ];
            $progressBar->advance();
        }

        $progressBar->finish();
        $progressBar->clear();

        if ((true === $dryRun || true === $extendedInfo)
            && \count($duplicates)
        ) {
            $io->table(
                \array_keys($duplicates[0]),
                $duplicates
            );
        }

        $plural = $total > 1 ? 's' : '';

        if (true === $dryRun) {
            $this->connection->rollBack();

            $hasError = $total > 0;

            if (true === $hasError) {
                $io->error(
                    sprintf(
                        "%s duplicated variant%s",
                        $total,
                        $plural
                    )
                );
            } else {
                $io->success(
                    'check completed'
                );
            }

            return (int) $hasError;
        }

        $response = $io->askQuestion(
            new Question(
                sprintf(
                    "Answer REALLY, if you REALLY want to remove %d variant%s",
                    $total,
                    $plural
                )
            )
        );


        if ('REALLY' === $response) {
            $this->connection->commit();
            $io->success("$total fixed variant$plural");
        } else {
            $this->connection->rollBack();
            $io->warning('Aborted');

            return 0;
        }

        // fix combinations hash
        return $this
            ->getApplication()
            ->find('products:declinations:update-hash')
            ->run(
                $input,
                $output
            );
    }

    /**
     * Get All duplicated variant
     * @throws \Doctrine\DBAL\DBALException
     */
    private function getDuplicatedVariantStatement(): Statement
    {
        $statement = $this->connection->prepare(
            'SELECT
                count(V.variant_id) AS count_variants,
                O.option_id,
                V.variant_name,
                V.lang_code
            FROM
                cscart_product_option_variants_descriptions as V
                INNER JOIN cscart_product_option_variants AS O
                    ON O.variant_id = V.variant_id
            GROUP BY V.variant_name, O.option_id, V.lang_code
            HAVING count_variants > 1;'
        );

        $statement->execute();

        return $statement;
    }

    /**
     * Get Variant by name and optionId
     * @param array $variant
     * @return array
     * @throws \Doctrine\DBAL\DBALException
     */
    private function getVariantByNameAndOptionId(array $variant): array
    {
        $result = [];
        $variants = $this->connection->prepare(
            'SELECT
                O.option_id, V.variant_id
            FROM
                cscart_product_option_variants_descriptions as V
                INNER JOIN cscart_product_option_variants as O
                    ON O.variant_id = V.variant_id
            WHERE
                V.variant_name = :variant_name
                AND V.lang_code = :lang_code
                AND O.option_id = :option_id
            ORDER BY V.variant_id ASC;
            '
        );
        $variants->execute(
            [
                'variant_name' => $variant['variant_name'],
                'option_id' => $variant['option_id'],
                'lang_code' => $variant['lang_code']
            ]
        );
        while ($variant = $variants->fetch()) {
            $result[] = $variant;
        }

        return $result;
    }

    /**
     * Generate combination optionId_variantId
     * @param $optionId
     * @param $variantId
     * @return string
     */
    private function generateCombination(string $optionId, string $variantId): string
    {
        return $optionId . '_' . $variantId;
    }

    /**
     * Remove all the old options inventory in double for product
     * @param $ProductIds
     * @param $combinations
     * @throws \Doctrine\DBAL\DBALException
     */
    private function removeOptionsDoubleInventoryForProduct(array $productIds, array $combinations): void
    {
        if (\count($combinations) > 1) {
            $in = str_repeat('?,', \count($combinations) - 1) . '?';
        } else {
            $in = '?';
        }
        foreach ($productIds as $optionInventoryData) {
            $conditionValues = $combinations;
            $conditionValues[] = (int) $optionInventoryData['product_id'];
            $LIMIT = (int) ((int) $optionInventoryData['count_options_inventory'] - 1);
            $removeOptionsInventory = $this->connection->prepare(
                "DELETE FROM cscart_product_options_inventory WHERE `combination` IN ($in) AND `product_id` = ? ORDER BY id ASC LIMIT $LIMIT"
            );
            $removeOptionsInventory->execute($conditionValues);
        }
    }

    /**
     * Get the product Ids used by combination to update
     * @param $combination
     * @return array
     * @throws \Doctrine\DBAL\DBALException
     */
    private function getProductWillUpdateByCombination(string $combination): array
    {
        $productIdsResult = [];
        $productIds = $this->connection->prepare(
            "SELECT product_id FROM cscart_product_options_inventory WHERE combination = ? Order BY product_id"
        );
        $productIds->execute([$combination]);
        while ($productId = $productIds->fetch()) {
            $productIdsResult[] = $productId;
        }

        return $productIdsResult;
    }

    /**
     * Update All the Options inventory used to set only one value is the first $combinations[0]
     * @param $combinations
     * @throws \Doctrine\DBAL\DBALException
     */
    private function updateOptionsInventory(array $combinations): void
    {
        if (\count($combinations) > 2) {
            $in = str_repeat('?,', \count($combinations) - 2) . '?';
        } else {
            $in = '?';
        }
        $updateOptionsInventory = $this->connection->prepare(
            "UPDATE
                cscart_product_options_inventory
            SET
                combination = ?
            WHERE
                combination IN ($in)
            "
        );
        $updateOptionsInventory->execute($combinations);
    }

    /**
     *
     * @param array $combinations
     * @return array
     * @throws \Doctrine\DBAL\DBALException
     */
    private function getProductIdsWithDoubleOptionInventory(array $combinations): array
    {
        $productIdsResult = [];
        if (\count($combinations) > 1) {
            $in = str_repeat('?,', \count($combinations) - 1) . '?';
        } else {
            $in = '?';
        }
        $productIdsWithDoubleOptionInventory = $this->connection->prepare(
            "SELECT
                count(id) AS count_options_inventory, product_id
            FROM
                cscart_product_options_inventory
            WHERE
                combination IN ($in)
            GROUP BY product_id
            HAVING count_options_inventory > 1;"
        );
        $productIdsWithDoubleOptionInventory->execute($combinations);
        while ($productId = $productIdsWithDoubleOptionInventory->fetch()) {
            $productIdsResult[] = $productId;
        }

        return $productIdsResult;
    }

    /**
     * Remove all the unused option variants in double
     * @param array $variantIds
     * @throws \Doctrine\DBAL\DBALException
     */
    private function removeOptionVariants(array $variantIds): void
    {
        if (\count($variantIds) > 1) {
            $in = str_repeat('?,', \count($variantIds) - 1) . '?';
        } else {
            $in = '?';
        }
        $variantIds[] = $variantIds[0];
        $removeOptionVariantsDescription = $this->connection->prepare(
            "DELETE From cscart_product_option_variants_descriptions WHERE variant_id IN ($in) AND variant_id != ?;"
        );
        $removeOptionVariantsDescription->execute($variantIds);
        $removeOptionVariants = $this->connection->prepare(
            "DELETE From cscart_product_option_variants WHERE variant_id IN ($in) AND variant_id != ?;"
        );
        $removeOptionVariants->execute($variantIds);
    }

    /**
     *
     * Relink the product with the only used variants
     * @param $productId
     * @throws \Doctrine\DBAL\DBALException
     */
    private function relinkProductOptionsExceptions(int $productId): void
    {
        // Remove all the link for the product Id
        $removeProductOptionsExceptions = $this->connection->prepare(
            "DELETE FROM cscart_product_options_exceptions WHERE product_id = ?;"
        );
        $removeProductOptionsExceptions->execute([$productId]);

        // We select the combinations used for this product
        $inventoryStatement = $this->connection->prepare(
            "SELECT combination from cscart_product_options_inventory WHERE product_id = ?"
        );
        $inventoryStatement->execute([$productId]);
        while ($inventory = $inventoryStatement->fetch()) {
            // we relink the product with used combination
            $dataCombination = [];
            [$optionId, $variantId] = \array_map(
                'intval',
                explode('_', $inventory['combination'])
            );
            $insertProductOptionsExceptions = $this->connection->prepare(
                "INSERT INTO cscart_product_options_exceptions (product_id, option_id, variant_id) VALUES (:product_id, :option_id, :variant_id)"
            );
            $insertProductOptionsExceptions->execute(
                [
                    'product_id' => $productId,
                    'option_id' => $optionId,
                    'variant_id' => $variantId,
                ]
            );
        }
    }
}
