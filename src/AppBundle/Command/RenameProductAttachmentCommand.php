<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Mime\MimeTypes;
use Wizacha\Marketplace\PIM\Product\Attachment;
use DoctrineBatchUtils\BatchProcessing\SimpleBatchIteratorAggregate;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class RenameProductAttachmentCommand extends Command
{
    private EntityManagerInterface $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        parent::__construct();

        $this->entityManager = $entityManager;
    }

    protected function configure()
    {
        $this
            ->setName('products:attachments:rename')
            ->setDescription('Rename product attachments based on original filename')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $repository = $this->entityManager->getRepository(Attachment::class);
        $storage = container()->get("Wizacha\Storage\ProductAttachmentsStorageService");
        $guesser = new MimeTypes();

        $iterator = SimpleBatchIteratorAggregate::fromQuery(
            $repository->createQueryBuilder('a')->getQuery(),
            100
        );

        foreach ($iterator as [$attachment]) {
            if (strpos($attachment->getPath(), 'url_uploaded_file_') === false) {
                continue;
            }

            try {
                $response = $attachment->getFile();
            } catch (\Exception $ex) {
                continue;
            }

            if ($response instanceof RedirectResponse) {
                $origin = $response->getTargetUrl();
            } elseif ($response instanceof BinaryFileResponse) {
                $file = $response->getFile();
                $origin = $file->getPathname();
            }

            $dest = tmpfile();
            $filename = stream_get_meta_data($dest)['uri'];
            $src = fopen($origin, 'r');
            $output->writeln("Downloading attachment to temporary destination '{$filename}'");
            stream_copy_to_stream($src, $dest);
            fclose($src);

            $mimeType = mime_content_type($filename);
            $extension = $guesser->getExtensions($mimeType)[0];
            $newPath = preg_replace('/\W+/', '-', $attachment->getName()); // safe file name

            if ($extension !== null) {
                $newPath .= '.' . $extension;
            }

            $output->writeln("Uploading attachment to new destination '{$attachment->getProduct()->getId()}/{$newPath}'");
            $storage->put($attachment->getProduct()->getId() . '/' . $newPath, [
                'file' => $filename,
                'mime-type' => $mimeType,
            ]);
            fclose($dest); // will remove tmpfile
            $output->writeln("Deleting attachment from old destination '{$attachment->getProduct()->getId()}/{$attachment->getPath()}'");
            $storage->delete($attachment->getProduct()->getId() . '/' . $attachment->getPath());
            $attachment->setPath($newPath);
            $this->entityManager->persist($attachment);
        }
    }
}
