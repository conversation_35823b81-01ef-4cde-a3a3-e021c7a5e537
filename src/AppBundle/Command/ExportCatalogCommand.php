<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Marketplace\Catalog\ExporterService;
use Wizacha\Marketplace\GlobalState\GlobalState;

class ExportCatalogCommand extends Command implements LoggerAwareInterface
{
    use LoggerAwareTrait;

    protected static $defaultName = 'export:catalog';

    protected ExporterService $exporterService;

    public function __construct(
        ExporterService $exporterService
    ) {
        parent::__construct();

        $this->exporterService = $exporterService;
    }

    protected function configure()
    {
        $this
            ->setDescription('Export Catalog')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('Export catalog');

        // On parcours les produits avec toutes les langues dispo sur la MP
        GlobalState::runWithAvailableLanguages(
            function () use ($io) {
                $io->section(
                    \sprintf(
                        'Locale %s',
                        GlobalState::contentLocale()
                    )
                );

                $progressBar = $io->createProgressBar();
                $progressBar->setFormat('debug');

                $success = 0;
                $total = 0;

                foreach ($this->exporterService->export() as $key => $value) {
                    switch ($key) {
                        case ExporterService::TOTAL_KEY:
                            $progressBar->setMaxSteps($value);
                            $total = $value;

                            break;
                        case ExporterService::TRIGGER_KEY:
                            $progressBar->advance();
                            $success += $value;

                            break;
                    }
                }

                $progressBar->finish();
                $progressBar->clear();

                $info = \sprintf(
                    '%s/%s exported',
                    $success,
                    $total
                );
                $success === $total ? $io->success($info) : $this->logger->error($info);
            }
        );
    }
}
