<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Prediggo\Exporter;

class PrediggoExportCommand extends Command
{
    protected static $defaultName = 'prediggo:export';

    /**
     * @var Exporter
     */
    private $exporter;

    /**
     * @var bool
     */
    private $enabled;

    public function __construct(Exporter $exporter, bool $enabled)
    {
        parent::__construct();

        $this->exporter = $exporter;
        $this->enabled = $enabled;
    }

    protected function configure()
    {
        $this
            ->setDescription('Export data to Prediggo format')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $io = new SymfonyStyle($input, $output);
        $io->title("Prediggo Export File Generation");

        if (!$this->enabled) {
            $io->comment("Prediggo files were not generated, this installation doesn't use Prediggo");

            return;
        }

        $this->exporter->export();

        $io->success("Prediggo files were successfully generated!");
    }
}
