<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Wizacha\Marketplace\Company\CompanyEvents;
use Wizacha\Marketplace\Company\CompanyService;
use Wizacha\Marketplace\Company\Event\CompanyLegalDocumentsUpdated;
use Wizacha\Marketplace\Payment\Processor\MangoPay;

class MangoPaySendKycCommand extends Command
{
    public const COMPANY_ID_KEY = 'company_id';

    /** @var MangoPay */
    protected $mangopay;

    /** @var CompanyService */
    protected $companyService;

    /** @var EventDispatcherInterface */
    protected $eventDispatcher;

    public function __construct(
        MangoPay $mangopay,
        CompanyService $companyService,
        EventDispatcherInterface $eventDispatcher
    ) {
        parent::__construct();

        $this->mangopay = $mangopay;
        $this->companyService = $companyService;
        $this->eventDispatcher = $eventDispatcher;
    }

    protected function configure(): void
    {
        $this
            ->setName('mangopay:send-kyc')
            ->setDescription('Send the KYC of all companies except companies with status = new for MangoPay')
            ->addOption(
                'no-pending-only',
                null,
                InputOption::VALUE_NONE,
                'Check the KYC of all companies except company with status = new'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if (false === $this->mangopay->isConfigured()) {
            return 0;
        }

        if ($input->getOption('no-pending-only') === true) {
            $statement = $this->companyService->getAllVendorsExceptNew();
        } else {
            $statement = $this->companyService->getPendingVendors(true);
        }

        $progressBar = new ProgressBar($output, $statement->rowCount());
        $progressBar->start();

        // Création du compte + envoi des KYCs chez le PSP (LEGAL_DOCUMENTS_UPDATED)
        $companiesErrors = [];
        while ($company = $statement->fetch()) {
            try {
                if ($this->mangopay->isCompanyActivated((int) $company[static::COMPANY_ID_KEY])) {
                    continue;
                }

                $this->eventDispatcher->dispatch(
                    new CompanyLegalDocumentsUpdated((int) $company[static::COMPANY_ID_KEY], []),
                    CompanyEvents::LEGAL_DOCUMENTS_UPDATED
                );

                $progressBar->advance();
            } catch (\Exception $exception) {
                $companiesErrors[$company[static::COMPANY_ID_KEY]] = $exception->getMessage();
            }
        }

        $progressBar->finish();

        if (\count($companiesErrors) > 0) {
            foreach ($companiesErrors as $companyId => $message) {
                $output->writeln("<error>MangoPay error send KYC #{$companyId}: {$message}</error>");
            }

            return 1;
        }

        return 0;
    }
}
