<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Driver\Connection;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wizacha\Premoderation\ProductModeration\ProductModerationInProgressService;

class ResetProgressForProductInModeration extends Command
{
    protected static $defaultName = 'products:moderation:reset-progress';

    /** @var ProductModerationInProgressService */
    protected $productModerationInProgressService;

    /** @var Connection */
    private $connection;

    public function __construct(
        ProductModerationInProgressService $moderationRepository,
        Connection $connection
    ) {
        parent::__construct();
        $this->productModerationInProgressService = $moderationRepository;
        $this->connection = $connection;
    }

    protected function configure(): void
    {
        $this
            ->setDescription('Resets all the products in the status "Moderation in progress"')
            ->addOption('dry-run', null, InputOption::VALUE_NONE, 'Dry mode won\'t alter the DB.');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $dryMode = $input->getOption('dry-run') !== false;

        $io->title('Resetting products in moderation status...');

        $productWithModeration = $this->productModerationInProgressService->getAllProductsInProgress();
        $numberProductWithModeration = \count($productWithModeration);

        if ($numberProductWithModeration === 0) {
            $io->success('There is currently no product with the status "Moderation in progress"');

            return 0;
        }

        if ($dryMode === false) {
            $this->productModerationInProgressService->removeAll();
        }

        $io->success(sprintf('%d product(s) removed successfully from the status "Moderation in progress" (in the moderation table). Falling back to "Waiting moderation" status (no data in the moderation table)', $numberProductWithModeration));

        return 0;
    }
}
