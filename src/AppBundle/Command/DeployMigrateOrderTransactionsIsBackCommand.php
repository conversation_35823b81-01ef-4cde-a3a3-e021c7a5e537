<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\AppBundle\Command\Traits\DeployPostActionsOnce;
use Wizacha\Marketplace\Payment\PaymentProcessorIdentifier;
use Wizacha\Marketplace\Payment\PaymentProcessorName;
use Wizacha\Marketplace\Transaction\Transaction;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionStatus;
use Wizacha\Marketplace\Transaction\TransactionType;
use Wizacha\Money\Money;

/**
 * Class DeployMigrateOrderTransactionsIsBackCommand
 *
 * The previous migration command has multiple issues, because it relies on cscart_order_data payments which:
 *   - are not filled with Hipay
 *   - are linked to the parent order while new transactions should be linked to the child orders
 * There was also a bug on Stripe SEPA capture.
 *
 * So this migration performs the following actions:
 *   1. Create transactions for Hipay (child) orders
 *   2. Create transactions for child orders which don't have any transactions yet
 *   3. Fix Stripe pending transaction
 *   4. WIRE Stripe transactions are actually DIRECT DEBIT
 */
class DeployMigrateOrderTransactionsIsBackCommand extends Command implements DeployPostActionsInterface
{
    use DeployPostActionsOnce;

    protected const VERSION_DATE = '2019-11-22 11:41:00';
    protected const VERSION_NAME = 'MigrateOrderTransactionsIsBack';

    protected const BATCH_SIZE = 250;

    /** @var EntityManagerInterface */
    protected $entityManager;

    /** @var TransactionService */
    protected $transactionService;

    /** @var \DateTimeImmutable */
    protected $processOrdersUntil;

    public function __construct(EntityManagerInterface $entityManager, TransactionService $transactionService)
    {
        parent::__construct();

        $this->entityManager = $entityManager;
        $this->processOrdersUntil = new \DateTimeImmutable('2019-11-12 13:43:00', new \DateTimeZone('Europe/Paris'));
        $this->transactionService = $transactionService;
    }

    public function getPriority(): int
    {
        return 102;
    }


    public function setProcessOrdersUntil(\DateTimeImmutable $processOrdersUntil): self
    {
        $this->processOrdersUntil = $processOrdersUntil;

        return $this;
    }

    protected function configure(): void
    {
        $this
            ->setName('deploy:command:migrate-transaction-fix')
            ->setDescription('Fix new transaction data migration.')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if ($this->hasRun() === true) {
            return 0;
        }

        $this->executeCreateOrderTransactions($input, $output);
        $this->executeFixStripePendingTransactions($input, $output);

        $this->saveExecution();

        return 0;
    }

    protected function executeCreateOrderTransactions(InputInterface $input, OutputInterface $output): void
    {
        $mapping = $this->getProcessorMapping();

        $sql = '
            SELECT
                co.order_id,
                co.total,
                cp.processor_id,
                co.is_paid,
                co.is_capture,
                cod.data AS orderData,
                codp.data AS parentData
            FROM cscart_orders co
            INNER JOIN cscart_payments cp on co.payment_id = cp.payment_id
            INNER JOIN cscart_payment_processors cpp on cp.processor_id = cpp.processor_id
            LEFT JOIN cscart_order_data cod on cod.order_id = co.order_id and cod.type = :type
            LEFT JOIN cscart_order_data codp
                on codp.order_id = co.parent_order_id and codp.type = :type and co.parent_order_id <> 0
            LEFT JOIN doctrine_order_transactions dot on co.order_id = dot.order_id
            WHERE co.is_parent_order = 0
            AND co.timestamp < :until
            AND dot.order_id IS NULL
            GROUP BY co.order_id
            ORDER BY co.order_id
            LIMIT :offset, :limit
        ';

        $statement = $this->getConnection()->prepare($sql);
        $batch = 1;
        $offset = 0;
        $limit = static::BATCH_SIZE;

        do {
            $statement->bindValue(':type', 'P');
            $statement->bindValue(':until', $this->processOrdersUntil->getTimestamp());
            $statement->bindValue(':offset', $offset, \PDO::PARAM_INT);
            $statement->bindValue(':limit', $limit, \PDO::PARAM_INT);
            $statement->execute();

            $data = $statement->fetchAll(\PDO::FETCH_ASSOC);

            if ($data === false) {
                $output->writeln('<error>An error occurred while executing batch #' . $batch . '</error>');
                break;
            }

            $count = \count($data);
            $progressBar = new ProgressBar($output, $count);

            $output->writeln('<info>Starting migration of ' . $count . ' transactions (batch #' . $batch . ')</info>');

            if (false === $output->isVerbose()) {
                $progressBar->start();
            }

            foreach ($data as $entry) {
                $information = null;
                $hipayIds = [PaymentProcessorIdentifier::HIPAY_CAPTURE(), PaymentProcessorIdentifier::HIPAY_CARD()];
                if ($entry['orderData'] !== null && \strlen($entry['orderData']) > 0) {
                    $information = $this->extractProcessorInformation($entry['orderData']);
                } elseif ($entry['parentData'] !== null && \strlen($entry['parentData']) > 0) {
                    $information = $this->extractProcessorInformation($entry['parentData']);
                } elseif (\in_array(new PaymentProcessorIdentifier((int) $entry['processor_id']), $hipayIds) === false) {
                    // Non HiPay order should have payment data, otherwise we skip them.
                    continue;
                }

                $status = $entry['is_paid'] ? TransactionStatus::SUCCESS() : TransactionStatus::FAILED();
                $transaction = new Transaction(
                    (int) $entry['order_id'],
                    $mapping[$entry['processor_id']][1],
                    $status,
                    Money::fromVariable($entry['total'])
                );

                $transaction
                    ->setProcessorName($mapping[$entry['processor_id']][0]->getValue())
                    ->setProcessorInformations($information)
                    ->updateTimestamp();
                $this->entityManager->persist($transaction);

                if (false === $output->isVerbose()) {
                    $progressBar->advance();
                } else {
                    $output->writeln('<info>Created transaction for Order ID #' . $entry['order_id'] . '</info>');
                }
            }

            if (false === $output->isVerbose()) {
                $progressBar->finish();
            } else {
                $output->writeln('Saving transactions in the database');
            }

            $this->entityManager->flush();
            $this->entityManager->clear();

            $offset += static::BATCH_SIZE;
            $limit += static::BATCH_SIZE;
            $batch++;
        } while ($count > 0);
    }

    protected function executeFixStripePendingTransactions(InputInterface $input, OutputInterface $output): void
    {
        $transactions = $this->transactionService->findBy([
            'processorName' => PaymentProcessorName::STRIPE()->getValue(),
        ]);

        $output->writeln('<info>' . \count($transactions) . ' transactions to process</info>');

        foreach ($transactions as $transaction) {
            if ($transaction->getStatus()->equals(TransactionStatus::PENDING())
                && (bool) $transaction->getInformation('stripe_charge_captured') === true
            ) {
                $transaction->setStatus(TransactionStatus::SUCCESS());
            }

            if ($transaction->getType()->equals(TransactionType::BANK_WIRE())) {
                $transaction->setType(TransactionType::DIRECT_DEBIT());
            }
        }

        $this->entityManager->flush();
    }

    protected function getConnection(): Connection
    {
        return $this->entityManager->getConnection();
    }

    /** @return array [Processor ID => [Processor name, Transaction type]] */
    protected function getProcessorMapping(): array
    {
        return [
            PaymentProcessorIdentifier::MANGOPAY_CARD()->getValue() => [
                PaymentProcessorName::MANGOPAY(), TransactionType::CREDITCARD(),
            ],
            PaymentProcessorIdentifier::LEMONWAY_CARD()->getValue() => [
                PaymentProcessorName::LEMONWAY(), TransactionType::CREDITCARD(),
            ],
            PaymentProcessorIdentifier::LEMONWAY_BANKWIRE()->getValue() => [
                PaymentProcessorName::LEMONWAY(), TransactionType::BANK_WIRE(),
            ],
            PaymentProcessorIdentifier::MANGOPAY_BANKWIRE()->getValue() => [
                PaymentProcessorName::MANGOPAY(), TransactionType::BANK_WIRE(),
            ],
            PaymentProcessorIdentifier::HIPAY_CARD()->getValue() => [
                PaymentProcessorName::HIPAY(), TransactionType::CREDITCARD(),
            ],
            PaymentProcessorIdentifier::SMONEY_CARD()->getValue() => [
                PaymentProcessorName::SMONEY(), TransactionType::CREDITCARD(),
            ],
            PaymentProcessorIdentifier::STRIPE_CARD()->getValue() => [
                PaymentProcessorName::STRIPE(), TransactionType::CREDITCARD(),
            ],
            PaymentProcessorIdentifier::STRIPE_SEPA_DIRECT()->getValue() => [
                PaymentProcessorName::STRIPE(), TransactionType::BANK_WIRE(),
            ],
            PaymentProcessorIdentifier::STRIPE_SEPA_DEFERMENT()->getValue() => [
                PaymentProcessorName::STRIPE(), TransactionType::BANK_WIRE(),
            ],
            PaymentProcessorIdentifier::NO_PAYMENT()->getValue() => [
                PaymentProcessorName::NONE(), TransactionType::CREDITCARD(),
            ],
            PaymentProcessorIdentifier::HIPAY_CAPTURE()->getValue() => [
                PaymentProcessorName::HIPAY(), TransactionType::CREDITCARD(),
            ],
            PaymentProcessorIdentifier::OFFLINE()->getValue() => [
                PaymentProcessorName::NONE(), TransactionType::CREDITCARD(),
            ],
        ];
    }

    protected function extractProcessorInformation(string $input): ?array
    {
        $out = \unserialize(fn_decrypt_text($input));
        if ($out === false) {
            // Could not decrypt or deserialize input
            return null;
        }

        return $out;
    }

    protected function getDateTime(): \DateTimeInterface
    {
        return new \DateTimeImmutable(static::VERSION_DATE);
    }

    protected function getVersionName(): string
    {
        return static::VERSION_NAME;
    }
}
