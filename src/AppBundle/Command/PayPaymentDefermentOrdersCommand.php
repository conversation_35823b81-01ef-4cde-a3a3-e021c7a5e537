<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Marketplace\Order\Action\Cancel;
use Wizacha\Marketplace\Order\Action\RedirectToPaymentProcessor;
use Wizacha\Marketplace\Order\OrderService;
use Wizacha\Marketplace\Order\Tracer\Tracer;
use Wizacha\Marketplace\Payment\Processor\AbstractProcessor;
use Wizacha\Marketplace\Transaction\Transaction;
use Wizacha\Marketplace\Transaction\TransactionService;
use Wizacha\Marketplace\Transaction\TransactionStatus;
use Wizacha\Marketplace\Transaction\TransactionType;
use Wizacha\Money\Money;
use Wizacha\Order;

/**
 * Note : Tag used in service for $processors is actually marketplace.payment_processor_service_deferment_payment
 * When all PSP will manage deferred SEPA payment we can replace this tag by marketplace.payment_processor_service
 */
class PayPaymentDefermentOrdersCommand extends Command
{
    /** @var OrderService */
    private $orderService;

    /** @var Cancel */
    private $cancelAction;

    /** @var LoggerInterface */
    private $logger;

    /** @var iterable|AbstractProcessor[] */
    private $processors;

    /** @var RedirectToPaymentProcessor */
    private $redirectToPaymentProcessor;

    /** @var Tracer */
    private $orderDebugTracer;

    /** @var TransactionService */
    private $transactionService;

    public function __construct(
        OrderService $orderService,
        Cancel $cancelAction,
        LoggerInterface $logger,
        RedirectToPaymentProcessor $redirectToPaymentProcessor,
        iterable $processors,
        Tracer $orderDebugTracer,
        TransactionService $transactionService
    ) {
        parent::__construct();

        $this->orderService = $orderService;
        $this->cancelAction = $cancelAction;
        $this->logger = $logger;
        $this->redirectToPaymentProcessor = $redirectToPaymentProcessor;
        $this->processors = $processors;
        $this->orderDebugTracer = $orderDebugTracer;
        $this->transactionService = $transactionService;
    }

    protected function configure(): void
    {
        $this
            ->setName('orders:pay-payment-deferment-orders')
            ->setDescription('Pay payment deferment orders, if it\'s the time.')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): void
    {
        foreach ($this->processors as $processor) {
            if (false === $processor->isConfigured()) {
                continue;
            }
            $output->writeln("Checking orders for " . $processor->getName());

            // Get deferred payment orders
            $ordersToBePaid = $this->orderService->getPaymentDefermentOrdersToBePaid($processor->getName());
            $orderCount = $count = 0;

            foreach ($ordersToBePaid as $order) {
                ++$orderCount;
                try {
                    $processor->createSepaTransaction($order->getId());
                    $count++;
                    if (true === $this->redirectToPaymentProcessor->isAllowed($order)) {
                        $this->redirectToPaymentProcessor->execute($order);
                    }
                } catch (\Exception $exception) {
                    $orderId = $order->getId();
                    $this->logger->error(
                        'Exception in payment deferment cron: ' . $exception->getMessage(),
                        [
                            'exception' => $exception,
                            'orderId' => $orderId,
                        ]
                    );
                    $output->writeln("<error>#$orderId $exception</error>");

                    $paymentInfo = (new Order($orderId))->getPaymentInformation();
                    $paymentInfo['deferred_payment_failures'] = ($paymentInfo['deferred_payment_failures'] ?? 0) + 1;

                    $output->writeln(
                        sprintf(
                            '<comment>Payment of order %s has failed. Refused.</comment>',
                            $orderId
                        )
                    );
                    $this->orderDebugTracer->trace($order->getId(), 'MarkPaymentAsRefused');
                    //create offline transaction with status Failed
                    $transaction = new Transaction(
                        $orderId = $order->getId(),
                        $type = TransactionType::OFFLINE(),
                        $status = TransactionStatus::FAILED(),
                        $amount = new Money($order->getTotal()->getAmount())
                    );
                    $transaction->setTransactionReference(uniqid('offline_'));
                    $this->transactionService->save($transaction);
                }
            }
            $output->writeln(
                sprintf(
                    "<info>%s commande(s) traitée(s), dont %s ont lancé le paiement à échéance.</info>",
                    $orderCount,
                    $count
                )
            );
        }
    }
}
