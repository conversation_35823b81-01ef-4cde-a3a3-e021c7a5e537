<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Aws\S3\S3Client;
use Cocur\Slugify\Slugify;
use GuzzleHttp\Psr7\Uri;
use Ifsnop\Mysqldump\Mysqldump;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class HadesBackupCommand extends Command
{
    protected static $defaultName = 'hades:backup';

    private string $platformTechnicalName;
    private S3Client $s3Client;
    private string $bucket;

    private Slugify $slugify;

    public function __construct(
        string $platformTechnicalName,
        S3Client $s3Client,
        string $bucket
    ) {
        parent::__construct();

        $this->platformTechnicalName = $platformTechnicalName;
        $this->s3Client = $s3Client;
        $this->bucket = $bucket;

        $this->slugify = new Slugify();
    }

    protected function configure()
    {
        $this
            ->setDescription(
                'Backup databases to storage'
            )
            ->setHelp(
                'sql dump working with janus:import command'
            )
        ;
    }

    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ): int {
        $io = new SymfonyStyle($input, $output);

        $io->title('Hades "last chance" backup');

        $this->process($io, 'marketplace');
        $this->process($io, 'discuss', 'discuss.');

        return 0;
    }

    private function process(
        SymfonyStyle $io,
        string $database,
        string $prefix = ''
    ): void {
        $io->section($database);

        $mysqldump = $this->getMysqldump($prefix);
        $mysqldump->setInfoHook(
            fn ($object, $info) => $io->writeln(
                implode(
                    ', ',
                    \array_map(
                        fn ($key, $value) => "$object $key: $value",
                        \array_keys($info),
                        \array_values($info)
                    )
                )
            )
        );

        $tmpFile = \tmpfile();

        $tmpPath = \stream_get_meta_data($tmpFile)['uri'];
        $mysqldump->start($tmpPath);

        $result = $this->uploadFile(
            $tmpFile,
            $this->getOutputPath($database)
        );

        \fclose($tmpFile);

        $io->success($result);
    }

    private function getMysqldump(string $prefix = ''): Mysqldump
    {
        $dumper = new Mysqldump(
            \sprintf(
                'mysql:host=%s;dbname=%s',
                container()->getParameter("{$prefix}database_host"),
                (
                    $prefix
                    ? container()->getParameter("{$prefix}database_dbname") # :|
                    : container()->getParameter("database_name")
                )
            ),
            container()->getParameter("{$prefix}database_user"),
            container()->getParameter("{$prefix}database_password"),
            [
                'compress' => Mysqldump::GZIP,
                'add-drop-table' => true,
            ]
        );

        return $dumper;
    }

    private function uploadFile(
        $tmpFile,
        string $outputPath
    ): Uri {
        $result = $this->s3Client->upload(
            $this->bucket,
            $outputPath,
            $tmpFile,
            'private'
        );

        return new Uri(
            $result->get('@metadata')['effectiveUri']
        );
    }

    private function getOutputPath(
        string $databaseName
    ): string {
        $now = new \DateTimeImmutable(
            'now',
            new \DateTimeZone('UTC')
        );

        $datetime = $now->format('Y-m-d\TH_i_sT');
        $platform = $this->slugify->slugify($this->platformTechnicalName);

        return "$platform/$platform-$databaseName-$datetime.sql.gz";
    }
}
