<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\DependencyInjection\ContainerAwareInterface;
use Symfony\Component\DependencyInjection\ContainerAwareTrait;
use Symfony\Component\Filesystem\Filesystem;

class AdminInstallCommand extends Command implements ContainerAwareInterface
{
    use ContainerAwareTrait;

    protected function configure()
    {
        $this
            ->setName('admin:install')
            ->setDescription('Install admin page')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $fs = new Filesystem();
        $path = $this->container->get('kernel')->getProjectDir();

        if (!$fs->exists($path . '/' . $this->container->getParameter('entrypoint.administrator'))
            && $fs->exists($path . '/admin.php')
        ) {
            $fs->rename($path . '/admin.php', $path . '/' . $this->container->getParameter('entrypoint.administrator'));
        }

        if (!$fs->exists($path . '/' . $this->container->getParameter('entrypoint.vendor'))
            && $fs->exists($path . '/vendor.php')
        ) {
            $fs->rename($path . '/vendor.php', $path . '/' . $this->container->getParameter('entrypoint.vendor'));
        }

        $output->writeln('<info>Admin page successfully installed!</info>');
    }
}
