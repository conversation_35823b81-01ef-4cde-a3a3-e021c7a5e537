<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Helper\TableSeparator;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Wizacha\Exim\Import\ImportReportMessage\AttributeMessage;
use Wizacha\Exim\Import\ImportReportMessage\AttributeVariantMessage;
use Wizacha\Exim\Import\ImportReportMessage\CategoryMessage;
use Wizacha\Exim\Import\ImportReportMessage\DivisionMessage;
use Wizacha\Exim\Import\ImportReportMessage\EntitiesMessage;
use Wizacha\Exim\Import\ImportReportMessage\EximMessage;
use Wizacha\Exim\Import\ImportReportMessage\MultiVendorProductMessage;
use Wizacha\Exim\Import\ImportReportMessage\ProductMessage;
use Wizacha\Exim\Import\ImportReportMessage\ProductPriceAndStockMessage;

class ListEximImportMessages extends Command
{
    protected static $defaultName = 'debug:import-messages';

    protected function configure()
    {
        parent::configure();

        $this
            ->addArgument(
                'langCode',
                InputArgument::REQUIRED,
                'Language code to retrieve translations.'
            )
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $messagesEnums = \array_merge(
            ['AttributeMessage' => AttributeMessage::values()],
            ['AttributeVariantMessage' => AttributeVariantMessage::values()],
            ['CategoryMessage' => CategoryMessage::values()],
            ['DivisionMessage' => DivisionMessage::values()],
            ['EntitiesMessage' =>  EntitiesMessage::values()],
            ['EximMessage' =>  EximMessage::values()],
            ['MultiVendorProductMessage' => MultiVendorProductMessage::values()],
            ['ProductMessage' => ProductMessage::values()],
            ['ProductPriceAndStockMessage' => ProductPriceAndStockMessage::values()],
        );
        $table = (new Table($output))->setHeaders([
            'ImportReportMessage Class',
            'Translation Key',
            'Translation Value',
        ]);
        $rows = [];

        foreach ($messagesEnums as $enumName => $enumValues) {
            foreach ($enumValues as $enumValue) {
                $rows[] = [
                    $enumName,
                    $enumValue->getValue(),
                    __(
                        $enumValue,
                        [],
                        \strtolower($input->getArgument('langCode'))
                    )
                ];
            }

            if ($enumName !== \array_key_last($messagesEnums)) {
                $rows[] = new TableSeparator();
            }
        }

        $table->setRows($rows);
        $table->render();
    }
}
