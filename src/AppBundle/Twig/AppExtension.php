<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Twig;

use Symfony\Component\Asset\Packages;
use Symfony\Component\DependencyInjection\Container;
use Symfony\Component\HttpFoundation\Session\Session;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Twig\Extension\AbstractExtension;
use Twig\Extension\GlobalsInterface;
use Twig\TwigFilter;
use Twig\TwigFunction;
use Tygh\SmartyEngine\Core;
use Wizacha\AppBundle\Service\ConfigurationService;
use Wizacha\Category;
use Wizacha\Cscart\Common;
use Wizacha\Marketplace\AdminCompany;
use Wizacha\Marketplace\Catalog\Product\ProductSummary;
use Wizacha\Marketplace\Cms\Menu;
use Wizacha\Marketplace\Image\Image;
use Wizacha\Marketplace\Price\PriceFormatter;
use Wizacha\Marketplace\ReadModel\Basket;
use Wizacha\Marketplace\Shipping\DeliveryType;
use Wizacha\Money\Money;
use Wizacha\Registry;
use Wizacha\User;

use function Wizacha\Marketplace\Order\is_order_status_equal_to;

class AppExtension extends AbstractExtension implements GlobalsInterface
{
    /**
     * @var PriceFormatter
     */
    private $priceFormatter;

    /**
     * @var PriceFormatter
     */
    private $backendPriceFormatter;

    /**
     * @var Packages
     */
    private $assetPackages;

    /**
     * @var Session
     */
    private $session;

    /**
     * @var Container
     */
    private $container;

    private ConfigurationService $configurationService;

    public function __construct(
        PriceFormatter $priceFormatter,
        PriceFormatter $backendPriceFormatter,
        Packages $assetPackages,
        Session $session,
        Container $container,
        ConfigurationService $configurationService
    ) {
        $this->priceFormatter = $priceFormatter;
        $this->backendPriceFormatter = $priceFormatter;
        $this->assetPackages = $assetPackages;
        $this->session = $session;
        $this->container = $container;
        $this->configurationService = $configurationService;
    }

    public function getGlobals()
    {
        $registry = Registry::defaultInstance();
        return [
            'basicConfig' => $this->configurationService->readConfiguration(),
            'cscartConfig' => $registry->get(['config']),
            // @deprecated: to replace with Symfony's native `app.user` variable
            'currentUser' => $this->getCurrentUser(),
            'global' => [
                'uploadMaxFileSize' => min(fn_return_bytes(ini_get('upload_max_filesize')), fn_return_bytes(ini_get('post_max_size'))) / 1024 / 1024, // En Mo
            ],
        ];
    }

    public function getFunctions()
    {
        return [
            new TwigFunction('includeStyle', [$this, 'includeStyle'], ['is_safe' => ['html']]),
            new TwigFunction('includeScripts', [$this, 'includeScripts'], ['is_safe' => ['html']]),
            new TwigFunction('cscart_url', 'fn_url'),
            new TwigFunction('cscart_url_admin', function ($url, $useExternalBoUrl = false) {
                $externalBoUrl = $this->container->getParameter('external_backoffice_url');
                if ($useExternalBoUrl && \is_string(filter_var($externalBoUrl, FILTER_VALIDATE_URL))) {
                    return $externalBoUrl;
                }

                return fn_url($url, 'A', 'https', null, true);
            }),
            new TwigFunction('cscart_url_vendor', function ($url, $useExternalBoUrl = false) {
                $externalBoUrl = $this->container->getParameter('external_backoffice_url');
                if ($useExternalBoUrl && \is_string(filter_var($externalBoUrl, FILTER_VALIDATE_URL))) {
                    return $externalBoUrl;
                }

                return fn_url($url, 'V', 'https', null, true);
            }),
            new TwigFunction('render_smarty', [$this, 'renderSmarty'], ['is_safe' => ['html']]),
            new TwigFunction('render_smarty_legacy', [$this, 'renderSmartyLegacy'], ['is_safe' => ['html']]),
            new TwigFunction('render_smarty_legacy_mail', [$this, 'renderSmartyLegacyMail'], ['is_safe' => ['html']]),
            new TwigFunction('render_mustache', [$this, 'renderMustache'], ['is_safe' => ['html']]),
            new TwigFunction('repeat', [$this, 'repeatArray']),
            new TwigFunction('captcha', [$this, 'renderCaptcha'], ['is_safe' => ['html']]),
            new TwigFunction('image_url_by_id', [$this, 'imageUrlById']),
            new TwigFunction('category_tree', [$this, 'getCategoryTree']),
            new TwigFunction('getRootCategories', [$this, 'getRootCategories']),
            new TwigFunction('getMenus', [$this, 'getMenus']),
            new TwigFunction('getBasket', [$this, 'getUserBasket']),
            new TwigFunction('routeCreateProduct', [$this, 'getRouteCreateProduct']),
            new TwigFunction('feature', [$this, 'getFeatureFlag']),
            new TwigFunction('getDeclinationOptions', [$this, 'getDeclinationOptions']),
            new TwigFunction('getAdminCompany', [$this, 'getAdminCompany']),
            new TwigFunction('assetMail', [$this, 'assetMail']),
            new TwigFunction('getNotifications', 'fn_get_notifications'),
            new TwigFunction('getCountries', 'fn_get_simple_countries'),
            new TwigFunction('getChronoRelaisDeliveryConstant', [$this, 'getChronoRelaisDeliveryConstant']),
        ];
    }

    public function getFilters()
    {
        return [
            new TwigFilter('price', [$this, 'formatPrice'], ['is_safe' => ['html']]),
            new TwigFilter('money', [$this, 'formatMoney'], ['is_safe' => ['html']]),
            new TwigFilter('sort_shippings', [$this, 'sortShippings']),
            new TwigFilter('image_url', [$this, 'imageUrl']),
            new TwigFilter('product_url', [$this, 'productUrl']),
            new TwigFilter('cscart_state_name', 'fn_get_state_name'),
            new TwigFilter('cscart_country_name', 'fn_get_country_name'),
            new TwigFilter('is_order_status_equal_to', [$this, 'isOrderStatusEqualTo']),
            new TwigFilter('is_order_refused_by_vendor', [$this, 'isOrderRefusedByVendor']),
            new TwigFilter('convertAmount', [$this, 'convertAmount']),
        ];
    }

    public function renderSmarty($path, $params = [])
    {
        /** @var \Smarty $smarty */
        $smarty = $this->container->get('templating.smarty');
        if (!empty($params)) {
            $smarty->assign($params);
        }
        return $smarty->fetch($path);
    }

    public function renderSmartyLegacy($path, $params = [])
    {
        /** @var Core $smarty */
        $smarty = \Tygh\Registry::get('view');
        if (!empty($params)) {
            $smarty->assign($params);
        }
        return $smarty->fetch($path);
    }

    public function renderSmartyLegacyMail($path, $params = [], $area = null)
    {
        // Workaround to PHP segfault, at this moment we cannot put a constant in default value if it is not defined
        if ($area === null) {
            $area = AREA;
        }

        /** @var Core $smarty */
        $smarty = \Tygh\Registry::get('view');
        $smarty->setArea($area, 'mail'); // fake area for mails
        if (!empty($params)) {
            $smarty->assign($params);
        }
        $output = $smarty->fetch($path);
        $smarty->setArea(AREA); // restore original area

        return $output;
    }

    public function renderMustache($path, $params = [])
    {
        $mustache = $this->container->get('templating.mustache');
        /** @var \Smarty $smarty */
        $smarty = $this->container->get('templating.smarty');
        return $mustache->render($smarty->fetch($path), $params);
    }

    public function formatPrice($price)
    {
        return AREA === 'C' ?
            $this->priceFormatter->formatFloat(\floatval($price)) :
            $this->backendPriceFormatter->formatFloat(\floatval($price));
    }

    public function sortShippings($shippings)
    {
        uasort(
            $shippings,
            function ($shipping1, $shipping2) {
                return $shipping1['first_rate'] - $shipping2['first_rate'];
            }
        );
        return $shippings;
    }

    public function formatMoney(Money $money)
    {
        return $this->formatPrice($money->getConvertedAmount());
    }

    public function convertAmount(Money $money): float
    {
        return $money->getConvertedAmount();
    }

    public function renderCaptcha()
    {
        // Avoids 2 catchas sharing the same ID
        static $id = 0;
        $id++;
        return "<div id='recaptcha-container-$id' class='wizacha-recaptcha'></div>";
    }

    /**
     * Returns the image URL for the given Image object.
     *
     * If the image is null, the default image will be used.
     *
     * If width is given and height is null, the image will be resized to the given width, keeping its ratio
     * If height is given and width is null, the image will be resized to the given height, keeping its ratio
     * If width and height are given, the ratio is kept and a white background is added
     */
    public function imageUrl(Image $image = null, int $width = null, int $height = null): string
    {
        if (!$image) {
            return $this->assetPackages->getUrl('images/no-image.jpg');
        }

        $imageUrl = $this->container->get('router')->generate('api_image_get', [
            'id' => $image->getId(),
            'w' => $width,
            'h' => $height,
        ]);

        return Common::normalizeApiUriString($imageUrl);
    }

    /**
     * Returns the image URL by its ID.
     * The image is square
     * @deprecated Use the `image_url` filter when possible instead.
     */
    public function imageUrlById(int $imageId = null, int $size = null): string
    {
        if ($imageId === null || $imageId === 0) {
            return $this->assetPackages->getUrl('images/no-image.jpg');
        }

        return $this->imageUrl(new Image($imageId), $size, $size);
    }

    /**
     * Returns the categories as a tree, to build menus.
     * @return array
     */
    public function getCategoryTree()
    {
        return Category::getTree();
    }

    /**
     * Returns only the top level categories. This includes category images.
     * @return Category[]
     */
    public function getRootCategories(): array
    {
        return $this->container->get('marketplace.catalog.category_service')->getRootCategories();
    }

    /**
     * Returns the logged in user.
     */
    public function getCurrentUser(): ?User
    {
        $userId = $this->session->get('auth')['user_id'];
        return $userId ? new User($userId) : null;
    }

    /**
     * @return Menu[]
     */
    public function getMenus()
    {
        return $this->container->get('marketplace.cms.menu_service')->getActiveMenus();
    }

    /**
     * @param bool $useNewModel
     * @return Basket|\Wizacha\Marketplace\Basket\ReadModel\Basket|null
     */
    public function getUserBasket(bool $useNewModel = false)
    {
        return $this->container->get('marketplace.user_service')->getUserBasket($useNewModel);
    }

    /**
     * @param int $productId, int $declinationId
     * @return array
     */
    public function getDeclinationOptions(int $productId, string $declinationId)
    {
        return $this->container->get('marketplace.product.productservice')->getDeclinationOptions($productId, $declinationId);
    }

    public function getRouteCreateProduct(): string
    {
        // C2C: product creation form
        if ($this->container->getParameter('feature.enable_c2c')) {
            $router = $this->container->get('router');
            return $router->generate('product_new', [], UrlGeneratorInterface::ABSOLUTE_URL);
        }

        // B2C: if the user is logged in, we link to the backoffice
        $userId = $this->container->get('session')->get('auth')['user_id'];
        if ($userId) {
            $user = new User($userId);
            if ($user->isProfessionalVendor()) {
                return \Tygh\Registry::get('config.vendor_domain') . '/' . \Tygh\Registry::get('config.vendor_index');
            }
        }

        // B2C registration
        return $this->container->get('router')->generate('company_new');
    }

    public function productUrl(ProductSummary $product, string $declinationId = null): string
    {
        if (empty($product->getSlug())) {
            return '#';
        }
        return $this->container->get('router')->generate('product', [
            'categoryPath' => $product->getCategorySlugPath(),
            'productSlug' => $product->getSlug(),
            'd' => $declinationId,
        ]);
    }

    public function getFeatureFlag(string $featureName)
    {
        if (!$this->container->hasParameter('feature.' . $featureName)) {
            return false;
        }
        return $this->container->getParameter('feature.' . $featureName);
    }

    public function getAdminCompany(): AdminCompany
    {
        return $this->container->get('marketplace.admin_company');
    }

    public function getChronoRelaisDeliveryConstant(): string
    {
        return (string) DeliveryType::CHRONO_RELAIS();
    }

    public function assetMail(string $asset): string
    {
        return '[imageToEmbed]' . $this->assetPackages->getUrl($asset) . '[/imageToEmbed]';
    }

    public function includeStyle(): string
    {
        $mainCssPath = $this->container->get('marketplace.asset_manager')->getAssetUrl('css/main.css');

        return sprintf('<link rel="stylesheet" href="%s" media="screen" />', $mainCssPath);
    }

    public function includeScripts(): string
    {
        $storage = container()->get("Wizacha\Storage\AssetsStorageService");

        $files = array_filter(
            $storage->getList(),
            function ($file) {
                return (new \SplFileInfo($file))->getExtension() === 'js';
            }
        );

        $html = '';
        foreach ($files as $file) {
            $url = $storage->getUrl($file, 'short');
            $html .= sprintf('<script type="text/javascript" src="%s"></script>', $url);
        }

        $html .= $this->container->get('app.captcha')->getJS();

        return $html;
    }

    public function isOrderStatusEqualTo(int $orderId, string ...$statuses): bool
    {
        return is_order_status_equal_to($orderId, ...$statuses);
    }

    public function isOrderRefusedByVendor(int $orderId): bool
    {
        return $this->container->get('marketplace.order.order_service')->isRefusedByVendor($orderId);
    }
}
