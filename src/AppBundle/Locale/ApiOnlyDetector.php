<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Locale;

use Wizacha\Component\Locale\LocaleDetectorInterface;
use Wizacha\Component\Locale\Locale;
use Wizacha\Component\Locale\Exception\LocaleNotFound;
use Symfony\Component\HttpFoundation\Request;

class ApiOnlyDetector implements LocaleDetectorInterface
{
    private $detector;

    public function __construct(LocaleDetectorInterface $detector)
    {
        $this->detector = $detector;
    }

    /**
     * {@inheritdoc}
     */
    public function detect(Request $request): Locale
    {
        if (substr($request->getPathInfo(), 0, 5) !== '/api/') {
            throw new LocaleNotFound();
        }

        return $this->detector->detect($request);
    }
}
