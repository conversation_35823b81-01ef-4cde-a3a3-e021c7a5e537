<?php

declare(strict_types=1);

namespace Wizacha\AppBundle\Locale;

use Symfony\Component\HttpFoundation\Request;
use Wizacha\Component\Locale\Exception\ExceptionInterface;
use Wizacha\Component\Locale\Locale;
use Wizacha\Component\Locale\LocaleDetectorInterface;
use Wizacha\Marketplace\GlobalState\GlobalState;
use Wizacha\Marketplace\User\UserService;

class Detector
{
    private $interfaceDetector;
    private $contentDetector;
    private $default;

    /** @var UserService */
    private $userService;

    public function __construct(
        LocaleDetectorInterface $interfaceDetector,
        LocaleDetectorInterface $contentDetector,
        string $default,
        UserService $userService
    ) {
        $this->interfaceDetector = $interfaceDetector;
        $this->contentDetector = $contentDetector;
        $this->default = new Locale($default);
        $this->userService = $userService;
    }

    public function detect(Request $request): void
    {
        try {
            $interface = $this->interfaceDetector->detect($request);
        } catch (ExceptionInterface $e) {
            $interface = $this->default;
        }

        $request->setDefaultLocale((string) $interface);
        $request->attributes->set('_locale', (string) $interface);

        try {
            $content = $this->contentDetector->detect($request);
        } catch (ExceptionInterface $e) {
            $content = $interface;
        }

        // MacGyver approved
        if (\preg_match('#^(?!\/api).*#', $request->getPathInfo()) > 0
            && ($request->query->has('sl') === true || $request->query->has('descr_sl') === true)
        ) {
            if ((string) $interface !== fn_get_session_data('cart_language' . AREA)) {
                // Save UI language to session
                fn_set_session_data('cart_language' . AREA, (string) $interface, COOKIE_ALIVE_TIME);
                $this->userService->updateUserProfile($_SESSION['auth']['user_id'], ['lang_code' => $interface->__toString()], false, false);
            }

            if ((string) $content !== fn_get_session_data('descr_sl')) {
                // Save content language to session
                fn_set_session_data('descr_sl', (string) $content, COOKIE_ALIVE_TIME);
            }
        }

        GlobalState::switchInterfaceTo($interface);
        GlobalState::switchContentTo($content);
    }
}
