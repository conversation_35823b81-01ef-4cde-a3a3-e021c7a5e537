<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Locale;

use Wizacha\Component\Locale\LocaleDetectorInterface;
use Wizacha\Component\Locale\Locale;
use Wizacha\Component\Locale\Exception\LocaleNotAllowed;
use Wizacha\Component\Locale\Exception\DomainException;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\NotAcceptableHttpException;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;

/**
 * Ce detector permet d'implémenter un version stricte de la gestion des langues
 * au niveau HTTP, c'est à dire que si le client attend une langue en réponse
 * qu'on ne supporte pas on lui retourne une "406 Not Acceptable" ou que la langue
 * du contenu qu'il nous envoie ne l'est pas non plus on lui retourne une
 * "422 Unprocessable Entity"
 */
class ApiDetector implements LocaleDetectorInterface
{
    private $detector;
    private $pathsWhitelist;

    /**
     * @param string $pathsWhitelist Liste des urls pour lesquelles on autorise l'utilisation d'une langue que l'on ne supporte pas
     */
    public function __construct(
        LocaleDetectorInterface $detector,
        string ...$pathsWhitelist
    ) {
        $this->detector = $detector;
        $this->pathsWhitelist = $pathsWhitelist;
    }

    /**
     * {@inheritdoc}
     */
    public function detect(Request $request): Locale
    {
        try {
            return $this->detector->detect($request);
        } catch (LocaleNotAllowed $e) {
            if (\in_array($request->getPathInfo(), $this->pathsWhitelist, true)) {
                return $e->locale();
            }
            if ($request->isMethodSafe(false)) {
                throw $e;
            } else {
                throw new UnprocessableEntityHttpException();
            }
        } catch (DomainException $e) {
            if (!$request->isMethodSafe(false)) {
                throw new UnprocessableEntityHttpException();
            }

            throw $e;
        }
    }
}
