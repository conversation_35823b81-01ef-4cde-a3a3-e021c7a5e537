<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\EventSubscriber;

use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\HttpExceptionInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\KernelEvents;
use Tygh\Registry;
use Wizacha\AppBundle\Backend\Exception\RunControllerException;
use Wizacha\AppBundle\Controller\Api\Error\ApiErrorException;
use Wizacha\AppBundle\Controller\Api\Error\ApiErrorResponseProvider;
use Wizacha\AppBundle\Controller\Api\Exception\MissingFieldsHttpException;
use Wizacha\Bridge\Symfony\Response\BadRequestJsonResponse;
use Wizacha\Bridge\Symfony\Response\InternalServerErrorJsonResponse;
use Wizacha\Marketplace\Exception\Forbidden;
use Wizacha\Marketplace\Exception\MarketplaceExceptionInterface;
use Wizacha\Marketplace\Exception\NotFound;

/**
 * Turn some of our model exceptions into HTTP exceptions.
 */
class ExceptionSubscriber implements EventSubscriberInterface
{
    /** @var LoggerInterface */
    private $logger;

    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    public static function getSubscribedEvents()
    {
        return [
            KernelEvents::EXCEPTION => ['onKernelException', 0],
        ];
    }

    public function onKernelException(ExceptionEvent $event)
    {
        if (!$event->isMasterRequest()) {
            // don't do anything if it's not the master request
            return;
        }

        $exception = $event->getThrowable();
        if ($exception instanceof NotFound) {
            $exception = new NotFoundHttpException($exception->getMessage(), $exception);
            $event->setThrowable($exception);
        } elseif ($exception instanceof Forbidden) {
            $exception = new AccessDeniedHttpException($exception->getMessage(), $exception);
            $event->setThrowable($exception);
        } elseif ($exception instanceof MarketplaceExceptionInterface) {
            $exception = new ApiErrorException($exception);
            $event->setThrowable($exception);
        }

        if (strpos($event->getRequest()->getPathInfo(), '/api/') === 0) {
            $this->handleExceptionInApi($event);
        }

        if ($this->isABackOfficeUrl($event->getRequest()->getBaseUrl())) {
            $this->handleExceptionInBO($event);
        }
    }

    /**
     * Return JSON responses in the API.
     */
    private function handleExceptionInApi(ExceptionEvent $event)
    {
        $exception = $event->getThrowable();
        $previousException = $exception->getPrevious();

        if ($exception instanceof MissingFieldsHttpException) {
            $event->setResponse(BadRequestJsonResponse::missingFields(...$exception->fields));
        } elseif ($exception instanceof ApiErrorResponseProvider) {
            $event->setResponse($exception->toApiErrorResponse());
        } elseif ($previousException instanceof ApiErrorResponseProvider) {
            $event->setResponse($previousException->toApiErrorResponse());
        } elseif ($exception instanceof HttpExceptionInterface) {
            $event->setResponse(new JsonResponse(['message' => $exception->getMessage()], $exception->getStatusCode()));

            if ($exception->getStatusCode() >= 500) {
                $this->logger->error($exception->getMessage(), ['exception' => $exception]);
            }
        } else {
            $event->setResponse(new InternalServerErrorJsonResponse());
            $this->logger->error($exception->getMessage(), ['exception' => $exception]);
        }
    }

    /**
     * Return BO error response Page
     */
    private function handleExceptionInBo(ExceptionEvent $event): self
    {
        $exception = $event->getThrowable();

        // Response Code
        $responseCode = $exception instanceof HttpExceptionInterface
            ? $exception->getStatusCode()
            : Response::HTTP_INTERNAL_SERVER_ERROR;

        // Log exception
        if ($responseCode >= Response::HTTP_INTERNAL_SERVER_ERROR) {
            // RunControllerException is just a wrapper, we need to log the previous exception
            $throwableToLog = $exception instanceof RunControllerException
                ? $exception->getPrevious()
                : $exception
            ;

            $this->logger->error($throwableToLog->getMessage(), ['exception' => $throwableToLog]);
        }

        // Build BO error page
        $view = Registry::get('view');
        $view->assign('exception_status', $responseCode);
        $view->assign('content_tpl', 'exception.tpl');
        $content = Registry::get('view')->fetch('index.tpl');

        // Set Response (and stop event propagation)
        $event->setResponse(new Response($content, $responseCode));

        return $this;
    }

    /**
     * Test if the given relative url is a Back Office url
     */
    protected function isABackOfficeUrl(string $url): bool
    {
        return 0 === strpos($url, '/' . container()->getParameter('entrypoint.administrator'))
            || 0 === strpos($url, '/' . container()->getParameter('entrypoint.vendor'));
    }
}
