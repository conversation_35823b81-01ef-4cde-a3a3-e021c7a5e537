<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\EventSubscriber;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\KernelEvents;

/**
 * Vérifie, dans le BO, que l'utilisateur a bien le droit de voir la page
 * à laquelle il tente d'accéder en se basant sur les schémas de permissions
 * de CsCart
 */
class CheckPermissionsSubscriber implements EventSubscriberInterface
{
    public static function getSubscribedEvents()
    {
        return [
            KernelEvents::REQUEST => ['checkPermissions', 0],
        ];
    }

    /**
     * Check permissions in the admin (A) area
     */
    public function checkPermissions(RequestEvent $event): void
    {
        if (!\defined('AREA') || AREA === null) {
            return;
        }

        if (AREA !== 'A') {
            return;
        }

        $request = $event->getRequest();
        $controller = $request->attributes->get('controller');

        if ($controller === null) {
            return;
        }

        $mode = $request->attributes->get('mode');
        $runControllers = fn_check_permissions($controller, $mode, 'admin', '', $_REQUEST);

        if ($runControllers == false) {
            if (\defined('AJAX_REQUEST')) {
                $_info = (\defined('DEVELOPMENT') && DEVELOPMENT) ? ' ' . $controller . '.' . $mode : '';
                fn_set_notification('W', __('warning'), __('access_denied') . $_info);
            }

            throw new AccessDeniedHttpException();
        }
    }
}
