<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\EventSubscriber\Yavin\User;

use Wizacha\Marketplace\User\Event\Yavin\UserUpdated;
use Wizacha\Marketplace\User\User;

class UserUpdatedSubscriber extends UserSubscriber
{
    public const EVENT_NAME = 'user.updated';

    /**
     * @param User $user
     *
     * @return string[]
     */
    public function getPayLoad(User $user): array
    {
        return [
            'userId' => $user->getUserUuid()->toString(),
            'apiKey' => $user->getApiKey(),
            'firstName' => $user->getFirstname(),
            'lastName' => $user->getLastname(),
            'email' => $user->getEmail(),
            'companyId' => $user->getCompanyId(),
            'type' => $user->getUserType(),
            'isActivated' => $user->isEnabled(),
        ];
    }

    public function getEventName(): string
    {
        return self::EVENT_NAME;
    }

    /**
     * @return string[]
     */
    public static function getSubscribedEvents(): array
    {
        return [
            UserUpdated::class => ['dispatchMessage', 0],
        ];
    }
}
