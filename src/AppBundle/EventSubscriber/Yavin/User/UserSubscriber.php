<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\EventSubscriber\Yavin\User;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Wizacha\Marketplace\Messenger\BroadcastPublisher;
use Wizacha\Marketplace\User\Event\UserEvent;
use Wizacha\Marketplace\User\User;

abstract class UserSubscriber implements EventSubscriberInterface
{
    private BroadcastPublisher $publisher;

    public function __construct(BroadcastPublisher $publisher)
    {
        $this->publisher = $publisher;
    }

    public function dispatchMessage(UserEvent $userEvent): void
    {
        $currentUser = $userEvent->getUser();

        $this->publisher->publish(
            $this->getEventName(),
            $this->getPayLoad($currentUser),
            []
        );
    }

    /**
     * @param User $user
     *
     * @return string[]
     */
    abstract public function getPayLoad(User $user): array;

    abstract public function getEventName(): string;
}
