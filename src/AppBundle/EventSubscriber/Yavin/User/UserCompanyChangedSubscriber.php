<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\EventSubscriber\Yavin\User;

use Wizacha\Marketplace\User\Event\Yavin\UserCompanyChanged;
use Wizacha\Marketplace\User\User;

class UserCompanyChangedSubscriber extends UserSubscriber
{
    public const EVENT_NAME = 'user.companyChanged';

    /**
     * @param User $user
     *
     * @return string[]
     */
    public function getPayLoad(User $user): array
    {
        return [
            'userId' => $user->getUserUuid()->toString(),
            'companyId' => $user->getCompanyId(),
        ];
    }

    public function getEventName(): string
    {
        return self::EVENT_NAME;
    }

    /**
     * @return string[]
     */
    public static function getSubscribedEvents(): array
    {
        return [
            UserCompanyChanged::class => ['dispatchMessage', 0],
        ];
    }
}
