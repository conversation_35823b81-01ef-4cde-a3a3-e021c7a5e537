<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\EventSubscriber\Yavin\User;

use Wizacha\Marketplace\User\Event\Yavin\UserTypeChanged;
use Wizacha\Marketplace\User\User;

class UserTypeChangedSubscriber extends UserSubscriber
{
    public const EVENT_NAME = 'user.typeChanged';

    /** @return string[] */
    public static function getSubscribedEvents(): array
    {
        return [
            UserTypeChanged::class => ['dispatchMessage', 0],
        ];
    }

    /** @return string[] */
    public function getPayLoad(User $user): array
    {
        return [
            'userId' => $user->getUserUuid()->toString(),
            'type' => $user->getUserType(),
        ];
    }

    public function getEventName(): string
    {
        return self::EVENT_NAME;
    }
}
