<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\EventSubscriber\Yavin\User;

use Wizacha\Marketplace\User\Event\Yavin\UserActivated;
use Wizacha\Marketplace\User\User;

class UserActivatedSubscriber extends UserSubscriber
{
    public const EVENT_NAME = 'user.activated';

    /** @return string[] */
    public static function getSubscribedEvents(): array
    {
        return [
            UserActivated::class => ['dispatchMessage', 0],
        ];
    }

    /** @return string[] */
    public function getPayLoad(User $user): array
    {
        return [
            'userId' => $user->getUserUuid()->toString(),
        ];
    }

    public function getEventName(): string
    {
        return self::EVENT_NAME;
    }
}
