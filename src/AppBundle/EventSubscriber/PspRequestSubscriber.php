<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\EventSubscriber;

use Psr\Log\LoggerInterface;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\Event\TerminateEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Wizacha\Bridge\Monolog\EventLogger\EventDurationTrait;

class PspRequestSubscriber implements EventSubscriberInterface
{
    use EventDurationTrait;

    private LoggerInterface $logger;

    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    public static function getSubscribedEvents()
    {
        return [
            KernelEvents::REQUEST => [['onRequestEvent', 1]],
            KernelEvents::TERMINATE => [['checkAndLogRequest', 1]],
        ];
    }

    public function onRequestEvent(RequestEvent $event)
    {
        $this->startEvent();
    }

    /**
     * Use some normalized ECS fields
     * @see https://www.elastic.co/guide/en/ecs/current/ecs-field-reference.html
     */
    public function checkAndLogRequest(TerminateEvent $event): self
    {
        if (true !== $event->getRequest()->attributes->get('isPsp')) {
            return $this;
        }

        $request = $event->getRequest();
        $response = $event->getResponse();

        $requestData = [
            'url.original' => $request->getRequestUri(),
            'http.request.method' => $request->getMethod(),
            'http.request.body.content' => $request->getContent(),
            'http.request.headers.content-type' => $request->headers->get('content-type'),
            'client.ip' => $request->getClientIp(),
            'http.response.code' => $response->getStatusCode(),
            'http.response.body.content' => $response->getContent(),
            'http.response.headers.location' => $response->headers->get('location'),
        ];

        $eventData = $this->endEvent();
        $extraData = array_merge($requestData, $eventData);

        $this->logger->info('PSP Notification', [
            'extra' => $extraData,
        ]);

        return $this;
    }
}
