<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\EventSubscriber;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;
use Symfony\Component\HttpKernel\KernelEvents;

/**
 * Global authorization for the whole API.
 * The goal of this service is to prevent most of our APIs from being public, except a specific whitelist.
 * This comes in addition to Symfony's firewalls, it does not replace them.
 */
class ApiApplicationFirewall implements EventSubscriberInterface
{
    private const API_AUTHORIZATION_HEADER = 'Application-Token';

    /**
     * @var string
     */
    private $expectedToken;

    public function __construct(?string $expectedToken)
    {
        $this->expectedToken = $expectedToken;
    }

    /**
     * @inheritdoc
     */
    public static function getSubscribedEvents()
    {
        return [
            KernelEvents::REQUEST => ['checkApiAuthentication', 0],
        ];
    }

    public function checkApiAuthentication(RequestEvent $event): void
    {
        if ($this->expectedToken === null || $this->expectedToken === '') {
            // if we don't have a token, don't try to perform authentication
            return;
        }

        if (strpos($event->getRequest()->getRequestUri(), '/api/') !== 0) {
            // apply authentication only to API routes
            return;
        }

        // by default, every api routes must have a token unless it's specified in route declaration
        if ((bool) $event->getRequest()->attributes->get('applicationTokenRequired', true) === false) {
            return;
        }

        $givenToken = $event->getRequest()->headers->get(self::API_AUTHORIZATION_HEADER, null);

        if ($givenToken === null) {
            throw new UnauthorizedHttpException('', "missing '" . self::API_AUTHORIZATION_HEADER . "' header");
        }

        if ($givenToken !== $this->expectedToken) {
            throw new AccessDeniedHttpException("invalid '" . self::API_AUTHORIZATION_HEADER . "' header");
        }
    }
}
