<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\EventSubscriber;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;

/**
 * Redirection vers la page de login du BO si l'utilisateur n'est pas connecté
 */
class AuthRedirectSubscriber implements EventSubscriberInterface
{
    public static function getSubscribedEvents()
    {
        return [
            KernelEvents::REQUEST => ['authRedirect', 0],
        ];
    }

    /**
     * Check if the user is logged in
     */
    public function authRedirect(RequestEvent $event): void
    {
        $request = $event->getRequest();

        // Il n'est pas possible de se baser sur la constante, car dans les
        // tests elle est définie à 'A' de partout ce qui casse tout
        if (0 !== strpos($request->getPathInfo(), '/admin')) {
            return;
        }

        if ($event->getRequest()->attributes->get('_route') === 'admin_oauth_authorize') {
            return;
        }

        if (empty($_SESSION['auth']['user_id'])) {
            fn_set_notification('E', __('access_denied'), __('error_not_logged'));
            $event->setResponse(
                new RedirectResponse(
                    fn_url('auth.login_form?return_url=' . urlencode($request->getRequestUri()))
                )
            );
        }
    }
}
