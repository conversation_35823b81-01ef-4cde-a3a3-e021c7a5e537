<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Constant;

use MyCLabs\Enum\Enum;

/**
 * @method static ResponseContentType CONTENT_TYPE()
 * @method static ResponseContentType PDF()
 */
class ResponseContentType extends Enum
{
    protected const CONTENT_TYPE = 'Content-Type';

    protected const PDF = 'application/pdf';

    public static function getContent(string $filename): string
    {
        return 'attachment; filename="' . $filename . '.pdf"';
    }

    public static function getHeader(string $filename): array
    {
        return [
            'Content-disposition' => static::getContent($filename),
            static::CONTENT_TYPE()->getValue() => static::PDF()->getValue(),
        ];
    }
}
