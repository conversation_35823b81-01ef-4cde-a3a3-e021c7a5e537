<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Exception\Search;

use Wizacha\Marketplace\User\Exception\RuntimeException;

class CannotRemoveSearchRecordException extends RuntimeException
{
    public function __construct($productId, \Throwable $previous = null)
    {
        parent::__construct("Cannot remove search record for product $productId", 0, $previous);
    }
}
