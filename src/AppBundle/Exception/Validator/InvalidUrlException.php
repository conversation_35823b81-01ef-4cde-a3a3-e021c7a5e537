<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @license     Proprietary
 * @copyright   Copyright (c) Wizacha
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Exception\Validator;

use Symfony\Component\HttpFoundation\Response;

class InvalidUrlException extends \InvalidArgumentException
{
    public string $url;

    public function __construct(
        string $url,
        int $code = Response::HTTP_INTERNAL_SERVER_ERROR,
        \Throwable $previous = null
    ) {
        parent::__construct(sprintf('Url is invalid : %s', $url), $code, $previous);
        $this->url = $url;
    }

    public function getUrl(): string
    {
        return $this->url;
    }
}
