<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

declare(strict_types=1);

namespace Wizacha\AppBundle\Validator;

use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class VideoValidator
{
    private static $availableMimeTypes = [
        'video/mp4',        // .mp4
        'video/x-msvideo',  // .avi
        'video/x-matroska', // .mkv
        'video/x-ms-wmv',   // .wmv
    ];

    private static $availableExtension = [
        'mp4',  // .mp4
        'avi',  // .avi
        'mkv',  // .mkv
        'wmv',  // .wmv
    ];

    public function assertIsValid(?UploadedFile $file): self
    {
        if (\is_null($file)) {
            throw new BadRequestHttpException('Non-existent video file');
        }

        if ($file->isFile() === false or $file->isValid() === false) {
            throw new BadRequestHttpException($file->getErrorMessage());
        }

        if (\in_array($file->getMimeType(), static::$availableMimeTypes) === false) {
            throw new BadRequestHttpException(__('exim_unsupported_video_extension', array('%ext%' => $file->getMimeType())));
        }

        return $this;
    }

    public function assertIsUrlValid(?string $videoUrl): self
    {
        $extension = fn_get_file_ext(parse_url($videoUrl, PHP_URL_PATH));
        if (\in_array(fn_strtolower($extension), static::$availableExtension) === false) {
            throw new BadRequestHttpException(__('exim_unsupported_video_extension', array('%ext%' => $extension)));
        }

        return $this;
    }

    public static function getAvailableMimeTypes(): array
    {
        return static::$availableMimeTypes;
    }
}
