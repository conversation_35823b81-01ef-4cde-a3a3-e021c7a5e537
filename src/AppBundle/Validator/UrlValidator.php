<?php

/**
 * <AUTHOR> DevTeam <<EMAIL>>
 * @copyright   Copyright (c) Wizacha
 * @license     Proprietary
 */

namespace Wizacha\AppBundle\Validator;

use Symfony\Component\HttpFoundation\IpUtils;

class UrlValidator implements UrlValidatorInterface
{
    public const AUTHORIZED_PROTOCOLS = ['http', 'https', 'ftp', 'ftps'];

    /** @see https://www.iana.org/assignments/iana-ipv4-special-registry/iana-ipv4-special-registry.xhtml */
    public const FORBIDDEN_IPS = [
        '0.0.0.0/8',
        '10.0.0.0/8',
        '**********/10',
        '*********/8',
        '***********/16',
        '**********/12',
        '*********/24',
        '*********/24',
        '************/24',
        '************/24',
        '***********/24',
        '***********/16',
        '************/24',
        '**********/15',
        '************/24',
        '***********/24',
        '*********/4',
        '240.0.0.0/4',
        '***************',
        '::1/128',
        'fc00::/7',
        'fe80::/10',
        '::ffff:0:0/96',
        '::/128',
    ];

    public function isUrlValid(string $url): bool
    {
        if ($url !== filter_var($url, FILTER_SANITIZE_URL)) {
            return false;
        }

        $protocol_matches = [];
        if (!preg_match('#^(.*)://#', $url, $protocol_matches)) {
            return false;
        }

        return \in_array(strtolower($protocol_matches[1]), self::AUTHORIZED_PROTOCOLS);
    }

    //Interdire les URLs IPV4
    //interdire les URLs  IPV6
    //Faire de la résolution (DNS lookup) pour obtenir l’ip ciblée par ce fqdn, sur la bse de cette ip résolue, interdire les URLs privées IPV4
    public function isUrlFqdnValid(string $url): bool
    {
        if ($url !== \filter_var($url, FILTER_SANITIZE_URL)) {
            return false;
        }

        $domain = \parse_url($url);
        $host = \preg_replace('/^www\./i', '', $domain['host']);

        if (UrlValidator::isIpV4HostValid($host) === false && UrlValidator::isIpV6HostValid($host) === false) {
            $record = @dns_get_record($host, DNS_A);

            if ($record !== false && \count($record) > 0) {
                return UrlValidator::isPrivateIpHostValid($record[0]['ip']);
            }
        }

        return false;
    }

    public static function isIpV4HostValid(string $host): bool
    {
        $result = \preg_match('/^((2[0-4]|1\d|[1-9])?\d|25[0-5])(\.(?1)){3}\z/', $host);

        return $result === 1;
    }

    public static function isIpV6HostValid(string $host): bool
    {
        $result = \preg_match('/^(((?=(?>.*?(::))(?!.+\3)))\3?|([\dA-F]{1,4}(\3|:(?!$)|$)|\2))(?4){5}((?4){2}|((2[0-4]|1\d|[1-9])?\d|25[0-5])(\.(?7)){3})\z/i', $host);

        return $result === 1;
    }

    public static function isPrivateIpHostValid(string $host): bool
    {
        return false === IpUtils::checkIp($host, self::FORBIDDEN_IPS);
    }
}
