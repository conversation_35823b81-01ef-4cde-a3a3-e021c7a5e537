{% block feature_row %}
    <div id="{{ form.vars.id }}"></div>
    <script type="application/javascript">
        var load_category_features;
        $(function () {
            var $feature_content    = $("#{{ form.vars.id }}");
            var input_name_prefix   = "{{ form.vars.full_name }}";
            var base_url            = "{{ path('features_for_form') }}";

            load_category_features = function load_category_features(category_id, $wrapper, callback) {
                $feature_content.load(
                    base_url,
                    {
                        category_id: category_id,
                        input_name_prefix: input_name_prefix,
                        product_id: {{ productId|default(0) }}
                    },
                    function () {
                        if ($feature_content.html().trim().length) {
                            $wrapper.slideDown();
                        } else {
                            $wrapper.slideUp();
                        }
                        if (callback !== undefined) {
                            callback();
                        }
                    }
                );
            }
        });
    </script>
{% endblock %}
