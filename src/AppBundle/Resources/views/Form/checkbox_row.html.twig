{% block checkbox_row %}
    <div class="checkbox">
        {% if label is not same as(false) -%}
            {% if not compound -%}
                {% set label_attr = label_attr|merge({'for': id}) %}
            {%- endif -%}
            {% if required -%}
                {% set label_attr = label_attr|merge({'class': (label_attr.class|default('') ~ ' required')|trim}) %}
            {%- endif -%}
            {% if label is empty -%}
                {%- if label_format is not empty -%}
                    {% set label = label_format|replace({
                        '%name%': name,
                        '%id%': id,
                    }) %}
                {%- else -%}
                    {% set label = name|humanize %}
                {%- endif -%}
            {%- endif -%}
            <label{% for attrname, attrvalue in label_attr %} {{ attrname }}="{{ attrvalue }}"{% endfor %}>
            {{ form_widget(form) }}
            {{ translation_domain is same as(false) ? label : label|trans({}, translation_domain) }}
            </label>
        {%- else -%}
            {{ form_widget(form) }}
        {% endif %}
    </div>
{% endblock %}
