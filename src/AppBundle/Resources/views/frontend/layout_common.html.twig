<!DOCTYPE html>
<html>
<head>

    {% block metas_page_title %} <title>{{ 'website_title' |trans }}</title> {% endblock %}
    {% block metas_description %} <meta name="description" content="{{ 'meta_description_content'|trans }}"  /> {% endblock %}
    {% block metas_keywords %} <meta name=keywords content="{{ 'meta_keywords_content'|trans }}" /> {% endblock %}

    <meta http-equiv="X-UA-Compatible" content="chrome=1">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="mode" content="full" />
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">

    {% block favicon %}{% endblock %}

    {% block metas__opengraph_basicinfos %}{% endblock %}
    {% block metas__opengraph %}{% endblock %}

    {% block metas__twittercards_basicinfos %}{% endblock %}
    {% block metas__twittercards %}{% endblock %}

    {% block metas__additional_metas %}{% endblock %}

    {{ render_smarty('common/head.tpl') }}

    <script type="text/javascript">
        /**
         * Stores analytics data.
         *
         * Can be used for example to post advanced data to Google Analytics, Tag Commander, etc.
         */
        var analytics = {};
    </script>

    {% block meta %}{% endblock %}

</head>
<body>

{% include '@App/frontend/common/notifications.html.twig' %}

<div id="right-menu" style="display:none;">
    <ul>
        <li><a href="{{ cscart_url('profiles.update') }}" class="no-underline">{{ 'block_my_account'|trans }}</a></li>
        <li><a href="{{ path('basket_view') }}" class="no-underline">{{ 'w_my_cart'|trans }}</a></li>
        <li><a href="{{ routeCreateProduct() }}" class="no-underline">{{ 'w_post_an_ad'|trans }}</a></li>
    </ul>
</div>
<div id="content-wrapper">
    <div id="full-screen-filters" class="filtres-responsive" style="display:none;"></div>
    <div id="full-screen-top-close-button" class="hidden">
        <button class="top-close-button close-button glyphicon glyphicon-remove"></button>
    </div>

    {% block header %}
        <!-- HEADER -->
        <header>
            {% block header_content %}
                {{ render_smarty('common/header.tpl') }}
            {% endblock %}
        </header>
        <!-- FIN HEADER -->
    {% endblock %}

    {% block main_container %}
        <div class="container" {% if content_id is defined %}  id="{{ content_id }}" {% endif %}>
            <div class="bloc-content">
                <div class="row">
                    {% block content %}
                        {{ content|raw }}
                    {% endblock %}
                </div>
            </div>
        </div>
    {% endblock %}

    {% block footer %}
        {{ render(controller('AppBundle:Footer:main')) }}
    {% endblock %}

    <div class="ajax-loading-box test-waiting-quantity-change"></div>
</div>

<div id="full-screen-bottom-close-button" class="bottom-close-button close-button hidden">
    <button class="btn centered">{{ 'w_validate'|trans }}</button>
</div>

{% block footer_scripts %}
    {{ render_smarty('common/footer_scripts.tpl') }}
{% endblock %}

{# Required for modal loading (for order info among others ) #}
{{ include("@App/frontend/common/generic-modal.html.twig") }}
</body>
</html>
