{extends file='layout.tpl'}

{block name="metas__additional-metas"}
    {if $product.canonical_url}<link rel="canonical" href="{$product.canonical_url}" />{/if}
    {if $product.status == 'H'}<meta name="robots" content="noindex">{/if}
{/block}

{block name="metas__opengraph"}
    <meta property="og:description" content="{$product.short_description|strip_tags:false}" />
    {foreach $product.images as $image}
    <meta property="og:image" content="{run_controller controller="AppBundle:Image:retrieveUrl" path=$image.path}" />
    {/foreach}
    <meta property="og:title" content="{$product.product_name}" />
    <meta property="og:url" content="{$product.canonical_url}" />
{/block}

{block name="metas__twittercards"}
    {if $product.images.0}<meta name="twitter:image" content="{run_controller controller="AppBundle:Image:retrieveUrl" path=$product.images.0.path}" />{/if}
{/block}

{block name="metas__page-title"}
    <title>{if $seo_data->getTitle()}{$seo_data->getTitle()}{else}{$product.product_name|strip_tags}{/if}</title>
{/block}
{block name="metas__description"}
    {if $seo_data->getDescription()}<meta name="description" content="{$seo_data->getDescription()}" />{else}{$smarty.block.parent}{/if}
{/block}
{block name="metas__keywords"}
    {if $seo_data->getKeywords()}<meta name="keywords" content="{$seo_data->getKeywords()}" />{else}{$smarty.block.parent}{/if}
{/block}

{block name="content"}

    {$data = $product}
    {assign var='preorder' value=$productReadModel->getAvailabilityDate()|is_datetime_in_future}
    {assign var='availabilityDate' value=$productReadModel->getAvailabilityDate()|date_format:'%d/%m/%y'}

    {nocache}
        {$thumbnail_size = 35}
        {$img_size = 650}
        {$parameters = [
            'thumbnail_size' => 35,
            'img_size' => 650,
            'img_base_url' => fn_url('image.thumbnail')
        ]}
    {/nocache}

<ol class="breadcrumb">
    {foreach $data.breadcrumbs as $elt}
        <li><a href="{$elt.link}">{$elt.name}</a></li>
    {/foreach}
</ol>

<div class="product-main-info row" itemscope itemtype="http://schema.org/Product">
    <meta itemprop="name" content="{$data.product_name|strip_tags}" />
    <meta itemprop="description" content="{$data.short_description|strip_tags}" />
    {if $product.images.0}<meta itemprop="image" content="{run_controller controller="AppBundle:Image:retrieveUrl" path=$product.images.0.path}" />{/if}
    <form id="js-declinations" data-product-id="{$data.product_id}" action="{url route='basket_add_product'}" method="post">
    <input type="hidden" name="redirect_url" value="{fn_url('index.php?dispatch=products.view&amp;product_id='|cat:$data.product_id)}" />
    <input type="hidden" name="product_id" class="js-declination-id" value="{$data.product_id}" />
    <div class="col-md-7 col-xs-12">
        <div id="js-mustache-images" class="produit container-full">
            {mustache template='views/products/mustache/images.mustache.tpl'
                      params=[ 'declination' => $data, 'parameters' => $parameters ]}
        </div>
        <script type="text/template" id="js-mustache-images-tpl">{include file='views/products/mustache/images.mustache.tpl'}
        </script>
    </div>

    <div class="col-md-5 col-xs-12"  itemprop="offers" itemscope itemtype="http://schema.org/Offer">
        <meta itemprop="price" content="{$data.reduced_price}" />
        <meta itemprop="pricecurrency" content="{currency_code}" />
        {if $data.condition == 'N'}
            <meta itemprop="itemCondition" itemtype="http://schema.org/OfferItemCondition" content="http://schema.org/NewCondition"/>
        {else}
            <meta itemprop="itemCondition" itemtype="http://schema.org/OfferItemCondition" content="http://schema.org/UsedCondition"/>
        {/if}
        {if $data.in_stock}
            <meta itemprop="availability" content="http://schema.org/InStock" />
        {else}
            <meta itemprop="availability" content="http://schema.org/OutOfStock" />
        {/if}
        <div class="sidebar-produit container-full">
            <h1>
                {$data.product_name|strip_tags|truncate:100}
            </h1>
            <p class="product__description">
                {$data.short_description|strip_tags|truncate:180}
            </p>
            <p class="product__description-link">
                <a href="#description" aria-controls="description" data-toggle="tab" class="js-tab-switch">{__('w_see_full_description')}</a>
            </p>
            <hr/>
            <div class="row">
                <div class="col-sm-6 col-xs-12">
                    <p class="product-condition">{__("w_product_condition_front_{$data.condition}")}</p>
                    {if $data.is_transactional}
                    <p>{__('availability')} :
                        <span id="js-mustache-stock">
                            {mustache template='views/products/mustache/stock.mustache.tpl'
                            params=[ 'declination' => $data, 'preorder' => $preorder, 'preorderText' => __('preorder_date', ['%date%' => $availabilityDate]), 'parameters' => $parameters ]}
                        </span>
                        <script type="text/template" id="js-mustache-stock-tpl">{include file='views/products/mustache/stock.mustache.tpl'}
                        </script>
                    </p>
                    {/if}
                </div>
                <div class="col-sm-6 col-xs-12 pull-right text-right">
                    <div>
                        {nocache}
                            {if $productReadModel->getAverageRating() !== null}
                                <input value="{$productReadModel->getAverageRating()}" disabled="disabled" class="js-rating" data-show-caption="false" data-size="xs" />
                            {/if}
                        {/nocache}
                    </div>
                    {capture name="nbRatings"}
                        {nocache}{run_controller controller="AppBundle:CustomerReview:retrieveNumberRatings" product_id=$data.product_id return_empty_for_zero=true}{/nocache}
                    {/capture}
                    <a href="#customer-review" aria-controls="customer-review" data-toggle="tab" class="js-tab-switch">{__("product_read_reviews", ['[number]' => $smarty.capture.nbRatings])}</a>
                </div>
            </div>

            <hr/>
            <div class="row">
                <div class="col-md-5">
                    <script type="text/javascript">
                        $(document).on('cycle-post-initialize', '.js-images-cycle', function(){
                            W.generateThumbnailsSlider($(this));
                        });
                    </script>
                    {if count($declinations) > 1 && !$data.hide_add_to_cart_data}
                        {include file='views/products/view/declination_selector.tpl' declinations=$declinations product=$data}
                        <script type="text/javascript">

                            var declinations = {$declinations|json_encode};
                            var parameters = {$parameters|json_encode};
                            $(function(){

                                var $form = $('#js-declinations');
                                var $priceTemplate = Hogan.compile($('#js-mustache-price-tpl').text());
                                var $imagesTemplate = Hogan.compile($('#js-mustache-images-tpl').text());
                                var $addToCartTemplate = Hogan.compile($('#js-mustache-add-to-cart-tpl').text());
                                var $quantityTemplate = Hogan.compile($('#js-mustache-quantity-tpl').text());
                                var $stockTemplate = Hogan.compile($('#js-mustache-stock-tpl').text());
                                var getCurrentCombination = function(optionSeparator) {
                                    var declination_id = $form.attr('data-product-id');
                                    $form.find('select.js-declinations-switch').each(function(i, elt) {
                                        declination_id = declination_id + '_' + $(elt).attr('data-option-id') + optionSeparator + $(elt).val();
                                    });
                                    return declination_id;
                                };
                                var switchDeclination = function() {
                                    W.toggleLoadingBox(true);
                                    var declination_id = getCurrentCombination('_');
                                    $('.js-declination-id').val(declination_id);
                                    $('#js-mustache-price').html($priceTemplate.render({
                                        declination: declinations[declination_id]
                                    }));
                                    if (declinations[declination_id]) {
                                        $('#js-mustache-images').html($imagesTemplate.render({
                                            declination: declinations[declination_id],
                                            parameters: parameters
                                        }));
                                    }
                                    $('#js-mustache-add-to-cart').html($addToCartTemplate.render({
                                        declination: declinations[declination_id]
                                    }));
                                    $('#js-mustache-quantity').html($quantityTemplate.render({
                                        declination: declinations[declination_id]
                                    }));
                                    $('#js-mustache-stock').html($stockTemplate.render({
                                        declination: declinations[declination_id],
                                        preorder: {$preorder|json_encode},
                                        preorderText: '{__('preorder_date', ['%date%' => $availabilityDate])|escape:javascript}'
                                    }));
                                    W.generateImagesSlider();
                                    W.toggleLoadingBox(false);
                                };
                                $form.find('select.js-declinations-switch').on('change', function(e) {
                                    switchDeclination();
                                });
                                switchDeclination();
                            });
                        </script>
                    {else}
                        <script type="text/javascript">
                            $(function(){
                                W.generateImagesSlider();
                            });
                        </script>
                    {/if}
                    <div class="options-wrapper">
                        {if $data.is_transactional}
                        <div id="js-mustache-quantity">
                            {mustache template='views/products/mustache/quantity.mustache.tpl'
                            params=[ 'declination' => $data, 'parameters' => $parameters ]}
                        </div>
                        <script type="text/template" id="js-mustache-quantity-tpl">{include file='views/products/mustache/quantity.mustache.tpl'}
                        </script>
                        {/if}
                    </div>
                </div>
                <div class="col-md-7">
                    <div id="js-mustache-price">
                        {mustache template='views/products/mustache/price.mustache.tpl' params=[ 'declination' => $data, 'parameters' => $parameters ]}
                    </div>
                    <script type="text/template" id="js-mustache-price-tpl">{include file='views/products/mustache/price.mustache.tpl'}</script>
                    {if $data.is_transactional}
                    <div id="js-mustache-add-to-cart" class="product__add-to-cart">
                        {mustache template='views/products/mustache/add_to_cart.mustache.tpl'
                        params=[ 'declination' => $data, 'parameters' => $parameters ]}
                    </div>
                    <script type="text/template" id="js-mustache-add-to-cart-tpl">{include file='views/products/mustache/add_to_cart.mustache.tpl'}
                    </script>
                    {else}
                    <div class="product__contact-vendor">
                        <div id="js-mustache-add-to-cart" class="product__add-to-cart">
                            {mustache template='views/products/mustache/not_transactional_add_to_cart.mustache.tpl'
                            params=[ 'declination' => $data ]}
                        </div>
                        <script type="text/template" id="js-mustache-add-to-cart-tpl">{include file='views/products/mustache/not_transactional_add_to_cart.mustache.tpl'}
                        </script>
                    </div>
                    {/if}
                </div>
            </div>
            <hr/>
            <div class="row">
                {if count($data.shippings) && !$data.hide_add_to_cart_data && $data.is_transactional}

                    {$thumbnail_shipping_height = 30}
                    <div class="col-xs-12 col-md-5">
                        {foreach $data.shippings as $shipping}
                            {if $shipping.image.path}
                                <img alt="{$shipping.name}" title="{$shipping.name}" src="{$parameters.img_base_url}&image_path={$shipping.image.path}&h={$thumbnail_shipping_height}" />
                            {/if}
                        {/foreach}
                    </div>
                    <div class="col-xs-12 col-md-7 text-right">
                        {__("product_shipping_price", ['[price]' => {format_price price=$data.cheapest_shipping} ])}
                    </div>

                {/if}
            </div>
            <hr/>
            <div class="addthis_sharing_toolbox text-right"></div>
        </div>

        <div class="container-full row">
            <div class="col-xs-2 text-right"><img src="{asset name="images/profil.png"}" alt=""/></div>
            <div class="col-xs-5">{__("w_sold_by")}
                <a href="{"companies.view?company_id=`$data.company_id`"|fn_url}" class="product__seller-link" itemprop="seller" itemscope itemtype="http://schema.org/Organization">
                    <span itemprop="name">{$data.company_name}</span>
                </a></div>
            <div class="col-xs-2 text-right"><img src="{asset name="images/enveloppe.png"}" alt=""/></div>
            <div class="col-xs-3"><a href="{"discuss.view?discussion_id=0?product_id=`$data.product_id`"|fn_url}" class="product__contact-link">{__("w_contact")}</a></div>
        </div>
        <a data-target="#report_pop_in" class="product__signal-link" data-toggle="modal">{__("report_content")}</a>
    </div>
    </form>
    {include file="views/products/common/report.tpl" product_id=$data.product_id}
</div>
<div class="row">
    <div class="col-xs-12">
            <div>

              <!-- Nav tabs -->
              <ul class="nav nav-tabs" role="tablist">
                  {if $data.description}
                      <li role="presentation" class="active"><a href="#description" aria-controls="description" role="tab" data-toggle="tab">{__('w_tabs_description')}</a></li>
                  {/if}
                  {if count($data.features)}
                      <li role="presentation"><a href="#details" aria-controls="details" role="tab" data-toggle="tab">{__('w_tabs_details')}</a></li>
                  {/if}
                  <li role="presentation"><a href="#customer-review" aria-controls="customer-review" role="tab" data-toggle="tab">{__('discussion_title_product')}</a></li>
              </ul>

            <!-- Tab panes -->
            <div class="tab-content">
                {if $data.description}
                    <div role="tabpanel" class="tab-pane active" id="description">
                        {$data.description}
                    </div>
                {/if}
                {if count($data.features)}
                    <div role="tabpanel" class="tab-pane" id="details">
                        <dl>
                            {foreach $data.features as $feature}
                                <dt>{$feature.name}</dt>
                                {foreach $feature.value as $value}
                                    <dd>{$value}</dd>
                                {/foreach}
                                {if $feature.subfeatures}
                                    <dd style="margin-left: 10px;">
                                        <dl>
                                        {foreach $feature.subfeatures as $subfeature}
                                            <dt>{$subfeature.name}</dt>
                                            {foreach $subfeature.value as $subvalue}
                                                <dd>{$subvalue}</dd>
                                            {/foreach}
                                        {/foreach}
                                        </dl>
                                    </dd>
                                {/if}
                            {/foreach}
                        </dl>
                    </div>
                {/if}
                <div role="tabpanel" class="tab-pane" id="customer-review">
                    {nocache}{run_controller controller="AppBundle:CustomerReview:retrieveProductPosts" product_id=$data.product_id}{/nocache}
                </div>
            </div>

        </div>
    </div>
</div>

    <script type="text/javascript">
        analytics.pageCode = "product";
        analytics.categories = [];
        {foreach $data.breadcrumbs as $elt}
            analytics.categories.push({$elt.name|json_encode nofilter});
        {/foreach}
    </script>

{/block}
