{% extends '@App/frontend/layout.html.twig' %}

{% block metas__page_title %}<title>{{ page.metaTitle }}</title>{% endblock %}
{% block metas__description %}<meta name="description" content="{{ page.metaDescription}}" />{% endblock %}
{% block metas__keywords %}<meta name="keywords" content="{{ page.metaKeywords}}" />{% endblock %}
{% block metas__opengraph %}
    <meta property="og:description" content="{{ page.metaDescription }}" />
    <meta property="og:image" content="{{ absolute_url(asset('images/logo.png')) }}" />
    <meta property="og:title" content=" {{ page.metaTitle }}" />
    <meta property="og:url" content="{{ cscart_url('pages.view?page_id=' ~ page.id) }}" />
{% endblock %}

{% block content %}
    <ol class="breadcrumb">
        {% for element in breadcrumbs %}
            <li><a href="{{ element.link }}">{{ element.name }}</a></li>
        {% endfor %}
    </ol>
    <h1>{{ page.title }}</h1>

    {% if page.wType == constant('\\Wizacha\\Marketplace\\Cms\\PageType::BECOME_SELLER') %}
        {% include '@App/frontend/cms/special_page_become_seller.html.twig' %}
    {% else %}
        {% if page.formData is empty %}
            {{ page.description|raw }}
        {% else %}
            {{ render_smarty_legacy('addons/form_builder/hooks/pages/page_content.override.tpl', {'page': {
                'page_type': page.type,
                'meta_title': page.metaTitle,
                'meta_description': page.metaDescription,
                'meta_keywords': page.metaKeywords,
                'page_id': page.id,
                'description': page.description,
                'form': page.formData
            }}) }}
        {% endif %}
    {% endif %}

    <script type="text/javascript">
        analytics.pageName = '{{ page.metaTitle|e('js') }}';
    </script>

{% endblock %}
