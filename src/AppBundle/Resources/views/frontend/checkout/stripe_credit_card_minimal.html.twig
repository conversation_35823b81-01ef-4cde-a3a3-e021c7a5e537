{# ¡ Modify this template only if you know what you are doing ! #}
{# Ex: if you change / add a class, this is a BC break because the client shows this on his own site #}
{#
Pour Stripe on passe par du HTML car ils ne fonctionnent pas en mode page de paiement hébergée
Du coup, on passe par leur lib JS qui permet d'afficher le form de paiement de notre coté, de générer un token à partir de la CB rentrée
Et ce token est ensuite posté sur notre back
#}

<script src="https://js.stripe.com/v3/"></script>

<form action="{{ callbackUrl }}" method="post" id="checkout-api-stripe-payment-form">
    <div>
        <label for="checkout-api-stripe-card-element">
            {{ 'stripe_checkout_label'|trans }}
        </label>
        <div id="checkout-api-stripe-card-element">
            <!-- A Stripe Element will be inserted here. -->
        </div>

        <!-- Used to display form errors. -->
        <div id="checkout-api-stripe-card-errors" role="alert"></div>
    </div>

    <button id="checkout-api-stripe-payment-button" data-secret="{{ clientSecret }}">{{ 'stripe_checkout_submit_payment'|trans }}</button>
</form>

<script>
    // Create a Stripe client.
    var stripe = Stripe('{{ stripePublicKey }}');

    // Create an instance of Elements.
    var elements = stripe.elements({
        locale: '{{ locale }}'
    });

    // Custom styling can be passed to options when creating an Element.
    // (Note that this demo uses a wider set of styles than the guide below.)
    var style = {
        base: {
            color: '#32325d',
            lineHeight: '18px',
            fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
            fontSmoothing: 'antialiased',
            fontSize: '16px',
            '::placeholder': {
                color: '#aab7c4'
            }
        },
        invalid: {
            color: '#fa755a',
            iconColor: '#fa755a'
        }
    };

    // Wizaplace integration: Define wizaplaceStripeCheckoutStyle to apply custom style
    if (typeof wizaplaceStripeCheckoutStyle !== 'undefined') {
        style = wizaplaceStripeCheckoutStyle;
    }

    // Create an instance of the card Element.
    var card = elements.create('card', {style: style});

    // Add an instance of the card Element into the `checkout-api-stripe-card-element` <div>.
    card.mount('#checkout-api-stripe-card-element');

    // Handle real-time validation errors from the card Element.
    card.addEventListener('change', function(event) {
        var displayError = document.getElementById('checkout-api-stripe-card-errors');
        if (event.error) {
            displayError.textContent = event.error.message;
        } else {
            displayError.textContent = '';
        }
    });

    // Handle form submission.
    var form = document.getElementById('checkout-api-stripe-payment-form');
    form.addEventListener('submit', function(event) {
        var submitButton = document.getElementById('checkout-api-stripe-payment-button');
        var clientSecret = submitButton.dataset.secret;
        if (submitButton.disabled) {
            return false;
        }
        event.preventDefault();
        submitButton.disabled = true;

        stripe.handleCardPayment(clientSecret, card).then(function(response) {
            if (response.error) {
                // Inform the user if there was an error.
                var errorElement = document.getElementById('checkout-api-stripe-card-errors');
                errorElement.textContent = response.error.message;
                submitButton.disabled = false;
            } else {
                // Send the token to your server.
                stripeTokenHandler(response.paymentIntent.id);
            }
        });
    });

    function stripeTokenHandler(token) {
        // Insert the token ID into the form so it gets submitted to the server
        var form = document.getElementById('checkout-api-stripe-payment-form');
        var hiddenInput = document.createElement('input');
        hiddenInput.setAttribute('type', 'hidden');
        hiddenInput.setAttribute('name', 'stripeToken');
        hiddenInput.setAttribute('value', token);
        form.appendChild(hiddenInput);

        // Submit the form
        form.submit();
    }
</script>
