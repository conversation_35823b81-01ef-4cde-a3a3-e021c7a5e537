{# ¡ Modify this template only if you know what you are doing ! #}
{# Ex: if you change / add a class, this is a BC break because the client shows this on his own site #}
{#
Moyen de paiement HTML qui permet au user d'envoyer son IBAN + nom à stripe
Stripe génère une Source a partir de ca
On affiche un lien vers le mandat de prelevement SEPA et on demande au user de confirmer
A ce moment on POST vers notre controlleur de callback le token de la Source
#}

<script src="https://js.stripe.com/v3/"></script>

<div id="checkout-api-stripe-sepa-mandate-acceptance">
    {{ 'stripe_sepa_mandate_acceptance'|trans({'[name]': adminName})|nl2br }}
</div>

<div>
    <label for="checkout-api-stripe-iban">{{ 'iban'|trans }}</label>
    <div id="checkout-api-stripe-iban"></div>
</div>

<div id="checkout-api-stripe-sepa-group">

    <label>
        <span>{{ 'name'|trans }}</span>
        <input type="text" id="checkout-api-stripe-owner-name" value="{{ name }}">
    </label>
</div>

<!-- Used to display form errors. -->
<div id="checkout-api-stripe-sepa-errors" role="alert"></div>

<button id="checkout-api-stripe-payment-button-sepa">{{ 'stripe_checkout_submit_payment_sepa_1'|trans }}</button>

<form action="{{ callbackUrl }}" method="post" id="checkout-api-stripe-payment-form-sepa" style="display:none">
    <hr>
    <input type="hidden" name="stripeToken" id="wizaplace-internal-stripe-token">
    <input type="hidden" name="idempotencyKey" id="wizaplace-internal-idempotency-key" value="{{ idempotencyKey }}">
    <input id="checkout-api-stripe-payment-button-sepa-mandate" type="submit" value="{{ 'stripe_checkout_submit_payment_sepa_2'|trans }}">
    <span>{{ 'stripe_sepa_mandate_warning'|trans }}: <a id="checkout-api-stripe-sepa-mandate-link" target="_blank">{{ 'stripe_mandat'|trans }}</a></span>
</form>



<script>
    // Create a Stripe client.
    var stripe = Stripe('{{ stripePublicKey }}', {apiVersion: '{{ stripeApiVersion }}'});

    var iban = stripe.elements().create('iban', {
        supportedCountries: ['SEPA']
    });

    iban.mount('#checkout-api-stripe-iban');

    // Handle form submission.
    var submitButton = document.getElementById('checkout-api-stripe-payment-button-sepa');
    submitButton.addEventListener('click', function() {
        var errorElement = document.getElementById('checkout-api-stripe-sepa-errors');
        errorElement.textContent = '';

        submitButton.disabled = true;

        stripe.createSource(iban, {
            type: 'sepa_debit',
            currency: '{{ currencyCode }}',
            owner: {
                name: document.getElementById('checkout-api-stripe-owner-name').value
            }
        }).then(function(result) {
            // handle result.error or result.source
            if (result.error) {
                // Inform the user if there was an error.
                errorElement.textContent = result.error.message;
                submitButton.disabled = false;
            } else {
                // show next step form + set mandate href + set hidden token + do not re enable button
                document.getElementById('checkout-api-stripe-payment-form-sepa').style.display = null;
                document.getElementById('checkout-api-stripe-sepa-mandate-link').href = result.source.sepa_debit.mandate_url;
                document.getElementById('wizaplace-internal-stripe-token').value = result.source.id;
            }
        });
    });

    var submitForm = document.getElementById('checkout-api-stripe-payment-button-sepa-mandate');
    submitForm.addEventListener('click', function() {
        submitForm.disabled = true;
        document.getElementById('checkout-api-stripe-payment-form-sepa').submit();
    });
</script>
