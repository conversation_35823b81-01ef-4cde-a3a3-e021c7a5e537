<div class="notifications">
    {% for key, message in getNotifications() %}
        {% set notificationType = 'alert-success' %}
        {% if message.type == 'I' %}
            {# Modal, e.g. product added to basket #}
            {% set notificationType = 'alert-info' %}

            <div id="notification_{{ key }}" class="modal js-open-modal-on-load">
                <div class="modal-dialog">
                    {{ message.message|raw }}
                </div>
            </div>
        {% else %}
            {% if message.type == 'W' %}
                {% set notificationType = 'alert-warning' %}
            {% elseif message.type == 'E' or message.type == 'O' %}
                {% set notificationType = 'alert-danger' %}
            {% endif %}

            <div class="alert {{ notificationType }} alert-dismissible" data-dismiss="alert">
                <button type="button" class="close" data-dismiss="alert"><span>&times;</span></button>
                <strong>{{ message.title }}</strong>
                {{ message.message }}
            </div>
        {% endif %}
    {% endfor %}
</div>
