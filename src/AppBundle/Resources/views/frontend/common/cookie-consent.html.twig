<script>
    // <PERSON><PERSON> plugin by <PERSON><PERSON> - http://silktide.com/cookieconsent
    window.cookieconsent_options = {
        message: "{{ "cookies_message"|trans|e('js') }}",
        dismiss: "{{ "cookies_dismiss"|trans|e('js') }}",
        learnMore: "{{ "cookies_learnmore"|trans|e('js') }}",
        theme: false,
        {% verbatim %}
        markup: [
            '<div class="cc_wizaplace_banner-wrapper cc_banner-wrapper {{containerClasses}}">',
            '<div class="cc_banner cc_container cc_container--open">',
            '<div class="cc_content">',
            "<p class='cc_message'>{{options.message}} <a data-cc-if='options.link' class='cc_more_info' href='{{options.link || '#null'}}'>{{options.learnMore}}</a></p>",
            "<a href='#null' data-cc-event='click:dismiss' target='_blank' class='cc_btn cc_btn_accept_all'>{{options.dismiss}}</a>",
            '</div>',
            '</div>',
            '</div>'
        ]
        {% endverbatim %}
    };
</script>
