<form action="{fn_url()}" id="header-search-form">
    <div class="search-form-home">
        <div class="row">
            {nocache}
                <div class="col-lg-4 col-sm-4 col-xs-12">
                    <input id="search_input" type="text" name="q" value="{$search.q|escape:'html'}" placeholder="{__('w_top-search_placeholder_search-input')}" tabindex="1" autocomplete="off" />
                </div>
                <div>
                    <div class="col-lg-3 col-sm-3 col-xs-6 more-search">
                        {run_controller controller="AppBundle:Category:dropdown"}
                    </div>
                    <div class="col-lg-2 col-sm-2 col-xs-6 more-search">
                        <input type="text" name="geoloc" value="{json_value json=$smarty.request.geoloc key='localization' default=$smarty.request.geoloc}" id="geoloc_input" class="js-geoloc-input" placeholder="{__('w_top-search_placeholder_geoloc-input')}" tabindex="2" autocomplete="off" />
                        <div class="hidden" id="top-search-geoloc-suggestion-template">
                            <div class="top-search-suggestion-geocoding">{literal}{{ ville }} <span class="code-postal">{{ code_postal }}</span>{/literal}</div>
                        </div>
                        <input type="hidden" name="lat" id="js-geoloc-input-latitude"  value="{$smarty.request.lat}" />
                        <input type="hidden" name="lng" id="js-geoloc-input-longitude" value="{$smarty.request.lng}" />
                    </div>
                    <div class="col-lg-3 col-sm-3 col-xs-12"><button type="submit">{__('w_search_button')}</button></div>
                </div>
            {/nocache}
        </div>
    </div>
</form>
{$search_config_class = '\Wizacha\Search\Config'}
{$search_class = $config[$search_config_class|cat:'::CFG_SEARCH_ENGINE'|constant][$search_config_class|cat:'::SEARCH_ENGINE_CLASS'|constant]}

<script type="text/javascript">
    $(function(){
        var topSearchInput  = $("#search_input");
        var topSearchGeoLoc = $(".js-geoloc-input");

        initGeoCoding(
                W.searchClient.initIndex('{$config[$search_config_class|cat:'::CFG_SEARCH_ENGINE'|constant][$search_config_class|cat:'::INDEX_GEOCODING'|constant]}'),
                topSearchGeoLoc,
                $("#js-geoloc-input-latitude"),
                $("#js-geoloc-input-longitude"),
                Hogan.compile($("#top-search-geoloc-suggestion-template").html())
        );
        topSearchGeoLoc.parent().css({ display: 'block' });

        {* Do not transmit enter key event to avoid accidental submission *}
        topSearchGeoLoc.on('keydown', function(e){
            if(e.keyCode == 13) { {* enter pressed *}
                e.preventDefault();
                e.stopPropagation();
                topSearchGeoLoc.trigger('wizacha:changed');
            }
        });

        topSearchGeoLoc.on('wizacha:changed', function(){
            {* Emulate event 'change' on main input *}
            var ttInstance = topSearchInput.data('ttTypeahead');
            if (undefined !== ttInstance) {
                ttInstance._onQueryChanged(null, topSearchInput.prop('value'));
            } else {
                topSearchInput.trigger('keyup');
            }
            {* Then give focus back to main input *}
            topSearchInput.focus();
        });
    });
</script>
