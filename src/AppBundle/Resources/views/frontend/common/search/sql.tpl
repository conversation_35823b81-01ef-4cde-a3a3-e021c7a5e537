<script type="text/javascript">

    $(function() {
        W.searchClient = new SQLSearchClient();
    });

    //Init object
    var SQLSearchClient = function () {
    };

    //set prototype
    SQLSearchClient.prototype = {
        initIndex: function (indexName) {
            return new this.Index(this, indexName);
        },
        Index: function (search, indexName) {
            this.indexName = indexName;
            this.as = search;
        },
        startQueriesBatch: function () {
            this.batch = [];
        },
        addQueryInBatch: function (indexName, term, params) {
            this.batch.push([indexName, term, params]);
        },
        sendQueriesBatch: function (callback) {
            var elt;
            var indexes = [];

            var args = [];
            var query = '';

            for (elt in this.batch) {
                if (this.batch.hasOwnProperty(elt)) {
                    if (elt == 0) {
                        query = this.batch[elt][1];
                    }
                    args = {
                        params: this.batch[elt][2],
                        index: this.batch[elt][0]
                    };
                    indexes.push(args);
                }
            }

            $.ajax({
                url: "{path route="home"}",
                data: {
                    indexes: indexes,
                    dispatch: 'search.batch',
                    q: query
                },
                success: function (data) {
                    callback(true, JSON.parse(data));
                }
            });
        },
        initHelper: function (index, config, callback) {
            callback(new this.Helper(index));
        },
        Helper: function(index) {
            this.index  = index;
            this.page   = 0;
            this.searchCallback = function(){};
            this.searchParams   = {};
            this.refinements    = {};
            this.disjunctiveRefinements = {};
        }
    };

    //set index prototype
    SQLSearchClient.prototype.Index.prototype = {
        search: function (query, callback, args, delay) {
            $.ajax({
                url: "{path route="home"}",
                data: {
                    args: args,
                    index: this.indexName,
                    dispatch: 'search.ajax',
                    q: query
                },
                success: function (data) {
                    callback(true, JSON.parse(data));
                }
            });
        },
        ttAdapter: function (params) {
            var self = this;
            return function (query, cb) {
                self.search(query, function (success, content) {
                    if (success) {
                        cb(content.hits);
                    }
                }, params);
            };
        }
    };

    SQLSearchClient.prototype.Helper.prototype = {
        search      : function (query, searchCallback, params) {
            this.searchCallback = searchCallback;
            this.searchParams = params;
            $.ajax({
                url    : "{path route="home"}",
                data   : {
                    q          : query,
                    args       : params,
                    index      : this.index,
                    page       : this.page,
                    refinements: this.refinements,
                    dispatch   : 'search.facets'
                },
                success: function (data) {
                    this.refinements = data.refinements || {};
                    searchCallback(true, data);
                }
            });
        },
        gotoPage    : function (page) {
            this.page = page;
            this.search('', this.searchCallback, this.searchParams);
        },
        setPage    : function (page) {
            this.page = page;
        },
        isRefined   : function (facet, value) {
            this.refinements[facet] = this.refinements[facet] || {};
            return this.refinements[facet][value] || false;
        },
        justToggleRefine: function (facet, value) {
            this.refinements[facet] = this.refinements[facet] || {};
            this.refinements[facet][value] = !(this.refinements[facet][value] || false);
            this.page = 0;
            return true;
        },
        toggleRefine: function (facet, value) {
            this.justToggleRefine(facet, value);
            this.search('', this.searchCallback, this.searchParams);
            return true;
        },
        addDisjunctiveRefine: function (facet, value) {
            this.disjunctiveRefinements[facet] = {};
            this.disjunctiveRefinements[facet][value] = true;
        }
    }
</script>
