{capture name="cart_block"}
    <a role="menuitem" rel="nofollow" class="dropdown-toggle" id="cart-trigger" data-toggle="dropdown" href="{path route='basket_view'}">{__('w_my_cart')}</a>
    <div aria-labelledby="cart-trigger" role="menu" class="dropdown-menu dropdown-menu-right" id="monpanier">
        {nocache}
            {run_controller controller='AppBundle:Basket:compact'}
        {/nocache}
    </div>
{/capture}

<div id="header-mini" class="floatable header-top" style="display:none;">
    <div class="container-fluid">
        <div class="row">
            <div class="visible-xs col-xs-4 col-md-2">
                <a href="{fn_url()}"><img class="logo-header" src="{asset name="images/Logo_sans_W_bleuvert.png"}" alt="{__("homepage")}"></a>
            </div>
            <div class="hidden-xs col-xs-4 col-md-2">
                <a href="{fn_url()}"><img class="logo-header" src="{asset name="images/Logo_couleur_bleu.png"}" alt="{__("homepage")}"></a>
            </div>
            <div class="col-xs-4 col-md-offset-1 col-lg-offset-2 top-menu-responsive top-menu">
                <div class="hidden-xs">
                    <input type="text" class="search-form js-focus-search" size="30" value="{$search.q|escape:'html'}" placeholder="{__('w_top-search_placeholder_search-input')}" />
                </div>
            </div>
            <div class="col-lg-4 col-md-5 col-xs-4">
                <nav>
                    <ul class="visible-lg-block visible-md-block">
                        <li class="dropdown">
                            <a role="menuitem" rel="nofollow" class="dropdown-toggle" id="account-trigger" data-toggle="dropdown" href="{fn_url('profiles.update')}">{__('block_my_account')}</a>
                            <div aria-labelledby="account-trigger" role="menu" class="dropdown-menu dropdown-menu-right">
                                {nocache}
                                    {run_controller controller='AppBundle:Profile:compact'}
                                {/nocache}
                            </div>
                        </li>
                        <li class="dropdown">{$smarty.capture.cart_block}</li>
                        {nocache}
                            <li><a class="btn-primary" rel="nofollow" href="{route_create_product}">{__('w_post_an_ad')}</a></li>
                        {/nocache}
                    </ul>
                    <ul class="hidden-lg hidden-md">
                        <li>
                            <button class="square visible-xs hidden-sm js-focus-search btn"><img src="{asset name="images/search_icon.png"}" alt="{__("search")}"></button>
                        </li>
                        <li>
                            <button class="square js-toggle-menu"><img src="{asset name="images/menu_responsive.png"}" alt="{__("open_menu")}"></button>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>


<div class="header-bottom-search">
    <div class="filtre-header-bottom">
        <div class="header-top">
            <div class="container-fluid" id="header-classic">
                <div class="row">
                    <div class="col-xs-2 hidden-xs">
                        <a href="{fn_url()}"><img class="logo-header" src="{asset name="images/Logo_couleur_blanc.png"}" alt="{__("homepage")}"></a>
                    </div>
                    <div class="col-xs-2 visible-xs">
                        <a href="{fn_url()}"><img class="logo-header" src="{asset name="images/logo.png"}" alt="{__("homepage")}"></a>
                    </div>
                    <div class="col-md-10 hidden-xs hidden-sm top-menu" >
                        <nav>
                            <ul>
                                <li class="dropdown">
                                    <a role="menuitem" rel="nofollow" class="dropdown-toggle" id="account-trigger" data-toggle="dropdown" href="{fn_url('profiles.update')}">{__('block_my_account')}</a>
                                    <div aria-labelledby="account-trigger" role="menu" class="dropdown-menu dropdown-menu-right">
                                        {nocache}
                                            {run_controller controller='AppBundle:Profile:compact'}
                                        {/nocache}
                                    </div>
                                </li>
                                <li class="dropdown">{$smarty.capture.cart_block}</li>
                                {nocache}
                                    <li><a class="btn-primary faded" rel="nofollow" href="{route_create_product}">{__('w_post_an_ad')}</a></li>
                                {/nocache}
                            </ul>
                        </nav>
                    </div>


                    <!-- Menu top right responsive -->

                    <div class="col-xs-10 visible-xs visible-sm top-menu">
                        <div class="top-menu-responsive">
                            <nav>
                                <ul>
                                    <li>
                                        {nocache}
                                            <a class="btn-primary faded" rel="nofollow" href="{route_create_product}">{__('sold')}</a>
                                        {/nocache}
                                    </li>
                                    <li>
                                        <button class="square js-toggle-menu"><img src="{asset name="images/menu_responsive.png"}" alt="{__("open_menu")}"></button>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <div class="container">
            {block name="punch_line"}
            {/block}
            <!-- Search Form -->
            <div class="row">
                <div class="col-md-10 col-md-offset-1">
                    {include file="common/search/search_form.tpl"}
                </div>
            </div>
        </div>
    </div>
</div>
