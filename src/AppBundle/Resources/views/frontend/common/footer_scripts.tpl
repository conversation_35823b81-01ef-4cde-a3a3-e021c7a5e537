<script type="text/javascript" src="//cdnjs.cloudflare.com/ajax/libs/jquery-form-validator/2.1.47/jquery.form-validator.min.js"></script>
<script type="text/javascript" src="//cdnjs.cloudflare.com/ajax/libs/URI.js/1.11.2/URI.min.js"></script>
<script type="text/javascript" src="//cdnjs.cloudflare.com/ajax/libs/typeahead.js/0.10.4/typeahead.bundle.min.js"></script>
<script type="text/javascript" src="//cdnjs.cloudflare.com/ajax/libs/jquery.cycle2/20140415/jquery.cycle2.min.js"></script>
<script type="text/javascript" src="//cdnjs.cloudflare.com/ajax/libs/fancybox/2.1.5/jquery.fancybox.min.js"></script>
<script type="text/javascript" src="//cdn.jsdelivr.net/algoliasearch/2.9.6/algoliasearch.min.js"></script>
<script type="text/javascript" src="//s7.addthis.com/js/300/addthis_widget.js#pubid=ra-54f57d6c58a2579e" async="async"></script>
<script type="text/javascript" src="//vjs.zencdn.net/5.5.3/video.min.js"></script>
<link href="//vjs.zencdn.net/5.5.3/video-js.css" rel="stylesheet">
<script type="text/javascript">
    var starCaptions = {
        0: '',
        1: '{__('poor')}',
        2: '{__('fair')}',
        3: '{__('average')}',
        4: '{__('very_good')}',
        5: '{__('excellent')}'
    }
</script>

<!-- Begin Cookie Consent plugin by Silktide - http://silktide.com/cookieconsent -->
<script type="text/javascript">
    window.cookieconsent_options = {
        message: "{__('cookies_message')}",
        dismiss: "{__('cookies_dismiss')}",
        learnMore: "{__('cookies_learnmore')}",
        {if $config.cookies_page_id}
            link: "{fn_url('pages.view&page_id='|cat:$config.cookies_page_id)}",
        {/if}
        theme: false,
        {literal}markup: [
            '<div class="cc_wizaplace_banner-wrapper cc_banner-wrapper {{containerClasses}}">',
            '<div class="cc_banner cc_container cc_container--open">',
            '<div class="cc_content">',
            "<p class='cc_message'>{{options.message}} <a data-cc-if='options.link' class='cc_more_info' href='{{options.link || '#null'}}'>{{options.learnMore}}</a></p>",
            "<a href='#null' data-cc-event='click:dismiss' target='_blank' class='cc_btn cc_btn_accept_all'>{{options.dismiss}}</a>",
            '</div>',
            '</div>',
            '</div>'
        ]{/literal}
    };
</script>
<!-- End Cookie Consent plugin -->

{include_scripts}
