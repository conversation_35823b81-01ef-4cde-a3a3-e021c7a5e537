<!DOCTYPE html>
<html>
<head>
    {block name="metas__page-title"}<title>{__("website_title")}</title>{/block}
    {block name="metas__description"}<meta name="description" content="{__('meta_description_content')}" />{/block}
    {block name="metas__keywords"}<meta name="keywords" content="{__('meta_keywords_content')}" />{/block}

    <meta http-equiv="X-UA-Compatible" content="chrome=1">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="mode" content="full" />
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">

    {block name="favicon"}{/block}

    {block name="metas__opengraph--basicinfos"}{/block}
    {block name="metas__opengraph"}{/block}

    {block name="metas__twittercards--basicinfos"}{/block}
    {block name="metas__twittercards"}{/block}

    {block name="metas__additional-metas"}{/block}

    {include file="common/head.tpl"}

    <script type="text/javascript">
        /**
         * Stores analytics data.
         *
         * Can be used for example to post advanced data to Google Analytics, Tag Commander, etc.
         */
        var analytics = {};
    </script>

    {block name="meta"}{/block}

</head>
<body>
{nocache}
    {run_controller controller='AppBundle:Notification:display'}
{/nocache}
<div id="right-menu" style="display:none;">
    <ul>
        <li><a href="{fn_url('profiles.update')}" class="no-underline">{__('block_my_account')}</a></li>
        <li><a href="{path route='basket_view'}" class="no-underline">{__('w_my_cart')}</a></li>
        {nocache}
            <li><a href="{route_create_product}" class="no-underline">{__('w_post_an_ad')}</a></li>
        {/nocache}
    </ul>
</div>
<div id="content-wrapper">
    <div id="full-screen-filters" class="filtres-responsive" style="display:none;">{*Filled in JS*}</div>
    <div id="full-screen-top-close-button" class="hidden">
        <button class="top-close-button close-button glyphicon glyphicon-remove"></button>
    </div>

    {block name="header"}
        <!-- HEADER -->
        <header>
            {block name="header_content"}
                {include file="common/header.tpl"}
            {/block}
        </header>
        <!-- FIN HEADER -->
    {/block}

    {block name="main_container"}
        <div class="container"{if $content_id} id="{$content_id}"{/if}>
            <div class="bloc-content">
                <div class="row">
                    {block name="content"}
                        {$content nofilter}
                    {/block}
                </div>
            </div>
        </div>
    {/block}

    {block name="footer"}
        {nocache}
            {run_controller controller="AppBundle:Footer:main"}
        {/nocache}
    {/block}

    <div class="ajax-loading-box test-waiting-quantity-change"></div>

    {nocache}
        {smarty_debug}
    {/nocache}
</div>

<div id="full-screen-bottom-close-button" class="bottom-close-button close-button hidden">
    <button class="btn centered">{__('w_validate')}</button>
</div>

{block name="footer_scripts"}
    {include file="common/footer_scripts.tpl"}
{/block}

{* Required for modal loading (for order info among others )*}
{render_twig path='@App/frontend/common/generic-modal.html.twig'}
</body>
</html>
