<a id="add-discount" class="js-switch smalltext" data-target-show="#discount-coupon" data-target-hide="#add-discount">{{ 'w_promo_code'|trans }}</a>

{% if basket.coupons is defined %}
    <ul>
        {% for couponCode in basket.coupons %}
            <li>{{ couponCode }} <a class="text-danger" href="{{ path('basket_remove_promotion', {coupon: couponCode}) }}">&cross;</a></li>
        {% endfor %}
    </ul>
{% endif %}

<div class="code-input discount-coupon" id="discount-coupon" style="display:none;">
    {{ form_start(form_coupon_code) }}
        <div class="form-group">
            {{ form_widget(form_coupon_code.coupon_code, {'attr':{'class':'form-control inline-block'}}) }}

            <div class="inline-block top">
                {{ form_row(form_coupon_code.ok, {'attr':{'class':'btn-small'}}) }}
            </div>
        </div>
    {{ form_end(form_coupon_code) }}
</div>
