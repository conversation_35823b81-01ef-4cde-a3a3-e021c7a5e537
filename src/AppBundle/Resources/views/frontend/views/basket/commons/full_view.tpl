<div class="col-xs-12">
    {if !($basket && $basket['groups'])}
        <p class="no-items" data-test-class="empty-basket">{__("text_cart_empty")}</p>
        <a href="{fn_url('')}" class="btn-lg">{__("continue_shopping")}</a>
    {else}
        <fieldset class="basket-group">
            {foreach $basket['groups'] as $company_group}
                <h2>{__('vendor')} : {$company_group['companyName']}</h2>
                {foreach $company_group['products'] as $shipping_group}
                    <div class="declinations">
                        {foreach $shipping_group['products'] as $product}
                            {include file="views/basket/commons/declination.tpl" product=$product}
                        {/foreach}
                    </div>
                    {if !empty($shipping_group['shippings'])}
                        <div class="basket-select-shipping">
                            {__('w_select_shipping_mode')} :
                            <select class="js-switch-shipping" data-group-id="{$shipping_group['simpleGroupId']}">
                                {foreach $shipping_group['shippings'] as $shipping}
                                    <option {if $shipping['selected']}selected="selected" {/if}value="{$shipping['shippingId']}">{$shipping['shippingName']} - {format_price price=$shipping['shippingPrice']}</option>
                                {/foreach}
                            </select>

                        </div>
                    {/if}
                {/foreach}
            {/foreach}
        </fieldset>
        {include file="views/basket/commons/additional_infos.tpl"}

    {/if}
</div>
