<div class="row">
    <div class="hidden-xs col-sm-6">
        <div class="order-general-infos">
            <table>
                <tr>
                    <td><i class="secured-icon icon"></i></td>
                    <td>{__('w_secured_payment')}&nbsp;<i class="payment-methods-icon icon"></i></td>
                </tr>
                <tr>
                    <td><i class="shipping-icon icon"></i></td>
                    <td>{__('w_delivery_text')}</td>
                </tr>
                <tr>
                    <td class="text-center"><i class="glyphicon glyphicon-ok"></i></td>
                    <td>{__('w_wizacha_guarantee')}</td>
                </tr>
            </table>
        </div>
    </div>
    <div class="col-xs-12 col-sm-6">
        <div class="basket-total">
            <table>
                <tr>
                    <td colspan="2" class="left">
                        {__('w_order_summary')}
                    </td>
                </tr>
                <tr>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
                <tr>
                    <td>{__('w_order_items_amount')}</td>
                    <td class="right">{$basket.subtotal|format_money}</td>
                </tr>
                <tr>
                    <td>{__('w_shipping_total_amount')}</td>
                    <td class="right">{format_price price=$basket.shipping_total}</td>
                </tr>
                {if ($basket.subtotal_discount->getAmount() != 0)}
                <tr>
                    <td>{__('order_discount')}</td>
                    <td class="right">-{$basket.subtotal_discount|format_money}</td>
                </tr>
                {/if}
                <tr>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
                <tr>
                    <td class="middle">
                        {__('w_cart_total')}<br/>
                        <span class="smalltext test-basket-tax">{__('w_TVA_amount', ['[amount]' => {format_price price=$basket.tax}])}</span>
                    </td>
                    <td class="right middle bold test-basket-total"><strong>{format_price price=$basket.total}</strong></td>
                </tr>
            </table>
        </div>
    </div>
    <div class="visible-xs col-xs-12">
        <div class="order-general-infos">
            <table>
                <tr>
                    <td><i class="secured-icon icon"></i></td>
                    <td>{__('w_secured_payment')}&nbsp;<i class="payment-methods-icon icon"></i></td>
                </tr>
                <tr>
                    <td><i class="shipping-icon icon"></i></td>
                    <td>{__('w_delivery_text')}</td>
                </tr>
                <tr>
                    <td class="text-center"><i class="glyphicon glyphicon-ok"></i></td>
                    <td>{__('w_wizacha_guarantee')}</td>
                </tr>
            </table>
        </div>
    </div>
    <div class="col-xs-12 marged">
        <div class="pull-right">
            <div class="inline-block middle">
                {nocache}{run_controller controller='AppBundle:Basket:addCartPromotion'}{/nocache}
            </div>
            <div class="inline-block middle">
                <a class="btn-lg btn-primary" rel="nofollow" href="{fn_url('checkout.checkout')}">{__('w_validate_command')}</a>
            </div>
        </div>
    </div>

</div>
