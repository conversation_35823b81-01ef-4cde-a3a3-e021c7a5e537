{$data = $product.declination}
{$quantity = $product.quantity}
{capture name="image"}
    {if $data.image_id}
        {run_controller controller="AppBundle:Image:display" image_id=$data.image_id size_x=120 size_y=120}
            {else}
        <img width="120" height="120" alt="{__("no_image")}" title="{__("no_image")}" src="{asset name="images/no-image.jpg"}">
    {/if}
{/capture}
{capture name="price"}
    <div class="price-product">
        <p>
            <span class="price">{format_price price=$product.subtotal}</span>
        </p>
    </div>
    <div>
        {if $data.price > $data.reduced_price}
        <span class="original-price">{format_price price=$data.price}</span><br>
        {/if}
        {$quantity} x <span class="price js-attr-reduced-price">{format_price price=$data.reduced_price} {__('ttc')}</span>
    </div>
{/capture}
{capture name="stock"}
    {if $data.availability_date|is_datetime_in_future}
        <p class="in-stock">{__('preorder_date', ['%date%' => $data.availability_date|date_format:'%d/%m/%y'])}</p>
    {elseif $data.amount}
        <p class="in-stock">{__('in_stock')}</p>
    {else}
        <p class="not-in-stock">{__('out_of_stock_products')}</p>
    {/if}
{/capture}
{capture name="quantity"}
    <div class="inline-block middle">{__('amount')} :</div>
    <div class="changer inline-block middle">
        <input type="number" class="form-control input-text-short js-quantity-modifier test-basket-item-quantity" value="{$quantity}" data-product-id="{$data.objectID}" data-old-value="{$quantity}" />
    </div>
{/capture}
<div class="row basket-declination test-basket-item" id="js-declination-{$data.objectID}">
    <div class="hidden-xs js-attr-container">
        <div class="col-xs-4 col-sm-2 inline-block middle js-attr-image">
            {$smarty.capture.image}
        </div><!-- inline-block fix
     --><div class="col-xs-8 col-sm-6 inline-block middle">
            <span class="js-attr-name">{$data.product_name|strip_tags|truncate:150}</span><br>
            <br>
            {$smarty.capture.stock}
        </div><!-- inline-block fix
     -->{if !$hide_quantity}<div class="col-xs-6 col-sm-2 inline-block middle">
            {$smarty.capture.quantity}
        </div>{/if}<!-- inline-block fix
     --><div class="col-xs-6 col-sm-{if !$hide_quantity}2{else}4{/if} inline-block middle">
            <div class="inline-block">
                {$smarty.capture.price}
            </div>
        </div>
    </div>
    <div class="visible-xs">
        <div class="col-xs-4">
            {$smarty.capture.image}
        </div>
        <div class="col-xs-7 col-xs-offset-1">
            <div class="row">
                {if !$hide_quantity}
                    <div class="col-xs-12">
                        {$smarty.capture.quantity}
                    </div>
                {/if}
                <div class="col-xs-12">
                    <div class="inline-block">
                        {$smarty.capture.price}
                    </div>
                </div>
            </div>
        </div>
        <div class="clearfix"></div>
        <div class="col-xs-12 col-sm-6">
            {$data.product_name|strip_tags|truncate:60}
            <br>
            {$smarty.capture.stock}
        </div>
    </div>
</div>
