{extends file='layout.tpl'}

{block name="metas__additional-metas"}<meta name="robots" content="noindex">{/block}

{block name="content"}

    {include file="views/basket/commons/breadcrumb.tpl"}

    <script type="text/javascript">
        $(function () {
            $(".breadcrumb-panier li:nth-child(1)").addClass("breadcrumb-actif");
        });
    </script>

    <div class="row" id="js-full-basket">
        {include file="views/basket/commons/full_view.tpl"}
    </div>

    <script type="text/template" id="remove-product-popin-template">
        {literal}{{=[[ ]]=}}{/literal}
        <div class="row" data-test-class="remove-product-confirmation-popin">
            <div class="col-xs-12">
                <p>{__('w_remove_item_from_cart')}</p>
                <div class="row basket-declination test-basket-item">
                    <div class="col-xs-4 col-sm-2 inline-block middle">[[& image]]</div><!-- inline-block fix
                 --><div class="col-xs-8 col-sm-6 inline-block middle">[[& name]]</div><!-- inline-block fix
                 --><div class="col-xs-6 col-sm-4 inline-block middle">
                        <div class="price-product">
                            <p><span class="price">[[& price]]</span></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xs-12">
                <button class="btn pull-left js-change-quantity" data-product-id="[[product_id]]" data-quantity="[[old_quantity]]" data-dismiss="modal">{__("cancel")}</button>
                <button class="btn-primary pull-right js-change-quantity" data-product-id="[[product_id]]" data-quantity="0" data-dismiss="modal" data-test-class="validate-product-deletion-button">{__('confirm')}</button>
            </div>
        </div>

    </script>

    <div id="js-remove-product-popin" class="modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>{__('w_remove_item_from_cart_title')}</h2>
                </div>
                <div class="modal-body js-product-to-remove">

                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript">
        $(function(){

            var modifyQuantity = function(product_id, quantity) {
                W.toggleLoadingBox(true);

                $.ajax({
                    method: "POST",
                    url: "{url route='basket_modify_product_quantity'}",
                    data: {
                        product_id: product_id,
                        quantity: quantity
                    }
                }).done(function (data) {
                    reloadBasket(data);
                });
            };
            var $basket = $('#js-full-basket');
            var reloadBasket = function(data) {
                $basket.html(data);
                $(this).removeAttr('disabled');
                W.toggleLoadingBox(false);
                $.ajax({
                    url: "{url route='notifications_display'}"
                }).done(function (html) {
                    $(".notifications").replaceWith(html);
                });
            };

            $('.js-quantity-modifier').each(function(i, elt){
                $(elt).removeAttr('disabled');
                $(elt).val($(elt).attr('data-old-value'));
            });

            $basket.on('change', '.js-quantity-modifier', function() {
                if( $(this).attr('data-old-value') != $(this).val() ) {
                    var id = $(this).attr('data-product-id');
                    if ($(this).val() == 0) {
                        var $popin = $('#js-remove-product-popin');
                        var $product_content = $('#js-declination-'+id);
                        var $productTemplate = Hogan.compile($('#remove-product-popin-template').text());
                        var product_to_remove = $productTemplate.render(
                                {
                                    image           : $product_content.find('.js-attr-container .js-attr-image').html(),
                                    name            : $product_content.find('.js-attr-container .js-attr-name').html(),
                                    price           : $product_content.find('.js-attr-container .js-attr-reduced-price').html(),
                                    product_id      : id,
                                    old_quantity    : $(this).attr('data-old-value')
                                }
                        );
                        $popin.find('.js-product-to-remove').html(product_to_remove);
                        $popin.modal('show');
                        return false;
                    }
                    $(this).attr('disabled', 'disabled');
                    modifyQuantity(id, $(this).val());
                }
            });
            $('body').on('click', '.js-change-quantity', function() {
                modifyQuantity($(this).attr('data-product-id'), $(this).attr('data-quantity'));
            });
            $basket.on('change', '.js-switch-shipping', function() {
                W.toggleLoadingBox(true);
                $.ajax({
                    method: "POST",
                    url: "{url route='basket_select_shipping'}",
                    data: {
                        group_id: $(this).attr('data-group-id'),
                        shipping_id: $(this).val()
                    }
                }).done(function (data) {
                    reloadBasket(data);
                });
            });

        });
    </script>

    <script type="text/javascript">
        {* Tag commander variables *}
        analytics.pageCode = "basket";
    </script>

{/block}
