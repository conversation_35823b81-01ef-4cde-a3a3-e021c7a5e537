{if $basket && $basket.groups}
    {foreach $basket.groups as $group}
        {foreach $group.products as $basket_elt}
            {foreach $basket_elt.products as $product}
                <div class="content-panier">
                    <div>
                        {if $product.declination.image_id}
                            {run_controller controller="AppBundle:Image:display" image_id=$product.declination.image_id size_x=50 size_y=50}
                        {/if}
                    </div>
                    <a href="{$product.declination.url}">{$product.declination.product_name}</a>

                    <p>{$product.quantity} x {format_price price=$product.declination.reduced_price} {__('ttc')}</p>
                </div>
            {/foreach}
        {/foreach}
    {/foreach}

    <a href="{url route="basket_view"}" rel="nofollow"><button class="btn btn-third">{__('view_cart')}</button></a>
    <a href="{url route="basket_view"}" rel="nofollow"><button class="btn btn-primary">{__('w_command')}</button></a>

    <script type="text/javascript">
        {* Analytics variables *}
        analytics.order = {
            id: "{$basket['id']}",
            // Order amount TF (tax free) without shipping fee
            subtotal_tf: "{$basket['subtotal']->getConvertedAmount() - $basket['tax']}",
            subtotal_ati: "{$basket['subtotal']->getConvertedAmount()}",
            products: []
        };
        {foreach $basket.groups as $group}
            {foreach $group.products as $basket_elt}
                {foreach $basket_elt.products as $product}
                    analytics.order.products.push({
                        id: {$product['declination']['product_id']|json_encode nofilter},
                        name: {$product['declination']['product_name']|json_encode nofilter},
                        quantity: {$product['quantity']|json_encode nofilter},
                        price: {$product['declination']['reduced_price']|json_encode nofilter}
                    });
                {/foreach}
            {/foreach}
        {/foreach}
    </script>

{else}
    {__('empty')}
{/if}
