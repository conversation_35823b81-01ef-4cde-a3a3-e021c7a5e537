<div class="row">
    <div class="card-title">
        <h4>{{ 'shipping_method'|trans }}</h4>
    </div>
    <div class="col-md-9 col-xs-8">
        {%  for company_group in basket.groups %}
            <div class="card-content">
                <div class="row">
                    <div class="col-xs-12">
                        <strong>{{ 'vendor'|trans }} :</strong> {{ company_group.companyName }}
                    </div>
                </div>
                {% for id, shipping_group in company_group.products %}
                    <div class="row">
                        <div class="col-xs-6">
                            {{ 'shipping_method'|trans }} :
                        </div>
                        <div class="col-xs-6">
                            {% for id, shipping in shipping_group.shippings %}
                                {% if shipping.selected == true %}
                                    <span id="shipping-{{ id }}">{{ shipping.shippingName }} - {{ shipping.shippingPrice |price }}</span>
                                {% endif %}
                            {% endfor %}
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% endfor %}
    </div>
    <div class="card-button col-md-3 col-xs-4 pull-right text-right">
        <a class="btn pull-right" href="{{ path('basket_view') }}">
            {{ 'edit'|trans }}
        </a>
    </div>
</div>
