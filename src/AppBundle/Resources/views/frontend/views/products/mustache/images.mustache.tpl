{literal}{{=[[ ]]=}}{/literal}
<div class="js-images-cycle-container images-cycle-container[[^declination.images]][[^declination.video]] initialized[[/declination.video]][[/declination.images]]">
    <div class="js-vertical-pager-container vertical-pager-container">
        <div id="js-vertical-pager-tpl" class="hidden">
            [[#declination.images]]
                <img src="[[parameters.img_base_url]]&image_path=[[path]]&w=[[parameters.thumbnail_size]]&h=[[parameters.thumbnail_size]]"
                     alt="[[declination.product_name]] - {__("thumb")} [[id]]"/>
            [[/declination.images]]
            [[#declination.video]]
                <img src="[[thumb]]"
                     style="width: [[parameters.thumbnail_size]]px"
                     alt="[[declination.product_name]] - {__("thumb")}" />
            [[/declination.video]]
        </div>
    </div>
    <div class="js-images-cycle">
        [[^declination.images]]
            <img src="{asset name="images/no-image.jpg"}" />
        [[/declination.images]]
        [[#declination.images]]
            <div>
                <a href="[[parameters.img_base_url]]&image_path=[[path]]" class="fancybox" rel="gallery">
                    <img src="[[parameters.img_base_url]]&image_path=[[path]]&w=[[parameters.img_size]]&h=[[parameters.img_size]]" alt="[[declination.product_name]] - [[id]]"/>
                </a>
            </div>
        [[/declination.images]]
        [[#declination.video]]
            <div>
                <video class="video-js vjs-default-skin vjs-big-play-centered" width="635" height="638" controls preload="auto" data-setup="{}">
                    <source src="[[path]]" type="video/mp4" />
                </video>
            </div>
        [[/declination.video]]
    </div>
</div>
