{% block _form_category_id_widget %}
    {{ block('hidden_widget') }}

    <div class="row">
        <div class="col-xs-12 choose-categories">
            <div class="container-categories">
                <label for="picker-search-input">{{ 'w_search_category'|trans }} :</label>
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group">
                            <input id="picker-search-input" type="text" class="form-control" placeholder="{{ 'w_enter_category'|trans }}">
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="form-group">
                            <select class="form-control" id="picker-search-in-categories" onchange="search_category()">
                            </select>
                        </div>
                    </div>
                </div>
                <div id="picker-categories-tree" class="row">
                    <div class="col-sm-12">
                        <label>{{ 'w_browse_categories'|trans }} :</label>
                        <ul id="picker-root-category"></ul>
                    </div>
                </div>
                <ul id="picker-categories-search-results" style="display: none;">
                </ul>
                <hr/>
                <a class="categories-show js-switch btn btn-primary" href="#"  data-target-hide=".choose-categories">{{ 'w_validate'|trans }}</a>
            </div>
        </div>
    </div>

    <script type="text/template" id="subcategories-template">
        {% verbatim %}
        {{#data}}
        {{^is_garbage}}
        <li>
            {{^is_leaf}}
            <a href="#" class="button-level-categories no-underline" onclick="category_clicked(this); return false">
                <div class="arrow-category"></div>
                {{ name }}
            </a>
            <ul data-category-id="{{category_id}}" style="display: none;"></ul>
            {{/is_leaf}}
            {{#is_leaf}}
            <label class="radio-inline">
                <input type="radio"
                       name="{% endverbatim %}{{ form.vars.full_name }}{% verbatim %}"
                       value="{{category_id}}"
                       onclick='category_selected({
                               "name": "{{name}}",
                               "category_id": {{category_id}},
                               "is_transactional": {{is_transactional}}
                               })'
                >
                {{#full_name}}
                {{ full_name }}
                {{/full_name}}
                {{^full_name}}
                {{ name }}
                {{/full_name}}
            </label>
            {{/is_leaf}}
        </li>
        {{/is_garbage}}
    {{/data}}
    {% endverbatim %}
    </script>

    <script type="application/javascript">
        var template = Hogan.compile($("#subcategories-template").text());
        var input_category_name;
        var $category_search_input;
        var $category_tree;
        var $category_search_results;
        var $category_search_root;
        var search_index;

        function category_clicked(elmt)
        {
            toggle_category($(elmt).siblings('ul'));
        }

        function category_selected(data)
        {
            input_category_name
                    .val(data.name)
                    .trigger('change', data)
            ;
        }

        function toggle_category(elmt)
        {
            if( !elmt.text()) {
                var category_id = elmt.attr('data-category-id') || 0;
                W.toggleLoadingBox(true);
                $.getJSON(
                        "{{ url('picker_subcategories') }}?ajax_custom=1&category_id=" + category_id,
                        function(json) {
                            elmt.html(template.render(json));
                            W.toggleLoadingBox(false);
                            elmt.slideDown(300);

                            if(category_id == 0) {
                                $category_search_root.append(new Option("{{ 'all_categories'|trans }}", 0));
                                json.data.forEach(function(c) {
                                    $category_search_root.append(new Option(c.name, c.category_id));
                                });
                            }
                        }
                );
            } else {
                elmt.slideToggle(300);
            }
        }

        function search_category()
        {
            W.toggleLoadingBox(true);
            var text = $category_search_input.val();
            if(text) {
                $category_search_results.slideDown(300);
                $category_tree.slideUp(300);
                var params = {
                    tagFilters : $category_search_root.val() != 0 ? "pid_" + $category_search_root.val() : ""
                };
                search_index.search(
                        text,
                        function(success, content) {
                            if (!success || content.query !== text) {
                                console.log('Search failed');
                                return;
                            }
                            var data = { data: [] };
                            $.each(content.hits, function(index, hit) {
                                hit.category_id = hit.objectID;
                                hit.is_leaf = hit.height == 0;
                                hit.is_transactional = hit.transactional;
                                hit.full_name = hit.path.join(' > ');
                                data.data.push(hit);
                            });
                            $category_search_results.html(template.render(data));
                            W.toggleLoadingBox(false);
                        },
                        params
                );
            } else {
                $category_search_results.slideUp(300);
                $category_tree.slideDown(300);
                W.toggleLoadingBox(false);
            }
        }



        $(function(){
            toggle_category($('#picker-root-category'));
            input_category_name     = $("#{{ form.parent.category_name.vars.id }}");
            $category_search_input  = $("#picker-search-input");
            $category_search_results= $("#picker-categories-search-results");
            $category_tree          = $("#picker-categories-tree");
            $category_search_root   = $("#picker-search-in-categories");
            $categories_root        = $('.choose-categories');

            search_index = W.searchClient.initIndex(
                    '{{ indexCategories }}'
            );

            var keystroke_timeout = undefined;
            var last_search       = '';
            $category_search_input.on('keyup change', function () {
                if(last_search == this.value) {
                    return;
                }
                last_search = this.value;
                W.toggleLoadingBox(true);
                clearTimeout(keystroke_timeout);
                keystroke_timeout = setTimeout(search_category, 500);
            });

            var category_id = '{{ form.vars.value }}';
            if(category_id) {
                $.getJSON(
                        "{{ url('picker_category') }}?ajax_custom=1&category_id=" + category_id,
                        category_selected
                );
            }

            $categories_root.hover(
                    function () {
                        $(this).attr('data-hover', 'true');
                    },
                    function () {
                        $(this).attr('data-hover', 'false');
                    }
            );
            $('html').on('click', function(){
                if ($categories_root.attr('data-hover') != 'true') {
                    $categories_root.slideUp(300);
                }
            });
        });
    </script>
{% endblock %}
