{% block _form_session_images_row %}
    {% if form.vars.value %}
    {% set attr = attr|merge({ 'class': 'js-collection' }) %}
    <div {{ block('widget_container_attributes') }}>
        {{ form_label(form) }}
        {% if form.parent is null %}
            {{ form_errors(form) }}
        {% endif %}
        {% for children in form %}
            <div>
                {{ render(controller(
                    'AppBundle:Image:display',
                    {
                        'image_id': children.vars.value,
                        'size_x': 50,
                        'size_y': 50,
                    }
                )) }}
                {{ form_errors(children) }}
                {{ form_widget(children) }}
            </div>
        {% endfor %}
    </div>
    {% endif %}
{% endblock %}
