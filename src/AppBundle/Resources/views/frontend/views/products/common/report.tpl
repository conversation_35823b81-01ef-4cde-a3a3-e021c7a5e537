<div class="modal" id="report_pop_in">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h2>{__("report_content_title_popin")}</h2>
            </div>
            <div class="modal-body">
                <form action="{path route="product_report"}" method="post">
                    <input name="product_id" type="hidden" value="{$product_id}"/>

                    {if !$auth.user_id}
                        <div class="form-group">
                            <label class="required" for="name">{__("name")}</label>
                            <input required="required" id="name" name="name" type="text"
                                   class="form-control"/>
                        </div>
                        <div class="form-group">
                            <label class="required" for="first_name">{__("first_name")}</label>
                            <input required="required" id="first_name" name="first_name" type="text"
                                   class="form-control"/>
                        </div>
                        <div class="form-group">
                            <label class="required" for="email">{__("email")}</label>
                            <input required="required"
                                   data-validation="email"
                                   data-validation-error-msg="{__('error_validator_email', ['[field]' => __("email")])}"
                                   name="email"
                                   type="text"
                                   class="form-control"
                                   id="email"
                            />
                        </div>
                    {/if}
                    <div class="form-group">
                        <label class="required" for="message_content">{__("report_content_message")}</label>
                        <textarea required="required" id="message_content" name="message" cols="25" rows="7" class="form-control"></textarea>
                    </div>
                    {if !$auth.user_id}
                        <div class="form-group">
                            {include file="common/recaptcha.tpl" recaptcha_id='report'}
                        </div>
                    {/if}
                    <input class="btn" type="submit" value="{__('send')}" name="submit">
                </form>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    $(function() {
        $('#report_pop_in').appendTo('body');
    });
</script>
