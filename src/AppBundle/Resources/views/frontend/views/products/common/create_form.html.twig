{% set key_search_engine = constant('Wizacha\\Search\\Config::CFG_SEARCH_ENGINE') %}
{% set key_index_geocoding = constant('Wizacha\\Search\\Config::INDEX_GEOCODING') %}
{% set key_index_categories = constant('Wizacha\\Search\\Config::INDEX_CATEGORIES') %}
{% set index_geocoding = attribute(attribute(config, key_search_engine), key_index_geocoding) %}
{% set index_categories = attribute(attribute(config, key_search_engine), key_index_categories) %}
{% set attr_class_form_control = { 'attr': {'class': 'form-control'} } %}
{{ 'w_c2c_products_update_explanation'|trans|raw }}
{{ form_start(form) }}
{% form_theme form.category_id '@App/frontend/views/products/common/_form_category_id_widget.html.twig' %}
{{ form_row(form.category_id, {'indexCategories': index_categories}) }}
<fieldset>
    <h2>{{ 'c2c_product_type'|trans }}</h2>
    <div class="row">
        <div class="col-sm-8">
            <div class="form-group">
                {% form_theme form.category_name '@App/frontend/views/products/common/_form_category_name_widget.html.twig' %}
                {{ form_row(form.category_name, attr_class_form_control) }}
                <script type="text/javascript">
                    $(function(){
                        $transactional_include = $('.transactional-include');
                        $transactional_exclude = $('.transactional-exclude');
                        $('#{{ form.category_name.vars.id }}').change(
                                function(evnt, data) {
                                    load_category_features(data.category_id, $(".categories-features"));
                                    W.toggleInput($transactional_include, data.is_transactional);
                                    W.toggleInput($transactional_exclude, !data.is_transactional);
                                }
                        );

                    });
                </script>
            </div>
        </div>
    </div>
</fieldset>

<fieldset>
    <h2>{{ 'c2c_your_ad'|trans }}</h2>
    {% if form.company is defined %}
    <div class="row">
        <div class="col-sm-12">
            <div class="form-group">
                {{ form_row(form.company, attr_class_form_control) }}
            </div>
        </div>
    </div>
    {% endif %}
    <div class="row">
        <div class="col-sm-12">
            <div class="form-group">
                {{ form_row(form.product, attr_class_form_control) }}
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-12">
            <div class="form-group">
                {{ form_row(form.full_description, attr_class_form_control) }}
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-12">
            <div class="form-group image-collection" data-toggle="popover" title="{{ 'add_photos'|trans|e('html_attr') }}" data-content="{{ 'required_images'|trans|e('html_attr') }}">
                {% form_theme form.session_images '@App/frontend/views/products/common/_form_session_images_row.html.twig' %}
                {{ form_row(form.session_images) }}
                {{ form_row(form.images, { 'attr': {'class': 'js-collection'} }) }}
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-4">
            <div class="form-group">
                {{ form_row(form.price, attr_class_form_control) }}
            </div>
        </div>
        <div class="col-sm-4">
            <div class="form-group">
                {{ form_row(form.amount, attr_class_form_control) }}
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-8">
            <div class="form-group">
                {{ form_row(form.w_condition) }}
            </div>
        </div>
    </div>
</fieldset>

<fieldset class="transactional-include" style="display: none">
    <h2>{{ 'c2c_product_delivery'|trans }}</h2>
    <div class="row">
        <div class="col-sm-6 col-xs-12">
            {{ form_row(form.shipping_standardDelivery) }}
            <div class="form-group">
                {{ form_row(form.shipping_standardDelivery_price, attr_class_form_control) }}
            </div>
        </div>
        <div class="col-sm-6 col-xs-12">
            {{ form_row(form.shipping_handDelivery) }}
            <div class="form-group">
                {{ form_row(form.shipping_handDelivery_label, attr_class_form_control) }}
                {{ form_widget(form.shipping_handDelivery_postal) }}
                {{ form_widget(form.shipping_handDelivery_lat) }}
                {{ form_widget(form.shipping_handDelivery_lng) }}
            </div>
            <script type="text/javascript">
                $(function(){
                    var $geolocField = $("#{{ form.shipping_handDelivery_label.vars.id }}");

                    initGeoCoding(
                            W.searchClient.initIndex('{{ index_geocoding }}'),
                            $geolocField,
                            $("#{{ form.shipping_handDelivery_lat.vars.id }}"),
                            $("#{{ form.shipping_handDelivery_lng.vars.id }}"),
                            Hogan.compile($("#top-search-geoloc-suggestion-template").html()),
                            $("#{{ form.shipping_handDelivery_postal.vars.id }}")
                    );

                    var $ttWrapper = $geolocField.parent();
                    var $geoloc_tt_hint = $ttWrapper.find('.tt-hint');
                    var $bgColor = $geoloc_tt_hint.css('background-color');

                    $ttWrapper.css({ display: 'block' });
                    $geolocField.on('disabled:removed', function(){
                        $geoloc_tt_hint.removeAttr('disabled').css({ backgroundColor:'transparent' });
                    }).on('disabled:added', function(){
                        $geoloc_tt_hint.attr('disabled', 'disabled').css({ backgroundColor: $bgColor });
                    });

                });
            </script>
        </div>
    </div>
</fieldset>

<fieldset class="transactional-exclude" style="display: none">
    <h2>{{ 'w_localization'|trans }}</h2>
    <div class="row">
        <div class="col-sm-8">
            <div class="form-group">
                {{ form_row(form.geoloc_label, attr_class_form_control) }}
                {{ form_widget(form.geoloc_postal) }}
                {{ form_widget(form.geoloc_lat) }}
                {{ form_widget(form.geoloc_lng) }}
            </div>
        </div>
        <script type="text/javascript">
            $(function(){
                var $geolocField = $("#{{ form.geoloc_label.vars.id }}");
                initGeoCoding(
                        W.searchClient.initIndex('{{ index_geocoding }}'),
                        $geolocField,
                        $("#{{ form.geoloc_lat.vars.id }}"),
                        $("#{{ form.geoloc_lng.vars.id }}"),
                        Hogan.compile($("#top-search-geoloc-suggestion-template").html()),
                        $("#{{ form.geoloc_postal.vars.id }}")
                );
                $geolocField.parent().css({ display: 'block' });
            });
        </script>
    </div>
</fieldset>

<fieldset class="categories-features" style="display:none;">
    <h2>{{ 'c2c_product_features'|trans }}</h2>
    <div class="row">
        <div class="col-sm-8">
            {{ form_row(form.features, {'productId':form.product_id.vars.value}) }}
        </div>
    </div>
</fieldset>

<input type="submit" class="publier-annonce btn-primary" value="{{ 'c2c_publish_ad'|trans }}">


{{ form_end(form) }}

<script>
    $(function() {
        $('form').submit(function (event){
            //Empecher la soumission du formulaire s'il n'y pas d'images
            if ($('#form_images input[type=file]').length == 0 && $('#form_session_images input[type=hidden]').length == 0) {

                $('html, body').animate({scrollTop: $('.image-collection').offset().top}, 500);
                window.setTimeout(
                        function() {
                            $('.image-collection').popover();
                            $('.image-collection').popover('show');
                        },
                        600
                );
                event.preventDefault();
            }
        })
    });

</script>
