{$options = []}
{foreach $declinations as $declination}
    {foreach $declination.declination as $option}
        {$options[$option.option_id]['option_id'] = $option.option_id}
        {$options[$option.option_id]['option_name'] = $option.option_name}
        {$options[$option.option_id]['values'][$option.value_name]['value_id'] = $option.value_id}
        {$options[$option.option_id]['values'][$option.value_name]['value_name'] = $option.value_name}
    {/foreach}
{/foreach}

{foreach $options as $option}
    <div class="options-wrapper">
        <label for="option_{$option.option_id}">{$option.option_name}</label>
        <select name="product_data[{$product.product_id}][product_options][{$option.option_id}]" id="option_{$option.option_id}" class="form-control js-declinations-switch" data-option-id="{$option.option_id}">
            {$values = $option.values}
            {foreach $values as $value}
                <option {if $value.value_id eq $product.declination[$option.option_id].value_id}selected="selected" {/if}value="{$value.value_id}" data-label="{$value.value_name}">{$value.value_name}</option>
            {/foreach}
        </select>
    </div>
{/foreach}
