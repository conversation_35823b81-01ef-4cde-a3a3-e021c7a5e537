{% extends '@App/frontend/layout.html.twig' %}

{% block metas_page_title %}<title>{{ 'w_vendor_registration_title'|trans }}</title>{% endblock %}

{% block content %}
<div class="container">
    <h1 class="mainbox-title">{{ 'w_vendor_registration_title'|trans }}</h1>
    <div class="mainbox-body">
    <div class="row">
    <div id="apply_for_vendor_form">
    <p class="MFont">{{ 'w_please_send_elements'|trans }}</p>
    <form name="vendor_apply_form" enctype="multipart/form-data" class="form-horizontal" method="post">
        <div class="col-sm-6">
            <fieldset>
                <h2>{{ 'w_your_store'|trans }}</h2>
                <div class="row">
                    <div class="form-group">
                        <label class="col-sm-3 control-label required" for="store_name">{{ 'w_store_name'|trans }}</label>
                        <div class="col-sm-6">
                            <input type="text" name="company_data[company]" id="store_name" value="{{ company.company | default }}" class="form-control" size="50" required />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="cm-trim col-sm-3 control-label required" for="email">{{ 'email'|trans }}</label>
                        <div class="col-sm-6">
                            <input type="email" name="company_data[email]" id="email" value="{{ company.email | default }}" class="form-control" size="50" required />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label required" for="address">{{ 'address'|trans }}</label>
                        <div class="col-sm-6">
                            <textarea style="height:35px;" name="company_data[address]" id="address" class="form-control" rows="2" size="50" required>{{ company.address | default }}</textarea>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label required" for="zipcode">{{ 'zip_postal_code'|trans }}</label>
                        <div class="col-sm-6">
                            <input type="text" name="company_data[zipcode]" id="zipcode" value="{{ company.zipcode | default }}" class="form-control" size="50" required />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label required" for="city">{{ 'city'|trans }}</label>
                        <div class="col-sm-6">
                            <input type="text" name="company_data[city]" id="city" value="{{ company.city | default }}" class="form-control" size="50" required />
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="elm_company_country" class="control-label col-sm-3 control-label">{{ 'country'|trans }}:</label>
                        {% set selectedCountryCode = company.country | default(default_country) %}
                        <div class="col-sm-6">
                            <select class="cm-country form-control" style="height:35px;" id="elm_company_country" name="company_data[country]">
                                <option value="">- {{ 'select_country'|trans }} -</option>
                                {% for code, country in countries %}
                                    <option {% if selectedCountryCode == code %}selected="selected"{% endif %} value="{{ code }}">{{ country }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="apply_message" class="col-sm-3 control-label">{{ 'your_message'|trans }}</label>
                        <div class="col-sm-6">
                            <textarea name="company_data[message]" id="apply_message" class="form-control" rows="10">{{ company.message | default }}</textarea>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-xs-12">
                            <label>
                                <input type="checkbox" name="terms_approved" id="terms_approved" value="1" {% if mandatory_terms %}required{% endif %}/>
                                <span class="required">{{ terms_label | raw }}</span>
                            </label>
                        </div>
                    </div>
                </div>
            </fieldset>
            <input type="submit" value="{{ 'w_apply_for_vendor_button_text'|trans }}" class="btn btn-primary" id="apply_but" />
        </div>
        <div class="col-sm-6">
            <fieldset>
                <h2>{{ 'w_store_administrator'|trans }}</h2>
                <div class="form-group">
                    <label class="col-sm-3 control-label required" for="firstname">{{ 'first_name'|trans }}</label>
                    <div class="col-sm-6">
                        <input type="text" name="company_data[admin_firstname]" id="firstname" value="{{ company.admin_firstname | default }}" class="form-control" size="50" required />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label required" for="lastname">{{ 'last_name'|trans }}</label>
                    <div class="col-sm-6">
                        <input type="text" name="company_data[admin_lastname]" id="lastname" value="{{ company.admin_lastname | default }}" class="form-control" size="50" required />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label required" for="phone">{{ 'phone'|trans }}</label>
                    <div class="col-sm-6">
                        <input type="text" name="company_data[phone]" id="phone" value="{{ company.phone | default }}" class="form-control" size="50" required />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label required" for="b_company">{{ 'company'|trans }}</label>
                    <div class="col-sm-6">
                        <input type="text" name="user_data[b_company]" value="{{ user.b_company | default }}" id="b_company" class="form-control" size="50" required />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label required" for="w_legal_status">{{ 'vendor_w_legal_status'|trans }}</label>
                    <div class="col-sm-6">
                        <input type="text" name="company_data[w_legal_status]"  value="{{ company.w_legal_status | default }}" id="w_legal_status" class="form-control" size="50" required />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label required" for="w_capital">{{ 'w_vendor_capital'|trans }}</label>
                    <div class="col-sm-6">
                        <input type="text" name="company_data[w_extras][w_capital]"  value="{{ company.w_extras.w_capital | default }}" id="w_capital" class="form-control" size="50" required />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label required" for="w_siret_number">{{ 'vendor_w_siret_number'|trans }}</label>
                    <div class="col-sm-6">
                        <input type="text" name="company_data[w_siret_number]" value="{{ company.w_siret_number | default }}" id="w_siret_number" class="form-control" size="19" required />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label required" for="w_rcs">{{ 'w_vendor_rcs'|trans }}</label>
                    <div class="col-sm-6">
                        <input type="text" name="company_data[w_extras][w_RCS]" value="{{ company.w_extras.w_RCS | default }}" id="w_rcs" class="form-control" size="19" required />
                    </div>
                </div>
                <div  class="form-group">
                    <label class="col-sm-3 control-label" id="label_w_vat_number" for="applier_w_vat_number">{{ 'vendor_w_vat_number'|trans }}</label>
                    <div class="col-sm-6">
                        <span id="div_input_vat" ><input type="text" name="company_data[w_vat_number]" value="{{ company.w_vat_number | default }}" id="applier_w_vat_number" class="form-control" size="50" /></span>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-sm-9 col-sm-offset-3">
                        <label class="no-padding justify" for="sw_div_input_vat">
                            <input data-ca-target="label_w_vat_number" class="cm-change-required cm-switch-inverse cm-switch-availability" type="checkbox" name="w_independent_vendor" id="sw_div_input_vat" />
                            {{ 'w_vendor_independent_declaration'|trans }}
                        </label>
                    </div>
                </div>
                {{ 'w_upload_max_size'|trans }} : <b>{{ global.uploadMaxFileSize }}MB</b>
                <div class="form-group">
                    <label class="col-sm-3 control-label required" for="applier_id">{{ 'vendor_w_id_card'|trans }}</label>
                    <div class="col-sm-6">
                        <input type="file" name="w_ID_card" id="applier_id" class="input-file" required />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label required" for="applier_kbis">{{ 'vendor_w_kbis'|trans }}</label>
                    <div class="col-sm-6">
                        <input type="file" name="w_KBIS" id="applier_kbis" class="input-file" required />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label required" for="applier_rib">{{ 'vendor_w_rib'|trans }}</label>
                    <div class="col-sm-6">
                        <input type="file" name="w_RIB" id="applier_rib" class="input-file" required />
                    </div>
                </div>
            </fieldset>
        </div>
    </form>
    </div>
    </div>
    </div>
</div>

{% endblock %}
