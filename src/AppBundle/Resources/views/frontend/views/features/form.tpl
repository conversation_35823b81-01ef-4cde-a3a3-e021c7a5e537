{* Recursive function to be able to render groups and sub-features *}
{function render_feature}
    {if $feature.feature_type != "G"}
        <div class="form-group {if $feature.position > 100}additional-feature hidden{/if}">
            <label class="{$w_meta_class} {if $feature.is_required == 'Y'}required{/if}" for="feature_{$feature_id}">{$feature.description}</label>
            {if $feature.feature_type == "E"}

                {render_twig path="@App/frontend/views/features/type/brand.html.twig" params=[
                    "featureId" => $feature_id,
                    "inputNamePrefix" => $input_name_prefix,
                    "feature" => $feature,
                    "productFeaturesCurrents" => $product_features_currents
                ]}

            {elseif $feature.feature_type == "S" || $feature.feature_type == "N"}
                {$selected_id=0}
                {if isset($product_features_currents['product_features'][$feature_id])}
                    {$selected_id=$product_features_currents['product_features'][$feature_id]}
                {/if}
                <div class="row">
                    <div class="col-xs-3">
                        <select
                            name="{$input_name_prefix}[product_features][{$feature_id}]"
                            {if $feature.is_required == 'Y'}required{/if}
                            class="form-control"
                        >
                            <option value="">-{__("none")}-</option>
                            {foreach from=$feature.variants item="var"}
                                <option
                                        value="{$var.variant_id}"
                                        {if $var.variant_id == $selected_id}selected="selected"{/if}
                                 >{$var.variant}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
            {elseif $feature.feature_type == "M"}
                <input type="hidden" name="{$input_name_prefix}[product_features][{$feature_id}]" value="" />
                {foreach from=$feature.variants item="var"}
                <div class="checkbox">
                    {$checked=(isset($product_features_currents['product_features'][$feature_id][$var.variant_id]) && $product_features_currents['product_features'][$feature_id][$var.variant_id])}
                    <label for="variant_{$var.variant_id}">
                        <input
                                type="checkbox"
                                id="variant_{$var.variant_id}"
                                name="{$input_name_prefix}[product_features][{$feature_id}][{$var.variant_id}]"
                                {if $checked}checked="checked"{/if}
                                value="{$var.variant_id}"
                        />
                        {$var.variant}
                    </label>
                </div>
                {/foreach}
            {elseif $feature.feature_type == "C"}
                {$feature_value=$product_features_currents['product_features'][$feature.feature_id]}
                <label class="checkbox">
                    <input type="hidden" name="{$input_name_prefix}[product_features][{$feature_id}]" value="N" />
                    <input
                            type="checkbox"
                            name="{$input_name_prefix}[product_features][{$feature_id}]"
                            value="Y"
                            id="feature_{$feature_id}"
                            {if $feature_value == "Y"}checked="checked"{/if}
                            {if $feature.is_required == 'Y'}required{/if}
                            />
                    {$feature.description}
                </label>
            {elseif $feature.feature_type == "O" || $feature.feature_type == "T"}
                {$feature_value=$product_features_currents['product_features'][$feature.feature_id]}
                <input type="text"
                       name="{$input_name_prefix}[product_features][{$feature_id}]"
                       {if $feature.is_required == 'Y'}required{/if}
                       value="{($feature.feature_type == "O") ? (($feature_value != "") ? ($feature_value|floatval) : '') : $feature_value}"
                       id="feature_{$feature_id}"
                       class="form-control {if $feature.feature_type == "O"} cm-value-decimal{/if}"
                />
            {/if}
        </div>
    {else}
        {* Group *}
        <h3>{$feature.description}</h3>
        {foreach from=$feature.subfeatures item=subfeature key="subfeature_id"}
            {* nocache seems to be necessary because of a Smarty bug... *}
            {nocache}
                {render_feature feature=$subfeature feature_id=$subfeature_id}
            {/nocache}
        {/foreach}
    {/if}
{/function}

{foreach from=$product_features item=feature key="feature_id"}
    {render_feature feature=$feature feature_id=$feature_id}
{/foreach}

<a class="action-display-additional-features hidden">{__("display_more_product_features")}</a>
<script type="text/javascript">
    $(function(){
        $('body').on('click', '.action-display-additional-features', function(e) {
            $('.additional-feature').removeClass('hidden');
            $('.action-display-additional-features').hide();
            e.preventDefault();
        });

        {* Show link with JS rather than smarty beacause of bug with global variables in Smarty (Global? Because of render_feature function) *}
        if ($('.additional-feature').length) {
            $('.action-display-additional-features').removeClass('hidden');
        }
    })
</script>
