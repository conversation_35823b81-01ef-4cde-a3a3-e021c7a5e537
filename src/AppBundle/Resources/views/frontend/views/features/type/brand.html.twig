<input type="hidden" name="{{ inputNamePrefix }}[product_features][{{ featureId }}]" />
<input type="text"
    {% if feature.is_required == 'Y' %} required {% endif %}
       class="typeahead form-control" id="feature_{{featureId}}_input"
       name="{{ inputNamePrefix }}[add_new_variant][{{ feature.feature_id }}][variant]"
       {% if productFeaturesCurrents['add_new_variant'][featureId]['variant'] is defined
            and productFeaturesCurrents['add_new_variant'][featureId] %}
           value="{{ productFeaturesCurrents }}['add_new_variant'][{{ featureId }}]['variant']"
        {% endif %}
>

<script>
    var feature_{{feature.feature_id}}_variants_provider = new Bloodhound({
        local: {{feature.variants_js_table|json_encode|raw}},
        queryTokenizer: Bloodhound.tokenizers.whitespace,
        datumTokenizer: Bloodhound.tokenizers.whitespace
    });

    feature_{{ feature.feature_id }}_variants_provider.initialize();

    $('#feature_{{ feature.feature_id }}_input').typeahead({
                hint: true,
                highlight: true,
                minLength: 1
            },
            {
                display: function(data) { return data; },
                source: feature_{{ feature.feature_id }}_variants_provider.ttAdapter()
    });
</script>
