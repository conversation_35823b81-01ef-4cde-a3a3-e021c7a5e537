{if $posts}
    <dl class="customer-review">
     {foreach $posts as $post}
         <dt class="customer-review-title">{$post->getName()} - <span class="customer-review-info inline-block"><input value="{$post->getRatingValue()}" disabled="disabled" class="js-rating" data-size="xs" data-show-caption="false" /></span></dt>
         <dd class="customer-review-message">{$post->getMessage()}</dd>
         <hr/>
    {/foreach}
    </dl>
{else}
    {__("no_posts_found")}
{/if}
{if $thread->getId()}
<form action="{""|fn_url}" method="post" class="form-horizontal posts-form" name="add_post_form" id="add_post_form_{$obj_prefix}{$obj_id}">
    <input type ="hidden" name="post_data[thread_id]" value="{$thread->getId()}" />
    <input type ="hidden" name="redirect_url" value="{'https://'|cat:$smarty.server.HTTP_HOST|cat:$smarty.server.REQUEST_URI}" />

    <div class="row">
        <div class="form-group">
            <label for="pseudo_{$obj_prefix}{$obj_id}" class="required control-label col-sm-2">{__("pseudo")}</label>
            <div class="col-sm-5">
                <input type="text" id="pseudo_{$obj_prefix}{$obj_id}" name="post_data[name]"  size="50" class="form-control" />
            </div>
        </div>

        <div class="form-group">
            {$rate_id = "rating_`$obj_prefix``$obj_id`"}
            <label for="{$rate_id}" class="required control-label col-sm-2">{__("your_rating")}</label>
            <div class="col-sm-10">
                <input name="post_data[rating_value]" value="3" class="js-rating" />
            </div>
        </div>

        <div class="form-group">
            <label for="message_{$obj_prefix}{$obj_id}" class="required control-label col-sm-2">{__("your_message")}</label>
            <div class="col-sm-5">
                <textarea id="message_{$obj_prefix}{$obj_id}" name="post_data[message]" class="form-control" rows="5" cols="72">{$discussion.post_data.message}</textarea>
            </div>
        </div>
    </div>

    <input type="submit" class="btn btn-primary col-sm-offset-2" name="dispatch[discussion.add]" value="{__('send')}" />
</form>
{/if}
