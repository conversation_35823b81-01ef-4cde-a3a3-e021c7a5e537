{assign var='home_url' value=fn_url()}
{nocache}
    {$current_url = $smarty.server.REQUEST_URI}
{/nocache}
<div id="connect-form">
    <div class="login">
        <form name="checkout_login_form" action="{$home_url}" method="post">
            <input type="hidden" name="return_url" value="{$current_url}" />
            <input type="hidden" name="redirect_url" value="{$current_url}" />

            <div class="dropdown__title">
                {__('returning_customer')}
            </div>
            <div class="form-group">
            <input
                    type="text"
                    name="user_login"
                    size="30"
                    value=""
                    class="form-control"
                    data-validation="email"
                    data-validation-error-msg="{__('error_validator_email')}"
                    placeholder="{__('email')}"
                    />
            </div>
            <div class="form-group">
            <input
                    type="password"
                    id="psw_checkout_login"
                    name="password"
                    size="30"
                    value=""
                    class="form-control"
                    maxlength="32"
                    placeholder="{__('password')}"
                    />
            </div>
            <br />

            <input type="hidden" name="remember_me" value="1"/>
            <div class="row">
                <div class="col-md-6">
                    <button class="btn-primary" name="dispatch[auth.login]">{__('sign_in')}</button>
                </div>
                <div class="col-md-6">
                    <a href="{path route='recover_password'}" rel="nofollow" class="forgot-password"><small>{__("forgot_password_question")}</small></a>
                </div>
            </div>

        </form>

        <hr/>
        <button class="btn js-switch" data-target-show=".register" data-target-hide=".login">{__('register')}</button>
    </div>

    <div class="register" style="display: none;">
        <button class="btn js-switch" data-target-show=".login" data-target-hide=".register">{__('sign_in')}</button>
        <hr/>
        <form name="register_form"
              action="{$home_url}" method="post">
            <input type="hidden" name="redirect_url" value="{$current_url}" />
            <div class="form-group">
            <input
                    type="text"
                    id="email"
                    name="user_data[email]"
                    size="30"
                    maxlength="128"
                    value=""
                    class="form-control"
                    tabindex="10"
                    data-validation="email"
                    data-validation-error-msg="{__('error_validator_email')}"
                    placeholder="{__('email')}"
                    />
            </div>
            <div class="form-group">
            <input
                    type="password"
                    id="password_3"
                    name="user_data[password1]"
                    size="30"
                    class="form-control"
                    data-validation="length"
                    data-validation-length="min5"
                    data-validation-error-msg="{__('error_password_min_symbols', ['[number]' => '5'])}"
                    tabindex="11"
                    placeholder="{__('password')}"
                    />
            </div>
            <div class="form-group">
                <label><input type="checkbox" required="required" name="terms_approved" value="1">{__('register_accept_terms', ['[url]' => 'pages.view?page_id=12'|fn_url]) nofilter}</label>
            </div>
            {if !$recaptcha_number}
                {assign var="recaptcha_number" value=uniqid()}
            {/if}
            {include file='common/recaptcha.tpl' recaptcha_id="header_create_profile`$recaptcha_number`"}
            <button class="btn-primary" type="submit" name="dispatch[profiles.update]">{__('register')}</button>
        </form>
    </div>
</div>
