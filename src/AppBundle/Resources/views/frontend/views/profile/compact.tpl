<ul>
    <li class="user-name">{$user->getFirstname()} {$user->getLastname()}</li>
    <li><a href="{fn_url('profiles.update')}" rel="nofollow">{__('profile_details')}</a></li>
    <li><a href="{fn_url('discuss.list')}" rel="nofollow">{__('discuss_menu')}</a></li>
    <li><a href="{fn_url('orders.search')}" rel="nofollow">{__('w_my_purchases')}</a></li>
    {feature_flag feature="enable_c2c"}
        <li><a href="{fn_url('c2c_products.manage')}" rel="nofollow">{__('w_c2c_vendor_profile')}</a></li>
    {/feature_flag}
    <li><a href="{fn_url('auth.logout')}" rel="nofollow">{__('sign_out') nofilter}</a></li>
</ul>

<script type="text/javascript">
    analytics.user = {
        id: "{$user->getId()}",
        email: "{$user->getEmail()}",
        hasOrders: {$user->hasOrders()|json_encode nofilter}
    };
</script>
