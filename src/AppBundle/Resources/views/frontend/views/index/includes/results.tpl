<!-- Vignettes -->
<div class="hidden js-search-result-block-title"><div class="search-results__title">{__('w_products_with_rmp')}</div></div>
<div id="hits-geo" class="row"></div>
<div class="hidden js-search-result-block-title"><div class="search-results__title">{__('w_products_with_delivery')}</div></div>

<div id="hits" class="row test-product-list">
    {* Products *}
</div>

<ul id="pagination" class="pagination"></ul>
<p class="no-items" id="search_engine_no_results" style="display:none;">{__("no_items_found")}</p>
<!-- Hit template -->
<script type="text/template" id="hit-template">
    {literal}{{=[[ ]]=}}{/literal}
    <div class="col-md-4 col-sm-6 col-xs-12">
        <article class="product-thumb js-article-[[& {'Wizacha\Search\Record\Product::KEY_OBJECT_ID'|constant} ]]">
            <a href="[[& {'Wizacha\Search\Record\Product::KEY_URL'|constant} ]]" class="no-underline" data-test-class="search-result">
                <div class="container-annonce">
                    <div class="content-annonce-image">
                        <img src="[[& {'Wizacha\Search\Record\Product::KEY_IMAGE_URL'|constant} ]]" alt="[[& {'Wizacha\Search\Record\Product::KEY_NAME'|constant} ]]">
                    </div>
                    <div class="content-annonce">
                        <h3 class="product-name">[[& {'Wizacha\Search\Record\Product::KEY_NAME'|constant} ]]</h3>
                        <div class="price">
                            <p>[[& {'Wizacha\Search\Record\Product::KEY_PRICE'|constant} ]] {currency_sign}</p>
                        </div>
                        <div class="old-price"></div>
                    </div>
                </div>
            </a>
        </article>
    </div>
</script>

<!-- Stats template -->
<script type="text/template" id="stats-template">
    {__('w_products_search_stats_light', ['{{ nbHits }}'])}
</script>
