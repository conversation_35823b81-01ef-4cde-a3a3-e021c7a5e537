<div class="hidden-xs col-sm-3">
    <div id="bloc-filtre">
        <div class="title-filter-colonne">
            <p>{__('w_refine')}</p>
        </div>

        <ul>
            <li id="js-facet-main_category">
                <div class="tab-filtre active">
                    {__('category')}
                </div>
                <div class="container-filtre">
                    <ul data-preserve='[&#34;category_1&#34;, &#34;category_2&#34;, &#34;main_category&#34;]'></ul>
                </div>
            </li>
            <li class="js-geoloc-content hidden">
                <div class="tab-filtre js-geoloc-checkbox">
                    {__('w_localization')}
                </div>
                <div class="container-filtre">
                    {nocache}
                        <input type="text" name="geoloc" value="{json_value json=$smarty.request.geoloc key='localization' default=$smarty.request.geoloc}" class="js-geoloc-input form-control" placeholder="{__('w_top-search_placeholder_geoloc-input')}" tabindex="2" autocomplete="off" />
                    {/nocache}
                    <p><strong>{__('w_extend_search_distance')}</strong></p>
                    <ul class="js-geoloc-distance">
                        <li class="checkbox-secondaire" data-value="15000">
                            15km
                        </li>
                        <li class="checkbox-secondaire" data-value="50000">
                            50km
                        </li>
                        <li class="checkbox-secondaire" data-value="100000">
                            100km
                        </li>
                    </ul>
                    <div class="js-include-delivery checkbox-secondaire yesboxmini" onclick="deliveryClicked(this)">
                        {__('w_include_products_with_delivery')}
                    </div>
                </div>
            </li>
            <div id="facets"></div>
        </ul>

        <!-- Menu template -->
        <script type="text/template" id="menu-template">
            {literal}{{=[[ ]]=}}{/literal}
            [[#facet_{'Wizacha\Search\Record\Product::KEY_PRICE'|constant}]]
            <li>
                <div class="tab-filtre">
                    [[ title ]]
                </div>
                <div class="container-filtre" style="display: none;">
                    <div class="form-group">
                        <label for="price-min" class="inline-block">{__("from")}</label>
                        <input class="box-price form-control inline-block" type="text" id="price-min" placeholder="[[ stats.min ]]" />
                        <span class="inline-block">{currency_sign}</span>
                    </div>
                    <div class="form-group">
                        <label for="price-max" class="inline-block">{__("to")}</label>
                        <input type="text" class="box-price form-control inline-block" id="price-max" placeholder="[[ stats.max ]]" />
                        <span class="inline-block">{currency_sign}</span>
                    </div>
                </div>
            </li>
            [[/facet_{'Wizacha\Search\Record\Product::KEY_PRICE'|constant}]]

            [[#facet_{'Wizacha\Search\Record\Product::KEY_CONDITION'|constant}]]
                [[> facet ]]
            [[/facet_{'Wizacha\Search\Record\Product::KEY_CONDITION'|constant}]]

            [[#facets]]
                [[> facet ]]
            [[/facets]]

            {feature_flag feature="enable_c2c"}
                [[#facet_{'Wizacha\Search\Record\Product::KEY_VENDOR_TYPE'|constant}]]
                    [[> facet ]]
                [[/facet_{'Wizacha\Search\Record\Product::KEY_VENDOR_TYPE'|constant}]]
            {/feature_flag}

        </script>

        <!-- Facet template -->
        <script type="text/template" id="facet-template">
            {literal}{{=[[ ]]=}}{/literal}
            <li id="js-facet-[[ facet_id ]]" class="js-[[^is_special]]not-[[/is_special]]special">
                <div class="tab-filtre">
                    [[ title ]]
                </div>
                <div class="container-filtre" style="display: none;">
                [[#stats]]
                    <div class="form-group">
                        <label for="[[ facet_id ]]-min" class="inline-block">{__("from")}</label>
                        <input type="text" class="box-price form-control inline-block" id="[[ facet_id ]]-min" placeholder="[[ stats.min ]]" />
                    </div>
                    <div class="form-group">
                        <label for="[[ facet_id ]]-max" class="inline-block">{__("to")}</label>
                        <input type="text" class="box-price form-control inline-block" id="[[ facet_id ]]-max" placeholder="[[ stats.max ]]" />
                    </div>
                [[/stats]]
                [[^stats]]
                    <ul>
                        [[> facet_values]]
                    </ul>
                [[/stats]]
                </div>
            </li>
        </script>

        <!-- Facet template -->
        <script type="text/template" id="facet_values-template">
            {literal}{{=[[ ]]=}}{/literal}
            [[#values]]
                [[> value ]]
            [[/values]]
        </script>

        <!-- Value template -->
        <script type="text/template" id="value-template">
            {literal}{{=[[ ]]=}}{/literal}
            <li class="checkbox-secondaire[[#refined]] yesboxmini[[/refined]]" onclick='[[#is_category]]facet_search.categoryMultivalueChange(this); [[/is_category]]toggleRefine("[[ facet_name ]]", "[[ label ]]"); return false;'>
                [[#is_empty]]
                    {{__('empty_facet_value')}}
                [[/is_empty]]
                [[^is_empty]]
                    [[ label ]]
                [[/is_empty]]
                ([[ count ]])
            </li>
        </script>

    </div>
</div>
