<script type="text/javascript">
    $(function () {
        facet_search = new FacetSearch(
            $,
            W,
            {
                index_name: '{$config['Wizacha\Search\Config::CFG_SEARCH_ENGINE'|constant]['Wizacha\Search\Config::INDEX_PRODUCTS'|constant]}_fr',
                tag_filters: '{$tag_id}',
                filters: {$filters|json_encode nofilter},
                empty_value: '{'Wizacha\Search\Record\Product::FEATURE_EMPTY_VALUE'|constant}',
                path_prefix: {[
                    'http' => $config.http_path,
                    'https' => $config.https_path
                ]|json_encode nofilter},
                geoloc: {
                    tag: '{'Wizacha\Search\Record\Product::TAG_GEOLOC'|constant}',
                    localization: $('#geoloc_input'),
                    lat: $("#js-geoloc-input-latitude"),
                    lng: $("#js-geoloc-input-longitude"),
                    extraContent: $('.js-geoloc-content')
                },
                {$specials=[
                    'Wizacha\Search\Record\Product::KEY_PRICE'|constant        => __('price'),
                    'Wizacha\Search\Record\Product::KEY_VENDOR_TYPE'|constant  => __('w_company_type'),
                    'Wizacha\Search\Record\Product::KEY_CONDITION'|constant    => __('w_product_condition'),
                    'category_1' => 'category_1',
                    'category_2' => 'category_2',
                    'category_3' => 'category_3',
                    'main_category' => 'main_category'
                ] }
            facets_enabled: true,
            facets_special: {
                {foreach $specials as $special => $title}
                    {$special} : {
                    target : "facet_{$special}",
                    title: {json_encode($title)}
                    }{if !$special@last},{/if}
                {/foreach}
            },
            facets_id: {
                main_category: 'main_category',
                    category_1: 'category_1',
                    category_2: 'category_2'
                },
                facets_persistent: {
                    disjunctive: [
                        '{'Wizacha\Search\Record\Product::KEY_CONDITION'|constant}',
                        '{'Wizacha\Search\Record\Product::KEY_VENDOR_TYPE'|constant}'
                    ],
                    numeric: ['{'Wizacha\Search\Record\Product::KEY_PRICE'|constant}']
                },
                pagination: {
                    first: '{__('w_first_page')}',
                    last : '{__('w_last_page')}',
                    prev : '<i class="text-arrow">&larr;</i> {__('prev')}',
                    next : '{__('next')} <i class="text-arrow">&rarr;</i>'
                },
                categories_label: '{__('all_categories')}'
            }
        );
    });
</script>
