{extends file='layout.tpl'}

{block name="metas__page-title"}
    {if $seo_data && $seo_data->getTitle()}
        <title>{$seo_data->getTitle()}</title>
    {elseif $category}
        <title>{__("w_default_category_page_title", ['[category_name]' => $category->getName()])}</title>
    {else}
        {$smarty.block.parent}
    {/if}
{/block}
{block name="metas__description"}
    {if $seo_data && $seo_data->getDescription()}<meta name="description" content="{$seo_data->getDescription()}" />{else}{$smarty.block.parent}{/if}
{/block}
{block name="metas__keywords"}
    {if $seo_data && $seo_data->getKeywords()}<meta name="keywords" content="{$seo_data->getKeywords()}" />{else}{$smarty.block.parent}{/if}
{/block}

{block name="punch_line"}
    <div class="hidden-xs">
        <h1>{__('w_punch_line')}</h1>
        <div class="header__baseline--secondary">{__('w_punch_line2')}</div>
    </div>

{/block}

{block name="header"}
    <!-- HEADER -->
    <header class="home-header">
        {block name="header_content"}
            {include file="common/header.tpl" include_concept=true}
        {/block}
    </header>
    <!-- FIN HEADER -->
{/block}

{block name="header_content"}
    {$smarty.block.parent}
    <!-- header bottom home -->

{/block}

{block name="content"}
    <div class="container">
        {if $breadcrumbs}
            <ol class="breadcrumb">
                {foreach $breadcrumbs as $elt}
                    <li><a href="{$elt.link}">{$elt.name}</a></li>
                {/foreach}
            </ol>
        {/if}
        <div class="bloc-content" id="js-main-content">
            <div class="row">
                {if $vendor_description}
                    <div class="col-xs-12">
                        <div class="header-description">
                            {$vendor_description}
                        </div>
                    </div>
                {/if}
                {if $category && $category->getDescription()}
                    <div class="col-xs-12">
                        <div class="header-description category-description">
                            {$category->getDescription()}
                        </div>
                    </div>
                {/if}
                <!-- Barre filtre -->
                {include file='views/index/includes/filters.tpl'}

                <!-- Bloc Annonce-->
                <div class="col-sm-9 col-xs-12">
                    <div class="bloc-annonce">

                        <!-- Header Annonce -->
                        <div class="row">
                            <div class="visible-xs col-xs-12 ">
                                <div class="button-filtre-responsive">
                                    <button>Affiner la recherche </button>
                                </div>
                            </div>

                            <div class="col-xs-12">
                                <div class="header-annonce">
                                    <span id="stats"></span>
                                    <select id="nb_hits_per_page" class="">
                                        {for $step=2 to 0 step -1}
                                            {$nb_items = pow(2,$step)*12}
                                            <option value="{$nb_items}">{$nb_items} {__("per_page")}</option>
                                        {/for}
                                    </select>
                                </div>
                            </div>
                        </div>

                        {include file='views/index/includes/results.tpl'}

                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        analytics.pageCode = "homepage";
    </script>
    {include file='views/index/includes/search-config.tpl'}

{/block}
