{% if subscriptions.mailingLists %}

    <div id="mailingLists">
        {% for mailingList in subscriptions.mailingLists %}
            <div class="checkbox">
                <label>
                    <input type="checkbox" value="{{ mailingList.id }}" {% if subscriptions.isSubscribedTo(mailingList) %}checked="checked"{% endif %}><span></span>
                    <span class="newsletter-label">{{ mailingList.name }}</span>
                </label>
            </div>
        {% endfor %}
    </div>

    <div class="newsletter-block">{{ 'w_newsletter_reassurance'|trans }}</div>

    <script>
        $(function () {
            var hasFrontNotifications = typeof $.ceNotification != 'undefined';
            /**
             * Subscribe to a mailing list.
             */
            function subscribe(id) {
                $.post('{{ path('mailing_list_subscribe') }}', {mailingList: id, notify: !hasFrontNotifications}, function () {
                    if (hasFrontNotifications) {
                        $.ceNotification('show', {
                            type: 'N',
                            title: '{{ 'subscribed_success'|trans|e('js') }}',
                            message: ''
                        });
                    } else {
                        window.location.reload();
                    }
                });
            }
            /**
             * Unsubscribe from a mailing list.
             */
            function unsubscribe(id) {
                $.post('{{ path('mailing_list_unsubscribe') }}', {mailingList: id, notify: !hasFrontNotifications}, function () {
                    if (hasFrontNotifications) {
                        $.ceNotification('show', {
                            type: 'N',
                            title: '{{ 'unsubscribed_success'|trans|e('js') }}',
                            message: ''
                        });
                    } else {
                        window.location.reload();
                    }
                });
            }

            $('#mailingLists').on('click', 'input[type=checkbox]', function () {
                if ($(this).prop('checked')) {
                    subscribe($(this).val());
                } else {
                    unsubscribe($(this).val());
                }
            });
        });
    </script>

{% endif %}
