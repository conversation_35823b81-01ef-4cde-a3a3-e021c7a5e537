{% extends '@App/frontend/layout.html.twig' %}

{% block content %}

    <h1>{{ 'sign_in'|trans }}</h1>

    <div class="row">
        <div class="col-md-6 col-xs-12">
            <fieldset>
                <form action="{{ cscart_url('index.php?dispatch=profiles.update') }}" method="post" class="test-register">
                    <h2>{{ 'w_c2c_register_title'|trans }}</h2>
                    <div>
                        {{ 'w_c2c_register_promo_text'|trans|raw }}
                    </div>
                    <div class="row">
                        <div class="col-sm-7 col-xs-10">
                            <div class="form-group">
                                {{ form_row(register_form.user_data.email, {attr: { class: 'form-control'}}) }}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-7 col-xs-10">
                            <div class="form-group">
                                {{ form_row(register_form.user_data.password1, {attr: { class: 'form-control js-show-password'}}) }}
                            </div>
                        </div>
                    </div>
                    {% if register_form.user_data.c2c_vendor_pseudo is defined %}
                        <div class="row">
                            <div class="col-sm-7 col-xs-10">
                                <div class="form-group">
                                    {{ form_row(register_form.user_data.c2c_vendor_pseudo, {attr: { class: 'form-control'}}) }}
                                </div>
                            </div>
                        </div>
                    {% endif %}
                    {{ form_widget(register_form.return_url) }}
                    <div class="row">
                        <div class="col-sm-7 col-xs-10">
                            <div class="form-group">
                                <label>
                                    {{ form_widget(register_form.terms_approved) }}
                                    {% if register_form.user_data.c2c_vendor_pseudo is defined %}
                                        {{ 'register_company_accept_terms'|trans({'[usageUrl]': cscart_url('pages.view?page_id=12'), '[saleUrl]': cscart_url('pages.view?page_id=8')})|raw }}
                                    {% else %}
                                        {{ 'register_accept_terms'|trans({'[url]': cscart_url('pages.view?page_id=12')})|raw }}
                                    {% endif %}
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-group">
                                {{ render_smarty('common/recaptcha.tpl', { 'recaptcha_id':'register' }) }}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-group">
                                {{ form_widget(register_form.register, {attr: { class: 'btn-primary'}}) }}
                            </div>
                        </div>
                    </div>
                </form>
            </fieldset>
        </div>

        <div class="col-md-6 col-xs-12">
            <fieldset>
                <form action="{{ cscart_url('index.php?dispatch=auth.login') }}" method="post">
                    <h2>{{ 'w_c2c_login_title'|trans }}</h2>
                    <div>
                        {{ 'w_c2c_login_promo_text'|trans }}
                    </div>
                    <div class="row">
                        <div class="col-sm-7 col-xs-10">
                            <div class="form-group">
                                {{ form_row(login_form.user_login, {attr: { class: 'form-control'}}) }}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-7 col-xs-10">
                            <div class="form-group">
                                {{ form_row(login_form.password, {attr: { class: 'form-control'}}) }}
                            </div>
                        </div>
                    </div>
                    {{ form_widget(login_form.return_url) }}
                    {{ form_widget(login_form.redirect_url) }}
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-group">
                                {{ form_widget(login_form.sign_in, {attr: { class: 'btn-primary'}}) }}
                            </div>
                        </div>
                    </div>
                </form>
            </fieldset>
        </div>
    </div>

{% endblock %}
