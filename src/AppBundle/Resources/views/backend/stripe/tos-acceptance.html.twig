
    <div class="cm-tabs-content content-no-filter">

        <p>
            {{ 'stripe_tos_acceptance_header'|trans|nl2br }}
        </p>

        <form class="form-horizontal form-edit dropzone" id="stripe-tos-form" action="{{ path('admin_stripe_handle_tos_acceptance') }}" name="stripe-tos-form" method="post">
            <input type="hidden" name="account_token" id="stripe_account_token">
            <input type="hidden" name="person_token" id="stripe_person_token">
            <input type="hidden" name="_method" value="{{ method }}">

            <fieldset>
                <legend>{{ 'company'|trans }}</legend>

                <div class="control-group">
                    <label class="control-label">Type</label>
                    <div class="controls">
                        <label class="radio inline" for="elm_field_stripe_type_0">
                            <input id="elm_field_stripe_type_0" type="radio" name="business_type" value="company" disabled {% if company.professional %}checked{% endif %}>
                            {{ 'w_type_product_v'|trans }}
                        </label>
                        <label class="radio inline" for="elm_field_stripe_type_1">
                            <input id="elm_field_stripe_type_1" type="radio" name="business_type" value="individual" disabled {% if not company.professional %}checked{% endif %}>
                            {{ 'w_type_product_c'|trans }}
                        </label>
                    </div>
                </div>

                <div class="control-group">
                    <label class="control-label" for="elm_field_stripe_business_name">{{ 'stripe_business_name'|trans }}</label>
                    <div class="controls">
                        <input class="input-long" id="elm_field_stripe_business_name" name="name" type="text" value="{{ company.corporateName }}" disabled />
                    </div>
                </div>

                <div class="control-group">
                    <label class="control-label" for="elm_field_stripe_siren">{{ 'stripe_business_id'|trans }}</label>
                    <div class="controls">
                        <input class="input-long" id="elm_field_stripe_siren" name="business_id" type="text" value="{{ company.siretNumber|replace({' ': ''})|slice(0, 9) }}" disabled />
                    </div>
                </div>
                <div class="control-group">
                    <label class="control-label" for="elm_field_stripe_account_number">{{ 'account_number'|trans }}</label>
                    <div class="controls">
                        <input class="input-long" id="elm_field_stripe_account_number" name="account_number" type="text" value="{{ company.IBAN }}" disabled/>
                    </div>
                </div>

                <div class="control-group">
                    <label class="control-label" for="elm_field_stripe_telephone">{{ 'company_phone'|trans }}</label>
                    <div class="controls">
                        <input class="input-long" id="elm_field_stripe_telephone" name="phone" type="text" value="{{ company.phoneNumber }}" />
                        <span class="help-block">{{ 'company_phone_intl_format'|trans }}</span>
                    </div>
                </div>

                <div class="control-group">
                    <label class="control-label" for="elm_field_stripe_url">{{ 'url'|trans }}</label>
                    <div class="controls">
                        <input class="input-long" id="elm_field_stripe_url" name="url" type="text" value="{{ company.url }}" disabled/>
                    </div>
                </div>

            </fieldset>

            <fieldset>
                <legend>{{ 'address'|trans }}</legend>
                <div class="control-group">
                    <label class="control-label" for="elm_field_stripe_address">{{ 'address'|trans }}</label>
                    <div class="controls">
                        <input class="input-large" id="elm_field_stripe_address" name="address_line1" type="text" value="{{ addressLine1 }}" disabled />
                    </div>
                </div>
                <div class="control-group">
                    <label class="control-label" for="elm_field_stripe_address2">{{ 'address_2'|trans }}</label>
                    <div class="controls">
                        <input class="input-large" id="elm_field_stripe_address2" name="address_line2" type="text" value="{{ addressLine2 }}" disabled />
                    </div>
                </div>
                <div class="control-group">
                    <label class="control-label" for="elm_field_stripe_address_city">{{ 'city'|trans }}</label>
                    <div class="controls">
                        <input class="input-long" id="elm_field_stripe_address_city" name="address_city" type="text" value="{{ company.city }}" disabled />
                    </div>
                </div>
                <div class="control-group">
                    <label class="control-label" for="elm_field_stripe_address_zip">{{ 'zipcode'|trans }}</label>
                    <div class="controls">
                        <input class="input-small" id="elm_field_stripe_address_zip" name="address_zip" type="text" value="{{ company.zipcode }}" disabled />
                    </div>
                </div>
                <div class="control-group">
                    <label class="control-label" for="elm_field_stripe_address_state">{{ 'region'|trans }}</label>
                    <div class="controls">
                        <input class="input-long" id="elm_field_stripe_address_state" name="address_state" type="text" value=""/>
                    </div>
                </div>
                <div class="control-group">
                    <label class="control-label" for="elm_field_stripe_address_country" >{{ 'country'|trans }}</label>
                    <div class="controls">
                        <select id="elm_field_stripe_address_country" name="address_country" disabled>
                            {% for countryCode, country in getCountries(true) %}
                                <option value="{{ countryCode }}" {% if countryCode == company.country %}selected{% endif %}>{{ country }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </fieldset>

            {% if personVerified == false %}
                <fieldset>
                    <legend>{{ 'legal_representative_'|trans }}</legend>
                    <div class="control-group">
                        <label class="control-label" for="elm_field_stripe_firstname">{{ 'first_name'|trans }}</label>
                        <div class="controls">
                            <input class="input-long" id="elm_field_stripe_firstname" name="person_first_name" type="text" value="{{ company.legalRepresentativeFirstName }}" disabled />
                        </div>
                    </div>
                    <div class="control-group">
                        <label class="control-label" for="elm_field_stripe_lastname">{{ 'last_name'|trans }}</label>
                        <div class="controls">
                            <input class="input-long" id="elm_field_stripe_lastname" name="person_last_name" type="text" value="{{ company.legalRepresentativeLastName }}" disabled />
                        </div>
                    </div>
                    <div class="control-group">
                        <label class="control-label" for="elm_field_stripe_birthday">{{ 'date_of_birth'|trans }}</label>
                        <div class="controls">
                            <input class="input-mini" id="elm_field_stripe_birthday" name="person_birth_day" type="number" value="" required /> /
                            <input class="input-mini" id="elm_field_stripe_birthday1" name="person_birth_month" type="number" value="" required /> /
                            <input class="input-mini" id="elm_field_stripe_birthday2" name="person_birth_year" type="number" value="" required /> DD/MM/YYYY
                        </div>
                    </div>

                    <div class="control-group">
                        <label class="control-label" for="elm_field_stripe_email">{{ 'email_address'|trans }}</label>
                        <div class="controls">
                            <input class="input-long" id="elm_field_stripe_email" name="person_email" type="text" value="{{ company.email }}" disabled/>
                        </div>
                    </div>

                    <div class="control-group">
                        <label class="control-label" for="elm_field_stripe_function">{{ 'vendor_w_function'|trans }}</label>
                        <div class="controls">
                            <input class="input-long" id="elm_field_stripe_function" name="person_relationship_title" type="text" value="" required/>
                        </div>
                    </div>

                    <div class="control-group">
                        <label class="control-label" for="elm_field_stripe_phone">{{ 'phone'|trans }}</label>
                        <div class="controls">
                            <input class="input-long" id="elm_field_stripe_phone" name="person_phone" type="text" value="{{ company.phoneNumber }}" />
                            <span class="help-block">{{ 'company_phone_intl_format'|trans }}</span>
                        </div>
                    </div>

                    <div class="control-group">
                        <label class="control-label" for="elm_field_stripe_owner_identity">{{ 'vendor_w_id_card'|trans }}</label>
                        <div class="controls">
                            {% if files.w_ID_card_ is defined %}
                                <span>
                                    {{ files.w_ID_card_.filename }}
                                </span>
                                <input type="hidden"
                                       id="elm_field_stripe_owner_identity"
                                       value="{{ files.w_ID_card_.content }}"
                                       data-filename="{{ files.w_ID_card_.filename }}"
                                >
                            {% else %}
                                <input type="file" id="elm_field_stripe_owner_identity" required>
                            {% endif %}
                        </div>
                    </div>

                    <div class="control-group">
                        <label class="control-label" for="elm_field_stripe_owner_additional_document">{{ 'vendor_w_kbis'|trans }}</label>
                        <div class="controls">
                            {% if files.w_KBIS_ is defined %}
                                <span>
                                {{ files.w_KBIS_.filename }}
                            </span>
                                <input type="hidden"
                                       id="elm_field_stripe_owner_additional_document"
                                       value="{{ files.w_KBIS_.content }}"
                                       data-filename="{{ files.w_KBIS_.filename }}"
                                >
                            {% else %}
                                <input type="file" id="elm_field_stripe_owner_additional_document" required>
                            {% endif %}
                        </div>
                    </div>

                </fieldset>
            {% endif %}

            <fieldset>
                <legend>{{ 'legal_representative_address'|trans }}</legend>
                <div class="control-group">
                    <label class="control-label" for="elm_field_stripe_owner_address">{{ 'address'|trans }}</label>
                    <div class="controls">
                        <input class="input-large" id="elm_field_stripe_owner_address" name="person_address_line1" type="text" value="{{ user.billingAddress.fieldValue('address') }}" required />
                    </div>
                </div>
                <div class="control-group">
                    <label class="control-label" for="elm_field_stripe_owner_address2">{{ 'address_2'|trans }}</label>
                    <div class="controls">
                        <input class="input-large" id="elm_field_stripe_owner_address2" name="person_address_line2" type="text" value="{{ user.billingAddress.fieldValue('address_2') }}" />
                    </div>
                </div>
                <div class="control-group">
                    <label class="control-label" for="elm_field_stripe_owner_address_city">{{ 'city'|trans }}</label>
                    <div class="controls">
                        <input class="input-long" id="elm_field_stripe_owner_address_city" name="person_address_city" type="text" value="{{ user.billingAddress.fieldValue('city') }}" required />
                    </div>
                </div>
                <div class="control-group">
                    <label class="control-label" for="elm_field_stripe_owner_address_zip">{{ 'zipcode'|trans }}</label>
                    <div class="controls">
                        <input class="input-small" id="elm_field_stripe_owner_address_zip" name="person_address_postal_code" type="text" value="{{ user.billingAddress.fieldValue('zipcode') }}" required />
                    </div>
                </div>
                <div class="control-group">
                    <label class="control-label" for="elm_field_stripe_owner_address_state">{{ 'region'|trans }}</label>
                    <div class="controls">
                        <input class="input-long" id="elm_field_stripe_owner_address_state" name="person_address_state" type="text" value=""/>
                    </div>
                </div>
                <div class="control-group">
                    <label class="control-label" for="elm_field_stripe_owner_address_country">{{ 'country'|trans }}</label>
                    <div class="controls">
                        <select id="elm_field_stripe_owner_address_country" name="person_address_country">
                            {% for countryCode, country in getCountries(true) %}
                                <option value="{{ countryCode }}" {% if countryCode == user.billingAddress.fieldValue('country')|default('FR') %}selected{% endif %}>{{ country }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </fieldset>
            <br />
            <br />
            <label class="checkbox">
                <input required type="checkbox" id="elm_field_stripe_tos" name="tos_shown_and_accepted" value="Y"> {{ 'stripe_tos_link'|trans|raw }}
            </label>

            <input type="submit" class="btn btn-primary"  value="{{ 'submit'|trans }}">
        </form>
    </div>


<script src="https://js.stripe.com/v3/"></script>
<script type="text/javascript" src="/js/backend/stripe/account.js"></script>
<script>
    const base64ToFile = function(base64, filename) {
        const base64Str = atob(base64);
        let base64Index = base64Str.length;
        const uint8Array = new Uint8Array(base64Index);

        while (base64Index--) {
            uint8Array[base64Index] = base64Str.charCodeAt(base64Index);
        }

        return new File([uint8Array], filename, { type: 'application/octet-stream' });
    }

    const account = new StripeAccount({
        form: document.getElementById('stripe-tos-form'),
        apiKey: '{{ stripePublicKey }}',
        apiVersion: '{{ stripeApiVersion }}'
    });

    account.onError(function (event) {
        let message = null;

        $('#waiting_box').remove();
        document.getElementsByClassName('btn btn-primary')[0].disabled = false;

        if (event.detail.type == 'upload') {
            message = typeof event.detail.message === 'undefined' ? '{{ 'cant_upload_file'|trans }}' : event.detail.message;
        } else {
            message = event.detail.message;
        }

        Tygh.$.ceNotification('show', {
            title: Tygh.tr('notice'),
            message: event.detail.message,
            type: "S", // S for a Notice, WTF ?!
            message_state: "I"
        });
    });

    account.onUpload(function () {
        {% if personVerified == false %}
            const fileInputs = {
                elm_field_stripe_owner_identity: 'document',
                elm_field_stripe_owner_additional_document: 'additional_document',
            };

            Object.keys(fileInputs).forEach(function (key) {
                let file = document.getElementById(key);

                if (file.getAttribute('type') === 'file') {
                    file = file.files[0];
                } else {
                    file = base64ToFile(file.value, file.dataset.filename);
                }

                account.uploadData.append(fileInputs[key], file);
            });
        {% else %}
            return false;
        {% endif %}
    })

    account.onCreateAccount(function (event) {
        if (!document.getElementById('elm_field_stripe_tos').checked) {
            Tygh.$.ceNotification('show', {
                title: Tygh.tr('notice'),
                message: '{{ 'register_must_accept_terms'|trans }}',
                type: "S", // S for a Notice, WTF ?!
                message_state: "I"
            });
            return false;
        }

        document.getElementsByClassName('btn btn-primary')[0].disabled = false;

        let type = document.getElementById('elm_field_stripe_type_0').checked ? 'company' : 'individual';

        account.setAccount({
            business_type: type,
            [type]: {
                name: document.getElementById('elm_field_stripe_business_name').value,
                tax_id: document.getElementById('elm_field_stripe_siren').value,
                phone:  document.getElementById('elm_field_stripe_telephone').value,
                address: {
                    line1: document.getElementById('elm_field_stripe_address').value,
                    line2: document.getElementById('elm_field_stripe_address2').value,
                    city: document.getElementById('elm_field_stripe_address_city').value,
                    state: document.getElementById('elm_field_stripe_address_state').value,
                    postal_code: document.getElementById('elm_field_stripe_address_zip').value,
                    country: document.getElementById('elm_field_stripe_address_country').value,
                },
            },
            tos_shown_and_accepted: true,
        })
    });

    account.onCreatePerson(function (event) {
        var data = {
            address: {
                line1: document.getElementById('elm_field_stripe_owner_address').value,
                line2: document.getElementById('elm_field_stripe_owner_address2').value,
                city: document.getElementById('elm_field_stripe_owner_address_city').value,
                state: document.getElementById('elm_field_stripe_owner_address_state').value,
                postal_code: document.getElementById('elm_field_stripe_owner_address_zip').value,
                country: document.getElementById('elm_field_stripe_owner_address_country').value,
            },
        }

        {% if personVerified == false %}
            data.first_name = document.getElementById('elm_field_stripe_firstname').value;
            data.last_name = document.getElementById('elm_field_stripe_lastname').value;
            data.email = document.getElementById('elm_field_stripe_email').value;
            data.phone = document.getElementById('elm_field_stripe_phone').value;
            data.dob = {
                day: parseInt(document.getElementById('elm_field_stripe_birthday').value, 10),
                month: parseInt(document.getElementById('elm_field_stripe_birthday1').value, 10),
                year: parseInt(document.getElementById('elm_field_stripe_birthday2').value, 10),
            }
            data.relationship = {
                owner: true,
                representative: true,
                director: true,
                executive: true,
                title: document.getElementById('elm_field_stripe_function').value,
            }
        {% endif %}
        account.setPerson(data);
    });

    account.onSuccess(function (event) {
        document.getElementById('stripe_account_token').value = event.detail.accountToken;
        document.getElementById('stripe_person_token').value = event.detail.personToken;
    })

    account.init();

    $('#elm_field_stripe_owner2_enabled').change(function () {
        $('#elm_field_stripe_owner2 .control-group :input').prop('disabled', !this.checked);
    });
    $('#elm_field_stripe_owner3_enabled').change(function () {
        $('#elm_field_stripe_owner3 .control-group :input').prop('disabled', !this.checked);
    });
    $('#elm_field_stripe_owner4_enabled').change(function () {
        $('#elm_field_stripe_owner4 .control-group :input').prop('disabled', !this.checked);
    });
</script>

