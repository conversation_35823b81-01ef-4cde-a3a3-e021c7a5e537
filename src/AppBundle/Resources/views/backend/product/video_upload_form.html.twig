<div class="control-group margin-bottom50">
    <div class="radio-inline">
        <label for="local" class="inline-block width50" id="radioLocal">
            <input type="radio" class="inline-block" name="type" id="local" value="local" checked>
            <span class="inline-block">{{ 'label_upload_file'|trans }}</span>
        </label>
        <label for="url" class="inline-block width50" id="radioUrl">
            <input type="radio" class="inline-block" name="type" id="url" value="url">
            <span class="inline-block">{{ 'label_upload_url'|trans }}</span>
        </label>
    </div>
</div>

<form id="awsUploadVideoForm" action="https://{{ form.awsBucket }}.s3-{{ form.awsRegion }}.amazonaws.com/" method="post" enctype="multipart/form-data">
    <input type="hidden" name="key" value="{{ form.key }}${filename}" />
    <input type="hidden" name="acl" value="public-read" />
    <input type="hidden" name="X-Amz-Credential" value="{{ form.awsKey }}/{{ form.shortDate }}/{{ form.awsRegion }}/s3/aws4_request" />
    <input type="hidden" name="X-Amz-Algorithm" value="AWS4-HMAC-SHA256" />
    <input type="hidden" name="X-Amz-Date" value="{{ form.isoDate }}" />
    <input type="hidden" name="Policy" value="{{ form.policyBase64 }}" />
    <input type="hidden" name="X-Amz-Signature" value="{{ form.signature }}" />
    <input type="hidden" name="success_action_status" value="201" />
    <input type="file" name="file" id="file_local"/>
    <input id="awsUploadVideoFormSubmit" type="submit" value="{{ 'upload'|trans }}" class="btn" />
</form>
<div id="awsUploadVideoBar" class="progress progress-striped active hide">
    <div class="bar"></div>
</div>
<div id="awsUploadVideoNoticeNote" class="alert alert-warning hide">
    <strong>{{ 'notice'|trans }}</strong> {{ 'video_popup_notice'|trans }}
</div>
<div id="awsUploadVideoNotice" class="alert alert-warning hide">
    {{ 'video_max_size'|trans({'%video_max_size%': form.videoMaxSize}) }}
</div>

<script src="//cdn.jsdelivr.net/npm/jquery-form@3.50.0/jquery.form.js" integrity="sha256-I/wlEz7Qcs332uV1ITBnsdo8aEOyoOSJyLf8mExgzms=" crossorigin="anonymous"></script>
<script>
    $(document).ready(function() {
        var pBarContainer = $('#awsUploadVideoBar');
        var pBar = pBarContainer.find('.bar');
        var fileIn = $("#file_local")[0];

        var fnError = function() {
            pBarContainer.removeClass('progress-striped active');
            pBar.css('width', '100%').text('{{ 'error'|trans|escape('js') }}').addClass('bar-danger');
            $('#awsUploadVideoFormSubmit').prop('disabled', false);
            $('#waiting_box').remove();
        };

        var fnSuccess = function (data) {
            if (data.id === undefined || data.thumb === undefined) {
                fnError();
                return false;
            }
            videoAdded(data.id, data.thumb);
            pBarContainer.removeClass('progress-striped active');
            pBar.text('{{ 'finished'|trans|escape('js') }}').addClass('bar-success');
            $('#awsUploadVideoFormSubmit').prop('disabled', false);
            $('#awsUploadVideoNotice').addClass('hide');
            $('#awsUploadVideoNoticeNote').addClass('hide');
            $('#waiting_box').remove();
        };

        var videoAdded = function (id, thumb)
        {
            $('#id_input_hidden_video').val(id);
            $('#current_video, #removeVideoButton').removeClass('hide');
            $('#current_video > img').attr('src', thumb);
        };

        $('#awsUploadVideoForm').ajaxForm({
            uploadProgress: function(event, position, total, progress) {
                pBar.css('width', progress+'%').text(progress+'%');
            },
            success: function(response) {
                pBar.css('width', '100%').text('{{ 'processing_upload'|trans|escape('js') }}');

                if (response.id) {
                    fnSuccess(response);
                } else {
                    var tmpKey = response.getElementsByTagName('Key')[0].childNodes[0].nodeValue;
                    if (tmpKey) {
                        $.getJSON('{{ url('video_transcode') }}?key='+tmpKey, function (data) {
                            fnSuccess(data);
                        }).fail(fnError);
                    } else {
                        fnError();
                    }
                }
            },
            error: fnError,
            beforeSubmit: function() {
                pBarContainer.removeClass('hide').addClass('progress-striped active');
                pBar.removeClass('bar-success bar-danger');
                $('#awsUploadVideoFormSubmit').prop('disabled', true);
                $('#awsUploadVideoNotice').addClass('hide'); // add class hide for multiple iteration
                if (fileIn.files !== null && fileIn.files[0] instanceof File) {
                    var size = fileIn.files[0].size/1048576,
                        max ='{{ form.videoMaxSize }}';

                    if (size > max) {
                        $('#awsUploadVideoNotice').removeClass('hide');
                        fnError();
                        return false;
                    }
                }
                $('#awsUploadVideoNoticeNote').removeClass('hide');
            }
        });

        $('#radioUrl').on('click', function () {
            $('#file_local').attr('type', 'text');
            $('#awsUploadVideoForm').attr('action', '{{ url('video_copy') }}');
        });

        $('#radioLocal').on('click', function () {
            $('#file_local').attr('type', 'file');
            $('#awsUploadVideoForm').attr('action', 'https://{{ form.awsBucket }}.s3-{{ form.awsRegion }}.amazonaws.com/');
        });
    });
</script>
