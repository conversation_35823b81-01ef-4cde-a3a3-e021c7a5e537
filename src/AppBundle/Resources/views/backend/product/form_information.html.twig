{% form_theme form '@App/backend/product/form-theme.html.twig' %}

{% set product, shortDescription, fullDescription = "", "", "" %}
{% if productDataPlaceholder is not null %}
    {% set product, shortDescription, fullDescription = productDataPlaceholder.product, productDataPlaceholder.short_description, productDataPlaceholder.full_description %}
{% endif %}

{{ form_row(form.product_template_type) }}
{{ form_row(form.status) }}
{{ form_row(form.category_ids) }}
<div id="js-transaction-mode" style="display: none;">{{ form_row(form.transaction_mode) }}</div>
<div id="js-transaction-affiliate" style="display: none;">{{ form_row(form.affiliate_link) }}</div>
{{ form_row(form.product, { 'attr': { 'placeholder': product }}) }}
{{ form_row(form.product_code) }}
{{ form_row(form.w_supplier_ref) }}
{{ form_row(form.company_id) }}
{{ form_row(form.price) }}
{% if form.max_price_adjustment is defined %}
    {{ form_row(form.max_price_adjustment) }}
{% endif %}
{{ form_row(form.crossed_out_price) }}
{{ form_row(form.tax_ids) }}
{{ form_row(form.w_green_tax) }}
{{ form_row(form.w_condition) }}
{{ form_row(form.amount, {
    attr: {readonly: form.vars.value is not null and form.vars.value.infinite_stock is defined and form.vars.value.infinite_stock},
    required: form.vars.value is null or form.vars.value.infinite_stock is not defined or not form.vars.value.infinite_stock
}) }}
{{ form_row(form.infinite_stock) }}
{{ form_row(form.geoloc_postal) }}
{{ form_row(form.geoloc_label) }}
{{ form_row(form.hidden_geoloc_lat, { 'id': 'hidden_geoloc_lat'}) }}
{{ form_row(form.hidden_geoloc_lng, { 'id': 'hidden_geoloc_lng'}) }}
{{ form_row(form.geoloc_lat) }}
{{ form_row(form.geoloc_lng) }}
{{ form_row(form.is_edp) }}
{{ form_row(form.unlimited_download) }}
{% if featureSubscription %}
    {{ form_row(form.is_subscription) }}

    {% set display = "none" %}

    {% if form.vars.value.is_subscription is defined and form.vars.value.is_subscription == "1" %}
        {% set display = "block" %}
    {% endif %}

    <div id="subscriptionInformation" style="display: {{ display }};">
        {{ form_row(form.is_renewable) }}
    </div>
{% endif %}
{{ form_row(form.short_description, { 'attr': { 'placeholder': shortDescription }}) }}
{{ form_row(form.full_description, { 'attr': { 'placeholder': fullDescription }}) }}
{{ form_row(form.avail_since) }}

<script>
$(function() {
    Tygh.$('#product_data_unlimited_download').closest('.control-group').toggleBy(!$('#product_data_is_edp').is(':checked'))

    // Add listener only on create product
    if ($('#product_data_product_template_type').length > 0 && $('input[name=product_id]').val() == 0) {
        $('body').on('change', '#product_data_product_template_type', function (event) {

            $.post(fn_url("products.product_templates_infos"), {'template' : $(this).val()}, function (response) {
                setFields(JSON.parse(response.text))
            }, 'json');
        });
    }

    let setFields = function(fields) {
        // show all fields
        $('.control-group').removeClass('hidden');

        // Hide & set fields
        for (let i in fields) {
            let input;
            let type;
            let field = fields[i];

            input = $('*[name="product_data\['+field.name+'\]"]');
            type = input.prop('type');

            // Checkbox or radio
            if (input.length > 1) {
                // disabled before check
                input.each(function(element) {
                    $(element).prop('checked', false);
                });

                input = $('*[name="product_data\['+field.name+'\]"][value="'+field.value+'"]');
                input.prop('checked', true);
            } else if (input.length > 0) {
                // Only one checkbox
                if (type === 'checkbox') {
                    if (field.value == 'Y') {
                        input.prop('checked', true);
                    } else if (field.value == 'N') {
                        input.prop('checked', false);
                    } else {
                        input.prop('checked', true);
                    }
                } else {
                    input.val(field.value);
                }
            }

            input.parents('.control-group').addClass('hidden');
        }
    };

    $('#product_data_infinite_stock').on('change', function(e) {
        $('#product_data_amount').prop('readonly', $(e.target).is(':checked'));
        $('#product_data_amount').prop('required', $(e.target).is(':not(:checked)'));
        $('label[for=product_data_amount]').toggleClass('cm-required', $(e.target).is(':not(:checked)'));
    });

    $('#product_data_category_ids').on('change', function () {
        let categoryId = $(this).val();

        if (categoryId > 0) {
            $.get(fn_url('categories.transaction_mode_overridable?category_id='+categoryId)).then(function (response) {
                $('#js-transaction-mode').toggle(response.transaction_mode_overridable);
            }).fail(function (error, textStatus, errorThrown) {
                Tygh.$.ceNotification('show', {
                    title: Tygh.tr('error'),
                    message: Tygh.tr('error_ajax').replace('[error]', errorThrown),
                    type: "E",
                    message_state: "I"
                });
            });
        } else {
            $('#js-transaction-mode').hide();
        }
    }).change();
});
</script>
