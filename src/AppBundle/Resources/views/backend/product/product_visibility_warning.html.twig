{% if productVisibilityReport is defined and productVisibilityReport is not null %}
    {% apply spaceless %}
        <div class="alert alert-warning">
            <strong>{{ productVisibilityReport.reportMessage }}</strong>
            {% set reportDetailsMessage = '' %}
            {% for reportDetail in productVisibilityReport.reportDetails %}
                {% set reportDetailsMessage = reportDetailsMessage ~ ((loop.index > 1) ? ' ' : '') %}
                {% set reportDetailsMessage = reportDetailsMessage ~ reportDetail.message_part_1 %}
                {% if reportDetail.message_part_2 is defined %}
                    {% if reportDetail.message_part_2.url is defined %}
                        {% set reportDetailsMessage = reportDetailsMessage ~ '<a href="' ~ reportDetail.message_part_2.url ~ '">' ~ reportDetail.message_part_2.message ~ '</a>' %}
                    {% else %}
                        {% set reportDetailsMessage = reportDetailsMessage ~ reportDetail.message_part_2 %}
                    {% endif %}
                {% endif %}
                {% set reportDetailsMessage = reportDetailsMessage ~ '.' %}
            {% endfor %}
            <span>
                {{- reportDetailsMessage | raw -}}
            </span>
        </div>
    {% endapply %}
{% endif %}
