{%- block form_label -%}
    {% if label is not same as(false) -%}
        {% set label_attr = label_attr|merge({'class': (label_attr.class|default('') ~ ' control-label')|trim}) %}
        {% if not compound -%}
            {% set label_attr = label_attr|merge({'for': id}) %}
        {%- endif -%}
        {% if form.vars.block_prefixes.2 == 'radio' %}
            {% set required = false %}
        {%- endif -%}
        {% if required -%}
            {% set label_attr = label_attr|merge({'class': (label_attr.class|default('') ~ ' cm-required')|trim}) %}
        {%- endif -%}
        {% if label is empty -%}
            {%- if label_format is not empty -%}
                {% set label = label_format|replace({
                    '%name%': name,
                    '%id%': id,
                }) %}
            {%- else -%}
                {% set label = name|humanize %}
            {%- endif -%}
        {%- endif -%}
        <{{ element|default('label') }}{% if label_attr %}{% with { attr: label_attr } %}{{ block('attributes') }}{% endwith %}{% endif %}>
        {%- if translation_domain is same as(false) -%}
            {{- label -}}
        {%- else -%}
            {{- label|trans({}, translation_domain) -}}
        {%- endif -%}
        {% if form.vars.block_prefixes.1 == 'money' %}
            &nbsp;({{ currencySign }})
        {% endif %}
        {% if label_attr['tooltip-text'] is defined %}
            <a class="cm-tooltip" title="{{ label_attr['tooltip-text']|trans|escape('html_attr') }}"><i class="icon-question-sign"></i></a>
        {%  endif %}
        </{{ element|default('label') }}>
    {%- endif -%}
{%- endblock form_label -%}

{%- block form_widget_simple -%}
    {%- set type = type|default('text') -%}
    {%- if type == 'range' or type == 'color' -%}
        {# Attribute "required" is not supported #}
        {%- set required = false -%}
    {%- endif -%}
    {%- if type == 'text' and form.vars.block_prefixes.1 != 'money' -%}
        {% set attr = attr|merge({'class': (attr.class|default('') ~ ' input-large')|trim}) %}
    {%- endif -%}
    <input type="{{ type }}" {{ block('widget_attributes') }} {% if value is not empty %}value="{{ value }}" {% endif %}/>
{%- endblock form_widget_simple -%}

{%- block checkbox_widget -%}
    <input type="checkbox" {{ block('widget_attributes') }}{% if value is defined %} value="{{ value }}"{% endif %}{% if checked %} checked="checked"{% endif %} />
{%- endblock checkbox_widget -%}

{% block checkbox_row -%}
    {{ block('form_row') }}
{%- endblock checkbox_row %}

{%- block product_price_widget -%}
    <div class="form-inline">
        {{ block('money_widget') }}
        {% if price_tiers_first_interval_quantity_max_limit > 0 %}
            &nbsp;&nbsp;&nbsp; ({{ 'price_tiers_info_quantity'|trans }} < {{ price_tiers_first_interval_quantity_max_limit }})
        {% endif %}
    </div>
    {% if product_id is not null %}
    <br/>
        {% if price_tier_management_flag == true %}
            <p id="product-price-tiers-product-edit-info"> {{ 'product_price_tiers_product_edit_info'|trans }} </p>
            {{ render_smarty_legacy('buttons/button.tpl', {
                'but_text': 'w_product_price_tiers_management'|trans,
                'but_href': 'price_tiers.update?product_id='~product_id,
                'but_role': 'edit',
                'but_meta': 'btn',
            }) }}
        {% endif %}
    {{ render_smarty_legacy('buttons/button.tpl', {
        'but_text': 'w_allowed_combinations'|trans,
        'but_href': 'product_options.exceptions?product_id='~product_id,
        'but_role': 'edit',
        'but_meta': 'btn',
    }) }}
    {% endif %}
{%- endblock product_price_widget -%}

{%- block money_widget -%}
    {{ block('form_widget_simple') }}
{%- endblock money_widget -%}

{%- block form_row -%}
    {%- set widget_attr = {} -%}
    <div class="control-group">
        {{- form_label(form) -}}
        {{- form_errors(form) -}}
        <div class="controls">
            {{- form_widget(form, widget_attr) -}}
        </div>
    </div>
{%- endblock form_row -%}

{%- block company_widget -%}
    {{ render_smarty_legacy('views/companies/components/company_field.tpl', {'name': 'product_data[company_id]', 'selected': value, "no_wrap": true}) }}
{%- endblock company_widget -%}

{%- block company_row -%}
    {{ block('form_row') }}
{%- endblock company_row -%}

{%- block link_to_inventory_widget -%}
    {{ render_smarty_legacy('buttons/button.tpl', {
        'but_text': 'edit'|trans,
        'but_href': 'product_options.inventory?product_id='~product_id,
        'but_role': 'edit',
    }) }}
{%- endblock link_to_inventory_widget -%}

{%- block categories_widget -%}
    {%- set type = type|default('hidden') -%}
    {{ render_smarty_legacy('pickers/categories/picker.tpl', {
        'w_leaves_only': true,
        'input_name': 'product_data[category_ids]',
        'hide_link': true,
        'hide_delete_button': true,
        'display_input_id': id~'_display',
        'disable_no_item_text': true,
        'view_mode': 'simple',
        'but_meta': 'btn',
        'multiple': false,
        'item_ids': value,
        'input_id': id,
    }) }}
{%- endblock categories_widget -%}

{%- block categories_row -%}
    {{ block('form_row') }}
{%- endblock categories_row -%}

{% block date_widget %}
    {%- if widget == 'single_text' -%}
        <div class="calendar">
            {% set attr = attr|merge({'class': 'cm-calendar', 'style': 'width: 103px!important;'}) %}
            {{ block('form_widget_simple') }}
            <span data-ca-external-focus-id="elm_date_avail_holder" class="icon-calendar cm-external-focus"></span>
        </div>
    {%- else -%}
        <div {{ block('widget_container_attributes') }}>
            {{- date_pattern|replace({
                '{{ year }}':  form_widget(form.year),
                '{{ month }}': form_widget(form.month),
                '{{ day }}':   form_widget(form.day),
            })|raw -}}
        </div>
    {%- endif -%}
{% endblock date_widget %}

{%- block textarea_widget -%}
    {% set attr = attr|merge({'class': (attr.class|default('') ~ ' input-large')|trim}) %}
    <textarea {{ block('widget_attributes') }}>{{ value }}</textarea>
{%- endblock textarea_widget -%}

{%- block radio_widget -%}
    <input type="radio" {{ block('widget_attributes') }}{% if value is defined %} value="{{ value }}"{% endif %}{% if checked %} checked="checked"{% endif %} />
{%- endblock radio_widget -%}

{%- block choice_widget_expanded -%}
    {%- for child in form %}
        <label class="{{ multiple ? 'checkbox' : 'radio' }} inline" for="{{ child.vars.id }}">
            {{- form_widget(child) -}}
            {%- if choice_translation_domain is same as(false) -%}
                {{- child.vars.label -}}
            {%- else -%}
                {{- child.vars.label|trans({}, choice_translation_domain) -}}
            {%- endif -%}
        </label>
    {% endfor -%}
{%- endblock choice_widget_expanded -%}
