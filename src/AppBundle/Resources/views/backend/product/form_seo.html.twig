{% form_theme form '@App/backend/product/form-theme.html.twig' %}

{% set seoName, pageTitle, metaDescription, metaKeywords = "", "", "", "" %}
{% if productDataPlaceholder is not null %}
    {% set seoName, pageTitle, metaDescription, metaKeywords = productDataPlaceholder.seo_name, productDataPlaceholder.page_title, productDataPlaceholder.meta_description, productDataPlaceholder.meta_keywords %}
{% endif %}

{{ form_row(form.seo_name, { 'attr': { 'placeholder': seoName }}) }}
{{ form_row(form.page_title, { 'attr': { 'placeholder': pageTitle }}) }}
{{ form_row(form.meta_description, { 'attr': { 'placeholder': metaDescription }}) }}
{{ form_row(form.meta_keywords, { 'attr': { 'placeholder': metaKeywords }}) }}
