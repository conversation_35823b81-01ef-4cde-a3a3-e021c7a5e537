<div id="commissions_page">

    <p>{{ 'define_default_commission'|trans }}</p>

    <form action="{{ path('admin_Commission_save') }}" method="post">
        <div class="input-append">
            <input type="number" step="0.01" min="0" max="99" name="percent" value="{{ percent }}" />
            <span class="add-on">%</span>
        </div>

        <strong>+</strong>

        <div class="input-append">
            <input type="number" step="0.01" min="0" name="fixed" value="{{ fixed }}" />
            <span class="add-on">{{ currencySign }}</span>
        </div>

        <div class="input-prepend input-append">
            <span class="add-on">{{ 'maximum'|trans }}&nbsp;</span>
            <input type="number" step="0.01" min="0.01" name="maximum" value="{{ maximum }}" oninvalid="this.setCustomValidity('{{ 'maximum_commission_value'|trans }}')"
                   oninput="this.setCustomValidity('')" />
            <span class="add-on">{{ currencySign }}</span>
        </div>

        <br />

        {% include '@App/backend/commission/commission_by_category.html.twig' %}

        <div>
            <label for="apply_to_all">
                <input type="checkbox" name="apply_to_all" id="apply_to_all" value="checked">
                <span></span>
                {{ 'define_default_commission_for_all_vendors'|trans }}
                <i class="icon-exclamation-sign"></i> {{ 'it_will_overwrite_existing_values'|trans }}
            </label>
        </div>

        <input type="hidden" name="csrf_token" value="{{ csrf_token('bo_default_commission') }}">
        <button type="submit" class="btn btn-primary cm-confirm">{{ 'save_commissions'|trans }}</button>
    </form>

</div>
