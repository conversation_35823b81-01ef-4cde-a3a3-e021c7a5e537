<h4 class="subheader">{{ 'vendor_commission_per_category'|trans }}</h4>

<table class="table" width="100%">
    <thead>
    <tr class="cm-first-sibling">
        <th>{{ 'category'|trans }}</th>
        <th>{{ 'commission'|trans }}</th>
        <th>{{ 'maximum_per_item'|trans }}</th>
        <th></th>
    </tr>
    </thead>
    <tbody id="body-comission">
        {% set indexNewCommissions = 0 %}
        {% for index, commission in commissionsByCategory %}
            <tr>
                <td>
                    {% block categories_widget %}
                        {{
                            render_smarty_legacy('pickers/categories/picker.tpl', {
                                'but_meta': 'btn',
                                'disable_no_item_text': true,
                                'multiple': false,
                                'item_ids': commission.categoryId,
                                'input_name': 'commission_data[' ~ index ~ '][category_id]',
                                'hide_link': true,
                                'hide_delete_button': true,
                                'data_id': 'category_id'
                            })
                        }}
                    {% endblock categories_widget %}
                </td>
                <td>
                    <input type="hidden" name="commission_data[{{ index }}][id]" value="{{ commission.id }}"  />
                    <div class="input-append">
                        <input type="number" step="0.01" min="0" style="width:10em !important" name="commission_data[{{ index }}][percent_amount]" value="{{ commission.percentAmount }}" />
                        <span class="add-on">%</span>
                    </div>
                        +
                        <div class="input-append">
                            <input type="number" step="0.01" min="0" style="width:10em !important" name="commission_data[{{ index }}][fixed_amount]" value="{{ commission.fixAmount }}" />
                            <span class="add-on">{{ currencySign }} / {{ "item"|trans }}</span>
                        </div>
                </td>
                <td>
                    <div class="input-append">
                        <input type="number" step="0.01" min="0.01" style="width:10em !important" name="commission_data[{{ index }}][maximum_amount]" value="{{ commission.maximumAmount }}" oninvalid="this.setCustomValidity('{{ 'maximum_commission_value'|trans }}')"
                               oninput="this.setCustomValidity('')"/>
                        <span class="add-on">{{ currencySign }} / {{ "item"|trans }}</span>
                    </div>
                </td>
                <td class="right nowrap">
                    <div class="hidden-tools">
                        <a class="cm-ajax cm-delete-row" href="{{ path('admin_Commission_by_category_delete', {'commissionId': commission.id}) }}" title="{{ "delete"|trans }}">
                            <i class="icon-trash"></i>
                        </a>
                    </div>
                </td>
            </tr>
            {% set indexNewCommissions = index + 1 %}
        {% endfor %}
        <tr id="box_add_commission_by_category{{ indexNewCommissions }}">
            <td>
                {% block categories_widget_empty %}
                    {{
                        render_smarty_legacy('pickers/categories/picker.tpl', {
                            'but_meta': 'btn',
                            'disable_no_item_text': true,
                            'multiple': false,
                            'item_ids': '',
                            'input_name': 'commission_data[' ~ indexNewCommissions ~ '][category_id]',
                            'on_change': 'enableFields();'
                        })
                    }}
                {% endblock categories_widget_empty %}
            </td>
            <td>
                <input type="hidden" name="commission_data[{{ indexNewCommissions }}][id]" value=""  />
                <div class="input-append">
                    <input type="number" step="0.01" min="0" style="width:10em !important" class="new-commission-{{ indexNewCommissions }}" name="commission_data[{{ indexNewCommissions }}][percent_amount]" value="" disabled />
                    <span class="add-on">%</span>
                </div>
                    +
                    <div class="input-append">
                        <input type="number" step="0.01" min="0" style="width:10em !important" class="new-commission-{{ indexNewCommissions }}" name="commission_data[{{ indexNewCommissions }}][fixed_amount]" value="" disabled />
                        <span class="add-on">{{ currencySign }} / {{ "item"|trans }}</span>
                    </div>
            </td>
            <td>
                <div class="input-append">
                    <input type="number" step="0.01" min="0.01" style="width:10em !important" class="new-commission-{{ indexNewCommissions }}" name="commission_data[{{ indexNewCommissions }}][maximum_amount]" value="" oninvalid="this.setCustomValidity('{{ 'maximum_commission_value'|trans }}')"
                           oninput="this.setCustomValidity('')" disabled />
                    <span class="add-on">{{ currencySign }} / {{ "item"|trans }}</span>
                </div>
            </td>
            <td class="right">
                <div class="hidden-tools">
                    {% block multiple_buttons %}
                        {{
                            render_smarty_legacy('design/backend/templates/buttons/multiple_buttons.tpl', {
                                'hide_clone': true,
                                'tag_level': 1,
                                'item_id': 'add_commission_by_category' ~ indexNewCommissions,
                                'disabled': 1,
                            })
                        }}
                    {% endblock multiple_buttons %}
                </div>
            </td>
        </tr>
    </tbody>
</table>
<style>
    .input-append {
        margin-bottom: 0px;
    }
</style>
<script type="text/javascript" src="/js/backend/commission/commission.js"></script>
