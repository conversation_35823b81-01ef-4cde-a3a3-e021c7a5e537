<div id="partial-refund">
    <h3>{{ "refund"|trans }}</h3>

    <input type="hidden" disabled value="{{ currencySign }}" name="currencySign">
    <input type="hidden" disabled value="{{ isTaxIncludedInPrice }}" name="isTaxIncludedInPrice">

    <form action="" method="post" id="refund-form">
        <div class="container">
            <div class="pull-right credit-note-number">
                <div class="input-append">
                    <span class="add-on">{{ "credit_note"|trans }} {{ "credit_note.number"|trans }} :</span>
                    <input type="text" id="credit_note_number" name="credit_note_number" value="{% if invoicingDisabled == false %} {{ autoGeneratedReference }} {% endif %}"
                           required
                           {% if autoGeneratedReference|length > 0 or invoicingDisabled == true %}readonly{% endif %}
                    />
                </div>
                <br>
                <label for="do_not_create_credit" {% if invoicingDisabled == true %} style="cursor: not-allowed;"{% endif %}>
                    <input type="checkbox" {% if invoicingDisabled == true %} checked disabled {% endif%} id="do_not_create_credit" name="do_not_create_credit" />&nbsp;
                    {{ "do_not_create_credit"|trans }}
                </label>
                <div id="select_credit_note_number" style="display:none;"><strong style="color:red;">{{ 'refund_select_credit_note_number'|trans }}</strong></div>
            </div>

            <div>
                <button class="btn" id="check_all">
                    {{ 'refund_fully'|trans }}
                </button>
            </div>

            <table style="width:100%;" class="table table-middle">
                <thead>
                <tr>
                    <th style="width:40%;">{{ "product"|trans }}</th>
                    <th style="width:15%;" class="text-right">{{ "price"|trans }}</th>
                    <th style="width:15%;" class="center">{{ "quantity"|trans }}</th>
                    {% if order_info.taxes %}
                        <th style="width:15%;" class="text-right">{{ "tax"|trans }}</th>
                    {% endif %}
                    <th style="width:15%;" class="right">{{ "w_total_duty_free"|trans }}</th>
                </tr>
                </thead>
                {% for key, product in remainingProducts %}
                    {% if product.amount > 0 %}
                        <tr class="product-line" data-item-id="{{ product.item_id }}">
                            <td style="width:40%;">
                                <input type="hidden" id="{{ product.item_id }}" name="item_ids[]" class="item_ids"
                                       value="{{ product.item_id }}">
                                <a href="{{ cscart_url('products.update?product_id=' ~ product.product_id) }}">
                                    {{ product.product }}
                                </a>
                                <div class="products-hint">
                                    <label for="{{ product.item_id }}">
                                        {% if product.product_code %}
                                            <p>{{ "sku"|trans ~ ':'|trans ~ product.product_code }}</p>
                                        {% endif %}

                                        {% if product.w_supplier_ref %}
                                            <p>{{ "w_supplier_ref"|trans ~ ':'|trans ~ product.w_supplier_ref }}</p>
                                        {% endif %}
                                    </label>
                                </div>
                            </td>
                            <td style="width:15%;" class="text-right">
                                {{ product.price|price }}
                                <input type="hidden" name="products[{{ product.item_id }}][base_price]"
                                       value="{{ product.price }}">
                                {% set vat = product.tax_value / product.amount %}
                                {% set unitPrice = isTaxIncludedInPrice
                                    ? product.price - vat
                                    : product.price
                                %}
                                {% set unitDiscount = product.discounted_unit_price is defined
                                    ? product.price - product.discounted_unit_price.preciseConvertedAmount
                                    : 0
                                %}
                                <input type="hidden" name="products[{{ product.item_id }}][unit_price]" disabled
                                       value="{{ unitPrice }}">
                                <input type="hidden" name="products[{{ product.item_id }}][unit_discount]" disabled
                                       value="{{ unitDiscount }}">
                                <input type="hidden" name="products[{{ product.item_id }}][unit_taxes]" disabled
                                       value="{{ vat }}">
                            </td>
                            <td style="width:15%;" class="center">
                                <input type="number" min="0" max="{{ product.amount }}"
                                       name="products[{{ product.item_id }}][amount]" value="0"
                                       class="quantity-input" data-item-id="{{ product.item_id }}">
                                <input type="hidden" name="products[{{ product.item_id }}][original_amount]"
                                       value="{{ product.amount }}">
                                <br>
                                <small>max{{':'|trans}}{{ product.amount }}</small>
                                <div id="select_quantity-{{ product.item_id }}" style="display:none;"><strong style="color:red;">{{ 'refund_select_quantities'|trans }}</strong></div>
                            </td>
                            {% if order_info.taxes %}
                                <td style="width:15%;" class="text-right">
                                    <span class="displayed-vat">{{ 0|price }}</span>
                                    <input type="hidden" name="products[{{ product.item_id }}][tax_value]" value="{{ product.tax_value }}">
                                </td>
                            {% endif %}
                            <td style="width:15%;" class="text-right">
                            <span class="displayed-subtotal">
                                {{ 0|price }}
                            </span>
                            </td>
                        </tr>
                    {% endif %}
                {% endfor %}

                {% if order_info.shipping_cost > 0 and shippingsRefunded == false %}
                    {% set shipping_ht = order_info.shipping_cost_without_tax %}
                    {% set shipping_tax = order_info.shipping_cost - shipping_ht %}
                    <tr>
                        <td>
                            <label for="shipping">{{ "shipping_cost"|trans }}</label>
                            <input type="hidden" id="shipping_cost" name="shipping_cost" value="{{ order_info.shipping_cost }}">
                            <input type="hidden" id="shipping_cost_original" value="{{ shippingInfo.originalShippingCost }}">
                            <input type="hidden" id="shipping_cost_discount" value="{{ shippingInfo.shippingCostDiscount }}">
                        </td>
                        <td class="text-right">
                            {{ order_info.shipping_cost|price }}
                        </td>
                        <td class="center">
                            <input type="checkbox" name="shipping" id="shipping" class="quantity-input">
                        </td>
                        {% if order_info.taxes %}
                            <td class="text-right"><input type="hidden" id="shipping_tax" name="shipping_tax" value="{{ shipping_tax }}">{{ shipping_tax|price }}</td>
                        {% endif %}
                        <td class="text-right">{{ shipping_ht|price }}</td>
                    </tr>
                {% endif %}
            </table>
            <div id="select_products" style="display:none;"><strong style="color:red;">{{ "refund_select_products"|trans }}</strong></div>

            <div class="totals order-notes statistic">

                <table>
                    <tr>
                        <td colspan="2" class="main-statistic-title statistic-title">{{ "w_totals_with_taxes"|trans }}</td>
                    </tr>
                    <tr>
                        <td>{{ "subtotal"|trans }}</td>
                        <td id="subtotal">
                            {{ 0|price }}
                        </td>
                    </tr>
                    {% if order_info.shipping_cost > 0 %}
                    <tr>
                        <td>{{ "shipping_cost"|trans }}</td>
                        <td id="shipping_total">
                            {{ 0|price }}
                        </td>
                    </tr>
                    {% endif %}

                    {% if order_info.taxes %}
                    <tr>
                        <td>{{ "w_taxes_order_details"|trans }}</td>
                        <td id="taxes">{{ 0|price }}</td>
                    </tr>
                    {% endif %}

                    {% if isDiscounted %}
                        <tr>
                            <td>{{ "including_discount"|trans }}</td>
                            <td id="discount">{{ 0|price }}</td>
                        </tr>
                    {% endif %}
                    <tr>
                        <td class="statistic-title">{{ "total"|trans }}</td>
                        <td class="price" id="total">{{ 0|price }}</td>
                    </tr>
                </table>

                <input type="hidden" id="total_refund" value="0" />
            </div>
        </div>

        <div class="text-right buttons">
            <a href="{{ cscart_url('orders.details&order_id=' ~ order_info.order_id) }}" class="btn btn-danger">{{ "cancel"|trans }}</a>
            <a id="partialRefundConfirmationModalButton" href="#partialRefundConfirmationModal" data-toggle="modal" class="btn btn-primary">{{ "refund_create"|trans }}</a>
        </div>
        <div id="partialRefundConfirmationModal" class="modal hide fade" tabindex="-1" role="dialog" aria-labelledby="partialRefundConfirmationModalLabel" aria-hidden="true">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                <h3 id="partialRefundConfirmationModalLabel">{{"warning"|trans }}</h3>
            </div>
            <div class="modal-body">{{ "refund_confirm_modal"|trans }}</div>
            <div class="modal-footer">
                <button class="btn" data-dismiss="modal" aria-hidden="true">{{ "cancel"|trans }}</button>
                <button type="submit" style="margin-left:0;" class="btn btn-danger">{{ "confirm"|trans }}</button>
            </div>
        </div>
    </form>
</div>

<script type="text/javascript" src="/js/backend/refund/refund.js"></script>
