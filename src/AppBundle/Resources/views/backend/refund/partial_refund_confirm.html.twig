<div id="partial-refund-confirm">
    {% if refund.creditNoteReference is not null %}
        <div class="pull-right credit-note-reference">
            {{ "credit_note"|trans ~ ' ' ~ "credit_note.number"|trans }} <strong>{{ refund.creditNoteReference }}</strong>
        </div>
    {% endif %}

    <h3>{{ "refund"|trans }} #{{ refund.getId() }}</h3>

    <div class="container" id="refund-form">
        <table style="width:100%;" class="table table-middle">
            <thead>
            <tr>
                <th style="width:50%;">{{ "product"|trans }}</th>
                <th>{{ "price"|trans }}</th>
                <th class="center">{{ "quantity"|trans }}</th>
                <th class="center">{{ "tax"|trans }}</th>
                <th class="right">{{ "total_ht"|trans }}</th>
            </tr>
            </thead>

            {% for creditNoteItem in creditNote.items %}
                {% set item = attribute(orderItems, creditNoteItem.itemId) %}
                {% set product = attribute(products, creditNoteItem.itemId) %}
                <tr>
                    <td style="width:70%;">
                        <a href="{{ cscart_url('products.update?product_id=' ~ item.productId) }}">
                            {{ product.product }}
                        </a>
                        <div class="products-hint">
                            <label for="{{ item.itemId }}">
                                {% if item.productCode %}
                                    <p>{{ "sku"|trans }}: {{ item.productCode }}</p>
                                {% endif %}

                                {% if product.w_supplier_ref %}
                                    <p>{{ "w_supplier_ref"|trans }}: {{ product.w_supplier_ref }}</p>
                                {% endif %}
                            </label>
                        </div>
                    </td>
                    <td class="text-right">
                        {{ creditNoteItem.unitPriceIncludingTaxes.convertedAmount|price }}
                    </td>
                    <td style="width:10%;" class="center">
                        {{ creditNoteItem.quantity }}
                    </td>
                    <td class="center">
                        {{ creditNoteItem.taxAmount.convertedAmount|price }}
                    </td>
                    <td style="width:10%;" class="text-right">
                        {{ creditNoteItem.total.convertedAmount|price }}
                    </td>
                </tr>
            {% endfor %}
            {% if refund.hasShipping() %}
                <tr>
                    <td style="width:70%;">{{ "shipping_cost"|trans }}</td>
                    <td style="width:10%;" class="right">{{ refund.getshippingAmount.convertedAmount|price }}</td>
                    <td></td>
                    <td class="right">{{ creditNote.shippingTaxAmount.convertedAmount|price }}</td>
                    <td class="right">
                        {{ refund.getshippingAmount.subtract(creditNote.shippingTaxAmount).convertedAmount|price }}
                    </td>
                </tr>
            {% endif %}

        </table>
        <div class="totals order-notes statistic">

            <table>
                <tr>
                    <td colspan="2" class="main-statistic-title statistic-title">{{ "w_totals_with_taxes"|trans }}</td>
                </tr>
                <tr>
                    <td>{{ "subtotal"|trans }}</td>
                    <td>
                        {{ creditNote.subtotal.isPositive ? creditNote.subtotal.convertedAmount|price : 0|price }}
                    </td>
                </tr>
                <tr>
                    <td>{{ "shipping_cost_ttc"|trans }}</td>
                    <td>
                        {{ refund.shippingAmount.convertedAmount|price }}
                    </td>
                </tr>
                {% if creditNote.taxes|length > 0 %}
                <tr>
                    <td>{{ 'w_taxes_order_details'|trans }}</td>
                </tr>
                {% for tax in creditNote.taxes %}
                <tr>
                    <td>{{ 'tax'|trans ~ ' ' ~ tax.rate ~ '%' }}</td>
                    <td>
                        {{ tax.total.convertedAmount|price }}
                    </td>
                </tr>
                {% endfor %}
                {% endif %}
                {% if creditNote.getDiscountTotal.convertedAmount > 0 %}
                <tr>
                    <td>{{ 'including_discount'|trans }}</td>
                    <td>
                        {{ creditNote.getDiscountTotal.convertedAmount|price }}
                    </td>
                </tr>
                {% endif %}
                <tr>
                    <td>{{ "refunded_amount"|trans }}</td>
                    <td>
                        <strong>{{ refund.amount.convertedAmount|price }}</strong>
                    </td>
                </tr>
            </table>

        </div>
    </div>

    <div class="text-right">
        <a href="{{ cscart_url('orders.details&order_id=' ~ order.id ~ '&selected_section=returns_and_refunds') }}" class="btn btn-primary">{{ "back"|trans }}</a>
    </div>
</div>

<script>
    $('.cm-back-link').on('click', function (e) {
        e.preventDefault();
        e.stopPropagation();

        location.href = '?dispatch=orders.details&order_id={{ order.id }}';
    });
</script>
