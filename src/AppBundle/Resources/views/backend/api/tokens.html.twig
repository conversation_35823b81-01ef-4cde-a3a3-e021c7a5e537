<div id="api_page">
    <h4>{{ 'api_access_section.documentation_title'|trans }}</h4>

    <p>{{ 'api_access_section.documentation_link_introduction'|trans }}</p>
    <p>
        <a class="btn btn-primary" href="/api/v1/doc/" target="_blank">
            <i class="icon-share"></i>
            {{ 'api_access_section.documentation_link_text'|trans }}
        </a>
    </p>

    <h4>{{ 'api_access_section.application_token_title'|trans }}</h4>

    {% if applicationToken %}
        <p>{{ 'api_access_section.application_token_introduction'|trans|raw }}</p>
        <pre>{{ applicationToken }}</pre>
        <div class="alert alert-warning">{{ 'api_access_section.application_token_keep_secret'|trans }}</div>
    {% else %}
        <p>{{ 'api_access_section.no_application_token'|trans }}</p>
    {% endif %}

    <h4>{{ 'application_version'|trans }}</h4>

    <p>{{ applicationVersion }}</p>
</div>

