<link href="design/backend/css/select2/select2.min.css" rel="stylesheet" />

<style>
    .division-div {
        width: 90%;
        margin: auto;
    }

    .division-select {
        width: 100%;
    }

    .division-div-button {
        padding-top: 1rem;
        text-align: right;
    }
</style>

<input type="hidden" name="division-loaded" value="true" />

<div id="divisions-list">

    {% if warningMsg<PERSON>ey is not null %}
        <div style="padding-top: 40px;" class="division-div">
            <span class="icon-info-sign"></span> <strong>{{ warningMsgKey|trans }}</strong>
        </div>
    {% endif %}

    <div class="division-div" id="included-divisions-div">
        <h4>
            <label for="included-divisions">{{ 'activate_division'|trans }}</label>
        </h4>

        <div class="control-group">
            <label for="inherit-divisions" class="checkbox inline">
                <input type="checkbox" name="inherit-divisions" id="inherit-divisions"/> {{ 'enable_all_divisions'|trans }}
            </label>
        </div>

            <select class="division-select" id="included-divisions" name="included[]" multiple="multiple">
                {% for division in divisions %}
                    <option
                        value="{{division.code}}"
                        {% if division.included %}
                            selected="selected"
                        {% endif %}
                    >{% apply spaceless %}
                        [{{ division.code }}] {{division.name}}
                    {% endapply %}</option>
                {% endfor %}
            </select>

        <div class="division-div-button">
            <a class="btn" id="included-divisions-button">{{ 'empty_active_divisions'|trans }}</a>
        </div>
    </div>
    <div class="division-div" id="excluded-division-div">
        <h4>
            <label for="excluded-divisions">{{ 'excluded_division'|trans }}</label>
        </h4>

        {% spaceless %}
            <select class="division-select" id="excluded-divisions" name="excluded[]" multiple="multiple">
                {% for division in divisions %}
                    {% if division.code != 'ALL' %}
                        <option
                            value="{{division.code}}"
                            {% if division.excluded %}
                                selected="selected"
                            {% endif %}
                        >{% apply spaceless %}
                            [{{ division.code }}] {{division.name}}
                        {% endapply %}</option>
                    {% endif %}
                {% endfor %}
            </select>
        {% endspaceless %}
        <div class="division-div-button">
            <a class="btn" id="excluded-divisions-button">{{ 'empty_excluded_divisions'|trans }}</a>
        </div>
    </div>

    <div class="divisions-pagination pagination-centered">
        <ul class="pagination"></ul>
    </div>
</div>

<script src="js/backend/select2/select2.full.min.js"></script>
<script>
    $(document).ready(function() {
        let $divisionLists = $('#included-divisions, #excluded-divisions');
        $divisionLists.select2();

        $divisionLists.on('select2:select', function(event) {
            let selectedDivision = event.params.data.id;
            let $otherList = $divisionLists
                .filter((index, list) => list.id !== $(this).attr('id'))
                .first()
            ;
            let otherListDivisions = $otherList.val();
            if (null !== otherListDivisions
                && otherListDivisions.includes(selectedDivision)
            ) {
                $otherList
                    .val(otherListDivisions.filter(elem => elem !== selectedDivision))
                    .trigger('change')
                ;
            }
        });

        $('#included-divisions-button').on('click', function(event) {
            event.preventDefault();
            resetSelect('#included');
        });

        $('#excluded-divisions-button').on('click', function(event) {
            event.preventDefault();
            resetSelect('#excluded');
        });

        const resetSelect = (prefix) => {
            let elementToReset = $(prefix + '-divisions');
            elementToReset.val(null).trigger('change');
        }

        // Include all divisions
        const includedDivisions = {
            inheritValue: 'ALL',
            $handler: $('#inherit-divisions'),
            $list: $('#included-divisions'),

            /** @returns {Boolean} **/
            doesInheritParentDivisions() {
                return $.inArray(this.inheritValue, this.$list.val()) >= 0;
            },

            /** @implements {VoidFunction} **/
            listen() {
                this.$handler
                    .on('change.inherit', () => {
                        if (this.$handler.is(':checked')) {
                            this.$list
                                // Use data-value as a temp. storage for previously selected value(s)
                                // If value(s) was/contained 'ALL', store empty value to empty field on uncheck
                                .data('value', (this.doesInheritParentDivisions() ? '' : this.$list.val()))
                                .val(this.inheritValue)
                                .prop('disabled', true)
                            ;
                        } else {
                            this.$list
                                .val(this.$list.data('value'))
                                .prop('disabled', false)
                            ;
                        }

                        // Trigger select2 update
                        this.$list.trigger('change');
                    });
            }
        };

        includedDivisions.listen();

        if (includedDivisions.doesInheritParentDivisions()) {
            includedDivisions.$handler
                .prop('checked', true)
                .trigger('change.inherit')
            ;
        }
    });
</script>

