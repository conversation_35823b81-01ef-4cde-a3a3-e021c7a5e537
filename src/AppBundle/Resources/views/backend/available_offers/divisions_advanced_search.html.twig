<table class="table table-striped table-hover table-condensed">
{% for division in divisions %}
    {% if (division.isEnabled and division.disabledBy is defined and division.disabledBy != "A") or
          (division.isEnabled and (division.companyId is not defined or division.companyId is null)) %}
        <tr>
            <td style="width: 75%">
                <label for="division_{{ division.code }}">
                    <strong class="division-code">[{{ division.code }}]</strong>
                    <span class="division-desc">{{ division.name }}</span>
                </label>
            </td>
            <td>
                <div class="text-center">
                    <input type="checkbox" id="division_{{ division.code }}" name="division-enabled[]" value="{{ division.code }}" {% if division.code in search_divisions_enabled %}checked="checked"{% endif %}>
                </div>
            </td>
        </tr>
    {% endif %}
{% endfor %}
</table>
