<div id="transactions_page">
    {# Create form #}
    {% include '@App/backend/transaction/filters.html.twig' with {
        form: form
    } %}

    <hr/>

    {# Display transactions data with pagination #}
    {% if transactions | length %}
        {# Store a rendered template in a variable #}
        {% set pagination_content %}
            {% include '@App/backend/transaction/pagination.html.twig' with {
                transactions: transactions,
                lastPage: lastPage,
                currentPage: currentPage,
                total: total,
                items_per_page: items_per_page
            } %}
        {% endset %}

        {# display the pagination above the table#}
        {{ pagination_content }}

    {# display transactions #}
    {% include '@App/backend/transaction/transaction_data.html.twig' with {
        transactions: transactions,
    } %}

    {# display the pagination below the table #}
    {{ pagination_content }}

    {% else %}
        {# No transaction found #}
        {% include '@App/backend/transaction/empty_transaction.html.twig' with {
            transactions: transactions,
        } %}
    {% endif %}
</div>
