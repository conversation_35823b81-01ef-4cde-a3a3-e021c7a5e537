<div id="transactions_page">
    <table class="table table-middle" id="table-transaction">
        <thead>
            <tr>
                <th>{{ 'table_transaction_header_date'|trans }}</th>
                <th>{{ 'table_transaction_header_type'|trans }}</th>
                <th>{{ 'table_transaction_header_origin'|trans }}</th>
                <th>{{ 'table_transaction_header_destination'|trans }}</th>
                <th>{{ 'table_transaction_header_transaction_id'|trans }}</th>
                <th>{{ 'table_transaction_header_amount'|trans }}</th>
                <th>{{ 'table_transaction_header_currency'|trans }}</th>
                <th>{{ 'table_transaction_header_status'|trans }}</th>
                <th>{{ 'table_transaction_header_order_id'|trans }}</th>
                <th>{{ 'table_transaction_header_additional_information'|trans }}</th>
            </tr>
        </thead>

        <tbody>
            {% for transaction in transactions %}
                {% set type = 'transaction_' ~ transaction.type %}
                <tr>
                    <td>
                        {{ transaction.createdAt|date("d/m/Y H:i:s") }}
                    </td>
                    <td>
                        {{ type|trans|upper }}
                    </td>
                    <td>
                        {{ transaction.origin|default('- -') }}
                    </td>
                    <td>
                        {{ transaction.destination|default('- -') }}
                    </td>
                    <td>
                        {{ transaction.transactionReference }}
                    </td>
                    <td>
                        {{ transaction.amount|convertAmount }}
                    </td>
                    <td>
                        {{ transaction.currency|default('- -') }}
                    </td>
                    <td>
                        {{ transaction.status }}
                    </td>
                    <td>
                        {{ transaction.orderId|default('- -') }}
                    </td>
                    <td>
                        {% if transaction.processorInformations.additional_info is defined %}
                            {{ transaction.processorInformations.additional_info }}
                        {% endif %}
                    </td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
