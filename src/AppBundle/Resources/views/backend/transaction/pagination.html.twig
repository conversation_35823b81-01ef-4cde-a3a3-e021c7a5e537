{% block pagination %}
    <div class="cm-pagination-container">
        <div class="pagination-wrap">
            {% if lastPage > 1 %}
                <div class="pagination center">
                    <ul>
                        {% set paginationRightLeftOffset = 5 %}
                        <li {{ currentPage == 1 ? 'class="disabled"' }}>
                            <a onclick="submitSearchForm({{currentPage-1 < 1 ? 1 : currentPage-1}})" data-ca-scroll=".cm-pagination-container">
                                « {{ 'previous' | trans }}
                            </a>
                        </li>

                        <li {{ currentPage == 1 ? 'class="disabled"' }}>
                            <a onclick="submitSearchForm()" data-ca-scroll=".cm-pagination-container">
                                1
                            </a>
                        </li>

                        {% if lastPage > 2 %}
                            {# Render -5 <=> +5 page number ( 5 is paginationRightLeftOffset ) #}
                            {% for i in max(2, currentPage-paginationRightLeftOffset)..min(lastPage-1, currentPage+paginationRightLeftOffset) %}
                                <li {{ currentPage == i ? 'class="active"' }}>
                                    <a onclick="submitSearchForm({{ i }}, {{ items_per_page }})" data-ca-scroll=".cm-pagination-container">
                                        {# show ... rather than page number if this is the 1st or last iteration AND this is not the current page AND this is not the last but one AND this is not the 2nd #}
                                        {% if i != currentPage and i != 2 and i != lastPage-1 and (loop.first or loop.last) %}
                                            ...
                                        {% else %}
                                            {{ i }}
                                        {% endif %}
                                    </a>
                                </li>
                            {% endfor %}
                        {% endif %}

                        <li {{ currentPage == lastPage ? 'class="disabled"' }}>
                            <a onclick="submitSearchForm({{ lastPage }}, {{ items_per_page }})" data-ca-scroll=".cm-pagination-container">
                                {{ lastPage }}
                            </a>
                        </li>

                        <li {{ currentPage == lastPage ? 'class="disabled"' }}>
                            <a onclick="submitSearchForm({{ (currentPage+1 <= lastPage ? currentPage+1 : currentPage) }}, {{ items_per_page }})"
                               data-ca-scroll=".cm-pagination-container">
                                {{ 'next' | trans }} »
                            </a>
                        </li>
                    </ul>
                </div>
            {% endif %}
        </div>

        <div class="pagination-desc pagination-centered">
            <div class="btn-group">
                <span style="font-size: 13px;" class="pagination-total-items">{{ 'view_by' | trans }} : {{ total }} / </span>
                <a class="btn-text dropdown-toggle" data-toggle="dropdown">
                    {{ items_per_page }}
                    <span class="caret"></span>
                </a>
                <ul class="dropdown-menu cm-smart-position">
                    <li>
                        {% for i in range(10, 100, 10) %}
                            <a data-ca-scroll=".cm-pagination-container"
                               onclick="submitSearchForm(1, {{ i }})"
                               data-ca-target-id="pagination_contents">{{ i }}</a>
                        {% endfor %}
                    </li>
                </ul>
            </div>
        </div>
    </div>
{% endblock %}

<script>
    function submitSearchForm(page = 1, nbItemsPerPage = null) {
        $('#form_page').val(page);

        if (nbItemsPerPage !== null) {
            $('#form_items_per_page').val(nbItemsPerPage);
        }

        $('#form_search').trigger( "click" );
    }
</script>
