{{ form_start(form, {'attr': {'id': 'filter_transaction'}}) }}
    <div class="form-control">
        <label>{{ 'period'|trans }}</label>
        <div id="reportRange" class="transaction_period">
            <span></span> <i class="caret"></i>
        </div>
    </div>
{{ form_end(form) }}

<script type="text/javascript" src="/js/lib/daterangepicker/daterangepicker.js"></script>
<script type="text/javascript" src="/js/lib/daterangepicker/moment.min.js"></script>
<link href="/design/backend/css/lib/daterangepicker/daterangepicker.css" rel="stylesheet" />
<!-- Bootstrap CSS -->
<script type="text/javascript" src="/js/lib/multiselect/multiselect.js"></script>
<link href="/design/backend/css/lib/multiselect/multiselect.css" rel="stylesheet" />
<script>

    // List of months with translation
    const months = {
        January: Tygh.tr('date_range_picker_month_name_1'),
        February: Tygh.tr('date_range_picker_month_name_2'),
        March: Tygh.tr('date_range_picker_month_name_3'),
        April: Tygh.tr('date_range_picker_month_name_4'),
        May: Tygh.tr('date_range_picker_month_name_5'),
        June: Tygh.tr('date_range_picker_month_name_6'),
        July: Tygh.tr('date_range_picker_month_name_7'),
        August: Tygh.tr('date_range_picker_month_name_8'),
        September: Tygh.tr('date_range_picker_month_name_9'),
        October: Tygh.tr('date_range_picker_month_name_10'),
        November: Tygh.tr('date_range_picker_month_name_11'),
        December: Tygh.tr('date_range_picker_month_name_12'),
    };

    $(function() {
        let filters = JSON.parse('{{ filters | json_encode | raw }}');

        $('#form_companies option').prop('selected', true);
        if ('companies' in filters) {
            $('#form_companies').val(filters['companies']);
        }
        $('#form_companies').multiselect({
            enableFiltering: true,
            includeSelectAllOption: true
        });

        let classType = $('#form_type').attr('class');
        if (classType !== 'noneShowing') {
            $('#form_type option').prop('selected', true);
            if ('type' in filters) {
                $('#form_type').val(filters['type']);
            }
            $('#form_type').multiselect({
                enableFiltering: true,
                includeSelectAllOption: true
            });
        }

        $('#form_status option').prop('selected', true);

        if ('status' in filters) {
            $('#form_status').val(filters['status']);
        }
        $('#form_status').multiselect({
            enableFiltering: true,
            includeSelectAllOption: true
        });

        var start, end;

        var periodStartSelector = $('#form_period_start');
        var periodEndSelector = $('#form_period_end');

        var inputPeriodStart = periodStartSelector.val();
        var inputPeriodEnd = periodEndSelector.val();

        if (inputPeriodStart === '') {
            start = moment();
            end = moment();
        } else {
            start = moment(new Date(inputPeriodStart));
            end = moment(new Date(inputPeriodEnd));
        }

        function cb(start, end) {
            //for button clear set start and end to moment()
            if (start === null) {
                start = moment();
            }
            if (end === null) {
                end = moment();
            }

            $('#form_period_start').val(start.format('YYYY-M-D'));
            $('#form_period_end').val(end.format('YYYY-M-D'));
            $('#reportRange span').html(translateDate(start) + ' - ' + translateDate(end));
        }

        var ranges = new Object();
        ranges[Tygh.tr('date_range_picker_today')] = [moment(), moment()];
        ranges[Tygh.tr('date_range_picker_yesterday')] = [moment().subtract(1, 'days'), moment().subtract(1, 'days')];
        ranges[Tygh.tr('date_range_picker_last_seven_days')] = [moment().subtract(6, 'days'), moment()];
        ranges[Tygh.tr('date_range_picker_thirty_days')] = [moment().subtract(29, 'days'), moment()];
        ranges[Tygh.tr('date_range_picker_this_month')] = [moment().startOf('month'), moment().endOf('month')];
        ranges[Tygh.tr('date_range_picker_this_year')] = [moment().startOf('year'), moment().endOf('year')];

        $('#reportRange').daterangepicker({
            startDate: start,
            endDate: end,
            locale: {
                applyLabel: Tygh.tr('date_range_picker_apply'),
                customRangeLabel: Tygh.tr('date_range_picker_label'),
                fromLabel: Tygh.tr('date_range_picker_fromLabel'),
                toLabel: Tygh.tr('date_range_picker_toLabel'),
                monthNames: [
                    Tygh.tr('date_range_picker_month_name_abr_1'),
                    Tygh.tr('date_range_picker_month_name_abr_2'),
                    Tygh.tr('date_range_picker_month_name_abr_3'),
                    Tygh.tr('date_range_picker_month_name_abr_4'),
                    Tygh.tr('date_range_picker_month_name_abr_5'),
                    Tygh.tr('date_range_picker_month_name_abr_6'),
                    Tygh.tr('date_range_picker_month_name_abr_7'),
                    Tygh.tr('date_range_picker_month_name_abr_8'),
                    Tygh.tr('date_range_picker_month_name_abr_9'),
                    Tygh.tr('date_range_picker_month_name_abr_10'),
                    Tygh.tr('date_range_picker_month_name_abr_11'),
                    Tygh.tr('date_range_picker_month_name_abr_12'),
                ],
                daysOfWeek: [
                    Tygh.tr('date_range_picker_weekday_abr_0'),
                    Tygh.tr('date_range_picker_weekday_abr_1'),
                    Tygh.tr('date_range_picker_weekday_abr_2'),
                    Tygh.tr('date_range_picker_weekday_abr_3'),
                    Tygh.tr('date_range_picker_weekday_abr_4'),
                    Tygh.tr('date_range_picker_weekday_abr_5'),
                    Tygh.tr('date_range_picker_weekday_abr_6'),
                    ]
            },
          ranges:
            ranges
        }, cb);
        cb(start, end);
    });

    function translateDate(date) {
        date = date.format('MMMM D, YYYY');
        const regExpr = new RegExp(Object.keys(months).join('|'));
        return (date).replace(regExpr, function(matched){
            return months[matched];
        });
    }
</script>
