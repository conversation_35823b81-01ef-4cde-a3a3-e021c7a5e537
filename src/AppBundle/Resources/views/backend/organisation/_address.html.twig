{# main address #}
<div class="form-group">
    <label for="{{ addressType }}-address-input" class="required">{{ 'organisation_address'|trans }}</label>
    <input id="{{ addressType }}-address-input" type="text" class="form-control" v-model="organisation.{{ addressType }}.address" required>
</div>

{# additional address #}
<div class="form-group">
    <label for="{{ addressType }}-additional-address-input">{{ 'organisation_additional_address'|trans }}</label>
    <input id="{{ addressType }}-additional-address-input" type="text" class="form-control" v-model="organisation.{{ addressType }}.additionalAddress">
</div>

{# zipcode #}
<div class="form-group">
    <label for="{{ addressType }}-zipcode-input" class="required">{{ 'organisation_zipcode'|trans }}</label>
    <input id="{{ addressType }}-zipcode-input" type="text" class="form-control" v-model="organisation.{{ addressType }}.zipCode" required>
</div>

{# city #}
<div class="form-group">
    <label for="{{ addressType }}-city-input" class="required">{{ 'organisation_city'|trans }}</label>
    <input id="{{ addressType }}-city-input" type="text" class="form-control" v-model="organisation.{{ addressType }}.city" required>
</div>

{# country #}
<div class="form-group">
    <label for="{{ addressType }}-country-input">{{ 'organisation_country'|trans }}</label>
    <select id="{{ addressType }}-country-input" class="form-control" class="form-control" v-model="organisation.{{ addressType }}.country">
        {% for code, country in countries %}
            <option value="{{ code }}">{{ country }}</option>
        {% endfor %}
    </select>
</div>
