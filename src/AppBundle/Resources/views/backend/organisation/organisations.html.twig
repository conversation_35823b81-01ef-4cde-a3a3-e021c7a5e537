<section id="organisations" class="bo-page-content" v-cloak>

    {# pagination management #}
    {% include '@App/backend/components/_pagination.html.twig' %}
    {% include '@App/backend/components/_pagination-selector.html.twig' %}

     <table class="table">
        <thead>
            <tr>
                <th>
                    <label for="check_all">
                        <input type="checkbox" id="check_all">
                        <span></span>
                    </label>
                </th>
                <th>{{ 'organisation_name'|trans }}</th>
                <th>{{ 'organisation_siret'|trans }}</th>
                <th>{{ 'organisation_status'|trans }}</th>
                <th>{{ 'organisation_action'|trans }}</th>
            </tr>
        </thead>

        <tbody>
            <tr is="organisation-item" :organisation="organisation" v-for="(organisation, index) in currentPageItems" :key="index"></tr>
        </tbody>
    </table>

    {% include '@App/backend/components/_pagination.html.twig' %}

    {# organisation vue template #}
    <script type="text/x-template" id="organisation-item-template">
        <tr>
            <td>
                <label :for="organisation.id">
                    <input type="checkbox" :id="organisation.id">
                    <span></span>
                </label>
            </td>
            <td><a :href="'organisations/' + organisation.id" v-text="organisation.name"></a></td>
            <td><a :href="'organisations/' + organisation.id" v-text="organisation.siret"></a></td>
            <td>
                <div class="dropdown">
                    <a href="#" data-toggle="dropdown"><span :class="{'disabled': !isActive(organisation)}" v-text="getStatusTranslation(organisation)"></span> <span class="caret"></span></a>
                    <ul class="dropdown-menu">
                        <li :class="{'disabled': (isActive(organisation) && ! isPending(organisation))}"><a href="#" @click.prevent="toggleOrganisationStatus(organisation)" v-text="'{{ 'organisation_status_activated'|trans }}'"></a></li>
                        <li :class="{'disabled': (! isActive(organisation) && ! isPending(organisation))}"><a href="#" @click.prevent="toggleOrganisationStatus(organisation)" v-text="'{{ 'organisation_status_deactivated'|trans }}'"></a></li>
                    </ul>
                </div>
            </td>
            <td><a href="#" @click.prevent="deleteOrganisation(organisation, $event)">{{ 'organisation_delete'|trans }}</a></td>
        </tr>
    </script>
</section>

<script>
    $(function () {
        var apiEndPoint = '/api/v1/organisations';
        var apiKey = '{{ apiKey }}';

        Vue.component('organisation-item', {
            template: "#organisation-item-template",

            props: ['organisation'],

            methods: {
                toggleOrganisationStatus: function (organisation) {

                    var organisationStatusEndPoint = '';
                    var newStatus = '';

                    if (this.isActive(organisation)) {
                        organisationStatusEndPoint += apiEndPoint + '/' + organisation.id + '/disapproval';
                        newStatus = "disapproved";
                    } else {
                        organisationStatusEndPoint += apiEndPoint + '/' + organisation.id + '/approval';
                        newStatus = "approved";
                    }

                    $.ajax({
                        method: 'POST',
                        url: organisationStatusEndPoint,
                        beforeSend: function (xhr) {
                            xhr.setRequestHeader('Authorization', 'token ' + apiKey);
                        }
                    }).done(function () {
                        organisation.status = newStatus;
                    });
                },

                deleteOrganisation: function (organisation, event) {
                    var url = apiEndPoint + '/' + organisation.id;

                    $.ajax({
                        method: 'DELETE',
                        url: url,
                        beforeSend: function (xhr) {
                            xhr.setRequestHeader('Authorization', 'token ' + apiKey);
                        }
                    }).done(function () {
                        $(event.target).closest('tr').remove();
                    });
                },

                isActive: function (organisation) {
                    return organisation.status === "approved";
                },

                isPending: function (organisation) {
                    return organisation.status === "pending";
                },

                getStatusTranslation: function (organisation) {
                    switch (organisation.status) {
                        case "approved":
                            return '{{ 'organisation_status_activated'|trans }}';
                        case "disapproved":
                            return '{{ 'organisation_status_deactivated'|trans }}';
                        case "pending":
                            return '{{ 'organisation_status_pending'|trans }}';
                    }
                },
            }
        });

        new Vue({
            el: '#organisations',

            {# values initialization #}
            data: {
                allItems: [],
                currentPageItems: [],

                pagination: {
                    pagesCount: 0,
                    pageItemsCount: 10,
                    itemsTotal: 0,
                    currentPage: 1
                },
            },

            methods: {

                {# pagination #}

                setCurrentPage: function (newPage) {
                    this.pagination.currentPage = newPage;
                },

                refreshPagination: function () {
                    this.pagination.pagesCount = Math.ceil(this.pagination.itemsTotal / parseInt(this.pagination.pageItemsCount));
                },

                refreshCurrentPageItems: function () {
                    var startIndex = ((this.pagination.currentPage - 1) * parseInt(this.pagination.pageItemsCount));
                    var endIndex = startIndex + parseInt(this.pagination.pageItemsCount);

                    this.currentPageItems =  this.allItems.slice(startIndex, endIndex);
                },

                refreshList: function () {
                    var self = this;

                    {# request all organisations only once #}
                    if (this.allItems.length) {
                        return;
                    }

                    $.ajax({
                        method: 'GET',
                        url: apiEndPoint,
                        beforeSend: function (xhr) {
                            xhr.setRequestHeader('Authorization', 'token ' + apiKey);
                        }
                    }).done(function (data) {
                        self.allItems = data._embedded.organisations;
                        self.pagination.itemsTotal = data.total;

                        {# update pagination #}
                        self.refreshPagination();
                        self.refreshCurrentPageItems();
                    });
                },
            },

            watch: {
                "pagination.pageItemsCount": function () {
                    this.refreshPagination();
                    this.refreshCurrentPageItems();
                },

                "pagination.currentPage": function () {
                    this.refreshPagination();
                    this.refreshCurrentPageItems();
                }
            },

            mounted: function () {
                this.refreshList();
            }
        });
    });
</script>
