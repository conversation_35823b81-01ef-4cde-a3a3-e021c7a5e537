<section id="organisation" class="bo-page-content" v-cloak>
    <h2>{{ 'organisation_edition'|trans }}: <span v-text="organisation.name"></span></h2>

    <form @submit.prevent="onSubmit" id="organisation_edit_form">
        <fieldset>
            <legend>{{ 'organisation_information'|trans }}</legend>

            {# organisation name #}
            <div class="form-group organisation-info">
                <label for="organisation-name">{{ 'organisation_name'|trans }}</label>
                <input id="organisation-name" type="text" class="form-control" v-model="organisation.name">
            </div>

            {# organisation business name #}
            <div class="form-group organisation-info">
                <label for="organisation-business-name">{{ 'organisation_business_name'|trans }}</label>
                <input id="organisation-business-name" type="text" class="form-control" v-model="organisation.businessName">
            </div>

            {# organisation business unit name #}
            <div class="form-group organisation-info">
                <label for="organisation-business-unit-name">{{ 'organisation_business_unit_name'|trans }}</label>
                <input id="organisation-business-unit-name" type="text" class="form-control" v-model="organisation.businessUnitName">
            </div>

            {# organisation business unit code #}
            <div class="form-group organisation-info">
                <label for="organisation-business-unit-code">{{ 'organisation_business_unit_code'|trans }}</label>
                <input id="organisation-business-unit-code" type="text" class="form-control" v-model="organisation.businessUnitCode">
            </div>

            {# organisation addresses #}
            <h3>{{ 'organisation_billing_address_address'|trans }}</h3>
            {% include '@App/backend/organisation/_address.html.twig' with {'addressType': "address"} %}

            <h3>{{ 'organisation_shipping_address_address'|trans }}</h3>
            {% include '@App/backend/organisation/_address.html.twig' with {'addressType': "shippingAddress"} %}
        </fieldset>

        <fieldset>
            <legend>{{ 'organisation_administrators_information'|trans }}</legend>
            <table class="table">
                <thead>
                    <tr>
                        <th><input type="checkbox"></th>
                        <th>{{ 'id'|trans }}</th>
                        <th>{{ 'organisation_administrator_name'|trans }}</th>
                        <th>{{ 'organisation_administrator_email'|trans }}</th>
                    </tr>
                </thead>

                <tbody>
                    <tr v-for="user in adminUsers">
                        <td><input type="checkbox"></td>
                        <td><a :href="getUserUrl(user.id)" v-text="user.id"></a></td>
                        <td><a :href="getUserUrl(user.id)" v-text="user.firstName + ' ' + user.lastName"></a></td>
                        <td><a :href="getUserUrl(user.id)" v-text="user.email"></a></td>
                    </tr>
                </tbody>
            </table>
        </fieldset>
    </form>
</section>

<script>
    $(function () {
        const organisationEndPoint = '/api/v1/organisations/' + '{{ organisationId }}';
        const groupsEndPoint = organisationEndPoint + '/groups';
        const apiKey = '{{ apiKey }}';

        new Vue({
            delimiters: ['${', '}'],

            el: '#organisation',

            data: {
                organisation: {
                    address: {
                        address: '',
                        additionalAddress: '',
                        zipCode: '',
                        city: '',
                        country: ''
                    },
                    shippingAddress: {
                        address: '',
                        additionalAddress: '',
                        zipCode: '',
                        city: '',
                        country: ''
                    },
                    businessName: '',
                    businessUnitCode: '',
                    businessUnitName: '',
                    name: '',
                    siret: '',
                    vatNumber: ''
                },
                adminGroup: null,
                adminUsers: []
            },

            methods: {

                {# get organisation data to display #}
                {# ================================ #}

                getOrganisationData: function () {
                    this.getOrganisation()
                        .then(this.getAdminGroup)
                        .then(this.getAdminUsers);
                },

                getOrganisation: function () {
                    var self = this;

                    return $.ajax({
                        method: 'GET',
                        url: organisationEndPoint,
                        beforeSend: function (xhr) {
                            xhr.setRequestHeader('Authorization', 'token ' + apiKey);
                        }
                    }).done(function (data) {
                        self.organisation = data;
                    });
                },

                getAdminGroup: function () {
                    var self = this;

                    return $.ajax({
                        method: 'GET',
                        url: groupsEndPoint,
                        beforeSend: function (xhr) {
                            xhr.setRequestHeader('Authorization', 'token ' + apiKey);
                        }
                    }).done(function (data) {
                        var groups = data._embedded.groups;

                        for (var key in groups) {
                            if (groups[key].hasOwnProperty('type') && groups[key].type === 'admin' ) {
                                self.adminGroup = groups[key];
                                break;
                            }
                        }
                    });
                },

                getAdminUsers: function () {
                    var self = this;
                    var adminUsersEndPoint = '/api/v1/organisations/groups/' + this.adminGroup.id + '/users';

                    return $.ajax({
                        method: 'GET',
                        url: adminUsersEndPoint,
                        beforeSend: function (xhr) {
                            xhr.setRequestHeader('Authorization', 'token ' + apiKey);
                        }
                    }).done(function (data) {
                        self.adminUsers = data._embedded.users;
                    });
                },


                {# helpers #}
                {# ======= #}

                getUserUrl: function (userId) {
                    return '{{ app.request.baseUrl }}?dispatch=profiles.update&user_id=' + userId;
                },

                onSubmit: function () {
                    this.updateOrganisation();
                    this.updateOrganisationAddresses();
                    return false;
                },
                updateOrganisation: function() {
                    const self = this;
                    $.ajax({
                        method: 'PUT',
                        url: "{{ url('api_organisation_update', {'organisationId': organisationId}) }}",
                        data: {
                            businessName: self.organisation.businessName,
                            businessUnitCode: self.organisation.businessUnitCode,
                            businessUnitName: self.organisation.businessUnitName,
                            name: self.organisation.name,
                            siret: self.organisation.siret,
                            vatNumber: self.organisation.vatNumber
                        },
                        beforeSend: function (xhr) {
                            xhr.setRequestHeader('Authorization', 'token ' + apiKey);
                        }
                    }).success(function(data) {
                        $.ceNotification('show', {
                            type: 'N',
                            title: '{{ 'notice'|trans|e('js') }}',
                            message: '{{ 'text_changes_saved'|trans|e('js') }}'
                        });
                    });
                },
                updateOrganisationAddresses: function() {
                    const self = this;
                    $.ajax({
                        method: 'PUT',
                        url: "{{ url('api_organisation_update_addresses', {'organisationId': organisationId}) }}",
                        data: {
                            address: self.organisation.address,
                            shippingAddress: self.organisation.shippingAddress,
                        },
                        beforeSend: function (xhr) {
                            xhr.setRequestHeader('Authorization', 'token ' + apiKey);
                        }
                    }).success(function(data) {
                        $.ceNotification('show', {
                            type: 'N',
                            title: '{{ 'notice'|trans|e('js') }}',
                            message: '{{ 'text_changes_saved'|trans|e('js') }}'
                        });
                    });
                }
            },

            mounted: function () {
                this.getOrganisationData();
            }
        });
    });
</script>
