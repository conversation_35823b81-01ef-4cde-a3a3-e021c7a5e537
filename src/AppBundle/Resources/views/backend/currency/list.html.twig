<form action="" method="post" name="manage_currencies_form">

    <div class="pull-right adv-buttons" id="">
        <div class="btn-group ">
        </div>
    </div>

    {% if currencies | length %}
        <table class="table table-middle">
            <thead>
            <tr>
                <th colspan="2">{{ 'user_currency'|trans }}</th>
                <th>{{ 'currency_rate'|trans }}</th>
                <th>{{ 'order_attachments.table_header.updated_at'|trans }}</th>
                <th class="right nowrap">{{ 'status'|trans }}</th>
            </tr>
            </thead>
            <tbody>
            {% if mainCurrency is not null%}
                <tr>
                    <td width="15%"><span class="btn-success text-white btn-small">{{ 'primary_currency'|trans }}</span></td>
                    <td>
                        <span class="align-right">{{ mainCurrency.code }}</span>
                    </td>
                    <td>
                        {{ mainCurrency.exchangeRateFloatValue|number_format(6) }}
                    </td>
                    <td>
                        {{ mainCurrency.updatedAt is null ? "-" : mainCurrency.updatedAt|date('d M Y H:i') }}
                    </td>
                    <td class="right nowrap">
                        {% if mainCurrency.enabled %}
                            {{  'active'|trans }}
                        {% else %}
                            {{  'disabled'|trans }}
                        {% endif %}
                    </td>
                </tr>
            {% endif %}
            {% for currency in currencies %}
                {% if currency.code != mainCurrency.code %}
                    <tr class="cm-row-status-{{ currency.enabled ? 'a' : 'd' }}">
                        <td></td>
                        <td class="row-status">
                            {{ currency.code }}
                        </td>
                        <td class="row-status">
                            {{ currency.exchangeRateFloatValue|number_format(6) }}
                        </td>
                        <td class="row-status">
                            {{ currency.updatedAt is null ? "-" : currency.updatedAt|date('d M Y H:i') }}
                        </td>
                        <td class="right nowrap">
                            <div class="cm-popup-box dropdown ">
                                <a id="sw_select_2_wrap" class="btn-text dropdown-toggle cm-combination" data-toggle="dropdown">
                                    {% if currency.enabled %}
                                        {{  'active'|trans }}
                                    {% else %}
                                        {{  'disabled'|trans }}
                                    {% endif %}
                                    <span class="caret"></span>
                                </a>
                                <ul class="dropdown-menu">
                                    <li class="{% if currency.enabled %} disabled {% endif %}">
                                        <a class="status-link-a {% if currency.enabled %} active {% endif %}"
                                           href="{{ path('currency_update_status', { code : currency.code, status : '1' }) }}"
                                           onclick="return fn_check_object_status(this, 'a', '');"
                                           data-ca-event="ce.update_object_status_callback">
                                            {{  'active'|trans }}
                                        </a>
                                    </li>
                                    <li class="{% if not currency.enabled %} disabled {% endif %}">
                                        <a class="status-link-d cm-ajax {% if not currency.enabled %} active {% endif %}"
                                           href="{{ path('currency_update_status', { code : currency.code, status : '0' }) }}"
                                           onclick="return fn_check_object_status(this, 'd', '');"
                                           data-ca-event="ce.update_object_status_callback">
                                            {{  'disabled'|trans }}
                                        </a>
                                    </li>
                                    <li class="divider"></li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                {% endif %}
            {% endfor %}
            </tbody>
        </table>
    {% else %}
        <div>{{ 'no_data' | trans }}</div>
    {% endif %}
</form>

<script type="text/javascript" src="/js/tygh/select_popup.js"></script>
