<nav class="pagination-wrapper" v-if="pagination.pagesCount > 1">
    <div class="pagination">
        <ul>
            {# previous page button #}
            <li :class="{'disabled': pagination.currentPage == 1}">
                <a href="#"  @click.prevent="setCurrentPage(pagination.currentPage - 1)" aria-label="{{ 'previous'|trans }}"><span aria-hidden="true">&laquo; {{ 'previous'|trans }}</span></a>
            </li>

            {# first page #}
            <li v-if="pagination.currentPage > 1">
                <a href="#" @click.prevent="setCurrentPage(1)">1</a>
            </li>

            {# ellipsis #}
            <li v-if="pagination.currentPage > 3 && pagination.pagesCount > 3"><span>...</span></li>

            {# page just before current page #}
            <li v-if="pagination.currentPage > 2">
                <a href="#" @click.prevent="setCurrentPage(pagination.currentPage - 1)" v-text="pagination.currentPage - 1"></a>
            </li>

            {# current page #}
            <li v-for="i in pagination.pagesCount" v-if="pagination.currentPage == i" class="active">
                <a href="#" v-text="i"></a>
            </li>

            {# page just after current page #}
            <li v-if="pagination.currentPage <  pagination.pagesCount - 1">
                <a href="#" @click.prevent="setCurrentPage(pagination.currentPage + 1)" v-text="pagination.currentPage + 1"></a>
            </li>

            {# ellipsis #}
            <li v-if="pagination.currentPage < pagination.pagesCount - 2 && pagination.pagesCount > 3"><span>...</span></li>

            {# last page #}
            <li v-if="pagination.currentPage < pagination.pagesCount">
                <a href="#" @click.prevent="setCurrentPage(pagination.pagesCount)" v-text="pagination.pagesCount"></a>
            </li>

            {# next page button #}
            <li :class="{'disabled': pagination.currentPage == pagination.pagesCount}">
                <a href="#" @click.prevent="setCurrentPage(pagination.currentPage + 1)" aria-label="{{ 'next'|trans }}"><span aria-hidden="true">{{ 'next'|trans }} &raquo;</span></a>
            </li>
        </ul>
    </div>
</nav>
