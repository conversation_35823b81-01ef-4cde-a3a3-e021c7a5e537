{% if warning %}
    <div class="alert alert-warning" role="alert">
        {{ 'notifications_config_synchronization_warning'|trans }}
        &nbsp;&nbsp;&nbsp;&nbsp;
        <a href="/admin.php/admin/notification/list?synchronize=1" class="btn btn-secondary">
            {{ "synchronize_notifications_config"|trans }}
        </a>
    </div>
{% endif %}

<form action=""  method="post" name="email_notifications_manage_form" id="email_notifications_manage_form">
    {% if events | length %}
        <table class="table table-middle" id="table-email-notifications">
            <thead>
                <tr>
                    <th style="text-align: center;">{{ 'enabled'|trans }}</th>
                    <th>{{ 'notifications'|trans }}</th>
                </tr>
            </thead>
            <tbody>
            {% for event in events %}
                <tr>
                    <td style="text-align: center;">
                        <input type="checkbox" name="notifications[{{ event.id }}]" {{ event.checked }}>
                    </td>
                    <td>
                        <a href="{{ path('admin_Notification_trigger', {'event': event.class}) }}">{{ event.description|trans }}</a>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    {% else %}
        <div>{{ 'email_notifications_empty_list' | trans }}</div>
    {% endif %}

</form>

<script type="text/javascript">
    $('#email_notifications_manage_form').submit(function(event) {
        if (confirm('{{ 'warning_notifications_update' | trans }}')) {
            return;
        }
        event.preventDefault();
        setTimeout(() => {$('#waiting_box').remove();}, 100);
    });
</script>
