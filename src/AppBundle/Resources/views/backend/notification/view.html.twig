{% if enabled == false %}
    <div class="alert alert-warning">
        <strong>{{ 'notification_disabled'|trans }}</strong>
    </div>
{% endif %}

<h3>{{ title|trans }}</h3>

<p><a href="{{ path('admin_Notification_list') }}">{{ 'back_to_the_notification_list'|trans }}</a></p>

{# This is a ugly hack to add the `cm-processed-form` class to the form #}
{# That way CS Cart will not ask for confirmation when leaving/refreshing the page #}
{{ form(form, { attr: { class: 'cm-processed-form' }}) }}

{% if resultMail is not empty %}
    <h4>{{ 'emails'|trans }}</h4>
{% endif %}
{% set i=0 %}
{% for item in resultMail %}
    <p>{{ 'subject'|trans }}: {{ item.subject }}</p>
    <iframe id="notificationResult{{ i }}" width="100%" scrolling="no"></iframe>

    <script>
        $('#notificationResult{{ i }}').contents().find('html').html("{{ item.body|e('js') }}");
        $('#notificationResult{{ i }}').height($('#notificationResult{{ i }}').contents().find('html').height());
    </script>
    {% set i=i+1 %}
{% endfor %}

<script>
    $('[data-prototype]').append('<a href="#" class="collection-add-link">{{ 'add'|trans }}</a>');

    $('.collection-add-link').click(function(e) {
        e.preventDefault();

        var me = $(this);
        me.before(me.parent().data('prototype').replace(/__name__/g, '').replace(/label__/g, ''));
    });
</script>

