<div class="exception-body login-content">

    <h2>{{ exception_status }}</h2>

    <h3>
        {% if exception_status == "403" %}
            {{ 'access_denied'|trans|raw }}
        {% elseif exception_status == "404" %}
            {{ 'page_not_found'|trans|raw }}
        {% else %}
            {{ 'an_error_occured'|trans|raw }}
        {% endif %}
    </h3>

    <div class="exception-content">
        {% if exception_status == "403" %}
            <h4>{{ 'access_denied_text'|trans|raw }}</h4>
        {% elseif exception_status == "404" %}
            <h4>{{ 'page_not_found_text'|trans|raw }}</h4>
        {% endif %}

        <ul class="exception-menu">
            <li id="go_back"><a class="cm-back-link">{{ 'go_back'|trans|raw }}</a></li>
            <li><a href="{{ path('home') }}">{{ 'go_to_the_admin_homepage'|trans|raw }}</a></li>
        </ul>

        <script type="text/javascript">
            //<![CDATA[
            {literal}
            Tygh.$(document).ready(function() {
                var $ = Tygh.$;
                $.each($.browser, function(i, val) {
                    if ((i == 'opera') && (val == true)) {
                        if (history.length == 0) {
                            $('#go_back').hide();
                        }
                    } else {
                        if (history.length == 1) {
                            $('#go_back').hide();
                        }
                    }
                });
            });
            {/literal}
            //]]>
        </script>
    </div>

</div>
