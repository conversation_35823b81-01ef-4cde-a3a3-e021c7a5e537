<h3>{{ 'multi_vendor_product_links'|trans }} {% if multiVendorProduct.name is defined %}"{{ multiVendorProduct.name }}"{% endif %}</h3>

{# Passe l'interface en read-only si on est en mode matching automatique #}
{% set linkEnabled = not feature('multi_vendor_product_auto_link') %}

{% if links is not empty %}
    {% if linkEnabled %}
        <button class="btn btn-danger cm-confirm test-mvp-remove-links-submit" type="submit" name="allow_detach" value="Y">
            <i class="icon-trash"></i> {{ 'delete_selected'|trans }}
        </button>
    {% endif %}
    <table class="table table-middle">
        <thead>
        <tr>
            {% if linkEnabled %}
                <th>
                    <input type="checkbox" onchange="$('.checkbox-remove-mvp-link').prop('checked', this.checked);" class="test-mvp-remove-all-links" />
                </th>
            {% endif %}
            <th>{{ 'Name'|trans }}</th>
            <th>{{ 'vendor'|trans }}</th>
            <th>{{ 'sku'|trans }}</th>
            <th>{{ 'w_supplier_ref'|trans }}</th>
            {% if linkEnabled %}
                <th>{{ 'Actions'|trans }}</th>
            {% endif %}
        </tr>
        </thead>
        <tbody>
        {% for link in links %}
            {% set url = cscart_url('products.update?product_id=' ~ link.productId) %}
            <tr class="cm-row-status-{{ link.productStatus|lower }}">
                {% if linkEnabled %}
                    <td>
                        <input type="checkbox" name="product_ids_to_detach[]" value="{{ link.productId }}" class="checkbox-remove-mvp-link" />
                    </td>
                {% endif %}
                <td class="row-status">
                    <a href="{{ url }}">{{ link.productName }}</a>
                </td>
                <td class="row-status">
                    <a href="{{ cscart_url('companies.update&company_id=' ~ link.companyId) }}">{{ link.companyName }}</a>
                </td>
                <td class="row-status">
                    <a href="{{ url }}">{{ link.productCode }}</a>
                </td>
                <td class="row-status">
                    <a href="{{ url }}">{{ link.supplierRef }}</a>
                </td>
                {% if linkEnabled %}
                    <td>
                        <a class="cm-confirm test-remove-mvp-link-{{ link.productId }}" href="{{ path('admin_MultiVendorProduct_removeLink', {'mvp_id': multiVendorProduct.id, 'product_id': link.productId, 'csrf': csrf_token('remove_mvp_link')}) }}">{{ 'Delete'|trans }}</a>
                    </td>
                {% endif %}
            </tr>
        {% endfor %}
        </tbody>
    </table>
{% else %}
    <div>{{ 'multi_vendor_product_links_empty_list' | trans }}</div>
{% endif %}

{% if linkEnabled %}
    <h3>{{ 'add_products'|trans }}</h3>

    <input type="text" id="mvp_product_picker_query" placeholder="{{ 'product_name'|trans }}"/>&nbsp;
    <button id="mvp_product_picker_search" type="button" class="btn">{{ 'search'|trans }}</button>

    <script>
        $('#mvp_product_picker_query').keydown(function(e) { if (e.keyCode == 13) { e.preventDefault(); return false; } });

        $('#mvp_product_picker_search').on('click', function () {
            query = $('#mvp_product_picker_query').val().trim();
            $('#mvp_product_picker_results_container').hide();
            if (query == '') {
                return false;
            }
            $.post('{{ cscart_url('products.picker_json?pname=Y') }}&q=' + query, {}, function (data) {
                $('#mvp_product_picker_results').html('');
                for (i in data) {
                    product = data[i];

                    // Don't display products that are already linked
                    if ([0{% for link in links %},{{ link.productId }}{% endfor %}].indexOf(parseInt(product.product_id)) !== -1) {
                        continue
                    }
                    $('#mvp_product_picker_results').append('<tr><td><input type="checkbox" id="product_picker_' + product.product_id + '" name="product_ids_to_attach[]" value="' + product.product_id + '" /></td><td><label for="product_picker_' + product.product_id + '">' + product.product + '</label></td><td>' + product.product_code + '</td></tr>')
                }

                if ($('#mvp_product_picker_results').html() == '') {
                    $('#mvp_linked_product_no_result').show();
                    $('#mvp_product_picker_results_container').hide();
                } else {
                    $('#mvp_linked_product_no_result').hide();
                    $('#mvp_product_picker_results_container').show();
                }

            }, 'json');
            return false
        });
    </script>

    <table class="table table-middle" id="mvp_product_picker_results_container" style="display:none;">
        <thead>
        <tr>
            <th>
                <button class="btn cm-tooltip" type="submit">
                    <i class="icon-plus"></i> {{ 'add_selected'|trans }}
                </button>
            </th>
            <th>{{ 'name'|trans }}</th>
            <th>{{ 'sku'|trans }}</th>
        </tr>
        </thead>
        <tbody id="mvp_product_picker_results"></tbody>
    </table>

    <div id="mvp_linked_product_no_result" style="display:none; margin-top: 20px;">{{ 'multi_vendor_product_products_to_link_empty_list' | trans }}</div>
{% endif %}
