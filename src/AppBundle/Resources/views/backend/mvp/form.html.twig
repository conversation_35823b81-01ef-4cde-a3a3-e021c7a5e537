<form action="{{ path('admin_MultiVendorProduct_save') }}" method="POST" name="mvp_product_update_form" enctype="multipart/form-data" class="form-horizontal form-edit">
    {% if multiVendorProduct.id is defined %}
        <input type="hidden" name="id" value="{{ multiVendorProduct.id }}"/>
    {% endif %}

    <script type="text/javascript" src="/js/tygh/tabs.js?ver=4.0.2"></script>
    <div class="cm-j-tabs cm-track tabs">
        <ul class="nav nav-tabs">
            <li id="general" class="cm-js {% if selectedSection is null %} active {% endif %}">
                <a>{{ "general"|trans }}</a>
            </li>
            <li id="video" class="cm-js">
                <a>{{ "video"|trans }}</a>
            </li>
            {% if attributes is not empty %}
            <li id="attributes" class="cm-js">
                <a>{{ "feature"|trans }}</a>
            </li>
            {% endif %}
            <li id="free_features" class="cm-js">
                <a>{{ "free_features"|trans }}</a>
            </li>
            <li id="links" class="cm-js">
                <a>{{ "products"|trans }}</a>
            </li>
            <li id="reviews" class="cm-js {% if selectedSection == 'reviews' %} active {% endif %}">
                <a>{{ "reviews"|trans }}</a>
            </li>
        </ul>
    </div>

    <div class="cm-tabs-content">
        <div id="content_general">
            {% include "@App/backend/mvp/form_general.html.twig" %}
        </div>
        {% if attributes is not empty %}
        <div id="content_attributes" class="hidden">
            {% include "@App/backend/mvp/form_attributes.html.twig" %}
        </div>
        {% endif %}

        {# Free attributes tab #}
        {{ render_smarty_legacy("views/products/components/products_update_free_features.tpl", {product_data:{free_features:freeAttributes}}) }}

        <div id="content_links" class="hidden">
            {% include "@App/backend/mvp/form_links.html.twig" %}
        </div>

        <div id="content_reviews" class="hidden">
            {% include '@App/backend/mvp/form_reviews.html.twig' %}
        </div>

        <div id="content_video" class="hidden">
            {% include "@App/backend/mvp/form_video.html.twig" %}
        </div>
    </div>
</form>


<div id="videoModal" class="modal fade hidden" role="dialog" style="width:500px">
    <div class="modal-dialog">

        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">{{ 'upload_a_video'|trans }}</h4>
            </div>
            <div class="modal-body">
                {{ render(controller('Wizacha\\AppBundle\\Controller\\VideoController::videoUploadFormAction')) }}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">{{ 'close' |trans }}</button>
            </div>
        </div>
    </div>
</div>

