{# Some clients reviews #}
<section class="product-reviews">
    <div class="container">
        <div class="reviews">
            {% if posts %}
                {% for i, post in posts %}

                    {# Display the review #}
                    <div class="review">

                        <div class="author">
                            {{ post.name|trim|default('Author'|trans) }}
                        </div>

                        <div class="message">
                            <p data-toggle="tooltip" data-placement="bottom" title="{{ post.message }}">{{ post.message|truncate(250, false, '...') }}</p>
                        </div>

                        <div class="wrapper">
                            <div class="status">
                                {% if post.active %}
                                    <span class="label label-success">{{ 'approved'|trans }}</span>
                                    <a href="{{ path('admin_MultiVendorProduct_decline_review', {'mvpId': multiVendorProduct.id, 'reviewId': post.id}) }}" class="change-status" title="{{ 'approve'|trans }}"><span class="glyphicon glyphicon-thumbs-down"></span></a>
                                {% else %}
                                    <span class="label label-important">{{ 'not_approved'|trans }}</span>
                                    <a href="{{ path('admin_MultiVendorProduct_approve_review', {'mvpId': multiVendorProduct.id, 'reviewId': post.id}) }}" class="change-status" title="{{ 'disapprove'|trans }}"><span class="glyphicon glyphicon-thumbs-up"></span></a>
                                {% endif %}
                            </div>

                            <div class="info">
                                <span class="date">{{ post.datetime.timestamp|date('d/m/Y') }}</span>

                                {# Review rating with shiny stars #}
                                <div class="review-rating">
                                    {% for j in 1..5 %}
                                        {% if j <= post.ratingValue %}
                                            <i class="glyphicon glyphicon-star on"></i>
                                        {% else %}
                                            <i class="glyphicon glyphicon-star-empty off"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            {% endif %}
        </div>
    </div>
</section>
