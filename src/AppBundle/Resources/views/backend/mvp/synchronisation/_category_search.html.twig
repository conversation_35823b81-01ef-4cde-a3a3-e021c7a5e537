<div class="modal fade" id="{{ modalId }}" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">

            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="{{ 'close'|trans }}"><span
                            aria-hidden="true">&times;</span></button>
                <h3 class="modal-title">{{ modalTitle }}</h3>
            </div>

            <div class="modal-body">
                <ul class="select-categories">
                    <li v-for="category in {{ itemContainer }}">
                        <label>
                            <input class="category-select" type="checkbox" :value="category.id"
                                   :checked="category.isSelected" v-model="category.isSelected">
                            <a class="item-name" role="button" data-toggle="collapse"
                               aria-expanded="false" :aria-controls="'{{ modalId }}-' + category.id"
                               :href="'#{{ modalId }}-' + category.id" v-text="category.name"
                               v-if="category.subcategories"
                            ></a>
                            <span class="item-name" v-text="category.name" v-else></span>
                        </label>

                        <ul :id="'{{ modalId }}-' + category.id" class="collapse">
                            <li class="categories-sublevel" v-for="category in category.subcategories">
                                <label>
                                    <input class="category-select" type="checkbox" :value="category.id"
                                           :checked="category.isSelected" v-model="category.isSelected">
                                    <a class="item-name" role="button" data-toggle="collapse"
                                       aria-expanded="false"
                                       :aria-controls="'{{ modalId }}-' + category.id"
                                       :href="'#{{ modalId }}-' + category.id" v-text="category.name"
                                       v-if="category.subcategories"
                                    ></a>
                                    <span class="item-name" v-text="category.name" v-else></span>
                                </label>

                                <ul :id="'{{ modalId }}-' + category.id" class="collapse">
                                    <li class="categories-sublevel" v-for="category in category.subcategories">
                                        <label>
                                            <input class="category-select" type="checkbox" :value="category.id"
                                                   :checked="category.isSelected" v-model="category.isSelected">
                                            <a class="item-name" role="button" data-toggle="collapse"
                                               aria-expanded="false"
                                               :aria-controls="'{{ modalId }}-' + category.id"
                                               :href="'#{{ modalId }}-' + category.id" v-text="category.name"
                                               v-if="category.subcategories"
                                            ></a>
                                            <span class="item-name" v-text="category.name" v-else></span>
                                        </label>

                                        <ul :id="'{{ modalId }}-' + category.id" class="collapse">
                                            <li class="categories-sublevel" v-for="category in category.subcategories">
                                                <label>
                                                    <input class="category-select" type="checkbox" :value="category.id"
                                                           :checked="category.isSelected" v-model="category.isSelected">
                                                    <span class="item-name" v-text="category.name"></span>
                                                </label>
                                            </li>
                                        </ul>
                                    </li>
                                </ul>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-dismiss="modal">{{ 'close'|trans }}</button>
            </div>
        </div>
    </div>
</div>
