<div id="rules" v-cloak>
    <div class="container">

        {# Include rules #}
        <div class="inclusive-rules">
            <h4>{{ 'catalog_sync_including_rules'|trans }}&nbsp;:</h4>

            {# include mvps from category #}
            <div class="row">
                <div class="span5">
                    <p>{{ 'catalog_sync_regarding_category'|trans|raw }}&nbsp;:</p>
                </div>

                <div class="span4">
                    <div class="input-append">
                        <input type="text" :value="includedCategoryIds.length + ' {{ 'catalog_sync_selected_categories'|trans }}'" title="" disabled>
                        <button type="button" class="btn btn-default btn-lg" data-toggle="modal"
                                data-target="#includeCategories">
                            <span class="glyphicon glyphicon-edit"></span>
                        </button>
                    </div>
                </div>
            </div>

            {# include mvps from brand #}
            <div class="row">
                <div class="span5">
                    <p>{{ 'catalog_sync_regarding_brand'|trans|raw }}&nbsp;:</p>
                </div>

                <div class="span4">
                    <div class="input-append">
                        <input type="text" :value="includedBrands.length + ' {{ 'catalog_sync_selected_brands'|trans }}'" title="" disabled>
                        <button type="button" class="btn btn-default btn-lg" data-toggle="modal"
                                data-target="#includeBrands">
                            <span class="glyphicon glyphicon-edit"></span>
                        </button>
                    </div>
                </div>
            </div>

            {# include specific mvps #}
            <div class="row">
                <div class="span5">
                    <p>{{ 'catalog_sync_specific_products'|trans|raw }}&nbsp;:</p>
                </div>

                <div class="span4">
                    <div class="input-append">
                        <input type="text" :value="includedProducts.length + ' {{ 'catalog_sync_selected_mvps'|trans }}'" title="" disabled>
                        <button type="button" class="btn btn-default btn-lg" data-toggle="modal"
                                data-target="#includeProducts">
                            <span class="glyphicon glyphicon-edit"></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        {# Category selection modal #}
        {% include '@App/backend/mvp/synchronisation/_category_search.html.twig' with {modalId: "includeCategories", modalTitle: 'categories_to_include'|trans, itemContainer: "includedCategories" } %}

        {# Brand selection modal #}
        {% include '@App/backend/mvp/synchronisation/_item_search.html.twig' with {modalId: "includeBrands", modalTitle: 'brands_to_include'|trans, itemContainer: "includedBrands", modalPlaceholder: 'brand_name'|trans } %}

        {# MVP selection modal #}
        {% include '@App/backend/mvp/synchronisation/_item_search.html.twig' with {modalId: "includeProducts", modalTitle: 'products_to_include'|trans, itemContainer: "includedProducts", modalPlaceholder: 'product_name'|trans } %}

        {# Exclude rules #}
        <div class="exclusive-rules">
            <h4>{{ 'catalog_sync_excluding_rules'|trans }}&nbsp;:</h4>

            {# exclude mvps from category #}
            <div class="row">
                <div class="span5">
                    <p>{{ 'catalog_sync_exclude_regarding_category'|trans|raw }}&nbsp;:</p>
                </div>

                <div class="span4">
                    <div class="input-append">
                        <input type="text" :value="excludedCategoryIds.length + ' {{ 'catalog_sync_selected_categories'|trans }}'" title="" disabled>
                        <button type="button" class="btn btn-default btn-lg" data-toggle="modal"
                                data-target="#excludeCategories">
                            <span class="glyphicon glyphicon-edit"></span>
                        </button>
                    </div>
                </div>
            </div>

            {# exclude mvps from brand #}
            <div class="row">
                <div class="span5">
                    <p>{{ 'catalog_sync_exclude_regarding_brand'|trans|raw }}&nbsp;:</p>
                </div>

                <div class="span4">
                    <div class="input-append">
                        <input type="text" :value="excludedBrands.length + ' {{ 'catalog_sync_selected_brands'|trans }}'" title="" disabled>
                        <button type="button" class="btn btn-default btn-lg" data-toggle="modal"
                                data-target="#excludeBrands">
                            <span class="glyphicon glyphicon-edit"></span>
                        </button>
                    </div>
                </div>
            </div>

            {# exclude specific mvps #}
            <div class="row">
                <div class="span5">
                    <p>{{ 'catalog_sync_exclude_specific_products'|trans|raw }}&nbsp;:</p>
                </div>

                <div class="span4">
                    <div class="input-append">
                        <input type="text" :value="excludedProducts.length + ' {{ 'catalog_sync_selected_mvps'|trans }}'" title="" disabled>
                        <button type="button" class="btn btn-default btn-lg" data-toggle="modal"
                                data-target="#excludeProducts">
                            <span class="glyphicon glyphicon-edit"></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        {# Category selection modal #}
        {% include '@App/backend/mvp/synchronisation/_category_search.html.twig' with {modalId: "excludeCategories", modalTitle: 'categories_to_exclude'|trans, itemContainer: "excludedCategories" } %}

        {# Brand selection modal #}
        {% include '@App/backend/mvp/synchronisation/_item_search.html.twig' with {modalId: "excludeBrands", modalTitle: 'brands_to_exclude'|trans, itemContainer: "excludedBrands", modalPlaceholder: 'brand_name'|trans } %}

        {# MVP selection modal #}
        {% include '@App/backend/mvp/synchronisation/_item_search.html.twig' with {modalId: "excludeProducts", modalTitle: 'products_to_exclude'|trans, itemContainer: "excludedProducts", modalPlaceholder: 'product_name'|trans } %}

    </div>

    <div class="configuration-save">
        <div class="row">
            <div class="span4 offset5">
                <button type="button" class="btn btn-primary btn-lg pull-right" @click="saveRules">{{ 'save_configuration'|trans }}</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/vue/2.2.0/vue.js"></script>

<script>
    var apiKey = "{{ userApiKey|e('js') }}";
    var mvpSearchEndPoint = "{{ path('admin_multi_vendor_product_search') }}";
    var brandSearchEndPoint = "{{ path('admin_attribute_search_brand') }}";
    var saveEndPoint = "{{ path('admin_save_sync_rules') }}";

    var vm = new Vue({
        el: '#rules',

        delimiters: ['${', '}'],

        data: {
            includedCategories: [],
            includedBrands: [],
            includedProducts: [],

            excludedCategories: [],
            excludedBrands: [],
            excludedProducts: [],

            {# disposable variables #}
            itemRenderList: [],
            query: null
        },

        computed: {

            includedCategoryIds: function() {
                return this.getCategoryIds(this.includedCategories);
            },

            includedBrandIds: function() {
                return this.getItemIds(this.includedBrands);
            },

            includedProductIds: function() {
                return this.getItemIds(this.includedProducts);
            },

            excludedCategoryIds: function() {
                return this.getCategoryIds(this.excludedCategories);
            },

            excludedBrandIds: function() {
                return this.getItemIds(this.excludedBrands);
            },

            excludedProductIds: function() {
                return this.getItemIds(this.excludedProducts);
            }
        },

        methods: {
            searchItem: function(event) {

                var searchEndPoint = null;
                var modalId = $(event.target).closest('.modal').attr('id');
                if("includeProducts" === modalId || "excludeProducts" === modalId) {
                    searchEndPoint = mvpSearchEndPoint;
                } else if("includeBrands" === modalId || "excludeBrands" === modalId) {
                    searchEndPoint = brandSearchEndPoint;
                }

                {# start clean #}
                this.itemRenderList = [];

                $.ajax({
                    url: searchEndPoint + '?query=' + this.query,
                    headers: {"Authorization": "token " + apiKey},
                    dataType: "json",
                    success: function(response) {
                        for(var key in response.data) {
                            if(! response.data.hasOwnProperty(key)) continue;

                            var item = {};
                            item.id = key;
                            item.name = response.data[key];
                            vm.itemRenderList.push(item);
                        }
                    }});
            },

            isAlreadySelected: function(item, container) {
                return container.filter(function(containerItem) {
                        return containerItem.id == item.id;
                    }).length > 0;
            },

            addItemToSelection: function(item, container) {
                container.push(item);
            },

            removeItemFromSelection: function(item, container) {
                container.splice(container.indexOf(item), 1);
            },

            getCategoryIds: function(categories) {
                var ids = [];

                (function getIds(categories) {
                    categories.forEach(function(category) {
                        // call recursive function if current category contains sub-categories
                        if(category.subcategories) { getIds(category.subcategories); }

                        // save category's id if current category is selected
                        if(category.isSelected) { ids.push(category.id); }
                    });
                })(categories);

                return ids;
            },

            getItemIds: function(items) {
                var ids = [];

                items.forEach(function(item) {
                    ids.push(item.id);
                });

                return ids;
            },

            cleanQuery: function() {
                this.itemRenderList = [];
                this.query = null;
            },

            saveRules: function() {

                $('.configuration-save button').prop('disabled', true);

                var data = {
                    includedCategories: this.includedCategoryIds,
                    includedBrands: this.includedBrandIds,
                    includedProducts: this.includedProductIds,
                    excludedCategories: this.excludedCategoryIds,
                    excludedBrands: this.excludedBrandIds,
                    excludedProducts: this.excludedProductIds
                };

                $.ajax({
                    url: saveEndPoint,
                    headers: {"Authorization": "token " + apiKey},
                    method: "POST",
                    data: JSON.stringify(data)

                }).done(function() {
                    $.ceNotification('show', {
                        type: 'N',
                        title: '{{ 'notice'|trans|e('js') }}',
                        message: '{{ 'saved_rules_confirmation'|trans|e('js') }}'
                    });

                }).fail(function() {
                    $.ceNotification('show', {
                        type: 'E',
                        title: '{{ 'error'|trans|e('js') }}',
                        message: '{{ 'an_error_occured'|trans|e('js') }}'
                    });
                });
            }
        },

        mounted: function () {
            this.includedCategories = {{ includedCategories|json_encode|raw }};
            this.includedBrands = {{ includedBrands|json_encode|raw }};
            this.includedProducts = {{ includedProducts|json_encode|raw }};
            this.excludedCategories = {{ excludedCategories|json_encode|raw }};
            this.excludedBrands = {{ excludedBrands|json_encode|raw }};
            this.excludedProducts = {{ excludedProducts|json_encode|raw }};
        }
    });
</script>
