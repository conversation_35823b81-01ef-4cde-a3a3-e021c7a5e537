<div class="modal fade" id="{{ modalId }}" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">

            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="{{ 'close'|trans }}"><span
                            aria-hidden="true">&times;</span></button>
                <h3 class="modal-title">{{ modalTitle }}</h3>
            </div>

            <div class="modal-body">

                {# search form #}
                <form class="form-inline" @submit.prevent="searchItem">
                    <div class="form-input">
                        <input type="text" v-model="query" placeholder="{{ modalPlaceholder }}">
                        <button type="submit" class="btn btn-default">{{ 'search'|trans }}</button>
                    </div>
                </form>

                {# search results list #}
                <ul class="search-result">
                    <li class="striped" v-for="item in itemRenderList">
                        <span class="item-name" v-text="item.name"></span>
                        <button class="btn btn-default btn-mini btn-select pull-right" @click="addItemToSelection(item, {{ itemContainer }})" :disabled="isAlreadySelected(item, {{ itemContainer }})">{{ "add_to_selection"|trans }}</button>
                    </li>
                </ul>

                {# selected items #}
                <h4>{{ 'catalog_sync_selected_items'|trans }}&nbsp;:</h4>
                <ul class="selected-items">
                    <li v-for="item in {{ itemContainer }}">
                        <span class="glyphicon glyphicon-check"></span>
                        <span class="item-name" v-text="item.name"></span>
                        <span class="remove-item glyphicon glyphicon-trash pull-right"
                              @click="removeItemFromSelection(item, {{ itemContainer }})"></span>
                    </li>
                </ul>
            </div>

            {# modal dismiss button #}
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-dismiss="modal" @click="cleanQuery">{{ 'close'|trans }}</button>
            </div>
        </div>
    </div>
</div>
