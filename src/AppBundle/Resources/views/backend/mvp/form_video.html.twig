<h3>{{ 'video'|trans }}</h3>

<div>
    <script type="text/javascript">
        function removeVideo()
        {
            $('#id_input_hidden_video').val('');
            $('#current_video, #removeVideoButton').addClass('hide');
        }
    </script>


    <div class="clear cm-row-item">

        <div id="current_video" {% if multiVendorProduct.video.id == false %}class="hide"{% endif %}>
            {{ 'current_video'|trans }}:<br />
        <img src="{% if multiVendorProduct.video.id %}{{ multiVendorProduct.video.thumb }}{% endif %}" />
        </div>

        <br>
        <input type="hidden" name="video" value="{% if multiVendorProduct.video.id != false %}{{ multiVendorProduct.video.id }}{% endif %}" id="id_input_hidden_video" />
        <button type="button" class="{% if multiVendorProduct.video.id == false %}hide{% endif %} btn btn-danger" id="removeVideoButton" onclick="removeVideo()">{{ 'delete'|trans }}</button>
        <button type="button" class="btn" data-toggle="modal" data-target="#videoModal">{{ 'add_a_video'|trans }}</button>
    </div>
</div>
