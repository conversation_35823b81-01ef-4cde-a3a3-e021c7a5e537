<form action="{{ path('admin_MultiVendorProduct_batch_delete') }}" method="post" name="manage_mvproducts_form">
    <input type="hidden" name="csrf" value="{{ csrf_token('delete_mvp') }}">
    {% if feature('allow_mvp_sync_from_products') %}
    <div class="pull-left adv-buttons" id="tools_manage_products_adv_buttons">
        <div class="btn-group ">
            <a class="btn cm-tooltip" href="{{ path('admin_MultiVendorProduct_syncAllFromProduct', {'csrf': csrf_token('sync_all_mvp')}) }}">
                {{ 'sync_all_mvp_from_products'|trans }}
            </a>
        </div>
    </div>
    {% endif %}

    <div class="pull-right adv-buttons" id="tools_manage_products_adv_buttons">
        <div class="btn-group ">
            <a class="btn btn-primary cm-tooltip test-mvp-add" href="{{ path('admin_MultiVendorProduct_form') }}">
                <i class="icon-plus"></i>
            </a>
        </div>
    </div>

    {% if multiVendorProducts | length %}
        {% set paginationId = 'mvp_paginator' %}
        {% block pagination %}
            <div class="cm-pagination-container" id="{{ paginationId }}">
            <div class="pagination-wrap">
                {% if lastPage > 1 %}
                    <div class="pagination center">
                        <ul>
                            {% set paginationRightLeftOffset = 5 %}
                            {# `«` arrow  #}
                            <li {{ currentPage == 1 ? 'class="disabled"' }}>
                                <a href="{{ path('admin_MultiVendorProduct_list', {'search': currentSearch, 'page': (currentPage-1 < 1 ? 1 : currentPage-1)}) }}" data-ca-scroll=".cm-pagination-container" data-ca-target-id="{{ paginationId }}">«</a>
                            </li>

                            <li {{ currentPage == 1 ? 'class="disabled"' }}>
                                <a href="{{ path('admin_MultiVendorProduct_list', {'search': currentSearch, 'page': 1}) }}" data-ca-scroll=".cm-pagination-container" data-ca-target-id="{{ paginationId }}">1</a>
                            </li>

                            {% if lastPage > 2 %}
                                {# Render -5 <=> +5 page number ( 5 is paginationRightLeftOffset ) #}
                                {% for i in max(2, currentPage-paginationRightLeftOffset)..min(lastPage-1, currentPage+paginationRightLeftOffset) %}
                                    <li {{ currentPage == i ? 'class="active"' }}>
                                        <a href="{{ path('admin_MultiVendorProduct_list', {'search': currentSearch, 'items_per_page': items_per_page, 'page': i}) }}" data-ca-scroll=".cm-pagination-container" data-ca-target-id="{{ paginationId }}">
                                            {# show ... rather than page number if this is the 1st or last iteration AND this is not the current page AND this is not the last but one AND this is not the 2nd #}
                                            {% if i != currentPage and i != 2 and i != lastPage-1 and (loop.first or loop.last) %}
                                                ...
                                            {% else %}
                                                {{ i }}
                                            {% endif %}
                                        </a>
                                    </li>
                                {% endfor %}
                            {% endif %}

                            <li {{ currentPage == lastPage ? 'class="disabled"' }}>
                                <a href="{{ path('admin_MultiVendorProduct_list', {'search': currentSearch, 'items_per_page': items_per_page, 'page': lastPage}) }}" data-ca-scroll=".cm-pagination-container" data-ca-target-id="{{ paginationId }}">{{ lastPage }}</a>
                            </li>

                            {# `»` arrow #}
                            <li {{ currentPage == lastPage ? 'class="disabled"' }}>
                                <a href="{{ path('admin_MultiVendorProduct_list', {'search': currentSearch, 'items_per_page': items_per_page, 'page': (currentPage+1 <= lastPage ? currentPage+1 : currentPage)}) }}" data-ca-scroll=".cm-pagination-container" data-ca-target-id="{{ paginationId }}">»</a>
                            </li>
                        </ul>
                    </div>
                {% endif %}
            </div>

            <div class="pagination-desc pagination-centered">

                <div class="btn-group">

                    <span style="font-size: 13px;" class="pagination-total-items">{{ 'view_by' | trans }} : {{ total }} / </span>

                    <a class="btn-text dropdown-toggle" data-toggle="dropdown">{{ items_per_page }}<span class="caret"></span></a>

                    <ul id="{{ paginationId }}" class="dropdown-menu cm-smart-position">
                        <li>
                            {% for i in range(10, 100, 10) %}
                                <a data-ca-scroll=".cm-pagination-container" href="{{ path('admin_MultiVendorProduct_list', {'search': currentSearch, 'items_per_page': i, 'page': 1}) }}" data-ca-target-id="pagination_contents">{{ i }}</a>
                            {% endfor %}
                        </li>
                    </ul>

                </div>

            </div>

            <div class="pagination-centered cpt_selected_rows" style="display: none">
                {{ 'number_of_selected_items' | trans }} <span id="cpt_selected_rows">0</span>
            </div>
        </div>
        {% endblock %}

        <table class="table table-middle" id="table-mvp-products">
        <thead>
        <tr>
            <th>
                <div class="btn-group btn-checkbox cm-check-items">
                    <a href="" data-toggle="dropdown" class="btn btn-secondary dropdown-toggle">
                        <span class="caret pull-right"></span>
                    </a>
                    <label for="check_all">

                        <input id="check_all" type="checkbox" name="check_all" value="Y" title="Tout cocher/ Tout décocher" class="pull-left cm-check-items ">
                        <span></span>
                    </label>
                    <ul class="dropdown-menu">
                        <li><a class="cm-on">Tous</a></li>
                        <li><a class="cm-off">Aucun</a></li>
                    </ul>
                </div>
            </th>
            <th>{{ 'name'|trans }}</th>
            <th>{{ 'code'|trans }}</th>
            <th>{{ 'reference'|trans }}</th>
            <th>{{ 'actions'|trans }}</th>
            <th>{{ 'status'|trans }}</th>
        </tr>
        </thead>
        <tbody>
        {% for multiVendorProduct in multiVendorProducts %}
            {% set url = path('admin_MultiVendorProduct_form', {'id': multiVendorProduct.id}) %}
            <tr>
                <td class="left">
                    <label for="p{{ multiVendorProduct.id }}">
                        <input type="checkbox" name="mvproduct_ids[]" value="{{ multiVendorProduct.id }}" id="p{{ multiVendorProduct.id }}" class="checkbox cm-item cm-item-status-a">
                        <span></span>
                    </label>
                </td>
                <td>
                    <a href="{{ url }}">{{ multiVendorProduct.name }}</a>
                </td>
                <td>
                    <a href="{{ url }}">{{ multiVendorProduct.code }}</a>
                </td>
                <td>
                    <a href="{{ url }}">{{ multiVendorProduct.supplierReference }}</a>
                </td>
                <td>
                    <ul>
                        <li>
                            <a href="{{ path('admin_MultiVendorProduct_delete', {'id': multiVendorProduct.id, 'csrf': csrf_token('delete_mvp')}) }}">{{ 'Delete'|trans }}</a>
                        </li>
                    </ul>
                </td>
                <td>
                    {{ statuses[multiVendorProduct.status.__toString] }}
                </td>
            </tr>
        {% endfor %}
        </tbody>
    </table>

        {{ block('pagination') }}
    {% else %}
        <div>{{ 'multi_vendor_product_empty_list' | trans }}</div>
    {% endif %}
</form>
