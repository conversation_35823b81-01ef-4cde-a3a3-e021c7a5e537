{# @var multiVendorProduct \Wizacha\Marketplace\PIM\MultiVendorProduct\MultiVendorProduct #}
<div class="control-group">
    <label for="mvp_form_product_template_type" class="control-label cm-required">{{ 'product_template_type'|trans }} : </label>
    <div class="controls">
        <select id="mvp_form_product_template_type" name="productTemplateType">
            {% for template in templates %}
                {# @var template \Wizacha\Marketplace\PIM\Product\Template\Template #}
                <option value="{{ template.id }}"{% if multiVendorProduct.productTemplateType == template.id %} selected="selected"{% endif %}>{{ template|trans }}</option>
            {% endfor %}
        </select>
    </div>
</div>
<div class="control-group">
    <label for="mvp_form_name" class="control-label cm-required">{{ 'name'|trans }} : </label>
    <div class="controls">
        <input id="mvp_form_name" type="text" name="name" value="{{ multiVendorProduct.name|default }}" placeholder="{{ multiVendorProductPlaceholder.name|default }}"/>
    </div>
</div>
<div class="control-group">
    <label for="cmvp_form_category_ids" class="control-label cm-required">{{ 'categories'|trans }} : </label>
    <div class="controls">
        <script type="text/javascript" src="/js/tygh/picker.js?ver=4.0.2"></script>
        <div id="mvp_form_category" class="manager_listener cm-display-radio choose-category">
            <input id="cmvp_form_category_ids" type="hidden" class="cm-picker-value" name="category" value="{{ multiVendorProduct.category.id|default }}">
            <div class="input-append choose-input">
                <span class="cm-js-item ">
                    <div class="input-append">
                        <input class="cm-picker-value-description " type="text" value="{{ multiVendorProduct.category.name|default }}" id="category_ids" size="10" readonly style="background-image: url(&quot;data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR4nGP6zwAAAgcBApocMXEAAAAASUVORK5CYII=&quot;);"/>
                        <a id="opener_picker_mvp_form_category" href="{{ cscart_url('categories.picker&w_leaves_only=1&display=radio&data_id=mvp_form_category&get_tree=multi_level') }}" data-ca-target-id="content_mvp_form_category" class=" btn cm-dialog-opener add-on no-decoration">
                            <i class="icon-plus"></i>
                        </a>
                    </div>
                </span>
            </div>
        </div>
    </div>
</div>
<div class="control-group">
    <label for="mvp_form_code" class="control-label">{{ 'sku'|trans }} : </label>
    <div class="controls">
        <input id="mvp_form_code" type="text" name="code" value="{{ multiVendorProduct.code|default }}"/>
    </div>
</div>
<div class="control-group">
    <label for="mvp_form_supplier_reference" class="control-label">{{ 'w_supplier_ref'|trans }} : </label>
    <div class="controls">
        <input id="mvp_form_supplier_reference" type="text" name="supplierReference" value="{{ multiVendorProduct.supplierReference|default }}"/>
    </div>
</div>
<div class="control-group">
    <label for="mvp_form_slug" class="control-label">{{ 'seo_name'|trans }} : </label>
    <div class="controls">
        <input id="mvp_form_slug" type="text" name="slug" value="{{ multiVendorProduct.slug|default }}"/>
    </div>
</div>
<div class="control-group">
    <label for="mvp_form_seo_title" class="control-label">{{ 'page_title'|trans }} : </label>
    <div class="controls">
        <input id="mvp_form_seo_tile" type="text" name="seoTitle" value="{{ multiVendorProduct.seoTitle|default }}" placeholder="{{ multiVendorProductPlaceholder.seoTitle|default }}"/>
    </div>
</div>
<div class="control-group">
    <label for="mvp_form_seo_description" class="control-label">{{ 'meta_description'|trans }} : </label>
    <div class="controls">
        <input id="mvp_form_seo_description" type="text" name="seoDescription" value="{{ multiVendorProduct.seoDescription|default }}" placeholder="{{ multiVendorProductPlaceholder.seoDescription|default }}"/>
    </div>
</div>
<div class="control-group">
    <label for="mvp_form_seo_keywords" class="control-label">{{ 'meta_keywords'|trans }} : </label>
    <div class="controls">
        <input id="mvp_form_seo_keywords" type="text" name="seoKeywords" value="{{ multiVendorProduct.seoKeywords|default }}" placeholder="{{ multiVendorProductPlaceholder.seoKeywords|default }}"/>
    </div>
</div>
<div class="control-group">
    <label for="mvp_form_shortDescription" class="control-label">{{ 'short_description'|trans }} : </label>
    <div class="controls">
        <textarea id="mvp_form_shortDescription" type="text" name="shortDescription" class="cm-wysiwyg input-large" placeholder="{{ multiVendorProductPlaceholder.shortDescription|default }}">
            {{ multiVendorProduct.shortDescription|default }}
        </textarea>
    </div>
</div>
<div class="control-group">
    <label for="mvp_form_description" class="control-label">{{ 'full_description'|trans }} : </label>
    <div class="controls">
        <textarea id="mvp_form_description" type="text" name="description" class="cm-wysiwyg input-large" placeholder="{{ multiVendorProductPlaceholder.description|default }}">
            {{ multiVendorProduct.description|default }}
        </textarea>
    </div>
</div>
{% for index, image in images %}
    <div class="control-group">
        <label class="control-label">{{ 'image'|trans }} {{ loop.index }} : </label>
        <div class="controls controls--remove" style="width: 192px;">
            <img src="{{ image | image_url }}">
            <input id="{{ image.id }}" type="checkbox" name="removeImage[{{ image.id }}]">
            <label for="{{ image.id }}">
                <i class="icon-remove-sign">
                    <i class="icon-remove-check"></i>
                </i>
                <span>{{ 'delete' | trans }}</span>
            </label>
        </div>
    </div>
    <div class="control-group">
        <label for="" class="control-label">{{ 'alt_text'|trans }} : </label>
        <div class="controls">
            <input id="alt_{{ image.id }}" type="text" name="alt_{{ image.id }}" maxlength="255" value="{{ image.altText }}">
        </div>
    </div>
{% endfor %}
<div class="control-group">
    <label for="mvp_form_newImage" class="control-label">{{ 'select_image'|trans }} : </label>
    <div class="controls">
        <input id="mvp_form_newImage" type="file" name="newImage">
    </div>
</div>
<div class="control-group">
    <label for="mvp_form_newImageAlt" class="control-label">{{ 'alt_text'|trans }} : </label>
    <div class="controls">
        <input id="mvp_form_newImageAlt" type="text" name="newImageAlt" maxlength="255" >
    </div>
</div>
<div class="control-group">
    <label for="mvp_form_status" class="control-label cm-required">{{ 'status'|trans }} : </label>
    <div class="controls">
        <select id="mvp_form_status" type="text" name="status">
            {% for value, label in statuses %}
                <option value="{{ value }}" {{ multiVendorProduct.status == value ? 'selected ' : '' }}>{{ label }}</option>
            {% endfor %}
        </select>
    </div>
</div>
