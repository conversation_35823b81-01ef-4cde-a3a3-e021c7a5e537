<div id="user_groups_page">
    <form action="{{ path('user_groups_update') }}" method="post" name="user_groups_update_form">
    <div class="pull-right adv-buttons" id="tools_manage_products_adv_buttons">
        <div class="btn-group ">
            <a class="btn btn-primary cm-tooltip" href="#">
                <i class="icon-plus"></i>
            </a>
        </div>
    </div>

    {% if groups | length %}
        {% set paginationId = 'group_paginator' %}
        {% block pagination %}
            <div class="cm-pagination-container" id="{{ paginationId }}">
                <div class="pagination-wrap">
                    {% if lastPage > 1 %}
                        <div class="pagination center">
                            <ul>
                                {% set paginationRightLeftOffset = 5 %}
                                {# `«` arrow  #}
                                <li {{ currentPage == 1 ? 'class="disabled"' }}>
                                    <a href="{{ path('user_groups_list', {'page': (currentPage-1 < 1 ? 1 : currentPage-1)}) }}" data-ca-scroll=".cm-pagination-container" data-ca-target-id="{{ paginationId }}">«</a>
                                </li>

                                <li {{ currentPage == 1 ? 'class="disabled"' }}>
                                    <a href="{{ path('user_groups_list', {'page': 1}) }}" data-ca-scroll=".cm-pagination-container" data-ca-target-id="{{ paginationId }}">1</a>
                                </li>

                                {% if lastPage > 2 %}
                                    {# Render -5 <=> +5 page number ( 5 is paginationRightLeftOffset ) #}
                                    {% for i in max(2, currentPage-paginationRightLeftOffset)..min(lastPage-1, currentPage+paginationRightLeftOffset) %}
                                        <li {{ currentPage == i ? 'class="active"' }}>
                                            <a href="{{ path('user_groups_list', {'items_per_page': items_per_page, 'page': i}) }}" data-ca-scroll=".cm-pagination-container" data-ca-target-id="{{ paginationId }}">
                                                {# show ... rather than page number if this is the 1st or last iteration AND this is not the current page AND this is not the last but one AND this is not the 2nd #}
                                                {% if i != currentPage and i != 2 and i != lastPage-1 and (loop.first or loop.last) %}
                                                    ...
                                                {% else %}
                                                    {{ i }}
                                                {% endif %}
                                            </a>
                                        </li>
                                    {% endfor %}
                                {% endif %}

                                <li {{ currentPage == lastPage ? 'class="disabled"' }}>
                                    <a href="{{ path('user_groups_list', {'items_per_page': items_per_page, 'page': lastPage}) }}" data-ca-scroll=".cm-pagination-container" data-ca-target-id="{{ paginationId }}">{{ lastPage }}</a>
                                </li>

                                {# `»` arrow #}
                                <li {{ currentPage == lastPage ? 'class="disabled"' }}>
                                    <a href="{{ path('user_groups_list', {'items_per_page': items_per_page, 'page': (currentPage+1 <= lastPage ? currentPage+1 : currentPage)}) }}" data-ca-scroll=".cm-pagination-container" data-ca-target-id="{{ paginationId }}">»</a>
                                </li>
                            </ul>
                        </div>
                    {% endif %}
                </div>

                <div class="pagination-desc pagination-centered">

                    <div class="btn-group">

                        <span style="font-size: 13px;" class="pagination-total-items">{{ 'view_by' | trans }} : {{ total }} / </span>

                        <a class="btn-text dropdown-toggle" data-toggle="dropdown">{{ items_per_page }}<span class="caret"></span></a>

                        <ul id="{{ paginationId }}" class="dropdown-menu cm-smart-position">
                            <li>
                                {% for i in range(10, 100, 10) %}
                                    <a data-ca-scroll=".cm-pagination-container" href="{{ path('user_groups_list', {'items_per_page': i, 'page': 1}) }}" data-ca-target-id="pagination_contents">{{ i }}</a>
                                {% endfor %}
                            </li>
                        </ul>

                    </div>

                </div>

                <div class="pagination-centered cpt_selected_rows" style="display: none">
                    {{ 'number_of_selected_items' | trans }} <span id="cpt_selected_rows">0</span>
                </div>
            </div>
        {% endblock %}

        <table class="table table-middle" id="table-groups">
            <thead>
            <tr>
                <th id="group-name-label">{{ 'name'|trans }}</th>
                <th id="group-delete-label">{{ 'user_groups_users'|trans }}</th>
                <th></th>
                <th></th>
            </tr>
            </thead>
            <tbody>
            {% for group in groups %}
                <tr>
                    <td>
                        <input type="text" readonly id="group-{{ group.id }}" name="ids[{{ group.id }}]" value="{{ group.name }}"/>
                        <div class="btn-group ">
                            <a class="btn cm-tooltip" onclick="editGroupName('{{ group.id }}')">
                                <i class="icon-edit"></i>
                            </a>
                        </div>
                    </td>
                    <td>
                        {{ group.users|length }}
                    </td>
                    <td>
                        <a class="btn cm-tooltip" onclick="setGroupId('{{ group.id  }}')" data-toggle="modal" data-target="#importModal">
                            <i class="icon-arrow-up"></i>
                        </a>
                    </td>
                    <td>
                        <a class="btn cm-tooltip" href="{{ path('user_groups_export', {'groupId' : group.id}) }}">
                            <i class="icon-arrow-down"></i>
                        </a>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>

        {{ block('pagination') }}
    {% else %}
        <div class="no-items" id="msg-user_groups">{{ 'user_groups_empty_list' | trans }}</div>
        <table class="table table-middle" id="table-groups">
            <thead>
                <tr>
                    <th id="group-name-label"></th>
                    <th id="group-delete-label"></th>
                    <th></th>
                    <th></th>
                </tr>
            </thead>
        </table>
    {% endif %}
</form>
</div>

{# Import Modal #}
<div id="importModal" class="modal fade" role="dialog" style="width: 560px;display: none;">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title center">{{ 'user_groups_import_modal_title' | trans }}</h4>
            </div>
            <form action="{{ path('user_groups_import') }}" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <p>
                        {{ 'user_groups_import_modal_body' | trans }}
                    </p>
                        <input type="hidden" name="groupId" id="groupId" value=""/>
                        <input type="file" name="file" accept=".csv" required/>
                </div>
                <div class="modal-footer" style="display: block;">
                    <button type="button" class="btn btn-danger" data-dismiss="modal">{{ 'user_groups_import_modal_cancel' | trans }}</button>
                    <button type="submit" class="btn btn-success">{{ 'user_groups_import_modal_submit' | trans }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    $(document).ready(function(){
        $("#tools_manage_products_adv_buttons").click(function(){
            $("#msg-user_groups").html("{{ 'user_groups_msg_info_create_group' | trans }}");
            $("#group-name-label").html("{{ 'name'|trans }}");
            $("#group-delete-label").html("{{ 'user_groups_users'|trans }}");
            $("#table-groups").append('<tr><td><input type="text" name="ids[]" value="" placeholder=""/></td><td></td><td></td><td><a href="javascript:void(0);" class="remCF">{{ 'delete' | trans }}</a></td></tr>');
        });
        $("#table-groups").on('click','.remCF',function(){
            $(this).parent().parent().remove();
        });
    });

    function editGroupName(id) {
        console.log("group-" + id);
        document.getElementById("group-" + id).readOnly = false;
    }

    function setGroupId(id) {
        $('#groupId').val(id);
    }
</script>
