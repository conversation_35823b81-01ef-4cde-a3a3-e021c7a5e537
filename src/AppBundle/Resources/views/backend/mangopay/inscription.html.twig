<div class="cm-tabs-content content-no-filter">

    <p class="center">
        {{ 'mangopay_inscription_header'|trans|nl2br }}
    </p>

    {% if mangoPayDeclarationId is defined
        and mangoPayDeclarationId is not null
    %}
    <p class="center">
        {{ 'decalaration_is_submitted'|trans({
            '%mangoPayDeclarationId%': mangoPayDeclarationId
        })|raw|nl2br }}
    </p>
    {% endif %}

    {{ form_start(form, {'attr': {'id': 'mangopay_inscription_form', 'class': 'form-horizontal form-edit dropzone'}}) }}
    <div id="companyPersonList" data-prototype="{{ form_widget(form.companyPersons.vars.prototype)|e('html_attr') }}">
        {% for key, person in form.companyPersons %}
            {% set personId = 0 %}
            <fieldset>
                <legend>
                    {{ 'legal_representativel'|trans }}
                    {% if companyPersonUBO.companyPersons[key].id is not null
                        and companyPersonUBO.companyPersons[key].id != 0
                        and formIsDisabled == false
                    %}
                        {% set personId = companyPersonUBO.companyPersons[key].id %}
                        <a class="btn btn-danger pull-right" href={{ path('company_delete_mangopay_inscription', { 'id': companyPersonUBO.companyPersons[key].id })}} >{{ 'delete'|trans }}</a>
                    {% endif %}
                </legend>

                <div class="control-group">
                    {{ form_label(person.title, 'user_title' , {'label_attr': {'class': 'control-label'}}) }}
                    <div class="controls">
                        {{ form_widget(person.title) }}
                    </div>
                    <div class="controls">
                        {{ form_errors(person.title) }}
                    </div>
                </div>

                <div class="control-group">
                    {{ form_label(person.firstname, 'first_name' , {'label_attr': {'class': 'control-label'}}) }}
                    <div class="controls">
                        {{ form_widget(person.firstname) }}
                    </div>
                    <div class="controls">
                        {{ form_errors(person.firstname) }}
                    </div>
                </div>

                <div class="control-group">
                    {{ form_label(person.lastname, 'last_name' , {'label_attr': {'class': 'control-label'}}) }}
                    <div class="controls">
                        {{ form_widget(person.lastname) }}
                    </div>
                    <div class="controls">
                        {{ form_errors(person.lastname) }}
                    </div>
                </div>

                <div class="control-group">
                    {{ form_label(person.address, 'address' , {'label_attr': {'class': 'control-label'}}) }}
                    <div class="controls">
                        {{ form_widget(person.address) }}
                    </div>
                    <div class="controls">
                        {{ form_errors(person.address) }}
                    </div>
                </div>

                        <div class="control-group">
                            {{ form_label(person.address2, 'address2' , {'label_attr': {'class': 'control-label'}}) }}
                            <div class="controls">
                                {{ form_widget(person.address2) }}
                            </div>
                        </div>

                <div class="control-group">
                    {{ form_label(person.city, 'city' , {'label_attr': {'class': 'control-label'}}) }}
                    <div class="controls">
                        {{ form_widget(person.city) }}
                    </div>
                    <div class="controls">
                        {{ form_errors(person.city) }}
                    </div>
                </div>

                <div class="control-group">
                    {{ form_label(person.zipcode, 'zipcode' , {'label_attr': {'class': 'control-label'}}) }}
                    <div class="controls">
                        {{ form_widget(person.zipcode) }}
                    </div>
                    <div class="controls">
                        {{ form_errors(person.zipcode) }}
                    </div>
                </div>

                <div class="control-group">
                    {{ form_label(person.state, 'region' , {'label_attr': {'class': 'control-label'}}) }}
                    <div class="controls">
                        {{ form_widget(person.state) }}
                    </div>
                    <div class="controls">
                        {{ form_errors(person.state) }}
                    </div>
                </div>

                <div class="control-group">
                    {{ form_label(person.country, 'country' , {'label_attr': {'class': 'control-label'}}) }}
                    <div class="controls">
                        {{ form_widget(person.country) }}
                    </div>
                    <div class="controls">
                        {{ form_errors(person.country) }}
                    </div>
                </div>

                <div class="control-group">
                    {{ form_label(person.nationalities, 'nationality' , {'label_attr': {'class': 'control-label'}}) }}
                    <div class="controls">
                        {{ form_widget(person.nationalities, {'attr' : {'data-id' : personId }}) }}
                    </div>
                    <div class="controls">
                        {{ form_errors(person.nationalities) }}
                    </div>
                </div>

                <div class="control-group">
                    {{ form_label(person.birthdate, 'date_of_birth' , {'label_attr': {'class': 'control-label'}}) }}
                    <div class="controls">
                        {{ form_widget(person.birthdate) }}
                    </div>
                    <div class="controls">
                        {{ form_errors(person.birthdate) }}
                    </div>
                </div>

                <div class="control-group">
                    {{ form_label(person.birthplaceCity, 'birthplace_city' , {'label_attr': {'class': 'control-label'}}) }}
                    <div class="controls">
                        {{ form_widget(person.birthplaceCity) }}
                    </div>
                    <div class="controls">
                        {{ form_errors(person.birthplaceCity) }}
                    </div>
                </div>

                <div class="control-group">
                    {{ form_label(person.birthplaceCountry, 'birthplace_country' , {'label_attr': {'class': 'control-label'}}) }}
                    <div class="controls">
                        {{ form_widget(person.birthplaceCountry) }}
                    </div>
                    <div class="controls">
                        {{ form_errors(person.birthplaceCountry) }}
                    </div>
                </div>

                <div class="control-group">
                    {{ form_label(person.ownershipPercentage, 'ownership_percentage' , {'label_attr': {'class': 'control-label'}}) }}
                    <div class="controls">
                        {{ form_widget(person.ownershipPercentage) }}
                    </div>
                    <div class="controls">
                        {{ form_errors(person.ownershipPercentage) }}
                    </div>
                </div>

                    </fieldset>
                {% endfor %}
            </div>
        {{ form_end(form) }}
    </div>

<script type="text/javascript">
    let btnAddName = '{{ "btn_add_representative_legal"|trans }}';
    let btnRemoveName = '{{ "btn_remove_representative_legal"|trans }}';
    let AddLegalReprentativel = '{{ "another_legal_representativel"|trans }}';
    let selectCountry = '{{ "select_country"|trans }}';
</script>
<script type="text/javascript" src="/js/backend/mangopay/mangopay.js"></script>
