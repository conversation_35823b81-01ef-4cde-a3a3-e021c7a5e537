<div id="company_messages">
    <p>
        <a class="cta-before" href="{{ path('admin_Discuss_list') }}">{{ 'previous'|trans }}</a><br />
        {% if productId %}<a class="discuss-title" href="{{ cscart_url('products.view?product_id='~productId, 'C') }}">{% endif %}
            {{ 'title'|trans }} : {{ title }}
            {% if productId %}</a>{% endif %}
    </p>
    {% for message in messages %}
        <div class="row">
            <div class="alert-info span6 {% if isCompany[message.id] == true %}offset6{% endif %}" {% if isCompany[message.id] == false %}style="color: #777; background-color: #FAFAFA; border-color: #CCC;"{% endif %}>

                {% set dateMessage = "discuss_reception_date" %}

                {% if isCompany[message.id] == false %}
                    {% set dateMessage = "discuss_send_date" %}
                {% endif %}

                <strong> {{ interlocutors[message.author] }} </strong>

                <strong>{{ dateMessage|trans({ '[date]': message.sendDate|date('d/m/Y'), '[time]': message.sendDate|date('H:i') }) }}</strong>
                <br />

                {{ message.content|escape }}

                {% for attachment in messageAttachmentService.getAttachmentsByMessage(message.id) %}
                        <div>
                            {% if attachment.viewUrl == '' %}
                                {{ attachment.name }} <span class="warn warning"></span><span style="color: red;">{{ 'file_not_found'|trans }}</span>
                            {% else %}
                                <a href="{{ attachment.viewUrl }}" target="_blank">{{ attachment.name }} </a>
                                <a href="{{ attachment.downloadUrl }}" download><i class="icon-download-alt"></i></a>
                            {% endif %}
                        </div>
                {% endfor %}
            </div>
        </div>
    {% endfor %}
    <div class="row">
        <div class="span6 offset6">
            <form action="{{ path('admin_Discuss_post') }}" method="post" enctype="multipart/form-data" class="cm-processed-form">
                <input type="hidden" name="discussionId" value="{{ discussionId }}" />
                <input type="hidden" name="token" value="{{ csrfToken }}" />
                <input type="hidden" name="formInOrderDetails" value="{{ formInOrderDetails }}">
                <textarea placeholder="{{ 'discuss_your_message'|trans }}" name="content" rows="5" style="width: 100%;"></textarea><br>
                <div class="field_wrapper">
                    <div>
                        <input type="file" name="files[]" onchange="validateFile(this)">
                        <a class="cm-tooltip" title="{{ 'max_file_size_authorized'|trans }} , {{ 'file_format_authorized'|trans }}">
                            <i class="icon-question-sign"></i>
                        </a>
                        <a href="javascript:void(0);" class="remove_button"><i class="icon-trash"></i></a>
                    </div>
                </div>
                <div>
                    <span class="alert-danger" id="file_error"></span>
                </div>
                <div>
                    <a href="javascript:void(0);" class="add_button" title="{{ 'add_document'|trans }}"><i class="icon-plus"></i></a>
                </div>
                <br>
                <button class="btn btn-primary">{{ 'send'|trans }}</button>
            </form>
        </div>
    </div>

    <script>
        $(function(){
            $("html, body").animate({ scrollTop: $(document).height() }, 1000);

            var addButton = $('.add_button'); //Add button selector
            var wrapper = $('.field_wrapper'); //Input field wrapper
            var fieldHTML = '<div><input type="file" name="files[]" onchange="validateFile(this)"/>  <a class="cm-tooltip" title="{{ "max_file_size_authorized"|trans }} , {{ "file_format_authorized"|trans }}"> <i class="icon-question-sign"></i></a><a href="javascript:void(0);" class="remove_button"><i class="icon-trash"></i></a></div>';

            //Once add button is clicked
            $(addButton).click(function(){
                //Check maximum number of input fields
                $(wrapper).append(fieldHTML); //Add field html
            });

            //Once remove button is clicked
            $(wrapper).on('click', '.remove_button', function(e){
                e.preventDefault();
                $(this).parent('div').remove(); //Remove field html
            });
        });

        function validateFile(file) {
            var extensions = {{ validateExtension|json_encode()|raw }}
            var fileSize = file.files[0].size / 1024 / 1024; // in MB
            var fileError = $('#file_error');

            fileError.text('');

            if (fileSize > 25) {
                fileError.text('{{ 'max_file_size_authorized'|trans|escape('js') }}');
                $(file).val(''); //for clearing with Jquery
            }

            if (extensions.includes(getExtension(file.files[0].name)) === false) {
                fileError.text('{{ 'file_format_authorized'|trans|escape('js') }}');
                $(file).val(''); //for clearing with Jquery
            }
        }

        function getExtension(path) {
            var basename = path.split(/[\\/]/).pop(),  // extract file name from full path ...
                // (supports `\\` and `/` separators)
                pos = basename.lastIndexOf(".");       // get last position of `.`

            // if file name is empty or ...
            if (basename === "" || pos < 1) {
                return "";
            }

            return basename.slice(pos + 1);            // extract extension ignoring `.`
        }
    </script>
</div>

