<table id="companies-buyers-discussion" width="100%" class="table table-middle">
    <thead>
    <tr>
        <th>
            {{ 'discuss_with'|trans }}
        </th>
        <th>
            {{ 'discuss_last_message'|trans }}
        </th>
        <th>
            {{ 'title'|trans }}
        </th>
        <th>
            &nbsp;
        </th>
    </tr>
    </thead>
    {% for discussion in discussions %}
        {% set lastMessage = (lastMessages[discussion.id]) %}
        {% set discussionUrl = path('admin_Discuss_view', {'discussionId': discussion.id}) %}
        <tr>
            <td>
                <a href="{{ discussionUrl }}">{{ (interlocutors[discussion.id]) }}</a>
            </td>
            <td>
                {% if lastMessage %}
                    <a href="{{ discussionUrl }}" class="message-short">
                        {{ lastMessage.sendDate|date('d/m/Y H:i') }} -
                        {{ lastMessage.content|striptags|truncate('40', true, '...') }}
                    </a>
                {% endif %}
            </td>
            <td>
                <a href="{{ discussionUrl }}">
                    {{ discussion.metaData('title')|truncate('40', true, '...') }}
                </a>
            </td>
            <td>
                <a href="{{ path('admin_Discuss_hide', {'discussionId': discussion.id, 'token': csrfToken}) }}" class="cm-confirm test-delete-discuss">
                    <i class="icon-trash"></i> {{ 'delete'|trans }}
                </a>
            </td>
        </tr>
    {% endfor %}
</table>
