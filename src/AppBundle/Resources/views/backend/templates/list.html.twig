<table class="table table-tree table-top table-nobg" width="100%">
    <thead>
    <tr>
        <th>{{ 'context'|trans }}</th>
        <th></th>
    </tr>
    </thead>
    <tbody>
        {% for category, templates in list %}
            {% for template in templates %}
                <tr>
                    <td>{{ template.file|default(category) }}</td>
                    <td>
                        {% if template.key_vars|length %}
                            <table width="100%">
                                <thead>
                                    <tr>
                                        <th width="60%">{{ 'translation_key'|trans }}</th>
                                        <th>{{ 'vars'|trans }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for keyVar in template.key_vars %}
                                        <tr>
                                            <td><a href="/admin.php?dispatch[languages.translations]&q={{ keyVar.key|url_encode }}">{{ keyVar.key|escape }}</a></td>
                                            <td>
                                                {% if keyVar.vars|length %}
                                                    {% for var in keyVar.vars %}
                                                        {{ var|escape }}{% if not loop.last %}<br />{% endif %}
                                                    {% endfor %}
                                                {% else %}-{% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        {% else %}-{% endif %}
                    </td>
                </tr>
            {% endfor %}
        {% endfor %}
    </tbody>
</table>
