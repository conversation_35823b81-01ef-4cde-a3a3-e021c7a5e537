<table class="table table-middle table-objects table-striped" id="associated-events">
    <tbody>
    {% if form.vars.value.id is defined %}
        {% for webhookEventReference in form.vars.value.eventReferences %}
        <tr>
            <td class="actual-events-names">
                <input readonly type="text" name="event_names[]" value={{ webhookEventReference }}>
            </td>
            <td class="right">
                {{
                    render_smarty_legacy('design/backend/templates/buttons/multiple_buttons.tpl', {
                        'hide_clone': true,
                        'hide_add': true,
                        'delete_element': true
                    })
                }}
            </td>
        </tr>
        {% endfor %}
    {% endif %}
    </tbody>
</table>
