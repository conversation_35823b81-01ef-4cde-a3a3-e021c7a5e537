<div id="notifications-configuration">
    {% if webhook is defined %}
        <h4>{{ 'webhooks_notifications_edit_subtitle'|trans({'[webhook_url]': webhook.url}) }}</h4>
        <div id="cancelConfirmationModal" class="modal hide fade" tabindex="-1" role="dialog" aria-labelledby="cancelConfirmationModalLabel" aria-hidden="true">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            <h3 id="cancelConfirmationModalLabel">{{ 'warning'|trans }}</h3>
        </div>
        <div class="modal-body">{{ 'webhooks_notifications_delete_confirm'|trans }}</div>
        <div class="modal-footer">
            <button class="btn" data-dismiss="modal" aria-hidden="true">{{ 'cancel'|trans }}</button>
            <a href="{{ path('admin_notifications_delete') ~'?id=' ~ webhook.id }}" class="btn btn-primary">{{ 'confirm'|trans }}</a>
        </div>
    </div>
    {% else %}
        <h4>{{ 'webhooks_notifications_new_notification'|trans }}</h4>
    {% endif %}
    <form action="{{ path('admin_notifications_save') }}" method="POST" name="notifications_save_form" enctype="multipart/form-data" class="form-horizontal form-edit">
        <div id="url-delete-group">
            <div>
                <h5>
                    {{ 'webhooks_notifications_update_title'|trans }}
                </h5>
                {{ form_row(form.url, {'attr': {'class': 'notification-url-large'}}) }}
            </div>
            {% if webhook is defined %}
            {# Delete button#}
            <div class="right" id="delete">
                <a href="#cancelConfirmationModal" role="button" class="btn btn-danger" data-toggle="modal">{{ 'delete'|trans }}</a>
            </div>
            {% endif %}
        </div>
        <div>
        {# All events#}
            <h5>
                {{ 'webhooks_notifications_event_references'|trans }}
            </h5>
            <div id="events-erase-group">
                {{ form_row(form.eventReferences) }}
                {# Clear button#}
                <div class="right" id="erase">
                    <a role="button" class="btn btn-warning" data-toggle="modal">{{ 'clear'|trans }}</a>
                </div>
            </div>
        </div>
        {# Events associated with webhook#}
        <div>
            {% if webhook is defined %}
            <input type="hidden" name="id" value={{ webhook.id }} />
            {% endif %}
            {% include '@App/backend/notifications/associated_events.html.twig' %}
        </div>
    </form>
</div>
<script type="text/javascript">
    var allEvents = '{{ 'webhooks_notifications_all_events' | trans }}';
</script>
