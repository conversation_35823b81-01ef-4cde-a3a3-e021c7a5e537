<div id="notifications-configuration">
    <h4>{{ 'webhooks_notifications_manage_subtitle'|trans }}</h4>
    <table class="table table-middle table-objects table-striped">
        <thead>
        <tr>
            <th>{{ 'url'|trans }}</th>
            <th>{{ 'webhooks_notifications_perimeter'|trans }}</th>
        </tr>
        </thead>
        <tbody>
        {% for webhook in webhooks %}
            {% if webhook.companyId is same as(switchCompanyId) or switchCompanyId == 0  %}
                <tr>
                    <td class="row-status">
                        <a href="{{ path('admin_notifications_view', {'id': webhook.id}) }}">
                            {{ webhook.url}}
                        </a>
                    </td>
                    <td class="row-status">
                        {% if webhook.companyId is null %}
                            {{ 'webhooks_notifications_marketplace_perimeter'|trans }}
                        {% else %}
                            {{ companies[webhook.companyId] ?? '' }}
                        {% endif %}
                    </td>
                </tr>
            {% endif %}
        {% endfor %}
        </tbody>
    </table>
</div>

<div class="pull-right adv-buttons" id="tools_manage_products_adv_buttons">
    <div class="btn-group ">
        <a class="btn btn-primary cm-tooltip test-mvp-add" href="{{ path('admin_notifications_view') }}">
            <i class="icon-plus"></i>
        </a>
    </div>
</div>
