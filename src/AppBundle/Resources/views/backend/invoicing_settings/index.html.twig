<div id="invoicing-settings">
    <form action="{{ path('admin_invoicing_settings') }}" method="post" id="invoicing-settings-form">
        <div class="form-control">
            <label for="invoicing_settings_label">
                <input type="checkbox" id="invoicing_settings_label" {% if marketplaceInvoicingDisplayed %} checked {% endif %} name="invoicing_settings" />
                    {{ 'invoicing_settings_label'|trans }}
            </label>
        </div>

        <a href="#confirmationModal" data-toggle="modal" class="btn btn-primary">{{ "invoicing_settings_button"|trans }}</a>

        <div id="confirmationModal" class="modal hide fade" tabindex="-1" role="dialog" aria-labelledby="ConfirmationModalLabel" aria-hidden="true">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                <h3>{{"warning"|trans }}</h3>
            </div>

            <div class="modal-body">{{ "invoicing_settings_modal"|trans }}</div>

            <div class="modal-footer" style="display: block;">
                <button type="button" class="btn btn-danger " data-dismiss="modal">{{ "cancel"|trans }}</button>
                <button type="submit" class="btn btn-primary">{{ "confirm"|trans }}</button>
            </div>
        </div>
    </form>
</div>
