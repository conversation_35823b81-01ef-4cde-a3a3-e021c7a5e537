/*PRODUCT CARD*/
.product-main-info {
  @basicSize : 650px;
  a, a:active {
    color: @basic;
    text-decoration: underline;
    &.bold {
      font-weight: bold;
    }
  }
  .product-info {
    background-color: @white;
    select {
      width: 215px;
    }
    .mx-no-margin;
    border: 1px solid #ebebeb;
    padding: 10px;
    height: calc(@basicSize - 20px);
    overflow: auto;
    .product-name {
      font-size: @LFont;
    }
    .qty-in-stock {
      font-size: @LFont;
    }
    .brand-wrapper {
      font-size: @LFont;
      font-weight: bold;
      color: @basic;
    }
    .product-short-description {
      margin-top: 15px;
      margin-bottom: 5px;
      font-size: @MFont;
    }
    .options-wrapper {
      margin-top: 10px;
    }
  }
  .image-border {
    margin-right: 10px;
  }
  .border-image-wrap {
    position: relative;
    width: @basicSize;
    height: @basicSize;
    overflow: hidden;
    line-height: @basicSize;
    margin: auto auto;
    & > img {
      vertical-align: middle;
    }
    .product-thumbnails {
      &, & * {
        .mx-no-wrapping;
        border: 0;
      }
      line-height: 35px;
      position: absolute;
      top: 0px;
      left: 0px;
      .thumbnails-item {
        & img {
          border: 1px solid @grey;
        }
        &.active img {
          border: 1px solid @w-red;
        }
        display: block;
        height: 35px;
        width: 35px;
        position: relative;
        top: -1px;
        opacity: 0.8;
        &:hover {
          opacity: 1;
        }
        background-color: white;
        margin: 5px;
      }
    }
  }
  .w-notation-wrapper {
    top: -5px;
    float: right;
    z-index: 60;
    .stars {
      float: none;
    }
  }
  .w-shipping-icon {
    display: inline-block;
  }
}

//Bloody override of persistent margin definition
.product-main-info .buttons-container .buttons-container {
  .mx-no-margin;
}

.buttons-container {
  .qty {
    padding-right: 0;
  }
  .mx-no-margin;
  table {
    width: 100%;
    tr td {
      text-align: right;
      vertical-align: middle;
      padding-left: 2px;
      input.button-submit-big {
        line-height: 48px;
        height: 48px;
        &, &:active, &:hover {
          background-color: #e67e22;
          background-image: none;
          border: none;
          padding-left: 17px;
          padding-right: 17px;
          font-size: @LFont;
          font-weight: bold;
          color: @white;
        }
      }
    }
  }
}

.w-old-price {
  & , * {
    font-size: @SFont;
    color: @grey;
  }
}

table.w-price-table {
  .price-num {
    font-size: @XXLFont;
  }
  .w-old-price {
    * {
      font-size: @MFont;
    }
  }
  .w-price-secondary-info {
    color: @w-price;
    font-size: @MFont;
  }
  .w-discount-block {
    color: @white;
    font-size: @LFont;
    background-color: @w-price;
    vertical-align: middle;
    text-align: center;
    font-weight: bold;
    line-height: 43px;
    width: 43px;
    margin: 0 auto;
  }
  td {
    &:nth-child(1) {
      padding-left: 10px;
      padding-right: 10px;
      .actual-price * {
        color: @w-price;
        font-weight: bold;
      }
    }
  }
}

/*TABS*/
/* tabs are generic concept -> see w_general.less*/

/*STARS*/
p.nowrap.stars {
  height: 16px;
  a {
    &, i.icon-star, i.icon-star-half, i.icon-star-empty {
      font-size: @MFont;
    }
  }
}