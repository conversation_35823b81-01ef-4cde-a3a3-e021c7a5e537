//
// Variables
// --------------------------------------------------


// Global values
// --------------------------------------------------


// Grays
// -------------------------
@black:                 #000;
@grayDarker:            #222;
@grayDark:              #333;
@gray:                  #555;
@grayLight:             #999;
@grayLighter:           #eee;
@white:                 #fff;


// Accent colors
// -------------------------
@blue:                  #049cdb;
@blueDark:              #0064cd;
@green:                 #46a546;
@red:                   #9d261d;
@yellow:                #ffc40d;
@orange:                #f89406;
@pink:                  #c3325f;
@purple:                #7a43b6;


// Scaffolding
// -------------------------
@bodyBackground:        @white;
@textColor:             @grayDark;


// Links
// -------------------------
@linkColor:             #08c;
@linkColorHover:        darken(@linkColor, 15%);


// Typography
// -------------------------
@sansFontFamily:        "Helvetica Neue", Helvetica, Arial, sans-serif;
@serifFontFamily:       Georgia, "Times New Roman", Times, serif;
@monoFontFamily:        Monaco, Menlo, Consolas, "Courier New", monospace;

@baseFontSize:          13px;
@baseFontFamily:        @sansFontFamily;
@baseLineHeight:        20px;
@altFontFamily:         @serifFontFamily;

@headingsFontFamily:    inherit; // empty to use BS default, @baseFontFamily
@headingsFontWeight:    bold;    // instead of browser default, bold
@headingsColor:         inherit; // empty to use BS default, @textColor


// Component sizing
// -------------------------
// Based on 14px font-size and 20px line-height

@fontSizeLarge:         @baseFontSize * 1.25; // ~18px
@fontSizeSmall:         @baseFontSize * 0.85; // ~12px
@fontSizeMini:          @baseFontSize * 0.75; // ~11px

@paddingLarge:          11px 19px; // 44px
@paddingSmall:          2px 10px;  // 26px
@paddingMini:           0 6px;   // 22px

@baseBorderRadius:      4px;
@borderRadiusLarge:     6px;
@borderRadiusSmall:     3px;


// Tables
// -------------------------
@tableBackground:                   transparent; // overall background-color
@tableBackgroundAccent:             #f9f9f9; // for striping
@tableBackgroundHover:              #f5f5f5; // for hover
@tableBorder:                       #ddd; // table and cell border

// Buttons
// -------------------------
@btnBackground:                     @white;
@btnBackgroundHighlight:            darken(@white, 10%);
@btnBorder:                         #ccc;

@btnPrimaryBackground:              @linkColor;
@btnPrimaryBackgroundHighlight:     spin(@btnPrimaryBackground, 20%);

@btnInfoBackground:                 #5bc0de;
@btnInfoBackgroundHighlight:        #2f96b4;

@btnSuccessBackground:              #62c462;
@btnSuccessBackgroundHighlight:     #51a351;

@btnWarningBackground:              lighten(@orange, 15%);
@btnWarningBackgroundHighlight:     @orange;

@btnDangerBackground:               #ee5f5b;
@btnDangerBackgroundHighlight:      #bd362f;

@btnInverseBackground:              #444;
@btnInverseBackgroundHighlight:     @grayDarker;


// GRID variables
// --------------------------------------------------

// Default 940px grid
// -------------------------
@gridColumns: 16;
@gridColumnWidth: 55px;
@gridGutterWidth: 10px;
@gridRowWidth: (@gridColumns * @gridColumnWidth) + (@gridGutterWidth * (@gridColumns - 1));

// 1200px min
@gridColumnWidth1200: 70px;
@gridGutterWidth1200: 30px;
@gridRowWidth1200: (@gridColumns * @gridColumnWidth1200) + (@gridGutterWidth1200 * (@gridColumns - 1));

// 768px-979px
@gridColumnWidth768: 42px;
@gridGutterWidth768: 20px;
@gridRowWidth768: (@gridColumns * @gridColumnWidth768) + (@gridGutterWidth768 * (@gridColumns - 1));

// Fluid grid
// -------------------------
@fluidGridColumnWidth: percentage(@gridColumnWidth/@gridRowWidth);
@fluidGridGutterWidth: percentage(@gridGutterWidth/@gridRowWidth);

// 1200px min
@fluidGridColumnWidth1200: percentage(@gridColumnWidth1200/@gridRowWidth1200);
@fluidGridGutterWidth1200: percentage(@gridGutterWidth1200/@gridRowWidth1200);

// 768px-979px
@fluidGridColumnWidth768: percentage(@gridColumnWidth768/@gridRowWidth768);
@fluidGridGutterWidth768: percentage(@gridGutterWidth768/@gridRowWidth768);

// Fluid container min and max width
@fluidContainerMaxWidth: 1100px;
@fluidContainerMinWidth: 760px;

// Core variables and mixins
@import "/lib/bootstrap/less/mixins.less";

// Grid system and page structure
@import "/lib/bootstrap/less/grid.less";
@import "/lib/bootstrap/less/layouts.less";

// Components: Buttons & Alerts
@import "/lib/bootstrap/less/buttons.less";
@import "/lib/bootstrap/less/button-groups.less";

//styles from back
@import "from_back.less";

// Utility classes
@import "/lib/bootstrap/less/utilities.less"; // Has to be last to override when necessary

// Container width
.container-fluid {
  max-width: @fluidContainerMaxWidth;
  min-width: @fluidContainerMinWidth;
  margin: 0 auto;
}

