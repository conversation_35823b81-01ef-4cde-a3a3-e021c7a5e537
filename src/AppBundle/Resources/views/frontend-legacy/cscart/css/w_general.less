@leftMenuWidth : (3 * @gridColumnWidth) + (2 * @gridGutterWidth) - 1;

/*GENERAL*/
body, body * {
  font-size: @MFont;
  color: @basic;
  position: relative;
}

.white {
  &, & * {
    color: @white;
  }
}

.shadowedText {
  text-shadow: 1px 1px 1px @basic;
}

.size50percent
{
  width: 49.5%; //FIXME : automatic padding cause overflow problem if there's 2 blocks.size50percent in same line and width is 50%
}

.mx-no-padding {
  padding: 0;
}

.mx-no-margin {
  margin: 0;
}

.mx-no-wrapping {
  .mx-no-margin;
  .mx-no-padding;
}

.marged (@margin:10px) {
  .raw-marged(@margin; @margin; @margin; @margin)
}

.marged {
  .marged;
}

.large-marged {
  .marged(20px);
}

.top-marged {
  .raw-marged(@gridGutterWidth; 0; 0; 0);
}
.right-marged {
   .raw-marged(0; @gridGutterWidth; 0; 0);
}
.bottom-marged {
    .raw-marged(0; 0; @gridGutterWidth; 0);
}
.left-marged {
  .raw-marged(0; 0; 0; @gridGutterWidth);
}

.raw-marged (@top:10px; @right:10px; @bottom:10px; @left:10px) {
  margin: @top @right @bottom @left !important;
}

.padded (@padding:10px) {
  .raw-padded(@padding; @padding; @padding; @padding);
}

.padded{
  .padded;
}

.raw-padded (@top:10px; @right:10px; @bottom:10px; @left:10px) {
  padding: @top @right @bottom @left;
}

.bold {
  font-weight: bold;
}

td.middle, .middle {
  vertical-align: middle;
}

td.top, .top {
  vertical-align: top;
}
td.bottom, .bottom {
  vertical-align: bottom;
}

.inline-block {
  display: inline-block !important;
}

a.only-hover-decoration {
  text-decoration: none !important;
  &:hover {
    text-decoration: underline !important;
  }
}

a.no-decoration {
  text-decoration: none !important;
}

ul.bullets-list li {
  list-style-type: disc !important;
}

/*HEADER*/
.tygh-top-panel {
  padding-top: 2px;
}

#top-quick-links li {
  color: @white;
  padding: 0;
  a {
    color: @white;
    font-size: @SFont;
  }
  a:hover {
    text-decoration: none;
  }
}

.logo-container {
  padding: 30px 0;
  min-height: 0;
}

.logo {
  margin: 0;
}

#top-menu-wrapper {
  .mx-no-wrapping;
  background-color: @waterGreen;
  color: @white;
  ul li {
    .mx-no-wrapping;
    display: inline-block;
    padding: @gridGutterWidth;
    color: @white;
    font-size: @MFont;
    cursor: pointer;
    a {
      color: @white;
      font-size: @MFont;
      &:hover {
        text-decoration: none;
      }
    }
    &:first-child {
      background-color: @darkWaterGreen;
      width: @leftMenuWidth - (2 * @gridGutterWidth);
      text-align: center;
      .icon-down-micro {
        font-size: 18px;
        padding-left: 10px;
        top: 3px;
        color: @white;
      }
    }
  }
}

.main-left-menu-wrapper{
  ul.menu-elt{
    top:-1px;
    min-height: 100%;
    margin:0;
    padding:0;
  }
}

/*MY ACCOUNT*/
.top-my-account {
  .popup-title {
    .mx-no-wrapping;
    font-size: @SFont;
    padding-right: 20px;
    i {
      &.icon-user {
        top: 2px;
      }
      &.icon-down-micro {
        top: 0;
      }
    }
  }
  #my_account_login_form, #my_account_creation_form {
    height: 370px;
    padding:0 10px;
    width:304px;
  }
}

/*CART*/
.top-cart-content {
  text-align: right;
  height: 16px;
  top:0;
  .popup-title {
    color:@white;
    .mx-no-wrapping;
    font-size:@MFont;
    padding-right: 20px;
    &:hover {
      color:@basic;
    }
  }
}

.cart-container {
  text-align: right;
}

.cart-icon-wrapper {
  display: inline-block;
}

.icon-basket {
  float: left;
  display: block;
  position: relative;
  height:16px;
  &.filled:before {
    content: url('../media/images/cart_filled_16.png');
  }
  &.empty:before {
    content: url('../media/images/cart_empty_16.png');
  }
}

.dropdown-box {
  &.cart-container, &.top-my-account, &.top-newsletter {
    text-align: left;
    & .popup-title:not(.open) * {
      color: @white;
    }
    & .popup-title:hover * {
      color: @basic;
    }
  }
  &.top-newsletter {
    .popup-title {
      padding: 0 20px 0 0;
      &, & * {
        font-size: @SFont;
      }
    }
  }
}

.minicart-title {
  .mx-no-padding;
}

/*BOTTOM MENU*/
.bottom-menu {
  &, * {
    color: @white
  }
  h4 {
    font-size: @MFont;
  }
  ul {
    li {
      .mx-no-wrapping;
    }
  }
}

.espace-communautaire {
  .wysiwyg-content {
    h4 {
      font-size: @MFont;
      .bold;
      color: @white;
    }
    .raw-padded(0; 0; 0; 10px;);
    .raw-marged(0; 0; 0; 10px;);
    border-left: 1px solid @white;
    .fb-like-box * {
      color: @white;
    }
    #follow-button {
      position: absolute;
      top: 25px;
      right:20px;
    }
  }
}

/*MAIN LEFT MENU*/
.main-left-menu-wrapper {
  background-color: @white;
  padding-top: 1px; /*Homepage banner adjustment*/
  width: @leftMenuWidth - 2px;
  z-index: 100;
  position: absolute;
  &.move-from-top {
    top: 39px;
  }
  left: 0;
  border: 1px solid @paleGrey;
  box-shadow: 1px 3px 3px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 1px 3px 3px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 1px 3px 3px rgba(0, 0, 0, 0.2);
  -ie-box-shadow: 1px 3px 3px rgba(0, 0, 0, 0.2);
  -o-box-shadow: 1px 3px 3px rgba(0, 0, 0, 0.2);
  ul {
    .mx-no-padding;
    li {
      display: block;
      &:nth-child(even) {
        background-color: @paleGrey;
      }
      &:nth-child(odd) {
        background-color: @white;
      }
    }
  }
}

/*THEMATIC BLOCK*/
.thematic-scroller {
  @localHeight: 228px;
  height: @localHeight;
  margin-top:25px;
  .header {
    &, * {
      color: @white;
    }
    font-weight: bold;
    padding: 5px;
    font-size: @MFont;
  }
  .illustration{
    .mx-no-wrapping;
    display:inline-block;
    vertical-align: top;
  }
  .no-illustration-separator{
    width:1px;
    display:inline-block;
    .mx-no-wrapping;
    height:100%;
    background-color: #f5f5f5;
  }
  .left-block{
    width: @leftMenuWidth;
    .title{
      &, * {
        color: @white;
      }
      padding:4px;
      font-size: 17px;
      height:@localHeight/2;
      text-align: right;
    }
    .additional-content{
      li.updateBestSellers{
        padding:3px;
      }
      ul.linkset {
        a{
          color:@basic;
          font-size:@SFont !important;
        }
        li{
          padding:0;
        }
      }
      padding:2px;
    }
    display:inline-block;
    vertical-align: top;
  }
  .jcarousel-wrapper{
    display:inline-block;
    left:4px;
  }
  .jcarousel-skin{
    display:inline-block;
    left:4px;
    .jcarousel-item-horizontal{
      margin-top:0 !important;
      margin-bottom:0 !important;
    }
  }
  &.with-carousel-offset{
    .jcarousel-skin {
      left:18px;
    }
  }
  .jcarousel-container-vertical {
    padding: 13px 0 13px 0;
  }
  .scroller-product-info {
    .product-title {
      padding-top: 2px;
      &, & * {
        color: @basic;
      }
    }
  }
  &.no-border {
    border: 0 !important;
  }
}

.brands-scroller {
  display: inline-block;
  margin-top:25px;
  background-color: @white;
  border-bottom:1px solid #b3b3b3;
  border-top:1px solid #b3b3b3;
  .jcarousel-skin{
    display:inline-block;
    overflow:hidden;
    vertical-align: top;
    .jcarousel-item{
      margin:0;
      & > div{
        padding:1px 0 1px 0;
        border-bottom:1px solid #b3b3b3;
        border-right:1px solid #b3b3b3;
      }
    }
  }
  .left-block{
    display:inline-block;
    vertical-align: top;
  }
  &.no-style {
    border: 0;
    div.header {
      background-color: transparent;
      color: @basic;
      border-bottom: 1px solid rgb(235, 235, 235);
    }
  }
  .brand-text{
    padding:2px;
    padding-top:35px;
    overflow: hidden;
  }
}

/*TABS*/
div.cm-j-tabs {
  border: none;
  ul {
    li.cm-js {
      border: none;
      a, a:hover {
        color: @white;
      }
      background-image: none;
      background-color: @waterGreen;
      border-bottom: 3px solid @white;
      &.active {
        border-bottom: 3px solid @waterGreen;
        a {
          font-weight: bold;
        }
      }
    }
  }
}

div.cm-tabs-content {
  background-color: @white;
  border: 2px solid @turquoise;
}

/*SIDEBOX*/
.bordered-grid {
  width: @leftMenuWidth - 2;
  border: 1px solid @grey;
  z-index: 50;
  margin-bottom: 10px;
  &:first-child {
    margin-top: 20px;
  }
  #dynamic-left-menu, &#adjacent-categories-menu {
    h1.sidebox-parent-cat {
      .raw-padded(5px; 10px; 5px; 10px);
      .bold;
      font-size: @MFont;
    }
    ul {
      .mx-no-wrapping;
      &.indented {
        .indented;
      }
      li {
        a {
          .raw-padded(2px; 10px; 2px; 10px;);
          display: block;
          color: @basic;
          text-decoration: none;
        }
      }
    }
  }
  .sidebox-wrapped {
    background-color: @white;
    .padded;
  }
}

.indented {
  .raw-padded(0; 0; 0; 10px;);
}

/*BUTTONS*/
input.w-button, a.w-button {
  -webkit-appearance: none;
  .mx-no-margin;
  border: 0;
  cursor: pointer;
  padding: 10px;
  display: inline-block;
  text-decoration: none !important;
  &, &:active {
    font-weight: bold;
  }
  &.orange {
    background-color: @w-orange;
    color: @white !important;
  }
  &.grey {
    background-color: @grey;
    color: @white !important;
  }
  &.red {
    background-color: @w-red;
    color: @white !important;
  }
  &.green {
    background-color: @w-green;
    color: @white !important;
  }
  &.palegrey {
    background-color: @paleGrey;
    color: black;
  }
  &.small {
    font-size: @SFont;
    font-weight: normal;
  }
  &.medium {
    font-size: @MFont;
  }
  &.large {
    font-size: @LFont;
  }
  &.input-sized{
    display: inline-block;
    vertical-align: top;
    .mx-no-wrapping;
    height:35px;
    line-height: 35px;
    text-align: center;
    padding: 0 10px 0 10px;
  }
}

/*WRAPPERS*/
.w-blockbox-padded {
  margin-top: @gridGutterWidth;
}

p.no-items {
  border: 1px solid @paleGrey;
  background-color: @white;
  background-image: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  -ie-box-shadow: none;
  -o-box-shadow: none;
  box-shadow: none;
}

.grey-wrapper {
  padding: 10px;
  border: 1px solid @grey;
  background-color: @paleGrey;
}

.step-body-active .control-group .input-text {
  width: 200px;
}

.sidebox-title {
  background-color: @paleGrey;
  color: @basic;
  * {
    font-weight: normal;
    color: @basic;
    font-size: @MFont;
  }
}

.sidebox-wrapper {
  border: 1px solid @paleGrey;
}

.sidebox-body {
  border: 0;
  padding: @gridGutterWidth;
  background-color: @white;
}

label.w-label {
  padding-right: 10px;
  text-align: right;
  width: 100px;
  float: left;
}

.profile-field-wrap select {
  width: 200px;
}

.smalltext , a.smalltext:active , span.smalltext{
  &, & * {
    font-size: @XSFont !important;
  }
}

.w-sidebox-title {
  text-align: center;
  padding: 0 0 10px 0;
}

table.simple-table {
  tr {
    td {
      padding: 10px 0 10px 0;
    }
  }
}

.vertical-rule{
  margin:10px;
  height:100%;
  width:1px;
  border-left: 1px solid @grey;
}

img.promo-bandeau , a.promo-bandeau{
  z-index:100;
  position: absolute;
  top:0;
  right:0;
  padding:0 !important;
}

/*COLORS*/
.grey{
  color:@grey;
}
.red{
  color:@w-red;
}

/*FONT SIZES*/
.XSFont{font-size:@XSFont;}
.SFont{font-size:@SFont;}
.MFont{font-size:@MFont;}
.LFont{font-size:@LFont;}
.XLFont{font-size:@XLFont;}
.XXLFont{font-size:@XXLFont;}


/*CS RESET*/
.cs-reset{
  &.table{
    border:0 !important;
    margin-top: 0!important;
    tr{
      border:0 !important;
      td{
        border:0 !important;
      }
    }
    th{
      border:0 !important;
      td{
        border:0 !important;
      }
    }
  }
}

.ui-dialog-content * {
    position:static;
}

input.force-input-large {
  width: 200px;
}

span.input-like-size {
  line-height: 35px;
}

.w-pagination li{
  display:inline-block;
}

/*POSITIONING*/
.fixed    {position: fixed !important;}
.static   {position: static !important;}
.absolute {position: absolute !important;}
.relative {position: relative !important;}

/*Visual notifications*/
div.visual-notification{
  background-color: @w-red;
  color:@white;
  border-radius: 16px !important;
  width: 16px;
  height: 16px;
  text-align: center;
  font-size: 10px;
  line-height: 16px;
  position:absolute;
  right:-20px;
  top:0px;
}

/*Visual adaptation for new categories*/
div.main-left-menu-wrapper{
  ul.dropdown-vertical li {
    a {
      padding-top:8px;
      padding-bottom:7px;
    }
    &:nth-child(4n+0) a {
      padding-top:7px;
    }
  }
}