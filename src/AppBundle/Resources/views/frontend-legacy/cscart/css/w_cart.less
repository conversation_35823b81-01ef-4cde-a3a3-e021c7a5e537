@flecheWidth : 265px;
.wysiwyg-content #step-indicator-links {
  margin: 0;
  margin-bottom: 10px;
  li {
    line-height: 60px;
    font-size: 26px;
    color: @basic;
    width: 295px;
    padding-left: 40px;
    display: inline-block;
    &.active {
      font-weight: bold;
    }
  }
}

#step-indicator {
  position: absolute;
  top: 0;
  left: 0;
  #elt1 {
    position: absolute;
    top: 0;
    left: 0;
    width: 450px;
  }
  #elt2 {
    position: absolute;
    top: 0;
    left: 335px;
    width: 450px;
  }
  #elt3 {
    position: absolute;
    top: 0;
    left: 670px;
    width: 450px;
  }
  & * {
    display: inline-block;
  }
  .fleche {
    border: 30px solid transparent;
    border-left-color: @paleGrey;
    &.active {
      border-left-color: @grey;
    }
    left: -4px;
  }
  .type1 {
    width: @flecheWidth;
    border: 30px solid @paleGrey;
    &.active {
      border-color: @grey;
    }
  }
  .type2 {
    width: @flecheWidth;
    border: 30px solid @paleGrey;
    &.active {
      border-color: @grey;
      border-left-color: transparent;
    }
    border-left-color: transparent;
  }
}

.w-buttons-container {
  margin: 10px 0 10px 0;
}

#cart_items {
  background-color: @white;
}

#w-cart-infos {
  & td:nth-child(1) {
    width: 625px;
  }
  & td:nth-child(2) {
    width: 400px;
  }
}

#w-cart-total {
  &, & * {
    font-size: @MFont;
  }
  height: 180px;
  background-color: @white;
  background-image: none;
  border: 1px solid @grey;
  table {
    margin: 10px;
    tr {
      td {
        vertical-align: middle;
        padding: 0 0 10px 0;
        &:nth-child(1) {
          width:240px;
        }
        &:nth-child(2) {
          width:140px;
        }
      }
    }
  }
}
.cart-right-buttons{
  width:380px;
}

#order-general-infos {
  border: 1px solid @paleGrey;
  background-color: @white;
  height: 180px;
  table {
    margin: 10px;
    tr {
      td {
        &:nth-child(1) {
          width: 40px;
        }
        vertical-align: middle;
        padding: 0 5px 5px 0;
      }
    }
  }
}

i.icon {
  &.payment-methods-icon {
    padding-left: 15px;
    top: 4px;
    &:before {
      content: url('../media/images/payment_methods.png')
    }
  }
  &.wizacha-icon {
    top: 2px;
    &:before {
      content: url('../media/images/wizacha-icon.png')
    }
  }
  &.delete-item-icon {
    top: 2px;
    &:before {
      content: url('../media/images/delete-icon.png')
    }
  }
  &.secured-icon {
    top: 2px;
    &:before {
      content: url('../media/images/secured-icon.png')
    }
  }
  &.rma-icon {
    top: 2px;
    &:before {
      content: url('../media/images/rma-icon.png')
    }
  }
  &.shipping-icon {
    top: 2px;
    &:before {
      content: url('../media/images/shipping-icon.png')
    }
  }
}

table.productTable {
  margin-bottom: 10px;
  &, & * {
    font-size: @MFont;
  }
  border: 1px solid @paleGrey;
  & tr {
    &:nth-child(1) {
      background-color: @paleGrey;
      background-image: none;
      margin-bottom: 10px;
    }
    border: none;
    & th {
      font-weight: normal;
      text-align: left;
      background-color: @paleGrey;
      background-image: none;
      text-shadow: none;
      border: none;
    }
    & td {
      &.product-description {
        color: @basic;
        font-size: @LFont;
      }
      border: none;
      padding: 10px;
      &:nth-child(1) {
        width: 20%;
      }
      &:nth-child(2) {
        width: 30%;
        border-right: 1px solid @paleGrey;
      }
      &:nth-child(3) {
        width: 25%;
        border-right: 1px solid @paleGrey;
      }
      &:nth-child(4) {
        width: 25%;
      }
      hr {
        .mx-no-wrapping;
      }
    }
  }
}

.w-price {
  font-size: @XXLFont !important;
  &.small-price{
    font-size: @XLFont !important;
  }
  font-weight: bold  !important;
  color: @w-price  !important;
}

table.simple-table {
  font-size: @MFont;
  tr:nth-child(1) {
    background-color: transparent;
    background-image: none;
  }
  td {
    border: 0;
    font-size: @MFont;
  }
}