.multicolumns-list table {
  .product-title-wrap a.product-title {
    max-height: none;
    overflow: auto;
    color: @grey;
    b, strong {
      color: @darkGrey;
    }
  }
  .product-description {
    p {
      .mx-no-wrapping;
    }
    span.price {
      span.price-num {
        .bold;
        color: @w-price;
        font-size: @MFont;
      }
    }
  }
}

.pagination-container {
  .sort-container {
    border: 0;
    background-color: @paleGrey;
    .padded(5px;);
    margin: 20px 0 10px 10px;
  }
}