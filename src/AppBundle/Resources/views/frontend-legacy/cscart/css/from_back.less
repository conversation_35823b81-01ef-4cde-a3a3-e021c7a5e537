//Feature picker
.ajax-popup-tools {
  overflow-x: auto;
  max-height: 150px;
  ul {
    margin-left: 0px;
    li {
      list-style-type: none;
      .mx-no-wrapping;
      a {
        display: block;
        padding: 3px 20px !important;
        clear: both;
        font-weight: normal;
        line-height: 26px;
        color: #333;
        white-space: nowrap;
        &:hover {
          text-decoration: none;
          color: #fff;
          #gradient > .vertical(#08c,#0077b3);
        }
      }
    }
  }
}

.hidden-input {
  display: none;
}


//Image & Upload
.attach-images {
  margin-bottom: 0px;
  .desc {
    color: #868686;
    font-size: 11px;
  }
  .image-wrap {
    position: relative;
    width: 200px;
    .image-delete {
      position: absolute;
      top: -7px;
      left: 152px + 4;
      z-index: 1;
      display: none;
      line-height: 0;
    }
    &:hover {
      .image-delete {
        display: block;
      }
    }
  }
  .upload-box {
    margin-top: 10px;
    margin-bottom: 25px;
    .image {
      .border-radius(3px);
      position: relative;
      display: table-cell;
      margin-right: 15px;
      padding: 5px;
      width: 152px;
      height: 110px;
      border: 1px solid #dddddd;
      background-color: white;
      vertical-align: middle;
      vertical-align: middle;
      text-align: center;
      // tooltip bug
      .tooltip {
        white-space: nowrap;
      }
      a {
        display: block;
        text-align: center;
      }
      img {
        display: inline;
        max-width: 152px;
        max-height: 110px;
        width: auto;
        height: auto;
        line-height: 110px;
      }
      .no-image {
        display: block;
        width: 152px;
        height: 108px;
      }
      &:hover{
        border: 1px solid #b9b9b9;
      }
    }
    .image-alt {
      padding-top: 10px;
      .input-append,
      .input-prepend {
        white-space: normal;
      }
      input {
        width: 127px;
      }
    }
  }
  .upload-box:last-child {
    margin-bottom: 0px !important;
  }
}
.image-upload {
  margin: 5px 0px 0px 16px;
  margin-left: 200px;
}
.upload-file-section {
  margin-bottom: 16px;
}
.upload-file-local {
  position: relative;
  display: inline-block;
  cursor: pointer;
  .btn {
    border-right: 0px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
  }
  .image-selector {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    overflow: hidden;
    width: 100%;
    height: 100%;
    cursor: pointer;
  }
  input[type="file"] {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 10;
    width: 100%;
    height: 30px;
    font-size: 50px;
    opacity: 0;
    filter: alpha(opacity=0);
    cursor: pointer;
  }
  &:hover {
    .btn {
      border-color: #BBBBBB;
      background-color: #D8D8D8;
      text-decoration: none;
      #gradient > .vertical(#F8F8F8, #D8D8D8);
    }
  };
}
.image-upload-wrap {
  margin-bottom: 40px;
}

//Sortable
.sortable-box {
  margin-bottom: 13px;
  border: 1px solid #e9e9e9;
  .sortable-bar {
    background: #f5f5f5;
    text-align: center;
    cursor: url("../media/images/openhand.cur"), move;
  }
  .sortable-item {
    padding: 10px;
    .attach-images {
      margin-bottom: 10px;
    }
  }
}

//Categories picker
@extraIconsSprite: url(../media/images/exicons.png);

// Extra icons
[class^="exicon-"], [class*=" exicon-"] {
  display: inline-block;
  width: 14px;
  height: 14px;
  background-image: @extraIconsSprite;
  background-position: 14px 14px;
  background-repeat: no-repeat;
  vertical-align: text-top;
  line-height: 14px;
}
.btn [class^="exicon-"], .btn [class*=" exicon-"], .btn [class^="icon-"], .btn [class*=" icon-"] {
  opacity: 0.7;
}
.btn:hover [class^="exicon-"], .btn:hover [class*=" exicon-"], .btn:hover [class^="icon-"], .btn:hover [class*=" icon-"] {
  opacity: 1;
}
.exicon-white {
  background-image: @extraIconsSpriteWhite;
}
.exicon-categories {
  background-position: 0 2px;
}
.exicon-products {
  background-position: -24px 2px;
}
.exicon-product_features {
  background-position: -24px 2px;
}
.exicon-product_filters {
  background-position: -48px 2px;
}
.exicon-global_options {
  background-position: -72px 2px;
}
.exicon-promotions {
  background-position: -96px 2px;
}
.exicon-back {
  background-position: -120px 2px;
}
.exicon-collapse {
  margin-top: 4px;
  background-position: -144px 2px;
  opacity: 0.3;
  cursor: pointer;
}
.collapsed .exicon-collapse {
  margin-top: 4px;
  background-position: -164px 0px;
}
.exicon-expand {
  margin-top: 4px;
  background-position: -164px 0px;
  opacity: 0.3;
  cursor: pointer;
}
.subheader.collapsed span {
  margin-top: 1px;
  background-position: -168px 5px;
  opacity: 1;
}
.exicon-preview {
  background-position: -192px 2px;
}
.exicon-clone {
  background-position: -214px 2px;
}
.exicon-desc {
  background-position: -235px 7px;
  opacity: 0.3;
}
.exicon-asc {
  background-position: -140px 7px;
  opacity: 0.3;
}
.exicon-usergroup {
  background-position: -288px 2px;
}
.exicon-cog {
  background-position: -312px 2px;
}
.exicon-off {
  background-position: -336px 2px;
}
.exicon-trash {
  background-position: -360px 2px;
}
.exicon-car {
  width: 24px;
  background-position: -384px 2px;
}
.exicon-charge {
  width: 22px !important;
  background-position: -408px 4px;
}
.exicon-ufa {
  display: inline-block;
  width: 20px;
  background-position: -456px 2px;
}
.exicon-ufa.visible {
  background-position: -432px 2px;
}
.exicon-statistic {
  background-position: -480px 2px;
}
.exicon-cogicon-edit {
  background-position: -312px 2px;
}
.exicon-box {
  background-position: -504px 0px;
}
.exicon-box-blue {
  background-position: -528px 0px;
}
.icons-bar a {
  margin-right: 5px;
}
.unedited-element, .undeleted-element {
  cursor: not-allowed;
}


// Disabled and read-only inputs
input[disabled],
select[disabled],
textarea[disabled],
input[readonly],
select[readonly],
textarea[readonly] {
  cursor: not-allowed;
  background-color: #eee;
}
// Explicitly reset the colors here
input[type="radio"][disabled],
input[type="checkbox"][disabled],
input[type="radio"][readonly],
input[type="checkbox"][readonly] {
  background-color: transparent;
}

input.input-large {
  width:50%;
}