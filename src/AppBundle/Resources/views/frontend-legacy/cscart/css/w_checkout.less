@localHeight: 280px;

.register-content {
  min-height: @localHeight;
  width: 7 * @gridColumnWidth + 6 * @gridGutterWidth - 2 * @gridGutterWidth - 2;
}

.login-form {
  min-height: @localHeight;
  margin-left: @gridGutterWidth;
  width: 6 * @gridColumnWidth + 5 * @gridGutterWidth - 2 * @gridGutterWidth - 2;
}

#inputWrapper{
  display:block;
  width:205px;
}
.forcePasswordDisplay {
  background-color: @paleGrey;
  border: 1px solid @grey;
  border-left: 0;
  height: 33px;
  width:45px;
  line-height: 33px;
  left:-6px;
  input{.mx-no-wrapping;}
  label{display:inline-block; .mx-no-wrapping;}
  & * {
    .mx-no-wrapping;
  }
  padding : 0 4px 0 4px;
}
input.shortened-input{
  width:145px;
  margin:0;
}
.step-title {
  font-size: @LFont;
  padding: @gridGutterWidth;
  border: 0;
  background-color: @paleGrey;
  color: @basic;
}

#step_two {
  border: 1px solid @waterGreen;
  .step-title {
    background-color: @waterGreen;
    color: @white;
  }
}

#step_four {
  border: 1px solid @paleGrey;
  .other-pay {
    div {
      margin: 10px;
    }
  }
  .terms {
    margin: 20px 0 20px 0;
  }
}

#shippingCard {
  .marged;
  hr {
    border-color: @turquoise;
    clear: both;
    margin-top: @gridGutterWidth;
  }
  #addressSummary {
    padding: @gridGutterWidth;
    margin: 0 10px 10px 100px;
    background-color: @paleGrey;
    float: left;
    width: 200px;
    .inline-block;
    .middle;
  }
  a {
    .inline-block;
    .middle;
  }
}

.card {
  margin-left: 20px;
  div {
    .inline-block;
    vertical-align: top;
    padding-right: 10px;
    &.card-content {
      min-height: 40px;
    }
    &.card-title {
      width: 100px;
      text-align: right;
    }
    &.card-button {
      .middle;
      float: right;
    }
  }
}

.order-products .order-product-list li {
  padding: 14px 0 0 0;
  &.sep:not(:last-child) {
    padding-bottom: 10px;
    border-bottom: 1px solid @paleGrey;
  }
}

.paym-methods li {
  display: inline-block;
}

.shipping-adress {
  li {
    &.b-address, &.s-address {
      clear: both;
    }
  }
}

.control-group {
  *, label {
    font-size: @MFont;
    color: @basic;
    font-weight: normal;
    a {
      text-decoration: underline;
    }
  }
}

#stay-in-touch {
  &, & * {
    font-size: @MFont;
    color: @basic;
  }
  li {
    margin-bottom: 10px;
    img {
      margin-right: 10px;
    }
  }
}

.newsletter-block {
  display: block;
  padding: 10px;
  margin-bottom: 10px;
  text-align: center;
  &.wizacha-newsletter {
    background-color: @waterGreen;
    label {
      color: @white;
      font-weight: bold;
    }
  }
  &.partner-newsletter {
    background-color: @paleGrey;
  }
}