@searchEngineMainWidth:  380px;
@searchEngineMainHeight: 42px;
@image_size : 50px;
@categoriesMenuWidth: 160px;

.suggestion-geocoding, .top-search-suggestion-geocoding, .c2c-product-suggestion-geocoding{
  .code-postal{
    float:right;
  }
}

.suggestion-geocoding {width:326px;}
.c2c-product-suggestion-geocoding {width:388px;}
.top-search-suggestion-geocoding {min-width:180px;}

//copy scheme mixin
.gradient (@startColor, @endColor) {
  background-color: @endColor;
  background: -webkit-gradient(linear, left top, left bottom, from(@startColor), to(@endColor));
  background: -webkit-linear-gradient(top, @startColor, @endColor);
  background: -moz-linear-gradient(top, @startColor, @endColor);
  background: -ms-linear-gradient(top, @startColor, @endColor);
  background: -o-linear-gradient(top, @startColor, @endColor);
}

.geocoding-input{
  min-width: 338px;
  &:not([disabled]) {
    .gradient (#fff, #fff);
  }
}

/*FACETS*/
#content_geoloc{
  margin-top:10px;
  input[type=text]{
    &#localization{
      color: @darkGrey;
    }
    width:100%;
  }
  #content_distance{
    padding-top:10px;
  }
}

/*SEARCH BOX*/
div.image_placeholder {
  img{
    max-height: @image_size;
    max-width: @image_size;
  }
  min-height: @image_size;
}

.baseline {
  padding-left: 40px;
}
.top-search {
  text-align: center;
  padding-top: 20px;
  padding-left: 40px;
  #search-block {
    display: inline-block;
    vertical-align: middle;
    .labels
    {
      width: @searchEngineMainWidth;
      padding-bottom:3px;
      margin-left: @categoriesMenuWidth + 40;
      div{
        display: inline-block;
        width: 49%;
        color:@white;
      }
    }
    .multi-search{
      width: @searchEngineMainWidth - 10;
      height: @searchEngineMainHeight - 10;
      padding:5px;
      display:inline-block;
      vertical-align: middle;
      background-color: @white;
      .first-field, .second-field{
        display:inline-block;
        vertical-align: middle;
      }
      .second-field{
        padding-left:20px;
      }
      input.search-input, input[readonly].search-input, input.geoloc-input, i{
        display:inline-block;
        color:@darkGrey;
        vertical-align: middle !important;
        line-height: 30px !important;
        border:0;
        .mx-no-margin;
        padding:0;
        background: none;
      }
      input.search-input, input.geoloc-input {
        border-bottom:1px solid #aaa;
        height:30px;
        box-shadow: none;
        width:150px;
        &.tt-hint{
          color:@grayLight;
        }
      }
      i{padding-right:2px;}
      .first-field .tt-dropdown-menu {
        margin-top:7px;
        margin-left:-18px;
        width:@searchEngineMainWidth - 12;
      }
      .second-field .tt-dropdown-menu {
        width:@searchEngineMainWidth - 12;
        margin-left:-18px;
      }
    }
  }
}

div#search_engine_results_page{
  .search-result-cell{
    display:inline-block;
    vertical-align: top;
  }
  a.hit{
    margin: 2px;
    display: inline-block;
    width: 124px;
    text-align: center;
    vertical-align: top;
    div.image_placeholder{
      img {
        margin:0 auto;
      }
    }
  }
  .search-result-block-title{
    text-align: left;
    font-weight: bold;
  }
  hr{
    margin: 5px 0 5px 0;
  }
  em{
    font-style: normal;
    font-weight: bold;
  }
}

div.search_results_4cols .search-result-cell{
  width:25%;
}
div.search_results_3cols .search-result-cell{
  width:33%;
}

span.tt-dropdown-menu{
  a.cm-submit{
    color: @darkGrey;
    display: block;
    font-weight: bold;
    text-align: right;
  }
  border: 1px solid #e0e0e0;
  .search-result-block-title{
    text-align: center;
    font-weight: bold;
  }
  hr{
    margin: 5px 0 5px 0;
  }
  background-color: @white;
  padding: 0 5px 0 5px;
}

div.tt-suggestion{
  width:100%;
  .search-result-block-title{
    text-align: center;
    font-weight: bold;
  }
  &.tt-cursor{
    background-color: @paleGrey;
  }
  a.hit{
    display:block;
    &:hover{
      background-color: @paleGrey;
    }
  }
  div.image_placeholder {
    float:left;
    width:@image_size;
    height:@image_size;
    margin-right:3px;
  }
  em{
    font-style: normal;
    font-weight: bold;
  }
  .infos{
    vertical-align: middle;
    width: @searchEngineMainWidth - 70;
    margin-left:5px;
    .name{
      text-align: left;
    }
    .price{
      color:@price;
    }
  }
}


