/*-- Slider--*/
.cm-slider {
    position: relative;
}
/*--Window/Masking Styles--*/
.cm-slider-window {
    width: 100%;
    overflow: hidden; /*--Hides anything outside of the set width/height--*/
    position: relative;
}
.cm-slide-page-reel {
    position: absolute;
    top: 0; left: 0;
}
.cm-slide-page-reel img {
    border: 0; padding: 0;
    max-width: 100%;
}
/*--Paging Styles--*/
.cm-paging {
    position: absolute;
    bottom: 20px; 
    right: 25px;
    width: 200px; 
    height:20px;
    margin-bottom: 10px;
    color: #808080;
    text-align: right;
    line-height: 40px;
    display: none; /*--Hidden by default, will be later shown with jQuery--*/
}
.cm-paging a, .cm-paging a:visited, .cm-paging a:hover, .cm-paging a:active {
    display: inline-block;
    padding: 5px 8px;
    text-decoration: none;
    text-align: center;
    color: #808080;
    line-height: 100%;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px;
}
.cm-paging a.active {
    font-weight: bold;
    color: #fff;
    -moz-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-radius: 3px;
}
.cm-paging-dots a, .cm-paging-dots a:hover {
    position: relative;
    width: 10px;
    height: 10px;
    padding: 0;
    margin-right: 7px;
    -webkit-box-shadow: inset 0 1px rgba(0,0,0,0.2);
    -moz-box-shadow:    inset 0 1px rgba(0,0,0,0.2);
    box-shadow:         inset 0 1px rgba(0,0,0,0.2);
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
}
.cm-paging-dots a.active {
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
}
.cm-paging-dots i, .cm-paging-dots a.active i, .cm-paging-dots a.active:hover i {
    position: absolute;
    left: 0;
    visibility: visible;
    display: inline-block;
    width: 6px;
    height: 6px;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    margin: 2px;
}
.cm-paging-dots a i {
    visibility: hidden;
}
.cm-slide-page {
    float: left;
    text-align: center;
    vertical-align: middle;
}
/*-- Arrows --*/
.cm-slide-prev, .cm-slide-next {
    position: absolute;
    height: 36px;
    bottom: 50px;
    cursor: pointer;
    background-repeat: no-repeat;
}
.cm-slide-prev i, .cm-slide-next i {
    font-size: 60px;
}
.cm-slide-prev {
    left: 15px;
}
.cm-slide-next {
    right: 15px;
}
/*-- /Slider--*/

/* Ads */
.ad-container {    /* to crop a large image */
    overflow: hidden;
    margin: 1px auto 11px auto;
    width: 100%;
}
/* /Ads */