/*DEFINITIONS*/
@white: #FFFFFF;
@marine : #2C3E50;
@darkGrey : #404040;
@grey : #95A5A6;
//@paleGrey : #EFEFEF;
@paleGrey : #efefef;
@basic : #333333;
@w-red : #C0392B;
@w-green : #339900;
@w-price : @w-red;
@w-orange : #e67e22;
@waterGreen : #009898;
@darkWaterGreen : #008585;
@turquoise : #16a085;

@XXLFont : 42px;
@XLFont : 24px;
@LFont : 18px;
@MFont : 14px;
@SFont: 12px;
@XSFont: 11px;

/*TESTS*/
@testBorder : 1px solid red;

@main_bg_color: #fff;
@main_first_color: #009a9b;
@main_second_color: #e84e0f;
@main_third_color: #7f8c8d;

/*Media queries breakpoints, defined from bootstrap*/
@screen-phone: 480px;
@screen-tablet: 768px;
@screen-desktop: 992px;
@screen-lg-desktop: 1200px;

/*STYLE IMPORT*/
@import "grid";
@import "w_general";
@import "w_product_card";
@import "w_product_type";
@import "w_cart";
@import "w_checkout";
@import "w_categories";
@import "w_b2b";
@import "w_universes";
@import "w_tables";
@import "from_back";
@import "w_banners";
@import "w_search";
@import "w_discuss";

/*****************************
 * GRID
 ****************************/
.col-xs-1, .col-sm-1, .col-md-1, .col-lg-1, .col-xs-2, .col-sm-2, .col-md-2, .col-lg-2, .col-xs-3, .col-sm-3, .col-md-3, .col-lg-3, .col-xs-4, .col-sm-4, .col-md-4, .col-lg-4, .col-xs-5, .col-sm-5, .col-md-5, .col-lg-5, .col-xs-6, .col-sm-6, .col-md-6, .col-lg-6, .col-xs-7, .col-sm-7, .col-md-7, .col-lg-7, .col-xs-8, .col-sm-8, .col-md-8, .col-lg-8, .col-xs-9, .col-sm-9, .col-md-9, .col-lg-9, .col-xs-10, .col-sm-10, .col-md-10, .col-lg-10, .col-xs-11, .col-sm-11, .col-md-11, .col-lg-11, .col-xs-12, .col-sm-12, .col-md-12, .col-lg-12{position:relative;min-height:1px;padding-left:7.5px;padding-right:7.5px}.col-xs-1, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9, .col-xs-10, .col-xs-11, .col-xs-12{float:left}.col-xs-12{width:100%}.col-xs-11{width:91.66666667%}.col-xs-10{width:83.33333333%}.col-xs-9{width:75%}.col-xs-8{width:66.66666667%}.col-xs-7{width:58.33333333%}.col-xs-6{width:50%}.col-xs-5{width:41.66666667%}.col-xs-4{width:33.33333333%}.col-xs-3{width:25%}.col-xs-2{width:16.66666667%}.col-xs-1{width:8.33333333%}.col-xs-pull-12{right:100%}.col-xs-pull-11{right:91.66666667%}.col-xs-pull-10{right:83.33333333%}.col-xs-pull-9{right:75%}.col-xs-pull-8{right:66.66666667%}.col-xs-pull-7{right:58.33333333%}.col-xs-pull-6{right:50%}.col-xs-pull-5{right:41.66666667%}.col-xs-pull-4{right:33.33333333%}.col-xs-pull-3{right:25%}.col-xs-pull-2{right:16.66666667%}.col-xs-pull-1{right:8.33333333%}.col-xs-pull-0{right:auto}.col-xs-push-12{left:100%}.col-xs-push-11{left:91.66666667%}.col-xs-push-10{left:83.33333333%}.col-xs-push-9{left:75%}.col-xs-push-8{left:66.66666667%}.col-xs-push-7{left:58.33333333%}.col-xs-push-6{left:50%}.col-xs-push-5{left:41.66666667%}.col-xs-push-4{left:33.33333333%}.col-xs-push-3{left:25%}.col-xs-push-2{left:16.66666667%}.col-xs-push-1{left:8.33333333%}.col-xs-push-0{left:auto}.col-xs-offset-12{margin-left:100%}.col-xs-offset-11{margin-left:91.66666667%}.col-xs-offset-10{margin-left:83.33333333%}.col-xs-offset-9{margin-left:75%}.col-xs-offset-8{margin-left:66.66666667%}.col-xs-offset-7{margin-left:58.33333333%}.col-xs-offset-6{margin-left:50%}.col-xs-offset-5{margin-left:41.66666667%}.col-xs-offset-4{margin-left:33.33333333%}.col-xs-offset-3{margin-left:25%}.col-xs-offset-2{margin-left:16.66666667%}.col-xs-offset-1{margin-left:8.33333333%}.col-xs-offset-0{margin-left:0}
.col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12{float:left}.col-sm-12{width:100%}.col-sm-11{width:91.66666667%}.col-sm-10{width:83.33333333%}.col-sm-9{width:75%}.col-sm-8{width:66.66666667%}.col-sm-7{width:58.33333333%}.col-sm-6{width:50%}.col-sm-5{width:41.66666667%}.col-sm-4{width:33.33333333%}.col-sm-3{width:25%}.col-sm-2{width:16.66666667%}.col-sm-1{width:8.33333333%}.col-sm-pull-12{right:100%}.col-sm-pull-11{right:91.66666667%}.col-sm-pull-10{right:83.33333333%}.col-sm-pull-9{right:75%}.col-sm-pull-8{right:66.66666667%}.col-sm-pull-7{right:58.33333333%}.col-sm-pull-6{right:50%}.col-sm-pull-5{right:41.66666667%}.col-sm-pull-4{right:33.33333333%}.col-sm-pull-3{right:25%}.col-sm-pull-2{right:16.66666667%}.col-sm-pull-1{right:8.33333333%}.col-sm-pull-0{right:auto}.col-sm-push-12{left:100%}.col-sm-push-11{left:91.66666667%}.col-sm-push-10{left:83.33333333%}.col-sm-push-9{left:75%}.col-sm-push-8{left:66.66666667%}.col-sm-push-7{left:58.33333333%}.col-sm-push-6{left:50%}.col-sm-push-5{left:41.66666667%}.col-sm-push-4{left:33.33333333%}.col-sm-push-3{left:25%}.col-sm-push-2{left:16.66666667%}.col-sm-push-1{left:8.33333333%}.col-sm-push-0{left:auto}.col-sm-offset-12{margin-left:100%}.col-sm-offset-11{margin-left:91.66666667%}.col-sm-offset-10{margin-left:83.33333333%}.col-sm-offset-9{margin-left:75%}.col-sm-offset-8{margin-left:66.66666667%}.col-sm-offset-7{margin-left:58.33333333%}.col-sm-offset-6{margin-left:50%}.col-sm-offset-5{margin-left:41.66666667%}.col-sm-offset-4{margin-left:33.33333333%}.col-sm-offset-3{margin-left:25%}.col-sm-offset-2{margin-left:16.66666667%}.col-sm-offset-1{margin-left:8.33333333%}.col-sm-offset-0{margin-left:0}
.col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12{float:left}.col-md-12{width:100%}.col-md-11{width:91.66666667%}.col-md-10{width:83.33333333%}.col-md-9{width:75%}.col-md-8{width:66.66666667%}.col-md-7{width:58.33333333%}.col-md-6{width:50%}.col-md-5{width:41.66666667%}.col-md-4{width:33.33333333%}.col-md-3{width:25%}.col-md-2{width:16.66666667%}.col-md-1{width:8.33333333%}.col-md-pull-12{right:100%}.col-md-pull-11{right:91.66666667%}.col-md-pull-10{right:83.33333333%}.col-md-pull-9{right:75%}.col-md-pull-8{right:66.66666667%}.col-md-pull-7{right:58.33333333%}.col-md-pull-6{right:50%}.col-md-pull-5{right:41.66666667%}.col-md-pull-4{right:33.33333333%}.col-md-pull-3{right:25%}.col-md-pull-2{right:16.66666667%}.col-md-pull-1{right:8.33333333%}.col-md-pull-0{right:auto}.col-md-push-12{left:100%}.col-md-push-11{left:91.66666667%}.col-md-push-10{left:83.33333333%}.col-md-push-9{left:75%}.col-md-push-8{left:66.66666667%}.col-md-push-7{left:58.33333333%}.col-md-push-6{left:50%}.col-md-push-5{left:41.66666667%}.col-md-push-4{left:33.33333333%}.col-md-push-3{left:25%}.col-md-push-2{left:16.66666667%}.col-md-push-1{left:8.33333333%}.col-md-push-0{left:auto}.col-md-offset-12{margin-left:100%}.col-md-offset-11{margin-left:91.66666667%}.col-md-offset-10{margin-left:83.33333333%}.col-md-offset-9{margin-left:75%}.col-md-offset-8{margin-left:66.66666667%}.col-md-offset-7{margin-left:58.33333333%}.col-md-offset-6{margin-left:50%}.col-md-offset-5{margin-left:41.66666667%}.col-md-offset-4{margin-left:33.33333333%}.col-md-offset-3{margin-left:25%}.col-md-offset-2{margin-left:16.66666667%}.col-md-offset-1{margin-left:8.33333333%}.col-md-offset-0{margin-left:0}
header * {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box
}
.row {
  margin-left: -7.5px;
  margin-right: -7.5px;
}
.dropup,.dropdown{position:relative}.dropdown-toggle:focus{outline:0}.dropdown-menu{position:absolute;top:100%;left:0;z-index:1000;display:none;float:left;min-width:160px;padding:5px 0;margin:2px 0 0;list-style:none;font-size:14px;text-align:left;background-color:#fff;border:1px solid #ccc;border:1px solid rgba(0,0,0,0.15);border-radius:0;-webkit-box-shadow:0 6px 12px rgba(0,0,0,0.175);box-shadow:0 6px 12px rgba(0,0,0,0.175);-webkit-background-clip:padding-box;background-clip:padding-box}.dropdown-menu.pull-right{right:0;left:auto}.dropdown-menu .divider{height:1px;margin:9px 0;overflow:hidden;background-color:#e5e5e5}.dropdown-menu>li>a{display:block;padding:3px 20px;clear:both;font-weight:normal;line-height:1.42857143;color:#333;white-space:nowrap}.dropdown-menu>li>a:hover,.dropdown-menu>li>a:focus{text-decoration:none;color:#262626;background-color:#f5f5f5}.dropdown-menu>.active>a,.dropdown-menu>.active>a:hover,.dropdown-menu>.active>a:focus{color:#fff;text-decoration:none;outline:0;background-color:#337ab7}.dropdown-menu>.disabled>a,.dropdown-menu>.disabled>a:hover,.dropdown-menu>.disabled>a:focus{color:#777}.dropdown-menu>.disabled>a:hover,.dropdown-menu>.disabled>a:focus{text-decoration:none;background-color:transparent;background-image:none;filter:progid:DXImageTransform.Microsoft.gradient(enabled = false);cursor:not-allowed}.open>.dropdown-menu{display:block}.open>a{outline:0}.dropdown-menu-right{left:auto;right:0}.dropdown-menu-left{left:0;right:auto}.dropdown-header{display:block;padding:3px 20px;font-size:12px;line-height:1.42857143;color:#777;white-space:nowrap}.dropdown-backdrop{position:fixed;left:0;right:0;bottom:0;top:0;z-index:990}.pull-right>.dropdown-menu{right:0;left:auto}.dropup .caret,.navbar-fixed-bottom .dropdown .caret{border-top:0;border-bottom:4px solid;content:""}.dropup .dropdown-menu,.navbar-fixed-bottom .dropdown .dropdown-menu{top:auto;bottom:100%;margin-bottom:2px}
@media (min-width:768px){.navbar-right .dropdown-menu{left:auto;right:0}.navbar-right .dropdown-menu-left{left:0;right:auto}}

/*****************************
 * HEADER TOP
 ****************************/
.floatable{
  position: fixed;
  display: block;
  z-index: 100;
  top: 0px;
  border-bottom: 0px;
}

#header-mini {
  .search-form {
    padding:3px;
    margin: -5px 0 0 0;
  }
}

.header-top{
  height: 50px;
  width: 100%;
  background-color: @main_bg_color;

  img{
    margin-top: 5px;
  }

  nav{
    float: right;
    margin-top: -7px;
  }

  li{
    display: inline-block;
    vertical-align: middle;
    margin-left: 15px;
  }

  a{
    font-family: "Quicksand", Verdana, Arial, sans-serif;
    font-size: 14px;
    color: #474747;
    &:hover, &:active, &:focus{
      text-decoration: none;
    }
  }

  .dropdown-menu {
    padding: 20px;
    border: none;
    margin-top: 17px;

    #connect-form {
      min-width: 300px;
    }
  }
  .input-text {
    width: auto;
  }
}

.nav-top{

  a{
    color: #474747;
    font-family: "Quicksand", Verdana, Arial, sans-serif;
  }

  a:hover{
    color: @main_first_color;
  }
}


#moncompte{
  text-align: left;

  input{
    margin-bottom: 10px;
    width: 100%;
  }

  button{
    width: 100%;
    margin: 15px 0px;
    font-size: 16px;
    border: 1px solid @main_second_color;
    background-color: @main_second_color;
  }

  a{
    font-size: 12px;
    color: #696969;

    .create-compte{
      text-align: center;
      font-size: 14px;
      font-family: "Quicksand";
      font-weight: bold;
      color: @main_second_color;
    }
  }

  p{
    font-size: 11px;
  }
}


#monpanier{
  button{
    width: 100%;
    margin: 5px 0px;
    font-size: 16px;
    border: 1px solid @main_second_color;
    background-color: @main_second_color;
  }
}

.panier-image{
  height: 50px;
  width: 50px;
  background-color: #efefef;
}

.content-panier{
  min-width: 200px;
  text-align: left;

  p{
    font-size: 12px;
    color: @main_third_color;
  }
}

.top-menu {
  padding-top: 12px;
}

.top-menu-responsive{
  li button.square {
    width: 35px;
    height: 35px;
    padding: 0px;
    border: 1px solid #efefef;
    img{

      max-width: 25px;
      height: auto;
    }
  }
}


/*****************************
 * HEADER BOTTOM
 ****************************/
.header-bottom-search{
  height: 200px;
  background-color: #000;
  text-align: center;
  background: url(../../../assets/images/db.jpg);
  background-position:  center;
  background-size: 100% auto;
  background-repeat: no-repeat ;

  .filtre-header-bottom{
    background-color: rgba(26, 36, 44, 0.6);
    height: 100%;
  }

  .search-form-home{
    background-color: @main_bg_color;
  }

  h1{
    font-size: 40px;
    margin-top: 75px;
    color: @main_bg_color;
  }

  h2{
    font-size: 30px;
    color: @main_bg_color;
  }

  form{
    margin-top: 55px;
    text-align: center;
    border: 12px solid rgba(0, 0, 0, 0.6);
    p{
      color: #888;
      position: relative;
      display: inline-block;
      vertical-align: middle;
      top: 3px;
    }
    input{
      height: 60px;
      width: 100%;
      padding: 10px;
      border: 0px solid #f8f8f8;
      font-size: 15px;
      font-family: Verdana, Arial;
      margin: 0;
      border-radius: 0;

      &:focus {
        outline: none;
        box-shadow: none;
      }
    }

    button{
      height: 60px;
      width: 100%;
      color: @main_bg_color;
      border: none;
      text-transform: uppercase;
      background-color: @main_third_color;
    }

    .search-category{
      height: 60px;
      background-color: #f3f3f3;
    }

  }
}

.arrow-bottom{
  width: 0;
  height: 0;
  display: inline-block;
  vertical-align: middle;
  position: relative;
  left: 15px;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #888;
}

.header-bottom-concept{
  background-color: #efefef;
  height: 80px;
  padding: 10px 0px;
  box-shadow: 0 5px 3px #d3d3d3;

  img{
    position: relative;
    top: 15px;
  }

  p{
    margin: 0px;
    padding: 0px;
    color: #3d464e;
    font-size: 18px;
    font-family: "Quicksand";
    font-weight: 700;
    margin-left: 13px;
    line-height: 1.2em;
    position: relative;
    top: 9px;
  }

  button{

    border: 1px solid @main_third_color;
    color: @main_third_color;
    font-size: 14px;
    height: 55px;
  }
}


/*****************************
 * FOOTER
 ****************************/

footer {
  background-color: #ffffff;
  padding: 25px 0px 50px 0px;
  line-height: 1.42857;

  a{
    &:hover, &:active, &:focus{
      text-decoration: none;
    }
  }

  h4, a {
    font-family: "Quicksand",Verdana,Arial,sans-serif;
  }
  h4 {
    font-size: 18px;
    margin-bottom: 0px;
    color: #009a9b;
    font-weight: normal;
  }
  ul {
    padding: 0px;
  }
  li {
    text-decoration: none;
    list-style: none;
  }
  a {
    color: #7f8c8d;
    font-size: 14px;
  }
  a:hover {
    color: #009a9b;
  }

  .social-footer {

    li {
      display: inline-block;
      vertical-align: middle;
      margin: 0px 5px;

      img {
        max-width: 20px;
        height: auto;
        display: block;
        margin: auto;
        position: relative;
        top: 10px;
      }
    }
    a {
      color: #e84e0f;
      font-size: 15px;
      .circle-social {
        height: 40px;
        width: 40px;
        border-radius: 50%;
        border: 1px solid #7f8c8d;
      }
    }
  }

}

/*****************************
 * BUTTONS
 ****************************/
button, .btn, [class*=btn-] {

  &, a& {
    &, &:hover {
      font-family: "Quicksand";
      font-weight: bold;
      padding: 8px 10px 8px 10px;
      background: @main_third_color;
      border: none;
      color: @main_bg_color;
      display: inline-block;
      box-shadow: none;
      cursor: pointer;
      text-shadow: none;
      border-radius: 0;
    }
  }

  &.btn-primary{
    background-color: @main_second_color;
  }
}


/*****************************
 * SEARCH
 ****************************/
.main-search {
  cursor: pointer;
  height:100%;
  line-height: 60px;
  color:#888;
  background-color: #fafafa;
}

.current > span {
    font-weight: bold;
}

#categories-menu-content{

  position: absolute;
  z-index: 100;
  text-align: left;
  background-color: #fff;
  display: none;
  cursor: pointer;
  width: 100%;
  padding: 20px;

  button {
    min-width: 50%;
    height: 40px;
    margin: 10px 0;
  }

  ul{

    padding: 0px;
  }

  li{

    list-style: none;
    margin-bottom: 5px;

    ul{

      padding: 10px 20px;
      display: none;

      span{

        color: #626262;
      }

    }

  }

  #active-category{

    font-weight: bold;
  }

}

.tt-dropdown-menu {
  background-color: @main_bg_color;
  .tt-cursor {
    background-color: @main_third_color;
  }
}

/*****************************
 * GENERAL
 ****************************/
header input, #content-wrapper .header-top input {
    display: inline-block;
    vertical-align: middle;
    margin: 8px 0;
    height: 35px;
    border-radius: 3px;
    border: 1px solid #d3d3d3;
    background-color: #fff;
    padding: 1px 5px;
}
small {
  font-size: 80%;
}
hr{-moz-box-sizing:content-box;-webkit-box-sizing:content-box;box-sizing:content-box;height:0}
hr{margin-top:20px;margin-bottom:20px;border:0;border-top:1px solid #eee}