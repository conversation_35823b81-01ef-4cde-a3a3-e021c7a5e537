{if $order_info && $display_order_summary}

{assign var="order_header" value=__("order_details")}


<table cellpadding="0" cellspacing="0" border="0" width="100%" class="main-table" style="background-color: #f4f6f8; font-size: 12px; font-family: Arial;">
<tr>
<td align="center" style="width: 100%; height: 100%;">
<table cellpadding="0" cellspacing="0" border="0" style=" width: 602px; table-layout: fixed; margin: 24px 0 24px 0;">
<tr>
<td style="background-color: #ffffff; border: 1px solid #e6e6e6; margin: 0px auto 0px auto; padding: 0px 44px 0px 46px; text-align: left;">
<table cellpadding="0" cellspacing="0" border="0" width="100%" style="padding: 27px 0px 0px 0px; border-bottom: 1px solid #868686; margin-bottom: 8px;">
    <tr>
        <td align="left" style="padding-bottom: 3px;" valign="middle"></td>
        <td width="100%" valign="bottom" style="text-align: right;  font: bold 26px Arial; text-transform: uppercase;  margin: 0px;">{$order_header|default:__("invoice_title")}</td>
    </tr>
</table>

<table cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr valign="top">

        <td style="width: 50%; padding: 14px 0px 0px 2px; font-size: 12px; font-family: Arial;"></td>
            <td style="padding-top: 14px;">
                <h2 style="font: bold 17px Tahoma; margin: 0px;">{if $doc_id_text}{$doc_id_text} <br />{/if}{__("order")}&nbsp;#{$order_info.order_id}</h2>
                <table cellpadding="0" cellspacing="0" border="0">
                    <tr valign="top">
                        <td style="font-size: 12px; font-family: verdana, helvetica, arial, sans-serif; text-transform: uppercase; color: #000000; padding-right: 10px; white-space: nowrap;">{__("date")}:</td>
                        <td style="font-size: 12px; font-family: Arial;">{$order_info.timestamp|date_format:"`$settings.Appearance.date_format`, `$settings.Appearance.time_format`"}</td>
                    </tr>
                    <tr valign="top">
                        <td style="font-size: 12px; font-family: verdana, helvetica, arial, sans-serif; text-transform: uppercase; color: #000000; padding-right: 10px; white-space: nowrap;">{__("payment_method")}:</td>
                        <td style="font-size: 12px; font-family: Arial;">{$payment_method.payment|default:" - "}</td>
                    </tr>
                    <tr valign="top">
                        <td style="font-size: 12px; font-family: verdana, helvetica, arial, sans-serif; text-transform: uppercase; color: #000000; padding-right: 10px; white-space: nowrap;">{__("w_client_id")}</td>
                        <td style="font-size: 12px; font-family: Arial;">{$order_info.user_id}</td>
                    </tr>
                    {if $order_info.shipping}
                        <tr valign="top">
                            <td style="font-size: 12px; font-family: verdana, helvetica, arial, sans-serif; text-transform: uppercase; color: #000000; padding-right: 10px; white-space: nowrap;">{__("shipping_method")}:</td>
                            <td style="font-size: 12px; font-family: Arial;">
                                {foreach from=$order_info.shipping item="shipping" name="f_shipp"}
                                    {$shipping.shipping}{if !$smarty.foreach.f_shipp.last}, {/if}
                                    {if $shipments[$shipping.group_key].tracking_number}{assign var="tracking_number_exists" value="Y"}{/if}
                                {/foreach}</td>
                        </tr>
                        {if $tracking_number_exists && !$use_shipments}
                            <tr valign="top">
                                <td style="font-size: 12px; font-family: verdana, helvetica, arial, sans-serif; text-transform: uppercase; color: #000000; padding-right: 10px; white-space: nowrap;">{__("tracking_number")}:</td>
                                <td style="font-size: 12px; font-family: Arial;">
                                    {foreach from=$order_info.shipping item="shipping" name="f_shipp"}
                                        {if $shipments[$shipping.group_key].tracking_number}{$shipments[$shipping.group_key].tracking_number}{if !$smarty.foreach.f_shipp.last},{/if}{/if}
                                    {/foreach}</td>
                            </tr>
                        {/if}
                    {/if}
                </table>
            </td>
    </tr>
</table>

{assign var="profile_fields" value='wInvoice'|fn_get_profile_fields}
{if $profile_fields}
    <table cellpadding="0" cellspacing="0" border="0" width="100%" style="padding: 32px 0px 24px 0px;">
        <tr valign="top">
            <td width="34%" style="font-size: 12px; font-family: Arial;padding-right: 10px;"></td>
            {if $profile_fields.S && $order_info.shipping && !$order_info.shipping_type.is_hand_delivery}
                {assign var="profields_s" value=$profile_fields.S|fn_fields_from_multi_level:"field_name":"field_id"}
                <td width="33%" style="font-size: 12px; font-family: Arial;">
                    <h3 style="font: bold 17px Tahoma; padding: 0px 0px 3px 1px; margin: 0px;">{__("ship_to")}:</h3>
                    <p style="margin: 2px 0px 3px 0px;">
                        {foreach from=$profile_fields.S item=field name="fields"}
                        {assign var="value" value=$order_info|fn_get_profile_field_value:$field}
                        {if $value}
                        {if $field.w_new_line=='Y'}
                    </p><p style="margin: 2px 0px 3px 0px;">
                        {/if}
                        {$value}
                        {/if}
                        {/foreach}
                    </p>
                </td>
            {/if}
        </tr>
    </table>
{/if}
{* Customer info *}


{* Ordered products *}

<table width="100%" cellpadding="0" cellspacing="1" style="background-color: #dddddd;">
    <tr>
        <th width="70%" style="background-color: #eeeeee; padding: 6px 10px; white-space: nowrap; font-size: 12px; font-family: Arial;">{__("product")}</th>
        <th style="background-color: #eeeeee; padding: 6px 10px; white-space: nowrap; font-size: 12px; font-family: Arial;">{__("quantity")}</th>
        <th style="background-color: #eeeeee; padding: 6px 10px; white-space: nowrap; font-size: 12px; font-family: Arial;">{__("unit_price")}</th>
        {if $order_info.use_discount}
            <th style="background-color: #eeeeee; padding: 6px 10px; white-space: nowrap; font-size: 12px; font-family: Arial;">{__("discount")}</th>
        {/if}
        {if $order_info.taxes}
            <th style="background-color: #eeeeee; padding: 6px 10px; white-space: nowrap; font-size: 12px; font-family: Arial;">{__("w_ht_price")}</th>
        {/if}
        <th style="background-color: #eeeeee; padding: 6px 10px; white-space: nowrap; font-size: 12px; font-family: Arial;">{__("w_ht_subtotal")}</th>
    </tr>
    {foreach from=$order_info.products item="oi"}
        {if !$oi.extra.parent}
            <tr>
                <td style="padding: 5px 10px; background-color: #ffffff; font-size: 12px; font-family: Arial;">
                    {$oi.product|default:__("deleted_product") nofilter}
                    {if $oi.product_code}<p style="margin: 2px 0px 3px 0px;">{__("sku")}: {$oi.product_code}</p>{/if}
                    {if $oi.extra.w_green_tax != 0}<p style="margin: 2px 0px 3px 0px;">{__("w_green_tax")}: {include file="common/price.tpl" value=$oi.extra.w_green_tax}</p>{/if}

                    {if $product.returns_info}
                        {if !$return_statuses}{assign var="return_statuses" value=$smarty.const.STATUSES_RETURN|fn_get_simple_statuses}{/if}
                        <p><a class="cm-combination combination-link" id="sw_ret_{$key}"><i title="{__("expand_sublist_of_items")}" id="on_ret_{$key}" class="icon-right-dir dir-list"></i><i title="{__("collapse_sublist_of_items")}" id="off_ret_{$key}" class="icon-down-dir dir-list hidden"></i>{__("returns_info")}</a></p>
                        <div class="box hidden" id="ret_{$key}">
                            {foreach from=$product.returns_info item="amount" key="status" name="f_rinfo"}
                                <p><strong>{$return_statuses.$status|default:""}</strong>:&nbsp;{$amount} {__("items")}</p>
                            {/foreach}
                        </div>
                    {/if}

                    {if $oi.product_options}<br/>{include file="common/options_info.tpl" product_options=$oi.product_options}{/if}

                    {if $oi.comment}
                        <br />
                        <strong>{__("comment")}:</strong>
                        <br />
                        {$oi.comment}
                    {/if}
                </td>
                <td style="padding: 5px 10px; background-color: #ffffff; text-align: center; font-size: 12px; font-family: Arial;">{$oi.amount}</td>
                <td style="padding: 5px 10px; background-color: #ffffff; text-align: right; font-size: 12px; font-family: Arial;">{if $oi.extra.exclude_from_calculate}{__("free")}{else}{include file="common/price.tpl" value=$oi.original_price}{/if}</td>
                {if $order_info.use_discount}
                    <td style="padding: 5px 10px; background-color: #ffffff; text-align: right; font-size: 12px; font-family: Arial;">{if $oi.extra.discount|floatval}{include file="common/price.tpl" value=$oi.extra.discount}{else}&nbsp;-&nbsp;{/if}</td>
                {/if}
                {if $order_info.taxes}
                    <td style="padding: 5px 10px; background-color: #ffffff; text-align: right; font-size: 12px; font-family: Arial;">{if $oi.tax_value}{include file="common/price.tpl" value=$oi.original_price-($oi.tax_value/$oi.amount)}{else}&nbsp;-&nbsp;{/if}</td>
                {/if}

                <td style="padding: 5px 10px; background-color: #ffffff; text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;"><b>{if $oi.extra.exclude_from_calculate}{__("free")}{else}{include file="common/price.tpl" value=$oi.display_subtotal-$oi.tax_value}{/if}</b>&nbsp;</td>
            </tr>
        {/if}
    {/foreach}
</table>

{hook name="orders:ordered_products"}
{/hook}
{* /Ordered products *}

{* Order totals *}
<table cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td align="right">
            <table border="0" style="padding: 3px 0px 12px 0px;">
                <tr>
                    <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;"><b>{__("subtotal")}:</b>&nbsp;</td>
                    <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;">{include file="common/price.tpl" value=$order_info.display_subtotal-($order_info|fn_w_get_subtotal_tax_from_order:'P')}</td>
                </tr>
                {if $order_info.discount|floatval}
                    <tr>
                        <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;"><b>{__("including_discount")}:</b>&nbsp;</td>
                        <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;">
                            {include file="common/price.tpl" value=$order_info.discount}</td>
                    </tr>
                {/if}


                {if $order_info.subtotal_discount|floatval}
                    <tr>
                        <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;">{__("order_discount")}:</td>
                        <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;">
                            {include file="common/price.tpl" value=$order_info.subtotal_discount}</td>
                    </tr>
                {/if}

                {if $order_info.shipping}
                    <tr>
                        <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;"><b>{__("shipping_cost")}:</b>&nbsp;</td>
                        {assign var="shipping_tax" value=$order_info|fn_w_get_subtotal_tax_from_order:'S'}
                        <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;">{include file="common/price.tpl" value=$order_info.display_shipping_cost-$shipping_tax}</td>
                    </tr>
                {/if}

                {if $order_info.taxes}
                    <tr>
                        <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;"><b>{__("taxes")}:</b>&nbsp;</td>
                        <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;">&nbsp;</td>
                    </tr>
                    {foreach from=$order_info.taxes item=tax_data}
                        <tr>
                            <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;">{$tax_data.description}&nbsp;:&nbsp;</td>
                            <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;">{include file="common/price.tpl" value=$tax_data.tax_subtotal}</td>
                        </tr>
                    {/foreach}
                {/if}
                {if $order_info.tax_exempt == 'Y'}
                    <tr>
                        <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;"><b>{__("tax_exempt")}</b></td>
                        <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;">&nbsp;</td>
                    </tr>
                {/if}

                {if $order_info.payment_surcharge|floatval && !$take_surcharge_from_vendor}
                    <tr>
                        <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;">{$order_info.payment_method.surcharge_title|default:__("payment_surcharge")}:&nbsp;</td>
                        <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;"><b>{include file="common/price.tpl" value=$order_info.payment_surcharge}</b></td>
                    </tr>
                {/if}



                <tr>
                    <td colspan="2"><hr style="border: 0px solid #d5d5d5; border-top-width: 1px;" /></td>
                </tr>
                <tr>
                    <td style="text-align: right; white-space: nowrap; font: 15px Tahoma; text-align: right;">{__("total_cost")}:&nbsp;</td>
                    <td style="text-align: right; white-space: nowrap; font: 15px Tahoma; text-align: right;"><strong style="font: bold 17px Tahoma;">{include file="common/price.tpl" value=$order_info.total}</strong></td>
                </tr>
            </table>
        </td>
    </tr>
</table>

{* /Order totals *}

{if $order_info.notes}
    <table cellpadding="0" cellspacing="0" border="0" width="100%">
        <tr valign="top">
            <td style="font-size: 12px; font-family: Arial;"><strong>{__("comment")}:</strong></td>
        </tr>
        <tr valign="top">
            <td><div style="overflow-x: auto; clear: both; width: 510px; height: 100%; padding-bottom: 20px; overflow-y: hidden; font-size: 12px; font-family: Arial;">{$order_info.notes|nl2br}</div></td>
        </tr>
    </table>
{/if}
{/if}

</td>
</tr>
</table>
</td>
</tr>
</table>
