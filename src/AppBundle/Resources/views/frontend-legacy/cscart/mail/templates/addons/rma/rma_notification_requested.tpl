<p>{__('w_rma_request',['[order_id]' => $order_info.order_id])}</p>

<p>{__('w_rma_return_address')}
<div class="info-list">
    {assign var='rma_address' value=$order_info.company_id|fn_w_get_rma_address}
    <div>
        <span>{$rma_address.address}</span>
    </div>
    <div>
        <span>{$rma_address.zipcode} {$rma_address.city}
             {$rma_address.state|fn_get_state_name:$rma_address.country} </span>
    </div>
    <div>
        <span>{$rma_address.country|fn_get_country_name}</span>
    </div>
</div>
</p>

<p>
    {__('w_return_products')}<br /><br />

    <table cellpadding="0" cellspacing="1" border="0" width="100%" style="background-color: #dddddd;">
        <tr>
            <th style="background-color: #eeeeee; padding: 6px 10px; white-space: nowrap;">{__("sku")}</th>
            <th style="background-color: #eeeeee; padding: 6px 10px; white-space: nowrap;">{__("product")}</th>
            <th style="background-color: #eeeeee; padding: 6px 10px; white-space: nowrap;">{__("price")}</th>
            <th style="background-color: #eeeeee; padding: 6px 10px; white-space: nowrap;">{__("amount")}</th>
            <th style="background-color: #eeeeee; padding: 6px 10px; white-space: nowrap;">{__("reason")}</th>
        </tr>
        {if $return_info.items[$smarty.const.RETURN_PRODUCT_ACCEPTED]}
            {foreach from=$return_info.items[$smarty.const.RETURN_PRODUCT_ACCEPTED] item="ri" key="key"}
                <tr>
                    <td style="padding: 5px 10px; background-color: #ffffff;">{$order_info.products.$key.product_code|default:"&nbsp;"}</td>
                    <td style="padding: 5px 10px; background-color: #ffffff;">{$ri.product nofilter}
                        {if $ri.product_options}<div style="padding-top: 1px; padding-bottom: 2px;">{include file="common/options_info.tpl" product_options=$ri.product_options}</div>{/if}</td>
                    <td align="center" style="padding: 5px 10px; background-color: #ffffff;">{if !$ri.price}{__("free")}{else}{include file="common/price.tpl" value=$ri.price}{/if}</td>
                    <td align="center" style="padding: 5px 10px; background-color: #ffffff;">{$ri.amount}</td>
                    <td align="center" style="padding: 5px 10px; background-color: #ffffff;">
                        {assign var="reason_id" value=$ri.reason}
                        &nbsp;{$reasons.$reason_id.property}&nbsp;</td>
                </tr>
            {/foreach}
        {else}
            <tr>
                <td colspan="6" align="center" style="padding: 5px 10px; background-color: #ffffff;"><p style="margin: 2px 0px 3px 0px;"><b>{__("text_no_products_found")}</b></p></td>
            </tr>
        {/if}
    </table>
    {* /Declined products *}

</p>

{__('w_rma_post_request')}
