{if $order_info}
{assign var="order_header" value=__("w_rma")}
{assign var=rma_number value=$return_info|fn_get_returns_rma_number}
{if $rma_number}
    {assign var="doc_id_text" value="{__("w_rma")} #`$rma_number`"}
{/if}


<table cellpadding="0" cellspacing="0" border="0" width="100%" class="main-table" style="background-color: #f4f6f8; font-size: 12px; font-family: Arial;">
<tr>
<td align="center" style="width: 100%; height: 100%;">
<table cellpadding="0" cellspacing="0" border="0" style=" width: 602px; table-layout: fixed; margin: 24px 0 24px 0;">
<tr>
<td style="background-color: #ffffff; border: 1px solid #e6e6e6; margin: 0px auto 0px auto; padding: 0px 44px 0px 46px; text-align: left;">
<table cellpadding="0" cellspacing="0" border="0" width="100%" style="padding: 27px 0px 0px 0px; border-bottom: 1px solid #868686; margin-bottom: 8px;">
    <tr>
        <td align="left" style="padding-bottom: 3px;" valign="middle"></td>
        <td width="100%" valign="bottom" style="text-align: right;  font: bold 26px Arial; text-transform: uppercase;  margin: 0px;">{$order_header|default:__("invoice_title")}</td>
    </tr>
</table>

<table cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr valign="top">
            <td style="width: 50%; padding: 14px 0px 0px 2px; font-size: 12px; font-family: Arial;">
                <h2 style="font: bold 12px Arial; margin: 0px 0px 3px 0px;">{$order_info.product_groups[0].package_info.origination.name}</h2>
                {$order_info.product_groups[0].package_info.origination.address}<br />
                {$order_info.product_groups[0].package_info.origination.zipcode} {$order_info.product_groups[0].package_info.origination.city}<br />
                {$order_info.product_groups[0].package_info.origination.country|fn_get_country_name}
            </td>
            <td style="padding-top: 14px;">
                <h2 style="font: bold 17px Tahoma; margin: 0px;">{if $doc_id_text}{$doc_id_text} <br />{/if}{__("invoice")}&nbsp;#{$order_info.w_invoice_number}</h2>
                <table cellpadding="0" cellspacing="0" border="0">
                    <tr valign="top">
                        <td style="font-size: 12px; font-family: verdana, helvetica, arial, sans-serif; text-transform: uppercase; color: #000000; padding-right: 10px; white-space: nowrap;">{__("date")}:</td>
                        <td style="font-size: 12px; font-family: Arial;">{$return_info.timestamp|date_format:"`$settings.Appearance.date_format`, `$settings.Appearance.time_format`"}</td>
                    </tr>
                    <tr valign="top">
                        <td style="font-size: 12px; font-family: verdana, helvetica, arial, sans-serif; text-transform: uppercase; color: #000000; padding-right: 10px; white-space: nowrap;">{__("w_client_id")}</td>
                        <td style="font-size: 12px; font-family: Arial;">{$order_info.user_id}</td>
                    </tr>
                    {if $order_info.shipping}
                        <tr valign="top">
                            <td style="font-size: 12px; font-family: verdana, helvetica, arial, sans-serif; text-transform: uppercase; color: #000000; padding-right: 10px; white-space: nowrap;">{__("shipping_method")}:</td>
                            <td style="font-size: 12px; font-family: Arial;">
                                {foreach from=$order_info.shipping item="shipping" name="f_shipp"}
                                    {$shipping.shipping}{if !$smarty.foreach.f_shipp.last}, {/if}
                                {/foreach}
                            </td>
                        </tr>
                    {/if}
                </table>
            </td>
    </tr>
</table>

{assign var="profile_fields" value='wInvoice'|fn_get_profile_fields}
{if $profile_fields}
    <table cellpadding="0" cellspacing="0" border="0" width="100%" style="padding: 32px 0px 24px 0px;">
        <tr valign="top">
            {if $profile_fields.C}
                {assign var="profields_c" value=$profile_fields.C|fn_fields_from_multi_level:"field_name":"field_id"}
                <td width="33%" style="font-size: 12px; font-family: Arial;">
                    <h3 style="font: bold 17px Tahoma; padding: 0px 0px 3px 1px; margin: 0px;">{__("customer")}:</h3>
                    <p style="margin: 2px 0px 3px 0px;">{if $profields_c.firstname}{$order_info.firstname}&nbsp;{/if}{if $profields_c.lastname}{$order_info.lastname}{/if}</p>
                    {if $profields_c.email}<p style="margin: 2px 0px 3px 0px;"><a href="mailto:{$order_info.email|escape:url}">{$order_info.email}</a></p>{/if}
                    {if $profields_c.phone}<p style="margin: 2px 0px 3px 0px;"><span style="text-transform: uppercase;">{__("phone")}:</span>&nbsp;{$order_info.phone}</p>{/if}
                    {if $profields_c.fax && $order_info.fax}<p style="margin: 2px 0px 3px 0px;"><span style="text-transform: uppercase;">{__("fax")}:</span>&nbsp;{$order_info.fax}</p>{/if}
                    {if $profields_c.company && $order_info.company}<p style="margin: 2px 0px 3px 0px;"><span style="text-transform: uppercase;">{__("company")}:</span>&nbsp;{$order_info.company}</p>{/if}
                    {if $profields_c.url && $order_info.url}<p style="margin: 2px 0px 3px 0px;"><span style="text-transform: uppercase;">{__("url")}:</span>&nbsp;{$order_info.url}</p>{/if}
                    {include file="profiles/profiles_extra_fields.tpl" fields=$profile_fields.C}
                </td>
            {/if}
            {if $profile_fields.B}
                {assign var="profields_b" value=$profile_fields.B|fn_fields_from_multi_level:"field_name":"field_id"}
                <td width="34%" style="font-size: 12px; font-family: Arial; {if $profile_fields.S}padding-right: 10px;{/if} {if $profile_fields.C}padding-left: 10px;{/if}">
                    <h3 style="font: bold 17px Tahoma; padding: 0px 0px 3px 1px; margin: 0px;">{__("bill_to")}:</h3>
                    <p style="margin: 2px 0px 3px 0px;">
                        {foreach from=$profile_fields.B item=field name="fields"}
                        {assign var="value" value=$order_info|fn_get_profile_field_value:$field}
                        {if $value}
                        {if $field.w_new_line=='Y'}
                    </p><p style="margin: 2px 0px 3px 0px;">
                        {/if}
                        {$value}
                        {/if}
                        {/foreach}
                    </p>
                </td>
            {/if}
            {if $profile_fields.S}
                {assign var="profields_s" value=$profile_fields.S|fn_fields_from_multi_level:"field_name":"field_id"}
                <td width="33%" style="font-size: 12px; font-family: Arial;">
                    <h3 style="font: bold 17px Tahoma; padding: 0px 0px 3px 1px; margin: 0px;">{__("ship_to")}:</h3>
                    <p style="margin: 2px 0px 3px 0px;">
                        {foreach from=$profile_fields.S item=field name="fields"}
                        {assign var="value" value=$order_info|fn_get_profile_field_value:$field}
                        {if $value}
                        {if $field.w_new_line=='Y'}
                    </p><p style="margin: 2px 0px 3px 0px;">
                        {/if}
                        {$value}
                        {/if}
                        {/foreach}
                    </p>
                </td>
            {/if}
        </tr>
    </table>
{/if}
{* Customer info *}


{* Ordered products *}

<table width="100%" cellpadding="0" cellspacing="1" style="background-color: #dddddd;">
    <tr>
        <th width="70%" style="background-color: #eeeeee; padding: 6px 10px; white-space: nowrap; font-size: 12px; font-family: Arial;">{__("product")}</th>
        <th style="background-color: #eeeeee; padding: 6px 10px; white-space: nowrap; font-size: 12px; font-family: Arial;">{__("quantity")}</th>
        <th style="background-color: #eeeeee; padding: 6px 10px; white-space: nowrap; font-size: 12px; font-family: Arial;">{__("unit_price")}</th>
        {if $order_info.use_discount}
            <th style="background-color: #eeeeee; padding: 6px 10px; white-space: nowrap; font-size: 12px; font-family: Arial;">{__("discount")}</th>
        {/if}
        {if $order_info.taxes}
            <th style="background-color: #eeeeee; padding: 6px 10px; white-space: nowrap; font-size: 12px; font-family: Arial;">{__("w_ht_price")}</th>
        {/if}
        <th style="background-color: #eeeeee; padding: 6px 10px; white-space: nowrap; font-size: 12px; font-family: Arial;">{__("w_ht_subtotal")}</th>
    </tr>

    {foreach from=$total_info['products'] item="oi"}
        {if !$oi.extra.parent}
            <tr>
                <td style="padding: 5px 10px; background-color: #ffffff; font-size: 12px; font-family: Arial;">
                    {$oi.product|default:__("deleted_product") nofilter}
                    {if $oi.product_code}<p style="margin: 2px 0px 3px 0px;">{__("sku")}: {$oi.product_code}</p>{/if}
                    {if $oi.extra.w_green_tax != 0}<p style="margin: 2px 0px 3px 0px;">{__("w_green_tax")}: {include file="common/price.tpl" value=$oi.extra.w_green_tax}</p>{/if}

                    {if $product.returns_info}
                        {if !$return_statuses}{assign var="return_statuses" value=$smarty.const.STATUSES_RETURN|fn_get_simple_statuses}{/if}
                        <p><a class="cm-combination combination-link" id="sw_ret_{$key}"><i title="{__("expand_sublist_of_items")}" id="on_ret_{$key}" class="icon-right-dir dir-list"></i><i title="{__("collapse_sublist_of_items")}" id="off_ret_{$key}" class="icon-down-dir dir-list hidden"></i>{__("returns_info")}</a></p>
                        <div class="box hidden" id="ret_{$key}">
                            {foreach from=$product.returns_info item="amount" key="status" name="f_rinfo"}
                                <p><strong>{$return_statuses.$status|default:""}</strong>:&nbsp;{$amount} {__("items")}</p>
                            {/foreach}
                        </div>
                    {/if}

                    {if $oi.product_options}<br/>{include file="common/options_info.tpl" product_options=$oi.product_options}{/if}
                </td>
                <td style="padding: 5px 10px; background-color: #ffffff; text-align: center; font-size: 12px; font-family: Arial;">{$oi.amount}</td>
                <td style="padding: 5px 10px; background-color: #ffffff; text-align: right; font-size: 12px; font-family: Arial;">{if $oi.extra.exclude_from_calculate}{__("free")}{else}{include file="common/price.tpl" value=$oi.price}{/if}</td>
                {if $order_info.use_discount}
                    <td style="padding: 5px 10px; background-color: #ffffff; text-align: right; font-size: 12px; font-family: Arial;">{if $oi.extra.discount|floatval}{include file="common/price.tpl" value=$oi.extra.discount}{else}&nbsp;-&nbsp;{/if}</td>
                {/if}
                {if $order_info.taxes}
                    <td style="padding: 5px 10px; background-color: #ffffff; text-align: right; font-size: 12px; font-family: Arial;">{if $oi.tax_value}{include file="common/price.tpl" value=$oi.price_HT}{else}&nbsp;-&nbsp;{/if}</td>
                {/if}

                <td style="padding: 5px 10px; background-color: #ffffff; text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;"><b>{if $oi.extra.exclude_from_calculate}{__("free")}{else}{include file="common/price.tpl" value=$oi.subtotal_HT}{/if}</b>&nbsp;</td>
            </tr>
        {/if}
    {/foreach}
</table>

{hook name="orders:ordered_products"}
{/hook}
{* /Ordered products *}

{* Order totals *}
<table cellpadding="0" cellspacing="0" border="0" width="100%">
    <tr>
        <td align="right">
            <table border="0" style="padding: 3px 0px 12px 0px;">
                <tr>
                    <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;"><b>{__("subtotal")}:</b>&nbsp;</td>
                    <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;">{include file="common/price.tpl" value=$total_info.display_subtotal}</td>
                </tr>
                {if $total_info.discount|floatval}
                    <tr>
                        <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;"><b>{__("including_discount")}:</b>&nbsp;</td>
                        <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;">
                            {include file="common/price.tpl" value=$total_info.discount}</td>
                    </tr>
                {/if}


                {if $total_info.subtotal_discount|floatval}
                    <tr>
                        <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;">{__("order_discount")}:</td>
                        <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;">
                            {include file="common/price.tpl" value=$total_info.subtotal_discount}</td>
                    </tr>
                {/if}

                {if $total_info.coupons}
                    {foreach from=$total_info.coupons item="coupon" key="key"}
                        <tr>
                            <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;"><b>{__("coupon")}:</b>&nbsp;</td>
                            <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;">{$key}</td>
                        </tr>
                    {/foreach}
                {/if}

                {if $total_info.shipping}
                    {assign var="shipping_tax" value=$total_info|fn_w_get_subtotal_tax_from_order:'S'}
                    <tr>
                        <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;"><b>{__("shipping_cost")}:</b>&nbsp;</td>
                        <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;">{include file="common/price.tpl" value=$total_info.display_shipping_cost-$shipping_tax}</td>
                    </tr>
                {/if}

                {if $total_info.taxes}
                    <tr>
                        <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;"><b>{__("taxes")}:</b>&nbsp;</td>
                        <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;">&nbsp;</td>
                    </tr>
                    {foreach from=$total_info.taxes item=tax_data}
                        <tr>
                            <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;">{$tax_data.description}&nbsp;:&nbsp;</td>
                            <td style="text-align: right; white-space: nowrap; font-size: 12px; font-family: Arial;">{include file="common/price.tpl" value=$tax_data.tax_subtotal}</td>
                        </tr>
                    {/foreach}
                {/if}


                <tr>
                    <td colspan="2"><hr style="border: 0px solid #d5d5d5; border-top-width: 1px;" /></td>
                </tr>
                <tr>
                    <td style="text-align: right; white-space: nowrap; font: 15px Tahoma; text-align: right;">{__("total_cost")}:&nbsp;</td>
                    <td style="text-align: right; white-space: nowrap; font: 15px Tahoma; text-align: right;"><strong style="font: bold 17px Tahoma;">{include file="common/price.tpl" value=$total_info.total}</strong></td>
                </tr>
            </table>
        </td>
    </tr>
</table>

{* /Order totals *}
{/if}
{if !$total_info.taxes }
    <br/> {__('w_self_entrepreneur_taxes_information')}
{/if}
</td>
</tr>
</table>
</td>
</tr>
</table>

