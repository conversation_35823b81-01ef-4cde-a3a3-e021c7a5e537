{if $order_info.returned_products}
{if $order_info.products}<p></p>{/if}
    <table cellpadding="2" cellspacing="1" border="0" width="100%" bgcolor="#000000">
    <tr>
        <td width="10%" align="center" bgcolor="#dddddd"><b>{__("sku")}</b></td>
        <td width="60%" bgcolor="#dddddd"><b>{__("returned_product")}</b></td>
        <td width="10%" align="center" bgcolor="#dddddd"><b>{__("amount")}</b></td>
        <td width="10%" align="center" bgcolor="#dddddd"><b>{__("subtotal")}</b></td>
    </tr>    
    {foreach from=$order_info.returned_products item='oi'}
    <tr>
        <td bgcolor="#ffffff">{$oi.product_code|default:"&nbsp;"}</td>
        <td bgcolor="#ffffff">
            {$oi.product}
            {if $oi.extra.w_green_tax != 0}<p style="margin: 2px 0px 3px 0px;">{__("w_green_tax")}: {include file="common/price.tpl" value=$oi.extra.w_green_tax}</p>{/if}

            {if $product.returns_info}
                {if !$return_statuses}{assign var="return_statuses" value=$smarty.const.STATUSES_RETURN|fn_get_simple_statuses}{/if}
                <p><a class="cm-combination combination-link" id="sw_ret_{$key}"><i title="{__("expand_sublist_of_items")}" id="on_ret_{$key}" class="icon-right-dir dir-list"></i><i title="{__("collapse_sublist_of_items")}" id="off_ret_{$key}" class="icon-down-dir dir-list hidden"></i>{__("returns_info")}</a></p>
                <div class="box hidden" id="ret_{$key}">
                    {foreach from=$product.returns_info item="amount" key="status" name="f_rinfo"}
                        <p><strong>{$return_statuses.$status|default:""}</strong>:&nbsp;{$amount} {__("items")}</p>
                    {/foreach}
                </div>
            {/if}

            {if $oi.product_options}<div style="padding-top: 1px; padding-bottom: 2px;">{include file="common/options_info.tpl" product_options=$oi.product_options}</div>{/if}</td>
        <td bgcolor="#ffffff" align="center">{$oi.amount}</td>
        <td align="right" bgcolor="#ffffff"><b>{if $oi.extra.exclude_from_calculate}{__("free")}{else}{include file="common/price.tpl" value=$oi.subtotal}{/if}</b>&nbsp;</td>
    </tr>
    {/foreach}
    </table>
{/if}
