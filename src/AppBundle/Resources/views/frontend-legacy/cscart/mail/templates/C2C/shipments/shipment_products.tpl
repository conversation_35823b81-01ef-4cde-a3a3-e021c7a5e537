{include file="common/letter_header.tpl"}

{assign var="full_shipped" value=$order_info|fn_w_check_all_shipped_order_after_current_shippment}

{if $full_shipped && $order_info.shipment_ids|count == 0}
    {__("w_one_full_shippment",['[order_id]' => $order_info.order_id])}
{else}
    {__("w_partial_shippment",['[order_id]' => $order_info.order_id])}
{/if}

<p>{__('w_current_sending')}
    {include file="shipments/shipment_info.tpl" }
</p>

{if $order_info.shipment_ids|count > 0 && $full_shipped}
    {__("w_finalize_shippment")}
{/if}


{__('w_todo_shipment_late', ['[form_url]' => fn_url('?dispatch=pages.view&page_id=4','C')]) nofilter}

{include file="common/letter_footer.tpl"}
