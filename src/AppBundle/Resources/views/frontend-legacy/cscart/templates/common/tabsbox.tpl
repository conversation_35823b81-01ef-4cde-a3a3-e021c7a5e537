{if !$active_tab}
    {assign var="active_tab" value=$smarty.request.selected_section}
{/if}

{if $navigation.tabs}

{assign var="empty_tab_ids" value=$content|empty_tabs}
{assign var="_tabs" value=false}
{if $top_order_actions}{$top_order_actions nofilter}{/if}
{script src="js/tygh/tabs.js"}
<div role="tabpanel">
    <div class="tabs cm-j-tabs{if $track} cm-track{/if} {$additional_class}" role="tablist">
        <ul {if $tabs_section}id="tabs_{$tabs_section}"{/if} class="nav nav-tabs" role="tablist">
        {foreach from=$navigation.tabs item=tab key=key name=tabs}
            {if ((!$tabs_section && !$tab.section) || ($tabs_section == $tab.section)) && !$key|in_array:$empty_tab_ids}
            {if !$active_tab}
                {assign var="active_tab" value=$key}
            {/if}
            {assign var="_tabs" value=true}
                {if $tab['only_c2c']}
                {feature_flag feature="enable_c2c"}
                    <li role="presentation" id="{$key}" class="{if $tab.js}cm-js{elseif $tab.ajax}cm-js cm-ajax{/if}{if $key == $active_tab} active{/if}"><a{if $tab.href} href="{$tab.href|fn_url}"{/if} data-toggle="tab" role="tab">{$tab.title}</a></li>
                {/feature_flag}

                {else}
                    <li role="presentation" id="{$key}" class="{if $tab.js}cm-js{elseif $tab.ajax}cm-js cm-ajax{/if}{if $key == $active_tab} active{/if}"><a{if $tab.href} href="{$tab.href|fn_url}"{/if} data-toggle="tab" role="tab">{$tab.title}</a></li>
                {/if}
            {/if}
        {/foreach}
        </ul>
    </div>

    {if $_tabs}
    <div class="cm-tabs-content tabs-content tab-content" id="tabs_content">
        {$content nofilter}
    </div>
    {/if}
</div>

{if $onclick}
<script type="text/javascript">
//<![CDATA[
    var hndl = {$ldelim}
        'tabs_{$tabs_section}': {$onclick}
    {$rdelim}
//]]>
</script>
{/if}
{else}
    {$content nofilter}
{/if}
