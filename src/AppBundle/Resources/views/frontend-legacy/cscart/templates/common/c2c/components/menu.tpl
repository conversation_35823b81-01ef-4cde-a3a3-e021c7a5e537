{**
active_tab  : Id of the current tab
content     : Html content in tab
*}
{if $active_tab}
{$schema = [
    'w_c2c_products' => [
        'c2c_products_manage' => [
            'title' => 'w_c2c_products_manage',
            'href'  => 'c2c_products.manage',
            'extern'=> true
        ]
    ],
    'w_c2c_company' => [
        'c2c_company_update' => [
            'title' => 'w_c2c_company_update',
            'href'  => 'c2c_company.update',
            'extern'=> true
        ],
        'c2c_company_update_informations' => [
            'title' => 'w_c2c_company_update_informations',
            'href'  => 'c2c_company.update_informations',
            'extern'=> true
        ]
    ],
    'w_c2c_orders' => [
        'c2c_orders_manage' => [
            'title' => 'w_c2c_orders_manage',
            'href'  => 'c2c_orders.manage',
            'extern'=> true
        ],
        'w_c2c_accounting' => [
            'title' => 'w_c2c_accounting',
            'href'  => 'accounting.view',
            'extern'=> true
        ]
    ]
]}
{*Build the menu, and retrieve current section*}
<div class="row c2c-menu">
    <div class="col-sm-3 col-xs-12">
        <div class="menu-membre">
            <div id="dynamic-left-menu" class="univ-default">
                {foreach from=$schema item='section' key='section_id'}
                <h2 class="sidebox-parent-cat ">{__($section_id)}</h2>
                <ul class="sidebox-wrapped">
                    {foreach from=$section item='tab' key='tab_id'}
                    <li><a href="{"`$tab.href`?selected_section=`$tab_id`"|fn_url}">{__($tab.title)}</a></li>
                    {if $tab_id==$active_tab}
                    {$active_section = $section}
                    {/if}
                    {/foreach}
                </ul>
                {/foreach}
            </div>
        </div>
        <div class="achat-vente-back">
            <a href="{fn_url("orders.search?selected_section=orders")}">{__("w_goto_orders")}</a>
        </div>
    </div>
    <div class="col-sm-9 col-xs-12">
        {*Build Tabs*}
        {$navigation.tabs = []}
        {foreach from=$active_section item='tab' key='tab_id'}
        {if $active_section.$active_tab.href != $tab.href}
        {$navigation.tabs[$tab_id] = [title=>$tab.title|__, ajax=>true, href=>$tab.href]}
        {else}
        {$navigation.tabs[$tab_id] = [title=>$tab.title|__, js=>true]}
        {/if}
        {/foreach}
        {include file="common/tabsbox.tpl" track=false additional_class="no-margin"}
    </div>
</div>
{/if}
