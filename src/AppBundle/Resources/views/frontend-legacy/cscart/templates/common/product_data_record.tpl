{if $product.tracking == "O"}
    {assign var="out_of_stock_text" value=__("text_combination_out_of_stock")}
{else}
    {assign var="out_of_stock_text" value=__("text_out_of_stock")}
{/if}

{if ($product.price|floatval || $product.zero_price_action == "P" || $product.zero_price_action == "A" || (!$product.price|floatval && $product.zero_price_action == "R")) && !($settings.General.allow_anonymous_shopping == "hide_price_and_add_to_cart" && !$auth.user_id)}
    {assign var="show_price_values" value=true}
{else}
    {assign var="show_price_values" value=false}
{/if}

{assign var="cart_button_exists" value=false}
{assign var="show_qty" value=$show_qty|default:true}
{assign var="obj_id" value=$obj_id|default:$product.product_id}
{assign var="product_amount" value=$product.inventory_amount|default:$product.amount}
{assign var="is_ajax" value=true}

{capture name="form_open_`$obj_id`"}
{/capture}

{capture name="view_name_`$obj_id`"}
    {assign var="description" value=$product.short_description|strip_tags}
    {assign var="brand" value=$product.header_features|fn_product_get_brand}
    {assign var="view_name" value='<strong>'|cat:$product.product|cat:'</strong>'}
    {$view_name|truncate:190 nofilter}
{/capture}

{capture name="add_to_cart_`$obj_id`"}
{/capture}

{if $smarty.capture.cart_button_exists}
    {assign var="cart_button_exists" value=true}
{/if}

{********************** Old Price *****************}
{capture name="old_price_`$obj_id`"}
    {if $show_price_values && $show_old_price}
        <span class="cm-reload-{$obj_prefix}{$obj_id}" id="old_price_update_{$obj_prefix}{$obj_id}">
            {hook name="products:old_price"}
            {if $product.discount}
                <span class="list-price nowrap w-old-price" id="line_old_price_{$obj_prefix}{$obj_id}"><span class="strike">{include file="common/price.tpl" value=$product.original_price|default:$product.base_price span_id="old_price_`$obj_prefix``$obj_id`" class="list-price nowrap"}</span></span>
            {elseif $product.list_discount}
                <span class="list-price nowrap w-old-price" id="line_list_price_{$obj_prefix}{$obj_id}">{if $details_page}<span class="list-price-label">{__("list_price")}:</span> {/if}<span class="strike">{include file="common/price.tpl" value=$product.list_price span_id="list_price_`$obj_prefix``$obj_id`" class="list-price nowrap"}</span></span>
            {/if}
            {/hook}
            <!--old_price_update_{$obj_prefix}{$obj_id}--></span>
    {/if}
{/capture}

{********************** Price *********************}
{capture name="price_`$obj_id`"}
    <span class="cm-reload-{$obj_prefix}{$obj_id} price-update" id="price_update_{$obj_prefix}{$obj_id}">
        <input type="hidden" name="appearance[show_price_values]" value="{$show_price_values}" />
        <input type="hidden" name="appearance[show_price]" value="{$show_price}" />
        {if $show_price_values}
            {hook name="products:prices_block"}
            {if $product.price|floatval || $product.zero_price_action == "P" || ($hide_add_to_cart_button == "Y" && $product.zero_price_action == "A")}
                <span class="price{if !$product.price|floatval && !$product.zero_price_action} hidden{/if}" id="line_discounted_price_{$obj_prefix}{$obj_id}">{if $details_page}{/if}{include file="common/price.tpl" value=$product.price span_id="discounted_price_`$obj_prefix``$obj_id`" class="price-num"}</span>
            {elseif $product.zero_price_action == "A"}
                <span class="price-curency"><span>{__("enter_your_price")}: </span><input class="input-text-short" type="text" size="3" name="product_data[{$obj_id}][price]" value="" />{currency_sign}</span>
            {elseif $product.zero_price_action == "R"}
                {assign var="show_qty" value=false}
            {/if}
            {/hook}
        {/if}
        <!--price_update_{$obj_prefix}{$obj_id}--></span>
{/capture}
{********************** You Save ******************}
{capture name="list_discount_`$obj_id`"}
    {if $show_price_values && $show_list_discount && $details_page}
        <span class="cm-reload-{$obj_prefix}{$obj_id}" id="line_discount_update_{$obj_prefix}{$obj_id}">
            <input type="hidden" name="appearance[show_price_values]" value="{$show_price_values}" />
            <input type="hidden" name="appearance[show_list_discount]" value="{$show_list_discount}" />
            {if $product.discount}
                <span class="list-price save-price nowrap" id="line_discount_value_{$obj_prefix}{$obj_id}">{__("you_save")}: {include file="common/price.tpl" value=$product.discount span_id="discount_value_`$obj_prefix``$obj_id`" class="list-price nowrap"}&nbsp;(<span id="prc_discount_value_{$obj_prefix}{$obj_id}" class="list-price nowrap">{$product.discount_prc}</span>%)</span>
            {elseif $product.list_discount}
                <span class="list-price save-price nowrap" id="line_discount_value_{$obj_prefix}{$obj_id}"> {__("you_save")}: {include file="common/price.tpl" value=$product.list_discount span_id="discount_value_`$obj_prefix``$obj_id`" class="list-price nowrap"}&nbsp;(<span id="prc_discount_value_{$obj_prefix}{$obj_id}" class="list-price nowrap">{$product.list_discount_prc}</span>%)</span>
            {/if}
            <!--line_discount_update_{$obj_prefix}{$obj_id}--></span>
    {/if}
{/capture}


{capture name="form_close_`$obj_id`"}
{/capture}
{foreach from=$images key="object_id" item="image"}
    <div class="cm-reload-{$image.obj_id}" id="{$object_id}">
        {if $image.link}
        <a href="{$image.link}">
            <input type="hidden" value="{$image.link}" name="image[{$object_id}][link]" />
            {/if}
            <input type="hidden" value="{$image.obj_id},{$image.width},{$image.height},{$image.type}" name="image[{$object_id}][data]" />
            {include file="common/image.tpl" image_width=$image.width image_height=$image.height obj_id=$object_id images=$product.main_pair}
            {if $image.link}
        </a>
        {/if}
        <!--{$object_id}--></div>
{/foreach}

{capture name="qty_table"}
    <div class="buttons-container cm-reload-{$obj_prefix}{$obj_id}" id="buttons_container_{$obj_prefix}_{$obj_id}">
        {if $product.amount && $product.price}
            <table>
                <tr>
                    <td>{__("quantity")}</td>
                    <td>
                        {assign var="qty" value="qty_`$obj_id`"}
                        {$smarty.capture.$qty nofilter}
                    </td>
                    <td align="right">
                        {assign var="add_to_cart" value="add_to_cart_`$obj_id`"}
                        {$smarty.capture.$add_to_cart nofilter}
                    </td>
                </tr>
            </table>
        {elseif !$product.amount}
            <span class="qty-out-of-stock">{$out_of_stock_text}</span>
        {elseif !$product.price}
            <span class="qty-out-of-stock">{__("contact_us_for_price")}</span>
        {/if}
        <!--buttons_container_{$obj_prefix}_{$obj_id}--></div>
{/capture}

{hook name="products:product_data"}{/hook}
