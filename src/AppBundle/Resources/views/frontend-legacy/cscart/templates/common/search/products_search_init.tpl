{if $categories }
    {$filters = $categories}
{elseif $company_id}
    {$tag_id = RecordProduct::NAMESPACE_VENDOR|cat:$company_id}
{elseif $brand_name}
    {$filters = [RecordProduct::getBrandFacetName() => $brand_name]}
{/if}

<div id="search_engine_results_page" class="results">

    <div class="sort-container">
        <span id="stats"></span>

        <select id="nb_hits_per_page" class="">
            {for $step=0 to 2}
                {$nb_items = pow(2,$step)*12}
                <option value="{$nb_items}">{$nb_items} {__("per_page")}</option>
            {/for}
        </select>
    </div>

{if $geoloc}
    <div class="hidden search-result-block-title"><h1>{__('w_products_with_rmp')}</h1></div>
    <div id="hits-geo" class="results-products">
    </div>

    <div class="hidden search-result-block-title"><h1>{__('w_products_with_delivery')}</h1></div>
{/if}
    <div id="hits" class="results-products">
    </div>

    <ul id="pagination" class="w-pagination pagination-bottom"></ul>
</div>
<p class="no-items" id="search_engine_no_results" style="display:none;">{__("no_items_found")}</p>


<script type="text/javascript">
    Tygh.$(function () {
        Tygh.facet_search = new FacetSearch(
            Tygh.$,
            Tygh,
            {
                index_name: '{$config[SearchConfig::CFG_SEARCH_ENGINE][SearchConfig::INDEX_PRODUCTS]}',
                tag_filters: '{$tag_id}',
                filters: {$filters|json_encode nofilter},
                empty_value: '{RecordProduct::FEATURE_EMPTY_VALUE}',
                path_prefix: {[
                    'http' => $config.http_path,
                    'https' => $config.https_path
                ]|json_encode nofilter},
                {if $geoloc}
                geoloc: {
                    tag: '{RecordProduct::TAG_GEOLOC}',
                    localization: $('#geoloc_input'),
                    lat: $("#top-search-geoloc-input-latitude"),
                    lng: $("#top-search-geoloc-input-longitude"),
                    extraContent: $('#sw_content_geoloc')
                },
                {/if}
                {if $facets_enabled}
                    {$specials=[
                        RecordProduct::KEY_PRICE        => __('price'),
                        RecordProduct::KEY_VENDOR_TYPE  => __('w_company_type'),
                        RecordProduct::KEY_CONDITION    => __('w_product_condition'),
                        'category_1' => 'category_1',
                        'category_2' => 'category_2',
                        'category_3' => 'category_3',
                        'main_category' => 'main_category'
                    ] }
                    facets_enabled: true,
                    facets_special: {
                    {foreach $specials as $special => $title}
                        {$special} : {
                                target : "facet_{$special}",
                                title: {json_encode($title)}
                        }{if !$special@last},{/if}
                    {/foreach}
                    },
                    facets_id: {
                        main_category: 'main_category',
                        category_1: 'category_1',
                        category_2: 'category_2'
                    },
                    facets_persistent: {
                        disjunctive: ['{RecordProduct::KEY_CONDITION}', '{RecordProduct::KEY_VENDOR_TYPE}'],
                        numeric: ['{RecordProduct::KEY_PRICE}']
                    },
                {/if}
                pagination: {
                    first: '{__('w_first_page')}',
                    last : '{__('w_last_page')}',
                    prev : '<i class="text-arrow">&larr;</i> {__('prev')}',
                    next : '{__('next')} <i class="text-arrow">&rarr;</i>'
                },
                categories_label: '{__('all_categories')}'
            }
        );
    });

</script>
<!-- Hit template -->
<script type="text/template" id="hit-template">
    <div class="product-cell search-result-cell">
        {* HTML fragment removed: it's no longer on Algolia *}
    </div>
</script>
<!-- Stats template -->
<script type="text/template" id="stats-template">
    {__('w_products_search_stats', '{{ nbHits }}', '{{ processingTimeMS }}')}
</script>
