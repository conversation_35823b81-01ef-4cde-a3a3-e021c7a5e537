{capture name="account_block"}
    <a role="menuitem" class="dropdown-toggle" id="account-trigger" data-toggle="dropdown" href="{fn_url('profiles.update')}">{__('block_my_account')}</a>
    <div aria-labelledby="account-trigger" role="menu" class="dropdown-menu dropdown-menu-right">
        {nocache}
            {run_controller controller='AppBundle:Profile:compact'}
        {/nocache}
    </div>
{/capture}
{capture name="cart_block"}
    <a role="menuitem" class="dropdown-toggle" id="cart-trigger" data-toggle="dropdown" href="{path route='basket_view'}">{__('w_my_cart')}</a>
    <div aria-labelledby="cart-trigger" role="menu" class="dropdown-menu dropdown-menu-right" id="monpanier">
        {nocache}
            {run_controller controller='AppBundle:Basket:compact'}
        {/nocache}
    </div>
{/capture}

<header>
    <div class="header-top">
        <div class="container">
            <div class="row">
                <div class="col-xs-2">
                    <a href="{fn_url()}"><img class="logo-header" src="{asset name="images/logo.png"}"></a>
                </div>
                <div class="col-md-10 hidden-xs hidden-sm top-menu" >
                    <nav>
                        <ul>
                            <li class="dropdown">
                                <a role="menuitem" rel="nofollow" class="dropdown-toggle" id="account-trigger" data-toggle="dropdown" href="{fn_url('profiles.update')}">{__('block_my_account')}</a>
                                <div aria-labelledby="account-trigger" role="menu" class="dropdown-menu dropdown-menu-right">
                                    {nocache}
                                        {run_controller controller='AppBundle:Profile:compact'}
                                    {/nocache}
                                </div>
                            </li>
                            <li class="dropdown">{$smarty.capture.cart_block nofilter}</li>
                            {nocache}
                                <li><a class="btn-primary" href="{route_create_product}">{__('w_post_an_ad')}</a></li>
                            {/nocache}
                        </ul>
                    </nav>
                </div>


                <!-- Menu top right responsive -->

                <div class="col-xs-10 visible-xs visible-sm top-menu">
                    <div class="top-menu-responsive">
                        <nav>
                            <ul>
                                <li>
                                    {nocache}
                                        <a class="btn-primary" href="{route_create_product}">{__('sold')}</a>
                                    {/nocache}
                                </li>
                                <li>
                                    <button class="square js-toggle-menu"><img src="{asset name="images/menu_responsive.png"}"></button>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <div id="header-mini" class="floatable header-top" style="display:none;">
        <div class="container">
            <div class="row">
                <div class="col-xs-6 col-md-3">
                    <a href="{fn_url()}"><img class="logo-header" src="{asset name="images/logo.png"}"></a>
                </div>
                <div class="col-xs-6 col-md-9 top-menu-responsive top-menu">
                    <div class="hidden-xs inline-block">
                        <input type="text" class="search-form js-focus-search" size="30" placeholder="{__('w_top-search_placeholder_search-input')}" />
                    </div>
                    <nav>
                        <ul class="visible-lg-block">
                            <li class="dropdown">
                                <a role="menuitem" rel="nofollow" class="dropdown-toggle" id="account-trigger" data-toggle="dropdown" href="{fn_url('profiles.update')}">{__('block_my_account')}</a>
                                <div aria-labelledby="account-trigger" role="menu" class="dropdown-menu dropdown-menu-right">
                                    {nocache}
                                        {run_controller controller='AppBundle:Profile:compact'}
                                    {/nocache}
                                </div>
                            </li>
                            <li class="dropdown">{$smarty.capture.cart_block nofilter}</li>
                            {nocache}
                                <li><a class="btn-primary" href="{route_create_product}">{__('w_post_an_ad')}</a></li>
                            {/nocache}
                        </ul>
                        <ul class="hidden-lg">
                            <li>
                                <button class="square visible-xs hidden-sm js-focus-search btn"><img src="{asset name="images/search_icon.png"}"></button>
                            </li>
                            <li>
                                <button class="square js-toggle-menu"><img src="{asset name="images/menu_responsive.png"}"></button>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>


    <div class="header-bottom-search">
        <div class="filtre-header-bottom">
            <div class="container">
                {block name="punch_line"}
                {/block}
                <!-- Search Form -->
                <div class="row">
                    <div class="col-md-10 col-md-offset-1">
                        <form action="{fn_url()}">
                            <div class="search-form-home">
                                <div class="row">
                                    {nocache}
                                        <div class="col-lg-5 col-ms-5 col-sm-5 col-xs-12">
                                            <button class="square hidden-sm hidden-md hidden-lg btn pull-right" type="submit"><img src="{asset name="images/search_icon.png"}"></button>
                                            <input id="search_input" type="text" name="q" value="{$search.q}" placeholder="{__('w_top-search_placeholder_search-input')}" tabindex="1" autocomplete="off" />
                                        </div>
                                        <div class="responsive-search-hide">
                                            <div class="col-lg-3 col-ms-3 col-sm-3 col-xs-6">
                                                {run_controller controller="AppBundle:Category:dropdown"}
                                            </div>
                                            <div class="col-lg-2 col-ms-2 col-sm-2 col-xs-3">
                                                <input type="text" name="geoloc" value="{json_value json=$smarty.request.geoloc key='localization'}" id="geoloc_input" class="js-geoloc-input" placeholder="{__('w_top-search_placeholder_geoloc-input')}" tabindex="2" autocomplete="off" />
                                                <div class="hidden" id="top-search-geoloc-suggestion-template">
                                                    <div class="top-search-suggestion-geocoding">{literal}{{ ville }} <span class="code-postal">{{ code_postal }}</span>{/literal}</div>
                                                </div>
                                                <input type="hidden" name="lat" id="js-geoloc-input-latitude"  value="{$smarty.request.lat}" />
                                                <input type="hidden" name="lng" id="js-geoloc-input-longitude" value="{$smarty.request.lng}" />
                                            </div>
                                            <div class="col-lg-2 col-ms-2 col-sm-2 col-xs-3"><button type="submit">{__('w_search_button')}</button></div>
                                        </div>
                                    {/nocache}
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>

<script type="text/javascript">
    var W = {
        truncate: function truncate (string, length) {
            if (string.length > length) {
                string = string.substring(0, length-1)+'…';
            }

            return string;
        }
    };

    {file_get_contents($config.dir.root|cat:'/design/frontend/common/js/views/categories/dropdown.js') nofilter}
    {file_get_contents($config.dir.root|cat:'/design/frontend/common/js/bootstrap.min.js') nofilter}

    $(function(){
        var topSearchInput  = $("#search_input");
        var topSearchGeoLoc = $(".js-geoloc-input");

        initGeoCoding(
                Tygh.searchClient.initIndex('{$config[SearchConfig::CFG_SEARCH_ENGINE][SearchConfig::INDEX_GEOCODING]}'),
                topSearchGeoLoc,
                $("#js-geoloc-input-latitude"),
                $("#js-geoloc-input-longitude"),
                Hogan.compile($("#top-search-geoloc-suggestion-template").html())
        );

        {* Do not transmit enter key event to avoid accidental submission *}
        topSearchGeoLoc.on('keydown', function(e){
            if(e.keyCode == 13) { {* enter pressed *}
                e.preventDefault();
                e.stopPropagation();
                topSearchGeoLoc.trigger('wizacha:changed');
            }
        });

        topSearchGeoLoc.on('wizacha:changed', function(){
            {* Emulate event 'change' on main input *}
            var ttInstance = topSearchInput.data('ttTypeahead');
            if (undefined !== ttInstance) {
                ttInstance._onQueryChanged(null, topSearchInput.prop('value'));
            } else {
                topSearchInput.trigger('keyup');
            }
            {* Then give focus back to main input *}
            topSearchInput.focus();
        });

        $('.js-switch').click(function(e){
            e.stopPropagation();
            $($(this).attr('data-target-show')).slideDown(200);
            $($(this).attr('data-target-hide')).slideUp(200);
        });
    });
</script>
