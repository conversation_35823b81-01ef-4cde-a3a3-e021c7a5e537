{if $code}
    {if $display_form}
        {assign var='remain_try' value=(C2CCode::MAX_LOGGED_ATTEMPT-count($order.code->logs()))}
        <form action="{''|fn_url}" method="post">
            <input type="hidden" name="order_id" value="{$order.order_id}" />
    {else}
        {assign var='remain_try' value=0}
    {/if}
        <table class="table" style="width:50%; margin-top:5px;">
            <tr>
                <td style="width:33%;" class="middle center">{include file="common/code.tpl" code=$code.0 remain=$remain_try}</td>
                <td style="width:33%;" class="middle center">{include file="common/code.tpl" code=$code.1 remain=$remain_try}</td>
                <td style="width:33%;" class="middle center">{include file="common/code.tpl" code=$code.2 remain=$remain_try}</td>
            </tr>
            <tr>
                <td class="middle center">{include file="common/code.tpl" code=$code.3 remain=$remain_try}</td>
                <td class="middle center">{include file="common/code.tpl" code=$code.4 remain=$remain_try}</td>
                <td class="middle center">{include file="common/code.tpl" code=$code.5 remain=$remain_try}</td>
            </tr>
            <tr>
                <td class="middle center">{include file="common/code.tpl" code=$code.6 remain=$remain_try}</td>
                <td class="middle center">{include file="common/code.tpl" code=$code.7 remain=$remain_try}</td>
                <td class="middle center">{include file="common/code.tpl" code=$code.8 remain=$remain_try}</td>
            </tr>
        </table><br/>
    {if $display_form}
        {if $remain_try > 0}
            {include file="buttons/button.tpl" but_text=__('w_validate_code') but_name="dispatch[c2c_orders.update_code]"}
        {else}
            {__('w_c2c_code_max_try_reach', ['[url]' => fn_url('contact.html')])}
        {/if}
    </form>
    {/if}
{/if}
