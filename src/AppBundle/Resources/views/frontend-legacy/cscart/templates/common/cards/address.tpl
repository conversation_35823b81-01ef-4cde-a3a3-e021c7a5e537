<div class="card row">
    {if !$hide_title}
        <div class="card-title">{$card_title}</div>
    {/if}
    <div class="card-button col-md-10 col-xs-8 pull-left">
        <ul>
            {assign var="card_fields" value='wCard'|fn_get_profile_fields}
            {foreach from=$card_fields.$section item="field"}
                {assign var="value" value=$cart.user_data|fn_get_profile_field_value:$field}
                {if $value}
                    <li class="{$field.field_name|replace:"_":"-"}">{$value}</li>
                {/if}
            {/foreach}
        </ul>
    </div>
    {if !$hide_button}
        <div class="card-button col-md-2 col-xs-4 pull-right text-right">
            <a class="btn js-open-modal"
               data-source-id="{$card_id}"
               data-modal-class="modal-small"
                    >{__("edit")}</a>
        </div>
    {/if}
</div>
<div id="{$card_id}" class="hidden">
    <div class="modal-header">
        <h2>{$card_title}</h2>
    </div>
    <div class="modal-body">
        <form action="{""|fn_url}" method="post" class="form-horizontal">
            <input type="hidden" name="result_ids" value="{$reload_id}">
            <input type="hidden" name="update_step" value="step_two"/>
            <input type="hidden" name="ship_to_another" value="{if $ship_to_another}1{else}0{/if}"/>
            {include file="views/profiles/components/profile_fields.tpl" section=$section body_id="" ship_to_another=true hide_title=true}
            <hr/>
            {include file="buttons/button.tpl" but_role="w-action" but_meta="btn-primary" but_name="dispatch[checkout.update_steps]" but_color="orange" but_size="medium" but_text="{__('approve')}"}
        </form>
    </div>
</div>