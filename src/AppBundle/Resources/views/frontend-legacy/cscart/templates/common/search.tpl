<div id="search-block">
<form action="{""|fn_url}" name="search_form" method="get">

{hook name="search:additional_fields"}{/hook}

{strip}
    {if $settings.General.search_objects}
        {assign var="search_title" value=__("search")}
    {else}
        {assign var="search_title" value=__("search_products")}
    {/if}
    <div class="labels">
        <div>{__('w_top-search_title_search-input')}</div>
        <div>{__('w_top-search_title_geoloc-input')}</div>
    </div>
    <div id="categories-menu">
        <div class="main-search"><span>{__('all_categories')}</span><i class="icon-down-dir"></i></div>
        <div id="categories-menu-content"></div>
        <input type="hidden" id="refinements" name="refinements" value="" />
    </div>
    <script type="text/template" id="categories-menu-ul-template">
        {literal}{{=[[ ]]=}}{/literal}
        <ul>
            <li id="all-categories-link" class="lvl1 current"><span>{__('all_categories')}</span></li>
            [[#categories]]
                [[> content ]]
            [[/categories]]
        </ul>
        <a class="w-button grey small float-right">{__('w_validate')}</a>
    </script>
    <script type="text/template" id="categories-menu-li-template">
        {literal}{{=[[ ]]=}}{/literal}
        <li class="lvl1" data-field="category_1" data-value="[[ name ]]">
            <span>[[ name ]]</span>
                <ul class="hidden">
                    [[#children]]
                    <li class="lvl2" data-field="category_2" data-value="[[ name ]]" data-preserve="category_1">
                        <span>[[ name ]]</span>
                    </li>
                    [[/children]]
                </ul>
        </li>
    </script>

    <div class="multi-search">
        <div class="first-field">
            <i class="icon-search"></i><input type="text" name="q" value="{$search.q}" id="search_input" placeholder="{__('w_top-search_placeholder_search-input')}" class="search-input" tabindex="1" autocomplete="off"/>
        </div>
        <div class="second-field">
            <i class="icon-location"></i><input type="text" name="geoloc" value="{json_value json=$smarty.request.geoloc key='localization'}" id="geoloc_input" placeholder="{__('w_top-search_placeholder_geoloc-input')}" class="geoloc-input" tabindex="2" autocomplete="off"/>
        </div>
    </div>
    <div class="hidden" id="top-search-geoloc-suggestion-template">
        <div class="top-search-suggestion-geocoding">{literal}{{ ville }} <span class="code-postal">{{ code_postal }}</span>{/literal}</div>
    </div>
    <input type="hidden" name="lat" id="top-search-geoloc-input-latitude"  value="{$smarty.request.lat}" />
    <input type="hidden" name="lng" id="top-search-geoloc-input-longitude" value="{$smarty.request.lng}" />
    <script type="text/javascript">
        /* Set category label */
        function setCategoryLabel($categoriesMenu) {
            var max_length = 21;
            var selected_category = $categoriesMenu.find('li.current > span').last().html();
            if(selected_category.length > max_length) {
                selected_category = selected_category.substr(0, max_length);
                if(selected_category.length == max_length) {
                    selected_category = selected_category.substr(0, max_length-3) + '...';
                }
            }
            if(selected_category.length) {
                $categoriesMenu.find('.main-search > span').html(selected_category);
            }
        }

        $(function() {
            var topSearchInput  = $("#search_input");
            var topSearchGeoLoc = $("#geoloc_input");

            initGeoCoding(
                    Tygh.searchClient.initIndex('{$config[SearchConfig::CFG_SEARCH_ENGINE][SearchConfig::INDEX_GEOCODING]}'),
                    topSearchGeoLoc,
                    $("#top-search-geoloc-input-latitude"),
                    $("#top-search-geoloc-input-longitude"),
                    Hogan.compile($("#top-search-geoloc-suggestion-template").html())
            );

            {* Do not transmit enter key event to avoid accidental submission *}
            topSearchGeoLoc.on('keydown', function(e){
                        if(e.keyCode == 13) { {* enter pressed *}
                            e.preventDefault();
                            e.stopPropagation();
                            topSearchGeoLoc.trigger('wizacha:changed');
                        }
                    });

            topSearchGeoLoc.on('wizacha:changed', function(){
                {* Emulate event 'change' on main input *}
                var ttInstance = topSearchInput.data('ttTypeahead');
                if (undefined !== ttInstance) {
                    ttInstance._onQueryChanged(null, topSearchInput.prop('value'));
                } else {
                    topSearchInput.trigger('keyup');
                }
                {* Then give focus back to main input *}
                topSearchInput.focus();
            });


            var $categoriesMenu = $('#categories-menu');
            var $categoriesMenuContent = $('#categories-menu-content');
            var $categoriesMenuUlTemplate = Hogan.compile($('#categories-menu-ul-template').text());
            var $categoriesMenuLiTemplate = Hogan.compile($('#categories-menu-li-template').text());

            var categories = [];
            for (var i in Tygh.categories_full_arbo) {
                var subcategories = [];
                for (var j in Tygh.categories_full_arbo[i]['children']) {
                    subcategories.push({
                        name:Tygh.categories_full_arbo[i]['children'][j]['name']
                    });
                };
                categories.push({
                    name:Tygh.categories_full_arbo[i]['name'],
                    children: subcategories
                });
            }
            $categoriesMenuContent.html(

                $categoriesMenuUlTemplate.render(
                    {
                        categories: categories
                    },
                    {
                        content: $categoriesMenuLiTemplate
                    }
                )
            );
            $categoriesMenu.find('.main-search, a').click(function(event){
                event.preventDefault();
                $categoriesMenuContent.slideToggle(200);
            });
            $categoriesMenu.hover(
                function () {
                    $(this).attr('data-hover', 'true');
                },
                function () {
                    $(this).attr('data-hover', 'false');
                }
            );
            $('html').on('click', function(){
               if ($categoriesMenu.attr('data-hover') != 'true') {
                   $categoriesMenuContent.slideUp(200);
               }
            });

            $categoriesMenu.find('li > span').on('click', function(){

                var root = $(this).closest('ul');
                var current = $(this).parent();
                var child = current.find('ul');

                /* Accordion */
                current.addClass('triggered');
                root.find('li:not(.triggered)').removeClass('current').find('ul').slideUp(200).find('li');
                if(!child.is(':visible')) {
                    child.slideDown(200);
                } else {
                    child.slideUp(200);
                }
                current.removeClass('triggered').toggleClass('current');
                var $selected_categories = $categoriesMenu.find('li.current');
                if($selected_categories.length) {
                    var categories = { };
                    $selected_categories.each(function(i,elt) {
                        categories[$(elt).data('field')] = $(elt).data('value');
                    });
                    $('#refinements').val(JSON.stringify([categories]));
                } else {
                    $('#all-categories-link').addClass('current');
                    $('#refinements').val('');
                }

                setCategoryLabel($categoriesMenu);
            });

        });
    </script>
    {include file="buttons/main_search.tpl"}
{/strip}

{capture name="search_input_id"}{math equation="x + y" x=$smarty.capture.search_input_id|default:1 y=1 assign="search_input_id"}{$search_input_id}{/capture}
</form>
</div>
