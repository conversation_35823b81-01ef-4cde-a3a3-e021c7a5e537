{if $but_role == "action"}
    {assign var="suffix" value="-action"}
    {assign var="file_prefix" value="action_"}
{elseif $but_role == "act"}
    {assign var="suffix" value="-act"}
    {assign var="file_prefix" value="action_"}
{elseif $but_role == "disabled_big"}
    {assign var="suffix" value="-disabled-big"}
{elseif $but_role == "big"}
    {assign var="suffix" value="-big"}
{elseif $but_role == "delete"}
    {assign var="suffix" value="-delete"}
{elseif $but_role == "tool"}
    {assign var="suffix" value="-tool"}
{else}
    {assign var="suffix" value=""}
{/if}

{if $but_name && $but_role != "text" && $but_role != "act" && $but_role != "delete"} {* SUBMIT BUTTON *}
    <input {if $but_id}id="{$but_id}"{/if} class="btn {$but_color} {$but_size} {$but_meta} {$but_class}" type="submit"
           name="{$but_name}" {if $but_onclick}onclick="{$but_onclick}"{/if} value="{$but_text}"/>
{elseif $but_role == "text" || $but_role == "act" || $but_role == "edit"} {* TEXT STYLE *}
    <a {$but_extra}
            class="{if $but_meta}{$but_meta} {/if}{if $but_name}cm-submit {/if}text-button{$suffix}"{if $but_id} id="{$but_id}"{/if}{if $but_name} data-ca-dispatch="{$but_name}"{/if}{if $but_href} href="{$but_href|fn_url}"{/if}{if $but_onclick} onclick="{$but_onclick} return false;"{/if}{if $but_target} target="{$but_target}"{/if}{if $but_rel} rel="{$but_rel}"{/if}{if $but_external_click_id} data-ca-external-click-id="{$but_external_click_id}"{/if}{if $but_target_form} data-ca-target-form="{$but_target_form}"{/if}{if $but_target_id} data-ca-target-id="{$but_target_id}"{/if}>{$but_text}</a>
{elseif $but_role == "delete"}
    <a {$but_extra} {if $but_id}id="{$but_id}"{/if}{if $but_name} data-ca-dispatch="{$but_name}"{/if} {if $but_href}href="{$but_href|fn_url}"{/if}{if $but_onclick} onclick="{$but_onclick} return false;"{/if}{if $but_meta} class="{$but_meta}"{/if}{if $but_target} target="{$but_target}"{/if}{if $but_rel} rel="{$but_rel}"{/if}{if $but_external_click_id} data-ca-external-click-id="{$but_external_click_id}"{/if}{if $but_target_form} data-ca-target-form="{$but_target_form}"{/if}{if $but_target_id} data-ca-target-id="{$but_target_id}"{/if}><i
                title="{__("delete")}" class="icon-cancel-circle"></i></a>
{elseif $but_role == "trash"}
    <a {$but_extra} {if $but_id}id="{$but_id}"{/if}{if $but_name} data-ca-dispatch="{$but_name}"{/if} {if $but_href}href="{$but_href|fn_url}"{/if}{if $but_onclick} onclick="{$but_onclick} return false;"{/if}{if $but_meta} class="{$but_meta}"{/if}{if $but_target} target="{$but_target}"{/if}{if $but_rel} rel="{$but_rel}"{/if}{if $but_external_click_id} data-ca-external-click-id="{$but_external_click_id}"{/if}{if $but_target_form} data-ca-target-form="{$but_target_form}"{/if}{if $but_target_id} data-ca-target-id="{$but_target_id}"{/if}><i
                title="{__("delete")}" class="icon-trashcan"></i></a>
{elseif $but_role == "icon"} {* LINK WITH ICON *}
    <a {$but_extra} {if $but_id}id="{$but_id}"{/if}{if $but_href} href="{$but_href|fn_url}"{/if} {if $but_onclick}onclick="{$but_onclick};{if !$allow_href} return false;{/if}"{/if} {if $but_target}target="{$but_target}"{/if} {if $but_rel} rel="{$but_rel}"{/if}{if $but_external_click_id} data-ca-external-click-id="{$but_external_click_id}"{/if}{if $but_target_form} data-ca-target-form="{$but_target_form}"{/if}{if $but_target_id} data-ca-target-id="{$but_target_id}"{/if}
            class="{if $but_meta}{$but_meta}{/if}">{$but_text nofilter}</a>
{else} {* Wizacha Default *}
    {if !$but_color}{$but_color = "btn-primary"}{/if}
    <a {if $but_id}id="{$but_id}"{/if} {if $but_href}href="{$but_href|fn_url}"{/if} rel="nofollow"
       class="btn {$but_color} {$but_size} {$but_class} {$but_meta}"
        {if $but_external_click_id} data-ca-external-click-id="{$but_external_click_id}"{/if}
        {if $but_target_form} data-ca-target-form="{$but_target_form}"{/if}
        {if $but_target_id} data-ca-target-id="{$but_target_id}"{/if}
        {if $data_ca_external_click_id}data-ca-external-click-id="{$data_ca_external_click_id}"{/if}
        >{$but_text}</a>
{/if}
