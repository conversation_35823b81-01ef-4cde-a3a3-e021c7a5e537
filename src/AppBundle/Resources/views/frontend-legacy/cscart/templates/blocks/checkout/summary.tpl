<div class="checkout-summary" id="checkout_info_summary_{$block.snapping_id}">
    <table>
        <tbody class="tbody">
        <tr>
            <td>{__("w_purchases")}</td>
            <td class="right" data-ct-checkout-summary="items">
                <span>{include file="common/price.tpl" value=$cart.display_subtotal}</span>
            </td>
        </tr>

        {if (!$cart.shipping_failed && $cart.chosen_shipping && $cart.shipping_required) || $cart.need_shipping}
            <tr>
                <td>{__("shipping_charges")}</td>
                <td class="right" data-ct-checkout-summary="shipping">
                    {if !$cart.display_shipping_cost}
                        <span>{__("free_shipping")}</span>
                    {else}
                        <span>{include file="common/price.tpl" value=$cart.display_shipping_cost}</span>
                    {/if}
                </td>
            </tr>
        {/if}

        {if ($cart.subtotal_discount|floatval)}
            <tr>
                <td>{__("order_discount")}</td>
                <td class="right discount-price" data-ct-checkout-summary="order-discount">
                    <span>-{include file="common/price.tpl" value=$cart.subtotal_discount}</span>
                </td>
            </tr>
        {/if}
        <tr>
            <td>
                {__("order_total")}
            </td>
            <td class="right"><span class="bold">{include file="common/price.tpl" value=$_total|default:$cart.total}</span></td>
        </tr>
        <tr>
            <td class="smalltext right" colspan="2">
                {if $cart.taxes}
                    {$cart.tax_subtotal = 0}
                    {foreach from=$cart.taxes item="tax"}
                        {$cart.tax_subtotal = $cart.tax_subtotal+$tax.tax_subtotal}
                    {/foreach}
                {/if}
                {include file="common/price.tpl" value=$cart.tax_subtotal assign="formatted_tax"}
                {__('w_TVA_amount', ['[amount]' => $formatted_tax])}
            </td>
        </tr>
        </tbody>
    </table>
    <!--checkout_info_summary_{$block.snapping_id}--></div>
