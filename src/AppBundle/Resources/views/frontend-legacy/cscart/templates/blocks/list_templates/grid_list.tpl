{if $products}

    {script src="js/tygh/exceptions.js"}

    {if !$no_pagination}
        {include file="common/pagination.tpl"}
    {/if}

    {if !$no_sorting}
        {include file="views/products/components/sorting.tpl" hide_layouts=true}
    {/if}

    {if !$show_empty}
        {split data=$products size=$columns|default:"2" assign="splitted_products"}
    {else}
        {split data=$products size=$columns|default:"2" assign="splitted_products" skip_complete=true}
    {/if}
    {math equation="100 / x" x=$columns|default:"2" assign="cell_width"}
    {if $item_number == "Y"}
        {assign var="cur_number" value=1}
    {/if}

    {script src="js/tygh/product_image_gallery.js"}
    {*FORCE DISABLE QUICK VIEW*}
    {$settings.Appearance.enable_quick_view = 'N'}

    {if $settings.Appearance.enable_quick_view == 'Y'}
        {$quick_nav_ids = $products|fn_fields_from_multi_level:"product_id":"product_id"}
    {/if}
    <table class="fixed-layout multicolumns-list table-width">
        {foreach from=$splitted_products item="sproducts" name="sprod"}
            <tr{if !$smarty.foreach.sprod.last} class="row-border"{/if}>
                {foreach from=$sproducts item="product" name="sproducts"}
                    <td class="product-spacer">&nbsp;</td>
                    <td class="product-cell valign-top" style="width: {$cell_width}%">
                        {if $product}
                            {include file="common/product_data.tpl" product=$product}
                            {include file='blocks/products/product_in_grid.tpl'}
                        {/if}
                    </td>
                    <td class="product-spacer">&nbsp;</td>
                {/foreach}
                {if $show_empty && $smarty.foreach.sprod.last}
                    {assign var="iteration" value=$smarty.foreach.sproducts.iteration}
                    {capture name="iteration"}{$iteration}{/capture}
                    {hook name="products:products_multicolumns_extra"}
                    {/hook}
                    {assign var="iteration" value=$smarty.capture.iteration}
                    {if $iteration % $columns != 0}
                        {math assign="empty_count" equation="c - it%c" it=$iteration c=$columns}
                        {section loop=$empty_count name="empty_rows"}
                            <td class="product-spacer">&nbsp;</td>
                            <td class="product-cell product-cell-empty valign-top" style="width: {$cell_width}%">
                                <div>
                                    <p>{__("empty")}</p>
                                </div>
                            </td>
                            <td class="product-spacer">&nbsp;</td>
                        {/section}
                    {/if}
                {/if}
            </tr>
        {/foreach}
    </table>
    {if !$no_pagination}
        {include file="common/pagination.tpl"}
    {/if}

{/if}

{capture name="mainbox_title"}{$title}{/capture}