<script type="text/javascript">
    var current_distance = 0;
    function distanceClicked(elmt, dist) {
        $('#distance_list .filter-item').removeClass('checked');
        if( current_distance != dist) {
            elmt.addClass('checked')
            current_distance = dist;
        } else {
            current_distance = 0;
        }
        setGeoDistance(current_distance);
    }

    var current_delivery = true;
    function deliveryClicked(elmt) {
        current_delivery = ! current_delivery;
        $(elmt).toggleClass('checked', current_delivery);
        setGeoDelivery(current_delivery);
    }

    $(function() {
        var $lat                = $("#top-search-geoloc-input-latitude");
        var $lng                = $("#top-search-geoloc-input-longitude");
        var $extraContentGeoLoc = $('#sw_content_geoloc');
        var $topSearchGeoLoc    = $("#geoloc_input");

        $topSearchGeoLoc.on('wizacha:changed', function(e){
            setGeoLatLng($lat.val(), $lng.val());
            $extraContentGeoLoc.toggleClass('hidden', !$lat.val() || !$lng.val());
        });
    })
</script>

<div id="sw_content_main_category" class="filter-wrap open cm-combination-filter_main_category hidden">
    <i class="icon-down-dir"></i><i class="icon-right-dir"></i>
    <span class="filter-title">{__('category')}</span><br/>
    <ul class="product-filters" id="content_main_category" data-preserve='[&#34;category_1&#34;, &#34;category_2&#34;, &#34;main_category&#34;]'></ul>
</div>

<div id="sw_content_geoloc" class="filter-wrap open cm-combination-filter_geoloc hidden">
    <i class="icon-down-dir"></i><i class="icon-right-dir"></i>
    <span class="filter-title">{__('w_localization')}</span>

    <div id="content_geoloc">
        <div id="content_distance">
            <b>{__('w_extend_search_distance')}</b>
            <ul id="distance_list" class="product-filters">
                <li>
                    <a id='distance15000' class="filter-item" rel="nofollow" onclick="distanceClicked($(this), 15000)">
                        <span class="filter-icon">
                            <i class="icon-ok"></i><i class="icon-cancel"></i>
                        </span>
                        15km
                    </a>
                </li>
                <li>
                    <a id='distance50000' class="filter-item" rel="nofollow" onclick="distanceClicked($(this), 50000)">
                        <span class="filter-icon">
                            <i class="icon-ok"></i><i class="icon-cancel"></i>
                        </span>
                        50km
                    </a>
                </li>
                <li>
                    <a id='distance100000' class="filter-item" rel="nofollow" onclick="distanceClicked($(this), 100000)">
                        <span class="filter-icon">
                            <i class="icon-ok"></i><i class="icon-cancel"></i>
                        </span>
                        100km
                    </a>
                </li>
            </ul>
        </div>

        <ul class="product-filters">
            <li>
            <a id='include_delivery' class="filter-item checked" rel="nofollow" onclick="deliveryClicked(this)">
                    <span class="filter-icon">
                        <i class="icon-ok"></i><i class="icon-cancel"></i>
                    </span>
                {__('w_include_products_with_delivery')}
            </a>
            </li>
        </ul>
    </div>
</div>

<div id="facets"></div>

<!-- Menu template -->
<script type="text/template" id="menu-template">
    {literal}{{=[[ ]]=}}{/literal}
    [[#facet_{RecordProduct::KEY_PRICE}]]
    <div id="sw_content_[[ facet_id ]]" class="filter-wrap open cm-combination-filter_[[ facet_id ]]">
        <i class="icon-down-dir"></i><i class="icon-right-dir"></i>
        <span class="filter-title">[[ title ]]</span>

        <div id="content_[[ facet_id ]]" class="price-slider">
            <span>
                {__("from")}
                <input type="text" class="input-text" id="price-min" placeholder="[[ stats.min ]]" />
                {currency_sign}
            </span>
            <span>
                {__("to")}
                <input type="text" class="input-text" id="price-max" placeholder="[[ stats.max ]]" />
                {currency_sign}
            </span>
        </div>
    </div>
    [[/facet_{RecordProduct::KEY_PRICE}]]

    [[#facet_{RecordProduct::KEY_CONDITION}]]
        [[> facet ]]
    [[/facet_{RecordProduct::KEY_CONDITION}]]

    [[#facets]]
        [[> facet ]]
    [[/facets]]

    [[#facet_{RecordProduct::KEY_VENDOR_TYPE}]]
        [[> facet ]]
    [[/facet_{RecordProduct::KEY_VENDOR_TYPE}]]
</script>
<!-- Facet template -->
<script type="text/template" id="facet-template">
    {literal}{{=[[ ]]=}}{/literal}
    <div id="sw_content_[[ facet_id ]]" class="filter-wrap[[#is_special]] open[[/is_special]][[^is_special]] not-special[[/is_special]] cm-combination-filter_[[ facet_id ]]">
        <i class="icon-down-dir"></i><i class="icon-right-dir"></i>
        <span class="filter-title">[[ title ]]</span>
        [[#stats]]
            <div id="content_[[ facet_id ]]" class="price-slider [[^is_special]]hidden[[/is_special]]">
                <span>
                    {__("from")}
                    <input type="text" class="input-text" id="[[ facet_id ]]-min" placeholder="[[ stats.min ]]" />
                </span>
                <span>
                    {__("to")}
                    <input type="text" class="input-text" id="[[ facet_id ]]-max" placeholder="[[ stats.max ]]" />
                </span>
            </div>
        [[/stats]]
        [[^stats]]
            <ul class="product-filters [[^is_special]]hidden[[/is_special]]" id="content_[[ facet_id ]]">
                [[> facet_values]]
            </ul>
        [[/stats]]
    </div>
</script>
<!-- Facet template -->
<script type="text/template" id="facet_values-template">
    {literal}{{=[[ ]]=}}{/literal}
    [[#values]]
    [[> value ]]
    [[/values]]

    [[#other_values.length]]
    <li class="show-more list-group-item"><a href="#" onclick="$(this).closest('ul').find('.show-more').toggle(); return false;"><i class="glyphicon glyphicon-chevron-down" />{__('more')}</a></li>
    <div class="show-more hidden">
        [[#other_values]]
        [[> value ]]
        [[/other_values]]
    </div>
    <li class="show-more list-group-item hidden" onclick="$(this).closest('ul').find('.show-more').toggle(); return false;"><a href=""><i class="glyphicon glyphicon-chevron-up" />{__('less')}</a></li>
    [[/other_values.length]]
</script>
<!-- Value template -->
<script type="text/template" id="value-template">
    {literal}{{=[[ ]]=}}{/literal}
    <li>
        <a href="" class="filter-item[[#refined]] checked[[/refined]]" rel="nofollow" onclick='[[#is_category]]Tygh.facet_search.categoryMultivalueChange(this); [[/is_category]]toggleRefine("[[ facet_name ]]", "[[ label ]]"); return false;'>
                <span class="filter-icon">
                    <i class="icon-ok"></i><i class="icon-cancel"></i>
                </span>
            [[#is_empty]]
                {{__('empty_facet_value')}}
            [[/is_empty]]
            [[^is_empty]]
                [[ label ]]
            [[/is_empty]]
        </a> ([[ count ]])
    </li>
</script>
