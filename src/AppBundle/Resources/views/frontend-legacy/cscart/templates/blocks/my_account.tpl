{** block-description:my_account **}
{capture name="title"}
    <i class="icon-user" style=""></i>
    <a href="{"profiles.update"|fn_url}">{__("my_account")}</a>
    <i class="icon-down-micro"></i>
{/capture}
<div id="account_info_{$block.snapping_id}">
    {assign var="return_current_url" value=$config.current_url|escape:url}
    {if $auth.user_id}
        {if empty($user_info)}
            {assign var="user_info" value=$user_info_ajax}
        {/if}
        <ul class="marged">
            {if $user_info.firstname || $user_info.lastname}
                <li class="user-name">{$user_info.firstname} {$user_info.lastname}</li>
            {else}
                {if $settings.General.use_email_as_login == 'Y'}
                    <li class="user-name">{$user_info.email}</li>
                {else}
                    <li class="user-name">{$user_info.user_login}</li>
                {/if}
            {/if}
            <li><a href="{"profiles.update"|fn_url}" rel="nofollow" class="underlined">{__("profile_details")}</a></li>
            <li><a href="{'discuss.list'|fn_url}" >
                    {__("discuss_menu")}
                    {$unread_messages = $discussClient->getMessageRepository()->getUnreadCount($auth.user_id)}
                    {if $unread_messages > 0}
                        <div class="visual-notification">{$unread_messages}</div>
                    {/if}
                </a></li>
            <li><a href="{'orders.search'|fn_url}" >{__("w_my_purchases")}</a></li>
            {if $auth.user_type == User::VENDOR_TYPE}
                <li><a href="{''|fn_url:User::VENDOR_TYPE}" target="_blank">{'w_my_vendor_account'|__}</a></li>
            {else}
                <li><a href="{'c2c_products.manage'|fn_url}">{'w_c2c_vendor_profile'|__}</a></li>
            {/if}
            <li><a href="{"auth.logout?redirect_url=`$return_current_url`"|fn_url}" rel="nofollow"
                   class="account">{__("sign_out") nofilter}</a></li>
        </ul>
    {else}
        <div class="marged">
            <div id="my_account_login_form">
                {include file="views/auth/login_form.tpl" id="checkout_login" style="checkout" result_ids="checkout*,account*"}
                <hr class="marged"/>
                <a class="w-button grey medium cm-show" data-ca-hide-self=false
                   data-ca-show-target="my_account_creation_form"
                   data-ca-hide-target="my_account_login_form">{__('register')}</a>
            </div>
            <div id="my_account_creation_form" class="hidden">
                <a class="w-button grey medium cm-show" data-ca-hide-self=false
                   data-ca-show-target="my_account_login_form"
                   data-ca-hide-target="my_account_creation_form">{__('sign_in')}</a>
                <hr class="marged"/>
                <form name="step_one_register_form" class=""
                      action="{""|fn_url}" method="post">
                    <input type="hidden" name="redirect_url" value="{$config.current_url}"/>

                    <div>
                        {include file="views/profiles/components/profiles_account.tpl" nothing_extra="Y" location="checkout"}
                        <div class="clear"></div>
                    </div>
                    <div class="clearfix">
                        {include file="buttons/button.tpl" but_name="dispatch[profiles.update]" but_role="w-action" but_color="orange" but_size="large" but_text=__("register")}
                    </div>
                </form>
            </div>
        </div>
    {/if}
    <!--account_info_{$block.snapping_id}--></div>
