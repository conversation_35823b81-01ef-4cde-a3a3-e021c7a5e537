{if $completed_steps.step_two && $edit_step == "step_four"}
<div class="row">
    <div class="col-xs-12">
        <fieldset>
        <h2>
            {$title}
        </h2>
        <div class="sidebox-wrapper">
        <div class="sidebox-body">
            {assign var="profile_fields" value="I"|fn_get_profile_fields}

            {if !$cart.shipping_failed && !empty($cart.chosen_shipping) && $cart.shipping_required}
                {include file="common/cards/delivery.tpl" hide_title=true capture=false}
                <hr class="marged"/>
            {/if}

            {if $profile_fields.B}
                <h4>{__("billing_address")} :</h4>
                <div class="shipping-adress clearfix" id="tygh_billing_adress">
                    {include file="common/cards/address.tpl" ship_to_another=$cart.ship_to_another card_id="billing_address" section="B" reload_id="tygh_billing_adress" card_title=__("billing_address") hide_title=true}
                    <!--tygh_billing_adress--></div>
                <hr class="marged"/>
            {/if}

            {if $profile_fields.S && $cart.shipping_required}
                <h4>{__("shipping_address")} :</h4>
                <div class="shipping-adress clearfix" id="tygh_shipping_adress">
                    {include file="common/cards/address.tpl" ship_to_another=true card_id="shipping_address" section="S" reload_id="tygh_shipping_adress" card_title=__("shipping_address") hide_title=true}
                    <!--tygh_shipping_adress--></div>
            {/if}
        </div>
        </div>
    </fieldset>
    </div>
</div>
{/if}
{assign var="block_wrap" value="checkout_order_info_`$block.snapping_id`_wrap" scope="parent"}
