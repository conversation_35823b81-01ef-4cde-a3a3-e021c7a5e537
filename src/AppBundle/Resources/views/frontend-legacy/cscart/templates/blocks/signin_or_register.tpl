<div class="row">
    <div class="col-md-6 col-xs-12">
        <fieldset>
            <form name="step_one_register_form" class="{$ajax_form} cm-ajax-full-render" action="{""|fn_url}" method="post">
                <input type="hidden" name="result_ids" value="checkout*,account*"/>
                {$extras nofilter}

                {$register_title = __("register_new_account")}
                {if $custom_register_title}{$register_title=$custom_register_title}{/if}
                {$register_promo_text = __("w_login_promo_text")}
                {if $custom_register_promo_text}{$register_promo_text=$custom_register_promo_text}{/if}

                {include file="common/subheader.tpl" title=$register_title}
                {$register_promo_text nofilter}
                {include file="views/profiles/components/profiles_account.tpl" nothing_extra="Y" location="checkout"}
                <div class="row">
                    <div class="col-xs-12">
                        <div class="form-group">
                            {include file="buttons/button.tpl" but_name=$register_dispatch|default:'dispatch[profiles.update]' but_role="w-action" but_color="btn-primary" but_size="large" but_text=__("register")}
                        </div>
                    </div>
                </div>
            </form>
        </fieldset>
    </div>
    <div class="col-md-6 col-xs-12">
        <fieldset class="test-login-form">
            {include file="views/auth/login_form.tpl" id="checkout_login" style="checkout" custom_login_title=$custom_login_title custom_login_promo_text=$custom_login_promo_text result_ids="checkout*,account*" return_url=$login_return_url}
        </fieldset>
    </div>
</div>
