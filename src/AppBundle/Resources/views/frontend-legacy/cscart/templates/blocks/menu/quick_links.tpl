{** block-description:quick_links **}
{if $items}
    <ul class="text-links text-links-inline inline-block" id="top-quick-links">
        {foreach from=$items item="menu"}
            <li class="level-{$menu.level|default:0}{if $menu.active} active{/if}">
                <a {if $menu.href}href="{$menu.href|fn_url}"{/if}
                   class="top-quick-link">{$menu.item}</a>{if !$smarty.foreach.items.last}&nbsp;|&nbsp;{/if}
                {if $menu.subitems}
                    {include file="blocks/menu/text_links.tpl" items=$menu.subitems}
                {/if}
            </li>
        {/foreach}
    </ul>
{/if}