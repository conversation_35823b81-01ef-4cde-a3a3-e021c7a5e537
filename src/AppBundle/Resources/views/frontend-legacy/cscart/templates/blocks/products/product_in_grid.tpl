{assign var="obj_id" value=$product.product_id}
{assign var="obj_id_prefix" value="`$obj_prefix``$product.product_id`"}
<div class="product-cell-wrapper">

    {assign var="form_open" value="form_open_`$obj_id`"}
    {$smarty.capture.$form_open nofilter}
    {hook name="products:product_multicolumns_list"}
        <table class="center-block">
            <tr class="valign-top">
                <td class="preview-image">
                    <div class="preview-image-wrapper">
                        {if $product.promotions}<a href="{WizachaProduct::frontUrl($product.product_id)}" class="promo-bandeau"><img src="{$images_dir}/bandeau_promo.png" alt="" /></a>{/if}
                        {include file="views/products/components/product_icon.tpl" product=$product show_gallery=true}
                        {if $product.list_discount_prc}
                            <span class="thumb-discount-label">{strip}
                                    {__("save_discount")} {$product.list_discount_prc}%
                                {/strip}
                        </span>
                        {/if}
                    </div>
                </td>
            </tr>
            <tr>
                <td class="product-title-wrap">
                    {if $item_number == "Y"}<span class="item-number">{$cur_number}.
                        &nbsp;</span>{math equation="num + 1" num=$cur_number assign="cur_number"}{/if}
                    {assign var="view_name" value="view_name_$obj_id"}
                    <a href="{WizachaProduct::frontUrl($product.product_id)}"
                       class="product-title"
                       title="{$product.product|strip_tags}">{$smarty.capture.$view_name nofilter}</a>
                </td>
            </tr>
            <tr>
                <td class="product-description">

                    <p>
                        {assign var="old_price" value="old_price_`$obj_id`"}
                        {if $smarty.capture.$old_price|trim}{$smarty.capture.$old_price nofilter}{/if}

                        {assign var="price" value="price_`$obj_id`"}
                        {$smarty.capture.$price nofilter}

                        {assign var="list_discount" value="list_discount_`$obj_id`"}
                        {$smarty.capture.$list_discount nofilter}
                    </p>

                    {if $settings.Appearance.enable_quick_view == 'Y'}
                        {include file="views/products/components/quick_view_link.tpl" quick_nav_ids=$quick_nav_ids}
                    {/if}

                    {if $show_add_to_cart}
                        <div class="buttons-container-item">
                            {assign var="add_to_cart" value="add_to_cart_`$obj_id`"}
                            {$smarty.capture.$add_to_cart nofilter}
                        </div>
                    {/if}
                </td>
            </tr>
        </table>
    {/hook}
    {assign var="form_close" value="form_close_`$obj_id`"}
    {$smarty.capture.$form_close nofilter}

</div>