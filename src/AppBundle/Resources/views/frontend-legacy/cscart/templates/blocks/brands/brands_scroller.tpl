{** block-description:w_brands_scroller **}

{assign var="obj_prefix" value="`$block.block_id`000"}
{assign var="delim_width" value=$block.properties.delim_width|default:4}
{math equation="delim_w + image_w" assign="item_width" image_w=$block.properties.thumbnail_width delim_w = $delim_width}
{assign var="item_qty" value=$block.properties.item_quantity}

<div class="brands-scroller {$block.user_class}">
    <div class="left-block">
        <img src="{$images_dir}/brand_illustration.jpg" alt="" />
    </div><ul id="scroll_list_{$block.block_id}" class="jcarousel jcarousel-skin jcarousel-multiple" style="display: none;">
        {assign var="image_h" value=$block.properties.thumbnail_width + 2 }

        {math equation="image_h * nb_items_v" assign="item_height" image_h=$block.properties.thumbnail_width + 2 nb_items_v = $nb_items_vertical}
        <li>
            {foreach from=$items item="brand" name="for_brands"}
            {if $smarty.foreach.for_brands.index % $nb_items_vertical == 0 && !$smarty.foreach.for_brands.first}
        </li>
        <li>
            {/if}
            <div class="" style="width: {$item_width}px; height:{$block.properties.thumbnail_width}px;">
                {if !empty($brand.image_pair)}
                    {include file="common/image.tpl" assign="object_img" image_width=$block.properties.thumbnail_width image_height=$block.properties.thumbnail_width images=$brand.image_pair no_ids=true}
                    <div class="center brand-image" style="height: {$image_h}px;">
                        <a href="{"product_features.view?variant_id=`$brand.variant_id`"|fn_url}">{$object_img nofilter}</a>
                    </div>
                {else}
                    <div class="center compact brand-text">
                        <a href="{"product_features.view?variant_id=`$brand.variant_id`"|fn_url}">{$brand.variant}</a>
                    </div>
                {/if}
            </div>
            {/foreach}
        </li>
    </ul>
</div>
{$block.properties.scroller_direction="left"}
{$block.properties.not_scroll_automatically="Y"}
{include file="common/scroller_init.tpl" mode='multiple_items' nb_items_vertical=$nb_items_vertical nb_items_horizontal=$nb_items_horizontal}
