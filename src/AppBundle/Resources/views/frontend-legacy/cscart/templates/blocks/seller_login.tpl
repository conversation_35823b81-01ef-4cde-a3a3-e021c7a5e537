{capture name='product_new_url'}{path route="product_new"}{/capture}
{capture name="extras"}
    <input type="hidden" name="return_url" value="{$smarty.capture.product_new_url}" />
{/capture}
{include
    file="blocks/signin_or_register.tpl"
    extras=$smarty.capture.extras
    login_return_url={$smarty.capture.product_new_url}
    custom_register_title=__('w_c2c_register_title')
    custom_register_promo_text=__('w_c2c_register_promo_text')
    custom_login_title=__('w_c2c_login_title')
    custom_login_promo_text=__('w_c2c_login_promo_text')
}
