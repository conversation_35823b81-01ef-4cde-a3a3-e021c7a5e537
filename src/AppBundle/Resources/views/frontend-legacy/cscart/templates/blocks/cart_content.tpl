{assign var="dropdown_id" value=$block.snapping_id}
{assign var="r_url" value=$config.current_url|escape:url}
{hook name="checkout:cart_content"}
    <div class="dropdown-box cart-container cm-auto-dropdown" id="cart_status_{$dropdown_id}">
        <div class="cart-icon-wrapper top">
            {if $smarty.session.cart.amount}
                <i class="icon-basket filled"></i>
            {else}
                <i class="icon-basket empty"></i>
            {/if}
        </div>
        <a href="{"checkout.cart"|fn_url}" id="w-sw_dropdown_{$dropdown_id}"
           class="popup-title cm-combination top">
            {__("w_my_cart")}
        </a>

        <div class="cm-auto-dropdown-target cm-popup-box popup-content hidden" style="border:none;">
            {hook name="checkout:minicart"}
                <div class="cm-cart-content {if $block.properties.products_links_type == "thumb"}cm-cart-content-thumb{/if} {if $block.properties.display_delete_icons == "Y"}cm-cart-content-delete{/if}">
                    <div class="cart-items">
                        {if $smarty.session.cart.amount}
                            <table class="minicart-table">
                                {hook name="index:cart_status"}
                                {assign var="_cart_products" value=$smarty.session.cart.products|array_reverse:true}
                                {foreach from=$_cart_products key="key" item="p" name="cart_products"}
                                    {if !$p.extra.parent}
                                        <tr class="minicart-separator">
                                            {if $block.properties.products_links_type == "thumb"}
                                                <td style="width: 5%"
                                                    class="cm-cart-item-thumb">{include file="common/image.tpl" image_width="40" image_height="40" images=$p.main_pair no_ids=true}</td>
                                            {/if}
                                            <td style="width: 94%"><a
                                                        href="{"products.view?product_id=`$p.product_id`"|fn_url}">{$p.product_id|fn_get_product_name nofilter}</a>

                                                <p>
                                                    <span>{$p.amount}</span><span>&nbsp;x&nbsp;</span>{include file="common/price.tpl" value=$p.display_price span_id="price_`$key`_`$dropdown_id`" class="none"}
                                                </p></td>
                                        </tr>
                                    {/if}
                                {/foreach}
                                {/hook}
                            </table>
                        {else}
                            <p class="center">{__("cart_is_empty")}</p>
                        {/if}
                    </div>
                    {if $block.properties.display_bottom_buttons == "Y"}
                        <div class="cm-cart-buttons buttons-container{if $smarty.session.cart.amount} full-cart{else} hidden{/if}">
                            <div class="float-right">{include file="buttons/button.tpl" but_role="w-action" but_href="checkout.cart" but_color="orange" but_size="medium" but_text=__("w_command") }</div>
                            <div class="view-cart-button">
                                {include file="buttons/button.tpl" but_role="w-action" but_href="checkout.cart" but_color="grey" but_size="medium" but_text=__("view_cart") }
                            </div>

                        </div>
                    {/if}
                </div>
            {/hook}
        </div>
        <!--cart_status_{$dropdown_id}--></div>
{/hook}
