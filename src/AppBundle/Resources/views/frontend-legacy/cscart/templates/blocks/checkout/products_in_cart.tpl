<div id="checkout_info_products_{$block.snapping_id}">
    <ul class="order-product-list">
        {foreach from=$product_groups item="product_group" key="product_group_id"}
            {assign var="prods" value=false}
            <li class="vendor">{__("vendor")} {$product_group.name}</li>
            {foreach from=$product_group.products item="product" key="key" name="cart_products"}
                {if !$cart.products.$key.extra.parent}
                    <li class="sep">
                        <div class="inline-block middle">
                            {include file="common/image.tpl" obj_id=$key images=$product.main_pair image_width=50 image_height=50}
                        </div>
                        <div class="inline-block middle">
                            <div class="brand">{$cart_products[$key].product_features|fn_product_get_brand}</div>
                            <div>{$product.product|truncate:30}</div>
                            <span class="product-price">
                                {$product.amount}&nbsp;x&nbsp;{format_price price=$product.display_price}
                                {__('ttc')}
                            </span>
                        </div>
                    </li>
                {/if}
            {/foreach}
        {/foreach}
    </ul>
<!--checkout_info_products_{$block.snapping_id}--></div>
