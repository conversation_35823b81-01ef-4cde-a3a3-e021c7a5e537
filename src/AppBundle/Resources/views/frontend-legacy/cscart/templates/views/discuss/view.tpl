{capture name="tabs_content"}
    {capture name="backlink"}
        <a href="{"discuss.list"|fn_url}">{__('discuss_back_to_discussion_list')}</a>
    {/capture}
    <div id="discuss-container">
        <div class="discuss-title">
            <p>{if $product_url}<a href="{$product_url}">{/if}{__('title')} : {$discussion->getMetaData('title')}{if $product_url}</a>{/if}</p>
        </div>
        {foreach $messages as $message }
            {$class="message-author"}
            {$date_message = "discuss_send_date"}
            {if $auth.user_id != $message->getAuthor()}
                {$class="message-recipient"}
                {$date_message = "discuss_reception_date"}
            {/if}
            <div class="message-container {$class}">
                {$message_date = $message->getSendDate()|date_format:"`$settings.Appearance.date_format`"}
                {$message_time = $message->getSendDate()|date_format:"%Hh%M"}
                <span class="smalltext">{__($date_message, ['[date]'=>$message_date, '[time]' =>$message_time])}</span>
                <div class="message">{$message->getContent() nofilter}</div>
            </div>
        {/foreach}
        <script type="text/javascript">
            $(function(){
                {* Force discuss-container to scroll to its own bottom in order to display last messages *}
                var $container = $("#discuss-container");
                $container.scrollTop($container[0].scrollHeight);
            });
        </script>
    </div>
    <hr class="marged"/>
    <div class="discuss-create-message">
        <form name="profile_form" action="{""|fn_url}" method="post">
            <input type="hidden" name="discussion_id" value="{$discussion->getId()|default:0}" />
            <input type="hidden" name="product_id" value="{$smarty.request.product_id|default:0}" />
            <input type="hidden" name="company_id" value="{$smarty.request.company_id|default:0}" />
            <textarea placeholder="{__('discuss_your_message')}" name="content" rows="5" class="inline-block middle" ></textarea>
            <button class="btn btn-primary inline-block top" name="dispatch[discuss.update]" id="add_message_but">{__("send")}</button>
        </form>
    </div>
    {$smarty.capture.backlink nofilter}
{/capture}

{include file="views/profiles/components/profile_menu.tpl" content=$smarty.capture.tabs_content active_tab=messages}
