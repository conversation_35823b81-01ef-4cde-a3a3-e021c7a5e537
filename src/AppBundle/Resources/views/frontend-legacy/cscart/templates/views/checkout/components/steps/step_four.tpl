{script src="js/tygh/tabs.js"}
<script type="text/javascript">
    $(function () {
        $(".breadcrumb-panier li:nth-child(3)").addClass("breadcrumb-actif");
    });
</script>
<div class="row">
    <div class="col-xs-12">
        <fieldset>
            <div class="step-container{if $edit}-active{/if} step-four" data-ct-checkout="billing_options" id="step_four">
                <h2>
                    {__("w_choose_payment_method")} <img src="{$images_dir}/secured-icon.png" alt="" style="height:20px;"/>
                </h2>

                <div id="step_four_body" class="step-body{if $edit}-active{/if} {if !$edit}hidden{/if}">
                    <div class="clearfix">

                        {if $cart|fn_allow_place_order}
                            {if $edit}
                                {if $cart.payment_id}
                                    {include file="views/checkout/components/payments/payment_methods.tpl" payment_id=$cart.payment_id}
                                {else}
                                    <div class="checkout-inside-block"><h2 class="subheader">{__("text_no_payments_needed")}</h2>
                                    </div>
                                    <form name="paymens_form" action="{path route="checkout_payment_update"}" method="post">
                                        <input type="hidden" name="accept_terms" value="Y">
                                        {include file="buttons/place_order.tpl" but_text=__("w_proceed_to_secured_payment") but_color="btn-primary" but_size="large" but_name="button_place_order" but_role="w-action" but_id="place_order" but_meta="test-next-step"}
                                    </form>
                                {/if}
                            {/if}

                        {else}
                            {if $cart.shipping_failed}
                                <p class="error-text center">{__("text_no_shipping_methods")}</p>
                            {/if}

                            {if $cart.amount_failed}
                                <div class="checkout-inside-block">
                                    <p class="error-text">{__("text_min_order_amount_required")}
                                        &nbsp;<strong>{include file="common/price.tpl" value=$settings.General.min_order_amount}</strong>
                                    </p>
                                </div>
                            {/if}
                            <div class="checkout-buttons">
                                {include file="buttons/continue_shopping.tpl" but_href=$continue_url|fn_url but_role="action"}
                            </div>
                        {/if}
                    </div>
                </div>
            <!--step_four--></div>
        </fieldset>
    </div>
</div>
