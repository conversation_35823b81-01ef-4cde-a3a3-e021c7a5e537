{capture name="tabsbox"}
    <div id="content_c2c_company_update_informations">
        {__('w_c2c_company_update_informations_explanation')}
        <form id='form' action="{""|fn_url}" method="post" name="company_update_informations_form" class="form-horizontal form-edit  cm-disable-empty-files" enctype="multipart/form-data">

            {include file="views/profiles/components/profile_fields.tpl" additionnal_label_class='block' section='C' body_id="profile_general" ship_to_another=true}

            <div class="form-group">
                <label for="elm_company_phone" class="required control-label col-sm-3 cm-required cm-phone">{__("phone")}</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" name="company_data[phone]" id="elm_company_phone" size="32" value="{$company_data.phone}" />
                </div>
            </div>

            <div class="form-group">
                <label for="elm_company_address" class="required control-label col-sm-3 cm-required">{__('address')}</label>
                <div class="col-sm-6">
                    <textarea row="4" class="form-control" cols="40" name="company_data[address]" id="elm_company_address" size="32">{$company_data.address}</textarea>
                </div>
            </div>

            <div class="form-group">
                <label for="elm_company_zipcode" class="required control-label col-sm-3 cm-required cm-zipcode cm-location-shipping">{__('zip_postal_code')}</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" name="company_data[zipcode]" id="elm_company_zipcode" size="32" value="{$company_data.zipcode}">
                </div>
            </div>

            <div class="form-group">
                <label for="elm_company_city" class="required control-label col-sm-3 cm-required">{__('city')}</label>
                <div class="col-sm-6">
                    <input type="text" class="form-control" name="company_data[city]" id="elm_company_city" size="32" value="{$company_data.city}">
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label cm-required" for="elm_company_name">
                    {__('w_c2c_company_name')}
                </label>
                <div class="col-sm-6">
                    <input id="elm_company_name" class="form-control" {if !empty($company_data.status) && $company_data.status != WizachaCompany::STATUS_NEW}disabled{/if} type="text" value="{$company_data.company}" size="32" name="company_data[company]"/>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label cm-required required" for="applier_id">
                    {__('vendor_w_id_card')}
                </label>
                <div class="col-sm-6">
                    <input id="applier_id" class="input-file" type="file" name="w_ID_card"/>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-3 control-label cm-required required" for="applier_rib">
                    {__('vendor_w_rib')}
                </label>
                <div class="col-sm-6">
                    <input id="applier_rib" class="input-file" type="file" name="w_RIB"/>
                </div>
            </div>

            <div class="clearfix">
                {include file="buttons/button.tpl" but_name="dispatch[c2c_company.update_informations]" but_role="w-action" but_color="btn-primary" but_size="large" but_text=__("w_c2c_save_company_informations")}
            </div>


        </form>
        <!--content_c2c_company_update_informations--></div>
{/capture}
{include file="common/c2c/components/menu.tpl" content=$smarty.capture.tabsbox active_tab=$smarty.request.selected_section|default:'c2c_company_update_informations'}
