{hook name="companies:view"}

{assign var="obj_id" value=$company_data.company_id}
{assign var="obj_id_prefix" value="`$obj_prefix``$obj_id`"}

    {if !empty($company_data.company_description)}
        <div class="tabs cm-j-tabs no-margin clearfix">
            <ul class="nav nav-tabs">
                    <li class="cm-js active">
                        <a>{$company_data.company}</a>
                    </li>
                <li class="cm-js extra-tab">
                    <a href="#company_products">{__('view_products')}</a>
                </li>
            </ul>
        </div>
        <div class="cm-tabs-content tabs-content clearfix">
            <div id="content_description"
                 class="{if $selected_section && $selected_section != "description"}hidden{/if}">
                {if $company_data.company_description}
                    <div class="wysiwyg-content">
                        {$company_data.company_description nofilter}
                    </div>
                {/if}
            </div>
        </div>
    {/if}
    <div id="company_products" class="search_results_4cols pagination-container">
        {include file="common/search/products_search_init.tpl" company_name=$company_data.company facets_enabled=false}
    </div>

{/hook}