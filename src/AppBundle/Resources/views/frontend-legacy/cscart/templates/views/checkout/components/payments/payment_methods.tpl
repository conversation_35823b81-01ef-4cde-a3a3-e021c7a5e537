{script src="js/tygh/tabs.js"}

{if $order_id}
    {$url = "orders.details?order_id=`$order_id`"}
    {$result_ids = "elm_payments_list"}
{else}
    {$url = "checkout.checkout"}
    {$result_ids = "checkout*,step_four"}
{/if}


<script type="text/javascript">
    (function (_, $) {
        $(_.doc).on('click', '.cm-select-payment', function () {
            var self = $(this);
            var accept_terms = $("input:checked[name=accept_terms]").length ==0 ? '' : '&accept_terms=Y';
            $.ceAjax('request', fn_url('{$url}&payment_id=' + self.val() + accept_terms), {
                result_ids: '{$result_ids}',
                full_render: true
            });
        });
    }(Tygh, Tygh.$));
</script>


{if $payment_methods|count > 1}
    <div class="tabs cm-j-tabs cm-track clearfix">
        <ul id="payment_tabs">
            {foreach from=$payment_methods key="tab_id" item="payments"}
                {assign var="tab_name" value="payments_`$tab_id`"}
                {if $tab_id == $active_tab || (!$active_tab && $payments[$payment_id])}
                    {$active = $tab_id}
                {/if}
                {$first_payment = $payments|reset}
                <li id="payments_{$tab_id}"
                    class="{if $tab_id == $active_tab || (!$active_tab && $payments[$payment_id])}active{/if}">
                    <a class="cm-ajax cm-ajax-full-render" data-ca-target-id="{$result_ids}"
                       href="{"`$url`?active_tab=`$tab_id`&payment_id=`$first_payment.payment_id`"|fn_url}">{__($tab_name)}</a>
                </li>
            {/foreach}
        </ul>
    </div>
{/if}

<div class="clearfix marged">
    {foreach from=$payment_methods key="tab_id" item="payments"}
        <div class="{if $active != $tab_id && $payment_methods|count > 1}hidden{/if}" id="content_payments_{$tab_id}">
            <form name="payments_form_{$tab_id}" action="{path route="checkout_payment_update"}" method="post">
                <input type="hidden" name="result_ids" value="{$result_ids}"/>
                <input type="hidden" name="payment_id" value="{$payment_id}"/>

                {if $order_id}
            <input type="hidden" name="order_id" value="{$order_id}"/>
                {else}
                <div class="checkout-billing-options {if $payment_methods|count == 1}notab{/if}">
                    {/if}

                    {if $payments|count == 1}
                        {assign var="payment" value=$payments|reset}

                        {capture name="payment_template"}
                            {if $payment.image}
                                <div class="clearfix">
                                    {include file="common/image.tpl" obj_id=$payment.payment_id images=$payment.image image_width=$settings.Thumbnails.product_cart_thumbnail_width image_height=$settings.Thumbnails.product_cart_thumbnail_height}
                                </div>
                            {/if}
                        {/capture}

                        {if $smarty.capture.payment_template|trim != ""}
                            <div class="clearfix">
                                <div class="other-text other-text-right">{$payment.instructions nofilter}</div>
                                {$smarty.capture.payment_template nofilter}
                            </div>
                        {else}
                            {include file="views/checkout/components/payments/payments_list.tpl" payments=[$payment]}
                        {/if}

                    {else}
                        {include file="views/checkout/components/payments/payments_list.tpl"}
                    {/if}

                    {if $order_id}
                    {include file="views/checkout/components/customer_notes.tpl"}

                    <div class="checkout-buttons">
                        {if $payment_method.params.button}
                            {$payment_method.params.button}
                        {/if}
                    </div>

                    {else}
                    {include file="views/checkout/components/terms_and_conditions.tpl" suffix=$tab_id}

                    {assign var="show_checkout_button" value=false}

                    {if $auth.act_as_user}
                        <div class="select-field">
                            <input type="checkbox" id="skip_payment" name="skip_payment" value="Y" class="checkbox"/>
                            <label for="skip_payment">{__("skip_payment")}</label>
                        </div>
                    {/if}
                </div>

                    {include file="buttons/place_order.tpl" but_text=__("w_proceed_to_secured_payment") but_name="button_place_order" but_role="w-action" but_id="place_order_`$tab_id`" but_meta="test-next-step"}
                {/if}

                <div class="processor-buttons hidden"></div>
            </form>

            <!--content_payments_{$tab_id}--></div>
    {/foreach}
</div>
