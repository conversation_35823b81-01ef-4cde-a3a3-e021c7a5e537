{capture name="cartbox"}
    {if $runtime.mode == "checkout"}
        {if $cart.coupons|floatval}<input type="hidden" name="c_id" value=""/>{/if}
        {hook name="checkout:form_data"}
        {/hook}
    {/if}

    <script type="text/javascript">
        Tygh.$(function () {
            var cart_items = Tygh.$("#cart_items");
            cart_items.on('change', function (e) {
                var jelm = Tygh.$(e.target);
                if(jelm.hasClass('cm-amount')){
                    var amount = jelm.val();
                    if(0 == amount){
                        jelm.val(e.target.defaultValue);
                        var key = jelm.attr('w-action-key');
                        var row_clone = cart_items.find('fieldset').clone();
                        var template = Hogan.compile($('#delete_popup').html());
                        row_clone.find('div.quantity').removeClass('quantity changer').html(jelm.val());
                        $('#generic_modal .modal-content').html(template.render({
                                    popup_detail: row_clone.html(),
                                    confirm_url: fn_url("checkout.delete?cart_id="+key+"&redirect_mode={$runtime.mode}")
                                })
                        );
                        $('#generic_modal').modal('show');

                        return false;
                    }
                }
            });
        });

        $(function(){
            $("#cart_items").on('change', '.cm-value-changer', function(){
                $("#"+$(this).find('input').data('targetField')).val($(this).find('input').val());
            });
        });
    </script>

    <div id="cart_items">
        {if $product_groups}
            {foreach from=$product_groups item="product_group" key="product_group_id"}
                <div class="row">
                <div class="col-xs-12">
                {if $product_group.products}
                    {assign var="prods" value=false}
                    <fieldset class="cart-group" id="cart_group_{$key}">
                        <h2>{__("vendor")} : {$product_group.name}</h2>
                        {foreach from=$product_group.products item="product" key="key" name="cart_products"}
                        {if !$smarty.foreach.cart_products.first}
                            <hr/>
                        {/if}
                        <div class="row" id="cart_item_{$key}">
                            {assign var="obj_id" value=$product.object_id|default:$key}
                            {if !$cart.products.$key.extra.parent}
                                <div class="hidden-xs">
                                    <div class="col-xs-4 col-sm-2 inline-block middle">
                                        {include file="common/image.tpl" obj_id=$key images=$cart_products[$key].main_pair image_width=$settings.Thumbnails.product_cart_thumbnail_width image_height=$settings.Thumbnails.product_cart_thumbnail_height}
                                    </div><!-- inline-block fix
                                    --><div class="col-xs-8 col-sm-6 inline-block middle" >
                                        {$cart_products[$key].product_features|fn_product_get_brand}<br/>
                                        {$product.product}<br/>
                                        <br/>
                                        {if $cart_products[$key].in_stock > 0}
                                            <span class="qty-in-stock">{__('in_stock')}</span>
                                        {else}
                                            <span class="qty-out-of-stock">{__("text_combination_out_of_stock")}</span>
                                        {/if}

                                        {if $cart.age_verification}
                                            {assign var="cart_product" value=$cart.products.$key}
                                            {if $cart_product.age_verification}
                                                <br/><span class="red smalltext">{'w_age_verification_product_unavailable'|__:['[age]'=>$cart_product.age_verification]}</span>
                                            {/if}
                                        {/if}
                                    </div><!-- inline-block fix
                                    --><div class="col-xs-6 col-sm-2 inline-block middle">
                                        {if $product.product_options}
                                            <ul class="product-options-summary">
                                                {foreach from=$cart_products[$key].product_options item="option"}
                                                    <input type="hidden" name="cart_products[{$key}][product_options][{$option.option_id}]" value="{$cart_products[$key].selected_options[$option.option_id]}" />
                                                    <li><strong>{$option.option_name}</strong> : {$option.variants[$cart_products[$key].selected_options[$option.option_id]].variant_name}</li>
                                                {/foreach}
                                            </ul>
                                        {/if}
                                        {if $use_ajax == true && $cart.amount != 1}
                                            {assign var="ajax_class" value="cm-ajax"}
                                        {/if}
                                        <div class="inline-block middle">{__("amount")} :</div>
                                        <div class="quantity changer inline-block middle"
                                             id="quantity_update_{$obj_id}">
                                            <input type="hidden" name="cart_products[{$key}][product_id]"
                                                   value="{$product.product_id}"/>

                                            <div class="center valign value-changer cm-value-changer">
                                                <input w-action-key="{$key}" type="number" size="3"
                                                       data-target-field="amount_{$key}"
                                                       value="{$product.amount}"
                                                       class="form-control cm-amount cm-submit"
                                                       style="width:6rem;"
                                                       data-ca-dispatch="dispatch[checkout.update]" {if $product.qty_step > 1} step="{$product.qty_step+5}"{/if} />
                                            </div>
                                        </div>

                                    </div><!-- inline-block fix
                                    --><div class="col-xs-6 col-sm-2 inline-block middle" >
                                        <div class="inline-block">
                                            {if $product.discount}
                                                <div class="left w-old-price">{include file="common/price.tpl" value=$product.base_price span_id="product_baseprice_`$key`" class="strike"}</div>
                                            {/if}
                                            {include file="common/price.tpl" value=$product.price*$product.amount span_id="product_subtotal_`$key`" class="price w-price"}
                                            <br/>

                                            <div><span id="indic_amount_{$key}">{$product.amount}</span>
                                                x {include file="common/price.tpl" value=$product.price span_id="product_baseprice_`$key`" class="price"}
                                            </div>
                                            <input type="hidden" name="cart_products[{$key}][price]"
                                                   value="{$product.price}"/>
                                        </div>
                                        <!--price_subtotal_update_{$obj_id}--></div>
                                </div>
                                <div class="visible-xs">
                                    <div class="col-xs-4">
                                        {include file="common/image.tpl" obj_id=$key images=$cart_products[$key].main_pair image_width=$settings.Thumbnails.product_cart_thumbnail_width image_height=$settings.Thumbnails.product_cart_thumbnail_height}
                                    </div>
                                    <div class="col-xs-7 col-xs-offset-1">
                                        <div class="row">
                                            <div class="col-xs-12">
                                                {if $product.product_options}
                                                    <ul class="product-options-summary">
                                                        {foreach from=$cart_products[$key].product_options item="option"}
                                                            <input type="hidden" name="cart_products[{$key}][product_options][{$option.option_id}]" value="{$cart_products[$key].selected_options[$option.option_id]}" />
                                                            <li><strong>{$option.option_name}</strong> : {$option.variants[$cart_products[$key].selected_options[$option.option_id]].variant_name}</li>
                                                        {/foreach}
                                                    </ul>
                                                {/if}
                                                {if $use_ajax == true && $cart.amount != 1}
                                                    {assign var="ajax_class" value="cm-ajax"}
                                                {/if}
                                                <div class="inline-block middle">{__("amount")} :</div>
                                                <div class="quantity changer inline-block middle"
                                                     id="quantity_update_{$obj_id}">
                                                    <input type="hidden" name="cart_products[{$key}][product_id]"
                                                           value="{$product.product_id}"/>

                                                    <div class="center valign value-changer cm-value-changer">
                                                        <input w-action-key="{$key}" type="number" size="3"
                                                               data-target-field="amount_{$key}"
                                                               value="{$product.amount}"
                                                               class="input-text-short cm-amount cm-submit" data-ca-dispatch="dispatch[checkout.update]" {if $product.qty_step > 1} step="{$product.qty_step+5}"{/if} />
                                                    </div>
                                                </div>

                                            </div>
                                            <div class="col-xs-12" >
                                                <div class="inline-block">
                                                    {if $product.discount}
                                                        <div class="left w-old-price">{include file="common/price.tpl" value=$product.base_price span_id="product_baseprice_`$key`" class="strike"}</div>
                                                    {/if}
                                                    {include file="common/price.tpl" value=$product.price*$product.amount span_id="product_subtotal_`$key`" class="price w-price"}
                                                    <br/>

                                                    <div><span id="indic_amount_{$key}">{$product.amount}</span>
                                                        x {include file="common/price.tpl" value=$product.price span_id="product_baseprice_`$key`" class="price"}
                                                    </div>
                                                    <input type="hidden" name="cart_products[{$key}][price]"
                                                           value="{$product.price}"/>
                                                </div>
                                                <!--price_subtotal_update_{$obj_id}--></div>
                                        </div>
                                    </div>
                                    <div class="clearfix"></div>
                                    <div class="col-xs-12 col-sm-6" >
                                        {$cart_products[$key].product_features|fn_product_get_brand}<br/>
                                        {$product.product}<br/>
                                        <br/>
                                        {if $cart_products[$key].in_stock > 0}
                                            <span class="qty-in-stock">{__('in_stock')}</span>
                                        {else}
                                            <span class="qty-out-of-stock">{__("text_combination_out_of_stock")}</span>
                                        {/if}

                                        {if $cart.age_verification}
                                            {assign var="cart_product" value=$cart.products.$key}
                                            {if $cart_product.age_verification}
                                                <br/><span class="red smalltext">{'w_age_verification_product_unavailable'|__:['[age]'=>$cart_product.age_verification]}</span>
                                            {/if}
                                        {/if}
                                    </div>
                                </div>
                            {/if}
                        </div>
                            <input type="hidden" id="amount_{$key}" value="{$product.amount}" name="cart_products[{$key}][amount]" />

                        {/foreach}
                        {if !$product_group.all_edp_free_shipping}
                        <div class="cart-select-shipping">
                            {__("w_select_shipping_mode")} :
                            <select name="shipping_ids[{$product_group_id}]" class="cm-submit" data-ca-dispatch="dispatch[checkout.update_shipping]">
                                {foreach from=$product_group.shippings item="shipping"}
                                    <option value="{$shipping.shipping_id}" {if $cart.chosen_shipping[$product_group_id] == $shipping.shipping_id}selected{/if}>
                                        {$shipping.shipping}
                                        - {include file="common/price.tpl" value=$shipping.rate}
                                    </option>
                                {/foreach}
                            </select>
                        </div>
                        {/if}
                    </fieldset>
                {/if}
            </div>
            </div>
            {/foreach}
        {/if}

        {hook name="checkout:extra_list"}
        {/hook}

        <!--cart_items--></div>
{/capture}
{include file="common/mainbox_cart.tpl" title=__("cart_items") content=$smarty.capture.cartbox}

<script type="text/hogan" id="delete_popup" class="hidden">

    <div class="modal-header">
        <h2>{__('w_remove_item_from_cart_title')}</h2>
    </div>
    <div class="modal-body">
        <p>{__('w_remove_item_from_cart')}</p>
        {literal}
        <fieldset>
            {{& popup_detail}}
        </fieldset>
        {/literal}
        <button class="btn-secundary" data-dismiss="modal">{__("cancel")}</button>
        <a
                href="{literal}{{& confirm_url}}{/literal}"
                class="btn btn-primary"
                >{__('confirm')}</a>
    </div>

</script>
