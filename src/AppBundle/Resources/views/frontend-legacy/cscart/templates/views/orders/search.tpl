{capture name="tabs_content"}
<div id="content_orders">
{assign var="c_url" value=$config.current_url|fn_query_remove:"sort_by":"sort_order"}
{if $search.sort_order == "asc"}
{assign var="sort_sign" value="<i class=\"icon-down-dir\"></i>"}
{else}
{assign var="sort_sign" value="<i class=\"icon-up-dir\"></i>"}
{/if}
{assign var="ajax_class" value="cm-ajax"}

    <div class="container-full">
        {include file="common/pagination.tpl"}

        {foreach from=$w_orders item="w_o" key="w_o_type"}
            {$w_o_type}
        <div class="membre-tableau table-responsive">
            <table class="table">
                <thead>
                <tr>
                    <th style="width: 5%">{__("w_order_number")}</th>
                    <th style="width: 15%">{__("date")}</th>
                    <th style="width: 15%">{__("vendor")}</th>
                    <th style="width: 10%">{__("total")}</th>
                    <th style="width: 35%">{__("status")}</th>
                    <th style="width: 20%">&nbsp;</th>

                </tr>
                </thead>
                {foreach from=$w_o item="o"}
                    <tr>
                        <td>{$o.order_id}</td>
                        <td>{$o.timestamp|date_format:"`$settings.Appearance.date_format`"}</td>
                        <td>{$o.company_id|fn_get_company_name}</td>
                        <td>{include file="common/price.tpl" value=$o.total}</td>
                        <td>{include file="common/status.tpl" status=$o.status display="view"}</td>
                        <td>
                            <a
                                    href="{"orders.details?order_id=`$o.order_id`"|fn_url}"
                                    data-remote="{"orders.details?order_id=`$o.order_id`"|fn_url} .mainbox-body"
                                    data-toggle="modal" data-target="#generic_modal"
                                    >{__("w_details_order")}</a>
                        </td>
                    </tr>
                    {foreachelse}
                    <tr>
                        <td colspan="7"><p class="no-items">{__("text_no_orders")}</p></td>
                    </tr>
                {/foreach}
            </table>
        </div>

        <br/> <br/>
        {/foreach}
        {include file="common/pagination.tpl"}
        <!--content_orders--></div>
    </div>

{/capture}

{include file="views/profiles/components/profile_menu.tpl" content=$smarty.capture.tabs_content active_tab=orders}
