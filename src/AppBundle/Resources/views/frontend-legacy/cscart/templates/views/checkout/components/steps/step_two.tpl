{if $runtime.action}
    {assign var="_action" value=".`$runtime.action`"}
{/if}
<script type="text/javascript">
    $(function () {
        $(".breadcrumb-panier li:nth-child(2)").addClass("breadcrumb-actif");
    });
</script>
<div class="step-container{if $edit}-active{/if} step-two" data-ct-checkout="billing_shipping_address" id="step_two">
    <fieldset>
        <h2 class="step-title" id="step-title">{__("billing_address")|mb_strtoupper}</h2>
    {if $cart.shipping_required !== false}
    <div id="shippingCard">
        <div class="row">
            <div id="addressSummary" class="col-sm-4"></div>
            <a class="btn btn-secondary w-button palegrey small">{__("edit")}</a>
        </div>

        <hr/>
    </div>
    {/if}
    <div class="clearfix"></div>

    <div id="step_two_body" class="step-body{if $edit}-active{/if}{if !$edit} hidden{/if} cm-skip-save-fields">
        <form name="step_two_billing_address" class="{$ajax_form} cm-ajax-full-render form-horizontal" action="{""|fn_url}"
              method="{if !$edit}get{else}post{/if}">
            <input type="hidden" name="update_step" value="step_two"/>
            <input type="hidden" name="next_step"
                   value="{if $smarty.request.from_step && $smarty.request.from_step != "step_two" && $smarty.request.from_step != "step_one"}{$smarty.request.from_step}{else}step_four{/if}"/>
            <input type="hidden" name="result_ids" value="checkout*,account*"/>
            <input type="hidden" name="dispatch" value="checkout.checkout"/>
            <div class="col-sm-12 col-md-8 col-lg-8">
            {if $smarty.request.profile == "new"}
                {assign var="hide_profile_name" value=false}
            {else}
                {assign var="hide_profile_name" value=true}
            {/if}

            {if $edit}
                {include file="views/profiles/components/multiple_profiles.tpl" show_text=true hide_profile_name=$hide_profile_name hide_profile_delete=true profile_id=$cart.profile_id create_href="checkout.checkout?edit_step=step_two&from_step=$edit_step&profile=new"}
            {/if}

            {if $checkout_address_billing_first}
                {assign var="first_section" value="B"}
                {assign var="first_section_text" value=__("billing_address")}
                {assign var="sec_section" value="S"}
                {assign var="sec_section_text" value=__("shipping_address")}
                {assign var="ship_to_another_text" value=__("text_ship_to_billing")}
                {assign var="body_id" value="sa"}
            {else}
                {assign var="first_section" value="S"}
                {assign var="first_section_text" value=__("shipping_address")}
                {assign var="sec_section" value="B"}
                {assign var="sec_section_text" value=__("billing_address")}
                {assign var="ship_to_another_text" value=__("text_billing_same_with_shipping")}
                {assign var="body_id" value="ba"}
            {/if}

            {if $edit}
                {if $profile_fields[$first_section]}
                    <div class="clearfix" data-ct-address="billing-address">
                        <div class="checkout-inside-block">
                            {include file="views/profiles/components/profile_fields.tpl" section=$first_section body_id="" ship_to_another=false hide_title=true title=$first_section_text display_explanations=true}
                        </div>
                    </div>
                {/if}

                {if $profile_fields['C']}
                    <div class="clearfix" data-ct-address="billing-address">
                        <div class="checkout-inside-block">
                            {include file="views/profiles/components/profile_fields.tpl" section='C' body_id="" ship_to_another=false hide_title=true title=$first_section_text}
                        </div>
                    </div>
                {/if}
            {if $cart.shipping_required !== false}
                {if $profile_fields[$sec_section]}
                    <div class="clearfix" data-ct-address="shipping-address">
                        {include file="views/profiles/components/profile_fields.tpl" section=$sec_section body_id=$body_id address_flag=$profile_fields|fn_compare_shipping_billing ship_to_another=$cart.ship_to_another title=$sec_section_text grid_wrap="checkout-inside-block" display_explanations=true}
                    </div>
                    <hr/>
                {/if}
            <div class="clearfix"></div>
            {include file="common/cards/delivery.tpl" capture=true}

            {/if}
            </div>


            <div class="col-xs-12">
                {include file="buttons/button.tpl" but_class="btn-primary" but_id="main_submit_button" but_name="dispatch[checkout.update_steps`$_action`]" but_text=__("w_proceed_to_secured_payment") but_meta="test-next-step"}
            </div>
            {/if}
        </form>
        {$smarty.capture.edit_shipping_form nofilter}
    </div>
    </fieldset>
    <!--step_two--></div>
