{capture name="tabsbox"}
<div id="content_c2c_products_manage">

    {nocache}
        <a href="{route_create_product}" class="btn btn-primary">{__("w_c2c_create_new_product")}</a>
    {/nocache}
    <hr/>
    <table class="table table-condensed">
        <thead>
            <tr>
                <th>{'product_name'|__}</th>
                <th>{'price'|__}</th>
                <th>{'amount'|__}</th>
                <th>{'status'|__}</th>
                <th>{'w_c2c_products_manage_annonce_header'|__}</th>
                <th>{'delete'|__}</th>
            </tr>
        </thead>
        <tbody>
        {foreach from=$products item="product"}
            <tr>
                <td><a href="{url route="product_update"}?product_id={$product.product_id}">{$product.product|truncate:50 nofilter}</a></td>
                <td>{include file="common/price.tpl" value=$product.price}</td>
                <td>{$product.amount}</td>
                <td>
                    {if $product.approved == Premoderation::STATUS_APPROVED}
                        {__("approved")}
                    {elseif $product.approved == Premoderation::STATUS_PENDING}
                        {__("pending")}
                    {elseif $product.approved == Premoderation::STATUS_STANDBY}
                        {__("w_premoderation_standby")}
                    {else}
                        {__("disapproved")}
                    {/if}
                </td>
                <td>
                    {if $product.approved == Premoderation::STATUS_APPROVED}
                        <a href="{"products.view&product_id=`$product.product_id`"|fn_url}">{__("w_view_annonce_manage_link")}</a>
                    {elseif $product.approved == Premoderation::STATUS_PENDING}
                        {__("pending")}
                    {elseif $product.approved == Premoderation::STATUS_STANDBY}
                        {__("w_premoderation_standby")}
                    {else}
                        {__("disapproved")}
                    {/if}
                </td>
                <td>
                {include  file="buttons/button.tpl"  but_meta="cm-confirm" but_role="trash" but_href="c2c_products.delete&product_id=`$product.product_id`"}
                </td>
            </tr>
        {foreachelse}
            <tr>
                <td colspan="5"><p class="no-items">{__("w_c2c_text_no_products")}</p></td>
            </tr>
        {/foreach}
        </tbody>
    </table>
<!--content_c2c_products_manage--></div>
{/capture}
{include file="common/c2c/components/menu.tpl" content=$smarty.capture.tabsbox active_tab=$smarty.request.selected_section|default:'c2c_products_manage'}
