{assign var="result_ids" value="cart_items,checkout_totals,checkout_steps,cart_status*,checkout_cart,"}

<form name="checkout_form" class="cm-check-changes cm-ajax cm-ajax-full-render cart-content-form" action="{""|fn_url}" method="post" enctype="multipart/form-data">
<input type="hidden" name="redirect_mode" value="cart" />
<input type="hidden" name="result_ids" value="{$result_ids}" />
<input type="submit" name="dispatch[checkout.update]" class="hidden" />
    {if $payment_methods}
        <div class="pull-right">{include file="buttons/proceed_to_checkout.tpl" but_href=$link_href}</div>
    {/if}

{include file="views/checkout/components/cart_items.tpl"}

</form>

{include file="views/checkout/components/checkout_totals.tpl"}


<div class="pull-right top-marged bottom-marged" >
    <div id="promotion_coupons" class="inline-block middle">
        {include file="views/checkout/components/promotion_coupon.tpl"}
    <!--promotion_coupons--></div>
    {if $payment_methods}
        <div class="inline-block middle">
            {include file="buttons/proceed_to_checkout.tpl" but_href=$link_href}
        </div>
    {/if}
</div>