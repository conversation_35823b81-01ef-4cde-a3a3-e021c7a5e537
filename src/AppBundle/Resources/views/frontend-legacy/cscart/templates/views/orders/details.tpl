<div class="orders">

{if $order_info}
    <div class="modal-header">
        <h2>{__('w_title_popin_order_detail',['[order_id]' => $order_info.order_id])}</h2>
    </div>

{capture name="order_actions"}
{/capture}
    <div class="modal-body">
        <div class="row">
            <div class="col-xs-12">
                <div class="table-responsive membre-tableau">

                    <table class="table">
                        <thead>
                        <tr>
                            <th>{__("w_order_number")}</th>
                            <th>{__("date")}</th>
                            <th>{__("vendor")}</th>
                            <th>{__("total")}</th>
                            <th>{__("status")}</th>
                            <th colspan="4">&nbsp;</th>
                        </tr>
                        </thead>
                        <tr {cycle values=",class=\"table-row\""}>
                            <td>{$order_info.order_id}</td>
                            <td>{$order_info.timestamp|date_format:"`$settings.Appearance.date_format`"}</td>
                            <td>{$order_info.company_id|fn_get_company_name}</td>
                            <td>{include file="common/price.tpl" value=$order_info.total}</td>
                            <td>{include file="common/status.tpl" status=$order_info.status display="view"}</td>
                            {if $order_info.allow_return}
                                <td><a
                                            href="{"rma.create_return?order_id=`$order_info.order_id`"|fn_url}"
                                            data-remote="{"rma.create_return?order_id=`$order_info.order_id`"|fn_url} .mainbox-body"
                                            data-dismiss="modal"
                                            data-toggle="modal"
                                            data-target="#generic_modal"
                                    >{__("return_registration")}</a></td>
                            {else}
                                <td></td>
                            {/if}
                            <td></td>
                            {if !$hide_invoice}
                                {if $order_info.do_not_create_invoice}
                                    {assign var="print_order" value=__("print_order_overview")}
                                    {assign var="print_pdf_order" value=__("print_pdf_order_overview")}
                                {else}
                                    {assign var="print_order" value=__("print_invoice")}
                                    {assign var="print_pdf_order" value=__("print_pdf_invoice")}
                                {/if}
                                <td>{include file="buttons/button.tpl" but_role="text" but_text=$print_order but_href="orders.print_invoice?order_id=`$order_info.order_id`" but_meta="cm-new-window"}</a></td>
                                <td>{include file="buttons/button.tpl" but_role="text" but_text=$print_pdf_order but_href="orders.print_invoice?order_id=`$order_info.order_id`&format=pdf"}</a></td>
                            {else}
                                <td colspan="2"></td>
                            {/if}
                        </tr>

                    </table>
                </div>

                <div class="table-responsive membre-tableau">
                    <table class="table">
                        <thead>
                        <tr>
                            <th colspan="2" style="width: 30%">{__("product")}</th>
                            <th style="width: 15%">{__("options")}</th>
                            <th style="width: 10%">{__("amount")}</th>
                            <th style="width: 45%">{__("price")}</th>
                        </tr>
                        </thead>
                        {foreach from=$order_info.products item="product" key="key"}
                            <tr {cycle values=",class=\"table-row\""}>
                                {assign var='product_current_data' value=$product.product_id|fn_get_product_data:$smarty.session.auth}
                                <td style="width: 15%" class="middle">{include file="common/image.tpl" images=$product_current_data.main_pair image_width=80 image_height=80}</td>
                                <td style="width: 15%" class="middle">
                                    {$product_current_data.product_features|fn_product_get_brand}<br />
                                    {$product.product}
                                </td>
                                <td style="width: 15%" class="middle">
                                    {if $product.product_options}
                                        <ul class="product-options-summary">
                                            {foreach from=$product.product_options item="option"}
                                                <li>{$option.variant_name}</li>
                                            {/foreach}
                                        </ul>
                                    {/if}
                                </td>
                                <td style="width: 10%" class="middle">{$product.amount}</td>
                                <td style="width: 45%" class="middle"><span class="w-price small-price">{include file="common/price.tpl" value=$product.display_subtotal}</span> <br /> {$product.amount} x {include file="common/price.tpl" value=$product.price}</td>
                            </tr>
                        {/foreach}
                    </table>
                </div>
                {if $edp_products}
                    <div class="table-responsive membre-tableau">
                        <table class="table">
                            <thead>
                            <tr>
                                <th style="width: 25%">{__("filename")}</th>
                                <th style="width: 50%">{__("download")}</th>
                                <th style="width: 25%">{__("size")}</th>
                            </tr>
                            </thead>
                            <tbody>
                            {foreach from=$edp_products item="edp_product"}
                                {foreach from=$edp_product.files_tree.files item="file"}
                                    {include file="views/products/components/file_tree.tpl" product_file=$file}
                                {/foreach}
                            {/foreach}
                            </tbody>
                        </table>
                    </div>
                {/if}

                <div class="row">
                    {if $order_info.shipping}
                    <div class="col-sm-offset-1 col-sm-11">
                        {__("shipment")} : {$order_info.shipping[0].shipping}, {include file="common/price.tpl" value=$order_info.shipping_cost}
                        {if $order_require_code && $order_info.code}
                            <p>{__('w_customer_code_explanation')}</p>
                            {include file="common/code_table.tpl" code=$order_info.code->full()}
                        {/if}
                    </div>
                    {/if}

                    {if !Shipping::isHandDelivery($order_info.shipping_ids)}
                        <div class="col-sm-offset-1 col-sm-4">
                            <b>{__("billing_address")}</b>
                            <div class="card">
                                <ul>
                                    <li>{$order_info.b_lastname} {$order_info.b_firstname}</li>
                                    <li>{$order_info.b_address}</li>
                                    <li>{$order_info.b_address_2}</li>
                                    <li>{$order_info.b_zipcode} {$order_info.b_city}</li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-sm-4 col-sm-offset-1">
                            <b>{__("shipping_address")}</b>
                            <div class="card">
                                <ul>
                                    <li>{$order_info.s_lastname} {$order_info.s_firstname}</li>
                                    <li>{$order_info.s_address}</li>
                                    <li>{$order_info.s_address_2}</li>
                                    <li>{$order_info.s_zipcode} {$order_info.s_city}</li>
                                </ul>
                            </div>
                        </div>
                    {/if}
                </div>
            </div>
        </div>
    </div>


{/if}
</div>




