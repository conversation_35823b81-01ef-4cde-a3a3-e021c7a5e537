<form action="{path route="product_report"}" method="post">
    <input name="product_id" type="hidden" value="{$product_id}" />
    {if !$auth.user_id}
        <div class="control-group">
            <label class="required cm-required" for="name" ><strong>{__("name")}</strong></label>
            <input id="name" name="name" type="text" style="width:450px;" class="input-text form-control" />
        </div>
        <div class="control-group">
            <label class="required cm-required" for="first_name" ><strong>{__("first_name")}</strong></label>
            <input id="first_name" name="first_name" type="text" style="width:450px;" class="input-text  form-control" />
        </div>
        <div class="control-group">
            <label class="required cm-required" for="email" ><strong>{__("email")}</strong></label>
            <input id="email" name="email" type="email" style="width:450px;" class="input-text  form-control" />
        </div>
    {/if}
    <div class="control-group">
        <label class="required cm-required" for="message_content" ><strong>{__("report_content_message")}</strong></label>
        <textarea id="message_content" name="message" cols="25" rows="7" style="width:450px;" class="input-textarea-long  form-control"></textarea>
    </div>
    {if !$auth.user_id}
        <div class="bottom-marged">{include file="blocks/recaptcha.tpl"}</div>
    {/if}

    <div class="center">
        <div id="send_button_message">
            {include file="buttons/button.tpl" but_text=__("send") but_color="orange" but_name="submit" but_meta="inline-block middle cm-process-items"}
            <!--send_button_message--></div>
    </div>
</form>
