{strip}
<div class="product-notification-body">

{if $added_products}
        {foreach from=$added_products item=product key="key"}
                <div class="col-sm-3 col-xs-12">
                    {include file="common/image.tpl" image_width="50" image_height="50" images=$product.main_pair no_ids=true class="product-notification-image"}
                </div>
                <div class="col-sm-5 col-xs-12 product-description">
                    <a href="{"products.view?product_id=`$product.product_id`"|fn_url}" >{$product.product_id|fn_get_product_name nofilter}</a>
                </div>
                <div class="col-sm-2 col-xs-12 product-quantity">
                    {if $product.product_option_data}
                        {include file="common/options_info.tpl" product_options=$product.product_option_data}
                    {/if}
                </div>
                <div class="col-sm-2 col-xs-12">
                    {$product.amount} x {include file="common/price.tpl" value=$product.display_price span_id="price_`$key`" class="none"}
                </div>
            </tr>
        {/foreach}
    {else}
        {$empty_text}
    {/if}

    <div class="col-xs-12 product-total">
        <p class="popup-total-articles ">{__("items_in_cart", [$smarty.session.cart.amount])}</p>
        <p class="popup-total-price">{__("cart_subtotal")} {include file="common/price.tpl" value=$smarty.session.cart.display_subtotal}</p>
    </div>
</div>
<div class="product-notification-buttons clearfix">

<div class="col-xs-12 clearfix">
            <button type="button" class="btn-third popup-continuer"  data-dismiss="modal">{__("continue_shopping")}</button>
            <a href="{fn_url('checkout.cart')}" class=" btn-primary popup-total-valider">{__("w_validate_command")}</a>
        </div>
</div>

{/strip}
