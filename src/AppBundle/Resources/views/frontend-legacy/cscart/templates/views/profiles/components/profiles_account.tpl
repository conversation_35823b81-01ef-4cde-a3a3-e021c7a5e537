{if $settings.General.use_email_as_login != "Y"}
    <div class="row">
        <div class="col-sm-8 col-xs-10">
            <div class="form-group">
                <label for="user_login_profile" class="cm-required cm-trim">{__("username")}</label>
                <input id="user_login_profile" type="text" name="user_data[user_login]" size="32" maxlength="32" value="{$user_data.user_login}" class="input-text" />
            </div>
        </div>
    </div>
{/if}


{if $settings.General.use_email_as_login == "Y" || $nothing_extra || $runtime.checkout}
    <div class="row">
        <div class="col-sm-8 col-xs-10">
            <div class="form-group">
                <label for="email" class="cm-required required cm-trim">{__("email")}</label>
                <input type="text" id="email" name="user_data[email]" size="32" maxlength="128"
                       value="{$user_data.email}" class="input-text form-control" tabindex="10"
                       data-validation="email"
                       data-validation-error-msg="{__('error_validator_email')}"/>
            </div>
        </div>
    </div>
{/if}
    <div class="row">
        <div class="col-sm-8 col-xs-10">
            <div class="form-group">
                <label for="password_{$block.block_id}" class="cm-required required">{__("password")}</label>

                <div class="input-group">
                    <input type="password" id="password_{$block.block_id}" name="user_data[password1]" size="24"
                           maxlength="24" class="cm-autocomplete-off form-control" tabindex="11"/>

                    <div class="input-group-addon">
                        <label for="cbForcePasswordDisplay_{$block.block_id}"><input type="checkbox"
                                                                                     id="cbForcePasswordDisplay_{$block.block_id}"
                                                                                     data-ca-target="password_{$block.block_id}"
                                                                                     class="cm-toggle-password"
                                                                                     tabindex="12"/>
                            {__("w_see")}</label>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label>
            <input type="checkbox" required="required" name="terms_approved" value="1" />
            {__('register_accept_terms', ['[url]' => 'pages.view?page_id=12'|fn_url]) nofilter}
        </label>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <div class="form-group">
                <div class="bottom-marged">{include file="blocks/recaptcha.tpl"}</div>
            </div>
        </div>
    </div>
