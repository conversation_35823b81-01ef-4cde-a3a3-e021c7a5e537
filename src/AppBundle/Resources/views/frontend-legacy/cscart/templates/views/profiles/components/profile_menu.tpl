{**
    active_tab  : Id of the current tab
    content     : Html content in tab
*}
{if $active_tab}
{$schema = [
    'my_account' => [
        'general'       => [
            'title' => 'profile',
            'href'  => 'profiles.update',
            'extern'=> false
        ],
        'addresses'     => [
            'title' => 'addresses',
            'href'  => 'profiles.update',
            'extern'=> false
        ],
        'newsletters'   => [
            'title' => 'newsletter',
            'href'  => 'profiles.update',
            'extern'=> false
        ]
    ],
    'w_my_purchases'  => [
        'orders'        => [
            'title' => 'orders',
            'href'  => 'orders.search',
            'extern'=> true
        ],
        'returns'       => [
            'title' => 'returns',
            'href'  => 'rma.returns',
            'extern'=> true
        ],
        'w_post_sales_support'   => [
            'title' => 'w_post_sales_support',
            'href'  => 'rma.returns',
            'extern'=> true
        ],
        'w_litigation'   => [
            'title' => 'w_litigation',
            'href'  => 'rma.returns',
            'only_c2c' => true,
            'extern'=> true
            ]
    ],
    'discuss_menu' => [
        'messages' => [
            'title' => 'discuss_my_messages',
            'href'  => 'discuss.list',
            'extern'=> true
        ]
    ]
]}
{*Build the menu, and retrieve current section*}
<div class="row profile-menu">
    <div class="col-sm-3 col-xs-12">
        <div class="menu-membre">
            <div id="dynamic-left-menu" class="univ-default">
                {foreach from=$schema item='section' key='section_id'}
                    <h2 class="sidebox-parent-cat ">{__($section_id)}</h2>
                    <ul class="sidebox-wrapped">
                        {foreach from=$section item='tab' key='tab_id'}
                            {if $tab['only_c2c']}
                                {feature_flag feature="enable_c2c"}
                                    <li><a href="{"`$tab.href`?selected_section=`$tab_id`"|fn_url}">{__($tab.title)}</a></li>
                                    {if $tab_id==$active_tab}
                                        {$active_section = $section}
                                    {/if}
                                {/feature_flag}

                            {else}
                                <li><a href="{"`$tab.href`?selected_section=`$tab_id`"|fn_url}">{__($tab.title)}</a></li>
                                {if $tab_id==$active_tab}
                                    {$active_section = $section}
                                {/if}
                            {/if}
                        {/foreach}
                    </ul>
                {/foreach}
            </div>
        </div>
        {feature_flag feature="enable_c2c"}
            <div class="achat-vente-back">
                {if $auth.user_type == User::VENDOR_TYPE}
                    <a href="{fn_url('',User::VENDOR_TYPE)}" target="_blank">{__('w_my_vendor_account')}</a>
                {else}
                    <a href="{fn_url("c2c_orders.manage?selected_section=c2c_orders_manage")}">{__("w_goto_c2c_orders")}</a>
                {/if}
            </div>
        {/feature_flag}

    </div>
    <div class="col-sm-9 col-xs-12">
        {*Build Tabs*}
        {$navigation.tabs = []}
        {foreach from=$active_section item='tab' key='tab_id'}
            {if $active_section.$active_tab.href != $tab.href}
                {$navigation.tabs[$tab_id] = [title=>$tab.title|__, ajax=>true, href=>$tab.href, only_c2c=>$tab.only_c2c]}
            {else}
                {$navigation.tabs[$tab_id] = [title=>$tab.title|__, js=>true, only_c2c=>$tab.only_c2c]}
            {/if}
        {/foreach}
        {include file="common/tabsbox.tpl" track=true additional_class="no-margin"}
    </div>
</div>
{/if}
