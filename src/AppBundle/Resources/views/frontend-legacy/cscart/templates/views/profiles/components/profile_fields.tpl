{* Variables for customizing the template, with defaults for BC *}
{if !isset($use_labels)} {assign var="use_labels" value=true} {/if}
{if !isset($use_placeholders)} {assign var="use_placeholders" value=false} {/if}
{if !isset($use_required)} {assign var="use_required" value=false} {/if}
{if !isset($field_col_class)} {assign var="field_col_class" value="col-sm-6"} {/if}
{if !isset($field_label_col_class)} {assign var="field_label_col_class" value="col-sm-3"} {/if}

{if $show_email}
    <div class="form-group">
        <label for="{$id_prefix}elm_email" class="required cm-required">{__("email")}<i>*</i></label>
        <input type="email" id="{$id_prefix}elm_email" data-required="true" name="user_data[email]" size="32" value="{$user_data.email}" class="input-text {$_class}" {$disabled_param} />
    </div>
{else}

{if $profile_fields.$section}

{if $address_flag}
    <script type="text/javascript">
        $(function () {
            $("#shippingCard").hide();
            $('input[name=ship_to_another]').change(function () {
                if (1 == $(this).val()) {
                    $("div[data-ct-address=billing-address]").slideUp();
                    $("#sa").slideDown().find('input,select').each(function () {
                        $(this).prop('disabled', false).removeClass('disabled');
                    });
                    feedShippingCard();
                    setStepTitle($(this).attr('w-title'));
                } else {
                    $("div[data-ct-address=billing-address]").slideDown();
                    $("#sa").slideUp().find('input,select').each(function () {
                        $(this).prop('disabled', true).addClass('disabled');
                    });
                    $("#shippingCard").hide();
                    setStepTitle($(this).attr('w-title'));
                }
            });

            $('#shippingCard a.w-button').click(function () {
                $("div[data-ct-address=billing-address]").slideDown();
                $("#shippingCard").hide();
            });

            function feedShippingCard() {
                $("#shippingCard").fadeIn().slideDown();
                var lastname = $('input[x-autocompletetype=surname]').val();
                var firstname = $('input[x-autocompletetype=given-name]').val();
                var address1 = $('input[x-autocompletetype=street-address]').val();
                var address2 = $('input[x-autocompletetype=address-line2]').val();
                var city = $('input[x-autocompletetype=city]').val();
                var postalCode = $('input[x-autocompletetype=postal-code]').val();
                $("#addressSummary").html(firstname + ' ' + lastname + '<br/>' + address1 + '<br/>' + address2 + '<br/>' + postalCode + ' ' + city);
            }

            function setStepTitle(title) {
                $('#step-title').html(title);
            }
        });
    </script>
    <div class="address-switch clearfix radio" id="checkout_address_switch">
        {if $display_s_address !== false}
            <label>
                <input class="radio" w-title="{{__("billing_address")|mb_strtoupper}}" type="radio" name="ship_to_another"
                   value="0" id="sw_{$body_id}_suffix_yes" {if !$ship_to_another}checked="checked"{/if} />
                {__("w_deliver_same_address")}
            </label>
            <label>
                <input class="radio" w-title="{{__("shipping_address")|mb_strtoupper}}" type="radio" name="ship_to_another"
                    value="1" id="sw_{$body_id}_suffix_no" {if $ship_to_another}checked="checked"{/if} />
                {__("w_deliver_other_address")}
            </label>
        {else}
            <input type="hidden" name="ship_to_another" value="0" />
        {/if}
    <!--checkout_address_switch--></div>
{/if}

{if ($address_flag && !$ship_to_another && ($section == "S" || $section == "B")) || $disabled_by_default}
    {assign var="disabled_param" value="disabled=\"disabled\""}
    {assign var="_class" value="disabled"}
    {assign var="hide_fields" value=true}
{else}
    {assign var="disabled_param" value=""}
    {assign var="_class" value=""}
{/if}

<div class="clearfix">
{if $body_id || $grid_wrap}
    <div id="{$body_id}" {if $hide_fields}style="display:none;"{/if}>
        <div class="{$grid_wrap}">
{/if}

            {if !$nothing_extra && !$hide_title}
                {include file="common/subheader.tpl" title=$title}
{/if}

{foreach from=$profile_fields.$section item=field}

{if $field.field_name}
    {assign var="data_name" value="user_data"}
    {assign var="data_id" value=$field.field_name}
    {assign var="value" value=$user_data.$data_id}
{else}
    {assign var="data_name" value="user_data[fields]"}
    {assign var="data_id" value=$field.field_id}
    {assign var="value" value=$user_data.fields.$data_id}
{/if}

{assign var="skip_field" value=false}
{if $section == "S" || $section == "B"}
    {if $section == "S"}
        {assign var="_to" value="B"}
    {else}
        {assign var="_to" value="S"}
    {/if}
    {if !$profile_fields.$_to[$field.matching_id]}
        {assign var="skip_field" value=true}
    {/if}
{/if}

<div class="form-group profile-field-wrap {$field.class} {if $field.required == "Y"}profile-field-required{/if}">
    {if $use_labels}
        {if $pref_field_name != $field.description || $field.required == "Y"}
            <label for="{$id_prefix}elm_{$field.field_id}"
                   class="{$field_label_col_class} control-label text-right {$additionnal_label_class|default:'w-label'} cm-profile-field {if $field.required == "Y"}required cm-required{/if}{if $field.field_type == "P"} cm-phone{/if}{if $field.field_type == "Z"} cm-zipcode{/if}{if $field.field_type == "Z"}{if $section == "S"}cm-location-shipping{else}cm-location-billing{/if}{/if}">{$field.description}</label>
        {/if}
    {/if}

    <div class="{$field_col_class}">
    {if $field.field_type == "A"}  {* State selectbox *}
        {$_country = $settings.General.default_country}
        {$_state = $value|default:$settings.General.default_state}

        <select {if $field.autocomplete_type}x-autocompletetype="{$field.autocomplete_type}"{/if} id="{$id_prefix}elm_{$field.field_id}" class="cm-state {if $section == "S"}cm-location-shipping{else}cm-location-billing{/if} {if !$skip_field}{$_class}{/if}" name="{$data_name}[{$data_id}]" {if !$skip_field}{$disabled_param}{/if} {if ($use_required && $field.required == "Y")}required="required"{/if} {if $field.required == "Y"}data-required="true" {/if}>
            <option value="">- {__("select_state")} -</option>
            {if $states && $states.$_country}
                {foreach from=$states.$_country item=state}
                    <option {if $_state == $state.code}selected="selected"{/if} value="{$state.code}">{$state.state}</option>
                {/foreach}
            {/if}
        </select><input {if $field.autocomplete_type}x-autocompletetype="{$field.autocomplete_type}"{/if} type="text" id="elm_{$field.field_id}_d" name="{$data_name}[{$data_id}]" size="32" maxlength="64" value="{$_state}" disabled="disabled" class="cm-state {if $section == "S"}cm-location-shipping{else}cm-location-billing{/if} input-text hidden {if $_class}disabled{/if}"/>

    {elseif $field.field_type == "O"}  {* Countries selectbox *}
        {assign var="_country" value=$value|default:$settings.General.default_country}
        <select {if $field.autocomplete_type}x-autocompletetype="{$field.autocomplete_type}"{/if} id="{$id_prefix}elm_{$field.field_id}" class="form-control cm-country {if $section == "S"}cm-location-shipping{else}cm-location-billing{/if} {if !$skip_field}{$_class}{else}cm-skip-avail-switch{/if}" name="{$data_name}[{$data_id}]" {if !$skip_field}{$disabled_param}{/if} {if ($use_required && $field.required == "Y")}required="required"{/if} {if $field.required == "Y"}data-required="true" {/if}>
            <option value="">- {__("select_country")} -</option>
            {foreach from=$countries item="country" key="code"}
            <option {if $_country == $code}selected="selected"{/if} value="{$code}">{$country}</option>
            {/foreach}
        </select>

    {elseif $field.field_type == "C"}  {* Checkbox *}
        <input type="hidden" name="{$data_name}[{$data_id}]" value="N" {if !$skip_field}{$disabled_param}{/if} />
        <input type="checkbox" id="{$id_prefix}elm_{$field.field_id}" name="{$data_name}[{$data_id}]" value="Y" {if $value == "Y"}checked="checked"{/if} class="checkbox {if !$skip_field}{$_class}{else}cm-skip-avail-switch{/if}" {if !$skip_field}{$disabled_param}{/if} />

    {elseif $field.field_type == "T"}  {* Textarea *}
        <textarea {if $field.autocomplete_type}x-autocompletetype="{$field.autocomplete_type}"{/if} class="input-textarea {if !$skip_field}{$_class}{else}cm-skip-avail-switch{/if}" id="{$id_prefix}elm_{$field.field_id}" name="{$data_name}[{$data_id}]" cols="32" rows="3" {if !$skip_field}{$disabled_param}{/if}>{$value}</textarea>

    {elseif $field.field_type == "D"}  {* Date *}
        {if !$skip_field}
            {include file="common/calendar.tpl" date_id="`$id_prefix`elm_`$field.field_id`" date_name="`$data_name`[`$data_id`]" date_val=$value start_year="1902" end_year="0" extra=$disabled_param}
        {else}
            {include file="common/calendar.tpl" date_id="`$id_prefix`elm_`$field.field_id`" date_name="`$data_name`[`$data_id`]" date_val=$value start_year="1902" end_year="0"}
        {/if}

    {elseif $field.field_type == "S"}  {* Selectbox *}
        <select {if $field.autocomplete_type}x-autocompletetype="{$field.autocomplete_type}"{/if} id="{$id_prefix}elm_{$field.field_id}" class="{if !$skip_field}{$_class}{else}cm-skip-avail-switch{/if}" name="{$data_name}[{$data_id}]" {if !$skip_field}{$disabled_param}{/if} {if ($use_required && $field.required == "Y")}required="required"{/if} {if $field.required == "Y"}data-required="true" {/if}>
            {if $field.required != "Y"}
            <option value="">--</option>
            {/if}
            {foreach from=$field.values key=k item=v}
            <option {if $value == $k}selected="selected"{/if} value="{$k}">{$v}</option>
            {/foreach}
        </select>

    {elseif $field.field_type == "R"}  {* Radiogroup *}
        <div id="{$id_prefix}elm_{$field.field_id}" class="radio">
            {foreach from=$field.values key=k item=v name="rfe"}
            <label>
            <input class="radio valign {if !$skip_field}{$_class}{else}cm-skip-avail-switch{/if} {$id_prefix}elm_{$field.field_id}" type="radio" id="{$id_prefix}elm_{$field.field_id}_{$k}" name="{$data_name}[{$data_id}]" value="{$k}" {if $value == $k}checked="checked"{/if} {if !$skip_field}{$disabled_param}{/if} />{$v}</label>
            {/foreach}
        </div>

    {elseif $field.field_type == "N"}  {* Address type *}
        <input class="radio valign {if !$skip_field}{$_class}{else}cm-skip-avail-switch{/if} {$id_prefix}elm_{$field.field_id}" type="radio" id="{$id_prefix}elm_{$field.field_id}_residential" name="{$data_name}[{$data_id}]" value="residential" {if !$value || $value == "residential"}checked="checked"{/if} {if !$skip_field}{$disabled_param}{/if} /><span class="radio">{__("address_residential")}</span>
        <input class="radio valign {if !$skip_field}{$_class}{else}cm-skip-avail-switch{/if} {$id_prefix}elm_{$field.field_id}" type="radio" id="{$id_prefix}elm_{$field.field_id}_commercial" name="{$data_name}[{$data_id}]" value="commercial" {if $value == "commercial"}checked="checked"{/if} {if !$skip_field}{$disabled_param}{/if} /><span class="radio">{__("address_commercial")}</span>

    {elseif $field.field_type == "E"}  {* Simple input *}
        <input {if $field.autocomplete_type}x-autocompletetype="{$field.autocomplete_type}"{/if} type="email" id="{$id_prefix}elm_{$field.field_id}" name="{$data_name}[{$data_id}]" size="32" value="{$value}" class="form-control input-text {if !$skip_field}{$_class}{else}cm-skip-avail-switch{/if}" {if !$skip_field}{$disabled_param}{/if} {if $use_placeholders}placeholder="{__($field.description)}"{/if} {if ($use_required && $field.required == "Y")}required="required"{/if} {if $field.required == "Y"}data-required="true" {/if} />
    {else}  {* Simple input *}
        <input {if $field.autocomplete_type}x-autocompletetype="{$field.autocomplete_type}"{/if} type="text" id="{$id_prefix}elm_{$field.field_id}" name="{$data_name}[{$data_id}]" size="32" value="{$value}" class="form-control input-text {if !$skip_field}{$_class}{else}cm-skip-avail-switch{/if}" {if !$skip_field}{$disabled_param}{/if} {if $use_placeholders}placeholder="{__($field.description)}"{/if} {if ($use_required && $field.required == "Y")}required="required"{/if} {if $field.required == "Y"}data-required="true" {/if} />
    {/if}

    {if $display_explanations}
        {if $field.class == 'billing-phone' || $field.class == 'shipping-phone'}
            <div class="smalltext inline-block middle">{'w_phone_mandatory_explanation'|__ nofilter}</div>
        {/if}
    {/if}
    {assign var="pref_field_name" value=$field.description}
    </div>
</div>
{/foreach}

{if $body_id || $grid_wrap}
        </div>
    </div>
{/if}
</div>

{/if}
{/if}
