{assign var="id" value=$id|default:"main_login"}

{capture name="login"}
<form class="{if $ajax}cm-ajax cm-reload-form cm-ajax-full-render{/if}" name="{$id}_form" action="{""|fn_url}" method="post">
{if !$return_url}
    {$return_url = $smarty.request.return_url|default:$config.current_url}
{/if}
<input type="hidden" name="return_url" value="{$return_url}" />
<input type="hidden" name="redirect_url" value="{$config.current_url}" />
    {if $ajax}
        <input type="hidden" name="result_ids" value="account_info_*,{$result_ids}" />
    {/if}
    {$login_title = __("returning_customer")}
    {if $custom_login_title}{$login_title=$custom_login_title}{/if}
    {$login_promo_text = ''}
    {if $custom_login_promo_text}{$login_promo_text=$custom_login_promo_text}{/if}


{if $style == "checkout"}
    {include file="common/subheader.tpl" title=$login_title}
        {$login_promo_text nofilter}
        {/if}
        <div class="row">
            <div class="col-sm-8 col-xs-10">
                <div class="form-group">
                    <label for="login_{$id}"
                           class="cm-required cm-trim">{if $settings.General.use_email_as_login == "Y"}{__("email")}{else}{__("username")}{/if}</label>
                    <input type="{if $settings.General.use_email_as_login == "Y"}email{else}text{/if}" id="login_{$id}" name="user_login" size="30" value="{$config.demo_username}"
                           class="form-control"/>
                </div>
            </div>
        </div>
        {if $style == 'ajax'}
        <div class="inline-block middle">
            {/if}
        <div class="row">
            <div class="col-sm-8 col-xs-10">
                <div class="form-group">
                    <label for="psw_{$id}" class="forgot-password-label cm-required">{__("password")}</label>
                    <input type="password" id="psw_{$id}" name="password"
                           size="{if $style != 'ajax'}30{else}15{/if}" value="{$config.demo_password}"
                           class="form-control" maxlength="32"/>
                    <a href="{path route='recover_password'}" class="forgot-password"
                       tabindex="5"><small>{__("forgot_password_question")}</small></a>
                </div>
            </div>
        </div>

    {if $style == 'ajax'}
        </div>
        <div class="inline-block middle">
    {/if}
        {if $style != "checkout"}
            <div class=" cm-cart-buttons{if $style == "popup"}buttons-container{/if}">
        {/if}
            <div class="body-bc clearfix">
                <input type="hidden" name="remember_me" value="1"/>

                <div class="row">
                    <div class="col-xs-12">
                        <div class="form-group">
                            {include file="buttons/login.tpl" but_name="dispatch[auth.login]" but_role="w-action" but_color="btn-primary" but_size="large"}
                        </div>
                    </div>
                </div>
            </div>
        {if $style != "checkout"}
            </div>
        {/if}
    {if $style == 'ajax'}
        </div>
    {/if}

</form>
{/capture}

{if $style == "popup"}
    {$smarty.capture.login nofilter}
{else}
    <div{if $style != "checkout"} class="{if $style != "popup" && $style != "ajax"}form-wrap{/if}"{/if}>
        {$smarty.capture.login nofilter}
    </div>

    {capture name="mainbox_title"}{__("sign_in")}{/capture}
{/if}
