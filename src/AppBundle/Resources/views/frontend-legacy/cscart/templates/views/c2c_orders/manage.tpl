{capture name="tabsbox"}
    <div id="content_c2c_orders_manage">
    <table class="table table-condensed">
        <thead>
        <tr>
            <th>{'products'|__}</th>
            <th>{'price'|__}</th>
            <th>{'date'|__}</th>
            <th>{'status'|__}</th>
            <th>{'action'|__}</th>
        </tr>
        </thead>
        <tbody>
        {foreach from=$orders item="order"}
            <tr>
                <td>
                    <ul>
                        {foreach from=$order.products item="product"}
                            <li>{$product.product}</li>
                        {/foreach}
                    </ul>
                </td>
                <td>{include file="common/price.tpl" value=$order.total}</td>
                <td>{$order.timestamp|date_format:"`$settings.Appearance.date_format`, `$settings.Appearance.time_format`"}</td>
                <td>{include file="common/status.tpl" status=$order.status display="view"}</td>
                <td>
                    {if $order.order_id|is_order_paid}
                        <a href="{"c2c_orders.update&order_id=`$order.order_id`"|fn_url}">{'w_see'|__}</a>
                    {/if}
                </td>
            </tr>
            {foreachelse}
            <tr>
                <td colspan="5"><p class="no-items">{"text_no_orders"|__}</p></td>
            </tr>
        {/foreach}
        </tbody>
    </table>
    <!--content_c2c_orders_manage--></div>
{/capture}
{include file="common/c2c/components/menu.tpl" content=$smarty.capture.tabsbox active_tab=$smarty.request.selected_section|default:'c2c_orders_manage'}
