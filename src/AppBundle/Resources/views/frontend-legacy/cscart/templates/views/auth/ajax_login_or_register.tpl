{assign var="rnd_value" value=uniqid()}
{assign var="random" value=$random|default:$rnd_value}
{assign var="login_form_id" value="my_account_login_form_message_`$random`"}
{assign var="register_form_id" value="my_account_creation_form_message_`$random`"}

<div id="{$login_form_id}">
    {include file="views/auth/login_form.tpl" style="ajax" ajax=$ajax result_ids=$result_ids}
    <div class="no-margin"><label for="link_register">{__('w_question_account')}</label>
        <a id="link_register"  class="cm-show" data-ca-hide-self=false
                data-ca-show-target="{$register_form_id}"
                data-ca-hide-target="{$login_form_id}">{__('register')}</a>
    </div>

</div>
<div id="{$register_form_id}" class="hidden">
    <form name="step_one_register_form" class="cm-ajax cm-ajax-full-render"
            action="{""|fn_url}" method="post">
        <input type="hidden" name="result_ids" value="account_info,{$result_ids}" />
        <input type="hidden" name="redirect_url" value="{$config.current_url}"/>

        <div class="inline-block bottom">
            {include file="views/profiles/components/profiles_account.tpl" nothing_extra="Y" location="checkout"}
            <div class="clear"></div>
        </div>
        <div class="inline-block bottom-marged align-right">
            {include file="buttons/button.tpl" but_name="dispatch[profiles.update]" but_role="w-action" but_color="orange" but_size="large" but_text=__("register")}
        </div>
    </form>
    <div>
        <label for="link_register">{__('w_existing_account')}</label>
        <a class="inline-block cm-show" data-ca-hide-self=false
                data-ca-show-target="{$login_form_id}"
                data-ca-hide-target="{$register_form_id}">{__('sign_in')}</a>
    </div>
</div>
