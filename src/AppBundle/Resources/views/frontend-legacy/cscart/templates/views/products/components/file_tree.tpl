<tr>
    <td style="width: 25%">
        <i class="icon-file"></i>
        {$product_file.file_name}
    </td>
    <td style="width: 50%">
        {if $product_file.ekey && ($product_file.activation_type !== "M" || $product_file.active == "Y") && $product_file.edp_info && (!$product_file.max_downloads || $product_file.downloads < $product_file.max_downloads)}<a href="{"orders.get_file?ekey=`$product_file.ekey`&product_id=`$product.product_id`&file_id=`$product_file.file_id`"|fn_url}" class="download-link cm-no-ajax">{__('download')}<i class="icon-download"></i></a>{/if}
        {if $product_file.activation_type == "M" && $product_file.active == "N"}<p>{__("notice")}: {__("not_active_file_notice")}</p>{elseif $product_file.max_downloads && $product_file.downloads >= $product_file.max_downloads}<p>{__("notice")}: {__("file_download_limit_exceeded", ["[limit]" => $product_file.max_downloads])}</p>{elseif !$product_file.edp_info}<p>{__("download_link_expired")}</p>{elseif !$product_file.ekey}<p>{if $product_file.activation_type == "P"}{__("file_avail_after_payment")}{else}{__("download_link_expired")}{/if}</p>{/if}
        {if $file.readme}
            <ul class="bullets-list">
                <li>
                    <a onclick="Tygh.$('#readme_{$file.file_id}').toggle(); return false;">{__("readme")}</a>
                    <div class="hidden" id="readme_{$file.file_id}">{$file.readme nofilter}</div>
                </li>
            </ul>
        {/if}
    </td>
    <td style="width: 25%">
        {$product_file.file_size|number_format:0:"":" "}&nbsp;{__("bytes")}
    </td>
</tr>
