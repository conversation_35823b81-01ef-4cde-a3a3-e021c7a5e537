{if $order.order_id|is_order_status_equal_to:OrderStatus::STANDBY_VENDOR}
    {capture name="status_change_action"}
        <div class="inline-block top">
            <a class="btn btn-primary" href="{fn_url("c2c_orders.accept?order_id=`$order.order_id`")}">{__("approve")}</a>
            <a class="btn cm-confirm" href="{fn_url("c2c_orders.refuse?order_id=`$order.order_id`")}">{__("decline")}</a>
        </div>
    {/capture}
    {capture name="explanation_text"}{__('w_c2c_orders_explanation_P')}{/capture}
{elseif $order.order_id|is_order_paid}
    {if !Shipping::isHandDelivery($order.shipping_ids)}
        {if $order.order_id|is_order_status_equal_to:OrderStatus::PROCESSING_SHIPPING}
            {capture name="explanation_text"}{__('w_c2c_orders_explanation_E')}{/capture}
        {else}
            {capture name="explanation_text"}{__('w_c2c_orders_explanation_generic')}{/capture}
        {/if}
    {/if}
    {capture name="status_change_action"}
        {if $order_require_code}
            {if $order.order_id|is_order_status_equal_to:OrderStatus::PROCESSING_SHIPPING}
                <p>{__('w_seller_code_explanation') nofilter}</p>
                {include file="common/code_table.tpl" code=$order.code->partial() display_form=true}
            {/if}
        {else}
            <div class="inline-block top">
                <div>{__('tracking_number')} : </div>
                <div>
                    {if !$order.shipment_datas.0.shipment_id }
                        <form action="{''|fn_url}" method="post">
                            {foreach from=$order.products item="product"}
                                <input type="hidden" name="shipment_data[products][{$product.item_id}]" value="{$product.amount}" />
                            {/foreach}
                            <input type="hidden" name="shipment_data[shipping_id]" value="{$order.shipping.0.shipping_id}" />
                            <input type="hidden" name="shipment_data[order_id]" value="{$order.order_id}" />
                            <input type="text" name="shipment_data[tracking_number]" placeholder="{__('tracking_number')}" />
                            {include file="buttons/save.tpl" but_name="dispatch[c2c_orders.update]"}
                        </form>
                    {else}
                        {$order.shipment_datas.0.tracking_number}
                    {/if}
                </div>
            </div>
        {/if}
    {/capture}
    {capture name="additionnal_info"}
        {if !Shipping::isHandDelivery($order.shipping_ids)}
        <tr>
            <td colspan="2">
                {include file="common/subheader.tpl" title=__('shipping_address')}
            </td>
        </tr>
        <tr>
            <td colspan="2">
                <ul>
                    <li>{$order.s_lastname} {$order.s_firstname}</li>
                    <li>{$order.s_address}</li>
                    <li>{$order.s_address_2}</li>
                    <li>{$order.s_zipcode} {$order.s_city}</li>
                </ul>
            </td>
        </tr>
        {/if}
    {/capture}
{/if}

{capture name="tabsbox"}
    <div id="content_c2c_orders_update">
        <table style="width:100%;" class="simple-table" >
            <tr>
                <td colspan="2">
                    {include file="common/subheader.tpl" title=__('block_checkout_description')}
                </td>
            </tr>
            <tr>
                <td style="width:20%">{__('w_order_num')} : </td>
                <td>{$order.order_id}</td>
            </tr>
            <tr>
                <td>{__('status')} :</td>
                <td>{include file="common/status.tpl" status=$order.status display="view"}</td>
            </tr>
            <tr>
                <td>{__('date')} : </td>
                <td>{$order.timestamp|date_format:"`$settings.Appearance.date_format`, `$settings.Appearance.time_format`"}</td>
            </tr>
            <tr>
                <td colspan="2">
                    {include file="common/subheader.tpl" title=__('shipping_method')}
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    {$order.shipping.0.shipping}
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    {include file="common/subheader.tpl" title=__('products') class="subheader top-marged"}
                    {$smarty.capture.explanation_text nofilter}
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <table class="table inline-block top" style="margin-top: 0; width:40%; margin-right:30px;">
                        <tr>
                            <th style="width:50%">{__('product')}</th>
                            <th style="width:25%">{__('amount')}</th>
                            <th style="width:25%">{__('price')}</th>
                        </tr>
                        {foreach from=$order.products item="product"}
                            <tr>
                                <td>{$product.product}</td>
                                <td>{$product.amount}</td>
                                <td>{include file="common/price.tpl" value=$product.price}</td>
                            </tr>
                        {/foreach}
                    </table>
                    {$smarty.capture.status_change_action nofilter}
                </td>
            </tr>
            {$smarty.capture.additionnal_info nofilter}
        </table>
        <!--content_c2c_orders_update--></div>
{/capture}
{include file="common/c2c/components/menu.tpl" content=$smarty.capture.tabsbox active_tab=$smarty.request.selected_section|default:'c2c_orders_manage'}
