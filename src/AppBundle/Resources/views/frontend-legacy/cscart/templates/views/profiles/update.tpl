{include file="views/profiles/components/profiles_scripts.tpl"}

    {capture name="tabsbox"}
        <script type="text/javascript">
            function togglePasswordBox(){
                var box = $('#password_box');
                box.toggle();
                var isVisible = box.is(':visible');
                $('input',box).attr('disabled',!isVisible?'disabled':false);
                if(isVisible){
                    $('label:not(.middle)',box).addClass('cm-required');
                }else{
                    $('label',box).removeClass('cm-required');
                }
            }
            {if $user_data.modify_password}
                $(function(){
                    $('#modify_password').click();
                });
            {/if}
        </script>
        <div class="row">
            <div class="col-sm-9">
        <form name="profile_form" action="{""|fn_url}" method="post" class="form-horizontal">
            <div id="content_general">
                    <input id="selected_section" type="hidden" value="general" name="selected_section"/>
                    <input id="default_card_id" type="hidden" value="" name="default_cc"/>
                    <input type="hidden" name="profile_id" value="{$user_data.profile_id}" />

                    {include file="views/profiles/components/profile_fields.tpl" section='C' body_id="profile_general" ship_to_another=true}

                    <div class="form-group">
                        <label for="pseudo" class="control-label col-sm-3">{__("pseudo")}</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" id="pseudo" name="user_data[additional_datas][pseudo]" value="{$user_data.additional_datas.pseudo}" {if !$can_edit_pseudo}disabled="disabled" {/if}/>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="col-sm-9 col-sm-offset-3">
                            <input type="hidden" name="user_data[additional_datas][settings_notifications]" value="0" />
                            <input type="checkbox" id="notifications" name="user_data[additional_datas][settings_notifications]" {if $user_data.additional_datas.settings_notifications}checked="checked" {/if}/>
                            <label for="notifications"  style="display:inline-block;">{__("settings_notifications")}</label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="email" class="w-label cm-required cm-trim control-label col-sm-3">{__("email")}</label>
                        <div class="col-sm-6">
                            <input type="email" id="email" name="user_data[email]" size="32" maxlength="128" value="{$user_data.email}" class="form-control" />
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-9 col-sm-offset-3">
                            <input type="checkbox" id="modify_password" class="cm-checkbox" name="user_data[modify_password]" onclick="togglePasswordBox()"/>
                            <label for="modify_password" style="display:inline-block;">{__("w_modify_password")}</label>
                        </div>
                    </div>

                    <div id="password_box" style="display:none;">
                        {if !$auth.w_recover_password}
                        <div class="form-group">
                            <label for="old_password" class="control-label col-sm-3 two-lines-label">{__("w_old_password")}</label>
                            <div class="col-sm-6">
                                <input type="password" id="old_password" name="user_data[old_password]" size="32" maxlength="32" class="form-control cm-autocomplete-off" disabled="disabled" value="{$user_data.old_password}"/>
                            </div>
                        </div>
                        {/if}
                        <div class="form-group">
                            <label for="password1" class="control-label col-sm-3 two-lines-label">{__("w_new_password")}</label>
                            <div class="col-sm-6">
                                <div class="input-group">
                                    <input type="password" id="password_{$block.block_id}" name="user_data[password1]" size="24" maxlength="24" disabled="disabled" class="cm-autocomplete-off form-control"/>
                                    <div class="input-group-addon">
                                        <input type="checkbox" id="cbForcePasswordDisplay_{$block.block_id}" data-ca-target="password_{$block.block_id}" class="inline-block middle cm-toggle-password"/>
                                        <label for="cbForcePasswordDisplay_{$block.block_id}" class="inline-block middle">{__("w_see")}</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {include file="buttons/save_modifications.tpl" but_name="dispatch[profiles.update]" but_class="btn-primary" but_id="save_profile_but"}
            <!--content_general--></div>
            <div id="content_addresses">

                        {if $profile_fields.B || $profile_fields.S}
                            {if $settings.General.user_multiple_profiles == "Y" && $runtime.mode == "update"}
                                <p>{__("text_multiprofile_notice")}</p>
                                {include file="views/profiles/components/multiple_profiles.tpl" profile_id=$user_data.profile_id}
                            {/if}

                            {if $checkout_address_billing_first}
                                {assign var="first_section" value="B"}
                                {assign var="first_section_text" value=__("billing_address")}
                                {assign var="sec_section" value="S"}
                                {assign var="sec_section_text" value=__("shipping_address")}
                                {assign var="body_id" value="sa"}
                            {else}
                                {assign var="first_section" value="S"}
                                {assign var="first_section_text" value=__("shipping_address")}
                                {assign var="sec_section" value="B"}
                                {assign var="sec_section_text" value=__("billing_address")}
                                {assign var="body_id" value="ba"}
                            {/if}

                            {include file="views/profiles/components/profile_fields.tpl" section=$first_section body_id="" ship_to_another=true title=$first_section_text}
                            {include file="views/profiles/components/profile_fields.tpl" section=$sec_section body_id=$body_id ship_to_another=true title=$sec_section_text address_flag=$profile_fields|fn_compare_shipping_billing ship_to_another=$ship_to_another}
                        {/if}

                        {if $runtime.mode == "add"}
                            {include file="buttons/register_profile.tpl" but_name="dispatch[profiles.update]" but_id="save_profile_but"}
                        {else}
                            {include file="buttons/save_modifications.tpl" but_name="dispatch[profiles.update]" but_class="btn-primary" but_id="save_profile_but"}
                            <script type="text/javascript">
                                var address_switch = $('input:radio:checked', '.address-switch');
                                $("#shipping_address_reset").on("click", function(e) {
                                    setTimeout(function() {
                                        address_switch.click();
                                    }, 50);
                                });
                            </script>
                        {/if}
            <!--content_addresses--></div>
            <div id="content_newsletters">
                <input type="hidden" name="user_data[email]" value="{$user_data.email}" />
                {run_controller controller='AppBundle:MailingList:form'}
            </div>
        </form>
            </div>
        </div>

        {capture name="additional_tabs"}
            {if $runtime.mode == "update"}
                {if $usergroups && !$user_data|fn_check_user_type_admin_area}
                <div id="content_usergroups">
                    <table class="table table-width">
                    <tr>
                        <th style="width: 30%">{__("usergroup")}</th>
                        <th style="width: 30%">{__("status")}</th>
                        {if $settings.General.allow_usergroup_signup == "Y"}
                            <th style="width: 40%">{__("action")}</th>
                        {/if}
                    </tr>
                    {foreach from=$usergroups item=usergroup}
                        {if $user_data.usergroups[$usergroup.usergroup_id]}
                            {assign var="ug_status" value=$user_data.usergroups[$usergroup.usergroup_id].status}
                        {else}
                            {assign var="ug_status" value="F"}
                        {/if}
                        {if $settings.General.allow_usergroup_signup == "Y" || $settings.General.allow_usergroup_signup != "Y" && $ug_status == "A"}
                            <tr {cycle values=",class=\"table-row\""}>
                                <td>{$usergroup.usergroup}</td>
                                <td class="center">
                                    {if $ug_status == "A"}
                                        {__("active")}
                                        {assign var="_link_text" value=__("remove")}
                                        {assign var="_req_type" value="cancel"}
                                    {elseif $ug_status == "F"}
                                        {__("available")}
                                        {assign var="_link_text" value=__("join")}
                                        {assign var="_req_type" value="join"}
                                    {elseif $ug_status == "D"}
                                        {__("declined")}
                                        {assign var="_link_text" value=__("join")}
                                        {assign var="_req_type" value="join"}
                                    {elseif $ug_status == "P"}
                                        {__("pending")}
                                        {assign var="_link_text" value=__("cancel")}
                                        {assign var="_req_type" value="cancel"}
                                    {/if}
                                </td>
                                {if $settings.General.allow_usergroup_signup == "Y"}
                                    <td>
                                        <a class="cm-ajax" data-ca-target-id="content_usergroups" href="{"profiles.usergroups?usergroup_id=`$usergroup.usergroup_id`&type=`$_req_type`"|fn_url}">{$_link_text}</a>
                                    </td>
                                {/if}
                            </tr>
                        {/if}
                    {/foreach}
                    <tr class="table-footer">
                        <td colspan="{if $settings.General.allow_usergroup_signup == "Y"}3{else}2{/if}">&nbsp;</td>
                    </tr>
                    </table>
                <!--content_usergroups--></div>
                {/if}
            {/if}
        {/capture}

        {$smarty.capture.additional_tabs nofilter}

    {/capture}
    {include file="views/profiles/components/profile_menu.tpl" content=$smarty.capture.tabsbox active_tab=$smarty.request.selected_section|default:'general'}
