<div class="row">
    <div class="col-sm-7">
        {assign var="my_account_url" value='profiles.update'|fn_url}
        {assign var="my_account_link" value='<a href="'|cat:$my_account_url|cat:'">'|cat:__('my_account')|cat:'</a>'}
        {if $order_info.need_shipping}
            {__("text_order_placed_successfully",['[my_account_link]' => $my_account_link]) nofilter}
        {else}
            {__("text_order_placed_successfully_for_edp",['[my_account_link]' => $my_account_link]) nofilter}
        {/if}
        <p>
            {__('w_order_num')} : {if $order_info.child_ids}{$order_info.child_ids}{else}{$order_info.order_id}{/if}<br/>
            {assign var="date" value=$order_info.timestamp|date_format:'%d/%m/%Y'}
            {assign var="time" value=$order_info.timestamp|date_format:'%H:%m'}
            {__('w_order_date', ['[time]'=>$time , '[date]'=> $date])}
        </p>
    </div>
    <div class="col-sm-5">
        <img src="{$images_dir}/w-merci.png"/>
    </div>
</div>
<div class="row">
    <div class="col-sm-4">
        <div class="sidebox-wrapper order-products">
            <div class="sidebox-title">{__('w_my_order')}</div>
            {include file="blocks/checkout/products_in_cart.tpl" cart_products=$order_info.products product_groups=$order_info.product_groups}
        </div>
        <div>
            <ul id="stay-in-touch">
                <li>{__('w_stay_in_touch')}</li>
                <li><a href="{__('twitter_link') nofilter}" target="_blank"><img src="{$images_dir}/logo_twitter.png">
                        Twitter</a></li>
                <li><a href="{__('facebook_link') nofilter}" target="_blank"><img src="{$images_dir}/logo_fb.png">Facebook</a>
                </li>
            </ul>
        </div>
    </div>
    <div class="col-sm-4">
        {if $order_info.need_shipping}
            <div class="sidebox-wrapper">
                <div class="sidebox-title">{__('shipment')}</div>
                <div class="sidebox-body">
                    <ul>
                        <li>{$order_info.s_lastname} {$order_info.s_firstname}</li>
                        <li>{$order_info.s_address} {$order_info.s_address_2}</li>
                        <li>{$order_info.s_zipcode} {$order_info.s_city}</li>
                    </ul>
                    {foreach from=$order_info.product_groups item="product_group"}
                        <br/>
                        <ul>
                            <li>{__('vendor')} {$product_group.name}</li>
                            <li>{$product_group.chosen_shippings[0].shipping} {include file="common/price.tpl" value=$product_group.chosen_shippings[0].rate}</li>
                        </ul>
                    {/foreach}
                </div>
            </div>
        {/if}
        <div class="sidebox-wrapper">
            <div class="sidebox-title">{__('w_billing')}</div>
            <div class="sidebox-body">
                <ul>
                    <li>{$order_info.b_lastname} {$order_info.b_firstname}</li>
                    <li>{$order_info.b_address} {$order_info.b_address_2}</li>
                    <li>{$order_info.b_zipcode} {$order_info.b_city}</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-sm-4">
        <div id="w-cart-total" class="top-marged">
            <div id="checkout_totals">
                <table style="width:90%">
                    <tr>
                        <td colspan="2" class="left">
                            {__('w_order_summary')}
                        </td>
                    </tr>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                    <tr>
                        <td>{__('w_order_items_amount')}</td>
                        <td class="right">{include file="common/price.tpl" value=$order_info.subtotal}</td>
                    </tr>
                    <tr>
                        <td>{__('w_shipping_total_amount')}</td>
                        <td class="right">{include file="common/price.tpl" value=$order_info.shipping_cost}</td>
                    </tr>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                    <tr>
                        <td class="middle">
                            {__('w_cart_total')}<br/>
                            {assign var="subtotal_tax" value=$order_info|fn_w_get_subtotal_tax_from_order}
                            {include file="common/price.tpl" value=$subtotal_tax assign="formatted_tax"}
                            {__('w_TVA_amount', ['[amount]' => $formatted_tax])}
                        </td>
                        <td class="right middle bold">{include file="common/price.tpl" value=$order_info.total}</td>
                    </tr>
                </table>
                <!--checkout_totals--></div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-sm-12">
        {run_controller controller='AppBundle:MailingList:form'}
    </div>
</div>

<script type="text/javascript">
    {* Analytics variables *}
    analytics.order = {
        id: "{$order_info.order_id}",
        // Order amount TF (tax free) without shipping fee
        subtotal_tf: "{$order_info.subtotal - $subtotal_tax}",
        subtotal_ati: "{$order_info.subtotal}",
        products: []
    };
    {foreach $order_info.products as $product}
    analytics.order.products.push({
        id: {$product.product_id|json_encode nofilter},
        name: {$product.product|json_encode nofilter},
        quantity: {$product.amount|json_encode nofilter},
        price: "{$product.subtotal|json_encode nofilter}"
    });
    {/foreach}
</script>
